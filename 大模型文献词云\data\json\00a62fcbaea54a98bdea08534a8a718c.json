[{"ArticleId": 79371473, "Title": "<PERSON><PERSON>-p Norm Based Approach for Tensor Completion", "Abstract": "Abstract <p>The matrix nuclear norm has been widely applied to approximate the matrix rank for low-rank tensor completion because of its convexity. However, this relaxation may make the solution seriously deviate from the original solution for real-world data recovery. In this paper, using a nonconvex approximation of rank, i.e., the Schatten-p norm, we propose a novel model for tensor completion. It’s hard to solve this model directly because the objective function of the model is nonconvex. To solve the model, we develop a variant of this model via the classical quadric penalty method, and propose an algorithm, i.e., SpBCD, based on the block coordinate descent method. Although the objective function of the variant is nonconvex, we show that the sequence generated by SpBCD is convergent to a critical point. Our numerical experiments on real-world data show that SpBCD delivers state-of-art performance in recovering missing data.</p>", "Keywords": "Tensor completion; Nuclear norm; Schatten p-norm; Block coordinate descent", "DOI": "10.1007/s10915-019-01108-9", "PubYear": 2020, "Volume": "82", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Wuhan University, Wuhan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Wuhan University, Wuhan, People’s Republic of China"}], "References": []}, {"ArticleId": 79371474, "Title": "An Efficient Formulation of Chebyshev Tau Method for Constant Coefficients Systems of Multi-order FDEs", "Abstract": "Abstract <p>The objective of the present work is to introduce a computational approach employing <PERSON><PERSON><PERSON><PERSON><PERSON> method for approximating the solutions of constant coefficients systems of multi-order fractional differential equations. For this purpose, a series representation for the exact solutions in a neighborhood of the origin is obtained to monitor their smoothness properties. We prove that some derivatives of the exact solutions of the underlying problem often suffer from discontinuity at the origin. To fix this drawback and design a high order approach a regularization procedure is developed. In addition to avoid high computational costs, a suitable strategy is implemented such that approximate solutions are obtained by solving some triangular algebraic systems. Complexity and convergence analysis of the proposed scheme are provided. Various practical test problems are presented to exhibit capability of the given approach.</p>", "Keywords": "Multi-order fractional differential equations; <PERSON><PERSON><PERSON><PERSON><PERSON> method; Convergence analysis; 34A09; 65L05; 65L20; 65L60; 65L80", "DOI": "10.1007/s10915-019-01104-z", "PubYear": 2020, "Volume": "82", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Basic Sciences, Sahand University of Technology, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Basic Sciences, Sahand University of Technology, Tabriz, Iran"}], "References": []}, {"ArticleId": 79371476, "Title": "Guest editorial: special issue on trust, privacy, and security in crowdsourcing computing", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11280-019-00772-z", "PubYear": 2020, "Volume": "23", "Issue": "1", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Soochow University, Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Macquarie University, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Macquarie University, Sydney, Australia"}, {"AuthorId": 4, "Name": "Qing Li", "Affiliation": "Department of Computing, The Hong Kong Polytechnic University, Hong Kong, China"}], "References": []}, {"ArticleId": 79371503, "Title": "Heterogeneous Computing (CPU–GPU) for Pollution Dispersion in an Urban Environment", "Abstract": "<p>The use of Computational Fluid Dynamics (CFD) to assist in air quality studies in urban environments can provide accurate results for the dispersion of pollutants. However, due to the computational resources needed, simulation domain sizes tend to be limited. This study aims to improve the computational efficiency of an emission and dispersion model implemented in a CPU-based solver by migrating it to a CPU–GPU-based one. The migration of the functions that handle boundary conditions and source terms for the pollutants is explained, as well as the main differences present in the solvers used. Once implemented, the model was used to run simulations with both engines on different platforms, enabling the comparison between them and reaching promising time improvements in favor of the use of GPUs.</p>", "Keywords": "Heterogeneous Computing; GPU; CFD; pollution dispersion Heterogeneous Computing ; GPU ; CFD ; pollution dispersion", "DOI": "10.3390/computation8010003", "PubYear": 2020, "Volume": "8", "Issue": "1", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IMFIA, Faculty of Engineering, UDELAR Montevideo, Montevideo 11300, Uruguay"}, {"AuthorId": 2, "Name": "Mariana <PERSON>", "Affiliation": "IMFIA, Faculty of Engineering, UDELAR Montevideo, Montevideo 11300, Uruguay"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IMFIA, Faculty of Engineering, UDELAR Montevideo, Montevideo 11300, Uruguay"}], "References": []}, {"ArticleId": 79371540, "Title": "A Mixed Method for Maxwell Eigenproblem", "Abstract": "Abstract <p>We propose a mixed method for the computation of the eigenvalues of the Maxwell eigenproblem, in terms of the electric field and a multiplier. The method allows the Lagrange elements of any order greater than or equal to two for the electric field, while a piecewise constant element always for the multiplier. We show that optimal error estimates yield for singular as well as smooth solutions. For the Maxwell eigenproblem in L-shaped domain which has singular and smooth eigenfunctions, we present numerical results for illustrating the effectiveness of the proposed method and for confirming the theoretical results. </p>", "Keywords": "Maxwell eigenproblem; Mixed finite element; Error estimates; 65N30", "DOI": "10.1007/s10915-019-01111-0", "PubYear": 2020, "Volume": "82", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Wuhan University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Wuhan University, Wuhan, China"}], "References": []}, {"ArticleId": 79371542, "Title": "Superconvergence of a Finite Element Method for the Multi-term Time-Fractional Diffusion Problem", "Abstract": "Abstract <p>A time-fractional initial-boundary value problem is considered, where the differential equation has a sum of fractional derivatives of different orders, and the spatial domain lies in \\({\\mathbb {R}}^d\\) with \\(d\\in \\{1,2,3\\}\\) . A priori bounds on the solution and its derivatives are stated; these show that typical solutions have a weak singularity at the initial time \\(t=0\\) . A standard finite element method with mapped piecewise bilinears is used to discretise the spatial derivatives, while for each time derivative we use the L1 scheme on a temporal graded mesh. Our analysis reveals the optimal grading that one should use for this mesh. A novel discrete fractional Gronwall inequality is proved: the statement of this inequality and its proof are different from any previously published Gronwall inequality. This inequality is used to derive an optimal error estimate in \\(L^\\infty (H^1)\\) . It is also used to show that, if each mesh element is rectangular in the case \\(d=2\\) or cubical in the case \\(d=3\\) , with the sides of the element parallel to the coordinate axes, then a simple postprocessing of the computed solution will yield a higher order of convergence in the spatial direction. Numerical results are presented to show the sharpness of our theoretical results.</p>", "Keywords": "Multiterm time-fractional; Finite element method; Gronwall inequality; Superconvergence; Caputo derivative; 65M60; 65M12; 35R11", "DOI": "10.1007/s10915-019-01115-w", "PubYear": 2020, "Volume": "82", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Quantitative Economics, Shandong University of Finance and Economics, Jinan, People’s Republic of China;Applied and Computational Mathematics Division, Beijing Computational Science Research Center, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Applied and Computational Mathematics Division, Beijing Computational Science Research Center, Beijing, People’s Republic of China"}], "References": []}, {"ArticleId": 79371864, "Title": "Boundary-layer flow of the power-law fluid over a moving wedge: a linear stability analysis", "Abstract": "<p>We study the stability of the two-dimensional boundary-layer flow of a power-law (<PERSON><PERSON><PERSON>) non-Newtonian fluid over a moving wedge. The mainstream velocity is assumed to have a power of distance from the leading boundary layer, such that the system admits to the self-similar solutions. We discuss the problem in question for both shear-thickening and shear-thinning fluids which lead to a non-uniqueness (double solutions) in the base flow solutions. We then address an issue of the stability of the non-unique solutions. A linear eigenvalue analysis of the double solution reveals that the basic flow represented by the first solution is always stable, and this flow is practically encountered. The system becomes unstable to the second solutions which have the mode-two perturbations with larger boundary-layer thickness. The first and second solutions form a tongue-like structure in the solution space. Furthermore, the modification of the viscosity for the power-law fluids reveals that the system predicts an infinite viscosity in the confinement of the boundary-layer region. Extensive comparisons of the solutions with the existing models with Newtonian fluid are made, and a physical explanation behind these solutions is proposed.</p>", "Keywords": "Boundary layers; Power-law fluids; Stability; Double solutions; Numerics and asymptotics", "DOI": "10.1007/s00366-019-00914-x", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Bengaluru Central University, Central College Campus, Bengaluru, India"}, {"AuthorId": 2, "Name": " Noor-E-Misbah", "Affiliation": "Department of Mathematics, Bengaluru Central University, Central College Campus, Bengaluru, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Mathematics, Bengaluru Central University, Central College Campus, Bengaluru, India"}], "References": [{"Title": "<PERSON><PERSON><PERSON><PERSON><PERSON> polynomials for generalized Couette flow of fractional Jeffrey nanofluid subjected to several thermochemical effects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "579", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 79371874, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0010-4485(20)30004-X", "PubYear": 2020, "Volume": "120", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [], "References": []}, {"ArticleId": 79371875, "Title": "Human factors considerations in designing a personalized mobile dialysis device: An interview study", "Abstract": "<p>Recent technical advances have enabled the creation of mobile dialysis device prototypes. These prototypes have been tested for their ability to allow an individual to be dialyzed continuously rather than sporadically. The most recent prototype of a mobile dialysis device aims at increased functionality, which suggests that human factors issues (e.g., efficiency, bulkiness, and weight) are now considered carefully. This study describes advances in the design of an Ambulatory Kidney to Improve Vitality (AKTIV), using an interview protocol during the early stages of product development to capture patients' and caregivers' reactions. The AKTIV has the potential to improve patients' quality of life and decrease mortality rates. The goal of our study is to examine patients' and caregivers' design preferences and feature considerations for an AKTIV. We interviewed 22 participants (age M = 57.50, SD = 13.30), of whom 12 were female and 16 were patients. A pre-interview survey was distributed to the participants, and semi-structured interviews were subsequently held. The pre-interview results show that the belt and backpack designs were preferred over the shoulder bag and distributed designs. The participants also indicated on their pre-interview forms that safety and accuracy were more important to them than attachment ease, comfort, compactness, or operational simplicity. Invisibility and mobility were frequently mentioned when determining the strengths of each of the five design types during the interviews. Finally, individual differences in preferences for the various design types and attributes were identified. The results from our study have important implications for improving efficiency, effectiveness, and user satisfaction in relation to AKTIV prototypes and products. The findings from this interview study will help to ensure engineers and clinicians have target parameters for redesigning the AKTIV.</p><p>Copyright © 2019 Elsevier Ltd. All rights reserved.</p>", "Keywords": "ESRD;Human factors;Interviewing;Mobile dialysis devices;Wearable medical devices", "DOI": "10.1016/j.apergo.2019.103003", "PubYear": 2020, "Volume": "85", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, University of Washington, Seattle, WA, USA. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Health Services, University of Washington, Seattle, WA, USA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, University of Washington, Seattle, WA, USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, University of Washington, Seattle, WA, USA."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, University of Washington, Seattle, WA, USA."}], "References": []}, {"ArticleId": 79371899, "Title": "Improving communication skills of children with autism through support of applied behavioral analysis treatments using multimedia computing: a survey", "Abstract": "<p>Naturalistic applied behavior analysis (ABA) techniques have been shown to help children with autism improve their communication skills. Recognizing that individuals who interact with children regularly are in the position to utilize treatments with profound effects, researchers have examined methodologies for training parents, teachers, and peers to implement treatments. These programs are time intensive and often unable to support trainees after training. Technologies need to be examined to determine how they can aid in the educational and support process. Academic publications and publicly available training programs were reviewed to determine the types of participants, methodologies, and training durations that have been reported for instructing interventionists. These resources illustrate a need to make programs more accessible. To address this, selected computer science research is applied to methods of evaluating ABA implementations in order to recommend how the technologies could be utilized to make training and support programs more accessible. Review results of instructional programs, both in research and available in the community, illustrate the challenges in providing training in ABA methodologies. Modern research in multimedia data processing and machine learning could be applied to reduce the human cost of training and support individuals implementing ABA techniques. Utilizing machine learning techniques to analyze video probes of naturalistic ABA treatment implementation could alleviate the human cost of evaluating fidelity, allowing for greater support for individuals interested in the treatments. These technologies could be used in the future to expand data collection to provide more perspective on the treatments.</p>", "Keywords": "Applied behavior analysis; Pivotal response treatment; Autism spectrum disorder; Parent training; Multimedia data processing; Machine learning", "DOI": "10.1007/s10209-019-00707-5", "PubYear": 2021, "Volume": "20", "Issue": "1", "JournalId": 1397, "JournalTitle": "Universal Access in the Information Society", "ISSN": "1615-5289", "EISSN": "1615-5297", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing, Informatics and Decision Systems Engineering, Arizona State University, Tempe, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing, Informatics and Decision Systems Engineering, Arizona State University, Tempe, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Informatics and Decision Systems Engineering, Arizona State University, Tempe, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Informatics and Decision Systems Engineering, Arizona State University, Tempe, USA"}], "References": []}, {"ArticleId": 79371917, "Title": "LWESM: learning with error based secure communication in mobile devices using fuzzy extractor", "Abstract": "<p>Rapid-growth in wireless communication technologies and increasing demand smart devices are enabling users to access various services in remote areas. However, security and privacy are the two key attributes of wireless communication. To establish secure channel, various anonymous authentication schemes have been proposed based on classical number-theoretic hard assumptions ( discrete logarithm or factorization ) have been introduced in the last two or three decades. Due to <PERSON><PERSON><PERSON>s algorithm, a scheme based on number-theoretic assumptions could be broken by post-quantum computers in polynomial time. Therefore, we have proposed learning with errors based anonymous authentication protocol using ideal in some lattice. The security proof of the proposed technique ensures provable-security in the random oracle under learning with errors problem in some lattice. Furthermore, an informal security discussion and performance analysis show that our LWESM protocol is efficient and could be used in various applications.</p>", "Keywords": "Security; Privacy; Authentication; Learning with error; Ideal lattice", "DOI": "10.1007/s12652-019-01675-7", "PubYear": 2020, "Volume": "11", "Issue": "10", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, The LNM Institute of Information Technology, Jaipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sreenivasa Institute of Technology and Management Studies (Autonomous), Chittoor, India"}], "References": [{"Title": "Security analysis and application of Chebyshev Chaotic map in the authentication protocols", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "10", "Page": "1095", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": 79371918, "Title": "Graphical Flow-based Spark Programming", "Abstract": "Abstract <p>Increased sensing data in the context of the Internet of Things (IoT) necessitates data analytics. It is challenging to write applications for Big Data systems due to complex, highly parallel software frameworks and systems. The inherent complexity in programming Big Data applications is also due to the presence of a wide range of target frameworks, with different data abstractions and APIs. The paper aims to reduce this complexity and its ensued learning curve by enabling domain experts, that are not necessarily skilled Big Data programmers, to develop data analytics applications via domain-specific graphical tools. The approach follows the flow-based programming paradigm used in IoT mashup tools. The paper contributes to these aspects by (i) providing a thorough analysis and classification of the widely used Spark framework and selecting suitable data abstractions and APIs for use in a graphical flow-based programming paradigm and (ii) devising a novel, generic approach for programming Spark from graphical flows that comprises early-stage validation and code generation of Spark applications. Use cases for Spark have been prototyped and evaluated to demonstrate code-abstraction, automatic data abstraction interconversion and automatic generation of target Spark programs, which are the keys to lower the complexity and its ensued learning curve involved in the development of Big Data applications.</p>", "Keywords": "Spark pipelines; IoT mashup tools; Graphical tools; Stream analytics; Flow-based programming", "DOI": "10.1186/s40537-019-0273-5", "PubYear": 2020, "Volume": "7", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Ma<PERSON>", "Affiliation": "Lehrstuhl für Software und Systems Engineering, Fakultät für Informatik, Technische Universität München, Garching b. <PERSON>, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Lehrstuhl für Software und Systems Engineering, Fakultät für Informatik, Technische Universität München, Garching b. <PERSON>, Germany"}], "References": []}, {"ArticleId": 79371924, "Title": "A technique for image splicing detection using hybrid feature set", "Abstract": "<p>Image manipulation has no longer been rocket science for non-professionals. Tampering of images has become so popular due to the accessibility of free editing application in smart phone’s store, these applications work without any agreement or license from the user which makes the condition more vulnerable. The image alteration is not limited to the smart phone’s applications, they can be done online without downloading and signing in the application making the scenario even worst. These forged images are so tricky that they are not predictable with bare human eyes. So, in order to tackle with this delinquent act, one must develop such system which can instantly discriminate between the unique and altered image. One of the best technologies that can tackle the problem and helps to develop such a scheme is Machine learning. There are several classification techniques based on the requirement of the system that can be applied to the data set, resulting in the classification of images under the groups forged and unforged images. In this work, we have discussed the images which are being forged using Image splicing Technique, in which the region of an original image is cropped and pasted onto the other original image. In this paper, a machine learning classification technique logistic regression has been used to classify images into two classes, spliced and non-spliced images. For this, a combination of four handcrafted features has been extracted from images for feature vector. Then these feature vectors are trained using logistic regression classification model. 10-fold cross-validation test evaluation procedure has been used to evaluate the result. Finally, the comparative analysis of the proposed method with other state-of-the-art methods on three online available datasets is presented in the paper. It is observed that the obtained results perform better than state-of-the-art methods.</p>", "Keywords": "Image splicing; Image forgery detection; Image features; Classification; Logistic regression", "DOI": "10.1007/s11042-019-08480-6", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computing and Vision Lab, Department of Computer Science and Engineering, Indian Institute of Technology (BHU), Varanasi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computing and Vision Lab, Department of Computer Science and Engineering, Indian Institute of Technology (BHU), Varanasi, India"}], "References": []}, {"ArticleId": 79371968, "Title": "Not All Attributes are Created Equal: d \n \n <sub>X</sub>\n -Private Mechanisms for Linear Queries", "Abstract": "Abstract <p> Differential privacy provides strong privacy guarantees simultaneously enabling useful insights from sensitive datasets. However, it provides the same level of protection for all elements (individuals and attributes) in the data. There are practical scenarios where some data attributes need more/less protection than others. In this paper, we consider dX -privacy, an instantiation of the privacy notion introduced in [6], which allows this flexibility by specifying a separate privacy budget for each pair of elements in the data domain. We describe a systematic procedure to tailor any existing differentially private mechanism that assumes a query set and a sensitivity vector as input into its d <sub>X</sub> -private variant, specifically focusing on linear queries. Our proposed meta procedure has broad applications as linear queries form the basis of a range of data analysis and machine learning algorithms, and the ability to define a more flexible privacy budget across the data domain results in improved privacy/utility tradeoff in these applications. We propose several d <sub>X</sub> -private mechanisms, and provide theoretical guarantees on the trade-off between utility and privacy. We also experimentally demonstrate the effectiveness of our procedure, by evaluating our proposed d <sub>X</sub> -private Laplace mechanism on both synthetic and real datasets using a set of randomly generated linear queries. </p>", "Keywords": "database privacy ; linear queries ; differential privacy", "DOI": "10.2478/popets-2020-0007", "PubYear": 2020, "Volume": "2020", "Issue": "1", "JournalId": 30919, "JournalTitle": "Proceedings on Privacy Enhancing Technologies", "ISSN": "", "EISSN": "2299-0984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne (work done while <PERSON><PERSON><PERSON> was a Postgraduate Researcher at Data61, CSIRO)"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ISAE-SUPAERO & Data61, CSIRO"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Macquarie University & Data61, CSIRO"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Macquarie University & Data61, CSIRO"}], "References": []}, {"ArticleId": ********, "Title": "Destination Based Stable Clustering Algorithm and Routing for VANET", "Abstract": "Now in modern telecommunication, one of the big topic research is a Vehicle Ad-hoc Network “VANET” (V2V). This topic is one of an “issues of the day” because research has problematic topic due to its many application-questions, what we need to solve: avoid collisions, any accidents on a way, and notifications about congestions on the road, available car parking, road-side commercial-business ads, and etcetera. These like application forms creating big delay constraining’s i.e. the instant data should reach the destination within certain time limits. Therefore, we need a really efficient stable clustering method and routing in vehicle ad-hoc network which will be resistant to network delays and meets network requirements. The methods are proposed in the paper for optimization VANETs data traffic as well as to minimizing delay. First, here is presented, a stable clustering algorithm based on the destination, contextually take into consideration various physical parameters for cluster formation such as location of the vehicle and its direction, vehicle speed and destination, as well as a possible list of interests of the vehicle. And also the next main process is to depend on these “five parameters” we can calculate the “Cluster Head Eligibility” of each car. Second, based on this “Cluster Head Eligibility”, described cluster head selection method. Third, for efficient communication between clusters, present a routing protocol based on the “destination”, which considered an efficient selecting method of next forwarding nodes, which is calculated by using “FE” metric.", "Keywords": "VANET;Vehicle Ad-Hoc Network;Stable Clustering Algorithm;Destination Based Clustering;Efficient Routing", "DOI": "10.4236/jcc.2020.81003", "PubYear": 2020, "Volume": "8", "Issue": "1", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Communication and Information Engineering, Chongqing University of Post and Telecommunication, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Communication and Information Engineering, Chongqing University of Post and Telecommunication, Chongqing, China"}], "References": []}, {"ArticleId": ********, "Title": "Study on Bridge Displacement Monitoring Algorithms Based on Multi-Targets Tracking", "Abstract": "<p>Bridge displacement measurement is an important area of bridge health monitoring, which can directly reflect whether the deformation of bridge structure exceeds its safety permission. Target tracking technology and Digital Image Correlation (DIC) are two fast-developing and well-known methods for non-contact bridge displacement monitoring in Digital Image Processing (DIP) methods. The former’s cost of erecting detection equipment is too large for bridges with a large span that need to locate more multi-targets because of its tracking only one target on a camera while the latter is not suitable for remote detection because it requires very high detection conditions. After investigating the evolution of bridge displacement monitoring, this paper proposes a bridge displacement monitoring algorithm based on multi-target tracking. The algorithm takes full account of practical application and realizes accuracy, robustness, real-time, low-cost, simplicity, and self-adaptability, which sufficiently adapts the bridge displacement monitoring in theory.</p>", "Keywords": "bridge displacement monitoring; digital image processing; multi-targets tracking bridge displacement monitoring ; digital image processing ; multi-targets tracking", "DOI": "10.3390/fi12010009", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical, Electrical and Information Engineering, Shandong University, Weihai 264209, China ↑ Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Author to whom correspondence should be addressed"}], "References": []}, {"ArticleId": ********, "Title": "Infinite time linear quadratic stackelberg game problem for unknown stochastic discrete‐time systems via adaptive dynamic programming approach", "Abstract": "<p>In this paper, we propose an adaptive dynamic programming (ADP) approach to solve the infinite horizon linear quadratic (LQ) Stackelberg game problem for unknown stochastic discrete‐time systems with multiple decision makers. Firstly, the stochastic LQ Stackelberg game problem is converted into the deterministic problem by system transformation. Next, a value iteration ADP approach is put forword and the convergence is given. Thirdly, in order to implement the iterative method, back propagation neural network (BPNN) is chosen to design model network, critic network and action network to approximate the unknown systems, objective functions and Stackelberg strategies. Finally, simulation results show that the algorithm is effective.</p>", "Keywords": "adaptive dynamic programming;back propagation neural network;Stackelberg game;stochastic discrete‐time systems", "DOI": "10.1002/asjc.2276", "PubYear": 2021, "Volume": "23", "Issue": "2", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Systems Science, Shandong University of Science and Technology, Qingdao, 266590 Shandong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and Systems Science, Shandong University of Science and Technology, Qingdao, 266590 Shandong, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, 266590 Shandong, China"}], "References": []}, {"ArticleId": 79372329, "Title": "Deep Learning for Lung Cancer Nodules Detection and Classification in CT Scans", "Abstract": "<p>Detecting malignant lung nodules from computed tomography (CT) scans is a hard and time-consuming task for radiologists. To alleviate this burden, computer-aided diagnosis (CAD) systems have been proposed. In recent years, deep learning approaches have shown impressive results outperforming classical methods in various fields. Nowadays, researchers are trying different deep learning techniques to increase the performance of CAD systems in lung cancer screening with computed tomography. In this work, we review recent state-of-the-art deep learning algorithms and architectures proposed as CAD systems for lung cancer detection. They are divided into two categories—(1) Nodule detection systems, which from the original CT scan detect candidate nodules; and (2) False positive reduction systems, which from a set of given candidate nodules classify them into benign or malignant tumors. The main characteristics of the different techniques are presented, and their performance is analyzed. The CT lung datasets available for research are also introduced. Comparison between the different techniques is presented and discussed.</p>", "Keywords": "lung cancer; deep learning; nodule detection; convolutional neural networks; computer-aided diagnosis lung cancer ; deep learning ; nodule detection ; convolutional neural networks ; computer-aided diagnosis", "DOI": "10.3390/ai1010003", "PubYear": 2020, "Volume": "1", "Issue": "1", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Perception, Robotics, and Intelligent Machines (PRIME), Department of Computer Science, Université de Moncton, Moncton, NB E1A3E9, Canada↑Department of Electronic Engineering, Universidad Técnica Federico <PERSON>, Valparaiso, Chile↑These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Perception, Robotics, and Intelligent Machines (PRIME), Department of Computer Science, Université de Moncton, Moncton, NB E1A3E9, Canada↑Author to whom correspondence should be addressed.↑These authors contributed equally to this work"}], "References": []}, {"ArticleId": 79372351, "Title": "Basic Features of the Analysis of Germination Data with Generalized Linear Mixed Models", "Abstract": "<p>Germination data are discrete and binomial. Although analysis of variance (ANOVA) has long been used for the statistical analysis of these data, generalized linear mixed models (GzLMMs) provide a more consistent theoretical framework. GzLMMs are suitable for final germination percentages (FGP) as well as longitudinal studies of germination time-courses. Germination indices (i.e., single-value parameters summarizing the results of a germination assay by combining the level and rapidity of germination) and other data with a Gaussian error distribution can be analyzed too. There are, however, different kinds of GzLMMs: Conditional (i.e., random effects are modeled as deviations from the general intercept with a specific covariance structure), marginal (i.e., random effects are modeled solely as a variance/covariance structure of the error terms), and quasi-marginal (some random effects are modeled as deviations from the intercept and some are modeled as a covariance structure of the error terms) models can be applied to the same data. It is shown that: (a) For germination data, conditional, marginal, and quasi-marginal GzLMMs tend to converge to a similar inference; (b) conditional models are the first choice for FGP; (c) marginal or quasi-marginal models are more suited for longitudinal studies, although conditional models lead to a congruent inference; (d) in general, common random factors are better dealt with as random intercepts, whereas serial correlation is easier to model in terms of the covariance structure of the error terms; (e) germination indices are not binomial and can be easier to analyze with a marginal model; (f) in boundary conditions (when some means approach 0% or 100%), conditional models with an integral approximation of true likelihood are more appropriate; in non-boundary conditions, (g) germination data can be fitted with default pseudo-likelihood estimation techniques, on the basis of the SAS-based code templates provided here; (h) GzLMMs are remarkably good for the analysis of germination data except if some means are 0% or 100%. In this case, alternative statistical approaches may be used, such as survival analysis or linear mixed models (LMMs) with transformed data, unless an ad hoc data adjustment in estimates of limit means is considered, either experimentally or computationally. This review is intended as a basic tutorial for the application of GzLMMs, and is, therefore, of interest primarily to researchers in the agricultural sciences.</p>", "Keywords": "binomial data; germination test; over-dispersion; under-dispersion; random effects binomial data ; germination test ; over-dispersion ; under-dispersion ; random effects", "DOI": "10.3390/data5010006", "PubYear": 2020, "Volume": "5", "Issue": "1", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Council for Agricultural Research and Economics—Research Centre for Genomics and Bioinformatics, via S. Protaso 302, 29017 Fiorenzuola d’Arda (PC), Italy"}], "References": []}, {"ArticleId": 79372431, "Title": "Analysing performance issues of open-source intrusion detection systems in high-speed networks", "Abstract": "Driven by the growing data transfer needs, industry and research institutions are deploying 100 Gb/s networks. As such high-speed networks become prevalent, these also introduce significant technical challenges. In particular, an Intrusion Detection System (IDS) cannot process network activities at such a high rate when monitoring large and diverse traffic volumes, thus resulting in packet drops. Unfortunately, the high packet drop rate has a significant impact on detection accuracy. In this work, we investigate two popular open-source IDSs: Snort and Suricata along with their comparative performance benchmarks to better understand drop rates and detection accuracy in 100 Gb/s networks. More specifically, we study vital factors (including system resource usage, packet processing speed, packet drop rate, and detection accuracy) that limit the applicability of IDSs to high-speed networks. Furthermore, we provide a comprehensive analysis to show the performance impact on IDSs by using different configurations, traffic volumes and different flows. Finally, we identify challenges of using open-source IDSs in high-speed networks and provide suggestions to help network administrators to address identified issues and give some recommendations for developing new IDSs that can be used for high-speed networks.", "Keywords": "", "DOI": "10.1016/j.jisa.2019.102426", "PubYear": 2020, "Volume": "51", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, The University of Auckland, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Center for Advanced Internet Research, Northwestern University, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, The University of Auckland, New Zealand;Corresponding author"}], "References": []}, {"ArticleId": 79372561, "Title": "Cloud architecture for plant phenotyping research", "Abstract": "<p>Digital phenotyping is an emergent science mainly based on imagery techniques. The tremendous amount of data generated needs important cloud computing for their processing. The coupling of recent advance of distributed databases and cloud computing offers new possibilities of big data management and data sharing for the scientific research. In this paper, we present a solution combining a lambda architecture built around Apache Druid and a hosting platform leaning on Apache Mesos. Lambda architecture has already proved its performance and robustness. However, the capacity of ingesting and requesting of the database is essential and can constitute a bottleneck for the architecture, in particular, for in terms of availability and response time of data. We focused our experimentation on the response time of different databases to choose the most adapted for our phenotyping architecture. Apache Druid has shown its ability to respond to typical queries of phenotyping applications in times generally inferior to the second.</p>", "Keywords": "cloud architecture;digital phenotyping;lambda architecture;plant phenotyping;research application hosting platform", "DOI": "10.1002/cpe.5661", "PubYear": 2020, "Volume": "32", "Issue": "17", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Unit, Faculty of Engineering, University of Mons, Mons, Belgium; Biosystems Dynamics and Exchanges (BioDynE) Department of Biosystem Engineering (BioSE), Gembloux Agro‐Bio Tech University of Liège, Gembloux, Belgium; <PERSON>, Computer Science Unit, Faculty of Engineering, University of Mons, Place du Parc 20, B‐7000 Mons, Belgium."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Unit, Faculty of Engineering, University of Mons, Mons, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Biosystems Dynamics and Exchanges (BioDynE) Department of Biosystem Engineering (BioSE), Gembloux Agro‐Bio Tech University of Liège, Gembloux, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Unit, Faculty of Engineering, University of Mons, Mons, Belgium"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Computer Science Unit, Faculty of Engineering, University of Mons, Mons, Belgium"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Biosystems Dynamics and Exchanges (BioDynE) Department of Biosystem Engineering (BioSE), Gembloux Agro‐Bio Tech University of Liège, Gembloux, Belgium; ITAP, University Montpellier, Irstea, Montpellier SupAgro, Montpellier, France"}], "References": []}, {"ArticleId": 79372632, "Title": "Study on Simultaneous-Action Discrimination Method Using Deep Learning", "Abstract": "<p>With the spread of smartphones, accidents caused by “Simultaneous-Action” such as “Simultaneous-Walking” which is walking while operating a smartphone and “Simultaneous-Cycling” which is cycling while operating a smartphone are increasing. So far, we have examined a method to discriminate “Simultaneous-Action” using neural network based on the information of the sensor mounted on the smartphone. In this study, we applied deep learning that can learn features contained in data step rather than machine learning such as support vector machine and neural network, and examined its effectiveness.</p>", "Keywords": "Deep learning; Simultaneous-action; Sensing; Smartphone", "DOI": "10.1007/s13177-019-00216-y", "PubYear": 2021, "Volume": "19", "Issue": "1", "JournalId": 6726, "JournalTitle": "International Journal of Intelligent Transportation Systems Research", "ISSN": "1348-8503", "EISSN": "1868-8659", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@ccmailg.meijo-u.ac.jp;Meijo University, Faculty of Science and Technology, Nagoya, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@ccmailg.meijo-u.ac.jp;Meijo University, Faculty of Science and Technology, Nagoya, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Meijo University, Faculty of Science and Technology, Nagoya, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Meijo University, Faculty of Science and Technology, Nagoya, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Meijo University, Faculty of Science and Technology, Nagoya, Japan"}], "References": []}, {"ArticleId": 79372683, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0925-2312(20)30005-9", "PubYear": 2020, "Volume": "378", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [], "References": []}, {"ArticleId": 79372706, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1389-1286(19)31665-2", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [], "References": []}, {"ArticleId": 79373031, "Title": "Using a segmenting description approach in multiple criteria decision aiding", "Abstract": "We propose a new method for analyzing a set of parameters in a multiple criteria ranking method. Unlike the existing techniques, we do not use any optimization method, instead incorporating and extending a Segmenting Description (SD) approach. The algorithm rewrites a set of initial inequalities into an equivalent SD system by means of three fundamental operations in a way that makes it possible to either easily analyze all feasible solutions or identify all sources of inconsistency. While considering a value-based preference disaggregation method, we demonstrate the usefulness of the introduced method in a multi-purpose decision analysis exploiting a system of inequalities that models the Decision Maker’s (DM’s) preferences. We focus on the contexts where informative results can be obtained through the analysis of (in)feasibility of such systems. Specifically, we discuss how SD can be applied for verifying the consistency between the revealed and estimated preferences as well as for identifying the reasons of potential incoherence. Moreover, we employ the method for conducting robustness analysis, i.e., discovering a set of all compatible parameter values and verifying the stability of suggested recommendation in view of multiplicity of feasible solutions. In addition, we make clear its suitability for generating arguments about the validity of outcomes and the role of particular criteria. Overall, the SD approach confirms its usefulness in analyzing the relationships between the DM’s preference information, the compatible parameters of an assumed model, and the value-driven ranking procedure. We also discuss the favourable characteristics of the proposed method enhancing its suitability for use in Multiple Criteria Decision Aiding. These include possibility of studying any system of inequalities without an objective function, keeping in memory an entire process of transforming a system of inequalities that makes identification of potential incoherence straightforward, and avoiding the need for processing the inequalities contained in the basic system which is subsequently enriched with some hypothesis to be verified. The latter is substantial for saving time when a multitude of hypotheses need to be checked in the same conditions. The applicability and the advantages of using the proposed method in the decision aiding context are clearly exemplified on a numerical study.", "Keywords": "Multiple criteria decision aiding ; Inconsistency analysis ; Robustness analysis ; Argumentation ; Linear programming ; Preference disaggregation", "DOI": "10.1016/j.eswa.2020.113186", "PubYear": 2020, "Volume": "147", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing Science, Faculty of Computing, Poznan University of Technology, Piotrowo 2, Poznań 60-965, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Computing Science, Faculty of Computing, Poznan University of Technology, Piotrowo 2, Poznań 60-965, Poland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CEG-IST, Instituto Superior Técnico, Universidade de Lisboa, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, 1, Lisbon 1049-001, Portugal"}], "References": []}, {"ArticleId": 79373048, "Title": "An efficient binary social spider algorithm for feature selection problem", "Abstract": "The social spider algorithm (SSA) is a heuristic algorithm created on spider behaviors to solve continuous problems. In this paper, firstly a binary version of the social spider algorithm called binary social spider algorithm (BinSSA) is proposed. Currently, there is insufficient focus on the binary version of SSA in the literature. The main part of the binary version is the transfer function. The transfer function is responsible for mapping continuous search space to binary search space. In this study, eight of the transfer functions divided into two families, S-shaped and V-shaped, are evaluated. BinSSA is obtained from SSA, by transforming constant search space to binary search space with eight different transfer functions (S-Shapes and V-Shaped). Thus, eight different variations of BinSSA are formed as BinSSA1, BinSSA2, <PERSON>SSA3, <PERSON>SSA4, BinSSA5, BinSSA6, <PERSON>SSA7, and <PERSON>SSA8. For increasing, exploration and exploitation capacity of BinSSA, a crossover operator is added as BinSSA-CR. In secondly, the performances of BinSSA variations are tested on feature selection task. The optimal subset of features is a challenging problem in the process of feature selection. In this paper, according to different comparison criteria (mean of fitness values, the standard deviation of fitness values, the best of fitness values, the worst of fitness values, accuracy values, the mean number of the selected features, CPU time), the best BinSSA variation is detected. In the feature selection problem, the K-nearest neighbor (K-NN) and support vector machines (SVM) are used as classifiers. A detailed study is performed for the fixed parameter values used in the fitness function. BinSSA is evaluated on low-scaled, middle-scaled and large-scaled twenty-one well-known UCI datasets and obtained results are compared with state-of-art algorithms in the literature. Obtained results have shown that BinSSA and BinSSA-CR show superior performance and offer quality and stable solutions.", "Keywords": "Social spider algorithm ; Feature selection ; Classifiers", "DOI": "10.1016/j.eswa.2020.113185", "PubYear": 2020, "Volume": "146", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Emine BAŞ", "Affiliation": "Kulu Vocational School, Selçuk University, 42075 Konya, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "Erkan ÜLKER", "Affiliation": "Department of Computer Engineering, Faculty of Engineering and Nature Sciences, Konya Technical University, 42075 Konya, Turkey"}], "References": [{"Title": "Improved salp swarm algorithm for feature selection", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Gh. S. El<PERSON>Ta<PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "3", "Page": "335", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 79373161, "Title": "Strategy archetypes for digital transformation: Defining meta objectives using business process management", "Abstract": "Digital transformation dominates the practical and scientific discourse. Still, many companies do not have a clear plan on how to approach it. Particularly, small- and medium-sized enterprises struggle to initiate their digital journey as they lack resources and expertise. In response, we examine how five companies use business process management (BPM) to implement digital transformation. We perform a qualitative interview study, and analyze the capabilities of BPM based on six requirements of digital transformation. Thereby, we carve out 17 recommendations, which must be adapted according to companies’ meta objectives. We derive three strategy archetypes to serve as implementation blueprints.", "Keywords": "Digital transformation ; Business process management ; Interview study ; Meta objective ; Strategy archetype", "DOI": "10.1016/j.im.2019.103262", "PubYear": 2020, "Volume": "57", "Issue": "5", "JournalId": 2493, "JournalTitle": "Information & Management", "ISSN": "0378-7206", "EISSN": "1872-7530", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Würzburg, Sanderring 2, 97070, Würzburg, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Würzburg, Sanderring 2, 97070, Würzburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Würzburg, Sanderring 2, 97070, Würzburg, Germany;Technische Universität Dresden, Helmholtzstraße 10, 01069, Dresden, Germany;Corresponding author at: University of Würzburg, Sanderring 2, 97070, Würzburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Würzburg, Sanderring 2, 97070, Würzburg, Germany"}], "References": []}, {"ArticleId": 79373166, "Title": "RETRACTED ARTICLE: Interpolative Leishman-Stained transformation invariant deep pattern classification for white blood cells", "Abstract": "<p>Blood analysis is regarded as the one of the most predominant examinations in medicine field to obtain patient physiological state. A significant process in the classification of white blood cells (WBC) is blood sample analysis. An automatic system that is potential of identifying WBC aids the physicians in early disease diagnosis. In contrast to previous methods, thus resulting in trade-off among computational time (CT) and performance efficiency, an interpolative Leishman-stained multi-directional transformation invariant deep classification (LSM-TIDC) for WBC is presented. LSM-TIDC method discovers possibilities of interpolation and Leishman-stained function, because they require no explicit segmentation, and yet they eliminated false regions for several input images. Next, with the preprocessed images, optimal and relevant features are extracted by applying multi-directional feature extraction. To identify and classify blood cells, a system is developed via the implementation of transformation invariant model for extraction of nucleus and subsequently performs classification through convolutional and pooling characteristics. The proposed method is evaluated by extensive experiments on benchmark database like blood cell images from Kaggle. Experimental results confirm that LSM-TIDC method significantly captures optimal and relevant features and improves the classification accuracy without compromising CT and computational overhead. </p>", "Keywords": "White blood cells; Interpolative; <PERSON><PERSON><PERSON> stained; Multi-directional; Transformation invariant deep classification; Deep convolutional neural network", "DOI": "10.1007/s00500-019-04662-4", "PubYear": 2020, "Volume": "24", "Issue": "16", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "SASTRA Deemed to be University, Thanjavur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "SASTRA Deemed to be University, Thanjavur, India"}], "References": []}, {"ArticleId": 79373169, "Title": "Enhancing the security in RSA and elliptic curve cryptography based on addition chain using simplified Swarm Optimization and Particle Swarm Optimization for mobile devices", "Abstract": "<p>Security is the major concern in mobile or portable devices because the internet community can do their work at any time at any place at anywhere. Today various cryptographic algorithms like RSA, Elliptic Curve Cryptography (ECC), etc., can be used to protect the information in mobile devices. But, they have some limitations viz., energy, battery power, processing speed, operating systems, screen size, resolution, memory size, etc. Providing security for limited power mobile devices is a challenging task. RSA and ECC are normally used in mobile devices. In RSA, both encryption and decryption are of the form x<sup>e</sup> mod n and in ECC, the scalar point k[P] where k is a scalar and P is a point in EC plays a vital role in performing encryption and decryption. The point arithmetic involved in ECC is a power starving process. To speed up the operations in both cryptographic algorithms, addition chains (AC) are normally used. If the encryption and decryption time get reduced, it ultimately reduces the power consumption. There are several AC algorithms exist in the literature. But, ACs are generated using Particle Swarm Optimization and Simplified Swarm Optimization are proposed in this paper and they are used in the said processes of RSA and ECC with two android and window emulators. The processing time, power consumption taken for encryption, decryption process and security of the said algorithms are also analysed.</p>", "Keywords": "RSA; ECC; Addition chain; PSO and SSO", "DOI": "10.1007/s41870-019-00413-8", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Seethalakshmi <PERSON>wami College (Autonomous), Affiliated to Bharathidasan University, Trichy, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Nehru Memorial College (Autonomous), Affiliated to Bharathidasan University, Trichy, India"}], "References": []}, {"ArticleId": 79373353, "Title": "Approximation algorithms for tours of orientation-varying view cones", "Abstract": "<p>This article considers the problem of finding a shortest tour to visit viewing sets of points on a plane. Each viewing set is represented as an inverted view cone with apex angle [Formula: see text] and height [Formula: see text]. The apex of each cone is restricted to lie on the ground plane. Its orientation angle (tilt) [Formula: see text] is the angle difference between the cone bisector and the ground plane normal. This is a novel variant of the 3D Traveling Salesman Problem with Neighborhoods (TSPN) called Cone-TSPN. One application of Cone-TSPN is to compute a trajectory to observe a given set of locations with a camera: for each location, we can generate a set of cones whose apex and orientation angles [Formula: see text] and [Formula: see text] correspond to the camera’s field of view and tilt. The height of each cone [Formula: see text] corresponds to the desired resolution. Recently, <PERSON><PERSON><PERSON> and <PERSON><PERSON> presented an approximation algorithm for Cone-TSPN for the case where all cones have a uniform orientation angle of [Formula: see text]. We study a new variant of Cone-TSPN where we relax this constraint and allow the cones to have non-uniform orientations. We call this problem Tilted Cone-TSPN and present a polynomial-time approximation algorithm with ratio [Formula: see text], where [Formula: see text] is the set of all cone heights. We demonstrate through simulations that our algorithm can be implemented in a practical way and that by exploiting the structure of the cones we can achieve shorter tours. Finally, we present experimental results from various agriculture applications that show the benefit of considering view angles for path planning.</p>", "Keywords": "", "DOI": "10.1177/0278364919893455", "PubYear": 2020, "Volume": "39", "Issue": "4", "JournalId": 943, "JournalTitle": "The International Journal of Robotics Research", "ISSN": "0278-3649", "EISSN": "1741-3176", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}, {"AuthorId": 3, "Name": "Volkan <PERSON>r", "Affiliation": "University of Minnesota, Minneapolis, MN, USA"}], "References": []}, {"ArticleId": 79373354, "Title": "Spectrum-specific encephalography standardized low-resolution brain electromagnetic tomography network and gray matter correlations in vascular dementia patients", "Abstract": "Vascular dementia, secondary to Alzheimer’s dementia, ranks as one of the most frequent dementia types. The process of vascular dementia is divergent with other neurodegenerative dementias and thus reversible at the early cognitive disorder or mild dementia stages. The encephalography and neuroimaging data mining at different stages would bring neuromodulation strategies in practice; 15 mild cognitive impairment patients and 16 mild vascular dementia patients as well as 17 cognitive healthy controls were screened in this study. Cognitive tests such as Mini-Mental State Examination, Montreal cognitive assessment, voxel-based morphometry, electroencephalography, and standardized low-resolution brain electromagnetic tomography connectivity network were conducted. Compared with healthy group, voxel-based morphometry analysis showed a decrease in gray/cerebrospinal fluid ratio ( p < .05) in mild dementia group; the energy power of gamma band decreased ( p < .05) in mild dementia group; and electroencephalography standardized low-resolution brain electromagnetic tomography analysis showed wider frontal and temporal lobe involvement in mild dementia patients ( p < .05). Network topological analysis screened top 10 key Brodmann areas (44R, 7R, 8L, 22L, 47L, 27L, 1L, 1R, 7R, 43L), which could be underlying neuromodulators for dementia patients. Electroencephalography as well as structural magnetic resonance imaging could be used for the evaluation of cognitive disorder patients. The spectrum-specific standardized low-resolution brain electromagnetic tomography analysis and connectivity network analysis could shed light on the neuromodulator targets in the early phase of dementia.", "Keywords": "", "DOI": "10.1177/1550147719895960", "PubYear": 2020, "Volume": "16", "Issue": "1", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "Yan Lv", "Affiliation": "Department of Neurology, Hainan General Hospital, Haikou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Radiology, Hainan General Hospital, Haikou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurology, Hainan General Hospital, Haikou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurology, Hainan General Hospital, Haikou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurology, Hainan General Hospital, Haikou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Radiology, Hainan General Hospital, Haikou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Neurology, Hainan General Hospital, Haikou, China;Department of Neuroelectrophysiology, Hainan General Hospital, Haikou, China"}], "References": []}, {"ArticleId": 79373422, "Title": "A Novel Model Predictive Runge–<PERSON>tta Neural Network Controller for Nonlinear MIMO Systems", "Abstract": "<p>In this paper, a novel model predictive Runge–Kutta neural network (RK-NN) controller based on Runge–Kutta model is proposed for nonlinear MIMO systems. The proposed adaptive controller structure incorporates system model which provides to approximate the K-step ahead future behaviour of the controlled system, nonlinear controller where Runge–Kutta neural network (RK-NN) controller is directly deployed and adjustment mechanism based on <PERSON><PERSON> optimization method so as to optimize the weights of the Runge–Kutta neural network (RK-NN) controller. RBF neural network is employed as constituent network in order to identify the changing rates of the controller dynamics. So, the learning ability of RBF neural network and Runge Kutta integration method are combined in the MIMO nonlinear controller block. The control performance of the proposed MIMO RK-NN controller has been examined via simulations performed on a nonlinear three tank system and Van <PERSON> benchmark system for different cases, and the obtained results indicate that the RK-NN controller and Runge–Ku<PERSON> model achieve good control and modeling performances for nonlinear MIMO dynamical systems.</p>", "Keywords": "Adaptive nonlinear MIMO controller; Nonlinear model predictive control; Runge–Kutta based system identification; Runge–Kutta EKF; Runge–Kutta neural network controller", "DOI": "10.1007/s11063-019-10167-w", "PubYear": 2020, "Volume": "51", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Faculty of Engineering, Muğla Sıtkı Koçman University, Muğla, Turkey"}], "References": []}, {"ArticleId": 79373432, "Title": "Graph‐based tag recommendations using clusters of patients in clinical decision support system", "Abstract": "<p>To support health professionals in making decisions, CDSS are developed to manage the patients' EHR, improve the way of diagnosis, and treatment of diseases. The process of analyzing EHRs is based on reading free‐text notes. However, it spends time and physicians' efforts. In this case, the most used solution is describing the EHRs with shortcut tags, representing pathologies or diseases, which are well‐defined and meaningful information. Still, this solution remains insufficient. The exploration of the relationship between those tags, the EHRs and their belonging patients will improve the analysis and then the CDSS. In this paper, we present a graph‐based tag recommendation approach that suggests relevant tags (diseases and pathologies) by analyzing the tagged medical images. We use graph analytics to generate graphs of tags, patients, and images by inspecting similar medical images descriptive. We have also created sub‐communities of patients with the same diseases by applying the <PERSON><PERSON><PERSON> clustering method. The tag recommendation aims to enhance the computer‐aided diagnosis in medical imaging. The tag recommendation approach will allow radiologists to detect and interpret invisible diseases of the underlying anatomical structure. It will also help in early revealing and diagnosis. The dataset ChestX‐Ray14 has been conducted to evaluate and test the accuracy and effectiveness of the proposed approach. Future perspective will focus on the deployment of our proposal within a Moroccan e‐health project.</p>", "Keywords": "clinical decision support system CDSS;community detection;graph‐based model;moroccan e‐health;network analysis;tag recommendations", "DOI": "10.1002/cpe.5624", "PubYear": 2021, "Volume": "33", "Issue": "1", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory ISI, Cadi Ayyad University, Marrakesh, Morocco; <PERSON><PERSON><PERSON>, Laboratory ISI, Cadi Ayyad University, 40000 Marrakesh, Morocco."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory ISI, Cadi Ayyad University, Marrakesh, Morocco; CSEHS, Mohammed VI Polytechnic University, Ben Guerir, Morocco"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory ISI, Cadi Ayyad University, Marrakesh, Morocco"}], "References": []}, {"ArticleId": 79373448, "Title": "Human Behavior Recognition from Multiview Videos", "Abstract": "With the proliferation of deep learning techniques, a significant number of applications related to home care systems have emerged recently. In particular, detecting abnormal events in a smart home environment has been extensively studied. In this paper, we adopt deep learning techniques, including convolutional neural networks (CNNs) and long short-term memory (LSTM) networks, to construct deep networks to learn the long-term dependencies from videos for human behavior recognition in a multiview framework. We adopt two cameras as our sensors to efficiently overcome the problem of occlusions and contour ambiguity for improving the accuracy performance of the multiview framework. After performing a series of image preprocessing on the raw data, we obtain human silhouette images as the input to our training model. In addition, because real-world datasets are complicated for analysis, labeling data is time consuming. Therefore, we present an image clustering method based on a stacked convolutional autoencoder (SCAE), which generates clustering labels for autolabeling. Finally, we set up our experimental environment as a normal residence to collect a large dataset, and the experimental results demonstrate the novelty of our proposed models.", "Keywords": "Human Behavior Recognition ; Multiview Framework ; Convolutional Neural Network ; Long Short-Term Memory Network ; Deep Learning ; Autoencoding ; Image Clustering", "DOI": "10.1016/j.ins.2020.01.002", "PubYear": 2020, "Volume": "517", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Information Engineering, Taiwan;Center for Innovative Research on Aging Society (CIRAS), Taiwan;Advanced Institute of Manufacturing with High-tech Innovations (AIM-HI), National Chung Cheng University, Taiwan;Corresponding author at: Department of Computer Science & Information Engineering, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Taiwan;Center for Innovative Research on Aging Society (CIRAS), Taiwan;Advanced Institute of Manufacturing with High-tech Innovations (AIM-HI), National Chung Cheng University, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Information Engineering, Taiwan"}], "References": []}, {"ArticleId": 79373542, "Title": "A reduced universum twin support vector machine for class imbalance learning", "Abstract": "In most of the real world datasets, there is an imbalance in the number of samples belonging to different classes. Various pattern classification problems such as fault or disease detection involve class imbalanced data. The support vector machine (SVM) classifier becomes biased towards the majority class due to class imbalance. Moreover, in the existing SVM based techniques for class imbalance, there is no information about the distribution of data. Motivated by the idea of prior information about data distribution, a reduced universum twin support vector machine for class imbalance learning (RUTSVM-CIL) is proposed in this paper. For the first time, universum learning is incorporated with SVM to solve the problem of class imbalance. Oversampling and undersampling of data is performed to remove the imbalance in the classes. The universum data points are used to give prior information about the data. To reduce the computation time of our universum based algorithm, we use a small sized rectangular kernel matrix. The reduced kernel matrix needs less storage space, and thus applicable for large scale imbalanced datasets. Comprehensive experimentation is performed on various synthetic, real world and large scale imbalanced datasets. In comparison to the existing approaches for class imbalance, the proposed RUTSVM-CIL gives better generalization performance for most of the benchmark datasets. Also, the computation cost of RUTSVM-CIL is very less, making it suitable for real world applications.", "Keywords": "Universum ; Rectangular kernel ; Class imbalance ; Imbalance ratio ; Twin support vector machine", "DOI": "10.1016/j.patcog.2019.107150", "PubYear": 2020, "Volume": "102", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Discipline of Mathematics, Indian Institute of Technology Indore, Simrol, Indore, 453552, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Discipline of Mathematics, Indian Institute of Technology Indore, Simrol, Indore, 453552, India;Corresponding author"}], "References": []}, {"ArticleId": 79374205, "Title": "A General Framework for Optimal Tuning of PID-like Controllers for Minimum Jerk Robotic Trajectories", "Abstract": "<p>The minimum jerk principle is commonly used for trajectory planning of robotic manipulators. However, since this principle is stated in terms of the robot’s kinematics, there is no guarantee that the joint controllers will actually track the planned acceleration and jerk profiles because the tuning of the controllers’ gains is decoupled from the trajectory planning. Bearing this in mind, in this paper we introduce a comprehensive framework for optimal estimation of the gains of PID-like controllers for tracking minimum-jerk (MJ) robot trajectories. The proposed methodology relies mainly on a novel variant of error-based performance indices (ISE, ITSE, IAE and ITAE) which are adapted to the tracking of MJ trajectories. Furthermore, the particle swarm optimization (PSO) algorithm is used to search for optimal values for the gains of the controllers of all joints simultaneously. The resulting approach is much simpler than recent developments based on more complex performance indices, in which joint controllers were individually optimized. The proposed approach is general enough to easily encompass the tuning of fractional PID controllers and a comprehensive set of experiments are reported comparing the performances of standard and fractional PID controllers for the task of interest.</p>", "Keywords": "Robot control; Minimum jerk principle; Fractional PID controller; Performance indices; Particle swarm optimization", "DOI": "10.1007/s10846-019-01121-y", "PubYear": 2020, "Volume": "99", "Issue": "3-4", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Program in Teleinformatics Engineering, Federal University of Ceará, Center of Technology Campus of Pici, Fortaleza, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Program in Teleinformatics Engineering, Federal University of Ceará, Center of Technology Campus of Pici, Fortaleza, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Graduate Program in Teleinformatics Engineering, Federal University of Ceará, Center of Technology Campus of Pici, Fortaleza, Brazil"}], "References": []}, {"ArticleId": 79374307, "Title": "Forecasting Inflection Points: Hybrid Methods with Multiscale Machine Learning Algorithms", "Abstract": "<p>This paper investigates hybrid time series forecasting models, which are based on combinations of ensemble empirical mode decomposition and least squares support vector machines. Several algorithms are considered: the genetic algorithm, the grid search, and particle swarm optimization. Theoretical guarantees of prediction accuracy are tested with sine curves. From a numerical testing perspective, we are interested in showing the superiority of one approach to another based on theoretical prediction and time series applications in finance (S&P 500), commodities (WTI oil price), or cryptocurrencies (Bitcoin). The superiority of hybrid models to soft- and hard-computed models is further assessed through a ‘horse race’ and trading performance, as well as through fine-tuning of the algorithms. </p>", "Keywords": "Genetic algorithms; Ensemble empirical mode decomposition; Least squares support vector machine; Grid Search; Particle swarm optimization; C44; G17; Q47", "DOI": "10.1007/s10614-019-09966-z", "PubYear": 2021, "Volume": "57", "Issue": "2", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IPAG Business School (IPAG Lab), Paris, France;University Paris 8 (LED), Saint-Denis, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Nanjing University of Information Science and Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, University of Melbourne, Parkville, Australia"}], "References": []}, {"ArticleId": 79374576, "Title": "L-algebras in logic, algebra, geometry, and topology", "Abstract": "<p>An intuitive introduction to L -algebras and their relationship to groups with a one- or two-sided lattice ordering is given, with applications in algebra, analysis, and geometry.</p>", "Keywords": "Algebraic logic; L-algebra; -group; p-adic numbers; Projective space; Measure; Integration; Heyting algebra", "DOI": "10.1007/s00500-019-04616-w", "PubYear": 2020, "Volume": "24", "Issue": "5", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Algebra and Number Theory, University of Stuttgart, Stuttgart, Germany"}], "References": []}, {"ArticleId": 79374697, "Title": "HydroDS: Data services in support of physically based, distributed hydrological models", "Abstract": "Physically based distributed hydrologic models require geospatial and time-series data that take considerable time and effort in processing them into model inputs. Tools that automate and speed up input processing facilitate the application of these models. In this study, we developed a set of web-based data services called HydroDS to provide hydrologic data processing ‘software as a service.’ HydroDS provides functions for processing watershed, terrain, canopy, climate, and soil data. The services are accessed through a Python client library that facilitates developing simple but effective data processing workflows with Python. Evaluations of HydroDS by setting up the Utah Energy Balance and TOPNET models for multiple headwater watersheds in the Colorado River basin show that HydroDS reduces the input preparation time compared to manual processing. It also removes the requirements for software installation and maintenance by the user, and the Python workflows enhance reproducibility of hydrologic data processing and tracking of provenance.", "Keywords": "HydroDS ; Web-based data services ; Distributed hydrologic modeling ; Geographic information systems ; Hydrologic data cyberinfrastructure", "DOI": "10.1016/j.envsoft.2020.104623", "PubYear": 2020, "Volume": "125", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "Tseganeh Z. Gichamo", "Affiliation": "Utah Water Research Laboratory, 8200 Old Main Hill, Utah State University, Logan, UT, 84322-8200, USA"}, {"AuthorId": 2, "Name": "Nazmus S<PERSON>", "Affiliation": "Science Application International Corporation, 8 Riverview Ct 302, Laurel, MD, 20707, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Utah Water Research Laboratory, 8200 Old Main Hill, Utah State University, Logan, UT, 84322-8200, USA;Corresponding author"}, {"AuthorId": 4, "Name": "Pabitra Dash", "Affiliation": "Utah Water Research Laboratory, 8200 Old Main Hill, Utah State University, Logan, UT, 84322-8200, USA"}], "References": [{"Title": "UEB parallel: Distributed snow accumulation and melt modeling using parallel computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "125", "Issue": "", "Page": "104614", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 79374717, "Title": "Optimized distributed large-scale analytics over decentralized data sources with imperfect communication", "Abstract": "<p>Tremendous amounts of data are generated by sensors and connected devices with high velocity in a variety of forms and large volumes. These characteristics, defined as big data, need new models and methods to be processed in near real-time. The nature of decentralized large-scale data sources requires distributed algorithms in which it is assumed that the data sources are capable of processing their own data and collaborating with neighbor sources. The network objective is to make an optimal decision, while the data are processed in a distributed manner. New technologies, like next generation of wireless communication and 5G, introduce practical issues such as imperfect communication that should be addressed. In this paper, we study a generalized form of distributed algorithms for decision-making over decentralized data sources. We propose an optimal algorithm that uses optimal weighting to combine the resource of neighbors. We define an optimization problem and find the solution by applying the proposed algorithm. We evaluate the performance of the developed algorithm by using both mathematical methods and computer simulations. We introduce the conditions in which the convergence of proposed algorithm is guaranteed and prove that the network error decreases considerably in comparison with some of the known modern methods.</p>", "Keywords": "Big data; Large scale; Optimization; Distributed; Imperfect communication", "DOI": "10.1007/s11227-019-03129-5", "PubYear": 2020, "Volume": "76", "Issue": "11", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, University of Calabria (UniCal), Rende, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical, Energy and Management Engineering, University of Calabria (UniCal), Rende, Italy"}], "References": []}, {"ArticleId": 79374837, "Title": "Can we learn from simplified simulation models? An experimental study on user learning", "Abstract": "Simple models are considered useful for decision making, especially when decisions are made by a group of stakeholders. This paper describes an experimental study that investigates whether the level of model detail affects users’ learning. Our subjects, undergraduate students, were asked to solve a resource utilisation task for an ambulance service problem. They worked in groups under three different conditions, based on the type of simulation model used (specifically a simple, adequate or no model at all), to analyse the problem and reach conclusions. A before and after questionnaire and a group presentation capture the participants’ individual and group attitudes towards the solution. Our results suggest that differences in learning from using the two different models were not significant, while simple model users demonstrated a better understanding of the problem. The outcomes and implications of our findings are discussed, alongside the limitations and future work.", "Keywords": "Discrete-event simulation ; simple models ; complexity ; learning ; behavioural operational research", "DOI": "10.1080/17477778.2019.1704636", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 31495, "JournalTitle": "Journal of Simulation", "ISSN": "1747-7778", "EISSN": "1747-7786", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business and Economics, Loughborough University, Loughborough, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business and Economics, Loughborough University, Loughborough, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Business and Economics, Loughborough University, Loughborough, UK"}], "References": []}, {"ArticleId": 79374871, "Title": "Novel features for intensive human activity recognition based on wearable and smartphone sensors", "Abstract": "<p>On the lap of this modern era, human activity recognition (HAR) has been of great help in case of health monitoring and rehabilitation. Existing works mostly use one or more specific devices (with embedded sensors) including smartphones for activity recognition and most of the time the detected activities are coarse grained like sit or walk rather than detailed and intensive like sit carrying weight or walk carrying weight . But, intensity of activities reflects valuable insight about a person’s health and more importantly, physical exertion for performing those activities. Consequently, in this paper, we propose an intense activity recognition framework that combines features from smartphone accelerometer (available in almost every smartphone) and that from wearable heartrate sensor. We introduce a set of novel heartrate features that takes into consideration finer variation of heartrate as compared to the resting heartrate of an individual. The proposed framework forms an ensemble model based on different classifiers to address the challenge of usage behavior in terms of how the smartphone is carried. The stack generalization based ensemble model predicts the intensity of activity. We have implemented the framework and tested for a real dataset collected from four users. We have observed that our work is able to identify both static and dynamic intense activities with 96% accuracy, and even found to be better than state of the art techniques.</p>", "Keywords": "", "DOI": "10.1007/s00542-019-04738-z", "PubYear": 2020, "Volume": "26", "Issue": "6", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Jadavpur University, Kolkata, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Jadavpur University, Kolkata, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Jadavpur University, Kolkata, India"}], "References": [{"Title": "Performance analysis of gas sensing device and corresponding IoT framework in mines", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "11", "Page": "3977", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 79375350, "Title": "A neighbour scale fixed approach for influence maximization in social networks", "Abstract": "<p>Influence maximization is currently a most extensively researched topic in social network analysis. Existing approaches tackle this task by either pursuing the real influence strength of a node or designing proper measurements for estimating it. The degree is a popularly adopted influence strength metric, based on which a variety of methods have been developed. Though with good efficiency, degree-based methods suffer unsatisfactory accuracy since this metric only covers a limited considered scale over the whole network of interest and also lacks discriminatory power. In this paper, we propose a novel influence maximization method, named Fixed Neighbour Scale (FNS), which extracts useful information from multiple levels of neighbours for a target node to estimate its influence strength, rather than only considering directly connected neighbours as in degree-based methods. To facilitate the implementation of FNS, we also present a centrality measurement termed FNS-dist, which estimates a node’s influence strength by summing its multi-level neighbours’ weights that are mainly determined by their distances to the target node. Experiments conducted on nine networks of different sizes and categories show that the proposed FNS method achieves excellent and stable performance compared with other algorithms based on designing metrics for measuring influence strength. We also exhibit that FNS-dist is a superior alternative centrality which is more proper and precise than the degree. </p>", "Keywords": "Influence maximization; Social network; Influence strength; Fixed neighbour scale; Multi-level neighbour; 91D30", "DOI": "10.1007/s00607-019-00778-5", "PubYear": 2020, "Volume": "102", "Issue": "2", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China;Mine Digitization Engineering Research Center of the Ministry of Education, Xuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of North Carolina at Charlotte, Charlotte, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China;Mine Digitization Engineering Research Center of the Ministry of Education, Xuzhou, China"}], "References": []}, {"ArticleId": 79375487, "Title": "The effect of stereoisomerism on the 4D-QSAR study of some dipeptidyl boron derivatives", "Abstract": "<p>The electron conformational genetic algorithm (EC-GA) method had been employed by distinguishing between enantiomers for the first time as a 4D-QSAR approach to reveal the pharmacophore (Pha) and to predict the bioactivity of the dipeptidyl boron compounds. The Electron Conformational Matrices of Congruity (ECMCs) were prepared for all conformers of compounds in the data set based on the quantum chemical calculations at HF/3-21 G level in an aqueous medium. The comparison of the ECMCs within the certain tolerances by the EMRE program revealed the pharmacophore for some dipeptidyl boron derivatives. For the selection of the most influential parameters on the activity and the calculation of theoretical activities, the genetic algorithm with the non-linear least square method was used. The final model was validated by the cross-validation method with the division of the data set into training and test items. The 12-parameter model gave excellent statistical results (R<sup>2</sup><sub>training</sub> = 0.850, R<sup>2</sup><sub>test</sub> = 0.809, q<sup>2</sup> = 0.755, q<sup>2</sup><sub>ext1</sub> = 0.776, q<sup>2</sup><sub>ext2</sub> = 0.759, q<sup>2</sup><sub>ext3</sub> = 0.735, CCC<sub>tr</sub> = 0.922, CCC<sub>test</sub> = 0.846, CCC<sub>all</sub> = 0.905). Because of the inexistence of 4D-QSAR studies on the dipeptidyl boron derivatives and the stereoisomerism effect on the biological activity was examined for the first time for these compounds, this study plays an important role in the development of new boron-containing compounds.</p><p>Copyright © 2019 Elsevier Ltd. All rights reserved.</p>", "Keywords": "4D-QSAR;Dipeptidyl boron;Electron conformational-genetic algorithm method;Pharmacophore;Stereoisomerism", "DOI": "10.1016/j.compbiolchem.2019.107190", "PubYear": 2020, "Volume": "84", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Erciyes University, Science Faculty, Department of Chemistry, Kayseri, Turkey."}, {"AuthorId": 2, "Name": "Nazmiye Sabancı", "Affiliation": "Siirt University, Science and Arts Faculty, Department of Chemistry, Siirt, Turkey. Electronic address:  ."}, {"AuthorId": 3, "Name": "Sevtap Çağlar Yavuz", "Affiliation": "Erciyes University, Science Faculty, Department of Chemistry, Kayseri, Turkey; Yozgat Bozok University, Şefaatli Vocational School, Department of Veterinary, Yozgat, Turkey."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Erciyes University, Science Faculty, Department of Chemistry, Kayseri, Turkey."}], "References": []}, {"ArticleId": 79375847, "Title": "An Evolutionary Neuro-Fuzzy C-means Clustering Technique", "Abstract": "One of the standard approaches for data analysis in unsupervised machine learning techniques is cluster analysis or clustering, where the data possessing similar features are grouped into a certain number of clusters. Among several significant ways of performing clustering, Fuzzy C-means (FCM) is a methodology, where every data point is hypothesized to be associated with all the clusters through a fuzzy membership function value. FCM is performed by minimizing an objective functional by optimally estimating the decision variables namely, the membership function values and cluster representatives, under a constrained environment. With this approach, a marginal increase in the number of data points leads to an enormous increase in the size of decision variables. This explosion, in turn, prevents the application of evolutionary optimization solvers in FCM, which thereby leads to inefficient data clustering. In this paper, a Neuro-Fuzzy C-Means Clustering algorithm (NFCM) is presented to resolve the issues mentioned above by adopting a novel Artificial Neural Network (ANN) based clustering approach. In NFCM, a functional map is constructed between the data points and membership function values, which enables a significant reduction in the number of decision variables. Additionally, NFCM implements an intelligent framework to optimally design the ANN structure, as a result of which, the optimal number of clusters is identified. Results of 9 different data sets with dimensions ranging from 2 to 30 are presented along with a comprehensive comparison with the current state-of-the-art clustering methods to demonstrate the efficacy of the proposed algorithm.", "Keywords": "Evolutionary optimization ; Artificial neural network ; Fuzzy C-means clustering ; Optimal cluster number ; Variable size reduction", "DOI": "10.1016/j.engappai.2019.103435", "PubYear": 2020, "Volume": "89", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Global Optimization and Knowledge Unearthing Laboratory, Department of Chemical Engineering, Indian Institute of Technology Hyderabad, Sangareddy, Telangana, 502 285, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Global Optimization and Knowledge Unearthing Laboratory, Department of Chemical Engineering, Indian Institute of Technology Hyderabad, Sangareddy, Telangana, 502 285, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Global Optimization and Knowledge Unearthing Laboratory, Department of Chemical Engineering, Indian Institute of Technology Hyderabad, Sangareddy, Telangana, 502 285, India;Corresponding author"}], "References": []}, {"ArticleId": 79375915, "Title": "Feature selection optimisation of software product line using metaheuristic techniques", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJES.2020.10026155", "PubYear": 2020, "Volume": "1", "Issue": "1", "JournalId": 31256, "JournalTitle": "International Journal of Embedded Systems", "ISSN": "1741-1068", "EISSN": "1741-1076", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 79376094, "Title": "A zone-based content pre-caching strategy in vehicular edge networks", "Abstract": "Content pre-caching is a kind of significant technology to lower response delay and improve network performance, especially for the delay-sensitive services in dynamic vehicular edge networks. Therefore, in this paper, we propose a zone-based content pre-caching strategy, which aims to implement an active content caching through two algorithms: pre-caching zone selecting algorithm- P C Z S and pre-caching node selecting algorithm- P C N S . Firstly, we organize the edge servers ( E S s) with a zone-based way at the edge, and assign a M a n a g e r node to collect the information of each zone; with the help of road topology and tables information recorded in M a n a g e r nodes, P C Z S can predict the vehicle motion and zone sojourn time with a high accuracy, and further get a content pre-caching zone by comparing estimated request delay and zone sojourn time; then, P C N S checks whether the content has been cached in the C S T of the zone selected by P C Z S , if C S T hits, the pre-caching process is over, otherwise, by combining E S centrality, load degree with content popularity, we perform P C N S to select a specific E S node to pre-cache the content; simulation results show that our strategy has a higher prediction accuracy and dynamic adaptability, it also outperforms in terms of average response delay and cache hit ratio.", "Keywords": "Vehicular edge networks ; Content pre-caching ; Zone-based ; Zone sojourn time", "DOI": "10.1016/j.future.2019.12.050", "PubYear": 2020, "Volume": "106", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing 100876, China"}, {"AuthorId": 2, "Name": "Lan-lan <PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing 100876, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>g <PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing 100876, China"}], "References": []}, {"ArticleId": 79376283, "Title": "A taxonomy and archetypes of smart services for smart living", "Abstract": "<p>Smart service integrates digital and physical competencies for automated service delivery in smart service systems to co-create value. Smart services envelop digital services delivered through smart products. The latter act as boundary objects to the consumer. Smart services are capable of learning, adapting, and decision-making based on communicated data through self-controlled functions. Due to the multidisciplinary discourse, there is a knowledge gap concerning common ground for central concepts, the transformative potential of smart products as well as evidence-based design knowledge derived from real-world services. In this paper, we apply conceptual research and data analysis to construct a taxonomy that supplies this common ground for smart service. The resulting taxonomy comprises 8 dimensions with 20 characteristics. Based on an empirical analysis of 100 smart services from the smart living sector, we performed a cluster analysis to derive five archetypes that classifies smart service as either monitor, command execution, diagnostics and automation, personal tracker, or trainable assistant using smart products as boundary objects for distinct purposes.</p>", "Keywords": "Smart service; Smart service system; Internet of Things; Taxonomy; Archetype; Smart living; L8; O3", "DOI": "10.1007/s12525-019-00384-5", "PubYear": 2020, "Volume": "30", "Issue": "1", "JournalId": 16578, "JournalTitle": "Electronic Markets", "ISSN": "1019-6781", "EISSN": "1422-8890", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Business Management and Economics, University of Würzburg, Würzburg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Business Management and Economics, University of Würzburg, Würzburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Business Management and Economics, University of Würzburg, Würzburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Business Management and Economics, University of Würzburg, Würzburg, Germany;Faculty of Business and Economics, Technische Universität Dresden, Dresden, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Business Management and Economics, University of Würzburg, Würzburg, Germany;PUMA SE, Herzogenaurach, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Business Management and Economics, University of Würzburg, Würzburg, Germany"}], "References": []}, {"ArticleId": 79376397, "Title": "Long short-term memory for predicting daily suspended sediment concentration", "Abstract": "<p>Frequent and accurate estimation of suspended sediment concentration (SSC) in surface waters and hydraulic schemes is of prime importance for proper design, operation and management of many hydraulic projects. in the present study, a long short-term memory (LSTM) was considered for predicting daily suspended sediment concentration in a river. The LSTM extends recurrent neural network with memory cells, instead of recurrent units, to store and output information, easing the learning of temporal relationships on long time scales. To build the model, daily observed time series of river discharge ( Q ) and SSC in the Schuylkill River in the United States were used. The results of the proposed model were evaluated and compared with the feedforward neural network and the adaptive neuro fuzzy inference system models which were trained using three different learning algorithms and widely used in the literature for prediction of daily SSC. The comparison of prediction accuracy of the models demonstrated that the LSTM model could satisfactory predict SSC time series, and adequately estimate cumulative suspended sediment load (SSL).</p>", "Keywords": "Long short-term memory; Fuzzy inference system; Schuylkill river; Suspended sediments", "DOI": "10.1007/s00366-019-00921-y", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Hydraulic and Water Resources Engineering, Technische Universität München, Munich, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Management, University of Tehran, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Hydraulic and Water Resources Engineering, Technische Universität München, Munich, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Hydraulic and Water Resources Engineering, Technische Universität München, Munich, Germany"}], "References": []}, {"ArticleId": 79376398, "Title": "A new improved whale optimization algorithm with joint search mechanisms for high-dimensional global optimization problems", "Abstract": "<p>Similar to other swarm-based algorithms, the recently developed whale optimization algorithm (WOA) has the problems of low accuracy and slow convergence. It is also easy to fall into local optimum. Moreover, WOA and its variants cannot perform well enough in solving high-dimensional optimization problems. This paper puts forward a new improved WOA with joint search mechanisms called JSWOA for solving the above disadvantages. First, the improved algorithm uses tent chaotic map to maintain the diversity of the initial population for global search. Second, a new adaptive inertia weight is given to improve the convergence accuracy and speed, together with jump out from local optimum. Finally, to enhance the quality and diversity of the whale population, as well as increase the probability of obtaining global optimal solution, opposition-based learning mechanism is used to update the individuals of the whale population continuously during each iteration process. The performance of the proposed JSWOA is tested by twenty-three benchmark functions of various types and dimensions. Then, the results are compared with the basic WOA, several variants of WOA and other swarm-based intelligent algorithms. The experimental results show that the proposed JSWOA algorithm with multi-mechanisms is superior to WOA and the other state-of-the-art algorithms in the competition, exhibiting remarkable advantages in the solution accuracy and convergence speed. It is also suitable for dealing with high-dimensional global optimization problems.</p>", "Keywords": "Whale optimization algorithm; Tent chaotic map; Adaptive inertia weight; Opposition-based learning; High-dimensional optimization problems", "DOI": "10.1007/s00366-019-00917-8", "PubYear": 2021, "Volume": "37", "Issue": "3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Civil Engineering, Fuzhou University, Fuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Civil Engineering, Fuzhou University, Fuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Land Surveying and Geo-Informatics, The Hong Kong Polytechnic University, Kowloon, China"}, {"AuthorId": 4, "Name": "Zhanghua Xia", "Affiliation": "College of Civil Engineering, Fuzhou University, Fuzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Yu", "Affiliation": "College of Civil Engineering, Hunan University, Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Wang", "Affiliation": "College of Civil Engineering, Fuzhou University, Fuzhou, China"}], "References": []}, {"ArticleId": 79376922, "Title": "Lagrange Stability for Delayed-Impulses in Discrete-Time Cohen–Grossberg Neural Networks with Delays", "Abstract": "<p>In this paper, the problem of exponential Lagrange stability for delayed-impulses in discrete-time Cohen–<PERSON> neural networks (CGNNs) with delays is considered. By establishing a novel convergent difference inequation, combining with inductive method and <PERSON><PERSON><PERSON><PERSON> theory, some sufficient conditions are obtained to ensure the exponential Lagrange stability for delayed-impulses in discrete-time CGNNs. Meanwhile, the exponential convergent domain for network is given. Finally, some examples with their simulations are given to verify the effectiveness of our results.</p>", "Keywords": "Delayed impulse; Discrete-time neural networks; Exponential Lagrange stability; <PERSON> neural network", "DOI": "10.1007/s11063-020-10190-2", "PubYear": 2020, "Volume": "51", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Chongqing Three Gorges University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Chongqing Three Gorges University, Chongqing, China;Key Laboratory of Intelligent information Processing and Control of Chongqing Municipal Institutions of Higher Education, Chongqing Three Gorges University, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Chongqing Three Gorges University, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent information Processing and Control of Chongqing Municipal Institutions of Higher Education, Chongqing Three Gorges University, Chongqing, China"}], "References": []}, {"ArticleId": 79377009, "Title": "Detection and multi-class classification of falling in elderly people by deep belief network algorithms", "Abstract": "<p>According to the reports on aging population, the number of elderly people without a caregiver has increased. These people are always at high risks of adverse incidents such as increased blood pressure, a variety of stroke-leading health issues, as well as other accidents resulting in body instability and eventually hazardous falls. An uncontrolled fall can result in far worse situations than the original cause itself, if the unattended patient is not promptly transmitted to a treatment center. To reduce the adverse consequences of such unfortunate events, the demand for intelligent systems to prevent, detect, and report the incidents has significantly increased during the past decade. So far, many studies have been proposed considering different aspects of the fall detection problem, from simple applied systems to complex ones regarding the detection algorithm and feature extraction methods. In this paper, a framework for smart detection, identification and notification of elderly falls is introduced. Using a personal smartphone, the tri-axial acceleration of the person’s movements is measured, and the related features are extracted following a pre-processing and timing the samples with a predefined window. The deep belief network (DBN) is used next for training and testing the system using two public datasets, with nine classes of fall and one class of daily activity. Simulation results on two generic datasets, TFall and MobiFall, show an accuracy of 97.56% sensitivity and 97.03% specificity, which is promising compared to nine other related studies.</p>", "Keywords": "Fall detection; Feature selection; Deep belief network; Smartphone; Elderly people", "DOI": "10.1007/s12652-020-01690-z", "PubYear": 2020, "Volume": "11", "Issue": "10", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Shahid Chamran University of Ahvaz, Ahvaz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Shahid Chamran University of Ahvaz, Ahvaz, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Shahid Chamran University of Ahvaz, Ahvaz, Iran"}], "References": [{"Title": "Fall detection and human activity classification using wearable sensors and compressed sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "349", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A smart inertial system for fall detection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "4503", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Online Fall Detection Using Recurrent Neural Networks on Smart Wearable Devices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1276", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}]}, {"ArticleId": 79377158, "Title": "Lung Cancer Prediction Using Stochastic Diffusion Search (SDS) Based Feature Selection and Machine Learning Methods", "Abstract": "<p>The symptoms of cancer normally appear only in the advanced stages, so it is very hard to detect resulting in a high mortality rate among the other types of cancers. Thus, there is a need for early prediction of lung cancer for the purpose of diagnosing and this can result in better chances of it being able to be treated successfully. Histopathology images of lung scan can be used for classification of lung cancer using image processing methods. The features from lung images are extracted and employed in the system for prediction. Grey level co-occurrence matrix along with the methods of Gabor filter feature extraction are employed in this investigation. Another important step in enhancing the classification is feature selection that tends to provide significant features that helps differentiating between various classes in an accurate and efficient manner. Thus, optimal feature subsets can significantly improve the performance of the classifiers. In this work, a novel algorithm of feature selection that is wrapper-based is proposed by employing the modified stochastic diffusion search (SDS) algorithm. The SDS, will benefit from the direct communication of agents in order to identify optimal feature subsets. The neural network, Naïve Bayes and the decision tree have been used for classification. The results of the experiment prove that the proposed method is capable of achieving better levels of performance compared to existing methods like minimum redundancy maximum relevance, and correlation-based feature selection.</p>", "Keywords": "Lung cancer; Small cell lung cancer (SCLC); Non-small cell lung cancer (NSCLC); Radiomic features; Gray level co-occurrence matrix (GLCM); Gabor filter; Stochastic diffusion search (SDS); Neural network (NN); Naive Bayes and decision tree", "DOI": "10.1007/s11063-020-10192-0", "PubYear": 2021, "Volume": "53", "Issue": "4", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Anna University, Chennai, India;Department of CSE, Sri Eshwar College of Engineering, Coimbatore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hindusthan College of Engineering and Technology, Coimbatore, India"}], "References": [{"Title": "Lung nodule detection and classification based on geometric fit in parametric form and deep learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "4629", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 79377164, "Title": "Temporal design for additive manufacturing", "Abstract": "Abstract Additive manufacturing (AM) is expected to generate huge economic revenue by 2025; however, this will only be realised by overcoming the barriers that are preventing its increased adoption to end-use parts. Design for AM (DfAM) is recognised as a multi-faceted problem, exasperated by constraints to creativity, knowledge propagation, insufficiencies in education and a fragmented software pipeline. This study proposes a novel approach to increase the creativity in DfAM. Through comparison between DfAM and in utero human development, the unutilised potential of design through the time domain was identified. Therefore, the aim of the research is to develop a computer-aided manufacturing (CAM) programme to demonstrate design through the time domain, known as Temporal DfAM (TDfAM). This was achieved through a bespoke MATLAB code which applies a linear function to a process parameter, discretised across the additive build. TDfAM was demonstrated through the variation of extrusion speed combined with the infill angle, through the axial and in-plane directions. It is widely accepted in the literature that AM processing parameters change the properties of AM materials. Thus, the application of the TDfAM approach offers the engineer increased creative scope and control, whilst inherently upskilling knowledge, in the design of AM materials.", "Keywords": "Additive manufacturing; Design; Developmental biology; Computer-aided manufacturing; CAM; CAD; Innovation", "DOI": "10.1007/s00170-019-04835-3", "PubYear": 2020, "Volume": "106", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "S. Saliba", "Affiliation": "Department of Mechanical Engineering, School of Engineering, University of Birmingham, Birmingham, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>-<PERSON>", "Affiliation": "Centre for Human Reproductive Science, Institute of Metabolism and Systems Research, College of Medical and Dental Sciences, University of Birmingham, Birmingham, UK"}, {"AuthorId": 3, "Name": "L. E<PERSON> <PERSON>", "Affiliation": "Department of Mechanical Engineering, School of Engineering, University of Birmingham, Birmingham, UK"}], "References": []}, {"ArticleId": ********, "Title": "Solving an integrated cell formation and group layout problem using a simulated annealing enhanced by linear programming", "Abstract": "<p>This paper proposes a new approach to integrating the cell formation, group layout of rectangle-shaped machines and routing selection problems. The problem is formulated as a mixed-integer program with the objective of minimizing the handling costs. Due to the computational complexity of the problem, a hybrid simulated annealing (SA) is employed to solve the problem. The sequence-pair representation, originally proposed for block placement, is utilized for solution encoding. Two placement algorithms are developed to evaluate the objective function value of an encoded solution in the SA. The first placement algorithm is based on solving a linear program embedded with a constraint reduction algorithm. The second one is a fast heuristic that can evaluate an encoded solution in much less computational time. Benchmarks selected from the literature are solved to verify the performance of the SA and to accomplish comparisons. The computational results demonstrated the high performance of both designed hybrid SA algorithms. The comparison against the literature also indicated that the flexibility incorporated into the model results in better layouts, even if the model is solved only by a solver such as CPLEX. </p>", "Keywords": "Cell formation; Group layout; Simulated annealing; Heuristic; Linear programming; Hybrid metaheuristic", "DOI": "10.1007/s00500-019-04626-8", "PubYear": 2020, "Volume": "24", "Issue": "15", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Amirkabir University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "S. M. <PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Amirkabir University of Technology, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Logistics, Tourism and Service Management, Faculty of Business and Economics, German University of Technology in Oman (GUtech), Muscat, Oman;Department of Industrial Engineering, Firoozkooh Branch, Islamic Azad University, Firoozkooh, Iran"}], "References": []}, {"ArticleId": 79377213, "Title": "Odd and Even Lidstone-type polynomial sequences. Part 2: applications", "Abstract": "Abstract <p>In this paper we consider some applications of Odd and Even Lidstone-type polynomial sequences. In particular we deal with the Odd and Even Lidstone-type and the Generalized Lidstone interpolatory problems with respect to a linear functional (L_1) and, respectively, (L_2) . Estimations of the remainder for the related interpolation polynomials are given. Numerical examples are provided. Some possible applications of these interpolant polynomials to BVPs, expansions of analytical real functions and numerical quadrature are sketched.</p>", "Keywords": "Polynomial sequences; Odd and Even polynomials; Li<PERSON><PERSON>; <PERSON><PERSON><PERSON> and <PERSON><PERSON> polynomials; Boundary value problems; Quadrature formulas; 41A58; 11B83", "DOI": "10.1007/s10092-019-0354-z", "PubYear": 2020, "Volume": "57", "Issue": "1", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "Francesco Aldo <PERSON>", "Affiliation": "Department of Mathematics and Computer Science, University of Calabria, Rende, Italy"}, {"AuthorId": 2, "Name": "Maria Italia Gualtieri", "Affiliation": "Department of Mathematics and Computer Science, University of Calabria, Rende, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Computer Science, University of Calabria, Rende, Italy"}], "References": []}, {"ArticleId": 79377237, "Title": "Fine-grained facial image-to-image translation with an attention based pipeline generative adversarial framework", "Abstract": "<p>Fine-grained feature detection and recognition is an important but tough work due to the resolution and noisy representation. Synthesize images with a specified tiny feature is even more challenging. Existing image-to-image generation studies usually focus on improving image generation resolution and increasing the representation learning abilities under coarse features. However, generating images with fine-grained attributes under an image-to-image framework is still a tough work. In this paper, we propose an attention based pipeline generative adversarial network (Atten-Pip-GAN) to generate various facial images under multi-label fine-grained attributes with only a neutral facial image. First, we use a pipeline adversarial structure to generate images with multiple features step by step. Second, we use an independent image-to-image framework as a prepossessing method to detection the small fine-grained features and provide an attention map to improve the generation performance of delicate features. Third, we also propose an attention-based location loss to improve the generated performance on small fine-grained features. We apply this method to an open facial image database RaFD and demonstrate the efficiency of Atten-Pip-GAN on generating fine-grained attribute facial images.</p>", "Keywords": "Fine-grained image-to-image generation; Facial images; GANs; Attention; Pipeline", "DOI": "10.1007/s11042-019-08346-x", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Ocean University of China, Qingdao, China"}], "References": [{"Title": "Semantic consistent adversarial cross-modal retrieval exploiting semantic similarity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "14733", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 79377674, "Title": "ESEAP: ECC based secure and efficient mutual authentication protocol using smart card", "Abstract": "Smart card based user server mutual authentication framework is famous for safe communication via unfavorable and insecure communication system. The authenticated user and server communicate to each other and share information via Internet. Recently, <PERSON> et al. suggested a lightweight password-assisted two factor authentication framework using smart card. We reviewed their scheme and observed that it does maintain security and privacy off-line password guessing attack and also impersonation attack. We proposed enhance elliptic curve cryptography(ECC) based authentication framework for the same environment. The proposed scheme ESEAP is secure resilience of many attractive security attributes and features like off-line password guessing attack, no password verifier-table, smart card loss attack, anonymity, mutual authentication, replay attack, impersonation attack, server spooling attack, no clock-synchronization attack, forward secrecy, insider attack, message authentication, provision of key agreement, parallel attack, sound repairability, no password exposure, timely typo detection, resistance to know attacks, password friendly, user unlinkability and server unlinkability. Further, the paper shows formal security analysis of the ESEAP which based on random oracle model. We compared the presented protocol with other related protocols in the same environment, and show that ESEAP is more efficient in terms of computation and communication cost. As a result, the presented protocol can be utilized over public communication channel.", "Keywords": "Mutual authentication ; Elliptic curve cryptography ; Smart card ; Random oracle model ; Security and privacy", "DOI": "10.1016/j.jisa.2019.102443", "PubYear": 2020, "Volume": "51", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Jamia Millia Islamia, New Delhi 110025, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jindal Global Business School, O. P. <PERSON>dal Global University, Haryana 131001, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Jamia Millia Islamia, New Delhi 110025, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, PGDAV College, University of Delhi, New Delhi 110065, India"}, {"AuthorId": 5, "Name": "Mans<PERSON>", "Affiliation": "Department of Computer Science, Jamia Millia Islamia, New Delhi 110025, India"}], "References": []}, {"ArticleId": 79377752, "Title": "A Guide to Using the R Package “multiColl” for Detecting Multicollinearity", "Abstract": "<p>The detection of problematic collinearity in a linear regression model is treated in all the existing statistical software packages. However, such detection is not always done adequately. The main shortcomings relate to treatment of independent qualitative variables and completely ignoring the role of the intercept in the model (consequently, ignoring the nonessential collinearity). This paper presents the R package multiColl , which implements the usually applied measures for detecting near collinearity while overcoming the weaknesses observed in other existing packages.</p>", "Keywords": "Multicollinearity; Detection; Intercept; Dummy; Software", "DOI": "10.1007/s10614-019-09967-y", "PubYear": 2021, "Volume": "57", "Issue": "2", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Department of Quantitative Methods for the Economy and Business, University of Granada, Granada, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Quantitative Methods for the Economy and Business, University of Granada, Granada, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Economics and Business, University of Almería, Almería, Spain"}], "References": []}, {"ArticleId": 79377932, "Title": "Detection of acetylcholinesterase and its inhibitors by liquid crystal biosensor based on whispering gallery mode", "Abstract": "In this work, a liquid crystal (LC) biosensor based on whispering gallery mode (WGM) lasing is reported and demonstrated for real-time and high-sensitive detection of acetylcholinesterase (AChE) and its inhibitors. Benefit from the double amplification by the WGM resonance and the 5CB molecules, small changes in biological processes are significantly enhanced in WGM spectral responses. The spectral responses provide direct information about the molecular adsorption/desorption at the LC/aqueous solution interface and can be used as an indicator of the enzymatic reaction. The limit of detection achieved was as low as 0.1 pg/mL for fenobucarb and 1 pg/mL for dimethoate, which is considerably lower than the standard levels of pesticides specified for water quality standards. Results indicate that this versatile method holds prospects for application in real-time and high-sensitive monitoring of biochemical reactions and can serve as an alternative solution to compensate for missing capabilities in conventional POM observations.", "Keywords": "Biosensor ; Whispering gallery mode ; Nematic liquid crystal microdroplet ; Acetylcholinesterase ; Inhibitors ; Enzymatic reactions", "DOI": "10.1016/j.snb.2020.127672", "PubYear": 2020, "Volume": "308", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physics and Optoelectronic Engineering, Harbin Engineering University, Harbin, 150001, China;Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, China"}, {"AuthorId": 3, "Name": "Yanzeng Li", "Affiliation": "Department of Physics and Optical Science, University of North Carolina at Charlotte, Charlotte, NC, 28223-0001, USA"}, {"AuthorId": 4, "Name": "Hanyang Li", "Affiliation": "College of Physics and Optoelectronic Engineering, Harbin Engineering University, Harbin, 150001, China;Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, China;Corresponding author at: Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, 145 Nantong Street, Harbin, 150080, China"}], "References": []}, {"ArticleId": 79377937, "Title": "Pr doped BiFeO3 hollow nanofibers via electrospinning method as a formaldehyde sensor", "Abstract": "In this work, we studied Pr doped BiFeO<sub>3</sub> hollow nanofibers as a potential functional material, assembled from nanoparticles through electrospinning technique and calcination route, which were decorated by Pr ions and ameliorated by hollow nanofiber structure. The Pr doped BiFeO<sub>3</sub> hollow nanofibers, improved by the uniform and tubular structure with a larger specific surface area (63.53 m<sup>2</sup>/g), higher pore size (38.17 nm) and smaller grain size, revealed better response to formaldehyde (50 ppm, 190 °C, R<sub>gas</sub>/R<sub>air</sub> = 17.6) along with long-term stability. Moreover, the influence of the relative humidity in atmosphere on the gas performance of the as-prepared sensors was also investigated.", "Keywords": "Pr doped BiFeO<sub>3</sub> ; Electrospinning ; Perovskite materials ; Formaldehyde sensor", "DOI": "10.1016/j.snb.2020.127689", "PubYear": 2020, "Volume": "308", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}, {"AuthorId": 2, "Name": "S.Y. Ma", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China;Corresponding author"}, {"AuthorId": 3, "Name": "S.T. Pei", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Atomic and Molecular Physics & Functional Materials of Gansu Province, College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, China"}], "References": []}, {"ArticleId": 79378007, "Title": "Call graph obfuscation and diversification: an approach", "Abstract": "Monetary loss due to software piracy nowadays reaches millions. In 2017, the commercial value for this concept rose to $46.3 billion. A way to mitigate this problem from the technological point of view is the use of software protection techniques, especially the obfuscation and diversification of code, highlighting the control obfuscation. There are many proposals connected with obfuscating control flow graph. However, there are few reported works that perform obfuscation of the call graph. In this study, the authors propose a novel mechanism for the static obfuscation and diversification of the call graph of a software. The mechanism is based on the routing of functions calls in order to modify the software call graph. A prototype of the proposed mechanism was developed by extending the functionalities of a compiler. The generated software differed structurally by 25% on average, compared to the original software. There was an increase in the level of obfuscation from 2 to 30% in the tests performed, with only a 3% overhead of the execution time in all cases. The proposal allows to restructure the whole call graph efficiently, increasing the level of protection without affecting significantly the software performance.", "Keywords": "monetary loss; static obfuscation; obfuscating control flow graph; software performance; software piracy; software protection techniques; control obfuscation; call graph obfuscation", "DOI": "10.1049/iet-ifs.2019.0216", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 17160, "JournalTitle": "IET Information Security", "ISSN": "1751-8709", "EISSN": "1751-8717", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Informática y Electrónica , Universidad Técnica de Manabí (UTM) , PortoViejo , Ecuador"}, {"AuthorId": 2, "Name": "Yulier Nuñez‐Musa", "Affiliation": "Departamento de Informática , Universidad Agraria de La Habana (UNAH) ‘Fructuoso <PERSON>’ , San José de Las Lajas , Cuba"}, {"AuthorId": 3, "Name": "<PERSON>‐<PERSON>", "Affiliation": "Departamento de Inteligencia Artificial e Infraestructura de Sistemas Informáticos , Universidad Tecnológica de la Habana ‘<PERSON> , La Habana , Cuba"}], "References": []}, {"ArticleId": 79378105, "Title": "A stochastic approximation approach to spatio-temporal anchorage planning with multiple objectives", "Abstract": "Globalization and subsequent increase in seaborne trade have necessitated efficient planning and management of world’s anchorage areas. These areas serve as a temporary stay area for commercial vessels for various reasons such as waiting for passage or port, fuel services, and bad weather conditions. The research question we consider in this study is how to place these vessels inside a polygon-shaped anchorage area in a dynamic fashion as they arrive and depart, which seems to be the first of its kind in the literature. We specifically take into account the objectives of (1) anchorage area utilization, (2) risk of vessel collisions, and (3) fuel consumption performance. These three objectives define our objective function in a weighted sum scheme. We present a spatio-temporal methodology for this multi-objective anchorage planning problem where we use Monte Carlo simulations to measure the effect of any particular combination of planning metrics (measured in real time for an incoming vessel) on the objective function (measured in steady state). We resort to the Simultaneous Perturbation Stochastic Approximation (SPSA) algorithm for identifying the linear combination of the planning metrics that optimizes the objective function. We present computational experiments on a major Istanbul Straight anchorage, which is one of the busiest in the world, as well as synthetic anchorages. Our results indicate that our methodology significantly outperforms comparable algorithms in the literature for daily anchorage planning. For the Istanbul Straight anchorage, for instance, reduction in risk was 42% whereas reduction in fuel costs was 45% when compared the best of the current state-of-the-art methods. Our methodology can be utilized within a planning expert system that intelligently places incoming vessels inside the anchorage so as to optimize multiple strategic goals. Given the flexibility of our approach in terms of the planning objectives, it can easily be adapted to more general variants of multi-objective spatio-temporal planning problems where certain objects need to be dynamically placed inside two or even-three dimensional spaces in an intelligent manner.", "Keywords": "Anchorage planning ; Spatio-temporal planning ; Planning expert system ; Stochastic approximation ; Multi-objective optimization", "DOI": "10.1016/j.eswa.2019.113170", "PubYear": 2020, "Volume": "146", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Civil Engineering and Geosciences, TU Delft, Mekelweg 5, Delft 2628 CD, the Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Royal Melbourne Institute of Technology, 124 La Trobe St, Melbourne, VIC 3000, Australia;Corresponding author"}], "References": []}, {"ArticleId": 79378143, "Title": "A hybrid algorithm based on particle filter and genetic algorithm for target tracking", "Abstract": "The particle filter (PF) is an influential instrument for visual tracking; it relies on the Monte Carlo Chain Framework and Bayesian probability that is of tremendous importance for smart monitoring systems. The current study introduces a particle filter based upon genetic resampling. In the suggested method called Reduced Particle Filter based upon Genetic Algorithm (RPFGA), particles with the highest weights are chosen and go through evolution using a GA in the resampling phase of PF algorithm. Moreover, this study aims to introduce the ideas of marking (marking the target by user (observer) in the first frame of a video sequence) and decreasing image size. Applying both ideas leads to reduced number of particles, the processing time of each frame, and the total tracking time. Additionally, the performance of the offered RPFGA method to tackle the occlusion problem is enhanced by the marking idea. According to the results obtained in challenges, such as Occlusions (OCC), deformation (DEF), low resolution (LR), scale variations(SV), Fast Motions (FM), In-Plane Rotation (IPR), Out-Of-Plane Rotation (OPR), Motion Blur (MB), Illumination Variation (IV) and color similarity between the target and the background, and regarding precision and tracking time, the recommended hybrid approach only with a few particles overtakes the generic particle filter, Particle Swarm Optimization particle filter (PSO-PF) and the particle filter based upon improved cuckoo search (ICS-PF). The suggested method can be applied for real time video objects tracking.", "Keywords": "Video objects tracking ; Particle filter ; Genetic algorithm ; Resampling ; Sample impoverishment", "DOI": "10.1016/j.eswa.2020.113188", "PubYear": 2020, "Volume": "147", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer and Information Technology Engineering, Qazvin Branch, Islamic Azad University, Qazvin, Iran;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Imam <PERSON> International University, Qazvin, Iran"}], "References": []}, {"ArticleId": 79378178, "Title": "Rock magnetic and environmental magnetic data of lacustrine sediments from the Heqing basin", "Abstract": "<p>This data article associated with the manuscript &quot;Magnetic mineral dissolution recorded in a lacustrine sequence from the Heqing Basin, SW China, and its relationship with changes in the Indian monsoon&quot;. Through detailed rock magnetic measurements, magnetic properties of the lacustrine sediments (magnetic mineralogy, their concentration and domain state) were clarified. Then, analyzing the relationship between magnetic property and paleoenvironmental proxies can reveal the paleoenvironmental implications of magnetic parameters of lacustrine sediments from the Heqing basin. Comparing paleoenvironmental proxies of lacustrine sediments from the Heqing basin with proxies associated with Indian monsoon recorded in Arabian Sea, and Bay of Bengal, can deepen our understanding about the characteristics of Indian Monsoon in the geological time.</p><p>© 2020 The Authors.</p>", "Keywords": "Indian monsoon;Magnetic mineral dissolution;Rock magnetism;The Heqing basin", "DOI": "10.1016/j.dib.2020.105107", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Key Laboratory of Earth Surface System and Environmntal Carrying Capacity, Northwest University, Xi'an 710127, China. ;State Key Laboratory of Loess and Quaternary Geology, Institute of Earth Environment, Chinese Academy of Sciences, Xi'an 710061, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Loess and Quaternary Geology, Institute of Earth Environment, Chinese Academy of Sciences, Xi'an 710061, China."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Loess and Quaternary Geology, Institute of Earth Environment, Chinese Academy of Sciences, Xi'an 710061, China."}, {"AuthorId": 4, "Name": "Chaofeng Fu", "Affiliation": "Key Laboratory of Western Mineral Resources and Geological Engineering, Ministry of Education of China &amp; Chang'an University, Xi'an 710054, China."}], "References": []}, {"ArticleId": 79378203, "Title": "A dataset on body composition, strength and performance in older adults", "Abstract": "<p>This article presents a dataset of body composition, strength and performance measurements in older adults; the data were collected as part of Rancho Bernardo Study (RBS), a longitudinal observational cohort study. All community dwelling adults in Rancho Bernardo, California between 1972 and 1974 were eligible for participation in the study. A subset of the participants returned every four years for subsequent visits. The dataset in this publication consists of some of the measures taken in Visits 7-10, for 1466 subjects who had at least one of these measures taken. We analysed the data with a feed-forward loop model fitted by structural equation modelling. The data can be valuable for modelling and extracting further information on how body composition, strength and performance affect each other over a long period of time. The data are analysed and interpreted in the research article <PERSON><PERSON><PERSON> et al., 2019.</p><p>© 2020 The Author(s).</p>", "Keywords": "Body composition;Frailty;Grip strength;Physical activity;Sarcopenia", "DOI": "10.1016/j.dib.2019.105103", "PubYear": 2020, "Volume": "29", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Biostatistics and Epidemiology, Department of Healthcare Policy and Research, Weill Cornell Medicine, Cornell University."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Biostatistics and Epidemiology, Department of Healthcare Policy and Research, Weill Cornell Medicine, Cornell University."}], "References": []}, {"ArticleId": 79378347, "Title": "Optimization of vacuum casting process parameters to enhance tensile strength of components using design of experiments approach", "Abstract": "<p>Vacuum casting is one of the widely used methods for small-volume production of plastic parts. The main challenge of this method is to choose the optimal working parameters to manufacture plastic parts with better mechanical properties. The conventional vacuum casting (CVC) technology uses gravity to make the casing material to fill the mold cavity, resulting in the yield of the molded components with high tensile strength is relatively low. Vacuum differential pressure casting (VDPC) can overcome this disadvantage since the filling mechanism of the casting material is different from CVC process. In this study, integration of design of experiments approach and the VDPC technique was employed to enhance tensile strength of molded components. It was found that the most important control factor affecting the tensile strength of the fabricated component is the mold cavity temperature, followed by the material mixing time, the differential pressure time, and the mixing chamber inlet valve angle. The optimal process parameters for producing components with better tensile strength are the mold cavity temperature of 35 °C, the material mixing time of 40 s, the differential pressure time of 8 s, and the mixing chamber inlet valve angle of 60 °.</p>", "Keywords": "Vacuum casting; Vacuum differential pressure casting; Tensile strength; Design of experiments", "DOI": "10.1007/s00170-019-04905-6", "PubYear": 2020, "Volume": "106", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>l-<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Ming Chi University of Technology, New Taipei City, Taiwan;Research Center for Intelligent Medical Devices, Ming Chi University of Technology, New Taipei City, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Ming Chi University of Technology, New Taipei City, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Ming Chi University of Technology, New Taipei City, Taiwan"}], "References": []}, {"ArticleId": 79378365, "Title": "Intuitionistic fuzzy optimistic and pessimistic multi-period portfolio optimization models", "Abstract": "<p>There are myriad works that deal with the fuzzy multi-period portfolio selection problem, but when we talk about multi-period portfolio selection in an intuitionistic fuzzy realm, to the best of our knowledge, there is no research work that deals with the same. So, to fill this research gap, we propose an intuitionistic fuzzy multi-period portfolio selection model with the objectives of maximization of the terminal wealth and minimization of the cumulative risk subject to several realistic constraints such as complete capital utilization, no short selling, fixed transaction costs for buying and selling, bounds on the desired returns of each period, cardinality constraint, and bounds on the minimal and the maximal proportion of the capital allocated to an asset. The membership and non-membership of the objectives are modeled using their extreme values. The proposed approach provides avenues for the inclusion and minimization of the hesitation degree into decision making, thereby resulting in a significantly better portfolio. Parameters (\theta _W) and (\theta _{Va}) are used to introduce the hesitation in the model, and, based on their values, the model is further categorized into optimistic and pessimistic intuitionistic fuzzy multi-period portfolio selection models for optimistic and pessimistic investors, respectively. The max–min approach is used to solve the proposed models. Furthermore, a numerical illustration is presented to exhibit the virtues of the proposed model.</p>", "Keywords": "Multi-period portfolio optimization; Intuitionistic fuzzy; Possibility measure; Multi-objective optimization; Max–min approach", "DOI": "10.1007/s00500-019-04639-3", "PubYear": 2020, "Volume": "24", "Issue": "16", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Operational Research, University of Delhi, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Operational Research, University of Delhi, Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Operational Research, University of Delhi, Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Operational Research, University of Delhi, Delhi, India"}], "References": []}, {"ArticleId": 79378381, "Title": "Scalable distributed evolutionary algorithm orchestration using Docker containers", "Abstract": "In smart factories, integrated optimisation of manufacturing process planning and scheduling leads to better results than a traditional sequential approach but is computationally more expensive and thus difficult to be applied to real-world manufacturing scenarios. In this paper, a working approach for cloud-based distributed optimisation for process planning and scheduling is presented. Three managers dynamically governing the creation and deletion of subpopulations (islands) evolved by a multi-objective genetic algorithm are proposed, compared and contrasted. A number of test cases based on two real-world manufacturing scenarios are used to show the applicability of the proposed solution.", "Keywords": "Smart factory ; Industry 4.0 ; Evolutionary algorithms ; Distributed optimisation ; Multi-objective optimisation ; Integrated process planning and scheduling", "DOI": "10.1016/j.jocs.2019.101069", "PubYear": 2020, "Volume": "40", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of York, Deramore Ln, Heslington YO10 5GH, York, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of York, Deramore Ln, Heslington YO10 5GH, York, UK;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of York, Deramore Ln, Heslington YO10 5GH, York, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational Intelligence, Wroclaw University of Technology, Wybrzeze Wyspianskiego 27, 50-370 Wroclaw, Poland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of York, Deramore Ln, Heslington YO10 5GH, York, UK"}], "References": []}, {"ArticleId": 79378468, "Title": "S-box design based on optimize LFT parameter selection: a practical approach in recommendation system domain", "Abstract": "<p>The strength of cryptography encryption is strongly depended upon the substitution box (S-box), hence highly non-linear S-box is appreciated in cryptography. In this work, we proposed a scheme to select the optimized values for control parameters (a, b, c, d) of linear fractional transform (LFT) to achieve an S-box with non-linearity of 112. For dynamic parameter values, we used location of chaotic sequences. The proposed s-box tested on different criteria such as non-linearity method (NL), probability methods (linear approximation (LP), differential approximation (DP), strictly avalanche criteria (SAC) and bit independence criteria (BIC). Our result shows that proposed s-box achieves better or equal to cryptographic strength as compared with state-of-the-art techniques. Moreover, we tested the proposed s-box based encryption method in recommendation system scenario with the idea of text-to-image conversion. The statistical analysis of this image encryption shows the bright prospects of the proposed method.</p>", "Keywords": "S-box; Cryptography; Galo<PERSON> field; Projective linear group (PGL); Chaotic theory; Text-2-image conversion; Recommendation system; NIST standard", "DOI": "10.1007/s11042-019-08464-6", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FAST-National University of Computer and Emerging Sciences, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Islamabad, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal Urdu University of Arts, Science & Technology, Islamabad, Pakistan"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Islamabad, Pakistan"}], "References": []}, {"ArticleId": 79378548, "Title": "Fault estimation and compensation for hypersonic flight vehicle via type-2 fuzzy technique and cuckoo search algorithm", "Abstract": "<p>This study proposes a fault estimation and compensation scheme for hypersonic flight vehicle (HFV) attitude system with body flap fault. To achieve the fault compensation capability, an interval type-2 fuzzy estimator is designed to approximate the unknown nonlinear function caused by modeling uncertainties and the fault. The fault-tolerant controller via the terminal sliding mode and dynamic surface techniques is developed to ensure the tracking accuracy of attitude angles for HFV and the desired control torque can be achieved. Then, a novel control allocation scheme with a cuckoo search algorithm and linear programming method is proposed to distribute the desired torque to aerodynamic surfaces and reaction control system jets. The stability of the proposed scheme is analyzed using the L<PERSON><PERSON>nov stability theory. The validity of the method is verified by a series of comparisons on numerical simulation results.</p>", "Keywords": "Fault-tolerant control ; hypersonic flight vehicle ; type-2 fuzzy estimator ; control allocation ; cuckoo search algorithm", "DOI": "10.1177/1729881419891605", "PubYear": 2020, "Volume": "17", "Issue": "1", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Chen", "Affiliation": "College of Automation Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}], "References": []}, {"ArticleId": 79379084, "Title": "Correction to: Novel capacitance evaluation model for microelectromechanical switch considering fringe and effect of holes in pull-up and pull-down conditions", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00542-019-04739-y", "PubYear": 2020, "Volume": "26", "Issue": "4", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Rajiv Gandhi University (A Central University), Itanagar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, NERIST, Itanagar, India"}], "References": []}, {"ArticleId": 79379088, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S2210-6502(19)31058-2", "PubYear": 2020, "Volume": "52", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [], "References": []}, {"ArticleId": 79379099, "Title": "Graph matching based on local and global information of the graph nodes", "Abstract": "<p>Graph matching is an essential NP-problem in computer vision and pattern recognition. In this paper, we propose an approximate graph matching method. This method formulates the problem of computing the correspondences between two graphs as a problem of selecting nodes on an association graph. The nodes of the association graph represent candidate correspondences between the two original graphs. Our method first constructs an affinity matrix based on both the global and local information of the original graphs’ nodes. Each element of this matrix is used to measure the mutual consistency of a pair of nodes within the association graph. Our method then applies the reweighted random walks technique that preserves the one-to-one matching constraint to simulate random walks on the association graph and to iteratively compute a quasi-stationary distribution. To discretize this distribution, our method finally applies the Hungarian algorithm and obtains an approximate matching between the original two graphs. Experimental results demonstrate the effectiveness of our method for graph matching and the ability of our method for being robust to outlier and deformation noise.</p>", "Keywords": "Graph matching; Affinity matrix; Global information; Local information; Random walks", "DOI": "10.1007/s11042-019-08516-x", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;Shandong Provincial Key Laboratory of Network based Intelligent Computing, University of Jinan, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong Provincial Key Laboratory of Network based Intelligent Computing, University of Jinan, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong Provincial Key Laboratory of Network based Intelligent Computing, University of Jinan, Jinan, China"}, {"AuthorId": 4, "Name": "Jun<PERSON> Liu", "Affiliation": "Shandong Provincial Key Laboratory of Network based Intelligent Computing, University of Jinan, Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong Provincial Key Laboratory of Network based Intelligent Computing, University of Jinan, Jinan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong Provincial Key Laboratory of Network based Intelligent Computing, University of Jinan, Jinan, China"}], "References": []}, {"ArticleId": 79379121, "Title": "Feature selection based on weighted conditional mutual information", "Abstract": "Feature selection is an essential step in data mining. The core of it is to analyze and quantize the relevancy and redundancy between the features and the classes. In CFR feature selection method, they rarely consider which feature to choose if two or more features have the same value using evaluation criterion. In order to address this problem, the standard deviation is employed to adjust the importance between relevancy and redundancy. Based on this idea, a novel feature selection method named as Feature Selection Based on Weighted Conditional Mutual Information (WCFR) is introduced. Experimental results on ten datasets show that our proposed method has higher classification accuracy.", "Keywords": "Feature selection ; Conditional mutual information ; Standard deviation", "DOI": "10.1016/j.aci.2019.12.003", "PubYear": 2024, "Volume": "20", "Issue": "1/2", "JournalId": 5816, "JournalTitle": "Applied Computing and Informatics", "ISSN": "2210-8327", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi'an University of Technology, Xi’an 710048, China;Shaanxi Key Laboratory of Network Computing and Security Technology, Xi’an 710048, China;Corresponding author at: School of Computer Science and Engineering, Xi'an University of Technology, Xi’an 710048, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi'an University of Technology, Xi’an 710048, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Xi'an University of Technology, Xi’an 710048, China"}], "References": []}, {"ArticleId": 79379124, "Title": "Improved structured filter design and analysis for perturbed phase-locked loops via sector and H ∞ norm constraints with convex computations", "Abstract": "This work focuses on designing a structured filter in a phase-locked loop (PLL) system that is perturbed by nonlinear effects from the phase comparator. The filter is designed based on H ∞ control synthesis which is also optimized along with the stability condition of the nonlinear PLL model. The improvement on the design technique is introduced via relaxations of the H ∞ norm constraint on the system’s frequency response and integration with the nonlinearity’s sector bound. The resulting problems are then formulated into matrix inequalities where the solvability conditions are provided via convex computation approaches. Numerical results and computer simulations demonstrate that the tracking capability of the PLL can be enforced via the proposed methods which then lead to a wider lock-in range and a faster acquisition time as compared to existing techniques.", "Keywords": "Nonlinear PLL ; Sector constraint ; Structured filter ; Convex ; H ∞ Norm ; Matrix inequalities,", "DOI": "10.1016/j.compeleceng.2019.106542", "PubYear": 2020, "Volume": "81", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Engineering Campus, Universiti Sains Malaysia, Nibong Tebal 14300, Penang, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Engineering Campus, Universiti Sains Malaysia, Nibong Tebal 14300, Penang, Malaysia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Engineering Campus, Universiti Sains Malaysia, Nibong Tebal 14300, Penang, Malaysia"}], "References": []}, {"ArticleId": 79379132, "Title": "RETRACTED:An assessment of software defined networking approach in surveillance using sparse optimization algorithm", "Abstract": "Unmanned aerial vehicles (UAV) can be used as basic elements of the sensor network or an upgrade of existing network that are built with static wireless sensor nodes. Wireless Networks (WN) are utilized across in surveillance in all domains like natural disasters, agriculture, water, forest, military, buildings, health monitoring, disaster relief &amp; emergency management, area and industrial surveillance, due to its wider applicability. Software Defined Networking (SDN) provide a hopeful resolution in bendy supervision WSNs by allowing the separation of the control logic from the sensor nodes/actuators. The advantage with this SDN-based supervision in structure of WSNs is that it enables centralized control of the entire WSN making it simpler to deploy network-wide management protocols and applications on demand. Synthetic Aperture Radar (SAR) images are difficult to analyze due to the presence of speckle noise. Speckle noise must be filtered out before applying to other image processing applications. Three Layered Feed Forward Back Propagation Neural Network (TLFFBPNN) has been proposed to suppress the speckle noise. GLCM properties have been extracted and Back propagation training algorithm is used to train the neural network.", "Keywords": "SDN ; SAR ; Speckle noise ; Contour wavelet transform ; Wiener filter ; Neural network ; SSI ; SSIM ; EPI ; ENL", "DOI": "10.1016/j.comcom.2019.12.061", "PubYear": 2020, "Volume": "151", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sridevi Women’s Engineering College, Hyderabad, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sri Venkateshwara College of Engineering, Sriperumbudur, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Panimalar Engineering College, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Alpha College of Engineering, Chennai, India"}], "References": []}, {"ArticleId": 79379778, "Title": "Overview of Research in the field of Video Compression using Deep Neural Networks", "Abstract": "<p>Deep Neural Networks (DNN) have emerged in recent year as a best-of-breed alternative for performing various classification, prediction and identification tasks in images and other fields of study. In the last few years, various research groups are exploring the option to harness them to improve video coding with the primary purpose of reducing video compression rates while retaining same video quality. Evolving neural-networks based video coding research efforts are focused on two different directions: (1) improving existing video codecs by performing better predictions that are incorporated within the same codec framework, and (2) holistic methods of end-to-end image/video compression schemes. While some of the results are promising and the prospects are good, no breakthrough has been reported as of yet. This paper provides an overview of state-of-the-art research work, providing examples of few prominent publications that illustrate and further explain the different highlighted topics in the field of using DNNs for video compression. Our conclusion is that the benefits have not been fully explored yet and additional work is expected to accomplish the next generation, neural networks based codecs.</p>", "Keywords": "Video Compression; Neural Networks; Deep Learning; Overview", "DOI": "10.1007/s11042-019-08572-3", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Communication Systems Engineering, School of Electrical and Computer Engineering, Ben-Gurion University, Be’er Sheva, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Communication Systems Engineering, School of Electrical and Computer Engineering, Ben-Gurion University, Be’er Sheva, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Communication Systems Engineering, School of Electrical and Computer Engineering, Ben-Gurion University, Be’er Sheva, Israel"}], "References": []}, {"ArticleId": 79379915, "Title": "An error-bounded B-spline curve approximation scheme using dominant points for CNC interpolation of micro-line toolpath", "Abstract": "This paper presents a unified framework for computing a B-spline curve to approximate the micro-line toolpath within the desired fitting accuracy. First, a bi-chord error test extended from our previous work is proposed to select the dominant points that govern the overall shape of the micro-line toolpath. It fully considers the geometric characteristics of the micro-line toolpath, i.e., the curvature, the curvature variation and the torsion, appropriately determining the distribution of the dominant points. Second, an initial B-spline curve is constructed by the dominant points in the least square sense. The fitting error is unpredictable and uncontrollable. It is classified into two types: (a) the geometric deviations between the vertices of the polygon formed by the data points and the constructed B-spline curve; (b) those between the edges of the polygon and the constructed B-spline curve. Herein, an applicable dominant point insertion is employed to keep the first geometric deviation within the specified tolerance of fitting error. A geometric deviation model extended from our previous work is developed to estimate the second geometric deviation. It can be effectively integrated into global toolpath optimization. Computational results demonstrate that the bi-chord error test applies to both the planar micro-line toolpath and the spatial micro-line toolpath, and it can greatly reduce the number of the control points. Simulation and experimental results demonstrate that the proposed B-spline approximation approach can significantly improve machining efficiency while ensuring the surface quality.", "Keywords": "G01 blocks ; B-spline curve approximation ; Dominant points ; Bi-chord error test ; Geometric deviation estimation", "DOI": "10.1016/j.rcim.2019.101930", "PubYear": 2020, "Volume": "64", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mechatronic Institute, Zhejiang Sci-Tech University, Hangzhou, Zhejiang Province 310018, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "AAC Technologies Holding Inc., Wuhan, Hubei Province 430070, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai 200240, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai 200240, PR China"}], "References": []}, {"ArticleId": 79380021, "Title": "Boundary sampled-data feedback stabilization for parabolic equations", "Abstract": "The aim of this work is to design an explicit finite dimensional boundary feedback controller of sampled-data form for locally exponentially stabilizing the equilibrium solutions to semilinear parabolic equations. The feedback controller is expressed in terms of the eigenfunctions corresponding to unstable eigenvalues of the linearized equation. This stabilizing procedure is applicable for any sampling rate, and when the sampling period tends to zero, the feedback converges to certain feedback designed for stabilizing the parabolic equations with continuous-time boundary feedback control.", "Keywords": "Parabolic equations ; Sampled-data control ; Boundary feedback stabilization", "DOI": "10.1016/j.sysconle.2019.104618", "PubYear": 2020, "Volume": "136", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Physics, China University of Geosciences, Wuhan 430074, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Physics, China University of Geosciences, Wuhan 430074, PR China"}], "References": []}, {"ArticleId": 79380062, "Title": "Highly selective ppb-level H2S sensor for spendable detection of exhaled biomarker and pork freshness at low temperature: Mesoporous SnO2 hierarchical architectures derived from waste scallion root", "Abstract": "Herein, we firstly reported a waste scallion root biotemplate strategy to massively fabricate mesoporous SnO<sub>2</sub> hierarchical architectures, which can realize precise detection of ppb-level H<sub>2</sub>S gas in highly humid atmosphere (RH = 85%). The hierarchical skeleton is made up of the crosslinked nanoparticles of about 13 nm with a layer of nanospheres uniformly loaded on the surface of small-sized particles. Such specially structural characteristic can ensure that SnO<sub>2</sub> sensing materials possess large specific surface area and good mesoporous connectivity, enhancing the response and selectivity towards trace H<sub>2</sub>S in a wide range of 0.5–1000 ppb at low working temperature of 92 °C. Especially, detection limit of 0.5 ppb is the lowest among all reported SnO<sub>2</sub>-based H<sub>2</sub>S sensors. Meanwhile, we also monitored the change of trace H<sub>2</sub>S gas concentration in exhaled breath of healthy human, the decay process of fresh pork in 72 h and the simulated environment of halitosis under high humidity, and the sensor exhibited satisfactory results. Therefore, the mesoporous SnO<sub>2</sub> hierarchical structure could be used as a spendable sensing material of detecting ppb-level H<sub>2</sub>S for the applications in meat freshness monitoring and halitosis diagnosis.", "Keywords": "SnO<sub>2</sub> ; Scallion root ; Gas sensor ; Mesoporous structure ; Exhaled biomarker", "DOI": "10.1016/j.snb.2020.127662", "PubYear": 2020, "Volume": "307", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, People's Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, People's Republic of China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, People's Republic of China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Functional Inorganic Material Chemistry, Ministry of Education, School of Chemistry and Materials Science, Heilongjiang University, Harbin, 150080, People's Republic of China;Corresponding authors"}], "References": [{"Title": "Design of highly porous SnO2-CuO nanotubes for enhancing H2S gas sensor performance", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "302", "Issue": "", "Page": "127179", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 79380120, "Title": "Adaptive protection scheme for microgrids based on SOM clustering technique", "Abstract": "Microgrids are penetrating into the power systems at an unprecedented rate. The reason is the mutual economic and environmental benefits of microgrids, both for power grid utility and the consumers. Some special features of microgrids such as, the two main operational conditions called, islanded and grid-connected modes, and being composed of various types of distributed energy resources along with different uncertainties cause some tough challenges to protection and control systems. From the protection aspect, the coordination of overcurrent relays protection will become a difficulty, due to the extensive changes in the fault current levels sensed by these devices. In this paper, a new adaptive protection coordination scheme based on Self-Organizing Map (SOM) clustering algorithm is proposed for digital overcurrent relays equipped with several setting groups. Considering the similarity of mis-coordinated relay pairs for the clustering purpose, the proposed protection scheme focuses on solving the mis-coordination between main/backup relay pairs. As a case study, a modified IEEE 33-bus test system is used as a microgrid. In the case study, a synchronous distributed generation and two electric vehicle charging stations are installed. The results suggest that not only the proposed method is fully capable and flexible to significantly improve the mis-coordination of overcurrent relay pairs, but it can also ameliorate the operating time of relay.", "Keywords": "Adaptive protection scheme ; Microgrid ; Overcurrent relays ; Clustering ; Self-Organizing Map (SOM) ; Distributed energy resources", "DOI": "10.1016/j.asoc.2020.106062", "PubYear": 2020, "Volume": "88", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Electrical Engineering Department, University of Zanjan, Zanjan 45371-38791, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, University of Zanjan, Zanjan 45371-38791, Iran;Corresponding author"}], "References": []}, {"ArticleId": 79380211, "Title": "Why and How to Approach User Experience in Safety-Critical Domains: The Example of Health Care", "Abstract": "Objective <p>To highlight the importance of the personal experience of users who interact with technology in safety-critical domains and summarize three interaction concepts and the associated theories that provide the means for addressing user experience.</p> Background <p>In health care, the dominant concepts of interaction are based on theories arising from classic cognitive psychology. These concepts focus mainly on safety and efficiency, with too little consideration being given to user experience.</p> Method <p>Users in complex socio-technical and safety-critical domains such as health care interact with many technological devices. Enhancing the user experience could improve the design of technology, enhance the well-being of staff, and contribute to modern safety management. We summarize concepts of “interaction” based on modern theories of human–computer interaction, which include the personal experience of users as an important construct.</p> Results and Conclusion <p>Activity theory, embodiment, and interaction as experience provide a theoretical foundation for considering user experience in safety-critical domains. Using an example from anesthesiology, we demonstrate how each theory provides a unique but complementary view on experience. Finally, the methodological possibilities for considering personal experience in design and evaluations vary among the theories.</p> Application <p>Considering user experience in health care and potentially other safety-critical domains can provide an additional means of optimizing interaction with technology, contributing to the well-being of staff, and improving safety.</p>", "Keywords": "activity theory;concepts of interaction;embodiment;eudaimonia;interaction as experience;safety-critical domains;user experience", "DOI": "10.1177/0018720819887575", "PubYear": 2021, "Volume": "63", "Issue": "5", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Julius-Maximilians-Universität Würzburg, Würzburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University Hospital Würzburg, Würzburg, Germany"}], "References": []}, {"ArticleId": 79380718, "Title": "Feature extraction of point clouds based on region clustering segmentation", "Abstract": "<p>This paper proposes a feature extraction method for scattered point clouds. First, a clustering algorithm is used to divide point clouds into different regions that represent the original features. In each sub-region, we calculate the angles between the directed line segments from sampling points to the neighborhood points and set the angle threshold to identify edge feature points of uniform distribution. For the edge points of non-uniform distribution, we introduce the local neighborhood size as a discrete scale parameter for edge point detection, and then accurately identify and record the detected edge points. Then, according to the mean curvature of point clouds, the local feature weights of sampling points in the sub-region are calculated so that potential sharp feature points in a local area are detected. Finally, a minimum spanning tree of feature points is established to construct connected regions and generate feature point sets. A Bidirectional Principal Component Analysis (BD-PCA) search method is used to trim and break the small branches and multiline segments to generate feature curves. We carried out experiments on point cloud models with different densities to verify the effectiveness and superiority of our method. Results show that the edge features and sharp features are effectively extracted, and our method is not affected by the noise, neighborhood scale, or quality of sampling.</p>", "Keywords": "Image processing; Point clouds; Feature extraction; Region clustering segmentation; Local feature weight; Curvature", "DOI": "10.1007/s11042-019-08512-1", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Architectural and Mechanical Engineering, Chifeng University, Chifeng, China;School of Mechatronic Engineering, Nanchang University, Nanchang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Guizhou Normal University, Guiyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering, Nanchang University, Nanchang, China"}], "References": []}, {"ArticleId": 79380720, "Title": "Denoising Color Images Based on Local Orientation Estimation and CNN Classifier", "Abstract": "<p>A structure-adaptive vector filter for removal of impulse noise from color images is presented. The proposed method is based on local orientation estimation. A color image is represented in quaternion form, and then, quaternion Fourier transform is used to compute the orientation of the pattern in a local neighborhood. Since the computation in quaternion frequency domain is extremely time-consuming, we prove a theorem that the integral of the product of frequency variables and the magnitude of quaternion frequency signals can be computed directly in spatial domain, which results that the color orientation detection problem can be solved in spatial domain. Based on the local orientation and orientation strength, the size, shape, and orientation of the support window of vector median filter (VMF) are adaptively determined, leading to an effective structure-adaptive VMF. Unlike the classical VMF restricting the output to the existing color samples, this paper computes the output of VMF over the entire 3D data space, which boosts the filtering performance effectively. To further improve denoising effect, a deep convolutional neural network is employed to detect impulse noise in color images and integrated into the proposed denoising framework. The experimental results exhibit the effectiveness of the proposed denoiser by showing significant performance improvements both in noise suppression and in detail preservation, compared to other color image denoising methods.</p>", "Keywords": "Color image; Impulse noise; Orientation estimation; Quaternion; Structure-adaptive filter; Convolutional neural network", "DOI": "10.1007/s10851-019-00942-8", "PubYear": 2020, "Volume": "62", "Issue": "4", "JournalId": 6119, "JournalTitle": "Journal of Mathematical Imaging and Vision", "ISSN": "0924-9907", "EISSN": "1573-7683", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Jin", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China"}], "References": []}, {"ArticleId": 79380736, "Title": "Multilayer Joint Segmentation Using MRF and Graph Cuts", "Abstract": "<p>The problem of jointly segmenting objects, according to a set of labels (of cardinality L ), from a set of images (of cardinality K ) to produce K individual segmentations plus one joint segmentation, can be cast as a Markov random field model. Coupling terms in the considered energy function enforce the consistency between the individual segmentations and the joint segmentation. However, neither optimality on the minimizer (at least for particular cases), nor the sensitivity of the parameters, nor the robustness of this approach against standard ones has been clearly discussed before. This paper focuses on the case where (L>1) , (K>1) and the segmentation problem is handled using graph cuts. Noticeably, some properties of the considered energy function are demonstrated, such as global optimality when (L=2) and (K>1) , the link with majority voting and the link with naive Bayes segmentation. Experiments on synthetic and real images depict superior segmentation performance and better robustness against noisy observations.</p>", "Keywords": "Multiple images; Markov random field; Segmentation; Graph cuts; Hyperspectral images", "DOI": "10.1007/s10851-019-00938-4", "PubYear": 2020, "Volume": "62", "Issue": "6-7", "JournalId": 6119, "JournalTitle": "Journal of Mathematical Imaging and Vision", "ISSN": "0924-9907", "EISSN": "1573-7683", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "SATIE Laboratory, Université Paris-Sud, Université Paris-Saclay, Orsay, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SATIE Laboratory, Université Paris-Sud, Université Paris-Saclay, Orsay, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institut de Mathématiques de Toulouse, UMR5219, Université de Toulouse, CNRS UPS IMT, Toulouse Cedex 9, France;Institut de Recherche Technologique Saint-Exupéry, Toulouse, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Veolia Recherche and Innovation, Limay, France"}], "References": []}, {"ArticleId": 79380750, "Title": "Finite-Time Synchronization of Coupled Inertial Memristive Neural Networks with Mixed Delays via Nonlinear Feedback Control", "Abstract": "<p>The finite-time synchronization of coupled inertial memristive neural networks (IMNNs) systems is discussed in this paper. Firstly, a mathematical model of IMNNs with time-varying delays is given, then the original system is transformed into a first-order differential equation by selecting a suitable variable substitution. Secondly, by using two different controllers and the definition of the upper right-hand derivative, it can be guaranteed that finite-time and fixed-time synchronization between response system and drive system based on finite time stability and fixed time theory. Finally, two numerical simulations are given to illustrate the effectiveness of the main results.</p>", "Keywords": "Finite-time synchronization; Fixed-time synchronization; Nonlinear feedback control; Time delays", "DOI": "10.1007/s11063-019-10180-z", "PubYear": 2020, "Volume": "51", "Issue": "2", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Nanchang University, Nanchang, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Nanchang University, Nanchang, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Yang", "Affiliation": "Department of Mathematics, Nanchang University, Nanchang, People’s Republic of China"}], "References": []}, {"ArticleId": 79380757, "Title": "A model of opinion and propagation structure polarization in social media", "Abstract": "Abstract The issue of polarization in online social media has been gaining attention in recent years amid the changing political landscapes of many parts of the world. Several studies empirically observed the existence of echo chambers in online social media, stimulating a slew of works that tries to model the phenomenon via opinion modeling. Here, we propose a model of opinion dynamics centered around the notion that opinion changes are invoked by news exposure. Our model comes with parameters for opinions and connection strength which are updated through news propagation. We simulate the propagation of multiple news under the model in synthetic networks and observe the evolution of the model’s parameters and the propagation structure induced. Unlike previous models, our model successfully exhibited not only polarization of opinion, but also segregated propagation structure. By analyzing the results of our simulations, we found that the formation probability of echo chambers is primarily connected to the news polarization. However, it is also affected by intolerance to dissimilar opinions and how quickly individuals update their opinions. Through simulations on Twitter networks, we found that the behavior of the model is reproducible across different network structure and sizes.", "Keywords": "Echo chambers; Polarization; Opinion modeling; News propagation", "DOI": "10.1186/s40649-019-0076-z", "PubYear": 2020, "Volume": "7", "Issue": "1", "JournalId": 21567, "JournalTitle": "Computational Social Networks", "ISSN": "", "EISSN": "2197-4314", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>z<PERSON> <PERSON>", "Affiliation": "Murata Laboratory, Tokyo Institute of Technology, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Murata Laboratory, Tokyo Institute of Technology, Tokyo, Japan"}], "References": []}, {"ArticleId": 79380786, "Title": "Flows over periodic hills of parameterized geometries: A dataset for data-driven turbulence modeling from direct simulations", "Abstract": "Computational fluid dynamics models based on Reynolds-averaged Navier–Stokes equations with turbulence closures still play important roles in engineering design and analysis. However, the development of turbulence models has been stagnant for decades. With recent advances in machine learning, data-driven turbulence models have become attractive alternatives worth further explorations. However, a major obstacle in the development of data-driven turbulence models is the lack of training data. In this work, we survey currently available public turbulent flow databases and conclude that they are inadequate for developing and validating data-driven models. Rather, we need more benchmark data from systematically and continuously varied flow conditions (e.g., Reynolds number and geometry) with maximum coverage in the parameter space for this purpose. To this end, we perform direct numerical simulations of flows over periodic hills with varying slopes, resulting in a family of flows over periodic hills which ranges from incipient to mild and massive separations. We further demonstrate the use of such a dataset by training a machine learning model that predicts Reynolds stress anisotropy based on a set of mean flow features. We expect the generated dataset, along with its design methodology and the example application presented herein, will facilitate development and comparison of future data-driven turbulence models.", "Keywords": "Physics-informed machine learning ; Turbulence modeling ; Separated flows", "DOI": "10.1016/j.compfluid.2020.104431", "PubYear": 2020, "Volume": "200", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> Department of Aerospace and Ocean Engineering, Virginia Tech, Blacksburg, VA, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kevin <PERSON> Department of Aerospace and Ocean Engineering, Virginia Tech, Blacksburg, VA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Aeronautics, Imperial College London, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, The Ohio State University, Columbus, OH, USA"}], "References": [{"Title": "Flows over periodic hills of parameterized geometries: A dataset for data-driven turbulence modeling from direct simulations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "104431", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 79380816, "Title": "Bag-of-Concepts representation for document classification based on automatic knowledge acquisition from probabilistic knowledge base", "Abstract": "Text representation, a crucial step for text mining and natural language processing, concerns about transforming unstructured textual data into structured numerical vectors to support various machine learning and data mining algorithms. For document classification, one classical and commonly adopted text representation method is Bag-of-Words (BoW) model. BoW represents document as a fixed-length vector of terms, where each term dimension is a numerical value such as term frequency or tf-idf weight. However, BoW simply looks at surface form of words. It ignores the semantic, conceptual and contextual information of texts, and also suffers from high dimensionality and sparsity issues. To address the aforementioned issues, we propose a novel document representation scheme called Bag-of-Concepts (BoC), which automatically acquires useful conceptual knowledge from external knowledge base, then conceptualizes words and phrases in the document into higher level semantics (i.e. concepts) in a probabilistic manner, and eventually represents a document as a distributed vector in the learned concept space. By utilizing background knowledge from knowledge base, BoC representation is able to provide more semantic and conceptual information of texts, as well as better interpretability for human understanding. We also propose Bag-of-Concept-Clusters (BoCCl) model which clusters semantically similar concepts together and performs entity sense disambiguation to further improve BoC representation. In addition, we combine BoCCl and BoW representations using an attention mechanism to effectively utilize both concept-level and word-level information and achieve optimal performance for document classification.", "Keywords": "Natural language processing ; Text representation ; Document classification ; Knowledge base ; Interpretability", "DOI": "10.1016/j.knosys.2019.105436", "PubYear": 2020, "Volume": "193", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "Peng<PERSON>i Li", "Affiliation": "School of Electrical and Electronic Engineering, Nanyang Technological University, 50 Nanyang Avenue, 639798, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Nanyang Technological University, 50 Nanyang Avenue, 639798, Singapore;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Nanyang Technological University, 50 Nanyang Avenue, 639798, Singapore"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Interdisciplinary Graduate School, Nanyang Technological University, 21 Nanyang Link, 637371, Singapore"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Nanyang Technological University, 50 Nanyang Avenue, 639798, Singapore"}], "References": []}, {"ArticleId": 79380863, "Title": "Effectful applicative similarity for call-by-name lambda calculi", "Abstract": "We introduce a notion of applicative similarity in which not terms but monadic values arising from the evaluation of effectful terms, can be compared. We prove this notion to be fully abstract whenever terms are evaluated in call-by-name order. This is the first full-abstraction result for such a generic, coinductive methodology for program equivalence.", "Keywords": "Call-by-name λ -calculus ; Applicative similarity ; <PERSON>'s method ; Algebraic effects", "DOI": "10.1016/j.tcs.2019.12.025", "PubYear": 2020, "Volume": "813", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ugo <PERSON>", "Affiliation": "Università di Bologna, Italy;INRIA Sophia Antipolis, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Università di Bologna, Italy;INRIA Sophia Antipolis, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo, Japan"}], "References": []}, {"ArticleId": 79381006, "Title": "Nanocutting mechanism of 6H-SiC investigated by scanning electron microscope online observation and stress-assisted and ion implant-assisted approaches", "Abstract": "<p>Nanocutting mechanism of single crystal 6H-SiC is investigated through a novel scanning electron microscope setup in this paper. Various undeformed chip thicknesses on (0001) < 1–100 > orientation are adopted in the nanocutting experiments. Phase transformation and dislocation activities involved in the 6H-SiC nanocutting process are also characterized and analyzed. Two methods of stress-assisted and ion implant-assisted nanocutting are studied to improve 6H-SiC ductile machining ability. Results show that stress-assisted method can effectively decrease the hydrostatic stress and help to activate dislocation motion and ductile machining; ion implant-induced damages are helpful to improve the ductile machining ability from MD simulation and continuous nanocutting experiments under the online observation platform.</p>", "Keywords": "Diamond turning; Silicon carbide; Phase transformation; Surface integrity; MD simulation; Ion beam machining", "DOI": "10.1007/s00170-019-04886-6", "PubYear": 2020, "Volume": "106", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Measuring Technology & Instruments, Centre of MicroNano Manufacturing Technology, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Measuring Technology & Instruments, Centre of MicroNano Manufacturing Technology, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Measuring Technology & Instruments, Centre of MicroNano Manufacturing Technology, Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "Dongyu Tian", "Affiliation": "State Key Laboratory of Precision Measuring Technology & Instruments, Centre of MicroNano Manufacturing Technology, Tianjin University, Tianjin, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Interdisciplinary Centre for Advanced Materials Simulation (ICAMS), Ruhr-University Bochum, Bochum, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Precision Engineering, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Precision Manufacturing, Department of Design, Manufacture & Engineering Management, University of Strathclyde, Glasgow, UK"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Fraunhofer Institute for Integrated Systems and Device Technology (IISB), Erlangen, Germany"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Physics, Helsinki Institute of Physics POB 43, University of Helsinki, Helsinki, Finland"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Precision Measuring Technology & Instruments, Centre of MicroNano Manufacturing Technology, Tianjin University, Tianjin, China"}, {"AuthorId": 11, "Name": "Fengzhou Fang", "Affiliation": "State Key Laboratory of Precision Measuring Technology & Instruments, Centre of MicroNano Manufacturing Technology, Tianjin University, Tianjin, China"}], "References": []}, {"ArticleId": 79381188, "Title": "ICT4D 3.0? Part 2—The patterns of an emerging “digital‐for‐development” paradigm", "Abstract": "<p>There is evidence of “ICT4D 3.0”: a new “digital‐for‐development” paradigm emerging in the relationship between digital technologies and international development. Part 1 of this paper looked at the component parts of that paradigm. Part 2, provided here, analyses the impact of the paradigm on development. It does this by looking for big‐picture patterns in terms of two key logics that shape society: the logic of competition and the logic of cooperation. Looking at evidence in the economic and political domains, it finds that digital technologies are associated with a reproduction, diffusion, mutation and intensification of the dominant, competitive logic in developing countries. We see this in relation to capitalism, competitive markets and hierarchical state‐citizen relations. At the same time, though, the digital‐for‐development paradigm is also associated with growing examples and opportunities for an alternative economics and an alternative politics based around cooperative logic. The paper ends with some suggestions for the future digital‐for‐development research agenda.</p>", "Keywords": "ICT4D;digital‐for‐development;paradigm;trends;logics", "DOI": "10.1002/isd2.12123", "PubYear": 2020, "Volume": "86", "Issue": "3", "JournalId": 6046, "JournalTitle": "The Electronic Journal of Information Systems in Developing Countries", "ISSN": "1681-4835", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Development Informatics, GDI, University of Manchester, Manchester, UK"}], "References": [{"Title": "Understanding the interplay of institutional logics and management practices in impact sourcing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "125", "JournalTitle": "Information Systems Journal"}, {"Title": "A method of garment factory workers’ performance monitoring using control chart based on RFID system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1049", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 79381383, "Title": "Face hallucination with K-means++ dictionary learning", "Abstract": "<p>Interested face regions have the low-resolution problem in the video surveillance because the distance between face and camera is far. Thus, the high-resolution (HR) faces need to be reconstructed from low-resolution (LR) faces for further processing. Typical face hallucination based on patch-wise sparse coding can achieve better results but have very high complexity for training. In order to reduce the complexity, this paper proposes a method which uses K-means++ clustering instead of sparse coding to obtain an over-complete dictionary pair. Then, the least angle regression (LARS) algorithm is utilized to calculate the coefficients and reconstruct the high-resolution faces. The experimental results show that proposed algorithm can effectively reduce complexity in condition of irregular LR faces. In addition, the comparisons also prove that the proposed method can improve the value of PSNR and SSIM in the same database.</p>", "Keywords": "Low-resolution; Face hallucination; K-means++ clustering; LARS; Dictionary pair", "DOI": "10.1007/s11042-019-08505-0", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China;Shenzhen Research Institute of Shandong University, Shandong University, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Liu", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}], "References": []}, {"ArticleId": 79381883, "Title": "Efficient convolution pooling on the GPU", "Abstract": "The main contribution of this paper is to show efficient implementations of the convolution-pooling in the GPU, in which the pooling follows the multiple convolution. Since the multiple convolution and the pooling operations are performed alternately in earlier stages of many Convolutional Neural Networks (CNNs), it is very important to accelerate the convolution-pooling. Our new GPU implementation uses two techniques, (1) convolution interchange with direct sum, and (2) conversion to matrix multiplication. By these techniques, the computational and memory access cost are reduced. Further the convolution interchange is converted to matrix multiplication, which can be computed by cuBLAS very efficiently. Experimental results using Tesla V100 GPU show that our new GPU implementation compatible with cuDNN for the convolution-pooling is expected 2.90 times and 1.43 times faster for fp32 and fp16 than the multiple convolution and then the pooling by cuDNN, respectively. the most popular library of primitives to implement the CNNs in the GPU.", "Keywords": "Deep learning ; Neural Networks ; Convolution ; Average pooling ; GPU", "DOI": "10.1016/j.jpdc.2019.12.006", "PubYear": 2020, "Volume": "138", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Hiroshima University, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Hiroshima University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Hiroshima University, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Hiroshima University, Japan;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Hiroshima University, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujitsu Laboratories, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fujitsu Laboratories, Japan"}], "References": []}, {"ArticleId": 79381922, "Title": "Preparation and mechanism investigation of highly sensitive humidity sensor based on two-dimensional porous Gold/Graphite carbon nitride nanoflake", "Abstract": "In this study, a humidity sensor with high performance based on two dimensional porous graphite carbon nitride (2D Au/g-C<sub>3</sub>N<sub>4</sub>) by stripping methods was designed. It solves the problem that g-C<sub>3</sub>N<sub>4</sub> does not easily dissociate water molecules to form conductive ions (H<sub>3</sub>O <sup>+</sup> ). Density functional theory (DFT) simulation results show that Au nanoparticles on the surface of g-C<sub>3</sub>N<sub>4</sub> can promote the decomposition of water to form hydroxyl groups and reduce the band gap of Au/g-C<sub>3</sub>N<sub>4</sub> when the hydroxyl group is adsorbed on the surface of Au/g-C<sub>3</sub>N<sub>4</sub>. The narrower band gap makes the electrons more easily to be excited and promotes the formation of H<sub>3</sub>O <sup>+</sup> , hence improving the conductivity of the sensor. Experiments show that an appropriate amount of Au nanoparticles between the g-C<sub>3</sub>N<sub>4</sub> layers can provide a large number of adsorption/active sites to accelerate the decomposition of water molecules and enhance the conductivity of g-C<sub>3</sub>N<sub>4</sub>. The conductivity of Au/g-C<sub>3</sub>N<sub>4</sub> (0.5 mM) humidity sensor achieves more than 5 orders of magnitude in impedance change with low hysteresis (1.38 %) and high stability when the relative humidity is varied from 11%–95%. This study provides a theoretical basis and guidance for the development of resistive humidity sensors based on g-C<sub>3</sub>N<sub>4</sub>.", "Keywords": "Resistive humidity sensor ; Graphite carbon nitride ; Porous Au/g-C<sub>3</sub>N<sub>4</sub> ; Density functional theory ; Small-sized Au nanoparticles", "DOI": "10.1016/j.snb.2020.127679", "PubYear": 2020, "Volume": "307", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physical Science and Technology, Xinjiang University, Urumqi 830046, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physical Science and Technology, Xinjiang University, Urumqi 830046, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Physical Science and Technology, Xinjiang University, Urumqi 830046, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Physical Science and Technology, Xinjiang University, Urumqi 830046, PR China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physical Science and Technology, Xinjiang University, Urumqi 830046, PR China"}], "References": []}, {"ArticleId": 79381984, "Title": "Complexity of modification problems for reciprocal best match graphs", "Abstract": "Reciprocal best match graphs (RBMGs) are vertex colored graphs whose vertices represent genes and the colors the species where the genes reside. Edges identify pairs of genes that are most closely related with respect to an underlying evolutionary tree. In practical applications this tree is unknown and the edges of the RBMGs are inferred by quantifying sequence similarity. Due to noise in the data, these empirically determined graphs in general violate the condition of being a “biologically feasible” RBMG. Therefore, it is of practical interest in computational biology to correct the initial estimate. Here we consider deletion (remove at most k edges) and editing (add or delete at most k edges) problems. We show that the decision version of the deletion and editing problem to obtain RBMGs from vertex colored graphs is NP-hard. Using known results for the so-called bicluster editing, we show that the RBMG editing problem for 2-colored graphs is fixed-parameter tractable. A restricted class of RBMGs appears in the context of orthology detection. These are cographs with a specific type of vertex coloring known as hierarchical coloring. We show that the decision problem of modifying a vertex-colored graph (either by edge-deletion or editing) into an RBMG with cograph structure or, equivalently, to an hierarchically colored cograph is NP-complete.", "Keywords": "Reciprocal best matches ; Hierarchically colored cographs ; Orthology relation ; Bicluster graph ; Editing ; NP-hardness ; Parameterized algorithms", "DOI": "10.1016/j.tcs.2019.12.033", "PubYear": 2020, "Volume": "809", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dpt. of Mathematics and Computer Science, University of Greifswald, Walther-Rathenau-Strasse 47, D-17487 Greifswald, Germany;Saarland University, Center for Bioinformatics, Building E 2.1, P.O. Box 151150, D-66041 Saarbrücken, Germany;Corresponding author at: Dpt. of Mathematics and Computer Science, University of Greifswald, Walther-Rathenau-Strasse 47, D-17487 Greifswald, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bioinformatics Group, Department of Computer Science; and Interdisciplinary Center of Bioinformatics, University of Leipzig, Härtelstraße 16-18, D-04107 Leipzig, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bioinformatics Group, Department of Computer Science; and Interdisciplinary Center of Bioinformatics, University of Leipzig, Härtelstraße 16-18, D-04107 Leipzig, Germany;Max-Planck-Institute for Mathematics in the Sciences, Inselstraße 22, D-04103 Leipzig, Germany;Inst. f. Theoretical Chemistry, University of Vienna, Währingerstraße 17, A-1090 Wien, Austria;Facultad de Ciencias, Universidad Nacional de Colombia, Bogotá, Colombia;Santa Fe Institute, 1399 Hyde Park Rd., Santa Fe, USA"}], "References": []}, {"ArticleId": 79381986, "Title": "Discovering Leonardo with artificial intelligence and holograms: A user study", "Abstract": "Cutting-edge visualization and interaction technologies are increasingly used in museum exhibitions, providing novel ways to engage visitors and enhance their cultural experience. Existing applications are commonly built upon a single technology, focusing on visualization, motion or verbal interaction (e.g., high-resolution projections, gesture interfaces, chatbots). This aspect limits their potential, since museums are highly heterogeneous in terms of visitors profiles and interests, requiring multi-channel, customizable interaction modalities. To this aim, this work describes and evaluates an artificial intelligence powered, interactive holographic stand aimed at describing <PERSON>’s art. This system provides the users with accurate 3D representations of <PERSON>’s machines, which can be interactively manipulated through a touchless user interface. It is also able to dialog with the users in natural language about <PERSON>’s art, while keeping the context of conversation and interactions. Furthermore, the results of a large user study, carried out during art and tech exhibitions, are presented and discussed. The goal was to assess how users of different ages and interests perceive, understand and explore cultural objects when holograms and artificial intelligence are used as instruments of knowledge and analysis.", "Keywords": "Holograms ; Conversational systems ; Artificial intelligence ; Touchless interfaces ; Cultural heritage ; 68T50 ; 68U35 ; 68T45 ; 68T30 ; 68T35", "DOI": "10.1016/j.patrec.2020.01.006", "PubYear": 2020, "Volume": "131", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy (ICAR-CNR), Via <PERSON> 111, Napoli 80131, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy (ICAR-CNR), Via <PERSON> 111, Napoli 80131, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy (ICAR-CNR), Via <PERSON> 111, Napoli 80131, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy (ICAR-CNR), <PERSON> 111, Napoli 80131, Italy;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy (ICAR-CNR), Via <PERSON> 111, Napoli 80131, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy (ICAR-CNR), Via <PERSON> 111, Napoli 80131, Italy;Dipartimento per le Tecnologie, Università degli Studi di Napoli Parthenope, Centro Direzionale Isola C4, Napoli 80143, Italy"}], "References": []}, {"ArticleId": 79381991, "Title": "Designing a colorimetric sensor containing nitrogen and oxygen atoms for uranyl ions identification: Chromatic mechanism, binding feature and on-site application", "Abstract": "It is still a challenge for scientists to eliminate interference and improve chromatic aberration in designing colorimetric uranyl ions sensors, and it is also not clear how the species of uranium and sensor synergistically affect the coloration mechanism and coordination mode of ligands containing nitrogen or oxygen atoms at different pH. Herein, we design and synthesize 2-(5′-(p-(diphenylamino)phenyl)-2′-pyridylazo)-5-(diethylamino)phenol (abbr. S-LH ) for the colorimetric detection of uranyl ions in mixed solvents. The rational introduced triphenylamine group by density functional theory (DFT) calculation enhances the chromaticity difference of the S-LH toward uranyl ions, making the detection limits of the uranyl ions approximately one order of magnitude lower than that of the present UV–vis and visual method. The species of S-LH and uranium at different pH are clarified and it well explains the reason of different color changes after adding uranyl ions. Stability constant experiments show that S-LH has excellent anti-interference to other metal ions during detecting uranium. The coordination structure of S-LH towards UO<sub>2</sub><sup>2+</sup> is further confirmed by HRMS, <sup>1</sup>H NMR titration, and DFT calculation. Finally, colorimetric test strips with S-LH are prepared to detect uranium in Xiang River, providing an on-site and real-time method to detect uranyl ions in the environment.", "Keywords": "Uranium ; Sensor ; Colorimetry ; pH effect ; Br-PADAP ; DFT calculation ; Species", "DOI": "10.1016/j.snb.2020.127681", "PubYear": 2020, "Volume": "307", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan Key Laboratory for the Design and Application of Actinide Complexes, University of South China, Hengyang, Hunan 421001, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, University of South China, Hengyang, Hunan 421001, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Key Laboratory for the Design and Application of Actinide Complexes, University of South China, Hengyang, Hunan 421001, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hunan Key Laboratory for the Design and Application of Actinide Complexes, University of South China, Hengyang, Hunan 421001, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, University of South China, Hengyang, Hunan 421001, PR China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, University of South China, Hengyang, Hunan 421001, PR China"}, {"AuthorId": 7, "Name": "Qinghua Hu", "Affiliation": "Hunan Key Laboratory for the Design and Application of Actinide Complexes, University of South China, Hengyang, Hunan 421001, PR China"}, {"AuthorId": 8, "Name": "Hong<PERSON> Wang", "Affiliation": "Hunan Key Laboratory for the Design and Application of Actinide Complexes, University of South China, Hengyang, Hunan 421001, PR China;Corresponding author"}], "References": []}, {"ArticleId": 79382019, "Title": "Scheduling directed acyclic graphs with optimal duplication strategy on homogeneous multiprocessor systems", "Abstract": "Modern applications generally need a large volume of computation and communication to fulfill the goal. These applications are often implemented on multiprocessor systems to meet the requirements in computing capacity and communication bandwidth, whereas, how to obtain a good or even the optimal performance on such systems remains a challenge. When tasks of the application are mapped onto different processors for execution, inter-processor communications become inevitable, which delays some tasks’ execution and deteriorates the schedule performance. To mitigate the overhead incurred by inter-processor communications and improve the schedule performance, task duplication strategy has been employed in the schedule. Most available techniques for the duplication-based scheduling problem utilize heuristic strategies to produce sub-optimal solutions, however, how to find the optimal duplication-based solution with the minimal schedule makespan remains an unsolved issue. To fill in this gap, this paper proposes a novel Mixed Integer Linear Programming (MILP) formulation for this problem, together with a set of key theorems which enable and simplify the MILP formulation. The proposed MILP formulation can optimize the duplication strategy, serialize the execution of task instances on each processor and determine data precedences among different task instances, thus producing the optimal solution. The proposed method is tested on a set of synthesized applications and platforms and compared with the well-known algorithm. The experimental results demonstrate the effectiveness of the proposed method.", "Keywords": "Task duplication ; Multiprocessor ; Schedule ; Mixed Integer Linear Programming ; Makespan", "DOI": "10.1016/j.jpdc.2019.12.012", "PubYear": 2020, "Volume": "138", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronic Science and Engineering, National University of Defense Technology, Changsha, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hunan University, Changsha, China;Department of Electronic Science and Engineering, National University of Defense Technology, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronic Science and Engineering, National University of Defense Technology, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronic Science and Engineering, National University of Defense Technology, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Science and Engineering, National University of Defense Technology, Changsha, China"}], "References": []}]