[{"ArticleId": 84856497, "Title": "Deep face clustering using residual graph convolutional network", "Abstract": "Face clustering has important applications in image retrieval and criminal investigation. Face images can be seen as the nodes of a graph and the possibility of links between the nodes will help us find clusters. Graph Convolutional Networks (GCNs) are powerful tools to infer the possibility of linkage between a given node and its neighbors. However, existing face clustering methods use shallow GCNs and have limited learning capabilities. We propose a deep face clustering method using Residual Graph Convolutional Network (RGCN), which contains more hidden layers. For each node, k -Nearest Neighbor ( k NN) algorithm is used to construct its sub-graphs. Then we apply the idea of ResNet into GCNs and construct RGCN to learn the possibility of linkage between two nodes. Compared with other popular face clustering approaches, our method is more efficient and has better clustering results in the experiments. In addition, the proposed RGCN clustering approach is able to detect the quantity of clusters automatically and can be extended to large datasets.", "Keywords": "Face clustering ; kNN ; Deep GCNs ; Residual network", "DOI": "10.1016/j.knosys.2020.106561", "PubYear": 2021, "Volume": "211", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China;Jiangsu Engineering Research Center of big data ubiquitous perception and intelligent agriculture applications, Zhenjiang 212013, China;Corresponding author at: School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China;Jiangsu Engineering Research Center of big data ubiquitous perception and intelligent agriculture applications, Zhenjiang 212013, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013, China"}], "References": []}, {"ArticleId": 84856520, "Title": "Output tracker design for uncertain nonlinear sandwich systems with sandwiched dead-zone nonlinearity based on adaptive finite-time control", "Abstract": "This paper addresses the adaptive finite-time tracking problem for nonlinear sandwich systems with dead-zone nonlinearity and full state constraints. The existence of the non-smooth dead-zone nonlinearity between the subsystems of the sandwich system makes the controller design as a challenging task. In this paper, a novel adaptive backstepping controller is proposed by employing the finite-time stability theorem and based on the barrier <PERSON><PERSON><PERSON><PERSON> functions and finite-time command filters. The proposed controller guarantees the output tracking of the specified time-varying reference signal with the sufficiently small bounded tracking error in a finite-time. Moreover, all states of the controlled system are confined to predefined compact sets. To verify the theoretical achievements and show the applicability of the proposed method, simulation results are provided for both numerical and practical examples.", "Keywords": "Nonlinear sandwich systems dead-zone nonlinearity barrier <PERSON><PERSON><PERSON><PERSON> functions adaptive finite-time tracking", "DOI": "10.1080/00207721.2020.1834641", "PubYear": 2021, "Volume": "52", "Issue": "3", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Shiraz University of Technology, Shiraz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Shiraz University of Technology, Shiraz, Iran"}], "References": [{"Title": "Adaptive Robust Tracker Design for Nonlinear Sandwich Systems Subject to Saturation Nonlinearities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3", "Page": "452", "JournalTitle": "Robotica"}]}, {"ArticleId": 84856532, "Title": "A graph-based clustering algorithm for software systems modularization", "Abstract": "Context: Clustering algorithms, as a modularization technique, are used to modularize a program aiming to understand large software systems as well as software refactoring. These algorithms partition the source code of the software system into smaller and easy-to-manage modules (clusters). The resulting decomposition is called the software system structure (or software architecture). Due to the NP-hardness of the modularization problem, evolutionary clustering approaches such as the genetic algorithm have been used to solve this problem. These methods do not make much use of the information and knowledge available in the artifact dependency graph which is extracted from the source code. Objective: To overcome the limitations of the existing modularization techniques, this paper presents a new modularization technique named GMA (Graph-based Modularization Algorithm). Methods: In this paper, a new graph-based clustering algorithm is presented for software modularization. To this end, the depth of relationships is used to compute the similarity between artifacts, as well as seven new criteria are proposed to evaluate the quality of a modularization. The similarity presented in this paper enables the algorithm to use graph-theoretic information. Results: To demonstrate the applicability of the proposed algorithm, ten folders of Mozilla Firefox with different domains and functions, along with four other applications, are selected. The experimental results demonstrate that the proposed algorithm produces modularization closer to the human expert’s decomposition (i.e., directory structure) than the other existing algorithms. Conclusion: The proposed algorithm is expected to help a software designer in the software reverse engineering process to extract easy-to-manage and understandable modules from source code.", "Keywords": "Graph clustering ; Clustering algorithms ; Software modularization ; Software maintenance ; Software comprehension ; Software architecture ; Architecture recovery", "DOI": "10.1016/j.infsof.2020.106469", "PubYear": 2021, "Volume": "133", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematical Sciences, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematical Sciences, University of Tabriz, Tabriz, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematical Sciences, University of Tabriz, Tabriz, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematical Sciences, University of Tabriz, Tabriz, Iran"}], "References": []}, {"ArticleId": 84856535, "Title": "Design and modeling of a two-magnet actuator for robotic micromanipulation", "Abstract": "In this paper we optimized the design of a magnetic actuator prototype based on permanent magnets for navigation of magnetic microparticles in cortical microvasculature networks. The proposed design can push-and-pull microparticles in an open-loop strategy in a wide range of several cm with force in the range of few hundred of μN. To do so, a successive quadratic programming (SQP) method is used to solve the optimization problem in terms of stability, displacement and force. To demonstrate the optimized actuator performances, we built a prototype and validated it experimentally. Finally, robotic experiments were conducted in order to investigate the micromanipulation capabilities of the designed actuator in realistic and vascular-shaped microfluidic phantoms mimicking the delivery of drugs to brain tumor sites.", "Keywords": "Microrobotics ; Magnetic actuator ; Modeling ; Magnetic manipulation ; Robotic control", "DOI": "10.1016/j.sna.2020.112391", "PubYear": 2020, "Volume": "316", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HEI Campus Centre, Laboratoire PRISME, Châteauroux, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "HEI Campus Centre, Laboratoire PRISME, Châteauroux, France;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INSA Centre Val de Loire, Univ. Orléans, Laboratoire PRISME, Bourges, France"}], "References": []}, {"ArticleId": 84856579, "Title": "initKmix-A novel initial partition generation algorithm for clustering mixed data using k-means-based clustering", "Abstract": "Mixed datasets consist of both numeric and categorical attributes. Various k-means-based clustering algorithms have been developed for these datasets. Generally, these algorithms use random partition as a starting point, which tends to produce different clustering results for different runs. In this paper, we propose, initKmix , a novel algorithm for finding an initial partition for k-means-based clustering algorithms for mixed datasets. In the initKmix algorithm, a k-means-based clustering algorithm is run many times, and in each run, one of the attributes is used to create initial clusters for that run. The clustering results of various runs are combined to produce the initial partition. This initial partition is then used as a seed to a k-means-based clustering algorithm to cluster mixed data. Experiments with various categorical and mixed datasets showed that initKmix produced accurate and consistent results, and outperformed the random initial partition method and other state-of-the-art initialization methods. Experiments also showed that k-means-based clustering for mixed datasets with initKmix performed similar to or better than many state-of-the-art clustering algorithms for categorical and mixed datasets.", "Keywords": "Mixed data ; Clustering ; k-means ; Initialization ; Random", "DOI": "10.1016/j.eswa.2020.114149", "PubYear": 2021, "Volume": "167", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information Technology, United Arab Emirates University, Al-Ain, United Arab Emirates;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "KITE, University Health Network, 550, University Avenue, Toronto, ON, Canada"}], "References": []}, {"ArticleId": 84856585, "Title": "Tailoring two-dimensional nanomaterials by structural engineering for chemical and biological sensing", "Abstract": "Atomically-thin two-dimensional (2D) nanostructure, an emerging class of nanomaterials, has achieved tremendous progress in the past decade and will remain a significant research topic in the near future. Apart from the discoveries of various 2D nanomaterials, remarkable attentions have been paid to the rational design and alteration of architectures based on pristine 2D materials to meet demands of next-generation functional applications in the near term. Herein, this review intends to provide an instant survey over the recent key advances on the structural engineering strategies of 2D nanomaterial-based architectures for new sensing capabilities and opportunities. Through intrinsic or extrinsic alterations, the pristine 2D nanostructures of some unique intrinsic properties could be modified and functionalized to overcome existing drawbacks and obtain synergistic abilities, demonstrating a great potential in promoting sensing performances. In parallel with the latest development of structural engineering strategies and related functionalized devices including sensors towards detecting Covid-19 antigens, the underlying mechanisms are reviewed to offer insights in understandings of architecture design and property tailoring for boosting sensing capabilities and performances.", "Keywords": "2D nanomaterials ; Chemical sensing ; Biosensing ; Structural engineering ; Covid-19", "DOI": "10.1016/j.snr.2020.100024", "PubYear": 2020, "Volume": "2", "Issue": "1", "JournalId": 72042, "JournalTitle": "Sensors and Actuators Reports", "ISSN": "2666-0539", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Materials Science and Engineering, University of Connecticut, Storrs, CT 06269-3136, USA;Institute of Materials Science, University of Connecticut, Storrs, CT 06269-3136, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Materials Science, University of Connecticut, Storrs, CT 06269-3136, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering, University of Connecticut, Storrs, CT 06269-3136, USA;Institute of Materials Science, University of Connecticut, Storrs, CT 06269-3136, USA;College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou 730070, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering, University of Connecticut, Storrs, CT 06269-3136, USA;Institute of Materials Science, University of Connecticut, Storrs, CT 06269-3136, USA;Department of Materials Science and Engineering & Institute of Materials Science, University of Connecticut, Storrs, CT 06269-3136, USA"}], "References": []}, {"ArticleId": 84856744, "Title": "A study on topic models using LDA and Word2Vec in travel route recommendation: focus on convergence travel and tours reviews", "Abstract": "<p>At present, we live in prosperity contrary to the past times. As income increases, people enjoy wealth, but more people tend to pursue their own inner happiness: travel. People go to other places or visit foreign countries for business or journey. This study aims to identify the best tour route for foreign tourists in South Korea. Based on the review analysis results, this paper also aims to put forward techniques and methodologies required in practical affairs when developing a travel site or a travel application. On this note, it collected tourists’ reviews at the Tripadvisor official website and conducted text mining technique as well as network analysis using R and Tagxedo, which is a big data analytic tool. The analysis results displayed that there were differences in travel preference, and especially, individual travelers had difficulty traveling by public transportation and selecting travel destinations. Therefore, customized travel routes were suggested for convenient use among travelers.</p>", "Keywords": "Convergence travel; Tours reviews; Topic model; LDA; Travel route; Convergence", "DOI": "10.1007/s00779-020-01476-2", "PubYear": 2022, "Volume": "26", "Issue": "2", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "Seong-Taek Park", "Affiliation": "Division of Software and Convergence, Sunmoon University, Chungnam, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Taishan University, Taian, China"}], "References": [{"Title": "The impacts of personal traits on knowledge discovery behaviors via mobile SNS", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "2", "Page": "395", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Construction of information search behavior based on data mining", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "2", "Page": "233", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "A study on smart factory-based ambient intelligence context-aware intrusion detection system using machine learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1405", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A survey on group recommender systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "2", "Page": "271", "JournalTitle": "Journal of Intelligent Information Systems"}]}, {"ArticleId": 84856758, "Title": "Improved Quantum Circuits via Intermediate Qutrits", "Abstract": "Quantum computation is traditionally expressed in terms of quantum bits, or qubits. In this work, we instead consider three-level qu\n trits \n . Past work with qutrits has demonstrated only constant factor improvements, owing to the log\n 2 \n (3) binary-to-ternary compression factor. We present a novel technique, intermediate qutrits, to achieve sublinear depth decompositions of the Generalized Toffoli and other arithmetic circuits using no additional ancilla—a significant improvement over linear depth for the best qubit-only equivalents. For example, our Generalized Toffoli construction features a 70× improvement in two-qudit gate count over a qubit-only decomposition. This results in circuit cost reductions for important algorithms like quantum neurons, Grover search, and even <PERSON><PERSON>’s algorithm. Using a previously developed simulator with near-term noise models, we demonstrate for these models over 90% mean reliability (fidelity) for the <PERSON><PERSON><PERSON> construction, versus under 30% for the qubit-only baseline. For our other constructions, such as the Incrementer, the A + B adder and the +K adder, we demonstrate the power of intermediate qutrits in producing asymptotic depth improvements with no additional ancilla. Together, these results suggest qutrits offer a promising path toward scaling quantum computation.", "Keywords": "", "DOI": "10.1145/3406309", "PubYear": 2020, "Volume": "1", "Issue": "1", "JournalId": 77792, "JournalTitle": "ACM Transactions on Quantum Computing", "ISSN": "2643-6809", "EISSN": "2643-6817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Chicago, Chicago"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Chicago, Chicago"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Chicago, Chicago"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, GA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Duke University, Durham, NC"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Chicago, Chicago"}], "References": []}, {"ArticleId": 84856925, "Title": "Clustering based feature selection using Partitioning Around Medoids (PAM)", "Abstract": "High-dimensional data contains a large number of features. With many features, high dimensional data requires immense computational resources, including space and time. Several studies indicate that not all features of high dimensional data are relevant to classification result. Dimensionality reduction is inevitable and is required due to classifier performance improvement. Several dimensionality reduction techniques were carried out, including feature selection techniques and feature extraction techniques. Sequential forward feature selection and backward feature selection are feature selection using the greedy approach. The heuristics approach is also applied in feature selection, using the Genetic Algorithm, PSO, and Forest Optimization Algorithm. PCA is the most well-known feature extraction method. Besides, other methods such as multidimensional scaling and linear discriminant analysis. In this work, a different approach is applied to perform feature selection. Cluster analysis based feature selection using Partitioning Around Medoids (PAM) clustering is carried out. Our experiment results showed that classification accuracy gained when using feature vectors' medoids to represent the original dataset is high, above 80%.", "Keywords": "Dimensionality reduction;Feature selection;Clustering;Partitioning Around Medoids (PAM);High dimensional data", "DOI": "10.26555/jifo.v14i2.a17620", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 45165, "JournalTitle": "Jurnal Informatika", "ISSN": "1978-0524", "EISSN": "2528-6374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Universitas Ahmad <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 84856930, "Title": "State of the art document clustering algorithms based on semantic similarity", "Abstract": "The constant success of the Internet made the number of text documents in electronic forms increases hugely. The techniques to group these documents into meaningful clusters are becoming critical missions. The traditional clustering method was based on statistical features, and the clustering was done using a syntactic notion rather than semantically. However, these techniques resulted in un-similar data gathered in the same group due to polysemy and synonymy problems. The important solution to this issue is to document clustering based on semantic similarity, in which the documents are grouped according to the meaning and not", "Keywords": "clustering documents;semantic similarity;algorithms;traditional method", "DOI": "10.26555/jifo.v14i2.a17513", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 45165, "JournalTitle": "Jurnal Informatika", "ISSN": "1978-0524", "EISSN": "2528-6374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Zakho"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Erbil Polytechnic University Erbil, Iraq"}], "References": []}, {"ArticleId": 84856945, "Title": "Child–display interaction: Lessons learned on touchless avatar-based large display interfaces", "Abstract": "Abstract \nDuring the last decade, touchless gestural interfaces have been widely studied as one of the most promising interaction paradigms in the context of pervasive displays. In particular, avatars and silhouettes have proved to be effective in making the touchless capacity of displays self-evident. In this paper, we focus on a child–display interaction approach to avatar-based touchless gestural interfaces. We believe that large displays offer an opportunity to stimulate children’s experiences and engagement; for instance, learning about art is very engaging for children but can bring a number of challenges. Our study aims to contribute to the literature on both pervasive displays and child–computer interaction by reporting the results of a study involving 107 children aged 2 to 10 years. The main purposes of this study were to discover: (1) whether an avatar (movable or immovable) provides interactions that are intuitive for children and therefore help to overcome so-called “affordance blindness”; (2) whether an avatar-based touchless interface makes children’s experiences engaging and enjoyable therefore improving recall of content provided through the interaction (learning about art). The study unveiled relevant outcomes in terms of affordance blindness and two-handed interactions. We provide evidence indicating that chronological age influences the style of child–avatar interaction. Finally, it is suggested that avatars could facilitate the development of new effective educational technologies for young children.", "Keywords": "Child–computer interaction; Gesture-based interfaces; Avatars; Pervasive displays; Ubiquitous computing", "DOI": "10.1007/s00779-020-01451-x", "PubYear": 2022, "Volume": "26", "Issue": "3", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Lancaster University, Lancaster, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Palermo, Palermo, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Hertfordshire, Hartfield, UK; Molde University College, Molde, Norway"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Palermo, Palermo, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Lincoln, Lincoln, UK"}], "References": []}, {"ArticleId": 84856969, "Title": "Information security analysis on physical security in university x using maturity model", "Abstract": "The threat of physical security can be from human factors, natural disasters, and information technology itself. Therefore, to prevent threats, we need the right tools to control current activities, evaluate potential impacts, and make appropriate plans so that business processes at X University will not be affected. This research starts by analyzing the problems that arise, followed by collecting the data needed, discussing the results, and making conclusions and recommendations that can be given. The method uses quantitative descriptive research. The research instrument uses interviews and questionnaire techniques. COBIT 5 is used as a framework for measuring the performance that is being implemented and will be achieved. Maturity models are used to measure current and future activities. The goal to be achieved is that the organization can create a physical security environment by the CIA principle (confidentiality, integrity, & availability). Positioning results are at level 3, meaning that the process is currently running in two main standard operating procedures. However, this evaluation specifically on the DSS5.5.5 subdomain (Providing Service Support-Managing physical security for IT Assets) in COBIT 5, and the results are still below the level 3 standard (Established Process), at 2.9 points. So, the right suggestion is to keep activities safe, one of which is to improve facilities and infrastructure, one of which is the use of biometric control in data center management rooms or other rooms with limited access.", "Keywords": "Physical Security;COBIT 5;Maturity Model", "DOI": "10.26555/jifo.v14i2.a14434", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 45165, "JournalTitle": "Jurnal Informatika", "ISSN": "1978-0524", "EISSN": "2528-6374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas AMIKOM Purwokerto"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas AMIKOM Purwokerto"}], "References": []}, {"ArticleId": 84856995, "Title": "Speech classification using combination virtual center of gravity and k-means clustering based on audio feature extraction", "Abstract": "Voice recognition can be done in a variety of ways. Sound patterns can be recognized by performing sound feature extraction. The trainer sound data is built from the best sound data selection using a correlation coefficient based on the level of similarity between sound data for optimal sound features. Extraction of voting features on this research using the Virtual Center of Gravity method. This method calculates the distance between the sound data against the center point of gravity with visualizations in the 3-dimensional form of white, black, and grey pattern spaces. The preprocessing process generates a complex number of data consisting of real numbers and imaginary numbers. The number will be calculated the distance to the Virtual Center of Gravity's pattern space using Euclidean Distance. The sound feature testing is done using K-Means Clustering by means of a speech classification data based sound. The results showed an accuracy of 92.5%.", "Keywords": "Classification;Feature Extraction;K-Mean;Virtual Center of Gravity", "DOI": "10.26555/jifo.v14i2.a17390", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 45165, "JournalTitle": "Jurnal Informatika", "ISSN": "1978-0524", "EISSN": "2528-6374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Politeknik Negeri Samarinda"}, {"AuthorId": 2, "Name": "Arief Bramanto Wicaksono Putra", "Affiliation": "Politeknik Negeri Samarinda"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Negeri Samarinda"}], "References": []}, {"ArticleId": 84857106, "Title": "Capacity of Median U-Turns Using Traffic Simulation", "Abstract": "<p>Median U-turn has been widely used at Iraqi highways and regards as one of sources of traffic congestion creation due to the fact the U-turn movements conflict with traffic at the origin and destination directions. To enhance traffic safety at U-turn sites, speed humps were installed to force speed reduction to the incoming traffic that conflicting with the turning traffic. This paper used real data taken from four median U-turn sites. Three of these sites has speed humps while the fourth is not. VISSIM traffic simulator has been used to build simulations models that could replicate the real site movements. The observations of traffic data for these sites have been conducted using video recordings to obtain traffic parameters contains traffic volumes and the time spent for tuning process. The simulation results based on traffic volumes and average time spent (ATS) for turning has been compared with the real data based on calibration and validation processes and good agreements have been obtained. The developed simulation models were used to examine different scenarios affecting the capacity of the U-turn traffic. The results suggested that the capacity is increased with the decreasing of speeds of opposing traffic and also with decreasing the distance between a speeds hump and the U-turn location.</p>", "Keywords": "Microsimulation; Traffic calming; U-turn; Speed humps; Capacity", "DOI": "10.1007/s13177-020-00237-y", "PubYear": 2021, "Volume": "19", "Issue": "1", "JournalId": 6726, "JournalTitle": "International Journal of Intelligent Transportation Systems Research", "ISSN": "1348-8503", "EISSN": "1868-8659", "Authors": [{"AuthorId": 1, "Name": "Jalal T. S. Al-Obaedi", "Affiliation": "Roads and Transport Department, College of Engineering, University of Al-Qadisiyah, Al-Diwaniyah, Iraq"}], "References": []}, {"ArticleId": 84857202, "Title": "Die Fristberechnung beim <PERSON>", "Abstract": "<p>Kommt es in einem Unternehmen zu einer Verletzung des Schutzes personenbezogener Daten (Data Breach) ist zügiges Handeln geboten. Sofern aus dem Vorfall Risiken für die Rechte und Freiheiten der betroffenen Personen erwachsen, besteht in einem solchen Fall eine Meldepflicht nach Art. 33 DSGVO. Der Beitrag macht Vorschläge, wie die vorgegebene 72-<PERSON>unden-Meldefrist in der Praxis zu berechnen ist. </p>", "Keywords": "", "DOI": "10.1007/s11623-020-1358-1", "PubYear": 2020, "Volume": "44", "Issue": "11", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hamburg, Deutschland"}], "References": []}, {"ArticleId": 84857314, "Title": "Artificial intelligence-based antivirus in order to detect malware preventively", "Abstract": "<p>The proposed paper investigates commercial antiviruses. About 17% of the antiviruses did not recognize the existence of the malicious samples analyzed. In order to overcome the limitations of commercial antiviruses, this project creates an antivirus able to identify the modus operandi of a malware application before it is even executed by the user. In the proposed methodology, the features extracted from the executables are the input attributes of artificial neural networks. The classification of neural networks aims to group executables of 32-bit architectures into two classes: benign and malware. In total, 6272 executables are used in order to validate the proposed methodology. The proposed antivirus achieves an average performance of 98.32% in the distinction between benign and malware executables, accompanied by an average response time of only 0.07 s. Our antivirus is statistically superior and more effective when compared to the best state-of-the-art antivirus. The limitations of commercial antiviruses can be catering for artificial intelligence techniques based on machine learning. Instead of empirical and heuristic models, the proposed work identifies, in a statistical way, behaviors previously classified as suspects in real time.</p>", "Keywords": "Malware; Disassembler; Antivirus; Artificial neural networks; Real-time malware detection; Computer forensics", "DOI": "10.1007/s13748-020-00220-4", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 7519, "JournalTitle": "Progress in Artificial Intelligence", "ISSN": "2192-6352", "EISSN": "2192-6360", "Authors": [{"AuthorId": 1, "Name": "Sidney M. <PERSON>. <PERSON>", "Affiliation": "Electronics and Systems Department, Federal University of Pernambuco, Recife, Brazil"}, {"AuthorId": 2, "Name": "Heverton K. de L. Silva", "Affiliation": "Tempest Security Intelligence, Recife, Brazil"}, {"AuthorId": 3, "Name": "<PERSON> S. Lu<PERSON>", "Affiliation": "Tempest Security Intelligence, Recife, Brazil"}, {"AuthorId": 4, "Name": "Hercília J. do N. Lima", "Affiliation": "São Miguel Faculty, Recife, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "São Miguel Faculty, Recife, Brazil"}, {"AuthorId": 6, "Name": "<PERSON> And<PERSON>", "Affiliation": "São Miguel Faculty, Recife, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "São Miguel Faculty, Recife, Brazil"}], "References": []}, {"ArticleId": 84857346, "Title": "Living in a Simulation? An Empirical Investigation of a Smart Driving-Simulation Testing System", "Abstract": "The internet of things (IoT) generally refers to the embedding of computing and communication devices in various types of physical objects (e.g., automobiles) used in people’s daily lives. This paper draws on feedback intervention theory to investigate the impact of IoT-enabled immediate feedback interventions on individual task performance. Our research context is a smart test-simulation service based on internet-of-vehicles (IoV) technology that was implemented by a large driver-training service provider in China. This system captures and analyzes data streams from onboard sensors and cameras installed in vehicles in real time and immediately provides individual students with information about errors made during simulation tests. We postulate that the focal smart service functions as a feedback intervention (FI) that can improve task performance. We also hypothesize that student training schedules moderate this effect and propose an interaction effect on student performance based on feedback timing and the number of FI cues. We collected data about students’ demographics, their training session records, and information about their simulation test(s) and/or their official driving skills field tests and used a quasi-experimental method along with propensity score matching to empirically validate our research model. Difference-in-difference analysis and multiple regression results support the significant impact of the simulation test as an FI on student performance on the official driving skills field test. Our results also supported the interaction effect between feedback timing and the number of corrective FI cues on official test performance. This paper concludes with a discussion of the theoretical contributions and practical significance of our research. © 2020 by the Association for Information Systems.", "Keywords": "Driver Training; Feedback Interventions; Feedback Timing; Internet of Things; Internet of Vehicles; Quasi-Experiments", "DOI": "10.17705/1jais.00622", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 24874, "JournalTitle": "Journal of the Association for Information Systems", "ISSN": "", "EISSN": "1536-9323", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hong Kong Polytechnic University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hong Kong Polytechnic University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hong Kong Polytechnic University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hong Kong Polytechnic University"}, {"AuthorId": 5, "Name": "Wen<PERSON><PERSON> Bai", "Affiliation": "Hong Kong Polytechnic University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "South China Agricultural University"}], "References": []}, {"ArticleId": 84857395, "Title": "IEEE 802.15.4 historical revolution versions: A survey", "Abstract": "<p>The IEEE 802.15.4 is a well-known standard that is widely utilized for wireless sensor networks in order to meet the low rate, low cost and energy efficiency requirements. Seventeen years have already passed since its appearance, but new amendments are constantly being introduced. Since the standard can operates in specific countries or can support some types of networks like SUN, LECIM and RFID, modifications are carried on the basic version and then new versions have been released. In this paper, we provide a clear and structured overview of the 802.15.4 standard and all its amendments and revisions. After a general introduction to the 802.15.4-2003 standard, we present a chronological description of the amendments specified by this standard. These descriptions include the modifications made either on the physical layer or the MAC sublayer. Some references having developed the amendments whether it be a simple overview or an evaluation of the performance of the standard have been cited. The aim of this paper is to present the evolution of the standard as well as to deal with many problems frequented by wireless sensor networks using the basic version.</p>", "Keywords": "WSN; IEEE 802.15.4; Amendment; MAC; PHY", "DOI": "10.1007/s00607-020-00844-3", "PubYear": 2021, "Volume": "103", "Issue": "1", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Unit LaMOS (Modeling and Optimization of Systems), Faculty of Exact Sciences, University of Bejaia, Bejaia, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Unit LaMOS (Modeling and Optimization of Systems), Faculty of Exact Sciences, University of Bejaia, Bejaia, Algeria"}], "References": [{"Title": "Multi‐Source Time Synchronization in IEEE Std 802.15.4‐2015 TSCH Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "2", "Page": "e148", "JournalTitle": "Internet Technology Letters"}]}, {"ArticleId": 84857669, "Title": "Ergonomics research on eye–hand control dual channel interaction", "Abstract": "<p>Eye-control interfaces are human–computer interfaces in which interaction is mediated by the user’s gaze. Using dwell time for target selection may be hindered by the “Midas Touch” problem, which posits that intentional selection and perceptual processes cannot be separated. To solve this problem, we investigated the influence of different dwell times on task performance. Results suggest that the optimal dwell time to trigger a click in eye-control movement plus eye-control click and hand-control movement plus eye-control click are 700 and 200 ms, respectively. In addition, the eye-control movement plus eye-control click mode has a lower completion rate than the hand-control movement plus eye-control click mode.</p>", "Keywords": "Human computer interaction; Usability testing; Interaction device", "DOI": "10.1007/s11042-020-10097-z", "PubYear": 2021, "Volume": "80", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Nuclear Power Safety Monitoring Technology and Equipment, China Nuclear Power Engineering Co., Ltd, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Nuclear Power Safety Monitoring Technology and Equipment, China Nuclear Power Engineering Co., Ltd, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Nuclear Power Safety Monitoring Technology and Equipment, China Nuclear Power Engineering Co., Ltd, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychology, Zhejiang Sci-Tech University, Hangzhou, China"}, {"AuthorId": 5, "Name": "Li <PERSON>", "Affiliation": "Department of Psychology, Zhejiang Sci-Tech University, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Zhejiang Sci-Tech University, Hangzhou, China"}, {"AuthorId": 7, "Name": "Hongting Li", "Affiliation": "Department of Psychology, Zhejiang Sci-Tech University, Hangzhou, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychology, Zhejiang Sci-Tech University, Hangzhou, China"}], "References": []}, {"ArticleId": 84857701, "Title": "Physical model inversion of the green spectral region to track assimilation rate in almond trees with an airborne nano-hyperspectral imager", "Abstract": "Significant advances toward the remote sensing of photosynthetic activity have been achieved in the last decades, including sensor design and radiative transfer model (RTM) development. Nevertheless, finding methods to accurately quantify carbon assimilation across species and spatial scales remains a challenge. Most methods are either empirical and not transferable across scales or can only be applied if highly complex input data are available. Under stress, the photosynthetic rate is limited by the maximum carboxylation rate (Vc<sub>max</sub>), which is determined by the leaf biochemistry and the environmental conditions. Vc<sub>max</sub> has been connected to plant photoprotective mechanisms, photosynthetic activity and chlorophyll fluorescence emission. Recent RTM developments such as the Soil-Canopy Observation of Photosynthesis and Energy fluxes (SCOPE) model allow the simulation of the sun-induced chlorophyll fluorescence (SIF) and Vc<sub>max</sub> effects on the canopy spectrum. This development provides an approach to retrieve Vc<sub>max</sub> through RTM model inversion and track assimilation rate. In this study we explore SIF, narrow-band indices and RTM inversion to track changes in photosynthetic efficiency as a function of vegetation stress. We use hyperspectral imagery acquired over an almond orchard under different management strategies which affected the assimilation rates measured in the field. Vc<sub>max</sub> used as an indicator of assimilation was retrieved through SCOPE model inversion from pure-tree crown hyperspectral data. The relationships between field-measured assimilation rates and Vc<sub>max</sub> retrieved from model inversion were higher (r<sup>2</sup> = 0.7–0.8) than when SIF was used alone (r<sup>2</sup> = 0.5–0.6) or when traditional vegetation indices were used (r<sup>2</sup> = 0.3–0.5). The method was proved successful when applied to two independent datasets acquired at two different dates throughout the season, ensuring its robustness and transferability. When applied to both dates simultaneously, the results showed a unique significant trend between the assimilation measured in the field and Vc<sub>max</sub> derived using SCOPE (r<sup>2</sup> = 0.56, p < 0.001). This work demonstrates that tracking assimilation in almond trees is feasible using hyperspectral imagery linked to radiative transfer-photosynthesis models.", "Keywords": "Assimilation ; Photosynthesis ; Hyperspectral ; Radiative transfer model ; RTM ; SCOPE ; Vc<sub>max</sub> ; Green spectral región ; Fluorescence ; SIF ; PRI ; Nano-Hyperspec", "DOI": "10.1016/j.rse.2020.112147", "PubYear": 2021, "Volume": "252", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Agriculture and Food, Faculty of Veterinary and Agricultural Sciences (FVAS), Department of Infrastructure Engineering, Melbourne School of Engineering (MSE), University of Melbourne, Melbourne, Victoria, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Agricultura Sostenible (IAS), Consejo Superior de Investigaciones Científicas (CSIC), <PERSON><PERSON>da <PERSON>dez <PERSON> s/n, 14004 Córdoba, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "European Commission (EC), Joint Research Centre (JRC), Ispra (VA), Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Agricultura Sostenible (IAS), Consejo Superior de Investigaciones Científicas (CSIC), <PERSON><PERSON><PERSON>dez <PERSON> s/n, 14004 Córdoba, Spain;Department of Geography, Swansea University, SA2 8PP Swansea, United Kingdom"}, {"AuthorId": 5, "Name": "P.J. <PERSON>-Te<PERSON>", "Affiliation": "School of Agriculture and Food, Faculty of Veterinary and Agricultural Sciences (FVAS), Department of Infrastructure Engineering, Melbourne School of Engineering (MSE), University of Melbourne, Melbourne, Victoria, Australia;Instituto de Agricultura Sostenible (IAS), Consejo Superior de Investigaciones Científicas (CSIC), Avenida Menéndez Pidal s/n, 14004 Córdoba, Spain"}], "References": []}, {"ArticleId": 84857751, "Title": "Automata-Based Dynamic Fault Tolerant Task Scheduling Approach in Fog Computing", "Abstract": "Fog computing is an extension of cloud computing that offers computing, storage and communication resources near the network edge, which makes it an ideal platform for processing latency-sensitive and compute-intensive tasks. However, efficient task execution in a fog platform is a challenging problem since fog nodes are loosely interconnected, highly dynamic, heterogeneous, and prone to failures. Therefore, a task scheduling algorithm that ensures reliable execution of the tasks while optimising response time is paramount. To address this challenge, we propose a new Dynamic Fault Tolerant Learning Automata (DFTLA) task scheduling approach. DFTLA determines an efficient assignment of the tasks to the fog nodes based on variable-structure learning automata. We evaluated the proposed DFTLA scheduler and compared its performance with three baseline methods. The results of the experiments show that the proposed algorithm ensures reliable execution of the tasks while optimising response time and energy consumption. Moreover, the proposed approach outperforms the baseline algorithms in all performance evaluation criteria.", "Keywords": "Fog computing;task scheduling;learning automata;fault tolerance", "DOI": "10.1109/TETC.2020.3033672", "PubYear": 2022, "Volume": "10", "Issue": "1", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Science, Engineering and Built Environment, Deakin University, Geelong, VIC, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science, Engineering and Built Environment, Deakin University, Geelong, VIC, Australia"}, {"AuthorId": 3, "Name": "Davood Izadi", "Affiliation": "<PERSON><PERSON><PERSON> is with the Canberra Institution Technology, Reid, ACT, Australia"}], "References": []}, {"ArticleId": 84857828, "Title": "A form-finding approach for the conceptual design of air-supported structures using 3D graphic statics", "Abstract": "This article introduces a novel equilibrium-based form-finding approach for air-supported structures that relies on vector-based 3D graphic statics and the Combinatorial Equilibrium Modelling (CEM). The proposed approach is particularly suitable for the conceptual phase of the design process, as it allows generating a wide range of design options of discrete air-supported structures without membrane material in real-time while taking into account the overall mechanical behaviour and the architectural potentials of these structures. Benchmarking analysis is carried out using Finite Element Method (FEM) models without the membrane material to validate the feasibility of the discrete structures generated using the proposed form-finding approach. The comparison between the models created by means of the proposed approach and the FEM models shows general compatibility in terms of the geometry of the structures and distribution of the internal forces. Moreover, a complete design method for air-supported structures that combines the strengths of vector-based 3D graphic statics and CEM, for the conceptual phase, and FEM, for the advanced phase with models that include the membrane material, is discussed.", "Keywords": "Air-supported structures ; Form-finding ; Structural design ; 3D graphic statics ; Combinatorial Equilibrium Modelling (CEM) ; Finite Element Method (FEM) ; Conceptual design phase", "DOI": "10.1016/j.compstruc.2020.106401", "PubYear": 2021, "Volume": "243", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "Zongshuai Wan", "Affiliation": "Key Laboratory of Structures Dynamic Behavior and Control of the Ministry of Education, Harbin Institute of Technology, Harbin 150090, China;School of Civil Engineering, Harbin Institute of Technology, Harbin 150090, China;Chair of Structural Design, Institute of Technology in Architecture, ETH Zürich, 8093 Zürich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Chair of Structural Design, Institute of Technology in Architecture, ETH Zürich, 8093 Zürich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chair of Structural Design, Institute of Technology in Architecture, ETH Zürich, 8093 Zürich, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Structures Dynamic Behavior and Control of the Ministry of Education, Harbin Institute of Technology, Harbin 150090, China;School of Civil Engineering, Harbin Institute of Technology, Harbin 150090, China;Corresponding author at: Key Laboratory of Structures Dynamic Behavior and Control of the Ministry of Education, Harbin Institute of Technology, Harbin 150090, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Structures Dynamic Behavior and Control of the Ministry of Education, Harbin Institute of Technology, Harbin 150090, China;School of Civil Engineering, Harbin Institute of Technology, Harbin 150090, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Chair of Structural Design, Institute of Technology in Architecture, ETH Zürich, 8093 Zürich, Switzerland"}], "References": [{"Title": "A Computer-Aided Approach to Equilibrium Design Based on Graphic Statics and Combinatorial Variations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "121", "Issue": "", "Page": "102802", "JournalTitle": "Computer-Aided Design"}]}, {"ArticleId": 84857832, "Title": "Correction to: Software engineering whispers: The effect of textual vs. graphical software design descriptions on software design communication", "Abstract": "To fulfill the contractual requirement of the Sweden Compact agreement, the following funding note has to be added and placed in the Funding section of the original article: Open access funding provided by University of Gothenburg .", "Keywords": "", "DOI": "10.1007/s10664-020-09889-6", "PubYear": 2020, "Volume": "25", "Issue": "6", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chalmers University of Gothenburg, Gothenburg, Sweden"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>-<PERSON><PERSON>nc", "Affiliation": "Lille University, Lille, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Chalmers University of Gothenburg, Gothenburg, Sweden"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Slovak University of Technology, Bratislava, Slovakia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Slovak University of Technology, Bratislava, Slovakia"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Lille University, Lille, France"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CEA LIST, Palaiseau, France"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Chalmers University of Gothenburg, Gothenburg, Sweden"}], "References": []}, {"ArticleId": 84858121, "Title": "Corner Reflector Based Misalignment-Tolerant Chipless RFID Tag Design Methodology", "Abstract": "Chipless RFID is a relatively new and rapidly growing field that faces some practical implementation challenges. One of these challenges is extreme sensitivity to small misalignments between the tag and the reader antenna. These tilts and translations can lead to erroneous responses which can then be interpreted as incorrect IDs or sensing parameter values in identification and sensing applications, respectively. While there has been some work to mitigate this limitation through reading and post-processing methods, the problem has yet to be sufficiently addressed from the tag design perspective. This work proposes a misalignment-tolerant chipless RFID tag design methodology that utilizes trihedral corner reflector bases loaded with resonators to produce tags that are tolerant of pitch and yaw rotations up to ±40° and roll rotations up to ±180° (i.e., orientation independence as it is commonly defined in the chipless RFID field). This approach allows for linearly polarized monostatic reading schemes to be used and provides for a large radar cross-section and resonance depth, which increases the detectability of the tag response.", "Keywords": "Chipless RFID;corner reflector;misalignment tolerant", "DOI": "10.1109/JRFID.2020.3034483", "PubYear": 2021, "Volume": "5", "Issue": "1", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Iowa State University, Ames, IA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Iowa State University, Ames, IA, USA"}], "References": [{"Title": "Practical Performance Comparison of 1-D and 2-D Decoding Methods for a Chipless RFID System in a Real Environment", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "532", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}]}, {"ArticleId": 84858248, "Title": "Two-stage deep regression enhanced depth estimation from a single RGB image", "Abstract": "", "Keywords": "", "DOI": "10.1109/TETC.2020.3034559", "PubYear": 2020, "Volume": "", "Issue": "", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "Jianyuan Sun", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84858395, "Title": "A Comparison of Procedural Safety Training in Three Conditions: Virtual Reality Headset, Smartphone, and Printed Materials", "Abstract": "Virtual reality (VR) experiences are receiving increasing attention in education and training. Some VR setups can deliver immersive VR training (e.g., on multiple projected screens), while others can deliver nonimmersive VR training (e.g., on standard desktop monitors). Recently, consumer VR headsets make it possible to deliver immersive VR training with six-degrees-of-freedom tracking of trainees’ head as well as hand controllers, while most smartphones can deliver nonimmersive VR training without the need for additional hardware. Previous studies compared immersive and nonimmersive VR setups for training, highlighting effects on performance, learning, presence, and engagement, but no study focused on contrasting procedural training with (immersive) VR headsets and (nonimmersive) smartphones. This article conducts a comparison of these two VR setups in the aviation safety domain. The considered training concerned door opening procedures in different aircraft and included a virtual instructor. In addition, we compared the two VR setups with the traditional printed materials used in the considered domain, i.e., safety cards. Results show that both VR setups allowed gaining and retaining more procedural knowledge than printed materials, and led to higher confidence in performing procedures. However, only the VR headset was considered to be significantly more usable than the printed materials, and presence was higher with the VR headset than the smartphone. The VR headset turned out to be important also for engagement and satisfaction, which were higher with the VR headset than both the printed materials and the smartphone. We discuss the implications of these results.", "Keywords": "Aviation safety;immersive virtual reality (VR);mobile devices;nonimmersive VR;procedural training;virtual instructor;VR;VR headset;smartphone;user study.", "DOI": "10.1109/TLT.2020.3033766", "PubYear": 2021, "Volume": "14", "Issue": "1", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Computer Science, and Physics, University of Udine, Udine, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Computer Science, and Physics, University of Udine, Udine, Italy"}], "References": [{"Title": "Humor and Fear Appeals in Animated Pedagogical Agents: An Evaluation in Aviation Safety Education", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "63", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "A Comparison of Seated and Room-Scale Virtual Reality in a Serious Game for Epidural Preparation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "218", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "A systematic review of immersive virtual reality applications for higher education: Design elements, lessons learned, and research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "103778", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 84858524, "Title": "Compact video fingerprinting via an improved capsule net", "Abstract": "Robustness, distinctiveness and compactness are the three basic performance metrics for video fingerprinting, and the three factors affect each other. It is challenging to improve them simultaneously. For this reason, an end-to-end fingerprinting via a capsule net is proposed. In order to capture video features, a capsule net, based on a 3D/2D mixed convolution module, is designed, which maps raw data to compact real vector directly. A new designed adaptive margin triplet loss function is introduced, and it can automatically adjust the loss according to the sample distance. It is beneficial for reducing training difficulty and improving performance. Three open access video datasets FCVID, TRECVID and You Tube are composed to train and test, large experimental results have shown that the proposed fingerprinting achieves better performance than traditional and deep learning methods.", "Keywords": "Mixed convolution module ; end-to-end ; adaptive margin triplet loss ; capsule net", "DOI": "10.1080/21642583.2020.1833782", "PubYear": 2021, "Volume": "9", "Issue": "sup1", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}], "References": []}, {"ArticleId": 84858578, "Title": "AudVowelConsNet: A phoneme-level based deep CNN architecture for clinical depression diagnosis", "Abstract": "Depression is a common and serious mood disorder that negatively affects the patient’s capacity of functioning normally in daily tasks. Speech is proven to be a vigorous tool in depression diagnosis. Research in psychiatry concentrated on performing fine-grained analysis on word-level speech components contributing to the manifestation of depression in speech and revealed significant variations at the phoneme-level in depressed speech. On the other hand, research in Machine Learning-based automatic recognition of depression from speech focused on the exploration of various acoustic features for the detection of depression and its severity level. Few have focused on incorporating phoneme-level speech components in automatic assessment systems. In this paper, we propose an Artificial Intelligence (AI) based application for clinical depression recognition and assessment from speech. We investigate the acoustic characteristics of phoneme units, specifically vowels and consonants for depression recognition via Deep Learning. We present and compare three spectrogram-based Deep Neural Network architectures, trained on phoneme consonant and vowel units and their fusion respectively. Our experiments show that the deep learned consonant-based acoustic characteristics lead to better recognition results than vowel-based ones. The fusion of vowel and consonant speech characteristics through a deep network significantly outperforms the single space networks as well as the state-of-art deep learning approaches on the DAIC-WOZ database.", "Keywords": "Major Depressive Disorder ; Clinical depression detection ; AI-based application ; HCI-based healthcare ; Speech depression recognition ; Deep phoneme-level learning", "DOI": "10.1016/j.mlwa.2020.100005", "PubYear": 2020, "Volume": "2", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Université Paris-Est Créteil (UPEC), LISSI, Vitry sur Seine 94400, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, 23 Avenue Guy <PERSON>, 69130 Écully, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Luxembourg, Kirchberg Campus, 6, rue Richard <PERSON>-Kalergi, L-1359, Luxembourg"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institut des Systèmes Intelligents et de Robotique, Sorbonne Universite, CNRS UMR 7222, Paris, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Université Paris-Est Créteil (UPEC), LISSI, Vitry sur Seine 94400, France;Corresponding author"}], "References": []}, {"ArticleId": 84858593, "Title": "In-situ and real-time probing cellulase biosensor formation and its interaction with lignosulfonate in varied media", "Abstract": "It is a challenge to interpret the signals obtained by Quartz Crystal Microbalance (QCM) and Surface Plasmon Resonance (SPR) accompanied with in the significant changes of the density and/or refractivity of media. The work presented herein focuses on demonstrating a universal in-situ and real-time monitoring methodology for ultrathin film formation on gold chips in varied media. Biomimetic model cellulase/enzyme film was immobilized in-situ on gold surface of sensors, followed by revealing its interaction with lignosulphonate in the media changed from ethanol-based to aqueous ones. Combining the techniques of Quartz Crystal Microbalance with Dissipation monitoring (QCM-D) and Multi-parametric Surface Plasmon Resonance (MP-SPR) along with respective software and models enabled us to precisely characterize the thickness, viscous and shear elastic moduli, and the coupled solvent for individual layer formed over the entire process, which are unachievable with conventional methods. The methodology provides real-time monitoring of filming process and offers us an in-situ qualify control approach to the biomimetic model films formed on different substrates including gold chip.", "Keywords": "Enzyme biosensor ; Real time monitoring ; Lignosulfonate ; Quartz crystal microbalance with dissipation monitoring (QCM-D) ; Surface plasmon resonance (SPR)", "DOI": "10.1016/j.snb.2020.129114", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China;Corresponding authors"}, {"AuthorId": 7, "Name": "Yongcan Jin", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, University of New Brunswick, Fredericton, NB, E3B 5A3 Canada"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Co-Innovation Center for Efficient Processing and Utilization of Forest Resources and Joint International Research Lab of Lignocellulosic Functional Materials, Nanjing Forestry University, Nanjing 210037, China;Corresponding authors"}], "References": []}, {"ArticleId": 84858613, "Title": "Human face recognition based on convolutional neural network and augmented dataset", "Abstract": "To deal with the issue of human face recognition on small original dataset, a new approach combining convolutional neural network (CNN) with augmented dataset is developed in this paper. The original small dataset is augmented to be a large dataset via several transformations of the face images. Based on the augmented face image dataset, the feature of the faces can be effectively extracted and higher face recognition accuracy can be achieved by using the ingenious CNN. The effectiveness and superiority of the proposed approach can be verified by several experiments and comparisons with some frequently used face recognition methods.", "Keywords": "Face recognition ; convolutional neural network ; augmented dataset ; CNN", "DOI": "10.1080/21642583.2020.1836526", "PubYear": 2021, "Volume": "9", "Issue": "sup2", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Energy and Mining Engineering, Shandong University of Science and Technology, Qingdao, People's Republic of China"}], "References": [{"Title": "Low resolution face recognition using a two-branch deep convolutional neural network architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112854", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-view face recognition using deep neural networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "375", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 84858633, "Title": "First steps towards a dynamical model for forest fire behaviour in Argentinian landscapes", "Abstract": "<p>We developed a Reaction Diffusion Convection (RDC) model for forest fire propagation coupled to a visualization platform with several functionalities requested by local firefighters. The dynamical model aims to understand the key mechanisms driving fire propagation in the Patagonian region. We'll show in this work the first tests considering combustion and diffusion in artificial landscapes. The simulator, developed in CUDA/OpenGL, integrates several layers including topography, weather, and fuel data. It allows to visualize the fire propagation and also to interact with the user in simulation time. The Fire Weather Index (FWI), extensively used in Argentina to support operative preventive measures for forest fires management, was also coupled to our visualization platform. This additional functionality allows the user to visualize on the landscape the fire risks, that are closely related to FWI, for Northwest Patagonian forests in Argentina.</p>", "Keywords": "Graphic Processing Units;Forest Fire Simulation;Reaction-Diffusion-Convection Model", "DOI": "10.24215/16666038.20.e09", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 56062, "JournalTitle": "Journal of Computer Science and Technology", "ISSN": "1666-6046", "EISSN": "1666-6038", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Nacional de Río Negro - CONICET"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Grupo de Física y Estadística Interdisciplinaria. Gerencia de Física. Comisión Nacional de Energía Atómica. CONICET"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centro Regional Universitario, Universidad Nacional del Comahue, Argentina"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universidad Nacional de Río Negro. Laboratorio de Procesamiento de Señales Aplicado y computación de Alto Rendimiento"}], "References": []}, {"ArticleId": 84858636, "Title": "Analysis, Deployment and Integration of Platforms for Fog Computing", "Abstract": "<p>In IoT applications, data capture in a sensor network can generate a large flow of information between the nodes and the cloud, affecting response times and device complexity but, above all, increasing costs. Fog computing refers to the use of pre-processing tools to improve local data management and communication with the cloud. This work presents an analysis of the features that platforms implementing fog computing solutions should have. Additionally, an experimental work integrating two specific platforms used for controlling devices in a sensor network, processing the generated data, and communicating with the cloud is presented.</p>", "Keywords": "IoT Platforms;Cloud Computing;Fog Computing;Internet of Things", "DOI": "10.24215/16666038.20.e12", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 56062, "JournalTitle": "Journal of Computer Science and Technology", "ISSN": "1666-6046", "EISSN": "1666-6038", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto de Investigación en Informática III LIDI, Facultad de Informática, Universidad Nacional de La Plata - Comisión de Investigaciones Científicas de la Provincia de Buenos Aires, La Plata, Argentina"}, {"AuthorId": 2, "Name": "Santiago Medina", "Affiliation": "Instituto de Investigación en Informática III LIDI, Facultad de Informática, Universidad Nacional de La Plata - Comisión de Investigaciones Científicas de la Provincia de Buenos Aires, La Plata, Argentina"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto de Investigación en Informática III LIDI, Facultad de Informática, Universidad Nacional de La Plata - Comisión de Investigaciones Científicas de la Provincia de Buenos Aires, La Plata, Argentina"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Investigación en Informática III LIDI, Facultad de Informática, Universidad Nacional de La Plata - Comisión de Investigaciones Científicas de la Provincia de Buenos Aires, La Plata, Argentina"}], "References": []}, {"ArticleId": 84858638, "Title": "Experimental Framework to Simulate Rescue Operations after a Natural Disaster", "Abstract": "<p>Computational simulation is a powerful tool for performance evaluation of computational systems. It is useful to make capacity planning of data center clusters, to obtain profiling reports of software applications and to detect bottlenecks. It has been used in different research areas like large scale Web search engines, natural disaster evacuations, computational biology, human behavior and tendency, among many others. However, properly tuning the parameters of the simulators, defining the scenarios to be simulated and collecting the data traces is not an easy task. It is an incremental process which requires constantly comparing the estimated metrics and the flow of simulated actions against real data. In this work, we present an experimental framework designed for the development of large scale simulations of two applications used upon the occurrence of a natural disaster strikes. The first one is a social application aimed to register volunteers and manage emergency campaigns and tasks. The second one is a benchmark application a data repository named MongoDB. The applications are deployed in a distributed platform which combines different technologies like a Proxy, a Containers Orchestrator, Containers and a NoSQL Database. We simulate both applications and the architecture platform. We validate our simulators using real traces collected during simulacrums of emergency situations.</p>", "Keywords": "Benchmark;Experimental framework;Simulation", "DOI": "10.24215/16666038.20.e07", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 56062, "JournalTitle": "Journal of Computer Science and Technology", "ISSN": "1666-6046", "EISSN": "1666-6038", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DIINF, CITIAPS, CeBiB, Universidad de Santiago de Chile, Santiago, Chile"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "DIINF, CITIAPS, CeBiB, Universidad de Santiago de Chile, Santiago, Chile"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidad de Valparaı́so, Valparaı́so, Chile"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad Nacional de San Luis, San Luis, Argentina"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DIINF, CITIAPS, CeBiB, Universidad de Santiago de Chile, Santiago, Chile"}], "References": []}, {"ArticleId": 84858641, "Title": "SEDAR: Soft Error Detection and Automatic Recovery in High Performance Computing Systems", "Abstract": "<p> \r Reliability and fault tolerance have become aspects of growing relevance in the field of HPC, due to the increased probability that faults of different kinds will occur in these systems. This is fundamentally due to the increasing complexity of the processors, in the search to improve performance, which leads to a rise in the scale of integration and in the number of components that work near their technological limits, being increasingly prone to failures. Another factor that affects is the growth in the size of parallel systems to obtain greater computational power, in terms of number of cores and processing nodes.\r As applications demand longer uninterrupted computation times, the impact of faults grows, due to the cost of relaunching an execution that was aborted due to the occurrence of a fault or concluded with erroneous results. Consequently, it is necessary to run these applications on highly available and reliable systems, requiring strategies capable of providing detection, protection and recovery against faults.\r In the next years it is planned to reach Exa-scale, in which there will be supercomputers with millions of processing cores, capable of performing on the order of 1018 operations per second. This is a great window of opportunity for HPC applications, but it also increases the risk that they will not complete their executions. Recent studies show that, as systems continue to include more processors, the Mean Time Between Errors decreases, resulting in higher failure rates and increased risk of corrupted results; large parallel applications are expected to deal with errors that occur every few minutes, requiring external help to progress efficiently. Silent Data Corruptions are the most dangerous errors that can occur, since they can generate incorrect results in programs that appear to execute correctly. Scientific applications and large-scale simulations are the most affected, making silent error handling the main challenge towards resilience in HPC. In message passing applications, a silent error, affecting a single task, can produce a pattern of corruption that spreads to all communicating processes; in the worst case scenario, the erroneous final results cannot be detected at the end of the execution and will be taken as correct.\r Since scientific applications have execution times of the order of hours or even days, it is essential to find strategies that allow applications to reach correct solutions in a bounded time, despite the underlying failures. These strategies also prevent energy consumption from skyrocketing, since if they are not used, the executions should be launched again from the beginning. However, the most popular parallel programming models used in supercomputers lack support for fault tolerance.</p>", "Keywords": "user-level checkpoint;Transient faults;soft errors;detection;process replication;automatic recovery;silent data corruption;HPC applications;multicore clusters;fault injection;system-level checkpoint", "DOI": "10.24215/16666038.20.e14", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 56062, "JournalTitle": "Journal of Computer Science and Technology", "ISSN": "1666-6046", "EISSN": "1666-6038", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "III-LIDI, Universidad Nacional de La Plata, Argentina"}], "References": []}, {"ArticleId": 84858644, "Title": "Microwave Spectroscopy Based Classification of Rat Hepatic Tissues: On the Significance of Dataset", "Abstract": "With the advancements in machine learning (ML) algorithms, microwave dielectric spectroscopy emerged as a potential new technology for biological tissue and material categorization. Recent studies reported the successful utilization of dielectric properties and Cole-Cole parameters. However, the role of the dataset was not investigated. Particularly, both dielectric properties and Cole-Cole parameters are derived from the S parameter response. This work investigates the possibility of using S parameters as a dataset to categorize the rat hepatic tissues into cirrhosis, malignant, and healthy categories. Using S parameters can potentially remove the need to derive the dielectric properties and enable the utilization of microwave structures such as narrow or wideband antennas or resonators. To this end, in vivo dielectric properties and S parameters collected from hepatic tissues were classified using logistic regression (LR) and adaptive boosting (AdaBoost) algorithms. Cole-Cole parameters and a reproduced dielectric property data set were also investigated. Data preprocessing is performed by using standardization and principal component analysis (PCA). Using the AdaBoost algorithm over 93% and 88% accuracy is obtained for dielectric properties and S parameters, respectively. These results indicate that the classification can be performed with a 5% accuracy decrease indicating that S parameters can be an alternative dataset for tissue classification.", "Keywords": "Cole-Cole parameters,dielectric properties,in vivo measurements,machine learning,rat hepatic tissues", "DOI": "10.17694/bajece.775198", "PubYear": 2020, "Volume": "8", "Issue": "4", "JournalId": 27857, "JournalTitle": "Balkan Journal of Electrical and Computer Engineering", "ISSN": "2147-284X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Tuba YİLMAZ", "Affiliation": "İSTANBUL TEKNİK ÜNİVERSİTESİ, ELEKTRONİK VE HABERLEŞME MÜHENDİSLİĞİ BÖLÜMÜ"}], "References": []}, {"ArticleId": 84858646, "Title": "Sketching enactive interactions", "Abstract": "<p>The continuous development of interactive technologies and the greater understanding of body importance in cognitive processes has driven HCI research, specifically on interaction design, to solve the user’s relationship with a multitude of beyond desktop devices. This has opened new challenges for having processes, methods and tools to achieve appropriate user experiences. Insofar as new devices and systems involve the body and social aspects of the human being, the consideration of paradigms, theories and support models that exceed the selection of navigation nodes and the appropriate visual organization of widgets and screens becomes more relevant. The interaction design must take care not only to get the product built properly but also to build the right product. This thesis is at the crossroads of three themes: the design of interactive systems that combine a foot in the digital and one in the physical, the theories of embodied and enactive cognition and the creative practices supported by sketching, in particular the processes of generation, evaluation and communication of interaction design ideas. This work includes contributions of different character. An in-depth study of the theories on embodied and enactive cognition, the design of interaction with digital devices and sketching as a basic tool of creative design is carried out. Based on this analysis of the existing literature and with a characterization of the enactive practice of enactive interactions based on ethnomethodological studies, a framework is proposed to conceptually organize this practice and a support tool for that activity conceived as a creative composition. The contributions are discussed, and possible lines of future work are considered.\r  </p>", "Keywords": "enactive cognition;HCI;interaction design;sketching", "DOI": "10.24215/16666038.20.e13", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 56062, "JournalTitle": "Journal of Computer Science and Technology", "ISSN": "1666-6046", "EISSN": "1666-6038", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centro de Investigación LIFIA, Facultad de Informática, UNLP, Argentina"}], "References": []}, {"ArticleId": 84858664, "Title": "WOLIF: An efficiently tuned classifier that learns to classify non-linear temporal patterns without hidden layers", "Abstract": "<p>We present in this paper a computationally efficient and biologically plausible classifier WOLIF, using Grey Wolf Optimizer (G WO ) tuned error function obtained from Leaky-Integrate-and-Fire ( LIF ) spiking neuron. Unlike traditional artificial neuron, spiking neuron is capable of intelligently classifying non-linear temporal patterns without hidden layer(s), which makes a Spiking Neural Network (SNN) computationally efficient. There is no additional cost of adding hidden layer(s) in SNN, it is also biologically plausible, and energy efficient. Since supervised learning rule for SNN is still in infancy stage, we introduced WOLIF classifier and its supervised learning rule based on GWO algorithm. WOLIF uses a single LIF neuron thereby use less network parameters, and homo-synaptic static long-term synaptic weights (both excitatory and inhibitory). Note that, WOLIF also reduces the total simulation time which improves computational efficiency. It is benchmarked on seven different datasets drawn from the UCI machine learning repository and found better results both in terms of accuracy and computational cost than state-of-the-art methods.</p>", "Keywords": "LIF neuron; Hidden layer; GWO; Temporal pattern; Non-linear; Static long-term plasticity", "DOI": "10.1007/s10489-020-01934-7", "PubYear": 2021, "Volume": "51", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Silchar, (Computer Science and Engineering), Silchar, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Institute of Technology Silchar, (Computer Science and Engineering), Silchar, India"}], "References": []}, {"ArticleId": 84858678, "Title": "An analysis of k-mer frequency features with SVM and CNN for viral subtyping classification", "Abstract": "<p>Viral subtyping classification is very relevant for the appropriate diagnosis and treatment of illnesses. The most used tools are based on alignment-based methods, nevertheless, they are becoming too slow with the increase of genomic data. For that reason, alignment-free methods have emerged as an alternative. In this work, we analyzed four alignment-free algorithms: two methods use k-mer frequencies (<PERSON><PERSON><PERSON> and Castor-KRFE); the third method used a frequency chaos game representation of a DNA with CNNs; finally the last one, process DNA sequences as a digital signal (ML-DSP). From the comparison, <PERSON><PERSON><PERSON> and Castor-KRFE outperformed the rest, followed by the method based on CNNs.</p>", "Keywords": "ML-DSP;CNN;genome;viral subtyping;k-mer;<PERSON><PERSON><PERSON>;Castor", "DOI": "10.24215/16666038.20.e11", "PubYear": 2020, "Volume": "20", "Issue": "2", "JournalId": 56062, "JournalTitle": "Journal of Computer Science and Technology", "ISSN": "1666-6046", "EISSN": "1666-6038", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Ingenierı́a y Matemáticas, Universidad La Salle, Peru"}], "References": []}, {"ArticleId": 84858680, "Title": "“There’s a Human Being Here”: A Doctoral Class Uses Duoethnography to Explore Invisibility, Hypervisibility, and Intersectionality", "Abstract": "Aim/Purpose: This paper contributes to the scholarly literature on intersectionality and social injustice (invisibility, hypervisibility) in higher education and serves as a model for enacting doctoral education where research, theory, and practice converge.\n\nBackground: Invisibility and hypervisibility have long been documented as social injustices, but very little literature has documented how doctoral students (who are also university employees) make meaning of intersecting privileges and oppressions within post-secondary hierarchies.\n\nMethodology: This study used a 10-week Duoethnography with co-researchers who were simultaneously doctoral students, staff, instructors, and administrators in higher education settings.\n\nContribution: This paper offers a unique glimpse into currere—the phenomenon of theory and practice converging—to offer an intensive interrogation of life as curriculum for five doctoral students and a professor. \n\nFindings: This paper illuminates rich meaning-making narratives of six higher educators as they grappled with invisibility and hypervisibility in the context of their intersecting social identities as well as their varied locations within post-secondary hierarchies/power structures.\n\nRecommendations for Practitioners: Duoethnography can be an effective strategy for social justice praxis in doctoral programs as well as other higher education departments, divisions, or student organizations.\n\nRecommendation for Researchers: Researchers can use Duoethnography to explore a plethora of social justice issues in doctoral education and across staff, faculty, and Ph.D. student experiences within the power structures of post-secondary education.\n\nImpact on Society: Examining intersectionality, invisibility and hypervisibility is an important way to delve into the complexity of oppression. There will be no justice until all forms of oppression (including hypervisibility and invisibility) are extinguished. \n\nFuture Research: Future research can more deeply explore social injustices and the intersections of not only social identities, but also social locations of doctoral students who are simultaneously employees and students in a university hierarchy.", "Keywords": "doctoral education; duoethnography; invisibility; hypervisibility; intersectionality", "DOI": "10.28945/4658", "PubYear": 2020, "Volume": "15", "Issue": "", "JournalId": 39448, "JournalTitle": "International Journal of Doctoral Studies", "ISSN": "1556-8881", "EISSN": "1556-8873", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Rhode Island"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Salve Regina University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Rhode Island"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Rhode Island College"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Community College Of Rhode Island"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Rhode Island"}], "References": []}, {"ArticleId": 84858717, "Title": "Information flow in context-dependent hierarchical Bayesian inference", "Abstract": "Recent theories developing broad notions of context and its effects on inference are becoming increasingly important in fields as diverse as cognitive psychology, information science and quantum information theory and computing. Here we introduce a novel and general approach to the characterisation of contextuality using the techniques of Chu spaces and Channel Theory viewed as general theories of information flow. This involves introducing three essential components into the formulism: events, conditions and measurement systems. Incorporating these factors in relationship to conditional probabilities leads to information flows both in the setting of Chu spaces and Channel Theory. The latter provides a representation of semantic content using local logics from which conditionals can be derived. We employ these features to construct cone-cocone diagrams, commutativity of which enforces inferential coherence. With these we build a scale-free architecture incorporating a Bayesian-like hierarchical structure, in which there is an interpretation of active inference and Markov blankets. We compare this architecture with other theories of contextuality which we briefly review. We also show that this development of ideas conveniently accommodates negative probabilities, leading to the notion of signed information flow, and address how quantum contextuality can be interpreted within this model. Finally, we relate contextuality to the Frame Problem, another way of characterising a fundamental limitation on the observational and inferential capabilities of finite agents.", "Keywords": "Chu space ; channel theory ; bayesian inference ; contextuality ; information flow ; local logic ; cone-Co<PERSON><PERSON> diagram ; active inference ; markov blanket ; frame problem", "DOI": "10.1080/0952813X.2020.1836034", "PubYear": 2022, "Volume": "34", "Issue": "1", "JournalId": 23221, "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence", "ISSN": "0952-813X", "EISSN": "1362-3079", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Caunes Minervois, FRANCE"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Computer Science Eastern,  Illinois University , Charleston, IL, USA;Adjunct Faculty Department of Mathematics,  University of Illinois at Urbana–Champaign , Urbana, IL, USA"}], "References": []}, {"ArticleId": 84858827, "Title": "DNA-based steganography using genetic algorithm", "Abstract": "Development in steganography cares about increasing the amount of secret data embedded with a carrier. DNA file as a cover media is the best choice for that manner to increase the data embededd. This paper presents steganography with DNA files and proposes a new method to embed secret data in DNA files using genetic algorithm to enhance the performance and care with the steganography performance measures. The proposed method solves the problems of substitution method, using genetic algorithm to choose the best positions in the DNA file, to embed secret data, make the modification rate equal 0 in most cases and the lowest in other cases, and reduce the list generated. The cracking probability of the algorithm is very low. For more security, the secret data are encrypted with RSA algorithm before embedding. Final experiment results show an improvement in modification rate of the carrier. © 2020 NSP Natural Sciences Publishing Cor.", "Keywords": "Data hiding; DNA files; Genetic algorithm; Security; Steganography", "DOI": "10.18576/isl/090307", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 84859064, "Title": "RETRACTED: Design of sports course management system based on Internet of Things and FPGA system", "Abstract": "Students' Significant requirements are the development of information technology and the increased activities in physical education classes. This is especially urgent to provide students with personalized physics education (physical education) courses. Just as college physics education lacked a specific application in the humanities teaching model, its game theory is also being used in physical education at colleges. It uses participant's behaviors, Information, strategies, etc. to design teaching methods and teaching materials through basic game theory concepts .i.e. content and rating method. This study proposes an Adaptive Data Reinforcement Technique (ADRT) technology that is the right solution when its unique run-time reconstruction is combined with curing adaptive data (Field Programmable Gate Arrays) FPGA-based motion process management strategy. However, they usually do not develop in user accessibility and usability. Then, it becomes the process of inputting game theory and practice only in the physical, educational process. Teachers need to actively guide students to participate in ball sports to actively improve student participation, which will improve skills in competitive games, social interaction, strong work skills, and good communication. In-games, Management ability effectively improves human quality and provides customers with a reference for reforming physics education in university physics education courses, and entirely plays an essential role in physics education of game characteristics.", "Keywords": "Sports teaching ; Adaptive Data Reinforcement Technique (ADRT) ; Field Programmable Gate Array (FPGA) ; The PE teaching reform", "DOI": "10.1016/j.micpro.2020.103357", "PubYear": 2021, "Volume": "80", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wuhan Institute of Technology, Wuhan 430070, China"}], "References": []}, {"ArticleId": 84859104, "Title": "Digital methods in cartographic source editing", "Abstract": "<p>The following article discusses current trends in digital methods used in historical cartographic source editing. Maps are often used not only by scholars interested in spatial history, but also by the general public. Due to increasing popularity of ‘maps online’, it is crucial to discuss, evaluate and classify current solutions and applications used in cartographic source editing. In the article the ‘edition’ is understood as the ‘critical representation of a historical documents’, and many types of map editions are considered. The level of map transformation: from raw image, through georeferenced map to spatial database constitutes the main axis of the division between various types of editions. On the very basic level (maps as images: digital libraries and collections), we only have a raster representation of a historic map, often supplemented by bibliographic metadata and the possibility to download it. The second level is geoportals with georeferenced maps, i.e. with spatial adjustment to modern geographic coordinates. The third, most complex level, includes elaboration of the spatial database with most important features (e.g. settlements, roads, land cover, etc.), which serves as the geographic index. The higher the map transformation level, the more useful the edition becomes. Nevertheless, more complex methods in map processing also should include more documentation about this process which is not always present within discussed projects. The main result of the article, apart from classifying existing applications and proposing their general types is to identify functionality and components of the so-called model edition of a historic map.</p>", "Keywords": "", "DOI": "10.1093/llc/fqaa061", "PubYear": 2021, "Volume": "36", "Issue": "3", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Historical Atlas, Polish Academy of Sciences, The Tadeusz Manteuffel Institute of History, Poland"}], "References": []}, {"ArticleId": 84859322, "Title": "Wireless Walking Paper Robot Driven by Magnetic Polymer Actuator", "Abstract": "<p>Untethered small-scale soft robots have been widely researched because they can be employed to perform wireless procedures via natural orifices in the human body, or other minimally invasive operations. Nevertheless, achieving untethered robotic motion remains challenging owing to the lack of an effective wireless actuation mechanism. To overcome this limitation, we propose a magnetically actuated walking soft robot based on paper and a chained magnetic-microparticle-embedded polymer actuator. The magnetic polymer actuator was prepared by combining Fe3O4 magnetic particles (MPs, diameter of ~50 nm) and silicon that are affected by a magnetic field; thereafter, the magnetic properties were quantified to achieve proper force and optimized according to the mass ratio, viscosity, and rotational speed of a spin coater. The fabricated polymer was utilized as a soft robot actuator that can be controlled using an external magnetic field, and paper was employed to construct the robot body with legs to achieve walking motion. To confirm the feasibility of the designed robot, the operating capability of the robot was analyzed through finite element simulation, and a walking experiment was conducted using electromagnetic actuation. The soft robot could be moved by varying the magnetic flux density and on–off state, and it demonstrated a maximum moving speed of 0.77 mm/s. Further studies on the proposed soft walking robot may advance the development of small-scale robots with diagnostic and therapeutic functionalities for application in biomedical fields.</p>", "Keywords": "soft robot; paper robot; magnetic polymer; electromagnetic actuation soft robot ; paper robot ; magnetic polymer ; electromagnetic actuation", "DOI": "10.3390/act9040109", "PubYear": 2020, "Volume": "9", "Issue": "4", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea↑Korea Institute of Medical Microrobotics, Gwangju 61011, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea↑Korea Institute of Medical Microrobotics, Gwangju 61011, Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Hong", "Affiliation": "Robotics Engineering, Chonnam National University, Gwangju 61186, Korea"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea↑Korea Institute of Medical Microrobotics, Gwangju 61011, Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON> Park", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea↑Korea Institute of Medical Microrobotics, Gwangju 61011, Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Chonnam National University, Gwangju 61186, Korea↑Korea Institute of Medical Microrobotics, Gwangju 61011, Korea↑Author to whom correspondence should be addressed"}], "References": [{"Title": "Magnetically actuated miniature walking soft robot based on chained magnetic microparticles-embedded elastomer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "301", "Issue": "", "Page": "111707", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 84859387, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0304-3975(20)30620-4", "PubYear": 2020, "Volume": "846", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 84859400, "Title": "On the longest common prefix of suffixes in an inverse Lyndon factorization and other properties", "Abstract": "The Lyndon factorization of a word has been largely studied and recently variants of it have been introduced and investigated with different motivations. In particular, the canonical inverse Lyndon factorization ICFL ( w ) of a word w , introduced in [1] , maintains the main properties of the Lyndon factorization since it can be computed in linear time and it is uniquely determined. In this paper we investigate new properties of this factorization with the aim of exploring their use in some classical queries on w . The main property we prove is related to a classical query on words. We prove that there are relations between the length of the longest common prefix (or longest common extension) lcp ( x , y ) of two different suffixes x , y of a word w and the maximum length M of two consecutive factors of ICFL ( w ) . More precisely, M is an upper bound on the length of lcp ( x , y ) . A main tool used in the proof of the above result is a property that we state for factors m i with nonempty borders in ICFL ( w ) : a nonempty border of m i cannot be a prefix of the next factor m i + 1 . Another interesting result relates sorting of global suffixes, i.e., suffixes of a word w , and sorting of local suffixes, i.e., suffixes of products of factors in ICFL ( w ) . This is the counterpart for ICFL ( w ) of the compatibility property, proved in [2] , [3] for the Lyndon factorization. Roughly, the compatibility property allows us to extend the mutual order between suffixes of products of the (inverse) Lyndon factors to the suffixes of the whole word. The last property we prove focuses on the Lyndon factorizations of a word and its factors. It suggests that the Lyndon factorizations of two words sharing a common overlap could be used to capture the common overlap of these two words.", "Keywords": "<PERSON> words ; <PERSON> factorization ; Combinatorial algorithms on words", "DOI": "10.1016/j.tcs.2020.10.034", "PubYear": 2021, "Volume": "862", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Informatica Sistemistica e Comunicazione, Università degli Studi di Milano Bicocca, Viale Sarca 336, 20126 Milano, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dipartimento di Informatica, Università degli Studi di Salerno, via <PERSON> 132, 84084 Fisciano (SA), Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Informatica, Università degli Studi di Salerno, via <PERSON> 132, 84084 Fisciano (SA), Italy"}, {"AuthorId": 4, "Name": "Rosalba Zizza", "Affiliation": "Dipartimento di Informatica, Università degli Studi di Salerno, via <PERSON> 132, 84084 Fisciano (SA), Italy"}], "References": [{"Title": "ω-Lyndon words", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "809", "Issue": "", "Page": "39", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Generalized Lyndon factorizations of infinite words", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "809", "Issue": "", "Page": "30", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 84859418, "Title": "Evaluating Lumbar Shape Deformation With Fabric Strain Sensors", "Abstract": "Objective <p>To better study human motion inside the space suit and suit-related contact, a multifactor statistical model was developed to predict torso body shape changes and lumbar motion during suited movement by using fabric strain sensors that are placed on the body.</p> Background <p>Physical interactions within pressurized space suits can pose an injury risk for astronauts during extravehicular activity (EVA). In particular, poor suit fit can result in an injury due to reduced performance capabilities and excessive body contact within the suit during movement. A wearable solution is needed to measure body motion inside the space suit.</p> Methods <p>An array of flexible strain sensors was attached to the body of 12 male study participants. The participants performed specific static lumbar postures while 3D body scans and sensor measurements were collected. A model was created to predict the body shape as a function of sensor signal and the accuracy was evaluated using holdout cross-validation.</p> Results <p>Predictions from the torso shape model had an average root mean square error (RMSE) of 2.02 cm. Subtle soft tissue deformations such as skin folding and bulges were accurately replicated in the shape prediction. Differences in posture type did not affect the prediction error.</p> Conclusion <p>This method provides a useful tool for suited testing and the information gained will drive the development of injury countermeasures and improve suit fit assessments.</p> Application <p>In addition to space suit design applications, this technique can provide a lightweight and wearable system to perform ergonomic evaluations in field assessments.</p>", "Keywords": "biomechanics;computational modeling;physical ergonomics;spine and low back", "DOI": "10.1177/0018720820965302", "PubYear": 2022, "Volume": "64", "Issue": "4", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "Linh Q. Vu", "Affiliation": "MEI Technologies, Houston, Texas, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Leidos Innovations, Houston, Texas, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Houston, Texas, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NASA Johnson Space Center, Houston, Texas, USA"}], "References": []}, {"ArticleId": 84859455, "Title": "Fault-tolerant complete visibility for asynchronous robots with lights under one-axis agreement", "Abstract": "We consider the distributed setting of N autonomous mobile robots that operate in Look-Compute-Move (LCM) cycles and communicate with other robots using colored lights following the robots with lights model. We assume obstructed visibility under which a robot cannot see another robot if a third robot is positioned between them on the straight line connecting them. We study the fundamental Complete Visibility problem of repositioning N robots, starting from N distinct points, on a plane so that each robot is visible to all others. We are interested in fault-tolerant algorithms. We study fault-tolerance with respect to failures on the mobility of robots. Therefore, any algorithm for Complete Visibility is required to provide visibility between all non-faulty robots (i.e., no three non-faulty robots are collinear and no faulty robot is between two non-faulty robots on the straight line connecting them), independently of the behavior of the faulty ones. We model mobility failures as crash faults in which each faulty robot may stop its movement at any time and, once it stopped moving, it will remain stationary indefinitely thereafter. There exists an algorithm for this problem that tolerates a single faulty robot in the semi-synchronous setting under both-axis agreement. In that algorithm, the light of the faulty robot does not need to work correctly after the robot experiences fault. In this article, we assume the model in which, even after a robot experiences fault, its light still operates correctly and provide the first algorithm for Complete Visibility that tolerates f ≤ N faulty robots in the asynchronous setting under one-axis agreement. The proposed algorithm has many interesting properties.", "Keywords": "Distributed algorithms ; Mobile robots ; Lights ; Crash faults ; Visibility ; Collisions ; Convex hull ; Obstruction", "DOI": "10.1016/j.tcs.2020.10.033", "PubYear": 2021, "Volume": "850", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Kent State University, Kent, OH 44242, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Kent State University, Kent, OH 44242, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Kent State University, Kent, OH 44242, USA;Corresponding author"}], "References": []}, {"ArticleId": 84859515, "Title": "Identifying propaganda from online social networks during COVID-19 using machine learning techniques", "Abstract": "<p>COVID-19, affected the entire world because of its non-availability of vaccine. Due to social distancing online social networks are massively used in pandemic times. Information is being shared enormously without knowing the authenticity of the source. Propaganda is one of the type of information that is shared deliberately for gaining political and religious influence. It is the systematic and deliberate way of shaping opinion and influencing thoughts of a person for achieving the desired intention of a propagandist. Various propagandistic messages are being shared during COVID-19 about the deadly virus. We extracted data from twitter using its application program interface (API), Annotation is being performed manually. Hybrid feature engineering is performed for choosing the most relevant features.The binary classification of tweets is being performed with the help of machine learning algorithms. Decision tree gives better results among all other algorithms. For better results feature engineering may be improved and deep learning can be used for classification task.</p><p>© Bharati Vidyapeeth's Institute of Computer Applications and Management 2020.</p>", "Keywords": "COVID-19;Decision tree;Machine learning;Online social networks;Propaganda", "DOI": "10.1007/s41870-020-00550-5", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Baba Ghulam Shah Badshah University, Rajouri, 185234 Jammu and Kashmir India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Baba Ghulam Shah Badshah University, Rajouri, 185234 Jammu and Kashmir India."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Sciences, Baba Ghulam Shah Badshah University, Rajouri, 185234 Jammu and Kashmir India."}], "References": [{"Title": "Diagnosis of diabetes type-II using hybrid machine learning based ensemble model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "2", "Page": "419", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Text classification algorithms for mining unstructured data: a SWOT analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1159", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Machine learning based approaches for detecting COVID-19 using clinical text data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "3", "Page": "731", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 84859579, "Title": "Parallel outlier detection in real time data streams", "Abstract": "Outlier detection is one of the major problems in modern applications. Specially, detecting outliers for streaming applications, as data can dynamically change in subtle ways following changes in the underlying infrastructure. Due to the evolution in data in ratio of data generated every second and velocity, detecting outliers in these types of data becomes a very challenging task. This makes processing the whole data one time is impossible. In this paper we propose a parallel window based local outlier detection (PWLOD) algorithm that can detect outliers in real time using the sliding window algorithm and partition each window among several processing nodes. Each processing node process its portion of window using Local Outlier Factor algorithm and send the results to the master node which collects the results and process them to select the outliers. The experimental results show that the proposed algorithm has better execution time and accuracy than the state-of-the-art algorithms. © 2020 NSP Natural S©ien©es Publishing Cor.", "Keywords": "Data streams; Outlier detection; Parallel processing", "DOI": "10.18576/isl/090308", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 84859907, "Title": "Error estimation and adaptivity for PGD based on complementary solutions applied to a simple 1D problem", "Abstract": "Abstract \nReduced order methods are powerful tools for the design and analysis of sophisticated systems, reducing computational costs and speeding up the development process. Among these reduced order methods, the Proper Generalized Decomposition is a well-established one, commonly used to deal with multi-dimensional problems that often suffer from the curse of dimensionality . Although the PGD method has been around for some time now, it still lacks mechanisms to assess the quality of the solutions obtained. This paper explores the dual error analysis in the scope of the PGD, using complementary solutions to compute error bounds and drive an adaptivity process, applied to a simple 1D problem. The energy of the error obtained from the dual analysis is used to determine the quality of the PGD approximations. We define a new adaptivity indicator based on the energy of the error and use it to drive parametric h- and p- adaptivity processes. The results are positive, with the indicator accurately capturing the parameter that will lead to lowest errors.", "Keywords": "Error bounds;Error estimation;Proper generalized decomposition;Equilibrium formulation", "DOI": "10.1186/s40323-020-00180-3", "PubYear": 2020, "Volume": "7", "Issue": "1", "JournalId": 8538, "JournalTitle": "Advanced Modeling and Simulation in Engineering Sciences", "ISSN": "", "EISSN": "2213-7467", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departamento de Engenharia Civil, Instituto Superior Técnico, Lisbon, Portugal;Laboratori de Calcul Numeric (LaCaN), E.T.S. de Ingenieros de Caminos, Canales y Puertos, Universitat Politecnica de Catalunya, Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Engenharia Civil, Instituto Superior Técnico, Lisbon, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratori de Calcul Numeric (LaCaN), E.T.S. de Ingenieros de Caminos, Canales y Puertos, Universitat Politecnica de Catalunya, Barcelona, Spain;International Centre for Numerical Methods in Engineering,CIMNE, Barcelona, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Laboratori de Calcul Numeric (LaCaN), E.T.S. de Ingenieros de Caminos, Canales y Puertos, Universitat Politecnica de Catalunya, Barcelona, Spain"}], "References": []}, {"ArticleId": 84859925, "Title": "A Guide on Solving Non-convex Consumption-Saving Models", "Abstract": "<p>Consumption-saving models with adjustment costs or discrete choices are typically hard to solve numerically due to the presence of non-convexities. This paper provides a number of tools to speed up the solution of such models. Firstly, I use that many consumption models have a nesting structure implying that the continuation value can be efficiently pre-computed and the consumption choice solved separately before the remaining choices. Secondly, I use that an endogenous grid method extended with an upper envelope step can be used to solve efficiently for the consumption choice. Thirdly, I use that the required pre-computations can be optimized by a novel loop reordering when interpolating the next-period value function. As an illustrative example, I solve a model with non-durable consumption and durable consumption subject to adjustment costs. Combining the provided tools, the model is solved almost 50 times faster than with standard value function iteration for a given level of accuracy. Software is provided in both Python and C++.</p>", "Keywords": "Endogenous grid method; Post-decision states; Stochastic dynamic programming; Continuous and discrete choices; Occasionally binding constraints; C6; D91; E21", "DOI": "10.1007/s10614-020-10045-x", "PubYear": 2021, "Volume": "58", "Issue": "3", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CEBI, Department of Economics, University of Copenhagen, Copenhagen K, Denmark"}], "References": []}, {"ArticleId": 84860031, "Title": "Deep Learning Systems: Algorithms, Compilers, and Processors for Large-Scale Production", "Abstract": "This book describes deep learning systems: the algorithms, compilers, and processor components to efficiently train and deploy deep learning models for commercial applications. The exponential growth in computational power is slowing at a time when the amount of compute consumed by state-of-the-art deep learning (DL) workloads is rapidly growing. Model size, serving latency, and power constraints are a significant challenge in the deployment of DL models for many applications. Therefore, it is imperative to codesign algorithms, compilers, and hardware to accelerate advances in this field with holistic system-level and algorithm solutions that improve performance, power, and efficiency. Advancing DL systems generally involves three types of engineers: (1) data scientists that utilize and develop DL algorithms in partnership with domain experts, such as medical, economic, or climate scientists; (2) hardware designers that develop specialized hardware to accelerate the components in the DL models; and (3) performance and compiler engineers that optimize software to run more efficiently on a given hardware. Hardware engineers should be aware of the characteristics and components of production and academic models likely to be adopted by industry to guide design decisions impacting future hardware. Data scientists should be aware of deployment platform constraints when designing models. Performance engineers should support optimizations across diverse models, libraries, and hardware targets. The purpose of this book is to provide a solid understanding of (1) the design, training, and applications of DL algorithms in industry; (2) the compiler techniques to map deep learning code to hardware targets; and (3) the critical hardware features that accelerate DL systems. This book aims to facilitate co-innovation for the advancement of DL systems. It is written for engineers working in one or more of these areas who seek to understand the entire system stack in order to better collaborate with engineers working in other parts of the system stack. The book details advancements and adoption of DL models in industry, explains the training and deployment process, describes the essential hardware architectural features needed for today's and future models, and details advances in DL compilers to efficiently execute algorithms across various hardware targets. Unique in this book is the holistic exposition of the entire DL system stack, the emphasis on commercial applications, and the practical techniques to design models and accelerate their performance. The author is fortunate to work with hardware, software, data scientist, and research teams across many high-technology companies with hyperscale data centers. These companies employ many of the examples and methods provided throughout the book.", "Keywords": "deep learning; machine learning; artificial intelligence; distributed training systems; inference; accelerators; processors; architectures; compilers; optimizations", "DOI": "10.2200/S01046ED1V01Y202009CAC053", "PubYear": 2020, "Volume": "15", "Issue": "4", "JournalId": 16575, "JournalTitle": "Synthesis Lectures on Computer Architecture", "ISSN": "1935-3235", "EISSN": "1935-3243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Intel"}], "References": []}, {"ArticleId": 84860121, "Title": "Human motion tracking under indoor and outdoor surveillance system", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICA.2020.10033245", "PubYear": 2020, "Volume": "11", "Issue": "4", "JournalId": 23927, "JournalTitle": "International Journal of Innovative Computing and Applications", "ISSN": "1751-648X", "EISSN": "1751-6498", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84860163, "Title": "Evaluation of Push and Pull Communication Models on a VANET with Virtual Traffic Lights", "Abstract": "<p>It is expected in a near future that safety applications based on vehicle-to-everything communications will be a common reality in the traffic roads. This technology will contribute to improve the safety of vulnerable road users, for example, with the use of virtual traffic light systems (VTLS) in the intersections. This work implements and evaluates a VTLS conceived to help the pedestrians pass safely the intersections without real traffic lights. The simulated VTLS scenario used two distinct communication paradigms—the pull and push communication models. The pull model was implemented in named data networking (NDN), because NDN uses natively a pull-based communication model, where consumers send requests to pull the contents from the provider. A distinct approach is followed by the push-based model, where consumers subscribe previously the information, and then the producers distribute the available information to those consumers. Comparing the performance of the push and pull models on a VANET with VTLS, it is observed that the push mode presents lower packet loss and generates fewer packets, and consequently occupies less bandwidth, than the pull mode. In fact, for the considered metrics, the VTLS implemented with the pull mode presents no advantage when compared with the push mode.</p>", "Keywords": "virtual traffic lights; vulnerable road user; vehicular named data networking; NDN; vehicular ad hoc networking; VANET virtual traffic lights ; vulnerable road user ; vehicular named data networking ; NDN ; vehicular ad hoc networking ; VANET", "DOI": "10.3390/info11110510", "PubYear": 2020, "Volume": "11", "Issue": "11", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal ↑ Authors to whom correspondence should be addressed"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal ↑ Authors to whom correspondence should be addressed"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal ↑ Authors to whom correspondence should be addressed"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal ↑ Authors to whom correspondence should be addressed"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal ↑ Authors to whom correspondence should be addressed"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal ↑ Authors to whom correspondence should be addressed"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Algoritmi Center, University of Minho, 4710-057 Braga, Portugal ↑ Authors to whom correspondence should be addressed"}], "References": []}, {"ArticleId": 84860194, "Title": "Reconstructing 1-km-resolution high-quality PM2.5 data records from 2000 to 2018 in China: spatiotemporal variations and policy implications", "Abstract": "Exposure to fine particulate matter (PM<sub>2.5</sub>) can significantly harm human health and increase the risk of death. Satellite remote sensing allows for generating spatially continuous PM<sub>2.5</sub> data, but current datasets have overall low accuracies with coarse spatial resolutions limited by data sources and models. Air pollution levels in China have experienced dramatic changes over the past couple of decades. However, country-wide ground-based PM<sub>2.5</sub> records only date back to 2013. To reveal the spatiotemporal variations of PM<sub>2.5</sub>, long-term and high-spatial-resolution aerosol optical depths, generated by the Moderate Resolution Imaging Spectroradiometer (MODIS) Multi-Angle implementation of Atmospheric Correction (MAIAC) algorithm, were employed to estimate PM<sub>2.5</sub> concentrations at a 1 km resolution using our proposed Space-Time Extra-Trees (STET) model. Our model can capture well variations in PM<sub>2.5</sub> concentrations at different spatiotemporal scales, with higher accuracies (i.e., cross-validation coefficient of determination, CV-R<sup>2</sup> = 0.86–0.90) and stronger predictive powers (i.e., R<sup>2</sup> = 0.80–0.82) than previously reported. The resulting PM<sub>2.5</sub> dataset for China (i.e., ChinaHighPM<sub>2.5</sub>) provides the longest record (i.e., 2000 to 2018) at a high spatial resolution of 1 km, enabling the study of PM<sub>2.5</sub> variation patterns at different scales. In most places, PM<sub>2.5</sub> concentrations showed increasing trends around 2007 and remained high until 2013, after which they declined substantially, thanks to a series of government actions combating air pollution in China. While nationwide PM<sub>2.5</sub> concentrations have decreased by 0.89 μg/m<sup>3</sup>/yr ( p < 0.001) during the last two decades, the reduction has accelerated to 4.08 μg/m<sup>3</sup>/yr ( p < 0.001) over the last six years, indicating a significant improvement in air quality. Large improvements occurred in the Pearl and Yangtze River Deltas, while the most polluted region remained the North China Plain, especially in winter. The ChinaHighPM<sub>2.5</sub> dataset will enable more insightful analyses regarding the causes and attribution of pollution over medium- or small-scale areas.", "Keywords": "PM<sub>2.5</sub> ; MODIS ; Space-Time Extra-Trees model ; ChinaHighPM<sub>2.5</sub> ; 1 km resolution", "DOI": "10.1016/j.rse.2020.112136", "PubYear": 2021, "Volume": "252", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, College of Global Change and Earth System Science, Beijing Normal University, Beijing, China;Department of Atmospheric and Oceanic Science, Earth System Science Interdisciplinary Center, University of Maryland, College Park, MD, USA"}, {"AuthorId": 2, "Name": "Zhanqing Li", "Affiliation": "Department of Atmospheric and Oceanic Science, Earth System Science Interdisciplinary Center, University of Maryland, College Park, MD, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory for Atmospheres, NASA Goddard Space Flight Center, Greenbelt, MD, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Geodesy and Geomatics, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Ministry of Education Key Laboratory for Earth System Modeling, Department of Earth System Science, Tsinghua University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, College of Global Change and Earth System Science, Beijing Normal University, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Atmospheric and Oceanic Science, Earth System Science Interdisciplinary Center, University of Maryland, College Park, MD, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Atmospheric and Oceanic Science, Earth System Science Interdisciplinary Center, University of Maryland, College Park, MD, USA"}], "References": [{"Title": "A new method to retrieve the diurnal variability of planetary boundary layer height from lidar under different thermodynamic stability conditions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111519", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Refining aerosol optical depth retrievals over land by constructing the relationship of spectral surface reflectances through deep learning: Application to Himawari-8", "Authors": "<PERSON><PERSON><PERSON> Su; <PERSON><PERSON><PERSON>; Zhanqing Li", "PubYear": 2020, "Volume": "251", "Issue": "", "Page": "112093", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 84860275, "Title": "A genetic algorithm for supplier selection problem under collaboration opportunities", "Abstract": "In this paper, we propose a collaborative model for the supplier selection for the purchasing activity in the supply chain. The problem addresses a set of firms that try to look for a cost saving configuration to optimise their ordering plans, given a set of suppliers with quantity discounts options. Possible collaborations between firms, modelled as a coalition formation, can be beneficial in the sense that the gathering of their orders generates a cost minimisation regarding the stand-alone situation. We propose the mathematical formulation of firms’ collaborative ordering modelled as a cost-dependent assignment problem. The collaborative scenario is viewed as a two independent steps: the first step is based on game-theoretic approach to model possible coalitions of firms and to generate stable coalition structures according to the core concept. Once coalitions are formed, the second step consists mainly on the genetic algorithm is trigged to assign coalitions to shared suppliers. The assignment problem is solved using a specifically designed hybrid genetic algorithm. Experiments, driven on a large test-bed, highlight the effectiveness of the collaboration in handling the ordering activity within the supply chain and the usefulness of hybrid genetic algorithm in solving such supplier selection problems. We show that in all cases the collaborative scenario is more profitable regarding the stand-alone position. The obtained results show that the hybrid genetic algorithm is able to generate good quality solutions in a reasonable run time regarding Cplex results.", "Keywords": "Supplier selection ; genetic algorithm ; assignment problem ; quantity discount ; coalition formation", "DOI": "10.1080/0952813X.2020.1836031", "PubYear": 2022, "Volume": "34", "Issue": "1", "JournalId": 23221, "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence", "ISSN": "0952-813X", "EISSN": "1362-3079", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "LARODEC Laboratory,  Université de Tunis, Institut Supérieur de Gestion de Tunis , Tunis, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LARODEC Laboratory,  Université de Tunis, Institut Supérieur de Gestion de Tunis , Tunis, Tunisia"}], "References": []}, {"ArticleId": 84860315, "Title": "Management of Information Risks for Complex System Using the «Cognitive Game» Mechanism", "Abstract": "Purpose of the article: development of mechanisms for solving problems of information risk management of complex systems in conditions of uncertainty and mutual influence of system elements on each other. Research method: game-theoretic mathematical modeling of risk management processes in complex systems based on arbitration schemes and multistep games on cognitive maps. The result: a general model of a complex system (for example, a heterogeneous computer network) is considered, within which the risk manager (risk-manager) carries out effective risk management by distributing the resource at his disposal among its elements (nodes of a computer network). To assess the state of the system elements, functions of local risk are proposed that satisfy certain specified requirements, and to assess the state of the system as a whole, an integral risk function is proposed. It is shown that in the case of independence (absence of mutual influence on each other) of the system elements to find an effective resource allocation, a game-theoretic approach can be used based on an arbitration scheme based on the principles of stimulation and non-suppression (MS-solution). For the case when changes in the level of risk for one element of the system can have a significant impact on the levels of risks of other elements, it is proposed to use game-theoretic models based on the MS-solution and a multistep “cognitive game”.", "Keywords": "", "DOI": "10.21681/2311-3456-2020-04-2-10", "PubYear": 2020, "Volume": "", "Issue": "4(38)", "JournalId": 48575, "JournalTitle": "Vop<PERSON><PERSON>", "ISSN": "2311-3456", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Control Sciences of the Russian Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Control Sciences of the Russian Academy of Sciences"}], "References": []}, {"ArticleId": 84860318, "Title": "Automation of Software Vulnerabilities Analysis on the Basis of Text Mining Technology", "Abstract": "Purpose: the development of automated system of software vulnerabilities analysis for information-control systems on the basis of intelligent analysis of texts written on the natural language (Text Mining). Methods: the idea of the used investigation method is based on matching the set of extracted software vulnerabilities and relevant information security threats by means of evaluating the semantic similarity metrics of their textual description with use of Text Mining methods. Practical relevance: the architecture of the automated system of software vulnerabilities analysis is developed, the application of which allows us to evaluate the level of vulnerabilities criticality and match it with the most suitable by discretion (i.e. semantically similar) threats from the Bank of information security threats of FSTEC Russia while ensuring vulnerabilities and threats. The main software modules of the system have been developed. Computational experiments were carried out to assess the effectiveness of its application. The results of comparative analysis show that application of the given system allows us to increase the credibility of evaluating the criticality degree of vulnerabilities, considerably decreasing the time for a search and matching vulnerabilities and threats.", "Keywords": "", "DOI": "10.21681/2311-3456-2020-04-22-31", "PubYear": 2020, "Volume": "", "Issue": "4(38)", "JournalId": 48575, "JournalTitle": "Vop<PERSON><PERSON>", "ISSN": "2311-3456", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal State Budgetary Educational Institution of Higher Education \""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ufa State Aviation Technical University\""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal State Budgetary Educational Institution of Higher Education \"Ufa State Aviation Technical University\""}], "References": []}, {"ArticleId": 84860319, "Title": "Assessment of the Impact of General Data Protection Regulation on Enterprise Security in the Russian Federation", "Abstract": "Abstract The purpose of the study is to analyze the existing requirements for personal data security and assess the impact of these requirements on the enterprises security in the Russian Federation. Research method: the problem of ensuring the security of personal data in accordance with the requirements of the Federal law of the Russian Federation FZ-152 and the international General Data Protection Regulation is investigated. The article analyzes the possible risks of interrupting the normal activities of enterprises in the Russian Federation due to violations of these requirements for personal data protection and the imposition of significant fines by international regulators. Numerical relationships are estimated between the amount of fines for violations of established requirements, including General Data Protection Regulation, and the cost of creating an effectiveness personal data protection system. Estimates of the permissible degree of influence of the General Data Protection Regulation requirements on the enterprises security in the Russian Federation are obtained. Research result: a study and comparison of possible penalties for violation of compliance with the requirements of the Federal law of the Russian Federation FZ-152 and the international General Data Protection Regulation was performed. Risk assessments of sanctions for violation of the established requirements for personal data protection were obtained. The analysis of the cost of preparing a personal data protection system for compliance with the requirements of the General Data Protection Regulation was performed. Based on the data obtained, examples of calculating the degree of maturity of the security system are presented – based on the ratio of the share of the budget allocated for security in relation to the cost of creating an effectiveness personal data protection system and based on the ratio of the amount of the fine for violation of the established requirements. The importance of accounting for the costs of personal data security to ensure the security of enterprises in the Russian Federation, taking into account the requirements of the General Data Protection Regulation, is shown", "Keywords": "", "DOI": "10.21681/2311-3456-2020-04-66-75", "PubYear": 2020, "Volume": "", "Issue": "4(38)", "JournalId": 48575, "JournalTitle": "Vop<PERSON><PERSON>", "ISSN": "2311-3456", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Saint-Petersburg National Research University of Information Technologies, Mechanics and Optics"}], "References": []}, {"ArticleId": ********, "Title": "Application of Deep Learning Methods in Cybersecurity Tasks. Part 2", "Abstract": "The purpose of the article: comparative analysis of methods for solving various cybersecurity problems based on the use of deep learning algorithms. Research method: Systematic analysis of modern methods of deep learning in various cybersecurity applications, including intrusion and malware detection, network traffic analysis, and some other tasks. The result obtained: classification scheme of the considered approaches to deep learning in cybersecurity, and their comparative characteristics by the used models, characteristics, and data sets. The analysis showed that many deeper architectures with a large number of neurons on each layer show better results. Recommendations are given for using deep learning methods in cybersecurity applications. The main contribution of the authors to the research of deep learning methods for cybersecurity tasks is the classification of the subject area; conducting a general and comparative analysis of existing approaches that reflect the current state of scientific research.", "Keywords": "", "DOI": "10.21681/2311-3456-2020-04-11-21", "PubYear": 2020, "Volume": "", "Issue": "4(38)", "JournalId": 48575, "JournalTitle": "Vop<PERSON><PERSON>", "ISSN": "2311-3456", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The St. Petersburg Institute for Informatics and Automation of the Russian Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The St. Petersburg Institute for Informatics and Automation of the Russian Academy of Sciences"}], "References": []}, {"ArticleId": 84860369, "Title": "Neural Cryptographic Information Security System of Recurrent Convergent Neural Networks", "Abstract": "Abstract. The purpose: to construct an algorithm for information transformation by recurrent convergent neural networks with a given set of local minima of the energy functional for its subsequent application in the field of information security. Method: system analysis of the existing neural network paradigms that can be used for classification of images. Neural cryptographic system synthesis is with analogy methods, recurrent convergent neural networks, noise- resistant encoding and block ciphers algorithms. The result: a promising neural cryptographic system is proposed that can be used to develop an algorithm for noise-resistant coding, symmetric or stream data encryption based on the generation of various variants of the distorted image representing the sequence of bits to mask the original message. An algorithm for block symmetric data encryption based on Hopfield-type neural networks has been created. Key information includes information on the selected (using radial basic functions) structural characteristics of the potential with a given set of energy minima, which determines the dynamics of the neural network as a potential dynamic system, whose attractors are symbols (several symbols) of the alphabet of the input text. The size of the key depends on the power of the alphabet of the original message and the form of representation of the energy functional. The presented neural cryptographic system can also be used in the authentication system.", "Keywords": "", "DOI": "10.21681/2311-3456-2020-04-44-55", "PubYear": 2020, "Volume": "", "Issue": "4(38)", "JournalId": 48575, "JournalTitle": "Vop<PERSON><PERSON>", "ISSN": "2311-3456", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Krasnodar Higher Military University of name General of Army S<PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": 84860376, "Title": "Methodology for Analyzing Vulnerabilities and Determining the Security Level of a Smart Contract When Placed in Distributed Ledger Systems", "Abstract": "Every year, the technology of using smart contracts is attracting more and more attention from users due to the unique advantages that it possesses: automatic execution of transactions in a traceable and unchanging way without third party authorization. At the same time, a smart contract is one of the most vulnerable elements in distributed ledger systems, which can be susceptible to attack by intruders. The aim of the research is to develop a methodology that allows analyzing a smart contract for information security vulnerabilities and determining the security level of a smart contract before placing it in distributed ledger systems. Research methods: to achieve this goal, methods of static and dynamic analysis were studied, the most relevant information security vulnerabilities were identified, and parameters for calculating the criticality factor of vulnerability and the security level of a smart contract were determined. Result: a promising static-dynamic method for analyzing the vulnerabilities of a smart contract is proposed, which makes it possible to unambiguously determine the security level of a smart contract before its placement in the distributed ledger system. Its main parameters are set, and the reference security factors of a smart contract are determined. The complete algorithm of the static-dynamic method of analyzing a smart contract is described, and an example of a generated documentary security report based on the results of analyzing a smart contract is given.", "Keywords": "", "DOI": "10.21681/2311-3456-2020-04-56-65", "PubYear": 2020, "Volume": "", "Issue": "4(38)", "JournalId": 48575, "JournalTitle": "Vop<PERSON><PERSON>", "ISSN": "2311-3456", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Moscow Polytechnic University"}, {"AuthorId": 2, "Name": "<PERSON> Repin", "Affiliation": "<PERSON><PERSON> \"Voskhod\""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Moscow Polytechnic University"}], "References": []}, {"ArticleId": 84860377, "Title": "Identifying the Significant Features in Illegal Texts", "Abstract": "The purpose of the study: development of a technique for determining lexical characteristics and psycholinguistic factors as discriminative features for identifying the topics of illegal texts by frequency methods for information security purposes. Method: automatic morphological and syntactic analysis, frequency methods, comparison of auto-generated dictionaries by correlation analysis methods. Results: a technique of frequency analysis of the illegal texts vocabulary has been developed, which allows to compare different sets of texts using frequency dictionaries and identify discriminative features; a technique of calculating pairwise rank correlation coefficient for comparison of frequency dictionaries of various lexical characteristics has been presented; a comparative analysis of different illegal texts collections has been carried out; the possibility of using frequency lexical characteristics to study the properties of texts in order to detect illegal resources and messages has been shown; the possibilities of using both morphological characteristics of words and word combinations and letter combinations as discriminative features have been shown; the possibility of calculating the psycholinguistic indicators of illegal texts based on automatic linguistic text analysis has been shown; the psycholinguistic characteristics for texts of various topics have been highlighted.", "Keywords": "", "DOI": "10.21681/2311-3456-2020-04-76-84", "PubYear": 2020, "Volume": "", "Issue": "4(38)", "JournalId": 48575, "JournalTitle": "Vop<PERSON><PERSON>", "ISSN": "2311-3456", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Higher School of Economics"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Federal Research Center “Informatics and Management”"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bauman Moscow State Technical University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Peoples' Friendship University of Russia"}], "References": []}, {"ArticleId": 84860412, "Title": "Staying in a hotel or peer-to-peer accommodation sharing? A discrete choice experiment with online reviews and discount strategies", "Abstract": "Purpose This study aims to elicit the preferences of potential travelers for different property listings' attributes (online review number, positive valence rate of reviews and discount strategy) when selecting hotels and peer-to-peer (P2P) accommodation sharing on online booking platforms. Design/methodology/approach A discrete choice experiment (DCE) was conducted with 291 respondents with accommodation needs. They were asked to choose between pairs of listings. Findings The authors found that when booking accommodation online, complex discount strategies were not determinant both in selecting hotels and P2P accommodations. Positive valence rate of reviews has a higher impact on the selection of traditional hotels than P2P accommodations, while the number of online reviews has a higher impact on the selection of P2P accommodations than traditional hotels. The authors further discuss the effect of each attribute on online accommodation selection in terms of price ranges of the property listings. Research limitations/implications The findings provide suggestions for platform operators and product/service providers to improve their marketing strategies and optimize their management efforts. Originality/value To the best of the authors’ knowledge, this is one of the first studies that investigate the role of property listings' attributes on the selections between hotels and P2P accommodations. The findings from this research study could be generalized to other online platforms and electronic commerce-related transactions.", "Keywords": "Choice experiment;Online review;Discount strategy;Online accommodation platforms;Peer-to-peer accommodation sharing", "DOI": "10.1108/INTR-01-2020-0031", "PubYear": 2020, "Volume": "31", "Issue": "2", "JournalId": 20482, "JournalTitle": "Internet Research", "ISSN": "1066-2243", "EISSN": "2054-5657", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing International Studies University , Beijing, China Research Center of Beijing Tourism Development , Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Renmin University of China , Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of International Business and Economics , Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Florida Atlantic University , Boca Raton, Florida, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Central Florida , Orlando, Florida, USA"}], "References": [{"Title": "Designing Embodied Virtual Agents as Product Specialists in a Multi-Product Category E-Commerce: The Roles of Source Credibility and Social Presence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "12", "Page": "1136", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 84860677, "Title": "AR Arbeitsplätze", "Abstract": "<p>T<PERSON>z fortschreitender Automatisierung bleiben manuelle Tätigkeiten ein wichtiger Baustein der Fertigung kundenindividueller Produkte. Um die Mitarbeiter(innen) zu unterstützen und um eine effiziente Arbeit zu ermöglichen, werden zunehmend auf Augmented Reality (AR) basierende Systeme eingesetzt. Die vorgestellte Arbeit konzentriert sich auf die Entwicklung ganzheitlicher AR-Arbeitsplätze für den Einsatz in kleinen und mittleren  Unternehmen (KMU). Das entwickelte AR- Handarbeitskonzept beinhaltet eine Just-in-time-Darstellung der Arbeitsaufgaben auf Werkstücken mit automatisierter Fertigungskontrolle. AlsReaktion auf kurze Produktlebenszyklen und hohe Produktvielfalten sind alle Komponenten auf maximale Flexibilität ausgelegt. Ein Umrüsten auf neue Produkte kann innerhalb von Minuten erfolgen.</p>", "Keywords": "Augmented Reality / Industrie 4.0 / Flexible Arbeitsplätze", "DOI": "10.17560/atp.v62i10.2495", "PubYear": 2020, "Volume": "62", "Issue": "10", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "FH Aachen"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "FH Aachen"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FH Aachen"}], "References": []}, {"ArticleId": 84860760, "Title": "Hyperspectral image super-resolution through clustering-based sparse representation", "Abstract": "<p>Promoting the spatial resolution of hyperspectral sensors is expected to improve computer vision tasks. However, due to the physical limitations of imaging sensors, the hyperspectral image is often of low spatial resolution. In this paper, we propose a new hyperspectral image super-resolution method from a low-resolution (LR) hyperspectral image and a high resolution (HR) multispectral image of the same scene. The reconstruction of HR hyperspectral image is formulated as a joint estimation of the hyperspectral dictionary and the sparse codes based on the spatial-spectral sparsity of the hyperspectral image. The hyperspectral dictionary is learned from the LR hyperspectral image. The sparse codes with respect to the learned dictionary are estimated from LR hyperspectral image and the corresponding HR multispectral image. To improve the accuracy, both spectral dictionary learning and sparse coefficients estimation exploit the spatial correlation of the HR hyperspectral image. Experiments show that the proposed method outperforms several state-of-art hyperspectral image super-resolution methods in objective quality metrics and visual performance.</p>", "Keywords": "Hyperspectral imaging; Sparse representation; Structural prior", "DOI": "10.1007/s11042-020-09952-w", "PubYear": 2021, "Volume": "80", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "McConnell Brain Imaging Centre, Montreal Neurological Institute, McGill University, Montreal, Canada"}], "References": []}, {"ArticleId": 84860982, "Title": "Developing machine-learning regression model with Logical Analysis of Data (LAD)", "Abstract": "This paper proposes a regression model based on Logical Analysis of Data (LAD). LAD is known as a combinatorial Boolean supervised data mining technique for pattern generation. It is used mainly for classification problems, and has demonstrated high accuracy compared to other classification techniques. In this paper, we extend the use of LAD to deal with supervised data with continuous responses. We derive a LAD regression model (LADR). Three discretization methods that transform the values of the response into a set of thresholds are tested. At each threshold, LAD analyzes the data as a two-class classification problem and extracts the prescriptive patterns for each class. LADR regression uses the generated patterns from the original data by using cbmLAD software to fit a numerical continuous dependent response. Therefore, a normalized regression model with only binary independent variables is obtained. LADR has been applied for six datasets and obtains better results compared with the linear regression (LR), support vector regression (SVR), Decision Tree Regression (DTR), Random Forest (RF), and Polynomial Regression (PolyR). The performance is evaluated by the Mean Square Error (MSE), Coefficient of Determination ( R 2 ), and Mean Absolute Error (MAE) based on a 10-fold cross validation.", "Keywords": "Regression techniques ; Logical analysis of data (LAD) ; LADR regression ; Discretization methods ; Combinatorial regression (CR)", "DOI": "10.1016/j.cie.2020.106947", "PubYear": 2021, "Volume": "151", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Industrial Engineering, École Polytechnique de Montréal, Montréal, Québec, Canada H3T 1J4;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Industrial Engineering, École Polytechnique de Montréal, Montréal, Québec, Canada H3T 1J4"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Industrial Engineering, École Polytechnique de Montréal, Montréal, Québec, Canada H3T 1J4"}], "References": []}, {"ArticleId": 84860992, "Title": "OKIoT: Trade off analysis of smart speaker architecture on open knowledge IoT project", "Abstract": "Successful deployment of smart environments based on Internet of Things (IoT) technologies face the challenges of constrained devices. Processing, bandwidth, and memory limitations must be considered when designing IoT systems to meet non-functional requirements. Software Engineering methods such as trade-off analysis could enhance the quality of IoT architectures at the design phase and bring benefits to end-users. These methods were applied to IoT projects specification during a short-term course (2019). Smart home capstone projects (2017, 2019) built with the proposed rationale are available on a public repository. Basic smart speaker services API supported qualitative and quantitative analysis of Brazilian Portuguese speech audios, smart speaker architecture Petri Net and queue models simulations on PIPE, and JMT tools enabled architecture trade-off analysis. Security vs. performance and accuracy vs. response time results provided insights for an optimized smart speaker open architecture.", "Keywords": "Education ; Security at design ; Smart home ; Smart speaker ; Simulation ; Petri net ; Queue model", "DOI": "10.1016/j.iot.2020.100310", "PubYear": 2020, "Volume": "12", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Polytechnic School, University of São Paulo, <PERSON>, São Paulo, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Polytechnic School, University of São Paulo, Luciano <PERSON>, São Paulo, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Polytechnic School, University of São Paulo, Luciano <PERSON>, São Paulo, Brazil"}], "References": [{"Title": "A survey of edge computing-based designs for IoT security", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "195", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Intelligent personal assistants: A systematic literature review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "113193", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Identifying the attack surface for IoT network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "", "Page": "100162", "JournalTitle": "Internet of Things"}, {"Title": "FogIoHT: A weighted majority game theory based energy-efficient delay-sensitive fog network for internet of health things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100181", "JournalTitle": "Internet of Things"}, {"Title": "A mapping of IoT user-centric privacy preserving approaches to the GDPR", "Authors": "<PERSON>; Georgia <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100179", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 84861023, "Title": "Finite-horizon H∞ state estimation for time-varying complex networks based on the outputs of partial nodes", "Abstract": "In this paper, the partial-nodes-based resilient filtering problem for a class of discrete time-varying complex networks is investigated. In order to reduce the effect of imprecision of filter parameters on estimation performance, a set of resilient filters is proposed. The measurement output from all network nodes may not be available in the actual system, but only from a fraction of nodes. The state estimators are designed for the time-varying complex network based on partial nodes to make the estimation error achieve the H ∞ performance constraint over a finite horizon. By employing the completing-the-square technique and the backward recursive Riccati difference equations, the sufficient conditions for the existence of the estimator are derived. Then the gain of the estimator is calculated. Finally, a numerical example is provided to illustrate the effectiveness of the proposed method.", "Keywords": "Time-varying complex network ; resilient filter ; partial-nodes-based estimation ; H ∞ state estimation", "DOI": "10.1080/21642583.2020.1837691", "PubYear": 2021, "Volume": "9", "Issue": "sup2", "JournalId": 1195, "JournalTitle": "Systems Science & Control Engineering", "ISSN": "", "EISSN": "2164-2583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "College of Control Science and Engineering, China University of Petroleum (East China), Qingdao, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Control Science and Engineering, China University of Petroleum (East China), Qingdao, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Control Science and Engineering, China University of Petroleum (East China), Qingdao, People's Republic of China"}], "References": []}, {"ArticleId": 84861060, "Title": "Multidrone aerial surveys of penguin colonies in Antarctica", "Abstract": "<p>Speed is essential in wildlife surveys due to the dynamic movement of animals throughout their environment and potentially extreme changes in weather. In this work, we present a multirobot path-planning method for conducting aerial surveys over large areas designed to make the best use of limited flight time. Unlike current survey path-planning solutions based on geometric patterns or integer programs, we solve a series of satisfiability modulo theory instances of increasing complexity. Each instance yields a set of feasible paths at each iteration and recovers the set of shortest paths after sufficient time. We implemented our planning algorithm with a team of drones to conduct multiple photographic aerial wildlife surveys of Cape Crozier, one of the largest Adélie penguin colonies in the world containing more than 300,000 nesting pairs. Over 2 square kilometers was surveyed in about 3 hours. In contrast, previous human-piloted single-drone surveys of the same colony required over 2 days to complete. Our method reduces survey time by limiting redundant travel while also allowing for safe recall of the drones at any time during the survey. Our approach can be applied to other domains, such as wildfire surveys in high-risk weather conditions or disaster response.</p>", "Keywords": "", "DOI": "10.1126/scirobotics.abc3000", "PubYear": 2020, "Volume": "5", "Issue": "47", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Stanford University, Stanford, CA, USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Point Blue Conservation Science, Petaluma, CA, USA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Point Blue Conservation Science, Petaluma, CA, USA."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Aeronautics and Astronautics, Stanford University, Stanford, CA, USA."}], "References": [{"Title": "An informative path planning framework for UAV-based terrain monitoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "6", "Page": "889", "JournalTitle": "Autonomous Robots"}]}, {"ArticleId": 84861213, "Title": "Using 360˚ Videos for Teaching Volleyball Skills to Primary School Students", "Abstract": "The study presents the results from a pilot project in which 360˚ videos were utilized for teaching students basic volleyball skills. The target group was thirty-six, eleven-to-twelve years-old primary school students, divided into two groups: the first was taught conventionally and the second using apps in which 360˚ videos were embedded. The project lasted for six two-teaching-hour sessions (three for each method), and data were collected using observation sheets and a questionnaire for recording students’ views regarding their experience. The data analysis confirmed that, compared with conventional teaching, 360˚ videos allowed students to perform better the skills that were examined. The participating students also thought that by viewing 360˚ videos they were more motivated to learn and that their experience was an enjoyable one. On the other hand, students did not consider the 360˚ videos as being useful in their learning. Given the lack of research in this field, the findings provide an initial indication of 360˚ videos’ potential in Physical Education. Then again, the results also point to the need of finding more innovative methods for integrating 360˚ videos in everyday teaching.", "Keywords": "", "DOI": "10.32591/coas.ojit.0301.03021p", "PubYear": 2020, "Volume": "3", "Issue": "1", "JournalId": 58953, "JournalTitle": "Open Journal for Information Technology", "ISSN": "", "EISSN": "2620-0627", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84861237, "Title": "Integrating bloxberg's Proof of Existence Service With MATLAB", "Abstract": "Proof of Existence as a blockchain service has first been published in 2013 as a public notary service on the Bitcoin network and can be used to verify the existence of a particular file in a specific point of time without sharing the file or its content itself. This service is also available on the Ethereum based bloxberg network, a decentralized research infrastructure that is governed, operated and developed by an international consortium of research facilities. Since it is desirable to integrate the creation of this proof tightly into the research workflow, namely the acquisition and processing of research data, we show a simple to integrate MATLAB extension based solution with the concept being applicable to other programming languages and environments as well.", "Keywords": "Blockchain; Ethereum; POE; Poa; Bloxberg; DLT; Open Science", "DOI": "10.3389/fbloc.2020.546264", "PubYear": 2020, "Volume": "3", "Issue": "", "JournalId": 61970, "JournalTitle": "Frontiers in Blockchain", "ISSN": "", "EISSN": "2624-7852", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Blockchain Lab, Institute for Internet Security, Westphalian University of Applied Sciences, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Blockchain Lab, Institute for Internet Security, Westphalian University of Applied Sciences, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biopsychology, Faculty of Psychology, Ruhr University Bochum, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Digital Labs, Max Planck Digital Library, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Blockchain Lab, Institute for Internet Security, Westphalian University of Applied Sciences, Germany"}], "References": []}, {"ArticleId": 84861273, "Title": "Fusion of visible and infrared images via saliency detection using two-scale image decomposition", "Abstract": "<p>As it isn’t sufficient to inspect the scene in several applications to think about just the noticeable articles, route and object identification require distinctive imaging modalities. In this paper, we propose another picture combination technique dependent on saliency discovery and two-scale picture disintegration. This technique is gainful on the grounds that saliency-based strategies have been broadly utilized in the combination of infrared (IR) and visible (VIS) images, which can feature the notable article locale and save the point by point foundation data at the same time. Another weight map development process dependent on visual saliency is proposed. Thus, it is quick, proficient and skilful. Our strategy is evaluated on a few datasets and is assessed subjectively by visual examination and quantitatively utilizing metrics. Results that are achieved by Matlab-2019A version through the comparison of entropy, standard deviation, PSNR and SSIM values of VIS and IR image datasets of several fusion methodologies uncover that the proposed technique execution is practically identical or better than the current strategies.</p>", "Keywords": "Visible and Infrared pictures; Decomposition; Filter subtract destroy; Charge-coupled device", "DOI": "10.1007/s10772-020-09755-2", "PubYear": 2020, "Volume": "23", "Issue": "4", "JournalId": 4265, "JournalTitle": "International Journal of Speech Technology", "ISSN": "1381-2416", "EISSN": "1572-8110", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, KLEF, Vaddeswaram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, KLEF, Vaddeswaram, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, KLEF, Vaddeswaram, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, KLEF, Vaddeswaram, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, KLEF, Vaddeswaram, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, KLEF, Vaddeswaram, India"}], "References": []}, {"ArticleId": 84861304, "Title": "A non-parametric binarization method based on ensemble of clustering algorithms", "Abstract": "<p>Binarization of document images still attracts the researchers especially when degraded document images are considered. This is evident from the recent Document Image Binarization Competition (DIBCO 2019) where we can see researchers from all over the world participated in this competition. In this paper, we present a novel binarization technique which is found to be capable of handling almost all types of degradations without any parameter tuning. Present method is based on an ensemble of three classical clustering algorithms (Fuzzy C-means, K-medoids and K-means++) to group the pixels as foreground or background, after application of a coherent image normalization method. It has been tested on four publicly available datasets, used in DIBCO series, 2016, 2017, 2018 and 2019. Present method gives promising results for the aforementioned datasets. In addition, this method is the winner of DIBCO 2019 competition.</p>", "Keywords": "Binarization; Document image; Clustering; Ensemble; DB index; DIBCO", "DOI": "10.1007/s11042-020-09836-z", "PubYear": 2021, "Volume": "80", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 3, "Name": "Showmik Bhowmik", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON> Choudhury Institute of Engineering and Technology (GKCIET), Malda, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}], "References": [{"Title": "Text-line extraction from handwritten document images using GAN", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112916", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 84861336, "Title": "The Schro ̈dinger’s cat paradox in the mind creative process", "Abstract": "This article, through <PERSON><PERSON><PERSON><PERSON><PERSON>’s cat theory, points to a conflict between individuality and universality in the creative process that would appear to be an inconsistency of cognition. We chose the axiomatic-logical concept of language as a system of oppositions of places and values for this discussion, understood as a complex phenomenon mediating between the human mind and the environment. Creativity involves language and cognition and is of interest to artificial intelligence, AI, thus, an in-depth look at creative aspects of the human mind helps to better understand the structure of natural language and gives wagers for new AI challenges. There is an interdisciplinary approach, highlighting aspects of language that leave room for creativity and differentiation, paving the way for them to be adopted by artificial intelligence. We found that the broad concept of language reveals the ’way of thinking’ that unveils how creation occurs in its statu nascendi, facing the choice between opposite states to establish the new, the creative: The origin of creation is not in the elements that are part of it, but it is in the path taken by the creative process. <PERSON><PERSON><PERSON><PERSON><PERSON>’s cat experiment illustrates how the creative process takes place: Either the supposed creation will reveal a tendency to take ’probable knowledge’ as ’fact of knowledge’, or real creation breaks this perspective and settles in the subjectivity of the subject. These considerations lead us to place bets on processes that would serve to replicate creativity in artificial intelligence, making it more intuitive, removing its curse of dimensionality and reducing the need for human intervention in machine learning. © 2020 NSP Natural Sciences Publishing Cor.", "Keywords": "Artificial Intelligence; Cognition; Creative process; Interdisciplinarity; Language", "DOI": "10.18576/isl/090301", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 84861359, "Title": "The laminar boundary layer over a rotating paraboloid", "Abstract": "This work is concerned with the derivation of the steady boundary layer equations that gives the laminar flow profile over the outer surface of a paraboloid rotating in an otherwise still fluid. Also, the series solution formulation for the laminar flow equations for a rotating paraboloid is given. The series solution were numerically calculated and the laminar flow profiles are visualized in detail. Further, we showed that the formulation of the laminar flow equations for paraboloid has a mathematical flaw and this mistake led to the work of <PERSON><PERSON> D. Ver<PERSON> [1]. © 2020 NSP Natural S©ien©es Publishing Cor.", "Keywords": "Laminar flow in 3-D boundary layers; Mathematical method; Navier-Stokes equations", "DOI": "10.18576/isl/090306", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 84861393, "Title": "An efficient approximate-analytical method to solve time-fractional KdV and KdVB equations", "Abstract": "In this article, we present the modified generalized Mitta<PERSON>-<PERSON> function method (MGMLFM) as an approximateanalytical method to give a proper solution of time-fractional Korteweg-de Vries (KdV) and Kortewe<PERSON><PERSON><PERSON> V<PERSON>-<PERSON>’s (KdVB) equations, which have various applications in physics and applied mathematics. The time-fractional partial derivatives are based on Caputo sense. The obtained solution is constructed in a rapidly convergent power series. By comparing the approximate MGMLFM solutions when the fractional operator equal one with known exact solutions we have an appropriate agreement. The advantage of the article is to apply the suggested method to solve linear and nonlinear time-fractional partial differential equations, where it needs less computational effort which saves time and effort. The convergence of absolute error be controlled on by the parameters in the timefractional KdV and KdVB equations were found. The simulation of the obtained results is presented in the forms of graphs to illustrate the reliability and efficiency of our method. © 2020 NSP Natural S©ien©es Publishing Cor.", "Keywords": "Applications in the physical sciences; KdV and KdVB equations; Mittag-<PERSON> function; Series solutions; Time-fractional partial differential equations", "DOI": "10.18576/isl/090305", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 84861415, "Title": "Interpolants for linear approximation over convex Polyhedron", "Abstract": "Finite element simulation of a 3-D information, despite having enormous significance and applications, could not get its due. Because of the complexity in the formulation of the basis functions, this topic is not much researched. In this paper, we propose a simple formulation of the <PERSON><PERSON><PERSON><PERSON> coordinates over a 3-D domain, where each node is considered to be of order 3(i.e. 3 planes do intersect at each node). The concept of barycentric coordinates for a polyhedron was proposed by <PERSON><PERSON><PERSON><PERSON>(1975), but being dependent on the Exterior Triple Points (ETPs), the computation of denominator function (adjoint) was quite intricate. Inspired by the simple recursive relation proposed by <PERSON><PERSON><PERSON> (2003), and with the help of a property which is being explored in this paper that,”for a polyhedron, wedge functions corresponding to the consecutive nodes which are linear on the common face, attain the same value at the mid point of the edge joining them”, a simple recursive relation has been derived in this paper. The entire analysis has been experimented over the convex hexahedron. © 2020 NSP Natural Sciences Publishing Cor.", "Keywords": "Adjoint; Approximation; GADJ; Polyhedronal discretization; Wachspress’ coordinates; Wedge functions", "DOI": "10.18576/isl/090304", "PubYear": 2020, "Volume": "9", "Issue": "3", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 84861530, "Title": "Comprehensive Evaluation of Non-waste Cities Based on Two-Tuple Mixed Correlation Degree", "Abstract": "<p>Under the background of vigorously promoting the construction of ecological civilization, the importance of constructing the “non-waste cities” becomes increasingly prominent. Taking 39 cities in the Yangtze River Economic Zone as the research objects, this paper establishes a new evaluation index system for “non-waste cities,” and proposes a multi-source, heterogeneous and multi-attribute decision-making method for the comprehensive evaluation of “non-waste cities.” Specifically, the evaluation index system of “non-waste cities” is constructed from four aspects: economic level, environmental pollution, resource consumption, and waste utilization. Considering that there is the characteristic of multi-source heterogeneity for the attribute values (that is, real numbers, interval numbers, and fuzzy linguistic variables coexist), the multi-source heterogeneous data are uniformly converted into two-tuples, and then a new two-tuple entropy weight method is proposed to determine the weights of evaluation attributes. Moreover, combining the traditional grey relational analysis method with TOPSIS, a multi-source, heterogeneous and multi-attribute decision-making method based on two-tuple mixed correlation degree (TTMCD-MSHMADM) is proposed to evaluate the “non-waste cities,” and an empirical analysis is made for the 39 cities in the Yangtze River Economic Zone. The result gives a theoretical basis for the formulation of sustainable economic development policies in the Yangtze River Economic Zone, and provides a decision reference for selecting the demonstration cities of “non-waste cities.”</p>", "Keywords": "Comprehensive evaluation; Non-waste cities; Multi-source heterogeneous data; Two-tuple entropy weight method; Two-tuple mixed correlation degree; TTMCD-MSHMADM", "DOI": "10.1007/s40815-020-00975-x", "PubYear": 2021, "Volume": "23", "Issue": "2", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Wuhan University of Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Wuhan University of Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics, Wuhan University of Technology, Wuhan, People’s Republic of China"}], "References": [{"Title": "Diversified binary relation-based fuzzy multigranulation rough set over two universes and application to multiple attribute group decision making", "Authors": "<PERSON><PERSON> Sun; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "91", "JournalTitle": "Information Fusion"}, {"Title": "Interval-valued intuitionistic fuzzy multiple attribute decision making based on nonlinear programming methodology and TOPSIS method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "424", "JournalTitle": "Information Sciences"}, {"Title": "Design of comprehensive evaluation index system for P2P credit risk of “three rural” borrowers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "15", "Page": "11493", "JournalTitle": "Soft Computing"}, {"Title": "Evaluation Model of Industrial Operation Quality Under Multi-source Heterogeneous Data Information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "522", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Innovative design approach for product design based on TRIZ, AD, fuzzy and Grey relational analysis", "Authors": "Yanling Wu; <PERSON><PERSON>; Jizhou Kong", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106276", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Prospect theory based method for heterogeneous group decision making with hybrid truth degrees of alternative comparisons", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "106285", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A fuzzy rough number-based AHP-TOPSIS for design concept evaluation under uncertain environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106228", "JournalTitle": "Applied Soft Computing"}, {"Title": "Grey–Lotka–Volterra model for the competition and cooperation between third-party online payment systems and online banking in China", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106501", "JournalTitle": "Applied Soft Computing"}, {"Title": "Parameter optimization for nonlinear grey <PERSON> model on biomass energy consumption prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106538", "JournalTitle": "Applied Soft Computing"}, {"Title": "2-stage modified random forest model for credit risk assessment of P2P network lending to “Three Rurals” borrowers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106570", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel grey Riccati–<PERSON> model and its application for the clean energy consumption prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103863", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 84861595, "Title": "A modified firefly algorithm based on neighborhood search", "Abstract": "<p>Firefly algorithm (FA) is a new member of intelligent optimization algorithms. Several studies demonstrated that FA could successfully deal with several engineering and theoretical optimization problems. Nevertheless, there are some weak points in FA, such as parameter dependence and high computational complexity. To overcome the above issues, this paper proposes a modified FA based on neighborhood search (MFANS). In MFANS, there are three modifications. First, a modified attraction strategy is employed to reduce the complexity. Then, a neighborhood search method is used to search around the best neighborhood solutions. Third, the step parameter is dynamically adjusted. Performance of MFANS is compared with two improved FAs. Results show the effectiveness of MFANS.</p>", "Keywords": "attraction;complexity;firefly algorithm;modified attraction;neighborhood search", "DOI": "10.1002/cpe.6066", "PubYear": 2021, "Volume": "33", "Issue": "6", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Fuyang Normal University, Fuyang, China"}], "References": [{"Title": "An under‐sampled software defect prediction method based on hybrid multi‐objective cuckoo search", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Hybrid many-objective particle swarm optimization algorithm for green coal production problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "256", "JournalTitle": "Information Sciences"}, {"Title": "Improving artificial Bee colony algorithm using a new neighborhood selection mechanism", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "227", "JournalTitle": "Information Sciences"}, {"Title": "A hybrid recommendation system with many-objective evolutionary algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113648", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A new interval native Bayes uncertain fault diagnosis method based on the firefly algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "24", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Enhancing firefly algorithm with courtship learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "18", "JournalTitle": "Information Sciences"}, {"Title": "Artificial bee colony algorithm based on knowledge fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "3", "Page": "1139", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 84861735, "Title": "Cloud Operations", "Abstract": "", "Keywords": "", "DOI": "10.1365/s40702-020-00680-1", "PubYear": 2020, "Volume": "57", "Issue": "5", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "i.t-consult GmbH, Stuttgart, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84861747, "Title": "Flexibility index and design of chemical systems by cylindrical algebraic decomposition", "Abstract": "The traditional methods for the solution of flexibility index and design problems are mainly developed by numerical calculation methods. In this work, a new solution approach is proposed for chemical systems described by polynomials to deduce explicit expressions of flexibility index on the value of the continuous design variables without solving any optimization problems. First, the flexibility index and design problems are reformulated as an existential quantifier model. Then, the cylindrical algebraic decomposition (CAD) method is introduced to project the solution space onto the dimension of design variables, flexibility index and uncertain parameters. Last, the analytical expressions between design variables and flexibility index can be deduced by the inscribed hyperrectangle checking rule. The case studies show that the proposed method is applicable to relatively small- or medium- scaled problems and the explicit relationship between the flexibility index and design variables can be deduced, regardless of linear or nonlinear systems.", "Keywords": "Flexibility index ; Design optimization ; Quantifier elimination ; Cylindrical algebraic decomposition", "DOI": "10.1016/j.compchemeng.2020.107142", "PubYear": 2021, "Volume": "144", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, Hangzhou, Zhejiang, 310027, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, Hangzhou, Zhejiang, 310027, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemical Engineering, Zhejiang University of Technology, Hangzhou, Zhejiang, 310014, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, Hangzhou, Zhejiang, 310027, China;*Corresponding author"}], "References": [{"Title": "Novel MINLP formulations for flexibility analysis for measured and unmeasured uncertain parameters", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "106727", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 84861771, "Title": "Kinematic interpretation of Darboux cyclides", "Abstract": "Kinematic interpretation of quaternionic–<PERSON> curves and surfaces is revealed. This combination of kinematic and geometric modeling methods is used to characterize orbits of quadratic surfaces in the Study quadric under the kinematic map as certain Darboux cyclides. In particular, earlier unknown rational parametrizations of one-oval Darboux cyclides are found.", "Keywords": "Rational parametrization ; The Study quadric ; Kinematic mapping ; Darboux cyclide ; Quaternionic-Bézier formulas", "DOI": "10.1016/j.cagd.2020.101945", "PubYear": 2020, "Volume": "83", "Issue": "", "JournalId": 6580, "JournalTitle": "Computer Aided Geometric Design", "ISSN": "0167-8396", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Science, Faculty of Mathematics and Informatics, Vilnius University, Lithuania;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Science, Faculty of Mathematics and Informatics, Vilnius University, Lithuania"}], "References": []}, {"ArticleId": 84861816, "Title": "Talking datasets – Understanding data sensemaking behaviours", "Abstract": "The sharing and reuse of data are seen as critical to solving the most complex problems of today. Despite this potential, relatively little attention has been paid to a key step in data reuse: the behaviours involved in data-centric sensemaking. We aim to address this gap by presenting a mixed-methods study combining in-depth interviews, a think-aloud task and a screen recording analysis with 31 researchers from different disciplines as they summarised and interacted with both familiar and unfamiliar data. We use our findings to identify and detail common patterns of data-centric sensemaking across three clusters of activities that we present as a framework: inspecting data, engaging with content, and placing data within broader contexts. Additionally, we propose design recommendations for tools and documentation practices, which can be used to facilitate sensemaking and subsequent data reuse.", "Keywords": "Sensemaking ; Human computer interaction ; Human data interaction ; Data reuse ; Data sharing", "DOI": "10.1016/j.ijhcs.2020.102562", "PubYear": 2021, "Volume": "146", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "King’s College London, England;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Data Archiving and Networked Services, Royal Netherlands Academy of Arts & Sciences, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Amsterdam, NL, Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "King’s College London, England"}], "References": [{"Title": "FAIR Data Reuse – the Path through Data Citation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1-2", "Page": "78", "JournalTitle": "Data Intelligence"}, {"Title": "Dataset Reuse: Toward Translating Principles to Practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "8", "Page": "100136", "JournalTitle": "Patterns"}]}, {"ArticleId": 84861830, "Title": "Variable Rigidity Module with a Flexible Thermoelectric Device for Bidirectional Temperature Control", "Abstract": "<p>Dynamic stiffness tuning is a promising approach for shape reconfigurable systems that must adapt their flexibility in response to changing operational requirements. Among stiffness tuning technologies, phase change materials are particularly promising because they are size scalable and can be powered using portable electronics. However, the long transition time required for phase change is a great limitation for most applications. In this study, we address this by introducing a rapidly responsive variable rigidity module with a low melting point material and flexible thermoelectric device (f-TED). The f-TED can conduct bidirectional temperature control; thereby, both heating and cooling were accomplished in a single device. By performing local cooling, the phase transition time from liquid to solid is reduced by 77%. The module in its rigid state shows 14.7 × higher bending stiffness than in the soft state. The results can contribute to greatly widening the application of phase transition materials for variable rigidity.</p>", "Keywords": "active temperature control;phase change materials;thermoelectric cooler;variable stiffness", "DOI": "10.1089/soro.2020.0080", "PubYear": 2021, "Volume": "8", "Issue": "6", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Daejeon, Republic of Korea."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tegway Co. Ltd., Daejeon, Republic of Korea."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Daejeon, Republic of Korea."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Daejeon, Republic of Korea."}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Daejeon, Republic of Korea."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Daejeon, Republic of Korea."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Carnegie Mellon University, Pittsburgh, Pennsylvania, USA."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Carnegie Mellon University, Pittsburgh, Pennsylvania, USA.;Center for Healthcare Robotics, AI·Robot Institute, Korea Institute of Science and Technology (KIST), Seoul, Republic of Korea."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Korea Advanced Institute of Science and Technology (KAIST), Daejeon, Republic of Korea."}], "References": []}, {"ArticleId": 84861838, "Title": "Contact tracing", "Abstract": "As pandemic wide spread results in locking down vital facilities, digital contact tracing is deemed as a key for re-opening. However, current efforts in digital contact tracing, running as mobile apps on users' smartphones, fall short in being effective and present two major weaknesses related to accessibility and apparent privacy concern augmentation. Indeed, accessibility is affected by several factors such as smartphone penetration, age, or socio-economic conditions. The privacy concern on the other hand comes from the fear of having a piece of technology that is monitoring us all the time, everywhere, even when contact tracing is irrelevant. This paper lays out the vision and guidelines for the next era of digital contact tracing, where the contact tracing functionality is moved from being personal responsibility to be the responsibility of facilities that users visit daily. Our proposal tackles the two aforementioned shortcomings by disengaging users from using their own smartphones and requiring facilities to provide the technological devices needed for contact tracing. By doing so, we reassure users that their contacts are only considered in places where manual contact tracing is not effective, and cease being recorded as soon as they leave the facilities they visit. A privacy-preserving architecture is proposed, which can be mandated as a prerequisite for any facility to re-open during or after the pandemic. We finally outline research opportunities and challenges revolving around contact tracing system design and data management.", "Keywords": "", "DOI": "10.1145/3431843.3431846", "PubYear": 2020, "Volume": "12", "Issue": "2", "JournalId": 27894, "JournalTitle": "SIGSPATIAL Special", "ISSN": "", "EISSN": "1946-7729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> Khalifa University. Doha, Qatar"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Khalifa University. Doha, Qatar"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Khalifa University. Doha, Qatar"}], "References": []}, {"ArticleId": 84861871, "Title": "COVID-19 ensemble models using representative clustering", "Abstract": "In response to the COVID-19 pandemic, there have been various attempts to develop realistic models to both predict the spread of the disease and evaluate policy measures aimed at mitigation. Different models that operate under different parameters and assumptions produce radically different predictions, creating confusion among policy-makers and the general population and limiting the usefulness of the models. This newsletter article proposes a novel ensemble modeling approach that uses representative clustering to identify where existing model predictions of COVID-19 spread agree and unify these predictions into a smaller set of predictions. The proposed ensemble prediction approach is composed of the following stages: (1) the selection of the ensemble components, (2) the imputation of missing predictions for each component, and (3) representative clustering in application to time-series data to determine the degree of agreement between simulation predictions. The results of the proposed approach will produce a set of ensemble model predictions that identify where simulation results converge so that policy-makers and the general public are informed with more comprehensive predictions and the uncertainty among them.", "Keywords": "", "DOI": "10.1145/3431843.3431848", "PubYear": 2020, "Volume": "12", "Issue": "2", "JournalId": 27894, "JournalTitle": "SIGSPATIAL Special", "ISSN": "", "EISSN": "1946-7729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "George <PERSON> University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "George <PERSON> University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "George <PERSON> University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "George <PERSON> University"}], "References": []}, {"ArticleId": 84861874, "Title": "A label-oriented loss function for learning sentence representations", "Abstract": "Neural network methods which leverage word-embedding obtained from unsupervised learning models have been widely adopted in many natural language processing (NLP) tasks, including sentiment analysis and sentence classification. Existing sentence representation generation approaches which serve for classification tasks generally rely on complex deep neural networks but relatively simple loss functions, such as cross entropy loss function. These approaches cannot produce satisfactory separable sentence representations because the usage of cross entropy may ignore the sentiment and semantic information of the labels. To extract useful information from labels for improving the distinguishability of the obtained sentence representations, this paper proposes a label-oriented loss function. The proposed loss function takes advantage of the word-embeddings of labels to guide the production of meaningful sentence representations which serve for downstream classification tasks. Compared with existing end-to-end approaches, the evaluation experiments on several datasets illustrate that using the proposed loss function can achieve competitive and even better classification results.", "Keywords": "Label-embeddings ; Label-oriented loss ; Multi-LSTM ; Sentence representations ; Sentiment analysis", "DOI": "10.1016/j.csl.2020.101165", "PubYear": 2021, "Volume": "66", "Issue": "", "JournalId": 6012, "JournalTitle": "Computer Speech & Language", "ISSN": "0885-2308", "EISSN": "1095-8363", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University 400715, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University 400715, China"}, {"AuthorId": 3, "Name": "Dongxu Lu", "Affiliation": "College of Computer and Information Science, Southwest University 400715, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University 400715, China;Corresponding author"}], "References": []}, {"ArticleId": 84861892, "Title": "Spatial data systems support for the internet of things", "Abstract": "The Internet of Things (IoT) has recently received significant attention. An IoT device may possess an array of sensors that for example monitors the air temperature, carbon monoxide level, wifi signals, and sound intensity. IoT data is initially created on the device, then sent over to a central database system (e.g., the cloud) that organizes and prepares such data for the ongoing use by myriad applications, which include but are not limited to smart home, smart city, the industrial internet, connected cars, and connected health. Data generated by IoT devices is inherently spatial and temporal. For instance, an audio signal represents the variation of the sound intensity (retrieved by a sound sensor) over the time dimension. Furthermore, IoT devices are either installed in a static location (e.g., a building, a traffic intersection) or can be attached to moving objects such as a connected vehicle or a wearable device. In this article, we argue that existing IoT data systems do not properly consider the SpatioTemporal aspect of such data. Hence, the article represents a call for action to the SIGSPATIAL community in order to conduct research on building systems and applications that treat both the spatial and temporal dimensions of IoT data as first class citizens.", "Keywords": "", "DOI": "10.1145/3431843.3431850", "PubYear": 2020, "Volume": "12", "Issue": "2", "JournalId": 27894, "JournalTitle": "SIGSPATIAL Special", "ISSN": "", "EISSN": "1946-7729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Arizona State University"}], "References": []}, {"ArticleId": 84862100, "Title": "Kollaborative Robotikanwendungen an Montagearbeitsplätzen", "Abstract": "<p lang=\"de\"> Zusammenfassung <p>Im Rahmen des Forschungsprojektes SynDiQuAss wird untersucht, wie Assistenzsysteme an bisher wenig automatisierten Arbeitsplätzen zur Verbesserung der Arbeitsbedingungen von WerkerInnen sowie zur Produktivitätssteigerung eingesetzt werden können. Kollaborative Robotersysteme ermöglichen neue Gestaltungsmöglichkeiten gerade in Montageprozessen. Anhand des Anwendungsbeispiels eines Getriebeherstellers wird in diesem Beitrag die Applikation eines kollaborierenden Systems für die variantenindividuelle Kleinserienmontage betrachtet. Als wesentliche Herausforderungen werden dabei die Schnittstellenkonzeption einer datengestützten Integrationsplattform für digital vernetzte Assistenzsysteme sowie die sichere Interaktion zwischen Mensch und Roboter in beschränkten Kollaborationsräumen diskutiert. Anhand des Anwendungsbeispiels werden daraus Auslegungskriterien und -empfehlungen für die Integration kollaborativer Roboter in ein spezifiziertes Gesamtsystem an Montagearbeitsplätzen abgeleitet.</p></p>", "Keywords": "Kollaborative Robotik; Sichere Mensch-Maschine Interaktion; Integrationsplattform; Montagesysteme; Collaborative robotics; Safe Human-Machine-Interaction; Vision guided robotics; Assembly systems", "DOI": "10.1365/s40702-020-00677-w", "PubYear": 2020, "Volume": "57", "Issue": "6", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Technologietransferzentrum Nördlingen, Hochschule für angewandte Wissenschaften Augsburg, Nördlingen, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technologietransferzentrum Nördlingen, Hochschule für angewandte Wissenschaften Augsburg, Nördlingen, Deutschland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technologietransferzentrum Nördlingen, Hochschule für angewandte Wissenschaften Augsburg, Nördlingen, Deutschland"}], "References": []}, {"ArticleId": 84862297, "Title": "Partial Least Squares Optimization Method and Path Analysis Integration for Chinese Medicine Data", "Abstract": "Partial least squares (PLS) is widely used in multivariate statistical analysis, but linear and nonlinear model variable selections are based on the selection of principal components. It does not involve the interactions of variables and predictors, which may adversely affect prediction accuracy. In this study, we design a tailor built temperature control system to monitor and control temperature settings during experiments on traditional Chinese medicine (TCM). We combine results from path analysis and the variables' covariance and correlation matrix, and propose a PLS optimization method that integrates path analysis (PLS-PA). To verify the validity of PLS-PA, we use the measured coefficients and residuals as evaluation indicators. We test the performance of PLS-PA using two TCM dose datasets and one dataset from the University of California, Irvine (UCI). The three experimental results demonstrate that the measured coefficients from the traditional PLS and PLS-PA methods increase by 11.8, 4.7, and 8.5%, which suggest the validity of our experiment. We conclude that PLS-PA can optimize the screening of variables and improve the PLS regression analysis of TCM experimental data without hampering model accuracy. © 2020 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "Chinese medicine dosage; Partial least squares (PLS); Path analysis; Variable selection", "DOI": "10.18494/SAM.2020.2931", "PubYear": 2020, "Volume": "32", "Issue": "10", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Jiangxi University of Traditional Chinese Medicine, Nanchang, Jiangxi, 330004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Jiangxi University of Traditional Chinese Medicine, Nanchang, Jiangxi, 330004, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Jiangxi University of Traditional Chinese Medicine, Nanchang, Jiangxi, 330004, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Jiangxi University of Traditional Chinese Medicine, Nanchang, Jiangxi, 330004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Jiangxi University of Traditional Chinese Medicine, Nanchang, Jiangxi, 330004, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Jiangxi University of Traditional Chinese Medicine, Nanchang, Jiangxi, 330004, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Engineering College, Jimei University, Xiamen, Fujian, 361021, China"}], "References": []}, {"ArticleId": 84862300, "Title": "Lead-lag Swimming Control of Robot Fish Using Color Detection Algorithm", "Abstract": "This paper is about lead-lag robot fish swimming control by image processing. In robot fish aquariums, fish can generally move randomly in any path. This irregular movement may cause collisions between the fish, damaging them. Hence, an effective swimming control method is necessary. We thus proposed a lead-lag swimming control system for robot fish. Here, we simply study the detection of moving objects in an aquarium because we need to find the positions of moving robot fish. The locations of the robot fish are recognized using an image processing technique employing image and position sensors. This method is used to obtain the velocity for each pixel in an image and assumes a constant velocity in each video frame to obtain the positions of robot fish by comparing consecutive video frames. By using the position data, we compute the distance between robot fish and determine which robot fish is the lead fish and which fish is the lagging fish. The lead fish then waits for the lagging fish to catch up. The results of this proposed system are satisfactory in preventing collisions between robot fish. This system is exhibited in Busan Science Museum in South Korea. © 2020 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "Image processing; Lead-lag swimming control; Object detection algorithm; Robot fish", "DOI": "10.18494/SAM.2020.2926", "PubYear": 2020, "Volume": "32", "Issue": "10", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of ICT Creative Design, Busan University of Foreign Studies, 65, Geumsaem-ro 485 Beon-gil, Geumjeong-gu, Busan, 46234, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ICT Creative Design, Busan University of Foreign Studies, 65, Geumsaem-ro 485 Beon-gil, Geumjeong-gu, Busan, 46234, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ICT Creative Design, Busan University of Foreign Studies, 65, Geumsaem-ro 485 Beon-gil, Geumjeong-gu, Busan, 46234, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ICT Creative Design, Busan University of Foreign Studies, 65, Geumsaem-ro 485 Beon-gil, Geumjeong-gu, Busan, 46234, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ICT Creative Design, Busan University of Foreign Studies, 65, Geumsaem-ro 485 Beon-gil, Geumjeong-gu, Busan, 46234, South Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ICT Creative Design, Busan University of Foreign Studies, 65, Geumsaem-ro 485 Beon-gil, Geumjeong-gu, Busan, 46234, South Korea"}], "References": []}, {"ArticleId": 84862326, "Title": "Document scanners for minutiae-based palmprint recognition: a feasibility study", "Abstract": "<p>Highly expensive capturing devices and barely existent high-resolution palmprint datasets have slowed the development of forensic palmprint biometric systems in comparison with civilian systems. These issues are addressed in this work. The feasibility of using document scanners as a cheaper option to acquire palmprints for minutiae-based matching systems is explored. A new high-resolution palmprint dataset was established using an industry-standard Green Bit MC517 scanner and an HP Scanjet G4010 document scanner. Furthermore, a new enhancement algorithm to attenuate the negative effect of creases in the process of minutiae extraction is proposed. Experimental results highlight the potentialities of document scanners for forensic applications. Advantages and disadvantages of both technologies are discussed in this context as well. </p>", "Keywords": "High resolution-palmprint matching; Palmprint recognition; Minutiae-based recognition; Document scanner", "DOI": "10.1007/s10044-020-00923-3", "PubYear": 2021, "Volume": "24", "Issue": "2", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Advanced Technologies Application Center, Havana, Cuba;Department of Computer Sciences, University of Salzburg, Salzburg, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Advanced Technologies Application Center, Havana, Cuba;Department of Computer Sciences, University of Salzburg, Salzburg, Austria"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Advanced Technologies Application Center, Havana, Cuba;Department of Computer Sciences, University of Salzburg, Salzburg, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Advanced Technologies Application Center, Havana, Cuba;Department of Computer Sciences, University of Salzburg, Salzburg, Austria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Technologies Application Center, Havana, Cuba;Department of Computer Sciences, University of Salzburg, Salzburg, Austria"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Advanced Technologies Application Center, Havana, Cuba;Department of Computer Sciences, University of Salzburg, Salzburg, Austria"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Advanced Technologies Application Center, Havana, Cuba;Department of Computer Sciences, University of Salzburg, Salzburg, Austria"}], "References": []}, {"ArticleId": 84862332, "Title": "Frequency simulation of viscoelastic multi-phase reinforced fully symmetric systems", "Abstract": "<p>Honeycomb structures have the geometry of the lattice network to allow the minimization of the amount of used material to reach minimal material cost and minimal weight. In this regard, this article deals with the frequency analysis of imperfect honeycomb core sandwich disk with multiscale hybrid nanocomposite (MHC) face sheets rested on an elastic foundation. The honeycomb core is made of aluminum due to its low weight and high stiffness. The rule of the mixture and modified <PERSON><PERSON>–<PERSON>sai model are engaged to provide the effective material constant of the composite layers. By employing <PERSON>’s principle, the governing equations of the structure are derived and solved with the aid of the generalized differential quadrature method (GDQM). Afterward, a parametric study is done to present the effects of the orientation of fibers ( \\(\\theta_{{\\text{f}}} /\\pi\\) ) in the epoxy matrix, Winkler–Pasternak constants ( \\(K_{{\\text{w}}}\\) and \\(K_{{\\text{p}}}\\) ), thickness to length ratio of the honeycomb network ( \\(t_{{\\text{h}}} /l_{{\\text{h}}}\\) ), the weight fraction of CNTs, value fraction of carbon fibers, angle of honeycomb networks, and inner to outer radius ratio on the frequency of the sandwich disk. The results show that it is true that the roles of \\(K_{{\\text{w}}}\\) and \\(K_{{\\text{p}}}\\) are the same as an enhancement, but the impact of \\(K_{{\\text{w}}}\\) could be much more considerable than the effect of \\(K_{{\\text{p}}}\\) on the stability of the structure. Additionally, when the angle of the fibers is close to the horizon, the frequency of the system improves.</p>", "Keywords": "Sandwich disk; Honeycomb core; Elastic foundation; GDQM; Imperfection multiscale hybrid laminated nanocomposite; Frequency characteristic", "DOI": "10.1007/s00366-020-01200-x", "PubYear": 2022, "Volume": "38", "Issue": "S5", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "M. S. H. Al-Furjan", "Affiliation": "School of Mechanical Engineering, Hangzhou Dianzi University, Hangzhou, China; State Key Laboratory of Silicon Materials, School of Materials Science and Engineering, Zhejiang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Research and Development, Duy Tan University, Da Nang, Vietnam; Faculty of Electrical-Electronic Engineering, Duy Tan University, Da Nang, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON> won Jung", "Affiliation": "Departement of Mechanical Engineering, Jeju National University, Jeju, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Yonsei Frontier Lab, Yonsei University, Seoul, South Korea"}], "References": [{"Title": "Thermal buckling and forced vibration characteristics of a porous GNP reinforced nanocomposite cylindrical shell", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "2", "Page": "461", "JournalTitle": "Microsystem Technologies"}, {"Title": "An enhanced Bacterial Foraging Optimization and its application for training kernel extreme learning machine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105884", "JournalTitle": "Applied Soft Computing"}, {"Title": "Chaotic multi-swarm whale optimizer boosted support vector machine for medical diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> Chen", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "105946", "JournalTitle": "Applied Soft Computing"}, {"Title": "The critical voltage of a GPL-reinforced composite microdisk covered with piezoelectric layer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3489", "JournalTitle": "Engineering with Computers"}, {"Title": "Application of exact continuum size-dependent theory for stability and frequency analysis of a curved cantilevered microtubule by considering viscoelastic properties", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3629", "JournalTitle": "Engineering with Computers"}, {"Title": "On the nonlinear dynamics of a multi-scale hybrid nanocomposite disk", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "2369", "JournalTitle": "Engineering with Computers"}, {"Title": "Application of nonlocal strain–stress gradient theory and GDQEM for thermo-vibration responses of a laminated composite nanoshell", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3359", "JournalTitle": "Engineering with Computers"}, {"Title": "A comprehensive computational approach for nonlinear thermal instability of the electrically FG-GPLRC disk based on GDQ method", "Authors": "<PERSON><PERSON> S<PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "801", "JournalTitle": "Engineering with Computers"}, {"Title": "Nonlinear vibration of functionally graded magneto-electro-elastic higher order plates reinforced by CNTs using FEM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1029", "JournalTitle": "Engineering with Computers"}, {"Title": "Bi-directional thermal buckling and resonance frequency characteristics of a GNP-reinforced composite nanostructure", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1559", "JournalTitle": "Engineering with Computers"}, {"Title": "A computational framework for propagated waves in a sandwich doubly curved nanocomposite panel", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON> won Jung", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1679", "JournalTitle": "Engineering with Computers"}, {"Title": "A computational framework for propagated waves in a sandwich doubly curved nanocomposite panel", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON> won Jung", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1679", "JournalTitle": "Engineering with Computers"}, {"Title": "RETRACTED ARTICLE: Chaotic simulation of the multi-phase reinforced thermo-elastic disk using GDQM", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "219", "JournalTitle": "Engineering with Computers"}, {"Title": "On the phase velocity simulation of the multi curved viscoelastic system via an exact solution framework", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "353", "JournalTitle": "Engineering with Computers"}, {"Title": "Wave propagation simulation in an electrically open shell reinforced with multi-phase nanocomposites", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "629", "JournalTitle": "Engineering with Computers"}, {"Title": "On the modeling of bending responses of graphene-reinforced higher order annular plate via two-dimensional continuum mechanics approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "703", "JournalTitle": "Engineering with Computers"}, {"Title": "Influence of in-plane loading on the vibrations of the fully symmetric mechanical systems via dynamic simulation and generalized differential quadrature framework", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S5", "Page": "3675", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 84862695, "Title": "Die Beantwortung von Auskunftsersuchen nach Art. 15 DSGVO", "Abstract": "<p>Die Rechtslage zum Auskunftsersuchen nach Art. 15 DSGVO hat Ähnlichkeit mit der vorhergehenden nach dem BDSG und brachte dennoch auch Neuerungen mit sich. Wie gut hat sich die neue Rechtslage zwei Jahre nach Geltungsbeginn der DSGVO in der Praxis eingespielt? Z<PERSON> dieser Studie auf Grundlage von über 100 Auskünften ist die Abbildung des ,,status quo‘‘ ihrer Beantwortung in der Praxis, die Darstellung von Problemfeldern sowie Vorschlägen zu deren Lösung. </p>", "Keywords": "", "DOI": "10.1007/s11623-020-1357-2", "PubYear": 2020, "Volume": "44", "Issue": "11", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Heidelberg, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mannheim, Deutschland"}], "References": [{"Title": "Die Datenkopie nach Artikel 15 Abs. 3 DS-GVO", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "2", "Page": "98", "JournalTitle": "Datenschutz und Datensicherheit - DuD"}]}, {"ArticleId": 84862703, "Title": "Popularity prediction caching based on logistic regression in vehicular content centric networks", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2020.110821", "PubYear": 2020, "Volume": "35", "Issue": "3", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Li", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84862704, "Title": "Fine-grained emotion recognition: fusion of physiological signals and facial expressions on spontaneous emotion corpus", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2020.110824", "PubYear": 2020, "Volume": "35", "Issue": "3", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Kyoungsoo Park", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84862719, "Title": "An Interpretable Predictive Model of Vaccine Utilization for Tanzania", "Abstract": "Providing accurate utilization forecasts is key to maintaining optimal vaccine stocks in any health facility. Current approaches to vaccine utilization forecasting are based on often outdated population census data, and rely on weak, low-dimensional demand forecasting models. Further, these models provide very little insights into factors that influence vaccine utilization. Here, we built a state-of-the-art, predictive machine learning model using novel, temporally and regionally relevant vaccine utilization data. This highly multidimensional machine learning approach accurately predicted bi-weekly vaccine utilization at the individual health facility level. Specifically, we achieved a forecasting fraction error of less than two for about 45% of regional health facilities in both the Tanzania regions analyzed. Our “random forest regressor” had an average forecasting fraction error that was almost 18 times less compared to the existing system. Importantly, using our model, we gleaned several key insights into factors underlying utilization forecasts. This work serves as an important starting point to reimagining predictive health systems in the developing world by leveraging the power of Artificial Intelligence and big data.", "Keywords": "machine learning; forecast; artificial intelligence; Random forest regression (RFR); Vaccine", "DOI": "10.3389/frai.2020.559617", "PubYear": 2020, "Volume": "3", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "United States;Northeastern University, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "United States"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "United States"}], "References": []}, {"ArticleId": 84862753, "Title": "Monitoring Activities of Daily Living Using UWB Radar Technology: A Contactless Approach", "Abstract": "<p>In recent years, the ultra-wideband (UWB) radar technology has shown great potential in monitoring activities of daily living (ADLs) for smart homes. In this paper, we investigate the significance of using non-wearable UWB sensors for developing non-intrusive, unobtrusive, and privacy-preserving monitoring of elderly ADLs. A controlled experiment was setup, implementing multiple non-wearable sensors in a smart home Lab setting. A total of nine (n = 9) participants were involved in conducting predefined scenarios of ADLs- cooking, eating, resting, sleeping and mobility. We employed the UWB sensing prototype and conventional implementation technologies, and the sensed data of both systems were stored, analysed and their performances were compared. The result shows that the performance of the non-wearable UWB technology is as good as that of the conventional ones. Furthermore, we provided a proof-of-concept solution for the real-time detection of abnormal behaviour based on excessive activity levels, and a model for automatic alerts to caregivers for timely medical assistance on-demand.</p>", "Keywords": "ultra-wideband; UWB; activities of daily living; ADL; AAL; non-wearable; sensors; smart-home; IoT ultra-wideband ; UWB ; activities of daily living ; ADL ; AAL ; non-wearable ; sensors ; smart-home ; IoT", "DOI": "10.3390/iot1020019", "PubYear": 2020, "Volume": "1", "Issue": "2", "JournalId": 58626, "JournalTitle": "IoT", "ISSN": "", "EISSN": "2624-831X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Technology, Kristiania University College, 0152 Oslo, Norway ↑ These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "Gebremariam <PERSON>res", "Affiliation": "Department of Technology, Kristiania University College, 0152 Oslo, Norway ↑ Author to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Technology, Kristiania University College, 0152 Oslo, Norway ↑ These authors contributed equally to this work"}, {"AuthorId": 4, "Name": "Tor-<PERSON><PERSON>", "Affiliation": "Department of Technology, Kristiania University College, 0152 Oslo, Norway ↑ These authors contributed equally to this work"}], "References": [{"Title": "Activity Recognition in Smart Homes using UWB Radars", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "10", "JournalTitle": "Procedia Computer Science"}]}]