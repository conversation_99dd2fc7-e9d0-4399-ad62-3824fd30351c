[{"ArticleId": 84669855, "Title": "Preface to the requirements engineering special issue on selected papers from RE’19", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00766-020-00340-2", "PubYear": 2020, "Volume": "25", "Issue": "4", "JournalId": 15613, "JournalTitle": "Requirements Engineering", "ISSN": "0947-3602", "EISSN": "1432-010X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical, Computer, and Software Engineering, University of Auckland, Auckland, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Victoria, Victoria, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Fondazione Bruno <PERSON>, Trento, Italy"}], "References": []}, {"ArticleId": 84669939, "Title": "Argument annotation and analysis using deep learning with attention mechanism in Bahasa Indonesia", "Abstract": "Abstract \nArgumentation mining is a research field which focuses on sentences in type of argumentation. Argumentative sentences are often used in daily communication and have important role in each decision or conclusion making process. The research objective is to do observation in deep learning utilization combined with attention mechanism for argument annotation and analysis. Argument annotation is argument component classification from certain discourse to several classes. Classes include major claim, claim, premise and non-argumentative. Argument analysis points to argumentation characteristics and validity which are arranged into one topic. One of the analysis is about how to assess whether an established argument is categorized as sufficient or not. Dataset used for argument annotation and analysis is 402 persuasive essays. This data is translated into Bahasa Indonesia (mother tongue of Indonesia) to give overview about how it works with specific language other than English. Several deep learning models such as CNN (Convolutional Neural Network), LSTM (Long Short-Term Memory), and GRU (Gated Recurrent Unit) are utilized for argument annotation and analysis while HAN (Hierarchical Attention Network) is utilized only for argument analysis. Attention mechanism is combined with the model as weighted access setter for a better performance. From the whole experiments, combination of deep learning and attention mechanism for argument annotation and analysis arrives in a better result compared with previous research.", "Keywords": "", "DOI": "10.1186/s40537-020-00364-z", "PubYear": 2020, "Volume": "7", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84669989, "Title": "Employee Attrition Prediction Using Machine Learning and Sentiment Analysis", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/91952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84669990, "Title": "Kerberos: Security Analysis of Authentication protocol", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/94952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84669992, "Title": "Machine Learning Household Waste Disposal Behavior Related Factors", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/98952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84669993, "Title": "Optimal Distributed Generations Placement in Radial Distribution Network Using Whale Optimization Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/110952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84669994, "Title": "Numerical Study of the Mechanical Behavior of Polyamide 66 Reinforced by <PERSON>rgan Nut Shell Particles with the Finite Element Method and the Mori-Tanaka Model", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/115952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84669998, "Title": "Quasi-Lossless Based Fractal Image Compression Using Krill Herd Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/121952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670002, "Title": "Appraisal for the Evolution of Risk in Construction Industry", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/36952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670007, "Title": "Neural Network for Breast Cancer Prediction", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/42952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670010, "Title": "Computer Simulation and Visualization of the Dynamic Poroelasticity Problem Solutions", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/53952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670011, "Title": "Content-Based Music Genre Classification: Modified AIS-Based Classifier", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/56952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670013, "Title": "Deep Learning based Dynamic Hand Gesture Recognition with Leap Motion Controller", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/61952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670015, "Title": "Design and Development of Smart Waste Management System: A Mobile App for Connecting and Monitoring Dustbin Using IoT", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/64952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670021, "Title": "Classification of the Final Project Utilized a Modified Naïve Bayes Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/48952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670047, "Title": "Collaborative Filter Based Product Recommendation System using Machine Learning KNN and Cosine Similarity over Twitter by Big Data vide Ontology", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/51952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670051, "Title": "Conceptual Model of the Education Management Information System for Higher Education Institutions", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/59952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670052, "Title": "A Property Oriented Pandemic Surviving Trading Model", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/71952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670053, "Title": "Design and Implementation of a Message Passing Interface (MPI) Dynamic Error Detection System", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/65952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670054, "Title": "Computer Vision and Machine Learning based Facial Expression Analysis", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/74952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670055, "Title": "Detection of COVID-19 in Computed Tomography (CT) Scan Images using Deep Learning", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/77952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670056, "Title": "Analysis of Santa’s river water pollution caused by the environmental liability of Ticapampa using grey clustering method", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/92952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670057, "Title": "Multinomial Classification of “Hete-Neurons” in Heterogeneous Information Networks", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/107952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670058, "Title": "Performance analysis and high recognition rate of automated hand gesture recognition though GMM and SVM-KNN classifiers", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/114952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670059, "Title": "Prognosis of Chronic Diseases Using Different Machine Learning Models", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/119952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670060, "Title": "Recent Advances in AI based Automated Personalized Nutrition System: Future Need of Healthcare", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/122952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670071, "Title": "Digital Standardization as a tool for effective development of dairy products", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/86952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670072, "Title": "Designing an Architecture for Delivering Multilingual Interactive Voice Information Services", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/76952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670073, "Title": "Data Mining as Tools to Improve Marketing Campaign", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/57952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670077, "Title": "Improved Scheduling Algorithm for the IIR Filter design for Low power VLSI Applications", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/79952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670078, "Title": "A Survey on Content Based Image Retrieval Using Convolutional Neural Networks", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/70952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670081, "Title": "Effect of Quenching Media on Laser butt Welded Joint on Transformed -Induced Plasticity (TRIP) Steel", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/90952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670082, "Title": "Maritime Surveillance with Planes Using Machine Learning Techniques", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/99952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670083, "Title": "Prediction of Mechanical Properties of Metal through Machine Learning Approach", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/117952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670087, "Title": "A Game Application to assist Speech Language Pathologists in the Assessment of Children with Speech Disorders", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/02952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670088, "Title": "A Simplified Model to Calculate the Power Absorbed for the Movement of Organic Waste in a Rotary Composter: Industrial Case Study", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/09952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670089, "Title": "Learning Materials Representation for Interoperability in Heterogeneous Environment", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/14952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670090, "Title": "Novel Method to Diagnose Brain Tumor in its Early Stage Using Brain MRI Images", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/21952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670091, "Title": "Advanced Information Extraction System based on Unstructured Mammogram Reports", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/25952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670092, "Title": "An Efficient Chaotic Encryption with Video Watermarking Technique using Improved Whale Optimization Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/28952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670093, "Title": "Application of the Swarm Intelligence for the Semantic Annotation of the Web of Things", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/34952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670094, "Title": "Analyzing Employers’ Feedback Using Text Analytics: An Associative Based Approach", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/32952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670095, "Title": "Auto-feed Hyperparameter Support Vector Regression Prediction Algorithm in Handling Missing Values in Oil and Gas Dataset", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/39952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670097, "Title": "CBIR System Using Scaled Conjugate Gradient Feed-Forward Neural Network", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/46952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670098, "Title": "Clickstream Data Schema for Learning Analytics to Understand Learner Behaviour", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/49952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670099, "Title": "Combination Of Scrum Methodology And IBM Design Thinking For Dashboard And Report System Development", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/52952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670102, "Title": "Constructing <PERSON><PERSON> of Mirai Botnets Attack using Graph Theory Approach", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/55952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670105, "Title": "Deep Learning for Conversational Agent via Context Question Answering Model", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/62952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670106, "Title": "Artificial Intelligence for Improving the Optimization of NP-Hard Problems: A Review", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/73952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670107, "Title": "Information System Security Risk Management E-Learning Using FMEA in University", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/93952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670108, "Title": "Analysis of Methods and Information Technologies for Dynamic Planning of Smart City Development", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/83952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670109, "Title": "Performance of Faster R-CNN to Detect Plastic Waste", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/120952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670111, "Title": "News Article to Uncover Media Bias", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/108952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670138, "Title": "A Generalized SVPWM Scheme for Multilevel Inverters with Fixed Computational Time", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/03952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670140, "Title": "A Prototype Model for Image Encryption using ZigZig Blocks in Inter-Pixel Displacement of RGB Value", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/06952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670141, "Title": "Integrated Security, Authentication and Decentralized Access Control (ISADA) Framework Based on Novel Key Exchange Mechanism for a Public Cloud Environment", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/12952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670142, "Title": "An Approach on Cyber Protection Changes by way of a cohesive Red and Blue Working Committee", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/26952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670143, "Title": "Broadband and High Efficiency Rectifier Design Based on Dual-mode Operation for RF Ambient Energy Harvesting", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/43952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670147, "Title": "Applying AHP to Formulate Weighted-Sum Goal Programming in Solving the RNP problem in WSNs", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/35952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670148, "Title": "Machine Learning Models for the Prediction the Necessity of Resorting to ICU of COVID-19 Patients", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/15952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670149, "Title": "Automatic system for Arabic Sign Language Recognition and translation to spoken One", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/37952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670154, "Title": "Methods of Cyber Security Assessment in the Information and Telecommunications System", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/17952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670155, "Title": "A Novel Sensitive Rectifier with Increased Output Load Tolerance for Ambient RF Energy Harvesting", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/22952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670156, "Title": "Baseline Wandering Removal in ECG Signal Using Filters", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/41952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84670216, "Title": "Detecting Ordinal Subcascades", "Abstract": "Abstract \n Ordinal classifier cascades are constrained by a hypothesised order of the semantic class labels of a dataset. This order determines the overall structure of the decision regions in feature space. Assuming the correct order on these class labels will allow a high generalisation performance, while an incorrect one will lead to diminished results. In this way ordinal classifier systems can facilitate explorative data analysis allowing to screen for potential candidate orders of the class labels. Previously, we have shown that screening is possible for total orders of all class labels. However, as datasets might comprise samples of ordinal as well as non-ordinal classes, the assumption of a total ordering might be not appropriate. An analysis of subsets of classes is required to detect such hidden ordinal substructures. In this work, we devise a novel screening procedure for exhaustive evaluations of all order permutations of all subsets of classes by bounding the number of enumerations we have to examine. Experiments with multi-class data from diverse applications revealed ordinal substructures that generate new and support known relations.", "Keywords": "Ordinal classification; Classifier cascades; Error bounds; Subsets; Supersets", "DOI": "10.1007/s11063-020-10362-0", "PubYear": 2020, "Volume": "52", "Issue": "3", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Medical Systems Biology, Ulm, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Medical Systems Biology, Ulm, Germany"}, {"AuthorId": 3, "Name": "<PERSON>e D<PERSON>", "Affiliation": "Institute of Medical Systems Biology, Ulm, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Internal Medicine I, University Hospital Ulm, Ulm, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Medical Systems Biology, Ulm, Germany;Leibniz Institute on Aging – Fritz Lipmann Institute, Jena, Germany"}], "References": []}, {"ArticleId": 84670238, "Title": "Modeling of Isocyanate Synthesis by the Thermal Decomposition of Carbamates", "Abstract": "<p>The presented work is devoted to isocyanate synthesis by the thermal decomposition of carbamates model. The work describes the existing isocyanate-obtaining processes and the main problems in the study of isocyanate synthesis by the thermal decomposition of carbamates, which can be solved using mathematical and computer models. Experiments with carbamates of various structures were carried out. After processing the experimental data, the activation energy and the pre-exponential factor for isocyanate synthesis by the thermal decomposition of carbamates were determined. Then, a mathematical model of the reactor for the thermal decomposition of carbamates using the COMSOL Multiphysics software was developed. For this model, computational experiments under different conditions were carried out. It was shown that the calculation results correspond to the experimental ones, so the suggested model can be used in the design of the equipment for isocyanate synthesis by the thermal decomposition of carbamates.</p>", "Keywords": "modeling; COMSOL Multiphysics; isocyanates; thermolysis modeling ; COMSOL Multiphysics ; isocyanates ; thermolysis", "DOI": "10.3390/computation8040089", "PubYear": 2020, "Volume": "8", "Issue": "4", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Center, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Center, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "International Science and Education Center for Transfer of Biopharmaceutical Technologies, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "International Science and Education Center for Transfer of Biopharmaceutical Technologies, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia↑Author to whom correspondence should be addressed"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "International Science and Education Center for Transfer of Biopharmaceutical Technologies, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "International Science and Education Center for Transfer of Biopharmaceutical Technologies, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Engineering Center, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Center, Mendeleev University of Chemical Technology of Russia, 125047 Moscow, Russia"}], "References": []}, {"ArticleId": 84670329, "Title": "Research Trend on the Use of IT in Digital Addiction: An Investigation Using a Systematic Literature Review", "Abstract": "<p>Despite the negative role of IT in digital addiction development, IT may have a positive role in dealing with digital addiction. The present study undertakes a systematic literature review to explore the state of play and the trend regarding the use of IT in digital addiction research. Using predefined keywords, the Scopus database was searched for relevant literature published from 2017 to 2020. The initial search found 1655 papers. Six stages of study selection were completed using a set of inclusion and exclusion criteria. The study selection and quality assessment process were applied, then 15 papers were selected for further review. The results show that addiction detection using IT is the most researched topic in digital addiction research. The most commonly used IT in the selected studies are AI methods and biosignal recording systems. Various approaches in detection, prevention, and intervention are suggested in the selected studies. The advantages and limitations of each approach are discussed. Based on these results, some future research directions are suggested.</p>", "Keywords": "information technology; digital addiction; internet addiction; detection; prevention; intervention information technology ; digital addiction ; internet addiction ; detection ; prevention ; intervention", "DOI": "10.3390/fi12100174", "PubYear": 2020, "Volume": "12", "Issue": "10", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "Flourensia Sapty Rahayu", "Affiliation": "Program Studi Sistem Informasi, Universitas Atma Jaya Yogyakarta, <PERSON><PERSON><PERSON> Yogyakarta 55281, Indonesia<PERSON><PERSON><PERSON><PERSON> to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departemen Teknik Elektro dan Teknologi Informasi, Universitas Gadjah Mada, <PERSON><PERSON>h <PERSON>timewa Yogyakarta 55281, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departemen Teknik Elektro dan Teknologi Informasi, Universitas Gadjah Mada, <PERSON><PERSON>h <PERSON>timewa Yogyakarta 55281, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Magister Informati<PERSON>, Universitas Atma Jaya Yogyakarta, <PERSON><PERSON>h <PERSON>wa Yogyakarta 55281, Indonesia"}], "References": [{"Title": "Is it possible to cure Internet addiction with the Internet?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "1", "Page": "245", "JournalTitle": "AI & SOCIETY"}]}, {"ArticleId": 84670353, "Title": "Secure Communication for Uplink Cellular Networks Assisted with Full-Duplex Device-to-Device User", "Abstract": "<p>In this paper, the secure communication based on the full-duplex (FD) device-to-device (D2D) in cellular networks is proposed. For the proposed scheme, the novel model is established, in which a D2D user is played as a relay operating in FD mode to assist in the secure transmission of uplink information. Considering that the D2D user as a relay is untrusted, D2D link rate maximization is formulated with the constraint of secrecy rate, which ensures the security of uplink cellular networks. To cope with the optimization problem, the optimal power allocation factors of the cellular user (CU) and the D2D user are jointly optimized. Firstly, by using the monotonicity of the objective function, the optimal solution of the power allocation factor at the D2D user can be obtained. Subsequently, the closed-form expression of the optimal power allocation factor at the CU is derived and verified that the solution is the global minimum point. Simulation results verify that the proposed scheme has better output performance than the conventional scheme.</p>", "Keywords": "D2D communication; FD untrusted relay; physical layer security; power allocation D2D communication ; FD untrusted relay ; physical layer security ; power allocation", "DOI": "10.3390/fi12100175", "PubYear": 2020, "Volume": "12", "Issue": "10", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Optimization and Smart Antenna Institute, Northeastern University, Qinhuangdao 066004, China↑School of Computer Science and Engineering, Northeastern University, Shenyang 110819, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Engineering Optimization and Smart Antenna Institute, Northeastern University, Qinhuangdao 066004, China↑School of Computer Science and Engineering, Northeastern University, Shenyang 110819, China↑Author to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Xu", "Affiliation": "Engineering Optimization and Smart Antenna Institute, Northeastern University, Qinhuangdao 066004, China↑School of Computer Science and Engineering, Northeastern University, Shenyang 110819, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Optimization and Smart Antenna Institute, Northeastern University, Qinhuangdao 066004, China↑School of Computer Science and Engineering, Northeastern University, Shenyang 110819, China"}], "References": []}, {"ArticleId": 84670540, "Title": "Universal stego post-processing for enhancing image steganography", "Abstract": "It is well known that the designing or improving embedding cost becomes a key issue for current steganographic methods. Unlike existing works, we propose a novel framework to enhance the steganography security via post-processing on the embedding units (i.e., pixel values and DCT coefficients) of stego directly. In this paper, we firstly analyze the characteristics of STCs (Syndrome-Trellis Codes), and then design the rule for post-processing to ensure the correct extraction of hidden message. Since the steganography artifacts are typically reflected on image residuals, we try to reduce the residual distance between cover and the modified stego in order to enhance steganography security. To this end, we model the post-processing as a non-linear integer programming, and implement it via heuristic search. In addition, we carefully determine several important issues in the proposed post-processing, such as the candidate embedding units to be modified, the direction and amplitude of post-modification, the adaptive filters for getting residuals, and the distance measure of residuals. Extensive experimental results evaluated on both hand-crafted steganalytic features and deep learning based ones demonstrate that the proposed method can effectively enhance the security of most modern steganographic methods both in spatial and JPEG domains.", "Keywords": "Stego post-processing ; Syndrome-Trellis Codes ; Steganography ; Steganalysis", "DOI": "10.1016/j.jisa.2020.102664", "PubYear": 2020, "Volume": "55", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, and Guangdong Key Laboratory of Information Security Technology, Sun Yat-sen University, Guangzhou 510006, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, and Guangdong Key Laboratory of Information Security Technology, Sun Yat-sen University, Guangzhou 510006, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, and Guangdong Key Laboratory of Information Security Technology, Sun Yat-sen University, Guangzhou 510006, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Shenzhen University, Shenzhen 518052, PR China"}], "References": []}, {"ArticleId": 84670787, "Title": "Failure prediction in electrical connector assembly: a case in automotive assembly process", "Abstract": "Purpose \nElectrical defects cover an important part of assembly defects and strongly affect the vehicle system performance. Almost 40% of assembly defects are classified as human errors and electrical connection failures represent a significant part of them. Humans still remain a cost-effective solution for the flexible manufacturing systems with increasing product complexity. So, understanding human behaviors is still a challenging task. The purpose of this study is to define, prioritize and validate the critical factors for the complexity of electrical connector plugin process.\n \n \n Design/methodology/approach \nThe critical variables were defined by the expert team members. The required number of measurements and variables were revised resulting preliminary analysis of binary logistic regression. After the revision of measurement plan, the list of critical input variables and the mathematical model were defined. The model has been validated by the fitted values of the residuals (FITS analysis).\n \n \n Findings \nTo the best of the authors’ knowledge, this is one of the limited studies, which defines the critical factors for electrical connection process complexity. Female connector harness length, connector width/height/length differences, operator sense of correct connector matching and ergonomy were defined as the factors with the highest impact on the failure occurrence. The obtained regression equation strongly correlates the failure probability.\n \n \n Practical implications \nThe obtained mathematical model can be used in new model development processes both for the product and assembly process design (ergonomy, accessibility and lay-out).\n \n \n Originality/value \nThe obtained risk factors demonstrated a strong correlation with assembly process complexity and failure rates. The output of this study would be used as an important guide for process (assembly line ergonomy, accessibility and lay-out) and product design in new model development and assembly ramp-up phases.", "Keywords": "Binary logistic regression;Failure prediction;Assembly complexity;Automotive assembly;Electrical connectors", "DOI": "10.1108/AA-06-2020-0077", "PubYear": 2020, "Volume": "40", "Issue": "6", "JournalId": 4373, "JournalTitle": "Assembly Automation", "ISSN": "0144-5154", "EISSN": "1758-4078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Quality Engineering, TOFAS, Bursa, Turkey"}, {"AuthorId": 2, "Name": "Ut<PERSON>", "Affiliation": "Department of Quality Engineering, TOFAS, Bursa, Turkey"}], "References": []}, {"ArticleId": 84670801, "Title": "Learning peg-in-hole assembly using Cartesian DMPs with feedback mechanism", "Abstract": "Purpose \nThis paper aims to enable the robot to obtain human-like compliant manipulation skills for the peg-in-hole (PiH) assembly task by learning from demonstration.\n \n \n Design/methodology/approach \nA modified dynamic movement primitives (DMPs) model with a novel hybrid force/position feedback in Cartesian space for the robotic PiH problem is proposed by learning from demonstration. To ensure a compliant interaction during the PiH insertion process, a Cartesian impedance control approach is used to track the trajectory generated by the modified DMPs.\n \n \n Findings \nThe modified DMPs allow the robot to imitate the trajectory of demonstration efficiently and to generate a smoother trajectory. By taking advantage of force feedback, the robot shows compliant behavior and could adjust its pose actively to avoid a jam. This feedback mechanism significantly improves the dynamic performance of the interactive process. Both the simulation and the PiH experimental results show the feasibility and effectiveness of the proposed model.\n \n \n Originality/value \nThe trajectory and the compliant manipulation skill of the human operator can be learned simultaneously by the new model. This method adopted a modified DMPs model in Cartesian space to generate a trajectory with a lower speed at the beginning of the motion, which can reduce the magnitude of the contact force.", "Keywords": "Peg-in-hole;Robotic assembly;Cartesian DMPs;Learning from demonstration;Compliance;Force control;Control;Automatic assembly;Assembly;Artificial intelligence;Assembly sequence planning", "DOI": "10.1108/AA-04-2020-0053", "PubYear": 2020, "Volume": "40", "Issue": "6", "JournalId": 4373, "JournalTitle": "Assembly Automation", "ISSN": "0144-5154", "EISSN": "1758-4078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation , Chinese Academy of Sciences, Shenyang, China ; and Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China and University of Chinese Academy of Sciences , Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Control Engineering , Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation , Chinese Academy of Sciences, Shenyang, China and Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation , Chinese Academy of Sciences, Shenyang, China and Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation , Chinese Academy of Sciences, Shenyang, China and Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China"}], "References": []}, {"ArticleId": 84671058, "Title": "A product ranking method combining the features–opinion pairs mining and interval-valued Pythagorean fuzzy sets", "Abstract": "Mining online reviews has become an important means of identifying consumer behavior and the innovation direction of products. However, it is difficult for both producers and consumers to effectively analyze and extract relevant opinions from a vast number of online reviews. To overcome this problem, a product ranking method that combines feature–opinion pairs mining and interval-valued Pythagorean fuzzy (IVPF) sets was proposed in this study. First, three types of important feature–opinion pairs were clearly defined based on the diversity and complexity of opinion expression forms in Chinese ecommerce reviews. Two deep learning models were then designed to automatically extract the feature–opinion terms and match them into pairs. Afterwards, sentiment analysis techniques were applied to identify sentiment orientation, and the feature–opinion pairs were clustered into groups using K-means clustering algorithm. Meanwhile, considering the confidence level based on the number of online reviews on different products, sentiment value was transformed into interval-value from, including interval membership and non-membership. As the sum of the converted interval membership and non-membership was greater than 1 and their quadratic sum was less than 1, IVPF set was introduced to represent the interval-valued sentiment. Furthermore, based on the interrelationship between product attributes, we proposed an IVPF weighted Heronian mean operator to aggregate the attribute information. Product ranking was then achieved based on the operator and operations under the IVPF information. Finally, a case study was used to verify the feasibility of the proposed method, and comparisons and sensitivity analysis were performed to demonstrate the superiority of our method.", "Keywords": "Features–opinion pairs mining ; Sentiment analysis ; Interval-valued Pythagorean fuzzy sets ; Heronian mean operator ; Product ranking method", "DOI": "10.1016/j.asoc.2020.106803", "PubYear": 2020, "Volume": "97", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Xiangling Fu", "Affiliation": "School of Software Engineering, Beijing University of Posts and Telecommunications, Beijing, China;Key Laboratory of Trustworthy Distributed Computing and Service, Ministry of Education, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Beijing University of Posts and Telecommunications, Beijing, China;Key Laboratory of Trustworthy Distributed Computing and Service, Ministry of Education, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Economics and Management, Beijing University of Technology, Beijing 100124, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Beijing University of Posts and Telecommunications, Beijing, China;Key Laboratory of Trustworthy Distributed Computing and Service, Ministry of Education, Beijing University of Posts and Telecommunications, Beijing, China"}], "References": [{"Title": "A decision‐making algorithm for online shopping using deep‐learning–based opinion pairs mining and\n q\n ‐rung orthopair fuzzy interaction Heronian mean operators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Xiangling Fu", "PubYear": 2020, "Volume": "35", "Issue": "5", "Page": "783", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 84671072, "Title": "Identification and characterization of the first fish parvalbumin-like protein data from a pathogenic fungal species, Trichophyton violaceum", "Abstract": "Parvalbumins are the most important fish allergens, which are heat-stable, classified in the family of calcium-binding EF-hand proteins, and contain one magnesium binding site. The functional connection between calcium and parvalbumin gives fish the high-speed swimming ability because of high concentration of Ca<sup>2+</sup>-binding parvalbumin in fish white muscles. Although parvalbumins are widely studied and conceivably play crucial roles in the physiology and swimming pattern of fishes, still no report is available about their presence in microbes, such as pathogenic fungal species. We detected a DNA sequence in the genome of Trichophyton violaceum and used in silico and polymerase chain reaction (PCR) technique with a designed pair of primers to identify it as parvalbumin-coding gene.", "Keywords": "Polymerase chain reaction (PCR) ; DNA sequence ; Molecular diagnosis ; In silico ; Parvalbumin ; Fungi ; Trichophyton violaceum", "DOI": "10.1016/j.dib.2020.106420", "PubYear": 2020, "Volume": "33", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Energy and Environmental Biotechnology, National Institute of Genetic Engineering and Biotechnology (NIGEB), 14965/161, Tehran, Iran;@amameh54"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Laboratory Sciences, Lorestan University of Medical Sciences, Khorramabad, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Materials Engineering, Tarbiat Modares University, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Immunology Research Center, Iran University of Medical Sciences, Tehran, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Medicine and Health Technology, Tampere University, FI-33014 Tampere, Finland;Fimlab Ltd. and Tampere University Hospital, FI-33520 Tampere, Finland"}], "References": []}, {"ArticleId": 84671074, "Title": "Biomimetic integration of MQL and tool surface microstructure in intermittent machining", "Abstract": "<p>Vegetable oil–based nanofluid minimum quantity lubrication (VNMQL) and tool surface microstructure can be employed to efficiently improve the cutting process in an environmentally friendly way. Biomimetics-based integration of VNMQL and tool surface microstructure is expected to have beneficial effects on the friction coefficient, the cutting force, the tool temperature, the tool wear, and the surface roughness in intermittent turning process. VNMQL was integrated with tool surface microstructure on the basis of the analysis results of Odontodactylus scyllarus . For the purpose of obtaining better machining results, different combinations of the element area A and the laser angle θ were adopted for the tool surface microstructure to match VNMQL. Biomimetics-based integration of VNMQL and tool surface microstructure led to even lower values of the quantities such as the friction coefficient, the cutting force, the tool temperature, the tool wear, and the surface roughness compared with dry condition and VNMQL condition. The smallest values of these quantities were acquired at the same optimum combination of the microstructure element area A and the laser angle θ. The optimum combination can be described as follows: A was 2.4 × 10<sup>−8</sup> m<sup>2</sup> and θ was 75°. There existed close relationship among the cutting force, the tool temperature, the tool wear, and the surface roughness. Cutting force was found to be more suitable for pre-evaluating tool wear and surface roughness compared with tool temperature.</p>", "Keywords": "Biomimetics-based integration; Intermittent turning; Tool surface microstructure; Vegetable-oil-based nanofluid MQL", "DOI": "10.1007/s00170-020-06247-0", "PubYear": 2020, "Volume": "111", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Energy Science and Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}], "References": []}, {"ArticleId": 84671101, "Title": "Physico-chemical and rheological properties datasets related to batch mesophilic anaerobic digestion of waste activated sludge, primary sludge, and mixture of sludge with organic and inorganic matter", "Abstract": "The datasets included in this paper provide periodically measured physico-chemical and rheological properties of mesophilic batch anaerobic digesters’ content for 30 days biochemical methane potential tests (BMP). Waste activated sludge (WAS) and primary sludge (PS) were the main substrates and digested sludge from a large scale mesophilic anaerobic digester was the inoculum. The substrates (F) and inoculum (I) were fed into the BMP rectors at different ratios of food to inoculum (F/I = 1:1, 1:2, and 1:3). Experimental data on co-digestion of WAS with inorganic and organic additives were also reported. The reported characteristics such as total solids, volatile solids, total and soluble biochemical oxygen demand, ammonia, pH, as well as rheological properties over the duration of the BMP test could be used for analysing the changes in digestate properties as the anaerobic digestion process proceeds. The discussion and interpretation of the data have been provided in previous publications [1] , [2] , [3] .", "Keywords": "Physico-chemical ; rheological properties ; mesophilic batch anaerobic digestion ; biochemical methane potential test", "DOI": "10.1016/j.dib.2020.106418", "PubYear": 2023, "Volume": "51", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chemical and Environmental Engineering, School of Engineering, RMIT University, 3001, Melbourne, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chemical and Environmental Engineering, School of Engineering, RMIT University, 3001, Melbourne, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Chemical and Environmental Engineering, School of Engineering, RMIT University, 3001 Melbourne, Australia."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Chemical and Environmental Engineering, School of Engineering, RMIT University, 3001 Melbourne, Australia."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chemical and Environmental Engineering, School of Engineering, RMIT University, 3001, Melbourne, Australia"}], "References": []}, {"ArticleId": 84671132, "Title": "Decentralized guaranteed cost control with\n H\n \n ∞\n \n performance for large‐scale web‐winding system", "Abstract": "<p>In this paper, a decentralized guaranteed cost H <sub> ∞ </sub> control strategy is proposed for large-scale web-winding systems with uncertain and time-varying parameters. First, the large-scale web-winding system is viewed as a synthetic system composed of several dynamic subsystems, and interval matrix is used to deal with parameter uncertainties and the changes of set point. Second, a decentralized guaranteed cost control (DGCC) is developed to achieve the track goal, and guarantees that a cost function defined for the control system has an upper bound. Furthermore, an optimal DGCC (ODGCC) is designed to minimize the upper bound. Third, H <sub> ∞ </sub> control strategy is incorporated in the DGCC to increase closed-loop system robustness to parameter uncertainties and external disturbances. Finally, some simulation and experiments are performed on a three-motor web-winding system to verify the effectiveness of the presented control methods.</p>", "Keywords": "decentralized guaranteed cost control (DGCC);decentralized guaranteed cost H∞ (DGC H∞) control;linear matrix inequality (LMI);robustness;web-winding system", "DOI": "10.1002/asjc.2442", "PubYear": 2022, "Volume": "24", "Issue": "1", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China; School of Information, Hunan University of Humanities, Science and Technology, Loudi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China"}], "References": []}, {"ArticleId": 84671139, "Title": "Data on the genome and proteome profiles of ciprofloxacin-resistant Acholeplasma laid<PERSON>ii strains selected under different conditions in vitro", "Abstract": "Acholeplasma laidlawii is widespread hypermutable bacteria (class Mollicutes) capable of infecting humans, animals, plants, which is the main contaminant of cell cultures and vaccine preparations. The mechanisms of the development of antimicrobial resistance of this bacterium are associated with the secretion of extracellular vesicles, which can mediate the lateral transfer of antibiotic resistance determinants. We compared the genome profiles of ciprofloxacin-resistant A.laidlawii strains PG8r1 (MIC 10 µg/ml) and PG8r3 (MIC 10 µg/ml) selected under different in vitro conditions - when ciprofloxacin-sensitive (MIC 0.5 µg/ml) A.laidlawii PG8B strain was cultured at increasing concentrations of ciprofloxacin in a broth medium alone, and with vesicles derived from the ciprofloxacin-resistant (MIC 20 µg/ml) A.laidlawii PG8R<sub>10</sub>c-2 strain, respectively. Genome profiles of PG8c-3 (obtained from a single colony of the strain PG8B) and PG8R<sub>10</sub>c-2 were analyzed too. Patterns of the quinolone target genes (gyrA, gyrB, parE, parC ) containing in extracellular vesicles of PG8c-3, PG8R<sub>10</sub>c-2, PG8r1 and PG8r3 were determined. Genome sequencing was performed on the NextSeq Illumina platform. Search and annotation of single nucleotide polymorphisms were performed using Samtools and SnpEff, respectively. We also compared cellular proteomes of PG8c-3, PG8r1 and PG8r3. The cellular proteome profiles of the A. laidlawii strains were determined by two-dimensional gel electrophoresis and MALDI-TOF/TOF MS. This work presents data on single nucleotide polymorphisms (SNPs) found in the genomes of the ciprofloxacin-resistant strains selected under different in vitro conditions and proteins that were differentially expressed in the cells of ciprofloxacin-resistant strains selected under different conditions in vitro .", "Keywords": "Acholeplasma <PERSON>ii ; Ciprofloxacin-resistant strains, Genomes ; Proteomes", "DOI": "10.1016/j.dib.2020.106412", "PubYear": 2020, "Volume": "33", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kazan Institute of Biochemistry and Biophysics, FRC Kazan Scientific Center of RAS, Russia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Kazan Institute of Biochemistry and Biophysics, FRC Kazan Scientific Center of RAS, Russia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Kazan Institute of Biochemistry and Biophysics, FRC Kazan Scientific Center of RAS, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Kazan (Volga Region) Federal University, Russia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Kazan Institute of Biochemistry and Biophysics, FRC Kazan Scientific Center of RAS, Russia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Kazan Institute of Biochemistry and Biophysics, FRC Kazan Scientific Center of RAS, Russia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Kazan Institute of Biochemistry and Biophysics, FRC Kazan Scientific Center of RAS, Russia"}], "References": []}, {"ArticleId": 84671241, "Title": "A scale for credibility evaluation of scientific websites: findings from a cross-contextual approach", "Abstract": "Purpose Due to the extreme importance of evaluating the credibility of information available on a huge number of scientific websites, the present research aimed to develop a measurement scale based on a validated questionnaire incorporating the novel conceptualization of the concept credibility by <PERSON><PERSON><PERSON> and <PERSON> (1951). Design/methodology/approach Regarding the descriptive and survey nature of the research, the data were collected based on a stratified random sampling among the 672 students in two different contexts at five top nonmedical universities and, with a one-year interval, at three top medical universities in Iran. High reliability and construct validity were reported by testing the convergent and discriminant validity of the main instrument. Findings Confirmatory factor analysis (CFA) resulted in a scale named SWCRED including eight components and 28 items. Considering path coefficients (?) and t -statistics ( t -value), a significant relationship was reported between all components and the main variable of credibility in the final scale at a 99% confidence level ( t  = 2.57). Practical implications Results suggest that the information credibility of scientific websites, especially in the university contexts, can be measured by asking participants to rate how well eight components represent content including ethics, writing style, website appearance, website identity, professional information, accuracy, usability and interaction. The scale has a goodness of fit from the different indices and is of high validity for use in different educational and research settings. Originality/value The framework underlying the research has the required quality integrating a set of most important criteria for exploring the credibility evaluation of scientific web information by the students, which is useful for future-related studies.", "Keywords": "Online information;Scale development;Web information;Credibility evaluation;Academic users;Scientific websites", "DOI": "10.1108/OIR-04-2020-0127", "PubYear": 2020, "Volume": "44", "Issue": "7", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Knowledge Science, Faculty of Education and Psychology, Shahid Beheshti University , Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Public Administration, University of Tehran , Tehran, Iran"}], "References": []}, {"ArticleId": 84671255, "Title": "Critical Barriers in Software Outsourcing Vendor Organizations and Their Impacts on Software Outsourcing Clients: A Systematic Literature Review", "Abstract": "Globalization and technological advancement have driven up the demand for software as many enterprises seek to gain a competitive edge through efficient business operations. Whereas software provides this capacity, the resources involved in software development sometimes provide a stumbling block, especially for startups and smaller businesses. Software development outsourcing or offshore software development offers a viable alternative for these enterprises. Software development outsourcing is a contractual agreement between a client and vendor organization (s), who then provide part or all of the software development related services at an agreed fee paid by the client. Despite the apparent importance of offshore software development, there is limited research literature that explores the general practices in software development outsourcing. Besides, identifying barriers that often present a significant challenge for client organizations has received a little interest from researchers. This paper presents a Systematic Literature Review (SLR) of 68 research articles to identify the critical barriers for offshore software development outsourcing vendors. In order to increase the internal validity of the reported findings, the articles selected for the review were taken from three continents; Africa, Asia and Europe. The critical barriers established from the study included communication problems, cultural barriers and incompatibility with the client’s requirements. From the findings, it is recommended that vendors address these barriers for an improved relationship between themselves and the clients. Moreover, addressing these barriers offer the vendors with an opportunity to position themselves strategically within the offshore outsourcing industry. Furthermore, the findings provide meaningful literature in the continuum of Software Development Outsourcing for Software project managers, researchers and other participants in the field. More importantly, the presented literature offers a starting point for generalizing the reported findings within the context of Software Development Outsourcing.", "Keywords": "Offshore Software Development;Outsourcing Vendors;Systematic Literature Review", "DOI": "10.3844/jcssp.2020.1346.1354", "PubYear": 2020, "Volume": "16", "Issue": "10", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "AlMaarefa University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Princess <PERSON><PERSON><PERSON> University for Technology Amman"}], "References": []}, {"ArticleId": 84671282, "Title": "The Impact of eHRM Practice on Organizational Performance: Investigating the Effect of Job Satisfaction of HRM Professionals", "Abstract": "In this study, an integrated eVALUE model is introduced to examine the relationship between organizational performance and electronic Human Resource Management (eHRM) practice. The model also explored the drivers of electronic Human Resource Management (eHRM) practice in the organizations. The primary data collected by using a pilot survey and closed-ended questionnaire designed on Likert 7-point scale. Simple random sampling was adopted to yield 674 acceptable responses from 165 organizations in Bangladesh. The structural equation modeling results indicate that only six drivers out of nine namely senior executives’ attitude, IT skill of HRM professionals, perceived compatibility, senior leadership support, firm culture and competitive pressure are revealed a strong positive effect on eHRM practice. The study revealed a positive and significant relationship between eHRM practice and organizational performance. Moreover, the effect of job satisfaction of HRM professionals is observed to partially mediate on practice - performance relationship. The practical implication of the outcome of study and the extent of further research are postulates at the conclusion. © 2020 <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON>. All Rights Reserved.", "Keywords": "Drivers; eHRM; Evolutionary Game Theory; Human-Organization-Technology (HOT)-Fit Model; Job Satisfaction; Organizational Performance; Resource-Based View; Technology-Organization-Environment (TOE) Framework", "DOI": "10.3844/jcssp.2020.983.1000", "PubYear": 2020, "Volume": "16", "Issue": "7", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, International Islamic University Chittagong, Chittagong, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, International Islamic University Chittagong, Chittagong, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Bangladesh Army International University of Science and Technology, Cumilla, Bangladesh"}, {"AuthorId": 4, "Name": "Loo-<PERSON>", "Affiliation": "Department of Administrative Studies and Politics, University of Malaya, Kuala Lumpur, Malaysia"}], "References": []}, {"ArticleId": ********, "Title": "Adopting Intelligent Modelling to Trace Fault in Underground Optical Network: A Comprehensive Survey", "Abstract": "Aiming at building new global optical network infrastructure optimized with fault tracing capabilities, light transmission monitoring, packets re-routing and reconfigurations require an intelligent optical transmission system. An intelligent approach to solve the increasingly complex problems, to enhance fault tracing in the underground optical network infrastructure need to be adopted. For over forty decades now, Optical Time Domain Reflectometry (OTDR) technology has been used to determine faults distance in Fiber Optic Cable (FOC). When it comes to underground optical networking, using OTDR measurements to trace fault on the earth surface takes much longer time since the device only measures the length of underground FOC from the optical transmitter to the point of the fault. Finding the exact spot of fault on earth is a complicated task due to several factors identified in this study. A comprehensive review of previous papers on how OTDR device and other scientific techniques have been used to trace faults in underground FOC were presented in this study. Due to the identified drawbacks in the OTDR to precisely trace fault in underground FOC networks, an intelligent fault tracing technique has been proposed to aid the process. The objective of this paper sought to conduct a comprehensive systematic review of previous studies on fault-finding techniques in underground FOC. To give a clear view of the available technologies used to conduct fault finding activities in underground FOC. The available intelligent systems and the possible future directions of tracing faults in FOC promptly and in a more economical manner.", "Keywords": "Fiber Optics Cable;Artificial Intelligence;Predictive Model;Underground Optical Networks;Machine Learning and OTDR", "DOI": "10.3844/jcssp.2020.1355.1366", "PubYear": 2020, "Volume": "16", "Issue": "10", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Energy and Natural Resources"}, {"AuthorId": 2, "Name": "Adebayo F. <PERSON>ekoya", "Affiliation": "The University of Energy and Natural Resources"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Energy and Natural Resources"}], "References": []}, {"ArticleId": 84671340, "Title": "Sensitivity analysis for network observations with applications to inferences of social influence effects", "Abstract": "Abstract <p>The validity of network observations is sometimes of concern in empirical studies, since observed networks are prone to error and may not represent the population of interest. This lack of validity is not just a result of random measurement error, but often due to systematic bias that can lead to the misinterpretation of actors’ preferences of network selections. These issues in network observations could bias the estimation of common network models (such as those pertaining to influence and selection) and lead to erroneous statistical inferences. In this study, we proposed a simulation-based sensitivity analysis method that can evaluate the robustness of inferences made in social network analysis to six forms of selection mechanisms that can cause biases in network observations—random, homophily, anti-homophily, transitivity, reciprocity, and preferential attachment. We then applied this sensitivity analysis to test the robustness of inferences for social influence effects, and we derived two sets of analytical solutions that can account for biases in network observations due to random, homophily, and anti-homophily selection.</p>", "Keywords": "", "DOI": "10.1017/nws.2020.36", "PubYear": 2021, "Volume": "9", "Issue": "1", "JournalId": 25796, "JournalTitle": "Network Science", "ISSN": "2050-1242", "EISSN": "2050-1250", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Algorithm for Compressing/Decompressing Sudoku Grids", "Abstract": "We describe a way to transfer efficiently Sudoku grids through the Internet. This is done by using linearization together with compression and decompression that use the information structure present in all sudoku grids. The compression and the corresponding decompression are based on the fact that in each Sudoku grid there are information dependencies and so some of the information is redundant. © 2020 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and Kimkhung Sov.", "Keywords": "Algorithms; Compression; Data structure; Decompression; Sudoku", "DOI": "10.3844/jcssp.2020.1319.1324", "PubYear": 2020, "Volume": "16", "Issue": "9", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information System, Botswana International University of Science and Technology, Botswana"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Computer and Telecommunications Engineering, Botswana International University of Science and Technology, Private Bag 16, Palapye, Botswana"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Zaman University, Cambodia"}, {"AuthorId": 4, "Name": "Daravisal Dy", "Affiliation": "Department of Computer Science, Zaman University, Cambodia"}, {"AuthorId": 5, "Name": "<PERSON>kh<PERSON> Sov", "Affiliation": "Department of Computer Science, Zaman University, Cambodia"}], "References": []}, {"ArticleId": 84671359, "Title": "Using Expert Evaluation to As<PERSON>s the Implementation of Persuasive Design in e-Commerce", "Abstract": "", "Keywords": "", "DOI": "10.3844/jcssp.2020.1393.1400", "PubYear": 2020, "Volume": "16", "Issue": "9", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "Amjad Al-mutairi", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84671523, "Title": "E‐government through the lens of trading zones: A case of e‐government implementation in Dubai", "Abstract": "<p>The paper explores the implementation of public sector information systems in a relatively atypical context, which is the rapidly developing city of Dubai. The paper specifically addresses the interactions between public and private sector teams in a large public sector organization in Dubai as they collaborate to complete an e‐government project within a short timeframe. The paper employs a framework that consists of institutional logics and concepts from the theory of trading zones to examine this exchange of ideas and artifacts between the two teams. Trading zones are conceptualized as embedded within wider institutional arrangements. The paper explores the trading activities between the teams as institutional enactments that come to shape e‐government outcomes in terms of roles and norms of the public sector staff. This reveals specific insights into how e‐government projects are locally enacted and how these shape the roles of public sector staff as contributors to city development. Overall, the paper argues that these engagements between public sector staff and contracted private sector consultants and vendors lead to the exchange of new knowledge, ideas, and artifacts that reproduce and reframe the sociocultural roles of public sector staff to fit with the new organizational context.</p>", "Keywords": "e‐government;enactment;implementation;institutional logics;trading zones", "DOI": "10.1002/isd2.12156", "PubYear": 2021, "Volume": "87", "Issue": "2", "JournalId": 6046, "JournalTitle": "The Electronic Journal of Information Systems in Developing Countries", "ISSN": "1681-4835", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Noora H<PERSON>", "Affiliation": "Department of Information Systems, College of Information Technology, University of Bahrain, Sakheer, Bahrain"}], "References": [{"Title": "ICT4D 3.0? Part 2—The patterns of an emerging “digital‐for‐development” paradigm", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "3", "Page": "", "JournalTitle": "The Electronic Journal of Information Systems in Developing Countries"}]}, {"ArticleId": 84671658, "Title": "The Grasp Strategy of a Robot Passer Influences Performance and Quality of the Robot-Human Object Handover", "Abstract": "Task-aware robotic grasping is critical if robots are to successfully cooperate with humans. The choice of a grasp is multi-faceted; however, the task to perform primes this choice in terms of hand shaping and placement on the object. This grasping strategy is particularly important for a robot companion, as it can potentially hinder the success of the collaboration with humans. In this work, we investigate how different grasping strategies of a robot passer influence the performance and the perceptions of the interaction of a human receiver. Our findings suggest that a grasping strategy that accounts for the subsequent task of the receiver improves substantially the performance of the human receiver in executing the subsequent task. The time to complete the task is reduced by eliminating the need of a post-handover re-adjustment of the object. Furthermore, the human perceptions of the interaction improve when a task-oriented grasping strategy is adopted. The influence of the robotic grasp strategy increases as the constraints induced by the object's affordances become more restrictive. The results of this work can benefit the wider robotics community, with application ranging from industrial to household human-robot interaction for cooperative and collaborative object manipulation.", "Keywords": "Human-robot interaction (HRI); Human-robot collaboration (HRC); Seamless interaction; Task-oriented grasping; Object handover", "DOI": "10.3389/frobt.2020.542406", "PubYear": 2020, "Volume": "7", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Extreme Robotics Laboratory, School of Metallurgy and Materials, University of Birmingham, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The BioRobotics Institute, Scuola Superiore Sant'Anna, Italy;Department of Excellence in Robotics and Artificial Intelligence (AI), Scuola Superiore Sant'Anna, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Extreme Robotics Laboratory, School of Metallurgy and Materials, University of Birmingham, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Extreme Robotics Laboratory, School of Metallurgy and Materials, University of Birmingham, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Extreme Robotics Laboratory, School of Metallurgy and Materials, University of Birmingham, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Australian Research Council (ARC) Centre of Excellence for Robotic Vision, Queensland University of Technology, Australia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "The BioRobotics Institute, Scuola Superiore Sant'Anna, Italy;Department of Excellence in Robotics and Artificial Intelligence (AI), Scuola Superiore Sant'Anna, Italy"}], "References": [{"Title": "An Affordance and Distance Minimization Based Method for Computing Object Orientations for Robot Human Handovers", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "1", "Page": "143", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Learning robust, real-time, reactive robotic grasping", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2-3", "Page": "183", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 84671709, "Title": "Extraction of Hierarchical Behavior Patterns Using a Non-parametric Bayesian Approach", "Abstract": "Extraction of complex temporal patterns, such as human behaviors, from time series data is a challenging yet important problem. The double articulation analyzer has been previously proposed by <PERSON><PERSON><PERSON> et al. to discover a hierarchical structure that leads to complex temporal patterns. It segments time series into hierarchical state subsequences, with the higher level and the lower level analogous to words and phonemes, respectively. The double articulation analyzer approximates the sequences in the lower level by linear functions. However, it is not suitable to model real behaviors since such a linear function is too simple to represent their non-linearity even after the segmentation. Thus, we propose a new method that models the lower segments by fitting autoregressive functions that allows for more complex dynamics, and discovers a hierarchical structure based on these dynamics. To achieve this goal, we propose a method that integrates the beta process - autoregressive hidden Markov model and the double articulation by nested Pitman-Yor language model. Our results showed that the proposed method extracted temporal patterns in both low and high levels from synthesized datasets and a motion capture dataset with smaller errors than those of the double articulation analyzer.", "Keywords": "Behavioral pattern; non-parametric Bayesian approach; segmentation; hierarchical structure; dynamics", "DOI": "10.3389/fcomp.2020.546917", "PubYear": 2020, "Volume": "2", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Japan"}], "References": []}, {"ArticleId": 84671790, "Title": "Virtual Monoenergetic CT Imaging via Deep Learning", "Abstract": "Conventional single-spectrum computed tomography (CT) reconstructs a spectrally integrated attenuation image and reveals tissues morphology without any information about the elemental composition of the tissues. Dual-energy CT (DECT) acquires two spectrally distinct datasets and reconstructs energy-selective (virtual monoenergetic [VM]) and material-selective (material decomposition) images. However, DECT increases system complexity and radiation dose compared with single-spectrum CT. In this paper, a deep learning approach is presented to produce VM images from single-spectrum CT images. Specifically, a modified residual neural network (ResNet) model is developed to map single-spectrum CT images to VM images at pre-specified energy levels. This network is trained on clinical DECT data and shows excellent convergence behavior and image accuracy compared with VM images produced by DECT. The trained model produces high-quality approximations of VM images with a relative error of less than 2%. This method enables multi-material decomposition into three tissue classes, with accuracy comparable with DECT.", "Keywords": "computed tomography ; machine learning ; virtual monoenergetic imaging ; multi-material decomposition ; MMD ; VM ; CT", "DOI": "10.1016/j.patter.2020.100128", "PubYear": 2020, "Volume": "1", "Issue": "8", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biomedical Imaging Center, Center for Biotechnology & Interdisciplinary, Department of Biomedical Engineering, School of Engineering, Rensselaer Polytechnic Institute, Troy, NY 12180, USA"}, {"AuthorId": 2, "Name": "Yan Xi", "Affiliation": "Shanghai First-Imaging Tech, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "GE Research, One Research Circle, Niskayuna, NY 12309, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "GE Research, One Research Circle, Niskayuna, NY 12309, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Biomedical Imaging Center, Center for Biotechnology & Interdisciplinary, Department of Biomedical Engineering, School of Engineering, Rensselaer Polytechnic Institute, Troy, NY 12180, USA;Corresponding author"}], "References": [{"Title": "Determining the photon interaction parameters of iodine compounds as contrast agents for use in radiology", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "252", "JournalTitle": "Journal of Radiation Research and Applied Sciences"}]}, {"ArticleId": 84671845, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-7051(20)30670-5", "PubYear": 2020, "Volume": "209", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [], "References": []}, {"ArticleId": 84671904, "Title": "Data mining privacy preserving: Research agenda", "Abstract": "<p>In the modern days, the amount of the data and information is increasing along with their accessibility and availability, due to the Internet and social media. To be able to search this vast data set and to discover unknown useful data patterns and predictions, the data mining method is used. Data mining allows for unrelated data to be connected in a meaningful way, to analyze the data, and to represent the results in the form of useful data patterns and predictions that help and predict future behavior. The process of data mining can potentially violate sensitive and personal data. Individual privacy is under attack if some of the information leaks and reveals the identity of a person whose personal data were used in the data mining process. There are many privacy‐preserving data mining (PPDM) techniques and methods that have a task to preserve the privacy and sensitive data while providing accurate data mining results at the same time. PPDM techniques and methods incorporate different approaches that protect data in the process of data mining. The methodology that was used in this article is the systematic literature review and bibliometric analysis. This article identifieds the current trends, techniques, and methods that are being used in the privacy‐preserving data mining field to make a clear and concise classification of the PPDM methods and techniques with possibly identifying new methods and techniques that were not included in the previous classification, and to emphasize the future research directions.</p> <p>This article is categorized under: Commercial, Legal, and Ethical Issues > Security and Privacy </p> <h3 >Abstract</h3> <p>Classification of PPDM techniques and methods <picture> </picture> <p ></p> </p>", "Keywords": "bibliometric analysis;data mining;privacy preserving data mining;privacy preserving methods;privacy preserving techniques", "DOI": "10.1002/widm.1392", "PubYear": 2021, "Volume": "11", "Issue": "1", "JournalId": 3682, "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery", "ISSN": "1942-4787", "EISSN": "1942-4795", "Authors": [{"AuthorId": 1, "Name": "Inda Kreso", "Affiliation": "School of Economics and Business, University of Sarajevo, Sarajevo, Bosnia and Herzegovina"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Business, University of Sarajevo, Sarajevo, Bosnia and Herzegovina"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 84671920, "Title": "Two-level clustering of UML class diagrams based on semantics and structure", "Abstract": "Context The reuse of software design has been an important issue of software reuse. UML class diagrams are widely applied in software design and has become DE factor standard. As a result, the reuse of UML class diagrams has received more attention. With the increasing number of class diagrams stored in reuse repository, their retrieval becomes a time-consuming job. The clustering can narrow down retrieval range and improve the retrieval efficiency. But few efforts have been done in clustering UML class diagrams. This paper tries to propose a clustering approach for UML class diagrams. Objective This paper proposes a two-level clustering of UML class diagrams, namely, semantic clustering and structural clustering. The UML class diagrams stored in reuse repository are clustered into a few domains based on semantics in the first level and a few categories based on structure in the second level. Method We propose a clustering algorithm named CUFS , in which the idea of partitioning and hierarchical clustering is combined and feature similarity is proposed for the similarity measure between two clusters in order to merge clusters. A better feature representation of a cluster, namely, feature class diagram, is proposed in this paper. In order to form each sub-cluster, the semantic and structural similarities between UML class diagrams are defined, respectively. Results A series of experimental results show that, the proposed feature similarity measure not only speeds up the clustering process, but also expresses the closeness degree between clusters for merging clusters. The proposed algorithm shows a good clustering quality and efficiency under the condition of different size and distribution of UML class diagrams. Conclusion It is concluded that the proposed two-level clustering method considers both semantics and structure contained in a class diagram, which can flexibly adapt to different clustering requirements. Also, the proposed clustering algorithm performs better than other related algorithms, regardless of in semantic, structural and hybrid clustering.", "Keywords": "Reuse ; UML class diagram ; Retrieval ; Clustering ; Similarity measure ; Feature similarity", "DOI": "10.1016/j.infsof.2020.106456", "PubYear": 2021, "Volume": "130", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science & Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 211106, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>chen <PERSON>", "Affiliation": "School of Chemical Process Automation, Shenyang University of Technology, Liaoyang 111004, China;School of Software, Northeastern University, Shenyang 110819, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Science & Technology, Nanjing University of Aeronautics and Astronautics, Nanjing 211106, China"}], "References": [{"Title": "Structural similarity measure between UML class diagrams based on UCG", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "2", "Page": "213", "JournalTitle": "Requirements Engineering"}]}, {"ArticleId": 84672099, "Title": "Convergence of Gaussian Process Regression with Estimated Hyper-Parameters and Applications in Bayesian Inverse Problems", "Abstract": "This work is concerned with the convergence of Gaussian process regression. A particular focus is on hierarchical Gaussian process regression, where hyper-parameters appearing in the mean and covariance structure of the Gaussian process emulator are a-priori unknown and are learned from the data, along with the posterior mean and covariance. We work in the framework of empirical Bayes, where a point estimate of the hyper-parameters is computed, using the data, and then used within the standard Gaussian process prior to posterior update. We provide a convergence analysis that (i) holds for a given, deterministic function f to be emulated, and (ii) shows that convergence of Gaussian process regression is unaffected by the additional learning of hyper-parameters from data and is guaranteed in a wide range of scenarios. As the primary motivation for the work is the use of Gaussian process regression to approximate the data likelihood in Bayesian inverse problems, we provide a bound on the error introduced in the Bayesian posterior distribution in this context. © 2020 Society for Industrial and Applied Mathematics and American Statistical Association.", "Keywords": "inverse problem; Bayesian inference; surrogate model; Gaussian process regression; posterior consistency; hierarchical; empirical Bayes; 60G15; 62G08; 62J07; 65D05; 65D30; 65J22", "DOI": "10.1137/19M1284816", "PubYear": 2020, "Volume": "8", "Issue": "4", "JournalId": 11089, "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification", "ISSN": "", "EISSN": "2166-2525", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, James Clerk Maxwell Building, University of Edinburgh, Edinburgh, EH9 3FD, United Kingdom"}], "References": []}, {"ArticleId": ********, "Title": "Comparative analysis between surrounding viscoelastic media on the buckling characteristics of nanobeams", "Abstract": "<p>The static and dynamic responses of axially loaded nanobeams with surrounding viscoelastic media are investigated and discussed. Size-dependent or nonlocal effects are accounted for via <PERSON><PERSON>’s nonlocal elasticity theory. Additionally, the system is studied for various boundary conditions including clamped–clamped, clamped-hinged, and hinged-hinged. To study the interaction between the nanobeam and the surrounding media, several viscoelastic media are considered including the two-element Kelvin-Voight and Maxwell models and the three-element Standard Solid I, Standard Solid II, Standard Fluid I, and Standard Fluid II models. The governing equations of motion are obtained via the <PERSON>ule<PERSON>–<PERSON> beam theory and extended <PERSON>’s principle and by considering each viscoelastic medium as a distributed load along the length of the nanobeam. After deriving and nondimensionalizing all governing equations, a static analysis is performed to determine the critical buckling loads for each of the surrounding viscoelastic media. Additionally, a linear dynamic analysis is employed to study the effect of the surrounding medium on the system’s linear natural frequency in the pre- and post-buckling regimes. Finally, an in-depth study on the elastic and viscous coefficients of each model depicts how the changes to these parameters can affect both the static and dynamic responses of the proposed system. A thorough study of axially loaded nanobeams embedded within all six viscoelastic media is not present in the literature, thus motivating this effort.</p>", "Keywords": "", "DOI": "10.1007/s00542-020-05049-4", "PubYear": 2021, "Volume": "27", "Issue": "8", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, New Mexico State University, Las Cruces, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, New Mexico State University, Las Cruces, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, New Mexico State University, Las Cruces, USA"}], "References": []}, {"ArticleId": 84672415, "Title": "keras_dna: a wrapper for fast implementation of deep learning models in genomics", "Abstract": "Summary \n Prediction of genomic annotations from DNA sequences using deep learning is today becoming a flourishing field with many applications. Nevertheless, there are still difficulties in handling data in order to conveniently build and train models dedicated for specific end-user’s tasks. keras_dna is designed for an easy implementation of Keras models (TensorFlow high level API) for genomics. It can handle standard bioinformatic files formats as inputs such as bigwig, gff, bed, wig, bedGraph or fasta and returns standardized inputs for model training. keras_dna is designed to implement existing models but also to facilitate the development of news models that can have single or multiple targets or inputs.\n \n \n Availability and implementation \n Freely available with a MIT License using pip install keras_dna or cloning the github repo at https://github.com/etirouthier/keras_dna.git.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btaa929", "PubYear": 2021, "Volume": "37", "Issue": "11", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sorbonne Universite, CNRS, Laboratoire de Physique Théorique de la Matière Condensée (LPTMC), Paris F-75252, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sorbonne Universite, CNRS, Laboratoire de Physique Théorique de la Matière Condensée (LPTMC), Paris F-75252, France;Muséum National d’Histoire Naturelle, Structure et Instabilité des Génomes, UMR7196, Paris 75231, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sorbonne Universite, CNRS, Laboratoire de Physique Théorique de la Matière Condensée (LPTMC), Paris F-75252, France;Muséum National d’Histoire Naturelle, Structure et Instabilité des Génomes, UMR7196, Paris 75231, France"}], "References": [{"Title": "Genome annotation across species using deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 84672712, "Title": "Exploring Implicit and Explicit Geometrical Structure of Data for Deep Embedded Clustering", "Abstract": "<p>Clustering is an essential data analysis technique and has been studied extensively over the last decades. Previous studies have shown that data representation and data structure information are two critical factors for improving clustering performance, and it forms two important lines of research. The first line of research attempts to learn representative features, especially utilizing the deep neural networks, for handling clustering problems. The second concerns exploiting the geometric structure information within data for clustering. Although both of them have achieved promising performance in lots of clustering tasks, few efforts have been dedicated to combine them in a unified deep clustering framework, which is the research gap we aim to bridge in this work. In this paper, we propose a novel approach, Manifold regularized Deep Embedded Clustering (MDEC), to deal with the aforementioned challenge. It simultaneously models data generating distribution, cluster assignment consistency, as well as geometric structure of data in a unified framework. The proposed method can be optimized by performing mini-batch stochastic gradient descent and back-propagation. We evaluate MDEC on three real-world datasets (USPS, REUTERS-10K, and MNIST), where experimental results demonstrate that our model outperforms baseline models and obtains the state-of-the-art performance.</p>", "Keywords": "Deep neural networks; Stacked autoencoder; Manifold constraint; Clustering", "DOI": "10.1007/s11063-020-10375-9", "PubYear": 2021, "Volume": "53", "Issue": "1", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Chongqing University of Technology, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "L3S Research Center, Leibniz University of Hannover, Hannover, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Science, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information, Renmin University of China, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Knowledge Technologies for the Social Sciences, Leibniz Institute for the Social Sciences, Cologne, Germany"}], "References": []}, {"ArticleId": 84672723, "Title": "Development of a Situational Awareness Estimation Model Considering Traffic Environment for Unscheduled Takeover Situations", "Abstract": "Abstract \nIn semi-autonomous vehicles (SAE level 3) that requires drivers to takeover (TO) the control in critical situations, a system needs to judge if the driver have enough situational awareness (SA) for manual driving. We previously developed a SA estimation system that only used driver’s glance data. For deeper understanding of driver’s SA, the system needs to evaluate the relevancy between driver’s glance and surrounding vehicle and obstacles. In this study, we thus developed a new SA estimation model considering driving-relevant objects and investigated the relationship between parameters. We performed TO experiments in a driving simulator to observe driver’s behavior in different position of surrounding vehicles and TO performance such as the smoothness of steering control. We adopted support vector machine to classify obtained dataset into safe and dangerous TO, and the result showed 83% accuracy in leave-one-out cross validation. We found that unscheduled TO led to maneuver error and glance behavior differed from individuals.", "Keywords": "Autonomous driving; Situational awareness; Cognitive behavior; Unscheduled takeover", "DOI": "10.1007/s13177-020-00231-4", "PubYear": 2021, "Volume": "19", "Issue": "1", "JournalId": 6726, "JournalTitle": "International Journal of Intelligent Transportation Systems Research", "ISSN": "1348-8503", "EISSN": "1868-8659", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Modern Mechanical Engineering, Waseda University, Tokyo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Modern Mechanical Engineering, Waseda University, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Institute for Science and Engineering(RISE), Waseda University, Tokyo, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Modern Mechanical Engineering, Waseda University, Tokyo, Japan"}], "References": []}, {"ArticleId": 84672784, "Title": "Multi-platform multimedia data analysis and modeling", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-020-10017-1", "PubYear": 2020, "Volume": "79", "Issue": "45-46", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [], "References": []}, {"ArticleId": 84672811, "Title": "Model Optimization for A Dynamic Rail Transport System on an Asymmetric Multi-Core System", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/18952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84672814, "Title": "A Comparative Analysis of Machine Learning Algorithms Used For Training in Face Recognition", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/67952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 84672815, "Title": "An overview of Quantum Cryptography and <PERSON><PERSON><PERSON>s Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.30534/ijatcse/2020/82952020", "PubYear": 2020, "Volume": "9", "Issue": "5", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}]