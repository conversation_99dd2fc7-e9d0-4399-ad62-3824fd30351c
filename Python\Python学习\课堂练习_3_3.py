# score = int(input("请输入你的成绩："))
# if score >= 60:
#     print("合格")
#     if score >= 80:
#         print("优秀")
#     else:
#         print("一般")
# else:
#     print("不合格")
#     if score < 30:
#         print("差生")
#     else:
#         print("可救")

# monthly_salary = float(input("请输入你的月工资："))
# if monthly_salary <= 500:
#     print("欢迎进入史塔克穷人帮前三名")
#     if monthly_salary <= 100:
#         print('恭喜您荣获“美元队长”称号')
#     else:
#         print("请找弗瑞队长加薪")
# elif 500 < monthly_salary <= 1000:
#     print("温饱")
# else:
#     print("经济危机难不倒您")
#     if monthly_salary <= 20000:
#         print("您是亿万富翁")
#     else:
#         print("您是亿万富翁中的亿万富翁")

# def text_return():
#   return 1,2,3
# x,y,z = text_return()
# print(x,y,z)

def test_function(compute):
    res = compute(1, 2)
    print(res)
test_function(lambda x, y: x + y)