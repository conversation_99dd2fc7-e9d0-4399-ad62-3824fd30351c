import orjson
import os

def read_json(filepath):

    with open(filepath, 'r', encoding='utf-8') as f:
        data = orjson.loads(f.read()) # 获取的是list，list的元素是dict
        print(data[0]['Keywords'])
        print(type(data[0]['Keywords']))
        print(len(data))

def read_all_json(folder_path):
    
    folder = os.listdir(folder_path)
    for file in folder:
        read_json(folder_path + '/' + file)


if __name__ == '__main__':
    read_json('JSON/test_json/0a2bd635e05d4d768ee42968cb759011.json')