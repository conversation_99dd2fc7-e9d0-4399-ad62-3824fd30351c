[{"ArticleId": ********, "Title": "Cost-based analysis and optimization of distributed generations and shunt capacitors incorporated into distribution systems with nonlinear demand modeling", "Abstract": "This paper optimizes the allocations of Distributed Generators (DG) and Shunt Capacitor (SC) banks to minimize a multi-objective problem consisting of four different indices. These indices include active/reactive power loss reduction, voltage profile improvement, and system stability enhancement. Consequently, the costs of energy loss, allocated devices, and total operation are estimated per year for different installed DG/SC pairs for studied systems. Multi-DG and SC banks are optimally installed in the standard 69-bus and 136-bus radial distribution systems considering six nonlinear load models. The six models are generated from the nonlinear relationship between the system voltage and demand. Furthermore, the load type affects the DG/SC allocation optimization problem and other performance parameters such as the total voltage deviation (TVD), stability index (SI), and the power drawn from the utility. A new metaheuristic optimization called the Jellyfish Search Algorithm (JSA) is adopted and applied to allocate the DG units and the SC banks optimally into the distribution systems. The savings in the annual energy loss reach 65.88%, 95.99%, and 97.91% with industrial load demand for 1DG/1SC, 2DG/2SC, and 3DG/3SC allocations, respectively for the 69-bus RDS. While for the 136-bus RDS, the saving reaches 39.71% with constant-impedance demand, 58.84%, and 73.87% with commercial demand for 1DG/1SC, 3DG/3SC, and 6DG/6SC pairs, respectively. It is found that the JSA is practical and suitable for solving these nonlinear optimization problems, and it gives better results as compared to other algorithms in the literature.", "Keywords": "Cost of energy loss ; Power loss reduction ; Jellyfish search algorithm ; Multi-objective optimization ; Total operating cost", "DOI": "10.1016/j.eswa.2022.116844", "PubYear": 2022, "Volume": "198", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Faculty of Engineering, Aswan University, 81542 Aswan, Egypt;Department of Electrical Engineering, College of Engineering, Qassim University, 56452 Unaizah, Saudi Arabia;Address: Department of Electrical Engineering, Faculty of Engineering, Aswan University, 81542 Aswan, Egypt"}], "References": []}, {"ArticleId": 93414842, "Title": "Realtime scheduling heuristics for just-in-time production in large-scale flexible job shops", "Abstract": "This study aims to enable jobs to go smoothly between shops on a production line by completing jobs in the upstream shop just in time (JIT) for the downstream shop. We propose solutions to a factory that is seeking ways for an upstream shop to complete every job at the precise time such that the downstream shop can process the job. We model the upstream shop as a flexible job shop and propose four methods that form a realtime scheduling and control system for JIT production. We first propose a method to set for each job a due date by which the job should be completed in the upstream shop. The due dates are set in such a manner that jobs would be completed JIT for the downstream shop, if they are completed JIT for their due dates. We then propose a method to estimate the minimum number of workers needed in the upstream shop for completing the jobs by their due dates. We further propose two methods that work dynamically to complete each job neither too early nor too late for its due date. One is a dispatching rule that dynamically sequences jobs in process according to urgency degree. The other is a job-selecting heuristic that dynamically assigns workers to jobs such that jobs not nearing completion will be given priority in processing. Simulations by using data from the factory show that the methods can achieve in real time (i.e. within 0.00 seconds) JIT production for a flexible job shop problem involving hundreds of operations. More extensive simulations by using a large number of randomly generated problem instances show that solutions obtained in real time by the proposed methods greatly outperform those obtained in much longer time by metaheuristics designed for solving similar problems, and that each proposed method outperforms its rivals in the literature. The findings imply that integrating fast and high-performing heuristics and rules can be a solution to solve large-scale scheduling problems in real time.", "Keywords": "Just-in-time production ; Flexible job shop ; Realtime scheduling ; Intelligent production ; Due date setting ; Dispatching rule ; Industrial case study", "DOI": "10.1016/j.jmsy.2022.01.006", "PubYear": 2022, "Volume": "63", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Liberal Arts and Science, Kanazawa University, Kakuma, Kanazawa, Ishikawa 920-1192, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information, Production and Systems, Waseda University, 2–7 Hibikino, Wakamatsu-ku, Kitakyushu, Fukuoka 808-0135, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shanghai Jiao Tong University, 800 Dongchuan Rd., Shanghai 200240, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information, Production and Systems, Waseda University, 2–7 Hibikino, Wakamatsu-ku, Kitakyushu, Fukuoka 808-0135, Japan"}], "References": [{"Title": "A flexible job shop scheduling approach with operators for coal export terminals – A mature approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "115", "Issue": "", "Page": "104834", "JournalTitle": "Computers & Operations Research"}, {"Title": "Mathematical modeling and a hybridized bacterial foraging optimization algorithm for the flexible job-shop scheduling problem with sequencing flexibility", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "74", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Mixed-integer linear programming and constraint programming formulations for solving distributed flexible job shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106347", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Scheduling pre-emptible tasks with flexible resourcing options and auxiliary resource requirements", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "106939", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 93414851, "Title": "Business intelligence and data analytics framework: case study of humanitarian organisations refugees' registration system", "Abstract": "Business intelligence and analytics has gained prominent focus among organisations with information systems that collect and process vast amounts of data. Voluminous, unprocessed data does not lend itself to offering useful insights for businesses, especially with basic statistical methods and traditional reporting techniques. In this work, we design a business intelligence and data analytics framework for refugee registration system serving over six million refugees to collect, collate and filter demographic data. The proposed reporting mechanism leverages the power of interactive dashboards to offer informative and intuitive reports and visualisations that are accessible and interpretable by stakeholders. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "Analytics; Business intelligence; Dashboards; Demographics; Reporting", "DOI": "10.1504/IJICA.2022.121384", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 23927, "JournalTitle": "International Journal of Innovative Computing and Applications", "ISSN": "1751-648X", "EISSN": "1751-6498", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> School of Computing Sciences, Princess <PERSON><PERSON><PERSON> University for Technology, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> School of Computing Sciences, Princess <PERSON><PERSON><PERSON> University for Technology, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> School of Computing Sciences, Princess <PERSON><PERSON><PERSON> University for Technology, Jordan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> School of Computing Sciences, Princess <PERSON><PERSON><PERSON> University for Technology, Jordan"}], "References": []}, {"ArticleId": 93415081, "Title": "Cooperative cache update using multi-agent recurrent deep reinforcement learning for mobile edge networks", "Abstract": "Caching the most likely to be requested content at the base stations in a cooperative manner can facilitate direct content delivery without fetching content from the remote content server and thus alleviate the user-perceived latency, reduce the burden on backhaul and minimize the duplicated content transmissions. Content popularity plays a vital role, and it drives caching on edge. In the literature, earlier works considered the content popularity either known earlier or obtained on prediction. However, the content popularity is time-varying and unknown in reality, so the above assumption makes it less practical. Therefore, this paper considers the cooperative cache replacement problem in a realistic scenario where the edge nodes are unaware of the content popularity in mobile edge networks. To address this problem, the main contribution of this paper is to design an intelligent content update mechanism using multi-agent deep reinforcement learning in dynamic environments. With the goal of maximizing the saved delay with deadline and capacity constraints, we formulate the cache replacement problem as Integer linear programming problem. Considering the dynamic nature of the content popularity, high dimensional parameters, and for an intelligent caching decision, we model the problem as a partially observable Markov decision process and present an efficient deep reinforcement learning algorithm by embedding the long short-term memory network (LSTM) into a multi-agent deep deterministic policy gradient formalism. The LSTM inclusion reduces the instability produced by partial observability of the environment. Extensive simulation results demonstrate that the proposed cooperative caching mechanism significantly improves the performance in terms of reward, acceleration ratio and hit ratio compared with existing mechanisms.", "Keywords": "Mobile edge networks ; Cooperative caching ; Multi-agent deep reinforcement learning ; Partially observable <PERSON><PERSON> decision process ; Multi-agent deep deterministic policy gradient", "DOI": "10.1016/j.comnet.2022.108876", "PubYear": 2022, "Volume": "209", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Warangal, Telangana, 506004, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Warangal, Telangana, 506004, India"}, {"AuthorId": 3, "Name": "D.V.L.N. <PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Warangal, Telangana, 506004, India;Indian Institute of Information Technology Design and Manufacturing (IIITDM) Kurnool, Andhra Pradesh, 518002, India"}], "References": [{"Title": "Contact duration-aware cooperative cache placement using genetic algorithm for mobile edge networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; D.V.L.N<PERSON>", "PubYear": 2021, "Volume": "193", "Issue": "", "Page": "108062", "JournalTitle": "Computer Networks"}, {"Title": "Multi-agent reinforcement learning for cost-aware collaborative task execution in energy-harvesting D2D networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "195", "Issue": "", "Page": "108176", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 93415104, "Title": "Gaming and the ‘Parergodic’ Work of Seriality in Interactive Digital Environments", "Abstract": "<p>Twentieth-century serial figures enacted a “parergonal” logic by crossing boundaries between various media, slipping in and out of their frames, and showing them—in accordance with a Derridean logic of the parergon—to be reversible. With the rise of interactive, networked, and convergent digital media environments, these medial logics are transformed. A figure like <PERSON> exemplifies the transition from a “parergonal” to a new “parergodic” logic; the latter term builds upon <PERSON><PERSON><PERSON>’s notion of the “ergodic” situation of gameplay—where ergodics combines the Greek ergon (work) and hodos (path), thus positing nontrivial labor as the aesthetic mode of players’ engagement with games. Ergodic media give rise to new forms of seriality that accompany, probe, and trace the developmental trajectories of the new media environment and the blurring of relations between work and play, between paid labor and the incidental work culled from our leisure and entertainment practices.</p>", "Keywords": "", "DOI": "10.7557/23.6352", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 96439, "JournalTitle": "Eludamos: Journal for Computer Game Culture", "ISSN": "", "EISSN": "1866-6124", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93415105, "Title": "Playing with Plants, Loving Computers: Queer Playfulness beyond the Human in Digital: A Love Story by <PERSON> and <PERSON>ustle Your Leaves to Me Softly by <PERSON> and <PERSON>", "Abstract": "<p>This article argues that queer playfulness sets up a utopian relationality based on desire and vulnerability between human players and their nonhuman Others. Specifically, using the indie games Rustle Your Leaves to Me Softly by <PERSON> and <PERSON> and Digital: A Love Story by <PERSON> as case studies, the article reconfigures the notion of queer playfulness from its more familiar conceptualizations in queer game studies as residing less in willful resistance and agentive subversion than in the willing subjection of the playing self to the play and pleasure of the nonhuman Other—in the case of these games, plants and computers. Thus, queerness manifests as a precarious form of desire that does not seek to and cannot master its object. Ultimately, the article posits queer playfulness as a radical decentering of the human subject and the playing ego in favor of a humble, vulnerable, and contingent form of relationality between humans and their unassimilable Others.</p>", "Keywords": "alterity;digital environments;interspecies love;nonhumans;posthumanity;queer indie games;utopia", "DOI": "10.7557/23.6364", "PubYear": 2022, "Volume": "12", "Issue": "1", "JournalId": 96439, "JournalTitle": "Eludamos: Journal for Computer Game Culture", "ISSN": "", "EISSN": "1866-6124", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "New York University Shanghai"}], "References": []}, {"ArticleId": 93415125, "Title": "Micro-expression recognition using local binary pattern from five intersecting planes", "Abstract": "<p>Micro-expression recognition has important research value and huge research difficulties. Local Binary Pattern from Three Orthogonal Planes (LBP-TOP) is a common and effective feature in micro-expression recognition. However, LBP-TOP only extracts the dynamic texture features in the horizontal and vertical directions and does not consider muscle movement in the oblique direction. In this paper, the feature in oblique directions is studied, and a new feature called Local Binary Pattern from Five Intersecting Planes (LBP-FIP) is proposed by analyzing the movement direction of facial muscles in the micro-expression video. LBP-FIP concatenates the proposed Eight Vertices LBP (EVLBP) with LBP-TOP extracted from three planes, where EVLBP is extracted from two planes in the oblique direction. In this way, the dynamic texture features in the oblique direction are extracted more directly. On the CASME II and SMIC database, we evaluated the proposed feature and the effectiveness of the features in the oblique direction. Extensive experiments prove that LBP-FIP provides more effective feature information than LBP-TOP, and extracting the features in oblique directions is discriminative for recognizing micro-expressions. Also, LBP-FIP has advantages comparing with other LBP based features and achieves satisfactory performance, especially on CASME II.</p>", "Keywords": "Micro-expression recognition; Oblique direction; Dynamic texture features; Eight vertices local binary pattern", "DOI": "10.1007/s11042-022-12360-x", "PubYear": 2022, "Volume": "81", "Issue": "15", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Jinsheng Wei", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Posts and Telecommunications, Nanjing, China"}], "References": [{"Title": "MERTA: micro-expression recognition with ternary attentions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "1", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93415375, "Title": "Preliminary Design of Seismic Isolation Systems Using Artificial Neural Networks", "Abstract": "<p>This works attempts to implement artificial neural networks (ANN) for modeling Seismic-Isolation (SI) systems consisting of Natural Rubber Bearings and Viscous Fluid Dampers subject to Near-Field (NF) earthquake ground motion. Fourteen NF earthquake records representing two seismic hazard levels are used. The commercial analysis program SAP2000 was used to perform the Time-History Analysis (THA) of the MDOF system (stick model representing a realistic five-story base-isolated building) subject to all 14 records. Different combinations of damping coefficients (c) and damping exponents (α) are investigated under the 14 earthquake records to develop the database of feasible combinations for the SI system. The total number of considered THA combinations is 350 and were used for training and testing the neural network. Mathematical models for the key response parameters are established via ANN. The input patterns used in the network included the damping coefficients (c), damping exponents (α), ground excitation (peak ground acceleration, PGA and Arias Intensity, Ia). The network was programmed to process this information and produce the key response parameters that represent the behavior of SI system such as the Total Maximum Displacement (DTM), the Peak Damper Force (PDF) and the Top Story Acceleration Ratio (TSAR) of the isolated structure compared to the fixed-base structure. The ANN models produced acceptable results with significantly less computation. The results of this study show that ANN models can be a powerful tool to be included in the design process of Seismic-Isolation (SI) systems, especially at the preliminary stages.</p>", "Keywords": "", "DOI": "10.46300/91016.2022.9.3", "PubYear": 2022, "Volume": "9", "Issue": "", "JournalId": 75420, "JournalTitle": "International Journal of Neural Networks and Advanced Applications", "ISSN": "", "EISSN": "2313-0563", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Civil and Environmental Engineering at the University of Sharjah, Sharjah, UAE"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering at the American University of Sharjah, Sharjah, UAE"}], "References": []}, {"ArticleId": 93415416, "Title": "Supervisory control of discrete event systems under asynchronous spiking neuron P systems", "Abstract": "Asynchronous spiking neural P systems with multiple channels (SNP systems) operate in an asynchronous pattern, in which the firing of enabled rules is not obligatory. Asynchronous models are one of the most potential research carriers of supervisory control. However, a systematic understanding and process of leveraging SNPs to address control issues in discrete event systems are still lacking. This paper extends a type of SNP formalization, called vector-SNP, to explore the system’s intrinsic stability properties without configuration attention by introducing two classes of structural feature vectors: N -invariants and R -invariants. Then, a typical style of specifications, namely neuron mutual exclusion inequality constraints (NMEICS), is advanced for supervisory control using SNPs. An NMEIC is enforced by explicitly adding a supervisor (group of observation and control monitors represented by SNPs) to an SNP system. On top of that, the supervisor construct algorithm that utilizes the linear algebraic calculations is established to obtain the supervisors for pre-designed control constraints. Finally, we evaluate the proposed supervisory control theory using SNPs on an automated manufacturing system. Experimental results achieve maximally permissive controlled behaviors that reserve all feasible legal states.", "Keywords": "", "DOI": "10.1016/j.ins.2022.03.003", "PubYear": 2022, "Volume": "597", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu 610039, China;Department of Computer Science and Operations Research, University of Montreal, Montreal, QC H3C3J7, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer and Software Engineering, Xihua University, Chengdu 610039, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Electronic Information, Xihua University, Chengdu 610039, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi’an 710119, China;Department of Computer Science, University of Exeter, Exeter EX4 4QF, UK"}], "References": [{"Title": "Spiking neural P systems with inhibitory rules", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "188", "Issue": "", "Page": "105064", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Small universal asynchronous spiking neural P systems with multiple channels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "A membrane parallel rapidly-exploring random tree algorithm for robotic motion planning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "2", "Page": "121", "JournalTitle": "Integrated Computer-Aided Engineering"}, {"Title": "Multi-behaviors coordination controller design with enzymatic numerical P systems for robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "2", "Page": "119", "JournalTitle": "Integrated Computer-Aided Engineering"}, {"Title": "LSTM-SNP: A long short-term memory model inspired from spiking neural P systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107656", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 93415487, "Title": "Bounds for the Security of Ascon against Differential and Linear Cryptanalysis", "Abstract": "<p>The NIST Lightweight Cryptography project aims to standardize symmetric cryptographic designs, including authenticated encryption and hashing, suitable for constrained devices. One essential criterion for the evaluation of the 10 finalists is the evidence for their security against attacks like linear and differential cryptanalysis. For <PERSON><PERSON>, one of the finalists and previous winner of the CAESAR competition in the ‘lightweight’ category, there is a large gap between the proven bounds and the best known characteristics found with heuristic tools: The bounds only cover up to 3 rounds with 15 differentially and 13 linearly active S-boxes, insufficient for proving a level of security for the full constructions.In this paper, we propose a new modeling strategy for SAT solvers and derive strong bounds for the round-reduced Ascon permutation. We prove that 4 rounds already ensure that any single characteristic has a differential probability or squared correlation of at most 2−72, and 6 rounds at most 2−108. This is significantly below the bound that could be exploited within the query limit for keyed Ascon modes. These bounds are probably not tight. To achieve this result, we propose a new search strategy of dividing the search space into a large number of subproblems based on ‘girdle patterns’, and show how to exploit the rotational symmetry of Ascon using necklace theory. Additionally, we evaluate and optimize several aspects of the pure SAT model, including the counter implementation and parallelizability, which we expect to be useful for future applications to other models.</p>", "Keywords": "", "DOI": "10.46586/tosc.v2022.i1.64-87", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81466, "JournalTitle": "IACR Transactions on Symmetric Cryptology", "ISSN": "", "EISSN": "2519-173X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graz University of Technology, Graz, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Infineon Technologies AG, Munich, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Graz University of Technology, Graz, Austria"}], "References": []}, {"ArticleId": 93415506, "Title": "Sentiment aware tensor model for multi-criteria recommendation", "Abstract": "<p>With the advance of sentiment analysis techniques, several studies have been on Multi-Criteria Recommender Systems (MCRS) leveraging sentiment information. However, partial preferences quite and naturally happen in MCRS and negatively affect the predictive performances of sentiment analysis and multi-criteria recommendation. In this paper, we propose a Sentiment Aware Tensor Model-based MCRS named SATM. It maps between i) a set of multiple classes from explicit user feedbacks and ii) sentiments extracted from free texts in user reviews. In particular, we found the four patterns of the partial preferences and applied a rule-based function to detect them and fill their incomplete ratings intuitively. Lastly, we introduce a mapping function of the misinterpretable patterns into sentiment scores in order to generate virtual user preferences that construct the SATM. Experiments on three datasets (i.e., hotel and restaurant reviews) collected from TripAdvisor show that the SATM is superior to various baseline techniques, including state-of-the-art approaches. Additionally, the experimental evaluation of the SATM’s variants reveals that the rule-based and mapping functions can handle the partial preferences and improve the MCRS’ performance, regardless of target domains.</p>", "Keywords": "Multi-criteria recommendation; Sentiment analysis; Partial preference; New user problem; Tensor factorization", "DOI": "10.1007/s10489-022-03267-z", "PubYear": 2022, "Volume": "52", "Issue": "13", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Minsung Hong", "Affiliation": "Smart Tourism Research Center, Kyung-Hee University, Seoul, Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Chung-Ang University, Seoul, Korea"}], "References": [{"Title": "A novel deep multi-criteria collaborative filtering model for recommendation system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104811", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A deep learning based algorithm for multi-criteria recommender systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106545", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-criteria tensor model for tourism recommender systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "114537", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Decrease and conquer-based parallel tensor factorization for diversity and real-time of multi-criteria recommendation", "Authors": "Minsung Hong", "PubYear": 2021, "Volume": "562", "Issue": "", "Page": "259", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 93415598, "Title": "Intelligent rubbing fault identification using multivariate signals and a multivariate one-dimensional convolutional neural network", "Abstract": "A rub-impact fault is recognized as a complex, non-stationary, and non-linear type of mechanical fault that frequently occurs in turbines. Extracting features for diagnosing rubbing faults at their early stages requires complex and computationally expensive signal processing approaches that are not always suitable for industrial applications. Furthermore, most of the known techniques experience challenges when applied to multivariate signals. In this paper, an intelligent approach that utilizes multivariate signals and a multivariate one-dimensional convolutional neural network is proposed for diagnosing rubbing faults of various intensities. Specifically, the vibration signals are first collected using multiple sources and then they are resampled into windows with overlap. Next, the envelope power spectra are extracted from the resampled signals to create patterns that are used as the inputs for the multivariate one-dimensional convolutional neural network and to reduce the signal dimensionality to speed up the operation of the intelligent framework. The pairs of convolutional and subsampling layers are used to extract the discriminative local features from the signals collected using multiple sources. These features are used to make a decision about the state of the system in the output layer of the proposed convolutional neural network. The proposed methodology is tested on two different rubbing fault datasets. The experimental results demonstrate that the proposed intelligent fault diagnosis framework is capable of differentiating rubbing faults of various intensities with high classification accuracy.", "Keywords": "Convolutional neural networks ; Deep learning ; Fault diagnosis ; ModCNN ; Multivariate signal ; Rub-impact fault", "DOI": "10.1016/j.eswa.2022.116868", "PubYear": 2022, "Volume": "198", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical, Electronics and Computer Engineering, University of Ulsan, No. 7, 93 Daehak ro, Nam-gu, Ulsan 44610, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical, Electronics and Computer Engineering, University of Ulsan, No. 7, 93 Daehak ro, Nam-gu, Ulsan 44610, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical, Electronics and Computer Engineering, University of Ulsan, No. 7, 93 <PERSON><PERSON><PERSON>o, Nam-gu, Ulsan 44610, South Korea;Corresponding author"}], "References": []}, {"ArticleId": 93415662, "Title": "A two-step machining and active learning approach for right-first-time robotic countersinking through in-process error compensation and prediction of depth of cuts", "Abstract": "Robotic machining processes are characterised by errors arising from the limitations of the industrial robots. These machining errors can potentially compromise the overall performance of the manufacturing process, resulting in final products with dimensions that vary from the nominal specifications. In order to identify defective parts at an early stage, a quality inspection step is usually performed after the cutting operation. This work presents an innovative two-step manufacturing method based on in-process prediction and compensation of the systematic errors in order to achieve right-first-time characteristics in robotic machining operations. The key idea behind the proposed method is based on the observation that under certain conditions the robotic machining errors remain largely consistent, and therefore by splitting the process in two steps and having an inspection step in between would allow the prediction and then compensation of the systematic errors. A Gaussian Process Regression (GPR) framework combined with a perturbation-based data generation approach is applied for the creation and training of robust process models that map the input-output relationship between in-process signal features and the post-process inspection result. Moreover, the predictive variance output provided by the GPR model is used to assess the confidence of the models. A two-step active learning algorithm that makes online decisions on the inspection step based on the current models’ confidence is proposed and then tested on a robotic countersinking process experiment. The active learning approach reduces the number of post-process inspections that are needed, thus saving both time and costs, whilst also identifying novel data relevant for the model training. The results showed that the algorithm was able to identify the cutting instances where the inspection step was not needed due to the high confidence in the model predictions. The two-step process method also allowed the final countersink depths to be very close to the desired target by compensation of the systematic depth errors observed in the first step, confirming its potential for right-first-time robotic machining.", "Keywords": "Two-step process ; Right-first-time machining ; Robotic machining ; Gaussian process regression ; Data-driven models ; Active learning", "DOI": "10.1016/j.rcim.2022.102345", "PubYear": 2022, "Volume": "77", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Advanced Manufacturing Research Centre (AMRC), The University of Sheffield, Sheffield, UK;Department of Automatic Control and Systems Engineering, The University of Sheffield, Sheffield, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Advanced Manufacturing Research Centre (AMRC), The University of Sheffield, Sheffield, UK;Sandvik Coromant AB, Stockholm, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automatic Control and Systems Engineering, The University of Sheffield, Sheffield, UK"}], "References": [{"Title": "A closed-loop error compensation method for robotic flank milling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101928", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Robot-process precision modelling for the improvement of productivity in flexible manufacturing cells", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101966", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Pose optimization in robotic machining using static and dynamic stiffness models", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "", "Page": "101992", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Rapid prediction and compensation method of cutting force-induced error for thin-walled workpiece", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "5453", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Inspection by exception: A new machine learning-based approach for multistage manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106787", "JournalTitle": "Applied Soft Computing"}, {"Title": "Spindle thermal error prediction approach based on thermal infrared images: A deep learning method", "Authors": "Wu <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "67", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A perturbation signal based data-driven Gaussian process regression model for in-process part quality prediction in robotic countersinking operations", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102105", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 93415664, "Title": "HiCARN: resolution enhancement of Hi-C data using cascading residual networks", "Abstract": "Motivation <p>High throughput chromosome conformation capture (Hi-C) contact matrices are used to predict 3D chromatin structures in eukaryotic cells. High-resolution Hi-C data are less available than low-resolution Hi-C data due to sequencing costs but provide greater insight into the intricate details of 3D chromatin structures such as enhancer–promoter interactions and sub-domains. To provide a cost-effective solution to high-resolution Hi-C data collection, deep learning models are used to predict high-resolution Hi-C matrices from existing low-resolution matrices across multiple cell types.</p> Results <p>Here, we present two Cascading Residual Networks called HiCARN-1 and HiCARN-2, a convolutional neural network and a generative adversarial network, that use a novel framework of cascading connections throughout the network for Hi-C contact matrix prediction from low-resolution data. Shown by image evaluation and Hi-C reproducibility metrics, both HiCARN models, overall, outperform state-of-the-art Hi-C resolution enhancement algorithms in predictive accuracy for both human and mouse 1/16, 1/32, 1/64 and 1/100 downsampled high-resolution Hi-C data. Also, validation by extracting topologically associating domains, chromosome 3D structure and chromatin loop predictions from the enhanced data shows that HiCARN can proficiently reconstruct biologically significant regions.</p> Availability and implementation <p>HiCARN can be accessed and utilized as an open-sourced software at: https://github.com/OluwadareLab/HiCARN and is also available as a containerized application that can be run on any platform.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btac156", "PubYear": 2022, "Volume": "38", "Issue": "9", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Biology, Concordia University Irvine, Irvine, CA 92612, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Colorado, Colorado Springs, CO 80918, USA"}], "References": []}, {"ArticleId": 93415704, "Title": "Customization of GPRS, and Wi-Fi device drivers for PXA270 of Linux OS based barcode scanner", "Abstract": "<p>To access any device, it is necessary to have an access point. A device driver is an entry point to access a device. This project is aimed to customize the Wi-Fi and general packet radio service (GPRS) device drivers in Linux OS for PXA270 (Intel Xscale ARM processor). Customizing a device driver is a special way of designing software that can be more easily ported from one architecture to another without rewriting it from scratch. The paper is discussing about the customisation of Wi-Fi and GPRS device driver in Linux OS for PXA270 (Intel Xscale ARM processor). To develop a device driver, it is necessary to understand the processor architecture and Linux kernel internals and other design constraints. Since dynamically loaded driver module is attached to the existing kernel, and any error in the driver will crash the entire system. Resource allocation and implementation for a device is one of the main concerns for device driver developers. The device resources are input/output, memory, IRQs and ports. The required toolchain to build the cross-complier for the Intel Xscale ARM processor was built on Linux platform. The customised device drivers of Wi-Fi, and GPRS was customised, and the customised images are made to port for PXA270 processor architecture on EMX-270 board. With all the supporting parameters the kernel images with drivers are build and ported efficiently. Also, a successful verification and testing had been performed for their functionalities.</p>", "Keywords": "EMX-270;GPRS;Linux;PXA270;Wi-Fi", "DOI": "10.11591/ijres.v11.i1.pp84-92", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 46991, "JournalTitle": "International Journal of Reconfigurable and Embedded Systems (IJRES)", "ISSN": "2089-4864", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "M. S. Ramaiah University of Applied Sciences"}, {"AuthorId": 2, "Name": "Sanket S. Na<PERSON>", "Affiliation": "Departement of Computer Science and Engineering, Faculty of Engineering and Technology, M. S. <PERSON>iah University of Applied Sciences, Bengaluru, India"}], "References": []}, {"ArticleId": 93415713, "Title": "A novel autonomous remote system applied in agriculture using transmission control protocol", "Abstract": "<span lang=\"EN-US\">An internet of things (IoT) irrigation system is challenged by several issues, such as cost, energy consumption, and data storage. This paper proposes a novel energy-efficient, cost-effective IoT system called \"NewAgriCom\" to monitor agricultural field water flow. NewAgriCom works with an embedded energy harvesting system, is an autonomous remote supervisory control and data acquisition (SCADA) based on a general packet radio service (GPRS) cellular network that effectively communicates irrigation field data to the Node.js server using SIM808 EVBV3.2 modem. In javascript object notation (JSON) format, data is transmitted over the hypertext transfer protocol (HTTP) protocol to the MySQL database. Then data are transferred to the proposed IoT platform, which gives us a hand to control actuators, visualise, store and download the data. NewAgriCom can significantly reduce water consumption. It can set a schedule to control water automatically at specific times in various modes, including normal, light, and deep sleep modes. It regularly provides the location, time, signal strength, and the state of actuators with the identifier of every device remotely on the IoT Platform.</span><p> </p>", "Keywords": "IoT Cellular Network Remote sensinsing Remote Server Data Storage Energy Efficiency Arduino", "DOI": "10.11591/ijres.v11.i1.pp1-12", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 46991, "JournalTitle": "International Journal of Reconfigurable and Embedded Systems (IJRES)", "ISSN": "2089-4864", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ibn <PERSON> University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ibn <PERSON> University"}, {"AuthorId": 3, "Name": "Imam <PERSON><PERSON>", "Affiliation": "Ibn <PERSON> University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Ibn <PERSON> University"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Ibn <PERSON> University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "NewEraCom Company"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mohamed <PERSON>"}], "References": []}, {"ArticleId": 93415808, "Title": "An efficient and robust zero watermarking algorithm", "Abstract": "<p>To improve the resistance to geometric rotation attacks and to solve the problem of high computational complexity of existing watermarking algorithms, a novel efficient and robust zero watermarking algorithm in spatial domain is proposed. First, the central pixels of different channels of colorful host image are taken as the center of the circle, and the features of the image are constructed by the pixels covered by rings with different radius and width. The binary feature image is obtained by binary operation on the constructed image features. Then, the zero watermark image is constructed by XOR operation for the scrambled binary feature image and copyright watermark image encrypted Logistic chaotic algorithm. Finally, the zero watermark image is stored in the intellectual property database for copyright certification in possible copyright disputes. Experimental results show that the proposed algorithm is robust to geometric rotation attacks and common image processing attacks. Compared with similar zero watermarking schemes, the proposed algorithm has lower computational complexity and better robustness.</p>", "Keywords": "Spatial domain; Zero watermarking; Geometric attack; Logistic chaotic encryption", "DOI": "10.1007/s11042-022-12115-8", "PubYear": 2022, "Volume": "81", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China; Institute of Optics and Electronics, Chinese Academy of Sciences, Chengdu, China; Key Laboratory of Optical Engineering, Chinese Academy of Science, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Space Utilization, Technology and Engineering Center for Space Utilization, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Tiangong University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China; Key Laboratory of Space Utilization, Technology and Engineering Center for Space Utilization, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China; Institute of Optics and Electronics, Chinese Academy of Sciences, Chengdu, China; Key Laboratory of Optical Engineering, Chinese Academy of Science, Chengdu, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China; Institute of Optics and Electronics, Chinese Academy of Sciences, Chengdu, China; Key Laboratory of Optical Engineering, Chinese Academy of Science, Chengdu, China"}], "References": [{"Title": "Robust and secure zero-watermarking algorithm for color images based on majority voting pattern and hyper-chaotic encryption", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "Page": "1169", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A robust zero-watermarking algorithm for color image based on tensor mode expansion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7599", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "On the implementation of a copyright protection scheme using digital image watermarking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "13125", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93415824, "Title": "Identifying Contradictions in the Legal Proceedings Using Natural Language Models", "Abstract": "<p>Identifying contradictory statements in legal proceedings is largely manual in nature. Automating this using intelligent techniques such as natural language model will not only save a lot of time but also aid in the process of solving legal cases. This paper aims at creating an artificial intelligent (AI) model that can identify contradictory sentences in legal proceedings. The novelty of this study is the construction of a new dataset that represents neutral and contradictory statements that may be given in the legal proceedings. We give a comparative study of the various pre-trained Natural Language Models such as ELMo, Google BERT, XLNet, and Google ALBERT to understand and identify contradictory statements made by people in legal proceedings. We achieve the highest accuracy of 88.0% in identifying contradictory statements using the Google ALBERT model. This model can be implemented on a real-time basis and hence has practical applicability.</p>", "Keywords": "Classification; Contradiction; Natural language understanding; Legal proceedings", "DOI": "10.1007/s42979-022-01075-3", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Thoughtworks Technologies, Pune, India"}, {"AuthorId": 2, "Name": "Sugam Dembla", "Affiliation": "Symbiosis Skills and Professional University, Pune, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ekklavya Infosys, Pune, India"}], "References": []}, {"ArticleId": 93415847, "Title": "Understanding the role of segmentation on process-structure–property predictions made via machine learning", "Abstract": "<p>The present study investigated the effect of porosity surface determination methods on performance of machine learning models used to predict the tensile properties of AlSi10Mg processed by laser powder bed fusion from micro-computed tomography data. Machine learning models applied in this work include support vector machines, neural networks, decision trees, and Bayesian classifiers. The effects of isosurface thresholding and local gradient approaches for porosity segmentation, as well as image filtering schemes, on model precision were evaluated for samples produced under differing levels of global energy density.</p>", "Keywords": "Machine learning; Computed tomography; Additive manufacturing; Part qualification; Porosity analysis", "DOI": "10.1007/s00170-022-09003-8", "PubYear": 2022, "Volume": "120", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON> School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, USA;Sandia National Laboratories, Albuquerque, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON> School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sandia National Laboratories, Albuquerque, USA"}], "References": [{"Title": "Effect of defects and specimen size with rectangular cross-section on the tensile properties of additively manufactured components", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "251", "JournalTitle": "Virtual and Physical Prototyping"}]}, {"ArticleId": 93415878, "Title": "Dataset for influence of visual and haptic feedback on the detection of threshold forces in a surgical grasping task", "Abstract": "The data is related to minimal force thresholds perception in robotic surgical grasping applications. The experimental setup included an indenter-based haptic device acting on the fingertip of a participant and a visual system that displays grasping tasks by a surgical grasper. The experiments included the display of two presentations at different force levels (i.e., grasping and indentation) in three different modes, namely, visual-alone, haptic-alone, and bimodal (i.e., combined). For each mode, the participants were asked to identify which of the two presentations was higher. Each experiment was repeated till the termination conditions were met. Sixty participants took part in these experiments. The experiments were randomized and the threshold forces were calculated based on an algorthim. The datasets contain the individual responses of each participant, the threshold forces calculations, and the number of iterations.", "Keywords": "Haptics;Minimal force threshold;Robotic surgery;Surgical grasping", "DOI": "10.1016/j.dib.2022.108045", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Qatar University, Doha, Qatar."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Qatar University, Doha, Qatar."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Australian Public Service, Port Melbourne, Victoria, Australia."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Technology, Engineering, and Mathematics, University of Washington, Bothell, WA, USA."}], "References": []}, {"ArticleId": 93415965, "Title": "Graph Structures and the Whitney Theorem", "Abstract": "The article considers a method for constructing the structures of an inseparable undirected graph G based on the use of the structure of its edge graph L(G). The method is grounded on the mapping of the edge graph L(G) by the subgraphs of the graph G. It is shown that the construction of the set of isometric cycles of the edge graph L(G) generates subsets of the subgraphs of the graph G. In turn, the set of generated subgraphs of the graph G allows constructing various invariants of the graph G and its topological drawing. The metric properties of the system of isometric cycles of the edge graph L(G) make it possible to determine the weights of the edges and vertices. This allows to construct the numerical characteristics of the graph structure. In turn, the numerical characteristics of the digital invariant of the edge graph make it possible to apply <PERSON>'s theorem to solve the problem of recognizing graph isomorphism. The algorithm for constructing a digital invariant of an inseparable graph belongs to the class of polynomial problems with a computational complexity of О(n6). The digital invariant of the edge graph does not depend on the numbering of the vertices and edges of the graph. It specifies the individual characteristics of the graph, which allows determining graph isomorphism by comparing the invariants.", "Keywords": "", "DOI": "10.17587/it.28.133-140", "PubYear": 2022, "Volume": "28", "Issue": "3", "JournalId": 55442, "JournalTitle": "INFORMACIONNYE TEHNOLOGII", "ISSN": "1684-6400", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Zaporizhzhya National University, Zaporizhzhya, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Zaporizhzhya Institute of Postgraduate Pedagogical Education, Zaporizhzhya, Ukraine"}], "References": []}, {"ArticleId": 93415972, "Title": "Play and Playfulness in <PERSON><PERSON><PERSON>’s What It Is", "Abstract": "<p>This article discusses play and playfulness in <PERSON><PERSON><PERSON>’s autobiographical comics/instructional work What It Is (2008). The term ‘playfulness’ is commonly used in two primary but distinct ways, namely in a phenomenological sense concerning a free attitude accompanying a given play activity, and referring to a frame-breaking form of disruption. I refer to the former as play/playing, and reserve the term ‘playfulness’ for the latter, while also suggesting that playfulness implies a form of disruptive attitude or intent. Playing is a central concept in <PERSON>’s work, one on which the author draws in terms of formulating the creative process. <PERSON>’s insistence on the phenomenological or experiential aspects of playing both reinforces and is reinforced by the stylistic aspects of What It Is. Thus, assertions of playfulness based on elements of <PERSON>’s work that subvert convention, often via a form of ambiguity, are consistently countered by <PERSON>’s emphasis on process. It is therefore argued that, if What It Is displays a form of playfulness, it is primarily in terms of the way that it occupies the border between immediacy and authenticity, on the one hand, and constructedness, on the other. The article first establishes the approach to playing adopted by <PERSON> throughout What It Is, based on the work of <PERSON><PERSON> <PERSON><PERSON>, and links it to other conceptualizations of play/playing, before drawing a distinction between playing and playfulness. Following this, it examines how <PERSON>’s delineation of the creative process as play, as well as the author’s approach to style, achieves a perceived form of immediacy and authenticity. After this, following consideration of the playfulness of <PERSON>’s collage pages, the article considers how What It Is occupies the border between immediacy and constructedness.</p>", "Keywords": "<PERSON><PERSON><PERSON>;constructedness;immediacy;play;playfulness", "DOI": "10.7557/23.6366", "PubYear": 2022, "Volume": "12", "Issue": "1", "JournalId": 96439, "JournalTitle": "Eludamos: Journal for Computer Game Culture", "ISSN": "", "EISSN": "1866-6124", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Siegen"}], "References": []}, {"ArticleId": 93415976, "Title": "The Playful Affordances of Picture Book Apps for Children", "Abstract": "<p>The tradition of children’s literature has evolved side by side with the market of children’s entertainment, games, and toys. The selection of contemporary print products includes a wide variety of materially or technologically enhanced picture books. This background is rarely considered in the examination of children’s book applications that have attracted scholarly interest after the arrival of smartphones and tablets during the early 2010s.The relationship between picture book apps, mobile games, and digital playthings requires further examination that considers the specific affordances of the mobile platform. Leaning on five case studies, this article examines how picture book apps afford opportunities for a reading experience that contains features characteristic for children’s digital play. For this purpose, I adapt a specific model of close reading that focuses on the visual, auditory, tactile, and performative elements of children’s video games.On the basis of the case studies, it seems that navigating a picture book app requires balancing between different modes of action: reading, playing, and exploring. Engagement with picture book apps has different forms that resemble the features of both traditional print reading and digital play. However, further examination of children’s playful reading practices and intergenerational play is necessary from a premise that recognizes playing with a book as a valuable research topic.</p>", "Keywords": "affordances;children’s literature;digital children’s literature;digital play;picture book apps", "DOI": "10.7557/23.6367", "PubYear": 2022, "Volume": "12", "Issue": "1", "JournalId": 96439, "JournalTitle": "Eludamos: Journal for Computer Game Culture", "ISSN": "", "EISSN": "1866-6124", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Jyväskylä"}], "References": []}, {"ArticleId": 93415993, "Title": "The Philosophy of Computer Games – Introduction", "Abstract": "<p>The Philosophy of Computer Games—this special issue's topic—might seem in vogue and out of date at the same time. Out of date on the one hand, because the first wave of philosophical approaches to computer games peaked about ten years ago. On the other hand, a renewed turn to computer game aesthetics and especially the turn towards a phenomenology of computer games that has gained some new momentum recently seem to have brought new attention to what philosophy has to offer to game studies (and vice versa), raising new questions and putting new emphases on old ones.</p>", "Keywords": "philosophy;introduction", "DOI": "10.7557/23.6351", "PubYear": 2020, "Volume": "11", "Issue": "1", "JournalId": 96439, "JournalTitle": "Eludamos: Journal for Computer Game Culture", "ISSN": "", "EISSN": "1866-6124", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93416069, "Title": "SME participation in cross-border e-commerce as an entry mode to foreign markets: A driver of innovation or not?", "Abstract": "<p>In the context of globalization, it is not just large corporates that need to update their technologies and processes to maintain competitiveness. Small-to-medium-sized enterprises (SMEs) also need to innovate if they are to satisfy customer preferences in both domestic and foreign markets. Today, internationalization is considered to be an essential factor in promoting SME innovation. However, it is often difficult for SMEs to innovative with overseas customers because many lack easy access to foreign markets. Compared to other traditional entry modes, cross-border e-commerce, with its low costs and high levels of control, can help to remove some of the barriers to internationalization for SMEs. In turn, it may be that cross-border e-commerce also promotes innovation in these firms. We gathered data on 781 Chinese SMEs to test several hypotheses surrounding this notion. The results of panel regression estimates indicate that cross-border e-commerce does indeed have a direct and positive effect on market innovation. More importantly, it has an indirect and positive impact on technology and process innovation by fully mediating the effects of an entrepreneurial orientation. These results shed light on how cross-border e-commerce impacts SME innovation performance, providing valuable implications for both academics and SME managers.</p>", "Keywords": "B2B platform; Cross-border e-commerce; Entrepreneurial orientation; Innovation; SMEs", "DOI": "10.1007/s10660-022-09539-7", "PubYear": 2023, "Volume": "23", "Issue": "4", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "Lipeng Pan", "Affiliation": "Zhejiang Informatization Development Institute, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Zhejiang Informatization Development Institute, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "Yongqing Li", "Affiliation": "Western Sydney University, Parramatta, Australia"}], "References": [{"Title": "A study on cross-border e-commerce partner selection in B2B mode", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "2", "Page": "1", "JournalTitle": "Electronic Commerce Research"}, {"Title": "An analysis on the policy evolution of cross-border ecommerce industry in China from the perspective of sustainability", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "3", "Page": "875", "JournalTitle": "Electronic Commerce Research"}]}, {"ArticleId": 93416169, "Title": "emteqPRO—Fully Integrated Biometric Sensing Array for Non-Invasive Biomedical Research in Virtual Reality", "Abstract": "<p>Virtual Reality (VR) enables the simulation of ecologically validated scenarios, which are ideal for studying behaviour in controllable conditions. Physiological measures captured in these studies provide a deeper insight into how an individual responds to a given scenario. However, the combination of the various biosensing devices presents several challenges, such as efficient time synchronisation between multiple devices, replication between participants and settings, as well as managing cumbersome setups. Additionally, important salient facial information is typically covered by the VR headset, requiring a different approach to facial muscle measurement. These challenges can restrict the use of these devices in laboratory settings. This paper describes a solution to this problem. More specifically, we introduce the emteqPRO system which provides an all-in-one solution for the collection of physiological data through a multi-sensor array built into the VR headset. EmteqPRO is a ready to use, flexible sensor platform enabling convenient, heterogenous, and multimodal emotional research in VR. It enables the capture of facial muscle activations, heart rate features, skin impedance, and movement data—important factors for the study of emotion and behaviour. The platform provides researchers with the ability to monitor data from users in real-time, in co-located and remote set-ups, and to detect activations in physiology that are linked to arousal and valence changes. The SDK (Software Development Kit), developed specifically for the Unity game engine enables easy integration of the emteqPRO features into VR environments.</p><p> Code available at : ( https://github.com/emteqlabs/emteqvr-unity/releases ) </p>", "Keywords": "virtual reality; Biosensors; Affect; Electromyography; Photoplethysmography; Biomedical measurement; Valence; Arousal", "DOI": "10.3389/frvir.2022.781218", "PubYear": 2022, "Volume": "3", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Digital Entertainment, Faculty of Media and Communication, Bournemouth University, United Kingdom;Emteq Labs, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Emteq Labs, United Kingdom;Department of Psychiatry, University of Southampton, United Kingdom;Department of Psychology, College of Science, Northeastern University, United States"}, {"AuthorId": 3, "Name": "Ifigeneia <PERSON>", "Affiliation": "Emteq Labs, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Emteq Labs, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Faculty of Science an Technology, Interdisciplinary Neuroscience Research Centre, Bournemouth University, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Communication Systems Engineering, University of the Aegean, Greece"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>-Ballester", "Affiliation": "Department of Computing and Informatics, Faculty of Science an Technology, Interdisciplinary Neuroscience Research Centre, Bournemouth University, United Kingdom"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Emteq Labs, United Kingdom"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Sport and Health Sciences, University of Brighton, United Kingdom"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Emteq Labs, United Kingdom"}], "References": []}, {"ArticleId": 93416255, "Title": "Assessment and insurance of cyber risks as tools for ensuring information security of an organisation (on the example of Russia)", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2022.10045738", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Gulnaz F. Galieva", "Affiliation": ""}], "References": []}, {"ArticleId": 93416260, "Title": "Modeling, reasoning, and application of fuzzy Petri net model: a survey", "Abstract": "<p>A fuzzy Petri net (FPN) is a powerful tool to model and analyze knowledge-based systems containing vague information. This paper systematically reviews recent developments of the FPN model from the following three perspectives: knowledge representation using FPN, reasoning mechanisms using an FPN framework, and the latest industrial applications using FPN. In addition, some specific modeling and reasoning approaches to FPN to solve the ‘state-explosion problem’ are illustrated. Furthermore, detailed analysis of the discussed aspects are shown to reveal some interesting findings, as well as their developmental history. Finally, we present conclusions and suggestions for future research directions.</p>", "Keywords": "Fuzzy Petri net; Knowledge representation; Modeling; Reasoning; Industrial application", "DOI": "10.1007/s10462-022-10161-0", "PubYear": 2022, "Volume": "55", "Issue": "8", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Jishou University, Jishou, China"}, {"AuthorId": 2, "Name": "Kai-<PERSON>", "Affiliation": "College of Information Science and Engineering, Jishou University, Jishou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Internet of Things and People Research Center, Department of Computer Science and Media Technology, Malmö University, Malmö, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UTM Big Data Centre, Universiti Teknologi Malaysia, Skudai, Malaysia"}], "References": [{"Title": "A novel method for failure mode and effects analysis using fuzzy evidential reasoning and fuzzy Petri nets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "2381", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 93416261, "Title": "Satellite imagery analysis for road segmentation using U-Net architecture", "Abstract": "<p>Road network plays a significant role in today’s urban development. These are a vital part of applications such as automatic road navigation, traffic management, route optimization, etc. In this paper, we aim to explore the potential and performance of convolution neural network architecture, U-Net, performing semantic segmentation to detect roads. The model has been evaluated on Massachusetts Roads Dataset with the estimation of numerous parameters such as filter stride, learning rate, training epochs, data size, and various augmentation techniques. The employment of the proposed technique and hyperparameters to the U-Net architecture gives a boost to test accuracy and improves the dice coefficient by a value of 0.007. Experimental results thereby demonstrate that our model is computationally efficient and achieves comparable segmentation results.</p>", "Keywords": "Convolutional neural network (CNN); Road extraction; Satellite images; Fully convolutional networks; Semantic image segmentation; Encoder–decoder", "DOI": "10.1007/s11227-022-04379-6", "PubYear": 2022, "Volume": "78", "Issue": "10", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sant Longowal Institute of Engineering and Technology, Sangrur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sant Longowal Institute of Engineering and Technology, Sangrur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sant Longowal Institute of Engineering and Technology, Sangrur, India"}], "References": []}, {"ArticleId": 93416284, "Title": "Energy saving on IoT using LoRa: a systematic literature review", "Abstract": "<span lang=\"EN-US\">The development of devices connected to the internet is very significant, encouraging the creation of the internet of things (IoT). With remote systems, IoT is not enough to use in case of internet instability. By using long range (LoRa), IoT systems can now solve this problem. Millions of data make IoT-LoRa have to spend a lot of energy. This paper helps discover where recent studies offer a broad perspective on energy savings using the systematic literature review (SLR). The paper extracted 252 articles from IEEE, ACM, MDPI, Springer, Hindawi, ScienceDirect, and IAES. 44 articles passed the specified inclusion and exclusion criteria. The article focuses on knowledge about IoT-Lora, energy saving needs, energy saving factors, and the paper demographics. The author synthesizes studies for that purpose on IoT applications using LoRa.</span>", "Keywords": "LoRa;Energy Savings;Systematic Literature Review;Internet of Things", "DOI": "10.11591/ijres.v11.i1.pp25-33", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 46991, "JournalTitle": "International Journal of Reconfigurable and Embedded Systems (IJRES)", "ISSN": "2089-4864", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Bina Nusantara University"}], "References": []}, {"ArticleId": 93416312, "Title": "Research on intelligent city parking guidance method based on ant colony algorithm", "Abstract": "In order to get the most satisfactory parking space at the fastest speed, an intelligent urban parking guidance method based on ant colony algorithm is proposed. The main factors affecting the selection of parking spaces in parking lots are analysed, including walking distance, driving distance, walking time, driving time and so on. Each factor is set as multiple attributes of berth, and the optimal berth selection model of smart city is established. Ant colony algorithm is used to solve the model, obtain the optimal parking space, and realise intelligent guidance of intelligent city parking. The simulation results show that the proposed method is feasible and effective. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "ACO; Ant colony algorithm; Intelligent guidance; Parking; Simulation", "DOI": "10.1504/IJICA.2022.121383", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 23927, "JournalTitle": "International Journal of Innovative Computing and Applications", "ISSN": "1751-648X", "EISSN": "1751-6498", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Chongqing University of Arts and Sciences, Chongqing, 402160, China; Lyceum of the Philippines University, Batangas Campus, Batangas City, Philippines"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Chen", "Affiliation": "College of Computer Science, Sichuan University, Chengdu, 610065, China"}], "References": []}, {"ArticleId": 93416331, "Title": "Fuzzy relational representation, modeling and interpretation of temporal data", "Abstract": "In virtue of omnipresence of temporal data (time series), a quest for efficient and interpretable models of such data becomes of paramount relevance. The accuracy-interpretable dilemma becomes more visible in light of the current trend of producing accurate, yet comprehensible models supporting users in making sound and highly actionable conclusions. This study is positioned in the realm of human-centric granular representation of temporal data (time series) where the logic settings of relational calculus is realized at the level of information granules. We establish modeling pursuits and resulting constructs at a certain level of abstraction delivered by information granules expressed as fuzzy sets. While have been some studies involving a framework of granular computing applied to granular models of time series, in this study we advocate a unified and original environment of logic blueprint of relational dependencies among information granules articulated by means of weighted logic expressions where both the components of the expressions as well as their associations are quantified (calibrated) in the presence of available experimental data. An overall processing scheme is structured as the following sequence of main functional modules: granulation (encoding) – relational computing – logic interpretation – numeric characterization of results (produced through decoding). Relational computing engages a suite of architectures that capture temporal dependencies describing predictions at the level of granular amplitudes and granular trends. The quality of the relational model is quantified at the level of information granules (which emphasizes the interpretability aspect) as well as. A series of experiments is reported to demonstrate the performance of the relational models and their performance impacted by the parameters of the relational architectures.", "Keywords": "Temporal data ; Interpretability ; Information granules ; Relational models ; Encoding and decoding ; Triangular norms", "DOI": "10.1016/j.knosys.2022.108548", "PubYear": 2022, "Volume": "244", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Sultan Qaboos University, Muscat, Oman;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical & Computer Engineering, University of Alberta, Edmonton, Canada;Department of Electrical and Computer Engineering, King Abdulaziz University, Jeddah, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Sultan Qaboos University, Muscat, Oman"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Middle East University, Amman, Jordan"}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "Granule description in knowledge granularity and representation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106160", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Explainable artificial intelligence and machine learning: A reality rooted perspective", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "6", "Page": "", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Time series forecasting based on kernel mapping and high-order fuzzy cognitive maps", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "206", "Issue": "", "Page": "106359", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A randomization mechanism for realizing granular models in distributed system modeling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "232", "Issue": "", "Page": "107376", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 93416375, "Title": "Secrecy sum rate maximization for a MIMO-NOMA uplink transmission in 6G networks", "Abstract": "Non-orthogonal multiple access (NOMA), as a well-qualified candidate for sixth-generation (6G) mobile networks, has been attracting remarkable research interests due to high spectral efficiency and massive connectivity. The aim of this study is to maximize the secrecy sum rate (SSR) for a multiple-input multiple-output (MIMO)-NOMA uplink network under the maximum total transmit power and quality of service (QoS) constraints. Thanks to the generalized singular value decomposition method, the SSR of NOMA is compared with conventional orthogonal multiple access and other baseline algorithms in different MIMO scenarios. Due to the subtractive and non-convex nature of the SSR problem, the first-order Taylor approximation is exploited to transform the original problem into a suboptimal concave problem. Simulation results are provided and compared with some other benchmarks to evaluate the efficacy of the proposed method.", "Keywords": "Multiple input multiple output (MIMO) ; Non-orthogonal multiple access (NOMA) ; Physical layer security (PLS) ; Sixth generation radio access networks (6G-RAN)", "DOI": "10.1016/j.phycom.2022.101675", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Manchester, School of Electrical and Electronic Engineering, United Kingdom;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Communication Systems (ICS), Home for 5GIC & 6GIC, University of Surrey, Guildford, Surrey, GU2 7XH, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The University of Manchester, School of Electrical and Electronic Engineering, United Kingdom"}], "References": [{"Title": "Secure beamforming design in MIMO NOMA networks for Internet of Things with perfect and imperfect CSI", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "187", "Issue": "", "Page": "107839", "JournalTitle": "Computer Networks"}, {"Title": "Error performance of cooperative relaying systems empowered by SWIPT and NOMA", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101450", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 93416393, "Title": "Early detection and prediction of cancer metastasis – Unravelling metastasis initiating cell as a dynamic marker using self- functionalized nanosensors", "Abstract": "The ability of cancer to metastasize to distant organs is an urgent problem to be solved clinically and continue to pose a greater challenge to researchers. Current treatments for cancer are ineffective for metastatic cancer due to inability of conventional imaging techniques to detect at early stages. Additionally, the ability to predict the ability of cancer to metastasize in advance will prove to be a valuable tool to improve patient prognosis. However, current techniques of prediction rely on molecular expression profiles which are highly tumor- specific, patient-specific and does not account for tumor heterogeneity. These factors critically impede the ability to use molecular expression profiles for early diagnosis and prediction of cancer metastasis. Hence, there is a need to concentrate on using tumor cells&#x27; phenotypic heterogeneity, which can serve as a diagnostic and predictive marker for cancer metastasis. Here, we have identified a rare subpopulation of cells within the tumor, which can signal metastasis initiation, known as metastasis-initiating cells (MICs) which are cancer non-specific, independent of epigenetic makeup, thereby becoming an excellent, unprejudiced marker for cancer metastasis irrespective of cancer type, the tissue of origin, and cancer stage. However, it is challenging to use MICs as a cellular marker for metastasis from a clinical perspective due to their rare, undetectable nature and the lack of sensitive methods to detect this elusive population of cells. To provide the ultra-sensitivity necessary for the early detection and prediction of cancer metastasis using MICs here we have created a self- functionalised nanosensor which is highly SERS active. The self- functionalized nanosensor enabled the detection of MIC using properties of intracellular biological processes and characters of the tumor microenvironment. The nanosensor enabled a detection sensitivity of 98%. It was able to identify MIC in a heterogeneous population of TICs with an unparalleled specificity of 99.62%. Further, the accuracy of MIC as a predictive marker for metastasis in a heterogeneous population of tumor spheroids was found to be 84.6%. This work contributed to the utilization of cancer cell heterogeneity to identify MIC, which can serve as a universal marker for diagnosis, prediction, and prognosis of tumor metastasis. To the best of our knowledge, this study is the first to design a probe that can provide both the diagnostic signature and predictive signature of cancer metastasis as early as the single cellular stage. This approach holds immense potential in the early diagnosis of metastatic tumors in a clinical setting and considerably improve patient prognosis.", "Keywords": "Cancer metastasis ; Nanosensor ; Biosensor ; SERS ; Self- functionalization", "DOI": "10.1016/j.snb.2022.131655", "PubYear": 2022, "Volume": "361", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Keenan Research Center for Biomedical Science, Unity Health Toronto, Toronto, Ontario M5B 1W8, Canada;Institute for Biomedical Engineering, Science and Technology (I BEST), Partnership between Ryerson University and St. Michael's Hospital, Toronto, Ontario M5B 1W8, Canada;Ultrashort Laser Nanomanufacturing Research Facility, Department of Mechanical and Industrial Engineering, Ryerson University, 350 Victoria Street, Toronto, Ontario M5B 2K3, Canada;Nano-Bio Interface facility, Department of Mechanical and Industrial Engineering, Ryerson University, 350 Victoria Street, Toronto, Ontario M5B 2K3, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Keenan Research Center for Biomedical Science, Unity Health Toronto, Toronto, Ontario M5B 1W8, Canada;Institute for Biomedical Engineering, Science and Technology (I BEST), Partnership between Ryerson University and St. Michael's Hospital, Toronto, Ontario M5B 1W8, Canada;Ultrashort Laser Nanomanufacturing Research Facility, Department of Mechanical and Industrial Engineering, Ryerson University, 350 Victoria Street, Toronto, Ontario M5B 2K3, Canada;Nano-Bio Interface facility, Department of Mechanical and Industrial Engineering, Ryerson University, 350 Victoria Street, Toronto, Ontario M5B 2K3, Canada;Corresponding author at: Keenan Research Center for Biomedical Science, Unity Health Toronto, Toronto, Ontario M5B 1W8, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Keenan Research Center for Biomedical Science, Unity Health Toronto, Toronto, Ontario M5B 1W8, Canada;Institute for Biomedical Engineering, Science and Technology (I BEST), Partnership between Ryerson University and St. Michael's Hospital, Toronto, Ontario M5B 1W8, Canada;Nanocharacterization Laboratory, Department of Aerospace Engineering, Ryerson University, 350 Victoria Street, Toronto, Ontario M5B 2K3, Canada;Nano-Bio Interface facility, Department of Mechanical and Industrial Engineering, Ryerson University, 350 Victoria Street, Toronto, Ontario M5B 2K3, Canada"}], "References": []}, {"ArticleId": 93416755, "Title": "Modified adaptive inertia weight particle swarm optimisation for data clustering", "Abstract": "Data clustering is widely applied in many real world domain including marketing, anthropology, medical science, engineering, economics, and others. It concerns with the partition of unlabelled dataset objects into clusters (groups) based on a similarity measure. The partitioning approach of dataset objects must follow that intra-cluster distances are smaller and inter-cluster distances are larger. In the current work, particle swarm optimisation (PSO) is employed for clustering. Some times the PSO may get stuck into a local optima; to overcome the PSO algorithm’s trapping in a local optima a modified adaptive inertia weight particle swarm optimisation (MAIWPSO) is developed for data clustering based on fitness value of particles. K-means, PSO and MAIWPSO for clustering have been simulated on six standard dataset namely iris, thyroid, heart, breast cancer, crude oil and pima. Simulation results confirm MAIWPSO is a better approach for clustering against K-means and PSO. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "Data clustering; Fitness function; K-means clustering; Particle swarms optimisation; PSO", "DOI": "10.1504/IJICA.2022.121387", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 23927, "JournalTitle": "International Journal of Innovative Computing and Applications", "ISSN": "1751-648X", "EISSN": "1751-6498", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, ABES Engineering College, Uttar Pradesh, Ghaziabad, India; Harcourt Butler Technical University, Nawabganj, Uttar Pradesh, Kanpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, ABES Engineering College, Uttar Pradesh, Ghaziabad, India; Harcourt Butler Technical University, Nawabganj, Uttar Pradesh, Kanpur, India"}], "References": []}, {"ArticleId": 93416756, "Title": "Fuzzy logic control based high step up converter for electric vehicle applications", "Abstract": "At present, the electric vehicles (EVs) have a dominant role for visualising the pollution less environment. Moreover, it is important to design efficient power conditioning units for fulfilling the desired tasks. With advancements in the technology, the fuel cells (FCs) can provide a better solution for powering the EVs. However, their downside is the production of very low output voltage. Therefore, this necessitates a high step ratio DC-DC converter for providing the desired dc link voltage for DC-AC converter. This paper focuses on fuzzy logic control (FLC) based high gain DC-DC converter for EV applications. The fuzzy controllers are superior in dealing the nonlinearities and plant parameter variations without the need of strict mathematical modelling of the converter system. The converter is simulated with simple voltage mode control as well as FLC based voltage mode control. The MATLAB simulation results are compared for the above two control strategies with an emphasis on the steady state and dynamic regulations. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "Control strategies; Electric vehicles; Fuel cells; Power converters", "DOI": "10.1504/IJICA.2022.121388", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 23927, "JournalTitle": "International Journal of Innovative Computing and Applications", "ISSN": "1751-648X", "EISSN": "1751-6498", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, R.V.R. & J.C. College of Engineering, Chowdavaram, A.P, Guntur, 522019, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, VNR Vignana Jyothi Institute of Engineering and Technology, Bachupally, T.S., Hyderabad, 500090, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Velagapudi <PERSON>a Engineering College, A.P., Vijayawada, 520007, India"}], "References": []}, {"ArticleId": 93416859, "Title": "LAMD: Location-based Alert Message Dissemination scheme for emerging infrastructure-based vehicular networks", "Abstract": "Requiring low dissemination delays and thorough vehicles coverage in the vicinity of an emergency event, Vehicular Ad-hoc NETworks (VANETs) were considered as the most adapted communication network to support alert messages dissemination . With the advent of Cooperative ITS services, emerging vehicular networks are expected to increasingly rely on Vehicle to Infrastructure (V2I) communication links, which are expected to be nominally available, with some transient and time-limited connectivity losses. The presence of V2I links paves the way to centralized network control, which can leverage vehicle-related and contextual information provided by the cloud to make more informed decisions. This paper proposes an effective alert message dissemination procedure called LAMD (Location-based Alert Messages Dissemination) for emerging vehicular networks that combines V2I broadcasts with selected V2V (Vehicle to Vehicle) rebroadcasts. The originality of our scheme lies in the selection process of V2V rebroadcasts, which is based on vehicles’ location regarding predefined rebroadcast points selected by a centralized controller . This leads to very limited collisions, low delivery delays, and high information coverage with insignificant signaling and network overhead compared to legacy VANET based dissemination techniques.", "Keywords": "", "DOI": "10.1016/j.iot.2022.100510", "PubYear": 2022, "Volume": "19", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, Toulouse, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, INSA, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, UT2J, Toulouse, France"}], "References": [{"Title": "VANET simulators: an updated review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "1", "JournalTitle": "Journal of the Brazilian Computer Society"}]}, {"ArticleId": 93416928, "Title": "Development of sustainable antibacterial coatings based on electrophoretic deposition of multilayers: gentamicin-loaded chitosan/gelatin/bioactive glass deposition on PEEK/bioactive glass layer", "Abstract": "", "Keywords": "Electrophoretic deposition; Chitosan; Local drug delivery; Sustainable antibacterial activity", "DOI": "10.1007/s00170-022-09024-3", "PubYear": 2022, "Volume": "120", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Materials Science and Engineering, Institute of Space Technology Islamabad, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Science and Engineering, Institute of Space Technology Islamabad, Islamabad, Pakistan"}], "References": [{"Title": "Electrophoretic deposition of PEEK/bioactive glass composite coatings on stainless steel for orthopedic applications: an optimization for in vitro bioactivity and adhesion strength", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "5-6", "Page": "1849", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 93416930, "Title": "Multiview clustering via consistent and specific nonnegative matrix factorization with graph regularization", "Abstract": "<p>Multiview clustering is a hot research topic in machine learning and computer vision, and many non-negative matrix factorization (NMF)-based multiview clustering approaches have been proposed. However, most existing NMF-based multiview clustering methods aim to only push the learned latent feature matrices of all views towards a common consensus representation or only consider the consistency among all multiview data, whereas the complementary information between different views is often ignored. In this work, we propose a novel multi-view clustering via consistent and specific nonnegative matrix factorization with graph regularization method ( MCCS ), where the consistency among all multiview data and view-specific information in each view data are simultaneously considered. Specifically, the NMF problem is first formulated using a shared consistent basis matrix, consistent coefficient matrix, a set of view-specific basis matrices, and view-specific coefficient matrices. Then, manifold regularization is embedded into the objective function to preserve the intrinsic geometrical structure of the original data space. Furthermore, a disagreement term is designed to push these view-specific coefficient matrices further towards a common consensus and to ensure that the multiple views have the same underlying cluster structure. Moreover, the multiplicative update algorithm is employed to optimize the objective function. Extensive experimental results on five multiview benchmark datasets, namely, BBC, BBCSport, 20NGs, Wikipedia, and Handwritten, demonstrate that the proposed MCCS outperforms state-of-the-art methods, achieving improvements of 2.29%, 6.63%, 16.15%, 6.51%, and 2.85%, respectively, over the MVCC method in terms of NMI.</p>", "Keywords": "Multi-view clustering; Non-negative matrix factorization; Complementary information; Graph", "DOI": "10.1007/s00530-022-00905-x", "PubYear": 2022, "Volume": "28", "Issue": "5", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Computer Vision and System, Ministry of Education, Tianjin University of Technology, Tianjin, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Computer Vision and System, Ministry of Education, Tianjin University of Technology, Tianjin, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Xuan", "Affiliation": "Key Laboratory of Computer Vision and System, Ministry of Education, Tianjin University of Technology, Tianjin, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Computer Vision and System, Ministry of Education, Tianjin University of Technology, Tianjin, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong Artificial Intelligence Institute, Qilu University of Technology (Shandong Academy of Sciences), Jinan, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Computer Vision and System, Ministry of Education, Tianjin University of Technology, Tianjin, People’s Republic of China"}], "References": [{"Title": "Semi-supervised multi-view clustering with Graph-regularized Partially Shared Non-negative Matrix Factorization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "190", "Issue": "", "Page": "105185", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep graph regularized non-negative matrix factorization for multi-view clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "390", "Issue": "", "Page": "108", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-view clustering by non-negative matrix factorization with co-orthogonal constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105582", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An overview of recent multi-view clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Athana<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "402", "Issue": "", "Page": "148", "JournalTitle": "Neurocomputing"}, {"Title": "Discriminative subspace matrix factorization for multiview data clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107676", "JournalTitle": "Pattern Recognition"}, {"Title": "A novel consensus learning approach to incomplete multi-view clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "107890", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-view data clustering via non-negative matrix factorization with manifold regularization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "677", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 93417050, "Title": "A review of cybersickness in head-mounted displays: raising attention to individual susceptibility", "Abstract": "Cybersickness still poses a significant challenge to the widespread usage of virtual reality, leading to different levels of discomfort and potentially breaking the immersive experience. Researchers have attempted to discover the possible fundamental causes of cybersickness for years. Despite the longstanding interest in the research field, inconsistent results have been drawn on the contributing factors and solutions to combating cybersickness. Moreover, little attention has been paid to individual susceptibility. A consolidated explanation remains under development, requiring more empirical studies with robust and reproducible methodologies. This review presents an integrated survey connecting the findings from previous review papers and the state of the art involving empirical studies and participants. A literature review is then presented, focusing on the practical studies of different contributing factors, the pros and cons of measurements, profiles of cybersickness, and solutions to reduce this phenomenon. Our findings suggest a lack of considerations regarding user susceptibility and gender balance in between groups studies. In addition, incongruities among empirical findings raised concerns. We conclude by suggesting points of insights for future empirical investigations.", "Keywords": "Cybersickness; Virtual reality; Literature review; Individual susceptibility", "DOI": "10.1007/s10055-022-00638-2", "PubYear": 2022, "Volume": "26", "Issue": "4", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne, Lausanne, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidade Lusófona, Lisbon, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne, Lausanne, Switzerland"}], "References": [{"Title": "A Comparative Study of Virtual Reality Methods of Interaction and Locomotion Based on Presence, Cybersickness, and Usability", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1542", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Effect of VR technology matureness on VR sickness", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "14491", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A study of cybersickness and sensory conflict theory using a motion-coupled virtual reality system", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101922", "JournalTitle": "Displays"}, {"Title": "Psychometric evaluation of Simulator Sickness Questionnaire and its variants as a measure of cybersickness in consumer virtual environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "", "Page": "102958", "JournalTitle": "Applied Ergonomics"}, {"Title": "Effects of steering locomotion and teleporting on cybersickness and presence in HMD-based virtual reality", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "453", "JournalTitle": "Virtual Reality"}, {"Title": "Evaluating discrete viewpoint control to reduce cybersickness in virtual reality", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "4", "Page": "645", "JournalTitle": "Virtual Reality"}, {"Title": "Narrative and gaming experience interact to affect presence and cybersickness in virtual reality", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "102398", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Virtual Reality Is Sexist: But It Does Not Have to Be", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "4", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Cybersickness in Virtual Reality Head-Mounted Displays: Examining the Influence of Sex Differences and Vehicle Control", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "12", "Page": "1161", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Estimating cybersickness from virtual reality applications", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "165", "JournalTitle": "Virtual Reality"}, {"Title": "Virtual Reality Sickness: A Review of Causes and Measurements", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "17", "Page": "1658", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "A novel method for VR sickness reduction based on dynamic field of view processing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "331", "JournalTitle": "Virtual Reality"}, {"Title": "Effects of dynamic field-of-view restriction on cybersickness and presence in HMD-based virtual reality", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "433", "JournalTitle": "Virtual Reality"}, {"Title": "Exploratory factor analysis and validity of the virtual reality symptom questionnaire and computer use survey", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "1", "Page": "69", "JournalTitle": "Ergonomics"}, {"Title": "Identifying Causes of and Solutions for Cybersickness in Immersive Technology: Reformulation of a Research and Development Agenda", "Authors": "<PERSON>; <PERSON>; Bas R<PERSON>s", "PubYear": 2020, "Volume": "36", "Issue": "19", "Page": "1783", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Latency and Cybersickness: Impact, Causes, and Measures. A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "31", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "Quantitative measures of the visually evoked sensation of body movement in space (Vection) using Electrovestibulography (EVestG)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "731", "JournalTitle": "Virtual Reality"}, {"Title": "Quantitative measures of the visually evoked sensation of body movement in space (Vection) using Electrovestibulography (EVestG)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "731", "JournalTitle": "Virtual Reality"}, {"Title": "Variations in visual sensitivity predict motion sickness in virtual reality", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "", "Page": "100423", "JournalTitle": "Entertainment Computing"}, {"Title": "Cybersickness in current-generation virtual reality head-mounted displays: systematic review and outlook", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "1153", "JournalTitle": "Virtual Reality"}, {"Title": "Cybersickness in current-generation virtual reality head-mounted displays: systematic review and outlook", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "1153", "JournalTitle": "Virtual Reality"}, {"Title": "The Visually Induced Motion Sickness Susceptibility Questionnaire (VIMSSQ): Estimating Individual Susceptibility to Motion Sickness-Like Symptoms When Using Visual Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "65", "Issue": "1", "Page": "107", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "The Visually Induced Motion Sickness Susceptibility Questionnaire (VIMSSQ): Estimating Individual Susceptibility to Motion Sickness-Like Symptoms When Using Visual Devices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "65", "Issue": "1", "Page": "107", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "The influence of personality, sound, and content difficulty on virtual reality sickness", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "2", "Page": "631", "JournalTitle": "Virtual Reality"}, {"Title": "Field-of-View Restriction to Reduce VR Sickness Does Not Impede Spatial Learning in Women", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Applied Perception"}, {"Title": "Field-of-View Restriction to Reduce VR Sickness Does Not Impede Spatial Learning in Women", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Applied Perception"}, {"Title": "Field-of-View Restriction to Reduce VR Sickness Does Not Impede Spatial Learning in Women", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Applied Perception"}, {"Title": "Quantifying VR cybersickness using EEG", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "77", "JournalTitle": "Virtual Reality"}, {"Title": "On the Effect of Standing and Seated Viewing of 360° Videos on Subjective Quality Assessment: A Pilot Study", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "6", "Page": "80", "JournalTitle": "Computers"}]}, {"ArticleId": 93417203, "Title": "A new survival analysis model in adjuvant Tamoxifen-treated breast cancer patients using manifold-based semi-supervised learning", "Abstract": "Breast cancer researchers are very interested in studying and analyzing the survival time of patients. However, choosing the appropriate model for survival time analysis is the main challenge in the survival analysis of these patients. Using a dataset of patients with breast cancer, a new survival analysis model is proposed in this study. First, using the Cox-PH model, the whole selected data was categorized into high-risk and low-risk groups. The classified data was given to the AFT model in the next step to estimate the survival time. In some instances, the value of the metabolic status of the Tamoxifen feature was missing. Next, using the information obtained from the first and second steps, in the proposed Manifold-based semi-supervised learning module, the value of missing data was estimated. Using all labeled data, the final survival analysis was performed, and the relative hazard obtained. The comparison of the proposed model via other previous models in survival analysis demonstrates that the proposed model is 26% more accurate than when only the Cox-PH model was used and 28% more accurate than when only the AFT model was used in the testing set. Furthermore, the findings demonstrate that when the Manifold-based SSL model was used in the learning module, the accuracy was 19% higher than the Co-training SSL method and 12% higher than the SVMSSL method. The advantages of the proposed survival analysis method are high capability for identifying the survival risk classes of the patient and high predictive accuracy for the relative hazard.", "Keywords": "Adjuvant Tamoxifen-treated ; Breast cancer ; Machine learning ; Manifold learning ; Pharmacogenomics ; Semi-supervised learning ; Survival analysis", "DOI": "10.1016/j.jocs.2022.101645", "PubYear": 2022, "Volume": "61", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Genetic Engineering and Biotechnology (NIGEB), Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, University College of Engineering, University of Tehran, Tehran, Iran"}], "References": [{"Title": "Improved survival analysis by learning shared genomic information from pan-cancer data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "Supplement_1", "Page": "i389", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 93417205, "Title": "Manufacturer’s selling mode choice in a platform-oriented dual channel supply chain", "Abstract": "This paper considers a platform-oriented dual channel supply chain (PDCSC), in which the online retailer resells products in a marketplace provided by the platform and the manufacturer chooses a selling mode to introduce a dual channel. Three possible selling modes for the manufacturer are considered: (1) Reselling mode (Mode R); (2) Agency selling mode (Mode A); and (3) Direct selling mode (Mode D). We examine the manufacturer’s equilibrium strategy and how this choice affects the other supply chain members’ preferences. By comparing equilibrium profits in the three selling modes, we demonstrate that the manufacturer prefers Mode D when the direct operating cost is relatively low. Otherwise, either Mode R or Mode A is optimal, largely depending on the interaction between competition intensity and commission rate. More specifically, if both the competition intensity and commission rate are low, Mode A is the optimal choice; if the competition intensity is low but commission rate is high, Mode R is optimal; if the commission rate is relatively high, the manufacturer should choose Mode R. Finally, we further characterize the influences of the different selling modes on the profits of the online retailer, the platform, and the whole supply chain. We also extend the model by considering the case where the online retailer and platform have equal channel power and set prices simultaneously, the case of Cournot competition, and the case where the platform acts as the channel leader.", "Keywords": "Online retailing ; Platform-oriented supply chain ; Selling mode choice ; Game theory", "DOI": "10.1016/j.eswa.2022.116842", "PubYear": 2022, "Volume": "198", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Modern Logistics, Henan University, Kaifeng 475004, China;School of Business, Henan University, Kaifeng 475004, China;School of Transportation and Logistics, Southwest Jiaotong University, Chengdu 611756, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering Management, School of Civil Engineering, Wuhan University, Wuhan 430072, China;School of Data Science, City University of Hong Kong, Hong Kong;Corresponding author at: Department of Engineering Management, School of Civil Engineering, Wuhan University, Wuhan 430072, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Institute of FTZ Supply Chain, Shanghai Maritime University, Shanghai 201306, China;Yonsei Frontier Lab, Yonsei University, Seoul, South Korea;Center for Sustainable Supply Chain Engineering, Department of Technology and Innovation, Danish Institute for Advanced Study, University of Southern Denmark, Campusvej 55, Odense M, Denmark"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Advanced Design and Systems Engineering, City University of Hong Kong, 83 Tat Chee Avenue, Kowloon Tong, Hong Kong;City University of Hong Kong Shenzhen Research Institute, Shenzhen, China"}], "References": [{"Title": "Effect of online product reviews on third parties’ selling on retail platforms", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "", "Page": "100900", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Remanufacturing in a competitive market: A closed-loop supply chain in a Stackelberg game framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113655", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Direct selling, agent selling, or dual-format selling: Electronic channel configuration considering channel competition and platform service", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107368", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Multi-outsourcing supply chain coordination under yield and demand uncertainties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "115177", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 93417212, "Title": "Aperiodic SFTs on Baumslag-Solitar groups", "Abstract": "We study the periodicity of subshifts of finite type (SFT) on Baumslag-Solitar groups. We show that for residually finite Baumslag-Solitar groups there exist both strongly and weakly-but-not-strongly aperiodic SFTs. In particular, this shows that unlike Z 2 , but like Z 3 , strong and weak aperiodic SFTs are different classes of SFTs in residually finite BS groups. More precisely, we prove that a weakly aperiodic SFT on B S ( m , n ) due to <PERSON><PERSON><PERSON> and <PERSON><PERSON> is, in fact, strongly aperiodic on B S ( 1 , n ) ; and weakly but not strongly aperiodic on any other B S ( m , n ) . In addition, we exhibit an SFT which is weakly but not strongly aperiodic on B S ( 1 , n ) ; and we show that there exists a strongly aperiodic SFT on B S ( n , n ) .", "Keywords": "Symbolic dynamics ; Aperodicity ; Subshift of finite type ; Tilings ; <PERSON><PERSON>ley graphs ; Substitutions ; Groups", "DOI": "10.1016/j.tcs.2022.03.010", "PubYear": 2022, "Volume": "917", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Solène J. <PERSON>nay", "Affiliation": "Université de Lyon, CNRS, ENS de Lyon, UCBL, LIP, F-69342, Lyon Cedex 07, France;Institut de Mathématiques de Toulouse, Université Paul Sabatier, 118 route de Narbonne, F-31062, Toulouse Cedex 9, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CNRS, Aix Marseille Univ., I2M, Marseille, France;Corresponding author"}], "References": []}, {"ArticleId": 93417243, "Title": "NaOH induced oxygen deficiency for fluorescent SrVO3- perovskite and its application in glucose detection", "Abstract": "We report a low-temperature annealing method to induce fluorescent traits in SrVO<sub>3</sub> for the first time. The lateral two-dimensional nanomaterial was synthesized in presence of NaOH to induce oxygen deficient surface defects, thus resulting in generation of excitons. It is found that NaOH can intercalate to the crystal structure during pre-annealing homogenization, and de-intercalate while annealing, consequently creating voids on the surface to form SrVO<sub>3- x </sub>. NaOH is also seen to stabilize the B-site metal cation at a higher oxidation state V<sup>4+</sup>, that contributes to the emission at 540 nm in the visible spectrum. Owing to its oxygen deficient structure, the aqueous dispersion of as-synthesized SrVO<sub>3- x </sub> nanosheets was seen to be stable at room temperature. To further enhance its optical characteristics and aqueous stability, we doped Zn on the surface of the nanosheets. Further to this, hydrogen peroxide mediated oxidation of SrVO<sub>3- x </sub> nanosheets resulted in fluorescence quenching at emission maxima. Based on the quenching attribute of the nanosheets, we demonstrate a sensitive detection of glucose molecule in an aqueous as well as biological fluid spiked solutions. A remarkable 15 nM limit of detection in an aqueous medium makes this material highly responsive to the trace amounts of glucose. The robust optical properties of SrVO<sub>3- x </sub> nanomaterials exhibit huge potential for further investigation towards real-time monitoring soft-bioelectronics applications.", "Keywords": "SrVO<sub>3- x </sub> perovskite ; Fluorescence ; Sodium hydroxide ; Oxygen deficiency ; Zinc capping", "DOI": "10.1016/j.snb.2022.131685", "PubYear": 2022, "Volume": "362", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, National Sun Yat-Sen University, No. 70, Lien-Hai Road, Kaohsiung 80424, Taiwan, ROC"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, National Sun Yat-Sen University, No. 70, Lien-Hai Road, Kaohsiung 80424, Taiwan, ROC"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry, National Sun Yat-Sen University, No. 70, Lien-Hai Road, Kaohsiung 80424, Taiwan, ROC"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, National Sun Yat-Sen University, No. 70, Lien-Hai Road, Kaohsiung 80424, Taiwan, ROC;School of Pharmacy, College of Pharmacy, Kaohsiung Medical University, Kaohsiung 807, Taiwan, ROC;Institute of Medical Science and Technology, National Sun Yat-Sen University, Kaohsiung 80424, Taiwan, ROC;International PhD Program for Science, National Sun Yat-Sen University, Kaohsiung 80424, Taiwan, ROC;Corresponding author at: Department of Chemistry, National Sun Yat-Sen University, No. 70, Lien-Hai Road, Kaohsiung 80424, Taiwan, ROC"}], "References": []}, {"ArticleId": 93417244, "Title": "Enabling 5G indoor services for residential environment using VLC technology", "Abstract": "Visible light communication (VLC) has emerged as a viable complement to traditional radio frequency (RF) based systems and as an enabler for high data rate communications for beyond-5G (B5G) indoor communication systems. In particular, the emergence of new B5G-based applications with quality of service (QoS) requirements and massive connectivity has recently led to research on the required service-levels and the development of improved physical (PHY) layer methods. As part of recent VLC standards development activities, the IEEE has formed the 802.11bb “Light Communications (LC) for Wireless Local Area Networking” standardization group. This paper investigates the network requirements of 5G indoor services such as virtual reality (VR) and high-definition (HD) video for residential environments using VLC. In this paper, we consider such typical VLC scenarios with additional impairments such as light-emitting diode (LED) nonlinearity and imperfect channel feedback, and propose hyperparameter-free mitigation techniques using Reproducing Kernel Hilbert Space (RKHS) methods. In this context, we also propose using a direct current biased optical orthogonal frequency division multiplexing (DCO-OFDM)-based adaptive VLC transmission method that uses precomputed bit error rate (BER) expressions for these RKHS-based detection methods and performs adaptive BER-based modulation-order switching. Simulations of channel impulse responses (CIRs) show that the adaptive transmission method provides significantly improved error rate performance, which makes it promising for high data rate VLC-based 5G indoor services.", "Keywords": "Visible light communication (VLC) ; Ray-tracing ; Adaptive transmission ; 5G services", "DOI": "10.1016/j.phycom.2022.101679", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Isik University, Department of Electrical and Electronics Engineering, 34980 Istanbul, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Texas at Dallas, Department of Electrical and Computer Engineering, 75080 Richardson, TX, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre Tecnològic de Telecomunicacions de Catalunya (CTTC), 08860 Barcelona, Spain"}, {"AuthorId": 4, "Name": "Rangeet Mitra", "Affiliation": "Ècole de Technologie Supèrieure (ETS), H3C1K3 Montreal, Canada"}], "References": [{"Title": "Least minimum symbol error rate based post-distortion for adaptive mobile VLC transmission with receiver selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101353", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 93417330, "Title": "Structure Strength Dynamic Strain Measurement Acquisition System Based on Data Fusion", "Abstract": "<p>Strain measurement technology plays an irreplaceable role in measuring the force change characteristics of mechanical components and monitoring important engineering structures. In structural health monitoring, dynamic stress and strain testing is the main way to determine its overall fatigue, and excessive stress will seriously affect the strength of the structure, causing the structure to deform or even break, which will affect the reliability of the experiment and the use of workpieces, so it is of great significance to carry out dynamic stress and strain tests on the structure. Resistance strain measurement technology is widely used in the civil engineering industry, but the principles of modern physics technology are unknown to many practitioners. Data fusion technology or information fusion technology is also called multisensor fusion technology. Using computer technology to automatically analyze and integrate the data processing process of the detection data from multiple sensors according to time sequence and certain criteria, it can complete the required decision-making and judgment.</p>", "Keywords": "", "DOI": "10.1155/2022/9689404", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Dynamic Measurement Technology, North University of China, Taiyuan, Shanxi 030051, China"}, {"AuthorId": 2, "Name": "Dongxing Pei", "Affiliation": "State Key Laboratory of Dynamic Measurement Technology, North University of China, Taiyuan, Shanxi 030051, China"}, {"AuthorId": 3, "Name": "Tiehua Ma", "Affiliation": "State Key Laboratory of Dynamic Measurement Technology, North University of China, Taiyuan, Shanxi 030051, China"}], "References": []}, {"ArticleId": 93417343, "Title": "Machine-Learning-Based Road Soft Soil Foundation Treatment and Settlement Prediction Method", "Abstract": "<p>In order to effectively predict the settlement of soft soil foundation, improve the accuracy of road soft soil foundation settlement prediction, and improve the safety of the project, this paper proposes an optimized SVM-AR model and discusses the application scope of the SVM model and the time series AR model, respectively. The SVM-AR model is proposed by combining the respective advantages of the two types of models. Firstly, the prediction method of foundation settlement is analyzed and studied, and then the improved ABC algorithm is used to optimize the SVM model. Secondly, the optimized SVM model is combined with the AR model, the ABC-SVM model is used to predict the trend settlement, and the AR model is used to predict the random settlement and then combined to obtain the predicted settlement. The example verification shows that SVM-AR is more accurate than the SVM model prediction results and better reflects the settlement process of highway soft soil foundation.</p>", "Keywords": "", "DOI": "10.1155/2022/3463413", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yinchuan University of Science and Technology, Yinchuan, Ningxia 750021, China"}], "References": []}, {"ArticleId": 93417354, "Title": "A joint computational and resource allocation model for fast parallel data processing in fog computing", "Abstract": "<p>Fog computing can be an effective way to improve quality of services and solve network problems, with the demand for real-time, latency-sensitive applications increasing as well as limitations such as network bandwidth and Internet of Things users' resources. Due to the fact that different tasks in a network can create overhead that can reduce the quality of service, dynamic voltage, and frequency scaling along with a ranking function and a high number of physical and virtual machines were used in this research. A profit function phase is used to analyze network tasks in order to improve QoS by sending them to physical machines and sending them via the network to physical machines. The simulation results demonstrate that this method is the most effective in allocating radio and computational resources to IoT devices in fog computing. A comparison is presented in the results section between the proposed method and the SPA, Markov-Fog, and TRAM methods. Criteria for evaluating performance include the response time for heterogeneous environments, energy consumption against tasks and users, memory processing, energy consumption for physical and virtual machines, and network profitability. </p>", "Keywords": "Fog computing; IoT; Cloud computing; Computational resource; Quality of service; DVFS", "DOI": "10.1007/s11227-022-04374-x", "PubYear": 2022, "Volume": "78", "Issue": "10", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Kerman Branch, Islamic Azad University, Kerman, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, <PERSON>id <PERSON> University of Kerman, Kerman, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Kerman Branch, Islamic Azad University, Kerman, Iran"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Guangzhou University, Guangzhou, China"}], "References": [{"Title": "RETRACTED ARTICLE: Detecting straggler MapReduce tasks in big data processing infrastructure by neural network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "9", "Page": "6969", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "TRAM: Technique for resource allocation and management in fog computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "1", "Page": "667", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "cTMvSDN: improving resource management using combination of Markov-process and TDMA in software-defined networking", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "3477", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 93417582, "Title": "Ability boosted knowledge tracing", "Abstract": "Knowledge tracing (KT) has become an increasingly relevant problem in intelligent education services, which estimates and traces the degree of learner’s mastery of concepts based on students’ responses to learning resources. The existing mainstream KT models, only attribute learners’ feedback to the degree of knowledge mastery and leave the influence of mental ability factors out of consideration. Although ability is an essential component of the problem-solving process, these knowledge-centered models cause a contradiction between data fitting and rationalization of the model decision-making process, making it difficult to achieve high precision and readability simultaneously. In this paper, an innovative KT model, a bility b oosted k nowledge t racing (ABKT) <sup>1</sup> is proposed, which introduces the ability factor into learning feedback attribution to enable the model to analyze the learning process from two perspectives, knowledge and ability, simultaneously. Based on constructive learning theory, continuous matrix factorization (CMF) model is proposed to simulate the knowledge internalization process, following the initiative growth and stationarity principles. In addition, the linear graph latent ability (LGLA) model is proposed to construct learner and item latent ability features, from graph-structured learner interaction data. Then, the knowledge and ability dual-tracing framework is constructed to integrate the knowledge and ability modules. Experimental results on four public databases indicate that the proposed methods perform better than state-of-the-art knowledge tracing algorithms in terms of prediction accuracy in quantitative assessments , displaying some advantages in model interpretability and intelligibility.", "Keywords": "", "DOI": "10.1016/j.ins.2022.02.044", "PubYear": 2022, "Volume": "596", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Educational Big Data, Central China Normal University, Wuhan 430079, China;National Engineering Research Center for E-Learning, Central China Normal University, Wuhan 430079, China;Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan 430079, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan 430079, China;Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan 430079, China"}, {"AuthorId": 3, "Name": "Qing Li", "Affiliation": "National Engineering Laboratory for Educational Big Data, Central China Normal University, Wuhan 430079, China;Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan 430079, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Educational Big Data, Central China Normal University, Wuhan 430079, China;Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan 430079, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Educational Big Data, Central China Normal University, Wuhan 430079, China;Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan 430079, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Educational Big Data, Central China Normal University, Wuhan 430079, China;Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan 430079, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Educational Big Data, Central China Normal University, Wuhan 430079, China;Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan 430079, China;Corresponding authors"}], "References": [{"Title": "Knowledge modeling via contextualized representations for LSTM-based personalized exercise recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "523", "Issue": "", "Page": "266", "JournalTitle": "Information Sciences"}, {"Title": "Exam paper generation based on performance prediction of student group", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "532", "Issue": "", "Page": "72", "JournalTitle": "Information Sciences"}, {"Title": "JKT: A joint graph convolutional network based Deep Knowledge Tracing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "510", "JournalTitle": "Information Sciences"}, {"Title": "Logistic Knowledge Tracing: A Constrained Framework for Learner Modeling", "Authors": "<PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "5", "Page": "624", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": 93417590, "Title": "Matrix approach for fuzzy description reduction and group decision-making with fuzzy β -covering", "Abstract": "Fuzzy β -minimal description ( β -FMiD) and fuzzy β -maximal description ( β -FMaD) are two basic information granules. Their uses include eliminating redundant information when describing an object contained in this information and inducing different types of fuzzy β -neighborhoods. Currently, various operators (such as approximation operators, positive domain operators, and dependency functions) induced by fuzzy β -neighborhoods are commonly used to deal with attribute reduction problems. Essentially, β -FMiD and β -FMaD are the basis of the existing methods. Hence, it is necessary to use β -FMiD and β -FMaD directly to solve attribute reduction, which will simplify the calculations and improve the redundancy rate. However, it is time-consuming and error-prone when employing the existing methods to calculate β -FMiD and β -FMaD. To solve this problem, novel matrix approaches of β -FMiD and β -FMaD are proposed, which are applied to two categories of reductions and to the issue of multi-criteria group decision-making (MCGDM) in this research. One approach is the correction and improvement of the existing method; the other is a novel method by using the matrix representation of fuzzy β -covering directly and the binary relation of inclusion between elements in the fuzzy β -covering. In addition, four classifications of fuzzy β -neighborhood operators are calculated by the matrix representations of β -FMiD and β -FMaD. Two new categories of reductions of fuzzy β -covering information systems ( β -FcISs) are also proposed via β -FMiD and β -FMaD, respectively. These two types of reductions are calculated via matrices and applied to the problem of MCGDM to eliminate fuzzy granular structures, which are not needed to reach a decision in MCGDM. Finally, several experimental comparisons are posited to show the computational efficiency of the proposed approaches on nine UCI datasets. Experimental results demonstrate that the suggested methods are promising and comparable to other designs.", "Keywords": "", "DOI": "10.1016/j.ins.2022.03.039", "PubYear": 2022, "Volume": "597", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Data Science, Shaanxi University of Science and Technology, Xi’an 710021, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Data Science, Shaanxi University of Science and Technology, Xi’an 710021, PR China;Shaanxi Joint Laboratory of Artificial Intelligence, Shaanxi University of Science and Technology, Xi’an 710021, PR China;Corresponding author at: School of Mathematics and Data Science, Shaanxi University of Science and Technology, Xi’an 710021, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Regina, SK S4S 0A2, Canada"}], "References": [{"Title": "Two types of coverings based multigranulation rough fuzzy sets and applications to decision making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "1", "Page": "167", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Covering based multigranulation fuzzy rough sets and corresponding applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1093", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A three-way decision approach with probabilistic dominance relations under intuitionistic fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "582", "Issue": "", "Page": "114", "JournalTitle": "Information Sciences"}, {"Title": "Grained matrix and complementary matrix: Novel methods for computing information descriptions in covering approximation spaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "591", "Issue": "", "Page": "68", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 93417594, "Title": "Interpolation-based reversible data hiding in the transform domain for fingerprint images", "Abstract": "<p>One major motivation for introducing Reversible Data Hiding (RDH) techniques in biometric systems is the need to provide a higher security level to biometric images without compromising their recognition accuracy. In this paper, a novel RDH scheme that merges the advantages of both image interpolation approaches and the transform-based data hiding schemes is proposed for fingerprint images. Since the secret data is hidden into the transform coefficients, the proposed scheme relies on setting back the reference pixels to their original values to maintain cover image reversibility. To compensate for the distortions induced by the aforementioned operation, we have adapted the robust DM-QIM embedding method, designed an enhanced perceptual masking model for fingerprint images and developed a novel iterative correction algorithm. As a result, the proposed scheme allows to achieve error-free embedding with no extra information needed for image or secret data recovery. Experimental results show that the proposed scheme can effectively ensure full system reversibility and provides higher performance in terms of embedding capacity and visual quality when compared with recent state-of-the-art techniques.</p>", "Keywords": "Interpolation; Reversible; Data hiding; Wavelet transform; DM-QIM; Embedding; Fingerprint; Perceptual masking", "DOI": "10.1007/s11042-022-12288-2", "PubYear": 2022, "Volume": "81", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire Systèmes Electroniques et Numériques, Ecole Militaire Polytechnique, Algiers, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratoire Systèmes Electroniques et Numériques, Ecole Militaire Polytechnique, Algiers, Algeria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratoire Systèmes Electroniques et Numériques, Ecole Militaire Polytechnique, Algiers, Algeria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Laboratoire Télécommunications, Ecole Militaire Polytechnique, Algiers, Algeria"}], "References": [{"Title": "Comparative analysis of integer wavelet transforms in reversible data hiding using threshold based histogram modification", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "878", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Reversible data hiding methods in integer wavelet transform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "1", "Page": "70", "JournalTitle": "International Journal of Information and Computer Security"}, {"Title": "Effective reversible data hiding using dynamic neighboring pixels prediction based on prediction-error histogram", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "12569", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Reversible Data Hiding Scheme for Interpolated Images Based on Pixel Intensity Range", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "18005", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Novel embedding secrecy within images utilizing an improved interpolation-based reversible data hiding scheme", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "2017", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Efficient reversible data hiding multimedia technique based on smart image interpolation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "39-40", "Page": "30087", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient general data hiding scheme based on image interpolation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "102584", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "High capacity reversible and secured data hiding in images using interpolation and difference expansion technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "3", "Page": "3623", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A new reversible data hiding in transform domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "6", "Page": "8931", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93417675, "Title": "A novel method of shuttlecock trajectory tracking and prediction for a badminton robot", "Abstract": "<p>This paper proposes an aerodynamic analysis of the shuttlecock and a novel method for predicting shuttlecock trajectory. First, we have established a shuttlecock track data set by an infrared-based binocular vision system. Then the unscented <PERSON><PERSON> filter algorithm is designed to further filter the noise and visual recognition algorithm errors. Third, the radial basis function (RBF)-based track prediction model is designed. This method offers a concept to obtain the neural network model of different kinds of flying or moving objects. The experimental results show that the proposed method can predict the shuttlecock trajectory in real time at high accuracy and can be used for implementing the algorithm of return strategies in the near future.</p>", "Keywords": "Target tracking;Prediction;RBF network", "DOI": "10.1017/S0263574721001053", "PubYear": 2022, "Volume": "40", "Issue": "6", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "Junming Zhi", "Affiliation": "Mechanical Engineering, School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China , SiChuan Province China "}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering, School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China , SiChuan Province China "}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering, School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China , SiChuan Province China "}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering, School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China , SiChuan Province China "}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering, School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China , SiChuan Province China "}], "References": [{"Title": "Multi-Agent Tracking of Non-Holonomic Mobile Robots via Non-Singular Terminal Sliding Mode Control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "11", "Page": "1984", "JournalTitle": "Robotica"}, {"Title": "Linear Quadratic Regulator Method in Vision-Based Laser Beam Tracking for a Mobile Target Robot", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "3", "Page": "524", "JournalTitle": "Robotica"}, {"Title": "An Adaptive Regressor-Free Fourier Series-Based Tracking Controller for Robot Manipulators: Theory and Experimental Evaluation", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "11", "Page": "1981", "JournalTitle": "Robotica"}, {"Title": "Agoraphilic navigation algorithm in dynamic environment with obstacles motion tracking and prediction", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "2", "Page": "329", "JournalTitle": "Robotica"}]}, {"ArticleId": 93417682, "Title": "An Empirical Evaluation of Network Representation Learning Methods", "Abstract": "<p>Network representation learning methods map network nodes to vectors in an embedding space that can preserve specific properties and enable traditional downstream prediction tasks. The quality of the representations learned is then generally showcased through results on these downstream tasks. Commonly used benchmark tasks such as link prediction or network reconstruction, however, present complex evaluation pipelines and an abundance of design choices. This, together with a lack of standardized evaluation setups, can obscure the real progress in the field. In this article, we aim at investigating the impact on the performance of a variety of such design choices and perform an extensive and consistent evaluation that can shed light on the state-of-the-art on network representation learning. Our evaluation reveals that only limited progress has been made in recent years, with embedding-based approaches struggling to outperform basic heuristics in many scenarios.</p>", "Keywords": "benchmark;evaluation;link prediction;network embedding;network reconstruction;representation learning", "DOI": "10.1089/big.2021.0107", "PubYear": 2024, "Volume": "12", "Issue": "6", "JournalId": 18108, "JournalTitle": "Big Data", "ISSN": "2167-6461", "EISSN": "2167-647X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Information Systems, Ghent University, Ghent, Belgium."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Information Systems, Ghent University, Ghent, Belgium."}, {"AuthorId": 3, "Name": "Tijl De Bie", "Affiliation": "Department of Electronics and Information Systems, Ghent University, Ghent, Belgium."}], "References": []}, {"ArticleId": 93417708, "Title": "Investigating the influence of big data analytics capabilities and human resource factors in achieving supply chain innovativeness", "Abstract": "The current study aims to bridge the gap in understanding how big data analytics and human resource factors impact supply chain innovation and enhance supply chain sustainability. For empirical investigation, data were collected from employees working in manufacturing firms. For inferential analysis 341 valid responses were used. Results indicate that supply chain innovation is predicted by big data analytic capability, big data analytic staff capability, employee development, employee empowerment and employee involvement and explained large variance R 2 0.538 % in measuring supply chain innovativeness. Practically, this research suggests that policy makers should focus on supply chain innovativeness, big data analytics, big data analytics staff capability and supply chain connectivity to enhance sustainable performance in supply chain operations. Theoretically, this research has synthesized literature and develops an integrated research model that combines technology and human resource factors altogether to investigate supply chain innovation and sustainability in supply chain performance.", "Keywords": "Big data analytics ; Employee development ; Employee empowerment ; Employee involvement ; Supply chain innovativeness", "DOI": "10.1016/j.cie.2022.108055", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Jeddah, Collage of Business, Department of Human Resources Management, Jeddah, Saudi Arabia"}], "References": [{"Title": "Dynamic competitive game study of a green supply chain with R&D level", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "107785", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 93417720, "Title": "Mammogram breast cancer CAD systems for mass detection and classification: a review", "Abstract": "Although there is an improvement in breast cancer detection and classification (CAD) tools, there are still some challenges and limitations that need more investigation. The significant development in machine learning and image processing techniques in the last ten years affected hugely the development of breast cancer CAD systems especially with the existence of deep learning models. This survey presents in a structured way, the current deep learning-based CAD system to detect and classify masses in mammography, in addition to the conventional machine learning-based techniques. The survey presents the current publicly mammographic datasets, also provides a dataset-based quantitative comparison of the most recent techniques and the most used evaluation metrics for the breast cancer CAD systems. The survey provides a discussion of the current literature and emphasizes its pros and limitations. Furthermore, the survey highlights the challenges and limitations in the current breast cancer detection and classification techniques.", "Keywords": "CAD system; Breast cancer; Mammogram; Mass; Detection; Classification", "DOI": "10.1007/s11042-022-12332-1", "PubYear": 2022, "Volume": "81", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computing and Information Technology, Arab Academy for Science and Technology, Cairo, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer and Information Sciences, Ain Shams University, Cairo, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computing & Information Technology, Arab Academy for Science and Technology, Alexandria, Egypt"}], "References": [{"Title": "Unsupervised domain adaptation with adversarial learning for mass detection in mammogram", "Authors": "Rongbo Shen; Jianhua Yao; Kezhou Yan", "PubYear": 2020, "Volume": "393", "Issue": "", "Page": "27", "JournalTitle": "Neurocomputing"}, {"Title": "An improved scheme for digital mammogram classification using weighted chaotic salp swarm algorithm-based kernel extreme learning machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106266", "JournalTitle": "Applied Soft Computing"}, {"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 93417721, "Title": "Using neural network for the evaluation of physical education teaching in colleges and universities", "Abstract": "<p>Teaching evaluation is a crucial measure to improve the quality of education. It is essential to formulate a scientific and reasonable evaluation system for teaching in colleges and universities. Teaching is a dynamic process, which makes it challenging to evaluate it comprehensively. However, as a new technology, the artificial neural network has the characteristics of nonlinear processing, adaptive learning, and high fault tolerance for various evaluation problems. This article first analyzes the current status of physical education and proposes solutions that comprehensively improve the quality of physical education in colleges and other institutions. It also suggests that a scientific evaluation of teaching in colleges provides a better feedback, incentives, and guidance, respectively. A model for the quality evaluation of physical education in colleges is established using artificial neural network. This model quantifies the concept of teaching evaluation index into actual data as its input, and teaching effect as output. The use of artificial neural network framework maps the input with output using an internally configured decision support system. Furthermore, it uses relevant software to conduct empirical research. The results show that this model enhances the quality of teaching evaluation, which not only overcomes the subjective factors of experts in the evaluation process but also produces satisfactory evaluation result.</p>", "Keywords": "Evaluation; Neural networks; Physical education; Decision support system; Teaching quality", "DOI": "10.1007/s00500-022-06848-9", "PubYear": 2022, "Volume": "26", "Issue": "20", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huzhou University, Huzhou, China"}], "References": []}, {"ArticleId": 93417825, "Title": "SoftPool++: An Encoder–Decoder Network for Point Cloud Completion", "Abstract": "We propose a novel convolutional operator for the task of point cloud completion. One striking characteristic of our approach is that, conversely to related work it does not require any max-pooling or voxelization operation. Instead, the proposed operator used to learn the point cloud embedding in the encoder extracts permutation-invariant features from the point cloud via a soft-pooling of feature activations, which are able to preserve fine-grained geometric details. These features are then passed on to a decoder architecture. Due to the compression in the encoder, a typical limitation of this type of architectures is that they tend to lose parts of the input shape structure. We propose to overcome this limitation by using skip connections specifically devised for point clouds, where links between corresponding layers in the encoder and the decoder are established. As part of these connections, we introduce a transformation matrix that projects the features from the encoder to the decoder and vice-versa. The quantitative and qualitative results on the task of object completion from partial scans on the ShapeNet dataset show that incorporating our approach achieves state-of-the-art performance in shape completion both at low and high resolutions.", "Keywords": "Point cloud; Completion; SoftPool; Skip-connection", "DOI": "10.1007/s11263-022-01588-7", "PubYear": 2022, "Volume": "130", "Issue": "5", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technische Universität München, Munich, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Google, Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technische Universität München, Munich, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Technische Universität München, Munich, Germany;Google, Zurich, Switzerland"}], "References": [{"Title": "Robust Attentional Aggregation of Deep Feature Sets for Multi-view 3D Reconstruction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "1", "Page": "53", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Pix2Vox++: Multi-scale Context-aware 3D Object Reconstruction from Single and Multiple Images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "12", "Page": "2919", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 93417831, "Title": "Automatic segmentation of plant leaves disease using min-max hue histogram and k-mean clustering", "Abstract": "<p>Automatic segmentation of plant image’s leaf diseases has recently become a popular area of study worldwide. The suggested approach automatically segments various areas of leaf disease from images of the plant, which can then be combined with machine learning or deep learning techniques to improve system accuracy. Our suggested method consists of three stages: Preprocessing is applied in the first stage where a rank order fuzzy (ROF) filter is proposed that reduces the background noise from the plant picture. In the next stage, disease spot detection is performed using proposed min-max hue histogram based techniques. Disease spot identification prior to segmentation helps in proper segmentation of k-mean clustering. The K-means clustering is then performed in the next stage to segment the leaf pictures into uniform regions. These segments are transformed into HSI color spaces and the segment with the largest hue value is extracted as the disease segment. The proposed methodology is implemented in Matlab 18a and studies are carried out on various plant images. The proposed ROF filter demonstrates superior results to the other state-of-the-art filters. The filter is also resistant to very large noise levels, and shows meaningful details at noise levels of 95%. Besides, our hue-based spot detection is compared with the existing method and it can be shown by the suggested approach, the diseases have been found mostly correctly. The segmentation accuracy of the proposed method is calculated using the Jaccard coefficient, Sensitivity and Positive Prediction Rate. Our proposed system achieved high Jaccard coefficient value of 0.7747.</p>", "Keywords": "Contrast stretching; Fuzzy filter; Hue histogram; HSI color model; K-mean clustering; Segmentation", "DOI": "10.1007/s11042-022-12518-7", "PubYear": 2022, "Volume": "81", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DoCSE ,UIT-RGPV, Bhopal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DoCSE ,UIT-RGPV, Bhopal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "SoIT, UIT-RGPV, Bhopal, India"}], "References": []}, {"ArticleId": 93417837, "Title": "Knowledge guided learning: Open world egocentric action recognition with zero supervision", "Abstract": "Advances in deep learning have enabled the development of models that have exhibited a remarkable tendency to recognize and even localize actions in videos. However, they tend to experience errors when faced with scenes or examples beyond their initial training environment. Hence, they fail to adapt to new domains without significant retraining with large amounts of annotated data. In this paper, we propose to overcome these limitations by moving to an open-world setting by decoupling the ideas of recognition and reasoning. Building upon the compositional representation offered by <PERSON><PERSON><PERSON>’s Pattern Theory formalism, we show that attention and commonsense knowledge can be used to enable the self-supervised discovery of novel actions in egocentric videos in an open-world setting, where data from the observed environment (the target domain) is open i.e., the vocabulary is partially known and training examples (both labeled and unlabeled) are not available. We show that our approach can infer and learn novel classes for open vocabulary classification in egocentric videos and novel object detection with zero supervision . Extensive experiments show its competitive performance on two publicly available egocentric action recognition datasets (GTEA Gaze and GTEA Gaze+) under open-world conditions.", "Keywords": "Event understanding ; Open world reasoning ; Pattern Theory ; Egocentric activity understanding ; Commonsense Knowledge", "DOI": "10.1016/j.patrec.2022.03.007", "PubYear": 2022, "Volume": "156", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Oklahoma State University, Stillwater, OK 74078, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Oklahoma State University, Stillwater, OK 74078, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Oklahoma State University, Stillwater, OK 74078, USA"}], "References": [{"Title": "The Open Images Dataset V4", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "7", "Page": "1956", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 93417848, "Title": "DESIGN AND CONSTRUCTION OF A COVID-19 DOOR ENTRA<PERSON><PERSON> CHECKING SYSTEM", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2022.11211", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "Nnebe S. U", "Affiliation": ""}, {"AuthorId": 2, "Name": "Ebilite U.N", "Affiliation": ""}, {"AuthorId": 3, "Name": "Nwankwo V.I", "Affiliation": ""}], "References": []}, {"ArticleId": 93417856, "Title": "A multi-method approach to scheduling and efficiency analysis in dual-resource constrained job shops with processing time uncertainty", "Abstract": "In dual-resource constrained job shop scheduling, jobs have to be assigned to workers and machines. While the problem is notoriously hard in terms of computational complexity, additional practical difficulties arise from data uncertainty and worker efficiency variation. In this paper, we propose a methodological pipeline combining mathematical optimization, simulation, and data analysis to cope with these aspects over time. Accounting for uncertain worker processing times, we employ an iterative two-stage optimization-simulation approach: A first-stage optimization model determines an assignment of workers to jobs; in the second stage, the scheduling of this assignment is evaluated operationally using sampled realizations of worker processing times. Both stages are executed in an alternating fashion until no further improvement is possible in the average realized makespan. At the end of a work day, realized worker efficiencies are then measured in a slacks-based data envelopment analysis on the operations level. Workers learn about their individual efficiency and are prompted to reduce inefficiencies. The resulting overall methodology not only provides robustified schedules for dual-resource constrained job shops under uncertainty, but also reduces the impact of uncertainty over time by motivating workers to operate on the efficient frontier. Computational results are conducted in several settings with both heuristic and exact solution procedures for the second-stage dual-resource constrained job shop scheduling problem. The results demonstrate the versatility of the outline with respect to addressing uncertainty and worker inefficiency.", "Keywords": "Optimization under uncertainty ; Simulation optimization ; Dual-resource constrained scheduling ; Data envelopment analysis ; Operations management", "DOI": "10.1016/j.cie.2022.108067", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute for Operations Research, Discrete Optimization and Logistics, Kaiserstr. 12, 76131 Karlsruhe, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology, Institute for Operations Research, Discrete Optimization and Logistics, Kaiserstr. 12, 76131 Karlsruhe, Germany"}], "References": [{"Title": "Operations management issues in design and control of hybrid human-robot collaborative manufacturing systems: a survey", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "49", "Issue": "", "Page": "264", "JournalTitle": "Annual Reviews in Control"}, {"Title": "Efficient scheduling of a stochastic no-wait job shop with controllable processing times", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "113879", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An effective approach for the dual-resource flexible job shop scheduling problem considering loading and unloading", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3", "Page": "707", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Reinforcement Learning for Dual-Resource Constrained Scheduling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "10810", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Metamodel-based simulation optimization: A systematic literature review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "102403", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "A simulation-based modified backtracking search algorithm for multi-objective stochastic flexible job shop scheduling problem with worker flexibility", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107960", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 93417871, "Title": "DIFCS: A Secure Cloud Data Sharing Approach Based on Decentralized Information Flow Control", "Abstract": "Summary In the field of cloud computing, collaboration and data sharing among clouds have dramatically increased. How to protect the security of shared cloud data is being an urgent problem. Based on decentralized information flow control (DIFC) model, this paper presents an approach, DIFC-based data sharing approach (DIFCS), to protect both confidentiality and integrity of shared cloud data. Additionally, this paper designs a privilege protection policy for DIFCS, resulting in its capability of preventing malicious users from modifying privilege. The correctness and security properties of DIFCS are proved by formal analysis and verification, where firstly, DIFCS is formally interpreted into high-level petri net (HLPN) representation and analyzed using Z language, then is automatically verified with SMT-Lib and Z3 solver. The formal analysis and verification results reveal that DIFCS holds the security properties of confidentiality, integrity, authenticity, and privilege tamper-proof. The experimental results further demonstrate the high efficiency of DIFCS.", "Keywords": "Decentralized information flow control ; Cloud data sharing ; Privilege protection ; Model checking ; Formal verification", "DOI": "10.1016/j.cose.2022.102678", "PubYear": 2022, "Volume": "117", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 4, "Name": "Shuyuan Jin", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China;Corresponding author"}], "References": [{"Title": "Flexible attribute-based proxy re-encryption for efficient data sharing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "511", "Issue": "", "Page": "94", "JournalTitle": "Information Sciences"}, {"Title": "Blockchain data-based cloud data integrity protection mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "902", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A robust privacy preserving approach for electronic health records using multiple dataset with multiple sensitive attributes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "102224", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 93417879, "Title": "Template Matching Algorithm Based on Circular Projection and Radial Projection", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.123053", "PubYear": 2022, "Volume": "12", "Issue": "3", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "小强 陈", "Affiliation": ""}], "References": []}, {"ArticleId": 93417968, "Title": "Structure design and optimization of ground moving and pole climbing inspection robot", "Abstract": "Purpose \nThe purpose of this paper is to demonstrate a multipurpose inspection robot that can both walk on the ground and climb on poles. The structure design, size optimization, kinematics analysis, experiment and arithmetic of the robot are discussed in the paper.\n \n \n Design/methodology/approach \nThe robot consists of three adjustable modules and a two-degree-of-freedom parallel mechanism in tandem, and the wheel-finger mechanism of each module can realize wheel-finger opening and closing for fast movement and obstacle crossing. This paper uses geometric analysis and simulation analysis to derive size optimization, and vector coordinate method to derive kinematics. Finally, the experiment is carried out by simulating the working environment of the robot.\n \n \n Findings \nThe robot can realize ground walking and ground turning through the robot entity prototype experiment on the built working environment and efficiently realize 0°–90° pole climbing by the assemble design, optimization and machining. In addition, the robot can also smoothly complete the state transition process from 0° ground to 90° pole climbing. Furthermore, the robot shows good environmental self-adaptation and can complete daily inspection work.\n \n \n Originality/value \nThe robot can pitch and yaw at a large angle and has six-legged characteristics. It is a multipurpose inspection robot that can walk on the ground and climb on poles. And through structure design, size optimization, kinematics analysis and simulation, the existing robots’ common shortcomings such as poor barrier-crossing ability and poor environmental adaptability are solved.", "Keywords": "Simulation;Modularization;Inspection robot;Kinesiology;Multipurpose;Rotating-finger", "DOI": "10.1108/AA-12-2021-0168", "PubYear": 2022, "Volume": "42", "Issue": "2", "JournalId": 4373, "JournalTitle": "Assembly Automation", "ISSN": "0144-5154", "EISSN": "1758-4078", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University , Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University , Shenyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University , Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronics Engineering, Shenyang Aerospace University , Shenyang, China"}], "References": [{"Title": "The soft multi-legged robot inspired by octopus: climbing various columnar objects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "17", "Page": "1096", "JournalTitle": "Advanced Robotics"}]}, {"ArticleId": 93418108, "Title": "A GA-based approach with an interval VIKOR method for solving the constrained QoS-aware service composition in dynamic IoT environments", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJWGS.2022.10045715", "PubYear": 2022, "Volume": "18", "Issue": "3", "JournalId": 17784, "JournalTitle": "International Journal of Web and Grid Services", "ISSN": "1741-1106", "EISSN": "1741-1114", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93418124, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1002/itl2.299", "PubYear": 2022, "Volume": "5", "Issue": "2", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 93418141, "Title": "Effect of Socratic Reflection Prompts via video-based learning system on elementary school students’ critical thinking skills", "Abstract": "This study designed a Socratic Reflection Prompts via Video-Based Learning System (SRP-VBLS) and investigated the effects of the SRP-VBLS on elementary school 4th graders&#x27; critical thinking skills. Participants were divided into three groups: No-SRPs (n = 46), Receiving-SRPs (n = 42), and Responding-to-SRPs (n = 40). Participants in three groups were pre-and post-tested with a critical thinking assessment particularly designed for assessing elementary school students&#x27; critical thinking skills. Results show that the Responding-to-SRPs group and the Receiving-SRPs group significantly outperformed the No-SRPs group regarding their critical thinking skill improvement. It was also found that by responding to the SRPs, one of the critical thinking core skills, the analysis skill, of the students was significantly improved. This finding suggests that the SRP-VBLS is effective in developing elementary school students&#x27; critical thinking skills, and requiring students to respond to SRPs is especially beneficial to students’ analysis skill enhancement. Implications of the findings for designing a video-based learning system in line with the Socratic reflection prompts are discussed.", "Keywords": "Elementary education ; Evaluation methodologies ; Pedagogical issues ; Teaching/learning strategies ; 21st-century abilities", "DOI": "10.1016/j.compedu.2022.104497", "PubYear": 2022, "Volume": "183", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program of Learning Sciences, National Taiwan Normal University, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, National Sun Yat-sen University, Kaohsiung, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Foreign Languages, National Yunlin University of Science and Technology, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Program of Learning Sciences, National Taiwan Normal University, Taipei, Taiwan;Corresponding author"}], "References": []}, {"ArticleId": 93418142, "Title": "Effectiveness of virtual reality in discrete event simulation models for manufacturing systems", "Abstract": "Virtual reality (VR) and discrete event simulation (DES) have become fundamental technologies of industry 4.0. There is a growing interest in such technologies in both academic and industry sectors. Nevertheless, few studies have reported the use of them simultaneously. This study develops a DES model and proposes an integrated approach combining self-reported and objective measurements to determine the VR effectiveness, based on data collection and analysis of 72 volunteers randomly assigned either a VR system or a laptop computer. The model represents a real manufacturing system where the user can move around, interact with objects, modify variables, run different scenarios, and identify the best one based on the system’s key performance indicators. Results showed that experimentation time is significantly less when users interact with the VR interface, and there is no significant difference between the two interfaces when comparing the decision-making time. However, the VR users obtained a higher percentage of correct answers. Finally, self-reported feedback indicated users preferred the VR system, in addition to the fact that discomfort and presence questionnaires exhibit typical values of the VR experience, and the usability questionnaire yields higher values than the laptop computer.", "Keywords": "Virtual reality ; Discrete event simulation ; Industry 4.0", "DOI": "10.1016/j.cie.2022.108079", "PubYear": 2022, "Volume": "168", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "CIATEQ-POSGRADOS, Av. del Retablo #150, Constituyentes-Fovissste, Queretaro, Querétaro 76150, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CIATEQ-POSGRAD<PERSON>, Av. del Retablo #150, Constit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON>ro, Querétaro 76150, Mexico;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>-Rob<PERSON>o", "Affiliation": "Department of IT, Control, and Electronics, CIATEQ A. C., Av. 23 de Agosto #213, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tabasco 86040, Mexico"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Electrical Engineering and Electronics (DIEE), Center for Engineering and Industrial Development (CIDESI), Av. Playa Pie de la Cuesta 702, Desarrollo San Pablo, Queretaro, Qro. 76125, Mexico"}], "References": [{"Title": "Virtual reality tour for first-time users of highly automated cars: Comparing the effects of virtual environments with different levels of interaction fidelity", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "103226", "JournalTitle": "Applied Ergonomics"}, {"Title": "Discrete event simulation–based energy efficient path determination scheme for probabilistic voting–based filtering scheme in sensor networks", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "8", "Page": "155014772094913", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Simulation in industry 4.0: A state-of-the-art review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106868", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Assessing mental workload in virtual reality based EOT crane operations: A multi-measure approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "", "Page": "103017", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Enhanced cognitive workload evaluation in 3D immersive environments with TOPSIS model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "102572", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Towards the implementation of Industry 4.0: A methodology-based approach oriented to the customer life cycle", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "126", "Issue": "", "Page": "103403", "JournalTitle": "Computers in Industry"}, {"Title": "Implementing Industry 4.0 principles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107379", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A collaborative virtual reality environment for liver surgery planning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "234", "JournalTitle": "Computers & Graphics"}, {"Title": "A comparison of the effects of haptic and visual feedback on presence in virtual reality", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "102717", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Intelligent autonomous agents and trust in virtual reality", "Authors": "<PERSON><PERSON><PERSON> Sun; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "100146", "JournalTitle": "Computers in Human Behavior Reports"}, {"Title": "Design of digital twin applications in automated storage yard scheduling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101477", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 93418162, "Title": "Der Artificial Intelligence Act – eine Praxisanalyse am Beispiel von Gesichtserkennungssoftware", "Abstract": "<p lang=\"de\"> Zusammenfassung <p> Der Einsatz Künstlicher Intelligenz (KI) eröffnet ein Spannungsfeld zwischen Innovationspotentialen und Regulierungsdrang. Auf harmonisierte Richtlinien zur Regulierung dieser Technologie kann derzeit jedoch nicht zurückgegriffen werden. Gleichzeitig kommt es zu Diskriminierungsfällen, durch die der Bedarf an entsprechenden Gesetzen deutlich wird. Ein politischer Meilenstein in der Entwicklung eines Regelwerks kommt aus der Europäischen Union. Seit dem 21.04.2021 liegt der Artificial Intelligence Act der Europäischen Kommission vor. Der Gesetzesvorschlag versucht einen Spagat, um Innovationen weiter zu fördern und gleichzeitig ethische Dimensionen wie Nicht-Diskriminierung zu regulieren. Dieser Artikel stellt eine Statusanalyse von KI-Systemen in der Strafverfolgung in Europa im Vergleich zu den USA vor. Dieser soll herausstellen inwieweit derzeit ein Unterschied im Einsatz von KI aufgrund der geografischen Lage existiert und welche Chancen ebenso wie Herausforderungen ein globales Regelwerk für den ethischen Einsatz von KI aus europäischer Perspektive birgt. Da die Anwendung von Gesichtserkennungssoftware im Bereich der Strafverfolgung weit verbreitet ist, kann durch diese Untersuchung dringend notwendiger Handlungsbedarf der Gesetzgebung erkannt werden. </p></p>", "Keywords": "Künstliche Intelligenz; Digitale Ethik; Artificial Intelligence Act; Gesichtserkennungssoftware; Strafverfolgung; Diskriminierung; Artificial intelligence; Digital ethics; Artificial intelligence act; Face recognition software; Law enforcement; Discrimination", "DOI": "10.1365/s40702-022-00854-z", "PubYear": 2022, "Volume": "59", "Issue": "2", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "NORDAKADEMIE Hochschule der Wirtschaft, Elmshorn, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "NORDAKADEMIE Hochschule der Wirtschaft, Elmshorn, Deutschland"}], "References": []}, {"ArticleId": 93418194, "Title": "Attention-based BiLSTM models for personality recognition from user-generated content", "Abstract": "Emojis have been widely used in social media as a new way to express various emotions and personalities. However, most previous research only focused on limited features from textual information while neglecting rich emoji information in user-generated content. This study presents two novel attention-based Bi-LSTM architectures to incorporate emoji and textual information at different semantic levels , and investigate how the emoji information contributes to the performance of personality recognition tasks. Specifically, we first extract emoji information from online user-generated content, and concatenate word embedding and emoji embedding based on word and sentence perspectives. We then obtain the document representations of all users from the word and sentence levels during the training process and feed them into the attention-based Bi-LSTM architecture to predict the Big Five personality traits . Experimental results show that the proposed methods achieve state-of-the-art performance over the baseline models on the real dataset, demonstrating the usefulness and contribution of emoji information in personality recognition tasks. The findings could help researchers and practitioners better understand the rich semantics of emoji information and provide a new way to introduce emoji information into personality recognition tasks.", "Keywords": "", "DOI": "10.1016/j.ins.2022.03.038", "PubYear": 2022, "Volume": "596", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai 200093, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Nanjing University of Science and Technology, Nanjing 210094, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai 200093, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai 200093, China"}], "References": [{"Title": "Exploiting similarities of user friendship networks across social networks for user identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "78", "JournalTitle": "Information Sciences"}, {"Title": "Recent trends in deep learning based personality detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "2313", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Knowledge of words: An interpretable approach for personality recognition from social media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Yuqing <PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105550", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Purchase intention-based agent for customer behaviours", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "521", "Issue": "", "Page": "380", "JournalTitle": "Information Sciences"}, {"Title": "Domain-specific meta-embedding with latent semantic structures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "555", "Issue": "", "Page": "410", "JournalTitle": "Information Sciences"}, {"Title": "Combining contextualized word representation and sub-document level analysis through Bi-LSTM+CRF architecture for clinical de-identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106649", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "An intelligent computer-aided approach for atrial fibrillation and atrial flutter signals classification using modified bidirectional LSTM network", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "320", "JournalTitle": "Information Sciences"}, {"Title": "Evaluation of online emoji description resources for sentiment analysis purposes", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115279", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 93418227, "Title": "Authentication and Handover Challenges and Methods for Drone Swarms", "Abstract": "Drones are begin used for various purposes such as border security, surveillance, cargo delivery, visual shows and it is not possible to overcome such intensive tasks with a single drone. In order to expedite performing such tasks, drone swarms are employed. The number of drones in a swarm can be high depending on the assigned duty. The current solution to authenticate a single drone using a 5G new radio (NR) network requires the execution of two steps. The first step covers the authentication between a drone and the 5G core network, and the second step is the authentication between the drone and the drone control station. It is not feasible to authenticate each drone in a swarm with the current solution without causing a significant latency. Authentication keys between a base station (BS) and a user equipment (UE) must be shared with the new BS while performing handover. The drone swarms are heavily mobile and require several handovers from BS to a new BS. Sharing authentication keys for each drone as explained in 5G NR is not scalable for the drone swarms. Also, the drones can be used as a UE or a radio access node on board unmanned aerial vehicle (UxNB). A UxNB may provide service to a drone swarm in a rural area or emergency. The number of handovers may increase and the process of sharing authentication keys between UxNB to new UxNB may be vulnerable to eavesdropping due to the wireless connectivity. In this work, we present a method where the time and the number of the communication for the authentication of a new drone joining the swarm are less than 5G NR. In addition, group-based handover solutions for the scenarios in which the base stations are terrestrial or mobile are proposed to overcome the scalability and latency issues in the 5G NR.", "Keywords": "Drone swarm;group authentication;unmanned aerial vehicles;handover;UxNB;drone base station;user equipment", "DOI": "10.1109/JRFID.2022.3158392", "PubYear": 2022, "Volume": "6", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Informatics Institute, Istanbul Technical University, Istanbul, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Poly-Grames Research Center, Polytechnique Montr&#x00E9;al, Montr&#x00E9;al, QC, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Informatics Institute, Istanbul Technical University, Istanbul, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems and Computer Engineering, Carleton University, Ottawa, ON, Canada"}], "References": []}, {"ArticleId": 93418345, "Title": "Single-machine scheduling with resource-dependent processing times and multiple unavailability periods", "Abstract": "<p>We consider a single-machine scheduling problem with multiple unavailability periods such that the processing time of each job is inversely proportional to the k th power of its own resource consumption amount. The objective is to minimize the sum of the makespan and the total resource consumption cost. For every (k>0) , we show that the problem with one unavailability period is NP-hard while it admits a fully polynomial-time approximation scheme. Furthermore, we analyze the (in)approximability for the case with multiple unavailability periods. For every (k>0) , we show that the problem with an arbitrary number of unavailability periods is strongly NP-hard. Finally, we extend the results to the problem of minimizing the makespan with a constraint on the total resource consumption cost.</p>", "Keywords": "Scheduling; Convex resource consumption functions; Machine unavailability period; Computational complexity; Inapproximability", "DOI": "10.1007/s10951-022-00723-z", "PubYear": 2022, "Volume": "25", "Issue": "2", "JournalId": 9977, "JournalTitle": "Journal of Scheduling", "ISSN": "1094-6136", "EISSN": "1099-1425", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Chungnam National University, Daejeon, Korea"}, {"AuthorId": 2, "Name": "Myoung-Ju Park", "Affiliation": "Department of Industrial and Management Systems Engineering, Kyung Hee University, Kyunggi-do, Korea"}], "References": []}, {"ArticleId": 93418386, "Title": "Editorial", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11370-022-00417-7", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 2152, "JournalTitle": "Intelligent Service Robotics", "ISSN": "1861-2776", "EISSN": "1861-2784", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Pohang, South Korea"}], "References": []}, {"ArticleId": 93418421, "Title": "Privatsphärefreundliches maschinelles Lernen: Teil 1: Grundlagen und Verfahren", "Abstract": "<p lang=\"de\"> Zusammenfassung <p><PERSON><PERSON><PERSON><PERSON> Lernverfahren finden seit einigen Jahren in immer mehr Bereichen vielfältige Anwendung, wodurch die Relevanz der dabei verwendeten Techniken deutlich wird. Unter dem Begriff des maschinellen Lernens (ML, oft auch „künstliche Intelligenz“) existieren zahlreiche Algorithmen, die unterschiedliche Komplexität und verschiedene Eigenschaften mit sich bringen. Für das Training dieser Algorithmen sind meist große Mengen an Daten notwendig. Insbesondere bei der Verwendung von personenbezogenen Daten stellen sich hierbei Fragen rund um den Datenschutz und die Privatsphäre von Betroffenen.</p><p>Dies ist der erste Teil eines zweiteiligen Artikels zum Thema privatsphärefreundliches ML. Dieser erste Teil bietet einen leicht verständlichen Einstieg in das Thema des ML und geht dabei auf die wichtigsten Grundbegriffe ein. Außerdem werden einige der meistverwendeten ML-Verfahren, wie Entscheidungsbäume und neuronale Netze, vorgestellt. Im zweiten Teil, der in der kommenden Ausgabe des Informatik Spektrums erscheint, werden Privatsphäreangriffe und datenschutzfördernde Maßnahmen im Kontext von ML behandelt.</p></p>", "Keywords": "", "DOI": "10.1007/s00287-022-01438-3", "PubYear": 2022, "Volume": "45", "Issue": "2", "JournalId": 7707, "JournalTitle": "Informatik-Spektrum", "ISSN": "0170-6012", "EISSN": "1432-122X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universität Hamburg, Hamburg, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universität Hamburg, Hamburg, Deutschland"}, {"AuthorId": 3, "Name": "Christian<PERSON><PERSON>", "Affiliation": "Universitätsklinikum Hamburg-Eppendorf, Hamburg, Deutschland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universität Hamburg, Hamburg, Deutschland"}, {"AuthorId": 5, "Name": "Thea Kreutzburg", "Affiliation": "Universitätsklinikum Hamburg-Eppendorf, Hamburg, Deutschland"}], "References": [{"Title": "A Survey on Bias and Fairness in Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Lernende Entscheidungsbäume: Überholtes Verfahren oder vielseitige KI-Methode?", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "5", "Page": "364", "JournalTitle": "Informatik-Spektrum"}]}, {"ArticleId": 93418429, "Title": "An Integrated Approach to Assessing the Digital Maturity of an Industry", "Abstract": "A methodology for calculating an indicator for assessing the aggregate level of digital maturity of an industry is proposed and its approbation is carried out on the example of the industrial complex of Russia. The proposed methodology includes such stages as descriptive statistics of local indicators of industry digitalization, factor and component analysis, calculation of an indicator for assessing the aggregate level of digital maturity of an industry based on weight coefficients and analysis of its dynamics. The method for calculating the indicator for assessing the aggregate level of digital maturity of an industry proposed in the article can be considered as a toolkit for improving the assessment of the digital transformation of an industrial complex.", "Keywords": "", "DOI": "10.17587/it.28.156-162", "PubYear": 2022, "Volume": "28", "Issue": "3", "JournalId": 55442, "JournalTitle": "INFORMACIONNYE TEHNOLOGII", "ISSN": "1684-6400", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": "Kazan State Power Engineering University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Kazan State Power Engineering University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Kazan State Power Engineering University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Kazan State Power Engineering University"}], "References": []}, {"ArticleId": 93418465, "Title": "A novel color image encryption scheme based on a new dynamic compound chaotic map and S-box", "Abstract": "<p>In modern technological era image encryption has become an attractive and interesting field for researchers. They work for improving the security of image data from unauthorized sources. Chaos theory, due to its randomness and unpredictable behaviors, is considered favorite for the purpose of image encryption. This paper proposes a diffusion based image encryption algorithm by using chaotic maps. Firstly a chaotic map (piecewise linear chaotic map) is used for the generation of S-box, then it is used for the pixel values modification to generate element of non-linearity. After this these modified values are further diffused with another random sequence, generated by tent logistic chaotic map. Finally the color components of pre-encrypted image are mixed with each other so that the developed randomness uniformly distributed in them. For image data we develop non-linearity and diffusion by using S-box and then more randomness is added in the pre-encrypted image with the help of Boolean operation XOR. The use of this combination of chaotic maps along with S-box and Boolean operation XOR is a different technique, that provides satisfactory results for security aspects and also works efficiently.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022.</p>", "Keywords": "Chaos;Chaotic system;Image encryption;Piecewise linear chaotic map;S-box;Tent logistic map", "DOI": "10.1007/s11042-022-12268-6", "PubYear": 2022, "Volume": "81", "Issue": "15", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Capital University of Science and Technology, Islamabad, Pakistan."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Capital University of Science and Technology, Islamabad, Pakistan."}], "References": [{"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A new chaos based color image encryption algorithm using permutation substitution and Boolean operation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19853", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficient approach for encrypting double color images into a visually meaningful cipher image using 2D compressive sensing", "Authors": "<PERSON><PERSON>; <PERSON>yang <PERSON>; Zhihua Gan", "PubYear": 2021, "Volume": "556", "Issue": "", "Page": "305", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 93418471, "Title": "Stochastic Demands Oriented General Resource Scheduling With Burstable Resources", "Abstract": "<p>To help cloud customers handle burst demands effectively, the leading cloud platforms provide a new kind of virtual machine instance called burstable instance. The under utilization of burstable instance can help accumulate quota, which can be used to handle burst demands. To effectively schedule burstable resources, the stochasticity of demands should be considered in the scheduling problem and handled accordingly. To handle the stochastic scheduling considering this kind of resources, we formulate a stochastic demands oriented general resource scheduling problem by incorporating the stochastic revenue model for burstable and classic resources together. We then propose an effective algorithm to solve the problem, where a quickly obtained lower bound can reduce the solution space of differential evolution based algorithm, and then further avoid invalid solutions in searching process by solution injection. The algorithm is validated using real-world and simulated data, and can increase the average revenue of existing algorithms by up to 47%.</p>", "Keywords": "Stochastic demand; Burstable instance; Resource Scheduling; Incremental scheduling", "DOI": "10.1007/s10723-021-09586-8", "PubYear": 2022, "Volume": "20", "Issue": "1", "JournalId": 6769, "JournalTitle": "Journal of Grid Computing", "ISSN": "1570-7873", "EISSN": "1572-9184", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Grain Information Processing and Control, (Henan University of Technology), Ministry of Education, Zhengzhou, China;College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Grain Information Processing and Control, (Henan University of Technology), Ministry of Education, Zhengzhou, China;College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Grain Information Processing and Control, (Henan University of Technology), Ministry of Education, Zhengzhou, China;College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Grain Information Processing and Control, (Henan University of Technology), Ministry of Education, Zhengzhou, China;College of Information Science and Engineering, Henan University of Technology, Zhengzhou, China"}], "References": [{"Title": "Green Cloud Computing Using Proactive Virtual Machine Placement: Challenges and Issues", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "4", "Page": "727", "JournalTitle": "Journal of Grid Computing"}, {"Title": "Energy and Makespan Aware Scheduling of Deadline Sensitive Tasks in the Cloud Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Grid Computing"}]}, {"ArticleId": 93418521, "Title": "Development of the GLASS 250-m leaf area index product (version 6) from MODIS data using the bidirectional LSTM deep learning model", "Abstract": "Leaf area index (LAI) is a terrestrial essential climate variable that is required in a variety of ecosystem and climate models. The Global LAnd Surface Satellite (GLASS) LAI product has been widely used, but its current version (V5) from Moderate Resolution Imaging Spectroradiometer (MODIS) data has several limitations, such as frequent temporal fluctuation, large data gaps, high dependence on the quality of surface reflectance, and low computational efficiency. To address these issues, this paper presents a deep learning model to generate a new version of the LAI product (V6) at 250-m resolution from MODIS data from 2000 onward. Unlike most existing algorithms that estimate one LAI value at one time for each pixel, this model estimates LAI for 2 years simultaneously. Three widely used LAI products (MODIS C6, GLASS V5, and PROBA-V V1) are used to generate global representative time-series LAI training samples using K-means clustering analysis and least difference criteria. We explore four machine learning models, the general regression neural network (GRNN), long short-term memory (LSTM), gated recurrent unit (GRU), and Bidirectional LSTM (Bi-LSTM), and identify Bi-LSTM as the best model for product generation. This new product is directly validated using 79 high-resolution LAI reference maps from three in situ observation networks. The results show that GLASS V6 LAI achieves higher accuracy, with a root mean square (RMSE) of 0.92 at 250 m and 0.86 at 500 m, while the RMSE is 0.98 for PROBA-V at 300 m, 1.08 for GLASS V5, and 0.95 for MODIS C6 both at 500 m. Spatial and temporal consistency analyses also demonstrate that the GLASS V6 LAI product is more spatiotemporally continuous and has higher quality in terms of presenting more realistic temporal LAI dynamics when the surface reflectance is absent for a long period owing to persistent cloud/aerosol contaminations. The results indicate that the new Bi-LSTM deep learning model runs significantly faster than the GLASS V5 algorithm, avoids the reconstruction of surface reflectance data, and is resistant to the noises (cloud and snow contamination) or missing values contained in surface reflectance than other methods, as the Bi-LSTM can effectively extract information across the entire time series of surface reflectance rather than a single time point. To our knowledge, this is the first global time-series LAI product at the 250-m spatial resolution that is freely available to the public ( www.geodata.cn and www.glass.umd.edu ).", "Keywords": "Leaf area index ; GLASS ; MODIS ; PROBA-V ; Deep learning ; LSTM", "DOI": "10.1016/j.rse.2022.112985", "PubYear": 2022, "Volume": "273", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "Han <PERSON>", "Affiliation": "School of Remote Sensing and Information Engineering, Wuhan University, Hubei 430010, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geographical Sciences, University of Maryland, College Park, MD 20742, USA;Corresponding author"}], "References": [{"Title": "Deep learning in environmental remote sensing: Achievements and challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111716", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Deep learning-based air temperature mapping by fusing remote sensing, station, simulation and socioeconomic data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111692", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Evaluation of global leaf area index and fraction of absorbed photosynthetically active radiation products over North America using Copernicus Ground Based Observations for Validation data", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "247", "Issue": "", "Page": "111935", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A data-driven approach to estimate leaf area index for Landsat images over the contiguous US", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "258", "Issue": "", "Page": "112383", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 93418648, "Title": "Algorithm/architecture solutions to improve beyond uniform quantization in embedded DNN accelerators", "Abstract": "The choice of data type has a major impact on speed, accuracy, and power consumption of deep learning accelerators. Quantizing the weights and activations of neural networks to integer based computation is an industry standard for reducing memory footprint and computation cost of inference in embedded systems. Uniform weight quantization can be used for tasks where accuracy drop can be tolerated. However, the drop in accuracy due to a uniform quantization might be non-negligible especially when performed on shallow networks, complex computer vision tasks, or with lower-bit integers. In this paper, we introduce a software and a hardware solution to improve on a baseline integer based uniform quantization so that it can be run on lower power systems and with even less bits. We also introduce a novel encoding technique on top of our software solution specific for partial sums to significantly reduce memory footprint, latency and energy consumption due to movement of partial sums. The proposed SW solution exploits non-uniform piece-wise linear quantization to improve accuracy by capturing bell shaped distribution of weights while still using INT-based computation units. The proposed partial sum encoding can be applied to the partial sums regardless of uniform or non-uniform quantization. The proposed HW solution can either combine integers to make larger integers or turn them into Floating-Point operations so that various levels can have various precisions or data types, if necessary. To do so, we studied upper limits of precision we need in our compute units to support floating point inner product operations. It turns out that we can improve upon integer IPUs to perform accurate floating-point operations without introducing large shift units or wide adder trees. Our proposed SW solution (PWLQ) achieves the state-of-the-art results on all cases and it outperforms all other methods with a large margin. The proposed partial sum encoding technique effectively compresses the partial sum of networks like Resnet-50 down to 12bits (from Int64/32) without loss in accuracy. The proposed HW architecture achieves area improvements of up to 46% in TOPS/mm2 with power efficiency improvements of up to 63% in TOPS/W when compared to state of the art mixed precision implementation.", "Keywords": "Quantization ; Deep learning ; Mixed precision ; Accelerator", "DOI": "10.1016/j.sysarc.2022.102454", "PubYear": 2022, "Volume": "126", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Samsung Semiconductor, Inc., San Jose, CA, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Samsung Semiconductor, Inc., San Jose, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Samsung Semiconductor, Inc., San Jose, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Samsung Semiconductor, Inc., San Jose, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Samsung Semiconductor, Inc., San Jose, CA, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Samsung Semiconductor, Inc., San Jose, CA, USA"}], "References": []}, {"ArticleId": 93418775, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0098-1354(22)00093-X", "PubYear": 2022, "Volume": "160", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Rule extraction from decision tree: Transparent expert system of rules", "Abstract": "<p>A system which is transparent and has less decision rules is an efficient, user-convincing system and moreover convenient and manageable to fields like banking, business, and medical. Decision Tree (DT) is a data mining technique which is transparent and produces a set of production rules for decision-making. However sometimes it creates some unnecessary and redundant rules which diminish its comprehensibility. Thus a system named Transparent Expert System of Rules (TESR) is proposed in this paper to efficiently improve comprehensibility of the DT by reducing the number of rules drastically without compromising accuracy. The proposed system adopts a Sequential Hill Climbing method with a flexible heuristic function to prune the insignificant rules from decision rules generated by DT. Finally, the proposed TESR system produces a transparent and comprehensible rule set for a decision. The proposed TESR performance is evaluated using 10 datasets and is compared with simple DT (ID3, C4.5, and Classification and Regression Trees) and also two of the existing transparent systems with respect to comprehensibility, accuracy, precision, recall, and F-measures.</p>", "Keywords": "decision tree;hill climbing;machine learning;rule-based system;transparent system", "DOI": "10.1002/cpe.6935", "PubYear": 2022, "Volume": "34", "Issue": "15", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department NIT  Silchar Assam India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department NIT  Silchar Assam India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department NIT  Silchar Assam India"}], "References": []}, {"ArticleId": 93419145, "Title": "Domain object hierarchies inducing multi-level models", "Abstract": "Conceptual modeling of domain object hierarchies, such as product hierarchies or organization hierarchies, is difficult due to the intricate nature of nonphysical domain objects organized in such hierarchies. Modeling domain object hierarchies as part-whole hierarchies covers their hierarchical structure, yet to capture their meaning, part-whole hierarchies have to be combined with specialization and multi-level instantiation. To this end we introduce the deep domain object (DDO) multi-level modeling pattern and approach. With the DDO approach, subclasses and metaclasses are induced by and integrated with the part-whole hierarchy. The approach is aligned with the multi-level theory (MLT) and formalized by a metamodel and a set of deductive rules implemented in F-Logic. The proof-of-concept prototype is used for automated application of the pattern and for querying induced multi-level models.", "Keywords": "Conceptual modeling; Multi-level modeling; Metamodeling; Part-whole; Generalization; Abstraction; Concretization", "DOI": "10.1007/s10270-022-00973-7", "PubYear": 2022, "Volume": "21", "Issue": "2", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> University Linz, Linz, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON> University Linz, Linz, Austria"}], "References": []}, {"ArticleId": 93419199, "Title": "Scene text detection and recognition: a survey", "Abstract": "<p>Scene text detection and recognition have been given a lot of attention in recent years and have been used in many vision-based applications. In this field, there are various types of challenges, including images with wavy text, images with text rotation and orientation, changing the scale and variety of text fonts, noisy images, wild background images, which make the detection and recognition of text from the image more complex and difficult. In this article, we first presented a comprehensive review of recent advances in text detection and recognition and described the advantages and disadvantages. The common datasets were introduced. Then, the recent methods compared together and analyzed the text detection and recognition systems. According to the recent decade studies, one of the most important challenges is curved and vertical text detection in this field. We have expressed approaches for the development of the detection and recognition system. Also, we have described the methods that are robust in the detection and recognition of curved and vertical texts. Finally, we have presented some approaches to develop text detection and recognition systems as the future work.</p>", "Keywords": "Scene text localization; Text image detection; End-to-end recognition; Multi oriented; Convolutional neural network; Text recognition", "DOI": "10.1007/s11042-022-12693-7", "PubYear": 2022, "Volume": "81", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Semnan Branch, Islamic Azad University, Semnan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Semnan Branch, Islamic Azad University, Semnan, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Garmsar Branch, Islamic Azad University, Garmsar, Iran"}], "References": [{"Title": "Realtime multi-scale scene text detection with scale-based region proposal network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107026", "JournalTitle": "Pattern Recognition"}, {"Title": "Scene text detection using enhanced Extremal region and convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "37-38", "Page": "27137", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel pipeline framework for multi oriented scene text image detection and recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "170", "Issue": "", "Page": "114549", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 93419223, "Title": "COMAL: compositional multi-scale feature enhanced learning for crowd counting", "Abstract": "<p>Accurately modeling the crowd's head scale variations is an effective way to improve the counting accuracy of the crowd counting methods. Most counting networks apply a multi-branch network structure to obtain different scales of head features. Although they have achieved promising results, they do not perform very well on the extreme scale variation scene due to the limited scale representability. Meanwhile, these methods are prone to recognize background objects as foreground crowds in complex scenes due to the limited context and high-level semantic information. We propose a compositional multi-scale feature enhanced learning approach (COMAL) for crowd counting to handle the above limitations. COMAL enhances the multi-scale feature representations from three aspects: (1) The semantic enhanced module (SEM) is developed for embedding the high-level semantic information to the multi-scale features; (2) The diversity enhanced module (DEM) is proposed to enrich the variety of crowd features' different scales; (3) The context enhanced module (CEM) is designed for strengthening the multi-scale features with more context information. Based on the proposed COMAL, we develop a crowd counting network under the encoder-decoder framework and perform extensive experiments on ShanghaiTech, UCF_CC_50, and UCF-QNRF datasets. Qualitative and quantitive results demonstrate the effectiveness of the proposed COMAL.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022.</p>", "Keywords": "Convolutional neural network;Crowd counting;Crowd density estimation;Multi-scale feature learning", "DOI": "10.1007/s11042-022-12249-9", "PubYear": 2022, "Volume": "81", "Issue": "15", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Shanghai Institute of Technology, Shanghai, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Shanghai Institute of Technology, Shanghai, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Engineering, Shanghai Institute of Technology, Shanghai, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Information Engineering, Shanghai Institute of Technology, Shanghai, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Shanghai Institute of Technology, Shanghai, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Shanghai Institute of Technology, Shanghai, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Science and Technology on Electromechanical Dynamic Control Laboratory, School of Mechatronical Engineering, Beijing Institute of Technology, Beijing, China."}], "References": [{"Title": "Robust crowd counting based on refined density map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "Page": "2837", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Gate and common pathway detection in crowd scenes and anomaly detection using motion units and LSTM predictive models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "20689", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "SCLNet: Spatial context learning network for congested crowd counting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "404", "Issue": "", "Page": "227", "JournalTitle": "Neurocomputing"}, {"Title": "KUMBH MELA: a case study for dense crowd counting and modeling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17837", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An effective feature engineering for DNN using hybrid PCA-GWO for intrusion detection in IoMT architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>.", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "139", "JournalTitle": "Computer Communications"}, {"Title": "Two stages double attention convolutional neural network for crowd counting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "39-40", "Page": "29145", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Pixel-Wise Crowd Understanding via Synthetic Data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "225", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 93419250, "Title": "Adaptive reversible data hiding algorithm for interpolated images using sorting and coding", "Abstract": "Existing reversible data hiding (RDH) algorithms based on the interpolation technology (IT) have high embedding capacities and visual qualities. However, they usually embed secret data directly into interpolated pixels sequentially, resulting in that the visual qualities may not be optimal. To address this problem, an adaptive IT-based RDH algorithm is proposed. First, for a given interpolated image, the amount of data that can be embedded into each interpolated pixel is sorted in the ascending order. Next, the secret data to be embedded are recoded and sequentially embedded into interpolated pixels after overflow/underflow preprocessing based on the obtained index. The experimental results demonstrate that the proposed algorithm outperforms several state-of-the-art RDH algorithms based on IT. On average, the average peak signal-to-noise ratio (PSNR) of our algorithm is improved by approximately 25%. In comparison with traditional RDH algorithms using histogram shifting and pixel value ordering, our algorithm also achieves a higher PSNR. In addition, our algorithm can resist histogram and regular singular steganalysis attacks. These results clearly demonstrate that the proposed algorithm is effective.", "Keywords": "Data hiding ; Reversible data hiding ; Image interpolation ; Data recoding ; Adaptive embedding ; Steganalysis", "DOI": "10.1016/j.jisa.2022.103137", "PubYear": 2022, "Volume": "66", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Big Data and Computer Science, Guizhou Normal University, Guiyang 550025, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Big Data and Computer Science, Guizhou Normal University, Guiyang 550025, China"}, {"AuthorId": 3, "Name": "Mengting Fan", "Affiliation": "School of Big Data and Computer Science, Guizhou Normal University, Guiyang 550025, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Big Data and Computer Science, Guizhou Normal University, Guiyang 550025, China"}], "References": [{"Title": "A Reversible Data Hiding Scheme for Interpolated Images Based on Pixel Intensity Range", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "18005", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Reversible hiding method for interpolation images featuring a multilayer center folding strategy", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "161", "JournalTitle": "Soft Computing"}, {"Title": "Efficient reversible data hiding multimedia technique based on smart image interpolation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "39-40", "Page": "30087", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An enhanced separable reversible and secure patient data hiding algorithm for telemedicine applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "115721", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 93419379, "Title": "Spatial prediction of air pollution levels using a hierarchical Bayesian spatiotemporal model in Catalonia, Spain", "Abstract": "Our objective in this work was to present a hierarchical Bayesian spatiotemporal model that allowed us to make spatial predictions of air pollution levels effectively and with very few computational costs. We specified a hierarchical spatiotemporal model using the Stochastic Partial Differential Equations of the integrated nested Laplace approximations approximation. This approach allowed us to spatially predict in the territory of Catalonia (Spain) the levels of the four pollutants for which there is the most evidence of an adverse health effect. Our model allowed us to make fairly accurate spatial predictions of both long-term and short-term exposure to air pollutants with a relatively low density of monitoring stations and at a much lower computation time. The only requirements of our method are the minimum number of stations distributed throughout the territory where the predictions are to be made, and that the spatial and temporal dimensions are either independent or separable.", "Keywords": "Spatial predictions ; Hierarchical Bayesian spatiotemporal model ; Stochastic partial differential equations (SPDE) ; Integrated nested laplace approximations (INLA)", "DOI": "10.1016/j.envsoft.2022.105369", "PubYear": 2022, "Volume": "151", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Group on Statistics, Econometrics and Health (GRECS), University of Girona, Girona, Spain;CIBER of Epidemiology and Public Health (CIBERESP), Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research Group on Statistics, Econometrics and Health (GRECS), University of Girona, Girona, Spain;CIBER of Epidemiology and Public Health (CIBERESP), Madrid, Spain;Corresponding author. Research Group on Statistics, Econometrics and Health (GRECS) and CIBER of Epidemiology and Public Health (CIBERESP) University of Girona Carrer de la Universitat de Girona 10, Campus de Montilivi, 17003, Girona, Spain"}], "References": []}, {"ArticleId": ********, "Title": "Financial Risk Control Model Based on Deep Neural Networks", "Abstract": "<p>Under the background of global economic integration, commercial banks are facing more and more complex business environment. As one of the major financial risks faced by commercial banks, liquidity risk determines and reflects the safety and profitability of bank operation. Based on joint-stock commercial banks as the research object, this paper, respectively, from the angle of the static and dynamic measurements and projections for liquidity risk and based on the current situation of four joint-stock commercial banks liquidity level study, tries to explore the change law of commercial banks liquidity risk and financial risk control of commercial bank and puts forward reasonable suggestions. In this paper, an AHP neural network model combining subjective and objective methods is proposed. This method can not only overcome the defects of the single evaluation method but also improve the data accessibility by using the qualitative data and quantitative data of AHP. In the aspect of financial risk control system, this paper tries to establish a more comprehensive and practical financial risk control model by combining the previous research of scholars, the business model process, and the experience of practical workers.</p>", "Keywords": "", "DOI": "10.1155/2022/7253832", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Northeast Normal University, Changchun 130117, China;Business School, Changchun Humanities and Sciences College, Changchun 130117, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Northeast Normal University, Changchun 130117, China"}], "References": [{"Title": "TARDB-Net: triple-attention guided residual dense and BiLSTM networks for hyperspectral image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "11291", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": ********, "Title": "A novel geo-opportunistic routing algorithm for adaptive transmission in underwater internet of things", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJWGS.2022.********", "PubYear": 2022, "Volume": "18", "Issue": "3", "JournalId": 17784, "JournalTitle": "International Journal of Web and Grid Services", "ISSN": "1741-1106", "EISSN": "1741-1114", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Overview of Service-Oriented Architecture Implementations for Calculating the Impact of External Influences on all Output Functions of an Electronic Circuit", "Abstract": "The principles of building information systems based on service-oriented architecture are considered. The advantages of using this architectural approach, as well as a model of the component composition of the system, are described. A description of various technologies for building software using a service-oriented approach based on web services is given. The main limitations of using the CORBA technology standard for writing distributed applications are determined. The technology of building web services based on the SOAP data exchange protocol is described in the context of using the ESB service bus for the interaction of heterogeneous services in a single information system. The architectural style of microservice organization of distributed software is considered as a way to increase the granularity of the system in order to ensure the best scalability and fault tolerance of the system. Methods for constructing the mathematical support of web services for distributed schematic CAD systems for calculating the influence of external influences (changes in temperature, radiation, etc.) on all output functions of the simulated circuit are presented. Parameters of two-terminal type R, L, and C and transmission parameters of dependent current or voltage sources controlled by potential or current variables are taken as variable values of the components.", "Keywords": "", "DOI": "10.17587/it.28.125-132", "PubYear": 2022, "Volume": "28", "Issue": "3", "JournalId": 55442, "JournalTitle": "INFORMACIONNYE TEHNOLOGII", "ISSN": "1684-6400", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Design Information Technologies Center Russian Academy of Sciences, Odintsovo, Moscow region, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Design Information Technologies Center Russian Academy of Sciences, Odintsovo, Moscow region, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Saint Petersburg Electrotechnical University, Saint Petersburg, Russian Federation"}], "References": []}, {"ArticleId": 93419937, "Title": "An Enhanced Application for Securing File Transfer Over Android Devices", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2022.11212", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Musa Sule Argungu", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93419946, "Title": "Adjustable whole-body dynamics for adaptive locomotion: the influence of upper body movements and its interactions with the lower body parts on the stable locomotion of a simple bipedal robot", "Abstract": "<p>This paper investigates the influence of adding an upper body to a bipedal robot on its stable walking behavior. The robot’s parts are mutually interconnected through an actuator network system. Therefore, the movement pattern of the upper body depends on the type of interactions created with other limbs. Throughout the experiments, various interactions among the different body parts were tested. The results showed that a robot with a motionless upper body exhibited unstable walking behavior. However, once the same upper body was involved and interacted properly, with other body parts, its movement significantly helped to stabilize the behavior of the robot.</p>", "Keywords": "actuator network system; bipedal robot; upper body; morphological computation; whole-body dynamics; adaptive locomotion", "DOI": "10.1017/S0263574722000212", "PubYear": 2022, "Volume": "40", "Issue": "9", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University, Toyonaka, Osaka, Japan RIKEN Information R&D and Strategy Headquarters, RIKEN, Kyoto, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University, Toyonaka, Osaka, Japan JST ERATO Graduate School of Informatics and Engineering, The University of Electro-Communications, Chofu, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University, Toyonaka, Osaka, Japan RIKEN Information R&D and Strategy Headquarters, RIKEN, Kyoto, Japan JST ERATO"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University, Toyonaka, Osaka, Japan JST ERATO"}], "References": []}, {"ArticleId": 93420047, "Title": "Review of Multi-Criteria Decision-Making Methods in Finance Using Explainable Artificial Intelligence", "Abstract": "<p>The influence of Artificial Intelligence is growing, as is the need to make it as explainable as possible. Explainability is one of the main obstacles that AI faces today on the way to more practical implementation. In practise, companies need to use models that balance interpretability and accuracy to make more effective decisions, especially in the field of finance. The main advantages of the multi-criteria decision-making principle (MCDM) in financial decision-making are the ability to structure complex evaluation tasks that allow for well-founded financial decisions, the application of quantitative and qualitative criteria in the analysis process, the possibility of transparency of evaluation and the introduction of improved, universal and practical academic methods to the financial decision-making process. This article presents a review and classification of multi-criteria decision-making methods that help to achieve the goal of forthcoming research: to create artificial intelligence-based methods that are explainable, transparent, and interpretable for most investment decision-makers.</p>", "Keywords": "Multiple criteria decision aid (MCDA); artificial intelligence; explainable artificial intelligence (XAI); Interpretability; Financial decision-making; Investment decision-making", "DOI": "10.3389/frai.2022.827584", "PubYear": 2022, "Volume": "5", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematical Modelling, Faculty of Mathematics and Natural Sciences, Kaunas University of Technology, Lithuania"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematical Modelling, Faculty of Mathematics and Natural Sciences, Kaunas University of Technology, Lithuania"}], "References": [{"Title": "Bibliometric analysis on tendency and topics of artificial intelligence over last decade", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "1545", "JournalTitle": "Microsystem Technologies"}, {"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "Explainable AI in Fintech Risk Management", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "", "Page": "26", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Explainable Machine Learning in Credit Risk Management", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "1", "Page": "203", "JournalTitle": "Computational Economics"}, {"Title": "<PERSON><PERSON><PERSON>y-<PERSON><PERSON>z eXplainable Artificial Intelligence", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114104", "JournalTitle": "Expert Systems with Applications"}, {"Title": "SHAP and LIME: An Evaluation of Discriminative Power in Credit Risk", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "140", "JournalTitle": "Frontiers in Artificial Intelligence"}]}, {"ArticleId": 93420087, "Title": "Performance analysis of disease diagnostic system using IoMT and real‐time data analytics", "Abstract": "<p>In this article, the Internet of Medical Things (IoMT) framework based on Apache Spark big data processing technology is proposed for real-time analysis of health data obtained from wireless body area networks (WBANs), which is one of the most important components of IoMT. The proposed framework consists of four layers: data source, data collection, data analytics and visualization. In addition, the proposed IoMT framework is presented with two different disease prediction scenarios, diabetes and heart disease. Diabetes and heart disease prediction processes are carried out using the random forest (RF), logistic regression (LR) and support vector machine (SVM) algorithms belonging to the Apache Spark machine learning library (MLlib). The analysis of health data generated in WBANs takes place in real-time in the Apache Spark-based data analytics layer. In this study, the performances of MLlib algorithms in the real-time model developed for heart and diabetes disease are examined. The SVM algorithm with an accuracy rate of 93.33% for heart disease and the LR algorithm with an accuracy rate of 78.89% for diabetes are found to provide the best performances.</p>", "Keywords": "Apache Spark;IoMT;WBANs;data analytics;machine learning", "DOI": "10.1002/cpe.6916", "PubYear": 2022, "Volume": "34", "Issue": "13", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Technologies Department, Osmaniye Korkut Ata University, Osmaniye, Turkey"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, Düzce University, Düzce, Turkey"}, {"AuthorId": 3, "Name": "Murtaza Cicioğlu", "Affiliation": "Computer Engineering Department, Bursa Uludağ University, Bursa, Turkey"}], "References": [{"Title": "An overview of Internet of Things (IoT): Architectural aspects, challenges, and protocols", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "21", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Heart disease identification from patients’ social posts, machine learning solution on Spark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "714", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 93420151, "Title": "Locomotion Control of Snake-like Robot utilizing Friction Forces: Stability Verification of Model Following Servo Controller", "Abstract": "The purpose of this paper is to verify the effectiveness of Model Following Servo Control (MFSC) as a stabilizing control measure for systems with uncontrollable disturbances. Both the gravity compensation control system of the 1-link manipulator and the head-position control system of the two-wheeled robot is designed. In gravitational compensation control, gravitational acceleration is defined as an uncontrollable state. In the head-position control, the frictional force in the axial direction of the skidding wheel is defined as an uncontrollable state. The effectiveness of the MFSC as a stabilizing control system for systems containing uncontrollable states is verified via numerical simulations.", "Keywords": "Snake-like Robot;Model Following Servo Controller;Gravity Compensation;Head Position Control", "DOI": "10.15748/jasse.9.113", "PubYear": 2022, "Volume": "9", "Issue": "1", "JournalId": 34297, "JournalTitle": "Journal of Advanced Simulation in Science and Engineering", "ISSN": "", "EISSN": "2188-5303", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Advanced Machinery Engineering, School of Engineering, Tokyo Denki University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Advanced Machinery Engineering, School of Engineering, Tokyo Denki University"}], "References": [{"Title": "Manipulability analysis of a snake robot without lateral constraint for head position control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "6", "Page": "2282", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": 93420186, "Title": "Refinement of Inverse Depth Plane in Textureless and Occluded Regions in a Multiview Stereo Matching Scheme", "Abstract": "<p>In the multiview stereo (MVS) vision, it is difficult to estimate accurate depth in the textureless and occluded regions. To solve this problem, several MVS investigations employ the matching cost volume (MCV) approach to refine the cost in the textureless and occluded regions. Usually, the matching costs in the large textureless image regions are not reliable. In addition, if an occluded region is also textureless, the matching cost contains a significant error. The goal of the proposed MVS method is to reconstruct accurate depth maps in both large-textureless and occluded-textureless regions by iteratively updating the erroneous disparity. The erroneous disparity in the textureless region is updated by the 3D disparity plane of the region in the inverse-depth space. Then, the surface consensus is computed and used to run the two processes, the surface consensus refinement and the matching cost update. By the iterative update of the 3D inverse depth plane, surface consensus, and matching cost, the performance of the depth reconstruction in the large-textureless and occluded-textureless regions is greatly improved. The performance of the proposed method is analyzed using the Middlebury multiview stereo dataset. The depth reconstruction performance is also compared with several stereo vision methods.</p>", "Keywords": "", "DOI": "10.1155/2022/7181445", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Electrical Engineering, Kyungpook National University, Daegu 41566, Republic of Korea"}, {"AuthorId": 2, "Name": "Soon-Yong Park", "Affiliation": "School of Electronic and Electrical Engineering, Kyungpook National University, Daegu 41566, Republic of Korea"}], "References": []}, {"ArticleId": 93420243, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0031-3203(22)00013-9", "PubYear": 2022, "Volume": "124", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [], "References": []}, {"ArticleId": 93420363, "Title": "Distance Learning Technologies during Pandemia: Implementation Approach and Example of Decision Making", "Abstract": "The happenings of 2020 were highlighting the topic of the role and place of distant learning in all educational institutions. In close future the transfer to the unified platform inside every institution is unavoidable, but it remains open if the previous experience of the e-learning professionals will be considered. The example described in this article raises doubts about it.", "Keywords": "", "DOI": "10.17587/it.28.163-167", "PubYear": 2022, "Volume": "28", "Issue": "3", "JournalId": 55442, "JournalTitle": "INFORMACIONNYE TEHNOLOGII", "ISSN": "1684-6400", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Russian University of Transport (MIIT), Moscow, Russian Federation"}], "References": []}, {"ArticleId": 93420374, "Title": "Combined Algorithm for Solving the Asymmetric Traveling Salesman Problem as Applied to Transport Logistics Problems", "Abstract": "The traveling salesman problem is to find a Hamiltonian cycle with the minimum sum of the weights of arcs in a complete oriented asymmetric graph. Despite its simple formulation, the traveling salesman problem is NP-hard. The Branch and Bound method is the basis of the most time efficient algorithm for solving the traveling salesman problem, delivering the exact solution. However, for a number of applied problems, the time to obtain a solution using this algorithm is practically unacceptable. Despite the majority of heuristic algorithms developed for the traveling salesman problem, for some applied problems in business informatics and logistics, it is important to obtain accurate solutions in the range of small dimensions. The article presents the results on the development and statistical study of a combined algorithm for solving the traveling salesman problem, which obtains exact solutions, under conditions of limitation on the average solution time for dimensions not exceeding 55 that arise when solving transport logistics problems (data presented by LLC \"Group KIT\"). The implementation of the <PERSON> and Bound method in combination with the Lin-<PERSON>an-<PERSON> metaheuristic algorithm is considered. Approaches are described that allowed, when developing this combined algorithm, to significantly reduce the time for solving individual traveling salesman problems, and to satisfy the company's requirement for time efficiency.", "Keywords": "", "DOI": "10.17587/it.28.141-147", "PubYear": 2022, "Volume": "28", "Issue": "3", "JournalId": 55442, "JournalTitle": "INFORMACIONNYE TEHNOLOGII", "ISSN": "1684-6400", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "V. A. Trapeznikov Institute of Control Sciences of the Russian Academy of Sciences,"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Nizhny Novgorod State Technical University n.a. R. E. Alekseev"}], "References": []}, {"ArticleId": 93420582, "Title": "SFNet: A slow feature extraction network for parallel linear and nonlinear dynamic process monitoring", "Abstract": "In a typical industrial process, there may exist both linear and nonlinear relationships among process variables. Besides, the existence of process dynamics poses challenges to process monitoring. Some monitoring methods have been developed for dynamic processes. However, purely linear or nonlinear methods can hardly tackle the hybrid linear and nonlinear relationships among process variables. In this work, a novel unsupervised neural network model, named slow feature network (SFNet), is proposed to monitor dynamic processes. In SFNet, a linear mapping module and a transform gate structure are constructed to realize the parallel extraction of linear and nonlinear characteristics. Moreover, slow constraints on features are designed to capture the velocity characteristics while retaining variability information so that the process dynamic behavior can be directly observed. Various statistics are developed to monitor the process status from both variability and velocity as well as both linear and nonlinear perspectives. Thus, monitoring results corresponding to different statistical information reflect different process behavior and fault characteristics with meaningful interpretations. One numerical example and two real industrial examples are adopted to validate the proposed method. For these experimental examples, the proposed SFNet method achieves an average false alarm rate of 5.17% (min 0.8%, max 8.08%), an average detection accuracy of 87.5% (min 50.2%, max 100%), and an average precision of 98.6% (min 96.3%, max 100%), outperforming counterpart methods in the literature.", "Keywords": "Process dynamics ; Slow feature network ; Transform gate ; Velocity information ; Linear and nonlinear characteristics ; Parallel analysis", "DOI": "10.1016/j.neucom.2022.03.012", "PubYear": 2022, "Volume": "488", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, Hangzhou 310027, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Industrial Control Technology, College of Control Science and Engineering, Zhejiang University, Hangzhou 310027, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Materials Engineering, University of Alberta, Edmonton, AB T6G 2G6, Canada"}], "References": []}, {"ArticleId": 93420606, "Title": "An efficient multi-level cache system for geometrically interconnected many-core chip multiprocessor", "Abstract": "<p><span lang=\"EN-US\">Many-core chip multiprocessor offers high parallel processing power for big data analytics; however, they require efficient multi-level cache and interconnection to achieve high system throughput. Using on-chip first level L1 and second level L2 per core fast private caches is expensive for large number of cores. In this paper, for moderate number of cores from 16 to 64, we present a cost and performance efficient multi-level cache system with per core L1 and last level shared bus cache on each bus line of a cost-efficient geometrically bus-based interconnection. In our approach, we extracted cache hit and miss concurrencies and applied concurrent average memory access time to more accurately determine the cache system performance. We conducted least recently used cache policy-based simulation for cache system with L1, with L1/L2, and with L1/shared bus cache. Our simulation results show that an average system throughput improvement of 2.5x can be achieved by using system with L1/shared bus cache system compared to using only first level L1 or L1/L2. Further, we show that the throughput degradation for the proposed cache system is only within 5% for a single bus fault, suggesting a good bus fault tolerance.</span></p>", "Keywords": "Big data;Bus cache;Geometrical;Heterogeneous;Many-core;Throughput", "DOI": "10.11591/ijres.v11.i1.pp93-102", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 46991, "JournalTitle": "International Journal of Reconfigurable and Embedded Systems (IJRES)", "ISSN": "2089-4864", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jackson State University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jackson State University"}], "References": []}]