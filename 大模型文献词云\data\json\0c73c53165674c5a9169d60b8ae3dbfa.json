[{"ArticleId": 118338082, "Title": "Advanced swarm intelligence algorithms in quantum circuit design", "Abstract": "", "Keywords": "", "DOI": "10.1080/17445760.2024.2388245", "PubYear": 2025, "Volume": "40", "Issue": "1", "JournalId": 14122, "JournalTitle": "International Journal of Parallel, Emergent and Distributed Systems", "ISSN": "1744-5760", "EISSN": "1744-5779", "Authors": [{"AuthorId": 1, "Name": "Libuš<PERSON>", "Affiliation": "IT4Innovations, VSB-Technical University of Ostrava, Ostrava-Poruba, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IT4Innovations, Quantum Computing Lab, VSB-Technical University of Ostrava, Ostrava-Poruba, Czech Republic;Department of Computer Science, VSB-Technical University of Ostrava, Ostrava-Poruba, Czech Republic"}], "References": [{"Title": "iSOMA swarm intelligence algorithm in synthesis of quantum computing circuits", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110350", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 118338126, "Title": "Comprehensive Smartphone Image Dataset for Bean and Cowpea Plant Leaf Disease Detection and Freshness Assessment from Bangladesh Vegetable Fields", "Abstract": "Agriculture greatly impacts Bangladesh's economy, and vegetable cultivation plays a significant role in Agriculture by providing nourishment, and food security as well as improving the economy. The necessity of food production is growing similarly to the population growth. The farmers of Bangladesh are working hard to meet this need for food production and to gain yields. However, every year the farmers face a significant amount of loss in production due to the attack of different diseases and viruses due to the lack to technological development. The reason behind most of these losses is the lack of knowledge about diseases and being unable to detect the diseases early. Therefore, the early detection of plant disease is significant in balancing the country's economy and preventing undesirable losses. To bring a solution to this problem our dataset provides a total of 4467 images of Beans and Cowpeas leaf images which include different disease classes and fresh leaves. The dataset comprises 2,273 images of Bean and 2,194 images of Cowpea plants where each plant provides 4 classes of different disease along with the healthy leaves. This dataset will assist researchers in identifying plant diseases and farmers as well as contribute to the economy of the country.", "Keywords": "Disease identification; Dataset collection; Image analysis; Leaf diseases; Agricultural challenges; Bean leaf disease; Cowpea leaf disease", "DOI": "10.1016/j.dib.2024.111023", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Aftabnagar, Dhaka, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Aftabnagar, Dhaka, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Aftabnagar, Dhaka, Bangladesh;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Aftabnagar, Dhaka, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Aftabnagar, Dhaka, Bangladesh"}, {"AuthorId": 6, "Name": "Sheikh <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Aftabnagar, Dhaka, Bangladesh"}], "References": [{"Title": "Smartphone Image Dataset to Distinguish Healthy and Unhealthy Leaves in Papaya Orchards in Bangladesh", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "55", "Issue": "", "Page": "110599", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 118338146, "Title": "A Novel Evidential Collaborative Filtering Framework Based on Discounting Conflicting Preferences", "Abstract": "<p>This paper presents a novel Framework to enhance Evidential Collaborative Filtering (ECF), a critical Recommender System (RS) designed for sensitive domains like healthcare and target tracking. The focus is on refining how user rating imperfections are handled, particularly in managing conflicting preferences during neighborhood selection to boost recommendation quality. The newly proposed ECF architecture integrates a Two-probabilities-focused approach with an advanced conflict management technique, employing Deng relative entropy and the Best Worst Method. This allows for assigning more accurate reliability weights to each user, improving preference selection and rating prediction in ECF. Experimental evaluations show that our model surpasses Two-probabilities-focused ECF in prediction error, precision, recall, and F-score.</p>", "Keywords": "", "DOI": "10.5455/jjcit.71-**********", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 40478, "JournalTitle": "Jordanian Journal of Computers and Information Technology", "ISSN": "2413-9351", "EISSN": "2415-1076", "Authors": [{"AuthorId": 1, "Name": "khadidja BELMESSOUS", "Affiliation": ""}, {"AuthorId": 2, "Name": "Faouzi SEBBAK", "Affiliation": ""}, {"AuthorId": 3, "Name": "M MATAOUI", "Affiliation": ""}], "References": []}, {"ArticleId": 118338423, "Title": "Hands-on analysis of using large language models for the auto evaluation of programming assignments", "Abstract": "The increasing adoption of programming education necessitates efficient and accurate methods for evaluating students’ coding assignments. Traditional manual grading is time-consuming, often inconsistent, and prone to subjective biases. This paper explores the application of large language models (LLMs) for the automated evaluation of programming assignments. LLMs can use advanced natural language processing capabilities to assess code quality, functionality, and adherence to best practices, providing detailed feedback and grades. We demonstrate the effectiveness of LLMs through experiments comparing their performance with human evaluators across various programming tasks. Our study evaluates the performance of several LLMs for automated grading. Gemini 1.5 Pro achieves an exact match accuracy of 86% and a ± 1 accuracy of 98%. GPT-4o also demonstrates strong performance, with exact match and ± 1 accuracies of 69% and 97%, respectively. Both models correlate highly with human evaluations, indicating their potential for reliable automated grading. However, models such as Llama 3 70B and Mixtral 8 × 7B exhibit low accuracy and alignment with human grading, particularly in problem-solving tasks. These findings suggest that advanced LLMs are instrumental in scalable, automated educational assessment. Additionally, LLMs enhance the learning experience by offering personalized, instant feedback, fostering an iterative learning process. The findings suggest that LLMs could play a pivotal role in the future of programming education, ensuring scalability and consistency in evaluation.", "Keywords": "", "DOI": "10.1016/j.is.2024.102473", "PubYear": 2025, "Volume": "128", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Informatics Science, School of Information Technology and Computer Science, Nile University, Giza, Egypt;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Informatics Science, School of Information Technology and Computer Science, Nile University, Giza, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Informatics Science, School of Information Technology and Computer Science, Nile University, Giza, Egypt;Faculty of Computers and Artificial Intelligence, Benha University, Benha, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Informatics Science, School of Information Technology and Computer Science, Nile University, Giza, Egypt;Faculty of Computers and Artificial Intelligence, Helwan University, Cairo, Egypt"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Informatics Science, School of Information Technology and Computer Science, Nile University, Giza, Egypt;Faculty of Computers and Artificial Intelligence, Helwan University, Cairo, Egypt"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Informatics Science, School of Information Technology and Computer Science, Nile University, Giza, Egypt"}], "References": []}, {"ArticleId": 118338452, "Title": "Advantages of Friend-Modelled Social Interactive Feedforward for VR Exergaming", "Abstract": "<p>VR exergaming is a promising motivational tool to incentivise exercise. We present a novel VR exergaming method called social interactive feedforward. The player competes with an 'enhanced model' of one of their friends in a real-time VR environment, showing improved performance levels in a way the player can relate to. Social interactive feedforward was tested in a cycling-based VR exergame and players competed with enhanced models of themselves, their friend, and a stranger moving at the same enhanced pace. Results show that friend-modelled social interactive feedforward improves performance and intrinsic motivation the most. This indicates that the mere association of the enhanced model with their friend results in a rapid improvement in performance and motivation which implies that social feedforward was successfully elicited by using an enhanced friend's model. This widens the application of self-modelled feedforward to a wide range of social options which enables players to also reap the benefits of socialising in addition to feedforward benefits.</p>", "Keywords": "", "DOI": "10.1145/3677103", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "Soumya <PERSON>", "Affiliation": "School of Computer Science and Informatics, Cardiff University, Cardiff, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Informatics, Cardiff University, Cardiff, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Bath, Bath, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Bath, Bath, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Bath, Bath, United Kingdom"}], "References": []}, {"ArticleId": 118338455, "Title": "Wordless: An integrated corpus tool with multilingual support for the study of language, literature, and translation", "Abstract": "This paper presents Wordless , an integrated corpus tool with multilingual support for the study of language, literature, and translation. It is a free, cross-platform, and open-source desktop application with a user-friendly graphical interface which is specially designed to cater the needs of non-technical users. Its ultimate goal is to remove all unnecessary technological barriers to the utilization of cutting-edge technologies by researchers in the field of corpus-based studies.", "Keywords": "Corpora; Corpus tool; Corpus analysis; Corpus-based approach; Corpus-based studies", "DOI": "10.1016/j.softx.2024.101931", "PubYear": 2024, "Volume": "28", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Corpus Studies and Applications, Shanghai International Studies University, Shanghai, China"}], "References": []}, {"ArticleId": 118338552, "Title": "Climatic Data Analysis Using Machine Learning and Correlation with Human Health", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDATS.2025.10067196", "PubYear": 2025, "Volume": "17", "Issue": "3", "JournalId": 27737, "JournalTitle": "International Journal of Data Analysis Techniques and Strategies", "ISSN": "1755-8050", "EISSN": "1755-8069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118338599, "Title": "Developing a Competitive Edge: Building an Effective Portfolio in Snowflake and Teradata Data Engineering", "Abstract": "This article examines the critical components of an effective portfolio for data engineering professionals specializing in Snowflake and Teradata platforms. As the data landscape evolves, the ability to showcase practical skills alongside theoretical knowledge has become paramount for career advancement. Through an analysis of industry trends and expert interviews, we identify key elements that contribute to a compelling portfolio, including project showcases demonstrating complex problem-solving, relevant certifications, and evidence of continuous learning. The article highlights the importance of balancing technical proficiency in SQL, data warehousing, and cloud technologies with strong documentation and communication skills. Our findings suggest that a well-curated portfolio not only validates a candidate's expertise but also provides tangible evidence of their ability to navigate the challenges of modern data ecosystems. This article provides a framework for data engineers to construct portfolios that effectively demonstrate their capabilities, potentially increasing their competitiveness in the job market and facilitating career growth in the rapidly evolving field of data engineering.", "Keywords": "Data Engineering Portfolio;Snowflake Certification;Teradata Administration;Cloud Data Warehousing;SQL Proficiency", "DOI": "10.32628/CSEIT241051045", "PubYear": 2024, "Volume": "10", "Issue": "5", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Medidata Solutions, USA"}], "References": []}, {"ArticleId": 118338727, "Title": "Analysis of innovation diffusion patterns through patent citation network: Focusing on Gartner Hype cycle for AI", "Abstract": "국가 및 기업 간에 기술 경쟁이 치열해지고 있는 현대 사회에서, 기술 초격차를 확대하기 위한 경쟁이 가속화되고 있다. 이에 정부, 산업계에서는 근거에 기반한 기술 전략 및 예측이 이루어질 수 있도록 노력하고 있다. 가트너에서 매년 발표하는 하이프사이클은 미래의 기술 시장을 예측할 수 있는 실용적인 보고서로 평가되지만 하이프사이클에 대한 실증적인 검토는 부족한 실정이다. 본 연구에서는 혁신확산이론에 근거하여 인공지능 분야 29개 기술의 하이프사이클 단계별 기술들의 특성 차이를 분석하였다. 이를 위해 기술 분야의 시장동향 및 예측자료로서의 가치가 있는 특허 데이터를 본 연구에 활용하였다. 한국, 미국, 일본, 중국, 유럽의 등록특허 4,018건을 수집하였다. 단계별 특허의 영향력의 정도와 방향성의 확인을 통한 확산패턴을 분석하기 위해 각 특허별 출원인 및 심사관의 피인용 정보를 네트워크 데이터로 변환하여 후행특허에 영향을 미친 특허의 관계를 분석하고자 하였다. 이를 바탕으로 수용자 확산비율과 비교하였다. 또한 응집규모를 파악 후 연결중심화, 근접중심화 정도를 외향(out)/내향(in)로 나누어 단계별 네트워크의 구조적인 특징을 살펴보았다. 또한 전체 네트워크 내에서 각 단계별 네트워크의 위치를 시각화하여 단계별 위치적인 특성의 차이를 확인하였다. 연구결과 연결중심화, 근접중심화 값이 Tb와 S단계에서 내부로 유입되는 스타형 네트워크 구조가 강해지는 것으로 나타났다. 특히 S단계에서 그 형태가 더 강하였는데, 이는 특허라는 데이터의 특성 상 하이프사이클 곡선의 구성 요소인 기술적/사업적 성숙도(Engineering and Business Maturity)의 영향으로 해석할 수 있다.", "Keywords": "가트너하이프사이클;혁신확산;특허네트워크;기술동향분석;Gartner Hype Cycle;Innovation Diffusion;Patent Network;Technology Foresight", "DOI": "10.13088/jiis.2024.30.3.027", "PubYear": 2024, "Volume": "30", "Issue": "3", "JournalId": 26374, "JournalTitle": "Journal of Intelligence and Information Systems", "ISSN": "2288-4866", "EISSN": "2288-4882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118338730, "Title": "A Study on Deep Learning-Based Emerging Commercial District Growth Prediction Model with Big Data from Dongbaekjeon", "Abstract": "본 연구는 부산광역시 지역화폐인 동백전 지급결제 시스템의 빅데이터를 활용하여 신흥 상권의 성장성을 예측하는 딥러닝 기반 모델을 개발하고, 이동평균법을 이용한 데이터 전처리 기법이 모델 성능에 미치는 영향을 검증하는 것을 목표로 한다. 상권은 업체와 서비스 제공자가 위치를 선택하고 마케팅 전략을 수립하는 데 중요한 요소로 작용한다. 기존 연구에서 사용된 원형 상권 분석 방법의 한계를 극복하기 위해, 국가 공공 데이터와 연계가 가능하고 회원 가입 시 등록하는 우편번호 기반의 상권 범위 설정 방법을 제안한다.BR 동백전 지급결제 시스템의 충전형 선불카드 승인 데이터를 우편번호 구역을 기준으로 집계하여 선정된 신흥 상권의, 시계열 데이터를 통해 성장성을 예측하는 데 가장 효율적인 이동평균법 기반 데이터 전처리 방법과 최적의 딥러닝 모델을 제안한다. 특히, RNN 모델과 RNN 계열 모델인 LSTM과 GRU 모델을 활용하여 전처리된 시계열 데이터를 학습하고 예측한다.BR 연구 결과, 단순이동평균법으로 전처리한 데이터를 GRU 모델을 사용하여 Random Seed 1에서 127 사이 구간에서 학습한 결과, 예측 성능 지표인 R²의 평균값이 0.89 이고, 평균 성능은 표준편차 0.05에서 0.89로 가장 안정적이고 우수한 결과를 보였다.BR 본 연구는 SMA(단순 이동평균), TMA(삼각 이동평균), WMA(가중 이동평균), EMA(지수 이동평균) 이동평균법을 기반으로 한 데이터 전처리가 딥러닝 모델의 예측 성능에 큰 영향을 미친다는 점을 시사한다. 데이터 특성에 맞는 이동평균법 기반 전처리 기법은 금융시장 및 다양한 산업 분야에서 시계열 데이터 학습 및 예측의 정확성과 효율성을 높이는 데 활용할 수 있다.", "Keywords": "동백전 지급결제 시스템;신흥 상권의 성장성 예측;딥러닝;Dongbaekjeon Payments system;growth prediction of emerging commercial areas;Moving Average;Deep Learning", "DOI": "10.13088/jiis.2024.30.3.327", "PubYear": 2024, "Volume": "30", "Issue": "3", "JournalId": 26374, "JournalTitle": "Journal of Intelligence and Information Systems", "ISSN": "2288-4866", "EISSN": "2288-4882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118338734, "Title": "Analyzing Perception Differences on Nuclear Energy Using BERTopic: A Framework for Comparing Experts and the Public", "Abstract": "최근 기후 변화와 에너지 안보 문제가 심각해지고 있어 에너지 정책이 중요한 역할을 한다. 원자력 에너지 정책은 전문가와 다양한 이해관계자들의 의견 조율을 통해 수립되지만, 전문가들의 의견이 더 큰 비중을 차지할 수 있다. 전문가 의견비중이 높은 에너지 정책은 기술적 측면이 강조되어 안전, 환경 등의 사회적 측면이 부족할 수 있다. 또한 5년 주기로 수립하고 시행되기 때문에 대중의 의견을 파악하고 반영하는 것이 중요하다. 따라서 본 연구는 에너지 관련 신문 데이터를 활용하여 전문가 의견을 수집하고, 원자력 관련 영상 댓글을 활용하여 대중의 의견을 수집한다. BERTopic으로 토픽 모델링하고 토픽별 감성분석을 통해 각 그룹의 인식을 파악한다. 전문가와 대중의 인식 비교 결과, 두 그룹 모두 사용후핵연료관리, 에너지 전환 정책, 탈원전 정책, 오염수 방류, 기후위기와 탄소중립 추진, 청정 수소에너지와 핵융합 등에 대해 유사한 인식을 보였다. 긍정적 인식은 관리 필요성과 청정에너지에 대한 관심에서 나타났으며, 부정적 인식은 경제적 손실, 환경 오염, 재생에너지 한계 등에서 공통적으로 나타났다. 또한, 전문가들은 기술적, 정책적 문제점과 가능성에 집중하는 반면, 대중은 일상적 영향과 안전에 대해 민감하게 받아들이는 차이점이 존재하였다. 이러한 공통점과 차이점을 반영하여 경제적 손실과 사회적 갈등을 최소화하고, 장기적이고 지속가능한 에너지 전환 정책을 수립하며, 미래 에너지에 대한 혁신적 기술에 대한 연구와 투자가 필요하다는 결론을 도출하였다. 이는 전문가와 대중의 인식을 비교 분석할 수 있는 프레임워크를 제시함으로써 균형잡힌 정책 수립이 가능하게 할 것이며 에너지 정책의 신뢰성과 수용성을 높이는데 중요한 역할을 할 것으로 기대된다.", "Keywords": "원자력 에너지;인식;텍스트 마이닝;감성분석;Nuclear Energy;Perception;Text Mining;BERTopic;Sentiment Analysis", "DOI": "10.13088/jiis.2024.30.3.185", "PubYear": 2024, "Volume": "30", "Issue": "3", "JournalId": 26374, "JournalTitle": "Journal of Intelligence and Information Systems", "ISSN": "2288-4866", "EISSN": "2288-4882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Hong", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118338776, "Title": "Human-AI Collaboration in Cooperative Games: A Study of Playing Codenames with an LLM Assistant", "Abstract": "<p>Playing partial information, restricted communication, cooperative (PIRCC) games with humans have proven challenging for AI, due to our reliance on social dynamics and sophisticated cognitive techniques. Yet, recent advances in generative AI may change this situation through new forms of human-AI collaboration. This paper investigates how teams of players interact with an AI assistant in the PIRCC game Codenames and the impact this has on cognition, social dynamics, and player experience. We observed gameplay and conducted post-game focus groups with 54 participants across ten groups. Each group played three rounds of Codenames, with an AI assistant supporting Cluegivers. We found the AI assistant enhanced players' convergent and divergent thinking, but interfered with formation of team mental models, highlighting a tension in the use of AI in creative team scenarios. The presence of the AI challenged many players' understanding of the 'spirit of the game'. Furthermore, the presence of the AI assistants weakened social connections between human teammates, but strengthened connections across teams. This paper provides an empirical account of an AI assistant's effect on cognition, social dynamics, and player experience in Codenames. We highlight the opportunities and challenges that arise when designing hybrid digital boardgames that include AI assistants.</p>", "Keywords": "", "DOI": "10.1145/3677081", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Melbourne, Melbourne, Victoria, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The University of Melbourne, Melbourne, Victoria, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Melbourne, Melbourne, Victoria, Australia"}], "References": [{"Title": "The Hanabi challenge: A new frontier for AI research", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "280", "Issue": "", "Page": "103216", "JournalTitle": "Artificial Intelligence"}, {"Title": "Mental Models of Mere Mortals with Explanations of Reinforcement Learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Interactive Intelligent Systems"}, {"Title": "Human-AI Collaboration in a Cooperative Game Setting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "More Than a Gimmick - Digital Tools for Boardgame Play", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CHI PLAY", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Play for Real(ism) - Using Games to Predict Human-AI interactions in the Real World", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CHI PLAY", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "\"I Want To See How Smart This AI Really Is\": Player Mental Model Development of an Adversarial AI Player", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CHI PLAY", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 118338777, "Title": "From Novelty to Clinical Practice: Exploring VR Exergames with Physical Therapists", "Abstract": "<p> The COVID-19 pandemic, the rise of telehealth and virtual medicine, and the increased accessibility of commercial VR headsets have created opportunities to revitalize the physical therapy field and introduce more innovative tools to complement traditional practice. Researchers are actively exploring the intersection of VR, exergames, and gamified exercise to create new products that can improve both provider efficiency and patient care. This study builds on that momentum by delving into the perspectives of practitioners. We interviewed eight physical therapists after letting them experience a state-of-the-art VR exercise product (i.e., Supernatural VR ). Participants loved the game and were most optimistic about the mental benefits of using VR exergames alongside traditional PT, but cautioned the lack of feedback and expert guidance that could lead to misuse and further injury. Regarding the future integration of VR and PT, participants were cautiously optimistic and offered design feedback and recommendations, with warnings regarding the potential barriers to innovating clinical practice. </p>", "Keywords": "", "DOI": "10.1145/3677068", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Northeastern University, Boston, MA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Northeastern University, Boston, MA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Northeastern University, Boston, MA, USA"}], "References": [{"Title": "Development and validation of the player experience inventory: A scale to measure player experiences at the level of functional and psychosocial consequences", "Authors": "<PERSON><PERSON>den <PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "102370", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "A Survey of Privacy Vulnerabilities of Mobile Device Sensors", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "11s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "“I’m Just Overwhelmed”: Investigating Physical Therapy Accessibility and Technology Interventions for People with Disabilities and/or Chronic Conditions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Accessible Computing"}, {"Title": "Privacy-Enhancing Technology and Everyday Augmented Reality", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Emotional Virtual Characters for Improving Motivation and Performance in VR Exergames", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "CHI PLAY", "Page": "1115", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 118338798, "Title": "Construction of Probability Graph Optimization Model on Basis of Localization Algorithm", "Abstract": "Accurate positioning is crucial in fields such as indoor positioning, autonomous driving, and robot navigation. However, traditional base station positioning algorithms are often affected by factors such as sensor errors, environmental noise, and uncertainty, resulting in certain errors in the positioning results. The article constructed a probability graph model using Radio Frequency Identification (RFID) by using position estimation results as variables. In this model, each position estimation result was represented as a node, and the dependency relationship between positions was represented as an edge. This article observed sensor data and uses Bayesian inference methods to update the probability distribution of each node. The experimental results showed that compared to traditional base station positioning algorithms, the probability graph optimization model based on radio frequency identification can significantly improve the accuracy and reliability of positioning. The highest values for both reached 0.989 and 0.95, respectively. This model has significant advantages in positioning tasks and provides an effective solution for practical applications.", "Keywords": "probability graph; positioning algorithm; optimization model; bayesian inference; radio frequency identification", "DOI": "10.1016/j.procs.2024.09.021", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Song Li", "Affiliation": "Hubei Industrial Polytechic Shiyan, Hubei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hubei Industrial Polytechic Shiyan, Hubei, China"}], "References": []}, {"ArticleId": 118338801, "Title": "Utilization of Computer Simulation Technology in Landscape Sustainability Assessment and Optimization", "Abstract": "Due to the acceleration of global environmental change and urbanization, landscape sustainability assessment and optimization have become key tasks in urban planning and management. Computer simulation technology, especially System Dynamics (SD) models, can simulate the dynamic behavior of landscape systems and provide scientific basis for decision-making. This study used the SD model to construct a landscape sustainability assessment model. Firstly, key factors affecting landscape sustainability, such as land use, water resource management, and biodiversity conservation, were identified through literature review and expert interviews. Then, the Causal Loop Diagram (CLD) was used to analyze the interactions between these factors. Next, the model in SD modeling software was built; parameters and initial conditions were set; model validation and calibration were performed. Research has found that the SD model can effectively simulate the dynamic changes of landscape systems, providing a comprehensive, dynamic, and quantitative method for landscape sustainability assessment. In addition, compared to traditional management strategies, the ecological service value of the same location under the SD method could increase by 91€; the Shannon Wiener index could increase by 0.97; the soil organic matter content could increase by 11.27g/kg. The comparative experimental data results indicated that the SD model had significant advantages in landscape sustainability assessment and optimization, and could provide sustained support for landscape sustainability. Therefore, computer simulation technology has important application value in landscape sustainability assessment and optimization.", "Keywords": "system dynamics; landscape sustainability; causal loop diagram; computer simulation technology", "DOI": "10.1016/j.procs.2024.09.031", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Art & Design, Xi'an FanYi University, Xi'an, Shaanxi, China"}], "References": [{"Title": "Computer simulation via a couple of homotopy perturbation methods and the generalized differential quadrature method for nonlinear vibration of functionally graded non-uniform micro-tube", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S3", "Page": "2481", "JournalTitle": "Engineering with Computers"}, {"Title": "The computational modeling for the static analysis of axially functionally graded micro-cylindrical imperfect beam applying the computer simulation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S4", "Page": "3217", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 118338802, "Title": "Study on Optimization of MFC Electric Generation Parameters based on Box-Behnken Design", "Abstract": "MFC is a new energy technology that has emerged in the fields of environmental protection and energy. MFC has achieved the function of clean pollution and electricity generation, maximizing the environmental protection needs of the current shipping industry. However, MFC has the disadvantages of low power generation, unstable operation and high cost, which restrict its engineering application. This article uses single factor analysis and Box- Behnken experimental design to explore the output voltage of MFC under different levels of power generation parameters. Through variance analysis, the optimal power generation process parameters of MFC are determined to improve its power generation performance. The experimental results show that the order of significance of the parameters is temperature >pH> electrode spacing, the interaction between temperature and electrode spacing, pH and electrode spacing is the most significant, but the interaction between temperature and pH is not significant. Box-Behnken experimental model has good regression, and the optimal power generation parameters for MFC output voltage are: temperature of 29.85 °C, pH value of 6.96, and electrode spacing of 2.03 cm. Under this optimal power generation condition, the voltage predicted by the experimental model can reach 529.998 mV, while the voltage obtained by the actual experiment is 531.4 mV, and the error with the model prediction is only 0.26%, the data predicted by the Box Behnken response surface is reliable. The results show that the model can be well applied to the optimization design of MFC generating parameters.", "Keywords": "Box-Behnken; MFC; generation parameters", "DOI": "10.1016/j.procs.2024.09.030", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Shipping, Shandong Jiaotong University, Weihai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Shipping, Shandong Jiaotong University, Weihai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hebei Zhonglian Energy Environmental Protection Technology Co., LTD, HeBei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Shipping, Shandong Jiaotong University, Weihai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Shipping, Shandong Jiaotong University, Weihai, China"}], "References": []}, {"ArticleId": 118338816, "Title": "Construction of Enterprise Economic Management Sharing Platform Based on Feature Recognition Grid System", "Abstract": "In this paper, we construct an enterprise economic management sharing platform based on the feature recognition grid system, firstly, we use the fuzzy matching recognition algorithm to extract the grid features of economic management and map them into the fuzzy set of affiliation features, and then we extract the feature vectors from each grid to get the feature matrix, and finally, according to the similarity degree of the feature sequences, we set a threshold to give the recognition result. The results show that the recognition rate of the platform constructed in this paper reaches more than 99%, the classification accuracy is about 90% on average, and the optimization time is only about 19 s. It is verified that the method of this paper improves the accuracy of enterprise economic management, and has a positive role in promoting the healthy development of enterprises.", "Keywords": "Feature Recognition; Enterprise Economic Management; Grid Features; Fuzzy Matching Recognition; Affinity Features", "DOI": "10.1016/j.procs.2024.09.072", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Huanggang Polytechnic College, Huanggang, 438002, Hubei, China"}], "References": [{"Title": "Agility and system documentation in large-scale enterprise system projects: a knowledge management perspective", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "386", "JournalTitle": "Procedia Computer Science"}, {"Title": "Research on Marine Economic Development Information Management System Based on Supply Chain Technology", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "Supp03", "Page": "", "JournalTitle": "Journal of Interconnection Networks"}]}, {"ArticleId": 118338820, "Title": "Design of Data Encryption and Compression Methods", "Abstract": "Big data, cloud computing and other hot areas are the use of personal information, but in the process of using this data, a large number of improper behavior is used to steal personal information. Most of the encryption algorithms in common use nowadays are based on ones that are no longer absolutely secure, such as RSA attacked by factorization, elliptic curves attacked by <PERSON><PERSON>'s lambda algorithm, and symmetric encryption algorithms attacked by collisions. In this paper, an integrated encryption and compression algorithm is designed, firstly, the data is transformed into the form of matrix, and the Jordan decomposition is used for preliminary compression and encryption from the two-dimensional point of view, and then based on the sparsity of matrix vectors, the compression-aware technique is used for compressing the set of key vectors, and the ciphertext group J produced by Jordan decomposition (which is a string of numbers with matrix diagonal length base √2 N), as well as the set of key vectors Y and the intermediate value Θ produced by compression awareness are obtained in the end. This sidesteps the common algorithms that have already been breached and is able to combine better encryption performance with compression performance while being innovative.", "Keywords": "information security; cryptography; matrix computation; Jordan decomposition; compressed sensing techniques", "DOI": "10.1016/j.procs.2024.09.148", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Cyberspace Security, Hainan University, Haikou, 570228, China;Key Laboratory of Internet Information Retrieval of Hainan Province, Haikou, 570228, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyberspace Security, Hainan University, Haikou, 570228, China;Key Laboratory of Internet Information Retrieval of Hainan Province, Haikou, 570228, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyberspace Security, Hainan University, Haikou, 570228, China;Key Laboratory of Internet Information Retrieval of Hainan Province, Haikou, 570228, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shanwei Institute of Technology, Shanwei, 516600, China;Key Laboratory of Internet Information Retrieval of Hainan Province, Haikou, 570228, China"}], "References": []}, {"ArticleId": 118339018, "Title": "Computer Aided Environment Design Technology Based on Simulation Data", "Abstract": "Computer aided environment design technology, also known as Computer aided environment (CAE) technology, is a technology that utilizes computers for engineering design, analysis, and optimization. Simulation data refers to the data generated by simulating the real environment or system in a computer, with its core characteristics of simulation and predictability, used for performance evaluation. CAE technology simulates the state and behavior of buildings, facilities, or environments, evaluating the effectiveness of design solutions and predicting performance through simulation data. This article aims to conduct a technical analysis and practical case studies to evaluate the effectiveness, draw conclusions and suggestions from the technical analysis, in order to promote the development of computer-aided environmental design technology and improve the efficiency and quality of environmental design. Therefore, this article uses CAE technology to simulate the thermal performance, structural response, and fluid dynamics changes of buildings. This article simulates the temperature environment of rooms in buildings and the pedestrian density and traffic flow in traffic through experimental simulation and data comparison. It is found that the peak pedestrian density occurs at 16 and 17 minutes, and the lowest value is at 45 minutes. There is a certain similarity between the data results and the real data.", "Keywords": "simulation data; computer-aided design; environmental design; technical analysis", "DOI": "10.1016/j.procs.2024.09.024", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Art and Design, Shenyang Jianzhu University, Shenyang, 110168, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Art and Design, Shenyang Jianzhu University, Shenyang, 110168, China"}], "References": [{"Title": "Fat-based studies for computer-assisted screening of child obesity using thermal imaging based on deep learning techniques: a comparison with quantum machine learning approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "18", "Page": "13093", "JournalTitle": "Soft Computing"}, {"Title": "Computer-assisted Analysis of Field Recordings: A Case Study of Georgian Funeral Songs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "Journal on Computing and Cultural Heritage"}]}, {"ArticleId": 118339021, "Title": "Construction and Evaluation of Credit Risk Early Warning Indicator System of Internet Financial Enterprises Based On AI and Knowledge Graph Theory", "Abstract": "Through in-depth analysis of credit risk in the context of the development of Internet finance, the study recognized that its diversification and complexity not only include financial risks, but also involve the impact of non-financial factors. Based on this, combined with ESG factors, principal component analysis and grey correlation method are used to optimize the indicators, ensuring the comprehensiveness and accuracy of the indicator system. Using Convolutional Neural Network (CNN) as the warning model, considering the dynamic and static characteristics of data, a sub convolutional network was designed for training different types of data. At the same time, the credit rating division and warning threshold selection methods were optimized, improving the accuracy and practicality of the model. Through experimental verification, we found that the CNN model has a high accuracy in credit risk warning, and it also shows excellent classification performance and comprehensive effect compared to other models. Through systematic research and application, this paper provides a complete solution for the credit risk management of Internet financial enterprises, and makes positive contributions to the stability of the financial system and risk prevention. At the same time, we also emphasize the important role of technological innovation in financial intelligent management, especially the application of innovation systems centered on AI and knowledge graphs in the field of credit management, which points out the direction for the future development of the financial industry.", "Keywords": "Risk control and early warning; Digital financial institutions; AI technology; Data graph theory; Neural network algorithm", "DOI": "10.1016/j.procs.2024.09.110", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Yicheng Peng", "Affiliation": "Mergers & Acquisitions Practice, West Monroe Partners, New York, 10019, NY, USA"}], "References": [{"Title": "Credit risk evaluation using clustering based fuzzy classification method", "Authors": "Furkan Baser; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Selcuk-Kestel", "PubYear": 2023, "Volume": "223", "Issue": "", "Page": "119882", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 118339022, "Title": "Digital Analysis of Human Resources Archives Based on Network Information Security", "Abstract": "With the rapid development of information technology, human resource file management is gradually moving towards digital transformation. Digital management is an important part of enterprise archives construction. The application of digital technology to gather information and build a complete human resource archive analysis database can effectively promote the informatization level of enterprise archives, improve work efficiency, and provide strong support for enterprise development and decision-making. However, digital transformation also brings a series of challenges, such as network information security, data sharing, and technical standards, which need to be addressed. This paper aims to explore the significance, and point out the existing problems, put forward management optimization measures in enterprise archives management under the background of information technology through literature research, to provide some theoretical and practical references for the digital transformation of enterprise human resources archives.", "Keywords": "Human Resources Archives; Digital Analysis; Network Information Security; Information Management; Digital System", "DOI": "10.1016/j.procs.2024.09.082", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Economics and Management , Lanzhou University of Technology , Lanzhou , Gansu , China"}], "References": []}, {"ArticleId": 118339023, "Title": "Design of Personalized Recommendation System for Teaching Resources Based on Cloud Edge Computing", "Abstract": "With the continuous development of cloud computing and edge computing technologies, the education sector is gradually applying these technologies to enhance the management and utilization efficiency of teaching resources. Therefore, to address the issue of information overload mentioned above, it is necessary to establish a personalized recommendation system based on user needs, preferences, and other information, recommending products, information, and resources that may be of interest to users. This can not only save users search time, but also alleviate the problem of information overload to some extent. On this basis, this article discussed a new content oriented recommendation method: constructing a user interest feature vector resource association matching model, and analyzing it to achieve recommendation of similar resources. The experimental results showed that the MAE (Mean Absolute Error) value of personalized recommendation based on CF (Collaborative Filtering) algorithm was below 0.8, which was smaller than other algorithms, indicating high accuracy of recommendation based on CF algorithm.", "Keywords": "Teaching Resources; Personalized Recommendation System; Cloud Edge Computing; Collaborative Filtering Algorithm", "DOI": "10.1016/j.procs.2024.09.099", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Innovative Technical College, Dongguan, 523960, China"}], "References": [{"Title": "Collaborative filtering algorithm with social information and dynamic time windows", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "5", "Page": "5261", "JournalTitle": "Applied Intelligence"}, {"Title": "Similarity measures for Collaborative Filtering-based Recommender Systems: Review and experimental comparison", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "7645", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Algorithms are not neutral", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "4", "Page": "763", "JournalTitle": "AI and Ethics"}, {"Title": "An analysis of learning analytics in personalised learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "371", "JournalTitle": "Journal of Computing in Higher Education"}, {"Title": "Enhanced personalized recommendation system for machine learning public datasets: generalized modeling, simulation, significant results and analysis", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "1583", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Optimal Dependence of Performance and Efficiency of Collaborative Filtering on Random Stratified Subsampling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "3", "Page": "192", "JournalTitle": "Big Data Mining and Analytics"}]}, {"ArticleId": 118339026, "Title": "Design of Supply Chain Logistics Intelligent Management Information System Based on GIS Optimization Model", "Abstract": "With the improvement of productivity and the development of internet and information technology, many e-commerce systems and supply chain logistics systems have emerged, allowing for online purchases of food such as vegetables and fruits, as well as clothing and shoes. Nowadays, online shopping platforms such as Duoduo Maicai, Taobao, JD, and Meituan Youxuan have been integrated into people's lives, and various supermarkets and shopping malls are also everywhere. Therefore, the safety, efficiency, and real-time location of goods delivery need to be taken into account. Traditional supply chain logistics has problems such as requiring a large amount of manpower, inaccurate system positioning, inaccurate designed delivery routes, low efficiency, and inability to understand the status of drivers and vehicles. GIS (Geographic Information System) can assist systems in designing delivery routes, obtaining real-time locations of drivers or goods, and navigating. Therefore, in order to save manpower and ensure the safety and efficiency of goods delivery, this article used genetic algorithms to optimize and design a supply chain logistics intelligent management information system for GIS. This system had three modules, namely the client, driver, and terminal. The client mainly had functions such as publishing orders, checking the estimated arrival time of goods, and learning the real-time location of goods. The driver end mainly had functions such as real-time positioning, navigation, communication, viewing delivered orders, and monitoring cameras. The terminal mainly had functions such as viewing driver monitoring cameras and managing background information. Experiments showed that optimizing GIS could more accurately locate drivers and goods, assist drivers in navigation, assist systems in arranging delivery routes, and calculate the estimated arrival time of goods, resulting in an efficiency improvement of nearly 12%.", "Keywords": "Supply Chain Logistics System; GIS Optimization Model; Intelligent Information Management; Genetic Algorithm", "DOI": "10.1016/j.procs.2024.09.049", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong Transport Vocational College, Weifang, China"}], "References": []}, {"ArticleId": 118339027, "Title": "Design and Application of Language Translation System Resource Platform on Basis of Artificial Intelligence", "Abstract": "In daily lives, people often use translators for cross-language translation, but the current translation system has the problem of translation accuracy, and its translation speed also has some limitations. Artificial intelligence has begun to develop, and the language translation system resource platform based on artificial intelligence has gradually attracted attention. The design and application of this platform are of great significance for providing high-quality and efficient language translation services. This research proposes and demonstrates a language translation system resource platform based on artificial intelligence technology. The platform is based on artificial intelligence technology for hardware resources, network resources, and the use of artificial intelligence algorithms to achieve language translation that is accurate and smooth. Through experimental testing, the results showed that the translation accuracy of the system platform reached 95-99%. The platform provides quality and efficient language translation services which can promote and facilitate human cross-language communication and understanding.", "Keywords": "Language Translation System; Resource Platform; Artificial Intelligence; Neural Machine Translation", "DOI": "10.1016/j.procs.2024.09.079", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "General Educational School, Jinan Vocational College, Jinan, 250103, Shandong, China"}, {"AuthorId": 2, "Name": "Caiyun Li", "Affiliation": "General Educational School, Jinan Vocational College, Jinan, 250103, Shandong, China"}], "References": [{"Title": "Automated Bangla sign language translation system for alphabets by means of MobileNet", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>. S<PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "3", "Page": "1292", "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)"}, {"Title": "Empirical Analysis of Phrase-Based Statistical Machine Translation System for English to Hindi Language", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "2", "Page": "135", "JournalTitle": "Vietnam Journal of Computer Science"}, {"Title": "SemMT: A Semantic-Based Testing Approach for Machine Translation Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 118339029, "Title": "Research on mental health status evaluation based on data mining model", "Abstract": "This paper puts forward a new mental health evaluation model based on data mining technology, and analyzes the factors that affect mental health. The model understands the influencing factors according to the mental health status of different student groups, then reduces the dimensionality of the original data through principal component analysis and discriminant analysis, and finally establishes a dependent variable prediction model through regression analysis. The experimental results show that the method proposed in this paper can effectively predict students' mental health status.", "Keywords": "Data mining; Mental health; Academic performance", "DOI": "10.1016/j.procs.2024.09.138", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Education, Linyi University, Linyi, 276000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Education, Linyi University, Linyi, 276000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Education, Linyi University, Linyi, 276000, China"}], "References": [{"Title": "Perceiving the self as authentic on social media precedes fewer mental health symptoms: A longitudinal approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "152", "Issue": "", "Page": "108056", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Exploring the potential of federated learning in mental health research: a systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "2", "Page": "1619", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 118339033, "Title": "Application and Practice of Intelligent Algorithms in Computer Software Design and Testing", "Abstract": "With the rapid development of information technology, software has become a part of social life. However, for complex software systems, traditional design and testing methods face a series of problems such as low testing coverage and low efficiency. The main purpose of this article is to study the optimization of software design and testing processes using intelligent algorithms, in order to improve the quality and efficiency of software. This article established a real-time optimization and feedback adjustment mechanism during the software design and testing phase, enabling intelligent algorithms to respond promptly to the environment and requirements. Monitoring and control strategies were designed to ensure the algorithm runs stably for a long time. Test method 1 (traditional method) had 100 test cases, a total execution time of 1200 seconds, and an average execution time of 12 seconds per test case; test method 2 (Intelligent Algorithm Optimization) had 100 test cases, a total execution time of 800 seconds, and an average execution time of 8 seconds per test case. The research results of this article play a good promoting role in improving software testing coverage and software structure optimization, and also provide new ideas and methods for the promotion of intelligent algorithms in software engineering.", "Keywords": "Computer Software Design; Software Testing; Intelligent Algorithms; Average Execution Time", "DOI": "10.1016/j.procs.2024.09.085", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Wuhan Polytechnic, Wuhan, Hubei, 430074, China"}], "References": [{"Title": "Using Computer-Aided Design Software in Teaching Environmental Art Design", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "S1", "Page": "173", "JournalTitle": "Computer-Aided Design and Applications"}]}, {"ArticleId": 118339035, "Title": "News Recommendation System Based on User Interest and Deep Network", "Abstract": "In order to provide the personalized news recommendation for users more efficiently, the personalized recommendation system combined with deep network was investigated. Based on the deep network, a news recommendation system for users was designed. By means of neural network and aggregating users’ interest characteristics, users’ personalized needs for news recommendation was met. In order to solve the problem of inaccurate construction of users’ interest preference characteristics, a personalized news method based on users’ search records and interest preference was proposed. By constructing exogenous user interest preferences from user search records, the final recommendation list was generated by using the fusion method of the two preferences. Because the traditional recommendation algorithm ignored time series of the users’ browsing behaviors, an improved circulation model of neural network algorithm was proposed. The parallel convolution neural network based on attention was used to aggregate characteristics of users’ interest and the recursive neural network based on attention mechanism was used to explore hidden time series characteristics. At the same time, it was tested on real news data sets and the results showed that this method had a good recommendation effect.", "Keywords": "deep network; personalized needs; news; users; deep learning; recommendation system", "DOI": "10.1016/j.procs.2024.09.131", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Move and Media, Sichuan Normal University, Chengdu, 610000, China"}], "References": [{"Title": "A trusted recommendation scheme for privacy protection based on federated learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "3-4", "Page": "218", "JournalTitle": "CCF Transactions on Networking"}, {"Title": "Dynamic Light Weight Recommendation System for Social Networking Analysis Using a Hybrid LSTM-SVM Classifier Algorithm", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; Dr<PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "1", "Page": "59", "JournalTitle": "Optical Memory and Neural Networks"}, {"Title": "Context-Aware News Recommendation System: Incorporating Contextual Information and Collaborative Filtering Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Reinforcement Learning-Based News Recommendation System", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "6", "Page": "4493", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Bias Reduction News Recommendation System", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "1", "Page": "92", "JournalTitle": "Digital"}]}, {"ArticleId": 118339038, "Title": "Extreme Weather Hazards and Insurance Revenues Based on Bi-directional LSTM", "Abstract": "As extreme weather becomes more prevalent, it has serious implications for the insurance industry, real estate owners, and historic buildings. First of all, for the extreme weather, we summarize extreme weather into three parts: extreme temperature, extreme precipitation, and extreme humidity, and we process the data by combining the actual situation and applying the improved Critic method, and we demonstrate the data processing by taking the three data of Lhasa and Luxor as examples. anomalies. Subsequently, a future extreme climate prediction model for a region based on the Bi-directional LSTM method is developed. A time-weather model was fitted based on the collected data, with the highest and lowest points as the anomalies. At the same time, we simplify the formula to derive the insurance company's revenue, thus modeling the relationship between the degree of extreme weather hazard and insurance revenue, and derive that the insurance company will not be able to underwrite when the combined hazard is greater than the ratio of the insurance claim cost to the insurance payment. We applied the resulting model to Lhasa and Luxor, and plotted the combined hazard level of the actual data and the predicted data to help the insurance company visualize the combined hazard level of the region each month, and then choose whether to underwrite the policy in that region or not.", "Keywords": "Extreme Weather; Insurance Industry; Bi-Directional LSTM; Time-Weather Model; Combined Hazard Level", "DOI": "10.1016/j.procs.2024.09.081", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Gen Li", "Affiliation": "School of Physics and Microelectronics, Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Zhengzhou University of Aeronautics, Zhengzhou, China"}], "References": []}, {"ArticleId": *********, "Title": "Enterprise Business Information Management Archive System under Computer Artificial Intelligence Technology", "Abstract": "The enterprise business information management archive system is an indispensable and important tool in modern enterprise management. With the rapid development of computer artificial intelligence technology, its application research in enterprise business information management archives system has attracted widespread attention. This article aims to explore the application of computer artificial intelligence technology in this field and evaluate its impact on system performance and efficiency. This study used literature review and empirical analysis methods to systematically collect and analyze relevant research literature, and summarized the application of computer artificial intelligence technology in enterprise business information management archive systems. The research progress and application cases of machine learning, natural language processing, deep learning, and recommendation systems in this field were given special attention. Research has found that computer artificial intelligence technology has broad application prospects and potential in enterprise business information management archive systems.", "Keywords": "Archive System; Enterprise Business Information Management; Artificial Intelligence Technology; Naive Bayes", "DOI": "10.1016/j.procs.2024.09.102", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rattana Bundit University, 10240, Bangkok, Thailand"}], "References": []}, {"ArticleId": 118339188, "Title": "Chronic Kidney Disease (CKD) Prediction Using Stochastic Deep Radial Basis for Feature Extractiona Residual Neural Network", "Abstract": "<p>Chronic Kidney Disease (CKD) is an increasingly common health issue, affecting a significant portion of the global population. Accurate prediction of CKD progression is crucial for early intervention and personalized healthcare. However, existing models often fail to adequately capture the temporal dependencies in disease progression, resulting in suboptimal performance. Current predictive models for CKD are limited by their inability to manage categorical variables effectively and address overfitting while capturing the necessary temporal subtleties. This study aims to address these limitations by developing a novel approach that enhances the accuracy and reliability of CKD progression predictions. The proposed method integrates a unique Automated Stochastic Deep Radial Basis (SDRB) for feature extraction, a Residual Neural Network (ResNet) for classification, and a Bidirectional Long Short-Term Memory (biLSTM) network for temporal prediction. This combination is designed to handle the complexities of CKD data, manage categorical variables, and mitigate overfitting while capturing temporal dependencies.Our model significantly outperforms current state-of-the-art models, achieving 92% accuracy, 91% recall, and 93% precision. The SDRB component improves feature extraction, ResNet enhances classification, and biLSTM effectively captures temporal dependencies, resulting in a robust and reliable CKD progression prediction system.</p>", "Keywords": "Chronic Kidney Disease; Temporal prediction; Feature extraction; Residual neural network; Bidirectional LSTM", "DOI": "10.1007/s42979-024-03302-5", "PubYear": 2024, "Volume": "5", "Issue": "7", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Vels Institute of Science, Technology & Advanced Studies (VISTAS), Chennai, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Vels Institute of Science, Technology & Advanced Studies (VISTAS), Chennai, India"}], "References": [{"Title": "A Comparative Analysis of Machine Learning and Deep Learning Approaches for Prediction of Chronic Kidney Disease Progression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "", "Page": "", "JournalTitle": "EAI Endorsed Transactions on Internet of Things"}]}, {"ArticleId": 118339297, "Title": "Review of AI & XAI-based breast cancer diagnosis methods using various imaging modalities", "Abstract": "<p>Breast cancer is a significant health concern; early detection and treatment are critical to improving patient outcomes. Artificial Intelligence has the potential to assist healthcare professionals in the diagnosis and treatment of breast cancer, and clinical decision support systems using AI algorithms can help to improve the accuracy and speed of breast cancer detection and diagnosis. By analysing large amounts of data from various imaging modalities, AI algorithms can help identify breast cancer early, increasing the chances of successful treatment. This survey aims to briefly overview the most prevalent breast cancer types, their staging, and the methods and modalities for their diagnosis to address these problems. Our investigation showed that, when compared to ML, DL techniques achieved the highest accuracy, with an approximate 2% improvement in accuracy. This review article mainly focuses on four commonly used imaging modalities: Mammogram, Thermogram, Ultrasound imaging and histopathology images. This survey critically analyzes breast cancer detection approaches and modalities, as well as a cost-to-accuracy comparison. Explainable AI (XAI) techniques and their application for breast cancer diagnosis are critically examined, as well as their advantages and limitations.</p>", "Keywords": "Breast cancer; Mammogram; Thermogram; Ultrasound imaging; Histopathology", "DOI": "10.1007/s11042-024-20271-2", "PubYear": 2025, "Volume": "84", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, SRM Institute of Science and Technology, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical & Electronics Engineering, SASTRA Deemed University, Thanjavur, India; Corresponding author."}, {"AuthorId": 3, "Name": "Thanikaiselvan V", "Affiliation": "School of Electronics Engineering, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, King Abdulaziz University, Jeddah, Kingdom of Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical & Electronics Engineering, SASTRA Deemed University, Thanjavur, India"}], "References": [{"Title": "Deep learning-based breast cancer classification through medical imaging modalities: state of the art and research challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "1655", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "From local explanations to global understanding with explainable AI for trees", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "56", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Automated breast cancer detection using hybrid extreme learning machine classifier", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "5489", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Deep learning for real-time semantic segmentation: Application in ultrasound imaging", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "27", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Medical imaging technique using curvelet transform and machine learning for the automated diagnosis of breast cancer from thermal image", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "3", "Page": "981", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "Breast ultrasound tumour classification: A Machine Learning—Radiomics based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "7", "Page": "e12713", "JournalTitle": "Expert Systems"}, {"Title": "Medical artificial intelligence", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "11", "Page": "34", "JournalTitle": "Communications of the ACM"}, {"Title": "Screening of breast cancer from thermogram images by edge detection aided deep transfer learning model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "7", "Page": "9331", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Hybrid deep learning and genetic algorithms approach (HMB-DLGAHA) for the early ultrasound diagnoses of breast cancer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "11", "Page": "8671", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Literature review: efficient deep neural networks techniques for medical image analysis", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "5791", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Automated diagnosis of breast cancer from ultrasound images using diverse ML techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "21", "Page": "30169", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A deep learning approach for automatic detection, segmentation and classification of breast lesions from thermal images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118774", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Gamma function based ensemble of CNN models for breast cancer detection in histopathology images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119022", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid end-to-end learning approach for breast cancer diagnosis: convolutional recurrent network", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "105", "Issue": "", "Page": "108562", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Predicting Colorectal Cancer Using Machine and Deep Learning Algorithms: Challenges and Opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "2", "Page": "74", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Multimodal classification of breast cancer using feature level fusion of mammogram and ultrasound images in machine learning paradigm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "7", "Page": "21347", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Unsupervised Domain Adaptation for Cross-domain Histopathology Image Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "8", "Page": "23311", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Exploring DeepDream and XAI Representations for Classifying Histological Images", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Explainable artificial intelligence for medical imaging: Review and experiments with infrared breast images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "3", "Page": "", "JournalTitle": "Computational Intelligence"}]}, {"ArticleId": *********, "Title": "Animation Character Mouth Matching Model Considering Reinforcement Learning and Feature Extraction", "Abstract": "With the development of the times, animation production has become increasingly sophisticated, and mouth matching is one of the key points to ensure the vividness and realism of animated characters. Therefore, a study proposes an animated character mouth matching model that takes into account reinforcement learning and feature extraction. Using the Actor-Critic method in reinforcement learning, the extracted audio and facial features are used as input states to predict the next facial feature in the next time step. The experimental results show that the proposed model has an average accuracy of 95.61% and an F1 value of 97.13% on three databases. Meanwhile, the peak signal-to-noise ratio and structural similarity index are 41.77 and 0.93, respectively, which are better than their comparison methods. In addition, the study tested the error of mouth shape under different emotions, and the results showed an average mean square error of only 6.639. Finally, the user survey results showed that the animated characters generated by the proposed model received more recognition in mouth shape matching and realism, with a highest selection rate of 98.64%. The successful application of the proposed model provides new ideas and methods for research in related fields, laying the foundation for further promotion and innovation of animation production technology.", "Keywords": "", "DOI": "10.31449/inf.v48i3.6187", "PubYear": 2024, "Volume": "48", "Issue": "3", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118339300, "Title": "Integrated Software Effort Estimation: A Hybrid Approach", "Abstract": "Risks associated with delivery of a software project and the effort spent on managing these risks are well researched topics. Very few have included this extra effort termed as risk exposure of a project, in the software effort estimate of a project. This research proposes to improve the accuracy of software effort estimates by integrating the risk exposure with the initial effort estimate of the project. A function to calculate integrated effort estimates has been defined and evolutionary algorithms ABC, PSO and GLBPSO have been used to optimize the MMRE. The approach has been tested on two datasets collected from industry, one for waterfall projects, another for agile projects. For both the datasets, integrated effort estimates were more accurate on account of MMRE, standardized accuracy, effect size and R 2 , than the initial effort estimates. Evolutionary algorithms also gave the optimum weight values at which the MMRE was optimal for both the datasets. These weight values determine the contribution of risk associated with each project cost factor in the risk exposure of the project. Integrated effort estimates have been found to be more accurate, reliable, and comprehensive than the initial effort estimates. Application of evolutionary algorithms help in reducing any bias in the integrated effort estimates.", "Keywords": "", "DOI": "10.31449/inf.v48i3.4515", "PubYear": 2024, "Volume": "48", "Issue": "3", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The NorthCap University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118339317, "Title": "Decentralized collaborative machine learning for protecting electricity data", "Abstract": "<p>In recent years, there has been a noticeable surge in electric power load due to economic development and improved living standards. The growing need for smart power solutions, such as leveraging user electricity data to forecast power peaks and utilizing power data statistics to enhance end-user services, has been on the rise. However, the misuse and unauthorized access of data have prompted stringent regulations to safeguard data integrity. This paper presents a novel decentralized collaborative machine learning framework aimed at predicting peak power loads while protecting the privacy of users’ power data. In this scheme, multiple users engage in collaborative machine learning training within a peer-to-peer network free from a centralized server, with the objective of predicting peak power loads without compromising users’ local data privacy. The proposed approach leverages blockchain technology and advanced cryptographic techniques, including multi-key homomorphic encryption and consistent hashing. Key contributions of this framework include the development of a secure dual-aggregate node aggregation algorithm and the establishment of a verifiable process within a decentralized architecture. Experimental validation has been conducted to assess the feasibility and effectiveness of the proposed scheme, demonstrating its potential to address the challenges associated with predicting peak power loads securely and preserving user data privacy.</p>", "Keywords": "", "DOI": "10.3233/JHS-230198", "PubYear": 2024, "Volume": "30", "Issue": "4", "JournalId": 37101, "JournalTitle": "Journal of High Speed Networks", "ISSN": "0926-6801", "EISSN": "1875-8940", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department first, State Grid Shandong Electric Power Company, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Shandong Electric Power Research Institute, Jinan, China"}], "References": [{"Title": "An overview on smart contracts: Challenges, advances and platforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Hong-<PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "475", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A review of applications in federated learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106854", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Privacy-preserving blockchain-based federated learning for traffic flow prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "328", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A survey on federated learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106775", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep AI Enabled Ubiquitous Wireless Sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Short Term Electric Power Load Forecasting Using Principal Component Analysis and Recurrent Neural Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "1", "Page": "149", "JournalTitle": "Forecasting"}]}, {"ArticleId": 118339350, "Title": "Design of Medical Equipment Management System Based on PCA-GRA-BK Algorithm", "Abstract": "In recent years, medical technology has developed very well, and efficient management of medical equipment has become one of the key factors in improving hospital operational efficiency. This article proposes a design scheme for a medical equipment management system based on Principal Component Analysis (PCA), Grey Relational Analysis (GRA), and BK (Baker-King) algorithms. This research plan integrates these three algorithms to optimize the decision-making process of equipment procurement, maintenance, and replacement, in order to improve the decision-making efficiency and precision of the system. This study first analyzed the shortcomings of existing medical equipment management, and then compared the effectiveness of traditional management methods and this scheme by designing and implementing the integrated algorithm, verifying its effectiveness. In the decision precision testing experiment, it was shown that the PCA-GRA-BK algorithm based system achieved a precision of 82% and a recall rate of 75% in device classification tasks. In the experiment of resource utilization analysis, the PCA-GRA-BK algorithm significantly improved the efficiency of device utilization and reduced resource waste. In the system response time measurement experiment, the response time of the system based on PCA-GRA-BK algorithm increased to a maximum of 11.72 seconds. In the final user satisfaction survey evaluation, it was found that users felt good about the operation of the system. From the above results, it can be seen that these experimental results not only confirm the effectiveness of the system design based on PCA-GRA-BK algorithm, but also provide specific data support for further optimizing system performance.", "Keywords": "Medical Equipment Management System; Decision Support System; PCA-GRA-BK Algorithm; Grey Correlation Analysis", "DOI": "10.1016/j.procs.2024.09.103", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> He", "Affiliation": "Pingdu Municipal Health Bureau, Pingdu, 266700, Shandong, China"}], "References": [{"Title": "Medical supply chain integrated with blockchain and IoT to track the logistics of medical products", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "21", "Page": "32917", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 118339351, "Title": "The Network Teaching Platform Based on Information Technology", "Abstract": "At present, network education, which takes computer network and technology as the means of education transmission and interaction, has become an important part of modern distance education, reflecting the characteristics of networking, personalization, interactivity and comprehensiveness. This paper studies the technical standards of network teaching platform, realizes the standardization of network teaching platform system, then analyzes several existing distance education teaching modes in China, and designs the function of network teaching platform system. In this paper, through the form of questionnaire survey to understand the specific implementation of the design and implementation of the network teaching platform, in the two user groups of teachers and students, the questionnaire survey analysis results. The results show that both teachers and students can't refuse the convenience of the online teaching platform. The teachers are satisfied with the convenience of the online teaching platform, and only 5.8% of the students have a general sense of the online teaching platform.", "Keywords": "Design and Implementation; Distance Education; Information Technology Standards; Network Teaching; Teaching Platform", "DOI": "10.1016/j.procs.2024.09.143", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Primary Education, Guangxi College for Preschool Education, Nanning 530022, China"}], "References": [{"Title": "Teaching recurrent neural networks to infer global temporal structure from local examples", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "4", "Page": "316", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Design and Application of English Assisted Learning System Based on Mobile Learning Platform", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "228", "Issue": "", "Page": "231", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 118339358, "Title": "Network Mathematical Virtual Data Analysis Model Based on Deep Learning Algorithm", "Abstract": "In today's era of big data, people need to extract valuable and nutritious information from these data, but for the huge database, manual browsing and manual retrieval by ordinary users are obviously inefficient. Network data analysis can help users to study network data and deeply understand the embedded value of big data, which is popular in both civil and industrial and academic circles. This article aimed to use intelligent mathematical models to assist in the construction of network data analysis models and enabled the model to be repeatedly trained in the training set based on deep learning algorithms, becoming increasingly able to identify patterns between data.", "Keywords": "Deep Learning Algorithm; Intelligent Mathematical Model; Network Data Analysis Model; Regularization Processing; Convolutional Neural Network", "DOI": "10.1016/j.procs.2024.09.046", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong Vocational College of Science and Technology, Weifang, Shandong, China"}], "References": [{"Title": "Convolutional neural networks in medical image understanding: a survey", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "1", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Deep Learning applications for COVID-19", "Authors": "<PERSON>; <PERSON><PERSON>; Bork<PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "18", "JournalTitle": "Journal of Big Data"}, {"Title": "Machine learning and deep learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "3", "Page": "685", "JournalTitle": "Electronic Markets"}, {"Title": "Deep Learning: A Comprehensive Overview on Techniques, Taxonomy, Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "6", "Page": "420", "JournalTitle": "SN Computer Science"}, {"Title": "Geometric deep learning on molecular representations", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "12", "Page": "1023", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Avoiding Overfitting: A Survey on Regularization Methods for Convolutional Neural Networks", "Authors": "<PERSON>i Gonçalves Dos Santos; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Social network data analysis to highlight privacy threats in sharing data", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}]}, {"ArticleId": *********, "Title": "Digital Human Resources Optimization Allocation Model and Algorithm of Artificial Intelligence Technology", "Abstract": "From the perspective of optimal allocation of enterprise human resources, in view of the challenges brought by changes in enterprise organizational structure and changes in job requirements in the digital environment to human resource allocation, the article aims to improve the competitiveness of enterprises, combines the characteristics of enterprise organizational structure and job demand, and analyzes the optimal allocation of human resources. The article builds a digital human resources optimization allocation model based on artificial intelligence technology, and designs an artificial intelligence-based digital human resources optimization allocation algorithm. The results show that the resource utilization rate of the digital human resources optimization allocation model based on artificial intelligence technology used in the experiment can reach up to 79.2%.", "Keywords": "human resources; artificial intelligence technology; optimized configuration model; company competitiveness", "DOI": "10.1016/j.procs.2024.09.017", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Changchun Humanities and Sciences College, Changchun, 130117, Jilin, China"}], "References": [{"Title": "The role of IT-based technologies on the management of human resources in the COVID-19 era", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "6", "Page": "2065", "JournalTitle": "Kybernetes"}]}, {"ArticleId": *********, "Title": "Research on Modeling of Influencing Factors of New Eco-tourism Management Based on Economic Fluctuation Prediction", "Abstract": "This paper begins by analyzing the effect of market economic fluctuations on the digital economy's tourism economy. Second, this paper uses a city cluster in the Yangtze River Delta in China as an example, selects the tourism economic data of the region from 2011 to 2021, and forecasts the tourism development potential of the region from 2022 to 2026 using the conventional methods for predicting economic fluctuations. However, conventional models for predicting economic fluctuations, such as ARIMA, have large prediction errors. This paper combines the RBF neural network and exponential smoothing method and compares them to GM(1, 1), ARIMA, and other models. The findings indicate that the tourism economy in this region is spatially distinct and relatively stable. Therefore, the methodology presented in this paper is capable of accurately predicting tourism demand and growth potential.", "Keywords": "Influencing factors; Eco-tourism Management; Economic fluctuation prediction; ARIMA", "DOI": "10.1016/j.procs.2024.09.139", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Weifang Engineering Vocational College, Weifang, 262500, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Weifang Engineering Vocational College, Weifang, 262500, China"}], "References": []}, {"ArticleId": 118339370, "Title": "Contents", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1877-0509(24)02783-2", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 118339598, "Title": "L2 learner experiences in a playful constructivist metaverse space", "Abstract": "<p>This study creates a virtual space for language learning using a user-customizable metaverse platform and explores its potential for EFL learning. To this end, a virtual learning space, grounded in constructivist learning principles – contextualized learning, active learning, and collaborative learning – was created on a 2D metaverse platform. The metaverse was designed as a simulated deserted island for enjoyable and playful learning, allowing the students to actively explore, discover, and interact as they look for clues to escape the island. For educational application, 29 Korean middle school students participated in a two-hour activity. Data included screen recordings of student activities, student surveys, and interviews with the students and teachers. The findings showed that, as an EFL learning space of playful constructivism, the metaverse had great potential to embed contextualized learning and served as a medium for active learning that positively affected student interest and motivation. The results confirmed that the team-based approach combined with a game-like metaverse fostered student collaboration. Overall, the study showcased how language instructors can make use of a customizable metaverse for L2 learning and how a virtual space may serve as an arena for learner-centered instruction.</p>", "Keywords": "playful constructivism; user-customizable metaverse; learner perception; collaborative learning; active learning", "DOI": "10.1017/S0958344024000235", "PubYear": 2025, "Volume": "37", "Issue": "1", "JournalId": 12184, "JournalTitle": "ReCALL", "ISSN": "0958-3440", "EISSN": "1474-0109", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Kyung Hee University, Republic of Korea ()"}, {"AuthorId": 2, "Name": "Tae youn Ahn", "Affiliation": "Korea National Sport University, Republic of Korea ()"}], "References": [{"Title": "Exploring the influence of interactive network and collective knowledge construction mode on students’ perceived collective agency", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "104240", "JournalTitle": "Computers & Education"}, {"Title": "Goal-oriented active learning (GOAL) system to promote reading engagement, self-directed learning behavior, and motivation in extensive reading", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "104239", "JournalTitle": "Computers & Education"}, {"Title": "Exploring Features facilitating learning on metaverse", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "3", "Page": "71", "JournalTitle": "Journal of Korea Game Society"}, {"Title": "Enhancing EFL pre-service teachers’ affordance noticing and utilizing with the Synthesis of Qualitative Evidence strategies: An exploratory study of a customizable virtual environment platform", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "104620", "JournalTitle": "Computers & Education"}, {"Title": "Factors affecting incidental L2 vocabulary acquisition and retention in a game-enhanced learning environment", "Authors": "<PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "274", "JournalTitle": "ReCALL"}, {"Title": "Second Language Learning Through an Emergent Narrative in a Narrative-Rich Customizable Metaverse Platform", "Authors": "<PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "6", "Page": "1071", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": 118339724, "Title": "Optimizing marine vehicles industry: a hybrid analytical hierarchy process and additive ratio assessment approach for evaluating and selecting IoT-based marine vehicles", "Abstract": "<p>Rapid developments in the Internet of Things (IoT) have opened the door for game-changing applications in numerous sectors, especially the vehicle industry. There is a rising demand for efficient assessment and decision-making methodologies to pinpoint the most promising choices for the vehicle sector with the introduction of IoT-based maritime vehicles. To overcome this issue, the integrated multi-criteria decision-making analysis (MCDA) paradigm proposed in this research combines the additive ratio assessment (ARAS) and analytic hierarchy process (AHP) approaches to evaluate and choose IoT-based maritime vehicles based on their performance- and authenticity-related criteria in the vehicle sector. The selection issue is hierarchically organized, and the assessment criteria are prioritized using the AHP approach. There are seven performance and authentication related criteria are selected that might aid in the selection procedure. Using the AHP, we are assigned these criteria proportionate weights that reflect their respective significance and interrelationships. AHP, however, falls short of offering a thorough analysis of the alternatives that exist. To overcome these restrictions, this research presents the integration of AHP with the ARAS approach for the ranking of alternatives according to how well they perform against the set criteria. By using the ARAS technique, it is possible to get over the restrictions of AHP and achieve a more thorough assessment of maritime IoT-based vehicles. The efficiency of the framework is proven using empirical data and professional judgment. The findings show that the hybrid method successfully encapsulates the intricate relationships between the factors being evaluated and objectively appraises the potential of IoT-based maritime vehicles for the automotive sector. This study extends to the area by providing an organized and thorough method for assessing and choosing IoT-based maritime vehicles. Considering several factors and their mutual dependence, the hybrid AHP and ARAS technique gives decision-makers a powerful tool for evaluating the potential of IoT-based maritime vehicles in the automotive sector. Smart decisions on the deployment of IoT-based marine vehicles and maximizing the potential they present may be made by beneficiaries in the automotive sector using the study’s results.</p>", "Keywords": "Internet of Things;MCDA;Marine vehicle industry", "DOI": "10.7717/peerj-cs.2308", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Accounting & Information Systems at the College of Business and Economics, Qatar University, Doha, Qatar"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, <PERSON><PERSON><PERSON> Institute of Engineering Sciences and Technology, Pakistan, Swabi, KPK, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Swabi, Pakistan, Swabi, KPK, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering, Gachon University, Sengnam-si, Seoul, Republic of South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Artificial Intelligence (AI) and Data Science, Sejong University, Seoul,  Republic of South Korea"}], "References": [{"Title": "RETRACTED: Application of ship electric propulsion based on internet of things system and electronic system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "103748", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Edge Metric Dimension of Honeycomb and Hexagonal Networks for IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "2", "Page": "2683", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Man Overboard Detection System Using IoT for Navigation Model", "Authors": "<PERSON>黶eyin G黵黮er; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "3", "Page": "4955", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 118339757, "Title": "AI robots pioneer the Smarter Inclusive Society", "Abstract": "This paper outlines a project aimed at realizing a “Smarter Inclusive Society” by 2050 through the integration of AI robots into various public facilities. Led by the Cabinet Office’s “Moonshot Research and Development Program,” the project focuses on developing Adaptable AI-enabled Robots that enhance self-efficacy by supporting users’ abilities while maintaining their sense of independence. Key to the project is the Robotic Nimbus, a soft and flexible robot designed to provide tailored assistance while preserving user agency. The concept of “Adaptable AI-enabled Robots” is introduced to ensure versatility in accommodating user needs and preferences. In addition to physical assistance, the project emphasizes creating engaging experiences through activities like dance and sports, fostering excitement and inclusivity. Collaborations, such as the “Yes We Dance!” performance, demonstrate the potential of AI technology in enhancing rehabilitation opportunities and promoting social participation. By 2050, the project aims to establish a society where AI robots contribute to mental, physical, and social wellbeing, empowering individuals to engage in independent activities and fostering a vibrant, inclusive community. This paper is a compilation of articles/papers/presentations previously presented on the Moonshot Hirata project.", "Keywords": "Moonshot Project; Smarter Inclusive Society; Wellbeing; Self-efficacy; Robotic Nimbus", "DOI": "10.1007/s10015-024-00975-2", "PubYear": 2024, "Volume": "29", "Issue": "4", "JournalId": 4137, "JournalTitle": "Artificial Life and Robotics", "ISSN": "1433-5298", "EISSN": "1614-7456", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Robotics, Tohoku University, Sendai, Japan; Corresponding author."}], "References": []}, {"ArticleId": 118339773, "Title": "Linking intra- and extra-cellular metabolic domains via neural-network surrogates for dynamic metabolic control", "Abstract": "We outline a modeling and optimization strategy for investigating dynamic metabolic engineering interventions. Our framework is particularly useful at the early stages of research and development, often constrained by limited knowledge and experimental data. Elucidating a priori optimal trajectories of manipulatable intracellular fluxes can guide the design of suitable control schemes, e.g., cyber(ge)netic or in-cell approaches, and the selection of appropriate actuators, e.g., at the transcriptional or post-translational levels. Model-based dynamic optimization is proposed to predict optimal trajectories of target manipulatable intracellular fluxes. A challenge emerges as existing models are often oversimplified, lacking insights into metabolism, or excessively complex, making them difficult to build and implement. Here, we use surrogates derived from steady-state solutions of constraint-based metabolic models to link manipulatable intracellular fluxes to the process exchange rates of structurally simple hybrid dynamic models. The latter can be conveniently used in optimal control problems of metabolism. As a proof of concept, we apply our method to a reduced metabolic network of Escherichia coli considering two different scenarios of dynamic metabolic engineering.", "Keywords": "Hybrid modeling; surrogate modeling; flux balance analysis; macro-kinetic modeling; dynamic metabolic engineering", "DOI": "10.1016/j.ifacol.2024.10.020", "PubYear": 2024, "Volume": "58", "Issue": "23", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>s", "Affiliation": "Department of Chemical and Biological Engineering , Princeton University , United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Biological Engineering , Princeton University , United States;Omenn-Darling Bioengineering Institute , Princeton University , United States;The Andlinger Center for Energy and the Environment , Princeton University , United States;High Meadows Environmental Institute , Princeton University , United States"}], "References": [{"Title": "CNApy: a CellNetAnalyzer GUI in Python for analyzing and designing metabolic networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "5", "Page": "1467", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 118339784, "Title": "Robust tracking and detection based on radar camera fusion filtering in urban autonomous driving", "Abstract": "<p>The primary goal of autonomous vehicles is vehicle safety, achieved through vehicle planning and control based on the understanding of the driving environment. To understand the surrounding environment, most autonomous driving systems utilize lidar and cameras for tracking nearby objects. However, both sensors are effective only within a short range of 50 m and lack robustness in adverse conditions, thereby failing to ensure the stability of object tracking and state estimation. Therefore, to enable continuous tracking over long distances, this paper proposes a robust tracking and detection system based on radar-camera fusion in urban autonomous driving. In this system, radar is used as the primary sensor to achieve uninterrupted tracking over longer distances, and cameras are employed to compensate for the relatively inaccurate measurements of the radar, thereby enhancing tracking accuracy. To achieve robust tracking results in urban environments and from surrounding objects, the track management process utilizes velocity values, unique to radar compared to other sensors, to establish associations between tracklets and measurements. Subsequently, the process selects valid tracklets among those unmatched by considering the relationship between the time of initialization for each tracklet and the current time. To enhance the accuracy of tracking results, the state update process integrates radar and camera information using a decentralized Kalman filter. This filter structure not only improves accuracy but also ensures robustness against sensor failure and real-time performance in the fusion process. The algorithm is implemented in autonomous vehicles equipped with radar and low-cost cameras and is validated on test tracks, as well as in urban and highway environments. The experimental results confirm that the algorithm continuously tracks targets over long distances and demonstrates enhanced tracking results through fusion with the camera, conducting the process efficiently and robustly.</p>", "Keywords": "Autonomous driving; Distant target tracking; Sensor fusion; Vehicle safety", "DOI": "10.1007/s11370-024-00563-0", "PubYear": 2024, "Volume": "17", "Issue": "6", "JournalId": 2152, "JournalTitle": "Intelligent Service Robotics", "ISSN": "1861-2776", "EISSN": "1861-2784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Seoul National University, Seoul, South Korea; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Seoul National University, Seoul, South Korea"}, {"AuthorId": 3, "Name": "Kyongsu Yi", "Affiliation": "Department of Mechanical Engineering, Seoul National University, Seoul, South Korea"}], "References": []}, {"ArticleId": 118339791, "Title": "Gauging Deep Learning Archetypal Effectiveness in Haematological Reclamation", "Abstract": "<p>This paper project an system for reclamation of medical images in an effective way for the extraction and retrieving of data from large archives is critical. This study aims to improve haematological image reclamation by combining visual-based image reclamation (VBIR) approaches with the GLCM algorithm, specifically for characteristics related to the prostate gland. While previous research has focused on computer-aided methods for Gleason grading, our technique focuses on early-stage feature reclamation across 14 paired glands. The experimental findings, including Bicubic Interpolation The resolution Enrichment with a standard deviation of 12.37, kurtosis of 122.83, and skewness of 2.92, demonstrate improved reclamation performance. Our suggested methodology uses a handmade learning approach that includes critical components such as GLCM contrast and correlation. This extensive feature extraction step includes descriptors for morphology, texture, fractals, and contextual information, which are then subjected to in-depth statistical analysis to identify significant features. Using an RNN, our technique improves performance somewhat but significantly when compared to alternatives, with time required for execution offering insights into the intricate interaction of stages and the effect of sample variability. These findings not only encourage more research, but also help to advance standard reviews in haematological analysis of images.</p>", "Keywords": "Haematological; Gray-level co-occurrence matrix (GLCM); Prostate gland; Haematological image; Gland reclamation", "DOI": "10.1007/s42979-024-03322-1", "PubYear": 2024, "Volume": "5", "Issue": "7", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>uma<PERSON>", "Affiliation": "Dept. of Computer Science and Design, Atria Institute of Technology, VTU Karnataka, Bangalore, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, Atria Institute of Technology, VTU Karnataka, Bangalore, India"}], "References": [{"Title": "Design of Chest Visual Based Image Reclamation Method Using Dual Tree Complex Wavelet Transform and Edge Preservation Smoothing Algorithm", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "4", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Design of Cost Efficient VBIR Technique Using ICA and IVCA", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "5", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 118339800, "Title": "Lie Commutator Based Energy Efficient Data Gathering Protocol in WSN for Industrial Automation", "Abstract": "<p>Recent advancements in industrial automation require a dynamic, heterogeneous and fault-tolerant environment established with electro-mechanical and embedded systems. Wireless Sensor Networks (WSNs) helps to form a device network and enable connections to share and store information. Sensor nodes in WSNs are battery-operated, which depletes energy during data transmission, so energy is the major constraint for prolonging the lifetime of the WSN in the industry. This paper introduces a Lie commutator-based Routing (LCR) protocol for energy-efficient data transmission. Initially, LCR clusters the network using K-Medoids with optimum K value and chooses the Cluster Head (CH) based on three parameters: distance to base station (BS), distance to the cluster center and remnant energy. Finally, in the routing phase, Lie algebra of upper triangular matrix is exploited for inter-cluster communication to reach the BS. The simulation results indicate the proposed approach prevails over the other protocols in both homogeneous and heterogeneous network.</p>", "Keywords": "Lie algebra; Commutators; Routing; Wireless sensor network; Upper triangular matrix", "DOI": "10.1007/s42979-024-03301-6", "PubYear": 2024, "Volume": "5", "Issue": "7", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Arts, Sciences, Humanities and Education, SASTRA Deemed to be University, Thanjavur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Arts, Sciences, Humanities and Education, SASTRA Deemed to be University, Thanjavur, India; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Srinivasa Ramanujan Centre, SASTRA Deemed to be University, Kumbakonam, India"}], "References": [{"Title": "HGC: HyperGraph based Clustering scheme for power aware wireless sensor networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "175", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Cluster-tree based energy efficient data gathering protocol for industrial automation using WSNs and IoT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "", "Page": "100156", "JournalTitle": "Journal of Industrial Information Integration"}, {"Title": "GAPSO-H: A hybrid approach towards optimizing the cluster based routing in wireless sensor network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100772", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Enhancement of Energy Efficiency and Network Lifetime Using Modified MPCT Algorithm in Wireless Sensor Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "Supp03", "Page": "", "JournalTitle": "Journal of Interconnection Networks"}, {"Title": "Energy-Efficient Data Aggregation and Cluster-Based Routing in Wireless Sensor Networks Using Tasmanian Fully Recurrent Deep Learning Network with Pelican Variable Marine Predators Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "4", "Page": "", "JournalTitle": "Journal of Interconnection Networks"}, {"Title": "Multi-level trust-based secure and optimal IoT-WSN routing for environmental monitoring applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "8", "Page": "11338", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": *********, "Title": "Flipping into the Future: Exploring Player Experience and Task Demand in Physical, VR, and PC Pinball", "Abstract": "<p>Attempts to digitize pinball have been met with skepticism; however, VR presents a new opportunity to access and enjoy this popular game. In a controlled study (N=60), we investigated how physical pinball, VR pinball, and PC pinball differ in terms of player experience (pX) and task demand using both quantitative and qualitative data. Participants mainly preferred physical pinball, followed by VR pinball; only two participants preferred pinball on the PC. With the exception of immersion, results showed no pX differences between physical and VR pinball; although PC pinball was rated as inferior on multiple dimensions. Furthermore, enjoyment of physical pinball was associated with curiosity, control, and challenge, whereas in VR and PC pinball, immersion and audiovisual appeal mattered. Interestingly, preference and pX did not depend on existing familiarity with pinball. Our findings suggest that VR can offer an accessible, enjoyable pinball experience, regardless of familiarity.</p>", "Keywords": "", "DOI": "10.1145/3677071", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "QUT, Brisbane, QLD, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "QUT, Brisbane, QLD, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Utrecht University, Utrecht, Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Victoria, Victoria, BC, Canada"}], "References": [{"Title": "Development and validation of the player experience inventory: A scale to measure player experiences at the level of functional and psychosocial consequences", "Authors": "<PERSON><PERSON>den <PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "102370", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": *********, "Title": "Deep Learning-Assisted Compound Bioactivity Estimation Framework", "Abstract": "Drug Discovery is a highly complicated process. On average, it takes six to twelve years to manufacture a new drug and have the product released in the market. It is of utmost importance to find methods that would accelerate the manufacturing process. This significant challenge in drug development can be addressed using deep learning techniques. The aim of this paper is to propose a deep learning-based framework that can help chemists examine compound biological activity in a more accurate manner. The proposed framework employs autoencoder for data representation of the compounds data, which is then classified using deep neural network followed by building a customized deep regression model to estimate an accurate value of the compound bioactivity. The proposed framework achieved an accuracy of 89% in autoencoder reconstruction error, 79.01% in classification, and MAE of 2.4 while predicting compound bioactivity using deep regression model.", "Keywords": "Drug discovery; Deep learning; Regression; Auto-encoder; Classification", "DOI": "10.1016/j.eij.2024.100558", "PubYear": 2024, "Volume": "28", "Issue": "", "JournalId": 5980, "JournalTitle": "Egyptian Informatics Journal", "ISSN": "1110-8665", "EISSN": "2090-4754", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science, October University for Modern Sciences and Arts, Cairo, Egypt;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems Department, Faculty of Computers and Artificial Intelligence, Cairo University, Cairo, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology Department, Faculty of Computers and Artificial Intelligence, Beni-Suef University, Beni-Suef, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> M<PERSON>", "Affiliation": "Pharmaceutical chemistry department faculty of pharmacy Cairo University, Cairo, Egypt;Pharmaceutical chemistry department faculty of pharmacy MSA University, Cairo, Egypt"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems Department, Faculty of Computers and Artificial Intelligence, Cairo University, Cairo, Egypt"}], "References": [{"Title": "Improving head pose estimation using two-stage ensembles with top-k regression", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "103827", "JournalTitle": "Image and Vision Computing"}, {"Title": "Head pose estimation by regression algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "179", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 118339874, "Title": "Breaking Barriers in Mobile Game Development", "Abstract": "<p>Mobile games occupy more market share than PC and console titles combined, while the designers responsible for delighting billions with their app store offerings remain relatively overlooked by avid players and academic researchers alike. Marginalized game developers in particular find themselves struggling to survive in the mammoth mobile marketplace, but few studies have yet to address their unique experiences. In this paper we present the results from a participatory research project to: discover the challenges that women, people of color, LGBTQIA+, and disabled game creators face in the mobile space; and develop actionable methods for dismantling those barriers to meaningful participation in smartphone play design. After summarizing the three most prominent hurdles underrepresented developers contend with, we then lay out ten key insights about how those impediments can be overcome. The piece concludes with a dozen brief proposals for concrete programs to implement a more representative and welcoming mobile gaming environment, and a call to action for us all to take part in building that better world.</p>", "Keywords": "", "DOI": "10.1145/3677061", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Smith College, Northampton, MA, USA"}, {"AuthorId": 2, "Name": "Morgan Romine", "Affiliation": "AnyKey, Santa Ana, CA, USA"}], "References": [{"Title": "A Seat at the Table", "Authors": "<PERSON><PERSON><PERSON>; India Irish", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Video Game Bad Smells: What They Are and How Developers Perceive Them", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Everywhere but Nowhere: Development Experiences of the International Game Developers in Finland during the Covid-19 Pandemic and Remote Work", "Authors": "Solip Park; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CHI PLAY", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Surveying the Effects of Remote Communication & Collaboration Practices on Game Developers Amid a Pandemic", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "4", "Page": "5", "JournalTitle": "Communication Design Quarterly"}]}, {"ArticleId": 118339877, "Title": "Mod Installation as Reclaiming Representational Diversity: The Role of Aesthetic Agency in Stardew Valley", "Abstract": "<p>Media (e.g., film) are often designed around cisgender, heternormative logics and are not representative of the multiplicity of identities and identity expressions that exist in the world. Thus, media impedes the identity expression and performance of the communities who use them, as we do not see many parts of our identities represented or find that our identities are misrepresented. Video games, which are a genre of media, are especially problematic in how they perpetuate heteronormative normative logics. Unlike most media, however, video games give people agency over the aesthetics of game worlds--the characters and narratives--through the installation of mods. We conducted a content analysis coupled with a triangulation of mods through personal experience focused on the widely popular farming simulation game Stardew Valley. Drawing on a conceptual lens bringing together theories of identity and aesthetic agency, we highlight how the aesthetic agency afforded by mod installation provides opportunities for reclaiming representational diversity in game worlds along the following dimensions: sex and intimacy, gender and sexuality, race and ethnicity, and body diversity. We discuss the implications of mod installation, exploring its potential relationship with identity work and identity play. We then propose design guidelines for achieving aesthetic alignment.</p>", "Keywords": "", "DOI": "10.1145/3677089", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Information Science, University of Colorado Boulder, Boulder, Colorado, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. of Information Science, University of Colorado Boulder, Boulder, Colorado, USA"}], "References": [{"Title": "Playing farmer: At the intersections of neo-liberal capitalism and ecocriticism in Stardew Valley", "Authors": "Sydney Crowley", "PubYear": 2023, "Volume": "15", "Issue": "1", "Page": "21", "JournalTitle": "Journal of Gaming & Virtual Worlds"}]}, {"ArticleId": 118339914, "Title": "Learning from Users: Everyday Playful Interactions to Support Architectural Spatial Changes", "Abstract": "<p>While there's growing interest in eliciting situated playful interactions with technologies in different contexts, how these interactions might shape everyday spaces still needs to be fully explored. To address this gap, this article aims to guide the architectural spatial changes by exploring everyday playful interactions of technology-adopted users in domestic spaces. We present our contributions in a two-fold study: First, through an extensive diary study involving 13 technology-adopted residents in gated communities, where distinct boundaries offer increased opportunities for playful interactions, we identified four playful themes: (1) creating and expanding play-spaces, (2) balancing play and comfort, (3) intertwining imagination with spatial experience, and (4) gamifying household interactions. Secondly, by building on these themes, we outline three design implications to inform architectural design processes, aiming to translate everyday playful interactions into tangible spatial changes. These implications include adapting shape-changing and wearable technologies for playful flexibility, embedding new forms of communications within infrastructures, and turning homes into interactive entities with multi-sensory technologies. While these findings provide a starting point for exploring new architectural design possibilities in similar environments, further research with architects, policymakers, and design researchers in the Human-Computer Interaction (HCI) and Human-Building Interaction (HBI) fields is essential to actualizing these changes.</p>", "Keywords": "", "DOI": "10.1145/3677085", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "Cansu Çetin Er", "Affiliation": "Arçelik Research Center for Creative Industries (KUAR), Koç University, Istanbul, Turkey"}, {"AuthorId": 2, "Name": "Oguzhan Özcan", "Affiliation": "Arçelik Research Center for Creative Industries (KUAR), Koç University, Istanbul, Turkey"}], "References": [{"Title": "Filtering and Informing the Design Space", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Touching Our Breathing through Shape-Change: Monster, Organic Other, or Twisted Mirror", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}]}, {"ArticleId": 118339931, "Title": "\"What a stupid way to do business\": Towards an Understanding of Older Adults' Perceptions of Deceptive Patterns and Ways to Develop Resistance", "Abstract": "<p>There are growing efforts to reduce the harmful effects of deceptive patterns pervasively employed on e-commerce websites. However, efforts to produce new guidelines and introduce ethical design standards geared towards older adults have been limited. We investigate the potential of a serious game in fostering older adults' resilience against manipulative designs in e-commerce through two studies. First, a survey with older adults (N = 61), explored their attitudes towards deceptive patterns and identified characteristics influencing them. We then created a serious game, 'Shopopolis', to bolster older adults' resistance to manipulative designs online and evaluated its efficacy with older adults (N = 65). Our findings show that Shopopolis is a valuable tool for enhancing awareness, concern, and recognition skills related to e-commerce deceptive patterns. We discuss older adults' unique perspectives on deceptive patterns and consider how insights can shape the design of targeted protective measures like Shopopolis for older adults in e-commerce contexts.</p>", "Keywords": "", "DOI": "10.1145/3677113", "PubYear": 2024, "Volume": "8", "Issue": "CHI PLAY", "JournalId": 41192, "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction", "ISSN": "", "EISSN": "2573-0142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UCL Interaction Centre, University College London, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UCL Interaction Centre, University College London, London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UCL Interaction Centre, University College London, London, United Kingdom"}], "References": [{"Title": "Multiple Purposes, Multiple Problems: A User Study of Consent Dialogs after GDPR", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "2", "Page": "481", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "End User Accounts of Dark Patterns as Felt Manipulation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "A Comparative Study of Dark Patterns Across Web and Mobile Modalities", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Toward Proactive Support for Older Adults", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Cookie monster", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "7", "Page": "30", "JournalTitle": "Communications of the ACM"}, {"Title": "Understanding Account Deletion and Relevant <PERSON> Patterns on Social Media", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": *********, "Title": "A nonlinear and asymmetric monostable compliant ortho-planar spring piezoelectric vibrational energy harvester using a H-I structure", "Abstract": "This paper proposes a compliant ortho-planar spring piezoelectric vibrational energy harvester (COPS-PVEH) using a H-I structure that exploits a nonlinearity and an asymmetric mono-stability to enhance the bandwidth of the device. The main structure is based on a double clamped beam (I-structure) coupled with four cantilever beams (H-structure) and an ortho-planar spring in the centre of the I-structure. Finite element analysis (FEA), analytical modelling, and experimentation were performed to analyse the dynamical and electrical behaviour of the harvester. The device was fabricated using polylactide (PLA). Six bimorph PZT-5H piezoelectric materials, electrically connected in parallel, were used as piezoelectric generators. The device was tested using an optimum load resistor of 90 kΩ under sinusoidal and sine sweep excitation in the gravity (vertical) direction. The pre-load (due to gravitation) causes a softening nonlinearity and an asymmetric mono-stability in the potential energy function. The results show that multiple peaks are observed in the output voltage of the harvester in the FEA, analytical modelling, and experimentation under harmonic excitation in the sub 15 Hz frequency range. Furthermore, analytical modelling and experimentation exhibit softening nonlinearity and chaotic behaviour, reaching a maximum amplitude power of 1.01 mW (at 8.3 Hz) and 1.07 mW (at 9.5 Hz) respectively under sine sweep excitation (amplitude of 0.6 g (g = 9.81 m/s<sup>2</sup>)). Experimentally, the harvester also generates an average power greater than 46 µW with a 6.8 Hz bandwidth under the same excitation. The softening nonlinearity and asymmetric mono-stability enhance the bandwidth of the harvester in the low frequency region where most broadband ambient vibrations are available in practical environments.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115984", "PubYear": 2024, "Volume": "379", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CONNECT, Stokes Laboratories, Bernal Institute, University of Limerick, Limerick, Ireland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CONNECT, Stokes Laboratories, Bernal Institute, University of Limerick, Limerick, Ireland"}, {"AuthorId": 3, "Name": "Vale<PERSON> Nico", "Affiliation": "CONNECT, Stokes Laboratories, Bernal Institute, University of Limerick, Limerick, Ireland"}], "References": [{"Title": "Modeling and design of V-shaped piezoelectric vibration energy harvester with stopper for low-frequency broadband and shock excitation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "317", "Issue": "", "Page": "112458", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "A review on vibration-based piezoelectric energy harvesting from the aspect of compliant mechanisms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "331", "Issue": "", "Page": "112743", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 118340079, "Title": "Change pattern relationships in event logs", "Abstract": "Process mining utilises process execution data to discover and analyse business processes. Event logs represent process executions, providing information about the activities executed. In addition to generic event attributes like activity name and timestamp, events might contain domain-specific attributes, such as a blood sugar measurement in a healthcare environment. Many of these values change during a typical process quite frequently. We refer to those as dynamic event attributes. Change patterns can be derived from dynamic event attributes, describing if the attribute values change from one activity to another. So far, change patterns can only be identified in an isolated manner, neglecting the chance of finding co-occuring change patterns. This paper provides an approach to identifying relationships between change patterns by utilising correlation methods from statistics. We applied the proposed technique on two event logs derived from the MIMIC-IV real-world dataset on hospitalisations in the US and evaluated the results with a medical expert. It turns out that relationships between change patterns can be detected within the same directly or eventually follows relation and even beyond that. Further, we identify unexpected relationships that are occurring only at certain parts of the process. Thus, the process perspective reveals novel insights on how dynamic event attributes change together during process execution. The approach is implemented in Python using the PM4Py framework.", "Keywords": "", "DOI": "10.1016/j.datak.2024.102368", "PubYear": 2024, "Volume": "154", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>, University of Potsdam, Prof.-Dr.-Helmert-Str. 2-3, Potsdam, 14482, Brandenburg, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, University of Potsdam, Prof.-Dr.-Helmert-Str. 2-3, Potsdam, 14482, Brandenburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>, University of Potsdam, Prof.-Dr.-Helmert-Str. 2-3, Potsdam, 14482, Brandenburg, Germany"}], "References": [{"Title": "An extended model for remaining time prediction in manufacturing systems using process mining", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "188", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Integrated detection and localization of concept drifts in process mining with batch and stream trace clustering support", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "149", "Issue": "", "Page": "102253", "JournalTitle": "Data & Knowledge Engineering"}]}, {"ArticleId": 118340296, "Title": "HEFS-MLDR: A novel hybrid ensemble feature selection framework for improved deep neural network architecture in the diagnosis of Parkinson’s disease", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-20276-x", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Abderazzak <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A deep learning approach for Parkinson’s disease diagnosis from EEG signals", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "10927", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Diagnosis of Parkinson’s disease using deep CNN with transfer learning and data augmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "7", "Page": "10113", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Parkinson’s diagnosis hybrid system based on deep learning classification with imbalanced dataset", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "3", "Page": "3204", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "An improved ensembling techniques for prediction of breast cancer tissues", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "11", "Page": "31975", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 118340324, "Title": "Cosserat modeling for deformation configuration of shape memory alloy unimorph actuators", "Abstract": "<p>Shape memory alloys (SMAs) can contract their length via a crystalline phase transition that is dependent upon their temperature and stress state. SMAs have been used as linear micro-actuators due to their high strength to weight ratio and compact structure. However, the relatively low linear contraction ([Formula: see text]4%–5% in length) limits their use. To remedy this, the SMA can be offset from a passive structure, which acts to magnify the deformation. The resulting amount of deformation depends upon the material properties and geometry of both the SMA and the passive structure. In this work, geometrically exact beam theory (also known as Cosserat theory) is coupled with SMA constitutive relations to model the maximum deformation configuration of these actuators. Four of these actuators of various lengths were fabricated and tested to verify the model. For the four actuators tested, the mean squared error between the experimental results and the Cosserat model were between 0.0702 mm (0.1% error) for the shortest actuator (66 mm in length) and 3.59 mm (2.7% error) for the longest actuator (135 mm in length). These results show that the closed form solution derived for this Cosserat beam model can accurately model the deformation of these active structures.</p>", "Keywords": "", "DOI": "10.1177/1045389X221109256", "PubYear": 2023, "Volume": "34", "Issue": "6", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LAB2701, Mechanical & Aerospace Engineering, North Carolina State University, Raleigh, NC, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Applied Research Laboratory, The Pennsylvania State University, University Park, PA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "LAB2701, Mechanical & Aerospace Engineering, North Carolina State University, Raleigh, NC, USA"}], "References": [{"Title": "Least-squares method for laminated beams with distributed braided piezoelectric composite actuators", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "18", "Page": "2165", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}, {"Title": "A Hermite interpolation element-free Galerkin method for piezoelectric materials", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "14", "Page": "1802", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}]}, {"ArticleId": 118340512, "Title": "Hesse-Matrix-basierte Qualitätsmanagementsysteme für die Fertigungsindustrie", "Abstract": "Zusammenfassung <p>Die Qualitätssicherung ist ein überaus zentrales Thema in der Fertigungsindustrie, da sie unmittelbar mit der Produktqualität und der Kundenzufriedenheit zusammenhängt. Fortschritte in Algorithmen und modernen Kommunikationstechnologien im Kontext von Industrie 4.0 haben dazu beigetragen, dass traditionelle Fertigungsindustrien Deep-Learning-Modelle zur Kontrolle der Produktionsqualität einsetzen. Allerdings stellen industrielle Anwendungen hohe Anforderungen an die Effizienz von Algorithmen. Zudem fehlen in praktischen Anwendungen häufig umfangreiche, gelabelte Daten für das Training von Deep-Learning-Modellen. Um diesen Herausforderungen zu begegnen, haben wir in diesem Artikel ein auf maschinellem Lernen basierendes Modell zur Qualitätserkennung entwickelt. Unser Modell nutzt eine effizientere Hesse-Matrix-Erkennungsmethode, um direkt die lokalen Maxima im Skalenraum des Eingangsbildes zu identifizieren, ohne zahlreiche Gauss-Differenzbilder berechnen zu müssen. Darüber hinaus wenden wir Methoden der Bildverarbeitung an, um die Trainingsdaten zu erweitern, sodass das Modell auch bei geringen Trainingsdatenmengen eine hohe Genauigkeit erreicht. Unsere experimentellen Ergebnisse zeigen, dass das vorgeschlagene Modell die höchste Genauigkeit und Effizienz im Vergleich zu gängige Methoden aufweist. Abschließend haben wir in diesem Artikel auch eine benutzerfreundliche Schnittstelle für unser Modell erstellt und dieses in das elektronische Kanban der Werkstatt integriert. Unsere empirischen Studien haben ergeben, dass die entwickelten Systeme in der industriellen Praxis anwendbar sind und die Fehlerquote senken sowie die Produktqualität erhöhen können.</p>", "Keywords": "", "DOI": "10.1515/auto-2024-0004", "PubYear": 2025, "Volume": "73", "Issue": "1", "JournalId": 16752, "JournalTitle": "at - Automatisierungstechnik", "ISSN": "0178-2312", "EISSN": "2196-677X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering , Tsinghua University , Beijing 100084 , P.R. China;Institut für Informationsmanagement im Ingenieurwesen (IMI), Karlsruhe Institute of Technology , 76133 Karlsruhe , Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering , Tsinghua University , Beijing 100084 , P.R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institut für Informationsmanagement im Ingenieurwesen (IMI), Karlsruhe Institute of Technology , 76133 Karlsruhe , Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Siemens AG , Berlin , German"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Siemens AG , Berlin , German"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut für Informationsmanagement im Ingenieurwesen (IMI), Karlsruhe Institute of Technology , 76133 Karlsruhe , Germany"}], "References": [{"Title": "Online inspection of narrow overlap weld quality using two-stage convolution neural network image recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}]}, {"ArticleId": 118340514, "Title": "Porosity control and properties improvement of Al-Cu alloys via solidification condition optimisation in wire and arc additive manufacturing", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2024.2414408", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Solidification Processing, Northwestern Polytechnical University, Xi'an, People’s Republic of China;MIIT Key Laboratory of Metal High Performance Additive Manufacturing and Innovative Design, Northwestern Polytechnical University, Xi'an, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Solidification Processing, Northwestern Polytechnical University, Xi'an, People’s Republic of China;MIIT Key Laboratory of Metal High Performance Additive Manufacturing and Innovative Design, Northwestern Polytechnical University, Xi'an, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Solidification Processing, Northwestern Polytechnical University, Xi'an, People’s Republic of China;MIIT Key Laboratory of Metal High Performance Additive Manufacturing and Innovative Design, Northwestern Polytechnical University, Xi'an, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Solidification Processing, Northwestern Polytechnical University, Xi'an, People’s Republic of China;MIIT Key Laboratory of Metal High Performance Additive Manufacturing and Innovative Design, Northwestern Polytechnical University, Xi'an, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Luoyang Ship Material Research Institute, Luoyang, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Solidification Processing, Northwestern Polytechnical University, Xi'an, People’s Republic of China;MIIT Key Laboratory of Metal High Performance Additive Manufacturing and Innovative Design, Northwestern Polytechnical University, Xi'an, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Solidification Processing, Northwestern Polytechnical University, Xi'an, People’s Republic of China;MIIT Key Laboratory of Metal High Performance Additive Manufacturing and Innovative Design, Northwestern Polytechnical University, Xi'an, People’s Republic of China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Luoyang Ship Material Research Institute, Luoyang, People’s Republic of China"}], "References": [{"Title": "Simulation-assisted investigation on the formation of layer bands and the microstructural evolution in directed energy deposition of Ti6Al4V blocks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "4", "Page": "387", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Mitigation of residual stresses and microstructure homogenization in directed energy deposition processes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "6", "Page": "4771", "JournalTitle": "Engineering with Computers"}, {"Title": "Influence of scale effect on surface morphology in laser powder bed fusion technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "19", "Issue": "1", "Page": "", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "Effect of heat treatment on the microstructure and mechanical properties of Mg-Gd-Y-Zr alloys fabricated by wire arc additive manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "19", "Issue": "1", "Page": "", "JournalTitle": "Virtual and Physical Prototyping"}]}, {"ArticleId": 118340927, "Title": "Perspective on complexity measures targeting read-once branching programs", "Abstract": "A model of computation for which reasonable yet still incomplete lower bounds are known is the read-once branching program. Here variants of complexity measures successful in the study of read-once branching programs are defined and studied. Some new or simpler proofs of known bounds are uncovered. Branching program resources and the new measures are compared extensively. The new variants are developed in part in the hope of tackling read- k branching programs for the tree evaluation problem. Other computation problems are studied as well. In particular, a common view of a function studied by <PERSON><PERSON><PERSON> and a function studied by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> leads to the general combinatorics of blocking sets. Technical combinatorial results of independent interest are obtained. New leads towards further progress are discussed. An exponential lower bound for non-deterministic read- k branching programs for the GEN function is also derived, independently from the new measures.", "Keywords": "", "DOI": "10.1016/j.ic.2024.105230", "PubYear": 2024, "Volume": "301", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Control Engineering, Shenzhen University of Advanced Technology, Shenzhen, 518107, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université de Montréal, Montréal, H3T 1J4, Canada"}], "References": []}, {"ArticleId": 118341032, "Title": "Data on the crystal structures of β-glucosidase from Thermoanaerobacterium saccharolyticum", "Abstract": "β-Glucosidase (Bgl) is a biomass-degrading enzyme that hydrolyzes cellobiose and glucose-substituted polysaccharides into glucose, playing a crucial role in enzymatic saccharification during biofuel production. Despite the wealth of structural information available on Bgl, the molecular properties of the loops above the substrate-binding pocket remain unexplored. In previous study, to better understand the molecular functions of these loop regions, four crystal structures of Thermoanaerobacterium saccharolyticum Bgl (TsaBgl) were determined. The molecular flexibility and conformational changes of the loop regions in TsaBgl were analysed, expanding our understanding of their roles in the Bgl family. The data processing and structure determination details provided here are valuable for further studies on the structural properties of these loop regions.", "Keywords": "β-glucosidase (Bgl); Loop structure; Structural dynamics; Temperature factor", "DOI": "10.1016/j.dib.2024.111019", "PubYear": 2024, "Volume": "57", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of General Education, Kookmin University, Seoul 02707, Republic of Korea"}], "References": []}, {"ArticleId": 118341165, "Title": "Controlling gene-expression variability via sequestration-based feedbacks", "Abstract": "Expressed Transcription Factors (TFs) not only bind to sites at target promoters but also to decoy sites scattered across the genome. Binding to such “decoys” sequesters TFs critically impacting the response time and stochasticity (noise) in TF and target gene expression level. When the TF is a stable molecule, whose concentration is diluted by cellular growth, our results show that for fixed mean concentration levels, such decoy bindings can both enhance or suppress random fluctuations in TF levels depending on the source of noise (i.e., intrinsic vs. extrinsic noise) and the strength of binding (i.e., weak vs. strong decoys). We implement negative autoregulation where free (unbound) TF molecules inhibit their synthesis. Our analytical results corroborated by numerical simulations reveal that sequestration accentuates the effects of feedback in the sense that noise attenuation by negative feedback is higher with sequestration than in the absence of feedback. We next consider an alternative form of feedback where the TF increases the production of its decoys, and such feedback architectures are frequently seen in endogenous gene regulation involving microRNA-TF circuits and in controlling cellular stress responses. For these circuits where decoy numbers are TF-regulated, we identify limits of noise suppression, and in many cases, these limits occur at intermediate TF-decoy binding affinities.", "Keywords": "Stochastic gene expression; decoys; feedback regulation; transcription factors", "DOI": "10.1016/j.ifacol.2024.10.003", "PubYear": 2024, "Volume": "58", "Issue": "23", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, and Department of Computer Science and Engineering, SRM University-AP, Amaravati, Andhra Pradesh Department of Physics, SRM University - AP, Amaravati, Andhra Pradesh 522240, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Corporación Colombiana de Investigación Agropecuaria - AGROSAVIA, Mosquera, Colombia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Departments of Electrical and Computer Engineering, Biomedical Engineering at the University of Delaware, Newark, DE 19716, USA"}], "References": []}, {"ArticleId": 118341226, "Title": "High-speed turbulent flows towards the exascale: STREAmS-2 porting and performance", "Abstract": "Exascale High Performance Computing (HPC) represents a tremendous opportunity to push the boundaries of Computational Fluid Dynamics (CFD), but despite the consolidated trend towards the use of Graphics Processing Units (GPUs), programmability is still an issue. STREAmS-2 (<PERSON><PERSON> et al. Comput. Phys. Commun. 285 (2023) 108644) is a compressible solver for canonical wall-bounded turbulent flows capable of harvesting the potential of NVIDIA GPUs. Here we extend the already available CUDA Fortran backend with a novel HIP backend targeting AMD GPU architectures. The main implementation strategies are discussed along with a novel Python tool that can generate the HIP and CPU code versions allowing developers to focus their attention only on the CUDA Fortran backend. Single GPU performance is analyzed focusing on NVIDIA A100 and AMD MI250x cards which are currently at the core of several HPC clusters. The gap between peak GPU performance and STREAmS-2 performance is found to be generally smaller for NVIDIA cards. Roofline analysis allows tracing this behavior to unexpectedly different computational intensities of the same kernel using the two cards. Additional single-GPU comparisons are performed to assess the impact of grid size, number of parallelized loops, thread masking and thread divergence. Parallel performance is measured on the two largest EuroHPC pre-exascale systems, LUMI (AMD GPUs) and Leonardo (NVIDIA GPUs). Strong scalability reveals more than 80% efficiency up to 16 nodes for Leonardo and up to 32 for LUMI. Weak scalability shows an impressive efficiency of over 95% up to the maximum number of nodes tested (256 for LUMI and 512 for Leonardo). This analysis shows that STREAmS-2 is the perfect candidate to fully exploit the power of current pre-exascale HPC systems in Europe, allowing users to simulate flows with over a trillion mesh points, thus reducing the gap between the Reynolds numbers achievable in high-fidelity simulations and those of real engineering applications.", "Keywords": "GPU; CUDA Fortran; HIP; HIPFort; Direct numerical simulation; Compressible flow; Wall turbulence", "DOI": "10.1016/j.jpdc.2024.104993", "PubYear": 2025, "Volume": "196", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, Sapienza University of Rome, via Eudossiana 18, 00184 Rome, Italy;Max Planck Computing and Data Facility, Gießenbachstraße 2, 85748 Garching, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, Sapienza University of Rome, via Eudossiana 18, 00184 Rome, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Aerodynamics Group, Faculty of Aerospace Engineering, Delft University of Technology, Kluyverweg 2, 2629 HS Delft, the Netherlands;Gran Sasso Science Institute, Viale Francesco Crispi 7, 67100 L'Aquila, Italy;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, Sapienza University of Rome, via Eudossiana 18, 00184 Rome, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "HPC Department, Cineca, Rome office, via dei Tizii 6/B, Rome, Italy"}], "References": [{"Title": "Hierarchical Roofline analysis for GPUs: Accelerating performance optimization for the NERSC‐9 Perlmutter system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "ZEFR: A GPU-accelerated high-order solver for compressible viscous flows using the flux reconstruction method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "250", "Issue": "", "Page": "107169", "JournalTitle": "Computer Physics Communications"}, {"Title": "STREAmS: A high-fidelity accelerated solver for direct numerical simulation of compressible turbulent flows", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "263", "Issue": "", "Page": "107906", "JournalTitle": "Computer Physics Communications"}, {"Title": "STREAmS-2.0: Supersonic turbulent accelerated Navier-Stokes solver version 2.0", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "285", "Issue": "", "Page": "108644", "JournalTitle": "Computer Physics Communications"}, {"Title": "URANOS: A GPU accelerated Navier-Stokes solver for compressible wall-bounded flows", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "287", "Issue": "", "Page": "108717", "JournalTitle": "Computer Physics Communications"}, {"Title": "OpenMP offload toward the exascale using Intel® GPU Max 1550: evaluation of STREAmS compressible solver", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "14", "Page": "21094", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 118341328, "Title": "Strategic safeguarding: A game theoretic approach for analyzing attacker-defender behavior in DNN backdoors", "Abstract": "Deep neural networks (DNNs) are fundamental to modern applications like face recognition and autonomous driving. However, their security is a significant concern due to various integrity risks, such as backdoor attacks. In these attacks, compromised training data introduce malicious behaviors into the DNN, which can be exploited during inference or deployment. This paper presents a novel game-theoretic approach to model the interactions between an attacker and a defender in the context of a DNN backdoor attack. The contribution of this approach is multifaceted. First, it models the interaction between the attacker and the defender using a game-theoretic framework. Second, it designs a utility function that captures the objectives of both parties, integrating clean data accuracy and attack success rate. Third, it reduces the game model to a two-player zero-sum game, allowing for the identification of Nash equilibrium points through linear programming and a thorough analysis of equilibrium strategies. Additionally, the framework provides varying levels of flexibility regarding the control afforded to each player, thereby representing a range of real-world scenarios. Through extensive numerical simulations, the paper demonstrates the validity of the proposed framework and identifies insightful equilibrium points that guide both players in following their optimal strategies under different assumptions. The results indicate that fully using attack or defense capabilities is not always the optimal strategy for either party. Instead, attackers must balance inducing errors and minimizing the information conveyed to the defender, while defenders should focus on minimizing attack risks while preserving benign sample performance. These findings underscore the effectiveness and versatility of the proposed approach, showcasing optimal strategies across different game scenarios and highlighting its potential to enhance DNN security against backdoor attacks.", "Keywords": "Signal; Image and Speech Processing;Systems and Data Security;Communications Engineering; Networks;Security Science and Technology", "DOI": "10.1186/s13635-024-00180-5", "PubYear": 2024, "Volume": "2024", "Issue": "1", "JournalId": 43752, "JournalTitle": "EURASIP Journal on Information Security", "ISSN": "", "EISSN": "2510-523X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut National de la Santé et de la Recherche Médicale (INSERM), Brest, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Thales Digital Identity & Security (DIS), La Ciotat, France;Institut National de Recherche en Informatique et en Automatique (INRIA), Rennes, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology Innovation Institute (TII), Masdar City, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institut National de Recherche en Informatique et en Automatique (INRIA), Rennes, France"}], "References": [{"Title": "A survey of deep learning techniques for autonomous driving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "3", "Page": "362", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Backdoor Attacks Against Transfer Learning With Pre-Trained Deep Learning Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "1526", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 118341439, "Title": "The comparison between single-point method and footprint-integrated validation method of the remote-sensing retrieval of evapotranspiration: a case study at Daman site", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2024.2412803", "PubYear": 2025, "Volume": "46", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 2, "Name": "Xin Pan", "Affiliation": "College of Geography and Remote Sensing, Hohai University, Nanjing, China;Key Laboratory of Soil and Water Processes in Watershed, Hohai University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 9, "Name": "Wenqing Ma", "Affiliation": "School of Earth Sciences and Engineering, Hohai University, Nanjing, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "College of Geography and Remote Sensing, Hohai University, Nanjing, China;Key Laboratory of Soil and Water Processes in Watershed, Hohai University, Nanjing, China"}], "References": [{"Title": "Improved global evapotranspiration estimates using proportionality hypothesis-based water balance constraints", "Authors": "Jianyu <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "279", "Issue": "", "Page": "113140", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 118341447, "Title": "Research on Remote Sensing Image Target Recognition and Image Change Detection Algorithm Based on Deep Learning", "Abstract": "", "Keywords": "", "DOI": "10.18178/ijfcc.2024.13.4.619", "PubYear": 2024, "Volume": "13", "Issue": "4", "JournalId": 14565, "JournalTitle": "International Journal of Future Computer and Communication", "ISSN": "", "EISSN": "2010-3751", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Zhejiang College of Security Technology, Wenzhou, China"}], "References": []}, {"ArticleId": 118341528, "Title": "A whole-body mathematical model of cholesterol metabolism and transport", "Abstract": "Cardiovascular diseases are the leading cause of death. Increased levels of plasma cholesterol are consistently associated with an increased risk of cardiovascular disease. As a result, it is imperative that studies are conducted to determine the best course of action to reduce whole-body cholesterol levels. A whole-body mathematical model for cholesterol metabolism and transport is proposed. The model can simulate the effects of lipid-lowering drugs like statins and anti-PCSK9. The model is based on ordinary differential equations and kinetic functions. It has been validated against literature data. It offers a versatile platform for designing personalized interventions for cardiovascular health management.", "Keywords": "Mathematical modeling; Metabolism; Systems biology; Multi-scale modeling; Quantitative Systems Pharmacology (QSP); Cholesterol; Cardiovascular disease; Obesity", "DOI": "10.1016/j.ifacol.2024.10.015", "PubYear": 2024, "Volume": "58", "Issue": "23", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Department of Applied Mathematics and Computer Science, DK-2800 Kgs. Lyngby, Denmark;Novo Nordisk A/S, DK-2880 Bagsværd, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Department of Applied Mathematics and Computer Science, DK-2800 Kgs. Lyngby, Denmark;Novo Nordisk A/S, DK-2880 Bagsværd, Denmark"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Department of Applied Mathematics and Computer Science, DK-2800 Kgs. Lyngby, Denmark;Novo Nordisk A/S, DK-2880 Bagsværd, Denmark"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Novo Nordisk A/S, DK-2880 Bagsværd, Denmark"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Department of Applied Mathematics and Computer Science, DK-2800 Kgs. Lyngby, Denmark"}], "References": [{"Title": "A whole-body multi-scale mathematical model for dynamic simulation of the metabolism in man", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "23", "Page": "58", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 118341534, "Title": "Low-dimensional representations of genome-scale metabolism", "Abstract": "Cellular metabolism is a highly interconnected network with thousands of reactions that convert nutrients into the molecular building blocks of life. Metabolic connectivity varies greatly with cellular context and environmental conditions, and it remains a challenge to compare genome-scale metabolism across cell types because of the high dimensionality of the reaction flux space. Here, we employ self-supervised learning and genome-scale metabolic models to compress the flux space into low-dimensional representations that preserve structure across cell types. We trained variational autoencoders (VAEs) on large fluxomic data (N = 800, 000) sampled from patient-derived models for various cancer cell types. The VAE embeddings have an improved ability to distinguish cell types than the uncompressed fluxomic data, and sufficient predictive power to classify cell types with high accuracy. We tested the ability of these classifiers to assign cell type identities to unlabelled patient-derived metabolic models not employed during VAE training. We further employed the pre-trained VAE to embed another 38 cell types and trained multilabel classifiers that display promising generalization performance. Our approach distils the metabolic space into a semantically rich vector that can be used as a foundation for predictive modelling, clustering or comparing metabolic capabilities across organisms.", "Keywords": "Variational autoencoders; deep learning; genome-scale metabolic models", "DOI": "10.1016/j.ifacol.2024.10.011", "PubYear": 2024, "Volume": "58", "Issue": "23", "JournalId": 962, "JournalTitle": "IFAC-PapersOnLine", "ISSN": "2405-8963", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Informatics, The University of Edinburgh, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Informatics, The University of Edinburgh, UK"}, {"AuthorId": 3, "Name": "Diego <PERSON>", "Affiliation": "School of Informatics, The University of Edinburgh, UK;School of Biological Sciences, The University of Edinburgh, UK"}], "References": [{"Title": "Integrating genome-scale metabolic modelling and transfer learning for human gene regulatory network reconstruction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "487", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 118341671, "Title": "A novel effective key synchronization approach based on optimized deep neural networks for IoT-based low-power wide area networks", "Abstract": "<p>Low-Power Wide Area Networks (LPWANs) represent a category of wireless technologies hailed for their efficiency in facilitating communication for Internet of Things (IoT) applications. This efficacy is attributed to their characteristics of low power consumption, extensive wireless transmission range, and cost-effectiveness. Despite these notable advantages, LPWANs exhibit drawbacks such as limited processing power, modest transmission rates, and notably constrained payload sizes, posing challenges for encryption techniques. The inadequacy of existing cipher-chaining encryption methods for LPWANs is underscored by their dependency on high computing power and payload capacity. To address this issue, this paper introduces an innovative chaining encryption approach tailored for LPWAN IoT technology. The proposed method incorporates a key synchronization mechanism based on deep learning algorithms. The effectiveness of this approach has been rigorously assessed through case studies and experiments. The experimental results demonstrate the commendable performance of the proposed approach: i) The proposed method achieves an average effort of 1.0202 for key synchronization after 10 lost packets, compared to 1.0215 for the best existing method; ii) It attains a 98.45% success rate in key synchronization after 10 lost packets, while the best existing method achieves 98.19%; iii) The proposed approach ensures 98.27% accuracy in receiving messages correctly under conditions of completely random data receipt, compared to 98.22% for the best existing method. These results establish the proposed method as a highly competitive solution among encryption approaches for LPWANs.</p>", "Keywords": "Low-power wide area network (LPWAN); Internet of things (IoT); Key synchronization", "DOI": "10.1007/s11227-024-06571-2", "PubYear": 2025, "Volume": "81", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Yasouj University, Yasouj, Iran; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Yasouj University, Yasouj, Iran; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering, Firat University, Elazig, Türkiye"}], "References": [{"Title": "A combined network control approach for the edge cloud and LPWAN‐based IoT services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "LPWAN Technologies: Emerging Application Characteristics, Requirements, and Design Considerations", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "3", "Page": "46", "JournalTitle": "Future Internet"}, {"Title": "Cipher chaining key re-synchronization in LPWAN IoT network using a deep learning approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "179", "Issue": "", "Page": "107373", "JournalTitle": "Computer Networks"}, {"Title": "A study of LoRaWAN protocol performance for IoT applications in smart agriculture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "164", "Issue": "", "Page": "148", "JournalTitle": "Computer Communications"}, {"Title": "Security explorations for routing attacks in low power networks on internet of things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "5", "Page": "4778", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Leveraging data aggregation algorithm in LoRa networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "15", "Page": "16861", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Evaluation of low-power devices for smart greenhouse development", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "9", "Page": "10277", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A Novel Deep Learning-Based Intrusion Detection System for IoT Networks", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "2", "Page": "34", "JournalTitle": "Computers"}, {"Title": "LoRa DL: a deep learning model for enhancing the data transmission over LoRa using autoencoder", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "15", "Page": "17079", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Protecting IoT devices from security attacks using effective decision-making strategy of appropriate features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "5", "Page": "5870", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A survey on IoT trust model frameworks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "80", "Issue": "6", "Page": "8259", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A comparative analysis of various machine learning methods for anomaly detection in cyber attacks on IoT networks", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "26", "Issue": "", "Page": "101162", "JournalTitle": "Internet of Things"}, {"Title": "Novel Approach towards a Fully Deep Learning-Based IoT Receiver Architecture: From Estimation to Decoding", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "5", "Page": "155", "JournalTitle": "Future Internet"}]}, {"ArticleId": 118341794, "Title": "Asistencia de la inteligencia artificial generativa como herramienta pedagógica en la educación superior", "Abstract": "This study examines the implementation of generative artificial intelligence (GAI) tools in higher education, focusing on the University of Guayaquil. The objective was to identify the most commonly used tools by faculty and assess the benefits and challenges of their adoption. The methodology included a literature review and surveys administered to 334 faculty members across various departments, using a structured questionnaire covering demographic aspects, knowledge, usage, and perceptions of GAI integration in teaching. The analysis revealed that ChatGPT is the most widely used tool, although its adoption varies significantly among faculties. Despite advantages such as personalized materials and improved educational planning, key challenges were identified, including resistance to change and inadequate training. The study concludes that the integration of generative AI has great potential to enhance education but requires strategic planning, ongoing faculty training, and clear guidelines to ensure its ethical and pedagogical use.", "Keywords": "Inteligencia artificial generativa;Herramientas didácticas;Educación superior;Planificación educativa", "DOI": "10.36825/RITI.12.26.006", "PubYear": 2024, "Volume": "12", "Issue": "26", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad de Guayaquil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad de Guayaquil, Guayaquil, Ecuador"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad de Guayaquil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidad de Guayaquil"}], "References": []}, {"ArticleId": 118341856, "Title": "So problematic and so tied to the media: analyzing the misinformation concern in six European countries", "Abstract": "", "Keywords": "", "DOI": "10.1108/OIR-02-2024-0094", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "The disinformation warfare: how users use every means possible in the political battlefield on social media", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "46", "Issue": "7", "Page": "1313", "JournalTitle": "Online Information Review"}, {"Title": "Online political engagement, cognitive skills and engagement with misinformation: evidence from Sub-Saharan Africa and the United States", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>-<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "5", "Page": "989", "JournalTitle": "Online Information Review"}, {"Title": "There's more to news media skepticism: a path analysis examining news media literacy, news media skepticism and misinformation behaviors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "48", "Issue": "3", "Page": "441", "JournalTitle": "Online Information Review"}, {"Title": "Trust but verify? Examining the role of trust in institutions in the spread of unverified information on social media", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "107992", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 118341928, "Title": "Similarity-driven adversarial testing of neural networks", "Abstract": "Although Convolutional Neural Networks (CNNs) are among the most important algorithms of computer vision and the artificial intelligence-based systems, they are vulnerable to adversarial attacks. Such attacks can cause dangerous consequences in real-life deployments. Consequently, testing of the artificial intelligence-based systems from their perspective is crucial to reliably support human prediction and decision-making through computation techniques under varying conditions. While proposing new effective attacks is important for neural network testing, it is also crucial to design effective strategies that can be used to choose target labels for these attacks. That is why, in this paper we propose a novel similarity-driven adversarial testing methodology for target label choosing. Our motivation is that CNNs, similarly to humans, tend to make mistakes mostly among categories they perceive similar. Thus, the effort to make models predict a particular class is not equal for all classes. Motivated by this, we propose to use the most and least similar labels to the ground truth according to different similarity measures to choose the target label for an adversarial attack. They can be treated as best- and worst-case scenarios in practical and transparent testing methodologies. As similarity is one of the key components of human cognition and categorization, the approach presents a shift towards a more human-centered security testing of deep neural networks. The obtained numerical results show the superiority of the proposed methods to the existing strategies in the targeted and the non-targeted testing setups.", "Keywords": "Adversarial attacks; Testing; Artificial intelligence security; Convolutional Neural Networks; Object recognition", "DOI": "10.1016/j.knosys.2024.112621", "PubYear": 2024, "Volume": "305", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Theoretical and Applied Informatics, Polish Academy of Sciences, Gliwice, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Theoretical and Applied Informatics, Polish Academy of Sciences, Gliwice, Poland"}], "References": [{"Title": "Software vulnerabilities in TensorFlow-based deep learning applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "", "Page": "102948", "JournalTitle": "Computers & Security"}, {"Title": "Global Entropy Pooling layer for Convolutional Neural Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "555", "Issue": "", "Page": "126615", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 118342067, "Title": "Design method based on extensible semantic representation algorithm and its application in product packaging design", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11761-024-00430-x", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4313, "JournalTitle": "Service Oriented Computing and Applications", "ISSN": "1863-2386", "EISSN": "1863-2394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Learning residual refinement network with semantic context representation for real-time saliency object detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107372", "JournalTitle": "Pattern Recognition"}, {"Title": "Semantic-based topic representation using frequent semantic patterns", "Authors": "<PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106808", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Clustered design-model generation from a program source code using chaos-based metaheuristic algorithms", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "4", "Page": "3283", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Machine learning approach to packaging compatibility testing in the new product development process", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "3", "Page": "963", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 118342075, "Title": "Handling Imbalanced Data in Intrusion Detection using Time Weighted Adaboost Support Vector Machine Classifier and Crossover Boosted Dwarf Mongoose Optimization Algorithm", "Abstract": "Cybersecurity threats pose a serious challenge in the present day and age, and Intrusion Detection Systems (IDS) have emerged as an effective solution to counter these threats. In this paper, a novel IDS is proposed that captures data from the NSL-KDD dataset and are preprocessed. The Kernel Principal Component Analysis (KPCA) model extracts features presented in the data, and the Crossover Boosted Dwarf Mongoose Optimization (CDMO) algorithm selects the relevant features for classification. The CDMO algorithm offers the advantages of improving exploitation, providing optimal solutions, and balancing global exploitation and local search capabilities. The selected features are classified into five classes using the Time Weighted Adaboost Support Vector Machine (TWASVM) classifier. The TWASVM classifier effectively handles imbalanced data and delivers high-performance results. Experiments conducted on MATLAB R2019a and the proposed model achieved an higher accuracy of 98.6 % and less time complexity of 13 seconds. Comparative analysis demonstrated that the proposed IDS outperforms other state-of-the-art methods. The advantages of CDMO algorithm include improved exploitation, optimal solutions, and a balanced crossover strategy for global exploitation and local search capabilities. The advantages of the TWASVM classifier include the ability to handle imbalanced data and deliver high-performance results. Overall, the proposed IDS offer a novel solution to the challenges of intrusion detection in a rapidly evolving cybersecurity landscape.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112327", "PubYear": 2024, "Volume": "167", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sathyabama Institute of Science and Technology, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational Intelligence, SRM Institute of Science and Technology, Kattankulathur, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, PES University, Bengaluru, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ka<PERSON>u <PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sathyabama Institute of Science and Technology, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sathyabama Institute of Science and Technology, India"}], "References": [{"Title": "Class-imbalanced dynamic financial distress prediction based on Adaboost-SVM ensemble combined with SMOTE and time weighting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "128", "JournalTitle": "Information Fusion"}, {"Title": "Design of Multimedia Education Network Security and Intrusion Detection System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "18801", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A deep learning approach for effective intrusion detection in wireless networks using CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "22", "Page": "17265", "JournalTitle": "Soft Computing"}, {"Title": "Applying big data based deep learning system to intrusion detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "3", "Page": "181", "JournalTitle": "Big Data Mining and Analytics"}, {"Title": "Data mining tools -a case study for network intrusion detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "4", "Page": "4999", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel combinatorial optimization based feature selection method for network intrusion detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "102164", "JournalTitle": "Computers & Security"}, {"Title": "An adapting soft computing model for intrusion detection system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "855", "JournalTitle": "Computational Intelligence"}, {"Title": "Hybrid optimization and deep learning based intrusion detection system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "100", "Issue": "", "Page": "107876", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "An improved whale optimization algorithm based on multilevel threshold image segmentation using the Otsu method", "Authors": "<PERSON><PERSON> Ma; <PERSON><PERSON>", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "104960", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Smart home anomaly-based IDS: Architecture proposal and case study", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Vicente <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "", "Page": "100773", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 118342085, "Title": "Application of Machine Learning Models for Malware Classification With Real and Synthetic Datasets: ", "Abstract": "<p>Stacking of multiple Machine Learning (ML) classifiers have gained popularity in addressing anomalous data classification along with Deep Learning (DL) algorithms. This study compares traditional ML classifiers, multi-layer stacking ML classifiers, and DL classifiers using an open-source malware dataset-containing equal numbers of benign and malware samples. The results on the realistic dataset indicate that the DL classifier, utilizing a Bidirectional Long Short-Term Memory (BiLSTM) model, outperformed the stacked classifiers with Logistic Regression (LR) and Support Vector Machine (SVM) as Meta learners by 36.78% and 39.69%, respectively, in terms of classification accuracy and performance. The research work was extended to study the impact of Generative Adversarial Network (GAN) based synthetic dataset of relatively smaller size on deep learning models. It was observed that the Deep Learning Multi-Layer Perceptron (DLMLP) Model had relatively superior performance as compared to complex deep learning models like Long Short-Term Memory LSTM and BiLSTM</p>", "Keywords": "", "DOI": "10.4018/IJISP.356513", "PubYear": 2024, "Volume": "18", "Issue": "1", "JournalId": 13525, "JournalTitle": "International Journal of Information Security and Privacy", "ISSN": "1930-1650", "EISSN": "1930-1669", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Florida International University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Florida International University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Manipal Institute of Technology, Bengaluru, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Florida International University, USA"}], "References": [{"Title": "A comparative analysis of similarity measures akin to the Jaccard index in collaborative recommendations: empirical and theoretical perspective", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Selecting critical features for data classification based on machine learning methods", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "A framework for anomaly detection and classification in Multiple IoT scenarios", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "322", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Windows PE Malware Detection Using Ensemble Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>luwan<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "10", "JournalTitle": "Informatics"}, {"Title": "Cyberattack and Fraud Detection Using Ensemble Stacking", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "1", "Page": "22", "JournalTitle": "AI"}, {"Title": "Impact of Gaussian Noise for Optimized Support Vector Machine Algorithm Applied to Medicare Payment on Raspberry Pi", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Christian King", "PubYear": 2022, "Volume": "45", "Issue": "4", "Page": "643", "JournalTitle": "Informatica"}, {"Title": "A novel deep learning-based approach for malware detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106030", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118342269, "Title": "Human–robot cooperative object transportation with intention-driven manipulation and trajectory guidance", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41315-024-00392-5", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6109, "JournalTitle": "International Journal of Intelligent Robotics and Applications", "ISSN": "2366-5971", "EISSN": "2366-598X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Qing Gao", "Affiliation": ""}, {"AuthorId": 3, "Name": "Xinyang Tian", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A simulated risk assessment of human-robot interaction in the domestic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "300", "JournalTitle": "IAES International Journal of Robotics and Automation (IJRA)"}, {"Title": "Robust variable admittance control for human–robot co-manipulation of objects with unknown load", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "", "Page": "102408", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A survey of robot manipulation in contact", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "156", "Issue": "", "Page": "104224", "JournalTitle": "Robotics and Autonomous Systems"}]}, {"ArticleId": 118342295, "Title": "Instagram vs reality: Exploring local culture through various lenses", "Abstract": "<p>Rather than viewing online and face-to-face learning as two dichotomous domains (<PERSON><PERSON>, 2018), this study seeks to explore ways that social media can be used to support and facilitate face-to-face exploration and communication in target language environments. It also aims to help English language learners improve their ability to critically evaluate social media by exploring a local community through a critical lens. This study applied the concept of “social media pathways” (authors, 2024) through an experiential learning model to integrate an authentic social networking site, Instagram, into an ESL curriculum for a community engagement project promoting a deeper and multifaceted understanding of the target language community. This study employed qualitative method approach, including seven international students’ reflections, a post-task questionnaire, and focus-group interview data, to investigate how the assumptions made from social media compare with observations made in the field. The findings were then used to establish a “Model for Community Exploration within Virtual and Face-to-face Contexts” that adds to the experiential learning framework the additional stages of experimentation, reflection, and new conceptualization that take place when learners use social media to form assumptions about a community and then use in-person exploration to test those hypotheses.</p>", "Keywords": "", "DOI": "10.29140/jaltcall.v20n3.1547", "PubYear": 2024, "Volume": "20", "Issue": "3", "JournalId": 67259, "JournalTitle": "The JALT CALL Journal", "ISSN": "", "EISSN": "1832-4215", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118342373, "Title": "Upconverting thermal history paint for investigations of short thermal events", "Abstract": "The main goal of the work was to develop phosphorescent coating for investigations of surface temperature distribution resulted from short duration and high energy thermal events such as small rocket engine testing. For that purpose, we developed thermal history paint with upconverting Gd<sub>2</sub>O<sub>3</sub>: Er<sup>3+</sup>, Yb<sup>3+</sup>, Mg<sup>2+</sup> nanopowder. The developed paint was heated by Joule effect with precise control over temperature and heating time. Photoluminescence signal of cooled samples was excited with 980 nm laser and collected in automatic manner with use of an optical fibre probe connected to photospectrometer. The results show that irreversible changes of paint emission are temperature and heating time specific for calcination temperatures from 700 °C to 1150 °C. Calibration curves were prepared for heating times of 10, 60 and 180 seconds. Finally, 2D surface temperature samples were determined with use of upconverting thermal history paint and compared to IR camera and pyrometric measurements. The results indicate that uncertainty of temperature measurement below 10 °C was achieved for temperatures above 1000 °C. It has been demonstrated that developed thermal history paint allows for measurements of 2D surface temperature distribution with high spatial resolution and good agreement to standard measurements techniques like thermal camera and pyrometer.", "Keywords": "Thermal history paints; Upconversion; Nanosensors; Surface temperature measurements", "DOI": "10.1016/j.sna.2024.115980", "PubYear": 2024, "Volume": "379", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>wicz Research Network - Institute of Aviation, Aerodynamic Department, Krakowska Av. 110/114, Warsaw 02-256, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics Polish Academy of Sciences, al Lotników 32/46, Warsaw 02-668, Poland;International Research Centre MagTop, al. <PERSON> 32/46, Warsaw 02-668, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics Polish Academy of Sciences, al Lotników 32/46, Warsaw 02-668, Poland;International Research Centre MagTop, al. <PERSON> 32/46, Warsaw 02-668, Poland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>wicz Research Network - Institute of Aviation, Aerodynamic Department, Krakowska Av. 110/114, Warsaw 02-256, Poland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "WEA Techlab sp. z o.o., Perla str. 10, Dąbrowa Górnicza 41-301, Poland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>wicz Research Network - Institute of Microelectronics and Photonics, Lotników Av. 32/46, Warsaw 02-668, Poland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>wicz Research Network - Institute of Microelectronics and Photonics, Lotników Av. 32/46, Warsaw 02-668, Poland"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Physics Polish Academy of Sciences, al Lotników 32/46, Warsaw 02-668, Poland"}], "References": []}, {"ArticleId": 118342468, "Title": "MARA: A deep learning based framework for multilayer graph simplification", "Abstract": "In many scientific fields, complex systems are characterized by a multitude of heterogeneous interactions/relationships that are challenging to model. Multilayer graphs constitute valuable tools that can represent such complex systems, thus making possible their analysis for downstream decision-making processes. Nevertheless, modeling such complex information still remains challenging in real-world scenarios. On the one hand, holistically including all relationships may lead to noisy or computationally intensive graphs. On the other hand, limiting the amount of information to model through the selection of a portion of the available relationships can introduce boundary specification biases. However, the current research studies are demonstrating that it is more beneficial to retain as much information as possible and at a later stage perform graph simplification i.e., removing uninformative or redundant parts of the graph to facilitate the final analysis. While simplification strategies, based on deep learning methods, have been already extensively explored in the context of single-layer graphs, only a limited amount of efforts have been devoted to simplification strategies for multilayer graphs. In this work, we propose the MultilAyer gRaph simplificAtion ( MARA ) framework, a GNN-based approach designed to simplify multilayer graphs based on the downstream task. MARA generates node embeddings for a specific task by training jointly two main components: (i) an edge simplification module and (ii) a (multilayer) graph neural network. We tested MARA on different real-world multilayer graphs for node classification tasks. Experimental results show the effectiveness of the proposed approach: MARA reduces the dimension of the input graph while keeping and even improving the performance of node classification tasks in different domains and across graphs characterized by different structures. Moreover, deep learning-based simplification allows MARA to preserve and enhance important graph properties for the downstream task. To our knowledge, MARA represents the first simplification framework especially tailored for multilayer graphs analysis.", "Keywords": "Graph neural network; Graph simplification; Multilayer graph", "DOI": "10.1016/j.neucom.2024.128712", "PubYear": 2025, "Volume": "612", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Milan, Milan, Italy;Queen <PERSON> of London, London, United Kingdom;Corresponding author at: Queen <PERSON> University of London, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CIRAD, UMR TETIS, Montpellier, France;INRIA, Montpellier, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INRAE, UMR TETIS, Univ. Montpellier, Montpellier, France;INRIA, Montpellier, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Milan, Milan, Italy"}], "References": [{"Title": "Multilayer network simplification: Approaches, models and methods", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "", "Page": "100246", "JournalTitle": "Computer Science Review"}, {"Title": "Extracting multilayer networks from Sentinel-2 satellite image time series", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "S1", "Page": "S26", "JournalTitle": "Network Science"}, {"Title": "Graph classification based on structural features of significant nodes and spatial convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "639", "JournalTitle": "Neurocomputing"}, {"Title": "Representing emotions with knowledge graphs for movie recommendations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "715", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Pyramidal Reservoir Graph Neural Network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "389", "JournalTitle": "Neurocomputing"}, {"Title": "Graph convolutional and attention models for entity classification in multilayer networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}, {"Title": "Graph Neural Networks in Recommender Systems: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Co-MLHAN: contrastive learning for multilayer heterogeneous attributed networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}, {"Title": "DyHANE: dynamic heterogeneous attributed network embedding through experience node replay", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}]}, {"ArticleId": 118342589, "Title": "Preface", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.procs.2024.09.001", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Visiting Professor of Artificial Intelligence, Abertay University, United Kingdom, and Democritus University of Thrace, Greece"}, {"AuthorId": 2, "Name": "Jinghua Zhao", "Affiliation": "University of Shanghai for Science and Technology, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fuyang Normal University, China"}], "References": []}, {"ArticleId": 118342595, "Title": "The Application of Computer Augmented Reality Technology to Landscape Design", "Abstract": "Landscape design has a long history, from ancient royal gardens to modern urban parks, and its design concepts and techniques are constantly evolving. Nowadays, with the improvement of people's environmental requirements, landscape design is also constantly innovating. Although the landscape has made great progress, in actual operation, there are problems such as communication barriers with customers, difficulty in design implementation, and inability to preview the design effect in real time. Computer augmented reality technology has begun to play an important role in landscape design. Augmented Reality (AR) overlays the virtual 3D model into the real scene, previews the effect of design implementation in real time, carries out data analysis and optimization, and improves the design better to meet the needs of the public. In order to scientifically evaluate the effect of computer augmented reality technology in landscape design, this paper sets two main indicators: efficiency and quality. Efficiency is primarily concerned with the speed difference compared to traditional design methods. By means of comparative experiments, the time required to use augmented reality technology is calculated. Quality is mainly based on user feedback and expert review. Through users' actual experience of landscape design under augmented reality technology, their feedback is collected, and combined with professional review, the performance of augmented reality technology in design quality is comprehensively evaluated. This paper investigates the evaluation results of people in four landscapes (green plants, pavilions, ancient buildings, fountains). The survey results show that the fountain design quality is 80 points, the efficiency score is consistent with the pavilion, the feasibility score exceeds 80 points (82), and the user satisfaction is 5 points higher than the pavilion design.", "Keywords": "Augmented reality; computer technology; garden landscape; landscape design", "DOI": "10.1016/j.procs.2024.09.025", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Heilongjiang University, Harbin, Heilongjiang 150001, China"}, {"AuthorId": 2, "Name": "Jing<PERSON><PERSON> Song", "Affiliation": "Heilongjiang University, Harbin, Heilongjiang 150001, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Heilongjiang University, Harbin, Heilongjiang 150001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Heilongjiang University, Harbin, Heilongjiang 150001, China"}], "References": [{"Title": "Acceptance of dance training system based on augmented reality and technology acceptance model (TAM)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "33", "JournalTitle": "Virtual Reality"}, {"Title": "Survey on tracking and registration technology for mobile augmented reality", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "2", "Page": "99", "JournalTitle": "International Journal of Web and Grid Services"}, {"Title": "Exploring Users’ Behavioral Intention to Adopt Mobile Augmented Reality in Education through an Extended Technology Acceptance Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "6", "Page": "1294", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Development of an Interactive Mobile Platform for Studying Radio Engineering Disciplines Using Augmented Reality Technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "19", "Page": "147", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "Privacy-Enhancing Technology and Everyday Augmented Reality", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "TechnoSapiens: merging humans with technology in augmented reality", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "2", "Page": "1021", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 118342596, "Title": "Utilization of Microsoft HoloLens Augmented Reality Technology in Health Care Virtual Simulation", "Abstract": "This article investigated the application of Microsoft HoloLens augmented reality technology in Health Care virtual simulation. By designing a virtual simulation system based on HoloLens, it was proved that this technology provided a more realistic and intuitive medical experience, and helped doctors and medical students better understand and master medical operation skills. In addition, the technology was also simulated and trained before surgery to reduce surgical risks and improve surgical success rates. The results of this study showed that the reliability of using HoloLens augmented reality technology in Health Care virtual simulation could reach up to 93%, which had a positive promoting effect on the improvement of medical education and surgical quality.", "Keywords": "health care; microsoft hololens; augmented reality; virtual simulation", "DOI": "10.1016/j.procs.2024.09.035", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dalian Railway Medical School, Dalian, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Affiliated Zhongshan Hospital of Dalian University, Dalian, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Affiliated Zhongshan Hospital of Dalian University, Dalian, PR China"}], "References": [{"Title": "Augmented Reality: A Comprehensive Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "2", "Page": "1057", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 118342667, "Title": "Computer vision-based algorithm for precise defect detection and classification in photovoltaic modules", "Abstract": "<p>In recent years, driven by advancements in the photovoltaic industry, solar power generation has emerged as a crucial energy source in China and the globe. A progressive annotation approach is employed to pinpoint and label defect samples to enhance the precision of automated detection technology for minor defects within photovoltaic modules. Subsequently, computer vision techniques are harnessed to segment photovoltaic modules and defect samples amidst intricate backgrounds accurately. Finally, a transfer learning training model is deployed to classify and identify defects effectively. The results indicate that the mask-region convolutional neural network model achieves remarkable accuracy and recall rates of 98.7% and 0.913, respectively. Furthermore, the detection speed and inference time are 280.69 frames per second and 3.53 ms, respectively. In essence, the defect detection and classification algorithm utilizing computer vision techniques significantly enhances the precision of automated detection technology in identifying minor defects within complex environments. This advancement holds profound practical significance in ensuring photovoltaic modules’ quality and operational reliability.</p>", "Keywords": "Computer vision;Defect detection;Photovoltaic modules;Progressive annotation;Transfer learning", "DOI": "10.7717/peerj-cs.2148", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Xiamen Ocean Vocational College, Xiamen, Fujian, China"}], "References": [{"Title": "Application of Chaos Cuckoo Search Algorithm in computer vision technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "18", "Page": "12373", "JournalTitle": "Soft Computing"}, {"Title": "Attention mechanisms in computer vision: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "331", "JournalTitle": "Computational Visual Media"}, {"Title": "ST‐SIGMA: Spatio‐temporal semantics and interaction graph aggregation for multi‐agent perception and trajectory forecasting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "4", "Page": "744", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 118342721, "Title": "Sliding window SA-CNN-based CFAR detector for extended target in shipborne HFSWR", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2024.2413028", "PubYear": 2025, "Volume": "46", "Issue": "1", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Oceanography and Space Informatics, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 2, "Name": "Yonggang Ji", "Affiliation": "College of Oceanography and Space Informatics, China University of Petroleum (East China), Qingdao, China"}], "References": [{"Title": "Signal structure information-based target detection with a fully convolutional network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "345", "JournalTitle": "Information Sciences"}, {"Title": "Small-target ship detection in SAR images based on densely connected deep neural network with attention in complex scenes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "4", "Page": "4162", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 118342730, "Title": "Open Source Software Adoption in the Financial Services Industry: Exploratory Evidence From South Africa", "Abstract": "<p>Although OSS has gained traction across industries worldwide, few studies have been reported on OSS adoption in the financial industry and even less in the context of developing countries. This paper presents an exploratory overview of the state of OSS adoption in the financial industry using the case of South Africa as a country with the largest and most developed financial sector in Africa. To achieve this, we conducted a qualitative study that leveraged the Technology-Organisation-Environment (TOE) framework, and the Diffusion of Innovation (DOI) theory as theoretical references and used thematic analysis to analyse the data collected from a focus group discussion (FGD) of eight experts from four financial services organisations based in South Africa. The study's findings reveal the state of practice of OSS and the technological, organisational, and environmental factors that affect OSS adoption, and diffusion in financial services organisations in South Africa. We also identified seven research themes that should gain the attention of researchers from now on.</p>", "Keywords": "", "DOI": "10.4018/IJOSSP.356512", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 18799, "JournalTitle": "International Journal of Open Source Software and Processes", "ISSN": "1942-3926", "EISSN": "1942-3934", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Johannesburg, South Africa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Pretoria, South Africa"}], "References": []}, {"ArticleId": 118343221, "Title": "Integrative Approach to Fluorescence-Based Oxygen Sensing in Polymer Optical Fibers with Surface Plasmon Resonance", "Abstract": "This study demonstrates a technique to enhance oxygen (O2) concentration measurement by combining surface plasmon resonance (SPR) and fluorescence-based sensor on polymer optical fiber (POF). The SPR was generated using silver (Ag) and the fluorescence was generated by organic dye. Three fluorescence dyes were used, Tris (4, 7-diphenyl-1, 10-phenanthroline) ruthenium (II) dichloride ( [ Ru ( dpp ) 3 ] 2 + ), platinum octaethylporphyrin (PtOEP) and 5,10,15,20-tetrakis (pentafluorophenyl) 21 H,23H-porphine palladium (II) (PdTFPP). The sensor was tested in a gas chamber with different concentration level of O2. Sensitivity values for the fluorescence dyes for [Ru(dpp)₃]²⁺, PtOEP, and PdTFPP were 0.0099 a.u/%, 0.0343 a.u/% and 0.05 a.u/% respectively. When SPR was implemented, the sensitivity values for Ag/[Ru(dpp)₃]²⁺, Ag/PtOEP, and Ag/PdTFPP were 0.0589 nm/%, 0.0345 nm/%, and 0.118 nm/% respectively. PdTFPP and Ag/PdTFPP exhibit highest sensitivity in each experiment. Therefore, the further analysis for the sensing performance of PdTFPP and Ag/PdTFPP were held. As a result, Ag/PdTFPP exhibits lower limit of detection (LOD), and limit of quantification (LOQ) with 3.054 % and 9.254 %, respectively, with higher R<sup>2</sup> of 0.9971 and lower mean standard deviation of 0.174, compared to PdTFPP 16.496 % and 49.988 % respectively, with R<sup>2</sup> of 0.9211 and higher mean standard deviation of 2.003. This improvement can benefit researchers and professionals in medical diagnostics, environmental monitoring, and industrial process control by providing a more sensitive and accurate method for measuring O2 levels.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115979", "PubYear": 2024, "Volume": "379", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, College of Engineering, Universiti Teknologi MARA, 40450 Shah Alam, Selangor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering, College of Engineering, Universiti Teknologi MARA, 40450 Shah Alam, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Microengineering and Nanoelectronics, Universiti Kebangsaan Malaysia, 43600 Bangi, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Power Engineering, Universiti Tenaga Nasional, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Power Engineering, Universiti Tenaga Nasional, 43000 Kajang, Selangor, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "School of Distance Education, Universiti Sains Malaysia, 11800 Minden, Pulau <PERSON>g, Malaysia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Microengineering and Nanoelectronics, Universiti Kebangsaan Malaysia, 43600 Bangi, Selangor, Malaysia;Photonics Technology Laboratory, Department of Electrical, Electronic and Systems Engineering, Faculty of Engineering and Built Environment, Universiti Kebangsaan Malaysia, 43600 Bangi, Selangor, Malaysia;Corresponding author at: Institute of Microengineering and Nanoelectronics, Universiti Kebangsaan Malaysia, 43600 Bangi, Selangor, Malaysia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, College of Engineering, Universiti Teknologi MARA, 40450 Shah Alam, Selangor, Malaysia;Corresponding author"}], "References": [{"Title": "Materials for optical oxygen sensing under high hydrostatic pressure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "352", "Issue": "", "Page": "131037", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 118343231, "Title": "Enhancing aspect-based sentiment analysis with linking words-guided emotional augmentation and hybrid learning", "Abstract": "Aspect-based sentiment analysis (ABSA) is a sophisticated task in the field of natural language processing that aims to identify emotional tendencies related to specific aspects of text. However, ABSA often faces significant data shortages, which limit the availability of annotated data for training and affects the robustness of models. Moreover, when a text contains multiple emotional dimensions, these dimensions can interact, complicating the judgments of emotional polarity. In response to these challenges, this study proposes an innovative training framework: Linking words-guided multidimensional emotional data augmentation and adversarial contrastive training (LWEDA-ACT). Specifically, this method alleviates the issue of data scarcity by synthesizing additional training samples using four different text generators. To obtain the most representative samples, we selected them by calculating sentence entropy. Meanwhile, to reduce potential noise, we introduced linking words to ensure text coherence. Additionally, by applying adversarial training, the model is able to learn generalized feature representations to handle minor input perturbations, thereby enhancing its robustness and accuracy in complex emotional dimension interactions. Through contrastive learning, we constructed positive and negative sample pairs, enabling the model to more accurately identify and distinguish the sentiment polarity of different aspect terms. We conducted comprehensive experiments on three popular ABSA datasets, namely Restaurant, Laptop, and Twitter, and compared our method against the current state-of-the-art techniques. The experimental results demonstrate that our approach achieved an accuracy improvement of +0.98% and a macro F1 score increase of +0.52% on the Restaurant dataset. Additionally, on the challenging Twitter dataset, our method improved accuracy by +0.77% and the macro F1 score by *****%.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.128705", "PubYear": 2025, "Volume": "612", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Software Engineering, Chongqing University of Posts and Telecommunications, Chongqing, China;Corresponding author"}, {"AuthorId": 3, "Name": "Zanxiong Li", "Affiliation": "School of Software Engineering, Chongqing University of Posts and Telecommunications, Chongqing, China"}], "References": [{"Title": "A Mixed approach of Deep Learning method and Rule-Based method to improve Aspect Level Sentiment Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "1/2", "Page": "163", "JournalTitle": "Applied Computing and Informatics"}, {"Title": "COVID-19 Public Sentiment Insights: A Text Mining Approach to the Gulf Countries", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "1613", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A multi-task learning framework for end-to-end aspect sentiment triplet extraction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "479", "Issue": "", "Page": "12", "JournalTitle": "Neurocomputing"}, {"Title": "Data augmentation approaches in natural language processing: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "71", "JournalTitle": "AI Open"}, {"Title": "Data augmentation for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "125", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Deep learning for aspect-based sentiment analysis: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e1044", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Dependency-enhanced graph convolutional networks for aspect-based sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "19", "Page": "14195", "JournalTitle": "Neural Computing and Applications"}, {"Title": "THFE: A Triple-hierarchy Feature Enhancement method for tiny boat detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106271", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Aspect-Pair Supervised Contrastive Learning for aspect-based sentiment analysis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "110648", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Artificial Intelligence Based Sentence Level Sentiment Analysis of COVID-19", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "1", "Page": "791", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "DSCA: A Dual Semantic Correlation Alignment Method for domain adaptation object detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "110329", "JournalTitle": "Pattern Recognition"}, {"Title": "DP-DDCL: A discriminative prototype with dual decoupled contrast learning method for few-shot object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "297", "Issue": "", "Page": "111964", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 118343254, "Title": "A high-order finite-difference solver for direct numerical simulations of magnetohydrodynamic turbulence", "Abstract": "This paper presents the development and validation of a Magnetohydrodynamics (MHD) module integrated into the Xcompact3d framework, an open-source high-order finite-difference suite of solvers designed to study turbulent flows on supercomputers. Leveraging the Fast Fourier Transform library already implemented in Xcompact3d, alongside sixth-order compact finite-difference schemes and a direct spectral Poisson solver, both the induction and potential-based MHD equations can be efficiently solved at scale on CPU-based supercomputers for fluids with strong and weak magnetic field, respectively. Validation of the MHD solver is conducted against established benchmarks, including Orszag-Tang vortex and MHD channel flows, demonstrating the module's capability to accurately capture complex MHD phenomena, providing a powerful tool for research in both engineering and astrophysics. The scalability of the Xcompact3d framework remains intact with the incorporation of the MHD module, ensuring efficient performance on modern high-performance clusters. This paper also presents new findings on the evolution of the Taylor-Green vortex under an external magnetic field for different flow regimes.", "Keywords": "Magnetohydrodynamics; High performance computing; High-order finite-difference schemes; Direct numerical simulation", "DOI": "10.1016/j.cpc.2024.109400", "PubYear": 2025, "Volume": "307", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Scientific Computing Department, Science and Technology Facilities Council, Daresbury Laboratory, Keckwick Lane, Daresbury, Warrington, WA4 4AD, United Kingdom;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Aeronautics, Imperial College London, London, SW7 2AZ, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Engineering, The University of Manchester, Manchester, M13 9PL, United Kingdom"}], "References": [{"Title": "Xcompact3D: An open-source framework for solving turbulence problems on a Cartesian mesh", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "", "Page": "100550", "JournalTitle": "SoftwareX"}, {"Title": "Vector potential-based MHD solver for non-periodic flows using Fourier continuation expansions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "275", "Issue": "", "Page": "108304", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 118343271, "Title": "Novel bioelectrode for sweat lactate sensor based on platinum nanoparticles/reduced graphene oxide modified carbonized silk cocoon", "Abstract": "Silk cocoons were used as a bioelectrode for the wearable electrochemical sensors of sweat lactate via carbonization and the in situ electrodeposition of platinum nanoparticles (PtNPs) and reduced graphene oxide (rGO), followed by lactate oxidase immobilization. Scanning electron microscopy, atomic force microscopy, and x-ray photoelectron microscopy confirmed that PtNPs/rGO were deposited on the carbonized silk cocoon surfaces, characterized via the physical and chemical alterations of silk cocoons. The electrocatalytic activity of PtNPs and the high surface area and functionality of rGO enhanced the electrochemical sensitivity of the sensor in lactate detection. This biosensor detected sweat lactate selectively in a range of 0–25 mM with a limit of detection of 0.07 mM, which is sufficient to distinguish between normal individuals and muscle fatigue-prone patients at a cut-off sweat lactate level of 12.5 mM. This biosensor was applied for sweat lactate detection and validated through laser desorption–ionization mass spectrometry with satisfactory results. This bioelectrode exhibits cytocompatibility with non-irritation and non-allergy to human skin, highlighting its application as a wearable lactate biosensor for self-monitoring of muscle fatigue.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136717", "PubYear": 2025, "Volume": "423", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Wisarttra Phamonpon", "Affiliation": "Nanoscience and Technology Program, Graduate School, Chulalongkorn University, Phayathai Road, Pathumwan, Bangkok 10330, Thailand"}, {"AuthorId": 2, "Name": "Nadtinan Promphet", "Affiliation": "Metallurgy and Materials Science Research Institute, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand"}, {"AuthorId": 3, "Name": "Kanokwan Saeng<PERSON>ttiyu<PERSON>", "Affiliation": "Metallurgy and Materials Science Research Institute, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Metallurgy and Materials Science Research Institute, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Metallurgy and Materials Science Research Institute, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand;Center of Excellence in Responsive Wearable Materials, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Fiber Science, College of Human Ecology, Cornell University, Ithaca, NY 14850, United States"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Metallurgy and Materials Science Research Institute, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand;Center of Excellence in Responsive Wearable Materials, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand;Corresponding author at: Metallurgy and Materials Science Research Institute, Chulalongkorn University, Soi Chula 12, Phayathai Road, Pathumwan, Bangkok 10330, Thailand"}], "References": []}, {"ArticleId": 118343353, "Title": "Research on Supply Chain Demand Prediction Model Based on LSTM", "Abstract": "The supply chain regards suppliers, producers, and consumers as an organic whole, unifying and coordinating the information flow, logistics, and capital flow of all members, and achieving the goal of win-win for all members in the overall operation of cross organization. Demand forecasting is an important factor driving the entire supply chain, and low error rates in forecasting are a common goal pursued by the industry. In order to improve the quality of demand forecasting, enhance the efficiency of supply chain operations, and leverage the important role of machine learning in the era of artificial intelligence, this paper conducts research based on LSTM. Firstly, this paper determines the objective function and constraints for supply chain demand forecasting; Then, this paper constructs a supply chain demand prediction model, based on the LSTM network structure, determine the network training method and model construction process; Finally, this paper conducts simulation experiments and result analysis, configure LSTM parameters, determine model performance evaluation indicators, and compare and analyze actual values with predicted values. The results indicate that the supply chain demand prediction model constructed in this article has very good performance and has promotional value in practice.", "Keywords": "LSTM; Supply Chain; Demand Prediction Model; Objective Function; Constraint Condition; Simulation Experiment", "DOI": "10.1016/j.procs.2024.09.039", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Liaoning Institute of Science and Engineering , Jinzhou , Liaoning , China"}], "References": [{"Title": "Session-based recommendations with sequential context using attention-driven LSTM", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "115", "Issue": "", "Page": "109138", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "CNN-LSTM and post-processing for EMG-Based Hand Gesture Recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "22", "Issue": "", "Page": "200352", "JournalTitle": "Intelligent Systems with Applications"}]}, {"ArticleId": 118343354, "Title": "Underwater Engineering Crack Identification based on Lightweight Convolutional Neural Network", "Abstract": "Convolutional neural network is an effective model for image feature recognition, which is widely used in crack recognition and risk detection. The crack identification of underwater engineering involves a huge scale of data, and the traditional R-CNN model and Faster R-CNN model have the problems of large number of model parameters and low efficiency for crack identification. Therefore, a lightweight convolutional neural network based crack identification method for underwater engineering is proposed to optimize the efficiency and accuracy of crack identification. Deep separable convolution is used to replace the common convolution of YOLOX network (an open source high-performance object detection algorithm), reducing the redundant parameters of the model to achieve lightweight design. Following the backbone feature extraction network framework of YOLOX, Transformer (a structure based on attention mechanism and forward neural network) vision module is adopted to replace the CSP structure (a feature extraction structure) at the end of the backbone network, adding attention mechanism and enhancing key information extraction capability. Make up for the problem of decreasing feature extraction accuracy caused by decreasing parameter number; Finally, the adaptive spatial feature fusion strategy is used instead of the traditional feature pyramid cascade to implement feature fusion and improve the feature extraction ability of small targets. The test results show that the improved YOLOX lightweight convolutional neural network has good prediction ability for underwater engineering cracks, and can accurately select the crack location, with fewer false detection and missed detection cases. Ablation experiments show that this study builds a lightweight convolutional neural network model, which improves the accuracy of the model to identify underwater engineering cracks. The improved lightweight convolutional neural network is suitable for crack identification in underwater engineering and can be widely applied in the field of underwater engineering risk identification.", "Keywords": "Lightweight; Convolutional neural network; Feature extraction; Self-adaptation; Attention mechanism; Crack identification", "DOI": "10.1016/j.procs.2024.09.027", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Liyuan Dai", "Affiliation": "Ma'anshan Health Diagnosis and Repair Technology Research Center, Wanjiang University of Technology, Ma'anshan, Anhui, China;School of Hydraulic Engineering, Wanjiang University of Technology, Ma'anshan, Anhui, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ma'anshan Health Diagnosis and Repair Technology Research Center, Wanjiang University of Technology, Ma'anshan, Anhui, China;School of Hydraulic Engineering, Wanjiang University of Technology, Ma'anshan, Anhui, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Ma'anshan Health Diagnosis and Repair Technology Research Center, Wanjiang University of Technology, Ma'anshan, Anhui, China;School of Hydraulic Engineering, Wanjiang University of Technology, Ma'anshan, Anhui, China"}], "References": [{"Title": "Automatic classification method of liver ultrasound standard plane images using pre-trained convolutional neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "975", "JournalTitle": "Connection Science"}, {"Title": "Optimal Deep Convolutional Neural Network with Pose Estimation for Human Activity Recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "44", "Issue": "2", "Page": "1719", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 118343355, "Title": "Application of Virtualization Digital Technology in Intelligent Platform Construction Pattern Analysis System", "Abstract": "Intelligent platforms are mainly service platforms composed of machine learning integrations that rely on continuous reasoning and learning. This technology has been widely used in today's the Internet Age, and its analysis system, as an important customer oriented feedback link, is also very important. Therefore, this article would optimize the intelligent platform analysis system by applying virtual digital technology, and use intelligent platforms for ship shipping and smart home platforms as examples.", "Keywords": "Virtualization digital technology; intelligent platform; analysis system; smart home", "DOI": "10.1016/j.procs.2024.09.050", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong Institute of Commerce and Technology, Jinan 250103, Shandong, China"}], "References": [{"Title": "Smart home health monitoring system for predicting type 2 diabetes and hypertension", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "862", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Design of online intelligent English teaching platform based on artificial intelligence techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "1166", "JournalTitle": "Computational Intelligence"}, {"Title": "Ensemble machine learning approach for classification of IoT devices in smart home", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "3179", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Generalized Sparse Bayesian Learning and Application to Image Reconstruction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "1", "Page": "262", "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification"}]}, {"ArticleId": 118343361, "Title": "Intelligent Analysis Method of E-Commerce Data Based on Multiple Machine Learning Algorithms", "Abstract": "Machine learning algorithm is one of the most popular research fields in the 21st century, and it is widely used in various industries. This paper first discusses the development status, characteristics and intelligent classification technology of e-commerce based on various methods. Then, through statistical analysis of multiple samples, this paper establishes a model to predict the quantity of goods and other time-related changes in the future period of time, and proposes an improved variance estimate to achieve the best effect under different data scenarios. Finally, the experiment proves that the response time of the linear regression algorithm fluctuates between 20ms and 25ms, and the overall fluctuation is small. For different samples, the response time of the linear regression algorithm is maintained in a relatively stable range, showing the stability of different input data. The response time of the decision tree algorithm fluctuates between 28ms and 33ms, showing a certain fluctuation. For different samples, the response time of decision tree algorithm fluctuates greatly, which may be affected by the characteristics of input data and show different processing efficiency. The response time of support vector machine algorithm fluctuates between 14ms and 18ms, and the change is relatively stable. For different samples, the response time of SVM algorithm is kept at a relatively stable level, showing the consistency of input data processing ability.", "Keywords": "Machine learning algorithms; e-commerce data; intelligent analysis; data analysis", "DOI": "10.1016/j.procs.2024.09.016", "PubYear": 2024, "Volume": "243", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jilin Agricultural Science and Technology University , Jilin , Jilin , China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jilin Agricultural Science and Technology University , Jilin , Jilin , China"}], "References": [{"Title": "Estimating city-level poverty rate based on e-commerce data with machine learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "1", "Page": "195", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Forecasting of e-commerce transaction volume using a hybrid of extreme learning machine and improved moth-flame optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "2", "Page": "952", "JournalTitle": "Applied Intelligence"}, {"Title": "RETRACTED ARTICLE: A comparative study of the market demand for Chinese proficiency test preparation books: evidence from e-commerce data", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "1", "Page": "207", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Semantic enhanced <PERSON>ov model for sequential E-commerce product recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "1", "Page": "67", "JournalTitle": "International Journal of Data Science and Analytics"}, {"Title": "New machine learning model based on the time factor for e-commerce recommendation systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "6", "Page": "6756", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "The Use of an Internet of Things Data Management System Using Data Mining Association Algorithm in an E-Commerce Platform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "Stylized Data-to-text Generation: A Case Study in the E-Commerce Domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Landscape of High-Performance Python to Develop Data Science and Machine Learning Applications", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Machine learning-based fault estimation of nonlinear descriptor systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Automation and Control"}]}, {"ArticleId": 118343472, "Title": "Artistic-style text detector and a new Movie-Poster dataset", "Abstract": "Although current text detection algorithms demonstrate effectiveness in general scenarios, their performance declines when confronted with artistic-style text featuring complex structures. This paper proposes a method that utilizes the Criss-Cross Attention and the residual dense block to address the incomplete and misdiagnosis of artistic-style text detection by current algorithms. Specifically, our method mainly consists of a feature extraction backbone, a Recycle Criss-Cross Attention module, a Residual Feature Pyramid Network, and a Boundary Discrimination Module. The Recycle Criss-Cross Attention module significantly enhances the model’s perceptual capabilities in complex environments by fusing horizontal and vertical contextual information, allowing it to capture detailed features overlooked in artistic-style text. We incorporate the residual dense block into the feature pyramid network to suppress the effect of background noise during feature fusion. Aiming to omit the complex post-processing, we explore a Boundary Discrimination Module that guides the correct generation of boundary proposals. Furthermore, given that movie poster titles often use stylized art fonts, we collected a Movie-Poster dataset to address the scarcity of artistic-style text data. Extensive experiments demonstrate that our proposed method performs superiorly on the Movie-Poster dataset and produces excellent results on multiple benchmark datasets. The code and the Movie-Poster dataset will be available at: https://github.com/AXNing/Artistic-style-text-detection .", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125544", "PubYear": 2025, "Volume": "261", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Chongqing University of Technology, Chongqing, 400054, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Chongqing University of Technology, Chongqing, 400054, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Agronomy and Biotechnology, Southwest University, Chongqing, 400715, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science, Chongqing University, Chongqing, 400044, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Industrial Training Center, Guangdong Polytechnic Normal University, Guangzhou, 510665, China"}], "References": [{"Title": "A multimodal particle swarm optimization-based approach for image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113233", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Automatic segmentation model combining U-Net and level set method for medical images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "113419", "JournalTitle": "Expert Systems with Applications"}, {"Title": "TextMountain: Accurate scene text detection via instance segmentation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107336", "JournalTitle": "Pattern Recognition"}, {"Title": "Scene Text Detection and Recognition: The Deep Learning Era", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "161", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Text proposals with location-awareness-attention network for arbitrarily shaped scene text detection and recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117564", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 118343495, "Title": "Contact-Free Physiological Monitoring of Cardiorespiratory States Using Radar and Optical Sensors", "Abstract": "The paper summarizes a Doctoral Thesis that focuses on two new approaches for unobtrusive contact-free monitoring of cardiorespiratory and hemodynamic states. First approach is based on radar signals and proposes a novel branched neural network architecture for classification of hemodynamic scenarios. The second is based on RGB camera signals and proposes multi-wavelength depth-dependant photoplethysmogram reconstruction, allowing for single-site pulse transit time measurement and blood pressure estimation using a consumer camera.", "Keywords": "", "DOI": "10.31449/inf.v48i3.7211", "PubYear": 2024, "Volume": "48", "Issue": "3", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "G<PERSON><PERSON><PERSON>", "Affiliation": "Department of Intelligent Systems, Jožef Stefan Institute"}], "References": []}, {"ArticleId": 118343504, "Title": "A Framework for Malicious Domain Names Detection using Feature Selection and Majority Voting Approach", "Abstract": "As cyber attacks become more sophisticated, identifying and mitigating bad domain names has become critical to assuring the security of online environments. This paper presents a framework for detecting malicious domain names using a feature selection strategy and a majority vote method. The suggested methodology begins with the extraction of important features from domain names and their related characteristics, followed by a rigorous feature selection procedure to determine the most discriminating attributes. To accomplish feature selection, a variety of feature selection techniques are used, including chi-square statistics, information gain, gain ratio, and correlation-based feature selection, to analyse the value of each characteristic in distinguishing benign and malicious domain names. In addition, a majority voting strategy is utilised to improve the detection system’s overall accuracy and reliability by combining the predictions of different classifiers such as AdaBoost, logistic regression, k-nearest neighbours, naive bayes, and multilayer perceptron. The ensemble of classifiers is trained on the ideal features, yielding a complete and robust model capable of accurately recognising mali- cious domain names while minimising false positives. The proposed approach is evalu- ated against real-world examples of harmful domain names. The suggested framework employing Chi-square feature selection and majority voting detects malicious domain names with an accuracy of 99.44%, precision of 99.44%, recall of 99.44%, and f-measure of 99.44%. The use of feature selection and a majority voting technique improves the system’s adaptability and resilience in the face emerging cyber threats.", "Keywords": "", "DOI": "10.31449/inf.v48i3.5824", "PubYear": 2024, "Volume": "48", "Issue": "3", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "R.C.Patel Institute of Technology, Shirpur"}], "References": []}, {"ArticleId": 118343519, "Title": "Multi-Criteria Analysis in Circular Economy Principles: Using AHP Model for Risk Assessment in Sustainable Whisky Production", "Abstract": "<p>As the whisky industry applies circular economy principles to maximize resource utilization and minimize environmental impact, companies become exposed to several risks, which require complex assessments to ensure reliable outcomes. This study provides an organized framework to identify, prioritize, and rank various risk factors commonly observed in the whisky industry through the development of an analytical hierarchy process (AHP) multi-criteria analysis model. Experts from 18 small European distilleries identified five main risk criteria and nineteen sub-criteria from brainstorming workplace observations and categorized them as: environmental (5), operational (4), technological innovation (3), food safety (3), and economical (4) risks. The analytical hierarchy process (AHP) approach was used to determine the weights and ranks of the main criteria and sub-criteria based on the survey responses received from experts from each distillery. The final judgements are consistent, as indicated by consistency values (CR) of less than 0.1 for all risk criteria. Unlike traditional risk assessment methods, the AHP model effectively integrates qualitative and quantitative data, aiding strategic decision making in the whisky industry by breaking down complex problems into manageable sub-problems. Future research directions may expand the criteria and explore additional sustainable practices.</p>", "Keywords": "", "DOI": "10.3390/computation12100206", "PubYear": 2024, "Volume": "12", "Issue": "10", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Economics, Administration and Business, Ştefan cel Mare University of Suceava, 720229 Suceava, Romania"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Economics, Administration and Business, Ştefan cel Mare University of Suceava, 720229 Suceava, Romania"}, {"AuthorId": 3, "Name": "Ancuţa Chetrariu", "Affiliation": "Faculty of Food Engineering, Ştefan cel Mare University of Suceava, 720229 Suceava, Romania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Food Engineering, Ştefan cel Mare University of Suceava, 720229 Suceava, Romania"}], "References": []}, {"ArticleId": 118343525, "Title": "Training learners to use Quizlet vocabulary activities on mobile phones in Vietnam with Facebook", "Abstract": "<p>Mobile phone ownership among university students in Vietnam has reached almost 100%, exceeding that of Internet-capable desktop computers. This has made them increasingly popular to allow learners to carry out learning activities outside of the classroom, but some studies have suggested that learners are not always willing to engage in activities outside of the classroom (<PERSON> et al., 2013). Recent research has suggested that providing training to learners that includes not only how but also why activities are important can improve learner engagement in mobile-based activities (Stockwell & Hubbard, 2014). In this paper, Vietnamese learners of English engaged in vocabulary and grammar tasks using the Quizlet app on their mobile phones outside of class time. Learners were provided with technical training in class, while ongoing strategic and pedagogical training were provided through a combination of in-class activities and interactions through a dedicated Facebook page over a 5-week period. Usage patterns of the site were recorded through a learning journal and interactions on the Facebook page were analysed to determine the nature of the discussions that took place. Learner attitudes towards the tasks and the training were examined through pre- and post-questionnaires and a focus group discussion. The results are discussed in terms of the problems encountered, and some suggestions for providing appropriate training to learning through mobile phones outside of class through social networking.</p>", "Keywords": "", "DOI": "10.29140/jaltcall.v12n1.j201", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 67259, "JournalTitle": "The JALT CALL Journal", "ISSN": "", "EISSN": "1832-4215", "Authors": [{"AuthorId": 1, "Name": "Phuong Tran", "Affiliation": ""}], "References": []}, {"ArticleId": 118343526, "Title": "Sustaining motivation for Japanese kanji learning: Can digital games help?", "Abstract": "<p>Educational digital games are often presented at Technology in Language Education conferences. The games are entertaining and are backed by research detailing how games can improve the learning experience through active critical learning, learner interaction, competition, challenge, and high learner motivation. The authors, inspired by such presentations, were interested in creating digital games to mitigate problems of demotivation in a beginner Japanese kanji (non-alphabetic script) class at Auckland University of Technology but found there was no body of research on digital games for learning non-alphabetic scripts. This paper contributes to filling this gap by describing the creation of three digital games for kanji learning. Difficulties were experienced during the development of the games and these are described with reference to the divide, discussed in gaming literature, between the type of digital games being showcased at conferences and the reality for teachers wishing to emulate the practice by developing their own digital games. Questionnaire responses and the game-related journal entries of three cohorts of learners were analysed, and teacher reflections on the action research project were used to answer the questions “Should we be leaving this field to the experts?” and “Other than high-end multi-level curriculum-centred digital games, are there different gaming scenarios worth exploring?”</p>", "Keywords": "", "DOI": "10.29140/jaltcall.v12n1.j200", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 67259, "JournalTitle": "The JALT CALL Journal", "ISSN": "", "EISSN": "1832-4215", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Nesbitt", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118343627, "Title": "Application of Short Text Classification Model Based on GPT-3.5 in E-Commerce: ", "Abstract": "<p>In the realm of e-commerce, the crucial role of short text classification in enhancing user experience and platform operational efficiency has led to the development of effective. approaches encompassing feature-based algorithms and early deep learning models. However, challenges remain when dealing with complex semantics and vast amounts of unstructured data. To bridge this gap, This paper proposes a novel model of short text classification based on GPT-3.5, which significantly improves classification accuracy by combining conceptualization and character-level information. Our model's superior performence across all datasets was validated through experimentsal trials on five widely used datasets (TREC, AG News, Bing, and Movie Review).</p>", "Keywords": "", "DOI": "10.4018/JOEUC.356500", "PubYear": 2024, "Volume": "36", "Issue": "1", "JournalId": 11438, "JournalTitle": "Journal of Organizational and End User Computing", "ISSN": "1546-2234", "EISSN": "1546-5012", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China University of Geosciences, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wuhan University of Technology, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhengzhou University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Cardlytics, Inc., USA"}], "References": [{"Title": "A BERT-Based Hybrid Short Text Classification Model Incorporating CNN and Attention-Based BiGRU", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "Text Similarity Measurement Method and Application of Online Medical Community Based on Density Peak Clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "A survey of transformers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "111", "JournalTitle": "AI Open"}, {"Title": "Design of a User Comment Management System Based on Text Mining: Innovative Organization Management for E-Commerce", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "DILF: Differentiable rendering-based multi-view Image–Language Fusion for zero-shot 3D shape understanding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "102", "Issue": "", "Page": "102033", "JournalTitle": "Information Fusion"}]}]