# an example for maze using qlearning, two dimension
import numpy as np
# reward matrix R
R = np.array([[-1, -1, -1, -1, 0, -1],[-1, -1, -1, 0, -1, 100],[-1, -1, -1, 0, -1, -1],[-1, 0, 0, -1, 0, -1],[0, -1, -1, 0, -1, 100],[-1, 0, -1, -1, 0, 100]])
Q = np.zeros((6, 6), float)
gamma = 0.8
episode = 0
while episode < 1000:
    state = np.random.randint(0, 6)
    for action in range(6):
        if R[state, action] > -1:
            Q[state, action] = R[state, action] + gamma*max(Q[action])
            episode = episode + 1
            print(Q)