[{"ArticleId": 115645376, "Title": "CommanderUAP: a practical and transferable universal adversarial attacks on speech recognition models", "Abstract": "<p>Most of the adversarial attacks against speech recognition systems focus on specific adversarial perturbations, which are generated by adversaries for each normal example to achieve the attack. Universal adversarial perturbations (UAPs), which are independent of the examples, have recently received wide attention for their enhanced real-time applicability and expanded threat range. However, most of the UAP research concentrates on the image domain, and less on speech. In this paper, we propose a staged perturbation generation method that constructs CommanderUAP, which achieves a high success rate of universal adversarial attack against speech recognition models. Moreover, we apply some methods from model training to improve the generalization in attack and we control the imperceptibility of the perturbation in both time and frequency domains. In specific scenarios, CommanderUAP can also transfer attack some commercial speech recognition APIs.</p>", "Keywords": "Adversarial examples;Universal adversarial perturbations;Speech recognition", "DOI": "10.1186/s42400-024-00218-8", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 5427, "JournalTitle": "Cybersecurity", "ISSN": "", "EISSN": "2523-3246", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Technology, Shandong University, Qingdao, China; Quancheng Laboratory, QCL, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Technology, Shandong University, Qingdao, China; Quancheng Laboratory, QCL, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Technology, Shandong University, Qingdao, China; Quancheng Laboratory, QCL, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Technology, Shandong University, Qingdao, China; Quancheng Laboratory, QCL, Jinan, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Technology, Shandong University, Qingdao, China; Quancheng Laboratory, QCL, Jinan, China"}], "References": [{"Title": "Towards the universal defense for query-based audio adversarial attacks on speech recognition system", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}]}, {"ArticleId": 115645474, "Title": "Detecting the Adversarially-Learned Injection Attacks via Knowledge Graphs", "Abstract": "Over the past two decades, many studies have devoted a good deal of attention to detect injection attacks in recommender systems. However, most of the studies mainly focus on detecting the heuristically-generated injection attacks, which are heuristically fabricated by hand-engineering. In practice, the adversarially-learned injection attacks have been proposed based on optimization methods and enhanced the ability in the camouflage and threat. Under the adversarially-learned injection attacks, the traditional detection models are likely to be fooled. In this paper, a detection method is proposed for the adversarially-learned injection attacks via knowledge graphs. Firstly, with the advantages of wealth information from knowledge graphs, item-pairs on the extension hops of knowledge graphs are regarded as the implicit preferences for users. Also, the item-pair popularity series and user item-pair matrix are constructed to express the user's preferences. Secondly, the word embedding model and principal component analysis are utilized to extract the user's initial vector representations from the item-pair popularity series and item-pair matrix, respectively. Moreover, the Variational Autoencoders with the improved R-drop regularization are used to reconstruct the embedding vectors and further identify the shilling profiles. Finally, the experiments on three real-world datasets indicate that the proposed detector has superior performance than benchmark methods when detecting the adversarially-learned injection attacks. In addition, the detector is evaluated under the heuristically-generated injection attacks and demonstrates the outstanding performance.", "Keywords": "", "DOI": "10.1016/j.is.2024.102419", "PubYear": 2024, "Volume": "125", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Xinzhou Normal University, Xinzhou, Shanxi Province, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Sydney, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "Xinzhou Normal University, Xinzhou, Shanxi Province, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Xinzhou Normal University, Xinzhou, Shanxi Province, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Xinzhou Normal University, Xinzhou, Shanxi Province, China"}], "References": [{"Title": "An unsupervised detection method for shilling attacks based on deep learning and community detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "477", "JournalTitle": "Soft Computing"}, {"Title": "A genre trust model for defending shilling attacks in recommender systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "3", "Page": "2929", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Ready for emerging threats to recommender systems? A graph convolution-based generative shilling attack", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "683", "JournalTitle": "Information Sciences"}, {"Title": "Gray-Box Shilling Attack: An Adversarial Learning Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "FedPOIRec: Privacy-preserving federated poi recommendation with social influence", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "623", "Issue": "", "Page": "767", "JournalTitle": "Information Sciences"}, {"Title": "Hybrid gated recurrent unit and convolutional neural network-based deep learning mechanism for efficient shilling attack detection in social networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "108", "Issue": "", "Page": "108673", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A novel classification‐based shilling attack detection approach for multi‐criteria recommender systems", "Authors": "<PERSON><PERSON><PERSON>lu <PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "499", "JournalTitle": "Computational Intelligence"}, {"Title": "Recommendation attack detection based on improved Meta Pseudo Labels", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "279", "Issue": "", "Page": "110931", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "RoPE: Defending against backdoor attacks in federated learning systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Yuanqing Xia", "PubYear": 2024, "Volume": "293", "Issue": "", "Page": "111660", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 115645530, "Title": "An assembly process planning pipeline for industrial electronic equipment based on knowledge graph with bidirectional extracted knowledge from historical process documents", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10845-024-02423-1", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "A review: Knowledge reasoning over knowledge graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112948", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Extraction of Formal Manufacturing Rules from Unstructured English Text", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "134", "Issue": "", "Page": "102990", "JournalTitle": "Computer-Aided Design"}, {"Title": "A novel cutting tool selection approach based on a metal cutting process knowledge graph", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "11-12", "Page": "3201", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A novel knowledge graph-based optimization approach for resource allocation in discrete manufacturing workshops", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102160", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Domain-specific knowledge graphs: A survey", "Authors": "Bilal Abu-Salih", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "103076", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "“FabNER”: information extraction from manufacturing process science domain literature using named entity recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "8", "Page": "2393", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Knowledge Graphs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "An automatic method for constructing machining process knowledge base from knowledge graph", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102222", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "An end-to-end tabular information-oriented causality event evolutionary knowledge graph for manufacturing documents", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101441", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A knowledge graph-based data representation approach for IIoT-enabled cognitive manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101515", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Weighted quantile discrepancy-based deep domain adaptation network for intelligent fault diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "240", "Issue": "", "Page": "108149", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "KRYSTAL: Knowledge graph-based framework for tactical attack discovery in audit data", "Authors": "<PERSON> Kurniawan; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "102828", "JournalTitle": "Computers & Security"}, {"Title": "Relation extraction for manufacturing knowledge graphs based on feature fusion of attention mechanism and graph convolution network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "255", "Issue": "", "Page": "109703", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Dynamic knowledge modeling and fusion method for custom apparel production process based on knowledge graph", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101880", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Knowledge graph-based manufacturing process planning: A state-of-the-art review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "70", "Issue": "", "Page": "417", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 115645540, "Title": "ML-Based Quantitative Analysis of Linguistic and Speech Features Relevant in Predicting Alzheimer’s Disease", "Abstract": "<p>Alzheimer’s disease (AD) is a severe neurological condition that affects numerous people globally with detrimental consequences. Detecting AD early is crucial for prompt treatment and effective management. This study presents a novel approach for detecting and classifying six types of cognitive impairment using speech-based analysis, including probable AD, possible AD, mild cognitive impairment (MCI), memory impairments, vascular dementia, and control. The method employs speech data from DementiaBank’s Pitt Corpus, which is preprocessed and analyzed to extract pertinent acoustic features. The characteristics are subsequently used to educate five machine learning algorithms, namely k-nearest neighbors (KNN), decision tree (DT), support vector machine (SVM), XGBoost, and random forest (RF). The effectiveness of every algorithm is assessed through a 10-fold cross-validation. According to the research findings, the suggested method based on speech obtains a total accuracy of 75.59% concerning the six-class categorization issue. Among the five machine learning algorithms tested, the XGBoost classifier showed the highest accuracy of 75.59%. These findings indicate that speech-based approaches can potentially be valuable for detecting and classifying cognitive impairment, including AD. The paper also explores robustness testing, evaluating the algorithms’ performance under various circumstances, such as noise variability, voice quality changes, and accent variations. The proposed approach can be developed into a noninvasive, cost-effective, and accessible diagnostic tool for the early detection and management of cognitive impairment.</p>", "Keywords": "Alzheimer’s disease;Feature selection;Machine learning;Deep learning", "DOI": "10.14201/adcaij.31625", "PubYear": 2024, "Volume": "13", "Issue": "", "JournalId": 20117, "JournalTitle": "ADCAIJ: <PERSON><PERSON><PERSON><PERSON> IN DISTRIBUTED COMPUTING AND ARTIFICIAL INTELLIGENCE JOURNAL", "ISSN": "", "EISSN": "2255-2863", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Sciences Department, MMMUT, Gorakhpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Sciences Department, MMMUT, Gorakhpur, India"}], "References": [{"Title": "A new machine learning method for identifying Alzheimer's disease", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "99", "Issue": "", "Page": "102023", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "An automatic Alzheimer’s disease classifier based on spontaneous spoken English", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "", "Page": "101298", "JournalTitle": "Computer Speech & Language"}]}, {"ArticleId": 115645574, "Title": "Understanding Self-Directed Learning in AI-Assisted Writing: A Mixed Methods Study of Postsecondary Learners", "Abstract": "This study investigates how postsecondary learners employ generative AI, specifically ChatGPT, to support their self-directed learning (SDL) for writing purposes. Following a sequential mixed methods design, we analyzed 384 survey responses and 10 semi-structured interviews with postsecondary writers. Findings suggest that the major learning task that the learners used ChatGPT for writing is brainstorming and seeking inspiration for ideas. While the entering motivation for using ChatGPT varies from curiosity about innovative technologies to fulfilling academic requirements, such entering motivation transformed into task motivation when the learners perceived the potential benefits of ChatGPT for assisting their writing. In terms of self-management, participants mostly demonstrated a high responsibility towards their own learning with ChatGPT and employed various strategies for SDL. Although survey respondents demonstrated a comparatively low level of self-monitoring, most interviewees claimed that they critically reflected on their learning process and validated information provided by ChatGPT. There are mixed opinions regarding whether the writing skills have improved as a result of using ChatGPT. Some participants suggested that the benefits brought by ChatGPT, such as alleviating social pressure and receiving instant feedback at any time, encouraged them to spend more time practicing writing and making revisions. However, some argue that assessing their AI-assisted SDL learning progress in the short term is challenging. This study addresses gaps in the existing literature where there is scarce, large-scale empirical research on self-directed AI usage in writing, shedding light on the emerging phenomenon of utilizing generative AI as a means of SDL in writing.", "Keywords": "Artificial intelligence; ChatGPT; Self-directed learning; Writing; Motivation", "DOI": "10.1016/j.caeai.2024.100247", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Writing, Colby College, 5290 Mayflower Hill, Waterville, ME, 04901, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Instructional Systems Technology, Indiana University Bloomington, 201 N Rose Ave, Education Building 3022, Bloomington, IN, 47405, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Instructional Systems Technology, Indiana University Bloomington, 201 N Rose Ave, Education Building 3022, Bloomington, IN, 47405, USA"}], "References": [{"Title": "Fostering self-directed learning in MOOCs: Motivation, learning strategies, and instruction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "153", "JournalTitle": "Online Learning"}, {"Title": "Reconceptualizing Self-directed Learning in the Era of Generative AI: An Exploratory Analysis of Language Learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "", "Page": "1489", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": 115645600, "Title": "The Application of Improved YOLOE Algorithm for Platen State Detection", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.145130", "PubYear": 2024, "Volume": "14", "Issue": "5", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [], "References": []}, {"ArticleId": 115645622, "Title": "<PERSON><PERSON><PERSON> Kualitas Produk Cacat Di PT KPM Menggunakan Pendekatan Six Sigma", "Abstract": "<p>PT. KPM merupakan salah satu perusahaan manufaktur yang bergerak dalam proses produksi sirup. Dal<PERSON> proses produksi terkadang masih terdapat beberapa produk yang tidak sesuai dengan yang diharapkan. Dimana kualitas produk yang dihasilkan tidak sesuai dengan standar atau mengalami kerusakan atau cacat produk. Hal tersebut akan membuat perusahaan mengalami kerugian karena target cacat perusahaan sendiri sebesar 2%. Metode yang digunakan yaitu Six Sigma dengan tahapan DMAIC (Define, Measure, Analyze, Improve, dan Control). Data yang didapatkan terdapat produk cacat yang terjadi pada observasi 1 hingga 30 yang dimulai pada periode 22 Maret hingga 28 Juni 2022. Jenis cacat berupa terdapat bercak kotoran di dalam botol kemasan botol kaca dan pecah botol. Faktor produk cacat berupa faktor, manusia, mesin, material, dan metode. Perbaikan untuk mengurangi faktor penyebab terjadinya cacat botol dengan faktor pekerja yang kurang memeperhatikan kebersihan lingkungan sekitar serta penerapan APD saat bekerja. Sehingga dalam hal dapat dilakukan penegasan mengenai SOP kebersihan. Dikarenakan SOP dapat digunakan sebagai acuan dalam menjalankan proses produksi, dengan adanya SOP maka kualitas mutu suatu produk dapat selalu terjaga.</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i3.10151", "PubYear": 2024, "Volume": "7", "Issue": "3", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Islam Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Islam Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Islam Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Islam Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Islam Indonesia"}], "References": []}, {"ArticleId": 115645804, "Title": "Analyzing Voting Power in Decentralized Governance: Who controls DAOs?", "Abstract": "We empirically study the state of three prominent DAO governance systems on the Ethereum blockchain: Compound, Uniswap and Ethereum name service (ENS). In particular, we examine how the voting power is distributed in these systems. Using a comprehensive dataset of all governance token holders, delegates, proposals, and votes, we analyze who holds the voting power and how this power is being used to influence governance decisions. While we reveal that the majority of voting power is concentrated in the hands of a small number of addresses, we rarely observe these powerful entities overturning a vote by choosing a different outcome than that of the overall community and less influential voters.", "Keywords": "Decentralized autonomous organization; DAO governance; Liquid democracy; Blockchain", "DOI": "10.1016/j.bcra.2024.100208", "PubYear": 2024, "Volume": "5", "Issue": "3", "JournalId": 83117, "JournalTitle": "Blockchain: Research and Applications", "ISSN": "2096-7209", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ETH Zurich, 8092, Zurich, Switzerland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ETH Zurich, 8092, Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETH Zurich, 8092, Zurich, Switzerland"}], "References": []}, {"ArticleId": 115645882, "Title": "Quantum Color Image Watermarking Scheme Based on Quantum Walk and Pixel Blocking Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.145128", "PubYear": 2024, "Volume": "14", "Issue": "5", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "骞卉 王", "Affiliation": ""}], "References": []}, {"ArticleId": 115645954, "Title": "Gradient descent algorithm for the optimization of fixed priorities in real-time systems", "Abstract": "This paper considers the offline assignment of fixed priorities in partitioned preemptive real-time systems where tasks have precedence constraints. This problem is crucial in this type of systems, as having a good fixed priority assignment allows for an efficient use of the processing resources while meeting all the deadlines. In the literature, we can find several proposals to solve this problem, which offer varying trade-offs between the quality of their results and their computational complexities. In this paper , we propose a new approach, leveraging existing algorithms that are widely exploited in the field of Machine Learning: Gradient Descent, the Adam Optimizer, and Gradient Noise. We show how to adapt these algorithms to the problem of fixed priority assignment in conjunction with existing worst-case response time analyses. We demonstrate the performance of our proposal on synthetic task-sets with different sizes. This evaluation shows that our proposal is able to find more schedulable solutions than previous heuristics, approximating optimal but intractable algorithms such as MILP or brute-force, while requiring reasonable execution times.", "Keywords": "Real-time; Fixed-priorities; Optimization; Gradient descent", "DOI": "10.1016/j.sysarc.2024.103198", "PubYear": 2024, "Volume": "153", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Software Engineering and Real-Time Group, Universidad de Cantabria, Avda. de los Castros 48, Santander, 39005, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Software Engineering and Real-Time Group, Universidad de Cantabria, Avda. de los Castros 48, Santander, 39005, Spain"}, {"AuthorId": 3, "Name": "Ana Guasque", "Affiliation": "Instituto de Automática e Informática Industrial (ai2), Universitat Politècnica de València, Camino de Vera, s/n, Valencia, 46022, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Instituto de Automática e Informática Industrial (ai2), Universitat Politècnica de València, Camino de Vera, s/n, Valencia, 46022, Spain"}], "References": [{"Title": "A comprehensive survey of industry practice in real-time systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "58", "Issue": "3", "Page": "358", "JournalTitle": "Real-Time Systems"}, {"Title": "Priority assignment in hierarchically scheduled time-partitioned distributed real-time systems with multipath flows", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "102339", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Optimal priority assignment for real-time systems: a coevolution-based approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "6", "Page": "142", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 115645974, "Title": "Predicting software vulnerability based on software metrics: a deep learning approach", "Abstract": "<p>The security of IT systems is the topmost priority of software developers. Software vulnerabilities undermine the security of computer systems. Lately, there have been a lot of reported issues of software vulnerabilities recorded in individuals and corporate systems. Software metrics-based techniques have been used and found to be effective due to low false positives and false negatives. However, there are software metrics that have not been investigated and could have an impact on the performance of the predictive model. In this study, we developed an ensemble deep learning algorithm made of LSTM, CNN, and MLP models with the non-investigated metrics as a feature to learn the latent representation of code metrics to predict anomalies in source code effectively. We trained the proposed model on the SySeVR datasets and compared the model's performance against five deep learning algorithms and four state-of-the-art SVPs. The proposed model performs better than the other deep learning with a classification accuracy of 96.17%, and a precision of 97.72%. The model's classification accuracy is 4.98% higher than that of the five deep learning models and 1.71% lower in precision than the state-of-the-art SVP.</p>", "Keywords": "Software vulnerabilities; Software metrics; Deep learning; Machine learning; Software security", "DOI": "10.1007/s42044-024-00195-8", "PubYear": 2024, "Volume": "7", "Issue": "4", "JournalId": 6478, "JournalTitle": "Iran Journal of Computer Science", "ISSN": "2520-8438", "EISSN": "2520-8446", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Kwame Nkrumah University of Science and Technology, Kumasi, Ghana; School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang, China; Department of Information Technology, Faculty of Computing and Information Systems, Ghana Communication Technology University, Accra, Ghana; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Kwame Nkrumah University of Science and Technology, Kumasi, Ghana"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang, China"}], "References": [{"Title": "Security and privacy in 6G networks: New areas and new challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "3", "Page": "281", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Using software metrics for predicting vulnerable classes and methods in Java projects: A machine learning approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "e2303", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "Detecting vulnerability in source code using CNN and LSTM network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "2", "Page": "1131", "JournalTitle": "Soft Computing"}, {"Title": "Machine Learning–based Cyber Attacks Targeting on Controlled Information", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Detecting and Augmenting Missing Key Aspects in Vulnerability Descriptions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Just-in-time software vulnerability detection: Are we there yet?", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "188", "Issue": "", "Page": "111283", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Learning from what we know: How to perform vulnerability prediction using noisy historical data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "7", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "A security vulnerability predictor based on source code metrics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "19", "Issue": "4", "Page": "615", "JournalTitle": "Journal of Computer Virology and Hacking Techniques"}]}, {"ArticleId": 115645992, "Title": "Multi-objective Evolutionary Search of Variable-length Composite Semantic Perturbations", "Abstract": "Deep neural networks have proven to be vulnerable to adversarial attacks in the form of adding specific perturbations on images to make wrong outputs. Among that, semantic perturbations attract increasing attention due to their naturalness and physical realizability. However, existing works about semantic perturbations have two limitations. On the one hand, the type of devised semantic perturbations is single, which easily causes poor attack performance, especially on the corresponding defensed models. On the other hand, composite semantic perturbations (CSP) improve the attack performance by integrating multiple semantic perturbations but lacks the role of automatic optimization, such as the attack order, which also leads to relatively low generalizability. To address these problems, we propose a novel method called multi-objective evolutionary search of variable-length composite semantic perturbations (MES-VCSP). Specifically, we construct the mathematical model of variable-length CSP, which allows the same type of attacks to be performed multiple times, realizing a more efficient attack. Besides, we introduce the multi-objective evolutionary search consisting of NSGA-II and neighborhood search to find near-optimal variable-length attack sequences. Experimental results on multiple image classification datasets show that compared with CSP, MES-VCSP can obtain adversarial examples with a higher attack success rate, more naturalness, and less time cost.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120827", "PubYear": 2024, "Volume": "677", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "Jialiang Sun", "Affiliation": "Defense Innovation Institute, Chinese Academy of Military Science, Beijing, 100071, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Defense Innovation Institute, Chinese Academy of Military Science, Beijing, 100071, China;Corresponding authors"}, {"AuthorId": 3, "Name": "Tingsong Jiang", "Affiliation": "Defense Innovation Institute, Chinese Academy of Military Science, Beijing, 100071, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Defense Innovation Institute, Chinese Academy of Military Science, Beijing, 100071, China"}], "References": [{"Title": "Bayesian evolutionary optimization for crafting high-quality adversarial examples with limited query budget", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110370", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 115646004, "Title": "Retraction Note: Age and gender classification using Seg-Net based architecture and machine learning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19577-y", "PubYear": 2024, "Volume": "83", "Issue": "23", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ECE Department, Punjab Engineering College, Chandigarh, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ECE Department, Punjab Engineering College, Chandigarh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "EE Department, Punjab Engineering College, Chandigarh, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "E&TC Department, Symbiosis International (Deemed University), Symbiosis Institute of Technology, Pune, India"}], "References": []}, {"ArticleId": 115646013, "Title": "On-machine detection of face milling cutter damage based on machine vision", "Abstract": "<p>Aiming at the problems of uneven illumination at the edge of the surface damage image of the face milling cutter, the blade line at the damage cannot be accurately identified, and the slow recognition rate of the traditional image processing technology, an on-machine accurate measurement method for the surface damage of the face milling cutter, is proposed. The industrial camera, lens, and adjustable LED ring lighting are placed on the camera support, and they are used to collect the image of the flank face of the face milling cutter. This method first divides the tool damage area into the tool wear bright zone area and the damaged edge missing area and selects the specific tool position area for template matching. Next, the tool damage area is extracted, the arc fitting method is used to fit the missing area of the broken edge, the edge curve is reshaped, and the improved sub-pixel edge detection method is used to extract the lower boundary of the tool wear bright zone area. The cutting edge curve of the damaged area and the lower boundary edge curve of the wear area are spliced to obtain the tool damage area, and finally, the maximum damage width value of the tool flank is calculated. The on-machine detection platform is built to carry out the milling experiment of the face milling cutter. The damage value extracted by this method is compared with the damage value measured by the super depth of field microscope. The average difference in the tool damage value is within 3%. The results show that the proposed method can effectively detect tool damage under the premise of ensuring efficiency on-line and in-process and provides an effective solution for the condition monitoring of face milling cutter in actual machining.</p>", "Keywords": "On-machine measurement; Tool damage; Machine vision; Cutting tool edge reconstruction", "DOI": "10.1007/s00170-024-13818-y", "PubYear": 2024, "Volume": "133", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Manufacturing and Intelligent Technology, Ministry of Education, Harbin University of Science and Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Manufacturing and Intelligent Technology, Ministry of Education, Harbin University of Science and Technology, Harbin, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Manufacturing and Intelligent Technology, Ministry of Education, Harbin University of Science and Technology, Harbin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Advanced Manufacturing and Intelligent Technology, Ministry of Education, Harbin University of Science and Technology, Harbin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Manufacturing and Intelligent Technology, Ministry of Education, Harbin University of Science and Technology, Harbin, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "<PERSON> School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, USA"}], "References": [{"Title": "A hybrid information model based on long short-term memory network for tool condition monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "6", "Page": "1497", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Use of image processing to monitor tool wear in micro milling", "Authors": "<PERSON>-<PERSON><PERSON>; <PERSON><PERSON>-<PERSON>; <PERSON>-<PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "333", "JournalTitle": "Neurocomputing"}, {"Title": "Chisel edge wear measurement of high-speed steel twist drills based on machine vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "128", "Issue": "", "Page": "103436", "JournalTitle": "Computers in Industry"}, {"Title": "Visual high-precision detection method for tool damage based on visual feature migration and cutting edge reconstruction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "5-6", "Page": "1341", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Classification of Tool Wear State based on Dual Attention Mechanism Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "83", "Issue": "", "Page": "102575", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 115646046, "Title": "FSVNet: Federated Self-driving Vehicle Network based on decomposition global convergence", "Abstract": "Autonomous vehicles (AVs) are expected to communicate with human drivers in heterogeneous environments. By leveraging the cloud-based capabilities of distributed learning, fleets of AVs could continuously retrain and improve trajectory forecasting models based on collective experience; however, these robots (AVs) should simultaneously avoid uploading raw driver interaction data to ensure that proprietary policies are protected (when sharing data with different stakeholders or entities) so that drivers’ privacy is not inadvertently compromised. Federated Learning (FL) is a widely employed methodology for learning models on cloud servers from a variety of users without divulging personal data. The problem with FL, nevertheless, is that it learns suboptimal models when the user data is of a heterogeneous distribution, which is a key characteristic of human–robot interactions. A novel variant of individualized FL and a new model called FSVNet are proposed in this article to tailor robust robot learning models to diverse distributions of users. The effectiveness of the FSVNet is illustrated via three publicly available datasets. Comparative studies with the state-of-the-art methodologies illustrate the superior performance of the proposed system.", "Keywords": "", "DOI": "10.1016/j.future.2024.05.059", "PubYear": 2024, "Volume": "160", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> Department of Construction Management, Louisiana State University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechatronic Systems Engineering, Simon Fraser University, 250-13450, 102 Avenue, Surrey, B.C., V3T 0A3, Canada;Corresponding author"}], "References": [{"Title": "Advances and Open Problems in Federated Learning", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1–2", "Page": "1", "JournalTitle": "Foundations and Trends® in Machine Learning"}]}, {"ArticleId": 115646090, "Title": "An Interval-valued Spherical Fuzzy CIMAS-WISP Group Decision-analytic Model for Blockchain Platform Selection in Digital Projects", "Abstract": "Digital projects aspiring to reach target audiences are executed through decentralized and trustworthy blockchain platforms (BPs). Once the objectives and target audience of a digital project are defined, the selection of suitable BPs is undertaken. The primary objective of this research is to develop a decision support system that aids in the selection of BPs for transferring digital data and assets. Numerous quantitative parameters determine the performance of BPs, alongside qualitative parameters indicating their performance. In this study, the aim is to determine the performance of BPs based on both qualitative and quantitative parameters. Within this scope, a multi-criteria decision-making approach and interval-valued spherical fuzzy (IVSF) sets are adopted. IVSF sets are utilized to determine expert importance levels. The IVSF-criteria importance assessment (CIMAS) method is introduced for the weighting of criteria. IVSF-CIMAS enables the determination of reliability levels in calculating criterion weights. The IVSF-simple weighted sum product (WISP) method is formulated to obtain the performance ranking of BPs. Thus, in this research, the IVSF-CIMAS-WISP hybrid model is developed, and an algorithm for this novel decision-analytic model is presented. A case study is developed focusing on BP selection for a digital project to demonstrate the applicability of the proposed hybrid model. The robustness of IVSF-CIMAS-WISP is tested through extensive sensitivity analysis scenarios. According to the research results, the applicability of the IVSF-CIMAS-WISP hybrid method is supported and its robustness is confirmed. The research findings provide numerous insights for project managers and practitioners.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111810", "PubYear": 2024, "Volume": "162", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Logistics Program, Artvin Çoruh University, Artvin 08300, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mathematics Program, Kırıkkale University, Yahşihan/Kırıkkale 71450, Turkey"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Belgrade, Faculty of Transport and Traffic Engineering, Vojvode Stepe 305, Belgrade 11010, Serbia;Yuan Ze University, College of Engineering, Department of Industrial Engineering and Management, Taoyuan City 320315, Taiwan;Department of Computer Science and Engineering, College of Informatics, Korea University, Seoul 02841, Republic of Korea;Corresponding author at: University of Belgrade, Faculty of Transport and Traffic Engineering, Vojvode Stepe 305, Belgrade 11010, Serbia"}, {"AuthorId": 4, "Name": "Çağatay Korkuç", "Affiliation": "Electrics Electronics Engineering, Faculty of Engineering, Gazi University, Ankara 06500, Turkey"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Logistics Program, Artvin Çoruh University, Artvin 08300, Turkey"}, {"AuthorId": 6, "Name": "Erkan <PERSON>", "Affiliation": "Electrics Electronics Engineering, Faculty of Engineering, Gazi University, Ankara 06500, Turkey"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Operations Research and Statistics, Faculty of Organizational Sciences, University of Belgrade, Belgrade, Serbia;Department of Mechanics and Mathematics, Western Caspian University, Baku, Azerbaijan;School of Engineering and Technology, Sunway University, Selangor, Malaysia"}], "References": [{"Title": "Barriers to implementation of blockchain into supply chain management using an integrated multi-criteria decision-making method: a numerical example", "Authors": "<PERSON><PERSON><PERSON>ü<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "19", "Page": "14771", "JournalTitle": "Soft Computing"}, {"Title": "A survey of blockchain consensus algorithms performance evaluation criteria", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "113385", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Token Economy", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "4", "Page": "457", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "A multi-criteria decision making method based on DNMA and CRITIC with linguistic D numbers for blockchain platform evaluation", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104200", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A survey of consensus algorithms in public blockchain systems for crypto-currencies", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "103035", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "On the suitability of blockchain platforms for IoT applications: Architectures, security, privacy, and performance", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Gueltoum Bendiab", "PubYear": 2021, "Volume": "191", "Issue": "", "Page": "108005", "JournalTitle": "Computer Networks"}, {"Title": "Blockchain technology for bridging trust, traceability and transparency in circular supply chain", "Authors": "<PERSON><PERSON>; <PERSON>; Pasquale Del Vecchio", "PubYear": 2022, "Volume": "59", "Issue": "7", "Page": "103508", "JournalTitle": "Information & Management"}, {"Title": "A Survey on Blockchain Interoperability: Past, Present, and Future Trends", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "User experience framework for understanding user experience in blockchain services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "158", "Issue": "", "Page": "102733", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Evolutionary privacy-preserving learning strategies for edge-based IoT data sharing schemes", "Authors": "Yizhou Shen; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "4", "Page": "906", "JournalTitle": "Digital Communications and Networks"}, {"Title": "A MCDM-based framework for blockchain consensus protocol selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "117609", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Blockchain digital technology empowers E-commerce supply chain sustainable value co-creation decision and coordination considering online consumer reviews", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "109662", "JournalTitle": "Applied Soft Computing"}, {"Title": "Sharding for Scalable Blockchain Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Research on significant factors affecting adoption of blockchain technology for enterprise distributed applications based on integrated MCDM FCEM-MULTIMOORA-FG method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "118", "Issue": "", "Page": "105699", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "One-dimensional VGGNet for high-dimensional data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "110035", "JournalTitle": "Applied Soft Computing"}, {"Title": "The blockchain technology selection in the logistics industry using a novel MCDM framework based on Fermatean fuzzy sets and Dombi aggregation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Sanjib Biswas", "PubYear": 2023, "Volume": "635", "Issue": "", "Page": "345", "JournalTitle": "Information Sciences"}, {"Title": "Survey of Machine Learning based intrusion detection methods for Internet of Medical Things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "110227", "JournalTitle": "Applied Soft Computing"}, {"Title": "A decision-making framework for blockchain platform evaluation in spherical fuzzy environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120833", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Interval-valued spherical fuzzy MABAC method based on Dombi aggregation operators with unknown attribute weights to select plastic waste management process", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "110516", "JournalTitle": "Applied Soft Computing"}, {"Title": "Interval-valued Pythagorean fuzzy operational competitiveness rating model for assessing the metaverse integration options of sharing economy in transportation sector", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110806", "JournalTitle": "Applied Soft Computing"}, {"Title": "Privacy-preserving offloading scheme in multi-access mobile edge computing based on MADRL", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "183", "Issue": "", "Page": "104775", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Insights into Internet of Medical Things (IoMT): Data fusion, security issues and potential solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "102", "Issue": "", "Page": "102060", "JournalTitle": "Information Fusion"}, {"Title": "Towards an integrated framework for developing blockchain systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "180", "Issue": "", "Page": "114181", "JournalTitle": "Decision Support Systems"}]}, {"ArticleId": 115646107, "Title": "Global quantitative analysis and visualization of big data and medical devices based on bibliometrics", "Abstract": "<b >Background</b> In the big data era, the healthcare sector grapples with increased data volumes and the push for more intelligent medical devices. This challenge is marked by data silos, higher data processing needs, and the quest for personalized medicine, emphasizing the importance of integrating and analyzing diverse big data from medical devices. This study employs bibliometric analysis to thoroughly analyze the research trends in big data and medical device research, aiming to identify key developments, patterns, and potential future directions in the field. <b >Methods</b> This study employed the Web of Science Core Collection, BIOSIS Citation Index, and Derwent Innovations Index for conducting keyword searches on ’big data’ and ’medical devices’, focusing on English-language articles, reviews, and patents, while excluding duplicates. Quantitative analyses and visualizations were facilitated using tools such as R 4.3.1, VOSviewer, CiteSpace, and Tableau. The research assessed the impact of journals and academic contributions through metrics like the impact factor and G-Index. <b >Results</b> Our analysis covered 592 articles and 795 patents. Notably, the annual growth rate of articles reached 62.19%, with the primary contributions originating from China, the United States, India, and England. Among the identified publications, “ IEEE Access ” emerged as the most prominent journal. The research identified key trends in the application of big data within medical devices, including the extensive use of artificial intelligence (AI), deep learning, Internet of Things (IoT) technologies, genomic data analysis, and bioinformatics. Case studies show how big data and medical device integration, exemplified by Electronic Health Records (EHRs), Clinical Decision Support Systems (CDSS), and smart wearables, enhance diagnostic efficiency and care quality. The study further identified intensive care units, radiology departments, and oncology as key application departments leveraging this integration. <b >Conclusion</b> Big data and medical device integration have led to smart healthcare and personalized treatment, improving medical decision accuracy and inspiring new research directions. Future research efforts should explore the potential for incorporating even more expansive datasets and multilingual resources to further maximize the potential of this integration and advance medical technology in disease prevention, diagnosis, treatment, and management.", "Keywords": "Big data; Medical devices; Bibliometrics; Internet of Things; Deep learning", "DOI": "10.1016/j.eswa.2024.124398", "PubYear": 2024, "Volume": "254", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Xiaoyang Bai", "Affiliation": "Department of Medical Equipment, The First Affiliated Hospital, and College of Clinical Medicine of Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Clinical Laboratory, The First Affiliated Hospital, and College of Clinical Medicine of Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Medical Equipment, The First Affiliated Hospital, and College of Clinical Medicine of Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 4, "Name": "Shuaiqiang Fu", "Affiliation": "Department of Medical Equipment, The First Affiliated Hospital, and College of Clinical Medicine of Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Equipment, The First Affiliated Hospital, and College of Clinical Medicine of Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Equipment, The First Affiliated Hospital, and College of Clinical Medicine of Henan University of Science and Technology, Luoyang 471003, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Medical Equipment, The First Affiliated Hospital, and College of Clinical Medicine of Henan University of Science and Technology, Luoyang 471003, China;Corresponding author"}], "References": [{"Title": "HealthFog: An ensemble deep learning based Smart Healthcare System for Automatic Diagnosis of Heart Diseases in integrated IoT and fog computing environments", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "187", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Apply IOT technology to practice a pandemic prevention body temperature measurement system: A case study of response measures for COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "5", "Page": "155014772110181", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Artificial intelligence with big data analytics-based brain intracranial hemorrhage e-diagnosis using CT images", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "22", "Page": "16037", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Big data analytics in Cloud computing: an overview", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "24", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Blockchain based Securing Medical Records in Big Data Analytics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "102122", "JournalTitle": "Data & Knowledge Engineering"}, {"Title": "Analysis of Medical Slide Images Processing using Depth Learning in Histopathological Studies of Cerebellar Cortex Tissue", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "611", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "An intelligent Medical Cyber–Physical System to support heart valve disease screening and diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121772", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 115646114, "Title": "UnseenSignalTFG: a signal-level expansion method for unseen acoustic data based on transfer learning", "Abstract": "<p>This study introduces a transfer learning-based approach for signal-level expansion of unseen acoustic signal data, aiming to address the scarcity of acoustic signal data in a specific domain. By establishing connections and sharing knowledge between the source and target domains, the method successfully mitigates cross-domain disparities, overcoming challenges posed by unavailable data in the target domain, thereby elevating the quality and precision of data expansion. Diverging from conventional methods that predominantly emphasize feature-level expansion, the proposed approach accentuates the preservation of data signal integrity and effectively achieves the expansion of unseen class samples within the target domain.The effectiveness of this method has been validated across four different types of signal datasets. In the bearing dataset, the expansion of unseen data achieved accuracies of 99% at the signal level and 95% at the spectral level. These experimental results not only demonstrate the method’s advantages in augmenting both seen and unseen data but also highlight its effectiveness and application potential in achieving comprehensive expansion of target signals.</p>", "Keywords": "Signal; Transfer learning; Unseen data expansion", "DOI": "10.1007/s10489-024-05568-x", "PubYear": 2024, "Volume": "54", "Issue": "13-14", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shaanxi Key Laboratory of Network Data Analysis and Intelligent Processing, Xi’an, China; School of Computer Science and Technology, Xi’an University of Post and Telecommunications, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shaanxi Key Laboratory of Network Data Analysis and Intelligent Processing, Xi’an, China; School of Computer Science and Technology, Xi’an University of Post and Telecommunications, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Key Laboratory of Network Data Analysis and Intelligent Processing, Xi’an, China; School of Computer Science and Technology, Xi’an University of Post and Telecommunications, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Key Laboratory of Network Data Analysis and Intelligent Processing, Xi’an, China; School of Computer Science and Technology, Xi’an University of Post and Telecommunications, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shaanxi Key Laboratory of Network Data Analysis and Intelligent Processing, Xi’an, China; School of Computer Science and Technology, Xi’an University of Post and Telecommunications, Xi’an, China; Xi’an Key Laboratory of Big Data and Intelligent Computing, Xi’an, China; Corresponding author."}], "References": [{"Title": "Stacked pruning sparse denoising autoencoder based intelligent fault diagnosis of rolling bearings", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106060", "JournalTitle": "Applied Soft Computing"}, {"Title": "DENS-ECG: A deep learning approach for ECG signal delineation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113911", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A review on transfer learning in EEG signal analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "421", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "A hybrid fine-tuned VMD and CNN scheme for untrained compound fault diagnosis of rotating machinery with unequal-severity faults", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114094", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep multi-scale adversarial network with attention: A novel domain adaptation method for intelligent fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "565", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A multi-source information transfer learning method with subdomain adaptation for cross-domain fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "243", "Issue": "", "Page": "108466", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Reinforcement learning-based saturated adaptive robust neural-network control of underactuated autonomous underwater vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "116714", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Vibration signal-based early fault prognosis: Status quo and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101609", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "SentATN: learning sentence transferable embeddings for cross-domain sentiment classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "15", "Page": "18101", "JournalTitle": "Applied Intelligence"}, {"Title": "Bearing fault diagnosis using transfer learning and self-attention ensemble lightweight convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "501", "Issue": "", "Page": "765", "JournalTitle": "Neurocomputing"}, {"Title": "A Survey on Cross-domain Recommendation: Taxonomies, Methods, and Future Directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "A novel water pollution detection method based on acoustic signals and long short-term neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "10", "Page": "12355", "JournalTitle": "Applied Intelligence"}, {"Title": "A bearing fault diagnosis method without fault data in new working condition combined dynamic model with deep learning", "Authors": "Kun Xu; Xianguang Kong; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "", "Page": "101795", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An effective zero-shot learning approach for intelligent fault detection using 1D CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "12", "Page": "16041", "JournalTitle": "Applied Intelligence"}, {"Title": "Video surveillance using deep transfer learning and deep domain adaptation: Towards better generalization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105698", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Time-series anomaly detection with stacked Transformer representations and 1D convolutional network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105964", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "MuseFlow: music accompaniment generation based on flow", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "20", "Page": "23029", "JournalTitle": "Applied Intelligence"}, {"Title": "A fault diagnosis method for few-shot industrial processes based on semantic segmentation and hybrid domain transfer learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "23", "Page": "28268", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 115646203, "Title": "Ni80Fe20 Thickness Optimization of Magnetoplasmonic Crystals for Magnetic Field Sensing", "Abstract": "A promising approach to enhance the transverse Kerr effect with potential applications in the detection of weak magnetic fields is the use of magnetoplasmonic crystals based on ferromagnetic metals. The sensitivity, measuring field range, and limit-of-detection of 1D magnetoplasmonic crystals with 5–20 nm thick Ni<sub>80</sub>Fe<sub>20</sub> layers are analyzed in this study based on magnetic, optical, and magneto-optical characterization. The magnetoplasmonic crystal with 10 nm-thick Ni<sub>80</sub>Fe<sub>20</sub> layer provided a sensitivity of 21.9 µV/mOe, a limit-of-detection of 3.6 mOe, and a measuring field range of 1.134 Oe. This sample was also utilized as a magnetic field probe to reconstruct the magnetic configuration of a multicore cable and a planar induction coil, thereby highlighting its potential for the visualization of DC magnetic fields.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115552", "PubYear": 2024, "Volume": "376", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Baltic Federal University, Kaliningrad 236041, Russia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Kant Baltic Federal University, Kaliningrad 236041, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lomonosov Moscow State University, Moscow 119991, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Max Planck Institute for Intelligent Systems, Stuttgart 70569, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Max Planck Institute for Intelligent Systems, Stuttgart 70569, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lomonosov Moscow State University, Moscow 119991, Russia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Lomonosov Moscow State University, Moscow 119991, Russia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Kant Baltic Federal University, Kaliningrad 236041, Russia"}], "References": []}, {"ArticleId": 115646228, "Title": "New Framework for Detecting the suitable Supplier of Smart Systems Based on the effect of Internet of Things", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijmsc.2024.02.03", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 28282, "JournalTitle": "International Journal of Mathematical Sciences and Computing", "ISSN": "2310-9025", "EISSN": "2310-9033", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computers and Informatics, Zagazig University, Egypt"}], "References": []}, {"ArticleId": 115646297, "Title": "Practical algorithms for weakly flexible job scheduling for smart mold component process", "Abstract": "In this paper, we focus on a novel troublesome practical challenge, termed Weakly Flexible Job-Shop Scheduling (WFJSP) with parallel machines, where we are required to schedule the jobs with many unconventional limitations, including maximum machine usage constrained by computing resources (single server with limited memory), machines ( i.e. , idle time, office hours and efficiency), mold components ( i.e. , idle time, counts, and uncertain processes), and processes ( i.e. , types and orders) on self-developed intelligent mold processing system hosted by a single resource-limited server. We first highlight the pros and cons of pure theoretical works by comparing with multiple jop-shop scheduling methods, then emphasize the necessity of considering system resource utilization from perspective of software quality. We then shed light on the definitions of different job-shop scheduling problems and clarify the novel scenario at length with six innate conflicts of WFJSP. Based on these detailed analysis, three methods are devised and designed inspired by combining greedy algorithm, ranking strategy, mature infrastructures and well-designed system hierarchy. Experiments are conducted on our self-developed intelligent mold processing system. As a baseline, we introduce a genetic algorithm specifically designed for WFJSP, named WFJSP-GeneA. To evaluate the efficacy and practicability of these four approaches, we adopt five metrics dependent on the software quality attributes. Our proposed algorithms outperform on all the metrics compared with the baseline WFJSP-GeneA. Particularly, we observe outstanding performance of our algorithms on the metrics of Parsimony , Reliability , and Performance . We thus consider that proposed algorithms overcome WFJSP and are practically applicable on production system using mature infrastructures and well-designed system hierarchy.", "Keywords": "", "DOI": "10.1016/j.future.2024.05.058", "PubYear": 2024, "Volume": "160", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Hanyang University, Seoul 04763, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Hanyang University, Seoul 04763, South Korea;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Nanotechnology, Daegu Gyeongbuk Institute of Science & Technology (DGIST), Deagu 42988, South Korea;Corresponding authors"}], "References": [{"Title": "A self-learning genetic algorithm based on reinforcement learning for flexible job-shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106778", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Product and design feature-based similar process retrieval and modeling for mold manufacturing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Kwangyeol Ryu", "PubYear": 2021, "Volume": "115", "Issue": "3", "Page": "703", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Dynamic opposite learning enhanced dragonfly algorithm for solving large-scale flexible job shop scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "238", "Issue": "", "Page": "107815", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey of job shop scheduling problem: The types and models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "142", "Issue": "", "Page": "105731", "JournalTitle": "Computers & Operations Research"}, {"Title": "An improved genetic algorithm for flexible job shop scheduling problem considering reconfigurable machine tools with limited auxiliary modules", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "650", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "An effective two-stage algorithm based on convolutional neural network for the bi-objective flexible job shop scheduling problem with machine breakdown", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117460", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": *********, "Title": "Convex-Optimization-Based Model Predictive Control for Space Debris Removal Mission Guidance", "Abstract": "<p>A convex-optimization-based model predictive control (MPC) algorithm for the guidance of active debris removal missions is proposed in this work. A high-accuracy reference for the convex optimization is obtained through a split-Edelbaum approach that takes the effects of [Formula: see text], drag, and eclipses into account. When the spacecraft deviates significantly from the reference trajectory, a new reference is calculated through the same method to reach the target debris. When required, phasing is integrated into the transfer. During the mission, the phase of the spacecraft is adjusted to match that of the target debris at the end of the transfer by introducing intermediate waiting times. The robustness of the guidance scheme is tested in a high-fidelity dynamic model that includes thrust errors and misthrust events. The guidance algorithm performs well without requiring successive convex iterations. Monte Carlo simulations are conducted to analyze the impact of these thrust uncertainties on the guidance. Simulation results show that the proposed convex-MPC approach can ensure that the spacecraft can reach its target despite significant uncertainties and long-duration misthrust events.</p>", "Keywords": "Mathematical Optimization; Active Debris Removal; Model Predictive Control; Monte Carlo Simulation; Guidance and Navigational Algorithms; Orbital Elements; Model predictive control ; Eclipses; Duty cycle; Electric Propulsion", "DOI": "10.2514/1.G008089", "PubYear": 2024, "Volume": "47", "Issue": "9", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Auckland, Auckland 1010, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Auckland, Auckland 1010, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Auckland, Auckland 1010, New Zealand"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Auckland, Auckland 1010, New Zealand"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Technical University of Madrid, 28040 Madrid, Spain"}], "References": [{"Title": "Trajectory optimization for Rendezvous and Docking using Nonlinear Model Predictive Control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "1", "Page": "518", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Near-Linear Orbit Uncertainty Propagation Using the Generalized Equinoctial Orbital Elements", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "46", "Issue": "4", "Page": "654", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Equinoctial Lyapunov Control Law for Low-Thrust Rendezvous", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "46", "Issue": "4", "Page": "781", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Fast Model Predictive Control for Spacecraft Rendezvous and Docking with Obstacle Avoidance", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "46", "Issue": "5", "Page": "998", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Autonomous Spacecraft Rendezvous Using Tube-Based Model Predictive Control: Design and Application", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "46", "Issue": "7", "Page": "1243", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Low-Order Automatic Domain Splitting Approach for Nonlinear Uncertainty Mapping", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "47", "Issue": "2", "Page": "291", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 115646305, "Title": "Real-Time Classification Model of Public Emergencies Using Fusion Expansion Network: ", "Abstract": "<p>In today's deep learning-dominated era, real-time classification of public emergencies is a critical research area. Existing methods, however, often fall short in considering both temporal and spatial aspects comprehensively. This study introduces GEDNAS, a novel model that combines atrous convolutional neural network (DCNN), gated recurrent unit (GRU), and neural structure search (NAS) to address these limitations. GEDNAS utilizes DCNN to capture local spatio-temporal features, integrates GRU for time series modeling, and employs NAS for overall structural optimization. The approach significantly enhances real-time public emergency classification performance, showcasing its efficiency and accuracy in responding to real-time scenarios and providing robust support for emergency response efforts. This research introduces an innovative solution for public safety, advancing the application of deep learning in emergency management and inspiring the design of real-time classification models, ultimately enhancing overall societal safety.</p>", "Keywords": "", "DOI": "10.4018/JOEUC.345245", "PubYear": 2024, "Volume": "36", "Issue": "1", "JournalId": 11438, "JournalTitle": "Journal of Organizational and End User Computing", "ISSN": "1546-2234", "EISSN": "1546-5012", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou Maritime University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Imperial College London, UK"}], "References": [{"Title": "Machine learning based approach for multimedia surveillance during fire emergencies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "23-24", "Page": "16201", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Machine learning based approach for multimedia surveillance during fire emergencies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "23-24", "Page": "16201", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Exploring the role of deep neural networks for post-disaster decision support", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "113234", "JournalTitle": "Decision Support Systems"}, {"Title": "Exploring the role of deep neural networks for post-disaster decision support", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "113234", "JournalTitle": "Decision Support Systems"}, {"Title": "Sentiment analysis of Twitter data during critical events through Bayesian networks classifiers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "92", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Online-Offline Interactive Urban Crowd Flow Prediction Toward IoT-Based Smart City", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "6", "Page": "3417", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "HCFNN: High-order coverage function neural network for image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Yu", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108873", "JournalTitle": "Pattern Recognition"}, {"Title": "HCFNN: High-order coverage function neural network for image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Yu", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108873", "JournalTitle": "Pattern Recognition"}, {"Title": "SMedia: social media data analysis for emergency detection and its type identification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "4", "Page": "385", "JournalTitle": "International Journal of Computational Science and Engineering"}, {"Title": "A smoothing Group Lasso based interval type-2 fuzzy neural network for simultaneous feature selection and system identification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "111028", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A smoothing Group Lasso based interval type-2 fuzzy neural network for simultaneous feature selection and system identification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "111028", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 115646347, "Title": "A Gamified Platform to Support Educational Activities about Fake News in Social Media", "Abstract": "", "Keywords": "", "DOI": "10.1109/TLT.2024.3410088", "PubYear": 2024, "Volume": "17", "Issue": "", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Turin, Turin, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Political, Social, and Communication Sciences, University of Salerno, Fisciano, Italy"}, {"AuthorId": 3, "Name": "Federica Patti", "Affiliation": "Department of Computer Science, University of Turin, Turin, Italy"}, {"AuthorId": 4, "Name": "Ruggero G<PERSON>", "Affiliation": "Department of Computer Science, University of Turin, Turin, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Turin, Turin, Italy"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Turin, Turin, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Turin, Turin, Italy"}], "References": [{"Title": "Designing Engaging Games for Education: A Systematic Literature Review on Game Motivators and Design Principles", "Authors": "<PERSON><PERSON><PERSON>; Renny S. N. <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "804", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Can an online educational game contribute to developing information literate citizens?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "161", "Issue": "", "Page": "104057", "JournalTitle": "Computers & Education"}, {"Title": "A Storytelling Robot Managing Persuasive and Ethical Stances via ACT-R: An Exploratory Study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "12", "Page": "2115", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Misinformation Is Contagious: Middle school students learn how to evaluate and share information responsibly through a digital game", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "202", "Issue": "", "Page": "104832", "JournalTitle": "Computers & Education"}]}, {"ArticleId": *********, "Title": "IMAGE CLASSIFIER FOR FAST SEARCH IN LARGE DATABASES", "Abstract": "<p>Relevance. The avalanche-like growth in the amount of information on the Internet necessitates the development of effective methods for quickly processing such information in information systems. Clustering of news information is carried out by taking into account both the morphological analysis of texts and graphic content. Thus, an urgent task is the clustering of images accompanying textual information on various web resources, including news portals. The subject of study is an image classifier that exhibits low sensitivity to increased information in databases. The purpose of the article is to enhance the efficiency of searching for identical images in databases experiencing a daily influx of 10-12 thousand images, by developing an image classifier. Methods used: mathematical modeling, content-based image retrieval, two-dimensional discrete cosine transform, image processing methods, decision-making methods. The following results were obtained. An image classifier has been developed with low sensitivity to increased database information. The properties of the developed classifier have been analyzed. The experiments demonstrated that clustering information based on images using the developed classifier proved to be sufficiently fast and cost-effective in terms of information volumes and computational power requirements.</p>", "Keywords": "", "DOI": "10.20998/2522-9052.2024.2.02", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 52678, "JournalTitle": "Advanced Information Systems", "ISSN": "2522-9052", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115646637, "Title": "Evaluation of a Stereo Depth Camera for Part Monitoring For CNC Machining", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2025.91-106", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115646659, "Title": "IMPLEMENTATION OF FIS TSUKAMOTO ON THE DECISION MODEL FOR ASSESSING THE NUTRITIONAL CONDITION AND GROWTH OF TODDLERS IN POSYANDU (CASE STUDY: TIR<PERSON><PERSON><PERSON>YU VILLAGE)", "Abstract": "Masalah gizi dan gangguan pertumbuhan seperti risiko stunting, wasting  dan underweight  pada anak dapat diantisipasi sejak dini. Evaluasi terhadap status gizi dan perkembangan balita perlu didukung oleh suatu teknologi komputer yang menghimpun pengetahuan-pengetahuan terkait antropometri anak. Salah satu pendekatan untuk memodelkan masalah terkait perkembangan balita berbasis AI adalah logika fuzzy. Makalah ini akan membahas desain model pendukung keputusan untuk penilaian kondisi gizi dan perkembangan balita dengan menerapkan metode Fuzzy Inference System  (FIS) Tsukamoto. Berdasarkan seluruh hasil pengujian yang telah dilakukan dapat disimpulkan bahwa penerapan FIS Tsukamoto dapat menghasilkan model pendukung keputusan yang valid yakni memiliki akurasi sebesar 78% dalam menilai status gizi balita, akurasi 75% dalam menilai kondisi pertumbuhan berdasarkan BB/U dan akurasi 84 % untuk kondisi pertumbuhan berdasar TB/U .", "Keywords": "Model <PERSON><PERSON><PERSON><PERSON>; FIS <PERSON>;  Kondi<PERSON>; Pertumbuhan <PERSON>", "DOI": "10.22373/cj.v8i1.16951", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 52873, "JournalTitle": "Cyberspace: Jurnal Pendidikan Teknologi Informasi", "ISSN": "2598-2079", "EISSN": "2597-9671", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Islam Indonesia"}], "References": []}, {"ArticleId": 115646664, "Title": "The Fusion of Fuzzy Theories and Natural Language Processing: A State-of-the-Art Survey", "Abstract": "Recent years have witnessed a drastic surge in natural language processing (NLP), which is a popular research orientation in artificial intelligence. In contrast to precise numbers, human language is very complex and diverse, with millions of expressions, both spoken and written. It is due to this ambiguity and imprecision that most of the problems in NLP relating to cognition, translation, and understanding are non-trivial. Fuzzy theory, which accepts the fact that ambiguity exists, aims to address and actively quantify conceptual vagueness into messages that can be processed by computers. Following the thread of recent studies, we systematically review the fusion of fuzzy theory and NLP technologies from the aspects of commonly used fuzzy theories in NLP, the NLP tasks fuzzy theories are applied to, the application fields of fusion and the basic paradigms of fusion. Towards the end of this paper, we delineate the constraints and obstacles encountered in current researches, while also endeavoring to suggest avenues for enhancement that may serve as a reference for subsequent scholarly inquiry.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111818", "PubYear": 2024, "Volume": "162", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing 210007, China;The Sixty-third Research Institute, National University of Defense Technology, Nanjing 210007, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Command and Control Engineering College, Army Engineering University of PLA, Nanjing 210007, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610064, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Sixty-third Research Institute, National University of Defense Technology, Nanjing 210007, China"}], "References": [{"Title": "A comprehensive review of type-2 fuzzy Ontology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1187", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A Hybrid Fuzzy System via Topic Model for Recommending Highlight Topics of CQA in Developer Communities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "15", "Page": "2050248", "JournalTitle": "Journal of Circuits, Systems and Computers"}, {"Title": "An intelligent personalized web blog searching technique using fuzzy-based feedback recurrent neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "12", "Page": "9321", "JournalTitle": "Soft Computing"}, {"Title": "Smart job searching system based on information retrieval techniques and similarity of fuzzy parameterized sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Bassam <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "636", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "Public opinion mining using natural language processing technique for improvisation towards smart city", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "3", "Page": "561", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Emotion-enhanced classification based on fuzzy reasoning", "Authors": "R<PERSON>eng Yan; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "3", "Page": "839", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A Comprehensive Survey on Word Representation Models: From Classical to State-of-the-Art Word Representation Language Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "The Interval probabilistic double hierarchy linguistic EDAS method based on natural language processing basic techniques and its application to hotel online reviews", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "1517", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Multi-task Fuzzy Clustering–Based Multi-task TSK Fuzzy System for Text Sentiment Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Dependency syntax guided BERT-BiLSTM-GAM-CRF for Chinese NER", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "116682", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A fuzzy semantic representation and reasoning model for multiple associative predicates in knowledge graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "599", "Issue": "", "Page": "208", "JournalTitle": "Information Sciences"}, {"Title": "A graph convolutional network based on object relationship method under linguistic environment applied to film evaluation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1283", "JournalTitle": "Information Sciences"}, {"Title": "Deep attention fuzzy cognitive maps for interpretable multivariate time series prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "275", "Issue": "", "Page": "110700", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A graph attention network under probabilistic linguistic environment based on Bi-LSTM applied to film classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "649", "Issue": "", "Page": "119632", "JournalTitle": "Information Sciences"}, {"Title": "Multimodal fuzzy granular representation and classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "23", "Page": "29433", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 115646721, "Title": "Effects of Visual Risk Indicators on Phishing Detection Behavior: An Eye-Tracking Experiment", "Abstract": "Cybersecurity vulnerability ranks among the foremost global business risks. Phishing attempts, in particular through email, persistently challenge organizations despite substantial investments in IT security and awareness training. Recognizing the limitations of unilateral technology- or human-centered approaches, this study explores how visual risk indication can support employees in detecting phishing attempts. To do so, we conducted an eye-tracking lab experiment in which participants rated the trustworthiness of emails with varying levels of credibility. Our analysis focuses on human information processing in identifying phishing attempts, indicating that the availability of a visual risk indicator can significantly influence trust and response behavior, without incapacitating implicit phishing cues (such as conspicuous senders or anonymous recipients). Our findings suggest that organizations should appropriately calibrate visual risk indicators to achieve the intended guiding effects. However, the calibration remains a trade-off and depends on the organization’s environment. We discuss implications for integrative cybersecurity approaches to mitigate phishing attempts more effectively.", "Keywords": "Cybersecurity; Phishing; Human-computer interaction; Eye-tracking; Visual risk indicators", "DOI": "10.1016/j.cose.2024.103940", "PubYear": 2024, "Volume": "144", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Trust in Digital Services, Technical University Berlin, Berlin, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Trust in Digital Services, Technical University Berlin, Berlin, Germany"}], "References": [{"Title": "Trust calibration of automated security IT artifacts: A multi-domain study of phishing-website detection tools", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "1", "Page": "103394", "JournalTitle": "Information & Management"}, {"Title": "Eyes wide open: The role of situational information security awareness for security‐related behaviour", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "3", "Page": "429", "JournalTitle": "Information Systems Journal"}, {"Title": "Markov chain to analyze web usability of a university website using eye tracking data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "331", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}, {"Title": "Learning not to take the bait: a longitudinal examination of digital training methods and overlearning on phishing susceptibility", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "2", "Page": "238", "JournalTitle": "European Journal of Information Systems"}, {"Title": "Experimental Investigation of Technical and Human Factors Related to Phishing Susceptibility", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Social Computing"}, {"Title": "Applying machine learning and natural language processing to detect phishing email", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102414", "JournalTitle": "Computers & Security"}, {"Title": "A hybrid DNN–LSTM model for detecting phishing URLs", "Authors": "Alper Ozcan; Cagatay Catal; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "7", "Page": "4957", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Human Factors in Phishing Attacks: A Systematic Literature Review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Evaluation of Contextual and Game-Based Training for Phishing Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "4", "Page": "104", "JournalTitle": "Future Internet"}, {"Title": "Contextual security awareness: A context-based approach for assessing the security awareness of users", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "246", "Issue": "", "Page": "108709", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey of human-in-the-loop for machine learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "135", "Issue": "", "Page": "364", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Home is where your Gaze is – Evaluating effects of embedding regional cues in user interfaces", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "136", "Issue": "", "Page": "107369", "JournalTitle": "Computers in Human Behavior"}, {"Title": "It's not just about accuracy: An investigation of the human factors in users' reliance on anti-phishing tools", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "113846", "JournalTitle": "Decision Support Systems"}, {"Title": "The role of cue utilization in the detection of phishing emails", "Authors": "<PERSON>; Chelsea Valenzuela; Oliver Plate", "PubYear": 2023, "Volume": "106", "Issue": "", "Page": "103887", "JournalTitle": "Applied Ergonomics"}, {"Title": "Indicators of employee phishing email behaviours: Intuition, elaboration, attention, and email typology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "172", "Issue": "", "Page": "102996", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Enabling cybersecurity incident response agility through dynamic capabilities: the role of real-time analytics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "33", "Issue": "2", "Page": "200", "JournalTitle": "European Journal of Information Systems"}, {"Title": "The human factor in phishing: Collecting and analyzing user behavior when reading emails", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "139", "Issue": "", "Page": "103671", "JournalTitle": "Computers & Security"}, {"Title": "Exploring the evidence for email phishing training: A scoping review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "139", "Issue": "", "Page": "103695", "JournalTitle": "Computers & Security"}, {"Title": "A typology of cybersecurity behavior among knowledge workers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "140", "Issue": "", "Page": "103741", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 115646742, "Title": "DEAR: a novel deep-level semantics feature reinforce framework for Infrared Small Object Segmentation", "Abstract": "<p>Infrared Small Object Segmentation (ISOS) faces challenges in isolating small and faint objects from infrared images due to their limited texture details and small spatial presence. Existing deep learning methods have shown promise but often assume that these networks can effectively map small objects to deep semantic features. This mapping, however, may not be accurately learned by the model due to the excessive downsampling, which may cause the loss of high-level semantic representations essential for accurately identifying small objects in infrared images. To address this issue, this study introduces a novel learning paradigm, DEAR, designed to reinforce deep-level features of infrared small objects from three perspectives. Specifically, the Multi-Scale Nested (MSN) module merges shallow object features with deep semantic features through iterative interactions. The Central Difference Convolution (CDC) module enhances semantic contrast between small objects and their backgrounds, minimizing information loss during downsampling. Additionally, the Orthogonal Dynamic Fusion (ODF) module enhances the depth feature representation by emphasizing and fusing the key features of small objects in both channel and spatial dimensions. Experimental evaluations on the NUAA-SIRST and NUDT-SIRST datasets show that DEAR significantly outperforms 18 state-of-the-art methods in ISOS, demonstrating its effectiveness. The code is publicly available online at https://github.com/nieyihe888/DEAR . </p>", "Keywords": "Infrared Small Object Segmentation; Multi-Scale Nested; Deep feature preservation", "DOI": "10.1007/s00371-024-03499-9", "PubYear": 2025, "Volume": "41", "Issue": "3", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Information, Sichuan University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Alibaba Group, Hangzhou, China"}, {"AuthorId": 3, "Name": "Yongxiang Li", "Affiliation": "College of Computer Science, Sichuan University, Chengdu, China; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Interdisciplinary Information Studies, The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic Information, Sichuan University, Chengdu, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic Information, Sichuan University, Chengdu, China"}], "References": [{"Title": "FusionNet: A Deep Fully Residual Convolutional Neural Network for Image Segmentation in Connectomics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "", "Page": "34", "JournalTitle": "Frontiers in Computer Science"}, {"Title": "Fine-Grained Guided Model Fusion Network with Attention Mechanism for Infrared Small Target Segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 115646923, "Title": "From Video Conferences to DSLRs: An In-depth Texture Evaluation with Realistic Mannequins", "Abstract": "Portraits are one of the most common use cases in photography, especially in smartphone photography. However, evaluating portrait quality in real portraits is costly, inconvenient, and difficult to reproduce. We propose a new method to evaluate a large range of detail preservation renditions on realistic mannequins. This laboratory setup can cover all commercial cameras from videoconference to high-end DSLRs. Our method is based on 1) the training of a machine learning method on a perceptual scale target 2) the usage of two different regions of interest per mannequin depending on the quality of the input portrait image 3) the merge of the two quality scales to produce the final wide range scale.\nOn top of providing a fine-grained wide range detail preservation quality output, numerical experiments show that the proposed method is robust to noise and sharpening, unlike other commonly used methods such as the texture acutance on the Dead Leaves chart.", "Keywords": "Image quality evaluation;Texture detail preservation;Data set fitting;DXOMARK;Machine Learning;Computer Vision;Realistic image quality charts;Camera quality assessment", "DOI": "10.2352/EI.2024.36.9.IQSP-261", "PubYear": 2024, "Volume": "36", "Issue": "9", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115646949, "Title": "Generalized Predictive Analysis of Reactions in Paper Devices via Graph Neural Networks", "Abstract": "Microfluidic technology facilitates high-throughput generation of time series data for biological and medical studies. Deep learning enables accurate, predictive analysis and proactive decision-making based on autonomous recognition of intricate pattern hidden in series. In this work, we first devised a paper-based microfluidic system for portable nucleic acid amplification test with economic energy consumption. Then, we employed Graph Neural Network (GNN), distinguished by its non-Euclidean data structure tailored for deep learning, with spatio-temporal attention mechanism to perform near-sensor predictive analysis of the on-chip reaction. Our findings demonstrated that the novel GNN model can provide accurate predictions of positive outcomes at the early stages of the reaction using less than one-third of the total reaction time. Then, the deep learning model trained by on-chip data was subsequently applied to more than 900 clinical plots. Generalization of the GNN model was successfully validated across different detection methods, diverse types of datasets and time series with variable length. Accuracy, sensitivity and specificity of the predictive approach were 96.5 %, 94.3 % and 99.0 % by utilizing the early half of reaction information. Finally, we compared the GNN model with various deep learning models. Despite differences in the prediction of negative samples among various models were minute, GNN obviously offered overall superior performance. This work ignites a cutting-edge application of deep learning in point-of-care and near-sensor tests. By harnessing the power of body area networks and edge/fog computing, our approach unlocks promising possibilities in diverse fields like healthcare and instrument science.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136085", "PubYear": 2024, "Volume": "417", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Hao Sun", "Affiliation": "School of Mechanical Engineering and Automation, Fuzhou University, Fuzhou, China;School of Mechatronics Engineering, Harbin Institute of Technology, Harbin, China;Corresponding author at: School of Mechanical Engineering and Automation, Fuzhou University, Fuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Fuzhou University, Fuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechatronics Engineering, Harbin Institute of Technology, Harbin, China;State Key Laboratory of Robotics and System, Harbin Institute of Technology, Harbin, China;Corresponding author at: School of Mechatronics Engineering, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Liu", "Affiliation": "School of Mechanical Engineering and Automation, Fuzhou University, Fuzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Automation, Fuzhou University, Fuzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation Control and System Engineering, University of Sheffield, Sheffield, UK"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Sino-German College of Intelligent Manufacturing, Shenzhen Technology University, Shenzhen, China;Corresponding author"}], "References": [{"Title": "Understanding deep learning (still) requires rethinking generalization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "107", "JournalTitle": "Communications of the ACM"}, {"Title": "A review on the attention mechanism of deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}, {"Title": "Financial time series forecasting with multi-modality graph neural network", "Authors": "Dawei Cheng; Fangzhou Yang; Sheng <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108218", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep learning techniques for classification of electroencephalogram (EEG) motor imagery (MI) signals: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "20", "Page": "14681", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Time Series Prediction Using Deep Learning Methods in Healthcare", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Management Information Systems"}]}, {"ArticleId": 115646954, "Title": "Application of entertainment E-learning mode based on Apriori algorithm in intelligent English reading assistance mode", "Abstract": "With the assistance of digital media, entertainment oriented E-learning models can effectively enhance students’ learning enthusiasm. This article analyzes the application of entertainment E-learning mode based on Apriori algorithm in intelligent English reading assistance mode. At present, English reading teaching faces some problems, such as outdated teaching methods, passive learning among students, excessive emphasis on imparting grammar knowledge while neglecting the improvement of students’ reading skills and strategies, and so on. Therefore, this article conducts research on intelligent English reading comprehension tools based on semantic analysis and Apriori algorithm. This paper proposes a recreational E-learning model based on Apriori algorithm. Based on Apriori algorithm, students’ interests and preferences on different learning resources and entertainment elements are mined and incorporated into the learning model design. Then, a set of entertaining English reading assistant model is designed, which uses a variety of entertainment elements, such as gamified learning, interactive activities and reward mechanism, to increase students’ learning participation and enthusiasm. This article adopts the idea of LSA algorithm to construct a BERT semantic analysis model. We treat nodes in the network as word items and then use singular value decomposition algorithm to decompose the word document matrix. Secondly, the original association rule Apriori algorithm was optimized, and the optimized association rule Apriori algorithm effectively solved the problem of excessive computation in traditional algorithms. Finally, based on semantic analysis and Apriori algorithm, this article designs an intelligent English reading comprehension tool, mainly analyzing the practical application of the system and greatly improving the efficiency of English reading teaching.", "Keywords": "", "DOI": "10.1016/j.entcom.2024.100744", "PubYear": 2024, "Volume": "51", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "English Teaching and Research Department, Shangqiu Medical College, Shangqiu, Henan 476000, China"}], "References": [{"Title": "Knowledge-guided unsupervised rhetorical parsing for text summarization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "101615", "JournalTitle": "Information Systems"}, {"Title": "Research on parallelization of Apriori algorithm in association rule mining", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "641", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 115646997, "Title": "Perancangan UI/UX Pada Rebranding Website Line Wisata Karimunjawa Sebagai Media Informasi Pariwisata Kepulauan Ka<PERSON>unjawa", "Abstract": "<p>One of the most popular marine destinations for tourism is the Karimunjawa Islands. One unique draw of the Karimunjawa Islands is their pristine seas and breathtaking scenery. The local economy, particularly the tourism industry and Tour Operator, may benefit from this. The quality of the agency itself also affects a Tour Operator credibility, as does the ability of the portfolio it offers to potential clients to draw them in and utilize its services. The research will focus on UI/UX design for the rebranded Karimunjawa Tourism Line website, which uses the design thinking methodology to create a modern, user-friendly, and educational tourism information medium for the Karimunjawa Islands. Tests that were conducted using a Likert scale showed excellent results that the general public could use. To make the information on potential visitors' travel needs easy to grasp and accessible, the website has been built to present it in a concise, easily understood, and organized manner.Keywords: UI/UX, Website, Karimunjawa Tourism, Rebranding</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i2.9926", "PubYear": 2024, "Volume": "7", "Issue": "2", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}, {"AuthorId": 2, "Name": "T<PERSON> <PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Kristen Satya <PERSON>"}], "References": []}, {"ArticleId": *********, "Title": "Differentially private consensus and distributed optimization in multi-agent systems: A review", "Abstract": "In the past few decades, distributed multi-agent system (MAS) control has received growing attention due to its numerous advantages. Nonetheless, the substantial reliance on local information exchange in distributed MAS control has given rise to significant privacy concerns. Differential privacy (DP), a mathematically rigorous privacy notion, has gained popularity as a means of safeguarding privacy across multiple fields, including distributed MAS control. In this paper, we present an in-depth overview of the techniques for preserving DP in distributed MAS control, concentrating on consensus and distributed optimization. We begin by outlining the defining features and modeling of MASs from the control theory perspective. Then, we illustrate the motivation for adopting differentially private mechanisms to protect the privacy of distributed MAS control and present the fundamental principles of DP. Based on them, we investigate the cutting-edge techniques designed to preserve DP in consensus and distributed optimization. This review sheds light on the current landscape of DP applications in distributed MAS control and lays the groundwork for future progress in this essential field.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127986", "PubYear": 2024, "Volume": "597", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The Department of Mechanical Engineering, The University of Hong Kong, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The Institute of Intelligence Science and Engineering, Shenzhen Polytechnic University, Shenzhen 518055, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The Department of Mechanical Engineering, The University of Hong Kong, Hong Kong;HKU Shenzhen Institute of Research and Innovation, Shenzhen 518057, China"}, {"AuthorId": 4, "Name": "Ka-Wai Kwok", "Affiliation": "The Department of Mechanical Engineering, The University of Hong Kong, Hong Kong"}], "References": [{"Title": "Blockchain Technology Secures Robot Swarms: A Comparison of Consensus Protocols and Their Resilience to Byzantine Robots", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "54", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Bipartite consensus for multi-agent systems with noises over Markovian switching topologies", "Authors": "<PERSON><PERSON><PERSON> Du; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "419", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "Communication-protocol-based analysis and synthesis of networked systems: progress, prospects and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "14", "Page": "3013", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Differentially private average consensus with general directed graphs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "458", "Issue": "", "Page": "87", "JournalTitle": "Neurocomputing"}, {"Title": "Differential privacy for bipartite consensus over signed digraph", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "468", "Issue": "", "Page": "11", "JournalTitle": "Neurocomputing"}, {"Title": "Consensus in multi-agent systems: a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "5", "Page": "3897", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A Survey on Differential Privacy for Unstructured Data Content", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "An overview on multi-agent consensus under adversarial attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "252", "JournalTitle": "Annual Reviews in Control"}, {"Title": "Sampled-based consensus of multi-agent systems with bounded distributed time-delays and dynamic quantisation effects", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "11", "Page": "2390", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Recent advances on cooperative control of heterogeneous multi-agent systems subject to constraints: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "1", "Page": "539", "JournalTitle": "Systems Science & Control Engineering"}, {"Title": "Security of networked control systems subject to deception attacks: a survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "16", "Page": "3577", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Privacy masking distributed saddle-point algorithm for dynamic economic dispatch", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "11", "Page": "8109", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Achieving linear convergence for differentially private full-decentralized economic dispatch over directed networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "642", "Issue": "", "Page": "119199", "JournalTitle": "Information Sciences"}, {"Title": "Private bipartite consensus control for multi-agent systems: A hierarchical differential privacy scheme", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "105", "Issue": "", "Page": "102259", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 115647018, "Title": "Preventing Users from Going Down Rabbit Holes of Extreme Video Content: A Study of the Role Played by Different Modes of Autoplay", "Abstract": "The autoplay feature of video platforms is often blamed for users going down rabbit holes of binge-watching extreme content. However, autoplay is not necessarily a passive experience, because users can toggle the feature off if they want. While the automation aspect is passive, the toggle option signals interactivity, making it “interpassive,” which lies between completely passive autoplay and manual initiation of each video. We empirically compare these three modes of video viewing in a user study ( N = 394), which exposed participants to either extreme or non-extreme content under conditions of manual play, interpassive autoplay, or completely passive autoplay. Results show that interpassive autoplay is favored over the other two. It triggers the control heuristic compared to passive autoplay, but leads to higher inattentiveness compared to manual play. Both the invoked control heuristic and inattentiveness result in higher rabbit hole perception. These findings have implications for socially responsible design of the autoplay feature.", "Keywords": "", "DOI": "10.1016/j.ijhcs.2024.103303", "PubYear": 2024, "Volume": "190", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Elon University, Elon, NC, USA;Correspondence author at: Communication Design Department, School of Communications, Elon University, Elon, NC, 27244; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Fudan University, Shanghai, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Meta, California, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Pennsylvania State University, University Park, PA, USA"}], "References": [{"Title": "Algorithmic extremism: Examining YouTube's rabbit hole of radicalization", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "First Monday"}, {"Title": "Don't Let Netflix Drive the Bus: User's Sense of Agency Over Time and Content Choice on Netflix", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": *********, "Title": "<PERSON><PERSON><PERSON> Kelola E-Learning Universitas Stikubank Semarang Terhadap Ke<PERSON>", "Abstract": "<p>E-learning menjadi faktor krusial yang memengaruhi keberhasilan pengalaman pembelajaran online. <PERSON><PERSON> karena itu, penelitian ini berfokus pada kepuasan mahasiswa terhadap e-learning governance yang akan mempengaruhi keberhasilan pengalaman pembelajaran online. Tujuan penelitian yaitu untuk menyelidiki dampak tata kelola system e-learning terhadap kepuasan pengguna, dengan fokus pada perpektif mahasiswa. Penelitian ini melibatkan 321 responden dengan data sampel yang valid. Analisis SmartPLS digunakan untuk menguji hipotesis yang diajukan. <PERSON><PERSON> pengujian menunjukkan bahwa e-learning governance terhadap kepuasan diterima dan signifikan, ini menunjukkan bahwa dampak keseluruhan yang dirasakan system e-learning dipengaruhi secara langsung oleh kepuasan. Dengan tata Kelola yang efektif, e-learning dapat meningkatkan efisiensi, ketersedia<PERSON> sumber daya, dan kualitas layanan pembelajaran online. Peran dan tanggung jawab yang jelas dalam pengelolaan e-learning dapat membantu lembaga atau organisasi mencapai tujuan.\r Kat<PERSON> Kunci:E-learning, Tata Kelola, Kepuasan Mahasiswa, SEM</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i2.8732", "PubYear": 2024, "Volume": "7", "Issue": "2", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "Fiorera Cinta Ayunda", "Affiliation": "Universitas Stikubank Semarang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Stikubank"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Stikubank"}], "References": []}, {"ArticleId": *********, "Title": "Improvements to a Machine Learning Machining Feature Recognition System", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2025.119-135", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Coalgebraic fuzzy geometric logic", "Abstract": "<p>A generalized form of modal logic can be created within the context of coalgebraic logic. Coalgebraic geometric logic was recently developed by adding modalities to the language of propositional geometric logic using the coalgebra approach. However, as far as we are aware, no studies have been done specifically on fuzzy geometric modal logic. This study is the first step towards developing fuzzy geometric modal logic using coalgebra theory. This new logic might potentially be used to model and reason about transition systems that involve uncertainty in behaviour. We propose a theoretical framework based on coalgebra theory to add modalities into the language of fuzzy geometric logic. Coalgebras for an endofunctor on a category of fuzzy topological spaces and fuzzy continuous maps serve as the foundation for models of this logic. Our key finding is the existence of a final model in the category of models for endofunctors defined on sober fuzzy topological spaces. Furthermore, we present a comparative analysis of the notions of behavioural equivalency, bisimulation, and modal equivalency on the resulting class of models.</p>", "Keywords": "Fuzzy geometric logic; Fuzzy topological spaces; Coalgebra; Final coalgebra; Bisimulation", "DOI": "10.1007/s41870-024-01905-y", "PubYear": 2024, "Volume": "16", "Issue": "6", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Jadavpur University, Kolkata, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ECSU, Indian Statistical Institute, Kolkata, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Jadavpur University, Kolkata, India"}], "References": [{"Title": "Fuzzy conceptualization of the search queries", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "2", "Page": "957", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Large text document summarization based on an enhanced fuzzy logic approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 115647188, "Title": "Perancangan Video Motion Graphics Sosialisasi Stunting Bagi Remaja Usia 15-19 Tahun Di Kabupaten Manokwari", "Abstract": "<p>The high stunting rate in Manokwari makes Manokwari Regency the district with the highest stunting rate in West Papua Province. One of the factors causing the high stunting is the high rate of early marriage. This is due to the difficulty of understanding stunting. The government has made various efforts to reduce stunting but people still feel that they do not understand the material presented in socialization activities, moreover the material cannot be accessed repeatedly, especially for adolescents aged 15-19 years. Video motion graphics can be a solution to convey stunting information to the public. The research aims to design videos of less than one minute that contain illustration, animation, text and backsound, so that information can be conveyed in a more interesting way. The research method used is descriptive qualitative the result of this research is that motion graphics videos can be a medium for delivering socialization about stunting, especially early marriage, which can increase the information of the target audience who watch the video, especially for adolescents aged 15-19 years in Manokwari Regency.\r  \r Keyboards : Stunting, Early Age Marriage, Motion graphics</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i2.9626", "PubYear": 2024, "Volume": "7", "Issue": "2", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115647190, "Title": "Regional Static Output Feedback Stabilization Based on Polynomial Lyapunov Functions for a Class of Nonlinear Systems", "Abstract": "<p>This paper presents a new method for regional stabilization of discrete-time nonlinear systems by static output feedback based on polynomial Lyapunov functions. The considered class of nonlinear systems subject to time-varying parameters can be described by difference-algebraic representation and an equivalent polytopic model is obtained, making it possible to apply <PERSON><PERSON><PERSON><PERSON> theory and linear matrix inequality-based tool. In this sense, a convex optimization problem in terms of LMI is provided to guarantee the closed-loop system’s robust stabilization and to enlarge the estimated domain of attraction (DoA). The proposed control design is a one-step approach that can be applied to systems with nonlinear output matrices. It requires no iterative algorithms or congruence transformation, and auxiliary decision variables are introduced only aiming at less conservative results. Besides that, the use of polynomial Lyapunov functions allows us to obtain asymmetric and non-convex estimated DoA, reducing the conservativeness. Numerical examples are provided to illustrate the effectiveness and advantages of the proposed approach.</p>", "Keywords": "Difference-algebraic representation; Polynomial L<PERSON>nov functions; Regional stabilization; Static output feedback", "DOI": "10.1007/s40313-024-01098-6", "PubYear": 2024, "Volume": "35", "Issue": "4", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "Gabriela L. Reis", "Affiliation": "Department of Education and Technology, Federal Institute of Southeast Minas Gerais, <PERSON><PERSON>, Brazil; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Control and Automation Engineering, Amazonas State University, Manaus, Brazil; Graduate Program in Electrical Engineering, Federal University of Amazonas, Manaus, Brazil"}, {"AuthorId": 3, "Name": "Leonardo A. B. Torres", "Affiliation": "Department of Electronics Engineering, Federal University of Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics Engineering, Federal University of Minas Gerais, Belo Horizonte, Brazil"}], "References": [{"Title": "A Multiple-Parameterization Approach for local stabilization of constrained Takagi-Sugeno fuzzy systems with nonlinear consequents", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "295", "JournalTitle": "Information Sciences"}, {"Title": "On discrete‐time LPV control using delayed Lya<PERSON>nov functions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "5", "Page": "2359", "JournalTitle": "Asian Journal of Control"}, {"Title": "Static output-feedback control for Cyber-physical LPV systems under DoS attacks", "Authors": "Paulo S.<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "563", "Issue": "", "Page": "241", "JournalTitle": "Information Sciences"}, {"Title": "Static output feedback stabilization of uncertain rational nonlinear systems with input saturation", "Authors": "<PERSON><PERSON><PERSON>; Diego de S. <PERSON>; Valessa V. Viana", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "105359", "JournalTitle": "Systems & Control Letters"}, {"Title": "A co-design condition for dynamic event-triggered feedback linearization control", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>rcia L.C<PERSON>", "PubYear": 2024, "Volume": "183", "Issue": "", "Page": "105678", "JournalTitle": "Systems & Control Letters"}]}, {"ArticleId": 115647205, "Title": "Exploring Weight Distributions and Dependence in Neural Networks With $\\alpha$-Stable Distributions", "Abstract": "", "Keywords": "", "DOI": "10.1109/TAI.2024.3409673", "PubYear": 2024, "Volume": "5", "Issue": "11", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua Berkeley Shenzhen Institute, Tsinghua Shenzhen International Graduate School, Shenzhen, Guangdong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua Berkeley Shenzhen Institute, Tsinghua Shenzhen International Graduate School, Shenzhen, Guangdong, China"}, {"AuthorId": 3, "Name": "Ercan Engin <PERSON>", "Affiliation": "Tsinghua Berkeley Shenzhen Institute, Tsinghua Shenzhen International Graduate School, Shenzhen, Guangdong, China"}], "References": [{"Title": "Neuro-Scientific Analysis of Weights in Neural Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "14", "Page": "", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}, {"Title": "Correlated Initialization for Correlated Data", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "2249", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 115647222, "Title": "JuBat: A Julia-based framework for battery modelling using finite element method", "Abstract": "The rapid development of electric vehicles necessitates lithium-ion batteries with high energy densities. To gain a deeper comprehension of lithium-ion batteries, JuBat has been created in Julia programming language for battery modelling. It is an open-source framework implemented by Julia functions, with excellent execution speed and a user-friendly implementation environment. JuBat covers three battery models: P2D, SPM, and SPMe, and uses finite element method to solve the governing equations. The results of JuBat demonstrate good agreement with that of PyBaMM. Moreover, JuBat provides additional options of quadratic element to achieve higher accuracy. These features of JuBat promote the parametric study, analysis, and design of battery cells, all of which are instrumental in advancing lithium-ion batteries.", "Keywords": "Battery modelling; P2D; SPM; Finite element method", "DOI": "10.1016/j.softx.2024.101760", "PubYear": 2024, "Volume": "27", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "China-Pakistan Belt and Road Joint Laboratory on Smart Disaster Prevention of Major Infrastructures, Southeast University, Nanjing, 211189, China;Jiangsu Key Laboratory of Mechanical Analysis for Infrastructure and Advanced Equipment, Southeast University, Nanjing, 211189, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research Centre of Structural Health Monitoring and Prognosis, State Key Laboratory of Mechanics and Control of Mechanical Structures, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China;Corresponding author at: Research Centre of Structural Health Monitoring and Prognosis, State Key Laboratory of Mechanics and Control of Mechanical Structures, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China"}], "References": [{"Title": "Python Battery Mathematical Modelling (PyBaMM)", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Open Research Software"}]}, {"ArticleId": 115647308, "Title": "Supporting stakeholder dialogue on ecosystem service tradeoffs with a simulation tool for land use configuration effects", "Abstract": "Various approaches are available to assist stakeholders in identifying and resolving ecosystem service tradeoffs. However, existing tools fall short in simulating land use configuration effects on ecosystem services and subsequently making these effects accessible to users with varying levels of expertise. To address this gap, we introduce PLACES, a tool that estimates land use impacts on multiple ecosystem services by incorporating landscape-level processes. Tool results are provided in real-time and visualized to support a dialogue between different stakeholders. This study presents the tool development and application during a mixed stakeholder workshop, after which mental models, questionnaires, and videos were analyzed to evaluate PLACES. The tool increased the participants’ understanding of insights of spatial processes and sparked discussions on the societal goals for sustainable landscapes. For future applications of PLACES, we encourage careful tailoring of the landscape representation and land use impact simulations to match the knowledge of the respective users.", "Keywords": "Ecosystem services; Land use configuration; Stakeholder communication; Complexity; Participatory planning tools", "DOI": "10.1016/j.envsoft.2024.106097", "PubYear": 2024, "Volume": "179", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Copernicus Institute of Sustainable Development, Utrecht University, Princetonlaan 8a, PO Box 80115, 3508 TC Utrecht, the Netherlands;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Landscape Architecture and Spatial Planning Group, Department of Environmental Sciences, Wageningen University & Research, Gebouw 101 ESG /LSP, Droevendaalsesteeg 3, 6708 PB Wageningen, the Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Interactive Technologies (IIT) University of Applied Sciences and Arts Northwestern Switzerland (FHNW), Bahnhofstrasse 6, 5210 Brugg-Windisch, Switzerland"}, {"AuthorId": 4, "Name": "Addo<PERSON>", "Affiliation": "Landscape Architecture and Spatial Planning Group, Department of Environmental Sciences, Wageningen University & Research, Gebouw 101 ESG /LSP, Droevendaalsesteeg 3, 6708 PB Wageningen, the Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Copernicus Institute of Sustainable Development, Utrecht University, Princetonlaan 8a, PO Box 80115, 3508 TC Utrecht, the Netherlands"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Geo-information Science and Remote Sensing, Wageningen University and Research, 6708 PB Wageningen, the Netherlands"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Copernicus Institute of Sustainable Development, Utrecht University, Princetonlaan 8a, PO Box 80115, 3508 TC Utrecht, the Netherlands"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Landscape Architecture and Spatial Planning Group, Department of Environmental Sciences, Wageningen University & Research, Gebouw 101 ESG /LSP, Droevendaalsesteeg 3, 6708 PB Wageningen, the Netherlands"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Copernicus Institute of Sustainable Development, Utrecht University, Princetonlaan 8a, PO Box 80115, 3508 TC Utrecht, the Netherlands"}], "References": [{"Title": "Finding the Balance Between Simplicity and Realism in Participatory Modeling for Environmental Planning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "105481", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 115647316, "Title": "Improving the Gas Sensing Performance of Halide Perovskite MAPbI3 Film via Fractal Geometry Electrode Structure.", "Abstract": "The first successful fabrication of metal halide Perovskite MAPbI<sub>3</sub> Film for NH<sub>3</sub> gas sensing using fractal electrode structure is reported. High performance NH<sub>3</sub> gas sensor was fabricated on ITO glass substrate by laser-patterning method. Gas sensors were fabricated using Hilbert, Peano, and <PERSON><PERSON> curve structure electrodes, and their performance is compared to conventional interdigitated electrode. Gas sensing performance on Hilbert electrode design showed the highest response of 2.82 at 10 ppm NH<sub>3</sub> gas, which is 145 % times higher compared to conventional interdigitated structures. Simulation study demonstrates that the electric field intensity and number of nodes of the fractal electrode are higher than those of the traditional interdigitated electrode. This study demonstrates that fractal electrode is a feasible strategy for optimizing the potential performance of MAPbI<sub>3</sub>-based Perovskite gas sensor with low-cost fabrication with no extra processing.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136091", "PubYear": 2024, "Volume": "417", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Taiwan University of Science and Technology, Taipei City 106335, Taiwan;Department of Electronic Engineering, Ming Chi University of Technology, New Taipei City 243303, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Taiwan University of Science and Technology, Taipei City 106335, Taiwan;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Ming Chi University of Technology, New Taipei City 243303, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Vocational School, Pakuan University, West Java 16143, Indonesia;Organic Electronics Research Centre, Ming Chi University of Technology, New Taipei City 243303, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Organic Electronics Research Centre, Ming Chi University of Technology, New Taipei City 243303, Taiwan;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Ming Chi University of Technology, New Taipei City 243303, Taiwan;Organic Electronics Research Centre, Ming Chi University of Technology, New Taipei City 243303, Taiwan;Corresponding author at: Department of Electronic Engineering, Ming Chi University of Technology, New Taipei City 243303, Taiwan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Ming Chi University of Technology, New Taipei City 243303, Taiwan;Corresponding authors"}], "References": [{"Title": "Surface functionalization of ion-in-conjugation polymer sensors for humidity-independent gas detection at room temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "372", "Issue": "", "Page": "132654", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 115647317, "Title": "Scanning strategies for the 316L part with lattice structures fabricated by selective laser melting", "Abstract": "<p>The part with lattice structures generally consists of a framework and lattice structures. Since its slices contain the large-area contours of the framework and some small and discrete contours of the lattice structures, using the scanning strategy recommended by the SLM machine supplier may result in material and shape defects, especially in the case of lattice structures. Therefore, this article proposes a rectangular ring area-partition (RRAP) combined with a 67°-interval rotating scanning strategy for the framework and a parallel line (PL) combined with a 67°-interval rotating scanning strategy for the lattice structures. To study the influence of scanning strategy on the produced samples from the aspects of tensile strength and deformation, the self-developed scanning strategies and existing scanning strategies are used to fabricate 316L samples. The results showed that the 316L solid samples using the RRAP scanning strategy could improve the tensile strength and reduce the deformation. The 316L lattice sandwich panels that adopted the PL scanning strategy can obtain the smallest deviation value of the top surface. Moreover, the effectiveness of the proposed scanning strategies was proven in SLM experiments on two parts that have lattice structures.</p>", "Keywords": "Selective laser melting; Parts with lattice structures; Scan path; Warping deformation", "DOI": "10.1007/s00170-024-13952-7", "PubYear": 2024, "Volume": "133", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Huang", "Affiliation": "School of Mechanical and Electrical Engineering, Jiangxi University of Science and Technology, Ganzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Digital Dentistry, Faculty of Prosthodontics, Peking University School and Hospital of Stomatology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Jiangxi University of Science and Technology, Ganzhou, China"}, {"AuthorId": 4, "Name": "Chunrong Pan", "Affiliation": "School of Mechanical and Electrical Engineering, Jiangxi University of Science and Technology, Ganzhou, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center of Digital Dentistry, Faculty of Prosthodontics, Peking University School and Hospital of Stomatology, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center of Digital Dentistry, Faculty of Prosthodontics, Peking University School and Hospital of Stomatology, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON> Wang", "Affiliation": "Key Laboratory of Construction Hydraulic Robots of Anhui Higher Education Institutes, Tongling University, Tongling, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Jiangxi University of Science and Technology, Ganzhou, China; Corresponding author."}], "References": [{"Title": "Effect of additive manufactured hybrid and functionally graded novel designed cellular lattice structures on mechanical and failure properties", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "128", "Issue": "11-12", "Page": "4873", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Mechanical and microstructural characterization of Ti6Al4V lattice structures with and without solid shell manufactured via electron beam powder bed fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "131", "Issue": "3-4", "Page": "1289", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 115647344, "Title": "VulNet: Towards improving vulnerability management in the Maven ecosystem", "Abstract": "<p>Developers rely on software ecosystems such as Maven to manage and reuse external libraries (i.e., dependencies). Due to the complexity of the used dependencies, developers may face challenges in choosing which library to use and whether they should upgrade or downgrade a library. One important factor that affects this decision is the number of potential vulnerabilities in a library and its dependencies. Therefore, state-of-the-art platforms such as Maven Repository (MVN) and Open Source Insights (OSI) help developers in making such a decision by presenting vulnerability information associated with every dependency. In this paper, we first conduct an empirical study to understand how the two platforms, MVN and OSI, present and categorize vulnerability information. We found that these two platforms may either overestimate or underestimate the number of associated vulnerabilities in a dependency, and they lack prioritization mechanisms on which dependencies are more likely to cause an issue. Hence, we propose a tool named VulNet to address the limitations we found in MVN and OSI. Through an evaluation of 19,886 versions of the top 200 popular libraries, we find VulNet includes 90.5% and 65.8% of the dependencies that were omitted by MVN and OSI, respectively. VulNet also helps reduce 27% of potentially unreachable or less impactful vulnerabilities listed by OSI in test dependencies. Finally, our user study with 24 participants gave VulNet an average rating of 4.5/5 in presenting and prioritizing vulnerable dependencies, compared to 2.83 (MVN) and 3.14 (OSI).</p>", "Keywords": "Software vulnerability management; Software ecosystems; Empirical software engineering", "DOI": "10.1007/s10664-024-10448-6", "PubYear": 2024, "Volume": "29", "Issue": "4", "JournalId": 3327, "JournalTitle": "Empirical Software Engineering", "ISSN": "1382-3256", "EISSN": "1573-7616", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Software Performance, Analysis, and Reliability (SPEAR) lab, Concordia University, Montreal, Canada; Corresponding author."}, {"AuthorId": 2, "Name": "Shouvick Mondal", "Affiliation": "Computer Science and Engineering, Indian Institute of Technology Gandhinagar, Gujarat, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The Software Performance, Analysis, and Reliability (SPEAR) lab, Concordia University, Montreal, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Software Analysis and Intelligence Lab (SAIL), Queen’s University, Kingston, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Software Analysis and Intelligence Lab (SAIL), Queen’s University, Kingston, Canada"}], "References": [{"Title": "Software reuse cuts both ways: An empirical analysis of its relationship with security vulnerabilities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "172", "Issue": "", "Page": "110653", "JournalTitle": "Journal of Systems and Software"}, {"Title": "A comprehensive study of bloated dependencies in the Maven ecosystem", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "3", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "Out of sight, out of mind? How vulnerable dependencies affect open-source projects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "4", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "Analyzing the Direct and Transitive Impact of Vulnerabilities onto Different Artifact Repositories", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "4", "Page": "1", "JournalTitle": "Digital Threats: Research and Practice"}, {"Title": "API beauty is in the eye of the clients: 2.2 million Maven dependencies reveal the spectrum of client–API usages", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "184", "Issue": "", "Page": "111134", "JournalTitle": "Journal of Systems and Software"}, {"Title": "An empirical study of developers’ discussions about security challenges of different programming languages", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "1", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "CAVP: A context-aware vulnerability prioritization model", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "102639", "JournalTitle": "Computers & Security"}, {"Title": "A Survey on Data-driven Software Vulnerability Assessment and Prioritization", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "On the impact of security vulnerabilities in the npm and RubyGems dependency networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "27", "Issue": "5", "Page": "1", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 115647363, "Title": "Evaluation of carbon capture technologies in the oil and gas industry using a socio-technical systems perspective-based decision support system under interval type-2 trapezoidal fuzzy set", "Abstract": "Concerns in relation to consequences of global warming and climate change have activated worldwide attempts for mitigating the concentration of carbon dioxide (CO<sub>2</sub>) produced by the industrial sector. Decarbonizing the oil and gas refining (OGR) industries is a challenging problem for policy-makers owing to its potential to prevent economic, environmental, and health risks. In this regard, CO<sub>2</sub> capture, utilization, and storage (CCUS) technologies are the most encouraging options to decarbonize. The technologies related to the part of CO<sub>2</sub> capture can play a vital role in solving the mentioned problem. Various technologies have been employed for CO<sub>2</sub> capture, and choosing the appropriate technology is a complex multi-criteria decision-making (MCDM) issue. This work develops a novel and robust decision support system (DSS). The DSS integrates MCDM techniques of the Delphi and Entropy integration method (DAEIM) and complex proportional assessment of alternatives (COPRAS) method with the interval type-2 trapezoidal fuzzy (IT2TF) environment. The proposed DSS is used to evaluate, prioritize, and choose technologies for CO<sub>2</sub> capture. A hybrid criteria system, which involves elements of socio-technical systems perspective has been used for evaluating the candidate technologies. For implementing the DSS of this work, five capture technologies of post-combustion ( A_cc <sub>1</sub>), pre-combustion ( A_cc <sub>2</sub>), oxy-fuel combustion ( A_cc <sub>3</sub>), direct air capture ( A_cc <sub>4</sub>), and indirect air capture ( A_cc <sub>5</sub>) have been chosen for evaluation. The final value of each technology is A_cc <sub>1</sub> (0. 2907), A_cc <sub>2</sub> (0.2602), A_cc <sub>3</sub> (0.1005), A_cc <sub>4</sub> (0.2304), and A_cc <sub>5</sub> (0.1181) and the preferences of the technologies are A_cc <sub>1</sub>> A_cc <sub>2</sub>> A_cc <sub>4</sub>> A_cc <sub>5</sub>> A_cc <sub>3</sub>. The evaluation findings reveal that post-combustion technology with the value of 0.2907 is the most suitable scenario for the capture of CO<sub>2</sub> emissions from Iran's OGR systems. The computation results demonstrate that the suggested DSS is feasible and applicable and give reliable and robust findings for acquiring the optimal CO<sub>2</sub> capture technology.", "Keywords": "CO<sub>2</sub> capture technologies; Industrial decarbonization; Interval type-2 trapezoidal fuzzy DAEIM-COPRAS model; Oil and gas industry; Socio-technical systems perspective", "DOI": "10.1016/j.dche.2024.100164", "PubYear": 2024, "Volume": "12", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Behbahan Branch, Islamic Azad University, Behbahan, Iran;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Young Researchers and Elite Club, Behbahan Branch, Islamic Azad University, Behbahan, Iran"}], "References": [{"Title": "Extended TODIM method for CCUS storage site selection under probabilistic hesitant fuzzy environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106381", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evaluation of renewable energy sources in China using an interval type-2 fuzzy large-scale group risk evaluation method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107458", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel Interval Type-2 Fuzzy best-worst method and combined compromise solution for evaluating eco-friendly packaging alternatives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "117188", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Supplier selection through interval type-2 trapezoidal fuzzy multi-attribute group decision-making method with logarithmic information measures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107006", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An interval type-2 fuzzy best-worst method and likelihood-based multi-criteria method in group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110856", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improvement to the interval type-2 fuzzy VIKOR method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "111055", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A multi-criteria decision-making model for analyzing a project-driven supply chain under interval type-2 fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110902", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 115647382, "Title": "Reliability-aware scheduling for ( m , k ) -firm real-time embedded systems under hard energy budget constraint", "Abstract": "For real-time embedded systems, feasibility, Quality of Service (QoS), reliability, and energy constraint are among the primary design concerns. In this research, we proposed a reliability-aware scheduling scheme for real-time embedded systems with ( m , k ) -firm deadlines under hard energy budget constraint. The ( m , k ) -firm systems require that at least m out of any k consecutive jobs of a real-time task meet their deadlines. To achieve the dual goals of maximizing the feasibility and QoS for such kind of systems while satisfying the reliability requirement under given energy budget constraint, we propose to reserve recovery space for real-time jobs in an adaptive way based on the mandatory/optional job partitioning strategy. The evaluation results demonstrate that the proposed techniques significantly outperform the previous research in maximizing the feasibility and QoS for ( m , k ) -firm real-time embedded systems while preserving the system reliability under hard energy budget constraint. Moreover, the proposed work has also addressed some insufficiency in Niu (2020) in terms of preserving the system reliability.", "Keywords": "", "DOI": "10.1016/j.sysarc.2024.103185", "PubYear": 2024, "Volume": "154", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Howard University, Washington, DC, 20059, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Howard University, Washington, DC, 20059, USA"}], "References": [{"Title": "Reliability-Aware Energy-Efficient Scheduling for (m, k)-Constrained Real-Time Systems Through Shared Time Slots", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "77", "Issue": "", "Page": "103110", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 115647406, "Title": "Differential faultt attack on DEFAULT", "Abstract": "Block cipher DEFAULT has been proposed as a differential fault analysis immune cipher at Asiacrypt 2021. In this paper, we consider the initial version of DEFAULT with no permutation involved in the last round and show that one can find the key in this version with complexity $ 2^{16} $ by injecting 112 faults. However, our idea does not work for the modified version of the cipher (where a key scheduling algorithm is involved).", "Keywords": "Block cipher; DEFAULT; differential fault attack; linear structure", "DOI": "10.3934/amc.2022035", "PubYear": 2024, "Volume": "18", "Issue": "4", "JournalId": 91009, "JournalTitle": "Advances in Mathematics of Communications", "ISSN": "1930-5346", "EISSN": "1930-5338", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Technology Jammu, Jammu"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Madras, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Madras, Chennai, India"}], "References": []}, {"ArticleId": 115647413, "Title": "Smart city VR landscape planning and user virtual entertainment experience based on artificial intelligence", "Abstract": "With the development of virtual reality technology, wearable visual devices and digital technology can provide users with intuitive virtual experiences, which can effectively improve the user experience of landscape planning. This article analyzes the VR landscape planning and user virtual entertainment experience of smart cities based on artificial intelligence. The application of this technology can also provide richer data support for urban landscape planning and design, allowing designers and planners to better utilize advanced technological means to optimize the planning and layout of urban landscapes. In the field of urban planning and design, artificial intelligence can be used to simulate street space architecture and urban pedestrian and vehicle behavior paths, evaluate the impact of planning and design schemes on the environment, transportation, and citizens, and effectively demonstrate the feasibility of implementing smart city planning schemes. Virtual landscape models utilize computer-generated 3D image technology to present real-world landscapes in a realistic manner. With the help of artificial intelligence technology, entertainment robots are able to interact with users in natural language and understand their needs and preferences. Through technologies such as deep learning and pattern recognition, entertainment robots can autonomously learn and recommend entertainment content suitable for users, further enhancing users’ virtual entertainment experience. Through virtual reality technology, immersive browsing and experience of landscapes can provide a more intuitive perceptual experience, and also enable decision-makers and the public to better understand the design and planning intentions of the landscape.", "Keywords": "", "DOI": "10.1016/j.entcom.2024.100743", "PubYear": 2024, "Volume": "51", "Issue": "", "JournalId": 21223, "JournalTitle": "Entertainment Computing", "ISSN": "1875-9521", "EISSN": "1875-953X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Creativity and Design, Guangzhou Huashang College, Guangzhou, Guangdong, 511300, China"}, {"AuthorId": 2, "Name": "Feng <PERSON>", "Affiliation": "School of Creativity and Design, Guangzhou Huashang College, Guangzhou, Guangdong, 511300, China;Corresponding author at: School of Creativity and Design, Guangzhou Huashang College, Guangzhou, Guangdong, 511300, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Film Technology, Jilin Animation Institute, Changchun, Jilin, 130013, China"}], "References": [{"Title": "Effect of VR technology matureness on VR sickness", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "14491", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Improved K-Means Clustering Algorithm for Big Data Mining under Hadoop Parallel Framework", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "2", "Page": "239", "JournalTitle": "Journal of Grid Computing"}]}, {"ArticleId": 115647414, "Title": "A robust blockchain-based watermarking using edge detection and wavelet transform", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-18907-4", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A tamper detection suitable fragile watermarking scheme based on novel payload embedding strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "Page": "1673", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust and imperceptible watermarking scheme based on Canny edge detection and SVD in the contourlet domain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "1", "Page": "439", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust and imperceptible watermarking scheme based on Canny edge detection and SVD in the contourlet domain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "1", "Page": "439", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "DwiMark: a multiscale robust deep watermarking framework for diffusion-weighted imaging images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "1", "Page": "295", "JournalTitle": "Multimedia Systems"}, {"Title": "Robust watermarking of databases in order-preserving encrypted domain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "2", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Securing Medical Data by Combining Encryption and Robust Blind Medical Image Watermarking Based on Zaslavsky Chaotic Map and DCT Coefficients", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A blockchain‐based privacy‐preserving authentication system for ensuring multimedia content integrity", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "5", "Page": "3050", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Fingerprint-based robust medical image watermarking in hybrid transform", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "6", "Page": "2245", "JournalTitle": "The Visual Computer"}, {"Title": "Watermarking techniques for three-dimensional (3D) mesh models: a survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON> <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "2", "Page": "623", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 115647477, "Title": "Memahami Determinan Keberhasilan Sistem: <PERSON><PERSON>ah <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>", "Abstract": "<p>Studi ini bertujuan untuk menganalisis dampak kualitas e-learning terhadap keterlibatan dan kepuasan pengguna, serta pengaruhnya terhadap keberhasilan sistem e-learning di lingkungan universitas. Model keberhasilan sistem informasi diadopsi untuk menguji efikasi sistem e-learning. Studi ini didasarkan pada hipotesis bahwa kualitas sistem, instruksi, dan informasi berpengaruh signifikan terhadap penggunaan sistem dan kepuasan pengguna dalam lingkungan e-learning universitas, yang pada gilirannya menentukan keberhasilan sistem. Model persamaan struktural (SEM) digunakan, dengan menerapkan Partial Least Squares (PLS) versi 4.0, untuk menilai dan memvalidasi model pengukuran dan struktural. Sampel penelitian ini terdiri dari 173 mahasiswa dari berbagai lembaga pendidikan tinggi di kota Semarang. <PERSON>muan menunjukkan bahwa kualitas sistem, kualitas informasi, dan kualitas instruksi memiliki efek yang signifikan dan positif terhadap penggunaan sistem (menjelaskan 57% varians) dan kepuasan pengguna (47,2% R Square). Selain itu, hasil juga menunjukkan bahwa kualitas sistem memberikan pengaruh terkuat terhadap kepuasan pengguna dan penggunaan sistem, sementara kualitas penggunaan sistem memiliki dampak yang lebih besar terhadap keberhasilan sistem e-learning universitas.</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i1.8779", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Progdi Sistem Informasi, Fakultas Teknologi Informasi & Industri, Universitas Stikubank Semarang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>hr<PERSON>", "Affiliation": "Progdi Sistem Informasi, Fakultas Teknologi Informasi & Industri, Universitas Stikubank Semarang"}, {"AuthorId": 3, "Name": "Novita Mariana", "Affiliation": "Progdi Sistem Informasi, Fakultas Teknologi Informasi & Industri, Universitas Stikubank Semarang"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Progdi Sistem Informasi, Fakultas Teknologi Informasi & Industri, Universitas Stikubank Semarang"}], "References": []}, {"ArticleId": *********, "Title": "Enhanced adaptive-convergence in Harris’ hawks optimization algorithm", "Abstract": "<p>This paper presents a novel enhanced adaptive-convergence in Harris’ hawks optimization algorithm (EAHHO). In EAHHO, considering that Harris’ hawks will adopt different perching strategies and chasing styles according to the value of the escaping energy parameter E, nonlinear adaptive-convergence factor a is designed and adjusted to enhance the convergence and robustness of the algorithm. Moreover, the convergence and stability of EAHHO are proved mathematically by using the Markov chain theory and <PERSON><PERSON><PERSON><PERSON> stability theory respectively. Moreover, numerical simulation results of 14 HHOs with different nonlinear convergence factors on 23 benchmark functions show that the nonlinear convergence factor of EAHHO is applicable to challenging problems with unknown search spaces, and the comparisons with the selected well-established algorithms on 56 test functions demonstrate that EAHHO performs competitively and effectively. Finally, the experiment results show that EAHHO algorithm also has a good performance to solve the optimization problems with relatively high dimensions and graph size of Internet of Vehicles routing problem.</p>", "Keywords": "Harris’ hawks optimization (HHO); Enhanced adaptive-convergence; Escaping energy; Markov chain; Lyapunov stability theory", "DOI": "10.1007/s10462-024-10802-6", "PubYear": 2024, "Volume": "57", "Issue": "7", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Wuxi University, Wuxi, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Nanjing University of Science and Technology, Nanjing, China"}], "References": [{"Title": "A new algorithm for normal and large-scale optimization problems: Nomadic People Optimizer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "14", "Page": "10359", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A three-dimensional group search optimization approach for simultaneous planning of distributed generation units and distribution network reconfiguration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "106012", "JournalTitle": "Applied Soft Computing"}, {"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Ant Lion Optimizer: A Comprehensive Survey of Its Variants and Applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1397", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Population size in Particle Swarm Optimization", "Authors": "<PERSON>; <PERSON><PERSON>sla<PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "100718", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A hybrid Harris Hawks optimization algorithm with simulated annealing for feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "593", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Gradient-based optimizer: A new metaheuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "131", "JournalTitle": "Information Sciences"}, {"Title": "RETRACTED ARTICLE: Group search optimizer: a nature-inspired meta-heuristic optimization algorithm with its results, variants, and applications", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2949", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Pulse coupled neural network based on <PERSON> hawks optimization algorithm for image segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "37-38", "Page": "28369", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Neighborhood centroid opposite-based learning Harris <PERSON> optimization for training neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "1847", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "A novel algorithm for global optimization: Rat Swarm Optimizer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Atulya Nagar", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8457", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Parameter fitting of variogram based on hybrid algorithm of particle swarm and artificial fish swarm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "265", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Qualitative Analysis of a Fractional Pandemic Spread Model of the Novel Coronavirus (Covid-19)", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "1", "Page": "843", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Binary biogeography-based optimization based SVM-RFE for feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107026", "JournalTitle": "Applied Soft Computing"}, {"Title": "Algebraic Stability Analysis of Particle Swarm Optimization Using Stochastic Lyapunov Functions and Quantifier Elimination", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Nature inspired meta heuristic algorithms for optimization problems", "Authors": "<PERSON><PERSON>; Anand H. S.", "PubYear": 2022, "Volume": "104", "Issue": "2", "Page": "251", "JournalTitle": "Computing"}, {"Title": "Nonlinear-based Chaotic Harris Hawks Optimizer: Algorithm and Internet of Vehicles application", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107574", "JournalTitle": "Applied Soft Computing"}, {"Title": "A quantum mutation-based backtracking search algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "4", "Page": "3019", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An improved symbiotic organisms search algorithm for higher dimensional optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107779", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An improved moth flame optimization algorithm based on modified dynamic opposite learning strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "4", "Page": "2811", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "RETRACTED ARTICLE: A hybrid slime mould algorithm for global optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "15", "Page": "22441", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 115647619, "Title": "A comparative analysis of predictive channel models for real shallow water environments", "Abstract": "Unlike traditional terrestrial scenarios, communication channels in underwater environments face severe limitations in bandwidth and experience long propagation delay. In order to address these issues, reliable techniques capable to dynamically adapt transmission parameters to time-varying channel conditions are necessary. Actually, their effectiveness primarily relies on an accurate characterization of the underwater communication channels, which is often obtained through predictive models. In this paper, we compare the performance of different types of SNR-based predictive models (i.e., Markov models, Hidden Markov models) in terms of balance between accuracy and complexity. We also provide a Kalman filter-based prediction of SNR values and compare this prediction with the performance achieved with the Markov models above mentioned. The models we have considered to carry out the comparison analysis have been developed based on real shallow water traces taken over the Tyrrhenian Sea, Italy.", "Keywords": "Underwater communications; Markov models; Hidden Markov models; Kalman filtering; Performance analysis", "DOI": "10.1016/j.comnet.2024.110557", "PubYear": 2024, "Volume": "250", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON>", "Affiliation": "University of Catania, Italy;CNIT Catania Research Unit, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Catania, Italy;CNIT Catania Research Unit, Italy"}, {"AuthorId": 3, "Name": "S. <PERSON>", "Affiliation": "University of Catania, Italy;CNIT Catania Research Unit, Italy;Corresponding author at: University of Catania, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Palermo, Italy;CNIT Catania Research Unit, Italy"}], "References": [{"Title": "An integrated acoustic/LoRa system for transmission of multimedia sensor data over an Internet of Underwater Things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; F<PERSON>; L. <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "132", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 115647665, "Title": "Exploring the influence of user characteristics on verbal aggression towards social chatbots", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2024.2362957", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Data Science Group, Institute for Basic Science, Daejeon, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, KAIST, Daejeon, South Korea"}], "References": [{"Title": "The morality of abusing a robot", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "271", "JournalTitle": "<PERSON><PERSON><PERSON>, Journal of Behavioral Robotics"}, {"Title": "Voices that Care Differently: Understanding the Effectiveness of a Conversational Agent with an Alternative Empathy Orientation and Emotional Expressivity in Mitigating Verbal Abuse", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "12", "Page": "1153", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Enhancing User’s Self-Disclosure through Chatbot’s Co-Activity and Conversation Atmosphere Visualization", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "18-20", "Page": "1891", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 115647704, "Title": "Flexible terrain erosion", "Abstract": "<p>In this paper, we present a novel particle-based method for simulating erosion on various terrain representations, including height fields, voxel grids, material layers, and implicit terrains. Our approach breaks down erosion into two key processes—terrain alteration and material transport—allowing for flexibility in simulation. We utilize independent particles governed by basic particle physics principles, enabling efficient parallel computation. For increased precision, a vector field can adjust particle speed, adaptable for realistic fluid simulations or user-defined control. We address material alteration in 3D terrains with a set of equations applicable across diverse models, requiring only per-particle specifications for size, density, coefficient of restitution, and sediment capacity. Our modular algorithm is versatile for real-time and offline use, suitable for both 2.5D and 3D terrains.</p>", "Keywords": "Procedural modeling; Terrain morphing; Natural phenomena; Erosion processes", "DOI": "10.1007/s00371-024-03444-w", "PubYear": 2024, "Volume": "40", "Issue": "7", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LIRMM, CNRS, Université de Montpellier, Montpellier, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IRIT, CNRS, Université de Toulouse, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LIRMM, CNRS, Université de Montpellier, Montpellier, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "LIRMM, CNRS, Université de Montpellier, Montpellier, France"}], "References": [{"Title": "Simulation, modeling and authoring of glaciers", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Synthesizing Geologically Coherent Cave Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "7", "Page": "277", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Grad<PERSON> Terrain Authoring", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "85", "JournalTitle": "Computer Graphics Forum"}, {"Title": "A Survey on SPH Methods in Computer Graphics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "737", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Large-scale Terrain Authoring through Interactive Erosion Simulation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Forming Terrains by Glacial Erosion", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 115647714, "Title": "Implementasi Sistem IoT Smart Lock Door Menggunakan Sensor RFID Pada Loker Mess PUSLATPUR", "Abstract": "<p><PERSON><PERSON><PERSON> sekarang  ini tingginya Tingkat kriminalitas  yang  bermacam macam  semakin  bertambah,  khususnya  pencurian atau pembobolan pada brankas maupun loker di tempat umum.  <PERSON><PERSON> kenya<PERSON>ya,  kasus pembobolan loker maupun brankas  pada  zaman  sekarang  dengan  mudahnya  dilakukan  oleh  para  pencuri dengan  membuka kunci   pengaman pada  pintu  karena  pengamanan  pintu  hanya  menggunakan  kunci  biasa  (manual). Kurangnya sistem keamanan ini merupakan salah satu hal yang memudahkan pelaku menjalankan aksinya. Dalam hal ini maka perlu adanya sistem keamanan yang harus lebih ditingkatkan. Berdasarkan permasalahan tersebut, peneliti akan meningkatkan sistem keamanan pintu loker dengan menggunakan sistem IOT smart lock door menggunakan sensor RFID yang  diharapkan  dapat  berjalan dengan baik  dan  lebih  meningkatkan keamanan pintu loker  jika dibandingkan dengan kunci pintu konvensional biasa. Penelitian ini menggunakan metode Exteme Programming (XP) yang  merupakan  seibuah proseis reikayasa  peirangkat lunak yang ceindeirung meinggunakan peindeikatan beiroriieintasii objeik..  Dengan adanya  penelitian  ini  diharapkan  dapat  mengurangi  rasa  khawatir  ketika  menyimpan suatu barang yang kita letakkan di dalam loker umum karena  peneliti melakukan  perancangan  sistem  IOT smart lock door yang  didukung  dengan  fitur fitur  yang terpasang seperti sensor sidik RFID, serta user monitor yang senantiasa merekam segala aktivitas percobaan membuka pintu loker sehingga dapat di lihat langsung data penggunakanya\r Kata Kunci: e-KTP, IoT, RFID.</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i2.9385", "PubYear": 2024, "Volume": "7", "Issue": "2", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Bina Darma"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas Bina Darma"}], "References": []}, {"ArticleId": 115647742, "Title": "Faster maximal clique enumeration in large real-world link streams", "Abstract": "Link streams offer a good model for representing interactions over time. They consist of links $(b,e,u,v)$, where $u$ and $v$ are vertices interacting during the whole time interval $[b,e]$. In this paper, we deal with the problem of enumerating maximal cliques in link streams. A clique is a pair $(C,[t_0,t_1])$, where $C$ is a set of vertices that all interact pairwise during the full interval $[t_0,t_1]$. It is maximal when neither its set of vertices nor its time interval can be increased.   Some main works solving this problem are based on the famous <PERSON><PERSON><PERSON><PERSON><PERSON> algorithm for enumerating maximal cliques in graphs.   We take this idea as a starting point to propose a new algorithm which matches the cliques of the instantaneous graphs formed by links existing at a given time $t$ to the maximal cliques of the link stream.   We prove its correctness and compute its complexity, which is better than the state-of-the art ones in many cases of interest. We also study the output-sensitive complexity, which is close to the output size, thereby showing that our algorithm is efficient. To confirm this, we perform experiments on link streams used in the state of the art, and on massive link streams, up to 100 million links.   In all cases our algorithm is faster, mostly by a factor of at least 10 and up to a factor of $10^4$. Moreover, it scales to massive link streams for which the existing algorithms are not able to provide the solution.", "Keywords": "Link stream;Temporal graph;Dynamic network;Maximal clique enumeration;Bron-Kerbosch;Output-sensitive complexity", "DOI": "10.7155/jgaa.v28i1.2932", "PubYear": 2024, "Volume": "28", "Issue": "1", "JournalId": 16217, "JournalTitle": "Journal of Graph Algorithms and Applications", "ISSN": "", "EISSN": "1526-1719", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sorbonne University, CNRS, LIP6"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sorbonne University, CNRS, LIP6"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sorbonne University, CNRS, LIP6"}], "References": []}, {"ArticleId": 115647762, "Title": "A Few-Shot Semantic Segmentation Method Based on Self Guided Prototype Enhancement—An Improved Segmentation Method of Base Learning and Meta Learning", "Abstract": "", "Keywords": "", "DOI": "10.12677/csa.2024.145126", "PubYear": 2024, "Volume": "14", "Issue": "5", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [], "References": [{"Title": "A survey of deep meta-learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "4483", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 115647851, "Title": "Enhancing Perceived Value in Human-Computer Interaction: The Mediating Role of User Participation and the Moderating Role of Task Complexity", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2350839", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Cultural Industries, Shenzhen University, Shenzhen, China"}, {"AuthorId": 2, "Name": "Dai<PERSON> Xiao", "Affiliation": "School of Humanities and Social Sciences, City University of Macau, Taipa, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Cultural Industries, Shenzhen University, Shenzhen, China"}], "References": [{"Title": "AI-based chatbots in customer service and their effects on user compliance", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "427", "JournalTitle": "Electronic Markets"}, {"Title": "Humor and camera view on mobile short-form video apps influence user experience and technology-adoption intent, an example of TikTok (DouYin)", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "106373", "JournalTitle": "Computers in Human Behavior"}, {"Title": "My Smart Speaker is Cool! Perceived Coolness, Perceived Values, and Users’ Attitude toward Smart Speakers", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "6", "Page": "560", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "The human side of human-chatbot interaction: A systematic literature review of ten years of research on text-based chatbots", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "102630", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Understanding the Effect that Task Complexity has on Automation Potential and Opacity: Implications for Algorithmic Fairness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "104", "JournalTitle": "AIS Transactions on Human-Computer Interaction"}, {"Title": "What makes consumers trust and adopt fintech? An empirical investigation in China", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "1", "Page": "3", "JournalTitle": "Electronic Commerce Research"}, {"Title": "On the Design of and Interaction with Conversational Agents: An Organizing and Assessing Review of Human-Computer Interaction Research", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "1", "Page": "96", "JournalTitle": "Journal of the Association for Information Systems"}, {"Title": "Transitioning to Human Interaction with AI Systems: New Challenges and Opportunities for HCI Professionals to Enable Human-Centered AI", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "494", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Communicating the Limitations of AI: The Effect of Message Framing and Ownership on Trust in Artificial Intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "4", "Page": "790", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "The Effects of Online Learning and Task Complexity on Students’ Procrastination and Academic Performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "13", "Page": "2656", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Theory-based approach for assessing cognitive load during time-critical resource-managing human–computer interactions: an eye-tracking study", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "1", "Page": "1", "JournalTitle": "Journal on Multimodal User Interfaces"}, {"Title": "A Survey on Measuring Cognitive Workload in Human-Computer Interaction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "13s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 115647899, "Title": "Fully Distributed Adaptive Event-Triggered Bipartite Containment Control of Linear Multiagent Systems With Actuator Faults", "Abstract": "This paper studies a new type of fully distributed double event-triggered bipartite containment (DETBC) problem for linear multiagent systems (MASs). Meanwhile, multiple leaders with non-zero inputs and time-varying multiplicative and additive actuator faults (TMAAFs) are considered. First, in contrast to traditional event-triggered mechanisms (ETMs), the proposed novel double ETMs can guarantee that information transfer between agents and controller updates proceeds asynchronously. Some adaptive parameters are introduced in the trigger functions to enhance the dynamic adjustment ability of double ETMs. The trigger threshold depends on the relative state-dependent, which is independent of continuous information from neighbors. Then, a fault-tolerant DETBC protocol is designed to solve actuator fault problems. Moreover, compared with other relevant works, our control protocol is fully distributed, which avoids reliance on global information. Finally, a simulation experiment is conducted to validate the feasibility of the designed protocol.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120854", "PubYear": 2024, "Volume": "677", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110819, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110819, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110819, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, Liaoning 110819, China"}], "References": [{"Title": "Containment control of general linear multi-agent systems by event-triggered control mechanisms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "433", "Issue": "", "Page": "263", "JournalTitle": "Neurocomputing"}, {"Title": "Event-based distributed secondary voltage tracking control of microgrids under DoS attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1572", "JournalTitle": "Information Sciences"}, {"Title": "Multi-Agent Based Stochastic Dynamical Model to Measure Community Resilience", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "262", "JournalTitle": "Journal of Social Computing"}, {"Title": "Event-triggered fault-tolerant secure containment control of multi-agent systems through impulsive scheme", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "622", "Issue": "", "Page": "1128", "JournalTitle": "Information Sciences"}, {"Title": "A distributed predictive formation control strategy for cyber-physical multi-agent systems under communication constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "641", "Issue": "", "Page": "119092", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 115647941, "Title": "Surface-enhanced Raman spectroscopy sensing of <PERSON><PERSON><PERSON>'s total antioxidant capacity utilizing Polyoxometalate-Based Frameworks nanozymes", "Abstract": "The ability to accurately assess the total antioxidant capacity (TAC) of Huangjiu is essential for the comprehensive assessment of its nutritional and medicinal benefits. Consequently, developing a rapid, sensitive and specific analytical methodology to determine TAC is an urgent research need. In this work, we explored the potential of a one-step solvothermal process, leading to the synthesis of polyoxometalates-metal-organic frameworks (POMs-MOFs), H<sub>3</sub>PW<sub>12</sub>O<sub>40</sub> (HPW)-CuBTC materials, which possess a high surface area and considerable active sites. Our results demonstrated that HPW-CuBTC materials exhibit remarkable surface-enhanced Raman scattering (SERS) activity, capable of detecting 10<sup>–8</sup> M methylene blue (MB). SERS activity was attributed to both the localized surface plasmon resonance (LSPR) and charge transfer (CT) synergistic enhancement mechanisms. Furthermore, HPW-CuBTC materials exhibited peroxidase (POD)-like activity, with a catalytic mechanism involving a synergistic catalytic effect between HPW and CuBTC. We developed a catechin sensor based on the POD-like properties of HPW-CuBTC, with a linear range from 4 μM to 8 μM (R<sup>2</sup>=0.98). Additionally, we utilized the HPW-CuBTC materials to construct a TAC sensing platform in Huangjiu. This work introduces new technologies and methodologies for rapid trace detection of TAC in Huangjiu, providing a valuable tool for evaluating its nutritional and medicinal benefits.", "Keywords": "", "DOI": "10.1016/j.snb.2024.136075", "PubYear": 2024, "Volume": "417", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China;College of Chemistry and Chemical Engineering, Xingtai University, Xingtai 054001, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jilin Provincial Key Laboratory of Nutrition and Functional Food, College of Food Science and Engineering, Jilin University, Changchun 130062, PR China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of Basic Medical Sciences, Jilin University, Changchun 130021, PR China;Corresponding authors"}, {"AuthorId": 9, "Name": "Yuqing Wu", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Supramolecular Structure and Materials, College of Chemistry, Jilin University, Changchun 130012, PR China;Corresponding authors"}], "References": [{"Title": "Crystal-amorphous Ni(OH)2 nanocages as SERS substrate with conspicuous defects-induced charge-transfer resonance for ultrasensitive detection of MicroRNA 155", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "375", "Issue": "", "Page": "132879", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Colorimetric evaluation of total antioxidant capacity based on peroxidase-like nonstoichiometric Cu2-Se nanoparticles", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "398", "Issue": "", "Page": "134794", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 115647953, "Title": "A novel ensemble artificial intelligence approach for coronary artery disease prediction", "Abstract": "Purpose Coronary artery disease is one of the most common cardiovascular disorders in the world, and it can be deadly. Traditional diagnostic approaches are based on angiography, which is an interventional procedure having side effects such as contrast nephropathy or radio exposure as well as significant expenses. The purpose of this paper is to propose a novel artificial intelligence (AI) approach for the diagnosis of coronary artery disease as an effective alternative to traditional diagnostic methods. Design/methodology/approach In this study, a novel ensemble AI approach based on optimization and classification is proposed. The proposed ensemble structure consists of three stages: feature selection, classification and combining. In the first stage, important features for each classification method are identified using the binary particle swarm optimization algorithm (BPSO). In the second stage, individual classification methods are used. In the final stage, the prediction results obtained from the individual methods are combined in an optimized way using the particle swarm optimization (PSO) algorithm to achieve better predictions. Findings The proposed method has been tested using an up-to-date real dataset collected at Basaksehir Çam and Sakura City Hospital. The data of disease prediction are unbalanced. Hence, the proposed ensemble approach improves majorly the F-measure and ROC area which are more prominent measures in case of unbalanced classification. The comparison shows that the proposed approach improves the F-measure and ROC area results of the individual classification methods around 14.5% in average and diagnoses with an accuracy rate of 96%. Originality/value This study presents a low-cost and low-risk AI-based approach for diagnosing heart disease compared to traditional diagnostic methods. Most of the existing research studies focus on base classification methods. In this study, we mainly investigate an effective ensemble method that uses optimization approaches for feature selection and combining stages for the medical diagnostic domain. Furthermore, the approaches in the literature are commonly tested on open-access dataset in heart disease diagnoses, whereas we apply our approach on a real and up-to-date dataset.", "Keywords": "Artificial intelligence;Ensemble model;Optimization;Prediction;Classification;Disease diagnosis", "DOI": "10.1108/IJICC-11-2023-0336", "PubYear": 2024, "Volume": "17", "Issue": "3", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "Özge H. <PERSON>", "Affiliation": "Industrial Engineering Department , Istanbul Technical University , Istanbul, Turkey Industrial Engineering Department , Turkish-German University , Istanbul, Turkey"}, {"AuthorId": 2, "Name": "Seda Yanık", "Affiliation": "Industrial Engineering Department , Istanbul Technical University , Istanbul, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Başakşehir Çam and Sakura City Hospital , Istanbul, Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Theoretical Information Technology , RWTH Aachen University , Aachen, Germany"}], "References": [{"Title": "RETRACTED ARTICLE: The health of things for classification of protein structure using improved grey wolf optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "2", "Page": "1226", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "HIOC: a hybrid imputation method to predict missing values in medical datasets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "598", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}, {"Title": "Computer-Aided Heart Disease Diagnosis Using Recursive Rule Extraction Algorithms from Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "2", "Page": "", "JournalTitle": "International Journal of Computational Intelligence and Applications"}, {"Title": "Evolutionary bagging for ensemble learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "510", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Heart disease prediction using distinct artificial intelligence techniques: performance analysis and comparison", "Authors": "Md. <PERSON>; <PERSON><PERSON><PERSON>; Md. <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "4", "Page": "397", "JournalTitle": "Iran Journal of Computer Science"}, {"Title": "Enhancing the Early Detection of Chronic Kidney Disease: A Robust Machine Learning Model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "3", "Page": "144", "JournalTitle": "Big Data and Cognitive Computing"}]}, {"ArticleId": 115647981, "Title": "Stability equivalence between regime‐switching jump diffusion delayed systems and corresponding systems with piecewise continuous arguments and application to discrete‐time feedback control", "Abstract": "In this paper, we mainly study the equivalence of exponential stability for regime‐switching jump diffusion delayed systems (RSJDDSs) and RSJDDSs with piecewise continuous arguments (RSJDDSs‐PCA). Our results show that if one of the RSJDDS and the RSJDDS‐PCA is \nth moment exponentially stable, then another system is also \nth moment exponentially stable when time delay and segment step size have a common upper bound, while both equations are almost surely exponentially stable, and we also provided a method to calculate this upper bound. In addition, as an application of the stability equivalence theorem, we design discrete‐time state and mode observations feedback control to stabilize unstable RSJDDSs and investigate that controllers of the drift, diffusion, and jump terms are all able to play a stabilizing effect on the controlled system.", "Keywords": "discrete-time feedback control;exponential stability equivalence;piecewise continuous arguments;regime-switching jump diffusion delayed systems;stabilization", "DOI": "10.1002/asjc.3407", "PubYear": 2024, "Volume": "26", "Issue": "5", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences Anhui University  Hefei China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences Anhui University  Hefei China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Big Data Hefei University  Hefei China"}], "References": [{"Title": "Almost sure exponential stability of hybrid stochastic delayed <PERSON> neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "4", "Page": "1904", "JournalTitle": "Asian Journal of Control"}, {"Title": "Almost sure stability of stochastic neutral Cohen<PERSON> neural networks with Lévy noise and time‐varying delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "1", "Page": "371", "JournalTitle": "Asian Journal of Control"}, {"Title": "Dynamic event-triggered-based global output feedback control for stochastic nonlinear systems with time-varying delay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "647", "Issue": "", "Page": "119420", "JournalTitle": "Information Sciences"}, {"Title": "Predefined-time stabilization for nonlinear stochastic Itô systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "8", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 115647985, "Title": "An Approach to Drawing Automation of Ship Stiffeners in the Shipbuilding Industry", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2025.25-41", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": ""}], "References": []}, {"ArticleId": 115648001, "Title": "HtStego: A halftone steganography utility", "Abstract": "Steganography, the art and science of concealing secret information within seemingly innocent cover media, is crucial in secure information exchange and data protection. This work introduces a free and open-source steganography software for concealing plaintext payloads within halftone images, accompanied by a payload extraction utility to retrieve payloads from images generated using the featured utility. The utility can compress the payload before hiding and generate color and binary stego halftone images via a selection of methods, including ordered dithering and error diffusion, with support for custom-defined error diffusion kernels. A distinguishing feature of the utility is the distribution of payloads across multiple outputs to enhance payload security against unauthorized extraction and eliminate the necessity of the original image during payload extraction. Additionally, the utility provides quantitative assessments of image quality for the produced images, which is used in this paper to showcase the efficiency of the featured method.", "Keywords": "Steganography; Digital halftoning", "DOI": "10.1016/j.softx.2024.101780", "PubYear": 2024, "Volume": "27", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Efe Çi̇ftci̇", "Affiliation": "Department of Computer Engineering, Çankaya University, Ankara, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Başkent University, Ankara, Turkey;Corresponding author"}], "References": [{"Title": "Coverless steganography based on image retrieval of DenseNet features and DWT sequence mapping", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105375", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Secure RGB image steganography based on modified LSB substitution", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "453", "JournalTitle": "International Journal of Embedded Systems"}, {"Title": "Image Steganography in Spatial Domain: Current Status, Techniques, and Trends", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "69", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "A novel steganography method for binary and color halftone images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e1062", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A novel pixel-split image encryption scheme based on 2D Salomon map", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118845", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A secure data hiding approach based on least-significant-bit and nature-inspired optimization techniques", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "4639", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 115648014, "Title": "A two-stage clonal selection algorithm for local feature selection on high-dimensional data", "Abstract": "Various evolutionary computation algorithms have shown their excellent performance for high-dimensional feature selection (FS). However, most of current FS methods choose a global feature subset and ignore the correlations between feature subsets and sample subspaces, which limits the performance of methods in the sample space with different probability distributions. To solve the issue, we propose a two-stage clonal selection algorithm for filter-based local feature selection (TSCSA-LFS) that integrates symmetric uncertainty and a discrete clonal selection algorithm with three contributions. First, unlike conventional feature selection methods, TSCSA-LFS introduces local sample behaviors and assigns subsets of features for different sample regions. Second, an improved discrete clonal selection algorithm is developed for searching relevant features, which contains mutual information-based individual initialization, a differential evolution-based mutation strategy pool and a local search technique. Third, a two-part antibody representation is employed for automatical adjustment of the weight-related parameter. Our method shows the promising experimental results compared with well-known global FS and clonal selection-based local FS methods on fourteen high-dimensional datasets. For instance, our method can obviously outperform local FS, filter-based and hybrid methods on at least nine datasets", "Keywords": "", "DOI": "10.1016/j.ins.2024.120867", "PubYear": 2024, "Volume": "677", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Hubei University of Economics, Wuhan, 430205, China"}, {"AuthorId": 2, "Name": "<PERSON>o T<PERSON>", "Affiliation": "School of Information Engineering, Hubei University of Economics, Wuhan, 430205, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Cybersecurity, Sichuan University, Chengdu, 610065, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Cybersecurity, Sichuan University, Chengdu, 610065, China"}], "References": [{"Title": "An adaptive hybrid evolutionary immune multi-objective algorithm based on uniform distribution selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "446", "JournalTitle": "Information Sciences"}, {"Title": "Local feature selection based on artificial immune system for classification", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105989", "JournalTitle": "Applied Soft Computing"}, {"Title": "Clonal selection based intelligent parameter inversion algorithm for prestack seismic data", "Authors": "<PERSON><PERSON><PERSON> Yan; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "86", "JournalTitle": "Information Sciences"}, {"Title": "Feature selection using bare-bones particle swarm optimization with mutual information", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107804", "JournalTitle": "Pattern Recognition"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Binary Artificial Algae Algorithm for feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "108630", "JournalTitle": "Applied Soft Computing"}, {"Title": "A comprehensive survey on recent metaheuristics for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Hakan Ezgi Kiziloz", "PubYear": 2022, "Volume": "494", "Issue": "", "Page": "269", "JournalTitle": "Neurocomputing"}, {"Title": "An adaptive clonal selection algorithm with multiple differential evolution strategies", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "604", "Issue": "", "Page": "142", "JournalTitle": "Information Sciences"}, {"Title": "A multi-objective evolutionary algorithm with interval based initialization and self-adaptive crossover operator for large-scale feature selection in classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "109420", "JournalTitle": "Applied Soft Computing"}, {"Title": "A binary individual search strategy-based bi-objective evolutionary algorithm for high-dimensional feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "610", "Issue": "", "Page": "651", "JournalTitle": "Information Sciences"}, {"Title": "TSFNFS: two-stage-fuzzy-neighborhood feature selection with binary whale optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "2", "Page": "609", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Gazelle optimization algorithm: a novel nature-inspired metaheuristic optimizer", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "5", "Page": "4099", "JournalTitle": "Neural Computing and Applications"}, {"Title": "SemiACO: A semi-supervised feature selection based on ant colony optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "214", "Issue": "", "Page": "119130", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid Artificial Immune optimization for high-dimensional feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "260", "Issue": "", "Page": "110111", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "SHCNet: A semi-supervised hypergraph convolutional networks based on relevant feature selection for hyperspectral image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "165", "Issue": "", "Page": "98", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A feature selection approach based on NSGA-II with ReliefF", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109987", "JournalTitle": "Applied Soft Computing"}, {"Title": "A high-dimensional feature selection method based on modified Gray Wolf Optimization", "Authors": "Hongyu Pan; Shanxiong Chen; Hailing Xiong", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "110031", "JournalTitle": "Applied Soft Computing"}, {"Title": "Set-based integer-coded fuzzy granular evolutionary algorithms for high-dimensional feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>-T", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110240", "JournalTitle": "Applied Soft Computing"}, {"Title": "Differential evolution based on network structure for feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "635", "Issue": "", "Page": "279", "JournalTitle": "Information Sciences"}, {"Title": "A novel adaptive memetic binary optimization algorithm for feature selection", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "11", "Page": "13463", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An external attention-based feature ranker for large-scale feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "281", "Issue": "", "Page": "111084", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 115648055, "Title": "A survey on Deep-Learning-based image steganography", "Abstract": "With the development of Internet and multimedia development, digital image steganography is becoming more extensive in transmitting data with high capacity and security. Although there have been some relevant surveys, there is a lack of relevant surveys on Deep-Learning-based image steganography recently, especially for the developments and perspectives on generative steganography and reversible steganography. So this paper fills this gap and investigates recent developments in Deep-Learning-based image steganography from the viewpoints of strategy and network structures, which is summarized and categorized into ten strategies and five network models. In particular, this paper summarizes the relative merits and challenges of the leading techniques and discusses the research prospect of these fields for promoting the development of steganography.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124390", "PubYear": 2024, "Volume": "254", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Bingbing Song", "Affiliation": "National Pilot School of Software and the Engineering Research Center of Cyberspace, Yunnan University, Yunnan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Pilot School of Software and the Engineering Research Center of Cyberspace, Yunnan University, Yunnan, China"}, {"AuthorId": 3, "Name": "Six<PERSON>", "Affiliation": "National Pilot School of Software and the Engineering Research Center of Cyberspace, Yunnan University, Yunnan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Kunming Institute of Physics, Yunnan, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Pilot School of Software and the Engineering Research Center of Cyberspace, Yunnan University, Yunnan, China;Corresponding author"}], "References": [{"Title": "Anti-steganalysis for image on convolutional neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "7-8", "Page": "4315", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Secure image steganography using framelet transform and bidiagonal SVD", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "Page": "1865", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Random selection based GA optimization in 2D-DCT domain color image steganography", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7101", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Enhanced payload and trade-off for image steganography via a novel pixel digits alteration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7471", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A PVD based high capacity steganography algorithm with embedding in non-sequential position", "Authors": "<PERSON><PERSON><PERSON> (Ganguly); <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "13449", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel chaotic steganography method with three approaches for color and grayscale images based on FIS and DCT with flexible capacity", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "13693", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Image steganography based on Kirsch edge detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "1", "Page": "73", "JournalTitle": "Multimedia Systems"}, {"Title": "Retracing extended sudoku matrix for high-capacity image steganography", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "12", "Page": "18627", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "AdvSGAN: Adversarial image Steganography with adversarial networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "17", "Page": "25539", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An adaptive fuzzy inference approach for color image steganography", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "16", "Page": "10987", "JournalTitle": "Soft Computing"}, {"Title": "Image steganography using deep learning based edge detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "24", "Page": "33475", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Enhancing visual quality of spatial image steganography using SqueezeNet deep learning network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "28-29", "Page": "36093", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "CSST-Net: an arbitrary image style transfer network of coverless steganography", "Authors": "<PERSON><PERSON> Zhang; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "6", "Page": "2125", "JournalTitle": "The Visual Computer"}, {"Title": "Generative high-capacity image hiding based on residual CNN in wavelet domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "108170", "JournalTitle": "Applied Soft Computing"}, {"Title": "GAN-based image steganography for enhancing security via adversarial attack and pixel-wise deep fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "5", "Page": "6681", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Hybrid Secured Approach Combining LSB Steganography and AES Using Mosaic Images for Ensuring Data Security", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 115648180, "Title": "Optimal constant for generalized diagonal update method", "Abstract": "<p><PERSON><PERSON><PERSON>’s method finds a primary solvent of an unilateral quadratic matrix equation (AX^{2}+BX+C=0) when some conditions are given on its coefficients. When a stricter condition is given, diagonal update method improves the performances of <PERSON><PERSON><PERSON> method, both in terms of number of iterations and in computational time. In this paper, we suggest generalized diagonal update method which improves <PERSON><PERSON><PERSON>’s method without the stricter condition. We also define optimal constant for the generalized diagonal update method, which guarantees monotone convergence to the primary solvent. We compare iteration numbers of the generalized diagonal update method with pure <PERSON><PERSON><PERSON>’s method, also compare computational time of them with the <PERSON>’s method and the cyclic reduction. Furthermore, we show that this generalized diagonal update method is useful to solve unilateral quadratic matrix equations with the form (AX^2+ psilon BX+C=0) .</p>", "Keywords": "Quadratic matrix equation; M-matrix; Fixed-point iteration; <PERSON><PERSON><PERSON>’s method; Diagonal update method; 65H10", "DOI": "10.1007/s10092-024-00581-5", "PubYear": 2024, "Volume": "61", "Issue": "2", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MEDIFUN Inc., 8F_BIFC, 40, Munhyeongeumyung-ro, Nam-gu, Busan, Republic of Korea; H.A.S. Inc., Busan, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Pusan National University, Busan, Republic of Korea; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute for Mathematical Sciences (NIMS), Daejeon, Republic of Korea; Finance. Fishery. Manufacture Industrial Mathematics Center on Big Data, Pusan National University, Busan, Republic of Korea"}], "References": []}, {"ArticleId": 115648253, "Title": "Implementasi Perbandingan Metode Graphql Dan Rest Api Pada Teknologi Nodejs", "Abstract": "<p>Penelitian ini bertujuan untuk membandingkan implementasi metode GraphQL dan REST API pada teknologi Node.js. GraphQL adalah bahasa query yang fleksibel dan efisien, sedangkan REST API telah menjadi standar de facto dalam pengembangan aplikasi web. <PERSON><PERSON> penelitian ini, kami melakukan analisis mendalam terhadap kedua metode dengan membandingkan kinerja, keg<PERSON><PERSON>, dan pengalaman pengembang. Metode yang digunakan dalam penelitian ini adalah eksperimen, di mana kami mengembangkan dua aplikasi Node.js yang menerapkan metode GraphQL dan REST API secara terpisah. mengukur kinerja kedua metode menggunakan metrik seperti waktu respon  dan skalabilitas.<PERSON>il penelitian menunjukkan bahwa kedua metode memiliki kelebihan dan kelemahan yang berbeda. GraphQL menawarkan fleksibilitas yang lebih tinggi dalam mengambil data dengan hanya meminta bagian yang diperlukan, mengurangi pengulangan dan mengoptimalkan penggunaan sumber daya. <PERSON><PERSON>, penggunaan GraphQL membutuhkan pembelajaran yang lebih mendalam dan pemahaman yang lebih baik tentang struktur data.\r  \r Kata Kunci : Perbandingan, GraphQL, REST API, Teknologi Node.js, Fleksibilitas.</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i1.8656", "PubYear": 2024, "Volume": "7", "Issue": "1", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115648258, "Title": "On Critical Node Problems with Vulnerable Vertices", "Abstract": "A vertex pair in an undirected graph is called \\emph{connected} ifthe two vertices are connected by a path. In the NP-hard\\textsc{Critical Node Problem}~(CNP), the input is an undirectedgraph~$G$ with integers~$k$ and~$x$, and the question is whether onecan transform~$G$ by deleting at most~$k$ vertices into a graph whose totalnumber of connected vertex pairs is at most~$x$. In this work, weintroduce and study two NP-hard variants of CNP where a subset ofthe vertices is marked as \\emph{vulnerable}, and we aim to obtain agraph with at most~$x$ connected vertex pairs containing at least one vulnerable vertex. In the first variant, which generalizes CNP,we may delete vulnerable and non-vulnerable vertices. Inthe second variant, we may only delete non-vulnerable vertices. We perform a parameterized complexity study of both problems. For example, we show that both problems are FPT with respect to~$k+x$. Furthermore, in the case of deletable vulnerable nodes, we provide a polynomial kernel for the parameter~$vc+k$, where~$vc$ is the vertex cover number. In the case of non-deletable vulnerable nodes, we prove NP-hardness even when there is only one vulnerable node.", "Keywords": "graph connectivity;vertex deletion;social networks", "DOI": "10.7155/jgaa.v28i1.2922", "PubYear": 2024, "Volume": "28", "Issue": "1", "JournalId": 16217, "JournalTitle": "Journal of Graph Algorithms and Applications", "ISSN": "", "EISSN": "1526-1719", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Science, Friedrich Schiller Universität Jena"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer IOSB-INA Lemgo"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Computer Science, Friedrich Schiller Universität Jena"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Computer Science, Friedrich Schiller Universität Jena"}], "References": []}, {"ArticleId": 115648299, "Title": "Evaluating epistemic uncertainty estimation strategies in vegetation trait retrieval using hybrid models and imaging spectroscopy data", "Abstract": "The new-generation satellite imaging spectrometers provide an unprecedented data stream to be processed into quantifiable vegetation traits. Hybrid models have gained widespread acceptance in recent years due to their versatility in converting spectral data into traits. In hybrid models, the retrieval is obtained through a machine learning regression algorithm (MLRA) trained on a wide range of simulated data. For instance, they are currently under development for trait retrieval in preparation for the upcoming Copernicus Hyperspectral Imaging Mission for the Environment (CHIME), among others targeting routine estimation of canopy nitrogen content (CNC). However, like any retrieval algorithm, the process is not error-free, and most MLRAs inherently lack an uncertainty estimation related to the retrieved traits, which implies a risk of misinterpretation when applying the model to real-world data. Therefore, this study aimed to assess epistemic uncertainty estimation strategies (Bayesian method, drop-out, quantile regression, and bootstrapping) alongside the estimation of CNC using competitive MLRAs. Each of the regression models was evaluated using three data sets: (1) simulated scenes with varying noise using the SCOPE 2.1 radiative transfer model, (2) hyperspectral images from the PRISMA sensor, and (3) field-measured data. Analysis of generated uncertainty intervals led to the following findings: First, Gaussian processes regression (GPR) offers meaningful uncertainties, primarily attributable to spectral data degradation, which provide supplementary insights into the quality of trait mapping. Second, bootstrapping uncertainties can be used as quality indicators of the reliability of the estimates retrieved by hybrid models. Yet, its variability depends on the used MLRA, which impedes trusting its variance as a confidence interval. Third, quantile regression forest (QRF), despite not being top-performing algorithm, exhibit outstanding robustness estimations and uncertainty when the spectral data is degraded, either by Gaussian noise or by striping, often occurring in satellite imagery. Fourth, bootstrapped kernel ridge regression (KRR) demonstrated comparable performance to the benchmark algorithm GPR; the retrievals and uncertainties of these two MLRAs were highly correlated. Fifth, bootstrapped partial least squares regression (PLSR) estimations and uncertainties exhibit poor robustness to noise degradation, with normalized root mean square error (NRMSE) increasing from 19% to 112%. Additionally, a GUI tool was integrated into the ARTMO software package for assessing epistemic uncertainties from the embedded regression algorithms, providing a trait mapping quality indicator for mapping applications, and improving decision-making.", "Keywords": "PRISMA; CHIME; Retrieval; Machine learning; Uncertainty; Bootstrapping; Quantile regression; Gaussian processes; Random forest; Kernel ridge; Support vector machine; Least squares; Partial least squares; Canopy nitrogen content", "DOI": "10.1016/j.rse.2024.114228", "PubYear": 2024, "Volume": "310", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IPL - University of Valencia, Catedrático A<PERSON> 9, <PERSON><PERSON><PERSON>, 46980, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IPL - University of Valencia, Catedrático Agustín <PERSON> 9, <PERSON><PERSON><PERSON>, 46980, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IPL - University of Valencia, Catedrático Agustín <PERSON> 9, <PERSON><PERSON><PERSON>, 46980, Spain"}, {"AuthorId": 4, "Name": "<PERSON>-Venteo", "Affiliation": "IPL - University of Valencia, Catedrático Agustín <PERSON> 9, <PERSON><PERSON><PERSON>, 46980, Spain"}, {"AuthorId": 5, "Name": "<PERSON>Caicedo", "Affiliation": "Secretary of Research and Graduate Studies, CONACYT-UAN, Tepic 63155, Mexico"}, {"AuthorId": 6, "Name": "Jochem Verrelst", "Affiliation": "IPL - University of Valencia, Catedrático Agustín <PERSON> 9, <PERSON><PERSON><PERSON>, 46980, Spain"}], "References": [{"Title": "Crop nitrogen monitoring: Recent progress and principal developments in the context of imaging spectroscopy missions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "242", "Issue": "", "Page": "111758", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Evaluation of global leaf area index and fraction of absorbed photosynthetically active radiation products over North America using Copernicus Ground Based Observations for Validation data", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "247", "Issue": "", "Page": "111935", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Quantifying vegetation biophysical variables from the Sentinel-3/FLEX tandem mission: Evaluation of the synergy of OLCI and FLORIS data sources", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "251", "Issue": "", "Page": "112101", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "PROSPECT-PRO for estimating content of nitrogen-containing leaf proteins and other carbon-based constituents", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "252", "Issue": "", "Page": "112173", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "NASA's surface biology and geology designated observable: A perspective on surface imaging algorithms", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "257", "Issue": "", "Page": "112349", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Field spectroscopy of canopy nitrogen concentration in temperate grasslands using a convolutional neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "257", "Issue": "", "Page": "112353", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Quantifying uncertainty in high resolution biophysical variable retrieval with machine learning", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON>-<PERSON>; <PERSON>-Ta<PERSON>ner", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113199", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 115648307, "Title": "A robust defense for spiking neural networks against adversarial examples via input filtering", "Abstract": "Spiking Neural Networks (SNNs) are increasingly deployed in applications on resource constraint embedding systems due to their low power. Unfortunately, SNNs are vulnerable to adversarial examples which threaten the application security. Existing denoising filters can protect SNNs from adversarial examples. However, the reason why filters can defend against adversarial examples remains unclear and thus it cannot ensure a trusty defense. In this work, we aim to explain the reason and provide a more robust filter against different adversarial examples. First, we propose two new norms l 0 and l ∞ to describe the spatial and temporal features of adversarial events for understanding the working principles of filters. Second, we propose to combine filters to provide a robust defense against different perturbation events. To make up the gap between the goal and the ability of existing filters, we propose a new filter that can defend against both spatially and temporally dense perturbation events. We conduct the experiments on two widely used neuromorphic datasets, NMNIST and IBM DVSGesture. Experimental results show that the combined defense can restore the accuracy to over 80% of the original SNN accuracy.", "Keywords": "", "DOI": "10.1016/j.sysarc.2024.103209", "PubYear": 2024, "Volume": "153", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, National University of Defense Technology, No. 460 Huangshan Road, Hefei, 230037, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Defense Innovation Institute, AMS, Beijing, 100071, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Defense Innovation Institute, AMS, Beijing, 100071, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic Engineering, National University of Defense Technology, No. 460 Huangshan Road, Hefei, 230037, China;Anhui Province Key Laboratory of Cyberspace Security Situation Awareness and Evaluation, No. 460 Huangshan Road, Hefei, 230037, China;Corresponding author at: College of Electronic Engineering, National University of Defense Technology, No. 460 Huangshan Road, Hefei, 230037, China"}], "References": [{"Title": "A multi-objective LSM/NoC architecture co-design framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "102154", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Hardware-aware liquid state machine generation for 2D/3D Network-on-Chip platforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "102429", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": *********, "Title": "IoT based sensor network clustering for intelligent transportation system using meta‐heuristic algorithm", "Abstract": "Internet of Things (IoT) based sensor networks have been established as a pillar in intelligent communication systems for efficiently handling roadside congestion and accidents. These IoT networks sense, collect, and process data on a real‐time basis. However, IoT based sensor network clustering has various energy constraints such as inefficient routing due to long‐haul transmission, hot spot problem, network overhead, and unstable network whenever deployed along with the roadside that affect their architecture. In such networks, clustering techniques play a crucial role in extending the lifespan and optimizing the routes by integrating sensor devices through clusters. Therefore, a meta‐heuristic algorithm for clustering in IoT sensor networks for an intelligent transportation system is proposed. In this work, the seagull optimization algorithm is applied for clustering by considering residual and average energy, node spacing, and distance fitness parameters. Moreover, this work also considers the dynamic communication range of the cluster heads for increasing the stability period and lifetime of the proposed networks. The experiment results demonstrate that the proposed Seagull optimization algorithm for clustering in IoT networks (SOAC‐IoTNs) and Seagull optimization algorithm for clustering in IoT networks with dynamic communication range (SOAC‐IoTNs‐DR) achieve a significant increase in the stability period and network lifetime, with percentage increments of 55.68% and 71.47%, and 10.03% and 88.66% respectively, compared to the existing optimized genetic algorithm for cluster head selection with single static sink (OptiGACHS‐StSS).", "Keywords": "", "DOI": "10.1002/cpe.8193", "PubYear": 2024, "Volume": "36", "Issue": "20", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering Dr B <PERSON> NIT  Jalandhar India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering Dr B <PERSON> NIT  Jalandhar India"}, {"AuthorId": 3, "Name": " Manju", "Affiliation": "Department of CSE Jaypee Institute of Information Technology  Noida India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of IT Dr B R <PERSON> NIT Jalandhar  Jalandhar India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering and Computer Science Queen Mary University of London  London UK"}], "References": [{"Title": "Optimized Cluster-Based Dynamic Energy-Aware Routing Protocol for Wireless Sensor Networks in Agriculture Precision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Journal of Sensors"}, {"Title": "Clustering objectives in wireless sensor networks: A survey and research direction analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "180", "Issue": "", "Page": "107376", "JournalTitle": "Computer Networks"}, {"Title": "Energy-efficient cluster head selection algorithm for IoT using modified glow-worm swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "7", "Page": "6457", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An efficient cluster head election based on optimized genetic algorithm for movable sinks in IoT enabled HWSNs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "107318", "JournalTitle": "Applied Soft Computing"}, {"Title": "A secure energy-efficient routing protocol for disease data transmission using IoMT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "101", "Issue": "", "Page": "108113", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Modified energy-proficient partial coverage methodology for optimizing coverage in WSN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "15", "Page": "22669", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": *********, "Title": "Co-Citation of Co-Citations: Structuring the Subset of Scientific Documents Reflecting Modern Bibliometric Methods", "Abstract": "<p>A cluster analysis of the 100 most cited scientific papers on bibliometrics (scientometrics and informetrics) is carried out. The main bibliometric methods are identified, which make it possible to distinguish between the subject areas of their application. Co-citation of scientific papers and co-citation of their authors are still common. The analysis of scientific vocabulary (co-occurrence of words) complements the noted techniques. More often, in recent years, a set of bibliometric methods is used as a result of the application of modern computer programs to the statistical processing of bibliographic data. The fields of development and the application of bibliometrics related to separate clusters include nonmatching groups of various research topics in combination with different bibliometric methods. The use of bibliometrics in soft science (such as the social sciences) research is widespread, and in recent years it has not come at an inferior rate to its more traditional use in the natural sciences. The statistics on documents, references, and other indicators, to some extent, form the soft sciences themselves and delineate the areas of relevant studies.</p>", "Keywords": "bibliometric methods; co-citation clusters; co-word; research fronts; natural sciences; soft sciences", "DOI": "10.3103/S0005105524700055", "PubYear": 2024, "Volume": "58", "Issue": "2", "JournalId": 15290, "JournalTitle": "Automatic Documentation and Mathematical Linguistics", "ISSN": "0005-1055", "EISSN": "1934-8371", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Golikov Scientific and Clinical Center of Toxicology, Federal Medical Biological Agency, St. Petersburg, Russian Federation; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Golikov Scientific and Clinical Center of Toxicology, Federal Medical Biological Agency, St. Petersburg, Russian Federation"}], "References": [{"Title": "Domain Delineation of an Emerging Field of Interdisciplinary Research Using Scientometric Methods: Case Study of Exposomics", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "2", "Page": "104", "JournalTitle": "Automatic Documentation and Mathematical Linguistics"}]}, {"ArticleId": 115648478, "Title": "An interactive food recommendation system using reinforcement learning", "Abstract": "Food Recommendation System (FRS) assists individuals in making healthier dietary choices. However, current FRS uses collaborative filtering algorithms for one-step recommendations. Although these systems can recommend foods based on users’ historical preferences, they lack the adaptability to real-time changes in users’ health requirements and, as a result, the dynamic adjustment of recommendation strategies. This study introduces a groundbreaking approach by incorporating the dynamic and adaptive nature of reinforcement learning algorithms (RL) into FRS. The proposed multi-step recommendation framework, RecipeRL, leverages RL’s continuous decision-making and sustained interaction capabilities. To more accurately recommend foods aligned with user preferences, we introduce an effective method for expressing users’ real-time state through fused state representation. We also introduce an interactive environment to simulate authentic interactions between users and the recommendation system, enabling the system to handle multi-step recommendations. Our approach was evaluated using publicly available real-world datasets and compared to ten state-of-the-art methods. The results of the Top@10 analysis show that our method outperforms other algorithms significantly, achieving 94.68% and 95.67% for traditional Precision and the recommendation system metric NDCG, respectively. Our method also exhibits adaptability in scenarios where user preferences change, achieving 93.2% and 95.71%, respectively.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124313", "PubYear": 2024, "Volume": "254", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Faculty of Computing, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "Yi <PERSON>uan", "Affiliation": "The Faculty of Computing, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Faculty of Computing, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The Faculty of Computing, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The Faculty of Computing, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Artificial Intelligence Institute, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Faculty of Computing, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 8, "Name": "Jingchi Jiang", "Affiliation": "The Second Affiliated Hospital of Harbin Medical University, Harbin, China;Corresponding author"}], "References": [{"Title": "State representation modeling for deep reinforcement learning based recommendation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106170", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Food recommendation with graph convolutional network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "584", "Issue": "", "Page": "170", "JournalTitle": "Information Sciences"}, {"Title": "A deep learning based trust- and tag-aware recommender system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "488", "Issue": "", "Page": "557", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 115648484, "Title": "Dataset for Authentication and Authorization using Physical Layer Properties in Indoor Environment", "Abstract": "<p>The proliferation landscape of the Internet of Things (IoT) has accentuated the critical role of Authentication and Authorization (AA) mechanisms in securing interconnected devices. There is a lack of relevant datasets that can aid in building appropriate machine learning enabled security solutions focusing on authentication and authorization using physical layer characteristics. In this context, our research presents a novel dataset derived from real-world scenarios, utilizing Zigbee Zolertia Z1 nodes to capture physical layer properties in indoor environments. The dataset encompasses crucial parameters such as Received Signal Strength Indicator (RSSI), Link Quality Indicator (LQI), Device Internal Temperature, Device Battery Level, and more, providing a comprehensive foundation for advancing Machine learning enabled AA in IoT ecosystems.</p><p>© 2024 Published by Elsevier Inc.</p>", "Keywords": "Authentication;Authorization;Internet of things.;LQI;Machine learning;Physical layer;RSSI;Security", "DOI": "10.1016/j.dib.2024.110589", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing and Information Systems, Sunway University, Petaling Jaya, 47500 Selangor, Malaysia."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computing, University of Turku, FI-20014 <PERSON><PERSON>, Finland. ;Centre of Research Impact and Outcome, Chitkara University Institute of Engineering and Technology, Chitkara University, Punjab 140401, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Technology Sunway University No 5, Jalan Universiti, Bandar Sunway 47500 Selangor Darul Ehsan, Malaysia."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IoT & Wireless Communication Protocols Lab, Department of Electrical and Computer Engineering, International Islamic University Malaysia, Jalan Gombak, 53100 Selangor, Malaysia."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Software, Northwestern Polytechnical University, Xian, Shaanxi, PR China. ;Department of Electronics and Communication Engineering, Istanbul Technical University, Turkey."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Instituto de Telecomunicações, Escola Superior de Tecnologia e Gestão de Águeda, Universidade de Aveiro, Águeda, Portugal."}], "References": []}, {"ArticleId": 115648595, "Title": "A Comparison of Mixed and Partial Membership Diagnostic Classification Models with Multidimensional Item Response Models", "Abstract": "<p>Diagnostic classification models (DCM) are latent structure models with discrete multivariate latent variables. Recently, extensions of DCMs to mixed membership have been proposed. In this article, ordinary DCMs, mixed and partial membership models, and multidimensional item response theory (IRT) models are compared through analytical derivations, three example datasets, and a simulation study. It is concluded that partial membership DCMs are similar, if not structurally equivalent, to sufficiently complex multidimensional IRT models.</p>", "Keywords": "", "DOI": "10.3390/info15060331", "PubYear": 2024, "Volume": "15", "Issue": "6", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON> ", "Affiliation": "IPN—Leibniz Institute for Science and Mathematics Education, Olshausenstraße 62, 24118 Kiel, Germany; Centre for International Student Assessment (ZIB), Olshausenstraße 62, 24118 Kiel, Germany"}], "References": []}, {"ArticleId": 115648680, "Title": "Novel Watermarking and Scrambling for Convolution Neural Network Weights", "Abstract": "Deep Neural Networks (DNNs), has seen revolutionary progress in recent years. Its applications spread from naïve image classification application to complex natural language processing like ChatGPT etc. Training of deep neural network (DNN) needs extensive use of hardware, time, and technical intelligence to suit specific application on a specific embedded processor. Therefore, trained DNN weights and network architecture are the intellectual property which needs to be protected from possible theft or abuse at the various stage of model development and deployment. Hence there is need of protection of Intellectual property of DNN and also there is need of identification of theft even if it happens in some case to claim the ownership of DNN weights. The Intellectual Property protection of DNN weights has attracted increasing serious attention in the academia and industries. Many works on IP protection for Deep Neural Networks (DNN) weights have been proposed. The vast majority of existing work uses naïve watermarking extraction to verify the ownership of the model after piracy occurs. \nIn this paper a novel method for protection and identification for intellectual property related to DNN weights is presented. Our method is based on inserting the digital watermarks at learned least significant bits of weights for identification purpose and usages of hardware effuse for rightful usages of these watermarked weights on intended embedded processor.", "Keywords": "Convolution Neural Network deployment;Convolution Neural Network Intellectual Property protection;Copyright and Counterfeit of CNN weights;Secure Convolution Neural Network;Watermark CNN weights", "DOI": "10.2352/EI.2024.36.4.MWSF-331", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115648741, "Title": "Resource monitoring framework for big raw data processing", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJBDI.2024.138935", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 31878, "JournalTitle": "International Journal of Big Data Intelligence", "ISSN": "2053-1389", "EISSN": "2053-1397", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115648773, "Title": "Disturbance rejection and tracking control design for nonlinear semi-Markovian jump systems", "Abstract": "In this study, combined problem of robust output tracking and disturbance rejection for nonlinear semi-Markovian jump systems with time-varying delays and uncertainties is investigated in through improved-equivalent-input-disturbance-estimator (IEIDE)-based modified repetitive control (MRC) law. More precisely, IEIDE is introduced into the channel of control input to estimate and compensate the disturbance signals with high precision with an eye to attain the robust tracking performance. Subsequently, MRC block is internally embedded in the control loop that makes the system output signals to track the given reference signals inside the selected neighborhood of the equilibrium via repetitive learning. Furthermore, to obtain the controller and observer gain matrices, the linear matrix inequality conditions are derived based on the technique of Lyapunov stability. Finally, a switching boost converter circuit and single-link robot arm model are used to demonstrate the control scheme's efficiency and superiority. In addition to that the comparative study with the existing works are carried out. In this way, the potential benefits from practical perspectives of the developed results are validated.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120841", "PubYear": 2024, "Volume": "677", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Advanced Sciences, Vellore Institute of Technology, Chennai - 600127, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Bharathiar University, Coimbatore - 641046, India;Department of Mathematics, Sungkyunkwan University, Suwon - 440 746, South Korea;Corresponding author at: Department of Applied Mathematics, Bharathiar University, Coimbatore - 641046, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Bharathiar University, Coimbatore - 641046, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Chungbuk National University, Cheongju - 28644, South Korea;Corresponding author"}], "References": [{"Title": "Reinforcement learning based-adaptive tracking control for a class of semi-markov non-Lipschitz uncertain system with unmatched disturbances", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "626", "Issue": "", "Page": "407", "JournalTitle": "Information Sciences"}, {"Title": "Memory-based adaptive event-triggered asynchronous tracking control for semi-Markov jump systems with hybrid actuator faults", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "49", "Issue": "", "Page": "101359", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}]}, {"ArticleId": 115648776, "Title": "OpenRAND: A performance portable, reproducible random number generation library for parallel computations", "Abstract": "We introduce OpenRAND, a C++17 library aimed at facilitating reproducible scientific research by generating statistically robust yet replicable random numbers in as little as two lines of code, overcoming some of the unnecessary complexities of existing RNG libraries. OpenRAND accommodates single and multi-threaded applications on CPUs and GPUs and offers a simplified, user-friendly API that complies with the C++ standard’s random number engine interface. It is lightweight; provided as a portable, header-only library. It is statistically robust: a suite of built-in tests ensures no pattern exists within single or multiple streams. Despite its simplicity and portability, it remains performant—matching and sometimes outperforming native libraries. Our tests, including a Brownian walk simulation, affirm its reproducibility and ease-of-use while highlight its computational efficiency, outperforming CUDA’s cuRAND by up to 1.8 times.", "Keywords": "Pseudo random number generation; GPGPU; HPC; C++", "DOI": "10.1016/j.softx.2024.101773", "PubYear": 2024, "Volume": "27", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Michigan State University, East Lansing, MI 48824, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Michigan State University, East Lansing, MI 48824, United States of America;Department of Computational Mathematics, Science and Engineering, Michigan State University, East Lansing, MI 48824, United States of America"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Computational Biology, Flatiron Institute, New York, NY 10010, United States of America"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Michigan State University, East Lansing, MI 48824, United States of America"}], "References": []}, {"ArticleId": 115648836, "Title": "On large automata processing: towards a high level distributed graph language", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJBDI.2024.138936", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 31878, "JournalTitle": "International Journal of Big Data Intelligence", "ISSN": "2053-1389", "EISSN": "2053-1397", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Mouhamado<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115648852, "Title": "PASS-CCTV: Proactive Anomaly surveillance system for CCTV footage analysis in adverse environmental conditions", "Abstract": "In recent decades, the growing deployment of Closed-Circuit Television (CCTV) systems for crime prevention and facility security has accelerated the importance of intelligent surveillance technologies. One of the primary challenges in this field includes varying viewpoints and adverse weather conditions that significantly compromise the accuracy of human tracking and anomaly detection. Moreover, conventional surveillance systems often focus only on specific events within limited scenarios, which restricts their applicability. Existing deep learning approaches also face limitations in adaptability to environmental variations, mainly due to the high maintenance costs involved in data collection. To address these challenges, we present a comprehensive surveillance system that utilizes deep learning to enhance human tracking and anomaly detection across diverse environments. Our approach includes the implementation of novel object filtering algorithms that decrease false positive rates and improve tracking precision. Additionally, our system is capable of monitoring multiple types of abnormal events, such as intrusion, loitering, abandonment, and arson. We further introduce a prompt-based recognition mechanism that enables active user participation in identifying abnormal scenes. Extensive evaluations using the Korea Internet & Security Agency CCTV datasets have demonstrated significant performance enhancements by our system, particularly under challenging weather conditions. Moreover, our system achieved competitive accuracy on the ABODA and FireNet datasets, even without additional training. This research establishes a new baseline for practical surveillance solutions that focus on comprehensive monitoring across various abnormal scenarios.", "Keywords": "Video surveillance system; Video anomaly detection; Human tracking; Vision–language model", "DOI": "10.1016/j.eswa.2024.124391", "PubYear": 2024, "Volume": "254", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Hobeom Jeon", "Affiliation": "University of Science and Technology, Deajeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Science and Technology, Deajeon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Science and Technology, Deajeon, Republic of Korea;Electronics and Telecommunications Research Institute, Deajeon, Republic of Korea;Corresponding author at: Electronics and Telecommunications Research Institute, Deajeon, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Telecommunications Research Institute, Deajeon, Republic of Korea"}], "References": [{"Title": "Scalable object detection pipeline for traffic cameras: Application to Tfl JamCams", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115154", "JournalTitle": "Expert Systems with Applications"}, {"Title": "FairMOT: On the Fairness of Detection and Re-identification in Multiple Object Tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "11", "Page": "3069", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Fallen person detection for autonomous driving", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119242", "JournalTitle": "Expert Systems with Applications"}, {"Title": "FireNet-v2: Improved Lightweight Fire Detection Model for Real-Time IoT Applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "2233", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 115648877, "Title": "Evolutionary Seeding of Diverse Structural Design Solutions via Topology Optimization", "Abstract": "<p>Topology optimization is a powerful design tool in structural engineering and other engineering problems. The design domain is discretized into elements, and a finite element method model is iteratively solved to find the element that maximizes the structure's performance. Although gradient-based solvers have been used to solve topology optimization problems, they may be susceptible to suboptimal solutions or difficulty obtaining feasible solutions, particularly in non-convex optimization problems. The presence of non-convexities can hinder convergence, leading to challenges in achieving the global optimum. With this in mind, we discuss in this paper the application of the quality diversity approach to topological optimization problems. Quality diversity (QD) algorithms have shown promise in the research field of optimization and have many applications in engineering design, robotics, and games. MAP-Elites is a popular QD algorithm used in robotics. In soft robotics, the MAP-Elites algorithm has been used to optimize the shape and control of soft robots, leading to the discovery of new and efficient motion strategies. This paper introduces an approach based on MAP-Elites to provide diverse designs for structural optimization problems. Three fundamental topology optimization problems are used for experimental testing, and the results demonstrate the ability of the proposed algorithm to generate diverse, high-performance designs for those problems. Furthermore, the proposed algorithm can be a valuable engineering design tool capable of creating novel and efficient designs.</p>", "Keywords": "", "DOI": "10.1145/3670693", "PubYear": 2024, "Volume": "4", "Issue": "4", "JournalId": 87585, "JournalTitle": "ACM Transactions on Evolutionary Learning and Optimization", "ISSN": "2688-299X", "EISSN": "2688-3007", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Cambridge, UK and CSIRO, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CSIRO, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "CSIRO, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CSIRO, Australia"}], "References": [{"Title": "From Bioinspiration to Computer Generation: Developments in Autonomous Soft Robot Design", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "1", "Page": "2100086", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": *********, "Title": "Compressing spectral kernels in Gaussian Process: Enhanced generalization and interpretability", "Abstract": "The modeling capabilities of a Gaussian Process (GP), such as generalization, nonlinearity, and smoothness, are largely determined by the choice of its kernel. A popular family of kernels for GPs, the spectral mixture (SM) kernels, have the desirable property that with a large number of spectral components they can approximate any stationary kernel. However, using a large number of SM components increases the risk of overfitting and hinders interpretability. To overcome these challenges, we propose a compression algorithm incorporating component pruning and component merging for GPs. Here SM components with small signal variance are removed, and a moment-matching merge method is proposed to further reduce the number of SM components. The main novelty of the proposed method is a similarity measure between SM components based on their normalized cross-correlation, which is related to the Bhatta<PERSON>ryya coefficient. We derive a greedy GP compression algorithm and perform a comparative evaluation over various learning tasks in terms of forecasting performance and compression capability. Results substantiate the beneficial effect of the method, both in terms of generalization and interpretability. <sup>1</sup>", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110642", "PubYear": 2024, "Volume": "155", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, Central South University, Changsha 410083, China;Institute for Computing and Information Sciences, Radboud University, Nijmegen, The Netherlands;Corresponding author at: School of Mathematics and Statistics, Central South University, Changsha 410083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Computing and Information Sciences, Radboud University, Nijmegen, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Computing and Information Sciences, Radboud University, Nijmegen, The Netherlands"}], "References": [{"Title": "Leveraging the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> coefficient for uncertainty quantification in deep neural networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "16", "Page": "10259", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Gaussian processes with skewed Laplace spectral mixture kernels for long-term forecasting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "8", "Page": "2213", "JournalTitle": "Machine Learning"}, {"Title": "Robust Gaussian process regression with a bias model", "Authors": "Chiwoo Park; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108444", "JournalTitle": "Pattern Recognition"}, {"Title": "Bayesian mixture of gaussian processes for data association problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "108592", "JournalTitle": "Pattern Recognition"}, {"Title": "Dirichlet process mixture of Gaussian process functional regressions and its variational EM algorithm", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109129", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 115648901, "Title": "A spatio-temporal prediction methodology based on deep learning and real Wi-Fi measurements", "Abstract": "The rapid development of Wi-Fi technologies in recent years has caused a significant increase in the traffic usage. Hence, knowledge obtained from Wi-Fi network measurements can be helpful for a more efficient network management. In this paper, we propose a methodology to predict future values of some specific network metrics (e.g. traffic load, transmission failures, etc.). These predictions may be useful for improving the network performance. After data collection and preprocessing, the correlation between each target access point (AP) and its neighbouring APs is estimated. According to these correlations, either an only-temporal or a spatio-temporal based prediction is done. To evaluate the proposed methodology, real measurements are collected from 100 APs deployed in different university buildings for 3 months. Deep Learning (DL) methods (i.e. Convolutional Neural Network (CNN), Simple Recurrent Neural Network (SRNN), Gated Recurrent Unit (GRU), Long Short-Term Memory (LSTM), Transformer) are evaluated and compared for both temporal and spatio-temporal based predictions. Moreover, a hybrid prediction methodology is proposed using a spatial processing based on CNN and a temporal prediction based on RNN. The proposed hybrid methodology provides an improvement in the prediction accuracy at expenses of a slight increase in the Training Computational Time (TCT) and negligible in Prediction Computational Time (PCT).", "Keywords": "", "DOI": "10.1016/j.comnet.2024.110569", "PubYear": 2024, "Volume": "250", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Signal Theory and Communications, Universitat Politècnica de Catalunya (UPC), Carrer de Jordi Girona, 1-3, 08034, Barcelona, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Signal Theory and Communications, Universitat Politècnica de Catalunya (UPC), Carrer de Jordi Girona, 1-3, 08034, Barcelona, Spain"}], "References": [{"Title": "Real-time throughput prediction for cognitive Wi-Fi networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "102499", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 115648915, "Title": "Consistency approximation: Incremental feature selection based on fuzzy rough set theory", "Abstract": "Fuzzy Rough Set Theory (FRST)-based feature selection has been widely used as a preprocessing step to handle dynamic and large datasets. However, large-scale or high-dimensional datasets remain intractable for FRST-based feature selection approaches due to high space complexity and unsatisfactory classification performance. To overcome these challenges, we propose a Consistency Approximation (CA)-based framework for incremental feature selection. By exploring CA, we introduce a novel significance measure and a tri-accelerator. The CA-based significance measure provides a mechanism for each sample in the universe to keep members with different class labels within its fuzzy neighbourhood as far as possible, while keeping members with the same label as close as possible. Furthermore, our tri-accelerator reduces the search space and decreases the computational space with a theoretical lower bound. The experimental results demonstrate the superiority of our proposed algorithm compared to state-of-the-art methods on efficiency and classification accuracy, especially for large-scale and high-dimensional datasets.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110652", "PubYear": 2024, "Volume": "155", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Data Science and Business Intelligence, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "Daiyang Wu", "Affiliation": "Department of Data Science and Business Intelligence, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Data Science and Business Intelligence, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Data Science and Business Intelligence, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Human–machine Interaction and Intelligent Decision, Nanning Normal University, Nanning, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON> See-To", "Affiliation": "Department of Computing and Decision Sciences, Faculty of Business, Lingnan University, Hong Kong, China"}], "References": [{"Title": "Incremental feature selection based on fuzzy rough sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "536", "Issue": "", "Page": "185", "JournalTitle": "Information Sciences"}, {"Title": "Online group streaming feature selection considering feature interaction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107157", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Online group streaming feature selection considering feature interaction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107157", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Dynamic interaction feature selection based on fuzzy rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "891", "JournalTitle": "Information Sciences"}, {"Title": "Online early terminated streaming feature selection based on Rough Set theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107993", "JournalTitle": "Applied Soft Computing"}, {"Title": "Dynamic feature weighting for data streams with distribution-based log-likelihood divergence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "107", "Issue": "", "Page": "104509", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Hybrid filter–wrapper attribute selection with alpha-level fuzzy rough sets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116428", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Online feature selection for multi-source streaming features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "590", "Issue": "", "Page": "267", "JournalTitle": "Information Sciences"}, {"Title": "Exploring interactive attribute reduction via fuzzy complementary entropy for unlabeled mixed data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "108651", "JournalTitle": "Pattern Recognition"}, {"Title": "Incremental feature selection by sample selection and feature-based accelerator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108800", "JournalTitle": "Applied Soft Computing"}, {"Title": "Incremental feature selection for dynamic incomplete data using sub-tolerance relations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110125", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 115648918, "Title": "Evaluation of Conventional and Quantum Computing for Predicting Mortality Based on Small Early-Onset Colorectal Cancer Data", "Abstract": "<b >Background</b> Quantum computing integrated with machine learning (ML) offers novel solutions in various fields, including healthcare. The synergy between quantum computing and ML in classification exploits unique data patterns. Despite theoretical advantages, the empirical application and effectiveness of quantum computing on small medical datasets remains underexplored. <b >Method</b> This retrospective study from a tertiary hospital used data on early-onset colorectal cancer with 93 features and 1501 patients from 2008 to 2020 to predict mortality. We compared quantum support vector machine (QSVM) models with classical SVM models in terms of number of features, number of training sets, and outcome ratio. We evaluated the model based on the area under the curve in the receiver operating characteristic curve (AUROC). <b >Results</b> We observed a mortality rate of 7.6 % (96 of 1253 subjects). We generated the mortality prediction model using 11 clinical variables, including cancer stage and chemotherapy history. We found that the AUROC difference between the conventional and quantum methods was the maximum for the top 11 variables. We also showed the AUROC in QSVM (mean [standard deviation], 0.863 [0.102]) outperformed all the number of trials in the conventional SVM (0.723 [0.231]). Compared to the conventional SVM, the QSVM showed robust performance, consistent with the AUROC, even in the unbalanced case. <b >Conclusion</b> Our study highlights the potential of quantum computing to improve predictive modeling in healthcare, especially for rare diseases with limited available data. The advantages of quantum computing, such as the exploration of Hilbert space, contributed to the superior predictive performance compared to conventional methods.", "Keywords": "Empirical quantum advantage; Quantum computing; Digital healthcare; Young early-onset colorectal cancer; AUROC Area under the receiver operating characteristic; AUPRC Area under precision-recall curve; EOCRC early-onset colorectal cancer; EQA empirical quantum advantage; ML machine learning; QML Quantum-Enhanced ML; QSVM quantum support vector machine; SDs standard deviations; SVM support vector machine; VQC variational quantum circuit", "DOI": "10.1016/j.asoc.2024.111781", "PubYear": 2024, "Volume": "162", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Systems Informatics, Yonsei University College of Medicine, Seoul, the Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>oo <PERSON><PERSON>", "Affiliation": "Department of Biomedical Systems Informatics, Yonsei University College of Medicine, Seoul, the Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Systems Informatics, Yonsei University College of Medicine, Seoul, the Republic of Korea"}, {"AuthorId": 4, "Name": "Si <PERSON> Park", "Affiliation": "KAIST, School of Electrical Engineering, Daejeon 34141, the Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Yonsei Cancer Center, Division of Medical Oncology, Department of Internal Medicine, Yonsei University College of Medicine, Seoul, the Republic of Korea;Correspondence to: Yonsei Cancer Center, Division of Medical Oncology, Department of Internal Medicine, Yonsei University College of Medicine, 50-1 Yonsei-ro, Seodaemun-gu, Seoul 03722, the Republic of Korea.; Corresponding author"}, {"AuthorId": 6, "Name": "Yu Rang Park", "Affiliation": "Department of Biomedical Systems Informatics, Yonsei University College of Medicine, Seoul, the Republic of Korea;Correspondence to: Department of Biomedical Systems Informatics, Yonsei University College of Medicine, 50-1 Yonsei-ro, Seodaemun-gu, Seoul 03722, the Republic of Korea.; Corresponding author"}], "References": [{"Title": "The power of quantum neural networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "6", "Page": "403", "JournalTitle": "Nature Computational Science"}, {"Title": "Explainable Heart Disease Prediction Using Ensemble-Quantum Machine Learning Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "36", "Issue": "1", "Page": "761", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Quantum Computing for Healthcare: A Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "94", "JournalTitle": "Future Internet"}]}, {"ArticleId": 115648950, "Title": "Exploring the Evolution of Brand Loyalty in the Age of Social Media", "Abstract": "This article examines the evolution of brand loyalty in the context of social media’s pervasive influence on consumer behavior and marketing practices. Traditionally, brand loyalty has been characterized by consumers’ consistent preference for a particular brand. However, the emergence of social media platforms has transformed the marketing landscape, offering brands unprecedented opportunities to engage with their audience, foster relationships, and cultivate loyalty. With billions of users actively participating on platforms like Facebook, Instagram, Twitter, and LinkedIn, social media has become a powerful tool for brands to connect with consumers. This paper highlights the significance of understanding the evolving nature of brand loyalty in the age of social media. It emphasizes the need for marketers to adapt their strategies to leverage this new terrain effectively. Through a review of relevant literature and theoretical frameworks, the article explores the implications of social media on brand-consumer relationships. It offers practical recommendations for marketers aiming to capitalize on the opportunities presented by the digital landscape.", "Keywords": "Brand Loyalty;Social Media Marketing;Engagement Strategies;User-Generated Content (UGC)", "DOI": "10.4236/jssm.2024.173011", "PubYear": 2024, "Volume": "17", "Issue": "3", "JournalId": 15061, "JournalTitle": "Journal of Service Science and Management", "ISSN": "1940-9893", "EISSN": "1940-9907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Studies, State University of Bangladesh, Dhaka, Bangladesh"}, {"AuthorId": 2, "Name": "Habiba Kibria", "Affiliation": "Monash College Pathways, Universal College Bangladesh, Dhaka, Bangladesh"}], "References": [{"Title": "The effects of trait-based personalization in social media advertising", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106525", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Can social media-based brand communities build brand relationships? Examining the effect of community engagement on brand love", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "6", "Page": "1270", "JournalTitle": "Behaviour & Information Technology"}]}, {"ArticleId": 115648988, "Title": "Neural Causal Graph Collaborative Filtering", "Abstract": "Graph collaborative filtering (GCF) has emerged as a prominent method in recommendation systems, leveraging the power of graph learning to enhance traditional collaborative filtering (CF). One common approach in GCF involves employing Graph Convolutional Networks (GCN) to learn user and item embeddings and utilize these embeddings to optimize CF models. However, existing GCN-based methods often fall short of generating satisfactory embeddings, mainly due to their limitations in capturing node dependencies and variable dependencies within the graph. Consequently, the learned embeddings are fragile in uncovering the root causes of user preferences, leading to sub-optimal performance of GCF models. In this work, we propose integrating causal modeling with the learning process of GCN-based GCF models, leveraging causality-aware graph embeddings to capture complex dependencies in recommendations. Our methodology encompasses three key designs: 1) Causal Graph conceptualization, 2) Neural Causal Model parameterization, and 3) Variational inference for the Neural Causal Model. We define a Causal Graph to model genuine dependencies in GCF models and utilize this Causal Graph to parameterize a Neural Causal Model. The proposed framework, termed Neural Causal Graph Collaborative Filtering (NCGCF) , uses variational inference to approximate neural networks under the Neural Causal Model. As a result, NCGCF is able to leverage the expressive causal effects from the Causal Graph to enhance graph representation learning. Extensive experimentation on four datasets demonstrates NCGCF's ability to deliver precise recommendations consistent with user preferences.", "Keywords": "Graph representation learning; Causal inference; Neural causal model; Recommendation system", "DOI": "10.1016/j.ins.2024.120872", "PubYear": 2024, "Volume": "677", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Technology Sydney, Sydney, NSW, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Curtin University, Perth, WA, Australia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Technology Sydney, Sydney, NSW, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "RIKEN Center for AIP, Tokyo, Japan"}, {"AuthorId": 5, "Name": "Qing Li", "Affiliation": "Hong Kong Polytechnic University, Hong Kong Special Administrative Region of China"}, {"AuthorId": 6, "Name": "G<PERSON><PERSON> Xu", "Affiliation": "University of Technology Sydney, Sydney, NSW, Australia;Education University of Hong Kong, Hong Kong Special Administrative Region of China;Corresponding author at: University of Technology Sydney, Sydney, NSW, Australia"}], "References": [{"Title": "Graph Learning: A Survey", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "109", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Deconfounded recommendation via causal intervention", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "529", "Issue": "", "Page": "128", "JournalTitle": "Neurocomputing"}, {"Title": "Analysis of Markovian Jump Stochastic Cohen–<PERSON>berg BAM Neural Networks with Time Delays for Exponential Input-to-State Stability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "8", "Page": "11055", "JournalTitle": "Neural Processing Letters"}, {"Title": "Contrastive graph learning long and short-term interests for POI recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121931", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A federated recommendation algorithm based on user clustering and meta-learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "158", "Issue": "", "Page": "111483", "JournalTitle": "Applied Soft Computing"}, {"Title": "Transfer Learning in Cross-Domain Sequential Recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "669", "Issue": "", "Page": "120550", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 115648997, "Title": "On the temporal stability of least-squares methods for linear hyperbolic problems", "Abstract": "Standard Galerkin methods often perform poorly for problems with low diffusion. In particular for purely convective transport, least-squares (LS) formulations provide a good alternative. While spatial stability is relatively straightforward in a least-squares finite element framework, estimates in time are restricted, in most cases, to one dimension. This article presents temporal stability proofs for the LS formulation of θ -schemes, including unconditional stability for the backward <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> methods in two or three dimensions. The theory includes also the linear advection-reaction equation. A series of numerical experiments confirm that the new stability estimates are sharp.", "Keywords": "Pure convection; Least-squares methods; Least-squares FEM; Unconditional stability; Numerical analysis; Advection-reaction equation", "DOI": "10.1016/j.camwa.2024.05.023", "PubYear": 2024, "Volume": "168", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chair for Computational Analysis of Technical Systems, RWTH Aachen University, Germany;Chair of Methods for Model-based Development in Computational Engineering, RWTH Aachen University, Germany;Department of Mathematical Sciences, Norwegian University of Science and Technology, Trondheim, Norway;Correspondence to: Chair for Computational Analysis of Technical Systems, RWTH Aachen University, Aachen, Germany.; Corresponding author"}], "References": []}, {"ArticleId": 115649079, "Title": "A Comprehensive Dataset for Arabic Word Sense Disambiguation", "Abstract": "<p>This data paper introduces a comprehensive dataset tailored for word sense disambiguation tasks, explicitly focusing on a hundred polysemous words frequently employed in Modern Standard Arabic. The dataset encompasses a diverse set of senses for each word, ranging from 3 to 8, resulting in 367 unique senses. Each word sense is accompanied by contextual sentences comprising ten sentence examples that feature the polysemous word in various contexts. The data collection resulted in a dataset of 3670 samples. Significantly, the dataset is in Arabic, which is known for its rich morphology, complex syntax, and extensive polysemy. The data was meticulously collected from various web sources, spanning news, medicine, finance, and more domains. This inclusivity ensures the dataset's applicability across diverse fields, positioning it as a pivotal resource for Arabic Natural Language Processing (NLP) applications. The data collection timeframe spans from the first of April 2023 to the first of May 2023. The dataset provides comprehensive model learning by including all senses for a frequently used Arabic polysemous term, even rare senses that are infrequently used in real-world contexts, thereby mitigating biases. The dataset comprises synthetic sentences generated by GPT3.5-turbo, addressing instances where rare senses lack sufficient real-world data. The dataset collection process involved initial web scraping, followed by manual sorting to distinguish word senses, supplemented by thorough searches by a human expert to fill in missing contextual sentences. Finally, in instances where online data for rare word senses was lacking or insufficient, synthetic samples were generated. Beyond its primary utility in word sense disambiguation, this dataset holds considerable value for scientists and researchers across various domains, extending its relevance to sentiment analysis applications.</p><p>© 2024 The Author(s).</p>", "Keywords": "Arabic language;Deep learning;GPT3.5;Labelled data;Machine learning;Natural language processing;Word sense disambiguation", "DOI": "10.1016/j.dib.2024.110591", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computing and Applied Technology, College of Technological Innovation, Zayed University, UAE."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computing and Applied Technology, College of Technological Innovation, Zayed University, UAE."}], "References": []}, {"ArticleId": 115649084, "Title": "Estrogen receptor β activation alleviates inflammatory bowel disease by suppressing NLRP3-dependent IL-1β production in macrophages via downregulation of intracellular calcium level", "Abstract": "<p><b>INTRODUCTION</b>:Although several estrogen receptor β (ERβ) agonists have been reported to alleviate IBD, the pivotal mechanism remains obscure.</p><p><b>OBJECTIVES</b>:To examine the effects and mechanisms of ERβ activation on cytokine/chemokine networks in colitis mice.</p><p><b>METHODS</b>:Dextran sulfate sodium salt (DSS) and trinitro-benzene-sulfonic acid (TNBS) were used to induce mouse colitis model. Multiple molecular biological methods were employed to evaluate the severity of mouse colitis and the level of cytokine and/or chemokine.</p><p><b>RESULTS</b>:Bioinformatics analysis, ELISA and immunofluorescence results showed that the targeted cytokines and/or chemokines associated with ERβ expression and activation is IL-1β, and the anti-colitis effect of ERβ activation was significantly attenuated by the overexpression of AAV9-IL-1β. Immunofluorescence analysis indicated that ERβ activation led to most evident downregulation of IL-1β expression in colonic macrophages as compared to monocytes and neutrophils. Given the pivotal roles of NLRP3, NLRC4, and AIM2 inflammasome activation in the production of IL-1β, we examined the influence of ERβ activation on inflammasome activity. ELISA and WB results showed that ERβ activation selectively blocked the NLRP3 inflammasome assembly-mediated IL-1β secretion. The calcium-sensing receptor (CaSR) and calcium signaling play crucial roles in the assembly of the NLRP3 inflammasome. WB and immunofluorescence results showed that ERβ activation reduced intracellular CaSR expression and calcium signaling in colonic macrophages. Combination with CaSR overexpression plasmid reversed the suppressive effect of ERβ activation on NLRP3 inflammasome assembly, and counteracting the downregulation of IL-1β secretion.</p><p><b>CONCLUSION</b>:Our research uncovers that the anti-colitis effect of ERβ activation is accomplished through the reduction of IL-1β levels in colonic tissue, achieved by specifically decreasing CaSR expression in macrophages to lower intracellular calcium levels and inhibit NLRP3 inflammasome assembly-mediated IL-1β production.</p><p>Copyright © 2024. Production and hosting by Elsevier B.V.</p>", "Keywords": "Calcium-sensing receptor;Estrogen receptor β;Inflammasome;Inflammatory bowel disease;Interleukin-1β", "DOI": "10.1016/j.jare.2024.06.004", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacology of Chinese Materia Medica, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmacology of Chinese Materia Medica, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmacology of Chinese Materia Medica, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Pharmacognosy, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacology of Chinese Materia Medica, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China."}, {"AuthorId": 6, "Name": "Yufeng Xia", "Affiliation": "Department of Pharmacognosy, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmacology of Chinese Materia Medica, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacology of Chinese Materia Medica, School of Traditional Chinese Pharmacy, China Pharmaceutical University, 639 Long Mian Avenue, Nanjing 211198, China"}], "References": []}, {"ArticleId": 115649186, "Title": "The Effect of Stereoscopic Depth Distortion on the Near Oculomotor Response", "Abstract": "During natural viewing, the oculomotor system interacts with depth information through a correlated, tightly related linkage between convergence, accommodation, and pupil miosis known as the near response. When natural viewing breaks down, such as when depth distortions and cue conflicts are introduced in a stereoscopic remote vision system (sRVS), the individual elements of the near response may decouple (e.g., vergence-accommodation, or VA, mismatch), limiting the comfort and usability of the sRVS. Alternatively, in certain circumstances the near response may become more tightly linked to potentially preserve image quality in the presence of significant distortion. In this experiment, we measured two elements of the near response (vergence posture and pupil size) of participants using an sRVS. We manipulated the degree of depth distortion by changing the viewing distance, creating a perceptual compression of the image space, and increasing the VA mismatch. We found a strong positive cross-correlation of vergence posture and pupil size in all participants in both conditions. The response was significantly stronger and quicker in the near viewing condition, which may represent a physiological response to maintain image quality and increase the depth of focus through pupil miosis.", "Keywords": "pupil size;vergence;vergence accommodation mismatch", "DOI": "10.2352/EI.2024.36.2.SDA-349", "PubYear": 2024, "Volume": "36", "Issue": "2", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115649202, "Title": "Analyzing Quantitative Detectors for Content-Adaptive Steganography", "Abstract": "In this article, we study the properties of quantitative steganography detectors (estimators of the payload size) for content-adaptive steganography. In contrast to non-adaptive embedding, the estimator's bias as well as variance strongly depend on the true payload size. Initially, and depending on the image content, the estimator may not react to embedding. With increased payload size, it starts responding as the embedding changes begin to ``spill'' into regions where their detection is more reliable. We quantify this behavior with the concepts of reactive and estimable payloads. To better understand how the payload estimate and its bias depend on image content, we study a maximum likelihood estimator derived for the MiPOD model of the cover image. This model correctly predicts trends observed in outputs of a state-of-the-art deep learning payload regressor. Moreover, we use the model to demonstrate that the cover bias can be caused by a small number of ``outlier'' pixels in the cover image. This is also confirmed for the deep learning regressor on a dataset of artificial images via attribution maps.", "Keywords": "deep learning;quantitative;steganalysis", "DOI": "10.2352/EI.2024.36.4.MWSF-336", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115649236, "Title": "<PERSON><PERSON><PERSON>tan Energi Nasional Menggunakan Data Energi Tahun 2013-2022", "Abstract": "<p>Proses pengambilan keputusan untuk meningkatkan keberlanjutan energi memerlukan informasi yang berbasis ilmiah. <PERSON><PERSON> penelitian ini, berbagai indikator dikumpulkan dan dianalisis secara kuantitatif untuk mengukur keberlanjutan energi nasional menggunakan indeks pembangunan energi berkelanjutan yang berasal dari indikator tersebut. Dengan menerapkan metode analisis ini, tantangan-tantangan terbesar yang terkait dengan keberlanjutan energi dapat diteliti. <PERSON><PERSON> itu, penelitian ini bertujuan untuk mengidentifikasi faktor-faktor yang menghambat Indonesia untuk mencapai keberlanjutan energi selama periode 2013-2022.\r Kata Kunci: Energi Berkelanjutan, Energi Terbarukan, Indikator</p>", "Keywords": "", "DOI": "10.31539/intecoms.v7i3.9979", "PubYear": 2024, "Volume": "7", "Issue": "3", "JournalId": 55838, "JournalTitle": "INTECOMS: Journal of Information Technology and Computer Science", "ISSN": "", "EISSN": "2614-1574", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Kristen Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas Kristen Indonesia"}], "References": []}, {"ArticleId": 115649247, "Title": "Unveiling Neurophysiological Markers of Consciousness Levels through EEG Exploration", "Abstract": "<p>The concept of consciousness levels typically refers to various aspects and tiers related to an individual’s cognition, perception, thinking, and awareness. Although neurophysiological markers have not yet been definitively identified to distinguish between these nuanced levels, this paper introduces a robust marker, the Approximate Entropy (ApEn), which quantifies the complexity of EEG signals to differentiate states of altered consciousness. Utilizing ApEn, we analyze EEG data from the frontal lobe—a region closely associated with consciousness—in states indicative of severely altered conditions, specifically anesthesia, coma, and brain death. To enhance the precision of consciousness levelassessment, we employ a Support Vector Machine (SVM) model, which classifies the states based on EEG complexity measures. This approach not only provides valuable insights into the neural correlations associated with changes in these critical states but also underscores the potential of combining quantitative EEG analysis with machine learning techniques to advance our understanding of consciousness. The findings demonstrate that EEG complexity, when analyzed using ApEn coupled with SVM classification, offers a novel and effective method for assessing and distinguishing between degrees of consciousness. This approach promises significant implications for clinical diagnostics and patient monitoring.</p>", "Keywords": "EEG;Approximate Entropy;Consciousness Levels", "DOI": "10.24297/ijct.v24i.9627", "PubYear": 2024, "Volume": "24", "Issue": "", "JournalId": 55824, "JournalTitle": "INTERNATIONAL JOURNAL OF COMPUTERS & TECHNOLOGY", "ISSN": "", "EISSN": "2277-3061", "Authors": [{"AuthorId": 1, "Name": "Jingming Gong", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 2, "Name": "Linfeng Sui", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 4, "Name": "Boning Li", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 5, "Name": "Chengyuan Shen", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 6, "Name": ",Taiyo Maeda", "Affiliation": "Graduate School of Engineering, Saitama Institute of Technology, Fukaya City, Saitama, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>ng Cao", "Affiliation": "RIKEN Center for Advanced Intelligence Project (AIP), Chuo-ku, Tokyo, Japan"}], "References": []}, {"ArticleId": 115649249, "Title": "Reliability Analysis for Rotate Vector Reducer by Combining Polynomial Chaos Expansion and Saddlepoint Approximation Considering Multi-Failure Modes", "Abstract": "<p>RV (Rotate Vector) reducer is an essential mechanical transmission device extensively used in industrial machinery, robotics, aerospace and other fields. The dynamic transmission characteristics and strength of the cycloidal pin gear, turning arm bearing of RV reducer significantly affect the motion accuracy and reliability of the whole equipment. Uncertainties from manufacturing and assembly error, working loads add complexity to these effects. Developing effective methods for uncertainty propagation and reliability analysis for the RV reducer is crucial. In this work, the mail failure modes of RV reducer are studied, and an effective reliability analysis method for RV reducer considering the correlation between multi-failure modes by combining polynomial chaos expansions (PCE) and saddlepoint approximation method (SPA) is proposed. This paper develops an uncertainty propagation strategy for RV reducer based on dynamic simulation and PCE method with high accuracy. On this basis, a surrogated cumulant generating function (CGF) and SPA are combined to analyze the stochastic characteristic for the failure behavior. Based on the probability density function (PDF) and cumulative distribution function (CDF) calculated by SPA, copula function is employed to quantify the correlations between the multi-failure modes. Then, the system reliability with multi-failure modes is estimated by SPA and optimal copula function. The proposed method provides an effective reliability assessment technology with high-accuracy for complex system under unknown physical model and distribution characteristics. The validity of the proposed approach is illustrated RV-320E reducer reliability estimation, offering a basis to improve the performance of complex dynamic system. .</p>", "Keywords": "Approximation;Reliability;Stress;Aerospace industry;Dynamic systems;Gears;Errors;Polynomials;Complex systems;Density;Mechanical drives", "DOI": "10.1115/1.4065690", "PubYear": 2024, "Volume": "9", "Issue": "2", "JournalId": 31251, "JournalTitle": "Journal of Verification, Validation and Uncertainty Quantification", "ISSN": "2377-2158", "EISSN": "2377-2166", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kunming Shipborne Equipment Research & Test Center, Kunming, 650051, China; School of Naval Architecture and Ocean Engineering, Huazhong University of Science and Technology, Wuhan, 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kunming Shipborne Equipment Research & Test Center, Kunming, 650051, China"}, {"AuthorId": 3, "Name": "Pan Lu", "Affiliation": "Kunming Shipborne Equipment Research & Test Center, Kunming, 650051, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Naval Architecture and Ocean Engineering, Huazhong University of Science and Technology, Wuhan, 430074, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China, Chengdu, 611731, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, University of Electronic Science and Technology of China, Chengdu, 611731, China"}], "References": []}]