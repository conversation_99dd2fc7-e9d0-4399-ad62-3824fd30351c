[{"ArticleId": 83739385, "Title": "Sensitive and Specific Detection of SARS-CoV-2 Antibodies Using a High-Throughput, Fully Automated Liquid-Handling Robotic System", "Abstract": "<p>As of July 22, 2020, more than 14.7 million infections of SARS-CoV-2, the virus responsible for Coronavirus Disease 2019 (COVID-19), have been confirmed globally. Serological assays are essential for community screening, assessing infection prevalence, aiding identification of infected patients, and enacting appropriate treatment and quarantine protocols in the battle against this rapidly expanding pandemic. Antibody detection by agglutination–PCR (ADAP) is a pure solution phase immunoassay that generates a PCR amplifiable signal when patient antibodies agglutinate DNA-barcoded antigen probes into a dense immune complex. Here, we present an ultrasensitive and high-throughput automated liquid biopsy assay based on the Hamilton Microlab ADAP STAR automated liquid-handling platform, which was developed and validated for the qualitative detection of total antibodies against spike protein 1 (S1) of SARS-CoV-2 that uses as little as 4 µL of serum. To assess the clinical performance of the ADAP assay, 57 PCR-confirmed COVID-19 patients and 223 control patients were tested. The assay showed a sensitivity of 98% (56/57) and a specificity of 99.55% (222/223). Notably, the SARS-CoV-2–negative control patients included individuals with other common coronaviral infections, such as CoV-NL63 and CoV-HKU, which did not cross-react. In addition to high performance, the hands-free automated workstation enabled high-throughput sample processing to reduce screening workload while helping to minimize analyst contact with biohazardous samples. Therefore, the ADAP STAR liquid-handling workstation can be used as a valuable tool to address the COVID-19 global pandemic.</p>", "Keywords": "PCR;agglutination;antibody;anti–spike protein;liquid handling", "DOI": "10.1177/2472630320950663", "PubYear": 2020, "Volume": "25", "Issue": "6", "JournalId": 19198, "JournalTitle": "SLAS TECHNOLOGY: Translating Life Sciences Innovation", "ISSN": "2472-6303", "EISSN": "2472-6311", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Enable Biosciences, South San Francisco, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Enable Biosciences, South San Francisco, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Enable Biosciences, South San Francisco, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Enable Biosciences, South San Francisco, CA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Enable Biosciences, South San Francisco, CA, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Enable Biosciences, South San Francisco, CA, USA"}, {"AuthorId": 7, "Name": "Honglin Tian", "Affiliation": "Nevada State Public Health Laboratory, Reno, NV, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Nevada State Public Health Laboratory, Reno, NV, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Hamilton Company, Reno, NV, USA"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Enable Biosciences, South San Francisco, CA, USA"}], "References": []}, {"ArticleId": 83739389, "Title": "Erbium spectral-luminescent characteristics in bromide-fluoride photo-thermo-refractive glasses", "Abstract": "Subject of Research. The paper presents the study of spectral-luminescent characteristics of erbium ions in bromide-fluoride photo-thermo-refractive glasses with 0.1 mol% of erbium oxide and 1-2 mol% of ytterbium oxide concentration. Such spectroscopic parameters as stimulated emission cross-section of erbium ions at a wavelength of 1.5 microns, the efficiency of energy transfer from ytterbium to erbium ions, and the quantum efficiency are calculated based on the measured absorption spectra of glasses under study. The gain/loss spectra of erbium ions in the infra-red range are calculated for various population inversion values. Methods. Bromide-fluoride photo-thermo-refractive glasses were synthesized in an electric furnace at the temperature of 1480 °C for 5 hours in the air. Judd-Ofelt theory was used for determination of spectroscopic intensity parameters. The amplification spectra were obtained using McCumber theory. Main Results. It was found that the stimulated emission cross-section of erbium ions at a wavelength of 1.5 microns is typical for erbium in silicate glasses. The quantum efficiency of radiation at the same wavelength reached 95 %, and the energy transfer efficiency was 86 %. The gain factor had a positive value when the population inversion parameter was higher than 0.5. Practical Relevance. Bromide-fluoride photo-thermo-refractive glasses activated with erbium and ytterbium ions have spectral-luminescent characteristics comparable with commercial laser silicate glasses. In addition, they allow for recording of highly efficient Bragg gratings. Thus, these glasses can be promising candidates for creating distributed feedback lasers with ultra-narrow spectral lines. © 2020, ITMO University. All rights reserved.", "Keywords": "bromide-ﬂuoride photo-thermo-refractive glass, ytterbium, erbium, distributed-feedback laser", "DOI": "10.17586/**************-20-4-520-524", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739390, "Title": "Modern approaches to multiclass intent classification based on pre-trained transformers", "Abstract": "Subject of Research. The paper considers modern approaches to the multiclass intention classification problem. The user intention is the incoming user requests when interacting with voice assistants and chatbots. The algorithm is meant for determination what class the call belongs to. Modern technologies such as transfer learning and transformers can improve significantly the multiclass classification results. Method. This study uses a comparative model analysis technique. In turn, each model is inlined into a common pipeline for data preparing and clearing, and the model training but with regard to its specific requirements. The following models applied in real projects have been selected for comparison: Logistic Regression + TF-IDF, Logistic Regression + FastText, LSTM + FastText, Conv1D + FastText, BERT, and XLM. The sequence of models corresponds to their historical origin, but in practice these models are used without regard to the time period of their creation but depending on the effectiveness of the problem being solved. Main Results. The effectiveness of the multiclass classification models on real data is studied. Comparison results of modern practical approaches are described. In particular, XLM confirms the superiority of transformers over other approaches. An assumption is made considering the reason why the transformers show such a gap. The advantages and disadvantages of modern approaches are described. Practical Relevance. From a practical point of view, the results of this study can be used for projects that require automatic classification of intentions, as part of a complex system (voice assistant, chatbot or other system), as well as an independent system. The pipeline designed during the study can be applied for comparison and selection of the most effective model for specific data sets, both in scientific research and production. Keywords. © 2020, ITMO University. All rights reserved.", "Keywords": "natural language processing, text classiﬁcation, transfer learning, transformers", "DOI": "10.17586/**************-20-4-532-538", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tomsk Polytechnic University, Tomsk, 634050, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tomsk Polytechnic University, Tomsk, 634050, Russian Federation"}], "References": []}, {"ArticleId": 83739399, "Title": "Methods of hot wire creation for fiber-optical thermal anemometer", "Abstract": "Subject of Research. The paper presents the study of various methods of optical radiation coupling from the fiber core into its cladding with the aim of creating a fiber-optic anemometer hot wire. The effectiveness of the described methods is evaluated by analyzing the obtained dependencies. Method. The structures of two optical fibers were studied, one of which was used to transmit an information signal, and the other — to create a hot wire. A Bragg grating was formed in the core of the first fiber, and a radiation coupling region was created in the second fiber. These fiber sections were aligned with each other and fixed by a tin-lead alloy. The temperature was measured by monitoring the spectral characteristics of fiber Bragg gratings inscribed by the phase mask method. The areas of radiation coupling were formed either by changing the fiber geometry using custom modes of the arc fusion splicer, or by creating a SMF-MMF-SMF transition. Main Results. Structures are developed which can be used as a hot wire of a fiber-optic anemometer sensitive element. The dependencies of the Bragg resonance wavelength shift on the pump laser power are obtained; the Bragg wavelength shift varies in the range from 0.15 nm to 3 nm as a result of a hot wire creation. The resulting curves are constructed after three experiments for each type of structure, taking into account the standard deviation. They make it possible to judge the effectiveness of various methods of radiation coupling. Comparative analysis of the studied methods is presented. Practical Relevance. The study describes the capabilities of fiber-optic anemometry when creating point and distributed sensors. The results of this work can be used in the areas where the creation of quasi-distributed and point diffusers is necessary, as well as partial or complete coupling of radiation from a fiber core. © 2020, ITMO University. All rights reserved.", "Keywords": "anemometer, hot wire method, thermal anemometry, ﬁber Bragg grating, ﬁber optic sensors", "DOI": "10.17586/**************-20-4-500-506", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "S.V. Varzhel", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739462, "Title": "An investigation towards speaker identification using a single-sound-frame", "Abstract": "<p>Traditional neural network-based speaker identification (SI) studies employ a combination of acoustic features extracted from sequential sounds to present the speakers’ voice biometrics in which several sound segments before and after the current segment are stacked and fed to the network. Although this method is particularly important for speech recognition tasks where words are constructed from sequential sound segments, and successful recognition of words depends on the previous phonetic sequences, SI systems should be able to operate based on the distinctive speaker features available in an individual sound segment and identify the speaker regardless of the previously uttered sounds. This paper investigates this hypothesis by proposing a novel text-independent SI model trained at sound level. In order to achieve this, the investigation was conducted by first studying the best distinguishable configuration of coefficients in a single acoustic segment, then to identify the best frame length to overlapping ratio, and finally measuring the reliability of conducting SI using only a single sound segment. Overall more than one hundred SI systems were trained and evaluated, in which results indicate that performing SI using a single acoustic sound frame decreases the complexity of SI and facilitates it since the classifier requires to learn fewer number of acoustic features in compare to the traditional stacked-based approaches.</p>", "Keywords": "Automatic speaker identification; Feature extraction; MFCC; Deep neural networks", "DOI": "10.1007/s11042-020-09580-4", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical, Computer, and Software Engineering, Faculty of Engineering, University of Auckland, Auckland, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Digital Technologies, Manukau Institute of Technology, Auckland, New Zealand"}], "References": []}, {"ArticleId": 83739484, "Title": "Effective implementation of modern McEliece cryptosystem on generalized (L,G)-codes", "Abstract": "Subject of Research. The paper presents the study of methods and approaches to implementation of the modern McEliece cryptosystem based on separable generalized (L, G)-codes. Method. A method is proposed based on the analysis of the well-known public sources on implementation of the modern McEliece cryptosystem that uses the generalized (L, G)-codes with locators of degree greater than or equal to the second one without using an extended field. Mаin Results. Approaches to implementation of the modern McEliece cryptosystem based on the generalized (L, G)-codes are developed, namely: creation of a parity check matrix for the generalized (L, G)-code using a separable Goppa polynomial and locators of various degrees, description of an approach to the implementation of encryption and decryption of messages in the modern McEliece cryptosystem, presentation of the <PERSON><PERSON>’s procedure for numerators of degree greater than or equal to the second one without expanding the field. Practical Relevance. The proposed methods can be used in the development of cryptographic systems that can withstand attacks from quantum computers and ensure data confidentiality, as well as improve the security and performance of cryptosystems. Aerospace, automobile, railway, network multimedia, telecommunication and mobile information protection systems can also be the scope of the work results. © 2020, ITMO University. All rights reserved.", "Keywords": "Goppa codes, generalized (L, G)-codes, decoding algorithms, modern McEliece cryptosystem, separable polynomial", "DOI": "10.17586/**************-20-4-539-544", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation; Saint Petersburg State University of Aerospace Instrumentation (SUAI), Saint Petersburg, 190000, Russian Federation"}], "References": []}, {"ArticleId": 83739513, "Title": "Blockchain technology in 5G networks", "Abstract": "The rapid development of mobile networks and the growth of various types of devices connected to them has called forth the integrity and confidentiality ensuring of the transmitted data that now is a high-priority problem. The emergence of cryptocurrency was followed by renewal of interest in blockchain technology and the possibility of its use in various fields. The paper discusses currently existing methods of blockchain technology application in 5G networks in order to solve security problems, network connectivity and productivity improvement, as well as to develop new directions that expand the capabilities of services and applications in fifth-generation networks. The paper presents analysis of modern research works on the blockchain technology integration and key technologies used in the fifth-generation mobile networks. Particular attention is paid to applications of blockchain technology in cloud computing, edge computing, software-defined networking, virtualization of network functions, 5G-slicing and device-to-device communication. Based on the materials presented in the review, the options are outlined that blockchain technology can provide to 5G mobile networks and services through the use of a decentralized architecture and a smart contract algorithm. The materials of the proposed review is focused on the three main aspects: enhancement of transmitted information security, system performance and resources management. The paper can be useful to specialists and researchers working in the field of information security of the fifth-generation mobile networks, as well as for experts in the field of blockchain technology as an up-to-date overview of various applications of blockchain technology in the fifth-generation networks. © 2020, ITMO University. All rights reserved.", "Keywords": "blockchain, mobile network, 5G, security, decentralization, cloud computing", "DOI": "10.17586/**************-20-4-472-484", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation; Saint Petersburg State University of Aerospace Instrumentation (SUAI), Saint Petersburg, 190000, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739515, "Title": "Absorption characteristics of silver ion-exchanged layers in chloride photo-thermo-refractive glass", "Abstract": "Subject of Research. The paper considers effect of chloride in composition of photo-thermo-refractive glass on the spectral properties of silver nanoparticles formed in ion-exchanged layers after heat treatment. Method. Glasses based on Na2O–ZnO–Al2O3–SiO2–F doped with antimony oxide Sb2O3, cerium oxide CeО2 and a variable chloride content (0–1 mol%) were synthesized for the study. Silver ions were introduced by the low-temperature Na+ –Ag+ ion exchange method into the synthesized glasses. For this purpose, glass samples were immersed in a mixture of 0.1AgNO3 /99.9NaNO3 (mol%) nitrates at the temperature of 320 °С for 15 minutes. After the ion exchange glasses were irradiated with ultraviolet radiation and heat-treated at the temperature of 500 °C for 3 hours to achieve the growth of silver nanoparticles. Main Results. Spectrum properties of chloride photo-thermo-refractive glasses with silver nanoparticles in ion-exchanged layers are studied. It is found that the presence of chloride in the photo-thermo-refractive glass matrix results in a long-wavelength shift of the absorption band of silver nanoparticles. That may be attributed to the growth of the mixed AgCl/NaCl shell on silver nanoparticles. The formation of silver nanoparticles in ion-exchanged layers occurs both in the irradiated and unirradiated regions of the glass. Practical Relevance. The results can be used to create Bragg gratings inside photo-thermo-refractive glass for input and output radiation (pump and signal) into the waveguide structures formed by the ion exchange method, and to create monolithic integrated optical elements on a single substrate, that is very essential for integrated optics. © 2020, ITMO University. All rights reserved.", "Keywords": "low-temperature ion exchange, photo-thermo-refractive glass, silver nanoparticles, chloride", "DOI": "10.17586/**************-20-4-515-519", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739535, "Title": "Normalization of Kazakh language words", "Abstract": "Subject of Research. Models and existing algorithms for normalization of natural language words are considered. The paper describes algorithms for automatic selection of the basic principles for a number of natural languages and possible ways of the normal word form synthesis for the Kazakh language. The research is aimed at creation of a complete classification for the Kazakh language ending system and development of a normalization algorithm for words based on the proposed classification approach for endings and suffixes. Method. Word formation analysis by applying endings for all Kazakh language parts of speech was carried out; a classification of endings and suffixes was presented. The paper discusses all kinds of placement options for endings and suffixes. The total number of various suffixes is 26 526 units and the endings is 3 565 units. All considered types are lexically and semantically valid, but some of them are not applicable. Only those, that are most commonly used, are added to the affix base. The order, that the affixes are added to the base, is presented using sets. Thus, the base is correctly selected. The study does not examine word-forming suffixes, as they change the word stem and contextual interpretation. Basically, word-forming suffixes are added to nouns. Main Results. A complete classification system for endings and suffixes of the Kazakh language has been developed. Deterministic finite automata for various parts of speech are created using all possible options, adding suffixes and endings, taking into account the morphological and lexical features of the Kazakh language grammar. A lexicon-free stemming algorithm is developed using the proposed classification system for endings of the Kazakh language. A normalization system has been implemented, proving the operability of the developed algorithm without a dictionary. The algorithm implementation was tested on the Kazakh language corpus. Punctuation and stop words were initially removed from the specified corpus. Practical Relevance. The results of the work can find application in the text analysis and normalization (lemmatization), as well as in information retrieval systems, in machine translation from the Kazakh language, and other applied problems. © 2020, ITMO University. All rights reserved.", "Keywords": "natural language processing, Kazakh, ending system, normalization, stemming algorithm", "DOI": "10.17586/**************-20-4-545-551", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Al-Farabi Kazakh National University, Almaty, 050040, Kazakhstan; Institute of Information and Computing Technologies, Almaty, 050000, Kazakhstan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Al-Farabi Kazakh National University, Almaty, 050040, Kazakhstan"}], "References": []}, {"ArticleId": 83739545, "Title": "Model of automated synthesis tool for hardware accelerators of convolutional neural networks for programmable logic devices", "Abstract": "Currently, more and more tasks on image processing and analysis are being solved using convolutional neural networks. Neural networks implemented using high-level programming languages, libraries and frameworks cannot be used in real-time systems, for example, for processing streaming video in cars, due to the low speed and energy efficiency of such implementations. The application of specialized hardware accelerators of neural networks is necessary for these tasks. The design of such accelerators is a complex iterative process requiring highly specialized knowledge and qualification. This consideration makes the creation of automation tools for high-level synthesis of such computers a relevant issue. The purpose of this research is a tool development for the automated synthesis of neural network accelerators from a high-level specification for programmable logic devices (FPGAs), which reduces the development time. A description of networks is used as a high-level specification, which can be obtained using the TensorFlow framework. The several strategies have been researched for optimizing the structure of convolutional networks, methods for organizing the computational process and formats for representing data in neural networks and their effect on the characteristics of the resulting computer. It was shown that structure optimization of neural network fully connected layers on the example of solving the handwritten digit recognition problem from the MNIST set reduces the number of network parameters by 95 % with a loss of accuracy equal to 0.43 %, pipelining of calculations speeds up the calculation by 1.7 times, and parallelization of the computing process individual parts provides the acceleration by almost 20 times, although it requires 4-6 times more FPGA resources. Applying of fixed-point numbers instead of floating-point numbers in calculations reduces the used FPGA resources by 1.7–2.8 times. The analysis of the obtained results is carried out and a model of an automated synthesis tool is proposed, which performs the indicated optimizations in automatic mode in order to meet the requirements for speed and resources used in the implementation of neural network accelerators on FPGA. © 2020, ITMO University. All rights reserved.", "Keywords": "convolutional neural networks, neural network accelerators, hardware accelerators, FPGA, CAD, high-level synthesis", "DOI": "10.17586/**************-20-4-560-567", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "OOO Siemens, Saint Petersburg, 191186, Russian Federation"}, {"AuthorId": 2, "Name": "S.<PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739546, "Title": "Parametric identification of differencial-difference models of heat transfer in one-dimensional bodies based on <PERSON><PERSON> filter algorithms", "Abstract": "The paper considers the solution of the inverse heat conduction problem by parametric identification of differential-difference models of heat transfer in one-dimensional bodies. Differential-difference models is a system of differential ordinary equations of the first order with respect to the state vector. In this case, the direct and inverse heat conduction problems are solved, and the <PERSON><PERSON> recurrent digital filter algorithm is used in terms of parameters for discrepancy minimization between the measured and model parameters values. The paper considers the <PERSON>lman algorithm application for two specific problems, namely: an experiment estimation and planning for restoration of heat transfer boundary conditions for a system of bodies. When planning an experiment or during field studies, the task parametrization is carried out initially and then parametric identification, as well. To determine the confidence area for measuring the desired parameters, the Gram matrix (Fisher information matrix) is used, involving the components which are sensitivity functions that represent all significant factors of heat metering: the type of heat transfer in the system, the number and location of temperature measurement points, the quality of channels for recording measured values, and the features of input actions, time measurement section and the number of measurement time points in this section. The paper gives an example of Kalman recurrent digital filter, considers the battery transformer of unsteady heat flux, for which the creation of the differential-difference model is carried out, shows the results of unsteady heat flux restoration, changing according to an arbitrary law. Confidence areas of the desired parameters are established. © 2020, ITMO University. All rights reserved.", "Keywords": "inverse heat conduction problem, parametric identiﬁcation, <PERSON><PERSON> ﬁlter", "DOI": "10.17586/**************-20-4-584-588", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Larionov Institute of the North Physical-Technical Problems of the RAS Siberian Branch, Yakutsk, 677980, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739547, "Title": "Mathematical model of liquefied natural gas evaporation and analysis of original composition effect on evaporation speed", "Abstract": "Subject of Research. The paper proposes a model for predicting evaporation of liquefied natural gas (LNG) stored in tanks of regasification terminals. The model is a combination of the strict thermodynamic LNG vapor-liquid equilibrium and heat transfer realistic models. Analysis of the initial composition effect and the stock of LNG, the initial content of nitrogen and the value of the outside air temperature on the LNG evaporation rate is carried out. Method. A numerical experiment using a mathematical model of the LNG evaporation process was based on the LNG composition data and its storage time in aboveground tanks. The proposed method provided a number of advantages compared to previously developed models: the heat input to the low-temperature part of the LNG storage tank was calculated taking into account the outdoor temperature and the LNG composition; the evaporation coefficient was not an input parameter, but was calculated as part of the simulation; the LNG density was calculated by experimental correlation. Main Results. An important parameter in assessing the LNG loss from its evaporation is the nitrogen content in the liquid, which evaporates at a higher rate than methane, the main LNG component. Analysis of the initial composition effect on the LNG evaporation rate has shown that the growth of the nitrogen content in the initial mixture causes noticeable decrease in the evaporation rate due to the nitrogen priority evaporation and, as a consequence, the growth of latent mixture heat vaporization. Analysis of the initial LNG supply value at the evaporation rate has shown that a large degree of the tank filling causes an outstripping reduction in losses from evaporation. The result is an earlier reduction in the amount of boil-off gas (BOG, stripping gas) for more filled tank. Analysis of the outdoor temperature effect on the rate of LNG evaporation has shown that the ambient temperature change of 1 °C leads to the decrease in the BOG amount by 0.2 %. Practical Relevance. The proposed mathematical model of the LNG evaporation process reduces BOG formation during long-term storage, maintain the original LNG quality, optimizes the operational parameters of regasification terminals, and provides the consumers with reliable supply of high-quality natural gas. © 2020, ITMO University. All rights reserved.", "Keywords": "liqueﬁed natural gas, evaporation, regasiﬁcation, storage, tanks, model, forecasting, evaluation, analysis, composition", "DOI": "10.17586/**************-20-4-603-610", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation; Research and Production Company “KRION”, Ltd, Saint Petersburg, 197375, Russian Federation"}], "References": []}, {"ArticleId": 83739551, "Title": "Accuracy increase of software and hardware appliance for muscle activity measuring and monitoring by filtration of carrier component and frequencies higher than measured signal range", "Abstract": "Subject of Research. The paper proposes a method of muscle activity filtering measurements for a mobile hardware-software appliance used in surface electromyography. The method extends the dynamic range of measurements by capacity growth of analog-to-digital converter aimed at the increase of recognition accuracy and range of muscle activity. Method. A filter model for signals from sensors for a muscle activity controller was developed in the Proteus Design Suite software package. The filter of the signal carrier component based on the RC high-pass filter provides isolation of the measuring unit from the reference voltage of the sensor. An active low-pass filter amplifies the signal from the sensor and filters out the noise higher than the frequency range of muscle activity signals. Main Results. Filtering of the signal carrier component and increasing the order of low-pass filter show positive results in simulation. The paper presents amplitude-frequency characteristics plots and model structures with and without RC filter, with an active low-pass filter of the first order and an active low-pass filter of the second order. An amplifier unit electrical circuit for a muscle activity controller is developed based on the methodology for muscle activity measurement filtering. The results obtained are applicable for improvement of the prototype for the mobile hardware-software appliance used in surface electromyography. Practical Relevance. The developed complex can be applied in a system for muscle activity measuring and monitoring as the rehabilitation process maintenance during the movement of patients with injuries and disorders of the musculoskeletal system. This complex can be used in various neurophysiological studies where the monitoring of muscle activity dynamics in the process of the examined subject movement is required. © 2020, ITMO University. All rights reserved.", "Keywords": "surface electromyography, surface EMG, muscle activity measurement, EMG signal high-pass ﬁlter, EMG signal active low-pass ﬁlter, neurocomputer interface", "DOI": "10.17586/**************-20-4-617-624", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "A.S. Kyzdarbekova", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "S.S. Reznikov", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739561, "Title": "Modular approach application in development of computer numerical control software", "Abstract": "Subject of Research. The paper proposes an approach to the development of a numerical control kernel. The approach implies creating software from separate modules interacting via a unified programming interface with a high level of granularity. Thus, a system with a required configuration can be developed in a relatively short time. The study also considers the possibility of using open source computer numerical control systems as a basis, which will further reduce the design time. The approach for computer numerical control development is considered in the context of its application on multipurpose modular equipment. Method. The proposed solution is based on a multi-protocol control system and combines software and hardware components from different manufacturers. Platform independence is also provided. This method allows a prompt development of the numerical control system for any type of processing or other operations according to the requirements of hardware, and also gives wide opportunities for further modifications that increase the equipment efficiency. Main Results. The practical result obtained is a software trajectory-planning library, including geometry analysis, feed rate control and interpolation. Commands for controlling outputs and status of inputs are integrated into the cyclic data of the drive control and transmitted via the same interface. All developed modules are independently designed and can be embedded into other open source systems, as well as be further modified for processing efficiency increase. Practical Relevance. The work is aimed at increasing the economic independence of small design organizations and enterprises. The proposed modular approach allows the development of a required numerical control system for multipurpose modular equipment in a short time, and will significantly expand the capabilities of rapid prototyping and ensure the prompt production of pilot batches. © 2020, ITMO University. All rights reserved.", "Keywords": "computer numerical control, modular approach, modular equipment, interpolation, feed rate control, trajectory planning, computer numerical control system", "DOI": "10.17586/**************-20-4-576-583", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739562, "Title": "Modeling of liquefied natural gas evaporation in mobile reservoirs", "Abstract": "The paper presents the analysis of isothermal storage of liquefied natural gas within the conditions of river navigation. The effect of constructive and technological solutions on the safety and profitability of liquefied natural gas application as an alternative to petroleum products is studied. The technique principle consists in a cryogenic storage modeling based on the energy equation of the vapor and liquid fractions and comparative analysis of several options for transportation of liquefied natural gas by river transport. In consequence of the study results, data were obtained on the relationship between the insulation thickness and the degree of tank filling having an effect on transport characteristics. It was revealed that transportation of liquefied natural gas in gas-insulated storage facilities is technically possible and satisfactory by combining the optimal specific volume of cryogenic liquid in the tank and sufficient thickness of thermal insulation. The results obtained can be used in the development of autonomous floating containers with a large insulation thickness, which will be towed along rivers without the use of carrier vessels. © 2020, ITMO University. All rights reserved.", "Keywords": "liqueﬁed natural gas, cryogenic storage, thermal insulation, steam space, heat inﬂux", "DOI": "10.17586/**************-20-4-595-602", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NPP “KRION”, Saint Petersburg, 197375, Russian Federation; ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation; St. Petersburg Branch of Gazprom Design, Saint Petersburg, 191036, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739566, "Title": "Packet reservations in real-time multipath transmissions", "Abstract": "Subject of Research. The paper presents research of mechanisms of multipath redundant transmissions in computer networks. Method. The developed application level protocol gives the possibility to increase the probability of timely and faultless data delivery in computer networks which work within the conditions of delivery time limits using multipath redundant transmissions of packet copies via different channels. Main Results. The protocol with redundant multipath transmissions opportunity based on UDP transport protocol is developed. The efficiency analysis of the presented solutions is carried out on the basis of simulation modeling in OMNeT++ environment. Models of the proposed protocol and computer network with redundant transmissions opportunity are developed. Experiments with these models are carried out and efficiency area of the developed protocol application is determined. The multiplicative criterion that shows time reserve of timely and faultless transmitted packets is used as an efficiency indicator. The presented protocol shows efficiency in various working cases with the intensity and redundancy coefficient change of packet arrival. Practical Relevance. The developed algorithms and mechanisms can be used for program implementation of protocol with redundant multipath transmissions aimed at its application in cyberphysic real-time systems. © 2020, ITMO University. All rights reserved.", "Keywords": "network protocols, multipath redundant transmissions, UDP, delivery time limits, delivery probability, real-time systems", "DOI": "10.17586/**************-20-4-568-575", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739567, "Title": "Selection of composite material in electromagnetic log sensor", "Abstract": "Subject of Research. The paper considers the issues of choosing an insulating material for a deep-sea electromagnetic log sensor. The effect of the insulating material characteristics on the operational factors of the sensor is studied. Method. Matlab program was used to analyze the effect of design parameters on the instrumental error of the sensor. For this purpose a mathematical model of the instrumental error component was built, and its evaluation was carried out. The stress-strain state of the sensor was simulated by the finite element method in the ANSYS Workbench application package. Analysis of insulating material peeling from the case during shrinkage was carried out in the Static Structural module. Fill simulation was performed in the Fluid Flow module. All materials are taken to be isotropic at calculations. Main Results. The effect of the insulating material characteristics on the instrumental error of the electromagnetic log sensor is shown. The design and technological issues of the insulating material choice are described. Recommendations on the choice of design parameters are given to reduce peeling of the material and the occurrence of sensors emptiness formed by filling with composite material. The validity of the recommendations is confirmed by computer simulation and experiments. Practical Relevance. The results obtained can be applied in the modernization, design and construction of new electromagnetic log sensors. © 2020, ITMO University. All rights reserved.", "Keywords": "electromagnetic log sensor, composite material, instrumental error, computer simulation", "DOI": "10.17586/**************-20-4-589-594", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Concern CSRI “Elektropribor”, JSC, Saint Petersburg, 197046, Russian Federation; ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Concern CSRI “Elektropribor”, JSC, Saint Petersburg, 197046, Russian Federation; ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739568, "Title": "Meshless modeling of elastic deformations of polymeric composite materials under static loading", "Abstract": "Subject of Research. Composites are unique materials combining lightness and strength, which makes them popular in the materials industry. Mathematical modeling of these materials is necessary to predict their behavior under specific loads. The paper proposes a meshless method for mathematical modeling of anisotropic composite materials based on their representation as a set of meta-particles with spring-like bonds. Special feature of the presented work is the applicability of the same equations for the multi-scale modeling: on micro-scale (single fiber and an elementary volume of the compound) and macro-scale (cross-section of the whole composite part). Method. The method is based on Verlet-like time integration of particle displacements with consequent resolving of a system of elastic bonds with pre-calculated mass and stiffness. The method estimates temporal dynamics of composite deformations under applied mechanical load. The code for solution of motion equations and result visualization is written in pure JavaScript without any dependencies. Main Results. Elastic deformations of a simplified 2D model of carbon-fiber-reinforced plastic have been simulated (with a mixture of epoxy and polyester resins as a compound) under static mechanical load using the proposed method. Calculations on the micro-and macro-scales with different directions of fiber layering: at the angles of 0° and 90°. Flat layering type is selected. The results are verified via the ANSYS Composite PrepPost solver under equivalent conditions. The coincidence of the calculation results by the meta-particle method of the developed solver and the finite element method is 89 %. Practical Relevance. The results obtained can be used in the development of new types of composite materials at the modeling and forecasting stage, and can also allow simulations of composites taking into account micro-processes for exclusion of pore formation and other defects that cannot be resolved using macro-scale modeling. © 2020, ITMO University. All rights reserved.", "Keywords": "meshless methods, composite materials, structural dynamics, strength, mathematical modeling", "DOI": "10.17586/**************-20-4-611-616", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kazan National Research Technical University named after <PERSON><PERSON><PERSON><PERSON>AI, Kazan, 420111, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kazan National Research Technical University named after <PERSON><PERSON><PERSON><PERSON>AI, Kazan, 420111, Russian Federation"}], "References": []}, {"ArticleId": 83739578, "Title": "Laser heating numerical simulation of titanium-containing optothermal fiber converter and vein wall during endovasal laser coagulation", "Abstract": "Subject of Research. The paper presents numerical methods that first studied laser heating of titanium-containing optothermal fiber converter and vein wall, as well as the effect of thermal damage of the vein wall during endovasal laser coagulation with different average power of 980 nm semiconductor laser and the traction speed of the converter inside the vein. Method. Models and conditions for numerical simulation of optical and thermal physical processes were formulated, occurring during endovasal laser coagulation of veins using titanium-containing optothermal fiber converter. The Monte Carlo method was used in optical modeling. The initial-edge task for a non-linear model of radiation-conductive heat transport with moving sources of radiation was analyzed at thermal simulation by the method of finite dispositions. The effect of vein wall thermal damage was assessed as a result of solving the Arrhenius equation. Main Results. The titanium-containing optothermal fiber converter can be used for endovasal laser coagulation of veins by radiation of 980 nm laser with average power up to 20 W. Laser radiation is almost completely absorbed by the converter. With the simultaneous start of laser radiation and the beginning of traction, the temperature of the vein wall does not immediately reach the maximum value; the waiting time can reach units and even tens of seconds. The temperature inside converter exceeds 250 °C. The optimal combinations of average laser power and titanium-containing converter traction speed are defined for uniform coagulation of the vein wall. Practical Relevance. The results can be used in the development of laser methods and devices for endovasal laser coagulation of veins. © 2020, ITMO University. All rights reserved.", "Keywords": "laser, converter, vein wall, heating, absorption, endovasal laser coagulation, radiation, numerical simulation", "DOI": "10.17586/**************-20-4-485-493", "PubYear": 2020, "Volume": "20", "Issue": "4", "JournalId": 33685, "JournalTitle": "Scientific and Technical Journal of Information Technologies, Mechanics and Optics", "ISSN": "2226-1494", "EISSN": "2500-0373", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation; Pavlov University, Saint Petersburg, 197022, Russian Federation"}, {"AuthorId": 2, "Name": "Thanh Tung Do", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITMO University, Saint Petersburg, 197101, Russian Federation"}], "References": []}, {"ArticleId": 83739785, "Title": "Optimal Portfolio Choice Under Shadow Costs with Fixed Assets when Time-Horizon Is Uncertain", "Abstract": "<p>We analyze in this paper the problem of choosing the optimal portfolio for investors under uncertain exit random time. We consider the portfolio choice with fixed assets in the presence of information costs and short sales constraints. This context allows us to focus on the optimal portfolio choice with fixed assets. Investors aim to maximize the ratio between the wealth and the value of the fixed assets. We obtain the optimal portfolio choice strategy with fixed assets when the time horizon is a random exit time. Our results are new in the literature. We illustrate the main findings through some simulation results.</p>", "Keywords": "Optimal portfolio; Asset price; Uncertain time-horizon; Dynamic programming; HJB equation; G11; G12", "DOI": "10.1007/s10614-020-09991-3", "PubYear": 2020, "Volume": "56", "Issue": "1", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cy Cergy Paris University, Cergy, France;ISC Paris Business School, Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics, Shandong University, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}], "References": []}, {"ArticleId": 83739798, "Title": "A systematic literature review on hardware implementation of artificial intelligence algorithms", "Abstract": "<p>Artificial intelligence (AI) and machine learning (ML) tools play a significant role in the recent evolution of smart systems. AI solutions are pushing towards a significant shift in many fields such as healthcare, autonomous airplanes and vehicles, security, marketing customer profiling and other diverse areas. One of the main challenges hindering the AI potential is the demand for high-performance computation resources. Recently, hardware accelerators are developed in order to provide the needed computational power for the AI and ML tools. In the literature, hardware accelerators are built using FPGAs, GPUs and ASICs to accelerate computationally intensive tasks. These accelerators provide high-performance hardware while preserving the required accuracy. In this work, we present a systematic literature review that focuses on exploring the available hardware accelerators for the AI and ML tools. More than 169 different research papers published between the years 2009 and 2019 are studied and analysed.</p>", "Keywords": "Hardware accelerators; Artificial intelligence; Machine learning; AI on hardware; Real-time AI", "DOI": "10.1007/s11227-020-03325-8", "PubYear": 2021, "Volume": "77", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sharjah, Sharjah, United Arab Emirates"}], "References": [{"Title": "A survey of FPGA-based accelerators for convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>l", "PubYear": 2020, "Volume": "32", "Issue": "4", "Page": "1109", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 83739822, "Title": "System‐of‐systems tools and techniques for the analysis of cyber‐physical systems", "Abstract": "Dynamic and real‐time adaptive configuration of Cyber‐Physical Systems (CPSs) results in increased complexity due to a variety of heterogeneous and interdependent variables and creates unique challenges. For example, (a) Emergent Behavior: How do we ensure that system constituents dynamically and adaptively collaborate to produce a consistent repeatable functionality while supporting the capability to upgrade the individual entities through technology infusion; (b) Scale: How do we ensure scalability of these systems by managing complexity; and (c) Risk Management: How do we evaluate and manage the risks associated with the connection and interdependencies of heterogenous systems. Design and development of this new generation of CPSs can be viewed through the lens of System‐of‐Systems (SoS) methodology which is designed to analyze and assess the evolving topologies created by interactions within a large complex system operating in dynamic and uncertain environment. In this paper, we propose the use of several SoS tools and techniques for the analysis and design of next‐generation CPSs. Our SoS methodologies address features such as diversity of component systems, complex hierarchical structures, dynamic and emergent behavior, and interactions between components. Therefore, they are suitable to treat some of the challenging features of CPSs. However, it is necessary to modify these methodologies to address specific aspects of CPSs. Constraints and metrics from SoS methodology, applied to the design space, will support decision on component systems and the topology of their connections, and provide a set of “good designs,” with desired characteristics.", "Keywords": "", "DOI": "10.1002/sys.21539", "PubYear": 2020, "Volume": "23", "Issue": "4", "JournalId": 2331, "JournalTitle": "Systems Engineering", "ISSN": "1098-1241", "EISSN": "1520-6858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Purdue University, West Lafayette, Indiana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Purdue University, West Lafayette, Indiana"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan, Hubei, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Purdue University, West Lafayette, Indiana"}], "References": []}, {"ArticleId": 83739993, "Title": "Automatic weighted matching rectifying rule discovery for data repairing", "Abstract": "<p>Data repairing is a key problem in data cleaning which aims to uncover and rectify data errors. Traditional methods depend on data dependencies to check the existence of errors in data, but they fail to rectify the errors. To overcome this limitation, recent methods define repairing rules on which they depend to detect and fix errors. However, all existing data repairing rules are provided by experts which is an expensive task in time and effort. Besides, rule-based data repairing methods need an external verified data source or user verifications; otherwise, they are incomplete where they can repair only a small number of errors. In this paper, we define weighted matching rectifying rules (WMRRs) based on similarity matching to capture more errors. We propose a novel algorithm to discover WMRRs automatically from dirty data in-hand. We also develop an automatic algorithm for rules inconsistency resolution. Additionally, based on WMRRs, we propose an automatic data repairing algorithm (WMRR-DR) which uncovers a large number of errors and rectifies them dependably. We experimentally verify our method on both real-life and synthetic data. The experimental results prove that our method can discover effective WMRRs from dirty data in-hand and perform dependable and full-automatic repairing based on the discovered WMRRs, with higher accuracy than the existing dependable methods. </p>", "Keywords": "Data quality; Data cleaning; Automatic rule discovery; Rules consistency; Automatic data repairing", "DOI": "10.1007/s00778-020-00617-6", "PubYear": 2020, "Volume": "29", "Issue": "6", "JournalId": 4083, "JournalTitle": "The VLDB Journal", "ISSN": "1066-8888", "EISSN": "0949-877X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Harbin Institute of Technology, Harbin, China;Department of Software Engineering and Information Systems, Tishreen University, Latakia, Syria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Department of Computer Science, Harbin Institute of Technology, Harbin, China"}], "References": []}, {"ArticleId": 83740024, "Title": "Formalizing the LLL Basis Reduction Algorithm and the LLL Factorization Algorithm in Isabelle/HOL", "Abstract": "<p>The LLL basis reduction algorithm was the first polynomial-time algorithm to compute a reduced basis of a given lattice, and hence also a short vector in the lattice. It approximates an NP-hard problem where the approximation quality solely depends on the dimension of the lattice, but not the lattice itself. The algorithm has applications in number theory, computer algebra and cryptography. In this paper, we provide an implementation of the LLL algorithm. Both its soundness and its polynomial running-time have been verified using Isabelle/HOL. Our implementation is nearly as fast as an implementation in a commercial computer algebra system, and its efficiency can be further increased by connecting it with fast untrusted lattice reduction algorithms and certifying their output. We additionally integrate one application of LLL, namely a verified factorization algorithm for univariate integer polynomials which runs in polynomial time.</p><p>© The Author(s) 2020.</p>", "Keywords": "Certified algorithm;Complexity verification;Lattices;Polynomial factorization;Shortest vector problem;Verified LLL implementation", "DOI": "10.1007/s10817-020-09552-1", "PubYear": 2020, "Volume": "64", "Issue": "5", "JournalId": 24991, "JournalTitle": "Journal of Automated Reasoning", "ISSN": "0168-7433", "EISSN": "1573-0670", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Innsbruck, Innsbruck, Austria."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Innsbruck, Innsbruck, Austria."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of La Rioja, Logroño, Spain."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Innsbruck, Innsbruck, Austria."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "University of Innsbruck, Innsbruck, Austria."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Innsbruck, Innsbruck, Austria."}], "References": [{"Title": "A Verified Implementation of the Berlekamp–Zassenhaus Factorization Algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "4", "Page": "699", "JournalTitle": "Journal of Automated Reasoning"}, {"Title": "A Verified Implementation of Algebraic Numbers in Isabelle/HOL", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "3", "Page": "363", "JournalTitle": "Journal of Automated Reasoning"}]}, {"ArticleId": 83740133, "Title": "An Intelligent Fault Detection Model for Fault Detection in Photovoltaic Systems", "Abstract": "<p>Effective fault diagnosis in a PV system requires understanding the behavior of the current/voltage (I/V) parameters in different environmental conditions. Especially during the winter season, I/V characters of certain faulty states in a PV system closely resemble that of a normal state. Therefore, a normal fault detection model can falsely predict a well-operating PV system as a faulty state and vice versa. In this paper, an intelligent fault diagnosis model is proposed for the fault detection and classification in PV systems. For the experimental verification, various fault state and normal state datasets are collected during the winter season under wide environmental conditions. The collected datasets are normalized and preprocessed using several data-mining techniques and then fed into a probabilistic neural network (PNN). The PNN model will be trained with the historical data to predict and classify faults when new data is fetched in it. The trained model showed better performance in prediction accuracy when compared with other classification methods in machine learning.</p>", "Keywords": "", "DOI": "10.1155/2020/6960328", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of IT Applied System Engineering, Jeonbuk National University, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of IT Applied System Engineering, Jeonbuk National University, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of IT Applied System Engineering, Jeonbuk National University, Republic of Korea"}], "References": []}, {"ArticleId": 83740233, "Title": "Single Image Super-Resolution Reconstruction based on the ResNeXt Network", "Abstract": "<p>To solve the complex computation, unstable network and slow learning speed problems of a generative adversarial network for image super-resolution (SRGAN), we proposed a single image super-resolution reconstruction model called the Res_WGAN based on ResNeXt. The generator is constructed by the ResNeXt network, which reduced the computational complexity of the model generator to 1/8 that of the SRGAN. The discriminator was constructed by the Wasserstein GAN(WGAN), which solved the SRGAN’s instability. By removing the normalization operation in the residual network, the learning rate is improved. The experimental results from the Res_WGAN demonstrated that the proposed model achieved better performance in the subjective and objective evaluations using four public data sets compared with other state-of-the-art models.</p>", "Keywords": "Single image super-resolution reconstruction; ResNeXt; WGAN; Deep learning", "DOI": "10.1007/s11042-020-09053-8", "PubYear": 2020, "Volume": "79", "Issue": "45-46", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;College of Software, Xinjiang University, Urumqi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;College of Software, Xinjiang University, Urumqi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;College of Software, Xinjiang University, Urumqi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China"}], "References": [{"Title": "Remote sensing images super-resolution with deep convolution networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "13-14", "Page": "8985", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 83740302, "Title": "Maximum likelihood-based influence maximization in social networks", "Abstract": "<p>Influence Maximization (IM) is an important issue in network analyzing which widely occurs in social networks. The IM problem aims to detect the top- k influential seed nodes that can maximize the influence spread. Although a lot of studies have been performed, a novel algorithm with a better balance between time-consumption and guaranteed performance is still needed. In this work, we present a novel algorithm called MLIM for the IM problem, which adopts maximum likelihood-based scheme under the Independent Cascade(IC) model. We construct thumbnails of the social network and calculate the L -value for each vertex using the maximum likelihood criterion. A greedy algorithm is proposed to sequentially choose the seeds with the smallest L -value. Empirical results on real-world networks have proved that the proposed method can provide a wider influence spreading while obtaining lower time consumption.</p>", "Keywords": "Influence Maximization; Independent Cascade Model; Maximum Likelihood", "DOI": "10.1007/s10489-020-01747-8", "PubYear": 2020, "Volume": "50", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information Engineering of Yangzhou University, Yangzhou, China;The Laboratory for Internet of Things and Mobile Internet Technology of Jiangsu Province, Huaiyin Institute of Technology, Huaiyin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information Engineering of Yangzhou University, Yangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering of Yangzhou University, Yangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering of Yangzhou University, Yangzhou, China"}], "References": [{"Title": "IM‐SSO: Maximizing influence in social networks using social spider optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 83740330, "Title": "Multiple attribute group decision making based on 2-dimension linguistic intuitionistic fuzzy aggregation operators", "Abstract": "<p>The 2-dimension linguistic variables (2-DLVs) add a subjective evaluation on the reliability of the evaluation results provided by decision makers, so 2-DLVs are very useful tools for describing uncertain or fuzzy information. This work extends the idea of 2-DLVs by introducing 2-dimension linguistic intuitionistic fuzzy variables (2-DLIFVs) in which 1 class and 2 class information describe in the form of linguistic intuitionistic fuzzy numbers. The paper defines some operational laws, score, and accuracy functions for 2-DLIFVs. Further, we develop some arithmetic and geometric aggregation operators for aggregating 2-DLIF information and prove a number of valuable properties associated with them. Using the proposed aggregation operators, an approach for multiple attribute group decision making with 2-DLIF information is formulated. Finally, an illustrated example is given to verify and prove the validity of the developed method. The computed results are also compared with the existing results. </p>", "Keywords": "Multiple attribute decision making; Linguistic intuitionistic fuzzy numbers; Linguistic variables; 2-dimension linguistic variables; 2-dimension linguistic intuitionistic fuzzy variables", "DOI": "10.1007/s00500-020-05026-z", "PubYear": 2020, "Volume": "24", "Issue": "22", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management Control and Information Systems, University of Chile, Santiago, Chile;School of Information, Systems and Modelling, Faculty of Engineering and Information Technology, University of Technology Sydney, Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Management Control and Information Systems, University of Chile, Santiago, Chile;School of Information, Systems and Modelling, Faculty of Engineering and Information Technology, University of Technology Sydney, Sydney, Australia"}], "References": []}, {"ArticleId": 83740332, "Title": "Enzyme-powered Janus platelet cell robots for active and targeted drug delivery", "Abstract": "<p>Transforming natural cells into functional biocompatible robots capable of active movement is expected to enhance the functions of the cells and revolutionize the development of synthetic micromotors. However, present cell-based micromotor systems commonly require the propulsion capabilities of rigid motors, external fields, or harsh conditions, which may compromise biocompatibility and require complex actuation equipment. Here, we report on an endogenous enzyme-powered Janus platelet micromotor (JPL-motor) system prepared by immobilizing urease asymmetrically onto the surface of natural platelet cells. This Janus distribution of urease on platelet cells enables uneven decomposition of urea in biofluids to generate enhanced chemophoretic motion. The cell surface engineering with urease has negligible impact on the functional surface proteins of platelets, and hence, the resulting JPL-motors preserve the intrinsic biofunctionalities of platelets, including effective targeting of cancer cells and bacteria. The efficient propulsion of JPL-motors in the presence of the urea fuel greatly enhances their binding efficiency with these biological targets and improves their therapeutic efficacy when loaded with model anticancer or antibiotic drugs. Overall, asymmetric enzyme immobilization on the platelet surface leads to a biogenic microrobotic system capable of autonomous movement using biological fuel. The ability to impart self-propulsion onto biological cells, such as platelets, and to load these cellular robots with a variety of functional components holds considerable promise for developing multifunctional cell-based micromotors for a variety of biomedical applications.</p>", "Keywords": "", "DOI": "10.1126/scirobotics.aba6137", "PubYear": 2020, "Volume": "5", "Issue": "43", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA.;Research Center for Bioengineering and Sensing Technology, University of Science and Technology Beijing, Beijing 100083, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 10, "Name": "Zheng<PERSON> Li", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 12, "Name": "Haifeng Dong", "Affiliation": "Research Center for Bioengineering and Sensing Technology, University of Science and Technology Beijing, Beijing 100083, China."}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Center for Bioengineering and Sensing Technology, University of Science and Technology Beijing, Beijing 100083, China."}, {"AuthorId": 15, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": "Department of NanoEngineering and Chemical Engineering Program, University of California San Diego, La Jolla, CA 92093, USA."}], "References": []}, {"ArticleId": 83740359, "Title": "Question answering model based on machine reading comprehension with knowledge enhancement and answer verification", "Abstract": "<p>Deep learning has led to important breakthroughs in natural language processing and obtained the state-of-the-art results on machine reading comprehension. However, it is essential to consider the entity recognition and the detection of unanswerable questions for accuracy improvement. A novel question answering model is proposed with knowledge enhancement and answer verification to promote the performance of reading comprehension. With knowledge enhancement, the proposed model is able to recognize entities from the passage and detect word boundary precisely. To deal with unanswerable questions, the answerability of questions is evaluated based on the textual entailment. Empirical studies suggest that the proposed model has better ability of reading comprehension than others, with improvement on question answering tasks.</p>", "Keywords": "answer verification;automatic question answering;knowledge enhancement;machine Reading comprehension", "DOI": "10.1002/cpe.5828", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology Jinan University  Jinan China"}, {"AuthorId": 2, "Name": "Yuxia Sun", "Affiliation": "College of Information Science and Technology Jinan University  Jinan China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology Jinan University  Jinan China"}], "References": []}, {"ArticleId": 83740365, "Title": "UWFP-Outlier: an efficient frequent-pattern-based outlier detection method for uncertain weighted data streams", "Abstract": "<p>In this paper, we propose an efficient frequent-pattern-based outlier detection method, namely, UWFP-Outlier, for identifying the implicit outliers from uncertain weighted data streams. For reducing the time cost of the UWFP-Outlier method, in the weighted frequent pattern mining phase, we introduce the concepts of the maximal weight and maximal probability to form a compact anti-monotonic property, thereby reducing the scale of potential extensible patterns. For accurately detecting the outliers, in the outlier detection phase, we design two deviation indices to measure the deviation degree of each transaction in the uncertain weighted data streams by considering more factors that may influence its deviation degree; then, the transactions which have large deviation degrees are judged as outliers. The experimental results indicate that the proposed UWFP-Outlier method can accurately detect the outliers from uncertain weighted data streams with a lower time cost.</p>", "Keywords": "Outlier detection; Weighted frequent pattern mining; Deviation indices; Uncertain weighted data streams", "DOI": "10.1007/s10489-020-01718-z", "PubYear": 2020, "Volume": "50", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Saihua Cai", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing, China"}, {"AuthorId": 4, "Name": "Sicong Li", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inner Mongolia Power Group Mengdian Information & Telecommunication Co., Ltd, Hohhot, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing, China;Scientific research base for Integrated Technologies of Precision Agriculture (animal husbandry), The Ministry of Agriculture, Beijing, China"}], "References": [{"Title": "Minimal weighted infrequent itemset mining-based outlier detection approach on uncertain data stream", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "11", "Page": "6619", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Integrating aspect analysis and local outlier factor for intelligent review spam detection", "Authors": "Lan You; Qingxi Peng; Zenggang <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "163", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Efficient methods for mining weighted clickstream patterns", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Bay <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "112993", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Robust deep auto-encoding Gaussian process regression for unsupervised anomaly detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "376", "Issue": "", "Page": "180", "JournalTitle": "Neurocomputing"}, {"Title": "MiFI-Outlier: Minimal infrequent itemset-based outlier detection approach on uncertain data stream", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>g Li; Gang Yuan", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105268", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 83740427, "Title": "A CNN-based computational algorithm for nonlinear image diffusion problem", "Abstract": "<p>In the past, several partial differential equations (PDEs) based methods have been widely studied in image denoising. While solving these methods numerically, some parameters need to be chosen manually. This paper proposes a cellular neural network (CNN) based computational scheme for solving the nonlinear diffusion equation modeled for removing additive noise of digital images. The diffusion acts like smoothing on the noisy image, which is taken as an initial condition for the nonlinear PDE. In the proposed scheme, the template matrices of CNN evolve during the iterative diffusion and act as edge-preserving filters on the noisy images. The evolving diffusion ensures convergence of the diffusion process after a specific diffusion time. Therefore, the advantages of such a CNN-based solution scheme are more accurate restoration in terms of image quality with low computation and memory requirements. The experimental results show the effectiveness of the proposed algorithm on different sets of benchmark images degraded with additive noise.</p>", "Keywords": "Additive Gaussian noise; Cellular neural network; Edge preserving filters; Image denoising; Nonlinear diffusion", "DOI": "10.1007/s11042-020-09077-0", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Roorkee, Roorkee, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Roorkee, Roorkee, India"}], "References": [{"Title": "Retracted: Multiscale fast correlation filtering tracking algorithm based on a feature fusion model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "e5533", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 83740480, "Title": "Composite Clustering Sampling Strategy for Multiscale Spectral-Spatial Classification of Hyperspectral Images", "Abstract": "<p>In recent years, many high-performance spectral-spatial classification methods were proposed in the field of hyperspectral image classification. At present, a great quantity of studies has focused on developing methods to improve classification accuracy. However, some research has shown that the widely adopted pixel-based random sampling strategy is not suitable for spectral-spatial hyperspectral image classification algorithms. Therefore, a composite clustering sampling strategy is proposed, which can greatly reduce the overlap between the training set and the test set, while making sample points in the training set sufficiently representative in the spectral domain. At the same time, in order to solve problems of a three-dimensional Convolutional Neural Network which is commonly used in spectral-spatial hyperspectral image classification methods, such as long training time and large computing resource requirements, a multiscale spectral-spatial hyperspectral image classification model based on a two-dimensional Convolutional Neural Network is proposed, which effectively reduces the training time and computing resource requirements.</p>", "Keywords": "", "DOI": "10.1155/2020/9637839", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Li", "Affiliation": "College of Computer and Information, Hohai University, Nanjing 211100, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information, Hohai University, Nanjing 211100, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer and Information, Hohai University, Nanjing 211100, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer and Information, Hohai University, Nanjing 211100, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information, Hohai University, Nanjing 211100, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information, Hohai University, Nanjing 211100, China"}], "References": [{"Title": "Non-overlapping classification of hyperspectral imagery based on set-to-sets distance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "422", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 83740520, "Title": "Digital twin-based assembly data management and process traceability for complex products", "Abstract": "Complex products such as satellites, missiles, and aircraft typically have demanding requirements for dynamic data management and process traceability. The assembly process for these complex products involves high complexity, strong dynamics, many uncertainties, and frequent rework and repair, especially in the model development stage. Achieving assembly data management and process traceability for complex products has always been a challenge. A recently proposed solution involves one-to-one mapping of the corresponding physical entity, also known as the digital twin method. This paper proposes a digital twin-based assembly data management and process traceability approach for complex products. First, the dynamic evolutionary process of complex product assembly data was analyzed from three dimensions: granularity, period and version. Then, a framework of digital twin-based assembly data management and process traceability for complex products was constructed. Some core techniques are: 1) workflow-based product assembly data organization and version management; 2) synchronous modeling of the product assembly process based on digital twin; and 3) hierarchical management and traceability of product assembly data based on digital twin. On this basis, an algorithm flowchart for generating a product assembly data package was created, which includes product assembly data management, assembly process traceability, and generation of a product assembly data package. Furthermore, the Digital Twin-based Assembly Process Management and Control System (DT-APMCS) was designed to verify the efficiency of the proposed approach. Some aerospace-related assembly enterprises are currently using DT-APMCS and achieving satisfactory results. Finally, a summary of our work is given, and the future research work is also discussed.", "Keywords": "Digital twin ; Complex product ; Workflow ; Data Management ; Process traceability ; Product data package", "DOI": "10.1016/j.jmsy.2020.05.011", "PubYear": 2021, "Volume": "58", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Digital Manufacturing, School of Mechanical Engineering, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "Jingcheng Gong", "Affiliation": "Laboratory of Digital Manufacturing, School of Mechanical Engineering, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Digital Manufacturing, School of Mechanical Engineering, Beijing Institute of Technology, Beijing 100081, China;Corresponding author"}], "References": [{"Title": "Blockchain-based data management for digital twin of product", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "361", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": ********, "Title": "A Systematic Literature Review for Understanding the Antecedents of the Digital Open Government Matrix", "Abstract": "<p>Developed and developing nations are increasingly focused on implementing Digital Open Government (DOG) systems with the purpose of fostering community engagement, citizen participation, accountability, growth and development, better public services, and useful data. Both citizens and government have come to understand the value of DOG, and they are increasingly coordinating and collaborating with each other to improve the public services. The current study highlights the under-researched areas of DOG by offering legal, individual, economic, managerial, organizational, and group perspectives that could influence future DOG practices in either developed or developing countries. The study determines the inclusion and exclusion criteria as well as the quality audit processes associated with the research methods in order to enhance the quality and value of the findings. Finally, the study identifies a number of research gaps that may prove helpful for future studies.</p>", "Keywords": "", "DOI": "10.4018/IJEGR.**********", "PubYear": 2020, "Volume": "16", "Issue": "1", "JournalId": 8342, "JournalTitle": "International Journal of Electronic Government Research", "ISSN": "1548-3886", "EISSN": "1548-3894", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Salford, UK"}], "References": []}, {"ArticleId": 83740562, "Title": "Theoretical Approach for Instrument Development in Measuring User-Perceived E-Government Service Quality", "Abstract": "<p>The quality of e-government services plays a vital role in the effective interaction of users/citizens with e-government portals, and it also improves governments' efficiency and responsiveness as per users' expectations. The objective of this study is to develop an instrument to measure perceived e-government service quality by applying a three-steps approach for models validation; conceptualization, design, and normalization; it was validated with Oman e-government service users. In this article, eight main quality dimensions were studied and validated (personalization, usability, performance, web design, security, citizen involvement, satisfaction, and loyalty). The reported results emphasized the varying importance of all eight quality instruments, in addition to the higher impact of web design and security on e-government services in the context of the Oman e-government.</p>", "Keywords": "", "DOI": "10.4018/IJEGR.2020010103", "PubYear": 2020, "Volume": "16", "Issue": "1", "JournalId": 8342, "JournalTitle": "International Journal of Electronic Government Research", "ISSN": "1548-3886", "EISSN": "1548-3894", "Authors": [{"AuthorId": 1, "Name": "Taisira Al Balushi", "Affiliation": "Sultan Qaboos University, Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sultan Qaboos University, Oman"}], "References": []}, {"ArticleId": 83740596, "Title": "A Comprehensive Survey of Optical Remote Sensing Image Segmentation Methods", "Abstract": "Many papers have reviewed remote sensing image segmentation (RSIS) algorithms currently. Those existing surveys are insufficiently exhaustive to sort out the various RSIS methods, it is impossible to comprehensively compare characteristics of different RSIS methods. In addition, the segmentation efficiency and accuracy of the RSIS methods cannot always meet the subsequent image analysis requirements. Thus, a clear comparative analysis of various RSIS methods is essential to provide an in-depth understanding of RSIS and theoretical ideas for conducting in-depth research in the future. The goal of this article is to provide readers with the latest information on optical RSIS technology. Comparative measures of these methods are provided in terms of conceptual details, the merits and demerits, and the performance of various RSIS methods. Moreover, various RSIS methods’ experiments are carried out on optical images using the NWPU VHR-10 public remote sensing datasets. Through the review of optical RSIS methods, this paper provides data as complete as possible for further related research and development of RSIS methods. De nombreux articles ont passé en revue les algorithmes de segmentation d’images en télédétection (RSIS). Ces études sont insuffisamment exhaustives pour trier les différentes méthodes RSIS, il est impossible de comparer leurs diverses caractéristiques. De plus, l’efficacité et l’exactitude des méthodes de segmentation ne peuvent pas toujours répondre aux exigences requises pour le traitement subséquent des images. Ainsi, une analyse comparative claire des diverses méthodes RSIS est essentielle pour fournir une compréhension approfondie de ces méthodes et des idées théoriques pour développer de nouvelles approches à l’avenir. L’objectif de cet article est de fournir aux lecteurs les dernières informations sur la technologie optique RSIS. Les concepts des différentes méthodes sont analysés en détail, leurs avantages, leurs désavantages ainsi que leur performance sont comparés. En outre, diverses méthodes RSIS ont été testées sur des images optiques de la base de données de télédétection publiques NWPU VHR-10. Grâce à l’examen des méthodes optiques RSIS, cet article fournit des informations aussi complètes que cela est possible pour le développement ultérieur des méthodes RSIS. Acknowledgments This research is supported by the Natural Science Foundation of Jiangxi Provincial Department of Science and Technology (No. 20171BAB203028), the Foundation of Jiangxi Province Special Project (No. YC2018-S320) and the Program of Qingjiang Excellent Young Talents, Jiangxi University of Science and Technology (No. JXUSTQJBJ2018002). The authors would like to acknowledge the contributions of Jacqueline Wah to the spelling and grammar check for this paper.", "Keywords": "", "DOI": "10.1080/07038992.2020.1805729", "PubYear": 2020, "Volume": "46", "Issue": "5", "JournalId": 4152, "JournalTitle": "Canadian Journal of Remote Sensing", "ISSN": "0703-8992", "EISSN": "1712-7971", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Architectural and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, China"}, {"AuthorId": 2, "Name": "Hua Lv", "Affiliation": "School of Architectural and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Architectural and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangzhou Urban Renewal Planning Institute, Guangdong, China"}], "References": []}, {"ArticleId": 83740613, "Title": "An incremental density-based clustering framework using fuzzy local clustering", "Abstract": "This paper presents a novel incremental density-based clustering framework using the one-pass scheme, named Fuzzy Incremental Density-based Clustering (FIDC). Employing one-pass clustering in which each data point is processed once and discarded, FIDC can process large datasets with less computation time and memory, compared to its density-based clustering counterparts. Fuzzy local clustering is employed in local clusters assignment process to reduce clustering inconsistencies from one-pass clustering. To improve the clustering performance and simplify the parameter choosing process, the modified valley seeking algorithm is used to adaptively determine the outlier thresholds for generating the final clusters. FIDC can operate in both traditional and stream data clustering. The experimental results show that FIDC outperforms state-of-the-art algorithms in both clustering modes.", "Keywords": "Incremental clustering ; Density-based clustering ; Fuzzy clustering ; Stream data clustering", "DOI": "10.1016/j.ins.2020.08.052", "PubYear": 2021, "Volume": "547", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Science, Srinakharinwirot University, Thailand;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Faculty of Science, Srinakharinwirot University, Thailand"}], "References": [{"Title": "Sparse and low-redundant subspace learning-based dual-graph regularized robust feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104830", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 83740643, "Title": "Detection against randomly occurring complex attacks on distributed state estimation", "Abstract": "With the development of digitization and intelligence of information technology, cyber attacks tend to be much more complicated and intelligent, which can disrupt the normal system operation if no any protection mechanism is implemented. Motivated by the security problem of industrial control system, we study secure estimation problem in which sensors are exposed to hostile communication environment, where the attacker can randomly launch either DoS attacks or data integrity attacks. We design a distributed estimator equipped with a statistical learning based detector for each sensor over wireless sensor network, and derive an optimal gain for the estimator. Moreover, we investigate the relationship between false rate and the chosen confident level of the detector, we also demonstrate the influence of sliding window of the detector on the estimation performance and show the existence of an optimal scaling parameter corresponding to the best estimation performance. Finally, we prove the effectiveness and feasibility of the proposed estimator by some numerical examples.", "Keywords": "State estimation ; DoS attack ; Data integrity attack ; Statistical learning based detection", "DOI": "10.1016/j.ins.2020.08.008", "PubYear": 2021, "Volume": "547", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Advanced Control and Optimization for Chemical Processes, East China University of Science and Technology, Shanghai, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>g <PERSON>", "Affiliation": "Key Laboratory of Advanced Control and Optimization for Chemical Processes, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Control and Optimization for Chemical Processes, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Seventh Research Division, Beihang University (BUAA), Beijing, China"}], "References": []}, {"ArticleId": 83740770, "Title": "Deep attentional fine-grained similarity network with adversarial learning for cross-modal retrieval", "Abstract": "<p>People have witnessed the swift development of multimedia devices and multimedia technologies in recent years. How to catch interesting and highly relevant information from the magnanimous multimedia data becomes an urgent and challenging matter. To obtain more accurate retrieval results, researchers naturally think of using more fine-grained features to evaluate the similarity among multimedia samples. In this paper, we propose a Deep Attentional Fine-grained Similarity Network (DAFSN) for cross-modal retrieval, which is optimized in an adversarial learning manner. The DAFSN model consists of two subnetworks, attentional fine-grained similarity network for aligned representation learning and modal discriminative network. The front subnetwork adopts Bi-directional Long Short-Term Memory (LSTM) and pre-trained Inception-v3 model to extract text features and image features. In aligned representation learning, we consider not only the sentence-level pair-matching constraint but also the fine-grained similarity between word-level features of text description and sub-regional features of an image. The modal discriminative network aims to minimize the “heterogeneity gap” between text features and image features in an adversarial manner. We do experiments on several widely used datasets to verify the performance of the proposed DAFSN. The experimental results show that the DAFSN obtains better retrieval results based on the MAP metric. Besides, the result analyses and visual comparisons are presented in the experimental section.</p>", "Keywords": "Attention mechanism; Cross-modal retrieval; Bidirectional LSTM; Fine-grained similarity", "DOI": "10.1007/s11042-020-09450-z", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Fudan University, Shanghai, China"}, {"AuthorId": 2, "Name": "Xiaodong Gu", "Affiliation": "Department of Electronic Engineering, Fudan University, Shanghai, China"}], "References": [{"Title": "Semantic consistent adversarial cross-modal retrieval exploiting semantic similarity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "14733", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 83740918, "Title": "Compatibilization of PA6/ABS blend by SEBS-g-MA: morphological, mechanical, thermal, and rheological properties", "Abstract": "<p>Blends polyamide 6 (PA6) and acrylonitrile-butadiene-styrene (ABS) were compatibilized with a styrene–(ethylene–butene)–styrene triblock copolymer grafted with maleic anhydride (SEBS-g-MA). In particular, the effects of ABS (0–100 wt%) and compatibilizer (0, 8, and 16 wt%) content were studied. The blends were first prepared by twin-screw extrusion, and different specimens were prepared by injection molding. From the samples produced, the effects of blend composition on morphological, mechanical, rheological, and thermal properties are reported. The structural analysis confirmed that the original blend is immiscible but showed some compatibilization when in the presence of SEBS-g-MA. Incorporation of the compatibilizer and ABS showed negligible effect on the melting behavior of PA6. The compatibilized blends showed higher tensile strength compared with uncompatibilized ones. However, <PERSON>’s modulus decreased with increasing compatibilizer content. The mechanical results were confirmed by rheological measurements in terms of interaction between each components in the blend.</p>", "Keywords": "Polyamide 6; Acrylonitrile–butadiene–styrene; Blend; Compatibilization; Morphology", "DOI": "10.1007/s00170-020-05888-5", "PubYear": 2020, "Volume": "110", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Moroccan Foundation for Advanced Science, Innovation and Research (MAScIR), Institute of Nanomaterials and Nanotechnology (NANOTECH), Composites and Nanocomposites Center, Rabat, Morocco;Mechanic, Materials and composites (MMC), Laboratory of Energy Engineering, Materials and Systems, National School of Applied Sciences of Agadir, Ibn Zohr University, Agadir, Morocco"}, {"AuthorId": 2, "Name": "F. Z. <PERSON> Mechtali", "Affiliation": "Faculty of Science, Laboratory of Mechanic and Materials (LMM), Mohammed V-Agdal University, Rabat, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science, Laboratory of Mechanic and Materials (LMM), Mohammed V-Agdal University, Rabat, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Moroccan Foundation for Advanced Science, Innovation and Research (MAScIR), Institute of Nanomaterials and Nanotechnology (NANOTECH), Composites and Nanocomposites Center, Rabat, Morocco"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Faculty of Science, Laboratory of Mechanic and Materials (LMM), Mohammed V-Agdal University, Rabat, Morocco"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of chemical engineering and CERMA, Université Laval, Quebec, Canada"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Moroccan Foundation for Advanced Science, Innovation and Research (MAScIR), Institute of Nanomaterials and Nanotechnology (NANOTECH), Composites and Nanocomposites Center, Rabat, Morocco"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Moroccan Foundation for Advanced Science, Innovation and Research (MAScIR), Institute of Nanomaterials and Nanotechnology (NANOTECH), Composites and Nanocomposites Center, Rabat, Morocco"}], "References": [{"Title": "Mechanical and thermomechanical properties of clay-Bambara nut shell polyester bio-composite", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; C. <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "7-8", "Page": "2483", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Processing and characterization of 3D-printed nanoclay/acrylonitrile butadiene styrene (abs) nanocomposite gear", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "3-4", "Page": "619", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 83740932, "Title": "How to Design While Loops", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.321.1", "PubYear": 2020, "Volume": "321", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Seton Hall University"}], "References": []}, {"ArticleId": ********, "Title": "Co-design between robust L1 fault-tolerant control and discrete event-triggered communication scheme for networked control systems with transmission delay and quantisation", "Abstract": "In order to take into account both the system performance and economical utilisation of network resources, under the discrete event-triggered communication scheme, the problems of the robust L 1 fault-tolerant control for a class of uncertain networked control systems with actuator fault constraint are investigated. In consideration with the imperfect networked environment with transmission delay and quantisation, a model of the error dependent networked closed-loop fault system is established. By constructing an appropriate delay-dependent <PERSON><PERSON><PERSON>nov–<PERSON> functional, the sufficient condition for the existence of robust L 1 fault-tolerant controller is obtained for an error dependent networked closed-loop fault system, and a co-design method for gaining the discrete event-triggered parameter and L 1 fault-tolerant controller is proposed in terms of linear matrix inequalities. Notice that the structure of <PERSON><PERSON><PERSON><PERSON>–<PERSON> functional which considers the piecewise-linear sawtooth structure characteristic of transmission delay, a less conservative result is obtained. Finally, simulation examples are provided to illustrate the feasibility of the proposed method.", "Keywords": "Co-design ; robust L 1 performance ; fault-tolerant control ; discrete event-triggered communication scheme (DETCS) ; quantisation ; networked control systems (NCSs)", "DOI": "10.1080/00207721.2020.1808111", "PubYear": 2020, "Volume": "51", "Issue": "15", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electrical and Information Engineering, Northeast Petroleum University, Daqing, People's Republic of China;Communication and Electronic Engineering Institute, Qiqihar University, Qiqihar, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical and Information Engineering, Northeast Petroleum University, Daqing, People's Republic of China"}], "References": []}, {"ArticleId": 83741427, "Title": "High-Performance Machine Learning for Large-Scale Data Classification considering Class Imbalance", "Abstract": "<p>Currently, data classification is one of the most important ways to analysis data. However, along with the development of data collection, transmission, and storage technologies, the scale of the data has been sharply increased. Additionally, due to multiple classes and imbalanced data distribution in the dataset, the class imbalance issue is also gradually highlighted. The traditional machine learning algorithms lack of abilities for handling the aforementioned issues so that the classification efficiency and precision may be significantly impacted. Therefore, this paper presents an improved artificial neural network in enabling the high-performance classification for the imbalanced large volume data. Firstly, the Borderline-SMOTE (synthetic minority oversampling technique) algorithm is employed to balance the training dataset, which potentially aims at improving the training of the back propagation neural network (BPNN), and then, zero-mean, batch-normalization, and rectified linear unit (ReLU) are further employed to optimize the input layer and hidden layers of BPNN. At last, the ensemble learning-based parallelization of the improved BPNN is implemented using the Hadoop framework. Positive conclusions can be summarized according to the experimental results. Benefitting from Borderline-SMOTE, the imbalanced training dataset can be balanced, which improves the training performance and the classification accuracy. The improvements for the input layer and hidden layer also enhance the training performances in terms of convergence. The parallelization and the ensemble learning techniques enable BPNN to implement the high-performance large-scale data classification. The experimental results show the effectiveness of the presented classification algorithm.</p>", "Keywords": "", "DOI": "10.1155/2020/1953461", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Grid Sichuan Economic Research Institute, Chengdu 610041, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Sichuan University, Chengdu 610065, China"}], "References": []}, {"ArticleId": 83741433, "Title": "Applicability of a Single Depth Sensor in Real-Time 3D Clothes Simulation: Augmented Reality Virtual Dressing Room Using Kinect Sensor", "Abstract": "<p>A busy lifestyle led people to buy readymade clothes from retail stores with or without fit-on, expecting a perfect match. The existing online cloth shopping systems are capable of providing only 2D images of the clothes, which does not lead to a perfect match for the individual user. To overcome this problem, the apparel industry conducts many studies to reduce the time gap between cloth selection and final purchase by introducing “virtual dressing rooms.” This paper discusses the design and implementation of augmented reality “virtual dressing room” for real-time simulation of 3D clothes. The system is developed using a single Microsoft Kinect V2 sensor as the depth sensor, to obtain user body parameter measurements, including 3D measurements such as the circumferences of chest, waist, hip, thigh, and knee to develop a unique model for each user. The size category of the clothes is chosen based on the measurements of each customer. The Unity3D game engine was incorporated for overlaying 3D clothes virtually on the user in real time. The system is also equipped with gender identification and gesture controllers to select the cloth. The developed application successfully augmented the selected dress model with physics motions according to the physical movements made by the user, which provides a realistic fitting experience. The performance evaluation reveals that a single depth sensor can be applied in the real-time simulation of 3D cloth with less than 10% of the average measurement error.</p>", "Keywords": "", "DOI": "10.1155/2020/1314598", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 13197, "JournalTitle": "Advances in Human-Computer Interaction", "ISSN": "1687-5893", "EISSN": "1687-5907", "Authors": [{"AuthorId": 1, "Name": "Sasadara <PERSON>", "Affiliation": "Department of Physics, University of Sri Jayewardenepura, Nugegoda, Sri Lanka"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Sri Jayewardenepura, Nugegoda, Sri Lanka"}, {"AuthorId": 3, "Name": "Ravinda G. N. <PERSON>", "Affiliation": "Department of Computer Science, University of Sri Jayewardenepura, Nugegoda, Sri Lanka"}, {"AuthorId": 4, "Name": "Indika L. <PERSON>", "Affiliation": "Department of Physics, University of Sri Jayewardenepura, Nugegoda, Sri Lanka"}], "References": []}, {"ArticleId": 83741486, "Title": "Approximating Global Optimum for Probabilistic Truth Discovery", "Abstract": "<p>The problem of truth discovery arises in many areas such as database, data mining, data crowdsourcing and machine learning. It seeks trustworthy information from possibly conflicting data provided by multiple sources. Due to its practical importance, the problem has been studied extensively in recent years. Two competing models were proposed for truth discovery, weight-based model and probabilistic model. While ((1+ psilon )) -approximations have already been obtained for the weight-based model, no quality guaranteed solution has been discovered yet for the probabilistic model. In this paper, we focus on the probabilistic model and formulate it as a geometric optimization problem. Based on a sampling technique and a few other ideas, we achieve the first ((1 +  psilon )) -approximation solution. Our techniques can also be used to solve the more general multi-truth discovery problem. We validate our method by conducting experiments on both synthetic and real-world datasets (teaching evaluation data) and comparing its performance to some existing approaches. Our solutions are closer to the truth as well as global optimum based on the experimental result. The general technique we developed has the potential to be used to solve other geometric optimization problems.</p>", "Keywords": "Geometric optimization; Data mining; High dimensions; Approximation", "DOI": "10.1007/s00453-020-00715-5", "PubYear": 2020, "Volume": "82", "Issue": "10", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, State University of New York at Buffalo, Buffalo, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, State University of New York at Buffalo, Buffalo, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, State University of New York at Buffalo, Buffalo, USA"}], "References": []}, {"ArticleId": 83741487, "Title": "The Inverse Voronoi Problem in Graphs I: Hardness", "Abstract": "<p>We introduce the inverse Voronoi diagram problem in graphs: given a graph G with positive edge-lengths and a collection \\({\\mathbb {U}}\\) of subsets of vertices of V ( G ), decide whether \\({\\mathbb {U}}\\) is a Voronoi diagram in G with respect to the shortest-path metric. We show that the problem is NP-hard, even for planar graphs where all the edges have unit length. We also study the parameterized complexity of the problem and show that the problem is W[1]-hard when parameterized by the number of Voronoi cells or by the pathwidth of the graph.</p>", "Keywords": "Distances in graphs; <PERSON><PERSON><PERSON><PERSON> diagram; Inverse <PERSON><PERSON><PERSON><PERSON> problem; NP-complete; Parameterized complexity", "DOI": "10.1007/s00453-020-00716-4", "PubYear": 2020, "Volume": "82", "Issue": "10", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LIP UMR5668, CNRS, ENS de Lyon, Univ Lyon, Université Claude Bernard Lyon 1, Lyon, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Mathematics and Physics, University of Ljubljana, Ljubljana, Slovenia;IMFM, Ljubljana, Slovenia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Simon Fraser University, Burnaby, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Departament d’Enginyeria Informàtica i Matemàtiques, Universitat Rovira i Virgili, Tarragona, Spain"}], "References": []}, {"ArticleId": 83741489, "Title": "Multiple time-series convolutional neural network for fault detection and diagnosis and empirical study in semiconductor manufacturing", "Abstract": "<p>The development of information technology and process technology have been enhanced the rapid changes in high-tech products and smart manufacturing, specifications become more sophisticated. Large amount of sensors are installed to record equipment condition during the manufacturing process. In particular, the characteristics of sensor data are temporal. Most the existing approaches for time series classification are not applicable to adaptively extract the effective feature from a large number of sensor data, accurately detect the fault, and provide the assignable cause for fault diagnosis. This study aims to propose a multiple time-series convolutional neural network (MTS-CNN) model for fault detection and diagnosis in semiconductor manufacturing. This study incorporates data augmentation with sliding window to generate amounts of subsequences and thus to enhance the diversity and avoid over-fitting. The key features of equipment sensor can be learned automatically through stacked convolution-pooling layers. The importance of each sensor is also identified through the diagnostic layer in the proposed MTS-CNN. An empirical study from a wafer fabrication was conducted to validate the proposed MTS-CNN and compare the performance among the other multivariate time series classification methods. The experimental results demonstrate that the MTS-CNN can accurately detect the fault wafers with high accuracy, recall and precision, and outperforms than other existing multivariate time series classification methods. Through the output value of the diagnostic layer in MTS-CNN, we can identify the relationship between each fault and different sensors and provider valuable information to associate the excursion for fault diagnosis.</p>", "Keywords": "Fault detection and diagnosis; Time series classification; Deep learning; Convolutional neural network; Smart manufacturing", "DOI": "10.1007/s10845-020-01591-0", "PubYear": 2021, "Volume": "32", "Issue": "3", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, National Taipei University of Technology, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, Yuan Ze University, Taoyuan, Taiwan"}], "References": [{"Title": "Data-driven prognostic method based on self-supervised learning approaches for fault detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7", "Page": "1611", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Literature review of Industry 4.0 and related technologies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "1", "Page": "127", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Intelligent rotating machinery fault diagnosis based on deep learning using data augmentation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "433", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Tool wear predicting based on multi-domain feature fusion by deep convolutional neural network in milling operations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "4", "Page": "953", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A hybrid information model based on long short-term memory network for tool condition monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "6", "Page": "1497", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Similarity matching of wafer bin maps for manufacturing intelligence to empower Industry 3.5 for semiconductor manufacturing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106358", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Development of a speed invariant deep learning model with application to condition monitoring of rotating machinery", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "393", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A case study of conditional deep convolutional generative adversarial networks in machine fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "407", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 83741507, "Title": "Accelerating number theoretic transform in GPU platform for fully homomorphic encryption", "Abstract": "<p>In scientific computing and cryptography, there are many applications that involve large integer multiplication, which is a time-consuming operation. To reduce the computational complexity, number theoretic transform is widely used, wherein the multiplication can be performed in the frequency domain with reduced complexity. However, the speed performance of large integer multiplication is still not satisfactory if the operand size is very large (e.g., more than 100K-bit). In view of that, several researchers had proposed to accelerate the implementation of number theoretic transform using massively parallel GPU architecture. In this paper, we proposed several techniques to improve the performance of number theoretic transform implementation, which is faster than the state-of-the-art work by <PERSON> et al. The proposed techniques include register-based twiddle factors storage and multi-stream asynchronous computation, which leverage on the features offered in new GPU architectures. The proposed number theoretic transform implementation was applied to CMNT fully homomorphic encryption scheme proposed by <PERSON><PERSON> et al. With the proposed implementation technique, homomorphic multiplications in CMNT take 0.27 ms on GTX1070 desktop GPU and 7.49 ms in Jetson TX1 embedded system, respectively. This shows that the proposed implementation is suitable for practical applications in server environment as well as embedded system.</p>", "Keywords": "Number theoretic transform; Homomorphic encryption; Graphics processing unit; Cryptography", "DOI": "10.1007/s11227-020-03156-7", "PubYear": 2021, "Volume": "77", "Issue": "2", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tunku <PERSON>, Kajang, Malaysia"}, {"AuthorId": 2, "Name": "Wai-<PERSON> Lee", "Affiliation": "Universiti Tunku <PERSON>, Kampar, Malaysia"}, {"AuthorId": 3, "Name": "Bok<PERSON><PERSON>", "Affiliation": "Universiti Tunku <PERSON>, Kajang, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Tunku <PERSON>, Kajang, Malaysia"}], "References": []}, {"ArticleId": 83741682, "Title": "An evaluation of pose-normalization algorithms for point clouds introducing a novel histogram-based approach", "Abstract": "Building Information Modeling is growing more relevant as digital models are not only used during the construction phase but also throughout the building’s life cycle. The digital representation of geometric, physical and functional properties enables new methods for planning, execution and operation. Digital models of existing buildings are commonly derived from surveying data such as laser scanning which needs to be processed either manually or automatically throughout various steps. Aligning point clouds along the coordinate system’s main axes (also commonly known as pose normalization) is a task benefitting any point cloud processing workflow, be it manual or automated. With the goal of automating this task, we compare various existing methods and present our own approach based on point density histograms. We conclude this paper by comparing and discussing all methods in terms of speed and robustness.", "Keywords": "Point clouds ; Computational geometry ; Principal component analysis ; Building information modeling ; Pose normalization", "DOI": "10.1016/j.aei.2020.101132", "PubYear": 2020, "Volume": "46", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Geodetic Institute and Chair for Computing in Civil Engineering & Geo Information Systems, RWTH Aachen University, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Geodetic Institute and Chair for Computing in Civil Engineering & Geo Information Systems, RWTH Aachen University, Germany"}], "References": []}, {"ArticleId": 83742587, "Title": "Fingerspelling Identification for Chinese Sign Language via AlexNet-Based Transfer Learning and Adam Optimizer", "Abstract": "<p>As an important component of universal sign language and the basis of other sign language learning, finger sign language is of great significance. This paper proposed a novel fingerspelling identification method for Chinese Sign Language via AlexNet-based transfer learning and Adam optimizer, which tested four different configurations of transfer learning. Besides, in the experiment, <PERSON> algorithm was compared with stochastic gradient descent with momentum (SGDM) and root mean square propagation (RMSProp) algorithms, and comparison of using data augmentation (DA) against not using DA was executed to pursue higher performance. Finally, the best accuracy of 91.48% and average accuracy of 89.48 ± 1.16% were yielded by configuration M1 (replacing the last FCL8) with Adam algorithm and using 181x DA, which indicates that our method can identify Chinese finger sign language effectively and stably. Meanwhile, the proposed method is superior to other five state-of-the-art approaches.</p>", "Keywords": "", "DOI": "10.1155/2020/3291426", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing Normal University of Special Education, Nanjing 210038, China;Joint Accessibility Key Laboratory, China Disabled Persons' Federation, Nanjing 210038, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Nanjing Normal University of Special Education, Nanjing 210038, China;Joint Accessibility Key Laboratory, China Disabled Persons' Federation, Nanjing 210038, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Engineering, KIIT Deemed to University, Bhubaneswar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Henan Polytechnic University, Jiaozuo, Henan 454000, China;School of Mathematics and Actuarial Science, University of Leicester, Leicester LE1 7RH, UK;School of Architecture Building and Civil Engineering, Loughborough University, Loughborough LE11 3TU, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, University of Leicester, Leicester LE1 7RH, UK;Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology, Guilin 541004, China;Department of Information Systems, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah 21589, Saudi Arabia"}], "References": [{"Title": "An eight-layer convolutional neural network with stochastic pooling, batch normalization and dropout for fingerspelling recognition of Chinese sign language", "Authors": "Xianwei Jiang; Mingzhou Lu; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "15697", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 83742716, "Title": "Students' self‐report and observed learning orientations in blended university course design: How are they related to each other and to academic performance?", "Abstract": "", "Keywords": "", "DOI": "10.1111/jcal.12453", "PubYear": 2020, "Volume": "36", "Issue": "6", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Office of Pro‐Vice‐Chancellor (Arts, Education and Law), Griffith University, Brisbane, Queensland, Australia; Griffith Institute for Educational Research, Griffith University, Brisbane, Queensland, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Information Technology, Engineering and the Environment, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Office of Pro‐Vice‐Chancellor (Arts, Education and Law), Griffith University, Brisbane, Queensland, Australia"}], "References": []}, {"ArticleId": 83742738, "Title": "Multifunctional surface microrollers for targeted cargo delivery in physiological blood flow", "Abstract": "<p>Mobile microrobots offer great promise for minimally invasive targeted medical theranostic applications at hard-to-access regions inside the human body. The circulatory system represents the ideal route for navigation; however, blood flow impairs propulsion of microrobots especially for the ones with overall sizes less than 10 micrometers. Moreover, cell- and tissue-specific targeting is required for efficient recognition of disease sites and long-term preservation of microrobots under dynamic flow conditions. Here, we report cell-sized multifunctional surface microrollers with ~3.0 and ~7.8-micrometer diameters, inspired by leukocytes in the circulatory system, for targeted drug delivery into specific cells and controlled navigation inside blood flow. The leukocyte-inspired spherical microrollers are composed of magnetically responsive Janus microparticles functionalized with targeting antibodies against cancer cells (anti-HER2) and light-cleavable cancer drug molecules (doxorubicin). Magnetic propulsion and steering of the microrollers resulted in translational motion speeds up to 600 micrometers per second, around 76 body lengths per second. Targeting cancer cells among a heterogeneous cell population was demonstrated by active propulsion and steering of the microrollers over the cell monolayers. The multifunctional microrollers were propelled against physiologically relevant blood flow (up to 2.5 dynes per square centimeter) on planar and endothelialized microchannels. Furthermore, the microrollers generated sufficient upstream propulsion to locomote on inclined three-dimensional surfaces in physiologically relevant blood flow. The multifunctional microroller platform described here presents a bioinspired approach toward in vivo controlled propulsion, navigation, and targeted active cargo delivery in the circulatory system.</p>", "Keywords": "", "DOI": "10.1126/scirobotics.aba5726", "PubYear": 2020, "Volume": "5", "Issue": "42", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany."}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Erkoc", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany.;Faculty of Engineering and Natural Sciences, Bahcesehir University, Istanbul 34353, Turkey."}, {"AuthorId": 4, "Name": "Alp Can Karacakol", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany.;Department of Mechanical Engineering, Carnegie Mellon University, Pittsburgh, PA 15213, USA."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Physical Intelligence Department, Max Planck Institute for Intelligent Systems, 70569 Stuttgart, Germany.;School of Medicine and School of Engineering, Koç University, Istanbul 34450, Turkey."}], "References": []}, {"ArticleId": 83742752, "Title": "Reliability-Centered Maintenance Task Planning for Overhead Electric Power Distribution Networks", "Abstract": "<p>In this paper, it is presented an approach to match technically and economically the reliability of electricity distribution networks through an optimization methodology consisting of the mathematical model and metaheuristic solution technique to obtain an optimized plan for efficient management of maintenance tasks. The problem of maintenance tasks is formulated as a mixed dynamic nonlinear multi-objective optimization model, in which costs to perform maintenance tasks on equipment and/or components that make up the electric distribution network are minimized, while they have their reliability maximized. The constraints of this model are the individual and group electricity supply interruption duration and frequency indices, and availability of financial and human resources. The solution of this problem is obtained through a specialized non-dominated sorting genetic algorithm multi-objective metaheuristic, which provides a set of non-dominated solutions very close to the optimal Pareto frontier. Each solution at this frontier represents a maintenance plan able to assist the making decision of the operators in distribution companies for managing the maintenance crews.</p>", "Keywords": "NSGA-II; Metaheuristic; Multi-objective programming; Reliability; Maintenance tasks; Distribution systems", "DOI": "10.1007/s40313-020-00606-8", "PubYear": 2020, "Volume": "31", "Issue": "5", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, São Paulo State University - UNESP, Ilha Solteira, Brazil"}, {"AuthorId": 2, "Name": "Jonatas B<PERSON>", "Affiliation": "Electrical Engineering Department, São Paulo State University - UNESP, Ilha Solteira, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State University of Mato Grosso - UNEMAT, Barra do Bugres, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Electrical Engineering Department, São Paulo State University - UNESP, Ilha Solteira, Brazil"}], "References": []}, {"ArticleId": 83742791, "Title": "Using Video Game Development to Motivate Program Design and Algebra Among Inner-City High School Students", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.321.5", "PubYear": 2020, "Volume": "321", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Seton Hall University"}], "References": []}, {"ArticleId": 83742861, "Title": "Underspecification, Parsing Mismatches and Routinisation: The Historical Development of the Clitic Systems of Greek Dialects", "Abstract": "In this paper, the historical development of the clitic systems of Standard Modern, Cypriot and Pontic Greek is discussed. These three varieties not only present the whole range of variation one can find across clitic systems in Greek but, furthermore, derive from a common linguistic ancestor, i.e. Koine Greek. This paper argues that the transition from Koine Greek to the Medieval varieties and from the Medieval varieties to the respective modern ones can be explained by making the assumption that routinisation (in the sense of <PERSON><PERSON> and <PERSON> in Behav Brain Sci 27:169–226, 2004) and parsing/hearer assymetries are two important factors behind syntactic change. The claim is that the transition from Koine to the Medieval Greek varieties involves the emergence of a clitic system with encoded syntactic constraints out of a freer one, where clitic positioning was regulated by pragmatic preferences rather than syntactic constraints. Then, the transition to the modern varieties from the respective medieval ones is explained, at least partly, on the assumption that production/parsing mismatches are capable of triggering syntactic change. This last assumption combined with: (a) the tendency to obtain more generalised parsing triggers for parsing the individual clitics and (b) the fact that the Medieval varieties in question differ in minimal but crucial ways, provides us an explanation for the transition to the modern varieties.", "Keywords": "Dynamic Syntax; Diachronic change; Routinization; Parsing/production assymetries", "DOI": "10.1007/s10849-020-09319-2", "PubYear": 2021, "Volume": "30", "Issue": "2", "JournalId": 5283, "JournalTitle": "Journal of Logic, Language and Information", "ISSN": "0925-8531", "EISSN": "1572-9583", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Linguistic Theory and Studies in Probability, Department of Philosophy, Linguistics and Theory of Science, University of Gothenburg, Gothenburg, Sweden"}], "References": []}, {"ArticleId": 83742878, "Title": "Stability Analysis of Interface Conditions for Ocean–Atmosphere Coupling", "Abstract": "<p>In this paper we analyze the stability of different coupling strategies for multidomain PDEs that arise in general circulation models used in climate simulations. We focus on fully coupled ocean–atmosphere models that are needed to represent and understand the complicated interactions of these two systems, becoming increasingly important in climate change assessment in recent years. Numerical stability issues typically arise because of different time-stepping strategies applied to the coupled PDE system. In particular, the contributing factors include using large time steps, lack of accurate interface flux, and single-iteration coupling. We investigate the stability of the coupled ocean–atmosphere models for various interface conditions such as the Dirichlet–Neumann condition and the bulk interface condition, which is unique to climate modeling. By analyzing a simplified model, we demonstrate how the parameterization of the bulk condition and other numerical and physical parameters affect the coupling stability and establish stability conditions for different coupling strategies.</p>", "Keywords": "Stability analysis; Coupled system; Partitioned algorithm; Ocean–atmosphere; 65M12; 34D20; 76R50", "DOI": "10.1007/s10915-020-01293-y", "PubYear": 2020, "Volume": "84", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematics and Computer Science Division, Argonne National Laboratory, Lemont, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geography, Ohio State University, Columbus, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Mathematics and Computer Science Division, Argonne National Laboratory, Lemont, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Environmental Science Division, Argonne National Laboratory, Lemont, USA"}], "References": []}, {"ArticleId": 83742935, "Title": "Integrating Gaussian mixture model and dilated residual network for action recognition in videos", "Abstract": "<p>Action recognition in video is one of the important applications in computer vision. In recent years, the two-stream architecture has made significant progress in action recognition, but it has not systematically explored spatial–temporal features. Therefore, this paper proposes an integrated approach using Gaussian mixture model (GMM) and dilated convolution residual network (GD-RN) for action recognition. This method uses ResNet-101 as spatial and temporal stream ConvNet. On the one hand, this paper first sends the action video into the GMM for background subtraction and then sends the video marking the action profile to ResNet-101 for identification and classification. Compared with the baseline, ConvNet takes the original RGB image as input, which not only reduces the complexity of the video background, but also reduces the amount of computation of the learning space information. On the other hand, using the stacked optical flow images as the input of the ResNet-101 added to the dilated convolution, the convolution receptive field is expanded without lowering the resolution of the optical flow image, thereby improving the classification accuracy. The two ConvNet-independent learning spatial and temporal features of the GD-RN network finally fine-tune and fuse the spatio-temporal features to obtain the final action recognition accuracy. The action recognition method proposed in this paper is tested on the challenging UCF101 and HMDB51 datasets, and accuracy rates of 91.3% and 62.4%, respectively, are obtained, which proves the proposed method with the competitive results.</p>", "Keywords": "Dilated convolution; GMM; Residual network; Video action recognition", "DOI": "10.1007/s00530-020-00683-4", "PubYear": 2020, "Volume": "26", "Issue": "6", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computing and Software Engineering, Kennesaw State University, Marietta, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northeast Normal University, Changchun, China"}], "References": []}, {"ArticleId": 83742943, "Title": "Deep learning based facial expression recognition using improved Cat Swarm Optimization", "Abstract": "<p>Human emotional facial expressions play a vital role in interpersonal relations. Automated facial expression recognition has always remained a challenging problem in real-life applications as people vary significantly in the way of showing their expressions. Recently various approaches have been proposed for automatically analyzing the facial expression of a person. In this paper, a novel approach to human facial expression recognition by applying a modified version of the Cat Swarm Optimization (CSO) algorithm, called Improved Cat Swarm Optimization (ICSO) algorithm is proposed. The input image given to the proposed system retrieves similar images from the dataset as well as identifies the person’s emotional state through facial expressions. Deep features present in the face image are extracted using Deep Convolution Neural Network (DCNN) approach. ICSO is proposed to select optimal features from the face image that can uniquely distinguish the facial expression of a person. Employing DCNN with ICSO improves the retrieval performance of the proposed system. Ensemble classifiers that employ Neural Network (NN) and Support Vector Machine (SVM) are implemented to classify facial expressions such as normal, happy, sad, surprise, fear and angry. The performance of the proposed system is evaluated using JAFFE, CK+, Pie datasets and some real-world images. The proposed system outperforms the existing system, thus achieving superior accuracy and reduced computation time.</p>", "Keywords": "Image retrieval; Facial expression; Deep convolution neural network; Improved cat swarm optimization; Ensemble classifiers; SVM; Neural network", "DOI": "10.1007/s12652-020-02463-4", "PubYear": 2021, "Volume": "12", "Issue": "2", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Mepco Schlenk Engineering College (Autonomous), Sivakasi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Mepco Schlenk Engineering College (Autonomous), Sivakasi, India"}], "References": [{"Title": "Deep learning-based face analysis system for monitoring customer interest", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Serap Kazan", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "237", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Graph based feature extraction and hybrid classification approach for facial expression recognition", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2131", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 83742960, "Title": "Proceedings Eighth and Ninth International Workshop on Trends in Functional Programming in Education", "Abstract": "", "Keywords": "", "DOI": "10.4204/EPTCS.321.0", "PubYear": 2020, "Volume": "321", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Utrecht University"}], "References": []}, {"ArticleId": 83743389, "Title": "Machine Learning-Based Reduced Kernel PCA Model for Nonlinear Chemical Process Monitoring", "Abstract": "<p>Principal component analysis (PCA) is a popular tool for linear dimensionality reduction and fault detection. Kernel PCA (KPCA) is the nonlinear form of the PCA, which better exploits a complicated spatial structure of high-dimensional features, where a kernel function implicitly defines a nonlinear transformation into a feature space wherein standard PCA is performed. Despite its success and flexibility, conventional KPCA might not perform properly because the use of KPCA for a large-sized training dataset imposes a high computational load and a significant storage memory space since the required elements used for modelling have to be saved and used for monitoring, as well. To address this problem, a reduced KPCA (RKPCA) for fault detection of chemical processes is developed. RKPCA is a novel machine learning tool which merges dimensionality reduction, supervised learning as well as kernel selection. This novel method is used to reduce the size of recorded measurements while maintaining the most relevant data features. The removed observations, including redundant samples that are linearly correlated in the collected measurements, are described by only one sample. The obtained uncorrelated observations via PCA technique are then employed to identify the reduced KPCA model by which Hotelling (T^2) and squared predictive error or Q statistics are extracted for detection purposes. Besides, their combination is also used as a detection index. The performance of the proposed process monitoring technique is illustrated through its application to Tennessee Eastman process. The obtained results demonstrate the effectiveness of the developed RKPCA technique in detecting various faults with remarkably reduced computation time and memory storage space.</p>", "Keywords": "Fault detection (FD); Principal component analysis (PCA); Kernel PCA (KPCA); Reduced KPCA; Tennessee Eastman (TE) process", "DOI": "10.1007/s40313-020-00604-w", "PubYear": 2020, "Volume": "31", "Issue": "5", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LASMA, Badji <PERSON> - Annaba University, Annaba, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Signals and Systems Laboratory, Institute of Electrical and Electronics Engineering, University M <PERSON> of Boumerdes, Boumerdes, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Research Laboratory of Automation, Signal Processing and Image, National Engineering School of Monastir, Monastir, Tunisia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Program, Texas A&M University at Qatar, Doha, Qatar"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering Program, Texas A&M University at Qatar, Doha, Qatar"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering Program, Texas A&M University at Qatar, Doha, Qatar"}], "References": []}, {"ArticleId": 83743443, "Title": "Gaussian-based optical networks-on-chip: Performance analysis and optimization", "Abstract": "Compared with optical networks-on-chip (ONoCs) based on traditional topology such as mesh, torus, and fat-tree, Gaussian-based ONoCs have significant topological advantages in the network diameter and average jump distance. However, the intrinsic loss and crosstalk noise, which are inevitably inherent elements in basic optical devices, can lead to severe degradation of network performance and even restrict the normal communication of ONoCs. Therefore, in this paper, a numerical analysis model for analyzing the worst-case crosstalk noise and optical signal-to-noise ratio (OSNR) in Gaussian-based ONoCs is proposed. According to the all-pass characteristic of Gaussian-based ONoCs, the 5-ports all-pass optical routers are selected, which make the proposed model suitable for Gaussian-based ONoCs using arbitrary 5-ports all-pass optical routers. In addition, a numerical simulation is presented, which uses the Cygnus and optimized crossbar optical routers to verify the feasibility of the proposed analytical model. The simulation results show that the OSNR of Gaussian-based ONoCs decreases sharply with the network scale enlargement, the insertion loss and crosstalk noise tremendously limits the network scale. Furthermore, in order to make the Gaussian-based ONoCs more available, an optimization method is also proposed for improving network performance. The optimization method choose the best optical link between two arbitrary communication points, which can effectively avoid the worst case and greatly improve the performance of Gaussian-based ONoCs.", "Keywords": "Crosstalk noise ; Gaussian-based optical networks-on-chip ; Network performance optimization ; Optical signal-to-noise ratio ; Power loss", "DOI": "10.1016/j.nancom.2020.100286", "PubYear": 2020, "Volume": "24", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [{"AuthorId": 1, "Name": "Tingting Song", "Affiliation": "School of Electronic and Information Engineering, Southwest University, No. 2 Tiansheng Road, BeiBei District, Chongqing, 400715, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Southwest University, No. 2 Tiansheng Road, BeiBei District, Chongqing, 400715, China;School of Optoelectronic Information, University of Electronic Science and Technology of China, No. 2006 Xiyuan Avenue, Chengdu High-tech Zone, Sichuan, 611731, China;Corresponding author at: School of Electronic and Information Engineering, Southwest University, No. 2 Tiansheng Road, BeiBei District, Chongqing, 400715, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Southwest University, No. 2 Tiansheng Road, BeiBei District, Chongqing, 400715, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Southwest University, No. 2 Tiansheng Road, BeiBei District, Chongqing, 400715, China"}, {"AuthorId": 5, "Name": "Bo<PERSON> Liu", "Affiliation": "School of Electronic and Information Engineering, Southwest University, No. 2 Tiansheng Road, BeiBei District, Chongqing, 400715, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Optoelectronic Information, University of Electronic Science and Technology of China, No. 2006 Xiyuan Avenue, Chengdu High-tech Zone, Sichuan, 611731, China"}], "References": []}, {"ArticleId": 83743528, "Title": "Low-cost data partitioning and encrypted backup scheme for defending against co-resident attacks", "Abstract": "Aiming at preventing user data leakage and the damage that is caused by co-resident attacks in the cloud environment, a data partitioning and encryption backup (P&XE) scheme is proposed. After the data have been divided into blocks, the data are backed up using the XOR operation between the data. Then, the backup data are encrypted using a random string. Compared with the existing scheme, the proposed scheme resolves the conflict between data security and survivability via encrypted backup. At the same time, because the XOR-encrypted backup causes multiple data blocks to share the same backup data, the storage overhead of the user is reduced. In this paper, existing probabilistic models are used to compare the performances of an existing scheme and the P&XE scheme in terms of data security, data survivability and user storage overhead, and the overall performances of the two schemes in terms of these three aspects that are compared using control variables. Finally, the experimental results demonstrate the effectiveness of the P&XE scheme at improving user data security and survivability and reducing user storage overhead.", "Keywords": "Cloud computing ; Co-resident attack ; Data partition ; Encrypted backup ; Data theft ; Data corruption", "DOI": "10.1186/s13635-020-00110-1", "PubYear": 2020, "Volume": "2020", "Issue": "1", "JournalId": 43752, "JournalTitle": "EURASIP Journal on Information Security", "ISSN": "", "EISSN": "2510-523X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> T<PERSON>", "Affiliation": "Computer Science and Technology, School of Cyber Security and Computer, Hebei University, Baoding, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Technology, School of Cyber Security and Computer, Hebei University, Baoding, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Technology, School of Cyber Security and Computer, Hebei University, Baoding, China"}], "References": [{"Title": "BotMark: Automated botnet detection with hybrid analysis of flow-based and graph-based traffic behaviors", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "511", "Issue": "", "Page": "284", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 83743897, "Title": "Biomedical Relation Extraction Using Distant Supervision", "Abstract": "<p>With the accelerating growth of big data, especially in the healthcare area, information extraction is more needed currently than ever, for it can convey unstructured information into an easily interpretable structured data. Relation extraction is the second of the two important tasks of relation extraction. This study presents an overview of relation extraction using distant supervision, providing a generalized architecture of this task based on the state-of-the-art work that proposed this method. Besides, it surveys the methods used in the literature targeting this topic with a description of different knowledge bases used in the process along with the corpora, which can be helpful for beginner practitioners seeking knowledge on this subject. Moreover, the limitations of the proposed approaches and future challenges were highlighted, and possible solutions were proposed.</p>", "Keywords": "", "DOI": "10.1155/2020/8893749", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Beijing Institute of Technology, Beijing 100081, China;Department of Computer Science, University of Swabi, Anbar, Pakistan"}], "References": [{"Title": "CoCoScore: context-aware co-occurrence scoring for text mining applications using distant supervision", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "264", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 83744142, "Title": "A Multichannel fNIRS System for Prefrontal Mental Task Classification with Dual-level Excitation and Deep Forest Algorithm", "Abstract": "<p>This paper presents a multichannel functional continuous-wave near-infrared spectroscopy (fNIRS) system, which collects data under a dual-level light intensity mode to optimize SNR for channels with multiple source-detector separations. This system is applied to classify different cortical activation states of the prefrontal cortex (PFC). Mental arithmetic, digit span, semantic task, and rest state were selected as four mental tasks. A deep forest algorithm is employed to achieve high classification accuracy. By employing multigrained scanning to fNIRS data, this system can extract the structural features and result in higher performance. The proposed system with proper optimization can achieve 86.9% accuracy on the self-built dataset, which is the highest result compared to the existing systems.</p>", "Keywords": "", "DOI": "10.1155/2020/1567567", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 3, "Name": "Shaoyang Cui", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 4, "Name": "Xiangao Qi", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Micro/Nano Electronics, Shanghai Jiao Tong University, Shanghai 200240, China"}], "References": []}, {"ArticleId": 83744241, "Title": "Increasing evacuation flow through school bus emergency roof hatches", "Abstract": "Emergency escape roof hatches are used to evacuate school buses in rolled-over orientations. In the United States, the minimum opening size of a roof hatch is defined by Federal Motor Vehicle Safety Standard (FMVSS) no. 217. With the prevalence of rising obesity rates among children, the minimum roof hatch opening size may not be large enough to accommodate larger passengers. Post-accident conditions such as injuries, disorientation, and exit obstructions may also prevent unobstructed passage for egress within acceptable time limits. The purpose of this study was to redesign and fabricate a roof hatch with a larger opening and evaluate its egress characteristics for a range of typical school bus passengers. The larger roof hatch opening allows greater evacuation flow rates, and is almost functionally equivalent to the evacuation flow rate of the front door on an upright school bus.", "Keywords": "Accident ; Roll-over ; Evacuation ; School bus ; Surface transportation ; Students ; Emergency exits", "DOI": "10.1016/j.apergo.2020.103178", "PubYear": 2020, "Volume": "88", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, 3301 Shelby Center, Auburn University, AL, 36849, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, 3301 Shelby Center, Auburn University, AL, 36849, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Occupational Safety and Health, 157 Industry & Technology Center, Murray State University, Murray, KY, 42071, USA;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, 3301 Shelby Center, Auburn University, AL, 36849, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, 3301 Shelby Center, Auburn University, AL, 36849, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Industrial & Systems Engineering, 3301 Shelby Center, Auburn University, AL, 36849, USA"}], "References": []}, {"ArticleId": 83744604, "Title": "Support Vector Regression-Based Recursive Ensemble Methodology for Confidence Interval Estimation in Blood Pressure Measurements", "Abstract": "<p>The monitors of oscillometry blood pressure measurements are generally utilized to measure blood pressure for many subjects at hospitals, homes, and office, and they are actively studied. These monitors usually provide a single blood pressure point, and they are not able to indicate the confidence interval of the measured quantity. In this paper, we propose a new technique using a recursive ensemble based on a support vector machine to estimate a confidence interval for oscillometry blood pressure measurements. The recursive ensemble is based on a support vector machine that is used to effectively estimate blood pressure and then measure the confidence interval for the systolic blood pressure and diastolic blood pressure. The recursive ensemble methodology provides a lower standard deviation of error, mean error, and mean absolute error for the blood pressure as compared to those of the conventional techniques.</p>", "Keywords": "", "DOI": "10.1155/2020/7360702", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Sejong University, 209 Neungdong-ro, Gwangjin-gu, Seoul 05006, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Sejong University, 209 Neungdong-ro, Gwangjin-gu, Seoul 05006, Republic of Korea"}], "References": []}, {"ArticleId": 83744613, "Title": "ctDNA Detection in Microfluidic Platform: A Promising Biomarker for Personalized Cancer Chemotherapy", "Abstract": "<p>Early detection and characterization of circulating tumor DNA (ctDNA) can reveal mint of comprehensive biological insights from indicating the presence of tumor, identifying mutational changes of malignant cells, and allowing precision or targeted therapy together with monitoring disease progression, treatment resistance, and relapse of the disease. Apart from these, one of the greatest axiomatic implications of ctDNA detection is that it provides a new shed of light as noninvasive liquid biopsy as a replaceable procedure of surgical tumor biopsy. Despite the tremendous potential of ctDNA in cancer research, there remains a paucity of quantitative study on ctDNA detection and analysis. The majority of previously published microfluidic-based studies have focused on circulating tumor cell (CTC) detection and have failed to address the potential of ctDNA. The studies on microfluidic ctDNA detection are not consistent might be due to the complexity of ctDNA isolation as they present in low concentration in blood plasma. Researchers need to leverage the ability of microfluidic system for ctDNA analysis so that the significant enigma about cancer can be resolved effectively. This study, therefore, highlights the importance of ctDNA as cancer biomarker for liquid biopsy and provides an overview of the current laboratory as well as microfluidic techniques for ctDNA detection. This paper also attempts to show the emergence of new strands of microfluidic ctDNA detection and analysis for personalized cancer chemotherapy.</p>", "Keywords": "", "DOI": "10.1155/2020/8353674", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Control and Mechatronics Engineering, School of Electrical Engineering, Faculty of Engineering, Universiti Teknologi Malaysia, 81310 Skudai, Johor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Division of Control and Mechatronics Engineering, School of Electrical Engineering, Faculty of Engineering, Universiti Teknologi Malaysia, 81310 Skudai, Johor, Malaysia"}], "References": []}, {"ArticleId": 83744658, "Title": "Near-field sound source localization using principal component analysis–multi-output support vector regression", "Abstract": "<p>In this article, principal component analysis method, which is applied to image compression and feature extraction, is introduced into the dimension reduction of input characteristic variable of support vector regression, and a method of joint estimation of near-field angle and range based on principal component analysis dimension reduction is proposed. Signal-to-noise ratio and calculation amount are the decisive factors affecting the performance of the algorithm. Principal component analysis is used to fuse the main characteristics of training data and discard redundant information, the signal-to-noise ratio is improved, and the calculation amount is reduced accordingly. Similarly, support vector regression is used to model the signal, and the upper triangular elements of the signal covariance matrix are usually used as input features. Since the covariance matrix has more upper triangular elements, training it as a feature input will affect the training speed to some extent. Principal component analysis is used to reduce the dimensionality of the upper triangular element of the covariance matrix of the known signal, and it is used as the input feature of the multi-output support vector regression machine to construct the near-field parameter estimation model, and the parameter estimation of unknown signal is herein obtained. Simulation results show that this method has high estimation accuracy and training speed, and has strong adaptability at low signal-to-noise ratio, and the performance is better than that of the back-propagation neural network algorithm and the two-step multiple signal classification algorithm.</p>", "Keywords": "", "DOI": "10.1177/1550147720916405", "PubYear": 2020, "Volume": "16", "Issue": "4", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Optoelectronic Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Physics and Optoelectronic Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Telecommunication Engineering, Shaanxi University of Technology, Hanzhong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Telecommunication Engineering, Shaanxi University of Technology, Hanzhong, China"}], "References": []}, {"ArticleId": 83744706, "Title": "Quantile Regression Analysis of Depression and Clinical Symptom Degree in Chinese Patients with Spinocerebellar Ataxia Type 3", "Abstract": "<p>Spinal cerebellar ataxia type 3 is a common SCA subtype in the world. It is a neurodegenerative disease characterized by ataxia. Patients exhibit common neuropsychological symptoms such as depression and anxiety. Some patients have suicidal tendencies when they are severely depressed. So, it is very important to study the severity of depression and clinical symptoms (SARA), to find out the patient’s psychological state in time and to help patients actively respond to treatment. A total of 97 Chinese SCA3 patients were enrolled in the study. The Beck Depression Scale was used to investigate the prevalence of depression in the confirmed patients. The distribution of depression data in these patients was investigated. Then, the quantifier was used to model the depression status of Chinese SCA3 patients. An analysis was conducted to identify the key factors affecting depression under different quantiles. Studies have shown that SARA and gender are important factors affecting depression; the effect of initial SARA is small, then the degree of influence increases, and the degree of influence decreases in the later period, but it is always positively correlated with depression; the development of women’s SARA is gentler than that of men, and the degree of depression is lower than that of men.</p>", "Keywords": "", "DOI": "10.1155/2020/1394617", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, College of Mathematics and Informatics & FJKLMAA, Fujian Normal University, Fuzhou 350000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, College of Mathematics and Informatics & FJKLMAA, Fujian Normal University, Fuzhou 350000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, College of Mathematics and Informatics & FJKLMAA, Fujian Normal University, Fuzhou 350000, China"}], "References": []}, {"ArticleId": 83744707, "Title": "An Adaptive Data Placement Architecture in Multicloud Environments", "Abstract": "<p>Cloud service providers (CSPs) can offer infinite storage space with cheaper maintenance cost compared to the traditional storage mode. Users tend to store their data in geographical and diverse CSPs so as to avoid vendor lock-in. Static data placement has been widely studied in recent works. However, the data access pattern is often time-varying and users may pay more cost if static placement is adopted during the data lifetime. Therefore, it is a pending problem and challenge of how to dynamically store users’ data under time-varying data access pattern. To this end, we propose ADPA, an adaptive data placement architecture that can adjust the data placement scheme based on the time-varying data access pattern and subject for minimizing the total cost and maximizing the data availability. The proposed architecture includes two main components: data retrieval frequency prediction module based on LSTM and data placement optimization module based on Q-learning. The performance of ADPA is evaluated through several experimental scenarios using NASA-HTTP workload and cloud providers information.</p>", "Keywords": "", "DOI": "10.1155/2020/1704258", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Donghua University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science & Information Engineering, Shanghai Institute of Technology, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Donghua University, Shanghai, China"}], "References": []}, {"ArticleId": 83744715, "Title": "Mean Shift-Based Multisource Localization Method in Wireless Binary Sensor Network", "Abstract": "<p>Source localization is one of the major research contents in the localization research of wireless sensor networks, which has attracted considerable attention for a long period. In recent years, the wireless binary sensor network (WBSN) has been widely used for source localization due to its high energy efficiency. A novel method which is based on WBSN for multiple source localization is presented in this paper. Firstly, the Neyman-Pearson criterion-based sensing model which takes into account the false alarms is utilized to identify the alarmed nodes. Secondly, the mean shift and hierarchical clustering method are performed on the alarmed nodes to obtain the cluster centers as the initial locations of signal sources. Finally, some voting matrices which can improve the localization accuracy are constructed to decide the location of each acoustic source. The simulation results demonstrate that the proposed method can provide a desirable performance superior to some traditional methods in accuracy and efficiency.</p>", "Keywords": "", "DOI": "10.1155/2020/4052409", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Robot Science and Engineering, Northeastern University, Shenyang 110004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Robot Science and Engineering, Northeastern University, Shenyang 110004, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang 110004, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Robot Science and Engineering, Northeastern University, Shenyang 110004, China"}], "References": []}, {"ArticleId": 83744731, "Title": "Image Retrieval Based on the Combination of Region and Orientation Correlation Descriptors", "Abstract": "<p>A large number of growing digital images require retrieval effectively, but the trade-off between accuracy and speed is a tricky problem. This paperwork proposes a lightweight and efficient image retrieval approach by combining region and orientation correlation descriptors (CROCD). The region color correlation pattern and orientation color correlation pattern are extracted by the region descriptor and the orientation descriptor, respectively. The feature vector of the image is extracted from the two correlation patterns. The proposed algorithm has the advantages of statistic and texture description methods, and it can represent the spatial correlation of color and texture. The feature vector has only 80 dimensions for full color images specifically. Therefore, it is very efficient in image retrieving. The proposed algorithm is extensively tested on three datasets in terms of precision and recall. The experimental results demonstrate that the proposed algorithm outperforms other state-of-the-art algorithms.</p>", "Keywords": "", "DOI": "10.1155/2020/6068759", "PubYear": 2020, "Volume": "2020", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Institute of Intelligent Control & Image Engineering, Xidian University, Xi’an 710071, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Institute of Intelligent Control & Image Engineering, Xidian University, Xi’an 710071, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Institute of Intelligent Control & Image Engineering, Xidian University, Xi’an 710071, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Research Institute of Intelligent Control & Image Engineering, Xidian University, Xi’an 710071, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Research Institute of Intelligent Control & Image Engineering, Xidian University, Xi’an 710071, China"}], "References": [{"Title": "Pattern-based image retrieval using GLCM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "10819", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 83744823, "Title": "A new solution approach for multi-stage semi-open queuing networks: An application in shuttle-based compact storage systems", "Abstract": "Multi-stage semi-open queuing networks (SOQNs) are widely used to analyze the performance of multi-stage manufacturing systems and automated warehousing systems. While there are several methods available for solving single-stage SOQNs, solution methods for multi-stage SOQNs are limited. Decomposition of a multi-stage SOQN into single-stage SOQNs and evaluation of an individual single-stage SOQN is a possibility. However, the challenge lies in obtaining the job departure process information from an upstream single-stage SOQN to evaluate the performance of a downstream single-stage SOQN. In this paper, we propose a two-moment approximation approach for estimating the squared coefficient of variation of the job inter-departure time from a single-stage SOQN, which can serve as an input to link multi-stage SOQNs. Using numerical experiments, we test the robustness of the proposed approach for various input parameter settings for both single and multi-class jobs. We find that the proposed approach works quite well, particularly when the coefficient of variation of the job inter-arrival time is less than two. We demonstrate the efficacy of the proposed approach using a case study on a multi-tier shuttle-based compact storage system and benchmark our results with an existing approach. The results indicate that our approach yields more accurate estimates of the performance measures in comparison to the existing approach in the literature.", "Keywords": "Semi-open queues ; Job departure process ; Approximation ; Facility planning and design", "DOI": "10.1016/j.cor.2020.105086", "PubYear": 2021, "Volume": "125", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Management Udaipur, Rajasthan 313001, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Management Ahmedabad, Gujarat 380015, India;Rotterdam School of Management, Erasmus University, Rotterdam, 3062 PA, The Netherlands;Corresponding author"}], "References": [{"Title": "A queue-based aggregation approach for performance evaluation of a production system with an AMHS", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "115", "Issue": "", "Page": "104838", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 83744824, "Title": "Health level classification by fusing medical evaluation from multiple social networks", "Abstract": "Accurate prediction of the health level of a user is a useful and promising technique involving many domains, such as medical analysis, user behavior understanding, industrial design, and human–computer interaction (HCI). User health level can be collaboratively determined by the interactions of physical/cyber companions within the same medical circle, since humans are generally considered social animals. Based on this observation, we propose a novel model for user health level categorization based on the intelligent fusion of medical evaluation results derived from multiple medically-aware social networks. More specifically, given the massive scale of Internet users, we are able to extract certain types of medical features to characterize user’s attributes from multiple sources. Initially, we construct a multiple affinity graph to describe a vast number of users’ relationships with each attribute. Afterwards, a graph-based clustering is conducted to group these massive number of users into multiple medically-aware social clusters. Based on these clusters, each user’s health status is determined by the user’s classified health level associated with his/her companions. Lastly, in order to combine the user health level from multiple attributes, a new multi-view medical attribute learning framework is presented. This framework automatically calculates the weight of each attribute. Comprehensive comparative studies on a real-world medical data set with millions of users have demonstrated the superiority and robustness of our approach.", "Keywords": "User health ; Medical data ; Social network ; Affinity graph ; Massive-scale ; Multi-view learning", "DOI": "10.1016/j.future.2020.08.008", "PubYear": 2021, "Volume": "114", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ocean University of China, College of Information Science and Engineering, Qingdao, 266100, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ocean University of China, College of Information Science and Engineering, Qingdao, 266100, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "China Center for Information Industry Development, Beijing, 100846, China"}], "References": []}, {"ArticleId": 83744840, "Title": "Impact of the COVID-19 pandemic on the Internet latency: A large-scale study", "Abstract": "The COVID-19 pandemic dramatically changed the way of living of billions of people in a very short time frame. In this paper, we evaluate the impact on the Internet latency caused by the increased amount of human activities that are carried out on-line. The study focuses on Italy, which experienced significant restrictions imposed by local authorities, but results about Spain, France, Germany, Sweden, and the whole of Europe are also included. The analysis of a large set of measurements shows that the impact on the network can be significant, especially in terms of increased variability of latency. In Italy we observed that the standard deviation of the average additional delay – the additional time with respect to the minimum delay of the paths in the region – during lockdown is ∼ 3 − 4 times as much as the value before the pandemic. Similarly, in Italy, packet loss is ∼ 2 − 3 times as much as before the pandemic. The impact is not negligible also for the other countries and for the whole of Europe, but with different levels and distinct patterns.", "Keywords": "Internet measurements ; COVID-19 ; Latency", "DOI": "10.1016/j.comnet.2020.107495", "PubYear": 2020, "Volume": "182", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dip. di Ing. dell’Informazione, Università di Pisa, Largo L. <PERSON>rino 1, 56122 Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Istituto di Informatica e Telematica, Consiglio Nazionale delle Ricerche, Via G. <PERSON>, 1, 56124 Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dip. di Ing. dell’Informazione, Università di Pisa, Largo L. <PERSON> 1, 56122 Pisa, Italy;Corresponding author"}], "References": [{"Title": "Campus traffic and e-Learning during COVID-19 pandemic", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "107290", "JournalTitle": "Computer Networks"}, {"Title": "RIPE IPmap active geolocation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "2", "Page": "3", "JournalTitle": "ACM SIGCOMM Computer Communication Review"}]}, {"ArticleId": 83744843, "Title": "Deep learning framework based on integration of S-Mask R-CNN and Inception-v3 for ultrasound image-aided diagnosis of prostate cancer", "Abstract": "The computer-aided diagnosis of prostate ultrasound images can aid in the detection and treatment of prostate cancer. However, the ultrasound images of the prostate sometimes come with serious speckle noise, low signal-to-noise ratio, and poor detection accuracy. To overcome this shortcoming, we proposed a deep learning model that integrates S-Mask R-CNN and Inception-v3 in the ultrasound image-aided diagnosis of prostate cancer in this paper. The improved S-Mask R-CNN was used to realize the accurate segmentation of prostate ultrasound images and generate candidate regions. The region of interest align algorithm was used to realize the pixel-level feature point positioning. The corresponding binary mask of prostate images was generated by the convolution network to segment the prostate region and the background. Then, the background information was shielded, and a data set of segmented ultrasound images of the prostate was constructed for the Inception-v3 network for lesion detection. A new network model was added to replace the original classification module, which is composed of forward and back propagation. Forward propagation mainly transfers the characteristics extracted from the convolution layer pooling layer below the pool_3 layer through the transfer learning strategy to the input layer and then calculates the loss value between the classified and label values to identify the ultrasound lesion of the prostate. The experimental results showed that the proposed method can accurately detect the ultrasound image of the prostate and segment prostate information at the pixel-level simultaneously. The proposed method has higher accuracy than that of the doctor’s manual diagnosis and other detection methods. Our simple and effective approach will serve as a solid baseline and help ease future research in the computer-aided diagnosis of prostate ultrasound images. Furthermore, this work will promote the development of prostate cancer ultrasound diagnostic technology.", "Keywords": "Prostate cancer ; Ultrasound image ; S-Mask R-CNN ; Inception-v3 ; Instance segmentation ; Image classification", "DOI": "10.1016/j.future.2020.08.015", "PubYear": 2021, "Volume": "114", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Guangdong Polytechnic Normal University, Guangzhou 510665, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ultrasonography, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Ultrasonography, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Guangdong Polytechnic Normal University, Guangzhou 510665, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Urology, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Guangdong Polytechnic Normal University, Guangzhou 510665, China;Corresponding author"}], "References": []}, {"ArticleId": 83744851, "Title": "A Blockchain-based access control scheme with multiple attribute authorities for secure cloud data sharing", "Abstract": "Ciphertext-policy attribute-based encryption(CP-ABE) has been widely studied and used in access control schemes for secure data sharing. Since in most of the existing attribute-based encryption methods, all user attributes are managed by a single central authority, it is easy to cause a single point of failure. Therefore, several multi-authority CP-ABE schemes are proposed to manage user attributes by multiple authorities. However, these schemes still do not eliminate the single point of failure in essence or suffer from high computation and communication overhead on data users. In this paper, we propose a Blockchain-based Multi-authority Access Control scheme called BMAC for sharing data securely. Shamir secret sharing scheme and permissioned blockchain (Hyperledger Fabric) are introduced to implement that each attribute is jointly managed by multiple authorities to avoid single point of failure. In addition, we take advantage of blockchain technology to establish trust among multiple authorities and exploit smart contracts to compute tokens for attributes managed across multiple management domains, which reduces communication and computation overhead on the data user side. Moreover, blockchain helps to record the access control process in a secure and auditable way. Finally, we analyze the security of the proposed algorithm. Further analysis and comparison show the performance of the proposed method.", "Keywords": "Attribute-based encryption ; Blockchain ; Access control ; Multiple authorities ; Data sharing", "DOI": "10.1016/j.sysarc.2020.101854", "PubYear": 2021, "Volume": "112", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Tsinghua University, Beijing 100084, China Beijing National Research Center for Information Science and Technology (BNRist), Beijing 100084, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Tsinghua University, Beijing 100084, China Beijing National Research Center for Information Science and Technology (BNRist), Beijing 100084, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Tsinghua University, Beijing 100084, China Beijing National Research Center for Information Science and Technology (BNRist), Beijing 100084, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Tsinghua University, Beijing 100084, China Beijing National Research Center for Information Science and Technology (BNRist), Beijing 100084, China"}], "References": [{"Title": "SBAC: A secure blockchain-based access control framework for information-centric networking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102444", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Blockchain data-based cloud data integrity protection mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "902", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A semi-autonomous distributed blockchain-based framework for UAVs system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "101728", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": 83744887, "Title": "Towards optimal wireless sensor network lifetime in three dimensional terrains using relay placement metaheuristics", "Abstract": "In wireless sensor networks, sensor nodes located close to the base station or sink are more susceptible to energy loss, leading to premature disconnection of the network or energy holes. This is due to the fact that all traffic is forwarded towards the sink, increasing the workloads for these closer nodes. One solution for this issue is to shorten the hop distance a sensor’s data has to travel until reaching the sink, by deploying additional relay nodes. This paper considers the problem of optimal relay node placement for maximizing the network lifetime of wireless sensor networks in three-dimensional terrains. We first design a mathematical model of the problem and reformulate it as a mixed-integer programming model to provide a basis for finding lower bound solutions. We divide the problem into two phases and show that the second phase can be solved exactly using an algorithm based on maximum flow and binary search. We then propose a local search algorithm for the first phase, utilizing the exact algorithm to create full solutions. Experimental validation on 3D datasets has been carried out to demonstrate the performance of our algorithms.", "Keywords": "Wireless sensor networks ; Load balancing ; Network lifetime ; Relay node placement", "DOI": "10.1016/j.knosys.2020.106407", "PubYear": 2020, "Volume": "206", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "VNU University of Science, Viet Nam;Hanoi University of Science and Technology, Viet Nam"}, {"AuthorId": 2, "Name": "Huynh Thi Thanh Binh", "Affiliation": "Hanoi University of Science and Technology, Viet Nam;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hanoi University of Science and Technology, Viet Nam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hanoi University of Science and Technology, Viet Nam"}, {"AuthorId": 5, "Name": "Le Trong Vin<PERSON>", "Affiliation": "VNU University of Science, Viet Nam"}], "References": [{"Title": "Machine learning based code dissemination by selection of reliability mobile vehicles in 5G networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "109", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 83744891, "Title": "Lessons learned from data stream classification applied to credit scoring", "Abstract": "The financial credibility of a person is a factor used to determine whether a loan should be approved or not, and this is quantified by a ‘credit score,’ which is calculated using a variety of factors, including past performance on debt obligations, profiling, amongst others. Machine learning has been widely applied to automate the development of effective credit scoring models over the years. Yet, studies show that the development of robust credit scoring models may take longer than a year, and thus, if the behavior of customers changes over time, the model will be outdated even before its deployment. In this paper, we made 3 anonymized real-world credit scoring datasets available alongside the results obtained. In each of these datasets, we verify whether the credit scoring task should be thought as an ephemeral scenario since many of the variables may drift over time, and thus, data stream mining techniques should be used since they were tailored for incremental learning and to detect and adapt to changes in the data distribution. Therefore, we compare both traditional batch machine learning algorithms with data stream algorithms in different validation schemes using both <PERSON><PERSON><PERSON><PERSON>–<PERSON> and Population Stability Index metrics. Furthermore, we also provide insights on the importance of features according to their Information Value, Mean Decrease Impurity, and Mean Positional Gain metrics, such that the last depicts changes in the importance of features over time. For 2 of the 3 tested datasets, the results obtained by data stream learners are comparable to predictive models currently in use, thus showing the efficiency of data stream classification for the credit scoring task.", "Keywords": "Credit scoring ; Machine learning datasets ; Data stream classification", "DOI": "10.1016/j.eswa.2020.113899", "PubYear": 2020, "Volume": "162", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "PPGIa, Pontifícia Universidade Católica do Paraná, Curitiba, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "PPGIa, Pontifícia Universidade Católica do Paraná, Curitiba, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PPGIa, Pontifícia Universidade Católica do Paraná, Curitiba, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "KST, Curitiba, Brazil"}], "References": [{"Title": "Kappa Updated Ensemble for drifting data stream mining", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "1", "Page": "175", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 83745188, "Title": "Joint feature and instance selection using manifold data criteria: application to image classification", "Abstract": "<p>In many pattern recognition applications feature selection and instance selection can be used as two data preprocessing methods that aim at reducing the computational cost of the learning process. Moreover, in some cases, feature subset selection can improve the classification performance. Feature selection and instance selection can be interesting since the choice of features and instances greatly influence the performance of the learnt models as well as their training costs. In the past, unifying both problems was carried out by solving a global optimization problem using meta-heuristics. This paradigm not only does not exploit the manifold structure of data but can be computationally expensive. To the best of our knowledge, the joint use of sparse modeling representative and feature subset relevance have not been exploited by the joint feature and selection methods. In this paper, we target the joint feature and instance selection by adopting feature subset relevance and sparse modeling representative selection. More precisely, we propose three schemes for the joint feature and instance selection. The first is a wrapper technique while the two remaining ones are filter approaches. In the filter approaches, the search process adopts a genetic algorithm in which the evaluation is mainly given by a score that quantify the goodness of the features and instances. An efficient instance selection technique is used and integrated in the search process in order to adapt the instances to the candidate feature subset. We evaluate the performance of the proposed schemes using image classification where classifiers are the nearest neighbor classifier and support vector machine classifier. The study is conducted on five public image datasets. These experiments show the superiority of the proposed schemes over various baselines. The results confirm that the filter approaches leads to promising improvement on classification accuracy when both feature selection and instance selection are adopted.</p>", "Keywords": "Feature selection; Instance selection; Feature and instance selection; Data reduction; Linear discriminant analysis (LDA); Local discriminant embedding (LDE); Classification", "DOI": "10.1007/s10462-020-09889-4", "PubYear": 2021, "Volume": "54", "Issue": "3", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "Fadi <PERSON>", "Affiliation": "University of the Basque Country UPV/EHU, San Sebastian, Spain;IKERBASQUE, Basque Foundation for Science, Bilbao, Spain"}], "References": []}, {"ArticleId": 83745243, "Title": "A Guide to Student-active Online Learning in Engineering", "Abstract": "Online learning in higher education is becoming increasingly common as the possibilities of the available digital infrastructure expand. A recent emergent driver for online learning is the closing of universities to limit the spread of the coronavirus (COVID-19). Many educators are now faced with the need to make their teaching digital, though they have little or no experience with online teaching methods. In such a situation, learning outcomes may come second to what can be readily implemented by available digital resources. In this paper, a design for student-active online learning in engineering is proposed as a guide to help take account of learning objectives first, and the digital tools and resources necessary to achieve those objectives second. In addition, the paper emphasises the social dimension of online learning, and recommends that explicit actions should be taken to increase positive social relations between students in an online course to be able to succeed with student-active learning methods. In the paper, a clear path is followed from objectives to learning activities, and then to assessments and evaluations, and appropriate digital tools and resources are suggested to support activities and evaluations in an online course. Online courses in engineering are targeted in particular, and challenges that arise from common activities such as problem solving and practical work in an online engineering course are addressed. The proposed guide emphasises usability to ensure that it can be used even by inexperienced digital educators, and an example on how the guide can be applied to design an online course in mobile robotics is given. The proposed guide aims to help shift online learning in engineering from traditionally teacher-active lectures to more student-active learning activities. © 2020 Norwegian Society of Automatic Control", "Keywords": "Engineering; Mobile Robotics; Online learning; Social processes; Student-active", "DOI": "10.4173/mic.2020.2.5", "PubYear": 2020, "Volume": "41", "Issue": "2", "JournalId": 22211, "JournalTitle": "Modeling, Identification and Control: A Norwegian Research Bulletin", "ISSN": "0332-7353", "EISSN": "1890-1328", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Electrical Engineering and Mathematics, Faculty of Engineering and Science, Western Norway University of Applied Sciences, Sogndal, Norway"}], "References": []}, {"ArticleId": 83745431, "Title": "Distributed Blade Pitch Angle Control in Floating Offshore Wind Farm with Coordination of Total Power Generation", "Abstract": "<p> <p>In this paper, we consider a distributed control approach for blade pitch control of a wind farm which consists of several floating offshore wind turbines. Each wind turbine is modeled as a discrete-time linear model based on the aerodynamics simulator for wind turbines called FAST (Fatigue, Aerodynamics, Structures, and Turbulence). We formulate an optimization problem with an objective function of a quadratic form to coordinate a total power generation determined by a Transmission System Operator (TSO) while keeping fluctuation of the generator speed and oscillation of each wind turbine sufficiently small. The numerical example with the FAST simulator shows the effectiveness of the proposed distributed control method.</p> </p>", "Keywords": "floating offshore wind turbine;LQ control;distributed optimization;blade pitch angle control", "DOI": "10.9746/sicetr.56.395", "PubYear": 2020, "Volume": "56", "Issue": "8", "JournalId": 24132, "JournalTitle": "Transactions of the Society of Instrument and Control Engineers", "ISSN": "0453-4654", "EISSN": "1883-8189", "Authors": [{"AuthorId": 1, "Name": "Wataru HASHIMOTO", "Affiliation": "Graduate School of Engineering, Osaka University"}, {"AuthorId": 2, "Name": "Naoki HAYASHI", "Affiliation": "Graduate School of Engineering, Osaka University"}, {"AuthorId": 3, "Name": "Naoyuki HARA", "Affiliation": "Graduate School of Engineering, Osaka Prefecture University"}, {"AuthorId": 4, "Name": "Shigemasa TAKAI", "Affiliation": "Graduate School of Engineering, Osaka University"}], "References": []}, {"ArticleId": 83745433, "Title": "Freehand RC Imaging Radar by Calibration of Positioning System with Optical Mouse Sensors", "Abstract": "<p> <p>RC radar is often used as a preliminary survey in renewal works for reinforced concrete (RC) structures. Because the RC radar linearly scans linear polarization antennas, the linear targets such as reinforcing bars and thin power lines perpendicular to the polarization is sometimes missed and is damaged in works of concrete coring. Therefore, a freehand RC radar is required which can flexibly scan to the arbitrary direction with arbitrary rotation angle. In this paper, we propose a calibration method of alignment and sensitivity errors of sensors for 2D positioning system of antennas using two optical mouse sensors. In addition, we integrate RC radar and positioning system, and show that freehand scan of circularly polarized antenna is effective for visualization of buried thin electric wire with 3D imaging results.</p> </p>", "Keywords": "RC radar;optical mouse sensor;positioning;3D imaging;error calibration", "DOI": "10.9746/sicetr.56.403", "PubYear": 2020, "Volume": "56", "Issue": "8", "JournalId": 24132, "JournalTitle": "Transactions of the Society of Instrument and Control Engineers", "ISSN": "0453-4654", "EISSN": "1883-8189", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>hi <PERSON>", "Affiliation": "Graduate School of Science and Technology, Gunma University"}, {"AuthorId": 2, "Name": "Jukiya KOSEKI", "Affiliation": "Graduate School of Science and Technology, Gunma University"}], "References": []}, {"ArticleId": 83745434, "Title": "Virtual Internal Model Tuning for Smith Compensator", "Abstract": "<p> <p>In this paper, we consider a new approach to data-driven controller tuning for time-delay systems. The <PERSON> compensator is widely known as an effective method for time-delay systems. In the case where a mathematical model of a time-delay system is completely known, it is well known that the desired response can be completely achieved by using suitable feedback controller using the mathematical model. In this paper, we address the case where a mathematical model is unknown and it is difficult to execute an ideal experiment for identification. A direct usage of the data is one of the rational approaches in such a case. Here, we apply Virtual Internal Model Tuning (VIMT), which was a data-driven controller update method by using output data and was proposed by the authors, to the <PERSON> compensator. We explain how VIMT is expand to this class of the controllers. We also consider the meaning of the cost function used in the proposed method. Finally, we illustrate an experimental example to show the validity and the effectiveness of the proposed method.</p> </p>", "Keywords": "data-driven control;time delay;Smith compensator;VIMT", "DOI": "10.9746/sicetr.56.412", "PubYear": 2020, "Volume": "56", "Issue": "8", "JournalId": 24132, "JournalTitle": "Transactions of the Society of Instrument and Control Engineers", "ISSN": "0453-4654", "EISSN": "1883-8189", "Authors": [{"AuthorId": 1, "Name": "Kei HIGUCHI", "Affiliation": "Department of Mechanical Engineering and Intelligent Systems Engineering, The University of Electro-Communications"}, {"AuthorId": 2, "Name": "Taichi IKEZAKI", "Affiliation": "Department of Mechanical Engineering and Intelligent Systems Engineering, The University of Electro-Communications"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering and Intelligent Systems Engineering, The University of Electro-Communications"}], "References": []}, {"ArticleId": 83745477, "Title": "SpPCANet: a simple deep learning-based feature extraction approach for 3D face recognition", "Abstract": "<p>Abstract</p><p>A Sparse Principal Component Analysis Network (SpPCANet) based feature extraction is proposed here for 3D face recognition. The network consists of three basic components: (1) Multistage sparse principal component analysis filters, (2) Binary hashing, and (3) Block-wise histogram computation. Here, the sparse principal component analysis is used to learn multistage filter banks at the convolution stage, which is followed by binary hashing for indexing and block-wise histogram for pooling. Finally, a linear support vector machine (SVM) is used for classifying the features extracted by SpPCANet. The proposed network SpPCANet is a lightweight deep learning network. Three well-known 3D face databases, namely, Frav3D, Bosphorus3D, and Casia3D, are used for validating the proposed system. This proposed network has been extensively studied by varying different parameters, such as the number of filters at the convolution layer and the size of filters at the convolution layer and size of non-overlapping blocks at the pooling layer. Handling all types of variation of faces available in Frav3D, Bosphorus3D, and Casia3D databases, the system has acquired 96.93%, 98.54%, and 88.80% recognition rates, respectively.</p>", "Keywords": "3D face image; Sparse principal component analysis filter; Binary hashing; Block-wise histogram; Lightweight deep network", "DOI": "10.1007/s11042-020-09554-6", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, Jadavpur University, Kolkata, India"}], "References": [{"Title": "DKD–DAD: a novel framework with discriminative kinematic descriptor and deep attention-pooled descriptor for action recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "5285", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 83745495, "Title": "A novel digital fuzzy system for image edge detection based on wrap-gate carbon nanotube transistors", "Abstract": "This study presents a novel digital fuzzy approach for image edge detection based on carbon nanotube field effect transistor (CNTFET). The proposed design employs a simple fuzzy rule for digital image edge detection. To investigate the functionality and performance of the proposed fuzzy approach, our results are compared with the MATLAB edge detection operators such as Sobel, <PERSON>ny, and Prewitt. Based on the simulation results, we demonstrate that the proposed fuzzy approach can be implemented through nanoscale logic gates and provide a reasonable accuracy. The core element of the proposed fuzzy system, implemented with CNTFETs, occupies 0.22 µm<sup>2</sup> layout area and consumes 535 nW power consumption. Our results also emphasize the benefits of the Gate-All-Around (GAA) CNTFETs as a potential candidate for nanoscale digital fuzzy image processing applications.", "Keywords": "Logic gates ; CNTFET fuzzy system ; Image edge detection ; Fuzzy logic", "DOI": "10.1016/j.compeleceng.2020.106811", "PubYear": 2020, "Volume": "87", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shahid Beheshti University, Tehran, Iran;Nanotechnology and Quantum Computing Lab of the Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Nanotechnology and Quantum Computing Lab of the Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Nanotechnology and Quantum Computing Lab of the Shahid Beheshti University, Tehran, Iran;Faculty of Electrical Engineering, Shahid Beheshti University, Tehran, Iran;Corresponding author at: Faculty of Electrical Engineering, Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shahid Beheshti University, Tehran, Iran;Nanotechnology and Quantum Computing Lab of the Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, University of California, Irvine, USA"}], "References": [{"Title": "Fuzzy conformable fractional differential equations: novel extended approach and new numerical solutions", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "16", "Page": "12501", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 83745612, "Title": "Exploring firms’ innovation capabilities through learning systems", "Abstract": "In this study, several machine learning-based experimental methods are used to analyse firms’ research and development (R&D)-related activities and predict their technological innovation performance. Using unbalanced panel data from the CSMAR database for all listed firms in China from 2008 to 2018, we analyse the firms’ basic information, R&D investment, patent application and authorization activity, financial status, and human capital. We use a logistic regression model, decision tree model, three weak classifiers random forest model, XGBoost model, and two weak classifiers gradient boosting decision tree (GBDT) model to integrate strong classifiers separately. A comparison of the results produced using the different models shows that the performance of the XGBoost model is better than that of the other models in terms of net profit, total sales revenue, and the number of invention patent applications as a proportion of the total number of patent applications. However, the performance of the GBDT model is significantly better than that of the other models in terms of the number of patent applications per 100,000 yuan of R&D expenditure. The results of this study can help scholars to accurately predict the innovation performance of firms and help business managers to make better decisions to improve the innovation performance of their firms in the current era of rapid technological change.", "Keywords": "Machine learning ; Innovation input capability ; Collaborative innovation capability ; Innovation performance ; XGBoost ; GBDT", "DOI": "10.1016/j.neucom.2020.03.100", "PubYear": 2020, "Volume": "409", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Beijing University of Posts and Telecommunications, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Psychology, Chinese Academy of Sciences, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Zhizhen Zhineng Network Technology Co., Ltd, China"}, {"AuthorId": 4, "Name": "Changyuan Jing", "Affiliation": "International School, Beijing University of Posts and Telecommunications, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Academy of Mathematics and Systems Science, Chinese Academy of Sciences, China;Corresponding author at: Academy of Mathematics and Systems Science, Chinese Academy of Sciences, China"}], "References": []}, {"ArticleId": 83745625, "Title": "DSM", "Abstract": "The number of cores and the capacities of main memory in modern systems have been growing significantly. Specifically, memory scaling, although at a slower pace than computation scaling, provided opportunities for very large DRAMs with Terabytes (TBs) capacity. Consequently, addressing the performance and energy consumption bottlenecks of DRAMs is more important than ever. DRAM memory refresh operation is one of the main contributing factors to the memory overheads, especially for large capacity DRAMs used in modern servers and emerging large-scale data centers. This paper addresses the memory refresh problem by leveraging the fact that most cloud servers host virtualized systems that use similar kernels, libraries, etc. We propose and experimentally evaluate a novel approach that exploits this observation to address the DRAM refresh overhead in such systems. More specifically, in this work, we present DSM, a light-weight hardware extension in memory controller to detect the pages with same content in memory and refresh only one of them and redirect the requests to the others to this page. Our detailed experimental analysis shows that the proposed DSM design can reduce 99\\textsuperscriptth percentile memory access latency by up to 2.01x, and it also reduces the overall memory energy consumption by up to 8.5%.", "Keywords": "", "DOI": "10.1145/3392151", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Pennsylvania State University, State College, PA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pennsylvania State University, State College, PA, USA"}, {"AuthorId": 3, "Name": "Jagadish B. Kotra", "Affiliation": "AMD Research, Austin, TX, USA"}], "References": []}, {"ArticleId": 83745626, "Title": "Optimal Bidding Strategies for Online Ad Auctions with Overlapping Targeting Criteria", "Abstract": "We analyze the problem of how to optimally bid for ad spaces in online ad auctions. For this we consider the general case of multiple ad campaigns with overlapping targeting criteria. In our analysis we first characterize the structure of an optimal bidding strategy. In particular, we show that an optimal bidding strategies decomposes the problem into disjoint sets of campaigns and targeting groups. In addition, we show that pure bidding strategies that use only a single bid value for each campaign are not optimal when the supply curves are not continuous. For this case, we derive a lower-bound on the optimal cost of any bidding strategy, as well as mixed bidding strategies that either achieve the lower-bound or can get arbitrarily close to it.", "Keywords": "", "DOI": "10.1145/3392152", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Toronto, Toronto, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Toronto, Toronto, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Waterloo, Waterloo, Canada"}], "References": []}, {"ArticleId": 83745627, "Title": "<PERSON><PERSON>", "Abstract": "Key-value lookup engines running in fast memory are crucial components of many networked and distributed systems such as packet forwarding, virtual network functions, content distribution networks, distributed storage, and cloud/edge computing. These lookup engines must be memory-efficient because fast memory is small and expensive. This work presents a new key-value lookup design, called Ludo Hashing, which costs the least space (3.76 + 1.05l bits per key-value item for l-bit values) among known compact lookup solutions including the recently proposed partial-key Cuckoo and Bloomier perfect hashing. In addition to its space efficiency, Ludo Hashing works well with most practical systems by supporting fast lookups, fast updates, and concurrent writing/reading. We implement Ludo Hashing and evaluate it with both micro-benchmark and two network systems deployed in CloudLab. The results show that in practice Ludo Hashing saves 40% to 80%+ memory cost compared to existing dynamic solutions. It costs only a few GB memory for 1 billion key-value items and achieves high lookup throughput: over 65 million queries per second on a single node with multiple threads.", "Keywords": "", "DOI": "10.1145/3392140", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of California, Santa Cruz, Santa Cruz, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of California, Santa Cruz, Santa Cruz, CA, USA"}], "References": []}, {"ArticleId": ********, "Title": "Privacy-Utility Tradeoffs in Routing Cryptocurrency over Payment Channel Networks", "Abstract": "Payment channel networks (PCNs) are viewed as one of the most promising scalability solutions for cryptocurrencies today. Roughly, PCNs are networks where each node represents a user and each directed, weighted edge represents funds escrowed on a blockchain; these funds can be transacted only between the endpoints of the edge. Users efficiently transmit funds from node A to B by relaying them over a path connecting A to B, as long as each edge in the path contains enough balance (escrowed funds) to support the transaction. Whenever a transaction succeeds, the edge weights are updated accordingly. In deployed PCNs, channel balances (i.e., edge weights) are not revealed to users for privacy reasons; users know only the initial weights at time 0. Hence, when routing transactions, users typically first guess a path, then check if it supports the transaction. This guess-and-check process dramatically reduces the success rate of transactions. At the other extreme, knowing full channel balances can give substantial improvements in transaction success rate at the expense of privacy. In this work, we ask whether a network can reveal noisy channel balances to trade off privacy for utility. We show fundamental limits on such a tradeoff, and propose noise mechanisms that achieve the fundamental limit for a general class of graph topologies. Our results suggest that in practice, PCNs should operate either in the low-privacy or low-utility regime; it is not possible to get large gains in utility by giving up a little privacy, or large gains in privacy by sacrificing a little utility.", "Keywords": "", "DOI": "10.1145/3392147", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 3, "Name": "G<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Oh", "Affiliation": "University of Washington, Seattle, WA, USA"}], "References": []}, {"ArticleId": ********, "Title": "vrfinder", "Abstract": "Current methods to analyze the Internet's router-level topology with paths collected using traceroute assume that the source address for each router in the path is either an inbound or off-path address on each router. In this work, we show that outbound addresses are common in our Internet-wide traceroute dataset collected by CAIDA's Ark vantage points in January 2020, accounting for 1.7% - 5.8% of the addresses seen at some point before the end of a traceroute. This phenomenon can lead to mistakes in Internet topology analysis, such as inferring router ownership and identifying interdomain links. We hypothesize that the primary contributor to outbound addresses is Layer 3 Virtual Private Networks (L3VPNs), and propose vrfinder, a technique for identifying L3VPN outbound addresses in traceroute collections. We validate vrfinder against ground truth from two large research and education networks, demonstrating high precision (100.0%) and recall (82.1% - 95.3%). We also show the benefit of accounting for L3VPNs in traceroute analysis through extensions to bdrmapIT, increasing the accuracy of its router ownership inferences for L3VPN outbound addresses from 61.5% - 79.4% to 88.9% - 95.5%.", "Keywords": "", "DOI": "10.1145/3392158", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CAIDA &amp; University of California, San Diego, La Jolla, CA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Waikato, Hamilton, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CAIDA &amp; University of California, San Diego, La Jolla, CA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CAIDA &amp; University of California, San Diego, La Jolla, CA, USA"}], "References": []}, {"ArticleId": ********, "Title": "Staleness Control for Edge Data Analytics", "Abstract": "A new generation of cyber-physical systems has emerged with a large number of devices that continuously generate and consume massive amounts of data in a distributed and mobile manner. Accurate and near real-time decisions based on such streaming data are in high demand in many areas of optimization for such systems. Edge data analytics bring processing power in the proximity of data sources, reduce the network delay for data transmission, allow large-scale distributed training, and consequently help meeting real-time requirements. Nevertheless, the multiplicity of data sources leads to multiple distributed machine learning models that may suffer from sub-optimal performance due to the inconsistency in their states. In this work, we tackle the insularity, concept drift, and connectivity issues in edge data analytics to minimize its accuracy handicap without losing its timeliness benefits. To this end, we propose an efficient model synchronization mechanism for distributed and stateful data analytics. Staleness Control for Edge Data Analytics (SCEDA) ensures the high adaptability of synchronization frequency in the face of an unpredictable environment by addressing the trade-off between the generality and timeliness of the model. Making use of online reinforcement learning, SCEDA has low computational overhead, automatically adapts to changes, and does not require additional data monitoring.", "Keywords": "", "DOI": "10.1145/3392156", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vienna University of Technology, Vienna, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Ottawa, Ottawa, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vienna University of Technology, Vienna, Austria"}], "References": []}, {"ArticleId": 83745636, "Title": "Characterizing Policies with Optimal Response Time Tails under Heavy-Tailed Job Sizes", "Abstract": "We consider the tail behavior of the response time distribution in an M/G/1 queue with heavy-tailed job sizes, specifically those with intermediately regularly varying tails. In this setting, the response time tail of many individual policies has been characterized, and it is known that policies such as Shortest Remaining Processing Time (SRPT) and Foreground-Background (FB) have response time tails of the same order as the job size tail, and thus such policies are tail-optimal. Our goal in this work is to move beyond individual policies and characterize the set of policies that are tail-optimal. Toward that end, we use the recently introduced SOAP framework to derive sufficient conditions on the form of prioritization used by a scheduling policy that ensure the policy is tail-optimal. These conditions are general and lead to new results for important policies that have previously resisted analysis, including the Gittins policy, which minimizes mean response time among policies that do not have access to job size information. As a by-product of our analysis, we derive a general upper bound for fractional moments of M/G/1 busy periods, which is of independent interest.", "Keywords": "", "DOI": "10.1145/3392148", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Amsterdam, Amsterdam, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, Eindhoven, Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Amsterdam, Amsterdam, Netherlands"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "California Institute of Technology, Pasadena, CA, USA"}], "References": []}, {"ArticleId": 83745637, "Title": "On Time Synchronization Issues in Time-Sensitive Networks with Regulators and Nonideal Clocks", "Abstract": "Flow reshaping is used in time-sensitive networks (as in the context of IEEE TSN and IETF Detnet) in order to reduce burstiness inside the network and to support the computation of guaranteed latency bounds. This is performed using per-flow regulators (such as the Token Bucket Filter) or interleaved regulators (as with IEEE TSN Asynchronous Traffic Shaping, ATS). The former use one FIFO queue per flow, whereas the latter use one FIFO queue per input port. Both types of regulators are beneficial as they cancel the increase of burstiness due to multiplexing inside the network. It was demonstrated, by using network calculus, that they do not increase the worst-case latency. However, the properties of regulators were established assuming that time is perfect in all network nodes. In reality, nodes use local, imperfect clocks. Time-sensitive networks exist in two flavours: (1) in non-synchronized networks, local clocks run independently at every node and their deviations are not controlled and (2) in synchronized networks, the deviations of local clocks are kept within very small bounds using for example a synchronization protocol (such as PTP) or a satellite based geo-positioning system (such as GPS). We revisit the properties of regulators in both cases. In non-synchronized networks, we show that ignoring the timing inaccuracies can lead to network instability due to unbounded delay in per-flow or interleaved regulators. We propose and analyze two methods (rate and burst cascade, and asynchronous dual arrival-curve method) for avoiding this problem. In synchronized networks, we show that there is no instability with per-flow regulators but, surprisingly, interleaved regulators can lead to instability. To establish these results, we develop a new framework that captures industrial requirements on clocks in both non-synchronized and synchronized networks, and we develop a toolbox that extends network calculus to account for clock imperfections.", "Keywords": "", "DOI": "10.1145/3392145", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne, Lausanne, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne, Lausanne, Switzerland"}], "References": []}, {"ArticleId": ********, "Title": "Fundamental Limits on the Regret of Online Network-Caching", "Abstract": "Optimal caching of files in a content distribution network (CDN) is a problem of fundamental and growing commercial interest. Although many different caching algorithms are in use today, the fundamental performance limits of network caching algorithms from an online learning point-of-view remain poorly understood to date. In this paper, we resolve this question in the following two settings: (1) a single user connected to a single cache, and (2) a set of users and a set of caches interconnected through a bipartite network. Recently, an online gradient-based coded caching policy was shown to enjoy sub-linear regret. However, due to the lack of known regret lower bounds, the question of the optimality of the proposed policy was left open. In this paper, we settle this question by deriving tight non-asymptotic regret lower bounds in both of the above settings. In addition to that, we propose a new Follow-the-Perturbed-Leader-based uncoded caching policy with near-optimal regret. Technically, the lower-bounds are obtained by relating the online caching problem to the classic probabilistic paradigm of balls-into-bins. Our proofs make extensive use of a new result on the expected load in the most populated half of the bins, which might also be of independent interest. We evaluate the performance of the caching policies by experimenting with the popular MovieLens dataset and conclude the paper with design recommendations and a list of open problems.", "Keywords": "", "DOI": "10.1145/3392143", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Madras, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Madras, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Madras, Chennai, India"}], "References": []}, {"ArticleId": 83745639, "Title": "Stability and Scalability of Blockchain Systems", "Abstract": "The blockchain paradigm provides a mechanism for content dissemination and distributed consensus on Peer-to-Peer (P2P) networks. While this paradigm has been widely adopted in industry, it has not been carefully analyzed in terms of its network scaling with respect to the number of peers. Applications for blockchain systems, such as cryptocurrencies and IoT, require this form of network scaling. In this paper, we propose a new stochastic network model for a blockchain system. We identify a structural property called one-endedness, which we show to be desirable in any blockchain system as it is directly related to distributed consensus among the peers. We show that the stochastic stability of the network is sufficient for the one-endedness of a blockchain. We further establish that our model belongs to a class of network models, called monotone separable models. This allows us to establish upper and lower bounds on the stability region. The bounds on stability depend on the connectivity of the P2P network through its conductance and allow us to analyze the scalability of blockchain systems on large P2P networks. We verify our theoretical insights using both synthetic data and real data from the Bitcoin network.", "Keywords": "", "DOI": "10.1145/3392153", "PubYear": 2020, "Volume": "4", "Issue": "2", "JournalId": 41996, "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems", "ISSN": "", "EISSN": "2476-1249", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Texas at Austin, Austin, TX, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of California, Berkeley, Berkeley, CA, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nokia Bell Labs, Murray Hill, NJ, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Texas at Austin, Austin, TX, USA"}], "References": []}, {"ArticleId": 83745673, "Title": "Model-Based Sensitivity Analysis of Flapping Wing Driveline Parameters", "Abstract": "", "Keywords": "Flapping Frequency; Sensitivity Analysis; Aerodynamic Damping; Mass Spring Damper Model; Mathematical Models; Lift Coefficient; Wing Configurations; Wing Root; Alternating Current; Center of Pressure", "DOI": "10.2514/1.G004714", "PubYear": 2020, "Volume": "43", "Issue": "8", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Catholic University of Leuven, 3001 Leuven, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Catholic University of Leuven, 3001 Leuven, Belgium"}], "References": []}, {"ArticleId": 83745778, "Title": "Cost-oriented robotic assembly line balancing problem with setup times: multi-objective algorithms", "Abstract": "<p>Robots are extensively used during the era of Industry 4.0 to achieve high productivity, better quality and lower cost. While designing a robotic assembly line, production managers are concerned about the cost involved in such a system development. Most of the research reported till date did not consider purchasing cost while optimizing the design of a robotic assembly line. This study presents the first attempt to study the cost-oriented robotic assembly line balancing problem with setup times to minimize the cycle time and total purchasing cost simultaneously. A mixed-integer linear programming model is developed to formulate this problem. The elitist non-dominated sorting genetic algorithm (NSGA-II) and improved multi-objective artificial bee colony (IMABC) algorithm are developed to achieve a set of Pareto solutions for the production managers to utilize for selecting the better design solution. The proposed IMABC develops new employed bee phase and scout phase, which selects one solution in the permanent Pareto archive to replace the abandoned solution, to enhance exploration and exploitation. The comparative study on a set of generated instances demonstrates that the proposed model is capable of achieving the proper tradeoff between line efficiency and purchasing cost, and the proposed NSGA-II and IMABC achieve competing performance in comparison with several other multi-objective algorithms.</p>", "Keywords": "Assembly line balancing; Robotic assembly line; Setup times; Multi-objective optimization; Metaheuristics", "DOI": "10.1007/s10845-020-01598-7", "PubYear": 2021, "Volume": "32", "Issue": "4", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Li", "Affiliation": "Key Laboratory of Metallurgical Equipment and Control Technology, Wuhan University of Science and Technology, Wuhan, China;Engineering Research Center for Metallurgical Automation and Measurement Technology of Ministry of Education, Wuhan University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, University of Leicester, Leicester, UK"}, {"AuthorId": 3, "Name": "S. G. <PERSON>", "Affiliation": "School of Mechanical Engineering, VIT University, Vellore, India"}], "References": [{"Title": "Solving a new cost-oriented assembly line balancing problem by classical and hybrid meta-heuristic algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "12", "Page": "8217", "JournalTitle": "Neural Computing and Applications"}]}]