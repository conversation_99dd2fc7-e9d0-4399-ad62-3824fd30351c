[{"ArticleId": 87883939, "Title": "A Perfect Storm", "Abstract": "<p> In an age where news information is created by millions and consumed by billions over social media ( SM ) every day, issues of information biases, fake news, and echo-chambers have dominated the corridors of technology firms, news corporations, policy makers, and society. While multiple disciplines have tried to tackle the issue using their disciplinary lenses, there has, hitherto, been no integrative model that surface the intricate, albeit “dark” explainable AI confluence of both technology and psychology. Investigating information bias anchoring as the overarching phenomenon, this research proposes a theoretical framework that brings together traditionally fragmented domains of AI technology, and human psychology. </p><p>The proposed Information Bias Anchoring Model reveals how SM news information creates an information deluge leading to uncertainty, and how technological rationality and individual biases intersect to mitigate the uncertainty, often leading to news information biases. The research ends with a discussion of contributions and offering to reduce information bias anchoring.</p>", "Keywords": "", "DOI": "10.1145/3428157", "PubYear": 2021, "Volume": "2", "Issue": "2", "JournalId": 74064, "JournalTitle": "Digital Threats: Research and Practice", "ISSN": "2576-5337", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kent State University, Ohio"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Kent State University, Ohio"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Miami University, Ohio"}], "References": [{"Title": "Fake News Early Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "2", "Page": "1", "JournalTitle": "Digital Threats: Research and Practice"}]}, {"ArticleId": 87883940, "Title": "A Famine of Riches in Scholarly Publication", "Abstract": "<p>I have been editing for a while. Long enough to have witnessed the evolution from pre-digital journals to the post-digital pure play. I negotiated and obtained the first online review system for this fine journal, back in 2006 when I was building my first proposal to edit The DATA BASE for Advances in Information Systems. I felt at the time that I might be a tad late coming to the digital field, seeing that the association I belonged to had already launched two digital pure-play publications which appeared to be destined for notice.</p>", "Keywords": "", "DOI": "10.1145/3462766.3462768", "PubYear": 2021, "Volume": "52", "Issue": "2", "JournalId": 16781, "JournalTitle": "ACM SIGMIS Database", "ISSN": "0095-0033", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Louisiana Tech University"}], "References": []}, {"ArticleId": 87883944, "Title": "The Philosopher's Corner", "Abstract": "<p>In an effort to contribute to the recent debate around epistemological and methodological anarchism inspired by the thinking of <PERSON>, we reflect on <PERSON><PERSON><PERSON>'s pragmatist perspective of social science. We argue that the information systems field instantiates a sort of pluralism that goes beyond the relativistic conclusions of <PERSON><PERSON><PERSON><PERSON>. This is evident through the different traditions of research into business processes and organizational routines. There is a healthy diversity of epistemological and methodological approaches in this research. Accompanying this diversity is an openness to novelty and change. Yet, at the same time, this does not necessitate the abandonment of rigor and a cumulative tradition implied by \"anything goes.\" Anything does not go, and that's a good thing. There is not a singular, hegemonic approach to what constitutes strong information systems research, but neither have we devolved into anarchy.</p>", "Keywords": "business process; feyerabend; information systems research; organizational routines; pluralism; pragmatism", "DOI": "10.1145/3462766.3462773", "PubYear": 2021, "Volume": "52", "Issue": "2", "JournalId": 16781, "JournalTitle": "ACM SIGMIS Database", "ISSN": "0095-0033", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Wirtschaftsuniversität Wien"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Notre Dame University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Liechtenstein"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Liechtenstein"}], "References": []}, {"ArticleId": 87884035, "Title": "Learning State Discrimination of Classroom Students Based on Lightweight Network", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2021.114121", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "秋会 刘", "Affiliation": ""}], "References": []}, {"ArticleId": 87884223, "Title": "Digital Health Literacy", "Abstract": "<p>The pandemic has shown the importance of health organizations adapting rapidly to teleconsultation services, investing in e-health with quality criteria and monitoring outcomes. Through a literature review and gathering research already carried out on e-health communication and with practical examples it is verified that, if the requirements of proximity, quality and interpersonal relationship are met, better health results can be obtained. When communication is established in health via mobile phone, with image, sound, voice, text, it is thus possible to work the memory and health instructions of patients and obtain better health outcomes. These strategies must be personalized and adapted to the patient's age and context.</p>", "Keywords": "", "DOI": "10.4018/IJMDWTFE.20210101oa01", "PubYear": 2021, "Volume": "11", "Issue": "1", "JournalId": 67957, "JournalTitle": "International Journal of Mobile Devices, Wearable Technology, and Flexible Electronics", "ISSN": "2640-4249", "EISSN": "2640-4257", "Authors": [], "References": []}, {"ArticleId": 87884230, "Title": "A Review of Occlusion as a Tool to Assess Attentional Demand in Driving", "Abstract": "Objective <p>The aim of this review is to identify how visual occlusion contributes to our understanding of attentional demand and spare visual capacity in driving and the strengths and limitations of the method.</p> Background <p>The occlusion technique was developed by <PERSON> to evaluate the attentional demand of driving. Despite its utility, it has been used infrequently in driver attention/inattention research.</p> Method <p>Visual occlusion studies in driving published between 1967 and 2020 were reviewed. The focus was on original studies in which the forward visual field was intermittently occluded while the participant was driving.</p> Results <p>Occlusion studies have shown that attentional demand varies across situations and drivers and have indicated environmental, situational, and inter-individual factors behind the variability. The occlusion technique complements eye tracking in being able to indicate the temporal requirements for and redundancy in visual information sampling. The proper selection of occlusion settings depends on the target of the research.</p> Conclusion <p>Although there are a number of occlusion studies looking at various aspects of attentional demand, we are still only beginning to understand how these demands vary, interact, and covary in naturalistic driving.</p> Application <p>The findings of this review have methodological and theoretical implications for human factors research and for the development of distraction monitoring and in-vehicle system testing. Distraction detection algorithms and testing guidelines should consider the variability in drivers’ situational and individual spare visual capacity.</p>", "Keywords": "minimum required attention;peripheral vision;self-paced;system-paced;visual demand", "DOI": "10.1177/00187208211010953", "PubYear": 2023, "Volume": "65", "Issue": "5", "JournalId": 3589, "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society", "ISSN": "0018-7208", "EISSN": "1547-8181", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Jyväskylä, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Swedish National Road and Transport Research Institute, Linköping, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Swedish National Road and Transport Research Institute, Linköping, Sweden"}], "References": [{"Title": "On the Difference Between Necessary and Unnecessary Glances Away From the Forward Roadway: An Occlusion Study on the Motorway", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "7", "Page": "1117", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Attentional Demand as a Function of Contextual Factors in Different Traffic Scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "7", "Page": "1171", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}]}, {"ArticleId": 87884415, "Title": "Tuning of Proportional-Resonant Controllers Combined with Phase-Lead Compensators Based on the Frequency Response", "Abstract": "<p>In this research article, the proportional-resonant controllers (PR) are introduced to fulfil the increased demand for controllers aiming at the tracking and/or rejection of periodic signals with superior performance than conventional PID ones. The main characteristic of these controllers is the infinite gain in the frequency of interest, which can lead to stability problems and makes their design and tuning more complex. More precisely, this paper presents a systematic approach for the computation of PR controllers parameters based on a frequency response method, using stability margins and sensitivity functions as indicators of performance and stability for the controlled system. In addition, a combination with a phase-lead compensator is proposed to allow better performance in an augmented bandwidth. Finally, the effectiveness of the proposed method is demonstrated for different classes of processes found in typical control problems.</p>", "Keywords": "Resonant controllers; Frequency response; Periodic signals; Proportional-resonant controllers", "DOI": "10.1007/s40313-021-00728-7", "PubYear": 2021, "Volume": "32", "Issue": "4", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Programa de Pós-graduação em Engenharia Elétrica, Universidade Federal do Rio Grande do Sul, Porto Alegre, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Programa de Pós-graduação em Engenharia Elétrica, Universidade Federal do Rio Grande do Sul, Porto Alegre, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Programa de Pós-graduação em Engenharia Elétrica, Universidade Federal do Rio Grande do Sul, Porto Alegre, Brazil"}], "References": []}, {"ArticleId": 87884439, "Title": "Why I give money to unknown people? An investigation of online donation and forwarding intention", "Abstract": "Online donation contributes significantly to marginalized and vulnerable people because it is helpful for beneficiaries or donation requesters to receive attention and finance funding from a large number of people. Drawing on social learning theory and trust transfer theory, this article proposes a model to investigate the effects of different factors on online donation intention and forwarding intention. Based on data collected from 266 responses, this study uses the structural equation modeling technique to test the research model. The results show that different antecedents impact online donation variously. Trust in online donation platform, peer influence, and enjoyment in helping others are positively related to online donation intention. Tie strength and enjoyment in helping others have direct and significant effects on forwarding intention. This research contributes to IS research by investigating different antecedents in the context of online donation and including the consideration of forwarding intention of the online donation request.", "Keywords": "Online donation intention ; Forwarding intention ; Social influence ; Trust", "DOI": "10.1016/j.elerap.2021.101055", "PubYear": 2021, "Volume": "47", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "Tingting Hou", "Affiliation": "School of Information Technology and Management, University of International Business and Economics, Beijing, China;Corresponding author at: School of Information Technology and Management, University of International Business and Economics, 10 Huixindongjie, Chaoyang District, Beijing 100029, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Health Sciences, Xinhua College of Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Shangqiu Normal University, Shangqiu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> (<PERSON>) <PERSON><PERSON>", "Affiliation": "Anderson School of Management, The University of New Mexico, Albuquerque, United States"}], "References": [{"Title": "Why do people patronize donation-based crowdfunding platforms? An activity perspective of critical success factors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "106470", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 87884543, "Title": "Points of Interest recommendations: Methods, evaluation, and future directions", "Abstract": "The emergence of Location-based social networks (LBSNs) in recent years has boosted improvements in Recommender Systems for a new and specific task: the recommendation of points-of-interest (POI). Despite all the blunt advances recently observed, the area still lacks an updated and consolidated view about the main limitations, common assumptions, and directions we are following as a community. Thus, this paper aims to provide an updated picture of POI recommendation, identifying relevant efforts, results, contributions, and limitations. In this sense, through a systematic mapping, we selected 74 relevant papers published in the last three years (2017, 2018, and 2019) in the main conferences and journals of the area. Most of these studies have focused on the general POI recommendation problem, although we still could identify a significant number of efforts addressing specific problems in the area, such as Time-Aware, Next POI, and In/Out-of-Town Recommendations. Also, we found user contexts, such as social network and geolocation, as recurring data types used to improve preference elicitation when exploring different methods (e.g., collaborative filtering, factorization, probabilistic, link-based, and hybrid methods). As major limitations, first, we identified that these studies prioritize accuracy over other quality dimensions, despite the consensus in the RS community that accuracy alone is not enough to assess the practical effectiveness of Recommender Systems. Further, we found a low intersection of metrics and datasets used to evaluate the proposed solutions, along with a large number of metrics used in a few distinct studies. These observations point out the potential damage to reproducibility and straightforward comparison of results in the area, motivating us to propose an extensible POI recommendation benchmark. Through this benchmark, we showed that when proper evaluations are carried out, by considering different datasets, metrics, and baselines, identifying which algorithm is the best one becomes non-trivial. Finally, we highlight as a promising future work the in-depth exploitation of textual data since just a few evaluated studies marginally use this rich data source.", "Keywords": "POI recommendation systems ; Survey ; Systematic mapping", "DOI": "10.1016/j.is.2021.101789", "PubYear": 2021, "Volume": "101", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal de São João del-Rei, São João del-Rei, Brazil"}, {"AuthorId": 2, "Name": "Nícollas Silva", "Affiliation": "Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal de São João del-Rei, São João del-Rei, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Belo Horizonte, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Seek, Belo Horizonte, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Universidade Federal de São João del-Rei, São João del-Rei, Brazil;Corresponding author"}], "References": [{"Title": "Geographic Diversification of Recommended POIs in Frequently Visited Areas", "Authors": "<PERSON><PERSON><PERSON> Han; <PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": 87884569, "Title": "Digital hair removal by deep learning for skin lesion segmentation", "Abstract": "Occlusion due to hair in dermoscopic images affects the diagnostic operation and the accuracy of its analysis of a skin lesion. Also, dermis hair has the following different characteristics: thin; overlapping; faded; of similar contrast or colour to the underlying skin; and obscuring/covering textured lesions. These make digital hair removal (DHR), which involves hair segmentation and hair gap inpainting, a challenging task. Thus, traditional hard-coded threshold-based hair removal methods are not effective, resulting in over-removal which loses important information of the skin lesion, or under-removal which cannot remove the hair effectively. In this paper, we propose a deep learning approach to DHR based on U-Net and a free-form image inpainting architecture. In hair segmentation, a well-labelled dataset is created and used to train U-Net in order to obtain accurate hair masks. In inpainting, a free-form image inpainting architecture (i.e., Gated convolution and SN-PatchGAN) which has been trained on millions of images is used to inpaint any hair gaps. We also propose an evaluation method to analyze the effect of hair removal based on a single dermoscopic image, named intra structural similarity (Intra-SSIM). The process of DHR is repeated until there is no change in the average value of Intra-SSIM. Using the ISIC 2018 dataset, the performance of the proposed method is shown to be better than other state-of-the-art methods.", "Keywords": "Dermoscopy ; Digital hair removal ; Skin lesion segmentation ; Deep learning", "DOI": "10.1016/j.patcog.2021.107994", "PubYear": 2021, "Volume": "117", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Digital Signal and Image Processing of Guangdong Province, Department of Electronic Engineering,College of Engineering, Shantou University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Digital Signal and Image Processing of Guangdong Province, Department of Electronic Engineering,College of Engineering, Shantou University, China;Corresponding author"}, {"AuthorId": 3, "Name": "Ta<PERSON>", "Affiliation": "School of Engineering, University of Warwick, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Digital Signal and Image Processing of Guangdong Province, Department of Electronic Engineering,College of Engineering, Shantou University, China"}], "References": [{"Title": "Contextual deconvolution network for semantic segmentation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "107152", "JournalTitle": "Pattern Recognition"}, {"Title": "U2-Net: Going deeper with nested U-structure for salient object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "107404", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 87884576, "Title": "A leakage compensated charge control driving circuit with sensor feedback for a comb drive actuator", "Abstract": "We present the design of a novel driver for a comb drive actuator. This actuator is required to have both a large displacement of 200 μ m and a high resolution of 50 nm. Traditionally, electromechanical MEMS actuation is done under voltage control. In this paper, we want to examine a charge controlled driver, as it might provide some advantages. A comb drive under charge control exhibits a longitudinal displacement proportional to Q 2 / 3 , rather than V 2 in case of voltage control. This more linear behavior might help to increase the achievable displacement resolution. It can also be shown that the stability range of an actuator under voltage control, for any type of actuator, will never be larger than the range of that same actuator under charge control. However, for a well-designed and well-fabricated comb drive actuator, where this instability is expressed in lateral pull-in, this effect is minimal. In this paper, we first create an electromechanical model of a comb drive, to be used in simulation of our driver. The created model is based on a previously designed comb drive with a displacement of 200 μ m at 91.6 V, or 138.1 pC. We then focus on the driver itself, minimizing the effects of leakage currents, as these are detrimental for a good charge controlled actuation. As the actuator will be driven dynamically, we cannot rely on the estimated charge on the comb drive to determine the immediate displacement. A sensor is needed that measures the position of the comb drive. Four different topologies of sensors are described, each of which uses the comb drive itself to estimate its position. A full electromechanical simulation is performed and a comparison is made between the different sensor topologies, evaluating their ability to determine the exact displacement. One of the proposed sensor topologies achieved the 50 nm resolution.", "Keywords": "Comb drive actuator ; Charge control ; MEMS driver ; Sensor feedback ; Electromechanical simulation", "DOI": "10.1016/j.sna.2021.112799", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CMST – ELIS, imec and Ghent University, Tech Lane Ghent Science Park – Campus A 126, B-9032 Ghent, Belgium;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "DIGCOM – TELIN, Ghent University, Sint-Pietersnieuwstraat 41, B-9000 Ghent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CMST – ELIS, imec and Ghent University, Tech Lane Ghent Science Park – Campus A 126, B-9032 Ghent, Belgium"}], "References": []}, {"ArticleId": 87884580, "Title": "Stiffness modulation of a cable-driven leg exoskeleton for effective human–robot interaction", "Abstract": "<p>With the widespread development of leg exoskeletons to provide external force-based repetitive training for gait rehabilitation, the prospect of undesired movement adaptation due to applied forces and imposed constraints require adequate investigation. A cable-driven leg exoskeleton, CDLE, presents a lightweight, flexible, and redundantly actuated architecture that enables the possibility of system parameters modulation to alter human–robot interaction while applying the desired forces. In this work, multi-joint stiffness performance of CDLE is formulated to systematically analyze human–CDLE interaction. Further, potential alterations in CDLE architecture are presented to tune human–CDLE interaction that favors the desired human leg movement during a gait rehabilitation paradigm.</p>", "Keywords": "Human–Robot interaction;Gait rehabilitation;Exoskeleton;Cable-driven robots;Stiffness modulation", "DOI": "10.1017/S0263574721000242", "PubYear": 2021, "Volume": "39", "Issue": "12", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "N. S. <PERSON><PERSON>", "Affiliation": "Human Centered Robotics Lab, IIT Gandhinagar, Ahmedabad  India "}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Human Centered Robotics Lab, IIT Gandhinagar, Ahmedabad  India "}], "References": []}, {"ArticleId": 87884740, "Title": "Demonstrating Interoperability Between Unmanned Ground Systems and Command and Control Systems", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIDSS.2021.10037444", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 31821, "JournalTitle": "International Journal of Intelligent Defence Support Systems", "ISSN": "1755-1587", "EISSN": "1755-1595", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Rafal <PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87884750, "Title": "Design approach to stabilizing controller in state space for non-driven micromechanical gyro", "Abstract": "The gyro is a new silicon micromechanical gyro, which has no a driving structure but only a sensing structure. The non-driven gyro is installed on a rotating carrier and utilizes the spinning of the carrier to obtain an angular momentum. When the carrier produces a transverse rotation, the sensing mass of the gyro is acted by Co<PERSON>lis force to sense the input angular velocity. In applications, we found that the shock causes the Si pendulum to go out of steady state, and to fail to work. So, the stability is a key problem. In this paper, we put forward a feasible way to overcome this problem. The method uses modeling in state space to construct a state feedback controller. We firstly introduce the working mechanism of the gyro and obtain the dynamic equation. Then we use Ric<PERSON>i equation to design a steady controller that has a ‘sandwich’ structure. For the stabilizing controller, we calculate the anti-torsion stillness coefficient and analyze the damping. Finally, some shock tests had been done. The theoretical simulation and experimental test shown that the designed stabilizing controller makes the gyro reach an asymptotic steady state in a short time.", "Keywords": "Si micromechanical gyro ; Stabilizing controller ; Stillness coefficient ; Damping", "DOI": "10.1016/j.sna.2021.112798", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer & Information Management, Inner Mongolia University of Finance and Economics, Hohhot, 010070, China;Sensing Technique Research Center, Beijing Information Science and Technology University, Beijing, 100101, China;Corresponding author at: School of Computer & Information Management, Inner Mongolia University of Finance and Economics, Hohhot, 010070, China;@imufe.edu.cn"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer & Information Management, Inner Mongolia University of Finance and Economics, Hohhot, 010070, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer & Information Management, Inner Mongolia University of Finance and Economics, Hohhot, 010070, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer & Information Management, Inner Mongolia University of Finance and Economics, Hohhot, 010070, China"}], "References": []}, {"ArticleId": 87884782, "Title": "A QoS Prediction Approach Based on Fusion of Network Representation Learning and Dynamic Collaborative Filtering for Cloud Service", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIITC.2021.10037446", "PubYear": 2021, "Volume": "1", "Issue": "3", "JournalId": 65533, "JournalTitle": "International Journal of Intelligent Internet of Things Computing", "ISSN": "2631-7060", "EISSN": "2631-7079", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Guoqing Fan", "Affiliation": ""}], "References": []}, {"ArticleId": 87884864, "Title": "A Low Power Architecture for 1D Median Filter using Carry Look Ahead adder", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2021.10037415", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87884899, "Title": "Real-time Issues in the Ada Parallel Model with OpenMP", "Abstract": "<p>The current proposal for the next revision of the Ada language considers the possibility to map the language parallel features to an underlying OpenMP runtime. As previously presented, and discussed in previous workshops, the works on fine-grain parallelism in Ada map well to the OpenMP tasking model for parallelism. Nevertheless, and although the general model of integration, and the semantic constructs are already reflected in the proposed revision of the standard, the integration of these new features with the Real-Time Systems Annex of Ada is still not complete. This paper presents an overview of what is supported and the still open issues.</p>", "Keywords": "", "DOI": "10.1145/3463478.3463491", "PubYear": 2021, "Volume": "40", "Issue": "2", "JournalId": 12096, "JournalTitle": "ACM SIGAda Ada Letters", "ISSN": "1094-3641", "EISSN": "1557-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Polytechnic Institute of Porto, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Barcelona Supercomputing Center, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Barcelona Supercomputing Center, Spain"}], "References": []}, {"ArticleId": 87884968, "Title": "Improved simultaneous localization and mapping algorithm combined with semantic segmentation model", "Abstract": "<p>In the past decades, emerging technologies such as unmanned driving and indoor navigation have developed rapidly, and simultaneous localization and mapping has played unparalleled roles as core technologies. However, dynamic objects in complex environments will affect the positioning accuracy. In order to reduce the influence of dynamic objects, this article proposes an improved simultaneous localization and mapping algorithm combined with semantic segmentation model. First, in the pre-processing stage, in order to reduce the influence of dynamic features, fully convolutional network model is used to find the dynamic object, and then the output image is masked and fused to obtain the final image without dynamic object features. Second, in the feature-processing stage, three parts are improved to reduce the computing complexity, which are extracting, matching, and eliminating mismatching feature points. Experiments show that the absolute trajectory accuracy in high dynamic scene is improved by 48.58% on average. Meanwhile, the average processing time is also reduced by 21.84%.</p>", "Keywords": "", "DOI": "10.1177/15501477211014131", "PubYear": 2021, "Volume": "17", "Issue": "4", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Oceanography and Space Informatics, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 4, "Name": "Shibao Li", "Affiliation": "College of Oceanography and Space Informatics, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, China University of Petroleum (East China), Qingdao, China"}, {"AuthorId": 6, "Name": "Haihua Chen", "Affiliation": "College of Oceanography and Space Informatics, China University of Petroleum (East China), Qingdao, China"}], "References": []}, {"ArticleId": 87885170, "Title": "Pharmacogenetics of tenofovir and emtricitabine penetration into cerebrospinal fluid", "Abstract": "<p>Background: Blood-cerebrospinal fluid (CSF) barrier transporters affect the influx and efflux of drugs. The antiretrovirals tenofovir and emtricitabine may be substrates of blood-brain barrier (BBB) and blood-CSF barrier transporters, but data are limited regarding the pharmacogenetics and pharmacokinetics of their central nervous system (CNS) penetration.Objectives: We investigated genetic polymorphisms associated with CSF disposition of tenofovir and emtricitabine.Method: We collected paired plasma and CSF samples from 47 HIV-positive black South African adults who were virologically suppressed on efavirenz, tenofovir and emtricitabine. We considered 1846 single-nucleotide polymorphisms from seven relevant transporter genes (ABCC5, ABCG2, ABCB1, SLCO2B1, SCLO1A2, SLCO1B1 and ABCC4) and 782 met a linkage disequilibrium (LD)-pruning threshold.Results: The geometric mean (95% confidence interval [CI]) values for tenofovir and emtricitabine CSF-to-plasma concentration ratios were 0.023 (0.021–0.026) and 0.528 (0.460–0.605), respectively. In linear regression models, the lowest p-value for association with the tenofovir CSF-to-plasma ratio was ABCB1 rs1989830 (p = 1.2 × 10−3) and for emtricitabine, it was ABCC5 rs11921035 (p = 1.4 × 10−3). None withstood correction for multiple testing.Conclusion: No genetic polymorphisms were associated with plasma, CSF concentrations or CSF-to-plasma ratios for either tenofovir or emtricitabine.</p>", "Keywords": "cerebrospinal fluid;emtricitabine;pharmacogenetics;pharmacokinetics;tenofovir", "DOI": "10.4102/sajhivmed.v22i1.1206", "PubYear": 2021, "Volume": "22", "Issue": "1", "JournalId": 16322, "JournalTitle": "Southern African Journal of HIV Medicine", "ISSN": "1608-9693", "EISSN": "2078-6751", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Clinical Pharmacology, Department of Medicine, Faculty of Medicine and Health Sciences, Stellenbosch University, Cape Town, South Africa. ;Division of Clinical Pharmacology, Department of Medicine, Faculty of Health Sciences, University of Cape Town, Cape Town, South Africa."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Clinical Pharmacology, Department of Medicine, Faculty of Health Sciences, University of Cape Town, Cape Town, South Africa."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Clinical Pharmacology, Department of Medicine, Faculty of Health Sciences, University of Cape Town, Cape Town, South Africa."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Psychiatry and Mental Health, Health Sciences, Groote Schuur Hospital, University of Cape Town, Cape Town, South Africa."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Medicine, Vanderbilt University Medical Center, Vanderbilt University, Nashville, Tennessee, United States. ;Department of Internal Medicine, Meharry Medical College, Nashville, Tennessee, United States."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Division of Clinical Pharmacology, Department of Medicine, Faculty of Health Sciences, University of Cape Town, Cape Town, South Africa."}], "References": []}, {"ArticleId": 87885193, "Title": "Multilevel analysis of factors affecting open-access institutional repository implementation in Nigerian universities", "Abstract": "Purpose The study aims to identify novel open-access institutional repository (OAIR) implementation barriers and explain how they evolve. It also aims to extend theoretical insights into the information technology (IT) implementation literature. Design/methodology/approach The study adopted the interpretive philosophy, the inductive research approach and qualitative case study research method. Three Nigerian universities served as the case research contexts. The unstructured in-depth interview and the participatory observation were adopted as the data collection instruments. The qualitative data collected were analysed using thematic data analysis technique. Findings Findings show that IR implementation barriers evolved from global, organisational and individual implementation levels in the research contexts. Results specifically reveal how easy access to ideas and information and easy movement of people across international boundaries constituted globalisation trend-driven OAIR implementation barriers given their influence on OAIR implementation activities at the organisational and individual implementation levels. The two factors led to overambitious craving for information technology (IT) implementation and inadequate OAIR implementation success factors at the organisational level in the research contexts. They also led to conflicting IR implementation ideas and information at the individual level in the research contexts. Research limitations/implications The primary limitation of the research is the adoption of qualitative case study research method which makes its findings not generalisable. The study comprised only three Nigerian universities. However, the study provides plausible insights that explain how OAIR implementation barriers emanate at the organisational and individual levels due to two globalisation trends: easy access to ideas and information and easy movement of people across international boundaries. Practical implications The study points out the need for OAIR implementers to assess how easy access to information and ideas and easy movement of people across international boundaries influence the evolution of conflicting OAIR implementation ideas and information at the individual level, and overambitious craving for IT implementation and setting inadequate OAIR implementation success factors at the organisational level. The study extends views in past studies that propose that OAIR implementation barriers only emanate at organisational and individual levels, that is, only within universities involved in OAIR implementation and among individuals working in the universities. Social implications The study argues that OAIR implementation consists of three implementation levels: individual, organisational and global. It provides stakeholders with the information that there is a third OAIR implementation level. Originality/value Data validity, sample validity and novel findings are the hallmarks of the study's originality. Study data consist of first-hand experiences and information derived during participatory observation and in-depth interviews with research participants. The participants were purposively selected, given their participation in OAIR implementation in the research contexts. Study findings on the connections among global, organisational and individual OAIR implementation levels and how their relationships lead to OAIR implementation barriers are novel.", "Keywords": "Open-access institutional repository;IR implementation levels;IR implementation barriers;Open-access initiative;Nigeria", "DOI": "10.1108/OIR-10-2018-0319", "PubYear": 2021, "Volume": "45", "Issue": "7", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, School of Information Technology and Computing , American University of Nigeria , Yola, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, University of Cape Town , Cape Town, South Africa Institution of Innovation and Technology Management , Ryerson University , Toronto, Canada"}], "References": []}, {"ArticleId": 87885455, "Title": "Care transitions in healthcare: The ‘high-hanging fruit’", "Abstract": "", "Keywords": "Care transitions;Healthcare;Quality improvement;Special edition", "DOI": "10.1016/j.apergo.2021.103437", "PubYear": 2021, "Volume": "95", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Design and Creative Arts, Loughborough University, UK. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Design and Creative Arts, Loughborough University, UK."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Design and Creative Arts, Loughborough University, UK."}], "References": []}, {"ArticleId": 87885509, "Title": "Integrated Multi-Model Face Shape and Eye Attributes Identification for Hair Style and Eyelashes Recommendation", "Abstract": "<p>Identifying human face shape and eye attributes is the first and most vital process before applying for the right hairstyle and eyelashes extension. The aim of this research work includes the development of a decision support program to constitute an aid system that analyses eye and face features automatically based on the image taken from a user. The system suggests a suitable recommendation of eyelashes type and hairstyle based on the automatic reported users’ eye and face features. To achieve the aim, we develop a multi-model system comprising three separate models; each model targeted a different task, including; face shape classification, eye attribute identification and gender detection model. Face shape classification system has been designed based on the development of a hybrid framework of handcrafting and learned feature. Eye attributes have been identified by exploiting the geometrical eye measurements using the detected eye landmarks. Gender identification system has been realised and designed by implementing a deep learning-based approach. The outputs of three developed models are merged to design a decision support system for haircut and eyelash extension recommendation. The obtained detection results demonstrate that the proposed method effectively identifies the face shape and eye attributes. Developing such computer-aided systems is suitable and beneficial for the user and would be beneficial to the beauty industrial.</p>", "Keywords": "cosmetic; deep learning; facial image; decision support system; eyelash extension; haircut recommendation; convolutional neural networks cosmetic ; deep learning ; facial image ; decision support system ; eyelash extension ; haircut recommendation ; convolutional neural networks", "DOI": "10.3390/computation9050054", "PubYear": 2021, "Volume": "9", "Issue": "5", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Electronics, University of Liverpool, Liverpool L69 3GJ, UK"}, {"AuthorId": 2, "Name": "Waleed Al-Nua<PERSON>y", "Affiliation": "Department of Electrical Engineering and Electronics, University of Liverpool, Liverpool L69 3GJ, UK↑Author to whom correspondence should be addressed. Academic Editor: <PERSON><PERSON><PERSON><PERSON>"}, {"AuthorId": 3, "Name": "Baidaa Al-Bander", "Affiliation": "Department of Computer Engineering, University of Diyala, Baqubah 32010, Iraq"}], "References": [{"Title": "A Survey of Deep Facial Attribute Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "Page": "2002", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 87885597, "Title": "An explainable stacked ensemble of deep learning models for improved melanoma skin cancer detection", "Abstract": "<p>Malignant melanoma is one of the most dreadful skin cancer types caused by the abnormal growth of melanocyte cells. Deep convolutional neural network (CNN) models are becoming prominent for the automated diagnosis of melanoma from dermoscopic images. Although being incredibly accurate, the “black-box” nature of deep CNN models due to the lack of proper interpretability still prevents their wide-spread use in clinical settings. This paper proposes an explainable CNN-based stacked ensemble framework to detect melanoma skin cancer at earlier stages. In the stacking ensemble framework, the transfer learning concept is used where multiple CNN sub-models that perform the same classification task are assembled. A new model called a meta-learner uses all the sub-models’ predictions and generates the final prediction results. The model is evaluated using an open-access dataset containing both benign and malignant melanoma images. An explainability method is developed by shapely adaptive explanations to produce heatmaps that visualize the areas of melanoma images that are most indicative of the disease. This provides interpretability of our model’s decision in a manner understandable to dermatologists. Evaluation results show the effectiveness of our ensemble model with a high degree of accuracy (95.76%), sensitivity (96.67%), and AUC (0.957).</p>", "Keywords": "Melanoma; Skin cancer; Stacked ensemble model; Explainable deep learning; Dermoscopic images", "DOI": "10.1007/s00530-021-00787-5", "PubYear": 2022, "Volume": "28", "Issue": "4", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, College of Computers and Information Technology, Taif University, Taif, Saudi Arabia"}], "References": [{"Title": "Adaptive melanoma diagnosis using evolving clustering, ensemble and deep neural networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104807", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Efficient fusion of handcrafted and pre-trained CNNs features to classify melanoma skin cancer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "Page": "31219", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A machine learning approach to automatic detection of irregularity in skin lesion border using dermoscopic images", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 87886434, "Title": "Gewissensbits – wie würden Si<PERSON> urteilen?", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00287-021-01355-x", "PubYear": 2021, "Volume": "44", "Issue": "3", "JournalId": 7707, "JournalTitle": "Informatik-Spektrum", "ISSN": "0170-6012", "EISSN": "1432-122X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institut für Informatik 10, TU München, München, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "HTW Berlin, Berlin, Deutschland"}], "References": []}, {"ArticleId": 87886436, "Title": "Das neue Datenschutzgesetz der Schweiz im Vergleich zur DSGVO", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00287-021-01356-w", "PubYear": 2021, "Volume": "44", "Issue": "3", "JournalId": 7707, "JournalTitle": "Informatik-Spektrum", "ISSN": "0170-6012", "EISSN": "1432-122X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Luzern, Schweiz"}], "References": []}, {"ArticleId": 87886678, "Title": "Optimization of extended business processes in digital supply chains using mathematical programming", "Abstract": "We propose a mathematical programming approach to optimize the business process transactions in digital supply chains. Five scheduling models from the Process Systems Engineering (PSE) area are applied to schedule the processing of orders in a simplified Order-To-Cash (OTC) business process, which is modeled as a multistage network with parallel units (agents). Two case studies are presented to compare the performance of the scheduling models on various sizes of a flexible jobshop representation of the OTC process. The models are compared and scaled to select those that are more suitable to this application. The continuous-time general precedence model provides an accurate representation of the real system and performs well for small instances. The discrete-time State-Task Network (STN), however, proves most efficient in terms of tractability, despite the well-known limitations resulting from discretizing time. The tightness of the linear programming (LP) relaxations in the discrete-time STN framework, as well as the ability of commercial solvers to perform preprocessing and apply heuristics to the STN formulation, enables finding near optimal solutions quickly even for larger instances.", "Keywords": "Business process optimization ; digital supply chain ; order-to-cash ; scheduling ; mathematical programming", "DOI": "10.1016/j.compchemeng.2021.107323", "PubYear": 2021, "Volume": "152", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh 15213, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The Dow Chemical Company, Midland 48674, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Dow Chemical Company, Midland 48674, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The Dow Chemical Company, Midland 48674, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh 15213, USA;Corresponding author"}], "References": [{"Title": "Batch scheduling with quality-based changeovers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "106617", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 87886833, "Title": "Variable-fidelity hypervolume-based expected improvement criteria for multi-objective efficient global optimization of expensive functions", "Abstract": "<p>Variable-fidelity surrogate-based efficient global optimization (EGO) method with the ability to adaptively select low-fidelity (LF) and high-fidelity (HF) infill point has emerged as an alternative to further save the computational cost of the single-fidelity EGO method. However, in terms of the variable-fidelity surrogate-assisted multi-objective optimization methods, existing methods rely on empirical parameters or are unable to adaptively select LF/HF sample in the optimal search process. In this paper, two variable-fidelity hypervolume-based expected improvement criteria with analytic expressions for variable-fidelity multi-objective EGO method are proposed. The first criterion relies on the concept of variable-fidelity expected improvement matrix (VFEIM) and is obtained by aggregating the VFEIM using a simplified hypervolume-based aggregation scheme. The second criterion termed as VFEMHVI is derived analytically based on a modified hypervolume definition. Both criteria can adaptively select new LF/HF samples in the iterative optimal search process to update the variable-fidelity models towards the HF Pareto front, distinguishing the proposed methods to the rests in the open literature. The constrained versions of the two criteria are also derived for problems with constraints. The effectiveness and efficiency of the proposed methods are verified and validated over analytic problems and demonstrated by two engineering problems including aerodynamic shape optimizations of the NACA0012 and RAE2822 airfoils. The results show that the VFEMHVI combined with the normalization-based strategy to define the reference point is the most efficient one over the compared methods.</p>", "Keywords": "Variable-fidelity surrogate; Multi-objective efficient global optimization; Surrogate; Expected improvement; Hypervolume", "DOI": "10.1007/s00366-021-01404-9", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Energy and Power Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Energy and Power Engineering, Xi’an Jiaotong University, Xi’an, China; Collaborative Innovation Center for Advance Aero-Engine (CICAAE) of China, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Energy and Power Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Energy and Power Engineering, Xi’an Jiaotong University, Xi’an, China"}], "References": [{"Title": "A two-stage adaptive multi-fidelity surrogate model-assisted multi-objective genetic algorithm for computationally expensive problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "623", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 87887265, "Title": "Process of Requirement Gathering and Techniques for Web Application", "Abstract": "Project administration is currently turning into an essential section of our code industries. To Scope with initiatives successfully could be a very huge deal. Within the software challenge administration method there are some phrases, the primary phase is demand gathering. To urge the right requirement and to require care of it is most important for the total venture successfully. The demand administration won’t make sure that the project or application meets the client's wish or expected result. Needs are outlined at some stage in the coming up with phase and so these wants are used for the duration of the project. There are some strategies for gathering requirements. In this paper, there'll be mentioned these strategies and the way to form the need gathering successfully.", "Keywords": "Conceptualizing, Document Analysis, Focus Group, Interface Examination, Interview, Observation, Prototyping, request Workshops, Reverse Engineering, Survey/Questionnaire.", "DOI": "10.14704/WEB/V18SI02/WEB18063", "PubYear": 2021, "Volume": "18", "Issue": "Special Issue 02", "JournalId": 74976, "JournalTitle": "Webology", "ISSN": "", "EISSN": "1735-188X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87887306, "Title": "DSANet: Dilated spatial attention for real-time semantic segmentation in urban street scenes", "Abstract": "Efficient and accurate semantic segmentation is particularly important in scene understanding for autonomous driving. Although Deep Convolutional Neural Networks(DCNNs) approaches have made a significant improvement for semantic segmentation. However, state-of-the-art models such as Deeplab and PSPNet have complex architectures and high computation complexity. Thus, it is inefficient for realtime applications. On the other hand, many works compromise the performance to obtain real-time inference speed which is critical for developing a light network model with high segmentation accuracy. In this paper, we present a computationally efficient network named DSANet, which follows a two-branch strategy to tackle the problem of real-time semantic segmentation in urban scenes. We first design a Semantic Encoding Branch, which employs channel split and shuffle to reduce the computation and maintain higher segmentation accuracy. Also, we propose a dual attention module consisting of dilated spatial attention and channel attention to make full use of the multi-level feature maps simultaneously, which helps predict the pixel-wise labels in each stage. Meanwhile, Spatial Encoding Network is used to enhance semantic information and preserve the spatial details. To better combine context information and spatial information, we introduce a Simple Feature Fusion Module. We evaluated our model with state-of-the-art semantic image semantic segmentation methods using two challenging datasets. The proposed method achieves an accuracy of 69.9% mean IoU and 71.3% mean IoU at speed of 75.3 fps and 34.08 fps on CamVid and Cityscapes test datasets respectively.", "Keywords": "Street scene understanding ; Deep learning ; Lightweight convolutional ; Neural network ; Real-time semantic segmentation ; Spatial attention", "DOI": "10.1016/j.eswa.2021.115090", "PubYear": 2021, "Volume": "183", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen 361005, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen 361005, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen 361005, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, Xiamen University, Xiamen 361005, China"}], "References": [{"Title": "AGLNet: Towards real-time semantic segmentation of self-driving images via attention-guided lightweight network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106682", "JournalTitle": "Applied Soft Computing"}, {"Title": "BiSeNet V2: Bilateral Network with Guided Aggregation for Real-Time Semantic Segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "11", "Page": "3051", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 87887311, "Title": "Real‐time model‐free resilient control for discrete nonlinear systems", "Abstract": "<p>Motivated by the increasing complexity of systems to be controlled and different system components that cause uncertainties, threats, and disturbances, among other system stressing phenomena. Resilient control is an important design paradigm that has attracted attention from academics, practitioners, and the industrial sector. Therefore, this paper proposes the design of a model-free resilient control for unknown discrete-time nonlinear systems based on a recurrent high order neural network trained with an on-line extended Kalman filter-based algorithm for output trajectory tracking in the presence of uncertainties, disturbances, and unmodeled dynamics. This paper also includes the stability proof of the entire proposed scheme; its applicability is shown via simulation and experimental results including a comparative analysis of the proposed controller against well-known controllers for a three-phase induction motor.</p>", "Keywords": "discrete-time nonlinear systems;model-free control;neural control;recurrent neural networks;resilient control", "DOI": "10.1002/asjc.2564", "PubYear": 2021, "Volume": "23", "Issue": "5", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Sciences Department, CUCEI, University of Guadalajara, Guadalajara, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Sciences Department, CUCEI, University of Guadalajara, Guadalajara, Mexico"}], "References": []}, {"ArticleId": 87887564, "Title": "Social robot deception and the culture of trust", "Abstract": "Abstract \n Human beings are deeply social, and both evolutionary traits and cultural constructs encourage cooperation based on trust. Social robots interject themselves in human social settings, and they can be used for deceptive purposes. Robot deception is best understood by examining the effects of deception on the recipient of deceptive actions, and I argue that the long-term consequences of robot deception should receive more attention, as it has the potential to challenge human cultures of trust and degrade the foundations of human cooperation. In conclusion: regulation, ethical conduct by producers, and raised general awareness of the issues described in this article are all required to avoid the unfavourable consequences of a general degradation of trust.", "Keywords": "", "DOI": "10.1515/pjbr-2021-0021", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 25988, "JournalTitle": "<PERSON><PERSON><PERSON>, Journal of Behavioral Robotics", "ISSN": "2080-9778", "EISSN": "2081-4836", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Østfold University College, Faculty of Business, Languages and Social Sciences , NO-1757 Halden , Norway"}], "References": []}, {"ArticleId": 87887566, "Title": "Are robots perceived as good decision makers? A study investigating trust and preference of robotic and human linesman-referees in football", "Abstract": "Abstract \n Increasingly, robots are decision makers in manufacturing, finance, medicine, and other areas, but the technology may not be trusted enough for reasons such as gaps between expectation and competency, challenges in explainable AI, users’ exposure level to the technology, etc. To investigate the trust issues between users and robots, the authors employed in this study, the case of robots making decisions in football (or “soccer” as it is known in the US) games as referees. More specifically, we presented a study on how the appearance of a human and three robotic linesmen (as presented in a study by <PERSON> et al.) impacts fans’ trust and preference for them. Our online study with 104 participants finds a positive correlation between “Trust” and “Preference” for humanoid and human linesmen, but not for “AI” and “mechanical” linesmen. Although no significant trust differences were observed for different types of linesmen, participants do prefer human linesman to mechanical and humanoid linesmen. Our qualitative study further validated these quantitative findings by probing possible reasons for people’s preference: when the appearance of a linesman is not humanlike, people focus less on the trust issues but more on other reasons for their linesman preference such as efficiency, stability, and minimal robot design. These findings provide important insights for the design of trustworthy decision-making robots which are increasingly integrated to more and more aspects of our everyday lives.", "Keywords": "", "DOI": "10.1515/pjbr-2021-0020", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 25988, "JournalTitle": "<PERSON><PERSON><PERSON>, Journal of Behavioral Robotics", "ISSN": "2080-9778", "EISSN": "2081-4836", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Design and Environmental Analysis, Cornell University , Ithaca , NY 14850 , United States of America"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Design and Artificial Intelligence (DAI), Singapore University of Technology and Design (SUTD) , Singapore , Singapore"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Design and Environmental Analysis and the Sibley School of Mechanical and Aerospace Engineering, Cornell University , Ithaca , NY 14850 , United States of America"}], "References": []}, {"ArticleId": 87887904, "Title": "Integral imaging display with enhanced depth of field based on bifocal lens array", "Abstract": "<p>In this paper, we propose a depth-enhanced integral imaging display based on bifocal lens array. The bifocal lens array consists of a small lens array with plano-convex circular lens units and a large lens array with square lens units. The proposed display device generates two central depth planes and stitches two reconstructed 3-D images in depth, which largely improves the depth of field. The experimental results indicate that the proposed structure reconstructed 3-D image with the depth of field as large as 150.8 mm, which is double of conventional integral imaging display.</p>", "Keywords": "bifocal lens array;depth of field;integral imaging", "DOI": "10.1002/jsid.1018", "PubYear": 2021, "Volume": "29", "Issue": "9", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 3, "Name": "Fei‐<PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China"}], "References": []}, {"ArticleId": 87887971, "Title": "Cascade control for heterogeneous multirotor UAS", "Abstract": "Purpose \n Despite of the numerous characteristics of the multirotor unmanned aircraft systems (UASs), they have been termed as less energy-efficient compared to fixed-wing and helicopter counterparts. The purpose of this paper is to explore a more efficient multirotor configuration and to provide the robust and stable control system for it.\n \n \n Design/methodology/approach \n A heterogeneous multirotor configuration is explored in this paper, which employs a large rotor at the centre to provide majority of lift and three small tilted booms rotors to provide the control. Design provides the combined characteristics of both quadcopters and helicopters in a single UAS configuration, providing endurance of helicopters keeping the manoeuvrability, simplicity and control of quadcopters. In this paper, rotational as well as translational dynamics of the multirotor are explored. Cascade control system is designed to provide an effective solution to control the attitude, altitude and position of the rotorcraft.\n \n \n Findings \n One of the challenging tasks towards successful flight of such a configuration is to design a stable and robust control system as it is an underactuated system possessing complex non-linearities and coupled dynamics. Cascaded proportional integral (PI) control approach has provided an efficient solution with stable control performance. A novel motor control loop is implemented to ensure enhanced disturbance rejection, which is also validated through Dryden turbulence model and 1-cosine gust model.\n \n \n Originality/value \n Robustness and stability of the proposed control structure for such a dynamically complex UAS configuration is demonstrated with stable attitude and position performance, reference tracking and enhanced disturbance rejection.", "Keywords": "Heterogeneous multirotor UAS;Cascaded PI control;Dryden turbulence model;Disturbance rejection", "DOI": "10.1108/IJIUS-02-2021-0008", "PubYear": 2022, "Volume": "10", "Issue": "4", "JournalId": 30076, "JournalTitle": "International Journal of Intelligent Unmanned Systems", "ISSN": "2049-6427", "EISSN": "2049-6435", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "RMIT University , Melbourne, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "RMIT University , Melbourne, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "RMIT University , Melbourne, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "RMIT University , Melbourne, Australia"}], "References": [{"Title": "Adaptive tracking controller for hexacopters with a wind disturbance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "2", "Page": "322", "JournalTitle": "Artificial Life and Robotics"}]}, {"ArticleId": 87888096, "Title": "Performance of Improved-DCSK system over land mobile satellite channel under effect of time-reversed chaotic sequences", "Abstract": "Among non-coherent chaos-based communication schemes, improved-differential chaos-shift keying (I-DCSK) with the use of time-reversed chaotic sequences shows good achievement in bit-error-rate (BER) performance and spectral efficiency. To date, BER performance of the I-DCSK system has been investigated over various transmission channels in the presence of Gaussian noise and Rayleigh fading. In this paper, we studied for the first time the performance of the I-DCSK system over a land mobile satellite channel. Discrete-time models of the transmitter and receiver over a two-state Lutz channel typically describing satellite environments are studied. We point out that there are differences in cross-correlation characteristics between the time-reversed and conventional chaotic sequences. From this aspect, BER performance of the studied system is analyzed in scenario of taking into account the cross-correlation effect of time-reversed chaotic sequences. BER expressions are verified by Monte Carlo simulations. Performance comparison between I-DCSK and DCSK-based schemes over the studied channel is carried out. Obtained results prove that the I-DCSK system is able to be used as a physical-layer modulation method using chaos for land mobile satellite applications.", "Keywords": "Improved-differential chaos shift keying ; Improved-DCSK ; I-DCSK ; Land mobile satellite channel ; Lutz model ; Analytical bit error rate (BER)", "DOI": "10.1016/j.phycom.2021.101342", "PubYear": 2021, "Volume": "47", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hanoi University of Science and Technology, No. 1 Dai Co Viet, Hai Ba Trung, Hanoi, Viet Nam;Thuyloi University, No. 175 Tay Son, Dong Da, Hanoi, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hanoi University of Science and Technology, No. 1 Dai Co Viet, Hai <PERSON>, Hanoi, Viet Nam;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hanoi University of Science and Technology, No. 1 Dai Co Viet, Hai Ba Trung, Hanoi, Viet Nam"}], "References": []}, {"ArticleId": 87888359, "Title": "Vectorization Challenges in Digital Signal Processing", "Abstract": "<p>The paper analyses the support for vectorization that can be found in some programming languages, and the ways it could also be used in Ada. A proposal for an Ada extension for enhanced vectorization support is included.</p>", "Keywords": "", "DOI": "10.1145/3463478.3463490", "PubYear": 2021, "Volume": "40", "Issue": "2", "JournalId": 12096, "JournalTitle": "ACM SIGAda Ada Letters", "ISSN": "1094-3641", "EISSN": "1557-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Juan <PERSON> la Puente", "Affiliation": ""}], "References": [{"Title": "SIMD programming using Intel vector extensions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "83", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 87888365, "Title": "Control theory for stochastic distributed parameter systems, an engineering perspective", "Abstract": "The main purpose of this paper is to survey some recent progresses on control theory for stochastic distributed parameter systems, i.e., systems governed by stochastic differential equations in infinite dimensions, typically by stochastic partial differential equations. We will explain the new phenomenon and difficulties in the study of controllability and optimal control problems for one dimensional stochastic parabolic equations and stochastic hyperbolic equations. In particular, we shall see that both the formulation of corresponding stochastic control problems and the tools to solve them may differ considerably from their deterministic/finite-dimensional counterparts. More importantly, one has to develop new tools, say, the stochastic transposition method introduced in our previous works, to solve some problems in this field.", "Keywords": "Stochastic distributed parameter system ; Controllability ; Optimal control ; Pontryagin-type maximum principle ; Stochastic linear quadratic problem", "DOI": "10.1016/j.arcontrol.2021.04.002", "PubYear": 2021, "Volume": "51", "Issue": "", "JournalId": 3642, "JournalTitle": "Annual Reviews in Control", "ISSN": "1367-5788", "EISSN": "1872-9088", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mathematics, Sichuan University, Chengdu 610064, Sichuan Province, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics, Sichuan University, Chengdu 610064, Sichuan Province, China;Corresponding author"}], "References": []}, {"ArticleId": 87888507, "Title": "An Energy Stable Finite Element Scheme for the Three-Component Cahn–<PERSON>iard-Type Model for Macromolecular Microsphere Composite Hydrogels", "Abstract": "<p>In this article, we present and analyze a finite element numerical scheme for a three-component macromolecular microsphere composite (MMC) hydrogel model, which takes the form of a ternary Cahn–<PERSON>iard-type equation with <PERSON><PERSON><PERSON><PERSON>de<PERSON> energy potential. The numerical approach is based on a convex–concave decomposition of the energy functional in multi-phase space, in which the logarithmic and the nonlinear surface diffusion terms are treated implicitly, while the concave expansive linear terms are explicitly updated. A mass lumped finite element spatial approximation is applied, to ensure the positivity of the phase variables. In turn, a positivity-preserving property can be theoretically justified for the proposed fully discrete numerical scheme. In addition, unconditional energy stability is established as well, which comes from the convexity analysis. Several numerical simulations are carried out to verify the accuracy and positivity-preserving property of the proposed scheme.</p>", "Keywords": "MMC-TDGL equations; Mass lumped FEM; Convex–concave decomposition; Energy stability; Positivity preserving; 35K25; 35K55; 60F10; 65M60", "DOI": "10.1007/s10915-021-01508-w", "PubYear": 2021, "Volume": "87", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Beijing Normal University, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Mathematics for Nonlinear Sciences, School of Mathematical Sciences, Fudan University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, The University of Massachusetts, North Dartmouth, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, The University of Tennessee, Knoxville, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Beijing Normal University and Laboratory of Mathematics and Complex Systems, Ministry of Education, Beijing, People’s Republic of China"}], "References": [{"Title": "Energy Stable Numerical Schemes for Ternary Cahn-Hilliard System", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "84", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 87888608, "Title": "Reachable sets of classifiers and regression models: (non-)robustness analysis and robust training", "Abstract": "Neural networks achieve outstanding accuracy in classification and regression tasks. However, understanding their behavior still remains an open challenge that requires questions to be addressed on the robustness, explainability and reliability of predictions. We answer these questions by computing reachable sets of neural networks, i.e. sets of outputs resulting from continuous sets of inputs. We provide two efficient approaches that lead to over- and under-approximations of the reachable set. This principle is highly versatile, as we show. First, we use it to analyze and enhance the robustness properties of both classifiers and regression models. This is in contrast to existing works, which are mainly focused on classification. Specifically, we verify (non-)robustness, propose a robust training procedure, and show that our approach outperforms adversarial attacks as well as state-of-the-art methods of verifying classifiers for non-norm bound perturbations. Second, we provide techniques to distinguish between reliable and non-reliable predictions for unlabeled inputs, to quantify the influence of each feature on a prediction, and compute a feature ranking.", "Keywords": "Robustness; Verification; Reachable set; Neural network", "DOI": "10.1007/s10994-021-05973-0", "PubYear": 2021, "Volume": "110", "Issue": "6", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Technical University of Munich, Munich, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Informatics, Technical University of Munich, Munich, Germany"}], "References": []}, {"ArticleId": 87888613, "Title": "Spatial dependence between training and test sets: another pitfall of classification accuracy assessment in remote sensing", "Abstract": "<p>Spatial autocorrelation is inherent to remotely sensed data. Nearby pixels are more similar than distant ones. This property can help to improve the classification performance, by adding spatial or contextual features into the model. However, it can also lead to overestimation of generalisation capabilities, if the spatial dependence between training and test sets is ignored. In this paper, we review existing approaches that deal with spatial autocorrelation for image classification in remote sensing and demonstrate the importance of bias in accuracy metrics when spatial independence between the training and test sets is not respected. We compare three spatial and non-spatial cross-validation strategies at pixel and object levels and study how performances vary at different sample sizes. Experiments based on Sentinel-2 data for mapping two simple forest classes show that spatial leave-one-out cross-validation is the better strategy to provide unbiased estimates of predictive error. Its performance metrics are consistent with the real quality of the resulting map contrary to traditional non-spatial cross-validation that overestimates accuracy. This highlight the need to change practices in classification accuracy assessment. To encourage it we developped Museo ToolBox , an open-source python library that makes spatial cross-validation possible.</p>", "Keywords": "Spatial autocorrelation; Cross-validation; Accuracy assessment; Overfitting; Remote sensing", "DOI": "10.1007/s10994-021-05972-1", "PubYear": 2022, "Volume": "111", "Issue": "7", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "INRAE, UMR DYNAFOR, Université de Toulouse, Castanet-Tolosan, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CNES/CNRS/INRAE/IRD/UPS, UMR CESBIO, Université de Toulouse, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "INRAE, UMR DYNAFOR, Université de Toulouse, Castanet-Tolosan, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "INRAE, UMR DYNAFOR, Université de Toulouse, Castanet-Tolosan, France"}], "References": [{"Title": "Explaining the unsuitability of the kappa coefficient in the assessment and comparison of the accuracy of thematic maps obtained by image classification", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "239", "Issue": "", "Page": "111630", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 87888670, "Title": "An efficient watermarking algorithm for medical images", "Abstract": "<p>Medical images exchanged over vulnerable networks are sensitive to modifications or tampering which could lead to misdiagnosis, risking the lives of the patients. Therefore, to provide safe and secure transmission of medical images, key security requirements such as authenticity, integrity, and confidentiality must be addressed. In this paper, a novel and efficient algorithm is proposed which provides security services for transmitted medical images in tele-medicine applications. The proposed algorithm employs joint encryption/watermarking procedures to provide the required security services, as well as to provide control access privileges at the receiving side. The algorithm is based on reversible data hiding principles that guarantee the extraction of the hidden data while restoring the original image unaffected. The embedded data is basically two watermarks; a spatial domain watermark and an encrypted domain watermark. The dual watermarks allow the authenticity and integrity of the transmitted medical images to be verified in the spatial and encrypted domains. The proposed algorithm has been extensively evaluated using three performance metrics; embedding capacity, visual image quality, and entropy. The ability of the algorithm to fulfill the security, reversibility, and separability requirements has been validated.</p>", "Keywords": "Medical image security; Reversible data hiding; Reversibility; Separability; Spatial domain watermarking; Encrypted domain watermarking; Embedding capacity; Entropy", "DOI": "10.1007/s11042-021-10801-7", "PubYear": 2021, "Volume": "80", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Princess <PERSON><PERSON> for Technology, Amman, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Princess <PERSON><PERSON> for Technology, Amman, Jordan"}], "References": []}, {"ArticleId": 87888671, "Title": "Efficient and dynamic verifiable multi-keyword searchable symmetric encryption with full security", "Abstract": "<p>Increasing the popularity of cloud computing raises the importance of efforts to improve the services of this paradigm. Searching over encrypted data is a requirement for cloud storage to provide, in addition to privacy-preserving, convenient and low-cost access to some of the outsourced data. Security and functionality along with efficiency are important characteristics of searchable encryption schemes that improve them make this schemes more applicable to the real world. There are proposed structures for symmetric searchable encryption (SSE) in this paper, by adding functionalities to randomized SSE schemes, to provide a optimal scheme. We design a rFSMSE scheme that allows searching by multiple keywords. This scheme is fully secure and its search time complexity is logarithmic. Then we upgrad this scheme to the verifiable rFSMSE scheme called rFSVMSE, without increasing the complexity of search time, storage, and communication. We demonstrate that the proposed schemes are upgradable to dynamic ones. Simulations show the time taken to search for multi-keywords in the rFSMSE and rFSVMSE schemes is less than that in the previous randomized SSE.</p>", "Keywords": "Randomized symmetric searchable encryption; Multi-keyword search; Dynamics; Verifiability; Binary search; Plaintext privacy; Predicate privacy; Full security", "DOI": "10.1007/s11042-021-10844-w", "PubYear": 2021, "Volume": "80", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Shahed university, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Shahed university, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Shahed University, Tehran, Iran"}], "References": [{"Title": "Enabling Verifiable and Dynamic Ranked Search over Outsourced Data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "69", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 87888672, "Title": "Video anomaly detection and localization based on appearance and motion models", "Abstract": "<p>In this paper, we present an approach to detect and localize anomalies in the surveillance videos. Precise detection, modeling the normality in a context and dealing with false alarms are the major challenges to cope with while performing detection and localization of anomalies. We propose appearance and motion models to detect an anomalous event at the frame level in a video. In the anomalous frames, we localize the anomaly using appearance and connected component analysis cues. The proposed appearance model utilizes the mask-rcnn for scene semantic analysis. The proposed motion model constructs frame descriptors based on the histograms of the intensity difference maps of the consecutive video frames. A one class support vector machine (OCSVM) is used to detect motion based anomalous event. In order to eliminate the false alarms of anomaly detection and localization emerging due to camera jitter and object movements in unlikely motion regions of a video frame, we exploit the connected component analysis. Experiments on the Avenue and UMN datasets reveal that the proposed approach achieves high accuracy and outperforms the existing methods.</p>", "Keywords": "Anomaly detection; Anomaly localization; Motion model; Appearance model", "DOI": "10.1007/s11042-021-10921-0", "PubYear": 2021, "Volume": "80", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, Quaid-i-Azam University, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics, Quaid-i-Azam University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electronics, Quaid-i-Azam University, Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronics, Quaid-i-Azam University, Islamabad, Pakistan"}], "References": []}, {"ArticleId": 87888816, "Title": "Interview with <PERSON>", "Abstract": "<p><PERSON><PERSON> is currently an Adjunct Professor of the Practice of Computer Science at Brown University. He received an Sc.B. in Computer Science from Brown in 1981, and is recognized for his work on linking and multimedia technology for the Internet and for the evolution of Web development software. In the 1980s, <PERSON><PERSON> served as a Co-Director of Brown University's Institute for Research in Information and Scholarship, where he led the development of Intermedia, a hypermedia system that influenced both the creator of the Web and the creator of the Mosaic Web browser. In mid-1980s, he helped start two ACM conferences - OOPSLA (Object-Oriented Programming, Systems, and Languages) and Hypertext '87, which continue to this day. Following his work in academia, <PERSON><PERSON> worked for several years as the Director of System/User Software for pen/tablet pioneer GO Corporation before transitioning to his role as President of Product Development at Macromedia (later acquired by Adobe). At Macromedia, <PERSON><PERSON> oversaw a variety of Web development and multimedia products at Macromedia, including Shockwave, Dreamweaver, Flash, the latter of which had more than 4 billion downloads in its heyday in the 1990s and early 2000s.</p>", "Keywords": "", "DOI": "10.1145/3460304.3460306", "PubYear": 2021, "Volume": "2021", "Issue": "Spring", "JournalId": 32248, "JournalTitle": "ACM SIGWEB Newsletter", "ISSN": "1931-1745", "EISSN": "1931-1435", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hof University, Germany"}], "References": []}, {"ArticleId": 87888818, "Title": "CIKM 2020 conference report", "Abstract": "<p> The 29th ACM International Conference on Information and Knowledge Management (CIKM) was held online from the 19 <sup>th</sup> to the 23 <sup>rd</sup> of October 2020. CIKM is an annual computer science conference, focused on research at the intersection of information retrieval, machine learning, databases as well as semantic and knowledge-based technologies. Since it was first held in the United States in 1992, 28 conferences have been hosted in 9 countries around the world. </p>", "Keywords": "", "DOI": "10.1145/3460304.3460305", "PubYear": 2021, "Volume": "2021", "Issue": "Spring", "JournalId": 32248, "JournalTitle": "ACM SIGWEB Newsletter", "ISSN": "1931-1745", "EISSN": "1931-1435", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Galway Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Heinrich-Heine-University Düsseldorf"}], "References": []}, {"ArticleId": 87888820, "Title": "Towards solution-oriented knowledge recommendation", "Abstract": "<p>This research identifies three practical problems in the existing scholarly recommendation systems, which leads to three research issues related to solution-oriented scientific knowledge recommendation that have not been addressed in the literature, including the lack of solution-oriented article recommendation, the lack of solution-oriented knowledge repositories, and the lack of weighted bibliometric network for ranking academic articles. Individual research could be conducted to address each issue, however this study proposes a knowledge fusion framework to collectively integrate multiple models which are specifically designed to solve each one of the issues respectively. The framework will be able to generate valuable knowledge repositories containing the solutions proposed in the mass scholarly articles to answer academic questions, and meanwhile it can rank these solutions based on weighted bibliometric networks.</p>", "Keywords": "", "DOI": "10.1145/3460304.3460308", "PubYear": 2021, "Volume": "2021", "Issue": "Spring", "JournalId": 32248, "JournalTitle": "ACM SIGWEB Newsletter", "ISSN": "1931-1745", "EISSN": "1931-1435", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "UNSW Canberra"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UTS"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "UNSW Canberra"}], "References": []}, {"ArticleId": 87888849, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1364-8152(21)00106-7", "PubYear": 2021, "Volume": "140", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [], "References": []}, {"ArticleId": 87888861, "Title": "Research on the efficient self-adaptive servo-control strategy in micro-EDM milling", "Abstract": "<p>As an important fabricating technology, micro-EDM is always confined by the rather low efficiency in practical applications. To solve this problem, a self-adaptive servo-control strategy is proposed in this study. Firstly, a self-determination technique is developed to define the threshold voltages of different discharge conditions. Then, the least square method is applied to predict the discharge condition of the next sampling period. After that, a detailed servo-control strategy considering both discharge condition and velocity is put forward to adjust the feed speed to match up with the actual material removal rate. Finally, a comparative experiment is undertaken to verify the effects of the proposed self-adaptive strategy. The results demonstrated that the material removal rate and ratio of spark pulse to the whole discharge pulses are significantly improved in comparison with the traditional constant speed strategy. The surface texture and tool wear generated by the two strategies are also compared. The results indicated that the proposed self-adaptive strategy enhances the efficiency without bringing extra disadvantages to the surface precision or tool wear. Moreover, retraction of the electrode is significantly decreased and the feed speed can steadily keep close to the material removal rate under the proposed strategy. Thus, the dynamic performance of the machine tool was better and the microelectrode was prevented from crashing into the workpiece. The idea of the proposed strategy can also be applied to other micro-EDM techniques such as drilling and die sinking.</p>", "Keywords": "Micro-EDM; Threshold self-determination; Discharge condition prediction; Self-adaptive strategy; Efficiency improvement", "DOI": "10.1007/s00170-021-07074-7", "PubYear": 2021, "Volume": "114", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Optical Science and Engineering, Fudan University, Shanghai, China;Institute of Machinery Manufacturing Technology, China Academy of Engineering Physics, Mianyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Machinery Manufacturing Technology, China Academy of Engineering Physics, Mianyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Machinery Manufacturing Technology, China Academy of Engineering Physics, Mianyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Machinery Manufacturing Technology, China Academy of Engineering Physics, Mianyang, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Machinery Manufacturing Technology, China Academy of Engineering Physics, Mianyang, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Optical Science and Engineering, Fudan University, Shanghai, China"}], "References": []}, {"ArticleId": 87888878, "Title": "Leveraging Human Perception in Robot Grasping and Manipulation Through Crowdsourcing and Gamification", "Abstract": "<p>Robot grasping in unstructured and dynamic environments is heavily dependent on the object attributes. Although Deep Learning approaches have delivered exceptional performance in robot perception, human perception and reasoning are still superior in processing novel object classes. Furthermore, training such models requires large, difficult to obtain datasets. This work combines crowdsourcing and gamification to leverage human intelligence, enhancing the object recognition and attribute estimation processes of robot grasping. The framework employs an attribute matching system that encodes visual information into an online puzzle game, utilizing the collective intelligence of players to expand the attribute database and react to real-time perception conflicts. The framework is deployed and evaluated in two proof-of-concept applications: enhancing the control of a robotic exoskeleton glove and improving object identification for autonomous robot grasping. In addition, a model for estimating the framework response time is proposed. The obtained results demonstrate that the framework is capable of rapid adaptation to novel object classes, based purely on visual information and human experience.</p>", "Keywords": "crowdsourcing;gamification;grasping;image classification;robot perception", "DOI": "10.3389/frobt.2021.652760", "PubYear": 2021, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "Gal Gorjup", "Affiliation": "New Dexterity Research Group, Department of Mechanical Engineering, The University of Auckland, Auckland, New Zealand."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "New Dexterity Research Group, Department of Mechanical Engineering, The University of Auckland, Auckland, New Zealand."}, {"AuthorId": 3, "Name": "Minas Liarokapis", "Affiliation": "New Dexterity Research Group, Department of Mechanical Engineering, The University of Auckland, Auckland, New Zealand."}], "References": []}, {"ArticleId": 87888940, "Title": "Balanced content space partitioning for pub/sub: a study on impact of varying partitioning granularity", "Abstract": "<p>The increasing volume of live content poses new challenges to publish/subscribe services at the cloud scale. Providing efficient publish/subscribe services for live content is a complex task because most subscriptions occupy only a small portion of the entire subscription space, i.e., use limited live content. Thus, the real-world workload of a publish/subscribe service for live content becomes skewed, and the distribution of subscriptions becomes seriously imbalanced, causing an inefficient processing of events. We present a correlation-based balanced content space partitioning technique for a publish/subscribe service. Our proposed technique reduces the degree of imbalance from a skewed subscription workload in a content-based publish/subscribe service, using the correlation coefficient between attributes to build dimension groups. We assign attributes of low correlation to the same dimension group to balance the subscription workloads. Moreover, we present our analysis on the load balance impacts of varying partitioning granularity for efficient message processing. We conducted empirical experiments evaluating the effectiveness of our partitioning technique and measuring the impact of varying partitioning granularity. The results show that the proposed technique outperforms conventional partitioning techniques by evaluating the ways in which subscriptions are evenly distributed among brokers. Moreover, the results show that the load balance can be improved by increasing the partitioning granularity with an adjustment of two degrees, i.e., the segment and dimension group degrees.</p>", "Keywords": "Publish/subscribe system; Load balance; Content space partitioning; Partitioning granularity", "DOI": "10.1007/s11227-021-03821-5", "PubYear": 2021, "Volume": "77", "Issue": "12", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ajou University, Suwon-si, Republic of Korea"}, {"AuthorId": 2, "Name": "Zhetao Li", "Affiliation": "Xiangtan University, Xiangtan, China"}, {"AuthorId": 3, "Name": "Sang<PERSON><PERSON> Oh", "Affiliation": "Ajou University, Suwon-si, Republic of Korea"}], "References": []}, {"ArticleId": 87888941, "Title": "An efficient mutual authentication and key agreement scheme without password for wireless sensor networks", "Abstract": "<p>Wireless sensor networks (WSNs) are usually deployed in hostile or unattended areas, and users need to obtain real-time data from WSNs. The data collected by sensor nodes are usually relatively private and sensitive, so users must pass the identity authentication before obtaining the information perceived by sensor nodes. In order to enhance the security of wireless sensor networks and prevent illegal users from accessing sensor nodes, this paper designs an efficient and secure identity authentication protocol for wireless sensor networks. The proposed protocol makes full use of the advantages of lightweight cryptographic primitives such as physical unclonable function, one-way hash function and bitwise exclusive operation. Moreover, users only need to use biometrics (such as fingerprints, iris) to access the remote systems and do not need to rely on any password, which avoids the trouble of memorizing and losing password. To prove the scheme’s security, the well-known formal security proof with random oracle model and traditional heuristic discussion are given. Additionally, the performance comparison results show that our scheme has less communication overhead and computation complexity, and is effective for the resource constrained sensor devices in WSNs.</p>", "Keywords": "Authentication; Physical unclonable functions (PUF); WSNs; ROM; Biometrics", "DOI": "10.1007/s11227-021-03820-6", "PubYear": 2021, "Volume": "77", "Issue": "12", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Wuhan University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Wuhan University, Wuhan, China"}], "References": [{"Title": "Bio-AKA: An efficient fingerprint based two factor user authentication and key agreement scheme", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "45", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 87888942, "Title": "Optimization for DV-Hop type of localization scheme in wireless sensor networks", "Abstract": "<p>Distance Vector Hop (DV-Hop) is a range-free scheme used for node localization in wireless sensor networks (WSNs). The original DV-Hop scheme localizes the unknown nodes depending on a number of anchor nodes’ known position information and the multi-hop relationship among nodes. It is very popular and can meet most application requirements as the network is isotropous. However, it becomes powerless while the network is anisotropic due to the natural defects of its ranging strategy. In view of such problems of the traditional DV-Hop, we provide a scheme aiming to improve the original DV-Hop. In our scheme, an improved cosine similarity parameter is used to measure the similarity between path pairs, and the anchor–anchor path which is most like the path from the unknown node to the target anchor is selected to compute the average hop distance of the node-anchor path independently. Then, an improved particle swarm optimization and simulated annealing hybrid algorithm is adapted to improve the position accuracy of the initial position of an unknown node, which has been derived by the trilateration algorithm used in the original DV-Hop scheme. Based on the simulation result, in comparison with the original DV-Hop scheme and another two existed improved schemes, our proposed scheme can perform much better both on the distance estimation accuracy and on the final node localization accuracy. Thereby, our proposed scheme is a feasible and optimized choice for node localization in WSNs.</p>", "Keywords": "DV-Hop; Node localization; Path matching; Particle swarm optimization; Simulated annealing", "DOI": "10.1007/s11227-021-03818-0", "PubYear": 2021, "Volume": "77", "Issue": "12", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>qi<PERSON> Shi", "Affiliation": "School of Computer Science and Information Engineering, Shanghai Institute of Technology, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Engineering, Shanghai Institute of Technology, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Suzhou Zhongke Advanced Technology Research Institute Co., Ltd., Suzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sino Parking Tech Co. Ltd, Shanghai, China"}], "References": [{"Title": "Multiple cloud storage mechanism based on blockchain in smart homes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "304", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 87888950, "Title": "APAE: an IoT intrusion detection system using asymmetric parallel auto-encoder", "Abstract": "<p>In recent years, the world has dramatically moved toward using the internet of things (IoT), and the IoT has become a hot research field. Among various aspects of IoT, real-time cyber-threat protection is one of the most crucial elements due to the increasing number of cyber-attacks. However, current IoT devices often offer minimal security features and are vulnerable to cyber-attacks. Therefore, it is crucial to develop tools to detect such attacks in real time. This paper presents a new and intelligent network intrusion detection system named APAE that is based on an asymmetric parallel auto-encoder and is able to detect various attacks in IoT networks. The encoder part of APAE has a lightweight architecture that contains two encoders in parallel, each one having three successive layers of convolutional filters. The first encoder is for extracting local features using standard convolutional layers and a positional attention module. The second encoder also extracts the long-range information using dilated convolutional layers and a channel attention module. The decoder part of APAE is different from its encoder and has eight successive transposed convolution layers. The proposed APAE approach has a lightweight and suitable architecture for real-time attack detection and provides very good generalization performance even after training using very limited training records. The efficacy of the APAE has been evaluated using three popular public datasets named UNSW-NB15, CICIDS2017, and KDDCup99, and the results showed the superiority of the proposed model over the state-of-the-art algorithms.</p>", "Keywords": "Internet of things; IoT; UNSW-NB15; KDD; CICIDS2017; IDS; Intrusion detection; Asymmetric auto-encoder; Convolutional neural networks; Attention module", "DOI": "10.1007/s00521-021-06011-9", "PubYear": 2023, "Volume": "35", "Issue": "7", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Graduate University of Advanced Technology, Kerman, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Graduate University of Advanced Technology, Kerman, Iran"}], "References": [{"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Building Auto-Encoder Intrusion Detection System based on random forest feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "101851", "JournalTitle": "Computers & Security"}, {"Title": "RETRACTED: Auto encoder based dimensionality reduction and classification using convolutional neural networks for hyperspectral images", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "", "Page": "103280", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 87888981, "Title": "CMCSF: a collaborative service framework for mobile web augmented reality base on mobile edge computing", "Abstract": "<p>Mobile web-based augmented reality (MWAR) provides users with quick richer information interaction forms due to its convenience and universality. However, the 3D model data service for MWAR is a type of centralized file-based data service, which cannot simultaneously meet diverse user and response delay requirements in large-scale and complex applications. This affects the application and promotion of large-scale and complex MWAR application. To this end, this paper proposes a collaborative model data computing service framework (CMCSF) for MWAR between Mobile Edge Servers (MES), Cloud Servers (CS), and mobile devices. The main contributions of this paper include: (1) The CMCSF converts the file-based 3D model data service in MWAR to a computing-based and interfaced data service to meet the diverse service requirements of users; (2) The CMCSF establishes a collaborative computing model data service between the MES and the CS, and it computes a control and deployment strategy for the collaborative computing model data service in order to reduce the response delay of 3D model data; and (3) The CMCSF optimizes the loading method of the WebGL engine (for example, three.js) and applies the asynchronous loading method of the JSON interface data on the http protocol for browsers to get persistent model data services. An experimental evaluation shows the CMCSF improves the response efficiency of the 3D model data file Significantly when compared with the original centralized file-based 3D model file service.</p>", "Keywords": "Mobile web augmented reality; Mobile edge computing; Model data computing service; Collaborative computing; 94A99", "DOI": "10.1007/s00607-021-00952-8", "PubYear": 2021, "Volume": "103", "Issue": "10", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Labaratory of Film and TV Media Technology of Zhejiang Province, School of Media Engineering, Communication University of Zhejiang, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Media Engineering, Communication University Of Zhejiang, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Media Engineering, Communication University Of Zhejiang, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Media Engineering, Communication University Of Zhejiang, Hangzhou, China"}, {"AuthorId": 5, "Name": "Yuan Li", "Affiliation": "@stu.cuz.edu.cn;School of Media Engineering, Communication University Of Zhejiang, Hangzhou, China"}], "References": []}, {"ArticleId": 87889009, "Title": "Application of an Analytic Hierarchy Process to Select the Level of a Cyber Resilient Capability Maturity Model in Digital Supply Chain Systems", "Abstract": "<p>Cyber resilient is the ability to prepare for, respond to and recover from cyber attacks. Cyber resilient has emerged over the past few years because traditional cybersecurity measures are no longer enough to protect organizations from the spate of persistent attacks. It helps an organization protect against cyber risks, defend against and limit the severity of attacks, and ensure its continued survival despite an attack.The cyber resilient capability maturity model is a very important element within an effective in digital supply chain. The maturity model has 6 components: identify, protect, detect, respond, recover and continuity which affect the cybersecurity of the organization. To measure the maturity level needs a holistic approach. Therefore, the analytic hierarchy process (AHP) approach which allows both multi-criteria and simultaneous evaluation. Generally, the factors affecting cyber resilient in digital supply chain have non-physical structures. Therefore, the real problem can be represented in a better way by using fuzzy numbers instead of numbers to evaluate these factors. In this study, a fuzzy AHP approach is proposed to determine the cyber resilient capability maturity level in digital supply chain. The proposed method is applied in a real SMEs company. In the application, factors causing are weighted with triangular fuzzy numbers in pairwise comparisons. The result indicate that the weight factors from comparing the relationship of all factors put the importance of identify factors first, followed by protect, detect, respond, recover and continuity respectively.</p>", "Keywords": "Analytic Hierarchy Process, Cyber Resilient Capability Maturity Model,  Digital Supply Chain", "DOI": "10.37936/ecti-cit.2021152.240631", "PubYear": 2021, "Volume": "15", "Issue": "2", "JournalId": 75000, "JournalTitle": "ECTI Transactions on Computer and Information Technology (ECTI-CIT)", "ISSN": "2286-9131", "EISSN": "2286-9131", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Logistics and Supply Chain, Sripatum University, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology, Sripatum University, Thailand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Logistics and Supply Chain, Sripatum University, Thailand"}], "References": []}, {"ArticleId": 87889190, "Title": "Modelling and compensation of temperature errors for articulated arm coordinate measuring machines", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJMIC.2020.10037488", "PubYear": 2020, "Volume": "35", "Issue": "4", "JournalId": 12335, "JournalTitle": "International Journal of Modelling, Identification and Control", "ISSN": "1746-6172", "EISSN": "1746-6180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Wenjin Ma", "Affiliation": ""}, {"AuthorId": 3, "Name": "Jing <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87889219, "Title": "Interval-Valued Spherical Fuzzy Analytic Hierarchy Process Method to Evaluate Public Transportation Development", "Abstract": "Consensus creation is a complex challenge in decision making for conflicting or quasi-conflicting evaluator groups. The problem is even more difficult to solve, if one or more respondents are non-expert and provide uncertain or hesitant responses in a survey. This paper presents a methodological approach, the Interval-valued Spherical Fuzzy Analytic Hierarchy Process, with the objective to handle both types of problems simultaneously; considering hesitant scoring and synthesizing different stakeholder group opinions by a mathematical procedure. Interval-valued spherical fuzzy sets are superior to the other extensions with a more flexible characterization of membership function. Interval-valued spherical fuzzy sets are employed for incorporating decision makers’ judgements about the membership functions of a fuzzy set into the model with an interval instead of a single point. In the paper, Interval-valued spherical fuzzy AHP method has been applied to public transportation problem. Public transport development is an appropriate case study to introduce the new model and analyse the results due to the involvement of three classically conflicting stakeholder groups: passengers, non-passenger citizens and the representatives of the local municipality. Data from a real-world survey conducted recently in the Turkish big city, Mersin, help in understanding the new concept. As comparison, all likenesses and differences of the outputs have been pointed out in the reflection with the picture fuzzy AHP computation of the same data. The results are demonstrated and analysed in detail and the step-by-step description of the procedure might foment other applications of the model.", "Keywords": "", "DOI": "10.15388/21-INFOR451", "PubYear": 2021, "Volume": "32", "Issue": "4", "JournalId": 35016, "JournalTitle": "Informatica", "ISSN": "0868-4952", "EISSN": "1822-8844", "Authors": [{"AuthorId": 1, "Name": "Szabolcs Duleba", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Sarbast Moslem", "Affiliation": ""}], "References": [{"Title": "A novel spherical fuzzy analytic hierarchy process and its renewable energy application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4607", "JournalTitle": "Soft Computing"}, {"Title": "Consensus analysis for AHP multiplicative preference relations based on consistency control: A heuristic approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105317", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 87889385, "Title": "A fully labelled proof system for intuitionistic modal logics", "Abstract": "<p>Labelled proof theory has been famously successful for modal logics by mimicking their relational semantics within deductive systems. <PERSON> in particular designed a framework to study a variety of intuitionistic modal logics integrating a binary relation symbol in the syntax. In this paper, we present a labelled sequent system for intuitionistic modal logics such that there is not only one but two relation symbols appearing in sequents: one for the accessibility relation associated with the Kripke semantics for normal modal logics and one for the pre-order relation associated with the Kripke semantics for intuitionistic logic. This puts our system in close correspondence with the standard birelational Kripke semantics for intuitionistic modal logics. As a consequence, it can be extended with arbitrary intuitionistic Scott–<PERSON> axioms. We show soundness and completeness, together with an internal cut elimination proof, encompassing a wider array of intuitionistic modal logics than any existing labelled system.</p>", "Keywords": "", "DOI": "10.1093/logcom/exab020", "PubYear": 2021, "Volume": "31", "Issue": "3", "JournalId": 2581, "JournalTitle": "Journal of Logic and Computation", "ISSN": "0955-792X", "EISSN": "1465-363X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University College London, Gower Street, WC1E 6BT London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire d’Informatique de l’École Polytechnique, 1 rue Honoré d'Estienne d'Orves 91120 Palaiseau, France and Inria Saclay"}, {"AuthorId": 3, "Name": "Lutz Straßburger", "Affiliation": "Laboratoire d’Informatique de l’École Polytechnique, 1 rue Honoré d'Estienne d'Orves 91120 Palaiseau, France and Inria Saclay"}], "References": []}, {"ArticleId": 87889388, "Title": "Estimation of an Inertia Tensor and Automatic Balancing of a Microsatellite Mockup on an Air-Bearing Testbed", "Abstract": "<p>This paper considers the problem of determining the position of the center-of-mass and inertia tensor of a microsatellite mockup on an air-bearing testbed by using optical measurements. The position of the mockup’s center of mass can be shifted relative to the suspension’s center by using a system of electrodynamic linear actuators and loads fixed on them. Using the equations of motion of the mockup on an aerodynamic suspension and measurements of its angular position, the inertia tensor and center-of-mass position of the model are estimated. Based on these estimates, the linear actuators move to set the desired center-of-mass position relative to the suspension point. The developed algorithm for automatic balancing of the microsatellite mockup is experimentally investigated.</p>", "Keywords": "", "DOI": "10.1134/S1064230721020088", "PubYear": 2021, "Volume": "60", "Issue": "2", "JournalId": 14082, "JournalTitle": "Journal of Computer and Systems Sciences International", "ISSN": "1064-2307", "EISSN": "1555-6530", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Keldysh Institute of Applied Mathematics, Russian Academy of Sciences, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Peoples’ Friendship University of Russia, Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Moscow Institute of Physics and Technology (National Research University), Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Keldysh Institute of Applied Mathematics, Russian Academy of Sciences, Moscow, Russia"}, {"AuthorId": 5, "Name": "D. S. Roldugin", "Affiliation": "Keldysh Institute of Applied Mathematics, Russian Academy of Sciences, Moscow, Russia"}], "References": []}, {"ArticleId": 87889389, "Title": "Optimization of Automatic Tracking Systems of Air Objects Based\non Local Quadratic-biquadratic Functionals. II. Study of the Efficiency of the Method", "Abstract": "<p>The results of studies of the effectiveness of the optimal radar altimeter when tracking targets moving along trajectories of varying complexity are presented. The tracking system of the altimeter is obtained in the first part of this article by the local minimization of the quadratic-biquadratic quality functional.</p>", "Keywords": "", "DOI": "10.1134/S1064230721020143", "PubYear": 2021, "Volume": "60", "Issue": "2", "JournalId": 14082, "JournalTitle": "Journal of Computer and Systems Sciences International", "ISSN": "1064-2307", "EISSN": "1555-6530", "Authors": [{"AuthorId": 1, "Name": "V. S. Verba", "Affiliation": "JSC Corporation “Vega”, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "JSC Corporation “Vega”, Moscow, Russia;Bauman Moscow State Technical University, Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "JSC Corporation “Vega”, Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Moscow Aviation Institute (National Research University), Moscow, Russia"}], "References": [{"Title": "Optimization of Automatic Support Systems of Air Objects Based on Local Quadratic-biquadratic Functionals. I. Synthesis of Optimum Control", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "1", "Page": "22", "JournalTitle": "Journal of Computer and Systems Sciences International"}]}, {"ArticleId": 87889495, "Title": "LimonDroid: a system coupling three signature-based schemes for profiling Android malware", "Abstract": "<p>Android remains an interesting target to attackers due to its openness. A contribution in the literature consists of providing similarity measurement such as fuzzy hashing to fight against code obfuscation techniques. Research works in this approach suffer from limited signature database. This work combines fuzzy hashing with YARA rules and VirusTotal signature-based schemes, to improve and consistency of the signature database. It is proposed LimonDroid, an Android system, which mimics Limon, a Desktop security tool that includes such schemes. LimonDroid has been tested with 341 malicious and 300 benign applications on a database of 12925 fuzzy-hashed malware signatures, 62 YARA malware families’ patterns and VirusTotal engine. Our approach gives a true-positive rate of 97.36%, a true negative rate of 98.33% and an accuracy of 97.82%. A comparison with similarity-based solutions reveals that LimonDroid is more efficient for users. The objective is not to propose a detection approach better than those in the literature. Instead, we aim at establishing a robust signature database able to identify malicious trends in Android apps.</p>", "Keywords": "Android malware; Detection; Fuzzy hashing; Limon sandbox; Static analysis; Similarity; VirusTotal; YARA", "DOI": "10.1007/s42044-020-00068-w", "PubYear": 2021, "Volume": "4", "Issue": "2", "JournalId": 6478, "JournalTitle": "Iran Journal of Computer Science", "ISSN": "2520-8438", "EISSN": "2520-8446", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Faculty of Science, University of Ngaoundéré, Ngaoundéré, Cameroon"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Faculty of Science, University of Ngaoundéré, Ngaoundéré, Cameroon"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Corne<PERSON>", "Affiliation": "Department of Mathematics and Computer Science, National School of Agro-Industrial Science, University of Ngaoundéré, Ngaoundéré, Cameroon"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Computing Sciences, Scientific Research Development Institute of Technology, Loganlea, Australia"}], "References": [{"Title": "A Survey on Various Threats and Current State of Security in Android Platform", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 87889607, "Title": "DecPDEVS: New Simulation Algorithms to Improve Message Handling in PDEVS", "Abstract": "This work proposes a new simulation algorithm to improve message handling in discrete event formalism. We present an approach to minimize simulation execution time. To do this, we propose to reduce the number of exchanged messages between Parallel DEVS (PDEVS) components (simulators and coordinators). We propose three changes from PDEVS: direct coupling, flat structure and local schedule. The goal is the decentralisation of a number of tasks to make the simulators more autonomous and simplify the coordinators to achieve a greater speedup. We propose to compare the simulation results of several models to demonstrate the benefits of our approach.", "Keywords": "Simulation;PDEVS Formalism;Direct Coupling;Decentralised Schedule;Flat Structure", "DOI": "10.4236/ojmsi.2021.92012", "PubYear": 2021, "Volume": "9", "Issue": "2", "JournalId": 32695, "JournalTitle": "Open Journal of Modelling and Simulation", "ISSN": "2327-4018", "EISSN": "2327-4026", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Corsica, CNRS UMR SPE, Campus Grimaldi 20250 Corti, Corsica, France ."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Corsica, CNRS UMR SPE, Campus Grimaldi 20250 Corti, Corsica, France ."}], "References": []}, {"ArticleId": 87889622, "Title": "Thermodynamic modeling reveals widespread multivalent binding by RNA-binding proteins", "Abstract": "Motivation <p>Understanding how proteins recognize their RNA targets is essential to elucidate regulatory processes in the cell. Many RNA-binding proteins (RBPs) form complexes or have multiple domains that allow them to bind to RNA in a multivalent, cooperative manner. They can thereby achieve higher specificity and affinity than proteins with a single RNA-binding domain. However, current approaches to de novo discovery of RNA binding motifs do not take multivalent binding into account.</p> Results <p>We present Bipartite Motif Finder (BMF), which is based on a thermodynamic model of RBPs with two cooperatively binding RNA-binding domains. We show that bivalent binding is a common strategy among RBPs, yielding higher affinity and sequence specificity. We furthermore illustrate that the spatial geometry between the binding sites can be learned from bound RNA sequences. These discovered bipartite motifs are consistent with previously known motifs and binding behaviors. Our results demonstrate the importance of multivalent binding for RNA-binding proteins and highlight the value of bipartite motif models in representing the multivalency of protein-RNA interactions.</p> Availability and implementation <p>BMF source code is available at https://github.com/soedinglab/bipartite_motif_finder under a GPL license. The BMF web server is accessible at https://bmf.soedinglab.org.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab300", "PubYear": 2021, "Volume": "37", "Issue": "Supplement_1", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Quantitative and Computational Biology, Max Planck Institute for Biophysical Chemistry, Göttingen 37077, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Quantitative and Computational Biology, Max Planck Institute for Biophysical Chemistry, Göttingen 37077, Germany;Campus-Institut Data Science (CIDAS), Göttingen 37077, Germany"}], "References": [{"Title": "MODER2: first-order Markov modeling and discovery of monomeric and dimeric binding motifs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "9", "Page": "2690", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 87889764, "Title": "Enabling narrative through design", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJHFE.2021.10037458", "PubYear": 2021, "Volume": "8", "Issue": "1", "JournalId": 16025, "JournalTitle": "International Journal of Human Factors and Ergonomics", "ISSN": "2045-7804", "EISSN": "2045-7812", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87890102, "Title": "Grammatical evolution for constraint synthesis for mixed-integer linear programming", "Abstract": "The Mixed-Integer Linear Programming models are a common representation of real-world objects. They support simulation within the expressed bounds using constraints and optimization of an objective function. Unfortunately, handcrafting a model that aligns well with reality is time-consuming and error-prone. In this work, we propose a Grammatical Evolution for Constraint Synthesis (GECS) algorithm that helps human experts by synthesizing constraints for Mixed-Integer Linear Programming models. Given relatively easy-to-provide data of available variables and parameters, and examples of feasible solutions, GECS produces a well-formed Mixed-Integer Linear Programming model in the ZIMPL modeling language. GECS outperforms several previous algorithms, copes well with tens of variables, and seems to be resistant to the curse of dimensionality.", "Keywords": "Mathematical programming ; Model acquisition ; Constraint learning ; High-level modeling language ; Operations research", "DOI": "10.1016/j.swevo.2021.100896", "PubYear": 2021, "Volume": "64", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Science, Poznan University of Technology, Poznań, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business, University College Dublin, Dublin, Ireland"}], "References": [{"Title": "Improved differential evolution for noisy optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "", "Page": "100628", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 87890207, "Title": "Low-cost physical computing platforms for end-user prototyping of smart home systems", "Abstract": "End-user development (EUD) seeks to facilitate the extension and customisation of systems during use, with increasing possibilities as the Internet-of-Things (IoT) computing paradigm becomes widespread and expands into realms such as the smart home. This exploratory research study explores two popular physical computing platforms, oriented towards novice makers, that allow end-user developers to create and deploy an IoT smart home sensor device, as well as visualise the resulting data in the cloud. The end users’ experiences are evaluated against known EUD design principles, finding that the platforms in their current state are only partially able to meet two of the principles. Such end-to-end IoT prototyping platforms are a relatively recent offering of these maker-focused organisations and, despite some issues in their current state, they offer the potential to increase the ability of end-users to prototype across the complex layers of an IoT system. Future possibilities around the data visualisation layer and for integration with visual programming work are identified, to improve the end-users’ ability to deeply customise their IoT systems.", "Keywords": "End-user development ; Arduino ; physical computing ; prototyping ; Internet-of-Things", "DOI": "10.1080/0144929X.2021.1918248", "PubYear": 2021, "Volume": "40", "Issue": "10", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Pratt Institute, School of Information, New York, NY, USA"}], "References": [{"Title": "Probing IoT-based consumer services: ‘insights’ from the connected shower", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "5", "Page": "595", "JournalTitle": "Personal and Ubiquitous Computing"}]}, {"ArticleId": 87890229, "Title": "Editors’ Introduction", "Abstract": "", "Keywords": "", "DOI": "10.2478/popets-2021-0034", "PubYear": 2021, "Volume": "2021", "Issue": "3", "JournalId": 30919, "JournalTitle": "Proceedings on Privacy Enhancing Technologies", "ISSN": "", "EISSN": "2299-0984", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "U.S. Naval Research Laboratory"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Waterloo"}], "References": []}, {"ArticleId": 87890474, "Title": "Efficient and robust deep learning architecture for segmentation of kidney and breast histopathology images", "Abstract": "Image segmentation is consistently an important task for computer vision and the analysis of medical images. The analysis and diagnosis of histopathology images by using efficient algorithms that separate hematoxylin and eosin-stained nuclei was the purpose of our proposed method. In this paper, we propose a deep learning model that automatically segments the complex nuclei present in histology images by implementing an effective encoder–decoder architecture with a separable convolution pyramid pooling network (SCPP-Net). The SCPP unit focuses on two aspects: first, it increases the receptive field by varying four different dilation rates, keeping the kernel size fixed, and second, it reduces the trainable parameter by using depth-wise separable convolution. Our deep learning model experimented with three publicly available histopathology image datasets. The proposed SCPP-Net provides better experimental segmentation results compared to other existing deep learning models and is evaluated in terms of F1-score and aggregated Jaccard index.", "Keywords": "Deep learning ; Pyramid pooling ; Separable convolution ; Kidney cancer ; Breast cancer ; Nuclei segmentation", "DOI": "10.1016/j.compeleceng.2021.107177", "PubYear": 2021, "Volume": "92", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, National Institute of Technology Karnataka, Surathkal, Mangaluru 575025, Karnataka, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, National Institute of Technology Karnataka, Surathkal, Mangaluru 575025, Karnataka, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, National Institute of Technology Karnataka, Surathkal, Mangaluru 575025, Karnataka, India;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pathology, Kasturba Medical College, Mangalore, Manipal Academy of Higher Education, Manipal, India;Corresponding authors"}], "References": [{"Title": "Multi-scale fully convolutional network for gland segmentation using three-class classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>g Pan; <PERSON><PERSON>", "PubYear": 2020, "Volume": "380", "Issue": "", "Page": "150", "JournalTitle": "Neurocomputing"}, {"Title": "Robust nuclei segmentation in histopathology using ASPPU-Net and boundary refinement", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "144", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 87890488, "Title": "Sequentially rotated polarization conversion metasurface for circularly polarized\n Fabry‐<PERSON>ot \n cavity antenna", "Abstract": "<p>A sequentially rotated polarization conversion metasurface composed by receiver-transmitter unit cell is presented in this article. The receiver-transmitter unit cell has the capabilities that tuning transmission and reflection coefficients independently, which ensure that the metasurface achieves polarization conversion while maintain its reflection coefficients constant. The transmitter patches of the metasurface are arranged in a sequentially rotated order. Through tuning the sizes of the transmitter patches, equal co- and cross-polarization transmission magnitudes with 90° phase difference are obtained. Then linearly polarized wave is converted to circularly polarized wave. Meanwhile, the metasurface is designed to present high reflectivity, so that it can be applied in Fabry-Perot cavity (FPC) antenna and make the antenna obtain high gain. Combined the polarization conversion capability of the metasurface, the FPC antenna can obtain high gain and circular polarization performance simultaneously. The measured results of the fabricated antenna show that the antenna achieves well circular polarization radiation characteristics. The maximum realized gain of the antenna reaches 17 dBic with in the band of 9.75 to 10.25 GHz. The antenna also achieves well AR performance due to the sequential rotation manner adopted by the metasurface. The AR of the antenna is lower than 3 dB in the band of 9.75 to 10.25 GHz.</p>", "Keywords": "circularly polarized;Fabry-<PERSON>ot cavity antenna;metasurface;polarization conversion", "DOI": "10.1002/mmce.22725", "PubYear": 2021, "Volume": "31", "Issue": "8", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Air and Missile Defense College, Air force Engineering University, Xi'an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Air and Missile Defense College, Air force Engineering University, Xi'an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Air and Missile Defense College, Air force Engineering University, Xi'an, China"}], "References": []}, {"ArticleId": 87890511, "Title": "A Complex Encryption System Design Implemented by AES", "Abstract": "With the rapid development of internet technology and the increasing popularity of e-commerce, data encryption technology plays a very important role in data security. Information security has two aspects: security protocol and cryptographic algorithm and the latter is the foundation and core technology of information security. Advanced Encryption Standard (AES) encryption algorithm is one of the most commonly used algorithms in symmetric encryption algorithms. Such algorithms face issues when used in the context of key management and security functions. This paper focuses on the systematic analysis of these issues and summarizes AES algorithm implementation, comprehensive application and algorithm comparison with other existing methods. To analyze the performance of the proposed algorithm and to make full use of the advantages of AES encryption algorithm, one needs to reduce round key and improve the key schedule, as well as organically integrate with RSA algorithm. Java language is used to implement the algorithm due to its large library, then to show the efficiency of the proposed method we compare different parameters, such as encryption/decryption speed, entropies and memory consumption...) with a classic algorithm. Based on the results of the comparison between AES and the hybrid AES algorithm, the proposed algorithm shows good performance and high security. It therefore can be used for key management and security functions, particularly for sharing sensitive files through insecure channel. This analysis provides a reference useful for selecting different encryption algorithms according to different business needs.", "Keywords": "AES Algorithm;RSA Algorithm;Encryption;Key Management", "DOI": "10.4236/jis.2021.122009", "PubYear": 2021, "Volume": "12", "Issue": "2", "JournalId": 26129, "JournalTitle": "Journal of Information Security", "ISSN": "2153-1234", "EISSN": "2153-1242", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic Information and Electrical Engineering, Dalian University of Technology, Dalian, China ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronic Information and Electrical Engineering, Dalian University of Technology, Dalian, China ."}], "References": []}, {"ArticleId": 87890739, "Title": "Coupling Adversarial Graph Embedding for transductive zero-shot action recognition", "Abstract": "Zero-shot action recognition (ZSAR) aims to recognize novel actions that have not been seen in the training stage. However, ZSAR always suffers from serious domain shift problem, which causes poor performance. This is because: 1) Videos contain complicated intrinsic structures, including cross-sample visual correlations and cross-category semantic relationships, which make it challenging to generalize domain shift over categories and transfer knowledge across videos. 2) Existing methods do not disentangle unique and shared information underlying unseen videos during embedding. They are always weakly adaptive to novel categories and easily shift unseen videos to irrelevant action prototypes. In this paper, we propose a novel Coupling Adversarial Graph Embedding (CAGE) method for ZSAR, which formulates an effective visual-to-semantic embedding to alleviate the domain shift problem. Our model implements in a transductive setting that assumes accessing to a full set of unseen videos. Firstly, a structured graph is built for expressing both seen and unseen videos, which integrally captures visual and semantic relationships between them. Then, an effective visual-to-semantic embedding is formulated based on graph convolutional network (GCN), which is generalized to disjoint action categories and optimized for label propagation. In addition, a couple of adversarial constraints are proposed to characterize unique information of unseen videos and purify shared information across categories, which further improve the adaptability and discriminability of our model. Experiments on Olympic sports, HMDB51 and UCF101 datasets show that our model achieves impressive performance on ZSAR task.", "Keywords": "Zero-shot learning ; Action recognition ; Graph convolutional network ; Generative adversarial network", "DOI": "10.1016/j.neucom.2021.04.031", "PubYear": 2021, "Volume": "452", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Institute of Information Science, Beijing Jiaotong University, China"}, {"AuthorId": 4, "Name": "Yu Kong", "Affiliation": "<PERSON><PERSON> <PERSON> College of Computing and Information Sciences, Rochester Institute of Technology, United States"}], "References": [{"Title": "Zero-shot learning for action recognition using synthesized features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "390", "Issue": "", "Page": "117", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 87890763, "Title": "Multi-photon 3D imaging with an electrothermal actuator with low thermal and inertial mass", "Abstract": "Design and testing of a low-mass electrothermal vertical scanning mirror for fast axial scanning during multi-photon microscopy is presented. The mirror makes use of a novel honeycomb mechanical support structure to substantially reduce both thermal and inertial mass relative to mirror size. At the same time, undesirable mirror curvature due to residual thermal stress is controlled without adversely affecting the image quality. In addition, the support structure makes the complete mirror area usable which otherwise is not possible in devices that rely on isotropic silicon etching. A high actuator efficiency with static displacement per unit power of 2.08 μm/mW was achieved due to the reduced thermal capacitance at the actuator legs, when using a 700 nm thin structural layer of SiO<sub>2</sub>. The resulting scanning mirror achieves open loop time constant as small as 3.42 ms with both thermal and mechanical bandwidths exceeding 100 Hz. Mirror surface non-idealities related to the mirror structure and their influence on image quality are discussed. Sample 3D multi-photon images captured by performing remote scanning in both lateral and axial direction on a benchtop testbed are presented. The efficacy of remote axial scan is ascertained by comparisons with images obtained by moving specimens relative to the objective.", "Keywords": "MEMS micromirror ; Electrothermal actuators ; Multi-photon images ; Endomicroscopy ; Honeycomb structure", "DOI": "10.1016/j.sna.2021.112791", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Michigan, Ann Arbor, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Bio-medical engineering, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Bio-medical engineering, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Michigan, Ann Arbor, USA;Department of Bio-medical engineering, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 7, "Name": "Kenn R<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Michigan, Ann Arbor, USA"}], "References": []}, {"ArticleId": 87890764, "Title": "Temperature compensation of the fiber-optic based system for the shape reconstruction of a minimally invasive surgical needle", "Abstract": "This paper presents the methodology for temperature compensation of a system for shape reconstruction of a minimally invasive surgical needle. The system is based on four optical fibers glued along the needle at 90 ∘ from each other and connected to the optical backscattering reflectometry interrogator. The interrogator measures backscattered light from four fibers, which shifts as a response to temperature or strain variations. During minimally invasive surgery the fibers sense both the strain change (due to the needle bending) and the temperature change (due to the difference between the temperature of the environment and the human body). Shape reconstruction is based on the strain measurements, so the temperature readings need to be compensated. The methodology of compensation is based on the two pairs of opposite fibers, which measure the same temperature change and opposite strain. The spectral shifts of the opposite fibers are added to find only the temperature component and the result is subtracted from the whole spectral shift to find the strain change detected by each fiber. This method has been validated by repeated constant insertion into a temperature-varying phantom. The algorithm has succeeded in the extraction of the strain component, which found to be the same in all trials despite the changing temperature.", "Keywords": "Distributed sensing ; Fiber optic sensors ; Medical needle ; Minimally invasive surgery ; OBR ; Shape reconstruction ; Temperature compensation", "DOI": "10.1016/j.sna.2021.112795", "PubYear": 2021, "Volume": "329", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Electrical Engineering, Nazarbayev University, Kabanbay Batyr, Nur-Sultan 010000, Kazakhstan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Electrical Engineering, Nazarbayev University, Kabanbay Batyr, Nur-Sultan 010000, Kazakhstan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Côte d’Azur, INPHYNI – CNRS UMR 7010, <PERSON><PERSON>, 06108 Nice, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer and Electrical Engineering, Nazarbayev University, Kabanbay Batyr, Nur-Sultan 010000, Kazakhstan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Electrical Engineering, Nazarbayev University, Kabanbay Batyr, Nur-Sultan 010000, Kazakhstan;Laboratory of Biosensors and Bioinstruments, National Laboratory of Astana, Kabanbay Batyr, Nur-Sultan 010000, Kazakhstan"}], "References": []}, {"ArticleId": 87890808, "Title": "A Secure and Lightweight Three-Factor Remote User Authentication Protocol for Future IoT Applications", "Abstract": "<p>With the booming integration of IoT technology in our daily life applications such as smart industrial, smart city, smart home, smart grid, and healthcare, it is essential to ensure the security and privacy challenges of these systems. Furthermore, time-critical IoT applications in healthcare require access from external parties (users) to their real-time private information via wireless communication devices. Therefore, challenges such as user authentication must be addressed in IoT wireless sensor networks (WSNs). In this paper, we propose a secure and lightweight three-factor (3FA) user authentication protocol based on feature extraction of user biometrics for future IoT WSN applications. The proposed protocol is based on the hash and XOR operations, including (i) a 3-factor authentication (i.e., smart device, biometrics, and user password); (ii) shared session key; (iii) mutual authentication; and (iv) key freshness. We demonstrate the proposed protocol’s security using the widely accepted Burrows–Abadi–<PERSON>ham (BAN) logic, Automated Validation of Internet Security Protocols and Applications (AVISPA) simulation tool, and the informal security analysis that demonstrates its other features. In addition, our simulations prove that the proposed protocol is superior to the existing related authentication protocols, in terms of security and functionality features, along with communication and computation overheads. Moreover, the proposed protocol can be utilized efficiently in most of IoT’s WSN applications, such as wireless healthcare sensor networks.</p>", "Keywords": "", "DOI": "10.1155/2021/8871204", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Huazhong University of Science and Technology, Wuhan, 400037 Hubei, China;Department of Mathematics, College of Science, University of Basrah, 61004, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Huazhong University of Science and Technology, Wuhan, 400037 Hubei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Medical Instrumentation Engineering Techniques Department, Al-Mustaqbal University College, 51001 Hillah, Babil, Iraq"}, {"AuthorId": 4, "Name": "Hongwei Lu", "Affiliation": "School of Computer Science, Huazhong University of Science and Technology, Wuhan, 400037 Hubei, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Education College of Pure Science, University of Basrah, 61004, Iraq"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Education College of Pure Science, University of Basrah, 61004, Iraq"}], "References": [{"Title": "Smart card-based secure authentication protocol in multi-server IoT environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "23-24", "Page": "15793", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Secure remote anonymous user authentication scheme for smart home environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "", "Page": "100158", "JournalTitle": "Internet of Things"}, {"Title": "A three-factor anonymous user authentication scheme for Internet of Things environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "", "Page": "102494", "JournalTitle": "Journal of Information Security and Applications"}]}, {"ArticleId": 87890819, "Title": "Hyperledger Fabric Endorsement Strategy Proposal Distribution Improvement Plan", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2021.114119", "PubYear": 2021, "Volume": "11", "Issue": "4", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "龙静 张", "Affiliation": ""}], "References": []}, {"ArticleId": 87891039, "Title": "The Effects of Magnetic Circuit Geometry and Material Properties on Surface Mounted Permanent Magnet Synchronous Generator Performance", "Abstract": "Permanent magnet synchronous generator is one of the generator types used in electrical energy production. In this study, a 1.5 kW inner rotor, surface located permanent magnet synchronous generator is used as a reference. The computer model of this generator has been prepared using Maxwell Rmxprt software. Rmxprt analysis of the generator has been performed and results were obtained for no-load and nominal load operating conditions. Parameters such as cogging torque which is an important parameter especially for permanent magnet synchronous generators used in wind turbines and efficiency were examined. The effects of changing in magnetic circuit as the permanent magnet pole arc to pole pitch ratio, magnet thickness and magnet material parameters on the generator performance were analyzed comparatively.", "Keywords": "Cogging torque,Efficiency,Permanent Magnet Synchronous Machines", "DOI": "10.17694/bajece.817766", "PubYear": 2021, "Volume": "9", "Issue": "2", "JournalId": 27857, "JournalTitle": "Balkan Journal of Electrical and Computer Engineering", "ISSN": "2147-284X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Tuğberk ÖZMEN", "Affiliation": "MANİSA CELÂL BAYAR ÜNİVERSİTESİ, MANİSA TEKNİK BİLİMLER MESLEK YÜKSEKOKULU, ELEKTRİK VE ENERJİ BÖLÜMÜ, ELEKTRİK PR."}, {"AuthorId": 2, "Name": "Nevzat ONAT", "Affiliation": "CELAL BAYAR UNIVERSITY, FACULTY OF ENGINEERING, DEPARTMENT OF ELECTRICAL AND <PERSON>LECTRONICS ENGINEERING"}], "References": [{"Title": "Comparative Design of Permanent Magnet Synchronous Motors for Low-Power Industrial Applications", "Authors": "Hicret YETİŞ; Taner GÖKTAŞ", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "218", "JournalTitle": "Balkan Journal of Electrical and Computer Engineering"}]}, {"ArticleId": 87891116, "Title": "A comparison study of fuzzy-based multiple-criteria decision-making methods to evaluating green concept alternatives in a new product development environment", "Abstract": "Purpose In this paper, the four popular multiple-criteria decision-making (MCDM) methods in fuzzy environment are utilized to reflect the vagueness and uncertainty on the judgments of decision-makers (DMs), because the crisp pairwise comparison in these conventional MCDM methods seems to be insufficient and imprecise to capture the right judgments of DMs. Of these methods, as Fuzzy analytic hierarchy process (F-AHP) is used to calculate criteria weights, the other methods; Fuzzy Technique for Order of Preference by Similarity to Ideal Solution (F-TOPSIS), Fuzzy Grey relational analysis (F-GRA) and Fuzzy Preference Ranking Organization METhod for Enrichment of Evaluations (F- PROMETHEE II) are used to rank alternatives in the three different ways for a comparative study. Design/methodology/approach The demand for green products has dramatically increased because the importance and public awareness of the preservation of natural environment was taken into consideration much more in the last two decades. As a result of this, especially manufacturing companies have been forced to design more green products, resulting in a problem of how they incorporate environmental issues into their design and evaluate concept options. The need for the practical decision-making tools to address this problem is rapidly evolving since the problem turns into an MCDM problem in the presence of a set of green concept alternatives and criteria. Findings The incorporation of fuzzy set theory into these methods is discussed on a real-life case study, and a comparative analysis is done by using its numerical results in which the three fuzzy-based methods reveal the same outcomes (or rankings), while F-GRA requires less computational steps. Moreover, more detailed analyses on the numerical results of the case study are completed on the normalization methods, distance metrics, aggregation functions, defuzzification methods and other issues. Research limitations/implications The designing and manufacturing environmental-friendly products in a product design process has been a vital issue for many companies which take care of reflecting environmental issues into their product design and meeting standards of recent green guidelines. These companies have utilized these guidelines by following special procedures at the design phase. Along the design process consisting of various steps, the environmental issues have been considered an important factor in the end-of-life of products since it can reduce the impact on the nature. In the stage of developing a new product with the aim of environmental-friendly design, the green thinking should be incorporated as early as possible in the process. Practical implications The case study was inspired from the previous work of the author, which was realized in a hot runner systems manufacturer, used in injection molding systems in a Canada. In a new product development process, the back- and front-ends of development efforts mainly determine the following criteria: cost, risk, quality and green used in this paper. The case study showed that the three fuzzy MCDM methods come to the same ranking outcomes. F-GRA has a better time complexity compared to the other two methods and uses a smaller number of computational steps. Moreover, a comparative analysis of the three F-MCDM methods; F-PROMETHEE II, F-TOPSIS and F-GRA used in ranking for green concept alternatives using the numerical results of the case study. For the case study; as seen in table 20, the three F-MCDM methods produced the numerical results on the rankings of the green concept alternatives as follows; {Concept A-Concept C–Concept B–Concept D}. Social implications Inclusion of environmental-related criteria into concept selection problem has been gaining increasing importance in the last decade. Therefore, to facilitate necessary calculations in applying each method especially with its fuzzy extension, it can be developed a knowledge-based (KB) or an expert system (ES) to help the DMs make the required calculations of each method, and interpret its results with detailed analysis. Originality/value The objective of the research was to propose a F-AHP based F-MCDM approach to green concept selection problem through F-PROMETHEE II, F-TOPSIS and F-GRA methods. As the F-AHP is used to weight evaluation criteria, the other methods are respectively used for ranking the concept alternatives and determine the best concept alternative.", "Keywords": "New product development;Green concept selection;Multiple-criteria decision making;Fuzzy logic;AHP;TOPSIS;GRA;PROMETHEE II", "DOI": "10.1108/IJICC-03-2021-0040", "PubYear": 2021, "Volume": "14", "Issue": "3", "JournalId": 26058, "JournalTitle": "International Journal of Intelligent Computing and Cybernetics", "ISSN": "1756-378X", "EISSN": "1756-3798", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>dir Has University , Istanbul, Turkey"}], "References": [{"Title": "TODIM-based multi-criteria decision-making method with hesitant fuzzy linguistic term sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3647", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A novel approach to emergency risk assessment using FMEA with extended MULTIMOORA method under interval-valued Pythagorean fuzzy environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "41", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}, {"Title": "Concept of Yager operators with the picture fuzzy set environment and its application to emergency program selection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "455", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}, {"Title": "Probabilistic uncertain linguistic TODIM method based on the generalized Choquet integral and its application", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "122", "JournalTitle": "International Journal of Intelligent Computing and Cybernetics"}]}, {"ArticleId": 87891141, "Title": "A daily-diary study on the effects of face-to-face communication, texting, and their interplay on understanding and relationship satisfaction", "Abstract": "There is conflicting literature regarding the associations between CMC communication (especially texting) and relationship outcomes in established romantic relationships. These discrepancies could be in part because of differences and imperfections in the methodology of earlier studies, which often study CMC in isolation from other forms of communication. To address this gap in the literature, we used a daily-diary study, in which we asked people to report on the time they spent in phone calls, video calls, and texting with their partner, as well as the time they spent face-to-face with their partners. Each day we also asked participants the extent to which they felt understood by their partner and satisfied with their relationships. Results of our multilevel analyses indicated that the more face-to-face communication participants had with their partners, the more understood they felt and the more satisfied they were with their relationship. Texting, by contrast, did not predict relationship satisfaction. Texting was positively associated with understanding, but only when face-to-face communication was relatively low. These findings differ from those found in cross-sectional studies and suggest that texting should be investigated in concert with other forms of communication, especially face-to-face communication.", "Keywords": "CMC ; Relationship satisfaction ; Understanding ; Texting", "DOI": "10.1016/j.chbr.2021.100088", "PubYear": 2021, "Volume": "3", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Corresponding author. Department of Communication and Cognition, Tilburg University, PO Box 90153, 5000 LE, Tilburg, the Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 87891157, "Title": "A high payload reversible data hiding algorithm for homomorphic encrypted absolute moment block truncation coding compressed images", "Abstract": "<p>With the aim to ensure privacy and security of secret message, an enhanced reversible data hiding algorithm in encrypted as well as compressed domain is proposed here. After compression of grey-scale image using absolute moment block truncation coding (AMBTC) method, pixels of AMBTC compressed image is segmented into two types - seed pixels (2<sup> k </sup>…255) and remaining pixels are called non-seed pixels. Now, embedded k , ( k ≥ 1) binary bits of secret message by changed over it into b a s e <sub>10</sub> numeral framework at seed pixels of AMBTC compressed image whereas seed pixel is divided into two individual units which are encrypted through Paillier cryptosytem separately. Highlight of proposed method is to embed variable size secret message at seed pixels of AMBTC compressed image without any occurrence of overflow problem. Experimental study revealed that for all type of test images, proposed method altogether beated all the compared methods in its ability to embed secret message and precisely recover it with a PSNR value of \\(\\infty \\) dB between cover image and reconstructed image too.</p>", "Keywords": "Absolute moment block truncation coding (AMBTC); Reversible data hiding in encrypted compressed images; Homomorphic encryption; Variable size secret message; Security analysis", "DOI": "10.1007/s11042-021-10722-5", "PubYear": 2021, "Volume": "80", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering Department, Thapar Institute of Engineering and Technology, Patiala, India"}], "References": [{"Title": "An improved block based joint reversible data hiding in encrypted images by symmetric cryptosystem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "60", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Adaptive and blind watermarking scheme based on optimal SVD blocks selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "Page": "243", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Robust and blind watermarking algorithm based on DCT and SVD in the contourlet domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7515", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Imperceptible digital watermarking scheme combining 4-level discrete wavelet transform with singular value decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "31-32", "Page": "22727", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 87891174, "Title": "Static Hybrid Multihop Relaying", "Abstract": "<p>In this work, we propose a new static hybrid mul- tihop relaying protocol where some relays amplify the received signal whereas the remaining ones use Decode and Forward (DF) relaying. The relaying mode in each relay is set using the distance between the different nodes or the average SNR. The exact and asymptotic bit error probabilities are derived. Simulation results are provided in different contexts to compare the performance of hybrid relaying to conventional AF and DF relaying.</p>", "Keywords": "", "DOI": "10.46300/9108.2021.15.7", "PubYear": 2021, "Volume": "15", "Issue": "", "JournalId": 75453, "JournalTitle": "International Journal of Computers", "ISSN": "", "EISSN": "1998-4308", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "King <PERSON><PERSON> University, Kingdom of Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Carthage, Sup’Com, COSIM Laboratory, Tunisia"}], "References": []}, {"ArticleId": 87891619, "Title": "Analysis Of JD Commodity Evaluation Word Cloud Based On Web Crawler", "Abstract": "", "Keywords": "", "DOI": "10.35444/IJANA.2021.12502", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 65085, "JournalTitle": "International Journal of Advanced Networking and Applications", "ISSN": "0975-0290", "EISSN": "0975-0282", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>-yu", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Evaluation of Residual Stress in Die Castings of Al-Si-Cu Alloy Considering Material Composition Change in Thickness Direction", "Abstract": "<p>This study investigated a method for accurately predicting the residual stress in die castings manufactured using aluminum alloy. To account for the mechanical properties caused by the material composition differences that occur in the thickness direction of the die castings, a model split in the thickness direction was used in the simulation model. <PERSON>’s law was applied to the constitutive equation of the material, and the stress relaxation phenomenon was examined. The composition of Al-Si-Cu alloy (JIS-ADC12) die castings in the thickness direction were analyzed using scanning electron microscopy and energy dispersive X-ray spectroscopy (SEM-EDS), and differences in composition were confirmed. As a result of calculating the residual stress using the simulation, it was possible to calculate the residual stress that could not be reproduced by the simulation model of uniform composition. This suggested that the difference in mechanical properties of die castings in the micro-region influences the residual stress.</p>", "Keywords": "die-casting;aluminum alloy;residual stress;segregation;FEM", "DOI": "10.20965/ijat.2021.p0359", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Faculty of Engineering, Gifu University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Science and Processing, Gifu University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Gifu Prefectural Industrial Technology Center"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Faculty of Engineering, Gifu University"}], "References": [{"Title": "Solid Fraction Examination at Flow Cessation and Flow Cessation Mechanism of Al-Si-Mg Alloy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "5", "Page": "835", "JournalTitle": "International Journal of Automation Technology"}]}, {"ArticleId": 87891768, "Title": "Design of an Arc-Core Moving Mechanism for Injection Molding Using a Link and Cam Mechanism", "Abstract": "<p>A novel plastic injection mold design for a product with a deep arc-hole based on the kinematic analysis is proposed. To move the wide angle arc-core, a hybrid mechanism combining a slider-crank and a swing cam is designed. The force transmission coefficient of the slider-crank and the pressure angle of the swing cam are used to evaluate the dimensions of the mechanism. The motion of the designed mechanism was confirmed by using a prototype made with a 3D printer.</p>", "Keywords": "mold design;arc-core moving mechanism;slider-crank;cam;kinematics", "DOI": "10.20965/ijat.2021.p0366", "PubYear": 2021, "Volume": "15", "Issue": "3", "JournalId": 8593, "JournalTitle": "International Journal of Automation Technology", "ISSN": "1881-7629", "EISSN": "1883-8022", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Faculty of Science and Engineering, Kindai University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Kindai University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujitsuka Seimitsu Kanagata Co., Ltd"}], "References": []}, {"ArticleId": 87892324, "Title": "Detection of HEVC double compression with non-aligned GOP structures via inter-frame quality degradation analysis", "Abstract": "Detection of double compression is of great significance to reveal video compression history. However, efficient detection algorithm for High Efficiency Video Coding (HEVC) double compression with non-aligned Group of Pictures (GOP) structures is rarely reported. To address this issue, a novel algorithm based on inter-frame quality degradation process analysis is proposed in this paper. Firstly, a brief introduction to HEVC standard and double compression with non-aligned GOP structures is provided. Next, theoretical analysis of inter-frame quality degradation process is given. Then, from the perspective of filtering decision, the inter-frame In-Loop Filtering Decision Mode (ILFDM) feature is adopted for double compression detection. Finally, the detection framework is given, which includes feature extraction, feature combination and periodic analysis. Experiments are conducted under different coding scenarios, by comparing with existing algorithms, results demonstrate that the proposed algorithm has better performance not only in terms of double compression detection, but also in original GOP size estimation. Furthermore, the proposed scheme is proved to be satisfactory in relocated I-frame detection.", "Keywords": "HEVC ; Double compression detection ; In-loop filtering ; GOP size estimation ; Relocated I-frame detection", "DOI": "10.1016/j.neucom.2021.04.092", "PubYear": 2021, "Volume": "452", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China;National Engineering Lab on Information Content Analysis Techniques, GT036001 Shanghai 200240, China"}, {"AuthorId": 2, "Name": "Xing<PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China;National Engineering Lab on Information Content Analysis Techniques, GT036001 Shanghai 200240, China"}, {"AuthorId": 3, "Name": "Tanfeng Sun", "Affiliation": "School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai 200240, China;National Engineering Lab on Information Content Analysis Techniques, GT036001 Shanghai 200240, China;Corresponding author ar: School of Electronic Information and Electrical Engineering, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Nanyang Technological University, 637553, Singapore"}], "References": []}, {"ArticleId": 87892334, "Title": "Index of Vol. 27 (2020)", "Abstract": "", "Keywords": "", "DOI": "10.1142/S1230161220990012", "PubYear": 2020, "Volume": "27", "Issue": "4", "JournalId": 13217, "JournalTitle": "Open Systems & Information Dynamics", "ISSN": "1230-1612", "EISSN": "1793-7191", "Authors": [], "References": []}, {"ArticleId": 87892350, "Title": "Hierarchical consensus problem in hybrid multiagent systems", "Abstract": "<p>This paper considers a hierarchical consensus problem in hybrid multiagent systems. By partitioning the agents into several groups, each group contains a value concerning all the state information inside the group, which is called group information. For each single agent, its control input includes not only the inner-group neighboring agent information but also the group information from its neighboring groups. Moreover, we discuss two kinds of hybrid systems composing of both continuous-time and discrete-time dynamic information and propose the corresponding control laws. Finally, simulations are provided to demonstrate the effectiveness of our theoretical results.</p>", "Keywords": "consensus;hierarchical structure;hybrid dynamic systems;multiagent systems", "DOI": "10.1002/asjc.2576", "PubYear": 2022, "Volume": "24", "Issue": "4", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science & Technology Zhejiang Sci‐Tech University  Hangzhou China;School of Electrical Engineering and Computing The University of Newcastle  Callaghan NSW Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science & Technology Zhejiang Sci‐Tech University  Hangzhou China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing Aerospace Automatic Control Institute  Beijing China;School of Automation Science and Electrical Engineering Beihang University  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science & Technology Zhejiang Sci‐Tech University  Hangzhou China"}], "References": []}, {"ArticleId": 87892453, "Title": "Wheat Head Detection using Deep, Semi-Supervised and Ensemble Learning", "Abstract": "In this paper, we propose an object detection methodology applied to Global Wheat Head Detection (GWHD) Dataset. We have been through two major architectures of object detection which are Faster R-CNN, and EfficientDet, in order to design a novel and robust wheat head detection model. We emphasize on optimizing the performance of our proposed final architectures. Furthermore, we have been through an extensive exploratory data analysis, data cleaning, data splitting and adapted best data augmentation techniques to our context. We use semi supervised learning, precisely pseudo-labeling, to boost previous supervised models of object detection. Moreover, we put much effort on ensemble learning including test time augmentation, multi-scale ensemble and bootstrap aggregating to achieve higher performance. Finally, we use weighted boxes fusion as our post processing technique to optimize our wheat head detection results. Our solution has been submitted to solve a research challenge launched on the GWHD Dataset which was led by nine research institutes from seven countries. Our proposed method was ranked within the top 6% in the above-mentioned challenge. Dans cet article, nous proposons une méthodologie originale de détection d'objets appliquée à la détection automatique de têtes de blé dans une base de données nommée GWHD créée et préparée en 2020 par un ensemble de chercheurs à travers le monde. Nous avons adopté deux architectures majeures de détection d'objets, Faster R-CNN et EfficientDet, afin de concevoir un nouveau modèle robuste de détection des têtes de blé. De plus, nous avons mis l’accent sur l’optimization des performances des architectures proposées. Pour ce faire, nous avons effectué une analyze approfondie des données disponibles, nous avons procédé au nettoyage des données et au fractionnement des données. Nous avons mis en place un apprentissage semi-supervisé et précisément une technique de “pseudo-étiquetage” pour booster les précédents modèles supervisés de détection d'objets. Par ailleurs, nous avons investi beaucoup d'efforts sur les techniques d’apprentissage du type ensemble, notamment l’augmentation de la durée des tests, les ensembles multi-échelles et l'agrégation “bootstrap” afin de garantir de meilleures performances. Enfin, nous utilisons des techniques de post-traitement spécifiques comme la fusion de régions pondérées pour optimizer nos résultats de détection des têtes de blé. Ces solutions ont été proposées pour répondre à un défi de recherche lancé sur l'ensemble de données GWHD et géré par neuf instituts de recherche dans sept pays du monde. La méthode que nous proposons dans cet article a été classée parmi les 6% meilleures solutions au défi susmentionné. Disclosure Statement No potential conflict of interest was reported by the author(s).", "Keywords": "", "DOI": "10.1080/07038992.2021.1906213", "PubYear": 2021, "Volume": "47", "Issue": "2", "JournalId": 4152, "JournalTitle": "Canadian Journal of Remote Sensing", "ISSN": "0703-8992", "EISSN": "1712-7971", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SERCOM Laboratory, Ecole Polytechnique de Tunisie, University of Carthage, Tunis, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON>d <PERSON><PERSON><PERSON>", "Affiliation": "SERCOM Laboratory, Ecole Polytechnique de Tunisie, University of Carthage, Tunis, Tunisia;L2TI, Institut Galilée, Université Sorbonne Paris Nord, Villetaneuse, France"}, {"AuthorId": 3, "Name": "Rabah <PERSON>", "Affiliation": "SERCOM Laboratory, Ecole Polytechnique de Tunisie, University of Carthage, Tunis, Tunisia"}], "References": [{"Title": "Albumentations: Fast and Flexible Image Augmentations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "125", "JournalTitle": "Information"}]}, {"ArticleId": 87892464, "Title": "Comparison of PO and INC MPPT Methods Using FPGA In-The-Loop Under Different Radiation Conditions", "Abstract": "The Maximum Power Point Tracking (MPPT) algorithms are applied to obtain maximum efficiency under different atmospheric conditions in photovoltaic (PV) systems. Perturb&amp;Observe (PO) and Incremental Conductance (INC) methods are the oldest algorithms used among MPPT methods. Field Programmable Gate Arrays (FPGA) are used especially in applications requiring high speed. FPGA in-the-loop feature is used to test algorithms designed in MATLAB/Simulink environment. In this study, PO and INC methods have been designed to work in FPGA environment. Both algorithms have been tested under different radiation conditions using FPGA-in-the-loop feature. The FPGA in-the-loop simulation result of PO and INC methods has been graphically shown. In this study, Altera DE2-115 development board was used to test PO and INC MPPT algorithms. On the other hand, PO and INC methods were synthesized using the Quartus-II program. Comparisons of simplicity of algorithms were made according to synthesis results. Thus, by using the FGPA in-the-loop feature and performing the synthesis process, both the algorithms were tested and the areas covered by the algorithms in the FPGA were compared.", "Keywords": "Solar Energy,PV systems,MPPT Algorithms,FPGA,Perturb and Observe,Incremental Conductance,Photovoltaic Energy Conversion", "DOI": "10.17694/bajece.884815", "PubYear": 2021, "Volume": "9", "Issue": "2", "JournalId": 27857, "JournalTitle": "Balkan Journal of Electrical and Computer Engineering", "ISSN": "2147-284X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Reşat ÇELİKEL", "Affiliation": "BATMAN UNIVERSITY"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> GÜNDOĞDU", "Affiliation": "BATMAN ÜNİVERSİTESİ"}], "References": [{"Title": "FPGA based implementation of MPPT algorithms for photovoltaic system under partial shading conditions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "77", "Issue": "", "Page": "103011", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 87892528, "Title": "Fighting Deepfakes Using Body Language Analysis", "Abstract": "<p>Recent improvements in deepfake creation have made deepfake videos more realistic. Moreover, open-source software has made deepfake creation more accessible, which reduces the barrier to entry for deepfake creation. This could pose a threat to the people’s privacy. There is a potential danger if the deepfake creation techniques are used by people with an ulterior motive to produce deepfake videos of world leaders to disrupt the order of countries and the world. Therefore, research into the automatic detection of deepfaked media is essential for public security. In this work, we propose a deepfake detection method using upper body language analysis. Specifically, a many-to-one LSTM network was designed and trained as a classification model for deepfake detection. Different models were trained by varying the hyperparameters to build a final model with benchmark accuracy. We achieved 94.39% accuracy on the deepfake test set. The experimental results showed that upper body language can effectively detect deepfakes.</p>", "Keywords": "imaging; machine learning; deepfake; human pose estimation; upper body language; computer vision; deep learning; Recurrent Neural Networks (RNNs); Long Short-Term Memory (LSTM) imaging ; machine learning ; deepfake ; human pose estimation ; upper body language ; computer vision ; deep learning ; Recurrent Neural Networks (RNNs) ; Long Short-Term Memory (LSTM)", "DOI": "10.3390/forecast3020020", "PubYear": 2021, "Volume": "3", "Issue": "2", "JournalId": 54589, "JournalTitle": "Forecasting", "ISSN": "", "EISSN": "2571-9394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering Science, Institute of Biomedical Engineering, University of Oxford, Oxford OX3 7DQ, UK↑Author to whom correspondence should be addressed. Academic Editors: <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham, Nottingham NG8 1BB, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Dalian University of Technology, Dalian 116024, China"}], "References": [{"Title": "Monocular human pose estimation: A survey of deep learning-based methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "102897", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Deepfakes and beyond: A Survey of face manipulation and fake detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "131", "JournalTitle": "Information Fusion"}, {"Title": "The Creation and Detection of Deepfakes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 87892589, "Title": "Stochastic programming approach for static origin–destination matrix reconstruction problem", "Abstract": "We propose a stochastic programming approach for a static origin–destination (OD) reconstruction problem. We focus on the reconstruction of route flows such that the likelihood function of route flows is maximized. The route volumes are assumed to follow exponential families that are known or estimated in advance. The consideration of the joint distribution function of route flows eliminates the route selection from the model, as the route choice patterns are embedded in the distribution of the route flows. We assume that additional information regarding the traffic counts (i.e., node counts, link counts, and turn counts) is available. Finally, solution methodologies for different stochastic programmings are proposed: barrier method and Primal-dual interior point method for Quadratic Programming and Convex Programming respectively. We compared the proposed stochastic models with the entropy approach. Experimental results indicate that the inclusion of traffic-count information in the stochastic model significantly improves the accuracy of OD reconstruction if we can predict the correct distribution of route flows. Meanwhile the entropy approach requires the inclusion of the additional information on the true volumes of route flows to achieve a similar level of performance. We apply the proposed algorithm to the bus transit system of Seoul, Korea using bus-card data. Compared with the real OD volumes, the reconstruction is fairly accurate.", "Keywords": "Barrier method ; Convex programming ; Exponential family ; OD estimation", "DOI": "10.1016/j.cie.2021.107373", "PubYear": 2021, "Volume": "157", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Hanyang University, Seoul 133-791, Republic of Korea"}, {"AuthorId": 2, "Name": "Dongjoo Park", "Affiliation": "Department of Transportation Engineering, University of Seoul, Republic of Korea;Corresponding author"}], "References": []}, {"ArticleId": 87892636, "Title": "Colocation for SLAM-Tracked VR Headsets with Hand Tracking", "Abstract": "<p>In colocated multi-user Virtual Reality applications, relative user positions in the virtual environment need to match their relative positions in the physical tracking space. A mismatch between virtual and real relative user positions might lead to harmful events such as physical user collisions. This paper examines three calibration methods that enable colocated Virtual Reality scenarios for SLAM-tracked head-mounted displays without the need for an external tracking system. Two of these methods—fixed-point calibration and marked-based calibration—have been described in previous research; the third method that uses hand tracking capabilities of head-mounted displays is novel. We evaluated the accuracy of these three methods in an experimental procedure with two colocated Oculus Quest devices. The results of the evaluation show that our novel hand tracking-based calibration method provides better accuracy and consistency while at the same time being easy to execute. The paper further discusses the potential of all evaluated calibration methods.</p>", "Keywords": "colocation; multi-user VR; hand tracking; shared space colocation ; multi-user VR ; hand tracking ; shared space", "DOI": "10.3390/computers10050058", "PubYear": 2021, "Volume": "10", "Issue": "5", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty for Electrical Engineering and Computer Science, Ravensburg-Weingarten University, 88250 Weingarten, Germany↑Institute of Visual Computing and Human-Centered Technology, TU Wien, 1040 Vienna, Austria↑Author to whom correspondence should be addressed. Academic Editors: <PERSON> and <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Visual Computing and Human-Centered Technology, TU Wien, 1040 Vienna, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty for Electrical Engineering and Computer Science, Ravensburg-Weingarten University, 88250 Weingarten, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Visual Computing and Human-Centered Technology, TU Wien, 1040 Vienna, Austria"}], "References": []}, {"ArticleId": 87892710, "Title": "A motif building process for simulating random networks", "Abstract": "A simple stochastic process is described which provides a useful basis for generating some types of random networks. The process is based on an iterative building block technique that uses a motif profile as a conditional probability model. The conditional iterative form of the algorithm insures that the calculations required to simulate an observed random network are relatively simple and does not require complicated models to be fit to an observed network. Bounds on the theoretical cohesiveness of the realized networks are established and empirical studies provide indications on more general properties of the resulting network, suggesting the types of applications where the process would be useful. The algorithm is used to generate networks similar to those observed in several examples.", "Keywords": "Markov process ; Network statistics ; Random graphs", "DOI": "10.1016/j.csda.2021.107263", "PubYear": 2021, "Volume": "162", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Statistics and Actuarial Science, Northern Illinois University, De Kalb, IL 60115, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>ramanik", "Affiliation": "Department of Statistics and Actuarial Science, Northern Illinois University, De Kalb, IL 60115, USA"}], "References": [{"Title": "A multilayer exponential random graph modelling approach for weighted networks", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106825", "JournalTitle": "Computational Statistics & Data Analysis"}]}, {"ArticleId": 87892748, "Title": "A Survey of Privacy Solutions using Blockchain for Recommender Systems: Current Status, Classification and Open Issues", "Abstract": "<p>Due to the rapid growth of Internet, E-commerce and Internet of Things, people use Web based services for most of their needs including buying items, reading books, watching online shows etc. Several companies are using recommender systems to influence people’s choices based on their likings, behaviours etc. Hence, people fear that their privacy is violated. Also, some of the online applications are not safe and secure. One way to overcome the privacy related issues is using secured solutions such as incorporating blockchain technologies for privacy-based applications. The decentralized nature of blockchain technologies have resolved several security, and authentication problems of Internet of Things systems. In this paper, we conduct a comprehensive survey on the privacy solutions for recommender systems emphasising current status, classification and open issues. We also discuss blockchain technology, including its structure as well as applications of blockchain technology for privacy solutions of recommender systems. Furthermore, we discuss the limitations and delve into future trends that blockchain technology can be adapted for privacy-base applications in the years to come.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxab065", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Sciences, University of Southern Queensland, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Sciences, University of Southern Queensland, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Sciences, University of Southern Queensland, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, University of Southern Queensland, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Technology, China"}, {"AuthorId": 6, "Name": "<PERSON>ai", "Affiliation": "South China University of Technology, Guangzhou, China"}], "References": [{"Title": "A survey on the security of blockchain systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "841", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A personal data store approach for recommender systems: enhancing privacy without sacrificing accuracy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112858", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Am I eclipsed? A smart detector of eclipse attacks for Ethereum", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Chunhua Su", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "101604", "JournalTitle": "Computers & Security"}, {"Title": "Blockchain-based data privacy management with Nudge theory in open banking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Hong-<PERSON>ng <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "812", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Recommender systems and their ethical challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "4", "Page": "957", "JournalTitle": "AI & SOCIETY"}, {"Title": "BDKM: A Blockchain-Based Secure Deduplication Scheme with Reliable Key Management", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "2657", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": ********, "Title": "Quasi-3D large deflection nonlinear analysis of isogeometric FGM microplates with variable thickness via nonlocal stress–strain gradient elasticity", "Abstract": "<p>Via the nonlocal stress–strain gradient continuum mechanics, the microscale-dependent linear and nonlinear large deflections of transversely loaded composite sector microplates with different thickness variation schemes are investigated. Microplates are assumed to be prepared from functionally graded materials (FGMs) the characteristics of which are changed along the thickness direction. A quasi-3D plate theory with a sinusoidal transverse shear function in conjunction with a trigonometric normal function was employed for the establishment of size-dependent modelling of FGM microplates with different thickness variation schemes. Then, to solve the nonlocal stress–strain gradient flexural problem, the non-uniform rational B-spline type of isogeometric solution methodology was applied for an accurate integration of geometric discerptions. It was found that the gap between load–deflection curves drawn for linear, concave and convex thickness variation patterns became greater by changing FGM composite microplate boundary conditions from clamped to simply supported. In addition, it was found that by considering only the nonlocal size effect, the plate deflection obtained by the nonlocal strain gradient quasi-3D plate model was greater than that extracted by the classical continuum elasticity because of the softening character of nonlocal size effect, while the strain gradient microstructural size dependency acted in opposite way and represented a stiffening character.</p>", "Keywords": "Nonlocal stress effect; Nonlinear flexural response; Normal shape function; Thickness variation; Elliptical plates", "DOI": "10.1007/s00366-021-01390-y", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Basic Courses, Jiaozuo University, Jiaozuo, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Rotating Equipment Department, Niroo Research Institute (NRI), Tehran, Iran"}, {"AuthorId": 3, "Name": "Baba<PERSON>", "Affiliation": "Department of Mechanical Engineering, Eastern Mediterranean University, Famagusta, Turkey"}], "References": [{"Title": "Postbuckling analysis of hydrostatic pressurized FGM microsized shells including strain gradient and stress-driven nonlocal effects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1549", "JournalTitle": "Engineering with Computers"}, {"Title": "Nonlinear secondary resonance of FG porous silicon nanobeams under periodic hard excitations based on surface elasticity theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1611", "JournalTitle": "Engineering with Computers"}, {"Title": "Couple stress-based dynamic stability analysis of functionally graded composite truncated conical microshells with magnetostrictive facesheets embedded within nonlinear viscoelastic foundations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1635", "JournalTitle": "Engineering with Computers"}, {"Title": "Size-dependent nonlinear bending behavior of porous FGM quasi-3D microplates with a central cutout based on nonlocal strain gradient isogeometric finite element modelling", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1657", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 87892909, "Title": "Hyperspectral multi-level image thresholding using qutrit genetic algorithm", "Abstract": "Hyperspectral images contain rich spectral information about the captured area. Exploiting the vast and redundant information, makes segmentation a difficult task. In this paper, a Qutrit Genetic Algorithm is proposed which exploits qutrit based chromosomes for optimization. Ternary quantum logic based selection and crossover operators are introduced in this paper. A new qutrit based mutation operator is also introduced to bring diversity in the off-springs. In the preprocessing stage two methods, called Interactive Information method and Band Selection Convolutional Neural Network are used for band selection. The modified Otsu Criterion and Masi entropy are employed as the fitness functions to obtain optimum thresholds. A quantum based disaster operation is applied to prevent the quantum population from getting stuck in local optima. The proposed algorithm is applied on the Salinas Dataset, the Pavia Centre Dataset and the Indian Pines dataset for experimental purpose. It is compared with classical Genetic Algorithm, Particle Swarm Optimization, Ant Colony Optimization, Gray Wolf Optimizer, Harris Hawk Optimization, Qubit Genetic Algorithm and Qubit Particle Swarm Optimization to establish its effectiveness. The peak signal-to-noise ratio and S ø rensen-Dice Similarity Index are applied to the thresholded images to determine the segmentation accuracy. The segmented images obtained from the proposed method are also compared with those obtained by two supervised methods, viz., U-Net and Hybrid Spectral Convolutional Neural Network. In addition to this, a statistical superiority test, called the one-way ANOVA test, is also conducted to judge the efficacy of the proposed algorithm. Finally, the proposed algorithm is also tested on various real life images to establish its diversity and efficiency.", "Keywords": "Hyperspectral image thresholding ; Quantum genetic algorithm ; Quantum mutation operator ; Multilevel quantum systems ; Qutrit", "DOI": "10.1016/j.eswa.2021.115107", "PubYear": 2021, "Volume": "181", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Assam University, Silchar, Assam, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON><PERSON>, D<PERSON><PERSON>guri, Jalpaiguri, West Bengal, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Christ University, Bangalore, Karnataka, India;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Assam University, Silchar, Assam, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Techno India NJR Institute of Technology, Udaipur, Rajasthan, India"}], "References": [{"Title": "Quantum and classical genetic algorithms for multilevel segmentation of medical images: A comparative study", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "83", "JournalTitle": "Computer Communications"}, {"Title": "A medical analytical system using intelligent fuzzy level set brain image segmentation based on improved quantum particle swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "", "Page": "103283", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 87892916, "Title": "Multirobot coordination with deep reinforcement learning in complex environments", "Abstract": "In the multiple autonomous robot system, it is very important to complete path planning coordinately and effectively in the processes of interference avoidance, resource allocation and information sharing. In traditional multirobot coordination algorithms, most of the solutions are in known environments, the target position that each robot needs to move to and the robot priority are set, which limits the autonomy of the robot. Only using visual information to solve the problem of multirobot coordination is still less. This paper proposes a multi-robot cooperative algorithm based on deep reinforcement learning to make the robot more autonomous in the process of selecting target positions and moving. We use the end-to-end approach, using only the top view, that is, a robot-centered top view, and the first-person view, that is, the image information collected from the first-person perspective of the robot, as input. The proposed algorithm, which includes a dueling neural network structure, can solve task allocation and path planning; we call the algorithm TFDueling. Through its perception and understanding of the environment, the robot can reach the target position without collision, and the robot can move to any target position. We compare the proposed algorithm, TFDueling, with different input structure algorithms, TDueling and FDueling, and with different neural network structures, TFDQN and TFDDQN. Experiments show that the proposed TFDueling algorithm has the highest accuracy and robustness.", "Keywords": "Multirobot coordination ; Reinforcement learning ; Deep learning ; Visual perception", "DOI": "10.1016/j.eswa.2021.115128", "PubYear": 2021, "Volume": "180", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing 10081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing 10081, China;Corresponding author"}], "References": [{"Title": "MRCDRL: Multi-robot coordination with deep reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Pan", "PubYear": 2020, "Volume": "406", "Issue": "", "Page": "68", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 87893277, "Title": "The OpenMP API for High Integrity Systems", "Abstract": "<p>OpenMP is traditionally focused on boosting performance in HPC systems. However, other domains are showing an increasing interest in the use of OpenMP by virtue of key aspects introduced in recent versions of the specification: the tasking model, the accelerator model, and other features like the requires and the assumes directives, which allow defining certain contracts. One example is the safety-critical embedded domain, where several efforts have been initiated towards the adoption of OpenMP. However, the OpenMP specification states that \"application developers are responsible for correctly using the OpenMP API to produce a conforming program\", being not acceptable in high integrity systems, where aspects such as reliability and resiliency have to be ensured at different levels of criticality. In this scope, programming languages like Ada propose a different paradigm by exposing fewer features to the user, and leaving the responsibility of safely exploiting the full underlying architecture to the compiler and the runtime systems, instead. The philosophy behind this kind of model is to move the responsibility of producing correct parallel programs from users to vendors.</p><p>In this panel, actors from different domains involved in the use of parallel programming models for the development of high-integrity systems share their thoughts about this topic.</p>", "Keywords": "ada; cps; openmp; productivity; safety", "DOI": "10.1145/3463478.3463480", "PubYear": 2021, "Volume": "40", "Issue": "2", "JournalId": 12096, "JournalTitle": "ACM SIGAda Ada Letters", "ISSN": "1094-3641", "EISSN": "1557-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "OpenMP ARB, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Barcelona Supercomputing Center, Barcelona, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "AdaCore, Lexington, MA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Bosch, Renningen, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Barcelona Supercomputing Center, Barcelona, Spain"}], "References": []}, {"ArticleId": 87893317, "Title": "iMRobot: an innovative approach for new media and communication", "Abstract": "This paper reports on an approach for new media and communication by using some cutting-edge technologies which are integrated in an intelligent robot. Intelligent media robot (iMRobot) is designed and developed for facilitating the media operations in the context of Media 4.0 where advanced technologies are used to upgrade and transform the traditional media industry into a smart generation. Some cutting-edge technologies are used for building the iMRobot which is specifically suitable for the media sector. A demonstrative implementation case is illustrated to show the feasibility and streamlined processes in Media 4.0 by using iMRobot's full functionalities. The ideas are significant to improve the modernisation of Media 4.0 in the future. © 2021 Inderscience Enterprises Ltd.. All rights reserved.", "Keywords": "Artificial intelligence; Communication; Information system; Media 4.0; New media; Robot", "DOI": "10.1504/IJIMS.2021.114540", "PubYear": 2021, "Volume": "8", "Issue": "1", "JournalId": 26851, "JournalTitle": "International Journal of Internet Manufacturing and Services", "ISSN": "1751-6048", "EISSN": "1751-6056", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hww Nz Limited, 1A/6 Viaduct Harbour Avenue, CBD Auckland, 1010, New Zealand"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Pokfulam Road, Hong Kong; Hong Kong"}], "References": []}, {"ArticleId": 87893361, "Title": "A systematic overview on the risk effects of psychosocial work characteristics on musculoskeletal disorders, absenteeism, and workplace accidents", "Abstract": "<p>The present article provides a systematic overview on the relationship between psychosocial work characteristics and musculoskeletal disorders, absenteeism, and workplace accidents. The study identified and reviewed the findings of 24 systematic reviews or meta-analysis and 6 longitudinal studies. Publications were systematically searched in several databases from 1966 to January 2021. To summarize the level of evidence, a best evidence synthesis was performed, and the quality of included studies was rated. High job demands, high job strain, high effort/reward-imbalance and low social support showed a strong evidence to increase the risk for musculoskeletal disorders. In addition to job demands and job strain, low perceived fairness proved to be a risk factor of absenteeism with strong evidence. Due to the small number of studies, no reliable evidence assessment for workplace accidents was possible. The summarized findings can improve risk assessment methods, by providing a systematic estimation of the potential risk severity of psychosocial work characteristics and assist practitioners in further developing the psychosocial risk assessment.</p><p>Copyright © 2021 Elsevier Ltd. All rights reserved.</p>", "Keywords": "Psychosocial work characteristics;Risk assessment;Stress", "DOI": "10.1016/j.apergo.2021.103434", "PubYear": 2021, "Volume": "95", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Psychology, Department of Work and Organizational Psychology, University of Duisburg-Essen, Universitätsstraße 2, 45141, Essen, Germany. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Psychology, Department of Work and Organizational Psychology, University of Duisburg-Essen, Universitätsstraße 2, 45141, Essen, Germany; Department of Occupational Medicine, Occupational Safety and Health, Thyssenkrupp Steel Europe AG, Kaiser-Wilhelm-Straße 100, 47166, Duisburg, Germany. Electronic address:  ."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Psychology, Department of Work and Organizational Psychology, University of Duisburg-Essen, Universitätsstraße 2, 45141, Essen, Germany. Electronic address:  ."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Psychology, Department of Work and Organizational Psychology, University of Duisburg-Essen, Universitätsstraße 2, 45141, Essen, Germany. Electronic address:  ."}], "References": []}, {"ArticleId": 87893370, "Title": "Interval-Valued Intuitionistic Fuzzy-Analytic Hierarchy Process for evaluating the impact of security attributes in Fog based Internet of Things paradigm", "Abstract": "Internet of Things (IoT) may be defined as a network of smart devices that are involved in data collection and exchange. This technology has automated the day-to-day jobs and thus made our lives easier. But, real-time analysis of data is not always possible in a typical cloud-IoT architecture, especially for latency-sensitive applications. This led to the introduction of fog computing. On one side, fog layer has the capability of data processing and computation at the network edge and thus provides faster results. But, on the other hand, it also brings the attack surface closer to the devices. This makes the sensitive data on the layer vulnerable to attacks. Thus, considering Fog-IoT security is of prime importance. The security of a system or platform depends upon multiple factors. The order of selection of these factors plays a vital role in efficient assessment of security. This makes the problem of assessment of Fog-IoT security a Multi-Criteria Decision-Making (MCDM) problem. Therefore, the authors have deployed an Interval-Valued Intuitionistic Fuzzy Set (IVIFS) based Analytical Hierarchy Process (AHP) for the said environment. Using this integrated approach, the Fog-IoT security factors and their sub-factors are prioritized and ranked. The results obtained using above hybrid approach are validated by comparing them with Fuzzy-AHP (F-AHP) and Classical- AHP (C-AHP) results and are found to statistically correlated. The ideology and results of this research will help the security practitioners in accessing the security of Fog-IoT environment effectively. Moreover, the outcome of this analysis will help in paving a path for researchers by shifting their focus towards the most prioritized factor thereby assuring security in the environment.", "Keywords": "Fog computing ; IoT ; Fog-IoT security ; Interval-valued intuitionistic fuzzy ; AHP", "DOI": "10.1016/j.comcom.2021.04.019", "PubYear": 2021, "Volume": "175", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON><PERSON><PERSON> (A Central University), Lucknow, 226025, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON><PERSON><PERSON><PERSON> University (A Central University), Lucknow, 226025, India"}], "References": [{"Title": "Solving cloud vendor selection problem using intuitionistic fuzzy decision framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "589", "JournalTitle": "Neural Computing and Applications"}, {"Title": "IIVIFS-WASPAS: An integrated Multi-Criteria Decision-Making perspective for cloud service provider selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "91", "JournalTitle": "Future Generation Computer Systems"}]}]