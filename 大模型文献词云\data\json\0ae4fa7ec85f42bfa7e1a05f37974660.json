[{"ArticleId": 80252659, "Title": "Interpretable policies for reinforcement learning by empirical fuzzy sets", "Abstract": "This paper proposes a method and an algorithm to implement interpretable fuzzy reinforcement learning (IFRL). It provides alternative solutions to common problems in RL, like function approximation and continuous action space. The learning process resembles that of human beings by clustering the encountered states, developing experiences for each of the typical cases, and making decisions fuzzily. The learned policy can be expressed as human-intelligible IF-THEN rules, which facilitates further investigation and improvement. It adopts the actor–critic architecture whereas being different from mainstream policy gradient methods. The value function is approximated through the fuzzy system AnYa. The state–action space is discretized into a static grid with nodes. Each node is treated as one prototype and corresponds to one fuzzy rule, with the value of the node being the consequent. Values of consequents are updated using the Sarsa( λ ) algorithm. Probability distribution of optimal actions regarding different states is estimated through Empirical Data Analytics (EDA), Autonomous Learning Multi-Model Systems (ALMMo), and Empirical Fuzzy Sets ( ε FS). The fuzzy kernel of IFRL avoids the lack of interpretability in other methods based on neural networks. Simulation results with four problems, namely Mountain Car, Continuous Gridworld, Pendulum Position, and Tank Level Control, are presented as a proof of the proposed concept.", "Keywords": "Interpretable fuzzy systems ; Reinforcement learning ; Probability distribution learning ; Autonomous learning systems ; AnYa type fuzzy systems ; Empirical Fuzzy Sets", "DOI": "10.1016/j.engappai.2020.103559", "PubYear": 2020, "Volume": "91", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, School of Mechanical Engineering, 200240 Shanghai, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lancaster University, School of Computing and Communications, LA1 4WA Lancaster, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, School of Mechanical Engineering, 200240 Shanghai, China"}], "References": []}, {"ArticleId": 80253147, "Title": "Development of an autonomous object transfer system by an unmanned aerial vehicle based on binocular vision", "Abstract": "<p>This article describes the development of an unmanned aerial vehicle system that had a remarkable performance in the 6th International Unmanned Aerial Vehicle Innovation Grand Prix, which was held on November 2–4, 2018, in Anji, China. The main mission of the competition was to build a simulated tower using prefabricated components by an unmanned rotorcraft, which could be decomposed into the following four subtasks: (1) navigation and control, (2) recognition and location, (3) grasp and construction, and (4) task planning and scheduling. All the tasks were required to perform autonomously without human intervention. According to the requirement of the mission, the unmanned aerial vehicle system was designed and implemented with high degree of autonomy and reliability, whose hardware was developed on a quadrotor platform by integrating various system components, including sensors, computers, power, and grasp mechanism. Software algorithms were exploited, and executable computer codes were implemented and integrated with the developed unmanned aerial vehicle hardware system. Integration of the two provided onboard intelligence to complete the mission. This article addresses the major components and development process of the unmanned aerial vehicle system and describes its applications to the competition mission.</p>", "Keywords": "Unmanned aircraft system ; vision-based perception ; navigation and control ; autonomous task", "DOI": "10.1177/1729881420907732", "PubYear": 2020, "Volume": "17", "Issue": "1", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China;Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China;Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "Yuqing He", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China;Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China"}, {"AuthorId": 4, "Name": "Decai Li", "Affiliation": "State Key Laboratory of Robotics, Shenyang Institute of Automation, Chinese Academy of Sciences, Shenyang, China;Institutes for Robotics and Intelligent Manufacturing, Chinese Academy of Sciences, Shenyang, China"}], "References": []}, {"ArticleId": 80253222, "Title": "Index Option Greek Analysis with Heikin-Ashi Transformed Data and Its prediction with Artificial Neural Network", "Abstract": "<p>This paper analyses the Index Option Greek with respect to a transformed data set of Index that has been <PERSON>ikin <PERSON> Transformed. It has been noted that Heikin <PERSON> Transformation can provide better prediction than normal data and the noise effect can also be used to filter out if volume weights are also considered. This paper tries to predict option greeks for index option with the help of a Neural Network setup. Since option greeks play a very important role in understanding the correct pricing of index option, the paper provides some useful insights in such models.</p>", "Keywords": "", "DOI": "10.32628/CSEIT206136", "PubYear": 2020, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Assistant Professor Govt College Kullu, Himachal Pradesh, India"}], "References": []}, {"ArticleId": 80253320, "Title": "Asynchronous l1 control for 2D switched positive systems with parametric uncertainties and impulses", "Abstract": "In this paper, the issue of asynchronous l 1 control is discussed for a class of two-dimensional (2D) switched positive systems with polytopic uncertain parameters and impulses. By choosing a suitable co-positive <PERSON><PERSON><PERSON><PERSON> function and utilizing the mode-dependent average dwell time technique, sufficient conditions can be derived under which the closed-loop system induced by a set of asynchronous state feedback controllers is positive, exponentially stable and has l 1 disturbance attenuation performance. In addition, the design schemes for the asynchronously switched controllers are also provided in detail. Finally, two examples are provided to demonstrate the accuracy and tractability of the approach outlined in this paper.", "Keywords": "Asynchronous l 1 control ; <PERSON><PERSON> model ; Impulsive switched systems ; Exponential stability ; Mode-dependent average dwell time", "DOI": "10.1016/j.nahs.2020.100887", "PubYear": 2020, "Volume": "37", "Issue": "", "JournalId": 5866, "JournalTitle": "Nonlinear Analysis: Hybrid Systems", "ISSN": "1751-570X", "EISSN": "1878-7460", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Southeast University, Nanjing 210096, PR China;School of Science, Anhui Agricultural University, Hefei 230036, PR China"}, {"AuthorId": 2, "Name": "<PERSON>ling Liang", "Affiliation": "School of Mathematics, Southeast University, Nanjing 210096, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation and Electrical Engineering, Linyi University, Linyi 276005, PR China"}], "References": []}, {"ArticleId": 80253359, "Title": "Feature selection and classification using support vector machine and decision tree", "Abstract": "<p>Breast cancer is one of the human threats which cause morbidity and mortality worldwide. The death rate can be reduced by advanced diagnosis. The objective of this article is to select the reduced number of features the help in diagnosing breast cancer in Wisconsin Diagnostic Breast Cancer (WDBC). This proposed model depicts women who all have no cancer cells or in benign stage later develop into malignant (metastases). Due to the dynamic nature of the big data framework, the proposed method ensures high confidence and low execution time. Moreover, healthcare information growth chases an exponential pattern, and current database systems cannot adequately manage the massive amount of data. So, it is requisite to adopt the “big data” solution for healthcare information.</p>", "Keywords": "big data;breast cancer;feature selection;WDBC", "DOI": "10.1111/coin.12280", "PubYear": 2020, "Volume": "36", "Issue": "4", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "VIT University, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "VIT University, Chennai, India"}], "References": []}, {"ArticleId": 80253372, "Title": "The views, measurements and challenges of elasticity in the cloud: A review", "Abstract": "Elasticity is one of the most important characteristics of cloud computing paradigm which enables deployed application to dynamically adapt to a changing demand by acquiring and releasing shared computational resources at runtime. Thus, elasticity is a key enabler for economies of scale in the cloud that enhances utility of cloud services. In practice, elasticity requires an autonomous management to reduce the gaps between the demand and supply of the computing resources at runtime. In this article, we Provide a review of the approaches and techniques from different perspectives. We examine elasticity management aspects from macro and micro economics perspectives to support value-driven elasticity decisions. We analyze the different views for measuring the elasticity of cloud-based systems. Furthermore, we discuss some of the open challenges in this domain.", "Keywords": "", "DOI": "10.1016/j.comcom.2020.02.010", "PubYear": 2020, "Volume": "154", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "King <PERSON>aziz University, Jeddah, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of New South Wales, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huazhong University of Science and Technology, Wuhan, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "King <PERSON>aziz University, Jeddah, Saudi Arabia"}], "References": []}, {"ArticleId": 80253733, "Title": "Improving prediction with enhanced Distributed Memory-based Resilient Dataset Filter", "Abstract": "Abstract Launching new products in the consumer electronics market is challenging. Developing and marketing the same in limited time affect the sustainability of such companies. This research work introduces a model that can predict the success of a product. A Feature Information Gain (FIG) measure is used for significant feature identification and Distributed Memory-based Resilient Dataset Filter (DMRDF) is used to eliminate duplicate reviews, which in turn improves the reliability of the product reviews. The pre-processed dataset is used for prediction of product pre-launch in the market using classifiers such as Logistic regression and Support vector machine. DMRDF method is fault-tolerant because of its resilience property and also reduces the dataset redundancy; hence, it increases the prediction accuracy of the model. The proposed model works in a distributed environment to handle a massive volume of the dataset and therefore, it is scalable. The output of this feature modelling and prediction allows the manufacturer to optimize the design of his new product.", "Keywords": "", "DOI": "10.1186/s40537-020-00292-y", "PubYear": 2020, "Volume": "7", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80253803, "Title": "Supporting primary students' learning of fraction conceptual knowledge through digital games", "Abstract": "With the advent of mobile technologies, well-designed fraction apps can be used to help children gain fraction knowledge, a challenging topic for both teachers and students. The present pilot study adopted a quasi-experimental design to investigate whether children can learn fraction concepts equally well if half of the lesson time (20 min) is replaced with game-based learning. Keeping the total lesson time (40 min) identical, the control group (N = 33) received traditional instruction, and the experimental group (N = 32) was presented with a blended learning approach spending half of the class time (20 min) playing tablet-based fraction games, where each of the learners had their own tablet. The results suggested that in the posttest, the experimental group achieved similar learning gains to the control group and appear to have achieved better performance in the transfer test than the control group. This paper also discusses the efficiency of game-based learning, the mechanism of how fraction games might enhance learning, and the potential of integrating game-based learning in educational settings. © 2020 John Wiley & Sons Ltd", "Keywords": "fraction learning;game‐based learning;math learning;serious games", "DOI": "10.1111/jcal.12422", "PubYear": 2020, "Volume": "36", "Issue": "4", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Office of Continuing Education, Peking University, Beijing, China; Lab of Learning Sciences, Graduate School of Education, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lab of Learning Sciences, Graduate School of Education, Peking University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Curriculum and Instruction, Faculty of Education, University of Victoria, Victoria, British Columbia, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Curriculum and Instruction, Faculty of Education, University of Victoria, Victoria, British Columbia, Canada"}], "References": []}, {"ArticleId": 80254246, "Title": "Analysis of the main directions of development of artificial intelligence technologies for automation of control processes in the oil and gas complex and assessment of prospects", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2020-2(559)-26-33", "PubYear": 2020, "Volume": "", "Issue": "2", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": "National University of Oil and Gas “Gubkin University”"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Oil and Gas “Gubkin University”"}], "References": []}, {"ArticleId": 80254248, "Title": "Cybernetics in oil and gas production", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2020-2(559)-44-48", "PubYear": 2020, "Volume": "", "Issue": "2", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tyumen Industrial University"}, {"AuthorId": 2, "Name": "Kh.<PERSON><PERSON>", "Affiliation": "Tyumen Industrial University"}], "References": []}, {"ArticleId": 80254286, "Title": "Information automated system used to monitor and analyze technological data of oil and gas production facilities", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2020-2(559)-11-20", "PubYear": 2020, "Volume": "", "Issue": "2", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "N.A. <PERSON>", "Affiliation": "Oil and Gas Research Institute Russian Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Oil and Gas Research Institute Russian Academy of Sciences"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom dobycha Orenburg"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom dobycha Orenburg"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom dobycha Orenburg"}], "References": []}, {"ArticleId": 80254740, "Title": "Factors affecting risky cybersecurity behaviors by U.S. workers: An exploratory study", "Abstract": "Behavioral determinants of cybersecurity have gained greater attention among information technology experts in recent years. However, drivers of risky cybersecurity behavior have not been widely studied. This exploratory study examines the extent to which risky cybersecurity behavior is predicted by factors of cybersecurity-related avoidance behavior. Self-reported risky cybersecurity behavior was examined in light of technology threat avoidance factors in a sample of 184 working adults in the United States. Risky behaviors were measured using the instrument by <PERSON><PERSON> (2017), previously used by researchers to measure behavioral associations with non-technology threat avoidance-related items. Hierarchical regression noted significant predictive associations between several technology threat avoidance factors and self-reported risky cybersecurity behavior: perceived susceptibility (p = .027), perceived cost (p = .003), and self-efficacy (p = .043). Combined, these variables explained 9.4% of the adjusted variance in levels of risky cybersecurity behavior (p = .001). Effect size calculations revealed predictive impacts in the low-medium range. Age was also confirmed as a confounding covariate (p = .045). The impact findings uniquely distinguish this study from previous works. Findings also infer that training in protective behavior can mitigate a significant portion of risky cybersecurity behavior.", "Keywords": "Technology threat avoidance and risky cybersecurity behavior ; Age and risky cybersecurity behavior ; Risky cybersecurity behavior in the workplace", "DOI": "10.1016/j.chb.2020.106319", "PubYear": 2020, "Volume": "108", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Indiana State University, USA;Corresponding author. 6208 Mountain Villa Dr., Austin, TX 78731, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indiana State University, USA"}], "References": []}, {"ArticleId": 80254811, "Title": "Multipath transmission control protocol–based multi-access traffic steering solution for 5G multimedia-centric network: Design and testbed system implementation", "Abstract": "<p>Various technologies have been developed for the efficient use of the multiple radio access technologies resource at the radio access network level or other network levels to improve user service quality in mobile communication networks. In long-term evolution, mobile carriers are commercializing radio access network-level traffic aggregation technologies such as licensed-assisted access-long-term evolution, long-term evolution-unlicensed, and long-term evolution-wireless local area network aggregation, which use the multi-accesses of the 3rd Generation Partnership Project and WiFi, and the multipath transmission control protocol–based traffic aggregation technologies at the L3 network level. The standardization of 3rd Generation Partnership Project Release 16, which is scheduled to be completed by 2020, is under progress to support the traffic aggregation technology at the L3 network level through a multi-access 5G network. Multipath transmission control protocol is also considered as a traffic aggregation technology. However, it is difficult to apply the multipath transmission control protocol employment model used in long-term evolution to the 5G network structure as it is due to the change to a common core architecture that accommodates multiple radio access technologies through one common interface. Therefore, this article proposes an optimal 5G system architecture and a multipath transmission control protocol adaptation method to support the access traffic steering function based on multipath transmission control protocol in a 3rd Generation Partnership Project 5G mobile communication network. We have verified the development of the multipath transmission control protocol–based multi-access traffic steering technology by implementing the proposed solution in a commercial server on a testbed based on the 5G system standard of 3rd Generation Partnership Project Release 15. Furthermore, this article defines problems that occur when implementing the multipath transmission control protocol–based multi-access traffic steering system and proposes relevant solutions. Based on the implementation results, it is demonstrated that the proposed multipath transmission control protocol–based multi-access traffic steering system can perform traffic steering in the 3rd Generation Partnership Project 5G network.</p>", "Keywords": "", "DOI": "10.1177/1550147720909759", "PubYear": 2020, "Volume": "16", "Issue": "2", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Korea Advanced Institute of Science and Technology, Daejeon, South Korea;Electronics and Telecommunications Research Institute, Daejeon, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Telecommunications Research Institute, Daejeon, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Changwon National University, Changwon, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Korea Advanced Institute of Science and Technology, Daejeon, South Korea"}], "References": []}, {"ArticleId": 80254996, "Title": "Attaining flexibility in seru production system by means of Shojinka: An optimization model and solution approaches", "Abstract": "This study explores the problem of workforce scheduling in the seru production environment where operations are performed within independent serus, so-called assembly cells. Although the seru system attracts more attention in recent years, the addressed problem remains scarcely investigated in the existing literature. To analyze the problem in detail, first, a comprehensive optimization model is proposed by placing an emphasis on achieving Shojinka, which is a Japanese word. Subsequently, the structural properties, the lower and upper bounds are presented to aid the understanding of the problem. The model is solved optimally for small-sized problems; however, several algorithms with two different initial population generation procedures are developed for large-sized problems due to the complexity of the problem. The impact of achieving Shojinka along with the workers’ heterogeneity is investigated in detail through experimental design. To this end, four different scenarios are constructed and a distinct algorithm is devoted to each scenario for the comparison purpose. According to the results, allowing interseru worker transfer leads to a considerable decrease in the makespan. This study contributes to the existing academic literature by presenting several insights regarding the implementation of operational strategies on the seru production system.", "Keywords": "Seru production system ; Shojinka ; Operational strategies ; Workers’ heterogeneity ; Genetic algorithm", "DOI": "10.1016/j.cor.2020.104917", "PubYear": 2020, "Volume": "119", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>mer Faruk Yılmaz", "Affiliation": ""}], "References": []}, {"ArticleId": 80255003, "Title": "Associations between duration and type of electronic screen use and cognition in US children", "Abstract": "This study investigated associations between screen-use (time and type) and cognition in children and tested the hypothesis that across all screen types, time spent on screens is negatively associated with cognition. Methods This study presents cross-sectional data from 11,875 US children aged 9–10 years. Exposures were self-reported daily recreational screen-use. The primary outcome was global cognition measured by the Youth NIH Toolbox®. Covariates included child education, pubertal development, parental education, household income, race/ethnicity, physical activity, and sleep duration. Findings The mean (SD) daily recreational screen time was 3.8 (3.1) hours and 99.7% of children reported using some form of screen daily. More screen time was accumulated on weekends compared to weekdays [4.6 (3.6) vs. 3.5 (3.1) hours, d = 0.34, p < 0.001], and boys reported more screen time than girls [4.1 (3.1) vs. 3.4 (3.0) hours, d = 0.22, p < 0.001]. Children in the high (7.2 h/day; β = −1.76 [-2.12, −1.40]) and middle (2.9 h/day; β = −0.82 [-1.15, −0.48]) daily screen time tertiles had lower measures of cognition compared to children in the low tertile (1.2 h/day). Children in the high tertile for TV watching (2.5 h/day; β = −0.99 [-1.55, −0.64]), video watching (2.3 h/day; β = −1.05 [-1.43, −0.67]), and social media (1.4 h/day; β = −0.79 [-1.14, −0.44]) had lower measures of cognition compared to children in the low tertile for each variable (0.3 h/day, 0.1 h/day, and 0.0 h/day, respectively). Higher video game time was positively associated with cognition compared to the low tertile (β = 0.12 [-0.25, 0.48], p = 0.53). Conclusions Higher daily screen time is associated with lower cognition in children. These findings suggest moderating screen-use for promoting cognitive development in children.", "Keywords": "Screen time ; TV watching ; Social media ; Crystallized intelligence ; Fluid intelligence ; Cognitive development", "DOI": "10.1016/j.chb.2020.106312", "PubYear": 2020, "Volume": "108", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Exercise, Metabolism, and Inflammation Laboratory, University of British Columbia Okanagan, Kelowna, BC, Canada;Healthy Active Living and Obesity Research Group, Children's Hospital of Eastern Ontario Research Institute, Ottawa, ON, Canada;Corresponding author. Exercise, Metabolism, and Inflammation Laboratory University of British Columbia Okanagan Kelowna, BC, V1V 1V7, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Healthy Active Living and Obesity Research Group, Children's Hospital of Eastern Ontario Research Institute, Ottawa, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Healthy Active Living and Obesity Research Group, Children's Hospital of Eastern Ontario Research Institute, Ottawa, ON, Canada;Department of Pediatrics, University of Ottawa, Ottawa, ON, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Healthy Active Living and Obesity Research Group, Children's Hospital of Eastern Ontario Research Institute, Ottawa, ON, Canada;Department of Pediatrics, University of Ottawa, Ottawa, ON, Canada;School of Human Kinetics, University of Ottawa, Ottawa, ON, Canada"}], "References": []}, {"ArticleId": 80255758, "Title": "An Introductory Review of Deep Learning for Prediction Models With Big Data", "Abstract": "Deep learning models stand for a new learning paradigm in artificial intelligence (AI) and machine learning. Recent breakthrough results in image analysis and speech recognition have generated a massive interest in this field because also applications in many other domains providing big data seem possible. On a downside, the mathematical and computational methodology underlying deep learning models is very challenging especially for interdisciplinary scientists. For this reason, we present in this paper an introductory review of deep learning approaches including Deep Feedforward Neural Networks (D-FFNN), Convolutional Neural Networks (CNNs), Deep Belief Networks (DBNs), Autoencoders (AEs) and Long Short-Term Memory (LSTM) networks. These models form the major core architectures of deep learning models currently used and should belong to any data scientist's toolbox. Importantly, those core architectural building blocks can be composed flexibly - in an almost Lego-like manner - to build new application-specific network architectures. Hence, a basic understanding of these network architectures is important to be prepared for future developments in AI.", "Keywords": "deep learning; artificial intelligence; machine learning; neural networks; Prediction models; data science", "DOI": "10.3389/frai.2020.00004", "PubYear": 2020, "Volume": "3", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Predictive Society and Data Analytics Lab, Faculty of Information Technology and Communication Sciences, Tampere University, Finland;Institute of Biosciences and Medical Technology, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Predictive Society and Data Analytics Lab, Faculty of Information Technology and Communication Sciences, Tampere University, Finland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Predictive Society and Data Analytics Lab, Faculty of Information Technology and Communication Sciences, Tampere University, Finland;School of Management, University of Applied Sciences Upper Austria, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Predictive Society and Data Analytics Lab, Faculty of Information Technology and Communication Sciences, Tampere University, Finland;School of Management, University of Applied Sciences Upper Austria, Austria"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Management, University of Applied Sciences Upper Austria, Austria;Department of Biomedical Computer Science and Mechatronics, University for Health Sciences, Medical Informatics and Technology (UMIT), Austria;College of Artificial Intelligence, Nankai University, China"}], "References": []}, {"ArticleId": 80255884, "Title": "A parallel generator of non‐Hermitian matrices computed from given spectra", "Abstract": "<p>Iterative linear algebra methods to solve linear systems and eigenvalue problems with non‐Hermitian matrices are important for both the simulation arising from diverse scientific fields and the applications related to big data, machine learning, and artificial intelligence. The spectral property of these matrices has impacts on the convergence of these solvers. Moreover, with the increase of the size of applications, iterative methods are implemented in parallel on clusters. Analysis of their behaviors with non‐Hermitian matrices on supercomputers is so complex that we need to generate large‐scale matrices with different given spectra for benchmarking. These test matrices should be non‐Hermitian and nontrivial, with high dimension. This paper highlights a scalable matrix generator that constructs large sparse matrices using the user‐defined spectrum, and the eigenvalues of generated matrices are ensured to be the same as the predefined spectrum. This generator is implemented on CPUs and multi‐GPUs platforms, with good strong and weak scaling performance on several supercomputers. We also propose a method to verify its ability to guarantee the given spectra. Finally, we give an example to evaluate the numerical properties and parallel performance of iterative methods using this matrix generator.</p>", "Keywords": "iterative methods;linear system and eigenvalue problem;matrix generation;non‐Hermitian matrix;parallel computing;spectrum", "DOI": "10.1002/cpe.5710", "PubYear": 2020, "Volume": "32", "Issue": "20", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>son de la Simulation, CNRS, Gif‐sur‐Yvette, France; CRIStAL, UMR CNRS 9189, University of Lille, Villeneuve d'Ascq, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON>son de la Simulation, CNRS, Gif‐sur‐Yvette, France; CRIStAL, UMR CNRS 9189, University of Lille, Villeneuve d'Ascq, France"}, {"AuthorId": 3, "Name": "Yutong Lu", "Affiliation": "National Supercomputing Center in Guangzhou, Sun Yat‐sen University, Guangzhou, China"}], "References": []}, {"ArticleId": 80256133, "Title": "State-of-the-art fuzzy active contour models for image segmentation", "Abstract": "<p>Image segmentation is the initial step for every image analysis task. A large variety of segmentation algorithm has been proposed in the literature during several decades with some mixed success. Among them, the fuzzy energy-based active contour models get attention to the researchers during last decade which results in development of various methods. A good segmentation algorithm should perform well in a large number of images containing noise, blur, low contrast, region in-homogeneity, etc. However, the performances of the most of the existing fuzzy energy-based active contour models have been evaluated typically on the limited number of images. In this article, our aim is to review the existing fuzzy active contour models from the theoretical point of view and also evaluate them experimentally on a large set of images under the various conditions. The analysis under a large variety of images provides objective insight into the strengths and weaknesses of various fuzzy active contour models. Finally, we discuss several issues and future research direction on this particular topic.</p>", "Keywords": "Segmentation; Active contour; Fuzzy energy; Blur; Intensity in-homogeneity; Noise and low contrast", "DOI": "10.1007/s00500-020-04794-y", "PubYear": 2020, "Volume": "24", "Issue": "19", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CVIT, International Institute of Information Technology, Hyderabad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "MIU, Indian Statistical Institute, Kolkata, India"}], "References": []}, {"ArticleId": 80256211, "Title": "Document Alignment for Generation of English-Punjabi Comparable Corpora from Wikipedia", "Abstract": "<p>Comparable corpora come as an alternative to parallel corpora for the languages where the parallel corpora is scarce. The efficiency of the models trained on comparable corpora is comparatively less to that of the parallel corpora however it helps to compensate much to the machine translation. In this article, the authors have explored Wikipedia as a potential source and delineated the process of alignment of documents which will be further used for the extraction of parallel data. The parallel data thus extracted will help to enhance the performance of Statistical Machine translation.</p>", "Keywords": "", "DOI": "10.4018/IJEA.2020010104", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 8333, "JournalTitle": "International Journal of E-Adoption", "ISSN": "1937-9633", "EISSN": "1937-9641", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Punjabi University, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mu<PERSON>ni Mal Modi College, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Punjabi University"}], "References": []}, {"ArticleId": 80256409, "Title": "PSORTm: a bacterial and archaeal protein subcellular localization prediction tool for metagenomics data", "Abstract": "Abstract Motivation <p>Many methods for microbial protein subcellular localization (SCL) prediction exist, however none is readily available for analysis of metagenomic sequence data, despite growing interest from researchers studying microbial communities in humans, agri-food relevant organisms, and in other environments (for example, for identification of cell-surface biomarkers for rapid protein-based diagnostic tests). We wished to also identify new markers of water quality from freshwater samples collected from pristine vs pollution-impacted watersheds.</p> Results <p>We report PSORTm, the first bioinformatics tool designed for prediction of diverse bacterial and archaeal protein SCL from metagenomics data. PSORTm incorporates components of PSORTb, one of the most precise and widely used protein SCL predictors, with an automated classification by cell envelope. An evaluation using 5-fold cross validation with in silico fragmented sequences with known localization showed that PSORTm maintains PSORTb’s high precision, while sensitivity increases proportionately with metagenomic sequence fragment length. PSORTm’s read-based analysis was similar to PSORTb-based analysis of metagenome-assembled genomes (MAGs), however the latter requires non-trivial manual classification of each MAG by cell envelope, and cannot make use of unassembled sequences. Analysis of the watershed samples revealed the importance of normalization and identified potential biomarkers of water quality. This method should be useful for examining a wide range of microbial communities, including human microbiomes, and other microbiomes of medical, environmental, or industrial importance.</p> Availability and Implementation <p>Documentation, source code, and docker containers are available for running PSORTm locally at https://www.psort.org/psortm/ (freely available, open source software under GNU General Public License Version 3).</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btaa136", "PubYear": 2020, "Volume": "36", "Issue": "10", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Molecular Biology and Biochemistry, Burnaby, BC V5A 1S6, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Molecular Biology and Biochemistry, Burnaby, BC V5A 1S6, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Molecular Biology and Biochemistry, Burnaby, BC V5A 1S6, Canada;Research Computing Group, Simon Fraser University, Burnaby, BC V5A 1S6, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Molecular Biology and Biochemistry, Burnaby, BC V5A 1S6, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Dalhousie University, Halifax, NS B3H 4R2, Canada"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Molecular Biology and Biochemistry, Burnaby, BC V5A 1S6, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Dalhousie University, Halifax, NS B3H 4R2, Canada"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Molecular Biology and Biochemistry, Burnaby, BC V5A 1S6, Canada"}], "References": []}, {"ArticleId": 80256410, "Title": "Genome Detective Coronavirus Typing Tool for rapid identification and characterization of novel coronavirus genomes", "Abstract": "Abstract \n \n Summary \n Genome detective is a web-based, user-friendly software application to quickly and accurately assemble all known virus genomes from next-generation sequencing datasets. This application allows the identification of phylogenetic clusters and genotypes from assembled genomes in FASTA format. Since its release in 2019, we have produced a number of typing tools for emergent viruses that have caused large outbreaks, such as Zika and Yellow Fever Virus in Brazil. Here, we present the Genome Detective Coronavirus Typing Tool that can accurately identify the novel severe acute respiratory syndrome (SARS)-related coronavirus (SARS-CoV-2) sequences isolated in China and around the world. The tool can accept up to 2000 sequences per submission and the analysis of a new whole-genome sequence will take approximately 1 min. The tool has been tested and validated with hundreds of whole genomes from 10 coronavirus species, and correctly classified all of the SARS-related coronavirus (SARSr-CoV) and all of the available public data for SARS-CoV-2. The tool also allows tracking of new viral mutations as the outbreak expands globally, which may help to accelerate the development of novel diagnostics, drugs and vaccines to stop the COVID-19 disease.\n \n \n Availability and implementation \n https://www.genomedetective.com/app/typingtool/cov\n \n \n Contact \n koen@emweb.<NAME_EMAIL>\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btaa145", "PubYear": 2020, "Volume": "36", "Issue": "11", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>b bv, Herent, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>b bv, Herent, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "KwaZulu-Natal Research Innovation and Sequencing Platform (KRISP), School of Laboratory Medicine and Medical Sciences, College of Health Sciences, University of KwaZulu-Natal, Durban, South Africa;Laboratório de Genética Celular e Molecular, Instituto de Ciências Biológicas, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil;Coordenação Geral dos Laboratórios de Saúde Pública/Secretaria de Vigilância em Saúde, Ministério da Saúde, (CGLAB/SVS-MS), Brasília, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "KwaZulu-Natal Research Innovation and Sequencing Platform (KRISP), School of Laboratory Medicine and Medical Sciences, College of Health Sciences, University of KwaZulu-Natal, Durban, South Africa"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Laboratório de Flavivírus, Instituto Oswaldo Cruz, Fundação Oswaldo Cruz, Rio de Janeiro, RJ, Brazil"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratório de Genética Celular e Molecular, Instituto de Ciências Biológicas, Universidade Federal de Minas Gerais, Belo Horizonte, Brazil;Laboratório de Flavivírus, Instituto Oswaldo Cruz, Fundação Oswaldo Cruz, Rio de Janeiro, RJ, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>b bv, Herent, Belgium"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "KwaZulu-Natal Research Innovation and Sequencing Platform (KRISP), School of Laboratory Medicine and Medical Sciences, College of Health Sciences, University of KwaZulu-Natal, Durban, South Africa;Centre for the AIDS Programme of Research in South Africa (CAPRISA), Durban, South Africa;Department of Global Health, University of Washington, Seattle, WA, USA"}], "References": []}, {"ArticleId": 80256883, "Title": "Wirtschaftsinformatik-Forschung für die Praxis", "Abstract": "Zusammenfassung Ganz im Sinne der Zeitschrift HMD – Praxis der Wirtschaftsinformatik existiert in Deutschland neben anderen Ausrichtungen auch eine explizit auf Praxisorientierung ausgerichtete Wirtschaftsinformatikforschung. Dieser Beitrag behandelt aus verschiedenen Blickwinkeln deren Entwicklungen und Strömungen. Im Mittelpunkt stehen Forschungsmethoden, die Forschung für die Praxis unterstützen. Dies sind insbesondere Methoden der Fallstudienforschung, der Aktionsforschung und der gestaltungsorientierten Forschung in verschiedenen Ausprägungen. Insbesondere diejenigen Methoden, die Forschung gemeinsam mit der Praxis anstreben, benötigen Rahmenbedingungen, die dies ermöglichen. Die gängigsten Kollaborationsformen, ihre Verknüpfung zu Forschungsmethoden, die von den jeweiligen Partnern zu erwartenden Nutzeffekte und Ergebnisse bilden den zweiten Schwerpunkt des Beitrages. Darüber hinaus identifizieren wir weitere Ansatzpunkte, die Forschern Wege aufzeigen, wie die Praxisrelevanz ihrer Forschung gesteigert werden kann. So gibt es methodische Bausteine, die vorhandene Forschungsmethoden um nicht invasive Komponenten ergänzen können, um die Relevanz der Forschungsergebnisse für die Praxis sicherzustellen. Zudem können Forscher auch an der besseren Konsumierbarkeit ihrer Ergebnisse arbeiten und sollten Mut fassen, die üblichen Routinen vergangener Forschung gelegentlich auch zu verlassen.", "Keywords": "Forschungsmethode; Design Science Research; Design Thinking; Aktionsforschung; Action Design Research; Fallstudienforschung; research method; design science research; design thinking; action research; action design research; case study research", "DOI": "10.1365/s40702-020-00603-0", "PubYear": 2020, "Volume": "57", "Issue": "2", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institut für Wirtschaftsinformatik, Informationsmanagement, Technische Universität Braunschweig, Braunschweig, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Professur Wirtschaftsinformatik, insb. Informationssysteme in Industrie und Handel, Technische Universität Dresden, Dresden, Deutschland"}], "References": []}, {"ArticleId": 80256922, "Title": "Optimization of Many Objective Pickup and Delivery Problem with Delay Time of Vehicle Using Memetic Decomposition Based Evolutionary Algorithm", "Abstract": "<p>The pickup and delivery problem (PDP) is a very common and important problem, which has a large number of real-world applications in logistics and transportation. In PDP, customers send transportation requests to pick up an object from one place and deliver it to another place. This problem is under the focus of researchers since the last two decades with multiple variations. In the literature, different variations of PDP with different number of objectives and constraints have been considered. Depending on the number of objectives, multi and many-objective evolutionary algorithms have been applied to solve the problem and to study the conflicts between objectives. In this paper, PDP is formulated as a many-objective pickup and delivery problem (MaOPDP) with delay time of vehicle having six criteria to be optimized. To the best of our knowledge, this variation of PDP has not been considered in the literature. To solve the problem, this paper proposes a memetic I-DBEA (Improved Decomposition Based Evolutionary Algorithm), which is basically the modification of an existing many-objective evolutionary algorithm called I-DBEA. To demonstrate the superiority of our approach, a set of experiments have been conducted on a variety of small, medium and large-scale problems. The quality of the results obtained by the proposed approach is compared with five existing multi and many-objective evolutionary algorithms using three different multi-objective evaluation measures such as hypervolume (HV), inverted generational distance (IGD) and generational distance (GD). The experimental results demonstrate that the proposed algorithm has significant advantages over several state-of-the-art algorithms in terms of the quality of the obtained solutions.</p>", "Keywords": "", "DOI": "10.1142/S0218213020500037", "PubYear": 2020, "Volume": "29", "Issue": "1", "JournalId": 18388, "JournalTitle": "International Journal on Artificial Intelligence Tools", "ISSN": "0218-2130", "EISSN": "1793-6349", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, National University of Computer and Emerging Sciences, Lahore, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, National University of Computer and Emerging Sciences, Lahore, Pakistan"}], "References": []}, {"ArticleId": 80257038, "Title": "Density-based Approach with Dual Optimization for Tracking Community Structure of Increasing Social Networks", "Abstract": "<p>The rapid evolution of social networks in recent years has focused the attention of researchers to find adequate solutions for the management of these networks. For this purpose, several efficient algorithms dedicated to the tracking and the rapid detection of the community structure have been proposed. In this paper, we propose a novel density-based approach with dual optimization for tracking community structure of increasing social networks. These networks are part of dynamic networks evolving by adding nodes with their links. The local optimization of the density makes it possible to reduce the resolution limit problem generated by the optimization of the modularity. The presented algorithm is incremental with a relatively low algorithmic complexity, making it efficient and faster. To demonstrate the effectiveness of our method, we test it on social networks of the real world. The experimental results show the performance and efficiency of our algorithm measured in terms of modularity density, modularity, normalized mutual information, number of communities discovered, running time and stability of communities.</p>", "Keywords": "", "DOI": "10.1142/S0218213020500025", "PubYear": 2020, "Volume": "29", "Issue": "1", "JournalId": 18388, "JournalTitle": "International Journal on Artificial Intelligence Tools", "ISSN": "0218-2130", "EISSN": "1793-6349", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory LARI, Faculty of Electrical Engineering and Computer Science, University Mouloud Mammeri of Tizi-Ouzou, Algeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory LARI, Faculty of Electrical Engineering and Computer Science, University Mouloud Mammeri of Tizi-Ouzou, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory LARI, Faculty of Electrical Engineering and Computer Science, University Mouloud Mammeri of Tizi-Ouzou, Algeria"}], "References": []}, {"ArticleId": 80257124, "Title": "Season wise bike sharing demand analysis using random forest algorithm", "Abstract": "Rental bike sharing is an urban mobility model that is affordable and ecofriendly. The public bike sharing model is widely used in several cities across the world over the past decade. Because bike use is rising constantly, understanding the system demand in prediction is important to boost the operating system readiness. This article presents a prediction model to meet user demands and efficient operations for rental bikes using Random Forest (RF), which is a homogeneous ensemble method. The approach is carried out in Seoul, South Korea to predict the hourly use of rental bikes. RF is compared with Support Vector Machine with Radial Basis Function Kernel, k ‐nearest neighbor and Classification and Regression Trees to verify RF supremacy in rental bike demand prediction. Performance Index measures the efficiency of RF compared to the other predictive models. Also, the variable importance analysis is performed to assess the most important characteristics during different seasons by creating a predictive model using RF for each season. The results show that the influence of variables changes depending on the seasons that suggest different operating conditions. RF models trained with yearly and seasonwise models show that bike sharing demand can be further improved by considering seasonal change.", "Keywords": "", "DOI": "10.1111/coin.12287", "PubYear": 2024, "Volume": "40", "Issue": "1", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> V. E.", "Affiliation": "Department of Information and Communication Engineering Sunchon National University  Suncheon Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering Sunchon National University  Suncheon Republic of Korea"}], "References": []}, {"ArticleId": 80257467, "Title": "Design, Fabrication, and Testing of an IoT Healthcare Cardiac Monitoring Device", "Abstract": "<p>The expansion of the concept of the Internet of Things (IoT), together with wireless sensor networks, has given rise to a wide range of IoT applications. This paper presents and describes the concept, theory of operation, and practical results of a Telecare-ECG (Electrocardiogram) Monitoring device, designed for the remote monitoring of out-of-hospital cardiac patients. ECG monitoring using the Telecare-ECG Monitor system ensures a better quality of life for patients and greater possibilities for the real-time monitoring and signaling of sporadic cardiac events, by recording instantaneous cardiac arrhythmias captured during certain activities or in the daily environment of the patient; furthermore, hospital resources are less impacted by this device than other devices. In accordance with the novelty and contribution of this paper to the field of ECG investigations, the results obtained in the analysis, testing, and validation of the Telecare-ECG Monitor system refer to the optimization of the functionality of the mobile ECG device under conditions that were as similar to reality as possible.</p>", "Keywords": "Internet of Medical Things; microcontroller; medical information systems; telecare Internet of Medical Things ; microcontroller ; medical information systems ; telecare", "DOI": "10.3390/computers9010015", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Stefan cel Mare University, 720229 Suceava, Romania↑Integrated Center for Research, Development and Innovation in Advanced Materials, Nanotechnologies, and Distributed Systems for Fabrication and Control (MANSiD), Stefan cel Mare University, 720229 Suceava, Romania↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Găitan", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Stefan cel Mare University, 720229 Suceava, Romania↑Integrated Center for Research, Development and Innovation in Advanced Materials, Nanotechnologies, and Distributed Systems for Fabrication and Control (MANSiD), Stefan cel Mare University, 720229 Suceava, Romania"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Stefan cel Mare University, 720229 Suceava, Romania↑Integrated Center for Research, Development and Innovation in Advanced Materials, Nanotechnologies, and Distributed Systems for Fabrication and Control (MANSiD), Stefan cel Mare University, 720229 Suceava, Romania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Stefan cel Mare University, 720229 Suceava, Romania"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>” Technical University of Iaşi, 700050 Iaşi, Romania"}], "References": []}, {"ArticleId": 80258098, "Title": "Autonomous aerial robot using dual‐fisheye cameras", "Abstract": "<p>Safety is undoubtedly the most fundamental requirement for any aerial robotic application. It is essential to equip aerial robots with omnidirectional perception coverage to ensure safe navigation in complex environments. In this paper, we present a light‐weight and low‐cost omnidirectional perception system, which consists of two ultrawide field‐of‐view (FOV) fisheye cameras and a low‐cost inertial measurement unit (IMU). The goal of the system is to achieve spherical omnidirectional sensing coverage with the minimum sensor suite. The two fisheye cameras are mounted rigidly facing upward and downward directions and provide omnidirectional perception coverage: 360° FOV horizontally, 50° FOV vertically for stereo, and whole spherical for monocular. We present a novel optimization‐based dual‐fisheye visual‐inertial state estimator to provide highly accurate state‐estimation. Real‐time omnidirectional three‐dimensional (3D) mapping is combined with stereo‐based depth perception for the horizontal direction and monocular depth perception for upward and downward directions. The omnidirectional perception system is integrated with online trajectory planners to achieve closed‐loop, fully autonomous navigation. All computations are done onboard on a heterogeneous computing suite. Extensive experimental results are presented to validate individual modules as well as the overall system in both indoor and outdoor environments.</p>", "Keywords": "aerial robotics;mapping;navigation;perception;SLAM", "DOI": "10.1002/rob.21946", "PubYear": 2020, "Volume": "37", "Issue": "4", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, Hong Kong University of Science and Technology, Hong Kong"}], "References": []}, {"ArticleId": 80258247, "Title": "ConfID : an analytical method for conformational characterization of small molecules using molecular dynamics trajectories", "Abstract": "Abstract \n \n Motivation \n The conformational space of small molecules can be vast and difficult to assess. Molecular dynamics (MD) simulations of free ligands in solution have been applied to predict conformational populations, but their characterization is often based on clustering algorithms or manual efforts.\n \n \n Results \n Here, we introduce ConfID, an analytical tool for conformational characterization of small molecules using MD trajectories. The evolution of conformational sampling and population frequencies throughout trajectories is calculated to check for sampling convergence while allowing to map relevant conformational transitions. The tool is designed to track conformational transition events and calculate time-dependent properties for each conformational population detected.\n \n \n Availability and implementation \n Toolkit and documentation are freely available at http://sbcb.inf.ufrgs.br/confid\n \n \n Contact \n marcelo.poleto@ufv.<NAME_EMAIL>\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btaa130", "PubYear": 2020, "Volume": "36", "Issue": "11", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centro de Biotecnologia, Universidade Federal do Rio Grande do Sul, Porto Alegre 91509-900, Brazil;Departamento de Biologia Geral, Universidade Federal de Viçosa, Viçosa 36570-000, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instituto de Informática, Universidade Federal do Rio Grande do Sul, Porto Alegre 91509-900, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto de Informática, Universidade Federal do Rio Grande do Sul, Porto Alegre 91509-900, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centro de Biotecnologia, Universidade Federal do Rio Grande do Sul, Porto Alegre 91509-900, Brazil"}], "References": []}, {"ArticleId": 80258337, "Title": "Application of Simulated Annealing Algorithm in the Service Scheduling of a Video Conference Network System", "Abstract": "模拟退火算法是一种有效的全局优化算法。本文在叙述模拟退火算法的基本原理及实现方法的基础上，采用该模型对视频会议网络系统业务调度问题给予了解答，并且对结果的可靠性进行了分析验证。结果表明，在视频会议网络实际运行过程中，当平均延迟时间为0.0458秒，平均流通时间为0.0472秒，网络设备利用率为2.94%，网络运行成本为0.0641时，整个网络性能达到最优，即节点处理所有业务的时间为最短值0.0018秒。", "Keywords": "模拟退火算法;业务调度;全局优化", "DOI": "10.12677/SEA.2020.91012", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "海霞 邵", "Affiliation": ""}], "References": []}, {"ArticleId": 80258351, "Title": "Reducing CRR in fast-track projects through BIM", "Abstract": "<p>Rework is one of the most commonly encountered issues that face construction projects, leading to potential loss of money and delays. The aim of this study is to investigate the potential role of Building Information Modelling (BIM) in reducing Client-Related Rework (CRR) when set within the context of fast-track construction projects in Egypt. In order to study this issue survey data was collected from 51 construction professionals within the Egyptian private construction sector with experience in fast-track projects through a self-administered questionnaire composed of fourteen closed and open-ended subgroup of questions, whose aim is to gain an understanding of the main sources of CRR in the industry. The survey results revealed that the majority of the respondents (92.1%) had experienced CRR in construction projects, resulting in an average project cost increase of 22% and an average delay of 23%, as well as formulating the basis for a novel Severity Index (SI), which was devised in order to rank CRR causes, with its finding revealing \"Clients' financial problems\", \"Impediment in prompting the decision making of the client\" and \"Replacement of materials by the client\", as the biggest contributors to CRR. The survey also highlights visualization as a potential solution to CRR, with eight case studies from literature as well as one from the Egyptian market being used to validate the use of BIM in the reduction of CRR. Findings illustrated that the use of BIM in the Egypt is similar to findings in literature, with BIM resulting in rework cost and schedule reduction of 49 and 57 percent respectively.</p>", "Keywords": "Building Information Modelling; Client-Related Rework; Egyptian construction; Fast track projects", "DOI": "10.36680/j.itcon.2020.009", "PubYear": 2020, "Volume": "25", "Issue": "", "JournalId": 70075, "JournalTitle": "Journal of Information Technology in Construction", "ISSN": "", "EISSN": "1874-4753", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Bartlett School of Construction and Project Management, University College London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Bartlett School of Construction and Project Management, University College London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Construction Engineering Department, School of Sciences and Engineering, American University in Cairo, Egypt"}], "References": []}, {"ArticleId": 80258375, "Title": "Design of Step Detection Algorithm Based on Acceleration Threshold Detection", "Abstract": "伴随城市化的进程推进，人们对于室内定位技术的精度要求日益增长，室内定位的研究也受到了社会的广泛关注。本文在传统的波峰波谷检测算法的基础上做出改进，设计了多重门限检测的波谷检测算法。该算法通过波峰值或者波谷值判断行人的运动状态，然后检测相邻波谷之间的时间差和波谷与波峰之间的时间差是否满足阈值条件，消除伪波峰和伪波谷的影响，实现计步。同时，本文也使用了卡尔曼滤波降低原始数据的噪声，平滑曲线。实验表明本文算法提高了计步的准确性，能够应用于计步器、室内定位等领域。", "Keywords": "波峰波谷检测;多重门限检测;卡尔曼滤波;室内定位", "DOI": "10.12677/SEA.2020.91011", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "", "Affiliation": ""}], "References": []}, {"ArticleId": 80258478, "Title": "Stability of RNN Classified Gene Sequence on Variant Maps", "Abstract": "循环神经网络(RNN)在分析整数序列的数据特征中具有优异的作用。利用其特征分类的原理，本文将预分类和分类后的基因序列进行包括移位等的序列操作，按照文中框架下模块操作得到一系列的变值概率统计图示可视化结果，通过变值图示以及其他的图示比较与分析，对RNN分类器的稳定性进行分析。在替换操作中多种类的替换关系和移位操作中长度的变化，提供丰富的可视化结果，综合交叉比较结果，有利于对RNN分类器稳定性问题进行分析和深入探索。", "Keywords": "分类器;稳定性;移位;序列操作;可视化", "DOI": "10.12677/SEA.2020.91010", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "", "Affiliation": ""}], "References": []}, {"ArticleId": 80258878, "Title": "Correction to: Rosette plant segmentation with leaf count using orthogonal transform and deep convolutional neural network", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00138-020-01062-9", "PubYear": 2020, "Volume": "31", "Issue": "1-2", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "VIT-AP University, Amaravati, India"}, {"AuthorId": 2, "Name": "S. <PERSON>", "Affiliation": "National Institute of Technology, Tiruchirappalli, India"}], "References": []}, {"ArticleId": 80259127, "Title": "FPGAaaS: A Survey of Infrastructures and Systems", "Abstract": "The popularity of cloud computing services for delivering and accessing infrastructure on demand has significantly increased over the last few years. Concurrently, the usage of FPGAs to accelerate compute-intensive applications has become more widespread in different computational domains due to their ability to achieve high throughput and predictable latency while providing programmability and improved energy efficiency. Computationally intensive applications such as big data analytics, machine learning, and video processing have been accelerated by FPGAs. With the exponential workload increase in data centers, major cloud service providers have made FPGAs and their capabilities available as cloud services. However, enabling FPGAs in the cloud is not a trivial task due to incompatibilities with existing cloud infrastructure and operational challenges related to abstraction, virtualization, partitioning, and security. In this article, we survey recent frameworks for offering FPGA hardware acceleration as a cloud service, classify them based on their virtualization mode, tenancy model, communication interface, software stack, and hardware infrastructure. We further highlight current FPGAaaS trends and identify FPGA resource sharing, security, and microservicing as important areas for future research.", "Keywords": "Web services;microservices;FPGA;virtualization;orchestration;hardware acceleration", "DOI": "10.1109/TSC.2020.2976012", "PubYear": 2022, "Volume": "15", "Issue": "2", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Center for Cyber Physical Systems, Khalifa University, Abu Dhabi, UAE"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Cyber Physical Systems, Khalifa University, Abu Dhabi, UAE"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Cyber Physical Systems, Khalifa University, Abu Dhabi, UAE"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for Cyber Physical Systems, Khalifa University, Abu Dhabi, UAE\"]$4S). Between November 2012 and October 2015, he was the founding co-director of Mubadala's TwinLab 3DSC, a joint research center on 3D integrated circuits with the Technical University of Dresden, Germany. He also headed the Masdar Institute Center for Microsystems (iMicro) from November 2013 until March 2016. From 1996 to 2010, he was with the corporate CAD organizations at IBM Research and the IBM Systems and Technology Group, Yorktown Heights, NY, where he was involved in the research, development, and deployment of CAD tools and methodologies for IBM's high-end microprocessors. His current research interests include IoT platform prototyping, energy-efficient edge and cloud computing, IoT communications; power and thermal management of multi-core processors, low-power, embedded digital-signal processing, 3D integration, and CAD for VLSI, MEMS, and silicon photonics. He is the recipient of six invention achievement awards, one Outstanding Technical Achievement Award and one Research Division Award, all from IBM, for his contributions in the area of VLSI CAD. He is the inventor or co-inventor of 50 issued US patents with several more pending. In 2014, he was the co-recipient of the D. O<PERSON> Pede<PERSON> Best Paper Award from the IEEE Transactions on Computer-Aided Design for Integrated Circuits and Systems. In 2018, he received (with <PERSON><PERSON> <PERSON>) the SRC Board of Director Special Award for “pioneering semiconductor research in Abu Dhabi.” He is the lead co-editor of three books: “3D Stacked Chips: From Emerging Processes to Heterogeneous Systems,” Springer, 2016, “The IoT Physical Layer: Design and Implementation,” Springer, 2019, and “Machine Learning in VLSI CAS,” Springer, 2019. Between 2009 and 2013, he served as an associate editor of the IEEE Transactions on Computer-Aided Design. He is currently serving as associate editor of the IEEE Transactions on VLSI Systems and on the editorial Board of the Microelectronics Journal (Elsevier). He has also served on the technical program committees of several leading conferences, including DAC, ICCAD, ASPDAC, DATE, ICCD, ICECS, and MWSCAS. He was the general co-chair of the IFIP/IEEE 25th International Conference on Very Large Scale Integration (VLSI-SoC 2017), Abu Dhabi, UAE.\"]},\"firstName\":\"Ibrahim M"}], "References": []}, {"ArticleId": ********, "Title": "Short-Term QoS Forecasting at the Edge for Reliable Service Applications", "Abstract": "Accurate short-term forecasts allow dynamic systems to adapt their behaviour when degradation is forecast e.g., transportation forecasting allows for alternative routing of traffic before gridlock. This rationale can be applied to service-oriented computing when creating and managing service applications. Recent approaches to improve reliability in service applications have focused on reducing the time to recovery of application using collaborative filtering-based approaches to make QoS predictions for similar users. In this article, we focus on reducing the time to detection of a failure by forecasting when a service is about to degrade in quality. Previous approaches that have focused on QoS forecasting have used traditional time-series methods that are not designed for sudden peaks caused by network congestion or battery-powered IoT devices that can reduce processing capabilities to extend battery life. More modern recurrent neural network-based approaches such as GRUs and LSTMs have long training times, which are unsuitable for dynamic environments. We propose a noisy echo state network-based approach that has been designed to reduce training time allowing the model to incorporate recent QoS values on devices at the edge. Our results show increased response time forecasting accuracy compared to state of the art approaches when tested on IoT and web services datasets.", "Keywords": "QoS forecasting;LSTM;echo state network;reliability;edge computing;SOA", "DOI": "10.1109/TSC.2020.2975799", "PubYear": 2022, "Volume": "15", "Issue": "2", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Trinity College Dublin, Dublin, Ireland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Trinity College Dublin, Dublin, Ireland"}], "References": []}, {"ArticleId": 80259142, "Title": "Scheduling Workflows With Composite Tasks: A Nested Particle Swarm Optimization Approach", "Abstract": "Scientific cloud workflows enable the access to distributed computing resources in cloud environments for executing scientific computing applications. In the literature, most workflow scheduling models assume that each workflow task is mapped to only one service instance. But in computation and data-intensive applications, it is common that the computation resources provided by a single service instance are insufficient for some complicated tasks which contain several closely correlated sub-tasks. To manage such complicated workflows, this article devises a novel workflow model with composite tasks (cWFS). The model views a complicated task as a composite task and allows mapping multiple service instances to a composite task. The data transmission among sub-tasks of a composite task can also be addressed by the proposed model. To solve cWFS problem, we devise a nested particle swarm optimization (N-PSO) that utilizes two kinds of populations, i.e., the outer population and inner population. Since N-PSO is a bit time-consuming, we further devise a Fast version of N-PSO (FN-PSO), which can save more than 60 percent of running time compared with N-PSO. The proposed approaches are evaluated on five real-world workflow types. The experimental results verify that the proposed approaches can solve the new workflow model effectively.", "Keywords": "Cloud computing;workflow scheduling;particle swarm optimization", "DOI": "10.1109/TSC.2020.2975774", "PubYear": 2022, "Volume": "15", "Issue": "2", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "An <PERSON>", "Affiliation": "School of Computer Science and Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80259189, "Title": "A dataset for the flood vulnerability assessment of the upper Cross River basin using morphometric analysis", "Abstract": "<p>The on-site collection of data is not only time consuming, but expensive and perhaps near impossible in restive communities within the upper Cross River basin (UCRB). Therefore, the importance of this data cannot be overemphasized. This article presents a Digital Elevation Model (DEM), land use and land cover (LULC) map, soil map, geology map and climatic datasets which enhance the understanding of the physical characteristics of the upper Cross River basin using morphometric analysis. The use of the LULC map, soil map and the DEM in conjunction with the climatic data enhance the creation of the Hydrologic Response Units (HRUs) and the water balance modelling. The simulation of the water balance at the HRU level enables the routing of the runoff to the reaches of the sub-basins and then to the channels. The geology map provides confirmatory information to the morphometric analysis. The compound factor computed from all the derived morphometric parameters enhance the determination of the overall flood potential of the congruent sub-basins.</p><p>© 2020 The Authors.</p>", "Keywords": "Flood ; Vulnerability ; Upper cross river basin ; Morphometric analysis", "DOI": "10.1016/j.dib.2020.105344", "PubYear": 2020, "Volume": "30", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Nkpa Ogarekpe", "Affiliation": "Department of Civil Engineering, Cross River University of Technology, Calabar, Nigeria."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Agronomy, Cross River University of Technology, Calabar, Nigeria."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ingram School of Engineering, Texas State University, San Marcos, TX, USA."}, {"AuthorId": 4, "Name": "Praise<PERSON>od <PERSON>", "Affiliation": "Department of Civil Engineering, Covenant University, Ota, Nigeria."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, University of Nigeria, Nsukka, Nigeria."}], "References": []}, {"ArticleId": 80259335, "Title": "Low-power clustering scheduling algorithm for wireless sensor nodes in the internet of things", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIPT.2020.10027245", "PubYear": 2020, "Volume": "13", "Issue": "2", "JournalId": 16582, "JournalTitle": "International Journal of Internet Protocol Technology", "ISSN": "1743-8209", "EISSN": "1743-8217", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80259403, "Title": "Research on intelligent scheduling strategy of elevator group under the big data platform", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIPT.2020.10027235", "PubYear": 2020, "Volume": "13", "Issue": "2", "JournalId": 16582, "JournalTitle": "International Journal of Internet Protocol Technology", "ISSN": "1743-8209", "EISSN": "1743-8217", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Mingyang Li", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80259427, "Title": "Autonomous car decision making and trajectory tracking based on genetic algorithms and fractional potential fields", "Abstract": "<p>This article deals with the issue of trajectory optimization of autonomous terrestrial vehicles on a specific range handled by the human driver. The main contributions of this paper are a genetic algorithm-potential field combined method for optimized trajectory planning, the definition of the multi-criteria optimization problem by including a time variable, dynamical vehicle constraints, obstacle motion for collision avoidance, and improvements on the attractive and repulsive potential field definitions. The main interests of the proposed method are its efficiency even in only arc-connected spaces with holes, trajectory optimality thanks to the genetic algorithm that minimizes multi-criteria optimization, reactivity thanks to the potential field through the consideration for nature and motion of obstacles, its orientation toward situations a human driver would consider, and finally the inclusion of constraints to avoid danger and to take into account vehicle dynamics. The global trajectory, optimized through genetic algorithm, is used as a reference in a fractional potential field, which is a reactive local path planning method. The repulsive potential field is made safer by adding fractional orders to the obstacles, and the attractive potential field is improved by creating a dynamical optimal target seen from a robust control point of view. This target replaces the unique attractive potential field point and avoids its drawbacks such as local minima. Autonomous car simulation results are given for a crossroad and an overtaking scenarios.</p>", "Keywords": "Autonomous vehicles; Trajectory planning; Potential fields; Optimization; Genetic algorithms; Fractional potentials", "DOI": "10.1007/s11370-020-00314-x", "PubYear": 2020, "Volume": "13", "Issue": "2", "JournalId": 2152, "JournalTitle": "Intelligent Service Robotics", "ISSN": "1861-2776", "EISSN": "1861-2784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ. Bordeaux, CNRS, IMS UMR 5218, Bordeaux INP/ENSEIRB-MATMECA, Talence Cedex, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Univ. Bordeaux, CNRS, IMS UMR 5218, Bordeaux INP/ENSEIRB-MATMECA, Talence Cedex, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Univ. Bordeaux, CNRS, IMS UMR 5218, Bordeaux INP/ENSEIRB-MATMECA, Talence Cedex, France"}], "References": [{"Title": "Control structure for a car-like robot using artificial neural networks and genetic algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "15771", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 80259441, "Title": "Deconstructing the Seven Cs of Social Media: A Summative Perspective", "Abstract": "<p><p>One of the defining technological forces which are reshaping world today is the easy accessibility to the Internet. The Internet has changed the way people communicate with each other. Social media whose development was first marshaled by Web 2.0, has revolutionized the entire world of communication. The most intriguing fact is that the world of social media is constantly changing. The platforms which are topping the charts today may not be tomorrow. Also, it can be observed that the power has shifted from the hands of marketers to the hands of users which in turn have empowered users. The objective of the present study is to explore the different facets of social media in detail. These facets form the base for the world of social media and can be referred to as the 7 Cs of social media. These seven Cs are - content, community, conversation, capital (social), culture, collaboration, and conversion respectively. With an enhanced understanding of all these Cs of social media, the study proposes a conceptual model depicting the relationship between these seven Cs and social media. Companies should analyze each of these Cs in detail and design their social media strategies accordingly. This will not only assure the efficient and effective use of social media but also will help managers to decide where and how to allot firm resources in a better fashion.</p></p>", "Keywords": "Social Media;Conceptual Model;Content;Community;Conversation;Social Capital;Culture;Collaboration;Conversion", "DOI": "10.3991/ijim.v14i03.10490", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SCMS-Pune"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80259444, "Title": "Understanding an Extension Technology Acceptance Model of Google Translation: A Multi-Cultural Study in United Arab Emirates", "Abstract": "<p>The importance of using Google Translate (GT) has become dominantly more effective. Most researchers, professors, and students rely on its translation as an immediate source of getting the information in different countries all over the world. However, the academic literature fails to acknowledge what factors could contribute to the user's intention to use GT, and consequently fail to discover the effects of using GT. The purpose of this study is to explore GT acceptance in UAE. It is assumed that users' attitude towards GT may vary based on the language used. The variations in languages are unidirectional from the source language (SL) to the target language (TL) and vice versa. The suggested analytical framework is based on an extended TAM model that is proposed by [1]. A quantitative methodology approach was adopted in this study. The hypothesized model is validated empirically using the responses received from a survey of 368 respondents were analyzed using structural equation modeling (SEM-PLS). Results indicated that Perceived Ease of Use, Perceived Usefulness, and Motivation have a significant impact on Behavioral Intention to use GT. In addition, Perceived Usefulness and Motivation significantly influenced Perceived Ease of Use. Furthermore, Perceived Usefulness is in turn influenced by Experience. The findings provide significant theoretical and practical implications for translation researchers, teachers, and MT system developers.</p>", "Keywords": "Google Translation;Acceptance Model;Source and Target Languages;Cultural Differences;UAE.", "DOI": "10.3991/ijim.v14i03.11110", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "The British University in Dubai"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80259445, "Title": "Mobile Government Adoption Model Based on Combining GAM and UTAUT to Explain Factors According to Adoption of Mobile Government Services", "Abstract": "<p><p class=\"Default\">This research examines the mobile-government services adoption, by combining both UTAUT and GAM models with adding new constructs for explaining the key factors that affect on adoption of mobile-government services. As a result, the study identified the critical factors that influence users’ to adopt the system, and developed an integrated model as a powerful tool that assists in the adoption process of mobile-government applications. The novelty of this research will be an added value to the body of knowledge and its implications will be vital for researchers and decision/policy makers who are willing to make a change.</p></p>", "Keywords": "Mobile-government services;Adoption;GAM model;UTAUT model.", "DOI": "10.3991/ijim.v14i03.11264", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Assistant Professor, King Faisal University, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Professor of computer information systems, Hashemite University of Jordan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Associate professor at Department of Software Engineering, Faculty of Science and Information Technology, Al-Zaytoonah University of Jordan, Amman, Jordan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Associate professor of Computer Engineering, Al Ahliyya Amman University"}], "References": []}, {"ArticleId": 80259446, "Title": "Mobiles and Cognition: The Associations Between Mobile Technology and Cognitive Flexibility", "Abstract": "<p><p class=\"0keywords\">Mobile phones hold a rather permeating role in our lives. Their portability and ease of use have turned them to indispensable parts of our everyday activities, since they are steadily connected to the internet, providing a wide range of applications at the same time. However, there has been little research so far, on the way mobile technology habits could be related to alterations in our cognitive functioning. The review presented in this paper, introduces findings of the last decade on the field of cognitive flexibility, some of which, tend to be contradictory at times. Cognitive flexibility is presented through the prism of attention-switching, task–switching and in close relation to working memory and inhibitory control, functions which have been proved to be interrelated. Moving a step forward, an attempt was made to present a more contemporary approach towards cognitive flexibility and the ways it is affected by mobile and advanced information and communication technologies.</p></p>", "Keywords": "cognitive flexibility;mobile phones;ICT;task-switching;working memory;inhibition control;attention control;multitasking;social networking sites (SNS);self-training;search engines", "DOI": "10.3991/ijim.v14i03.11233", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Net Media Lab - Mind & Brain R&D, Institute of Informatics and Telecommunications, N.C.S.R. \"Demokritos\""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Net Media Lab - Mind & Brain R&D, Institute of Informatics and Telecommunications, N.C.S.R. \"Demokritos\""}, {"AuthorId": 3, "Name": "Athanasios Drigas", "Affiliation": "Net Media Lab - Mind & Brain R&D, Institute of Informatics and Telecommunications, N.C.S.R. \"Demokritos\""}], "References": []}, {"ArticleId": 80259478, "Title": "Optimisation of classification algorithm of associated data features of large-scale network system", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIPT.2020.10027231", "PubYear": 2020, "Volume": "13", "Issue": "2", "JournalId": 16582, "JournalTitle": "International Journal of Internet Protocol Technology", "ISSN": "1743-8209", "EISSN": "1743-8217", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80259572, "Title": "Application of the technology of Microsoft Power BI analytical reporting formation in the “Infotech” system to analyze gas compressor information", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2020-2(559)-21-25", "PubYear": 2020, "Volume": "", "Issue": "2", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom orgenergogaz"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom orgenergogaz"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom orgenergogaz"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gazprom orgenergogaz"}], "References": []}, {"ArticleId": 80259629, "Title": "Numerical solution of Volterra integro-differential equation with delay", "Abstract": "We consider an initial value problem for a linear first-order Volterra delay integro-differential equation. We develop a novel difference scheme for the approximate solution of this problem via a finite difference method. The method is based on the fitted difference scheme on a uniform mesh which is achieved by using the method of integral identities which includes the exponential basis functions and applying to interpolate quadrature formulas that contain the remainder term in integral form. Also, the method is proved to be first-order convergent in the discrete maximum norm. Furthermore, a numerical experiment is performed to verify the theoretical results. Finally, the proposed scheme is compared with the implicit Euler scheme. © 2020 All rights reserved.", "Keywords": "Error estimate; Finite difference method; Volterra delay integro-differential equation", "DOI": "10.22436/jmcs.020.03.08", "PubYear": 2020, "Volume": "20", "Issue": "3", "JournalId": 34363, "JournalTitle": "Journal of Mathematics and Computer Science", "ISSN": "", "EISSN": "2008-949X", "Authors": [{"AuthorId": 1, "Name": "Erkan Cimen", "Affiliation": "Department of Mathematics, Faculty of Education, Van Yuzuncu Yil University, Van, 65080, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Institute of Pure and Applied Sciences, Van Yuzuncu Yil University, Van, 65080, Turkey"}], "References": []}, {"ArticleId": 80259636, "Title": "Using the H111 mode in microwave densitometers of gas condensate and oil-gas condensate wells production in case of an annular-dispersed mode of gas-liquid flows", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2020-2(559)-5-10", "PubYear": 2020, "Volume": "", "Issue": "2", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Moscow University named after <PERSON><PERSON><PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Moscow University named after <PERSON><PERSON><PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Izhevsk Motor Plant “Aksion-Holding”"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Izhevsk Motor Plant “Aksion-Holding”"}], "References": []}, {"ArticleId": 80259637, "Title": "Neural network simulation of a mixture detonation process by inert fillers", "Abstract": "", "Keywords": "", "DOI": "10.33285/0132-2222-2020-2(559)-34-39", "PubYear": 2020, "Volume": "", "Issue": "2", "JournalId": 47793, "JournalTitle": "Automation, Telemechanization and Communication in Oil Industry", "ISSN": "0132-2222", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kazan National Research Technological University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Russian University of Cooperation"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kazan National Research Technological University"}], "References": []}, {"ArticleId": 80259649, "Title": "Modified online Newton step based on elementwise multiplication", "Abstract": "<p>The second‐order method using a Newton step is a suitable technique in online learning to guarantee a regret bound. The large data are a challenge in the Newton method to store second‐order matrices such as the hessian. In this article, we have proposed a modified online Newton step that stores first‐ and second‐order matrices of dimension m (classes) by d (features). We have used elementwise arithmetic operations to maintain the size of matrices. The modified second‐order matrix size results in faster computations. Also, the mistake rate is on par with respect to popular methods in the literature. The experimental outcome indicates that proposed method could be helpful to handle large multiclass datasets on common desktop machines using second‐order method as the Newton step.</p>", "Keywords": "machine learning;online learning;online Newton step;elementwise multiplication;second‐order online learning", "DOI": "10.1111/coin.12298", "PubYear": 2020, "Volume": "36", "Issue": "3", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, Panjab University, Chandigarh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, Panjab University, Chandigarh, India"}], "References": []}, {"ArticleId": 80259726, "Title": "Guest editorial: Special issue on data intelligence in sustainable computing", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.suscom.2020.100375", "PubYear": 2020, "Volume": "25", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of South China, Hengyang, China|University of Texas at Rio Grande Valley, Brownsville, USA|Harbin Institute of Technology (Shenzhen), Shenzhen, China|Tshinghua University, Beijing, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology (Shenzhen), Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tshinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Texas at Rio Grande Valley, Brownsville, USA"}], "References": []}, {"ArticleId": 80259729, "Title": "Special issue on networking technologies for sustainable computing", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.suscom.2019.100371", "PubYear": 2020, "Volume": "25", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Deptt. of CSE, Jaypee University of IT, Waknaghat, Solan, HP, India|School of Education Intelligent Technology, Jiangsu Normal University, China|Purdue University, USA|National Chung Cheng University, Chiayi, Taiwan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Purdue University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Chung Cheng University, Chiayi, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Education Intelligent Technology, Jiangsu Normal University, China"}], "References": []}, {"ArticleId": 80259749, "Title": "Fusing hotel ratings and reviews with hesitant terms and consensus measures", "Abstract": "<p>People have come to refer to reviews for valuable information on products before making a purchase. Digesting relevant opinions regarding a product by reading all the reviews is challenging. An automated methodology which aggregates opinions across all the reviews for a single product to help differentiate any two products having the same overall rating is defined. In order to facilitate this process, rating values, which capture the overall satisfaction, and written reviews, which contain the sentiment of the experience with a product, are fused together. In this manner, each reviewer’s opinion is expressed as an interval rating by means of hesitant fuzzy linguistic term sets. These new expressions of opinion are then aggregated and expressed in terms of a central opinion and degree of consensus representing the agreement among the sentiment of the reviewers for an individual product. A real case example based on 2506 TripAdvisor reviews of hotels in Rome during 2017 is provided. The efficiency of the proposed methodology when discriminating between two hotels is compared with the TripAdvisor rating and median of reviews. The proposed methodology obtains significant differentiation between product rankings.</p>", "Keywords": "Hesitant fuzzy linguistic term sets; Linguistic decision making; Consensus models; Tourism; Reviews", "DOI": "10.1007/s00521-020-04778-x", "PubYear": 2020, "Volume": "32", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitat Politècnica de Catalunya - BarcelonaTech, Edifici Omega, Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ESADE Business School, Ramon <PERSON> University, Sant Cugat, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ESADE Business School, Ramon <PERSON> University, Sant Cugat, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universitat Politècnica de Catalunya - BarcelonaTech, Edifici Omega, Barcelona, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universitat Politècnica de Catalunya - BarcelonaTech, Edifici Omega, Barcelona, Spain"}], "References": []}, {"ArticleId": 80259899, "Title": "Flexible data anonymization using ARX—Current status and challenges ahead", "Abstract": "<p>The race for innovation has turned into a race for data. Rapid developments of new technologies, especially in the field of artificial intelligence, are accompanied by new ways of accessing, integrating, and analyzing sensitive personal data. Examples include financial transactions, social network activities, location traces, and medical records. As a consequence, adequate and careful privacy management has become a significant challenge. New data protection regulations, for example in the EU and China, are direct responses to these developments. Data anonymization is an important building block of data protection concepts, as it allows to reduce privacy risks by altering data. The development of anonymization tools involves significant challenges, however. For instance, the effectiveness of different anonymization techniques depends on context, and thus tools need to support a large set of methods to ensure that the usefulness of data is not overly affected by risk‐reducing transformations. In spite of these requirements, existing solutions typically only support a small set of methods. In this work, we describe how we have extended an open source data anonymization tool to support almost arbitrary combinations of a wide range of techniques in a scalable manner. We then review the spectrum of methods supported and discuss their compatibility within the novel framework. The results of an extensive experimental comparison show that our approach outperforms related solutions in terms of scalability and output data quality—while supporting a much broader range of techniques. Finally, we discuss practical experiences with ARX and present remaining issues and challenges ahead.</p>", "Keywords": "data anonymization;de‐identification;privacy;security;software tools", "DOI": "10.1002/spe.2812", "PubYear": 2020, "Volume": "50", "Issue": "7", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Medical Informatics Lab, Berlin Institute of Health (BIH) and Charité Universitätsmedizin Berlin, Berlin, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Medicine, Institute of Medical Informatics, Statistics and Epidemiology, Technical University of Munich, Munich, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Medicine, Institute of Medical Informatics, Statistics and Epidemiology, Technical University of Munich, Munich, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Medicine, Institute of Medical Informatics, Statistics and Epidemiology, Technical University of Munich, Munich, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Medicine, Institute of Medical Informatics, Statistics and Epidemiology, Technical University of Munich, Munich, Germany"}], "References": []}, {"ArticleId": 80260104, "Title": "The Potential of the ePortfolio as a Recruitment Tool: From the Perspective of HR Directors", "Abstract": "<p><p class=\"0abstract\">In this paper, the authors explore the potential of the electronic portfolio (ePortfolio) as a recruitment tool, in particular whether Human Resources directors would be willing to use it during the recruitment and selection process. The ePortfolio presents, documents, reflects on, and fosters students’ skills, credentials, certificates and diverse formal and informal experiences in a structured and well-organized manner. Because the ePortfolio is, by nature, flexible and adaptable, an ePortfolio developed with the help of professors over the course of a students' studies can easily be incorporated into professional networking platforms as a job search tool and showcase the skills required for a successful applicant. It is often said that the difference between what is taught in the academic world and the practical skills that are required by the business sector make it difficult for students to access the labor market. The ePortfolio may help to reconcile both worlds. In order to examine whether Human Resources directors are willing to use an ePortfolio as a recruitment tool, a survey was carried out among fifty-two Human Resources directors. The research findings reveal that an educational ePortfolio can count on significant approval in the business sector. This research study is relevant as it provides valuable information on this topic and it is the only one to date, conducted among HR directors in the context of Spanish-speaking countries.</p></p>", "Keywords": "ePortfolio;educational technology;higher education;employability;human resources.", "DOI": "10.3991/ijim.v14i03.11557", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Education, Department of Applied Didactics; Villanueva-Complutense University of Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, UDIMA – Universidad a Distancia de Madrid. Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Global HR LG Electronics Global HR Operation Team, LG Electronics Yeongdeungpo-gu, Seoul, South Korea"}], "References": []}, {"ArticleId": 80260105, "Title": "Exploring the Key Factors in the Use of an E-Learning System Among Students at King Faisal University, Saudi Arabia", "Abstract": "<p><p>The primary purpose of this study is to review and analyse the literature related to e-learning, which is linked to previous studies. Specifically, studies that used the TAM model to examine the effect of eleven external factors, such as website content quality, website design quality, website technical quality, website access speed, usability, e-learning system functionality, e-learning system reliability, facilitating conditions, top management support, student awareness and academic staff awareness on the use of an e-learning system in King Faisal University, Saudi Arabia. The findings of this study offer valuable guidelines for practitioners, researchers and educators about the most important factors that enhance the use of e-learning systems in universities.</p></p>", "Keywords": "E-learning system;Actual use;Saudi universities.", "DOI": "10.3991/ijim.v14i03.11576", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80260106, "Title": "Use of a Mobile Anonymous Question-Raising System to Assist Flipped-Classroom Learning", "Abstract": "<p>Flipped classroom emphasizes the following. First, the instructor uses a digital platform to store course contents, including video lectures. Then, students retrieve these contents from the platform and learn by themselves before class. Finally, the instructor helps the students acquire their knowledge by asking questions in class, thereby helping the students to attain their learning goals. In previous studies, it was found that students might be affected by their peers and their own personalities, causing them not to express their opinions and views; this in turn might cause the students not to learn well; in some extreme cases, some students might even give up entirely. To solve the problem that students may hesitate to ask questions in class, this research developed a mobile anonymous question-raising system (MAQ) and used it to help students raise questions. By conducting an educational experiment, it was found that the use of MAQ in class could indeed help students to learn better in a flipped classroom setting.</p>", "Keywords": "anonymous question-raising;blended learning;flipped classroom;problem-based learning", "DOI": "10.3991/ijim.v14i03.11722", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Chung Yuan Christian University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Chung Yuan Christian University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Chung Yuan Christian University"}, {"AuthorId": 4, "Name": "<PERSON>son<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Soochow University"}], "References": []}, {"ArticleId": 80260107, "Title": "Implementation and Analysis of a Smart Team Management System Using iOS Devices As iBeacon", "Abstract": "<p><p class=\"0abstract\">A cost-effective smart team management system using iOS devices as iBeacon for better task monitoring and presence detection is proposed in this work. It is designed to maximum utilize the proximity information offered by iOS devices through iBeacon integration. In addition to this positioning service, iOS core location framework, geofencing and push notifications features are also included in this work, to create a productive environment in an organization. Along with the employee tracking and task monitoring features, the proposed application provides the typical project management services like task allocation and notification, instant chat, status report, and employee log. On entering the iBeacon range, daily task details and announcements from all associated teams will be available in team member devices. The system is connected to a server for productive team management, and peer to peer communication is utilized for instant exchange of information. An experimental evaluation to study the effect of interference, obstacles, and measured power on received signal strength and accuracy of the proposed iBeacon is also conducted. The observed results positively recommend the proposed app as an efficient iBeacon application for project management.</p></p>", "Keywords": "Team management; iBeacon; Core Location; iOS Application", "DOI": "10.3991/ijim.v14i03.11680", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "Sindhumol S", "Affiliation": "Cochin University of Science and Technology, Kochi, India"}], "References": []}, {"ArticleId": ********, "Title": "Adaptive Model for Credit Card Fraud Detection", "Abstract": "<p><p>While the flow of banking transactions is increasing, the risk of credit card fraud is becoming greater particularly with the technological revolution that we know, fraudulent are improve and always find new methods to deal with the preventive measures that financial systems set up. Several studies have proposed predictive models for credit card fraud detection based on different machine learning techniques. In this paper, we present an adaptive approach to credit card fraud detection that exploits the performance of the techniques that have given high level of accuracy and consider the type of transaction and the client's profile. Our proposition is a multi-level framework, which encompasses the banking security aspect, the customer profile and the profile of the transaction itself.</p></p>", "Keywords": "Fraud Detection; Machine-Learning; Credit Card Fraud; customer profile; transaction profile", "DOI": "10.3991/ijim.v14i03.11763", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Modeling and Information Technology Faculty of sciences Ben M’SIK, University Hassan II Casablanca, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Modeling and Information Technology Faculty of sciences Ben M’SIK, University Hassan II Casablanca, Morocco"}, {"AuthorId": 3, "Name": "Faouzia Benabbou", "Affiliation": "Laboratory of Modeling and Information Technology Faculty of sciences Ben M’SIK, University Hassan II Casablanca, Morocco"}], "References": []}, {"ArticleId": 80260109, "Title": "Smartphone Crowdsourcing and Data Sharing Towards Advancing User Experience and Mobile Services", "Abstract": "<p>Recent advances in IT have offered people the opportunity to have powerful ultramobile devices in their pockets incorporating a plethora of capabilities which, in most cases, require Internet connectivity. This constant need for Internet access though, especially when a wireless hotspot is unavailable requires connection via network data. The fixed cellular data packages offered by carriers usually do not come for free and to a large extend cannot be adapted to the dynamic needs of users who sometimes need more than they own, while in other cases under-use their network data.  This issue, in combination with the fact that signal quality in many areas is not even fair for one or more carriers has led to the development of a novel android application that aims at allowing users in general and tourists to “foresee” bad signal coverage at places of their interest, as well as to share their cellular data via mobile tethering.</p>", "Keywords": "smartphone application;crowdsourcing;mobile signal coverage;sensors;social application;data sharing", "DOI": "10.3991/ijim.v14i03.11815", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Piraeus"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Piraeus"}], "References": []}, {"ArticleId": 80260110, "Title": "Character Education Based On Digital Comic Media", "Abstract": "<p>Reading is one of the cultures in society that tends to be abandoned along with the rapid development of information technology as children nowadays tend to choose something practical as a medium for finding information. Therefore, to improve the latest learning methods through digital media, character-based literacy comics become the main choice in building positive educational values among elementary school students. This study aims to produce character-based comic media, determine the feasibility and effectiveness of character-based comic media on the development of character education for fourth-grade elementary school students. This research used development research consisting of some stages, namely: research and data collection, planning, product draft development, expert validation, expert-based revision, limited trials, improvement of the product of limited trial results, field trials, improvement of the final product, and product dissemination. The subjects of this study were the fourth-grade students of the Koran Elementary School (SDQu) i.e. 26 students consisting of 6 students for limited trials and 20 students for field trials. The results of this study show that: (1) character-based comic media was produced in thematic-integrative learning, (2) the developed comic media were viewed in terms of the quality aspects of media aspects and material aspects from the experts, the teacher, and the results of students’ responses were categorized very well , and (3) comic media developed effectively increased the value of student character in the learning process.<strong></strong></p>", "Keywords": "Digital Media;Character Education;Thematic-Integrative", "DOI": "10.3991/ijim.v14i03.12111", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Padjadjaran, Telkom University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universitas Padjadjaran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Padjadjaran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Padjadjaran"}], "References": []}, {"ArticleId": 80260111, "Title": "Students' Digital Ethics Profile in the Era of Disruption: An Overview from the Internet Use at Risk in Surakarta City, Indonesia", "Abstract": "<p class=\"0abstract\"><strong>Abstract—</strong><em> </em>This study aims to reveal students' profile of digital ethics in Surakarta City, Indonesia. Current technological trends generate significant implications for the technological digitalizations to all parts of human lives. Information and Communication Technology (ICT) is increasingly integrated in society. This condition at least triggers the emergence of an era of disruption due to changes in the order and lifestyle of the younger generation who prefer the use of various digital technologies in their daily life activities. In recent years, a lot of discussion about the risks and opportunities of digital technology on teenagers have been many found. From several studies conclude that the highest students' communication activities have been majorly done through online. Internet at risk use engages students’ live activities, among others, expressing personal data, bullying, hoaxes, and hate speech to accessing the pornographic contents. Digital ethics is one of the components in digital citizenship from which the users must adhere to. Netiquette, a hybrid word combining \"network\" and \"etiquette,\" essentially referring to the social code of the Internet. As netiquette includes how we communicate, treat others, describe and protect ourselves online related to ethical issues. This study was designed with a quantitative-descriptive research model that applies the survey method. The survey was conducted in three schools, each of which was the high school, the state vocational school and private vocational high school in the Surakarta residency area. The study subjects involved as many as 210 students, from ages 15 to 17 years participating in the survey. The questionnaire consisted of 16 statements applying <PERSON><PERSON>'s point of scale one to five. The results showed that students’ digital ethics with having less category had the highest percentage of 35.23%. While those with enough category reached 32.85% and those with good category was 31.90%. One of the effective ways to strengthening students' digital ethics can be realized through humanistic literacy which is integrated into Civics Education subject at schools.</p>", "Keywords": "Abstract— This study aims to reveal students' profile of digital ethics in Su-rakarta City, Indonesia. Current technological trends generate significant implica-tions for the technological digitalizations to all parts of human lives. Information and Commu", "DOI": "10.3991/ijim.v14i03.12207", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Civics Education Department, Faculty of Teacher Training and Education, Sebelas Maret Univer-sity, Surakarta, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Civics Education Department, Sebelas Maret University of Surakarta, Central Java, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Civics Education Department, Sebelas Maret University of Surakarta, Central Java, Indonesia"}], "References": []}, {"ArticleId": 80260112, "Title": "A Voice-Enabled Game Based Learning Application using Amazon's Echo with Alexa Voice Service: A Game Regarding Geographic Facts About Austria and Europe", "Abstract": "<p>An educational, interactive Amazon Alexa Skill called “Österreich und Europa Spiel / Austria and Europe Game” was developed at Graz University of Technology for a German as well as English speaking audience. This Skills intent is to assist learning geographic facts about Austria as well as Europe by interaction via voice controls with the device. The main research question was if an educational, interactive speech assistant application could be made in a way such that both under-age and full age subjects would be able to use it, enjoy the Game Based Learning experience overall and be assisted learning about the Geography of Austria and Europe. The Amazon Alexa Skill was tested for the first time in a class with 16 students at lower secondary school level. Two further tests were done with a total of five adult participants. After the tests the participants opinion was determined via a questionnaire. The evaluation of the tests suggests that the game indeed gives an additional motivational factor in learning Geography.</p>", "Keywords": "game-based learning experience;geography;educational;interactive;voice enabled;speech assistant;Amazon Alexa", "DOI": "10.3991/ijim.v14i03.12311", "PubYear": 2020, "Volume": "14", "Issue": "3", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "Leonardo <PERSON>", "Affiliation": "Educational Technologies Graz University of Technology, Graz, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Educational Technologies Graz University of Technology, Graz, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Educational Technologies Graz University of Technology, Graz, Austria"}], "References": []}, {"ArticleId": 80260148, "Title": "Delay and jitter sensitivity analysis with varying TCP fraction for multiplexed internet communications", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIPT.2020.10027233", "PubYear": 2020, "Volume": "13", "Issue": "2", "JournalId": 16582, "JournalTitle": "International Journal of Internet Protocol Technology", "ISSN": "1743-8209", "EISSN": "1743-8217", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80260269, "Title": "Signal extraction and monitoring of motion loads based on wearable online device", "Abstract": "Monitoring of human exercise load is a hot area of wearable technology. The mainstream human motion calculation method predicts human motion through offline machine learning techniques. Personalized monitoring poses new challenges to the original learning model. Aiming at the detection of R peak value of motion load signal and the location of QRS complex, an improved R-peak detection algorithm based on adaptive threshold is proposed. The feature extraction method of motion load signal is introduced from multiple dimensions. The characteristics of time domain features and frequency domain features are analyzed. The time domain features and the eigenvalues of frequency domain features are defined and extracted. Experiments show that the proposed algorithm has higher detection accuracy. A multi-threshold-peak step algorithm is proposed and a motion state machine model is established. According to the periodic changes of the trunk movement and the arm swing acceleration in motion, the feature values are extracted, the step detection and the gait discrimination are performed, and the influence of external acceleration interference is excluded. The state machine adopts a nested structure, which is divided into two layers, a parent state and a child state, and calibrates the state transition condition. A verification method for the validity of feature parameters is proposed. The selected feature parameters are taken as an example to analyze and simulate the method. Simulation results show that the characteristic parameters in this paper are effective in the monitoring of the reasonableness of exercise load.", "Keywords": "Wearable online device ; Signal extraction ; Motion load monitoring ; Monitoring state machine", "DOI": "10.1016/j.comcom.2020.02.072", "PubYear": 2020, "Volume": "154", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physical Education, Henan Polytechnic University, Jiaozuo 454000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical Education, Henan Polytechnic University, Jiaozuo 454000, China;Corresponding author"}], "References": []}, {"ArticleId": 80260622, "Title": "Paradigm", "Abstract": "", "Keywords": "", "DOI": "10.1111/cgf.13898", "PubYear": 2020, "Volume": "39", "Issue": "1", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 80261090, "Title": "Deep hashing for multi-label image retrieval: a survey", "Abstract": "<p>Content-based image retrieval (CBIR) aims to display, as a result of a search, images with the same visual contents as a query. This problem has attracted increasing attention in the area of computer vision. Learning-based hashing techniques are amongst the most studied search approaches for approximate nearest neighbors in large-scale image retrieval. With the advance of deep neural networks in image representation, hashing methods for CBIR have started using deep learning to build binary codes. Such strategies are generally known as deep hashing techniques. In this paper, we present a comprehensive deep hashing survey for the task of image retrieval with multiple labels, categorizing the methods according to how the input images are treated: pointwise, pairwise, tripletwise and listwise, as well as their relationships. In addition, we present discussions regarding the cost of space, efficiency and search quality of the described models, as well as open issues and future work opportunities.</p>", "Keywords": "Content-based image retrieval; Fast similarity search; Hashing; Multi-label learning; Deep learning; Deep hash", "DOI": "10.1007/s10462-020-09820-x", "PubYear": 2020, "Volume": "53", "Issue": "7", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto Federal de Rondônia, Porto Velho, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidade Federal do Amazonas, Manaus, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidade Federal do Amazonas, Manaus, Brazil"}], "References": []}, {"ArticleId": 80261120, "Title": "Assessment of supervised machine learning algorithms using dynamic API calls for malware detection", "Abstract": "Detection of malware using traditional malware detection techniques is very hard. Machine Learning (ML) algorithms provide a solution to detect the malware which is being developed at a very high pace. ML automatic anti-malware system can be developed which can update the system with incoming malware to keep the system secure. To train the malware classifiers runtime features are captured through Cuckoo Sandbox. During execution, malware can drop other malicious payloads and every payload performs different malicious activities. API calls of every process executed by malware or benign file are extracted. In this paper, parameter tuning of Machine Learning (ML) is done to produce the high accuracy results in a binary classification of binary files into malware or benign. In machine learning algorithms, a few essential parameters like k value, kernel function, depth of the tree, loss function, splitting criteria, learning rate, and n-estimators are evaluated using API calls for achieving the high accurate results of the malware classifiers. At last, supervised machine learning classification algorithms were assessed with 6434 benign and 8634 malware samples. Malware classifier produced 99.1% accuracy using ensemble algorithms. This paper provides insight into the parameter tuning of ML algorithms for detecting the malware using API calls.", "Keywords": "Malware ; dynamic analysis ; API calls ; machine learning algorithms ; classification", "DOI": "10.1080/1206212X.2020.1732641", "PubYear": 2022, "Volume": "44", "Issue": "3", "JournalId": 4962, "JournalTitle": "International Journal of Computers and Applications", "ISSN": "1206-212X", "EISSN": "1925-7074", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Punjabi University Patiala, Patiala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Punjabi University Patiala, Patiala, India"}], "References": []}, {"ArticleId": 80261450, "Title": "<PERSON>–Cotes Discretization for Improved Dead-Reckoning in Bayesian Estimators with Limited Sampling Rate", "Abstract": "", "Keywords": "Dead Reckoning; Unscented Kalman Filter; Numerical Integration; Quaternions; Small Satellites; Reaction Control System; Three Axis Magnetometer; Runge <PERSON>; Star Tracker; Eclipses", "DOI": "10.2514/1.G004930", "PubYear": 2020, "Volume": "43", "Issue": "4", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, Colorado 80309"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, Colorado 80309"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, Colorado 80309"}], "References": []}, {"ArticleId": 80261483, "Title": "Orbital Element Reachable Set After Gravity Assists of Planets in Elliptical Orbits", "Abstract": "", "Keywords": "Earth; Orbital Elements; Elliptical Orbit; Asteroids; Astronomical Unit; Ecliptic Coordinate System; Periapsis; Numerical Integration; Orbital Boundary Value; Interplanetary Mission", "DOI": "10.2514/1.G004611", "PubYear": 2020, "Volume": "43", "Issue": "5", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology, 100081 Beijing, People Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology, 100081 Beijing, People Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, 100081 Beijing, People Republic of China"}], "References": []}, {"ArticleId": 80261518, "Title": "Multibody modeling for concept-level floating offshore wind turbine design", "Abstract": "Abstract Existing Floating Offshore Wind Turbine (FOWT) platforms are usually designed using static or rigid-body models for the concept stage and, subsequently, sophisticated integrated aero-hydro-servo-elastic models, applicable for design certification. For the new technology of FOWTs, a comprehensive understanding of the system dynamics at the concept phase is crucial to save costs in later design phases. This requires low- and medium-fidelity models. The proposed modeling approach aims at representing no more than the relevant physical effects for the system dynamics. It consists, in its core, of a flexible multibody system. The applied Newton–Euler algorithm is independent of the multibody layout and avoids constraint equations. From the nonlinear model a linearized counterpart is derived. First, to be used for controller design and second, for an efficient calculation of the response to stochastic load spectra in the frequency-domain. From these spectra the fatigue damage is calculated with <PERSON><PERSON><PERSON>’s method and short-term extremes by assuming a normal distribution of the response. The set of degrees of freedom is reduced, with a response calculated only in the two-dimensional plane, in which the aligned wind and wave forces act. The aerodynamic model is a quasistatic actuator disk model. The hydrodynamic model includes a simplified radiation model, based on potential flow-derived added mass coefficients and nodal viscous drag coefficients with an approximate representation of the second-order slow-drift forces. The verification through a comparison of the nonlinear and the linearized model against a higher-fidelity model and experiments shows that even with the simplifications, the system response magnitude at the system eigenfrequencies and the forced response magnitude to wind and wave forces can be well predicted. One-hour simulations complete in about 25 seconds and even less in the case of the frequency-domain model. Hence, large sensitivity studies and even multidisciplinary optimizations for systems engineering approaches are possible.", "Keywords": "Floating offshore wind turbine; Reduced-order modeling; Controller design; Optimization", "DOI": "10.1007/s11044-020-09729-x", "PubYear": 2020, "Volume": "49", "Issue": "2", "JournalId": 5013, "JournalTitle": "Multibody System Dynamics", "ISSN": "1384-5640", "EISSN": "1573-272X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Stuttgart Wind Energy (SWE), University of Stuttgart, Stuttgart, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stuttgart Wind Energy (SWE), University of Stuttgart, Stuttgart, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MesH Engineering GmbH, Stuttgart, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Flensburg University of Applied Sciences, Flensburg, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Stuttgart Wind Energy (SWE), University of Stuttgart, Stuttgart, Germany"}], "References": []}, {"ArticleId": 80261583, "Title": "Model-driven process enactment for NFV systems with MAPLE", "Abstract": "<p>The network functions virtualization (NFV) advent is making way for the rapid deployment of network services (NS) for telecoms. Automation of network service management is one of the main challenges currently faced by the NFV community. Explicitly defining a process for the design, deployment, and management of network services and automating it is therefore highly desirable and beneficial for NFV systems. The use of model-driven orchestration means has been advocated in this context. As part of this effort to support automated process execution, we propose a process enactment approach with NFV systems as the target application domain. Our process enactment approach is megamodel-based. An integrated process modelling and enactment environment, MAPLE, has been built into Papyrus for this purpose. Process modelling is carried out with UML activity diagrams. The enactment environment transforms the process model to a model transformation chain, and then orchestrates it with the use of megamodels. In this paper, we present our approach and environment MAPLE, its recent extension with new features as well as application to an enriched case study consisting of NS design and onboarding process.</p>", "Keywords": "Process enactment; Megamodelling; Papyrus; Network functions virtualization (NFV)", "DOI": "10.1007/s10270-020-00783-9", "PubYear": 2020, "Volume": "19", "Issue": "5", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CS, Ryerson University, Toronto, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ECE, Concordia University, Montreal, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ECE, Concordia University, Montreal, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ECE, Concordia University, Montreal, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Ericsson Inc., Montreal, Canada"}], "References": []}, {"ArticleId": 80261589, "Title": "Hyperspectral Inverse Skinning", "Abstract": "<p>In example‐based inverse linear blend skinning (LBS), a collection of poses (e.g. animation frames) are given, and the goal is finding skinning weights and transformation matrices that closely reproduce the input. These poses may come from physical simulation, direct mesh editing, motion capture or another deformation rig. We provide a re‐formulation of inverse skinning as a problem in high‐dimensional Euclidean space. The transformation matrices applied to a vertex across all poses can be thought of as a point in high dimensions. We cast the inverse LBS problem as one of finding a tight‐fitting simplex around these points (a well‐studied problem in hyperspectral imaging). Although we do not observe transformation matrices directly, the 3D position of a vertex across all of its poses defines an affine subspace, or flat. We solve a ‘closest flat’ optimization problem to find points on these flats, and then compute a minimum‐volume enclosing simplex whose vertices are the transformation matrices and whose barycentric coordinates are the skinning weights. We are able to create LBS rigs with state‐of‐the‐art reconstruction error and state‐of‐the‐art compression ratios for mesh animation sequences. Our solution does not consider weight sparsity or the rigidity of recovered transformations. We include observations and insights into the closest flat problem. Its ideal solution and optimal LBS reconstruction error remain an open problem.</p>", "Keywords": "linear blend skinning;deformation;animation;affine geometry;hyperspectral unmixing;• Computing methodologies → Mesh geometry models;Motion processing", "DOI": "10.1111/cgf.13903", "PubYear": 2020, "Volume": "39", "Issue": "6", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, George Mason University, Fairfax, VA USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, George Mason University, Fairfax, VA USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Houston, Houston, TX USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, George Mason University, Fairfax, VA USA"}], "References": []}, {"ArticleId": 80261619, "Title": "Design of smart objects of fear with a taxonomy of factors affecting the user experience of exposure therapy systems for small-animal phobias", "Abstract": "<p>In vivo exposure therapy (IVET) is the treatment of choice for specific phobias, including animal phobia. Recently virtual reality exposure therapy (VRET) and Augmented reality exposure therapy (ARET) have been presented and validated as suitable tools, however, to the best of our knowledge, no other work has identified nor provided an explicit classification or taxonomy to classify, compare and even inform the design of these kind of systems. In this work we propose a taxonomy of feedback factors that affect the experience of use of mixed reality systems for small-animal phobias. The taxonomy comprises feedback factors in three categories, namely realism, interaction, and intensity; while considering: auditory, haptic and visual dimensions for each. To illustrate this taxonomy in use, we conducted a survey to compare mixed reality applications for small-animal phobias, analysing their features according to the factors proposed in the taxonomy. In addition, we developed the concept of smart objects of fear by means of a design fiction world. In this world, in addition to IVET, VRET and ARET, where the objects of fear are either real (in the former) or virtual (in the two later), we envision a new kind of mixed reality therapy, where the objects of fear are represented using context-aware smart objects, capable of inferring the emotional state of the patient, and of adapting their behavior to improve the system’s response during the therapy; which in turn, may enrich the patient’s therapy experience. We validated the perceived usability of this design fiction world with an expert in exposure therapy for small-animal phobias, whom perceived it as useful and correct. Finally, we show how the use of the taxonomy allowed us to discuss the features of the proposed future technologies in terms of how they furnish each of the factors of the taxonomy.</p>", "Keywords": "Mixed reality systems; Taxonomy; User experience; Exposure therapies; Small-animal phobias", "DOI": "10.1007/s12652-020-01802-9", "PubYear": 2022, "Volume": "13", "Issue": "11", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Facultad de Ciencias, Universidad Autónoma de Baja California, Ensenada, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tecnológico Nacional de México/I.T. de Ensenada, Ensenada, Mexico"}, {"AuthorId": 3, "Name": "Victoria Meza-Kubo", "Affiliation": "Facultad de Ciencias, Universidad Autónoma de Baja California, Ensenada, Mexico"}], "References": []}, {"ArticleId": 80261716, "Title": "Topical classification of domain names based on subword embeddings", "Abstract": "A good domain name can help a company rapidly increase their brand awareness, attract more visitors, and therefore obtain more customers. Due to the exponential increase in the number of domain names, registrants are often frustrated because their preferred domain names are already taken. In order to enhance registrants’ satisfaction and efficiency, as well as to increase the revenue of registrars (e.g. GoDaddy, Yahoo, Squarespace), it is important to suggest alternative domain names that are available. The first step is to detect registrants’ needs by classifying the attempted domain name to one of the categories. This study is the first that defines the problem of domain name classification, which classifies a registrant’s preferred domain name into pre-defined categories. The paper proposes deep neural networks with subword embeddings that are built in multiple strategies. We build embeddings for character n-grams of a domain name by learning from training data, learning from external corpus, or learning from external corpus and adjusting based on training data. The experiments show that the proposed methods significantly outperform the baselines.", "Keywords": "Domain names ; Text classification ; WWW ; Internet ; E-Commerce", "DOI": "10.1016/j.elerap.2020.100961", "PubYear": 2020, "Volume": "40", "Issue": "", "JournalId": 7146, "JournalTitle": "Electronic Commerce Research and Applications", "ISSN": "1567-4223", "EISSN": "1873-7846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "S&P Global, 55 Water Street, New York, NY, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "New Jersey Institute of Technology, University Heights, Newark, NJ, USA"}], "References": []}, {"ArticleId": 80262013, "Title": "An online and generalized non-negativity constrained model for large-scale sparse tensor estimation on multi-GPU", "Abstract": "Non-negative Tensor Factorization (NTF) models are effective and efficient in extracting useful knowledge from various types of probabilistic distribution with multi-way information. Current NTF models are mostly designed for problems in computer vision which involve the whole Matricized Tensor Times K h a t r i − R a o Product (MTTKRP). Meanwhile, a Sparse NTF (SNTF) proposed to solve the problem of sparse Tensor Factorization (TF) can result in large-scale intermediate data. A Single-thread-based SNTF (SSNTF) model is proposed to solve the problem of non-linear computing and memory overhead caused by large-scale intermediate data. However, the SSNTF is not a generalized model. Furthermore, the above methods cannot describe the stream-like data from industrial applications in mainstream processors, e.g, Graphics Processing Unit (GPU) and multi-GPU in an online way. To address these two issues, a Generalized SSNTF (GSSNTF) is proposed, which extends the works of SSNTF to the Euclidean distance, KullbackLeibler (KL)-divergence, and ItakuraSaito (IS)-divergence. The GSSNTF only involves the feature elements instead of the entire factor matrices during its update process, which can avoid the formation of large-scale intermediate matrices with convergence and accuracy promises. Furthermore, GSSNTF can merge the new data into the state-of-the-art built tree dataset for sparse tensor, and then online learning has the promise of the correct data format. At last, a model of Compute Unified Device Architecture (CUDA) parallelizing GSSNTF (CUGSSNTF) is proposed on GPU and Multi-GPU (MCUGSSNTF). Thus, CUGSSNTF has linear computing complexity and space requirement, and linear communication overhead on multi-GPU. CUGSSNTF and MCUGSSNTF are implemented on 8 P100 GPUs in this work, and the experimental results from real-world industrial data sets indicate the linear scalability and 40X speedup performances of CUGSSNTF than the state-of-the-art parallelized approachs.", "Keywords": "Canonical polyadic decomposition ; Generalized model ; GPU and multi-GPU ; High-dimension and sparse data ; Single-thread-based model ; Sparse non-negative tensor factorization ; Online learning ; Stream-like data merging", "DOI": "10.1016/j.neucom.2020.02.068", "PubYear": 2020, "Volume": "399", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China;Department of Computer Science, State University of New York, New Paltz, NY 12561, USA;Corresponding author at: College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China"}], "References": []}, {"ArticleId": 80262036, "Title": "Collaborative Trajectory Mining in Smart-Homes to Support Early Diagnosis of Cognitive Decline", "Abstract": "Our ageing world population claims for innovative tools to support healthcare and independent living. In this article, we address this challenge by introducing a novel system to recognize symptoms of cognitive decline by exploiting modern smart-home sensors. Since several studies indicate that cognitive issues are frequently associated to locomotion anomalies, our work relies on clinical models of wandering behavior. Previous works tried to recognize wandering of elderly people in outdoor environments, using GPS data and location trace analysis. However, the recognition of wandering indoors poses additional challenges. On the one hand, when moving in a restricted indoor environment, a system for wandering recognition may produce a large number of false positive, since a person's movements are frequently more intricate indoors than outdoors. On the other hand, several indoor movements resembling wandering may be actually due to the normal execution of daily living activities, or to the particular shape of the home. To address these challenges, we adopt a collaborative learning approach, using a training set of trajectories shared by individuals living in smart-homes. New wandering episodes are classified using a personalized model, built considering the homes’ shape and the individuals’ profiles. We apply a long-term analysis of classified wandering episodes to provide a hypothesis of diagnosis to be communicated to a medical center for further inspection. We implemented our algorithms and evaluated the system with a large dataset of real-world subjects, including people with dementia, MCI persons, and cognitively healthy people. The results indicate the potential utility of this system to support the early diagnosis of cognitive impairment.", "Keywords": "Cognitive decline;abnormal behavior detection;activity recognition;ambient intelligence", "DOI": "10.1109/TETC.2020.2975071", "PubYear": 2021, "Volume": "9", "Issue": "3", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Geo-spatial Information System, K. N. Toosi University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, University of Cagliari, Cagliari, Italy"}], "References": [{"Title": "Time-Bounded Activity Recognition for Ambient Assisted Living", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "471", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}]}, {"ArticleId": 80262089, "Title": "Prediction of subsurface damage depth in rotary ultrasonic machining of glass BK7 with probability statistics", "Abstract": "<p>Subsurface damage (SSD) generated in rotary ultrasonic machining (RUM) process significantly deteriorates the technological and structural performance of the optical components. However, the invisibility of subsurface cracks underneath the machined surface makes it difficult to accurately and online evaluate the SSD depth. In the present research, incorporated with the probability statistics of the abrasive heights and the indentation fracture mechanics of the brittle material, a theoretical prediction model was established by investigating the inherent correlation between the measured cutting force of the diamond tool and the maximum depth of the subsurface cracks. Utilizing this predictive method, the SSD depth could be rapidly and precisely calculated through the mechanical properties of the material, the cutting force of the diamond tool, and the geometrical characteristics of the abrasives. To validate the feasibility of prediction technique, the experimental measurements of the maximum SSD depths were compared with the predicted results, revealing the acceptable consistency in their values.</p>", "Keywords": "Rotary ultrasonic machining; Cutting force; Subsurface damage; Theoretical prediction model; Glass BK7", "DOI": "10.1007/s00170-019-04775-y", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Dongxi Lv", "Affiliation": "Institute of Advanced Manufacturing Technology, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Advanced Manufacturing Technology, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Advanced Manufacturing Technology, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Advanced Manufacturing Technology, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Advanced Manufacturing Technology, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, Xiamen University, Xiamen, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Advanced Manufacturing Technology, Ningbo Institute of Material Technology and Engineering, Chinese Academy of Sciences, Ningbo, People’s Republic of China;Center of Materials Science and Optoelectronics Engineering, University of Chinese Academy of Sciences, Beijing, People’s Republic of China"}], "References": []}, {"ArticleId": 80262170, "Title": "Amperometric H2S sensor based on a Pt-Ni alloy electrode and a proton conducting membrane", "Abstract": "In this work, carbon fiber carrying platinum-nickel alloy is used as a sensing electrode material to develop a H<sub>2</sub>S gas sensor based on Nafion proton membrane. Characterizations of the material by X-ray diffraction (XRD), scanning electron microscope (SEM) and Energy Dispersive Spectrometer (EDS) reveal that the structure and morphology of the particles change with the ratio of the alloy. The electrochemical specific area (ECSA) of the material is calculated by cyclic voltammetry, which further proves the influence of alloy formation on the catalytic performance of the materials. When the ratio of platinum to nickel is 1:1, the material is uniformly loaded and its catalytic performance is optimal. Thus, the device made with it has the highest sensitivity. The sensor's low detection limit is 0.2 ppm. For the incorporation of nickel, the sensitivity is increased to -0.306 μA/ppm, which is 18 % higher than before. Not only does the sensor have good repeatability and short response recovery time, the presence of oxygen and water vapor does not have much effect on the sensor. Therefore, it has a great application potential in the field of gas detection at room temperature.", "Keywords": "Hydrogen sulfide ; Platinum-nickel alloy catalyst ; Room temperature ; Nafion", "DOI": "10.1016/j.snb.2020.127900", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 10, "Name": "Yuan Gao", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China;Corresponding authors"}], "References": []}, {"ArticleId": 80262186, "Title": "Nafion-based methanol gas sensor for fuel cell vehicles", "Abstract": "In this paper, a Nafion-based methanol gas sensor for fuel cell vehicles is produced. The sensing electrode materials are a series of Pt-Cu alloys (the atomic ratios of Pt to Cu are 1:0, 2:1, 1:1, 1:2) supported on carbon fibers. The structure and morphology of the materials are characterized by XRD, SEM and TEM. The electrochemical properties of the materials are tested by cyclic voltammetry (CV), Amperometric i-t Curve and alternating current impedance (A.C. Impedance) measurements. When the Pt-Cu atomic ratio in the alloy is 1:1, the material agglomeration phenomenon of the material is the weakest and the electrochemical specific area (ECSA) is the largest. The sensor made by this material exhibits the best sensing performance. The device has a response of −36 μA to 500 ppm methanol and a sensitivity of 0.07 μA/ppm. This device has good resistance to CO poisoning and has been verified by DFT theoretical calculations. The sensor operates at room temperature with high selectivity and humidity resistance. Therefore, the sensor has a good application prospect for fuel cell vehicle’s methanol detection.", "Keywords": "Methanol gas sensor ; Pt-Cu alloy catalyst ; Room temperature ; Nafion ; CO poisoning", "DOI": "10.1016/j.snb.2020.127905", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory on Integrated Optoelectronics, Key Laboratory of Advanced Gas Sensors, Jilin Province, College of Electronic Science and Engineering, Jilin University, 2699 Qianjin Street, Changchun, 130012, China;Corresponding authors"}], "References": []}, {"ArticleId": 80262257, "Title": "The Fuzzy Control Automation Architecture of Parallel Action for the Intelligent Smart Grid Networks", "Abstract": "<p>Some technology introduction problems of Smart Grid in the electric networks of Ukraine are considered in the article. It is offered to apply the automats of parallel action in control system by a power supply. It is offered to make alteration in the structure of parallel action classical automat for the decision of problem vagueness detains. The general strategy is set for the construction of parallel action automats with fuzzy logic</p>", "Keywords": "", "DOI": "10.37394/23205.2020.19.3", "PubYear": 2020, "Volume": "19", "Issue": "", "JournalId": 72856, "JournalTitle": "WSEAS TRANSACTIONS ON COMPUTERS", "ISSN": "1109-2750", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation and the Computer Integrated Technologies, Kharkiv Petro Vasilenko National Technical University of Agriculture, Kharkiv, UKRAINE"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation and the Computer Integrated Technologies, Kharkiv Petro Vasilenko National Technical University of Agriculture, Kharkiv, UKRAINE"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Sumy State University, Sumy, UKRAINE"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Sumy State University, Sumy, UKRAINE"}], "References": []}, {"ArticleId": 80262372, "Title": "DNA4mC-LIP: a linear integration method to identify N4-methylcytosine site in multiple species", "Abstract": "Abstract \n \n Motivation \n DNA N4-methylcytosine (4mC) is a crucial epigenetic modification. However, the knowledge about its biological functions is limited. Effective and accurate identification of 4mC sites will be helpful to reveal its biological functions and mechanisms. Since experimental methods are cost and ineffective, a number of machine learning-based approaches have been proposed to detect 4mC sites. Although these methods yielded acceptable accuracy, there is still room for the improvement of the prediction performance and the stability of existing methods in practical applications.\n \n \n Results \n In this work, we first systematically assessed the existing methods based on an independent dataset. And then, we proposed DNA4mC-LIP, a linear integration method by combining existing predictors to identify 4mC sites in multiple species. The results obtained from independent dataset demonstrated that DNA4mC-LIP outperformed existing methods for identifying 4mC sites. To facilitate the scientific community, a web server for DNA4mC-LIP was developed. We anticipated that DNA4mC-LIP could serve as a powerful computational technique for identifying 4mC sites and facilitate the interpretation of 4mC mechanism.\n \n \n Availability and implementation \n http://i.uestc.edu.cn/DNA4mC-LIP/.\n \n \n Contact \n <EMAIL>.<NAME_EMAIL> or <EMAIL>\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btaa143", "PubYear": 2020, "Volume": "36", "Issue": "11", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Innovative Institute of Chinese Medicine and Pharmacy, Chengdu University of Traditional Chinese Medicine, Chengdu 611137, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Neuro-Information of Ministry of Education, School of Life Science and Technology, Center for Informational Biology, University of Electronic Science and Technology of China, Chengdu 610054, China"}, {"AuthorId": 3, "Name": "Jiaqing Yuan", "Affiliation": "Innovative Institute of Chinese Medicine and Pharmacy, Chengdu University of Traditional Chinese Medicine, Chengdu 611137, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Innovative Institute of Chinese Medicine and Pharmacy, Chengdu University of Traditional Chinese Medicine, Chengdu 611137, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Innovative Institute of Chinese Medicine and Pharmacy, Chengdu University of Traditional Chinese Medicine, Chengdu 611137, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Neuro-Information of Ministry of Education, School of Life Science and Technology, Center for Informational Biology, University of Electronic Science and Technology of China, Chengdu 610054, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Neuro-Information of Ministry of Education, School of Life Science and Technology, Center for Informational Biology, University of Electronic Science and Technology of China, Chengdu 610054, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Innovative Institute of Chinese Medicine and Pharmacy, Chengdu University of Traditional Chinese Medicine, Chengdu 611137, China;Center for Genomics and Computational Biology, School of Life Sciences, North China University of Science and Technology, Tangshan 063000, China"}], "References": []}, {"ArticleId": 80262719, "Title": "Combining weighted category-aware contextual information in convolutional neural networks for text classification", "Abstract": "<p>Convolutional neural networks (CNNs) are widely used in many natural language processing tasks, which employ some convolutional filters to capture useful semantic features of a text. However, a small window size convolutional filter is short of the ability to capture contextual information, simply increasing the window size may bring the problems of data sparsity and enormous parameters. To capture the contextual information, we propose to use the weighted sum operation to obtain contextual word representation. We present one implicit weighting method and two explicit category-aware weighting methods to assign the weights of the contextual information. Experimental results on five text classification datasets show the effectiveness of our proposed methods.</p>", "Keywords": "Convolutional neural networks; Text classification; Contextual information; Word representation", "DOI": "10.1007/s11280-019-00757-y", "PubYear": 2020, "Volume": "23", "Issue": "5", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>ai", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "Qing Li", "Affiliation": "Department of Computing, The Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Xu", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, The Chinese University of Hong Kong, Hong Kong, China"}], "References": []}, {"ArticleId": 80262834, "Title": "Theoretical analysis of skip connections and batch normalization from generalization and optimization perspectives", "Abstract": "Abstract <p>Deep neural networks (DNNs) have the same structure as the neocognitron proposed in 1979 but have much better performance, which is because DNNs include many heuristic techniques such as pre-training, dropout, skip connections, batch normalization (BN), and stochastic depth. However, the reason why these techniques improve the performance is not fully understood. Recently, two tools for theoretical analyses have been proposed. One is to evaluate the generalization gap, defined as the difference between the expected loss and empirical loss, by calculating the algorithmic stability, and the other is to evaluate the convergence rate by calculating the eigenvalues of the Fisher information matrix of DNNs. This overview paper briefly introduces the tools and shows their usefulness by showing why the skip connections and BN improve the performance.</p>", "Keywords": "Deep neural networks;ResNet;Skip connections;Batch normalization", "DOI": "10.1017/ATSIP.2020.7", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 10204, "JournalTitle": "APSIPA Transactions on Signal and Information Processing", "ISSN": "", "EISSN": "2048-7703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nara Institute of Science and Technology,Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nara Institute of Science and Technology,Japan"}], "References": []}, {"ArticleId": 80262852, "Title": "Recovering facial reflectance and geometry from multi-view images", "Abstract": "While the problem of estimating shapes and diffuse reflectances of human faces from images has been extensively studied, there is relatively less work done on recovering the specular albedo. This paper presents a lightweight solution for inferring photorealistic facial reflectance and geometry. Our system processes video streams from two views of a subject, and outputs two reflectance maps for diffuse and specular albedos, as well as a vector map of surface normals. A model-based optimization approach is used, consisting of the three stages of multi-view face model fitting, facial reflectance inference and facial geometry refinement. Our approach is based on a novel formulation built upon the 3D morphable model (3DMM) for representing 3D textured faces in conjunction with the Blinn-Phong reflection model. It has the advantage of requiring only a simple setup with two video streams, and is able to exploit the interaction between the diffuse and specular reflections across multiple views as well as time frames. As a result, the method is able to reliably recover high-fidelity facial reflectance and geometry, which facilitates various applications such as generating photorealistic facial images under new viewpoints or illumination conditions.", "Keywords": "3D facial reconstruction ; Specular estimation ; Multi-view capture", "DOI": "10.1016/j.imavis.2020.103897", "PubYear": 2020, "Volume": "96", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanyang Technological University;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanyang Technological University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanyang Technological University;Monash University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanyang Technological University"}], "References": []}, {"ArticleId": 80262921, "Title": "Evaluation of Self-Healing Systems: An Analysis of the State-of-the-Art and Required Improvements", "Abstract": "<p>Evaluating the performance of self-adaptive systems is challenging due to their interactions with often highly dynamic environments. In the specific case of self-healing systems, the performance evaluations of self-healing approaches and their parameter tuning rely on the considered characteristics of failure occurrences and the resulting interactions with the self-healing actions. In this paper, we first study the state-of-the-art for evaluating the performances of self-healing systems by means of a systematic literature review. We provide a classification of different input types for such systems and analyse the limitations of each input type. A main finding is that the employed inputs are often not sophisticated regarding the considered characteristics for failure occurrences. To further study the impact of the identified limitations, we present experiments demonstrating that wrong assumptions regarding the characteristics of the failure occurrences can result in large performance prediction errors, disadvantageous design-time decisions concerning the selection of alternative self-healing approaches, and disadvantageous deployment-time decisions concerning parameter tuning. Furthermore, the experiments indicate that employing multiple alternative input characteristics can help with reducing the risk of premature disadvantageous design-time decisions.</p>", "Keywords": "self-healing; failure model; performance; simulation; evaluation self-healing ; failure model ; performance ; simulation ; evaluation", "DOI": "10.3390/computers9010016", "PubYear": 2020, "Volume": "9", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, University of Potsdam, Prof.-Dr.-Helmert-Str. 2-3, D-14482 Potsdam, Germany ↑ Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, University of Potsdam, Prof.-Dr.-Helmert-Str. 2-3, D-14482 Potsdam, Germany"}], "References": []}, {"ArticleId": 80263111, "Title": "RETRACTED: Research on sports planning and stability control of humanoid robot table tennis", "Abstract": "<p>The humanoid robot has the human shape and has great advantages in assisting human life and work. The ability to work, especially in a dynamic, unstructured environment, is an important prerequisite for humanoid robots to assist humans in their mission. Table tennis hitting involves a variety of key technologies such as visual inspection, trajectory planning, and artificial intelligence. It is an important research example that can reflect the ability of humanoid robots. First, according to the requirements of humanoid robots in the human living environment and the requirements of coordinating table tennis batting movements throughout the body, a method of establishing a humanoid robot model was analyzed, and a control system was designed to meet the needs of rapid table tennis batting. Second, a motion model construction and optimization algorithm based on intelligent learning training is proposed. Based on the parameter knowledge base established by the multiple trajectories of table tennis, a kind of electromagnetic mechanism and D-optimality regularized orthogonal minima are introduced. Design a two-pass method (regularized orthogonal least squares method + D-optimality) to learn the two-level learning method, which is used to learn the key parameters of the table tennis model. Third, for human-like robotic table tennis fast-moving, it is necessary to satisfy both the task and the stability requirements and to propose a stability-optimized whole-system coordinated trajectory planning method. The effectiveness of the proposed humanoid robot table tennis hitting motion planning and stability control method is verified by experiments.</p>", "Keywords": "Humanoid robot ; table tennis hitting ; trajectory planning ; stability control", "DOI": "10.1177/1729881420905960", "PubYear": 2020, "Volume": "17", "Issue": "1", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Humanities and Social Sciences, Harbin Institute of Technology, Shenzhen, China"}], "References": []}, {"ArticleId": 80263372, "Title": "StackCPPred: a stacking and pairwise energy content-based prediction of cell-penetrating peptides and their uptake efficiency", "Abstract": "Abstract \n \n Motivation \n Cell-penetrating peptides (CPPs) are a vehicle for transporting into living cells pharmacologically active molecules, such as short interfering RNAs, nanoparticles, plasmid DNAs and small peptides, thus offering great potential as future therapeutics. Existing experimental techniques for identifying CPPs are time-consuming and expensive. Thus, the prediction of CPPs from peptide sequences by using computational methods can be useful to annotate and guide the experimental process quickly. Many machine learning-based methods have recently emerged for identifying CPPs. Although considerable progress has been made, existing methods still have low feature representation capabilities, thereby limiting further performance improvements.\n \n \n Results \n We propose a method called StackCPPred, which proposes three feature methods on the basis of the pairwise energy content of the residue as follows: RECM-composition, PseRECM and RECM–DWT. These features are used to train stacking-based machine learning methods to effectively predict CPPs. On the basis of the CPP924 and CPPsite3 datasets with jackknife validation, StackDPPred achieved 94.5% and 78.3% accuracy, which was 2.9% and 5.8% higher than the state-of-the-art CPP predictors, respectively. StackCPPred can be a powerful tool for predicting CPPs and their uptake efficiency, facilitating hypothesis-driven experimental design and accelerating their applications in clinical therapy.\n \n \n Availability and implementation \n Source code and data can be downloaded from https://github.com/Excelsior511/StackCPPred.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btaa131", "PubYear": 2020, "Volume": "36", "Issue": "10", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "Xiangzheng Fu", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, Hunan 410082, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, Hunan 410082, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, Hunan 410082, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Fundamental and Frontier Sciences, University of Electronic Science and Technology of China, Chengdu 610054, China"}], "References": []}, {"ArticleId": 80263612, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0305-0548(19)30319-3", "PubYear": 2020, "Volume": "115", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [], "References": []}, {"ArticleId": 80263618, "Title": "Smooth topological design of 3D continuum structures using elemental volume fractions", "Abstract": "Topology optimization has emerged as a powerful tool for generating innovative designs. However, several topology optimization algorithms are finite element (FE) based where mesh-dependent zigzag or blurry boundaries are rarely avoidable. This paper presents a continuum topological design algorithm capable of obtaining smooth 3D topologies based on elemental volume fractions. Parametric studies are thoroughly conducted to determine the proper ranges of the parameters in the proposed algorithm. The numerical results confirm the robustness of the proposed algorithm. Furthermore, it is shown that very small penalty coefficients can be used to obtain clear and convergent topologies. The effectiveness of the proposed algorithm is further proven via numerical comparison with a well-established topology optimization framework. Because of the smooth boundary representation, optimized topologies are suitable for additive manufacturing (AM) without redesign or post-processing.", "Keywords": "Topology optimization ; Elemental volume fractions ; Smooth boundaries ; Continuation approach ; Print-ready design", "DOI": "10.1016/j.compstruc.2020.106213", "PubYear": 2020, "Volume": "231", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Deakin University, Waurn Ponds, VIC 3217, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering, Deakin University, Waurn Ponds, VIC 3217, Australia"}, {"AuthorId": 3, "Name": "Louis N.S. Chiu", "Affiliation": "Department of Materials Science and Engineering, Monash University, Clayton, VIC 3800, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Deakin University, Waurn Ponds, VIC 3217, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science, Engineering and Technology, Swinburne University of Technology, Hawthorn, VIC 3122, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Deakin University, Waurn Ponds, VIC 3217, Australia;Corresponding author"}], "References": []}, {"ArticleId": 80264295, "Title": "Hybrid strategy based model parameter estimation of irregular-shaped underwater vehicles for predicting velocity", "Abstract": "The hydrodynamic model can be used to predict velocity of underwater vehicles in still water. However, there are few economical and effective methods for estimating the hydrodynamic parameters of irregular-shaped underwater vehicles. Thus, this paper proposes a hybrid estimation strategy, which contains a rough estimation using a least squares (LS) based algorithm and a precise estimation using an improved particle swarm optimization (IPSO) algorithm. The numerical simulation and field data based tests suggest that the accuracy of the predicted velocity using the hydrodynamic parameters estimated by the IPSO-based hybrid strategy is better than two state-of-the-art algorithms. Finally, a pool experiment is conducted to verify the accuracy of the predicted horizontal velocity of the underwater vehicle.", "Keywords": "Hydrodynamic model ; Parameter estimation ; Underwater vehicles ; Model-based velocity prediction ; Hybrid strategy", "DOI": "10.1016/j.robot.2020.103480", "PubYear": 2020, "Volume": "127", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Fluid Power and Mechatronic Systems, Zhejiang University, Hangzhou, 310027, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Fluid Power and Mechatronic Systems, Zhejiang University, Hangzhou, 310027, China;Pilot National Laboratory for Marine Science and Technology (Qingdao), Qingdao, 266000, China;Ningbo Research Institute, Zhejiang University, Ningbo, 315100, China;Correspondence to: State Key Laboratory of Fluid Power and Mechatronic Systems, Zhejiang University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Fluid Power and Mechatronic Systems, Zhejiang University, Hangzhou, 310027, China"}], "References": []}, {"ArticleId": 80264296, "Title": "Activatable selenium-containing fluorescent apoptotic agent for biosensing and tracing cancer cell apoptosis", "Abstract": "Activation of cancer cell apoptosis and understanding its working mechanism play a critical role in formulating effective cancer treatment strategies. Herein, we report the design, synthesis, and biological evaluations of a selenium-containing fluorescent apoptotic agent MPSE . MPSE enabled the tracking and imaging of cancer cell apoptosis process. Its emission at 550 nm turned off in the presence of thioredoxin reductase (TrxR) in mitochondria, by forming a non-emissive compound MP . This emission “turn-off” phenomenon allows the monitoring of reactive oxygen species (ROS) induced mitochondria damages. As dysfunctional mitochondria were removed by lysosomes, MP entered lysosomes and its protonation led to bright emissions peaked at 475 nm. The new emission enables the tracking of lysosome activation during the apoptosis process. With the help of MPSE , along with other biological techniques, we have revealed three signal transduction pathways that activate cancer cell apoptosis and inhibit tumor cell proliferation. Our results showed that MPSE was highly useful in elucidating the working mechanism of theranostic-based optical apoptotic agents. We hope that understanding this mechanism will greatly facilitate the development of new cancer treatments.", "Keywords": "Selenium-containing ; Fluorescent apoptotic agent ; Cancer cell apoptosis ; Bioimaging", "DOI": "10.1016/j.snb.2020.127915", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Key Laboratory of Processed Food for Special Medical Purpose, National Engineering Laboratory for Deep Process of Rice and Byproducts, Hunan Key Laboratory of Grain-Oil Deep Process and Quality Control, Central South University of Forestry and Technology Changsha, Hunan 410004, China;Corresponding author at: Central South University of Forestry and Technology, Changsha, Hunan, 410004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan Key Laboratory of Processed Food for Special Medical Purpose, National Engineering Laboratory for Deep Process of Rice and Byproducts, Hunan Key Laboratory of Grain-Oil Deep Process and Quality Control, Central South University of Forestry and Technology Changsha, Hunan 410004, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Singapore University of Technology and Design 8 Somapah Road, Singapore, 487372, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Key Laboratory of Processed Food for Special Medical Purpose, National Engineering Laboratory for Deep Process of Rice and Byproducts, Hunan Key Laboratory of Grain-Oil Deep Process and Quality Control, Central South University of Forestry and Technology Changsha, Hunan 410004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Singapore University of Technology and Design 8 Somapah Road, Singapore, 487372, Singapore"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Key Laboratory of Processed Food for Special Medical Purpose, National Engineering Laboratory for Deep Process of Rice and Byproducts, Hunan Key Laboratory of Grain-Oil Deep Process and Quality Control, Central South University of Forestry and Technology Changsha, Hunan 410004, China;Corresponding author at: Central South University of Forestry and Technology, Changsha, Hunan, 410004, China"}], "References": []}, {"ArticleId": 80264299, "Title": "Allosteric DNA molecular beacons: Using a novel mechanism to develop universal biosensor arrays to fully discriminate DNA/RNA analogues", "Abstract": "With a unique stem-loop hairpin structure, DNA molecular beacons have proven effective in the accurate discrimination of nucleic acid sequences with small changes. However, there are still many problems in current molecular beacons such as stringent assay conditions, insufficient specificity, non-universality and difficulty in multi-targets recognition. One universal type of allosteric DNA molecular beacons was developed in this work by separating the recognition element and signal reporter. The response process was demonstrated by real-time fluorescence simulations and native gel electrophoresis. Used as universal elements in biosensor arrays, the allosteric DNA molecular beacons can fully discriminate various muted DNA/RNA targets as separate clusters without any overlap. The new allosteric DNA molecular beacons have many advantages over conventional ones, typically used for biosensor arrays, and expand the application prospect of molecular beacons for complex bioassay tasks.", "Keywords": "DNA molecular beacons ; Allosteric biomolecules ; Biosensor arrays ; lInear discriminant analysis ; DNA/RNA analogues", "DOI": "10.1016/j.snb.2020.127908", "PubYear": 2020, "Volume": "311", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, PR China"}], "References": []}, {"ArticleId": 80264304, "Title": "UAV-enabled intelligent traffic policing and emergency response handling system for the smart city", "Abstract": "<p>As modern cities expand and develop, the resultant increase in population density gives rise to the need for smart solutions to cope with the demands applied to the infrastructure of the city. In this paper, we investigate the shortcomings of traffic policing and emergency response handling systems; propose an intelligent, autonomous UAV-enabled solution; and describe the system in a simulated environment. Several scenarios of traffic monitoring and policing system are considered in the simulation: traffic light violations and accident detection, mobile speeding traps and automated notification, congestion detection and traffic rerouting, flagged stolen vehicles/pending arrest warrants and vehicle tracking using UAVs, and autonomous emergency response handling systems. Furthermore, smart city infrastructure enable intelligent handling of emergencies by providing traffic light prioritization for ground emergency response units to reduce delay for patient care, automated physical bollard on routes with congested points due to accidents or hazards, first responder support UAV units—medical supplies UAV, fire fighting UAV to combat or control small fires, and numerous other benefits. Lastly, we present the results of the simulated system and discuss our findings.</p>", "Keywords": "Smart city; Traffic monitoring; Traffic policing; UAV; Emergency response; Accident detection", "DOI": "10.1007/s00779-019-01297-y", "PubYear": 2021, "Volume": "25", "Issue": "1", "JournalId": 7381, "JournalTitle": "Personal and Ubiquitous Computing", "ISSN": "1617-4909", "EISSN": "1617-4917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, King <PERSON> University of Petroleum and Minerals, Az Z<PERSON>ran, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, King <PERSON> University of Petroleum and Minerals, Az Z<PERSON>ran, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, King <PERSON> University of Petroleum and Minerals, Az Z<PERSON>ran, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Transportation Research Institute (IMOB), Hasselt University, Hasselt, Belgium"}], "References": []}, {"ArticleId": 80264311, "Title": "Constructing interval-valued generalized partitioned Bonferroni mean operator with several extensions for MAGDM", "Abstract": "<p>In group decision making, each expert’s background and the level of knowledge and ability differ, which makes the expert’s information inputs to the decision-making process heterogeneous. Such heterogeneity in the information can affect the outcome of the selection of the decision alternatives. This paper therefore attempts to partition the heterogeneous information into homogeneous groups to elicit similar (related) and dissimilar (unrelated) data using a clustering algorithm. We then develop an aggregation approach to gather the collective opinions from the homogeneous clusters to accurately model the decision problem in a group setting. The proposed aggregation approach, labeled as the generalized partitioned Bonferroni mean (GPBM), is studied to investigate the characteristics of the aggregation operator. Further, we extend the GPBM concept to an interval-valued fuzzy set context using the additive generators of the strict t -conorms and we develop two other new aggregation operators: the interval-valued GPBM (IVGPBM) and the weighted IVGPBM (WIVGPBM). We analyze the aggregation of fuzzy numbers by the IVGPBM operator using interval arithmetic involving ( lpha) -cuts and the ( lpha) -cut-based decomposition principle of fuzzy numbers. Two practical examples are presented to illustrate the applicability of these operators, and a comparison is conducted to highlight the effects of the confidence level and the sensitivity of the parameters chosen, analyzing the results with the parameterized strict t -conorm. Finally, we compare the experimental results of the proposed method with existing methods.</p>", "Keywords": "Group decision making; Partitioned Bonferroni mean; Interval-valued fuzzy sets; Fuzzy numbers; Clustering", "DOI": "10.1007/s00521-020-04765-2", "PubYear": 2020, "Volume": "32", "Issue": "17", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology, Patna, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Logistics Institute—Asia Pacific, National University of Singapore, Singapore, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Medical Science and Technology, Indian Institute of Technology, Kharagpur, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The Logistics Institute—Asia Pacific, National University of Singapore, Singapore, Singapore"}], "References": []}, {"ArticleId": 80264332, "Title": "Quanvolutional neural networks: powering image recognition with quantum circuits", "Abstract": "<p>Convolutional neural networks (CNNs) have rapidly risen in popularity for many machine learning applications, particularly in the field of image recognition. Much of the benefit generated from these networks comes from their ability to extract features from the data in a hierarchical manner. These features are extracted using various transformational layers, notably the convolutional layer which gives the model its name. In this work, we introduce a new type of transformational layer called a quantum convolution, or quanvolutional layer. Quanvolutional layers operate on input data by locally transforming the data using a number of random quantum circuits, in a way that is similar to the transformations performed by random convolutional filter layers. Provided these quantum transformations produce meaningful features for classification purposes, then this algorithm could be of practical use for near-term quantum computers as it requires small quantum circuits with little to no error correction. In this work, we empirically evaluated the potential benefit of these quantum transformations by comparing three types of models built on the MNIST dataset: CNNs, quantum convolutional neural networks (QNNs), and CNNs with additional non-linearities introduced. Our results showed that the QNN models had both higher test set accuracy as well as faster training compared with the purely classical CNNs.</p>", "Keywords": "Quantum computing; Machine learning; Deep neural networks", "DOI": "10.1007/s42484-020-00012-y", "PubYear": 2020, "Volume": "2", "Issue": "1", "JournalId": 64156, "JournalTitle": "Quantum Machine Intelligence", "ISSN": "2524-4906", "EISSN": "2524-4914", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Computing, Berkeley, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Computing, Berkeley, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Computing, Berkeley, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Computing, Berkeley, USA"}], "References": []}, {"ArticleId": 80264340, "Title": "Effects of magnetoelastic loads on free vibration characteristics of the magnetorheological-based sandwich beam", "Abstract": "<p>In this contribution, we have investigated the effects of magnetoelastic loads on free vibration characteristics of the magnetorheological-based sandwich beam. The considered sandwich beam consists of a magnetorheological core with elastic top and base layers. For these means, the structural governing equations are derived using the Hamilton principle and solved by the finite element method. The results are validated in comparison with the existing results in the literature. The effects of variation in the parameters such as magnetic field intensity and the thickness of the core and top layers on the deviation of the first natural frequency and the corresponding loss factor are studied as well. Finally, in order to provide deep insight, the effects of magnetoelastic loads on the dynamic behavior of the three-layered sandwich beam are examined through a comprehensive survey.</p>", "Keywords": "", "DOI": "10.1177/1045389X20905986", "PubYear": 2020, "Volume": "31", "Issue": "7", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Aerospace Engineering, Sharif University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, Sharif University of Technology, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Aerospace Engineering, Sharif University of Technology, Tehran, Iran"}], "References": []}, {"ArticleId": 80264365, "Title": "Planar arrangement of permanent magnets in design of a magneto-solid damper by finite element method", "Abstract": "<p>This article studies the energy dissipation mechanism of a proposed magneto-solid damper using a three-dimensional finite element model developed in COMSOL Multiphysics software. The energy dissipation mechanism of the magneto-solid damper dissipates energy through combined actions of friction and eddy current damping. The key components of the magneto-solid damper are a steel plate, two copper plates placed on two sides of the steel plate in parallel, and two planar arrays of permanent magnets each one placed between the steel plate and one of the copper plates. These arrays are kept away from the steel and copper plates through narrow gaps; the gaps between them and the steel plate are filled with thin friction pads made of non-magnetic materials. The attractive magnetic interaction between the permanent magnet arrays and the steel plate provides the normal force for the friction developed between the friction pads and the steel plate when the permanent magnet arrays move relative to the steel plate. The motion of the permanent magnet arrays relative to the copper plates, on the other hand, provides the eddy current damping. The main contribution of this article is to optimize the pole arrangement of the permanent magnets and demonstrate that how the optimum pole arrangement can affect the energy dissipation capacity of the magneto-solid damper. The analysis results show that, for a given number and size of the permanent magnets, alternate arrangement of the poles of permanent magnets along the direction of their motion is the most optimal case resulting in large and smooth hysteresis force–displacement loops. This pole arrangement has also been used to find the optimum size of the steel and copper plates by addressing edge and skin effects in the design of the damper.</p>", "Keywords": "", "DOI": "10.1177/1045389X20905968", "PubYear": 2020, "Volume": "31", "Issue": "7", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, The City University of New York, The City College of New York, New York, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, The City University of New York, The City College of New York, New York, USA"}], "References": []}, {"ArticleId": 80264402, "Title": "Least squares support vector machines with fast leave-one-out AUC optimization on imbalanced prostate cancer data", "Abstract": "<p>Quite often, the available pre-biopsy data for early prostate cancer detection are imbalanced. When the least squares support vector machines (LS-SVMs) are applied to such scenarios, it becomes naturally desirable for us to introduce the well-known AUC performance index into the LS-SVMs framework to avoid bias towards majority classes. However, this may result in high computational complexity for the minimal leave-one-out error. In this paper, by introducing the parameter \\(\\lambda \\) , a generalized Area under the ROC curve (AUC) performance index \\(R_{AUCLS}\\) is developed to theoretically guarantee that \\(R_{AUCLS}\\) linearly depends on the classical AUC performance index \\(R_{AUC}\\) . Based on both \\(R_{AUCLS}\\) and the classical LS-SVM, a new AUC-based least squares support vector machine called AUC-LS-SVMs is proposed for directly and effectively classifying imbalanced prostate cancer data. The distinctive advantage of the proposed classifier AUC-LS-SVMs exists in that it can achieve the minimal leave-one-out error by quickly optimizing the parameter \\(\\lambda \\) in \\(R_{AUCLS}\\) using the proposed fast leave-one-out cross validation (LOOCV) strategy. The proposed classifier is first evaluated using generic public datasets. Further experiments are then conducted on a real-world prostate cancer dataset to demonstrate the efficacy of our proposed classifier for early prostate cancer detection.</p>", "Keywords": "Prostate cancer detection; Imbalanced data; AUC performance index; Least squares support vector machines; Leave-one-out cross validation", "DOI": "10.1007/s13042-020-01081-y", "PubYear": 2020, "Volume": "11", "Issue": "8", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Discipline of Information Technology, Mathematics and Statistics, Murdoch University, Perth, Australia;Centre for Smart Health, School of Nursing, The Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Division of Urology, Department of Surgery, Prince of Wales Hospital, The Chinese University of Hong Kong, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence, School of Software, Faculty of Engineering and Information Technology, University of Technology Sydney, Broadway, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Smart Health, School of Nursing, The Hong Kong Polytechnic University, Hong Kong, China"}], "References": []}, {"ArticleId": 80264406, "Title": "A self controlled RDP approach for feature extraction in online handwriting recognition using deep learning", "Abstract": "<p>The identification of accurate features is the initial task for benchmarked handwriting recognition. For handwriting recognition, the objective of feature computation is to find those characteristics of a handwritten stroke that depict the class of a stroke and make it separable from the rest of the stroke classes. The present study proposes a feature extraction technique for online handwritten strokes based on a self controlled Ramer-Douglas-Peucker (RDP) algorithm. This novel approach prepares a smaller length feature vector for different shaped online handwritten strokes without preprocessing and without any control parameter to RDP. Thus, it also overcomes the shortcomings of the traditional chain code based feature extraction approach that requires preprocessing of data, and the original RDP algorithm that requires a control parameter as an input to RDP. We further propose a deep learning network of 1-dimensional convolutional neural networks (Conv1Ds) for recognition, which trains in few minutes due to the smaller dimension of the convolution combined with smaller length feature vectors. The proposed approach can be applied to different scripts and different writing styles. The key aim of the present study is to provide a script independent feature extraction technique that is well suited for smaller devices. It improves the recognition over the best reported accuracy in the literature which was achieved using hidden Markov models with directional features, from 87.67% to 95.61% on a Gurmukhi dataset. For Unipen online handwriting datasets the results are at par with the literature.</p>", "Keywords": "Online handwriting recognition; Feature extraction; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Unipen; Deep learning", "DOI": "10.1007/s10489-020-01632-4", "PubYear": 2020, "Volume": "50", "Issue": "7", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Boise State University, Boise, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering, University of Cambridge, Cambridge, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Boise State University, Boise, USA"}], "References": []}]