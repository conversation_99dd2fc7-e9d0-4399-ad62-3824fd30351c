[{"ArticleId": 94080715, "Title": "The effect of augmentation and transfer learning on the modelling of lower-limb sockets using 3D adversarial autoencoders", "Abstract": "Lower limb amputation is a condition affecting millions of people worldwide. Patients are often prescribed with lower limb prostheses to aid their mobility, but these prostheses require frequent adjustments through an iterative and manual process, which heavily depends on patient feedback and on the prosthetist’s experience. New computer-aided design and manufacturing technologies have been emerging as ways to improve the fitting process by creating virtual models of the prosthesis’ interface component with the limb, the socket. Using Adversarial Autoencoders, a generative model describing both transtibial and transfemoral sockets was created. Two strategies were tested to counteract the small size of the dataset: transfer learning using the ModelNet dataset and data augmentation through a previously validated socket statistical shape model. The minimum reconstruction error was 0.00124 mm and was obtained for the model which combined the two approaches. A single-blind assessment conducted with prosthetists showed that, while generated and real shapes are distinguishable, most generated ones assume plausible shapes. Our results show that the use of transfer learning allowed for a correct training and regularization of the latent space, inducing in the model generative abilities with potential clinical applications.", "Keywords": "3D generative modelling ; Adversarial Autoencoder ; Lower limb sockets ; 3D scanning ; Transfer learning ; Data augmentation", "DOI": "10.1016/j.displa.2022.102190", "PubYear": 2022, "Volume": "74", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering of the University of Porto, <PERSON><PERSON>. <PERSON>, 4200-465 Porto, Portugal;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Adapttech, Rua de <PERSON>iro 649, 4050-445 Porto, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Adapttech, Rua de <PERSON>iro 649, 4050-445 Porto, Portugal"}, {"AuthorId": 4, "Name": "Sofia Assis", "Affiliation": "Adapttech, Rua de <PERSON> 649, 4050-445 Porto, Portugal;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INESC-TEC, <PERSON><PERSON><PERSON>, 4200-465 Porto, Portugal;Faculty of Sciences of the University of Porto, Rua do Campo Alegre, 4169-007 Porto, Portugal"}], "References": []}, {"ArticleId": 94080734, "Title": "DP- k -modes: A self-tuning k -modes clustering algorithm", "Abstract": "The k -modes clustering algorithm was proposed by <PERSON> for handling datasets with categorical attributes, however, the dissimilarity measure used limits its applicability. <PERSON> et al. improved on <PERSON>’s k -modes algorithm by proposing a new dissimilarity measure between objects. Moreover, both k -modes algorithms require the initial seeds to be randomly chosen and the number of clusters be specified manually. To overcome the limitations of <PERSON><PERSON><PERSON> and <PERSON>’s k -modes clustering algorithms, we first extend the clustering algorithm published in Science in 2014 (“clustering by fast search and find of density peaks”). The optimal initial seeds and the number of clusters of a dataset are determined simultaneously by taking the standard deviation as the self-tuning cutoff distance and the simple match dissimilarity as the distance measurement in the definition of the density of a point. A new dissimilarity measure is proposed to calculate the dissimilarities between objects to improve on that of <PERSON>’s k -modes algorithm. The performance of our resulting self-tuning k -modes clustering algorithm was tested on nine datasets (three being relatively large) from the UCI (University of California in Irvine) machine learning repository. The clustering results were compared to those produced by <PERSON><PERSON>s and <PERSON>’s algorithms. Statistical tests of three k -modes algorithms were undertaken to determine whether or not there is significant difference between our self-tuning k -modes algorithm and <PERSON>’s and <PERSON>’s k -modes algorithms. All these experimental results demonstrate that our proposed k -modes clustering algorithm is superior to <PERSON><PERSON><PERSON> and <PERSON>’s k -modes algorithms in terms of clustering accuracy ( ACC ) and the well-known Adjusted Rand Index ( ARI ) metric. Our self-tuning k -modes algorithm is significantly different from both <PERSON>’s and <PERSON>’s k -modes algorithms, and there is no statistically significant difference between <PERSON>’s and <PERSON>’s k -modes algorithms.", "Keywords": "k -Modes clustering ; Local density ; Density peaks ; Standard deviation ; Initial seeds", "DOI": "10.1016/j.patrec.2022.04.026", "PubYear": 2022, "Volume": "158", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi’an, Shaanxi province 710119, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi’an, Shaanxi province 710119, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi’an, Shaanxi province 710119, China;Information Network Center, Northwest University of Political Science and Law, Xi’an, Shaanxi province 710119, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, Shaanxi Normal University, Xi’an, Shaanxi province 710119, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Formerly of the Department of Computer Science, Swansea University, Swansea SA2 8PP, UK"}], "References": [{"Title": "Diverse fuzzy c-means for image clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "275", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Adaptive multi-view subspace clustering for high-dimensional data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "299", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Hyperspectral anomaly detection via density peak clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "144", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A rough set based algorithm for updating the modes in categorical clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "2069", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "An automatic clustering algorithm based on the density-peak framework and Chameleon method", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "40", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 94080796, "Title": "MMI: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.18.63", "PubYear": 2021, "Volume": "", "Issue": "18", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " Collectif", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Organiser la « résistance culturelle", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.1814", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "BANK TRANSACTION USING IRIS RECOGNITION SYSTEM", "Abstract": "", "Keywords": "", "DOI": "10.26634/jip.8.3.18124", "PubYear": 2021, "Volume": "8", "Issue": "3", "JournalId": 40088, "JournalTitle": "i-manager’s Journal on Image Processing", "ISSN": "2349-4530", "EISSN": "2349-6827", "Authors": [{"AuthorId": 1, "Name": "KUMAR R. NITHESH", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kanchipuram, Tamil Nadu, India."}, {"AuthorId": 2, "Name": "D. RAHUL", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kanchipuram, Tamil Nadu, India."}, {"AuthorId": 3, "Name": "V. DHANAKOTI", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kanchipuram, Tamil Nadu, India."}, {"AuthorId": 4, "Name": "S. SARAN", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kanchipuram, Tamil Nadu, India."}], "References": []}, {"ArticleId": 94081186, "Title": "Modal decomposition-based hybrid model for stock index prediction", "Abstract": "Stock index prediction is considered one of the most challenging issues in the financial sector owing to its noise, volatility, and instability. Traditional stock index prediction methods, such as statistical and machine learning methods, cannot achieve a high denoising effect, and also cannot mine enough data features from the stock data, resulting in a poor prediction performance. Deep learning has become an effective tool to predict non-stationary and nonlinear stock indices with strong learning ability. However, there is still room for prediction accuracy improvement if a single deep learning prediction model is replaced with a hybrid model. Therefore, this study proposes a novel deep learning hybrid model for stock index prediction named CEEMDAN-DAE-LSTM. In this hybrid model, the stock index is first decomposed using complete ensemble empirical mode decomposition with adaptive noise (CEEMDAN) into a series of intrinsic mode functions (IMFs) arranged from high to low frequency. Next, the deep autoencoder (DAE) is applied to remove redundant data and extract deep-level features. Then, high-level abstract features are separately fed into long short-term memory (LSTM) networks to predict the stock returns of the next trading day. Finally, the final predicted value is obtained by synthesizing the value of each component. Empirical research results on six stock indices representing both developed and emerging markets showed that our model is superior to other reference models in terms of prediction accuracy and stock index trends; furthermore, it has higher prediction performance for stock indices with greater volatility. In general, this model could be applicable to various stock markets with different degrees of development.", "Keywords": "Stock index prediction ; Deep learning hybrid prediction model ; Adaptive noise complete ensemble empirical mode decomposition ; Deep autoencoder ; Long short-term memory", "DOI": "10.1016/j.eswa.2022.117252", "PubYear": 2022, "Volume": "202", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Pin Lv", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, China;@st.gxu.edu.cn"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi University, Nanning, China;@st.gxu.edu.cn"}], "References": [{"Title": "A systematic review of fundamental and technical analysis of stock market predictions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "3007", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Yin-Yang firefly algorithm based on dimensionally Cauchy mutation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Kwok-<PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113216", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Financial time series forecasting with deep learning : A systematic literature review: 2005–2019", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106181", "JournalTitle": "Applied Soft Computing"}, {"Title": "Share price prediction of aerospace relevant companies with recurrent neural networks based on PCA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115384", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94081227, "Title": "A multi-agent based enhancement for multimodal biometric system at border control", "Abstract": "Border criminals and unauthorized immigrants are dramatically increasing within the last few years in globally due to the absence of proper authorization methods at the border locations. Usually, passenger controls done by trained immigration officers who compare the passport and the physical appearance at the border while some of the countries done by automated border control (ABC) systems. ABC is one of the applicable real-world applications of the biometric domain which commonly implements with fingerprint and face (multimodal) biometric authorization. However, selecting an appropriate classification method is a challenging task at the decision-making stage. This paper proposes a novel architecture for multimodal biometric authorization engaged with the multi-agent system (MAS) to come up with the optimal solution by using the co-feature of MAS, such as coordination, corporation, and negotiation features. The experiment was done with four available multimodal datasets, namely, the National Institute of Standard Technology (NIST) multimodal, SDUMLA-HMT multimodal, BANCA and PRIVATE databases have been reported to demonstrate the efficiency of the proposed method. The experimental result delivers an excellent performance comparing with previous ABC systems at the authentication phase and computationally fast.", "Keywords": "Artificial intelligence ; Biometrics ; Multi-agent system ; Border control system", "DOI": "10.1016/j.array.2022.100171", "PubYear": 2022, "Volume": "14", "Issue": "", "JournalId": 66362, "JournalTitle": "Array", "ISSN": "2590-0056", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Shibaura Institute of Technology, Tokyo, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, Shibaura Institute of Technology, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, Mie University, Kurimamachiya, Tsu, Mie, Japan"}], "References": [{"Title": "Candidate selections with proportional fairness constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}]}, {"ArticleId": 94081316, "Title": "Utility indicator for emotion detection in a speaker authentication system", "Abstract": "<h3>Purpose</h3> <p>Authenticating an individual through voice can prove convenient as nothing needs to be stored and cannot easily be stolen. However, if an individual is authenticating under duress, the coerced attempt must be acknowledged and appropriate warnings issued. Furthermore, as duress may entail multiple combinations of emotions, the current f-score evaluation does not accommodate that multiple selected samples possess similar levels of importance. Thus, this study aims to demonstrate an approach to identifying duress within a voice-based authentication system.</p> <h3>Design/methodology/approach</h3> <p>Measuring the value that a classifier presents is often done using an f-score. However, the f-score does not effectively portray the proposed value when multiple classes could be grouped as one. The f-score also does not provide any information when numerous classes are often incorrectly identified as the other. Therefore, the proposed approach uses the confusion matrix, aggregates the select classes into another matrix and calculates a more precise representation of the selected classifier’s value. The utility of the proposed approach is demonstrated through multiple tests and is conducted as follows. The initial tests’ value is presented by an f-score, which does not value the individual emotions. The lack of value is then remedied with further tests, which include a confusion matrix. Final tests are then conducted that aggregate selected emotions within the confusion matrix to present a more precise utility value.</p> <h3>Findings</h3> <p>Two tests within the set of experiments achieved an f-score difference of 1%, indicating, Mel frequency cepstral coefficient, emotion detection, confusion matrix, multi-layer perceptron, Ryerson audio-visual database of emotional speech and song (RAVDESS), voice authentication that the two tests provided similar value. The confusion matrix used to calculate the f-score indicated that some emotions are often confused, which could all be considered closely related. Although the f-score can represent an accuracy value, these tests’ value is not accurately portrayed when not considering often confused emotions. Deciding which approach to take based on the f-score did not prove beneficial as it did not address the confused emotions. When aggregating the confusion matrix of these two tests based on selected emotions, the newly calculated utility value demonstrated a difference of 4%, indicating that the two tests may not provide a similar value as previously indicated.</p> <h3>Research limitations/implications</h3> <p>This approach’s performance is dependent on the data presented to it. If the classifier is presented with incomplete or degraded data, the results obtained from the classifier will reflect that. Additionally, the grouping of emotions is not based on psychological evidence, and this was purely done to demonstrate the implementation of an aggregated confusion matrix.</p> <h3>Originality/value</h3> <p>The f-score offers a value that represents the classifiers’ ability to classify a class correctly. This paper demonstrates that aggregating a confusion matrix could provide more value than a single f-score in the context of classifying an emotion that could consist of a combination of emotions. This approach can similarly be applied to different combinations of classifiers for the desired effect of extracting a more accurate performance value that a selected classifier presents.</p>", "Keywords": "Voice authentication;Emotion detection;Confusion matrix;RAVDESS;MFCC;MLP", "DOI": "10.1108/ICS-07-2021-0097", "PubYear": 2022, "Volume": "30", "Issue": "5", "JournalId": 31004, "JournalTitle": "Information and Computer Security", "ISSN": "2056-4961", "EISSN": "2056-497X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Security, Nelson Mandela University , Port Elizabeth, South Africa"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Nelson Mandela University , Port Elizabeth, South Africa"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Technology, Nelson Mandela Metropolitan University , Port Elizabeth, South Africa"}], "References": []}, {"ArticleId": 94081434, "Title": "Staged cascaded network for monocular 3D human pose estimation", "Abstract": "<p>The study of deep end-to-end representation learning for 2D to 3D monocular human pose estimation is a common yet challenging task in computer vision. However, current methods still face the problem that the recognized 3D key points are inconsistent with the actual joint positions. The strategy that trains 2D to 3D networks using 3D human poses with corresponding 2D projections to solve this problem is effective. On this basis, we build a cascaded monocular 3D human pose estimation network, which uses a hierarchical supervision network, and uses the proposed composite residual module (CRM) and enhanced fusion module (EFM) as the main components. In the cascaded network, CRMs are stacked to form cascaded modules. Compared with the traditional residual module, the proposed CRM expands the information flow channels. In addition, the proposed EFM is alternately placed with cascaded modules, which addresses the problems of reduced accuracy and low robustness caused by multi-level cascade. We test the proposed network on the standard benchmark Human3.6M dataset and MPI-INF-3DHP dataset. We compare the results under the fully-supervised methods with six algorithms and the results under the weakly-supervised methods with five algorithms. We use the mean per joint position error (MPJPE) in millimeters as the evaluation index and get the best results.</p>", "Keywords": "3D human pose estimation; Residual network; Deep learning; Cascaded network", "DOI": "10.1007/s10489-022-03516-1", "PubYear": 2023, "Volume": "53", "Issue": "1", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Eletrical Information Engineering, Northeast Petroleum University, Daqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Eletrical Information Engineering, Northeast Petroleum University, Daqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Eletrical Information Engineering, Northeast Petroleum University, Daqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Eletrical Information Engineering, Northeast Petroleum University, Daqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Eletrical Information Engineering, Northeast Petroleum University, Daqing, China"}], "References": [{"Title": "A survey on monocular 3D human pose estimation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> Dong", "PubYear": 2020, "Volume": "2", "Issue": "6", "Page": "471", "JournalTitle": "Virtual Reality & Intelligent Hardware"}, {"Title": "Deep 3D human pose estimation: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "210", "Issue": "", "Page": "103225", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 94081469, "Title": "L’index éditorialisé Inducks entre amateurs et professionnels de la bande dessinée Disney : nouveaux équilibres et nouveaux modèles participatifs", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.1725", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94081478, "Title": "L’injonction au partage photographique : une forme de participation profitable pour le public ou pour l’institution ?", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.1900", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94081484, "Title": "La Nuit de l’info pendant une pandémie…: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.113", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " Le bureau de la Nuit de l’info", "Affiliation": ""}], "References": []}, {"ArticleId": 94081488, "Title": "Entretien avec deux lycéennes de Haute-Savoie: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.53", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94081498, "Title": "La fabrique des fembots pop stars : participation, co-création ou exploitation culturelle en ligne ?", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.1843", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Towards an Ontology and Ethics of Virtual Influencers", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "", "Page": "", "JournalTitle": "Australasian Journal of Information Systems"}]}, {"ArticleId": 94081499, "Title": "Concours CAPES Numérique et sciences informatiques: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.29", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94081502, "Title": "Estimation de posture 3D à partir de données imprécises et incomplètes : application à l’analyse d’activité d’opérateurs humains dans un centre de tri: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.127", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94081516, "Title": "Colorizing Grayscale CT images of human lungs using deep learning methods", "Abstract": "<p>Image colorization refers to computer-aided rendering technology which transfers colors from a reference color image to grayscale images or video frames. Deep learning elevated notably in the field of image colorization in the past years. In this paper, we formulate image colorization methods relying on exemplar colorization and automatic colorization, respectively. For hybrid colorization, we select appropriate reference images to colorize the grayscale CT images. The colours of meat resemble those of human lungs, so the images of fresh pork, lamb, beef, and even rotten meat are collected as our dataset for model training. Three sets of training data consisting of meat images are analysed to extract the pixelar features for colorizing lung CT images by using an automatic approach. Pertaining to the results, we consider numerous methods (i.e., loss functions, visual analysis, PSNR, and SSIM) to evaluate the proposed deep learning models. Moreover, compared with other methods of colorizing lung CT images, the results of rendering the images by using deep learning methods are significantly genuine and promising. The metrics for measuring image similarity such as SSIM and PSNR have satisfactory performance, up to 0.55 and 28.0, respectively. Additionally, the methods may provide novel ideas for rendering grayscale X-ray images in airports, ferries, and railway stations.</p><p>© The Author(s) 2022.</p>", "Keywords": "CNN;Colorization;Deep learning;Lung CT images", "DOI": "10.1007/s11042-022-13062-0", "PubYear": 2022, "Volume": "81", "Issue": "26", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Auckland University of Technology, Auckland, 1010 New Zealand."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Auckland University of Technology, Auckland, 1010 New Zealand."}], "References": []}, {"ArticleId": 94081628, "Title": "Weighted-Average Least Squares (WALS): Confidence and Prediction Intervals", "Abstract": "We consider inference for linear regression models estimated by weighted-average least squares (WALS), a frequentist model averaging approach with a Bayesian flavor. We propose a new simulation method that yields re-centered confidence and prediction intervals by exploiting the bias-corrected posterior mean as a frequentist estimator of a normal location parameter. We investigate the performance of WALS and several alternative estimators in an extensive set of Monte Carlo experiments that allow for increasing complexity of the model space and heteroskedastic, skewed, and thick-tailed regression errors. In addition to WALS, we include unrestricted and fully restricted least squares, two post-selection estimators based on classical information criteria, a penalization estimator, and Mallows and jackknife model averaging estimators. We show that, compared to the other approaches, WALS performs well in terms of the mean squared error of point estimates, and also in terms of coverage errors and lengths of confidence and prediction intervals.", "Keywords": "Linear model; WALS; Confidence intervals; Prediction intervals; Monte Carlo simulations; C11; C12; C18; C21; C52", "DOI": "10.1007/s10614-022-10255-5", "PubYear": 2023, "Volume": "61", "Issue": "4", "JournalId": 4021, "JournalTitle": "Computational Economics", "ISSN": "0927-7099", "EISSN": "1572-9974", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Palermo, Palermo, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Amsterdam and Tinbergen Institute, Amsterdam, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Rome Tor Vergata and EIEF, Rome, Italy"}], "References": []}, {"ArticleId": 94081713, "Title": "Chemically grafted polyurethane/graphene ternary slurry for advanced chemical–mechanical polishing of single-crystalline SiC wafers", "Abstract": "<p>This paper aims to develop an innovative ternary slurry named as G-slurry to be fabricated by a chemically grafted method to strongly bond PU/GO (polyurethane microspheres/ graphene oxide nanoplatelets) and with addition of SiO<sub>2</sub> abrasives. This G-slurry is demonstrated for efficient CMP of Si face of 4H-SiC wafers. FTIR spectra is adopted to prove having strong bonding between PU and GO by chemical modification of PU microspheres. With this G-slurry, a semi-fixed abrasive polishing (SFAP) process has been investigated to effectively polish SiC wafers, and its performance is compared with that of a counterpart 1 slurry which is consisting of PU and SiO<sub>2</sub> served as a benchmark in this work. In preparation of G-slurry, three important processing parameters include weight fraction of graphene oxide (GO), sonication time, and pH value. Effects on material removal rate (MRR), surface roughness, and surface roughness improvement rate (SRIR) of polished SiC wafers are investigated. SEM, Raman, and XPS (X-ray photoelectron spectroscopy) spectra are adopted to analyze the specific CMP mechanism of polished SiC wafers by G-slurry. Based on the mechanism, three key factors of G-slurry have been verified with its better performance on CMP of SiC wafers compared with two counterpart slurries and shown to be potentially applied for SiC wafer processing. </p>", "Keywords": "Ternary slurry; Chemically grafted method; Graphene oxide; Semi-fixed abrasive polishing (SFAP); Sonication time", "DOI": "10.1007/s00170-022-09241-w", "PubYear": 2022, "Volume": "120", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Hsien<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Computer Aided Engineering, Feng Chia University, Taichung, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University of Science and Technology, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Computer Aided Engineering, Feng Chia University, Taichung, Taiwan"}], "References": [{"Title": "Effects of compound diamond slurry with graphene for lapping of sapphire wafers", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "4755", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Recent developments and applications of chemical mechanical polishing", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "Page": "1419", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 94081720, "Title": "Improving sugarcane production in saline soils with Machine Learning and the Internet of Things", "Abstract": "The Indian sugar industry is the second largest in the world. Sugar is an essential domestic grocery item required in India producing over 25 million tonnes per annum. Sugarcane is the root of sugar products that grow in over 5 million hectares all over India. However, nearly 1.5 million hectares of overall farms are saline soil lands (high salt content). This leads to lower yields in sugarcane agriculture than what would be expected. Therefore, tackling the salinity problem is crucial to achieve strong food security as well as tackle the sustainability of farming practices in India that have reach beyond just sugarcane. This research proposes efficient, sustainable, smart farming techniques for sugarcane cultivation in salt-affected lands with the help of the Internet of Things (IoT) and Machine Learning (ML). The proposed model has been implemented in a real-world two hectare sugar cane field cultivated from saline soils using Raspberry PI IoT nodes to control the drip irrigation (water supply). The Naïve Bayes model has been used to train and predict the leaching requirement suggested by Food and Agriculture Organization of the United Nations (FAO) and United Nations Educational, Scientific and Cultural Organization (UNESCO) for efficient leaching water requirements. The performance of the proposed model has been evaluated in terms of sugar cane growth, cost of cultivation, as well as water requirements leading to an improved outlook for future use. Moreover, our results have been compared with regular sugar cane cultivation to show their effectiveness.", "Keywords": "Sugarcane production ; Smart agriculture ; Saline soil ; Machine Learning ; Internet of Things ; Sustainable farming", "DOI": "10.1016/j.suscom.2022.100743", "PubYear": 2022, "Volume": "35", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, VIT-AP University, Amaravathi. Andhra Pradesh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Math and Computer Science, Brandon University, Canada;Research Centre for Interneural Computing, China Medical University, Taichung, Taiwan;Corresponding author at: Department of Math and Computer Science, Brandon University, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Kongu Engineering College, Perundurai, Tamil Nadu, India"}], "References": [{"Title": "Service offloading oriented edge server placement in smart farming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "12", "Page": "2540", "JournalTitle": "Software: Practice and Experience"}, {"Title": "RETRACTED: An improved multilayer perceptron approach for detecting sugarcane yield production in IoT based smart agriculture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103822", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "SP2F: A secured privacy-preserving framework for smart agricultural Unmanned Aerial Vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "187", "Issue": "", "Page": "107819", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 94081735, "Title": "NWS volume 10 issue 1 Cover and Front matter", "Abstract": "", "Keywords": "", "DOI": "10.1017/nws.2022.2", "PubYear": 2022, "Volume": "10", "Issue": "1", "JournalId": 25796, "JournalTitle": "Network Science", "ISSN": "2050-1242", "EISSN": "2050-1250", "Authors": [], "References": []}, {"ArticleId": 94081799, "Title": "Numerical-analytical transformation method of analyzing nonlinear mathematical models with polynomial structure", "Abstract": "<p>The article considers a scientific problem of investigating mathematical models with polynomial nonlinearity, which are represented by systems of nonlinear differential equations. There is presented a numericall-analytical transformation method for investigating nonlinear dynamical systems with many degrees of freedom of polynomial structure. As opposed to the analogues, this method allows solving a wide range of problems for nonlinear systems \r of general polynomial structure while reducing the computational resource intensity. An algorithm of the method \r of transformations for investigating the nonlinear dynamic systems with m degrees of freedom is given. The research of stability of solutions of nonlinear dynamic systems with m degrees of freedom is carried out. Algorithmic formulas of transformation method for the solution of systems with nonlinearity of the fourth degree are given. Computational experiments on solving systems of differential equations with a small nonlinear part in the form of a multinomial \r of the sixth degree using the transformation method show fourth order accuracy in computation. A nonlinear mathematical model of the vibration protection system is investigated by the presented method of transformations. The theorem on determination of the stationary state for systems of differential equations of polynomial structure by transformation method is proved. Algorithmic formulas for computation are presented. A general matrix form for vector indices is presented. Formulas for economical computation of right-hand sides of polynomial structure are presented and it is proposed to apply <PERSON>'s scheme with coefficient preprocessing. This method allows studying the different modes \r of nonlinear models dynamics, for example, to determine such extreme modes as resonance, sub-harmonic, and polyharmonic modes. As an example of the method application, the vibration protection problem of the tower from external periodic influences was solved. The transformed solution takes into account all nonlinear polynomial components. The method allows to investigate the dynamics of a wide range of nonlinear systems with the necessary accuracy</p>", "Keywords": "", "DOI": "10.24143/2072-9502-2022-2-97-109", "PubYear": 2022, "Volume": "2022", "Issue": "2", "JournalId": 47501, "JournalTitle": "VESTNIK OF ASTRAKHAN STATE TECHNICAL UNIVERSITY. SERIES: MANAGEMENT, COMPUTER SCIENCE AND INFORMATICS", "ISSN": "2072-9502", "EISSN": "2224-9761", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ITMO University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ITMO University"}], "References": []}, {"ArticleId": ********, "Title": "The making of popstar fembots: Participation, co-creation, or online cultural exploitation?", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.2254", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Towards an Ontology and Ethics of Virtual Influencers", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "", "Page": "", "JournalTitle": "Australasian Journal of Information Systems"}]}, {"ArticleId": 94081815, "Title": "Les ordinateurs quantiques : un enthousiasme justifié: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.18.35", "PubYear": 2021, "Volume": "", "Issue": "18", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94081831, "Title": "Mendeleïeva : un jeu de plateau pour mettre en valeur les femmes en sciences: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.18.81", "PubYear": 2021, "Volume": "", "Issue": "18", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " Florence Sèdes", "Affiliation": ""}], "References": []}, {"ArticleId": 94081834, "Title": "The Inducks index, editorialized by Disney comics amateurs and professionals: New dynamics, new participatory models", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.2145", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94081855, "Title": "Information and telecommunication resources of critical infrastructures: features of interval security analysis", "Abstract": "<p>The object of the research is a new methodological approach to solving the problem of interval analysis \r of the security of information and telecommunication resources of critical infrastructures. This approach is one of the variants of practising the methods of the class midvalues (interval calculations). The approach characteristics were analyzed to determine its validity and usefulness for assessing the security indicators of such complex technical systems over a time interval. There have been considered theoretical aspects of building the algorithms for calculating the class midvalues of security levels of information and telecommunication resources of critical infrastructures, factors of calculating the upper and lower class midvalues of security levels. A sequence of calculations and analytical expressions for calculations on the example of a specific security indicator are proposed. The approach offers taking into account the modern requirements of security management subsystems of complex technical systems, the requirements of officials (auditors, security administrators) related to the inertia of decision-making processes, with the duration of cycles of security control and security management of systems of this class. It allows one to obtain not point (instantaneous), but interval estimates of security indicators, while the analysis is carried out with a predetermined frequency and takes into account the uncertainty of the initial data – observed and controlled security indicators. At the same time, interval analysis does not have great mathematical and computational complexity, but it allows one to obtain interval estimates of resource security adequate to control and management tasks, saves computing resources and, ultimately, works \r to increase the reliability of security control of modern critical infrastructures</p>", "Keywords": "", "DOI": "10.24143/2073-5529-2022-2-33-40", "PubYear": 2022, "Volume": "2022", "Issue": "2", "JournalId": 47501, "JournalTitle": "VESTNIK OF ASTRAKHAN STATE TECHNICAL UNIVERSITY. SERIES: MANAGEMENT, COMPUTER SCIENCE AND INFORMATICS", "ISSN": "2072-9502", "EISSN": "2224-9761", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "St. Petersburg Federal Research Center of the Russian  Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "St. Petersburg Institute for Informatics and Automation of the Russian Academy of Sciences"}], "References": []}, {"ArticleId": 94081922, "Title": "Integration of design and NMPC-based control for chemical processes under uncertainty: An MPCC-based framework", "Abstract": "The use of nonlinear model predictive control (NMPC) for the integration of design and control remains as an open area of research. When NMPC is incorporated into the framework for design and control, this results into a bilevel problem formulation. The search of a solution for a bilevel problem is often carried out with the implementation of systematic iterative methodologies. In this work, we implement the classical KKT transformation strategy to transform the original bilevel problem for simultaneous design and NMPC-based control into a single-level dynamic optimization problem. This single-level formulation is referred to as a mathematical program with complementarity constraints (MPCC). Violations to constraint qualifications (CQs) are avoided through the implementation of several reformulation techniques. This facilitates the convergence of the MPCC for optimal design and control. The use of an MPCC-based formulation allows to fully incorporate the NMPC’s necessary conditions for optimality into the design problem as a set of algebraic constraints. Accordingly, the MPCC is solved as a conventional NLP, i.e., we avoid the use of decomposition or simplification methodologies for the solution of the bilevel problem. This guarantees optimality in the solution for integrated design and NMPC-based control. This strategy was implemented in three case studies involving small, medium-scale, and highly nonlinear systems to demonstrate the application of MPCCs for design and NMPC-based control under process disturbances and uncertainty.", "Keywords": "Integration of design and control ; Complementarity constraints ; KKT conditions ; Model predictive control ; Parameter uncertainty", "DOI": "10.1016/j.compchemeng.2022.107815", "PubYear": 2022, "Volume": "162", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering, University of Waterloo, Waterloo, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>l", "Affiliation": "Department of Chemical Engineering, University of Waterloo, Waterloo, Canada;Corresponding author"}], "References": [{"Title": "New frontiers, challenges, and opportunities in integration of design and control for enterprise-wide sustainability", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "106610", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "On the Implementation of Nonlinear Model Predictive Control for Simultaneous Designand Control Using a Back-Off Approach", "Authors": "<PERSON>; <PERSON>; <PERSON>-<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "11551", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 94081941, "Title": "Characterization of signal kinetics in real time surgical tissue classification system", "Abstract": "Effective surgical margin assessment is paramount for good oncological outcomes and new methods are in active development. One emerging approach is the analysis of the chemical composition of surgical smoke from tissues. Surgical smoke is typically removed with a smoke evacuator to protect the operating room staff from its harmful effects to the respiratory system. Thus, analysis of the evacuated smoke without disturbing the operation is a feasible approach. Smoke transportation is subject to lags that affect system usability. We analyzed the smoke transportation delay and evaluated its effects to tissue classification with differential mobility spectrometry in a simulated setting using porcine tissues. With a typical smoke evacuator setting, the front of the surgical plume reaches the analysis system in 380 ms and the sensor within one second. For a typical surgical incision (duration 1.5 s), the measured signal reaches its maximum in 2.3 s and declines to under 10% of the maximum in 8.6 s from the start of the incision. Two-class tissue classification was tested with 2, 3, 5, and 11 s repetition rates resulting in no significant differences in classification accuracy, implicating that signal retention from previous samples is mitigated by the classification algorithm.", "Keywords": "Differential ion mobility spectrometry ; Surgical margin analysis ; Tissue classification ; Signal carry-over", "DOI": "10.1016/j.snb.2022.131902", "PubYear": 2022, "Volume": "365", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sensor Technology and Biomeasurements, Faculty of Medicine and Health Technology, Tampere University, Hervanta Campus, Korkeakoulunkatu 3, FI-33720 Tampere, Finland;Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland;Correspondence to: Tampere University, Korkeakoulunkatu 6, FI-33101 Tampere, Finland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Sensor Technology and Biomeasurements, Faculty of Medicine and Health Technology, Tampere University, Hervanta Campus, Korkeakoulunkatu 3, FI-33720 Tampere, Finland;Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Pázmány Péter Catholic University, Faculty of Information Technology and Bionics, Budapest Práter u. 50/A 1083, Hungary"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Surgery, Faculty of Medicine and Health Technology, Tampere University, Kauppi Campus, Arvo Building, Arvo Ylpön katu 34, FI-33520 Tampere, Finland;Department of Pathology, Fimlab Laboratories, Arvo Ylpön katu 4, FI-33520 Tampere, Finland"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland;Surgery, Faculty of Medicine and Health Technology, Tampere University, Kauppi Campus, Arvo Building, Arvo Ylpön katu 34, FI-33520 Tampere, Finland;Vascular Centre, Tampere University Hospital, Central Hospital, P.O. Box 2000, FI-33521 Tampere, Finland"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Olfactomics Ltd, Kampusareena, Korkeakoulunkatu 7, FI-33720 Tampere, Finland"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Sensor Technology and Biomeasurements, Faculty of Medicine and Health Technology, Tampere University, Hervanta Campus, Korkeakoulunkatu 3, FI-33720 Tampere, Finland"}], "References": []}, {"ArticleId": 94081954, "Title": "Authorship Attribution in Bangla Literature (AABL) via Transfer Learning using ULMFiT", "Abstract": "<p>Authorship Attribution is the task of creating an appropriate characterization of text that captures the authors’ writing style to identify the original author of a given piece of text. With increased anonymity on the internet, this task has become increasingly crucial in various security and plagiarism detection fields. Despite significant advancements in other languages such as English, Spanish, and Chinese, Bangla lacks comprehensive research in this field due to its complex linguistic feature and sentence structure. Moreover, existing systems are not scalable with the increasing number of authors, and performance drops with the small number of samples per author. In this paper, we propose the use of Average-Stochastic Gradient Descent Weight-Dropped Long Short-Term Memory (AWD-LSTM) architecture and an effective transfer learning approach that addresses the problem of complex linguistic features extraction and scalability for authorship attribution in Bangla Literature (AABL). We analyze the effect of different tokenization, such as word, sub-word, and character level tokenization, and demonstrate the effectiveness of these tokenizations in the proposed model. Moreover, we introduce the publicly available Bangla Authorship Attribution Dataset of 16 authors (BAAD16) containing 17,966 sample texts and 13.4+ million words to solve the standard dataset scarcity problem and release six variations of pre-trained language models for use in any Bangla NLP downstream task. For evaluation, we used our developed BAAD16 dataset as well as other publicly available datasets. Empirically, our proposed model outperformed state-of-the-art models and achieved 99.8% accuracy in the BAAD16 dataset. Furthermore, we showed that the proposed system scales much better with the increasing number of authors, and performance remains steady even with few training samples.</p>", "Keywords": "", "DOI": "10.1145/3530691", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Shahjalal University of Science and Technology"}], "References": [{"Title": "Deep Learning Based Sentiment Analysis in a Code-Mixed English-Hindi and English-Bengali Social Media Corpus", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "5", "Page": "2050014", "JournalTitle": "International Journal on Artificial Intelligence Tools"}]}, {"ArticleId": 94082036, "Title": "Financial time series forecasting using optimized multistage wavelet regression approach", "Abstract": "<p>This paper presents financial time series forecasting with multistage wavelet transform (WT). First, the time series data is processed through WT with different mother wavelet functions to extract high frequency and low frequency coefficients. Later, standard particle swarm optimization (PSO) algorithm is utilized to find optimal regression models in order to predict future samples. Mean square error (MSE) is opted as cost function for PSO to find optimal coefficients of the regression model. This study further extended to various mother wavelet functions and their decomposition levels to investigate their impacts on time series prediction. These investigations help to data scientists for selection of process parameters and variables. Further, the impact of control parameters of PSO is also discussed to show the importance in the search mechanism especially in regression problems.</p><p>© The Author(s), under exclusive licence to <PERSON><PERSON><PERSON> Vidyapeeth's Institute of Computer Applications and Management 2022.</p>", "Keywords": "Linear regression;Particle swarm optimization;Wavelet transform", "DOI": "10.1007/s41870-022-00924-x", "PubYear": 2022, "Volume": "14", "Issue": "4", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, <PERSON><PERSON>, Guntur, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "KL University, Vijayawada, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of EEE, SRKR Engineering College, Bhimavaram, India."}], "References": [{"Title": "Financial time series forecasting with deep learning : A systematic literature review: 2005–2019", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106181", "JournalTitle": "Applied Soft Computing"}, {"Title": "Gold volatility prediction using a CNN-LSTM approach", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "113481", "JournalTitle": "Expert Systems with Applications"}, {"Title": "PSO–LSTM for short term forecast of heterogeneous time series electricity price signals", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2375", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Time series forecasting based on kernel mapping and high-order fuzzy cognitive maps", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "206", "Issue": "", "Page": "106359", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Particle swarm optimization and intuitionistic fuzzy set-based novel method for fuzzy time series forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "2", "Page": "285", "JournalTitle": "Granular Computing"}]}, {"ArticleId": 94082092, "Title": "Incomplete smooth S-curve acceleration and deceleration feedrate planning modeling and analysis", "Abstract": "<p>The toolpath smoothing and feedrate scheduling in computer numerical control (CNC) system are critical for high-speed and high-precision machining. The existing toolpath smoothing schemes perform insufficiently in raising the smoothness of feedrate trajectory. Aiming at increasing the smoothness of toolpath and reducing machining vibration, a smooth S-curve acceleration/deceleration(AD) control algorithm based on the jounce limited profile is proposed. In the paper, for the curves that do not meet the complete smooth S-curve acceleration/deceleration conditions, they are divided into three cases according to the length of the curve and give a detailed calculation formula. All trajectory parameters are derived with the analytical algorithm to ensure an acceptable computational complexity. Finally, a series of numerical simulations and actual machining and measurement experiments are conducted, and the simulation and experimental results have showed the good performance of the proposed algorithm compared with previous method both in motion smooth and surface quality.</p>", "Keywords": "Feedrate planning; Jerk-continuous feedrate profile; Incomplete smooth acceleration/deceleration; Surface roughness", "DOI": "10.1007/s00170-022-09236-7", "PubYear": 2022, "Volume": "120", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Hefei University of Technology, Hefei, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Hefei University of Technology, Hefei, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Hefei University of Technology, Hefei, People’s Republic of China"}], "References": []}, {"ArticleId": 94082094, "Title": "Effectiveness of virtual reality exposure treatment for posttraumatic stress disorder due to motor vehicle or industrial accidents", "Abstract": "<p>Virtual reality exposure treatment (VRET) for post-traumatic stress disorder (PTSD) is an emerging treatment. The purpose of this study was to examine the effectiveness and safety of VRET in patients with PTSD due to motor vehicle or industrial accidents. Twenty-six patients with PTSD (19 motor vehicle accidents and 7 industrial accidents) and eighteen subjects without PTSD were enrolled in five VRET sessions that were conducted using a head-mounted display. The VRET was based on systematic desensitization and included psychoeducation and training for breathing and relaxation techniques. The effectiveness of VRET was evaluated using the Posttraumatic Stress Disorder Checklist-Civilian version (PCL-C), Impact of Event Scale-Revised (IES-R), Clinical Global Impression-Severity scale (CGI-S), and Sheehan Disability Scale (SDS). Safety was assessed using the Simulator Sickness Questionnaire and Presence Questionnaire. After controlling for age, sex, marital status, job, economic status, and body mass index, we found that the CGI-S ( F = 12.76, p = 0.001), PCL-C ( F = 11.87, p = 0.002), IES-R (total score; F -8.31, p = 0.007), and SDS-A ( F = 7.53, p = 0.010) scores in the VRET group were lower than those in the control group. Responses to the Simulator Sickness and Presence questionnaires did not differ significantly between the VRET and control groups ( p > 0.05). In conclusion, for patients with PTSD due to motor vehicle accidents, VRET is a safe and potentially effective treatment method. Future randomized controlled studies are needed to provide stronger evidence for the effectiveness of VRET in patients with PTSD. </p>", "Keywords": "Virtual reality; PTSD; Exposure; Motor vehicle accident; Industrial accident", "DOI": "10.1007/s10055-022-00623-9", "PubYear": 2022, "Volume": "26", "Issue": "4", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "Won-<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Inha University Hospital, Incheon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Gachon University Gil Medical Center, Incheon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychiatry, Samsung Medical Center Sungkyunkwan University School of Medicine, Seoul, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Inha University Hospital, Incheon, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Inha University Hospital, Incheon, Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Gachon University Gil Medical Center, Incheon, Republic of Korea"}, {"AuthorId": 7, "Name": "Kyoung-<PERSON><PERSON> Na", "Affiliation": "Department of Psychiatry, Gachon University Gil Medical Center, Incheon, Republic of Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Gachon University, Seongnam, Republic of Korea"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of IT Convergence Engineering, Gachon University Graduate School, Seongnam, Republic of Korea"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Inha University Hospital, Incheon, Republic of Korea"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Gachon University Gil Medical Center, Incheon, Republic of Korea"}], "References": []}, {"ArticleId": 94082115, "Title": "Post hurricane Harvey dataset: Portable free fall penetrometer and chirp sonar measurements of Texas rivers", "Abstract": "This data article includes datasets collected at three sections of the Guadalupe River, Brazos River, and Colorado River in Texas, USA, almost ten months post Hurricane Harvey. Instruments used include a Portable Free Fall Penetrometer (PFFP), Chirp Sonar, Side Scan Sonar (SSS), Acoustic Doppler Current Profiler (ADCP) and sediment grab sampler. Measurements were collected from small vessels such as canoes and a 6-feet inflatable zodiac and were supported by long term hydrodynamic data from local river water level and discharge gages. Laboratory testing performed on samples collected included grain size analysis, Atterberg test, and erodibility testing using an Erosion Function Apparatus (EFA). Data collected were analyzed to estimate sediment strength derived from the PFFP, backscatter intensity recorded by the chirp sonar, and soil sample characteristics. The dataset includes raw and processed data for the measurements recorded by the instruments, location of measurements, and laboratory testing grouped for each river with a readme file which gives a potential for reuse by other researchers for further analysis if needed. This data article is representing supplementary data to the following research article published in Engineering Geology [1] : Jaber, R., Stark, N., Jafari, N., &amp; <PERSON><PERSON>n, N. “Combined Portable Free Fall Penetrometer and Chirp Sonar Measurements of three Texas River Sections Post Hurricane Harvey.” Raw data was published [2] : <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> (2020). Combined Geotechnical and Geophysical Investigation of Texas Rivers Post Hurricane Harvey. in Combined Geotechnical and Geophysical Investigation of Texas Rivers Post Hurricane Harvey. DesignSafe-CI. https://doi.org/10.17603/ds2-835m-zp94", "Keywords": "Extreme events;Geotechnical investigation;Offshore geotechnical properties;Riverine environments;Site characterization", "DOI": "10.1016/j.dib.2022.108203", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Virginia Tech., 200 Patton Hall, Blacksburg, VA 24061, United States."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Virginia Tech., 200 Patton Hall, Blacksburg, VA 24061, United States."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Louisiana State University, 3212D <PERSON>, Baton Rouge, LA 70803, United States."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Clemson University, 202 Lowry Hall, Clemson, SC 29634, United States."}], "References": []}, {"ArticleId": 94082133, "Title": "Using a small dataset to classify strength-interactions with an elastic display: a case study for the screening of autism spectrum disorder", "Abstract": "<p>Health data collection of children with autism spectrum disorder (ASD) is challenging, time-consuming, and expensive; thus, working with small datasets is inevitable in this area. The diagnosis rate in ASD is low, leading to several challenges, including imbalance classes, potential overfitting, and sampling bias, making it difficult to show its potential in real-life situations. This paper presents a data analytics pilot-case study using a small dataset leveraging domain-specific knowledge to uncover differences between the gestural patterns of children with ASD and neurotypicals. We collected data from 59 children using an elastic display we developed during a sensing campaign and 9 children using the elastic display as part of a therapeutic program. We extracted strength-related features and selected the most relevant ones based on how the motor atypicality of children with ASD influences their interactions: children with ASD make smaller and narrower gestures and experience variations in the use of strength. The proposed machine learning models can correctly classify children with ASD with 97.3% precision and recall even if the classes are unbalanced. Increasing the size of the dataset via synthetic data improved the model precision to 99%. We finish discussing the importance of leveraging domain-specific knowledge in the learning process to successfully cope with some of the challenges faced when working with small datasets in a concrete, real-life scenario.</p>", "Keywords": "Autism spectrum disorder; Screnning of ASD; Small dataset; Elastic display; Machine learning", "DOI": "10.1007/s13042-022-01554-2", "PubYear": 2023, "Volume": "14", "Issue": "1", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CICESE, Ensenada, Mexico"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chapman University, Orange, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CICESE, Ensenada, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CICESE, Ensenada, Mexico"}], "References": []}, {"ArticleId": 94082137, "Title": "STRIDER: Steric hindrance and metal coordination identifier", "Abstract": "Biomolecular structural knowledge is important to understand the biological processes and the mechanisms underlying human diseases. In silico modeling plays a vital role in de novo design and docking of biomacromolecules as well as in exploring their conformational dynamics. Additionally, it has a major role in acquiring the structural insights using the parameters derived from experimental techniques such as cryo-electron microscopy. Steric hindrance is one of the important measures to validate the accuracy of the modeled biomolecular structures. A web user interface (WUI), namely, STRIDER ( st eric hind r ance and metal coord i nation i d entifi er ) ( www.iith.ac.in/strider/ ) estimates and reports pairwise inter- and intra- molecular steric hindrances using the van der <PERSON> radius of 117 elements. STRIDER identifies and reports the coordination pattern of 64 metals in an interactive mode. It can provide conformer wise interaction pattern(s) of an ensemble of conformers which is needed in circumventing sampling issue in flexible docking, protein folding and structure based virtual screening. Further, it generates a pymol session file which highlights the aforementioned interaction for an offline analysis. Since STRIDER simply requires the Cartesian coordinates of a molecule in PDB format, any chemical structure can be given as an input. Functionality of STRIDER is illustrated here by considering several examples.", "Keywords": "Ensemble of conformers;Metal coordination;Steric hindrance;Web interface;van der Waals radius", "DOI": "10.1016/j.compbiolchem.2022.107686", "PubYear": 2022, "Volume": "98", "Issue": "", "JournalId": 1395, "JournalTitle": "Computational Biology and Chemistry", "ISSN": "1476-9271", "EISSN": "1476-928X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biotechnology, Indian Institute of Technology Hyderabad, Kandi, Telangana State 502285, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biotechnology, Indian Institute of Technology Hyderabad, Kandi, Telangana State 502285, India. Electronic address:  ."}], "References": []}, {"ArticleId": 94082140, "Title": "Facial Action Units Detection to Identify Interest Emotion: An Application of Deep Learning", "Abstract": "<p>For human beings, facial expression is one of the most powerful and natural ways to communicate their emotions and intentions. A human being is able to recognize facial expressions effortlessly, but for a machine, this task is very difficult. Today, facial expression recognition is proving to be one of the most relevant applications in many fields such as human–computer interaction, medicine, security, education, etc. Facial Action Coding System (FACS) is a method that describes face movements. This later became a main description tool used in the studies concerned with facial expression. In this paper, we propose an action units recognition system which reflects interest emotion using deep learning, particularly the Convolutional Neural Network (CNN) architecture “MobileNetV2”. Our choice of this system is motivated by its success in image classification. Our system allows detecting action units which define interest emotion from an input image. In other words, our classifier differentiates interest facial movements from other affective states. This identification is very useful in several areas, particularly in e-learning: For example, knowing whether a distance learner is interested or not in the course certainly influences the quality of learning and reduces the dropout rate. Our proposed approach presented a very satisfactory recognition rate despite the absence of a large database.</p>", "Keywords": "FACS; interest; emotion; action units; Convolutional Neural Network; facial expression; MobileNetV2", "DOI": "10.1142/S2424922X22500061", "PubYear": 2022, "Volume": "14", "Issue": "1n02", "JournalId": 17622, "JournalTitle": "Advances in Data Science and Adaptive Analysis", "ISSN": "2424-922X", "EISSN": "2424-9238", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Laboratory on Computer Science’s Complex Systems (RELA(CS)), Faculty of Exact Sciences, Natural Sciences and Life, Larbi Ben M’Hidi University, Oum El Bouaghi 04000, Algeria"}], "References": []}, {"ArticleId": 94082145, "Title": "A Study of Health Insurance Fraud in China and Recommendations for Fraud Detection and Prevention: ", "Abstract": "<p>Healthcare insurance fraud influences not only organizations by overburdening the already fragile healthcare systems, but also individuals in terms of increasing premiums in health insurance and even fatalities. Identifying the behavioral characteristics of fraudulent claims can help shed light on the development of artificial intelligence and machine learning technologies to detect fraud in health information system research. In this paper, a theoretical model of medical insurance fraud identification is proposed, which characterizes the judgment variables of fraud from the three dimensions of time, quantity, and expenses. The model is verified with large-scale, real-world medical records. Our study shows that, in comparison with claims made by normal people, fraudulent claims usually have a greater frequency of hospital visits, and more medical bills, accompanied by higher amounts of medical expenses. An interesting discovery is that the price per bill for fraudulent cases is not statistically different from the normal cases.</p>", "Keywords": "", "DOI": "10.4018/JOEUC.301271", "PubYear": 2022, "Volume": "34", "Issue": "4", "JournalId": 11438, "JournalTitle": "Journal of Organizational and End User Computing", "ISSN": "1546-2234", "EISSN": "1546-5012", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hebei University of Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Brown University, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Hebei University of Technology, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Iowa State University, USA"}], "References": [{"Title": "Business Analytics/Business Intelligence and IT Infrastructure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "4", "Page": "138", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "Research Commentary on IS/IT Role in Emergency and Pandemic Management", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "2", "Page": "67", "JournalTitle": "Journal of Database Management"}, {"Title": "Financial fraud detection applying data mining techniques: A comprehensive review from 2009 to 2019", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100402", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 94082182, "Title": "Event-triggered adaptive consensus for stochastic multi-agent systems with saturated input and partial state constraints", "Abstract": "This paper is concerned with the consensus tracking problem of stochastic multi-agent systems with both output, partial state constraints, and input saturation via event-triggered strategy. To handle with the saturated control inputs, the saturation function is transformed into a linear form of the control input. By using radial basis function neural network to approximate the unknown nonlinear function, the unmeasurable states are acquired by an adaptive observer. To ensure that the constraints of system outputs and partial states are never violated, an appropriate time-varying barrier <PERSON><PERSON><PERSON><PERSON> function is constructed. The control scheme is event-triggered in order to save communication resources. The proposed distributed controller can guarantee the boundedness of all system signals, the consensus tracking with a small bounded error, and the avoidance of the Zeno behavior by using backstepping techniques. The validity of the theoretical results is verified by computer simulation.", "Keywords": "", "DOI": "10.1016/j.ins.2022.04.035", "PubYear": 2022, "Volume": "603", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Three Gorges Mathematical Research Center, China Three Gorges University, Yichang 443002, China;College of Science, China Three Gorges University, Yichang 443002, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Three Gorges Mathematical Research Center, China Three Gorges University, Yichang 443002, China;College of Science, China Three Gorges University, Yichang 443002, China;Corresponding author at: Three Gorges Mathematical Research Center, China Three Gorges University, Yichang 443002, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre of New Energy Systems, Department of Electrical, Electronics and Computer Engineering, University of Pretoria, Pretoria, South Africa"}], "References": [{"Title": "Adaptive neural network control for time-varying state constrained nonlinear stochastic systems with input saturation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "191", "JournalTitle": "Information Sciences"}, {"Title": "Secure bipartite tracking control of a class of nonlinear multi-agent systems with nonsymmetric input constraint against sensor attacks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "504", "JournalTitle": "Information Sciences"}, {"Title": "Disturbance-observer-based finite-time adaptive fuzzy control for non-triangular switched nonlinear systems with input saturation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "561", "Issue": "", "Page": "152", "JournalTitle": "Information Sciences"}, {"Title": "Fuzzy adaptive event-triggered control for a class of nonlinear systems with time-varying full state constraints", "Authors": "<PERSON><PERSON>; Yuan-<PERSON><PERSON>", "PubYear": 2021, "Volume": "563", "Issue": "", "Page": "111", "JournalTitle": "Information Sciences"}, {"Title": "Neural networks-based adaptive event-triggered consensus control for a class of multi-agent systems with communication faults", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "99", "JournalTitle": "Neurocomputing"}, {"Title": "Optimal consensus model-free control for multi-agent systems subject to input delays and switching topologies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "497", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 94082191, "Title": "Diversion inference model of learning effectiveness supported by differential evolution strategy", "Abstract": "AI-enabled Interactive learning environment has become an important platform. It needs to reduce or improve the ineffective and invalid learning behavior, and build a timely and effective diversion inference model of learning effectiveness to provide early warning. Through the early warning, Timely identify risk learners, make effective early warning responses and make targeted interventions. Early warning is an automatic measure based on learning behavior, it evaluates the risk trend, calculates the possible probability of learners&#x27; failure in assessment and leaving early. This study proposes and designs a diversion inference model of learning effectiveness. Firstly, we analyze and design one differential evolution strategy. Secondly, the convolutional neural network is improved and optimized based on the differential evolution strategy. Thirdly, the corresponding algorithms are designed and applied. Finally, based on the visualization of the experimental results, we put forward the intervention measures. The technical means might serve the precise teaching intervention and learning recommendation, and has strong theoretical value and practical significance for AI-enabled interactive learning processes.", "Keywords": "Interactive learning environment ; Differential evolution strategy ; Convolution neural network ; Inference model of learning effectiveness diversion ; Education big data ; Learning analytics", "DOI": "10.1016/j.caeai.2022.100071", "PubYear": 2022, "Volume": "3", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Education, Qufu Normal University, Qufu, Shandong, 273165, China;Chinese Academy of Education Big Data, Qufu Normal University, Qufu, Shandong, 273165, China;School of Computer Science, Qufu Normal University, Rizhao, Shandong, 276826, China;Faculty of Education, Qufu Normal University, Qufu, Shandong, 273165, China"}], "References": [{"Title": "A\n three‐step\n model for the gamification of training and automaticity acquisition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "994", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Reinforcing learning in Deep Belief Networks through nature-inspired optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107466", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 94082227, "Title": "Network intrusion detection using ACO-DNN model with DVFS based energy optimization in cloud framework", "Abstract": "Recent technologies and innovations have encouraged users to adopt cloud-based environment. Network intrusion detection (NID) is an important method for network system administrators to detect various security holes. The performance of traditional NID methods can be affected when unknown or new attacks are detected. For instance, existing intrusion detection systems may have overfitting, low classification accuracy, and high false positive rate (FPR) when faced with significantly large volume and variety of network data. For that reason, this system has been agreed by many establishments to allure the users with its suitable features. Because of its design, it is exposed to malicious attacks. An Intrusion Detection System (IDS) is required to handle these issues which can detect such attacks accurately in a cloud environment. To analyze the IDS datasets some of the predominant choices are Deep learning and Machine learning (ML) algorithms. By adopting nature-inspired algorithms, the problems concerning the data quality and the usage of high-dimensional data can be managed. In this study the datasets KDD Cup 99 and NSL-KDD are used. The dataset is cleaned using the min-max normalization technique and it is processed using the 1-N encoding approach for achieving homogeneity. Dimensionality reduction is done using the Ant colony optimization (ACO) algorithm and further processing is done using the deep neural network (DNN). To minimize the energy consumption we have adopted the Dynamic Voltage and Frequency Scaling (DVFS) mechanism to the system. The main reason to set up this concept is to develop a balance between the energy consumption and the time of different modes of VMs or hosts. The proposed model is validated and compared with ACO and Principal component analysis (PCA)-based (Naïve Bayes) NB models, the experimental outcomes proved the superiority of the ACO-DNN model over the existing methods.", "Keywords": "Network Intrusion detection system (NIDS) ; Deep neural network (DNN) ; Ant colony optimization (ACO) ; DVFS ; Dimension reduction ; NSL-KDD dataset", "DOI": "10.1016/j.suscom.2022.100746", "PubYear": 2022, "Volume": "35", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dr <PERSON> <PERSON> National Institute of Technology, Jalandhar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Dehradun, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Communications, Middlesex University, London, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Dehradun, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of computer science, Amity University, Noida, Uttar Pradesh, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Petroleum and Energy Studies, Dehradun, India;Corresponding author"}], "References": [{"Title": "Evolving deep learning architectures for network intrusion detection using a double PSO metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "107042", "JournalTitle": "Computer Networks"}, {"Title": "Detection of distributed denial of service attack in cloud computing using the optimization-based deep networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "405", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "An intrusion detection approach based on improved deep belief network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "10", "Page": "3162", "JournalTitle": "Applied Intelligence"}, {"Title": "Intrusion detection approach based on optimised artificial neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "705", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 94082267, "Title": "How users of automated vehicles benefit from predictive ambient light displays", "Abstract": "With the introduction of Level 3 and 4 automated driving, the engagement in a variety of non-driving related activities (NDRAs) will become legal. Previous research has shown that users desire information about the remaining time in automated driving mode and system status information to plan and terminate their activity engagement. In past studies, however, the positive effect of this additional information was realized when it was integrated in or displayed close by the NDRA. As future activities and corresponding items will be diverse, a device-independent and non-interruptive way of communication is required to continuously keep the user informed, thus avoiding negative effects on driver comfort and safety. With a set of two driving simulator studies, we have investigated the effectiveness of ambient light display (ALD) concepts communicating remaining time and system status when engaged in visually distracting NDRAs. In the first study with 21 participants, a traffic light color-coded ALD concept (LED stripe positioned at the bottom of the windshield) was compared to a baseline concept in two subsequent drives. Subjects were asked to rate usability, workload, trust, and their use of travel time after each drive. Furthermore, gaze data and NDRA disengagement timing was analyzed. The ALD with three discrete time steps led to improved usability ratings and lower workload levels compared to the baseline interface without any ALD. No significant effects on trust, attention ratio, travel time evaluation, and NDRA continuation were found, but a vast majority favored the ALD. Due to this positive evaluation, the traffic light ALD concept was subsequently improved and compared to an elapsing concept in a subsequent study with 32 participants. In addition to the first study, the focus was on the intuitiveness of the developed concepts. In a similar setting, results revealed no significant differences between the ALD concepts in subjective ratings (workload, usability, trust, travel time ratings), but advantages of the traffic light concept can be found in terms of its intuitiveness and the level of support experienced.", "Keywords": "Automated driving ; User information needs ; Ambient light displays ; Non-driving related activities", "DOI": "10.1016/j.apergo.2022.103762", "PubYear": 2022, "Volume": "103", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chair of Ergonomics, Department of Mechanical Engineering, Technical University of Munich, Garching, BY, 85748, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chair of Ergonomics, Department of Mechanical Engineering, Technical University of Munich, Garching, BY, 85748, Germany"}, {"AuthorId": 3, "Name": "Luca<PERSON><PERSON>", "Affiliation": "Chair of Ergonomics, Department of Mechanical Engineering, Technical University of Munich, Garching, BY, 85748, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Chair of Ergonomics, Department of Mechanical Engineering, Technical University of Munich, Garching, BY, 85748, Germany"}], "References": [{"Title": "Information Needs and Visual Attention during Urban, Highly Automated Driving—An Investigation of Potential Influencing Factors", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "62", "JournalTitle": "Information"}, {"Title": "From HMI to HMIs: Towards an HMI Framework for Automated Driving", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "61", "JournalTitle": "Information"}, {"Title": "The Effects of a Predictive HMI and Different Transition Frequencies on Acceptance, Workload, Usability, and Gaze Behavior during Urban Automated Driving", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "73", "JournalTitle": "Information"}, {"Title": "Multi-Vehicle Simulation in Urban Automated Driving: Technical Implementation and Added Benefit", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "272", "JournalTitle": "Information"}, {"Title": "Does Information on Automated Driving Functions and the Way of Presenting It before Activation Influence Users’ Behavior and Perception of the System?", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "54", "JournalTitle": "Information"}]}, {"ArticleId": 94082276, "Title": "Behavioral Predictive Analytics Towards Personalization for Self-management: a Use Case on Linking Health-Related Social Needs", "Abstract": "<p>The objective of this research is to investigate the feasibility of applying behavioral predictive analytics to optimize patient engagement in diabetes self-management, and to gain insights on the potential of infusing a chatbot with NLP technology for discovering health-related social needs. In the U.S., less than 25% of patients actively engage in self-health management, even though self-health management has been reported to associate with improved health outcomes and reduced healthcare costs. The proposed behavioral predictive analytics relies on manifold clustering to identify subpopulations segmented by behavior readiness characteristics that exhibit non-linear properties. For each subpopulation, an individualized auto-regression model and a population-based model were developed to support self-management personalization in three areas: glucose self-monitoring, diet management, and exercise. The goal is to predict personalized activities that are most likely to achieve optimal engagement. In addition to actionable self-health management, this research also investigates the feasibility of detecting health-related social needs through unstructured conversational dialog. This paper reports the result of manifold clusters based on 148 subjects with type 2 diabetes and shows the preliminary result of personalization for 22 subjects under different scenarios, and the preliminary results on applying Latent Dirichlet Allocation to the conversational dialog of ten subjects for discovering social needs in five areas: food security, health (insurance coverage), transportation, employment, and housing.</p>", "Keywords": "Self-health management; Behavioral predictive analytics; Natural language processing (NLP); Latent dirichlet allocation", "DOI": "10.1007/s42979-022-01092-2", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Queens College/City University of NY, Queens, USA;Graduate Center/City University of NY, New York, USA;SIPPA Solutions, Queens, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "SIPPA Solutions, Queens, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "SIPPA Solutions, Queens, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Public Health, Hunter College/City University of NY, New York, USA"}], "References": [{"Title": "YAKE! Keyword extraction from single documents using multiple local features", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "257", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 94082279, "Title": "Hyperspectral reconstruction method for optically complex inland waters based on bio-optical model and sparse representing", "Abstract": "For better use of well-performed water quality parameter estimation models and the comprehensive use of multi-source remote sensing data, hyperspectral reconstruction is urgently needed in the remote sensing of optically complex inland waters. In this study, we proposed a bio-optical–based hyperspectral reconstruction (BBHR) algorithm to generate hyperspectral above-surface remote-sensing reflectance ( R <sub>rs</sub>) data ranging in wavelength from 400 to 800 nm. One core advantage of the BBHR method is its in situ data independency, which theoretically renders the algorithm universal. The other advantage is its ability to reconstruct hyperspectral R <sub>rs</sub> for the 400–800 nm spectral range, which facilitates the construction of more high accuracy chlorophyll-a concentration ( C <sub>chla</sub>) estimation models for optically complex waters. The reconstruction was tested by employing six widely used multispectral sensors: the Medium Resolution Imaging Spectrometer, (MERIS), Sentinel-3 Ocean and Land Color Instrument (S3 OLCI), Sentinel-2 Multispectral Instrument (S2 MSI), Geostationary Ocean Color Imager (GOCI), Visible Infrared Imaging Radiometer Suite (VIIRS), and Moderate Resolution Imaging Spectroradiometer (MODIS). The model performance was validated by using a ASD FieldSpec spectroradiometer-measured hyperspectral dataset containing 1396 samples and a satellite- in-situ match-up dataset with 66 samples. The results show that the proposed BBHR method exhibits satisfactory performance. The average mean absolute percentage error (MAPE), root mean square error (RMSE), R<sup>2</sup> and bias indices of the BBHR-reconstructed R <sub>rs</sub> over all spectral bands of the six multispectral sensors were 3.27%, 8.86 × 10<sup>−4</sup> sr<sup>−1</sup>, 0.98, and − 6.53 × 10<sup>−5</sup> sr<sup>−1</sup>, respectively. In the field C <sub>chla</sub> estimation experiment that contained 391 samples (mean C <sub>chla</sub> is 25.42 ± 16.37 μg/L), the BBHR algorithm improved the MAPE and RMSE indices of multispectral data from 0.47 and 12.80 μg/L to 0.42 and 10.16 μg/L, respectively. For the satellite image match-up dataset (66 samples), the BBHR method decreased the MAPE and RMSE indices of multispectral images from 0.51 and 12.94 μg/L to 0.32 and 8.01 μg/L, respectively. The proposed algorithm outperformed the other two high-accuracy models in terms of spectral fidelity and C <sub>chla</sub> estimation. In addition, the BBHR method shows great potential for the multi-source monitoring of inland water bodies. This could improve the accuracy and robustness of the reconstruction when semi-synchronized multi-source data are input and increase the consistency of multi-source data when non-synchronized multi-source data are provided. Our results revealed that BBHR is a trustworthy algorithm that offers hyperspectral R <sub>rs</sub> data and facilitates the remote monitoring of turbid inland waterbodies.", "Keywords": "Bio-optical model ; Sparse representing ; Hyperspectral reconstruction ; Inland optically complex waters ; C <sub>chla</sub> estimation", "DOI": "10.1016/j.rse.2022.113045", "PubYear": 2022, "Volume": "276", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of the Resources and Environmental Sciences, Henan Agricultural University, Zhengzhou 450002, China;Human Engineering Research Center of Land Consolidation and Ecological Restoration, Henan Agricultural University, Zhengzhou 450002, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Geography, Nanjing Normal University, Nanjing 210023, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Geography, Nanjing Normal University, Nanjing 210023, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Jiangsu Collaborative Innovation Center of Regional Modern Agriculture & Environmental Protection, Huaiyin Normal University, Huai'an 223000, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of the Resources and Environmental Sciences, Henan Agricultural University, Zhengzhou 450002, China;Human Engineering Research Center of Land Consolidation and Ecological Restoration, Henan Agricultural University, Zhengzhou 450002, China"}, {"AuthorId": 6, "Name": "Li <PERSON>", "Affiliation": "School of Tourism and Urban & Rural Planning, Zhejiang Gongshang University, Hangzhou 310018, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "College of the Resources and Environmental Sciences, Henan Agricultural University, Zhengzhou 450002, China;Human Engineering Research Center of Land Consolidation and Ecological Restoration, Henan Agricultural University, Zhengzhou 450002, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of the Resources and Environmental Sciences, Henan Agricultural University, Zhengzhou 450002, China;Human Engineering Research Center of Land Consolidation and Ecological Restoration, Henan Agricultural University, Zhengzhou 450002, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "College of the Resources and Environmental Sciences, Henan Agricultural University, Zhengzhou 450002, China;Human Engineering Research Center of Land Consolidation and Ecological Restoration, Henan Agricultural University, Zhengzhou 450002, China"}, {"AuthorId": 10, "Name": "Ji <PERSON>xing", "Affiliation": "College of the Resources and Environmental Sciences, Henan Agricultural University, Zhengzhou 450002, China;Human Engineering Research Center of Land Consolidation and Ecological Restoration, Henan Agricultural University, Zhengzhou 450002, China"}], "References": [{"Title": "Evaluating the potential of CubeSats for remote sensing reflectance retrieval over inland waters", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>raes <PERSON>; <PERSON><PERSON><PERSON><PERSON>o Clemente Faria Bar<PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "7", "Page": "2807", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Seamless retrievals of chlorophyll-a from Sentinel-2 (MSI) and Sentinel-3 (OLCI) in inland and coastal waters: A machine-learning approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111604", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Seamless retrievals of chlorophyll-a from Sentinel-2 (MSI) and Sentinel-3 (OLCI) in inland and coastal waters: A machine-learning approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111604", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Remote estimation of chlorophyll a concentrations over a wide range of optical conditions based on water classification from VIIRS observations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111735", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Eutrophication changes in fifty large lakes on the Yangtze Plain of China derived from MERIS and OLCI observations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "246", "Issue": "", "Page": "111890", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A machine learning approach to estimate chlorophyll-a from Landsat-8 measurements in inland lakes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111974", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Extending satellite ocean color remote sensing to the near-blue ultraviolet bands", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112228", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Estimating ultraviolet reflectance from visible bands in ocean colour remote sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "258", "Issue": "", "Page": "112404", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A three-step semi analytical algorithm (3SAA) for estimating inherent optical properties over oceanic, coastal, and inland waters from remote sensing reflectance", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "263", "Issue": "", "Page": "112537", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 94082315, "Title": "Federated Multi-task Graph Learning", "Abstract": "Distributed processing and analysis of large-scale graph data remain challenging because of the high-level discrepancy among graphs. This study investigates a novel subproblem: the distributed multi-task learning on the graph, which jointly learns multiple analysis tasks from decentralized graphs. We propose a\n federated multi-task graph learning (FMTGL) \n framework to solve the problem within a privacy-preserving and scalable scheme. Its core is an innovative data-fusion mechanism and a low-latency distributed optimization method. The former captures multi-source data relatedness and generates universal task representation for local task analysis. The latter enables the quick update of our framework with gradients sparsification and tree-based aggregation. As a theoretical result, the proposed optimization method has a convergence rate interpolates between\n \n \\( \\mathcal {O}(1/T) \\) \n \n and\n \n \\( \\mathcal {O}(1/\\sqrt {T}) \\) \n \n , up to logarithmic terms. Unlike previous studies, our work analyzes the convergence behavior with adaptive stepsize selection and non-convex assumption. Experimental results on three graph datasets verify the effectiveness and scalability of FMTGL.", "Keywords": "", "DOI": "10.1145/3527622", "PubYear": 2022, "Volume": "13", "Issue": "5", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of CAD and CG, Hangzhou, China"}, {"AuthorId": 2, "Name": "Dongming Han", "Affiliation": "State Key Laboratory of CAD and CG, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of CAD and CG, Hangzhou, China"}, {"AuthorId": 4, "Name": "Haiyang Zhu", "Affiliation": "State Key Laboratory of CAD and CG, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Artificial Intelligence, Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of CAD and CG, Hangzhou, China"}], "References": []}, {"ArticleId": 94082422, "Title": "Tailored Computing Instruction for Economics Majors", "Abstract": "", "Keywords": "", "DOI": "10.22369/issn.2153-4136/13/1/6", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 35042, "JournalTitle": "The Journal of Computational Science Education", "ISSN": "", "EISSN": "2153-4136", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Zhen<PERSON> He", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94082457, "Title": "Key point localization and recurrent neural network based water meter reading recognition", "Abstract": "Due to the complicated arrangement of the pipes in the narrow space leads to random orientation of the mechanical water meter dial meanwhile its digit wheels are accompanied by arbitrary angle rotation, which makes the detection and recognition of meter reading more difficult. Even the latest visual network technology cannot deal with the challenges. In this paper, two special visual task networks are being closely cooperated to solve above issues. First, a professional water meter detection method is proposed by redesigning and retraining a human joints detection network to accurately locate four key points of reading region. Based on key points the distorted reading region will be geometric corrected by using homography relation to reduce the interference from shooting angle and improve accuracy of subsequent digit recognition. Then, a water meter reading recognition method is proposed by modifying a recurrent block convolutional network. The robustness of digit recognition is improved by block recognition and transcription of reading region features. During transcription stage, we add new recognition markers and probability vectors between each digit in dictionary to solve the issue of digit wheels rotations. Finally, our method achieves more robust water meter detection in harsh environment and higher recognition accuracy. Experimental results showed that our method can get better performance in detection efficiency (6.15 fps) and accuracy (95.30%) compared with recent related works and closer to the level of practical application.", "Keywords": "Mechanical water meters reading ; Reading region detection ; Digit wheels recognition ; Key point location ; Recurrent convolutional network", "DOI": "10.1016/j.displa.2022.102222", "PubYear": 2022, "Volume": "74", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing University of Posts and Telecommunications, Beijing 100876, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, Beijing 100190, China"}], "References": []}, {"ArticleId": 94082532, "Title": "Bilan du prix de thèse <PERSON> 2020: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.129", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " Clé<PERSON><PERSON> Maurice", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94082535, "Title": "Le mot de la SIF: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.15.3", "PubYear": 2020, "Volume": "", "Issue": "15", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94082573, "Title": "Magnetic concentric tube robots: Introduction and analysis", "Abstract": "<p>In this paper, we propose a new type of continuum robot, referred to as a magnetic concentric tube robot (M-CTR), for performing minimally invasive surgery in narrow and difficult-to-access areas. The robot combines concentric tubes and magnetic actuation to benefit from the ‘follow the leader’ behaviour, the dexterity and stability of existing robots, while targeting millimetre-sized external diameters. These three kinematic properties are assessed through numerical and experimental studies performed on a prototype of a M-CTR. They are performed with general forward and inverse kineto-static models of the robot, continuation and bifurcation analysis, and a specific experimental setup. The prototype presents unique capabilities in terms of deployment and active stability management, while its dexterity in terms of tip orientability is also among the best reported for other robots at its scale.</p>", "Keywords": "", "DOI": "10.1177/02783649211071113", "PubYear": 2022, "Volume": "41", "Issue": "4", "JournalId": 943, "JournalTitle": "The International Journal of Robotics Research", "ISSN": "0278-3649", "EISSN": "1741-3176", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cube, University of Strasbourg, CNRS, INSA Strasbourg, France;FEMTO-ST Institute/AS2M, Univ. Bourgogne Franche-Comte/CNRS, Besançon, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ETH Zurich/Multi-Scale Robotics Lab, Zürich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "FEMTO-ST Institute/AS2M, Univ. Bourgogne Franche-Comte/CNRS, Besançon, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "FEMTO-ST Institute/AS2M, Univ. Bourgogne Franche-Comte/CNRS, Besançon, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "ETH Zurich/Multi-Scale Robotics Lab, Zürich, Switzerland"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "FEMTO-ST Institute/AS2M, Univ. Bourgogne Franche-Comte/CNRS, Besançon, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FEMTO-ST Institute/AS2M, Univ. Bourgogne Franche-Comte/CNRS, Besançon, France"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Cube, University of Strasbourg, CNRS, INSA Strasbourg, France"}], "References": [{"Title": "Tendon-driven continuum robots with extensible sections—A model-based evaluation of path-following motions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>-<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "1", "Page": "7", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 94082860, "Title": "A hybrid algorithm based on modified chemical reaction optimization and best-first search algorithm for solving minimum vertex cover problem", "Abstract": "<p>The minimum vertex cover problem (MVCP) is one of the well-known NP-complete problems that can be used to formulate numerous real-life applications. The MVCP has been solved using different approaches, exact, heuristic, and metaheuristic. Chemical reaction optimization (CRO) is a population-based metaheuristic algorithm that simulates what happens in chemical reactions to solve many problems. In this paper, the MVCP is solved using hybridization of a modified version of the CRO algorithm and the best-first search (BFS) algorithm. This hybridization is symbolized by MCROA-BFS. The BFS algorithm is exploited to generate initial populations with high-quality initial solutions in comparison with the random bit-vector (RBV) approach as a traditional approach for generating initial populations for several population-based metaheuristic algorithms. At first, the MCROA is evaluated analytically in terms of run time complexity. Then the performance of the MCROA is evaluated experimentally to show the effectiveness of exploiting the BFS in terms of quality of gained solutions and run time. The most valuable player algorithm (MVPA), genetic algorithm (GA), and hybrid CRO algorithm (HCROA) are metaheuristic algorithms that used the RBV approach to generate the initial population. MCROA-BFS is compared, under the selected performance metrics, with these metaheuristic algorithms in addition to MCROA with the RBV approach (MCROA-RBV). The conducted experiments were carried out using 18 instances of DIMACS and BHOSLIB benchmarks. The experimental results revealed the superiority of MCROA-BFS over MVPA, GA, and MCROA-RBV in terms of both performance metrics. Although HCROA has the smallest run time, it failed to obtain high-quality solutions in comparison with MCROA.</p>", "Keywords": "Minimum vertex cover problem; Optimization problem; Metaheuristic algorithm; Chemical reaction optimization; Genetic algorithm; Most valuable player algorithm", "DOI": "10.1007/s00521-022-07262-w", "PubYear": 2022, "Volume": "34", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Autonomous Systems Department, Faculty of Artificial Intelligence, Al-Balqa Applied University, Al-Balqa, Jordan"}, {"AuthorId": 2, "Name": "Basel A. Ma<PERSON>h", "Affiliation": "Computer Science Department, King <PERSON> School of Information Technology, The University of Jordan, Amman, Jordan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Science Department, King <PERSON> School of Information Technology, The University of Jordan, Amman, Jordan"}], "References": [{"Title": "Transportation scheduling optimization by a collaborative strategy in supply chain management with TPL using chemical reaction optimization", "Authors": "M<PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "3649", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Optimization of protein folding using chemical reaction optimization in HP cubic lattice model", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "3117", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Parallel quicksort algorithm on OTIS hyper hexa-cell optoelectronic architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Basel A<PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "61", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Multithreaded scheduling for program segments based on chemical reaction optimizer", "Authors": "Basel <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "2741", "JournalTitle": "Soft Computing"}, {"Title": "Shrink: An Efficient Construction Algorithm for Minimum Vertex Cover Problem", "Authors": "", "PubYear": 2021, "Volume": "10", "Issue": "2", "Page": "255", "JournalTitle": "Information Sciences Letters"}, {"Title": "RETRACTED ARTICLE: Hybrid harmony search algorithm for social network contact tracing of COVID-19", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Basel <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "6", "Page": "3343", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 94082877, "Title": "Speeding up wheel factoring method", "Abstract": "<p>The security of many public key cryptosystems that are used today depends on the difficulty of factoring an integer into its prime factors. Although there is a polynomial time quantum-based algorithm for integer factorization, there is no polynomial time algorithm on a classical computer. In this paper, we study how to improve the wheel factoring method using two approaches. The first approach is introducing two sequential modifications on the wheel factoring method. The second approach is parallelizing the modified algorithms on a parallel system. The experimental studies on composite integers n that are a product of two primes of equal size show the following results. (1) The percentages of improvements for the two modified sequential methods compared to the wheel factoring method are almost (47%) and (90%) . (2) The percentage of improvement for the two proposed parallel methods compared to the two modified sequential algorithms is (90%) on the average. (3) The maximum speedup achieved by the best parallel proposed algorithm using 24 threads is almost 336 times the wheel factoring method.</p>", "Keywords": "Integer factoring; Wheel factorization; Parallel algorithm; RSA; Multicore", "DOI": "10.1007/s11227-022-04470-y", "PubYear": 2022, "Volume": "78", "Issue": "14", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> M<PERSON>", "Affiliation": "Information and Computer Science Department, College of Computer Science and Engineering, University of Ha’il, Hail, Kingdom of Saudi Arabia; Computer Science Division, Mathematics Department, Faculty of Science, Ain Shams University, Cairo, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Computer Science Division, Mathematics Department, Faculty of Science, Ain Shams University, Cairo, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Information and Computer Science Department, College of Computer Science and Engineering, University of Ha’il, Hail, Kingdom of Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Information and Computer Science Department, College of Computer Science and Engineering, University of Ha’il, Hail, Kingdom of Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, College of Computer Science and Engineering, University of Ha’il, Hail, Kingdom of Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Division, Mathematics Department, Faculty of Science, Ain Shams University, Cairo, Egypt"}], "References": [{"Title": "A Unified Method for Private Exponent Attacks on RSA Using Lattices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "207", "JournalTitle": "International Journal of Foundations of Computer Science"}, {"Title": "Fermat Factorization using a Multi-Core System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "323", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}]}, {"ArticleId": 94082888, "Title": "Dual relation network for temporal action localization", "Abstract": "Temporal action localization is a challenging task for video understanding. Most previous methods process each proposal independently and neglect the reasoning of proposal-proposal and proposal-context relations. We argue that the supplementary information obtained by exploiting these relations can enhance the proposal representation and further boost the action localization. To this end, we propose a dual relation network to model both proposal-proposal and proposal-context relations. Concretely, a proposal-proposal relation module is leveraged to learn discriminative supplementary information from relevant proposals, which allows the network to model their interaction based on appearance and geometric similarities. Meanwhile, a proposal-context relation module is employed to mine contextual clues by adaptively learning from the global context outside of region-based proposals. They effectively leverage the inherent correlation between actions and the long-term dependency with videos for high-quality proposal refinement. As a result, the proposed framework enables the model to distinguish similar action instances and locate temporal boundaries more precisely. Extensive experiments on the THUMOS14 dataset and ActivityNet v1.3 dataset demonstrate that the proposed method significantly outperforms recent state-of-the-art methods.", "Keywords": "Temporal action localization ; Relation reasoning", "DOI": "10.1016/j.patcog.2022.108725", "PubYear": 2022, "Volume": "129", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Robotics, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Artificial Intelligence and Robotics, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Robotics, Xi’an Jiaotong University, Xi’an, Shaanxi 710049, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Wormpex AI Research, Bellevue, WA 98004, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Illinois, Chicago, IL 60607, USA"}], "References": [{"Title": "Learning motion representation for real-time spatio-temporal action localization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107312", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep snippet selective network for weakly supervised temporal action localization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107686", "JournalTitle": "Pattern Recognition"}, {"Title": "Temporal filtering networks for online action detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Jongyoul Park", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107695", "JournalTitle": "Pattern Recognition"}, {"Title": "Weakly-supervised action localization via embedding-modeling iterative optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107831", "JournalTitle": "Pattern Recognition"}, {"Title": "Temporally smooth online action detection using cycle-consistent future anticipation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107954", "JournalTitle": "Pattern Recognition"}, {"Title": "Multiple knowledge representation for big data artificial intelligence: framework, applications, and case studies", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "12", "Page": "1551", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": 94083116, "Title": "Multi-strategy ensemble binary hunger games search for feature selection", "Abstract": "Feature selection is a crucial preprocessing step in the sphere of machine learning and data mining, devoted to reducing the data dimensionality to improve the performance of learning models. In this paper, a vigorous metaheuristic named hunger games search (HGS) is integrated with a multi-strategy (MS) framework, including chaos theory, greedy selection, and vertical crossover, to boost search equilibrium between explorative and exploitative cores. The new MS-HGS algorithm is developed for global optimization, and its binary variant MS-bHGS is applied to the feature selection problem particularly. To evaluate and validate the performance of our proposed approach, on the one hand, MS-HGS is compared with HGS alongside single strategy embedded HGS on 23 benchmark functions and compared with seven state-of-the-art algorithms on IEEE CEC 2017 test suite. On the other hand, MS-bHGS is employed for feature selection in 20 datasets from the UCI repository and compared with three groups of methods, i.e., traditional, recent, and enhanced, respectively. The relevant experimental results confirm that MS-bHGS exceeds bHGS and most existing techniques in terms of classification accuracy, the number of selected features, fitness values, and execution time. Overall, this paper’s findings suggest that MS-HGS is a superior optimizer, and MS-bHGS can be considered a valuable wrapper-mode feature selection technique.", "Keywords": "Feature selection ; Hunger games search ; Multi-strategy ; Chaos theory ; Greedy selection ; Vertical crossover", "DOI": "10.1016/j.knosys.2022.108787", "PubYear": 2022, "Volume": "248", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Surveying and Geospatial Engineering, University of Tehran, Tehran, Iran"}], "References": [{"Title": "An effective distance based feature selection approach for imbalanced data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "3", "Page": "717", "JournalTitle": "Applied Intelligence"}, {"Title": "Manta ray foraging optimization: An effective bio-inspired optimizer for engineering applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103300", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Improved Salp Swarm Algorithm based on opposition based learning and novel local search algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113122", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A Comprehensive Analysis of Nature-Inspired Meta-Heuristic Techniques for Feature Selection Problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1103", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Slime mould algorithm: A new method for stochastic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "300", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Joint imbalanced classification and feature selection for hospital readmissions", "Authors": "<PERSON><PERSON> Du; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "106020", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An improved Dragonfly Algorithm for feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106131", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Exploring the financial indicators to improve the pattern recognition of economic data based on machine learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "723", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Mutual information based feature subset selection in multivariate time series classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107525", "JournalTitle": "Pattern Recognition"}, {"Title": "Dynamic Salp swarm algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113873", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Alzheimer’s disease multiclass diagnosis via multimodal neuroimaging embedding feature selection and fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "170", "JournalTitle": "Information Fusion"}, {"Title": "Ant colony optimization with horizontal and vertical crossover search: Fundamental visions for multi-threshold image segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114122", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Chaotic random spare ant colony optimization for multi-threshold image segmentation of 2D Kapur entropy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106510", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Hyper Learning Binary Dragonfly Algorithm for Feature Selection: A COVID-19 Case Study", "Authors": "<PERSON>wei Too; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106553", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "BEPO: A novel binary emperor penguin optimizer for automatic feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106560", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Orthogonal learning covariance matrix for defects of grey wolf optimizer: Insights, balance, diversity, and feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106684", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "S-shaped versus V-shaped transfer functions for binary Manta ray foraging optimization in feature selection problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "17", "Page": "11027", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Review of swarm intelligence-based feature selection methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104210", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A novel multi population based particle swarm optimization for feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "219", "Issue": "", "Page": "106894", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hunger games search: Visions, conception, implementation, deep analysis, perspectives, and towards performance shifts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114864", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel gaussian based particle swarm optimization gravitational search algorithm for feature selection and classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "19", "Page": "12301", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A multi-measure feature selection algorithm for efficacious intrusion detection", "Authors": "<PERSON><PERSON>-Semenets; <PERSON><PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107264", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "AIEOU: Automata-based improved equilibrium optimizer with U-shaped transfer function for feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107283", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Particle distance rank feature selection by particle swarm optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115620", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94083174, "Title": "LABVIEW FOR MOTION DETECTION USING WEBCAM", "Abstract": "", "Keywords": "", "DOI": "10.26634/jip.8.3.18235", "PubYear": 2021, "Volume": "8", "Issue": "3", "JournalId": 40088, "JournalTitle": "i-manager’s Journal on Image Processing", "ISSN": "2349-4530", "EISSN": "2349-6827", "Authors": [{"AuthorId": 1, "Name": "THOTAKURA SUSHMA", "Affiliation": "Department of Electronics and Communication Engineering, Prasad <PERSON>a Institute of Technology, Vijayawada,\r\nAndhra Pradesh, India."}, {"AuthorId": 2, "Name": "HARSHITHAKANNEGANTI BABY", "Affiliation": "Department of Electronics and Communication Engineering, Prasad <PERSON>a Institute of Technology, Vijayawada,\r\nAndhra Pradesh, India."}], "References": []}, {"ArticleId": 94083197, "Title": "Community animation in participatory cultural projects: Lessons from the Wiki Loves Monuments photography contests", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.2124", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083240, "Title": "Machine learning regression to boost scheduling performance in hyper-scale cloud-computing data centres", "Abstract": "Data centres increase their size and complexity due to the increasing amount of heterogeneous workloads and patterns to be served. Such a mix of various purpose workloads makes the optimisation of resource management systems according to temporal or application-level patterns difficult. Data-centre operators have developed multiple resource-management models to improve scheduling performance in controlled scenarios. However, the constant evolution of the workloads makes the utilisation of only one resource-management model sub-optimal in some scenarios. In this work, we propose: (a) a machine learning regression model based on gradient boosting to predict the time a resource manager needs to schedule incoming jobs for a given period; and (b) a resource management model, Boost, that takes advantage of this regression model to predict the scheduling time of a catalogue of resource managers so that the most performant can be used for a time span. The benefits of the proposed resource-management model are analysed by comparing its scheduling performance KPIs to those provided by the two most popular resource-management models: two-level, used by Apache Mesos, and shared-state, employed by Google Borg. Such gains are empirically evaluated by simulating a hyper-scale data centre that executes a realistic synthetically generated workload that follows real-world trace patterns.", "Keywords": "Data centre ; Cloud computing ; Scheduling optimisation ; Machine learning ; Gradient boosting", "DOI": "10.1016/j.jksuci.2022.04.008", "PubYear": 2022, "Volume": "34", "Issue": "6", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Languages and Systems, University of Seville, Avda. Reina Mercedes s/n., 41012 Seville, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Languages and Systems, University of Seville, Avda. Reina Mercedes s/n., 41012 Seville, Spain"}, {"AuthorId": 3, "Name": "Agnieszka Jakóbik", "Affiliation": "Department of Computer Science, Cracow University of Technology, Cracow, Poland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Languages and Systems, University of Seville, Avda. Reina Mercedes s/n., 41012 Seville, Spain"}], "References": [{"Title": "Clustering Cloud Workloads: K-Means vs Gaussian Mixture Model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "158", "JournalTitle": "Procedia Computer Science"}, {"Title": "DISCERNER: Dynamic selection of resource manager in hyper-scale cloud-computing data centres", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Agnieszka <PERSON>akóbik", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "190", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Cost-aware automatic scaling and workload-aware replica management for edge-cloud environment", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "180", "Issue": "", "Page": "103017", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 94083300, "Title": "The perils of machine learning in designing new chemicals and materials", "Abstract": "", "Keywords": "Biomaterials;Data publication and archiving", "DOI": "10.1038/s42256-022-00481-9", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SLAC National Accelerator Laboratory, Menlo Park, USA;Department of Materials Science and Engineering, Stanford University, Stanford, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, Stanford University, Stanford, USA"}], "References": [{"Title": "Dual use of artificial-intelligence-powered drug discovery", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "3", "Page": "189", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 94083367, "Title": "Double information preserving canonical correlation analysis", "Abstract": "The methods based on canonical correlation analysis (CCA-based methods) are typical and effective methods for unsupervised dimensionality reduction of multi-view data. However, the traditional CCA-based methods ignore the dissimilarity information while considering the similarity information of samples, which will make the heterogeneous samples in the subspace cannot be well separated. In this paper, we propose a novel unsupervised multi-view dimensionality reduction method: Double Information Preserving Canonical Correlation Analysis (DIPCCA). DIPCCA aims at finding two projection matrices by integrating two cross double weight graphs with CCA to explore the similarity and dissimilarity information of data in cross views. Furthermore, on the basis of consistency and complementarity, DIPCCA uses the similarity information to maintain the local structure, and the dissimilarity information to disperse embedded samples of distinct clusters, so as to extract more discriminative features. Moreover, CCA and a new locality-preserving CCA are two special cases of DIPCCA when parameters take special values. In order to better extract the features of nonlinear data and more than two views, DIPCCA is extended to Double Information Preserving Kernel Canonical Correlation Analysis and Double Information Preserving Multiple Canonical Correlation Analysis, respectively. Experiments on an artificial dataset and three real multi-view datasets show our proposed methods have better performance than the traditional CCA-based methods.", "Keywords": "Unsupervised learning ; Multi-view learning ; Dimensionality reduction ; Similarity and dissimilarity information", "DOI": "10.1016/j.engappai.2022.104870", "PubYear": 2022, "Volume": "112", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing 100083, PR China;Beijing Engineering and Technology Research Center for Internet of Things in Agriculture, Beijing 100083, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science, China Agricultural University, Beijing 100083, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing 100083, PR China;National Innovation Center for Digital Fishery, China Agricultural University, Beijing 100083, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijing 100083, PR China;National Innovation Center for Digital Fishery, China Agricultural University, Beijing 100083, PR China;Key Laboratory of Smart Farming Technologies for Aquatic Animal and Livestock, Ministry of Agriculture and Rural Affairs, Beijing 100083, PR China;Beijing Engineering and Technology Research Center for Internet of Things in Agriculture, Beijing 100083, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Science, China Agricultural University, Beijing 100083, PR China;Key Laboratory of Smart Farming Technologies for Aquatic Animal and Livestock, Ministry of Agriculture and Rural Affairs, Beijing 100083, PR China;Corresponding author at: College of Science, China Agricultural University, Beijing 100083, PR China"}], "References": [{"Title": "Multi-view predictive latent space learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "56", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Multi-view Locality Low-rank Embedding for Dimension Reduction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105172", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Unsupervised double weight graphs based discriminant analysis for dimensionality reduction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "6", "Page": "2209", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Collaborative weighted multi-view feature extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103527", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Design of multi-view graph embedding using multiple kernel learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103534", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Cross-regression for multi-view feature extraction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "105997", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-view Low-rank Preserving Embedding: A novel method for multi-view representation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "104140", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Multi-view discriminant analysis with sample diversity for ECG biometric recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "110", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Locality cross-view regression for feature extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "104414", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 94083369, "Title": "Leveraging Northeast Cyberteam Successes to Build the CAREERS Cyberteam Program: Initial Lessons Learned", "Abstract": "", "Keywords": "", "DOI": "10.22369/issn.2153-4136/13/1/7", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 35042, "JournalTitle": "The Journal of Computational Science Education", "ISSN": "", "EISSN": "2153-4136", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 17, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 18, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 19, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083402, "Title": "Case-based tuning of a metaheuristic algorithm exploiting sensitivity analysis and design of experiments for reverse engineering applications", "Abstract": "<p>Due to its capacity to evolve in a large solution space, the Simulated Annealing (SA) algorithm has shown very promising results for the Reverse Engineering of editable CAD geometries including parametric 2D sketches, 3D CAD parts and assemblies. However, parameter setting is a key factor for its performance, but it is also awkward work. This paper addresses the way a SA-based Reverse Engineering technique can be enhanced by identifying its optimal default setting parameters for the fitting of CAD geometries to point clouds of digitized parts. The method integrates a sensitivity analysis to characterize the impact of the variations in the parameters of a CAD model on the evolution of the deviation between the CAD model itself and the point cloud to be fitted. The principles underpinning the adopted fitting algorithm are briefly recalled. A framework that uses design of experiments (DOEs) is introduced to identify and save in a database the best setting parameter values for given CAD models. This database is then exploited when considering the fitting of a new CAD model. Using similarity assessment, it is then possible to reuse the best setting parameter values of the most similar CAD model found in the database. The applied sensitivity analysis is described together with the comparison of the resulting sensitivity evolution curves with the changes in the CAD model parameters imposed by the SA algorithm. Possible improvements suggested by the analysis are implemented to enhance the efficiency of SA-based fitting. The overall approach is illustrated on the fitting of single mechanical parts but it can be directly extended to the fitting of parts’ assemblies. It is particularly interesting in the context of the Industry 4.0 to update and maintain the coherence of the digital twins with respect to the evolution of the associated physical products and systems.</p>", "Keywords": "Sensitivity analysis; CAD model parameters; Simulated annealing; Reverse engineering; Digital twins", "DOI": "10.1007/s00366-022-01650-5", "PubYear": 2023, "Volume": "39", "Issue": "4", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LISPEN, EA 7515, HeSam, Arts et Métiers, Aix-en-Provence, France; IMATI, Consiglio Nazionale delle Ricerche, Genoa, Italy; DIME-Dipartimento di Ingegneria meccanica, energetica, gestionale e dei trasporti, Universitá degli Studi di Genova, Genoa, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LISPEN, EA 7515, He<PERSON>am, Arts et Métiers, Aix-en-Provence, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LISPEN, EA 7515, He<PERSON>am, Arts et Métiers, Aix-en-Provence, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IMATI, Consiglio Nazionale delle Ricerche, Genoa, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "IMATI, Consiglio Nazionale delle Ricerche, Genoa, Italy"}], "References": [{"Title": "Reverse engineering by CAD template fitting: study of a fast and robust template-fitting strategy", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "2803", "JournalTitle": "Engineering with Computers"}, {"Title": "Simulated annealing-based fitting of CAD models to point clouds of mechanical parts’ assemblies", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "2891", "JournalTitle": "Engineering with Computers"}, {"Title": "User-Driven Computer-Assisted Reverse Engineering of Editable CAD Assembly Models", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "2", "Page": "", "JournalTitle": "Journal of Computing and Information Science in Engineering"}]}, {"ArticleId": 94083407, "Title": "Integrated framework for estimating remaining useful lifetime through a deep neural network", "Abstract": "This paper proposes an integrated framework for a deep neural network to estimate the remaining useful life (RUL) to ensure the reliability and safety of complex mechanical systems and enable proactive maintenance for intelligent operation. This data-driven method can predict complex and highly nonlinear degradation characteristics that are difficult to predict using physics-based prognostics and health management. In particular, this study focused on feature preprocessing and hyperparameter optimization, whereas previous studies had focused on the neural network architecture to improve prediction accuracy and robustness. The proposed integrated framework comprises four phases: feature preprocessing, feature reasoning using a deep neural network, hyperparameter optimization using a genetic algorithm, and RUL estimation. In the first phase, sensor measurements sensitive to degradation are selected and separated into primary and dynamic degradation trends. In addition, step differential values are extracted to account for multiple operational modes using an unsupervised clustering method. In the second phase, feature reasoning is performed using a deep neural network to characterize hidden complex and highly nonlinear degradation features. The health indicators manipulated in the first phase are trained using the proposed deep neural network. In the third phase, a genetic algorithm is introduced to optimize the hyperparameters used in feature preprocessing and reasoning. The final phase estimates the RUL using the proposed deep neural network with optimized hyperparameters. The proposed method was validated on the C-MAPSS dataset. The results show that the proposed integrated framework outperformed other state-of-the-art machine learning and deep learning methods under different operational conditions, suggesting that efficient feature preprocessing and hyperparameter optimization significantly improve the prediction accuracy and robustness of RUL for data-driven prognostics and health management.", "Keywords": "Deep neural network ; Genetic algorithm ; Moving-time window ; Feature extraction ; Feature reasoning ; Remaining useful life ; Step differential method", "DOI": "10.1016/j.asoc.2022.108879", "PubYear": 2022, "Volume": "122", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Convergence Engineering, Hanyang University, 222, Wangsimni-ro, Seondong-gu, Seoul, 04763, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Convergence Engineering, Hanyang University, 222, Wangsimni-ro, Seondong-gu, Seoul, 04763, Republic of Korea;Corresponding author"}], "References": []}, {"ArticleId": 94083443, "Title": "Quels imaginaires pour les outils numériques participatifs au musée ?", "Abstract": "", "Keywords": "", "DOI": "10.4000/hybrid.1569", "PubYear": 2022, "Volume": "", "Issue": "8", "JournalId": 95156, "JournalTitle": "Hybrid", "ISSN": "", "EISSN": "2276-3538", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083457, "Title": "Le Chapitre français d’Eurographics et ses Fellows: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.167", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": " <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": " <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": " <PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": " <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083471, "Title": "MMI: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.15.102", "PubYear": 2020, "Volume": "", "Issue": "15", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " Collectif", "Affiliation": ""}], "References": []}, {"ArticleId": 94083474, "Title": "Emergency preventing system at production technological objects based on situational mathematical model", "Abstract": "<p>The main criterion characterizing the efficiency of the equipment of an industrial facility is its operation without failures and damages. To reduce the emergency rate, there has been proposed an emergency prevention system targeted at identifying the technological situation at the facility, predicting its further development, and also de-veloping the control actions to return the facility to normal operation. The task of identifying pre-emergency and emergency situations for technological objects is realized as a particular task of recognizing a technological situation based on a situational mathematical model of an object. The situational mathematical model of the object is implemented as a set of production rules that reflect the knowledge of experts about the identification of pre-emergency situations at the object, as well as possible trajectories for bringing the control object into normal operation. A three-level grouping of the rules of the situational mathematical model by security levels is proposed. The features of the formation of the rules of the situational mathematical model in the processing of qualitative information about the object are considered. Based on the situational mathematical model, a method for identifying pre-emergency situations is proposed, the stages of the method implementation are described. An algorithm of functioning the emergency prevention system has been developed. The interaction scheme of the proposed emergency prevention system and the process control system based on the SCADA system is determined. The use of the proposed system will improve the safety and stability of the operation of hazardous technological facilities</p>", "Keywords": "", "DOI": "10.24143/2072-9502-2022-2-22-32", "PubYear": 2022, "Volume": "2022", "Issue": "2", "JournalId": 47501, "JournalTitle": "VESTNIK OF ASTRAKHAN STATE TECHNICAL UNIVERSITY. SERIES: MANAGEMENT, COMPUTER SCIENCE AND INFORMATICS", "ISSN": "2072-9502", "EISSN": "2224-9761", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Astrakhan State Technical University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Astrakhan State Technical University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Astrakhan State Technical  University"}], "References": [{"Title": "Decision support for managing pre-emergency situations in case ofcatalytic reforming unit", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "2", "Page": "19", "JournalTitle": "VESTNIK OF ASTRAKHAN STATE TECHNICAL UNIVERSITY. SERIES: MANAGEMENT, COMPUTER SCIENCE AND INFORMATICS"}]}, {"ArticleId": 94083491, "Title": "Les pièces magnétiques: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.151", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083494, "Title": "Visibilité des femmes scientifiques dans les médias, discours d’inauguration de la journée Sciences et médias: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.117", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083524, "Title": "Urdu Named Entity Recognition System Using Deep Learning Approaches", "Abstract": "<p>Named entity recognition (NER) is a fundamental part of other natural language processing tasks such as information retrieval, question answering systems and machine translation. Progress and success have already been achieved in research on the English NER systems. However, the Urdu NER system is still in its infancy due to the complexity and morphological richness of the Urdu language. Existing Urdu NER systems are highly dependent on manual feature engineering and word embedding to capture similarity. Their performance lags if the words are previously unknown or infrequent. The feature-based models suffer from complicated feature engineering and are often highly reliant on external resources. To overcome these limitations in this study, we present several deep neural approaches that automatically learn features from the data and eliminate manual feature engineering. Our extension involved convolutional neural network to extract character-level features and combine them with word embedding to handle out-of-vocabulary words. The study also presents a tweets dataset in Urdu, annotated manually for five named entity classes. The effectiveness of the deep learning approaches is demonstrated on four benchmarks datasets. The proposed method demonstrates notable progress upon current state-of-the-art NER approaches in Urdu. The results show an improvement of 6.26% in the F1 score.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxac047", "PubYear": 2023, "Volume": "66", "Issue": "8", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, 300350 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, 300350 China;Tianjin University-Aishu Data Intelligence Joint Laboratory, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science University of Science and technology, Bannu, PK 28100"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, 300350 China"}], "References": [{"Title": "ASTRAL: Adversarial Trained LSTM-CNN for Named Entity Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Xingyu Fu", "PubYear": 2020, "Volume": "197", "Issue": "", "Page": "105842", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey on sentiment analysis in Urdu: A resource-poor language", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "1", "Page": "53", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "Sentiment Analysis of Fast Food Companies With Deep Learning Models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "383", "JournalTitle": "The Computer Journal"}, {"Title": "Deep Learning-based Sentiment Analysis of Facebook Data: The Case of Turkish Users", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "3", "Page": "473", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 94083584, "Title": "Bibliometrics Is Valuable Science. Why Do Some Journals Seem to Oppose It?", "Abstract": "", "Keywords": "", "DOI": "10.2478/jdis-2022-0012", "PubYear": 2022, "Volume": "7", "Issue": "3", "JournalId": 38345, "JournalTitle": "Journal of Data and Information Science", "ISSN": "", "EISSN": "2543-683X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Emporia State University , 1 Kellogg Circle , Emporia , Kansas , U.S."}], "References": []}, {"ArticleId": 94083629, "Title": "Building a Computational and Data Science Workforce", "Abstract": "", "Keywords": "", "DOI": "10.22369/issn.2153-4136/13/1/5", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 35042, "JournalTitle": "The Journal of Computational Science Education", "ISSN": "", "EISSN": "2153-4136", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083715, "Title": "A low latency traffic sign detection model with an automatic data labeling pipeline", "Abstract": "<p>As a crucial sub-task for autonomous driving and intelligent transportation, traffic sign detection attracts a lot of researchers’ attention. Although prior works have achieved promising results, it still suffers from two problems, the need of massive labeled data and the slow inference speed on high-resolution images, which are also the common problem of generic object detection. Motivated by these problems, we propose a lightweight traffic sign detection model and employ an automatic data labeling pipeline. The detection model utilizes a cascaded structure that consists of a localization module and a recognition module, achieving quite fast speed on GPU and edge devices. We discard all complex structures and operations to boost the speed of the model, making it easy for deployment. Then, we propose a two-stage automatic data labeling pipeline to reduce the cost of data labeling work. With only traffic sign template images, a synthetic dataset is constructed for generating initial pseudo labels in the first stage. In the second stage, we propose to use an image pretext model to refine the initial labels. The accuracy of the final pseudo labels is nearly 100%. We test the proposed method on TT-100K, GTSDB, and GTSRB datasets, and the results show that the model trained with the pseudo labels only has a negligible accuracy loss compared with the model trained by real labels. The proposed model’s calculation latency is around 1 ms on GPU, and the accuracy is still on par with the state-of-the-art models.</p>", "Keywords": "Traffic sign detection; Image clustering; Real-time detection; Automatic labeling; Intelligent transportation", "DOI": "10.1007/s00521-022-07253-x", "PubYear": 2022, "Volume": "34", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Science and Engineering, Nanjing University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Science and Engineering, Nanjing University, Nanjing, China"}], "References": [{"Title": "Traffic sign detection and recognition based on pyramidal convolutional networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "11", "Page": "6533", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Real-time traffic sign detection and classification towards real traffic scene", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "18201", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Group multi-scale attention pyramid network for traffic sign detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 94083741, "Title": "Efficient Parameter Estimation of Truncated Boolean Product Distributions", "Abstract": "<p>We study the problem of estimating the parameters of a Boolean product distribution in d dimensions, when the samples are truncated by a set \\(S \\subseteq \\{0, 1\\}^d\\) accessible through a membership oracle. This is the first time that the computational and statistical complexity of learning from truncated samples is considered in a discrete setting. We introduce a natural notion of fatness of the truncation set S , under which truncated samples reveal enough information about the true distribution. We show that if the truncation set is sufficiently fat, samples from the true distribution can be generated from truncated samples. A stunning consequence is that virtually any statistical task (e.g., learning in total variation distance, parameter estimation, uniformity or identity testing) that can be performed efficiently for Boolean product distributions, can also be performed from truncated samples, with a small increase in sample complexity. We generalize our approach to ranking distributions over d alternatives, where we show how fatness implies efficient parameter estimation of Mallows models from truncated samples. Exploring the limits of learning discrete models from truncated samples, we identify three natural conditions that are necessary for efficient identifiability: (i) the truncation set S should be rich enough; (ii) S should be accessible through membership queries; and (iii) the truncation by S should leave enough randomness in all directions. By carefully adapting the Stochastic Gradient Descent approach of (<PERSON><PERSON><PERSON><PERSON> et al., FOCS 2018), we show that these conditions are also sufficient for efficient learning of truncated Boolean product distributions.</p>", "Keywords": "Truncated Statistics; Boolean Product Distributions; Ranking Distributions; Stochastic Gradient Descent", "DOI": "10.1007/s00453-022-00961-9", "PubYear": 2022, "Volume": "84", "Issue": "8", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, National Technical University of Athens, Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, National Technical University of Athens, Athens, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, University of Wisconsin-Madison, Madison, USA"}], "References": []}, {"ArticleId": 94083767, "Title": "Data for validation and adjustment of APACHE II score in cardiogenic shock patients treated with a percutaneous left ventricular assist device", "Abstract": "A precise prognosis is of imminent importance in intensive care medicine. This article provides data showing the overestimation of intrahospital mortality by APACHE II score in various subgroups of cardiogenic shock patients treated with a percutaneous left ventricular assist device. The data set includes additional baseline characteristics regarding age, pre-existing diseases, characteristics of coronary artery disease, characteristics of cardiopulmonary resuscitation, and hemodynamic parameter not included in the APACHE II score. Further data were provided which characterize derivation and validation group. Both groups were used for adjustment of the APACHE II approach. The data are supplemental to our original research article titled “Predictive value of the APACHE II score in cardiogenic shock patients treated with a percutaneous left ventricular assist device” (<PERSON><PERSON><PERSON> et al., IJC Heart &amp; Vasculature. 40 (2022) 101013. https://doi.org/10.1016/j.ijcha.2022.101013 ).", "Keywords": "Acute Physiology and Chronic Health Evaluation II score ; Predicted mortality ; Percutaneous left ventricular assist device ; Impella CP® ; Mechanical circulatory support .", "DOI": "10.1016/j.dib.2022.108199", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany;Corresponding author at: Fetscherstraße 76, 01307 Dresden, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Kreiskrankenhaus Freiberg, Klinik für Innere Medizin II, Freiberg, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Klinikum Chemnitz, Klinik für Innere Medizin I, Chemnitz, Germany"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Technische Universität Dresden, Department of Internal Medicine and Cardiology, Herzzentrum Dresden, University Clinic, Dresden, Germany"}], "References": []}, {"ArticleId": 94083819, "Title": "Designing a strong test for measuring true common-sense reasoning", "Abstract": "Common-sense reasoning has recently emerged as an important test for artificial general intelligence, especially given the much-publicized successes of language representation models such as T5, BERT and GPT-3. Currently, typical benchmarks involve question answering tasks, but to test the full complexity of common-sense reasoning, more comprehensive evaluation methods that are grounded in theory should be developed.", "Keywords": "Computational science;Computer science", "DOI": "10.1038/s42256-022-00478-4", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Sciences Institute, University of Southern California, Marina del Rey, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tetherless World Constellation, Rensselaer Polytechnic Institute, Troy, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tetherless World Constellation, Rensselaer Polytechnic Institute, Troy, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Tetherless World Constellation, Rensselaer Polytechnic Institute, Troy, USA"}], "References": []}, {"ArticleId": 94083859, "Title": "Entretien ave<PERSON>, <PERSON><PERSON><PERSON><PERSON>: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.109", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94083864, "Title": "IFIP: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.17.75", "PubYear": 2021, "Volume": "", "Issue": "17", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": " Collectif", "Affiliation": ""}], "References": []}, {"ArticleId": 94083885, "Title": "A REVIEW ON OBJECT DETECTION SINCE 2005", "Abstract": "Recent years have seen a significant increase in interest in object detection, which is considered to be one of the most fundamental and demanding computer vision tasks. It might be called the pinnacle of computer vision history, because of its rapid progress since 2005. If we regard today's object identification to be a kind of deep learning-powered technical aesthetics, then rewinding the clock since 2005 would allow us to observe the wisdom of the cold war period. This study examines studies on object detection since 2005 in the context of technological advancements that have occurred throughout a quarter-century. This article covers a wide range of subjects, including historical milestone detectors, metrics, datasets, speed-up strategies, etc. This article also reviews important traditional object detections (DPM and HOG), single-stage object detection methods (YOLOR, RetinaNet, SSD, YOLO), and two-stage object detection methods (Mask-RCNN, Faster-RCNN, Fast-RCNN, SPPNet, R-CNN).", "Keywords": "Object detection; Localization; Classification; Technical evolution; Deep learning; Computer vision; Convolutional neural networks (CNNs).", "DOI": "10.26483/ijarcs.v13i2.6804", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering National Institute of Technology Jamshedpur, India"}], "References": []}, {"ArticleId": 94084013, "Title": "Optimal area polygonization problems: Exact solutions through geometric duality", "Abstract": "In this paper, we describe exact methods to solve two problems: given a set S of n points in the plane, find a simple polygon whose set of vertices is precisely S and has minimum ( Min-Area ) or maximum ( Max-Area ) area. These problems are strongly related to the Euclidean TSP, whilst the goal here is to min / max -imize the finite area bounded by the cycle. Both problems are known to be NP -complete. Previous works focused on heuristic methods, specially in 2019, where considerable attention was given to them due to a worldwide contest that took place as part of the Computational Geometry Week. Moreover, to the best of our knowledge, there is only one work aimed to solve these problems exactly. Our main contributions include a novel Integer Linear Programming (ILP) formulation for these problems, along with preprocessing and formulation strengthening techniques to improve its performance in practice. We conducted an extensive experimental study to assess the effectiveness of our model and its variants, and to compare our results to the literature. With respect to the latter analysis, we achieved a speedup of approximately 11.46 (2.21) on average for  Min-Area ( Max-Area ), when compared to the best known model.", "Keywords": "Polygonization ; Integer programming ; Computational geometry", "DOI": "10.1016/j.cor.2022.105842", "PubYear": 2022, "Volume": "145", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Campinas, Campinas, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Campinas, Campinas, Brazil"}, {"AuthorId": 3, "Name": "Cid <PERSON><PERSON>", "Affiliation": "University of Campinas, Campinas, Brazil"}], "References": []}, {"ArticleId": 94084023, "Title": "Versatile, full‐spectrum, and swift network sampling for model generation", "Abstract": "Given one task, it is difficult to generate CNN models for many different hardware platforms with extremely diverse computing power for this task. Repeating network pruning or architecture search for each platform is very time-consuming. In this paper, we propose properties that are required for this model generation problem: versatile (fits diverse applications and network structures), full-spectrum (generates models for devices with tiny to gigantic computing power), and swift (total training time for all platforms is short, and generated models have low latency). We show that existing methods do not satisfy these requirements and propose a VFS method (the V/F/S represents Versatile/Full-spectrum/Swift, respectively). VFS uses importance sampling to sample many submodels with versatile structures and with different input image resolutions. We propose new fine-tuning strategies that only need to fine-tune a best candidate submodel for few epochs for each platform. VFS satisfies all three requirements. It generates versatile models with low latency for diverse applications, is suitable for devices with a wide range of computing power differences, and the models which are generated by VFS achieve state-of-the-art accuracy.", "Keywords": "Model generation ; Convolutional neural networks ; Structured pruning ; Model compression", "DOI": "10.1016/j.patcog.2022.108729", "PubYear": 2022, "Volume": "129", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, China;Corresponding author"}], "References": [{"Title": "Compressing the CNN architecture for in-air handwritten Chinese character recognition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "190", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Goal driven network pruning for object recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107468", "JournalTitle": "Pattern Recognition"}, {"Title": "AutoPruner: An end-to-end trainable filter pruning method for efficient deep model inference", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107461", "JournalTitle": "Pattern Recognition"}, {"Title": "Pruning by explaining: A novel criterion for deep neural network pruning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "107899", "JournalTitle": "Pattern Recognition"}, {"Title": "Improving One-Shot NAS with Shrinking-and-Expanding Supernet", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "108025", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep neural network compression through interpretability-based filter pruning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "108056", "JournalTitle": "Pattern Recognition"}, {"Title": "Explainable deep learning for efficient and robust pattern recognition: A survey of recent developments", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108102", "JournalTitle": "Pattern Recognition"}, {"Title": "Differentiable neural architecture learning for efficient neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108448", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 94084085, "Title": "Concise integer linear programming formulation for clique partitioning problems", "Abstract": "A Clique Partitioning Problem (CPP) finds an optimal partition of a given edge-weighted undirected graph, such that the sum of the weights is maximized. This general graph problem has a wide range of real-world applications, including correlation clustering, group technology, community detection, and coalition structure generation. Although a CPP is NP-hard, due to the recent advance of Integer Linear Programming (ILP) solvers, we can solve reasonably large problem instances by formulating a CPP as an ILP instance. The first ILP formulation was introduced by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> ( Mathematical Programming, 45 (1-3), 59–96, 1989). Recently, <PERSON><PERSON><PERSON> et al. (2018) proposed a more concise ILP formulation that can significantly reduce transitivity constraints as compared to previously introduced models. In this paper, we introduce a series of concise ILP formulations that can reduce even more transitivity constraints. We theoretically evaluate the amount of reduction based on a simple model in which edge signs (positive/negative) are chosen independently. We show that the reduction can be up to 50% (dependent of the ratio of negative edges) and experimentally evaluate the amount of reduction and the performance of our proposed formulation using a variety of graph data sets. Experimental evaluations show that the reduction can exceed 50% (where edge signs can be correlated), and our formulation outperforms the existing state-of-the-art formulations both in terms of memory usage and computational time for most problem instances.", "Keywords": "Clique partitioning problem; Integer linear programming; Transitivity constraints", "DOI": "10.1007/s10601-022-09326-z", "PubYear": 2022, "Volume": "27", "Issue": "1-2", "JournalId": 6708, "JournalTitle": "Constraints", "ISSN": "1383-7133", "EISSN": "1572-9354", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kyushu University, Fukuoka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Kyushu University, Fukuoka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Kyushu University, Fukuoka, Japan; National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Kyushu University, Fukuoka, Japan"}], "References": []}, {"ArticleId": 94084165, "Title": "Automated diatom detection in forensic drowning diagnosis using a single shot multibox detector with plump receptive field", "Abstract": "The detected result of diatoms is an important indicator in forensic drowning examination, and most of the current deep learning methods have achieved greater success in detecting diatoms with simple or no backgrounds. However, diatom images captured by the high-definition electron scanning microscopy in modern forensic science contain complex backgrounds and hamper the accurate diatom detection, resulting in the omission detection of the small and marginal diatoms in multi-diatom scenario. In this paper, we proposed a Hybrid-Dilated-Convolution-incorporated Single Shot Multibox Detector (HDC-SSD) to address this problem. By adopting the merit of the plump receptive field of HDC, the proposed algorithm not only improves the detection rate but also enhances the detection ability of the small objects and the marginal objects. The proposed method was validated by using our self-established dataset. Compared with SSD, the HDC-SSD reduces the undetected rate by approximately 48.6% and almost keeps as fast as the SSD. More importantly, compared with some current state-of-the-art methods, the HDC-SSD obtains the highest Recall value at 0.9302.", "Keywords": "Hybrid-Dilated-Convolution ; SSD ; Detection ; Forensic ; Diatom", "DOI": "10.1016/j.asoc.2022.108885", "PubYear": 2022, "Volume": "122", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Guosheng Gu", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information and Computer Engineering, NorthEast Forestry University, Harbin 150040, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou 510006, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Southern Medical University, Shaitai Road South 1023-1063, Baiyun District, Guangzhou 510515, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information and Computer Engineering, NorthEast Forestry University, Harbin 150040, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kunming Medical University, Chunrong Road West 1168, Chenggong District, Kunming 650500, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Guangzhou Forensic Science Institute & Key Laboratory of Forensic Pathology, Ministry of Public Security, Baiyun Avenue 1708, Baiyun District, Guangzhou 510442, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou Forensic Science Institute & Key Laboratory of Forensic Pathology, Ministry of Public Security, Baiyun Avenue 1708, Baiyun District, Guangzhou 510442, China;Corresponding authors"}], "References": [{"Title": "A Survey on Distributed Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 94084168, "Title": "Data of RNA-seq transcriptomes of liver, bone, heart, kidney and blood in klotho mice at a pre-symptomatic state and the effect of a traditional Japanese multi-herbal medicine, j<PERSON><PERSON><PERSON><PERSON>", "Abstract": "We performed RNA-seq analyses of mRNA isolated from five organs, liver, bone, heart, kidney and blood at the pre-symptomatic state of klotho mice with/without administration of a Japanese traditional herbal medicine, juzenta<PERSON>oto (JTT). Data of differentially expressed genes (DEG) with/without JTT was included. Intron retention (IR) is an important regulatory mechanism that affects gene expression and protein functions. We collected data in which retained-introns were accumulated in a particular set of genes of these organs, and showed that among these retained introns in the liver and bone a subset was recovered to the normal state by the medicine. All of the data present changes of molecular events on the levels of metabolites, proteins and gene expressions observed at the pre- symptomatic state of aging in klotho mice with/without JTT. The research article related to this Data in Brief is published in GENE entitled as “Intron retention as a new pre-symptomatic marker of aging and its recovery to the normal state by a traditional Japanese herbal medicine”.", "Keywords": "Aging;Alternative splicing;Intron retention;Japanese herbal medicine;Kampo;Pre-symptomatic state;RNA-seq", "DOI": "10.1016/j.dib.2022.108197", "PubYear": 2022, "Volume": "42", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Pharmacy, Kitasato University, 1-15-1 Kitasato, Minami, Sagamihara, Kanagawa 252- 0373 Japan. ;Foundation for Advancement of International Science (FAIS) , 3-24-16 Kasuga, Tsukuba, Ibaraki 305-0821 Japan. ;Nagahama Institute of Bio-Science and Technology, Nagahama, Japan."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Pharmacy, Kitasato University, 1-15-1 Kitasato, Minami, Sagamihara, Kanagawa 252- 0373 Japan. ;Foundation for Advancement of International Science (FAIS) , 3-24-16 Kasuga, Tsukuba, Ibaraki 305-0821 Japan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Foundation for Advancement of International Science (FAIS) , 3-24-16 Kasuga, Tsukuba, Ibaraki 305-0821 Japan. ;Nagahama Institute of Bio-Science and Technology, Nagahama, Japan."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Pharmacy, Kitasato University, 1-15-1 Kitasato, Minami, Sagamihara, Kanagawa 252- 0373 Japan. ;Foundation for Advancement of International Science (FAIS) , 3-24-16 Kasuga, Tsukuba, Ibaraki 305-0821 Japan."}, {"AuthorId": 5, "Name": "Kenya Matsumura", "Affiliation": "School of Pharmacy, Kitasato University, 1-15-1 Kitasato, Minami, Sagamihara, Kanagawa 252- 0373 Japan."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Foundation for Advancement of International Science (FAIS) , 3-24-16 <PERSON><PERSON><PERSON>, Tsukuba, Ibaraki 305-0821 Japan."}, {"AuthorId": 7, "Name": "Trieu-<PERSON><PERSON>", "Affiliation": "School of Pharmacy, Kitasato University, 1-15-1 Kitasato, Minami, Sagamihara, Kanagawa 252- 0373 Japan. ;Foundation for Advancement of International Science (FAIS) , 3-24-16 Kasuga, Tsukuba, Ibaraki 305-0821 Japan."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Tsumura Kampo Research Laboratories, Tsumura &amp; CO., 3586 Yoshiwara, Ami-machi, Ibaraki 300- 1192 Japan."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsumura Kampo Research Laboratories, Tsumura &amp; CO., 3586 Yoshiwara, Ami-machi, Ibaraki 300- 1192 Japan."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Tsumura Kampo Research Laboratories, Tsumura &amp; CO., 3586 Yoshiwara, Ami-machi, Ibaraki 300- 1192 Japan."}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsumura Kampo Research Laboratories, Tsumura &amp; CO., 3586 Yoshiwara, Ami-machi, Ibaraki 300- 1192 Japan."}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsumura Kampo Research Laboratories, Tsumura &amp; CO., 3586 Yoshiwara, Ami-machi, Ibaraki 300- 1192 Japan."}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tsumura Kampo Research Laboratories, Tsumura &amp; CO., 3586 Yoshiwara, Ami-machi, Ibaraki 300- 1192 Japan."}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsumura Kampo Research Laboratories, Tsumura &amp; CO., 3586 Yoshiwara, Ami-machi, Ibaraki 300- 1192 Japan."}, {"AuthorId": 15, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Foundation for Advancement of International Science (FAIS) , 3-24-16 Kasuga, Tsukuba, Ibaraki 305-0821 Japan. ;Laboratory Animal Resource Center, University of Tsukuba, 1-1-1 Tennodai, Tsukuba, Ibaraki 305- 8575 Japan."}], "References": []}, {"ArticleId": 94084182, "Title": "CSIN-assisted AF dual-hop relaying communication systems over imperfect Nagakami- m fading channels: Exact error performance", "Abstract": "Relaying-based communication techniques are promising for current and future wireless systems. Amplify-and-forward (AF) relaying is more attractive due to the low cost and simplicity in its relay implementation. As compared to other AF schemes, the channel-state-information (CSI)-and-noise (CSIN)-assisted one is optimal and practically recommended. However, up to this moment, the exact symbol error rate (SER) performance of CSIN-assisted AF dual-hop relaying systems has been analytically derived only for Rayleigh fading environments, while for other more generic fading models, including Nakagami- m , it has been computed either approximately or via long-running time computer simulations. Thus, in this paper, we consider a CSIN-assisted AF dual-hop relaying system with independent and non-identical Nakagami- m fading channels and derive a new expression for its exact SER performance under the effect of imperfect channel estimation. The derived expression includes infinite series that can truncate quickly using a few terms, requires short computational time as compare to simulation, shows perfect matching with the simulation results, and offers remarkable accuracy enhancement over its approximate peer derived in literature. In addition, it helps in demonstrating some useful insights into the system’s realistic error performance considering different system and fading severity conditions. For example, results show that the system’s exact SER performance is dominated by the worse fading hop, does not change when the system’s two fading hops exchange their fading conditions, and improves faster with the per symbol average SNR when the fading severity conditions are better.", "Keywords": "Relaying ; Nakagami- m fading ; Imperfect channel estimation ; Error performance", "DOI": "10.1016/j.phycom.2022.101717", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, The University of Jordan, Amman 11942, Jordan"}], "References": [{"Title": "Performance analysis of AF relaying assisted NOMA system with imperfect CSI and SIC", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101197", "JournalTitle": "Physical Communication"}, {"Title": "Outage performance of UAV-assisted AF relaying with hardware impairments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "", "Page": "101334", "JournalTitle": "Physical Communication"}, {"Title": "Performance analysis of dual-hop UAV relaying systems over mixed fluctuating two-ray and Nakagami-m fading channels", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> P<PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "4", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 94084208, "Title": "Interactive smart home technologies for users with visual disabilities: a systematic mapping of the literature", "Abstract": "This paper presents a systematic mapping of the literature concerning interactive technologies for smart homes targeting users with visual disabilities. The analysis stemmed from a search resulting in 265 papers, of which 25 were selected. The results show the main types of interaction modes reported, including voice, gesture, touch, keyboard and ambient sensors. Technological approaches included desktop computers, mobile devices, embedded systems and stand-alone smart devices. The studies showed important features to aggregate different interaction modalities and provide accessible interfaces in mobile and desktop devices to interact with the home. This paper provides valuable insight into implications for the design of smart home technologies for users with visual disabilities and reveals significant research gaps to be investigated in the future, including overcoming barriers with legacy inaccessible utilities and methodologies to enhance user research in the area. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "ambient-assisted living; blind users; literature review; smart homes; visually impaired users", "DOI": "10.1504/IJCAT.2021.122347", "PubYear": 2021, "Volume": "67", "Issue": "4", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Federal University of Lavras, Minas Gerais, Lavras, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Federal University of Lavras, Minas Gerais, Lavras, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Federal University of Lavras, Minas Gerais, Lavras, Brazil"}], "References": []}, {"ArticleId": 94084214, "Title": "A Security Cost Modelling Framework for Cyber-Physical Systems", "Abstract": "<p> Cyber-Physical Systems (CPS) are formed through interconnected components capable of computation, communication, sensing and changing the physical world. The development of these systems poses a significant challenge, since they have to be designed in a way to ensure cyber-security without impacting their performance. This article presents the Security Cost Modelling Framework (SCMF) and shows supported by an experimental study how it can be used to measure, normalise, and aggregate the overall performance of a CPS. Unlike previous studies, our approach uses different metrics to measure the overall performance of a CPS and provides a methodology for normalising the measurement results of different units to a common Cost Unit . Moreover, we show how the Security Costs can be extracted from the overall performance measurements, which allows us to quantify the overhead imposed by performing security-related tasks. Furthermore, we describe the architecture of our experimental testbed and demonstrate the applicability of SCMF in an experimental study. Our results show that measuring the overall performance and extracting the security costs using SCMF can serve as basis to redesign interactions to achieve the same overall goal at less costs. </p>", "Keywords": "", "DOI": "10.1145/3450752", "PubYear": 2022, "Volume": "22", "Issue": "2", "JournalId": 23139, "JournalTitle": "ACM Transactions on Internet Technology", "ISSN": "1533-5399", "EISSN": "1557-6051", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Lancaster University, Lancaster, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Applied Sciences Burgenland, Eisenstadt, AT"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Lancaster University, Lancaster, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Koblenz-Landau, Germany, DE"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Applied Sciences Burgenland, Eisenstadt, AT"}], "References": [{"Title": "Measuring cyber-physical security in industrial control systems via minimum-effort attack strategies", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "", "Page": "102471", "JournalTitle": "Journal of Information Security and Applications"}]}, {"ArticleId": 94084330, "Title": "Voyage au coeur de l'informatique : technologies, usages, enjeux: ", "Abstract": "", "Keywords": "", "DOI": "10.48556/SIF.1024.18.145", "PubYear": 2021, "Volume": "", "Issue": "18", "JournalId": 97371, "JournalTitle": "Bulletin 1024", "ISSN": "2270-1419", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94084345, "Title": "Approaches and algorithms to mitigate cold start problems in recommender systems: a systematic literature review", "Abstract": "<p>Cold Start problems in recommender systems pose various challenges in the adoption and use of recommender systems, especially for new item uptake and new user engagement. This restricts organizations to realize the business value of recommender systems as they have to incur marketing and operations costs to engage new users and promote new items. Owing to this, several studies have been done by recommender systems researchers to address the cold start problems. However, there has been very limited recent research done on collating these approaches and algorithms. To address this gap, the paper conducts a systematic literature review of various strategies and approaches proposed by researchers in the last decade, from January 2010 to December 2021, and synthesizes the same into two categories: data-driven strategies and approach-driven strategies. Furthermore, the approach-driven strategies are categorized into five main clusters based on deep learning, matrix factorization, hybrid approaches, or other novel approaches in collaborative filtering and content-based algorithms. The scope of this study is limited to a systematic literature review and it does not include an experimental study to benchmark and recommend the best approaches and their context of use in cold start scenarios.</p>", "Keywords": "Recommender systems; Cold start problems; New user problem; New item problem; Systematic literature review", "DOI": "10.1007/s10844-022-00698-5", "PubYear": 2022, "Volume": "59", "Issue": "2", "JournalId": 7081, "JournalTitle": "Journal of Intelligent Information Systems", "ISSN": "0925-9902", "EISSN": "1573-7675", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Management Indore, Indore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Management Indore, Indore, India"}], "References": [{"Title": "A hybrid personalized scholarly venue recommender system integrating social network analysis and contextual similarity", "Authors": "Tribi<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "1139", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Joint Personalized Markov Chains with social network embedding for cold-start recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "386", "Issue": "", "Page": "208", "JournalTitle": "Neurocomputing"}, {"Title": "Recommendations from cold starts in big data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "6", "Page": "1323", "JournalTitle": "Computing"}, {"Title": "Resolving data sparsity and cold start problem in collaborative filtering recommender system using Linked Open Data", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113248", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid context-aware approach for e-tourism package recommendation based on asymmetric similarity measurement and sequential pattern mining", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "", "Page": "100978", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Normalization-based Neighborhood Model for Cold Start Problem in Recommendation System", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "3", "Page": "281", "JournalTitle": "The International Arab Journal of Information Technology"}, {"Title": "New technique to alleviate the cold start problem in recommender systems using information from social media and random decision forests", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "536", "Issue": "", "Page": "156", "JournalTitle": "Information Sciences"}, {"Title": "Simple and effective neural-free soft-cluster embeddings for item cold-start recommendations", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "1560", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Approaching the cold-start problem using community detection based alternating least square factorization in recommendation systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "835", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Cold-start Point-of-interest Recommendation through Crowdsourcing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}, {"Title": "A hybrid recommendation system based on profile expansion technique to alleviate cold start problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "2", "Page": "2339", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Meta-User2Vec model for addressing the user and item cold-start problem in recommender systems", "Authors": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "261", "JournalTitle": "User Modeling and User-Adapted Interaction"}, {"Title": "RBPR: A hybrid model for the new user cold start problem in recommender systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106732", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Employing singular value decomposition and similarity criteria for alleviating cold start and sparse data in context-aware recommender systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "2", "Page": "681", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Seamlessly Unifying Attributes and Items: Conversational Recommendation for Cold-start Users", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": 94084366, "Title": "Blockchain-based secure and trusted data sharing scheme for autonomous vehicle underlying 5G", "Abstract": "This paper proposes a blockchain-based secure and reliable data sharing scheme for autonomous vehicles (AVs). It aims to secure data sharing among AVs. We integrate the fifth-generation (5G) communication network with the proposed blockchain-based scheme to enable ultra-low latency, high reliability, and availability. We have incorporated the edge intelligence nodes (EINs) with blockchain for efficient data processing. We have used a random forest classifier model (RFCM) on the road accidents dataset to predict the accident severity, i.e., fatal, serious, and slight. It is based on road type, light condition, and vehicle movement to be shared among other AVs. We have considered various scenarios and performed the predictive analysis that needs to be secured by using blockchain technology. Results of the study demonstrate the accuracy of RFCM as 93%, the precision of 98%, 97%, and 87% for fatal, serious, and slight categories of accident severity, respectively. The results show that the proposed scheme with RFCM yields better accuracy of 93% than other existing classification algorithms such as Decision tree, Naive Bayes, and AdaBoost. The blockchain-based proposed system has been compared with the traditional approach based on parameters such as network latency, scalability, computation time, delay comparison for node validation, data storage latency, and data storage cost. Obtained results show that the proposed scheme enables the data sharing reliable, secure, and efficient for AVs and outperforms the blockchain and non-blockchain systems in terms of security and efficiency.", "Keywords": "Blockchain ; Random forest ; Latency ; Scalability ; Edge intelligence ; 5G", "DOI": "10.1016/j.jisa.2022.103179", "PubYear": 2022, "Volume": "67", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Institute of Technology, Nirma University, Ahmedabad, Gujarat, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Institute of Technology, Nirma University, Ahmedabad, Gujarat, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Institute of Technology, Nirma University, Ahmedabad, Gujarat, India;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Institute of Technology, Nirma University, Ahmedabad, Gujarat, India;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Centre for Inter-Disciplinary Research and Innovation, University of Petroleum and Energy Studies, P.O. Bidholi Via-Prem Nagar, Dehradun, India"}], "References": [{"Title": "Performance analysis and comparison of PoW, PoS and DAG based blockchains", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "480", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Blockchain-based security attack resilience schemes for autonomous vehicles in industry 4.0: A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106717", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 94084386, "Title": "A feast-and-famine pattern of sleep: Do railway staff get the sleep they need?", "Abstract": "The relationship between sleep and health and wellbeing is receiving increasing attention in our society, following decades of research that has demonstrated the impact of insufficient sleep on performance and health. Fatigue has been identified as a factor in 21% of high-risk incidents in the GB rail industry, and insufficient sleep and long periods of being awake are known to be two key contributors to fatigue. This paper presents evidence from a recent survey conducted in the GB rail industry, which enabled the sleep and wakefulness of railway workers to be quantified. There were 7807 responses to the survey, which represents a very large sample. Responses were primarily collected online, though paper copies were made available in some organisations. Respondents reported sleeping less when working nights, more when working days, and the most on days off, which suggests a feast-and-famine pattern of sleep. 41% were getting 6 h of sleep or less when working days, compared to 63% when working nights. Getting 6 h or less of sleep was associated with excessive daytime sleepiness. Although it is accepted that individuals’ sleep requirements vary, the patterns that shift workers reported in this survey suggest that many are not achieving the amount of sleep they need. The findings of the survey presented in this paper highlight that there is work to be done to understand and address the causes of insufficient sleep in railway workers.", "Keywords": "Fatigue risk management ; Sleep ; Shiftwork ; Railway", "DOI": "10.1016/j.apergo.2022.103711", "PubYear": 2022, "Volume": "102", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Rail Safety and Standards Board, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rail Safety and Standards Board, London, UK;Corresponding author"}], "References": []}, {"ArticleId": 94084399, "Title": "Preventing technical debt with the TAP framework for Technical Debt Aware Management", "Abstract": "<b  >Context:</b> Technical Debt (TD) is a metaphor for technical problems that are not visible to users and customers but hinder developers in their work, making future changes more difficult. TD is often incurred due to tight project deadlines and can make future changes more costly or impossible. Project Management usually focuses on customer benefits and pays less attention to their IT systems’ internal quality. TD prevention should be preferred over TD repayment because subsequent refactoring and re-engineering are expensive. <b  >Objective:</b> This paper evaluates a framework focusing on both TD prevention and TD repayment in the context of agile-managed projects. The framework was developed and applied in an IT unit of a publishing house. The unique contribution of this framework is the integration of TD management into project management. <b  >Method:</b> The evaluation was performed as a comparative case study based on ticket statistics and two structured surveys. The surveys were conducted in the observed IT unit using the framework and a comparison unit not using the framework. The first survey targeted team members, the second one IT managers. <b  >Results:</b> The evaluation shows that in this IT unit the TAP framework led to a raised awareness for the incurrence of TD. Decisions to incur TD are intentional, and TD is repaid timelier. Unintentional TD incurred by unconscious decisions is prevented. Furthermore, better communication and better planning of the project pipeline can be observed. <b  >Conclusion:</b> We provide an insight into practitioners’ ways to identify, monitor, prevent and repay TD. The presented framework includes a feasible method for TD prevention despite tight timelines by making TD repayment part of project management.", "Keywords": "", "DOI": "10.1016/j.infsof.2022.106926", "PubYear": 2022, "Volume": "148", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universität Hamburg, Department of Informatics, Vogt-Kölln-Str.30, 22527 Hamburg, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universität Hamburg, Department of Informatics, Vogt-Kölln-Str.30, 22527 Hamburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universität Hamburg, Department of Informatics, Vogt-Kölln-Str.30, 22527 Hamburg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Gruner + Jahr GmbH, Information Technology, Baumwall 11, 22459 Hamburg, Germany"}], "References": [{"Title": "The influence of Technical Debt on software developer morale", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "110586", "JournalTitle": "Journal of Systems and Software"}, {"Title": "CODE reuse in practice: Benefiting or harming technical debt", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "110618", "JournalTitle": "Journal of Systems and Software"}, {"Title": "The practitioners’ point of view on the concept of technical debt and its causes and consequences: a design for a global family of industrial surveys and its first results from Brazil", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "5", "Page": "3216", "JournalTitle": "Empirical Software Engineering"}, {"Title": "Technical debt forecasting: An empirical study on open-source repositories", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "110777", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Interdisciplinary effects of technical debt in companies with mechatronic products — a qualitative study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "110809", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Building and evaluating a theory of architectural technical debt in software-intensive systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "176", "Issue": "", "Page": "110925", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Architectural design decisions that incur technical debt — An industrial case study", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "139", "Issue": "", "Page": "106669", "JournalTitle": "Information and Software Technology"}, {"Title": "Technical debt payment and prevention through the lenses of software architects", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "106692", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 94084417, "Title": "A novel colour image encryption scheme using dynamic DNA coding, chaotic maps, and SHA-2", "Abstract": "<p>In this paper, a novel encryption scheme that combines hyperchaotic maps schemes, SHA-2, and a pixel-shifting based on the Zaslavskii map is proposed. The plain image is first scrambled based on values obtained from a 2-D hyperchaotic map. A cover image is then generated using the combined logistic-tent map. A mask image is then created based on a novel bit indexing scheme based on the SHA-2 value of the cover image. DNA encoding is carried out on both images, using a dynamically chosen logistic-tent map. Then diffusion using the exclusive-OR method was executed. Lastly, multiple diffusion operations are carried out to produce the cipher image. By simulational analysis, this proposed scheme has been determined to yield high security. The proposed method supersedes the previously developed method by the authors.</p>", "Keywords": "Combined logistic-tent map; Hyperchaotic maps; Image encryption; Zaslavskii map; SHA-2 hash", "DOI": "10.1007/s11042-022-13095-5", "PubYear": 2022, "Volume": "81", "Issue": "26", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering, Vellore Institute of Technology (VIT), Vellore, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology, Vellore Institute of Technology (VIT), Vellore, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty, School of Electrical and Electronics Engineering, SASTRA Deemed University, Thanjavur, India"}], "References": [{"Title": "Hopfield attractor-trusted neural network: an attack-resistant image encryption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11477", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Colour image encryption algorithm combining Arnold map, DNA sequence operation, and a Mandelbrot set", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "", "Page": "102428", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Image encryption through RNA approach assisted with neural key sequences", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "12093", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "FPGA based generic RO TRNG architecture for image confusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "13841", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Tri-level scrambling and enhanced diffusion for DICOM image cipher- DNA and chaotic fused approach", "Authors": "<PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "39-40", "Page": "28807", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A new one-dimensional chaotic map and its application in a novel permutation-less image encryption scheme", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "7", "Page": "1757", "JournalTitle": "The Visual Computer"}, {"Title": "A 2D logistic map and Lorenz-<PERSON> chaotic system based RGB image encryption approach", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "3", "Page": "3749", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Neural-assisted image-dependent encryption scheme for medical image cloud storage", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "12", "Page": "6671", "JournalTitle": "Neural Computing and Applications"}, {"Title": "2D Logistic-Adjusted-Chebyshev map for visual color image encryption", "Authors": "Lidong Liu; Donghua Jiang; <PERSON><PERSON><PERSON> Wang", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "102854", "JournalTitle": "Journal of Information Security and Applications"}]}, {"ArticleId": 94084478, "Title": "Meta-heuristic Based Hybrid Service Placement Strategies for Two-Level Fog Computing Architecture", "Abstract": "<p>The smart manufacturing industry (Industry 4.0) uses the Internet of Things (IoT) devices referred to as Industrial IoT (IIoT) to automate the industrial environment. These IIoT devices generate a massive amount of data called big data. Using fog computing architecture for processing this extensive data will reduce the service time and the service cost for the IIoT applications. The primary challenge is to design better service placement strategies to deploy the IIoT service requests on the fog nodes to minimize service costs and ensure the Quality of Service (QoS) of IIoT applications. Hence, the placement of IIoT services on the fog nodes can be considered as NP-hard problem. In this work, the meta-heuristic-based hybrid algorithms, namely: MGAPSO and EGAPSO, are developed by combining the GA & PSO and Elitism-based GA (EGA) & PSO, respectively. Further, carried out experiments on the two-level fog computing framework developed using docker and containers on 1.4 GHz, 64-bit quad-core processor devices. Experimental results demonstrate that the proposed hybrid EGAPSO algorithm minimizes service time, service cost, and energy consumption and ensures the IIoT applications’ QoS compared to other proposed and state-of-the-art service placement strategies considered for the performance evaluation.</p>", "Keywords": "Containers; Docker; Internet of things; Industry 4.0; Multi-objective; Quality of service; Service time", "DOI": "10.1007/s10922-022-09660-w", "PubYear": 2022, "Volume": "30", "Issue": "3", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Information Technology, National Institute of Technnology Karnataka, Surathkal, Mangalore, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, National Institute of Technnology Karnataka, Surathkal, Mangalore, India"}], "References": [{"Title": "Simulating FogDirector Application Management", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "102021", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Resource Management in a Containerized Cloud: Status and Challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "2", "Page": "197", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "How to place your apps in the fog: State of the art and open challenges", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "5", "Page": "719", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Fog to cloud and network coded based architecture: Minimizing data download time for smart mobility", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "102034", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Research on the optimization of IIoT data processing latency", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "290", "JournalTitle": "Computer Communications"}, {"Title": "URMILA: Dynamically trading-off fog and edge resources for performance and mobility-aware IoT services", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Hongyang Sun", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "101710", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Efficient resource management and workload allocation in fog–cloud computing paradigm in IoT using learning classifier systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "217", "JournalTitle": "Computer Communications"}, {"Title": "An Overview of Service Placement Problem in Fog and Edge Computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "An energy harvesting solution for computation offloading in Fog Computing networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "577", "JournalTitle": "Computer Communications"}, {"Title": "Performance and Availability Trade-Offs in Fog–Cloud IoT Environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "IoTSim-Osmosis: A framework for modeling and simulating IoT applications over an edge-cloud continuum", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "101956", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Task scheduling with precedence and placement constraints for resource utilization improvement in multi-user MEC environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "101970", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Adopting elitism-based Genetic Algorithm for minimizing multi-objective problems of IoT service placement in fog computing environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "102972", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Self-organizing Fog Support Services for Responsive Edge Computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "The FORA Fog Computing Platform for Industrial IoT", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "101727", "JournalTitle": "Information Systems"}, {"Title": "Data Processing on Edge and Cloud: A Performability Evaluation and Sensitivity Analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}]}, {"ArticleId": 94084578, "Title": "Perceived Level of Usability as an Evaluation Metric in Adaptive E-learning", "Abstract": "<p>The global spread of COVID-19 has shifted the learning process towards e-learning. In this context, a critical challenge for researchers is to understand and evaluate the effectiveness of e-learning, especially when the learning is adapted to the needs of individual users. In this work we argue that the learner’s perception of the level of usability of a system is a valuable metric that gives an insight into the learners’ engagement and motivation to learn. Little attention has been paid to this metric. In this paper we explore why this is important and valuable. We present a case study which uses the System Usability Scale (SUS) questionnaire to measure the user’s perception of usability as an indirect (proxy) measure of engagement. A between-subject experiment was conducted with 41 learners with dyslexia. The intervention group used the adaptive version of the e-learning system that matched the material to the needs of the learner. The control group used a standard version. At the end, learning gain and SUS scores were assessed. The correlation between learning performance and the perceived level of usability was positive and moderate (0.517, p < 0.05) among participants in the intervention group. However, learning performance and perceived level of usability were unrelated in the control group (− 0.364, p > 0.05). From this, and other work, it appears that using a learner’s assessment of the usability of a system is an effective way to measure their attitude to their learning. It reflects their perception of its suitability to their needs and this, in turn, is likely to affect their engagement and motivation. As such, this provides an effective instrument to judge whether adaptation based on learner needs has been successful.</p>", "Keywords": "E-learning evaluation; Adaptation; Perceived usability; System Usability Scale; Learning gain; Dyslexia", "DOI": "10.1007/s42979-022-01138-5", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Alwajh University College, University of Tabuk, Tabuk, Kingdom of Saudi Arabia;School of Computer Science, University of Birmingham, Birmingham, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Birmingham, Birmingham, UK"}], "References": [{"Title": "Evaluating E-learning systems success: An empirical study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "67", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 94084581, "Title": "Games for Peace and Welfare", "Abstract": "", "Keywords": "", "DOI": "10.1177/10468781221089058", "PubYear": 2022, "Volume": "53", "Issue": "3", "JournalId": 3394, "JournalTitle": "Simulation & Gaming", "ISSN": "1046-8781", "EISSN": "1552-826X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94084691, "Title": "A Circular Economy Model of Economic Growth with Circular and Cumulative Causation and Trade", "Abstract": "<p>The idea of a circular economy , first discussed by <PERSON> in the 1960s and ‘70 s and reintroduced by environmental economists <PERSON> and <PERSON><PERSON> <PERSON> in 1990, is a characterization of how goods and services can be produced and consumed in an ecologically sound and environmentally sustainable manner that meets concerns of overuse of resources, waste management, and climate change, inter alia , through the conscious interlinking of disparate economic activities. The notion of circular and cumulative causation (CCC), through which certain positive and negative effects are promoted and reinforced by positive feedbacks, also is not new. <PERSON> et al. ( 2015 ) have presented a theoretical circular economy model of economic growth that leads them to infer that the maintenance or improvement of environmental quality is incompatible with economic growth. However, <PERSON><PERSON><PERSON> ( 2021b ) has demonstrated how, when sources of CCC are introduced to the model of <PERSON> et al., a different conclusion may be reached; CCC may be harnessed to bring about desirable systems properties, such as those of a circular market economy. The present paper reviews the arguments and findings of the latter two studies and extends the analysis by introducing trade between three national or regional economies in an environmentally polluting resource, other materials, and recycling technologies. Results of numerical simulation exercises suggest that there could be gains from trade in terms of progress by multiple economies in aggregate towards an international or interregional circular growth economy. The paper also suggests how aspects of the <PERSON> model of a circular economy not presently included in theoretical circular economy models of economic growth (closed or open) can be accommodated.</p>", "Keywords": "Circular Economic Growth; Waste Recycling; Circular and Cumulative Causation; Gains from Trade; Connectivity; Resilience", "DOI": "10.1007/s11067-022-09559-8", "PubYear": 2022, "Volume": "22", "Issue": "3", "JournalId": 20615, "JournalTitle": "Networks and Spatial Economics", "ISSN": "1566-113X", "EISSN": "1572-9427", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Cornell University, Ithaca, USA"}], "References": [{"Title": "Vulnerability, Resilience and ‘Systemic Interest’: a Connectivity Approach", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "3", "Page": "691", "JournalTitle": "Networks and Spatial Economics"}]}, {"ArticleId": 94084693, "Title": "The Celerity High-level API: C++20 for Accelerator Clusters", "Abstract": "<p>Providing convenient APIs and notations for data parallelism which remain accessible for programmers while still providing good performance has been a long-term goal of researchers as well as language and library designers. C++20 introduces ranges and views, as well as the composition of operations on them using a concise syntax, but the efficient implementation of these library features is restricted to CPUs. We present the Celerity High-level API, which makes similarly concise mechanisms applicable to GPUs and accelerators, and even distributed memory clusters of GPUs. Crucially, we achieve this very high level of abstraction without a significant negative impact on performance compared to a lower-level implementation, and without introducing any non-standard toolchain components or compilers, by implementing a C++ library infrastructure on top of the Celerity system. This is made possible by two central API design and implementation strategies, which form the core of our contribution. Firstly, gathering as much information as possible at compile-time and using metaprogramming techniques to automatically fuse several distinctly formulated processing steps into a single accelerator kernel invocation. And secondly, leveraging C++20 “Concepts” in order to avoid type erasure, allowing for highly efficient code generation. We have evaluated our approach quantitatively in a comparison to lower-level manual implementations of several benchmarks, demonstrating its low overhead. Additionally, we investigated the individual performance impact of our specific optimizations and design choices, illustrating the advantages afforded by a Concepts-based approach.</p>", "Keywords": "GPU computing; Compile-time expressions; API design; SYCL; C++20", "DOI": "10.1007/s10766-022-00731-8", "PubYear": 2022, "Volume": "50", "Issue": "3-4", "JournalId": 12927, "JournalTitle": "International Journal of Parallel Programming", "ISSN": "0885-7458", "EISSN": "1573-7640", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Distributed and Parallel Systems, University of Innsbruck, Innsbruck, Austria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Distributed and Parallel Systems, University of Innsbruck, Innsbruck, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Distributed and Parallel Systems, University of Innsbruck, Innsbruck, Austria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Distributed and Parallel Systems, University of Innsbruck, Innsbruck, Austria"}], "References": []}, {"ArticleId": 94084760, "Title": "Time Series Prediction Using Deep Learning Methods in Healthcare", "Abstract": "<p>Traditional machine learning methods face unique challenges when applied to healthcare predictive analytics. The high-dimensional nature of healthcare data necessitates labor-intensive and time-consuming processes when selecting an appropriate set of features for each new task. Furthermore, machine learning methods depend heavily on feature engineering to capture the sequential nature of patient data, oftentimes failing to adequately leverage the temporal patterns of medical events and their dependencies. In contrast, recent deep learning (DL) methods have shown promising performance for various healthcare prediction tasks by specifically addressing the high-dimensional and temporal challenges of medical data. DL techniques excel at learning useful representations of medical concepts and patient clinical data as well as their nonlinear interactions from high-dimensional raw or minimally processed healthcare data.</p><p>In this article, we systematically reviewed research works that focused on advancing deep neural networks to leverage patient structured time series data for healthcare prediction tasks. To identify relevant studies, we searched MEDLINE, IEEE, Scopus, and ACM Digital Library for relevant publications through November 4, 2021. Overall, we found that researchers have contributed to deep time series prediction literature in 10 identifiable research streams: DL models, missing value handling, addressing temporal irregularity, patient representation, static data inclusion, attention mechanisms, interpretation, incorporation of medical ontologies, learning strategies, and scalability. This study summarizes research insights from these literature streams, identifies several critical research gaps, and suggests future research opportunities for DL applications using patient time series data.</p>", "Keywords": "", "DOI": "10.1145/3531326", "PubYear": 2023, "Volume": "14", "Issue": "1", "JournalId": 24450, "JournalTitle": "ACM Transactions on Management Information Systems", "ISSN": "2158-656X", "EISSN": "2158-6578", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Utah, Santa Clara University, Santa Clara, CA"}, {"AuthorId": 2, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT"}], "References": []}, {"ArticleId": 94084762, "Title": "DSG-Net: Learning Disentangled Structure and Geometry for 3D Shape Generation", "Abstract": "<p>3D shape generation is a fundamental operation in computer graphics. While significant progress has been made, especially with recent deep generative models, it remains a challenge to synthesize high-quality shapes with rich geometric details and complex structures, in a controllable manner. To tackle this, we introduce DSG-Net, a deep neural network that learns a disentangled structured & geometric mesh representation for 3D shapes, where two key aspects of shapes, geometry and structure, are encoded in a synergistic manner to ensure plausibility of the generated shapes, while also being disentangled as much as possible. This supports a range of novel shape generation applications with disentangled control, such as interpolation of structure (geometry) while keeping geometry (structure) unchanged. To achieve this, we simultaneously learn structure and geometry through variational autoencoders (VAEs) in a hierarchical manner for both, with bijective mappings at each level. In this manner, we effectively encode geometry and structure in separate latent spaces, while ensuring their compatibility: the structure is used to guide the geometry and vice versa. At the leaf level, the part geometry is represented using a conditional part VAE, to encode high-quality geometric details, guided by the structure context as the condition. Our method not only supports controllable generation applications, but also produces high-quality synthesized shapes, outperforming state-of-the-art methods.</p>", "Keywords": "3D shape generation; disentangled representation; structure; geometry; hierarchies", "DOI": "10.1145/3526212", "PubYear": 2023, "Volume": "42", "Issue": "1", "JournalId": 15014, "JournalTitle": "ACM Transactions on Graphics", "ISSN": "0730-0301", "EISSN": "1557-7368", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, CAS and University of Chinese Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Stanford University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cardiff University"}, {"AuthorId": 4, "Name": "Leonidas J. <PERSON>", "Affiliation": "Stanford University"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Computing Technology, CAS and University of Chinese Academy of Sciences"}], "References": [{"Title": "A survey on deep geometry learning: From a representation perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "113", "JournalTitle": "Computational Visual Media"}, {"Title": "Learning Generative Models of 3D Structures", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "643", "JournalTitle": "Computer Graphics Forum"}, {"Title": "MGCN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "3D reconstruction using deep learning: a survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "4", "Page": "389", "JournalTitle": "Communications in Information and Systems"}, {"Title": "Hausdorff point convolution with geometric priors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "11", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 94084768, "Title": "KP solitons from tropical limits", "Abstract": "We study solutions to the <PERSON><PERSON><PERSON><PERSON> equation whose underlying algebraic curves undergo tropical degenerations. Riemann&#x27;s theta function becomes a finite exponential sum that is supported on a Delaunay polytope. We introduce the Hirota variety which parametrizes all tau functions arising from such a sum. We compute tau functions from points on the Sato Grassmannian that represent Riemann-Roch spaces and we present an algorithm that finds a soliton solution from a rational nodal curve.", "Keywords": "KP equation ; Soliton solutions ; Theta function ; Delaunay polytope ; <PERSON> ; Riemann-<PERSON> space", "DOI": "10.1016/j.jsc.2022.04.009", "PubYear": 2023, "Volume": "114", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "MPI-MiS, Leipzig, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "MPI-MiS, Leipzig, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "UC Berkeley, United States of America"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>ur<PERSON>", "Affiliation": "MPI-MiS, Leipzig, Germany;UC Berkeley, United States of America"}], "References": []}]