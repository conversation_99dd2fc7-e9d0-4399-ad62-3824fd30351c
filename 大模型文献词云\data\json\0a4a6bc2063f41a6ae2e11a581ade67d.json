[{"ArticleId": 96255621, "Title": "C‐/Ka‐band circularly polarized shared‐aperture antenna array", "Abstract": "<p>A C-/Ka-band circularly polarized (CP) shared-aperture scanning antenna array has been proposed in this paper. Here, the antenna array working in the C-band (6.48–6.52 GHz) is composed of 4 × 4 cross-dipole antenna elements, and each cross-dipole antenna element is fed by a 1-to-2 power divider with 90° phase difference, so that it can induce left/right-hand CP wave. As for the antenna array working in the Ka-band (27.5–28.5 GHz), it is made up of 16 × 16 CP antenna elements, in which each CP antenna element is an aperture-coupled chamfered patch fed by substrate integrated waveguide (SIW) structure. To realize the proposed scanning array, the C-band antenna array is set within the gap between adjacent antenna elements of the Ka-band antenna array, and the radiation apertures of all the antenna elements are printed on the same plane. Notably, such a layout can yield good isolation performance between the C-band antenna and the Ka-band antenna element. From the measured results, in the C-band, the proposed scanning array can achieve a beam scanning range of 0°–38° with axial ratio (AR) < 3 dB, a port isolation of greater than 45 dB, and a maximum gain of 18.2 dBic. On the other hand, in the Ka-band, the proposed scanning array can yield a beam scanning range of 0°–45° with AR <3 dB, a port isolation of greater than 37 dB, and a maximum gain of 26.2 dB.</p>", "Keywords": "CP antenna;millimeter wave;scanning antenna array;shared-aperture antenna array", "DOI": "10.1002/mmce.23398", "PubYear": 2022, "Volume": "32", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronic Engineering University of Electronic Science and Technology of China (UESTC)  Chengdu China"}, {"AuthorId": 2, "Name": "Yong‐Ling Ban", "Affiliation": "School of Electronic Engineering University of Electronic Science and Technology of China (UESTC)  Chengdu China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Sun", "Affiliation": "School of Electronic Engineering University of Electronic Science and Technology of China (UESTC)  Chengdu China"}, {"AuthorId": 4, "Name": "Chow‐Yen‐Desmond <PERSON>", "Affiliation": "Department of Electrical Engineering Feng Chia University  Taichung Taiwan"}], "References": []}, {"ArticleId": 96255632, "Title": "A Survey of Design and Implementation of Phase Noise Optimization Based on OFDM System", "Abstract": "<p>Orthogonal Frequency Division Multiplexing (OFDM) is a kind of modulation technology with strong resistance to frequency selective fading and high frequency spectrum utilization. It is a kind of multi-carrier transmission scheme with the lowest implementation complexity and the most widely used. However, the whole OFDM system is particularly strict on the orthogonality between subcarriers. Any small carrier frequency offset will destroy the orthogonality between subcarriers, so it is sensitive to local oscillator phase noise. This paper analyzes and summarizes the existing phase noise optimization research, and a phase noise optimization algorithm based on feedback decision is proposed. Through the simulation of phase noise elimination algorithm at the receiver, it is verified that the phase noise of OFDM system can be suppressed.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040811", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96255634, "Title": "Research on Image Style Convolution Neural Network Migration Based on Deep Hybrid Generation Model", "Abstract": "<p>The main content of image style transfer transforms the image style from one region to another. This task puts forward new needs for the traditional convolutional neural network architecture. Therefore, a deep hybrid generation model is usually used in the study of processing image style transfer. Image style transfer aims to transform the image into a new idea by image generation. This paper proposes an image-style convolution neural network migration model based on the deep mixing generation model based on background. The image quality is improved through image processing. The deep hybrid generation model mainly relies on to combine confrontation network generation and self-encoder. In this paper, unsupervised and supervised image style migrations are designed according to the different basic tasks of image style migration. On this basis, unsupervised image style migration of combative neural networks based on cyclic consistency and supervised image style migration of adversarial networks based on cross-domain self-encoder are proposed. This paper further improves the quality of created images by introducing an unsupervised and supervised image style migration standard dataset.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040816", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96255638, "Title": "Dynamic Path Planning for Mobile Robots Based on the Improved A-Star Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040814", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96255710, "Title": "An Intelligent Kurdish Sign Language Recognition System Based on Tuned CNN", "Abstract": "<p>Hearing-impaired individuals have both hearing and speech disabilities. Therefore, they use a special language that involves visual gestures—known as “sign language”—for communicating ideas and emotions. Recognizing the gestures contained in sign language enables deaf people communicate more effectively with their interlocutor. It also helps people without such disabilities understand and identify those signs, thereby enriching the communication. However, designing a system that can automatically identify the signs of Kurdish sign language is a challenging task, especially for Kurdish sign language. This is attributable to the unavailability of a dataset and lack of standardized sign language. In this study, we investigate the problem by collecting a dataset of seven static signs and designing a model for sign recognition. The dataset consists of 3690 high-resolution images taken mostly from college students. To develop the classifier, a four-layer convolutional neural network model with a filter size of 5 × 5 was designed. To compare the model performance, two other pre-trained networks, namely MobileNetV2 and VGG16, were trained and fine-tuned using the same dataset. After a variety of hyperparameter fine-tuning, the proposed approach achieved the same outcome as the two pre-trained networks, with an accuracy of 99.75%. That is, the model identified 396 of the 397 images in the test set. In addition, we performed an external test using 58 images of various signs, and the model approximately classified all the images correctly. This demonstrates that our approach achieved an outstanding result, which can be considered a first in the field.</p>", "Keywords": "Sign language recognition; Data acquisition; CNN; Transfer learning", "DOI": "10.1007/s42979-022-01394-5", "PubYear": 2022, "Volume": "3", "Issue": "6", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Raparin, Raniyah, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Raparin, Raniyah, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Raparin, Raniyah, Iraq"}, {"AuthorId": 4, "Name": "Ra<PERSON>", "Affiliation": "University of Raparin, Raniyah, Iraq"}], "References": [{"Title": "Understanding vision-based continuous sign language recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "31-32", "Page": "22177", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Deep learning-based sign language recognition system for static signs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "12", "Page": "7957", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Comparative Analysis of Convolution Neural Network Models for Continuous Indian Sign Language Classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "1542", "JournalTitle": "Procedia Computer Science"}, {"Title": "Sign Language Recognition: A Deep Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113794", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Arabic sign language recognition using Ada-Boosting based on a leap motion controller", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "3", "Page": "1221", "JournalTitle": "International Journal of Information Technology"}, {"Title": "An Efficient Sign Language Recognition (SLR) System Using Camshift Tracker and Hidden Markov Model (HMM)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A Novel Web Based Dictionary Framework for Indian Sign Language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Interpretation of Swedish Sign Language Using Convolutional Neural Networks and Transfer Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Applying deep neural networks for the automatic recognition of sign language words: A communication aid to deaf agriculturists", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115601", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A comprehensive evaluation of deep models and optimizers for Indian sign language recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "", "Page": "200032", "JournalTitle": "Graphics and Visual Computing"}]}, {"ArticleId": 96255720, "Title": "Cross-supervision-based equilibrated fusion mechanism of local and global attention for semantic segmentation", "Abstract": "<p>In recent years, weakly supervised semantic segmentation has become one of the hot research directions, but the problems of object location accuracy and small activation areas are still a challenge. In this paper, we propose a network with an equilibrated fusion mechanism of local and global attention based on cross-supervision(CSEFM). To accomplish multitask learning, we design the class activation branch and decoding prediction branch, which share the same backbone, to generate high-quality pseudo labels and obtain semantic segmentation results. Specifically, the network is first trained to update the backbone weights using images with strong labels. Images with weak labels are then used to help obtain a reliable class activation mapping (CAM) at the class activation branch, and the dense conditional random fields (DenseCRFs) are used to generate high-quality pseudo labels. Finally, the strong label images and the weak label images that have obtained pseudo labels are fed to the network for retraining, and the result of segmentation is predicted by the decoding prediction branch. Given the training method, the dataset is divided into several groups as a few images with strong labels and several images with weak labels and fed to the network successively for cross-supervised learning. Our proposed network is trained and validated on the PASCAL VOC 2012 dataset, and the results show that the mean Intersection over Union (mIoU) on the validation set is 65.6%. Compared with other mainstream methods, better segmentation is achieved and the performance gap between image-level and pixel-level semantic segmentation is reduced when using our approach.</p>", "Keywords": "Semantic segmentation; Class activation mapping; Dense condition random fields; Cross-supervision; Multitask learning", "DOI": "10.1007/s10489-022-04085-z", "PubYear": 2023, "Volume": "53", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Yuan", "Affiliation": "College of Big Data and Information Engineering, Guizhou University, Guiyang, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Big Data and Information Engineering, Guizhou University, Guiyang, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Big Data and Information Engineering, Guizhou University, Guiyang, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Big Data and Information Engineering, Guizhou University, Guiyang, People’s Republic of China"}], "References": [{"Title": "Deep clustering for weakly-supervised semantic segmentation in autonomous driving scenes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "20", "JournalTitle": "Neurocomputing"}, {"Title": "Deep clustering for weakly-supervised semantic segmentation in autonomous driving scenes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "20", "JournalTitle": "Neurocomputing"}, {"Title": "Weakly-Supervised Semantic Segmentation by Iterative Affinity Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "6", "Page": "1736", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Weakly supervised instance segmentation using multi-prior fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "103261", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 96255721, "Title": "Security Analysis of Zigbee Protocol Implementation via Device-agnostic Fuzzing", "Abstract": "<p>Zigbee is widely adopted as a resource-efficient wireless protocol in the IoT network. IoT devices from manufacturers have recently been affected due to major vulnerabilities in Zigbee protocol implementations. Security testing of Zigbee protocol implementations is becoming increasingly important. However, applying existing vulnerability detection techniques such as fuzzing to the Zigbee protocol is not a simple task. Dealing with low-level hardware events still remains a big challenge. For the Zigbee protocol, which communicates over a radio channel, many existing protocol fuzzing tools lack a sufficient execution environment.</p><p> To narrow the gap, we designed Z-Fuzzer , a device-agnostic fuzzing tool for detecting security flaws in Zigbee protocol implementations. To simulate Zigbee protocol execution, Z-Fuzzer leverages a commercial embedded device simulator with pre-defined peripherals and hardware interrupt setups to interact with the fuzzing engine. Z-Fuzzer generates more high-quality test cases with code-coverage heuristics. We compare Z-Fuzzer with advanced protocol fuzzing tools, BooFuzz and Peach fuzzer, on top of Z-Fuzzer’s simulation platform. Our findings suggest that Z-Fuzzer can achieve greater code coverage in Z-Stack, a widely used Zigbee protocol implementation. Compared to BooFuz<PERSON> and <PERSON>each, Z-Fuzzer found more vulnerabilities with fewer test cases. Three of them have been assigned CVE IDs with high CVSS scores (7.5~8.2). </p>", "Keywords": "IoT network; Zigbee protocol; fuzzing", "DOI": "10.1145/3551894", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 74064, "JournalTitle": "Digital Threats: Research and Practice", "ISSN": "2576-5337", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The University of Texas at Arlington, Arlington, Texas, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Texas at Arlington, Arlington, Texas, USA"}, {"AuthorId": 3, "Name": "Huadong Feng", "Affiliation": "The University of Texas at Arlington, Arlington, Texas, USA"}, {"AuthorId": 4, "Name": "Jiang <PERSON>", "Affiliation": "The University of Texas at Arlington, Arlington, Texas, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "The University of Texas at Arlington, Arlington, Texas, USA"}], "References": []}, {"ArticleId": 96255724, "Title": "Scenario-based verification of uncertain parametric MDPs", "Abstract": "We consider parametric Markov decision processes (pMDPs) that are augmented with unknown probability distributions over parameter values. The problem is to compute the probability to satisfy a temporal logic specification with any concrete MDP that corresponds to a sample from these distributions. As solving this problem precisely is infeasible, we resort to sampling techniques that exploit the so-called scenario approach. Based on a finite number of samples of the parameters, the proposed method yields high-confidence bounds on the probability of satisfying the specification. The number of samples required to obtain a high confidence on these bounds is independent of the number of states and the number of random parameters. Experiments on a large set of benchmarks show that several thousand samples suffice to obtain tight and high-confidence lower and upper bounds on the satisfaction probability.", "Keywords": "Markov decision processes; Uncertainty; Verification; Scenario optimization", "DOI": "10.1007/s10009-022-00673-z", "PubYear": 2022, "Volume": "24", "Issue": "5", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Radboud University, Nijmegen, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Texas at Austin, Austin, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Radboud University, Nijmegen, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Radboud University, Nijmegen, The Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 6, "Name": "Ufuk <PERSON>", "Affiliation": "The University of Texas at Austin, Austin, USA"}], "References": [{"Title": "Parametric Markov chains: PCTL complexity and fraction-free Gaussian elimination", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "272", "Issue": "", "Page": "104504", "JournalTitle": "Information and Computation"}, {"Title": "The risk of making decisions from data through the lens of the scenario approach", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "7", "Page": "607", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 96255762, "Title": "Course Scheduling Information System Using Genetic Algorithms", "Abstract": "<p>University X is a private university which has 8 faculties, namely Economics, Engineering, Social Sciences, Faculty of Social and Political Sciences, Fikes, Law, Islam and Vocational Studies. One of the leading study programs at the University X is the Informatics Engineering Study Program at the Faculty of Engineering which is currently experiencing problems when the process of dividing space and hour schedules is still manual.The problems that arise include the accumulation of lecture hours for a lecturer who takes more than one course. Another problem is the disruption of the lecture process at the beginning of the semester due to the tentative scheduling system. The preparation of lecture schedules at the Faculty of Engineering, University X is still done semi-manually using Microsoft Excel, so it will take several days to make a schedule. Meanwhile, making a schedule must be done quickly and optimally because it is used for lecture activities, the solution that can overcome the above problems is by creating a Course Scheduling Information System Using Genetic Algorithms.\r The genetic algorithm is one of the most appropriate algorithms used in solving complex optimization problems, which are difficult to do with conventional methods. The nature of genetic algorithms is looking for possibilities from potential solutions to get the optimal for solving problems. The scope of all viable solutions, i.e. the objects between matching solutions, is called the search space. Each point in the search space represents a viable solution.\r The limitations of the built problem are: This system is operated by the administrative staff of the study program at the Faculty of Engineering, University X. This system is made using the Codeigniter framework, Responsive Bootstrapping (display) and MySQL as the database and the method used by this system is the genetic algorithm method.\r The final result of this research is that it is helpful and easy to apply as a tool in helping the optimization process of lecture scheduling in order to minimize schedule collisions in lectures at the Faculty of Engineering, University X</p>", "Keywords": "Genetic Algorithms; Course Scheduling; Systems", "DOI": "10.24235/itej.v6i1.55", "PubYear": 2021, "Volume": "6", "Issue": "1", "JournalId": 83875, "JournalTitle": "ITEJ (Information Technology Engineering Journals)", "ISSN": "2548-2130", "EISSN": "2548-2157", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "freddy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Muhammadiyah Cirebon"}], "References": []}, {"ArticleId": 96255768, "Title": "Graph Application in Finding the fastest Path From Subang City To Cirebon City", "Abstract": "<p>This paper discusses the application of one of the Discrete Mathematics materials, namely Graph Theory, in determining the fastest route from Subang City, more precisely from Subang Pusakanagara, Subang to Cirebon City. The fastest path is the route taken by considering the minimum travel time from one place to another. There are various ways to determine the minimum travel path, but the goal is the same, namely to find a travel path with the minimum travel time. The research process begins with formulating a problem using Graph Theory with the help of the Google Maps application and the experiences of the researchers. This Google Maps application is very useful when traveling long distances but want to use the path with minimal travel time. After formulating the problem, it is continued with the implementation of the Weighted Average Method and ends with the Floyd Warshall Algorithm. The result of this study is to find the fastest path from the starting point, namely Subang Pusakanagara to the end point, namely Cirebon City.</p>", "Keywords": "The Fastest Path;Graph Theory;Weighted Average;Floyd <PERSON>hall Algorithm", "DOI": "10.24235/itej.v6i2.104", "PubYear": 2022, "Volume": "6", "Issue": "2", "JournalId": 83875, "JournalTitle": "ITEJ (Information Technology Engineering Journals)", "ISSN": "2548-2130", "EISSN": "2548-2157", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IAIN Syekh Nurjati Cirebon"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "IAIN Syekh Nurjati Cirebon"}], "References": []}, {"ArticleId": 96255837, "Title": "bGSL: An imperative language for specification and refinement of backtracking programs", "Abstract": "We present an imperative refinement language for the development of backtracking programs and discuss its semantic foundations. For expressivity, our language includes prospective values and preference   —   the latter being a variant of Nelson&#x27;s biased choice that backtracks from infeasibility of a continuation. Our key contribution is to examine feasibility-preserving refinement as a basis for developing backtracking programs, and several key refinement laws that enable compositional refinement in the presence of non-monotonic program combinators.", "Keywords": "Backtracking ; Semantics ; Preferential choice ; Nondeterminism", "DOI": "10.1016/j.jlamp.2022.100811", "PubYear": 2023, "Volume": "130", "Issue": "", "JournalId": 781, "JournalTitle": "Journal of Logical and Algebraic Methods in Programming", "ISSN": "2352-2208", "EISSN": "2352-2216", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "HASLab / INESC TEC & Faculty of Engineering, University of Porto, Portugal;INESC-ID & IST, University of Lisbon, Portugal;Teesside University, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "INESC-ID & IST, University of Lisbon, Portugal;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "HASLab / INESC TEC & Faculty of Engineering, University of Porto, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "HASLab / INESC TEC & Faculty of Engineering, University of Porto, Portugal;INESC-ID & IST, University of Lisbon, Portugal;Teesside University, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Teesside University, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "HASLab / INESC TEC & Faculty of Engineering, University of Porto, Portugal;INESC-ID & IST, University of Lisbon, Portugal;Teesside University, United Kingdom"}], "References": []}, {"ArticleId": 96255899, "Title": "Novel hybrid multi-head self-attention and multifractal algorithm for non-stationary time series prediction", "Abstract": "Traditional time series prediction methods have shown their outstanding capabilities in time series prediction. However, due to essential differences in volatility characteristics among diverse types of non-stationary multivariate time series (NSMTS), it is difficult for traditional methods to maintain robust prediction performance. This study proposes a novel dynamic recurrent neural network to achieve stable and robust prediction performance. First, a multifractal gated recurrent unit (MF-GRU) based on the multifractal method is proposed to extract volatility characteristics. Meanwhile, to strengthen the parameters of the historical hidden layer state that has a more significant impact on the output, a self-attention mechanism is introduced into the MF-GRU, leading to a multifractal gated recurrent unit multi-head self-attention model. The efficiency of the proposed model was verified on public datasets. The experimental results show that the proposed model outperforms the traditional methods, such as long short-term memory (LSTM), the gated recurrent unit (GRU), and the minimal gated unit (MGU). etc.", "Keywords": "", "DOI": "10.1016/j.ins.2022.08.126", "PubYear": 2022, "Volume": "613", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan, 430078, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan, 430078, China;Corresponding author"}, {"AuthorId": 3, "Name": "T<PERSON><PERSON> Zhu", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan, 430078, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan, 430078, China"}], "References": [{"Title": "Forecasting in non-stationary environments with fuzzy time series", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106825", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel prediction model for the inbound passenger flow of urban rail transit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "566", "Issue": "", "Page": "347", "JournalTitle": "Information Sciences"}, {"Title": "A Model Combining Seq2Seq Network and LightGBM Algorithm for Industrial Soft Sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "12068", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Prediction of time series using an analysis filter bank of LSTM units", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107371", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Time series analysis and prediction of nonlinear systems with ensemble learning framework applied to deep learning neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "572", "Issue": "", "Page": "167", "JournalTitle": "Information Sciences"}, {"Title": "Time series predicting of COVID-19 based on deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "468", "Issue": "", "Page": "335", "JournalTitle": "Neurocomputing"}, {"Title": "Multi actor hierarchical attention critic with RNN-based feature extraction", "Authors": "<PERSON><PERSON><PERSON> Shi; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "471", "Issue": "", "Page": "79", "JournalTitle": "Neurocomputing"}, {"Title": "Machine learning techniques and data for stock market forecasting: A literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "197", "Issue": "", "Page": "116659", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Evolutionary polynomial regression algorithm combined with robust bayesian regression", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "103101", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Data-driven models for train control dynamics in high-speed railways: LAG-LSTM for train trajectory prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "600", "Issue": "", "Page": "377", "JournalTitle": "Information Sciences"}, {"Title": "SPLNet: A sequence-to-one learning network with time-variant structure for regional wind speed prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "79", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96255914, "Title": "K-Periodic Scheduling for Throughput-Buffering Trade-Off Exploration of CSDF", "Abstract": "<p>The design of time-critical embedded systems often requires static models of computation such as Cyclo-Static Dataflow. These models enable performance guarantees, execution correctness, and optimized memory usage. Nonetheless, determining optimal buffer sizing of dataflow applications remains difficult: existing methods offer either approximate solutions, or fail to provide solutions for complex instances. We propose a throughput-buffering trade-off exploration that uses K-periodic scheduling to direct a design space exploration — providing optimal solutions while significantly reducing the search space compared to existing methodologies. We compare this strategy against previous approaches and demonstrate search-space reductions over two benchmark suites, resulting in significant improvements in computation times while retaining optimal results.</p>", "Keywords": "", "DOI": "10.1145/3559760", "PubYear": 2023, "Volume": "22", "Issue": "1", "JournalId": 17571, "JournalTitle": "ACM Transactions on Embedded Computing Systems", "ISSN": "1539-9087", "EISSN": "1558-3465", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing, National University of Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Yale-NUS College, National University of Singapore, Singapore"}], "References": []}, {"ArticleId": 96255945, "Title": "Condition-based maintenance method for multi-component system based on RUL prediction: Subsea tree system as a case study", "Abstract": "To maintain process safety, condition-based maintenance (CBM) has arouse wide attention as the last part of diagnosis-prediction-maintenance in prognostics and health management. For a multi-component system, perfect maintenance is preferred to be adopted. However, because of the limitations of the special working environment, maintenance technology, or technical level of maintenance personnel, perfect maintenance cannot be realized. Generally, imperfect maintenance is considered and applied in practical engineering application. Reasonable spare part management for the multi-component system need to be scheduled to reduce maintenance preparation time and cost. A novel CBM method based on remaining useful life (RUL) for the multi-component is proposed considering imperfect maintenance and spare part management. Imperfect maintenance model is constructed by adopting virtual age rule. Quantities of spare parts are determined according to practical maintenance requirements, and ordering time and ordering type of spare parts are determined based on prediction results of RUL. In this way, total numbers of spare parts and maintenance preparation cost can be significantly reduced. Maintenance preparation threshold, spare part ordering threshold and total spare parts threshold are three maintenance decision variables that need to be optimized based on genetic algorithm. Subsea tree system for offshore oil and gas development is used to demonstrate the application of the proposed method.", "Keywords": "Condition-based maintenance ; Multi-component system ; Spare parts management ; Imperfect maintenance", "DOI": "10.1016/j.cie.2022.108650", "PubYear": 2022, "Volume": "173", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Baoping Cai", "Affiliation": "College of Mechanical and Electronic Engineering, China University of Petroleum, Qingdao, Shandong 266580, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Wang", "Affiliation": "College of Mechanical and Electronic Engineering, China University of Petroleum, Qingdao, Shandong 266580, China;Weichai Power Co., Ltd, Weifang 261001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electronic Engineering, China University of Petroleum, Qingdao, Shandong 266580, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Norwegian University of Science and Technology, Trondheim N-7034, Norway"}, {"AuthorId": 5, "Name": "Weifeng Ge", "Affiliation": "CNOOC Safety Technology Services Co., Ltd, Tianjin 300456, China"}, {"AuthorId": 6, "Name": "Rongkang Li", "Affiliation": "College of Mechanical and Electronic Engineering, China University of Petroleum, Qingdao, Shandong 266580, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electronic Engineering, China University of Petroleum, Qingdao, Shandong 266580, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering, Ocean University of China, Qingdao, Shandong 266100, China"}], "References": [{"Title": "Deep reinforcement learning based preventive maintenance policy for serial production lines", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "113701", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Remaining useful life re-prediction methodology based on Wiener process: Subsea Christmas tree system as a case study", "Authors": "<PERSON><PERSON> C<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "106983", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Expert system dedicated to condition-based maintenance based on a knowledge graph approach: Application to an aeronautic system", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "115767", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep multi-agent reinforcement learning for multi-level preventive maintenance in manufacturing systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116323", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 96255950, "Title": "Parkinson’s disease effective biomarkers based on H<PERSON>rth features improved by machine learning", "Abstract": "Parkinson’s disease (PD) is the second most common neurodegenerative condition in the world and is caused by reduced levels of dopamine in the central nervous system. The diagnosis of PD is a difficult and time-consuming task, and there is no definitive protocol for achieving it. Therefore, several studies have been performed in order to find reliable PD biomarkers. The analysis of characteristics of electroencephalogram (EEG) signals is one of the techniques that have been used in the search for biomarkers. EEG signals capture the activity of neurons through electrodes placed on the scalp and with the advancement of Artificial Intelligence (AI) techniques, their characteristics have started to be used in machine learning (ML) algorithms for the automatic diagnosis of brain diseases, suggesting that EEG signals are promising biomarkers that could be used for automatic identification of individuals with PD. Thus, this work evaluates the performance of Hjorth features obtained from electroencephalographic signals, as biomarkers for Parkinson’s disease. Using the database available at the public repository called The Patient Repository for EEG Data + Computational Tools (PRED + CT), we analyzed EEG data from PD individuals periodically exposed to auditory stimuli. The analysis of the proposed biomarkers showed differences between healthy and PD patients in parietal, frontal, central, and occipital lobes. For classification the Support Vector Machine (SVM), K-Nearest Neighbors (KNN), and Random Forest algorithms were used followed by a 5-fold cross-validation methodology. The proposed model achieved an accuracy of 89.56% when differentiating patients with PD and healthy individuals with an SVM classifier. The results suggest that the Hjorth features extracted from EEG signals could be used as PD biomarkers.", "Keywords": "Parkinson’s disease ; EEG ; <PERSON><PERSON><PERSON> features ; Machine learning", "DOI": "10.1016/j.eswa.2022.118772", "PubYear": 2023, "Volume": "212", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Postgraduate Program in Health and Biological Sciences, Federal University of Vale do São Francisco (UNIVASF), Petrolina, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Postgraduate Program in Health and Biological Sciences, Federal University of Vale do São Francisco (UNIVASF), Petrolina, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Faculty, Federal University of Vale do São Francisco (UNIVASF), Juazeiro, Bahia, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Electrical Engineering Faculty, Federal University of Vale do São Francisco (UNIVASF), Juazeiro, Bahia, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Postgraduate Program in Health and Biological Sciences, Federal University of Vale do São Francisco (UNIVASF), Petrolina, Brazil;Psychology Faculty, Federal University of Vale do São Francisco (UNIVASF), Petrolina, Brazil"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Postgraduate Program in Health and Biological Sciences, Federal University of Vale do São Francisco (UNIVASF), Petrolina, Brazil;Electrical Engineering Faculty, Federal University of Vale do São Francisco (UNIVASF), Juazeiro, Bahia, Brazil"}], "References": []}, {"ArticleId": 96255983, "Title": "Uncanny Valley Effects on Chatbot Trust, Purchase Intention, and Adoption Intention in the Context of E-Commerce: The Moderating Role of Avatar Familiarity", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2022.2121038", "PubYear": 2024, "Volume": "40", "Issue": "2", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Journalism and Digital Communication, University of South Florida, St. Petersburg, FL, USA"}, {"AuthorId": 2, "Name": "Mincheol Shin", "Affiliation": "Department of Communication and Cognition, Tilburg University, Tilburg, The Netherlands"}], "References": [{"Title": "AI-based chatbots in customer service and their effects on user compliance", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "427", "JournalTitle": "Electronic Markets"}, {"Title": "Trust me, if you can: a study on the factors that influence consumers’ purchase intention triggered by chatbots based on brain image evidence and self-reported assessments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "11", "Page": "1177", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "The human side of human-chatbot interaction: A systematic literature review of ten years of research on text-based chatbots", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "102630", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 96255996, "Title": "New Solution of Equation of Degree n with One Unknown", "Abstract": "<p>There are many methods to solve equation of degree n with one unknown, but the restrictions of most methods are very harsh. Based on <PERSON><PERSON><PERSON>’s theorem, this paper gives the symbolic effective coefficient method and the elimination of rotation symmetry method to solve the n-th equation of one variable.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050302", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Construction of Early Warning Mechanism of Network Public Opinion in Colleges and Universities under Big Data Environment", "Abstract": "", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050206", "PubYear": 2022, "Volume": "5", "Issue": "2", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Prediction on the Value Trends of Bitcoin and Gold-on Account of ARMA Time Series Forecasting Model", "Abstract": "<p>In this paper, we aimed to build a quantitative investment trading model based on a combination of a multivariate cycle ARMA model and Apriori. We first note that in order to have a sound investment strategy, a forecast for the next trading day needs to be made. To do this, a basic time series forecasting model was first built to predict the value of gold and bitcoin for the next day based on the market volatility of the previous 40 days. The next step is developing a trading strategy model with a stable rate of return and some risk tolerance. At the same time, we developed a fixed stop-loss strategy to protect the strategy's stability and improve the risk resistance performance. Ultimately, using this model, we calculated that on 10 September 2021, we will have a return of $4816941 in Bitcoin and $1129.0503 in gold.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050713", "PubYear": 2022, "Volume": "5", "Issue": "7", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256039, "Title": "Fuel Consumption Prediction Based on Whale Algorithm Optimized Support Vector Machine", "Abstract": "<p>In order to improve the prediction accuracy of vehicle fuel consumption, this paper proposes a support vector machine model based on whale optimization algorithm for prediction. First, SPSS software is used to analyze the relationship between the influencing factors of fuel consumption. On this basis, the correlation between various influencing factors and fuel consumption is analyzed. Then, the whale optimization algorithm is used to optimize the penalty parameter C and the kernel parameter g in the support vector machine kernel function, and it is brought into the model for simulation. Taking the determination coefficient and mean square error as the evaluation indexes, the prediction results show that the model has high accuracy. </p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050605", "PubYear": 2022, "Volume": "5", "Issue": "6", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "A study of bank failure risk based on neural network model", "Abstract": "<p>This paper focuses on the analysis of bank failure causes i.e. prediction. Based on the neural network model, we use principal component analysis and fuzzy C-mean cluster analysis algorithm to achieve the evaluation of bank efficiency, the mining of bank insolvency causes, and the prediction of bank failure risk. By considering the literature search and production method together, we classify the 64 indicators of the indicator set into input indicator layer and output indicator layer. In the process of data analysis, we found that some of the higher weights do not objectively reflect the causes of bank failures. In order to avoid simply selecting 5 indicators from the 64 indicators, we used principal component analysis to reduce the dimensionality, set the number of extracted factors to 5, and consider the bank failure factors comprehensively through the 5 selected principal components, and calculate the percentage weights of the eigenvalues calculated for each principal component each year to complete the calculation of each bank's score. Whether a bank fails or not has a strong nonlinear relationship with 64 indicators, and considering that some of the data present exponential relationship, logarithmic relationship, less pure linear relationship between the data, and the indicator dimension is too large. Therefore, we decided to build a model based on BP neural network model, and continuously reduce the error by forward, backward transmission.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050615", "PubYear": 2022, "Volume": "5", "Issue": "6", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Multi-Objective Optimization Model and Genetic Algorithm Based on \"FAST\" Active Reflecting Surface Dynamic Optimization Research", "Abstract": "<p>FAST requires the active reflector to be dynamically adjusted to an ideal paraboloid in real time when tracking the celestial motion, but the real paraboloid is not ideal due to the interconnection of the main cable nodes and the adjustment constraint of the reflector panel. First, the ideal paraboloid is determined when the object S is located directly above the reference sphere, and the reflecting panel adjustment is considered.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050811", "PubYear": 2022, "Volume": "5", "Issue": "7", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Search engine optimization (SEO) for digital marketers: exploring determinants of online search visibility for blood bank service", "Abstract": "Purpose Given that online search visibility is influenced by various determinants, and that influence may vary across industries, this study aims in investigating the major predictors of online search visibility in the context of blood banks. Design/methodology/approach To formalize the online visibility, the authors have found theoretical foundations in activity theory, while to quantify online visiblity the authors have used the search engine optimization (SEO) Index, ranking, and a number of visitors. The examined model includes ten hypotheses and was tested on data from 57 blood banks. Findings Results challenge shallow domain knowledge. The major predictors of online search visibility are Alternative Text Attribute (ALT) text, backlinks, robots, domain authority (DA) and bounce rate (BR). The issues are related to the number of backlinks, social score, and DA. Polarized utilization of SEO techniques is evident. Practical implications The methodology can be used to analyze the online search visibility of other industries or similar not-for-profit organizations. Findings in terms of individual predictors can be useful for marketers to better manage online search visibility. Social implications The acute blood donation problems may be to a certain degree level as the information flow between donors and blood banks will be facilitated. Originality/value This is the first study to analyze the blood bank context. The results provide invaluable inputs to marketers, managers, and policymakers.", "Keywords": "Online search visibility;Search engine optimization;SEO;Blood banks;Health care", "DOI": "10.1108/OIR-05-2022-0276", "PubYear": 2023, "Volume": "47", "Issue": "4", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Corporate Economy, Faculty of Economics and Administration, Masaryk University, Brno, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Digital Marketing, Optiweb, Škofja Loka, Slovenia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Marketing Department, Foleon, Amsterdam, Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management, School of Economics Management and Statistics, University of Bologna, Bologna, Italy"}], "References": [{"Title": "Evaluating the websites of academic departments through SEO criteria: a hesitant fuzzy linguistic MCDM approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "875", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Big Data Analytics for Search Engine Optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "5", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "Using Machine Learning for Web Page Classification in Search Engine Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "1", "Page": "9", "JournalTitle": "Future Internet"}]}, {"ArticleId": 96256112, "Title": "Research Hotspots of Data Mining Technology in the Field of Library and Information", "Abstract": "<p>With the rapid development of computer network information technology, data mining technology has begun to be widely used in all walks of life. In the field of Library and information technology, data mining technology can efficiently analyze and screen a large amount of information. This paper mainly studies the useful content, readers' interests and reading habits in the database. Firstly, it theoretically expounds the literature review of text classification methods and association rule-based algorithms by scholars at home and abroad. Then the data mining technology is used to establish the relevant models in the field of Library and information. Finally, the experimental results show that economics, geography, environmental resources, language, industrial technology and literature are the types of books that readers often consult. These five categories are a collection of common books, with a confidence level of more than 50%. Although the support of public project sets decreases with the increase of the number of public project sets, the average support of the five public project sets is 30.2%, which is higher than the minimum support of public project sets. Therefore, there is a strong correlation between the categories of books in the five project collections in the library.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040805", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256144, "Title": "YOLOv5-based fatigue state detection method", "Abstract": "<p>With the development of social economy and the continuous increase of car ownership in China, fatigue driving is one of the problems people need to focus on nowadays, however, most of the current fatigue state detection methods are affected by the problems of expensive, susceptible to the environment and complicated methods. In order to solve the above problems, this paper proposes a fatigue state detection method with YOLOv5m as the basic network model, which first enhances the original image and then improves the loss function. The experimental results show that the mean accuracy of this method can be as high as 95.6%, which is 4.13 percentage points higher than YOLOv4 and 6.2 percentage points higher than YOLOv3, and the model accuracy is as high as 98.27%, which is 4 and 5.2 percentage points higher than YOLOv4 and YOLOv3, respectively. The recall rate is 95.1%, which is 2 and 3.2 percentage points higher than YOLOv4 and YOLOv3, respectively. It proves the reliability and advantages of the method in this paper.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050304", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256152, "Title": "A GPU-based machine learning approach for detection of botnet attacks", "Abstract": "Rapid development and adaptation of the Internet of Things (IoT) has created new problems for securing these interconnected devices and networks. There are hundreds of thousands of IoT devices with underlying security vulnerabilities, such as insufficient device authentication/authorisation making them vulnerable to malware infection. IoT botnets are designed to grow and compete with one another over unsecure devices and networks. Once infected, the device will monitor a Command-and-Control (C&amp;C) server indicating the target of an attack via Distributed Denial of Service (DDoS) attack. These security issues, coupled with the continued growth of IoT, presents a much larger attack surface for attackers to exploit in their attempts to disrupt or gain unauthorized access to networks, systems, and data. Large datasets available online provide good benchmarks for the development of accurate solutions for botnet detection, however model training is often a time-consuming process. Interestingly, significant advancement of GPU technology allows shortening the time required to train such large and complex models. This paper presents a methodology for the pre-processing of the IoT-Bot dataset and classification of various attack types included. We include descriptions of pre-processing actions conducted to prepare data for training and a comparison of results achieved with GPU accelerated versions of Random Forest, k-Nearest Neighbour, Support Vector Machine (SVM) and Logistic Regression classifiers from the cuML library. Using our methodology, the best-trained models achieved at least 0.99 scores for accuracy, precision, recall and f1-score. Moreover, the application of feature selection and training models on GPU significantly reduced the training and estimation times.", "Keywords": "Internet of Things ; Machine learning ; Random forest ; Feature selection ; Attack detection ; Classification", "DOI": "10.1016/j.cose.2022.102918", "PubYear": 2022, "Volume": "123", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Mathematics, Liverpool John Moores University, Liverpool, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Mathematics, Liverpool John Moores University, Liverpool, UK;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Technological Innovation, Zayed University, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Technological Innovation, Zayed University, United Arab Emirates"}], "References": [{"Title": "Selection of effective machine learning algorithm and Bot-IoT attacks traffic identification for internet of things in smart city", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "433", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A novel Machine Learning-based approach for the detection of SSH botnet infection", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "387", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 96256181, "Title": "Research on Financial Product Price Prediction Based on Grey Prediction and WOA-BP Neural Network", "Abstract": "<p>Quantitative trading is an emerging financial investment strategy. In this paper, based on the historical data prediction-guided trading strategy problem, small-sample prediction and machine learning are used to build a prediction trading model. To solve the problem of insufficient training samples in the early stage, we build a gray prediction model for small sample forecasting, and train the neural algorithm and use it for later predictions. In the gray prediction model, the optimal prediction parameters are obtained by tuning the parameters for the differential equation. The whale algorithm was introduced to optimize the BP neural network to obtain a threshold closer to the optimal solution, which accelerated the degree of threshold optimal approximation by reducing the mean absolute percentage error (MAPE) by 26%.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050414", "PubYear": 2022, "Volume": "5", "Issue": "4", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256192, "Title": "A Supply Chain Traceability Scheme Based on Blockchain", "Abstract": "<p>The traditional supply chain management system generally uses the centralized server to store the data, which has the problems of opaque transaction information, high maintenance cost of the central server and low database security factor. In order to improve the transparency and security of data in the supply chain system, this paper uses blockchain technology for supply chain management. In addition, the cost of maintaining the anti-counterfeiting traceability platform in the traditional supply chain management system is very high, and the anti-counterfeiting code is easy to be stolen. In order to reduce the hardware cost of the anti-counterfeiting traceability platform, this paper designs a set of anti-counterfeiting traceability mechanism of supply chain products based on asymmetric encryption and digital signature technology, which improves the function of the system application layer. In order to reduce the hardware cost of the anti-counterfeiting traceability platform, this paper designs a set of anti-counterfeiting traceability mechanism of supply chain products based on asymmetric encryption and digital signature technology, which improves the function of the system application layer. In order to further improve the data processing efficiency and stability of blockchain network, this paper improves the master peer selection algorithm in fabric network.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050509", "PubYear": 2022, "Volume": "5", "Issue": "5", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256302, "Title": "Influence of warm isostatic press (WIP) process parameters on mechanical properties of additively manufactured acrylonitrile butadiene styrene (ABS) parts", "Abstract": "Owing to the deposition mechanism, parts fabricated from the material extrusion (ME) process have intrinsic air gaps that negatively impact their mechanical properties. Thus, the amount of air gaps should be minimized. In this study, a warm isostatic press (WIP) process was adopted to decrease the amount of air gaps, resulting in improved mechanical properties using acrylonitrile butadiene styrene (ABS). To identify changes in the mechanical properties, tensile tests were performed with specimens heat-treated by the WIP processes with different pressure–temperature profiles. The influence of the temperature and pressure on tensile strength, elongation at break, and toughness was investigated. Water tightness evaluation was conducted to prove the decrease in the air-gap size. Based on the investigation, the WIP process was concluded to be effective for decreasing the intrinsic air gaps and improving the mechanical properties owing to the increase of the bonding force between the lines and layers, which led to the suggestion of a method that optimizes the parameters of the WIP process.", "Keywords": "Additive manufacturing (AM); Material extrusion (ME); Warm isostatic press (WIP); Mechanical properties; Tightness", "DOI": "10.1007/s00170-022-10094-6", "PubYear": 2022, "Volume": "122", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Seong Je Park", "Affiliation": "Additive Manufacturing Innovation Agency, Korea Institute of Industrial Technology (KITECH), Siheung-si, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Additive Manufacturing Innovation Agency, Korea Institute of Industrial Technology (KITECH), Siheung-si, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Additive Manufacturing Innovation Agency, Korea Institute of Industrial Technology (KITECH), Siheung-si, Republic of Korea"}, {"AuthorId": 4, "Name": "Il Hyuk Ahn", "Affiliation": "School of Mechanical Engineering, Tongmyong University, Busan, Republic of Korea"}], "References": [{"Title": "The effects of hot isostatic pressing (HIP) and solubilization heat treatment on the density, mechanical properties, and microstructure of austenitic stainless steel parts produced by selective laser melting (SLM)", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "1-2", "Page": "109", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Additive manufacturing of a shift block via laser powder bed fusion: the simultaneous utilisation of optimised topology and a lattice structure", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "4", "Page": "460", "JournalTitle": "Virtual and Physical Prototyping"}]}, {"ArticleId": 96256375, "Title": "Parameterized DAWGs: Efficient constructions and bidirectional pattern searches", "Abstract": "Two strings x and y over Σ ∪ Π of equal length are said to parameterized match ( p-match ) if there is a renaming bijection f : Σ ∪ Π → Σ ∪ Π that is identity on Σ and transforms x to y (or vice versa). The p-matching problem is to look for substrings in a text that p-match a given pattern. In this paper, we propose parameterized suffix automata ( p-suffix automata ) and parameterized directed acyclic word graphs ( PDAWGs ) which are the p-matching versions of suffix automata and DAWGs. While suffix automata and DAWGs are equivalent for standard strings, we show that p-suffix automata can have Θ ( n 2 ) nodes and edges but PDAWGs have only O ( n ) nodes and edges, where n is the length of an input string. We also give an O ( n | Π | log ⁡ ( | Π | + | Σ | ) ) -time O ( n ) -space algorithm that builds the PDAWG in a left-to-right online manner. As a byproduct, it is shown that the parameterized suffix tree for the reversed string can also be built in the same time and space, in a right-to-left online manner. This duality also leads us to two further efficient algorithms for p-matching: Given the parameterized suffix tree for the reversal T ‾ of the input string T , one can build the PDAWG of T in O ( n ) time in an offline manner; One can perform bidirectional p-matching in O ( m log ⁡ ( | Π | + | Σ | ) + occ ) time using O ( n ) space, where m denotes the pattern length and occ is the number of pattern occurrences in the text T .", "Keywords": "Parameterized matching ; Suffix trees ; DAWGs ; Suffix automata", "DOI": "10.1016/j.tcs.2022.09.008", "PubYear": 2022, "Volume": "933", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information Sciences, Tohoku University, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information Sciences, Tohoku University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information Sciences, Tohoku University, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Kyushu University, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Information Sciences, Tohoku University, Japan;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Kyushu University, Japan;PRESTO, Japan Science and Technology Agency, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "M&D Data Science Center, Tokyo Medical and Dental University, Japan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information Sciences, Tohoku University, Japan"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Kyushu University, Japan"}], "References": [{"Title": "Simpler FM-index for parameterized string matching", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "106026", "JournalTitle": "Information Processing Letters"}]}, {"ArticleId": 96256438, "Title": "The Research of STBC in MIMO System", "Abstract": "", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050804", "PubYear": 2022, "Volume": "5", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256537, "Title": "On invertible and stably reversible non-uniform cellular automata", "Abstract": "For non-uniform cellular automata (NUCA) over an arbitrary universe with multiple local transition rules, we introduce and investigate fundamental dynamical properties such as stable injectivity, stable reversibility, and stable post-surjectivity. Over infinite amenable group universes (e.g. infinite abelian groups), we show that it is impossible to obtain injective NUCA by disturbing the local transition rules of a finite number of cells of non-injective cellular automata. Moreover, we establish the equivalence between reversibility, stable reversibility, and stable injectivity for NUCA. The surjectivity and invertibility of several classes of injective and stably injective NUCA are also obtained.", "Keywords": "Reversibility ; Non-uniform cellular automata", "DOI": "10.1016/j.tcs.2022.09.011", "PubYear": 2023, "Volume": "940", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Département d'informatique et de recherche opérationnelle, Université de Montréal, Montréal, Québec, H3T 1J4, Canada"}], "References": []}, {"ArticleId": 96256539, "Title": "Cooperative social network community partition: A data envelopment analysis approach", "Abstract": "Community partition is a significant task in social network analysis. For some interest networks, the interest of nodes may influence the formation of community. Considering the cooperation relation among nodes, we propose a cooperative social network partition approach by using the technique of data envelopment analysis. In this approach, the interest relationship among decision making units (DMUs) is identified by measuring the increased revenue of two cooperative DMUs, and then, a weighted interest network is constructed correspondingly. In this interest network, each node would like to cooperate with others to gain more benefits. Regarding each node as a player, the formation of community can be achieved by a cooperative game. We employ Shapley value mechanism mainly including five algorithms, i.e., maximum Shapley value of each node, expand algorithm, check stability, update, and merge process, to achieve community partition. Finally, the proposed method is applied to mineral resource of 31 provinces in Chinese mainland.", "Keywords": "Data envelopment analysis ; Social network ; Cooperative game ; Community partition", "DOI": "10.1016/j.cie.2022.108658", "PubYear": 2022, "Volume": "172", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Qingxian An", "Affiliation": "School of Business, Central South University, Changsha, Hunan 410083, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business, Central South University, Changsha, Hunan 410083, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Hunan University, Changsha, Hunan 410082, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "Yi Dai", "Affiliation": "School of Business, Central South University, Changsha, Hunan 410083, PR China"}], "References": [{"Title": "Minimum conflict consensus with budget constraint based on social network analysis", "Authors": "Yuxiang Yuan; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "108098", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 96256590, "Title": "Diagnosis of PV Array Faults Using RUSBoost", "Abstract": "<p>Solar photovoltaic (SPV) has become an inalienable part of the power system due to its numerous advantages over conventional energy sources. However, SPVs can be subjected to various kinds of faults which can degrade the overall performance of the system. Machine learning (ML) techniques may be useful to identify faults occurring in photovoltaic (PV) systems. In this paper, an ML technique called random under-sampling boosting (RUSBoost) has been applied to detect the different types of faults occurring on the DC side of the PV system. A test system of 4.8 kW<sub>p</sub> has been designed in MATLAB/Simulink environment for data acquisition of different operating conditions. Commonly used performance parameters have been used as features for the ML model. Thereafter, RUSBoost has been trained using features acquired from the test system. The work also investigates the optimum number of features required for fast and accurate detection of PV array faults. It has been found that, training the model with the current ratio, voltage ratio, power ratio, and array efficiency gives the best result with 99.6% training accuracy and 2.78 s of training time. The performance of RUSBoost is further compared to popular AdaBoost and bagged tree ensemble classifier algorithm to establish the efficacy of the applied ML technique. </p>", "Keywords": "Fault diagnosis; Machine learning; PV array; RUSBoost", "DOI": "10.1007/s40313-022-00947-6", "PubYear": 2023, "Volume": "34", "Issue": "1", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology Agartala, West Tripura, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology Agartala, West Tripura, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Institute of Technology Agartala, West Tripura, India"}], "References": [{"Title": "Detection and Classification of Incipient Faults in Three-Phase Power Transformer Using DGA Information and Rule-based Machine Learning Method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "5", "Page": "1251", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": 96256592, "Title": "Research on the Used Car System Based on Blockchain and Cryptographic Technology", "Abstract": "<p>Different countries have different issues with related to topic of used car in all of world, and used car issues involve very wide range of fields, such as environmental protection, market economy, sales services, information systems, regulations and policies, components, materials, and logistics so on. In this study the authors would like to propose a scheme of used car transaction information system from perspective of mathematical modelling. We introduce the concept of a third party appraiser as an arbitrator between buyers and sellers in this solution. In order to avoid collusion between third parties and sellers, the blockchain and cryptography technology be used to prevent collusion and ensure fair and just transactions.</p>", "Keywords": "Anonymous; Di<PERSON>ie-Hellman Protocol; Third Party; Used Car Trading System", "DOI": "10.37394/232018.2022.10.14", "PubYear": 2022, "Volume": "10", "Issue": "", "JournalId": 73734, "JournalTitle": "WSEAS TRANSACTIONS ON COMPUTER RESEARCH", "ISSN": "1991-8755", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Science and Engineering, Shiyuan College of Nanning Normal University, Nanning 530226, CHINA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Science and Engineering, Shiyuan College of Nanning Normal University, Nanning 530226, CHINA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics, Qingdao University, Qingdao 266061, CHINA"}], "References": [{"Title": "A study for efficiency improvement of used car trading based on a public blockchain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "9", "Page": "10621", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 96256659, "Title": "Compound adversarial examples in deep neural networks", "Abstract": "Although deep learning has made great progress in many fields, they are still vulnerable to adversarial examples. Many methods for generating adversarial examples have been proposed, which either contain adversarial perturbation or patch. In this paper, we explore the method that creates compound adversarial examples including both perturbation and patch. We show that fusing two weak attack modes can produce more powerful adversarial examples, where the patch covers only 1 % of the pixels at random location in the image, and the perturbation changes only by 2/255 in the original pixel value (scale to 0–1). For both targeted attack and untargeted attack, compound attack can improve the generative efficiency of adversarial examples, and can attain higher attack success rate with fewer iteration steps. The compound adversarial examples successfully attack the models with defensive mechanisms that previously can defend perturbation attack or patch attack. Furthermore, the compound adversarial examples show good transferability on normal trained classifiers and adversarial trained classifiers. Experimental results on a series of widely used classifiers and defense models show that the proposed compound adversarial examples have strong robustness, high effectiveness, and good transferability.", "Keywords": "", "DOI": "10.1016/j.ins.2022.08.031", "PubYear": 2022, "Volume": "613", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Hunan Province for Internet of Things and Information Security, Xiangtan University, Xiangtan, Hunan 411105, China;School of Computer Science of Xiangtan University, Xiangtan, Hunan 411105, China"}, {"AuthorId": 2, "Name": "Zhetao Li", "Affiliation": "Key Laboratory of Hunan Province for Internet of Things and Information Security, Xiangtan University, Xiangtan, Hunan 411105, China;School of Computer Science of Xiangtan University, Xiangtan, Hunan 411105, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Hunan Province for Internet of Things and Information Security, Xiangtan University, Xiangtan, Hunan 411105, China;School of Computer Science of Xiangtan University, Xiangtan, Hunan 411105, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Jinan University, Guangzhou, Guangdong 510632, China;Corresponding author at: College of Information Science and Technology, Jinan University, Guangzhou, Guangdong 510632, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National & Local Joint Engineering Research Center of Network Security Detection and Protection Technology, Jinan University, Guangzhou 510632, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang University, Zhejiang 310027, China"}], "References": [{"Title": "Adversarial example generation with adaptive gradient search for single and ensemble deep neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "528", "Issue": "", "Page": "147", "JournalTitle": "Information Sciences"}, {"Title": "A GPU-based residual network for medical image classification in smart medicine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "536", "Issue": "", "Page": "91", "JournalTitle": "Information Sciences"}, {"Title": "A face recognition framework based on a pool of techniques and differential evolution", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "219", "JournalTitle": "Information Sciences"}, {"Title": "An improved loop subdivision to coordinate the smoothness and the number of faces via multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "23", "JournalTitle": "Integrated Computer-Aided Engineering"}, {"Title": "A Kernel Correlation-Based Approach to Adaptively Acquire Local Features for Learning 3D Point Clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "146", "Issue": "", "Page": "103196", "JournalTitle": "Computer-Aided Design"}]}, {"ArticleId": 96256663, "Title": "Modelling and determination of heavy-duty vehicle driver visual field in the virtual environment", "Abstract": "<p>In the world of computers, that is, by application of modern tools to determine and represent the visual field, of the heavy-duty vehicles driver in the virtual environment, the real investigations can be successfully replaced with the virtual ones. Demands which one vehicle must to satisfy during the projecting phase are: comfort, visibility, easy manoeuvrability, esthetical demands and similar. One very important demand from the aspect of the safety of all traffic participants and reliability of all systems on the vehicle is the good visibility around the vehicle, which investigation is the aim of this paper. The purpose of this paper is the analysis of everyday situation of truck driver at the intersection, in the virtual reality, as well as the analysis of causes which lead to the traffic accident. The main aim of the paper is to determine do a truck driver sees the vulnerable group of traffic participants depending from their mutual position, by application of RAMSIS software. By application of the virtual reality, the main finding of this study, is that the truck driver in some situations cannot see the vulnerable group of traffic participants. The originality of this study bases on the investigation, do a truck driver sees the electric scooter driver, and the idea for such research have come on the basis of everyday situations, because the electric scooters are more and more present on streets.</p>", "Keywords": "Virtual environment; Traffic participants; Traffic accident; Blind spots; Ergonomics", "DOI": "10.1007/s12652-022-04397-5", "PubYear": 2023, "Volume": "14", "Issue": "8", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, University of Kragujevac, Kragujevac, Serbia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering Dept, Faculty of Engineering, South Valley University, Qena, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, University of Kragujevac, Kragujevac, Serbia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department in Trstenik, Academy of professional studies Sumadija, Kragujevac, Serbia"}], "References": [{"Title": "Analyzing the kinematic and kinetic contributions of the human upper body’s joints for ergonomics assessment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "12", "Page": "6093", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Promoting eco-driving behavior through multisensory stimulation: a preliminary study on the use of visual and haptic feedback in a virtual reality driving simulator", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "945", "JournalTitle": "Virtual Reality"}]}, {"ArticleId": 96256700, "Title": "A Multi-Layer Acoustic Neural Network-Based Intelligent Early Diagnosis System for Rheumatic Heart Disease", "Abstract": "<p>Rheumatic Heart Disease (RHD) is a disorder of heart caused by streptococcal throat infection followed by the organ damage, irreversible valve damage and heart failure. Acute Rheumatic Fever (ARF) is a precursor to the disease. Sometimes, RHD can occur without any signs or symptoms, and if there are any symptoms, they occur with the infection in the heart valves and fever. Due to these issues, respiratory problems occur with chest pain and tremors. Additionally, the symptoms include faint, heart murmurs, stroke and unexpected collapse. The techniques available try to detect the RHD as early as possible. Although the recent medical health care department uses crucial techniques, they are not accurate in terms of symptom classification, precision and prediction. On the scope, we are developing Multi-Layered Acoustic Neural (MLAN) Networks to detect the RHD symptoms using heart beat sound and Electrocardiogram (ECG) measurements. In this proposed MLAN system, the novel techniques such as multi-attribute acoustic data sampling model, heart sound sampling procedures, ECG data sampling model, RHD Recurrent Convolutional Network (RRCN) and Acoustic Support Vector Machine (ASVM) are used for increasing the accuracy. In the implementation section, the proposed model has been compared to the Long Short-Term Memory-based Cardio (LSTC) data analysis model, Cardio-Net and Video-Based Deep Learning (VBDL) techniques. In this comparison, the proposed system has 10%–17% higher accuracy in RHD detection than existing techniques.</p>", "Keywords": "Diagnosis; ECG; DL techniques; heart disease; heart sound analysis; RHD", "DOI": "10.1142/S0219467824500128", "PubYear": 2024, "Volume": "24", "Issue": "1", "JournalId": 14884, "JournalTitle": "International Journal of Image and Graphics", "ISSN": "0219-4678", "EISSN": "1793-6756", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Vel Tech Rangarajan Dr. <PERSON><PERSON> R&D, Institute of Science and Technology, Avadi, Chennai 600062, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Vel Tech Rangarajan Dr. <PERSON><PERSON> R&D, Institute of Science and Technology, Avadi, Chennai 600062, Tamil Nadu, India"}], "References": [{"Title": "Lightweight End-to-End Neural Network Model for Automatic Heart Sound Classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "54", "JournalTitle": "Information"}]}, {"ArticleId": 96256764, "Title": "VELAS: An open-source toolbox for visualization and analysis of elastic anisotropy", "Abstract": "Although elastic properties and their anisotropy play a crucial role in materials science and technology , it is not straightforward to visualize how properties such as shear modulus vary with the direction of stress/strain. Here, written in GNU Octave, we have developed an easy-to-use, flexible, and user-friendly open-source toolbox with a graphical user interface, VELAS , which automatically visualizes and analyses the elastic anisotropy of arbitrary crystal systems using second-order elastic constants. Using Voigt-<PERSON><PERSON>-<PERSON> averaging scheme, VELAS allows the calculation of more than ten significant mechanical properties such as modulus, hardness, anisotropy index, ductility , and bond type. It also supports the determination of the mechanical stability of crystals at atmospheric and high pressures using the Born mechanical stability criterion. VELAS provides a new easy-to-use tool for detecting and identifying unusual mechanical properties such as negative Poisson&#x27;s ratio , negative linear compressibility , and negative bulk modulus . VELAS offers several alternative visualization solutions for elastic properties, such as unit spherical projection, and map projection, and supports direct output of high-quality images. Furthermore, VELAS provides users an interface to read data from the Materials Project API in both online and offline modes. After introducing the basic theory, we detail the software framework, technical route, and installation specifications of VELAS. Moreover, the reliability and versatility of VELAS are confirmed by the analysis of the cases of negative linear compressibility, negative Poisson&#x27;s ratio, hardness, and fracture toughness . <b  >Program summary</b> Program Title: VELAS CPC Library link to program files: https://doi.org/10.17632/bj27474v6f.1 Developer&#x27;s repository link: https://github.com/ranzhengcode/VELAS Licensing provisions: GNU General Public License 3 Programming language: GNU Octave Nature of problem: To determine the mechanical stability of any crystal at atmospheric and high pressures. To automatically visualize and analyze the elastic anisotropy of arbitrary crystals and identify unusual elastic properties. Solution method: Firstly, the determination of the mechanical stability of crystals at atmospheric and high pressures using the Born mechanical stability criterion. Secondly, more than ten significant mechanical properties such as elastic moduli, ratios, hardness, and anisotropy index are analyzed using Voigt-Reuss-Hill averaging scheme. Then, the mechanical behavior of crystals in the elastic regime is derived from a comprehensive tensor analysis of the second-order elastic constants. Finally, Pugh Ratio, Hardness, and Fracture Toughness in 3D space are calculated based on our approximate treatment of elastic properties.", "Keywords": "", "DOI": "10.1016/j.cpc.2022.108540", "PubYear": 2023, "Volume": "283", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Materials Science and Engineering, Harbin Institute of Technology, Harbin 150001, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Harbin Institute of Technology, Harbin 150001, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Harbin Institute of Technology, Harbin 150001, People's Republic of China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Harbin Institute of Technology, Harbin 150001, People's Republic of China;Corresponding authors"}], "References": [{"Title": "LAMMPS - a flexible simulation tool for particle-based materials modeling at the atomic, meso, and continuum scales", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "108171", "JournalTitle": "Computer Physics Communications"}, {"Title": "ElTools: A tool for analyzing anisotropic elastic properties of the 2D and 3D materials", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "108195", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 96256831, "Title": "A decision-support framework for data anonymization with application to machine learning processes", "Abstract": "The application of machine learning techniques to large and distributed data archives might result in the disclosure of sensitive information about the data subjects. Data often contain sensitive identifiable information, and even if these are protected, the excessive processing capabilities of current machine learning techniques might facilitate the identification of individuals, raising privacy concerns. To this end, we propose a decision-support framework for data anonymization, which relies on a novel approach that exploits data correlations, expressed in terms of relaxed functional dependencies ( rfd s) to identify data anonymization strategies providing suitable trade-offs between privacy and data utility. Moreover, we investigate how to generate anonymization strategies that leverage multiple data correlations simultaneously to increase the utility of anonymized datasets. In addition, our framework provides support in the selection of the anonymization strategy to apply by enabling an understanding of the trade-offs between privacy and data utility offered by the obtained strategies. Experiments on real-life datasets show that our approach achieves promising results in terms of data utility while guaranteeing the desired privacy level, and it allows data owners to select anonymization strategies balancing their privacy and data utility requirements.", "Keywords": "", "DOI": "10.1016/j.ins.2022.09.004", "PubYear": 2022, "Volume": "613", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Via Giovanni Paolo II n.132, 84084 <PERSON><PERSON>o (SA), Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Via Giovanni Paolo II n.132, 84084 <PERSON> (SA), Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Via Giovanni Paolo II n.132, 84084 <PERSON><PERSON>o (SA), Italy"}, {"AuthorId": 4, "Name": "Genoveffa <PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Via Giovanni Paolo II n.132, 84084 <PERSON><PERSON>o (SA), Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Eindhoven University of Technology, Eindhoven, Netherlands"}], "References": [{"Title": "Privacy-preserving computation in cyber-physical-social systems: A survey of the state-of-the-art and perspectives", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "341", "JournalTitle": "Information Sciences"}, {"Title": "Inference attacks on genomic privacy with an improved HMM and an RCNN model for unrelated individuals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "207", "JournalTitle": "Information Sciences"}, {"Title": "Effective privacy preserving data publishing by vectorization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "311", "JournalTitle": "Information Sciences"}, {"Title": "Mining relaxed functional dependencies from data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2", "Page": "443", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Privacy-preserving high-dimensional data publishing for classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "101785", "JournalTitle": "Computers & Security"}, {"Title": "Privacy preservation for machine learning training and classification based on homomorphic encryption schemes", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "526", "Issue": "", "Page": "166", "JournalTitle": "Information Sciences"}, {"Title": "DI-Mondrian: Distributed improved Mondrian for satisfaction of the L-diversity privacy model using Apache Spark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "A weighted K-member clustering algorithm for K-anonymization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "10", "Page": "2251", "JournalTitle": "Computing"}, {"Title": "The limits of differential privacy (and its misuse in data release and machine learning)", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "33", "JournalTitle": "Communications of the ACM"}, {"Title": "Dependency Visualization in Data Stream Profiling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "", "Page": "100240", "JournalTitle": "Big Data Research"}, {"Title": "A review of Pareto pruning methods for multi-objective optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "108022", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Data anonymization evaluation for big data and IoT environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Prosanta Gope", "PubYear": 2022, "Volume": "605", "Issue": "", "Page": "381", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96256833, "Title": "On the security of TrCBC", "Abstract": "TrCBC is a variant of CBC-MAC which appeared in <PERSON> et al. (2012) [9] . The authors claimed TrCBC to be a secure message authentication code (MAC) with some interesting properties. If TrCBC is instantiated with a block cipher with block length n , then it requires ⌈ λ / n ⌉ block cipher calls for authenticating a λ -bit message and requires a single key, which is the block cipher key. The authors state that TrCBC can have tag lengths of size less than n / 2 . We show that with high probability, an adversary can forge TrCBC with tag length n / 2 − 1 with just three queries. The attack that we show can be applied to forge a large class of messages. The authors proved TrCBC to be a pseudorandom function (PRF). A scrutiny of the claimed PRF bound shows that for some recommended values of tag lengths, the bound turns out to be quite large. Thus, the security theorem does not imply security of TrCBC for all recommended tag lengths.", "Keywords": "Cryptography ; Message authentication codes ; CBC MAC ; TrCBC ; Length extension attacks", "DOI": "10.1016/j.ipl.2022.106320", "PubYear": 2023, "Volume": "179", "Issue": "", "JournalId": 5748, "JournalTitle": "Information Processing Letters", "ISSN": "0020-0190", "EISSN": "1872-6119", "Authors": [{"AuthorId": 1, "Name": "Debrup Chakraborty", "Affiliation": "Indian Statistical Institute, 203 B.T. <PERSON>, Kolkata 700108, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Statistical Institute, 203 B.T. Road, Kolkata 700108, India"}], "References": []}, {"ArticleId": 96256847, "Title": "Empirical mode decomposition using deep learning model for financial market forecasting", "Abstract": "<p>Financial market forecasting is an essential component of financial systems; however, predicting financial market trends is a challenging job due to noisy and non-stationary information. Deep learning is renowned for bringing out excellent abstract features from the huge volume of raw data without depending on prior knowledge, which is potentially fascinating in forecasting financial transactions. This article aims to propose a deep learning model that autonomously mines the statistical rules of data and guides the financial market transactions based on empirical mode decomposition (EMD) with back-propagation neural networks (BPNN). Through the characteristic time scale of data, the intrinsic wave pattern was obtained and then decomposed. Financial market transaction data were analyzed, optimized using PSO, and predicted. Combining the nonlinear and non-stationary financial time series can improve prediction accuracy. The predictive model of deep learning, based on the analysis of the massive financial trading data, can forecast the future trend of financial market price, forming a trading signal when particular confidence is satisfied. The empirical results show that the EMD-based deep learning model has an excellent predicting performance.</p>", "Keywords": "Decision making and analysis;Deep learning;EMD;Eigenmode function;Interval EMD;Particle swarm optimization;Time series", "DOI": "10.7717/peerj-cs.1076", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Management, Ocean University of China, Qingdao, Shandong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Yingcai Information Technology Ltd., Fengxian, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jinan University, Nanshan, Shenzhen, China"}], "References": [{"Title": "Stock closing price prediction based on sentiment analysis and LSTM", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "13", "Page": "9713", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A bibliometric analysis and cutting-edge overview on fuzzy techniques in Big Data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103625", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "RETRACTED ARTICLE: Stock market analysis using candlestick regression and market trend prediction (CKRM)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "4819", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Deep learning for financial applications : A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106384", "JournalTitle": "Applied Soft Computing"}, {"Title": "Artificial intelligence and internet of things in small and medium-sized enterprises: A survey", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "362", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "The application research of neural network and BP algorithm in stock price pattern classification and prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "872", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Forecasting daily stock trend using multi-filter feature selection and deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114444", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Computational Intelligence in the hospitality industry: A systematic literature review and a prospect of challenges", "Authors": "<PERSON>-Montenegro; <PERSON>-<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107082", "JournalTitle": "Applied Soft Computing"}, {"Title": "Experimental Analysis of Hyperparameters for Deep Learning-Based Churn Prediction in the Banking Sector", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "34", "JournalTitle": "Computation"}, {"Title": "A review of machine learning experiments in equity investment decision-making: why most published research findings do not live up to their promise in real life", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "3", "Page": "221", "JournalTitle": "International Journal of Data Science and Analytics"}, {"Title": "An intelligent quantitative trading system based on intuitionistic-GRU fuzzy neural networks", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107471", "JournalTitle": "Applied Soft Computing"}, {"Title": "Forecasting crude oil prices based on variational mode decomposition and random sparse Bayesian learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "108032", "JournalTitle": "Applied Soft Computing"}, {"Title": "EfficientNet: A Low-bandwidth IoT Image Sensor Framework for Cassava Leaf Disease Classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "x", "Page": "4031", "JournalTitle": "Sensors and Materials"}]}, {"ArticleId": 96256864, "Title": "Research on Real-time Tracking Algorithm of Moving Objects Based on Machine Vision", "Abstract": "", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040804", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256873, "Title": "A multi-group ant colony algorithm-based method for planning emergency repair tasks for electrical equipment", "Abstract": "<p>The electrical system is composed of multiple types of equipment such as power plants, substations, transmission lines, distribution systems and loads, etc. Different equipment corresponds to different maintenance strategies, so for multiple types of electrical equipment emergency repair task planning problems, this paper proposes a method that uses a multi-group ant colony algorithm to solve the problem considering factors such as equipment importance, task threat, path distance and repair operation time, and uses the method to carry out case simulation. In the simulation experiment of planning the task planning of 25 electrical equipment emergency repair in 3 categories with a time of 5.2 seconds, the research results show that the algorithm can quickly solve to get the task assignment and route order of the equipment emergency repair group with feasibility and rationality.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050305", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256890, "Title": "Efficient multi-granularity network for fine-grained image classification", "Abstract": "<p>Fine-grained visual classification (FGVC) is widely used to identify different sub-categories of ships, dogs, flowers, and so on, and aims to help the ordinary people distinguish sub-categories with only slight differences. It mainly faces the challenges of small inter-class differences and large intra-class variations. The current effective methods adopt multi-scale or multi-granularity feature to find the subtle difference. However, these methods pay their attentions to the accuracy while neglecting the computational cost in practice. Therefore, in this paper, an improved efficient Multi-granularity Learning method with Only Forward Once (MLOFO) is proposed. It reduces the forward and back propagation in training from several times to once, and decreases the computational cost several times. And more, an intra-class metric loss, named prototype metric (PM) loss, is proposed to supervise learning the effective features for classification in a multi-granularity network (MGN) framework. The effectiveness of the proposed method is verified on four fine-grained classification datasets (CUB-200-2011, Stanford Cars, FGVC-Aircraft, and AircraftCarrier). Experimental results demonstrate that our method achieves state-of-the-art accuracies, substantially improving FGVC tasks. Furthermore, we discuss that the new PM loss can compress the distribution of the intra-class features as label smoothing to achieve better generalization ability. Our method is helpful to promote the training efficiency of the MGN model and improve the accuracy of fine-grained classification to a certain extent.</p>", "Keywords": "Image classification; Fine-grained classification; Loss function; Intra-class variations; Multi-granularity", "DOI": "10.1007/s11554-022-01228-w", "PubYear": 2022, "Volume": "19", "Issue": "5", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Army Engineering University of PLA, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Army Engineering University of PLA, Nanjing, China"}, {"AuthorId": 3, "Name": "Hang Li", "Affiliation": "Army Engineering University of PLA, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Army Engineering University of PLA, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Army Engineering University of PLA, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Army Engineering University of PLA, Nanjing, China"}], "References": [{"Title": "A new hybrid chaotic atom search optimization based on tree-seed algorithm and Levy flight for solving optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3079", "JournalTitle": "Engineering with Computers"}, {"Title": "A New Supervised Clustering Framework Using Multi Discriminative Parts and Expectation–Maximization Approach for a Fine-Grained Animal Breed Classification (SC-MPEM)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "727", "JournalTitle": "Neural Processing Letters"}, {"Title": "Part-based annotation-free fine-grained classification of images of retail products", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108257", "JournalTitle": "Pattern Recognition"}, {"Title": "Two-view fine-grained classification of plant species", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "467", "Issue": "", "Page": "427", "JournalTitle": "Neurocomputing"}, {"Title": "A learning automata-based hybrid MPA and JS algorithm for numerical optimization problems and its application on data clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107682", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 96256891, "Title": "Analysis of China's population growth forecast based on Leslie model under the three-child policy", "Abstract": "<p>The number and structure of the population are important factors affecting socio-economic development. China has experienced the implementation of \"family planning\", \"comprehensive two-child\" and \"liberalized three-child\" policies, which are all adjustments to the trend of China's population development. In this paper, we establish a mathematical model by combining relevant data. This paper establishes a mathematical model to predict the demographic situation of China in the next 10 years after the opening of the three-child policy, considering the age structure of China's population. Based on this, we analyze how to implement the new policies in medical care to further alleviate the aging process of China's population, considering the actual situation at present.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050809", "PubYear": 2022, "Volume": "5", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256927, "Title": "Multi-donor Neural Transfer Learning for Genetic Programming", "Abstract": "<p>Genetic programming (GP), for the synthesis of brand new programs, continues to demonstrate increasingly capable results towards increasingly complex problems. A key challenge in GP is how to learn from the past, so that the successful synthesis of simple programs can feed in to more challenging unsolved problems. Transfer Learning in the literature has yet to demonstrate an automated mechanism to identify existing donor programs with high-utility genetic material for new problems, instead relying on human guidance. In this paper we present a transfer learning mechanism for GP which fills this gap: we use a Turing-complete language for synthesis, and demonstrate how a neural network (NN) can be used to guide automated code fragment extraction from previously solved problems for injection into future problems. Using a framework which synthesises code from just 10 input-output examples, we first study NN ability to recognise the presence of code fragments in a larger program, then present an end-to-end system which takes only input-output examples and generates code fragments as it solves easier problems, then deploys selected high-utility fragments to solve harder ones. The use of NN-guided genetic material selection shows significant performance increases, on average doubling the percentage of programs that can be successfully synthesised when tested on two separate problem corpora, in comparison with a non-transfer-learning GP baseline.</p>", "Keywords": "Genetic programming; neural networks; transfer learning; genetic algorithms", "DOI": "10.1145/3563043", "PubYear": 2022, "Volume": "2", "Issue": "4", "JournalId": 87585, "JournalTitle": "ACM Transactions on Evolutionary Learning and Optimization", "ISSN": "2688-299X", "EISSN": "2688-3007", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Lancaster University, Lancaster, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Lancaster University, UK"}], "References": [{"Title": "Transfer learning in constructive induction with Genetic Programming", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "4", "Page": "529", "JournalTitle": "Genetic Programming and Evolvable Machines"}]}, {"ArticleId": 96256929, "Title": "Driving Maneuver Anomaly Detection Based on Deep Auto-Encoder and Geographical Partitioning", "Abstract": "<p> This paper presents GeoDMA , which processes the GPS data from multiple vehicles to detect anomalous driving maneuvers, such as rapid acceleration, sudden braking, and rapid swerving. First, an unsupervised deep auto-encoder is designed to learn a set of unique features from the normal historical GPS data of all drivers. We consider the temporal dependency of the driving data for individual drivers and the spatial correlation among different drivers. Second, to incorporate the peer dependency of drivers in local regions, we develop a geographical partitioning algorithm to partition a city into several sub-regions to do the driving anomaly detection. Specifically, we extend the vehicle-vehicle dependency to road-road dependency and formulate the geographical partitioning problem into an optimization problem. The objective of the optimization problem is to maximize the dependency of roads within each sub-region and minimize the dependency of roads between any two different sub-regions. Finally, we train a specific driving anomaly detection model for each sub-region and perform in-situ updating of these models by incremental training. We implement GeoDMA in Pytorch and evaluate its performance using a large real-world GPS trajectories. The experiment results demonstrate that GeoDMA achieves up to 8.5% higher detection accuracy than the baseline methods. </p>", "Keywords": "", "DOI": "10.1145/3563217", "PubYear": 2023, "Volume": "19", "Issue": "2", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, University of California, Merced, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, University of California, Merced, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Computer Science, University of Central Florida, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Computer Science, City University of Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science and Engineering, University of California, Merced, USA"}], "References": []}, {"ArticleId": 96256976, "Title": "Research on asset trading strategy based on forecasting model and decision-making trading model", "Abstract": "<p>Quantitative investment is becoming more and more popular among traders. Market traders who seek to maximize returns by buying and selling volatile assets focus more on quantitative trading. They try to find the optimal trading strategy by quantitative investment. We developed a decision trading model to try to find the optimal trading strategy to obtain the optimal returns. Our model consists of four parts, data processing, Arima prediction model, risk prediction model and bull-bear market predicting model.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050708", "PubYear": 2022, "Volume": "5", "Issue": "7", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96256988, "Title": "Financial Time Series Forecasting Based on LSTM Neural Network optimized by Wavelet Denoising and Whale Optimization Algorithm", "Abstract": "<p>In order to further explore the application of deep learning in predicting financial market time series data and improve the accuracy of the prediction, this paper adopts a financial time series prediction method based on wavelet denoising, whale optimization algorithm and long-short term memory (LSTM) neural network. This article chooses 10 common evaluation indexes in the financial market as the input, the financial time series data are denoised by wavelet analysis. Then the optimal LSTM neural network parameters are obtained by whale optimization algorithm (WOA). Finally, the LSTM neural network algorithm is used for stock prediction to output the predicted closing price. To verify the effectiveness of WP-WOA-LSTM model, three other neural networks are used to compare with the forecasting result. By comparing the prediction accuracy of different methods, it is obvious that the mean absolute error (MAE) of LSTM neural network under whale optimization algorithm can be reduced by 22 % compared with the standard LSTM neural network. Therefore, the results show that WOA-LSTM model has significantly improved the prediction accuracy.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050701", "PubYear": 2022, "Volume": "5", "Issue": "7", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257062, "Title": "Stock Price Trend Analysis Method Based on Change Cycle Clustering", "Abstract": "<p>With the continuous development of financial informatization and the continuous improvement of China's economic system, analyzing and mining financial data has become an important means to study financial problems. Compared with other industries in the financial field, stock data is easier to collect and store, and its application is more convenient. Analyzing and predicting the changing trend of the future stock market through the historical data of stocks is helpful to reduce the risk of investors and increase income. It has become a research hotspot in the financial field. Using the historical price data of domestic stocks, a method based on a clustering algorithm is proposed to analyze the periodic characteristics of stock price changes. This method can provide a basis for the prediction of stock price and the detection of trading behaviour in the stock market.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040808", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257066, "Title": "Recognition and classification of flower species based on artificial intelligence", "Abstract": "<p>The deep learning algorithm draws more and more attention in recent years. It mimics how human recognize images, and the ability of extract abstract features from original input, which greatly improves the efficiency of image processing, makes it popular in fields like image recognition and face recognition. This paper discusses the application of deep learning algorithm by showing how it relates to each field and designs a flower recognition experiment to explore how deep learning algorithm works. The experiment focuses on the best-known CNN model and chooses three kinds of flowers to test its efficiency and the impact of parameters in the model, like learning rate and steps. The experiment also explores the optimization which focuses on the overfitting.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040815", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257072, "Title": "MOFA/D-URAM for Solving the Air and Missile Defense Problem Based on Uncertainty Theory", "Abstract": "<p>For the uncertain factors in the fire allocation process of air and missile defense problem, the uncertainty theory is used to deal with the uncertain factors in the problem, and an uncertain multi-objective dynamic weapon target assignment model is proposed. In order to deal with the above model, a multi-objective evolutionary algorithm based on decomposition is proposed, which adds the displacement mechanism of firefly algorithm and uniformly randomly adaptive weights mechanism. Then, the simulation results show that the proposed algorithm has good convergence and distribution uniformity for solving multi-objective optimization problem. Lastly, using the algorithm to solve the above model, the results verify the rationality of the model.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050301", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257186, "Title": "Exploring the role of task on kinematic variability and assessing consistency in individual responses across repetitive manual tasks", "Abstract": "<p>To gain a greater understanding of motor variability (MV) as an individual trait, the effect of task type on MV and individual consistency in MV across three tasks was investigated. Twenty participants performed repetitive carrying, lifting and simulated sawing tasks. MV was assessed using the linear measure of mean point-by-point standard deviation in three-dimensional upper body joint angles. Task type affected MV, where carrying showed higher MV compared to sawing (23-29%) and lifting (12-19%). Furthermore, MV was higher in lifting compared to sawing (12-25%). Poor to moderate individual consistency (ICC =0.42-0.63) was found across tasks. Task type determined MV and only some support for MV as an individual trait across tasks was found. Based on this work, differences in degrees of freedom afforded by the task influences the opportunity to exploit MV, and possibly individual consistency in MV magnitude is specific to the degrees of freedom afforded by the task.</p>", "Keywords": "individual consistency;kinematic variability;manual tasks;motor variability;repetitive tasks", "DOI": "10.1080/00140139.2022.2125178", "PubYear": 2023, "Volume": "66", "Issue": "6", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Kinesiology and Health Sciences, University of Waterloo, Waterloo, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Kinesiology and Health Sciences, University of Waterloo, Waterloo, Canada;Faculty of Health Sciences, School of Human Kinetics, University of Ottawa, Ottawa, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Kinesiology and Health Sciences, University of Waterloo, Waterloo, Canada"}], "References": [{"Title": "Consistent individual motor variability traits demonstrated by females performing a long-cycle assembly task under conditions differing in temporal organisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "85", "Issue": "", "Page": "103046", "JournalTitle": "Applied Ergonomics"}, {"Title": "Exploring the role of task constraints on motor variability and assessing consistency in individual responses during repetitive lifting using linear variability of kinematics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "100", "Issue": "", "Page": "103668", "JournalTitle": "Applied Ergonomics"}]}, {"ArticleId": 96257374, "Title": "NullSpaceRDAR: Regularized discriminative adaptive nullspace for object tracking", "Abstract": "Recently, discriminative-based and Siamese-based trackers have achieved outstanding performance on most tracking benchmarks. However, these trackers use the pre-trained backbone networks that have been mainly designed for classification to extract target-specific features without taking into consideration the visual object tracking task. In this paper, we propose NullSpaceRDAR, a novel tracker that learns a robust target-specific feature representation specifically designed for object tracking. This feature representation is learned by projecting the traditional backbone feature space onto a novel discriminative nullspace that is used to regularize the backbone loss function. We refer to the discriminative nullspace herein as joint-nullspace. The same target features (i.e., target-specific) in the proposed joint-nullspace are collapsed into a single point, and different target-specific features are collapsed into different points. Consequently, the joint-nullspace forces the network to be sensitive to the object’s variations from the same class (i.e., intra-class variations). Moreover, a modified adaptive loss function is developed for bounding box estimation to select the most suitable loss function from a super set family of loss functions based on the training data. This makes NullSpaceRDAR more robust to different challenges such as occlusions and background clutter. Extensive experiments have been conducted on six benchmarks to evaluate NullSpaceRDAR: OTB100, VOT variations (VOT2018, VOT2019, and VOT2020), LaSOT, TrackingNet, UAV123, and GOT10k. The results show that NullSpaceRDAR outperforms the state-of-the-art trackers.", "Keywords": "Visual object tracking ; Joint-nullspace ; Convolutional neural network", "DOI": "10.1016/j.imavis.2022.104550", "PubYear": 2022, "Volume": "127", "Issue": "", "JournalId": 5631, "JournalTitle": "Image and Vision Computing", "ISSN": "0262-8856", "EISSN": "1872-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Mathematics, Physics and Statistics, University of British Columbia, Kelowna, BC, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Mathematics, Physics and Statistics, University of British Columbia, Kelowna, BC, Canada"}], "References": []}, {"ArticleId": 96257395, "Title": "Leader election of dynamic wireless intelligent control machine in sensor network distributed processing", "Abstract": "Strategies to streamline energy resources in wireless sensor networks (WSN) are still a hot topic. Many researchers have proposed various models of the WSN protocol so that the base station (BS)/sink can receive data collected from each sensor node. The energy in the WSN, which has a long life span, will convey a lot of data to the sink until the sensor nodes die. This paper has proposed modeling the WSN protocol by utilizing the classification and clustering of sensor nodes using the chimpanzee leader election optimization (CLEO) method. The CLEO algorithm is a meta-heuristic algorithm inspired by a social model that applies the pattern of leader selection to the chimpanzee community. The optimization method has the advantage of getting a fast fitness value which is one of the critical factors in utilizing meta-heuristic models in the WSN field. The closest sensor nodes at a certain distance to the sink are marked as classification, while other sensor nodes are marked as clusters. The clustering of sensor nodes utilizes a cluster function using the CLEO algorithm. This paper also carried out the selection of group leaders to obtain an energy efficiency model from WSN-CLEO. The results of the proposed method, WSN-CLEO, have been compared to other protocol models.", "Keywords": "Classification ; Chimpanzee leader election optimization ; Clustering ; Energy efficiency ; Intelligent wireless sensor network", "DOI": "10.1016/j.jksuci.2022.08.037", "PubYear": 2022, "Volume": "34", "Issue": "10", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Doctoral Program of Computer Science, Information Technology Faculty, Satya Wacana Christian University, Salatiga, Indonesia"}, {"AuthorId": 2, "Name": "Ferry Wahyu Wibowo", "Affiliation": "Doctoral Program of Computer Science, Information Technology Faculty, Satya Wacana Christian University, Salatiga, Indonesia;Informatics Department, Computer Science Faculty, Universitas Amikom Yogyakarta, Jl. Ring Road Utara, Condong Catur, Depok, Sleman, Yogyakarta, Indonesia;Corresponding author at: Doctoral Program of Computer Science, Information Technology Faculty, Satya Wacana Christian University, Salatiga, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON>ndriyanto <PERSON>", "Affiliation": "Doctoral Program of Computer Science, Information Technology Faculty, Satya Wacana Christian University, Salatiga, Indonesia"}], "References": [{"Title": "Clustering objectives in wireless sensor networks: A survey and research direction analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "180", "Issue": "", "Page": "107376", "JournalTitle": "Computer Networks"}, {"Title": "Swarm intelligence–based energy efficient clustering with multihop routing protocol for sustainable wireless sensor networks", "Authors": "<PERSON>; <PERSON> <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "9", "Page": "***************", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Clustering protocols in wireless sensor network: A survey, classification, issues, and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100396", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": ********, "Title": "Kinematic calibration of a hexapod robot based on monocular vision", "Abstract": "<p>Robot kinematic calibration is an effective way to reduce the errors of kinematic parameters and improve the positioning accuracy of a robot. This paper presents a cost-effective kinematic calibration method for a hexapod robot that only needs a monocular camera and two planar markers. The markers are attached to the body and the foot-tip of the robot separately, and the robot’s six legs are calibrated one by one. The kinematic model and error model of the robot are established based on the local product of exponential (POE) model, and the calibration task is formulated as a nonlinear least squares problem where 24 unknown parameters are estimated for each leg. The proposed calibration procedure is successfully evaluated on a real hexapod robot, and the experimental results show that the robot can have a better walking performance after calibration.</p>", "Keywords": "Hexapod robot; Kinematic calibration; Monocular vision; Local POE", "DOI": "10.1007/s00138-022-01339-1", "PubYear": 2022, "Volume": "33", "Issue": "6", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Fluid Power and Mechatronic Systems, Zhejiang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Fluid Power and Mechatronic Systems, Zhejiang University, Hangzhou, China; Ningbo Research Institute, Zhejiang University, Ningbo, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ningbo Hoyea Machinery Manufacture Co., Ltd., Ningbo, China"}], "References": []}, {"ArticleId": 96257457, "Title": "Managing hospital inpatient beds under clustered overflow configuration", "Abstract": "Inpatient beds represent one of the most critical resources in hospitals, of which the availability has been under increasing pressure in China. Such beds are usually to be either used exclusively (“dedicated”) by each department or shared (“pooled”) among departments. This paper considers a generalized bed configuration known as the clustered overflow configuration. The departments are partitioned and managed as clusters. Each department has its dedicated beds and can also admit patients to overflowed beds shared among the departments in the same cluster. A mixed-integer stochastic programming model is developed to optimize the partition and bed-allocation decisions. The objective is to minimize the weighted total cost of rejecting patients, holding patients waiting, nursing cost, and partitioning cost. A simulation-based metaheuristic approach (SMA) is proposed to solve the problem. A niching genetic algorithm (NGA) framework is first proposed to optimize the partitions, and then each partition is evaluated by optimizing the underlying bed-allocation plan through adaptive hyperbox algorithm-based local search (AHA-LS). In AHA-LS, two speeding mechanisms are proposed to effectively identify promising partitions and discard the worst ones. One is an original sequential simulation budget allocation (SSBA) mechanism to sequentially allocate the simulation iteration to evaluate the bed allocation decisions for a given partition; the other is optimal computing budget allocation (OCBA) to allocate the simulation budget to evaluate the bed-allocation plans. The elite clusters form a cluster pool, based on which a set covering model is proposed and solved to obtain a better solution. Case studies are conducted based on real data collected from a public hospital in Shanghai, China. The numerical results collectively demonstrate the applicability and efficiency of the proposed method. Sensitivity analyses are also performed to gain managerial insights.", "Keywords": "Bed allocation ; Clustered overflow configuration ; Stochastic programming model ; Simulation optimization ; Simulation-based metaheuristic", "DOI": "10.1016/j.cor.2022.106021", "PubYear": 2022, "Volume": "148", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Management, Shanghai Jiao Tong University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sino-US Global Logistics Institute, Shanghai Jiao Tong University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Sino-US Global Logistics Institute, Shanghai Jiao Tong University, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Sino-US Global Logistics Institute, Shanghai Jiao Tong University, China;Corresponding author"}], "References": [{"Title": "A fuzzy rule-based efficient hospital bed management approach for coronavirus disease-19 infected patients", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "14", "Page": "11361", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Integrated Multiresource Capacity Planning and Multitype Patient Scheduling", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "129", "JournalTitle": "INFORMS Journal on Computing"}]}, {"ArticleId": 96257485, "Title": "Heterogeneous big data fusion in distributed networking systems for anomaly detection and localisation", "Abstract": "An efficient anomaly detection and localisation mechanism is crucial for achieving high-quality network services. In particular, learning-based methods have recently been developed to achieve this goal by discovering helpful information from a massive amount of heterogeneous network data. However, heterogeneous data from various network components lead to significant challenges and an unexpected burden for analysis. The distributed scale of networking systems challenges data integrity and knowledge retrieval due to the separation of coupled functions over the distributed system. In this article, an insightful survey is performed by thoroughly reviewing recent academic and industrial contributions regarding anomaly detection and localisation. To tackle the issues, we propose a new framework to effectively learn informative representations of heterogeneous data and fuse this information for efficient anomaly detection and localisation. Furthermore, a case study is presented for anomaly detection and localisation through learning data representations and performing heterogeneous data fusion. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "anomaly detection; anomaly localisation; data heterogeneity; distributed networking systems; machine learning", "DOI": "10.1504/IJSN.2022.125511", "PubYear": 2022, "Volume": "17", "Issue": "3", "JournalId": 15172, "JournalTitle": "International Journal of Security and Networks", "ISSN": "1747-8405", "EISSN": "1747-8413", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Innovation Institution of Defence Technology, PLA Academy of Military Science, Beijing, China"}, {"AuthorId": 2, "Name": "Xiaozhou Zhu", "Affiliation": "National Innovation Institution of Defence Technology, PLA Academy of Military Science, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Innovation Institution of Defence Technology, PLA Academy of Military Science, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Innovation Institution of Defence Technology, PLA Academy of Military Science, Beijing, China"}], "References": []}, {"ArticleId": 96257490, "Title": "Joint alignment and steering to manage interference", "Abstract": "In wireless communication networks, mobile users in overlapping areas may experience severe interference, therefore, designing effective Interference Management (IM) methods is crucial to improving network performance. However, when managing multiple disturbances from the same source, it may not be feasible to use existing IM methods such as Interference Alignment (IA) and Interference Steering (IS) exclusively. It is because with IA, the aligned interference becomes indistinguishable at its desired Receiver (Rx) under the cost constraint of Degrees-of-Freedom (DoF), while with IS, more transmit power will be consumed in the direct and repeated application of IS to each interference. To remedy these deficiencies, Interference Alignment Steering (IAS) is proposed by incorporating IA and IS and exploiting their advantages in IM. With IAS, the interfering Transmitter (Tx) first aligns one interference incurred by the transmission of one data stream to a one-dimensional subspace orthogonal to the desired transmission at the interfered Rx, and then the remaining interferences are treated as a whole and steered to the same subspace as the aligned interference. Moreover, two improved versions of IAS, i.e., IAS with Full Adjustment at the Interfering Tx (IAS-FAIT) and Interference Steering and Alignment (ISA), are presented. The former considers the influence of IA on the interfering user-pair's performance. The orthogonality between the desired signals at the interfered Rx can be maintained by adjusting the spatial characteristics of all interferences and the aligned interference components, thus ensuring the Spectral Efficiency (SE) of the interfering communication pairs. Under ISA, the power cost for IS at the interfered Tx is minimized, hence improving SE performance of the interfered communication-pairs. Since the proposed methods are realized at the interfering and interfered Txs cooperatively, the expenses of IM are shared by both communication-pairs. Our in-depth simulation results show that joint use of IA and IS can effectively manage multiple disturbances from the same source and improve the system's SE.", "Keywords": "Interference; Interference management; Interference alignment; Interference steering; Spectral efficiency", "DOI": "10.1016/j.dcan.2022.09.001", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710126, China;Technol. on Commun. Net. Lab., The 54th Research Inst. of China Elec. Technol. Group Corp., Shijiazhuang, 050081, China;Corresponding author. School of Cyber Engineering, Xidian University, Xi'an, 710126, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710126, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710126, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi'an, 710126, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Cyber Engineering, Xidian University, Xi'an, 710126, China;The Department of Communications and Networking (Comnet), Aalto University, 02150, Espoo, Finland"}], "References": []}, {"ArticleId": 96257518, "Title": "Special issue on Ambient Intelligence in the IoT: Convergence Trends and Challenges (AmIIoT)", "Abstract": "", "Keywords": "", "DOI": "10.1007/s12652-022-04406-7", "PubYear": 2022, "Volume": "13", "Issue": "11", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Social Sciences, University of Castilla-La Mancha, Talavera de la Reina, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Engineering and Exact Sciences, Anáhuac Mayab University, Mérida, Mexico"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Division of Networked and Embedded Systems, Mälardalen University, Västerås, Sweden"}], "References": []}, {"ArticleId": 96257591, "Title": "DenseBert4Ret: Deep bi-modal for image retrieval", "Abstract": "In this study, we focus on the task of desired image retrieval from an extensive database. This comes under the purview of image retrieval systems. Users may input an image and a text as a query and expect the system to retrieve an image. The retrieved image should be close to the users’ desires expressed in the query. Nowadays, digital media generates more than petabytes of imagery data in one day. Moreover, due to the internet, this massive amount of data is readily available to users. However, extracting the desired images from such a colossal databank is a challenging task. Users always prefer to extract an image that reflects their wishful thinking, and the user may desire to alter their visionary thoughts according to their abstract thoughts. For example, <PERSON> wants to have a laptop that is similar to her friend’s laptop. However, she wants to have the same kind of laptop with a built-in GPU and in silver color. So she expects the e-business platform to show a laptop according to her wish. This paper attempts to devise a multi-modal algorithm for such tasks. It takes care of the user’s visual and textual query. It has a query image and text as input and retrieves an image similar to the input image but modified according to the text query. This study focuses on a multi-modal image retrieval system that processes both image and text as input queries. Users can input an image and a text query to modify the image or add more information on it. The text reflects the desired modifications in the image. We proposed a bi-modal image retrieval system named “ DenseBert 4 Ret ” that learns image and text features concurrently. As the name indicates, DenseNet and BERT models are used for image and text features extractions, respectively. It is based on deep learning techniques used for the joint representation of image and text features. We trained the model, which forces the input image to be modified according to the user’s textual query. We used deep information learning to train and test our model on three challenging real-world datasets, i.e., MIT States, Fshion200K and FashionIQ. We also show that the proposed model outperforms its predecessor with tuned parameters.", "Keywords": "", "DOI": "10.1016/j.ins.2022.08.119", "PubYear": 2022, "Volume": "612", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, Gwangju Institute of Science and Technology (GIST), South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Science and Technology (NUST), Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dankook University, South Korea"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, Gwangju Institute of Science and Technology (GIST), South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, Gwangju Institute of Science and Technology (GIST), South Korea;Corresponding author"}], "References": [{"Title": "Towards multi-modal causability with Graph Neural Networks enabling information fusion for explainable AI", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "28", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 96257596, "Title": "A split–merge clustering algorithm based on the k-nearest neighbor graph", "Abstract": "Numerous graph-based clustering algorithms relying on k-nearest neighbor (KNN) have been proposed. However, the performance of these algorithms tends to be affected by many factors such as cluster shape, cluster density and outliers. To address these issues, we present a split–merge clustering algorithm based on the KNN graph (SMKNN), which is based on the idea that two adjacent clusters can be merged if the data points located in the connection layers of the two clusters tend to be consistent in distribution. In Stage 1, a KNN graph is constructed. In Stage 2, the subgraphs are obtained by removing the pivot points from the KNN graph, in which the pivot points are determined by the size of local distance ratio of data points. In Stage 3, the adjacent cluster pairs satisfying the maximum similarity are merged, in which the similarity measure of two clusters is designed with two concepts including external connection edges and internal connection edges. By the experiments on ten synthetic data sets and eight real data sets, we compared SMKNN with two traditional algorithms, two density-based algorithms, nine graph-based algorithms and four neural network based algorithms in accuracy. The experimental results demonstrate a good performance of the proposed clustering method.", "Keywords": "K-nearest neighbor ; Clustering algorithm ; Split ; Merge ; Similarity measure", "DOI": "10.1016/j.is.2022.102124", "PubYear": 2023, "Volume": "111", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, 200234, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, 200234, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, 200234, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, 200234, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science & Engineering, Vellore Institute of Technology, Vellore, 632014, India"}], "References": [{"Title": "Design of building construction safety prediction model based on optimized BP neural network algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "11", "Page": "7839", "JournalTitle": "Soft Computing"}, {"Title": "A novel density-based clustering algorithm using nearest neighbor graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107206", "JournalTitle": "Pattern Recognition"}, {"Title": "Research on the clustering algorithm of ocean big data based on self‐organizing neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1609", "JournalTitle": "Computational Intelligence"}, {"Title": "A multiple k-means clustering ensemble algorithm to find nonlinearly separable clusters", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "36", "JournalTitle": "Information Fusion"}, {"Title": "KdMutual: A novel clustering algorithm combining mutual neighboring and hierarchical approaches using a new selection criterion", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106220", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "DenMune: Density peak based clustering using mutual nearest neighbors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107589", "JournalTitle": "Pattern Recognition"}, {"Title": "A multi-stage hierarchical clustering algorithm based on centroid of tree and cut edge constraint", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "557", "Issue": "", "Page": "194", "JournalTitle": "Information Sciences"}, {"Title": "A robust clustering algorithm based on the identification of core points and KNN kernel density estimation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "195", "Issue": "", "Page": "116573", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 96257628, "Title": "Application of Computer Aided Operation in Financial Business", "Abstract": "<p>At present, there are a large number of scenarios in the process of financial business processing in China, such as manual entry, data cleaning, data aggregation, multi system operation, etc. the process is cumbersome, the operation efficiency is low, and it is unable to quickly respond to business changes and expansion. The pressure on the annual festival and monthly settlement has increased sharply, resulting in a shortage of manpower. Repetitive, unbalanced, timely, waiting, and data handling process operations can be automated to allow procedures to replace artificial, reduce the waste of human resources, improve business processing efficiency, ensure the timely completion of daily work, reduce costs and increase efficiency for enterprises, and promote their digital transformation. This paper introduces the application of computer automation RPA in financial business, the operation principle and development of automation system, and looks forward to the future development direction.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050609", "PubYear": 2022, "Volume": "5", "Issue": "6", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257632, "Title": "Research on emergency distribution problem based on graph theory algorithm", "Abstract": "<p>When unexpected events such as road block, closure and damage caused by some major events occur, they will have an impact on people's daily life. With the gradual popularization of 5g network, the application of UAV is becoming more and more extensive. This paper mainly studies the \"emergency material distribution problem in 5g network environment\". It analyzes the two cases of separate distribution of distribution vehicles and the combined distribution of \"distribution vehicles + UAVs\" respectively. In order to complete the material distribution task as soon as possible, it puts forward the best distribution scheme respectively. First, the optimal time is simplified, that is, the optimal time is converted into the shortest path, and the graph theory model is used for modeling. The path graph is highlighted by MATLAB, and the minimum spanning tree method is used to visualize all paths. After that, the shortest path from the ninth point to any point and the sequence of several traveling paths are obtained. The sequence of several traveling paths is classified into three schemes, and the total kilometers of the three schemes are calculated respectively. The best scheme is the one with the smallest total number of paths. For the best scheme, the path map of the best scheme is obtained by drawing. Secondly, the combined distribution mode of \"distribution vehicle + UAV\" is adopted for material transportation. Nowadays, 5g network is becoming more and more popular, and UAVs are more and more widely used. In order to complete the material distribution task as soon as possible, it is assumed that UAVs are used to transport materials to the greatest extent to reduce the transportation time and improve the efficiency. Visualize the path of \"distribution vehicle + UAV\" for subsequent problem solving. The aircraft route is divided into \"three consecutive sections\" and \"two consecutive sections\". After a large amount of data processing, the classification scheme is obtained. Combined with the vehicle optimal path, the best scheme of \"distribution vehicle + UAV\" combination to complete a complete distribution is obtained. Finally, the maximum load capacity of the delivery vehicle is 500 kg, and the other conditions are the same as above. Based on the obtained optimal scheme, the second question is solved by MATLAB for the constraints given in the question, and two feasible schemes are obtained. The frequency of UAV use in both schemes is greater than that of distribution vehicles. Disassemble and analyze scheme 1 and scheme 2 respectively.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050704", "PubYear": 2022, "Volume": "5", "Issue": "7", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257641, "Title": "Research on forest management plan based on forest ecosystem carbon sequestration model (FCSM) and forest value assessment model (FVM)", "Abstract": "", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050702", "PubYear": 2022, "Volume": "5", "Issue": "7", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257647, "Title": "A constrained multi-objective optimization algorithm using an efficient global diversity strategy", "Abstract": "When solving constrained multi-objective optimization problems (CMOPs), multiple conflicting objectives and multiple constraints need to be considered simultaneously, which are challenging to handle. Although some recent constrained multi-objective evolutionary algorithms (CMOEAs) have been developed to solve CMOPs and have worked well on most CMOPs. However, for CMOPs with small feasible regions and complex constraints, the performance of most algorithms needs to be further improved, especially when the feasible region is composed of multiple disjoint parts or the search space is narrow. To address this issue, an efficient global diversity CMOEA (EGDCMO) is proposed in this paper to solve CMOPs, where a certain number of infeasible solutions with well-distributed feature are maintained in the evolutionary process. To this end, a set of weight vectors are used to specify several subregions in the objective space, and infeasible solutions are selected from each subregion. Furthermore, a new fitness function is used in this proposed algorithm to evaluate infeasible solutions, which can balance the importance of constraints and objectives. In addition, the infeasible solutions are ranked higher than the feasible solutions to focus on the search in the undeveloped areas for better diversity. After the comparison tests on three benchmark cases and an actual engineering application, EGDCMO has more impressive performance compared with other constrained evolutionary multi-objective algorithms.", "Keywords": "Constrained multi-objective optimization; Evolutionary algorithm; Constraint handling; Global diversity strategy", "DOI": "10.1007/s40747-022-00851-1", "PubYear": 2023, "Volume": "9", "Issue": "2", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 5, "Name": "Jinglu Li", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 7, "Name": "Chongbo Fu", "Affiliation": "School of Marine Science and Technology, Northwestern Polytechnical University, Xi’an, China"}], "References": [{"Title": "Kriging-assisted Discrete Global Optimization (KDGO) for black-box problems with costly objective and constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106429", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-objective evolutionary clustering for large-scale dynamic community detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "549", "Issue": "", "Page": "269", "JournalTitle": "Information Sciences"}, {"Title": "A multi-stage evolutionary algorithm for multi-objective optimization with complex constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "68", "JournalTitle": "Information Sciences"}, {"Title": "Surrogate-guided multi-objective optimization (SGMOO) using an efficient online sampling strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "220", "Issue": "", "Page": "106919", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 96257651, "Title": "Government Data Resource Sharing Application System Based on Big Data Technology", "Abstract": "<p>The government affairs system based on big data technology is an advanced stage in the development of e-government. In this stage, with the help of big data platforms in the information age, through the large-scale use of modern communication equipment and the Internet, a new government management mode and management thinking are created, and then through the above way to promote information sharing. The connotation of intelligent government is information sharing, reducing government departments' office threshold to achieve information transparency and openness. Based on the above background problems, this paper analyzes the current government information management problems and the lack of resources sharing, and the problems existing in the concept of government personnel. On this basis, the current government system is analyzed. The feasibility and demand analysis of the current government system is put forward from engineering thinking to pave the way for designing the next GDRSAS(government data resource sharing application system). Finally, the design and construction of the GDRSAS are carried out, and in-depth analysis is carried out. The system can make more reasonable sharing application of government resources information under significant data background.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040818", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257653, "Title": "Construction and application of smart city development evaluation index system", "Abstract": "<p>Big data makes data sharing possible. The existing database of the government management can realize efficient interconnection, greatly improve the collaborative office capacity of all departments of the government, improve the efficiency of serving the people, and greatly reduce the cost of government management. The most important thing is to provide strong support for government decision-making. His continuous \"wisdom\" will promote the smart city to be more intelligent and more intelligent. Scientific and more efficient goal. Based on the principle of wisdom, this paper first analyzes and selects 25 important indicators from the four aspects of environment, economy, society and population to establish the indicator system of sustainable urban development; then establishes the evaluation model of urban intelligent growth, and uses the size of the comprehensive index to measure the degree of urban development wisdom. Taking Xi'an as an example, the comprehensive index of the city based on the current development plan is obtained by using the urban smart growth evaluation model, and a new development plan is made for the city by using the urban smart growth evaluation Introduction.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050204", "PubYear": 2022, "Volume": "5", "Issue": "2", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257680, "Title": "Analysis of gold and bitcoin price prediction based on LSTM model", "Abstract": "<p>As a new investment method, quantitative investment is expanding its market scale and share due to its stable investment performance. In this paper we propose a prediction model based on LSTM. It is helpful for the traders to predict the future price to formulate the best trading strategy. Using this model, we can precisely forecast each price separately to determine when the asset should be traded based on future price fluctuations. Simulation results show that our model can successfully predict the future price trend of the two assets within the acceptable range of error, which helps us to better optimize our portfolio. In addition, the RMSE (root mean square error) is selected as the loss function to describe the accuracy of our prediction model.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050614", "PubYear": 2022, "Volume": "5", "Issue": "6", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257682, "Title": "A novel assembly process guidance using augmented reality for a standalone hybrid energy system", "Abstract": "<p>The increase in the maturity level and the competitiveness of renewable energy systems, such as wind or solar-powered systems, is modifying the energy production market. Due to the possibility of decentralizing energy sources, standalone hybrid energy systems are those with the most promising potential. They generate a whole new electricity distribution network and new opportunities for socio-economic development in off-grid areas. However, several technical challenges are encountered during the deployment and operation of these systems, including transportation logistic costs, assembly, remote monitoring, performance evaluation, and real-time maintenance. Renewable energy systems must demonstrate a high degree of autonomy when operating without the supervision of an expert on-site. Furthermore, these systems must be able to communicate with their control center, sometimes located several hundreds of kilometers away. Once installed on-site, GreenCube (GC), a standalone hybrid energy technology, does not require specialized software or skilled user intervention to operate. Given the different versions of GC and the complexity of components, the assembly process plays a crucial role in the operational performance and durability of the equipment. This work applies the concept of augmented reality (AR) to provide assistance and guidance during assembly operations. It reviews the state of the art in hardware and software solutions based on AR to increase assembly precision, safety, and cadence in an industrial maintenance context. A real case scenario is described in which the user is guided step-by-step through sequences to deploy and assemble the components of a standalone hybrid energy system. Results suggest that the proposed paperless and AR-based method helps to reduce human errors during assembly operations, increase the health and safety of the assembly team, and provide insights on how to perform the tasks efficiently without the need for external documentation. The architecture can be implemented in any database management system to control relevant data generated from the assembly process and will be the basis for further research on remote maintenance approaches. </p>", "Keywords": "Augmented reality; Hybrid energy system; On-site assembly; Industrial maintenance; Industry 4.0", "DOI": "10.1007/s00170-022-10122-5", "PubYear": 2022, "Volume": "122", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departent of Mechanical Engineering, Université du Québec à Trois-Rivières, Drummondville, Canada; Institut Technologique de Maintenance Industrielle, Cégep de Sept-Îles, Sept-Îles, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institut de Recherche d’Hydro-Québec, Varennes, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Technologique de Maintenance Industrielle, Cégep de Sept-Îles, Sept-Îles, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institut Technologique de Maintenance Industrielle, Cégep de Sept-Îles, Sept-Îles, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institut Technologique de Maintenance Industrielle, Cégep de Sept-Îles, Sept-Îles, Canada"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Centre de Recherche et d’Innovation en Intelligence Énergétique, Cégep de Sept-Îles, Sept-Îles, Canada"}], "References": [{"Title": "Virtual, mixed, and augmented reality: a systematic review for immersive systems research", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "3", "Page": "773", "JournalTitle": "Virtual Reality"}, {"Title": "Augmented reality in vocational training: A systematic review of research and applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "107125", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 96257690, "Title": "Optimal joint maintenance and orienteering strategy for complex mission-oriented systems: A case study in offshore wind energy", "Abstract": "This paper introduces and solves the joint maintenance and orienteering problem with an application to offshore wind farms. The quest for sustainable energy production is fueling the growth of offshore wind electricity generation. Energy producing offshore wind turbines are typically dispersed across several remote wind farms and must be maintained and operated with high reliability levels for long time-periods separated by scheduled maintenance rotations. Due to resource constraints such as travel time, cost, and availability of repair crews, only a subset of turbines and their components can be selected for maintenance operations during maintenance trips. This paper proposes a novel joint maintenance and orienteering framework to address the selection of turbines to visit, the components to maintain, the maintenance levels to be performed, the assignment of repair crews, and their routing with the goal of minimizing total cost while satisfying a minimum required reliability threshold during the next operating mission until the next maintenance rotation. A mixed-integer linear programming optimization model is developed and fully discussed. To solve the problem for large-scale instances, a column generation method based on <PERSON><PERSON><PERSON><PERSON> decomposition and labeling algorithm is proposed. Several numerical experiments demonstrate the validity of the proposed model and the benefit of jointly optimizing maintenance and orienteering decisions. The results show that inclusion of detailed reliability and multiple maintenance levels allows better maintenance and routing decisions. Numerical results also show trade-offs between reliability requirement, shift duration and total cost.", "Keywords": "Maintenance ; Orienteering problem ; Optimization ; MILP ; Selective maintenance", "DOI": "10.1016/j.cor.2022.106020", "PubYear": 2023, "Volume": "149", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Dalhousie University, 5269 Morris Street, PO Box 15000, Halifax, Nova Scotia B3H-4R2, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Dalhousie University, 5269 Morris Street, PO Box 15000, Halifax, Nova Scotia B3H-4R2, Canada;Laboratory of Computer Engineering, Production and Maintenance, Lorraine University, Metz, France;Corresponding author at: Laboratory of Computer Engineering, Production and Maintenance, Lorraine University, Metz, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Dalhousie University, 5269 Morris Street, PO Box 15000, Halifax, Nova Scotia B3H-4R2, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Dalhousie University, 5269 Morris Street, PO Box 15000, Halifax, Nova Scotia B3H-4R2, Canada"}], "References": [{"Title": "Large-scale selective maintenance optimization using bathtub-shaped failure rates", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106876", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Multi-agent based simulation-optimization of maintenance routing in offshore wind farms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107342", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A continuous location and maintenance routing problem for offshore wind farms: Mathematical models and hybrid methods", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "144", "Issue": "", "Page": "105825", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": ********, "Title": "An Autonomous Evolutionary Approach to Planning the IoT Services Placement in the Cloud-Fog-IoT Ecosystem", "Abstract": "<p>The growth of network-based computing devices and the development of massive services are both results of the Internet of Things (IoT) by deploying Internet-connected devices at the edge. These devices are limited in terms of storage and computing, where the resources they need are provided by the cloud computing paradigm. Unfortunately, the two-tier cloud-IoT architecture is not efficient enough to provide the resources needed for latency-sensitive applications. Consequently, Fog Computing Paradigm (FCP) has been proposed to complement cloud computing and support such IoT-generated applications at the network edge. Heterogeneity, geographical distribution, and large-scale of fog nodes need the development of new methodologies for deploying and running IoT applications on fog nodes. A collection of IoT services, which have varying Quality of Service (QoS) needs, make up applications for the IoT, so finding an autonomous IoT Fog Service Placement (IoT-FSP) scheme in such an infrastructure can be challenging. The current study presents a distributed conceptual computing framework to address this problem. It is based on an autonomous approach, which improves resource management in three-tier cloud-fog-IoT architecture. Besides, we use the Cuckoo Optimization Algorithm (COA) as a meta-heuristic approach to efficiently solve the FSP. We refer to the proposed strategy as the FSP-COA. The FSP-COA formulates the problem as a multi-objective problem to reconcile various objectives, including SLA violations, service latency, response time, fog utilization, service cost, and energy consumption. Finally, the performance of FSP-COA is evaluated compared to state-of-the-art algorithms using the iFogSim simulator. According to experiments, FSP-COA is more efficient than other algorithms regarding various metrics, including latency, energy and cost, and on average, outperforms the better of existing algorithms ranging from 4% to 16%.</p>", "Keywords": "Fog computing; IoT services; Fog service placement; Cuckoo optimization algorithm; Autonomous approach", "DOI": "10.1007/s10723-022-09622-1", "PubYear": 2022, "Volume": "20", "Issue": "3", "JournalId": 6769, "JournalTitle": "Journal of Grid Computing", "ISSN": "1570-7873", "EISSN": "1572-9184", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou College of Technology and Business, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou College of Technology and Business, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou College of Technology and Business, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Robat Karim Branch, Islamic Azad University, Robat Karim, Iran"}], "References": [{"Title": "Scheduling Internet of Things requests to minimize latency in hybrid Fog–Cloud​ computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "539", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A modified water cycle evolutionary game theory algorithm to utilize QoS for IoT services in cloud-assisted fog computing environments", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "7", "Page": "5578", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Enriched Latent Dirichlet Allocation for Sentiment Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "4", "Page": "", "JournalTitle": "Expert Systems"}, {"Title": "Requirements for distributed task placement in the fog", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "", "Page": "100237", "JournalTitle": "Internet of Things"}, {"Title": "Improved many-objective particle swarm optimization algorithm for scientific workflow scheduling in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106649", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A comprehensive survey of the Grasshopper optimization algorithm: results, variants, and applications", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "19", "Page": "15533", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Resource provisioning for IoT services in the fog computing environment: An autonomic approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "109", "JournalTitle": "Computer Communications"}, {"Title": "A hybrid algorithm for the university course timetabling problem using the improved parallel genetic algorithm and local search", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "1", "Page": "467", "JournalTitle": "Applied Intelligence"}, {"Title": "An autonomous\n IoT\n service placement methodology in fog computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "5", "Page": "1097", "JournalTitle": "Software: Practice and Experience"}, {"Title": "Adopting elitism-based Genetic Algorithm for minimizing multi-objective problems of IoT service placement in fog computing environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "102972", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "An autonomous computation offloading strategy in Mobile Edge Computing: A deep learning-based hybrid approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "102974", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Toward an autonomic approach for Internet of Things service placement using gray wolf optimization in the fog computing environment", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "8", "Page": "1745", "JournalTitle": "Software: Practice and Experience"}, {"Title": "A survey on computation offloading and service placement in fog computing-based IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "2", "Page": "1983", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An Evolutionary Multi-objective Optimization Technique to Deploy the IoT Services in Fog-enabled Networks: An Autonomous Approach", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Impact of Centrality Measures on the Common Neighbors in Link Prediction for Multiplex Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "2", "Page": "138", "JournalTitle": "Big Data"}, {"Title": "A cost-efficient IoT service placement approach using whale optimization algorithm in fog computing environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "117012", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A QoS-Aware IoT Service Placement Mechanism in Fog Computing Based on Open-Source Development Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "20", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Grid Computing"}, {"Title": "Multimedia services placement algorithm for cloud–fog hierarchical environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "78", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 96257758, "Title": "State-of-the-art review of heat integrated water allocation network synthesis", "Abstract": "Heat integrated water allocation networks (HIWANs) were developed to conserve energy and water in the process industry using heat exchangers, and in some cases with water regeneration units. This paper addresses the challenges in optimizing HIWANs posed by the interactions between the water, energy, regeneration units, and heat exchangers. The optimization strategies are classified based on the adopted ideologies, and the progress in each category is discussed. A case study is examined to highlight the effect of adopting various strategies on the optimal solution. It is observed that the research progresses from the conservation of resources and operating costs to the inclusion of investment costs. More recent papers include studies that capture the trade-offs between freshwater, regeneration, utilities, and heat exchangers. The analysis of publications in different conferences and peer-reviewed journals is performed. After a detailed review of the existing work, some areas of improvement and new research perspectives are identified.", "Keywords": "Heat integrated water allocation networks ; Mathematical programming ; Pinch analysis ; Process integration ; Critical review ; ASAGA Adaptive simulated annealing and genetic algorithm ; FCL Fixed contaminant load ; FFR Fixed flow rate ; FO Fixed outlet contaminant concentration ; GA Genetic algorithm ; HEN Heat exchanger network ; HT Heat exchanger unit and area targeting ; HS Heat exchanger network synthesis ; IT Isothermal mixing ; LCC Limiting Composite Curve ; LP Linear programming ; MILP Mixed-integer linear programming ; MINLP Mixed-integer non-linear programming ; M Multiple contaminants ; MP Mathematical programming ; NI Non-isothermal mixing ; NLP Non-linear programming ; D-NLP Non-linear programming with discontinuous derivatives ; N Negligible contaminants ; NI Non-isothermal ; P With multiple properties ; Pr Process ; PA Pinch analysis ; R Regeneration unit ; RG Type of regeneration ; RR Fixed removal ratio ; S Single contaminant ; SA Simulated Annealing ; SEQ Sequential strategy ; SIM Simultaneous strategy ; TAC Total annualized cost ; TOC Total operating cost ; UT Utility Targeting ; WT Water flow rate targeting", "DOI": "10.1016/j.compchemeng.2022.108003", "PubYear": 2022, "Volume": "167", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, University of Lincoln, Lincoln LN6 7TS, United Kingdom;Department of Energy Science and Engineering, Indian Institute of Technology Bombay, Powai, Mumbai 400076, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Energy Science and Engineering, Indian Institute of Technology Bombay, Powai, Mumbai 400076, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical and Environmental Engineering/Centre of Excellence for Green Technologies, University of Nottingham Malaysia, Broga Road, 43500 Semenyih, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Chemical Engineering, College of Chemical and Biological Engineering, Zhejiang University, Hangzhou, Zhejiang 310027, P.R. China"}], "References": []}, {"ArticleId": 96257781, "Title": "Protecting Systems from Violating Constraints Using Reference Governors", "Abstract": "<p>Reference governors are add-on predictive safety supervision algorithms that monitor and modify, if it becomes necessary, commands passed to a nominal system to ensure that pointwise-in-time state and input constraints are satisfied. After briefly surveying the basics of the reference governor schemes, this paper discusses several more recent extensions of the reference governors. These include reduced order reference governors with flexible error budgeting, reference governors for nonlinear systems that exploit the logarithmic norm for response bounding, stochastic reference governors, and controller state and reference governors. Learning reference governors that are capable of handling constraints in uncertain systems are also discussed.</p>", "Keywords": "State and control constraints; Reference governors; Predictive control; Constrained control", "DOI": "10.1007/s42979-022-01374-9", "PubYear": 2022, "Volume": "3", "Issue": "6", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, The University of Michigan, Ann Arbor, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Aerospace Engineering, Auburn University, Auburn, USA"}], "References": []}, {"ArticleId": 96257790, "Title": "Research on financial irregularities identificationA machine learning perspective", "Abstract": "In the era of big data, data-driven analytics can generate many meaningful insights. With the gradual maturity of artificial intelligence (AI) algorithm, it can help solve problems that are difficult to identify in the financial field. Through theoretical analysis, this paper constructs feature engineering of multiple internal and external factors that affect corporate financial irregularities, and then automatically identifies Chinese listed companies with financial irregularities based on machine learning algorithm. In this paper, we verify the effectiveness of SMOTE algorithm in improving the imbalance data of financial irregularities of Chinese listed companies and use LightGBM algorithm to sort the ten factors of characteristic importance of financial irregularities of Chinese listed companies. This paper provides a new way to detect financial irregularities for financial regulatory authorities and a paradigm for the applying of AI in the financial field. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "China; financial irregularities identification; machine learning; SMOTE algorithm", "DOI": "10.1504/IJIMS.2022.10050659", "PubYear": 2022, "Volume": "8", "Issue": "4", "JournalId": 26851, "JournalTitle": "International Journal of Internet Manufacturing and Services", "ISSN": "1751-6048", "EISSN": "1751-6056", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Business School, Guilin University of Electronic Technology, Guangxi Province, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Guilin University of Electronic Technology, Guangxi Province, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Business School, Guilin University of Electronic Technology, Guangxi Province, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Business School, Guilin University of Electronic Technology, Guangxi Province, Guilin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Guilin University of Electronic Technology, Guangxi Province, Guilin, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Guilin University of Electronic Technology, Guangxi Province, Guilin, China"}], "References": []}, {"ArticleId": 96257831, "Title": "Tingkat Kepuasan Pengguna E-Learning Mahasiswa Pascasarjana Universitas Swasta Terbaik di Jakarta", "Abstract": "<p>Innovation learning that is relatively cheaper, easily accessible, or an integrated system as an e-learning platform in blended or hybrid. The purpose of this study was to analyze the effect of using e-learning on the satisfaction level of postgraduate students at a private university in Jakarta during the COVID-19 pandemic. Sample data obtained from postgraduate students; the number is 324 postgraduate students through online questionnaires on a google form. To conduct research testing through PLS-SEM analysis with the help of the SmartPls application version 3.0. The results of this study indicate that for H1 the original sample value is 0.158, the p-value is below 0.05, and the t-statistic is 2.791, which is greater than the t-table value of 1.962. For the results of H2, the original sample value is 0.132 and the p-value is above 0.05, and the t-statistic is 1.583 which is smaller than the t-table value of 1.962. For the H3 results, the original sample value is 0.201, the p-value is below 0.05, and the t-statistic is 2.221 which is greater than the t-table value of 1.962. So that, the overall result of this research is that the quality of the system, and the quality of information that affect the level of user satisfaction.</p>", "Keywords": "Postgraduate students;E-learning;D&M Model;Sytems;PLS-SEM", "DOI": "10.24235/itej.v7i1.97", "PubYear": 2022, "Volume": "7", "Issue": "1", "JournalId": 83875, "JournalTitle": "ITEJ (Information Technology Engineering Journals)", "ISSN": "2548-2130", "EISSN": "2548-2157", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Binus University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Binus University"}], "References": []}, {"ArticleId": 96257836, "Title": "Solving Sparse Assignment Problems on FPGAs", "Abstract": "<p>The assignment problem is a fundamental optimization problem and a crucial part of many systems. For example, in multiple object tracking, the assignment problem is used to associate object detections with hypothetical target tracks and solving the assignment problem is one of the most compute-intensive tasks. To enable low-latency real-time implementations, efficient solutions to the assignment problem is required. In this work, we present Sparse and Speculative (SaS) Auction, a novel implementation of the popular Auction algorithm for FPGAs. Two novel optimizations are proposed. First of all, the pipeline width and depth are reduced by exploiting sparsity in the input problems. Secondly, dependency speculation is employed to enable a fully pipelined design and increase the throughput. Speedups as high as 50 × are achieved relative to the state-of-the-art implementation for some input distributions. We evaluate the implementation both on randomly generated data sets and realistic data sets from multiple object tracking.</p>", "Keywords": "", "DOI": "10.1145/3546072", "PubYear": 2022, "Volume": "19", "Issue": "4", "JournalId": 10048, "JournalTitle": "ACM Transactions on Architecture and Code Optimization", "ISSN": "1544-3566", "EISSN": "1544-3973", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Norwegian University of Science and Technology, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Norwegian University of Science and Technology, Norway"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Norwegian University of Science and Technology, Norway"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Norwegian University of Science and Technology, Norway"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Norwegian University of Science and Technology, Norway"}], "References": [{"Title": "Auction-Based Algorithms for Routing and Task Scheduling in Federated Networks", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "2", "Page": "271", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "A concave optimization algorithm for matching partially overlapping point sets", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107322", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": ********, "Title": "Nonlinear Vibration Characteristics Identification of Drum - Rocker System of Shearer Based on Artificial Intelligence", "Abstract": "<p>As the core component of coal interception and coal loading, the spiral drum of coal shearer directly affects the working efficiency of coal shearer and the quality of coal. In the past research on shearing force of shearer drum, there are many problems and deficiencies in research methods and theoretical analysis. Therefore, based on the actual production conditions and the original drum model parameters, a simulation model of shearer drum cutting coal wall is established to simulate the dynamic process of coal cutting, and the research on the nonlinear vibration performance of the shearer rocker system affects its stability. The key issue of the performance and service life is therefore important to study the nonlinear vibration characteristics of the rocker system. This paper mainly studies the nonlinear vibration characteristics of the rocker system. In this paper, based on ANSYS software, the model analysis of the rocker arm shell and the traction part shell is carried out. Harmonic response analysis was carried out on the housing of the traction part, and its response under sinusoidal excitation of different frequencies was obtained. The lateral vibration acceleration and swing angle acceleration of the shearer body are 0.0098 and 0.0074 , respectively. The larger the average cutting impedance of coal rock, the greater the lateral acceleration of each part of the shearer under the oblique cutting condition, the greater the fluctuation, and the direction changes with time. The average cutting impedance of coal rock has a great influence on the front drum and front rocker of the shearer. The experimental results show that the study of nonlinear vibration characteristics provides a reference for the optimization of the shearer's structural strength and the improvement of its resistance to shock and vibration.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050211", "PubYear": 2022, "Volume": "5", "Issue": "2", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257850, "Title": "Research on Unmanned Obstacle Recognition Based on Computer Vision", "Abstract": "<p>With the deepening of sensing technology and deep learning, unmanned driving technology has been greatly developed. The purpose of this paper is to sort out the development status of unmanned obstacle recognition, summarize the technical points of obstacle recognition in the field of computer vision, and summarize the problems and defects of existing obstacle recognition technology and put forward relevant development suggestions.<br/></p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050207", "PubYear": 2022, "Volume": "5", "Issue": "2", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257852, "Title": "Research on Fault Diagnosis of Rotating Equipment Based on Artificial Intelligence", "Abstract": "<p>Rotating equipment is widely used in large industrial and mining enterprises, so its safe and stable operation is of great significance. With rapid development of artificial intelligence algorithms in recent years, many researchers apply them to the fault diagnosis of rotating machinery and equipment. This paper takes a company’s rotating equipment as an object, discusses artificial intelligence algorithms suitable for remote fault diagnosis. Then, taking circulating water pump as an example, it summarizes the characteristic vectors, monitoring methods and fault types of circulating water pump. Based on operating status and characteristic parameters of the equipment management system accessed to circulating water pump, it designs four types of typical artificial intelligence algorithm models, and assesses its accuracy and effects through simulation software. In this way, it supports rapid diagnosis and analysis of the existing faults, points out the cause of the fault in time, effectively reduces the number of unit shutdowns for maintenance, extends the cycle of unit operation, and provides reliable guarantees for the safe operation of energy equipment.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050201", "PubYear": 2022, "Volume": "5", "Issue": "2", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96257892, "Title": "HydroLang: An open-source web-based programming framework for hydrological sciences", "Abstract": "This paper introduces HydroLang, an open-source and integrated community-driven computational web framework for hydrology and water resources research and education. HydroLang employs client-side web technologies and standards to carry out various routines aimed at acquiring, managing, transforming, analyzing, and visualizing hydrological datasets. HydroLang consists of four major high-cohesion low-coupling modules: (1) retrieving, manipulating, and transforming raw hydrological data, (2) statistical operations, hydrological analysis, and model creation, (3) generating graphical and tabular data representations, and (4) mapping and geospatial data visualization. To demonstrate the framework&#x27;s capabilities, portability, and interoperability, two detailed case studies (assessment of lumped models and construction of a rainfall disaggregation model) have been presented. HydroLang&#x27;s unique modular architecture and open-source nature allow it to be easily tailored into any use case and web framework, and it encourages iterative enhancements with community involvement to establish the comprehensive next-generation hydrological software toolkit.", "Keywords": "Scientific visualization ; Hydrological analysis ; Software libraries ; Web frameworks ; Neural networks", "DOI": "10.1016/j.envsoft.2022.105525", "PubYear": 2022, "Volume": "157", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Brandenburg University of Technology Cottbus-Seftenberg, Cottbus, Germany;IIHR – Hydroscience & Engineering, University of Iowa, Iowa City, IA, USA;Civil and Environmental Engineering, University of Iowa, Iowa City, IA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IIHR – Hydroscience & Engineering, University of Iowa, Iowa City, IA, USA;Corresponding author.. IIHR – Hydroscience & Engineering, University of Iowa, 300 S. Riverside Dr., Iowa City, IA, 52246, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Brandenburg University of Technology Cottbus-Seftenberg, Cottbus, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IIHR – Hydroscience & Engineering, University of Iowa, Iowa City, IA, USA;Civil and Environmental Engineering, University of Iowa, Iowa City, IA, USA"}], "References": []}, {"ArticleId": 96257938, "Title": "Multiple instance relation graph reasoning for cross-modal hash retrieval", "Abstract": "The similarity calculation is too simple in most cross-modal hash retrieval methods, which do not consider the impact of the relations between instances. To solve this problem, this paper proposes a reasoning method based on multiple instance relation graphs. By constructing similarity matrices, we establish global and local instance relation graphs, which fully exploit fine-grained relations between instances. First, we perform relation reasoning based on the relation graphs of the image and text modalities; then, we map the relations within the two modalities into the instance graphs; finally, we perform relation reasoning based on the instance graphs. Furthermore, to accommodate the features of both the image and text modalities, we employ a step-by-step training strategy to train the proposed neural network model. According to the results of experiments on the MIRFlickr and NUS-WIDE datasets, our method has apparent advantages in terms of m A P and has a good effect on the topK-precision curve. This shows that our method realizes the in-depth mining of instance relations, which can improve the retrieval performance significantly.", "Keywords": "Relation graph reasoning ; Cross-modal hash retrieval ; Similarity matrix ; K-nearest neighbor ; Step-by-step training strategy", "DOI": "10.1016/j.knosys.2022.109891", "PubYear": 2022, "Volume": "256", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining and Security, Guangxi Normal University, Guilin 541004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining and Security, Guangxi Normal University, Guilin 541004, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining and Security, Guangxi Normal University, Guilin 541004, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Lab of Multi-source Information Mining and Security, Guangxi Normal University, Guilin 541004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Northwest Normal University, Lanzhou 730070, China"}], "References": [{"Title": "Learning latent hash codes with discriminative structure preserving for cross-modal retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "1", "Page": "283", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "DRSL: Deep Relational Similarity Learning for Cross-modal Retrieval", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "298", "JournalTitle": "Information Sciences"}, {"Title": "Boost image captioning with knowledge reasoning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "12", "Page": "2313", "JournalTitle": "Machine Learning"}, {"Title": "Multi-attention based semantic deep hashing for cross-modal retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "8", "Page": "5927", "JournalTitle": "Applied Intelligence"}, {"Title": "NSDH: A Nonlinear Supervised Discrete Hashing framework for large-scale cross-modal retrieval", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "217", "Issue": "", "Page": "106818", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "High-order nonlocal Hashing for unsupervised cross-modal retrieval", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "2", "Page": "563", "JournalTitle": "World Wide Web"}, {"Title": "CHOP: An orthogonal hashing method for zero-shot cross-modal retrieval", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "247", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Hierarchical semantic interaction-based deep hashing network for cross-modal retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Deep robust multilevel semantic hashing for multi-label cross-modal retrieval", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108084", "JournalTitle": "Pattern Recognition"}, {"Title": "Learning a maximized shared latent factor for cross-modal hashing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107252", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Clustering-driven Deep Adversarial Hashing for scalable unsupervised cross-modal retrieval", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "459", "Issue": "", "Page": "152", "JournalTitle": "Neurocomputing"}, {"Title": "Self-attention and adversary learning deep hashing network for cross-modal retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "93", "Issue": "", "Page": "107262", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "DHLBT: Efficient Cross-Modal Hashing Retrieval Method Based on Deep Learning Using Large Batch Training", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "7", "Page": "949", "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering"}, {"Title": "Discrete matrix factorization hashing for cross-modal retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "3023", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Matching images and texts with multi-head attention network for cross-media hashing retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104475", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Unsupervised hash retrieval based on multiple similarity matrices and text self-attention mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "7", "Page": "7670", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 96257996, "Title": "Design and Implementation of Control System for Submersible AGV", "Abstract": "<p>In this essay, a PID trajectory tracking algorithm combining the lateral position with the yaw Angle is proposed, which is also implemented in the insert AGV control system, so as to meet the demand of a certain automobile manufacturer in the outdoor use of AGV to automatically move new cars rolling off the assembly line to the parking lot. According to the positioning information of RTK-GPS, the AGV control system takes the vehicle-mounted coordinate system as the control reference and uses the PID algorithm combining the lateral position and yaw Angle, thus realizing the trajectory tracking. Through numerous tests of real vehicles under various environments, the positioning accuracy and orientation accuracy of AGV in the process of picking up the vehicle reach ±25mm and ±0.1°respectively. Being deployed in the AGV trailer, the control algorithm with the dispatch of the scheduling system can take fixed-point pickup, track tracking, vehicle release and other actions, and can run for a long time with high precision and stability.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050314", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258012, "Title": "The skinning in character animation: A survey", "Abstract": "<p>The skinning in the character animation is always one of the vital research contents in computer animation. In recent years, with the rapid development of the 3D game, animation, and film special effects production industry, the requirements for character skin technology have gradually increased, and the innovative development of skin technology has also received more and more attention from researchers, resulting in the birth of many new methods and technologies. This article focuses on analyzing different methods of skinning technology, which can be roughly divided into four categories: Direct Skinning Methods, Automatic Skinning, Example-based Shape Deformation, and Direct Delta Skinning.  The contents and details of each method are listed, and the advantages and disadvantages of the above methods are compared and analyzed. Finally, aiming at the shortcomings in the current work, some problems that can be further studied are proposed.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050402", "PubYear": 2022, "Volume": "5", "Issue": "4", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258020, "Title": "Adaptive Image Enhancement Method Based on Gamma Correction", "Abstract": "<p>To address the problems of local detail loss and sharpness degradation in traditional image enhancement algorithms under low illumination conditions, an image enhancement algorithm based on the <PERSON> law is proposed. The adaptive gamma correction function and the adaptive contrast enhancement function are used to reduce the noise interference of the original image, the RGB mode of the original image is converted to HSV mode to improve the overall visual comfort of the image, the classical adaptive correction algorithm is optimised to separate the luminance components into blocks and obtain two images; Finally, the image fusion technique is used to extract the details from the two images and synthesise the final image. The images enhanced are clearer, brighter and more natural than the classical algorithms. </p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050412", "PubYear": 2022, "Volume": "5", "Issue": "4", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258038, "Title": "Network Security Technology Based on Cloud Computing", "Abstract": "<p>With the rapid development of computers and the Internet, cloud computing network technology is widely used in all aspects of the world with its powerful data processing capabilities. While it promotes people's convenient life, it also faces many security issues. Aiming at the network security risks and threats under cloud computing, briefly describe cloud computing technology, analyze the types and characteristics of network security under cloud computing, and clarify the importance of network security under cloud computing. Research and analyze the implementation strategy and development direction of network security technology under cloud computing, in order to promote the development of cloud computing network security technology in the future.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050413", "PubYear": 2022, "Volume": "5", "Issue": "4", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258170, "Title": "Graph neural networks and cross-protocol analysis for detecting malicious IP addresses", "Abstract": "An internet protocol (IP) address is the foundation of the Internet, allowing connectivity between people, servers, Internet of Things, and services across the globe. Knowing what is connecting to what and where connections are initiated is crucial to accurately assess a company’s or individual’s security posture. IP reputation assessment can be quite complex because of the numerous services that may be hosted on that IP address. For example, an IP might be serving millions of websites from millions of different companies like web hosting companies often do, or it could be a large email system sending and receiving emails for millions of independent entities. The heterogeneous nature of an IP address typically makes it challenging to interpret the security risk. To make matters worse, adversaries understand this complexity and leverage the ambiguous nature of the IP reputation to exploit further unsuspecting Internet users or devices connected to the Internet. In addition, traditional techniques like dirty-listing cannot react quickly enough to changes in the security climate, nor can they scale large enough to detect new exploits that may be created and disappear in minutes. In this paper, we introduce the use of cross-protocol analysis and graph neural networks (GNNs) in semi-supervised learning to address the speed and scalability of assessing IP reputation. In the cross-protocol supervised approach, we combine features from the web, email, and domain name system (DNS) protocols to identify ones which are the most useful in discriminating suspicious and benign IPs. In our second experiment, we leverage the most discriminant features and incorporate them into the graph as nodes’ features. We use GNNs to pass messages from node to node, propagating the signal to the neighbors while also gaining the benefit of having the originating nodes being influenced by neighboring nodes. Thanks to the relational graph structure we can use only a small portion of labeled data and train the algorithm in a semi-supervised approach. Our dataset represents real-world data that is sparse and only contain a small percentage of IPs with verified clean or suspicious labels but are connected. The experimental results demonstrate that the system can achieve $$85.28\\%$$ \n \n 85.28 \n % \n \n accuracy in detecting malicious IP addresses at scale with only $$5\\%$$ \n \n 5 \n % \n \n of labeled data.", "Keywords": "Graph neural networks;IP reputation;Machine learning;Network security", "DOI": "10.1007/s40747-022-00838-y", "PubYear": 2023, "Volume": "9", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hillsboro, OR 97229 USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Brentwood, CA 94513 USA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "West Lakeland, MN 55082 USA."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Lubbock, TX 79403 USA."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hillsboro, OR 97229 USA."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Elizabeth, CO 80107 USA."}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Lilburn, GA 30047 USA."}], "References": [{"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}]}, {"ArticleId": 96258179, "Title": "The Integration of Blockchain and Artificial Intelligence for a Smart City", "Abstract": "<p>Smart city can be transformed into a smart society by adopting emerging technologies. This paper provides a comprehensive literature review on the security issues affecting the deployment of smart city blockchain systems. This paper discusses solutions of blockchain security enhancement in detail and summarizes key points that can be used to develop various block-based intelligent management systems. In addition, future research directions are explored, including new security recommendations and future guidelines for sustainable smart city ecosystems. </p>", "Keywords": "", "DOI": "10.25236/AJCIS.2021.040807", "PubYear": 2021, "Volume": "4", "Issue": "8", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258254, "Title": "Design and implementation of a three-port DC-DC converter", "Abstract": "<p>In order to resolve the problems of low efficiency and poor stability of traditional renewable energy systems, a three-port DC-DC converter for photovoltaic (PV) power systems and its control method are proposed in this paper. Firstly, the operating principles of the three-port DC-DC converter are analyzed in detail, and then, its topology are described. The proposed converter features simple topology, thus leading to high efficiency. Furthermore, the control method for the converter has been proposed for realizing constant voltage closed-loop control and maximum power point tracking (MPPT). Its operation can be transited mode automatically according to the photovoltaic power. Finally, the experimental data are given to verify the high efficiency and the feasibility of the converter.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050601", "PubYear": 2022, "Volume": "5", "Issue": "6", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258258, "Title": "The Application of Computer-Assisted Corrective Feedback in Chinese Learning Environment", "Abstract": "<p>Corrective feedback plays in critical role in second language learning and facilitates the learners’ uptake of a linguistic structure. The study seeks to explore the teachers’ perception towards corrective feedback for foreign students and attempts to find the feasibility of carrying out computer-assisted corrective feedback in China’s tertiary institutes. A sample of 38 Chinese language instruc-tors and that of 39 foreign students were randomly selected respectively. For the teachers a question-naire and a post hoc interview had been conducted and the finding thereby shows that the 36.8% of interviewees can accept computer-assisted corrective feedback. For the students, a three-month teach-ing practice targeting Chinese wh-question learning has been conducted between the control group (n=19) and the experiment group (n=20). A pre-test, two post-tests on grammaticality judgments had been conducted, and the results demonstrate that there is significant change between two groups. The implication of the present study is ease the tension between the shortage of language instructors and the increase of foreign students in China. </p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050611", "PubYear": 2022, "Volume": "5", "Issue": "6", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258283, "Title": "Improved image noise level estimation based on segmentation and block processing", "Abstract": "<p>Accurate estimation of noise parameters in digital images is of great significance to improve the quality of image processing. Principal component analysis is an important means of image denoising, and the traditional method is to estimate the whole image. Due to the complexity of image content, this paper proposes a preprocessing method based on superpixel segmentation to process the largest possible smooth block in the image. Compared with contrast method, the estimated value of the proposed method is closer to the true value.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050606", "PubYear": 2022, "Volume": "5", "Issue": "6", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258297, "Title": "UAV Land Classification Method Based on Federated Learning", "Abstract": "<p>In the application of unmanned aerial vehicle (UAV), land classification is an important field. Image recognition technology based on deep learning is a new method to solve the problem of land classification in recent years. In this paper, we propose a land classification method based on federated learning (FL) which uses Fedavg-Adam algorithm. This method enables UAVs to learn land models using training data distributed in their local database. By aggregating local computational updates of the land classification model, a shared global model is constructed. UAVs can collectively benefit from global models without sharing datasets and protect sensitive information collected by UAVs. To obtain good classification performance, we further propose an improved CNN network. The experimental results show that the improved CNN network suitable for federated learning has the good performance considering the influence of time and accuracy. In the RSSCN7 Dataset, the FedAvg-Adam algorithm converges in the 43rd rounds with an accuracy rate of 82.71%. Compared with FedAvg and FedAdam, the final accuracy of the three is similar, but the latter two converge at 56 rounds and 114 rounds respectively, and Fedavg-Adam has the fastest speed, which proves the superiority of our method.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.050712", "PubYear": 2022, "Volume": "5", "Issue": "7", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96258351, "Title": "Entropy and likelihood-based detection of DGA generated domain names and their families", "Abstract": "Botnet is a network of hosts (bots) infected by a common malware and controlled by command and control (C&C) servers. Once the malware is found in an infected host, it is easy to get the domain of its C&C server and block it. To counter such detection, many malware families use probabilistic algorithms, known as domain generation algorithms (DGAs), to generate domain names for the C&C servers. In this paper, we propose a probabilistic approach to identify the domain names that are likely to be generated by malware using DGAs. The proposed solution is based on the hypothesis that the entropy of human-generated domain names should be lesser than the entropy of DGA generated domain names. Results show that the percentage of false negatives in the detection of DGA generated domain names using the proposed method is less than 29% across 39 DGA families considered by us in our experimentation. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "botnets; C&C server; command and control servers; domain generations algorithms; domain name system", "DOI": "10.1504/IJSN.2022.125512", "PubYear": 2022, "Volume": "17", "Issue": "3", "JournalId": 15172, "JournalTitle": "International Journal of Security and Networks", "ISSN": "1747-8405", "EISSN": "1747-8413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, BITS, Rajasthan, Pilani, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Artificial Intelligence and Robotics (CAIR), DRDO, Karnataka, Bangalore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, <PERSON><PERSON><PERSON> National Institute of Technology, Madhya Pradesh, Bhopal, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, BITS, Rajasthan, Pilani, India"}], "References": []}, {"ArticleId": 96258366, "Title": "Self-supervised endoscopic image key-points matching", "Abstract": "Feature matching and finding correspondences between endoscopic images is a key step in many clinical applications such as patient follow-up and generation of panoramic image from clinical sequences for fast anomalies localization. Nonetheless, due to the high texture variability present in endoscopic images, the development of robust and accurate feature matching becomes a challenging task. Recently, deep learning techniques which deliver learned features extracted via convolutional neural networks (CNNs) have gained traction in a wide range of computer vision tasks. However, they all follow a supervised learning scheme where a large amount of annotated data is required to reach good performances, which is generally not always available for medical data databases. To overcome this limitation related to labeled data scarcity, the self-supervised learning paradigm has recently shown great success in a number of applications. This paper proposes a novel self-supervised approach for endoscopic image matching based on deep learning techniques. When compared to standard hand-crafted local feature descriptors, our method outperformed them in terms of precision and recall. Furthermore, our self-supervised descriptor provides a competitive performance in comparison to a selection of state-of-the-art deep learning based supervised methods in terms of precision and matching score.", "Keywords": "Self-supervised learning ; Feature matching ; Endoscopic images ; Deep learning ; Image key-points matching", "DOI": "10.1016/j.eswa.2022.118696", "PubYear": 2023, "Volume": "213", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre de Recherche en Numérique de Sfax, Technopôle de Sfax, 3021 Sfax, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>-Chouayakh", "Affiliation": "Centre de Recherche en Numérique de Sfax, Technopôle de Sfax, 3021 Sfax, Tunisia;Laboratory of Signals, Systems, Artificial Intelligence and Networks, Technopôle de Sfax, 3021 Sfax, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre de Recherche en Numérique de Sfax, Technopôle de Sfax, 3021 Sfax, Tunisia;Laboratory of Signals, Systems, Artificial Intelligence and Networks, Technopôle de Sfax, 3021 Sfax, Tunisia;Corresponding author at: Centre de Recherche en Numérique de Sfax, Technopôle de Sfax, 3021 Sfax, Tunisia"}], "References": [{"Title": "Learning local representations for scalable RGB-D face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "113319", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Image Matching from Handcrafted to Deep Features: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "23", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 96258377, "Title": "Model-based hybrid dynamic event-triggered control for systems subject to DoS attacks: A hybrid system approach", "Abstract": "This article addresses the event-triggered control problem for networked control systems with uncertainties and denial-of-service (DoS) attacks. A resilient model-based hybrid dynamic event-triggered mechanism (HDETM) is proposed to defend against DoS attacks and reduce communication in sensor-controller-actuator networks. Owing to the introduction of regularization variables, Zeno behavior is naturally excluded, even if there exist disturbances. By using model-based computing, the novel HDETM outperforms the existing event-triggered mechanisms in terms of network utilization and robustness. To model and analyze the proposed HDETM, we propose a novel framework based on hybrid systems, which is different from the traditional discrete-time system framework. Meanwhile, the exponential stability and the L 2 -gain performance of the closed-loop system are guaranteed by utilizing the stability theorems for hybrid systems. In addition to the centralized model-based HDETM, a decentralized HDETM is also provided for large-scale systems which are composed of multiple interconnected subsystems. Finally, two examples are given that verify the improvement of the proposed model-based HDETM.", "Keywords": "", "DOI": "10.1016/j.ins.2022.08.125", "PubYear": 2022, "Volume": "613", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Lab of Industrial Computer Control Engineering of Hebei Province, Yanshan University, Qinhuangdao 066004, China;Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao 066004, China"}, {"AuthorId": 2, "Name": "Fu<PERSON><PERSON>", "Affiliation": "Key Lab of Industrial Computer Control Engineering of Hebei Province, Yanshan University, Qinhuangdao 066004, China;Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao 066004, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of Industrial Computer Control Engineering of Hebei Province, Yanshan University, Qinhuangdao 066004, China;Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao 066004, China"}, {"AuthorId": 4, "Name": "Can<PERSON> Wang", "Affiliation": "Key Lab of Industrial Computer Control Engineering of Hebei Province, Yanshan University, Qinhuangdao 066004, China;Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao 066004, China"}], "References": [{"Title": "A dynamic event-triggered resilient control approach to cyber-physical systems under asynchronous DoS attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "519", "Issue": "", "Page": "260", "JournalTitle": "Information Sciences"}, {"Title": "Event-triggered predictive control for networked control systems with DoS attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "542", "Issue": "", "Page": "71", "JournalTitle": "Information Sciences"}, {"Title": "Resilient observer-based control for cyber-physical systems under denial-of-service attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "545", "Issue": "", "Page": "102", "JournalTitle": "Information Sciences"}, {"Title": "Distributed predictor-based stabilization of interconnected systems with network induced delays", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "564", "Issue": "", "Page": "368", "JournalTitle": "Information Sciences"}, {"Title": "Predictive control based on event-triggering mechanism of cyber-physical systems under denial-of-service attacks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "586", "Issue": "", "Page": "294", "JournalTitle": "Information Sciences"}, {"Title": "All state constrained decentralized adaptive implicit inversion control for a class of large scale nonlinear hysteretic systems with time-delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "588", "Issue": "", "Page": "52", "JournalTitle": "Information Sciences"}, {"Title": "Event-triggered remote state estimation for cyber-physical systems under malicious DoS attacks", "Authors": "Yuan<PERSON><PERSON>; G<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "602", "Issue": "", "Page": "43", "JournalTitle": "Information Sciences"}, {"Title": "Observer-based asynchronous event-triggered control for interval type-2 fuzzy systems with cyber-attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "606", "Issue": "", "Page": "805", "JournalTitle": "Information Sciences"}, {"Title": "Event-based distributed secondary voltage tracking control of microgrids under DoS attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "1572", "JournalTitle": "Information Sciences"}, {"Title": "Sampled-data-based consensus of multi-agent systems with multiplicative noise and time-delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "1621", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96258397, "Title": "Novel technique for caries detection using curvilinear semantic deep convolutional neural network", "Abstract": "<p>Radiography image processing is a technique used for processing radiography images using mathematical operations in which the input is a dental X- ray image or a sequence of X- ray images. The accuracy related segmentation for tooth images forms key point in computer based algorithms. To the great extent image processing techniques has two dimensional images used for processing. The methods of dental X-ray image diagnostic procedure are well established in the dentistry field. This is very useful to the dentist to get extra diagnostic information. Typically, in dental X-ray images, detection of caries and other hard tissues are challenging tasks. These x-ray images have unwanted noises that lead to poor diagnostic information. The main aim of proposed dental image processing system is to remove the unwanted noises at first with the help of robust Hybrid Binary Thresholding with Notch Filter (HBT-NF). The next step with the segmentation of caries, hard tissues from tooth lies with proposed Curvilinear Semantic Deep Convolutional neural network. Finally the teeth are separated from hard tissues and caries at high accuracy of 93.7%. Experimental results on the real dental X-ray images of the proposed system will give better effectiveness compared with other detection methods.</p>", "Keywords": "Dental X-ray image; Segmentation; Notch filter", "DOI": "10.1007/s11042-022-13789-w", "PubYear": 2023, "Volume": "82", "Issue": "7", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Anna University, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, University College of Engineering, Nagercoil, India"}], "References": [{"Title": "Semi-supervised OTSU based hyperbolic tangent Gaussian kernel fuzzy C-mean clustering for dental radiographs segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "3-4", "Page": "2745", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 96258408, "Title": "Acoustic scene classification using projection Kervolutional neural network", "Abstract": "<p>In this paper, a novel Projection Kervolutional Neural Network (ProKNN) is proposed for Acoustic Scene Classification (ASC). ProKNN is a combination of two special filters known as the left and right projection layers and Kervolutional Neural Network (KNN). KNN replaces the linearity of the Convolutional Neural Network (CNN) with a non-linear polynomial kernel. We extend the ProKNN to learn from the features of two channels of audio recordings in the initial stage. The performance of the ProKNN is evaluated on the two publicly available datasets: TUT Urban Acoustic Scenes 2018 and TUT Urban Acoustic Scenes Mobile 2018 development datasets. Results show that the proposed ProKNN outperforms the existing systems with an absolute improvement of accuracy of 8% and 14% on TUT Urban Acoustic Scenes 2018 and TUT Urban Acoustic Scenes Mobile 2018 development datasets respectively, as compared to the baseline model of Detection and Classification of Acoustic Scene and Events (DCASE) - 2018 challenge.</p>", "Keywords": "Projection Kervolutional Neural Network (ProKNN); Projection layers; Kervolutional Neural Network (KNN); Acoustic Scene Classification (ASC)", "DOI": "10.1007/s11042-022-13763-6", "PubYear": 2023, "Volume": "82", "Issue": "6", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Manipal Institute of Technology, Manipal Academy of Higher Education, Manipal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology Karnataka, Surathkal, India"}], "References": []}, {"ArticleId": 96258519, "Title": "Online computation offloading with double reinforcement learning algorithm in mobile edge computing", "Abstract": "Smart mobile devices have recently emerged as a promising computing platform for computation tasks. However, the task performance is restricted by the computing power and battery capacity of mobile devices. Mobile edge computing, an extension of cloud computing, solves this problem well by providing computational support to mobile devices. In this paper, we discuss a mobile edge computing system with a server and multiple mobile devices that need to perform computation tasks with priorities. The limited resources of the mobile edge computing server and mobile device make it challenging to develop an offloading strategy to minimize both delay and energy consumption in the long term. To this end, an online algorithm is proposed, namely, the double reinforcement learning computation offloading (DRLCO) algorithm, which jointly decides the offloading decision, the CPU frequency, and transmit power for computation offloading. Concretely, we first formulate the power scheduling problem for mobile users to minimize energy consumption. Inspired by reinforcement learning, we solve the problem by presenting a power scheduling algorithm based on the deep deterministic policy gradient (DDPG). Then, we model the task offloading problem to minimize the delay of tasks and propose a double Deep Q-networks (DQN) based algorithm. In the decision-making process, we fully consider the influence of task queue information, channel state information, and task information. Moreover, we propose an adaptive prioritized experience replay algorithm to improve the model training efficiency. We conduct extensive simulations to verify the effectiveness of the scheme, and the simulation results show that compared with the conventional schemes, our method reduces the delay by 48% and the energy consumption by 53%.", "Keywords": "Mobile edge computing ; Power control ; Computation offloading ; Deep deterministic policy gradient ; Double Deep Q-Networks", "DOI": "10.1016/j.jpdc.2022.09.006", "PubYear": 2023, "Volume": "171", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [{"AuthorId": 1, "Name": "Lin<PERSON>", "Affiliation": "School of Informatics / Shenzhen Research Institute, Xiamen University, Xiamen/Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics / Shenzhen Research Institute, Xiamen University, Xiamen/Shenzhen, China;School of Mathematics and Information Engineering, Longyan University, Longyan, China;Corresponding author at: School of Informatics / Shenzhen Research Institute, Xiamen University, Xiamen/Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Automation, School of Aerospace Engineering, Xiamen University, Xiamen, China"}, {"AuthorId": 4, "Name": "Wenhua Zeng", "Affiliation": "School of Informatics / Shenzhen Research Institute, Xiamen University, Xiamen/Shenzhen, China"}], "References": [{"Title": "Offloading dependent tasks in multi-access edge computing: A multi-objective reinforcement learning approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "333", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 96258526, "Title": "Sentiment analysis in cross-linguistic context: How can machine translation influence sentiment classification?", "Abstract": "<p>In recent years, there has been a lot of interest in cross-language sentiment classification, as the research in sentiment analysis has shifted focus from English to less resourceful languages. Cross-language sentiment classification attempts to leverage the automated machine translation (MT) capability utilizing the infrastructure of languages rich in linguistic resources, mainly English, to help build sentiment analysis systems for low-resource languages. In this study, we explore how MT influences cross-language sentiment classification. To this end, we perform three different experiments, obtaining promising results. In the first experiment, we automatically translate 4,000 positive and negative reviews from English into Greek and Italian, thus obtaining labeled sentiment datasets in these languages. Then, we train a Naive Bayes classifier and compare the performance with the source dataset. In the second experiment, the translated reviews are automatically translated back into the source language (English), aiming to compare the classification accuracy with the one obtained in the original dataset. In the final approach, the reviews are translated from the source (English) into Italian through an intermediate translation in Greek to examine whether the performance was further diminished compared with the approach of the first experiment.</p>", "Keywords": "", "DOI": "10.1093/llc/fqac053", "PubYear": 2023, "Volume": "38", "Issue": "1", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Italian Language and Literature, School of Philosophy, National and Kapodistrian University of Athens , Athens, Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Humanities and Social Sciences, <PERSON>ad <PERSON> University , Doha, Qatar"}], "References": [{"Title": "A comparative study of machine translation for multilingual sentence-level sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1078", "JournalTitle": "Information Sciences"}, {"Title": "A survey on sentiment analysis methods, applications, and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5731", "JournalTitle": "Artificial Intelligence Review"}]}]