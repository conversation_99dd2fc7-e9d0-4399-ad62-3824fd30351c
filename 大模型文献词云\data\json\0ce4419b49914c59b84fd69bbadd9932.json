[{"ArticleId": 86617449, "Title": "Humidity compensation based on power-law response for MOS sensors to VOCs", "Abstract": "The compensation model for humidity is proposed for Metal oxide semiconductor (MOS) sensors to respond to volatile organic compounds (VOCs) vapor. There are four frequently-used sensors were investigated for the detection of three typical VOCs, namely, acetone, ethanol and methanol. In this study, water vapor was treated as a reactant involved in the response, then a model is proposed based on the power law response. As the resistance of sensor and humidity enter the model, the detected gas concentration will be output. The effect of model is well verified on the experimental system. Even the model was applied to electronic nose to improve the recognition results. It indicates that our method is of great value to system that needs to remove the interference of water vapor.", "Keywords": "MOS gas sensor ; Humidity compensation ; Pattern recognition", "DOI": "10.1016/j.snb.2021.129601", "PubYear": 2021, "Volume": "334", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Electronic Materials and Devices, School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, 300401, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tianjin Key Laboratory of Electronic Materials and Devices, School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, 300401, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Electronic Materials and Devices, School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, 300401, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Electronic Materials and Devices, School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, 300401, China"}, {"AuthorId": 5, "Name": "Wentao Sun", "Affiliation": "Tianjin Key Laboratory of Electronic Materials and Devices, School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, 300401, China"}, {"AuthorId": 6, "Name": "Jin<PERSON> Zhang", "Affiliation": "Tianjin Key Laboratory of Electronic Materials and Devices, School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, 300401, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Electronic Materials and Devices, School of Electronics and Information Engineering, Hebei University of Technology, Tianjin, 300401, China"}], "References": []}, {"ArticleId": 86617454, "Title": "Reconciling interoperability with efficient Verification and Validation within open source simulation environments", "Abstract": "A Cyber–Physical System (CPS) comprises physical as well as software subsystems. Simulation-based approaches are typically used to support design and Verification and Validation (V&V) of CPSs in several domains such as: aerospace, defence, automotive, smart grid and healthcare. Accordingly, many simulation-based tools are available to support CPS design. This, on one side, enables designers to choose the toolchain that best suits their needs, on the other side poses huge interoperability challenges when one needs to simulate CPSs whose subsystems have been designed and modelled using different toolchains. To overcome such an interoperability problem, in 2010 the Functional Mock-up Interface (FMI) has been proposed as an open standard to support both Model Exchange (ME) and Co-Simulation (CS) of simulation models created with different toolchains. FMI has been adopted by several modelling and simulation environments. Models adhering to such a standard are called Functional Mock-up Units (FMUs). Indeed FMUs play an essential role in defining complex CPSs through, e.g. , the System Structure and Parametrisation (SSP) standard. Simulation-based V&V of CPSs typically requires exploring different simulation scenarios ( i.e. , exogenous input sequences to the CPS under design). Many such scenarios have a shared prefix. Accordingly, to avoid simulating many times such shared prefixes, the simulator state at the end of a shared prefix is saved and then restored and used as a start state for the simulation of the next scenario. In this context, an important FMI feature is the capability to save and restore the internal FMU state on demand. This is crucial to increase efficiency of simulation-based V&V. Unfortunately, the implementation of this feature is not mandatory and it is available only within some commercial software. As a result, the interoperability enabled by the FMI standard cannot be fully exploited for V&V when using open-source simulation environments. This motivates developing such a feature for open-source CPS simulation environments. Accordingly, in this paper, we focus on JModelica, an open-source modelling and simulation environment for CPSs based on an open standard modelling language, namely Modelica. We describe how we have endowed JModelica with our open-source implementation of the FMI 2.0 functions needed to save and restore internal states of FMUs for ME. Furthermore, we present experimental results evaluating, through 934 benchmark models, correctness and efficiency of our extended JModelica. Our experimental results show that simulation-based V&V is, on average, 22 times faster with our get/set functionality than without it.", "Keywords": "Simulation ; Verification and Validation ; Interoperability ; FMI/FMU ; Model Exchange ; Cyber–Physical Systems", "DOI": "10.1016/j.simpat.2021.102277", "PubYear": 2021, "Volume": "109", "Issue": "", "JournalId": 1087, "JournalTitle": "Simulation Modelling Practice and Theory", "ISSN": "1569-190X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Sapienza University of Rome, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, Sapienza University of Rome, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Sapienza University of Rome, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Sapienza University of Rome, Italy"}], "References": []}, {"ArticleId": 86617553, "Title": "Prime Fuzzy Bi-Ideals in Near-Subtraction Semigroups", "Abstract": "<p>A study on fuzzy prime ideals in near-subtraction semigroups is already known. We have to expand the concept of prime fuzzy bi-ideals in near-subtraction semigroups and analyse some of its properties to characterize it. This will lead to learn a new type of fuzzy ideal and to develope the researcher to made their research.</p>", "Keywords": "", "DOI": "10.46300/91017.2020.7.6", "PubYear": 2021, "Volume": "7", "Issue": "", "JournalId": 75449, "JournalTitle": "International Journal of Fuzzy Systems and Advanced Applications", "ISSN": "", "EISSN": "2313-0512", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Scholarof Mathematics A.P.C.Mahalaxmi College for Women Thoothukudi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor of Mathematics A.P.C.Ma<PERSON> College for Women Thoothukudi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor of Mathematics Sri Parasakthi College for Women Courtallam, India"}], "References": []}, {"ArticleId": 86617657, "Title": "Information sources, practices and barriers: a situated and context-bound model of Pakistani electronic media reporters", "Abstract": "Purpose This study presents a situated and context-bound model of electronic media reports by exploring their detailed information practices within the workplace actions. It further investigates the information sources they usually consult for news or story-making process and barriers that hinder them to acquire required information. Design/methodology/approach The data were collected through semistructured, face-to-face interviews of electronic media reporters from the top news channels of Pakistan. These reporters had international exposure while having 7–20 years of work experience with different news channels. Findings In relation to information practices, the model also highlights the seven steps involved in news-making process of electronic media. Initially inspired from <PERSON><PERSON><PERSON><PERSON>'s (2003) model of information practice, which was developed within the everyday life context, this model attempts to see the information practices of electronic media reporters situated at their workplaces and might be seen as an extension of previous works. Originality/value This study is a unique attempt to find patterns of information practices situated in their workplace actions. The results of this study would be helpful for librarians and information specialists, who are working in media house libraries for the planning and designing of library services. Peer review The peer review history for this article is available at: https://publons.com/publon/10.1108/OIR-07-2020-0308", "Keywords": "Contextual model;Electronic media;Information practices;Journalists;Pakistan;Reporters", "DOI": "10.1108/OIR-07-2020-0308", "PubYear": 2021, "Volume": "45", "Issue": "5", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Arts and Design , University of the Punjab , Lahore, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Management, University of the Punjab , Lahore, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information Management, University of the Punjab , Lahore, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Engineering, National University of Sciences and Technology , Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Information Management, University of the Punjab , Lahore, Pakistan"}], "References": []}, {"ArticleId": 86618022, "Title": "DiPOSH: A portable OpenSHMEM implementation for short API‐to‐network path", "Abstract": "<p>In this article, we introduce DiPOSH, a multi-network, distributed implementation of the OpenSHMEM standard. The core idea behind DiPOSH is to have an API-to-network software stack as slim as possible, in order to minimize the software overhead. Following the heritage of its non-distributed parent POSH, DiPOSH's communication engine is organized around the processes' shared heaps, and remote communications are moving data from and to these shared heaps directly. This article presents its architecture and several communication drivers, including one that takes advantage of a helper process, called the Hub, for inter-process communications. This architecture allows use to explore different options for implementing the communication drivers, from using high-level, portable, optimized libraries to low-level, close to the hardware communication routines. We present the perspectives opened by this additional component in terms of communication scheduling between and on the nodes. DiPOSH is available at https://github.com/coti/DiPOSH .</p>", "Keywords": "distributed run-time environment;high-performance communication library;OpenSHMEM", "DOI": "10.1002/cpe.6179", "PubYear": 2021, "Volume": "33", "Issue": "11", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LIPN, CNRS UMR 7030, Université Sorbonne Paris Nord, Villetaneuse, France; University of Oregon, Eugene, Oregon, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Oregon, Eugene, Oregon, USA"}], "References": []}, {"ArticleId": 86618115, "Title": "A Survey of Bayesian Calibration and Physics-informed Neural Networks in Scientific Modeling", "Abstract": "<p>Computer simulations are used to model of complex physical systems. Often, these models represent the solutions (or at least approximations) to partial differential equations that are obtained through costly numerical integration. This paper presents a survey of two important statistical/machine learning approaches that have shaped the field of scientific modeling. Firstly we survey the developments on Bayesian calibration of computer models since the seminal work by <PERSON> and <PERSON>. In their paper, the authors proposed an elegant way to use the Gaussian processes to extend calibration beyond parameter and observation uncertainty and include model-form and data size uncertainty. Secondly, we also survey physics-informed neural networks, a topic that has been receiving growing attention due to the potential reduction in computational cost and modeling flexibility. In addition, in order to help the interested reader to familiarize with these topics and venture into custom implementations, we present a summary of applications and software tools. Finally, we close the paper with suggestion for future research directions and a thought provoking call for action.</p>", "Keywords": "", "DOI": "10.1007/s11831-021-09539-0", "PubYear": 2021, "Volume": "28", "Issue": "5", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "Felipe A. C. Viana", "Affiliation": "Department of Mechanical and Aerospace Engineering, University of Central Florida, Orlando, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Head of High Performance Computing & Machine Learning Solutions, Amazon Web Services, San Francisco, USA"}], "References": [{"Title": "Multiscale Modeling Meets Machine Learning: What Can We Learn?", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1017", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Physics-Informed Neural Networks for Missing Physics Estimation in Cumulative Damage Models: A Case Study in Corrosion Fatigue", "Authors": "<PERSON><PERSON>; Felipe A. <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "6", "Page": "1", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Unified Framework and Survey for Model Verification, Validation and Uncertainty Quantification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "4", "Page": "2655", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "FEMa: a finite element machine for fast learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "6393", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 86618141, "Title": "Trusted data collection for Internet of Things", "Abstract": "<p>Trusted date collection is the precondition of the security of Internet of Things (IOT). Recently, compressed sensing technology, which can enable the reduction of the energy consumption and time delay of data collection, has been extensively studied in IOT; However, the security of compressed sensing technology in IOT has not been sufficiently considered. Since compressed sensing depends the cooperation between IOT nodes and then sensing data is transparent for all IOT nodes, which is subjected to malicious nodes attack. Therefore, we propose a trusted data collection scheme based on compressed sensing oriented to the IOT. When data collection is tampered with, the scheme can detect malicious nodes. Experiments demonstrate that the proposed scheme can ensure data security with a low energy consumption.</p>", "Keywords": "compressed sensing;IOT;malicious node detection;trusted data collection", "DOI": "10.1002/cpe.6166", "PubYear": 2021, "Volume": "33", "Issue": "10", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, China; Beijing Key Laboratory of Trusted Computing, Beijing University of Technology, Beijing, China; National Engineering Laboratory for Critical Technologies of Information Security Classified Protection, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, China; Beijing Key Laboratory of Trusted Computing, Beijing University of Technology, Beijing, China; National Engineering Laboratory for Critical Technologies of Information Security Classified Protection, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, China; Beijing Key Laboratory of Trusted Computing, Beijing University of Technology, Beijing, China; National Engineering Laboratory for Critical Technologies of Information Security Classified Protection, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Nanjing University of Technology, Nanjing, China"}], "References": [{"Title": "ELDC: An Artificial Neural Network Based Energy-Efficient and Robust Routing Scheme for Pollution Monitoring in WSNs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "106", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "A Gaussian error correction multi‐objective positioning model with NSGA‐II", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": ********, "Title": "Parkinson’s Disease Diagnostics Based on the Analysis of Digital Sentence Writing Test", "Abstract": "<p>Analysis of the sentence writing test is conducted in this paper to support diagnostics of the Parkinsons disease. Drawing and writing tests digitization has become a trend where synergy of machine learning techniques on the one side and knowledge base of the neurology and psychiatry on the other side leading sophisticated result in computer aided diagnostics. Such rapid progress has a drawback. In many cases, decisions made by machine learning algorithm are difficult to explain in a language human practitioner familiar with. The method proposed in this paper employs unsupervised learning techniques to segment the sentence into the individual characters. Then, feature engineering process is applied to describe writing of each letter using a set of kinematic and pressure parameters. Following feature selection process applicability of different machine learning classifiers is evaluated. To guarantee that achieved results may be interpreted by human, two major guidelines are established. The first one is to keep dimensionality of the feature set low. The second one is clear physical meaning of the features describing the writing process. Features describing amount and smoothness of the motion observed during the writing alongside with letter size are considered. Resulting algorithm does not take into account any semantic information or language particularities and therefore may be easily adopted to any language based on Latin or Cyrillic alphabets.</p>", "Keywords": "", "DOI": "10.1142/S2196888821500238", "PubYear": 2021, "Volume": "8", "Issue": "4", "JournalId": 29506, "JournalTitle": "Vietnam Journal of Computer Science", "ISSN": "2196-8888", "EISSN": "2196-8896", "Authors": [{"AuthorId": 1, "Name": "Aleksei <PERSON>unajev", "Affiliation": "Tallinn University of Technology, Akadeemia tee 3, Tallinn 12618, Estonia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Software Science, Tallinn University of Technology, Akadeemia tee 15a, Tallinn 12618, Estonia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Natural Sciences and Health, Tallinn University, Narva mnt. 25, Tallinn 10120, Estonia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Sport Sciences Physiotherapy, University of Tartu, Puusepa 8, Tartu 51014, Estonia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurology and Neurosurgery, University of Tartu, Puusepa 8, Tartu 51014, Estonia"}], "References": []}, {"ArticleId": ********, "Title": "Stochastic process mining: Earth movers’ stochastic conformance", "Abstract": "Initially, process mining focused on discovering process models from event data, but in recent years the use and importance of conformance checking has increased. Conformance checking aims to uncover differences between a process model and an event log. Many conformance checking techniques and measures have been proposed. Typically, these take into account the frequencies of traces in the event log, but do not consider the probabilities of these traces in the model. This asymmetry leads to various complications. Therefore, we define conformance for stochastic process models taking into account frequencies and routing probabilities. We use the earth movers’ distance between stochastic languages representing models and logs as an intuitive conformance notion. In this paper, we show that this form of stochastic conformance checking enables detailed diagnostics projected on both model and log. To pinpoint differences and relate these to specific model elements, we extend the so-called ‘reallocation matrix’ to consider paths. The approach has been implemented in ProM and our evaluations show that stochastic conformance checking is possible in real-life settings.", "Keywords": "Process mining ; Conformance checking ; Stochastic process mining", "DOI": "10.1016/j.is.2021.101724", "PubYear": 2021, "Volume": "102", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Queensland University of Technology, Brisbane, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Rheinisch-Westfälische Technische Hochschule, Aachen, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Rheinisch-Westfälische Technische Hochschule, Aachen, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Melbourne, Australia"}], "References": [{"Title": "Monotone Precision and Recall Measures for Comparing Executions and Specifications of Dynamic Systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 86618627, "Title": "Output constraints vibration control for a flexible aircraft wing with prescribed performance", "Abstract": "This study focuses on the vibration control and boundary output constraint problems for a flexible wing system. The coupled twist-bending dynamic model of the flexible wing contains several partial differential equations (PDEs) and ordinary differential equations (ODEs). With the help of prescribed performance functions, an innovative boundary controller is designed to achieve the vibration suppression without the violation of prescribed performance. The proposed controller can make the boundary deformations converge to an arbitrarily small residual set. In addition, a disturbance observer is proposed to eliminate the adverse effect caused by unknown external disturbances. Then the asymptotic stability of the closed-loop system is justified by the <PERSON><PERSON><PERSON><PERSON>’s direct method. Finally, numerical simulations are carried out to demonstrate the effectiveness of the proposed control scheme.", "Keywords": "PDE model ; boundary control ; output constraint ; prescribed performance ; vibration control ; disturbance observer", "DOI": "10.1080/00207721.2021.1882611", "PubYear": 2021, "Volume": "52", "Issue": "11", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University (Beijing University of Aeronautics and Astronautics), Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University (Beijing University of Aeronautics and Astronautics), Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University (Beijing University of Aeronautics and Astronautics), Beijing, People’s Republic of China"}], "References": []}, {"ArticleId": 86618649, "Title": "Fault diagnosis and fault-tolerant control for T-S fuzzy time-varying delay stochastic distribution systems with actuator and sensor faults", "Abstract": "The problem of fault diagnosis (FD) and fault-tolerant control (FTC) for a class of nonlinear time-varying delay stochastic distribution control (SDC) systems with actuator and sensor faults is investigated in this paper. The Takagi–Sugeno (T-S) model is used to approximate the nonlinear dynamics. The B-spline function is used to approximate the output probability density function (PDF) of the system. According to the output equivalence principle and Laplace transformation, an augmented state vector is designed to solve the time-varying delay problem. An augmented state adaptive diagnosis observer is proposed to estimate the system state, actuator and sensor faults simultaneously. A new fault-tolerant control algorithm is designed based on the PI control strategy to compensate sensor fault and actuator faults simultaneously. The sensor fault is compensated through the information obtained by the observer. The PI controller can compensate for the influence of the actuator fault, and the output PDF of system can still track the desired PDF when fault occurs. Simulation results are provided to show the effectiveness of the proposed method.", "Keywords": "T-S fuzzy model ; time-varying delay ; SDC systems ; Laplace transformation ; actuator and sensor faults ; FD ; FTC", "DOI": "10.1080/00207721.2021.1880666", "PubYear": 2021, "Volume": "52", "Issue": "11", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Zhengzhou University, Zhengzhou, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Zhengzhou University, Zhengzhou, People's Republic of China"}], "References": [{"Title": "Fault‐Tolerant Control Based on Augmented State Estimator and PDF", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "999", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": 86618731, "Title": "Dubious until officially censored: Effects of online censorship exposure on viewers’ attitudes in authoritarian regimes", "Abstract": "Authoritarian regimes use censorship to prevent people from accessing unfavorable content. We argue that censorship, when detected by citizens, will have an adverse impact on their assessment of the government because censorship signals the government’s inability to address the issue being censored. Using an online survey experiment conducted in China, we find that censorship awareness significantly decreases people’s willingness to seek assistance from the government when needs arise. In addition, our survey respondents find a piece of news more credible when they believe that it is censored by the state. The findings suggest that censorship likely lowers people’s evaluation of the government’s problem-solving ability.", "Keywords": "Information manipulation ; online censorship ; government effectiveness ; China ; authoritarian regimes ; social media ; political attitudes", "DOI": "10.1080/19331681.2021.1879343", "PubYear": 2021, "Volume": "18", "Issue": "3", "JournalId": 6629, "JournalTitle": "Journal of Information Technology & Politics", "ISSN": "1933-1681", "EISSN": "1933-169X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86618959, "Title": "Project performance prediction model linking agility and flexibility demands to project type", "Abstract": "<p>The main purpose of this work is the development of a performance prediction model of projects, considering the influence of the leadership style and organizational factors on the agility and flexibility of the organization. The motivation of this work is the absence in the literature of a model to establish the relationship among leadership and agility factors, by means of the integration of prediction and sensitivity analysis capacities. The originality of this work lies on the presentation of a general model integrating both capacities, dedicated to the analysis of different kind of projects. The first part of the study deals with a literature review regarding the components of leadership, organizational structure, agility, flexibility and organizational factors and the intrinsic relationship among them. This information is the basis of the framework of this study that serves for the construction of the model. Artificial intelligence (AI) techniques and statistical tools are used for modeling purposes. From the model application, it is possible to identify under which conditions of leadership style and organizational factors there are the highest chances of high project performances. The main results are the characterization of agility and flexibility demands for each type of project, the understanding of how leadership affects agility and flexibility, and the impacts on the project performance.</p>", "Keywords": "agility;leadership;project performance;artificial intelligence", "DOI": "10.1111/exsy.12675", "PubYear": 2021, "Volume": "38", "Issue": "4", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Department, UNISOCIESC, SOCIESC University Center, Joinville, Brazil"}, {"AuthorId": 2, "Name": "Luiz V. O. Dalla Valentina", "Affiliation": "Research Department, UNISOCIESC, SOCIESC University Center, Joinville, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Research Department, UNISOCIESC, SOCIESC University Center, Joinville, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Production Engineering Department, UFSC, Federal University of Santa Catarina, Florianópolis, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Mechanical Engineering Department, UFSC, Federal University of Santa Catarina, Florianópolis, Brazil"}], "References": []}, {"ArticleId": 86619088, "Title": "Programming Multistate Aggregation‐Induced Emissive Polymeric Hydrogel into 3D Structures for On‐Demand Information Decryption and Transmission", "Abstract": "<p>Fluorescent hydrogels have recently become one of the most prominent materials for smart confidential information protection. But it is still quite challenging to develop a 3D anticounterfeiting platform with on-demand information decryption and transmission capacities, which are of great importance to achieve high-level data protection security. Herein, robust aggregation-induced emissive polymeric hydrogels with thermo-triggered multistate fluorescence switching, shape memory, and self-healing properties based on supramolecular dynamic lanthanide coordination interactions are presented. By a collective action of these promising properties, a fluorescent hydrogel-based 3D information encoding platform is demonstrated for on-demand information decryption, in which information pre-encrypted in 3D hydrogel structures is stepwise decrypted by the control of external stimuli. Such unique on-demand information decryption features are further expanded to the transmission of manifold customized messages to multiple receivers. Herein, the possibility of utilizing 3D fluorescent hydrogel structures for high-level information encryption and on-demand decryption is opened up.</p>", "Keywords": "aggregation-induced emissions;information encryptions;multistate fluorescence switching;on-demand decryptions;three-dimensional hydrogel structures", "DOI": "10.1002/aisy.202000239", "PubYear": 2021, "Volume": "3", "Issue": "6", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China; Nano Science and Technology Institute, University of Science and Technology of China, No. 166 Renai Road, Suzhou, 215000 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China; School of Chemical Sciences, University of Chinese Academy of Sciences, 19A Yuquan Road, Beijing, 100049 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China; School of Chemical Sciences, University of Chinese Academy of Sciences, 19A Yuquan Road, Beijing, 100049 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China"}, {"AuthorId": 5, "Name": "Huizhen Yan", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China; School of Chemical Sciences, University of Chinese Academy of Sciences, 19A Yuquan Road, Beijing, 100049 China; Guangdong Provincial Key Laboratory of Luminescence from Molecular Aggregates, South China University of Technology, Guangzhou, 510640 China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China; School of Chemical Sciences, University of Chinese Academy of Sciences, 19A Yuquan Road, Beijing, 100049 China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Polymer Science and Engineering, University of Science and Technology of China, No. 96 Jinzhai Road, Hefei, 230026 China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Marine Materials and Related Technologies, Zhejiang Key Laboratory of Marine Materials and Protective Technologies, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences, Ningbo, 315201 China; School of Chemical Sciences, University of Chinese Academy of Sciences, 19A Yuquan Road, Beijing, 100049 China"}], "References": [{"Title": "Autonomic perspiration in 3D-printed hydrogel actuators", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "38", "Page": "eaaz3918", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 86619095, "Title": "A Robust Non-Blind Watermarking for Biomedical Images Based on Chaos", "Abstract": "The advent of the Internet in these last years encouraged a considerable traffic of digital images. In the sanitary field, precisely in telemedicine branch, medical images play a very important role for therapeutic diagnoses. Thus, it is necessary to protect medical images data before transmission over the network to preserve their security and prevent unauthorized access. In this paper, a secure algorithm for biomedical images encryption scheme based on the combination of watermarking technique and chaotic function is proposed. In the proposed method, to achieve high security level performances, a non-blind hybrid watermarking technique with audio signal, Discrete Wavelet Transform is used; smoothness is also used as selected criteria; the iterations obtained by the chaotic sequences are essential and allow a good realization of the encryption process. One of the main advantages of chaos-based encryption schemes is the generation of a large number of key spaces to resist brute force attacks from the encryption algorithm. The experimental results presented in this paper attest to the invisibility and robustness of the proposed algorithm combining watermarking and chaos-based encryption.", "Keywords": "Biomedical Image;Watermarking;Wavelet Transform;Chaotic Encryption;DCT", "DOI": "10.4236/jcc.2021.92001", "PubYear": 2021, "Volume": "9", "Issue": "2", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, University of Ngaoundere, Ngaoundere, Cameroon"}, {"AuthorId": 2, "Name": "Ntsama Eloundou Pascal", "Affiliation": "Department of Physics, University of Ngaoundere, Ngaoundere, Cameroon"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, University of Ngaoundere, Ngaoundere, Cameroon"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Energy Renouvelable, School Petroleum of Kaele, University of Maroua, Maroua, Cameroon"}], "References": []}, {"ArticleId": 86619108, "Title": "REAL-TIME COMPUTATIONAL FLUID DYNAMICS FLOW RESPONSE VISUALISATION AND INTERACTION APPLICATION BASED ON AUGMENTED REALITY", "Abstract": "<p>The behaviour of fluid flow is a complex paradigm for cognitive interpretation and visualisation. Engineers need to visualise the behaviour mechanics of flow field response in order to enhance the cognitive ability in problem solving. Therefore, mixed reality related technology is the solution for enhanced virtual interactive learning environment. However, there are limited augmented reality platforms on fluid flow interactive learning. Therefore, an interactive education application is proposed for students and engineers to interact and understand the complex flow behaviour pattern subjected to elementary geometry body relative to external flow. This paper presented the technical development of a real-time flow response visualisation augmented reality application for computational fluid dynamics application. It was developed with the assistance of several applications such as Unity, Vuforia, and Android. Particle system modules available in the Unity engine were used to create a two-dimensional flow stream domain. The flow visualisation and interaction were limited to two-dimensional and the numerical fluid continuum response was not analysed. The physical flow response pattern of three simple geometry bodies was validated against ANSYS simulated results based on visual empirical observation. The particle size and number of particles emitted were adjusted in order to emulate the physical representation of fluid flow. Colour contour was set to change according to fluid velocity. Visual validation indicated trivial dissimilarities between FLUENT generated results and flow response exhibited by the proposed augmented reality application.</p>", "Keywords": "", "DOI": "10.32890/jict2020.19.4.5", "PubYear": 2020, "Volume": "19", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Automotive Engineering Technology, Universiti Malaysia Pahang, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mechanical and Automotive Engineering Technology, Universiti Malaysia Pahang, Malaysia"}, {"AuthorId": 3, "Name": "Daing Mohamad <PERSON>", "Affiliation": "Faculty of Mechanical and Automotive Engineering Technology, Universiti Malaysia Pahang, Malaysia"}], "References": []}, {"ArticleId": 86619185, "Title": "ПРОЦЕДУРЫ ПОИСКА УСЕЧЕННЫХ РЕШЕНИЙ ЛИНЕЙНЫХ ДИФФЕРЕНЦИАЛЬНЫХ УРАВНЕНИЙ С БЕСКОНЕЧНЫМИ И УСЕЧЕННЫМИ СТЕПЕННЫМИ РЯДАМИ В РОЛИ КОЭФФИЦИЕНТОВ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421020035", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "С. <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "А. А. Рябенко", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86619306, "Title": "#Godwin. A discursive analysis and typology of strategic references to the thirties and the Second World War in Flemish MPs’ tweets", "Abstract": "<p>In the wake of a series of late-2019 identitarian conflicts, public discourse in the Belgian region of Flanders was marked by references to the 1930s and 1940s as well as debates about the political appropriateness and significance of such historical allusions. Moving beyond a description of how historical references are condensed in digital political communication, the present article investigates a corpus of tweets sent from accounts of Flemish MPs in order to open up interdisciplinary perspectives on, among others, the dynamics of implicit and explicit accusations of fascism, and the complex, contested fields of speech in which the 1930s and 1940s are evoked on social media. It is thereby argued that (1) on Twitter, terminology associated with the 1930s and 1940s blurs boundaries between present and past, (2) that this conceptual flexibility allows these terms to be deployed in support of a range of political strategies, and (3) that these strategies share prominent accusatory aspects. The paper thus makes an evidence-based contribution to our understanding of how the memory and imagery of the 1930s and the Second World War strategically figure in digital political communication. These findings on online conflict and debate dynamics are supplemented with a methodological reflection on the gains of adopting a localized approach to the analysis of social media texts.</p>", "Keywords": "", "DOI": "10.1093/llc/fqab009", "PubYear": 2021, "Volume": "36", "Issue": "4", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Brussel, Belgium"}], "References": []}, {"ArticleId": 86619526, "Title": "A Business Model Taxonomy for Start-Ups in the Electric Power Industry — The Electrifying Effect of Artificial Intelligence on Business Model Innovation", "Abstract": "Artificial intelligence (AI) serves as a technological driver for business model innovation by guiding decisions and automating services, thereby leveraging efficiency-enhancing and profitable business practices. Especially in the electric power industry, a multitude of start-ups have entered the market offering disruptive AI-based services. However, there has been little research to date on what concrete business models result from the diffusion of AI and how these might be classified. In view of this research gap, this paper contributes to a better understanding of start-ups in the electric power industry that use AI technologies by systematically developing a business model taxonomy. In addition, we conducted 12 semi-structured interviews with domain experts for the evaluation step and validated the robustness of the taxonomy based on cluster analysis to identify common business model archetypes. Finally, we derived and discussed the academic and practical implications of our research and highlighted future research avenues.", "Keywords": "Electric power industry;business models;artificial intelligence;taxonomy", "DOI": "10.1142/S0219877021500048", "PubYear": 2021, "Volume": "18", "Issue": "3", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Building Blocks of Successful Digital Transformation: Complementing Technology and Market Issues", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "1", "Page": "2050004", "JournalTitle": "International Journal of Innovation and Technology Management"}, {"Title": "A taxonomy and archetypes of smart services for smart living", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "131", "JournalTitle": "Electronic Markets"}, {"Title": "Understanding token-based ecosystems – a taxonomy of blockchain-based business models of start-ups", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "307", "JournalTitle": "Electronic Markets"}, {"Title": "Virtual Assistance in Any Context", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "3", "Page": "211", "JournalTitle": "Business & Information Systems Engineering"}]}, {"ArticleId": 86619574, "Title": "The HDR Process: Reflection on the Motivational Factors, Academic Model, Influences and Challenges That Enabled an Academic to Complete Her Thesis Part-Time", "Abstract": "", "Keywords": "", "DOI": "10.20533/iji.1742.4712.2020.0209", "PubYear": 2020, "Volume": "13", "Issue": "2", "JournalId": 35123, "JournalTitle": "International Journal for Infonomics", "ISSN": "", "EISSN": "1742-4712", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86619626, "Title": "E-tourism: How ICTs Help the Local Tourist District Drive Economic Vitality. The Case of Campania, Italy", "Abstract": "The purpose of the study is to answer to the following Research Question (RQ): “What are the perception and the application of ICTs for e-tourism in different LTDs in Campania?” To answer the RQ a qualitative study was done with a questionnaire to 144 managers of Local Tourist Districts (LTDs) in Campania, Italy. The questionnaire was summited in 2017. The acquired data were processed using the factor analyses. The study of the region’s 12 LTDs allowed for the identification of three district typologies i.e. three homogeneous groups. The one, defined as proactive, exhibited positive results thanks to the skills and competencies of its managers. By contrast, despite the wealth of attractions, the other districts did not fare as well mostly due to management inadequacies and organizational culture. The research was limited to only 12 LTDs located in Italy. Possible measures to address operational and cultural problems in the region have been identified in implementation of ITCs’ applications to foster e-tourism and the local entrepreneurial ecosystem as well as the accumulation of knowledge to enhance the awareness of the education as a strategic factor [ <PERSON><PERSON><PERSON> , J. <PERSON>. ( 2015 ). Towards e-commerce use for pro-poor tourism promotion: Local providers’ ICT training needs in Tanzania. International Journal of Innovation and Technology Management, 12(4), 1550020]. Moreover, at the national and international level, other strategic elements can be identified in: analysis of good practices and cross contamination of proactive district managers with the participated and insensitive district managers.It is the first research paper on the Campania districts since their inception. The analysis of the phenomenon may be extended to other countries too.", "Keywords": "e-Tourism;factor analysis;local tourist district;Campania, Italy", "DOI": "10.1142/S0219877021500097", "PubYear": 2021, "Volume": "18", "Issue": "3", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "The digital revolution in the travel and tourism industry", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "3", "Page": "455", "JournalTitle": "Information Technology & Tourism"}]}, {"ArticleId": 86619631, "Title": "A smartly designed automated map based clustering algorithm for the enhanced diagnosis of pathologies in brain MR images", "Abstract": "<p>The competitive segmentation of fuzzy clustering is utilized in a greater manner to deal with the local spatial information of input medical images. Fuzzy clustering favours lesions and tumour identification through the segmentation process where less accuracy attainment and time complexity might be instigated for the identification of oddities. To rectify the above‐said problems, a novel methodology that encapsulates the combination of unsupervised neural network and fuzzy clustering processes, which effortlessly distinguishes the lesion and tumour region in MR brain images is developed through this study. The initial process of the proposed algorithm employs the histogram‐based feature extraction of the input images; whereof, a feature vector selection is made for the operation of self‐organizing map (SOM), which is a neural network functionary that progresses through the mapping process. Modification regarding the membership function of fuzzy entropy clustering (MFEC) is done based on the entropy value of the input image that results in quicker convergence. Finally, the updated objective function of MFEC algorithm augments the SOM result. It is found that the proposed SOM based MFEC algorithm is superior to other traditional segmentation algorithms, which have rendered the better visible understanding of the image. Further, the end‐results of the algorithm are verified through the evaluation of quantity metrics using ground truth of the brain MR images. The proposed SOM based MFEC algorithm precisely provides 82.26% of Jaccard value and 90.05% of Dice Overlap Index value, and these values prove better brain slices segmentation and provide enormous help to radiologists during patient diagnosis.</p>", "Keywords": "medical image analysis;modified fuzzy entropy clustering;self‐organizing map;tissue segmentation;tumours and lesion identification", "DOI": "10.1111/exsy.12625", "PubYear": 2021, "Volume": "38", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, Kalasalingam Academy of Research and Education, Srivilliputtur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Kalasalingam Academy of Research and Education, Srivilliputtur, India"}, {"AuthorId": 3, "Name": "Yu<PERSON> Zhang", "Affiliation": "Department of Informatics, University of Leicester, Leicester, UK"}, {"AuthorId": 4, "Name": "Pallikon<PERSON><PERSON>", "Affiliation": "Department of ECE, Kalasalingam Academy of Research and Education, Srivilliputtur, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Kalasalingam Academy of Research and Education, Srivilliputtur, India"}], "References": [{"Title": "Kernel intuitionistic fuzzy entropy clustering for MRI image segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4003", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 86619632, "Title": "A semantic‐enabled and context‐aware monitoring system for the internet of medical things", "Abstract": "<p>The emergence of the Internet of Things (IoT) in the medical field has led to the massive deployment of a myriad of medical connected objects (MCOs). These MCOs are being developed and implemented for remote healthcare monitoring purposes including elderly patients with chronic diseases, pregnant women, and patients with disabilities. Accordingly, different associated challenges are emerging and include the heterogeneity of the gathered health data from these MCOs with ever‐changing contexts. These contexts are relative to the continuous change of constraints and requirements of the MCOs deployment (time, location, state). Other contexts are related to the patient (medical record, state, age, sex, etc.) that should be taken into account to ensure a more precise and appropriate treatment of the patient. These challenges are difficult to address due to the absence of a reference model for describing the health data and their sources and linking these data with their contexts. This article addresses this problem and introduces a semantic‐based context‐aware system (IoT Medicare system) for patient monitoring with MCOs. This system is based on a core domain ontology (HealthIoT‐O), that is, designed to describe the semantic of heterogeneous MCOs and their data. Moreover, an efficient interpretation and management of this knowledge in diverse contexts are ensured through SWRL rules such as the verification of the proper functioning of the MCOs and the analysis of the health data for diagnosis and treatment purposes. A case study of gestational diabetes disease management is proposed to evaluate the effectiveness of the implemented IoT Medicare system. An evaluation phase is provided and focuses on the quality of the elaborated semantic model and the performance of the system.</p>", "Keywords": "context‐awareness;medical connected objects;ontology;patient monitoring", "DOI": "10.1111/exsy.12629", "PubYear": 2021, "Volume": "38", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ahlem Rhayem", "Affiliation": "Miracl Laboratory, Sfax University, Sfax, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Miracl Laboratory, Sfax University, Sfax, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CNRS, LAAS, Université de Toulouse, Toulouse, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CNRS, LAAS, Université de Toulouse, Toulouse, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Miracl Laboratory, Sfax University, Sfax, Tunisia"}], "References": [{"Title": "MSSN-Onto: An ontology-based approach for flexible event processing in Multimedia Sensor Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "1140", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Retracted: Intelligent medical IoT system based on WSN with computer vision platforms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "12", "Page": "e5036", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Interoperable Internet of Medical Things platform for e-Health applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "1", "Page": "155014771988959", "JournalTitle": "International Journal of Distributed Sensor Networks"}, {"Title": "Semantic Web Technologies for the Internet of Things: Systematic Literature Review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100206", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 86619863, "Title": "Low power perforated membrane microheater", "Abstract": "Heat loss to the membrane of a microheater is one of the major sources of heat loss in a micro heater. In this paper, we present a low power consuming micro heater. We show that power consumption can be decreased by decreasing heat loss in the supporting membrane on which the heating resistor sits on. We designed a modified structured two beams suspended membrane microheater with a perforated dielectric layer. The test result shows an 18.6 % reduction in power consumption with 15.18mW and a response time of 0.42ms at 400 °C. It was observed to be thermally stable and should provide a good platform for exploitation in the design of commercial MEMS sensors that have microheaters as one of its components.", "Keywords": "Microheater ; Low power ; MEMS", "DOI": "10.1016/j.sna.2021.112607", "PubYear": 2021, "Volume": "322", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Microelectronics, University of Science and Technology of China, Hefei, 230026, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Microelectronics, University of Science and Technology of China, Hefei, 230026, China"}, {"AuthorId": 3, "Name": "Dongcheng Xie", "Affiliation": "School of Microelectronics, University of Science and Technology of China, Hefei, 230026, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Microelectronics, University of Science and Technology of China, Hefei, 230026, China;Corresponding author"}], "References": []}, {"ArticleId": 86619877, "Title": "A chromene based fluorescence probe: Accurate detection of peroxynitrite in mitochondria, not elsewhere", "Abstract": "A novel fluorescent probe (BC-PN-2) for the detection of ONOO<sup>−</sup> was synthesized by conjugating dicyano-vinyl with benzopyran-chromone. The dicyano-vinyl group can be destructed by ONOO<sup>−</sup> to generate an aldehyde group resulting in strong fluorescence. Compared with the general fluorescence probes, a novel reactive site to detect ONOO<sup>−</sup> in mitochondria is used in this probe and the results show short response time, high sensitivity and excellent selectivity toward ONOO<sup>−</sup> among reactive oxygen species (ROS) and reactive nitrogen species (RNS) in PBS. Subsequent fluorescence imaging shows the high membrane permeability, a certain mitochondrial targeting and localization effect of the probe. In addition, the product BC-aldehyde cannot permeate through the membrane. Meanwhile, BC-PN-2 can accurately identify ONOO<sup>−</sup> in mitochondria by imaging of exogenous ONOO<sup>−</sup> in HepG2 cells and prove detected ONOO<sup>−</sup> are from mitochondria.", "Keywords": "Fluorescent probe ; Peroxynitrite ; Chromene ; Mitochondria ; Cell imaging", "DOI": "10.1016/j.snb.2021.129603", "PubYear": 2021, "Volume": "334", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China"}, {"AuthorId": 3, "Name": "Hai-<PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Chen", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Optic-electric Sensing and Analytical Chemistry for Life Science, MOE, College of Chemistry and Molecular Engineering, Qingdao University of Science and Technology, Qingdao, 266042, China;Corresponding authors"}], "References": []}, {"ArticleId": 86619910, "Title": "DeepCoroNet: A deep LSTM approach for automated detection of COVID-19 cases from chest X-ray images", "Abstract": "The new coronavirus, known as COVID-19, first emerged in Wuhan, China, and since then has been transmitted to the whole world. Around 34 million people have been infected with COVID-19 virus so far, and nearly 1 million have died as a result of the virus. Resource shortages such as test kits and ventilator have arisen in many countries as the number of cases have increased beyond the control. Therefore, it has become very important to develop deep learning-based applications that automatically detect COVID-19 cases using chest X-ray images to assist specialists and radiologists in diagnosis. In this study, we propose a new approach based on deep LSTM model to automatically identify COVID-19 cases from X-ray images. Contrary to the transfer learning and deep feature extraction approaches, the deep LSTM model is an architecture, which is learned from scratch. Besides, the Sobel gradient and marker-controlled watershed segmentation operations are applied to raw images for increasing the performance of proposed model in the pre-processing stage. The experimental studies were performed on a combined public dataset constituted by gathering COVID-19, pneumonia and normal (healthy) chest X-ray images. The dataset was randomly separated into two sections as training and testing data. For training and testing, these separations were performed with the rates of 80%–20%, 70%–30% and 60%–40%, respectively. The best performance was achieved with 80% training and 20% testing rate. Moreover, the success rate was 100% for all performance criteria, which composed of accuracy, sensitivity, specificity and F-score. Consequently, the proposed model with pre-processing images ensured promising results on a small dataset compared to big data. Generally, the proposed model can significantly improve the present radiology based approaches and it can be very useful application for radiologists and specialists to help them in detection, quantity determination and tracing of COVID-19 cases throughout the pandemic.", "Keywords": "COVID-19 ; Automated detection ; Marker-controlled watershed segmentation ; Deep LSTM model", "DOI": "10.1016/j.asoc.2021.107160", "PubYear": 2021, "Volume": "103", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Firat University, Technology Faculty, Electrical-Electronics Engineering Department, Elazig, Turkey"}], "References": [{"Title": "A Novel Medical Diagnosis model for COVID-19 infection detection based on Deep Features and Bayesian Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106580", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning approaches for COVID-19 detection based on chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114054", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 86620168, "Title": "5G \n wideband\n on‐chip \n dipole antenna for\n WSN \n soil moisture monitoring", "Abstract": "<p>Recently, soil dielectric constant detection has been used in the irrigation of vastly differing farming conditions. Soil monitoring has seen a push toward fifth generation (5G) telecommunications technology, due to the higher speed and bandwidth required to connect a huge number of sensor nodes simultaneously. A novel architecture for a fully integrated, unequal arms dipole antenna is presented in this article. The proposed on‐chip antenna is implemented using UMC180 nm CMOS technology. A dipole radiator is added on the upper metal layer, a ground sheet set at the lower metal layer and a patch layer is added on the intermediate metal layer. This configuration has improved the antenna impedance bandwidth by 38%. In addition, it increased the electrical length of the dipole antenna to reduce the antenna resonant frequency by 10 GHz. The proposed antenna has a total area 1300 × 250 μm, and its wide bandwidth extends from 21 to 40 GHz at reflection coefficient  S<sub>11</sub>  < –7 dB, which makes it suitable for on‐chip wireless sensor network applications in 5G technology. All Simulations are completed by using the Ansys HFSS (high‐frequency structure simulator) ver.15 and the simulated results show a good comparison with measurements. To investigate the proposed antenna's performance in different environmental conditions, practical measurements for soil dielectric constant in different compositions with variable moisture ratios are applied. The resulting values are used in antenna simulation and have ultimately proved the antenna's improved capability to sense soil moisture in different conditions.</p>", "Keywords": "dipole antenna;fifth generation;LUTs;millimeter‐wave;on‐chip antenna;UMC 180 nm;wireless sensor network", "DOI": "10.1002/mmce.22556", "PubYear": 2021, "Volume": "31", "Issue": "4", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Department, Faculty of Engineering‐Badr University, Badr University, Cairo, Egypt; Microstrip Department, Electronics Research Institute, Giza, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Microelectronics Department, Electronics Research Institute, Giza, Egypt"}], "References": []}, {"ArticleId": 86620339, "Title": "Combination of LiDAR’s Multiple Attributes for Wetland Classification: A Case Study of Yellow River Delta", "Abstract": "Wetlands have received intensive interdisciplinary attention as unique ecosystems with valuable resources. As a new technology, the airborne LiDAR system has recently been applied in wetland research. However, most of the studies have focused on one or two LiDAR observations to extract either terrain or vegetation information from wetlands. This research aims at evaluating the combination of LiDAR’s spatial, spectral, and derived information to improve the mapping and classification of wetlands. Six LiDAR data derived attributes (DSM, DTM, off-ground features, slope map, multiple pulse returns, and normalized intensity) have been utilized for wetland classification based on an object-oriented classification method. The overall classify accuracy of the proposed LiDAR’s multi-attribute approach reaches 91.2%. Les milieux humides ont reçu une attention interdisciplinaire intensive en tant qu’écosystèmes uniques avec des ressources précieuses. En tant que nouvelle technologie, le LiDAR aéroporté a récemment été utilisé comme outil dans les études sur les milieux humides. Toutefois, la plupart des études se sont concentrées sur une ou deux observations LiDAR pour extraire de l’information sur la topographie ou la végétation des milieux humides. Cette recherche vise à évaluer la combinaison des informations spatiale, spectrale et dérivée du LiDAR afin d’améliorer la cartographie et la classification des milieux humides. Six attributs dérivés des données LiDAR (DSM, DTM, caractéristiques hors sol, carte des pentes, retours d’impulsion multiples et l’intensité normalisée) ont été utilisés pour la classification des milieux humides basée sur une méthode de classification orientée objet. L’exactitude globale de l’approche multi-attributs LiDAR proposée atteint 91,2%. Acknowledgments The dataset used for this study was provided by the First Institute of Oceanography of China. Additional information Funding This research was supported in part by the National Natural Science Foundation of China [Grant No. 41874031, No. 41704019 and No. 41804021].", "Keywords": "", "DOI": "10.1080/07038992.2020.1867833", "PubYear": 2020, "Volume": "46", "Issue": "6", "JournalId": 4152, "JournalTitle": "Canadian Journal of Remote Sensing", "ISSN": "0703-8992", "EISSN": "1712-7971", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Surveying and Mapping Engineering, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping and Remote Sensing, Wuhan University, Wuhan 430079, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Surveying and Mapping Engineering, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 4, "Name": "Long Tang", "Affiliation": "Department of Surveying and Mapping Engineering, Guangdong University of Technology, Guangzhou 510006, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Surveying and Mapping Engineering, Guangdong University of Technology, Guangzhou 510006, China"}], "References": []}, {"ArticleId": 86620521, "Title": "Petri Net based modeling and analysis for improved resource utilization in cloud computing", "Abstract": "<p> The cloud is a shared pool of systems that provides multiple resources through the Internet, users can access a lot of computing power using their computer. However, with the strong migration rate of multiple applications towards the cloud, more disks and servers are required to store huge data. Most of the cloud storage service providers are replicating full copies of data over multiple data centers to ensure data availability. Further, the replication is not only a costly process but also a wastage of energy resources. Furthermore, erasure codes reduce the storage cost by splitting data in n chunks and storing these chunks into n + k different data centers, to tolerate k failures. Moreover, it also needs extra computation cost to regenerate the data object. Cache-A Replica On Modification (CAROM) is a hybrid file system that gets combined benefits from both the replication and erasure codes to reduce access latency and bandwidth consumption. However, in the literature, no formal analysis of CAROM is available which can validate its performance. To address this issue, this research firstly presents a colored Petri net based formal model of CAROM. The research proceeds by presenting a formal analysis and simulation to validate the performance of the proposed system. This paper contributes towards the utilization of resources in clouds by presenting a comprehensive formal analysis of CAROM. </p>", "Keywords": "Cloud computing;Colored Petri net;Formal analysis;Replication", "DOI": "10.7717/peerj-cs.351", "PubYear": 2021, "Volume": "7", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Western Norway University of Applied Sciences, Bergen, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Lahore Campus, Lahore, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Lahore Campus, Lahore, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical & Computer Engineering, COMSATS University Islamabad, Attock Campus, Attock, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Information Systems, College of Computer Science and Information Technology, <PERSON> University, Dammam, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Educational Technology, College of Education, <PERSON> F<PERSON> University, Dammam, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, Saudi Arabia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Wuhan University, Wuhan, China"}], "References": []}, {"ArticleId": 86620577, "Title": "Unsupervised random forests", "Abstract": "<p>sidClustering is a new random forests unsupervised machine learning algorithm. The first step in sidClustering involves what is called sidification of the features: staggering the features to have mutually exclusive ranges (called the staggered interaction data [SID] main features) and then forming all pairwise interactions (called the SID interaction features). Then a multivariate random forest (able to handle both continuous and categorical variables) is used to predict the SID main features. We establish uniqueness of sidification and show how multivariate impurity splitting is able to identify clusters. The proposed sidClustering method is adept at finding clusters arising from categorical and continuous variables and retains all the important advantages of random forests. The method is illustrated using simulated and real data as well as two in depth case studies, one from a large multi-institutional study of esophageal cancer, and the other involving hospital charges for cardiovascular patients.</p>", "Keywords": "Impurity;sidClustering;staggered interaction data;unsupervised learning", "DOI": "10.1002/sam.11498", "PubYear": 2021, "Volume": "14", "Issue": "2", "JournalId": 5556, "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal", "ISSN": "1932-1864", "EISSN": "1932-1872", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Biostatistics, University of Miami, Miami, Florida, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Biostatistics, University of Miami, Miami, Florida, USA"}], "References": [{"Title": "Outer-Points shaver: Robust graph-based clustering via node cutting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "107001", "JournalTitle": "Pattern Recognition"}, {"Title": "A novel density-based clustering algorithm using nearest neighbor graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107206", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 86620926, "Title": "LSTM based Online Public Opinion Rumors Recognition Method", "Abstract": "", "Keywords": "", "DOI": "10.21742/IJSH.2020.14.2.05", "PubYear": 2020, "Volume": "14", "Issue": "2", "JournalId": 16886, "JournalTitle": "International Journal of Smart Home", "ISSN": "1975-4094", "EISSN": "1975-4094", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Heilongjiang Vocational College of Winter Sports, Heilongjiang, China"}], "References": []}, {"ArticleId": 86621064, "Title": "Smart access control system by using sparse representation features", "Abstract": "<p>The access control system is used in massive scenarios to protect personal property safety, such as dormitory in campus, company office, and smart apartment. The recent access control system use either RFID card or biometric features, such as fingerprint, face. The RFID card may be lost and misused by other persons. The fingerprint features must be collected by touching a sensor, which is improper during the epidemic of Covid‐19. The face identification in access control system is effected by the mask, especially under the current epidemic situation. The research about partial occlusion face recognition is helpful to improve the existing face authentication for access control system. This paper establishes a smart access control system by using partial occlusion face recognition technology. First, the face is captured by a camera. Second, the collected faces are used to establish a face library for constructing low rank sparse model by using nuclear norm to measure the error matrix. Third, the sparse features of faces are used to train a neural network. The new personal face are verified by the learnt neural network model. The experimental results on a public partial occlusion face dataset demonstrate the effectiveness of the proposed access control system.</p>", "Keywords": "biometric features;face recognition;smart access control system;sparse representation", "DOI": "10.1002/itl2.278", "PubYear": 2021, "Volume": "4", "Issue": "3", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Management, Wuhan University, Wuhan, People's Republic of China"}], "References": []}, {"ArticleId": 86621068, "Title": "Sport action recognition by fusing multi‐source sensor information", "Abstract": "<p>In traditional sport and training, the quality of training plan mainly relies on individual observation and experience of coaches, which inevitably depends on individual objective opinion. With the emerging of sensors and body area network (BAN), it becomes possible to automatically recognize athletes' posture. Thus, the coaches can make decision based on recognition results to enhance athletes' competitive ability, which can greatly improve the effectiveness and quality of the sport training. This paper proposes an automatically action recognition system for sport training. First, we collect body information from body sensors. Second, the information from multi-source sensors is input into a Kalman filter to remove the interference noise. Third, the denoised signals are divided as several unit actions according to four limbs. Fourth, the unit actions are represented as a matrix consisting of acceleration, angular velocity, combined acceleration, and combined angular velocity. We extract frequency domain features and time domain features to represent the matrix. Lastly, the extracted features are used to train an intelligent classifier which is used to predict the future postures. The experimental results on a sport training dataset demonstrates the effectiveness of our framework for action recognition for sport training.</p>", "Keywords": "action recognition;body area network;Kalman filter;multi-source sensors", "DOI": "10.1002/itl2.279", "PubYear": 2021, "Volume": "4", "Issue": "3", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Jizu Shi", "Affiliation": "Jilin Institute Of Physical Education, Changchun, People's Republic of China"}], "References": [{"Title": "A comprehensive survey of multi-view video summarization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107567", "JournalTitle": "Pattern Recognition"}, {"Title": "MotioNet", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 86621356, "Title": "VMS Support: A mobile-based support to computerized visitor management system", "Abstract": "VMS Support is a mobile-based application which is intended to integrate with a computerized visitor management system (CVMS). The application is used as a receiver of visit notification, respond to the CVMS and update the current whereabout. Visit notification was built using a mobile offline push notification which will not force the organization to invest in any internet provider and subscribe to a premium push notification service. User may respond to visit notification by sending a message that may be visible to the CVMS, and update its current whereabout to guide the visitor correctly.", "Keywords": "Visitor management system ; Mobile support ; Visitor management system mobile support", "DOI": "10.1016/j.simpa.2021.100056", "PubYear": 2021, "Volume": "8", "Issue": "", "JournalId": 66395, "JournalTitle": "Software Impacts", "ISSN": "2665-9638", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON> State College, College of Computer Studies, Alijis Campus, Bacolod City, Negros Occidental, Philippines"}], "References": []}, {"ArticleId": 86621885, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1111/exsy.12586", "PubYear": 2021, "Volume": "38", "Issue": "2", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 86622032, "Title": "A decentralized blockchain oriented framework for automated bug assignment", "Abstract": "Context In large software projects bug fixing is a time-bound, time-consuming, mind-numbing, and challenging task that requires a collaborative effort with multiple developers, separated geographically. Objective The objective of this paper is to propose a decentralized automated bug assignment approach to improve the quality of bug assignments to minimize backlogs and overall bug fixing time. Method To the best of our knowledge, the literature lacks in studies focusing on how to increase software developer's motivation for efficient bug resolution. It is a novel decentralized blockchain oriented, transparent auction-based bug assignment framework which uses incentive mechanism as reward and penalty backed by blockchain technology using smart contracts for developers motivation. The process allows individual developers to select bug reports, matching their preferences and schedule for which they shall we able to provide robust solutions with reduced overhead in cost and time of bug fixing. Results Results of experimentation and surveys conclude that the proposed method is transparent and effective in bug assignment minimizing overall bug fixing time. The low cost of contract execution demonstrates that it can be used quantitatively and without ambiguity. Conclusion The work presented is novel to improve (i) bug assignment (ii) allow individual developers to choose what they like to provide robust solutions (iii) handles two major issues of differentiating between active and inactive developers and confusion over the assignment of bugs (iv) will further reduce bug-fixing delays and will prevent reassignment problem.", "Keywords": "Bug triaging, Bug assignment ; Bug reports ; Developer contribution assessment ; Blockchain technology ; Auction", "DOI": "10.1016/j.infsof.2021.106540", "PubYear": 2021, "Volume": "134", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade da Beira Interior, Portugal;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade da Beira Interior, Portugal;Instituto de Telecomunicações, Portugal"}], "References": []}, {"ArticleId": 86622566, "Title": "Variable population‐sized particle swarm optimization for highly imbalanced dataset classification", "Abstract": "<p>Real-world datasets used for classification often face many challenges when they are imbalanced in nature which is unavoidable and need to be handled by analysts. Many researchers have proposed methods for handling imbalanced datasets and they mostly concentrated on handling binary classification with only two class labels. Only very few research works have been carried out for treating highly imbalanced datasets and fail to handle multiclass datasets. To address imbalance problem in multiclass datasets, this paper proposes a Variable Population sized Particle Swarm Optimization (VPPSO) which is a modified version of Particle Swarm Optimization (PSO) which works based on clustering. PSO usually has fixed population size and has high computational complexity. In order to reduce this, the population size is varied over generations and the particles are loaded into the population iteratively by retaining the balance nature of solutions. PSO optimizes the selection of training and testing samples from each class label in imbalanced datasets for improved classification results. From the implementation results, it is evident that using VPPSO, highly imbalanced datasets with multiclass attributes are classified more efficiently than state-of-the-art algorithms. The statistical results also prove the superior performance of VPPSO.</p>", "Keywords": "highly imbalanced dataset;multiclass labels;particle swarm optimization;variable population", "DOI": "10.1111/coin.12436", "PubYear": 2021, "Volume": "37", "Issue": "2", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, Kongu Engineering College, Erode, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Nandha Engineering College, Erode, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Kongu Engineering College, Erode, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Kongu Engineering College, Erode, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Kongu Engineering College, Erode, India; Department of Computer Science and Engineering, Nandha Engineering College, Erode, India"}], "References": [{"Title": "Performance evaluation of a cost-sensitive differential evolution classifier using spark – Imbalanced binary classification", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "40", "Issue": "", "Page": "101065", "JournalTitle": "Journal of Computational Science"}]}, {"ArticleId": 86622742, "Title": "What makes a helpful online review? Empirical evidence on the effects of review and reviewer characteristics", "Abstract": "Purpose The reviews submitted by users are the foundation of user-generated content (UGC) platforms. However, the rapid growth of users brings the problems of information overload and spotty content, which makes it necessary for UGC platforms to screen out reviews that are really helpful to users. The authors put forward in this paper the factors influencing review helpfulness voting from the perspective of review characteristics and reviewer characteristics. Design/methodology/approach This study uses 8,953 reviews from 20 movies listed on Douban.com with variables focusing on review characteristics and reviewer characteristics that affect review helpfulness. To verify the six hypotheses proposed in the study, Stata 14 was used to perform tobit regression. Findings Findings show that review helpfulness is significantly influenced by the length, valence, timeliness and deviation rating of the reviews. The results also underlie that a review submitted by a reviewer who has more followers and experience is more affected by review characteristics. Originality/value Previous literature has discussed the factors that affect the helpfulness of reviews; however, the authors have established a new model that explores more comprehensive review characteristics and the moderating effect reviewer characteristics have on helpfulness. In this empirical research, the authors selected a UGC community in China as the research object. The UGC community may encourage users to write more helpful reviews by highlighting the characteristics of users. Users in return can use this to establish his/her image in the community. Future research can explore more variables related to users. Peer review The peer review history for this article is available at: https://publons.com/publon/10.1108/OIR-05-2020-0186 .", "Keywords": "Online review helpfulness;Extreme rating;Review valence;Timeliness;Moderator analysis", "DOI": "10.1108/OIR-05-2020-0186", "PubYear": 2021, "Volume": "45", "Issue": "3", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business and Management , Shanghai International Studies University , Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business and Management , Shanghai International Studies University , Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business and Management , Shanghai International Studies University , Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Business and Management , Shanghai International Studies University , Shanghai, China"}], "References": [{"Title": "Determinants of writing positive and negative electronic word-of-mouth: Empirical evidence for two types of expectation confirmation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "113168", "JournalTitle": "Decision Support Systems"}, {"Title": "Vote or not? How various information cues affect helpfulness voting of online reviews", "Authors": "<PERSON><PERSON>g; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "4", "Page": "787", "JournalTitle": "Online Information Review"}, {"Title": "Investigating the effects of textual reviews from consumers and critics on movie sales", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "6", "Page": "1245", "JournalTitle": "Online Information Review"}]}, {"ArticleId": 86622860, "Title": "Random Regular Expression Over Huge Alphabets", "Abstract": "<p>In this article, we study some properties of random regular expressions of size [Formula: see text], when the cardinality of the alphabet also depends on [Formula: see text]. For this, we revisit and improve the classical Transfer Theorem from the field of analytic combinatorics. This provides precise estimations for the number of regular expressions, the probability of recognizing the empty word and the expected number of Kleene stars in a random expression. For all these statistics, we show that there is a threshold when the size of the alphabet approaches [Formula: see text], at which point the leading term in the asymptotics starts oscillating.</p>", "Keywords": "", "DOI": "10.1142/S012905412141001X", "PubYear": 2021, "Volume": "32", "Issue": "5", "JournalId": 9642, "JournalTitle": "International Journal of Foundations of Computer Science", "ISSN": "0129-0541", "EISSN": "1793-6373", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LIGM, UGE & CNRS, 5 boulevard Descartes, Champs-sur-Marne, 77454, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LITIS, Univ Rouen Normandie, 685 avenue de l’université, Saint-Étienne-du-Rouvray, 76800, France"}], "References": []}, {"ArticleId": 86623114, "Title": "ESTIMATION OF MISSING VALUES USING OPTIMISED HYBRID FUZZY C-MEANS AND MAJORITY VOTE FOR MICROARRAY DATA", "Abstract": "<p>Missing values are a huge constraint in microarray technologies towards improving and identifying disease-causing genes. Estimating missing values is an undeniable scenario faced by field experts. The imputation method is an effective way to impute the proper values to proceed with the next process in microarray technology. Missing value imputation methods may increase the classification accuracy. Although these methods might predict the values, classification accuracy rates prove the ability of the methods to identify the missing values in gene expression data. In this study, a novel method, Optimised Hybrid of Fuzzy C-Means and Majority Vote (opt-FCMMV), was proposed to identify the missing values in the data. Using the Majority Vote (MV) and optimisation through Particle Swarm Optimisation (PSO), this study predicted missing values in the data to form more informative and solid data. In order to verify the effectiveness of opt-FCMMV, several experiments were carried out on two publicly available microarray datasets (i.e. Ovary and Lung Cancer) under three missing value mechanisms with five different percentage values in the biomedical domain using Support Vector Machine (SVM) classifier. The experimental results showed that the proposed method functioned efficiently by showcasing the highest accuracy rate as compared to the one without imputations, with imputation by Fuzzy C-Means (FCM), and imputation by Fuzzy C-Means with Majority Vote (FCMMV). For example, the accuracy rates for Ovary Cancer data with 5% missing values were 64.0% for no imputation, 81.8% (FCM), 90.0% (FCMMV), and 93.7% (opt-FCMMV). Such an outcome indicates that the opt-FCMMV may also be applied in different domains in order to prepare the dataset for various data mining tasks.</p>", "Keywords": "", "DOI": "10.32890/jict2020.19.4.1", "PubYear": 2020, "Volume": "19", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Teknologi Malaysia, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Teknologi Malaysia, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Teknologi Malaysia, Malaysia"}], "References": []}, {"ArticleId": 86623115, "Title": "MODIFIED PASSIVE A<PERSON><PERSON><PERSON>LE BANDWIDTH ESTIMATION IN IEEE 802.11 WLAN", "Abstract": "<p>Quality of Service provisioning for real-time multimedia applications is largely determined by a network’s available bandwidth. Until now, there is no standard method for estimating bandwidth on wireless networks. Therefore, in this study, a mathematical model called Modified Passive Available Bandwidth Estimation (MPABE) was developed to estimate the available bandwidth passively on a Distributed Coordination Function (DCF) wireless network on the IEEE 802.11 protocol. The mathematical model developed was a modification of three existing mathematical models, namely Available Bandwidth Estimation (ABE), Cognitive Passive Estimation of Available Bandwidth V2 (cPEAB-V2), and Passive Available Bandwidth Estimation (PABE). The proposed mathematical model gave emphasis on what will be faced to estimate available bandwidth and will help in building strategies to estimate available bandwidth on IEEE 802.11. The developed mathematical model consisted of idle period synchronisation between sender and receiver, the overhead probability occurring in the Medium Access Control (MAC) layer, as well as the successful packet transmission probability. Successful packet transmission was influenced by three variables, namely the packet collision probability caused by a number of neighbouring nodes, the packet collision probability caused by traffic from hidden nodes, and the packet error probability. The proposed mathematical model was tested by comparing it with other relevant mathematical models. The performance of the four mathematical models was compared with the actual bandwidth. Using a series of experiments that have been performed, it was found that the proposed mathematical model is approximately 26% more accurate than ABE, 36% more accurate than cPEABV2, and 32% more accurate than PABE.</p>", "Keywords": "", "DOI": "10.32890/jict2020.19.4.2", "PubYear": 2020, "Volume": "19", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Bandung", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung, Indonesia"}], "References": []}, {"ArticleId": 86623118, "Title": "CLASSIFICATION OF SHORT POSSESSIVE CLITIC PRONOUN NYA IN MALAY TEXT TO SUPPORT ANAPHOR CANDIDATE DETERMINATION", "Abstract": "<p>Anaphor candidate determination is an important process in anaphora resolution (AR) systems. There are several types of anaphor, one of which is pronominal anaphor. Pronominal anaphor is an anaphor that involves pronouns. In some of the cases, certain pronouns can be used without referring to any situation or entity in a text, and this phenomenon is known as pleonastic. In the case of the Malay language, it usually occurs for the pronoun nya. The pleonastic that exists in every text causes a severe problem to the anaphora resolution systems. The process to determine the pleonastic nya is not the same as identifying the pleonastic ‘it’ in the English language, where the syntactic pattern could not be used because the structure of nya comes at the end of a word. As an alternative, semantic classes are used to identify the pleonastic itself and the anaphoric nya. In this paper, the automatic semantic tag was used to determine the type of nya, which at the same time could determine nya as an anaphor candidate. The new algorithms and MalayAR architecture were proposed. The results of the F-measure showed the detection of clitic nya as a separate word achieved a perfect 100% result. In comparison, the clitic nya as a pleonastic achieved 88%, clitic nya referring to humans achieved 94%, and clitic nya referring to non-humans achieved 63%. The results showed that the proposed algorithms were acceptable to solve the issue of the clitic nya as pleonastic, human referral as well as non-human referral.</p>", "Keywords": "", "DOI": "10.32890/jict2020.19.4.3", "PubYear": 2020, "Volume": "19", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>@Ka<PERSON>h <PERSON>d <PERSON>", "Affiliation": "Faculty of Computing, Universiti Malaysia Pahang, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Science & Technology, Universiti Kebangsaan Malaysia, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Science & Technology, Universiti Kebangsaan Malaysia, Malaysia"}], "References": [{"Title": "Chinese Zero Pronoun Resolution", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Anaphora and coreference resolution: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "139", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 86623123, "Title": "EMPIRICAL MODE DECOMPOSITION BASED ON THETA METHOD FOR FORECASTING DAILY STOCK PRICE", "Abstract": "<p>Forecasting is a challenging task as time series data exhibit many features that cannot be captured by a single model. Therefore, many researchers have proposed various hybrid models in order to accommodate these features to improve forecasting results. This work proposed a hybrid method between Empirical Mode Decomposition (EMD) and Theta methods by considering better forecasting potentiality. Both EMD and Theta are efficient methods in their own ground of tasks for decomposition and forecasting, respectively. Combining them to obtain a better synergic outcome deserves consideration. EMD decomposed the training data from each of the five Financial Times Stock Exchange 100 Index (FTSE 100 Index) companies’ stock price time series data into Intrinsic Mode Functions (IMF) and residue. Then, the Theta method forecasted each decomposed subseries. Considering different forecast horizons, the effectiveness of this hybridisation was evaluated through values of conventional error measures found for test data and forecast data, which were obtained by adding forecast results for all component counterparts extracted from the EMD process. This study found that the proposed method produced better forecast accuracy than the other three classic methods and the hybrid EMD-ARIMA models.</p>", "Keywords": "", "DOI": "10.32890/jict2020.19.4.4", "PubYear": 2020, "Volume": "19", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Applied Mathematics, Noakhali Science and Technology University, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Universiti Sains Malaysia, Malaysia"}], "References": []}, {"ArticleId": 86623128, "Title": "A diffusion wavelets-based multiscale framework for inverse optimal control of stochastic systems", "Abstract": "This work presents a multiscale framework to solve a class of inverse optimal control (IOC) problems in the context of robot motion planning and control in a complex environment. In order to handle complications resulting from a large decision space and complex environmental geometry, two key concepts are adopted: (a) a diffusion wavelet representation of the Markov chain for hierarchical abstraction of the state space; and (b) a desirability function-based representation of the Markov decision process (MDP) to efficiently calculate the optimal policy. An IOC problem constructed on a ‘abstract state’ is solved, which is much more tractable than using the original bases set; moreover, the solution can be obtained recursively in the ‘coarse to fine’ direction by utilizing the hierarchical structure of basis functions. The resulting multiscale plan is utilized to finally compute a continuous-time optimal control policy within a receding horizon implementation. Illustrative numerical experiments on a robot path control in a complex environment and on a quadrotor ball-catching task are presented to verify the proposed method.", "Keywords": "Inverse optimal control ; diffusion wavelets ; multiresolution analysis", "DOI": "10.1080/00207721.2021.1882011", "PubYear": 2021, "Volume": "52", "Issue": "11", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Learning & Intelligent Systems Lab, Technical University of Berlin, Berlin, Germany;Max Planck Institute for Intelligent Systems, Stuttgart, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering & KI for Robotics, KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering & KI for Robotics, KAIST, Daejeon, Republic of Korea"}], "References": []}, {"ArticleId": 86623182, "Title": "Recent Progress in Transistor‐Based Optoelectronic Synapses: From Neuromorphic Computing to Artificial Sensory System", "Abstract": "<p>Neuromorphic electronics draw attention as innovative approaches that facilitate hardware implementation of next-generation artificial intelligent system including neuromorphic in-memory computing, artificial sensory perception, and humanoid robotics. Among the various neuromorphic devices, optoelectronic synapses are promising neuromorphic devices that use optical means to mimic synaptic plasticity and related functions. Compared with classical neuromorphic chip based on electronic synapses using electrical means, photonic neuromorphic chip using light as input spike signal can be attractive alternative approach for next-generation artificial intelligent system capable of high density, low power consumption, and low crosstalk. Thus, various optoelectronic synaptic electronics have been developed to overcome the drawback of conventional artificial intelligent system based on electrical synapses. Herein, the recent progresses in transistor-based optoelectronic synapses for artificial intelligent system and review their device architecture, neuromorphic operational mechanisms, manufacturing methodologies, and advanced applications for artificial intelligent computing and visual perception systems are focused. Finally, the future challenges and research direction in the optoelectronic synaptic research are discussed.</p>", "Keywords": "artificial sensory systems;neuromorphic computing;optoelectronic synapses;photo-synaptic plasticity;thin-film transistors", "DOI": "10.1002/aisy.202000162", "PubYear": 2021, "Volume": "3", "Issue": "6", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Printed Electronics Engineering, Sunchon National University, Sunchon, Jeonnam, 57922 Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electrical and Electronics Engineering, Chung-Ang University, Seoul, 06974 Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Advanced Materials Science and Engineering, Sungkyunkwan University, Suwon, 16419 Republic of Korea; SKKU Advanced Institute of Nanotechnology (SAINT), Sungkyunkwan University, Suwon, 16419 Republic of Korea"}, {"AuthorId": 4, "Name": "Sung Kyu Park", "Affiliation": "School of Electrical and Electronics Engineering, Chung-Ang University, Seoul, 06974 Republic of Korea"}], "References": [{"Title": "Recent Progress in Photonic Synapses for Neuromorphic Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "1900136", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Artificial Perception Built on Memristive System: Visual, Auditory, and Tactile Sensations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "1900118", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Piezo/Tribotronics Toward Smart Flexible Sensors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "7", "Page": "1900175", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Device and Circuit Architectures for In‐Memory Computing", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "7", "Page": "2000040", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Ion‐Gated Transistor: An Enabler for Sensing and Computing Integration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "12", "Page": "2000156", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 86623191, "Title": "A SCIENTOMETRIC ANALYSIS OF THE EMERGING TOPICS IN GENERAL COMPUTER SCIENCE", "Abstract": "<p>Citations have been an acceptable journal performance metric used by many indexing databases for inclusion and discontinuation of journals in their list. Therefore, editorial teams must maintain their journal performance by increasing article citations for continuous content indexing in the databases. With this aim in hand, this study intended to assist the editorial team of the Journal of Information and Communication Technology (JICT) in increasing the performance and impact of the journal. Currently, the journal has suffered from low citation count, which may jeopardise its sustainability. Past studies in library science suggested a positive correlation between keywords and citations. Therefore, keyword and topic analyses could be a solution to address the issue of journal citation. This article described a scientometric analysis of emerging topics in general computer science, the Scopus subject area for which JICT is indexed. This study extracted bibliometric data of the top 10% journals in the subject area to create a dataset of 5,546 articles. The results of the study suggested ten emerging topics in computer science that can be considered by the journal editorial team in selecting articles and a list of highly used keywords in articles published in 2019 and 2020 (as of 15 April 2020). The outcome of this study might be considered by the JICT editorial team and other journals in general computer science that suffer from a similar issue.</p>", "Keywords": "", "DOI": "10.32890/jict2020.19.4.6", "PubYear": 2020, "Volume": "19", "Issue": "", "JournalId": 66251, "JournalTitle": "Journal of Information and Communication Technology", "ISSN": "1675-414X", "EISSN": "2180-3862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Utara Malaysia, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing, Universiti Utara Malaysia, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Universiti Utara Malaysia, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Arts and Sciences, Shatt Al-Arab University, Iraq"}], "References": [{"Title": "Anthropocentric perspective of production before and within Industry 4.0", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "105644", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Placing the operator at the centre of Industry 4.0 design: Modelling and assessing human activities within cyber-physical systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "105058", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Deep Learning Based Recommender System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 86623692, "Title": "Unfolding the Evolution of Machine Learning and its Expediency﻿", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i01.001", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "Ritvik Voleti﻿", "Affiliation": ""}], "References": []}, {"ArticleId": 86623693, "Title": "Ensemble Classifier for Plant Disease Detection", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i01.003", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86623740, "Title": "Simulation for Graduate Students Research Consultations to Measure Organization Performance", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2020.v09i12.011", "PubYear": 2020, "Volume": "9", "Issue": "12", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>uf K.<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>﻿", "Affiliation": ""}], "References": []}, {"ArticleId": 86623794, "Title": "Recommender using Hybrid Approach for Candidate Generation", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2020.v09i12.012", "PubYear": 2020, "Volume": "9", "Issue": "12", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>.", "Affiliation": ""}, {"AuthorId": 3, "Name": "Bhor Pranali S.", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>.", "Affiliation": ""}], "References": []}, {"ArticleId": 86623809, "Title": "Intelligent fault diagnosis for rolling bearings based on graph shift regularization with directed graphs", "Abstract": "Graph shift regularization is a new and effective graph-based semi-supervised classification method, but its performance is closely related to the representation graphs. Since directed graphs can convey more information about the relationship between vertices than undirected graphs, an intelligent method called graph shift regularization with directed graphs (GSR-D) is presented for fault diagnosis of rolling bearings. For greatly improving the diagnosis performance of GSR-D, a directed and weighted k -nearest neighbor graph is first constructed by treating each sample (i.e., each vibration signal segment) as a vertex, in which the similarity between samples is measured by cosine distance instead of the commonly used Euclidean distance, and the edge weights are also defined by cosine distance instead of the commonly used heat kernel. Then, the labels of samples are considered as the graph signals indexed by the vertices of the representation graph. Finally, the states of unlabeled samples are predicted by finding a graph signal that has minimal total variation and satisfies the constraint given by labeled samples as much as possible. Experimental results indicate that GSR-D is better and more stable than the standard convolutional neural network and support vector machine in rolling bearing fault diagnosis, and GSR-D only has two tuning parameters with certain robustness.", "Keywords": "Fault diagnosis ; Rolling bearings ; Graph shift regularization ; Directed graphs ; Convolutional neural network ; Support vector machine", "DOI": "10.1016/j.aei.2021.101253", "PubYear": 2021, "Volume": "47", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Design and Manufacturing for Vehicle Body, Hunan University, Changsha 410082, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Design and Manufacturing for Vehicle Body, Hunan University, Changsha 410082, PR China;Corresponding author"}], "References": [{"Title": "Discriminative manifold random vector functional link neural network for rolling bearing fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> Hu", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106507", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": ********, "Title": "Mixed integer non-linear programming model for reliable and safer design at an early stage", "Abstract": "We present a Mixed Integer Non-Linear Programming (MINLP) model capable of choosing the best design considering economic profit, availability, and safety. The model takes into account the probability of suffering a failure in a year of operation, as well as the revenue generated and the probability of the process units of being in a non-functional state. The inclusion of programmed maintenances of a specified duration is considered in the model, assuming an equal distribution in the maintenances time. The performance of the model is illustrated by small examples to help the reader to better understand the model, before applying it to the methanol synthesis case study, where the economic and safety objectives are represented in a Pareto front. The results showcase the possibility of considering safety during the early design stage.", "Keywords": "Reliability ; Process safety ; Mixed integer non-linear programming", "DOI": "10.1016/j.compchemeng.2021.107256", "PubYear": 2021, "Volume": "147", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Chemical Process Engineering, University of Alicante, PO 99, E-3080 Alicante, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Chemical Process Engineering, University of Alicante, PO 99, E-3080 Alicante, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Chemical Process Engineering, University of Alicante, PO 99, E-3080 Alicante, Spain"}], "References": []}, {"ArticleId": 86624140, "Title": "Development of an empirical relation to assess soil spatial variability for off-road trafficability using terrain similarity analysis & geospatial data", "Abstract": "Soils trafficability is measure of soil strength required to support the manoeuvring of military vehicles. Trafficability can be measured with the tests performed using cone penetrometer and moisture metre to understand the complexity of phenomenon. Empirical relation can be developed through the outcomes of these tests. This empirical relation can be used in tandem with geospatial data to understand the heterogeneous soil behaviour and its impact on trafficability. Further, it will enhance the remote sensing data capability to assess the trafficability in inaccessible areas. In this study, a numerical approach is described in which geo-parameters like land use, soil, elevation and moisture are quantified using various data sources such as remote sensing and ground survey information. Terrain similarity analysis was performed to find analogous soil patches of homogenous nature. The aim of this paper is to outline a procedure, which will be useful for off-road trafficability studies. The current advancement in remote sensing technologies and ground survey from digital equipment was used to have better accuracy of results along with better understanding of utilization of geo-spatial data. It will pave the way to analyse the terrain suitability for off-road trafficability operation. Acknowledgments I am thankful to Director, Defence Terrain Research Laboratory, Delhi, who has given me the opportunity to work in this area. Disclosure statement There is no conflict of interest among all co-authors.", "Keywords": "", "DOI": "10.1080/2150704X.2021.1880657", "PubYear": 2021, "Volume": "12", "Issue": "3", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Defence Terrain Research Laboratory,  Defence Research and Development Organization (DRDO) , New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering,  Indian Institute of Technology , Roorkee, India"}], "References": [{"Title": "Development of mapping techniques for off road trafficability to support military operation", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "4", "Page": "495", "JournalTitle": "Spatial Information Research"}]}, {"ArticleId": 86624389, "Title": "Customizing SVM as a base learner with AdaBoost ensemble to learn from multi-class problems: A hybrid approach AdaBoost-MSVM", "Abstract": "Learning from a multi-class problem has not been an easy task for most of the classifiers, because of multiple issues. In the complex multi-class scenarios, samples of different classes overlap with each other by sharing attribute, and hence the visibility of least represented samples decrease even more. Learning from imbalanced data studied extensively in the research community, however, the overlapping issues and the co-occurrence impact of overlapping with data imbalance have received comparatively less attention, even though their joint impact is more thoughtful on classifiers’ performance. In this paper, we introduce a modified SVM, MSVM to use as a base classifier with the AdaBoost ensemble classifier (MSVM-AdB) to enhance the learning capability of the ensemble classifier. To implement the proposed technique, we divide the multi-class dataset into overlapping and non-overlapping region. The overlapping region is further filter into the Critical and less Critical region depending upon their sample contribution in the overlapped region. The MSVM is designed to map the overlapped samples in a higher dimension by modifying the kernel mapping function of the standard SVM by using the mean distance of the Critical region samples. To highlight the learning enhancement of the MSVM-AdB, we use 20 real datasets with varying imbalance ratio and the overlapping degree to compare the significance of the AdaBoost-MSVM with the standard SVM, and AdaBoost with standard base classifiers. Experimental results show the superiority of the MSVM-AdB on a collection of benchmark datasets to its standard counterpart classifiers.", "Keywords": "Machine learning classifiers ; Class overlapping ; Imbalanced distribution of data ; Imbalanced problem ; Decomposition techniques", "DOI": "10.1016/j.knosys.2021.106845", "PubYear": 2021, "Volume": "217", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Gujrat, Pakistan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSAT University Islamabad, Pakistan"}], "References": [{"Title": "Optimising Prediction in Overlapping and Non-Overlapping Regions", "Authors": "Sumana B.V.;  <PERSON><PERSON><PERSON><PERSON><PERSON> M.", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "45", "JournalTitle": "International Journal of Natural Computing Research"}, {"Title": "HBoost: A heterogeneous ensemble classifier based on the Boosting method and entropy measurement", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "113482", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 86624553, "Title": "Decision Supporting System For Malang City Baseball and Softball Athletes Selection Using The Vikor Method", "Abstract": "<p>Decision support system is a computer-based system that is used to solve problems by semi-structured and unstructured conditions. In this case, the best solution can be found from certain criteria and provided alternatives and is easy to use by users. In this study, researchers designed and built a website-based decision support system to select athletes in PERBASASI Malang with the provided test criteria, namely the hit test, catch test, throw test, and run test. The purpose of this study was to provide recommendations for participants who passed the selection based on test scores that have been processed using the VlseKriterijumska Optimizacija I Kompromisno Resenje (VIKOR) method. From the results of calculations, the use of the VIKOR method for this case study produced an accuracy value of 90.90%, a precision value of 93.33%, and a recall value of 93.33%. In addition, the VIKOR sensitivity test showed a consistent ranking of the calculation of the value with veto (value of v less than 0.5), by consensus (value of v is 0.5), and voting by majority rule (value of v more than 0.5). User testing that was applied by using each level of the existing account, i.e. administrator account level, selection account level, and members account level conducted on this information system concluded that the system that was built was running smoothly and was easy to use.</p>", "Keywords": "Decision Support System;Athlete Selection;Baseball;Softball;PERBASASI;VlseKriterijumska Optimizacija I Kompromisno Resenje;VIKOR Method", "DOI": "10.21107/ijseit.v5i1.8101", "PubYear": 2020, "Volume": "5", "Issue": "1", "JournalId": 79522, "JournalTitle": "International journal of science, engineering, and information technology", "ISSN": "", "EISSN": "2548-4214", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Polytechnic of Malang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Polytechnic of Malang"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Polytechnic of Malang"}], "References": []}, {"ArticleId": ********, "Title": "STRATEGY SURVIVELY IN THE MIDDLE OF UNCERTAINITY PANDEMI COVID-19 MARITIME SECTOR BUSINESS", "Abstract": "<p>The Covid-19 pandemic crisis of 2020, different from previous crisis / disasters, given the uncertainty, no one can predict or look ahead, when the end of this crisis is over. Global impact that touches almost all sectors and is directly related personally and family. Fast-moving events, we talk day by day, with a situation that is changing and unprecedented. This requires us to make policies, responses and rapid mitigation of impacts on shared covid-19. Management policies must reach the infrastructure, work and engagement sectors required to be fast and appropriate. In dealing with such situations, it is necessary to apply crisis management, so that the main things that must be prioritized can be found. The government is expected to provide fiscal incentives, especially to national companies, in the form of tax breaks, small loans, or other assistance. So that the company can survive to avoid layoffs and be able to rise again after going through this crisis.</p>", "Keywords": "", "DOI": "10.21107/ijseit.v5i1.7288", "PubYear": 2020, "Volume": "5", "Issue": "1", "JournalId": 79522, "JournalTitle": "International journal of science, engineering, and information technology", "ISSN": "", "EISSN": "2548-4214", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hang Tuah University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> G<PERSON>", "Affiliation": "Sepuluh Nopember Institute of Technology"}], "References": []}, {"ArticleId": 86624610, "Title": "Method of differential privacy protection for web-based communities based on adding noise to the centroid of positions", "Abstract": "The research of location privacy protection has a very important significance for the emergence and sustenance of web-based communities in the big data era. The method of differential privacy for position is a privacy model strictly for the measurement of privacy. However, under the privacy protection for multi-positions, it will bring much bigger errors by adopting the method of adding noise to protect the privacy of multi-positions. In this paper, the author puts forward a new method of differential privacy for multi-positions based on mechanism of adding noise to the centroid of positions and compares with the independent mechanism of adding noise. The experiment shows that the centroid mechanism of adding noise is better than the mechanism of independently adding noise. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Centroid; Location-based services; Multi-positions; Privacy; Privacy protection; Web-based communities", "DOI": "10.1504/IJWBC.2021.112855", "PubYear": 2021, "Volume": "17", "Issue": "1", "JournalId": 9397, "JournalTitle": "International Journal of Web Based Communities", "ISSN": "1477-8394", "EISSN": "1741-8216", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Nanchang University, Nanchang, Jiangxi, 330031, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Software, Nanchang University, Nanchang, Jiangxi, 330047, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Software, Nanchang University, Nanchang, Jiangxi, 330047, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Nanchang University, Nanchang, Jiangxi, 330031, China"}], "References": []}, {"ArticleId": 86624611, "Title": "Remote working in the time of covid-19: developing a web-based community", "Abstract": "The purpose of this article is to provide suggestions for the implementation of remote working under enforced lockdown as experienced in many countries facing the consequences of the covid-19 virus. The author addresses the singularities and challenges of implementing remote working as a consequence of lockdown. Several measures for organisations are proposed, like the technological implementation of the technology and the importance of maintaining a routine to provide continuity, but also the need to be flexible towards the employees, the need to keep and adapt quality standards and to trust the motivation of the employees to do a good job. From an employee perspective, there is the need to create a personal routine, to set rules and have a working space, to use the right tools to communicate with colleagues and show up for online meetings, remaining in a positive frame of mind. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Covid-19; Lockdown; Remote working; Teleworking", "DOI": "10.1504/IJWBC.2021.112862", "PubYear": 2021, "Volume": "17", "Issue": "1", "JournalId": 9397, "JournalTitle": "International Journal of Web Based Communities", "ISSN": "1477-8394", "EISSN": "1741-8216", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Zayed University, P.O. Box 19282, Dubai, United Arab Emirates"}], "References": []}, {"ArticleId": 86624614, "Title": "The Impact of Emerging Wireless Network System and Cybersecurity in A Global Community", "Abstract": "<p>Wireless network systems and cybersecurity threats are growing faster than their mitigation measures. World Economic Forum has identified wireless network security and cybersecurity threats as its top global risks for the past eight years. This paper aims to critically examine the impact of emerging wireless network systems and cybersecurity in a global community and suggest some best countermeasures against wireless and cybersecurity threats that have been of global concern. To achieve this, the study conducted an in-depth review of wireless network security and cybersecurity.  This study has presented a robust wireless security mechanism and suggests appropriate countermeasure against wireless network and cybersecurity threats that is more cost-effective in mounting attacks in the service area, and simultaneously providing higher security than basic security mechanisms.</p>", "Keywords": "Wireless Network System, Cybersecurity, Global Community, Cyber-attacks,                            Fusion centers, Collaborative.", "DOI": "10.14738/tnc.85.9628", "PubYear": 2020, "Volume": "8", "Issue": "5", "JournalId": 37896, "JournalTitle": "Transactions on Networks and Communications", "ISSN": "", "EISSN": "2054-7420", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Calabar"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Calabar, Calabar, Cross River State - Nigeria"}, {"AuthorId": 3, "Name": "Obono, I. O", "Affiliation": "Department of Computer Science, University of Calabar, Calabar, Cross River State - Nigeria"}], "References": []}, {"ArticleId": 86624615, "Title": "Preserving Privacy Location in 5G by Using Variable Pseudonym", "Abstract": "<p>User privacy is one of the most issues addressed extensively in mobile communications evolutions, research literature, and standardization. Location privacy is a key parameter and crucial aspect for user privacy, where most of the tracking, Unsolicited advertising, malicious activities, and location-based terrorism attacks are depending on the location of the victims. For preserving location privacy, various methods in previous mobile networks use a pseudonym instead of permanent identity i.e. Cell Radio Network Temporary Identifiers (C-RNTI), However, these methods based on C-RNII have been proofed that faces many vulnerable due to the clear text used for C-RNII exchange. A man-in-the-middle attack can easily trace users and collect information. The main objective of this paper is to propose a new location privacy algorithm that can greatly enhance the capabilities of the 5G architecture. The proposed algorithm introduces a novel variable pseudonym (V-RNTI) as an identifier for the user radio channel. Also, it provides an enhanced pseudonyms allocation procedure for identification. A new procedure that enabling UE to use different values for V-RNTI changed frequently using agreed equations to generate the values of the identifier. The proposed scheme is compatible with 3GPP standards architecture, where minor modifications/upgrades are needed for UEs eNB. Specifically, we build our model of the 5G V-RNTI authentication protocol and perform an automated security verification tool analysis of the protocol model by using the ProVerif model checker. Our analysis results show that the proposed procedure is working without flaws.</p>", "Keywords": "", "DOI": "10.14738/tnc.85.9571", "PubYear": 2020, "Volume": "8", "Issue": "5", "JournalId": 37896, "JournalTitle": "Transactions on Networks and Communications", "ISSN": "", "EISSN": "2054-7420", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86625005, "Title": "Degrees of sets having no subsets of higher m- and t t-degree", "Abstract": "We consider sets without subsets of higher m- and [Formula: see text]-degree, that we call m-introimmune and [Formula: see text]-introimmune sets respectively. We study how they are distributed in partially ordered degree structures. We show that: each computably enumerable weak truth-table degree contains m-introimmune [Formula: see text]-sets; each hyperimmune degree contains bi- m-introimmune sets. Finally, from known results we establish that each degree a with [Formula: see text] covers a degree containing [Formula: see text]-introimmune sets.", "Keywords": "", "DOI": "10.3233/COM-200296", "PubYear": 2021, "Volume": "10", "Issue": "3", "JournalId": 33294, "JournalTitle": "Computability", "ISSN": "2211-3568", "EISSN": "2211-3576", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Sciences and Technology, Mathematics Division, University of Camerino, 62032 Camerino, Italy. "}], "References": []}, {"ArticleId": 86625305, "Title": "Entropy based enhanced particle swarm optimization on multi‐objective software reliability modelling for optimal testing resources allocation", "Abstract": "<p>This paper proposes a generalization of the exponential software reliability model to characterize several factors including fault introduction and time-varying fault detection rate. The software life cycle is designed based on module structure such as testing effort spent during module testing and detected software faults etc. The resource allocation problem is a critical phase in the testing stage of software reliability modelling. It is required to make decisions for optimal resource allocation among the modules to achieve the desired level of reliability. We formulate a multi-objective software reliability model of testing resources for a new generalized exponential reliability function to characterizes dynamic allocation of total expected cost and testing effort. An enhanced particle swarm optimization (EPSO) is proposed to maximize software reliability and minimize allocation cost. We perform experiments with randomly generated testing-resource sets and varying the performance using the entropy function. The multi-objective model is compared with modules according to weighted cost function and testing effort measures in a typical modular testing environment.</p>", "Keywords": "entropy;imperfect debugging;modular testing;particle swarm optimization;resource allocation;software reliability", "DOI": "10.1002/stvr.1765", "PubYear": 2021, "Volume": "31", "Issue": "6", "JournalId": 9990, "JournalTitle": "Software Testing, Verification and Reliability", "ISSN": "0960-0833", "EISSN": "1099-1689", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, and Department of Mathematics, National Institute of Technology Puducherry, Karaikal, 609609 India"}, {"AuthorId": 2, "Name": "G. <PERSON>. Ma<PERSON>pa<PERSON>", "Affiliation": "Department of Computer Science and Engineering, and Department of Mathematics, National Institute of Technology Puducherry, Karaikal, 609609 India"}], "References": []}, {"ArticleId": 86625377, "Title": "Internet of things skills and needs satisfaction: do generational cohorts' variations matter?", "Abstract": "Purpose The authors’ understanding of the Internet of things (IoT) skills and needs satisfaction for IoT devices and generational cohorts' variations remains limited as commentaries are often oversimplified and generalized. This research fills a gap in the literature by highlighting the dynamics between the IoT skills and needs satisfaction for IoT devices and seeks to expound on the variations among generational cohorts using the self-determination theory. Design/methodology/approach Survey data were obtained from 1,245 residents and IoT device users in Aksaray, Turkey. The obtained data were analyzed with variance-based structural equation modeling and the analysis of variance technique. Findings The results demonstrate that IoT skills determine the needs satisfaction for IoT devices. Generation Xers, Generation Yers and Generation Zers are distinct cohorts with respect to the IoT skills and needs satisfaction for IoT devices. Originality/value Collectively, this study provides empirical evidence that informs the debate about the contributions of IoT skills and generational cohorts on needs satisfaction for IoT devices. The implications and several avenues for future theory-building research are discussed. Peer review The peer review history for this article is available at: https://publons.com/publon/10.1108/OIR-04-2020-0144", "Keywords": "Internet of things;Internet;Smart device;Generation;Needs satisfaction;IoT skills", "DOI": "10.1108/OIR-04-2020-0144", "PubYear": 2021, "Volume": "45", "Issue": "5", "JournalId": 3377, "JournalTitle": "Online Information Review", "ISSN": "1468-4527", "EISSN": "1468-4535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Communication, Bolu Abant İzzet Baysal University , Bolu, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Business and Social Sciences, Antalya Bilim University , Antalya, Turkey"}], "References": []}, {"ArticleId": 86625420, "Title": "An Experimental Study for Tracking Ability of Deep Q-Network", "Abstract": "", "Keywords": "", "DOI": "10.17781/P002679", "PubYear": 2020, "Volume": "10", "Issue": "3", "JournalId": 38494, "JournalTitle": "International Journal of New Computer Architectures and their Applications", "ISSN": "2220-9085", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Masashi SUGIMOTO", "Affiliation": ""}, {"AuthorId": 2, "Name": "Ryunosuke UCHIDA", "Affiliation": ""}, {"AuthorId": 3, "Name": "Kentarou KURASHIGE", "Affiliation": ""}, {"AuthorId": 4, "Name": "Shinji TSUZUKI", "Affiliation": ""}], "References": []}, {"ArticleId": 86625626, "Title": "Computer firewalls: security and privacy protection for Mac—review", "Abstract": "<p>Internet has become the ultimate information resource. It allows both to reach information from the outside, as well as to share inside data. Yet, such power also involves potential weaknesses, in relation to security and privacy protection. Indeed, threats may come from malicious web sites, trying to steal user's data or install unwanted applications. Malware may include adware, Potentially Unwanted Applications (PUA) or Potentially Unwanted Programs (PUP), and even ransomware. Another menace may even come from otherwise bona fide applications installed by the user. Thus, such applications may send private data to external servers, without the user's consent and knowledge. Therefore, a proper firewall is required to protect the privacy of users. Indeed, they are built inside the operating systems, as basic security tools. Third party companies offer more comprehensive firewalls, including free and nonfree ones. This review analyzes the firewalls available for Mac (macOS) computers. As a practical example, applications in scientific research, in general, as well as bioinformatics, in particular, are described. Last but not least, Internet security and privacy protection is a work in progress, and as such, users should know about this critical fact, and take proper action, installing and updating appropriate firewalls.</p> \t\t -->", "Keywords": "Review;World Wide Web (WWW), web browser, electronic mail (e-mail, eMail), Graphical User Interface (GUI),", "DOI": "10.3934/bdia.2021001", "PubYear": 2021, "Volume": "6", "Issue": "", "JournalId": 54880, "JournalTitle": "Big Data & Information Analytics", "ISSN": "2380-6966", "EISSN": "2380-6974", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Bioquímica y Biología Molecular, Campus Rabanales C6-1-E17, Campus de Excelencia Internacional Agroalimentario (ceiA3), Universidad de Córdoba, 14071 Córdoba, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departamento de Lenguajes y Ciencias de la Computación, ETSI Informática, Campus de Teatinos, Universidad de Málaga, 29071 Málaga, Spain"}, {"AuthorId": 3, "Name": "<PERSON> del Pilar <PERSON>", "Affiliation": "Departamento de Bioquímica y Biología Molecular, Campus Rabanales C6-1-E17, Campus de Excelencia Internacional Agroalimentario (ceiA3), Universidad de Córdoba, 14071 Córdoba, Spain;Departamento de Lenguajes y Ciencias de la Computación, ETSI Informática, Campus de Teatinos, Universidad de Málaga, 29071 Málaga, Spain;Departamento de Química Física y Termodinámica Aplicada, <PERSON><PERSON> <PERSON>, Campus de Rabanales, Universidad de Córdoba, Campus de Excelencia Internacional Agroalimentario, ceiA3, 14071 Córdoba, Spain"}], "References": []}, {"ArticleId": 86625896, "Title": "Effect of Social Environment on Brand Recall in Sports Video Games", "Abstract": "<p>Studies have shown that in-game advertisements can be effective. However, these studies typically examine single player scenarios. This study aimed to investigate the effects of social dynamics on brand awareness of in-game advertisements in sports video games. Two studies were conducted with soccer and basketball simulation games. In each study, participants were split into two groups where they either played against a computer-controlled opponent or against another player. For both studies, independent-samples t-tests were conducted to compare the recall rates between both groups. Both studies showed similar findings where respondents in the single player group reported higher recall and recognition rates when compared to respondents in the multi-player group. These findings suggest that the social environment can affect the effectiveness of in-game advertisements.</p>", "Keywords": "", "DOI": "10.4018/IJGCMS.20210101.oa1", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 26182, "JournalTitle": "International Journal of Gaming and Computer-Mediated Simulations", "ISSN": "1942-3888", "EISSN": "1942-3896", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Nanyang Technological University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "St Joseph's Institution, Singapore"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "River Valley High School, Singapore"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Nanyang Technological University, Singapore"}], "References": []}, {"ArticleId": 86625936, "Title": "Using 360-Video Virtual Reality to Influence Caregiver Emotions and Behaviors for Childhood Literacy", "Abstract": "<p>Through the design and exploratory evaluation of a narrative-based 360-video virtual reality experience, the authors aimed at building empathy in adults towards children who experience challenges in early literacy. This contributes to a limited literature on VR empathetic design by specifically studying caregivers in relation to reading difficulties and utilizing a low-cost immersive medium. This research performed a quasi-experimental pilot study following a pretest-posttest design with 27 participants, collecting measures such as participant empathy, anxiety, immersion, and emotional reactions. This paper explored changes in pre-post measures, correlations between variables, and possible explanations for the observed results. The VR experience increased positive caregiver attitudes towards struggling readers. Participants who reported a high degree of emotional reactions showed increased willingness to donate to help reading difficulties. Participants with teaching experience or with lower starting empathy scores were less likely to be affected.</p>", "Keywords": "", "DOI": "10.4018/IJGCMS.20210101.oa2", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 26182, "JournalTitle": "International Journal of Gaming and Computer-Mediated Simulations", "ISSN": "1942-3888", "EISSN": "1942-3896", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harvard University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Harvard University, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Harvard University, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harvard University, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Harvard University, USA"}], "References": []}, {"ArticleId": 86625959, "Title": "The Effects of Rewards on Autonomous Unmanned Aerial Vehicle (UAV) Operations Using Reinforcement Learning", "Abstract": "The effects of rewards on the ability of an autonomous UAV controlled by a Reinforcement Learning agent to accomplish a target localization task were investigated. It was shown that with an increase in the reward obtained by a learning agent upon correct detection, systems would become more risk-tolerant, efficient and have a tendency to locate targets faster with an increase in the sensor sensitivity after systems achieve steady-state performance.", "Keywords": "", "DOI": "10.1142/S2301385021500187", "PubYear": 2021, "Volume": "9", "Issue": "4", "JournalId": 26691, "JournalTitle": "Unmanned Systems", "ISSN": "2301-3850", "EISSN": "2301-3869", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86626078, "Title": "A general architecture for a real-time monitoring system based on the internet of things", "Abstract": "Recently there has been significant progress in the real-time monitoring system based on the Internet of Things (IoT). The use rate of IoT has been increasing exponentially because of its enormous application in different areas, and many of them are yet to be explored. This paper explains how to design an IoT system and describes its working mechanism. We present a general architecture of the real-time monitoring system using IoT and related services. We successfully implement our proposed architecture for a single domain. Then, we describe how to use the proposed architecture to monitor the different real-time contextual domains. Also, we present ideas on how to plug the data from a third-party application into the proposed architecture. Introduction The Internet of Things (IoT) is a new type of smart technology that interconnects various objects such as sensors, actuators, vehicles, homes, equipments, and appliances, together with the Internet. 2nd The combination of different objects is tremendously useful in real-world applications. In an IoT system, sensors are at the bottom level to collect real-time data from the surrounding environment. After that, there is an IoT gateway that receives and collects the data from the sensor, maybe performs preliminary computations on raw data, and then sends semi-purified (or raw) data to the main server. The main server is located on the cloud and performs the computations on the data received from the IoT gateway, then provides meaningful results based on different analytic tools and techniques. At the top, client devices, such as the smartphone, tablet, and personal computer (PC), are used to monitor and control the real-time events. The main server can also control the real-time events automatically by implementing the decision-making algorithms. Many researchers are attracted towards IoT because of its influential communication paradigms [2], [3], and the exponential growth of its use in various real-world applications, for instance, home automation systems [4], social services [5], medical systems [6], [7], [8], supply chains [9], environment monitoring [10], [11], energy saving [12], industries [13], manufacturing [14], [15], [16], and many more [17], [18], [19], [20]. A group of researchers worked together and developed an IoT system that gives a solution to a particular problem, which is based on a given specification or a defined context. An architectural solution provided by a group of researchers may not be suitable for other research groups even though they are working on the same problem domain because of their architectural concepts and functional layers. Moreover, the possibility of integration is less likely if they are researching in different domains. The challenges faced by novice researchers are how to get an entry in the IoT and related research. Similarly, for the experienced researcher, how is it possible to expand their existing research idea and methodology from one contextual domain to the other domains? Similarly, how can they integrate other IoT systems to their system? Also, for the industry, what would be the common architectural framework that will cover many of the IoT systems? In this paper, we review the recently available architecture of real-time monitoring IoT systems, which are implemented or proposed for a specific domain. After reviewing, we notice that their architectures have some structures in common even though their working domain and implementation styles are different. Most of the real-time IoT systems do not explain how to integrate their system into systems designed by others and how to plugin third-party systems into theirs. A few IoT systems use artificial intelligence (AI) and machine learning (ML) algorithms in a defined contexts. Nevertheless, it is hard to find out whether is it possible to extend the use of those AI and ML into other contexts or not. Similarly, it is difficult to discover out where to implement business logic. Is it on the client application or on the cloud? The goal of this paper is to discover the common behaviors of different IoT systems and provide a general architecture of an IoT system, which can work in various domains. We provide a detailed explanation of each layer of our proposed architecture and explain how to integrate this architecture with others IoT systems. We also describe the plug-in mechanism to fit others’ system data to our designed system. We give ideas where and how to use business logic, AI and ML algorithms. We believe that our proposed architecture will be a common architecture for various types of IoT systems. Moreover, we provide an example that follows the proposed architecture and works successfully for a single context and then explain how to use the proposed architecture for any other context. This paper helps novice researchers understand how IoT systems are designed and functionally implemented. Similarly, experienced researchers can integrate their idea to our proposed architecture (and vice-versa), and extend their research horizon. Also, industry can use our proposed architecture in their IoT systems to cover many contextual domains according to the business requirements. The remainder of this paper is organized as follows. In Section 2, we review the architectural design of the recent real-time monitoring IoT systems. Section 3 describes a general architecture. Section 4 explains our proposed architecture, and gives an example of a working IoT system. Then, in Section 5, we provide detailed steps of procedure for the implementation. We also explain how to integrate our architecture with other systems. Finally, the conclusion and future works are summarized in Section 8. Figures General overview of an IoT system. Functions of cloud platforms. Layers of a General architecture of an IoT system. sensor data format that we used in our implementation for communication. IsMalfunctioned(D(tk),M〈D(tis)〉,listSize). Section snippets Literature review An IoT system is designed to monitor the defined contextual scenario. We reviewed many IoT system architectures that work for different environmental settings, such as home automation, medical care, ecological and environmental monitoring, agriculture and farming process control and industrial manufacturing to the supply chain activity. General architecture We reviewed various types of IoT systems which are proposed or implemented in different real-time monitoring environments in Section 2. In this section; we isolate their common features, and then propose a general architecture that can serve as the standard architecture for any type of domain. We also explain the plug-in mechanism for the specific requirements. We noticed that all IoT systems have sensors (or actuators) at the perception layer. They are connected to the interfaces, and the Proposed architecture We described the key ingredients of an IoT system and explained how they work together to monitor the real-time environment. We formalize them in different layers so that a general architecture of the real-time monitoring system based on the IoT is formed. Our general architecture has four layers: perception layer, gateway and communication layer, cloud computation and service layer, and application layer. See Fig. 3. Implementation We implemented our proposed architecture and passed the testing operation successfully<sup>1</sup>. For the implementation, we developed a real-time monitoring system of room temperature. However, we also explain how we can use this architecture for other contextual domains, later in Section 6. We selected a DHT11 temperature-humidity sensor and a Raspberry Pi 3 as an interface. Similarly, we chose a personal computer for an IoT gateway, Extension We can also use our proposed architecture in other contextual domains. In this section, we will explain how we can use our proposed architecture in various domains. We also describe how to integrate our proposed architecture into the existing IoT system or other systems. Challenges and solutions Every IoT system has specific challenges according to its functional domain. However, there are a few common challenges, which are faced by most of the IoT systems. We have listed a few of them as follows. Conclusion In this paper, we explained the general structure of an IoT system and its working mechanism. We reviewed many different real-time monitoring systems based on IoT that are working for various contextual domains and extract their common behaviors. We also described the different layers of an IoT system and proposed a general architecture of an IoT system for a real-time monitoring system. After that, we provided an example to explain how to implement our architecture and make it functional in a Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. References (71) K. Zheng et al. Challenges of massive access in highly dense LTE-advanced networks with machine-to-machine communications IEEE Wireless Commun. (2014) J.C. Bezdek et al. Generalized k-nearest neighbor rules Fuzzy Sets Syst. (1986) S. Mika, G. Rätsch, J. Weston, B. Schölkopf, K.-R. Müller, Fisher Discriminant Analysis With Kernels,... S.B. Kotsiantis Decision trees: a recent overview Artif. Intell. Rev. (2013) J. Ravichandran et al. Data validation algorithm for wireless sensor networks Int. J. Distrib. Sens. Netw. (2013) J. Jermyn et al. Scalability of Machine to Machine systems and the Internet of Things on LTE mobile networks Proceedings of the IEEE 16th International Symposium on A World of Wireless, Mobile and Multimedia Networks (WoWMoM) (2015) W. He et al. Developing vehicular data cloud services in the IoT environment IEEE Trans. Ind. Inf. (2014) a I.B.M Developer, Scan your app to find and fix OWASP Top 10 – 2017 vulnerabilities, ?... E. Oyekanlu Predictive edge computing for time series of industrial IoT and large scale critical infrastructure based on open-source software analytic of big data Proceedings of the IEEE International Conference on Big Data (Big Data) (2017) N. Paudel et al. A general architecture for a real-time monitoring system based on the internet of things Proceedings of the 3rd International Symposium on Computer Science and Intelligent Control (2019) Domo, Data Never Sleeps 2.0, 2020,... L. Liu et al. SCout: Prying into supply chains via a public query interface IEEE Syst. J. (2016) S. Hawkins et al. Outlier detection using replicator neural networks D. Zhang et al. Real-Time locating systems using active RFID for internet of things IEEE Syst. J. (2016) B.-G. Chun et al. CloneCloud: Elastic Execution between Mobile Device and Cloud Proceedings of the Sixth Conference on Computer Systems (2011) L.D. Xu et al. Internet of things in industries: A Survey IEEE Trans. Ind. Inf. (2014) S. Yi et al. Fog Computing: Platform and Applications Proceedings of the Third IEEE Workshop on Hot Topics in Web Systems and Technologies (HotWeb) (2015) F. Tao et al. CCIoT-CMfg: Cloud computing and internet of things-Based cloud manufacturing service system IEEE Trans. Ind. Inf. (2014) K. Ha et al. Towards Wearable Cognitive Assistance Proceedings of the 12th Annual International Conference on Mobile Systems, Applications, and Services (2014) F. Paganelli et al. A web of things framework for RESTful applications and its experimentation in a smart city IEEE Syst. J. (2016) R. Pérez-Chacón et al. Big data time series forecasting based on pattern sequence similarity and its application to the electricity demand Inf. Sci. (Ny) (2020) Y. Liang et al. An integrated approach of sensing tobacco-Oriented activities in online participatory media IEEE Syst. J. (2016) K. Rieck et al. Linear-time computation of similarity measures for sequential data J. Mach. Learn. Res. (2008) M. Domb Smart Home Systems Based on Internet of Things (2019) S.S. Manvi et al. Resource management for infrastructure as a service (iaas) in cloud computing: a survey J. Netw. Comput. Appl. (2014) T. Datta et al. A developer-friendly library for smart home IoT privacy-preserving traffic obfuscation Proceedings of the Workshop on IoT Security and Privacy (2018) A.J. Ferrer et al. Multi-cloud platform-as-a-service model, functionalities and approaches Procedia Comput. Sci. (2016) V.C. Gungor et al. Challenges and Issues in Designing Architectures and Protocols for Wireless Mesh Networks (2008) A. Amiri Application placement and backup service in computer clustering in software as a service (saas) networks Comput. Operat. Res. (2016) Y. Ye et al. SHVC, The scalable extensions of HEVC, and its applications (2016) T.N. Gia et al. Fault tolerant and scalable IoT-based architecture for health monitoring Proceedings of the IEEE Sensors Applications Symposium (SAS) (2015) K. Chung et al. Knowledge-based health service considering user convenience using hybrid Wi-Fi P2P Inf. Technol. Manag. (2016) Y. Huang et al. Naive Bayes classification algorithm based on small sample set Proceedings of the IEEE International Conference on Cloud Computing and Intelligence Systems (2011) M. Fahim et al. A sleep monitoring application for u-lifecare using accelerometer sensor of smartphone A.P. Plageras et al. IoT-based surveillance system for ubiquitous healthcare Proceedings of the IECON 42nd Annual Conference of the IEEE Industrial Electronics Society (2016) View more references Cited by (0) Recommended articles (6) Research article An HMAC-based authentication scheme for network coding with support for error correction and rogue node identification Journal of Systems Architecture, Volume 116, 2021, Article 102051 Show abstract Authentication schemes based on homomorphic message authentication codes (HMACs) with or without a homomorphic cryptographic signature (HCS) have always played a vital role in network coding (NC). A newly developing trend with respect to research in this area takes authentication in NC-compatible networks beyond error detection. Current HMAC-based authentication schemes for networks that support NC not only detect and drop corrupted packets but also identify the rogue nodes that begun the attack. However, in order to completely optimize the performance of an NC-enabled network, nodes that identify corrupted packets should also be able to correct the errors. Doing this eliminates the communication cost associated with the need for re-transmission and improves throughput. In this paper we propose what to the best of our knowledge is the first of such HMAC-based authentication schemes. Like all current state of the art HMAC-based authentication schemes, the proposed scheme is able to identify pollution attacks and the nodes that launched them. Furthermore, the proposed scheme is also able to correct the corruption. This according to our evaluation improves the throughput of the network when compared to other similar state of the art schemes that do not possess this feature. Research article Dead ringers? Legal persons and the deceased in European data protection law Computer Law & Security Review, Volume 40, 2021, Article 105495 Show abstract Notwithstanding suggestions that the concrete treatment of legal and deceased person data during European data protection's development has been broadly comparable, this article finds that stark divergences are in fact apparent. Justification for the inclusion of both categories has rested on a claimed linkage to living natural person interests. However, despite early fusion, legal persons have been increasingly seen to have qualitatively different information entitlements compared to natural persons, thereby leaving European data protection with a very limited and indirect role here. In contrast, living natural persons and the deceased have not been conceived as normatively dichotomous and since the 1990s there has been growing interest both in establishing sui generis direct protection for deceased person data and also indirect inclusion through a link with living natural persons. Whilst the case for some indirect inclusion is overwhelming, a broad approach to the inter-relational nature of data risks further destabilizing the personal data concept even in relation to living persons alone. Given that jurisdictions representing almost half of the EEA's population now provide some direct protection and the challenges of managing digital data on death continue to grow, the time may be ripe for a ‘soft’ recommendation on direct protection in this area. Drawing on existing law and scholarship, such a recommendation could seek to specify the role of both specific control rights and diffuse confidentiality obligations, the criteria for time-limits in each case and the need for a balance with other rights and interests which recognises the significantly decreasing interest in protection over time. Research article Explaining the persistence of “decentralisation” of education in Egypt International Journal of Educational Development, Volume 82, 2021, Article 102357 Show abstract This study examines how and why decentralisation remained central to education reform in Egypt over the period 1990–2016 despite the mixed outcome at both the national and international levels. Three decentralisation models were examined: community schools, public-private partnerships and school-based management. Network analysis was combined with a process-tracing approach to identify the key actors and mechanisms causing policy persistence. Analysis revealed international agencies used coercion through funding and persuasion by framing the models to match political sponsors’ interests. There was also bounded-rational learning by national actors from foreign experts and experiences and several complementary mechanisms with minor influence. Research article Examining institutional effects on B2B relationships through the lens of transitioning economies Industrial Marketing Management, Volume 93, 2021, pp. 221-234 Show abstract Our study addresses the limited attention paid to the role of indigenous institutional environments in framing the legitimate forms of governance used to shape buyer-supplier exchanges. Drawing on institutional theory and marketing channel literature, the study suggests that the emphasis in buyer-supplier exchanges on communication modalities and norms has much to do with the effectiveness of legal systems in a buyer's country. Three contexts for legitimacy are conceptualized from an examination of exchanges in developed economies with strong formal institutions and transitioning economies with underdeveloped institutions. Using surveys of US buyers to represent strong formal institutional environments, we conclude that buyers heavily influenced by regulative enforcement place a relatively greater emphasis on formal information sharing in their partnership efforts to build trust and enhance supplier performance. From surveys of buyers in India and China, we conclude that buyers place a relatively greater emphasis on informal information sharing when their legitimacy derives from the endogenously enforced moral codes of their private networks. Finally, surveys administered in Brazil and Russia revealed that buyers in environments pressured by the familial loyalty practices of their private networks place a relatively greater emphasis on the norm of solidarity to build trust in their suppliers. Research article An intelligent VegeCareAI tool for next generation plant growth management Internet of Things, Volume 14, 2021, Article 100381 Show abstract In the field of agriculture, there are many individual micro businesses with low investment capacity and awareness of IT utilization, and it is difficult to obtain a return on investment. In recent years, edge computing and Artificial Intelligence (AI) technologies have attracted a lot of attention in the agricultural industry to cover the labor shortage. Also, safer vegetables are required by peoples due to COVID-19 epidemic and radioactive pollution. In this paper, we propose an agricultural support system called VegeCareAI for agricultural workers. The proposed system supports vegetable classification, plant disease classification and insect pest classification to improve the crops’ productively. The support system can show the growth condition of vegetables. When there are some problems, the VegeCareAI presents information on how to deal with diseases and insect pests. From the results, we found that our proposed VegeCareAI tool has advantage for supporting several crops. For vegetable classification, our training data for 300 epochs predicted six kinds of vegetables correctly. For plant disease classification, for 400 epochs the accuracy is more than 96% accuracy for both potato and corn leaves. For insect pest classification, the accuracy of corn insect pests is more than 73%, but the results of different life cycles showed low classification accuracy, which present a future challenge. Research article THINKING STRATEGICALLY ABOUT BLOCKCHAIN ADOPTION RISKS and RISK MITIGATION Business Horizons, 2021 Show abstract Blockchain technologies are quickly changing the competitive business landscape. Companies need to think strategically about how to best prepare for a future in which they might systematically enhance value and unleash new value using blockchain. In this article, we address how cooperating companies jointly create unique value with blockchain technology, the risks they face along the way, and how they can mitigate those risks. We briefly identify three different reasons a company might adopt blockchain – to enhance value creation, to strengthen existing value ecosystems, or to create new value ecosystems. Then, we identify three strategic risks of blockchain adoption as they relate to business issues, legal issues, and technological issues. Finally, we highlight four different forms of strategic maneuvers for minimizing these risks. The strategic maneuvers include the all-industry approach, the walled garden approach, the many gardens approach, and the options approach. We provide prescriptive advice to managers on how to strategically think about blockchain adoptions, how to identify the underlying risks, and how to consider strategic approaches to mitigate their adoption risk. <sup> ☆ </sup> A preliminary version of this paper was presented at the 2<sup>nd</sup>, or second International Conference on Cyber Physical Systems and IoT(CPSIOT 2019) and published on the Proceeding series ISCSIC 2019 of the ACM International Conference Proceeding Series (ICPS) [1] <sup> </sup> The code (and data) in this article has been certified as Reproducible by Code Ocean: ( https://codeocean.com/ ). More information on the Reproducibility Badge Initiative is available at https://www.elsevier.com/physical-sciences-and-engineering/computer-science/journals . View full text © 2021 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.iot.2021.100367", "PubYear": 2021, "Volume": "14", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gran Sasso Science Institute, Viale Francesco Crispi, 7, L’Aquila, AQ 67100, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "Ram <PERSON>", "Affiliation": "Texas A&M University-Texarkana, 7101 University Ave, Texarkana, TX 75503, USA"}], "References": [{"Title": "Big data time series forecasting based on pattern sequence similarity and its application to the electricity demand", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; F<PERSON>", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "160", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 86626082, "Title": "Sistem Informasi Penjualan Telur pada Peternakan Ayam CV Sinar Jaya Berbasis Web", "Abstract": "Perkembangan teknologi saat ini yang semakin berkembang dan juga perkembangan sistem informasi yang sering dibutuhkan di era modern ini, pendapatan sama juga mengalami perkembangan dengan bertujuan untuk mengatur perkembangan keuangan suatu usaha agar lebih efisien. Dengan diterapkannya Sistem Informasi Penjualan Telur pada Peternakan Ayam CV Sinar Jaya Berbasis Web diharapkan setiap telur yang terjual, data telur tersebut dapat tersimpan tanpa harus menulis di buku dikarenakan usaha tersebut belum menerapkan penggunaan komputer. Tujuan penelitian ini adalah dapat menghasilkan pendapatan penjualan sistem informasi berbasis web yang menggunakan MySQL sebagai databasenya dan PHP sebagai bahasa pemrogramannya. Berdasarkan masalah tersebut, di rancang sebuah sistem rekapitulasi penjualan telur yang dimana lebih memudahkan admin atau user untuk mengelola usaha tersebut agar mudah, efisien dan mempunyai gambaran untuk mengetahui pendapatan penjualan secara berkala yang diperoleh dari sistem informasi rekapitulasi penjualan.", "Keywords": "Sistem Informasi;Peternakan <PERSON>;PHP;MySQL", "DOI": "10.30742/melek-it.v6i2.349", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "Bima Mukt<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626091, "Title": "SISTEM INFORMASI ADMINISTRASI LANSIA DI KELURAHAN BABAT JERAWAT SURABAYA BERBASIS WEB", "Abstract": "Sistem Informasi Administrasi Lansia di Kelu<PERSON>an <PERSON>, Kecamatan Pakal, surabaya adalah sebuah sistem untuk memudahkan para kader agar tidak lagi terjadi kesalahan pencatatan data masyarakat (lansia) dan data kegiatan. Sistem yang sekarang dilakukan masih manual dan masih sering terjadi kesalahan dalam pencatatan data masyarakat (lansia) dan data Kegiatan. Penulis merancang membuat aplikasi yang dapat memberi kemudahan dalam pengolahan data masyarakat (lansia) dan data kegiatan lansia di Kelurahan Babat Jerawat, Kecamatan Pakal, Surabaya. Berdasarkan hasil ujicoba sistem dapat memudahkan pengguna untuk mencari kegiatan dalam suatu wilayah lain, disimpulkan bahwa sistem bermanfaat dalam memberikan layanan informasi tentang data dan kegiatan lansia, sehingga memudahkan pengguna mendapat informasi yang dibutuhkan.   Kata kunci : Sistem Informasi, Web , Lansia, <PERSON><PERSON><PERSON><PERSON>.", "Keywords": "", "DOI": "10.30742/melek-it.v6i2.312", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "Andya Nova", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626092, "Title": "Sistem Informasi Administrasi Pada Teguh Motor Berbasis Mobile Web", "Abstract": "Bengkel Teguh Motor adalah salah satu usaha dalam bidang pelayanan jasa layanan perbaikan sepeda motor yang ada di Surabaya barat. banyak kendaraan sepeda motor yang memperbaiki di Teguh Motor sehingga banyak antrian layanan perbaikan menjadi kendala yang dihadapi oleh pihak bengkel. Penelitian ini bertujuan untuk membuat Sistem Informasi Administrasi berbasis Mobile Web yang bisa menyelesaikan kendala-kendala pada Teguh Motor. Sistem informasi yang dihasilkan memiliki fitur layanan perbaikan, status layanan perbaikan, informasi tagihan layanan perbaikan, dan pemenuhan sparepart . Kesimpulan dari penelitian ini adalah Sistem Informasi Administrasi Pada Teguh Motor memberikan informasi antrian serta pemesanan layanan perbaikan tanpa harus datang ke bengkel Teguh Motor. Kata Kunci: <PERSON><PERSON><PERSON>, Sistem Informasi, Website .", "Keywords": "Sistem Informasi", "DOI": "10.30742/melek-it.v6i2.316", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "Sigit Sugiantoro", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626098, "Title": "What Do Practitioners Discuss about IoT and Industry 4.0 Related Technologies? Characterization and Identification of IoT and Industry 4.0 Categories in Stack Overflow Discussions", "Abstract": "Internet of Things and Industry 4.0–related discussions have become increasingly prevalent on several online Q&A websites in recent years. Analyzing and understanding such discussions could provide practitioners with more insights into various trends in the evolution of topics of interest and help different research communities, including software development and industrial sectors, better understand the needs and challenges facing practitioners as they operate in these fields. We conduct an exploratory study to capture and compare the popularity as well as the effect of the discussion topics on the domain across such communities. We use extracted data from popular online Q&A websites, such as Stack Exchange, and investigate 176,819 posts to investigate what practitioners are asking and looking for. We also use Machine Learning for Language Toolkit (MALLET) based–Latent Dirichlet Allocation (LDA) topic models to help summarize Internet of Things/Industry 4.0–related questions, identify the most challenging popular related issues, explore specific issues of the Internet of Things/Industry 4.0, and investigate the types of questions that practitioners ask about. We summarize all the topics into various categories and evaluate the popularity and difficulty of various topics as well. Our findings help to highlight the challenges facing the Internet of Things/Industry 4.0 practitioners which in the future would require more attention from various research and software development communities and develop new approaches to analyzing questions posed on various Q&A forums. Introduction The Internet of Things (IoT) is an emerging technology in a wide range of domains. It is defined as the interconnection of physical objects, i.e., things, and sites through the Internet [1], [2]. It defines a technological revolution where physical, as well as virtual objects, would be interconnected to others and to the current Internet infrastructure. Consequently, IoT technologies have attracted increasing interest from the research/software development communities. IoT-related discussions have become prevalent in various domains’ question and answer (Q&A) forums, such as Stack Exchange communities. Such Q&A platforms moderate thousands of posts each month from IoT practitioners, including developers, with a variety of backgrounds and experiences. The ability to analyze and interpret such knowledge repositories can provide valuable insights into the topics of interest and their assessment. Previous studies have carried out a wide range of empirical studies on the IoT knowledge in various domains, including Q&A website programming [3], [4], [5], [6], [7], [8], [9]. These studies provided insights into taxonomy, categories, topics as well as trends in numerous Q&A software development websites, and revealed different challenges and concepts through collective understanding and knowledge on these platforms. Whereas, Industry 4.0 is a new initiative to advance the development of the industrial sectors. The main purpose is to utilize the innovative potentials of the new concepts, such as availability of the Internet, the Cloud and the Internet of Things, synthesis of technical and–or business processes, usage of smart means of industrial productions in order to influence the entire industries, including companies, by reconstructing the way goods are structured, built, distributed or even paid for. Such a fourth industrial revolution, i.e., Industry 4.0, can best be explained as a changeover in the line of reasoning of production and–or manufacture towards a growing-ly decentralized, self-regulating value creation approach, empowered by various concepts and technologies such as Cyber Physics Systems (CPS), Internet of Things, Cloud Computing, or additive manufacturing and smart factories to allow organizations meet future operational and production needs. Organizations are intended to describe the meaning of Industry 4.0 for them because of the broad nature of this definition. Most of the previously studied topics and–or technologies via Stack Exchange communities often have long existence before Stack Exchange emerged as a reliable platform for practitioner communication and knowledge sharing. Internet of Things (IoT) and Industry 4.0–related technologies are emerging in contrast to these technologies, so we have a lucrative alternative for the first time to learn how a development community builds up its awareness in fast-paced areas. In response to a wide range of implications, we can still investigate how a domain continues to develop as captured on the Stack Exchange communities. The ability to determine such discussion topics will assist identify the main challenges facing IoT practitioners today. Previous studies suggested using the Latent Dirichlet Allocation (LDA) topic modeling method to investigate discussions on Stack Exchange communities, e.g., [5],[6]. Simple use of LDA is not capable of handling our case because LDA simply captures the topics blindly without paying attention to the varying nature of the dataset and domain-specific concepts. More specifically, LDA is biased towards larger sized corpora<sup>1</sup>[10]. In our study, discussions about older technologies are likely to account for the dominant properties in the extracted dataset. LDA-derived topics, on the other hand, are not connected to higher-level domain-specific concepts. They are not useful for the analysis of commonalities and differences among platforms. We are conducting a comprehensive study on topics discussed by IoT-related questions on Stack Exchange to extend previous works investigating different Stack Overflow topics. We employ a dataset<sup>2</sup> that is publicly accessible on Stack Exchange Data Dump. There are more than 176,808 posts in this dataset, each with a text (e.g., body) explaining a question and–or a response, including answers, with some metadata included (e.g., creation date and view content). Initially, we utilize two heuristics to retrieve IoT/Industry 4.0-related questions based on the tags and posts, then we use an adopted topic model which is based on LDA tuned using MALLET, to group different IoT/Industry 4.0-related questions based on their associated texts. Upon acquiring various IoT/Industry 4.0-related topics, we employ their metadata to perform different analyses. To address the above-mentioned challenges, we use the publicly available Stack Overflow data to investigate the most common issues that are/were encountered by IoT/Industry-4.0 practitioners. Knowing the issues that the practitioners are facing will help: 1) understand the common issues or challenges that others have encountered, so they can set countermeasures and–or alternative plans for resolutions accordingly, 2) Communities for research/software development determine future research directions (i.e., assist in knowing the real issues that do exist in the field). In order to determine what the practitioners ask about andor look for, we employ LDA-based topic models on the IoT/Industry 4.0–related posts. We analyze the topics extracted, rank them according to their views, comments, and up-votes. We, then, look at the number of questions that are answered and accepted in each topic as well as their response time. The aim of our study is to answer the following research questions: RQ1. What are the topics being covered by IoT-related questions and been asked on the Stack Overflow platform? We use the topic modeling method to analyze the IoT-related topics. Stack Overflow IoT-related questions span a wide variety of topics; these topics are belonging to five main categories, i.e., network management, software development, platform development, hardware management, and system management. We then mapped them to the integration, security, and applications deployments. Among them, most questions are about software development, i.e., applications deployments, with ≈ 39%. RQ2. Among the IoT-related questions, which topics are considered the most popular? We evaluate the popularity of IoT-related topics by one main metric (i.e., the average number of views), and the other three minor metrics (i.e., the average number of each of the comments, favorites, and score). The top four most popular IoT topics are ”Software Bugs”, ”Development apps for devices”, ”Initializing and creating IoT projects”, and ”Design and implementation of IoT platforms”, among which the first two topics are the most valuable as they earn the richest number of comments and favorites, as well as the highest scores. RQ3. Which IoT-related topics are the hardest to answer? We measure the difficulty of IoT-related topics by two metrics (i.e., the percentage of questions receiving satisfactory answers and the average time, in days, needed to get an accepted answer). We identified the top most difficult IoT-related topics by considering both the percentage and the average time needed. ”Software bugs” and ”design and implementation of IoT platforms”, are the two topics that deserve the most attention since they are popular, difficult, and take a longer time to be addressed to the satisfaction of practitioners. RQ4 . What concerns do Industrial individuals discuss about the new Industry 4.0 trend? Identifying the discussion topics can help practitioners pinpoint the major concerns that Industry 4.0 practitioners are currently experiencing. Industrial researchers could objectively discover real issues. Accordingly, future work efforts can therefore focus on such specific concerns and–or problems in order to support and improve the quality of Industry 4.0 platforms. RQ5 . What do industrial sectors, including factory managers, engineers, and administrators, look for in IoT discussions? It is not surprising that the key concern of the tech professional community, including software developers and practitioners, is the development of software, verification tests, tracing of bugs, and resolving them. However, we should also be concerned about those who make decisions, e.g., managers, and put directions to disseminate IoT to different technological communities and cultures. We mine different topics on additional platforms other than Stack Overflow and present the reflection of the current characterizations. RQ6 . What is the current State-of-Practice of the Industrial Internet of Things (IIoT)? Although many Industrial Internet of Things (IIoT) projects are still in the proof-of-concept (POC) and–or trial phase, there are clear signs of a widespread move towards full production deployments. Related technologies are ready to help achieve good returns, but still, we need to investigate more into that the high-level approaches we take to address our research questions are illustrated in Fig. 1. The main contributions out of this work are as follows . - We perform an empirical study on Stack Overflow to figure out and cluster IoT-related questions. To the best of the authors’ knowledge, so far, this is the first large-scale study to examine IoT- and Industry 4.0-related topics and trends on the Stack Exchange communities. - We report several helpful and informative conclusions. We examine the popularity and difficulty of IoT- and Industry 4.0-related topics that have some consequences for both researchers and practitioners. The remainder of this article is structured as follows. Section 2 briefly provides backgrounds for IoT and Industry 4.0, introduces the Latent Dirichlet Allocation (LDA), and evaluates the associated works. The steps of data collection and our experimental methods are represented in Section 3. Both Section 4 and Section 5, respectively, present our study results and provide recommendations to practitioners and researchers based on our findings. Section 6 discusses the possible threats to the validity of our findings. Section 7 concludes our paper. Figures Our high-level study methodology. IoT-related post on Stack Overlfow. Related post whose tags do not contain ”IoT”. Related post whose tags contain ”industry4.0” Statistics for each topic of questions. Statistics for each category of questions. Show all figures Section snippets What is the ”Internet of Things”? The Internet of Things (IoT) structure is a new model where different objects, i.e., things, equipped with various sensors, actuators, or processors are interconnected with one another to support the delivery of meaningful services [11]. Objects interconnection through the Internet has already shifted the way people live in a dramatic way [12]. Such a notion has also been prolonged to the industrial sector ; it not only provides real-time, reliable, and secure way of communications but also Setup Of Our Case Study The details of our empirical experimental analysis are reflected in this section. At first, we present the details for the data collection in Section 3.1, then in the subsequent Sections and Sub-Sections we clarify our experimental processes. Case Study And Scenario In this section, we present the experimental findings from our study in relation to the three research questions which we are interested in (i.e., Section 1). The carried out experiments for answering the aforementioned research questions are conducted on a Linux machine, i.e., Ubuntu 17.10, in a lab environment; this machine has an Intel i7-870 Quad-Core 2.93GHz CPU with 16GB of memory, 630GB SATA storage, 8MB Cache and is connected to a local gigabyte Ethernet cable. Implications Of Our Findings Generally, we have so far explored and analyzed the issues that the practitioners ask for on online Q&A forums and how complicated these issues are, the challenges that are unique to IoT and IIoT, and the categories of questions that are being asked. We also shed the light on the contextual tagging in this section and explore the consequences of our observations. Threats to Validity Like other empirical studies, the work presented in this paper is subject to multiple threats. We now discuss such threats following well-known guidelines for empirical studies [71]. Threats to construct validity concern the relation between theory and observation. In our work context, it is essentially due to potential mistakes in the detection of both IoT–Industry 4.0 related posts. We double-check both the title and body of the identified posts to determine whether the post-and-or question Conclusion And Future Work The purpose of this study is to discover IoT- and Industry 4.0- related topics on common development Q&A websites, i.e. Stack Exchange communities, that have millions of active users. Nevertheless, the simple utilization of the LDA arbitrarily captures topics in different discussions without taking into consideration the diversity of the extracted datasets as well as the domain-specific concepts. We analyze Stack Exchange records in order to identify what IoT–industry practitioners look for and Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments We truly want to thank the anonymous reviewers for providing us valuable feedback to enhance our article. We are also grateful to Assistant Professor Uddin for giving valuable insights on the collected data and how to use and–or analyze them to provide viable explanations and results. Last but not least, we thank all the coders, i.e., labelers, who participated in the development of the benchmark data and the evaluation of the algorithms in this work. References (72) L. Atzori et al. The internet of things: a survey Comput. Netw. (2010) A. Saifullah et al. Snow: Sensor network over white spaces Proceedings of the 14th ACM Conference on Embedded Network Sensor Systems CD-ROM (2016) M. Asaduzzaman et al. Answering questions about unanswered questions of stack overflow Proceedings of the 10th Working Conference on Mining Software Repositories (2013) S.M. Nasehi et al. What makes a good code example?: A study of programming q&a in stackoverflow Proceedings of the 28th IEEE International Conference on Software Maintenance (ICSM) (2012) H. Li et al. What help do developers seek, when and how? Proceedings of the 20th Working Conference on Reverse Engineering (WCRE) (2013) J. Gubbi et al. Internet of things (IoT): a vision, architectural elements, and future directions Future Generat. Comput. Syst. (2013) S. Wang et al. Towards smart factory for industry 4.0: a self-organized multi-agent system with big data based feedback and coordination Comput. Netw. (2016) S.K. Lukins et al. Source code retrieval for bug localization using latent dirichlet allocation Proceedings of the 15th Working Conference on Reverse Engineering (2008) S.K. Lukins et al. Bug localization using latent dirichlet allocation Inf. Softw. Technol. (2010) M. Röder et al. Exploring the space of topic coherence measures Proceedings of the Eighth ACM International Conference on Web Search and Data Mining (2015) C. Rosen et al. What are mobile developers asking about? a large scale study using stack overflow Emp. Softw. Eng. (2016) X.-L. Yang et al. What security questions do developers ask? a large-scale study of stack overflow posts J. Comput. Sci. Technol. (2016) A. Barua et al. What are developers talking about? an analysis of topics and trends in stack overflow Emp. Softw. Eng. (2014) P. Sethi et al. Internet of things: architectures, protocols, and applications J. Electr. Comput. Eng. (2017) W. Wang et al. Detecting api usage obstacles: A study of IoS and android developer questions Proceedings of the 10th Working Conference on Mining Software Repositories (2013) K. Henning, Recommendations for implementing the strategic initiative industrie 4.0... S. Azad et al. Generating API call rules from version history and stack overflow posts ACM Trans. Softw. Eng. Methodol. (TOSEM) (2017) K. Bajaj et al. Mining questions asked by web developers Proceedings of the 11th Working Conference on Mining Software Repositories (2014) P.M. F, Snowball: A language for stemming algorithms., 2016, (Available at... G.P. Joshi et al. Survey, nomenclature and comparison of reader anti-collision protocols in RFID IETE Techn. Rev. (2008) M. Linares-Vásquez et al. Api change and fault proneness: a threat to the success of android apps Proceedings of the 9th Joint Meeting on Foundations of Software Engineering (2013) N. Nic Disruptive civil technologies: six technologies with potential impacts on us interests out to 2025 (2008) L. Mamykina et al. Design lessons from the fastest q&a site in the west Proceedings of the SIGCHI Conference on Human Factors in Computing Systems (2011) M. Mittermair Industry 4.0 initiatives SMT: Surface Mount Technol. (2015) S.R. Services, Schlumberger limited 2018 annual report, 2018, (Available Online:... M. Brettel et al. How virtualization, decentralization and network building change the manufacturing landscape: an industry 4.0 perspective Int. J. Mech. Ind. Sci. Eng. (2014) E. Sisinni et al. Industrial internet of things: challenges, opportunities, and directions IEEE Trans. Ind. Inf. (2018) S. Neuhaus et al. Security trend analysis with cve topic models Proceedings of the IEEE 21st International Symposium on Software Reliability Engineering (2010) T. Rault et al. Energy efficiency in wireless sensor networks: a top-down survey Comput. Netw. (2014) S.W. Thomas et al. Modeling the evolution of topics in source code histories Proceedings of the 8th Working Conference on Mining Software Repositories (2011) M. Allamanis et al. Why, when, and what: analyzing stack overflow questions by topic, type, and code Proceedings of the 10th Working Conference on Mining Software Repositories (MSR) (2013) A. Panichella et al. How to effectively use topic models for software engineering tasks? an approach based on genetic algorithms Proceedings of the 2013 International Conference on Software Engineering (2013) 3GPP, 3gpp the mobile broadband standard, 2016, (Available Online: https://www.3gpp.org/news-events/3gpp-news/1785-nb_,... A.T. Nguyen et al. Duplicate bug report detection with a combination of information retrieval and topic modeling Proceedings of the 27th IEEE/ACM International Conference on Automated Software Engineering (2012) P. Ferrari et al. Delay estimation of industrial IoT applications based on messaging protocols IEEE Trans. Instrum. Meas. (2018) D.M. Blei, J. Lafferty, Topic models. text mining: theory and applications, 2009,... View more references Cited by (0) Recommended articles (6) Research article Current status of tokamak T-15MD Fusion Engineering and Design, Volume 164, 2021, Article 112211 Show abstract At the present time, the preparation to physical start-up of tokamak T-15MD is completed in the National Research Center “Kurchatov Institute”. The main parameters of T-15MD are: R = 1.48 m, a = 0.67 m, B = 2.0 T, Ipl = 2.0 MA. The magnet system is capable to maintain without overheating (more 60 °C) the plasma current of 2 MA for 4 s, 1 MA for 20 s, 700 kA for 40 s, 500 kA for 80 s, 300 kA for 160 s and 250 kA for 400 s. Plasma current drive can be maintained either by injection of fast neutrals or by electron cyclotron (EC)-, ion cyclotron (IC)- and low hybrid (LH) - waves. In August 2019 the electromagnetic system, consisting of TF and PF coils, together with vacuum vessel have been assembled in experimental hall. Power supply system of Tokamak T-15MD includes: two substations 110/10 kV, two substations 10/0.83 kV, thyristor convertors and different equipment. Total power consumption during the pulse with plasma current 2 MA and additional plasma heating of 20 MW will consist of 300 MVA. Power supply system is in the commissioning. Tokamak T-15MD will be operate using the information and control system. All the information and control system equipment, required for the implementation of physical start-up of tokamak T-15MD, is available. For plasma control the 250 different electromagnetic probes are installed inside vacuum vessel. The gyrotron with frequency 82.6 GHz and power of 1 MW will be used for pre-ionization. Research article NEDetector: Automatically extracting cybersecurity neologisms from hacker forums Journal of Information Security and Applications, Volume 58, 2021, Article 102784 Show abstract Underground hacker forums serve as an online social platform for hackers to communicate and spread hacking techniques and tools. In these forums, a lot of latest information indirectly or directly affects cyberspace security, thereby threatening the assets of enterprises or individuals. Therefore, social media such as hacker forums and twitter have a great impact on the cybersecurity area. In recent years, analyzing hacker forum data to explore hacking activities and cybersecurity situational awareness have aroused widespread interest among researchers. Automatically identifying cybersecurity words and extracting neologisms from open source social platforms are less successful and still require further research. In order to provide early warning of cyber attack incidents, we proposed NEDetector, a novel method to automatically identify cybersecurity words and extract neologisms from unstructured content, mainly focus on attack groups and hacking tools. NEDetector firstly analyzes the cybersecurity words and proposes four group features to build cybersecurity words identification model based on Bidirectional LSTM algorithm. Secondly, NEDetector introduces 4 sets of features to identify cybersecurity neologisms based on RandomForest algorithm. The experiment result shows that the whole system of NEDetector achieves an identification precision of 89.11%. Furthermore, the proposed extracting neologisms system is often earlier than having enough data in Google Trends when performing predictions on Twitter data, which prove the validity and timeliness of presented system. Research article Attacking and defence pathways for Intelligent Medical Diagnosis System (IMDS) International Journal of Medical Informatics, Volume 148, 2021, Article 104415 Show abstract The Intelligent Medical Diagnosis System (IMDS) has been targeted by the cyber attackers, who aim to damage the Healthcare Critical National Infrastructure (CNI). This research is motivated by the recent cyber attacks happened worldwide that have resulted in the compromise of medical diagnosis records. This study was conducted to demonstrate how the IMDS could be attacked and diagnosis records compromised (i.e. heart disease) and suggest a list of security defence strategies to prevent against such attacks. This research developed an IMDS simulation platform by implementing the OpenEMR system. A Cardiac Diagnosis Component is then added to the IMDS. The IMDS is fed with the ECG data (retrieved from the PhysioNet/Computing in Cardiology Challenge 2017). This research then launched systematic ethical hacking, which was tailored to target IMDS diagnosis records. The systematic hacking was based on the NIST ethical hacking method and followed an attack pathway, starting from identifying the entry points of the medical websites, then propagating to gain access to the server, with the ultimate aim of modifying the heart disease diagnosis records. The hacking was successful. Four major vulnerabilities (i.e. broken authentication, broken access control, security misconfiguration and using components with known vulnerabilities) were identified in the simulated IMDS and the cardiac diagnosis records were compromised. This research then proposed a list of security defence strategies to prevent such attacks at each possible attacking points along the attacking pathway. This research demonstrated a systematic ethical hacking to the IMDS, identified four major vulnerabilities and proposed the security defence pathways. It provided novel insights into the protection of IMDS and will benefit researchers in the community to conduct further research in security defence of IMDS. Research article MECGuard: GRU enhanced attack detection in Mobile Edge Computing environment Computer Communications, Volume 172, 2021, pp. 1-9 Show abstract With the commercialization of 5G technology, various Mobile Edge Computing (MEC) services are being deployed widely. Generally, MEC services rely on MEC devices and servers deployed at the edge of the network. Whether it is a MEC device or an edge server, most of them lack computing resources, and it is difficult to implement powerful security capabilities. Moreover, there are a large number of MEC service providers, different standards, and different protocols, which extend the attack interface of MEC services. In response to this situation, this paper proposes MECGuard, an attack detection solution designed for the MEC environment based on deep learning technology. Based on its distributed architecture designed for the MEC environment, MECGuard implements a lightweight TCP-level protocol extractor based on Decision Tree, and an attack detection network based on Gated Recurrent Unit (GRU). Experiments prove that MECGuard could have a good performance of malicious traffic detection in the EMC environment. Research article Two novel coordination polymers and their hybrid materials with Ag nanoparticles for non-enzymatic detection of glucose Journal of Solid State Chemistry, Volume 297, 2021, Article 122086 Show abstract Employing a rigid 6-(3-pyridyl)isophthalic acid (H<sub>2</sub>PIAD) linker, two novel coordination polymers (CPs), [Mn(PIAD)]<sub>n</sub> ( 1 ) and [Pb(HPIAD)(NO<sub>3</sub>)]<sub>n</sub> ( 2 ) were synthesized and characterized. The hybrid of Ag nanoparticles (NPs) with compound 1 ( [email protected] 1 ) was further modified onto electrode for the detection of glucose. The results demonstrated that [email protected] 1 possessed high sensitivity (0.156 ​ μ A ​ μ M<sup>−1</sup> ​cm<sup>−2</sup>) and low detection limit (1.6 ​ μ M) towards glucose sensing in the range of 5–3500 ​ μ M. With the significant electrochemical performance, this work promotes the proposed strategy for CPs-based hybrid composites as sensing application. Research article NodeXP: NOde.js server-side JavaScript injection vulnerability DEtection and eXPloitation Journal of Information Security and Applications, Volume 58, 2021, Article 102752 Show abstract Web applications are widely used, and new ways for easier and cost-effective methods to develop them are constantly introduced. A common omission among the new development and implementation techniques when designing them is security; Node.js is no exception, as Server-Side JavaScript Injection ( SSJI ) attacks are possible due to the use of vulnerable functions and neglecting to sanitize data input provided by untrusted sources. This specific kind of injection attack stands out because it has the potential to compromise servers, where the JavaScript code is executed. In this work, we fill a significant gap in the literature by introducing NodeXP , which, to the best of our knowledge, is the first methodology (presented as a software tool) that detects and automatically exploits SSJI vulnerabilities. Beyond the capabilities of the current state-of-the-art tools, NodeXP uses obfuscation methods, making it more stealth and adaptive to the current needs of red teaming. To this end, we provide a thorough analysis of SSJI attacks and the foundation upon which they rely on, along with concrete examples to facilitate the reader to comprehend the underlying concepts. Finally, we evaluate NodeXP , compare it to its peers, and discuss its efficacy. View full text © 2021 Elsevier B.V. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.iot.2021.100364", "PubYear": 2021, "Volume": "14", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Polytechnique Montreal, 2700 <PERSON><PERSON>, Montreal, PQ, Canada, H3T 1J4;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Polytechnique Montreal, 2700 <PERSON><PERSON>, Montreal, PQ, Canada, H3T 1J4"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Polytechnique Montreal, 2700 <PERSON><PERSON>, Montreal, PQ, Canada, H3T 1J4"}], "References": []}, {"ArticleId": 86626119, "Title": "Steganografi LSB Dengan Modifikasi Kriptografi: <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> dan <PERSON>fair Pada Image", "Abstract": "Pada era modern ini, teknologi informasi berkembang dan menjadi salah satu media yang bertujuan membentuk sistem dengan cara pengelolahan, pengum<PERSON><PERSON>, penyimpanan, sampai pengiriman. Keamanan informasi harus diperhatikan, dikarenakan terdapat kejahatan dimana oleh banyak pihak yang ingin mengakses informasi dan menyalagunakan informasi yang bukan pemiliknya. <PERSON><PERSON> karena itu, diperlukan teknologi informasi dan pengamanan informasi untuk memiliki informasi bersifat pribadi. Penggabungan teknik kriptografi dan steganografi dapat digunakan untuk pengamanan informasi. Teknik kriptografi menggabungkan empat metode yaitu Caesar cipher, vigenere, hill cipher, dan playfair. Pada teknik steganografi LSB (least Significant Bit) untuk menyisipkan informasi berupa teks didalam gambar. Metode pengujian dilakukan dengan menggunakan beberapa cara yaitu kapasitas penyisipan, histogram, pemotongan gambar dan pengubahan ekstensi. Hasilnya menunjukan bahwa penyisipan data yang terdapat pada gambar mengalami penambahan ukuran file yaitu menggunakan 17 karekter menghasilkan nilai 192 KB dan 13 karakter menadapatkan nilai 170 KB.  Selain itu, hasil pada pengujian histogram juga mengalami perbedaan yaitu pada nilai mean pada file gambar enkripsi mengalami peningkatan 220.87. Pada pengukian selanjutnya adalah standard deviation yang mengalami penurunan pada file gambar enkripsi yaitu dengan nilai 72.35 dari nilai 72.45.", "Keywords": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playfair, SLB (Least Significant Bit)", "DOI": "10.30742/melek-it.v6i1.301", "PubYear": 2020, "Volume": "6", "Issue": "1", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Amikom Yogyakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> S. H. S", "Affiliation": "Universitas Amikom Yogyakarta"}], "References": []}, {"ArticleId": 86626120, "Title": "DESAIN DATA WAREHOUSE UNTUK ANALISIS PT. ATLAS TRANSINDO RAYA JASA PENGIRIMAN BARANG", "Abstract": "Peran serta teknologi yang semakin dominan dalam membantu meningkatkan performa kerja suatu organisasi/perusahaan padaera globalisasi ini mendorong perusahaan saling berlomba-lomba dalam mengakui sisi teknologi yang ada guna mendukung perusahaan dalam melakukan aktivitas kerjanya agar bisa dilakukan seefektif, serta seefisien mungkin. Data warehouse sebagai salah satu konsep yang berorientasi pada komponen inti dalam suatu perusahaan, yaitu data; dapat dikategorikan sebagai aspek penunjang yang bersifat strategic karena melalui pembentukan data warehouse dapat diperoleh suatu output berupa laporan yang dapat dijadikan  sebagai bahan analisis bagi pihak eksekutif dalam proses pengambilan keputusan.Tujuan yang ingin dicapai  dalam penelitian ini adalah merumuskan model data warehouse dan rancangan aplikasi yang sesuai dengan hasil analisis keb<PERSON><PERSON><PERSON>, yang nantinya dapat menunjang pada perusaha<PERSON> jasa pen<PERSON>, dimana pada penelitian  ini melibatkan PT. Atlas Transindo Raya sebagai objek penelitian.", "Keywords": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Strategik, Data Warehouse, Jasa Pengiriman", "DOI": "10.30742/melek-it.v6i1.305", "PubYear": 2020, "Volume": "6", "Issue": "1", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "Bagus <PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626121, "Title": "PERANCANGAN DATABASE PADA RUMAH SAKIT DR. ARIFIN", "Abstract": "<PERSON><PERSON><PERSON>kit Dr. <PERSON><PERSON>lam menangani para pasiennya perlu untuk memaksimalkan penanganan  pelayanannya, Untuk memastikan bahwa data dapat diolah dengan baik sehingga menghasilkan informasi yang berguna, tepat dan akurat serta dapat diakses oleh semua pihak yang terlibat dalam penyediaan layanan kesehatan yang baik, dibutuhkan bantuan infrastruktur teknologi informasi dan komunikasi (TIK) yang dikenal dengan sistem informasi rumah sakit. Jurnal ini membahas tentang perancangan database serta peran sistem informasi rumah sakit dalam meningkatkan pelayanan kesehatan bagi masyarakat. Jurnal ini diharapkan juga dapat memberikan masukan bagi semua pengelola rumah sakit, dokter, para medis, pasien pencari jasa rumah sakit tentang pentingnya pembangunan sistem informasi untuk membantu peningkatan kualitas layanan kesehatan. <PERSON><PERSON> masalah yang memperlambat pelayanan seperti administrasi data pasien secara manual , perhitungan data yang kurang akurat dan kehilangan data diperlukan sebuah sistem database Rumah Sakit Dr. Arifin sendiri yang mempermudah pengolahan sebuah data baik dalam menghapus, mengedit, menambahkan  dan memanipulasi data tersebut dengan berupa perancangannya secara bertahap dengan perancangan active database nya. Tujannya adalah Membuat perancangan untuk pelayanan yang efektif dan mudah pada pasien di Rumah Sakit Dr. Arifin.", "Keywords": "<PERSON><PERSON><PERSON>, Database, Data, Perencanaan", "DOI": "10.30742/melek-it.v6i1.308", "PubYear": 2020, "Volume": "6", "Issue": "1", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "MUHAMAD AZRIL DOFIANSYAH", "Affiliation": ""}, {"AuthorId": 2, "Name": "DWIKY CHANDRA NUGROHO", "Affiliation": "Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 3, "Name": "HERLINA PUTRI ANGGRAINI", "Affiliation": "Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626123, "Title": "Sistem Informasi Penjualan Kosmetik", "Abstract": "Abstrak Sistem Informasi Penjualan Kosmetik berbasis web adalah sebuah sistem informasi yang menyediakan atau menjual produk-produk kosmetik. Akan tetapi banyak web yang hanya menjual kosmetik tetapi tidak memenuhi kebutuhan konsumen. Walaupun sudah terdapat sistem informasi penjualan kosmetik, konsumen tetap mengikuti seminar atau kelas yang diadakan secara offline oleh penyelenggara untuk menambah pengetahuan tentang kosmetik. Penelitian ini bertujuan untuk membuat Sistem Informasi Penjualan yang bisa menyelesaikan kendala-kendala yang dialami oleh konsumen. Kebutuhan sistem ini diantaranya konsumen dapat mengikuti seminar online , dapat berinteraksi dengan sesama konsumen melalui dunia maya dan konsumen dapat mengikuti kelas kecantikan secara online. Hasil dari penelitian ini berupa Sistem Informasi Penjualan Kosmetik yang meliputi katalog penjualan, fitur seminar online , fitur beauty class online , fitur forum diskusi online dan pembayaran.   Abstract Web-based Cosmetic Sales Information System is an information system that provides or sells cosmetic products. However, many websites only sell cosmetics but do not meet the needs of consumers. Even though there is a cosmetics sales information system, consumers continue to attend seminars or classes held offline by the organizer to increase knowledge about cosmetics. This study aims to create a Sales Information System that can solve the constraints experienced by consumers. These system needs include consumers being able to take part in online seminars, being able to interact with fellow consumers through cyberspace and consumers being able to take beauty classes online. The results of this study are Cosmetic Sales Information Systems which include sales catalogs, online seminar features, online beauty class features, online discussion forum features, and payments.", "Keywords": "Sistem Informasi, Penjualan, Kosmetik, Web, E-commerce", "DOI": "10.30742/melek-it.v6i1.294", "PubYear": 2020, "Volume": "6", "Issue": "1", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> .", "Affiliation": "Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86626124, "Title": "DESAIN DATA WAREHOUSE PADA PERPUSTAKAAN UWKS", "Abstract": "Perpustakaan merupakan bagian dari fasilitas pendidikan untuk menunjang kemajuan pendidikan yang memberikan sebuah informasi dan ilmu pengetahuan kepada peserta didik sehingga para peserta didik memiliki wawasan yang luas. Sistem perpustakaan di Universitas Wijaya Kusuma Surabaya  ini masih menggunakan cara yang manual dalam setiap kegiatannya sehingga dalam kegiatan pengarsipan sering terjadi kesalahan dan sering terjadi kehilangan data karena penyimpanan data yang tidak teratur dan tidak adanya backup data. Oleh karena itu proyek akhir ini bertujuan untuk  memberikan informasi dan meningkatkan kualitas di perpustakaan Universitas Wijaya Kusuma Surabaya dengan memfasilitasi data warehouse, sehingga dapat mendukung dan membantu pelayanan di perpustakaan Universitas Wijaya Kusuma.", "Keywords": "internet, sistem informasi, perpustakaan, warehouse", "DOI": "10.30742/melek-it.v6i1.306", "PubYear": 2020, "Volume": "6", "Issue": "1", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626141, "Title": "SISTEM PENJUALAN DAN MONITORING ARUS MATERIAL BERBASIS MULTI WAREHOUSE", "Abstract": "Toko Aliyah merupakan toko yang bergerak di bidang penjualan bahan pangan yang digunakan untuk memenuhi kebutuhan masyarakat atau khalayak umum. <PERSON><PERSON><PERSON>han yang ada pada toko tersebut ialah mengenai penggunaan transaksi yang masih dilaksanakan secara manual sehingga dapat menghambat kinerja penjualan yang sedang berlangsung. Maka dari itu peneliti mengusulkan suatu sistem penjualan dan monitoring yang diharapkan dapat membantu memperlancar dalam proses penjualan dan monitoring arus material. Penelitian ini diawali dengan mendata barang yang masih tersedia didalam toko, langkah selanjutnya ialah menganalisa suatu masalah yang ada pada studi kasus sehingga peneliti dapat mendapatkan data yang benar. Kemudian metode yang  digunakan adalah mencari jarak terdekat atau Shortest Path   untuk mengatasi permasalahan pergudangan agar tidak adanya penumpukan barang pada setiap Gudang. Pen<PERSON>ti merancang sistem penjualan dan monitoring berbasis multi warehouse mempermudah dalam pemasaran produk, karena dapat mengatur stok toko dan memperbarui produk toko, serta proses dalam verifikasi pembayaran juga cukup mudah.", "Keywords": "", "DOI": "10.30742/melek-it.v6i2.311", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626143, "Title": "Sistem Layanan Point of Sales Services Sparepart Kendaraan Motor Pada Komunitas XYZ", "Abstract": "<PERSON>am penelitian ini adalah untuk memberikan solusi yang tepat guna pada komunitas yang berorintasi pada layanan perawatan suku cadang motor bagi anggota komunitas yaitu dalam bentuk POS (Point Of Sales) layanan servis suku cadang yang dilakukan anggota komunitas secara berkala. Adapun dalam proses perancangan dan membangun sistem informasi layanan servis ini adalah dengan menerapkan metode Research and Development untuk mendokumentasikan data-data yang disimpan dalam sistem berjalan dan mengaplikasikan model Agile Extreme Programming dalam mendesain sistem yang diusulkan. Pencapaian dalam implementasi sistem layanan point of sales servis spare part yang dijalankan dapat memberikan kontribusi yang memadai sehingga kesalahan-kesalahan pendataan atas penggantian berkala pada suku cadang dapat diminimalisasi sehingga informasi yang diperoleh tepat guna.", "Keywords": "Point Of Sales, Services, Sparepart, Kendaraan motor", "DOI": "10.30742/melek-it.v6i2.350", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Forkas Tiroy <PERSON>-butar", "Affiliation": "Universitas Indraprasta PGRI"}], "References": []}, {"ArticleId": 86626162, "Title": "SISTEM PENCARIAN LOKASI TOKO BATIK DI WILAYAH SURABAYA DENGAN ALGORITMA DIJKSTRA", "Abstract": "Semakin berkembangnya batik saat ini mampu memberikan dampak positif pada kota Surbaya,  pada dasarnya masih banyak masyarakat maupun para wisatawan yang kesulitan mencari toko batik yang sesuai dengan harapan mereka. <PERSON><PERSON> karena masih minimnya informasi tentang jarak, harga dan popularitas toko batik menjadikan masyarakat maupun wisatawan masih kebingungan dalam menentukan lokasi  toko batik mana yang akan dituju. Berdasarkan permasalahan yang  telah dipaparkan  maka di penelitian ini menggunakan metode Algoritma Dijkstra dengan pembuatan aplikasi penentuan jalur terpendek pencarian lokasi toko batik di Surabaya dengan metode Algoritma Dijkstra berbasis web . Hasil dari penelitian ini dapat disimpulkan bahwa berdasarkan pengambilan data yang diperoleh, penyusunan rute toko batik di surabaya dengan masing masing titik sudah dapat disusun dalam bentuk graph berbobot, membandingkan bobot masing masing lintasan sehingga diambil nilai perbandingan yang paling terkecil. Sehingga Algoritma Dijkstra secara otomatis akan mencari jalur terpendek dari rute tersebut.", "Keywords": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> terp<PERSON>,website, batik", "DOI": "10.30742/melek-it.v6i2.314", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626163, "Title": "Sistem Penjualan Pupuk Organik berbasis Web Di Toko Nusantara Dili Timor - Leste", "Abstract": "", "Keywords": "Melek IT;Information System", "DOI": "10.30742/melek-it.v6i2.335", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}], "References": []}, {"ArticleId": 86626169, "Title": "Pengembangan Sistem Presensi Perkuliahan dengan Menggunakan Teknologi NFC (Near Field Communication) Berbasis Android", "Abstract": "Presensi merupakan proses pengumpulan data guna mengetahui kehadiran seseorang dalam suatu kegiatan. Salah satu kegiatan yang membutuhkan presensi adalah perkuliahan. Presensi dalam perkuliahan secara umum masih dilakukan secara manual, yakni dengan menggunakan lembar presensi yang dibawa oleh dosen di tiap pertemuan. Tujuan dari penelitian ini adalah untuk merancang dan mengembangkan sistem dengan memanfaatkan teknologi NFC yang terdapat pada perangkat mobile sehingga dapat menjadi alternatif untuk menggantikan presensi manual, sekaligus untuk meminimalisir kecurangan oleh mahasiswa yang sering terjadi dalam proses presensi manual. Sistem ini dirancang dengan menggunakan use case diagram, use case narrative, class diagram, activity diagram, sequence diagram, deployment diagram, dan rancangan UI ( User Interface ), sedangkan pengujian sistem dilakukan dengan menggunakan metode black box guna mengetahui sejauh mana fungsionalitas dari sistem yang telah dibangun. Dari hasil pengujian black box dapat disimpulkan bahwa sistem presensi yang dikembangkan dapat digunakan oleh mahasiswa untuk melakukan presensi dengan memanfaatkan teknologi NFC yang terdapat pada perangkat mobile. Mahasiswa hanya perlu melakukan tap ke NFC tag yang telah disediakan di dalam ruang kuliah. Selain itu, sistem ini dapat digunakan mahasiswa untuk mengirim izin perihal ketidakhadirannya.", "Keywords": "Presensi;NFC;Android", "DOI": "10.30742/melek-it.v6i2.286", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Teknik Informatika, Fakultas Teknik, Universitas Wijaya Kusuma Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Indra P<PERSON>a", "Affiliation": ""}], "References": []}, {"ArticleId": 86626170, "Title": "RANCANG BANGUN SISTEM PENILAIAN KINERJA PADA PT.M2S", "Abstract": "PT M2S merupakan perusahaan yang bergerak di bidang property. Penilaian kinerja karyawan merupakan satu proses yang harus dilakukan perusahaan untuk mendapatkan Sumber Daya Manusia (SDM) yang baik, tetapi penilaian kinerja karyawan yang di terapkan di PT M2S belum memenuhi standart karena penilaiannya hanya dilakukan dengan melihat hasil fisiknya.Penelitian ini diawali dengan menilai kemampuan kinerja masing-masing karyawan, kedisi<PERSON><PERSON>n karyawan, ketetapan waktu mengerjakan saat mendapat perintah dari pimpinan, kemampuan bekerja sama yang dituntut suatu perusahaan.Setelah mengetahui masalah yang terjadi pada PT M2S maka peneliti memutuskan untuk membuat aplikasi web penilaian kinerja, Aplikasi ini di anggap mampu mengatasi masalah penilaian kinerja karyawan pada perusahaan, Aplikasi ini juga menyediakan akses untuk mengisi kuisioner kepada setiap manager agar mendapatkan hasil yang maksimal dalam memberikan penilaian terhadap karyawan.  Dengan adanya Aplikasi ini, PT. M2S mendapat kemudahan dalam proses penilaian kinerja karyawan. Aplikasi ini menghasilkan Laporan Analisa Penilaian untuk direktur yang dikembangkan dengan metode Graphic Rating Scale. Kata kunci : Penilaian Kinerja, Sistem, Rancang Bangun, Graphic Rating Scale   Abstract PT M2S is a company engaged in the property sector. Employee performance appraisal is a process that companies must undertake to obtain good Human Resources (HR), but the employee performance appraisal applied at PT M2S has not met the standard because the assessment is only done by looking at the physical results.This research begins by assessing the performance ability of each employee, employee discipline, working time schedule when receiving orders from the leadership, the ability to work together that is demanded by a company.After knowing the problems that occurred at PT M2S, the researchers decided to make a performance appraisal web application, this application is considered capable of solving the problem of employee performance appraisal at the company, this application also provides access to fill out questionnaires to each manager in order to get maximum results in providing an assessment towards employees. With this application, PT. M2S finds it easy in the employee performance appraisal process. This application produces an Assessment Analysis Report for directors developed using the Graphic Rating Scale method.", "Keywords": "information system", "DOI": "10.30742/melek-it.v6i2.348", "PubYear": 2021, "Volume": "6", "Issue": "2", "JournalId": 66619, "JournalTitle": "Melek IT Information Technology Journal", "ISSN": "2442-3386", "EISSN": "2442-4293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Indra <PERSON>", "Affiliation": "wijaya kusuma surabaya"}], "References": []}, {"ArticleId": 86626282, "Title": "Selective gas detection of H2 and CO by a single MOX-sensor", "Abstract": "The temperature modulation of the SnO 2 -based metal oxide sensor with the addition of PdO x opened up the possibility of selective analysis of both conventionally one-component systems “hydrogen in air”, “carbon monoxide in air” and conditionally two-component system “hydrogen and carbon monoxide in air”. The aim of this work was the quantitative and qualitative analysis of gases using a single MOX sensor based on a small number of training sample concentrations in the range from 1 to 100 ppm. Gas concentrations in test experiments differed from gas concentrations in the training sample. We also studied the effect of air humidity on the error of the selective determination of gas concentrations in conventionally one- and two-component systems. To reduce multidimensional data in determining analyte concentrations, we used a specific combination of PCA regression analysis algorithms. The temperature modulation of the SnO 2 -sensor with the addition of PdO x allowed us to increase the sensor responses for concentrations from 10 to 100 ppm by about two orders of magnitude compared to the stationary temperature regime.", "Keywords": "Sensitivity ; MOX sensor ; Temperature modulation ; Qualitative analysis ; Quantitative analysis ; Multidimensional data", "DOI": "10.1016/j.snb.2020.129376", "PubYear": 2021, "Volume": "334", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, Voronezh State Agrarian University, Voronezh 394087, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Physics, Voronezh State Agrarian University, Voronezh 394087, Russia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, Voronezh State Agrarian University, Voronezh 394087, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry, Voronezh State Agrarian University, Voronezh 394087, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NRC Kurchatov Institute, Moscow 123182, Russia"}], "References": [{"Title": "Nanostructured Pr-doped Ceria (PCO) thin films as sensing electrodes in solid-electrolyte type gas sensors with enhanced toluene sensitivity", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "317", "Issue": "", "Page": "128037", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 86626288, "Title": "How smartphone use becomes problematic: Application of the ALT-SR model to study the predicting role of personality traits", "Abstract": "Smartphones have become a ubiquitous part of adolescents' life, and studies have repeatedly revealed a positive association between smartphone use (SU) and problematic smartphone use (PSU). However, longitudinal research investigating the reciprocal relationship among SU and PSU during adolescence are scarce, and studies that take into consideration personality traits as predisposing factors are lacking. This study used survey data collected annually over four years from 855 adolescents aged 11 at time 1 and distributed across 37 Swiss middle schools. An Autoregressive Latent Trajectory Model with Structured Residuals (ALT-SR model) was used to investigate between - and within -person effects over time. Additionally, gender and personality traits, measured according to the recently developed DSM-5 domains, were entered as predictors of the latent intercepts and slopes. The final model showed that, at the within -person level, SU significantly increased PSU at all four time points, but not viceversa . At the between -person level, the personality traits antagonism and negative affect significantly and positively predicted the latent intercepts, whereas being female, psychoticism, and disinhibition significantly and positively influenced the latent slopes. This study highlights the importance of investigating predisposing factors of PSU in adolescence, using advance statistical approaches. The results are discussed against the background of the I-PACE model on predisposing factors and mechanisms that lead to addictive behaviors such as PSU.", "Keywords": "Adolescence ; Problematic smartphone use ; ALT-SR ; Longitudinal ; Personality ; Gender", "DOI": "10.1016/j.chb.2021.106731", "PubYear": 2021, "Volume": "119", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Communication and Health, Faculty of Communication, Culture, and Society, USI Università Della Italiana, Via Giuseppe <PERSON> 13, 6900, Lugano, Switzerland;Corresponding author. LAB (level 2), Via Buffi 13, 6900, Lugano, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Communication and Health, Faculty of Communication, Culture, and Society, USI Università Della Italiana, Via Giuseppe <PERSON> 13, 6900, Lugano, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Communication and Health, Faculty of Communication, Culture, and Society, USI Università Della Italiana, Via Giuseppe <PERSON> 13, 6900, Lugano, Switzerland"}], "References": [{"Title": "Does time spent using social media impact mental health?: An eight year longitudinal study", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "106160", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Neuroticism in the digital age: A meta-analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "", "Page": "100026", "JournalTitle": "Computers in Human Behavior Reports"}]}, {"ArticleId": 86626671, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1569-190X(21)00015-0", "PubYear": 2021, "Volume": "108", "Issue": "", "JournalId": 1087, "JournalTitle": "Simulation Modelling Practice and Theory", "ISSN": "1569-190X", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 86626681, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0034-4257(21)00050-X", "PubYear": 2021, "Volume": "255", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [], "References": []}, {"ArticleId": 86626941, "Title": "АЛГОРИТМ ПОСТРОЕНИЯ РЕЗУЛЬТАНТА ДВУХ ЦЕЛЫХ ФУНКЦИЙ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421020096", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON><PERSON>ов", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86626984, "Title": "ФАКТОРИЗАЦИЯ БУЛЕВЫХ ПОЛИНОМОВ: ПАРАЛЛЕЛЬНЫЕ АЛГОРИТМЫ И ЭКСПЕРИМЕНТАЛЬНАЯ ОЦЕНКА", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421020059", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>и", "Affiliation": ""}, {"AuthorId": 4, "Name": "С. К. Нанди", "Affiliation": ""}, {"AuthorId": 5, "Name": "Д. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "Affiliation": ""}, {"AuthorId": 6, "Name": "С. Раха", "Affiliation": ""}], "References": []}, {"ArticleId": 86626985, "Title": "Авторский указатель статей, опубликованных в 2020 году", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421100022", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 86626992, "Title": "МОДЕЛИРОВАНИЕ ДИНАМИКИ КВАНТОВОЙ ЗАПУТАННОСТИ В КОНЕЧНОЙ КВАНТОВОЙ МЕХАНИКЕ: КОМПЬЮТЕРНО-АЛГЕБРАИЧЕСКИЙ ПОДХОД", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421020072", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "В. <PERSON>. <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86626993, "Title": "Редакционные правила для авторов журнала “Программирование”", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421100010", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 86627016, "Title": "СЕМИНАР ПО КОМПЬЮТЕРНОЙ АЛГЕБРЕ В 2019–2020 гг.", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421020023", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "С. <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "А. <PERSON><PERSON> Бого<PERSON>юбская", "Affiliation": ""}], "References": []}, {"ArticleId": 86627041, "Title": "СИМВОЛЬНО-АНАЛИТИЧЕСКИЕ МЕТОДЫ ИССЛЕДОВАНИЯ ПОЛОЖЕНИЙ РАВНОВЕСИЯ СПУТНИКА НА КРУГОВОЙ ОРБИТЕ", "Abstract": "", "Keywords": "", "DOI": "10.31857/S0132347421020060", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 58312, "JournalTitle": "Программирование", "ISSN": "0132-3474", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "С. <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "В. <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86627049, "Title": "Securing Cluster Head in Wireless Sensor Network for Internet of Things", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i01.005", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>﻿", "Affiliation": ""}], "References": []}, {"ArticleId": 86627062, "Title": "To be or not to be after Virus Attack by Nutrition (Olive Leaf) Contains Nucleoside Triphosphate same as Remdesivir Drug after Intracellular and Vitamin D", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i01.002", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>﻿", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Sara Bahra<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>﻿", "Affiliation": ""}], "References": []}, {"ArticleId": 86627063, "Title": "DISTRIBUTED DAT<PERSON>ASE MANAGEMENT SYSTEM (DBMS) ARCHITECTURES AND DISTRIBUTED DATA INDEPENDENCE﻿", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2021.v10i01.004", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "Orlunwo Placida O.", "Affiliation": ""}, {"AuthorId": 2, "Name": "Prince <PERSON><PERSON><PERSON><PERSON>.", "Affiliation": ""}], "References": []}, {"ArticleId": 86627169, "Title": "PolInSAR coherence-based decomposition modeling for scattering characterization: A case study in Uttarakhand, India", "Abstract": "Polarimetric decomposition models such as <PERSON><PERSON><PERSON><PERSON>’s three-component and <PERSON><PERSON><PERSON>’s four-component models were used to extract the scattering mechanisms. In a few cases, both the models have shown overestimation of the volume scattering from the highly-dense building instead of even-bounce scattering. Deorientation process helped to reduce the overestimation of the volume scattering but didn’t accomplish completely. This paper addressed this issue by the insertion of PolInSAR coherence in the existing decomposition models. The coherence varies with features based on its temporal and volume decorrelation, hence forest results in low coherence compared to permanent scatterers helps to characterize the man-made and natural features. This paper experimented with a proposed model on the RADARSAT-2 dataset of Uttarakhand as a case study. Volume scattering was modified and extracted based on different coherence parameter assumptions. Finally, the first optimal band with spatial and temporal baseline coefficients as 1 and 0.6 thresholds gave a more reliable outcome. For qualitative analysis, the scattering mechanisms of features from different models were compared. In the proposed model, closely spaced buildings exhibited 54% and 26% dominance in the even and odd-bounce scattering while volume scattering reduced to 18%. Whereas <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>’s model has shown dominance in volume nearly 98% and 50% while even and odd-bounce scattering were less than 1% in <PERSON><PERSON><PERSON><PERSON> model and 39% and 11% in <PERSON><PERSON><PERSON>’s model. <PERSON> shows dominance in the volume scattering higher than 60% in the <PERSON><PERSON><PERSON><PERSON> and the proposed models whereas 35% in the <PERSON><PERSON><PERSON>’s model. Therefore, the proposed methodology successfully fused polarimetry and interferometry to overcome the ambiguity in scattering.", "Keywords": "Synthetic aperture radar ; <PERSON><PERSON><PERSON><PERSON> decomposition model ; <PERSON><PERSON><PERSON> decomposition model ; PolInSAR coherence ; PolInSAR coherence decomposition model", "DOI": "10.1016/j.srs.2021.100020", "PubYear": 2021, "Volume": "3", "Issue": "", "JournalId": 72499, "JournalTitle": "Science of Remote Sensing", "ISSN": "2666-0172", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "M.N.S. Ramya", "Affiliation": "Centre for Space Science and Technology Education in Asia and the Pacific, Dehradun, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Space Science and Technology Education in Asia and the Pacific, Dehradun, India;Photogrammetry & Remote Sensing Department, Indian Institute of Remote Sensing, ISRO Dept. of Space, Dehradun, India;Corresponding author"}], "References": [{"Title": "PolInSAR decorrelation-based decomposition modelling of spaceborne multifrequency SAR data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "4", "Page": "1398", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Snow depth retrieval in North-Western Himalayan region using pursuit-monostatic TanDEM-X datasets applying polarimetric synthetic aperture radar interferometry based inversion Modelling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "8", "Page": "2872", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 86627179, "Title": "iviz: A ROS visualization app for mobile devices", "Abstract": "In this work, we introduce iviz , a mobile application for visualizing data in the Robot Operating System (ROS). In the last few years, the popularity of ROS has grown enormously, making it the standard platform for robotic programming. However, the availability of this environment is generally restricted to PCs with the Linux operating system. Thus, users wanting to see what is happening in the system with a smartphone or a tablet are stuck with solutions such as screen mirroring or web browser versions of rviz , making newer visualization modalities such as Augmented Reality impossible. Our application iviz, based on the Unity engine, addresses these issues by providing a visualization platform designed from scratch to be usable in mobile platforms such as iOS, Android, and UWP, and including native support for Augmented Reality for all three platforms. If desired, it can also be used in a PC with Linux, Windows, or macOS without any changes.", "Keywords": "Robotics ; Data visualization ; Augmented reality", "DOI": "10.1016/j.simpa.2021.100057", "PubYear": 2021, "Volume": "8", "Issue": "", "JournalId": 66395, "JournalTitle": "Software Impacts", "ISSN": "2665-9638", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Intelligent Sensor-Actuator-Systems Laboratory (ISAS), Karlsruhe Institute of Technology (KIT), Adenauerring 2 Geb. 50.20, 76131 Karlsruhe, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Intelligent Sensor-Actuator-Systems Laboratory (ISAS), Karlsruhe Institute of Technology (KIT), Adenauerring 2 Geb. 50.20, 76131 Karlsruhe, Germany"}], "References": []}]