[{"ArticleId": 90999771, "Title": "Geometric Correction Method Applying the Holographic Ray Direction Control Technology", "Abstract": "<p>In recent years, there has been an increasing need for larger screens and higher definition displays, while projectors are becoming smaller and cheaper. Furthermore, an ultra-short-throw projector that can display on a large screen while significantly reducing the distance between the projector and screen is being developed. However, ultra-short-throw projectors are required to be precisely aligned with the screen, and if the screen is not flat, the projected image becomes distorted. Therefore, geometric correction projection technology is attracting attention for projection on curtains and the walls of living rooms instead of screens for realizing the correction of distortion during projection with ultra-short-throw projectors, projection mapping, signage, etc. We focused on developing a hologram with perfect command of the ray. Conventional geometry-correction systems are expensive systems that require a personal computer and a camera. In this study, we developed a geometric correction method applying holographic ray direction control technology to control a holographic ray at a low cost and in real time. In this paper, we studied the exposure technology and proposed a ray-direction control technology that combines a scanning laser projector that uses a hologram and a micro electro mechanical systems mirror. We also proposed and demonstrated the basic principle of a holographic surface projector (HSP), which uses hologram geometry correction technology. Finally, we constructed a geometrically corrected hologram exposure system using a depth camera and conducted geometrically corrected projection experiments.</p>", "Keywords": "hologram;holographic surface projector;geometric correction method;MEMS;3D-object shape measurement", "DOI": "10.20965/jrm.2021.p1155", "PubYear": 2021, "Volume": "33", "Issue": "5", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology, Numazu College 3600 Ooka, Numazu, Shizuoka 410-8501, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shape in Space LLC 591-17 Kawaharagaya, Mishima, Shizuoka 411-0022, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "HolyMine Corp. 591-17 Kawaharagaya, Mishima, Shizuoka 411-0022, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HolyMine Corp"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology, Numazu College"}], "References": []}, {"ArticleId": 90999772, "Title": "Subtypes of smokers in a randomized controlled trial of a web-based smoking cessation program and their role in predicting intervention non-usage attrition: Implications for the development of tailored interventions", "Abstract": "Introduction Web-based smoking interventions hold potential for smoking cessation; however, many of them report low intervention usage (i.e., high levels of non-usage attrition). One strategy to counter this issue is to tailor such interventions to user subtypes if these can be identified and related to non-usage attrition outcomes. The aim of this study was two-fold: (1) to identify and describe a smoker typology in participants of a web-based smoking cessation program and (2) to explore subtypes of smokers who are at a higher risk for non-usage attrition (i.e., early dropout times). Methods We conducted secondary analyses of data from a large randomized controlled trial (RCT) that investigated effects of a web-based Cognitive Bias Modification intervention in adult smokers. First, we conducted a two-step cluster analysis to identify subtypes of smokers based on participants' baseline characteristics (including demographics, psychological and smoking-related variables, N = 749). Next, we conducted a discrete-time survival analysis to investigate the predictive value of the subtypes on time until dropout. Results We found three distinct clusters of smokers: Cluster 1 (25.2%, n = 189) was characterized by participants being relatively young, highly educated, unmarried, light-to-moderate smokers, poly-substance users, and relatively high scores on sensation seeking and impulsivity; Cluster 2 (41.0%, n = 307) was characterized by participants being older, with a relatively high socio-economic status (SES), moderate-to-heavy smokers and regular drinkers; Cluster 3 (33.8%, n = 253) contained mostly females of older age, and participants were further characterized by a relatively low SES, heavy smoking, and relatively high scores on hopelessness, anxiety sensitivity, impulsivity, depression, and alcohol use. Additionally, Cluster 1 was more likely to drop out at the early stage of the intervention compared to Cluster 2 (adjusted Hazard Ratio ( HR <sub>adjusted</sub>) = 1.51, 95% CI = [1.25, 1.83]) and Cluster 3 ( HR <sub>adjusted</sub> = 1.52, 95% CI = [1.25, 1.86]). Conclusions We identified three clusters of smokers that differed on a broad range of characteristics and on intervention non-usage attrition patterns. This highlights the heterogeneity of participants in a web-based smoking cessation program. Also, it supports the idea that such interventions could be tailored to these subtypes to prevent non-usage attrition. The subtypes of smokers identified in this study need to be replicated in the field of e-health outside the context of RCT; based on the smoker subtypes identified in this study, we provided suggestions for developing tailored web-based smoking cessation intervention programs in future research.", "Keywords": "Smoker typologies ; Cluster analysis ; Non-usage attrition ; Smoking cessation ; Web-based intervention ; Randomized controlled trials", "DOI": "10.1016/j.invent.2021.100473", "PubYear": 2021, "Volume": "26", "Issue": "", "JournalId": 11817, "JournalTitle": "Internet Interventions", "ISSN": "2214-7829", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Addiction Development and Psychopathology (ADAPT)-lab, Department of Psychology, University of Amsterdam, Amsterdam, the Netherlands;Corresponding author at: Addiction Development and Psychopathology (ADAPT)-Lab, Department of Psychology, University of Amsterdam, Postbus 15916, 1001 NK Amsterdam, the Netherlands"}, {"AuthorId": 2, "Name": "Reinout W. <PERSON>", "Affiliation": "Addiction Development and Psychopathology (ADAPT)-lab, Department of Psychology, University of Amsterdam, Amsterdam, the Netherlands;Center for Urban Mental Health, University of Amsterdam, Amsterdam, the Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Addiction Development and Psychopathology (ADAPT)-lab, Department of Psychology, University of Amsterdam, Amsterdam, the Netherlands;Department of Psychology, Education and Child Studies, Erasmus University Rotterdam, Rotterdam, the Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Programme group Psychological Methods, Department of Psychology, University of Amsterdam, Amsterdam, the Netherlands"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Addiction Development and Psychopathology (ADAPT)-lab, Department of Psychology, University of Amsterdam, Amsterdam, the Netherlands;Open Science Tools (PsychoPy)-Lab, School of Psychology, University of Nottingham, Nottingham, United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Addiction Development and Psychopathology (ADAPT)-lab, Department of Psychology, University of Amsterdam, Amsterdam, the Netherlands"}], "References": []}, {"ArticleId": 90999779, "Title": "Inter-Module Physical Interactions: A Force-Transmissive Modular Structure for Whole-Body Robot Motion", "Abstract": "<p>Robots are required to be significantly compliant and versatile to work in unstructured environments. In a number of studies, robots have positively exploited the environments during interactions and completed tasks from a morphological viewpoint. Modular robots can help realize real-world adaptive robots. Researchers have been investigating the actuation, coupling, and communication mechanisms among these robots to realize versatility. However, the diverse force transmission among modules needs to be further studied to achieve the adaptive whole-body dynamics of a robot. In this study, we fabricated a modular robot and proposed the realization of force transmission on this robot, by constructing fluid transferable network systems on the actuation modules. By exploiting the physical property variations of the modular robot, our experimental results prove that the robot’s motion can be changed by switching the connection pattern of the system.</p>", "Keywords": "modular robots;force transmission;humanoid mechanism;morphological computation", "DOI": "10.20965/jrm.2021.p1190", "PubYear": 2021, "Volume": "33", "Issue": "5", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University 1-3 Machikaneyama-cho, Toyonaka, Osaka 560-8531, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering Science, Osaka University"}], "References": []}, {"ArticleId": 90999976, "Title": "Robust high-order iterative learning control approach for two-dimensional linear discrete time-varying For<PERSON>ini–<PERSON> systems with iteration-dependent reference trajectory", "Abstract": "This paper first investigates robust tracking problem of a high-order iterative learning control (HOILC) algorithm for two classes of two-dimensional linear discrete time-varying Fornasini–<PERSON>ni systems (2-D LDTVFMS) and 2-D LDTVFMS with input delays with iteration-dependent reference trajectory described by a high-order internal model (HOIM) operator, boundary states and disturbances. An extended high-order linear discrete inequality is proposed to guarantee the ILC tracking error of 2-D LDTVFMS converge robustly to a bounded range, the bound of which is relied on iteration-dependent uncertainties from reference trajectory, boundary states and disturbances. A simulation example on a practical thermal process is used to validate the effectiveness and feasibility of the proposed HOILC law. Additionally, it is verified by theory analysis and simulation that no matter how the ILC controller gain is selected, the convergence condition in Theorem 2 of Wan and <PERSON> [(2021). High-order internal model based iterative learning control for 2-D linear FMMI systems with iteration-varying trajectory tracking. IEEE Transactions on Systems Man and Cybernetics: Systems , 51 , 1462–1472] is not satisfied. Using the proposed HOILC law, perfect tracking result in Theorem 2 of Wan and <PERSON> (2021) can be obtained.", "Keywords": "High-order iterative learning control (HOILC) ; two-dimensional linear discrete time-varying Fornasini–<PERSON> systems (2-D LDTVFMS) ; extended high-order linear discrete inequality ; iteration-dependent reference trajectory", "DOI": "10.1080/00207721.2021.1988188", "PubYear": 2022, "Volume": "53", "Issue": "5", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electronic Information and Electrical Engineering, Huizhou University, Huizhou, People's Republic of China"}], "References": [{"Title": "Iterative learning control for 2-D linear discrete For<PERSON><PERSON>–<PERSON> model with input saturation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "8", "Page": "1482", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": 91000037, "Title": "A two-step multivariate statistical learning approach for batch process soft sensing", "Abstract": "Statistical machine learning algorithms have been widely used to analyse industrial data for batch process monitoring and control. In this study, we aimed to take a two-step approach to systematically reduce data dimensionality and to design soft-sensors for product quality prediction. The approach first employs partial least squares to screen the entire dataset and identify critical time regions and operational variables, then adopts multiway partial least squares to construct soft-sensors within the reduced space to estimate final product quality. Innovations of this approach include the ease of data visualisation and ability to identify major operational activities within the factory. To highlight efficiency and practical benefits, an industrial personal care product manufacturing process was presented as an example and two soft-sensors were successfully developed for product end viscosity estimation. Furthermore, the accuracy, reliability, and data efficiency of the soft-sensors were thoroughly discussed. This paper, therefore, demonstrates the industrial potential of the proposed approach.", "Keywords": "Machine learning ; Multiway partial least squares ; Batch process ; Soft-sensor ; Dimensionality reduction ; Viscosity prediction", "DOI": "10.1016/j.dche.2021.100003", "PubYear": 2021, "Volume": "1", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Analytical Science, University of Manchester, Oxford Road, Manchester, M1 3AL, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Analytical Science, University of Manchester, Oxford Road, Manchester, M1 3AL, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Analytical Science, University of Manchester, Oxford Road, Manchester, M1 3AL, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Analytical Science, University of Manchester, Oxford Road, Manchester, M1 3AL, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Unilever Research Port Sunlight, Quarry Rd East, Bebington C63 3JW, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Unilever Research Port Sunlight, Quarry Rd East, Bebington C63 3JW, United Kingdom"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering and Analytical Science, University of Manchester, Oxford Road, Manchester, M1 3AL, United Kingdom;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering and Analytical Science, University of Manchester, Oxford Road, Manchester, M1 3AL, United Kingdom;Corresponding authors"}], "References": [{"Title": "Stochastic data-driven model predictive control using gaussian processes", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106844", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "A deep reinforcement learning approach for chemical production scheduling", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "106982", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 91000091, "Title": "Experimental and Simulation Analysis of Effect of <PERSON><PERSON><PERSON> Radius of Micro Square Hole on Copper Sheets in Deep Drawing of Sheets", "Abstract": "In the design and fabrication of molds in the deep drawing process, the gap between the punch and the mold is extremely small. Obtaining a product of the correct size and shape necessitates the control of certain variables in the process, consideration of the problems of springback after load removal and the occurrence of cracks in the drawing process, and the estimation of the punch load. In this study, we focused on copper sheets and considered the influence of the scale effect and fillet radius of a micro square hole on thin sheets on the basis of an updated Lagrangian formulation (ULF) and finite element analysis. Sheet behavior was simulated using a micro-elastoplastic material model, the performance of which was compared with that of models involving conventional materials. Subsequently, the Dynaform LS-DYNA solver was used for simulation analysis, and pre- and post-processing were carried out to obtain the material deformation history, as well as to determine the thickness change distribution and material stress and prepare strain distribution maps. It was found that the scale effect of the sheet thickness influences the relation between the punch load and stroke, the distribution of thickness, the distribution of stress and strain, the maximum diameter of the flange hole, and the maximum flange height. Finally, the simulation results were compared with experimental results to confirm the accuracy of 3D finite element analysis of the elastoplastic deformation. The results show the influence of the fillet radius of the inner hole (Br) for copper sheets on the drawing process: the punch load increases with increasing Br, but the minimum thickness of the formed flange decreases as Br increases. The maximum principal stress/strain and the flange height increase as Br increases. The findings serve as a valuable reference for the design and processing of micro deep drawing. © MYU K.K.", "Keywords": "Copper; Material deformation history; Micro deep drawing; Springback", "DOI": "10.18494/SAM.2021.3581", "PubYear": 2021, "Volume": "33", "Issue": "10", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Chin-Yi University of Technology, No. 57, Sec.2, Zhongshan Rd., Taiping Dist., Taichung, 411030, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Chin-Yi University of Technology, No. 57, Sec.2, Zhongshan Rd., Taiping Dist., Taichung, 411030, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Precision Manufacturing, National Chin-Yi University of Technology, No. 57, Sec.2, Zhongshan Rd., Taiping Dist., Taichung, 411030, Taiwan"}], "References": []}, {"ArticleId": 91000107, "Title": "Development of Lead-free Force-feedback Tactile Sensor Fabricated from BiFeO3 Piezoelectric Film", "Abstract": "In this study, we develop a flexible lead-free BiFeO3 (BFO) film sensor that can be applied on complex surfaces of devices. This sensor is fabricated by depositing BFO thin film on a polyimide (PI)-coated copper substrate. The sol-gel (SG) method and spin-coating method are utilized to prepare the BFO piezoelectric film. Two different spin-coating processes under three different sets of parameters and two different annealing temperatures are used in the fabrication. X-ray diffraction (XRD) and scanning electron microscopy are used to examine the crystallinity and surface topography of the BFO thin films, respectively. A lifetime test is performed on the device to validate the robustness of its piezoelectric properties, and a linear scaling relationship between the force and output response is obtained. We propose suitable conditions for preparing the piezoelectric film and demonstrate the reliability of this sensor. © MYU K.K.", "Keywords": "BiFeO3; Sol-gel; Tactile sensor; Thin film", "DOI": "10.18494/SAM.2021.3623", "PubYear": 2021, "Volume": "33", "Issue": "10", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate Institute of Mechatronic System Engineering, National University of Tainan, No. 33, Sec. 2, Shu-Lin St., West Central Dist., Tainan City, 700301, Taiwan, Department of Materials Science, National University of Tainan, No. 33, Sec. 2, Shu-Lin St., West Central Dist., Tainan City, 700301, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Science, National University of Tainan, No. 33, Sec. 2, Shu-Lin St., West Central Dist., Tainan City, 700301, Taiwan"}, {"AuthorId": 3, "Name": "Po<PERSON><PERSON>", "Affiliation": "Graduate Institute of Mechatronic System Engineering, National University of Tainan, No. 33, Sec. 2, Shu-Lin St., West Central Dist., Tainan City, 700301, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Mechatronic System Engineering, National University of Tainan, No. 33, Sec. 2, Shu-Lin St., West Central Dist., Tainan City, 700301, Taiwan"}], "References": []}, {"ArticleId": 91000129, "Title": "Construction of the Luxury Marketing Model Based on Machine Learning Classification Algorithm", "Abstract": "<p>China has become the world’s largest luxury goods consumer market due to its population base. In view of the bright prospects of the luxury consumer market, major companies have entered and want to get a share. For the luxury goods industry, traditional mass marketing methods are not able to serve corporate sales and marketing strategies more effectively, and targeted marketing is clearly much more efficient than randomized marketing. Therefore, in this paper, based on consumer buying habits and characteristics data of luxury goods, the paper uses a machine learning algorithm to build a personalized marketing strategy model. And the paper uses historical data to model and form deductions to predict the purchase demand of each consumer and evaluate the possibility of customers buying different goods, including cosmetics, jewelry, and clothing.</p>", "Keywords": "", "DOI": "10.1155/2021/6511552", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Institute of Visual Arts, Shanghai 201620, China"}, {"AuthorId": 2, "Name": "Shousong Cai", "Affiliation": "School of Business Administration, Shanghai Lixin University of Accounting and Finance, Shanghai 201209, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Financial Technology, Shanghai Lixin University of Accounting and Finance, Shanghai 201209, China"}], "References": [{"Title": "A privacy-preserving aggregation scheme based on negative survey for vehicle fuel consumption data", "Authors": "Weidong Yang; <PERSON><PERSON><PERSON> Chen; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "570", "Issue": "", "Page": "526", "JournalTitle": "Information Sciences"}, {"Title": "Research on AI security enhanced encryption algorithm of autonomous IoT systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "575", "Issue": "", "Page": "379", "JournalTitle": "Information Sciences"}]}, {"ArticleId": ********, "Title": "Scale-Insensitive Object Detection via Attention Feature Pyramid Transformer Network", "Abstract": "<p>With the progress of deep learning, object detection has attracted great attention in computer vision community. For object detection task, one key challenge is that object scale usually varies in a large range, which may make the existing detectors fail in real applications. To address this problem, we propose a novel end-to-end Attention Feature Pyramid Transformer Network framework to learn the object detectors with multi-scale feature maps via a transformer encoder-decoder fashion. AFPN learns to aggregate pyramid feature maps with attention mechanisms. Specifically, transformer-based attention blocks are used to scan through each spatial location of feature maps in the same pyramid layers and update it by aggregating information from deep to shadow layers. Furthermore, inter-level feature aggregation and intra-level information attention are repeated to encode multi-scale and self-attention feature representation. The extensive experiments on challenging MS COCO object detection dataset demonstrate that the proposed AFPN outperforms its baseline methods, i.e ., DETR and Faster R-CNN methods, and achieves the state-of-the-art results. </p>", "Keywords": "Object detection; Feature pyramid; Attention; Convolutional network", "DOI": "10.1007/s11063-021-10645-0", "PubYear": 2022, "Volume": "54", "Issue": "1", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>ling Li", "Affiliation": "School of Intelligent Engineering, Zhengzhou University of Aeronautics, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Software, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yunnan Key Laboratory of Artificial Intelligence, Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Zhonghangzhi Technology Co.,Ltd., Beijing, China"}, {"AuthorId": 5, "Name": "Taisong Jin", "Affiliation": "School of Informatics, Xiamen University, Xiamen, China"}], "References": [{"Title": "Deep learning in video multi-object tracking: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "61", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 91000172, "Title": "Design and Control of Drones", "Abstract": "<p>The design and control of drones remain areas of active research, and here we review recent progress in this field. In this article, we discuss the design objectives and related physical scaling laws, focusing on energy consumption, agility and speed, and survivability and robustness. We divide the control of such vehicles into low-level stabilization and higher-level planning such as motion planning, and we argue that a highly relevant problem is the integration of sensing with control and planning. Lastly, we describe some vehicle morphologies and the trade-offs that they represent. We specifically compare multicopters with winged designs and consider the effects of multivehicle teams.</p><p>Expected final online publication date for the Annual Review of Control, Robotics, and Autonomous Systems, Volume 5 is May 2022. Please see http://www.annualreviews.org/page/journal/pubdates for revised estimates.</p>", "Keywords": "", "DOI": "10.1146/annurev-control-042920-012045", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 15573, "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems", "ISSN": "2573-5144", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of California, Berkeley, California, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical System Design Engineering, Seoul National University of Science and Technology, Seoul, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute for Dynamic Systems and Control, ETH Zurich, Zurich, Switzerland"}], "References": []}, {"ArticleId": 91000177, "Title": "From Theoretical Work to Clinical Translation: Progress in Concentric Tube Robots", "Abstract": "<p>Continuum robots can traverse anatomical pathways to intervene in regions deep inside the human body. They are able to steer along 3D curves in confined spaces and dexterously handle tissues. Concentric tube robots (CTRs) are continuum robots that comprise a series of precurved elastic tubes that can be translated and rotated with respect to each other to control the shape of the robot and tip pose. CTRs are a rapidly maturing technology that has seen extensive research over the past decade. Today, they are being evaluated as tools for a variety of surgical applications, as they can offer precision and manipulability in tight workspaces. This review provides an exhaustive classification of research on CTRs based on their clinical applications and highlights approaches for modeling, control, design, and sensing. Competing approaches are critically presented, leading to a discussion of future directions to address the limitations of current research and its translation to clinical applications.</p><p>Expected final online publication date for the Annual Review of Control, Robotics, and Autonomous Systems, Volume 5 is May 2022. Please see http://www.annualreviews.org/page/journal/pubdates for revised estimates.</p>", "Keywords": "", "DOI": "10.1146/annurev-control-042920-014147", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 15573, "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems", "ISSN": "2573-5144", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Robotics and Vision in Medicine Lab, School of Biomedical Engineering and Imaging Sciences, King's College London, London, United Kingdom;;Wellcome/EPSRC Centre for Interventional and Surgical Sciences, University College London, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Robotics and Vision in Medicine Lab, School of Biomedical Engineering and Imaging Sciences, King's College London, London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Robotics and Vision in Medicine Lab, School of Biomedical Engineering and Imaging Sciences, King's College London, London, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Wellcome/EPSRC Centre for Interventional and Surgical Sciences, University College London, London, United Kingdom;Moorfields Eye Hospital, London, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Robotics and Vision in Medicine Lab, School of Biomedical Engineering and Imaging Sciences, King's College London, London, United Kingdom"}], "References": []}, {"ArticleId": 91000419, "Title": "Enhancing Human-Computer Interaction in Digital Repositories through a MCDA-Based Recommender System", "Abstract": "<p>Digital repositories contain a large amount of content, which is available to heterogeneous groups of people. As such, in many cases people encounter difficulties in finding specific content which is related to their preferences. In view of this compelling need and towards advancing human-computer interaction, this paper presents a recommender system which is incorporated in a digital repository. The recommender system is designed using multiple-criteria decision analysis (MCDA) and more specifically the weighted sum model (WSM) in order to refine the delivered content to the users. It also considers several users’ characteristics (their preferences as depicted by the content they visited or searched and by the frequency of searches/visits) and features of the content (content types and traffic). The recommender system outputs the suggestions of content to users based on their preferences and interests. The presented recommender system was evaluated by real users, and the results show a high degree of accuracy in the recommended content and satisfaction by users.</p>", "Keywords": "", "DOI": "10.1155/2021/7213246", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 13197, "JournalTitle": "Advances in Human-Computer Interaction", "ISSN": "1687-5893", "EISSN": "1687-5907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of West Attica, Egaleo, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of West Attica, Egaleo, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of West Attica, Egaleo, Greece"}], "References": [{"Title": "Document Recommendations and Feedback Collection Analysis within the Slovenian Open-Access Infrastructure", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "497", "JournalTitle": "Information"}]}, {"ArticleId": 91000512, "Title": "The role of individual factors in L2 vocabulary learning with cognitive-linguistics-based static and dynamic visual aids", "Abstract": "<p> The present study aims to verify the impact of dynamic aids on learning L2 prepositions in relation to individual learner variables. Situated within the cognitive linguistics (CL) framework and differing from previous research, the present study hypothesizes that dynamic (animated) aids are not equally effective for all learners; rather, their effectiveness differs according to learners’ first languages (L1s) (Chinese or Japanese) and information-processing styles (verbalizers or imagers). To verify this hypothesis, we utilized learning materials comprised of static and dynamic images for three English spatial prepositions ( above , on , over ). After conducting a Style of Processing questionnaire, we administered three cloze tests (pretest, posttest, and delayed posttest) of target words to Taiwanese and Japanese participants ( N = 109), whose L1s differed in terms of their linguistic proximity to English. Although no significant differences were found between the treatment groups in tests for all participants, the results were differentiated by individual factors. In results of a two-way ANOVA, Taiwanese participants showed significantly greater improvement from the pretest to posttest than Japanese participants when the participants used dynamic images, whereas the Japanese group made more learning gains from the posttest to the delayed posttest test. Moreover, imagers obtained more benefits from the visual aids, whether static or dynamic, than verbalizers. Our findings indicate that CL-based visual aids are beneficial and that individual factors, especially learners’ L1, may produce different learning effects, especially in multimedia environments. </p>", "Keywords": "image schema;static;dynamic;animation;individual factors;preposition;multimedia learning;cognitive linguistics", "DOI": "10.1017/S0958344021000288", "PubYear": 2022, "Volume": "34", "Issue": "2", "JournalId": 12184, "JournalTitle": "ReCALL", "ISSN": "0958-3440", "EISSN": "1474-0109", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tokyo University of Agriculture and Technology  Japan （ ） "}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Providence University  Taiwan （ ） "}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Meisei University  Japan （ ） "}], "References": []}, {"ArticleId": 91000589, "Title": "Blockchain-enabled cyber-physical smart modular integrated construction", "Abstract": "Modular Integrated Construction (MiC) has been one of the most innovative solutions to address the ever-growing housing demands in megacities such as Hong Kong. MiC offers a range of benefits including cost-effectiveness, high productivity, and high sustainability for construction industry. Multiple stakeholders in MiC project, such as project client, module manufacturer, logistics company, and building contractor, normally use their own proprietary and centralized Enterprise Information System (EIS). However, stakeholders are facing challenges to share information throughout MiC projects. “Islands of Information” issue is commonly existing and creates information fragmentation and discontinuity. As an emerging Information and Communication Technology (ICT), blockchain provides unified standards and protocols for information sharing based on decentralized P2P framework with enhanced transparency and security. Aiming to address the information fragmentation and discontinuity in MiC project, this paper proposes a blockchain-enabled cyber-physical smart MiC platform to facilitate cross-enterprise information sharing among multiple stakeholders based on User-Centered Design (UCD) method. A practical roadmap is presented for the design, development, deployment, and application of MiC blockchain with new opportunities and guidelines. Initial investigations based on an industrial collaborated company are analyzed and the blockchain-enabled cyber-physical MiC workflow is designed. The user-centered blockchain explorers with high-fidelity digital twins are illustrated. Some preliminary results are found that MiC blockchain not only facilitates cyber-physical construction progress traceability, and real-time KPI visualization & evaluation, but also improves the information reliability, immutability, and transparency in MiC projects.", "Keywords": "Blockchain ; Modular Integrated Construction (MiC) ; Cyber-Physical System (CPS) ; Information communication and sharing", "DOI": "10.1016/j.compind.2021.103553", "PubYear": 2021, "Volume": "133", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China"}], "References": [{"Title": "Industrial blockchain based framework for product lifecycle management in industry 4.0", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101897", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Do you need a blockchain in construction? Use case categories and decision framework for DLT design options", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "", "Page": "101094", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Peeking into the void: Digital twins for construction site logistics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "121", "Issue": "", "Page": "103264", "JournalTitle": "Computers in Industry"}, {"Title": "Blockchain in healthcare: A systematic literature review, synthesizing framework and future research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "122", "Issue": "", "Page": "103290", "JournalTitle": "Computers in Industry"}, {"Title": "Blockchain for energy sharing and trading in distributed prosumer communities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "103282", "JournalTitle": "Computers in Industry"}, {"Title": "Building information modeling (BIM)-based modular integrated construction risk management – Critical survey and future needs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "103327", "JournalTitle": "Computers in Industry"}, {"Title": "Two-layer Adaptive Blockchain-based Supervision model for off-site modular housing production", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "128", "Issue": "", "Page": "103437", "JournalTitle": "Computers in Industry"}, {"Title": "A blockchain-based system to enhance aircraft parts traceability and trackability for inventory management", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "115101", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 91000718, "Title": "Generating probabilistic safety guarantees for neural network controllers", "Abstract": "<p>Neural networks serve as effective controllers in a variety of complex settings due to their ability to represent expressive policies. The complex nature of neural networks, however, makes their output difficult to verify and predict, which limits their use in safety-critical applications. While simulations provide insight into the performance of neural network controllers, they are not enough to guarantee that the controller will perform safely in all scenarios. To address this problem, recent work has focused on formal methods to verify properties of neural network outputs. For neural network controllers, we can use a dynamics model to determine the output properties that must hold for the controller to operate safely. In this work, we develop a method to use the results from neural network verification tools to provide probabilistic safety guarantees on a neural network controller. We develop an adaptive verification approach to efficiently generate an overapproximation of the neural network policy. Next, we modify the traditional formulation of Markov decision process model checking to provide guarantees on the overapproximated policy given a stochastic dynamics model. Finally, we incorporate techniques in state abstraction to reduce overapproximation error during the model checking process. We show that our method is able to generate meaningful probabilistic safety guarantees for aircraft collision avoidance neural networks that are loosely inspired by Airborne Collision Avoidance System X (ACAS X), a family of collision avoidance systems that formulates the problem as a partially observable Markov decision process (POMDP).</p>", "Keywords": "Neural network controller; Verification; Model checking; Safety", "DOI": "10.1007/s10994-021-06065-9", "PubYear": 2023, "Volume": "112", "Issue": "8", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "Sydney M<PERSON>", "Affiliation": "Department of Aeronautics and Astronautics, Stanford University, Stanford, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Aeronautics and Astronautics, Stanford University, Stanford, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Stanford University, Stanford, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aeronautics and Astronautics, Stanford University, Stanford, USA"}], "References": [{"Title": "Formal verification of neural agents in non-deterministic environments", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}]}, {"ArticleId": 91000728, "Title": "ZipLine: an optimized algorithm for the elastic bulk synchronous parallel model", "Abstract": "<p>The bulk synchronous parallel (BSP) is a celebrated synchronization model for general-purpose parallel computing that has successfully been employed for distributed training of deep learning models. A shortcoming of the BSP is that it requires workers to wait for the straggler at every iteration. Therefore, employing BSP increases the waiting time of the faster workers of a cluster and results in an overall prolonged training time. To ameliorate this shortcoming of BSP, we propose ElasticBSP , a model that aims to relax its strict synchronization requirement with an elastic synchronization by allowing delayed synchronization to minimize the waiting time. ElasticBSP offers more flexibility and adaptability during the training phase, without sacrificing the accuracy of the trained model. ElasticBSP is realized by the algorithm named ZipLine , which consists of two phases. First, it estimates for each worker the end time points of its future iterations at run time, and then a one-pass algorithm over the estimated time points of all workers is employed to fast compute an optimal future time point for synchronization. We provide theoretical results about the correctness and performance of the ZipLine algorithm. Furthermore, we propose algorithmic and implementation optimizations of ZipLine , namely ZipLineOpt and ZipLineOptBS , which reduce the time complexity of ZipLine to linearithmic time. A thorough experimental evaluation demonstrates that our proposed ElasticBSP model, materialized by the proposed optimized ZipLine variants, converges faster and to a higher accuracy than the predominant BSP. The focus of the paper is on optimizing the synchronization scheduling over a parameter server architecture. It is orthogonal to other types of optimizations, such as the learning rate optimization.</p>", "Keywords": "Distributed deep learning; Parameter server framework; Data parallelism; BSP; Stale synchronous parallel; Asynchronous parallel", "DOI": "10.1007/s10994-021-06064-w", "PubYear": 2021, "Volume": "110", "Issue": "10", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Lassonde School of Engineering, York University, Toronto, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Lassonde School of Engineering, York University, Toronto, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Lassonde School of Engineering, York University, Toronto, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Lassonde School of Engineering, York University, Toronto, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Platform Computing, IBM, Markham, Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Platform Computing, IBM, Markham, Canada"}], "References": []}, {"ArticleId": 91001045, "Title": "Self‐Adaptive Plasma Chemistry and Intelligent Plasma Medicine", "Abstract": "<p>Plasma-based biomedical applications rely on the reactive oxygen and nitrogen species generated in cold atmospheric plasmas, where complex chemical kinetic schemes occur. The optimization of plasma medicine is thus required for each specific biomedical purpose. In the view of pharmacology, it is to optimize the active pharmaceutical ingredients. This work is thus the first attempt of such a complex task utilizing the recent development of machine learning technologies. Herein, a general method of passive plasma chemical diagnostics and optimization in real time is proposed. Based on spontaneous emission spectroscopy, an artificial neural network provides the gas chemical compositions along with other information such as temperatures. The information further passes through the second neural network which outputs the adjustments of external control inputs including energy, gas injections, and extractions to optimize the plasma chemistry.</p>", "Keywords": "active pharmaceutical ingredients;cold atmospheric plasma;machine learning;plasma chemistry;plasma medicine", "DOI": "10.1002/aisy.202100112", "PubYear": 2022, "Volume": "4", "Issue": "3", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mechanical and Aerospace Engineering, The George Washington University, 800 22nd St. NW, Washington DC, 22202 USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical and Aerospace Engineering, The George Washington University, 800 22nd St. NW, Washington DC, 22202 USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical and Aerospace Engineering, The George Washington University, 800 22nd St. NW, Washington DC, 22202 USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Mechanical and Aerospace Engineering, The George Washington University, 800 22nd St. NW, Washington DC, 22202 USA"}], "References": []}, {"ArticleId": ********, "Title": "Embedded Microprocessor Wireless Communication Data Collection Aids in Early Warning of Default Risk for Internet Finance Bank Customers", "Abstract": "<p>Thanks to the maturity and innovation of embedded technology, products based on embedded platforms continue to penetrate people’s lives and play a pivotal role in various fields of society, while the development of data acquisition systems based on wireless communication of embedded microprocessors is one of the frontier directions of embedded development. Modern commercial bank risk management system includes risk control organization, risk measurement techniques (including risk warning), risk avoidance techniques, and total risk management model. In this paper, a multichannel data acquisition system combined with a wireless sensor platform is designed for early warning of default risk of Internet financial bank customers, which can realize real-time monitoring, acquisition, display, and data storage of DC signals and indoor environment information output from sensor platforms or electronic devices, and for frequent transactions, fund splitting, and mortgage pledging related to complex customers in today’s e-commerce platform, which triggers when the associated enterprise credit risk presents complexity, continuity, wholeness, multiplicity, volatility, severity, etc., it is used to improve risk judgment and risk warning ability, enhance investment risk response ability, and reduce the related losses caused by marketing risk.</p>", "Keywords": "", "DOI": "10.1155/2021/1679907", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Northwest University, Xi’an 710127, China"}], "References": [{"Title": "Intelligence in the Internet of Medical Things era: A systematic review of current and future trends", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "644", "JournalTitle": "Computer Communications"}, {"Title": "An energy and coverage sensitive approach to hierarchical data collection for mobile sink based wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "1267", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 91001286, "Title": "IFC/Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-9236(21)00197-4", "PubYear": 2021, "Volume": "151", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [], "References": []}, {"ArticleId": 91001338, "Title": "Development and real time implementation of intelligent holistic power control for stand alone solar photovoltaic generation system", "Abstract": "This paper focusses on real-time implementation of an intelligent Holistic Power-Control system which is suitable for uniform, dynamic-irradiance and shaded-conditions for a stand-alone solar-PV system. The model consists of two intelligent Maximum Power Point Tracking(MPPT) control algorithms under different environmental conditions and a novel shading-detection mechanism. A fuzzy logic MPPT system with rule-base developed from real-time solar panel have been implemented. A partial shading detection mechanism based on power loss generated from solar PV array without any sensors is incorporated in the real-time platform. A fuzzy-logic Sweep-Inclusion algorithm is proposed to reach global maximum power point with fast tracking-time under partially-shaded conditions. To test the performance, real-time PV-system with data acquisition using LabVIEW is developed. The standalone solar-PV system is benefitted by the proposed-method by its less tracking-time and more efficiency, accurate partial shading detection through modified power loss scheme and Fuzzy Logic Sweep-Inclusion algorithm for identifying the global-MPPT point.", "Keywords": "Holistic power control ; Fuzzy logic MPPT ; Partial shading detection ; Sweep algorithm ; Data acquisition system", "DOI": "10.1016/j.compeleceng.2021.107519", "PubYear": 2021, "Volume": "96", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, SRM Institute of Science and Technology, Kattankulathur, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Kalasalingam Academy of Research and Education, Krishnankoil, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Ocean Observation Systems, National Institute of Ocean Technology, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Micron Technology, Hyderabad, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, University of Washington, USA"}], "References": []}, {"ArticleId": 91001439, "Title": "Application of HFACS and grounded theory for identifying risk factors of air traffic controllers’ unsafe acts", "Abstract": "Unsafe acts of air traffic controllers (ATCers) are caused by various factors. Based on interview data and case reports, human factors analysis and classification system (HFACS) and the grounded theory were adopted to identify the risk factors of ATCers’ unsafe acts comprehensively. The interview data and the case data issued by the authority were first collected. Then, the above data were encoded to obtain the relevant concepts and categories based on the grounded theory, and the HFACS model is used to classify the concepts and categories. Finally, the relationship between the core category and the secondary category was sorted out in the way of storyline. The results show that the risk factors include environmental factors, organizational influences, unsafe supervision and controllers’ states, and the unsafe acts manifest as errors and violations. Among them, the controllers’ states are intermediate variable, and other factors indirectly affect the controllers’ unsafe acts. The first three risk factors with high frequency in unsafe incidents are technical environment, mental states and business ability. The three most common unsafe acts are giving the wrong order, insufficient situational awareness, and poor work order on-site. Through combining HFACS framework and grounded theory to analyze data, a more clear and comprehensive conceptual model of risk factors of ATCers’ unsafe acts can be obtained.", "Keywords": "Unsafe acts ; Risk factors ; HFACS ; Grounded theory ; ATCers", "DOI": "10.1016/j.ergon.2021.103228", "PubYear": 2021, "Volume": "86", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Henan University of Engineering, Zhengzhou, Henan, 451191, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management, Wuhan University of Technology, Wuhan, Hubei, 430070, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Branch of Central South Air Traffic Management Bureau CAAC, Wuhan, Hubei, 430300, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Branch of Central South Air Traffic Management Bureau CAAC, Wuhan, Hubei, 430300, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Wuhan University of Technology, Wuhan, Hubei, 430070, China"}], "References": [{"Title": "Predicting unsafe behaviors at nuclear power plants: An integration of Theory of Planned Behavior and Technology Acceptance Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "", "Page": "103047", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "A path analysis model of individual variables predicting safety behavior and human error: The mediating effect of situation awareness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "84", "Issue": "", "Page": "103144", "JournalTitle": "International Journal of Industrial Ergonomics"}]}, {"ArticleId": 91001448, "Title": "Corrigendum to: A Self-Tallying Electronic Voting Based on Blockchain", "Abstract": "", "Keywords": "", "DOI": "10.1093/comjnl/bxab175", "PubYear": 2023, "Volume": "66", "Issue": "2", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Peng Cheng Laboratory, Xingke 1st Street, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Logistics and Supply Chain MultiTech R&D Centre, 100 Cyberport Road, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, The University of Hong Kong, Pokfulam, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Peng Cheng Laboratory, Xingke 1st Street, Shenzhen, China"}], "References": []}, {"ArticleId": 91001490, "Title": "Finite-time stability of impulsive pantograph systems with applications", "Abstract": "This paper is mainly concerned with the finite-time stability of impulsive pantograph systems. By proposing a novel Razumikh<PERSON> condition, and combining Lyapunov-Ra<PERSON><PERSON>khin method with average impulsive interval approach, we derive some criteria to ensure that the addressed impulsive pantograph systems are finite-time stable. Also, based on this <PERSON><PERSON><PERSON><PERSON><PERSON> condition, new Lyapunov-based conditions are obtained for the finite-time stability and the global power stability of nonlinear pantograph systems. The validity of our results is finally illustrated by three examples.", "Keywords": "Finite-time stability ; Pantograph system ; Impulse ; Average impulsive interval ; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> method", "DOI": "10.1016/j.sysconle.2021.105054", "PubYear": 2021, "Volume": "157", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Computational Science, Wuyi University, Jiangmen, Guangdong 529020, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Computational Science, Wuyi University, Jiangmen, Guangdong 529020, China"}], "References": [{"Title": "A Novel Delay-Dependent Criterion for Global Power Stability of Cellular Neural Networks with Proportional Delay", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "1", "Page": "867", "JournalTitle": "Neural Processing Letters"}, {"Title": "Exponential synchronization and polynomial synchronization of recurrent neural networks with and without proportional delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "372", "Issue": "", "Page": "109", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 91001697, "Title": "Integrated energy storage systems with the Jordanian electrical power grid", "Abstract": "Advantageous integrated energy storage systems (IESS) can be utilized for power systems’ operations generating set units with maximum possible efficiency, optimizing of unit commitment, integrating of more renewable energy generators, and utilizing renewable energy generators as peak power plants. Additionally, IESS implementation can aid in controlling the Jordanian national grid's frequencies under faults circumstances, maintaining the equilibrium between the electric loads and the generating capacities, and utilizing the existence of tie line in feasible applications, and maintaining the grid's frequency. This work presents an overall technical and feasibility studies on IESS implementation in power systems. New algorithms illustrated in flow charts present detailed mechanism to control the power flow and to store or discharge energy upon the need and load demand. Different energy storage systems are explored and presented as well. A study plan followed by recommendations are presented to enhance the grid's behavior and transition it into a smart grid.", "Keywords": "Charging process ; Discharging process ; Energy management applications ; Frequency stabilization ; Power quality and reliability function ; Renewable energy generators (REGs)", "DOI": "10.1016/j.compeleceng.2021.107470", "PubYear": 2021, "Volume": "96", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Energy Engineering Department, German Jordanian University, Amman, Jordan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Energy Engineering Department, German Jordanian University, Amman, Jordan;Prof. <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Energy Engineering Department, German Jordanian University, Amman, Jordan"}], "References": []}, {"ArticleId": 91001750, "Title": "The emergence of collective obstacle avoidance based on a visual perception mechanism", "Abstract": "Coordinated and ordered collective behaviors that emerge from decentralized and local self-organizing interactions among individuals widely exists in many biological groups. The classical models often focus on the imitation of biological phenomena with the given position and velocity, but ignore the modeling of individual perception during the emergence of collective behaviors. Based on the mechanism of visual perception, we present a model named visual perception-decision-propulsion (VPDP) and clearly define several evaluation indicators to explore the emergence of collective obstacle avoidance in flocks. Within this model, there is no centralized control and no information exchange among individuals. Instead, individuals interacting with others merely rely on the perceptual information represented by whether the visual field is occupied by neighbors. We demonstrate that our model effectively achieves collective obstacle avoidance, preserving a safe distance between individuals and obstacles while maintaining relatively good coherence. Moreover, the sensitivity analysis of the parameters indicates the robustness of our VPDP model. Hence, this paper provides a novel model for collective obstacle avoidance based on visual perception as well as a detailed analysis. Introduction Collective behavior widely exists in different biological groups [43], including bacteria [9], [36], microtubules [38], histiocytes [39], [12], locusts [8], [5], [22], fishes [25], [20], [21], [18], [28], [6], [27], birds [26], [3], humans [29], [15] and other organisms. Coordinated and ordered collective behavior in biological groups emerges from relatively simple and local self-organizing interactions among individuals [33]. Thus, biological groups show distribution, self-adaptation, robustness and other intelligent characteristics in all kinds of situations, such as, clusters, obstacle avoidance and cooperative hunt [1], [13]. The research objectives in this field attempt to provide explanations for collective behavior similar to the above movement patterns shown by biological groups in terms of individual interactions and to create artificial swarm systems that exhibit the characteristics of biological groups. Therefore, modeling collective behavior and exploring corresponding intrinsic mechanisms is very challenging for experts in various fields. Several models have been proposed to solve this problem over the past several decades, such as the Boids model, Vicsek model, and Couzin model. The classical models of collective behavior were followed as SAC rules: separation (increasing the distance between individuals to achieve collision avoidance), alignment (narrowing the velocity difference to achieve coherent movement) and cohesion (reducing the distance between individuals to achieve group aggregation) [43]. The SAC rules were originally used to simulate the collective behavior of fishes and birds. The random rotation model presented by Aoki [2] and the Boids model presented by Reynolds [34] both belonged to this type of model. Later, a physicist proposed the Vicsek model [42], which only considered the alignment while taking neighbor velocity as input to explore the minimal realization for collective behavior. A biologist developed the Couzin model [10] which is now widely used in theoretical biology: it has been extended to achieve collective motion in group robotics. Considerable progress in the aforementioned classical models has been achieved in recent years, but the interactions that are distinct from physical reality in the models are constructed from phenomenological view. In addition, the models suggest that individuals respond to the positions and velocities of their neighbors by focusing closely on the decision and propulsion mechanisms of collective behavior. These models neglect the cognition involved in the perception mechanism; however, perception is a crucial step of the transition from animal groups to artificial swarm systems. Thus, classical approaches seriously hinder researchers from exploring the intrinsic mechanism of collective behavior. Nevertheless, neuroscience has quickly developed with respect to the cognition of biological perception. Geisler et al. [16] used a descriptive function method to measure the detection performance of the primary visual cortex of higher animals and demonstrated that edge information in images is extracted by the visual cortex. Since then, numerous researchers have increasingly explored collective behavior based on visual projections, which is a promising direction for explaining emergent collective behavior in various creatures. Bastien et al. [4] showed that a purely vision-based model of collective behavior is sufficient to generate organized collective behavior even in the absence of spatial representation and collision. Strandburg et al.[37] analyzed the motion of fishes in groups using visual perception information as input and found that the interaction network constructed from visual projection is different from those constructed from measurement and topology. Rosenthal et al.[35] also studied interaction networks in fishes by calculating the visual field of individuals and revealed the intrinsic mechanism of social contagion. From perception cognition, complex cascades of behavioral change can be predicted, full evidence of which is provided by these approaches. Using a flock of birds as the research object, Pearce et al. [32] modeled flocks of birds based on visual projection and provided a density control method. For researching collective behavior in human crowds, Moussaïd et al. [30] proposed a cognitive science approach guided by visual information to predict self-organization phenomena and generate crowd turbulence, which is a phenomenon that has been observed during crowd disasters. Lavergne et al. [23] demonstrated that formation and cohesion can be generated in artificial swarm systems merely by changing the motility of individuals in response to visual perception; they applied this principle in a real system composed of Janus particles that become active in light conditions. Although these models of collective behavior can achieve various motion patterns similar to those of biological groups, they are often constructed from the point of view of phenomena shown by biological groups. For example, these types of models consider the interaction of velocity alignment, which takes neighbor velocity as input to reproduce the coherence. However, the neighbor velocity is difficult to obtain in artificial swarm systems, and therefore, such models clearly present a substantial challenge when constructing artificial swarm systems. In addition, most artificial swarm systems to date have been individually programmed for predefined trajectories or centrally controlled, which results in a weak ability of individuals to tackle outburst incidents. Meanwhile, neuroscience has quickly developed with respect to the cognition of biological visual perception, and the visual perception mechanism of visual cortex used by biological individuals to acquire outside information is known. According to the above research, the field of collective behavior still needs more effective methods, but only a few studies have considered obstacle avoidance [17], [40], [41]. Moreover, the larger the number of individuals in a group is, the larger the momentum of that group will be, which increases the pressure on individuals when the group encounters obstacles [41]. Similarly, individuals are more likely to become injured and die in disaster events [7], [19]. Therefore, research on obstacle avoidance shown by biological groups not only provides assistance for artificial swarm systems but can also help reduce the problem of casualties during disaster events. In this study, we develop a new method from the visual perception perspective to describe the emergence of collective obstacle avoidance. From a sensory neuroscience perspective, we abstract the method of information acquisition by biological individuals as edge detection based on a visual perception mechanism and present a model named visual perception-decision-propulsion (VPDP) to represent the formation process of collective obstacle avoidance. Within this model, the interaction of velocity alignment is not considered, and each individual separated from the group can independently avoid obstacles. Moreover, no central control exists in this system and no information exchange occurs between individuals. Instead, individuals make decisions while merely relying on visual perception information. Hence, artificial swarm systems built from this model even work normally under communication interference. Our model effectively reproduces collective obstacle avoidance with coherence. In particular, groups can form multiple subgroups when they encounter an obstacle. The following is a summary of the major contributions of this paper. 1. The VPDP model is presented to explore collective obstacle avoidance. This model focuses on the perception process represented by whether the visual field is occupied from a visual neuroscience perspective; moreover, we clearly define several evaluation indicators to evaluate the model’s effectiveness with respect to obstacle avoidance. 2. We constructed 5 scenes to test the model experimentally. By analyzing the data obtained from simulations of these scenes, we show that the model effectively achieves obstacle avoidance by preserving a safe distance between individuals and obstacles while maintaining relatively good coherence. Although group coherence immediately declines when the group encounters obstacles, it quickly returns. 3. The model contains 3 internal parameters and 3 external empirical parameters. We conducted a sensitivity analysis of internal and external parameters to analyze the role they play in the model and found that wider behaviors of obstacle avoidance with good coherence occur under different combinations of internal parameters such that the attraction-to-repulsion ratio is small and the changes in velocity magnitude and direction are large and balanced. For external empirical parameters, a moderate radius, which is used to divide the area, must be chosen. In addition, a large group size means that individuals’ fields of view are relatively saturated and that they cannot effectively interact with others; therefore, achieving obstacle avoidance with good coherence under this condition is difficult. Moreover, a larger field of view of individuals should be chosen so that the group achieves obstacle avoidance with good coherence. The remainder of this paper is organized as follows: in the second section, VPDP is presented to model collective obstacle avoidance. In the third section, we explicitly define several indicators for evaluating the model’s effectiveness in reproducing the obstacle avoidance of biological groups. To further validate our model, we perform numerical experiments in predetermined scenes with obstacles and show the results and analysis in the fourth section. Finally, we summarize and discuss our work in the fifth section. Section snippets The Visual Perception-Decision-Propulsion Model As a necessary condition for constructing artificial swarm systems, the realization of collective obstacle avoidance is both complex and promising. In the process of collective behavior formation, individuals experience perception, decision and propulsion processes. The perception process refers to individuals acquiring the position and velocity information of their neighbors, and the propulsion process refers to the actions that individuals subsequently need to perform. The decision process is Evaluation Indicators Based on the above VPDP model, we define the following indicators, which represent different aspects of collective behavior, to assess the quality of obstacle avoidance. To monitor the rotational motion of the group, we express the average normalized velocity in local-angle polar (LAP) coordinates[41], I LAP = v ‾ y ( t ) r ‾ x ( t ) - v ‾ x ( t ) r ‾ y ( t ) v 0 r ‾ x 2 ( t ) + r ‾ y 2 ( t ) , where r ‾ x ( t ) and r ‾ y ( t ) indicate the average positions of individuals relative to the center of the scene, and v ‾ x ( t ) and v ‾ y ( t ) represent the Case Study To evaluate the quality of the VPDP model for modeling obstacle avoidance of biological groups, we conducted several experiments to analyze the model based on the above evaluation indicators. At the beginning of each experiment, we randomly placed individuals with random velocities ( v ∈ [ 0 , 1 ] and ψ ∈ [ - π , π ] ) in the preset scenes and set the unit time Δ t = 1 , upper time limit t max = 5000 , radius of individuals l = 1 , predetermined velocity threshold v 0 = 1 , and velocity relaxation rate γ = 0.1 . We first Conclusion Collective behavior widely exists in biological groups and reveals fascinating patterns. To compensate for the shortcomings of the existing model and provide guidance for transferring biological group characteristics to artificial swarm systems, we proposed the VPDP model presented in this article to explore the obstacle avoidance of biological groups from a biological perception perspective [24]. Although this model does not include an alignment term, which uses the velocity information of CRediT authorship contribution statement Jingtao Qi: Formal analysis, Methodology, Writing - original draft. Liang Bai: Writing - review & editing. Yandong Xiao: Conceptualization, Supervision, Writing - review & editing. Yingmei Wei: Writing - review & editing. Wansen Wu: Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgment This work is supported by the National Natural Science Foundation of China under Grant 61902418 and the Postgraduate Scientific Research Innovation Project of Hunan Province under Grant CX20200076. References (44) T. Vicsek et al. Collective motion Physics Reports (2012) A. Strandburg-Peshkin et al. Visual sensory networks and effective information transfer in animal groups Current Biology (2013) B.H. Lemasson et al. Collective motion in animal groups from a neurobiological perspective: the adaptive benefits of dynamic sensory loads and selective attention Journal of Theoretical Biology (2009) N.O. Handegard et al. The dynamics of coordinated group hunting and collective information transfer among schooling prey Current Biology (2012) S. Bazazi et al. Collective motion and cannibalism in locust migratory bands Current Biology (2008) L. Angelani Collective predation and escape strategies Physical Review Letters (2012) Ichiro AOKI.A simulation study on the schooling mechanism in fish. NIPPON SUISAN GAKKAISHI, 48(8):1081–1088,... M. Ballerini, N. Cabibbo, R. Candelier, A. Cavagna, E. Cisbani, I. Giardina, V. Lecomte, A. Orlandi, G. Parisi, A.... R. Bastien et al. A model of collective behavior based purely on vision Science Advances (2020) N.W.F. Bode and M.J. Seitz. Using hidden markov models to characterise intermittent social behaviour in fish shoals.... A. Bottinelli, D.T.J. Sumpter, J.L. Silverberg. Emergent structural mechanisms for high-density collective motion... J. Buhl et al. From disorder to order in marching locusts Science (2006) X. Chen, X. Dong, A. Be’er, H.L. Swinney, H.P. Zhang. Scale-invariant correlations in dynamic bacterial clusters.... I.D. Couzin et al. Collective memory and spatial sorting in animal groups Journal of Theoretical Biology (2002) T.W. Cronin, S. Johnsen, N.J. Marshall, E.J. Warrant. Visual ecology. Princeton University Press,... T.S. Deisboeck et al. Collective behavior in cancer cell populations Bioessays (2009) P. DeLellis, G. Polverino, G. Ustuner, N. Abaid, S. Macrı̀, E.M. Bollt, M. Porfiri. Collective behaviour across animal... J. Dentler, M. Rosalie, G. Danoy, P. Bouvry, S. Kannan, M.A. Olivares-Mendez, H. Voos. Collision avoidance effects on... I.J. Farkas et al. Patterns in the collective behavior of humans W.S. Geisler et al. Visual cortex neurons in monkeys and cats: detection, discrimination, and identification Visual Neuroscience (1997) F. Gökçe et al. To flock or not to flock: the pros and cons of flocking in long-range migration of mobile robot swarms D. Helbing et al. Simulating dynamical features of escape panic Nature (2000) View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.10.039", "PubYear": 2022, "Volume": "582", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha 410073, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha 410073, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Xiao", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha 410073, PR China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha 410073, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha 410073, PR China"}], "References": []}, {"ArticleId": 91001751, "Title": "Event triggered control of connected vehicles under multiple cyber attacks", "Abstract": "This paper investigates an event triggered control problem of connected vehicles under multiple cyber-attacks, including denial-of-service attacks (DoS) and deception attacks, which occur aperiodically. Then, a novel event triggered control scheme is proposed in the presence of multiple cyber-attacks. The upper bound of attack frequency, attack length rate of DoS attacks and the conditions of controller gains are obtained. It is proven that the proposed distributed control scheme can realize the asymptotic tracking performance of connected vehicles while avoiding Zeno behavior. A string stability analysis under multiple cyber-attacks is also provided. Simulation results confirm the effectiveness of the event triggered control schemes under multiple cyber-attacks. Introduction Connected vehicle systems (CVSs) are one of the most promising solutions to the problems faced by the transportation system, such as congestion and air pollution [1], [2]. CVSs can be regarded as vehicle cyber physical system where each vehicle exchanges information with its neighbor vehicles over vehicular ad hoc networks (VANETs). However, connected vehicular networks are vulnerable to network-induced issues like packed dropouts [3], network delay [4] and cyber-attacks [5]. Especially, in the past few years, security issues due to cyber-attacks in the CVSs have received increasing attention. Typical attacks include replay attack, deception attack and DoS attack. Usually, replay attacks are to resend the eavesdropped data to the system intact. The outdated information can mislead platoon members. Deception attack is an attempt to destroy the integrity of sensor and/or control data in order to manipulate the vehicle to approach the adversaries' expected behavior. Adversaries often have adequate knowledge of VANET real-time data to carry out successful deception. Adversaries, for example, might change the intended velocity of a follower vehicle based on information from its predecessor [6]. DoS attacks aim to break the communication link and prevent vehicles from exchanging information. From a technological standpoint, attackers can launch DoS attacks by disrupting the radio frequency of a wireless communication channel, resulting in congestion [7]. These issues may seriously affect the performance or even cause the failure of cooperative control of connected vehicles. In the worst case, the entire network of CVSs may collapse when one or more vehicles are hijacked, since the effect may pass on to neighboring vehicles. This paper aims to find a resilient control methodology for connected vehicles under DoS attacks and deception attacks. To improve the resistance to cyber-attacks for CVSs, some results are reported. For example, ref. [8] proposes a framework for detecting deception attacks on connected vehicles. Ref. [9] designs an observer to detect and estimate DoS attacks for CVSs, and further analyze the observer convergence. Ref. [10] proposes a more practical scheme to detect deception attacks in connected vehicles with model error and sensor noises. A designed filter containing two ellipsoidal sets is used to detect cyber-attacks in ref. [11], and recovery mechanism is also provided. Most existing results mainly concentrate on the detection and estimation of cyber-attacks and system performance analysis under a pre-designed controller. However, there are still few reports on the resilient control of CVS. Recently, ref. [12] proposes a decentralized hybrid controller to resilient to attacks while ensuring string stability. Ref. [13] proposes a collaborative control algorithm for connected vehicles to defense cyber-attacks. Ref. [14] proposes an adaptive platooning control of autonomous vehicles in the presence of DoS attacks. However, these papers only focus on one type of malicious attack, and do not consider the control scheme under multiple attacks. Hence, to design an effective controller for CVSs to defense multiple attacks and ensure the required performance is our first motivation. The limited communication resource is one of the major problems that restrict the application of CVSs. Most of the existing communication imperfections (such as communication delay, packet loss, etc.) are greatly related to the overuse of communication resources [15]. Most of existing results on automated platoon control systems are time triggered. This allows each vehicle send its state data to its adjacent vehicles in each sampling period [16], [17]. However, this greatly wastes the communication resources, especially when the transmission states do not change rapidly. Event triggered mechanism can solve this problem. Event triggered mechanism transmits sampled-state data only when the vehicle state changes beyond some pre-designed thresholds [18], [19]. The event triggered scheme has already been widely employed in multiagent systems [20] and network systems [21]. Recently, it also has been used in platoon system [22], [23], [24], [36], [45], [46]. For example, ref. [22] proposes an event triggered control of platoons with actuator delays. Ref. [36] designs a fully distributed event triggered control scheme for vehicle platoons with actuator uncertainty to achieve leader–follower consensus. However, almost all existing results on event triggered mechanism are studied in a perfect vehicle network environment [25], [26], [27]. Unfortunately, since event triggered mechanism has less communication, it is more sensitive to the reliability of communication. Some recent research results consider event triggered scheme under unreliable communication links for vehicle platooning. For example, Refs. [28], [29] propose event triggered control strategies for platoon system taking communication delay into consideration. Refs. [30], [31] establish event triggered schemes to maintain string stable of vehicle platooning considering packets dropout. However, their common points and limitations are that they only adjust the feedback parameters and ignore the effects of attacks on the event trigger threshold. Furthermore, ref. [37] proposes an adaptive event triggered vehicular platoon control with communication time delay and package dropout to achieve a good balance between communication usage and vehicle following performance. Ref. [38] proposes a resilient event triggered control for vehicle platoon, and presents a co-design criterion to determine controller and triggering condition. Up to now, most of the existing results on CVSs investigate a single attack for simplicity of analysis and design. In practice, however, a CVS may be subject to multiple cyber-attacks. Thus, it is necessary to find a control methodology that can simultaneously defense various cyber-attacks and save communication resources. This is our second motivation. This paper investigates an event triggered control problem for CVSs with deception attacks and DoS attacks. Our aim is to get a better understanding as to how these cyber-attacks affect the CVS and under what conditions the system can achieve a certain performance. By introducing a novel model for the CVS involving the effects of deception attacks and DoS attacks, an event-triggered control method is derived, which yields a system resilient to these cyber-attacks with reduced communication cost. The main contributions are threefold: 1) A mathematical model is established for CVSs, taking deception attacks and DoS attacks into consideration. 2) An event triggered scheme which is affected by attacks is developed, and a distributed controller is proposed to deal with second-order CVSs under multiple attacks. The conditions of controller gains and the explicit upper bounds of DoS attack frequency and length rate are obtained to ensure asymptotic tracking of follower vehicles while Zeno behavior is avoided under multiple malicious attacks. 3) The string stability is analyzed by H ∞ approach under multiple cyber-attacks based on the established model. It is characterized as the feasibility of an optimization problem to avoid the amplification of the spacing error in CVSs. The remainder of this article is organized as follows. The communication graph, modeling of vehicles and attacks, the event triggered scheme, and the problem formulation are present in Section 2. Some main results are given to achieve asymptotic stability and string stability of the CVSs under deception attacks and DoS attacks in Section 3. Then, the simulations are implemented in Section 4 to verify the effective of the designed method. Finally, the conclusions are drawn in Section 5. Notations : N stands for the set of the positive integers; R , R + , R n × n R + are real number, positive real number and n × n real matrix respectively; I N and 0 NN represent the N -dimension identity matrix and N × N zero matrix respectively. ⊗ is the Kronecker product.   ·   means to take the absolute value and    ·    denotes to take modulus value. max ( · ) and min ( · ) stand for taking the maximum and minimum values. E ( · ) represents mathematical expectation. Section snippets Communication graphs Vehicles network in CVSs can be depicted as a digraph, denoted by G = ( V , E , A ) , with vehicles as node set V and edge set E ⊆ V × V . V = { 1 , . . . , N } consists of N follower vehicles. A ∈ R n × n is the adjacency matrix with A i , j = a i , j ∀ i , j ∈ V . when no communication between vehicle j and vehicle i , a i , j = 0 , otherwise, a i , j = 1 . Given a graph G , it has a spanning tree if there exists at least one way from one vehicle to all the other vehicles. The Laplacian matrix L of the graph G is L = ( l ij ) N × N , l ij = - a i , j if i ≠ j , l ij = ∑ j Main results In this section, one firstly determines the threshold of event triggering. Then, some sufficient conditions are given to realize asymptotic tracking of follower vehicles for CVSs (1) under multiple cyber-attacks with event triggered control protocol (12). The string stability is also analyzed. From (6), one has e ( t ) 2 = e s i ( t ) 2 + e v i ( t ) 2 ⩽ κ 1 [ ( L ⊗ I N ) ( e s ( t ) + ξ s ( t ) ) + δ ] T [ ( L ⊗ I N ) ( e s ( t ) + ξ s ( t ) ) + δ ] + κ 1 [ ( L ⊗ I N ) ( e v ( t ) + ξ v ( t ) ) + σ ] T [ ( L ⊗ I N ) ( e v ( t ) + ξ v ( t ) ) + σ ] + κ 2 ( ξ s ( t ) + δ ) T ( P ⊗ I N ) ( ξ s ( t ) + δ ) + κ 2 ( ξ v ( t ) + σ ) T ( P ⊗ I N ) ( ξ v ( t ) + σ ) = κ 1 [ e T ( t ) ( I 2 Numerical simulations Numerical simulations of CVSs composed by a leader vehicle and N ( N = 4 ) follower vehicles are conducted to verify the main results in this paper. The reference velocity of the leader vehicle is shown in Fig. 3. The initial states of vehicles are taken as s 0 ( 0) v 0 ( 0) T = 0 15 T , s 1 ( 0) v 1 ( 0) T = - 6 9 T , s 2 ( 0) v 2 ( 0) T = - 11 5 T , s 0 ( 0) v 0 ( 0) T = - 19 3 T , s 0 ( 0) v 0 ( 0) T = - 25 0 T . Considering the network topology as Fig. 4, the Laplacian matrix is L = 1 0 0 0 - 1 1 0 0 0 - 1 1 0 0 0 - 1 0 . Suppose the leader vehicle only communicates with the first Conclusion This article has investigated an event triggered control problem for second-order CVSs under multiple cyber-attacks. A control protocol combined with the event triggered scheme is proposed to ensure the performance of CVSs, and save the communication resources. Moreover, some sufficient conditions have been given to achieve asymptotic tracking of follower vehicles under Assumption 1, Assumption 2. It has been found that the CVSs can achieve the asymptotic stability if attack length rate and CRediT authorship contribution statement Yangguang Xu: Conceptualization, Methodology, Software, Validation, Investigation, Writing – original draft. Ge Guo: Supervision, Writing – review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgment This work was supported by the National Natural Science Foundation of China under grants 62173079 U1808205 . References (47) D. Yang et al. Decentralized event triggered consensus for linear multi-agent systems under general directed graphs Automatica (2016) S. Xiao et al. Resilient distributed event triggered control of vehicle platooning under DoS attacks IFAC-PapersOnLine (2020) T. Dong et al. Leader-following secure consensus for second-order multi-agent systems with nonlinear dynamics and event triggered control strategy under DoS attack Neurocomputing (2020) Y. Wei et al. Event triggered platoon control of vehicles with time-varying delay and probabilistic faults Mech. Syst. Sig. Process. (2017) S. Wen et al. Event triggered cooperative control of vehicle platoons in vehicular ad hoc networks Inf. Sci. (2018) G.e. Guo et al. A distributed event triggered transmission strategy for sampled-data consensus of multi-agent systems Automatica (2014) C. Peng et al. Event triggered communication and H∞ control co-design for networked control systems Automatica (2013) A. Petrillo et al. A collaborative approach for improving the security of vehicular scenarios: the case of platooning Comput. Commun. (2018) D. Caveney Cooperative vehicular safety applications IEEE Control Syst. (2010) A. Vahidi et al. Research advances in intelligent collision avoidance and adaptive cruise control IEEE Trans. Intell. Transp. Syst. (2003) J. Li et al. Robust fault detection filter design for interconnected systems subject to packet dropouts and structure changes IET Control Theory Appl. (2018) W.P.M.H. Heemels et al. Networked control systems with communication constraints: tradeoffs between transmission intervals, delays and performance IEEE Trans. Autom. Control (2010) S. Dadras et al. Vehicular platooning in an adversarial environment D.R. Ding et al. Security control for discrete-time stochastic nonlinear systems subject to deception attacks IEEE Trans. Syst., Man, Cybern.: Syst. (2018) D. Zhang, G. Feng, A New Switched System Approach to Leader-Follower Consensus of Heterogeneous Linear Multiagent... Z. Ju et al. Deception attack detection and estimation for a local vehicle in vehicle platooning based on a modified UFIR estimator IEEE Internet Things J. (2020) Z.A. Biron et al. Real-time detection and estimation of denial of service attack in connected vehicle systems IEEE Trans. Intell. Transp. Syst. (2018) Z. Ju et al. Distributed deception attack detection in platoon-based connected vehicle systems IEEE Trans. Veh. Technol. (2020) E. Mousavinejad et al. Distributed cyber attacks detection and recovery mechanism for vehicle platooning IEEE Trans. Intell. Transp. Syst. (2020) R. Merco, F. Ferrante, P. Pisu, A Hybrid Controller for DOS-Resilient String-Stable Vehicle Platoons, IEEE Trans.... S. Xiao, X. Ge, Q L. Han, Y. Zhang, Secure Distributed Adaptive Platooning Control of Automated Vehicles Over Vehicular... N.T. Hung et al. Cooperative path following of constrained autonomous vehicles with model predictive control and event triggered communications Int. J. Robust Nonlinear Control (2020) S. Oncu et al. Cooperative adaptive cruise control: network-aware analysis of string stability IEEE Trans. Intell. Transp. Syst. (2014) View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.10.032", "PubYear": 2022, "Volume": "582", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Dalian Maritime University, Dalian 116026, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Synthetical Automation for Process Industries, Northeastern University, Shenyang 110819, China;School of Control Engineering, Northeastern University Qinhuangdao, Qinhuangdao 066004, China;Corresponding author at: State Key Laboratory of Synthetical Automation for Process Industries, Northeastern University, Shenyang 110819, China"}], "References": [{"Title": "Leader-following secure consensus for second-order multi-agent systems with nonlinear dynamics and event-triggered control strategy under DoS attack", "Authors": "<PERSON> Dong; <PERSON><PERSON> Gong", "PubYear": 2020, "Volume": "416", "Issue": "", "Page": "95", "JournalTitle": "Neurocomputing"}, {"Title": "Resilient Distributed Event-Triggered Control of Vehicle Platooning Under DoS Attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1807", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 91001842, "Title": "Food recommendation with graph convolutional network", "Abstract": "Food recommendation has attracted increasing attentions to various food-related applications and services. The food recommender models aim to match users’ preferences with recipes, where the key lies in the representation learning of users and recipes. However, ranging from early content-based filtering and collaborative filtering methods to recent hybrid methods , the existing work overlooks the various food-related relations, especially the ingredient-ingredient relations, leading to incomprehensive representations. To bridge this gap, we propose a novel model Food recommendation with Graph Convolutional Network (FGCN), which exploits ingredient-ingredient, ingredient-recipe, and recipe-user relations deeply. FGCN employs the information propagation mechanism and adopts multiple embedding propagation layers to model high-order connectivity across different food-related relations and enhance the representations. Specifically, we develop three types of information propagation: (1) ingredient-ingredient information propagation, (2) ingredient-recipe information propagation, and (3) recipe-user information propagation. To validate the effectiveness and rationality of FGCN, we conduct extensive experiments on a real-world dataset. The results show that the proposed FGCN outperforms the state-of-the-art baselines. Further in-depth analyses reveal that FGCN could alleviate the sparsity issue in food recommendation. Introduction Food recommender systems play important roles in a wide spectral of lifestyle applications such as Koubei<sup>1</sup> and NYT Cooking<sup>2</sup>. This is because food choices play an important role in users’ daily life, e.g., affecting users’ healthy [6], [9]. The target of a food recommender system is to predict users’ preference over recipes, which can be served by restaurants or cooked by the user depending on the business of the application. The task of building a food recommender system is typically formulated as a machine learning task [28], [32], i.e., learning users’ preferences from historical interactions on the recipes. Aiming to properly match the user with a recipe, the key lies in the representation learning of user and recipe. In this work, we explore the central theme of learning comprehensive user and recipe representations to enhance food recommendation. Existing food recommender models are mainly in three categories that emphasize different types of information for representation learning: • Collaborative filtering. These methods [28], [32] learn user and recipe representations from historical interactions through the general collaborative filtering models. These methods are easy to implement, but ignore the domain knowledge in food recommendation. They thus lead to coarse-grained recipe representation, degrading food recommendation performance. • Content-based filtering. These methods explore food contents ( e.g., ingredients and photos) to construct recipe representation [23], [27], [33]. They largely ignore user interests, failing to please the user. • Hybrid methods. Recent work [14] incorporates food contents into the collaborative filtering model, justifying the benefit of considering richer food information. In this light, we further consider the various food-related relations, especially the ingredient-ingredient relations. Fig. 1 provides an intuitive example where the user, recipe, and ingredient are represented as an orange circle, gray rectangle, and green triangle, respectively. The dotted line represents the relation connecting two ingredients, e.g., having the same cooking method. Due to such connections, recipes i 2 and i 4 are indeed similar to each other, while their food content features lack overlap. As such, ignoring the ingredient-ingredient relation will fail to recommend i 4 when serving the user u 2 , pushing food recommendation to account for such relations. However, the target is non-trivial to achieve since the complex interactions of ingredient-ingredient, ingredient-recipe, and recipe-user require a careful model design. Towards this end, we propose Food Graph Convolutional Network (FGCN), which represents the rich interactions as a graph and distill useful signals from the local structure among nodes ( i.e., users, recipes, and ingredients). To enhance the representation learning, FGCN performs information propagation over the graph with multiple graph convolution layers, which model the complex and high-order connectivity. In this way, FGCN is able to exploit ingredient-ingredient, ingredient-recipe, recipe-user semantics deeply. More specially, we design three kinds of information propagation: (1) ingredient-ingredient information propagation, (2) ingredient-recipe information propagation, and (3) recipe-user information propagation. Extensive experiments on the real-world dataset validate the effectiveness of the proposed FGCN, which outperforms the state-of-the-art method [35] by 5.4%. In summary, this work makes the following main contributions: • To the best of our knowledge, this is the first work to consider various food-related relations in food recommendation. • We develop a novel model FGCN, which learns comprehensive user and recipe representations by exploiting the graph of ingredient-ingredient, ingredient-recipe, and recipe-user relations. • We conduct extensive experiments on the real-world food dataset, where the proposed FGCN achieves state-of-the-art performance. In the following, Section 2 describes the task formulation, Section 3 introduces the proposed framework, then Section 4 presents experiment settings and analyzes the results. Finally, Section 5 reviews related work, followed by the conclusions in Section 6. Section snippets Task Formulation Let G denote a heterogeneous graph with three types of nodes to represent users, recipes, and ingredients. The connections within G can be seen as three subgraphs: (1) the user-recipe bipartite graph, which encodes the user-recipe interactions; (2) recipe-ingredient bipartite graph, which represents the relations between recipes and ingredients; and (3) ingredient graph, which encodes the relations among ingredients. In particular, • User-Recipe Bipartite Graph: It includes the historical Methodology As shown in Fig. 2, the framework consists of three key components: (1) embedding layer, which includes user embeddings, recipe embeddings, and ingredient embeddings; (2) information propagation, which purifies the representations by exploiting ingredient-ingredient, ingredient-recipe, and user-recipe relations; (3) prediction layer, which aggregates the purified representations and predicts the score for each user-recipe pair. In the following, we elaborate on the components one by one. Experiments We perform extensive experiments on a large-scale dataset to answer the following research questions: • RQ1: How does FGCN perform as compared with the state-of-the-art food recommendation methods? • RQ2: How do different components ( i.e., model depth and aggregator selection) influence the effectiveness of FGCN? • RQ3: What is the key hyper-parameter of FGCN and how does it impact FGCN’s performance? • RQ4: How does FGCN perform with respect to different sparsity levels in user groups? In the following, we Related Work We firstly review graph-based recommendation, then introduce food recommendation methods in this section. Conclusion This work argues that there exist complex relationships among ingredient-ingredient, ingredient-recipe, and recipe-user interactions, which are important for food recommendation. In this light, we propose Food recommendation with Graph Convolutional Network (FGCN), which employs the information propagation mechanism and adopts multiple embedding propagation layers to model high-order connectivity and enhance the representation learning. Extensive experiments conducted on a large-scale dataset CRediT authorship contribution statement Xiaoyan Gao: Conceptualization, Methodology, Software, Writing - original draft. Fuli Feng: Methodology, Supervision, Writing - review & editing. Heyan Huang: Supervision, Writing - review & editing. Xian-Ling Mao: Supervision, Writing - review & editing. Tian Lan: Software. Zewen Chi: Software. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgement This work is supported by the National Natural Science Foundation of China [Grant Nos. No. U19B2020, 61772076, 61751201]. Xiaoyan Gao is currently pursuing the Ph.D. degree in the School of Computer Science, Beijing Institute of Technology. She received the B.E. degree in software engineering from Fuzhou University, Fujian, in 2012. Her research interests include recommendation and natural language processing. She has published several works in MM, TMM, and TOIS. References (47) L. Zhang et al. Modeling hierarchical category transition for next poi recommendation with uncertain check-ins Information Sciences (2020) W. Yuan et al. Attention-based context-aware sequential recommendation model Information Sciences (2020) W. Wang et al. Market2dish: Health-aware food recommendation ACM Transactions on Multimedia Computing, Communications, and Applications (TOMM) (2021) J. Ni et al. An effective recommendation model based on deep representation learning Information Sciences (2021) Z. Liu et al. Combining graph neural networks with expert knowledge for smart contract vulnerability detection IEEE Transactions on Knowledge and Data Engineering (TKDE) (2021) Y.C. Lee et al. M-bpr: A novel approach to improving bpr for recommendation with multi-type pair-wise preferences Information Sciences (2021) S. Fu et al. Hesgcn: Hessian graph convolutional networks for semi-supervised classification Information Sciences (2020) M. Chen et al. Eating healthier: Exploring nutrition information for healthier recipe recommendation Information Processing & Management (2020) D. Cao et al. Video-based recipe retrieval Information Sciences (2020) X. Cai et al. A many-objective optimization recommendation algorithm based on knowledge mining Information Sciences (2020) Bachmann, G., Bécigneul, G., Ganea, O., 2020. Constant curvature graph convolutional networks, in: International... Berg, R.v.d., Kipf, T.N., Welling, M., 2018. Graph convolutional matrix completion, in: Proceedings of the 24th ACM... J. Chen et al. Zero-shot ingredient recognition by multi-relational graph convolutional network W. Chen et al. Multi-interest diversification for end-to-end sequential recommendation ACM Transactions on Information Systems (TOIS) (2021) Y. Chen et al. Bayesian feature interaction selection for factorization machines Artificial Intelligence (2021) D. Elsweiler et al. Exploiting food choice biases for healthier recipe recommendation F. Feng et al. Cross-gcn: Enhancing graph convolutional network with k-order feature interactions IEEE Transactions on Knowledge and Data Engineering. (2021) F. Feng et al. Should graph convolution trust neighbors? a simple causal inference method Freyne, J., Berkovsky, S., 2010. Intelligent food planning: personalized recipe recommendation, in: Proceedings of the... X. Gao et al. Hierarchical attention network for visually-aware food recommendation IEEE Trans. Multimedia (2020) Gori, M., Pucci, A., Roma, V., Siena, I., 2007. Itemrank: A random-walk based scoring algorithm for recommender... X. He et al. Lightgcn: Simplifying and powering graph convolution network for recommendation X. He et al. Birank: Towards ranking on bipartite graphs IEEE Transactions on Knowledge and Data Engineering (2016) View more references Cited by (0) Recommended articles (6) Xiaoyan Gao is currently pursuing the Ph.D. degree in the School of Computer Science, Beijing Institute of Technology. She received the B.E. degree in software engineering from Fuzhou University, Fujian, in 2012. Her research interests include recommendation and natural language processing. She has published several works in MM, TMM, and TOIS. Fuli Feng is a research fellow in National University of Singapore (NUS). He received his Ph.D. in Computer Science from NUS in 2019. He has over 40 publications appeared in several top conferences such as SIGIR, WWW, and ACMMM, and journals including TKDE and TOIS. His work on recommender system has received the Best Poster Award in WWW 2018 and the Honorable Mention of Best Paper Award in SIGIR 2021. Moreover, he has served as the PC member for top-tier conferences including SIGIR, WWW, SIGKDD, NeurIPS, and ACMMM, and the invited reviewer for prestigious journals such as TPAMI, TNNLS, TOIS, and TKDE. Heyan Huang received the BE degree in computer science from Wuhan University, in 1983, the ME degree in computer science and technology from the National University of Defense Technology, in 1986, and the PhD degree from the China Academy of Sciences, Institute of Computer Technology, in 1989. Now, she is a professor, doctoral tutor, and president in School of Computer, Beijing Institute of Technology, and the director of the Research Center of High Volume Language Information Processing and Cloud Computing. Her current research interests mainly focus on natural language processing. Xian-Ling Mao received the PhD degree from Peking University, in 2013. Now, he is an assistant professor in the School of Computer Science, Beijing Institute of Technology. His current research interests include Topic Modeling, Learning to Hashing, and Question Answering. Various parts of his work have been published in top conferences and journals including AAAI, TKDE, IJCAI, EMNLP, COLING and CIKM. Tian Lan is currently pursuing the Ph.D. degree in the School of Computer Science, Beijing Institute of Technology. He received the bachelor’s degree from the Beijing institute of technology in 2019. His research interests include natural language processing, automatic evaluation and dialogue systems. Zewen Chi is currently pursuing the Ph.D. degree in the School of Computer Science, Beijing Institute of Technology. He received the B.E. degree in Computer Science from Beijing Institute of Technology in 2018. His research interests include natural language processing and information extraction. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.10.040", "PubYear": 2022, "Volume": "584", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Beijing Institute of Technology, Beijing 100081, China;Beijing Engineering Research Center of High Volume Language Information Processing and Cloud Computing Applications, Beijing 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, National University of Singapore, 117417, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Beijing Institute of Technology, Beijing 100081, China;Beijing Engineering Research Center of High Volume Language Information Processing and Cloud Computing Applications, Beijing 100081, China;Corresponding author at: School of Computer, Beijing Institute of Technology, Beijing 100081, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Beijing Institute of Technology, Beijing 100081, China;Beijing Engineering Research Center of High Volume Language Information Processing and Cloud Computing Applications, Beijing 100081, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Beijing Institute of Technology, Beijing 100081, China;Beijing Engineering Research Center of High Volume Language Information Processing and Cloud Computing Applications, Beijing 100081, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Beijing Institute of Technology, Beijing 100081, China;Beijing Engineering Research Center of High Volume Language Information Processing and Cloud Computing Applications, Beijing 100081, China"}], "References": [{"Title": "Attention-based context-aware sequential recommendation model", "Authors": "Wei<PERSON> Yuan; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "510", "Issue": "", "Page": "122", "JournalTitle": "Information Sciences"}, {"Title": "HesGCN: Hessian graph convolutional networks for semi-supervised classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "484", "JournalTitle": "Information Sciences"}, {"Title": "Video-based recipe retrieval", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "302", "JournalTitle": "Information Sciences"}, {"Title": "Modeling hierarchical category transition for next POI recommendation with uncertain check-ins", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "515", "Issue": "", "Page": "169", "JournalTitle": "Information Sciences"}, {"Title": "A many-objective optimization recommendation algorithm based on knowledge mining", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "537", "Issue": "", "Page": "148", "JournalTitle": "Information Sciences"}, {"Title": "An effective recommendation model based on deep representation learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "542", "Issue": "", "Page": "324", "JournalTitle": "Information Sciences"}, {"Title": "M-BPR: A novel approach to improving BPR for recommendation with multi-type pair-wise preferences", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "255", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 91001955, "Title": "An efficient fine-grained data access control system with a bounded service number", "Abstract": "In a data access control system oriented toward the cloud storage environment, a data owner defines attribute-based access control policies for data files to realize fine-grained data sharing. However, the existing schemes have defects in user execution efficiency and user privacy protection, and they do not consider the problems of user revocation and attribute updates. To this end, we propose a ciphertext policy attribute-based encryption method with verifiable outsourced decryption; this requires a user to complete decryption with the help of a server, but the results of the outsourced decryption can be verified independently. With this new encryption scheme and the technique of k-times anonymous authentication, a new fine-grained data access control system was constructed; this system allows a server to provide users with outsourced decryption services, and users’ computation cost is independent of the size of the underlying access control policy. Moreover, the number of outsourced decryption requests is limited. In addition, the new system supports user revocation and attribute updates and it is provably secure under formal proofs. An efficiency analysis shows that it can be compared with other similar systems in terms of performance, despite the addition of several practical properties. Introduction The rise of cloud computing not only promotes changes in the information technology industry structure and operation mode but also produces a win–win situation for enterprises and individuals. Enterprises can outsource resources to cloud service providers, thus reducing hardware overhead and data maintenance costs. Moreover, customers can enjoy a variety of low-cost cloud services by the pay-per-use mode. The current cloud architecture can be further divided into public clouds and private clouds. Compared with a private cloud, a public cloud is more beneficial for realizing resource sharing, but it also faces more intractable security problems. Specifically, while the owner of cloud resources wants to prevent unauthorized users from accessing data stored in the cloud server, authorized users do not want their access behavior to be monitored and tracked by the cloud service provider (SP). Attribute-based encryption (ABE) is a one-to-many encryption scheme that makes it unnecessary for data owners to know the number and identity of users in the encryption stage. Data owners need only define unique access structures for sensitive data based on user attributes. Moreover, ABE combines attributes with ciphertexts and users’ private keys so that users can access sensitive data only if the intersection of their attribute set and ciphertext attribute set satisfies the access structure defined in the encryption stage. ABE has become an important tool for meeting the needs of fine-grained access control in cloud storage applications [38]. In this paper, we focus on the following five access control issues and assume that in order to provide users with high-quality and secure services, the SP also wants to solve these problems: (1) Fine-grained access control, which allows data owners to define attribute-based access control policies for file resources so that users have different access rights. (2) Outsourcing computation; it is well known that one of the major drawbacks of ABE is that users’ computation cost in the decryption phase is linear with the complexity of the underlying access policy. The ideal case is that the SP provides secure outsourced decryption services to users but cannot obtain any information about the encrypted file. (3) Restricted user access; to prevent malicious users from issuing a large number of outsourced decryption requests and thus excessively occupying the computing resources of SP, the maximum number of anonymous user access should be limited. (4) User accountability; once a malicious user attempts to get rid of the restriction of access times, the SP can detect and update ciphertexts in time, making the malicious user’s decryption private key useless. (5) Attribute updates; when users’ attributes change, the SP can help them update their decryption private keys. In [36], Yu et al. proposed a fine-grained access control system wherein data owners act as content providers and define their attribute-based access control policies. The system takes account of user revocation and allows data owners to outsource most of their computing tasks in the user revocation phase to cloud servers. However, users’ computation cost in the resource access phase depends on the size of the access control policy of the underlying ABE scheme. In [37], Yuen et al. proposed a security model of k-time attribute-based access control and a specific system. This system enables the SP to rent all kinds of cloud services to users and limit the number of users’ visits; however, its disadvantage is that the operation efficiency of the authentication stage is low. Later, Hong et al. [15] extended Yuen et al.’s system to a k-times attribute-based access control system for a fog-to-cloud (F2C) architecture. The main differences between the two schemes are as follows: (1) Role division on the verifier side is different. In Yuen et al.’s system, users gain access to services by performing an authentication protocol with the SP. In Hong et al.’s system, two types of participants are added: the cloud and fog nodes. The SP is responsible for defining access control policies and outsourcing the task of providing cloud services to the cloud, which assigns the specific tasks involved in providing services to the fog nodes under its control. Users enjoy cloud services by executing anonymous authentication protocols with fog nodes. (2) The user tracing mechanism is different. Yuen et al.’s system does not support tracing malicious users. By contrast, Hong et al.’s system allows trusted participants to perform identity tracing on malicious users. (3) The access control method is different. In Yuen et al.’s system, a single SP is responsible for implementing access control over users’ access behavior. On the contrary, in Hong et al.’s scheme, the operation to control users’ access behavior is completed by a series of fog nodes (i.e., the scheme adopts a distributed access control method). Recently, Ning et al. [25] proposed an attribute-based key encapsulation scheme that supports outsourced decryption. The scheme has the following characteristics: (1) Neither a user nor a server can complete the decryption operation independently, and a user can only make an outsourced decryption request to a server and recover the encapsulated key from the partial decryption result returned by the server; and (2) the user can only make a limited number of outsourced decryption requests. Ning et al. used this scheme to construct a fine-grained access control system with limited access times. Although the resulting system reduces the decryption computation of users in the resource access stage, the users cannot independently verify the outsourced computation results provided by the server. Although the noted schemes can basically meet the application requirements considered in this paper, the schemes still are not satisfactory in terms of user computing efficiency, user privacy protection level, user revocation, and attribute updates. The main contributions of this paper are summarized as follows: (1) Inspired by the ideas of Ning et al. [25], we extended Zhang et al.’s Ciphertext Policy ABE (CP-ABE) scheme [40] into a CP-ABE with verifiable outsourced decryption that allows users to independently verify the results of outsourced decryption and provide strict security proofs for the scheme. (2) We define a new fine-grained data access control system model with limited access times by using a simulation-based technique. (3) Based on the proposed CP-ABE with verifiable outsourced decryption and Au et al.’s k-times anonymous authentication [1], we construct a new fine-grained data access control system with limited access times. The new system supports user revocation and attribute updates and offers provable security under a newly defined security model. (4) Through prototype experiments, we make a detailed efficiency comparison between the new system and similar systems. In this section, we review previous schemes relevant to our system. According to their technical characteristics, these schemes can be divided into fine-grained cloud storage access control systems, k-times anonymous authentication schemes, and privacy-preserving attribute-based credential (ABC) systems. In [20], Li et al. constructed a multi-authority access control scheme that offers robustness and verifiability. In this scheme, multiple authorities can jointly maintain the attribute universe, and no single authority can completely control a particular attribute. In [35], Yang et al. proposed a privacy-preserving access control system in which all attributes are hidden in the access policy. In [34], Xue et al. proposed a system that allows access control for both data owners and the cloud. The system allowed servers to authenticate users when they request downloads, thus preventing resource consumption attacks by malicious users. In [2], Belguith et al. proposed an attribute-based fine-grained access control framework, thus realizing two layers of access control: one for fine-grained access control and one for anonymous data access. In [19], Li et al. proposed an access control scheme against user collusion. This scheme uses the idea of the attribute group to realize user attribute revocation and outsource the operation of attribute revocation to a third party. In [33], Wang et al. proposed a distributed access control scheme that supports outsourced decryption. The pseudonym technique can be used to realize user anonymity, but a pseudonym used for a long time cannot ensure user privacy. In [8], Deng et al. proposed an attribute-based proxy re-encryption scheme, enabling a user with decryption ability to share ABE-encrypted data with another user of an identity-based encryption scheme. In [39], Zhang et al. proposed a multi-authority patient health record-sharing system that supports fine-grained access control. This system requires users and cloud servers to perform lightweight anonymous authentication, thus ensuring the integrity of outsourced data and protecting user privacy simultaneously. In [28], Qin et al. proposed a lightweight access control system for the Internet of Things (IoT). In the access stage, users can obtain a pre-decryption service provided by the blockchain, thus reducing their computation cost in performing attribute-based decryption. In [10], Fan et al. proposed an access control system constructed using ABE and a trusted execution environment technique. This system provides enhanced security protection for outsourced data both on the client side and the cloud side. Recently, Qi et al. [27] proposed a fine-grained industrial data access control system that provides users with a hybrid cloud service architecture, enabling users to use a public cloud to store IoT data while outsourcing heavy computing tasks (i.e., attribute-based encryption) to a private cloud. In a k-times anonymous authentication (k-TAA) scheme, registered users can authenticate with the SP anonymously, but the number of authentication times cannot exceed the allowed limit. Au et al. [1] proposed a dynamic k-TAA scheme that allows the SP to grant or revoke the access rights of users autonomously. In [21], Lian et al. proposed a periodic k-TAA scheme wherein users can only anonymously show their credentials at most k-times in each time period. In [32], Tian et al. proposed a k-time secret handshake protocol that allows two authorized users to generate a shared key using their respective credentials. If users show their credentials more than k-times, anyone can reveal their identity. In [31], Tian et al. constructed a k-times remote user authentication scheme that is suitable for deployment on mobile platforms. In [22], Lian et al. proposed an anti-clone technique that can be applied to k-TAA schemes. Their solution does not need to make any physical security assumptions and eliminates the limitations of existing anti-clone schemes on user login times and epoch length. Recently, Huang et al. [16] proposed a k-TAA system suitable for the pay-as-you-go business model. This system allows users to access a service multiple times in a linkable and anonymous way in one authentication. In an ABC system, user identity can be seen as a collection of attributes. Users can generate valid displayable tokens only if they have obtained the corresponding attribute credentials and their access has not been revoked. The token does not reveal much other than the attributes it contains [3]. In [6], Camenisch et al. proposed a user revocation method applicable to ABC systems. In [29], Ringers et al. proposed an ABC system that is provably secure and efficient enough to be deployed on smart cards. In [26], Pussewalage et al. proposed an attribute-based credential scheme for a health information–sharing environment. Assuming that a doctor has a credential for an attribute set ω, for the purpose of collaborative diagnosis and treatment, they can generate a new credential for the subset ω<sub>1</sub> of ω as a delegator. In [5], Camenisch et al. proposed the most efficient ABC system so far, but it requires that the credential issuer and the verifier be the same entity. Furthermore, Han et al. [14] designed a privacy-preserving electronic ticket system based on attribute credentials. In [17], Krenn et al. proposed the first cloud-based ABC system that allows users to outsource the task of showing their credentials to a specific cloud service called a wallet. To protect user privacy, the wallet can only access the encrypted form of user attributes. Later, Habock et al. [13] pointed out that the security model in [17] has defects, and they also corrected the defective model. Recently, Tan et al. [30] proposed an efficient ABC system that has the advantage of supporting show proofs for AND, OR, and the threshold statements. In addition, it offers a higher level of security properties than existing systems. In summary, the research content of this paper falls in the category of fine-grained cloud storage access control but limits users’ access times as an explicit design goal to better conform to the pay-per-use business model in cloud computing. Although k-TAA schemes can limit users’ access times, they do not meet the needs of access control under the cloud storage environment. Although ABC systems also consider user attributes, the goal of such systems is to allow users to achieve privacy-preserving authentication by selectively exposing a small number of attributes; therefore, they again do not meet the fine-grained access control requirements considered in this paper. The rest of this paper is organized as follows. In section 2, we introduce the preliminary knowledge. In section 3, we propose a new CP-ABE scheme with verifiable outsourced decryption. In section 4, we propose a new fine-grained access control system model with limited access times and construct a concrete scheme. In section 5, we provide a performance analysis of the new system. Finally, we conclude the paper in section 6. Section snippets Bilinear groups Let B G ( 1 κ ) be a bilinear group parameter generator algorithm that takes the security parameter 1 κ as input and outputs ( G 0 , G T , p , e ̂ ) . We call ( G 0 , G T ) a bilinear group pair, where G 0 and G T are p -order cyclic multiplicative groups, and p is a prime. Moreover, e ̂ : G 0 × G 0 → G T represents a bilinear pairing that satisfies the following three properties. (1) Bilinearity: for any elements g 1 , g 2 of G 0 and any members a , b in Z p , the equation e ̂ ( g 1 a , g 2 b ) = e ̂ ( g 1 , g 2 ) ab is true. (2) Non-degeneracy: when neither g A new CP-ABE scheme with verifiable outsourced decryption In this section, we propose a CP-ABE scheme with verifiable outsourced decryption (denoted by Σ VOCP - ABE ). We assume the scheme involves four types of parties: the key generation authority ( KA ), service provider ( SP ), user ( U ), and data owner ( DO ). A new fine-grained data access control system with a bounded service number In this section, we introduce several notations. U L represents a secret list maintained by K A for storing users’ registration information. T L represents a public list maintained by S P for storing transcripts of the access protocol executed by users. R L represents a public list maintained by S P for storing the pseudonyms of revoked users. I D SP represents SP ’s identity. I D U represents U ’s identity. N y m U represents U ’s pseudonym. S P C L represents a list of credentials for S P . U C L represents a list Performance analysis In this section, we compare our system in section 4 with existing similar systems [36], [37], [15], [25]. In Table 1, we compare the security properties of these systems. We divide the existing attribute-based access control systems into two categories. The systems of type I are constructed by using an attribute-based signature (ABS) mechanism [37], [15], which is suitable for implementing access control in applications where the service object is non-data online resources. The systems of type Conclusion Focusing on problems of access control, user computing efficiency, and privacy protection in the cloud storage service environment, a new fine-grained access control system is proposed in this paper. Our system supports limiting users’ access times and is provably secure under a newly defined security model based on simulation. The core module of our system is a new CP-ABE scheme that supports verifiable outsourced decryption. In addition, the technique of k-TAA is adopted in the construction CRediT authorship contribution statement Xin Liu: Conceptualization, Methodology, Formal analysis, Writing – original draft, Writing – review & editing, Supervision, Project administration. Hao Wang: Conceptualization, Methodology, Formal analysis, Writing – review & editing, Supervision. Bo Zhang: Software, Validation, Investigation, Resources, Data curation. Bin Zhang: Software, Validation, Investigation, Resources, Data curation. Declaration of competing interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This work was supported by the National Natural Science Foundation of China (NO.62071280), the Major Scientific and Technological Innovation Project of Shandong Province (NO.2020CXGC010115), the Project of Shandong Province Higher Educational Science and Technology Program (NO. J17KA081), and the Scientific Research Project of Shandong Youth University of Political Science (NO. XJPY2021). We thank LetPub (www.letpub.com) for its linguistic assistance during the preparation of this manuscript. References (40) X. Qin et al. LBAC: A lightweight blockchain-based access control scheme for the internet of things Inf. Sci. (2021) B. Lian et al. A practical solution to clone problem in anonymous information system Inf. Sci. (2020) Y. Fan et al. One enhanced secure access scheme for outsourced data Inf. Sci. (2021) H. Deng et al. Flexible attribute-based proxy re-encryption for efficient data sharing Inf. Sci. (2020) S. Belguith et al. Accountable privacy preserving attribute based framework for authenticated encrypted access in clouds J. Parallel Distrib. Comput. (2020) M.H. Au et al. Constant-size dynamic k-times anonymous authentication IEEE Syst. J. (2013) J. Camenisch. Concepts around privacy-preserving attribute-based credentials. Privacy and Identity 2014, IFIP AICT 421,... J. Camenisch, R. Chaabouni, A. Shelat. Efficient protocols for set membership and range proofs. In: Proceedings of... J. Camenisch, M. Drijvers, P. Dzurenda, et al. Fast keyed-verification anonymous credentials on standard smart cards.... J. Camenisch, M. Drijvers, J. Hajny. Scalable revocation scheme for anonymous credentials based on n-times unlinkable... S. Canard, I. Coisel, A. Jambert, J. Traoré. New results for the practical use of range proofs. In: Proceedings of... Y. Dodis, A. Yampolskiy. A verifiable random function with short proofs and keys. In: Proceedings of PKC 2005,... M. Green, S. Hohenberger, B. Waters. Outsourcing the decryption of ABE ciphertexts. In: Proceedings of USENIX SEC 11,... U. Haböck, S. Krenn. Breaking and fixing anonymous credentials for the cloud. In: Proceedings of CANS 2019, Springer,... J. Han, L. Chen, S. Schneider, et al. Privacy-preserving electronic ticket scheme with attribute-based credentials.... J. Hong et al. Service outsourcing in F2C architecture with attribute-based anonymous access control and bounded service number IEEE Trans. Dependable Secure Comput. (2020) J. Huang W. Susilo F. Guo G.e. Wu Z. Zhao Q. Huang An anonymous authentication system for pay-as-you-go cloud computing... S. Krenn, T. Lorünser, A. Salzer, et al. Towards attribute-based credentials in the cloud. In: Proceedings of CANS... J. Lai et al. Attribute-based encryption with verifiable outsourced decryption IEEE Trans. Inf. Forensics Secur. (2013) View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.10.038", "PubYear": 2022, "Volume": "584", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Shandong Youth University of Political Science, China;Key Laboratory of Information Security and Intelligent Control in Universities of Shandong (Shandong Youth University of Political Science), China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Normal University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering, University of Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Shandong Youth University of Political Science, China;Key Laboratory of Information Security and Intelligent Control in Universities of Shandong (Shandong Youth University of Political Science), China"}], "References": [{"Title": "Cryptographic Solutions for Cloud Storage: Challenges and Research Opportunities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "567", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "VOD-ADAC: Anonymous Distributed Fine-Grained Access Control Protocol with Verifiable Outsourced Decryption in Public Cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "3", "Page": "572", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Accountable privacy preserving attribute based framework for authenticated encrypted access in clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "1", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Flexible attribute-based proxy re-encryption for efficient data sharing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "511", "Issue": "", "Page": "94", "JournalTitle": "Information Sciences"}, {"Title": "A practical solution to clone problem in anonymous information system", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "516", "Issue": "", "Page": "158", "JournalTitle": "Information Sciences"}, {"Title": "One enhanced secure access scheme for outsourced data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "561", "Issue": "", "Page": "230", "JournalTitle": "Information Sciences"}, {"Title": "LBAC: A lightweight blockchain-based access control scheme for the internet of things", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "554", "Issue": "", "Page": "222", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 91001964, "Title": "Fault-tolerant control for nonlinear switched systems with unknown control coefficients and full-state constraints", "Abstract": "This paper investigates the adaptive fault-tolerant control problem for a class of uncertain nonlinear switched systems with unknown control coefficients , full-state constraints and actuator failures . To surmount the design difficulty from full-state constraints, we construct the switched barrier L<PERSON><PERSON><PERSON> function mechanism. At the same time, the lower bound of unknown control coefficients is introduced to solve the problem of unknown control coefficients. Meanwhile, the trouble caused by actuator failures is compensated by switched controller and adaptive law. Furthermore, the tracking error asymptotically converges to zero is guaranteed and all signals in the closed-loop system are bounded. In addition, the full-state constraints are not violated. Finally, the effectiveness of the proposed approach is verified by simulation examples. Introduction In the past decade, switched systems have been widely used in many practical systems, such as aerospace, control systems and power systems. Due to the interaction of discrete dynamics and continuous dynamics, switched systems are unstable and their behaviors are more complex, so it is difficult to analyze the stability of switched systems. Common Lyapunov function method [1], multiple Lyapunov function strategy [2], average dwell time approach [3] and so on are used to study the stability of switched systems. In [4], the event trigger mechanism and switched change mechanism were combined flexibly to realize the global stability of the system. In order to relax the existing restrictions on the subsystems of switched systems, the authors in [5] constructed new state dependent switching laws and adaptive fuzzy control signals. In [6], a moving switching sequence approach was proposed, which can greatly reduce computational cost, and it was effective to apply to the compass-like biped walking robot. In the working process, many practical control systems are limited by some constraints such as speed, acceleration and so on. Therefore, it has certain theoretical and application value to study the control problems of state constraints systems [7], [8], [9]. In [10], a novel finite-time command filtered backstepping approach has been used to discuss stabilization of state constraints nonlinear systems. The proposed strategy in [11] was implemented by barrier Lyapunov function to ensure that the system state was always limited to some preselected compact sets. For nonlinear systems with time-varying state constraints [12], a design method of fuzzy adaptive event-triggered control was proposed to reduce the communication burden from controller to actuator. Otherwise, the quartic barrier Lyapunov functions was constructed in [13] to limit the system state to ensure the stability of the system with random disturbances. However, the control coefficients of aforementioned works are limited to known constants or nonlinear functions. With the development of modern science and technology, the unknown control coefficients are widely used in the models of the actual physical systems, such as the ship autopilotl system [14], the robot system [15], and single-link robot arm system [16], which add additional parameters and function uncertainties to the system. This leads us to explore the system with unknown control coefficients. In [17], according to using the convex combination method, the difficulty caused by unknown nonlinearities was overcome successfully. The lower bounds of the control coefficients were introduced into the BLF in [18], so that the controller has enough ability to compensate the unknown virtual control coefficients and parametric uncertainties. In [19], considering the nonlinear systems which virtual control coefficients include the known and unknown terms, by utilizing an auxiliary variable and the Nussbaum gain technique, an improved real control signal was constructed and applied to the attitude control of a quadrotor. In addition, It is well know that fault-tolerant control (FTC) has great responsibility in many practical systems, it effectively compensates the system losses caused by faults. The fault and state estimation observer was modeled as a distributed delay model in [20], and an impulse fault-tolerant controller was designed to compensate the influence of actuator fault and estimate the fault online, which improved the reliability and safety of the actuator fault model. In [21], an observer based fuzzy fault-tolerant controller was proposed for a class of nonlinear systems with actuator fault. Stability problem was discussed in [22] for a class of nonlinear switched systems with actuator fault by the command filter approach. Previously, for uncertain switched nonaffine nonlinear systems with actuator failures and time-delays, a novel nonlinear fault compensation function with adjustable parameter factor was constructed in [23] by using the mean value theorem. However, to the best of our knowledge, so far, the FTC problem of nonlinear switched systems with unknown control coefficients, full-state constraints and actuator failures has not been fully considered. Compared with the deterministic systems, it is obviously more difficult to design the switched fault-tolerant controller with unknown control coefficient and full-state constraints. Inspired by the above-mentioned works, this paper is devoted to the adaptive FTC problem for a class of uncertain switched systems with actuator failures, where the system is full-state constrained and contains unknown control coefficients. At the same time, the lower bound of the control coefficients is introduced into the BLF. Compared with the existing work, the main contributions of this paper can be summarized as follows. 1) For the nonlinear system described in [18] with unknown control coefficients and full-state constraints, the switched systems and switching rules are given by using the unmodeled state, and the influence of full-state constraints is eliminated by switched BLF for each subsystem. The designed switching rules ensure the expected performance of the system. 2) Different from [24], in this paper, each control input corresponds to an unknown control coefficients, this requirement makes the controller design more challenging. Fortunately, in step n , by designing the actual control and adaptive control law, the difficulty of unknown control coefficients on system performance is solved. 3) Unknown control coefficients [25] increases the uncertainty and complexity of the switched system. Therefore, actuator failures might be a potential danger. Based on this, this paper designs a switched fault-tolerant adaptive mechanism to compensate the fault. 4) The main difficulty of adaptive FTC for switched systems [22], [23] is related to the design of virtual control. Through this design method, the actuator failures is effectively solved. By means of barrier Lyapunov function analysis, it is proved that the tracking error can asymptotically converge to zero and all closed-loop signals are bounded. Therefore, the scheme in this paper can be applied more widely. The organization of the rest paper is as follows. Some preliminaries knowledge and formulates problems are described in Sect. 2. Sect. 3 is the systematic design procedure based on the BLF. Stability analysis is given in Sect. 4. the simulation studies are showed in Sect. 5 to verify the proposed strategy. Finally, Sect. 6 concludes the paper. Section snippets System description Consider a nonlinear switched systems with actuator failures in the following form: d ̇ = ϕ 0 , σ ( d , x 1 ) x ̇ i = g i , σ ( x ¯ i ) x i + 1 + μ σ T ϕ i , σ ( x ¯ i ) , 1 ⩽ i ⩽ n - 1 x ̇ n = ∑ c = 1 s g n c , σ ( x ¯ n ) u c , σ ( t ) + μ σ T ϕ n , σ ( x ¯ n ) y = x 1 where d ∈ R p , x ¯ i = [ x 1 , x 2 , … , x i ] T ∈ R i , i = 1 , 2 , … , n are the state vector of the system. In the system, u c , σ ∈ R s , c = 1 , 2 , ⋯ , s indicates the input of the system, and y ∈ R represents the system output, μ σ ∈ R l denotes the unknown system parameter, g i , σ ∈ R are unknown control gain functions. Specially, function σ is represented as: R + → M = 1 , 2 , … , Adaptive switched fault-tolerant controller design with BLF Following the backstepping approach, introduce the following coordinate transformation: z 1 = x 1 - y d z i = x i - α i - 1 , j , i = 2 , ⋯ , n With respect to the unknown parameters, define θ j = max ‖ ϑ i , j ‖ , i = 1 , ⋯ , n where θ ̃ j = θ j - θ ̂ j is the corresponding estimation error of θ j , and θ ̂ j is the estimate of the parameter θ j . In order to go further, the following proposition is set up. Proposition 1 ( [ 18 ] ). For any position integrable time-varying functions δ i , j ( t ) , there exist known nonlinear function η i , j = k z i , j S i , j T S i , j k z i , j S i , j T s i , j Stability analysis Theorem 1 Consider the switched nonlinear system ( 1 ) with unknown control coefficients and actuator failures, under Assumptions 1–3. If the initial conditions content z i ( 0 ) ∈ Ω z i = z i :   z i   < k b i , j . Then, the switched fault-tolerant controllers with the virtual control laws ( 34 ) , tuning function ( 35 ) , control v, adaptive law ( 36 ) and a state-dependent switching signal σ ( t ) = arg min j ∈ M { U j ( d ) } , can guarantee that the locally uniformly boundedness of all the closed-loop signals, and the tracking error of all Simulation examples In this section, the effectiveness of the proposed adaptive switched control method will be verified by the following examples. Conclusion A adaptive fault tolerant control is proposed for a class of uncertain nonlinear switched systems, which possess unknown control coefficients, full-state constraints and actuator failures. The impact of full-state constraints and unknown control coefficients is counteracted by proper barrier Lyapunov functions and the lower bound of unknown control coefficients. Based on adaptive backstepping technique and tuning function method, the fault-tolerant controllers with switching laws are developed, CRediT authorship contribution statement Yaxin Li: Validation, Writing - original draft, Writing - review & editing. Libing Wu: Conceptualization, Methodology, Visualization, Validation. Yuhan Hu: Supervision. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work is supported in part by the National Natural Science Foundation of China under (Grant Nos. 62022044 and 61773221), the Jiangsu Natural Science Foundation for Distinguished Young Scholars under (Grant No. BK20190039), and the Scientific Research Foundation of Liaoning Educational Committee of China under (Grant Nos. LJKZ0284, 2019LNJC13, 2019LNJC11 and 2020LNJC11). References (29) L.B. Wu et al. Robust adaptive fault-tolerant control for a class of uncertain nonlinear systems with multiple time delays J. Process Control (2016) H.F. Li et al. Integral-based event-triggered fault estimation and impulsive fault-tolerant control for networked control systems applied to underwater vehicles Neurocomputing (2021) Z. Liu et al. Adaptive neural quantized control for a class of switched nonlinear systems Inf. Sci. (2020) X. Jin et al. Fuzzy adaptive event-triggered control for a class of nonlinear systems with time-varying full state constraints Inf. Sci. (2021) K. Wang et al. Command filtered finite-time control for nonlinear systems with state constraints and its application to TCP network Inf. Sci. (2021) Q. Jiang et al. Full state constraints and command filtering-based adaptive fuzzy control for permanent magnet synchronous motor stochastic systems Inf. Sci. (2021) H.T. Wang et al. Exponential stability criterion of the switched neural networks with time-varying delay Neurocomputing (2019) G. Zong et al. H ∞ synchronization of switched complex networks: a switching impulsive control method Commun. Nonlinear Sci. Numer. Simul. (2019) D. Cui et al. Finite-time adaptive fault-tolerant tracking control for nonlinear switched systems with dynamic uncertainties Int. J. Robust Nonlinear Control (2021) Y.X. Huang, Y.G. Liu, Switching event-triggered control for a class of uncertain nonlinear systems,... S. Li et al. Command-filter-based adaptive fuzzy finite-time control for switched nonlinear systems using state-dependent switching method IEEE Trans. Fuzzy Syst. (2021) S. Katayama et al. A moving switching sequence approach for nonlinear model predictive control of switched systems with state-dependent switches and state jumps Int. J. Robust Nonlinear Control (2020) Y.M. Li, J.X. Zhang, W. Liu, S.C. Tong, Observer-based adaptive optimized control for stochastic nonlinear systems with... T. Wang et al. Adaptive fuzzy tracking control for a class of strict-feedback nonlinear systems with time-varying input delay and full state constraints IEEE Trans. Fuzzy Syst. (2020) View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.10.033", "PubYear": 2022, "Volume": "582", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, University of Science and Technology Liaoning, Anshan, Liaoning 114051, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, University of Science and Technology Liaoning, Anshan, Liaoning 114051, PR China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, University of Science and Technology Liaoning, Anshan, Liaoning 114051, PR China"}], "References": [{"Title": "Self-constraining and attention-based hashing network for bit-scalable cross-modal retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "400", "Issue": "", "Page": "255", "JournalTitle": "Neurocomputing"}, {"Title": "Finite-Time Adaptive Fault-Tolerant Control for a Class of Nonlinear Systems with Actuator Faults", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "5", "Page": "1075", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}, {"Title": "Adaptive neural quantized control for a class of switched nonlinear systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "537", "Issue": "", "Page": "313", "JournalTitle": "Information Sciences"}, {"Title": "Command filtered finite-time control for nonlinear systems with state constraints and its application to TCP network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "550", "Issue": "", "Page": "189", "JournalTitle": "Information Sciences"}, {"Title": "Fuzzy adaptive event-triggered control for a class of nonlinear systems with time-varying full state constraints", "Authors": "<PERSON><PERSON>; Yuan-<PERSON><PERSON>", "PubYear": 2021, "Volume": "563", "Issue": "", "Page": "111", "JournalTitle": "Information Sciences"}, {"Title": "Full state constraints and command filtering-based adaptive fuzzy control for permanent magnet synchronous motor stochastic systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "567", "Issue": "", "Page": "298", "JournalTitle": "Information Sciences"}, {"Title": "Integral-based event-triggered fault estimation and impulsive fault-tolerant control for networked control systems applied to underwater vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "442", "Issue": "", "Page": "36", "JournalTitle": "Neurocomputing"}, {"Title": "$${L_\\infty }$$ Fault Estimation and Fault-Tolerant Control for Nonlinear Systems by T–S Fuzzy Model Method with Local Nonlinear Models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "6", "Page": "1714", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 91001982, "Title": "Truth tables for modal logics T and S4, by using three-valued non-deterministic level semantics", "Abstract": "Novel three-valued non-deterministic level semantics for modal logics $\\textbf {T}$ and $\\textbf {S4}$ are presented. A criterion for partial level valuations is given, making it possible to create truth tables. Additionally, semantics and truth tables for $\\textbf {0}$ (defined as $\\textbf {PC}$ plus rule of necessitation) and $\\textbf {0T}$ with only two values are based on <PERSON><PERSON><PERSON>’s work. We need <PERSON><PERSON><PERSON>’ notion of level valuations: a generalization of <PERSON><PERSON><PERSON><PERSON>’s theorem shows that there is no non-deterministic semantics for modal logics up to $\\textbf {S5}$, containing the rule of necessitation.", "Keywords": "", "DOI": "10.1093/logcom/exab068", "PubYear": 2022, "Volume": "32", "Issue": "1", "JournalId": 2581, "JournalTitle": "Journal of Logic and Computation", "ISSN": "0955-792X", "EISSN": "1465-363X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Software Engineering Group, Department of Computer Science, Technische Universität Darmstadt, Darmstadt, Germany"}], "References": [{"Title": "Modular non-deterministic semantics for T, TB, S4, S5 and more", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "1", "Page": "158", "JournalTitle": "Journal of Logic and Computation"}]}, {"ArticleId": 91002012, "Title": "Reliable and Efficient Long-Term Social Media Monitoring", "Abstract": "Social media data is now widely used by many academic researchers. However, long-term social media data collection projects, which most typically involve collecting data from public-use APIs, often encounter issues when relying on local area network servers (LANs) to collect high-volume streaming social media data over long periods of time. In this paper, we present a cloud-based data collection, pre-processing, and archiving infrastructure, and argue that this system mitigates or resolves the problems most typically encountered when running social media data collection projects on LANs at minimal cloud-computing costs. We show how this approach works in different cloud computing architectures, and how to adapt the method to collect streaming data from other social media platforms. The contribution of our research lies in the development of methodologies that researchers can use to monitor and analyze phenomena including how public opinion and public discourse change in response to events, monitoring the evolution and change of misinformation campaigns, and studying how organizations and entities change how they present and frame information online.", "Keywords": "Social Media;Cloud Computing;Twitter;Time Series", "DOI": "10.4236/jcc.2021.910006", "PubYear": 2021, "Volume": "9", "Issue": "10", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of the Humanities and Social Sciences, California Institute of Technology, Pasadena, California, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Accenture, San Jose, California, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of the Humanities and Social Sciences, California Institute of Technology, Pasadena, California, USA"}], "References": []}, {"ArticleId": 91002150, "Title": "Satellite remote sensing of active fires: History and current status, applications and future requirements", "Abstract": "Landscape fire is a widespread, somewhat unpredictable phenomena that plays an important part in Earth's biogeochemical cycling. In many biomes worldwide fire also provides multiple ecological benefits, but in certain circumstances can also pose a risk to life and infrastructure, lead to net increases in atmospheric greenhouse gas concentrations, and to degradation in air quality and consequently human health. Accurate, timely and frequently updated information on landscape fire activity is essential to improve our understanding of the drivers and impacts of this form of biomass burning, as well as to aid fire management. This information can only be provided using satellite Earth Observation approaches, and remote sensing of active fire is one of the key techniques used. This form of Earth Observation is based on detecting the signature of the (mostly infrared) electromagnetic radiation emitted as biomass burns. Since the early 1980's, active fire (AF) remote sensing conducted using Earth orbiting (LEO) satellites has been deployed in certain regions of the world to map the location and timing of landscape fire occurrence, and from the early 2000's global-scale information updated multiple times per day has been easily available to all. Geostationary (GEO) satellites provide even higher frequency AF information, more than 100 times per day in some cases, and both LEO- and GEO-derived AF products now often include estimates of a fires characteristics, such as its fire radiative power (FRP) output, in addition to the fires detection. AF data provide information relevant to fire activity ongoing when the EO data were collected, and this can be delivered with very low latency times to support applications such as air quality forecasting. Here we summarize the history of achievements in the field of active fire remote sensing, review the physical basis of the approaches used, the nature of the AF detection and characterization techniques deployed, and highlight some of the key current capabilities and applications. Finally, we list some important developments we believe deserve focus in future years.", "Keywords": "Active fire ; Infrared ; Satellites ; FRP ; Review", "DOI": "10.1016/j.rse.2021.112694", "PubYear": 2021, "Volume": "267", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Leverhulme Center for Wildfire, Environment & Society, c/o Dept. of Geography, Kings College London, Aldwych, London WC2B 4BG, UK;NERC National Centre for Earth Observation (NCEO), c/o Dept. of Geography, Kings College London, Aldwych, London WC2B 4BG, UK;Corresponding author at: Leverhulme Center for Wildfire, Environment & Society, c/o Dept. of Geography, Kings College London, Aldwych, London WC2B 4BG, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Southampton, Geography and Environmental Science, Southampton, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Geographical Sciences, University of Maryland, College Park, MD, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Forest, Rangeland, and Fire Sciences, University of Idaho, Moscow, ID, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Interdisciplinary Studies, Atmospheric and Environmental Sciences, Howard University, Washington, District of Columbia, USA"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "NOAA/NESDIS/OSPO Satellite Analysis Branch, College Park, MD, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Trigg-Davies Consulting Ltd., Malvern, UK;GSFC, Science Systems and Applications Inc., Lanham, Maryland, United States"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Geographical Sciences, University of Maryland, College Park, MD, USA"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Centro de Previsão de Tempo e Estudos Climáticos/Instituto Nacional de Pesquisas Espaciais, Programa de Monitoramento de Queimada por Satélites, 12227-010, São José dos Campos, SP, Brazil"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "NOAA/NESDIS Center for Satellite Applications and Research (STAR), College Park, MD, USA"}, {"AuthorId": 14, "Name": "Tercia Strydom", "Affiliation": ""}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "iMMAP Middle East, Mecca St. 145, Amman, Jordan"}, {"AuthorId": 16, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NERC National Centre for Earth Observation (NCEO), c/o Dept. of Geography, Kings College London, Aldwych, London WC2B 4BG, UK"}, {"AuthorId": 17, "Name": "Weidong Xu", "Affiliation": "NERC National Centre for Earth Observation (NCEO), c/o Dept. of Geography, Kings College London, Aldwych, London WC2B 4BG, UK"}, {"AuthorId": 18, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 19, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 20, "Name": "<PERSON>", "Affiliation": "Science Systems and Applications, Inc. (SSAI), NASA Goddard Space Flight Center, Greenbelt, MD 20771, USA"}, {"AuthorId": 21, "Name": "<PERSON>", "Affiliation": "NASA Marshall Space Flight Center, Huntsville, Alabama 35811, USA"}, {"AuthorId": 22, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 23, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 24, "Name": "<PERSON>", "Affiliation": "Department of Geography and Geospatial Analysis Center, Miami University, Oxford, OH, USA"}, {"AuthorId": 25, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Upper ASEAN Wildland Fire Special Research Unit, Kasetsart University, Bkk, Thailand"}, {"AuthorId": 26, "Name": "<PERSON>", "Affiliation": "Cooperative Institute for Meteorological Satellite Studies, Space Science Engineering Center, University of Wisconsin-, Madison, USA"}, {"AuthorId": 27, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Diagnosing spatial biases and uncertainties in global fire emissions inventories: Indonesia as regional case study", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111557", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "First study of Sentinel-3 SLSTR active fire detection and FRP retrieval: Night-time algorithm enhancements and global intercomparison to MODIS and VIIRS AF products", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111947", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Advances in the estimation of high Spatio-temporal resolution pan-African top-down biomass burning emissions made using geostationary fire radiative power (FRP) and MAIAC aerosol optical depth (AOD) data", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111971", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "On the outstanding need for a long-term, multi-decadal, validated and quality assessed record of global burned area: Caution in the use of Advanced Very High Resolution Radiometer data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "", "Page": "100007", "JournalTitle": "Science of Remote Sensing"}, {"Title": "Estimating wildfire fuel consumption with multitemporal airborne laser scanning data and demonstrating linkage with MODIS-derived fire radiative energy", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "251", "Issue": "", "Page": "112114", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Improvements in high-temporal resolution active fire detection and FRP retrieval over the Americas using GOES-16 ABI with the geostationary Fire Thermal Anomaly (FTA) algorithm", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "", "Page": "100016", "JournalTitle": "Science of Remote Sensing"}, {"Title": "Sentinel-3 active fire detection and FRP product performance - Impact of scan angle and SLSTR middle infrared channel selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "261", "Issue": "", "Page": "112460", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 91002218, "Title": "A comprehensive E-commerce customer behavior analysis using convolutional methods", "Abstract": "E-commerce is one of the most widely preferred business models all around the world. People can order items and have them in days or maybe in hours without visiting the shops. They count on the reviews of the online items to purchase them, so reviews have become one of the most significant metrics. Modern day algorithms and models can be made to decide the reviews seen by the user to judge an item fairly. We have attempted to make models that can judge the sentiment of the user writing the reviews from the text of the review, classifying it into positive, negative or neutral. Edge Computation is an emerging field in this scenario that can be very beneficial alongside capsule networks for processing the data as data capsules. In this work, we have performed a comparison of the effect of convolutional layers, attention layers and capsule network layers on base models for GRU, LSTM, Bi-GRU, and Bi-LSTM. These are done with the purpose to support the functioning of Edge Computation. The final results show that Bi-LSTM-Capsule Model (BiCapsModel) gives highest accuracy of 94.8%. The final outcome is very promising and can be used in various domains in journalism, product review and stock market. For classification, an Amazon review dataset has been considered. The dataset has various types of classified reviews based on which the emotional text analysis can be performed efficiently covering each aspect.", "Keywords": "Sentiment analysis ; Capsule network ; Attention ; Recurrent neural networks ; Convolutional neural networks ; Long short-term memory ; Gated recurrent units", "DOI": "10.1016/j.compeleceng.2021.107541", "PubYear": 2021, "Volume": "96", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, <PERSON><PERSON><PERSON>'s College of Engineering, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Purdue University Fort Wayne, Fort Wayne, IN 46805, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, <PERSON><PERSON><PERSON>'s College of Engineering, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON> <PERSON>", "Affiliation": "Department of ECE, Karunya Institute of Technology and Sciences, Coimbatore, India;Corresponding author"}], "References": [{"Title": "Text classification using capsules", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "376", "Issue": "", "Page": "214", "JournalTitle": "Neurocomputing"}, {"Title": "Sentiment analysis of Twitter data during critical events through Bayesian networks classifiers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "92", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 91002233, "Title": "Trajectory-tracking control of Mecanum-wheeled omnidirectional mobile robots using adaptive integral terminal sliding mode", "Abstract": "Mecanum-wheeled omnidirectional mobile robot (MWOMR) is playing an increasingly significant role in modern transportation and industry due to its high flexibility and maneuverability. This paper presents an adaptive integral terminal sliding mode (AITSM) control algorithm for an MWOMR trajectory-tracking task. Initially, a four-inputs-three-outputs kinematic-and-dynamic model is identified to describe the MWOMR’s trajectory-tracking behavior. Subsequently, an AITSM controller is designed for the MWOMR, where the stability of control system is theoretically validated by means of <PERSON><PERSON><PERSON><PERSON>. For comparison, a conventional sliding mode (CSM) controller and a nonsingular terminal sliding mode (NTSM) controller are also designed as benchmarks. Finally, simulations are executed to test the performance of proposed control laws in various scenarios. The simulation results substantiate that our AITSM control strategy is an effective solution to the MWOMR trajectory-tracking problem, which exhibits remarkable superiority in terms of tracking precision and control robustness compared with the CSM and NTSM control.", "Keywords": "Omnidirectional mobile robot ; Trajectory tracking ; Mecanum wheel ; Adaptive integral terminal sliding mode", "DOI": "10.1016/j.compeleceng.2021.107500", "PubYear": 2021, "Volume": "96", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, China;Correspondence to: 288 Liuhe Road, Hangzhou, 310023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, China;@zjut.edu.cn"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Zhejiang University of Technology, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science, Engineering and Technology, Swinburne University of Technology, Australia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science, Engineering and Technology, Swinburne University of Technology, Australia"}], "References": [{"Title": "Extreme-learning-machine-based FNTSM control strategy for electronic throttle", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "14507", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Robust adaptive integral terminal sliding mode control for steer-by-wire systems based on extreme learning machine", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106756", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 91002278, "Title": "MoNET: an R package for multi-omic network analysis", "Abstract": "<p><b>MOTIVATION</b>:The increasing availability of multi-omic data has enabled the discovery of disease biomarkers in different scales. Understanding the functional interaction between multi-omic biomarkers is becoming increasingly important due to its great potential for providing insights of the underlying molecular mechanism.</p><p><b>RESULTS</b>:Leveraging multiple biological network databases, we integrated the relationship between SNPs, genes/proteins and metabolites, and developed an R package MoNET for multi-omic network analysis. This new tool enables users to not only track down the interaction of SNPs/genes with metabolome level, but also trace back for the potential risk variants/regulators given altered genes/metabolites. MoNET is expected to advance our understanding of the multi-omic findings by unveiling their trans-omic interactions and is likely to generate new hypotheses for further validation.</p><p><b>AVAILABILITY</b>:The MoNET package is freely available on https://github.com/JW-Yan/MONET.</p><p><b>SUPPLEMENTARY INFORMATION</b>:Supplementary data are available at Bioinformatics online.</p><p>© The Author(s) (2021). Published by Oxford University Press. All rights reserved. For Permissions, please email: <EMAIL>.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab722", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Intelligent Systems Science and Engineering, Harbin Engineering University, Harbin, China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Intelligent Systems Science and Engineering, Harbin Engineering University, Harbin, China."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Intelligent Systems Science and Engineering, Harbin Engineering University, Harbin, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of BioHealth Informatics, School of Informatics and Computing, Indiana University Purdue University Indianapolis, Indianapolis, USA."}], "References": []}, {"ArticleId": 91002330, "Title": "Abiogenesis-Biogenesis Transition in Evolutionary Cybernetic System", "Abstract": "Biogenesis and abiogenesis stages of natural evolution are the well-known terms to characterize a quite different phases of life development on the Earth. It is accustomed to think that an abiogenesis as the early stage of evolution is predominantly the chemistry phase dealing with interaction between less or more complex polymer chains when possible appearances of life if any assumes significant involvement of collective effects. A biogenesis as the later stage of evolution is the time for predominance of <PERSON>’s laws manifesting, in part, in competition among individual species in the form of variability-heredity game. In this report, we discuss possible nature of abiogenesis-biogenesis transition as a natural result of energy development in an evolutionary cybernetic system simulated by mathematical model of open thermodynamic system with unlimited number of energy links with its surroundings.", "Keywords": "Abiogenesis ; biogenesis ; evolutionary unit ; natural evolution", "DOI": "10.1080/01969722.2021.1991662", "PubYear": 2022, "Volume": "53", "Issue": "7", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Burnaby, BC, Canada"}], "References": []}, {"ArticleId": 91002358, "Title": "Person re-identification using adversarial haze attack and defense: A deep learning framework", "Abstract": "In this paper, the adversarial haze attack problem is addressed using the dark channel prior (DCP) de-hazing method. The adversarial attack affects rank-1 accuracy, where searching a target image against each test image is a specific search problem. To resolve this kind of problem, a feature fusion model is proposed to fuse handcrafted features and a pre-trained network model to obtain robust and discriminative features. The proposed model learns global features using transfer learning architecture whereas local features are obtained using the conventional method. Three pre-trained CNN models (AlexNet, ResNet, and Inception-v3) are used for feature extraction via transfer learning. The experiments are performed on publicly available datasets, achieving 68.6% accuracy in rank-1 with VIPER dataset and 79.6% accuracy with CHUK03 dataset. The proposed model enhances rank-1 accuracy of person re-identification when comparing with other state-of-the-art methods.", "Keywords": "Person re-identification ; Adversarial haze attack ; Dark channel prior (DCP) algorithm ; Feature fusion ; Deep learning ; Transfer learning ; Handcrafted model", "DOI": "10.1016/j.compeleceng.2021.107542", "PubYear": 2021, "Volume": "96", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, HITEC University Taxila, Taxila, Pakistan;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Applied Computing and Technology, Noroff University College, Kristiansand, Norway"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Wah Campus, Wah Cantt, Pakistan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sathyabama Institute of Science and Technology, Chennai, Tamil Nadu 600119, India"}], "References": [{"Title": "Attributes-aided part detection and refinement for person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "107016", "JournalTitle": "Pattern Recognition"}, {"Title": "Hand-crafted and deep convolutional neural network features fusion and selection strategy: An application to intelligent human action recognition", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105986", "JournalTitle": "Applied Soft Computing"}, {"Title": "Flow-guided feature enhancement network for video-based person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "383", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 91002383, "Title": "High-resolution marine heatwave mapping in Australasian waters using Himawari-8 SST and SSTAARS data", "Abstract": "Marine heatwaves (MHWs) have significant ecological and economic impact at local and regional scales. Consequently, there is a pressing need to map the fine-scale temporal and spatial patterns of MHWs, for both historical and near real-time events. Satellite remote sensing of Sea Surface Temperature (SST) provides fundamental data for the mapping of MHWs. This study used high-resolution Himawari-8 SST and the Sea Surface Temperature Atlas of the Australian Regional Seas (SSTAARS) data, which have a spatial resolution of ~2 km, to map recent and near real-time MHW events in waters around Australasia. The high-resolution MHWs mapping identified two broad areas of MHW hotspots that occurred between August 2015 and February 2019. The Tropical Warm Pool region (including the Great Barrier Reef and part of the Coral Sea) was affected by strong and prolonged MHW conditions for the greater part of 2016. The unusually strong 2015–16 El Niño event was believed to be the primary underlying driver for the MHWs, and the air-sea heat flux was likely the key local process controlling the heat budget. The south-east of the study area (including Australia's south-east coast, the Tasman Sea and New Zealand's east coast) suffered severe MHWs in 2015–16, 2017–18 and 2018–19. The 2015–16 summer Bass Strait and East Tasmania MHW event was likely due to the anomalous heat transported by the East Australia Current. The air-sea heat flux, however, was likely an important local driver for the MHW events in the Tasman Sea during the 2016 autumn, the 2017–18 summer and the 2018–19 summer. The anomalous heat flux into ocean was attributed to potential underlying climate drivers including ENSO teleconnections, positive SAM and their interaction. This mapping has not only enhanced our understanding of the spatio-temporal characteristics of several previously documented MHWs, but also identified and mapped several previously undocumented MHWs. Two case studies in the Beagle Marine Park located in north-eastern Bass Strait and the Elizabeth and Middleton Reefs situated ~550 km east of the Australian coast within the Lord Howe Marine Park proved the value of Himawari-8 SST and SSTAARS data in mapping fine details of MHWs in a small area, which could not be captured by the broad-scale DOISST data. During the 2017–18 summer event, the case studies revealed much stronger MHW influence in the shallow waters east of the Beagle Marine Park where most of the important rocky reef habitats exist. During the same event, the case studies also confirmed more severe MHW impact on the Elizabeth Reef than the Middleton Reef which resulted in bleaching to selective coral species on the Elizabeth Reef. The near-real time MHWs mapping showed that both the GBR and the Coral Sea marine parks were experiencing MHW conditions in early March 2020, with most affected areas having strong MHW class.", "Keywords": "MHWs ; Marine Heatwaves ; Himawari-8 ; SST ; SSTAARS ; Australia", "DOI": "10.1016/j.rse.2021.112742", "PubYear": 2021, "Volume": "267", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Earth and Marine Observations Branch, Geoscience Australia, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CSIRO Oceans and Atmosphere, Crawley, Western Australia, Australia;Centre for Southern Hemisphere Oceans Research, Hobart, Tasmania, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bureau of Meteorology, Melbourne, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CSIRO Oceans and Atmosphere, Hobart, Tasmania, Australia;Woods Hole Oceanography Institution, Woods Hole, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "CSIRO Oceans and Atmosphere, Hobart, Tasmania, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Bureau of Meteorology, Melbourne, Australia"}], "References": []}, {"ArticleId": 91002385, "Title": "Adaptive sliding-mode control of a class of disturbed cyber–physical systems against actuator attacks", "Abstract": "In this paper, an adaptive integral sliding-mode control scheme is presented to cope with the false data injection actuator attacks on a class of disturbed cyber–physical systems. Considering the time-varying state-dependent actuator attacks can be parameterized in the form of a neural network, a filter operator is firstly introduced to identify the unknown weight vectors. Then, an integral sliding-mode function is utilized to develop the attack tolerant controller, which can compensate for the impacts of actuator attacks and external disturbance on system performance. By using the Lyapunov stability theory, the uniformly ultimately bounded result of system states, NN weight estimation errors and sliding motion can be obtained. Finally, the effectiveness of the proposed control theory is verified via a decoupled model of an F-18 aircraft.", "Keywords": "Cyber–physical systems ; Actuators attacks ; Integral sliding-mode control ; Uniform ultimate boundedness", "DOI": "10.1016/j.compeleceng.2021.107492", "PubYear": 2021, "Volume": "96", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "S<PERSON>yu Lü", "Affiliation": "School of Electrical Engineering and Automation, Hefei University of Technology, Hefei, Anhui 230009, PR China;@mail.hfut.edu.cn"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Jin", "Affiliation": "School of Computer Science and Technology, Qilu University of Technology (Shandong Academy of Sciences), Jinan, Shandong, 250353, PR China;Shandong Computer Science Center (National Supercomputer Center in Jinan), Jinan, Shandong, 250014, PR China;Shandong Provincial Key Laboratory of Computer Networks, Jinan, Shandong, 250014, PR China;Corresponding author at: School of Computer Science and Technology, Qilu University of Technology (Shandong Academy of Sciences), Jinan, Shandong, 250353, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Hefei University of Technology, Hefei, Anhui 230009, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Hefei University of Technology, Hefei, Anhui 230009, PR China"}], "References": [{"Title": "Modified adaptive second order sliding mode control: Perturbed system response robustness", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "81", "Issue": "", "Page": "106536", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Robust adaptive integral terminal sliding mode control for steer-by-wire systems based on extreme learning machine", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106756", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Distributed adaptive security consensus control for a class of multi-agent systems under network decay and intermittent attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "88", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 91002390, "Title": "Transfer-learning-based approach for leaf chlorophyll content estimation of winter wheat from hyperspectral data", "Abstract": "Leaf chlorophyll, as a key factor for carbon circulation in the ecosystem, is significant for the photosynthetic productivity estimation and crop growth monitoring in agricultural management. Hyperspectral remote sensing (RS) provides feasible solutions for obtaining crop leaf chlorophyll content (LCC) by the advantages of its repeated and high throughput observations. However, the data redundancy and the poor robustness of the inversion models are still major obstacles that prevent the widespread application of hyperspectral RS for crop LCC evaluation. For winter wheat LCC inversion from hyperspectral observations, this study described a novel hybrid method, which is based on the combination of amplitude- and shape- enhanced 2D correlation spectrum (2DCOS) and transfer learning. The innovative feature selection method, amplitude- and shape- enhanced 2DCOS, which originated from 2DCOS, additionally considered the relationships between external perturbations and hyperspectral amplitude and shape characteristics to enhance the dynamic spectrum response. To extract the representative LCC featured wavelengths, the amplitude- and shape- enhanced 2DCOS was conducted on the leaf optical PROperties SPECTra (PROSPECT) + Scattering from Arbitrarily Inclined Leaves (SAIL) (PROSAIL) simulated dataset, which covered most possible winter wheat canopy spectra. Nine wavelengths (i.e., 455, 545, 571, 615, 641, 662, 706, 728, and 756 nm) were then extracted as the sensitive wavelengths of LCC with the amplitude- and shape- enhanced 2DCOS. These wavelengths had specificity to LCC and showed good correlation with LCC from the aspect of photosynthesis mechanism, molecular structure, and optical properties. The transfer learning techniques based on the deep neural network was then introduced to transfer the knowledge learned from the PROSAIL simulated dataset to the inversion tasks of field measured LCC. Parts of the labeled samples in field observations were used to finetune the model pre-trained by the simulated dataset to improve the inversion accuracy of the winter wheat LCC in different field scenes, aiming to reduce the need for the field measured and labeled sample size. To further ascertain the universality, transferability and predictive ability of the proposed hybrid method, field samples collected from different locations at different phenological phases, including the jointing and heading stages in 2013, 2014, and 2018, were utilized as target tasks to validate the proposed hybrid method. Moreover, the LCC of winter wheat estimated with the proposed method was evaluated with the ground-based platform and the UAV-based platform to verify the model versatility for different monitoring platforms. Various validations demonstrated that the hybrid inversion method combining the amplitude- and shape- enhanced 2DCOS and the fine-tuned transfer learning model could effectively estimate winter wheat LCC with good accuracy and robustness, and can be extended to the detection and inversion of other key variables of crops.", "Keywords": "Hyperspectral ; Chlorophyll ; PROSAIL ; Transfer learning ; UAV experiment ; Ground-based measurement", "DOI": "10.1016/j.rse.2021.112724", "PubYear": 2021, "Volume": "267", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Modern Precision Agriculture System Integration Research, Ministry of Education, China Agricultural University, Beijing 100083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CETC Big Data Research Institute Chengdu Branch Co., Ltd., Chengdu 610000, China;National Engineering Laboratory for Big Data Application on Improving Government Governance Capabilities, Guiyang 550000, China"}, {"AuthorId": 3, "Name": "Qiming Qin", "Affiliation": "Institute of Remote Sensing and Geographic Information System, School of Earth and Space Sciences, Peking University, Beijing 100871, China"}, {"AuthorId": 4, "Name": "Yuanheng Sun", "Affiliation": "Institute of Remote Sensing and Geographic Information System, School of Earth and Space Sciences, Peking University, Beijing 100871, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Remote Sensing and Geographic Information System, School of Earth and Space Sciences, Peking University, Beijing 100871, China"}, {"AuthorId": 6, "Name": "Hong Sun", "Affiliation": "Key Laboratory of Modern Precision Agriculture System Integration Research, Ministry of Education, China Agricultural University, Beijing 100083, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Modern Precision Agriculture System Integration Research, Ministry of Education, China Agricultural University, Beijing 100083, China;Corresponding author"}], "References": []}, {"ArticleId": 91002410, "Title": "A hybrid modelling approach for characterizing hole shrinkage mechanisms in drilling Ti6Al4V under dry and cryogenic conditions", "Abstract": "<p>Hole shrinkage is a common phenomenon in drilling difficult-to-cut materials like Ti6Al4V due to their poor thermal properties and high elasticity, which can lead to increase in tool wear and decrease in surface integrity. In this study, an in-depth analysis of hole shrinkage mechanism is carried out through a hybrid modelling approach for both dry and cryogenic cutting conditions. The plastic deformation induced by chip formation and tool-workpiece interaction is treated as equivalent thermomechanical loads, and heat convection conditions are described along tool path in order to perform details in heat transfer process for both cases. Quantitative analysis is presented through numerical simulation and experimental data of temperature and deformation along hole contour to analyze deformation components in order to reveal the hole shrinkage mechanism. Additional interactions between cutting tool and workpiece material are induced by recovery of elastoplastic deformation, and plastic portion is a more devastating factor in tool wear and surface damage induced by hole shrinkage. This study presents an in-depth and fundamental understanding of the hole shrinkage mechanism through a hybrid modelling approach, which can characterize heat transfer process during machining for both dry and cryogenic conditions, and simulation of this fully coupled thermomechanical cutting process with supply of coolants was rarely reported in previous research. The results show that plastic deformation induced hole shrinkage can enhance the interaction between workpiece material and cutting tool, and cryogenic assistance presents a good performance in restricting this kind of phenomenon. The related results could be used to optimize strategies of eliminating hole shrinkage with cryogenic assistance in industrial applications.</p>", "Keywords": "Cryogenic machining; Hole shrinkage; Drilling; Hybrid approach; Ti6Al4V", "DOI": "10.1007/s00170-021-08229-2", "PubYear": 2022, "Volume": "118", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Arts et Metiers Institute of Technology, LaBoMaP, Université Bourgogne Franche-Comté, HESAM Université, Cluny, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Arts et Metiers Institute of Technology, LaBoMaP, Université Bourgogne Franche-Comté, HESAM Université, Cluny, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>d", "Affiliation": "Arts et Metiers Institute of Technology, LAMPA, HESAM Université, Angers, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Arts et Metiers Institute of Technology, LaBoMaP, Université Bourgogne Franche-Comté, HESAM Université, Cluny, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Arts et Metiers Institute of Technology, LaBoMaP, Université Bourgogne Franche-Comté, HESAM Université, Cluny, France"}], "References": [{"Title": "Effect of cryogenic assistance on hole shrinkage during Ti6Al4V drilling", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "9-10", "Page": "2675", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Cryogenic orthogonal turning of Ti-6Al-4V", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "1-2", "Page": "359", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 91002462, "Title": "Architecture evaluation in continuous development", "Abstract": "Context In automotive, stage-gate processes have previously been the norm, with architecture created mainly during an early phase and then used to guide subsequent development phases. Current iterative and Agile development methods, where the implementation evolves continuously, changes the role of architecture. Objective We investigate how architecture evaluation can provide useful feedback during development of continuously evolving systems. Method Starting from the Architecture Tradeoff Analysis Method (ATAM), we performed architecture evaluation, both in a national research project led by an automotive Original Equipment Manufacturer (OEM), and at the OEM, in the context of continuous development. This allows us to include the experience of several architects from different organizations over several years. Using data produced during the evaluations we perform a post-hoc analysis to derive initial findings. We then validate and refine these findings through a series of focus groups with architects and industry experts. Findings We propose principles of continuous evaluation and evolution of architecture, and based on these discuss a roadmap for future research. Conclusion In iterative development settings, the needs are different from what typical architecture evaluation methods provide. Our principles show the importance of dedicated feedback-loops for continuous evolution of systems and their architecture.", "Keywords": "Architecture evaluation ; Continuous software engineering", "DOI": "10.1016/j.jss.2021.111111", "PubYear": 2022, "Volume": "184", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> — University of Gothenburg, Sweden;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> — University of Gothenburg, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Western Norway University of Applied Sciences, Norway;<PERSON><PERSON>s — University of Gothenburg, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Gran Sasso Science Institute (GSSI), Italy;<PERSON><PERSON>s — University of Gothenburg, Sweden"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Volvo Cars, Sweden"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Volvo Cars, Sweden"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Volvo Cars, Sweden"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Volvo Cars, Sweden"}], "References": []}, {"ArticleId": 91002484, "Title": "Large‐Area Piezoresistive Tactile Sensor Developed by Training a Super‐Simple Single‐Layer Carbon Nanotube‐Dispersed Polydimethylsiloxane Pad", "Abstract": "<p>The revolutionary concept of creating a large-area tactile sensor by training a simple, bulky material through deep learning (DL) is proposed. This enables the replacement of the conventional tactile sensor comprising a patterned array structure with a super-simple, single-layer, large-area tactile sensor pad. A crude carbon nanotube-dispersed polydimethylsiloxane pad—with a bias applied to the center and the resultant piezoresistive current detected at several electrodes located on the pad edge—plays a smart sensory role without the need for complicated fabrication of microengineered structures. The piezoresistive current while recording the indented location and the pressure thereon is measured, and then various DL models (a multimodel arrangement is necessary due to the viscoelasticity of the pad) using the collected data are trained. The proposed concept is realized using a tandem model comprising a combination of algorithms selected from deep neural networks, convolutional neural networks, long short-term memory networks, and 16 state-of-the-art machine learning algorithms. The hold-out dataset test accuracy for the indented location identification reaches 98.89%, and the goodness of fit for pressure prediction is evaluated with mean squared error of 2.5 × 10<sup>−3</sup> and coefficient of determination of 98.05%.</p>", "Keywords": "carbon nanotubes;deep learning;polydimethylsiloxane;piezoresistive materials;tactile sensing", "DOI": "10.1002/aisy.202100123", "PubYear": 2022, "Volume": "4", "Issue": "1", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": "Nanotechnology & Advanced Materials Engineering, Sejong University, 209 Neungdong-ro, Gwangjin-gu, Seoul, 143-747 South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanotechnology & Advanced Materials Engineering, Sejong University, 209 Neungdong-ro, Gwangjin-gu, Seoul, 143-747 South Korea"}, {"AuthorId": 3, "Name": "Chaewon Park", "Affiliation": "Nanotechnology & Advanced Materials Engineering, Sejong University, 209 Neungdong-ro, Gwangjin-gu, Seoul, 143-747 South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Nanotechnology & Advanced Materials Engineering, Sejong University, 209 Neungdong-ro, Gwangjin-gu, Seoul, 143-747 South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Materials Research Sector, Hyundai Mobis Co., Ltd, Yongin-si, Gyeonggi-do, 16891 South Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Materials Research Sector, Hyundai Mobis Co., Ltd, Yongin-si, Gyeonggi-do, 16891 South Korea"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Materials Research Sector, Hyundai Mobis Co., Ltd, Yongin-si, Gyeonggi-do, 16891 South Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanotechnology & Advanced Materials Engineering, Sejong University, 209 Neungdong-ro, Gwangjin-gu, Seoul, 143-747 South Korea"}], "References": []}, {"ArticleId": 91002544, "Title": "Human motion state recognition based on MEMS sensors and Zigbee network", "Abstract": "This article is to study the system structure scheme based on Zigbee wireless transmission, and complete the overall design of the system scheme on this basis. Human motion capture systems are widely used in the creation of film and television works, motion analysis, video games, rehabilitation medicine and other fields. This article discusses the design and implementation of a human motion capture system based on MEMS sensors and Zigbee networks. The system can be installed on the human body Multiple sensor nodes in various parts obtain the movement information of the human body, and use sensor network technology to aggregate these data and upload them to the host computer. First, this article introduces the characteristics of angular velocity sensors, acceleration sensors, magneto resistive sensors and Zigbee networks. Then, this article explains the overall structure of the system, and from a theoretical point of view, explains how the system uses angular velocity sensors, acceleration sensors, magneto resistive sensors and Zigbee networks to achieve human motion capture. This part focuses on including vector observation methods and angular velocity Two posture capture methods including the integration method, and their advantages and disadvantages are analyzed. To achieve the complementary advantages of the two algorithms, a data fusion algorithm based on complementary filtering is introduced and optimized appropriately. In addition, this article also introduces the networking principles and optimization schemes of the Zigbee network in this section. After this, this article explains in detail the system hardware structure, chip selection scheme, circuit design scheme, software workflow and implementation of core programs Method. Finally, this article shows the effect of the actual work of the system, and compares it with the theory to verify the feasibility of the theory. Based on the research of MEMS sensor measurement unit and algorithm, a Zigbee-based wireless transmission test system was established. LabVIEW software with functions of data reception, attitude angle calculation, trajectory calculation, eigenvalue extraction, BP neural network recognition, display and data saving was designed and tested the whole system functions. The test results show that the wireless data transmission of Zigbee network is normal, the data detection and processing programs of the host computer are stable, and the correct identification of the human body’s motion state can be realized. The results show that compared with the existing research, our research has increased its efficiency by 10%, and its accuracy has increased by nearly 15%.", "Keywords": "MEMS ; Zigbee ; Human motion ; State recognition ; Measurement", "DOI": "10.1016/j.comcom.2021.10.018", "PubYear": 2022, "Volume": "181", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Physical Education, Suqian University, Suqian, Jiangsu 223800, China;@squ.edu.cn"}], "References": [{"Title": "Human‐like evaluation method for object motion detection algorithms", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "8", "Page": "674", "JournalTitle": "IET Computer Vision"}, {"Title": "Human motion reconstruction using deep transformer networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Hyeonseung Im", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "162", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 91002566, "Title": "Construction of multi-modal perception model of communicative robot in non-structural cyber physical system environment based on optimized BT-SVM model", "Abstract": "With the rapid development of intelligent control technology, computer technology, bionics, artificial intelligence and other disciplines, more and more attention has been paid to the research of intelligent mobile robot technology, and autonomous positioning is the basis for mobile robots to conduct autonomous navigation and exploration research. Sensors assist each other to provide a wealth of perception information about the internal state of the robot and the external surrounding environment. This paper proposes a method for optimizing the Support Vector Machine (SVM) multi-classifier with a binary tree structure, which improves the accuracy of multi-modal tactile signal recognition. The improved particle swarm clustering algorithm is used to optimize the binary tree structure, reduce the error accumulation of the binary tree structure SVM multi-classifier, and further improve the accuracy of multi-modal tactile signal recognition. The effect of the method in this paper is verified by robot grasping experiments. The results show that the use of multi-modal information of two-dimensional images and three-dimensional point cloud images can effectively identify and locate target objects of different shapes. Compared with the processing method of two-dimensional or point cloud monomodal image information, the positioning error can be reduced by 54.8%, and the direction error can be reduced by 50.8%, which has better robustness and accuracy. The simulation results show that the improved PSOBT-SVM model has the best classification effect for artificial features, PCA features and spatio-temporal correlation features. The improved PSOBT-SVM optimizes the classification accuracy without changing the number of SVM classifiers, and proves its accuracy in classifying multimodal tactile signals.", "Keywords": "Inclusive robot ; Multi-modal perception ; Cyber–physical system ; PSO algorithm", "DOI": "10.1016/j.comcom.2021.10.019", "PubYear": 2022, "Volume": "181", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Wuchang University of Technology, Wuhan 430223, Hubei, China;Corresponding author;@wut.edu.cn"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligent Manufacturing, Wuhan Guanggu Vocational College, Wuhan 430079, Hubei, China"}], "References": [{"Title": "Multi-sensor fusion for body sensor network in medical human–robot interaction scenario", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "15", "JournalTitle": "Information Fusion"}, {"Title": "Robots That Use Language", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "1", "Page": "25", "JournalTitle": "Annual Review of Control, Robotics, and Autonomous Systems"}, {"Title": "Deep interaction: Wearable robot-assisted emotion communication for enhancing perception and expression ability of children with Autism Spectrum Disorders", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "709", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Non-verbal behavior of the robot companion: a contribution to the likeability", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "169", "Issue": "", "Page": "800", "JournalTitle": "Procedia Computer Science"}, {"Title": "Accurate and fast URL phishing detector: A convolutional neural network approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "178", "Issue": "", "Page": "107275", "JournalTitle": "Computer Networks"}, {"Title": "Fuzzy based image edge detection algorithm for blood vessel detection in retinal images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106452", "JournalTitle": "Applied Soft Computing"}, {"Title": "On Designing Expressive Robot Behavior: The Effect of Affective Cues on Interaction", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "6", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 91002589, "Title": "Face detection algorithm based on improved TinyYOLOv3 and attention mechanism", "Abstract": "An improved face detection method ground on TinyYOLOv3 algorithm is put forward in this paper in view of the low recognition rate of traditional face detection methods in complex background and the long detection time of existing face detection methods ground on deep learning The main network of TinyYOLOv3 is redesigned to extract more abundant semantic information, which is increasing the number of network layers. The deep separable convolution is used instead of the traditional convolution and the features of different network layers are fused. Meanwhile, the accuracy of face detection is guaranteed, size of this network model is reduced. The CIoU (complete intersection over union) loss is used on improving this original prediction loss of bounding box coordinates. The channel attention mechanism is integrated into the feature extraction network to improve the positioning accuracy and detection accuracy. The network convergence speed is accelerated by hyperparameter optimization and priori box clustering method. The face detection experiments were conducted on the Wider Face data sets. And the experimental results manifest that the proposed algorithm has high recognition accuracy in complex scenes. At the same time, the proposed algorithm is better than others in terms of detection speed and model size, and can meet the requirements of embedded devices.", "Keywords": "Face recognition ; TinyYOLOv3 ; Attention mechanism ; CIoU loss", "DOI": "10.1016/j.comcom.2021.10.023", "PubYear": 2022, "Volume": "181", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "Jiangjin Gao", "Affiliation": "Information Technology Center, Chengdu Sport University, Chengdu 610041, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Education and Information Technology Center, China West Normal University, Nanchong City, Nanchong 637002, China;Corresponding author"}], "References": [{"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 91002594, "Title": "Precise Learning of Source Code Contextual Semantics via Hierarchical Dependence Structure and Graph Attention Networks", "Abstract": "Deep learning is being used extensively in a variety of software engineering tasks, e.g., program classification and defect prediction. Although the technique eliminates the required process of feature engineering, the construction of source code model significantly affects the performance on those tasks. Most recent works was mainly focused on complementing AST-based source code models by introducing contextual dependencies extracted from CFG. However, all of them pay little attention to the representation of basic blocks, which are the basis of contextual dependencies. In this paper, we integrated AST and CFG and proposed a novel source code model embedded with hierarchical dependencies. Based on that, we also designed a neural network that depends on the graph attention mechanism. Specifically, we introduced the syntactic structural of the basic block, i.e., its corresponding AST, in source code model to provide sufficient information and fill the gap. We have evaluated this model on three practical software engineering tasks and compared it with other state-of-the-art methods. The results show that our model can significantly improve the performance. For example, compared to the best performing baseline, our model reduces the scale of parameters by 50% and achieves 4% improvement on accuracy on program classification task.", "Keywords": "Graph neural network ; Program analysis ; Deep learning ; Abstract syntax Tree ; Control flow graph", "DOI": "10.1016/j.jss.2021.111108", "PubYear": 2022, "Volume": "184", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies, Peking University, Beijing 100871, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Beijing Forestry University, Beijing 100083, China;Corresponding authors"}, {"AuthorId": 3, "Name": "Ge Li", "Affiliation": "Key Laboratory of High Confidence Software Technologies, Peking University, Beijing 100871, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, Swinburne University of Technology, Hawthorn VIC 3122, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies, Peking University, Beijing 100871, China;Corresponding authors"}], "References": []}, {"ArticleId": 91002622, "Title": "Big data-driven scheduling optimization algorithm for Cyber–Physical Systems based on a cloud platform", "Abstract": "In this paper, we study big data-driven Cyber–Physical Systems (CPS) through cloud platforms and design scheduling optimization algorithms to improve the efficiency of the system. A task scheduling scheme for large-scale factory access under cloud–edge collaborative computing architecture is proposed. The method firstly merges the directed acyclic graphs on cloud-side servers and edge-side servers; secondly, divide the tasks using a critical path-based partitioning strategy to effectively improve the allocation accuracy; then achieves load balancing through reasonable processor allocation, and finally compares and analyses the proposed task scheduling algorithm through simulation experiments. The experimental system is thoroughly analysed, hierarchically designed, and modelled, simulated, and the experimental data analysed and compared with related methods. The experimental results prove the effectiveness and correctness of the worst-case execution time analysis method and the idea of big data-driven CPS proposed in this paper and show that big data knowledge can help improve the accuracy of worst-case execution time analysis. This paper implements a big data-driven scheduling optimization algorithm for Cyber–Physical Systems based on a cloud platform, which improves the accuracy and efficiency of the algorithm by about 15% compared to other related studies.", "Keywords": "Cloud platform ; Big data-driven ; Cyber–Physical Systems ; Scheduling optimization algorithm", "DOI": "10.1016/j.comcom.2021.10.020", "PubYear": 2022, "Volume": "181", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Management, Central China Normal University, Wuhan, Hubei 430079, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Big Data, Taiyuan University of Technology, Taiyuan, Shanxi 030024, China"}], "References": []}, {"ArticleId": 91002627, "Title": "Two-stage salient object identification and segmentation based on irregularity", "Abstract": "<p>Saliency extraction is a technique inspired by the human approach in processing a selected portion of the visual information received. This feature in the human visual system helps reduce the processing the brain needs to extract important information and neglect general and unimportant information. This paper presents a novel approach to identifying the saliency of regions in a scene from which objects likely to be salient can be extracted. The proposed approach uses two stages, namely, local saliency identification (LSI) and global saliency identification (GSI) and uses irregularity as the saliency measure in both stages. Local saliency uses the structure of the object to determine saliency while global saliency identifies the saliency of the region based on the contrast in relation to the entire background. An object is considered to be salient if it satisfies both local and global criteria. In this work, the key challenges and limitations of existing methods, such as the sensitivity to texture and noise, the need to manually define certain parameters, and the need to have pre-knowledge of the nature of the image, were considered and appropriate solutions have been suggested. The proposed algorithm was tested on a set of 1000 images selected from MSRA saliency identification standard dataset and benchmarked with state-of-the-art approaches. The results obtained showed very good efficiency and this is evident from the evaluation values obtained from the used evaluation method, e. g. the value of the F-measure, reached 96.5 per cent in some cases. The limitation of the approach was with complex objects which themselves comprising more than one important region such as an image of a person. This will be discussed thoroughly in the result section.</p>", "Keywords": "Irregularity; Saliency; Image Processing; Thresholding; Fuzzy", "DOI": "10.1007/s11042-021-11378-x", "PubYear": 2021, "Volume": "80", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Oman College of Management & Technology, Muscat, Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence, De Montfort University, Leicester, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "TAUCHI, Tampere University, Tampere, Finland"}], "References": [{"Title": "Saliency-Based Image Retrieval as a Refinement to Content-Based Image Retrieval", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ELCVIA Electronic Letters on Computer Vision and Image Analysis"}]}, {"ArticleId": 91002629, "Title": "Usability inspection: Novice crowd inspectors versus expert", "Abstract": "Objective: This research study aims to investigate the use of novice crowd inspectors for usability inspection with respect to time spent and the cost incurred. This study compares the results of the novice crowd usability inspection guided by a single expert’s heuristic usability inspection (novice crowd usability inspection henceforth) with the expert heuristic usability inspection. Background: Traditional usability evaluation methods are time-consuming and expensive. Crowdsourcing has emerged as a cost-effective and quick means of software usability evaluation. Method: In this regard, we designed an experiment to evaluate the usability of two websites and a web dashboard. Results: The results of the experiment show that novice crowd usability inspection guided by a single expert’s heuristic usability inspection: a) Finds the same usability issues (w.r.t. content & quantity) as expert heuristic usability inspection. b) Is cost-effective than expert heuristic usability inspection employing less time duration. Conclusion: Based on the findings of this research study, we can conclude that the novice crowd usability inspection guided by a single expert’s heuristic usability inspection and expert heuristic usability inspection, on average, gives the same results in terms of issues identified.", "Keywords": "Crowdsourcing ; Usability inspection ; Heuristic evaluations ; Empirical studies in visualizations", "DOI": "10.1016/j.jss.2021.111122", "PubYear": 2022, "Volume": "183", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Basic and Applied Sciences International Islamic University, Islamabad, Pakistan;Faculty of Computing, Riphah International University, Islamabad, Pakistan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computing, Riphah International University, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Basic and Applied Sciences International Islamic University, Islamabad, Pakistan"}], "References": []}, {"ArticleId": 91002651, "Title": "Constriction Factor Particle Swarm Optimization based load balancing and cell association for 5G heterogeneous networks", "Abstract": "With the rapidly growing traffic demand, the cellular industry is moving toward heterogeneity to fulfill the heavy traffic requirement. The existing heterogeneous networks are comprised of the Long-Term Evolution (LTE), LTE-Advanced (LTE-A), and other compatible access technologies. It has been identified that due to the shortage of capacity, cell performance can deteriorate. To deal with users’ requirements, the 5G heterogeneous network architectures comprising LTE-advanced access technology with the combination of comparatively low configured small base stations, and macro eNodeBs (MeNB) have been extended as a solution. Therefore, operators have introduced the 5G with the existing LTE Advanced Heterogeneous Network (5GLHN) where small cells such as Home eNodeBs (HeNBs) are deployed overlapping with the conventional macro eNodeB (MeNB). However, in highly dense and closely compacted 5GLHNs, the cell capacity can still be lower than what is on-demand, thus affecting the system throughput. This article proposes a framework to maximize the throughput in 5GLHNs through a Constriction Factor Particle Swarm Optimization (CFPSO) technique of cell association and load-balancing algorithm to enhance the throughput of the 5GLHN. The proposed approach is designed to offload the traffic of MeNB Users (MUEs) to the small cells (HeNBs). The convergence, cumulative distribution function of the UEs rate, average throughput, and allocation time are analyzed to evaluate the performance of the proposed CFPSO approach. Simulation results reveal that the throughput of the proposed approach is improved by up to 44.08% of the existing index-based approach and by 94.20 % of the existing Matching with Minimum Quota (MMQ) approach.", "Keywords": "Signal to interference plus noise ratio ; 5G ; LTE-advanced ; Heterogeneous network ; Cell association ; Load management", "DOI": "10.1016/j.comcom.2021.10.021", "PubYear": 2021, "Volume": "180", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Cyber Security, Faculty of Information Science and Technology, Universiti Kebangsaan Malaysia (UKM), 43600 Bangi, Selangor, Malaysia;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Multimedia University, 63100 Cyberjaya, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, A’Sharqiyah University, 400 Ibra, Sultanate of Oman"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Cyberspace Institute of Advanced Technology, Guanghzou University, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of CSIT, Al-Baha University, Saudi Arabia and ReDCAD Laboratory, University of Sfax, Tunisia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Science and Digital Innovation, UCSI University Malaysia, 56000 Kuala Lumpur, Malaysia;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical engineering department, Sukkur IBA University, Sindh, Pakistan"}], "References": [{"Title": "Selection of effective machine learning algorithm and Bot-IoT attacks traffic identification for internet of things in smart city", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "433", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "IoT malicious traffic identification using wrapper-based feature selection mechanisms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "101863", "JournalTitle": "Computers & Security"}, {"Title": "A discrete PSO-based static load balancing algorithm for distributed simulations in a cloud environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "497", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 91002660, "Title": "Energy consumption model for data transfer in smartphone", "Abstract": "Smartphones manufactured at present are equipped with the new Wireless Local Area Network (WLAN) calibrated to IEEE standards on its interface, which supports the Multiple Input Multiple Output (MIMO) feature. This technological advancement has enhanced smartphones to satisfy the present IEEE standard requirements for WLANs. However, the merits of smartphones are restricted by battery lifecycle. In this paper, the energy consumption of a smartphone equipped with the MIMO system throughout the transmission and reception of data is studied. The impact of the various factors of recently developed WLANs (such as 802.11n, which includes modulation and coding scheme and MIMO) are considered in conditions of throughput and power/energy consumption by the use of well-known simulator, which is called Network Simulator 3 (NS-3). In addition, the energy consumed in the course of transmitting and receiving data is differentiated through per-bit energy consumption with various MIMO compositions and physical data rates. The consequences reveal that growing the system configuration farther 2 × 2 MIMO enhances the throughput and reduces the per-bit energy consumption. Furthermore, the capability to predict the energy consumed for data transmission is considered essential for energy-aware applications. For example, task offloading from modern mobile appliances to cloud computing is a highly potential approach for saving energy in mobile devices. The decision to offload is essential to make offloading more effective. Energy models are required to precisely predict the energy consumption of networking and computing processes. Thus, this precision enables the offloading technology to precisely assess whether offloading a specified task will save energy. For this purpose, an energy consumption model for transmitting and receiving is developed based on a MIMO parameter with high accuracy. The simulation demonstrates that our energy models are practical and effective in real-world scenarios. In addition, these models estimate the energy consumption per bit, and offer system designers and application developers effective tools for designing energy-efficient systems.", "Keywords": "Energy consumption ; Energy models ; Power consumption ; Smartphone ; MIMO ; WLAN ; WiFi ; IEEE 802.11", "DOI": "10.1016/j.comcom.2021.10.014", "PubYear": 2022, "Volume": "182", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, College of Engineering, King Saud University, Riyadh 11421, Saudi Arabia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, College of Engineering, King Saud University, Riyadh 11421, Saudi Arabia"}], "References": []}, {"ArticleId": 91002672, "Title": "On bivariate Jain operators", "Abstract": "<p style='text-indent:20px;'>In this paper we deal with bivariate extension of Jain operators. Using elementary method, we show that these opearators are non-increasing in <inline-formula><tex-math id=\"M1\">\\begin{document}$ n $\\end{document}</tex-math></inline-formula> when the attached function is convex. Moreover, we demonstrate that these operators preserve the properties of modulus of continuity. Finally, we present a Voronovskaja type theorem for the sequence of bivariate Jain operators.</p>", "Keywords": "Bivariate Jain operator", "DOI": "10.3934/mfc.2021026", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>z-Tunca", "Affiliation": ""}], "References": []}, {"ArticleId": 91002774, "Title": "Application of machine learning algorithms for prediction of sinter machine productivity", "Abstract": "Sinter machine productivity is key techno-economic parameter of an integrated steel plant. It depends upon the composition of different constituents like iron ore fines, flux and coke breeze which are agglomerated to produce sinter for blast furnaces. It is difficult to assess the interdependence of these constituents and their effect on sinter productivity through physical experimentation. In this paper, machine learning and data analytics approach have been applied to predict the sinter machine productivity. Industrial data of sinter machine productivity from an integrated steel plant have been collected. Linear regression and artificial neural network (ANN) models were developed to predict sinter machine productivity with the composition of constituent materials of the agglomerate as model inputs. The ANN model, developed in the present work, agrees well with measured sinter machine productivity. Sensitivity analysis identified that, percentage of MgO in flux and CaO in sinter have a highly detrimental effect whereas total Fe content in iron ore fines and percentage of SiO <sub>2</sub> in sinter have the most favorable impact on sinter machine productivity.", "Keywords": "Sinter machine productivity ; Linear regression ; Artificial neural network ; Sensitivity analysis", "DOI": "10.1016/j.mlwa.2021.100186", "PubYear": 2021, "Volume": "6", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kalinga Institute of Industrial Technology, Bhubaneshwar, India;Corresponding author"}, {"AuthorId": 2, "Name": "Subhra Dhara", "Affiliation": "Research & Development Centre for Iron & Steel, Steel Authority of India Limited, Ranchi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research & Development Centre for Iron & Steel, Steel Authority of India Limited, Ranchi, India"}], "References": [{"Title": "Real-time moisture control in sintering process using offline–online NARX neural networks", "Authors": "Yu<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "209", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 91002895, "Title": "A new kind of Bi-variate $ \\lambda $ -<PERSON> type operator with shifted knots and its associated GBS form", "Abstract": "<p style='text-indent:20px;'>In this paper, we introduce a bi-variate case of a new kind of <inline-formula><tex-math id=\"M1\">\\begin{document}$ \\lambda $\\end{document}</tex-math></inline-formula>-<PERSON> type operator with shifted knots defined by <PERSON> et al. [<xref ref-type=\"bibr\" rid=\"b31\">31</xref>]. The rate of convergence of the bi-variate operators is obtained in terms of the complete and partial moduli of continuity. Next, we give an error estimate in the approximation of a function in the Lipschitz class and establish a Voronovskaja type theorem. Also, we define the associated GBS(Generalized Boolean Sum) operators and study the degree of approximation of Bögel continuous and Bögel differentiable functions by these operators with the aid of the mixed modulus of smoothness. Finally, we show the rate of convergence of the bi-variate operators and their GBS case for certain functions by illustrative graphics and tables using MATLAB algorithms.</p>", "Keywords": "Bi-variate $ \\lambda- $<PERSON>-Ka<PERSON><PERSON>ich operators", "DOI": "10.3934/mfc.2021025", "PubYear": 2022, "Volume": "5", "Issue": "3", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 91003016, "Title": "Nonlinear Dynamics and Performance Analysis of a Buck Converter with Hysteresis Control", "Abstract": "<p>This paper presents the mathematical modeling and experimental implementation of a Buck converter with hysteresis control. The system is described using a state-space model. Theoretical and simulation studies show that the zero hysteresis control leads to an equilibrium point with the implication of an infinite commutation frequency, while the use of a constant hysteresis band induces a limit cycle with a finite switching frequency. There exists a tradeoff between voltage output ripple and transistor switching frequency. An experimental prototype for the Buck power converter is built, and theoretical results are verified experimentally. In general terms, the Buck converter with the hysteresis control shows a robust control with respect to load variations, with undesired high switching frequency taking place for a very narrow hysteresis band, which is solved by tuning the hysteresis band properly.</p>", "Keywords": "bifurcation; buck power converter; chaos; hysteresis control; nonlinear dynamics; switching frequency bifurcation ; buck power converter ; chaos ; hysteresis control ; nonlinear dynamics ; switching frequency", "DOI": "10.3390/computation9100112", "PubYear": 2021, "Volume": "9", "Issue": "10", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Istituto Motori IM, Italian National Research Council, Via Guglielmo <PERSON>, 4, 80125 Napoli, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Energy and Automation, Faculty of Mines, Universidad Nacional de Colombia, Sede Medellín, Carrera 80 No. 65-223, Robledo, Medellín 050041, Colombia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Energy and Automation, Faculty of Mines, Universidad Nacional de Colombia, Sede Medellín, Carrera 80 No. 65-223, Robledo, Medellín 050041, Colombia"}], "References": []}, {"ArticleId": 91003136, "Title": "Preface", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00542-021-05235-y", "PubYear": 2022, "Volume": "28", "Issue": "2", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Kalyani, Kalyani, India"}], "References": []}, {"ArticleId": 91003293, "Title": "Construction of biocomputing systems based on switchable bioelectrocatalysis and stimulus-responsive film electrodes", "Abstract": "The construction of biocomputing platforms, particularly based on multiple stimulus-responsive film electrodes and switchable bioelectrocatalysis, is briefly overviewed. The bioelectrocatalysis mechanisms and switch principles for common redox biomacromolecules, such as horseradish peroxidase (HRP), glucose oxidase (GOD), natural deoxyribonucleic acid (DNA) and coenzyme nicotinamide adenine dinucleotide (NADH), are briefly introduced. Reversible switch systems are exemplified with smart hydrogel polymer films, functional nanomaterial composite films, inorganic coordination polymers and molecularly imprinted polymer (MIP) films modified on electrode surfaces, demonstrating different electrochemical and optical properties depending on external stimuli. This review highlights the various biomolecular logic gates, logic circuits and devices including sequential circuits achieved due to biocatalytic reactions combined with stimulus-responsive film electrodes. Future developments leading to intelligent multianalyte biosensing and information processing at the molecular level based on switchable and tunable bioelectrocatalysis properties are greatly expected as a research trend in this area.", "Keywords": "Bioelectrocatalysis ; Biocomputing ; Stimulus-responsive film electrodes ; Biointerfaces", "DOI": "10.1016/j.snr.2021.100054", "PubYear": 2021, "Volume": "3", "Issue": "", "JournalId": 72042, "JournalTitle": "Sensors and Actuators Reports", "ISSN": "2666-0539", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>g <PERSON>n", "Affiliation": "Department of Applied Chemistry, College of Basic Science, Tianjin Agricultural University, Tianjin 300384, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Radiopharmaceuticals, Ministry of Education, College of Chemistry, Beijing Normal University, Beijing 100875, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Radiopharmaceuticals, Ministry of Education, College of Chemistry, Beijing Normal University, Beijing 100875, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Basic Medicine, Ningxia Medical University, Yinchuan 750004, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Liu", "Affiliation": "Key Laboratory of Radiopharmaceuticals, Ministry of Education, College of Chemistry, Beijing Normal University, Beijing 100875, PR China;Corresponding author"}], "References": []}, {"ArticleId": 91003313, "Title": "Research on Endpoint Detection Algorithm in High Altitude Explosion Point Location Technology", "Abstract": "Aiming at the problem of inadequate positioning accuracy of sound endpoints by the dual-element single-threshold endpoint detection algorithm and the single-element dual-threshold endpoint detection algorithm in the process of locating the sound source of weather modification bombs at high altitudes during artificial weather modification, a multi-element dual threshold endpoint detection algorithm. First, according to the characteristics of high-altitude explosion of weather modification bombs and ground reception, the sound signal is filtered and denoised, divided into frames, and windowed. Then, the time-domain feature short-term energy, short-term zero-crossing rate and frequency domain feature short-term information entropy of each frame of the sound signal are calculated, and double thresholds are set for detection. In this way, the start and end points of the explosion sound in the collected sound signal are found, and the data is imported into the positioning algorithm for processing, and then the explosion point of the weather modification bombs in the high air is located. The test results show that the method can accurately distinguish the end point of effective explosion sound, and has practical application value for the location of the sound source of the high-altitude explosion point of the weather modification bombs.", "Keywords": "", "DOI": "10.7753/IJCATR1011.1001", "PubYear": 2021, "Volume": "10", "Issue": "11", "JournalId": 17019, "JournalTitle": "International Journal of Computer Applications Technology and Research", "ISSN": "", "EISSN": "2319-8656", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 91003352, "Title": "Privacy-Preserving cloud-Aided broad learning system", "Abstract": "Broad Learning System (BLS) is a new deep learning model proposed recently, which shows its effectiveness in many fields, such as image recognition and fault detection. In this paper, we propose a secure, efficient, and verifiable outsourcing algorithm for BLS. This algorithm enables resource constrained devices to outsource BLS algorithm to untrusted cloud server to complete model training, which is of great significance for the promotion and application of BLS algorithm. Compared with the original BLS algorithm, this algorithm not only improves the efficiency of the algorithm on the client, but also ensures that the sensitive information of the client will not be leaked to the cloud server. In addition, in our algorithm, the client can verify the correctness of returned results with a probability of almost 1. Finally, we analyze the security and efficiency of our algorithm in theory and prove our algorithms feasibility through experiments.", "Keywords": "Broad learning system (BLS) ; Deep learning ; Secure outsourcing computations ; Privacy preserving", "DOI": "10.1016/j.cose.2021.102503", "PubYear": 2022, "Volume": "112", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Liu", "Affiliation": "College of Computer Science and Technology, Qingdao University, 266071 Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, 266071 Qingdao, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, 266071 Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Qingdao University, 266071 Qingdao, China;Institute of Big Data Technology and Smart City, Qingdao University, 266071 Qingdao, China;State Key Laboratory of Information Security, Institute of Information Engineering, Chinese Academy of Sciences, 100093 Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Xian Jiaotong University, 710049 Xian, China"}], "References": [{"Title": "Differentially private model publishing in cyber physical systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "1297", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A survey on security and privacy of federated learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "619", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 91003353, "Title": "Efficient algorithms for victim item selection in privacy-preserving utility mining", "Abstract": "High-utility itemset mining has evolved as an essential and captivating research topic. It aims to extract the patterns/itemsets having high utility value; hence, they are called high utility itemsets (HUIs). From a business perspective, a utility can be the benefit associated with the sale of a particular item or the usefulness or satisfaction that a customer experiences from a product. The economic utilities are helpful to evaluate the drivers behind a customer’s purchase decision. The advances in information technology have enabled us to access the datasets related to various domains like health care, stock market, market-basket, education and bioinformatics. Companies strive to increase the utility value of their products and share their customer’s transactions data to extract high utility patterns to achieve global customer insights. However, this can lead to massive security and privacy risk if their competitors misuse the patterns that can disclose their confidential information. Privacy-preserving utility mining (PPUM) is a branch of privacy-preserving data mining (PPDM) that presents various algorithms which intend to hide sensitive high utility itemsets (SHUIs) and maintain a balance between utility-maximizing and privacy-preserving. In this paper, two SHUIs hiding algorithms, MinMax and Weighted, are proposed with three variants of each algorithm. Experiments on various datasets show that proposed algorithms perform better than the existing SHUIs hiding algorithms as fewer distortions of non-sensitive knowledge occur. This study uses six performance evaluating metrics to assess the proposed algorithms against compared algorithms.", "Keywords": "Privacy-preserving utility mining ; Sensitive pattern ; Data sanitization ; High utility itemsets ; Knowledge hiding technique", "DOI": "10.1016/j.future.2021.10.008", "PubYear": 2022, "Volume": "128", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Indian Institute of Technology Roorkee, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Indian Institute of Technology Roorkee, India"}], "References": [{"Title": "Effective sanitization approaches to protect sensitive knowledge in high-utility itemset mining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "1", "Page": "169", "JournalTitle": "Applied Intelligence"}, {"Title": "MR-OVnTSA: a heuristics based sensitive pattern hiding approach for big data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "12", "Page": "4241", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 91003603, "Title": "Bound state solutions for fractional Schrödinger-Poisson systems", "Abstract": "<p style='text-indent:20px;'>In this work, we investigate a class of fractional Schrödinger - Poisson systems</p><p style='text-indent:20px;'><disp-formula> <label/> <tex-math id=\"FE1\"> \\begin{document}$ \\begin{equation*} \\left\\{\\begin{array}{ll}(-\\triangle)^s u +V(x)u+\\lambda\\phi u = \\mu u+|u|^{p-1}u, &amp; x\\in\\ \\mathbb{R}^3, \\\\(-\\triangle)^s \\phi = u^2, &amp; x\\in\\ \\mathbb{R}^3, \\end{array}\\right. \\end{equation*} $\\end{document} </tex-math></disp-formula></p><p style='text-indent:20px;'>where <inline-formula><tex-math id=\"M1\">\\begin{document}$ s\\in(\\frac{3}{4}, 1) $\\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id=\"M2\">\\begin{document}$ p\\in(3, 5) $\\end{document}</tex-math></inline-formula>, <inline-formula><tex-math id=\"M3\">\\begin{document}$ \\lambda $\\end{document}</tex-math></inline-formula> is a positive parameter. By the variational method, we show that there exists <inline-formula><tex-math id=\"M4\">\\begin{document}$ \\delta(\\lambda)&gt;0 $\\end{document}</tex-math></inline-formula> such that for all <inline-formula><tex-math id=\"M5\">\\begin{document}$ \\mu\\in[\\mu_1, \\mu_1+\\delta(\\lambda)) $\\end{document}</tex-math></inline-formula>, the above fractional Schrödinger -Poisson systems possess a nonnegative bound state solutions with positive energy. Here <inline-formula><tex-math id=\"M6\">\\begin{document}$ \\mu_1 $\\end{document}</tex-math></inline-formula> is the first eigenvalue of <inline-formula><tex-math id=\"M7\">\\begin{document}$ (-\\triangle)^s +V(x) $\\end{document}</tex-math></inline-formula>.</p>", "Keywords": "Fractional Schrödinger-Poisson systems", "DOI": "10.3934/mfc.2021023", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Gen Li", "Affiliation": ""}], "References": []}, {"ArticleId": 91003628, "Title": "Some generalizations of delay integral inequalities of Gronwall-Bellman type with power and their applications", "Abstract": "<p xml:lang=\"fr\"><p style='text-indent:20px;'>Noting the diverse generalizations of the <PERSON><PERSON><PERSON>-<PERSON> inequality, this paper investigates some new delay integral inequalities with power, deriving explicit bound on the solution and providing an example. The inequalities given here can act as powerful tools for studying qualitative properties such as existence, uniqueness, boundedness, stability and asymptotics of solutions of differential and integral equations.</p></p>", "Keywords": "Nonlinear integral inequality", "DOI": "10.3934/mfc.2021022", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 91003800, "Title": "File System Support for Privacy-Preserving Analysis and Forensics in Low-Bandwidth Edge Environments", "Abstract": "<p>In this paper, we present initial results from our distributed edge systems research in the domain of sustainable harvesting of common good resources in the Arctic Ocean. Specifically, we are developing a digital platform for real-time privacy-preserving sustainability management in the domain of commercial fishery surveillance operations. This is in response to potentially privacy-infringing mandates from some governments to combat overfishing and other sustainability challenges. Our approach is to deploy sensory devices and distributed artificial intelligence algorithms on mobile, offshore fishing vessels and at mainland central control centers. To facilitate this, we need a novel data plane supporting efficient, available, secure, tamper-proof, and compliant data management in this weakly connected offshore environment. We have built our first prototype of Dorvu, a novel distributed file system in this context. Our devised architecture, the design trade-offs among conflicting properties, and our initial experiences are further detailed in this paper.</p>", "Keywords": "edge computing; privacy preservation; artificial intelligence; file systems; machine learning; digital forensics edge computing ; privacy preservation ; artificial intelligence ; file systems ; machine learning ; digital forensics", "DOI": "10.3390/info12100430", "PubYear": 2021, "Volume": "12", "Issue": "10", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway↑Author to whom correspondence should be addressed. Academic Editors: <PERSON> and <PERSON><PERSON>"}, {"AuthorId": 2, "Name": "Tor<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway↑Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway↑Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, UiT The Arctic University of Norway, 9037 Tromsø, Norway Holistic Systems Department, SimulaMet, 0164 Oslo, Norway Department of Computer Science, Oslo Metropolitan University, 0130 Oslo, Norway"}], "References": [{"Title": "A novel multi-stage ensemble model with enhanced outlier adaptation for credit scoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113872", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dimension reduction of multimodal data by auto-weighted local discriminant analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "461", "Issue": "", "Page": "27", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 91004011, "Title": "GENERTATION OF SYNTHETIC EEG SIGNAL; EVALUATION OF MUTUAL INFORMATION AND CORRELATION COEFFICIENT", "Abstract": "When it comes to the diagnosis and treatment of epilepsy, as well as the general quality of life of the patient, the electroencephalogram (EEG) is an often utilised as auxiliary test to aid in the process. It is the primary diagnostic test for epilepsy because it gives a continuous assessment of brain function with great temporal resolution over a long period of time, making it an excellent tool for early detection of epilepsy. Specifically, in this paper, we propose the creation of two Simulink models that can generate synthetic EEG data while maintaining the statistical characteristics of the EEG. In addition, we present the evaluation of two characteristics such as mutual information and correlation coefficient, in order to test the characteristics of any such synthetic generated data. The characteristics proposed here are tested with standard data available on online repository. Apart from using these characteristics for testing the validity of synthetic data, we may use these characteristics as features for machine learning applications.", "Keywords": "Electroencephalogram, Mutual Information, Correlation", "DOI": "10.26483/ijarcs.v12i5.6773", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering BMS Institute of Technology and Management Affiliated to Visvesvaraya Technological University Bangalore, India"}], "References": []}, {"ArticleId": 91004041, "Title": "WIRELESS SENSOR NETWORK PERFORMANCE ANALYSIS UNDER SINKHOLE ATTACKS", "Abstract": "WSN is one of the modern networks that use technologies and applications in public places. It consists of hundreds and thousands of tiny sensor nodes scattered in the network and has limited scope and range resources connected to the base stations. The specifications of these nodes are low cost and low energy and used for monitoring purposes. Since the sensors are small and many, it is easy to attack these networks. Therefore, there will be many potential attacks on the network of sensors, and among these attacks are jamming, sinkhole, eavesdropping, and other attacks. The sinkhole attack is the most attack that works to destroy paths by announcing the update of the fake routing related to it; the attack occurs through the compromised node (the malicious node) So that it announces a file containing the routing information and works to attract the rest of the nodes to do this routing the data towards it and then operating the sphere of influence. One of the effects of this attack is to reduce the overall network performance, and it can also be used to make another attack, such as a selective redirection attack and a spoofing attack. This paper aims to analyze and detect sinkhole attacks in wireless sensor networks.", "Keywords": "", "DOI": "10.26483/ijarcs.v12i5.6767", "PubYear": 2021, "Volume": "12", "Issue": "5", "JournalId": 35682, "JournalTitle": "International Journal of Advanced Research in Computer Science", "ISSN": "", "EISSN": "0976-5697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ITRDC, University of Kufa Al Najaf, Iraq"}], "References": []}, {"ArticleId": ********, "Title": "Teleportation in Virtual Reality; A Mini-Review", "Abstract": "<p>Teleportation is a widely implemented virtual locomotion technique that allows users to navigate beyond the confines of available tracking space with a low possibility of inducing virtual reality (VR) sickness. This paper provides a comprehensive overview of prior research on teleportation. We report results from user studies that have evaluated teleportation in comparison to other locomotion methods and survey improved versions of teleportation. We identify a number of areas for future research.</p>", "Keywords": "Virtual realty; Virtual locomotion; Teleportation; VR sickness; navigation", "DOI": "10.3389/frvir.2021.730792", "PubYear": 2021, "Volume": "2", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "United States"}], "References": [{"Title": "Effects of steering locomotion and teleporting on cybersickness and presence in HMD-based virtual reality", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "453", "JournalTitle": "Virtual Reality"}, {"Title": "Examining virtual reality navigation techniques for 3D network visualisations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "100937", "JournalTitle": "Journal of Computer Languages"}]}, {"ArticleId": 91004222, "Title": "Immutable Log Storage as a Service on Private and Public Blockchains", "Abstract": "Service Level Agreements (SLA) are employed to ensure the performance of Cloud solutions. When a component fails, the importance of logs increases significantly. All departments may turn to logs to determine the cause of the issue and find the party at fault. The party at fault may be motivated to tamper with the logs to hide their role. We argue that the critical nature of Cloud logs calls for immutability and verification mechanism without the presence of a single trusted party. This article proposes such a mechanism by describing a blockchain-based log storage system, called Logchain, which can be integrated with existing private and public blockchain solutions. Logchain uses the immutability feature of blockchain to provide a tamper-resistance platform for log storage. Additionally, we propose a hierarchical structure to address blockchains’ scalability issues. To validate the mechanism, we integrate Logchain into Ethereum and IBM Blockchain. We show that the solution is scalable and perform the analysis of the cost of ownership to help a reader select an implementation that would address their needs. The Logchain's scalability improvement on a blockchain is achieved without any alteration of blockchains’ fundamental architecture. As shown in this work, it can function on private and public blockchains and, therefore, can be a suitable alternative for organizations that need a secure, immutable log storage platform.", "Keywords": "Blockchain;log storage;cloud monitoring;cloud computing;log verification;immutable storage", "DOI": "10.1109/TSC.2021.3120690", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Ryerson University, Toronto, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Ryerson University, Toronto, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IBM Canada Lab, Toronto, ON, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IBM Watson and Cloud Platform, Austin, TX, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Ryerson University, Toronto, ON, Canada"}], "References": [{"Title": "A survey of blockchain consensus algorithms performance evaluation criteria", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "113385", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A taxonomy of blockchain consensus protocols: A survey and classification framework", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114384", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 91004260, "Title": "Frequency stabilization in interconnected power system using bat and harmony search algorithm with coordinated controllers", "Abstract": "Modern power system faces excessive frequency aberrations due to the intermittent renewable generations and persistently changing load demands. To avoid any possible blackout, an efficient and robust control strategy is obligatory to minimize deviations in the system frequency and tie-line. Hence, to achieve this target, a new two-degree of freedom-tilted integral derivative with filter (2DOF–TIDN) controller is proposed in this work for a two-area wind-hydro-diesel power system. To enhance the outcome of the proposed 2DOF–TIDN controller, its gain parameters are optimized with the use of a newly designed hybrid bat algorithm-harmony search algorithm (hybrid BA–HSA) technique. The effectiveness and superiority of hybrid BA–HSA tuned 2DOF–TIDN is validated over various existing optimization techniques like cuckoo search (CS), particle swarm optimization (PSO), HSA, BA and teaching learning-based optimization (TLBO). To further refine the system outcome in the dynamic conditions, several flexible AC transmission systems (FACTS) and superconducting magnetic energy storage (SMES) units are adopted for enriching the frequency and tie-line responses. The FACTS controllers like static synchronous series compensator (SSSC), thyristor-controlled phase shifter (TCPS), unified power flow controller (UPFC) and interline power flow controller (IPFC) are employed with SMES simultaneously. The simulation results disclose that the hybrid BA–HSA based 2DOF–TIDN shows superior dynamic performance with IPFC–SMES than other studied approaches. A sensitivity analysis is examined to verify the robustness of proposed controller under ± 25% changes in loading and system parameters.", "Keywords": "Automatic generation control ; Hybrid BA–HSA ; FACTS controllers ; SMES ; 2DOF–TIDN ; Interconnected system", "DOI": "10.1016/j.asoc.2021.107986", "PubYear": 2021, "Volume": "113", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Universiti Malaysia Pahang, Kuantan, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, Universiti Malaysia Pahang, Kuantan, Malaysia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronics Eng., Nalanda Institute of Eng. &Tech., A.P, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering & the Environment, University of Southampton, Highfield, United Kingdom"}], "References": [{"Title": "Improved stochastic fractal search algorithm and modified cost function for automatic generation control of interconnected electric power systems", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "103407", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Modified sine cosine algorithm-based fuzzy-aided PID controller for automatic generation control of multiarea power systems", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "17", "Page": "12919", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 91004396, "Title": "Influence of heterogeneous edge weights on assortative mixing patterns in military personnel networks", "Abstract": "<p>A toy model to examine the effect of a heterogeneous edge weight structure on assortative mixing patterns is developed. This model is used as a benchmark to assess assortative mixing patterns in a real military personnel network describing occupation changes among recruits to the Canadian Armed Forces. Mixing patterns on the network suggest a strong tendency for members to transfer between different occupation groups; possible areas on which to focus retention strategies are identified.</p>", "Keywords": "Mixing patterns; Weighted network; Heterogeneous edge weights; Assortative mixing", "DOI": "10.1007/s10044-021-01036-1", "PubYear": 2022, "Volume": "25", "Issue": "1", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of National Defence, Ottawa, Canada"}], "References": []}, {"ArticleId": 91004474, "Title": "Embodied energy of parts in sheet metal forming: modeling and application for energy saving in the workshop", "Abstract": "<p>Parts or products can be considered as the carrier of energy consumption during manufacturing since they are the final output of workshops. The concept of “embodied energy” is presented as a feasible indicator to characterize the energy consumption of a part or a product. Previous work mainly discussed methods and technologies to apply the embodied energy of parts for selecting materials and processes in product design; the role that embodied energy of parts can play in production optimization was seldom investigated. In addition, different from machining workshops, which has been widely discussed, there is less research on energy saving in the sheet metal forming workshop. In this paper, modeling and application of embodied energy for energy saving in the sheet metal forming workshop is presented. The embodied energy of parts during the manufacturing phase ( EEPM ) is modeled. The EEPM is evaluated by discrete event simulation to identify energy distribution, energy-intensive processes, and bottlenecks (e.g., processes with feed blocking, machines with low utilization, and others) during production. Energy-oriented scheduling in the workshop is implemented, and a two-stage approach based on ranks of EEPM that can quickly select more efficient production solutions is proposed. A case in a stamping workshop of a partner company validates the proposed methods. This study provides a practical approach to seek a trade-off between energy saving and high production efficiency for energy-intensive discrete workshops.</p>", "Keywords": "Embodied energy; Energy saving; Sheet metal forming; Sustainable manufacturing; Workshop", "DOI": "10.1007/s00170-021-08209-6", "PubYear": 2022, "Volume": "118", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Green Design and Manufacturing of Mechanical Industry, Hefei University of Technology, Hefei, China;School of Mechanical Engineering, Hefei University of Technology, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Green Design and Manufacturing of Mechanical Industry, Hefei University of Technology, Hefei, China;School of Mechanical Engineering, Hefei University of Technology, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Green Design and Manufacturing of Mechanical Industry, Hefei University of Technology, Hefei, China;School of Mechanical Engineering, Hefei University of Technology, Hefei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Green Design and Manufacturing of Mechanical Industry, Hefei University of Technology, Hefei, China;School of Mechanical Engineering, Hefei University of Technology, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Green Design and Manufacturing of Mechanical Industry, Hefei University of Technology, Hefei, China;School of Mechanical Engineering, Hefei University of Technology, Hefei, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electronic Engineering, Suzhou University, Suzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Green Design and Manufacturing of Mechanical Industry, Hefei University of Technology, Hefei, China;School of Mechanical Engineering, Hefei University of Technology, Hefei, China"}], "References": []}, {"ArticleId": 91004664, "Title": "An ObsPy Library for Event Detection and Seismic Attribute Calculation: Preparing Waveforms for Automated Analysis", "Abstract": "<p>We have implemented an extension for the observational seismology <em>obspy</em> software package to provide a streamlined tool tailored to the processing of seismic signals from non-earthquake sources, in particular those from deforming systems such as glaciers and landslides. This <em>seismic attributes</em> library provides functionality to: (1) download and/or pre-process seismic waveform data; (2) detect and catalogue seismic events using multi-component signals from one or more seismometers; and (3) calculate characteristics (‘attributes’/‘features’) of the identified events. The workflow is controlled by three main functions that have been tested for the breadth of data types expected from permanent and campaign-deployed seismic instrumentation. A selected STA/LTA-type (short-term average/long-term average), or other, event detection algorithm can be applied to the waveforms and user-defined functions implemented to calculate any required characteristics of the detected events. The code is written inPython 2/3 and is available on GitHub together with detailed documentation and worked examples.</p>", "Keywords": "geophysics; seismology; data processing; reproducibility; Python;geophysics;seismology;data processing;reproducibility;Python", "DOI": "10.5334/jors.365", "PubYear": 2021, "Volume": "9", "Issue": "1", "JournalId": 15783, "JournalTitle": "Journal of Open Research Software", "ISSN": "", "EISSN": "2049-9647", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Natural Sciences (Physics), University of Tasmania, Private Bag 37, Hobart, 7001"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Natural Sciences (Physics), University of Tasmania, Private Bag 37, Hobart, 7001"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Natural Sciences (Physics), University of Tasmania, Private Bag 37, Hobart, 7001; and Institute for Marine and Antarctic Studies, University of Tasmania, Private Bag 129, Hobart, TAS 7001"}], "References": []}, {"ArticleId": 91004806, "Title": "Adaptive hough transform with optimized deep learning followed by dynamic time warping for hand gesture recognition", "Abstract": "<p>Hand gesture is a natural interaction method, and hand gesture recognition is familiar in human–computer interaction. Yet, the variations, as well as the complexity of hand gestures such as self-structural characteristics, views, and illuminations, made hand gesture recognition as a challenging task. Nowadays, the human–computer interaction area enhancement leads to putting interest in the dynamic hand gesture segmentation based on the gesture recognition system. Apart from the lengthy clinical success, dynamic hand gesture segmentation through webcam vision seems challenging due to the light effects, partial occlusion, and complicated environment. Hence, to segment the entire hand gesture region and enhance the segmentation accuracy, this paper develops an improved segmentation and deep learning-based strategy for dynamic hand gesture recognition. The data is gathered from the ISL benchmark dataset that consists of both static as well as dynamic images. The initial process of the proposed model is the pre-processing, which is being performed by grey scale conversion and histogram equalization. Further, the segmentation of gestures is done by the novel Adaptive Hough Transform (AHT), where the theta angle is tuned. Once the segmentation of gestures is done, the optimized Deep Convolutional Neural Network (Deep CNN) is used for gesture recognition. The learning rate, epoch count, and hidden neurons are tuned by the same heuristic concept. As the main contribution, the segmentation and classification are enhanced by the hybridization of Electric Fish Optimization (EFO), and Whale Optimization Algorithm (WOA) called Electric Fish-based Whale Optimization Algorithm (E-WOA). The training of optimized Deep CNN is handled by Dynamic Time Warping (DTW) for avoiding redundant frames, thus enhancing the performance of dynamic hand gestures. Quantitative measurement is accomplished for evaluating hand gesture segmentation and recognition, which portrays the superior behaviour of the proposed model.</p>", "Keywords": "Dynamic and static hand gestures; Hand gesture recognition; Adaptive hough transform; Deep convolutional neural network; Electric fish-based whale optimization algorithm; Dynamic time warping", "DOI": "10.1007/s11042-021-11469-9", "PubYear": 2022, "Volume": "81", "Issue": "2", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics & Communication Engineering Department, MIT World Peace University, Pune, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics & Communication Engineering Department, MIT World Peace University, Pune, India"}], "References": [{"Title": "Electric fish optimization: a new heuristic algorithm inspired by electrolocation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11543", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A novel hybrid bidirectional unidirectional LSTM network for dynamic hand gesture recognition with Leap Motion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "", "Page": "100373", "JournalTitle": "Entertainment Computing"}, {"Title": "Gesture recognition in somatosensory game via Kinect sensor", "Authors": "<PERSON>v", "PubYear": 2023, "Volume": "6", "Issue": "5", "Page": "e311", "JournalTitle": "Internet Technology Letters"}]}, {"ArticleId": 91004819, "Title": "Financial Credit Risk Control Strategy Based on Weighted Random Forest Algorithm", "Abstract": "<p>In order to improve the effectiveness of financial credit risk control, a financial credit risk control strategy based on weighted random forest algorithm is proposed. The weighted random forest algorithm is used to classify the financial credit risk data, construct the evaluation index system, and use the analytic hierarchy process to evaluate the financial credit risk level. The targeted risk control strategies are taken according to different risk assessment results. We compared the proposed method with two other methods, and the experimental results show that the proposed method has higher classification accuracy of financial credit data and the risk assessment threshold is basically consistent with the actual results.</p>", "Keywords": "", "DOI": "10.1155/2021/6276155", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Business School, Hunan International Economics University, Hunan, Changsha 410205, China"}], "References": [{"Title": "A Comparative Assessment of Credit Risk Model Based on Machine Learning ——a case study of bank loan data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "174", "Issue": "", "Page": "141", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": ********, "Title": "A Review on Performance Analysis of PDE based Anisotropic Diffusion Approaches for Image Enhancement", "Abstract": "Partial differential equation based anisotropic diffusion techniques are used extensively in computer vision for image enhancement and edge detection.  Anisotropic Diffusion which is found to be a low computational complexity approach which has overcome the undesirable effects of linear smoothing filters and now is popular in prominent research areas of enhancing the quality of low contrast images and speckle noise reduction from geological, industrial and medical images. This paper presents a comprehensive survey on various state-of-the-art anisotropic diffusion techniques for image enhancement. The capability of anisotropic diffusion for enhancing the quality of low contrast images and speckle noise reduction from medical images are further explored. Various objective image quality measures are studied which are used to validate the performance of enhancement approaches. The major research issuesand possible future scopes in this diffusion filtering approach are also discussed.", "Keywords": "Anisotropic diffusion; Edge preservation; Image de-noising; Image enhancement", "DOI": "10.31449/inf.v45i6.3333", "PubYear": 2021, "Volume": "45", "Issue": "6", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jaypee University of Information Technology, Solan, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jaypee University of Information Technology, Solan, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Jaypee University of Information Technology, Solan, India"}], "References": []}, {"ArticleId": 91005023, "Title": "Study on Customized Shuttle Transit Mode Responding to Spatiotemporal Inhomogeneous Demand in Super-Peak", "Abstract": "<p>Instantaneous mega-traffic flow has long been one of the major challenges in the management of mega-cities. It is difficult for the public transportation system to cope directly with transient mega-capacity flows, and the uneven spatiotemporal distribution of demand is the main cause. To this end, this paper proposed a customized shuttle bus transportation model based on the “boarding-transfer-alighting” framework, with the goal of minimizing operational costs and maximizing service quality to address mega-transit demand with uneven spatiotemporal distribution. The fleet application is constructed by a pickup and delivery problem with time window and transfer (PDPTWT) model, and a heuristic algorithm based on Tabu Search and ALNS is proposed to solve the large-scale computational problem. Numerical tests show that the proposed algorithm has the same accuracy as the commercial solution software, but has a higher speed. When the demand size is 10, the proposed algorithm can save 24,000 times of time. In addition, 6 reality-based cases are presented, and the results demonstrate that the designed option can save 9.93% of fleet cost, reduce 45.27% of vehicle waiting time, and 33.05% of passenger waiting time relative to other existing customized bus modes when encountering instantaneous passenger flows with time and space imbalance.</p>", "Keywords": "shuttle transit service; pickup and delivery with time windows and transfers; super-peak; instantaneous demand shuttle transit service ; pickup and delivery with time windows and transfers ; super-peak ; instantaneous demand", "DOI": "10.3390/info12100429", "PubYear": 2021, "Volume": "12", "Issue": "10", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Traffic and Transportation, Beiing Jiaotong University, No. 3 Shang Yuan Cun, Hai Dian District, Beijing 100044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Traffic and Transportation, Beiing Jiaotong University, No. 3 Shang Yuan Cun, Hai Dian District, Beijing 100044, China"}, {"AuthorId": 3, "Name": "Jun<PERSON> Chen", "Affiliation": "School of Traffic and Transportation, Beiing Jiaotong University, No. 3 Shang Yuan Cun, Hai Dian District, Beijing 100044, China ↑ Author to whom correspondence should be addressed. Academic Editors: <PERSON><PERSON><PERSON> and <PERSON><PERSON>"}], "References": []}, {"ArticleId": 91005053, "Title": "A fourth-order compact difference method for the nonlinear time-fractional fourth-order reaction–diffusion equation", "Abstract": "<p>In this paper, a high-order compact scheme is proposed for solving two-dimensional nonlinear time-fractional fourth-order reaction-diffusion equations. The fractional derivative is the Caputo fractional derivative. A scheme with the second-order accuracy is applied to deal with the time derivative, while the spatial derivatives are discretized by the fourth-order compact numerical differential formulas. The unique solvability of the numerical method is proved in detail. Then by using the energy method, it is proved that the proposed algorithm is convergent with order (O({\tau ^2} + h_1^4 + h_2^4)) , where (\tau) is the temporal step size and (h_1) , (h_2) are the spatial step sizes. Finally, some numerical examples are given to verify the theoretical analysis and efficiency of the developed scheme.</p>", "Keywords": "Biharmonic equation; Caputo fractional derivative; High-order finite difference method; Error analysis; 65M06; 65M12", "DOI": "10.1007/s00366-021-01524-2", "PubYear": 2023, "Volume": "39", "Issue": "2", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, <PERSON><PERSON> Teacher Training University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Applied Mathematics, Faculty of Basic Sciences, Sahand University of Technology, Tabriz, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Faculty of Mathematics and Computer Science, Amirkabir University of Technology, Tehran, Iran"}], "References": [{"Title": "Direct meshless local <PERSON><PERSON> (DMLPG) method for time-fractional fourth-order reaction–diffusion problem on complex domains", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "3", "Page": "876", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "Superconvergence of $$C^0$$-$$Q^k$$ Finite Element Method for Elliptic Equations with Approximated Coefficients", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Mixed element algorithm based on a second-order time approximation scheme for a two-dimensional nonlinear time fractional coupled sub-diffusion model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "1", "Page": "51", "JournalTitle": "Engineering with Computers"}, {"Title": "Graded mesh discretization for coupled system of nonlinear multi-term time-space fractional diffusion equations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "2", "Page": "1351", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": 91005079, "Title": "Concentration modulated vanadium oxide nanostructures for NO2 gas sensing", "Abstract": "Vanadium Oxide (V<sub>2</sub>O<sub>5</sub>) nanostructures have been prepared via a simple and inexpensive hydrothermal method using ammonium metavanadate as a vanadium precursor along with oxalic acid. Present work demonstrates the effect of concentration (0.1–0.4 M) variation on gas sensing performance of hydrothermally prepared vanadium pentoxide. The structural, morphological, functional group, and optical properties of V<sub>2</sub>O<sub>5</sub> nanostructure have been investigated by using different characterization techniques like X-Ray Diffraction (XRD), Scanning Electron Microscopy (SEM), Fourier Transform Infra-Red (FTIR) Spectroscopy, Raman spectroscopy, and Diffused Reflectance Spectroscopy (DRS) studies respectively. The structural analysis revealed orthorhombic V<sub>2</sub>O<sub>5</sub> phase formation with the c-axis orientation along (001) plane. The SEM images revealed platelet-like nanostructures, agglomerated nano structure, and clusters of elongated nano structure . The variation in band gap from 2.06 to 2.14 eV is observed with a change in precursor conc. The BET study indicates good specific surface area and optimum pore diameter. The Photoluminescence shows a centered peak arising at characteristic wavelength 709.25 nm and some other peaks observed at 540.91 nm, 470.86 nm and 391.50 nm which is good for gas sensing application. The sensing selectivity was high for NO<sub>2</sub> gas detection at 150 °C. All samples revealed good response with fast response and recovery time. Sample prepared with 0.3 M concentration exhibited the best response attributed to optimum pore size and high surface area which governs superior sensing features. The response of V<sub>2</sub>O<sub>5</sub> is found to be 13% towards 100 ppm NO<sub>2</sub> gas, while response and recovery time is 4 and 55 s respectively. The present work depicts the results of NO<sub>2</sub> gas sensing with fast response and recovery time at relatively low working temperature (150 ℃). Hence it explores the use of this material as a potential candidature for the fabrication of vanadium oxide-based gas sensors.", "Keywords": "Hydrothermal method ; Vanadium oxide ; NO<sub>2</sub> gas ; Gas sensor", "DOI": "10.1016/j.snb.2021.130947", "PubYear": 2022, "Volume": "351", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Yashwantrao Chavan Institute of Science, Satara, Maharashtra 415 001, India"}, {"AuthorId": 2, "Name": "K.B. Pi<PERSON>", "Affiliation": "Department of Physics, Yashwantrao Chavan Institute of Science, Satara, Maharashtra 415 001, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Yashwantrao Chavan Institute of Science, Satara, Maharashtra 415 001, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Thin Film Materials Laboratory, Department of Physics, Shivaji University, Kolhapur, Maharashtra 416 004, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Arts, Science and Commerce College, Ramanandnagar, Sangli, Maharashtra 416 308, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Smt. Kasturbai Walchand College, Sangli, Maharashtra 416 416, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Smt. <PERSON>st<PERSON><PERSON><PERSON> Walchand College, Sangli, Maharashtra 416 416, India;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Thin Film Materials Laboratory, Department of Physics, Shivaji University, Kolhapur, Maharashtra 416 004, India;Corresponding authors"}], "References": [{"Title": "Shape-controlled and stable hollow frame structures of SnO and their highly sensitive NO2 gas sensing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "340", "Issue": "", "Page": "129940", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "How femtosecond laser irradiation can affect the gas sensing behavior of SnO2 nanowires toward reducing and oxidizing gases", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; Hyoungwon <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "342", "Issue": "", "Page": "130036", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 91005123, "Title": "GM FASST: General Method for Labeling Augmented Sub-sampled Images from a Small Data Set for Transfer Learning", "Abstract": "In this work, the use of transfer learning was explored to achieve the overall goal of classifying a small number (approx. 215 images) of Nonlinear Multiphoton Multimodal Microscopy Images of unstained oral cancer biopsies into three categories—healthy, inflammatory, and cancerous. This is achieved by first training a neural network model to detect basic histological components of human tissues, such as the stroma and mucosa, from a much larger (5000 images) Kaggle data set containing images of stained human colorectal cancer biopsies. Experiments were conducted to optimize the model’s architecture and hyperparameters, i.e., hidden layers, optimizers, and API callback functions used prior to retraining the model on a new and much smaller data set consisting of 215 Nonlinear Multiphoton Multimodal Microscopy Images. In addition to having different class labels, these images were acquired using an entirely different imaging and detection set up, and thus have different features than the Kaggle data set. In order to expand the limited size of the Nonlinear Multiphoton Multimodal Microscopy Images data set; tiling methods were used to sub-sample and augment the images though standard transforms such as rotation, scaling and mirroring. This research shows transfer learning and data set re-sampling can improve classification accuracy by 10% over training on the smaller data set alone.", "Keywords": "Machine learning ; Transfer learning ; Image classification", "DOI": "10.1016/j.mlwa.2021.100168", "PubYear": 2021, "Volume": "6", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Chemistry, 578 S Shaw Ln, East Lansing, 48824, MI, USA;Department of Computational Mathematics, Science and Engineering, 428 S Shaw Ln, East Lansing, 48824, MI, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computational Mathematics, Science and Engineering, 428 S Shaw Ln, East Lansing, 48824, MI, USA"}], "References": []}, {"ArticleId": 91005133, "Title": "Algebraic Fault Analysis of SHA-256 Compression Function and Its Application", "Abstract": "<p>Cryptographic hash functions play an essential role in various aspects of cryptography, such as message authentication codes, pseudorandom number generation, digital signatures, and so on. Thus, the security of their hardware implementations is an important research topic. <PERSON><PERSON> et al. proposed an algebraic fault analysis (AFA) for the SHA-256 compression function in 2014. They showed that one could recover the whole of an unknown input of the SHA-256 compression function by injecting 65 faults and analyzing the outputs under normal and fault injection conditions. They also presented an almost universal forgery attack on HMAC-SHA-256 using this result. In our work, we conducted computer experiments for various fault-injection conditions in the AFA for the SHA-256 compression function. As a result, we found that one can recover the whole of an unknown input of the SHA-256 compression function by injecting an average of only 18 faults on average. We also conducted an AFA for the SHACAL-2 block cipher and an AFA for the SHA-256 compression function, enabling almost universal forgery of the chopMD-MAC function.</p>", "Keywords": "algebraic fault analysis; SHA-256 compression function; SAT solver; MAC function algebraic fault analysis ; SHA-256 compression function ; SAT solver ; MAC function", "DOI": "10.3390/info12100433", "PubYear": 2021, "Volume": "12", "Issue": "10", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, University of Fukui, Fukui 910-8507, Japan Tokai Rika Co., Ltd., Oguchi 480-0195, Japan Faculty of Engineering, University of Fukui, Fukui 910-8507, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, University of Fukui, Fukui 910-8507, Japan Tokai Rika Co., Ltd., Oguchi 480-0195, Japan Faculty of Engineering, University of Fukui, Fukui 910-8507, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, University of Fukui, Fukui 910-8507, Japan Tokai Rika Co., Ltd., Oguchi 480-0195, Japan Faculty of Engineering, University of Fukui, Fukui 910-8507, Japan↑Author to whom correspondence should be addressed. Academic Editor: <PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": 91005178, "Title": "Towards Edge Computing Using Early-Exit Convolutional Neural Networks", "Abstract": "<p>In computer vision applications, mobile devices can transfer the inference of Convolutional Neural Networks (CNNs) to the cloud due to their computational restrictions. Nevertheless, besides introducing more network load concerning the cloud, this approach can make unfeasible applications that require low latency. A possible solution is to use CNNs with early exits at the network edge. These CNNs can pre-classify part of the samples in the intermediate layers based on a confidence criterion. Hence, the device sends to the cloud only samples that have not been satisfactorily classified. This work evaluates the performance of these CNNs at the computational edge, considering an object detection application. For this, we employ a MobiletNetV2 with early exits. The experiments show that the early classification can reduce the data load and the inference time without imposing losses to the application performance.</p>", "Keywords": "deep neural networks; early-exit neural networks; model partitioning; cloud offloading; cloud computing; edge computing deep neural networks ; early-exit neural networks ; model partitioning ; cloud offloading ; cloud computing ; edge computing", "DOI": "10.3390/info12100431", "PubYear": 2021, "Volume": "12", "Issue": "10", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Grupo de Teleinformática e Automação (GTA), PEE/COPPE-DEL/Poli, Universidade Federal do Rio de Janeiro (UFRJ), Rio de Janeiro 21941-972, Brazil ↑ Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Grupo de Teleinformática e Automação (GTA), PEE/COPPE-DEL/Poli, Universidade Federal do Rio de Janeiro (UFRJ), Rio de Janeiro 21941-972, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Grupo de Teleinformática e Automação (GTA), PEE/COPPE-DEL/Poli, Universidade Federal do Rio de Janeiro (UFRJ), Rio de Janeiro 21941-972, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Grupo de Teleinformática e Automação (GTA), PEE/COPPE-DEL/Poli, Universidade Federal do Rio de Janeiro (UFRJ), Rio de Janeiro 21941-972, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Grupo de Teleinformática e Automação (GTA), PEE/COPPE-DEL/Poli, Universidade Federal do Rio de Janeiro (UFRJ), Rio de Janeiro 21941-972, Brazil"}], "References": [{"Title": "Efficient adaptive inference for deep convolutional neural networks using hierarchical early exits", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107346", "JournalTitle": "Pattern Recognition"}, {"Title": "A survey on deep learning for challenged networks: Applications and trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "194", "Issue": "", "Page": "103213", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 91005264, "Title": "Correction to: Analysis and optimization of gear skiving parameters regarding interference and theoretical machining deviation based on chaos map", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-021-08232-7", "PubYear": 2021, "Volume": "117", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of High Speed Cutting and Precision Machining, Tianjin University of Technology and Education, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of High Speed Cutting and Precision Machining, Tianjin University of Technology and Education, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Mechanism and Equipment Design of Ministry of Education, Tianjin University, Tianjin, China"}], "References": []}, {"ArticleId": 91005296, "Title": "Accelerated Additive Schwarz Methods for Convex Optimization with Adaptive Restart", "Abstract": "<p>Based on an observation that additive <PERSON><PERSON>rz methods for general convex optimization can be interpreted as gradient methods, we propose an acceleration scheme for additive Schwarz methods. Adopting acceleration techniques developed for gradient methods such as momentum and adaptive restarting, the convergence rate of additive <PERSON><PERSON>rz methods is greatly improved. The proposed acceleration scheme does not require any a priori information on the levels of smoothness and sharpness of a target energy functional, so that it can be applied to various convex optimization problems. Numerical results for linear elliptic problems, nonlinear elliptic problems, nonsmooth problems, and nonsharp problems are provided to highlight the superiority and the broad applicability of the proposed scheme.</p>", "Keywords": "Additive Schwarz method; Acceleration; Adaptive restart; Convex optimization; 65N55; 65B99; 65K15; 90C25", "DOI": "10.1007/s10915-021-01648-z", "PubYear": 2021, "Volume": "89", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "Jongho Park", "Affiliation": "Natural Science Research Institute, KAIST, Daejeon, Korea"}], "References": []}, {"ArticleId": 91005319, "Title": "Blockchain and Recordkeeping: Editorial", "Abstract": "<p>Distributed ledger technologies (DLT), including blockchains, combine the use of cryptography and distributed networks to achieve a novel form of records creation and keeping designed for tamper-resistance and immutability. Over the past several years, these capabilities have made DLTs, including blockchains, increasingly popular as a general-purpose technology used for recordkeeping in a variety of sectors and industry domains, yet many open challenges and issues, both theoretical and applied, remain. This editorial introduces the Special Issue of Computers focusing on exploring the frontiers of blockchain/distributed ledger technology and recordkeeping.</p>", "Keywords": "blockchain; distributed ledger technology; records; recordkeeping; records management; computational archival science blockchain ; distributed ledger technology ; records ; recordkeeping ; records management ; computational archival science", "DOI": "10.3390/computers10110135", "PubYear": 2021, "Volume": "10", "Issue": "11", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "Victoria L<PERSON>", "Affiliation": "School of Information, The University of British Columbia, Vancouver, BC V6T 1Z4, Canada"}], "References": [{"Title": "Understanding the Blockchain Oracle Problem: A Call for Action", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "11", "Page": "509", "JournalTitle": "Information"}, {"Title": "Prototyping a Smart Contract Based Public Procurement to Fight Corruption", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "7", "Page": "85", "JournalTitle": "Computers"}, {"Title": "Research and Development of Blockchain Recordkeeping at the National Archives of Korea", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "8", "Page": "90", "JournalTitle": "Computers"}, {"Title": "Distributed Interoperable Records: The Key to Better Supply Chain Management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "7", "Page": "89", "JournalTitle": "Computers"}, {"Title": "Digital Archives Relying on Blockchain: Overcoming the Limitations of Data Immutability", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "8", "Page": "91", "JournalTitle": "Computers"}, {"Title": "Using Blockchain to Ensure Trust between Donor Agencies and NGOs in Under-Developed Countries", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "8", "Page": "98", "JournalTitle": "Computers"}, {"Title": "Betraying Blockchain: Accountability, Transparency and Document Standards for Non-Fungible Tokens (NFTs)", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "9", "Page": "358", "JournalTitle": "Information"}]}, {"ArticleId": ********, "Title": "Robust model predictive kinematic tracking control with terminal region for wheeled robotic systems", "Abstract": "This paper addresses the nonlinear model predictive control (MPC) for wheeled mobile robots (WMRs) under external disturbance. The decoupling technique is utilized based on the non-holonomic constraint description for separating the WMR model. This method is able to achieve the under-actuated kinematic sub-system without disturbance and fully-actuated dynamic sub-system in presence of disturbance. Thanks to the decoupling technique, the disturbance is lumped into dynamic sub-system. The novelty lies in that the MPC-based tracking control with fixed initial point guarantees the stability based on a new establishment of terminal region and equivalent terminal controller. The feasibility problem is demonstrated to lead the tracking problem using theoretical analysis. Moreover, the control structure is inserted more the robust nonlinear dynamic controller. The effectiveness and advantages of the proposed control scheme are verified by numerical simulations using Yamip tool.", "Keywords": "Robust model predictive control ; wheeled mobile robots ; terminal controller ; terminal region ; feasibility", "DOI": "10.1080/00051144.2021.1991148", "PubYear": 2021, "Volume": "62", "Issue": "3-4", "JournalId": 7651, "JournalTitle": "Automatika", "ISSN": "0005-1144", "EISSN": "1848-3380", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hanoi University of Science and Technology, Hanoi, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Thai Nguyen University of Technology, 251750 Thai Nguyen City, Vietnam"}], "References": []}, {"ArticleId": 91005488, "Title": "Разработка алгоритма приведения двухуровневой кооперативной задачи о назначениях к задаче коммивояжера", "Abstract": "", "Keywords": "", "DOI": "10.15827/2311-6749.20.3.1", "PubYear": 2020, "Volume": "3", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON>е<PERSON><PERSON><PERSON><PERSON>а", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Г.В<PERSON> <PERSON>азу<PERSON>овский", "Affiliation": ""}, {"AuthorId": 4, "Name": "G.V<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 91005540, "Title": "Нейросетевые модели распознавания элементов сложных конструкций в системах компьютерного зрения", "Abstract": "", "Keywords": "", "DOI": "10.15827/2311-6749.20.4.1", "PubYear": 2020, "Volume": "1", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Е.С. Агешин", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 91005728, "Title": "Single Parameter Model for Cosmic Scale Photon Redshift in a Closed Universe", "Abstract": "A successful single parameter model has been formulated to match the observations of photons from type 1a supernovae which were previously used to corroborate the standard ?? cold dark matter model. The new single parameter model extrapolates all the way back to the cosmic background radiation (CMB) without requiring a separate model to describe inflation of the space dimensions after the Big Bang. This single parameter model assumes that spacetime forms a finite symmetrical manifold with positive curvature. For the spacetime manifold to be finite, the time dimension must also have positive curvature. This model was formulated to consider whether the curvature of the time dimension may be related to the curvature of the space dimensions. This possibility is not considered in the more complex models previously used to fit the available redshift data. The geometry for the finite spacetime manifold was selected to be compatible with the Friedmann equation with positive curvature. The manifold shape was motivated by an assumption that there exists a matter hemisphere (when considering time together with a single space dimension) and an antimatter hemisphere to give a symmetrical and spherical overall spacetime manifold. Hence, the space dimension expands from a pole to the equator, at a maximum value for the time dimension. This is analogous to the expansion of a circle of latitude on a globe from a pole to the equator. The three space dimensions are identical so that any arbitrary single space direction may be selected. The initial intention was to modify the assumed geometry for the spacetime manifold to account for the presence of matter. It was surprisingly found that, within the error of the reported measurements, no further modification was necessary to fit the data. The Friedmann equation reduces to the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> equation at the equator so this can be used to predict the total amount of mass in the Universe. The resulting prediction is of the order of 1051 kg. The corresponding density of matter at the current time is approximately 1.6 × 10-28 kg·m-3.", "Keywords": "Photon Redshift;Cold Dark Matter Model;Single Parameter Model;Cosmic Background Radiation", "DOI": "10.4236/ojmsi.2021.94026", "PubYear": 2021, "Volume": "9", "Issue": "4", "JournalId": 32695, "JournalTitle": "Open Journal of Modelling and Simulation", "ISSN": "2327-4018", "EISSN": "2327-4026", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Andre <PERSON>nberg Consulting LLC, Dublin, OH, USA"}], "References": []}, {"ArticleId": ********, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0304-3975(21)00618-6", "PubYear": 2021, "Volume": "892", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 91005922, "Title": "Research on adaptive transmission and controls of COVID-19 on the basis of a complex network", "Abstract": "<p>COVID-19 has caused massive disruption on the global economy and presents a considerable risk to human lives. Some countries have successfully controlled the pandemic by adopting strict measures, such as lockdown and travel restriction, but such methods are difficult to be applied widely due to their huge costs. To explore available and low-cost solutions, this study proposes an adaptive transmission model on the basis of a complex network, and gives control simulation method of COVID-19. The suggested model considers adaptive changes such as travel network and people's travel intention to form a three-level adaptive network transmission model among cities, communities, and people. The improved susceptible-exposed-infectious-recovered-dead transmission process is integrated into the network. Simulation experiments under high-, low-, and conventional-cost controls are performed. In these experiments, the travel restriction and closing cities are considered, and sensitivity analyses of the parameters are conducted to explore low-cost measures. Meanwhile, time duration and application conditions of different controls are discussed. Results show that lockdown is the most effective way, and the contact and infection rates are the two most important factors to control the pandemic. Low-cost combined control measures are feasible and effective for most countries. Finally, several suggestions are given for national and urban preventions and controls of COVID-19 and other infectious diseases in the future.</p><p>© 2021 Elsevier Ltd. All rights reserved.</p>", "Keywords": "Adaptive network transmission model;COVID-19;Complex network;Control measures;Transmission dynamics", "DOI": "10.1016/j.cie.2021.107749", "PubYear": 2021, "Volume": "162", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, School of Management, Xi'an Jiaotong University, Xi'an, Shaanxi 710049, China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, School of Management, Xi'an Jiaotong University, Xi'an, Shaanxi 710049, China. ;Key Laboratory of Process Control &amp; Efficiency Engineering (Xi'an Jiaotong University), Ministry of Education, Xi'an, Shaanxi 710049, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Xi'an Jiaotong University, Xi'an, Shaanxi 710049, China. ;Department of Manufacturing Automation, School of Construction Machinery, Chang'an University, Xi'an, Shaanxi 710064, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, School of Management, Xi'an Jiaotong University, Xi'an, Shaanxi 710049, China."}], "References": [{"Title": "Designing robust policies under deep uncertainty for mitigating epidemics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106221", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 91006079, "Title": "Effects of information sharing, decision synchronization and goal congruence on SC performance", "Abstract": "Nowadays, supply chain performance is measured since it is essential in globalized companies as maquiladoras. They import all raw material and import and export all final products, with high information, material, and money flow, having complex networks. This article reports findings from a structural equation model integrating four latent variables: Decision synchronization, Goal congruence, and Information sharing as independent variables, and Supply chain performance as the dependent variable related through six hypotheses to know their relationship. Hypotheses were tested using information from 143 responses to a questionnaire applied to the maquiladora industry in northern Mexico. The structural equation model is evaluated using the partial least squares (PLS) method integrated into WarpPLS 6.0 software. Findings indicate that five hypotheses are statistically significant, and it is concluded that Goal congruence, Information sharing, and Decision synchronization directly affect supply chain performance. The most critical variable to guarantee it is Goal congruence among partners.", "Keywords": "Supply chain management ; SC financial performance ; Information sharing ; Goal congruence", "DOI": "10.1016/j.cie.2021.107744", "PubYear": 2021, "Volume": "162", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Manufacturing Engineering, Autonomous University of Ciudad Juárez, Av. del Charro 450 Norte, Ciudad Juárez, Chihuahua, Mexico;Division of Research and Postgraduate Studies, Tecnológico Nacional de México/Instituto Tecnológico de Ciudad Juárez, Av. Tecnológico No. 1340 Fraccionamiento El Crucero, Ciudad Juárez 32500, Chihuahua, Mexico;Corresponding author at: Department of Industrial and Manufacturing Engineering, Autonomous University of Ciudad Juarez, Av. del Charro 450 Norte, Ciudad Juárez, Chihuahua, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Research and Postgraduate Studies, Tecnológico Nacional de México/Instituto Tecnológico de Ciudad Juárez, Av. Tecnológico No. 1340 Fraccionamiento El Crucero, Ciudad Juárez 32500, Chihuahua, Mexico"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Higher School of Engineering and Technology, International University of La Rioja (UNIR), Avda. de la Paz, 137, Logroño, La Rioja, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, University of La Rioja, Luis de Ulloa 20, 26004 Logroño, La Rioja, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of La Rioja, Luis de Ulloa 20, Logroño 26004, La Rioja, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Zaragoza. Edif. <PERSON><PERSON><PERSON><PERSON>, María de Luna, s/n, 50018, Zaragoza, Spain"}], "References": [{"Title": "Synchromodal transportation planning using travel time information", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "103367", "JournalTitle": "Computers in Industry"}]}, {"ArticleId": 91006097, "Title": "Subjective markers of successful aging and change in Internet use among older adults: The distinctive role of subjective health", "Abstract": "Lower rates of Internet adoption among older adults pose significant challenges in delivering important online services to older adults. Addressing the age-related digital divide requires determining factors that can influence Internet use in aging and may be targeted for intervention. Candidate factors include self-perceptions associated with successful aging, but prior research has not determined whether changes in self-perceptions are related to changes in Internet use within individuals. The present study examined the relationship between self-perception measures (subjective age, subjective health, and life satisfaction) and Internet use among older adults, using longitudinal data from the Health and Retirement Study. Results indicated a selectively robust relationship between Internet use and better subjective health among older Americans. Further, these relationships were not altered by changes in technology adoption over time. Finally, longitudinal data over eight years revealed that change in Internet use was selectively associated with changes in subjective health. Together, these results indicate that among self-perception measures of successful aging, subjective health has a robust relationship with both current Internet use and changes in Internet use over time among older Americans. Such findings suggest that effective interventions to increase digital technology utilization likely require accommodations for older adults with poor subjective health.", "Keywords": "Internet use ; Subjective health ; Subjective age ; Life satisfaction ; Self-perceptions ; Aging", "DOI": "10.1016/j.chb.2021.107064", "PubYear": 2022, "Volume": "127", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "Xiao<PERSON> Wan", "Affiliation": "Department of Psychology, University of Central Florida, 4111 Pictor Lane Orlando, FL, 32816, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychology, University of Central Florida, 4111 Pictor Lane Orlando, FL, 32816, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, University of Central Florida, 4111 Pictor Lane Orlando, FL, 32816, USA"}], "References": [{"Title": "Cognitive, social, emotional, and subjective health benefits of computer use in adults: A 9-year longitudinal study from the Midlife in the United States (MIDUS)", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "106179", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 91006119, "Title": "A process-oriented probabilistic linguistic decision-making model with unknown attribute weights", "Abstract": "As an effective tool to describe qualitative evaluations, probabilistic linguistic term set (PLTS) can identify the different preference degrees for the possible linguistic evaluations. For the multi-attribute decision making (MADM) problems based on the PLTSs, making decisions is not instantaneous behavior but needs some time to complete information processing. Considering the dynamic nature of decision-making behavior, this study aims to develop a process-oriented probabilistic linguistic decision-making framework. First, we introduce the parameters in the probabilistic linguistic multi-alternative decision field theory (PLMDFT) model. An improved decision rule for selecting the optimal alternative(s) is also presented. Then, a deviation entropy-based model is developed to determine attribute weights. Furthermore, we construct a probabilistic linguistic decision-making framework based on the PLMDFT and deviation entropy. Finally, the constructed framework is applied to solve emergency scheme selection problem. Some discussion and comparative analysis is complemented to demonstrate the validity of the proposed framework.", "Keywords": "Probabilistic linguistic term set ; Decision field theory ; Deviation entropy-based model ; Process-oriented decision making", "DOI": "10.1016/j.knosys.2021.107594", "PubYear": 2022, "Volume": "235", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Chengdu University, Chengdu, Sichuan 610106, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Unit 31431 of PLA, Shenyang, Liaoning 110031, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu, Sichuan 610064, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Manchester, Manchester M13 9PL, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, Chengdu University, Chengdu, Sichuan 610106, China"}], "References": [{"Title": "Extended decision field theory with social-learning for long-term decision-making processes in social networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1293", "JournalTitle": "Information Sciences"}, {"Title": "Trust modeling based on probabilistic linguistic term sets and the MULTIMOORA method", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113817", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dynamic assessment of Internet public opinions based on the probabilistic linguistic Bayesian network and Prospect theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107359", "JournalTitle": "Applied Soft Computing"}, {"Title": "An Overview of Studies Based on the Probability-Based Decision-Making Information: Current Developments, Methodologies, Applications and Challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "3", "Page": "1253", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 91006216, "Title": "Self-adaptive mobile web service discovery framework for Dynamic Mobile Environment", "Abstract": "This paper proposes a self-adaptive mobile web service (MWS) discovery framework for a dynamic mobile environment (DME) to deal with MWS proliferation, dynamic context, and irrelevant MWS discovery challenges. The main contribution of this research includes an improvement of the matchmaking algorithm, enhanced MWS categorization approach, and extensible meta-context ontology that represents the context information in DME. This was achieved by enabling the self-adaptive matchmaker to learn MWS relevance using a Modified-Negative Selection Algorithm (M-NSA) and retrieve the most relevant MWS based on the current context of the discovery. To assess the proposed framework, series of experiments was carried out using publicly-available datasets. The performance of the framework is evaluated against the state-of-the-art frameworks. It was found that the proposed framework is more effective and attained better binary and graded relevance when subjected to context variations which are prevalent in DME. This is useful for service-based application designers and other MWS clients.", "Keywords": "Self-adaptive ; Mobile web service ; Service discovery ; Negative Selection Algorithm ; Dynamic Mobile Environment", "DOI": "10.1016/j.jss.2021.111120", "PubYear": 2022, "Volume": "184", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Faculty of Engineering, Universiti Teknologi Malaysia, 81310 UTM, Skudai, Johor, Malaysia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Faculty of Engineering, Universiti Teknologi Malaysia, 81310 UTM, Skudai, Johor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Faculty of Engineering, Universiti Teknologi Malaysia, 81310 UTM, Skudai, Johor, Malaysia"}], "References": []}, {"ArticleId": 91006598, "Title": "Trade-off between accuracy and tractability of Network Calculus in FIFO networks", "Abstract": "Computing accurate deterministic performance bounds is a strong need for communication technologies having stringent requirements on latency and reliability. Within new scheduling protocols such as TSN, the FIFO policy remains at work inside each class of communication. In this paper, we focus on computing deterministic performance bounds in FIFO networks in the Network Calculus framework. We propose a new algorithm based on linear programming that presents a trade-off between accuracy and tractability. This algorithm is first presented for tree networks. Next, we generalize our approach and present a linear program for computing performance bounds for arbitrary topologies, including cyclic dependencies. Finally, we provide numerical results, both of toy examples and realistic topologies, to assess the interest of our approach.", "Keywords": "Network Calculus ; FIFO systems ; Linear programming", "DOI": "10.1016/j.peva.2021.102250", "PubYear": 2022, "Volume": "153", "Issue": "", "JournalId": 4685, "JournalTitle": "Performance Evaluation", "ISSN": "0166-5316", "EISSN": "1872-745X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Huawei Technologies France, 20 quai du Point du Jour, 92100 Boulogne-Billancourt, France"}], "References": [{"Title": "Bounding the delays of the MPPA network-on-chip with network calculus: Models and benchmarks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "102124", "JournalTitle": "Performance Evaluation"}]}, {"ArticleId": 91006723, "Title": "Определение местонахождения игроков в виртуальном футболе", "Abstract": "", "Keywords": "", "DOI": "10.15827/2311-6749.21.2.1", "PubYear": 2021, "Volume": "2", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "Д.<PERSON><PERSON> Петруненко", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "С.А. Беля<PERSON>в", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 91006726, "Title": "A Mobile Bayesian Network Structure Learning Method Using Genetic Incremental K2 Algorithm and Random Attribute Order Technology", "Abstract": "<p>The application of existing datasets to construct a probabilistic network has always been the primary research focus for mobile Bayesian networks, particularly when the dataset size is large. In this study, we improve the K2 algorithm. First, we relax the K2 algorithm requirements for node order and generate the node order randomly to obtain the best result in multiple random node order. Second, a genetic incremental K2 learning method is used to learn the Bayesian network structure. The training dataset is divided into two groups, and the standard K2 algorithm is used to find the optimal value for the first set of training data; simultaneously, three similar suboptimal values are recorded. To avoid falling into the local optimum, these four optimal values are mutated into a new genetic optimal value. When the second set of training data is used, only the best Bayesian network structure within the five abovementioned optimal values is identified. The experimental results indicate that the genetic incremental K2 algorithm based on random attribute order achieves higher computational efficiency and accuracy than the standard algorithm. The new algorithm is especially suitable for building Bayesian network structures in cases where the dataset and number of nodes are large.</p>", "Keywords": "", "DOI": "10.1155/2021/4743752", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Internet of Tings Technology, Wuxi Institute of Technology, Wuxi, Jiangsu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Internet of Tings Technology, Wuxi Institute of Technology, Wuxi, Jiangsu, China;Management & Science University, Shah Alam, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Internet of Tings Technology, Wuxi Institute of Technology, Wuxi, Jiangsu, China"}], "References": [{"Title": "Application of Artificial Intelligence in Precision Marketing: ", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "4", "Page": "209", "JournalTitle": "Journal of Organizational and End User Computing"}, {"Title": "Research and Analysis of an Enterprise E-Commerce Marketing System Under the Big Data Environment", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}]}, {"ArticleId": 91006753, "Title": "Система поддержки принятия решений Decerns-FT для анализа многокритериальных задач в условиях нечеткости", "Abstract": "", "Keywords": "", "DOI": "10.15827/2311-6749.21.1.1", "PubYear": 2021, "Volume": "1", "Issue": "", "JournalId": 19680, "JournalTitle": "International journal \"Programmnye produkty i sistemy\"", "ISSN": "0236-235X", "EISSN": "2311-2735", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>.<PERSON><PERSON>о", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 91006841, "Title": "A three-layer architecture to support disparity map construction in stereo vision systems", "Abstract": "By adequately locating objects of interest, a computer vision-driven machine can avoid obstacles and collisions, set safe paths to move autonomously, and reconstruct the geometric properties of a scene. In this sense, three-dimensional information is required to perform these tasks properly. As digital images provide information about the scene, they are often used to estimate objects’ spatial position and depths concerning image acquisition machinery. Thus, disparity maps can be obtained, and the differences between similar features from two or more images can be used to encode depth information. Although the disparity map construction has been extensively surveyed, some concepts such as code reuse and compartmentalization need further investigation to optimize application development. Therefore, based on the major stages of disparity calculation, we present a framework to support disparity map construction. This study’s main contributions include a delimitation of elements that integrate stereo vision scope and the modeling of an architecture that integrates the main components of disparity calculation. The proposal is evaluated in different scenarios which demand sparse and dense disparity maps. The standard concepts shared between them are discussed and used to implement a multifaceted application, useful in evaluating stereo vision methods and providing disparity maps as inputs for three-dimensional reconstruction and point cloud elaboration. The code prepared by the authors is publicly available <sup>1</sup> .", "Keywords": "Software components ; Image processing ; Active vision ; Computer applications ; Robotics", "DOI": "10.1016/j.iswa.2021.200054", "PubYear": 2021, "Volume": "12", "Issue": "", "JournalId": 89889, "JournalTitle": "Intelligent Systems with Applications", "ISSN": "2667-3053", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal Institute Goiano, Computer Vision Laboratory, Urutaí - GO, Brazil;Federal University of Goiás, Pixellab Laboratory, Goiânia - GO, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Federal Institute Goiano, Computer Vision Laboratory, Urutaí - GO, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Goiás, Pixellab Laboratory, Goiânia - GO, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Federal University of Goiás, Pixellab Laboratory, Goiânia - GO, Brazil"}], "References": []}, {"ArticleId": 91007164, "Title": "Better degree of approximation by modified Bernstein-Durrmeyer type operators", "Abstract": "<p style='text-indent:20px;'>In the present article we investigate a Durrmeyer variant of the generalized Bernstein-operators based on a function <inline-formula><tex-math id=\"M1\">\\begin{document}$ \\tau(x), $\\end{document}</tex-math></inline-formula> where <inline-formula><tex-math id=\"M2\">\\begin{document}$ \\tau $\\end{document}</tex-math></inline-formula> is infinitely differentiable function on <inline-formula><tex-math id=\"M3\">\\begin{document}$ [0, 1], \\; \\tau(0) = 0, \\tau(1) = 1 $\\end{document}</tex-math></inline-formula> and <inline-formula><tex-math id=\"M4\">\\begin{document}$ \\tau^{\\prime }(x)&gt;0, \\;\\forall\\;\\; x\\in[0, 1]. $\\end{document}</tex-math></inline-formula> We study the degree of approximation by means of the modulus of continuity and the Ditzian-Totik modulus of smoothness. A Voronovskaja type asymptotic theorem and the approximation of functions with derivatives of bounded variation are also studied. By means of a numerical example, finally we illustrate the convergence of these operators to certain functions through graphs and show a careful choice of the function <inline-formula><tex-math id=\"M5\">\\begin{document}$ \\tau(x) $\\end{document}</tex-math></inline-formula> leads to a better approximation than the generalized Bernstein-Durrmeyer type operators considered by Kajla and Acar [<xref ref-type=\"bibr\" rid=\"b11\">11</xref>].</p>", "Keywords": "Modulus of continuity", "DOI": "10.3934/mfc.2021024", "PubYear": 2022, "Volume": "5", "Issue": "2", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Şule Yüksel Güngör", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}]