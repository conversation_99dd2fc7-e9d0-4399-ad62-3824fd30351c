
# 设计一个类，

from file_define import TextFileReader, JsonFileReader
from data_define import Record
from pyecharts.charts import Bar
from pyecharts.options import *
from pyecharts.globals import ThemeType

# 读取数据文件
text_file_reader = TextFileReader("D:\黑马程序员\\1、Python快速入门（8天零基础入门到精通）\资料\第13章资料\\2011年1月销售数据.txt")

# 读取数据文件
text_file_reader = TextFileReader("D:\黑马程序员\\1、Python快速入门（8天零基础入门到精通）\资料\第13章资料\\2011年1月销售数据.txt")

# 读取数据文件
text_file_reader = TextFileReader("D:\黑马程序员\\1、Python快速入门（8天零基础入门到精通）\资料\第13章资料\\2011年1月销售数据.txt")
json_file_reader = JsonFileReader("D:\黑马程序员\\1、Python快速入门（8天零基础入门到精通）\资料\第13章资料\\2011年2月销售数据JSON.txt")

# 读取数据
jan_data: list[Record] = text_file_reader.read_data()
feb_data:list[Record] = json_file_reader.read_data()

# 将2月份的数据合并为1个list来存储
all_data = jan_data + feb_data

# 开始进行数据计算
data_dict = {}
for record in all_data:
    if record.date in data_dict:
        data_dict[record.date] += record.money
    else:
        data_dict[record.date] = record.money

# 可视化图表开发
bar = Bar(init_opts=InitOpts(theme=ThemeType.LIGHT))
bar.add_xaxis(list(data_dict.keys()))
bar.add_yaxis("销售额", list(data_dict.values()),label_opts=LabelOpts(False))
bar.set_global_opts(title_opts=TitleOpts(title="2011年1月和2月销售额统计"))

bar.render("每日销售额柱状图.html")
