{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import scipy.stats as ss\n", "import matplotlib\n", "\n", "#解决绘图的兼容问题\n", "%matplotlib inline\n", "matplotlib.rcParams['font.sans-serif'] = ['SimHei']"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Company</th>\n", "      <th>Product</th>\n", "      <th>TypeName</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>ScreenResolution</th>\n", "      <th>CPU_Company</th>\n", "      <th>CPU_Type</th>\n", "      <th>CPU_Frequency (GHz)</th>\n", "      <th>RAM (GB)</th>\n", "      <th>Memory</th>\n", "      <th>GPU_Company</th>\n", "      <th>GPU_Type</th>\n", "      <th>OpSys</th>\n", "      <th>Weight (kg)</th>\n", "      <th>Price (Euro)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Apple</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Ultrabook</td>\n", "      <td>13.3</td>\n", "      <td>IPS Panel Retina Display 2560x1600</td>\n", "      <td>Intel</td>\n", "      <td>Core i5</td>\n", "      <td>2.3</td>\n", "      <td>8</td>\n", "      <td>128GB SSD</td>\n", "      <td>Intel</td>\n", "      <td>Iris Plus Graphics 640</td>\n", "      <td>macOS</td>\n", "      <td>1.37</td>\n", "      <td>1339.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Apple</td>\n", "      <td>Macbook Air</td>\n", "      <td>Ultrabook</td>\n", "      <td>13.3</td>\n", "      <td>1440x900</td>\n", "      <td>Intel</td>\n", "      <td>Core i5</td>\n", "      <td>1.8</td>\n", "      <td>8</td>\n", "      <td>128GB Flash Storage</td>\n", "      <td>Intel</td>\n", "      <td>HD Graphics 6000</td>\n", "      <td>macOS</td>\n", "      <td>1.34</td>\n", "      <td>898.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>HP</td>\n", "      <td>250 G6</td>\n", "      <td>Notebook</td>\n", "      <td>15.6</td>\n", "      <td>Full HD 1920x1080</td>\n", "      <td>Intel</td>\n", "      <td>Core i5 7200U</td>\n", "      <td>2.5</td>\n", "      <td>8</td>\n", "      <td>256GB SSD</td>\n", "      <td>Intel</td>\n", "      <td>HD Graphics 620</td>\n", "      <td>No OS</td>\n", "      <td>1.86</td>\n", "      <td>575.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Apple</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Ultrabook</td>\n", "      <td>15.4</td>\n", "      <td>IPS Panel Retina Display 2880x1800</td>\n", "      <td>Intel</td>\n", "      <td>Core i7</td>\n", "      <td>2.7</td>\n", "      <td>16</td>\n", "      <td>512GB SSD</td>\n", "      <td>AMD</td>\n", "      <td>Radeon Pro 455</td>\n", "      <td>macOS</td>\n", "      <td>1.83</td>\n", "      <td>2537.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Apple</td>\n", "      <td>MacBook Pro</td>\n", "      <td>Ultrabook</td>\n", "      <td>13.3</td>\n", "      <td>IPS Panel Retina Display 2560x1600</td>\n", "      <td>Intel</td>\n", "      <td>Core i5</td>\n", "      <td>3.1</td>\n", "      <td>8</td>\n", "      <td>256GB SSD</td>\n", "      <td>Intel</td>\n", "      <td>Iris Plus Graphics 650</td>\n", "      <td>macOS</td>\n", "      <td>1.37</td>\n", "      <td>1803.60</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Company      Product   TypeName  Inches                    ScreenResolution  \\\n", "0   Apple  MacBook Pro  Ultrabook    13.3  IPS Panel Retina Display 2560x1600   \n", "1   Apple  Macbook Air  Ultrabook    13.3                            1440x900   \n", "2      HP       250 G6   Notebook    15.6                   Full HD 1920x1080   \n", "3   Apple  MacBook Pro  Ultrabook    15.4  IPS Panel Retina Display 2880x1800   \n", "4   Apple  MacBook Pro  Ultrabook    13.3  IPS Panel Retina Display 2560x1600   \n", "\n", "  CPU_Company       CPU_Type  CPU_Frequency (GHz)  RAM (GB)  \\\n", "0       Intel        Core i5                  2.3         8   \n", "1       Intel        Core i5                  1.8         8   \n", "2       Intel  Core i5 7200U                  2.5         8   \n", "3       Intel        Core i7                  2.7        16   \n", "4       Intel        Core i5                  3.1         8   \n", "\n", "                Memory GPU_Company                GPU_Type  OpSys  \\\n", "0            128GB SSD       Intel  Iris Plus Graphics 640  macOS   \n", "1  128GB Flash Storage       Intel        HD Graphics 6000  macOS   \n", "2            256GB SSD       Intel         HD Graphics 620  No OS   \n", "3            512GB SSD         AMD          Radeon Pro 455  macOS   \n", "4            256GB SSD       Intel  Iris Plus Graphics 650  macOS   \n", "\n", "   Weight (kg)  Price (Euro)  \n", "0         1.37       1339.69  \n", "1         1.34        898.94  \n", "2         1.86        575.00  \n", "3         1.83       2537.45  \n", "4         1.37       1803.60  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"D:\\文件文档\\数据集\\laptop_price - dataset.csv\",encoding=\"gbk\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最高价格为：6099.0\n", "最低价格为：174.0\n"]}], "source": ["df_price = df[\"Price (Euro)\"]\n", "print(\"最高价格为：\",end='')\n", "print(df_price.max())\n", "print(\"最低价格为：\",end='')\n", "print(df_price.min())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}