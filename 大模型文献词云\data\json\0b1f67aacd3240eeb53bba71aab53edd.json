[{"ArticleId": 110705949, "Title": "Automated rail-water intermodal transport container terminal handling equipment cooperative scheduling based on bidirectional hybrid flow-shop scheduling problem", "Abstract": "The importance of rail-water intermodal transport is becoming more and more prominent in recent years. As a key node of rail-water intermodal transportation, the operation efficiency of automated container terminal directly affects the overall transportation efficiency. However, there are few studies on the collaborative scheduling of automated container terminal equipment in rail-water intermodal transportation. Therefore, this paper focuses on the ‘Automated Quay Crane (AQC)-Automated Straddle Carrier (ASC)-Automated Rail Mounted Gantry Crane (ARMG)’ operation system and addresses the operational efficiency requirements of the Automated Rail-Water Intermodal Transport Container Terminal (ARWITCT), Based on the bidirectional Hybrid Flow-shop Scheduling Problem (HFSP), a scheduling optimization model for the ARWITCT is established with the objective of minimizing the maximum completion time. To solve this complex scheduling problem, Particle Swarm Optimization based on Adaptive Tent Chaos Mapping (PSO-ATCM) is designed. In this algorithm, a parameter adaptive adjustment strategy and a Tent Chaos Mapping Optimization Strategy are introduced to help PSO jump out of local optimum. Problem-feature-based encoding and decoding methods are designed for the problem. An ‘insertion-translation’ mechanism is used to judge and resolve the conflicts between import and export containers during the decoding process. The effect of proposed scheduling model and the performance of the proposed algorithm are validated via comprehensive comparison experiments. To validate the effectiveness of proposed scheduling model, we compare the unidirectional HFSP model with the bidirectional HFSP model. The proposed PSO-ATCM is compared with standard Genetic Algorithm, Particle Swarm Optimization algorithm and Adaptive Genetic Algorithm. Computational results demonstrate that the proposed model is effective and PSO-ATCM performs better than other three compared algorithms.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109696", "PubYear": 2023, "Volume": "186", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Transportation and Logistics Engineering, Wuhan University of Technology, Wuhan, Hubei 430063, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Transportation and Logistics Engineering, Wuhan University of Technology, Wuhan, Hubei 430063, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Transportation and Logistics Engineering, Wuhan University of Technology, Wuhan, Hubei 430063, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Transportation and Logistics Engineering, Wuhan University of Technology, Wuhan, Hubei 430063, PR China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> Li", "Affiliation": "School of Transportation and Logistics Engineering, Wuhan University of Technology, Wuhan, Hubei 430063, PR China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Transportation and Logistics Engineering, Wuhan University of Technology, Wuhan, Hubei 430063, PR China"}], "References": [{"Title": "Scheduling of container-handling equipment during the loading process at an automated container terminal", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106848", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Optimization of Continuous <PERSON><PERSON> by Taking into Account Double-Line Ship Mooring", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "Optimization for integrated scheduling of intelligent handling equipment with bidirectional flows and limited buffers at automated container terminals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "145", "Issue": "", "Page": "105863", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": *********, "Title": "A novel framework for designing and manufacturing cranial prostheses through incremental sheet metal forming", "Abstract": "<p>This investigation aims to propose a novel framework for designing and manufacturing cranial prostheses through incremental sheet metal forming (ISF). First, the three-dimensional models of the defective crania and the prosthesis were constructed from the CT data. Then, to verify the formability of the prosthesis model, a method for testing the mesh sheet’s incremental forming limit (IFL) was proposed with a variable angle cone model. The angle between the normal vector of the fracture point and the Z-axis calculated the IFL. The tests showed that the method could realize the testing of the IFL of the mesh sheet with the reduction of processing time and cost. In addition, finite element modeling and experimental trials were used to evaluate the ISF process of the prosthesis, including thickness distribution and geometric accuracy. The results showed that the incremental forming can form prosthesis samples with high precision, while the thickness distribution is relatively uniform without excessive thinning. Finally, the obtained prosthesis sample was practically assembled with the cranial defect model to verify the fitting effect and feasibility of the prosthesis formed by the ISF process.</p>", "Keywords": "Cranial prosthesis; Incremental sheet forming; Incremental forming limit; Finite element modeling", "DOI": "10.1007/s00170-023-12581-w", "PubYear": 2023, "Volume": "129", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, College of Engineering, Kharj, Prince <PERSON> Abdulaziz University, Al Kharj, Saudi Arabia; Mechanical Engineering Department, Faculty of Engineering-Helwan, Helwan University, Cairo, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 5, "Name": "Guangcheng Zha", "Affiliation": "School of Materials Science and Engineering, Nanjing Institute of Technology, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "College of Material Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}], "References": []}, {"ArticleId": 110706010, "Title": "SIMPLIFIED ADAPTIVE TRIANGULATION OF THE CONTACT BOUNDARIES OF THE DAM MODEL", "Abstract": "<p>To numerically solve the system of integral equations, it is customary to establish a discrete grid within each integration area. In the context of 3D modeling, these areas correspond to surfaces situated in space. The standard discretization technique employed for the computational domain is triangulation. This study addresses the integral equation system pertinent to the electrical tomography of dams. The structural model encompasses an embankment dam, the upstream and downstream water bodies, the dam's base, and a potential leakage region on the upstream side. An alternative configuration may be encountered in specific scenarios with no water downstream. Consequently, the model may incorporate up to nine distinct contact boundaries. Accordingly, the system of integral equations comprises an equivalent number of equations. Effectively resolving this system through numerical methods necessitates applying triangulation techniques to these diverse surfaces. While mathematical packages like Matlab offer triangulation functions, they may not fully address the specific demands of the problem. Additionally, the grid resolution should be heightened in proximity to key elements such as the sounding line, the supply electrode, and the various contact lines within the medium. These considerations transform the triangulation task into a distinct subtask within the numerical simulation of the resistivity tomography problem. In this paper, we provide our specific approach to this problem. The simplification of the triangulation algorithm is rooted in the predominant utilization of the two-dimensional geometric properties inherent to the object under study. For most contact boundaries, the triangulation is constructed layer by layer with a gradual modulation in triangle dimensions as one progresses from one layer to the next, orthogonal to the axis of the dam. Concerning the surface corresponding to the leakage area, cylindrical coordinates are used for surface parameterization. This approach enables partitioning the surface into discrete strata, facilitating a systematic, layer-by-layer grid construction. Additionally, points at the intersections of contact boundaries are integrated into the pre-existing triangulation by applying a standard function within the Matlab package. In the future, the mathematical modeling based on the Integral Equation Method with adaptive discretization will help incorporate real-time computations into information systems related to monitoring hydraulic structures.</p>", "Keywords": "ERT;dam sounding;integral equations;triangulation;adaptive grid", "DOI": "10.37943/15AHSE8085", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 75769, "JournalTitle": "Scientific Journal of Astana IT University", "ISSN": "2707-9031", "EISSN": "2707-904X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Astana IT University"}], "References": []}, {"ArticleId": 110706043, "Title": "Graph machine learning framework for depicting wavefunction on interface", "Abstract": "<p>The wavefunction, as the basic hypothesis of quantum mechanics, describes the motion of particles and plays a pivotal role in determining physical properties at the atomic scale. However, its conventional acquisition method, such as density functional theory, requires a considerable amount of calculation, which brings numerous problems to wide application. Here, we propose an algorithmic framework based on graph neural network to machine-learn the wavefunction of electrons. This framework primarily generates atomic features containing information about chemical environment and geometric structure and subsequently constructs a scalable distribution map. For the first time, the visualization of wavefunction of interface is realized by machine learning methods, bypassing complex calculation and obscure comprehension. In this way, we vividly illustrate quantum mechanics, which can inspire theoretical exploration. As an intriguing case to verify the ability of our method, a novel quantum confinement phenomenon on interfaces based on graphene nanoribbon is uncovered. We believe that the versatility of this framework paves the way for swiftly linking quantum physics and atom-level structures.</p>", "Keywords": "", "DOI": "10.1088/2632-2153/ad0937", "PubYear": 2023, "Volume": "4", "Issue": "4", "JournalId": 72803, "JournalTitle": "Machine Learning: Science and Technology", "ISSN": "", "EISSN": "2632-2153", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 4, "Name": "Shurong <PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Artificial Micro- and Nano-Structures of Ministry of Education, School of Physics and Technology, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; School of Microelectronics, Wuhan University, Wuhan, Hubei 430072, People’s Republic of China; Corresponding author."}], "References": []}, {"ArticleId": 110706047, "Title": "Joint learning of motion deblurring and defocus deblurring networks with a real-world dataset", "Abstract": "When recovering the sharp image from a blurry observation, moving objects, e.g. , people and vehicles, usually attract more perceptual attention. However, existing motion deblurring methods primarily focus on removing the global camera motion blur, while neglecting the object motion blur. In this paper, we propose a j oint l earning framework for m otion deblurring and d efocus deblurring (JLMD), where a motion deblurring network can be trained for removing object motion blur, and a defocus deblurring network can be also obtained as a byproduct. Especially, we propose to circumvent the hardship in obtaining all-sharp ground truth for object motion blurry images by leveraging the complementary nature between object motion blur and defocus blur. The JLMD framework incorporates three key components. Firstly, a mask generation module is deployed to predict the boundary of motion blur. Secondly, a mutual supervision mechanism is elaborated, which enables the defocus blurry image and the motion blurry image to serve as the ground truth for each other. Thirdly, a content preservation loss is designed to encourage non-blurry regions to stay unchanged. Furthermore, we introduce a Defocus and Motion Deblurring Dataset (DMDD), which is the first real-world dataset specifically dedicated to object motion blurry images. Extensive experiments validate that our method is more effective in removing object motion blur than state-of-the-art methods, while it is at least comparable or superior in removing global camera motion blur and defocus blur. The source code and dataset are available at https://github.com/liyucs/JLMD .", "Keywords": "", "DOI": "10.1016/j.neucom.2023.126996", "PubYear": 2024, "Volume": "565", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China"}, {"AuthorId": 2, "Name": "Xinya Shu", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Ren", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Li", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, 150001, China"}], "References": [{"Title": "Joint blur kernel estimation and CNN for blind image restoration", "Authors": "<PERSON><PERSON> Huang; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "324", "JournalTitle": "Neurocomputing"}, {"Title": "Image deblurring using tri-segment intensity prior", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "398", "Issue": "", "Page": "265", "JournalTitle": "Neurocomputing"}, {"Title": "Deep robust image deblurring via blur distilling and information comparison in latent space", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "466", "Issue": "", "Page": "69", "JournalTitle": "Neurocomputing"}, {"Title": "Hierarchical complementary residual attention learning for defocus blur detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "501", "Issue": "", "Page": "88", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 110706115, "Title": "Discussion on the application of 5G communication technology in the Internet of Things", "Abstract": "<p>At present, the Internet of Things has been widely used in various fields of people's work and life. At the same time, 5G communication technology has been widely concerned in various fields, which makes the network technology is constantly adopted and optimized. 5G communication technology has the advantages of high capacity, fast speed and low latency. The technology has played an important role in promoting the development of the Internet of Things. This paper focuses on the relationship between 5G communication and the Internet of Things, as well as the convenience brought by the combination of 5G communication and the Internet of Things technology for people's life and production.</p>", "Keywords": "Internet of Things; 5G communication technology.", "DOI": "10.56028/ijcit.1.5.1.2023", "PubYear": 2023, "Volume": "5", "Issue": "1", "JournalId": 98795, "JournalTitle": "International Journal of Computing and Information Technology", "ISSN": "", "EISSN": "2790-170X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 110706162, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-7051(23)00891-2", "PubYear": 2023, "Volume": "281", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [], "References": []}, {"ArticleId": 110706220, "Title": "Hierarchical generative modelling for autonomous robots", "Abstract": "", "Keywords": "", "DOI": "10.1038/s42256-023-00752-z", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "ASNets: Deep Learning for Generalised Planning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "68", "Issue": "", "Page": "1", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Multi-expert learning of adaptive legged locomotion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "49", "Page": "eabb2174", "JournalTitle": "Science Robotics"}, {"Title": "Active Inference: Demystified and Compared", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "674", "JournalTitle": "Neural Computation"}, {"Title": "Hierarchical Reinforcement Learning: A Survey and Open Research Challenges", "Authors": "<PERSON><PERSON><PERSON>; Kevin <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "1", "Page": "172", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "Multi-expert synthesis for versatile locomotion and manipulation skills", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "", "Page": "970890", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 110706310, "Title": "Transfers to Frozen Orbits Around Planetary Moons Using Manifolds of Averaged Dynamics", "Abstract": "<p>A novel methodology is proposed to design the transfers from halo orbits to low-eccentricity, high-inclination frozen orbits around planetary moons, with applications to the Jupiter-Europa system. The manifolds of averaged dynamics are used to reduce the transfer [Formula: see text] cost. A one-degree-of-freedom dynamical model around Europa, incorporating effects of Jupiter’s third-body perturbation and Europa’s nonspherical gravity, is established via double-averaging. Europa frozen orbits are calculated and classified into five groups according to their stability and locations in the phase space. The phase portraits indicate that low-eccentricity, high-inclination frozen orbits are always unstable, and manifolds are found to be associated with them. The transfers from halo orbits to these frozen orbits are designed by inserting into the manifolds first and then following the natural evolution until the arrival at the target frozen orbit. A nonlinear programming problem is established to optimize the transfers. Through the analytical derivations, the optimization model is simplified and finally solved by the particle swarm optimization algorithm. The efficiency of the proposed methodology is demonstrated by numerical experiments, which indicate that the transfer [Formula: see text] cost can be reduced by approximately 10 to 40% at the expense of longer transfer time.</p>", "Keywords": "Frozen Orbits; Third-Body Gravity Perturbation; Averaged Dynamics; Jupiter-Europa System; Oblateness Perturbation; CR3BP; Transfer Orbits; Libration Point Orbits; Manifolds; Optimization", "DOI": "10.2514/1.G007774", "PubYear": 2024, "Volume": "47", "Issue": "2", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beihang University, 102206 Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "Pengfei Lu", "Affiliation": "Beihang University, 102206 Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "Tao Fu", "Affiliation": "Beihang University, 102206 Beijing, People’s Republic of China"}], "References": [{"Title": "Eclipse-Conscious Transfer to Lunar Gateway Using Ephemeris-Driven Terminal Coast Arcs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "11", "Page": "1972", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 110706323, "Title": "IoT-based systems and applications for elderly healthcare: a systematic review", "Abstract": "<p>This paper systematically reviews previous studies about Internet of Things (IoT)-based systems and applications for elderly healthcare. The review aims to achieve several objectives. Firstly, it seeks to classify the healthcare categories and research areas addressed in the reviewed studies. Secondly, it aims to explore the current trends in terms of main ideas, contributions, and limitations, providing insights for future research directions. Lastly, it presents distributions of the research works based on healthcare categories, area studies, publication years, and publishers. The review process follows the guidelines of Systematic Reviews and Meta-Analyses (PRISMA) to ensure a rigorous examination of the selected papers. A total of 54 academic papers, published in peer-reviewed venues between 2018 and 2022, were included in the review, making it the first of its kind. The results indicate that IoT-based technologies play a significant role in creating systems and applications to support the elderly across various healthcare categories, including health promotion, health prevention, health treatment, and health rehabilitation. There are several area studies identified as (1) security, (2) platforms, infrastructure, and architecture, (3) designs and developments, (4) analytics, models, and algorithms, (5) frameworks, (6) hardware and devices, (7) computer systems. Among the healthcare categories, health monitoring in the health rehabilitation category and fall detection for health prevention category emerged as the most commonly addressed topics. In terms of publication trends, the majority of relevant papers were published in 2021. The research area with the highest number of papers was designs and developments, with ScienceDirect being the most prominent publisher for this research. Overall, this systematic review provides valuable insights into the field of IoT-based systems and applications for elderly healthcare. These findings can guide future studies and contribute to the advancement of IoT-based solutions for elderly healthcare.</p>", "Keywords": "Systematic review; Elderly healthcare; IoT; Systems; Applications", "DOI": "10.1007/s10209-023-01055-1", "PubYear": 2025, "Volume": "24", "Issue": "1", "JournalId": 1397, "JournalTitle": "Universal Access in the Information Society", "ISSN": "1615-5289", "EISSN": "1615-5297", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Innovation and Digital Transformation, Department of Interdisciplinary Engineering, Faculty of Engineering, Prince of Songkla University, Hatyai, Songkhla, Thailand; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Division of Computational Science, Faculty of Science, Prince of Songkla University, Hatyai, Songkhla, Thailand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computational Science, Faculty of Science, Prince of Songkla University, Hatyai, Songkhla, Thailand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computational Science, Faculty of Science, Prince of Songkla University, Hatyai, Songkhla, Thailand"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computational Science, Faculty of Science, Prince of Songkla University, Hatyai, Songkhla, Thailand"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Traditional Thai Medical Research and Innovation Center, Faculty of Traditional Thai Medicine, Prince of Songkla University, Hatyai, Songkhla, Thailand"}, {"AuthorId": 7, "Name": "K. <PERSON>. <PERSON>", "Affiliation": "Traditional Thai Medical Research and Innovation Center, Faculty of Traditional Thai Medicine, Prince of Songkla University, Hatyai, Songkhla, Thailand"}], "References": [{"Title": "Secure IoT communications for smart healthcare monitoring system", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "", "Page": "100036", "JournalTitle": "Internet of Things"}, {"Title": "Hash polynomial two factor decision tree using IoT for smart health care scheduling", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112924", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An IoT based device-type invariant fall detection system", "Authors": "Sheikh <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "", "Page": "100130", "JournalTitle": "Internet of Things"}, {"Title": "Fall detection in older adults with mobile IoT devices and machine learning in the cloud and on the edge", "Authors": "<PERSON><PERSON>; <PERSON>; Bożena Małysiak-<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "537", "Issue": "", "Page": "132", "JournalTitle": "Information Sciences"}, {"Title": "IoT supported smart home for the elderly", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "", "Page": "100239", "JournalTitle": "Internet of Things"}, {"Title": "Jointly optimization for activity recognition in secure IoT-enabled elderly care applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106788", "JournalTitle": "Applied Soft Computing"}, {"Title": "IoT Ambient Assisted Living: Scalable Analytics Architecture and Flexible Process", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "177", "Issue": "", "Page": "396", "JournalTitle": "Procedia Computer Science"}, {"Title": "Analysis of factors affecting IoT-based smart hospital design", "Authors": "Banu Çalış Uslu; Ertuğ Okay; Erkan Dursun", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "67", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "IoT and digital twin enabled smart tracking for safety management", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "128", "Issue": "", "Page": "105183", "JournalTitle": "Computers & Operations Research"}, {"Title": "RETRACTED: Design of hospital IoT system and drug intervention in patients with acute myocardial infarction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "103662", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "RETRACTED: Medical IoT system platform and elderly patients’ femoral shaft fracture nursing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103868", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "IoT-based smart healthcare video surveillance system using edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "3195", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "SPICE-IT: Smart COVID-19 pandemic controlled eradication over NDN-IoT", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "74", "Issue": "", "Page": "50", "JournalTitle": "Information Fusion"}, {"Title": "FriendCare-AAL: a robust social IoT based alert generation system for ambient assisted living", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "4", "Page": "1735", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "SafeMobility: An IoT- based System for safer mobility using machine learning in the age of COVID-19", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "524", "JournalTitle": "Procedia Computer Science"}, {"Title": "AIoTES: Setting the principles for semantic interoperable and modern IoT-enabled reference architecture for Active and Healthy Ageing ecosystems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "96", "JournalTitle": "Computer Communications"}, {"Title": "IoT-based hybrid optimized fuzzy threshold ELM model for localization of elderly persons", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115500", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Remote patient monitoring and classifying using the internet of things platform combined with cloud computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Optimal deployment of face recognition solutions in a heterogeneous IoT platform for secure elderly care applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "192", "Issue": "", "Page": "3204", "JournalTitle": "Procedia Computer Science"}, {"Title": "A multi-level simulation-based optimization framework for IoT-enabled elderly care systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "102420", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Knowledge Engineering Framework for IoT Robotics Applied to Smart Healthcare and Emotional Well-Being", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "445", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 110706362, "Title": "Correction to: Submerged arc welding process: a numerical investigation of temperatures, displacements, and residual stresses in ASTM A516‑Gr70 corner joined samples", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-023-12617-1", "PubYear": 2023, "Volume": "129", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical, Energy and Management Engineering, University of Calabria, Arcavacata di Rende, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical, Energy and Management Engineering, University of Calabria, Arcavacata di Rende, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical, Energy and Management Engineering, University of Calabria, Arcavacata di Rende, Italy"}], "References": []}, {"ArticleId": 110706380, "Title": "Current status and outlook of magnetic data storage devices", "Abstract": "<p>Big data analytics, cloud services, internet of things (IoT), personal mobile devices, social networks and artificial intelligence (AI) have created strong demand for enterprises to amass information. Studies show that the amount of data being recorded is increasing about 30–40% per year. Based on some estimates, in 2023, approximately 330 million terabytes of data were created each day. It is further estimated that 80–90% of data created never gets accessed again. Magnetic tape and hard disk drives and semiconductor-based solid-state drives are used to store data. Hard disk and solid-state drives are online, and tape drives are offline and used for archival storage of big data and backup. The market share of solid-state drives continues to increase; however, they are more expensive than hard disk drives in cost per TB. Over the years, areal recording densities of magnetic data storage devices have continued to increase by two digits annually because of the introduction of new technologies. Total capacity and units shipped have increased astronomically but price per TB continues to go down which keeps magnetic storage industry under constant pressure. In 2024, because of low cost per TB, hard disk drives are projected to control more than half of the world’s data and will remain robust for some time. In 2023, magnetic tape drives remained dominant for archival storage and backup because of high volumetric density and low cost per TB. This paper starts with a description of new technologies to meet growing areal density demands followed by an overview of the current market and outlook of magnetic data storage devices. Competitive solid-state drives for data storage are also discussed.</p>", "Keywords": "", "DOI": "10.1007/s00542-023-05549-z", "PubYear": 2023, "Volume": "29", "Issue": "11", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Ohio State University, San Jose, USA"}], "References": [{"Title": "Revisiting NASA space shuttle challenger magnetic-tape data recovery investigation", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "5", "Page": "683", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": 110706386, "Title": "Real-time Face Recognition System Using Deep Learning Method", "Abstract": "<p>Face recognition is one of the most popular methods currently used for biometric systems. The selection of a suitable method greatly affects the reliability of the biometrics system. This research will use Deep learning to improve the reliability of the biometric system and will compare it with the SVM method. The Deep Learning method will be adopted using the Siamese Network with the YoloV5 detection method as a real-time face detector. There are two stages in this research: the registration process and the recognition process. The registration process is image acquisition using YoloV5. The image result will be saved in the storage folder, and the preprocessing and training process will use the Siamese Network. The face feature model will be stored in the database. The recognition process is the same as the registration, but the feature extraction result will be embedded and compared with the already trained models. The accuracy rate using the Siamese model was 94%.\r  </p>", "Keywords": "", "DOI": "10.24843/LKJITI.2023.v14.i01.p06", "PubYear": 2023, "Volume": "14", "Issue": "1", "JournalId": 46965, "JournalTitle": "Lontar Komputer : <PERSON><PERSON> Ilmiah Teknologi Informasi", "ISSN": "2088-1541", "EISSN": "2541-5832", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology Department, Udayana University"}, {"AuthorId": 2, "Name": "I Ketut Gede Darma Putra", "Affiliation": "Information Technology Department, Udayana University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Electrical Engineering, Udayana University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering, Udayana University"}, {"AuthorId": 5, "Name": "Lennia <PERSON>", "Affiliation": "Information Technology Department, Udayana University"}], "References": []}, {"ArticleId": 110706389, "Title": "Joint relational triple extraction based on potential relation detection and conditional entity mapping", "Abstract": "<p>Joint relational triple extraction treats entity recognition and relation extraction as a joint task to extract relational triples, and this is a critical task in information extraction and knowledge graph construction. However, most existing joint models still fall short in terms of extracting overlapping triples. Moreover, these models ignore the trigger words of potential relations during the relation detection process. To address the two issues, a joint model based on P otential R elation D etection and C onditional E ntity M apping is proposed, named PRDCEM. Specifically, the proposed model consists of three components, i.e., potential relation detection, candidate entity tagging, and conditional entity mapping, corresponding to three subtasks. First, a non-autoregressive decoder that contains a cross-attention mechanism is applied to detect potential relations. In this way, different potential relations are associated with the corresponding trigger words in the given sentence, and the semantic representations of the trigger words are fully utilized to encode potential relations. Second, two distinct sequence taggers are employed to extract candidate subjects and objects. Third, an entity mapping module incorporating conditional layer normalization is designed to align the candidate subjects and objects. As such, each candidate subject and each potential relation are combined to form a condition that is incorporated into the sentence, which can effectively extract overlapping triples. Finally, the negative sampling strategy is employed in the entity mapping module to mitigate the error propagation from the previous two components. In a comparison with 15 baselines, the experimental results obtained on two widely used public datasets demonstrate that PRDCEM can effectively extract overlapping triples and achieve improved performance.</p>", "Keywords": "Conditional layer normalization; Entity mapping; Joint relational triple extraction; Potential relation detection", "DOI": "10.1007/s10489-023-05111-4", "PubYear": 2023, "Volume": "53", "Issue": "24", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Big Data Intelligent Computing, Chongqing University of Posts and Telecommunications, Chongqing, China; Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Tourism Multisource Data Perception and Decision, Ministry of Culture and Tourism, Chongqing, China"}, {"AuthorId": 2, "Name": "Qing<PERSON> Zhang", "Affiliation": "Key Laboratory of Big Data Intelligent Computing, Chongqing University of Posts and Telecommunications, Chongqing, China; Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Tourism Multisource Data Perception and Decision, Ministry of Culture and Tourism, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Big Data Intelligent Computing, Chongqing University of Posts and Telecommunications, Chongqing, China; Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Tourism Multisource Data Perception and Decision, Ministry of Culture and Tourism, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Big Data Intelligent Computing, Chongqing University of Posts and Telecommunications, Chongqing, China; Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Tourism Multisource Data Perception and Decision, Ministry of Culture and Tourism, Chongqing, China"}], "References": [{"Title": "RMAN: Relational multi-head attention neural network for joint extraction of entities and relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "3", "Page": "3132", "JournalTitle": "Applied Intelligence"}, {"Title": "A region-based hypergraph network for joint entity-relation extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "228", "Issue": "", "Page": "107298", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A relation aware embedding mechanism for relation extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "10022", "JournalTitle": "Applied Intelligence"}, {"Title": "ERGM: A multi-stage joint entity and relation extraction with global entity match", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "271", "Issue": "", "Page": "110550", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 110706407, "Title": "Real-Time Reachable Set Synthesis of Takagi–Sugeno Fuzzy Uncertain Singular Systems Based on Adaptive Event-Triggered Scheme", "Abstract": "<p>In this paper, the problem of real-time reachable set synthesis for a type of Takagi–Sugeno fuzzy uncertain singular systems founded on an adaptive event-triggered strategy is discussed. Firstly, based on the adaptive event-triggered strategy, a proportional and differential feedback controller is designed to transform the singular system into a normal system to ensure the admissibility of the system. Secondly, combined with <PERSON><PERSON><PERSON><PERSON> stability theory, the sufficient conditions of reachable set boundary are given in the form of linear matrix inequality, and the stability of closed-loop control system is verified. In addition, the required real-time reachable set boundary data and controller gains are calculated using the matrix decoupling approach. Finally, an actual DC motor model is used in the simulation part to verify the efficacy of the presented approach.</p>", "Keywords": "Event-triggered; Reachable set; Linear matrix inequality; <PERSON><PERSON><PERSON>–Sugeno fuzzy singular systems", "DOI": "10.1007/s40815-023-01599-7", "PubYear": 2024, "Volume": "26", "Issue": "1", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "College of Engineering, Bohai University, Jinzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Engineering, Bohai University, Jinzhou, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "Faculty of Electronic Information and Electrical Engineering, Dalian University of Technology, Dalian, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Bohai University, Jinzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, Saudi Arabia"}], "References": [{"Title": "Robust event-triggered reliable control for T-S fuzzy uncertain systems via weighted based inequality", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "31", "JournalTitle": "Information Sciences"}, {"Title": "Adaptive fuzzy control design for synchronization of chaotic time-delay system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "535", "Issue": "", "Page": "225", "JournalTitle": "Information Sciences"}, {"Title": "Event-based fuzzy control for T-S fuzzy networked systems with various data missing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "322", "JournalTitle": "Neurocomputing"}, {"Title": "A variable memory state feedback and its application to robust control of uncertain singular time-delay systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "2177", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Adaptive neural network asymptotic control design for MIMO nonlinear systems based on event-triggered mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "603", "Issue": "", "Page": "91", "JournalTitle": "Information Sciences"}, {"Title": "Observer-Based Robust Adaptive TS Fuzzy Control of Uncertain Systems with High-Order Input Derivatives and Nonlinear Input–Output Relationships", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "4", "Page": "1400", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Sliding-mode surface-based decentralized event-triggered control of partially unknown interconnected nonlinear systems via reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "641", "Issue": "", "Page": "119070", "JournalTitle": "Information Sciences"}, {"Title": "Command Filter-Based Adaptive Fuzzy Self-Triggered Control for MIMO Nonlinear Systems with Time-Varying Full-State Constraints", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "25", "Issue": "8", "Page": "3144", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 110706438, "Title": "Influence of aircraft attitude on fully polarized radiometer and its correction", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2023.2266120", "PubYear": 2023, "Volume": "44", "Issue": "20", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Microwave Remote Sensing, National Space Science Center, Chinese Academy of Sciences, Beijing, China;School of Electronic, Electrical and Communication Engineering, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> Wang", "Affiliation": "Key Laboratory of Microwave Remote Sensing, National Space Science Center, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Microwave Remote Sensing, National Space Science Center, Chinese Academy of Sciences, Beijing, China;School of Electronic, Electrical and Communication Engineering, University of Chinese Academy of Sciences, Beijing, China"}], "References": [{"Title": "Edge-Preserving classification of polarimetric SAR images using Wishart distribution and conditional random field", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "6", "Page": "2134", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": *********, "Title": "Hybrid deep learning model for wave height prediction in Australia's wave energy region", "Abstract": "Waves are emerging as a renewable energy resource, but the harnessing of such energy remains among the least developed in terms of renewable energy technologies on a regional or a global basis. To generate usable energy, wave heights must be predicted in near-real-time, which is the driving force for wave energy converters. This study develops a hybrid Convolutional Neural Network-Long Short-Term Memory-Bidirectional Gated Recurrent Unit forecast system (CLSTM-BiGRU) trained to accurately predict significant wave height ( H <sub> sig </sub>) at multiple forecasting horizons (30 min, 0.5 H ; 2 h, 02 H ; 3 h, 03 H and 6 h, 06 H . In this model, convolutional neural networks (CNNs), long-short-term memories (LSTMs), and bidirectional gated recurrent units (BiGRUs) are employed to predict H <sub> sig </sub>. To construct the proposed CLSTM-BiGRU model, historical wave properties, including maximum wave height, zero-up crossing wave period, peak energy wave period, sea surface temperature, and significant wave heights are analysed. Several wave energy generation sites in Queensland, Australia were tested using the hybrid deep learning CLSTM-BiGRU model. Based on statistical score metrics, scatterplots, and error evaluations, the hybrid CLSTM-BiGRU model generates more accurate forecasts than the benchmark models. This study established the practical utility of the hybrid CLSTM-BiGRU model for modelling H <sub> sig </sub> and therefore shows the model could have significant implications for wave and ocean energy generation systems, tidal or wave height monitoring as well as sustainable wave energy resource evaluation where a prediction of wave heights is required.", "Keywords": "Deep learning model ; Significant wave height ; Wave energy ; Renewable energy ; Sea level monitoring system", "DOI": "10.1016/j.asoc.2023.111003", "PubYear": 2024, "Volume": "150", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Infrastructure Engineering, The University of Melbourne, Victoria 3010 Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics Physics and Computing, University of Southern Queensland, Springfield, QLD 4300, Australia"}, {"AuthorId": 3, "Name": "Mohanad S. AL-Musa<PERSON>", "Affiliation": "Department of Information Technologies, Management Technical College, Southern Technical University, Basrah 61001, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics Physics and Computing, University of Southern Queensland, Springfield, QLD 4300, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alabama at Birmingham, Birmingham, AL, United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics Physics and Computing, University of Southern Queensland, Springfield, QLD 4300, Australia;Corresponding author"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "LOS Cable Solutions, Bandadalen 17, 5417 Stord, Vestland, Norway"}], "References": []}, {"ArticleId": *********, "Title": "Anomalous node detection in attributed social networks using dual variational autoencoder with generative adversarial networks", "Abstract": "Many types of real-world information systems, including social media and e-commerce platforms, can be modelled by means of attribute-rich, connected networks. The goal of anomaly detection in artificial intelligence is to identify illustrations that deviate significantly from the main distribution of data or that differ from known cases. Anomalous nodes in node-attributed networks can be identified with greater precision if both graph and node attributes are taken into account. Almost all of the studies in this area focus on supervised techniques for spotting outliers. While supervised algorithms for anomaly detection work well in theory, they cannot be applied to real-world applications owing to a lack of labelled data. Considering the possible data distribution, our model employs a dual variational autoencoder (VAE), while a generative adversarial network (GAN) assures the model is robust to adversarial training. The dual VAEs are used in another capacity: as a fake-node generator. Adversarial training is used to ensure that our latent codes have a Gaussian or uniform distribution. To provide a fair presentation of the graph, the discriminator instructs the generator to generate latent variables with distributions that are more consistent with the actual distribution of the data. Once the model has been learned, the discriminator is used for anomaly detection via reconstruction loss it has been trained to distinguish between the normal and artificial distributions of data. First, using a dual VAE, our model simultaneously captures cross-modality interactions between topological structure and node characteristics and overcomes the problem of unlabeled anomalies, allowing us to better understand the network sparsity and nonlinearity. Second, the proposed model considers the regularization of the latent codes while solving the issue of unregularized embedding techniques that can quickly lead to unsatisfactory representation. Finally, we use the discriminator reconstruction loss for anomaly detection as the discriminator is well-trained to separate the normal and generated data distributions because reconstruction-based loss does not include the adversarial component. Experiments conducted on attributed networks demonstrate the effectiveness of the proposed model and show that it greatly surpasses the previous methods. The area under the curve scores of our proposed model for the BlogCatalog, Flickr, and Enron datasets are 0.83680, 0.82020, and 0.71180, respectively, proving the effectiveness of the proposed model. The result of the proposed model on the Enron dataset is slightly worse than the other models; we attribute this to the dataset&#x27;s low dimensionality as the most probable explanation.", "Keywords": "Anomaly detection deep learning ; Attributed networks autoencoder ; Dual variational-autoencoder ; Generative adversarial networks", "DOI": "10.1016/j.dsm.2023.10.005", "PubYear": 2024, "Volume": "7", "Issue": "2", "JournalId": 84310, "JournalTitle": "Journal of Information Technology and Data Management", "ISSN": "2666-7649", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Education Foundation Vaddeswaram, AP, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Aligarh Muslim University, Aligarh, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Education Foundation Vaddeswaram, AP, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Era University, Lucknow, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Education Foundation Vaddeswaram, AP, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> Education Foundation Vaddeswaram, AP, India"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Sandip Foundation, Nashik, India"}], "References": []}, {"ArticleId": 110706583, "Title": "F-SCP: An automatic prompt generation method for specific classes based on visual language pre-training models", "Abstract": "The zero-shot classification performance of large-scale vision-language pre-training models (e.g., CLIP, BLIP and ALIGN) can be enhanced by incorporating a prompt (e.g., “a photo of a [CLASS]”) before the class words. Modifying the prompt slightly can have significant effect on the classification outcomes of these models. Thus, it is crucial to include an appropriate prompt tailored to the classes. However, manual prompt design is labor-intensive and necessitates domain-specific expertise. The CoOp (Context Optimization) converts hand-crafted prompt templates into learnable word vectors to automatically generate prompts, resulting in substantial improvements for CLIP. However, CoOp exhibited significant variation in classification performance across different classes. Although CoOp-CSC (Class-Specific Context) has a separate prompt for each class, only shows some advantages on fine-grained datasets. In this paper, we propose a novel automatic prompt generation method called F-SCP (Filter-based Specific Class Prompt), which distinguishes itself from the CoOp-UC (Unified Context) model and the CoOp-CSC model. Our approach focuses on prompt generation for low-accuracy classes and similar classes. We add the Filter and SCP modules to the prompt generation architecture. The Filter module selects the poorly classified classes, and then reproduce the prompts through the SCP (Specific Class Prompt) module to replace the prompts of specific classes. Experimental results on six multi-domain datasets shows the superiority of our approach over the state-of-the-art methods. Particularly, the improvement in accuracy for the specific classes mentioned above is significant. For instance, compared with CoOp-UC on the OxfordPets dataset, the low-accuracy classes, such as, Class21 and Class26, are improved by 18% and 12%, respectively.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.110096", "PubYear": 2024, "Volume": "147", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai University of Engineering Science, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai University of Engineering Science, Shanghai, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai University of Engineering Science, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Malaysia-Japan International Institute of Technology (MJIIT), Universiti Teknologi Malaysia, Kuala Lumpur, 54100, Malaysia;Andalusian Research Institute in Data Science and Computational Intelligence (DaSCI), University of Granada, Granada, Spain;Regional Research Center, Iwate Prefectural University, Takizawa, 020-0693, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai University of Engineering Science, Shanghai, China"}], "References": [{"Title": "Learning to Prompt for Vision-Language Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "9", "Page": "2337", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 110706599, "Title": "Adaptive event-triggered PID load frequency control for multi-area interconnected wind power systems under aperiodic DoS attacks", "Abstract": "In this article, an adaptive event-triggered proportional–integral–derivative (PID) load frequency control (LFC) strategy is designed for multi-area interconnected wind power systems under aperiodic denial-of-service (DoS) attacks. First, to relieve the frequency fluctuation caused by wind power integration, a dynamic model including the doubly-fed induction generator is established for multi-area interconnected wind power systems. Second, to avoid superfluous occupation of network communication bandwidth , an adaptive event-triggered mechanism with memory property is proposed under aperiodic DoS attacks. Then, with the aid of intermittent control method , a memory-based PID LFC strategy is achieved to stabilize the frequency of wind power systems. Third, by constructing a switched Lyapunov-Krasovskii functional, two exponential stability criteria meeting the H ∞ norm bound are derived. Finally, simulation examples are provided to verify the effectiveness of the presented control strategy.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122420", "PubYear": 2024, "Volume": "241", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, University of Electronic Science and Technology of China, Chengdu 611731, China;Aircraft Swarm Intelligent Sensing and Cooperative Control Key Laboratory of Sichuan Province, Chengdu 611731, China"}, {"AuthorId": 2, "Name": "Shaoyu Hu", "Affiliation": "School of Automation Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China;School of Aeronautics and Astronautics, University of Electronic Science and Technology of China, Chengdu 611731, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Sinopec Xinan Oilfield Service Corporation, Chengdu 610041, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, University of Electronic Science and Technology of China, Chengdu 611731, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Aeronautics and Astronautics, University of Electronic Science and Technology of China, Chengdu 611731, China"}, {"AuthorId": 6, "Name": "Kaibo Shi", "Affiliation": "School of Electronic Information and Electrical Engineering, Chengdu University, Chengdu 610106, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics Sciences, University of Electronic Science and Technology of China, Chengdu 611731, China"}], "References": [{"Title": "Secure consensus switching control for multiagent system under abnormal deception attacks and its application to unmanned surface vehicle clusters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Kaibo Shi", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117702", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Integral-based event-triggering switched LFC scheme for power system under deception attack", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Huaicheng Yan", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "121075", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 110706600, "Title": "WordTransABSA: Enhancing Aspect-based Sentiment Analysis with masked language modeling for affective token prediction", "Abstract": "In recent years, Aspect-based Sentiment Analysis (ABSA) has been a crucial yet challenging task in recognizing textual emotions from text. ABSA has numerous application across various fields, such as social media, commodity review, and movie comment, making it an attractive area of research. Many researchers are working to develop more powerful sentiment analysis models. Currently, most existing ABSA models use the generic pre-trained language models (PLMs) based fine-tuning paradigm, which only utilizes the encoder parameters while discarding the decoder parameters of PLMs. However, this approach fails to leverage the prior knowledge revealed in PLMs effectively. To address these issues, we investigate the potential of the initial pre-training scheme of PLMs to conduct ABSA and thus propose a novel approach in this paper, namely Target Word Transferred ABSA (WordTransABSA). In WordTransABSA, we propose “Word Transferred LM”, a novel sequence-level optimization strategy that transferred target words in sentence into pivot tokens to stimulate better PLM semantic understanding capability. Given a sentence with aspect terms as input, WordTransABSA generates contextually appropriate semantics and predicts the affective tokens on the corresponding positions of the aspect terms. The final sentiment polarity of each aspect term is determined through several sentiment identification strategies that we selected. WordTransABSA takes full advantage of the versatile linguistic knowledge of Pre-trained Language Model, resulting in competitive accuracy compared with recent baselines. The WordTransABSA demonstrates its superiority and effectiveness through extensive experiments in both data-sufficient (full-data supervised learning) and data-insufficient (few-shot learning) scenarios. We have made our code publicly available on GitHub: https://github.com/albert-jin/WordTransABSA .", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122289", "PubYear": 2024, "Volume": "238", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communications Engineering, Xi‘an Jiaotong University, Xi‘an, Shaanxi, 710049, China;School of Computer Engineering and Science, Shanghai University, Shangda Road No. 99., Baoshan, Shanghai, 200444, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communications Engineering, Xi‘an Jiaotong University, Xi‘an, Shaanxi, 710049, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information and Communications Engineering, Xi‘an Jiaotong University, Xi‘an, Shaanxi, 710049, China;College of Physics & Electronic Information, Yan’an University, Yan’an, Shanxi, 716000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communications Engineering, Xi‘an Jiaotong University, Xi‘an, Shaanxi, 710049, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Engineering and Science, Shanghai University, Shangda Road No. 99., Baoshan, Shanghai, 200444, China;Corresponding author"}], "References": [{"Title": "DecomVQANet: Decomposing visual question answering deep network via tensor decomposition and regression", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107538", "JournalTitle": "Pattern Recognition"}, {"Title": "Pre-trained models: Past, present and future", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "225", "JournalTitle": "AI Open"}, {"Title": "DAFS: a domain aware few shot generative model for event detection", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "112", "Issue": "3", "Page": "1011", "JournalTitle": "Machine Learning"}, {"Title": "Pre-train, Prompt, and Predict: A Systematic Survey of Prompting Methods in Natural Language Processing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Joint event causality extraction using dual-channel enhanced neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "258", "Issue": "", "Page": "109935", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 110706701, "Title": "Peak stress and peak strain evaluation of concrete columns confined with lateral ties under axial compression by artificial neural networks", "Abstract": "<p>The peak stress and peak strain of concrete columns confined with lateral stirrups were important indicators for evaluating the load-bearing capacity and axial deformation of concrete columns under axial compression. However, it took much work to determine the peak stress and peak strain of concrete confined with lateral ties under axial compression due to the complicated arching actions of lateral ties and longitudinal reinforcements, as well as the complex interaction between concrete and lateral ties. In this paper, two typical artificial neural networks (ANN), including (BP networks and Elamn networks) were applied to evaluate the peak stress and peak strain of concrete columns confined with lateral ties based on a reliable database consisting of 196 test data sets for peak stress and 166 test data sets for peak strain collected from previous studies. Both proposed ANN models had high prediction performance in the training and testing process. Furthermore, By comparing with existing analytical models, the proposed BP networks had high reliability and applicability in predicting confined concrete’s peak stress. In contrast, the Elman network had high reliability and applicability in peak strain of concrete columns confined with lateral ties. Furthermore, based on the sensitivity analysis, the concrete strength and the properties of lateral ties obviously influence the peak stress of confined concrete. In contrast, the volumetric ratio of lateral ties significantly affected the peak strain of confined concrete.</p>", "Keywords": "Confined concrete columns; Peak stress; Peak strain; Artificial neural networks", "DOI": "10.1007/s00500-023-09357-5", "PubYear": 2024, "Volume": "28", "Issue": "6", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Transportation Science and Engineering, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Lab of Structures Dynamic Behavior and Control Ministry of Education, Harbin Institute of Technology, Harbin, China; Key Lab of Smart Prevention and Mitigation of Civil Engineering Disasters of the Ministry of Industry and Information Technology, Harbin Institute of Technology, Harbin, China; School of Civil Engineering, Harbin Institute of Technology, Harbin, China; Corresponding author."}], "References": [{"Title": "A two-step combined algorithm based on NARX neural network and the subsequent prediction of the residues improves prediction accuracy of the greenhouse gases concentrations", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "1547", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Determination of theoretical stress concentration factor for circular/elliptical holes with reinforcement using analytical, finite element method and artificial neural network techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "19", "Page": "12641", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Compressive strength evaluation of concrete confined with spiral stirrups by using adaptive neuro-fuzzy inference system (ANFIS)", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "21", "Page": "11873", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 110706755, "Title": "μ ODNS: A distributed approach to DNS anonymization with collusion resistance", "Abstract": "The traditional Domain Name System (DNS) lacks fundamental security and privacy features in its design. As privacy concerns increased on the Internet, security and privacy enhancements of DNS have been actively investigated. Specifically, in the context of user privacy in DNS queries, several relay-based anonymization schemes have been recently introduced. However, these schemes are vulnerable to collusion between relays and full-service resolvers, which means user identities cannot be hidden from resolvers. This paper introduces a new concept for achieving user anonymity in DNS queries through a multiple-relay-based approach, called μ ODNS ( Mutualized Oblivious DNS ), by extending the concept of existing relay-based schemes. μ ODNS introduces a reasonable assumption that each user has at least one trusted or dedicated relay within the network and mutually shares the relay with other users. The user simply sets his trusted relay as the next-hop relay to convey his queries to the resolver and randomly chooses its zero or more subsequent relays shared by other entities. Under this assumption, the user’s identity remains concealed from the target resolver in μ ODNS even if an unknown subset of relays colludes with the resolver. Namely, in μ ODNS, users can preserve their anonymity by paying a small cost of sharing their resources. Additionally, we extend existing protocols, Anonymized DNSCrypt and Oblivious DoH, to provide practical Proof-of-Concept specifications and implementations as instances of μ ODNS. These implementations are publicly available on the Internet as open-source software and public services . Furthermore, we demonstrate, through measurements of round-trip times for DNS messages, that our implementation can minimize the performance degradation resulting from its privacy enhancements, achieving performance levels that maintain the positive user experiences observed in existing schemes.", "Keywords": "Domain Name System (DNS) ; Oblivious DoH ; DNSCrypt ; Anonymity ; DNS privacy ; Collusion resistance", "DOI": "10.1016/j.comnet.2023.110078", "PubYear": 2023, "Volume": "237", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate School of Information Science, University of Hyogo, 7-1-28 Minatojima-minamimachi, Chuo-ku, Kobe, 650-0047, Hyogo, Japan;Zettant Inc., 1-8-1-8F BcH, Nihonbashi-Kayabacho, Chuo-ku, 103-0025, Tokyo, Japan;Corresponding author at: Graduate School of Information Science, University of Hyogo, 7-1-28 Minatojima-minamimachi, Chuo-ku, Kobe, 650-0047, Hyogo, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information Science, University of Hyogo, 7-1-28 Minatojima-minamimachi, Chuo-ku, Kobe, 650-0047, Hyogo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Zettant Inc., 1-8-1-8F BcH, Nihonbashi-Kayabacho, Chuo-ku, 103-0025, Tokyo, Japan"}], "References": [{"Title": "Measuring DoH with web ads", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "212", "Issue": "", "Page": "109046", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 110706812, "Title": "Backward Elimination for Feature Selection on Breast Cancer Classification Using Logistic Regression and Support Vector Machine Algorithms", "Abstract": "<p>Breast cancer is a prevalent form of cancer that afflicts women across all nations globally. One of the ways that can be done as a prevention to reduce elevated fatality due to breast cancer is with a detection system that can determine whether a cancer is benign or malignant. Logistic Regression and Support Vector Machine (SVM) classification algorithms are often used to detect this disease, but the use of these two algorithms often doesn’t give optimal results when applied to datasets that have many features, so additional algorithm is needed to improve classification performance by using Backward Elimination feature selection. The comparison of Logistic Regression and SVM algorithms was carried out by applying feature selection to breast cancer data to see the best model. The breast cancer dataset has 30 features and two classes, Benign and Malignant. Backward Elimination has reduced features from 30 features to 13 features, thereby increasing the performance of both classification models. The best classification was obtained by using the Backward Elimination feature selection and linear kernel SVM with an increase in accuracy value from 96.14% to 97.02%, precision from 98.06% to 99.49%, recall from 90.48% to 92.38%, and the AUC from 0.95 to 0.96.</p>", "Keywords": "Breast Cancer Classification;Backward Elimination;Logistic Regression;SVM", "DOI": "10.22146/ijccs.88926", "PubYear": 2023, "Volume": "17", "Issue": "4", "JournalId": 42354, "JournalTitle": "IJCCS (Indonesian Journal of Computing and Cybernetics Systems)", "ISSN": "1978-1520", "EISSN": "2460-7258", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Lambung Mangkurat University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Lambung Mangkurat University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lambung Mangkurat University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Lambung Mangkurat University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lambung Mangkurat University"}], "References": []}, {"ArticleId": 110706838, "Title": "Novel spectral indices for enhanced estimations of 3-dimentional flavonoid contents for Ginkgo plantations using UAV-borne LiDAR and hyperspectral data", "Abstract": "Leaf flavonoid content (LFC) is a marked indicator of the protection signals from biotic and abiotic stresses, as well as the potential in the recovery of phenolic compounds from plants for producing potent antioxidants. LFC has been non-destructively retrieved from leaf reflectance spectra in recent studies. However, the LFC estimation from canopy-level spectra remains poorly understood and challenging arise from the confounding effects of other pigments and canopy structure. To address this limitation, this study proposed a suite of new 3-Dimentional spectral indices (SIs), in which the leaf-level standard flavonoid indices (FIs) are normalized by structure indices or chlorophyll indices. The hypothesis investigated is that these new SIs, derived from UAV-based hyperspectral point cloud data (fused by canopy hyperspectral images and LiDAR point cloud data), can enhance detecting LFC distribution within the canopies of Ginkgo plantations, by mitigating the effects of canopy structure and chlorophyll absorption. The results demonstrated that most chlorophyll-based normalized indices (CV-R<sup>2</sup> = 0.56–0.65) outperformed the structure-based normalized indices (CV-R<sup>2</sup> = 0.44–0.57) and the standard FIs (CV-R<sup>2</sup> = 0.19–0.54). In specific, FI<sub>420,710</sub>/SR<sub>800,710</sub> (CV-R<sup>2</sup> = 0.65) out of chlorophyll-based normalized indices performed better than other indices. With the use of FI<sub>420,710</sub>/SR<sub>800,710</sub>, the 3-Dimentional distribution of LFC within Ginkgo canopies can be well mapped. In summary, this study indicates marked potentials of the developed normalized indices for mapping LFC distribution, as well as providing new insight into alleviating the confounding effects of chlorophyll and structure on LFC estimation of Ginkgo plantations, with simulations conducted by the canopy radiative transfer model.", "Keywords": "", "DOI": "10.1016/j.rse.2023.113882", "PubYear": 2023, "Volume": "299", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Co-Innovation Center for Sustainable Forestry in Southern China, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Co-Innovation Center for Sustainable Forestry in Southern China, Nanjing Forestry University, Nanjing 210037, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Co-Innovation Center for Sustainable Forestry in Southern China, Nanjing Forestry University, Nanjing 210037, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Co-Innovation Center for Sustainable Forestry in Southern China, Nanjing Forestry University, Nanjing 210037, China"}], "References": [{"Title": "Quantifying vertical profiles of biochemical traits for forest plantation species using advanced remote sensing approaches", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "250", "Issue": "", "Page": "112041", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Improved estimation of leaf chlorophyll content of row crops from canopy reflectance spectra through minimizing canopy structural effects and optimizing off-noon observation time", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111985", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "PROSPECT-PRO for estimating content of nitrogen-containing leaf proteins and other carbon-based constituents", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "252", "Issue": "", "Page": "112173", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Design of supercontinuum laser hyperspectral light detection and ranging (LiDAR) (SCLaHS LiDAR)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "10", "Page": "3731", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Predicting medicinal phytochemicals of Moringa oleifera using hyperspectral reflectance of tree canopies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "10", "Page": "3955", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Monitoring restored tropical forest diversity and structure through UAV-borne hyperspectral and lidar fusion", "Authors": "<PERSON><PERSON>da; <PERSON>ben <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112582", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 110706942, "Title": "Research on state-parameter estimation of unmanned Tractor—A hybrid method of DEKF and ARBFNN", "Abstract": "Unmanned tractor relies on multi-sensor information collection to obtain the current state or parameters. However, when driving in complex field, it will inevitably suffer from the uneven ground, the impact of crop straw and clo<PERSON> et al., which usually poses considerable challenges for multi-sensor to obtain stable and accurate value of states and parameters to realize tractor automatic lane guidance control. Therefore, a novel state and parameter estimation method by mixing the dual extended Kalman filter (DEKF) technology and adaptive radial basis function neural network (ARBFNN) technology is proposed in this paper. Firstly, DEKF technique is applied to estimate key states and initial model time-varying parameters–front/rear axle cornering stiffnesses at the same time. Then, in order to further improve the accuracy of estimation value of front/rear axle cornering stiffnesses during the automatic lane guidance control process, an ARBFNN technology is investigated by taking the heading error, lateral error and initial estimation value of front/rear axle cornering stiffnesses from DEKF as inputs to approach ideal estimation value. Finally, results of automatic lane guidance control scenarios from both simulation and hardware-in-loop (HIL) implementation show that the proposed hybrid estimated method of DEKF and ARBFNN can robustly obtain satisfactory estimation value and automatic lane guidance control performance for tractor when the control system is characterized by both time-varying model parameters and uncertain external energy-bounded disturbance. A comparative study is also conducted to investigate cases to show its effectiveness when the hybrid DEKF-ARBFNN state and parameter estimation method is used and when it is not.", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107402", "PubYear": 2024, "Volume": "127", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Liaocheng University, Liaocheng, 252000, China;Corresponding author"}, {"AuthorId": 2, "Name": "Meizhou Chen", "Affiliation": "College of Agricultural Engineering and Food Science, Shandong University of Technology, Zibo, 255000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Aerospace Engineering, Nanyang Technological University, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei Technologies Co.Ltd, Shanghai, 200000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automotive Engineering, Liaocheng University, Liaocheng, 252000, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Agricultural Engineering and Food Science, Shandong University of Technology, Zibo, 255000, China"}], "References": [{"Title": "Object manipulation with a variable-stiffness robotic mechanism using deep neural networks for visual semantics and load estimation", "Authors": "Ertug<PERSON><PERSON> Bayraktar; Cihat Bora Yigit; Pinar <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "13", "Page": "9029", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 110706950, "Title": "CMOT: A cross-modality transformer for RGB-D fusion in person re-identification with online learning capabilities", "Abstract": "Person re-identification (reID) is a crucial aspect of intelligent surveillance systems, enabling the recognition of individuals across non-overlapping camera views. As compared to the RGB modality, RGB-D based reID has the potential to achieve robust and high performance by leveraging rich complementary features of both modalities, making it applicable in various occluded scenarios. However, current multimodal reID approaches often rely on late fusion or feature-level fusion techniques to combine multiple modalities, which limits their ability to proficiently exploit complementary visual and depth-related semantic information and capture complex interactions between unimodal features. To address these limitations, this paper introduces a cross-modality online transformer (CMOT) for RGB-D based person reID with online learning capabilities, which effectively utilizes both RGB and depth modalities for the extraction of spatio-temporal features and fuses across modalities. Our CMOT is composed of three main components: (1) a hypothesis generation module based on a person detector and tracker, (2) dual-stream feature extractors via convolutional neural networks (CNNs), (3) and a fusion transformer based on a self-attention-driven self-attentive modality refinement module (SAMR) and a cross-attention-driven cross-attentive modality interaction module (CAMI) to refine and fuse RGB-D complementary features extracted from the dual-stream, RGB and depth stream, CNNs. Additionally, we introduce a bottleneck enhancement feed-forward block to enhance the model’s representation capability within SAMR and CAMI, significantly reducing parameters and computations compared to the traditional feed-forward network. Moreover, we design the triplet loss function with distance measuring ability for incorporating online learning and finally CMOT works as a few-shot network for reID. Experimental results on three RGB-D person re-identification datasets, namely BIWI RGBD-ID, RobotPKU RGBD-ID, and TVPR2, demonstrate the effectiveness and robustness of CMOT.", "Keywords": "", "DOI": "10.1016/j.knosys.2023.111155", "PubYear": 2024, "Volume": "283", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Intelligent Criminology Lab, National Center of Artificial Intelligence, AlKhawarizmi Institute of Computer Science, University of Engineering and Technology, GT, Road, Lahore, 54890, Punjab, Pakistan;Department of Computer Science, University of Engineering and Technology Lahore, G.T. Road, Lahore, 54890, Punjab, Pakistan;Corresponding author at: Department of Computer Science, University of Engineering and Technology Lahore, G.T. Road, Lahore, 54890, Punjab, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Intelligent Criminology Lab, National Center of Artificial Intelligence, AlKhawarizmi Institute of Computer Science, University of Engineering and Technology, GT, Road, Lahore, 54890, Punjab, Pakistan;Department of Computer Science, University of Engineering and Technology Lahore, G.T. Road, Lahore, 54890, Punjab, Pakistan"}], "References": [{"Title": "Human Re-Identification with a Robot Thermal Camera Using Entropy-Based Sampling", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "1", "Page": "85", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Capturing what human eyes perceive: A visual hierarchy generation approach to emulating saliency-based visual attention for grid-like urban street networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "", "Page": "101454", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "Hetero-Center loss for cross-modality person Re-identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "386", "Issue": "", "Page": "97", "JournalTitle": "Neurocomputing"}, {"Title": "Tensor-based sparse canonical correlation analysis via low rank matrix approximation for RGB-D long-term person re-identification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "17-18", "Page": "11787", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Deep features for person re-identification on metric learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107424", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep understanding of shopper behaviours and interactions using RGB-D vision", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7-8", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Hybrid-attention guided network with multiple resolution features for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "525", "JournalTitle": "Information Sciences"}, {"Title": "AAGCN: Adjacency-aware Graph Convolutional Network for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107300", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Fusion in dissimilarity space for RGB-D person re-identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "", "Page": "100089", "JournalTitle": "Array"}, {"Title": "Cross-modal distillation for RGB-depth person re-identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "216", "Issue": "", "Page": "103352", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Key point-aware occlusion suppression and semantic alignment for occluded person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "606", "Issue": "", "Page": "669", "JournalTitle": "Information Sciences"}, {"Title": "Denseformer: A dense transformer framework for person re‐identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "5", "Page": "527", "JournalTitle": "IET Computer Vision"}, {"Title": "SeSAME: Re-identification-based ambient intelligence system for museum environment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "161", "Issue": "", "Page": "17", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Person re-identification in indoor videos by information fusion using Graph Convolutional Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "118363", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Guard Your Heart Silently", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Dual-granularity feature alignment for cross-modality person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "511", "Issue": "", "Page": "78", "JournalTitle": "Neurocomputing"}, {"Title": "Thermal to Visual Person Re-Identification Using Collaborative Metric Learning Based on Maximum Margin Matrix Factorization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109069", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-granularity cross attention network for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "10", "Page": "14755", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Sparse co-attention visual question answering networks based on thresholds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "1", "Page": "586", "JournalTitle": "Applied Intelligence"}, {"Title": "Reliability modeling and contrastive learning for unsupervised person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "263", "Issue": "", "Page": "110263", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Dual-branch adaptive attention transformer for occluded person re-identification", "Authors": "<PERSON><PERSON> Lu; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "", "Page": "104633", "JournalTitle": "Image and Vision Computing"}, {"Title": "Cross-scale cascade transformer for multimodal human action recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "168", "Issue": "", "Page": "17", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Complementary networks for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "633", "Issue": "", "Page": "70", "JournalTitle": "Information Sciences"}, {"Title": "Structural redundancy reduction based efficient training for lightweight person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "637", "Issue": "", "Page": "118962", "JournalTitle": "Information Sciences"}, {"Title": "Learning consistent region features for lifelong person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "144", "Issue": "", "Page": "109837", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 110706997, "Title": "Mapping integrated crop-livestock systems in Brazil with planetscope time series and deep learning", "Abstract": "Accurate mapping of crops with high spatiotemporal resolution plays a critical role in achieving the Sustainable Development Goals (SDGs), especially in the context of integrated crop-livestock systems (ICLS). Stakeholders can make informed decisions and implement targeted strategies to achieve multiple SDGs related to agriculture, rural development, and sustainable livelihoods by understanding the spatial dynamics of these systems. Accurate information on the extent of ICLS derived from multitemporal remote sensing and emerging map techniques such as deep learning can help in the implementation of sustainable agricultural practices. However, far too little attention has been paid to ICLS map accuracy because it may not be at the forefront of research agendas compared to those of other agricultural practices. This paper aims to map ICLS using high spatiotemporal resolution imagery and deep learning neural network classifiers at two different sites located in Brazil. The pipeline involves four interpretation approaches based on the ICLS class: evaluating deep neural network classifiers with different image composition intervals, explaining commission and omission errors, evaluating the temporal transferability of the method, and evaluating the influence of variables. The study area consists of two locations in São Paulo (study site 1, SS1) and Mato Grosso state (study site 2, SS2), Brazil. We derived nine spectral variables from PlanetScope (PS) images and four metrics through object-based image analysis (OBIA) using two time intervals, 10 and 15 days, to generate the image compositions. These input variables were used in three deep neural network classifiers: convolutional neural network in one dimension (Conv1D), long short-term memory (LSTM), and LSTM with a fully convolutional network (LSTM-FCN). Our results showed that mapping dynamic land use such as ICLS is possible by using high-spatiotemporal-resolution imagery and deep neural network classifiers. The 15-day LSTM-FCN classifier returned the highest map accuracies for both sites, with the following class-level accuracies: producer accuracy (PA) = 97.0% and user accuracy (UA) = 97.0% for SS1 and PA = 82.0% and UA = 96.5% for SS2. Meanwhile, we found map uncertainties arising from the diverse crop calendars and spectro-temporal similarities between ICLS and other land use. The best approaches revealed that temporal generalization was suitable for mapping ICLS, but some classifiers could not generalize due to the inherent characteristics of the class. Most variables were considered efficient for predicting ICLS, although spectral indices revealed better functional relationships, while the PS bands had a lower influence on the predictions. The accuracies achieved with the proposed method represent promising opportunities for the sufficiently accurate mapping of ICLS and other complex crop activities.", "Keywords": "", "DOI": "10.1016/j.rse.2023.113886", "PubYear": 2023, "Volume": "299", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "Inacio T. Bueno", "Affiliation": "Interdisciplinary Center of Energy Planning, University of Campinas, Campinas 13083-896, SP, Brazil;School of Agricultural Engineering, University of Campinas, Campinas 13083-875, SP, Brazil;Corresponding author at: Interdisciplinary Center of Energy Planning, University of Campinas, Campinas 13083-896, SP, Brazil"}, {"AuthorId": 2, "Name": "João F.<PERSON>", "Affiliation": "Interdisciplinary Center of Energy Planning, University of Campinas, Campinas 13083-896, SP, Brazil;Embrapa Digital Agriculture, Brazilian Agricultural Research Corporation, Campinas 13083-886, SP, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Interdisciplinary Center of Energy Planning, University of Campinas, Campinas 13083-896, SP, Brazil;School of Agricultural Engineering, University of Campinas, Campinas 13083-875, SP, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Agricultural Engineering, University of Campinas, Campinas 13083-875, SP, Brazil"}, {"AuthorId": 5, "Name": "Ana P.S.G.D.D. Toro", "Affiliation": "School of Agricultural Engineering, University of Campinas, Campinas 13083-875, SP, Brazil"}, {"AuthorId": 6, "Name": "Gleyce K.D.A. <PERSON>o", "Affiliation": "School of Agricultural Engineering, University of Campinas, Campinas 13083-875, SP, Brazil"}, {"AuthorId": 7, "Name": "Júlio C.D.<PERSON>", "Affiliation": "School of Agricultural Engineering, University of Campinas, Campinas 13083-875, SP, Brazil;Embrapa Digital Agriculture, Brazilian Agricultural Research Corporation, Campinas 13083-886, SP, Brazil"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Interdisciplinary Center of Energy Planning, University of Campinas, Campinas 13083-896, SP, Brazil;School of Agricultural Engineering, University of Campinas, Campinas 13083-875, SP, Brazil"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Embrapa Digital Agriculture, Brazilian Agricultural Research Corporation, Campinas 13083-886, SP, Brazil"}, {"AuthorId": 10, "Name": "Paulo S.G. <PERSON>es", "Affiliation": "Interdisciplinary Center of Energy Planning, University of Campinas, Campinas 13083-896, SP, Brazil"}], "References": [{"Title": "Deep learning in environmental remote sensing: Achievements and challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "241", "Issue": "", "Page": "111716", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Deeply synergistic optical and SAR time series for crop dynamic monitoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "247", "Issue": "", "Page": "111952", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Change detection using deep learning approach with object-based image analysis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "256", "Issue": "", "Page": "112308", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Towards interpreting multi-temporal deep learning models in crop mapping", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112599", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Crop mapping from image time series: Deep learning with multi-scale label hierarchies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112603", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Assessing the potential of using high spatial resolution daily NDVI-time-series from planet CubeSat images for crop monitoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "18", "Page": "7114", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Deep machine learning with Sentinel satellite data to map paddy rice production stages across West Java, Indonesia", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "265", "Issue": "", "Page": "112679", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 110707005, "Title": "Cloud-load forecasting via decomposition-aided attention recurrent neural network tuned by modified particle swarm optimization", "Abstract": "Recent improvements in networking technologies have led to a significant shift towards distributed cloud-based services. However, adequate management of computation resources by providers is vital to maintain the costs of operations and quality of services. A robust system is needed to forecast demand and prevent excessive resource allocations. Extensive literature review suggests that the potential of recurrent neural networks with attention mechanisms is not sufficiently explored and applied to cloud computing. To address this gap, this work proposes a methodology for forecasting load of cloud resources based on recurrent neural networks with and without attention layers. Utilized deep learning models are further optimized through hyperparameter tuning using a modified particle swarm optimization metaheuristic, which is also introduced in this work. To help models deal with complex non-stationary data sequences, the variational mode decomposition for decomposing complex series has also been utilized. The performance of this approach is compared to several state-of-the-art algorithms on a real-world cloud-load dataset. Captured performance metrics ( $$R^2$$ \n \n R \n 2 \n \n , mean square error, root mean square error, and index of agreement) strongly indicate that the proposed method has great potential for accurately forecasting cloud load. Further, models optimized by the introduced metaheuristic outperformed competing approaches, which was confirmed by conducted statistical validation. In addition, the best-performing forecasting model has been subjected to SHapley Additive exPlanations analysis to determine the impact each feature has on model forecasts, which could potentially be a very useful tool for cloud providers when making decisions.", "Keywords": "Cloud-load forecasting; Recurrent neural networks; Attention layers; Particle swarm optimization; Hyper-parameters optimization; Variational mode decomposition.", "DOI": "10.1007/s40747-023-01265-3", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electronics, University of Nis, Nis, Serbia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Technical Sciences, Singidunum University, Belgrade, Serbia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Transport and Traffic Engineering, University of Belgrade, Belgrade, Serbia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Computing, Singidunum University, Belgrade, Serbia; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Computing, Singidunum University, Belgrade, Serbia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Technical Science, University of Pristina in Kosovska Mitrovica, Kosovska Mitrovica, Serbia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics and Computing, Singidunum University, Belgrade, Serbia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Technical Sciences, Singidunum University, Belgrade, Serbia"}], "References": [{"Title": "Chimp optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113338", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A cloud load forecasting model with nonlinear changes using whale optimization algorithm hybrid strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "15", "Page": "10205", "JournalTitle": "Soft Computing"}, {"Title": "A prescription of methodological guidelines for comparing bio-inspired optimization algorithms", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "100973", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A review on job scheduling technique in cloud computing and priority rule based intelligent framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "2309", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Resource scheduling methods for cloud computing environment: The role of meta-heuristics and artificial intelligence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105345", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Evaluating the performance of meta-heuristic algorithms on CEC 2021 benchmark problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "2", "Page": "1493", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Bipartite synchronization for cooperative-competitive neural networks with reaction–diffusion terms via dual event-triggered mechanism", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "550", "Issue": "", "Page": "126498", "JournalTitle": "Neurocomputing"}, {"Title": "Hysteresis quantified control for switched reaction–diffusion systems and its application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "6", "Page": "7451", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 110707024, "Title": "Distributed multi-target tracking and active perception with mobile camera networks", "Abstract": "Smart cameras are an essential component in surveillance and monitoring applications, and they have been typically deployed in networks of fixed camera locations. The addition of mobile cameras, mounted on robots, can overcome some of the limitations of static networks such as blind spots or back-lightning, allowing the system to gather the best information at each time by active positioning. This work presents a hybrid camera system, with static and mobile cameras, where all the cameras collaborate to observe people moving freely in the environment and efficiently visualize certain attributes from each person. Our solution combines a multi-camera distributed tracking system, to localize with precision all the people, with a control scheme that moves the mobile cameras to the best viewpoints for a specific classification task. The main contribution of this paper is a novel framework that exploits the synergies that result from the cooperation of the tracking and the control modules, obtaining a system closer to the real-world application and capable of high-level scene understanding. The static camera network provides global awareness of the control scheme to move the robots. In exchange, the mobile cameras onboard the robots provide enhanced information about the people on the scene. We perform a thorough analysis of the people monitoring application performance under different conditions thanks to the use of a photo-realistic simulation environment. Our experiments demonstrate the benefits of collaborative mobile cameras with respect to static or individual camera setups.", "Keywords": "Multi-camera scene analysis ; Collaborative and autonomous decision making", "DOI": "10.1016/j.cviu.2023.103876", "PubYear": 2024, "Volume": "238", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DIIS - I3A, Universidad de Zaragoza, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cognitive Robotics at TU Delft, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DIIS - I3A, Universidad de Zaragoza, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cognitive Robotics at TU Delft, The Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cognitive Robotics at TU Delft, The Netherlands"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "DIIS - I3A, Universidad de Zaragoza, Spain"}], "References": [{"Title": "Multi-camera multi-player tracking with deep player identification in sports video", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107260", "JournalTitle": "Pattern Recognition"}, {"Title": "Optimal trajectory planning for cinematography with multiple Unmanned Aerial Vehicles", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "103778", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Multi-person multi-camera tracking for live stream videos based on improved motion model and matching cascade", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "492", "Issue": "", "Page": "561", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 110707194, "Title": "Overcoming barriers to integrated management systems via developing guiding principles using G-AHP and F-TOPSIS", "Abstract": "Implementing Integrated Management Systems (IMS) has become a prerequisite for transforming businesses into more competitive and sustainable enterprises. However, the various barriers impede the effective implementation of IMS. Although prior studies have associated several barriers with implementation, they neither elaborate on the degree to which these barriers hinder integration nor offer any strategies to overcome them. To fill this gap in the literature, this study offers a systematic decision-making framework to prioritize barriers and develop various guiding principles to overcome them. First, we conducted a literature review to identify twenty-three sub-barriers nested in five main barrier categories of Resources &amp; Assets, Employees, Implementation, Financial, and Organization Culture. The literature review also helped us to develop fifteen guiding principles. Next, we employed the Grey Analytical Hierarchy Process (G-AHP) method to prioritize barriers and sub-barriers. Finally, the Fuzzy Technique for Order of Preference by Similarity to Ideal Solution (F-TOPSIS) is used to evaluate the guiding principles for sustainable implementation of IMS. The results of the G-AHP method reveal “Resources &amp; Assets”, “Employees”, and “Implementation” as the most critical IMS barriers. Besides, the results of the F-TOPSIS method show that “Encourage standardization of processes”, “Standardize organizational practices and concepts”, and “Engage employees in IMS development” are the most feasible guiding principles to overcome the IMS barriers. Finally, sensitivity analyses were conducted to check the robustness of the results. The contributions of this study help managers and policymakers overcome the barriers of IMS integration while simultaneously fostering sustainability through the implementation of guiding principles.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122305", "PubYear": 2024, "Volume": "239", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cyberspace Research Institute, Shahid Beheshti University, Tehran, Iran"}], "References": [{"Title": "The similarities and divergences between grey and fuzzy theory", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "115812", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 110707195, "Title": "Learning high-dependence Bayesian network classifier with robust topology", "Abstract": "The increase in data variability and quantity makes it urgent for learning complex multivariate probability distributions. The state-of-the-art Tree Augmented Naive Bayes (TAN) classifier uses maximum weighted spanning tree (MWST) to graphically model data with excellent time and space complexity. In this paper, we theoretically prove the feasibility of scaling up one-dependence MWST to model high-dependence relationships, and then propose to apply a heuristic search strategy to improve the fitness of extended topology to data. The newly added edges to each attribute node may provide a local optimal solution . Then ensemble learning is introduced to improve the generalization performance and reduce the sensitivity to variation in training data. The experimental results on 32 benchmark datasets reveal that this highly scalable algorithm inherits the expressive power of TAN and achieves an excellent bias–variance tradeoff, and it also demonstrates competitive classification performance when compared to a range of high-dependence or ensemble learning algorithms .", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122395", "PubYear": 2024, "Volume": "239", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130012, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, Changchun 130012, China"}, {"AuthorId": 2, "Name": "<PERSON>ling Li", "Affiliation": "College of Computer Science and Technology, Jilin University, Changchun 130012, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, Changchun 130012, China;Corresponding author at: College of Computer Science and Technology, Jilin University, Changchun 130012, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Instrumentation and Electrical Engineering, Jilin University, Changchun 130012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, Changchun 130012, China"}], "References": [{"Title": "Instance-based weighting filter for superparent one-dependence estimators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106085", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Learning semi-lazy Bayesian network classifier under the c.i.i.d assumption", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "208", "Issue": "", "Page": "106422", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Attribute and instance weighted naive Bayes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107674", "JournalTitle": "Pattern Recognition"}, {"Title": "Averaged tree-augmented one-dependence estimators", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "7", "Page": "4270", "JournalTitle": "Applied Intelligence"}, {"Title": "Semi-supervised learning for k-dependence Bayesian classifiers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "4", "Page": "3604", "JournalTitle": "Applied Intelligence"}, {"Title": "Stochastic optimization for bayesian network classifiers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "13", "Page": "15496", "JournalTitle": "Applied Intelligence"}, {"Title": "Label augmented and weighted majority voting for crowdsourcing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "606", "Issue": "", "Page": "397", "JournalTitle": "Information Sciences"}, {"Title": "Alleviating the attribute conditional independence and I.I.D. assumptions of averaged one-dependence estimator by double weighting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109078", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Learning causal Bayesian networks based on causality analysis for classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105212", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Explainable Bayesian networks applied to transport vulnerability", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "209", "Issue": "", "Page": "118348", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Attribute augmentation-based label integration for crowdsourcing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "5", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "BIM-based solution to enhance the performance of public-private partnership construction projects using copula bayesian network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "119501", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Forecasting Bike Sharing Demand Using Quantum Bayesian Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "221", "Issue": "", "Page": "119749", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 110707230, "Title": "Cost aware LSTM model for predicting hard disk drive failures based on extremely imbalanced S.M.A.R.T. sensors data", "Abstract": "Failure prediction in a population of hard drives remains challenging due to the extreme class imbalance. Many existing failure prediction methods rely on problematic resampling approaches. Besides, existing deep learning methods in the failure prediction domain typically employ the sigmoid cross-entropy loss function which is not capable of solving the class imbalance problem. To address these challenges, this study proposes long-short term memory networks that leverage the sequential time series self monitoring analysis and reporting technology sensors data provided by the Backblaze data center. These models employ a new, modified focal loss function combined with weighted cross-entropy loss to tackle the negative influence of the class imbalance during training the long-short term memory networks. The long-short term memory networks models are compared to two traditional machine learning algorithms: random forest and logistic regression. This study focuses on daily snapshot data of the Seagate ST4000DM000 hard drive model from the Backblaze data center to maintain the homogeneity of the SMART attributes. Long-short term memory networks with modified focal loss function model provides the highest geometric mean score of 0 . 786 ± 0 . 011 while keeping the failure detection rate higher without compromising the false alarm rate. Hence, it demonstrates that the proposed focal loss function in long-short term memory networks leads to improved performance compared to other approaches under the class imbalance.", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107339", "PubYear": 2024, "Volume": "127", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Weber State University, Ogden, 84408, UT, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Bowling Green State University, Bowling Green, 43402, OH, USA;Corresponding author"}], "References": [{"Title": "A disk failure prediction method based on LSTM network due to its individual specificity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "791", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 110707255, "Title": "Learning Trajectory Tracking for an Autonomous Surface Vehicle in Urban Waterways", "Abstract": "<p>Roboat is an autonomous surface vessel (ASV) for urban waterways, developed as a research project by the AMS Institute and MIT. The platform can provide numerous functions to a city, such as transport, dynamic infrastructure, and an autonomous waste management system. This paper presents the development of a learning-based controller for the Roboat platform with the goal of achieving robustness and generalization properties. Specifically, when subject to uncertainty in the model or external disturbances, the proposed controller should be able to track set trajectories with less tracking error than the current nonlinear model predictive controller (NMPC) used on the ASV. To achieve this, a simulation of the system dynamics was developed as part of this work, based on the research presented in the literature and on the previous research performed on the Roboat platform. The simulation process also included the modeling of the necessary uncertainties and disturbances. In this simulation, a trajectory tracking agent was trained using the proximal policy optimization (PPO) algorithm. The trajectory tracking of the trained agent was then validated and compared to the current control strategy both in simulations and in the real world.</p>", "Keywords": "", "DOI": "10.3390/computation11110216", "PubYear": 2023, "Volume": "11", "Issue": "11", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Scuola di Ingegneria Industriale e dell’Informazione, Politecnico di Milano, 20133 Milan, Italy; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Roboat, 1018 JA Amsterdam, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Scuola di Ingegneria Industriale e dell’Informazione, Politecnico di Milano, 20133 Milan, Italy"}], "References": [{"Title": "Reinforcement Learning-Based Tracking Control of USVs in Varying Operational Conditions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "", "Page": "32", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Learning quadrupedal locomotion over challenging terrain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "47", "Page": "eabc5986", "JournalTitle": "Science Robotics"}, {"Title": "Roboat III: An autonomous surface vessel for urban transportation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "8", "Page": "1996", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 110707256, "Title": "Pole Estimation and Optical Navigation Using Circle of Latitude Projections", "Abstract": "<p>Images of both rotating celestial bodies (e.g., asteroids) and spheroidal planets with banded atmospheres (e.g., Jupiter) can contain features that are well-modeled as a circle of latitude (CoL). At large distances, the projections of these CoLs appear as ellipses in images collected by cameras or telescopes onboard exploration spacecraft. This work shows how CoL projections may be used to determine the pole orientation and covariance for a spinning asteroid. In the case of a known planet modeled as an oblate spheroid, it is shown how similar CoL projections may be used for spacecraft localization. These methods are developed using the principles of projective geometry. Numerical results are provided for simulated images of asteroid <PERSON><PERSON> (for pole orientation) and of Jupiter (for spacecraft localization).</p>", "Keywords": "Asteroids; Gas Giant; Telescopes; Planetary Science and Exploration; Statistical Analysis; Monte Carlo Analysis; Image Processing; Shape Reconstruction; Computing and Informatics; Hydrostatic Equilibrium", "DOI": "10.2514/1.G007529", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Georgia Institute of Technology, Atlanta, Georgia 30332"}], "References": [{"Title": "Latitude Estimation from Jupiter’s Banded Atmosphere", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "46", "Issue": "2", "Page": "410", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 110707272, "Title": "Explainability in Deep Reinforcement Learning: A Review into Current Methods and Applications", "Abstract": "<p>The use of Deep Reinforcement Learning (DRL) schemes has increased dramatically since their first introduction in 2015. Though uses in many different applications are being found, they still have a problem with the lack of interpretability. This has bread a lack of understanding and trust in the use of DRL solutions from researchers and the general public. To solve this problem, the field of Explainable Artificial Intelligence has emerged. This entails a variety of different methods that look to open the DRL black boxes, ranging from the use of interpretable symbolic Decision Trees to numerical methods like Shapley Values. This review looks at which methods are being used and for which applications. This is done to identify which models are the best suited to each application or if a method is being underutilised.</p>", "Keywords": "", "DOI": "10.1145/3623377", "PubYear": 2024, "Volume": "56", "Issue": "5", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, City University of London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, City University of London, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, City University of London, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Defence, Science and Technology Laboratory (Dstl), UK"}], "References": [{"Title": "DRLViz: Understanding Decisions and Memory in Deep Reinforcement Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "3", "Page": "49", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Explainable AI and Reinforcement Learning—A Systematic Review of Current Approaches and Trends", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "48", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Explainable robotic systems: understanding goal-driven actions in a reinforcement learning scenario", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "25", "Page": "18113", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Explainable AI methods on a deep reinforcement learning agent for automatic docking", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "16", "Page": "146", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Explaining autonomous drones: An\n XAI\n journey", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "4", "Page": "e54", "JournalTitle": "Applied AI Letters"}, {"Title": "Event-driven temporal models for explanations - ETeMoX: explaining reinforcement learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "3", "Page": "1091", "JournalTitle": "Software & Systems Modeling"}, {"Title": "Explainable Deep Reinforcement Learning: State of the Art and Challenges", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 110707277, "Title": "The Hybrid Features and Supervised Learning for Batik <PERSON> Classification", "Abstract": "<p>Several countries have traditional textiles as a piece of their cultural heritage. Indonesia has a traditional textile called batik. Central Java is one of the regions producing batik known for its variety of distinctive themes. It has unique designs and several motifs that emphasize the beauty of historic sites. Since the diversity of central java batik motifs and the lack of knowledge from the surrounding community, only a select group of people, especially the batik craftsmen themselves, can recognize these motifs. Consequently, the method to identify the batik according to the primary ornament pattern is required. Therefore, this study proposes a computer vision-based method for classifying batik patterns. The proposed method required discriminating appropriate features to produce optimal results. The discriminating features were constructed based on color, shape, and texture. Those features were derived using the method of Color Moments, Area Based Invariant Moments, Gray Level Co-occurrence Matrix (GLCM), and Local Binary Pattern (LBP). This study’s proposed hybrid features were formed based on the most discriminating and appropriate features. These were yielded by the Correlation-based feature selection (CFS) method. The hybrid features were then fed into several classifiers to determine the batik pattern. The pattern consists of ten classes: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Blekok, Blekok Warak, Gambang Semarangan, Kembang Sepatu, Semarangan, Tugu Muda, and Warak Beras Utah. Based on the experimental results, the most optimal predicted class of the batik pattern was generated using the Artificial Neural Network (ANN) classifier. It was indicated by achieving an accuracy value of 99.76% based on the 3000 images (each class consists of 300 images) with cross-validation using a k-fold value of 10. This study has proved that the hybrid features incorporated with ANN can be selected as a suitable model to classify the batik patterns.</p>", "Keywords": "", "DOI": "10.1145/3631131", "PubYear": 2024, "Volume": "17", "Issue": "2", "JournalId": 23137, "JournalTitle": "Journal on Computing and Cultural Heritage", "ISSN": "1556-4673", "EISSN": "1556-4711", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology and Industry, Universitas Stikubank, Semarang, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Mulawarman University, Samarinda, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology and Industry, Universitas Stikubank, Semarang, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, Mulawarman University, Samarinda, Indonesia"}], "References": [{"Title": "Islamic Geometric Patterns in Higher Dimensions", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "3", "Page": "777", "JournalTitle": "Nexus Network Journal"}, {"Title": "Texture classification of fabric defects using machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "4", "Page": "4390", "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)"}, {"Title": "A Novel Reliable Approach For Image Batik Classification That Invariant With Scale And Rotation Using MU2ECS-LBP Algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "863", "JournalTitle": "Procedia Computer Science"}, {"Title": "Descending stairs and floors classification as control reference in autonomous smart wheelchair", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "6040", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Fabric defect detection based on completed local quartet patterns and majority decision algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116827", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Face recognition algorithm based on stack denoising and self-encoding LBP", "Authors": "<PERSON><PERSON><PERSON> Lu; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "1", "Page": "501", "JournalTitle": "Journal of Intelligent Systems"}, {"Title": "Sketch2Photo: Synthesizing photo-realistic images from sketches via global contexts", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105608", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 110707296, "Title": "ANALYSIS OF THE RATES OF <PERSON><PERSON><PERSON><PERSON> GROWTH IN THE CITY OF SAINT PETERSBURG: INDUSTRY STRUCTURE AND DYNAMICS", "Abstract": "", "Keywords": "", "DOI": "10.21685/2227-8486-2023-3-1", "PubYear": 2023, "Volume": "", "Issue": "3", "JournalId": 78084, "JournalTitle": "MODELS, SY<PERSON>EM<PERSON>, NET<PERSON>OR<PERSON> IN ECONOMICS, ENGINEERING, NATURE AND SOCIETY", "ISSN": "2227-8486", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Saint Petersburg State University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Saint Petersburg State University"}], "References": []}, {"ArticleId": 110707308, "Title": "Numerical Approximations of Diblock Copolymer Model Using a Modified Leapfrog Time-Marching Scheme", "Abstract": "<p>An efficient modified leapfrog time-marching scheme for the diblock copolymer model is investigated in this paper. The proposed scheme offers three main advantages. Firstly, it is linear in time, requiring only a linear algebra system to be solved at each time-marching step. This leads to a significant reduction in computational cost compared to other methods. Secondly, the scheme ensures unconditional energy stability, allowing for a large time step to be used without compromising solution stability. Thirdly, the existence and uniqueness of the numerical solution at each time step is rigorously proven, ensuring the reliability and accuracy of the method. A numerical example is also included to demonstrate and validate the proposed algorithm, showing its accuracy and efficiency in practical applications.</p>", "Keywords": "", "DOI": "10.3390/computation11110215", "PubYear": 2023, "Volume": "11", "Issue": "11", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Computational Science Research Center, Beijing 100193, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Beijing University of Technology, Beijing 100124, China; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing Computational Science Research Center, Beijing 100193, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Computational Science Research Center, Beijing 100193, China"}], "References": []}, {"ArticleId": 110707321, "Title": "Occluded person re-identification with deep learning: A survey and perspectives", "Abstract": "Person re-identification (Re-ID) technology plays an increasingly crucial role in intelligent surveillance systems. Widespread occlusion significantly impacts the performance of person Re-ID. Occluded person Re-ID refers to a pedestrian matching method that deals with challenges such as pedestrian information loss, noise interference, and perspective misalignment. It has garnered extensive attention from researchers. Over the past few years, several occlusion-solving person Re-ID methods have been proposed, tackling various sub-problems arising from occlusion. However, there is a lack of comprehensive studies that compare, summarize, and evaluate the potential of occluded person Re-ID methods in detail. In this review, we commence by offering a meticulous overview of the datasets and evaluation criteria utilized in the realm of occluded person Re-ID. Subsequently, we undertake a rigorous scientific classification and analysis of existing deep learning-based occluded person Re-ID methodologies, examining them from diverse perspectives and presenting concise summaries for each approach. Furthermore, we execute a systematic comparative analysis among these methods, pinpointing the state-of-the-art solutions, and provide insights into the future trajectory of occluded person Re-ID research.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.122419", "PubYear": 2024, "Volume": "239", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Semiconductors, Chinese Academy of Sciences, Beijing, 100083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Semiconductors, Chinese Academy of Sciences, Beijing, 100083, China;Center of Materials Science and Optoelectronics Engineering & School of Microelectronics, University of Chinese Academy of Sciences, Beijing, 100083, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Software, Xinjiang University, Xinjiang, 830000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Semiconductors, Chinese Academy of Sciences, Beijing, 100083, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology, Halmstad University, Halmstad, 30118, Sweden"}], "References": [{"Title": "Effective person re-identification by self-attention model guided feature learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104832", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Dual attention-based method for occluded person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106554", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "SSS-PR: A short survey of surveys in person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "143", "Issue": "", "Page": "50", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Deep 3D human pose estimation: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "210", "Issue": "", "Page": "103225", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Cross-modal distillation for RGB-depth person re-identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "216", "Issue": "", "Page": "103352", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Transformers in Vision: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "PAFM: pose-drive attention fusion mechanism for occluded person re-identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "8241", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep learning-based person re-identification methods: A survey and outlook of recent works", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "", "Page": "104394", "JournalTitle": "Image and Vision Computing"}, {"Title": "Short range correlation transformer for occluded person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "20", "Page": "17633", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 110707332, "Title": "markovMSM: An R Package for Checking the Markov Condition in Multi-State Survival Data", "Abstract": "Multi-state models can be used to describe processes in which an individual moves through a finite number of states in continuous time. These models allow a detailed view of the evolution or recovery of the process and can be used to study the effect of a vector of explanatory variables on the transition intensities or to obtain prediction probabilities of future events after a given event history. In both cases, before using these models, we have to evaluate whether the Markov assumption is tenable. This paper introduces the markovMSM package, a software application for R, which considers tests of the Markov assumption that are applicable to general multi-state models. Three approaches using existing methodology are considered: a simple method based on including covariates depending on the history; methods based on measuring the discrepancy of the non-Markov estimators of the transition probabilities to the Markovian A<PERSON><PERSON>-<PERSON> estimators; and, finally, methods that were developed by considering summaries from families of log-rank statistics where individuals are grouped by the state occupied by the process at a particular time point. The main functionalities of the markovMSM package are illustrated using real data examples. © (2023). All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2023-032", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "EPIUnit, Institute of Public Health of the University of Porto\r\n(ISPUP)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics & Centre of Mathematics, University of\r\nMinho"}], "References": []}, {"ArticleId": 110707346, "Title": "Non-Parametric Analysis of Spatial and Spatio-Temporal Point Patterns", "Abstract": "The analysis of spatial and spatio-temporal point patterns is becoming increasingly necessary, given the rapid emergence of geographically and temporally indexed data in a wide range of fields. Non-parametric point pattern methods are a highly adaptable approach to answering questions about the real-world using complex data in the form of collections of points. Several methodological advances have been introduced in the last few years. This paper examines the current methodology, including the most recent developments in estimation and computation, and shows how various R packages can be combined to run a set of non-parametric point pattern analyses in a guided and intuitive way. An example of non-specific gastrointestinal disease reports in Hampshire, UK, from 2001 to 2003 is used to illustrate the methods, procedures and interpretations. © (2023). All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2023-025", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "Jonatan <PERSON><PERSON>", "Affiliation": "King <PERSON> University of Science and Technology (KAUST)"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "King <PERSON> University of Science and Technology (KAUST)"}], "References": []}, {"ArticleId": 110707353, "Title": "DETERMINATION OF THE OPTIMAL CONTROLL<PERSON><PERSON> KEY INDICATOR OF CALL CENTER IN ORDER TO INCREASE EFFICIENCY FOR GENERATING INCOME", "Abstract": "<p>This paper focuses on call centers, which have become a common means of communication with potential customers in various companies. Specifically, this paper analyzes call center data and the importance of assessing key indicators for evaluating call center performance. The questions this paper addresses are the criteria for evaluating call center quality and the methods for analyzing call center data. Previous research has shown the significance of call centers as the \"face of the company,\" with the quality of their work reflecting how efficiently a company will serve its customers ' requests in the future. The main goal of this paper is to fill a gap in previous research by identifying the main controlled key indicator for call center quality and to suggest ways to improve efficiency. By using analytical methods to examine call center data, this paper identifies the most important criteria for call center quality and provides recommendations for enhancing service quality. The main findings of this paper show the importance of call center operator performance in determining call center performance which affects company revenue. By evaluating key indicators such as the number of operators, this paper demonstrates how call centers can reduce service costs and improve efficiency. During the analysis using call center data for two years, it turned out that the company had expenses 1/3 of the total amount of maintenance compared to the previous year, which is not effective in terms of economy. Operational planning has a direct impact on operators’ costs and the main cost component is the hourly cost of operators. If optimal planning turns out to be at least 10% better than the arrangement set in the call center, company will save a good amount. The significance of this paper lies in its potential to improve the quality of service in call centers and its contribution to the field of customer service management. By providing insight into the importance of call center efficiency, this research offers recommendations for predicting the optimal number of operators to improve the customer experience with reducing service costs.</p>", "Keywords": "call center;indicators;operator;call service center;data analysis;machine learning;call center quality;optimization", "DOI": "10.37943/15SNLS1783", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 75769, "JournalTitle": "Scientific Journal of Astana IT University", "ISSN": "2707-9031", "EISSN": "2707-904X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Astana IT University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Astana IT University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Abai Kazakh National Pedagogical University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Astana IT University"}], "References": []}, {"ArticleId": 110707362, "Title": "Shuffle SwishNet-181: COVID-19 diagnostic framework using ECG images", "Abstract": "Abstract </h3> <p>Early and precise detection of COVID-19 holds significant benefits, particularly in facilitating the prompt isolation of infected individuals and helping to control the spread of this disease as vaccinated persons also got infected from COVID-19. The research community has explored various COVID-19 diagnostic tools operated on different imaging modalities like X-rays and CT scans apart from conventional PCR testing which often takes several hours to get the results. Existing studies on ECG-based COVID-19 diagnostics are limited even though this modality is quickly available as compared to CT scans and X-rays. Moreover, our preliminary analysis suggests that ECG images can also be used to study the correlation of COVID-19 with cardio diseases, which is not possible in the case of X-rays and CT scans. Moreover, current ECG-based COVID-19 diagnostics approaches often report an issue of low detection accuracy and focus more on binary classification. To overcome these challenges, we developed an effective COVID-19 diagnostic tool by proposing a novel Shuffle SwishNet-181 deep learning-based model. During the pre-processing, the background is subtracted from the signals and combined these signals in a hexaxial way. Shuffle SwishNet-181 extracts the distinctive deep features and accurately classifies the ECG images into COVID-19, normal, myocardial infarction (MI), abnormal heartbeat patients (HB), and patients who have a history of myocardial infarction (PMI). Moreover, the Score-cam technique is employed to visualize the working of the proposed model by showing the top priority features extracted by the Shuffle SwishNet-181 model. The rigorous experimentation is performed on a publicly available ECG imaging dataset to demonstrate the effectiveness of the COVID-19 diagnostic framework. The proposed model achieved an accuracy of 99% in the case of COVID-19 vs. Normal, 99.4% in the case of COVID-19 vs. MI, 98.8% in the case of COVID-19 vs. HB, and 98.7% in the case of COVID-19 vs. PMI. For multiclass classification, the proposed model achieved 91.6% accuracy. Experimental results show the reliability of the method for binary and multi-class classification of COVID-19. Explainability analysis proved that the proposed model precisely focuses on salient features for classification.</p>", "Keywords": "COVID-19 detection; Cardiovascular patients; ECG; Shuffle SwishNet-181", "DOI": "10.1007/s11042-023-17579-w", "PubYear": 2024, "Volume": "83", "Issue": "16", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering, University of Engineering and Technology, Taxila, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, University of Engineering and Technology, Taxila, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Technology, Jazan University, Jazan, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Information Technology, Jazan University, Jazan, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, National Textile University, Faisalabad, Pakistan; Corresponding author."}], "References": [{"Title": "ECG Images dataset of Cardiac and COVID-19 Patients", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "34", "Issue": "", "Page": "106762", "JournalTitle": "Data in Brief"}, {"Title": "COVID-19 detection in X-ray images using convolutional neural networks", "Authors": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>-Aria<PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "", "Page": "100138", "JournalTitle": "Machine Learning with Applications"}, {"Title": "ECG-COVID: An end-to-end deep model based on electrocardiogram for COVID-19 detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "324", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 110707406, "Title": "Powder loading rate impact on the performances of sintered MnZn ferrites fabricated by powder injection moulding", "Abstract": "<p>Power electronics’ power density increase mainly depends on size reduction of passive components. Development of compact solutions having two or more magnetic functions (filtering, storage, and signal transformation) on a single-magnetic core or of cores having hollowed structures for thermal integration purposes may require advanced geometries. Ferrites’ conventional fabrication technique (uniaxial compression) presents limitations for the production of cores having advanced geometrical features. Additive manufacturing or powder injection moulding (PIM) are promising techniques that allow for the shaping of magnetics having unprecedented geometrical features. These last fabrication techniques rely on the principle of mixing a powder with a binder system made of polymers to make the parts injectable or printable. The binder system may generally constitute at least 10 wt% of the total formulation’s content (much higher than for the conventional process: 2–3 wt%). The goal of this study was to analyze the impact of the binder amount (11.5, 15.3, 21.3 and 29.6 wt%, what corresponds to a MnZn ferrite powder loading rate of 88.5, 84.7, 78.7 and 70.4 wt%, respectively) inside a formulation designed for PIM. The formulations were made by extrusion, then injected in a pressing machine, debinded and finally sintered at same time (four different PIM samples per batch) in a furnace having partial oxygen pressure control. Our results showed that the power losses were not the lowest ones for the samples having the highest loading rates. The cores fabricated with a loading rate of 84.7 wt% (50 vol%) generated the lowest losses (on the whole frequencies range; between 100 and 1000 kHz/50 mT and temperatures range; between 25 and 110 °C). This behaviour was attributed to lower hysteresis losses (better magnetic phase purity). The power losses of sintered ring-shape cores achieved 88 mW/cm<sup>3</sup> at 500 kHz/50 mT/85 °C. The losses that have been obtained for the parts fabricated by PIM were similar to the ones given by the ferrite powder supplier (conventional fabrication). These results pave the way to the fabrication of advanced cores in applications where a high level of compactness will be required as for on-board-chargers for battery electric vehicles applications.</p>", "Keywords": "Ferrite; Feedstock; Powder loading; Power losses; Powder injection moulding", "DOI": "10.1007/s00170-023-12520-9", "PubYear": 2023, "Volume": "129", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Grenoble Alpes University, CEA-LITEN, Grenoble Cedex 9, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Grenoble Alpes University, CEA-LITEN, Grenoble Cedex 9, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Grenoble Alpes University, CEA-LITEN, Grenoble Cedex 9, France"}], "References": []}, {"ArticleId": 110707431, "Title": "Comparison of machine learning algorithms used to catalog Google Appstore", "Abstract": "Background: Google Play Store is a popular Android app store where users’ reviews and ratings provide valuable insights. As part of application development, clients and app designers have a significant impact on the market. Accurately predicting market trends is critical to the success of applications, and this is where information mining comes in. By evaluating various factors such as application name, pricing, reviews, and category, we can predict which types of apps are most likely to be successful. Methods: To do this, we can use machine learning techniques that help us analyze data from diverse metrics and identify relationships. In this work, five different types of existing machine learning computation techniques like decision tree algorithm (DTA), support vector machine (SVM), random forest (RF), stochastic gradient boosting (SGB) and gated recurrent unit (GRU) were used to show a comprehensive comparison between the models to describe and forecast the structure of Android Market applications. Through these methods, we can extract a wealth of information to make wise decisions and present data more effectively. Results: Our findings have shown that the GRU-based technique performance accuracy of our predictions is high, with an average of 89.4465%, which can be best visualized as an unusual forest picture better than the four baseline algorithms. Among all the five techniques, GRU classifies 5,616 apps and provides the most precise results for both users and developers, whereas the other classifiers only classified 3,744 apps. Conclusions: According to the result analysis, we can conclude that all five machine learning algorithms were capable of analysing the android market, GRU outperforms in terms of accuracy. © Journal of Medical Artificial Intelligence. All rights reserved.", "Keywords": "classification; gated recurrent unit (GRU); Google Appstore; machine learning; ratings", "DOI": "10.21037/jmai-23-58", "PubYear": 2023, "Volume": "6", "Issue": "", "JournalId": 57593, "JournalTitle": "Journal of Medical Artificial Intelligence", "ISSN": "", "EISSN": "2617-2496", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Informatics, Berlin School of Business & Innovation (BSBI), Berlin, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Lovely Professional University, Phagwara, India"}], "References": []}, {"ArticleId": 110707476, "Title": "EENet: embedding enhancement network for compositional image-text retrieval using generated text", "Abstract": "<p>In this paper, we consider the compositional image-text retrieval task, which searches for appropriate target images given a reference image with feedback text as a query. For instance, when a user finds a dress on an E-commerce site that meets all their needs except for the length and decoration, the user can give sentence-form feedback, e.g., \"I like this dress, but I wish it was a little shorter and had no ribbon,\" to the system. This is a practical scenario for advanced retrieval systems and is applicable to user interactive search systems or E-commerce systems. To tackle this task, we propose a model, the Embedding Enhancement Network (EENet), which includes a text generation module and an image feature enhancement module using the generated text. While the conventional works mainly focus on developing an efficient composition module of a given image and text query, EENet actively generates an additional textual description to enhance the image feature vector in the embedding space, which is inspired by the human ability to recognize an object using a visual sensor and prior textual information. Also, a new training loss is introduced to ensure that images and additional generated texts are well combined. The experimental results show that the EENet achieves considerable improvement on retrieval performance evaluations; for the Recall@1 metric, it improved by 3.4% in Fashion200k and 1.4% in MIT-States over the baseline model.</p>", "Keywords": "Compositional Image-Text Retrieval; Image-Captioning; Joint embedding; Visual Feature Enhancement; Textual Feature Generation", "DOI": "10.1007/s11042-023-17531-y", "PubYear": 2024, "Volume": "83", "Issue": "16", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Kyungpook National University, Daegu, Korea"}, {"AuthorId": 2, "Name": "Hyeyoung Park", "Affiliation": "School of Computer Science and Engineering, Kyungpook National University, Daegu, Korea; Corresponding author."}], "References": []}, {"ArticleId": 110707499, "Title": "Disentangled representation learning for collaborative filtering based on hyperbolic geometry", "Abstract": "In the realm of recommender systems, the exploration of hyperbolic geometry-based embeddings for users and items has emerged as a promising avenue, particularly in the context of collaborative filtering through graph convolution networks. Despite the advancements in this domain, there are two significant questions have received limited attention: (i) Most hyperbolic geometry-based methods ignore the influence of different users’ intents on the preferences of historical behaviors; (ii) They usually learn high-dimensional embeddings akin to Euclidean geometry-based methods, without taking good advantage of the capacity of hyperbolic spaces. To tackle these limitations head-on, we propose a novel method called Disentangled Hyperbolic Collaborative Filtering (DHCF). DHCF learns multiple embeddings in different low-dimensional hyperbolic spaces, enabling the disentanglement of users’ intents and the separate modeling of user and item representations. Moreover, we design an intent-aware graph reconstruction module, which adaptively allocates intent-aware relation strengths to build a dynamic interaction graph. Additionally, this module reduces the risk of oversmoothing in low-dimension spaces, facilitating the stacking of multiple graph aggregation layers. To the best of our knowledge, our method achieves competitive performance compared to state-of-the-art approaches.", "Keywords": "", "DOI": "10.1016/j.knosys.2023.111135", "PubYear": 2023, "Volume": "282", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Jiangsu Provincial Engineering Laboratory of Pattern Recognition and Computational Intelligence, Jiangnan University, Wuxi, 214122, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Jiangsu Provincial Engineering Laboratory of Pattern Recognition and Computational Intelligence, Jiangnan University, Wuxi, 214122, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Process Control for Light Industry (Ministry of Education), Jiangnan University, Wuxi, 214122, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Advanced Process Control for Light Industry (Ministry of Education), Jiangnan University, Wuxi, 214122, China"}, {"AuthorId": 5, "Name": "Jun Kong", "Affiliation": "Key Laboratory of Advanced Process Control for Light Industry (Ministry of Education), Jiangnan University, Wuxi, 214122, China"}], "References": [{"Title": "Cloud Pricing Models", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A deep reinforcement learning based long-term recommender system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106706", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "User preference and embedding learning with implicit feedback for recommender systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "2", "Page": "568", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Graph Fusion Network for Text Classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107659", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Genetic-GNN: Evolutionary architecture search for Graph Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "247", "Issue": "", "Page": "108752", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-relational graph attention networks for knowledge graph completion", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109262", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Graph-ICF: Item-based collaborative filtering based on graph neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109208", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 110707507, "Title": "Solving the C-Shaped Ring Type Patience Cube as Planning Problem", "Abstract": "정확한 힘과 방향을 요구하는 작업이 주어졌을 때, 사람은 강화학습과 유사한 휴리스틱 방법론을 통해 작업을 수행한다. 따라서, 이러한 사람의 의사결정-조작의 과정을 강화학습으로 구현하여 로봇에게 적용하여준다면 정교한 손동작이 요구되는 작업을 성공적으로 수행할 수 있을 것으로 기대할 수 있다. 본 논문은 사람의 정교한 손놀림이 요구되는 C형 고리 patience cube를 로봇으로 해결하기 위한 강화학습 기반 플래너 설계 기법을 제안한다. 먼저 두 축을 기준으로 회전하는 평평한 판과 그 위에서 구르는 공에 대한 운동 방정식을 구하였다. 이후, C형 고리 patience cube 문제를 C형 고리의 입구로 공을 가져오는 플래닝 문제와 그 영역으로부터 공을 고리 안으로 넣는 플래닝 문제로 구성하였다. 이어서, 각 플래닝 문제를 해결하기 위한 마르코프 의사 결정(MDP) 튜플(상태, 행동, 보상)을 정의하였다. 에이전트(플래너)는 가상환경에서 강화학습 알고리즘(PPO)을 통해 학습되었다. 결과적으로, 가상환경에서 학습된 에이전트가 가상환경에서 C형 고리 patience cube문제를 잘 해결할 수 있음을 확인하였다. 또한, 실제 환경의 양팔 로봇에 이식하였을 때도 주어진 C형 고리 patience cube 문제를 해결할 수 있음을 확인하였다. 이를 통해, 본 논문에서 제안된 방법론으로, 다양한 정교한 손동작 문제를 kinodynamic 플래닝 문제로 정의하여 해결할 수 있는 가능성을 보였다.", "Keywords": "Reinforcement Learning;Dexterous Manipulation;Kinodynamic Path Planning", "DOI": "10.5391/JKIIS.2023.33.5.477", "PubYear": 2023, "Volume": "33", "Issue": "5", "JournalId": 15630, "JournalTitle": "Journal of Korean Institute of Intelligent Systems", "ISSN": "1976-9172", "EISSN": "2288-2324", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 110707629, "Title": "Investigation of the effects of process parameters on hydrodynamic deep drawing of AL-1050 sheet with indentations using genetic algorithm–based optimization", "Abstract": "<p>Producing conical parts from sheet metal is a highly intricate process that poses significant challenges in the manufacturing industry. To investigate the ductility of an AL-1050 conical part with 12 indentations, the present study employed both experimental and numerical methods, utilizing hydroforming deep drawing with radial pressure (HDDRP). Specifically, the study analyzed the effects of the pressure path, wherein the punch moves towards the die, on the punch force and the distribution of thickness. The study findings indicated an inverse relationship between pressure and cup thickness, with higher pressure resulting in reduced thickness, and the force-displacement curve revealed that the punch force initially increased and then decreased due to an increase in the deformation resistance of the sheet. Additionally, it was observed that when the angle of the conical section was less than 35°, workpiece rupture occurred. To optimize the process parameters, a genetic algorithm–based approach was implemented. The process parameters were fine-tuned by genetic algorithm–based optimization to achieve an optimal outcome.</p>", "Keywords": "Hydroforming deep drawing; Indentation; Thickness distribution; Genetic algorithm", "DOI": "10.1007/s00170-023-12480-0", "PubYear": 2023, "Volume": "129", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Pusan National University, Busan, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Pusan National University, Busan, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Islamic Azad University Sari Branch, Sari, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Islamic Azad University Sari Branch, Sari, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, The University of Waikato, Hamilton, New Zealand"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Pusan National University, Busan, South Korea"}], "References": [{"Title": "Manufacturing of bent tubes with non-uniform curvature and cross-section using a novel hydroforming die: experimental, finite element analysis, and optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1683", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 110707682, "Title": "Fabrication of ZnO-SnO2 heterojunction inverse opal photonic balls for chemiresistive acetone sensing", "Abstract": "The development of metal oxide semiconductor-based chemiresistive gas sensors with ultra-fast response/recovery for toxic and flammable gases is of great importance, but still challenging. Here, ZnO-SnO<sub>2</sub> heterojunction three-dimensional (3D) inverse opal photonic crystal balls (IOPBs) were fabricated based on rapid and liquid waste-free spray drying. The spherical three-dimensional (3D) ordered macroporous skeleton can provide more active sites and faster gas diffusion channels. Furthermore, the n-n heterojunction established by ZnO and SnO<sub>2</sub> is favorable for electron transport. The response (Ra/Rg) of IOPBs to 50 ppm acetone reaches 40.3 at an optimal operating temperature of 260 °C when the Zn/Sn atomic ratio is 1:4, which is 5 times higher than that of the pure SnO<sub>2</sub> sensor. Response/recovery time also reduces from 23/47 s for pure SnO<sub>2</sub> to 6/10 s. In addition, the IOPBs gas sensors also exhibit good selectivity and excellent long-term stability. This work provides a straightforward and versatile synthetic route for the preparation of 3D IOPBs to explore novel gas sensors with ultra-fast response speed.", "Keywords": "", "DOI": "10.1016/j.snb.2023.134887", "PubYear": 2024, "Volume": "400", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China;State Key Laboratory of Fine Chemicals, Dalian University of Technology, Dalian 116024, China;Corresponding author at: School of Chemical Engineering, Zhengzhou University, Zhengzhou 450001, China"}], "References": [{"Title": "SnO2/ZnSnO3 double-shelled hollow microspheres based high-performance acetone gas sensor", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "332", "Issue": "", "Page": "129212", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Facile synthesis of ZnO-SnO2 hetero-structured nanowires for high-performance NO2 sensing application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "333", "Issue": "", "Page": "129613", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "In-situ generated TiO2/α-Fe2O3 heterojunction arrays for batch manufacturing of conductometric acetone gas sensors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "340", "Issue": "", "Page": "129926", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Gas sensor based on cobalt-doped 3D inverse opal SnO2 for air quality monitoring", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "350", "Issue": "", "Page": "130807", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly selective ppm level LPG sensors based on SnO2-ZnO nanocomposites operable at low temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "377", "Issue": "", "Page": "133080", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A novel flexible substrate-free NH3 sensing membrane based on PANI covered rGO functionalized fiber", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "380", "Issue": "", "Page": "133307", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Enhanced xylene detection performance of colloidal quantum dots modified tungsten oxide composites", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "390", "Issue": "", "Page": "133984", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Pompon-like Ni doped ZnO mesoporous microspheres for conductometric NO2 sensing properties at room temperature", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "394", "Issue": "", "Page": "134388", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 110707759, "Title": "SYSTEMATIC DATA PROCUREMENT IN AN OWL-<PERSON><PERSON><PERSON>DED INFORMATION AND ANALYTICAL FRAMEWORK FOR THE MONITORING OF WATER RESOURCES IN THE ILE-BALKHASH BASIN", "Abstract": "<p>The world is facing an escalating water shortage crisis, with dire consequences for ecosystems, human health, and socio-economic development. This article explores the multifaceted nature of the water shortage problem of Ile-Balkhash basin that falls under the jurisdiction of the Balkhash-Alakol Republic of Kazakhstan, its underlying causes, and the complex web of challenges it presents. The predicament in the Ile-Balkhash basin is a complex interplay of various factors. Climate change has led to erratic precipitation patterns, exacerbating the problem. The simultaneous rise in population places additional stress on the already limited water resources. Moreover, inefficient water management practices have perpetuated the issue, hindering the equitable distribution of water. The challenge of conducting a comprehensive basin analysis is a formidable task due to the numerous variables and indicators involved. It demands an enormous amount of time and effort. To address this issue, a web application framework integrated with the Web Ontology Language database, allowing for the execution of advanced queries to extract valuable insights from various objects and indicators, has been developed. The database underpinning this system is meticulously compiled, drawing upon data from the Hydrological monitoring of water bodies and the National Hydrometeorological Service of the Republic of Kazakhstan. These sources provide critical data that forms the bedrock of the analysis. Recognizing the importance of data storage and management in this endeavor, integrated components have been established. These components play a pivotal role in structuring the diverse data sources and maintaining their currency. The water shortage issue in the Ile-Balkhash basin serves as a stark reminder of the urgency with which such crises must be addressed. The tools and methods offer hope and underscore global water management sustainability.</p>", "Keywords": "monitoring of water resources;owl ontology;Ile-Balkhash basin;geo-data management;water shortage crisis;data package", "DOI": "10.37943/15JUNJ2506", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 75769, "JournalTitle": "Scientific Journal of Astana IT University", "ISSN": "2707-9031", "EISSN": "2707-904X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Farabi Kazakh National University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Farabi Kazakh National University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Riga Technical University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Farabi Kazakh National University, Kazakhstan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Al-Farabi Kazakh National University"}], "References": []}, {"ArticleId": 110707768, "Title": "Triple Dual Learning for Opinion-based Explainable Recommendation", "Abstract": "<p> Recently, with the aim of enhancing the trustworthiness of recommender systems, explainable recommendation has attracted much attention from the research community. Intuitively, users’ opinions towards different aspects of an item determine their ratings (i.e., users’ preferences) for the item. Therefore, rating prediction from the perspective of opinions can realize personalized explanations at the level of item aspects and user preferences. However, there are several challenges in developing an opinion-based explainable recommendation: (1) The complicated relationship between users’ opinions and ratings. (2) The difficulty of predicting the potential (i.e., unseen) user-item opinions because of the sparsity of opinion information. To tackle these challenges, we propose an overall preference-aware opinion-based explainable rating prediction model by jointly modeling the multiple observations of user-item interaction (i.e., review, opinion, rating). To alleviate the sparsity problem and raise the effectiveness of opinion prediction, we further propose a triple dual learning-based framework with a novelly designed triple dual constraint . Finally, experiments on three popular datasets show the effectiveness and great explanation performance of our framework. </p>", "Keywords": "", "DOI": "10.1145/3631521", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 12861, "JournalTitle": "ACM Transactions on Information Systems", "ISSN": "1046-8188", "EISSN": "1558-2868", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, China and University of Chinese Academy of Sciences, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Thrust of Artificial Intelligence, The Hong Kong University of Science and Technology (Guangzhou), China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence, Beihang University, China and Zhongguancun Laboratory, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, China and University of Chinese Academy of Sciences, China"}, {"AuthorId": 5, "Name": "Zhulin An", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, China"}], "References": [{"Title": "Efficient Neural Matrix Factorization without Sampling for Recommendation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Graph Neural Networks in Recommender Systems: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "On the Relationship between Explanation and Recommendation: Learning to Rank Explanations for Improved Performance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 110707771, "Title": "Towards white box modeling of compressive strength of sustainable ternary cement concrete using explainable artificial intelligence (XAI)", "Abstract": "Since the production of sustainable ternary cement concrete (TCC) involves a large range of constituents which can affect the compressive strength (CS) of TCC in different ways, the evaluation of CS in a unified manner is necessary. Unlike the black box machine learning (ML) models, the predictions of CS of TCC using local explanations to global understanding with explainable artificial intelligence (XAI) models have been investigated in a systematic approach by means of SHapley Additive exPlanations (SHAP). For this, two conventional ML and three ensemble ML models were used. Hyper-parameter tuning was also carried out. The ensemble ML models had a higher accuracy than the conventional ML models . <PERSON> diagram, model evaluation parameters and the SHAP values were used to interpret machine learning model predictivity. From insights obtained through local explanations, it can be concluded that predictions made by black box models should be used carefully.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110997", "PubYear": 2023, "Volume": "149", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, ZHCET AMU, Aligarh 202002, UP, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, ZHCET AMU, Aligarh 202002, UP, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, ZHCET AMU, Aligarh 202002, UP, India"}], "References": [{"Title": "Developing GEP tree-based, neuro-swarm, and whale optimization models for evaluation of bearing capacity of concrete-filled steel tube columns", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "1", "JournalTitle": "Engineering with Computers"}, {"Title": "Ensemble approach based on bagging, boosting and stacking for short-term prediction in agribusiness time series", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105837", "JournalTitle": "Applied Soft Computing"}, {"Title": "Applying a meta-heuristic algorithm to predict and optimize compressive strength of concrete samples", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "1133", "JournalTitle": "Engineering with Computers"}, {"Title": "Concrete compressive strength using artificial neural networks", "Authors": "Panagiot<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "15", "Page": "11807", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A novel artificial intelligence technique to predict compressive strength of recycled aggregate concrete using ICA-XGBoost model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3329", "JournalTitle": "Engineering with Computers"}, {"Title": "From local explanations to global understanding with explainable AI for trees", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "56", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "A comparative study of ANN and ANFIS models for the prediction of cement-based mortar materials compressive strength", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "9", "Page": "4501", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improving performance of deep learning models with axiomatic attribution priors and expected gradients", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "7", "Page": "620", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Perturbation-based methods for explaining deep neural networks: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "228", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Efficient computation of counterfactual explanations and counterfactual metrics of prototype-based classifiers", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "304", "JournalTitle": "Neurocomputing"}, {"Title": "Unbox the black-box for the medical explainable AI via multi-modal and multi-centre data fusion: A mini-review, two showcases and beyond", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "29", "JournalTitle": "Information Fusion"}, {"Title": "Extracting spatial effects from machine learning model using local interpretation method: An example of SHAP and XGBoost", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "96", "Issue": "", "Page": "101845", "JournalTitle": "Computers, Environment and Urban Systems"}, {"Title": "Counterfactual explanations and how to find them: literature review and benchmarking", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "38", "Issue": "5", "Page": "2770", "JournalTitle": "Data Mining and Knowledge Discovery"}]}, {"ArticleId": *********, "Title": "CO gas sensing characteristics of BaSnO3 epitaxial films prepared by PLD: The effect of film thickness", "Abstract": "In the present work, perovskite Barium Stannate (BaSnO <sub>3</sub> ) thin films of varying thickness were investigated for carbon monoxide (CO) gas sensing application. Epitaxial BaSnO <sub>3</sub> thin films of thickness (90, 270 and 360 nm) were grown on MgO substrates by using pulsed laser deposition technique. The enhanced electrical properties of the films with decreasing thickness can be attributed to the increased amount of Sn <sup>4+</sup> ions in the film. An increase in CO gas sensing response of thin films was observed with decreasing film thickness. BaSnO <sub>3</sub> thin film having 90 nm thickness exhibits a higher gas sensing response of 58% at 400 °C, which is a quite lower temperature as compared to the previous works in similar systems. This high value is attributed to the presence of large number of grains and grain boundaries providing easy adsorption and desorption of oxygen species at the surface, confirmed by the atomic force microscopy and X-ray photoelectron spectroscopy, respectively.", "Keywords": "", "DOI": "10.1016/j.snb.2023.134882", "PubYear": 2024, "Volume": "400", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR, National Physical Laboratory, Dr<PERSON> <PERSON><PERSON> <PERSON><PERSON>, New Delhi 110012, India;Academy of Scientific and Innovative Research (AcSIR), Ghaziabad 201002, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CSIR, National Physical Laboratory, Dr<PERSON> <PERSON><PERSON> <PERSON><PERSON>, New Delhi 110012, India;Academy of Scientific and Innovative Research (AcSIR), Ghaziabad 201002, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR, National Physical Laboratory, Dr<PERSON> <PERSON><PERSON> <PERSON><PERSON>, New Delhi 110012, India;Academy of Scientific and Innovative Research (AcSIR), Ghaziabad 201002, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology Mandi, Kamand Campus, Kamand, Himachal Pradesh 175075, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR, National Physical Laboratory, Dr<PERSON> <PERSON><PERSON> <PERSON><PERSON>, New Delhi 110012, India"}, {"AuthorId": 6, "Name": "C.S. Yadav", "Affiliation": "Indian Institute of Technology Mandi, Kamand Campus, Kamand, Himachal Pradesh 175075, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR, National Physical Laboratory, Dr<PERSON> <PERSON><PERSON> <PERSON><PERSON>, New Delhi 110012, India;Academy of Scientific and Innovative Research (AcSIR), Ghaziabad 201002, India"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR, National Physical Laboratory, Dr<PERSON> <PERSON><PERSON> <PERSON><PERSON>, New Delhi 110012, India;Academy of Scientific and Innovative Research (AcSIR), Ghaziabad 201002, India;Corresponding author at: CSIR, National Physical Laboratory, Dr. <PERSON><PERSON> <PERSON><PERSON>, New Delhi 110012, India"}], "References": [{"Title": "Carbon monoxide gas sensing properties of metal-organic frameworks-derived tin dioxide nanoparticles/molybdenum diselenide nanoflowers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127369", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 110707838, "Title": "A Hexagon Tile Map Algorithm for Displaying Spatial Data", "Abstract": "Spatial distributions have been presented on alternative representations of geography, such as cartograms, for many years. In modern times, interactivity and animation have allowed alternative displays to play a larger role. Alternative representations have been popularised by online news sites, and digital atlases with a focus on public consumption. Applications are increasingly widespread, especially in the areas of disease mapping, and election results. The algorithm presented here creates a display that uses tessellated hexagons to represent a set of spatial polygons, and is implemented in the R package called sugarbag. It allocates these hexagons in a manner that preserves the spatial relationship of the geographic units, in light of their positions to points of interest. The display showcases spatial distributions, by emphasising the small geographical regions that are often difficult to locate on geographic maps. © (2023). All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2023-021", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Monash University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Queensland University of Technology"}], "References": []}, {"ArticleId": *********, "Title": "MLN-net: A multi-source medical image segmentation method for clustered microcalcifications using multiple layer normalization", "Abstract": "The accurate segmentation of clustered microcalcifications in mammography is crucial for the diagnosis and treatment of breast cancer. Despite exhibiting expert-level accuracy, recent deep learning advancements in medical image segmentation provide insufficient contribution to practical applications, due to the domain shift resulting from differences in patient postures, individual gland density, and imaging modalities, etc. In this paper, a novel framework named MLN-net is proposed for clustered microcalcification segmentation. It can segment multi-source images using only single source images. Specifically, to rich domain distribution information, we introduce a source domain image augmentation for generating multi-source images. A structure of multiple layer normalization (LN) layers is then used to construct the segmentation network, which can be found efficient for clustered microcalcification segmentation in different domains. Additionally, a branch selection strategy is designed for measuring the similarity of the source domain data and the target domain data. To validate the proposed MLN-net, extensive analyses including ablation experiments are performed, comparison of 12 baseline methods. MLN-net enhances segmentation quality of full-field digital mammography (FFDM) and digital breast tomosynthe (DBT) images from the FFDM-DBT dataset, achieving the average Dice similarity coefficient (DSC) of 86.52% and the average Hausdorff distance (HD) of 20.49 mm on the source domain DBT. And it outperforms the baseline models for the task in FFDM images from both the CBIS-DDSM and the FFDM-DBT dataset, achieving the average DSC of 50.78% and the average HD of 35.12 mm on the source domain CBIS-DDSM. Extensive experiments validate the effectiveness of MLN-net in segmenting clustered microcalcifications from different domains and its segmentation accuracy surpasses state-of-the-art methods. Code will be available at https://github.com/yezanting/MLN-NET-VERSON1 .", "Keywords": "", "DOI": "10.1016/j.knosys.2023.111127", "PubYear": 2024, "Volume": "283", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Information Science and Technology, Zhejiang Shuren University, Hangzhou, China;State Key Laboratory of Industrial Control Technology, Zhejiang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "Zanting Ye", "Affiliation": "School of Computer Science and Artificial Intelligence, Changzhou University, Changzhou, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Newcastle university, Newcastle upon Tyne, UK"}, {"AuthorId": 4, "Name": "Haidong Cui", "Affiliation": "Breast Surgery, First Affiliated Hospital, Zhejiang University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "The Second Affiliated Hospital of Zhejiang University School of Medicine, Zhejiang University, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Technology, Zhejiang Shuren University, Hangzhou, China;School of Computer Science and Artificial Intelligence, Changzhou University, Changzhou, China"}], "References": [{"Title": "COVID-19 classification by CCSHNet with deep fusion using transfer learning and discriminant correlation analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "131", "JournalTitle": "Information Fusion"}, {"Title": "Batch normalization embeddings for deep domain generalization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109115", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 110707922, "Title": "Prognostics of lithium-ion batteries health state based on adaptive mode decomposition and long short-term memory neural network", "Abstract": "In the field of battery health state prognostics, the inaccurate lithium-ion battery&#x27;s health status prediction is usually caused by the capacity regeneration (CR) phenomenon triggered by relaxation effects during the degradation process. To address this issue, we pay more attention to the main rapid degradation trend of battery capacity instead of many researches focusing on only CR phenomenon. This paper presents a prognostic framework called CEEMDAN-LSTM to decouple the normal capacity degradation process while eliminating local regenerative capacity, capturing degradation characteristics for battery state-of-health (SOH) prognostics. It introduces the complete ensemble empirical mode decomposition with adaptive noise (CEEMDAN) to decompose the original battery capacity degradation curve into principal trend sequence and other high-frequency subsequences, and Pearson correlation coefficient (PCC) is calculated to remove irrelevant high-frequency subsequences. Predictions task is completed by bidirectional long short-term memory network (Bi-LSTM) and long short-term memory network (LSTM) groups. Experimental validations are conducted on two lithium-ion battery datasets from NASA Ames Research Center and the Advanced Life-Cycle Engineering Center of the University of Maryland. The results demonstrate that the proposed framework achieves more accurate SOH prediction than many previous mainstream methods.", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107317", "PubYear": 2024, "Volume": "127", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering, Anhui Polytechnic University, Wuhu, 241000, China;College of Intelligent Systems Science and Engineering, Hubei Minzu University, Enshi, 445000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Anhui Polytechnic University, Wuhu, 241000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Anhui Polytechnic University, Wuhu, 241000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Automation, Nanjing University of Aeronautics and Astronautics, Nanjing, 211106, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Automation, Nanjing University of Aeronautics and Astronautics, Nanjing, 211106, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Automation, Nanjing University of Aeronautics and Astronautics, Nanjing, 211106, China"}], "References": [{"Title": "A Particle Filter and Long Short-Term Memory Fusion Technique for Lithium-Ion Battery Remaining Useful Life Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "143", "Issue": "6", "Page": "", "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control"}, {"Title": "Image- and health indicator-based transfer learning hybridization for battery RUL prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105120", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 110707925, "Title": "Learning Analytics nach Satzung: Rechtssicherer Einsatz durch Hochschulen", "Abstract": "Zusammenfassung</h3> <p>Aktuell stecken Learning-Analytics-Projekte an Hochschulen häufig noch in der Planungs- und Entwicklungsphase. Rechtsunsicherheit in Bezug auf eine tragfähige Rechtsgrundlage für die Datenverarbeitung in Learning Analytics-Projekten stellt dabei ein großes Problem dar. Nach der Beschreibung von Learning Analytics (1) und datenschutzrechtlicher Grundlagen (2) wird untersucht, inwieweit die gesetzlichen Aufgabenzuweisungen in Verbindung mit datenschutzrechtlichen Generalklauseln die mit Learning Analytics einhergehende Datenverarbeitung rechtfertigen können (3). Darauf aufbauend wird erläutert, wie durch Hochschulsatzung der Einsatz von Learning Analytics rechtssicher gestaltet werden kann (4). </p>", "Keywords": "", "DOI": "10.1007/s11623-023-1849-y", "PubYear": 2023, "Volume": "47", "Issue": "11", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Datenrecht Beratungsgesellschaft, Baunatal, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universität Kassel, Kassel, Deutschland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universität Kassel, Kassel, Deutschland"}], "References": []}, {"ArticleId": 110708076, "Title": "State Complexity of Permutation and the Language Inclusion Problem up to <PERSON><PERSON>h Equivalence on Alphabetical Pattern Constraints and Partially Ordered NFAs", "Abstract": "<p>We investigate the state complexity of the permutation operation, or the commutative closure, on Alphabetical Pattern Constraints (APCs). This class corresponds to level [Formula: see text] of the Straubing-Thérien hierarchy and includes the finite, the piecewise testable, or [Formula: see text]-trivial, and the [Formula: see text]-trivial and [Formula: see text]-trivial languages. We give a sharp state complexity bound expressed in terms of the longest strings in the unary projection languages of an associated finite language. Additionally, for a subclass, we give sharp bounds expressed in terms of the size of a recognizing input automaton and the size of the alphabet. We also state a related state complexity bound for the commutative closure on finite languages. Lastly, we investigate the language inclusion, equivalence and universality problems on APCs up to permutational, or Parikh, equivalence. These problems are known to be [Formula: see text]-complete on APCs in general, even for fixed alphabets. We show them to be decidable in polynomial time for fixed alphabets if we only want to solve them up to Parikh equivalence. We also correct a mistake from the conference version in a bound on the size of recognizing automata for the commutative closure.</p>", "Keywords": "State complexity; finite automata; alphabetical pattern constraints; commutative closure; language inclusion problem; <PERSON>rikh equivalence; partially ordered nondeterministic automata", "DOI": "10.1142/S0129054123430025", "PubYear": 2023, "Volume": "34", "Issue": "8", "JournalId": 9642, "JournalTitle": "International Journal of Foundations of Computer Science", "ISSN": "0129-0541", "EISSN": "1793-6373", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Informatikwissenschaften, FB IV, Universität Trier, Germany"}], "References": [{"Title": "Semilinearity of Families of Languages", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "8", "Page": "1179", "JournalTitle": "International Journal of Foundations of Computer Science"}]}, {"ArticleId": 110708176, "Title": "Enhancing Walking Accessibility in Urban Transportation: A Comprehensive Analysis of Influencing Factors and Mechanisms", "Abstract": "<p>The rise in “urban diseases” like population density, traffic congestion, and environmental pollution has renewed attention to urban livability. Walkability, a critical measure of pedestrian friendliness, has gained prominence in urban and transportation planning. This research delves into a comprehensive analysis of walking accessibility, examining both subjective and objective aspects. This study aims to identify the influencing factors and explore the underlying mechanisms driving walkability within a specific area. Through a questionnaire survey, residents’ subjective perceptions were gathered concerning various factors such as traffic operations, walking facilities, and the living environment. Structural equation modeling was employed to analyze the collected data, revealing that travel experience significantly impacts perceived accessibility, followed by facility condition, traffic condition, and safety perception. In the objective analysis, various types of POI data served as explanatory variables, dividing the study area into grids using ArcGIS, with the Walk Score® as the dependent variable. Comparisons of OLS, GWR and MGWR demonstrated that MGWR yielded the most accurate fitting results. Mixed land use, shopping, hotels, residential, government, financial, and medical public services exhibited positive correlations with local walkability, while corporate enterprises and street greening showed negative correlations. These findings were attributed to the level of development, regional functions, population distribution, and supporting facility deployment, collectively influencing the walking accessibility of the area. In conclusion, this research presents crucial insights into enhancing walkability, with implications for urban planning and management, thereby enriching residents’ walking travel experience and promoting sustainable transportation practices. Finally, the limitations of the thesis are discussed.</p>", "Keywords": "", "DOI": "10.3390/info14110595", "PubYear": 2023, "Volume": "14", "Issue": "11", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Transportation, Southeast University, Nanjing 211189, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Transportation, Southeast University, Nanjing 211189, China; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Transportation, Southeast University, Nanjing 211189, China"}], "References": []}, {"ArticleId": *********, "Title": "Dedicated hardware design for efficient quantum computations using classical logic gates", "Abstract": "<p>This work presents a novel approach to quantum computing by proposing a customizable hardware design of a dedicated processor that emulates the execution of quantum algorithms. Unlike software-based quantum computation simulators, which run on standard general-purpose computers and suffer from reduced performance, this hardware design, which is based on classical concepts of bits, registers and memories, aims to leverage pure parallelism and pipelined execution for efficient quantum computations via emulation. The architecture includes several key components: memories, computation unit, measurement unit and control unit. The quantum state memory stores the individual and group states of qubits. This memory is crucial for maintaining the quantum information required for quantum operations. Basic operators are stored in dedicated operator memory. Additionally, a scratch memory allows for larger operators to be dynamically built at runtime. The computation unit is responsible for performing complex number multiplications, which form the basis of tensor and matrix products necessary for executing quantum operations. A measurement unit enables quantum state sampling, which is an essential aspect of quantum computation. Furthermore, a control unit is incorporated to ensure the correct operation of the quantum processor’s data path. It utilizes a microprogram to manage and coordinate the functional units. All the functional units communicate with each other through dedicated and shared data buses, depending on the frequency of information exchange. This enables efficient data transfer and coordination among the components. The proposed hardware design has been simulated and proved to be effective in executing quantum operations. By exploiting parallelism and employing a pipelined execution, this architecture overcomes the limitations of software-based simulators, delivering improved performance for emulating quantum algorithms. We use <PERSON><PERSON>’s search algorithm as a benchmark to evaluate the performance of the proposed hardware design and compare it to software-based simulation and to hardware-based algorithm-dedicated emulation.</p>", "Keywords": "", "DOI": "10.1007/s11227-023-05687-1", "PubYear": 2024, "Volume": "80", "Issue": "5", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronics Engineering and Telecommunications, Engineering Faculty, State University of Rio de Janeiro, Rio de Janeiro, Brazil; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Post-Graduate Program of Electronics Engineering, Engineering Faculty, State University of Rio de Janeiro, Rio de Janeiro, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems Engineering and Computation, Engineering Faculty, State University of Rio de Janeiro, Rio de Janeiro, Brazil"}], "References": [{"Title": "Tools for Quantum Computing Based on Decision Diagrams", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Quantum Computing"}]}, {"ArticleId": 110708283, "Title": "Language support for verifying reconfigurable interacting systems", "Abstract": "<p>Reconfigurable interacting systems consist of a set of autonomous agents, with integrated interaction capabilities that feature opportunistic interaction. Agents seemingly reconfigure their interaction interfaces by forming collectives and interact based on mutual interests. Finding ways to design and analyse the behaviour of these systems is a vigorously pursued research goal. In this article, we provide a modelling and analysis environment for the design of such system. Our tool offers simulation and verification to facilitate native reasoning about the domain concepts of such systems. We present our tool named R-CHECK (please find the associated toolkit repository here: https://github.com/dsynma/recipe ). R-CHECK supports a high-level input language with matching enumerative and symbolic semantics and provides modelling convenience for features such as reconfiguration, coalition formation, and self-organisation. For analysis, users can simulate the designed system and explore arising traces. Our included model checker permits reasoning about interaction protocols and joint missions.</p>", "Keywords": "Model checking; Agent theories and models; Verification of multi-agent systems", "DOI": "10.1007/s10009-023-00729-8", "PubYear": 2023, "Volume": "25", "Issue": "5-6", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Gothenburg, Gothenburg, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Gothenburg, Gothenburg, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Gothenburg, Gothenburg, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Gothenburg, Gothenburg, Sweden"}], "References": [{"Title": "Programming interactions in collective adaptive systems by relying on attribute-based communication", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "102428", "JournalTitle": "Science of Computer Programming"}, {"Title": "Modelling and verification of reconfigurable multi-agent systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "2", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}]}, {"ArticleId": *********, "Title": "Approximate dynamic programming approach to efficient metro train timetabling and passenger flow control strategy with stop-skipping", "Abstract": "Urban metro systems continuously face high travel demand during rush hours, which brings excessive energy waste and high risk to passengers. In order to alleviate passenger congestion, improve train service levels and reduce energy consumption, a nonlinear dynamic programming (DP) model of efficient metro train timetabling and passenger flow control strategy with stop-skipping is presented, which consists of state transition equations concerning train traffic and passenger load. To overcome the curse of dimensionality, the formulated nonlinear DP problem is transformed into a discrete Markov decision process, and a novel approximate dynamic programming (ADP) approach is designed based on the lookahead policy and linear parametric value function approximation. Finally, the effectiveness of this method is verified by three groups of numerical experiments. Compared with Particle Swarm Optimization (PSO) and Simulated Annealing (SA), the designed ADP approach could obtain high-quality solutions quickly, which makes it applicable to the practical implementation of metro operations.", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107393", "PubYear": 2024, "Volume": "127", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Systems Science, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 2, "Name": "Shukai Li", "Affiliation": "School of Systems Science, Beijing Jiaotong University, Beijing, 100044, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Systems Science, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Systems Science, Beijing Jiaotong University, Beijing, 100044, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Systems Science, Beijing Jiaotong University, Beijing, 100044, China"}], "References": [{"Title": "Dynamic speed trajectory generation and tracking control for autonomous driving of intelligent high-speed trains combining with deep learning and backstepping control methods", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "105230", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Robust metro train scheduling integrated with skip-stop pattern and passenger flow control strategy under uncertain passenger demands", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "151", "Issue": "", "Page": "106116", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 110708356, "Title": "Poison Attack and Poison Detection on Deep Source Code Processing Models", "Abstract": "In the software engineering (SE) community, deep learning (DL) has recently been applied to many source code processing tasks, achieving state-of-the-art results. Due to the poor interpretability of DL models, their security vulnerabilities require scrutiny. Recently, researchers have identified an emergent security threat to DL models, namely,\n poison attacks \n . The attackers aim to inject insidious backdoors into DL models by poisoning the training data with poison samples. The backdoors mean that poisoned models work normally with clean inputs but produce targeted erroneous results with inputs embedded with specific triggers. By using triggers to activate backdoors, attackers can manipulate poisoned models in security-related scenarios (e.g., defect detection) and lead to severe consequences.\n \n \n To verify the vulnerability of deep source code processing models to poison attacks, we present a poison attack approach for source code named\n CodePoisoner \n as a strong imaginary enemy.\n CodePoisoner \n can produce compilable and functionality-preserving poison samples and effectively attack deep source code processing models by poisoning the training data with poison samples. To defend against poison attacks, we further propose an effective poison detection approach named\n CodeDetector \n .\n CodeDetector \n can automatically identify poison samples in the training data. We apply\n CodePoisoner \n and\n CodeDetector \n to six deep source code processing models, including defect detection, clone detection, and code repair models. The results show that ❶\n CodePoisoner \n conducts successful poison attacks with a high attack success rate (average: 98.3%, maximum: 100%). It validates that existing deep source code processing models have a strong vulnerability to poison attacks. ❷\n CodeDetector \n effectively defends against multiple poison attack approaches by detecting (maximum: 100%) poison samples in the training data. We hope this work can help SE researchers and practitioners notice poison attacks and inspire the design of more advanced defense techniques.", "Keywords": "", "DOI": "10.1145/3630008", "PubYear": 2024, "Volume": "33", "Issue": "3", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> ♂", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Peking University), Ministry of Education; School of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Peking University), Ministry of Education; School of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Peking University), Ministry of Education; School of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 4, "Name": "Ge Li", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Peking University), Ministry of Education; School of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High Confidence Software Technologies (Peking University), Ministry of Education; School of Computer Science, Peking University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang University, Ningbo, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Huawei, Hangzhou, China"}], "References": [{"Title": "Adversarial examples for models of code", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "DeepWukong", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "CodeEditor : Learning to Edit Source Code with Pre-trained Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 110708391, "Title": "Investigating healthcare workers' technostress when welfare technology is introduced in long-term care facilities", "Abstract": "", "Keywords": "", "DOI": "10.1080/0144929X.2023.2276802", "PubYear": 2024, "Volume": "43", "Issue": "13", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "Sofia Thunberg", "Affiliation": "Cognition & Interaction Lab, Department of Computer and Information Science, Linköping University, Linköping, Sweden"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Gender Studies, Department of Thematic Studies, Linköping University, Linköping, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Cognition & Interaction Lab, Department of Computer and Information Science, Linköping University, Linköping, Sweden"}], "References": []}, {"ArticleId": 110708416, "Title": "Inner-Product Matchmaking Encryption: Bilateral Access Control and Beyond Equality", "Abstract": "<p>We present an inner-product matchmaking encryption (IP-ME) scheme achieving weak privacy and authenticity in prime-order groups under symmetric external <PERSON><PERSON><PERSON> (SXDH) assumption in the standard model. We further present an IP-ME with Monotone Span Program Authenticity (IP-ME with MSP Auth) scheme, where the chosen sender policy is upgraded to MSP, and the scheme also achieves weak privacy and authenticity in prime-order groups under SXDH assumption in the standard model. Both of the schemes have more expressive functionalities than identity-based matchmaking encryption (IB-ME) scheme, and are simpler than <PERSON><PERSON><PERSON> et al.’s modular ME scheme (Crypto’ 19). But our schemes only achieve a very limited flavor of security, which is reflected in the privacy.</p>", "Keywords": "", "DOI": "10.1049/2023/8829580", "PubYear": 2023, "Volume": "2023", "Issue": "1", "JournalId": 17160, "JournalTitle": "IET Information Security", "ISSN": "1751-8709", "EISSN": "1751-8717", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Trustworthy Computing, Software Engineering Institute, East China Normal University, Shanghai 200062, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 3, "Name": "Hai<PERSON> Qian", "Affiliation": "Shanghai Key Laboratory of Trustworthy Computing, Software Engineering Institute, East China Normal University, Shanghai 200062, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Trustworthy Computing, Software Engineering Institute, East China Normal University, Shanghai 200062, China"}], "References": []}, {"ArticleId": 110708424, "Title": "THE TASK OF CHOOSING PARTNERS FOR THE ORGANIZATION OF COOPERATION IN THE FRAMEWORK OF SCIENTIFIC AND EDUCATIONAL PROJECTS", "Abstract": "<p>The primary objective of this article is to establish a set of fundamental criteria for the selection of scientific partners for collaborative research efforts. Achieving this objective entails addressing the challenge of identifying criteria that are both objective and universally applicable, capable of encompassing various fields of scientific research, such as natural sciences, technical sciences, and economic sciences, among others. One such criterion, applicable to scientists, may involve assessing their publication activity within specific research areas that align with the objectives of the relevant scientific research or international projects. In the contemporary landscape of scientific research, there is a growing urgency to enhance the effectiveness of research endeavors and to foster efficient collaboration within scientific communities. This is particularly vital for organizations oriented towards project-based research. In the formation of research project teams, a conventional approach is to select partners from the pool of scientists possessing the requisite qualifications and experience in the execution of such projects. A widely accepted yardstick for evaluating the outcomes of scientists' research endeavors is the citation metrics associated with their publications. Typically, these metrics take the form of scalar values. While this approach offers several advantages, it is not without its limitations. One notable drawback is the potential loss of information when converting raw data into scalar metrics, and the existence of certain edge cases where the parameter remains unchanged despite variations in the number of citations and publications. Hence, it is pertinent to explore the development of new methodologies or modifications to existing ones that can effectively evaluate the results of scientists' research activities while mitigating these limitations.Начало формы The article describes the criteria for the search and selection of partners for joint scientific research. This will make it possible to effectively form teams for narrowly focused scientific research or international collaboration projects in interdisciplinary scientific projects such as the European Horizon Program or educational projects such as the Erasmus plus program. Also, the proposed solution will allow the formation of small teams for joint scientific publications. It is imperative to acknowledge that the process of partner selection is predominantly driven by a consideration of the knowledge, whether it be novel or foundational, possessed by prospective partners who are entrusted with the execution of a project. It becomes crucial to delineate the specific criteria governing partner selection which can vary contingent upon factors such as the typology of partners, the nature of project tasks, the depth of knowledge possessed, and related contextual variables. A vital underpinning for the formation of project consortia is the mathematical conundrum of choice which furnishes a formal rationale for the judicious selection of a particular partner.</p>", "Keywords": "search for scientific partners;scientific collaboration;scientific research;criteria for choosing scientific partners;scientific cooperation;educational cooperation", "DOI": "10.37943/15FJVM4636", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 75769, "JournalTitle": "Scientific Journal of Astana IT University", "ISSN": "2707-9031", "EISSN": "2707-904X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Astana IT University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> National University of Kyiv"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Astana IT University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Uzhhorod National University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> East Kazakhstan Technical University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>ev North Kazakhstan University"}], "References": []}, {"ArticleId": *********, "Title": "Global Properties of Cytokine-Enhanced HIV-1 Dynamics Model with Adaptive Immunity and Distributed Delays", "Abstract": "<p>In this paper, we study a model that enhances our understanding of cytokine-influenced HIV-1 infection. The impact of adaptive immune response (cytotoxic T lymphocytes (CTLs) and antibodies) and time delay on HIV-1 infection is included. The model takes into account two types of distributional delays, (i) the delay in the HIV-1 infection of CD4+T cells and (ii) the maturation delay of new virions. We first investigated the fundamental characteristics of the system, then found the system’s equilibria. We derived five threshold parameters, ℜi, i = 0, 1,…, 4, which completely determine the existence and stability of the equilibria. The <PERSON><PERSON><PERSON>nov method was used to prove the global asymptotic stability for all equilibria. We illustrate the theoretical results by performing numerical simulations. We also performed a sensitivity analysis on the basic reproduction number ℜ0 and identified the most-sensitive parameters. We found that pyroptosis contributes to the number ℜ0, and then, neglecting it will make ℜ0 underevaluated. Necrosulfonamide and highly active antiretroviral drug therapy (HAART) can be effective in preventing pyroptosis and at reducing viral replication. Further, it was also found that increasing time delays can effectively decrease ℜ0 and, then, inhibit HIV-1 replication. Furthermore, it is shown that both CTLs and antibody immune responses have no effect on ℜ0, while this can result in less HIV-1 infection.</p>", "Keywords": "", "DOI": "10.3390/computation11110217", "PubYear": 2023, "Volume": "11", "Issue": "11", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Al-Azhar University, Assiut Branch, Assiut 71524, Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Al-Azhar University, Assiut Branch, Assiut 71524, Egypt↑Department of Mathematics, Faculty of Science, King Abdulaziz University, P.O. Box 80203, <PERSON><PERSON> 21589, Saudi Arabia; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, King Khalid University, Abha 62529, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Al-Azhar University, Assiut Branch, Assiut 71524, Egypt"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Faculty of Science, Al-Azhar University, Assiut Branch, Assiut 71524, Egypt"}], "References": []}, {"ArticleId": 110708500, "Title": "Fast Non-overlapping Domain Decomposition Methods for Continuous Multi-phase Labeling Problem", "Abstract": "<p>This paper presents the domain decomposition methods (DDMs) for achieving fast parallel computing on multi-core computers when dealing with the multi-phase labeling problem. To handle the non-smooth multi-phase labeling model, we introduce a quadratic proximal term, resulting in a strongly convex model. This paper provides theoretical evidence supporting the convergence of the proposed non-overlapping DDMs. Specifically, it is demonstrated that the non-overlapping DDMs for the non-smooth labeling model exhibits an O (1/ n ) convergence rate of the energy functional, where n is the number of iterations. Moreover, the fast iterative shrinkage-thresholding algorithm (<PERSON> and <PERSON> in SIAM J Imaging Sci 2(1):183–202, 2009) is applied to achieve an (O(1/n^2)) convergence rate. Numerical experiments are evaluated to demonstrate the convergence and efficiency of the proposed DDMs in solving multi-phase labeling problems.</p>", "Keywords": "Multi-phase labeling problem; Dual model; Non-overlapping domain decomposition; convergence rate", "DOI": "10.1007/s10915-023-02382-4", "PubYear": 2023, "Volume": "97", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics, North University of China, Taiyuan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Tianjin Normal University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Beijing Normal University, Beijing, China"}], "References": [{"Title": "A Multigrid Algorithm for Maxflow and Min-Cut Problems with Applications to Multiphase Image Segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "87", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Accelerated Additive Schwarz Methods for Convex Optimization with Adaptive Restart", "Authors": "Jongho Park", "PubYear": 2021, "Volume": "89", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Additive <PERSON> methods for convex optimization with backtracking", "Authors": "Jongho Park", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "332", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 110708538, "Title": "Likelihood Ratio Test-Based Drug Safety Assessment using R Package \\pkg{pvLRT}", "Abstract": "Medical product safety continues to be a key concern of the twenty-first century. Several spontaneous adverse events reporting databases established across the world continuously collect and archive adverse events data on various medical products. Determining signals of disproportional reporting (SDR) of product/adverse event pairs from these large-scale databases require the use of principled statistical techniques. Likelihood ratio test (LRT)-based approaches are particularly noteworthy in this context as they permit objective SDR detection without requiring ad hoc thresholds. However, their implementation is non-trivial due to analytical complexities, which necessitate the use of computation-heavy methods. Here we introduce R package pvLRT which implements a suite of LRT approaches, along with various post-processing and graphical summary functions, to facilitate simplified use of the methodologies. Detailed examples are provided to illustrate the package through analyses of three real product safety datasets obtained from publicly available FDA FAERS and VAERS databases. © (2023). All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2023-027", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biostatistics, SUNY University at Buffalo"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biostatistics, SUNY University at Buffalo"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Drug Evaluation and Research, U.S. Food and Drug Administration"}], "References": []}, {"ArticleId": 110708541, "Title": "Estimating Causal Effects using Bayesian Methods with the R Package BayesCACE", "Abstract": "Noncompliance, a common problem in randomized clinical trials (RCTs), complicates the analysis of the causal treatment effect, especially in meta-analysis of RCTs. The complier average causal effect (CACE) measures the effect of an intervention in the latent subgroup of the population that complies with its assigned treatment (the compliers). Recently, Bayesian hierarchical approaches have been proposed to estimate the CACE in a single RCT and a meta-analysis of RCTs. We develop an R package, BayesCACE, to provide user-friendly functions for implementing CACE analysis for binary outcomes based on the flexible Bayesian hierarchical framework. This package includes functions for analyzing data from a single study and for performing a meta-analysis with either complete or incomplete compliance data. The package also provides various functions for generating forest, trace, posterior density, and auto-correlation plots, which can be useful to review noncompliance rates, visually assess the model, and obtain study-specific and overall CACEs. © (2023). All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2023-038", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhou", "Affiliation": "Gilead Inc."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Minnesota Twin Cities"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Minnesota Twin Cities"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Arizona"}, {"AuthorId": 5, "Name": "Hai<PERSON><PERSON>", "Affiliation": "(Affliation 1) Pfizer Inc. (Affliation 2) University of Minnesota Twin Cities"}], "References": []}, {"ArticleId": 110708580, "Title": "Implementation of natural hand gestures in holograms for 3D object manipulation", "Abstract": "Holograms provide a characteristic manner to display and convey information, and have been improved to provide better user interactions Holographic interactions are important as they improve user interactions with virtual objects. Gesture interaction is a recent research topic, as it allows users to use their bare hands to directly interact with the hologram. However, it remains unclear whether real hand gestures are well suited for hologram applications. Therefore, we discuss the development process and implementation of three-dimensional object manipulation using natural hand gestures in a hologram. We describe the design and development process for hologram applications and its integration with real hand gesture interactions as initial findings. Experimental results from Nasa TLX form are discussed. Based on the findings, we actualize the user interactions in the hologram.", "Keywords": "Hologram ; Gesture interaction ; Natural hand gesture ; Three-dimensional object manipulation ; Gesture recognition", "DOI": "10.1016/j.vrih.2023.02.001", "PubYear": 2023, "Volume": "5", "Issue": "5", "JournalId": 61022, "JournalTitle": "Virtual Reality & Intelligent Hardware", "ISSN": "2096-5796", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mixed and Virtual Environment Research Lab (Mivielab), ViCubeLab, Universiti Teknologi Malaysia, Johore 81310, Malaysia;Faculty of Computing, Universiti Teknologi Malaysia, Johore 81310, Malaysia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mixed and Virtual Environment Research Lab (Mivielab), ViCubeLab, Universiti Teknologi Malaysia, Johore 81310, Malaysia;Faculty of Computing, Universiti Teknologi Malaysia, Johore 81310, Malaysia"}], "References": [{"Title": "3D Touch Surface for Interactive Pseudo‐Holographic Displays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "2", "Page": "2000126", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 110708720, "Title": "Paddy field object detection for robotic combine based on real‐time semantic segmentation algorithm", "Abstract": "The development of robotic combine for rice harvesting has garnered worldwide attention in recent years. The robotic combine is capable of running along a designated path; however, it still requires human operator supervision due to the lack of object detection sensors for safety purposes. To achieve a fully unmanned robotic combine, a real‐time paddy field object detection method is necessary. Typically, all paddy field objects are detected individually using multiple algorithms and sensors, which significantly increases the complexity and cost of the detection process. In this study, the deep learning (DL) based semantic segmentation (SS) method was employed to detect all paddy field objects simultaneously using only an RGB camera. Considering the environment of the paddy field, a new SS model called “The Robotic Combine Network (TRCNet)” was specifically designed for the robotic combine. And four state‐of‐the‐art lightweight convolutional neural networks were applied as the backbones of the TRCNet. To achieve real‐time detection, TensorRT (NVIDIA) was utilized for speeding up the prediction process. All models were trained and evaluated using paddy field images captured during the robotic combine's harvesting process. The results showed that the TRCNet can successfully detect all paddy field objects. The mean intersection over union, and frames per second (FPS) of the best two SS models were 0.823, 47.48, and 0.834, 32.44, respectively. The FPS values were obtained after speed acceleration and tested with an image size of 640 × 480 pixels on an embedded processor (Jetson TX2), enabling real‐time object detection in paddy fields for the robotic combine.", "Keywords": "convolutional neural networks;deep learning;object detection;paddy field;real-time;robotic combine;semantic segmentation", "DOI": "10.1002/rob.22260", "PubYear": 2024, "Volume": "41", "Issue": "2", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Agriculture Kyoto University  Kyoto Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Agriculture Kyoto University  Kyoto Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Graduate School of Agriculture Kyoto University  Kyoto Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Agriculture Kyoto University  Kyoto Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Agriculture Kyoto University  Kyoto Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Agriculture Kyoto University  Kyoto Japan"}], "References": [{"Title": "Transfer learning between crop types for semantic segmentation of crops versus weeds in precision agriculture", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "1", "Page": "7", "JournalTitle": "Journal of Field Robotics"}, {"Title": "A comparative study of fruit detection and counting methods for yield mapping in apple orchards", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "263", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Review the state-of-the-art technologies of semantic segmentation based on deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "493", "Issue": "", "Page": "626", "JournalTitle": "Neurocomputing"}, {"Title": "Research on fault diagnosis of bearings in walking part of wall‐building robot based on roadside acoustic signal", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "2", "Page": "215", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 110708772, "Title": "Quatro++: Robust global registration exploiting ground segmentation for loop closing in LiDAR SLAM", "Abstract": "<p>Global registration is a fundamental task that estimates the relative pose between two viewpoints of 3D point clouds. However, there are two issues that degrade the performance of global registration in LiDAR SLAM: one is the sparsity issue and the other is degeneracy. The sparsity issue is caused by the sparse characteristics of the 3D point cloud measurements in a mechanically spinning LiDAR sensor. The degeneracy issue sometimes occurs because the outlier-rejection methods reject too many correspondences, leaving less than three inliers. These two issues have become more severe as the pose discrepancy between the two viewpoints of 3D point clouds becomes greater. To tackle these problems, we propose a robust global registration framework, called Quatro++. Extending our previous work that solely focused on the global registration itself, we address the robust global registration in terms of the loop closing in LiDAR SLAM. To this end, ground segmentation is exploited to achieve robust global registration. Through the experiments, we demonstrate that our proposed method shows a higher success rate than the state-of-the-art global registration methods, overcoming the sparsity and degeneracy issues. In addition, we show that ground segmentation significantly helps to increase the success rate for the ground vehicles. Finally, we apply our proposed method to the loop closing module in LiDAR SLAM and confirm that the quality of the loop constraints is improved, showing more precise mapping results. Therefore, the experimental evidence corroborated the suitability of our method as an initial alignment in the loop closing. Our code is available at https://quatro-plusplus.github.io .</p>", "Keywords": "", "DOI": "10.1177/02783649231207654", "PubYear": 2024, "Volume": "43", "Issue": "5", "JournalId": 943, "JournalTitle": "The International Journal of Robotics Research", "ISSN": "0278-3649", "EISSN": "1741-3176", "Authors": [{"AuthorId": 1, "Name": "Hyung<PERSON><PERSON> Lim", "Affiliation": "School of Electrical Engineering, KAIST (Korea Advanced Institute of Science and Technology), Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Artificial Intelligence, Hanyang University, Seoul, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Robotics Program, KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, KAIST (Korea Advanced Institute of Science and Technology), Daejeon, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, KAIST (Korea Advanced Institute of Science and Technology), Daejeon, Republic of Korea"}], "References": [{"Title": "OverlapNet: a siamese network for computing LiDAR scan similarity with applications to loop closing and localization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "46", "Issue": "1", "Page": "61", "JournalTitle": "Autonomous Robots"}]}, {"ArticleId": 110708793, "Title": "Towards robotic deep spatiotemporal language understanding based on mental-image-directed semantic theory", "Abstract": "<p>Among subsets of natural language, spatial language, more exactly spatiotemporal language here, has been considered most essential for human-like interaction between people and robots expected in near future. Quite distinctively from conventional learning-based approaches to natural language understanding (NLU), mental-image-directed theory (MIDST) proposes a robotic deep NLU methodology based on a mental image model as a formal system for knowledge representation and reasoning. The application system named CoMaS is designed to understand <PERSON><PERSON>’s utterances in text and respond in text or animation through human-like spatiotemporal reasoning based on the mental image model. In this work, CoMaS was compared with human subjects through a psychological experiment on spatiotemporal language understanding and showed globally good agreement with them and locally some interesting and reasonable disagreement. This kind of disagreement was found among the human participants as well and explainable as difference in personal conceptualization or reasoning based on mental image. The experimental results and theoretical discussion based on them showed well the effectiveness and uniqueness of our study.</p>", "Keywords": "Commonsense reasoning; Mental image model; Natural language understanding; Spatiotemporal language", "DOI": "10.1007/s10015-023-00905-8", "PubYear": 2024, "Volume": "29", "Issue": "1", "JournalId": 4137, "JournalTitle": "Artificial Life and Robotics", "ISSN": "1433-5298", "EISSN": "1614-7456", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational Science and Digital Technology, Kasetsart University, Nakhonpathom, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Science Laboratory, Fukuoka Institute of Technology, Fukuoka, Japan; Corresponding author."}], "References": []}, {"ArticleId": 110708923, "Title": "A novel EZS-MSCA and SeLu SqueezeNet-based lung tumor detection and classification", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-023-09341-z", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Classification of non-small cell lung cancer using one-dimensional convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113564", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Volumetric analysis framework for accurate segmentation and classification (VAF-ASC) of lung tumor from CT images", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "24", "Page": "18489", "JournalTitle": "Soft Computing"}, {"Title": "Lung cancer detection and classification with DGMM-RBCNN technique", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15601", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 110708977, "Title": "Robots trends and megatrends: artificial intelligence and the society", "Abstract": "Purpose \nThe purpose of this study is to analyze the robot trends of the next generation.\n \n \n Design/methodology/approach \nThis paper is divided into two sections: the key modern technology on which Europe's robotics industry has built its foundation is described. Then, the next key megatrends were analyzed.\n \n \n Findings \nArtificial intelligence (AI) and robotics are technologies of major importance for the development of humanity. This time is mature for the evolution of industrial and service robots. The perception of robot use has changed from threading to aiding. The cost of mass production of technological devices is decreasing, while a rich set of enabling technologies is under development. Soft mechanisms, 5G and AI have enabled us to address a wide range of new problems. Ethics should guide human behavior in addressing this newly available powerful technology in the right direction.\n \n \n Originality/value \nThe paper describes the impact of new technology, such as AI and soft robotics. The world of work must react quickly to these epochal changes to enjoy their full benefits.", "Keywords": "Robots trends;Soft robotics;Artificial intelligence;AI and robotics;Robotics and workplaces;Education and robotics;Ageing workforce;Frugal innovation (FI);Climate change;Robot ethics;Control and systems engineering;Industrial and manufacturing engineering", "DOI": "10.1108/IR-05-2023-0095", "PubYear": 2024, "Volume": "51", "Issue": "1", "JournalId": 4481, "JournalTitle": "Industrial Robot: An International Journal", "ISSN": "0143-991X", "EISSN": "1758-5791", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of DIME, University of Genoa , Genoa, Italy"}, {"AuthorId": 2, "Name": "Francesco <PERSON>", "Affiliation": "Department of DIME, University of Genoa , Genoa, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Project Office, Italian Institute of Technology , Genoa, Italy"}, {"AuthorId": 4, "Name": "Elvezia Maria Cepolina", "Affiliation": "Department of DIME, University of Genoa , Genoa, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Technology Transfer Office, University of Genoa , Genova, Italy"}], "References": [{"Title": "Robotic Process Automation and Artificial Intelligence in Industry 4.0 – A Literature review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "51", "JournalTitle": "Procedia Computer Science"}, {"Title": "Industrial robot programming by demonstration using stereoscopic vision and inertial sensing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; Luís F. R<PERSON>", "PubYear": 2022, "Volume": "49", "Issue": "1", "Page": "96", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Robot obstacle avoidance system using deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "49", "Issue": "2", "Page": "301", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Digital twin modeling", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "", "Page": "372", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Transferring artificial intelligence practices between collaborative robotics and autonomous driving", "Authors": "Milan Zorman; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "52", "Issue": "9", "Page": "2924", "JournalTitle": "Kybernetes"}, {"Title": "Future of industry 5.0 in society: human-centric solutions, challenges and prospective research areas", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "The role of robots in environmental monitoring", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "3", "Page": "369", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "Design and experimental research of the hybrid-driven soft robot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "4", "Page": "648", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "A systematic automated grasping approach for automatic manipulation of fabric with soft robot grippers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "4", "Page": "623", "JournalTitle": "Industrial Robot: An International Journal"}]}, {"ArticleId": 110709070, "Title": "Second-order Confidence Network for Early Classification of Time Series", "Abstract": "<p>Time series data are ubiquitous in a variety of disciplines. Early classification of time series, which aims to predict the class label of a time series as early and accurately as possible, is a significant but challenging task in many time-sensitive applications. Existing approaches mainly utilize heuristic stopping rules to capture stopping signals from the prediction results of time series classifiers. However, heuristic stopping rules can only capture obvious stopping signals, which makes these approaches give either correct but late predictions or early but incorrect predictions. To tackle the problem, we propose a novel second-order confidence network for early classification of time series, which can automatically learn to capture implicit stopping signals in early time series in a unified framework. The proposed model leverages deep neural models to capture temporal patterns and outputs second-order confidence to reflect the implicit stopping signals. Specifically, our model exploits the data not only from a time step but also from the probability sequence to capture stopping signals. By combining stopping signals from the classifier output and the second-order confidence, we design a more robust trigger to decide whether or not to request more observations from future time steps. Experimental results show that our approach can achieve superior results in early classification compared to state-of-the-art approaches.</p>", "Keywords": "", "DOI": "10.1145/3631531", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "Junwei Lv", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), China, and Hefei University of Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei University of Technology, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Laboratory of Pattern Recognition, Institute of Automation, Chinese Academy of Sciences, China"}, {"AuthorId": 4, "Name": "Peipei Li", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), China, and Hefei University of Technology, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), China and Hefei University of Technology, China and Anhui Province Key Laboratory of Industry Safety and Emergency Technology, China"}], "References": [{"Title": "Multistage attention network for multivariate time series prediction", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "383", "Issue": "", "Page": "122", "JournalTitle": "Neurocomputing"}, {"Title": "TS-CHIEF: a scalable and accurate forest algorithm for time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3", "Page": "742", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "TEASER: early and accurate time series classification", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "1336", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Shapelet-transformed Multi-channel EEG Channel Selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "ROCKET: exceptionally fast and accurate time series classification using random convolutional kernels", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "1454", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Extracting diverse-shapelets for early classification on time series", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "6", "Page": "3055", "JournalTitle": "World Wide Web"}, {"Title": "InceptionTime: Finding AlexNet for time series classification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "6", "Page": "1936", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Indirectly Supervised Anomaly Detection of Clinically Meaningful Health Events from Smart Home Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Causal Mechanism Transfer Network for Time Series Domain Adaptation in Mechanical Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Approaches and Applications of Early Classification of Time Series: A Review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "47", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "MultiETSC: automated machine learning for early time series classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "6", "Page": "2602", "JournalTitle": "Data Mining and Knowledge Discovery"}]}, {"ArticleId": 110709269, "Title": "Defense against adversarial attacks via textual embeddings based on semantic associative field", "Abstract": "Deep neural networks are known to be vulnerable to various types of adversarial attacks, especially word-level attacks, in the field of natural language processing. In recent years, various defense methods are proposed against word-level attacks; however, most of those defense methods only focus on synonyms substitution-based attacks, while word-level attacks are not based on synonym substitution. In this paper, we propose a textual adversarial defense method against word-level adversarial attacks via textual embedding based on the semantic associative field. More specifically, we analyze the reasons why humans can read and understand textual adversarial examples and observe two crucial points: (1) There must be a relation between the original word and the perturbed word or token. (2) Such a kind of relation enables humans to infer original words, while humans have the ability to associations. Motivated by this, we introduce the concept of semantic associative field and propose a new defense method by building a robust word embedding, that is, we calculate the word vector by exerting the related word vector to it with potential function and weighted embedding sampling for simulating the semantic influence between words in same semantic field. We conduct comprehensive experiments and demonstrate that the models using the proposed method can achieve higher accuracy than the baseline defense methods under various adversarial attacks or original testing sets. Moreover, the proposed method is more universal, while it is irrelevant to model structure and will not affect the efficiency of training.", "Keywords": "Adversarial examples; Natural language processing; Semantic associative field; Word-level", "DOI": "10.1007/s00521-023-08946-7", "PubYear": 2024, "Volume": "36", "Issue": "1", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, China; School of Cyber Security and Information Law, Chongqing University of Posts and Telecommunications, Chongqing, China"}], "References": [{"Title": "A novel word sense disambiguation approach using WordNet knowledge graph", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "101337", "JournalTitle": "Computer Speech & Language"}]}, {"ArticleId": 110709284, "Title": "From Large Language Models to Databases and Back: A Discussion on Research and Education", "Abstract": "<p>In recent years, large language models (LLMs) have garnered increasing attention from both academia and industry due to their potential to facilitate natural language processing (NLP) and generate highquality text. Despite their benefits, however, the use of LLMs is raising concerns about the reliability of knowledge extraction. The combination of DB research and data science has advanced the state of the art in solving real-world problems, such as merchandise recommendation and hazard prevention [30]. In this discussion, we explore the challenges and opportunities related to LLMs in DB and data science research and education.</p>", "Keywords": "", "DOI": "10.1145/3631504.3631518", "PubYear": 2023, "Volume": "52", "Issue": "3", "JournalId": 23659, "JournalTitle": "ACM SIGMOD Record", "ISSN": "0163-5808", "EISSN": "1943-5835", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CNRS, Univ. Grenoble Alpes, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Univ. Lyon 1, CNRS, IUF, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "HKUST(GZ) & HKUST, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 5, "Name": "Kyuseok <PERSON>", "Affiliation": "Seoul National University, Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hong Kong Baptist University"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Northeastern University, China"}], "References": [{"Title": "An analytical study of large SPARQL query logs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "2-3", "Page": "655", "JournalTitle": "The VLDB Journal"}, {"Title": "The future is big graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "9", "Page": "62", "JournalTitle": "Communications of the ACM"}, {"Title": "Hoping for the Best as AI Evolves", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "4", "Page": "6", "JournalTitle": "Communications of the ACM"}, {"Title": "Data Science---A Systematic Treatment", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "7", "Page": "106", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 110709310, "Title": "Development and evaluation of fiber reinforced modular soft actuators and an individualized soft rehabilitation glove", "Abstract": "In the research and development of soft rehabilitation gloves (SRGs), individualized support considering users’ morphological and biomechanical characteristics is the next critical step toward use in rehabilitation practice. Compared with single-structure soft actuators for a whole finger, which require redesigning for each user, modular soft actuators enable the support for each joint by adjusting rigid connectors that are easier to fabricate. Several modular actuators have been developed, however, neither the effect of interaction between soft actuators and fingers nor the functionality of a hand under the support of an SRG has been appropriately evaluated. In this study, we proposed a new modular soft actuator that improved the support performance with enlarged chambers and fiber reinforcement . Moreover, we made an SRG with the proposed actuators and conducted an objective evaluation for the range of motion (ROM), torque, and response time of both actuators and the SRG with a dummy finger and hand. Furthermore, the <PERSON><PERSON><PERSON><PERSON> test, a clinical evaluation of the thumb’s opposition, was performed to show the performance of the proposed actuators and the SRG. As a result, the new modular soft actuators showed better support performance, and the SRG can support multiple grasping profiles and enable the thumb’s opposition. This work moves one step toward applying soft robotics to rehabilitation.", "Keywords": "", "DOI": "10.1016/j.robot.2023.104571", "PubYear": 2024, "Volume": "171", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Engineering, Graduate School of Engineering, Chiba University, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Medical Engineering, Graduate School of Engineering, Chiba University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Medical Engineering, Graduate School of Engineering, Chiba University, Japan;Center for Frontier Medical Engineering, Chiba University, Japan"}], "References": [{"Title": "A review of soft wearable robots that provide active assistance: Trends, common actuation methods, fabrication, and applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "e3", "JournalTitle": "Wearable Technologies"}]}, {"ArticleId": 110709338, "Title": "METHOD FOR INCREASING OSSEOINTEGRATION OF MEDICAL EQUIPMENT BASED ON LASER FOAMING OF METAL SURFACES", "Abstract": "", "Keywords": "", "DOI": "10.21685/2227-8486-2023-3-11", "PubYear": 2023, "Volume": "", "Issue": "3", "JournalId": 78084, "JournalTitle": "MODELS, SY<PERSON>EM<PERSON>, NET<PERSON>OR<PERSON> IN ECONOMICS, ENGINEERING, NATURE AND SOCIETY", "ISSN": "2227-8486", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Penza State University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Penza State University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Penza State University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Penza State University"}], "References": []}, {"ArticleId": 110709423, "Title": "Addressing Class Imbalance in Multiple Causal Relations and Keyword Network for Maritime Safety Management", "Abstract": "최근 자연어처리 기술을 활용하여 해양사고의 인과 키워드를 추출하고 분석하는 연구가 많이 수행되고 있으나, 복합적 요인에 의해서 발생하는 해양사고는 복수의 인과관계 포함하므로, 클래스 불균형 문제가 발생할 수 있다. 본연구에서는 클래스 불균형 문제를 극복함으로써, 해양사고 사례 내 복수의 인과관계를 효과적으로 추출할 수 있는 방법을 제안한다. 복수인과관계의 등장으로 인한 클래스 불균형 문제를 극복하기 위하여 개체명 인식과 관계 추출을 분리하고 단계적으로 수행한다. 이를 위해 해양사고 판결문 1,365건을 수집하여 학습 데이터셋을 구축하였으며, 사전학습 언어 모델인 RoBERTa를 파인튜닝(fine-tuning) 하였다. 실험을 통해 복수의 인과관계를 포함하는 해양 사고의 인과 키워드 추출이 가능함을 확인하였다. 또한, 추출한 인과 키워드를 기반으로 키워드 네트워크를 구축하였고, 사고의 원인과 결과를 직관적으로 파악할 수 있음을 확인하였다.", "Keywords": "해양 안전관리;키워드 추출;인과관계 추출;인과관계 네트워크;Maritime Safety Management;Keyword Extraction;Causal Relation Extraction;Causal Network;RoBERTa", "DOI": "10.5391/JKIIS.2023.33.5.454", "PubYear": 2023, "Volume": "33", "Issue": "5", "JournalId": 15630, "JournalTitle": "Journal of Korean Institute of Intelligent Systems", "ISSN": "1976-9172", "EISSN": "2288-2324", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 110709437, "Title": "Who’s the Enemy?: Angreifermodelle für Produktentwicklung und Infrastruktur", "Abstract": "Zusammenfassung</h3> <p>Wie schützt man sich vor etwas, das man nicht kennt? Im Forschungsbereich Cybersecurity and Law am FZI Forschungszentrum Informatik wird ein thematisch breites Spektrum an Forschungsthemen im Bereich der Informationssicherheit behandelt. Immer wieder stellt sich dabei die Frage, wie passend implementierte Sicherheitsmechanismen sind. Welche Maßnahmen bieten einen Mehrwert beim Schutz von Systemen? Diese Frage lässt sich ohne Kontext nicht ohne Weiteres beantworten, da die Wirksamkeit von Sicherheitsmechanismen davon abhängt, gegen wen oder was man sich schützen möchte. An diese Stelle tritt die Angreifermodellierung: Durch Abstraktion von realen Angreifenden in Angreifermodelle kann die Wahl geeigneter Schutzmechanismen und -maßnahmen unterstützt werden. Der Beitrag beginnt mit einem Einblick in die Klassifizierung von Angreifenden und beschreibt die Anwendung von Angreifermodellen für Unternehmen, die IoT Geräte entwickeln, sowie bei kleinen und mittleren Unternehmen. </p>", "Keywords": "", "DOI": "10.1007/s11623-023-1843-4", "PubYear": 2023, "Volume": "47", "Issue": "11", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "FZI Forschungszentrum Informatik, Karlsruhe, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "FZI Forschungszentrum Informatik, Karlsruhe, Deutschland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "FZI Forschungszentrum Informatik, Karlsruhe, Deutschland"}], "References": []}, {"ArticleId": 110709546, "Title": "INTELLIGENT ANALYSIS OF SENSORY DATA BASED ON FUZZY LOGIC AND NEURAL NETWORK IN CRITICAL EVENT MONITORING SYSTEMS", "Abstract": "", "Keywords": "", "DOI": "10.21685/2227-8486-2023-3-9", "PubYear": 2023, "Volume": "", "Issue": "3", "JournalId": 78084, "JournalTitle": "MODELS, SY<PERSON>EM<PERSON>, NET<PERSON>OR<PERSON> IN ECONOMICS, ENGINEERING, NATURE AND SOCIETY", "ISSN": "2227-8486", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Penza State University"}], "References": []}, {"ArticleId": 110709556, "Title": "Automating the Translation of Cloud Users’ High-Level Security Needs to an Optimal Placement Model in the Cloud Infrastructure", "Abstract": "Deploying network security functions in the cloud to protect the application, dynamically and with different goals, has been one of the most important topics. An example of these functions can be firewall, DPI, or IDS, which can be placed between different layers of applications as needed. So far, many works have been done regarding the placement of network security functions in the cloud, considering various goals, such as minimizing the consumption of resources, energy consumption, and the cost of users or the service provider. One of the required tasks in this field is to automate the translation of users’ security needs into a placement model to be executed by a solver. In all works, this translation is not considered due to the lack of a model or pattern, and it is assumed that the user has a specific requirement, and as a result, more focus is placed on optimizing the placement or placing the functions in the right place. In order to execute the optimal placement algorithms, we need appropriate goals and related constraints. In this paper, for the first time, a model is proposed to translate high-level user needs to the goals and constraints of the optimal placement algorithm. To ensure the accuracy of the generated placement models, three different scenarios have been considered from the cloud user’s point of view, and the generated placement model has been implemented in the popular Fat-tree topology with the number of nodes from 36 to 540,800. The real scenarios presented in the paper and the accuracy of the output produced by automation model can be welcomed by cloud service providers.", "Keywords": "Automation;cloud computing;NFV;network security defence patterns;security function placement", "DOI": "10.1109/TSC.2023.3327632", "PubYear": 2023, "Volume": "16", "Issue": "6", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Shahid Beheshti University (SBU), Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Engineering, Shahid Beheshti University (SBU), Tehran, Iran"}], "References": [{"Title": "Machine learning-driven service function chain placement and scaling in MEC-enabled 5G networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "166", "Issue": "", "Page": "106980", "JournalTitle": "Computer Networks"}, {"Title": "Energy efficient network service deployment across multiple SDN domains", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "449", "JournalTitle": "Computer Communications"}, {"Title": "Service Function Chain Placement in Distributed Scenarios: A Systematic Review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Energy aware fuzzy approach for placement and consolidation in cloud data centers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "161", "Issue": "", "Page": "130", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Correction to: Joint Reliability-Aware and Cost Efficient Path Allocation and VNF Placement using Sharing Scheme", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}]}, {"ArticleId": 110709612, "Title": "Resampling Fuzzy Numbers with Statistical Applications: FuzzyResampling Package", "Abstract": "The classical bootstrap has proven its usefulness in many areas of statistical inference. However, some shortcomings of this method are also known. Therefore, various bootstrap modifications and other resampling algorithms have been introduced, especially for real-valued data. Recently, bootstrap methods have become popular in statistical reasoning based on imprecise data often modeled by fuzzy numbers. One of the challenges faced there is to create bootstrap samples of fuzzy numbers which are similar to initial fuzzy samples but different in some way at the same time. These methods are implemented in FuzzyResampling package and applied in different statistical functions like single-sample or two-sample tests for the mean. Besides describing the aforementioned functions, some examples of their applications as well as numerical comparisons of the classical bootstrap with the new resampling algorithms are provided in this contribution. © (2023). All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2023-036", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Systems Research Institute, Polish Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Mathematics and Information Science, Warsaw University of\r\nTechnology"}], "References": []}, {"ArticleId": 110709649, "Title": "Optimizing ODP Device Placement on FTTH Network Using Genetic Algorithms", "Abstract": "<p>Currently the problem of Optical Distribution Point (ODP) infrastructure is important in fiber to the home (FTTH) network access because ODP infrastructure development is no longer dependent on demand, so placing ODP manually without a systematic method can cause an increase in the value of optical fiber attenuation. on the length of the cable and cause the cable distribution to be irregular. This study aims to optimize the placement of ODP devices in PT BCV's FTTH network by using the Traveling Salesman Problem (TSP) scheme with the genetic algorithm (GA) approach and using hybrid GA, testing is carried out using Matlab software. Testing with development using Hybrid GA gets the best path with a fitness value of 28.6457 and a computation time of 89.93 seconds.</p>", "Keywords": "Genetic algorithm;infrastructure development;optical distribution Point", "DOI": "10.22146/ijccs.84358", "PubYear": 2023, "Volume": "17", "Issue": "4", "JournalId": 42354, "JournalTitle": "IJCCS (Indonesian Journal of Computing and Cybernetics Systems)", "ISSN": "1978-1520", "EISSN": "2460-7258", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Universitas Andalas, Padang, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, Universitas Andalas, Padang, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Computing And Meta-Technology, Universiti Pendidikan Sultan <PERSON>, Malaysia"}, {"AuthorId": 4, "Name": "Nurus Sabah", "Affiliation": "Faculty of Engineering, Universitas Tanjungpura Pontianak, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sekolah Tinggi Teknik Ar-Rahmah, Bintan, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sekolah Tinggi Teknik Ar-Rahmah, Bintan, Indonesia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing, Sekolah Tinggi Teknik Ar-Rahmah, Bintan, Indonesia"}], "References": []}, {"ArticleId": 110709675, "Title": "Classification Methods Performance On Logistic Package State Recognition", "Abstract": "<p> In the distribution sector, logistic package experience activities, such as transport, distribution, storage, packaging, and handling. Even though those processes have reasonable operational procedures, sometimes the package experience mishandling. The mishandling is hard to identify because many packages run simultaneously, and not all processes are monitored. An Inertial Measurement Unit (IMU) is installed inside a package to collect three acceleration and rotation data. The data is then labeled manually into four classes: correct handling, vertical fall, and thrown and rotating fall. Then, using cross-validation, ten classifiers were used to generate a model to classify the logistic package status and evaluate the accuracy score. It is hard to differentiate between free-fall and thrown. The classification only uses the accelerometer data to minimize the running time. The correct handling classification gives a good result because the data pattern has few variations. However, the thrown, free-fall and rotating data give a lower result because the pattern resembles each other. The average accuracy of the ten classifications is 78.15, with a mean deviation of 4.31. The best classifier for this research is the Gaussian Process, with a mean accuracy of 94.4 % and a deviation of 3.5 %.</p>", "Keywords": "Classification;Logistic;IoT;IMU;Mishandling", "DOI": "10.22146/ijccs.82697", "PubYear": 2023, "Volume": "17", "Issue": "4", "JournalId": 42354, "JournalTitle": "IJCCS (Indonesian Journal of Computing and Cybernetics Systems)", "ISSN": "1978-1520", "EISSN": "2460-7258", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Electronics, FMIPA UGM, Yogyakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Electronics, FMIPA UGM, Yogyakarta"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Bachelor Program of Electronics and Instrumentation, FMIPA UGM, Yogyakarta"}, {"AuthorId": 4, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Bachelor Program of Electronics and Instrumentation, FMIPA UGM, Yogyakarta"}], "References": []}, {"ArticleId": 110709797, "Title": "Better value estimation in Q-learning-based multi-agent reinforcement learning", "Abstract": "<p>In many real-life scenarios, multiple agents necessitate cooperation to accomplish tasks. Benefiting from the significant success of deep learning, many single-agent deep reinforcement learning algorithms have been extended to multi-agent scenarios. Overestimation in value estimation of Q-learning is a significant issue that has been studied comprehensively in the single-agent domains, but rarely in multi-agent reinforcement learning. In this paper, we first demonstrate that Q-learning-based multi-agent reinforcement learning (MARL) methods generally have notably serious overestimation issues, which cannot be alleviated by current methods. To tackle this problem, we introduce the double critic networks structure and the delayed policy update to Q-learning-based multi-agent MARL methods, which reduce the overestimation and enhance the quality of policy updating. To demonstrate the versatility of our proposed method, we select several Q-learning based MARL methods and evaluate them on several multi-agent tasks on the multi-agent particle environment and SMAC. Experimental results demonstrate that the proposed method can avoid the overestimation problem and significantly improve performance. Besides, application in the Traffic Signal Control verifies the feasibility of applying the proposed method in real-world scenarios.</p>", "Keywords": "Multi-agent reinforcement learning; Value estimation; Overestimation issue; Q-Learning", "DOI": "10.1007/s00500-023-09365-5", "PubYear": 2024, "Volume": "28", "Issue": "6", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Intelligence and Computing, Tianjin University, Tianjin, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China; Corresponding author."}], "References": [{"Title": "Multi-DQN: An ensemble of Deep Q-learning agents for stock market forecasting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "113820", "JournalTitle": "Expert Systems with Applications"}, {"Title": "How to train your robot with deep reinforcement learning: lessons we have learned", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4-5", "Page": "698", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "A visual path-following learning approach for industrial robots using DRL", "Authors": "<PERSON>-<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102130", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Multi-agent deep reinforcement learning: a survey", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "895", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A distributed deep reinforcement learning method for traffic light control", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "490", "Issue": "", "Page": "390", "JournalTitle": "Neurocomputing"}, {"Title": "Value function factorization with dynamic weighting for deep multi-agent reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "615", "Issue": "", "Page": "191", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 110709844, "Title": "Dense-and-Similar Object detection in aerial images", "Abstract": "The general object detection performance has been improving significantly due to the prosperity of deep learning . When applied to aerial images , these algorithms perform poorly. There are, as we summarized, two practical reasons: (1) photographed from far above, objects are commonly small in size, dense in clusters, but sparsely scattered in the whole image; (2) some classes are naturally similar in appearance, not to mention the insufficiency of distinguishable visual features captured by cameras due to factors such as bad weather, various angles of view and long distances, etc. To address the two issues, we propose a dense-and-similar object detector with four key components: a coarse detector, an adaptive clustering procedure, a similar-class classifier, and a fine detector. At first, we input the original image into both the coarse and fine detectors. Then we cluster and patch the coarsely detected results adaptively to form a foreground region image. We feed the patched image into the coarse detector again and use the similar-class classifier to re-identify the labels of detected bounding boxes . At last, we put re-identified detections and outputs of the fine detector together, and obtain the final detections after non-maximal suppression. The approach proposed in this study achieved excellent results on both the VisDrone 2019 and DIOR datasets through experimental validation.", "Keywords": "", "DOI": "10.1016/j.patrec.2023.10.028", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Innovation Institute of Defense Technology, Academy of Military Science, Beijing, 100071, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Innovation Institute of Defense Technology, Academy of Military Science, Beijing, 100071, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Aerospace Science and Technology Corporation Intelligent Unmanned System Overall Technology Research and Development Center, Beijing, 100094, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Innovation Institute of Defense Technology, Academy of Military Science, Beijing, 100071, China;Corresponding author"}], "References": [{"Title": "Small object detection in remote sensing images based on super-resolution", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "153", "Issue": "", "Page": "107", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Adaptive dynamic networks for object detection in aerial images", "Authors": "Zhenyu Wu; Haibin Yan", "PubYear": 2023, "Volume": "166", "Issue": "", "Page": "8", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "SOLARNet: A single stage regression based framework for efficient and robust object recognition in aerial images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "172", "Issue": "", "Page": "37", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 110709860, "Title": "Comparing perfomance abstractions for collective adaptive systems", "Abstract": "<p>Non-functional properties of collective adaptive systems (CAS) are of paramount relevance practically in any application. This paper compares two recently proposed approaches to quantitative modelling that exploit different system abstractions: the first is based on generalised stochastic Petri nets, and the second is based on queueing networks. Through a case study involving autonomous robots, we analyse and discuss the relative merits of the approaches. This is done by considering three scenarios which differ on the architecture used to coordinate the distributed components. Our experimental results assess a high accuracy when comparing model-based performance analysis results derived from two different quantitative abstractions for CAS.</p>", "Keywords": "Behavioural specifications; Model-based performance predictions; Queueing Networks; Generalised Stochastic Petri Nets", "DOI": "10.1007/s10009-023-00728-9", "PubYear": 2023, "Volume": "25", "Issue": "5-6", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Gran Sasso Science Institute, L’Aquila, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gran Sasso Science Institute, L’Aquila, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Gran Sasso Science Institute, L’Aquila, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Gran Sasso Science Institute, L’Aquila, Italy"}], "References": [{"Title": "Rigorous engineering of collective adaptive systems: special section", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "4", "Page": "389", "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)"}, {"Title": "Partitioned integration and coordination via the self-organising coordination regions pattern", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "44", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "CAMP: cost-aware multiparty session protocols", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Multiparty motion coordination: from choreographies to robotics programs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 110709980, "Title": "Kùzu: A Database Management System For \"Beyond Relational\" Workloads", "Abstract": "<p>I would like to share my opinions on the following question: how should a modern graph DBMS (GDBMS) be architected? This is the motivating research question we are addressing in the K`uzu project at University of Waterloo [4, 5].1 I will argue that a modern GDBMS should optimize for a set of what I will call, for lack of a better term, \"beyond relational\" workloads. As a background, let me start with a brief overview of GDBMSs.</p>", "Keywords": "", "DOI": "10.1145/3631504.3631514", "PubYear": 2023, "Volume": "52", "Issue": "3", "JournalId": 23659, "JournalTitle": "ACM SIGMOD Record", "ISSN": "0163-5808", "EISSN": "1943-5835", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Waterloo"}], "References": [{"Title": "Optimizing One-time and Continuous Subgraph Queries using Worst-case Optimal Joins", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Database Systems"}]}, {"ArticleId": 110710026, "Title": "Positional Encoding-based Resident Identification in Multi-resident Smart Homes", "Abstract": "<p>We propose a novel resident identification framework to identify residents in a multi-occupant smart environment. The proposed framework employs a feature extraction model based on the concepts of positional encoding. The feature extraction model considers the locations of homes as a graph. We design a novel algorithm to build such graphs from layout maps of smart environments. The Node2Vec algorithm is used to transform the graph into high-dimensional node embeddings. A Long Short-Term Memory (LSTM) model is introduced to predict the identities of residents using temporal sequences of sensor events with the node embeddings. Extensive experiments show that our proposed scheme effectively identifies residents in a multi-occupant environment. Evaluation results on two real-world datasets demonstrate that our proposed approach achieves 94.5% and 87.9% accuracy, respectively.</p>", "Keywords": "", "DOI": "10.1145/3631353", "PubYear": 2024, "Volume": "24", "Issue": "1", "JournalId": 23139, "JournalTitle": "ACM Transactions on Internet Technology", "ISSN": "1533-5399", "EISSN": "1557-6051", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Sydney, Australia"}], "References": [{"Title": "A sequential deep learning application for recognising human activities in smart homes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "501", "JournalTitle": "Neurocomputing"}, {"Title": "Smart home resident identification based on behavioral patterns using ambient sensors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "151", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Smart home reasoning systems: a systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "4485", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An internet of things service roadmap", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "9", "Page": "86", "JournalTitle": "Communications of the ACM"}, {"Title": "Driver Identification Using Optimized Deep Learning Model in Smart Transportation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "A Survey on Conflict Detection in IoT-based Smart Homes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 110710031, "Title": "Correction", "Abstract": "", "Keywords": "", "DOI": "10.1080/00207721.2023.2278944", "PubYear": 2024, "Volume": "55", "Issue": "3", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [], "References": []}, {"ArticleId": 110710047, "Title": "Publisher Erratum zu: Aktive Cyberabwehr", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11623-023-1826-5", "PubYear": 2023, "Volume": "47", "Issue": "11", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Darmstadt, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Darmstadt, Deutschland"}], "References": []}, {"ArticleId": 110710145, "Title": "Medizinische Innovation fördern und Patientendaten schützen", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11623-023-1842-5", "PubYear": 2023, "Volume": "47", "Issue": "11", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [], "References": []}, {"ArticleId": 110710253, "Title": "Neighborhood multigranulation rough sets for cost-sensitive feature selection on hybrid data", "Abstract": "Feature selection is a vital preprocessing step in real applications of data mining and machine learning . With the prevalence of high-dimensional hybrid data sets in real-world scenarios, along with the presence of test costs and misclassification costs, the need for effective feature selection methods has become more prominent. However, existing feature selection approaches mainly focus on cost-sensitive data from a single granularity perspective, and they are primarily applicable to single-typed data sets. To address these limitations, this paper presents a novel feature selection approach specifically designed for hybrid data, considering variable test costs and misclassification costs. The proposed method is based on neighborhood multigranulation rough sets (NMRS), which provides more comprehensive and multi-angle data analysis for cost-sensitive hybrid data. First, a novel multigranulation model is developed to effectively process cost-sensitive hybrid data. Building upon this model, a cost-based multi-criteria measure is proposed to evaluate the significance of features. This measure takes into account the comprehensive information of candidate features, including their power in algebraic view, information view, and associated costs. Furthermore, a heuristic feature selection algorithm based on NMRS is proposed to handle hybrid data with the test costs and misclassification costs. This algorithm leverages the benefits of the proposed multigranulation model and cost-based measure to identify the most discriminative features efficiently. Finally, the experimental results on twelve different datasets show that the proposed heuristic algorithm outperforms other compared algorithms, especially in total cost and classification accuracy .", "Keywords": "", "DOI": "10.1016/j.neucom.2023.126990", "PubYear": 2024, "Volume": "565", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "Wenhao Shu", "Affiliation": "School of Information Engineering, East China Jiaotong University, Nanchang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, East China Jiaotong University, Nanchang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Jiangxi Agricultural University, Nanchang, China;Corresponding author"}], "References": [{"Title": "Granularity-driven sequential three-way decisions: A cost-sensitive approach to classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "644", "JournalTitle": "Information Sciences"}, {"Title": "Neighborhood multi-granulation rough sets-based attribute reduction using Lebesgue and entropy measures in incomplete neighborhood decision systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105373", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Incremental feature selection for dynamic hybrid data using neighborhood rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105516", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Attribute reducts of multi-granulation information system", "Authors": "Qingzhao Kong; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1353", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Cost-sensitive hierarchical classification for imbalance classes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "8", "Page": "2328", "JournalTitle": "Applied Intelligence"}, {"Title": "A mixed integer linear programming support vector machine for cost-effective feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106145", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Cost-sensitive feature selection on multi-label data via neighborhood granularity and label enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2210", "JournalTitle": "Applied Intelligence"}, {"Title": "A novel approach to attribute reduction based on weighted neighborhood rough sets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "220", "Issue": "", "Page": "106908", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel hybrid feature selection method considering feature interaction in neighborhood rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "107167", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Triple-G: a new MGRS and attribute reduction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "2", "Page": "337", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 110710330, "Title": "Privacy-preserving recommendation system based on user classification", "Abstract": "Recommender systems have become ubiquitous in many application domains such as e-commerce and entertainment to recommend items that are interesting to the users. Collaborative Filtering is one of the most widely known techniques for implementing a recommender system, it models user–item interactions using data such as ratings to predict user preferences, which could potentially violate user privacy and expose sensitive data. Although there exist solutions for protecting user data in recommender systems, such as utilising cryptography, they are less practical due to computational overhead. In this paper, we propose RSUC, a privacy-preserving Recommender System based on User Classification. RSUC incorporates homomorphic encryption for better data confidentiality. To mitigate performance issues, RSUC classifies similar users in groups and computes the recommendation in a group while retaining privacy and accuracy. Furthermore, an optimised approach is applied to RSUC to further reduce communication and computational costs using data packing. Security analysis indicates that RSUC is secure under the semi-honest adversary model. Experimental results show that RSUC achieves 4× performance improvement over the standard approach and offers 54× better overall performance over the existing solution.", "Keywords": "Privacy ; Homomorphic encryption ; Recommender systems ; Collaborative Filtering", "DOI": "10.1016/j.jisa.2023.103630", "PubYear": 2023, "Volume": "79", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing Technologies, RMIT University, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing Technologies, RMIT University, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing Technologies, RMIT University, Australia"}, {"AuthorId": 4, "Name": "Fengling Han", "Affiliation": "School of Computing Technologies, RMIT University, Australia"}], "References": [{"Title": "Improved collaborative filtering recommendation algorithm based on differential privacy protection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> Shi; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "7", "Page": "5161", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Privacy Preserving Location-Aware Personalized Web Service Recommendations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "3", "Page": "791", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Federated matrix factorization for privacy-preserving recommender systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107700", "JournalTitle": "Applied Soft Computing"}, {"Title": "FedPOIRec: Privacy-preserving federated poi recommendation with social influence", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "623", "Issue": "", "Page": "767", "JournalTitle": "Information Sciences"}]}]