[{"ArticleId": 93025742, "Title": "Multimodal information interaction and fusion for the parallel computing system using AI techniques", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJHPSA.2021.10045122", "PubYear": 2021, "Volume": "10", "Issue": "3/4", "JournalId": 28011, "JournalTitle": "International Journal of High Performance Systems Architecture", "ISSN": "1751-6528", "EISSN": "1751-6536", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93025794, "Title": "Social Media and Networks for Sharing Scholarly Information Among Social Science Research Scholars in the State Universities of Tamil Nadu", "Abstract": "<p>The study examines the impact and utilization of social networks and media for disseminating scholarly information among social science research scholars in south universities of Tamil Nadu, India. The study analyzed the respondent coverage from the universities: University of Madras, Bharathiar University, Bharathidasan University, Madurai Kamaraj University, Alagappa University, Manonmaniam Sundaranar University, and Periyar University. An aggregate of 520 polls appropriated to the respondents out of eight universities in Tamil Nadu and 501 (96.34%) of the filled survey got from the respondents in the social science departments of the universities for the examination. The task of research has taken effort to collect the data to find out usefulness and impact of the social media and networks among the respondents in the selected state universities of Tamil Nadu.</p>", "Keywords": "", "DOI": "10.4018/IJSMOC.2021070104", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 79791, "JournalTitle": "International Journal of Social Media and Online Communities", "ISSN": "2642-2247", "EISSN": "2642-2255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": " Pitchaipandi P.", "Affiliation": ""}], "References": []}, {"ArticleId": 93025797, "Title": "FPGA-based edge computing: Task modeling for cloud-edge collaboration", "Abstract": "<p>With the development of the Internet of Things and devices continuing to scale, using cloud computing resources to process data in real-time is challenging. Edge computing technologies can improve real-time performance in processing data. By introducing the FPGA into the computing node and using the dynamic reconfigurability of the FPGA, the FPGA-based edge node can increase the edge node capability. In this paper, a task-based collaborative method for an FPGA-based edge computing system is proposed in order to meet the collaboration among FPGA-based edge nodes, edge nodes, and the cloud. The modeling of the task includes two parts, task information and task-dependent file. Task information is used to describe the running information and dependency information required for the task execution. Task-dependent file contains the configuration bit-stream of FPGA in running of the task. By analyzing the task behavior, this paper builds four basic behaviors, analyzes the critical attributes of each behavior, and summarizes the task model suitable for FPGA-based edge nodes. Tasks with specific functions can be created by modifying different attributes of model nodes. Finally, the availability of the model and the task-based collaborative method are verified by simulation experiments. The experimental results that the task model proposed in this paper can meet cloud-edge collaboration in the FPGA-based edge computing environment.</p>", "Keywords": "", "DOI": "10.1142/S1793962322410094", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 12484, "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing", "ISSN": "1793-9623", "EISSN": "1793-9615", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Beijing Information Science and Technology University, Beijing 100101, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer, Beijing Information Science and Technology University, Beijing 100101, China"}], "References": [{"Title": "Enhancing the dynamic load balancing technique for cloud computing using HBATAABC algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "2050041", "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing"}]}, {"ArticleId": 93025804, "Title": "Attention Mechanism Based on Improved Spatial-Temporal Convolutional Neural Networks for Traffic Police Gesture Recognition", "Abstract": "Human action recognition has attracted extensive research efforts in recent years, in which traffic police gesture recognition is important for self-driving vehicles. One of the crucial challenges in this task is how to find a representation method based on spatial-temporal features. However, existing methods performed poorly in spatial and temporal information fusion, and how to extract features of traffic police gestures has not been well researched. This paper proposes an attention mechanism based on the improved spatial-temporal convolutional neural network (AMSTCNN) for traffic police gesture recognition. This method focuses on the action part of traffic police and uses the correlation between spatial and temporal features to recognize traffic police gestures, so as to ensure that traffic police gesture information is not lost. Specifically, AMSTCNN integrates spatial and temporal information, uses weight matching to pay more attention to the region where human action occurs, and extracts region proposals of the image. Finally, we use Softmax to classify actions after spatial-temporal feature fusion. AMSTCNN can strongly make use of the spatial-temporal information of videos and select effective features to reduce computation. Experiments on AVA and the Chinese traffic police gesture datasets show that our method is superior to several state-of-the-art methods.", "Keywords": "", "DOI": "10.1142/S0218001422560018", "PubYear": 2022, "Volume": "36", "Issue": "8", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Information Service Engineering, Beijing Union University, Beijing 100101, P. R. China;College of Robotics, Beijing Union University, Beijing 100101, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing 100124, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Tsinghua University, Beijing 100084, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Robotics, Beijing Union University, Beijing 100101, P. R. <PERSON>"}, {"AuthorId": 5, "Name": "Xinka<PERSON> Xu", "Affiliation": "Demonstration Center of Experimental Teaching in Comprehensive Engineering, Beijing Union University, Beijing 100101, P. R. China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Information Service Engineering, Beijing Union University, Beijing 100101, P. R. China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing 100081, P. R. China"}], "References": [{"Title": "Visual Recognition of traffic police gestures with convolutional pose machine and handcrafted features", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "390", "Issue": "", "Page": "248", "JournalTitle": "Neurocomputing"}, {"Title": "Spatiotemporal saliency-based multi-stream networks with attention-aware LSTM for action recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "14593", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Future vehicles: learnable wheeled robots", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "9", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Two-stream spatiotemporal feature fusion for human action recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "7", "Page": "1821", "JournalTitle": "The Visual Computer"}, {"Title": "Combination of temporal‐channels correlation information and bilinear feature for action recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "8", "Page": "634", "JournalTitle": "IET Computer Vision"}, {"Title": "Future vehicles: interactive wheeled robots", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "5", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 93025808, "Title": "An Examination of Verbal Aggression in Politically-Motivated Digital Discourse", "Abstract": "<p>Verbal aggression in digital discourse has emerged as a substantial focus of interest for scholars wishing to examine how conflicts begin, unfold and end on Twitter, YouTube, blogs, and WhatsApp. However, despite all the scholarly attention that Facebook has received since its origin, verbal aggression on Facebook is still relatively under-researched. This paper examines verbal aggression online in the political discourse of Congo-Brazzaville Facebook users. More specifically, quantitative along with qualitative analyses of a dataset of 9,330 Facebook comments were jointly carried out. One critical finding is that explicit aggressive comments are overwhelmingly pervasive and on the increase, pointing to the conclusion that historical, social, sociolinguistic, and political factors are to a larger extent instigators of verbal aggression on Facebook. Furthermore, users employ different strategies to express or intensify verbal aggression, favoring explicit expressions of aggression such as insult and abuse over others like teasing.</p>", "Keywords": "", "DOI": "10.4018/IJSMOC.**********", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 79791, "JournalTitle": "International Journal of Social Media and Online Communities", "ISSN": "2642-2247", "EISSN": "2642-2255", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93025839, "Title": "Knowledge-based mining with the game-theoretic rough set approach to handling inconsistent healthcare data", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJHPSA.2021.10045115", "PubYear": 2021, "Volume": "10", "Issue": "3/4", "JournalId": 28011, "JournalTitle": "International Journal of High Performance Systems Architecture", "ISSN": "1751-6528", "EISSN": "1751-6536", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93026031, "Title": "Modelling the effect of slide table mass on machine tool energy consumption: The role of lightweighting", "Abstract": "Much of the environmental footprint of a machine tool is due to the energy consumed during its use phase. A promising strategy for reducing this energy consumption is to minimize the mass of components through changes in the design of the machine tool. This paper explores the lightweighting of the machine slides (or tables) to achieve energy savings. An energy model is developed which considers the table and workpiece mass, the cutting force, and the motor that drives the table. A case study is carried out on a three-axis vertical milling machine to explore the energy savings that can be realized via lightweighting a slide table. The model is used to compare the energy consumption of a solid table and a lightweight table. Results show a potential energy savings of up to 38% in terms of the energy required to move the table. The model is verified using experimental data already found in the literature. It was observed that the model has a lower prediction capability when the table speed and power demand are very low but as the speed and power increase the prediction capability increases significantly.", "Keywords": "Sustainable manufacturing ; Energy consumption ; Energy modeling ; Machine tools", "DOI": "10.1016/j.jmsy.2022.02.003", "PubYear": 2022, "Volume": "62", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Environmental and Ecological Engineering, Purdue University, 500 Central Drive, West Lafayette, IN 47907, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Environmental and Ecological Engineering, Purdue University, 500 Central Drive, West Lafayette, IN 47907, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Environmental and Ecological Engineering, Purdue University, 500 Central Drive, West Lafayette, IN 47907, USA"}], "References": [{"Title": "An energy-aware scheduling algorithm under maximum power consumption constraints", "Authors": "Ywh-<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "182", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A novel approach to CNC machining center processing parameters optimization considering energy-saving and low-cost", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "59", "Issue": "", "Page": "535", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A generalized method for the inherent energy performance modeling of machine tools", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "406", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Modeling and analyses of energy consumption for machining features with flexible machining configurations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "463", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 93026039, "Title": "Life cycle inventory and assessment data for quantifying the environmental impacts of a wide range of food products belonging to the same food category: A case study of 80 pizzas representatives of the French retail market", "Abstract": "Most of the time, Life Cycle Assessments (LCA) of food products are performed only on one representative of a food category. This doesn&#x27;t allow us to understand the possible variations of environmental impacts within a food product category and the responsible factors for these variations. For this reason, LCAs were conducted for 80 different industrial pizzas representative of the French retail market. The LCAs were performed using the “EF 3.0 Method (adapted) V1.00/EF 3.0 normalization and weighting set” on SimaPro software. Most of the data used were taken from the AGRIBALYSE 3.0 and Ecoinvent 3.6 databases. The system perimeter goes from the production of the ingredients to the pizza consumption. The functional unit used was 1 kg of ready-to-eat pizza. Life cycle inventories were made to include the different flows in the LCA (materials, transport, energy, water, waste, etc.). The dataset contains details on products, life-cycle inventories (LCI) and LCIA results. These data can enrich the discussion on the need to study the environmental impacts of different products belonging to the same food category and not only one representative in order to avoid erroneous conclusions.", "Keywords": "Life cycle assessment (LCA) ; Food industry ; Supply chain ; Processed food ; Recipe", "DOI": "10.1016/j.dib.2022.107950", "PubYear": 2022, "Volume": "41", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, INRAE, AgroParisTech, UMR SayFood, F-78850, Thiverval-<PERSON>non, France;@AdelineCortesi"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université Paris-Saclay, INRAE, AgroParisTech, UMR SayFood, F-78850, <PERSON><PERSON><PERSON><PERSON>, France;Corresponding author;@cpenicaud"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Université Paris-Saclay, INRAE, AgroParisTech, UMR SayFood, F-78850, Thiverval-Grignon, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, INRAE, UR ALISS, 94205, Ivry-sur-Seine, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Université Paris-Saclay, INRAE, AgroParisTech, UMR SayFood, F-78850, Thiverval-Grignon, France;Université d'Avignon, INRAE, UMR SQPOV, 84911 Avignon Cedex 9, France;@IsabelleSouchon"}], "References": []}, {"ArticleId": 93026047, "Title": "Arabic handwritten alphabets, words and paragraphs per user (AHAWP) dataset", "Abstract": "This article presents a handwritten Arabic alphabets, words and paragraphs dataset (AHAWP). The dataset contains 65 different Arabic alphabets (with variations on begin, end, middle and regular alphabets), 10 different Arabic words (that encompass all Arabic alphabets) and 3 different paragraphs. The dataset was collected anonymously from 82 different users. Each user was asked to write each alphabet and word 10 times. A userid uniquely but anonymously identifies the writer of each alphabet, word and paragraph. In total, the dataset consists of 53199 alphabet images, 8144 words images and 241 paragraphs images. This dataset can be used for multiple purposes. It can be used for optical handwriting recognition of alphabets and words. It can also be used for writer identification (or verification) of handwritten Arabic text. It is also possible to evaluate difference in writing styles of isolated alphabets as compared to the same alphabet written as part of the word or in paragraph by the same user using this dataset. The dataset is publicly available at https://data.mendeley.com/datasets/2h76672znt/1 .", "Keywords": "Handwritten Arabic alphabets ; Handwritten Arabic words ; Handwritten Arabic paragraphs ; Writer identification ; Arabic Text recognition", "DOI": "10.1016/j.dib.2022.107947", "PubYear": 2022, "Volume": "41", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Engineering and Science, <PERSON> University, Khobar, Eastern Province, Saudi Arabia;Corresponding author"}], "References": []}, {"ArticleId": 93026072, "Title": "A comparison of temperature and relative humidity measurements derived from COSMIC-2 radio occultations with radiosonde observations made over the Asian summer monsoon region", "Abstract": "The newly launched FORMOSAT-7/COSMIC-2 (Formosa Satellite Mission-7 and Constellation Observing System for Meteorology, Ionosphere, and Climate-2), has an equatorial constellation of six satellites carrying advanced radio occultation receivers, providing high-resolution measurements of temperature and humidity between 45° N to 45° S. COSMIC-2 provides global data of about 5000–6000 profiles per day and has better profiling over the tropics as compared to FORMOSAT-3/COSMIC-1. The vertical resolution of COSMIC-2 (50 m up to tropopause) is better than COSMIC-1 (100 m). In this study, two years of COSMIC-2 measurements are compared with radiosonde observations over the Asian summer monsoon region, which is a highly monsoon active region and a gateway for stratospheric pollutants. The present study analysed the accuracy of temperature and water vapour measurements in the lower and middle atmosphere. A very good agreement between COSMIC-2 and radiosonde measured temperature by an absolute mean difference of less than 0.5 K with a standard deviation of less than 2.5 K is observed. Relative humidity shows a mean difference of 10% with a standard deviation of 15–20%. These results indicate that the COSMIC-2 sounding is robust, has high accuracy, and has excellent global coverage, especially over the tropical and sub-tropical regions. Acknowledgments The authors greatly appreciate the team of COSMIC-2 for providing valuable data through COSMIC Analysis and Archive Centre, ECMWF for the ERA5 data, India Meteorological Department and University of Wyoming for radiosonde data. The authors Veenus Venugopal and Bukya Sama thank the Indian Space Research Organisation for providing doctoral fellowship during the study period. Disclosure statement No potential conflict of interest was reported by the author(s). Data availability statement Publicly available datasets were analysed in this study. COSMIC-2 data is available in the website https://data.cosmic.ucar.edu/gnss-ro/cosmic2/nrt/level2/ ( 10.5065 /t353-c093). Radiosonde data are available from the University of Wyoming sounding data archive http://weather.uwyo.edu/upperair/sounding.html . ERA5 reanalysis data were downloaded from https://cds.climate.copernicus.eu : ( 10.24381/cds.adbb 2d47). Supplementary material Supplemental data for this article can be accessed here .", "Keywords": "", "DOI": "10.1080/2150704X.2022.2033345", "PubYear": 2022, "Volume": "13", "Issue": "4", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Space Physics Laboratory, <PERSON><PERSON><PERSON> Space Centre, ISRO, Thiruvananthapuram, India;Department of Physics, University of Kerala, Thiruvananthapuram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Space Physics Laboratory, <PERSON><PERSON><PERSON> Space Centre, ISRO, Thiruvananthapuram, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Space Physics Laboratory, <PERSON><PERSON><PERSON> Space Centre, ISRO, Thiruvananthapuram, India;Department of Physics, University of Kerala, Thiruvananthapuram, India"}, {"AuthorId": 4, "Name": "K. <PERSON><PERSON>", "Affiliation": "Space Physics Laboratory, <PERSON><PERSON><PERSON> Space Centre, ISRO, Thiruvananthapuram, India"}], "References": []}, {"ArticleId": 93026080, "Title": "SAR image segmentation with combined <PERSON><PERSON> rule, GMTRJ, and EM algorithms", "Abstract": "This study analyses the roles of all the classes in the segmentation procedure, and aims to improve the segmentation accuracy of synthetic aperture radar (SAR) images. Accordingly, weight variables of all classes for SAR images are defined and incorporated into the segmentation model by <PERSON><PERSON>’ rule and geometric partition tessellation. In addition, the Generalized Multiple-Try Reversible Jump (GMTRJ) and expectation maximization (EM) algorithms are designed to obtain the values of weight variables and the optimal segmentation by simulating the segmentation model. The greater the value of the weight variable, the more important the role of its class. The proposed approach can automatically determine the classes’ roles in the segmentation procedure and segment SAR images accurately and quickly. Finally, the proposed and comparison approaches are tested on SAR images, and the results fully demonstrate the effectiveness of the proposed approach. Disclosure statement No potential conflict of interest was reported by the authors. Additional information Funding This research was funded by the National Nature Science Foundation of China [grant number 41901370], Guangxi Natural Science Foundation [AD19110064,2020GXNSFBA297096] (grant number 2020GXNSFBA297096, AD19110064; Guangxi Key Laboratory of Spatial Information and Geomatics, grant number [19-050-11-20].", "Keywords": "", "DOI": "10.1080/2150704X.2022.2026519", "PubYear": 2022, "Volume": "13", "Issue": "4", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangxi Key Laboratory of Spatial Information and Geomatics, Guilin University of Technology, Guilin, China;College of Geomatics and Geoinformation, Guilin University of Technology, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Spatial Information and Geomatics, Guilin University of Technology, Guilin, China;College of Geomatics and Geoinformation, Guilin University of Technology, Guilin, China"}], "References": []}, {"ArticleId": 93026327, "Title": "Theory analysis method for the effect of temperature and electrostrictive on the actuation stability of dielectric elastomers", "Abstract": "<p> Dielectric elastomer (DE) as an electroactive soft material can produce large deformation induced by voltage. Usually, the deformation is limited by various failure modes, which affect the stability of the DE electrical actuation. The previous researches have demonstrated temperature affection on the dielectric constant of DE. In this paper, the mathematical calculation theory for the various unstable DE failure is deduced with the thermoelastic model. The numerical model and analysis result discussed the effects of temperature and electrostriction coefficient on the instability phenomena of DE. The nominal stress deformation changes with the nominal electric displacement on the DE material at different temperatures. The stable working range area encircles by the curves of electromechanical instability (EMI), tension loss ( s = 0), electrical breakdown (EB), and tension rupture (λ = λ <sub>R</sub> ). The results show that the increase of temperature can improve the nominal stress and the nominal electric field of the actuator, which consequently enhances the stability of DE. Simultaneously, the stability of DE can also be improved when the electrostriction coefficient becomes smaller within a certain range. These conclusions have implications for improving the stability of DE electro-deformation. </p>", "Keywords": "", "DOI": "10.1177/1045389X221077449", "PubYear": 2022, "Volume": "33", "Issue": "18", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Du", "Affiliation": "College of Mechanical and Electronic Engineering, Shandong University of Science and Technology, Qingdao, Shandong Province, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electronic Engineering, Shandong University of Science and Technology, Qingdao, Shandong Province, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electronic Engineering, Shandong University of Science and Technology, Qingdao, Shandong Province, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electronic Engineering, Shandong University of Science and Technology, Qingdao, Shandong Province, P. R. China"}, {"AuthorId": 5, "Name": "Hong Yu", "Affiliation": "College of Science, China University of Petroleum, Qingdao, Shandong Province, P. R. China"}], "References": []}, {"ArticleId": 93026366, "Title": "Induction motor fault detection by a new sliding mode observer based on backstepping", "Abstract": "<p>In order to detect the fault of induction motor quickly and accurately, a new fault detection method based on backstepping sliding mode observer is proposed in this paper. The new sliding mode observer has good robustness to unknown load disturbance, can effectively weaken the chattering phenomenon, and has better response speed and stability than exponential and traditional observers. Firstly, the backstepping controller is designed according to the mathematical model of induction motor. Secondly, based on the backstepping method, the sliding mode control is introduced, and a new reaching law sliding mode observer is designed to estimate the stator current and speed. After comparing the actual value with the observed value, the self-detection of induction motor fault is realized. Then, the Simulink model under different fault conditions is established to simulate the three faults of stator winding fault, rotor winding fault and simultaneous fault of stator and rotor winding. The comparative experimental results show that the new sliding mode observer based on backstepping can accurately and sensitively detect different types of early micro faults, which has a certain reference value for the engineering application of fault detection.</p>", "Keywords": "Induction motor; Backstepping; Sliding mode observer; Fault detection", "DOI": "10.1007/s12652-022-03755-7", "PubYear": 2023, "Volume": "14", "Issue": "9", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "Lingzhi Yi", "Affiliation": "Hunan Province Multi-Energy Cooperative Control Technology Engineering Research Center, Xiangtan, China; School of Automation and Electronic Information of Xiangtan University, Xiangtan, China"}, {"AuthorId": 2, "Name": "Tao Sun", "Affiliation": "School of Automation and Electronic Information of Xiangtan University, Xiangtan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Electrical Engineering, Hunan University of Science and Technology, Xiangtan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation and Electronic Information of Xiangtan University, Xiangtan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Disaster Prevention and Reduction for Power Grid Transmission and Distribution Equipment, Changsha, China; State Grid Hunan Electric Power Company Disaster Prevention and Reduction Center, Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation and Electronic Information of Xiangtan University, Xiangtan, China"}], "References": [{"Title": "Sliding mode learning algorithm based adaptive neural observer strategy for fault estimation, detection and neural controller of an aircraft", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2547", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "RETRACTED ARTICLE: Sliding-mode control of gantry crane system with recursive least square parameters identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "S1", "Page": "181", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 93026398, "Title": "Market Demand Forecasting Research Based on EEMD and GRU—Supply Chain Management and Decision Support System Exploration in the Era of Artificial Intelligence", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.122029", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "博阳 李", "Affiliation": ""}], "References": []}, {"ArticleId": 93026567, "Title": "An efficient implementation of one-dimensional discrete wavelet transform algorithms for GPU architectures", "Abstract": "In this paper, the authors present several self-developed implementation variants of the Discrete Wavelet Transform (DWT) computation algorithms and compare their execution times against the commonly approved ones for representative modern Graphics Processing Units (GPUs) architectures. The proposed solutions avoid the time-consuming modulo divisions and conditional instructions used for DWT filters wrapping by proper expansion of the DWTs input data vectors. The main goal of the research is to improve the computation times for popular DWT algorithms for representative modern GPU architectures while retaining the code’s clarity and simplicity. The relations between algorithms execution time improvements for GPUs are also compared with their counterparts for traditional sequential processors. The experimental study shows that the proposed implementations, in the case of parallel realization on GPUs, are characterized by very simple kernel code and high execution time performance.", "Keywords": "Time effectiveness optimization; Graphics processing unit (GPU); Discrete wavelet transform (DWT); Lattice structure; Matrix-based approach", "DOI": "10.1007/s11227-022-04331-8", "PubYear": 2022, "Volume": "78", "Issue": "9", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Technology, Lodz University of Technology, Lodz, Poland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Technology, Lodz University of Technology, Lodz, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Technology, Lodz University of Technology, Lodz, Poland"}], "References": []}, {"ArticleId": 93026699, "Title": "Transcendental Logic-Based Formalism for Semantic Representation of Software Project Requirements Architecture", "Abstract": "<p>This article is devoted to the analysis of the situation that has arisen in the practice of using artificial intelligence methods for software development. Nowadays there are many disparate approaches, models, and practices based on the use of narrow intelligence for decision-making at different stages of the life cycle of software products, and an almost complete lack of solutions brought to wide practical use. The article provides a comprehensive overview of the main reasons for the lack of the expected effect from the implementation of Agile and suggests a way to solve this problem based on the use of a self-organizing knowledge model. Based on the heuristic usage of transcendental logic in the terms of &quot;ontological predicates&quot;, such a model makes it possible to create a formalism of the semantic representation of the requirements architecture of a software project, which could provide semantic interoperability and an executable semantic framework for automated ontology generation from unstructured informal software requirements text. The main benefit of this model is that it is flexible and ensures the accumulation of knowledge without the need to change the initial infrastructure as well as that the ontology inference engine is the part of the mechanism of collective interaction of active elements of knowledge and not some externally programmed system of rules that imitate the process of thinking.</p>", "Keywords": "", "DOI": "10.5539/cis.v15n2p15", "PubYear": 2022, "Volume": "15", "Issue": "2", "JournalId": 16023, "JournalTitle": "Computer and Information Science", "ISSN": "1913-8989", "EISSN": "1913-8997", "Authors": [{"AuthorId": 1, "Name": "Oleg V. <PERSON>z", "Affiliation": ""}, {"AuthorId": 2, "Name": "Oleksii O<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93026789, "Title": "Design and control of an energy-efficient triple-column pressure swing distillation configuration for separation of acetone-methanol-hexane mixture", "Abstract": "Although several studies on the design of ternary mixtures with multiple binary azeotropes can be easily found in the existing literature, there are only a few recent design studies dealing with ternary mixtures including both binary and ternary azeotropes. In this study, two alternative triple-column pressure swing distillation (PSD) configurations are proposed for acetone-methanol-hexane mixture including three binary and one ternary minimum boiling homogeneous azeotropes. They are compared based on total annual cost by analyzing ternary maps at different pressures. Results show that methanol-acetone-hexane (M-A-H) separation sequence obtained by a column configuration operating at 0.5, 1 and 5 bar has a better economic performance compared to acetone-methanol-hexane (A-M-H) separation sequence. Moreover, the fully heat-integrated M-A-H configuration results in a further saving of 13.8%. Furthermore, the plantwide control structure of the heat-integrated M-A-H configuration including a pressure-compensated temperature controller shows a good performance against disturbances in feed flowrate and composition.", "Keywords": "Ternary azeotropes ; Pressure-swing distillation ; Process design ; Heat integration ; Dynamic control", "DOI": "10.1016/j.compchemeng.2022.107731", "PubYear": 2022, "Volume": "160", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering, Istanbul Technical University, 34469, Maslak, Istanbul, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Chemical Engineering, Istanbul Technical University, 34469, Maslak, Istanbul, Turkey;Corresponding author"}], "References": []}, {"ArticleId": 93026808, "Title": "Reconstructing <PERSON>’s Anoeta Velodrome", "Abstract": "The 1972 dome to cover the Anoeta Velodrome is the product of the experiences up to that date of the architects <PERSON> and <PERSON>, specialists, respectively, in the design of deployable reticular structures and thin concrete shells using hyperbolic paraboloids. It is a very relevant project in the careers of both architects, as the Anoeta dome was <PERSON>’s last project, and also the last of the domes in which <PERSON><PERSON> participated since 1965. <PERSON>’s death in 1972 marked the end of a phase in <PERSON><PERSON>’s life. This paper describes the virtual reconstruction of a variation of the project and analyses its geometry, based on the subdivision of the sphere into polygons onto which hyperbolic paraboloids were then inserted. New documentation is provided which offers context for the evolution of <PERSON><PERSON>’s work, linking this project to his previous major work: the Palacio de los Deportes built for the 1968 Mexico Olympics.", "Keywords": "<PERSON>; <PERSON>; Anoeta Velodrome; Sphere tessellations; Hypars; Domes; Virtual reconstruction", "DOI": "10.1007/s00004-022-00590-3", "PubYear": 2022, "Volume": "24", "Issue": "4", "JournalId": 7250, "JournalTitle": "Nexus Network Journal", "ISSN": "1590-5896", "EISSN": "1522-4600", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Architecture, Polytechnic University of Madrid, Madrid, Spain"}], "References": [{"Title": "Geometric Evaluation of Deployable Structures Using Parametric Modelling", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON>-Vicario", "PubYear": 2020, "Volume": "22", "Issue": "1", "Page": "247", "JournalTitle": "Nexus Network Journal"}]}, {"ArticleId": 93026961, "Title": "Brain Tumor Classification Based on Hybrid Optimized Multi-features Analysis Using Magnetic Resonance Imaging Dataset", "Abstract": "Brain tumors are deadly but become deadliest because of delayed and inefficient diagnosis process. Large variations in tumor types also instigate additional complexity. Machine vision brain tumor diagnosis addresses the problem. This research’s objective was to develop a brain tumor classification model based on machine vision techniques using brain Magnetic Resonance Imaging (MRI). For this purpose, a novel hybrid-brain-tumor-classification (HBTC) framework was designed and evaluated for the classification of cystic (cyst), glioma, meningioma (menin), and metastatic (meta) brain tumors. The proposed framework lessens the inherent complexities and boosts performance of the brain tumor diagnosis process. The brain MRI dataset was input to the HBTC framework, pre-processed, segmented to localize the tumor region. From the segmented dataset Co-occurrence matrix (COM), run-length matrix (RLM), and gradient features were extracted. After the application of hybrid multi-features, the nine most optimized features were selected and input to the framework’s classifiers, namely multilayer perception (MLP), J48, meta bagging (MB), and random tree (RT) to classify cyst, glioma, menin, and meta tumors. Maximum brain tumor classification performance achieved by the HBTC framework was 98.8%. The components and performance of the proposed framework show that it is a novel and robust classification framework.", "Keywords": "", "DOI": "10.1080/08839514.2022.2031824", "PubYear": 2022, "Volume": "36", "Issue": "1", "JournalId": 7360, "JournalTitle": "Applied Artificial Intelligence", "ISSN": "0883-9514", "EISSN": "1087-6545", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, The Islamia University of Bahawalpur (Iub), Bahawalpur, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, The Islamia University of Bahawalpur (Iub), Bahawalpur, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, <PERSON> University of Agriculture Multan (Mns-uam), Multan, Pakistan"}], "References": [{"Title": "Fully automatic model‐based segmentation and classification approach for MRI brain tumor using artificial neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 93027141, "Title": "Antenatal Management Information System (Case Study: China-Uganda Friendship Hospital, Naguru)", "Abstract": "<p>To eradicate fetal deaths and stillbirths, the World Health Organization (WHO) set up antenatal care guidelines to help expectant mothers through this period. In Uganda today, it is estimated that at least 90% of the expectant mothers receive antenatal care, and with such a large number of people receiving this service, data is collected manually with pen, which makes it a long process and cumbersome to search specific records during emergencies and analysis of data for proper decision making. Therefore, there is a need for an antenatal management information system. The software is sectioned into registration, triage and consultation. The records officer handles registration of patients, patient visits, viewing and printing patient statistics. The senior clinical officer handles triaging the patient and capturing their vitals, viewing and printing patient statistics. The head midwife handles the monthly progress examination, and views and prints patient statistics. All these processes happen in real time. This system is designed to overcome the problems identified in the current antenatal management information system. The interfaces for the new system were implemented using JSP, Bootstrap and Javascript. PostgreSQL was also used for implementing the system database while Spring was used to create interactivity with the database. After the implementation, the new system was then tested and validated. When developing the system, the focus was on making the whole process of information management in the antenatal department faster, more convenient and efficient.</p>", "Keywords": "", "DOI": "10.52589/BJCNIT_FVEUH8TC", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 88034, "JournalTitle": "British Journal of Computer, Networking and Information Technology", "ISSN": "", "EISSN": "2689-5315", "Authors": [{"AuthorId": 1, "Name": "Edison K.", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>.", "Affiliation": ""}], "References": []}, {"ArticleId": 93027156, "Title": "ROB volume 40 issue 3 Cover and Front matter", "Abstract": "", "Keywords": "", "DOI": "10.1017/S0263574722000133", "PubYear": 2022, "Volume": "40", "Issue": "3", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [], "References": []}, {"ArticleId": 93027264, "Title": "DIPPAS: a deep image prior PRNU anonymization scheme", "Abstract": "Source device identification is an important topic in image forensics since it allows to trace back the origin of an image. Its forensics counterpart is source device anonymization, that is, to mask any trace on the image that can be useful for identifying the source device. A typical trace exploited for source device identification is the photo response non-uniformity (PRNU), a noise pattern left by the device on the acquired images. In this paper, we devise a methodology for suppressing such a trace from natural images without a significant impact on image quality. Expressly, we turn PRNU anonymization into the combination of a global optimization problem in a deep image prior (DIP) framework followed by local post-processing operations. In a nutshell, a convolutional neural network (CNN) acts as a generator and iteratively returns several images with attenuated PRNU traces. By exploiting straightforward local post-processing and assembly on these images, we produce a final image that is anonymized with respect to the source PRNU, still maintaining high visual quality. With respect to widely adopted deep learning paradigms, the used CNN is not trained on a set of input-target pairs of images. Instead, it is optimized to reconstruct output images from the original image under analysis itself. This makes the approach particularly suitable in scenarios where large heterogeneous databases are analyzed. Moreover, it prevents any problem due to the lack of generalization. Through numerical examples on publicly available datasets, we prove our methodology to be effective compared to state-of-the-art techniques.", "Keywords": "Deep image prior;Image anonymization;Image forensics;PRNU", "DOI": "10.1186/s13635-022-00128-7", "PubYear": 2022, "Volume": "2022", "Issue": "1", "JournalId": 43752, "JournalTitle": "EURASIP Journal on Information Security", "ISSN": "", "EISSN": "2510-523X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dipartimento di Elettronica, Informazione e Biongegneria - Politecnico di Milano, Milano, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dipartimento di Elettronica, Informazione e Biongegneria - Politecnico di Milano, Milano, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dipartimento di Elettronica, Informazione e Biongegneria - Politecnico di Milano, Milano, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Dipartimento di Elettronica, Informazione e Biongegneria - Politecnico di Milano, Milano, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dipartimento di Elettronica, Informazione e Biongegneria - Politecnico di Milano, Milano, Italy"}], "References": [{"Title": "On robustness of camera identification algorithms", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "1", "Page": "921", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 93027392, "Title": "A novel approach for product competitive analysis based on online reviews", "Abstract": "<p>Recently, online reviews have become a prevalent information source for competitive analysis because they provide rich information on the voices of customers. Based on online reviews, we propose a novel method named Integrated-Degree based K-shell decomposition (ID-KS) to conduct competitive analysis via product comparison networks. Under the consideration of feature differences among products, we apply text-mining approaches and ID-KS to convert online reviews into competitive insights including competitor identification, product comparison, product ranking, brand comparison and market-structure analysis. To validate the feasibility and the effectiveness of ID-KS, we demonstrate our approach in two cases, SUV cars and laptops, and compare it with state-of-the-art methods. The results show that ID-KS analyzes product comparison networks more effectively and properly, and it derives comprehensive comparative insights that are not fully captured by existing studies.</p>", "Keywords": "Competitive analysis; Text analysis; Latent dirichlet allocation; Sentiment analysis; K-shell decomposition", "DOI": "10.1007/s10660-022-09534-y", "PubYear": 2023, "Volume": "23", "Issue": "4", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Management and Economics, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Management and Economics, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Management and Economics, Tianjin University, Tianjin, China"}], "References": []}, {"ArticleId": 93027435, "Title": "Kidney image classification using transfer learning with convolutional neural network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCVR.2022.10045127", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 15056, "JournalTitle": "International Journal of Computational Vision and Robotics", "ISSN": "1752-9131", "EISSN": "1752-914X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Priyanka N.A.", "Affiliation": ""}], "References": []}, {"ArticleId": 93027467, "Title": "Salient Regions and Hierarchical Indexing for Crop Disease Images", "Abstract": "<p>With the development of modern agricultural facilities, crop diseases recognition, nutritional status and morphology achieved rapid growth. To avoid yield loss caused by the delay of disease detection, digital images that contain information with respect to crop growth, disease type and nutrition deficiency have been studied by some researchers. However, traditional image processing methods fail to extract typical disease features of crop images with ambiguous disease information. In this paper, a crop disease image recognition technique based on the salient region and hierarchical indexing was proposed. Improved Harris algorithm and maximum radius were used to calculate the widest salient region. In order to eliminate the effect of different salience distribution ranges between different features, a group of images in the cucumber disease image library were normalized. Experiment results indicate that the time complexity of each algorithm will go up as the size of the dataset increase. Especially when testing large datasets, nonhierarchical and nonclustering, hierarchical and nonclustering and hierarchical based on points all tend to raise the algorithm’s time complexity. Plant Village dataset and AI Challenger 2018 dataset were utilized to compare the recognition performances among the models based on machine learning, neural network, deep learning and our methods. The experiment results show that the method proposed in this paper is capable of recognizing local similar images effectively rather than global similar images, therefore, it has better recognition performance than the model learning methods in the early detection stage of crop disease.</p>", "Keywords": "Salient regions; dynamic weight; hierarchical indexing; clustering; similarity search", "DOI": "10.1142/S0218001422560043", "PubYear": 2022, "Volume": "36", "Issue": "3", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Research Center for Information Technology in Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, P. R. China;National Engineering Research Center for Information Technology in Agriculture, Beijing 100097, P. R. China;Key Laboratory for Information Technologies in Agriculture, Ministry of Agriculture, Beijing 100097, P. R. China;Beijing Engineering Research Center of Agricultural Internet of Things, Beijing 100097, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON>feng Li", "Affiliation": "Beijing Research Center for Information Technology in Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, P. R. China;National Engineering Research Center for Information Technology in Agriculture, Beijing 100097, P. R. China;Key Laboratory for Information Technologies in Agriculture, Ministry of Agriculture, Beijing 100097, P. R. China;Beijing Engineering Research Center of Agricultural Internet of Things, Beijing 100097, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Research Center for Information Technology in Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, P. R. China;National Engineering Research Center for Information Technology in Agriculture, Beijing 100097, P. R. China;Key Laboratory for Information Technologies in Agriculture, Ministry of Agriculture, Beijing 100097, P. R. China;Beijing Engineering Research Center of Agricultural Internet of Things, Beijing 100097, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Beijing Research Center for Information Technology in Agriculture, Beijing Academy of Agriculture and Forestry Sciences, Beijing 100097, P. R. China;National Engineering Research Center for Information Technology in Agriculture, Beijing 100097, P. R. China;Key Laboratory for Information Technologies in Agriculture, Ministry of Agriculture, Beijing 100097, P. R. China;Beijing Engineering Research Center of Agricultural Internet of Things, Beijing 100097, P. R. China"}], "References": [{"Title": "Multi-label learning for crop leaf diseases recognition and severity estimation based on convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "20", "Page": "15327", "JournalTitle": "Soft Computing"}, {"Title": "AWkS: adaptive, weighted k-means-based superpixels for improved saliency detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "2", "Page": "625", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "RETRACTED: Image recognition of competitive aerobics movements based on embedded system and digital image processing", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103925", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Quality is in the Salient Region of the Image", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "9", "Page": "263", "JournalTitle": "Electronic Imaging"}, {"Title": "Deep neural network features fusion and selection based on PLS regression with an application for crops diseases classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107164", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 93027500, "Title": "Harnessing Indigenous Tweets: The Reo Māori Twitter corpus", "Abstract": "Te reo Māori, the Indigenous language of Aotearoa New Zealand, is a distinctive feature of the nation’s cultural heritage. This paper documents our efforts to build a corpus of 79,000 Māori-language tweets using computational methods. The Reo Māori Twitter (RMT) Corpus was created by targeting Māori-language users identified by the Indigenous Tweets website, pre-processing their data and filtering out non-Māori tweets, together with other sources of noise. Our motivation for creating such a resource is three-fold: (1) it serves as a rich and unique dataset for linguistic analysis of te reo Māori on social media; (2) it can be used as training data to develop and augment Natural Language Processing (NLP) tools with robust, real-world Māori-language applications; and (3) it will potentially promote awareness of, and encourage positive interaction with, the growing community of Māori tweeters, thereby increasing the use and visibility of te reo Māori in an online environment. While the corpus captures data from 2007 to 2020, our analysis shows that the number of tweets in the RMT Corpus peaked in 2014, and the number of active tweeters peaked in 2017, although at least 600 users were still active in 2020. To the best of our knowledge, the RMT Corpus is the largest publicly-available collection of social media data containing (almost) exclusively Māori text, making it a useful resource for language experts, NLP developers and Indigenous researchers alike.", "Keywords": "Corpus linguistics;Endangered languages;Indigenous languages;Social media;Te reo Māori;Twitter", "DOI": "10.1007/s10579-022-09580-w", "PubYear": 2022, "Volume": "56", "Issue": "4", "JournalId": 3989, "JournalTitle": "Language Resources and Evaluation", "ISSN": "1574-020X", "EISSN": "1574-0218", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing and Mathematical Sciences, University of Waikato, Hamilton, New Zealand."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing and Mathematical Sciences, University of Waikato, Hamilton, New Zealand."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Māori and Indigenous Studies, University of Waikato, Hamilton, New Zealand."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computing and Mathematical Sciences, University of Waikato, Hamilton, New Zealand."}], "References": [{"Title": "Hybrid Hashtags: #YouKnowYoureA<PERSON>iwiWhen Your Tweet Contains Māori and English", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "", "Page": "15", "JournalTitle": "Frontiers in Artificial Intelligence"}]}, {"ArticleId": 93027528, "Title": "Students’ Attendance Management in Higher Institutions Using Azure Cognitive Service and Opencv Face Detection & Recognition Attendance System", "Abstract": "<p>This research aimed at studying the current methods of attendance used at higher institutions of learning in Uganda and the feasibility of using facial biometrics as a new method of capturing attendance. Facial biometrics is distinct from other biometrics because it can be carried out without the consent of the person involved. As a result, the researcher developed a face recognition attendance system using OpenCV and Microsoft Azure CS. Questionnaires, interviews, and observations were used to capture data for the research. The data were analyzed using SPSS to get the requirements and systems functionalities. Object-Oriented Design tools were used to model the architecture of the system. Data Flow Diagram, Use-Case Diagram, Activity Diagram, and Flow Chart were used for processing whereas Entity Relation Diagram was used for data modeling. The system was designed to facilitate attendance management of a large number of attendees with ease. Efficiency and reliability were essential features of the system. Data visualization was provided to help management make informed and timely decisions on management matters that are related to attendance. The system was developed using python Tkinter, OpenCV, and Azure CS as mentioned above. The data (images) used by the system were stored in the cloud for accessibility by multiple users. The system was tested thoroughly using various testing types to uncover and fix errors and to minimize the severity of failures.</p>", "Keywords": "", "DOI": "10.52589/BJCNIT-ALQQMEEE", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 88034, "JournalTitle": "British Journal of Computer, Networking and Information Technology", "ISSN": "", "EISSN": "2689-5315", "Authors": [{"AuthorId": 1, "Name": "Edison K.", "Affiliation": ""}, {"AuthorId": 2, "Name": "Sani U.", "Affiliation": ""}], "References": []}, {"ArticleId": 93027599, "Title": "Electromagnetic flanging using a field shaper with multiple seams", "Abstract": "<p>Electromagnetic forming is a high-speed forming method based on pulsed magnetic force, which can greatly improve the forming limit of materials. Flat helical coils are often used in conventional electromagnetic flanging. It is difficult to set multi-turn coils in the flanged area if the size of the hole is small. The field shaper is used to concentrate the magnetic field into the specific area to be deformed. Compared with the tapered field shaper, the single-step field shaper is more efficient because it can generate greater electromagnetic force in the forming area. However, the magnetic field distribution on the workpiece will not be uniform due to one seam existence. Thus, the electromagnetic flanging using a field shaper with multiple seams was proposed. The distribution of the electromagnetic force on the sheet can be adjusted by adding short seam on the field shaper. Finally, the deformation uniformity of the sheet is improved due to the uniform distribution of electromagnetic force on sheet. The sheet-deformed profile obtained by experiment and simulations was compared, and the correctness of simulation results was verified.</p>", "Keywords": "Electromagnetic flanging; Multi-seam field shaper; Numerical simulation", "DOI": "10.1007/s00170-022-08842-9", "PubYear": 2022, "Volume": "120", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Central South University, Changsha, China;Light Alloy Research Institute, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sichuan Aerospace-Changzheng Equipment Manufacturing Co., Ltd., Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sichuan Aerospace-Changzheng Equipment Manufacturing Co., Ltd., Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Central South University, Changsha, China;Light Alloy Research Institute, Central South University, Changsha, China;State Key Laboratory of High Performance Complex Manufacturing, Central South University, Changsha, People’s Republic of China"}, {"AuthorId": 5, "Name": "Shengping Ye", "Affiliation": "Sichuan Aerospace-Changzheng Equipment Manufacturing Co., Ltd., Chengdu, China"}, {"AuthorId": 6, "Name": "<PERSON>yang Qiu", "Affiliation": "College of Mechanical and Electrical Engineering, Central South University, Changsha, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Central South University, Changsha, China"}], "References": []}, {"ArticleId": 93027628, "Title": "On the social and technical challenges of Web search autosuggestion moderation", "Abstract": "<p>Past research shows that users benefit from systems that support them in their writing and exploration tasks. The autosuggestion feature of Web search engines is an example of such a system: It helps users formulate their queries by offering a list of suggestions as they type. Such autosuggestions are typically generated by machine learning (ML) systems trained on a corpus of search logs and document representations. These automated methods can however become prone to issues that might result in the system making problematic suggestions that are biased, racist, sexist or in other ways inappropriate. While current search engines have become increasingly proficient at suppressing many types of problematic suggestions, there are still persistent issues that remain. In this paper, we reflect on past efforts and on why certain issues still linger by covering explored solutions along a prototypical pipeline for identifying, detecting, and mitigating problematic autosuggestions. To showcase their complexity, we discuss several dimensions of problematic suggestions, difficult issues along the pipeline, and why our discussion applies to an increasing number of applications (beyond Web search) that implement similar textual suggestion features. By outlining several persistent social and technical challenges in moderating Web search suggestions, we hope to provide a renewed call for action.</p>", "Keywords": "", "DOI": "10.5210/fm.v27i2.10887", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 13787, "JournalTitle": "First Monday", "ISSN": "", "EISSN": "1396-0466", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Twitter"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Microsoft"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Google"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Microsoft"}], "References": []}, {"ArticleId": 93027645, "Title": "“You will never win” The digital review economy and mobile gambling applications", "Abstract": "<p>With the advent of online gambling and gaming applications, stores such as Google Play host thousands of online reviews from satisfied and disgruntled customers. Online reviews are powerful tools in the digital review economy, as well as providing and publicizing feedback to game developers and potential users. As review platforms proliferate and impact consumer and developer relationships, digital media scholars need to examine how users engage in these spaces. However, little is known about user expectations of these digital spaces, particularly within the context of the little-regulated online gambling applications. The aims of this study are twofold: first, to identify why digital application review sites are critical to the development of mobile applications, and two, to examine how users engage these spaces within the rapidly expanding online gambling industry. Through a discourse analysis of online reviews, this study identifies how: (1) users reflect on application quality to impact future design developments; (2) users perceive reviews as a part of application development; and, (3) mobile gambling application producers respond to online reviews to further shape digital reputation. Through this analysis, a series of best practices is developed for users and game developers to maximize the feedback and engagement enacted in these mobile spaces.</p>", "Keywords": "", "DOI": "10.5210/fm.v27i2.11586", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 13787, "JournalTitle": "First Monday", "ISSN": "", "EISSN": "1396-0466", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Penn State University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Rowan University"}], "References": []}, {"ArticleId": 93027866, "Title": "Deep Q-Network Interpretability: Applications to ETF Trading", "Abstract": "", "Keywords": "", "DOI": "10.51483/IJAIML.2.1.2022.61-70", "PubYear": 2022, "Volume": "2", "Issue": "1", "JournalId": 90566, "JournalTitle": "International Journal of Artificial Intelligence and Machine Learning", "ISSN": "", "EISSN": "2789-2557", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93027922, "Title": "Error analysis of sparkle contrast by applying image subtraction method", "Abstract": "<p>Measurement error of sparkle contrast was investigated by applying image subtraction method. The sparkle contrast was measured by changing the region of interest (ROI) and the imaging F-number, while the measurement distance was fixed. The statistical error was dependent on the size of the ROI. It was suppressed when the ROI was over 100 × 100 LMD pixels under the condition which the intensity modulation of the image of the display matrix elements optically disappeared on the LMD sensor plane by choosing the specific combination of the effective F-number of the imaging lens, the pitch of the display matrix element, and the optical magnification. The ratio of the intensity standard deviation of the subtracted image to that of the original image could be used as an error indicator of the statistical error by insufficient sampling points. It could also be used for analyzing the error caused by the low-frequency intensity variations within the ROI. In case that the low-frequency intensity variations were not eliminated from the measured sparkle pattern, the sparkle contrast kept changing with increasing the ROI. The error indicator was not converged to the certain value even if the size of the ROI was over 100 × 100 LMD pixels.</p>", "Keywords": "anti-glare;error analysis;sparkle;sparkle contrast", "DOI": "10.1002/jsid.1109", "PubYear": 2022, "Volume": "30", "Issue": "9", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 4, "Name": "Shump<PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd.  Tokyo Japan"}], "References": [{"Title": "4‐2: Anti‐Glare Cover Glass Optical Properties Dependence on the Display Module Configuration", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "1", "Page": "25", "JournalTitle": "SID Symposium Digest of Technical Papers"}]}, {"ArticleId": 93027978, "Title": "Simple agents, smart swarms: a cooperative search algorithm for swarms of autonomous underwater vehicles", "Abstract": "Searching within an unknown environment quickly by utilising a small number of high-capacity robots or a large number of low-cost robots poses an endless question with a non-trivial answer. If the robot's operating environment is underwater, the problem becomes even more complicated due to its three-dimensional nature and the communication restrictions. In this paper, we propose an algorithm appropriate for target searching in unknown underwater environments. The proposed method considers a homogeneous decentralised multi-robot coordination scheme applied from a single-robot configuration to a large swarm. In this model, simple agents (SA) form smart swarms (SS), despite SA do not need to have a strong ability to transmit search and location information, and the SS can efficiently perform search tasks in unknown environments. Specifically, when a swarm performs a search task, agents only search according to the simple strategy and share mapping information within their communication range, enhancing search efficiency. Simulation results demonstrate the effectiveness and that search time reduces proportionally by increasing the number of robots comprising the swarm, while the repetition search rate does not increase with the expansion of the swarm size. We believe that our SS architecture provides insights into the future application of swarm intelligence.", "Keywords": "Unknown dynamic environments ; cooperatively search ; multi-robot ; search problem", "DOI": "10.1080/00207721.2022.2032465", "PubYear": 2022, "Volume": "53", "Issue": "9", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Turbulence and Complex Systems, Intelligent Biomimetic Design Lab, College of Engineering, Peking University, Beijing, People's Republic of China;<PERSON><PERSON> (Beijing) Robot Technology Co., Ltd., Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Turbulence and Complex Systems, Intelligent Biomimetic Design Lab, College of Engineering, Peking University, Beijing, People's Republic of China;Institute of Ocean Research, Peking University, Beijing, China;Peng Cheng Laboratory, Shenzhen, China"}], "References": [{"Title": "A multirobot target searching method based on bat algorithm in unknown environments", "Authors": "Hongwei Tang; Wei Sun; Hong<PERSON> Yu", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112945", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel hybrid grey wolf optimizer algorithm for unmanned aerial vehicle (UAV) path planning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105530", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A constrained differential evolution algorithm to solve UAV path planning in disaster scenarios", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106209", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A competitive mechanism integrated multi-objective whale optimization algorithm with differential evolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "432", "Issue": "", "Page": "170", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 93028128, "Title": "Image captioning model using attention and object features to mimic human image understanding", "Abstract": "Image captioning spans the fields of computer vision and natural language processing. The image captioning task generalizes object detection where the descriptions are a single word. Recently, most research on image captioning has focused on deep learning techniques, especially Encoder-Decoder models with Convolutional Neural Network (CNN) feature extraction. However, few works have tried using object detection features to increase the quality of the generated captions. This paper presents an attention-based, Encoder-Decoder deep architecture that makes use of convolutional features extracted from a CNN model pre-trained on ImageNet (Xception), together with object features extracted from the YOLOv4 model, pre-trained on MS COCO. This paper also introduces a new positional encoding scheme for object features, the “importance factor”. Our model was tested on the MS COCO and Flickr30k datasets, and the performance is compared to performance in similar works. Our new feature extraction scheme raises the CIDEr score by 15.04%. The code is available at: https://github.com/abdelhadie-almalla/image_captioning", "Keywords": "Image captioning;Object features;Convolutional neural network;Deep learning", "DOI": "10.1186/s40537-022-00571-w", "PubYear": 2022, "Volume": "9", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Higher Institute for Applied Sciences and Technology, Damascus, Syria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Higher Institute for Applied Sciences and Technology, Damascus, Syria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Arab International University, Daraa, Syria"}], "References": []}, {"ArticleId": 93028178, "Title": "Multicriteria decision making taxonomy of code recommendation system challenges: a fuzzy-AHP analysis", "Abstract": "The recommendation systems plays an important role in today’s life as it assist in reliable selection of common utilities. The code recommendation system is being used by the code databases (GitHub, source frog etc.) aiming to recommend the more appropriate code to the users. There are several factors that could negatively impact the performance of code recommendation systems (CRS). This study aims to empirically explore the challenges that could have critical impact on the performance of the CRS. Using systematic literature review and questionnaire survey approaches, 19 challenges were identified. Secondly, the investigated challenges were further prioritized using fuzzy-AHP analysis. The identification of challenges, their categorization and the fuzzy-AHP analysis provides the prioritization-based taxonomy of explored challenges. The study findings will assist the real-world industry experts and to academic researchers to improve and develop the new techniques for the improvement of CRS.", "Keywords": "Code recommendation system; Empirical investigations; Fuzzy-AHP", "DOI": "10.1007/s10799-021-00355-3", "PubYear": 2023, "Volume": "24", "Issue": "2", "JournalId": 20680, "JournalTitle": "Information Technology and Management", "ISSN": "1385-951X", "EISSN": "1573-7667", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Software Engineering Department, Lappeenranta-Lahti University of Technology, Lappeenranta, Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, University of Jyvaskyla, Jyvaskyla, Finland; M3S Empirical Software Engineering Research Unit, University of Oulu, Oulu, Finland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}], "References": []}, {"ArticleId": 93028331, "Title": "Children’s Disease Reasoning Model Based on Knowledge Graph", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.122028", "PubYear": 2022, "Volume": "12", "Issue": "2", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "柏涛 罗", "Affiliation": ""}], "References": []}, {"ArticleId": 93028341, "Title": "Subpar: The Challenges of Gender Parity in Canada’s Artificial Intelligence Ecosystem", "Abstract": "<p>Artificial Intelligence (AI) systems are gaining momentum in complementing and/or replacing performing tasks typically done with the aid of human ability. AI systems, inherently human creations, are, however, beset by, wittingly or unwittingly, so-called male chauvinism, despite all the advancements made in the progress of civilization to make inroads for women&rsquo;s equitable participation in the labor force, particularly as regards to the digital economy versus AI. In regards to the Canadian context, this column has examined the evidence to find research highlighting gender representation in the Canadian AI ecosystem. We found a lack of studies on women and their contribution to AI-related activities. Canadian women&rsquo;s participation in their country&rsquo;s AI sector therefore should go beyond mere instruments such as the Montreal Declaration for a Responsible Development of AI, and disjointed interests. Advocating for Canadian women in the AI sector requires a voice in unison best achieved through parliamentary action. This column is thus issuing a clarion call to attaining gender fairness and equity; global principles under the United Nations (UN) Sustainable Goals, to which the Government of Canada is committed.</p>", "Keywords": "", "DOI": "10.5539/cis.v15n2p1", "PubYear": 2022, "Volume": "15", "Issue": "2", "JournalId": 16023, "JournalTitle": "Computer and Information Science", "ISSN": "1913-8989", "EISSN": "1913-8997", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93028423, "Title": "Exploring human behavior patterns and socio‐demographic factors based on American Time Use Survey", "Abstract": "As human activities in mobile environments are facing an ever‐increasing range of data, it is of great research importance to explore in depth the salient features of data that discriminate human behavior patterns and socio‐demographic factors. However, human activity behavior that consists of a series of complex spatio‐temporal activity processes is difficult to model. In this article, we develop a framework to perform activity behavior pattern mining and recognition, and the proposed framework has been applied to the American Time Use Survey to explore representative activity behavior patterns and socio‐demographic factors. The main contributions are as follows: (1) A method of activity behavior similarity is presented based on daily activities and activity sequences. (2) An activity sequence similarity algorithm with is proposed by line segment tree, greedy algorithm, and dynamic programming. (3) Representative activity behavior patterns and socio‐demographic factors are derived by clustering analysis and mining. (4) The activity behavior pattern is recognized by activity behavior features or socio‐demographic features. Through the experiments, we find that different daily activity behavior patterns are associated with specific socio‐demographic factors.", "Keywords": "activity behavior pattern mining;activity behavior pattern recognition;activity behavior similarity;clustering analysis;socio-demographic factors", "DOI": "10.1002/cpe.6878", "PubYear": 2023, "Volume": "35", "Issue": "20", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology Shandong Jianzhu University  Jinan China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology Shandong Jianzhu University  Jinan China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Information and Telecommunication Co Ltd  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology Shandong Jianzhu University  Jinan China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology Shandong Jianzhu University  Jinan China"}, {"AuthorId": 6, "Name": "Yuling Ma", "Affiliation": "School of Computer Science and Technology Shandong Jianzhu University  Jinan China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering Shandong Electric Power College  Jinan China"}], "References": [{"Title": "Exploring Weather Data to Predict Activity Attendance in Event-based Social Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}]}, {"ArticleId": 93028625, "Title": "How the Utilization of Integrated Industry 4.0 Technologies will Radically Address Medical Data Integrity Concerns in Singapore", "Abstract": "", "Keywords": "", "DOI": "10.51483/IJAIML.2.1.2022.75-117", "PubYear": 2022, "Volume": "2", "Issue": "1", "JournalId": 90566, "JournalTitle": "International Journal of Artificial Intelligence and Machine Learning", "ISSN": "", "EISSN": "2789-2557", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93028679, "Title": "The Future of Artificial Intelligence", "Abstract": "", "Keywords": "", "DOI": "10.51483/IJAIML.2.1.2022.1-37", "PubYear": 2022, "Volume": "2", "Issue": "1", "JournalId": 90566, "JournalTitle": "International Journal of Artificial Intelligence and Machine Learning", "ISSN": "", "EISSN": "2789-2557", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93028724, "Title": "A Critical Review of Blockchain Acceptance Models—Blockchain Technology Adoption Frameworks and Applications", "Abstract": "<p>Blockchain is a promising breakthrough technology that is highly applicable in manifold sectors. The adoption of blockchain technology is accompanied by a range of issues and challenges that make its implementation complicated. To facilitate the successful implementation of blockchain technology, several blockchain adoption frameworks have been developed. However, selecting the appropriate framework based on the conformity of its features with the business sector may be challenging for decision-makers. This study aims to provide a systematic literature review to introduce the adoption frameworks that are most used to assess blockchain adoption and realize business sectors that these models have been applied. Thus, the blockchain adoption models in 56 articles are reviewed and the results of the studies are summarized by categorizing the articles into five main sections including supply chain, industries, financial sector, cryptocurrencies, and other articles (excluded from the former fields). The findings of the study show that the models based on the technology acceptance model (TAM), technology–organization–environment (TOE), and new conceptual frameworks were the focus of the majority of selected articles. Most of the articles have focused on blockchain adoption in different industry fields and supply chain areas.</p>", "Keywords": "blockchain technology; acceptance model; adoption model; blockchain adoption; blockchain acceptance; blockchain acceptance model; blockchain acceptance framework; narrative review blockchain technology ; acceptance model ; adoption model ; blockchain adoption ; blockchain acceptance ; blockchain acceptance model ; blockchain acceptance framework ; narrative review", "DOI": "10.3390/computers11020024", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Arts, Communications and Social Sciences, University Canada West, Vancouver, BC V6Z 2V3, Canada"}], "References": [{"Title": "Fuzzy DEMATEL analysis of barriers to Blockchain-based life cycle assessment in China", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106684", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Blockchain in operations management and manufacturing: Potential and barriers", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106789", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A framework of blockchain technology adoption: An investigation of challenges and expected value", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "3", "Page": "103444", "JournalTitle": "Information & Management"}, {"Title": "Determinants of Blockchain Technology Introduction in Organizations: an Empirical Study among Experienced Practitioners", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON><PERSON>; Polyxeni Vassilakopoulou", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "664", "JournalTitle": "Procedia Computer Science"}, {"Title": "What are the main drivers of Blockchain Adoption within Supply Chain? – an exploratory research", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "495", "JournalTitle": "Procedia Computer Science"}, {"Title": "The Literature Review of Blockchain Adoption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>gi Al-<PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "29", "JournalTitle": "Asian Journal of Research in Computer Science"}]}, {"ArticleId": 93028750, "Title": "High-performance adder using a new XOR gate in QCA technology", "Abstract": "<p>Quantum-dot Cellular Automata is one of the new nanoscale technologies that have been offered as a viable replacement for CMOS. QCA technology has many attractive features in terms of speed, size and power consumption. These features allow the technology an opportunity to be suitable for replacing CMOS technology. In the digital system, the adder circuit is important to do all arithmetic operations such as division, multiplication and subtraction. Therefore, this work introduces a new layout of full adder in QCA technology. The proposed design is built using a novel structure of a 3-input XOR gate. Then, the suggested adder is used to build an 8-bit Ripple Carry Adder (RCA). The presented full adder in this work provided 25%, 30%, and 78% improvement in terms of cell count, area, and cost, respectively, while the suggested 8-bit RCA gives 67%, 25%, 11% and 80% improvement in terms of cell count, area, delay and cost, respectively. In terms of total power consumption, the significant advantage offered by the proposed adder is energy-saving as it reduces the total dissipated energy by 26%, 37% and 45% at three levels of tunneling energy (0.5 Ek , 1 Ek and 1.5 Ek ), respectively. The QCADesigner tool v2.0.3 has been used to design and simulate all circuits in this work and QCAPro tool is used for power calculations.</p>", "Keywords": "QCA technology; Full adder; XOR gate; Nanotechnology; RCA", "DOI": "10.1007/s11227-022-04339-0", "PubYear": 2022, "Volume": "78", "Issue": "9", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Kufa, Najaf, Iraq"}, {"AuthorId": 2, "Name": "Esam Alkaldy", "Affiliation": "University of Kufa, Najaf, Iraq"}], "References": []}, {"ArticleId": 93028852, "Title": "Research on the active pressurized forced lubrication deep drawing process and evaluation of the lubrication effect", "Abstract": "<p>Friction and lubrication are important parameters that affect the quality of sheet metal forming; excellent lubrication conditions and less harmful friction can reduce local thinning, delay fracture, and improve surface quality. Aiming at the poor friction conditions and difficult lubrication in the flange area during deep drawing, an active pressurized forced lubrication deep drawing (FLDD) process was proposed in this paper. A hydraulic system was employed to flush high-pressure lubricating oil into the contact gap between the die and sheet in the flange area. The high-pressure hydrostatic oil film in the contact gap can reduce the real contact area and effectively improve the lubrication conditions. The equipment is simple, the cost is low, and the lubricating oil pressure can be measured and controlled. Under the conditions of 20-kN, 35-kN, and 50-kN blank holder force (BHF), FLDD testing of box parts was carried out with 5-MPa and 9-MPa pressure using water-based lubricating oil. The horizontal comparison experiment was conducted with vegetable oil and water-based lubricating oil under the same process conditions. The test results illustrated that the lubrication effect of vegetable oil was the worst, whereas the lubrication effect of water-based mineral lubricant was significantly improved after pressurization. The maximum forming height was increased by 17.97%, the maximum forming force was reduced by 8.9%, the maximum wall thickness thinning rate was decreased by 7%, and the lubrication effect at 9-MPa pressure was superior to that of 5 MPa. The FLDD process has definite application value in improving the production environment, pollution control, and automatic production.</p>", "Keywords": "Forced lubrication; Hydraulic; Deep drawing; Friction; Comparative test", "DOI": "10.1007/s00170-022-08892-z", "PubYear": 2022, "Volume": "120", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Forging & Stamping Technology and Science, Ministry of Education of China, Yanshan University, Qinhuangdao, China;College of Mechanical Engineering, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Forging & Stamping Technology and Science, Ministry of Education of China, Yanshan University, Qinhuangdao, China;College of Mechanical Engineering, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Forging & Stamping Technology and Science, Ministry of Education of China, Yanshan University, Qinhuangdao, China;College of Mechanical Engineering, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Key Laboratory of Advanced Forging & Stamping Technology and Science, Ministry of Education of China, Yanshan University, Qinhuangdao, China;College of Mechanical Engineering, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Advanced Forging & Stamping Technology and Science, Ministry of Education of China, Yanshan University, Qinhuangdao, China;College of Mechanical Engineering, Yanshan University, Qinhuangdao, China"}], "References": []}, {"ArticleId": 93028874, "Title": "Social Media Bots, Trolls, and the Democratic Mandates in Sub-Saharan Africa", "Abstract": "<p>This exploratory study investigates the role that bots and trolls played on social media in widening the gaps of political partisanship in sub-Saharan Africa. Taking the case of the 2018 Zambian by-elections, the authors examined the relationship between online social media content that propagates hate and organized trolling efforts in Zambia. The study used machine learning tools to identify the origin of the bots on Facebook and Twitter accounts (trolls) of the two major political parties in Zambia (PF and UPND). Online posts that accounted for the election campaigns and the aftermath in the year 2018 were considered for the study. Findings suggest that social-mediated conversations were divided along political lines and that the examined trolling accounts systematically took advantage of the existing echo chambers to create hate messages on Zambian social networks. In other words, the findings indicated that the online hate messages that accounted for violence were neither created by the PF or UPND political parties as earlier studies suggest but by bots and trolls.</p>", "Keywords": "", "DOI": "10.4018/IJSMOC.**********", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 79791, "JournalTitle": "International Journal of Social Media and Online Communities", "ISSN": "2642-2247", "EISSN": "2642-2255", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "lg: An R package for Local Gaussian Approximations", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-079", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "Håkon Otneim", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Estimating Social Influence Effects in Networks Using A Latent Space Adjusted Approach in R", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-069", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93028910, "Title": "CompModels: A Suite of Computer Model Test Functions for Bayesian Optimization", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-076", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93028911, "Title": "Generalized Linear Randomized Response Modeling using GLMMRR", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-104", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93028941, "Title": "Influence of the Dynamic Effects and Grasping Location on the Performance of an Adaptive Vacuum Gripper", "Abstract": "<p>A rigid in-plane matrix of suction cups is widely used in robotic end-effectors to grasp objects with flat surfaces. However, this grasping strategy fails with objects having different geometry e.g., spherical and cylindrical. Articulated rigid grippers equipped with suction cups are an underinvestigated solution to extend the ability of vacuum grippers to grasp heavy objects with various shapes. This paper extends previous work by the authors in the development of a novel underactuated vacuum gripper named <PERSON><PERSON><PERSON> by analyzing the impact of dynamic effects and grasping location on the vacuum force required during a manipulation cycle. An articulated gripper with suction cups, such as Polypus, can grasp objects by adhering to two adjacent faces, resulting in a decrease of the required suction action. Moreover, in the case of irregular objects, many possible grasping locations exist. The model explained in this work contributes to the choice of the most convenient grasping location that ensures the minimum vacuum force required to manipulate the object. Results obtained from an extensive set of simulations are included to support the validity of the proposed analytical approach.</p>", "Keywords": "underactuation; vacuum grasping; suction cups; grasping configurations; inertial effects underactuation ; vacuum grasping ; suction cups ; grasping configurations ; inertial effects", "DOI": "10.3390/act11020055", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "These authors contributed equally to this work"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "These authors contributed equally to this work"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Author to whom correspondence should be addressed.↑These authors contributed equally to this work"}], "References": [{"Title": "Octopus Arm-Inspired Tapered Soft Actuators with <PERSON><PERSON> for Improved Grasping", "Authors": "<PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "5", "Page": "639", "JournalTitle": "Soft Robotics"}]}, {"ArticleId": 93029010, "Title": "Shadow Modelling Algorithm for Photovoltaic Systems: Extended Analysis and Simulation", "Abstract": "<p>In this paper, an algorithm capable of modelling shadows from nearby obstructions onto photovoltaic arrays is proposed. The algorithm developed is based on the calculation of the solar position in the sky for any given instant in order to obtain the shadow projection for any object point. The computation is based on considering the shadows as convex regions and on a rasterization process to evaluate the shadowed area of the array. The idea is extended to provide the shading patterns for a desired range of time and to calculate the efficiency rate of the irradiation power incident on the array in comparison with the non-shadowed case. The algorithm has interesting applications, such as optimizing array positioning and orientation, evaluating the impact of new obstructions on pre-existing array installations, allowing precise and practical data for control strategies and MPPT techniques for partially shaded systems, calculating more realistically constrained payback scenarios and finding the optimal PV array interconnection. The results are illustrated by three numerical examples, in which the effects of a nearby building in the irradiation received by a photovoltaic array throughout the year, panel relocation and different interconnections are analysed.</p>", "Keywords": "Photovoltaic systems; Solar position; Shadow modelling; Algorithm; Efficiency", "DOI": "10.1007/s40313-022-00905-2", "PubYear": 2022, "Volume": "33", "Issue": "5", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "Bárbara Azevedo de Sá", "Affiliation": "Santa Catarina State University, Joinville, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Santa Catarina State University, Joinville, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Santa Catarina State University, Joinville, Brazil"}], "References": []}, {"ArticleId": 93029094, "Title": "New Look-Angle Tracking Guidance Strategy for Impact Time and Angle Control", "Abstract": "This paper presents a new guidance strategy for a certain class of homing missiles subject to various terminal constraints and lateral acceleration limit. To achieve zero miss distance in the presence of various terminal impact constraints, a planar engagement scenario of missiles is considered to develop a feedback guidance law of tracking a time-varying look angle that is modeled as an inverse tangent function. The proposed strategy can be applied to both the impact time control and the impact time and angle control guidance problems. Furthermore, it does not require any forms of time-to-go estimation, numerical iterations, and linearization procedures. Numerical simulations are conducted to validate the effectiveness, robustness, and energy efficiency of the proposed guidance strategy in comparison with other existing guidance strategies.", "Keywords": "Control Guidance; Numerical Simulation; Energy Efficiency; Homing Missile; Sliding Mode Control; Autopilot; Flight Trajectory; Predicted Impact Point; Nonlinear Kinematics; Flight Path Angle", "DOI": "10.2514/1.G006229", "PubYear": 2022, "Volume": "45", "Issue": "3", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, 150001 Harbin, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, 150001 Harbin, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Institute of Technology, 150001 Harbin, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Korea Advanced Institute of Science and Technology, Daejeon 34141, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Iowa State University, Ames, Iowa 50010-2271"}], "References": [{"Title": "Impact Time and Angle Control Against Moving Targets with <PERSON> Angle Shaping", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "5", "Page": "1020", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Impact-Time-Control Guidance Strategy with a Composite Structure Considering the Seeker’s Field-of-View Constraint", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "8", "Page": "1566", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}, {"Title": "Terminal Time-Constrained Nonlinear Interception Strategies Against Maneuvering Targets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "1", "Page": "200", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 93029169, "Title": "Machine Learning – Algorithmic Trading Strategies for Superior Growth, Outperformance and Competitive Advantage", "Abstract": "", "Keywords": "", "DOI": "10.51483/IJAIML.2.1.2022.38-60", "PubYear": 2022, "Volume": "2", "Issue": "1", "JournalId": 90566, "JournalTitle": "International Journal of Artificial Intelligence and Machine Learning", "ISSN": "", "EISSN": "2789-2557", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93029315, "Title": "SAND: semi-automated adaptive network defense via programmable rule generation and deployment", "Abstract": "<p>Cyber security is dynamic as defenders often need to adapt their defense postures. The state-of-the-art is that the adaptation of network defense is done manually (i.e., tedious and error-prone). The ideal solution is to automate adaptive network defense, which is however a difficult problem. As a first step towards automation, we propose investigating how to attain semi-automated adaptive network defense (SAND). We propose an approach extending the architecture of software-defined networking, which is centered on providing defenders with the capability to program the generation and deployment of dynamic defense rules enforced by network defense tools. We present the design and implementation of SAND, as well as the evaluation of the prototype implementation. Experimental results show that SAND can achieve agile and effective dynamic adaptations of defense rules (less than 15 ms on average for each operation), while only incurring a small performance overhead.</p>", "Keywords": "network defense; adaptive defense; automated defense; programmable defense; security services; software-defined networking; security management", "DOI": "10.1007/s11432-020-3193-2", "PubYear": 2022, "Volume": "65", "Issue": "7", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center for Big Data Technology and System, Services Computing Technology and System Lab, Cluster and Grid Computing Lab, School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "Deqing Zou", "Affiliation": "National Engineering Research Center for Big Data Technology and System, Services Computing Technology and System Lab, Hubei Engineering Research Center on Big Data Security, School of Cyber Science and Engineering, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Engineering Research Center for Big Data Technology and System, Services Computing Technology and System Lab, Cluster and Grid Computing Lab, School of Computer Science and Technology, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Colorado at Colorado Springs, Colorado Springs, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Engineering Research Center for Big Data Technology and System, Services Computing Technology and System Lab, Hubei Engineering Research Center on Big Data Security, School of Cyber Science and Engineering, Huazhong University of Science and Technology, Wuhan, China"}], "References": []}, {"ArticleId": 93029316, "Title": "Facial Emotion Intensity: A Fusion Way", "Abstract": "<p>Many scientific works have been conducted for developing the Emotion intensity recognition system. But developing a system that is capable to estimate small to peak intensity levels with less complexity is still challenging. Therefore, we propose an effective facial emotion intensity classifier by fusion of the pre-trained deep architecture and fuzzy inference system. The pre-trained architecture VGG16 is used for basic emotion classification and it predicts emotion class with the class index value. By class index value, images are sent to the corresponding Fuzzy inference system for estimating the intensity level of detected emotion. This fusion model effectively identifies the facial emotions (happy, sad, surprise, and angry) and also predict the 13 categories of emotion intensity. This fusion model got 83% accuracy on a combined dataset (FER 2013, CK + and KDEF). The performance and findings of this proposed work are further compared with state-of-the-art models.</p>", "Keywords": "Pre-trained VGG; Fuzzy inference system; Basic emotion recognition; Emotion intensity estimation; Computer vision", "DOI": "10.1007/s42979-022-01049-5", "PubYear": 2022, "Volume": "3", "Issue": "2", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Statistics and Computer Science, G. B. Pant University of Agriculture and Technology, Pantnagar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Statistics and Computer Science, G. B. Pant University of Agriculture and Technology, Pantnagar, India"}], "References": []}, {"ArticleId": 93029380, "Title": "Ordered Weighted Averaging (OWA), Decision Making under Uncertainty, and Deep Learning: How Is This All Related?", "Abstract": "<p>Among many research areas to which <PERSON> contributed are decision making under uncertainty (in particular, under interval and fuzzy uncertainty) and aggregation—where he proposed, analyzed, and utilized ordered weighted averaging (OWA). The OWA algorithm itself provides only a specific type of data aggregation. However, it turns out that if we allow several OWA stages, one after another, we obtain a scheme with a universal approximation property—moreover, a scheme which is perfectly equivalent to modern ReLU-based deep neural networks. In this sense, <PERSON> can be viewed as a (grand)father of ReLU-based deep learning. We also recall that the existing schemes for decision making under uncertainty are also naturally interpretable in OWA terms.</p>", "Keywords": "ordered weighted averaging (OWA); decision making under uncertainty; deep learning ordered weighted averaging (OWA) ; decision making under uncertainty ; deep learning", "DOI": "10.3390/info13020082", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Texas at El Paso, El Paso, TX 79968, USA"}], "References": []}, {"ArticleId": 93029457, "Title": "Enhanced nuclear norm based matrix regression for occluded face recognition", "Abstract": "An effective approach for the task of face recognition is proposed in this paper, which formulates the problem as an enhanced nuclear norm based matrix regression model and explores the low-rank property of the reconstructed image. Previous works have already leveraged the nuclear norm to obtain a low-rank representation of the error image and get a promising recognition rate. Motivated by the low-rank property of the reconstructed image through theoretical observation, our model imposes the nuclear norm constraints not only on the representation residual but also on the reconstructed image. The proposed method preserves the 2D structural information of the error images and reconstructs images, which is significant for the face recognition tasks. To further improve the performance of the proposed model, we explore the impact of different regularization terms under various scenarios. Extensive experiments on several benchmark datasets show the efficacy of the proposed model especially in terms of robustness against contiguous occlusion and illumination changes, which achieves superior performance over the most competitive methods.", "Keywords": "Face recognition ; Occluded image ; Nuclear norm ; Low-Rank", "DOI": "10.1016/j.patcog.2022.108585", "PubYear": 2022, "Volume": "126", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "Qin Li", "Affiliation": "School of Software Engineering, Shenzhen Institute of Information Technology, Shenzhen 518172, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Services Networks, Xidian University, Xi’an 710071, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Software Engineering, Shenzhen Institute of Information Technology, Shenzhen 518172, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, Shenzhen Institute of Information Technology, Shenzhen 518172, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Services Networks, Xidian University, Xi’an 710071, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Integrated Services Networks, Xidian University, Xi’an 710071, China;Corresponding author"}], "References": [{"Title": "Minimum margin loss for deep face recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "107012", "JournalTitle": "Pattern Recognition"}, {"Title": "A novel approach inspired by optic nerve characteristics for few-shot occluded face recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "376", "Issue": "", "Page": "25", "JournalTitle": "Neurocomputing"}, {"Title": "Face recognition approach by subspace extended sparse representation and discriminative feature learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "373", "Issue": "", "Page": "35", "JournalTitle": "Neurocomputing"}, {"Title": "A local multiple patterns feature descriptor for face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "373", "Issue": "", "Page": "109", "JournalTitle": "Neurocomputing"}, {"Title": "MobileFAN: Transferring deep hidden representation for face alignment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107114", "JournalTitle": "Pattern Recognition"}, {"Title": "Deformable face net for pose invariant face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107113", "JournalTitle": "Pattern Recognition"}, {"Title": "A paired sparse representation model for robust face recognition from a single sample", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107129", "JournalTitle": "Pattern Recognition"}, {"Title": "Noise-robust dictionary learning with slack block-Diagonal structure for face recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107118", "JournalTitle": "Pattern Recognition"}, {"Title": "A novel classification-selection approach for the self updating of template-based face recognition systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107121", "JournalTitle": "Pattern Recognition"}, {"Title": "Face recognition with dense supervision", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Weijun Hong; Hongzhou Zhang", "PubYear": 2020, "Volume": "387", "Issue": "", "Page": "100", "JournalTitle": "Neurocomputing"}, {"Title": "Image decomposition based matrix regression with applications to robust face recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107204", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 93029481, "Title": "Examining the Predictive Capability of Advanced Software Fault Prediction Models – An Experimental Investigation Using Combination Metrics", "Abstract": "Background: Fault prediction is a key problem in software engineering domain. In recent years, an increasing interest in exploiting machine learning techniques to make informed decisions to improve software quality based on available data has been observed.\n\t\t\t\t\t Aim: The study aims to build and examine the predictive capability of advanced fault prediction models based on product and process metrics by using machine learning classifiers and ensemble design.\n\t\t\t\t\t Method: Authors developed a methodological framework, consisting of three phases i.e., (i) metrics identification (ii) experimentation using base ML classifiers and ensemble design (iii) evaluating performance and cost sensitiveness. The study has been conducted on 32 projects from the PROMISE, BUG, and JIRA repositories.\n\t\t\t\t\t Result: The results shows that advanced fault prediction models built using ensemble methods show an overall median of F-score ranging between 76.50% and 87.34% and the ROC(AUC) between 77.09% and 84.05% with better predictive capability and cost sensitiveness. Also, non-parametric tests have been applied to test the statistical significance of the classifiers.\n\t\t\t\t\t Conclusion: The proposed advanced models have performed impressively well for inter project fault prediction for projects from PROMISE, BUG, and JIRA repositories.", "Keywords": "Classifiers; Ensemble design; Product and process metrics; Software fault prediction; Software quality", "DOI": "10.37190/e-Inf220104", "PubYear": 2022, "Volume": "16", "Issue": "1", "JournalId": 73155, "JournalTitle": "e-Informatica Software Engineering Journal", "ISSN": "1897-7979", "EISSN": "2084-4840", "Authors": [{"AuthorId": 1, "Name": "Issam <PERSON>", "Affiliation": "Dr. <PERSON> <PERSON> National Institute of Technology, Jalandhar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dr. <PERSON> <PERSON> National Institute of Technology, Jalandhar, India"}], "References": []}, {"ArticleId": 93029506, "Title": "Correction to: LSTM-TC: Bitcoin coin mixing detection method with a high recall", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10489-022-03249-1", "PubYear": 2022, "Volume": "52", "Issue": "8", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, School of Computer Science, National Pilot Software Engineering School), Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, School of Computer Science, National Pilot Software Engineering School), Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, School of Computer Science, National Pilot Software Engineering School), Beijing University of Posts and Telecommunications, Beijing, China"}], "References": []}, {"ArticleId": 93029641, "Title": "An adaptive stochastic central force optimisation algorithm for node localisation in wireless sensor networks", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2022.10045104", "PubYear": 2022, "Volume": "39", "Issue": "1/2", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON> Chu<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93029658, "Title": "An Empirical Study of Virtual Social Networks", "Abstract": "<p>Virtual social network is a kind of social network established through indirect interactions between individuals, especially through online interactions. Virtual social network has significant implications on business operations, governing, scientific research, and social relations. This paper describes several virtual social networks and studies their properties and effects using empirical approach and computer simulations. Furthermore, the authors find that these virtual social networks have similar distributions with respect to the frequency of nodes and different edges. These distributions are similar between directed networks and undirected networks. In addition, through simulations they demonstrate that the roles played by different nodes in a virtual social network are unequal. Limitations of this study and future research directions are also presented.</p>", "Keywords": "", "DOI": "10.4018/IJSMOC.2021070101", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 79791, "JournalTitle": "International Journal of Social Media and Online Communities", "ISSN": "2642-2247", "EISSN": "2642-2255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93029804, "Title": "Hybrid deep-learning model to detect botnet attacks over internet of things environments", "Abstract": "<p>In recent years, the use of the internet of things (IoT) has increased dramatically, and cybersecurity concerns have grown in tandem. Cybersecurity has become a major challenge for institutions and companies of all sizes, with the spread of threats growing in number and developing at a rapid pace. Artificial intelligence (AI) in cybersecurity can to a large extent help face the challenge, since it provides a powerful framework and coordinates that allow organisations to stay one step ahead of sophisticated cyber threats. AI provides real-time feedback, helping rollover daily alerts to be investigated and analysed, effective decisions to be made and enabling quick responses. AI-based capabilities make attack detection, security and mitigation more accurate for intelligence gathering and analysis, and they enable proactive protective countermeasures to be taken to overwhelm attacks. In this study, we propose a robust system specifically to help detect botnet attacks of IoT devices. This was done by innovatively combining the model of a convolutional neural network with a long short-term memory (CNN-LSTM) algorithm mechanism to detect two common and serious IoT attacks (BASHLITE and Mirai) on four types of security camera. The data sets, which contained normal malicious network packets, were collected from real-time lab-connected camera devices in IoT environments. The results of the experiment showed that the proposed system achieved optimal performance, according to evaluation metrics. The proposed system gave the following weighted average results for detecting the botnet on the Provision PT-737E camera: camera precision: 88%, recall: 87% and F1 score: 83%. The results of system for classifying botnet attacks and normal packets on the Provision PT-838 camera were 89% for recall, 85% for F1 score and 94%, precision. The intelligent security system using the advanced deep learning model was successful for detecting botnet attacks that infected camera devices connected to IoT applications.</p>", "Keywords": "Internet of things; Botnet attacks; Artificial intelligence; Cybersecurity; Convolutional neural network", "DOI": "10.1007/s00500-022-06750-4", "PubYear": 2022, "Volume": "26", "Issue": "16", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Sciences and Information Technology, Al Baha University, Al Bahah, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, College of Computing (Al Qunfudhah), Umm Al-Qura University, Makkah, Saudi Arabia"}], "References": [{"Title": "Unsupervised intelligent system based on one class support vector machine and Grey Wolf optimization for IoT botnet detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "7", "Page": "2809", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An efficient reinforcement learning-based Botnet detection approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "102479", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Performance evaluation of Botnet DDoS attack detection using machine learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "2", "Page": "283", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Evolving deep learning architectures for network intrusion detection using a double PSO metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "107042", "JournalTitle": "Computer Networks"}, {"Title": "Detecting botnet by using particle swarm optimization algorithm based on voting system", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "95", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Defense against distributed DoS attack detection by using intelligent evolutionary algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "3", "Page": "219", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "Adaptive inertia weight Bat algorithm with Sugeno-Function fuzzy search", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106159", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning-based classification model for botnet attack detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "7", "Page": "3457", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A network intrusion detection method based on semantic Re-encoding and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "164", "Issue": "", "Page": "102688", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "An efficient XGBoost–DNN-based classification model for network intrusion detection system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12499", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Near real-time security system applied to SDN environments in IoT networks using convolutional neural network", "Authors": "<PERSON>; <PERSON><PERSON>; Joel J.P.C<PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106738", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "M-AdaBoost-A based ensemble system for network intrusion detection", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "113864", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An efficient approach to detect IoT botnet attacks using machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "3", "Page": "241", "JournalTitle": "Journal of High Speed Networks"}, {"Title": "Classification of botnet attacks in IoT smart factory using honeypot combined with machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 93029900, "Title": "Globally Optimal Linear Model Fitting with Unit-Norm Constraint", "Abstract": "<p>Robustly fitting a linear model from outlier-contaminated data is an important and basic task in many scientific fields, and it is often tackled by consensus set maximization. There have been several studies on globally optimal methods for consensus set maximization, but most of them are currently confined to problems with small number of input observations and low outlier ratios. In this paper, we develop a globally optimal algorithm aiming at consensus set maximization to solve the robust linear model fitting problems with the unit-norm constraint, which is based on the branch-and-bound optimization framework. The unit-norm constraint is utilized to fix the unknown scale of linear model parameters, and we propose a compact representation of the unit-bounded searching domain to avoid introducing the additional non-linearity in the unit-norm constraint. The compact representation leads to a geometrically derived bound, which accelerates the calculation and enables the method to handle the problems with large number of observations. Experiments on both synthetic and real data show that the proposed algorithm outperforms existing globally optimal methods, especially in low dimensional problems with large number of input observations and high outlier ratios. The implementation of the source code is publicly available https://github.com/YiruWangYuri/Demo-for-GoCR .</p>", "Keywords": "Robust fitting; Global optimization; Unit-norm constraint", "DOI": "10.1007/s11263-022-01574-z", "PubYear": 2022, "Volume": "130", "Issue": "4", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technische Universität München, München, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Digital Medical Research Center, School of Basic Medical Sciences, Fudan University, Shanghai, China;Shanghai Key Laboratory of Medical Image Computing and Computer Assisted Intervention, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Digital Medical Research Center, School of Basic Medical Sciences, Fudan University, Shanghai, China;Shanghai Key Laboratory of Medical Image Computing and Computer Assisted Intervention, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Tongji University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Technische Universität München, München, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Digital Medical Research Center, School of Basic Medical Sciences, Fudan University, Shanghai, China;Shanghai Key Laboratory of Medical Image Computing and Computer Assisted Intervention, Shanghai, China"}], "References": [{"Title": "Practical globally optimal consensus maximization by Branch-and-bound based on interval arithmetic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "107897", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 93030165, "Title": "A Privacy-Preserving and Standard-Based Architecture for Secondary Use of Clinical Data", "Abstract": "<p>The heterogeneity of the formats and standards of clinical data, which includes both structured, semi-structured, and unstructured data, in addition to the sensitive information contained in them, require the definition of specific approaches that are able to implement methodologies that can permit the extraction of valuable information buried under such data. Although many challenges and issues that have not been fully addressed still exist when this information must be processed and used for further purposes, the most recent techniques based on machine learning and big data analytics can support the information extraction process for the secondary use of clinical data. In particular, these techniques can facilitate the transformation of heterogeneous data into a common standard format. Moreover, they can also be exploited to define anonymization or pseudonymization approaches, respecting the privacy requirements stated in the General Data Protection Regulation, Health Insurance Portability and Accountability Act and other national and regional laws. In fact, compliance with these laws requires that only de-identified clinical and personal data can be processed for secondary analyses, in particular when data is shared or exchanged across different institutions. This work proposes a modular architecture capable of collecting clinical data from heterogeneous sources and transforming them into useful data for secondary uses, such as research, governance, and medical education purposes. The proposed architecture is able to exploit appropriate modules and algorithms, carry out transformations (pseudonymization and standardization) required to use data for the second purposes, as well as provide efficient tools to facilitate the retrieval and analysis processes. Preliminary experimental tests show good accuracy in terms of quantitative evaluations.</p>", "Keywords": "ETL architecture; secondary use of clinical data; HL7 FHIR; information retrieval; privacy laws; pseudonymization ETL architecture ; secondary use of clinical data ; HL7 FHIR ; information retrieval ; privacy laws ; pseudonymization", "DOI": "10.3390/info13020087", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy, <PERSON>, 111, 80131 Naples, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for High Performance Computing and Networking, National Research Council of Italy, <PERSON>, 111, 80131 Naples, Italy"}], "References": []}, {"ArticleId": 93030178, "Title": "A Survey on Text Classification Algorithms: From Text to Predictions", "Abstract": "<p>In recent years, the exponential growth of digital documents has been met by rapid progress in text classification techniques. Newly proposed machine learning algorithms leverage the latest advancements in deep learning methods, allowing for the automatic extraction of expressive features. The swift development of these methods has led to a plethora of strategies to encode natural language into machine-interpretable data. The latest language modelling algorithms are used in conjunction with ad hoc preprocessing procedures, of which the description is often omitted in favour of a more detailed explanation of the classification step. This paper offers a concise review of recent text classification models, with emphasis on the flow of data, from raw text to output labels. We highlight the differences between earlier methods and more recent, deep learning-based methods in both their functioning and in how they transform input data. To give a better perspective on the text classification landscape, we provide an overview of datasets for the English language, as well as supplying instructions for the synthesis of two new multilabel datasets, which we found to be particularly scarce in this setting. Finally, we provide an outline of new experimental results and discuss the open research challenges posed by deep learning-based language models.</p>", "Keywords": "text classification; tokenisation; topic labelling; news classification; transformer; shallow learning; deep learning; multilabel corpora text classification ; tokenisation ; topic labelling ; news classification ; transformer ; shallow learning ; deep learning ; multilabel corpora", "DOI": "10.3390/info13020083", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Management, Ca’ Foscari University, 30123 Venice, Italy↑Author to whom correspondence should be addressed"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Management, Ca’ Foscari University, 30123 Venice, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Management, Ca’ Foscari University, 30123 Venice, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Environmental Sciences, Informatics and Statistics, Ca’ Foscari University, 30123 Venice, Italy"}], "References": [{"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 93030305, "Title": "Analysis of Dynamic System of Exercise Load Condition Monitoring Based on Characteristic Parameters", "Abstract": "<p>According to the algorithm of time difference and threshold value, this paper selects the more valuable data for motion state recognition and selects the characteristics, respectively selects the data of the combined acceleration and the combined angular velocity, and uses the data of the pitch angle and the roll angle more novelly. In the aspect of data preprocessing, the sliding segmentation window method is used for feature processing, and the time domain and frequency domain features of the data are extracted. A total of 108 dimensional features are extracted. In order to improve the calculation performance, PCA technology is used for data dimensionality reduction. In this paper, we collected data on changes in physiological parameters of 24 experimenters before and after exercise, collected 14 self-evaluated severely fatigued volunteers and self-evaluated severely stressed volunteers’ resting heart rate and blood pressure data as unhealthy data samples, and collected physiological data of 14 healthy experimenters as unhealthy data samples. For healthy samples, three sets of experiments were set up to analyze the changes of exercise heart rate, exercise blood pressure and exercise body temperature, and the effectiveness of fusion of physiological data to improve the performance of exercise recognition and the analysis of the health status of physiological parameters that introduce exercise interference. The experimental results show that during exercise, monitoring changes in systolic blood pressure is more meaningful than monitoring changes in diastolic blood pressure; it verifies the effectiveness of improving the performance of exercise recognition by fusion of physiological parameters. The addition of physiological data can effectively improve the recognition rate of exercise. The recognition rate has been increased from 93.7% to 96.3%; the effectiveness and applicability of the algorithm in this paper are analyzed through design experiments, and the results show that the recognition accuracy of the algorithm in this paper is above 87%. This result has a good classification recognition rate for a small sample.</p>", "Keywords": "", "DOI": "10.1155/2022/1048914", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 11845, "JournalTitle": "Journal of Sensors", "ISSN": "1687-725X", "EISSN": "1687-7268", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Applied Engineering Henan University of Science and Technology, Zhengzhou Henan 472000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Police Sport Henan Police College, Zhengzhou Henan 450046, China"}], "References": []}, {"ArticleId": 93030383, "Title": "The vote Package: Single Transferable Vote and Other Electoral Systems in R", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-086", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Ševčíková", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030405, "Title": "dad: an R Package for Visualisation, Classification and Discrimination of Multivariate Groups Modelled by their Densities", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-071", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>mail <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030424, "Title": "volesti: Volume Approximation and Sampling for Convex Polytopes in R", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-077", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030440, "Title": "Analysis of Corneal Data in R with the rPACI Package", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-099", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Ana,D. <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030450, "Title": "More challenging or more achievable? The impacts of difficulty and dominant goal orientation in leaderboards within educational gamification", "Abstract": "<h3 > Background</h3> <p>As one of the gamification elements, leaderboard, especially absolute leaderboard, is widely used in educational gamification systems. However, empirical studies on the optimal use condition of the leaderboard and underlying influence mechanisms are deficient.</p> <h3 > Objectives</h3> <p>This study explored which difficulty was more conducive to learning performance in leaderboard context, and when and how it played a role.</p> <h3 > Methods</h3> <p>To address these questions, this study conducted a 2 (dominant goal orientation: learning/performance) × 2 (difficulty: high/low) between-subjects design. Seventy-eight dominant learning-oriented and 78 dominant performance-oriented participants were recruited and randomly assigned to the high or low difficulty group respectively.</p> <h3 > Results and Conclusions</h3> <p>Participants in the low difficulty group experienced more positive emotions, less negative emotions, and higher learning motivation than those in the high difficulty group, but the effect of difficulty on performance was not significant. Moreover, goal orientation did not moderate the effects of difficulty, dominant learning-oriented and performance-oriented learners were equally affected by difficulty. Further mediating analysis showed that negative emotions and learning motivation rather than positive emotions mediated the relationship between difficulty and learning performance.</p> <h3 > Implications</h3> <p>These results confirmed the positive effect of low difficulty in leaderboard context, as well as the mediating roles of emotions and motivation involved in the relationship between difficulty and learning performance. These findings enlighten us that it is necessary to equip leaderboards in educational gamification with achievable difficulty.</p> <h3 >Lay Description</h3> <h3 > What is already known about this topic</h3> As one of the gamification elements, leaderboard was widely used in educational gamification. The effect of the leaderboard was not always consistent among various educational situations. Empirical studies on the optimal use condition of the leaderboard were deficient. The mechanisms by difficulty within leaderboards affected the learning performance were not clear. <h3 > What this paper adds</h3> Lower difficulty within leaderboards could induce more positive emotions, less negative emotions and higher learning motivation. Difficulty within leaderboards did not directly affect learning performance. Negative emotions and learning motivation mediated the relationship between difficulty and performance. Dominant goal orientation did not moderate the effect of difficulty on learning. <h3 > The implications of study findings for practitioners</h3> We could improve learners' emotional and motivational states by reducing difficulty within leaderboards. We could promote learning performance through appropriate emotional and motivational designs using gamification.", "Keywords": "difficulty;dominant goal orientation;emotion;learning performance;motivation", "DOI": "10.1111/jcal.12652", "PubYear": 2022, "Volume": "38", "Issue": "3", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Adolescent Cyberpsychology and Behavior (CCNU), Ministry of Education, Wuhan, China; School of Psychology, Central China Normal University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Adolescent Cyberpsychology and Behavior (CCNU), Ministry of Education, Wuhan, China; School of Psychology, Central China Normal University, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Psychology, Henan University, Kaifeng, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "Yan‐<PERSON> Wang", "Affiliation": "Key Laboratory of Adolescent Cyberpsychology and Behavior (CCNU), Ministry of Education, Wuhan, China; School of Psychology, Central China Normal University, Wuhan, China"}], "References": [{"Title": "Gamification in the classroom: Examining the impact of gamified quizzes on student learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "103666", "JournalTitle": "Computers & Education"}, {"Title": "The impact of learner metacognition and goal orientation on problem-solving in a serious game environment", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "151", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Evaluation of technology use in education: Findings from a critical analysis of systematic literature reviews", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "241", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Achievement appraisals, emotions and socio-cognitive processes: How they interplay in collaborative problem-solving?", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "106267", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Virtual reality in problem‐based learning contexts: Effects on the problem‐solving performance, vocabulary acquisition and motivation of English language learners", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Hui‐<PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "851", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "From top to bottom: How positions on different types of leaderboard may affect fully online student learning performance, intrinsic motivation, and course engagement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "104297", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 93030467, "Title": "survidm: An R package for Inference and Prediction in an Illness-Death Model", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-070", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030474, "Title": "spNetwork: A Package for Network Kernel Density Estimation", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-102", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030485, "Title": "A novel global-local block spatial-spectral fusion attention model for hyperspectral image classification", "Abstract": "Deep learning brought a new method for hyperspectral image (HSI) classification, in which images are usually pre-processed by reducing their dimensions before being packaged into pieces to be input to the deep network for feature extraction. However, the learning capability of convolutional kernels of fixed dimensions is usually limited, and thus they are inclined to cause losses of feature details. In this paper, a new global-local block spatial-spectral fusion attention (GBSFA) model is proposed. An improved Inception structure is designed to extract the feature information of the global block, and the self-attention mechanism and spatial pyramid pooling (SPP) are applied to focus on the interclass edge feature information of the local block. Combined with long-short term memory (LSTM) networks, the effective information of the spectral dimension is extracted. Finally, the features extracted from the spatial dimension and the spectral dimension are conveyed in the full connection layer for classification training. Experimental results show that the classification accuracy of the proposed approach is higher than that of other comparative methods using small training sets. Disclosure statement No potential conflict of interest was reported by the author(s). Additional information Funding This research was funded by the National Natural Science Foundation of China (61801018), the Fundamental Research Funds for the Central Universities (FRF-GF-20-13B), and Science and Technology Innovation Foundation of Shunde Graduate School, USTB (BK19CE019).", "Keywords": "", "DOI": "10.1080/2150704X.2021.2022237", "PubYear": 2022, "Volume": "13", "Issue": "4", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Instrument Science and Technology, University of Science and Technology Beijing, Beijing, China;Beijing Engineering Research Center of Industrial Spectrum Imaging, School of Automation and Electrical Engineering, University of Science and Technology Beijing, Beijing, China;Shunde Graduate School, University of Science and Technology Beijing, Beijing, Guangdong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Instrument Science and Technology, University of Science and Technology Beijing, Beijing, China;Beijing Engineering Research Center of Industrial Spectrum Imaging, School of Automation and Electrical Engineering, University of Science and Technology Beijing, Beijing, China;Shunde Graduate School, University of Science and Technology Beijing, Beijing, Guangdong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical, Computer and Telecommunications Engineering, University of Wollongong, Wollongong, NSW, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Instrument Science and Technology, University of Science and Technology Beijing, Beijing, China;Beijing Engineering Research Center of Industrial Spectrum Imaging, School of Automation and Electrical Engineering, University of Science and Technology Beijing, Beijing, China;Shunde Graduate School, University of Science and Technology Beijing, Beijing, Guangdong, China"}], "References": [{"Title": "Data Classification of Hyperspectral Images Based on Inception Networks and Extended Attribute Profiles", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "22", "Page": "8717", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 93030498, "Title": "Super-resolution guided knowledge distillation for low-resolution image classification", "Abstract": "With the development of deep convolutional neural networks, the high-resolution image classification has achieved excellent classification results. However, in natural scenes, low-resolution images are very common, such as images taken by a webcam or images taken with a lens far away from the target object. Low-resolution image classification is a very difficult problem, because low-resolution images have small size and contain fewer discriminative features, which lead to a sharp decline in classification performance. In order to solve the above problem, this paper proposes a Super-Resolution guided Knowledge Distillation (SRKD) framework, which consists of two sub-networks: one is the super-resolution sub-network used to enhance the features of low-resolution images, and the other is the knowledge distillation sub-network used to minimize the difference between the features of high-resolution images and the features of the images output by the super-resolution sub-network. Extensive experiments on the Pascal VOC 2007 and CUB-200-2011 datasets show that the proposed method has a great improvement compared to the benchmark which is trained on high-resolution images. Especially in the case of very low resolution, the proposed method improves the mAP on Pascal VOC 2007 test set by 30.4% and improves the classification accuracy on CUB-200-2011 test set by 60.37% compared with the benchmark model.", "Keywords": "Low-resolution image classification ; Super-resolution ; Knowledge distillation", "DOI": "10.1016/j.patrec.2022.02.006", "PubYear": 2022, "Volume": "155", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "Hong<PERSON> Chen", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing 100044, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Traffic Data Analysis and Mining, Beijing Jiaotong University, Beijing 100044, China"}], "References": [{"Title": "Low resolution face recognition using a two-branch deep convolutional neural network architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112854", "JournalTitle": "Expert Systems with Applications"}, {"Title": "EBIT: Weakly-supervised image translation with edge and boundary enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "534", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "SSFNET-VOS: Semantic segmentation and fusion network for video object segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "49", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 93030785, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0898-1221(22)00058-X", "PubYear": 2022, "Volume": "109", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [], "References": []}, {"ArticleId": 93030836, "Title": "Visual Diagnostics for Constrained Optimisation with Application to Guided Tours", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-105", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>,<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030841, "Title": "Automatic Time Series Forecasting with Ata Method in R: ATAforecasting Package", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-101", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Güçkan Yapar", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>,<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030854, "Title": "StratigrapheR: Concepts for Litholog Generation in R", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-039", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>,<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 93030863, "Title": "RobustBF: An R Package for Robust Solution to the Behrens-Fisher Problem", "Abstract": "", "Keywords": "", "DOI": "10.32614/RJ-2021-107", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Şükrü Acıtaş", "Affiliation": ""}, {"AuthorId": 3, "Name": "Hatice Şamkar", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Şenoğlu", "Affiliation": ""}], "References": []}, {"ArticleId": 93030871, "Title": "diproperm: An R Package for the DiProPerm Test", "Abstract": "<p>High-dimensional low sample size (HDLSS) data sets frequently emerge in many biomedical applications. The direction-projection-permutation (DiProPerm) test is a two-sample hypothesis test for comparing two high-dimensional distributions. The DiProPerm test is exact, i.e., the type I error is guaranteed to be controlled at the nominal level for any sample size, and thus is applicable in the HDLSS setting. This paper discusses the key components of the DiProPerm test, introduces the diproperm R package, and demonstrates the package on a real-world data set.</p>", "Keywords": "", "DOI": "10.32614/RJ-2021-072", "PubYear": 2021, "Volume": "13", "Issue": "2", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "University of North Carolina at Chapel Hill, Department of Biostatistics."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of North Carolina at Chapel Hill, Department of Biostatistics."}, {"AuthorId": 3, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "University of North Carolina at Chapel Hill, Department of Biostatistics."}], "References": []}, {"ArticleId": ********, "Title": "A survey on event-driven and query-driven hierarchical routing protocols for mobile sink-based wireless sensor networks", "Abstract": "<p>The sink mobility in wireless sensor networks (WSNs) brings new challenges specifically while designing the routing protocols. The advertisement of the mobile sink location to the sensor nodes is one of these challenges that is a core problem for any routing protocol. To deal with this issue, several hierarchical routing protocols have been developed for different mode of data transmissions, i.e., event-driven and query-driven, in mobile sink-based WSNs (MSWSNs). A routing protocol considers a particular data transmission mode depending on the application requirement. Routing protocols designed for event-driven scenarios do not ensure their efficient working in query-driven scenarios and vice versa. Thus, each application requires some specific designing of routing protocol. By considering the impact of data transmission mode on routing protocols, this paper makes the very first attempt to provide a comprehensive survey on hierarchical routing protocols designed for query-driven and event-driven data transmission scenarios. We provide a comparative study by discussing and describing their functionalities along with their advantages, disadvantages and key performance parameters. To help readers understand the evolution within each category, the relationship among different routing protocols is outlined with detailed descriptions as well as in-depth analysis.</p>", "Keywords": "Wireless sensor networks; Mobile sink; Hierarchical routing protocol; Event-driven data transmission; Query-driven data transmission", "DOI": "10.1007/s11227-022-04327-4", "PubYear": 2022, "Volume": "78", "Issue": "9", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Thapar Institute of Engineering & Technology, Patiala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Indian Institute of Information Technology, Lucknow, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Wireless Sensor Networks Laboratory, ABV-Indian Institute of Information Technology and Management, Gwalior, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ABV-Indian Institute of Information Technology and Management, Gwalior, India"}], "References": [{"Title": "Energy-efficient data dissemination algorithm based on virtual hexagonal cell-based infrastructure and multi-mobile sink for wireless sensor networks", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "1", "Page": "150", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A dynamic distributed boundary node detection algorithm for management zone delineation in Precision Agriculture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "102712", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Long range wide area network for agricultural wireless underground sensor networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "4903", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A Query Processing Framework for Efficient Network Resource Utilization in Shared Sensor Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}, {"Title": "An energy and coverage sensitive approach to hierarchical data collection for mobile sink based wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "1267", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An extended ACO-based mobile sink path determination in wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "8991", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Nature-inspired algorithms for Wireless Sensor Networks: A comprehensive survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "", "Page": "100342", "JournalTitle": "Computer Science Review"}, {"Title": "Multisensor data fusion technique for energy conservation in the wireless sensor network application “condition-based environment monitoring”", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An optimal mobile sink sojourn location discovery approach for the energy-constrained and delay-sensitive wireless sensor network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "12", "Page": "10837", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Successors of PEGASIS protocol: A comprehensive survey", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "", "Page": "100368", "JournalTitle": "Computer Science Review"}, {"Title": "Strategies based on various aspects of clustering in wireless sensor networks using classical, optimization and machine learning techniques: Review, taxonomy, research findings, challenges and future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100376", "JournalTitle": "Computer Science Review"}, {"Title": "Optimal rendezvous points selection to reliably acquire data from wireless sensor networks using mobile sink", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "4", "Page": "707", "JournalTitle": "Computing"}, {"Title": "EDVWDD: Event-Driven Virtual Wheel-based Data Dissemination for Mobile Sink-Enabled Wireless Sensor Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "10", "Page": "11432", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Clustering protocols in wireless sensor network: A survey, classification, issues, and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100396", "JournalTitle": "Computer Science Review"}, {"Title": "EAM: energy aware method for chain-based routing in wireless sensor network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "9", "Page": "4265", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "An adaptive hierarchical data dissemination mechanism for mobile data collector enabled dynamic wireless sensor network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "103097", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Optimal rendezvous points selection and mobile sink trajectory construction for data collection in WSNs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "6", "Page": "7147", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": ********, "Title": "Bipartite graph capsule network", "Abstract": "Graphs have been widely adopted in various fields, where many graph models are developed. Most of previous research focuses on unipartite or homogeneous graph analysis. In this graphs, the relationships between the same type of entities are preserved in the graphs. Meanwhile, the bipartite graphs that model the complex relationships among different entities with vertices partitioned into two disjoint sets, are becoming increasing popular and ubiquitous in many real life applications. Though several graph classification methods on unipartite and homogenous graphs have been proposed by using kernel method, graph neural network, etc. However, these methods are unable to effectively capture the hidden information in bipartite graphs. In this paper, we propose the first bipartite graph-based capsule network, namely Bipartite Capsule Graph Neural Network (BCGNN), for the bipartite graph classification task. BCGNN exploits the capsule network and obtains information between the same type vertices in the bipartite graphs by constructing the one-mode projection. Extensive experiments are conducted on real-world datasets to demonstrate the effectiveness of our proposed method.", "Keywords": "Capsule network; Graph neural network; Bipartite graph; Graph classification", "DOI": "10.1007/s11280-022-01009-2", "PubYear": 2023, "Volume": "26", "Issue": "1", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of New South Wales, Kensington, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Gongshang University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Gongshang University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Zhejiang Gongshang University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Gongshang University, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of New South Wales, Kensington, Australia"}], "References": [{"Title": "Toward heterogeneous information fusion: bipartite graph convolutional networks for in silico drug repurposing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "Supplement_1", "Page": "i525", "JournalTitle": "Bioinformatics"}, {"Title": "Deep attributed network representation learning of complex coupling and interaction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106618", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Caps-OWKG: a capsule network model for open-world knowledge graph", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "6", "Page": "1627", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Powerful graph of graphs neural network for structured entity analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "2", "Page": "609", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 93031244, "Title": "Exploratory State Representation Learning", "Abstract": "<p> Not having access to compact and meaningful representations is known to significantly increase the complexity of reinforcement learning (RL). For this reason, it can be useful to perform state representation learning (SRL) before tackling RL tasks. However, obtaining a good state representation can only be done if a large diversity of transitions is observed, which can require a difficult exploration, especially if the environment is initially reward-free. To solve the problems of exploration and SRL in parallel, we propose a new approach called XSRL (eXploratory State Representation Learning). On one hand, it jointly learns compact state representations and a state transition estimator which is used to remove unexploitable information from the representations. On the other hand, it continuously trains an inverse model, and adds to the prediction error of this model a k -step learning progress bonus to form the maximization objective of a discovery policy. This results in a policy that seeks complex transitions from which the trained models can effectively learn. Our experimental results show that the approach leads to efficient exploration in challenging environments with image observations, and to state representations that significantly accelerate learning in RL tasks. </p>", "Keywords": "state representation learning; pretraining; exploration; unsupervised learning; deep reinforcement learning", "DOI": "10.3389/frobt.2022.762051", "PubYear": 2022, "Volume": "9", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sorbonne Université, CNRS, Institut des Systèmes Intelligents et de Robotique, ISIR, France"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Sorbonne Université, CNRS, Institut des Systèmes Intelligents et de Robotique, ISIR, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sorbonne Université, CNRS, Institut des Systèmes Intelligents et de Robotique, ISIR, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sorbonne Université, CNRS, Institut des Systèmes Intelligents et de Robotique, ISIR, France"}], "References": []}, {"ArticleId": 93031262, "Title": "An investigation of the relationship between joint visual attention and product quality in collaborative business process modeling: a dual eye-tracking study", "Abstract": "<p>Collaborative business process modeling is a collective activity where team members jointly discuss, design, and document business processes. During such activities, team members need to communicate with each other to coordinate the modeling activities, propose and justify changes, and negotiate common terms and definitions. Throughout this process, stakeholders should be aware of when and what kind of changes have been made by each team member on the shared space so that they can discuss design ideas and build on each other’s work. Joint visual attention has a fundamental role in establishing and maintaining common ground among interlocutors in such cooperative work settings. In addition to this, the co-constructed model's quality is often considered a key evaluation outcome measure to assess the success of collaboration. However, process and outcome measures of collaboration have been prone to difficulties due to challenges in devising measures that can adequately capture the complex dynamics of collaborative work. This study explored the relationship between a popularly used outcome measure in the business process modeling literature and a process measure approximating the level of joint visual attention present among the participants based on the degree of gaze cross-recurrence among the team members over a shared task space. The results suggest that joint visual attention as operationalized in terms of gaze cross-recurrence was a strong predictor of the syntactic, semantic, and pragmatic qualities of collaboratively produced business process models. Moreover, the collaboration process was subjected to qualitative analysis to probe further into the interactional organization of the modeling activity, which identified communication, coordination, awareness, group decision making, and motivation dimensions as key factors contributing to the quality of collaboration among group members. The results indicated strong relationships between the distribution of quality factors and the degree of gaze cross-recurrence and the final models' syntactic and semantic quality scores. Given the increasing availability of affordable eye trackers and the low resolution, practical nature of the employed analysis methodology, the proposed approach can be fruitfully employed to evaluate team performance and test the effectiveness of software interfaces designed to support collaborative work.</p>", "Keywords": "Computer-supported collaborative business process modeling; Joint visual attention; Business process model quality; Dual eye tracking; Gaze cross-recurrence", "DOI": "10.1007/s10270-022-00974-6", "PubYear": 2022, "Volume": "21", "Issue": "6", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "Duygu Fındık-Coşkunçay", "Affiliation": "Faculty of Economics and Administrative Sciences, Ataturk University, Erzurum, Turkey; Informatics Institute, Middle East Technical University, Ankara, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Informatics Institute, Middle East Technical University, Ankara, Turkey; METU-TAF Modeling & Simulation R&D Center, Middle East Technical University, Ankara, Turkey"}], "References": [{"Title": "From analytical purposes to data visualizations: a decision process guided by a conceptual framework and eye tracking", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "3", "Page": "531", "JournalTitle": "Software & Systems Modeling"}, {"Title": "Pedagogical learning supports based on human–systems inclusion applied to rail flow control", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "193", "JournalTitle": "Cognition, Technology & Work"}, {"Title": "Measuring causality between collaborative and individual gaze metrics for collaborative problem‐solving with intelligent tutoring systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "51", "JournalTitle": "Journal of Computer Assisted Learning"}]}, {"ArticleId": 93031287, "Title": "mzMD: visualization-oriented MS data storage and retrieval", "Abstract": "Motivation <p>Drawing peaks in a data window of an MS dataset happens at all time in MS data visualization applications. This asks to retrieve from an MS dataset some selected peaks in a data window whose image in a display window reflects the visual feature of all peaks in the data window. If an algorithm for this purpose is asked to output high-quality solutions in real time, then the most fundamental dependence of it is on the storage format of the MS dataset.</p> Results <p>We present mzMD, a new storage format of MS datasets and an algorithm to query this format of a storage system for a summary (a set of selected representative peaks) of a given data window. We propose a criterion Q-score to examine the quality of data window summaries. Experimental statistics on real MS datasets verified the high speed of mzMD in retrieving high-quality data window summaries. mzMD reported summaries of data windows whose Q-score outperforms those mzTree reported. The query speed of mzMD is the same as that of mzTree whereas its query speed stability is better than that of mzTree.</p> Availability and implementation <p>The source code is freely available at https://github.com/yrm9837/mzMD-java.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btac098", "PubYear": 2022, "Volume": "38", "Issue": "8", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}, {"AuthorId": 2, "Name": "Jingjing Ma", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, City University of Hong Kong, Hong Kong, China;City University of Hong Kong Shenzhen Research Institute, Shenzhen 518057, China"}, {"AuthorId": 6, "Name": "Dam<PERSON>", "Affiliation": "School of Computer Science and Technology, Shandong University, Qingdao 266237, China"}], "References": []}, {"ArticleId": 93031290, "Title": "Reasoning about epistemic social network dynamics using dynamic term-modal logic", "Abstract": "<p>Logics for social networks have been studied in the recent literature. This paper presents a framework based on dynamic term-modal logic ($\\textsf {DTML}$), a quantified variant of dynamic epistemic logic (DEL). In contrast with DEL where it is commonly known to whom agent names refer, $\\textsf {DTML}$ can represent dynamics with uncertainty about agent identity. We exemplify dynamics where such uncertainty and de re/de dicto distinctions are key to social network epistemics. Technically, we show that $\\textsf {DTML}$ semantics can represent a popular class of hybrid logic epistemic social network models. We also show that $\\textsf {DTML}$ can encode previously discussed dynamics for which finding a complete logic was left open. As complete reduction axioms systems exist for $\\textsf {DTML}$, this yields a complete system for the dynamics in question.</p>", "Keywords": "", "DOI": "10.1093/logcom/exac019", "PubYear": 2022, "Volume": "32", "Issue": "6", "JournalId": 2581, "JournalTitle": "Journal of Logic and Computation", "ISSN": "0955-792X", "EISSN": "1465-363X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence and Machine Learning Group, Universitat Pompeu Fabra, Barcelona 08018, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>vi<PERSON>", "Affiliation": "Center for Information and Bubble Studies, University of Copenhagen, Copenhagen 2300S, Denmark"}], "References": [{"Title": "Dynamic term-modal logics for first-order epistemic planning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>r<PERSON> Rendsvig", "PubYear": 2020, "Volume": "286", "Issue": "", "Page": "103305", "JournalTitle": "Artificial Intelligence"}, {"Title": "Modal Logics and Group Polarization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "8", "Page": "2240", "JournalTitle": "Journal of Logic and Computation"}]}, {"ArticleId": 93031327, "Title": "Auction-based approach with improved disjunctive graph model for job shop scheduling problem with parallel batch processing", "Abstract": "The job-shop scheduling problem (JSSP) is encountered in several industries, including the military where heat treatment is applied prior to the machining process in production. This study aims to minimize the overall make-span of a JSSP with parallel batch processing. The problem is formulated as a mixed-integer linear programming model. Feasible solutions are derived from an auction-based approach for forming batches, allocating operation machines, and scheduling. An improved disjunctive graph model is further developed to search for better solutions. We conduct numerical experiments to test a set of benchmark instances. A comparison of the results with those obtained applying other existing algorithms and CPLEX demonstrates the effectiveness and stability of the proposed auction-based approach and improved graph model. Furthermore, a statistical analysis using IBM SPSS shows that the proposed auction-based approach has an absolute advantage in solving medium-scale and large-scale instances of JSSP with batch processing.", "Keywords": "Job shop scheduling ; Parallel batch processing ; Auction-based approach ; Improved disjunctive graph model", "DOI": "10.1016/j.engappai.2022.104735", "PubYear": 2022, "Volume": "110", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management and Decision Sciences, School of Business Administration, Northeastern University, Shenyang 110167, China;Corresponding author"}, {"AuthorId": 2, "Name": "Guiqing Qi", "Affiliation": "Department of Organization and Management, School of Business Administration, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management and Decision Sciences, School of Business Administration, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Management Science & Engineering, Dongbei University of Finance and Economics (DUFE), Dalian, Shahekou, 116025, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management and Decision Sciences, School of Business Administration, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 6, "Name": "Chongjun Yan", "Affiliation": "College of Management Science & Engineering, Dongbei University of Finance and Economics (DUFE), Dalian, Shahekou, 116025, China"}], "References": [{"Title": "Solving a new robust reverse job shop scheduling problem by meta-heuristic algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-Mo<PERSON>ad<PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104207", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An imperialist competitive algorithm with feedback for energy-efficient flexible job shop scheduling with transportation and sequence-dependent setup times", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104307", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Efficient repairs of infeasible job shop problems by evolutionary algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "104368", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "When serial batch scheduling involves parallel batching decisions: A branch and price scheme", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "105514", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 93031384, "Title": "Laser point cloud registration method based on iterative closest point improved by Gaussian mixture model considering corner features", "Abstract": "As a fundamental process of three-dimensional lidar point cloud data (3D LPCD) processing, numerous registration methods are time consuming and easily fall into local optimum. A 3D LPCD registration method based on the iterative closest point (ICP) algorithm, which is improved by the Gaussian mixture model (GMM) considering corner features, is proposed in this article to address these limitations. The GMM method is used for coarse registration, and the input original 3D LPCD is replaced by corner features extracted by the improved 3D Harris algorithm to improve the efficiency of coarse registration. In addition, a satisfactory initial position between the reference and the moving 3D LPCD is prepared for ICP fine registration by coarse registration; thus, the accuracy of fine registration can be improved. The registration accuracy and efficiency of the new method is proved to be higher than those of four common ICP-based registration methods (3DSC-RANSACICP, 3DSC-SAC-IAICP, FPFH-RANSACICP, and FPFH-SAC-IAICP), and GMM registration methods, and the local optimum problem is effectively addressed.", "Keywords": "three-dimensional lidar point cloud registration ; Gaussian mixture model ; three-dimensional Harris ; corner features ; iterative closest point", "DOI": "10.1080/01431161.2021.2022242", "PubYear": 2022, "Volume": "43", "Issue": "3", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Civil and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Civil and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, People’s Republic of China"}, {"AuthorId": 5, "Name": "Jing <PERSON>", "Affiliation": "School of Civil and Surveying & Mapping Engineering, Jiangxi University of Science and Technology, Ganzhou, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Surveying and EngineeringZhejiang Land Surveying and Planning Co. Ltd, Hangzhou, People’s Republic of China"}], "References": [{"Title": "Precise iterative closest point algorithm for RGB-D data registration with noise and outliers", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "399", "Issue": "", "Page": "361", "JournalTitle": "Neurocomputing"}, {"Title": "Deep learning based point cloud registration: an overview", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "222", "JournalTitle": "Virtual Reality & Intelligent Hardware"}]}, {"ArticleId": 93031591, "Title": "Automated Full Scene Parsing for Marine ASVs Using Monocular Vision", "Abstract": "<p>Perceiving and parsing a maritime scene automatically and in real time is a key task for autonomous surface vehicle navigation. We propose a panoptic segmentation framework that allows end-to-end training and multiple task cascading to meet the navigational challenges of scene parsing in a complex maritime environment. In our framework, the feature extraction backbone is based on Res2Net combined with improved FPN. The fusion network neck adds a mask branch to the latest YOLO detector and embeds a bottleneck attention module. We address possible inference conflict between semantic segmentation and instance segmentation with a panoptic fusion head that resolves conflict using Dezert-Smarandache theory. We also constructed the first maritime scene parsing dataset MarPS-1395, which is completely and fully annotated. MarPS-1395 is the first panoptic segmentation dataset in this field. We validated our model on MarPS-1395 as well as the publicly available dataset to investigate the real-time performance and the accuracy of multitask implementation in panoptic segmentation, which included object detection and classification, instance segmentation, and semantic segmentation. The experimental results also show that our method can robustly accomplish full scene parsing in a complex maritime environment, and achieved a good balance between accuracy of segmentation and speed of computing.</p>", "Keywords": "Autonomous surface vessel (ASV); Monocular vision; Maritime scene parsing; Panoptic segmentation Dezert-Smarandache theory (DSmT); Deep learning", "DOI": "10.1007/s10846-021-01543-7", "PubYear": 2022, "Volume": "104", "Issue": "2", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Shanghai Maritime University, Shanghai, China;College of Information Engineering, Jiangsu Maritime Institute, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> Liu", "Affiliation": "College of Information Engineering, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Nanjing Marine Radar Institute, Nanjing, China"}, {"AuthorId": 4, "Name": "Taizhi Lyu", "Affiliation": "College of Information Engineering, Jiangsu Maritime Institute, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Information Engineering, Jiangsu Maritime Institute, Nanjing, China"}], "References": [{"Title": "An integrated ship segmentation method based on discriminator and extractor", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "103824", "JournalTitle": "Image and Vision Computing"}, {"Title": "A Simple and Light-Weight Attention Module for Convolutional Neural Networks", "Authors": "<PERSON><PERSON> Park; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "783", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 93031595, "Title": "Design and analysis of a novel piezoelectric inertial actuator with large stepping displacement amplified by compliant mechanism", "Abstract": "<p>A novel piezoelectric inertial actuator is developed in this paper, to realize the requirements of large stroke and high output displacement accuracy. This proposed actuator which is different from those excellent researches before can not only earn large stepping displacement advantage by elaborately designing a compliant driving foot, but also possess a well load regulation capability through an exquisite adjustment device. The conceptual scheme and operating principle of the actuator are described. And the compliant driving foot is optimized towards for a desired amplification ratio and natural frequency. Subsequently, theoretical model is built and finite element analysis is conducted for verifying the optimization results. Finally, a series of experimental tests are carried out and the results demonstrate the effectiveness of the proposed actuator. In addition, the compliant driving foot and payload adjustment device show a favorable prospect in development of miniaturized and integrated smart material actuator.</p>", "Keywords": "", "DOI": "10.1007/s00542-022-05257-0", "PubYear": 2022, "Volume": "28", "Issue": "4", "JournalId": 809, "JournalTitle": "Microsystem Technologies", "ISSN": "0946-7076", "EISSN": "1432-1858", "Authors": [{"AuthorId": 1, "Name": "Xiaoqing Sun", "Affiliation": "Department of Mechanical Engineering, Donghua University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Donghua University, Shanghai, China"}, {"AuthorId": 3, "Name": "Sicheng Yi", "Affiliation": "Shanghai Aerospace Control Technology Institute, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai Aerospace Control Technology Institute, Shanghai, China;State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, China"}], "References": [{"Title": "Design and experimental performance of an inertial giant magnetostrictive linear actuator", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "301", "Issue": "", "Page": "111771", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Integrated design, fabrication, and experimental study of a parallel micro-nano positioning-vibration isolation stage", "Authors": "Xiaoqing Sun; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "", "Page": "101988", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 93031645, "Title": "RateML: A Code Generation Tool for Brain Network Models", "Abstract": "<p>Whole brain network models are now an established tool in scientific and clinical research, however their use in a larger workflow still adds significant informatics complexity. We propose a tool, RateML, that enables users to generate such models from a succinct declarative description, in which the mathematics of the model are described without specifying how their simulation should be implemented. RateML builds on NeuroML’s Low Entropy Model Specification (LEMS), an XML based language for specifying models of dynamical systems, allowing descriptions of neural mass and discretized neural field models, as implemented by the Virtual Brain (TVB) simulator: the end user describes their model’s mathematics once and generates and runs code for different languages, targeting both CPUs for fast single simulations and GPUs for parallel ensemble simulations. High performance parallel simulations are crucial for tuning many parameters of a model to empirical data such as functional magnetic resonance imaging (fMRI), with reasonable execution times on small or modest hardware resources. Specifically, while RateML can generate Python model code, it enables generation of Compute Unified Device Architecture C++ code for NVIDIA GPUs. When a CUDA implementation of a model is generated, a tailored model driver class is produced, enabling the user to tweak the driver by hand and perform the parameter sweep. The model and driver can be executed on any compute capable NVIDIA GPU with a high degree of parallelization, either locally or in a compute cluster environment. The results reported in this manuscript show that with the CUDA code generated by RateML, it is possible to explore thousands of parameter combinations with a single Graphics Processing Unit for different models, substantially reducing parameter exploration times and resource usage for the brain network models, in turn accelerating the research workflow itself. This provides a new tool to create efficient and broader parameter fitting workflows, support studies on larger cohorts, and derive more robust and statistically relevant conclusions about brain dynamics.</p>", "Keywords": "Brain Network Models; Domain Specific Language (DSL); Automatic code generation; High performance computing; simulation", "DOI": "10.3389/fnetp.2022.826345", "PubYear": 2022, "Volume": "2", "Issue": "", "JournalId": 89151, "JournalTitle": "Frontiers in Network Physiology", "ISSN": "", "EISSN": "2674-0109", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Simulation and Data Lab Neuroscience, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institut de Neurosciences des Systèmes, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institut de Neurosciences des Systèmes, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Simulation and Data Lab Neuroscience, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Simulation and Data Lab Neuroscience, Germany"}, {"AuthorId": 6, "Name": "<PERSON> ", "Affiliation": "Institut de Neurosciences des Systèmes, France"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Simulation and Data Lab Neuroscience, Germany;Institute of Neuroscience and Medicine (INM-6) and Institute for Advanced Simulation (IAS-6) and JARA-Institute Brain, Germany;Computer Science 3–Software Engineering, Germany"}], "References": []}, {"ArticleId": 93031746, "Title": "Learning in Repeated Auctions", "Abstract": "Online auctions are one of the most fundamental facets of the modern economy and power an industry generating hundreds of billions of dollars a year in revenue. Auction theory has historically focused on the question of designing the best way to sell a single item to potential buyers, with the concurrent objectives of maximizing revenue generated or welfare created. Theoretical results in this area have typically relied on some prior Bayesian knowledge agents were assumed to have on each other. This assumption is no longer satisfied in new markets such as online advertising: similar items are sold repeatedly, and agents are unaware of each other or might try to manipulate each other. On the other hand, statistical learning theory now provides tools to supplement those missing pieces of information given enough data, as agents can learn from their environment to improve their strategies. This monograph covers recent advances in learning in repeated auctions, starting from the traditional economic study of optimal one-shot auctions with a Bayesian prior. We then focus on the question of learning optimal mechanisms from a dataset of bidders' past values. The sample complexity as well as the computational efficiency of different methods will be studied. We will also investigate online variants where gathering data has a cost to be accounted for, either by sellers or buyers (“earning while learning”). Later in the monograph, we will further assume that bidders are also adaptive to the mechanism as they interact repeatedly with the same seller. We will show how strategic agents can actually manipulate repeated auctions, to their own advantage. A particularly interesting example is that of reserve price improvements for strategic buyers in second price auctions. All the questions discussed in this monograph are grounded in real-world applications and many of the ideas and algorithms we describe are used every day to power the Internet economy. © 2022 T. Nedelec et al.", "Keywords": "Machine Learning; Theoretical Computer Science; Statistical Learning Theory; Game theoretic learning; Algorithmic game theory; Design and Analysis of Algorithms; Operations Research", "DOI": "10.1561/**********", "PubYear": 2022, "Volume": "15", "Issue": "3", "JournalId": 26667, "JournalTitle": "Foundations and Trends® in Machine Learning", "ISSN": "1935-8237", "EISSN": "1935-8245", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ENS Paris Saclay and Criteo AI Lab, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Criteo AI Lab, France"}, {"AuthorId": 3, "Name": "Noureddine El Karoui", "Affiliation": "Work done while at UC, Berkeley, USA, and Criteo AI Lab, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Criteo AI Lab, France"}], "References": []}, {"ArticleId": 93031834, "Title": "Multiaperture multibeam antenna design method for high‐throughput satellite applications", "Abstract": "<p>As the core technology of high-throughput satellite payloads, multibeam antennas have received high attention and rapid development in recent years. Because of the characteristics of high gain and high beam isolation, multiaperture multibeam antennas is an important technical approach to meet the high-performance requirements of high-throughput satellites. This article proposes a mathematical modeling method based on the mutual coupling parameters of the reflector. This method takes capacity requirements and satellite structure design boundary conditions as input condition, and adopts an adaptive evolutionary multiobjective optimization algorithm to perform joint optimization and solution of multiple parameters, which can complete the optimization efficiently. This method does not need to perform a large number of antenna parameter simulation, reduces the number of iterations and structural layout design, greatly reduces the design cost. This article takes the demand of the AP-6D satellite as an example, applies the above method to the multibeam antenna design of the satellite, and completes the simulation verification, structural design, physical manufacturing, and various tests of the antenna to verify the correctness of the design method.</p>", "Keywords": "high-throughput satellite;multiaperture multibeam antenna;multiobjective optimization;reflector mutual coupling parameters", "DOI": "10.1002/mmce.23116", "PubYear": 2022, "Volume": "32", "Issue": "6", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Antenna Academy, China Academy of Space Technology (Xi'an), Xi'an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Antenna Academy, China Academy of Space Technology (Xi'an), Xi'an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Antenna Academy, China Academy of Space Technology (Xi'an), Xi'an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Antenna Academy, China Academy of Space Technology (Xi'an), Xi'an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Antenna Academy, China Academy of Space Technology (Xi'an), Xi'an, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Communications and Navigation Satellite Division, China Academy of Space Technology, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Communications and Navigation Satellite Division, China Academy of Space Technology, Beijing, China"}], "References": []}, {"ArticleId": 93031852, "Title": "Automatic generation of textual descriptions in data-to-text systems using a fuzzy temporal ontology: Application in air quality index data series", "Abstract": "In this paper we present a model based on computational intelligence and natural language generation for the automatic generation of textual summaries from numerical data series, aiming to provide insights which help users to understand the relevant information hidden in the data. Our model includes a fuzzy temporal ontology with temporal references which addresses the problem of managing imprecise temporal knowledge, which is relevant in data series. We fully describe a real use case of application in the environmental information systems field, providing linguistic descriptions about the air quality index (AQI), which is a very well-known indicator provided by all meteorological agencies worldwide. We consider two different data sources of real AQI data provided by the official Galician (NW Spain) Meteorology Agency: (i) AQI distribution in the stations of the meteorological observation network and (ii) time series which describe the state and evolution of the AQI in each meteorological station. Both application models were evaluated following the current standards and good practices of manual human expert evaluation of the Natural Language Generation field. Assessment results by two experts meteorologists were very satisfactory, which empirically confirm that the proposed textual descriptions fit this type of data and service both in content and layout.", "Keywords": "Fuzzy linguistic terms ; Linguistic descriptions of data ; Data to text systems ; Natural language generation", "DOI": "10.1016/j.asoc.2022.108612", "PubYear": 2022, "Volume": "119", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, 15782, Santiago de Compostela, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, 15782, Santiago de Compostela, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, 15782, Santiago de Compostela, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "MeteoGalicia, Xunta de Galicia, Santiago de Compostela, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, 15782, Santiago de Compostela, Spain"}], "References": [{"Title": "A possibilistic approach for interval type-2 fuzzy linguistic summarization of time series", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "5", "Page": "3991", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 93031897, "Title": "Simulation and Evaluation of Cloud Storage Caching for Data Intensive Science", "Abstract": "A common task in scientific computing is the data reduction. This workflow extracts the most important information from large input data and stores it in smaller derived data objects. The derived data objects can then be used for further analysis. Typically, these workflows use distributed storage and computing resources. A straightforward setup of storage media would be low-cost tape storage and higher-cost disk storage. The large, infrequently accessed input data are stored on tape storage. The smaller, frequently accessed derived data is stored on disk storage. In a best-case scenario, the large input data is only accessed very infrequently and in a well-planned pattern. However, practice shows that often the data has to be processed continuously and unpredictably. This can significantly reduce tape storage performance. A common approach to counter this is storing copies of the large input data on disk storage. This contribution evaluates an approach that uses cloud storage resources to serve as a flexible cache or buffer, depending on the computational workflow. The proposed model is explored for the case of continuously processed data. For the evaluation, a simulation tool was developed, which can be used to analyse models related to storage and network resources. We show that using commercial cloud storage can reduce on-premises disk storage requirements, while maintaining an equal throughput of jobs. Moreover, the key metrics of the model are discussed, and an approach is described, which uses the simulation to assist with the decision process of using commercial cloud storage. The goal is to investigate approaches and propose new evaluation methods to overcome future data challenges.", "Keywords": "Cloud storage; Transfer simulation; Quality-of-service storage", "DOI": "10.1007/s41781-021-00076-w", "PubYear": 2022, "Volume": "6", "Issue": "1", "JournalId": 52838, "JournalTitle": "Computing and Software for Big Science", "ISSN": "2510-2036", "EISSN": "2510-2044", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Wuppertal, Wuppertal, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "European Organization for Nuclear Research (CERN), Meyrin, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Hochschule Niederrhein, Krefeld, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Wuppertal, Wuppertal, Germany"}], "References": []}, {"ArticleId": 93032040, "Title": "Efficient 3D CNNs with knowledge transfer for sign language recognition", "Abstract": "<p>Heavy 3D CNNs for spatiotemporal modeling have gained impressive performance in sign language recognition (SLR). However, as for memory/computation cost, heavy 3D CNNs are expensive and unsuitable for real-time applications. In this paper, we seek for efficient spatiotemporal modeling with respect to model size and speed for both isolated and continuous SLR. Specifically, we first build several efficient 3D CNNs including 3D-MobileNets, 3D-ShuffleNets, and X3Ds. Then, we further boost the performance by designing a random knowledge distillation strategy (RKD) which concurrently considers the temperature of the distillation process, the ratio of true labels to soft labels, and multi-teacher networks to transfer the knowledge from larger teacher models for isolated SLR. We finally apply these lightweight models as spatiotemporal feature extractors in the framework of attention-based sequence-to-sequence for the more challenging continuous SLR. In our experiments, the best 16-frame MobileNetv2-1.0-S obtains 95.12% test accuracy on the isolated CSL-500 dataset, and the efficient sequence-to-sequence framework obtains 2.2 Word Error Rate (WER) on the CSL-continuous dataset. The experimental results achieve competitive performance across all datasets while being 10s to 100s faster than the state-of-the-art methods.</p>", "Keywords": "Sign language recognition; Efficient 3D CNNs; Knowledge transfer; Sequence-to-sequence", "DOI": "10.1007/s11042-022-12051-7", "PubYear": 2022, "Volume": "81", "Issue": "7", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Xiangzu Han", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Control Science and Engineering, Shandong University, Jinan, China"}], "References": [{"Title": "Towards multi-modal causability with Graph Neural Networks enabling information fusion for explainable AI", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "28", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 93032055, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0305-0548(22)00051-X", "PubYear": 2022, "Volume": "141", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [], "References": []}, {"ArticleId": 93032064, "Title": "Application of canny operator threshold adaptive segmentation algorithm combined with digital image processing in tunnel face crevice extraction", "Abstract": "<p>The present work aims to reduce tunnel construction accidents to personnel. The threshold adaptive segmentation algorithm combined with the <PERSON><PERSON> operator is employed to extract and detect the cracks on the rock mass of the tunnel face from digital images of the tunnel face. Firstly, the gray change processing and histogram equalization technology of the image processing algorithm enhance the contrast of the digital image of rock mass on the tunnel face. Then, the Canny operator and OTSU method construct a threshold adaptive segmentation algorithm to segment the rock mass crevice image after increasing the contrast and to classify crevices on the tunnel face into streak cracks and irregular cracks. Secondly, the segmented image is corrupted, extended, and refined; meanwhile, boundary fitting, separation, merging, and filtering are carried out to form a relatively complete rock boundary recognition result. Finally, the streak crevices and irregular crevices are detected according to the crevice geometry and pixel distribution characteristics to determine the crack direction. The experimental results show that this method can extract complete rock cracks with less than a 2% extraction error rate. Besides, the detection rates of the algorithm for the streak crevices and irregular crevices are 97% and 94%, respectively, and the detection accuracy of the crevice direction is 98%. This indicates that the algorithm proposed here is applicable to geological sketch and provides a reference for the classification of surrounding rock on the tunnel face.</p>", "Keywords": "Canny operator; Threshold adaptive segmentation; Digital image processing; Crevice extraction", "DOI": "10.1007/s11227-022-04330-9", "PubYear": 2022, "Volume": "78", "Issue": "9", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "Feng <PERSON>", "Affiliation": "Department of Civil Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 4, "Name": "Cheng<PERSON> Zheng", "Affiliation": "Department of Civil Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Shandong University of Science and Technology, Qingdao, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Shandong University of Science and Technology, Qingdao, China"}], "References": [{"Title": "Color image segmentation using Kapur, Otsu and Minimum Cross Entropy functions based on Exchange Market Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "172", "Issue": "", "Page": "114636", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 93032359, "Title": "Perspectives of cybersecurity for ameliorative Industry 4.0 era: a review-based framework", "Abstract": "Purpose \nIndustry 4.0 refers to the interconnection of cyber-physical systems, which connects the physical and digital worlds by collecting digital data from physical objects/processes, and using this data to drive automation and optimisation. Digital technologies used in this revolution gather and handle massive volumes of high-velocity streams while automating field operations and supply chain activities. Cybersecurity is a complicated process that helps sort out various hacking issues of Industry 4.0. This purpose of this paper is to provide an overview on cybersecurity and its major applications for Industry 4.0.\n \n \n Design/methodology/approach \nThe rise of Industry 4.0 technologies is changing how machines and associated information are obtained to evaluate the data contained within them. This paper undertakes a comprehensive literature-based study. Here, relevant research papers related to cybersecurity for Industry 4.0 are identified and discussed. Cybersecurity results in high-end products, with faster and better goods manufactured at a lesser cost.\n \n \n Findings \nArtificial intelligence, cloud computing, internet of things, robots and cybersecurity are being introduced to improve the Industry 4.0 environment. In the starting, this paper provides an overview of cybersecurity and its advantages. Then, this study discusses technologies used to enhance the cybersecurity process. Enablers, progressive features and steps for creating a cybersecurity culture for Industry 4.0 are discussed briefly. Also, the research identified the major cybersecurity applications for Industry 4.0 and discussed them. Cybersecurity is vital for better data protection in many businesses and industrial control systems. Manufacturing is getting more digitised as the sector embraces automation to a more significant level than ever before.\n \n \n Originality/value \nThis paper states about Industry 4.0 and the safety of multiple business process systems through cybersecurity. A significant issue for Industry 4.0 devices, platforms and frameworks is undertaken by cybersecurity. Digital transformation in the Industry 4.0 era will increase industrial competitiveness and improve their capacity to make optimum decisions. Thus, this study would give an overview of the role of cybersecurity in the effective implementation of Industry 4.0.", "Keywords": "Applications;Security;Cybersecurity;Industry 4.0;Cyber threats", "DOI": "10.1108/IR-10-2021-0243", "PubYear": 2022, "Volume": "49", "Issue": "3", "JournalId": 4481, "JournalTitle": "Industrial Robot: An International Journal", "ISSN": "0143-991X", "EISSN": "1758-5791", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Jamia Millia Islamia , New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Jamia Millia Islamia , New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Production Engineering, Dr <PERSON> <PERSON> National Institute of Technology , Jalandhar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Jamia Millia Islamia , New Delhi, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Production Engineering, G.B. Pant University of Agriculture and Technology , Pantnagar, India"}], "References": [{"Title": "Cybersecurity in the context of industry 4.0: A structured classification of critical assets and business impacts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "114", "Issue": "", "Page": "103165", "JournalTitle": "Computers in Industry"}, {"Title": "Anomaly detection via blockchained deep learning smart contracts in industry 4.0", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "23", "Page": "17361", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Industry 4.0 in the port and maritime industry: A literature review", "Authors": "<PERSON>zuelo; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "", "Page": "100173", "JournalTitle": "Journal of Industrial Information Integration"}, {"Title": "Securing IIoT using Defence-in-Depth: Towards an End-to-End secure Industry 4.0", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "367", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Managing the barriers of Industry 4.0 adoption and implementation in textile and clothing industry: Interpretive structural model and triple helix framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "103372", "JournalTitle": "Computers in Industry"}, {"Title": "Artificial intelligence, cyber-threats and Industry 4.0: challenges and opportunities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "5", "Page": "3849", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Cyber Security and Privacy Issues in Industrial Internet of Things", "Authors": "NZ Jhanjhi; <PERSON><PERSON><PERSON>; Saleh N. <PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "361", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 93032385, "Title": "Corrigendum to: A Multi-Objective Randomly Updated Beetle Swarm and Multi-Verse Optimization for Brain Tumor Segmentation and Classification", "Abstract": "", "Keywords": "", "DOI": "10.1093/comjnl/bxac018", "PubYear": 2023, "Volume": "66", "Issue": "6", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [], "References": []}]