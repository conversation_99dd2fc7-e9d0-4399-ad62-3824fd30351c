[{"ArticleId": 106517101, "Title": "Multi-Attribute Decision-Making Method Based on Complex Interval-Valued q-Rung Orthopair Linguistic Heronian Mean Operators and Their Application", "Abstract": "<p>In this manuscript, we firstly proposed a novel concept of complex interval-valued q-rung orthopair linguistic (CIVq-ROL) information by integrating the concepts of complex interval-valued q-rung orthopair fuzzy (CIVq-ROF) information and linguistic set (LS), and it is more generalized than most existing sets, and it is very feasible and valuable for depicting awkward and unreliable information in a difficult situation. Further, we developed some new operational laws of the CIVq-ROF information based on algebraic t-norm and t-conorm. It is also clear that it is a very challenging task to fuse the collection of data into a singleton set in information fusion and decision making, and the Heronian mean (HM) operator is an important aggregation operator, which can not only achieve the aggregation function from the collection of data into a singleton one, but also consider the relationship between any two data. Therefore, based on the CIVq-ROL information and HM operator, we proposed the CIVq-ROL Heronian mean (CIVq-ROLHM), CIVq-ROL weighted HM (CIVq-ROLWHM), CIVq-ROL geometric HM (CIVq-ROLGHM), and CIVq-ROL weighted geometric HM (CIVq-ROLWGHM) operators, and then, the various desirable properties and specific cases of them are also investigated. Furthermore, we developed a multi-attribute decision-making (MADM) procedure for evaluating the finest business in the country from the collection of four different types of electronics-related businesses based on the proposed operators. Finally, various examples are given to show the application of the invented techniques, and a comparative analysis is carried out for the parameters to show the advantages of the proposed approaches.</p>", "Keywords": "Complex interval-valued q-rung orthopair linguistic sets; Geometric/averaging Heronian mean operators; Decision-making strategy", "DOI": "10.1007/s40815-022-01455-0", "PubYear": 2023, "Volume": "25", "Issue": "4", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Qi", "Affiliation": "School of International Business, Qingdao Huanghai University, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics & Statistics, International Islamic University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics & Statistics, International Islamic University Islamabad, Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of International Business, Qingdao Huanghai University, Qingdao, China"}], "References": [{"Title": "On some distance measures of complex Pythagorean fuzzy sets and their applications in pattern recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "15", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "New generalised Bonferroni mean aggregation operators of complex intuitionistic fuzzy information based on Archimedean t-norm and t-conorm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "1", "Page": "81", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Group decision-making framework using complex Pythagorean fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "2085", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Algorithms for complex interval‐valued q‐rung orthopair fuzzy sets in decision making based on aggregation operators,\n AHP,\n and\n TOPSIS", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "1", "Page": "", "JournalTitle": "Expert Systems"}, {"Title": "Multi-attribute decision making with Pythagorean fuzzy sets via conversions to intuitionistic fuzzy sets and ORESTE method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "3", "Page": "372", "JournalTitle": "Journal of Control and Decision"}, {"Title": "Integration of fuzzy-weighted zero-inconsistency and fuzzy decision by opinion score methods under a q-rung orthopair environment: A distribution case study of COVID-19 vaccine doses", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "103572", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "A three-way decision approach with probabilistic dominance relations under intuitionistic fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "582", "Issue": "", "Page": "114", "JournalTitle": "Information Sciences"}, {"Title": "Complex Pythagorean fuzzy einstein aggregation operators in selecting the best breed of Horsegram", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115990", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A ranking method based on Muirhead mean operator for group decision making with complex interval-valued q-rung orthopair fuzzy numbers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "22", "Page": "14001", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 106517137, "Title": "After the Complaint", "Abstract": "", "Keywords": "", "DOI": "10.1145/3581640", "PubYear": 2023, "Volume": "66", "Issue": "3", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "How COVID-19 Changed Self-Presentation on Instagram and its Relation to User Well-Being", "Abstract": "<p>Social media use increased during the COVID-19 pandemic with platforms providing an important forum for communication and self-expression. In this study, we explore shifts in online posting behaviors and self-presentation following the onset of lockdown. Content analysis of active Instagram accounts (n = 73) was conducted for the 3-month period before and immediately following the start of lockdown in the UK, and compared to psychological well-being, social media dependency and motives for online self-presentation during lockdown. Changes in the nature of images and captions used by profile owners were found following the start of lockdown, with more selfies and throwback photographs of past events being posted. Images in contexts depicting users as ‘social’, and positive or explanatory message captioning decreased during lockdown. Limited evidence was found to support the hypothesis that images posted were predictive of psychological well-being in lockdown. More followers and the degree to which online portrayals represented ‘real-self’ appeared more critical to well-being during lockdown.</p>", "Keywords": "", "DOI": "10.1093/iwc/iwad013", "PubYear": 2023, "Volume": "35", "Issue": "5", "JournalId": 5476, "JournalTitle": "Interacting with Computers", "ISSN": "0953-5438", "EISSN": "1873-7951", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Portsmouth Department of Psychology King Henry Building, , Portsmouth, PO1 2DY, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Portsmouth Department of Psychology King Henry Building, , Portsmouth, PO1 2DY, United Kingdom"}], "References": [{"Title": "Do you filter who you are?: Excessive self-presentation, social cues, and user evaluations of Instagram selfies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "106159", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Social media use and well-being: A prospective experience-sampling study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106510", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The relationship between burden caused by coronavirus (Covid-19), addictive social media use, sense of control and anxiety", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "106720", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The dynamics of social support and affective well-being before and during COVID: An experience sampling study", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "121", "Issue": "", "Page": "106776", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Passive social media use and psychological well-being during the COVID-19 pandemic: The role of social comparison and emotion regulation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "107050", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 106517299, "Title": "A Legal Challenge to Algorithmic Recommendations", "Abstract": "<p>Reconsidering liability shield considerations.</p>", "Keywords": "", "DOI": "10.1145/3581763", "PubYear": 2023, "Volume": "66", "Issue": "3", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of California, Berkeley, CA"}], "References": []}, {"ArticleId": *********, "Title": "Mapping the Privacy Landscape for Central Bank Digital Currencies", "Abstract": "<p>Now is the time to shape what future payment flows will reveal about you.</p>", "Keywords": "", "DOI": "10.1145/3579316", "PubYear": 2023, "Volume": "66", "Issue": "3", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "BIS Innovation Hub"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Innsbruck, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Information Systems Engineering in Montreal, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Quebec in Montreal, Canada"}], "References": []}, {"ArticleId": *********, "Title": "Split Your Overwhelmed Teams", "Abstract": "<p>Two teams of five is not the same as one team of 10.</p>", "Keywords": "", "DOI": "10.1145/3579315", "PubYear": 2023, "Volume": "66", "Issue": "3", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Stack Overflow, Inc. in New York City, NY"}], "References": []}, {"ArticleId": 106517328, "Title": "Machine Learning for Data Center Optimizations: Feature Selection Using Shapley Additive exPlanation (SHAP)", "Abstract": "<p>The need for artificial intelligence (AI) and machine learning (ML) models to optimize data center (DC) operations increases as the volume of operations management data upsurges tremendously. These strategies can assist operators in better understanding their DC operations and help them make informed decisions upfront to maintain service reliability and availability. The strategies include developing models that optimize energy efficiency, identifying inefficient resource utilization and scheduling policies, and predicting outages. In addition to model hyperparameter tuning, feature subset selection (FSS) is critical for identifying relevant features for effectively modeling DC operations to provide insight into the data, optimize model performance, and reduce computational expenses. Hence, this paper introduces the Shapley Additive exPlanation (SHAP) values method, a class of additive feature attribution values for identifying relevant features that is rarely discussed in the literature. We compared its effectiveness with several commonly used, importance-based feature selection methods. The methods were tested on real DC operations data streams obtained from the ENEA CRESCO6 cluster with 20,832 cores. To demonstrate the effectiveness of SHAP compared to other methods, we selected the top ten most important features from each method, retrained the predictive models, and evaluated their performance using the MAE, RMSE, and MPAE evaluation criteria. The results presented in this paper demonstrate that the predictive models trained using features selected with the SHAP-assisted method performed well, with a lower error and a reasonable execution time compared to other methods.</p>", "Keywords": "", "DOI": "10.3390/fi15030088", "PubYear": 2023, "Volume": "15", "Issue": "3", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University College of Dublin, D04 V1W8 Dublin, Ireland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University College of Dublin, D04 V1W8 Dublin, Ireland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Wolaita Sodo University, Wolaita P.O. Box 138, Ethiopia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "ENEA-R.C. <PERSON>, 80055 Portici (NA), Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "ENEA-<PERSON><PERSON><PERSON><PERSON>, 00196 Rome, Italy; Corresponding author"}], "References": []}, {"ArticleId": 106517329, "Title": "Optimal investment strategy for the DC pension plan based on jump diffusion model and S-shaped utility", "Abstract": "In this paper, we study the optimal investment strategy for a defined contribution pension plan under the jump diffusion model and S-shaped utility. We assume that pension plan members can invest their pension fund in the financial market consisting of a risk-free asset and a risk asset whose price process follows a jump diffusion process. The goal of pension plan managers is to maximize the expected utility of the real terminal wealth under the jump diffusion model and S-shaped utility. We apply Lagrange dual method, concavification technique and martingale method to derive the closed-form expressions of the optimal wealth process and the optimal investment strategy. Finally, we also try to use some numerical analysis to explain the impacts of model parameters on the optimal terminal wealth value and trading strategy.", "Keywords": "Defined contribution pension plan; jump diffusion model; S-shaped utility; dual control; concavification; martingale method", "DOI": "10.3934/mfc.2023007", "PubYear": 2024, "Volume": "7", "Issue": "3", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Statistics and Data Science, Qufu Normal University, Qufu 273165, China"}], "References": []}, {"ArticleId": 106517334, "Title": "Adding Smarts to Vending Machines Drives Convenience, Efficiency", "Abstract": "<p>Advanced feature sets and functionality are projected to drive the market for connected vending to nearly nine million units by 2024.</p>", "Keywords": "", "DOI": "10.1145/3579651", "PubYear": 2023, "Volume": "66", "Issue": "3", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "4K Research & Consulting, LLC"}], "References": []}, {"ArticleId": 106517341, "Title": "Performing Wash Trading on NFTs: Is the Game Worth the Candle?", "Abstract": "<p>Wash trading is considered a highly inopportune and illegal behavior in regulated markets. Instead, it is practiced in unregulated markets, such as cryptocurrency or NFT (Non-Fungible Tokens) markets. Regarding the latter, in the past many researchers have been interested in this phenomenon from an “ex-ante” perspective, aiming to identify and classify wash trading activities before or at the exact time they happen. In this paper, we want to investigate the phenomenon of wash trading in the NFT market from a completely different perspective, namely “ex-post”. Our ultimate goal is to analyze wash trading activities in the past to understand whether the game is worth the candle, i.e., whether these illicit activities actually lead to a significant profit for their perpetrators. To the best of our knowledge, this is the first paper in the literature that attempts to answer this question in a “structured” way. The efforts to answer this question have enabled us to make some additional contributions to the literature in this research area. They are: (i) a framework to support future “ex-post” analyses of the NFT wash trading phenomenon; (ii) a new dataset on wash trading transactions involving NFTs that can support further future investigations of this phenomenon; (iii) a set of insights of the NFT wash trading phenomenon extracted at the end of an experimental campaign.</p>", "Keywords": "", "DOI": "10.3390/bdcc7010038", "PubYear": 2023, "Volume": "7", "Issue": "1", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering (DII), Polytechnic University of Marche, 60121 Ancona, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering (DII), Polytechnic University of Marche, 60121 Ancona, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering (DII), Polytechnic University of Marche, 60121 Ancona, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering (DII), Polytechnic University of Marche, 60121 Ancona, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Iconsulting, 40033 Casalecchio di Reno, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Iconsulting, 40033 Casalecchio di Reno, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering (DII), Polytechnic University of Marche, 60121 Ancona, Italy; Corresponding author"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering (DII), Polytechnic University of Marche, 60121 Ancona, Italy"}], "References": [{"Title": "Use of optimal control in studying the dynamical behaviors of fractional financial awareness models", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON>. <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "7", "Page": "3401", "JournalTitle": "Soft Computing"}, {"Title": "Market manipulation detection: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "118330", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106517342, "Title": "“Easy” meta-embedding for detecting and correcting semantic errors in Arabic documents", "Abstract": "<p>Word-Embedding models have enabled massive advances in natural language understanding tasks and achieved state-of-the-art performances in multiple natural language processing tasks. In this paper, we present an original method based on an “easy” meta-embedding to automatically detect and correct Arabic real-words errors that are semantically inconsistent with the context of the sentence. Due to the lexical proximity of words in Arabic, the risk of having this type of errors in documents is relatively high compared to other languages. Our method uses three word embedding techniques and their combination, namely SkipGram, FastText and BERT for both detection and correction. It checks the semantic affinity of words with the immediate context in a collocation and the near context of the sentence. Experiments have shown that the proposed meta-embedding improves the overall performance of our system.</p>", "Keywords": "Detection-correction; Real-word error; Semantic inconsistency; Meta-embedding; Collocation; SkipGram; FastText; BERT", "DOI": "10.1007/s11042-023-14553-4", "PubYear": 2023, "Volume": "82", "Issue": "14", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ENSI, Manouba University, La Manouba, Tunisia"}], "References": []}, {"ArticleId": 106517369, "Title": "Observer-based fault tolerant control design for periodic piecewise time-varying systems: a fault estimation approach", "Abstract": "This study tackles the fault estimation issue for a continuous-time periodic piecewise time-varying systems (PPTVSs) with delays, fault signal and external disturbances. In particular, the PPTVSs are conjured up by segmenting the fundamental period of the periodic system into a finite number of subintervals. Moreover, fault estimator is designed in line with periodic piecewise observer systems to estimate the immeasurable states of the PPTVSs and the fault signal, simultaneously. Subsequently, in accordance with the estimated values of the fault signal and state dynamics, a fault-tolerant controller is devised. Moreover, by utilising Lyapunov stability theory and matrix polynomial lemma, sufficient criteria are procured in context of linear matrix inequalities to affirm the uniform boundedness of the undertaken system and error system. Further, relying on the acquired constraints, the precise configuration of the fault-tolerant control and observer gain matrices are proffered. Eventually, by offering two illustrative examples including mass-spring-damper system, the utility and potential of presented theoretical insights are validated.", "Keywords": "Periodic piecewise time-varying systems ; external disturbances ; time-varying delay ; adaptive fault estimation ; fault-tolerant control", "DOI": "10.1080/00207721.2023.2180336", "PubYear": 2023, "Volume": "54", "Issue": "7", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Bharathiar University, Coimbatore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, Bharathiar University, Coimbatore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, PSGR Krishnammal College for Women, Coimbatore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, PSGR Krishnammal College for Women, Coimbatore, India"}], "References": [{"Title": "Non-fragile H∞ control of periodic piecewise time-varying systems based on matrix polynomial approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "4", "Page": "805", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Event-triggered observer-based H∞ control of switched linear systems with time-varying delay", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "8", "Page": "1618", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Observer-based output reachable set synthesis for periodic piecewise time-varying systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "571", "Issue": "", "Page": "246", "JournalTitle": "Information Sciences"}, {"Title": "PD control of positive interval continuous-time systems with time-varying delay", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "371", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 106517377, "Title": "Metacognition-driven user-to-project recommendation for online education services", "Abstract": "<p>Various online learning platforms have accumulated a large number of users who are learning or have completed their studies. In which, online education mostly provides services for theoretical learning. A vital issue is how to bridge theoretical learning and practical projects for improving users’ competence. To address this issue, we propose a novel M etacognition-driven U ser-to- P roject recommendation approach for online E ducation S ervices (MUP-ES). We closely model theoretical content and practical project by constructing H eterogeneous I nformation N etworks (HINs) covering both user learning outcomes (learned courses, acquired knowledge concepts, etc.) and project knowledge requirements. We design an attention-based metacognitive information aggregation process to efficiently refine the structural and semantic information of HINs and match users to appropriate projects. To embed HINs, we design a meta-path based random walk strategy to generate meaningful node sequences. MUP-ES provides two major components, path filtering and information aggregation. The path filtering module filters the impurity information in the path through the attention mechanism, and enhances the weight of user learning information that is more suitable for the current project. The information aggregation module learns the neighborhood information of the nodes in the path through the multi-head self-attention mechanism, which aggregates the rich user-related information to the project nodes. Finally, MUP-ES predicts the project’s match score with real users and tests the model’s ability to mitigate the cold-start problem. Extensive experiments on two real-world education datasets show that MUP-ES achieves more accurate prediction results than state-of-the-art baselines.</p>", "Keywords": "Online education services; Recommender systems; Heterogeneous information networks; Multi-head self-attention", "DOI": "10.1007/s11280-023-01139-1", "PubYear": 2023, "Volume": "26", "Issue": "5", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Automatic Detecting Technology and Instruments, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Guangxi Key Laboratory of Automatic Detecting Technology and Instruments, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology, Guilin, China"}], "References": [{"Title": "Extracting, Mining and Predicting Users’ Interests from Social Media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "5", "Page": "445", "JournalTitle": "Foundations and Trends® in Information Retrieval"}]}, {"ArticleId": 106517427, "Title": "Local feature graph neural network for few-shot learning", "Abstract": "<p>The few-shot learning method based on local feature attention can suppress the irrelevant distraction in the global information and extract discriminating features. However, empirically defining the relationship between local features cannot fully utilize the power of local feature attention. This paper proposes a local feature graph neural network model (LFGNN), which uses the GNN to automatically extract and aggregate the relationship between different local parts and obtain features with stronger expressive ability for classification. Specifically, a sparse hierarchical connectivity graph is proposed to describe the relationship between features, in which the global features of all samples in the support set and the query set are connected in pairs, and the local features of each sample are only connected to the corresponding global features. Further, a multiple node-edge aggregation strategy is developed to learn a similarity metric. By integrating the edge loss with the classification loss, our LFGNN learns a better classifier to distinguish samples of novel classes. We conducted extensive experiments under the 5-way 1-shot and 5-way 5-shot setting on two benchmark datasets: miniImageNet, tieredImageNet. Experimental results demonstrate that the proposed approach is effective for boosting performance of meta-learning few-shot classification.</p>", "Keywords": "Few-shot learning; Local feature; Graph neural network", "DOI": "10.1007/s12652-023-04545-5", "PubYear": 2023, "Volume": "14", "Issue": "4", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Zhongshan Institute, University of Electronic Science and Technology China, Zhongshan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Zhongshan Institute, University of Electronic Science and Technology China, Zhongshan, China"}, {"AuthorId": 3, "Name": "Li Ren", "Affiliation": "Computer Science and Engineering, University of Electronic Science and Technology China, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Zhongshan Institute, University of Electronic Science and Technology China, Zhongshan, China"}], "References": [{"Title": "Self-weighted Robust LDA for Multiclass Classification with Edge Classes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Frog-GNN: Multi-perspective aggregation based graph neural network for few-shot text classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "176", "Issue": "", "Page": "114795", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep Discrete Cross-<PERSON><PERSON> with Multiple Supervision", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "486", "Issue": "", "Page": "215", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-local feature relation network for few-shot learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "7393", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 106517453, "Title": "The Analysis of Synonymy and Antonymy in Discourse Relations: An Interpretable Modeling Approach", "Abstract": "<p>The idea that discourse relations are interpreted both by explicit content and by shared knowledge between producer and interpreter is pervasive in discourse and linguistic studies. How much weight should be ascribed in this process to the lexical semantics of the arguments is, however, uncertain. We propose a computational approach to analyze contrast and concession relations in the PDTB corpus. Our work sheds light on the question of how much lexical relations contribute to the signaling of such explicit and implicit relations, as well as on the contribution of different parts of speech to these semantic relations. This study contributes to bridging the gap between corpus and computational linguistics by proposing transparent and explainable computational models of discourse relations based on the synonymy and antonymy of their arguments.</p>", "Keywords": "", "DOI": "10.1162/coli_a_00477", "PubYear": 2023, "Volume": "49", "Issue": "2", "JournalId": 13254, "JournalTitle": "Computational Linguistics", "ISSN": "0891-2017", "EISSN": "1530-9312", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Morelos, Centro de Investigación en Ciencias Cognitivas. "}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Morelos, Centro de Investigación en Ciencias. "}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Morelos, Centro de Investigación en Ciencias. "}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma del Estado de Morelos, Centro de Investigación en Ciencias. "}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Morelos, Centro de Investigación en Ciencias. "}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma del Estado de Morelos, Centro de Investigación en Ciencias. "}], "References": []}, {"ArticleId": 106517478, "Title": "Introduction to the Special Issue on Smart Systems for Industry 4.0 and IoT", "Abstract": "", "Keywords": "", "DOI": "10.1145/3583985", "PubYear": 2022, "Volume": "13", "Issue": "4", "JournalId": 24450, "JournalTitle": "ACM Transactions on Management Information Systems", "ISSN": "2158-656X", "EISSN": "2158-6578", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Cheng Kung University, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Texas at Dallas, U.S.A."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Giresun University, Turkey"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Instituto Politécnico Nacional, Mexico"}], "References": []}, {"ArticleId": 106517516, "Title": "Business Analysis", "Abstract": "<p>Not every job in tech is technical, writes <PERSON>CS</p>", "Keywords": "", "DOI": "10.1093/combul/bwad035", "PubYear": 2023, "Volume": "65", "Issue": "1", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106517534, "Title": "<PERSON>, <PERSON><PERSON>–19 and How Data Saves Lives", "Abstract": "<p><PERSON>, Chief Data Officer at the University of Leeds, shares her experience of improving cancer outcomes through timely data analysis – following the COVID–19-influenced decline in early stage cancer detection and diagnosis.</p>", "Keywords": "", "DOI": "10.1093/combul/bwad005", "PubYear": 2023, "Volume": "65", "Issue": "1", "JournalId": 23480, "JournalTitle": "ITNOW", "ISSN": "1746-5702", "EISSN": "1746-5710", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106517605, "Title": "A blockchain-based data-driven trustworthy approval process system", "Abstract": "Approval processes are intra-organizational business processes designed to sanction the execution of specific administrative tasks. Purchasing essential stationery products, filing bills for acquired items, approving attendance at conferences, processing insurance claims, approving loans, and similar tasks are examples for which employees require approvals from multiple authorities within an organization. Common concerns mentioned by both the approving authority and the proposal submitter are the genuineness of the proposer and the time required for approving the proposal. This paper presents a novel Blockchain-based Approval Process System (BAPS) to establish mutual trust between the submitter and the approving authorities. The proposed system’s design, implementation, and evaluation are included in this paper. The suggested approach can shorten the time needed to obtain the permissions and increase transparency between the users and the authority. In addition, it eliminates issues such as the misplacement of papers. It stores the information in a secure and tamper-proof platform which is some of the most significant drawbacks of traditional paper-based systems.", "Keywords": "Blockchain technology ; Approval process ; Decentralized apps ; Data-driven decision making ; Reputation system", "DOI": "10.1016/j.jjimei.2023.100162", "PubYear": 2023, "Volume": "3", "Issue": "1", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dr. <PERSON><PERSON><PERSON><PERSON> Technological University, Lonere 402104 MS, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dr. <PERSON><PERSON><PERSON><PERSON> Technological University, Lonere 402104 MS, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dr. <PERSON><PERSON><PERSON><PERSON> Technological University, Lonere 402104 MS, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dr. <PERSON><PERSON><PERSON><PERSON> Technological University, Lonere 402104 MS, India"}], "References": [{"Title": "Robotic Process Automation: Contemporary themes and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "115", "Issue": "", "Page": "103162", "JournalTitle": "Computers in Industry"}, {"Title": "An Exploration into Future Business Process Management Capabilities in View of Digitalization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "2", "Page": "83", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "A Blockchain-based Land Title Management System for Bangladesh", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "3096", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A Blockchain Platform for User Data Sharing Ensuring User Control and Incentives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "", "Page": "48", "JournalTitle": "Frontiers in Blockchain"}, {"Title": "Cybersecurity Enhancement through Blockchain Training (CEBT) – A serious game approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100001", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Business Process Management: The evolution of a discipline", "Authors": "<PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "126", "Issue": "", "Page": "103404", "JournalTitle": "Computers in Industry"}, {"Title": "i-Pulse: A NLP based novel approach for employee engagement in logistics organization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100011", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Blockchain-empowered Data-driven Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Information security vulnerability prediction based on business process model using machine learning approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102422", "JournalTitle": "Computers & Security"}, {"Title": "Supply chain digitalization: An integrated MCDM approach for inter-organizational information systems selection in an electronic supply chain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100038", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "A lightweight approach to smart contracts supporting safety, security, and privacy", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "100772", "JournalTitle": "Journal of Logical and Algebraic Methods in Programming"}, {"Title": "The Critical Success Factors for Robotic Process Automation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "138", "Issue": "", "Page": "103646", "JournalTitle": "Computers in Industry"}, {"Title": "Industry 4.0 in Healthcare: A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "100079", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Improving the Financial Security of National Health Insurance using Cloud-Based Blockchain Technology Application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "1", "Page": "100081", "JournalTitle": "International Journal of Information Management Data Insights"}]}, {"ArticleId": 106517640, "Title": "Intelligent recognition of audio scene based on hybrid attention and parallel deep feature processing under genetic evolutionary computing", "Abstract": "<p>Intelligent recognition of audio scene aims to analyze the environment information of audio signal with computer, which has important research significance. The audio scene recognition methods extract features from the input acoustic feature representation and use the acoustic features to classify the scene type. The common feature extraction method of audio signal is Mel frequency cepstrum coefficient. Although this method can capture the most recognizable part of audio data, it can only analyze the short-term characteristics of the signal. This is often not enough to completely describe the structural characteristics of the entire audio data. With the development of computer technology and high-performance processors, audio scene recognition via deep learning solves modeling high-dimensional and multi-classification complex relationships. In this work, we propose an audio scene recognition network that combines deep learning and genetic algorithm called IGA-HA-CNN-BiGRU. First, this work combines CNN and BiGRU networks to build a parallel depth feature extraction network. Parallel neural network has strong learning ability of spatial and temporal features and can effectively extract audio feature parameters. Second, this work combines time-domain attention with channel-domain attention to design a hybrid attention mechanism. This can process features to enhance the discriminability of audio features. Thirdly, in view of the defect of initialization of deep neural network, this work uses improved genetic algorithm to optimize it to improve the model performance. Finally, this work has carried out various experiments on the proposed method, and the experimental data can prove the reliability of the method.</p>", "Keywords": "Intelligent recognition; Audio scene; Deep learning; Genetic evolutionary computing", "DOI": "10.1007/s00521-023-08351-0", "PubYear": 2023, "Volume": "35", "Issue": "36", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Danyang Li", "Affiliation": "Shandong Normal University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shandong Normal University, Jinan, China"}], "References": [{"Title": "Environmental Audio Scene and Sound Event Recognition for Autonomous Surveillance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Pattern analysis based acoustic signal processing: a survey of the state-of-art", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "4", "Page": "913", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Intelligent Audio Signal Processing for Detecting Rainforest Species Using Deep Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "693", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "DeepSpectrumLite: A Power-Efficient Transfer Learning Framework for Embedded Speech and Audio Processing From Decentralized Data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "5", "Issue": "", "Page": "47", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Evaluation of Multimedia Popular Music Teaching Effect Based on Audio Frame Feature Recognition Technology", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Advances in Multimedia"}]}, {"ArticleId": 106517669, "Title": "Multi‐lane recognition using the YOLO network with rotatable bounding boxes", "Abstract": "<p>Currently, driver assistance and autonomous driving functions are emerging as essential convenience functions in automobiles. For autonomous driving, fast and accurate lane recognition is required, along with driving environment recognition. The recognized lanes must be divided into ego and left- and right-side lanes. Among deep learning, the You Only Look Once (YOLO) network is widely known as a fast and accurate object detection technique. The general methods are not robust to angle variations of the objects because of the use of a traditional bounding box, a rotation variant structure for locating rotated objects. The rotatable bounding box (RBBox) can effectively handle situations where the orientation angles of the objects are arbitrary. This study uses a YOLO approach with RBBox to recognize multi-lane accurately. The proposed method recognizes the ego lane and its surrounding lanes by accurately distinguishing them. And the proposed method shows stable multi-lane recognition performance by predicting them that exist in the images but do not exist in the ground truth of the TuSimple data set. Even compared to other lane recognition methods, it shows good competitiveness. Nevertheless, more training data and network learning are needed in a specific road environment (a lane is centered on the image).</p>", "Keywords": "autonomous driving;lane recognition;rotatable bounding box;You Only Look Once (YOLO)", "DOI": "10.1002/jsid.1193", "PubYear": 2023, "Volume": "31", "Issue": "3", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "<PERSON>e<PERSON>n Park", "Affiliation": "Department of Mechatronics Engineering Gyeongsang National University  Jinju South Korea"}, {"AuthorId": 2, "Name": "Jin‐Hyun Park", "Affiliation": "Department of Mechatronics Engineering Gyeongsang National University  Jinju South Korea"}], "References": [{"Title": "A review of lane detection methods based on deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107623", "JournalTitle": "Pattern Recognition"}, {"Title": "Rotational multipyramid network with bounding‐box transformation for object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "9", "Page": "5307", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 106517765, "Title": "Unintentional synchronization with self-avatar for upper- and lower-body movements", "Abstract": "<p> The subjective experience of embodying an avatar when immersed in virtual reality (VR) is known to support the sense of presence and to help with the interaction in a virtual environment. Virtual embodiment is often thought of as the consequence of replacement of the physical body by a virtual one, with a sense of agency for the avatar obtained by making the avatar’s body follow the user’s movements. This unidirectional motor link was, however, challenged by studies observing the opposite effect under different circumstances, for example, in a slow-motion context or when an arm movement was snapped on a predefined axis. These reports are, however, still rare or anecdotal. With the idea of a generalized bidirectional relationship between the user and the avatar in mind, we established a methodology to systematically provoke and study the circumstances under which participants follow the movements of their avatar during long repetitive movements without having been instructed to do so. A preliminary study confirmed that our virtual experimental setup, using full-body motion capture, avatar animation, and virtual mirrors, supports a strong sense of agency and body ownership for the avatar while enabling the experimental manipulation of the avatar’s movement. In the main experimental study, where participants performed repetitive upper- and lower-body movements while their avatar animations were either congruent or out-of-phase, we observed that almost all participants synchronized with their avatar at least once, for ∼ 47 % of trials for lower limb movements and ∼ 38 % for upper limb movements. Participants still reported low agency and ownership for the avatar under the incongruent condition, but, most interestingly, some of them also reported that their movements were not influenced by the avatar despite the behavioral effect. Our methodological approach and results contribute to the characterization of the conditions of occurrence of the self-avatar follower effect and, thereby, to identifying an enriched interaction design for VR, involving complex avatar–user mutual interdependencies. </p>", "Keywords": "virtual reality; Sense of embodiment; Sense of ownership; sense of agency; Self-Avatar Follower Effect", "DOI": "10.3389/frvir.2023.1073549", "PubYear": 2023, "Volume": "4", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Communication Sciences, Switzerland; School of Life Sciences, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Sciences, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Sciences, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Life Sciences, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer and Communication Sciences, Switzerland"}], "References": [{"Title": "Active Inference: Demystified and Compared", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "674", "JournalTitle": "Neural Computation"}]}, {"ArticleId": 106517797, "Title": "An omnidirectional approach to touch-based continuous authentication", "Abstract": "This paper focuses on how touch interactions on smartphones can provide a continuous user authentication service through behaviour captured by a touchscreen. While efforts are made to advance touch-based behavioural authentication, researchers often focus on gathering data, tuning classifiers, and enhancing performance by evaluating touch interactions in a sequence rather than independently. However, such systems only work by providing data representing distinct behavioural traits. The typical approach separates behaviour into touch directions and creates multiple user profiles. This work presents an omnidirectional approach which outperforms the traditional method independent of the touch direction - depending on optimal behavioural features and a balanced training set. Thus, we evaluate five behavioural feature sets using the conventional approach against our direction-agnostic method while testing several classifiers, including an Extra-Tree and Gradient Boosting Classifier, which is often overlooked. Results show that in comparison with the traditional, an Extra-Trees classifier and the proposed approach are superior when combining strokes. However, the performance depends on the applied feature set. We find that the TouchAlytics feature set outperforms others when using our approach when combining three or more strokes. Finally, we highlight the importance of reporting the mean area under the curve and equal error rate for single-stroke performance and varying the sequence of strokes separately.", "Keywords": "Behavioural biometric ; Continuous authentication ; Touch biometric ; Smartphone security ; Model selection", "DOI": "10.1016/j.cose.2023.103146", "PubYear": 2023, "Volume": "128", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Edinburgh Napier University, School of Computing, Engineering, and the Built Environment, Edinburgh, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Edinburgh Napier University, School of Computing, Engineering, and the Built Environment, Edinburgh, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Edinburgh Napier University, School of Computing, Engineering, and the Built Environment, Edinburgh, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Tan", "Affiliation": "Edinburgh Napier University, School of Computing, Engineering, and the Built Environment, Edinburgh, UK;Corresponding author"}], "References": [{"Title": "Identifying Child Users via Touchscreen Interactions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Sensor Networks"}]}, {"ArticleId": 106517833, "Title": "Spherical fuzzy TODIM method for MAGDM integrating cumulative prospect theory and CRITIC method and its application to commercial insurance selection", "Abstract": "<p>In rent years, commercial insurance selection is a hot issue in multiple attribute group decision making (MAGDM). Spherical fuzzy sets (SFSs) can better express the vague factors to commercial insurance selection. In this paper, spherical fuzzy TODIM approach based on cumulative prospect theory (SF-CPT-TODIM) is presented for MAGDM issues. It can display the psychological perception of decision makers (DMs) very well. Furthermore, when the attribute weights are unknown, we obtain the attribute weights through CRITIC method under SFSs to heighten the reasonability of weight information. Finally, this article gives a practical example of the raised approach for the hot issue about commercial insurance selection to certify the availability and superiority for the raised method via comparing with some existing approaches. </p>", "Keywords": "Multiple attribute group decision making (MAGDM); Spherical fuzzy sets (SFSs); TODIM; Cumulative prospect theory (CPT); CRITIC; Commercial insurance selection", "DOI": "10.1007/s10462-023-10409-3", "PubYear": 2023, "Volume": "56", "Issue": "9", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Liupanshui Normal University, Liupanshui, China; School of Mathematical Sciences, Sichuan Normal University, Chengdu, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Chongqing University of Arts and Sciences, Chongqing, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Sichuan Normal University, Chengdu, People’s Republic of China; School of Business, Sichuan Normal University, Chengdu, People’s Republic of China"}], "References": [{"Title": "TODIM-based multi-criteria decision-making method with hesitant fuzzy linguistic term sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3647", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Interval-valued intuitionistic fuzzy TODIM method based on <PERSON><PERSON><PERSON>zer–Sklar power aggregation operators and their applications to group decision making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "18", "Page": "14091", "JournalTitle": "Soft Computing"}, {"Title": "Pythagorean fuzzy MCDM method based on CoCoSo and CRITIC with score function for 5G industry evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3813", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Improved TODIM method for intuitionistic fuzzy MAGDM based on cumulative prospect theory and its application on stock investment selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "891", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "TODIM method for multiple attribute group decision making based on cumulative prospect theory with 2‐tuple linguistic neutrosophic sets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "1199", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A novel interval-valued fuzzy soft decision-making method based on CoCoSo and CRITIC for intelligent healthcare management evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4213", "JournalTitle": "Soft Computing"}, {"Title": "Directional correlation coefficient measures for Pythagorean fuzzy sets: their applications to medical diagnosis and cluster analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "1025", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "CPT‐TODIM method for bipolar fuzzy multi‐attribute group decision making and its application to network security service provider selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "1943", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Pythagorean Fuzzy TODIM Method Based on the Cumulative Prospect Theory for MAGDM and Its Application on Risk Assessment of Science and Technology Projects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "4", "Page": "1027", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Picture fuzzy interactional partitioned Heronian mean aggregation operators: an application to MADM process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "1171", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "TODIM method based on cumulative prospect theory for multiple attribute group decision‐making under 2‐tuple linguistic Pythagorean fuzzy environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "6", "Page": "2548", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A probabilistic hesitant fuzzy Choquet integral-based TODIM method for multi-attribute group decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116266", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Distance measure on intuitionistic fuzzy sets and its application in decision‐making, pattern recognition, and clustering problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "3", "Page": "2458", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A heterogeneous multi-attribute case retrieval method based on neutrosophic sets and TODIM for emergency situations", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "13", "Page": "15177", "JournalTitle": "Applied Intelligence"}, {"Title": "A novel picture fuzzy CRITIC & REGIME methodology: Wearable health technology application", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "104942", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Extended base-criterion method based on the spherical fuzzy sets to evaluate waste management", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "19", "Page": "9979", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 106517958, "Title": "3D facial expression retargeting framework based on an identity-independent expression feature vector", "Abstract": "<p>One important aspect of multimedia application scenarios is the ability to control the facial expressions of virtual characters. One popular solution is to retarget the expressions of actors to virtual characters. Traditional 3D facial expression retargeting algorithms are mostly based on the Blendshape model. However, excessive reliance on the Blendshape model introduces several limitations. For example, the quality of the base expressions has a large influence on the expression retargeting results, requires large amounts of 3D face data, and must be calibrated for each user. We propose a 3D facial expression retargeting framework based on an identity-independent expression feature vector (hereafter referred to as the expression vector). This expression vector, which is related only to facial expressions, is originally extracted from face images; then, the corresponding expressions are transferred to the target (which can be any 3D face model) using V2ENet, a generative adversarial network (GAN)-structured model. Our framework requires only the expression vector and a neutral 3D face model to achieve natural and vivid expression retargeting, and it does not rely on the Blendshape model. When using the expression vector obtained from a cognitive perspective, our method can also perform 3D expression retargeting at the cognitive level. A series of experiments demonstrates that our method not only provides a simplified expression retargeting process but also achieves a better effect than the deformation transfer algorithm. The proposed framework is suitable for a wide range of applications and also achieves good expression retargeting for cartoon-style face models.</p>", "Keywords": "Virtual characters; 3D face model; Expression retargeting; Deep learning", "DOI": "10.1007/s11042-023-14547-2", "PubYear": 2023, "Volume": "82", "Issue": "15", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Engineering Research Center of Mixed Reality and Advanced Display, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 2, "Name": "Dong<PERSON> Weng", "Affiliation": "Beijing Engineering Research Center of Mixed Reality and Advanced Display, Beijing Institute of Technology, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "China Software Testing Center, Bejing, China"}, {"AuthorId": 4, "Name": "Le Luo", "Affiliation": "Beijing Engineering Research Center of Mixed Reality and Advanced Display, Beijing Institute of Technology, Beijing, China"}], "References": []}, {"ArticleId": 106517997, "Title": "Enhancement in web accessibility for visually impaired people using hybrid deep belief network –bald eagle search", "Abstract": "<p>In the modern era, accessing the web is the major task for Visually Impaired (VI) people that creates opportunities to connect with social media as part of professional, political, and social life. One of the difficulties faced by VI users is accessing and understanding images online. The advancements in assistive technologies based on computer-vision (CV) assist the VI people in diverse scenarios such as grocery shopping, generation of alternative text, object recognition, understanding text documents, identifying people etc. In this study, to make the digital platform user-friendly for VI people, an automated system is developed to generate alternative (alt) text for online images that are not captioned or the alt text is not specified for the image. The proposed Deep Belief Network - Bald Eagle Search (DBN-BES) method offers an effective way for VI that allows automatic captioning of web images. Our proposed work consists of two stages. The initial stage is the selection of images that are not captioned, and this selection process is obtained using the Bald Eagle Search (BES) Algorithm. After the selection stage, alt text for corresponding images is produced with the help of the Deep Belief Network (DBN) model. Thus, the proposed DBN-BES model automatically generates alt text which helps the VI people to understand the image content better. The presented model routinely increases web accessibility, addresses massive image data being created daily, and makes the web accessible to multiple users around the globe.</p>", "Keywords": "Visually impaired; Artificial intelligence; Alt text images; Bald eagle search; Deep belief network", "DOI": "10.1007/s11042-023-14494-y", "PubYear": 2023, "Volume": "82", "Issue": "16", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SRMIST, Ghaziabad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, SRMIST, Ghaziabad, India"}], "References": [{"Title": "Wearable assistive devices for visually impaired: A state of the art survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "37", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Speech and web-based technology to enhance education for pupils with visual impairment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Zdeněk K<PERSON>ň<PERSON>l; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "219", "JournalTitle": "Journal on Multimodal User Interfaces"}, {"Title": "Video Summarization Using Highlight Detection and Pairwise Deep Ranking Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1839", "JournalTitle": "Procedia Computer Science"}, {"Title": "Web accessibility investigation and identification of major issues of higher education websites with statistical measures: A case study of college websites", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "3", "Page": "901", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Feature-transfer network and local background suppression for microaneurysm detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Advanced Deep Learning for Resource Allocation and Security Aware Data Offloading in Industrial Mobile Edge Computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "4", "Page": "265", "JournalTitle": "Big Data"}]}, {"ArticleId": 106518050, "Title": "Robobo SmartCity: An Autonomous Driving Model for Computational Intelligence Learning Through Educational Robotics", "Abstract": "This article presents the Robobo SmartCity model, an educational resource to introduce students to computational intelligence (CI) topics using educational robotics as the core learning technology. Robobo SmartCity allows educators to train learners in artificial intelligence (AI) fundamentals from a feasible and practical perspective, following the recommendations of digital education plans to introduce AI at all educational levels. This resource is based on the Robobo educational robot and an autonomous driving setup. It is made up of a city mockup, simulation models, and programming libraries adapted to the students’ skill level. In it, students can be trained in CI topics that support robot autonomy, as computer vision, machine learning, or human–robot interaction, while developing solutions in the motivating and challenging scope of autonomous driving. The main details of this open resource are provided with a set of possible challenges to be faced in it. They are organized in terms of the educational level and students’ skills. The resource has been mainly tested with secondary and high school students, obtaining successful learning outcomes, presented here to inspire other teachers in taking advantage of this learning technology in their classes.", "Keywords": "Computational intelligence (CI);educational robots;educational simulations;machine learning;mobile and personal devices;robot programming;science;technology;engineering;and mathematics (STEM)", "DOI": "10.1109/TLT.2023.3244604", "PubYear": 2023, "Volume": "16", "Issue": "4", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Integrated Group for Engineering Research of the CITIC Research Center, University of Coruña, Ferrol, Spain"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Integrated Group for Engineering Research of the CITIC Research Center, University of Coruña, Ferrol, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Integrated Group for Engineering Research of the CITIC Research Center, University of Coruña, Ferrol, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Integrated Group for Engineering Research of the CITIC Research Center, University of Coruña, Ferrol, Spain"}], "References": [{"Title": "Learning Robotics: a Review", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "Current Robotics Reports"}, {"Title": "Conceptualizing AI literacy: An exploratory review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "100041", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "How to pick a mobile robot simulator: A quantitative comparison of CoppeliaSim, Gazebo, MORSE and Webots with a focus on accuracy of motion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102629", "JournalTitle": "Simulation Modelling Practice and Theory"}]}, {"ArticleId": 106518074, "Title": "A fast coarse-to-fine point cloud registration based on optical flow for autonomous vehicles", "Abstract": "<p>Point cloud registration is a vital prerequisite for many autonomous vehicle tasks. However, balancing the accuracy and computational complexity is very challenging for existing point cloud registration algorithms. This paper proposes a fast coarse-to-fine point cloud registration approach for autonomous vehicles. Our method uses nearest neighbor sample consensus optical flow pairwise matching resulting from a 2D bird’s eye view to initialize the coarse registration. It provides an initial 2D guess matrix for the fine registration and effectively reduces the computational complexity. In two-stage registration, our approach eliminates outliers by utilizing our self-correction module, which improves the robustness without using global positioning system (GPS) information. Point cloud registration experiments show that only our approach can process in real-time (71 ms, on average) while achieving state-of-the-art accuracy on the KITTI Odometry dataset, achieving a mean relative rotation error of 0.125<sup>∘</sup> and a mean relative translation error of 0.038 m. In addition, real-road vehicle-to-vehicle point cloud registration experiments verify that the proposed algorithm can effectively align two vehicles’ point cloud when the GPS is not synchronized. A demonstration video is available at https://youtu.be/BJTSDChQchw .</p>", "Keywords": "Autonomous vehicles; Point cloud registration; Coarse-to-fine registration; Optical flow; Real-time", "DOI": "10.1007/s10489-022-04308-3", "PubYear": 2023, "Volume": "53", "Issue": "16", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, China; University of Science and Technology of China, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, China; Anhui Engineering Laboratory for Intelligent Driving Technology and Application, Hefei, China; Innovation Research Institute of Robotics and Intelligent Manufacturing (Hefei), Chinese Academy of Sciences, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, China; University of Science and Technology of China, Hefei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, China; Anhui Engineering Laboratory for Intelligent Driving Technology and Application, Hefei, China; Innovation Research Institute of Robotics and Intelligent Manufacturing (Hefei), Chinese Academy of Sciences, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei Institutes of Physical Science, Chinese Academy of Sciences, Hefei, China; University of Science and Technology of China, Hefei, China"}], "References": [{"Title": "Local feature extraction network with high correspondences for 3d point cloud registration", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "9638", "JournalTitle": "Applied Intelligence"}, {"Title": "Coarse-fine point cloud registration based on local point-pair features and the iterative closest point algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "11", "Page": "12569", "JournalTitle": "Applied Intelligence"}, {"Title": "Dynamic vehicle pose estimation and tracking based on motion feedback for LiDARs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "2", "Page": "2362", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 106518086, "Title": "Matrix Quantization and LPC Vocoder Based Linear Predictive for Low-Resource Speech Recognition system", "Abstract": "<p>Over the last ten years, there has been significant progress in the use of low-rate speech coders in voice applications for computers, military communications, and civil communications. This advancement has been made possible by the development of new speech coders that can generate high-quality speech at low data rates. The majority of existing coders include spectral representation of speech, speech waveform matching, and ”optimization” of the coder’s performance for human hearing. The goal of this paper is to provide a thorough evaluation of voice coding methods for educational purposes, with a particular emphasis on the algorithms used in low-rate cellular communication standards. The algorithm we developed using a voice-excited LPC vocoder produces clear, low-distortion results. Ordinary LPCs, on the other hand, fall short of vocoders because they can handle signals other than speech, such as music. To improve quality, additional bandwidth is used to reduce the bit rate. To improve the quality, we tried two approaches. The first was to increase the number of bits required to quantize the DCT coefficients. This coefficient would outperform the inverse DCT in closer error rearrangements. The second possibility is to increase the total number of quantized coefficients. As a result, error array rearrangements would be more accurate. The goal is to identify the point at which a method improvement outperforms the previous, better result. Other coding methods become more complex, but this vocoder suffices.</p>", "Keywords": "", "DOI": "10.1145/3585313", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Computer Science and Information Technology, King Faisal University, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering & Applications, GLA University, Mathura, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology, Vellore 632014, India, and Department of Electrical and Computer Engineering, Lebanese American University, Byblos, Lebanon"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering & Applications, GLA University, Mathura, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems College of computer and Information Science. Princess <PERSON><PERSON><PERSON><PERSON> University P.O. BOX 84428, Riyadh 11671, Saudi Arabia"}], "References": []}, {"ArticleId": 106518098, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0925-2312(23)00174-1", "PubYear": 2023, "Volume": "529", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [], "References": []}, {"ArticleId": 106518160, "Title": "Optimisation of SPARQL queries over the RDF data in the cloud environment", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2023.10054363", "PubYear": 2023, "Volume": "24", "Issue": "3/4", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106518215, "Title": "Output-based adaptive distributed observer for general linear leader systems over periodic switching digraphs", "Abstract": "<p>In this paper, we present a sufficient condition for the exponential stability of a class of linear switched systems. As an application of this stability result, we establish an output-based adaptive distributed observer for a general linear leader system over a periodic jointly connected switching communication network, which extends the applicability of the output-based adaptive distributed observer from a marginally stable linear leader system to any linear leader system and from an undirected switching graph to a directed switching graph. This output-based adaptive distributed observer will be applied to solve the leader-following consensus problem for multiple double-integrator systems.</p>", "Keywords": "Distributed observer; Multi-agent systems; Stability; Switched systems", "DOI": "10.1007/s43684-023-00046-6", "PubYear": 2023, "Volume": "3", "Issue": "1", "JournalId": 90531, "JournalTitle": "Autonomous Intelligent Systems", "ISSN": "", "EISSN": "2730-616X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute and Department of Mechanical and Automation Engineering, The Chinese University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute and Department of Mechanical and Automation Engineering, The Chinese University of Hong Kong, Hong Kong, China"}], "References": [{"Title": "Semi-global leader-following output consensus of heterogeneous systems subject to actuator position and rate saturation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Intelligent Systems"}]}, {"ArticleId": 106518265, "Title": "Clustering Technique for Mobile Edge Computing To Detect Clumps in Transportation-Related Problems", "Abstract": "<p>The daily functioning of civilization depends heavily on transportation. In most cities, a sizable section of the working class consistently attends both employment and education. There are many different things you can do when commuting, such as unwinding, eating out, and other things. The most popular means of transportation in North Cyprus, particularly in developing cities, is island transportation, which includes the usage of both private cars and commercial vehicles. The advent of edge computing, which offers the opportunity to connect potent processing servers next to the mobile device, is a significant step toward improving user experience and reducing resource use. Mobile Edge Computing is the next trustworthy approach for how mobile devices consume communications and computing. Offloading computation is a key component developing mobile edge computing, which enables devices to get around clustering techniques' limitations and get around computing, storage, and energy constraints. However, computation offloading is not always the best strategy to use; making choosing unloading is a critical step that requires consideration of numerous factors. For instance, shifting the high-resource node to an edge server and granting similar capabilities to the low-resource nodes would delegate heavy duties to the external unit inside the network. The evaluations' results were noteworthy and substantial. Problems involving the vehicles of institutions and organizations can be resolved using the suggested solution to the school bus routing issue. We also test the impact of network latency on the delivery of a particular result using an Edge Computing simulator.</p>", "Keywords": "Mobile edge computing (MEC); transportation problems; save time; clustering technique; energy efficiency; resource optimization", "DOI": "10.3991/ijim.v17i04.37801", "PubYear": 2023, "Volume": "17", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Associate Professor, Department of Computer Science and Engineering, Sathyabama Institute of Science and Technology, Chennai"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": " <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": " <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106518401, "Title": "Usable AI: Critical Review of Its Current Issues and Trends", "Abstract": "Usable Artificial Intelligence (AI) application refers to an AI solution that is characterized by the easiness of use and learning through an optimum interface established by a proficient Human-Computer Interface (HCI) design. This article reviews the related literature to find out the importance of HCI to make AI valuable and usable. The article also investigates the possible solutions and guidelines that ensure the usability of AI-powered applications as well as the corresponding challenges of designing usable AI. It also essentially explores several usability evaluation methods employed by recent studies. The findings demonstrate the substantial role of HCI in designing Human-centred AI (HAI) applications. HAI lets AI applications support humans instead of replacing them and grants user’s better control over security and privacy. No standard usability measures exist and more research is required especially on the issues of interpretability, integrability, and collaboration © 2023 Majed Alshamari and Thanaa Alsalem. This open-access article is distributed under a Creative Commons Attribution (CC-BY) 4.0 license.", "Keywords": "Human Computer Interaction (HCI); Human-Centred AI (HAI); Usability; Usable Artificial Intelligence (AI)", "DOI": "10.3844/jcssp.2023.326.333", "PubYear": 2023, "Volume": "19", "Issue": "3", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Systems Department, King Faisal University, Hofuf, Saudi Arabia"}, {"AuthorId": 2, "Name": "Thanaa Alsalem", "Affiliation": "Information Systems Department, King Faisal University, Hofuf, Saudi Arabia"}], "References": []}, {"ArticleId": 106518425, "Title": "Mobile Screen Addiction Using Addictive Attribute by Combination of Algorithm-In Addiction Disorder", "Abstract": "<p>Mobile Screen spent by teenage who use lot of time on screen experience psychiatric problems such as frustration, fatigue, depression, anxiety, and so on. Matplotlib pyplot with histogram figure is used to evaluate the psychiatric condition data (CSV file). For the purpose of rating the condition data, the standard deviation and mean are determined. For data analysis, there is a correlation matrix that compares every row and column. Gradient <PERSON>, Logistic Regression, Knn algorithm is used to compare the precision of the machine learning process, which merged the characteristics of addictive and non-addictive data to find rating disorders.</p>", "Keywords": "Data Science; <PERSON><PERSON><PERSON>; logistic regression; Pyplot.", "DOI": "10.3991/ijim.v17i04.37745", "PubYear": 2023, "Volume": "17", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Computer Application, VISTAS, Chennai"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106518454, "Title": "Data-driven consensus control of fully distributed event-triggered multi-agent systems", "Abstract": "<p>This study investigates the consensus control issue in discrete-time linear multi-agent systems (MASs) using data-driven control under undirected communication networks. To alleviate the communication burden, an adaptive event-triggered control strategy involving only local information is proposed and a model-based stability condition is derived that guarantees the asymptotic consensus of MASs. Furthermore, a data-based consensus condition for unknown MASs is established by combining a data-based system representation with the model-based stability condition, using only pre-collected noisy input-state data instead of the accurate system information a priori. Specifically, both model-based and data-driven event-triggered controllers can be utilized without requiring any global information. The validity and correctness of the controllers and associated theoretical results are demonstrated via numerical simulations.</p>", "Keywords": "distributed control; event-triggered control; data-driven control; discrete-time MASs", "DOI": "10.1007/s11432-022-3629-1", "PubYear": 2023, "Volume": "66", "Issue": "5", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China; The State Key Lab of Autonomous Intelligent Unmanned Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China; The State Key Lab of Autonomous Intelligent Unmanned Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China; The State Key Lab of Autonomous Intelligent Unmanned Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Automation, Beijing Institute of Technology, Beijing, China; The State Key Lab of Autonomous Intelligent Unmanned Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Control Science and Engineering, Tongji University, Shanghai, China; School of Automation, Beijing Institute of Technology, Beijing, China; The State Key Lab of Autonomous Intelligent Unmanned Systems, Beijing Institute of Technology, Beijing, China"}], "References": [{"Title": "Simultaneous cooperative relative localization and distributed formation control for multiple UAVs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "1", "Page": "119201", "JournalTitle": "Science China Information Sciences"}, {"Title": "How often should one update control and estimation: review of networked triggering techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "5", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Wearable ubiquitous energy system", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "2", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Robust distributed adaptive consensus for discrete-time multiagent systems with uncertain topologies", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "449", "Issue": "", "Page": "100", "JournalTitle": "Neurocomputing"}, {"Title": "An Overview of Recent Advances in Distributed Coordination of Multi-Agent Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "3", "Page": "307", "JournalTitle": "Unmanned Systems"}, {"Title": "Dynamic Event-triggered Consensus Control of Discrete-time Linear Multi-agent Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "17", "Page": "123", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Adaptive event-triggered state estimation for large-scale systems subject to deception attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "2", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 106518550, "Title": "Cross-model consensus of explanations and beyond for image classification models: an empirical study", "Abstract": "<p>Existing explanation algorithms have found that, even if deep models make the same correct predictions on the same image, they might rely on different sets of input features for classification. However, among these features, some common features might be used by the majority of models. In this paper, we are wondering what the common features used by various models for classification are and whether the models with better performance may favor those common features . For this purpose, our work uses an explanation algorithm to attribute the importance of features (e.g., pixels or superpixels) as explanations and proposes the cross-model consensus of explanations to capture the common features. Specifically, we first prepare a set of deep models as a committee , then deduce the explanation for every model, and obtain the consensus of explanations across the entire committee through voting . With the cross-model consensus of explanations, we conduct extensive experiments using 80+ models on five datasets/tasks. We find three interesting phenomena as follows: (1) the consensus obtained from image classification models is aligned with the ground truth of semantic segmentation; (2) we measure the similarity of the explanation result of each model in the committee to the consensus (namely consensus score ), and find positive correlations between the consensus score and model performance; and (3) the consensus score potentially correlates to the interpretability.</p>", "Keywords": "Interpretability; Explanations of deep neural networks; Semantic segmentation; and Visualization", "DOI": "10.1007/s10994-023-06312-1", "PubYear": 2023, "Volume": "112", "Issue": "5", "JournalId": 1797, "JournalTitle": "Machine Learning", "ISSN": "0885-6125", "EISSN": "1573-0565", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Baidu Inc., Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Baidu Inc., Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Harvard University, Cambridge, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Baidu Inc., Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Baidu Inc., Beijing, China"}], "References": [{"Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "336", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "A decision-theoretic approach for model interpretability in Bayesian framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "9-10", "Page": "1855", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 106518551, "Title": "Machine translation and its evaluation: a study", "Abstract": "<p>Machine translation (namely MT) has been one of the most popular fields in computational linguistics and Artificial Intelligence (AI). As one of the most promising approaches, MT can potentially break the language barrier of people from all over the world. Despite a number of studies in MT, there are few studies in summarizing and comparing MT methods. To this end, in this paper, we principally focus on presenting the two mainstream MT schemes: statistical machine translation (SMT) and neural machine translation (NMT), including their basic rationales and developments. Meanwhile, the detailed translation models are also presented, such as the word-based model, syntax-based model, and phrase-based model in statistical machine translation. Similarly, approaches in NMT, such as the recurrent neural network-based, attention mechanism-based, and transformer-based models are presented. Last but not least, the evaluation approaches also play an important role in helping developers to improve their methods better in MT. The prevailing machine translation evaluation methodologies are also presented in this article.</p>", "Keywords": "Natural Language Processing; Computational linguistics; Statistical machine translation; Neural machine translation; Evaluation methods", "DOI": "10.1007/s10462-023-10423-5", "PubYear": 2023, "Volume": "56", "Issue": "9", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Macau University of Science and Technology, Taipa, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Macau University of Science and Technology, Taipa, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Deakin University, Geelong, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Macau University of Science and Technology, Taipa, China"}, {"AuthorId": 5, "Name": "Hong-Ning Dai", "Affiliation": "The Department of Computer Science, Hong Kong Baptist University, Hong Kong, China"}], "References": [{"Title": "Assessing gender bias in machine translation: a case study with Google Translate", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "6363", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Linguistic knowledge-based vocabularies for Neural Machine Translation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "485", "JournalTitle": "Natural Language Engineering"}, {"Title": "Knowledge Graphs Effectiveness in Neural Machine Translation Improvement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "3", "Page": "287", "JournalTitle": "Computer Science"}, {"Title": "Neural machine translation with a polysynthetic low resource language", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "4", "Page": "325", "JournalTitle": "Machine Translation"}, {"Title": "Sustainable AI: AI for sustainability and the sustainability of AI", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "3", "Page": "213", "JournalTitle": "AI and Ethics"}, {"Title": "Towards achieving a delicate blending between rule-based translator and neural machine translator", "Authors": "Md. <PERSON>; M<PERSON><PERSON> <PERSON>; <PERSON><PERSON> B<PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "18", "Page": "12141", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 106518585, "Title": "Designing Hybrid Human–AI Orchestration Tools for Individual and Collaborative Activities: A Technology Probe Study", "Abstract": "Combining individual and collaborative learning is common, but dynamic combinations (which happen as-the-need arises, rather than in preplanned ways, and may happen on an individual basis) are rare. This work reports findings from a technology probe study exploring alternative designs for classroom co-orchestration support for dynamically transitioning between individual and collaborative learning. The study involved 1) a technology-probe classroom study in an authentic, AI-supported classroom to understand teachers' and students' needs for co-orchestration support over dynamic transitions; and 2) workshops and interviews with students and teachers to get informed feedback about their lived experiences. About 118 students and 3 teachers from a middle school in the US experienced a pairing policy—student, teacher, and AI-controlled pairing policy—(i.e., identifying students needing help and potential helpers) for switching from individual to a peer-tutoring activity. This work aims to answer the following questions: 1) How did students and teachers react to these pairing policies? and 2) What are students' and teachers' desires for sharing control over the orchestration of dynamic transitions? Findings suggest the need for a form of hybrid control between students, teachers, and AI systems over transitions, as well as for adaptivity and adaptability for different classroom characteristics, teachers, and students' prior knowledge.", "Keywords": "Adaptive and intelligent educational systems;collaborative learning;human–AI orchestration;hybrid human–AI tools;individual learning;orchestration tools", "DOI": "10.1109/TLT.2023.3248155", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Monash University, Clayton, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Human Computer Interaction Institute, Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 3, "Name": "LuE<PERSON><PERSON><PERSON>", "Affiliation": "Instructional Technology and Learning Sciences, Utah State University Emma Eccles Jones College of Education and Human Services, Logan, USA"}, {"AuthorId": 4, "Name": "Nikol Rummel", "Affiliation": "Department of Philosophy and Educational Science, Ruhr-Universitaet Bochum, Bochum, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Human Computer Interaction Institute, Carnegie Mellon University, Pittsburgh, USA"}], "References": []}, {"ArticleId": *********, "Title": "Exponential Synchronization for Variable-order Fractional Complex Dynamical Networks via Dynamic Event-triggered Control Strategy", "Abstract": "<p>This paper is concerned with the global exponential synchronization issues for variable-order fractional complex dynamic networks (FCDNs). Firstly, a new derivative operator, which is called as the generalized Caputo variable-order fractional derivative, is developed, and some properties and lemmas are rigorous proved. Secondly, a new dynamic event-triggered control mechanism is designed to realize the synchronization objective, where the generalized Caputo variable-order fractional derivative is applied to characterize the evolution state of internal dynamic variable. And the exclusion for Zeno behavior is verified by contradiction analysis method. Thirdly, a class of functions, which is an extension of the <PERSON><PERSON><PERSON><PERSON> function, is introduced to model the nonlinear dynamics for the considered system. With the aid of fractional <PERSON><PERSON><PERSON><PERSON> functional method, some auxiliary functions and advanced mathematical analysis techniques, the global exponential synchronization conditions are established in terms of linear matrix inequalities (LMIs). Finally, the correctness of the theoretical results and the feasibility of the designed controller in this paper are confirmed by applying a numerical simulation example.</p>", "Keywords": "Complex dynamical networks; Variable-order fractional operators; Dynamic event-triggered mechanism; Zeno behavior; Synchronization", "DOI": "10.1007/s11063-023-11169-5", "PubYear": 2023, "Volume": "55", "Issue": "7", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Southeast University, Nanjing, China; Yonsei Frontier Lab, Yonsei University, Seoul, Korea"}], "References": []}, {"ArticleId": 106518680, "Title": "Stretchable e-skin and transformer enable high-resolution morphological reconstruction for soft robots", "Abstract": "Many robotic tasks require knowledge of the exact 3D robot geometry. However, this remains extremely challenging in soft robotics because of the infinite degrees of freedom of soft bodies deriving from their continuum characteristics. Previous studies have achieved only low proprioceptive geometry resolution (PGR), thus suffering from loss of geometric details (for example, local deformation and surface information) and limited applicability. Here we report an intelligent stretchable capacitive e-skin to endow soft robots with high PGR (3,900) bodily awareness. We demonstrate that the proposed e-skin can finely capture a wide range of complex 3D deformations across the entire soft body through multi-position capacitance measurements. The e-skin signals can be directly translated to high-density point clouds portraying the complete geometry via a deep architecture based on transformer. This high PGR proprioception system providing millimetre-scale, local and global geometry reconstruction (2.322 ± 0.687 mm error on a 20 × 20 × 200 mm soft manipulator) can assist in solving fundamental problems in soft robotics, such as precise closed-loop control and digital twin modelling. Developing proprioception systems for flexible structures such as soft robots is a challenge. <PERSON> et al. report a stretchable e-skin for soft robot proprioception. Combined with deep learning, the e-skin enables high-resolution 3D geometry reconstruction of the soft robot and can be applied in many scenarios, such as human–robot interaction.", "Keywords": "Design; synthesis and processing;Electrical and electronic engineering;Engineering;general", "DOI": "10.1038/s42256-023-00622-8", "PubYear": 2023, "Volume": "5", "Issue": "3", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON>in <PERSON>", "Affiliation": "SMART Group, Institute for Digital Communications, School of Engineering, The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Integrated Micro and Nano Systems, School of Engineering, The University of Edinburgh, Edinburgh, UK; Edinburgh Centre for Robotics, The Bayes Centre, The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, The University of Hong Kong, Hong Kong SAR, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SMART Group, Institute for Digital Communications, School of Engineering, The University of Edinburgh, Edinburgh, UK"}], "References": [{"Title": "Electronic skins and machine learning for intelligent soft robots", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "41", "Page": "eaaz9239", "JournalTitle": "Science Robotics"}, {"Title": "Heterogeneous sensing in a multifunctional soft sensor for human-robot interfaces", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "49", "Page": "eabc6878", "JournalTitle": "Science Robotics"}, {"Title": "Somatosensory actuator based on stretchable conductive photothermally responsive hydrogel", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Lecheng Ruan", "PubYear": 2021, "Volume": "6", "Issue": "53", "Page": "eabd5483", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 106518694, "Title": "Isolated toughness and fractional (\n a,b,n \n )-critical graphs", "Abstract": "A graph G is a fractional ( a , b , n ) -critical graph if removing any n vertices from G , the resulting subgraph still admits a fractional [ a , b ] -factor. In this paper, we determine the exact tight isolated toughness bound for fractional ( a , b , n ) -critical graphs. To be specific, a graph G is fractional ( a , b , n ) -critical if δ ( G ) ≥ a + n and I ( G ) > a − 1 + n + 1 n a , b , where n a , b ≥ 2 is an integer satisfies ( n a , b − 1 ) a ≤ b ≤ n a , b a − 1 . Furthermore, the sharpness of bounds is showcased by counterexamples. Our contribution improves a result from [<PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, Tight isolated toughness bound for fractional ( k , n ) -critical graphs, Discrete Appl. Math. 322 (2022), 194–202] which established the tight isolated toughness bound for fractional ( k , n ) -critical graphs.", "Keywords": "Graph ; isolated toughness ; fractional [ a ; b ] -factor ; fractional ( a ; b ; n ) -critical graph", "DOI": "10.1080/09540091.2023.2181482", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 9414, "JournalTitle": "Connection Science", "ISSN": "0954-0091", "EISSN": "1360-0494", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Yunnan Normal University, Kunming, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Zhejiang Normal University, Jinhua, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Nanjing University, Nanjing, People's Republic of China"}], "References": []}, {"ArticleId": 106518699, "Title": "A Blockchain-Based Copyright Protection Scheme With Proactive Defense", "Abstract": "Copyright protection, including copyright registration, copyright transfer and infringement penalty, plays a critical role in preventing illegal usage of original works. The mainstream traditional copyright protection schemes need an authority online all the time to handle copyright issues and face some problems such as intricate copyright transfer, single point of failure and so on. To alleviate the burden of the authority, a few blockchain-based copyright protection schemes are proposed. However, most of them do not consider copyright transfer, and their infringement penalty may only happen after copyright owners discover the infringement behavior (i.e., “ex-post penalty”). In this article, we propose a new security strategy, called “Proactive Defense” in copyright protection which can prevent infringement before it occurs. With our proposed proactive defense strategy, we design a secure copyright protection scheme which provides advantages of compact copyright transfer and prior infringement penalty. More concrete, both copyright registration and transfer are regarded as transactions and recorded to the blockchain. Based on the double-authentication-prevention signature and non-interactive zero-knowledge proof techniques, illegal copyright transfer can be detected and the infringement penalty can be done automatically with a tailored smart contract before the completion of the transfer. Our security analysis shows that the proposed scheme can achieve all desirable security properties. Moreover, we implement our scheme in Java and evaluate the performance experimentally. Experimental results show that the proposed scheme has good security and efficiency, which can be applied for the copyright protection.", "Keywords": "Blockchain;copyright protection;double-authentication-preventing signature", "DOI": "10.1109/TSC.2023.3246476", "PubYear": 2023, "Volume": "16", "Issue": "4", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Cyber Security, Jinan University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Cyber Security, Jinan University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Cyber Security, Jinan University, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fongwell Data Limited Company, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Waterloo, Waterloo, ON, Canada"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Cyber Security, Jinan University, Guangzhou, China"}], "References": [{"Title": "Effective watermarking technique using optimal discrete wavelet transform and sanitization technique", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "13161", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Circuit Copyright Blockchain: Blockchain-Based Homomorphic Encryption for IP Circuit Protection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1410", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Watermarking-Based Secure Plaintext Image Protocols for Storage, Show, Deletion and Retrieval in the Cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "1678", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "FedCrowd: A Federated and Privacy-Preserving Crowdsourcing Platform on Blockchain", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "2060", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "PoTS: A Secure Proof of TEE-Stake for Permissionless Blockchains", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "4", "Page": "2173", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "A DWT-SVD based adaptive color multi-watermarking scheme for copyright protection using AMEF and PSO-GWO", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114414", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Research on digital copyright protection based on the hyperledger fabric blockchain network technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 106518756, "Title": "LimitAccess: on-device TinyML based robust speech recognition and age classification", "Abstract": "<p>Automakers from Honda to Lamborghini are incorporating voice interaction technology into their vehicles to improve the user experience and offer value-added services. Speech recognition systems are a key component of smart cars, enhancing convenience and safety for drivers and passengers. In the future, safety-critical features may rely on speech recognition, but this raises concerns about children accessing such services. To address this issue, the LimitAccess system is proposed, which uses TinyML for age classification and helps parents limit children’s access to critical speech recognition services. This study employs a lite convolutional neural network (CNN) model for two different reasons: First, CNN showed superior accuracy compared to other audio classification models for age classification problems. Second, the lite model will be integrated into a microcontroller to meet its limited resource requirements. To train and evaluate our model, we created a dataset that included child and adult voices of the keyword “open”. The system approach categorizes voices into age groups (child, adult) and then utilizes that categorization to grant access to a car. The robustness of the model was enhanced by adding a new class (recordings) to the dataset, which enabled our system to detect replay and synthetic voice attacks. If an adult voice is detected, access to start the car will be granted. However, if a child’s voice or a recording is detected, the system will display a warning message that educates the child about the dangers and consequences of the improper use of a car. Arduino Nano 33 BLE sensing was our embedded device of choice for integrating our trained, optimized model. Our system achieved an overall F1 score of 87.7% and 85.89% accuracy. LimitAccess detected replay and synthetic voice attacks with an 88% F1 score.</p>", "Keywords": "Age classification; Speech recognition; Embedded devices; Tiny machine learning; Embedded machine learning; Edge artificial intelligence", "DOI": "10.1007/s44163-023-00051-x", "PubYear": 2023, "Volume": "3", "Issue": "1", "JournalId": 91443, "JournalTitle": "Discover Artificial Intelligence", "ISSN": "", "EISSN": "2731-0809", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Qatar University, Doha, Qatar"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Qatar University, Doha, Qatar"}, {"AuthorId": 3, "Name": "K<PERSON>la Al-Janahi", "Affiliation": "Department of Computer Science and Engineering, Qatar University, Doha, Qatar"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CSIRO’s Data61, Sydney, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Qatar University, Doha, Qatar"}], "References": [{"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "TinyML Meets IoT: A Comprehensive Survey", "Authors": "Dr. <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "", "Page": "100461", "JournalTitle": "Internet of Things"}, {"Title": "Environmental Sound Classiﬁcation on the Edge: A Pipeline for Deep Acoustic Networks on Extremely Resource-Constrained Devices", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109025", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": *********, "Title": "A multi-attribute decision-making method for ternary hybrid decision matrices in view of ( <i>T, S</i> )-fuzzy rough sets with fuzzy preference relations", "Abstract": "", "Keywords": "", "DOI": "10.1080/0952813X.2023.2181404", "PubYear": 2024, "Volume": "36", "Issue": "8", "JournalId": 23221, "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence", "ISSN": "0952-813X", "EISSN": "1362-3079", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Liu", "Affiliation": "College of Mathematics and Computer Science, Zhejiang Normal University, Jinhua, Zhejiang, P.R. China"}], "References": [{"Title": "Uncertain multi-attribute group decision making based on linguistic-valued intuitionistic fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "293", "JournalTitle": "Information Sciences"}, {"Title": "Optimizing consistency and consensus improvement process for hesitant fuzzy linguistic preference relations and the application in group decision making", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "114", "JournalTitle": "Information Fusion"}, {"Title": "A neighborhood rough set model with nominal metric embedding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "373", "JournalTitle": "Information Sciences"}, {"Title": "Social network community analysis based large-scale group decision making approach with incomplete fuzzy preference relations", "Authors": "<PERSON><PERSON> Chu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "98", "JournalTitle": "Information Fusion"}, {"Title": "Group decision making based on acceptable multiplicative consistency of hesitant fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "524", "Issue": "", "Page": "77", "JournalTitle": "Information Sciences"}, {"Title": "Double-quantitative decision rough set over two universes and application to African swine fever decision-making", "Authors": "Xiaoyuan Hu; Bingzhen Sun; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "331", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "And-like-uninorm-based transitivity and analytic hierarchy process with interval-valued fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "375", "JournalTitle": "Information Sciences"}, {"Title": "Generalized trapezoidal hesitant fuzzy numbers and their applications to multi criteria decision-making problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "1017", "JournalTitle": "Soft Computing"}, {"Title": "Bonferroni mean operators of generalized trapezoidal hesitant fuzzy numbers and their application to decision-making problems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4925", "JournalTitle": "Soft Computing"}, {"Title": "Comprehensive minimum cost models for large scale group decision making with consistent fuzzy preference relations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "215", "Issue": "", "Page": "106780", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Probability granular distance-based fuzzy rough set model", "Authors": "<PERSON><PERSON>; Qing<PERSON> Hu; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107064", "JournalTitle": "Applied Soft Computing"}, {"Title": "Group decision making with hesitant fuzzy linguistic preference relations based on modified extent measurement", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114235", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Group decision making based on multiplicative consistency-and-consensus preference analysis for incomplete q-rung orthopair fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "653", "JournalTitle": "Information Sciences"}, {"Title": "The breaking of additively reciprocal property of fuzzy preference relations and its implication to decision making under uncertainty", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "92", "JournalTitle": "Information Sciences"}, {"Title": "Multiple attribute group decision making based on advanced linguistic intuitionistic fuzzy weighted averaging aggregation operator of linguistic intuitionistic fuzzy numbers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "587", "Issue": "", "Page": "813", "JournalTitle": "Information Sciences"}, {"Title": "Choquet integral-based intuitionistic fuzzy arithmetic aggregation operators in multi-criteria decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116242", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A new three-way multi-criteria decision-making method with fuzzy complementary preference relations based on additive consistency", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "592", "Issue": "", "Page": "277", "JournalTitle": "Information Sciences"}, {"Title": "Consensus checking and improving methods for AHP with q-rung dual hesitant fuzzy preference relations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "208", "Issue": "", "Page": "117902", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A two-stage EDM method based on KU-CBR with the incomplete linguistic intuitionistic fuzzy preference relations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "172", "Issue": "", "Page": "108552", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 106518949, "Title": "Feasibility, coverage, and inter-rater reliability of the assessment of therapeutic interaction by a humanoid robot providing arm rehabilitation to stroke survivors using the instrument THER-I-ACT", "Abstract": "<p> Objective: The instrument THERapy-related InterACTion (THER-I-ACT) was developed to document therapeutic interactions comprehensively in the human therapist–patient setting. Here, we investigate whether the instrument can also reliably be used to characterise therapeutic interactions when a digital system with a humanoid robot as a therapeutic assistant is used. </p><p> Methods: Participants and therapy: Seventeen stroke survivors receiving arm rehabilitation (i.e., arm basis training (ABT) for moderate-to-severe arm paresis [n = 9] or arm ability training (AAT) for mild arm paresis [n = 8]) using the digital therapy system E-BRAiN over a course of nine sessions. Analysis of the therapeutic interaction: A total of 34 therapy sessions were videotaped. All therapeutic interactions provided by the humanoid robot during the first and the last (9th) session of daily training were documented both in terms of their frequency and time used for that type of interaction using THER-I-ACT. Any additional therapeutic interaction spontaneously given by the supervising staff or a human helper providing physical assistance (ABT only) was also documented. All ratings were performed by two trained independent raters. </p><p> Statistical analyses: Intraclass correlation coefficients (ICCs) were calculated for the frequency of occurrence and time used for each category of interaction observed. </p><p> Results: Therapeutic interactions could comprehensively be documented and were observed across the dimensions provision of information, feedback, and bond-related interactions. ICCs for therapeutic interaction category assessments from 34 therapy sessions by two independent raters were high (ICC ≥0.90) for almost all categories of the therapeutic interaction observed, both for the occurrence frequency and time used for categories of therapeutic interactions, and both for the therapeutic interaction performed by the robot and, even though much less frequently observed, additional spontaneous therapeutic interactions by the supervisory staff and a helper being present. The ICC was similarly high for an overall subjective rating of the concentration and engagement of patients (0.87). </p><p> Conclusion: Therapeutic interactions can comprehensively and reliably be documented by trained raters using the instrument THER-I-ACT not only in the traditional patient–therapist setting, as previously shown, but also in a digital therapy setting with a humanoid robot as the therapeutic agent and for more complex therapeutic settings with more than one therapeutic agent being present. </p>", "Keywords": "arm;interaction;rater;rehabilitation;reliability;robot;social;stroke", "DOI": "10.3389/frobt.2023.1091283", "PubYear": 2023, "Volume": "10", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Neurorehabilitation research group, University Medical Centre, Greifswald, Germany. ;BDH-Klinik Greifswald, Institute for Neurorehabilitation and Evidence-Based Practice, An-Institut, University of Greifswald, Greifswald, Germany."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Neurorehabilitation research group, University Medical Centre, Greifswald, Germany."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Neurorehabilitation research group, University Medical Centre, Greifswald, Germany."}], "References": [{"Title": "Extended Interviews with Stroke Patients Over a Long-Term Rehabilitation Using Human–Robot or Human–Computer Interactions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "8", "Page": "1893", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 106519052, "Title": "A Novel Mobile Charging Planning Method Based on Swarm Reinforcement Learning in Wireless Sensor Networks", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSNET.2023.10054399", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Peng<PERSON>i Li", "Affiliation": ""}, {"AuthorId": 3, "Name": "Zengwei Lyu", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106519071, "Title": "Optional Verification and Signaling in Online Matching Markets: Evidence from a Randomized Field Experiment", "Abstract": "<p>Online matching platforms lack common informational mechanisms such as ratings and reviews that serve to reduce information asymmetry in transactional platforms. The lack of verified information about participants further exacerbates issues of information asymmetry in such markets. This study focuses on a novel role of verification in such matching markets—its ability to serve as a credible signal for a user when such verification is made optional and visible to other users. In collaboration with a leading online dating platform with no reputation mechanisms and where most of the information is self-disclosed, we design and conduct a randomized field experiment to examine not only who chooses to verify but also, the effectiveness of such optional verification for different types of users. Interestingly, we find that users on the two sides use the same signal very differently. Males act consistent with the conventional prediction of signaling, with high-type males being more likely to opt in to verification. As for females, we find that medium-type females are the most likely to opt in to verification as compared with high-type females. We also find that such differential opt-in decisions are related to the differences in the credibility of the existing key attribute of each side (viz., income for males and beauty for females). In examining the outcomes of verification, we find that verified users receive more contacts from higher-type users, with the high-type males and medium-type females benefitting the most. More interestingly, we find that verified users become more proactive and reach out to more and better potential partners. Further, the introduction of this voluntary verification facilitates desirable matching outcomes and benefits the platform as a whole. These findings have useful implications for research as well as practice.</p><p>History: <PERSON>, senior editor.</p><p>Supplemental Material: The online appendix is available at https://doi.org/10.1287/isre.2022.1194 .</p>", "Keywords": "two-sided matching markets; signaling; optional verification; randomized field experiment; deep learning", "DOI": "10.1287/isre.2022.1194", "PubYear": 2023, "Volume": "34", "Issue": "4", "JournalId": 11444, "JournalTitle": "Information Systems Research", "ISSN": "1047-7047", "EISSN": "1526-5536", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "McIntire School of Commerce, University of Virginia, Charlottesville, Virginia 22901"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> School of Business, University of Maryland, College Park, Maryland 20740"}], "References": [{"Title": "The Secret to Finding a Match: A Field Experiment on Choice Capacity Design in an Online Dating Platform", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "4", "Page": "1248", "JournalTitle": "Information Systems Research"}]}, {"ArticleId": 106519073, "Title": "Digital “x”—Charting a Path for Digital-Themed Research", "Abstract": "<p>As of late, the use of “digital” as a qualifier to established research concepts is increasing. However, we have yet to clearly establish what makes a “digital x” concept distinct from an “IT x” concept when “x” stands for well-established concepts such as strategy, infrastructure, innovation, or transformation, among others. In this paper, we review the need for, and merit of, labeling focal concepts in our field as “digital x” in contrast to using the dominant, incumbent label of “IT x.” We position the shift as a call to attend to new salient features that characterize contemporary settings of information technology use and its effects. Recognizing this need, we develop guidelines for future research by arguing what novel phenomena the label of “digital x” foregrounds and how insights gained through such foregrounding contribute to scholarship in ways that the term “IT x” does not. By doing so, this paper promotes clarity for the use of the digital x concepts and introduces explicit guidelines to delineate between the nascent stream of digital x research and established modes of IT x research. We hope that the essay helps information systems scholars and scholars in neighboring disciplines attending to digital phenomena identify novel research opportunities grounded on sound conceptual foundations that will foster cumulative generation of knowledge around digital x.</p><p>History: <PERSON> served as the Senior Editor and the Associate Editor for this article.</p><p>Supplemental Material: The e-companion is available at https://doi.org/10.1287/isre.2022.1186 .</p>", "Keywords": "digital x; IT x; digitalization; digitization; conceptual clarity; information systems (IS) theory; context shifts; guidelines", "DOI": "10.1287/isre.2022.1186", "PubYear": 2023, "Volume": "34", "Issue": "2", "JournalId": 11444, "JournalTitle": "Information Systems Research", "ISSN": "1047-7047", "EISSN": "1526-5536", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Digitalization Department, Copenhagen Business School, 2000 Frederiksberg, Denmark;;Queen’s University, Kingston, Ontario K7L 3N6, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Walton College of Business, University of Arkansas, Fayetteville, Arkansas 72701"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Weatherhead School of Management, Case Western Reserve University, Cleveland, Ohio 44106"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Center for Information Systems Research, Sloan School of Management, Massachusetts Institute of Technology, Cambridge, Massachusetts 02142"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Carlson School of Management, University of Minnesota, Minneapolis, Minnesota 55455"}], "References": [{"Title": "Digital transformation and the new logics of business process management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "3", "Page": "238", "JournalTitle": "European Journal of Information Systems"}, {"Title": "A paradoxical perspective on technology renewal in digital transformation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "1", "Page": "198", "JournalTitle": "Information Systems Journal"}]}, {"ArticleId": 106519088, "Title": "Hardware Implementations of LBlock and XXTEA Lightweight Block Ciphers for Resource-Constrained IoT application", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJHPSA.2023.10054395", "PubYear": 2023, "Volume": "11", "Issue": "3", "JournalId": 28011, "JournalTitle": "International Journal of High Performance Systems Architecture", "ISSN": "1751-6528", "EISSN": "1751-6536", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106519089, "Title": "Building Knowledge through Action: Considerations for Machine Learning in the Workplace", "Abstract": "<p>Innovations in machine learning are enabling organisational knowledge bases to be automatically generated from working people's activities. The potential for these to shift the ways in which knowledge is produced and shared raises questions about what types of knowledge might be inferred from working people's actions, how these can be used to support work, and what the broader ramifications of this might be. This article draws on findings from studies of (i) collaborative actions, and (ii) knowledge actions, to explore how these actions might (i) inform automatically generated knowledge bases, and (ii) be better supported through technological innovation. We triangulate findings to develop a framework of actions that are performed as part of everyday work, and use this to explore how mining those actions could result in knowledge being explicitly and implicitly contributed to a knowledge base. We draw on these possibilities to highlight implications and considerations for responsible design.</p>", "Keywords": "", "DOI": "10.1145/3584947", "PubYear": 2023, "Volume": "30", "Issue": "5", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Microsoft Research"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Microsoft"}], "References": [{"Title": "Machines as teammates: A research agenda on AI in team collaboration", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "2", "Page": "103174", "JournalTitle": "Information & Management"}, {"Title": "Bridging the Gap Between Ethics and Practice", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Interactive Intelligent Systems"}, {"Title": "Designing AI systems that make organizational knowledge actionable", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "6", "Page": "72", "JournalTitle": "interactions"}, {"Title": "The Design of Reciprocal Learning Between Human and Artificial Intelligence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 106519120, "Title": "A new method for writer identification based on historical documents", "Abstract": "<p>Identifying the writer of a handwritten document has remained an interesting pattern classification problem for document examiners, forensic experts, and paleographers. While mature identification systems have been developed for handwriting in contemporary documents, the problem remains challenging from the viewpoint of historical manuscripts. Design and development of expert systems that can identify the writer of a questioned manuscript or retrieve samples belonging to a given writer can greatly help the paleographers in their practices. In this context, the current study exploits the textural information in handwriting to characterize writer from historical documents. More specifically, we employ oBIF(oriented Basic Image Features) and hinge features and introduce a novel moment-based matching method to compare the feature vectors extracted from writing samples. Classification is based on minimization of a similarity criterion using the proposed moment distance. A comprehensive series of experiments using the International Conference on Document Analysis and Recognition 2017 historical writer identification dataset reported promising results and validated the ideas put forward in this study.</p>", "Keywords": "", "DOI": "10.1515/jisys-2022-0244", "PubYear": 2023, "Volume": "32", "Issue": "1", "JournalId": 7501, "JournalTitle": "Journal of Intelligent Systems", "ISSN": "0334-1860", "EISSN": "2191-026X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, <PERSON><PERSON><PERSON><PERSON>rbi Tebessi University, Route de Constantine 12002 Tébessa , Tebessa , Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, <PERSON><PERSON><PERSON><PERSON>rbi Tebessi University, Route de Constantine 12002 Tébessa , Tebessa , Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, <PERSON><PERSON><PERSON><PERSON>rbi Tebessi University, Route de Constantine 12002 Tébessa , Tebessa , Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, AI Enabling Technologies Research Center, Bahria University , Islamabad 44000 , Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of M’sila , M’sila 28000 , Algeria"}], "References": [{"Title": "An end-to-end deep learning system for medieval writer identification", "Authors": "N.D. Cilia; <PERSON><PERSON>; F. <PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "137", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "GR-RNN: Global-context residual recurrent neural networks for writer identification", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107975", "JournalTitle": "Pattern Recognition"}, {"Title": "Writer Identification using Deep Learning with FAST Keypoints and Harris corner detector", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115473", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A deep learning based system for writer identification in handwritten Arabic historical manuscripts", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "21", "Page": "30769", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106519203, "Title": "Low-frequency noise in gas sensors: A review", "Abstract": "Semiconductor-based gas sensors have been applied to a variety of applications, including environmental, safety, and health monitoring. Extensive efforts have been made to improve sensing performance outcomes, with the majority of these efforts focusing on the sensor response, sensitivity, and selectivity issues, mainly by optimizing the sensing materials and sensor structures. However, low-frequency noise (LFN), which has a considerable impact on the stability and reliability of sensors, has received far less attention in gas sensor research. In gas sensing applications, the noise in the sensing signal is determined by the LFN due to the slow reaction process. Thus, it is necessary to characterize the LFN in semiconductor-based gas sensors. This review article presents an overview of the LFN in semiconductor-based gas sensors. First, the history of LFN in gas sensor studies is explored briefly. Then, we discuss noise generation mechanisms in resistor-type, thin-film transistor-type, and horizontal floating-gate field-effect-transistor-type gas sensors. On the basis of this information, the signal-to-noise ratio, which determines the limit of detection, is examined, and the method to optimize the SNR in each sensor platform is discussed. Finally, LFN spectroscopy for selective gas detection is introduced, and its working principle is analyzed. This review article provides a foundation for understanding the LFN in semiconductor-based gas sensors and methods to control it based on application requirements.", "Keywords": "Limit of detection ; Low-frequency noise (LFN) ; LFN spectroscopy ; Semiconductor-based gas sensors ; Signal-to-noise ratio", "DOI": "10.1016/j.snb.2023.133551", "PubYear": 2023, "Volume": "383", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 5, "Name": "Jinwoo Park", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 7, "Name": "Kangwook Choi", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Inter-university Semiconductor Research Center, Seoul National University, Seoul 08826, Republic of Korea;Corresponding author"}], "References": [{"Title": "W18O49/Ti3C2Tx Mxene nanocomposites for highly sensitive acetone gas sensor with low detection limit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127274", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Methylbenzene sensors using Ti-doped NiO multiroom spheres: Versatile tunability on selectivity, response, sensitivity, and detection limit", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "308", "Issue": "", "Page": "127730", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Low frequency noise characteristics of resistor- and Si MOSFET-type gas sensors fabricated on the same Si wafer with In2O3 sensing layer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "318", "Issue": "", "Page": "128087", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Low frequency noise characteristics of resistor- and Si MOSFET-type gas sensors fabricated on the same Si wafer with In2O3 sensing layer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "318", "Issue": "", "Page": "128087", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Low frequency noise characteristics of resistor- and Si MOSFET-type gas sensors fabricated on the same Si wafer with In2O3 sensing layer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "318", "Issue": "", "Page": "128087", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A facile PDMS coating approach to room-temperature gas sensors with high humidity resistance and long-term stability", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "325", "Issue": "", "Page": "128810", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Enhanced acetone sensing properties of Pt@Al-doped ZnO core-shell nanoparticles", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129153", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Improved signal-to-noise-ratio of FET-type gas sensors using body bias control and embedded micro-heater", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129166", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "FET-type gas sensors: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "330", "Issue": "", "Page": "129240", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "MXene/SnO2 heterojunction based chemical gas sensors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>v", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129275", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Low-power and reliable gas sensing system based on recurrent neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "340", "Issue": "", "Page": "129258", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Bimetal Au-Pd decorated hierarchical WO3 nanowire bundles for gas sensing application", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "334", "Issue": "", "Page": "129584", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Selective ppb-level ozone gas sensor based on hierarchical branch-like In2O3 nanostructure", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "336", "Issue": "", "Page": "129612", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A low-power embedded poly-Si micro-heater for gas sensor platform based on a FET transducer and its application for NO2 sensing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "334", "Issue": "", "Page": "129642", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Noise spectroscopy data analysis-based gas identification with a single MOX sensor", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "334", "Issue": "", "Page": "129654", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Comparison of the characteristics of semiconductor gas sensors with different transducers fabricated on the same substrate", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "335", "Issue": "", "Page": "129661", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Comparison of the characteristics of semiconductor gas sensors with different transducers fabricated on the same substrate", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "335", "Issue": "", "Page": "129661", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Material dependent and temperature driven adsorption switching (p- to n- type) using CNT/ZnO composite-based chemiresistive methanol gas sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "336", "Issue": "", "Page": "129729", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Thin films of tungsten oxide materials for advanced gas sensors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "341", "Issue": "", "Page": "129996", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Core-double shell ZnO@In2O3@ZnO hollow microspheres for superior ethanol gas sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "341", "Issue": "", "Page": "130002", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Mesoporous WO3-TiO2 heterojunction for a hydrogen gas sensor", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "341", "Issue": "", "Page": "130035", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly stable Si MOSFET-type humidity sensor with ink-jet printed graphene quantum dots sensing layer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "343", "Issue": "", "Page": "130134", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Effects of IGZO film thickness on H2S gas sensing performance: Response, excessive recovery, low-frequency noise, and signal-to-noise ratio", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130148", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Effects of IGZO film thickness on H2S gas sensing performance: Response, excessive recovery, low-frequency noise, and signal-to-noise ratio", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130148", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Effect of mixing ratio on NO2 gas sensor response with SnO2-decorated carbon nanotube channels fabricated by one-step dielectrophoretic assembly", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "344", "Issue": "", "Page": "130257", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Sensitive H2 gas sensors based on SnO2 nanowires", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "345", "Issue": "", "Page": "130334", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Efficient fusion of spiking neural networks and FET-type gas sensors for a fast and reliable artificial olfactory system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "345", "Issue": "", "Page": "130419", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly efficient toluene gas sensor based on spinel structured hollow urchin-like core-shell ZnFe2O4 spheres", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "349", "Issue": "", "Page": "130734", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Gas sensor based on cobalt-doped 3D inverse opal SnO2 for air quality monitoring", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "350", "Issue": "", "Page": "130807", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Gas-sensing performance of In2O3@MoO3 hollow core-shell nanospheres prepared by a two-step hydrothermal method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "352", "Issue": "", "Page": "131007", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Coal mine gases sensors with dual selectivity at variable temperatures based on a W18O49 ultra-fine nanowires/Pd@Au bimetallic nanoparticles composite", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "354", "Issue": "", "Page": "131004", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Effects of UV light irradiation on fluctuation enhanced gas sensing by carbon nanotube networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "352", "Issue": "", "Page": "131069", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Techniques for wearable gas sensors fabrication", "Authors": "<PERSON>; Nanqing <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "353", "Issue": "", "Page": "131133", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Gold-loaded tellurium nanobelts gas sensor for ppt-level NO2 detection at room temperature", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "355", "Issue": "", "Page": "131300", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Optimization of channel structure and bias condition for signal-to-noise ratio improvement in Si-based FET-type gas sensor with horizontal floating-gate", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "357", "Issue": "", "Page": "131398", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Analysis of Cr/Au contact reliability in embedded poly-Si micro-heater for FET-type gas sensor", "Authors": "Jinwoo Park; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "360", "Issue": "", "Page": "131673", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Fully integrated FET-type gas sensor with optimized signal-to-noise ratio for H2S gas detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "367", "Issue": "", "Page": "132052", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Design optimization of FET-type gas sensor considering device characteristics, sensitivity, power, noise, and SNR", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "369", "Issue": "", "Page": "132257", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "PEI/PEG functionalized Black Phosphorus prepared by a One-Pot method for a wide detection range CO2 gas sensor", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "369", "Issue": "", "Page": "132303", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A novel design and fabrication of self-heated In2O3 nanowire gas sensor on glass for ethanol detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "345", "Issue": "", "Page": "113769", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": *********, "Title": "Energy Efficient Routing Scheme for Performance Enhancement of MANET Through Dominating Set", "Abstract": "<p>Mobile Ad-Hoc network is a distributed wireless network that is self-organized and self-maintained and it doesn’t require a fixed framework or central administration. Wireless nodes in a mobile ad hoc network are transient and have a short lifespan. They combine to form a self-configured, infrastructure-free network where routing is a significant difficulty. The wireless nodes in the network must synchronize in order to decide connectivity and routing. Ad hoc network synchronization includes activities including node organization, neighborhood detection, and routing. The predetermined framework resulted in an increase in network control overhead. If a node can link to every other node in the network, it is referred to as a dominating node, and a set of dominating nodes make up the dominating set. The main purpose of this article is to lessen routing overhead in the wireless network with the dominating set based routing (DSBR). In order to discover routes, shorten reroute establishment times, and increase packet delivery rates, this research presents an energy-efficient routing algorithm (EERA) that considerably increases the network performance. The effectiveness of this technique is demonstrated by the simulation analysis of the outcome.</p>", "Keywords": "MANET; Routing; Dominating Set; Nodes; routing overhead; EERA", "DOI": "10.3991/ijim.v17i04.37803", "PubYear": 2023, "Volume": "17", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of Data Science and Business Systems SRMIST, Kattankulathur, Chennai"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": " <PERSON><PERSON> <PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "An energy balance cluster network framework based on Simultaneous Wireless Information and Power Transfer", "Abstract": "Wireless NanoSensor Network (WNSN) is a brand-new type of sensor network with broad application prospects. In view of the limited energy of nano-nodes and unstable links in WNSNs, we propose an energy balance cluster network framework (EBCNF) based on Simultaneous Wireless Information and Power Transfer (SWIPT). The EBCNF framework extends the network lifetime of nano-nodes and uses a clustering algorithm called EBACC (an energy balance algorithm for intra-cluster and inter-cluster nodes) to make the energy consumption of nodes more uniform. Simulation shows that the EBCNF framework can make the network energy consumption more uniform, reduce the error rate of data transmission and the average network throughput, and can be used as an effective routing framework for WNSNs.", "Keywords": "Cooperative communication ; Routing protocol ; SWIPT ; WNSNs", "DOI": "10.1016/j.nancom.2023.100441", "PubYear": 2023, "Volume": "36", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electronic and Information Engineering, Tongji University, 201804, Shanghai, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Electronic and Information Engineering, Tongji University, 201804, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Electronic and Information Engineering, Tongji University, 201804, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic and Information Engineering, Tongji University, 201804, Shanghai, China"}], "References": []}, {"ArticleId": *********, "Title": "Open curriculum for teaching digital accessibility", "Abstract": "<p>In Ontario, Canada, universities are obligated under the Accessibility for Ontarians with Disabilities Act (AODA) to ensure that people with disabilities do not face barriers to education, and they are free from barriers in society more broadly. Those who produce online curriculum for postsecondary education in the province need at least a basic understanding of digital accessibility, and for some roles, like software or web developers, a level of expertise is required. However, finding people with the right knowledge, skills, and attitude can be difficult. This problem can be attributed to the fact that until recently digital accessibility skills have received little attention in post-secondary education. To address the issue, in 2015, with support from the Government of Ontario, we began several projects to develop digital accessibility curriculum. These efforts created a series of free Massive Open Online Courses (MOOCs) aimed at teaching digital accessibility skills to audiences ranging from office support workers, to managers, to developers, to digital accessibility specialists. The MOOCs ran between 2016 and 2019 and served more than 5000 participants, with more than 600 successfully completing the requirements for the digital badge(s) awarded. Following the MOOCs project, the content of the courses was converted into Open Educational Resources (OERs) that could be used as textbooks to support the introduction of digital accessibility topics over a range of subject areas, with encouragement for others to reuse the content to add accessibility related topics into their teaching. The OERs were downloaded more than 10,000 times between late 2020 and late 2022 and provided the base content for four open courses developed through OERU. In this article the pedagogy and curriculum for this digital accessibility training are described.</p>", "Keywords": "accessibility; Digital; Education; Postsecondary; Curriculum; Online; open access", "DOI": "10.3389/fcomp.2023.1113936", "PubYear": 2023, "Volume": "5", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The G<PERSON> School of Continuing Education, Toronto Metropolitan University, Canada"}], "References": []}, {"ArticleId": 106519478, "Title": "Visualization for Emotion Detection in Mobile-Based Land Monitoring Using User-Centred Design", "Abstract": "<p>Many colleges employ online learning as media learning where each piece of multimedia, be it sound, film, or text, can receive feedback from students. The lecture wants to know about the emotions that students experienced when they accessed the media, such as happiness, disappointment, or unhappiness, and instructors want to know how joyful they felt. This study built a tool cell for online media to use in identifying emotions in column comments. This paper focuses on user experience (UX) design, which is based on the Human-Centred Design method that prioritizes empathy for users and focuses on user demands to meet the discovery of user wants and develop high-fidelity prototypes. The results of the testing show that the average accuracy is 1.68%, the average recall is 1.55, the average precision is 1.45, and the average accuracy for the use of this phone website is 80% for emotion recognition based only on a column of comment in the internet media.</p>", "Keywords": "User-centred design; User experience; mobile; emotion detection; visualization; Styles", "DOI": "10.3991/ijim.v17i04.37805", "PubYear": 2023, "Volume": "17", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Assistant professor, Department of Networking and communications, SRM Institute of Science and Technology, India"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": " <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": " Nor Hissam <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106519555, "Title": "Anonymous and Distributed Authentication for Peer-to-Peer Networks", "Abstract": "Well-known authentication mechanisms such as Public-key Infrastructure (PKI) and Identity-based Public-key Certificates (ID-PKC) are not suitable for integration in a Peer-to-Peer (P2P) network environment, the reason being either the lack of or the difficulty in maintaining a centralized authority to manage the certificates. Authentication becomes even harder in anonymous environments. In this study, we present three authentication protocols such that the users can authenticate themselves in an anonymous P2P network, without revealing their identities. The first protocol uses existing ring signature schemes to obtain anonymous authentication, the second is an anonymous authentication protocol utilizing secret sharing schemes, and lastly a zero-knowledge-based anonymous authentication protocol. We provide security justifications for the three aforementioned protocols in terms of anonymity, completeness, soundness, resilience to impersonation attacks, and resilience to replay attacks. We also provide examples of conceptual topologies and how the peers would behave and rearrange in case of failure © 2023 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>. This open-access article is distributed under a Creative Commons Attribution (CC-BY) 4.0 license", "Keywords": "Anonymous authentication; Blockchains; Peer-to-peer networks; Ring signatures; Secret sharing; Zero-knowledge", "DOI": "10.3844/jcssp.2023.1.10", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Peradeniya, Sri Lanka"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Peradeniya, Sri Lanka"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Peradeniya, Sri Lanka"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Research and Innovation Centers Division, Faculty of Resilience, Rabdan Academy, United Arab Emirates; Institute for Integrated and Intelligent Systems, Griffith University, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Research and Innovation Centers Division, Faculty of Resilience, Rabdan Academy, United Arab Emirates; Institute for Integrated and Intelligent Systems, Griffith University, Australia"}], "References": []}, {"ArticleId": *********, "Title": "Discrete to Continuous Algorithm for Optimal Channel Selection to Detect Alcoholism through EEG Recordings", "Abstract": "Alcoholism is a serious public health issue, and early diagnosis of this brain disease can be performed by analyzing Electroencephalogram (EEG) signals. However, the high dimensionality of EEG datasets requires significant computational time and resources for the automatic processing of EEG signals. This study proposes a novel method to reduce EEG dataset dimensionality using a Discrete to Continuous algorithm (DtC) by selecting optimal EEG channels. The DtC approach compares alcoholic and nonalcoholic EEG signals as two-time series in a two-dimensional space based on a distance measurement between the two-time series. The Dynamic Time Warping (DTW) algorithm is used to compare the performance of the DtC approach. Classification performance metrics were evaluated for both the DtC and DTW algorithms. The optimal selected channels by our approach are the C3, CP5, PO7, and F8 channels with accuracy values of 100, 100, 94 and 81%, respectively. These findings are consistent with previous research on statistical analysis and machine learning methods and with the DTW algorithm results. Our findings are also in line with scientific evidence from clinical research. The DtC approach was efficient in selecting the best channels to reduce the EEG dataset dimensionality, allowing us to select four out of the 64 EEG channels (C3, CP5, PO7, and F8) that retain essential information related to alcoholism, which is useful in reducing computational time and resources during the classification task of alcoholic EEG. © 2023 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>", "Keywords": "Alcoholism; Biological Time Series; Discrete to Continuous; Electroencephalography; Optimal Channel Selection", "DOI": "10.3844/jcssp.2023.126.144", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ICES Team, ENSIAS College, Mohammed V University, Rabat, Morocco; National School of Public Health, Rabat, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ICES Team, ENSIAS College, Mohammed V University, Rabat, Morocco; National School of Public Health, Rabat, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "E2SN Research Team, ENSAM, Mohammed V University, Rabat, Morocco; Moroccan Society of Digital Health, Rabat, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "E2SN Research Team, ENSAM, Mohammed V University, Rabat, Morocco; Moroccan Society of Digital Health, Rabat, Morocco"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "E2SN Research Team, ENSAM, Mohammed V University, Rabat, Morocco"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Epidemiology and Public Health, Faculty of Medicine, Pharmacy and Dental Medicine, University Sidi Mohammed <PERSON>, Fez, Morocco"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ICES Team, ENSIAS College, Mohammed V University, Rabat, Morocco"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ICES Team, ENSIAS College, Mohammed V University, Rabat, Morocco; Department of Economics, Faculty of Law Economics and Social Sciences-Souissi, Mohammed V University, Rabat, Morocco"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ICES Team, ENSIAS College, Mohammed V University, Rabat, Morocco; Department of Economics, Faculty of Law Economics and Social Sciences-Souissi, Mohammed V University, Rabat, Morocco"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Moroccan Society of Digital Health, Rabat, Morocco; National Center for Scientific and Technical Research (CNRST), Rabat, Morocco"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Moroccan Society of Digital Health, Rabat, Morocco; National Center for Scientific and Technical Research (CNRST), Rabat, Morocco"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "Moroccan Society of Digital Health, Rabat, Morocco; National Center for Scientific and Technical Research (CNRST), Rabat, Morocco"}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "Moroccan Society of Digital Health, Rabat, Morocco; National Center for Scientific and Technical Research (CNRST), Rabat, Morocco"}], "References": []}, {"ArticleId": 106519644, "Title": "A federated learning-enabled predictive analysis to forecast stock market trends", "Abstract": "This article proposes a federated learning framework to build Random Forest, Support Vector Machine, and Linear Regression models for stock market prediction. The performance of the federated learning is compared against centralised and decentralised learning frameworks to figure out the best fitting approach for stock market prediction. According to the results, federated learning outperforms both centralised and decentralised frameworks in terms of Mean Square Error if Random Forest (MSE = 0.021) and Support Vector Machine techniques (MSE = 37.596) are used, while centralised learning (MSE = 0.011) outperforms federated and decentralised frameworks if a linear regression model is used. Moreover, federated learning gives a better model training delay as compared to the benchmarks if Linear Regression (time = 9.7 s) and Random Forest models (time = 515 s) are used, whereas decentralised learning gives a minimised model training delay (time = 3847 s) for Support Vector Machine.", "Keywords": "Federated learning; Decentralised learning; Centralised learning; Stock market trend prediction", "DOI": "10.1007/s12652-023-04570-4", "PubYear": 2023, "Volume": "14", "Issue": "4", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Ardakan<PERSON>", "Affiliation": "School of Computer Science, University of Lincoln, Lincoln, UK; School of Computer Science, University of Nottingham Ningbo China, Ningbo, China"}, {"AuthorId": 2, "Name": "Nanjiang Du", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Ningbo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Ningbo, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Ningbo, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Ningbo, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Ningbo, China"}], "References": [{"Title": "An innovative neural network approach for stock market prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "2098", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Short-term stock market price trend prediction using a comprehensive deep learning system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "66", "JournalTitle": "Journal of Big Data"}, {"Title": "Social Media and Stock Market Prediction: A Big Data Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "2569", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Federated Random Forests can improve local performance of predictive models for various healthcare applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "8", "Page": "2278", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 106519666, "Title": "Comparison of machine learning techniques for spam detection", "Abstract": "<p>Email is a useful communication medium for better reach. There are two types of emails, those are ham or legitimate email and spam email. Spam is a kind of bulk or unsolicited email that contains an advertisement, phishing website link, malware, Trojan, etc. This research aims to classify spam emails using machine learning classifiers and evaluate the performance of classifiers. In the pre-processing step, the dataset has been analyzed in terms of attributes and instances. In the next step, thirteen machine learning classifiers are implemented for performing classification. Those classifiers are Adaptive Booster, Artificial Neural Network, Bootstrap Aggregating, Decision Table, Decision Tree, J48, K-Nearest Neighbor, Linear Regression, Logistic Regression, Naïve Bayes, Random Forest, Sequential Minimal Optimization and, Support Vector Machine. In terms of accuracy, the Random Forest classifier performs best and the performance of the Naïve Bayes classifier is substandard compared to the rest of the classifiers. Random Forest classifier had the accuracy of 99.91% and 99.93% for the Spam Corpus and Spambase datasets respectively. The naïve Bayes classifier had the accuracy of 87.63% and 79.53% for the Spam Corpus and Spambase datasets respectively.</p>", "Keywords": "Machine learning algorithms; Classification; Spam email detection; Machine learning; Artificial intelligence", "DOI": "10.1007/s11042-023-14689-3", "PubYear": 2023, "Volume": "82", "Issue": "19", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational Logistics, Alagappa University, Karaikudi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational Logistics, Alagappa University, Karaikudi, India"}], "References": [{"Title": "A semantic-based classification approach for an enhanced spam detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "101716", "JournalTitle": "Computers & Security"}, {"Title": "A linear multivariate binary decision tree classifier based on K-means splitting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107521", "JournalTitle": "Pattern Recognition"}, {"Title": "Electrical load forecasting: A deep learning approach based on K-nearest neighbors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Tonglin Fu", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106900", "JournalTitle": "Applied Soft Computing"}, {"Title": "Optimization of decision trees using modified African buffalo algorithm", "Authors": "<PERSON><PERSON>; Dharmpal D<PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "4763", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Comparative analysis of credit card fraud detection in Simulated Annealing trained Artificial Neural Network and Hierarchical Temporal Memory", "Authors": "<PERSON><PERSON><PERSON><PERSON>; E.F. <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "", "Page": "100080", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Evaluating the Effectiveness of Machine Learning Methods for Spam Detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "190", "Issue": "", "Page": "479", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 106519681, "Title": "Recovering Clean Data with Low Rank Structure by Leveraging Pre-learned Dictionary for Structured Noise", "Abstract": "<p>In recent years, a series of methods have been proposed to recover clean data with low rank structure from noisy data, double robust principle component analysis (DRPCA) and the method of dictionary learning with structured noise (DLSN), to name a few. However, existing methods always fail to consider the dependence of reconstruction error on low rank representation, characterize the local geometric structure and precisely depict noise simultaneously. To tackle this problem, motivated by previous works especially DRPCA and DLSN, we put forward a low rank recovery method with pre-learned dictionaries for structured noise. In more detail, this work first model the real data as the mix of clean data, structured noise with its own dictionary and the residual. Low rank techniques is utilized to build the bridge between low-rank representation and reconstruction errors and the bridge is further incorporated into the objective function of the RPCA model, thus forming a new optimization model. Then the Alternating Direction Method of Multipliers (ADMM) is employed to solve solvable sub-problems of the original optimization model in parallel, which is named low rank recovery algorithm with structural interference (LRPD). Theoretically, LRPD benefits from the pre-learned dictionaries as it could characterize the data structure better even with the small sample size, and the learned mapping of the observation matrix since it could be directly used to solve out-of-sample problem. Finally, comparisons against various representative clean data recovery methods are conducted on multiple data sets and confirm the superiority of LRPD in recovering clean data with low rank structure from noisy data.</p>", "Keywords": "Low rank recovery; Sparse representation; Pre-learned dictionaries; Structured noise", "DOI": "10.1007/s11063-023-11164-w", "PubYear": 2023, "Volume": "55", "Issue": "5", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Shanghai University, Shanghai, People’s Republic of China"}], "References": [{"Title": "Singing voice separation with pre-learned dictionary and reconstructed voice spectrogram", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "3311", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Double robust principal component analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "391", "Issue": "", "Page": "119", "JournalTitle": "Neurocomputing"}, {"Title": "Coupled low rank representation and subspace clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "1", "Page": "530", "JournalTitle": "Applied Intelligence"}, {"Title": "Multiview Common Subspace Clustering via Coupled Low Rank Representation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 106519684, "Title": "New information search model for online reviews with the perspective of user requirements", "Abstract": "<p>Many e-commerce websites currently provide online reviews to share e-shoppers’ experience with the products. To help e-shoppers obtaining information efficiently, these websites usually summarize product information based on their certain predefined aspects. However, e-shopper’s aspects should be annotated to make sure that more highly related information of online reviews can be fetched for fulfilling e-shopper’s requirements. Hence, this study integrates an annotation approach with similarity techniques (Keyword pair similarity and Aspect-sentence similarity) to propose a new framework to fetch more highly correlated sentences for e-shoppers. Experimental results show that most of the combinations in the proposed approach have high prediction performance in the Top 10 sentences with Precision (0.90 or higher).</p>", "Keywords": "E-commerce; Online review; Keyword annotation; Google distance; WordNet", "DOI": "10.1007/s11042-023-14847-7", "PubYear": 2023, "Volume": "82", "Issue": "18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, National Chin-Yi University of Technology, Taichung, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, National Chung Cheng University, Chia-Yi, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, National Central University, Taoyuan City, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, National Central University, Taoyuan City, Taiwan"}], "References": [{"Title": "Multi Clustering Recommendation System for Fashion Retail", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "7", "Page": "9989", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106519686, "Title": "Learning Weight Signed Network Embedding with Graph Neural Networks", "Abstract": "<p>Network embedding aims to map nodes in a network to low-dimensional vector representations. Graph neural networks (GNNs) have received much attention and have achieved state-of-the-art performance in learning node representation. Using fundamental sociological theories (status theory and balance theory) to model signed networks, basing GNN on learning node embedding has become a hot topic in signed network embedding. However, most GNNs fail to use edge weight information in signed networks, and most models cannot be directly used in weighted signed networks. We propose a novel signed directed graph neural networks model named WSNN to learn node embedding for Weighted signed networks. The proposed model reconstructs link signs, link directions, and signed directed triangles simultaneously. Based on the network representations learned by the proposed model, we conduct link sign prediction in signed networks. Extensive experimental results in real-world datasets demonstrate the superiority of the proposed model over the state-of-the-art network embedding algorithms for graph representation learning in signed networks.</p>", "Keywords": "Network embedding; Graph neural networks; Sociological theories; Weighted signed networks; Link prediction", "DOI": "10.1007/s41019-023-00206-x", "PubYear": 2023, "Volume": "8", "Issue": "1", "JournalId": 15887, "JournalTitle": "Data Science and Engineering", "ISSN": "2364-1185", "EISSN": "2364-1541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The College of Computer Science and Engineering, North Minzu University, Yinchuan, China"}, {"AuthorId": 2, "Name": "Qiancheng Yu", "Affiliation": "The College of Computer Science and Engineering, North Minzu University, Yinchuan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The College of Computer Science and Engineering, North Minzu University, Yinchuan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The College of Computer Science and Engineering, North Minzu University, Yinchuan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The College of Computer Science and Engineering, North Minzu University, Yinchuan, China"}], "References": [{"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}]}, {"ArticleId": 106519759, "Title": "Magnetic coupling governed pinning directions in magnetic tunnel junctions under magnetic field annealing with zero magnetic field cooling", "Abstract": "<p>This study demonstrates the manipulation of the pinning direction in MTJs with varying tIrMn under the zero magnetic field cooling process, which results from the competition between the exchange bias coupling and interlayer coupling through the SAF layer. Our results clarify the coupling mechanism for determining the pinning direction of MTJs under the zero magnetic field cooling treatment, which can guide the design scheme of full Wheatstone bridge-based TMR sensors.</p>", "Keywords": "", "DOI": "10.1007/s11432-021-3467-5", "PubYear": 2023, "Volume": "66", "Issue": "4", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics, State Key Laboratory of Crystal Materials, Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "Shaohua Yan", "Affiliation": "Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Lu", "Affiliation": "Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics, State Key Laboratory of Crystal Materials, Shandong University, Jinan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics, State Key Laboratory of Crystal Materials, Shandong University, Jinan, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics, State Key Laboratory of Crystal Materials, Shandong University, Jinan, China; Fert Beijing Institute, MIIT Key Laboratory of Spintronics, School of Integrated Circuit Science and Engineering, Beihang University, Beijing, China"}], "References": [{"Title": "Tuning the pinning direction of giant magnetoresistive sensor by post annealing process", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "6", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 106519791, "Title": "Policy-Based Holistic Application Management with BPMN and TOSCA", "Abstract": "<p>With the wide adoption of cloud computing across technology industries and research institutions, an ever-growing interest in cloud orchestration frameworks has emerged over the past few years. These orchestration frameworks enable the automated provisioning and decommissioning of cloud applications in a timely and efficient manner, but they offer limited or no support for application management. While management functionalities, such as configuring, monitoring and scaling single components, can be directly covered by cloud providers and configuration management tools, holistic management features, such as backing up, testing and updating multiple components, cannot be automated using these approaches. In this paper, we propose a concept to automatically generate executable holistic management workflows based on the TOSCA standard. The practical feasibility of the approach is validated through a prototype implementation and a case study.</p><p>© The Author(s) 2023.</p>", "Keywords": "BPMN;Cloud management automation;Holistic application management;Policies;TOSCA", "DOI": "10.1007/s42979-022-01616-w", "PubYear": 2023, "Volume": "4", "Issue": "3", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical, Electronic and Computer Engineering, University of Catania, V.le A. Doria 6, 95125 Catania, Italy."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, Electronic and Computer Engineering, University of Catania, V.le A. Doria 6, 95125 Catania, Italy."}], "References": [{"Title": "Freezing and defrosting cloud applications: automated saving and restoring of running applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "1-2", "Page": "101", "JournalTitle": "SICS Software-Intensive Cyber-Physical Systems"}, {"Title": "A Survey of DevOps Concepts and Challenges", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Describing and Processing Topology and Quality of Service Parameters of Applications in the Cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "4", "Page": "761", "JournalTitle": "Journal of Grid Computing"}, {"Title": "Cloud resource orchestration in the multi-cloud landscape: a systematic review of existing frameworks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "TORCH: a TOSCA-Based Orchestrator of Multi-Cloud Containerised Applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Grid Computing"}, {"Title": "SODALITE@RT: Orchestrating Applications on Cloud-Edge Infrastructures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Grid Computing"}]}, {"ArticleId": 106519878, "Title": "A Long Short-Term Memory-Based Interconnected Architecture for Classification of Grasp Types Using Surface-Electromyography Signals", "Abstract": "Reliable classification of grasp types from human limbs has become an important aspect used by applications with humanoid robotic systems, because of their high-accuracy implementations in human movement replication and detection. Biomedical features extracted from muscular signals are commonly used for this purpose, however, their extraction and usage have been targeted independently, with time series features not even considered in the classification stage. Recently, studies show deep neural networks could obtain the signal's features in their internal architecture and use them directly over a classification task, avoiding all preprocessing steps and improving the obtained accuracy. Therefore, the current study proposes a deep architecture based on long short-term memory networks for the classification of six grasp types as an end-to-end deep model approach, working with raw surface electromyography signals. Classification accuracy of 99.12% was obtained and compared with previous studies which use different machine learning techniques over the same dataset. Results obtained showed that our model's architecture improves previous results as well as provides a robust solution avoiding overfitting, with an F1-score higher than 99% for all grasp types.", "Keywords": "", "DOI": "10.1109/TAI.2023.3244177", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Biomedical Engineering, University of Saskatchewan, SK, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Division of Biomedical Engineering, University of Saskatchewan, SK, Canada"}], "References": [{"Title": "Thumbs up, thumbs down: non-verbal human-robot interaction through real-time EMG classification via inductive and supervised transductive transfer learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "12", "Page": "6021", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Surface EMG signal classification using TQWT, Bagging and Boosting for hand movement recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "7", "Page": "3539", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Hilbert sEMG data scanning for hand gesture recognition based on deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2645", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Efficient deep neural network model for classification of grasp types using sEMG signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "9", "Page": "4437", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Deep learning for single-lead ECG beat arrhythmia-type detection using novel iris spectrogram representation", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "3", "Page": "1123", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 106519942, "Title": "Numerical and experimental investigation of the ultrasonic vibration effects on the tube hydroforming process in a die with a square cross-section", "Abstract": "<p>The use of tube hydroforming process to produce integrated parts is growing in various industries. In this research, the hydroforming process has been used to convert the circular cross-section of the tube into a square one. In this process, due to the high hydrostatic pressure of the fluid, the friction in the contact area between the tube and the die surface increases significantly. High friction prevents the metal flowing of the tube material on the die surface, and therefore it becomes very difficult to completely form the tube inside the die and obtain sharp corners. In this research, in order to improve the tube formability, applying ultrasonic vibrations to the hydroforming die has been used, which causes a temporary gap to be created in the contact surface of the tube and the die, and therefore the amount of friction is reduced and the tube material can slide more easily. By developing a 3D finite element model, the ultrasonic tube hydroforming process was evaluated. Modal analysis was used to evaluate the different shape modes of the die. The effects of ultrasonic vibrations on the deformation process have been evaluated using two variables: corner radius of square die and average wall thickness. An ultrasonic hydroforming setup was designed to form the annealed copper tube and was stimulated using selected resonance frequencies. The results of the finite element model were validated with the deformed tube in the experimental test. After confirming the results, the numerical model was used to evaluate the process parameters.</p>", "Keywords": "Tube hydroforming process; Square die; Ultrasonic vibrations; Modal analysis; Sharp corner", "DOI": "10.1007/s00170-023-11081-1", "PubYear": 2023, "Volume": "126", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Birjand University of Technology, Birjand, Iran; Birjand, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Birjand University of Technology, Birjand, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Birjand University of Technology, Birjand, Iran"}], "References": [{"Title": "Effect of resonant frequency variation on the ultrasonically assisted deep drawing process: numerical and experimental study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2243", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 106519953, "Title": "Multi-view metro station clustering based on passenger flows: a functional data-edged network community detection approach", "Abstract": "<p>This paper aims at metro station clustering based on passenger flow data. Compared with existing clustering methods that only use boarding or alighting data of each station separately, we focus on higher granularity origin-destination (O-D) path flow data, and provide more flexible and insightful clustering results. In particular, we regard the metro system as a network, with each station as a node. The real-time passenger flows over time between different O-D paths serve as directed edges between nodes. Compared with traditional networks, our edges are temporal curves, and can be regarded as functional data. For this functional data-edged graph, we are the first to develop a novel community detection approach for node clustering. Our method is based on functional factorization. First a dual time-warped sparse nonnegative functional factorization is proposed for extracting patterns of the functional edges. Then the passenger flow of each O-D path can be regarded as a linear combination of different extracted passenger flow patterns. Based on it, we construct a multi-view directed and weighted network, where each view represents one particular pattern, and the factorization coefficient of each O-D path on this pattern is treated as the weight of this directed edge in this particular view. Then a novel community detection algorithm based on nonnegative matrix tri-factorization is constructed according to the topological structure of the multi-view network. The fusion of different views can be either subjectively determined or objectively learnt in a data-driven way, which gives flexibility of the clustering algorithm to emphasize on different travel patterns. Two real datasets of Singapore and Hong Kong metro systems are used to validate the proposed method. </p>", "Keywords": "Functional data-edged network; Multi-view network community detection; Nonnegative functional factorization; Passenger flow pattern extraction; Station clustering; Smart card data", "DOI": "10.1007/s10618-023-00916-w", "PubYear": 2023, "Volume": "37", "Issue": "3", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zheng", "Affiliation": "School of Computing and Information Systems, Singapore Management University, Singapore, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering and Decision Analytics, Hong Kong University of Science and Technology, Clear Water Bay, Hong Kong"}], "References": [{"Title": "A survey of community detection methods in multilayer networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "1", "Page": "1", "JournalTitle": "Data Mining and Knowledge Discovery"}]}, {"ArticleId": 106520309, "Title": "Multi-objective two-stage emergent blood transshipment-allocation in COVID-19 epidemic", "Abstract": "The problem of blood transshipment and allocation in the context of the COVID-19 epidemic has many new characteristics, such as two-stage, trans-regional, and multi-modal transportation. Considering these new characteristics, we propose a novel multi-objective optimization model for the two-stage emergent blood transshipment-allocation. The objectives considered are to optimize the quality of transshipped blood, the satisfaction of blood demand, and the overall cost including shortage penalty. An improved integer encoded hybrid multi-objective whale optimization algorithm (MOWOA) with greedy rules is then designed to solve the model. Numerical experiments demonstrate that our two-stage model is superior to one-stage optimization methods on all objectives. The degree of improvement ranges from 0.69 to 66.26%.", "Keywords": "Blood shortage;Blood supply chain;Blood transshipment;COVID-19;MOWOA", "DOI": "10.1007/s40747-023-00976-x", "PubYear": 2023, "Volume": "9", "Issue": "5", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhou", "Affiliation": "Research Center for Economy of Upper Reaches of the Yangtze River, Chongqing Technology and Business University, Chongqing, 400067 China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Chongqing Technology and Business University, Chongqing, 400067 China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Guangzhou University, Guangzhou, 510006 China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics, Sunway University, 47500 Selangor Darul Ehsan, Malaysia."}], "References": [{"Title": "Modeling and optimization of a reliable blood supply chain network in crisis considering blood compatibility using MOGWO", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12173", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Research on intelligent workshop resource scheduling method based on improved NSGA-II algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "71", "Issue": "", "Page": "102141", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Blood supply chain operation considering lifetime and transshipment under uncertain environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107364", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel capacity sharing mechanism to collaborative activities in the blood collection process during the COVID-19 outbreak", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107821", "JournalTitle": "Applied Soft Computing"}, {"Title": "Investigating the conflicts between different stakeholders’ preferences in a blood supply chain at emergencies: a trade-off between six objectives", "Authors": "<PERSON><PERSON>; M. A. S. <PERSON>; S. M<PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "21", "Page": "13389", "JournalTitle": "Soft Computing"}, {"Title": "Optimization of energy-efficient open shop scheduling with an adaptive multi-objective differential evolution algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "", "Page": "108459", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 106520313, "Title": "MLFFCSP: a new anti-occlusion pedestrian detection network with multi-level feature fusion for small targets", "Abstract": "<p>Pedestrian detection relying on deep convolution neural networks has achieved significant progress. However, the performance of current pedestrian detection algorithms remains unsatisfactory when it comes to small targets or heavily occluded pedestrians. In this paper, a new anti-occlusion video pedestrian detection network with multi-level feature fusion named MLFFCSP is proposed for small targets and heavily occluded pedestrians. In the proposed network, the pyramid convolutional neural network PyConvResNet101 is used as backbone to extract features. Then, the shallow and deep features are fused at multiple levels to fully obtain the shallow location information and deep semantic information. In order to improve the robustness of the model, data augmentation is also implemented via random erasing on the training data. Experiments are carried out on Caltech and Citypersons datasets, and the log-average miss rate is used to evaluate the performance of the model. The results show that the performance of MLFFCSP is better than other pedestrian detection algorithms in the case of small targets and serious occlusion.</p>", "Keywords": "Pedestrian detection; Multi-level; Feature; Fusion; Anti-occlusion; Small targets", "DOI": "10.1007/s11042-023-14721-6", "PubYear": 2023, "Volume": "82", "Issue": "19", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Zhejiang, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Zhejiang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Zhejiang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Zhejiang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Zhejiang, China"}], "References": [{"Title": "CornerNet: Detecting Objects as Paired Keypoints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "3", "Page": "642", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "PSC-Net: learning part spatial co-occurrence for occluded pedestrian detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "2", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Self-Learning Network-based segmentation for real-time brain M.R. images through HARIS", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Vale<PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 106520316, "Title": "CEACAM7 expression contributes to early events of pancreatic cancer", "Abstract": "<b  >Background</b> The trends of pancreatic cancer (PanCa) incidence and mortality are on rising pattern, and it will be a second leading cause of cancer related deaths by 2030. Pancreatic ductal adenocarcinoma (PDAC), major form of PanCa, exhibits a grim prognosis as mortality rate is very close to the incidence rate, due to lack of early detection methods and effective therapeutic regimen. Considering this alarming unmet clinic need, our team has identified a novel oncogenic protein, carcinoembryonic antigen-related cell adhesion molecule 7 (CEACAM7), that can be useful for spotting early events of PDAC. <b  >Methodology</b> This study includes bioinformatics pre-screening using publicly available cancer databases followed by molecular biology techniques in PDAC progressive cell line panel and human tissues to evaluate CEACAM7 expression in early events of pancreatic cancer. <b  >Results</b> PanCa gene and protein expression analysis demonstrated the significantly higher expression of CEACAM7 in PDAC, compared to other cancers and normal pancreas. Overall survival analysis demonstrated an association between the higher expression of CEACAM7 and poor patients’ prognosis with high hazard ratio. Additionally, in a performance comparison analysis CEACAM7 outperformed S100A4 in relation to PDAC. We also observed an increase of CEACAM7 in PDAC cell line panel model. However, poorly differentiated, and normal cell lines did not show any expression. Human tissue analysis also strengthened our data by showing strong and positive IHC staining in early-stage tumors. <b  >Conclusion</b> Our observations clearly cite that CEACAM7 can serve as a potential early diagnostic and/or prognostic marker of PDAC and may also potentiate the sensitivity of the existing biomarker panel of PDAC. However, further studies are warranted to determine its clinical significance.", "Keywords": "CEACAM7;Early detection biomarker;PDAC;Pancreatic cancer;Tumor grading & Bioinformatics", "DOI": "10.1016/j.jare.2023.02.013", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; Himalayan School of Biosciences and Cancer Research Institute, Himalayan Institute of Medical Sciences, Swami Rama Himalayan University, Dehradun, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Research and Scientific Studies Unit, College of Nursing and Allied Health Sciences, Jazan University, Jazan, Saudi Arabia; Bursa Uludağ University Faculty of Medicine, Nilüfer, Bursa, Turkey."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Immunology and Microbiology, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA; South Texas Center of Excellence in Cancer Research, School of Medicine, University of Texas Rio Grande Valley, McAllen, USA. Electronic address:  ."}], "References": []}, {"ArticleId": 106520448, "Title": "Value Co-creation in Virtual Game Communities: A Perspective on Social Influence Theory", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJNVO.2022.10054393", "PubYear": 2022, "Volume": "28", "Issue": "4", "JournalId": 14131, "JournalTitle": "International Journal of Networking and Virtual Organisations", "ISSN": "1470-9503", "EISSN": "1741-5225", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON> chu <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106520534, "Title": "Stiffened Gas Approximation and GRP Resolution for Compressible Fluid Flows of Real Materials", "Abstract": "<p>The equation of state (EOS) embodies thermodynamic properties of compressible fluid materials and usually has very complicated forms in real engineering applications. The complexity of EOS in form gives rise to the difficulty in analyzing relevant wave patterns and in designing efficient numerical algorithms. In this paper, a strategy of local stiffened gas approximation is proposed for computing compressible fluid flows of real materials. The stiffened gas EOS is used to approximate general EOSs locally with certain thermodynamic compatibility at each interface of computational control volumes so that the exact Riemann solver can be significantly simplified and the computational cost of the resulting scheme is reduced up to two orders of magnitude. Then the generalized Riemann problem solver is further adopted not only to increase the accuracy and resolution but also to effectively reflect the thermodynamic effect through the inclusion of entropy variation into the numerical fluxes. The resulting scheme is demonstrated to be efficient and robust through typical numerical examples which display the excellent performance of such an approximation under extreme thermodynamics.</p>", "Keywords": "Compressible fluid flows; Real materials; Equation of state; Riemann solver; GRP solver; Stiffened gas approximation; 76M12; 76N15; 65M08", "DOI": "10.1007/s10915-023-02140-6", "PubYear": 2023, "Volume": "95", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Applied Physics and Computational Mathematics, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Academy of Multidisciplinary Studies, Capital Normal University, Beijing, People’s Republic of China; State Key Laboratory for Turbulence Research and Complex System, Peking University, Beijing, People’s Republic of China"}], "References": []}, {"ArticleId": 106520564, "Title": "Optimal web page classification technique using artificial neural network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAIP.2023.10054365", "PubYear": 2023, "Volume": "24", "Issue": "3/4", "JournalId": 10510, "JournalTitle": "International Journal of Advanced Intelligence Paradigms", "ISSN": "1755-0386", "EISSN": "1755-0394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106520724, "Title": "MAA: multi-objective artificial algae algorithm for workflow scheduling in heterogeneous fog-cloud environment", "Abstract": "<p>With the development of current computing technology, workflow applications have become more important in a variety of fields, including research, education, health care, and scientific experimentation. A group of tasks with complicated dependency relationships constitute the workflow applications. It can be difficult to create an acceptable execution sequence while maintaining precedence constraints. Workflow scheduling algorithms (WSA) are gaining more attention from researchers as a real-time concern. Even though a variety of research perspectives have been demonstrated for WSAs, it remains challenging to develop a single coherent algorithm that simultaneously meets a variety of criteria. There is very less research available on WSA in the heterogeneous computing system. Classical scheduling techniques, evolutionary optimisation algorithms, and other methodologies are the available solution to this problem. The workflow scheduling problem is regarded as NP-complete. This problem is constrained by various factors, such as Quality of Service, interdependence between tasks, and user deadlines. In this paper, an efficient meta-heuristic approach named Multi-objective Artificial Algae (MAA) algorithm is presented for scheduling scientific workflows in a hierarchical fog-cloud environment. In the first phase, the algorithm pre-processes scientific workflow and prepares two task lists. In order to speed up execution, bottleneck tasks are executed with high priority. The MAA algorithm is used to schedule tasks in the following stage to reduce execution times, energy consumption and overall costs. In order to effectively use fog resources, the algorithm also utilises the weighted sum-based multi-objective function. The proposed approach is evaluated using five benchmark scientific workflow datasets. To verify the performance, the proposed algorithm's results are compared to those of conventional and specialised WSAs. In comparison to previous methodologies, the average results demonstrate significant improvements of about 43% in execution time, 28% in energy consumption and 10% in total cost without any trade-offs.</p>", "Keywords": "Heterogeneous computing system; Workflow scheduling; Artificial algae algorithm; MAA algorithm", "DOI": "10.1007/s11227-023-05110-9", "PubYear": 2023, "Volume": "79", "Issue": "10", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, National Institute of Technology, Raipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, National Institute of Technology, Raipur, India"}], "References": [{"Title": "A Survey on Scheduling Strategies for Workflows in Cloud Environment and Emerging Trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A hybrid meta-heuristic algorithm for scientific workflow scheduling in heterogeneous distributed computing systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103501", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Efficient scientific workflow scheduling for deadline-constrained parallel tasks in cloud computing environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "531", "Issue": "", "Page": "31", "JournalTitle": "Information Sciences"}, {"Title": "Hybrid scheduling for scientific workflows on hybrid clouds", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "181", "Issue": "", "Page": "107438", "JournalTitle": "Computer Networks"}, {"Title": "HPSOGWO: A Hybrid Algorithm for Scientific Workflow Scheduling in Cloud Computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "10", "Page": "626", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "A bi-objective task scheduling approach in fog computing using hybrid fireworks algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "4236", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 106520808, "Title": "Examining the effect of regulatory factors on avoiding online blackmail threats on social media: A structural equation modeling approach", "Abstract": "Individuals and organizations rely on social media to share files and knowledge. On the other hand, criminals find social media applications a rich place to attack through techniques like phishing, fraud, and blackmail. While the rate of blackmail is growing, there is a lack of literature on the subject. This study aims to examine the impact of information security regulation factors such as information security awareness, policies, and culture towards avoiding online blackmail threats. The study proposes a model constructed from technology threat avoidance theory (TTAT), the theory of planned behavior (TPB), and organizational control (OC). A quantitative approach based on an online survey was conducted to collect data from 547 employees and students from different industrial backgrounds. The results show that information security culture and information security awareness have an impact on users’ motivation to avoid online blackmail threats through attitude and subjective norms. Furthermore, perceiving a mandated policy significantly impacts avoidance behavior. In addition, apathy and anticipated regret play a significant role in avoidance motivation. These results provide evidence on the importance of social factors on impacting avoidance motivation using the TTAT model. Further, the study encourages organizations to focus more on specifying policies and building an information security culture in order to ensure users avoid these threats through compliance and motivation. Introduction Most people use their mobiles and computers to connect with others. Because of this, social media platforms have grown in demand. Social media applications support relational and personal communication and collaboration via web-based platforms (<PERSON><PERSON><PERSON> et al., 2022, pp. 1–21; Arpaci & Bahari, 2023; Hysa & Spalek, 2019; Kwon & Park, 2022). Facebook, LinkedIn and Twitter are famous examples of social media tools, where users can share content, personal details and opinions with others. However, a high dependency on social media applications leads to an increase in the rate of cybercriminals (Brands & Van Doorn, 2022; Carvalho et al., 2020). The rate of cyber-attacks has increased as the world depends more on communication and interaction with others through digital platforms. Cyber-attacks in the second quarter of 2021 increased by more than 7% compared to the same period in 2020 (Positive Technologies, 2021). Cybercriminals use many tactics to steal social media user data, such as fraud, malware, ransomware, blackmail, and other tactics. Sharing personal data with other users on social media raises the risk of revealing sensitive and private content related to people, which leads to information leakage (Beigi et al., 2018; Keshavarzi & Ghaffary, 2023; Khan et al., 2023; Yokotani & Takano, 2022). Recently, online blackmail has increased remarkably, and this rise was observed by many authorities in the UK and Europe (EUROPOL, 2017; Statista, 2021). Generally, blackmail is defined as the threat of distributing hurtful data or detaining valuable items until the victim adheres to required conditions, which can include ransom (Kurtaliqi et al., 2022; Maitlo et al., 2019; Patchin & Hinduja, 2020; Soomro & Hussain, 2019). Blackmail attacks on social media are growing, as reported by various law agencies globally (Al Habsi et al., 2020; Pleger et al., 2021; Silic & Back, 2016). In addition, online dating scams - which are a kind of online blackmail - generate more than 130 million U.S. dollars on social media (McGuire, 2019). Based on a literature review, blackmail can cause individuals to be at risk on social media because they tend to reveal sensitive information (Al Habsi et al., 2020; Ameen et al., 2021; Kopecký, 2017). Avoiding these risks will save social media users from the risk of having any vulnerabilities exploited that may lead criminals to achieve their target of data collection. There is a lack of knowledge about online blackmail on social media (Al Habsi et al., 2020), and this study focuses on blackmail as a growing crime in many regions across the globe. The tactics used by cybercriminals are to target staff inside their organizations instead of attacking the organization's systems directly (Jain & Agrawal, 2020; Krombholz et al., 2015; Steinmetz et al., 2021). The rising severity of threats exploits individuals' lack of awareness, leading many companies to issue policies and guidelines to ensure the security of the data internally. In order to prevent these threats, it is suggested that people and companies continually educate themselves about policies regarding security (Arachchilage et al., 2016; Siponen et al., 2014; Torten et al., 2018). Enterprises have also created legislation related to information security to ensure security awareness among their staff and individuals. The European Union is a good example: it has created legislation to defend itself from cyber threats (Carvalho et al., 2020). Identifying social media users' inspiration for threat avoidance and action plays a significant part in developing effective information security solutions and regulations for both users and organizations (Carpenter et al., 2019). Even for organizations more concerned with protecting their individuals from facing any cyber threats, they could still experience financial losses, leakage of sensitive files, violence, or harmful reputations (Alanazi et al., 2022; Campbell, 2019). Firms have implemented various information security practices and policies to achieve effective security awareness from all residents. Policies and awareness should work in tandem to ensure that awareness surrounding the importance of following policies for security is effectively spread (Yan et al., 2021). In addition, information security culture means that staff are able to share security concepts while also being able to perceive information security requests and understand how to comply with security requirements (Da Veiga, 2018; Da Veiga & Eloff, 2010). Adopting this culture would help people effectively protect information by informing their attitudes, responsibilities and beliefs (Rocha Flores & Ekstedt, 2016; Solomon & Brown, 2021). To understand how social media users behave with security threats under defined information security policies and practices by governmental entities or private corporations, this study uses the technology threat avoidance theory (TTAT) model as a basic model. The technology threat avoidance theory model focuses on studying and interpreting the users' behavior to avoid a digital threat (Liang & Xue, 2009). Furthermore, it uses the basics of organizational control (OC) theory which measures the relationship between mandated information security policies and avoidance motivation. Moreover, the study uses the theory of planned behavior (TPB) to study the impact of environmental factors such as information security culture and awareness towards human factors such as attitude, self-efficacy, and subjective norms in making the user more inspired to avoid threats. Studying the factors that affect the user's motivation to escape from any possible technical threat is important to prevent the various attempts by criminals to steal valuable information from social media users (Liang & Xue, 2010). This study attempts to lay the groundwork to understanding how public authorities and companies impact the motivation of social media users to avoid the widening rate of blackmail threats through implementing information security practices and policies. Addressing how to measure the motivation of the user's avoidance against blackmail threat under information security regulatory factors would garner a deeper understanding of the issue. Further, uncovering the relationship between threat avoidance motivation and the implementation of policies and practices would further develop the information security policies and culture provided by an organization to ensure the prevention of these threats and promote positive behavior towards information security. The study intends to provide contributions on developing regulations related to information security and initiating associated roles and policies. Based on the TTAT model, social effects can have consequences on users' IT threat avoidance behavior (Liang & Xue, 2009; Mamonov & Benbunan-Fich, 2018). The implementation of information security regulatory practices can affect users' confidence or, as a consequence, impact the users' behavior in avoiding blackmail threats (Ameen et al., 2022; Liang & Xue, 2009). The study aims to examine the impact of organizational and behavioral factors related to information security on motivating social media users to avoid online blackmail risk at work. Based on this, it uses the TTAT model along with OC and TPB models. Using these models will help to measure these factors with the user's motivation to avoid and escape from any cyberattack such as online blackmail. TTAT proposes that when the user perceives an attack or a threat, it impacts their motivation to create a safeguarding process to avoid this threat (Carpenter et al., 2019; Gillam & Foster, 2020; Liang & Xue, 2009). The theory indicates that the avoidance behavior of IT threats arises from threat and coping appraisal models (Alomar et al., 2019; Liang & Xue, 2009). To illustrate the TTAT model process, the user initially establishes his perceptions of the technology threat after estimating the degree of violence and its likelihood, factors which are related to the “perceived threat” concept (Alomar et al., 2019; Liang & Xue, 2009). Then, the user begins to estimate the impact of safeguarding measures (Liang & Xue, 2009). Measures may include technical behavior like the use of anti-malware applications that protect information. For this study, measures might be taken to prevent any attempt from the attacker to steal information, such as not opening an unknown link, refusing to share sensitive information, or not accepting an invitation from a suspicious account. The TTAT model empirically tests eight constructs of perceived susceptibility, perceived severity, perceived threat, avoidance motivation, safeguard effectiveness, safeguard costs, self-efficacy, and avoidance behavior, and finds valuable illustrations about the user's avoidance behavior towards risks and threats (Liang & Xue, 2010). Prior studies with different scopes showed similar findings in testing the TTAT model's constructs (Alomar et al., 2019, Carpenter, 2019). In addition, other studies use the basics of the TTAT model and embed additional constructs from different models to measure the impacts of behavioral and organizational factors on the avoidance motivation construct. For instance, Verkijika (2019) uses the TTAT model to measure the impact of anticipating regret on motivation to avoid phishing threats by users. Also,Forrester (2017) Forrester (2019) shows that knowledge sharing, information security experience, and group norms can impact the motivation and behavior of users for threat avoidance. The research comes with certain gaps that need to be bridged. First, there are a lack of studies in relation to online blackmail, especially on social media (Al Habsi, 2020). Moreover, there are a shortage of studies related to blackmail threats based on the TTAT model. Further, the authors of TTAT indicate the impact of security awareness and education programs (Liang & Xue, 2009). This study aims to bridge these gaps by developing a conceptual framework that measures the impact of awareness, culture, and policies related to information security on users’ motivation to avoid online blackmail threats on social media. Based on the study's objectives, a proposed model was developed to examine the relationship between avoidance motivation and information security regulatory factors. Fig. 1 shows the proposed model based on the TTAT model. The hypothesized model starts with the perceived threat construct and then shows the impacts of constructs based on PBT and OC constructs: attitude, subjective norms, self-efficacy, the perceived mandatories of information on security policy, and apathy. In addition, it includes the construct of anticipated regret in relation to avoidance motivation. The following paragraphs describe each hypothesis in the proposed model. Liang and Xue (2009) state that avoidance motivation is the result of a perceived threat. Perceived threat refers to the degree at which the user perceives a cyber-threat (Lian, 2020). Under cyberattacks, a person can be harmed by a privacy breach and loss of money (Liang & Xue, 2009; Sharma & Warkentin, 2019). This perception of harm and pain leads to attempts to avoid such harm and pain (Freud, 1915; Liang & Xue, 2009). Avoidance motivation refers to how the user is motivated to avoid technical danger or threats (Liang & Xue, 2010). Based on the TTAT model, when an individual perceives a threat through digital communication, they are motivated to avoid or ignore this danger (Liang & Xue, 2009, p. 2010). As an example for this study, if a social media user perceives that an individual has a suspicious aim to use their information illegally, the user will be motivated to avoid this contact. Many studies based on TTAT prove this relationship (Liang & Xue, 2010; Young et al., 2016). Thus, this research hypothesizes the following: H1. A perceived blackmail threat positively impacts avoidance motivation. The latest journals pay more attention to human elements and the purpose of managing and securing information (Rocha Flores & Ekstedt, 2016). The TPB introduces attitude, subjective norms, and self-efficacy as concepts that impact a person's intention (Ajzen, 1991; Ajzen & Fishbein, 1975). In relation to information security, attitudes related to positive performance are geared towards protecting information and avoiding a threat (Liu et al., 2020; Rocha Flores & Ekstedt, 2016; Vimalkumar et al., 2021). Subjective norms or normative beliefs regarding information security mean that the user's belief in perceiving social pressure such as from other colleagues, managers, or community in behaving the expected behavior in securing information (Bulgurcu et al., 2010; Hanus et al., 2018). These constructs have been used in testing and approving the relationship between attitude and subjective norms towards the behavioral intention to protect and secure information from attacks based on TPB (Bodford et al., 2021; Bulgurcu et al., 2010; Hanus et al., 2018; Rocha Flores & Ekstedt, 2016; Shore et al., 2022; Wiafe et al., 2020). This study proposes that avoidance motivation is treated as intention (Liang & Xue, 2010, p. 400). Treating avoidance motivation as a security intention allows previous studies to measure it as a critical predictor for certain behavior, as per the TTAT model (Verkijika, 2019). Consequently, scholars are motivated to examine the relationship between security intention and security actions (Ameen et al., 2020; Verkijika, 2019). Accordingly, this study hypothesizes that: H2 Subjective norms about the danger of blackmail positively affects the motivation to avoid this attack. H3 The attitude towards avoiding blackmail attacks positively affects the motivation to avoid these attacks. Self-efficacy is a construct used in models such as TTAT and TPB (Ajzen, 1991; Bandura, 1986; Liang & Xue, 2009) to measure how confident the user is in taking protection measures. Further, self-efficacy in information security is well-known for influencing information security behavior (Verkijika, 2019). Various studies based on the TTAT model prove the positive effect of self-efficacy towards the motivation to avoid threats in the information security field (e.g., Alomar et al., 2019; Ikhalia et al., 2017; Sadok, Alter, & Bednar, 2020; Samhan, 2017; Verkijika, 2019; Williams, 2021). Also, studies related to the information security field used the self-efficacy construct based on the TPB model to prove the relationship between self-efficacy and intention to take security protection (e.g. Hanus et al., 2018; Hooper & Blunt, 2020; Nguyen & Kim, 2017, pp. 4947–4956). This study aims to examine the positive impact of self-efficacy on avoidance motivation. For example, if the user is more confident in avoiding and resisting blackmail threats, the motivation will be more easily avoided. Following that, the study proposes this hypothesis: H4 An individual's self-efficacy about avoiding blackmail threats positively impacts the avoidance motivation. The TTAT model argues that the social environment can support users by providing information related to security protection (Liang & Xue, 2009; Wiley et al., 2020). Studying the positive impact of information security culture (ISC) on the staff's attitude and subjective norms can show the role of ISC in helping users avoid threats. The previous study confirms ISC's impacts on attitude and subjective norms to influence the intention of behaving correctly to secure information (Rocha Flores & Ekstedt, 2016). Besides, ISC can impact users' behavior in relation to information security (Tenzin, 2021). In addition, Liang et al. (2013) argue that the environment impacts normative beliefs of ignoring and preventing threats. Based on this, the present study proposes these hypotheses: H5 information security culture in the organization is positively related to the staff's subjective norms towards avoidance motivation. H6 information security culture in the organization is positively related to the staff's attitude towards avoidance motivation . TPB concludes that knowledge and experience, such as information security awareness, can indirectly impact behavior through self-efficacy, normative beliefs, and attitude (Ajzen, 1991). These impacts have been studied and their compliance with information security policies has been confirmed (Rocha Flores & Ekstedt, 2016; Li, Y., Pan & Zhang, 2019). Information security awareness plays a basic role in complying with security policies (Koohang et al., 2019). Many studies have proved that the individual intends to take action against any risk that threatens organizational or personal data (Ameen et al., 2020; Bulgurcu et al., 2010; Hadlington et al., 2021; Koohang et al., 2019; Rocha Flores & Ekstedt, 2016). Rocha Flores and Ekstedt (2016) studied the impact of information security awareness on a person's attitude and self-efficacy in resisting social engineering attacks. The results of the previous study show a strong impact on both instruments (attitude and self-efficacy). These studies conclude the importance of awareness programs to impact the beliefs of staff in acting correctly to avoid threats on social media. The following hypotheses are therefore proposed: H7 . Information security awareness related to staff is positively linked with attitude towards avoiding blackmail threats. H8 Information security awareness related to staff is positively linked with self-efficacy towards avoiding blackmail threats. Anticipated regret was originally a psychological concept indicating self-efficacy thoughts when people feel they may regret not carrying out an action (Bakker et al., 1997). It has become a related factor used for information security behavior (Sommestad et al., 2015). Anticipated regret has proved to positively impact users’ intention to adopt the right behavior related to information security, whether organizational (Sommestad et al., 2015), or individual (Verkijika, 2018). In this research, anticipated regret refers to the avoidance of negative impact from responding negatively to avoid a threat. Thus, the hypothesis is as follows: H09 . Anticipated regret has a positive impact on avoidance motivation. Apathy is a construct developed by Boss et al. (2009), and refers to users’ disregard in following policies due to limited time or a belief that a policy is not critical. This construct has also been tested empirically and has been proven to negatively impact precautionary action and intention in information security (Boss et al., 2009; Chen & Liang, 2019), as well as the reaction to new policies (Lowry & Moody, 2015). This proposed model aims to measure the apathy construct in relation to avoidance motivation, in case the user does not follow regulation. It looks to examine the lack of compliance policies in the organization towards avoiding threats made by users. Apathy should negatively impact the intention to avoid online blackmail threats. According to this, the proposed hypothesis is as follows: H10 The apathy or ignorance of following information security policies regarding blackmail risks negatively influencing social media users' avoidance motivation. Models have been developed to explain precautions taken by a person in information security matters (Boss et al., 2009; Kirsch & Boss, 2007). These studies have concluded the importance of implementing mandated policies to achieve compliance (Boss et al., 2009; Kirsch & Boss, 2007). They contain precautions taken, as well as perceived mandatories, specification, evaluation, and reward of an information security policy. These constructs have been tested using a survey. The study shows the perception of mandatories effectively increases the motivation of individuals to take safeguarding actions. Precautions taken refer to the level at which people perceive that they apply measures to safeguard their devices and comply with information security policies and procedures (Boss et al., 2009; Chen et al., 2015). Based on the model, the perceived mandates of an information security policy positively impact specification, evaluation, and reward. Accordingly, it positively impacts precautions taken. The proposed model aims to measure the impact of perceived mandated information security policy. It also looks to understanding the impact of this policy on a person's behavior in order to avoid threats. The concept of this measure comes from the TTAT model's statement that the right knowledge and information received by staff can help them behave positively against threats (Liang & Xue, 2009). Also, the TTAT model states that the social environment impacts users who intend to avoid the IT security risk (Liang & Xue, 2009). For instance, if an organization launches mandated policies regarding safety on social media, and the individual learns this policy, the individual's intention to take security precautions to prevent risk will be affected. Thus, the proposed hypothesis is as follows: H11 Perceived mandatories of information security policy positively influences the behavior in order to avoid blackmail threats. In addition, Boss et al. (2009) assume that the majority of staff could comply with the policy if forced to by management. Based on this, they tested the concept of evaluation and specification as control elements related to information security policy to ensure compliance. Specification and evaluation refer to the description of a specific behavior or outcome and appraise the behavior of staff in completing the tasks aligned with the policies (Kirsch, 2004). The two constructs have been tested and empirically proven (Boss et al., 2009; Cuganesan et al., 2018; Philip et al., 2022). In a recent study related to corporate security policy, increased specification was shown to significantly impact the perception of mandated policies (Lee & Lee, 2021). Boss et al. (2009) measure perceived mandated information policies as a mediator between security precaution on one side and specification and evaluation on the other side. To measure the mediation role of perceiving mandated policy in the relation between policy specification and evaluation and staff's behavioral intention to avoid threats, this proposed model hypothesizes the following: H12 Perceived mandated information security policy will play a mediation role in the relation between the specification control element and avoidance behavior. H13 Perceived mandated information security policy will play a mediation role in the relation between the evaluation control element and avoidance behavior. Avoidance behavior refers to the user's action for protecting information and securing it in order to mitigate and reduce the threat technically (Liang & Xue, 2009; Nasir et al., 2019; Dzidzah et al., 2020). The TTAT model includes avoidance behavior as the last construct, which represents the result of avoidance motivation. The model states that avoidance motivation directly impacts whether users take action against a threat (Liang & Xue, 2009). This relation is built based on Steers and Mowdat (2004) who conclude that individuals are more likely to behave if motivated. Studies show that there is a significant positive impact of avoidance motivation on behavior (e.g., Alomar et al., 2019; Butler & Butler, 2021, pp. 134–138; Verkijika; 2019). To measure this relationship this paper proposes this hypothesis: H14 Avoidance motivation positively impacts behavior to avoid blackmail threats by taking safeguard measures. Section snippets Instrument development To evaluate the validity of all measurements, all the items along with their measurements were adopted from previous studies. Details of the measurement items are shown in Appendix A. These items were anchored on a 7-point Likert scale, ranging from 1-strongly disagree to 7-strongly agree. The demographic information about the respondents such as gender, age and experience were measured using a nominal scale. Pilot study For the current study, the pilot study had a sample of (N = 51) volunteers from Instrument validation In this study, the researcher uses certain measurements to measure the model fit and construct validity in order to check the model's validity and reliability with its constructs and related items. Structural Equation Modeling (SEM) is a statistical approach that is used increasingly in the social science field (Civelek, 2018). The present study uses partial least squares (PLS) to examine the measurement related to the model. The PLS method is a well-known procedure of SEM as it is not heavily Discussion This study provides clearer concepts about the impact of information security regulations on the motivation by users to avoid technical threats such as online blackmail. This research fills the gap by measuring the strength of regulations that control and guide individuals inside an organization in order to prevent any attempt of online blackmail threats from cybercriminals. The attitude of users towards avoiding online blackmail significantly impacts avoidance motivation. Attitude is shown to Declarations Compliance with Ethical Standards: Funding This study is not funded by any organization. Consent for publication N/A. Authors' contributions Mr. Al Ghanboosi led the study and participated in preparing the conceptual framework and also participated in drafting the manuscript. Dr Ali participated in the design of the study and helped with the literature review and also participated in drafting the manuscript. Dr Tarhini helped with performing the statistical analysis and also participated in drafting the manuscript, and participated in drafting the results and other portions of the manuscript. He also handled the responsibility of Declaration of competing interest The authors declare that they have no conflict of interest. Acknowledgements NA. Basim Juma Al Ghanboosi has a Master degree from Sultan Qaboos University in information systems in 2021. He is interested in information systems subjects like, information technology governance, machine learning, and information security. He composed a book with other academics in education major about machine learning and E-leaning in Oman in Sultan Qaboos University in 2022. He is currently a head of information systems section in Oman Royal Court Affairs. References (93) K. Yokotani et al. Predicting cyber offenders and victims and their offense and damage time from routine chat times and online social network activities Computers in Human Behavior (2022) Z. Yan et al. Risk and protective factors for intuitive and rational judgment of cybersecurity risks in a large sample of K-12 students and teachers Computers in Human Behavior (2021) M.D. Williams Social commerce and the mobile platform: Payment and security perceptions of potential users Computers in Human Behavior (2021) A. Wiley et al. More than the individual: Examining the relationship between culture and information security awareness Computers & Security (2020) M. Vimalkumar et al. ‘Okay google, what about my privacy?’: User's privacy perceptions and acceptance of voice based digital assistants Computers in Human Behavior (2021) S.F. Verkijika If you know what to do, will you take action to avoid mobile phishing attacks”: Self-efficacy, anticipated regret, and gender Computers in Human Behavior (2019) R. Torten et al. The impact of security awareness on in- formation technology professionals' behavior Computers & Security (2018) K.F. Steinmetz et al. Performing social engineering: A qualitative study of information security deceptions Computers in Human Behavior (2021) M. Siponen et al. Employees' adherence to information security policies: An exploratory field study Information & Management (2014) M. Silic et al. The dark side of social networking sites: Understanding phishing risks Computers in Human Behavior (2016) A. Shore et al. To share or not to share: Extending Protection Motivation Theory to understand data sharing with the police Computers in Human Behavior (2022) S. Sharma et al. Do I really belong?: Impact of employment status on information security policy compliance Computers & Security (2019) W. Rocha Flores et al. Shaping intention to resist social engineering through transformational leadership, information security culture and awareness Computers & Security (2016) L.E. Pleger et al. Making public concerns tangible: An empirical study of German and UK citizens' perception of data protection and data security Computers in Human Behavior (2021) A. Nasir et al. An analysis on the dimensions of information security culture concept: A review Journal of Information Security and Applications (2019) C. Liu et al. Motivating information security policy compliance: The critical role of supervisor-subordinate guanxi and organizational commitment International Journal of Information Management (2020) S. Kwon et al. Understanding user responses to the COVID-19 pandemic on Twitter from a terror management theory perspective: Cultural differences among the US, UK and India Computers in Human Behavior (2022) F. Kurtaliqi et al. The psychological reassurance effect of mobile tracing apps in Covid-19 Era Computers in Human Behavior (2022) K. Krombholz et al. Advanced social engineering attacks Journal of Information Security and Applications (2015) K. Kopecký Online blackmail of Czech children focused on so-called “sextortion” (analysis of culprit and victim behaviors) Telematics and Informatics (2017) M.I. Khan et al. Cynicism as strength: Privacy cynicism, satisfaction and trust among social media users Computers in Human Behavior (2023) M. Keshavarzi et al. An ontology-driven framework for knowledge representation of digital extortion attacks Computers in Human Behavior (2023) L. Hadlington et al. Exploring role of moral disengagement and counterproductive work behaviours in information security awareness Computers in Human Behavior (2021) A.R. Gillam et al. Factors affecting risky cybersecurity behaviors by US workers: An exploratory study Computers in Human Behavior (2020) A. Da Veiga et al. A framework and assessment instrument for information security culture Computers & Security (2010) Y.A.N. Chen et al. Impacts of comprehensive information security programs on information security culture Journal of Computer Information Systems (2015) J. Brands et al. The measurement, intensity and determinants of fear of cybercrime: A systematic review Computers in Human Behavior (2022) J.E. Bodford et al. Does perceived social networking site security arise from actual and perceived physical safety? Computers in Human Behavior (2021) I. Arpaci et al. A complementary SEM and deep ANN approach to predict the adoption of cryptocurrencies from the perspective of cybersecurity Computers in Human Behavior (2023) N.A.G. Arachchilage et al. Phishing threat avoidance behaviour: An empirical investigation Computers in Human Behavior (2016) N. Ameen et al. Keeping customers' data secure: A cross-cultural study of cybersecurity compliance among the gen-mobile workforce Computers in Human Behavior (2021) N. Ameen et al. Employees' behavioural intention to smartphone security: A gender-based, cross-national study Computers in Human Behavior (2020) N. Ameen et al. The personalisation-privacy paradox: Consumer interaction with smart technologies and shopping mall loyalty Computers in Human Behavior (2022) N. Alomar et al. Uncovering the predictors of unsafe computing behaviors in online crowdsourcing contexts Computers & Security (2019) A. Alhogail Design and validation of information security culture framework Computers in Human Behavior (2015) M. Alanazi et al. Exploring the factors that influence the cybersecurity behaviors of young adults Computers in Human Behavior (2022) I. Ajzen The theory of planned behavior Organizational Behavior and Human Decision Processes (1991) I. Ajzen et al. Belief, attitude, intention and behaviour: An introduction to theory and research (issue may 1975 (1975) A. Al Habsi et al. Blackmail on social media: What do we know and what remains unknown? Security Journal (2020) A.S. Al-Busaidi et al. The role of excessive social media content generation, attention seeking, and individual differences on the fear of missing out: A multiple mediation model (2022) A.B. Bakker et al. The moderating role of self- efficacy beliefs in the relationship between anticipated feelings of regret and condom Use Journal of Applied Social Psychology (1997) A. Bandura Social foundations of thought and action: A social cognitive theory (1986) G. Beigi et al. Securing social media user data - an adversarial approach S.R. Boss et al. If someone is watching, I’ll do what I'm asked: Mandatoriness, control, and information security European Journal of Information Systems (2009) B. Bulgurcu et al. Information security policy compliance: An empirical study of rationality-based beliefs and information security awareness MIS Quarterly (2010) M. Butler et al. The influence of mobile operating systems on user security behavior. In2021 IEEE 5th international co nference on cryptography, Security and privacy (CSP) (2021) View more references Cited by (0) Recommended articles (6) Research article The influence of parents and peers on adolescents’ problematic social media use revealed Computers in Human Behavior, Volume 143, 2023, Article 107705 Show abstract Nowadays, parents, caretakers, teachers and researchers have an increasing interest in the development and consequences of problematic social media use, especially among adolescents. A growing body of research investigates factors that may influence the development of problematic social media use. This study examined the role of the broader context of parental (time spent with parents and family support), peer (peer support and peer pressure), and individual factors (perceived self-control) in the development of adolescents' risky and problematic social media use (ref = normative), as well as the moderating role of self-control by using a longitudinal design. Adolescents ( N = 1384) aged 11–19 years ( M <sub> age </sub> = 14.1, SD = 1.03) were included and completed a self-report questionnaire twice (6-month interval). A Multinomial Logistic Regression showed that parent and peer factors predicted risky social media use, but not problematic use of social media. Adolescents’ level of self-control did not modify these relationships. However, low self-control did increase the odds of developing risky or problematic social media use directly. In conclusion, this study provides preliminary evidence that general parent and peer factors can help to prevent risky social media use, but to a lesser extent problematic use of social media. For the latter, the individual factor self-control, however, seems to play a role. Research article Dialogic communication on local government social media during the first wave of COVID-19: Evidence from the health commissions of prefecture-level cities in China Computers in Human Behavior, Volume 143, 2023, Article 107715 Show abstract Although some scholars have explored the level and determinants of Dialogic Communication on Government Social Media (DCGSM), none have conducted their studies in the context of public crisis. The current study contributes to the understanding on DCGSM by 16,822 posts crawled from the official Sina Weibo accounts of 104 Chinese health commissions in prefecture-level cities during the first wave of the COVID-19 pandemic. Results show that Chinese local government agencies have great variations in their DCGSM during the pandemic and the overall performance is poor. Furthermore, Chinese local governments prefer to conserve visitors and generate return visits, rather than dialogic loops development and the usefulness of information enhancement. The findings suggest that both public pressure and peer pressure contribute to the DCGSM of Chinese local governments during the public health crisis. In addition, the effect of public pressure is stronger than that of the peer pressure, indicating that local government agencies have experienced more demand-pull DCGSM. Research article Attentional inhibitory control interference related to videogames, pornography, and TV series exposure: An experimental study in three independent samples Computers in Human Behavior, Volume 143, 2023, Article 107683 Show abstract Attentional inhibitory control (AIC) is responsible for ignoring salient yet irrelevant stimuli (i.e., distractors) to focus cognitive resources on goal-oriented demands. While the relevance of this cognitive process is well established when it comes to explaining the etiopathogenesis of substance use disorders, few studies have investigated AIC in the context of non-substance-related addictive behaviors. This experimental study focused on exploring the contribution of AIC to understanding problematic engagement in videogames, pornography, and TV series. The main aim of this study was to compare AIC when exposed to these contents and their correlates with different indicators of Gaming Disorder (GD), Problematic Pornography Use (PPU), and binge-watching (BW). Participants from three independent samples (40 men from Luxembourg; 91 men from Spain; and 108 women from Spain) completed an adapted version of the Stroop task designed to measure AIC when exposed to pornography, videogames, and TV series, as well as different self-reports assessing problematic engagement in these activities. Participants experienced more AIC problems –i.e., increased reactions times– when answering the Stroop task while presented with TV series and pornography as distractors, but not when presented with videogames. Furthermore, we only found few anecdotal results supporting the relationship between individual differences in the level of AIC when confronted with these contents and an increased risk of displaying GD, PPU, or BW symptoms. Although preliminary, our results question the notion that AIC may constitute a central process in explaining the initiation and/or maintenance of non-substance-related addictive behaviors. Research article Spatio-temporal patterns of tropospheric NO<sub>2</sub> over India during 2005–2019 Atmospheric Pollution Research, Volume 14, Issue 3, 2023, Article 101692 Show abstract Air pollution is the leading environmental health risk factor globally. Nitrogen dioxide (NO<sub>2</sub>) is a criteria pollutant associated with adverse health effects. In India, the focus has been mostly on ambient PM<sub>2.5</sub>; hence understanding the space-time variability of NO<sub>2</sub> and the potential role of different NO<sub>2</sub> sources are required. This work aimed to address these two gaps by first examining a long-term spatial heterogeneity of NO<sub>2</sub> and linking it to the major contributing sources (e.g., power plants and industries) over the Indian region, and secondly by examining the variability in land use patterns and its changes over the years 2005–2019. We analyzed tropospheric NO<sub>2</sub> vertical columnar densities (VCDs) from the Ozone Monitoring Instrument (OMI) onboard the Aura satellite at a resolution of 0.1° × 0.1° for the period 2005 to 2019. We calculated linear trends in tropospheric NO<sub>2</sub> VCDs and performed a significance test to ensure real changes. We also analyzed Global Human Settlement Layer (GHSL) data to identify the land use as ‘high-density urban,’ ‘low-density urban,’ ‘rural,’ and ‘no settlement.’ The spatial analysis found higher tropospheric NO<sub>2</sub> VCDs (>5 × 10<sup>15</sup> molecules/cm<sup>2</sup>) in eastern (coal belt with many power plants) and northern India, along with a few locations in central India. The change in tropospheric NO<sub>2</sub> VCDs during the last 15 years showed an increasing trend (>2 × 10<sup>13</sup> molecules/cm<sup>2</sup>) in most parts of India. Whereas during the last decade (2010–2019), the annual average trend over the country had reduced by up to ∼76% relative to 2005–2009. Tropospheric NO<sub>2</sub> VCDs increased by 12.5%–29.6% from 2005 to 2019 across all levels of land settlement in India. Our study suggested considering NO<sub>2</sub> as an important pollutant in addition to PM<sub>2.5</sub> and developing a NO<sub>2</sub> exposure model for further health impact assessment studies. Research article Can chatbots satisfy me? A mixed-method comparative study of satisfaction with task-oriented chatbots in mainland China and Hong Kong Computers in Human Behavior, Volume 143, 2023, Article 107716 Show abstract Task-oriented chatbots are gradually being used across the globe. Most notably, while chatbots have for a long time penetrated users’ daily lives in mainland China, Hong Kong is still struggling to improve and promote its chatbot services. To determine whether antecedents of satisfaction and usage intention differ based on different stages of chatbot adoption and development, we conduct a comparative study based on a research model that integrates the Delone and McLean Information System success model and privacy concerns. The model is developed and examined using a mixed-method approach. After conducting focus group interviews (N = 15) in both regions, online surveys were conducted in mainland China (N = 637) and Hong Kong (N = 647), respectively. Based on qualitative exploration, we identified critical factors of perceived quality and privacy concerns. The quantitative findings further illuminate the different roles of the antecedents in the two regions. The results show that usage intention can be positively influenced by satisfaction, and satisfaction can be increased by relevance, completeness, pleasure and assurance in both regions. However, response time and empathy are factors influencing satisfaction only in mainland China. Privacy concerns cannot influence satisfaction in both regions. Research article Decision control and explanations in human-AI collaboration: Improving user perceptions and compliance Computers in Human Behavior, 2023, Article 107714 Show abstract Human-AI collaboration has become common, integrating highly complex AI systems into the workplace. Still, it is often ineffective; impaired perceptions—such as low trust or limited understanding—reduce compliance with recommendations provided by the AI system. Drawing from cognitive load theory, we examine two techniques of human-AI collaboration as potential remedies. In three experimental studies, we grant users decision control by empowering them to adjust the system’s recommendations, and we offer explanations for the system’s reasoning. We find decision control positively affects user perceptions of trust and understanding, and improves user compliance with system recommendations. Next, we isolate different effects of providing explanations that may help explain inconsistent findings in recent literature: while explanations help reenact the system’s reasoning, they also increase task complexity. Further, the effectiveness of providing an explanation depends on the specific user’s cognitive ability to handle complex tasks. In summary, our study shows that users benefit from enhanced decision control, while explanations—unless appropriately designed for the specific user—may even harm user perceptions and compliance. This work bears both theoretical and practical implications for the management of human-AI collaboration. Basim Juma Al Ghanboosi has a Master degree from Sultan Qaboos University in information systems in 2021. He is interested in information systems subjects like, information technology governance, machine learning, and information security. He composed a book with other academics in education major about machine learning and E-leaning in Oman in Sultan Qaboos University in 2022. He is currently a head of information systems section in Oman Royal Court Affairs. Dr. Saqib Ali is an Associate Professor in the Department of Information Systems at Sultan Qaboos University, Sultanate of Oman. He has a PhD and MSc in Information Systems from La Trobe, Australia. Dr. Ali is a business, IT implementation and execution professional, with strong skills in business analysis, Software development, management, and implementation, developed through higher education and useful work experience. Strong understanding of developing business strategies, business process integration and their alignment with IT, along with innovative practices to gain or sustain a competitive advantage. Dr. Saqib has also been active in teaching and R&D (Research and Development), he successfully able to attract a number of internal, external and strategic grants while working at SQU. He has published several journals, conference papers, technical reports and edited two books on Business Information Systems. Recently he authored a book on Cybersecurity for Cyber Physical Systems by Springer. In addition, he received a number of awards in Teaching, Research and Academic excellence from SQU. He has been invited to serve in many international conferences, journals and program committees. Dr. Ali Tarhini<sup>∗</sup> is an Associate Professor in the Department of Information Systems at Sultan Qaboos University, Sultanate of Oman. He holds a Master degree in E-commerce from University of Essex, and a PhD in Information Systems from Brunel University London, UK. His research interests include Knowledge Management, ICT in information systems (Technology acceptance and adoption), cross-cultural issues in IT (at individual and national culture, cross-cultural studies), and Quantitative Methods (Psychometrics; Instrument development and validation; Cross-cultural measurement; Issues in survey development and administration; structural equation modeling). Dr. Tarhini has published over 60 research papers in international journals including the Computers in Human Behavior, Information Technology & people, Journal of Enterprise Information Management, Journal of Management Development, Management Research Review, British Journal of Educational Technology, Journal of Computing in Higher Education, Education and Information Technologies. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.chb.2023.107702", "PubYear": 2023, "Volume": "144", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sultan Qaboos University, Department of Information Systems, P.O. Box 20, PC 123, Muscat, Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sultan Qaboos University, Department of Information Systems, P.O. Box 20, PC 123, Muscat, Oman"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sultan <PERSON>aboos University, Department of Information Systems, P.O. Box 20, PC 123, Muscat, Oman;Corresponding author"}], "References": [{"Title": "Factors influencing the information security behaviour of IT employees", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "8", "Page": "862", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Social commerce and the mobile platform: Payment and security perceptions of potential users", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "105557", "JournalTitle": "Computers in Human Behavior"}, {"Title": "More than the individual: Examining the relationship between culture and Information Security Awareness", "Authors": "<PERSON><PERSON> Wiley; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "101640", "JournalTitle": "Computers & Security"}, {"Title": "Employees’ behavioural intention to smartphone security: A gender-based, cross-national study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "106184", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Factors affecting risky cybersecurity behaviors by U.S. workers: An exploratory study", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "106319", "JournalTitle": "Computers in Human Behavior"}, {"Title": "It is not my job: exploring the disconnect between corporate security policies and actual security practices in SMEs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "3", "Page": "467", "JournalTitle": "Information and Computer Security"}, {"Title": "Security behaviour of mobile financial service users", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "5", "Page": "719", "JournalTitle": "Information and Computer Security"}, {"Title": "Keeping customers' data secure: A cross-cultural study of cybersecurity compliance among the Gen-Mobile workforce", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106531", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Exploring role of moral disengagement and counterproductive work behaviours in information security awareness.", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106557", "JournalTitle": "Computers in Human Behavior"}, {"Title": "‘Okay google, what about my privacy?’: User's privacy perceptions and acceptance of voice based digital assistants", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "106763", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Does perceived social networking site security arise from actual and perceived physical safety?", "Authors": "<PERSON>; <PERSON>; Virginia S.Y<PERSON>", "PubYear": 2021, "Volume": "121", "Issue": "", "Page": "106779", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Risk and protective factors for intuitive and rational judgment of cybersecurity risks in a large sample of K-12 students and teachers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "121", "Issue": "", "Page": "106791", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Making public concerns tangible: An empirical study of German and UK citizens’ perception of data protection and data security", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "122", "Issue": "", "Page": "106830", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Performing social engineering: A qualitative study of information security deceptions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "106930", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The personalisation-privacy paradox: Consumer interaction with smart technologies and shopping mall loyalty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "106976", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The measurement, intensity and determinants of fear of cybercrime: A systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "107082", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Understanding user responses to the COVID-19 pandemic on Twitter from a terror management theory perspective: Cultural differences among the US, UK and India", "Authors": "<PERSON>yeon Kwon; Albert Park", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "107087", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Predicting cyber offenders and victims and their offense and damage time from routine chat times and online social network activities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "107099", "JournalTitle": "Computers in Human Behavior"}, {"Title": "To share or not to share: Extending Protection Motivation Theory to understand data sharing with the police", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "107188", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The psychological reassurance effect of mobile tracing apps in Covid-19 Era", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "107210", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Exploring the factors that influence the cybersecurity behaviors of young adults", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "136", "Issue": "", "Page": "107376", "JournalTitle": "Computers in Human Behavior"}, {"Title": "An ontology-driven framework for knowledge representation of digital extortion attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "107520", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Cynicism as strength: Privacy cynicism, satisfaction and trust among social media users", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>; <PERSON> (M.I.) <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "107638", "JournalTitle": "Computers in Human Behavior"}, {"Title": "A complementary SEM and deep ANN approach to predict the adoption of cryptocurrencies from the perspective of cybersecurity", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "107678", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 106520820, "Title": "On Using Conversational Frameworks to Support Natural Language Interaction in Intelligent Tutoring Systems", "Abstract": "In this article, we analyze the potential of conversational frameworks to support the adaptation of existing tutoring systems to a natural language form of interaction. We have based our research on a pilot study, in which the open-source machine learning framework Rasa has been used to build a conversational agent that interacts with an existing intelligent tutoring system (ITS) called hypergraph-based problem solver (HBPS). This agent has been seamlessly integrated into the ITS to replace the previously available button-based user interface and allow the user to interact with HBPS in natural language. Once appropriately trained, the conversational agent was capable of identifying the intention of a given user utterance and extracting the relevant entities related to the message content, with average weighted F1-scores of 0.965 and 0.989 for intents and entities, respectively. Methodological guidelines are provided to generate a realistic training set that enables the creation of the required natural language understanding model and also evaluate the resulting system. These guidelines can be easily exported to other ITS and contexts to provide an enhanced interaction based on natural language processing methods.", "Keywords": "Conversational agents;intelligent tutoring systems (ITS);interactive learning environments (ILE);natural language processing (NLP);natural language understanding (NLU);Rasa", "DOI": "10.1109/TLT.2023.3245121", "PubYear": 2023, "Volume": "16", "Issue": "5", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-De <PERSON>", "Affiliation": "Departament d'Informàtica, Universitat de València, Avda. de la Universitat s/n, Valencia, Burjassot, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Departament d'Informàtica, Universitat de València, Avda. de la Universitat s/n, Valencia, Burjassot, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departament de Didàctica de la Matemàtica, Universitat de València, Valencia, Spain"}], "References": [{"Title": "An introduction to Deep Learning in Natural Language Processing: Models, techniques, and tools", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "443", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 106520975, "Title": "A Data-Driven Bilevel Optimization Problem Considering Product Popularity for the E-Commerce Presale Mode", "Abstract": "<p>To obtain a competitive advantage in the e-commerce presale mode, an e-tailer needs to design new products favored by consumers and consider the demand matching and supply chain services to achieve better operational results. This paper develops a data-driven optimization model based on consumer analytics and bilevel multiobjective programming to provide practical solutions for the e-commerce presale mode. At the upper level, the e-tailer, as the leader, identifies consumer preferences by quantifying product popularity through consumer analytics. Then, the e-tailer optimizes the product popularity to select the products suitable for presale and formulates the production plan. At the lower level, the logistics enterprise, as the follower, formulates the distribution plan based on the leader’s decision. Because consumer analytics are utilized and the model has a bilevel structure, a data-driven optimization method is proposed to conduct simulations for the proposed model. The model uses the multiple objectives binary particle swarm optimization with multiple social structures (MOBGLNPSO) and bilevel multiobjective particle swarm optimization with multiple social structures (Bi-MOGLNPSO). The results analysis and sensitivity analysis verify that the proposed model and method can improve the demand matching and operational efficiency of the e-commerce supply chain and uncover managerial implications for both the e-tailers and logistic enterprises.</p>", "Keywords": "E-commerce supply chain; Product popularity; Consumer analytics; Bilevel multiobjective programming; Multiobjective particle swarm optimization algorithm with multiple social structures", "DOI": "10.1007/s40815-023-01483-4", "PubYear": 2023, "Volume": "25", "Issue": "5", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, University of Science and Technology Beijing, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Science and Technology Beijing, Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "Xiangbin Yan", "Affiliation": "School of Economics and Management, University of Science and Technology Beijing, Beijing, People’s Republic of China"}], "References": [{"Title": "A guided population archive whale optimization algorithm for solving multiobjective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "112972", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-objective artificial bee colony algorithm for multi-stage resource leveling problem in sharing logistics network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106338", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Aggregate production planning considering implementation error: A robust optimization approach using bi-level particle swarm optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "106367", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Omnichannel logistics network design with integrated customer preference for deliveries and returns", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "106433", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Integrated production planning and scheduling under uncertainty: A fuzzy bi-level decision-making approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "201-202", "Issue": "", "Page": "106056", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-Objective Multi-Skill Resource-Constrained Project Scheduling Problem Under Time Uncertainty", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "518", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Nonlinear bilevel programming approach for decentralized supply chain using a hybrid state transition algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "240", "Issue": "", "Page": "108119", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hybrid Fuzzy and Data-Driven Robust Optimization for Resilience and Sustainable Health Care Supply Chain with Vendor-Managed Inventory Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "1216", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 106521013, "Title": "Examining the effect of different knowledge aspects on information security awareness", "Abstract": "The purpose of this paper is to identify the different aspects of knowledge and how they associate with information security awareness (ISA). The paper also explores how ISA differs based on demographic characteristics.,Survey data was collected from 609 respondents in Malaysia.,The results show that increasing access to informal, multimedia learning mediums, declarative, schematic and strategic knowledge positively impacts an individual's ISA, whereas textual learning medium decreases the ISA. Respondents with different education levels significantly prefer different types of knowledge. Males learn better for ISA with schematic and strategic knowledge compared to females.,The research provides implications for governments and organizations in designing effective ISA campaigns.,Studies show that ISA is crucial in improving information systems policy compliance behavior. The literature has examined various topics ranging from the factors influencing the ISA to how ISA impacts information security behavior. However, there is a lack of study on how different aspects of knowledge impact ISA. This study identified various knowledge aspects from the literature and grouped them into the source, type of knowledge, emotion toward knowledge and learning medium.", "Keywords": "Information security awareness (ISA);Knowledge types;Source of knowledge;Type of knowledge;Medium of knowledge", "DOI": "10.1108/ICS-11-2022-0183", "PubYear": 2023, "Volume": "31", "Issue": "4", "JournalId": 31004, "JournalTitle": "Information and Computer Security", "ISSN": "2056-4961", "EISSN": "2056-497X", "Authors": [{"AuthorId": 1, "Name": "Hui Na Chua", "Affiliation": "Department of Computing and Information Systems, Sunway University , Bandar Sunway, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing and Information Systems, Sunway University , Bandar Sunway, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>’s Business School, Taylor’s University , Subang Jaya, Malaysia and Digital Innovation and Smart Society Impact Lab, Taylor’s University, Subang Jaya, Malaysia"}], "References": [{"Title": "More than the individual: Examining the relationship between culture and Information Security Awareness", "Authors": "<PERSON><PERSON> Wiley; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "101640", "JournalTitle": "Computers & Security"}, {"Title": "The effect of Fair information practices and data collection methods on privacy-related behaviors: A study of Mobile apps", "Authors": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "1", "Page": "103284", "JournalTitle": "Information & Management"}, {"Title": "Eyes wide open: The role of situational information security awareness for security‐related behaviour", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "3", "Page": "429", "JournalTitle": "Information Systems Journal"}, {"Title": "The effects of different personal data categories on information privacy concern and disclosure", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "102453", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 106521035, "Title": "An advanced intelligent MPPT control strategy based on the imperialist competitive algorithm and artificial neural networks", "Abstract": "<p>The development of a robust optimization control technique, which can handle numerous nonlinear system problems, is one of the most challenging aspects of science production. In this study, hybrid intelligent Maximum Power Point Tracking (MPPT) control approach based on the Imperialist Competitive Algorithm (ICA), and an adaptive Artificial Neural Network (ANN) model is proposed to solve the efficiency optimization problem of Photovoltaic (PV) systems, which are considered as one of the most demanded source energy in the world. Consequently, a comparison between various metaheuristic algorithms based ANN including, Particle Swarm Optimization, Grey Wolf Optimization, and Whale Optimization Algorithm, is made for four distinct PV panel architectures to prove the effectiveness of the suggested approach in the optimization process based ICA technique, and in the training phase based three training algorithms namely, Bayesian Regularization (BR), <PERSON><PERSON> Marquardt (LM), and Scaled Conjugate Gradient (SCG). Accordingly, the obtained outcomes have proven that the ICA–ANN approach-based BR algorithm outperformed in three of four cases the other techniques by reaching an accuracy that can go up to 99.9994%. In the second part of this study, the evaluation of the obtained findings confirmed that our proposed model was able to track the Maximum Power Point (MPP) faster with a response time between 1.9 and 9.6 ms, and efficiency higher than 99.9652%, which is can up to 99.9984%, and it has shown excellent remarkable stability compared to the Perturb & Observe, Incremental Conductance, and the most applied metaheuristic-based MPPT techniques that we have used to conduct the optimization performance comparison.</p>", "Keywords": "Artificial neural network; Metaheuristic algorithms; Imperialist competitive algorithm; Optimization; Training algorithms; Photovoltaic system; Maximum power point tracking", "DOI": "10.1007/s12065-023-00838-y", "PubYear": 2024, "Volume": "17", "Issue": "3", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science and Technology, Abdelmalek Essaadi University, Al Hoceima, Tetouan, Morocco; Corresponding author."}, {"AuthorId": 2, "Name": "Nabil El Akchioui", "Affiliation": "Faculty of Science and Technology, Abdelmalek Essaadi University, Al Hoceima, Tetouan, Morocco"}], "References": [{"Title": "Annealed gradient descent for deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "380", "Issue": "", "Page": "201", "JournalTitle": "Neurocomputing"}, {"Title": "An Intelligent Improvement Based on a Novel Configuration of Artificial Neural Network Model to Track the Maximum Power Point of a Photovoltaic Panel", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "2", "Page": "363", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": 106521101, "Title": "Efficient 2D DCT architecture based on approximate compressors for image compression with HEVC intra-prediction", "Abstract": "<p>This study presents a design of two-dimensional (2D) discrete cosine transform (DCT) architecture to be used with high-efficiency video coding (HEVC) intra-prediction method in image compression. Since the amount of calculation required by the transform step in HEVC is high and accordingly the power consumption is high, a novel DCT architecture for HEVC is proposed to reduce this calculation complexity and power consumption. This architecture is based on erroneous calculations in the steps, which can be ignored in the quantizing step. For this purpose, approximate 5:3 compressor circuits with different error rates are designed and used instead of addition/subtraction in DCT architecture. This DCT architecture is designed to support 4 × 4, 8 × 8, 16 × 16 and 32 × 32 transform blocks. The designed architecture is performed on FPGA and experiments are conducted. In these experiments, hardware performance parameters are examined, and it is proved that the use of approximate compressor can provide advantages on power consumption and physical area. The efficiency of the proposed architecture is investigated by performing image compression and video coding tests.</p>", "Keywords": "HEVC; Intra-prediction; Image compression; Approximate compressor; 2D DCT architecture", "DOI": "10.1007/s11554-023-01261-3", "PubYear": 2023, "Volume": "20", "Issue": "2", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, İstanbul Ticaret University, Istanbul, Turkey"}, {"AuthorId": 2, "Name": "Serap Cekli", "Affiliation": "Department of Electrical-Electronics Engineering, İstanbul University-Cerrahpaşa, Istanbul, Turkey"}], "References": [{"Title": "Efficient approximate core transform and its reconfigurable architectures for HEVC", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "2", "Page": "329", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "A novel architecture design for VLSI implementation of integer DCT in HEVC standard", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "Page": "23977", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106521136, "Title": "Siamese self-supervised learning for fine-grained visual classification", "Abstract": "Fine-grained visual classification (FGVC) is challenging to capture subtle yet distinct visual cues due to large intra-class and small inter-class variances. To this end, we propose a new Siamese Self-supervised Learning method to perform alignment between different views of one image. Specifically, we employ the attention mechanism to explore the semantic parts of one image, and then generate different views by crop and erase strategy. Meanwhile, we adopt the Siamese network to perform the feature alignment across various views and capture the view-invariant feature in a self-supervised way. Finally, we introduce the center loss to explicitly ensure consistency between different views. Extensive experimental results show the proposed method performs on par with the state-of-the-art methods on three public benchmarks including CUB-200-2011, FGVC-Aircraft, and Stanford Cars. Introduction The FGVC aims to classify sub-categories under the same super-category, for example, different species of birds and dogs. As a upstream research, FGVC has facilitated a wide variety of applications in the downstream tasks such as person re-identification (<PERSON><PERSON> et al., 2020, <PERSON> et al., 2021), instance segmentation (<PERSON> et al., 2021c) and emotion detection (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, 2017). Nevertheless, it remains a highly challenging task to date and has attracted extensive research attention in computer vision field over the past few years. As depicted in Fig. 1, FGVC is challenging due to two reasons: (1) high intra-class variances: the samples belonging to the same category usually present significant different appearance, (2) low inter-class variances: the samples belonging to different categories usually share similar appearance. Recently, considerable efforts have been made to alleviate these issues. A set of methods (<PERSON> et al., 2015a, <PERSON> et al., 2020, Zhuang et al., 2020, He et al., 2019) exemplify this research strand. In specific, B-CNN (Lin et al., 2015a) utilizes the bilinear pooling to encode features generated by parallel extractors to form richer representations. MC-Loss (Chang et al., 2020) takes advantage of two channel-wise modules to excavate the discriminability and diversity of features within a single network. However, the aforementioned methods fall short in ensuring the consistency of feature distribution in each category explicitly, resulting in degraded performance. To address this issue, we propose a novel end-to-end Siamese Self-supervised Learning for the FGVC task in this study. The proposed method is constructed on the concept of Siamese architecture. Driven by the advantages of Siamese network, we apply it to encode view-invariant features and align features of different views of one image. Moreover, we impose the consistency constraint on features of different views to prevent them from contradicting each other. In short, the contributions of this paper are summarized as follows. • To alleviate the issue of large intra-class and small inter-class variances, a new Siamese Self-supervised Learning (SSSL) method is proposed for the FGVC task, where we utilize Siamese architecture with shared parameters to encode the feature from different views and guide model to learn view-invariant features in a self-supervised learning way. • To our best knowledge, we are the first to introduce self- supervised learning to ensure the consistency between different views of an image explicitly and the proposed model relies on the positive pairs for optimization, without the limitation of batch size. • The proposed method performs favorably against the state-of-the-art methods on three challenging FGVC benchmarks including CUB-200-2011, FGVC-Aircraft and Stanford Cars. Comprehensive ablative studies are provided to shed light on the effectiveness of model design choices. Section snippets Fine-grained visual categorization It is vital to characterize the detailed visual cues for the fine-grained visual classification. Early, prior arts (Zhang et al., 2014, Zhang et al., 2016, Lin et al., 2015b, Huang et al., 2016) directly guide models to capture discrepancies in subtle regions with the supervision of object- or part-level annotations. However, such annotation requires expertise and is labor-intensive, making these methods inapplicable for real scenarios. Later, developments over this task shift towards weakly Method In this section, we describe the rationale behind Siamese Self-supervised Learning for the FGVC task in detail. As shown in Fig. 2, the proposed network is mainly composed of the following three components: siamese encoder, self-supervised learning, and loss function. First, the siamese encoder is used to extract latent features from raw image. Then, we perform cropping and erasing operations on the high response areas to form the different semantic views. Meanwhile, we explicitly enforce Implementation details We utilize PyTorch (Paszke et al., 2017) as default deep learning framework to implement the proposed model and the entire model is trained on a workstation with a 2.20 GHz Intel processor, 1 Tesla V 100 GPU and 32 GB memory. Following common protocol in FGVC task, we adopt a truncated model pre-trained on ILSVRC CLSLOC (Russakovsky et al., 2015) as backbone. The input image is resized to be 448 × 448 with color jittering. Random rotation and random horizontal flip are applied for data Conclusion In this paper, we propose a Siamese Self-supervised Learning for the fine-grained visual classification task. Specifically, different semantic views of an image are generated by the strategy of cropping and erasing under the guidance of attention mechanism and aligned to learn view-invariant representation by the Siamese architecture with shared parameters. The whole network is optimized by stochastic gradient decent algorithm in an end-to-end manner. Extensive experimental results conducted on Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgment All authors approved the version of the manuscript to be published. This work was supported by the Key Research Program of Frontier Sciences, CAS , Grant No. ZDBS-LY-JSC038 . Libo Zhang was supported by Youth Innovation Promotion Association, CAS ( 2020111 ), and Outstanding Youth Scientist Project of ISCAS . References (68) Abdul-Mageed M. et al. EmoNet: Fine-grained emotion detection with gated recurrent neural networks Chang D. et al. The Devil is in the channels: Mutual-channel loss for fine-grained image classification IEEE Trans. Image Process. (2020) Chang, D., Pang, K., Zheng, Y., Ma, Z., Song, Y., Guo, J., 2021. Your “Flamingo” is My “Bird”: Fine-Grained, or Not.... Chen T. et al. A simple framework for contrastive learning of visual representations Chen, T., Kornblith, S., Swersky, K., Norouzi, M., Hinton, G.E., 2020b. Big Self-Supervised Models are Strong... Chen X. et al. An empirical study of training self-supervised vision transformers, CoRR abs/2104.02057 (2021) Chopra, S., Hadsell, R., LeCun, Y., 2005. Learning a Similarity Metric Discriminatively, with Application to Face... Cubuk E.D. et al. AutoAugment: Learning augmentation policies from data, CoRR abs/1805.09501 (2018) Ding Y. et al. AP-CNN: Weakly supervised attention pyramid convolutional neural network for fine-grained visual classification IEEE Trans. Image Process. (2021) Ding, Y., Zhou, Y., Zhu, Y., Ye, Q., Jiao, J., 2019. Selective Sparse Sampling for Fine-Grained Image Recognition. In:... Du R. et al. Fine-grained visual classification via progressive multi-granularity training of jigsaw patches Dubey A. et al. Pairwise confusion for fine-grained visual classification Fu, J., Zheng, H., Mei, T., 2017. Look Closer to See Better: Recurrent Attention Convolutional Neural Network for... Gao, Y., Han, X., Wang, X., Huang, W., Scott, M.R., 2020. Channel Interaction Networks for Fine-Grained Image... Ge, W., Lin, X., Yu, Y., 2019. Weakly Supervised Complementary Parts Models for Fine-Grained Image Classification From... Glorot X. et al. Deep sparse rectifier neural networks Grill, J., Strub, F., Altché, F., Tallec, C., Richemond, P.H., Buchatskaya, E., Doersch, C., Pires, B.Á., Guo, Z.,... Hadsell R. et al. Dimensionality reduction by learning an invariant mapping Hanselmann H. et al. ELoPE: Fine-grained visual classification with efficient localization, pooling and embedding He K. et al. Momentum contrast for unsupervised visual representation learning He X. et al. Fast fine-grained image classification via weakly supervised discriminative localization IEEE Trans. Circuits Syst. Video Technol. (2019) He K. et al. Deep residual learning for image recognition, CoRR abs/1512.03385 (2015) Hong P. et al. Fine-grained shape-appearance mutual learning for cloth-changing person re-identification Hu T. et al. See better before looking closer: Weakly supervised data augmentation network for fine-grained visual classification, CoRR abs/1901.09891 (2019) Hu, J., Shen, L., Sun, G., 2018. Squeeze-and-Excitation Networks. In: CVPR. pp.... Huang S. et al. SnapMix: Semantically proportional mixing for augmenting fine-grained data, CoRR abs/2012.04846 (2020) Huang, S., Xu, Z., Tao, D., Zhang, Y., 2016. Part-Stacked CNN for Fine-Grained Visual Categorization. In: CVPR. pp.... Ioffe S. et al. Batch normalization: Accelerating deep network training by reducing internal covariate shift Ji, R., Wen, L., Zhang, L., Du, D., Wu, Y., Zhao, C., Liu, X., Huang, F., 2020. Attention Convolutional Binary Neural... Leng Q. et al. A survey of open-world person re-identification IEEE Trans. Circuits Syst. Video Technol. (2020) Li, P., Xie, J., Wang, Q., Gao, Z., 2018. Towards Faster Training of Global Covariance Pooling Networks by Iterative... Li, H., Zhang, X., Tian, Q., Xiong, H., 2020. Attribute Mix: Semantic Data Augmentation for Fine Grained Recognition.... Lin, T., Maji, S., 2017. Improved Bilinear Pooling with CNNs. In:... Lin, T., RoyChowdhury, A., Maji, S., 2015a. Bilinear CNN Models for Fine-Grained Visual Recognition. In: ICCV. pp.... View more references Cited by (0) Recommended articles (0) View full text © 2023 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cviu.2023.103658", "PubYear": 2023, "Volume": "229", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Software Chinese Academy of Sciences, Beijing, 100190, China;University of Chinese Academy of Sciences, Beijing, 100049, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Information Science and Technology University, Beijing, 100192, China"}, {"AuthorId": 3, "Name": "Libo Zhang", "Affiliation": "Institute of Software Chinese Academy of Sciences, Beijing, 100190, China;University of Chinese Academy of Sciences, Beijing, 100049, China;Nanjing Institute of Software Technology, Beijing, 210000, China;Corresponding author"}], "References": []}, {"ArticleId": 106521177, "Title": "Continuous improvement of self-driving cars using dynamic confidence-aware reinforcement learning", "Abstract": "Today’s self-driving vehicles have achieved impressive driving capabilities, but still suffer from uncertain performance in long-tail cases. Training a reinforcement-learning-based self-driving algorithm with more data does not always lead to better performance, which is a safety concern. Here we present a dynamic confidence-aware reinforcement learning (DCARL) technology for guaranteed continuous improvement. Continuously improving means that more training always improves or maintains its current performance. Our technique enables performance improvement using the data collected during driving, and does not need a lengthy pre-training phase. We evaluate the proposed technology both using simulations and on an experimental vehicle. The results show that the proposed DCARL method enables continuous improvement in various cases, and, in the meantime, matches or outperforms the default self-driving policy at any stage. This technology was demonstrated and evaluated on the vehicle at the 2022 Beijing Winter Olympic Games. Reinforcement learning is a powerful technique to learn complex behaviours, but in the context of self-driving vehicles it might result in unsafe behaviour in previously unseen situations. <PERSON> et al. create a confidence-aware method that improves through reinforcement learning but reverts to safe behaviour when a situation is new.", "Keywords": "Industry;Mechanical engineering;Technology;Engineering;general", "DOI": "10.1038/s42256-023-00610-y", "PubYear": 2023, "Volume": "5", "Issue": "2", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Vehicle and Mobility, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Vehicle and Mobility, Tsinghua University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Vehicle and Mobility, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Vehicle and Mobility, Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering, University of Michigan, Ann Arbor, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Vehicle and Mobility, Tsinghua University, Beijing, China"}], "References": [{"Title": "Using online verification to prevent autonomous vehicles from causing accidents", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "9", "Page": "518", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 106521214, "Title": "Performance analysis of multiple input single layer neural network hardware chip", "Abstract": "<p>An artificial neural network (ANN) is a computational system that is designed to replicate and process the behavior of the human brain using neuron nodes. ANNs are made up of thousands of processing neurons with input and output modules that self-learn and compute data to offer the best results. The hardware realization of the massive neuron system is a difficult task. The research article emphasizes the design and realization of multiple input perceptron chips in Xilinx integrated system environment (ISE) 14.7 software. The proposed single-layer ANN architecture is scalable and accepts variable 64 inputs. The design is distributed in eight parallel blocks of ANN in which one block consists of eight neurons. The performance of the chip is analyzed based on the hardware utilization, memory, combinational delay, and different processing elements with targeted hardware Virtex-5 field-programmable gate array (FPGA). The chip simulation is performed in Modelsim 10.0 software. Artificial intelligence has a wide range of applications, and cutting-edge computing technology has a vast market. Hardware processors that are fast, affordable, and suited for ANN applications and accelerators are being developed by the industries. The novelty of the work is that it provides a parallel and scalable design platform on FPGA for fast switching, which is the current need in the forthcoming neuromorphic hardware.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2023, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "ANN architecture;Single layer ANN;Virtex-5 FPGA", "DOI": "10.1007/s11042-023-14627-3", "PubYear": 2023, "Volume": "82", "Issue": "18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Galgotia's University, Greater Noida, NCR India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Galgotia's University, Greater Noida, NCR India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronics Engineering, University of Petroleum and Energy Studies, Dehradun, India."}], "References": [{"Title": "A smart healthcare monitoring system for heart disease prediction based on ensemble deep learning and feature fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "208", "JournalTitle": "Information Fusion"}, {"Title": "Performance analysis of DSDV and OLSR wireless sensor network routing protocols using FPGA hardware and machine learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "14", "Page": "22301", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Performance analysis of all-optical logical gate using artificial neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "115029", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An AW-HARIS Based Automated Segmentation of Human Liver Using CT Images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3303", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Performance Analysis of AODV Routing for Wireless Sensor Network in FPGA Hardware", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "3", "Page": "1073", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": *********, "Title": "Interview of <PERSON>", "Abstract": "<PERSON> was born in Hastings, Nebraska, in 1938. He received a B.S. degree from the Carnegie Institute of Technology (1959) in electrical engineering, an M.S. degree from Caltech (1960), and a Ph.D. in electrical engineering from MIT (1963). <PERSON>'s dissertation, Sketchpad: A Man Machine Graphical Communication System, described a groundbreaking interactive computer-aided design system, one whose graceful interaction and functionality inspire admiration among computer graphics professionals even today. From 1964 to 1966, <PERSON> was director of the Information Processing Techniques Office of the Advanced Research Projects Agency (ARPA), where he guided computer research across the United States. In 1968, he joined colleague <PERSON> to build a center of computer graphics research at the University of Utah and to found with him the Evans and Sutherland Computer Corporation. From 1976 to 1980, <PERSON> served as a Chairman of computer science at Caltech, and from 1980 onwards, was a Vice President of the consulting firm Sutherland, Sproull and Associates, acquired by Sun Microsystems in 1990 to form the basis of its corporate research laboratory. <PERSON> has won many awards for his work, including the ACM Turing Award (1988) and the IEEE John von Neumann Medal (1998). He is a Fellow of the Computer History Museum.", "Keywords": "", "DOI": "10.1109/MAHC.2023.3244258", "PubYear": 2023, "Volume": "45", "Issue": "1", "JournalId": 33128, "JournalTitle": "IEEE Annals of the History of Computing", "ISSN": "1058-6180", "EISSN": "1934-1547", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Massachusetts, Amherst, MA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer History Museum, Mountain View, CA, USA"}], "References": []}, {"ArticleId": 106521399, "Title": "Assessing Students' Perceptions of Mobile Applications Usability using System Usability Scale", "Abstract": "The purpose of this study is to explore the usability level of the mobile application from the perspectives of students at higher education institutes by using the most used measure of perceived usability, namely, the System Usability Scale (SUS). Furthermore, the study investigated the effect of some of the students’ demographic attributes on mobile application usability. Results revealed that the system usability scale score is 63% which indicated that the mobile application usability is inadequate and requires more attention. Moreover, the results indicated that there is a significant impact of students’ gender on system usability scale scores; while other demographic attributes such as study level, department, usage experience, and device type, did not have impacts on System Usability Scale scores. The results of the study will be helpful to policymakers, top management, and mobile applications developers, in designing mobile applications with high quality in terms of its usability © 2023 Mohammed Hameed Afif. This open-access article is distributed under a Creative Commons Attribution (CC-BY) 4.0 license", "Keywords": "Assessment; Higher education; Mobile application; Systems usability scale (sus); Usability", "DOI": "10.3844/jcssp.2023.11.19", "PubYear": 2023, "Volume": "19", "Issue": "1", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Management Information Systems, College of Business Administration, Prince <PERSON> University, Saudi Arabia"}], "References": []}, {"ArticleId": 106521518, "Title": "Generalized Truncated Moment Problems with Unbounded Sets", "Abstract": "<p>This paper studies generalized truncated moment problems with unbounded sets. First, we study geometric properties of the truncated moment cone and its dual cone of nonnegative polynomials. By the technique of homogenization, we give a convergent hierarchy of Moment-SOS relaxations for approximating these cones. With them, we give a Moment-SOS method for solving generalized truncated moment problems with unbounded sets. Finitely atomic representing measures, or certificates for their nonexistence, can be obtained by the proposed method. Numerical experiments and applications are also given.</p>", "Keywords": "Moment; Polynomial; Truncated moment problem; Homogenization; Moment-SOS relaxation; 90C23; 90C22; 44A60; 47A57", "DOI": "10.1007/s10915-023-02139-z", "PubYear": 2023, "Volume": "95", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computational Mathematics and Scientific/Engineering Computing, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing, China; School of Mathematical Sciences, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of California San Diego, La Jolla, USA"}, {"AuthorId": 3, "Name": "Ya-<PERSON><PERSON>", "Affiliation": "Institute of Computational Mathematics and Scientific/Engineering Computing, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing, China"}], "References": []}, {"ArticleId": 106521528, "Title": "Efficient feature selection using BoWs and SURF method for leaf disease identification", "Abstract": "<p>When plant diseases appear, they have an adverse effect on agricultural output. Food insecurity would worsen if plant diseases were not identified accurately in time. Without early identification of plant diseases, agricultural production management and decision-making would not be feasible. Now a days deep learning model is widely used for image classification to get more accurate result. But machine learning based classifier also produces good result if good feature selection technique is used. In this paper, we have used hybrid machine learning techniques for classifying leaf diseases present in species such as tomato, potato and pepper bell. We have used bag-of- feature for visually representing diseased leaf features. SURF technique is used to extract strongest number of features and for classification task SVM is used. The proposed method gives good result in terms of precision, accuracy, recall, F1-score, FPR, FNR, and MCC on all the three employed datasets. The classification accuracy obtained in our proposed model on dataset1, dataset2 and dataset3 are 97%, 97% and 93% respectively. We have also calculated percentage feature reduction for all three types of species which are approximately 45.21%, 40.65% and 34.73% for tomato, potato and pepper bell respectively. We have compared disease classification accuracy of several previous work done by various authors on tomato leaf dataset using various machine learning and deep learning technique with our proposed work and find that our method is performing better.</p>", "Keywords": "Leaf disease; BoWs; SURF; K-means; Features; SVM", "DOI": "10.1007/s11042-023-14625-5", "PubYear": 2023, "Volume": "82", "Issue": "18", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, National Institute of Technology Jamshedpur, Jamshedpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, National Institute of Technology Jamshedpur, Jamshedpur, India"}], "References": [{"Title": "Fungi affected fruit leaf disease classification using deep CNN architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "3815", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A comprehensive survey on leaf disease identification & classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "23", "Page": "33897", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106521546, "Title": "Properties evaluation of chemically treated TPU parts printed through FFF", "Abstract": "<p>Fused filament fabrication (FFF) is the fast-growing additive manufacturing (AM) technology that enables the fabrication of complex 3D models using polymers at low cost. However, the inferior surface quality and high layer resolution of the build parts has limited its real-life application. Chemical treatment process is one of the low costs and highly effective post processing method which can be used for eliminating this disadvantage. It improves the surface finish of FFF build parts by dissolving the layers of the surface exposed to the chemical environment. The present research work investigated the effect of chemical treatment on the compressive strength, surface roughness and dimensional accuracy of the FFF printed flexible parts fabricated with thermoplastic polyurethane (TPU) filament. Two chemicals dimethyl formamide (DMF) and dimethyl sulfoxide (DMSO) have been used for the chemical treatment process. It has been observed that samples treated with DMSO exhibited better compressive strength and surface quality in comparison to DMF. A case study has been done to implement the outcome of the present investigation in real-life application.</p>", "Keywords": "FFF; TPU; Flexible parts; Chemical treatment; Compressive strength; Surface roughness; Dimensional accuracy", "DOI": "10.1007/s00170-023-11111-y", "PubYear": 2023, "Volume": "126", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Di<PERSON>", "Affiliation": "FFF Lab, Mechanical Engineering Department, PDPM Indian Institute of Information Technology, Design and Manufacturing, Jabalpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "FFF Lab, Mechanical Engineering Department, PDPM Indian Institute of Information Technology, Design and Manufacturing, Jabalpur, India"}], "References": [{"Title": "Tensile properties of the FFF-processed thermoplastic polyurethane (TPU) elastomer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "5-6", "Page": "1709", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Laser cutting of 3D printed acrylonitrile butadiene styrene plates for dimensional and surface roughness optimization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "3-4", "Page": "2301", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Hybrid 3D printing of multifunctional polylactic acid/carbon black nanocomposites made with material extrusion and post-processed with CO2 laser cutting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "5-6", "Page": "1843", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 106521613, "Title": "Analysis of Government Policy Sentiment Regarding Vacation during the COVID-19 Pandemic Using the Bidirectional Encoder Representation from Transformers (BERT)", "Abstract": "<p>To address the COVID-19 situation in Indonesia, the Indonesian government has adopted a number of policies. One of them is a vacation-related policy. Government measures with regard to this vacation policy have produced a wide range of viewpoints in society, which have been extensively shared on social media, including YouTube. However, there has not been any computerized system developed to date that can assess people’s social media reactions. Therefore, this paper provides a sentiment analysis application to this government policy by employing a bidirectional encoder representation from transformers (BERT) approach. The study method began with data collecting, data labeling, data preprocessing, BERT model training, and model evaluation. This study created a new dataset for this topic. The data were collected from the comments section of YouTube, and were categorized into three categories: positive, neutral, and negative. This research yielded an F-score of 84.33%. Another contribution from this study regards the methodology for processing sentiment analysis in Indonesian. In addition, the model was created as an application using the Python programming language and the Flask framework. The government can learn the extent to which the public accepts the policies that have been implemented by utilizing this research.</p>", "Keywords": "", "DOI": "10.3390/data8030046", "PubYear": 2023, "Volume": "8", "Issue": "3", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "Intan Nurma Yulit<PERSON>", "Affiliation": "Research Center for Artificial Intelligence and Big Data, Universitas Padjadjaran, Bandung 40132, Indonesia; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematics and Natural Sciences, Universitas Padjadjaran, Sumedang 45363, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Mathematics and Natural Sciences, Universitas Padjadjaran, Sumedang 45363, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Cultural Sciences, Universitas Padjadjaran, Sumedang 45363, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Social and Political Science, Universitas Padjadjaran, Sumedang 45363, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Faculty of Computing and Information Technology in Rabigh, King Abdulaziz University, Rabigh 21911, Saudi Arabia"}], "References": [{"Title": "A comprehensive study for Arabic Sentiment Analysis (Challenges and Applications)", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "1", "Page": "7", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "Sentiment analysis for mining texts and social networks data: Methods and tools", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "A Hitchhiker’s Guide On Distributed Training Of Deep Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "65", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Sentiment analysis using deep learning architectures: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "6", "Page": "4335", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Sentiment analysis of Twitter data during critical events through Bayesian networks classifiers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "92", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A review of sentiment analysis research in Arabic language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "408", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep Learning for Source Code Modeling and Generation", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep learning CNN–LSTM framework for Arabic sentiment analysis using textual information shared in social networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "A survey of sentiment analysis in the Portuguese language", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "2", "Page": "1087", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Sentiment analysis and its applications in fighting COVID-19 and infectious diseases: A systematic review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114155", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Topic detection and sentiment analysis in Twitter content related to COVID-19 from Brazil and the USA", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107057", "JournalTitle": "Applied Soft Computing"}, {"Title": "Scalable multi-channel dilated CNN–BiLSTM model with attention mechanism for Chinese textual sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "297", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Joint evaluation of preprocessing tasks with classifiers for sentiment analysis in Brazilian Portuguese language", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "10", "Page": "15391", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Transformer models for text-based emotion detection: a review of BERT-based approaches", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "8", "Page": "5789", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Bidirectional Language Modeling: A Systematic Literature Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "Twitter Sentiment Analysis towards COVID-19 Vaccines in the Philippines Using Naïve Bayes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "204", "JournalTitle": "Information"}, {"Title": "A comprehensive survey on sentiment analysis: Approaches, challenges and trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "226", "Issue": "", "Page": "107134", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Text Data Augmentation for Deep Learning", "Authors": "<PERSON>; <PERSON><PERSON>; Bork<PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "101", "JournalTitle": "Journal of Big Data"}, {"Title": "A Review of Urdu Sentiment Analysis with Multilingual Perspective: A Case of Urdu and Roman Urdu Language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "3", "JournalTitle": "Computers"}, {"Title": "A survey on sentiment analysis methods, applications, and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5731", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Efficient Transformers: A Survey", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Transformer models used for text-based question answering systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "9", "Page": "10602", "JournalTitle": "Applied Intelligence"}, {"Title": "A Survey of Cross-lingual Sentiment Analysis: Methodologies, Models and Evaluations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "279", "JournalTitle": "Data Science and Engineering"}]}, {"ArticleId": 106521672, "Title": "Splitting ore from X-ray image based on improved robust concave-point algorithm", "Abstract": "<p>Image segmentation is a key part of ore separation process based on X-ray images, and its segmentation result directly affects the accuracy of ore classification. In the field of ore production, the conventional segmentation method is difficult to meet the requirements of real-time, robustness and accuracy during ore segmentation process. In order to solve the above problems, this article proposes an ore segmentation method dealing with pseudo-dual-energy X-ray image which is composed of contour extraction module, concave point detection module and concave point matching module. In the contour extraction module, the image is firstly cut into two parts with high and low energy, then the adaptive threshold is used to obtain the ore binary image. After filtering and morphological operation, the image contour is obtained from the binary image. Concave point detection module uses vector to detect concave points on contour. As the main contribution of this article, the concave point matching module can remove the influence of boundary interference concave points by drawing the auxiliary line and judging the relative position of auxiliary line and ore contour. With the matching concave points connected, the whole ore segmentation is completed. In order to verify the effectiveness of this method, a comparative experiment was conducted between the proposed method and conventional segmentation method using X-ray images of antimony ore as data samples. The result of industrial experiment shows that the proposed intelligent segmentation method can remove the interference of pseudo concave points on the contour, achieve accuracy segmentation result, and satisfy the requirements of processing X-ray image of ore.</p>", "Keywords": "Concave point matching;Image segmentation;Ore separation", "DOI": "10.7717/peerj-cs.1263", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center of Coal Preparation and Purification, China University of Mining Technology, Xuzhou, China"}, {"AuthorId": 2, "Name": "Hongdong Hu", "Affiliation": "School of Information and Control Engineering, China University of Mining Technology, Xuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LONGi Magnet Co., LTD., Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining Technology, Xuzhou, China"}], "References": []}, {"ArticleId": 106521706, "Title": "AIR QUALITY DETECTION AND MON<PERSON>ORING WITH VISUALISED ANANLYSIS OF PARTICULATE MATTER USING RASPBERRY PI", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2023.12237", "PubYear": 2023, "Volume": "12", "Issue": "2", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106521793, "Title": "Crowdsourcing to predict RNA degradation and secondary structure", "Abstract": "Predicting RNA degradation is a fundamental task in designing RNA-based therapeutic agents. Dual crowdsourcing efforts for dataset creation and machine learning were organized to learn biological rules and strategies for predicting RNA stability.", "Keywords": "RNA;RNA decay;Engineering;general", "DOI": "10.1038/s42256-023-00615-7", "PubYear": 2023, "Volume": "5", "Issue": "2", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Biochemistry and Biophysics, School of Electrical Engineering and Computer Science, Oregon State University, Corvallis, USA"}], "References": [{"Title": "Deep multi-scale attention network for RNA-binding proteins prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "582", "Issue": "", "Page": "287", "JournalTitle": "Information Sciences"}, {"Title": "Deep learning models for predicting RNA degradation via dual crowdsourcing", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "12", "Page": "1174", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 106521804, "Title": "Joint optimization strategy of offloading in multi-UAVs-assisted edge computing networks", "Abstract": "<p>Mobile edge computing (MEC) has been developed to solve the problem of insufficient computing resource of edge user devices. However, it is common that the buildings obstacle information transmission. Motivated by the high flexibility of unmanned aerial vehicles (UAVs), we explore a multi-UAVs-assisted MEC system, in which UAVs play two roles, i.e., offering computation resources or acting as relay nodes, to reduce total consumption of edge computing network in terms of time and energy. Accordingly, an optimization problem is formulated to minimize the total energy consumption of the MEC system. The problem is further formulated as a markov decision process and two reinforcement learning methods: Q-learning based method and dueling deep reinforcement learning based method, are proposed to obtain the optimal policies for computation offloading and resource allocation. Finally, the numerical results in the edge computing network are given to show the effectiveness of the proposed methods.</p>", "Keywords": "Mobile edge computing; Dueling deep reinforcement learning; Unmanned aerial vehicles; Joint optimization", "DOI": "10.1007/s12652-023-04558-0", "PubYear": 2023, "Volume": "14", "Issue": "4", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Internet of Things Engineering, Jiangnan University, Wuxi, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Internet of Things Engineering, Jiangnan University, Wuxi, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Internet of Things Engineering, Jiangnan University, Wuxi, People’s Republic of China"}], "References": [{"Title": "Agent-enabled task offloading in UAV-aided mobile edge computing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "324", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 106521835, "Title": "The cost/benefit strategic grid approach: a decision support method for design and improvement of configuration management databases", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJISCM.2022.10054382", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 31255, "JournalTitle": "International Journal of Information Systems and Change Management", "ISSN": "1479-3121", "EISSN": "1479-313X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106521973, "Title": "Performance improvement for additive light field displays with weighted simultaneous algebra reconstruction technique and tracked views", "Abstract": "<p>Additive light field displays are transparent autostereoscopic three-dimensional displays without backlights, thus suitable for augmented reality applications. However, when the parallax between viewpoint images becomes large with the increase of viewing angle, the optimization algorithm is hard to handle too many dissimilarities evenly on all viewpoints, resulting in poor reconstructed quality. This paper presents an additive light field display using the weighted simultaneous algebraic reconstruction technique with viewing angle-dependent weight distribution functions. We constrain the optimization to deliver a reconstructed light field of high image quality for viewpoints of large weight. When the proposed method is applied, with a wide dynamic viewing angle of 57° × 43°, the tracked views' peak signal-to-noise ratio exceeds 30 dB with only two additive display layers.</p>", "Keywords": "additive light field display;weighted simultaneous algebraic reconstruction technique;wide viewing angle", "DOI": "10.1002/jsid.1194", "PubYear": 2023, "Volume": "31", "Issue": "4", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Modern Optical Instrumentation, College of Optical Science and Engineering Zhejiang University  Hangzhou China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Modern Optical Instrumentation, College of Optical Science and Engineering Zhejiang University  Hangzhou China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Modern Optical Instrumentation, College of Optical Science and Engineering Zhejiang University  Hangzhou China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Modern Optical Instrumentation, College of Optical Science and Engineering Zhejiang University  Hangzhou China"}, {"AuthorId": 5, "Name": "Haifeng Li", "Affiliation": "State Key Laboratory of Modern Optical Instrumentation, College of Optical Science and Engineering Zhejiang University  Hangzhou China;Research Center for Intelligent Chips and Devices Zhejiang Lab  Hangzhou China"}], "References": [{"Title": "Review on tabletop true 3D display", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "1", "Page": "75", "JournalTitle": "Journal of the Society for Information Display"}, {"Title": "Project starline: a high-fidelity telepresence system", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 106521975, "Title": "Evolutionary multiobjective optimization via efficient sampling-based offspring generation", "Abstract": "With the rising number of large-scale multiobjective optimization problems from academia and industries, some evolutionary algorithms (EAs) with different decision variable handling strategies have been proposed in recent years. They mainly emphasize the balance between convergence enhancement and diversity maintenance for multiobjective optimization but ignore the local search tailored for large-scale optimization. Consequently, most existing EAs can hardly obtain the global or local optima. To address this issue, we propose an efficient sampling-based offspring generation method for large-scale multiobjective optimization, where convergence enhancement and diversity maintenance, together with ad hoc local search, are considered. First, the decision variables are dynamically classified into two types for solving large-scale decision space in a divide-and-conquer manner. Then, a convergence-related sampling strategy is designed to handle those decision variables related to convergence enhancement. Two additional sampling strategies are proposed for diversity maintenance and local search, respectively. Experimental results on problems with up to 5000 decision variables have indicated the effectiveness of the algorithm in large-scale multiobjective optimization.", "Keywords": "Large-scale optimization; Multiobjective optimization; Evolutionary algorithm; Sampling", "DOI": "10.1007/s40747-023-00990-z", "PubYear": 2023, "Volume": "9", "Issue": "5", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Automation, Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Brain-inspired Intelligent Computation, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Chair of Nature Inspired Computing and Engineering, Bielefeld University, Bielefeld, Germany"}], "References": [{"Title": "Solving large-scale many-objective optimization problems by covariance matrix adaptation evolution strategy with scalable small subpopulations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "457", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 106522001, "Title": "Hybrid optimization enabled multi‐aggregator‐based charge scheduling of electric vehicle in internet of electric vehicles", "Abstract": "<p>In modern days, electric vehicles are quickly industrialized as well as their penetration is also increased highly, which brings more challenges for the power system. The electric vehicle charge scheduling process is vital to encourage the daily usage of the electric vehicle. However, irregular charging methods for electric vehicles may disturb voltage security areas because of their stochastic characteristics. Moreover, an electric vehicle requires recurrent charging owing to its constrained battery capacity, but it is a time-consuming process. In this article, an effective charge scheduling model is devised using the fractional social sea lion optimization (Fr-SSLO) algorithm. At first, IoEV network is simulated along with charge station and electric vehicle location. Furthermore, multi aggregator-based charge scheduling is done for increasing the profit and amount of scheduled electric vehicles. Then, routing is performed based on developed Fr-SSLO algorithm. Moreover, several fitness measures, including distance, energy and variable energy purchase are included. Here, the devised Fr-SSLO model is designed by integrating fractional calculus (FC) and sea lion optimization (SLnO) technique along with SOA. After the completion of routing process, charge scheduling is performed based on developed Fr-SSLO approach. Moreover, various fitness functions are also considered for computing better performance.</p>", "Keywords": "charging scheduling;electric vehicles;fractional calculus;sea lion optimization algorithm;social optimization algorithm", "DOI": "10.1002/cpe.7654", "PubYear": 2023, "Volume": "35", "Issue": "9", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "Pandian Suresh", "Affiliation": "Department of EEE St. Joseph College of Engineering, Sriperumbudur  Chennai India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of EEE Prathyusha Engineering College  Thiruvallur India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of EEE SRM Institute of Science And Technology, Ramapuram Campus  Chennai India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechatronics Engineering SRM Institute of Science and Technology, Kattankulathur  Chengalpattu India"}], "References": [{"Title": "MapReduce and Optimized Deep Network for Rainfall Prediction in Agriculture", "Authors": "Oswalt Manoj S; Ananth J P", "PubYear": 2020, "Volume": "63", "Issue": "6", "Page": "900", "JournalTitle": "The Computer Journal"}, {"Title": "Object Detection and Localization Using Sparse-FCM and Optimization-driven Deep Convolutional Neural Network", "Authors": "<PERSON> <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "5", "Page": "1225", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 106522071, "Title": "Pushing AI to wireless network edge: an overview on integrated sensing, communication, and computation towards 6G", "Abstract": "<p>Pushing artificial intelligence (AI) from central cloud to network edge has reached board consensus in both industry and academia for materializing the vision of artificial intelligence of things (AIoT) in the sixth-generation (6G) era. This gives rise to an emerging research area known as edge intelligence, which concerns the distillation of human-like intelligence from the vast amount of data scattered at the wireless network edge. Typically, realizing edge intelligence corresponds to the processes of sensing, communication, and computation, which are coupled ingredients for data generation, exchanging, and processing, respectively. However, conventional wireless networks design the three mentioned ingredients separately in a task-agnostic manner, which leads to difficulties in accommodating the stringent demands of ultra-low latency, ultra-high reliability, and high capacity in emerging AI applications like auto-driving and metaverse. This thus prompts a new design paradigm of seamlessly integrated sensing, communication, and computation (ISCC) in a task-oriented manner, which comprehensively accounts for the use of the data in downstream AI tasks. In view of its growing interest, this study provides a timely overview of ISCC for edge intelligence by introducing its basic concept, design challenges, and enabling techniques, surveying the state-of-the-art advancements, and shedding light on the road ahead.</p>", "Keywords": "sixth-generation (6G); edge intelligence; artificial intelligence of things (AIoT); integrated sensing; communication; and computation (ISCC)", "DOI": "10.1007/s11432-022-3652-2", "PubYear": 2023, "Volume": "66", "Issue": "3", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute of Big Data, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute of Big Data, Shenzhen, China; Future Network of Intelligence Institute (FNii), The Chinese University of Hong Kong (Shenzhen), Shenzhen, China; School of Science and Engineering (SSE), The Chinese University of Hong Kong (Shenzhen), Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute of Big Data, Shenzhen, China; State Key Laboratory of Advanced Optical Communication Systems and Networks, School of Electronics, Peking University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute of Big Data, Shenzhen, China; State Key Laboratory of Advanced Optical Communication Systems and Networks, School of Electronics, Peking University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering and Institute for Data Science and Computing, University of Miami, Coral Gables, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Future Network of Intelligence Institute (FNii), The Chinese University of Hong Kong (Shenzhen), Shenzhen, China; School of Science and Engineering (SSE), The Chinese University of Hong Kong (Shenzhen), Shenzhen, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shenzhen Research Institute of Big Data, Shenzhen, China; Future Network of Intelligence Institute (FNii), The Chinese University of Hong Kong (Shenzhen), Shenzhen, China; School of Science and Engineering (SSE), The Chinese University of Hong Kong (Shenzhen), Shenzhen, China; Peng Cheng Laboratory, Shenzhen, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing, China; Peng Cheng Laboratory, Shenzhen, China"}], "References": [{"Title": "Towards 6G wireless communication networks: vision, enabling technologies, and new paradigm shifts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "1", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Training time minimization for federated edge learning with optimized gradient quantization and bandwidth allocation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "8", "Page": "1247", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}]