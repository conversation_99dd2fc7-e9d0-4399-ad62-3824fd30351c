import requests
from bs4 import BeautifulSoup
import csv
import time
import random

def get_top_movies(num_pages=10):
    """
    Scrape the top 250 movies from Douban.
    
    Args:
        num_pages: Number of pages to scrape (25 movies per page)
    
    Returns:
        List of dictionaries containing movie information
    """
    movies = []
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml',
        'Accept-Language': 'en-US,en;q=0.9',
    }
    
    for page in range(num_pages):
        # Douban shows 25 movies per page
        url = f'https://movie.douban.com/top250?start={page*25}'
        
        try:
            # Add random delay to be respectful to the server
            time.sleep(random.uniform(1, 3))
            
            response = requests.get(url, headers=headers)
            response.raise_for_status()  # Raise exception for HTTP errors
            
            soup = BeautifulSoup(response.text, 'html.parser')
            movie_items = soup.select('.grid_view li')
            
            for item in movie_items:
                rank = item.select_one('.pic em').text
                title_element = item.select_one('.title')
                if title_element:
                    title = title_element.text.strip()
                    
                    # Extract original title if available
                    original_title_element = item.select_one('.other')
                    original_title = original_title_element.text.strip() if original_title_element else ""
                    
                    # Extract rating
                    rating_element = item.select_one('.rating_num')
                    rating = rating_element.text.strip() if rating_element else ""
                    
                    # Extract director and cast info
                    info_element = item.select_one('.bd p')
                    info = info_element.text.strip().replace('\n', ' ').replace(' ', ' ') if info_element else ""
                    
                    # Extract quote if available
                    quote_element = item.select_one('.quote .inq')
                    quote = quote_element.text.strip() if quote_element else ""
                    
                    movie_data = {
                        'rank': rank,
                        'title': title,
                        'original_title': original_title,
                        'rating': rating,
                        'info': info,
                        'quote': quote
                    }
                    
                    movies.append(movie_data)
                    
                    # Print progress
                    print(f"Scraped: {rank}. {title}")
            
            print(f"Completed page {page + 1}/{num_pages}")
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching page {page + 1}: {e}")
            # Wait a bit longer if there was an error
            time.sleep(5)
            continue
    
    return movies

def save_to_csv(movies, filename="douban_top250.csv"):
    """Save the movie data to a CSV file"""
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['rank', 'title', 'original_title', 'rating', 'info', 'quote']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for movie in movies:
                writer.writerow(movie)
        
        print(f"Successfully saved data to {filename}")
    except IOError as e:
        print(f"Error saving to CSV: {e}")

def main():
    print("Starting to scrape Douban Top 250 Movies...")
    movies = get_top_movies(10)  # 10 pages = 250 movies
    
    if movies:
        print(f"Successfully scraped {len(movies)} movies")
        save_to_csv(movies)
    else:
        print("No movies were scraped")

if __name__ == "__main__":
    main()