[{"ArticleId": 86955081, "Title": "Supervisory control design for congestion control and bandwidth management", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCC.2021.113256", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 19270, "JournalTitle": "International Journal of Systems, Control and Communications", "ISSN": "1755-9340", "EISSN": "1755-9359", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955093, "Title": "Design and control systemic methodology ver.03: systemic modelling with cybernetic aspects", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASS.2020.113253", "PubYear": 2020, "Volume": "9", "Issue": "2", "JournalId": 18638, "JournalTitle": "International Journal of Applied Systemic Studies", "ISSN": "1751-0589", "EISSN": "1751-0597", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Nikitas A<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955099, "Title": "Development of a Secured Vehicle Spot Detection System Using GSM", "Abstract": "<p>The number of vehicles stolen is increasing annually. The alarm system for vehicles is commonly used to expose the burglars publicly to make a scene. If the vehicle owner is far away, it will be hard to distinguish the threat to the vehicle from the intruder. To deter such crime, there should be a safer protection mechanism for vehicles around the globe. Moreover, overcrowd parking lot problems have always been an issue. Nevertheless, it became even worse with the progressing urbanization and population expansion. Anyone may have experienced that moment of panic when you realize you have no idea where you parked your vehicle. Note that, the keyless remote control of vehicles has a short-range radio transmitter. It usually can reach up 5–20 meters. Therefore, this paper develops a proposed system that is used for detecting vehicle location using Global System for Mobile Communications (GSM). It is basically capable to switch ON and OFF vehicle alarm system to detect the location of the vehicle in a crowded area. PIC microcontroller that has been embedded in the system programmed using C language. The intelligent proposed system also designs to enhance the security feature for the transportation era. The proposed system offers better service and cost-effective for the vehicle owners.</p>", "Keywords": "PIC16F886 Microcontroller;GSM;SMS;Buzzer", "DOI": "10.3991/ijim.v15i04.19267", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "Loay F. <PERSON>", "Affiliation": "Jouf University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jouf University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Jouf University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Jouf University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jouf University"}], "References": []}, {"ArticleId": 86955100, "Title": "Gender Differences in Digital Competence Among Secondary School Students", "Abstract": "<p>Digital technologies have become powerful tools in today society in many aspects. However, without proper knowledge and guidance, youth is   being challenged with potential hazards of cybercrimes. Hence, the purpose of this study was to compare who is at a greater risk of cybercrimes, and to assess whether there is a significant difference in the digital competency between girls and boys at rural areas. Digital competence was measured as technology, cognitive, ethical knowledge and communication, and cybersecurity was measured as intellectual property, privacy, accuracy and accessibility. Questionnaires were distributed to selected secondary schools. Based on the t-test analyses of 211 responses, the findings indicate there is a significant difference in the attitude toward ICT usage between male and female students, as well as significant differences in the behavior of troubleshooting, staying safe online and being credible and in the ethical knowledge. Comparing the cybersecurity traits, gender differentiates the privacy concerns, accuracy and accessibility behavior. As the results are alarming, specific concerns and focus must be placed on youth digital technology usage and education for ensuring they are not neglected, and exposed as cyber victims, but also to raise a responsible e-society with excellent digital citizenship attributes.</p>", "Keywords": "Digital Competency, ICT Usage in Rural Areas, Digital Citizen, Youth and Cybersecurity, Gender Differences in ICT", "DOI": "10.3991/ijim.v15i04.20197", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi MARA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business and Management, Universiti Teknologi MARA, Cawangan Selangor, Kampus Puncak Alam, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business and Management, Universiti Teknologi MARA, Cawangan Selangor, Kampus Puncak Alam, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business and Management, Universiti Teknologi MARA, Cawangan Selangor, Kampus Puncak Alam, Selangor, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Business and Management, Universiti Teknologi MARA, Cawangan Selangor, Kampus Puncak Alam, Selangor, Malaysia"}], "References": []}, {"ArticleId": 86955130, "Title": "Some investigations on cost, study for economic order quantity model by quantity declined under time - associated demand and non-steady holding cost", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCSYSE.2021.113252", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 28963, "JournalTitle": "International Journal of Computational Systems Engineering", "ISSN": "2046-3391", "EISSN": "2046-3405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955131, "Title": "Interval valued fuzzy matrix-based decision making for machine learning algorithms", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCSYSE.2021.113262", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 28963, "JournalTitle": "International Journal of Computational Systems Engineering", "ISSN": "2046-3391", "EISSN": "2046-3405", "Authors": [{"AuthorId": 1, "Name": "Priya Bhatnagar", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955175, "Title": "Effective decoding and ambient backscattering phase cancellation mitigation in tag-to-tag communication", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCC.2021.113251", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 19270, "JournalTitle": "International Journal of Systems, Control and Communications", "ISSN": "1755-9340", "EISSN": "1755-9359", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Dengao Li", "Affiliation": ""}], "References": []}, {"ArticleId": 86955222, "Title": "Computing stationary distributions of the D-MAP/D-MSP$$^{(a,b) }/1$$ queueing system", "Abstract": "<p>The present study investigates a single-server batch service queueing model with arrival process as discrete-time Markovian arrival process and service process as discrete-time Markovian service process. The server serves the customers in batches followed by the general bulk-service rule. We first determine the random epoch probabilities using the matrix-geometric method, where the rate matrix \\({\\mathbf{R}}\\) is determined by an efficient approach based on the eigenvalues and the corresponding eigenvectors of the associated characteristic equation. Next we obtain the explicit closed-form expressions for the pre-arrival, intermediate, outside observer’s and post-departure epoch probabilities by developing the relations among them in equilibrium state. Further, we provide an analytically simple approach to carry out the waiting-time distribution in the queue measured in slots as well as the distribution of the size of a service batch of an arriving customer. We also demonstrate a cost function to evaluate the optimum value of the minimum service batch size and the corresponding expected cost of the system. Finally, an adequate variety of numerical experiments are performed for validation purpose of our analytical results and they are conferred in the form of tables and graphs.</p>", "Keywords": "Discrete-time Markovian arrival process (D-MAP); Discrete-time Markovian service process (D-MSP); Matrix-geometric method; Queueing; Spectral expansion method", "DOI": "10.1007/s12652-021-02919-1", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, National Institute of Technology Raipur, Chhattisgarh, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, National Institute of Technology Raipur, Chhattisgarh, India"}], "References": [{"Title": "Analysis of a retrial queue with group service of impatient customers", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "2591", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 86955228, "Title": "A tri‐band and miniaturized planar antenna based on countersink and defected ground structure techniques", "Abstract": "<p>In this article, a compact planar heart‐shaped antenna based on countersink and partial ground plane techniques is proposed to operate as a multiband device for modern wireless systems. With a surface area of 28.5 mm<sup>2</sup>, the countersink technique (CT) has been developed to achieve high electromagnetic antenna performance. The CT consists of a particular multilayer configuration that uses two distinct materials where one of them with a radiating element on its top is placed inside another. In this article, a 508‐μm thick Rogers RO4350B with a radiating patch designed on its top is thermally embedded into a 1.524 mm thick FR4 HTG‐175. The slits are used to get multiband behavior, and their combination with the defected ground structure results in about 79.58% reduction in resonant frequency compared to the conventional antenna. A mathematical model is developed to predict the resonant frequency of the proposed antenna configuration. After modeling the heart‐shaped antenna with an electromagnetic simulator, its prototype was manufactured and validated through experimental measurements. The CT considerably improved the antenna's performance compared to the use of a single layer technology for the same antenna configuration. The return loss read from −10 dB shows that the proposed antenna covers multiple modern wireless applications, including LTE bands, 5G, WLAN, WiMAX, and Unlicensed National Information Infrastructure (UNII) radio bands. Furthermore, the antenna operates as narrowband and wideband simultaneously on its lower/middle and upper operating frequency bands. A close agreement is found between simulated and measured results across all the frequency bands.</p>", "Keywords": "countersink;heart‐shaped antenna;miniature antenna;monopole;multiband", "DOI": "10.1002/mmce.22617", "PubYear": 2021, "Volume": "31", "Issue": "5", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Pan African University Institute for Basic Sciences Technology and Innovation, Nairobi, Kenya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Sciences and Techniques, Electrical and Electronics Engineering Laboratory, Marien Ngouabi University, Brazzaville, B.P 69 Republic of the Congo"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, Technical University of Kenya, Nairobi, Kenya"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Université Grenoble Alpes, CNRS, G2ELab, F‐38400 Saint‐Martin d'Hères France"}], "References": [{"Title": "Compact triband slotted printed monopole antenna for WLAN and WiMAX applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}, {"Title": "Design and development of enhanced bandwidth multi‐frequency slotted antenna for 4G‐LTE/WiMAX/WLAN and S/C/X‐band applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "7", "Page": "", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}, {"Title": "A compact multi‐slots loaded gap coupled CP antenna with DGS for WLAN/WiMAX applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "12", "Page": "", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}]}, {"ArticleId": 86955229, "Title": "Interactive multilevel programming approaches in neutrosophic environments", "Abstract": "<p>Multilevel programming is a mathematical programming problem with hierarchical structure. A typical feature of multilevel programming is that the upper level exhibits a priority over the lower level. However, the solutions obtained by most existing programming methods either violate this rule or ignore the participants’ desire for a win–win outcome. The objective of this study is to propose new multilevel programming approaches for obtaining desirable solutions. First, three types of membership functions in neutrosophic set are defined to comprehensively describe fuzzy cognition of decision makers. Then, considering dissimilar intentions of experts, three different interactive approaches are proposed to solve multilevel programming problems. To demonstrate the feasibility of the proposed approaches, a case of pricing decision-making of data products is investigated and the impacts of four key parameters are discussed. Finally, several numerical examples are studied by using the proposed approaches and other existing methods. Two evaluation indexes, the equilibrium coefficient and distance measure, are utilized to appraise the performance of the developed programming methods. The results demonstrate that the proposed approaches can obtain sound solutions which obey the rule of multilevel programming, realize the mutual benefits of participants, and can provide guidelines for the pricing of satellite image data products.</p>", "Keywords": "Interactive approach; Multilevel programming; Neutrosophic set theory; Pricing decision-making", "DOI": "10.1007/s12652-021-02975-7", "PubYear": 2022, "Volume": "13", "Issue": "4", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha, China;Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Alberta, Edmonton, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Systems Engineering, National University of Defense Technology, Changsha, China"}], "References": [{"Title": "Neutrosophic goal programming strategy for multi-level multi-objective linear programming problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>rama<PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "8", "Page": "3175", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Consensus reaching for social network group decision making by considering leadership and bounded confidence", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106240", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Large-scale multiple criteria decision-making with missing values: project selection through TOPSIS-OPA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "9341", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Pricing of satellite image data products: Neutrosophic fuzzy pricing approaches under different game scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107106", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 86955230, "Title": "S-THAD: a framework for sensor-based temporal human activity detection from continuous data streams", "Abstract": "<p>With recent evolvement in smart sensing systems, sensor-based activity recognition has endured numerous research studies that mainly emphasize classifying pre-segmented data chunks having a fixed duration. Each data chunk generally involves a single human activity for classification into one of the predefined activity classes. Such activity recognition models, trained with pre-segmented and fixed-size data chunks, cannot adapt well to natural human activities in real-time settings, where the duration of activities varies. Also, the real-time data available from smart devices is in a continuous form and not discrete chunks. As a result, the real-time implementation of activity-aware applications based on the existing models becomes impractical. Therefore, in this paper, a novel framework, i.e., “S-THAD”, is proposed for sensor-based temporal human activity detection from the continuous and untrimmed 3D motion sensor data. The proposed method is capable of detecting when and which activity of interest appears in a continuous data stream. The publicly available PAMAP2 dataset is used to test the proposed scheme, which entails long and untrimmed data streams from the wearable inertial sensors placed at three different body positions, including hand, chest, and ankle. The experimental results indicate that the proposed scheme achieves significant detection performance on this dataset.</p>", "Keywords": "Activity recognition; Continuous data stream; Machine learning; Proposal classification; Temporal activity detection; Wearable sensor", "DOI": "10.1007/s12652-021-02931-5", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Telecom and Information Engineering, University of Engineering and Technology (UET), Taxila, Pakistan;Sino-Pak Center for Artificial Intelligence, Pak-Austria Fachhochschule: Institute of Applied Sciences and Technology, Haripur, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technology and Innovation Research Group, School of Information Technology, Whitecliffe , Wellington, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Sino-Pak Center for Artificial Intelligence, Pak-Austria Fachhochschule: Institute of Applied Sciences and Technology, Haripur, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Telecom and Information Engineering, University of Engineering and Technology (UET), Taxila, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Computer Science, Victoria University of Wellington, Wellington, New Zealand"}], "References": [{"Title": "Adaptive sliding window based activity recognition for assisted livings", "Authors": "<PERSON><PERSON><PERSON><PERSON> Ma; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "55", "JournalTitle": "Information Fusion"}, {"Title": "Online human activity recognition employing hierarchical hidden Markov models", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "1141", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A body sensor data fusion and deep recurrent neural network-based behavior recognition approach for robust healthcare", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "105", "JournalTitle": "Information Fusion"}, {"Title": "Opportunistic sensing for inferring in-the-wild human contexts based on activity pattern recognition using smart computing", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "374", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Human activity recognition in smart environments employing margin setting algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "7", "Page": "3669", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Sensor-based and vision-based human activity recognition: A comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "107561", "JournalTitle": "Pattern Recognition"}, {"Title": "Human activity recognition based on smartphone using fast feature dimensionality reduction technique", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2365", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Vectors of temporally correlated snippets for temporal action detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "85", "Issue": "", "Page": "106654", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 86955254, "Title": "STOCK PRICE PREDICTION BASED ON FINANCE RELATED NEWS\nUSING NLP, LASSO AND ARIMAX", "Abstract": "", "Keywords": "", "DOI": "10.26634/jse.14.4.17661", "PubYear": 2020, "Volume": "14", "Issue": "4", "JournalId": 39941, "JournalTitle": "i-manager’s Journal on Software Engineering", "ISSN": "0973-5151", "EISSN": "2230-7168", "Authors": [{"AuthorId": 1, "Name": "SUDHAKAR KALVA", "Affiliation": "Department of Computer Science and Engineering, Jawaharlal Nehru Technological University, Kakinada, Andhra Pradesh, India."}, {"AuthorId": 2, "Name": "S. NAGANJANEYULU", "Affiliation": "Department of Information Technology, Lakireddy Bali Reddy College of Engineering, Krishna, Andhra Pradesh, India."}], "References": []}, {"ArticleId": 86955298, "Title": "Subjective assessment on visual fatigue versus stereoscopic disparities", "Abstract": "<p>The sense of discomfort when watching stereoscopic display caused by visual fatigue has hindered the widespread applications of stereoscopic display. In this work, we explore the relationship between visual fatigue and stereoscopic parallax using subjective assessment. The visual fatigue of the subjects is evaluated after viewing different parallax stereoscopic samples. It shows that visual fatigue exacerbates greater in negative parallax than positive parallax. Surprisingly, the results indicate that visual fatigue can be alleviated with tiny parallax, which increases the comfort of stereoscopic visual perception. We envision that this relationship is able to provide helpful hints in designing visual healthy stereoscopic images.</p>", "Keywords": "digital calibration task;parallax;stereoscopic display;visual fatigue", "DOI": "10.1002/jsid.991", "PubYear": 2021, "Volume": "29", "Issue": "6", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Biological Science and Medical Engineering, Beihang University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Optoelectronic Materials and Technologies, School of Physics, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "Yu<PERSON> Fan", "Affiliation": "School of Biological Science and Medical Engineering, Beihang University, Beijing, China"}, {"AuthorId": 4, "Name": "Xiangfeng Meng", "Affiliation": "Institute for Medical Device Control, National Institutes for Food and Drug Control, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Optoelectronic Materials and Technologies, School of Physics, Sun Yat-sen University, Guangzhou, China"}], "References": []}, {"ArticleId": 86955393, "Title": "Replication of an agent-based model using the Replication Standard", "Abstract": "Despite the critical role that replication plays in the advancement of science, its presence in modelling literature is rare. To encourage others to conduct replication and report success and challenges that facilitate or hinder replication, we present the replication of an agent-based model (ABM) of residential sprawl using the Replication Standard. Replication results achieved relational equivalence. Through the replication process, issues with the original research were identified and corrected in an Improved model, which qualitatively supported original results. A specific challenge affecting alignment of original and Replicate models included capturing model output variability and publishing all original output data for statistical analysis. Through the replication of agent-based models, additional confidence in model behaviour can be garnered and replicated ABMs can become accredited for reuse by others. Future research in the development and refinement of replication methodology and assessments should be cultivated along with a culture of value for replication efforts.", "Keywords": "Model replication ; Agent-based modelling ; Replication Standard ; Verification", "DOI": "10.1016/j.envsoft.2021.105016", "PubYear": 2021, "Volume": "139", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography and Environmental Management, University of Waterloo, Waterloo, Ontario, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Geography and Environmental Management, University of Waterloo, Waterloo, Ontario, Canada;Corresponding author. University of Waterloo, Department of Geography and Environmental Management, 200 University Avenue West, Waterloo, Ontario, N2L 3G1, Canada"}], "References": []}, {"ArticleId": 86955405, "Title": "Post Privacy Shield – Ende der Drittstaatentransfers wie wir sie kannten?", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2021.02.03", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955406, "Title": "Ausgewählte Rechtsprechung und Verfahren", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2021.02.05", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955407, "Title": "Data Protection by Process", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2021.02.09", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955434, "Title": "Ein Hoch auf die Zwischenlösung", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2021.02.04", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955435, "Title": "Die Zukunft der Online-Rechtsinformation", "Abstract": "", "Keywords": "", "DOI": "10.37307/j.2196-9817.2021.02.07", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 78011, "JournalTitle": "PinG Privacy in Germany", "ISSN": "2197-1862", "EISSN": "2196-9817", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955587, "Title": "Multiarea Power System Performance Measurement using Optimized PID Controller", "Abstract": "The major goal of this work is to diminish the area control error, settling time, undershoots and overshoots, and frequency variations in multi area power systems. The optimal design of PID controller gain is to be automatically adjusted for different loading conditions. Unified Power Flow Controller (UPFC) and Redox Flow Battery (RFB) are incorporated into the proposed system to obtain better dynamic performance for different operating scenario. The settling time of GWO based PID controller is improved to 10% and 30% in area-1 is compared to PSO and GA based PID. Similarly, the settling time improved to 17% and 19% in area-2 and 29% and 60% in area-3 compared to PSO and GA. Also, the peak undershoots of frequency in area-1 for GWO, PSO and GA based PID controller are -0.058, -0.068 and -0.072.", "Keywords": "", "DOI": "10.1016/j.micpro.2021.104238", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "EEE Department, R.V.S Engineering College, Dindigul, TN, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "EEE Department, Mepco Schlenk Engineering College, Sivakasi, TN, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "EEE Department, Rajalakshmi Engineering College, Chennai, TN, India"}], "References": []}, {"ArticleId": 86955677, "Title": "Distributed predictor-based stabilization of interconnected systems with network induced delays", "Abstract": "This paper presents sufficient stability conditions for distributed prediction-based control of interconnected systems subject to network-induced time-varying delay. Due to the flexibility of the proposed criteria, continuous-time, sampled-data control and output-feedback problems can be handled by the same framework. A detuning procedure is combined with a distributed LQR design in order to ensure the closed-loop stability for a given network-induced delay bound. If compared with related works, the proposed framework can be applied to obtain the time-varying delay bound derived from distinct combinations of distributed dead-time compensation strategies with stabilizing control laws. Two simulation case studies based on a benchmark problem and the level control of a irrigation canal are presented to illustrate the usefulness of the proposed framework. Introduction Single-input delays, also known as dead times, are commonly found in multiple industrial processes due to the time interval required to: (i) transfer mass and energy; (ii) induced by computation and communication delays; and (iii) due to the approximated descriptions based on reduced models [21]. It is well known that long delays increase control design complexity due to the undesired time interval between the control action and its output effect [21]. Since <PERSON>’s seminal work [26], several Dead-Time Compensators (DTCs), also known as predictor-based controllers, have been proposed to enhance closed-loop performance in the presence of delays. Time-varying delays may be observed as a consequence of the process intrinsic behavior or they may be induced by communication networks [24]. Network induced delays cannot be directly handled as a constant uncertain delay due to their time-varying nature [10], [14], [9]. Several works have been proposed to perform stability analysis with time-varying delays either in continuous-time framework [16], [4], [15], [18], [17] or based on discrete-time representation [32], [29]. In Networked Control System (NCS) applications, network-induced delay effect can be combined with the intrinsic process dead-time, which may be undesired from closed-loop perspective [22]. In this type of situation, prediction based control strategies have been used to compensate the minimum constant admissible delay [11], [22], [8]. Recently, prediction-based control strategies have been proposed to stabilize distributed interconnected systems [19], [31], [12]. In this type of system, multiple delayed subsystems are independently stabilized despite the coupled interacting effect. Each subsystem is stabilized with a local control law, but the measurements of the other subsystems are not available from local perspective. As pointed out in [19], the distributed predictor-based strategy cannot be used to stabilize coupled subsystems with arbitrary long delays due to the neglected interacting effect in contrast to the centralized predictor based approach. A continuous-time predictor control strategy and a stability criterion have been proposed in [19] to ensure exponential stability of the prediction-based distributed control law with constant delays. The analysis of the sampled-data case with constant network delays subject to continuous measurement and continuous-time actuation is presented in [31]. An interesting comparison between analysis complexity of decentralized and centralized analysis criteria is also presented in [31]. A robust event-driven weighted prediction feedback has been proposed in [11] by applying discrete-time stability analysis, but the network-induced delays are also assumed to be constant. Despite the relevance of these recent results, time-varying delay and output feedback have not been considered in these related works. This paper presents continuous-time and discrete-time robust stability criteria for prediction-based control of interconnected with time-varying network induced delay based on the small-gain theorem [14], [8]. Modified Artstein predictors and Smith predictor-based compensators for distributed stabilization subject to time-varying delay can be directly analyzed from the stability criteria presented in this work. The stability condition is generalized to the distributed prediction-based output-feedback problem due to the flexibility of the proposed analysis formulation. In contrast to recent results, the proposed stability criteria for decentralized control law with distributed delay compensation are general as the predictors and the controllers are not prescribed by given strategies. Hence, closed-loop concerns such as disturbance rejection, robust stability subject to time-varying delay, and output-feedback problem can be handled within the same framework. It should be remarked that disturbance rejection, network induced delays and output-feedback are practical challenges that have not been considered in related works based on distributed predictors. An academic case study based on a benchmark model [19], [31] and a canal control level case study [20] are investigated to illustrate the benefits of the proposed criteria. The presentation is organized as follows: Section 2 revisits the preliminaries and the problem statement, Section 3 presents the stability criteria based on the small-gain theorem, Section 4 shows the output feedback extension, the simulation case studies are analyzed in Section 5, and the concluding remarks are presented in Section 6. Figures Network configuration for distributed interconnected systems. Dashed lines – digital information, solid lines – continuous-time signal. Gray lines represent signals subject to network induced delays. Continuous-time closed-loop control based on distributed prediction and control with time-varying delay: original representation and equivalent loop for small gain criterion analysis. Sampled-data closed-loop control based on distributed prediction and control with network induced delay: original representation and equivalent loop for small gain criterion analysis. State responses without delay uncertainty: original continuous-time controller and alternative sampled-data strategies. State responses with delay uncertainty: original continuous-time controller and alternative sampled-data strategies. Worst-case admissible delay analysis with respect to LQR design parameter. Show all figures Section snippets Preliminaries and problem statement Consider the following interconnected continuous-time linear system with constant input delay: x ̇ j ( t ) = A j x j ( t ) + B j u j ( t - h j ) + ∑ ℓ ≠ j F j , ℓ x ℓ ( t ) , x j ( 0 ) = x j , 0 , u j ( τ ) = 0 , τ ∈ [ - h j , 0 ) , where j = 1 , 2 , … M represents the subsystem index, h j ⩾ 0 is the input delay of the subsystem j , x j ( t ) ∈ R n j is the state vector of the subsystem j , and u j ( t ) ∈ R p j is the input vector of the subsystem j . The coupling effects are described by the matrices F j , ℓ ∈ R n j × n ℓ where j = 1 , 2 , … M and ℓ ≠ j . From an analysis perspective, the original problem can Main results This section proposes two small gain theorem criteria [14] to analyze the closed-loop stability of prediction-based control of interconnected systems with time-varying delays. Both continuous-time and sample-data control problems are considered for the sake of generality as distributed prediction-based control for systems with constant delays have received increasing attention [19], [31]. Distributed output-feedback predictor-based stabilization The output-feedback control of delayed systems is an important problem in practice [30]. Indeed, the flexibility of the distributed predictor-based formulation can be explored to incorporate the distributed output-feedback strategy as a stabilizing control approach. Now, assume that the measured outputs are given: y ( t ) = Cx ( t ) , y ( kT s ) = Cx ( kT s ) . The continuous-time and discrete-time distributed state estimators are respectively defined by: x ¯ ̇ j ( t ) = A j x ¯ j ( t ) + B j u ( t - h j ) + L j ( y ( t ) - y ¯ j ( t ) ) , y ¯ j ( t ) = C x ¯ j ( t ) , where Case I – open-loop unstable problem The initial case study is an academic benchmark problem [13], [3], [19], [31], [12], which has been recently used to illustrate distributed delay compensation benefits in contrast to control law without predictors. The main objective is to highlight the usefulness of the LQR design based on the proposed criterion and the effectiveness of output-feedback control with a distributed predictor. The connected model is defined by the following matrices: A 1 = A 2 = 0 1 0 0 2.9156 0 - 0.0005 0 0 0 0 1 - 1.6663 0 0.0002 0 , B 1 = B 2 Conclusions This paper presents two stability criteria for distributed predictor-based control of systems with network-induced time-varying delay based on the small-gain theorem. The proposed criteria can be applied either to continuous-time or discrete-time modelling. The flexibility of the proposed distributed-predictor framework can be used to deal with practical challenges such as network-induced delay, disturbance rejection, and output-feedback problem. The generality of the predictor representation CRediT authorship contribution statement Tito L. M. Santos: Investigation, Funding acquisition. Taniel S. Franklin: Conceptualization, Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments First author gratefully acknowledges Brazilian agency (CNPq) [Grant Number 309675/2018-9 and 425606/2018-0]. References (32) J. Alcaina et al. Delay-independent dual-rate PID controller for a packet-based networked control system Information Sciences (2019) T.S. Franklin et al. Robust filtered Smith predictor for processes with time-varying delay: A simplified stability approach European Journal of Control (2020) X. Ge et al. Distributed networked control systems: A brief overview Information Sciences (2017) R. Gielen et al. On polytopic inclusions as a modeling framework for systems with time-varying delays Automatica (2010) T.S. Franklin et al. Receptance-based robust stability criteria for second-order linear systems with time-varying delay and unstructured uncertainties Mechanical Systems and Signal Processing (2021) A. González A weighted distributed predictor-feedback control synthesis for interconnected time delay systems Information Sciences (2021) T.L. Santos et al. Receptance-based stability criterion for second-order linear systems with time-varying delay Mechanical Systems and Signal Processing (2018) C.-Y. Kao et al. Simple stability criteria for systems with time-varying delays Automatica (2004) T.H. Lee et al. Improved stability conditions of time-varying delay systems based on new Lyapunov functionals Journal of the Franklin Institute (2018) T.H. Lee et al. Relaxed conditions for stability of time-varying delay systems Automatica (2017) T.L. Santos et al. On the filtered Smith predictor for MIMO processes with multiple time delays Journal of Process Control (2014) T. Santos et al. J.E.N.-R., Dealing with noise in unstable dead-time process control Journal of Process Control (2010) M. Wu et al. Further results on delay-dependent stability criteria of discrete systems with an interval time-varying delay Journal of the Franklin Institute (2017) Z. Li et al. Improved inequality-based functions approach for stability analysis of time delay system Automatica (2019) J. Chen et al. Stability analysis of continuous-time systems with time-varying delay using new Lyapunov–Krasovskii functionals Journal of the Franklin Institute (2018) K.-Z. Liu et al. Distributed predictor-based stabilization of continuous interconnected systems with input delays Automatica (2018) View more references Cited by (0) Recommended articles (6) Research article Image robust adaptive steganography adapted to lossy channels in open social networks Information Sciences, Volume 564, 2021, pp. 306-326 Show abstract Currently, the demand for covert communication in open social networks brings new opportunities and challenges to existing image steganography technology in terms of robustness and security. To this end, an image robust adaptive steganography is proposed with robustness against multiple image processing attacks and detection resistance. First, a robust embedding domain with theoretical foundation and optimal invisibility is constructed based on the compression resistance principle. Then, utilizing the robust image abstraction and saliency measurement, the embedding channel is selected to avoid modifications in smooth regions and enhance visual quality. On this basis, the proposed method is given combining with error-correcting and STC codes to realize message embedding with minimum costs and improve extraction accuracy. Lastly, after parameters discussion and selection, the performance experiments are conducted compared with previous representative steganography algorithms, concerning robustness and detection resistance, and the fault tolerance is deduced, thereby providing the recommended coding parameters to improve message extraction integrity. The experimental results show that the proposed method can realize message extraction with high accuracy after JPEG compression, Gaussian noising, and scaling attacks, while holding comparable detection resistance to adaptive steganography against statistical features, which indicates its application prospect for covert communication in open lossy channels. Research article The detection of low-rate DoS attacks using the SADBSCAN algorithm Information Sciences, Volume 565, 2021, pp. 229-247 Show abstract Low-rate denial-of-service (DoS) attacks, which can exploit vulnerabilities in Internet protocols to deteriorate the quality of service, are variants of DoS attacks. It is challenging to identify low-rate DoS attacks using traditional DoS defence mechanisms due to their low attack rate and stealthy nature. Most of the existing attack detection techniques are based on statistical analysis and signal processing. They usually show a high false negative rate and are only applicable to small-scale data. We propose a new low-rate DoS attack detection scheme based on the self-adaptive density-based spatial clustering of applications with noise (SADBSCAN) algorithm. The SADBSCAN algorithm provides a solution to adaptively identify clusters in multidensity datasets. We use the SADBSCAN algorithm to group network traffic according to the characteristics of the network traffic subject to low-rate DoS attacks. Then, we use cosine similarity to determine whether the groups contain low-rate DoS attacks. To evaluate performance, we conducted experiments and compared the results with those of other detection solutions. The experimental data include data generated by the NS-2 and TestBed simulations and the WIDE public dataset. The results show that our scheme improves the detection accuracy, reduces the false negative rate, and can be adapted to large-scale complex network environments. Research article On the minimality of some generating sets of the aggregation clone on a finite chain Information Sciences, Volume 564, 2021, pp. 193-201 Show abstract Clone theory plays an important role in studying aggregation functions on bounded posets or bounded lattices. Several important classes of aggregation functions on a bounded lattice L form a clone, particularly the set of all aggregation functions on L , the so-called full aggregation clone on L . For any finite lattice L , this clone is known to be finitely generated and various generating sets and their constructions have been presented in recent papers. The aim of this paper is to extend previous results concerning generating sets of aggregation clones on finite chains. Namely, the objective is to discuss the minimality of certain generating bases, the so-called ( χ , ⊕ ) -generating sets. Research article A turning point-based offline map matching algorithm for urban road networks Information Sciences, Volume 565, 2021, pp. 32-45 Show abstract Offline map matching is a crucial step to facilitate many trajectory-based services in urban areas by finding vehicles’ travel paths from recorded and stored trajectory data. This paper proposes a novel turning point-based offline map matching algorithm, which introduces the concept of vehicle turning points to implement map matching piecewisely. The algorithm first separates the entire trajectory into multiple sub-trajectories using the identified turning points. It then selects the best-matched path for each sub-trajectory from the corresponding K -shortest paths. Extensive experiments are conducted to compare the performance of our algorithm with five state-of-the-art map matching algorithms in terms of four different criteria, including one correctly matched criterion, two incorrectly matched criteria, and one computation time-related criterion. Experimental results show that our algorithm has the best average matching accuracy and efficiency at different sampling intervals. Specifically, compared with the five benchmark algorithms, our algorithm can improve the correctly matched percentages by 1.43% to 34.66%, reduce the incorrectly matched percentages by 15.23% to 56.79%, and improve the matching speeds by 3.16–61.01 times. Research article Prescribed performance synchronization of complex dynamical networks with event-based communication protocols Information Sciences, Volume 564, 2021, pp. 254-272 Show abstract The paper addresses the prescribed performance synchronization (PPS) issue for complex dynamical networks (CDNs) using an event-triggered communication mechanism and PPS scheme with event-based protocols is designed for the CDNs. The designed synchronization control strategy can reduce the overshoot in transient processes, and stabilize synchronous errors at the origin. Furthermore, by introducing event-based protocols, continuous communication of the networks is avoided, so that the frequency of information communication is reduced and network resources are saved. Moreover, Zeno behavior in the networks can be excluded. Combining the Lyapunov stability method, a sufficient condition for asymptotic synchronization is proposed. Finally, the validity of the results is demonstrated by the CDNs with one link pendulum and Chua’s chaotic circuit (CCC) systems. Research article Viewpoint adaptation learning with cross-view distance metric for robust vehicle re-identification Information Sciences, Volume 564, 2021, pp. 71-84 Show abstract Many vehicle re-identification (Re-ID) problems require the robust recognition of vehicle instances across multiple viewpoints. Existing approaches for dealing with the vehicle re-ID problem are insufficiently robust because they cannot distinguish among vehicles of the same type nor recognize high-level representations in deep networks for identical vehicles with various views. To address these issues, this paper proposes a viewpoint adaptation network (VANet) with a cross-view distance metric for robust vehicle Re-ID. This method consists of two modules. The first module is the VANet with cross-view label smoothing regularization (CVLSR), which abstracts different levels of a vehicle’s visual patterns and subsequently integrates multi-level features. In particular, CVLSR based on color domains assigns a virtual label to the generated data to smooth image-image translation noise. Accordingly, this module supplies the viewing angle information of the training data and provides strong robust capability for vehicles across different viewpoints. The second module is the cross-view distance metric, which designs a cascaded cross-view matching approach to combine the original features with the generated ones, and thus, obtain additional supplementary viewpoint information for the multi-view matching of vehicles. Results of extensive experiments on two large scale vehicle Re-ID datasets, namely, VeRi-776 and VehiclelD demonstrate that the performance of the proposed method is robust and superior to other state-of-the-art Re-ID methods across multiple viewpoints. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.02.041", "PubYear": 2021, "Volume": "564", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON> L.<PERSON>", "Affiliation": "Departamento de Engenharia Elétrica e de Computação, Universidade Federal da Bahia, Escola Politécnica, Rua Aristides Novis, 02, Federação, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Engenharia Elétrica e de Computação, Universidade Federal da Bahia, Escola Politécnica, Rua Aristides Novis, 02, Federação, Brazil;Departamento de Automação, Centro Universitário SENAI CIMATEC, Av. <PERSON>, 1845- Piatã, 41650-010, Brazil"}], "References": [{"Title": "A weighted distributed predictor-feedback control synthesis for interconnected time delay systems", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "367", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 86955678, "Title": "A turning point-based offline map matching algorithm for urban road networks", "Abstract": "Offline map matching is a crucial step to facilitate many trajectory-based services in urban areas by finding vehicles’ travel paths from recorded and stored trajectory data . This paper proposes a novel turning point-based offline map matching algorithm , which introduces the concept of vehicle turning points to implement map matching piecewisely. The algorithm first separates the entire trajectory into multiple sub-trajectories using the identified turning points. It then selects the best-matched path for each sub-trajectory from the corresponding K -shortest paths. Extensive experiments are conducted to compare the performance of our algorithm with five state-of-the-art map matching algorithms in terms of four different criteria, including one correctly matched criterion, two incorrectly matched criteria, and one computation time-related criterion. Experimental results show that our algorithm has the best average matching accuracy and efficiency at different sampling intervals. Specifically, compared with the five benchmark algorithms, our algorithm can improve the correctly matched percentages by 1.43% to 34.66%, reduce the incorrectly matched percentages by 15.23% to 56.79%, and improve the matching speeds by 3.16–61.01 times. Introduction The past years have seen the popularity of Global Positioning System (GPS)-enabled devices being embedded in vehicles. This promotes the collection of a large amount of trajectory data that reflect vehicles’ positioning, speed, and direction information. In the Intelligent Transportation System (ITS), these GPS trajectory data are commonly integrated with the Geographic Information System (GIS) to provide individuals or companies with various services, such as vehicle navigation, tracking and recovering vehicles’ routes, and mapping new roads for the GIS [1]. In such integration with the GIS, a crucial step is to integrate trajectory data with spatial network data to identify the correct road segment on which a vehicle is travelling and to determine the vehicle’s location on the road segment, which is referred to as map matching [2]. Effective map matching is easy-to-achieve when the GPS trajectory data and the road network data are sufficiently accurate. However, real-world applications need effective map matching algorithms to handle GPS positioning errors and outdated road maps especially in dense urban road networks, where the road structure is very complicated and the road segments are usually dense. Map matching can be performed in either online or offline mode in practice [3]. Online map matching algorithms are used to process real-time and streaming trajectory data and determine the road segment and/or the specific position (including lanes) where the vehicle is travelling [4], [5], [6]. They are used to facilitate some real-time services, such as vehicle navigation [7], driving trip path prediction [8], and taxi customer-searching [9]. In some other cases, we need to satisfy some trajectory-based services, such as travel route recommendation [10], anomalous taxi trajectory detection [11], and traversal time estimation [12]. These are the focuses of offline map matching algorithms that process stored and complete trajectory data and determine an entire best-matched path in the road network [13], [14], [15]. However, to ease the financial burden of transforming and storing raw data, the ITS typically reduces the sampling frequency of stored trajectory data [16]. For such low-sampling-rate trajectories, online map matching algorithms cannot guarantee complete and topologically feasible matched paths due to the so-called arc-skipping problem [17]. That is, the vehicle generally passes multiple road segments between two consecutive low-sampling-rate GPS points. Moreover, it is challenging to handle this in dense and complicated urban road networks, which is the focus of this paper. A large number of offline map matching algorithms have been proposed for the case of low-sampling-rate trajectory data [13], [18], [19], [14]. To determine the specific travel path between two consecutive points, these matching algorithms typically first set multiple candidate matched positions for each trajectory point, and then calculate the shortest path between each pair of candidate positions of every two consecutive points. Although these algorithms can achieve high matching accuracies, they suffer from a long computation time caused by numerous shortest path calculations. Taking the algorithm proposed by Rahmani and Koutsopoulos [18] as an example, 71.18% of the matching time contributes to the shortest path calculations. A long computation time not only leads to a significant computation burden but also limits the application scenarios of these algorithms. For example, in the application of estimating the travel time of a path, existing route-based estimation methods need to call the map matching algorithm repetitively because they generally match and concatenate multiple historical and real-time trajectories that occurred in nearby areas during the current time period [20], [21], [12]. The resulting estimation process is thus inapplicable due to time-consuming repetitive map matching calculations. Therefore, it is crucial to improve the efficiency of the offline map matching. To address this challenge, this paper develops an efficient and effective offline map matching algorithm for low-sampling-rate trajectories. Our map matching algorithm is designed based on the observation that a true travel path tends to be direct rather than roundabout [13]. In this case, before the vehicle turns, multiple consecutive trajectory points should be matched to the same road segment or the same approximately straight shortest path. Therefore, it is natural to match all trajectory points between two consecutive vehicle turns to a straight road segment or path at one time. This piecewise matching mechanism is promising to achieve a high computation efficiency because it eliminates the redundant shortest path calculations between every two consecutive trajectory points in the existing algorithms [13], [18], [6], [22]. Based on this mechanism, we develop a turning point-based map matching algorithm to perform piecewise map matching. The algorithm first identifies all vehicle turning points and separates the original trajectory into multiple sub-trajectories using the turning points. It then selects the best-matched path for each sub-trajectory from the corresponding K -shortest paths. To the best of our knowledge, this paper is the first to use turning points for piecewise matching in map matching. We will see in Sub-Section 5.3 that the proposed algorithm has clear advantages in both matching accuracy and efficiency compared with five state-of-the-art map matching algorithms [18], [3], [6], [23], [22]. This paper contributes to the literature by (1) introducing the concept of turning point into offline map matching to implement piecewise matching of travel trajectories, which leads to a substantial improvement of matching efficiency; (2) improving the performance of map matching by integrating piecewise matching with a K -shortest path solving method; and (3) finding that both correctly and incorrectly matched criteria are helpful to provide a comprehensive evaluation of the map matching performance. The rest of this paper is organized as follows. Section 2 reviews related work in the literature. Section 3 introduces some notations and defines the offline map matching problem. Section 4 describes the proposed turning point-based map matching algorithm. Section 5 presents the results of numerical experiments. In Section 6, conclusions are made, and future research directions are discussed. Figures Architecture of the proposed turning point-based map matching algorithm. Schematic diagram of the proposed turning point-based map matching algorithm. Schematic diagrams of (a) the first and (b) the second ordered neighbouring intersections of v1. An example of the two types of indicators. Distributions of (a) the number of GPS points and (b) the travel distance of trajectories in the 3-s dataset. Experimental results at different θmin values. Show all figures Section snippets Related work The research on map matching can be traced back to 1980’s [24]. According to the trajectory portion considered in each matching operation, existing map matching algorithms can be divided into three categories [14], including local algorithms [25], [4], [5], [26], incremental algorithms [17], [16], [6], [27], and global algorithms [28], [29], [13], [18], [30], [22]. The local map matching algorithm matches a single trajectory point at one time and does not care about its predecessors and Notations and definitions This section defines the preliminary notations and terms used for the offline map matching. Definition 1 (Trajectory Point): A trajectory point is a sampling point collected by a GPS device embedded in a vehicle to record the position of the vehicle. Let p i denote the i th trajectory point collected. p i is represented by a triple 〈 p i . lng , p i . lat , p i . t 〉 , which denote the longitude, latitude and timestamp of p i , respectively. Definition 2 (Trajectory): A trajectory T is a sequence of trajectory points p 1 → p 2 → … → p n ordered Algorithm overview Our turning point-based map matching algorithm is designed to match all trajectory points between two consecutive vehicle turns at one time. For each sub-trajectory between two vehicle turning points, the algorithm selects the best-matched path from the corresponding K -shortest paths. Fig. 1 presents the architecture of our algorithm, which consists of four steps: turning point-based separation, sub-network construction, K -candidate sub-path calculation, and the best sub-path selection. Step 1: Numerical experiment We conduct extensive numerical experiments to evaluate the performance of our map matching algorithm, including the sensitivity analysis of algorithmic parameters and the performance comparison with five state-of-the-art map matching algorithms. Conclusion This paper proposed a novel turning point-based map matching algorithm to implement effective offline map matching based on the latitude, longitude, and timestamp information of GPS points. The proposed algorithm first separated the original trajectory into multiple sub-trajectories based on the identified turning points. Then, for each sub-trajectory, the algorithm constructed a sub-network to include all potential matched road intersections and road segments and calculated the K -shortest CRediT authorship contribution statement Dongqing Zhang: Methodology, Investigation, Software, Validation. Yucheng Dong: Supervision, Methodology, Writing - review & editing. Zhaoxia Guo: Supervision, Methodology, Investigation, Funding acquisition, Resources, Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgment Guo would like to thank the financial supports from the National Natural Science Foundation of China (Grant No.: 71872118), the MOE (Ministry of Education in China) Project of Humanities and Social Sciences (Grant No.: 18YJC630045), the Science and Technology Planning Project of Sichuan Province (Grant No.: 2020YJ0043), and Sichuan University (Grant No.s: 2018hhs-37, SKSYL201819). Zhang thanks the financial supports from the China Scholarship Council. References (37) M. Hashemi et al. A critical review of real-time map-matching algorithms: current issues and future directions Comput. Environ. Urban Syst. (2014) H. Alt et al. Matching planar maps J. Algorithms (2003) E. Jenelius et al. Travel time estimation for urban road networks using low frequency probe vehicle data Transp. Res. Part B: Methodol. (2013) M. Quddus et al. Shortest path and vehicle trajectory aided map-matching for low frequency GPS data Transp. Res. Part C (2015) M.A. Quddus et al. Current map-matching algorithms for transport applications: State-of-the art and future research directions Transp. Res. Part C (2007) J. Tang et al. Inferring driving trajectories based on probabilistic model from large scale taxi GPS data Physica A (2018) M. Rahmani et al. Path inference from sparse floating car data for urban networks Transp. Res. Part C (2013) T. Miwa et al. Development of map matching algorithm for low frequency probe data Transp. Res. Part C (2012) M. Nikoli, J. Jovi, Implementation of generic algorithm in map-matching model, Expert Syst. Appl. 72 (2017) 283–292,... A. Karduni, A. Kermanshah, S. Derrible, A protocol to convert spatial polyline data to network formats and applications... J.L. Bentley et al. Efficient worst-case data structures for range searching Acta Informatica (1980) M. Kubicka et al. Comparative study and application-oriented classification of vehicular map-matching methods IEEE Intell. Transp. Syst. Mag. (2018) D. Bernstein, A. Kornhauser, An introduction to map matching for personal navigation assistants (1996). URL:... S. Liu et al. Fraud detection from taxis’ driving behaviors IEEE Trans. Veh. Technol. (2014) H. Wu, W. Sun, B. Zheng, Is only one GPS position sufficient to locate you to the road network accurately?, in:... Y. Lou, C. Zhang, Y. Zheng, X. Xie, W. Wang, Y. Huang, Map-matching for low-sampling-rate GPS trajectories, in:... J. Tang, S. Zhang, Y. Zou, F. Liu, An adaptive map-matching algorithm based on hierarchical fuzzy system from vehicular... C. Blazquez et al. An instance-specific parameter tuning approach using fuzzy logic for a post-processing topological map-matching algorithm IEEE Intell. Transp. Syst. Mag. (2018) View more references Cited by (0) Recommended articles (6) Research article Adaptive denoising algorithm using peak statistics-based thresholding and novel adaptive complementary ensemble empirical mode decomposition Information Sciences, Volume 563, 2021, pp. 269-289 Show abstract This paper proposes an adaptive denoising methodology for noisy signals that employs a novel adaptive complementary ensemble empirical mode decomposition (NACEEMD) and a peak statistics (PS)-based thresholding technique. The key idea in this paper is the peak statistics (PS)-based thresholding technique,which breaks the traditional strategy with respect to selecting more accurate and more adaptive thresholds. The NACEEMD algorithm is proposed to decompose the noisy signal into a series of intrinsic mode functions (IMFs). At the same time, NACEEMD is also used to verify the applicability of the PS-based thresholding technique in different decomposition algorithms. The PS-based threshold is used to remove the noise inherent in noise-dominant IMFs, and the denoised signal is reconstructed by combining the denoised noise-dominant IMFs and the signal-dominant IMFs. This paper uses a various of simulated signals in various noisy environments for experiments, the experimental results indicate that the proposed algorithm outperforms traditional threshold denoising methodologies in terms of signal-to-noise ratio, root mean square error, and percent root distortion. Moreover, through real ECG signal and multi-sensor data fusion experiments, the application of the proposed algorithm in the field of engineering is explored and expanded. Research article Two-stage stochastic minimum cost consensus models with asymmetric adjustment costs Information Fusion, Volume 71, 2021, pp. 77-96 Show abstract When dealing with consensus cost problems with asymmetric adjustment costs, the uncertain scenarios with certain probabilities which are becoming a serious problem decision-makers have to face. However, existing optimization-based consensus models have failed to consider uncertain factors that could influence the final consensus and total consensus cost. In order to better deal with these issues, it is necessary to develop practical consensus optimal models. Thus, we establish three two-stage stochastic minimum cost consensus models with asymmetric adjustment costs that may eventually lead the way to better consensus outcomes. The impact of uncertain parameters (such as individual opinions, unit asymmetric adjustment costs, compromise limits, cost-free thresholds) are investigated by modeling three kinds of uncertain consensus models. We solve the proposed two-stage stochastic consensus problem iteratively using the L-shaped algorithm and show the convergence of the algorithm. Furthermore, a case of pollution control negotiations verifies the practicability of the proposed models. Moreover, the comparison of results with the L-shaped algorithm and CPLEX shows that the L-shaped algorithm is more effective in solving time. Some discussions and comparisons on local and global sensitivity analysis of the uncertain parameters are presented to reveal the features of the proposed models. Finally, the relationships between the minimum cost consensus model and minimum cost consensus models with asymmetric adjustment costs and the proposed models are also provided. Research article A novel approach of two-stage three-way co-opetition decision for crowdsourcing task allocation scheme Information Sciences, Volume 559, 2021, pp. 191-211 Show abstract In the crowdsourcing task allocation scheme, there is an emerging and realistic co-opetition phenomenon. To availably solve the crowdsourcing task allocation problem with co-opetition, this paper designs a two-stage co-opetition model by constructing novel three-way decision (TWD), including a competition-optimization model and a negotiation-cooperation model. Unlike the other studies, the two-stage co-opetition model with TWD can not only protect the profits of the task candidates, but also optimize the overall benefits. Specifically speaking, in the competition-optimization model, we construct an optimization model based on the data envelopment analysis (DEA) method in advance, which maximizes the personal benefit. By integrating information system and the loss function matrix, we consider the linkage of evaluation information and risk information and then improve the original TWD to make an initial allocation. In the negotiation-cooperation model, considering that the relationship among the candidates may influence the task performance, the fuzzy measure is introduced to describe a broader partnership. Meanwhile, we also design two different schemes to coordinate and optimize the best task allocations based on the initial allocation. In order to choose the best scheme, the selection strategy between schemes is further investigated under the guidance of the utility and the loss. Finally, we give an example of a medical supply chain crowdsourcing problem to illustrate and verify our proposed approach. Research article Fairness concern: An equilibrium mechanism for consensus-reaching game in group decision-making Information Fusion, Volume 72, 2021, pp. 147-160 Show abstract For many group decision-making (GDM) issues, such as water-resource allocation, urban resettlement, and traffic-route planning, the benefits of the decision makers (DMs) are closely related to the collective decision-making result. In fact, the consensus-reaching process is a game between the decision makers and the moderator. The fairness concern in GDM impacts DMs’ preference modification and influences consensus achievement, but it has long been ignored because most studies have focused on the consensus mechanism or cost. We introduced the fairness concern into the consensus-reaching process in GDM and established an equilibrium mechanism for GDM. Specifically, we proposed a general consensus game that considers the decision fairness concern. Then, we proved the existence of the Nash Equilibrium between the decision makers and the moderator and established a consensus model with a minimum compensation cost. A simulated annealing algorithm was designed to solve for an equilibrium solution. Through numerical examples used in previous studies and an application example of green supply chain management, we analyzed the relationship between fairness concern, the degree of coordination, and effectiveness in GDM and demonstrated the impact of the fairness concern on consensus achievement. Research article Viewpoint adaptation learning with cross-view distance metric for robust vehicle re-identification Information Sciences, Volume 564, 2021, pp. 71-84 Show abstract Many vehicle re-identification (Re-ID) problems require the robust recognition of vehicle instances across multiple viewpoints. Existing approaches for dealing with the vehicle re-ID problem are insufficiently robust because they cannot distinguish among vehicles of the same type nor recognize high-level representations in deep networks for identical vehicles with various views. To address these issues, this paper proposes a viewpoint adaptation network (VANet) with a cross-view distance metric for robust vehicle Re-ID. This method consists of two modules. The first module is the VANet with cross-view label smoothing regularization (CVLSR), which abstracts different levels of a vehicle’s visual patterns and subsequently integrates multi-level features. In particular, CVLSR based on color domains assigns a virtual label to the generated data to smooth image-image translation noise. Accordingly, this module supplies the viewing angle information of the training data and provides strong robust capability for vehicles across different viewpoints. The second module is the cross-view distance metric, which designs a cascaded cross-view matching approach to combine the original features with the generated ones, and thus, obtain additional supplementary viewpoint information for the multi-view matching of vehicles. Results of extensive experiments on two large scale vehicle Re-ID datasets, namely, VeRi-776 and VehiclelD demonstrate that the performance of the proposed method is robust and superior to other state-of-the-art Re-ID methods across multiple viewpoints. Research article Adaptive Discriminant Analysis for Semi-supervised Feature Selection Information Sciences, 2021 Show abstract As semi-supervised feature selection is becoming much more popular among researchers, many related methods have been proposed in recent years. However, many of these methods first compute a similarity matrix prior to feature selection, and the matrix is then fixed during the subsequent feature selection process. Clearly, the similarity matrix generated from the original dataset is susceptible to the noise features. In this paper, we propose a novel adaptive discriminant analysis for semi-supervised feature selection, namely, SADA. Instead of computing a similarity matrix first, SADA simultaneously learns an adaptive similarity matrix S and a projection matrix W with an iterative process. Moreover. we introduce the ℓ 2 , p norm to control the sparsity of S by adjusting p. Experimental results show that S will become sparser with the decrease of p. The experimental results for synthetic datasets and eight benchmark datasets demonstrate the superiority of SADA, in comparison with 6 semi-supervised feature selection methods. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.02.052", "PubYear": 2021, "Volume": "565", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "Dong<PERSON> Zhang", "Affiliation": "Business School, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 2, "Name": "Yucheng Dong", "Affiliation": "Business School, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610065, China;Corresponding author"}], "References": []}, {"ArticleId": 86955706, "Title": "Malware detection employed by visualization and deep neural network", "Abstract": "With the fast growth of malware’s volume circulating in the wild, to obtain a timely and correct classification is increasingly difficult. Traditional approaches to automatic classification suffer from some limitations. The first one concerns the feature extraction: static approaches are hindered by code obfuscation techniques, while dynamic approaches are time consuming and evasion techniques often impede the correct execution of the code. The second limitation regards the building of the prediction models: the adequateness of a training dataset may degrade over time or can not be sufficient for some malware families or instances. With this paper we investigate the effectiveness of a new approach that uses malware visualization, for overcoming the problems related to the features selection and extraction, along with deep learning classification, whose performances are less sensitive to a small dataset than machine learning. The experiments carried out on twelve different neural network architectures and with a dataset of 20,199 malware, demonstrate that the proposed approach is successful as produced an F-measure of 99.97%.", "Keywords": "Malware classification ; Malware detection ; Malware visualization ; Machine learning ; Deep learning", "DOI": "10.1016/j.cose.2021.102247", "PubYear": 2021, "Volume": "105", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SCMS School of Engineering and Technology, Cochin, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> M L", "Affiliation": "Department of Computer Science and Engineering, SCMS School of Engineering and Technology, Cochin, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Applications, Cochin University of Science and Technology, Cochin, India;Corresponding authors"}, {"AuthorId": 4, "Name": "C.A. Visaggio", "Affiliation": "Department of Engineering, University of Sannio, Benevento, Italy;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SCMS School of Engineering and Technology, Cochin, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SCMS School of Engineering and Technology, Cochin, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, SCMS School of Engineering and Technology, Cochin, India"}], "References": []}, {"ArticleId": 86955709, "Title": "DCA for online prediction with expert advice", "Abstract": "<p>We investigate DC (Difference of Convex functions) programming and DCA (DC Algorithm) for a class of online learning techniques, namely prediction with expert advice, where the learner’s prediction is made based on the weighted average of experts’ predictions. The problem of predicting the experts’ weights is formulated as a DC program for which an online version of DCA is investigated. The two so-called approximate/complete variants of online DCA based schemes are designed, and their regrets are proved to be logarithmic/sublinear. The four proposed algorithms for online prediction with expert advice are furthermore applied to online binary classification. Experimental results tested on various benchmark datasets showed their performance and their superiority over three standard online prediction with expert advice algorithms—the well-known weighted majority algorithm and two online convex optimization algorithms.</p>", "Keywords": "Online learning; Prediction with expert advice; Online DC programming; Online DCA", "DOI": "10.1007/s00521-021-05709-0", "PubYear": 2021, "Volume": "33", "Issue": "15", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "Hoai An Le Thi", "Affiliation": "Department for Management of Science and Technology Development, Ton Duc Thang University, Ho Chi Minh City, Vietnam;Faculty of Mathematics and Statistics, Ton Duc Thang University, Ho Chi Minh City, Vietnam;LGIPM, Université de Lorraine, Metz, France"}, {"AuthorId": 2, "Name": "Vinh Than<PERSON> Ho", "Affiliation": "LGIPM, Université de Lorraine, Metz, France"}], "References": []}, {"ArticleId": ********, "Title": "AI at banking infrastructure", "Abstract": "Efforts for better services are achieved by small steps such as analyzing data of the customer. What is significant for the customer should as well significant for the banking institution. Transparency and a better understand- ding of the pattern behavior of customers can be used for the good of both partners such as good relationships in the fu- ture eventually be beneficial for the customer as well as a banking institution. The responsibility of both sides is crucial to understand the accountability of customers and banking institutions. The method of identification of user messages of the banking application proposed in the article involves the use of user data for information processing, taking into account the peculiarities of the use of mobile devices and the user's dialogue with bank messages. Also, the proposed method allows you to rank messages to identify the most important messages and get the desired result by providing ef- fective recommendations in favor of each of the participants in customer interaction with the bank. The introduction of modern educational programs \"Information Control Systems and Technologies\", \"Artificial Intelligence Systems\" and \"Systems Analysis\" in the field of information technology, allows users and managers to interact with the bank's custo- mers sufficient information to make informed recommendations for effective management decisions. The article consi- ders the conceptual model of interaction of users and managers on interaction with users of the bank, use of technolo- gies and algorithms of artificial intelligence, machine learning processes to formalize the process of dialogue, systema- tization and ranking of messages and notifications between customers and managers. The Conceptual model of inter- action of the user of banking services with messages is presented. The article also describes the features of the dialogue between the user of banking services and the manager for the implementation of algorithms for interaction with custo- mers. The example of the city block of bank users considers and takes into account the difference in the amount of in- formation received by the bank, which must be sent during different periods of the week and take into account the amount of information to be sent, which will be significantly less and, consequently, the number of necessary services. will also be smaller. In this example, taking into account the amount of information that can be consumed during differ- rent periods of the week, the number of services that can be provided to the user will also be much smaller. The reflec- tion of such interactions in the model is an important aspect, as noted in the article.", "Keywords": "", "DOI": "10.15407/jai2020.04.007", "PubYear": 2020, "Volume": "25", "Issue": "4", "JournalId": 84968, "JournalTitle": "Artificial Intelligence", "ISSN": "2710-1673", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Ustenko S.V.", "Affiliation": "State Higher Education Institution “Vadym Hetman Kyiv National Economic University”"}, {"AuthorId": 2, "Name": "Ostapovych T.V.", "Affiliation": "State Higher Education Institution “Vadym Hetman Kyiv National Economic University”"}], "References": []}, {"ArticleId": ********, "Title": "When minutes matter: A university emergency notification system dataset", "Abstract": "<p>The data presented in this data article were collected at three points in the 2012-2013 academic year; Fall 2012, Spring 2013, and Summer 2013 from undergraduate students enrolled in an introductory health education course at a large university in the United States. The data regarding undergraduate students' perceptions of and experiences with the campus emergency notification system were ascertained using a self-administered online-delivered survey instrument. The data included in the Mendeley Data repository affiliated with this data article encompass closed- and open-ended responses from 746 undergraduate students. Closed-ended questions included items based on central constructs from Technology Acceptance Model research-perceived usefulness, perceived ease of use, attitudes toward use, and behavioral intention. Survey questions also assessed students' actual use of emergency notification messages, students' perceived self-efficacy to respond to future potential emergency notifications, and demographics and technology use characteristics. This research team asked open-ended questions to collect students' ideas for systematic improvement in their own words. Descriptive statistics for demographic variables, participant characteristic variables, and scale variables were conducted in SPSS 27 and are provided in tables. The open-ended question response frequencies were also calculated in SPSS 27 and are provided within a supplemental PDF. To date, no data pertaining to an institution of higher education's emergency notification system are published for open access use. This article provides open access data and surveys that campus emergency planners, researchers, and health education specialists can use to inform emergency communication plans, improve the content of critical campus alert messages, structure future emergency notification studies, and frame future emergency notification system evaluations. This research team anticipates these data will help campus emergency personnel craft more effective messages and optimize their channel mixture to make emergency notifications reach and resonate with students in situations when minutes matter. The data for this article are hosted in a .csv file for widespread access in the following Mendeley Data repository: https://data.mendeley.com/datasets/6jdwfbwzk5/1.</p><p>© 2021 The Authors. Published by Elsevier Inc.</p>", "Keywords": "College students;Colleges and universities;Emergency notification system;Health education;Health promotion;Public health", "DOI": "10.1016/j.dib.2021.106910", "PubYear": 2021, "Volume": "35", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Texas Woman's University, United States;Corresponding author;@dr_menn"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Auburn University, United States;@carolinempurvis"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Alabama, United States;@ChaneyBeth"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Alabama, United States;@JDonChaney"}], "References": [{"Title": "The Health Education Research Experience (HERE) program metadata dataset", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "", "Page": "105180", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 86955786, "Title": "HCAC-EHR: hybrid cryptographic access control for secure EHR retrieval in healthcare cloud", "Abstract": "<p>Technology that is perfect is free of vulnerability. Technological growth offers users online data storage and access to it from anywhere. Cloud computing is a model that provides data storage on a contract facility and a slew of different services. Today, online data relating to health is inevitably stored and managed. These health records comprise data that includes X-ray images, scanned images, therapy procedures, medical prescriptions, and patient information. Medical professionals use the stored health data for diagnosis, patients for their understanding, and government and insurance companies for further follow-up. Since multiple category of users want access to health data, data needs protection and to be stored with extreme security before being stored online in the form of electronic health records (EHRs) with proper access control mechanisms. To this end to provide secure cloud storage, we propose a novel scheme by implementing a hybrid cryptography algorithm in which we use Improved Key Generation Scheme of RSA (IKGSR) algorithm to encrypt health data and Blowfish algorithm for key encryption. We follow steganography-based access control for key sharing by means of substring indexing and keyword search mechanism to efficiently retrieve the encrypted data. We measure performance evaluation as well as the security of the proposed method and compare with existing hybrid method consider New York State Department of Health dataset. The results clearly confirm that our method provides better security and also retrieves data efficiently.</p>", "Keywords": "Cloud security; Steganography-based access control; Hybrid cryptography; Electronic health record (EHR); Wildcard-fuzzy search; Health Care Cloud", "DOI": "10.1007/s12652-021-02942-2", "PubYear": 2022, "Volume": "13", "Issue": "2", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Sri Shakthi Institute of Engineering and Technology, Coimbatore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Kalasalingam Academy of Research and Education, Kirshnankoil, India"}], "References": [{"Title": "Secure keyword search using dual encryption in cloud computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1063", "JournalTitle": "International Journal of Information Technology"}, {"Title": "RETRACTED ARTICLE: Enhancing the security of cloud data using hybrid encryption algorithm", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "S1", "Page": "51", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Hiding shares by multimedia image steganography for optimized counting-based secret sharing", "Authors": "<PERSON><PERSON>; Maimo<PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "11-12", "Page": "7951", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 86955790, "Title": "Dispersing Obnoxious Facilities on a Graph", "Abstract": "Abstract We study a continuous facility location problem on a graph where all edges have unit length and where the facilities may also be positioned in the interior of the edges. The goal is to position as many facilities as possible subject to the condition that any two facilities have at least distance $$\\delta$$ \n δ \n from each other. We investigate the complexity of this problem in terms of the rational parameter $$\\delta$$ \n δ \n . The problem is polynomially solvable, if the numerator of $$\\delta$$ \n δ \n is 1 or 2, while all other cases turn out to be NP-hard.", "Keywords": "Algorithms; Complexity; Optimization; Graph theory; Facility location; Mathematics of computing: graph theory; Theory of computation: graph algorithms analysis; Theory of computation: discrete optimization", "DOI": "10.1007/s00453-021-00800-3", "PubYear": 2021, "Volume": "83", "Issue": "6", "JournalId": 2779, "JournalTitle": "Algorithmica", "ISSN": "0178-4617", "EISSN": "1432-0541", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Quantitative Economics, Maastricht University, Maastricht, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, RWTH Aachen, Aachen, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Discrete Mathematics, TU Graz, Graz, Austria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, RWTH Aachen, Aachen, Germany"}], "References": []}, {"ArticleId": 86955809, "Title": "The Relationship between Massive Online Open Courses (MOOCs) Content Design and Students’ Performance", "Abstract": "<p>Over the years, there has been tremendous growth in online social networking which contributed to the revolution of higher education’s learning environment. Massive Online Open Courses (MOOC) is now common in online education, which provides open networks of self-directed learning. MOOCs are referred as a continuation trend in innovation that initiated by learning from geographical distance and online. MOOCs is a current evolvement in higher learning institution in Malaysia with the aims to provide quality education for the students. However, there are some challenges in developing effective instructional design courses and retaining learners in MOOCs. Therefore, the purpose of this study is to discover the relationship between MOOC’s content design and students’ performances among UTeM’s engineering students. Through literature review, variables are identified such as courses content design, enhancement in teaching and learning and students’ performance. In this study, 373 samples have been collected and the data analysed using SPSS. The results revealed that there are significant relationships between courses materials, courses activities, and courses tools with students’ performance. It is believed that this research paper will beneficial to higher education to improve MOOC’s content design in order to enhance the students’ performance in the future.</p>", "Keywords": "MOOC, MOOC’s Content Design, Students’ Performance,  Higher Education, UTeM", "DOI": "10.3991/ijim.v15i04.20201", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "entre for Technopreneurship Development (CTeD), Universiti Teknikal Malaysia Melaka, Malays"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Technopreneurship Development (CTeD), Universiti Teknikal Malaysia Melaka, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Technopreneurship Development (CTeD), Universiti Teknikal Malaysia Melaka, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Technopreneurship Development (CTeD), Universiti Teknikal Malaysia Melaka, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Pendidikan Sultan <PERSON>, Perak, Malaysia"}], "References": []}, {"ArticleId": 86955810, "Title": "Information and Communication Technology-Based Education Planning and Attitude of College Students", "Abstract": "<p>The ubiquity of Information and Communication Technology (ICT) in every school framework in the world has become a trend. With ASEAN integration problems in 2015, can fresh and younger HEI's and their students cope with this trend? The goal of this analysis is to decide the preparedness and role of new HEI students in the field of ICT education. In this study a concise template utilizing a researcher-made questionnaire (α=.971) was used. The research findings show that students at college have a optimistic declaration ('agreement') on ICT use in education (x=3.71; S=.75). This was also shown that sex greatly affects the mindset of students (z=3.91, p=.00), where male attitudes are higher (x=3.91) than female attitudes (x=3.61). Moreover, it demonstrates that preparedness for ICT in general has little to do with the mindset of students towards the usage of ICT in school. Nonetheless, there is a important marginal positive association between ICT access and an attitude of significance to 0.05 (r=.15; p=.002; single-tailed), which suggests that the more a student gets introduced to ICT, the more he develops his attitude to ICT education. It was believed that ICT-based curriculum can be readily adopted and modified for the next several years by complete ICT adoption as the current educational framework. Current and younger HEIs will also spend more in ICT Accessibility and Connectivity to further boost ICT-based awareness and preparedness.</p>", "Keywords": "Schooling Focused on ICT, ICT Mindset, ICT Planning", "DOI": "10.3991/ijim.v15i04.20365", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Business, Social Science & Hospitality Segi University, Kota Damansara, 47810 Petaling Jaya, Selangor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> International Business School (AHIBS), Universiti Teknologi Malasia, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor Abbottabad University of Science and Technology"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Lecturer, Department of Management Sciences, Abbottabad University of Science and Technology, Pakistan PhD Candidate, Faculty of Business, Economics & Social Development, Universiti Malaysia Terengganu, Malaysia"}], "References": []}, {"ArticleId": 86955822, "Title": "Factorized solution of generalized stable Sylvester equations using many-core GPU accelerators", "Abstract": "<p>We investigate the factorized solution of generalized stable Sylvester equations such as those arising in model reduction, image restoration, and observer design. Our algorithms, based on the matrix sign function, take advantage of the current trend to integrate high performance graphics accelerators (also known as GPUs) in computer systems. As a result, our realisations provide a valuable tool to solve large-scale problems on a variety of platforms. </p>", "Keywords": "Sylvester equation; Matrix sign function; Newton iteration; GPUs", "DOI": "10.1007/s11227-021-03658-y", "PubYear": 2021, "Volume": "77", "Issue": "9", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Max Planck Institute for Dynamics of Complex Technical Systems, Magdeburg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería, Universidad de la República, Montevideo, Uruguay"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería, Universidad de la República, Montevideo, Uruguay"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería, Universidad de la República, Montevideo, Uruguay"}, {"AuthorId": 5, "Name": "<PERSON>-Ortí", "Affiliation": "DISCA, Universitat Politècnica de València, Valencia, Spain"}], "References": []}, {"ArticleId": 86955842, "Title": "Measuring gap in expected and perceived quality of ICT enabled customer services: a systematic study of top ten retailers of India", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASS.2020.113260", "PubYear": 2020, "Volume": "9", "Issue": "2", "JournalId": 18638, "JournalTitle": "International Journal of Applied Systemic Studies", "ISSN": "1751-0589", "EISSN": "1751-0597", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955843, "Title": "Social Perceived Value on Social Media and Online News Portal: Benefits to The Aborigines Women in Malaysia", "Abstract": "<p>Online news media and social media applications recorded an unprecedented success in just few of the recent years. However, online news media and social media domain are still little understood in term of usage among aborigines or Orang Asli in Malaysia. Thus, this research is to see the social perceived value on social media and online news portal among Orang Asli. A quantitative research design is used based on data will be collected via questionnaire from Orang Asli at Kampung Batu 12, Gombak, Selangor, at the outskirt of Kuala Lumpur. Questionnaires were distributed to 50 Orang Asli aged 15 and above. Factor analysis was used to examined the data. Exploratory factor analysis was used to extracts new factor, Online media usage impact. The social values perceived (16 items) factor was examined. After identifying the component based on an examination of eigenvalues and scree plot, rotation was done by varimax method. The items with 0.5 loadings or greater are considered practically significant.  The findings showed that most of the Orang Asli gathered current information and news from the social media and social media is the gateway to online news portal. Furthermore, despite of financial circumstances, they willing to spend in order to have the internet access as it gave positive social values in their life, plus the applications internet access were all user friendly.</p>", "Keywords": "Perceived Value;Social Media;Orang <PERSON>", "DOI": "10.3991/ijim.v15i04.20189", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Associate Professor, Department of Media and Communication Studies, Faculty of Arts and Social Sciences, University of Malaya, Kuala Lumpur, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Postgraduate Student, Department of Media and Communication Studies, Faculty of Arts and Social Sciences, University of Malaya, Kuala Lumpur, Malaysia"}], "References": []}, {"ArticleId": 86955844, "Title": "Constructing and Validating University Students’ Blended Learning Acceptance Scale", "Abstract": "<p class=\"0abstract\"><strong>Abstract—</strong>Increasingly, blended learning courses are being offered in schools throughout China. One of the key factors influencing the effectiveness of blended learning is students’ acceptance. The researchers proposed six predictive dimensions that contribute to students’ acceptance for blended learning, namely, perceived usefulness, perceived ease of use, learning climate, attitudes towards online learning, attitudes towards classroom learning and personal charm of the instructor. One existing questionnaire is adapted, together with the interview data with university undergraduates, generating 63 items for the initial questionnaire. The first draft of the scale was distributed to 180 university students in Hubei Province, China. Data collected were analyzed by SPSS 24.0. Eleven items were deleted based on the Exploratory Factor Analysis results. Then the second pilot was done to purify the scale further. 191 questionnaires were distributed, and the data collected were processed by EFA again. One item was removed in the second pilot and 51 items were reserved for the final version of the scale. The final adapted version of Blended Learning Acceptance Scale (BLAS) is reported to possess high levels of reliability and validity. The BLAS can be used to investigate Chinese University students’ acceptance for blended learning so that better opinions can be put forward to improve the effectiveness of blended learning.</p>", "Keywords": "Blended Learning;Acceptance;University Students;Scale", "DOI": "10.3991/ijim.v15i04.20195", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hubei Normal University, Huangshi, China, & Infrastructure University Kuala Lumpur (IUKL), Selangor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Normal University, Huangshi, China, & Infrastructure University Kuala Lumpur (IUKL), Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknologi MARA (UITM), Selangor, Malaysia"}], "References": []}, {"ArticleId": 86955845, "Title": "Digital Technologies in Education 4.0. Does it Enhance the Effectiveness of Learning? A Systematic Literature Review", "Abstract": "<p class=\"0abstract\"><strong>Abstract—</strong> Over the past decade, digitalization shapes the overall educational structure worldwide, with the attention received from practitioners, researchers, and policymakers for educational development.   Digital technologies are bringing massive changes across education, skills, and employment. These changes mirror how technology is increasingly central to education 4.0. Digital technologies are expanding beyond innovative and less traditional techniques of teaching and learning via education collaboration. However, the present study will explore the research conducted on digital technologies and education. Data is selected from the Scopus database reputed journals. The final 47 studies are chosen for the review process using PRISMA statement 2015, and bibliometric analysis is done to find the occurrence of keywords.  The findings of the study are strengthening the value of educational growth and development of high-tech skills. Education's Future focuses on digital technologies, and the traditional modes of education will be replaced entirely.</p>", "Keywords": "Digital Technologies, Digital Transformation, E-Learning, Education, Infor-mation Management, Digital Education, and Artificial Intelligence", "DOI": "10.3991/ijim.v15i04.20291", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UniKL Business School Universiti Kuala Lumpur, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> International Business School (AHIBS), Universiti Teknologi Malaysia (UTM), 81310, <PERSON><PERSON>, Johor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Technology Management & Business, Universiti Tun Hussein Onn Malaysia, Johor, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Technology Management & Business, Universiti Tun Hussein Onn Malaysia, Johor, Malaysia"}], "References": []}, {"ArticleId": 86955906, "Title": "Efficient algorithm design on hybrid CPU-FPGA architecture for high performance computing", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCC.2021.113239", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 19270, "JournalTitle": "International Journal of Systems, Control and Communications", "ISSN": "1755-9340", "EISSN": "1755-9359", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86955907, "Title": "PAPR reduction of MIMO-OFDM using galaxy inspired swarm-based PTS strategy", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCC.2021.113243", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 19270, "JournalTitle": "International Journal of Systems, Control and Communications", "ISSN": "1755-9340", "EISSN": "1755-9359", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86956065, "Title": "NetV.js: A web-based library for high-efficiency visualization of large-scale graphs and networks", "Abstract": "Graph visualization plays an important role in several fields, such as social media networks, protein–protein interaction networks, and traffic networks. A number of visualization design tools and programming toolkits have been widely used in graph-related applications. However, a key challenge remains in the high-efficiency visualization of large-scale graph data. In this study, we present NetV.js, an open-source and WebGL-based JavaScript library that supports the fast visualization of large-scale graph data (up to 50 thousand nodes and 1 million edges) at an interactive frame rate with a commodity computer. Experimental results demonstrate that our library outperforms existing toolkits (Sigma.js, D3.js, Cytoscape.js, and Stardust.js) in terms of performance.", "Keywords": "Graph ; Graph visualization ; Network visualization ; Node-link diagram", "DOI": "10.1016/j.visinf.2021.01.002", "PubYear": 2021, "Volume": "5", "Issue": "1", "JournalId": 47888, "JournalTitle": "Visual Informatics", "ISSN": "2543-2656", "EISSN": "2468-502X", "Authors": [{"AuthorId": 1, "Name": "Dongming Han", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, Zhejiang, China;Zhejiang Lab, Hangzhou, Zhejiang, China"}, {"AuthorId": 2, "Name": "Jiacheng Pan", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, Zhejiang, China;Zhejiang Lab, Hangzhou, Zhejiang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, Zhejiang, China;Zhejiang Lab, Hangzhou, Zhejiang, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, Zhejiang, China;Corresponding author"}], "References": [{"Title": "RCAnalyzer: visual analytics of rare categories in dynamic networks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "4", "Page": "491", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "DataV: Data Visualization on large high-resolution displays", "Authors": "<PERSON><PERSON> Mei; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "3", "Page": "12", "JournalTitle": "Visual Informatics"}]}, {"ArticleId": 86956070, "Title": "C-TOL: Convex triangulation for optimal node localization with weighted uncertainties", "Abstract": "There is always a need to reduce localization error in any wireless sensor network (WSN), and our aim is to observe the impact of localization uncertainty on network awareness. When nodes are deployed in a 2D plane and their l 2 -norm ranged triangulations are found, usually the unweighted localization uncertainty values become absurdly large with large triangulation cases. Moreover, there is no regard for the disparity between the lengths of any two links on the localization uncertainty. The upper bound of uncertainty keeps on rising with formation of asymmetric node triangulations with longer internodal distances and sharper vertices. To address this gap, a convex combination weighted approach (C-TOL, standing for Convex-Triangulation for Optimal node Localization) for solving the localization uncertainty problem is described here. The advantage of the proposed method is shown with the help of rigorous mathematical analysis of weighted uncertainty behaviour. The relationship of sensor node symmetry with triangulation uncertainty is formulated algebraically by considering both symmetric as well as asymmetric triangulations. <PERSON><PERSON><PERSON> bound is derived to justify estimation under triangulation uncertainty. This approach paves the way for the WSN to prioritize different kinds of triangulations. Numerical results reveal that the weighted method prefers triangulations with more symmetry; hence it consistently achieves significantly lower values of mean and standard deviations than the existing unweighted localization technique, especially for densely connected sensor networks. Moreover, the proposed method shows robust localization performance for sparsely deployed networks as well, when compared to recent methods in literature.", "Keywords": "Convex combination ; Fisher information matrix (FIM) ; Triangulation uncertainty ; Target localization", "DOI": "10.1016/j.phycom.2021.101300", "PubYear": 2021, "Volume": "46", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": " Prateek", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Patna, Bihar, 800005, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Patna, Bihar, 800005, India"}], "References": []}, {"ArticleId": 86956161, "Title": "Attractive community detection in academic social network", "Abstract": "Academic social network analysis has attracted significant attention. For each researcher in such a network, he/she has several research interests. We regard these researchers sharing common interests as a research community. For each community, it may be attractive or not to researchers from other communities. In this paper, we study a new and interesting problem: which is the most attractive research community in the academic social network? Here, attractive research communities are those potentially valuable and increasingly popular communities, which are different from hot communities. To address this problem, we first extract both of the internal and external features of attractive research communities. The internal feature refers to the novelty of the topic in the research community and the external feature refers to the researchers’ transition among the communities. Intuitively, a community with a novel topic attracts the researchers from other research communities can be considered as the attractive community. Based on the extracted features, we design a novel Attractive Research community Ranking (ARTRank) algorithm to rank the research communities. The core idea of this algorithm lies in two measurements for each community: a positiveness score and a negativeness score, which measure the attractiveness of a community from the in-attention aspect and the out-attention aspect, respectively. Similar to HITS, these two scores are calculated in an iterative way until convergence. Through extensive experiments, we show that our proposed algorithm significantly outperforms the state-of-the-art algorithms in terms of the recommendation intensity.", "Keywords": "Social network analysis ; Community ranking ; Attractiveness ; HITS", "DOI": "10.1016/j.jocs.2021.101331", "PubYear": 2021, "Volume": "51", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Telecommunication and Navigation Satellites, China Academy of Space Technology, Beijing, China;Corresponding author"}, {"AuthorId": 2, "Name": "Xiaodong Han", "Affiliation": "Institute of Telecommunication and Navigation Satellites, China Academy of Space Technology, Beijing, China"}], "References": []}, {"ArticleId": 86956369, "Title": "Image segmentation using multilevel thresholding based on type II fuzzy entropy and marine predators algorithm", "Abstract": "<p>The digital image segmentation is an open problem that is growing day by day and is attracting the attention of researchers from last few years. Image resolution and their speed has led to the use of thresholding approaches. Image thresholding is simple, easy and effective method for image segmentation. Multi-level image thresholding is a key perspective in several real-time pattern recognition and image processing-based applications. It identifies pixels quickly and effectively in different groups indicating multiple regions in an image. Segmentation of images based on thresholding by using various intelligent optimization techniques with fuzzy entropy is widely utilized for defining thresholds in a better way to use them precisely. In this research, a novel technique for multi-level thresholding is proposed by combining Fuzzy Entropy Type II (FE-TII) with recently developed meta-heuristics named Marine Predators Algorithm (MPA). For achieving optimal thresholds of an image, the maximization of entropy is tedious and consumes a lot of time with an increasing number of thresholds. The MPA method presented is analyzed in context with image segmentation, particularly on thresholds with TII-FE. For this reason, proposed methodology is evaluated using several images along with the distribution of histograms. For analyzing the performance efficiency of the proposed methodology, the results are compared and robustness is tested with efficiency of proposed technique to multi-level image segmentation, several images are used randomly from datasets.</p>", "Keywords": "Multilevel thresholding; Digital image; Type II fuzzy entropy; Stochastic optimization; Marine predators algorithm; Evolutionary computation", "DOI": "10.1007/s11042-021-10641-5", "PubYear": 2021, "Volume": "80", "Issue": "13", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronics and Communication Engineering, Shri <PERSON> Devi University, J&K, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Deptt. of Electronics and Communication Engineering, Chandigarh University, Punjab, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Communication Engineering, Shri <PERSON> Devi University, J&K, India"}], "References": [{"Title": "Improving image thresholding by the type II fuzzy entropy and a hybrid optimization algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "19", "Page": "14885", "JournalTitle": "Soft Computing"}, {"Title": "Marine Predators Algorithm: A nature-inspired metaheuristic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "113377", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Research of improving semantic image segmentation based on a feature fusion model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "11", "Page": "5033", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 86956381, "Title": "The 35th Year of AI EDAM: Where are We Heading?", "Abstract": "", "Keywords": "", "DOI": "10.1017/S0890060421000032", "PubYear": 2021, "Volume": "35", "Issue": "1", "JournalId": 9460, "JournalTitle": "Artificial Intelligence for Engineering Design, Analysis and Manufacturing", "ISSN": "0890-0604", "EISSN": "1469-1760", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86956409, "Title": "Firms’ strategy analysis under different retailing formats considering emission reduction efficiency and low-carbon preference", "Abstract": "<p>In response to the cap-and-trade policy, many manufacturers begin to reduce emissions by various measures, such as investing in technologies and using clean materials, which will affect their operation strategies and further affect the whole supply chain. Based on this, we explore how the cap-and-trade policy affects firms’ operation decisions under different retailing formats. We study a supply chain with an e-commerce platform and one manufacturer, in which they can cooperate via reseller or marketplace formats. Meanwhile, we employ a new cost function of emission reduction by incorporating the manufacturer’s emission reduction efficiency (ERE). We find that in the absence of low-carbon preference, the manufacturer’s and the platform’s profits both increase as the manufacturer’s ERE improves in reseller format; but in marketplace format, the platform is worse off as the manufacturer’s ERE improves if the efficiency is larger than a threshold. Besides, under which format the platform or the manufacturer is better off is related to the product category. For some product categories, low-carbon preference could alter the retailing format under which the platform or the manufacturer gains a higher profit. Moreover, the platform and the manufacturer are both better off in the presence of low-carbon preference, regardless of retailing formats.</p>", "Keywords": "Low-carbon supply chain; The cap-and-trade policy; Reseller; Marketplace", "DOI": "10.1007/s00500-021-05667-8", "PubYear": 2021, "Volume": "25", "Issue": "8", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "Jinjin Liu", "Affiliation": "School of Economics and Management, Tongji University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Tongji University, Shanghai, China"}], "References": [{"Title": "Impact of emission reduction investments on decisions and profits in a supply chain with two competitive manufacturers", "Authors": "Jin<PERSON> Liu; <PERSON><PERSON>; Guangdong Tian", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106784", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": ********, "Title": "Dolphin Swarm Inspired Protocol (DSIP) for Routing in Underwater Wireless Sensor Networks", "Abstract": "Underwater communication is still carried out using communication cables because of the minimum development that is established in underwater wireless communications. The utilization of wires to make sure the connectivity of sensor nodes that are located at the bottom of the sea is highly expensive. Finding the best route to send the sensed data to the destination in minimum duration has become a primary challenge in underwater wireless sensor networks (UWSN). Feasible routing protocols available for general sensor networks are not feasible for UWSN because of the difficult communication medium. Existing routing protocol face the problem of consuming more energy to deliver the data packet and also due to selecting the unfit route it faces more delay. To overcome the routing challenges present in UWSN, Dolphin Swarm Inspired Protocol (DSIP) is proposed in this paper. DSIP is inspired by the swarming nature of dolphins towards finding their food. Four significant phases involved in DSIP to find the best route in UWSN are searching, calling, reception, and predation. NS3 is used to evaluate the performance of DSIP against previous routing protocols with benchmark metrics namely packet delivery ratio, end-to-end delay, node death rate, and energy consumption. Results indicate that DSIP has consumed 1.43 times less energy than other previous protocols.", "Keywords": "Energy; Delay; Swarm; Dolphin; Routing; Packet Delivery Ratio.", "DOI": "10.22247/ijcna/2021/207981", "PubYear": 2021, "Volume": "8", "Issue": "1", "JournalId": 39456, "JournalTitle": "International Journal of Computer Networks And Applications", "ISSN": "", "EISSN": "2395-0455", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, PKR Arts College for Women, Gobichettipalayam, Erode, Tamil Nadu"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, PKR Arts College for Women, Gobichettipalayam, Erode, Tamil Nadu"}], "References": []}, {"ArticleId": ********, "Title": "A Critical Survey on Overhead Control Traffic Reduction Strategies in Software-Defined Wireless Sensor Networking", "Abstract": "The rising interest in the Internet of Things has contributed to the rapid deployment of wireless sensor networks (WSNs). However, as a result of the design of the sensor nodes and networks, WSNs exhibit dynamic challenges in mobile and large-scale applications. The nodes are equipped with limited resources and the networks have static architectures. These problems hinder the effective implementation of WSNs. Software-Defined Networking (SDN) is intended to overcome these problems by removing control logic from the data plane and incorporating programmability to allow dynamic management and control of the nodes. Unfortunately, the gains from incorporating SDN into WSNs are diminished by high overhead control traffic, created to discover and maintain a global network topology view, leading to impaired network performance. This paper provides a systematic overview of the software-defined wireless network sensor literature to identify potential gaps and to provide recommendations for future studies.", "Keywords": "Software-Defined Wireless Sensor Networks; Topology Discovery Protocol; Minimal Overhead Control Traffic; Energy Consumption; Software-Defined Networking.", "DOI": "10.22247/ijcna/2021/207978", "PubYear": 2021, "Volume": "8", "Issue": "1", "JournalId": 39456, "JournalTitle": "International Journal of Computer Networks And Applications", "ISSN": "", "EISSN": "2395-0455", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Telecommunication Engineering, Kwame Nkrumah University of Science and Technology, Kumasi"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Telecommunication Engineering, Kwame Nkrumah University of Science and Technology, Kumasi"}, {"AuthorId": 3, "Name": "Justice <PERSON><PERSON><PERSON>", "Affiliation": "Department of Telecommunication Engineering, Kwame Nkrumah University of Science and Technology, Kumasi"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON>wu<PERSON>", "Affiliation": "Department of Telecommunication Engineering, Kwame Nkrumah University of Science and Technology, Kumasi"}], "References": []}, {"ArticleId": 86956481, "Title": "Elliptic Curve Cryptography based Secure Image Transmission in Clustered Wireless Sensor Networks", "Abstract": "Wireless Sensor Networks (WSN) is arising as a potential computing platform in diverse zones such as weather forecasting, modern robotization, medical health care, and military systems, etc. Since the sensors are constantly gathering information from the actual world and communicate with one another through remote connections, keeping up the security and protection of WSN communication is a prerequisite. In this paper, safe confirmation and key organization scheme dependent on Elliptic Curve Cryptography (ECC) has been suggested to make sure about information/picture transmission in WSNs. The scheme proposed in this paper is protected, competent, and appropriate for providing sensor technology based IoT services and applications. The protocol provides all the security features such as mutual authentication, confidentiality, data integrity, perfect forward secrecy, fair key agreement, etc. and is secure against hello flood attack, DoS attack, man-in-middle attack, etc. Simulation software AVISPA has confirmed the safety of the protocol for the known assaults. The performance analysis ensures the superiority of the projected proposal over the existing schemes.", "Keywords": "WSN; Security; Elliptical Curve Cryptography (ECC); Automated Validation of Internet Security Protocols and Applications (AVISPA).", "DOI": "10.22247/ijcna/2021/207983", "PubYear": 2021, "Volume": "8", "Issue": "1", "JournalId": 39456, "JournalTitle": "International Journal of Computer Networks And Applications", "ISSN": "", "EISSN": "2395-0455", "Authors": [{"AuthorId": 1, "Name": ", Rekha", "Affiliation": "Department of Computer Science and Applications, <PERSON><PERSON><PERSON> (Deemed to be University), <PERSON><PERSON><PERSON> (Ambala)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, <PERSON><PERSON><PERSON> (Deemed to be University), <PERSON><PERSON><PERSON> (Ambala)"}], "References": []}, {"ArticleId": 86956540, "Title": "Design and Analysis of a Reliable, Prioritized and Cognitive Radio-Controlled Telemedicine Network Architecture for Internet of Healthcare Things", "Abstract": "This paper proposes and evaluates a reliable and efficient wireless telemedicine network architecture using cognitive radio network technology for e-Health applications. The proposed architectural framework is designed, to tackle congestion and inconsistency in network availability using the cognitive radio (CR) and to provide priority-based health services to distant primary health care centers. The proposed architectural framework utilizes the (1) dynamic prioritization scheme of the data, based on patient condition (2) prioritization based channel allocation using novel MAC protocol and (3) efficient utilization multiple wireless communication technologies using cognitive radio network. This paper utilizes the Data Sensitive Adaptive MAC (DSA-MAC) protocol for medical data prioritization and transmission at a body area network level (consist of multiple wireless medical sensors implanted on the single patient) of communication. Based on DSA-MAC, a novel MAC layer protocol, Node Sensitive Adaptive MAC (NSA-MAC) protocol is developed to prioritize the different patients based on their medical conditions and assist the prioritization based data transfer. Finally, the proposed architectural framework tackles congestion and inconsistency in network availability by shifting the data transfer process to any of the available networks (GSM, 3G-UMTS, WiMAX and 4G-LTE), with the help of cognitive radio technology.", "Keywords": "Wireless Network Application; Internet of Healthcare Things; Cognitive Radio; MAC protocol; E-Health; Rural Health.", "DOI": "10.22247/ijcna/2021/207982", "PubYear": 2021, "Volume": "8", "Issue": "1", "JournalId": 39456, "JournalTitle": "International Journal of Computer Networks And Applications", "ISSN": "", "EISSN": "2395-0455", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering Department, Motilal Nehru National Institute of Technology Allahabad, Prayagraj, Uttar Pradesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Meerut Institute of Engineering Technology, Meerut, Uttar Pradesh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering Department, Motilal Nehru National Institute of Technology Allahabad, Prayagraj, Uttar Pradesh"}], "References": []}, {"ArticleId": 86956621, "Title": "Novel OneM2M Communication Mechanism Based on Labeling of IoT Devices", "Abstract": "The increasing demand for food has raised the profile of the agricultural sector. The Internet of Things (IoT) is an important technology that is capable of solving many problems in modern agriculture. In traditional IoT communication technology, the operation technology of a single platform through remote or decentralized management is reasonably mature. In such an environment, all devices need to return the results of their execution tasks to the core server, and the server dispatches further script tasks to be performed. However, there is a problem in the mechanisms used for communication management of the devices at the same time when a large amount of data needs to be transmitted. The machine-to-machine (M2M) service platform has been standardized to enable the communication of devices, which is the basis for smart environments with IoT. Recently, the OneM2M standard communication mechanism has provided a way for devices to send messages to core servers and assign specific tasks to other devices, and a two-way communication mechanism capable of returning and sending instructions has been realized. In this study, an innovative label computing model combined with the M2M communication mechanism was proposed, and labels were used to record events in a standard way. By notifying all devices of the result of the labeling through the M2M mechanism, the cost of data transmission among the devices of a network can be reduced. The proposed model can also automatically determine how to assign and execute IoT operation tasks through the labeling mechanism. Finally, the proposed model was implemented in a smart agriculture environment to increase agricultural production. The execution time on a simulated smart agricultural system was reduced by about 30% compared with that on an existing system. © MYU K.K.", "Keywords": "Communication; Computing model; IoT; OneM2M", "DOI": "10.18494/SAM.2021.3045", "PubYear": 2021, "Volume": "33", "Issue": "2", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Engineering, Longyan University, Fujian, No. 1, North Dongxiao Rd., Longyan City, Fujian Province, 364012, China"}, {"AuthorId": 2, "Name": "Yao<PERSON><PERSON>", "Affiliation": "Department of Information Management, Hsuan Chuang University, Hsinchu, No. 48, Hsuan Chuang Rd., Xiangshan Dist., Hsinchu City, 300, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Healthcare Information & Management, Ming Chuan University/Hsuan Chuang University, No. 48, Xuanzhuang Rd., Xiangshan Dist., Hsinchu City, 300, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Engineering, Longyan University, Fujian, No. 1, North Dongxiao Rd., Longyan City, Fujian Province, 364012, China"}], "References": []}, {"ArticleId": 86956804, "Title": "Mathematical Model for Gaussian-Like Pulse Design for a UWB System Based on the Generalized Bessel Polynomials", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.11", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "King <PERSON><PERSON>’s Institute of Technology Ladkrabang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "King <PERSON><PERSON>'s Institute of Technology Ladkrabang"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "King <PERSON><PERSON>'s Institute of Technology Ladkrabang"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Southeast Bangkok Colleage"}], "References": []}, {"ArticleId": 86956852, "Title": "Critical Reflections on Outsourcing for a Mobile Communications Company", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.04", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Orange"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cardiff Metropolitan University"}], "References": []}, {"ArticleId": 86956853, "Title": "Spatial and Temporal Shoreline Changes of the Bang Pakong District, Bang Pakong Subdistrict, Chachoengsao Province, Thailand in 2009-2018", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.06", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "Surat Poolsawat", "Affiliation": "King <PERSON><PERSON>’s Institute of Technology Ladkrabang"}, {"AuthorId": 2, "Name": "Somkid <PERSON>", "Affiliation": "Mahidol University"}, {"AuthorId": 3, "Name": "Wannapong Triampo", "Affiliation": "Mahidol University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mahidol University, Bangkok"}], "References": []}, {"ArticleId": 86956854, "Title": "Services Recommendation on Intercity Motorway", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.10", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shinawatra University, Pathum Thani"}, {"AuthorId": 2, "Name": "Suchai Thanawastien", "Affiliation": "Shinawatra University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shinawatra University"}], "References": []}, {"ArticleId": ********, "Title": "Simulation Based AGVs Routing Analysis for Internal Logistics in Automotive Manufacturing", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.05", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "Busaba Phruksaphanrat", "Affiliation": "Thammasat University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Thammasat University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Thammasat University"}], "References": []}, {"ArticleId": ********, "Title": "B2E Benefit System Architecture", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.09", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "Thepmongkol Keawken", "Affiliation": "Shinawatra University, Pathum Thani"}, {"AuthorId": 2, "Name": "Suchai Thanawastien", "Affiliation": "Shinawatra University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shinawatra University"}], "References": []}, {"ArticleId": 86956901, "Title": "Extraction of Multiple Diseases in Apple Leaf Using Machine Learning", "Abstract": "<p>This paper proposes a novel algorithm of segmentation of diseased part in apple leaf images. In agriculture-based image processing, leaf diseases segmentation is the main processing task for region of interest extraction. It is also extremely important to segment the plant leaf from the background in case on live images. Automated segmentation of plant leaves from the background is a common challenge in the processing of plant images. Although numerous methods have been proposed, still it is tough to segment the diseased part of the leaf from the live leaf images accurately by one particular method. In the proposed work, leaves of apple having different background have been segmented. Firstly, the leaves have been enhanced by using Brightness-Preserving Dynamic Fuzzy Histogram Equalization technique and then the extraction of diseased apple leaf part is done using a novel extraction algorithm. Real-time plant leaf database is used to validate the proposed approach. The results of the proposed novel methodology give better results when compared to existing segmentation algorithms. From the segmented apple leaves, color and texture features are extracted which are further classified as marsonina coronaria or apple scab using different machine learning classifiers. Best accuracy of 96.4% is achieved using K nearest neighbor classifier.</p>", "Keywords": "", "DOI": "10.1142/S021946782140009X", "PubYear": 2022, "Volume": "22", "Issue": "3", "JournalId": 14884, "JournalTitle": "International Journal of Image and Graphics", "ISSN": "0219-4678", "EISSN": "1793-6756", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering, University Institute of Technology, Himachal Pradesh University, Shimla  171005, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chitkara University Institute of Engineering and Technology, Chitkara University, Punjab 140401, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chitkara University School of Engineering and Technology, Chitkara University, Himachal Pradesh 174103, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chitkara University Institute of Engineering and Technology, Chitkara University, Punjab 140401, India"}], "References": [{"Title": "ToLeD: Tomato Leaf Disease Detection using Convolution Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "293", "JournalTitle": "Procedia Computer Science"}, {"Title": "Deep Convolutional Neural Network based Detection System for Real-time Corn Plant Disease Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2003", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 86956916, "Title": "A Comprehensive Security Solution for IOT Network with Integrated Technology of Improved Lightweight Encryption Mechanisms", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.14", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vietnam National University Hanoi"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hanoi University of Science and Technology"}], "References": []}, {"ArticleId": 86956937, "Title": "Network Function Virtualization over Cloud - Disaster Recovery Solution over Hybrid Cloud", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.15", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Orange & Ain Shams University - Faculty of Engineering"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ain <PERSON>hams University Cairo"}], "References": []}, {"ArticleId": 86956975, "Title": "An SPH-based mass transfer model for simulating hydraulic characteristics and mass transfer process of dammed rivers", "Abstract": "<p>Aerated flow characterized by complex mass transfer processes with multiple hydraulic properties is a common enviro-hydraulics phenomenon, which have a variety of profound effects on aquatic ecosystems and the environment. Accurate prediction of the mass transfer process between air and water is crucial to manage the water ecosystem. In this research, an improved two-phase mass transfer smoothed particle hydrodynamics (ITMT-SPH) model is developed with considering the inherent hydrodynamic equations and transport equations to investigate multiple key factors that have ecological implications such as DO (dissolved oxygen) concentration and TDG (total dissolved gas) level. The established model can eliminate the effects of phase separation and non-physical voids for conventional SPH models, which performs better than conventional grid-based methods in terms of prediction accuracy of hydraulic calculation. In addition, to improve the prediction accuracy of mass transfer calculation, experimental and observed field data from a physical model and a practical project are used to calibrate the source parameter of transport equations. Then, a physical model of Songta Hydropower Station (1:80) is established to simulate DO concentration. At the same time, the practical project of Tongjiezi Hydropower Station is used to explore dynamics of TDG level. Simulation results show that ITMT-SPH can simulate ecological indicators of the aerated flow well. The maximum relative error of DO deficit recovery rate between simulation and experiment is 7.8% downstream of dam of Songta physical model. The predicted result of TDG level downstream of Second Dam is 130–148% for Tongjiezi Project, which is close to the monitoring result (138%). This work provides an important theoretical and technical support for the application of ITMT-SPH model to accurately simulate ecological indicators in the ecosystem management.</p>", "Keywords": "Aerated flow; ITMT-SPH; Ecological indicators; DO concentration; TDG level", "DOI": "10.1007/s00366-021-01354-2", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "Hang Wan", "Affiliation": "Guangdong Provincial Key Laboratory of Water Quality Improvement and Ecological Restoration for Watersheds, Institute of Environmental and Ecological Engineering, Guangdong University of Technology, Guangzhou, China; State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 3, "Name": "Yanpeng <PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Water Quality Improvement and Ecological Restoration for Watersheds, Institute of Environmental and Ecological Engineering, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Civil Engineering, Guizhou University, Guiyang, China"}], "References": []}, {"ArticleId": 86956982, "Title": "A Novel Evolution Optimization Algorithm Using a Multidimensional Geometric Method: Pivot Optimiser", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.13", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "King <PERSON><PERSON>'s Institute of Technology Ladkrabang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "King <PERSON><PERSON>'s Institute of Technology Ladkrabang"}], "References": []}, {"ArticleId": 86956983, "Title": "High Availability Solution over Hybrid Cloud Using Failover Clustering Feature", "Abstract": "", "Keywords": "", "DOI": "10.5013/IJSSST.a.21.04.02", "PubYear": 2020, "Volume": "21", "Issue": "", "JournalId": 49376, "JournalTitle": "International Journal of Simulation Systems, Science & Technology", "ISSN": "1473-8031", "EISSN": "1473-804X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Orange & Ain Shams University - Faculty of Engineering"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ain Shams University, CHEP, Cairo"}], "References": []}, {"ArticleId": 86957029, "Title": "A Criterion for the Asymptotic Stability\nof a Periodic Selector-Linear Differential Inclusion", "Abstract": "<p> We consider a periodic selector-linear differential inclusion. It is proved that for this inclusion to be uniformly asymptotically stable, it is necessary and sufficient that there exists a time-periodic Lyapunov function of a quasi-quadratic form. We derive estimates for the Lya<PERSON>nov function that guarantee its positive definiteness and the existence of an infinitesimal upper limit. </p>", "Keywords": "periodic selector-linear differential inclusion; <PERSON><PERSON><PERSON><PERSON> function", "DOI": "10.1134/S0005117921010045", "PubYear": 2021, "Volume": "82", "Issue": "1", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Trapeznikov Institute of Control Sciences, Russian Academy of Sciences, Moscow, Russia"}], "References": []}, {"ArticleId": 86957030, "Title": "A Guaranteed Deterministic Approach to Superhedging:\nNo Arbitrage Properties of the Market", "Abstract": "<p> For a discrete-time super-replication problem, a guaranteed deterministic formulation is considered: the problem is to ensure a complete coverage of contingent claims on an option under all admissible scenarios. These scenarios are specified using a priori defined compact sets, depending on the price prehistory: at each time step the price increments must belong to the corresponding compact sets. The market is considered with trading constraints and without transaction costs. The problem statement is game-theoretic in nature and directly yields the <PERSON>–<PERSON> equations. This paper is focused on several concepts formalizing the no arbitrage property of the market within the deterministic approach as well as on their properties. A new concept of robustness (structural stability) of the no arbitrage properties of the market is introduced. </p>", "Keywords": "guaranteed estimates; deterministic price dynamics; super-replication; option; arbitrage; no arbitrage opportunities; <PERSON><PERSON>–Isaac<PERSON> equations; multivalued mapping; robustness; structural stability of model", "DOI": "10.1134/S0005117921010124", "PubYear": 2021, "Volume": "82", "Issue": "1", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON>. <PERSON><PERSON>", "Affiliation": "Lomonosov Moscow State University, Moscow, Russia"}], "References": []}, {"ArticleId": 86957073, "Title": "Control of a Mobile Robot with a Trailer Based on Nilpotent\nApproximation", "Abstract": "<p> We consider a kinematic model of a mobile robot with a trailer moving on a homogeneous plane. The robot can move back and forth and make a pivot turn. For this model, we pose the following optimal control problem: transfer the “robot–trailer” system from an arbitrarily given initial configuration into an arbitrarily given final configuration so that the amount of maneuvering is minimal. By a maneuver we mean a functional that defines a trade-off between the linear and angular robot motion. Depending on the trailer–robot coupling, this problem corresponds to a two-parameter family of optimal control problems in the 4-dimensional space with a 2-dimensional control. </p><p>We propose a nilpotent approximation method for the approximate solution of the problem. A number of iterative algorithms and programs have been developed that successfully solve the posed problem in the ideal case, namely, with no state constraints. Based on these algorithms, we propose a dedicated reparking algorithm that solves a particular case of the problem where the initial and final robot position coincide and takes into account a state constraint on the trailer’s turning angle occurring in real systems. </p>", "Keywords": "robot with trailer; kinematic model; optimal control; nilpotent approximation; sub-Riemannian problem", "DOI": "10.1134/S0005117921010057", "PubYear": 2021, "Volume": "82", "Issue": "1", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Ailamazyan Program Systems Institute, Russian Academy of Sciences, Pereslavl-Zalessky, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Ailamazyan Program Systems Institute, Russian Academy of Sciences, Pereslavl-Zalessky, Russia"}], "References": []}, {"ArticleId": ********, "Title": "Mathematical Modeling of Kinetics of Iodine-Containing\nRadiotracers in Nuclear Medicine Problems", "Abstract": "<p> We consider methods for modeling and develop a unified approach to constructing mathematical models of the kinetics of radiopharmaceuticals with iodine isotopes in the human body during diagnostic and therapeutic procedures of nuclear medicine. Various techniques for identifying the model parameters based on quantitative data of radionuclide studies of the functional state of organs are proposed. The results of pharmacokinetic modeling in radionuclide diagnostics of the liver, kidneys, and thyroid using iodine-containing radiopharmaceuticals are presented and analyzed. The features and results of modeling and dosimetric planning of thyroid radioiodine therapy are also discussed. </p>", "Keywords": "mathematical modeling; nuclear medicine; kinetics; radiopharmaceutical; chamber model", "DOI": "10.1134/S0005117921010082", "PubYear": 2021, "Volume": "82", "Issue": "1", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Dostoevsky Omsk State University, Omsk, Russia"}], "References": []}, {"ArticleId": 86957075, "Title": "Adaptive Approach to Solving a Two-Point Boundary Value\nProblem\nunder Partial Uncertainty in the Disturbance Field", "Abstract": "<p> We consider the construction of an adaptive algorithm for solving a boundary value problem ensuring that a trajectory issuing from some point hits a finite-size target at a given time under partial uncertainty in the disturbance field. Although the disturbance field has a component that is unknown in explicit form but is still important for hitting a target of given size, we have constructed an iteration procedure for solving the problem in finitely many steps under a number of conditions. The algorithm is based on trial trajectories and uses the measurement of their deviations from the target center as a feedback; this proves sufficient to compensate for the incompleteness of information about the external disturbance field. </p>", "Keywords": "adaptive control; two-point boundary value problems; uncertainty conditions; differential equations", "DOI": "10.1134/S0005117921010069", "PubYear": 2021, "Volume": "82", "Issue": "1", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "St. Petersburg State University, St. Petersburg, Russia"}], "References": []}, {"ArticleId": 86957305, "Title": "A Data Resource for Sulfuric Acid Reactivity of Organic Chemicals", "Abstract": "<p>We describe a dataset of the quantitative reactivity of organic chemicals with concentrated sulfuric acid. As well as being a key industrial chemical, sulfuric acid is of environmental and planetary importance. In the absence of measured reaction kinetics, the reaction rate of a chemical with sulfuric acid can be estimated from the reaction rate of structurally related chemicals. To allow an approximate prediction, we have collected 589 sets of kinetic data on the reaction of organic chemicals with sulfuric acid from 262 literature sources and used a functional group-based approach to build a model of how the functional groups would react in any sulfuric acid concentration from 60–100%, and between −20 °C and 100 °C. The data set provides the original reference data and kinetic measurements, parameters, intermediate computation steps, and a set of first-order rate constants for the functional groups across the range of conditions −20 °C–100 °C and 60–100% sulfuric acid. The dataset will be useful for a range of studies in chemistry and atmospheric sciences where the reaction rate of a chemical with sulfuric acid is needed but has not been measured.</p>", "Keywords": "kinetics; reactivity; sulfuric acid; sulfonation; solvolysis kinetics ; reactivity ; sulfuric acid ; sulfonation ; solvolysis", "DOI": "10.3390/data6030024", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Earth, Atmospheric and Planetary Sciences, Massachusetts Institute of Technology, 77 Massachusetts Avenue, Cambridge, MA 02139, USA↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Earth, Atmospheric and Planetary Sciences, Massachusetts Institute of Technology, 77 Massachusetts Avenue, Cambridge, MA 02139, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Earth, Atmospheric and Planetary Sciences, Massachusetts Institute of Technology, 77 Massachusetts Avenue, Cambridge, MA 02139, USA↑Department of Physics, Massachusetts Institute of Technology, 77 Massachusetts Avenue, Cambridge, MA 02139, USA↑Department of Aeronautics and Astronautics, Massachusetts Institute of Technology, 77 Massachusetts Avenue, Cambridge, MA 02139, USA"}], "References": []}, {"ArticleId": 86957312, "Title": "A Service Sustainable Live Migration Strategy for Multiple Virtual Machines in Cloud Data Centers", "Abstract": "Virtual machine (VM) migration is an indispensable aspect of a virtualized cloud environment. It assists in resource management by dynamically relocating VMs from one physical machine to another. This is an essential aspect especially for big data applications that are prone to variable workloads and often demand relocation of resources in terms of VMs. However, such applications not only experience stochastic workloads but also have stringent requirements on the maximum tolerable latency. To address such issues, VMs are often relocated using live VM migration. VM migration is associated with overheads, hence, in this paper, we propose a modified serial migration strategy to migrate multiple VMs based on the pre-copy live migration technique. We propose to interleave the pre-copy stages in such a way that a balance is achieved between the migration time and downtime overheads. The proposed technique is compared with the state-of-the-art serial, parallel, and improved serial migration strategies. Concerning downtime, the proposed approach performs exceptionally well compared to both serial and improved serial methods. The downtime of the proposed scheme and parallel are comparative for read-intensive applications (low dirtying rates). However, for write-intensive applications (high dirtying rates) the former significantly outperforms the latter. The migration time performance of the proposed scheme is observed to be much better than that of the parallel technique and is slightly higher than those of serial and improved serial techniques.", "Keywords": "Cloud computing ; Virtualization ; Virtual machine ; Migration ; Downtime ; Migration time", "DOI": "10.1016/j.bdr.2021.100213", "PubYear": 2021, "Volume": "25", "Issue": "", "JournalId": 3265, "JournalTitle": "Big Data Research", "ISSN": "2214-5796", "EISSN": "2214-580X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Rourkela, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Rourkela, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Rourkela, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Rourkela, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Federal University of Piauí (UFPI), Teresina – PI, Brazil;Instituto de Telecomunicações, Portugal"}, {"AuthorId": 6, "Name": "Sambit Bakshi", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Rourkela, India"}], "References": [{"Title": "Multi-objective optimization for rebalancing virtual machine placement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "824", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "On demand clock synchronization for live VM migration in distributed cloud data centers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "15", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 86957503, "Title": "Ion sensitivity from current hysteresis in InAs nanowire field-effect transistors functionalized with ionophore-doped fluorosilicone membranes", "Abstract": "Trapping of environmental charges in surface states typically dominates electrical transport in nanostructured field-effect transistors (FETs) applied as sensors. Such surface effects produce exceptional sensitivity, yet time dependencies on experimental timescales simultaneously results in hysteresis of FET conductance and signal instability. Whereas hysteresis is usually suppressed by means of chemical surface treatments, here we study it as a source of information for ion sensing. Ion-sensitive FETs were prepared by coupling InAs nanowires to fluorosilicone membranes doped with Na<sup>+</sup> ionophores. From cyclic transfer characteristics in electrolytes of varying concentration, potentiometric and hysteretic calibration curves were obtained. The observed hysteresis was attributed to changes in membrane capacitance by redox reactions between ionized donor-like traps at the InAs surface and electroactive membrane constituents. Hysteresis was correlated to the ion potential through a model and demonstrated a filtering effect that stabilized the hysteretic response against potential drifts. Furthermore, the model elucidated the ability to modulate ion sensitivity by controlling the initial density of ionized traps via electrostatic polarization by the gate. In this mode of active operation, we demonstrate enhancement above the Nernstian limit with linear calibrations of (−77.5 ± 3.2 to −80.7 ± 3.0 mV/dec) despite the presence of non-equilibrium ion fluxes.", "Keywords": "InAs ; Nanowire ; Field-effect transistor ; Hysteresis ; Ionophore ; Ion sensor", "DOI": "10.1016/j.snb.2021.129704", "PubYear": 2021, "Volume": "336", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Advanced Nanotechnology, University of Toronto, 170 College Street, Toronto, Ontario M5S 3E4, Canada;Department of Materials Science and Engineering, University of Toronto, 184 College Street, Toronto, Ontario M5S 3E4, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Materials Engineering, School of Engineering, The University of Tokyo, 7-3-1 Hongo, Bunkyo-ku, Tokyo 113-8656, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Advanced Nanotechnology, University of Toronto, 170 College Street, Toronto, Ontario M5S 3E4, Canada;Department of Materials Science and Engineering, University of Toronto, 184 College Street, Toronto, Ontario M5S 3E4, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centre for Advanced Nanotechnology, University of Toronto, 170 College Street, Toronto, Ontario M5S 3E4, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Centre for Advanced Nanotechnology, University of Toronto, 170 College Street, Toronto, Ontario M5S 3E4, Canada"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Defence Research and Development Canada Suffield, Medicine Hat, Alberta T1A 8K6, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Centre for Advanced Nanotechnology, University of Toronto, 170 College Street, Toronto, Ontario M5S 3E4, Canada;Department of Materials Science and Engineering, University of Toronto, 184 College Street, Toronto, Ontario M5S 3E4, Canada"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials Engineering, School of Engineering, The University of Tokyo, 7-3-1 Hongo, Bunkyo-ku, Tokyo 113-8656, Japan;Corresponding author"}], "References": []}, {"ArticleId": 86957505, "Title": "A smartphone-integrated colorimetric sensor of total volatile basic nitrogen (TVB-N) based on Au@MnO2 core-shell nanocomposites incorporated into hydrogel and its application in fish spoilage monitoring", "Abstract": "Total volatile basic nitrogen (TVB-N) content is the most widely used index for the assessment of fish freshness. Herein, a smartphone-integrated colorimetric sensor of TVB-N is developed based on a functional hydrogel loaded with [email protected] <sub>2</sub> core-shell nanocomposites and β- d -glucose pentaacetate (β- d -GP). In the sensing hydrogel, [email protected] <sub>2</sub> serves as a colorimetric substance and β- d -GP is a precursor of reducing agent. TVB-N can permeate the sensing hydrogel, causing an alkaline environment in which β- d -GP is hydrolyzed to produce β- d -glucose. A redox reaction between MnO<sub>2</sub> and β- d -glucose can occur and thus the TVB-N can induce the etching of MnO<sub>2</sub> layer of the [email protected] <sub>2</sub>. Owing to the change of localized surface plasma resonance (LSPR) effect, the color of AuNPs changing from purple to red is easily distinguished by the naked eye. To quantify the variation conveniently, the color information is digitized by a smartphone combined with RGB analysis. The hydrogel sensing platform is further used for real-time monitoring of fish spoilage successfully. The introduction of smartphone avoids the usage of expensive instrument and opens a practical way for cost-effective, non-invasive, and portable monitoring of foodstuff freshness.", "Keywords": "Total volatile basic nitrogen ; [email protected] <sub>2</sub> nanocomposites ; Sensing hydrogel ; Fish freshness ; RGB analysis", "DOI": "10.1016/j.snb.2021.129708", "PubYear": 2021, "Volume": "335", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Micro & Nano Biosensing Technology in Food Safety, Hunan Provincial Key Laboratory of Food Science and Biotechnology, College of Food Science and Technology, Hunan Agricultural University, Changsha, 410128, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Micro & Nano Biosensing Technology in Food Safety, Hunan Provincial Key Laboratory of Food Science and Biotechnology, College of Food Science and Technology, Hunan Agricultural University, Changsha, 410128, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory of Micro & Nano Biosensing Technology in Food Safety, Hunan Provincial Key Laboratory of Food Science and Biotechnology, College of Food Science and Technology, Hunan Agricultural University, Changsha, 410128, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Laboratory of Micro & Nano Biosensing Technology in Food Safety, Hunan Provincial Key Laboratory of Food Science and Biotechnology, College of Food Science and Technology, Hunan Agricultural University, Changsha, 410128, China;Corresponding authors"}, {"AuthorId": 5, "Name": "Xingbo Shi", "Affiliation": "Laboratory of Micro & Nano Biosensing Technology in Food Safety, Hunan Provincial Key Laboratory of Food Science and Biotechnology, College of Food Science and Technology, Hunan Agricultural University, Changsha, 410128, China;Corresponding authors"}], "References": [{"Title": "A low-cost device for rapid ‘color to concentration’ quantification of cyanide in real samples using paper-based sensing chip", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "322", "Issue": "", "Page": "128622", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 86957509, "Title": "Harnessing heterogeneous social networks for better recommendations: A grey relational analysis approach", "Abstract": "Most of the extant studies in social recommender system are based on explicit social relationships, while the potential of implicit relationships in the heterogeneous social networks remains largely unexplored. This study proposes a new approach to designing a recommender system by employing grey relational analysis on the heterogeneous social networks. It starts with the establishment of heterogeneous social networks through the user-item bipartite graph, user social network graph and user-attribute bipartite graph; and then uses grey relational analysis to identify implicit social relationships, which are then incorporated into the matrix factorization model. Five experiments were conducted to test the performance of our approach against four state-of-the-art baseline methods. The results show that compared with the baseline methods, our approach can effectively alleviate the sparsity problem, because the heterogeneous social network provides richer information. In addition, the grey relational analysis method has the advantage of low requirements for data size and efficiently relieves the cold start problem. Furthermore, our approach saves processing time, thus increases recommendation efficiency. Overall, the proposed approach can effectively improve the accuracy of rating prediction in social recommendations and provide accurate and efficient recommendation service for users.", "Keywords": "Recommender system ; Heterogeneous social network ; Grey relational analysis ; Recommendation algorithm ; Implicit social relationships ; User profile", "DOI": "10.1016/j.eswa.2021.114771", "PubYear": 2021, "Volume": "174", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, Fuzhou University, Fuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Fuzhou University, Fuzhou, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Durham University Business School, Mill Hill Lane, Durham DH1 3LB, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Computer Science, Fuzhou University, Fuzhou, China"}], "References": [{"Title": "Social recommendation algorithm based on stochastic gradient matrix decomposition in social network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "601", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A social recommender system based on reliable implicit relationships", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "105371", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Combining novelty and popularity on personalised recommendations via user profile learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113149", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dynamic portfolio optimization based on grey relational analysis approach", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "113207", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid recommender system for recommending relevant movies using an expert system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "158", "Issue": "", "Page": "113452", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Simultaneous incremental matrix factorization for streaming recommender systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "113685", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Catering for unique tastes: Targeting grey-sheep users recommender systems through one-class machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114061", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 86957510, "Title": "An evolutionary approach to implement logic circuits on three dimensional FPGAs", "Abstract": "Three Dimensional Field Programmable Gate Arrays (3D FPGAs) recently are presented as the next generation of the FPGA family to continue the integration of more transistors on a single chip seamlessly. The 3D FPGA are fabricated by stacking several layers of semiconductor substrates and the interconnection among layers are realized using Through Silicon Vias (TSVs). Despite their benefits regarding less area and higher speed, 3D FPGAs encounter two major problems; huge size of single TSV and trapping generated heat in inner layers. To handle these problems, we propose a complete Computer Aided Design (CAD) flow to implement an arbitrary logic circuit on 3D FPGA. Prtitioning, Placement, and Routing are primary stages of the proposed CAD flow. The partitioning and placement stages of the flow are based on Simulated Annealing algorithm. Furthermore, the routing stage is a modified version of the Pathfinder algorithm. Unbalanced SA based partitioning tremendously reduces the required TSVs along with distribution of highly active circuit’s modules on the bottom layers and constructing thermal channels facilitate transferring the generated heat in intermediate layers. Simulation results show more than 60%, 65%, and 23% reduction in TSV count, heat transfer performance, and area respectively, along with 4% increase in critical path delay. In addition, comparison between 2D FPGA and 3D FPGA with our proposed architecture (including 2 tier), shows that the circuit speed increases by 28.61%, and minimum channel width decreases by 30.47%. Finally, the results of comparison between 2-tier and 4-tier in 3D FPGA show that circuit speed and minimum channel width increase by 15.95% and 15.92% in 4-tier, respectively.", "Keywords": "3D FPGA ; Simulated annealing ; Partitioning ; Place and route ; 3D architecture ; Heat transfer", "DOI": "10.1016/j.eswa.2021.114780", "PubYear": 2021, "Volume": "174", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Kurdistan, Department of Electrical Engineering, Sanandaj, Kurdistan 90210, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Kurdistan, Department of Electrical Engineering, Sanandaj, Kurdistan 90210, Iran;Corresponding author"}], "References": [{"Title": "NN-SSTA: A deep neural network approach for statistical static timing analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113309", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 86957832, "Title": "Das Smartphone-Dilemma und die Frage der Offenheit", "Abstract": "", "Keywords": "", "DOI": "10.1365/s40702-021-00713-3", "PubYear": 2021, "Volume": "58", "Issue": "2", "JournalId": 15010, "JournalTitle": "HMD Praxis der Wirtschaftsinformatik", "ISSN": "1436-3011", "EISSN": "2198-2775", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hochschule Darmstadt, Darmstadt, Deutschland"}], "References": []}, {"ArticleId": 86957948, "Title": "Imbalance deep multi‐instance learning for predicting isoform–isoform interactions", "Abstract": "<p>Multi-instance learning (MIL) can model complex bags (samples) that are further made of diverse instances (subsamples). In typical MIL, the labels of bags are known while those of individual instances are unknown and to be specified. In this paper we propose an imbalanced deep multi-instance learning approach (IDMIL-III) and apply it to predict genome-wide isoform–isoform interactions (IIIs). This prediction task is crucial for precisely understanding the interactome between proteoforms and to reveal their functional diversity. The current solutions typically formulate the prediction of IIIs as a MIL problem by pairing two genes as a “bag” and any two isoforms spliced from these two genes as “instances.” The key instances (interacting isoform pairs) trigger the label of the positive (interacting) gene bags, which is important for identifying the IIIs. Furthermore, the prediction task was simplified as a balanced classification problem, which in practice is a rather imbalanced one. To address these issues, IDMIL-III fuses RNA-seq, nucleotide sequence, amino acid sequence and exon array data, and further introduces a novel loss function to separately model the loss of positive pairs and of negative pairs, and thus to avoid the expected loss dominated by majority negative pairs. In addition, it includes an attention strategy to identify positive isoform pairs from a positive gene bag. Extensive experimental results prove the effectiveness of IDMIL-III on predicting IIIs. Particularly, IDMIL-III achieves an F1 value as 95.4%, at least 3.8% higher than those of competitive methods at the gene-level; and obtains an F1 as 29.8%, at least 2.4% higher than the state-of-the-art methods at the isoform-level. The code of IDMIL-III is available at http://mlda.swu.edu.cn/codes.php?name=IDMIL-III .</p>", "Keywords": "data fusion;deep learning;imbalance data;isoform–isoform interaction;multi-instance learning", "DOI": "10.1002/int.22402", "PubYear": 2021, "Volume": "36", "Issue": "6", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Jinan, China; College of Computer and Information Science, Southwest University, Chongqing, China; Joint SDU-NTU Centre for Artificial Intelligence Research, Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University, Chongqing, China; Joint SDU-NTU Centre for Artificial Intelligence Research, Shandong University, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Computer and Information Science, Southwest University, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CEMSE, King <PERSON> University of Science and Technology, Thuwal, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Beijing University of Civil Engineering and Architecture, Beijing, China"}], "References": [{"Title": "Isoform function prediction based on bi-random walks on a heterogeneous network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "303", "JournalTitle": "Bioinformatics"}, {"Title": "Differentiating isoform functions with collaborative matrix factorization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "6", "Page": "1864", "JournalTitle": "Bioinformatics"}, {"Title": "A self‐adaptive synthetic over‐sampling technique for imbalanced classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "6", "Page": "923", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 86957987, "Title": "The Use of Fragility Curves in the Life-Cycle Assessment of Deteriorating Bridge Structures", "Abstract": "<p>Within the context of structure deterioration studies, we propose a new numerical method based on the use of fragility curves. In particular, the present work aims to theoretically study the degradation of concrete bridge structures subjected to aggressive environments. A simple probabilistic method based on fragility curves is presented which allows the forecasting of the lifetime of the considered structural system and the best monitoring time. The method was applied to investigate the degradation of a concrete bridge used as a case study. A Monte Carlo numerical procedure was used to simulate the variation over time of the residual resistant section and the ultimate bending moment of the deck of the case study. Within this context, fragility curves are used as reliable indicators of possible monitoring scenarios. In comparison with other methods, the main advantage of the proposed approach is the small amount of computing time required to obtain rapid assessment of reliability and deterioration level of the considered structure.</p>", "Keywords": "life-cycle assessment; reinforce concrete bridges; concrete deterioration; probabilistic modelling; fragility curves life-cycle assessment ; reinforce concrete bridges ; concrete deterioration ; probabilistic modelling ; fragility curves", "DOI": "10.3390/computation9030025", "PubYear": 2021, "Volume": "9", "Issue": "3", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, Politecnico di Milano, 20133 Milan, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Politecnico di Milano, 20133 Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Architecture, Architectural Engineering and Urban Planning, Université Catholique de Louvain, 7500 Tournai, Belgium↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}], "References": []}, {"ArticleId": 86958111, "Title": "Dataset of working mPEG-alkyne with scCO2", "Abstract": "<p>This article contains data related to the research article entitled \"Carbon dioxide sorption and melting behavior of mPEG-alkyne\". The presented data gives information on the thermodynamics properties of the solvent and the polymer. The time saturation of mPEG-alkyne in supercritical carbon dioxide (scCO<sub>2</sub>) was evaluated in a high-pressure variable volume cell in different period of time at different pressure at the same temperature. The effects of pressure and temperature on the density of CO<sub>2</sub> when it is above supercritical conditions are determined with <PERSON> and Bend<PERSON> Equation and compared with the NIST database and values of equation of Bender. The characteristic parameters of CO<sub>2</sub> were determined with the equations proposed by <PERSON><PERSON><PERSON> et al. [1] and the sum of squared error was calculated for each parameter. Furthermore in this work the solubility data of scCO<sub>2</sub>/polymer mixture were correlated with <PERSON> Equation of State (SL EOS) and Heuristic model proposed by <PERSON> et al. [2]. This work describes the methodology for solving the SL EOS between the polymer and scCO<sub>2</sub> and the procedure of determining the solubility parameter with the group contribution method necessary to apply the heuristic model is described.</p><p>© 2021 The Authors.</p>", "Keywords": "MPEG-alkyne;Mathematical models;ScCO2;Sorption", "DOI": "10.1016/j.dib.2021.106907", "PubYear": 2021, "Volume": "35", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Institute of Chemical and Environmental Technology (ITQUIMA), University of Castilla-La Mancha, Avda. Camilo José Cela 12, 13071 Ciudad Real, Spain."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Institute of Chemical and Environmental Technology (ITQUIMA), University of Castilla-La Mancha, Avda. Camilo José Cela 12, 13071 Ciudad Real, Spain."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Institute of Chemical and Environmental Technology (ITQUIMA), University of Castilla-La Mancha, Avda. Camilo José Cela 12, 13071 Ciudad Real, Spain."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Institute of Chemical and Environmental Technology (ITQUIMA), University of Castilla-La Mancha, Avda. Camilo José Cela 12, 13071 Ciudad Real, Spain."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Institute of Chemical and Environmental Technology (ITQUIMA), University of Castilla-La Mancha, Avda. Camilo José Cela 12, 13071 Ciudad Real, Spain."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Institute of Chemical and Environmental Technology (ITQUIMA), University of Castilla-La Mancha, Avda. Camilo José Cela 12, 13071 Ciudad Real, Spain."}], "References": []}, {"ArticleId": 86958461, "Title": "Exploiting Spatio-Temporal Properties of I/Q Signal Data Using 3D Convolution for RF Transmitter Identification", "Abstract": "In this work we implement an automatic emitter identification system using three dimensional convolutional neural networks (3D-CNN). We show that it is possible to leverage the short term spatio-temporal properties of the raw I/Q signal data using 3D-CNN and apply this to the task of transmitter identification. The time series of raw I/Q signal data exhibits a “helical” structure which encodes the intrinsic properties of the emitter as short term variations. These features are exploited by our system to create unique “fingerprints” for the emitters, which are finally used for the task of identification. Our system is end-to-end, works with the raw I/Q signal data and is able to automatically discriminate between different transmitters without using a recurrent structure, thus reducing the complexity of the system. We experimented with four novel RF transmitter datasets that we curated in an uncalibrated and uncontrolled indoor environment. The first dataset consists of four identical transmitters, the second eight identical transmitters, the third four heterogeneous transmitters and the last eight heterogeneous transmitters. We use Ettus Research USRP Software Defined Radios (USRP B210, B200, X310), ADLM Pluto and BladeRF for transmission and collect the raw signal data using a RTL Software Defined Radio (RTL SDR) receiver dongle, from each of the four and eight emitters at a time. We use the data to train and test the 3D convolutional neural network for discerning intrinsic transmitter characteristics for the task of classification. Through our methods and implementation, we show that we can identify the transmitters with ~99 % accuracy.", "Keywords": "RF fingerprinting;machine learning;convolutional neural network;deep neural network;I/Q data;time series;software defined radios", "DOI": "10.1109/JRFID.2021.3051901", "PubYear": 2021, "Volume": "5", "Issue": "2", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Alabama in Huntsville, Huntsville, AL, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, University of Central Florida, Orlando, FL, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, University of Central Florida, Orlando, FL, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86958744, "Title": "A Sufficient Statistic for Influence in Structured Multiagent Environments", "Abstract": "<p>Making decisions in complex environments is a key challenge in artificial intelligence (AI). Situations involving multiple decision makers are particularly complex, leading to computational intractability of principled solution methods. A body of work in AI has tried to mitigate this problem by trying to distill interaction to its essence: how does the policy of one agent influence another agent? If we can find more compact representations of such influence, this can help us deal with the complexity, for instance by searching the space of influences rather than the space of policies. However, so far these notions of influence have been restricted in their applicability to special cases of interaction. In this paper we formalize influence-based abstraction (IBA), which facilitates the elimination of latent state factors without any loss in value, for a very general class of problems described as factored partially observable stochastic games (fPOSGs). On the one hand, this generalizes existing descriptions of influence, and thus can serve as the foundation for improvements in scalability and other insights in decision making in complex multiagent settings. On the other hand, since the presence of other agents can be seen as a generalization of single agent settings, our formulation of IBA also provides a sufficient statistic for decision making under abstraction for a single agent. We also give a detailed discussion of the relations to such previous works, identifying new insights and interpretations of these approaches. In these ways, this paper deepens our understanding of abstraction in a wide range of sequential decision making settings, providing the basis for new approaches and algorithms for a large class of problems.</p>", "Keywords": "markov decision processes;multiagent systems;planning;reinforcement learning", "DOI": "10.1613/jair.1.12136", "PubYear": 2021, "Volume": "70", "Issue": "", "JournalId": 56494, "JournalTitle": "Journal of Artificial Intelligence Research", "ISSN": "", "EISSN": "1076-9757", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Delft University of Technology"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Nissan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CSAIL Massachusetts Institute of Technology, 32 Vassar Street, Cambridge, MA  02139, United States"}], "References": []}, {"ArticleId": 86958906, "Title": "Long Term Orientation Moderating Effect on Acceptance of Jogja Smart Service in Countries with Pragmatic Society", "Abstract": "<p>The government and stakeholders with smart city concept can take advantage of communication technology, a network that has grown rapidly to date for all the needs of urban challenges. The challenges of government services that must be shortened, cut distances, and information disclosure can begin to be resolved with the concept of a smart city with its various products. The technology acceptance model combined with the cultural moderator has been used by several researchers for acceptance testing, one of which is this paper. Research and survey data collection were processed using Smart PLS 3.2 with Sequential Equation Modeling in Yogyakarta, Indonesia. This is unique to discuss because the p-value of Long Term Orientation (LT) is 0.386 which means that it does not significantly moderate behavior. This is interesting to be presented in publications, because Indonesia is a country with a high Long Term orientation value or a pragmatic society. Pragmatic societies tend to be more flexible in adjusting traditions, with the current context, and easy to adapt. Our supporting findings are that respondents who have tried using JSS are still given the opportunity to use conventional methods in government services.</p>", "Keywords": "Long Term Orientation;UTAUT2;Cultural Moderators;Smart City", "DOI": "10.24002/ijis.v3i2.4232", "PubYear": 2021, "Volume": "3", "Issue": "2", "JournalId": 61585, "JournalTitle": "Indonesian Journal of Information Systems", "ISSN": "2623-0119", "EISSN": "2623-2308", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sistem Informasi, Universitas Atma Jaya Yogyakarta"}], "References": []}, {"ArticleId": 86958907, "Title": "Web Vulnerability Through Cross Site Scripting (XSS) Detection with OWASP Security Shepherd", "Abstract": "<p>Web applications are needed as a solution to the use of internet technology that can be accessed globally, capable of displaying information that is rich in content, cost effective, easy to use and can also be accessed by anyone, anytime and anywhere. In the second quarter of 2020, Wearesocial released information related to internet users in the world around 4.54 billion with 59% penetration. People become very dependent on the internet and also technology. This condition was also triggered due to the Covid-19 pandemic.One thing that becomes an issue on website application security is internet attacks on website platforms and we never expected the vulnerability. One type of attack or security threat that often arises and often occurs is Cross Site Scripting (XSS). XSS is one of Top 10 Open Web Application Security Projects (OWASP) lists.There are several alternatives that we can use to prevent cyber-attack. OWASP Security Shepherd can be used as a way to prevent XSS attacks. The OWASP Security Shepherd project allows users to learn or develop their manual penetration testing skills. In this research, there are several case examples or challenges that we can use as a simulation of the role of OWASP Security Shepherd to detect this XSS. The purpose of this paper is to conduct a brief and clear review of technology on OWASP Security Shepherd. This technology was chosen as an appropriate and inexpensive alternative for users to ward off XSS attacks.</p>", "Keywords": "Cross Site Scripting (XSS), web application security, OWASP Security Shepherd", "DOI": "10.24002/ijis.v3i2.4192", "PubYear": 2021, "Volume": "3", "Issue": "2", "JournalId": 61585, "JournalTitle": "Indonesian Journal of Information Systems", "ISSN": "2623-0119", "EISSN": "2623-2308", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Mukti Wibowo", "Affiliation": "Department of Information System, Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, Kingdom of Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON>, Saudi Arabia"}], "References": []}, {"ArticleId": 86958908, "Title": "Identification of Formaldehyde Bananas using Learning Vector Quantization", "Abstract": "<p>Bananas that ripen with chemical process or do not ripen naturally usually, this can be recognized by the presence of blackish patches on the surface of the skin. But visual recognition has its drawbacks, which is that it is difficult to recognize similarities between formalin bananas and natural bananas, resulting in a lack of accurate identification. In this study, a system was built that can determined formalin bananas and natural bananas through digital image identification using supervised classification. The image to be identification previously goes through the process of transforming RGB (Red Green Blue) color to Grayscale, and the process of extracting texture features using statically recognizable features through histograms, in the form of average, standard deviation, skewness, kurtosis, energy, entropy and smoothness. The extraction of texture features is classified with LVQ (Learning Vector Quantization) to determine formalin or natural bananas. The test was conducted with 122 banana imagery sample data, 100 imagery as training data consisting of 50 imagery for natural bananas and 50 imagery for bananas formalin, 22 imagery as test data. The test results showed LVQ method has the best percentage at Learning Rate 0.1, Decreased Learning Rate 0.75 and maximum epoch of 1000 with the smallest epoch of 7, obtained accuracy 90.90%, precision 84.61% and recall 100%.</p>", "Keywords": "Learning Vector Quantization;Neural Network;Banana", "DOI": "10.24002/ijis.v3i2.4110", "PubYear": 2021, "Volume": "3", "Issue": "2", "JournalId": 61585, "JournalTitle": "Indonesian Journal of Information Systems", "ISSN": "2623-0119", "EISSN": "2623-2308", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Mercu Buana Yogyakarta"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Mercu Buana Yogyakarta"}], "References": []}, {"ArticleId": 86959198, "Title": "A Compact Hardware Implementation of CCA-Secure Key Exchange Mechanism CRYSTALS-KYBER on FPGA", "Abstract": "<p>Post-quantum cryptosystems should be prepared before the advent of powerful quantum computers to ensure information secure in our daily life. In 2016 a post-quantum standardization contest was launched by National Institute of Standards and Technology (NIST), and there have been lots of works concentrating on evaluation of these candidate protocols, mainly in pure software or through hardware-software co-design methodology on different platforms. As the contest progresses to third round in July 2020 with only 7 finalists and 8 alternate candidates remained, more dedicated and specific hardware designs should be considered to illustrate the intrinsic property of a certain protocol and achieve better performance. To this end, we present a standalone hardware design of CRYSTALS-KYBER, amodule learning-with-errors (MLWE) based key exchange mechanism (KEM) protocol within the 7 finalists on FPGA platform. Through elaborate scheduling of sampling and number theoretic transform (NTT) related calculations, decent performance is achieved with limited hardware resources. The way that Encode/Decode and the tweaked Fujisaki-Okamoto transform are implemented is demonstrated in detail. Analysis about minimizing memory footprint is also given out. In summary, we realize the adaptive chosen ciphertext attack (CCA) secure Kyber with all selectable module dimension k on the smallest Xilinx Artix-7 device. Our design computes key-generation, encapsulation (encryption) and decapsulation (decryption and reencryption) phase in 3768/5079/6668 cycles when k = 2, 6316/7925/10049 cycles when k = 3, and 9380/11321/13908 cycles when k = 4, consuming 7412/6785 LUTs, 4644/3981 FFs, 2126/1899 slices, 2/2 DSPs and 3/3 BRAMs in server/client with 6.2/6.0 ns critical path delay, outperforming corresponding high level synthesis (HLS) based designs or hardware-software co-designs to a large extent.</p>", "Keywords": "full hardware implementation;CRYSTALS-KYBER;post-quantum cryptography;key exchange mechanism;Module-LWE", "DOI": "10.46586/tches.v2021.i2.328-356", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Microelectronics, Tsinghua University, Beijing, China"}], "References": []}, {"ArticleId": 86959228, "Title": "Time-Memory Analysis of Parallel Collision Search Algorithms", "Abstract": "<p>Parallel versions of collision search algorithms require a significant amount of memory to store a proportion of the points computed by the pseudo-random walks. Implementations available in the literature use a hash table to store these points and allow fast memory access. We provide theoretical evidence that memory is an important factor in determining the runtime of this method. We propose to replace the traditional hash table by a simple structure, inspired by radix trees, which saves space and provides fast look-up and insertion. In the case of many-collision search algorithms, our variant has a constant-factor improved runtime. We give benchmarks that show the linear parallel performance of the attack on elliptic curves discrete logarithms and improved running times for meet-in-the-middle applications.</p>", "Keywords": "radix tree;discrete logarithm;parallelism;collision;elliptic curves;meet-in-the-middle;attack;trade-off", "DOI": "10.46586/tches.v2021.i2.254-274", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire MIS, Université de Picardie Jules Verne, Amiens, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratoire MIS, Université de Picardie Jules Verne, Amiens, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratoire MIS, Université de Picardie Jules Verne, Amiens, France"}], "References": []}, {"ArticleId": 86959229, "Title": "A White-Box Masking Scheme Resisting Computational and Algebraic Attacks", "Abstract": "<p>White-box cryptography attempts to protect cryptographic secrets in pure software implementations. Due to their high utility, white-box cryptosystems (WBC) are deployed by the industry even though the security of these constructions is not well defined. A major breakthrough in generic cryptanalysis of WBC was Differential Computation Analysis (DCA), which requires minimal knowledge of the underlying white-box protection and also thwarts many obfuscation methods. To avert DCA, classic masking countermeasures originally intended to protect against highly related side-channel attacks have been proposed for use in WBC. However, due to the controlled environment of WBCs, new algebraic attacks against classic masking schemes have quickly been found. These algebraic DCA attacks break all classic masking countermeasures efficiently, as they are independent of the masking order.In this work, we propose a novel generic masking scheme that can resist both DCA and algebraic DCA attacks. The proposed scheme extends the seminal work by <PERSON><PERSON> et al. which is probing secure and thus resists DCA, to also resist algebraic attacks. To prove the security of our scheme, we demonstrate the connection between two main security notions in white-box cryptography: probing security and prediction security. Resistance of our masking scheme to DCA is proven for an arbitrary order of protection, using the well-known strong non-interference notion by <PERSON><PERSON> et al. Our masking scheme also resists algebraic attacks, which we show concretely for first and second-order algebraic protection. Moreover, we present an extensive performance analysis and quantify the overhead of our scheme, for a proof-of-concept protection of an AES implementation.</p>", "Keywords": "Algebraic Attacks;White-box Cryptography;Boolean Masking;Non-linear Masking;Probing Security;Prediction Security;Differential Computation Analysis", "DOI": "10.46586/tches.v2021.i2.61-105", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Lübeck, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Lübeck, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Lübeck, Germany"}], "References": []}, {"ArticleId": 86959230, "Title": "Machine Learning of Physical Unclonable Functions using Helper Data: Revealing a Pitfall in the Fuzzy Commitment Scheme", "Abstract": "<p>Physical Unclonable Functions (PUFs) are used in various key-generation schemes and protocols. Such schemes are deemed to be secure even for PUFs with challenge-response behavior, as long as no responses and no reliability information about the PUF are exposed. This work, however, reveals a pitfall in these constructions: When using state-of-the-art helper data algorithms to correct noisy PUF responses, an attacker can exploit the publicly accessible helper data and challenges. We show that with this public information and the knowledge of the underlying error correcting code, an attacker can break the security of the system: The redundancy in the error correcting code reveals machine learnable features and labels. Learning these features and labels results in a predictive model for the dependencies between different challenge-response pairs (CRPs) without direct access to the actual PUF response. We provide results based on simulated data of a k-SUM PUF model and an Arbiter PUF model. We also demonstrate the attack for a k-SUM PUF model generated from real data and discuss the impact on more recent PUF constructions such as the Multiplexer PUF and the Interpose PUF. The analysis reveals that especially the frequently used repetition code is vulnerable: For a SUM-PUF in combination with a repetition code, e.g., already the observation of 800 challenges and helper data bits suffices to reduce the entropy of the key down to one bit. The analysis also shows that even other linear block codes like the BCH, the Reed-Muller, or the Single Parity Check code are affected by the problem. The code-dependent insights we gain from the analysis allow us to suggest mitigation strategies for the identified attack. While the shown vulnerability advances Machine Learning (ML) towards realistic attacks on key-storage systems with PUFs, our analysis also facilitates a better understanding and evaluation of existing approaches and protocols with PUFs. Therefore, it brings the community one step closer to a more complete leakage assessment of PUFs.</p>", "Keywords": "Key Distribution;Physical Unclonable Function;PUF;Machine Learning;Supervised Learning;Fuzzy Commitment Scheme;Fuzzy Extractor;Error Correcting Code;Neural Network;Key Storage", "DOI": "10.46586/tches.v2021.i2.1-36", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 81477, "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems", "ISSN": "", "EISSN": "2569-2925", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer Institute for Applied and Integrated Security (AISEC), Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technical University of Munich, Germany, Department of Electrical and Computer Engineering, Chair of Security in Information Technology"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Technical University of Munich, Germany, Department of Electrical and Computer Engineering, Chair of Security in Information Technology"}], "References": []}, {"ArticleId": 86959393, "Title": "Basic low-voltage high gain amplifier block having multiple-inputs designed using differential FVF", "Abstract": "<p>Flipped voltage follower (FVF) is a basic unit used as a fundamental component in almost every field including analog/digital devices, fuzzy circuits, neural networks, circuits for signal processing and biological components. This paper presents a differential flipped voltage follower (DFVF) based multiple-input transconductance amplifier. The proposed amplifier operates at low voltage and increases the transconductance gain of the amplifier significantly by the use of multiple-input architecture. It utilizes the low voltage, low power and differential characteristics of DFVF configuration. The simulations have been carried out in Mentor Graphics EldoSpice with TSMC based 0.18 µm CMOS technology. The proposed circuit offers multiple-input characteristics and produces high output current and transconductance.</p>", "Keywords": "Multi-input amplifier; Transconductance amplifiers; Unity gain source follower; Flipped voltage follower; Differential amplifier", "DOI": "10.1007/s41870-021-00621-1", "PubYear": 2021, "Volume": "13", "Issue": "3", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ECE Division, NSIT, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ECE Division, NSIT, Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ECE Division, NSIT, Delhi, India"}], "References": []}, {"ArticleId": 86959435, "Title": "Modeling the angular effect of MODIS LST in urban areas: A case study of Toulouse, France", "Abstract": "Satellite observation of land surface temperature (LST) is an important tool for monitoring urban thermal environments but is prone to significant angular effects over non-isothermal pixels characterized by the substantial three-dimensional (3D) structure of urban areas. However, accurately characterizing the urban thermal anisotropy at the satellite pixel scale is still challenging. In this paper, comparing the simultaneous airborne observations and the temporal-aggregated signal from MODIS LST, we further investigate the seasonal and diurnal patterns of thermal anisotropy in urban areas with various simulation scenarios. The anisotropy was defined as the difference between off-nadir and nadir temperatures. First, a realistic morphological representation of urban surfaces as well as urban component temperatures measured by infrared thermometers (IRTs) were input into the discrete anisotropic radiative transfer (DART) model to simulate MODIS-scale anisotropy for Toulouse, France in summer. Extending this ‘IRT-DART’ method to other seasons was restricted because the IRTs' radiative source areas are affected by shadows when the sun elevation is low, meaning that measured sunlit temperatures are not reliably available. An energy balance model, TUF3D, and a sensor view model, SUM, for simplified urban geometry were coupled to replace ‘IRT-DART’ in winter. The cross-comparison shows that ‘TUF-SUM’ can simulate MODIS-scale anisotropy if urban structure and materials are relatively homogeneous, which provides a basis for assessing anisotropy when IRT sunlit temperatures are unavailable. The MODIS anisotropy is subject to a strong diurnal yet a discernable seasonal variation. For example, for a mid-latitude city, the anisotropy over all MODIS view angles reaches up to 6.6 K for Terra-Day (around 11:00 local time) and 4.9 K for Aqua-Day (around 13:00) in summer, and 6.1 K for Terra-Day and 4.0 K for Aqua-Day in winter. Nocturnal anisotropy is weak with values less than 0.2 K. The modeling methods can be used to quantify anisotropy of satellite LST in a broad range of urban environments and facilitate the use of multi-angular MODIS LST for a better assessment of urban thermal environments in the future.", "Keywords": "Thermal remote sensing ; Urban thermal anisotropy ; Angular effect ; MODIS land surface temperature (LST) ; Model simulation", "DOI": "10.1016/j.rse.2021.112361", "PubYear": 2021, "Volume": "257", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Land Science and Technology, China University of Geosciences, Beijing 100083, China;State Key Laboratory of Earth Surface Processes and Resource Ecology, Faculty of Geographical Science, Beijing Normal University, Beijing 100875, China;Beijing Key Laboratory of Environmental Remote Sensing and Digital Cities, Faculty of Geographical Science, Beijing Normal University, Beijing 100875, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Earth Surface Processes and Resource Ecology, Faculty of Geographical Science, Beijing Normal University, Beijing 100875, China;Beijing Key Laboratory of Environmental Remote Sensing and Digital Cities, Faculty of Geographical Science, Beijing Normal University, Beijing 100875, China;Corresponding author at: No. 19 Xinjiekouwai Street, Beijing 100875, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Atmospheric and Earth Science, University of Alabama in Huntsville, AL 35899, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Geography and Environment, University of Western Ontario, London, ON N6A 5C2, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>Etch<PERSON>", "Affiliation": "CESBIO, Toulouse University, CNES, CNRS, IRD, UT3, Toulouse, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Environmental Sciences, University of Guelph, Guelph, ON N1G 2W1, Canada"}], "References": [{"Title": "An advanced geometric model to simulate thermal anisotropy time-series for simplified urban neighborhoods (GUTA-T)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111547", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 86959446, "Title": "CCmed: cross-condition mediation analysis for identifying replicable trans-associations mediated by cis-gene expression", "Abstract": "Motivation <p>Trans-acting expression quantitative trait loci (eQTLs) collectively explain a substantial proportion of expression variation, yet are challenging to detect and replicate since their effects are often individually weak. A large proportion of genetic effects on distal genes are mediated through cis-gene expression. Cis-association (between SNP and cis-gene) and gene-gene correlation conditional on SNP genotype could establish trans-association (between SNP and trans-gene). Both cis-association and gene-gene conditional correlation have effects shared across relevant tissues and conditions, and trans-associations mediated by cis-gene expression also have effects shared across relevant conditions.</p> Results <p>We proposed a Cross-Condition Mediation analysis method (CCmed) for detecting cis-mediated trans-associations with replicable effects in relevant conditions/studies. CCmed integrates cis-association and gene-gene conditional correlation statistics from multiple tissues/studies. Motivated by the bimodal effect-sharing patterns of eQTLs, we proposed two variations of CCmed, CCmedmost and CCmedspec for detecting cross-tissue and tissue-specific trans-associations, respectively. We analyzed data of 13 brain tissues from the Genotype-Tissue Expression (GTEx) project, and identified trios with cis-mediated trans-associations across brain tissues, many of which showed evidence of trans-association in two replication studies. We also identified trans-genes associated with schizophrenia loci in at least two brain tissues.</p> Availability and implementation <p>CCmed software is available at http://github.com/kjgleason/CCmed.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab139", "PubYear": 2021, "Volume": "37", "Issue": "17", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and Informatics, Colorado School of Public Health, University of Colorado Anschutz Medical Campus, Aurora, CO 80045, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Public Health Sciences, University of Chicago, Chicago, IL 60637, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biostatistics, University of Pittsburgh, Pittsburgh, PA 15261, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Unit of Functional Genomics in Psychiatry, Center for Psychiatric Genetics, NorthShore University HealthSystem, Evanston, IL 60201, USA;Department of Psychiatry and Behavioral Neuroscience, Chicago, IL 60637, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Human Genetics, University of Chicago, Chicago, IL 60637, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Public Health Sciences, University of Chicago, Chicago, IL 60637, USA;Department of Human Genetics, University of Chicago, Chicago, IL 60637, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Public Health Sciences, University of Chicago, Chicago, IL 60637, USA"}], "References": []}, {"ArticleId": 86959526, "Title": "Evaluation of symbol error probability using a new tight Gaussian Q approximation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCC.2021.113241", "PubYear": 2021, "Volume": "12", "Issue": "1", "JournalId": 19270, "JournalTitle": "International Journal of Systems, Control and Communications", "ISSN": "1755-9340", "EISSN": "1755-9359", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 86959546, "Title": "Improving Questionnaire Reliability using Construct Reliability for Researches in Educational Technology", "Abstract": "<p class=\"0abstract\">This paper is exploring on maneuver to improve research instrument reliability in scientific research related to Educational Technology by analyzing the traditional Cronbach’s alpha reliability using SPSS software and the newer statistical tool, AMOS using Construct Reliability (CR) approach. Two sets of data were used as sample to perform the comparison. The first set of data is from a research involving Technology Enhanced Learning Environment. The second data are sampled from research in digital competency. Finding from this paper concluded that, conventional approach of using Cronbach’s alpha have lower reliability than the newer approach of using CR. Using Cronbach’s alpha show tendency toward measuring consistency instead of reliability. CR offer better definition of reliability and give a robust measurement of reliability in research. This paper had shed light into offering alternative approach to the commonly and widely uses of research reliability especially when it involves questionnaire as instrument.<strong></strong></p>", "Keywords": "Educational Technology, Online Learning, Questionnaire Reliability, Con-struct Reliability", "DOI": "10.3991/ijim.v15i04.20199", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia, Johor Bahru, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universiti Tun <PERSON>, Parit Raja, Malaysia"}, {"AuthorId": 3, "Name": "Sultan <PERSON><PERSON>", "Affiliation": "University of Hail, Hail, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Pendidikan Sultan <PERSON>, Tanjung <PERSON>, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia, Johor Bahru, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Universiti Teknologi Malaysia, Johor Bahru, Malaysia"}], "References": []}, {"ArticleId": 86959727, "Title": "Group decision support model based on sequential additive complementary pairwise comparisons", "Abstract": "<p>In group decision support systems, it is important on how to process and manage individual decision information. In the paper, a sequential model is proposed to manage individual judgements with additively reciprocal property over paired alternatives. The process of realizing additive complementary pairwise comparisons (ACPCs) is captured. A real-time feedback mechanism is constructed to address the irrational behavior of individuals. An optimization model is established and solved by using the particle swarm optimization (PSO) algorithm, such that the consistency of individual judgements can be improved fast yet effectively. For the aggregation of individual decision information in group decision making (GDM), the weighted averaging operator is used. It is found that when all individual judgements are acceptably additively consistent, the collective matrix is with acceptable additive consistency. Under the control of individual consistency degrees, the approach of reaching consensus in GDM is further proposed. By comparing with some existing models, the observations reveal that the sequential model of originating additive complementary pairwise comparisons possesses the ability to rationally manage individual decision information.</p>", "Keywords": "Group decision making (GDM); Additive complementary pairwise comparisons (ACPCs); Sequential model; Particle swarm optimization (PSO); Acceptable additive consistency; Consensus", "DOI": "10.1007/s10489-021-02248-y", "PubYear": 2021, "Volume": "51", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Business School, Guangxi University, Nanning, China;School of Mathematics and Information Science, Guangxi University, Nanning, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;Business School, Guangxi University, Nanning, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Guangxi University of Finance and Economics, Nanning, China"}], "References": [{"Title": "Multi-stage optimization model for hesitant qualitative decision making with hesitant fuzzy linguistic preference relations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "1", "Page": "222", "JournalTitle": "Applied Intelligence"}, {"Title": "Decision making with a sequential modeling of pairwise comparison process", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105642", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Particle swarm optimization for trust relationship based social network group decision making under a probabilistic linguistic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "105999", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A decision making model based on the leading principal submatrices of a reciprocal preference relation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106448", "JournalTitle": "Applied Soft Computing"}, {"Title": "A maximum self-esteem degree based feedback mechanism for group consensus reaching with the distributed linguistic trust propagation in social network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "80", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 86959758, "Title": "A secure data analytics scheme for multimedia communication in a decentralized smart grid", "Abstract": "<p>With the exponential increase in energy demands (commercial as well as residential), the traditional grid infrastructure significantly shifted to intelligent ICT-based Smart Grid (SG) infrastructure. In the SG environment, only efficient energy management may not be sufficient as the SG dynamics have significant impacts on multimedia communications such as video surveillance of the technical/non-technical losses of energy and many more. The inevitable energy losses can be identified by process and analyze the massive amount of heterogeneous data, i.e., Big Data (BD) generated through smart devices such as sensors, Smart Meters (SMs), and others. The key challenges in analyzing multimedia BD are computational complexity, operational integration complexity, data security, and privacy. To overcome the aforementioned issues, this paper proposes a blockchain-based data analytics scheme called ChoIce , which offers secure data collection, analysis, and decision support for the SG systems. It works in two phases; (i) secure data collection over Ethereum and (ii) BD analytics and decision-making using deep learning (DL). The robust and secure data analytics, efficient network management, and high-performance computing for BD are crucial towards the optimization of SG operation. The performance of ChoIce is evaluated considering parameters such as the data storage cost, multimedia communication latency, and prediction accuracy. Thus, the results of ChoIce shows that it outperforms in contrast to other state-of-the-art approaches.</p>", "Keywords": "Multimedia system; Big data; Smart grid; Secure data analytics; Blockchain; Deep learning; Data security & privacy", "DOI": "10.1007/s11042-021-10512-z", "PubYear": 2022, "Volume": "81", "Issue": "24", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Institute of Technology, Nirma University, Ahmedabad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Institute of Technology, Nirma University, Ahmedabad, India"}], "References": [{"Title": "Design and analysis of an optimal ECC algorithm with effective access control mechanism for big data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "15-16", "Page": "9757", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "RETRACTED ARTICLE: The energy management strategy for parallel hybrid electric vehicles based on MNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "7-8", "Page": "5321", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A hybrid framework for multimedia data processing in IoT-healthcare using blockchain technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "15-16", "Page": "9711", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A unified framework for data integrity protection in people-centric smart cities", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "7-8", "Page": "4989", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "GUARDIAN: Blockchain-Based Secure Demand Response Management in Smart Grid System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "613", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Secure data analytics for smart grid systems in a sustainable smart city: Challenges, solutions, and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "", "Page": "100427", "JournalTitle": "Sustainable Computing: Informatics and Systems"}]}, {"ArticleId": 86959914, "Title": "An effective dynamic service composition reconfiguration approach when service exceptions occur in real-life cloud manufacturing", "Abstract": "Cloud Manufacturing Service Composition (CMSC), as one of the key issues of Cloud Manufacturing (CMfg), has already attracted much attention. Existing researches on CMSC mainly focus on the optimization efficiency in ideal conditions, while scarcely focus on how to efficiently reconfigure CMSC when service exceptions occur. Uncertain service exceptions often occur during CMSC's execution in real-life CMfg. Thus, it is an urgent issue to perform an adjustment for CMSC to continue to complete the processing task. Besides, some practical constraints are non-negligible in real-world CMfg. Thus, it is necessary to consider them when reconfiguring CMSC. To bridge these gaps, this paper proposes a dynamic service composition reconfiguration model when service exceptions occur under practical constraints (DSCRWECPC). This model redefines optimization objectives, including machining quality, service quality, and cost. Besides, DSCRWECPC considers service exceptions, the cloud manufacturing service occupancy time constraint, the strict time constraint of original CMSC, and dynamic service quality change as its practical constraints. To solve this model, this paper proposes a service composition reconfiguration algorithm (SCRIHHO) based on the strengthened Harris Hawks Optimizer (HHO). Finally, to certify SCRIHHO's performance, this paper conducts numerical experiments and the case application to perform comparisons between SCRIHHO and other algorithms (Particle Swarm Optimization (PSO) and Grey Wolf Optimizer (GWO)). Results showed SCRIHHO in this paper is superior to PSO, GWO when tackling the practical DSCRWECPC in CMfg.", "Keywords": "Cloud manufacturing service composition (CMSC) ; Service exceptions ; Practical constraints ; Dynamic service composition reconfiguration model when service exceptions occur under practical constraints (DSCRWECPC) ; Service composition reconfiguration algorithm based on improved Harris hawks optimization(SCRIHHO) ; Practical application", "DOI": "10.1016/j.rcim.2021.102143", "PubYear": 2021, "Volume": "71", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Wang", "Affiliation": "STATE KEY LABORATORY OF MECHANICAL TRANSMISSION, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "STATE KEY LABORATORY OF MECHANICAL TRANSMISSION, Chongqing University, Chongqing 400044, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "STATE KEY LABORATORY OF MECHANICAL TRANSMISSION, Chongqing University, Chongqing 400044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "STATE KEY LABORATORY OF MECHANICAL TRANSMISSION, Chongqing University, Chongqing 400044, China"}], "References": [{"Title": "Service composition model and method in cloud manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101840", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Service composition model and method in cloud manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101840", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Service agent networks in cloud manufacturing: Modeling and evaluation based on set-pair analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "", "Page": "101970", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A classification-based approach for integrated service matching and composition in cloud manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "66", "Issue": "", "Page": "101989", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "An effective adaptive adjustment method for service composition exception handling in cloud manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3", "Page": "735", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 86960006, "Title": "Calendar of Events", "Abstract": "", "Keywords": "", "DOI": "10.1142/S1469026821830017", "PubYear": 2021, "Volume": "20", "Issue": "1", "JournalId": 17616, "JournalTitle": "International Journal of Computational Intelligence and Applications", "ISSN": "1469-0268", "EISSN": "1757-5885", "Authors": [], "References": []}, {"ArticleId": 86960039, "Title": "Precipitation reconstruction from climate-sensitive lithologies using Bayesian machine learning", "Abstract": "Although global circulation models (GCMs) have been used for the reconstruction of precipitation for selected geological time slices, there is a lack of a coherent set of precipitation models for the Mesozoic-Cenozoic period (the last 250 million years). There has been dramatic climate change during this time period capturing a supercontinent hothouse climate, and continental breakup and dispersal associated with successive greenhouse and ice-house climate periods. We present an approach that links climate-sensitive sedimentary deposits such as coal, evaporites and glacial deposits to a global plate model, reconstructed paleo-elevation maps and high-resolution GCMs via Bayesian machine learning. We model the joint distribution of climate-sensitive sediments and annual precipitation through geological time, and use the dependency between sediments and precipitation to improve the model's predictive accuracy. Our approach provides a set of 13 data-driven global paleo-precipitation maps between 14 and 249 Ma, capturing major changes in long-term annual rainfall patterns as a function of plate tectonics, paleo-elevation and climate change at a low computational cost.", "Keywords": "Paleo-climate ; Gaussian process ; Bayesian methods ; Forecasting ; Precipitation", "DOI": "10.1016/j.envsoft.2021.105002", "PubYear": 2021, "Volume": "139", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, University of Sydney, NSW, 2006, Sydney, Australia;Data Analytics for Resources and Environments, Australian Research Council - Industrial Transformation Training Centre, Australia;Corresponding author. School of Mathematics and Statistics, University of Sydney, NSW, 2006, Sydney, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Data Analytics for Resources and Environments, Australian Research Council - Industrial Transformation Training Centre, Australia;School of Mathematics and Statistics, University of Sydney, NSW, 2006, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Sydney Informatics Hub, University of Sydney, NSW, 2006, Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "EarthByte Group, School of Geosciences, University of Sydney, NSW, 2006, Sydney, Australia"}], "References": [{"Title": "Bayesreef: A Bayesian inference framework for modelling reef growth in response to environmental change and biological dynamics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "125", "Issue": "", "Page": "104610", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 86960139, "Title": "Influence of Weather Features in Determining Sudden Braking", "Abstract": "<p>Understanding conditions and situations causing abnormal driving behaviors like sudden braking or sudden acceleration is important for preventing traffic accidents. Previous studies have used probe vehicle data to detect risky situations where sudden braking frequently occurred. However, they have mainly focused on location and vehicle-related factors. In this paper, we build models which discriminate sudden braking using a machine learning method. The models use weather-related information as well as probe data. To investigate how weather-related factors help to determine sudden braking, we conducted extensive experiments using probe data obtained from dashboard cameras and two types of weather-related information obtained from meteorological observatories (MO) and AMeDAS. Experimental results illustrate that using weather-related information improves performance in determining sudden braking and that the temporally and spatially denser characteristics of weather-related factors from AMeDAS help to compensate for insufficiencies in the model with MO data.</p>", "Keywords": "Probe data; Sudden braking; Machine learning; Weather information; AMeDAS", "DOI": "10.1007/s13177-021-00253-6", "PubYear": 2021, "Volume": "19", "Issue": "2", "JournalId": 6726, "JournalTitle": "International Journal of Intelligent Transportation Systems Research", "ISSN": "1348-8503", "EISSN": "1868-8659", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Advanced Information Technology, Graduate School of Information Science and Electrical Engineering, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Advanced Information Technology, Graduate School of Information Science and Electrical Engineering, Kyushu University, Fukuoka, Japan"}, {"AuthorId": 3, "Name": "Tsunenori Mine", "Affiliation": "Department of Advanced Information Technology, Faculty of Information Science and Electrical Engineering, Kyushu University, Fukuoka, Japan"}], "References": []}, {"ArticleId": 86960403, "Title": "Call for Papers, Issue 5/2022", "Abstract": "", "Keywords": "", "DOI": "10.1007/s12599-021-00687-y", "PubYear": 2021, "Volume": "63", "Issue": "2", "JournalId": 5738, "JournalTitle": "Business & Information Systems Engineering", "ISSN": "2363-7005", "EISSN": "1867-0202", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Information Systems and Marketing, KIT Karlsruhe Institute of Technology, Karlsruhe, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Einstein Center Digital Future, TU Berlin, Berlin, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Fox School of Business, Temple University, Philadelphia, USA"}], "References": []}, {"ArticleId": 86960477, "Title": "A nondominated selection procedure with partially consistent non-reciprocal probabilistic linguistic preference relations and its application in social donation channel selection under the COVID-19 outbreaks", "Abstract": "A non-reciprocal fuzzy preference relation (NrFPR) can express partial relations of alternatives, including indifference relations, preference relations and incomparability relations, but cannot depict linguistic preference intensities. A probabilistic linguistic preference relation (PLPR) can represent preference intensities in forms of probabilities and linguistic terms, but the incomparability relations of alternatives were not defined in a PLPR. Given that the NrFPR and PLPR can overcome each other’s drawbacks, this study proposes the non-reciprocal probabilistic linguistic preference relation (NrPLPR). Six conditions are given to express the partial relations of alternatives. Since the P -cut of probabilistic linguistic term sets (PLTSs) is effective in the operations of PLTSs without information loss, we construct the P -cut matrix of an NrPLPR by an adaptive P -determination method. Afterwards, nine rules are provided to define the partially consistent NrPLPR. To repair the inconsistent NrPLPR, a two-stage consistency repairing process, containing the linguistic information and probability repairing stages, is introduced. In addition, a non-reciprocal probabilistic linguistic nondominated selection procedure is proposed to rank alternatives. A case study on selecting social donation channels under the COVID-19 outbreaks is given to demonstrate the applicability of the proposed method. A comparative analysis is done to show the effectiveness of the proposed method. Introduction As a method to express the preference information of an expert, the binary relation [22] uses “0″ to represent “ not prefer ” and “1” to represent “ prefer ”. However, experts might be difficult to give exact preference relations because of the complexity of problems. In this regard, the fuzzy relation [26] was defined, and experts could give a membership degree between 0 and 1 to express the preference degree of one alternative over another. Based on the fuzzy relation, Parreiras et al. [18] proposed the non-reciprocal fuzzy preference relation which satisfies several non-reciprocal conditions. By the non-reciprocal conditions, the indifference relations, preference relations and incomparability relations of objects can be expressed in the preference information given by experts, and thus it is handy to apply non-reciprocal fuzzy preference relations to real issues. A nondominated selection procedure [18] was introduced to rank alternatives with non-reciprocal fuzzy preference relations. To represent the preference intensities of objects, not only numbers but also linguistic terms can be used. Linguistic preference relations [5] and hesitant fuzzy linguistic preference relations [31] were introduced to represent linguistic information. However, neither linguistic variables nor hesitant fuzzy linguistic term sets can express the preference intensities of objects in numbers and linguistic terms simultaneously. In this regard, probabilistic linguistic preference relations (PLPRs) [30] were proposed in which the preference intensities were expressed by probabilistic linguistic term sets (PLTSs) [17], characterized by multiple linguistic terms associated with probabilities. Since the PLPR can deal with both quantitative information (probabilities) and qualitative information (linguistic terms), it has achieved good applications in solving practical problems [3], [4], [13], [14], [25], [28]. Because inconsistent PLPRs might cause errors in results, the consistency of PLPRs attracted growing attention. Although the non-reciprocal fuzzy preference relation can express partial relations of alternatives, including indifference relations, preference relations and incomparability relations, it cannot express the preference intensities of alternatives in linguistic terms. The PLPR can represent the preference intensities of alternatives with both quantitative and qualitative information, but the incomparability relation was not defined in the PLPR. The ordinal consistent PLPR was proposed based on the scores of PLTSs, which were defined as the mean values of the subscripts of linguistic terms multiplying their associated probabilities [29]. For an ordinal consistent PLPR, only preference relations were considered. In addition, the normalization method used to define the ordinal consistent PLPR might cause information loss [7]. It is found that the non-reciprocal fuzzy preference relation and PLPR can overcome each other’s drawbacks. Because the P -cut of PLTSs [7] is effective in the operations of PLTSs without information loss, it can be applied to overcome the drawback of the normalization method for PLPRs. Motivated by these concerns, this study introduces the non-reciprocal probabilistic linguistic preference relation (NrPLPR). A partially consistent NrPLPR, which contains partial relations including indifference relations, preference relations, and incomparability relations of objects, is defined based on the P -cut matrix of an NrPLPR. A two-stage consistency repairing process is proposed to make an NrPLPR partially consistent. A non-reciprocal probabilistic linguistic nondominated selection procedure is given to rank alternatives. The contributions of this study are summarized as follows: (1) The partially consistent NrPLPR is defined. Six conditions are proposed to express the partial relations of objects, and then the NrPLPR is defined based on these non-reciprocal conditions. The P -cut matrix of an NrPLPR is established by an adaptive P -determination method. Nine rules considering the partial relations of objects are given to define the partially consistent NrPLPR. (2) A two-stage consistency repairing process and a non-reciprocal probabilistic linguistic nondominated selection procedure are proposed. The linguistic information repairing stage and probability repairing stage are introduced to repair the consistency of an NrPLPR. Based on the partially consistent NrPLPR, the non-reciprocal probabilistic linguistic nondominated selection procedure containing nondominated degrees of alternatives and alternative scores is presented to rank alternatives. (3) The non-reciprocal probabilistic linguistic nondominated selection procedure is applied to solve the social donation channel selection problem under the COVID-19 outbreaks. The feasibility of the non-reciprocal probabilistic linguistic nondominated selection procedure is validated by a comparative analysis. This study is organized as follows: In Section 2, we review the non-reciprocal fuzzy preference relations, PLTSs and PLPRs. Section 3 puts forward the partially consistent NrPLPR. The non-reciprocal probabilistic linguistic nondominated selection procedure is introduced in Section 4. A case study about the selection of social donation channels is introduced in Section 5. Concluding remarks are given in Section 6. Figures The graphs of partially consistent rules. The money received by the Wuhan Branch of the Red Cross Society of China from January 24th to April 28th, 2020. Section snippets Related work For the convenience of future discussions, this section reviews the related work on non-reciprocal fuzzy preference relations, PLTSs and PLPRs. Partially consistent NrPLPRs In a real decision-making scenario, experts might consume a lot of time and energy to refine numbers to depict the complex information of decision environment. In this regard, the natural language might be more flexible for experts to use although it seems more complicated than numbers. For the alternatives that experts know well, they might provide commonly-used linguistic terms with appropriate probability information to describe the evaluation information; however, for the alternatives that A non-reciprocal probabilistic linguistic non-dominated selection procedure After the NrPLPR is partially consistent, a multistage non-reciprocal probabilistic linguistic non-dominated selection procedure is introduced to rank alternatives. Let a partially consistent NrPLPR be N ^ = ( L ij ^ ) n × n where L ij ^ = { s ij ( k ) ^ ( s ij ( k ) ^ ) } . In the P -cut matrix of N ^ , known from the fifth rule of non-reciprocal linguistic conditions, if σ ^ 1 ( L P ij ^ ) > σ ^ 1 ( L P ji ^ ) , x i is preferred to x j . Known from the sixth rule of non-reciprocal linguistic conditions, if σ ^ δ ( L P ij ^ ) = σ ^ δ ( L P ji ^ ) and σ ^ δ + 1 ( L P ij ^ ) > σ ^ δ Case study: The channel selection of social donations under the COVID-19 outbreaks In this section, a social donation channel selection problem under the COVID-19 outbreaks is solved by the non-reciprocal probabilistic linguistic non-dominated selection procedure. A comparison is done with the interval values of P to indicate the effectiveness of the proposed method. Conclusions This paper introduced the NrPLPR based on the non-reciprocal linguistic conditions and the P -cut matrices of the NrPLPR. The nine rules of partial consistency and corresponding graphs were given to define the partially consistent NrPLPR. A two-stage consistency repairing process was developed to repair the consistency of NrPLPRs and a non-reciprocal probabilistic linguistic non-dominated selection procedure was given to rank alternatives. To sum up, the contributions of this study are as follows: CRediT authorship contribution statement Lisheng Jiang: Conceptualization, Data curation, Formal analysis, Writing - original draft. Huchang Liao: Conceptualization, Funding acquisition, Supervision, Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements The work was supported by the National Natural Science Foundation of China (71771156, 71971145) and the 2020 Sichuan Planning Project of Social Science (SC20TJ009). References (31) W.J. Chang et al. Triangular bounded consistency of fuzzy preference relations Inf. Sci. (2019) H.C. Liao et al. A linear programming method for multiple criteria decision making with probabilistic linguistic information Inf. Sci. (2017) Y.X. Zhang et al. An ordinal consistency-based group decision making process with probabilistic linguistic preference relation Inf. Sci. (2018) Y.X. Zhang et al. A consensus process for group decision making with probabilistic linguistic preference relations Inf. Sci. (2017) H.C. Liao et al. Novel operations of PLTSs based on the disparity degrees of linguistic terms and their use in designing the probabilistic linguistic ELECTRE III method Appl. Soft Comput. (2019) L.A. Zadeh The concept of a linguistic variable and its application to approximate reasoning-I Inf. Sci. (1975) L.A. Zadeh Similarity relations and fuzzy orderings Inf. Sci. (1971) J.C. Fodor et al. Valued preference structures Eur. J. Oper. Res. (1994) Q. Pang et al. Probabilistic linguistic term sets in multi-attribute group decision making Inf. Sci. (2016) R. Parreiras et al. A dynamic consensus scheme based on a nonreciprocal fuzzy preference relation modeling Inf. Sci. (2012) K. Kułakowski Inconsistency in the ordinal pairwise comparisons method with and without ties Eur. J. Oper. Res. (2018) H.G. Peng et al. Multi-criteria outranking method based on probability distribution with probabilistic linguistic information Comput. Ind. Eng. (2020) L.S. Jiang et al. Mixed fuzzy least absolute regression analysis with quantitative and probabilistic linguistic information Fuzzy Sets Syst. (2020) Z.Y. Ren et al. Generalized Z-numbers with hesitant fuzzy linguistic information and their application to medicine selection for the patients with mild symptoms of the COVID-19 Comput. Ind. Eng. (2020) E. Herrera-Viedma et al. Some issues on consistency of fuzzy preference relations Eur. J. Oper. Res. (2004) View more references Cited by (0) Recommended articles (6) Research article Robust guaranteed cost control for uncertain discrete-time systems with state and input quantizations Information Sciences, Volume 564, 2021, pp. 288-305 Show abstract The robust guaranteed cost control problem for uncertain discrete-time systems with state and input quantizations has been studied in this paper. The polytope type uncertainties are considered in the plants. Different from previous related works, a novel guaranteed cost control strategy has been put forward in this paper. The novelty and challenge lie in that the quantized state and quantized input are included in the guaranteed cost function. Through introducing some auxiliary scalars and combining with the S -procedure, new criteria are developed for the robust stability and guaranteed cost performance for discrete-time systems with state and input quantizations. Based on a new two-step design strategy, the controller and dynamic quantizers can be easily obtained by means of linear matrix inequalities. In the end, two examples are given to demonstrate the effectiveness and applicability of the proposed method. Research article Adaptive denoising algorithm using peak statistics-based thresholding and novel adaptive complementary ensemble empirical mode decomposition Information Sciences, Volume 563, 2021, pp. 269-289 Show abstract This paper proposes an adaptive denoising methodology for noisy signals that employs a novel adaptive complementary ensemble empirical mode decomposition (NACEEMD) and a peak statistics (PS)-based thresholding technique. The key idea in this paper is the peak statistics (PS)-based thresholding technique,which breaks the traditional strategy with respect to selecting more accurate and more adaptive thresholds. The NACEEMD algorithm is proposed to decompose the noisy signal into a series of intrinsic mode functions (IMFs). At the same time, NACEEMD is also used to verify the applicability of the PS-based thresholding technique in different decomposition algorithms. The PS-based threshold is used to remove the noise inherent in noise-dominant IMFs, and the denoised signal is reconstructed by combining the denoised noise-dominant IMFs and the signal-dominant IMFs. This paper uses a various of simulated signals in various noisy environments for experiments, the experimental results indicate that the proposed algorithm outperforms traditional threshold denoising methodologies in terms of signal-to-noise ratio, root mean square error, and percent root distortion. Moreover, through real ECG signal and multi-sensor data fusion experiments, the application of the proposed algorithm in the field of engineering is explored and expanded. Research article Prescribed performance synchronization of complex dynamical networks with event-based communication protocols Information Sciences, Volume 564, 2021, pp. 254-272 Show abstract The paper addresses the prescribed performance synchronization (PPS) issue for complex dynamical networks (CDNs) using an event-triggered communication mechanism and PPS scheme with event-based protocols is designed for the CDNs. The designed synchronization control strategy can reduce the overshoot in transient processes, and stabilize synchronous errors at the origin. Furthermore, by introducing event-based protocols, continuous communication of the networks is avoided, so that the frequency of information communication is reduced and network resources are saved. Moreover, Zeno behavior in the networks can be excluded. Combining the Lyapunov stability method, a sufficient condition for asymptotic synchronization is proposed. Finally, the validity of the results is demonstrated by the CDNs with one link pendulum and Chua’s chaotic circuit (CCC) systems. Research article Image robust adaptive steganography adapted to lossy channels in open social networks Information Sciences, Volume 564, 2021, pp. 306-326 Show abstract Currently, the demand for covert communication in open social networks brings new opportunities and challenges to existing image steganography technology in terms of robustness and security. To this end, an image robust adaptive steganography is proposed with robustness against multiple image processing attacks and detection resistance. First, a robust embedding domain with theoretical foundation and optimal invisibility is constructed based on the compression resistance principle. Then, utilizing the robust image abstraction and saliency measurement, the embedding channel is selected to avoid modifications in smooth regions and enhance visual quality. On this basis, the proposed method is given combining with error-correcting and STC codes to realize message embedding with minimum costs and improve extraction accuracy. Lastly, after parameters discussion and selection, the performance experiments are conducted compared with previous representative steganography algorithms, concerning robustness and detection resistance, and the fault tolerance is deduced, thereby providing the recommended coding parameters to improve message extraction integrity. The experimental results show that the proposed method can realize message extraction with high accuracy after JPEG compression, Gaussian noising, and scaling attacks, while holding comparable detection resistance to adaptive steganography against statistical features, which indicates its application prospect for covert communication in open lossy channels. Research article Fixed-time time-varying formation tracking for nonlinear multi-agent systems under event-triggered mechanism Information Sciences, Volume 564, 2021, pp. 45-70 Show abstract This study focuses on the fixed-time event-triggered time-varying formation tracking issue for a class of nonlinear multi-agent systems with multi-dimensional dynamics, uncertain disturbances and non-zero control input of leader. Firstly, a distributed fixed-time event-triggered control scheme is proposed such that the time-varying formation tracking can be achieved with intermittent controller updates and intermittent communication. To reduce the chattering phenomenon, a novel control protocol with saturation function is designed. It should be noted that continuous triggering condition monitoring is needed in the triggering mechanism. To address above issue, a novel triggering mechanism is further proposed. Moreover, the fixed-time event-triggered time-varying formation containment with multiple leaders is considered. It is verified that the Zeno behavior can be excluded. Finally, two examples are presented to demonstrate the feasibility of the main theoretical findings. Research article Viewpoint adaptation learning with cross-view distance metric for robust vehicle re-identification Information Sciences, Volume 564, 2021, pp. 71-84 Show abstract Many vehicle re-identification (Re-ID) problems require the robust recognition of vehicle instances across multiple viewpoints. Existing approaches for dealing with the vehicle re-ID problem are insufficiently robust because they cannot distinguish among vehicles of the same type nor recognize high-level representations in deep networks for identical vehicles with various views. To address these issues, this paper proposes a viewpoint adaptation network (VANet) with a cross-view distance metric for robust vehicle Re-ID. This method consists of two modules. The first module is the VANet with cross-view label smoothing regularization (CVLSR), which abstracts different levels of a vehicle’s visual patterns and subsequently integrates multi-level features. In particular, CVLSR based on color domains assigns a virtual label to the generated data to smooth image-image translation noise. Accordingly, this module supplies the viewing angle information of the training data and provides strong robust capability for vehicles across different viewpoints. The second module is the cross-view distance metric, which designs a cascaded cross-view matching approach to combine the original features with the generated ones, and thus, obtain additional supplementary viewpoint information for the multi-view matching of vehicles. Results of extensive experiments on two large scale vehicle Re-ID datasets, namely, VeRi-776 and VehiclelD demonstrate that the performance of the proposed method is robust and superior to other state-of-the-art Re-ID methods across multiple viewpoints. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.02.044", "PubYear": 2021, "Volume": "564", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>ng Jiang", "Affiliation": "Business School, Sichuan University, Chengdu 610064, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, Sichuan University, Chengdu 610064, China;Corresponding author"}], "References": [{"Title": "Probabilistic linguistic information fusion: A survey on aggregation operators in terms of principles, definitions, classifications, applications, and challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "3", "Page": "529", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multi-criteria outranking method based on probability distribution with probabilistic linguistic information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "106318", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Generalized Z-numbers with hesitant fuzzy linguistic information and its application to medicine selection for the patients with mild symptoms of the COVID-19", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "106517", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 86960478, "Title": "A cluster-based oversampling algorithm combining SMOTE and k-means for imbalanced medical data", "Abstract": "The algorithm of C4.5 decision tree has the advantages of high classification accuracy , fast calculation speed and comprehensible classification rules, so it is widely used for medical data analysis. However, for imbalanced medical data, the classification accuracy of decision trees-based models is not ideal. Therefore, this paper proposes a cluster-based oversampling algorithm (KNSMOTE) combining Synthetic minority oversampling technique (SMOTE) and k -means algorithm. The sample classes clustered by k -means and the original sample classes are calculated to select the ‘‘safe samples” whose sample classes have not been changed. The ‘‘safe samples” are linearly interpolated to synthesize the new samples. The improved SMOTE sets the oversampling ratio according to the imbalance ratio of the original samples, which is used to synthesize the samples whose number is the same as that of the original samples. Compared with other oversampling algorithms on 8 UCI datasets, our algorithm has achieved significant advantages. Our algorithm was applied to the medical datasets, and the average values of the Sensitivity and Specificity indexes of the Random forest (RF) algorithm were 99.84% and 99.56%, respectively. Introduction The application of information technology in the medical field promoted the digitization of medical information. Digitization of medical equipment and instruments has greatly enriched electronic medical records, which caused a large amount of medical data [1]. This medical data is very valuable for the diagnosis, treatment and medical research [2]. However, the current operations on medical data are limited to the operations such as insertion, modification and deletion, but lack of data on ensemble and analysis [3]. In recent years, data mining algorithms [4], [5] are used in medical data analysis. Data mining refers to the extraction of potentially useful information and knowledge hidden from the database, which is unknown in advance. As data mining algorithm, C4.5 decision tree [6] is widely used in medical data analysis [7], [8]. It has high accuracy and fast calculation speed, especially its classification rules are simple and easy to understand. For C4.5 decision tree, usually assumed that each class has the same number of samples [9], also each class has the same cost of misclassification [10], [11]. However, medical data is often extremely imbalance. Where, minority samples are far less than majority samples, and the misclassification cost resulted from the case samples is greater. In addition, studies showed [12] that the C4.5 decision tree is unstable, which small fluctuations in the dataset can cause significant changes in the classification results of decision trees. Therefore, how to improve the accuracy of the decision tree in imbalanced medical data is an urgent problem. At present, methods for solving imbalanced data classification problem mainly focus on the algorithm level [13], [14] and the data level [15], [16]. At the algorithm level, mainly combined with the characteristics of imbalanced data, to improve the accuracy of minority samples. A typical approach uses ensemble methods for ensemble learning of decision trees. Firstly, multiple training sets are produced by multiple sampling, and multiple models are trained by training sets [14]. Then, test samples are predicted by corresponding models, and the probability that the test sample belongs to each class is obtained. All misclassification costs of the test samples are calculated, and class labels are determined based on the minimum cost. Although this method retains the original data distribution, its usual range is relatively limited, and difficult to be improved [17]. At the data level, data imbalance is mainly reduced or eliminated by changing the sample distribution of the datasets. Random oversampling is a classic data level algorithm to reduce the imbalance of the data by randomly copying minority samples, but blind copying may cause overfitting [18]. Therefore, some scholars [19] proposed the Synthetic minority oversampling technique (SMOTE). The algorithm uses linear interpolation to synthesize the new minority sample between two minority samples, there by effectively alleviating the problem caused by overfitting problems caused. However, SMOTE is easy to synthesize noise samples, boundary samples, and overlapping samples [20]. In recent years, some scholars [17], [21], [22] proposed algorithms combining the cluster-based algorithm and the SMOTE. There are two strategies: first [16], a small number of samples are divided into multiple clusters, and then the SMOTE is used to perform linear interpolation within the divided clusters. Second [22], a cluster-based algorithm is used to identify the class of all samples. Here the class of samples is different. Then the SMOTE is used for linear interpolation to achieve a balanced distribution of the two classes of samples. However, both of the above strategies are blind. For example, it may cause problems such as loss of important samples and new boundary samples. More specifically, we make the following contributions: Firstly, to solve the problems of SMOTE, we propose a k -means cluster-based strategy. The k -means is used to cluster the original samples, and the spatial distance of samples is calculated according to the euclidean distance to obtain more is a tight sample cluster. Different from SMOTE, the greater similarity is given by k -means clustering to reclassify the sample in a class. Then, to solve the problems of traditional cluster-based oversampling, we propose a k -means cluster-based filtering strategy. Define a matrix of original sample classes, perform class difference calculations on the clustered samples, and screen out ‘‘safe samples” that have no change in sample class. Different from traditional cluster-based oversampling, our clustering filtering strategy can effectively delete boundary samples, noise samples and overlapping samples, and avoid the loss of important samples and the generation of new boundary samples. Finally, the ‘‘safe samples” are linearly interpolated by improved SMOTE to synthesize the new two classes of samples. Different from the SMOTE, the classes of the samples may have changed after cluster-based filtering. Therefore, two oversampling ratios are set according to the imbalance ratio of original samples to synthesize and restore original samples. In addition, in view of the instability of C4.5 decision tree, we propose three ensemble methods for ensemble learning of C4.5 decision tree. In terms of experiments, the KNSMOTE algorithm proposed is verified. By comparing the KNSMTOE algorithm with other classic oversampling algorithms on 8 UCI datasets, the superiority of the KNSMTOE algorithm is verified. Then three ensemble C4.5 decision tree algorithms are verified to be more suitable for imbalanced data. In the application experiment, 4 UCI medical datasets and private missed abortion dataset are selected. As in the verification experiment, the application experiment is also compared with other traditional oversampling algorithms. Then, the optimal ensemble method is used to learn the C4.5 decision tree, and the practical application significance is obtained. The rest of this paper is organized as follows. Section 2 reviews the related works. Section 3 describes the KNSMOTE algorithm in detail. Section 4 shows three ensemble methods and evaluation indexes. Section 5 shows the experimental results and Section 6 conclusion Figures The principle of the SMOTE. The principle of the cluster-based SMOTE. The principle of k-means cluster-based filtering algorithm. Sample distribution of the KNSMOTE algorithm after oversampled. The Sensitivity index of RF with the improvement of the imbalance ratio and feature dimensions. The wpbc data distribution after oversampling algorithm oversampled. Show all figures Section snippets Related works In medical data, the amount of information in minority samples cannot compete with the amount of information in majority samples. The information of minority samples is overwhelmed by the majority of samples, resulting in a large number of misclassification [9], [10]. The costs of misclassification a patient as a healthy individual in disease diagnosis is much higher than that of misclassification in a healthy individual as a patient [23]. Therefore, it is very important to resolve the The cluster-based filtering oversampling algorithm Traditional cluster-based oversampling algorithms may cause problems such as the loss of samples classes and new boundary samples. Therefore, this paper proposes a cluster-based oversampling algorithm (KNSMOTE). Firstly, the algorithm clusters original datasets by using k -means algorithm. Then, screen ‘‘safe samples” by using the class discriminant function to filter the clustered samples. Finally, performs linear interpolation on the ‘‘safe samples” after cluster-based filtering by using the Ensemble method The ensemble method [34] is a learning algorithm that combines weak classifiers. Due to its advantages such as high accuracy, fast operation speed, and good robustness, it is widely used in medical data analysis tasks. According to the sampling method of the sample set and the training method of the weak classifier, it can fall into three strategies: AdaBoost [35], Bagging [36], and RF [37]. They describe in turn as follows: AdaBoost, this method first trains a weak learner from the initial Experimental data In order to verify the effectiveness of the KNSMOTE algorithm, six UCI datasets, 4 UCI medical datasets, and private missed abortion datasets were selected for experiments. Eight of them were two-class datasets and five were multi-class datasets. For multi-class dataset, the combination of some lesser classes is defined as the minority class, The class with the largest number of samples is defined as the majority class, and the multi-class datasets is redefined as a two-class datasets. All Conclusion For the imbalance problem of medical data, this paper proposes a cluster-based filtering oversampling algorithm (KNSMOTE). Different from traditional cluster-based oversampling, k -means cluster-based filtering is firstly used to cluster the original datasets, delete the boundary samples and noise samples that are easily misclassified, and keep the ‘‘safe samples” for synthesis; then the improved SMOTE is used to linearly interpolate the filtered ‘‘safe samples”; finally, the balanced data set CRediT authorship contribution statement Zhaozhao Xu: Conceptualization, Data curation, Formal analysis, Methodology, Software, Writing - original draft. Derong Shen: Data curation, Supervision, Writing - original draft, Investigation, Validation. Tiezheng Nie: Funding acquisition, Writing - review & editing, Visualization. Yue Kou: Funding acquisition, Writing - review & editing. Nan Yin: Data curation, Resources, Project administration. Xi Han: Data curation, Resources, Project administration. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This work is supported by the National Key R&D Program of China ( 2018YFB1003404 ) and the National Natural Science Foundation of China ( 62072084 , 62072086 ). References (37) B. Pu et al. Fetal cardiac cycle detection in multi-resource echocardiograms using hybrid classification framework Fut. Gener. Comput. Syst. (2021) S.-J. Lee et al. A novel bagging C4.5 algorithm based on wrapper feature selection for supporting wise clinical decision making J. Biomed. Inform. (2018) L.O. Moraes et al. A decision-tree approach for the differential diagnosis of chronic lymphoid leukemias and peripheral B-cell lymphomas Comput. Methods Programs Biomed. (2019) G. Douzas et al. Self-organizing map oversampling (SOMO) for imbalanced data set learning Expert Syst. Appl. (2017) A.K. Jain Data clustering: 50 years beyond K-means Pattern Recogn. Lett. (2010) D. Devarriya et al. Unbalanced breast cancer data classification using novel fitness functions in genetic programming Expert Syst. Appl. (2020) J. Sun et al. Imbalanced enterprise credit evaluation with DTE-SBD: Decision tree ensemble based on SMOTE and bagging with differentiated sampling rates Inf. Sci. (2018) H. Zhao et al. A cost sensitive decision tree algorithm based on weighted class distribution with batch deleting attribute mechanism Inf. Sci. (2017) V. García et al. On the effectiveness of preprocessing methods when dealing with different levels of class imbalance Knowl.-Based Syst. (2012) J.A. Sáez et al. SMOTE–IPF: Addressing the noisy and borderline examples problem in imbalanced classification by a re-sampling method with filtering Inf. Sci. (2015) I. Kavakiotis et al. Machine learning and data mining methods in diabetes research Comput. Struct. Biotechnol. J. (2017) G. Douzas et al. Improving imbalanced learning through a heuristic oversampling method based on k-means and SMOTE Inf. Sci. (2018) J. Chen et al. A disease diagnosis and treatment recommendation system based on big data mining and cloud computing Inf. Sci. (2018) G. Haixiang et al. Learning from class-imbalanced data: Review of methods and applications Expert Syst. Appl. (2017) B. Krawczyk et al. Ensemble learning for data stream analysis: A survey Inform. Fusion (2017) S. Itani et al. Specifics of medical data mining for diagnosis aid: A survey Expert Syst. Appl. (2019) C. Zhu et al. Entropy-based matrix learning machine for imbalanced data sets Pattern Recogn. Lett. (2017) N.V. Chawla et al. SMOTE: synthetic minority oversampling technique J. Artif. Intell. Res. (2002) View more references Cited by (0) Recommended articles (6) Research article Multiple imputation using nearest neighbor methods Information Sciences, Volume 570, 2021, pp. 500-516 Show abstract Missing values are a major problem in medical research. As the complete case analysis discards useful information, estimation and inference may suffer strongly. Multiple imputation has been shown to be a useful strategy to handle missing data problems and account for the uncertainty of imputation. In the presence of high-dimensional data ( p ≫ n ), the missing values raise even more serious problems as the existing software packages tend to fail. We present multiple imputation methods based on nearest neighbors. The distances are computed using the information of correlation among the target and candidate predictors. Thus only the relevant predictors contribute for computing distances. The method successfully imputes missing values also in high-dimensional settings. Using a variety of simulated data with MCAR and MAR missing patterns, the proposed algorithm is compared to existing methods. Various measures are used to compare the performance of methods, including MSE for imputation, MSE of estimated regression coefficients, their standard errors, confidence intervals, and their coverage probabilities. The simulation results, for both cases n < p and n > p , show that the sequential imputation using weighted nearest neighbors can be successfully applied to a wide range of data settings and outperforms or is close to the best when compared to existing methods. Research article A novel prediction model for the inbound passenger flow of urban rail transit Information Sciences, Volume 566, 2021, pp. 347-363 Show abstract High-precision short-term inbound passenger flow prediction is of great significance to the daily crowd management and line rescheduling in urban rail systems. Although current models have been applied to prediction, most methods need optimization to meet refined passenger flow management demand. In order to better predict the passenger flow, a novel Wave-LSTM model, based on long short-term memory network (LSTM) and wavelet, is introduced in this paper. In an empirical study with practical passenger flow data of Dongzhimen Station in the Beijing Subway system, the hybrid model exhibited more effective performance in terms of prediction accuracy than the existing algorithms, e.g., autoregressive integrated moving average (ARIMA), nonlinear regression (NAR), and traditional LSTM model. The study illustrates that our newly adopted model is a promising approach for predicting high-precision short-term inbound passenger flow. Research article Identifying influential nodes in complex networks: Effective distance gravity model Information Sciences, 2021 Show abstract The identification of important nodes in complex networks is an area of exciting growth due to its applications across various disciplines like disease control, data mining and network system control. Many measures have been proposed to date, but they are either based on the locality of nodes or the global nature of the network. These measures typically use the traditional Euclidean Distance, which only focuses on local static geographic distance between nodes but ignores the dynamic interaction between nodes in real-world networks. Both the static and dynamic information should be considered for the purpose of identifying influential nodes. In order to address this problem, we have proposed an original and novel gravity model with effective distance for identifying influential nodes based on information fusion and multi-level processing. Our method is able to comprehensively consider the global and local information of complex networks, and also utilizes the effective distance to incorporate static and dynamic information. Moreover, the proposed method can help us mine for hidden topological structure of real-world networks for more accurate results. The susceptible infected model, Kendall correlation coefficient and eight existing identification methods are utilized to carry out simulations on twelve different real networks. Research article Rough approximation-based approach for designing a personalized tour route under a fuzzy environment Information Sciences, 2021 Show abstract Trip planning significantly improves tourists’ experiences and enhances the competitive advantage of tourist attractions. We focus on the tourist trip design problem (TTDP) under a fuzzy environment, which is an extension of TTDP that considers the spatiotemporal route structure and variable sightseeing value at points of interest. A rough approximation-based model is proposed to deal with fuzzy variables, and a hybrid genetic algorithm is designed to identify the optimal route. We conduct a numerical experiment to assess the performance of the presented approach. The results of the Wilcoxon rank sum tests indicate that our approach performs significantly better than currently available methods. The evolution strategies based on improved particle swarm optimization also demonstrate better efficiency than existing approaches. Research article Empirical risk minimization for dominance-based rough set approaches Information Sciences, Volume 567, 2021, pp. 395-417 Show abstract In this paper, we consider two parametric dominance-based rough set approaches (DRSA) proposed in the literature: variable precision DRSA (VP-DRSA) and variable consistency DRSA (VC-DRSA). They were introduced to cope with classification data encountered in practice for which the original definition of lower approximations is too restrictive. Both these extensions allow an augmentation of lower approximations, which is controlled parametrically in different ways. We give statistical interpretations for VP-DRSA and VC-DRSA from the perspective of empirical risk minimization typical for machine learning. Given families of classifiers and loss functions, we consider classification problems which relate directly VP-DRSA and VC-DRSA to ordinal classification. Then, we characterize the parametrically augmented lower approximations of both approaches as optimal solutions of associated empirical risk minimization problems. As a consequence, a connection between parametric DRSA and statistical learning is established. Moreover, new characterizations of the augmented lower approximations allow us to exhibit differences and similarities between VP-DRSA and VC-DRSA. Research article ClEnDAE: A classifier based on ensembles with built-in dimensionality reduction through denoising autoencoders Information Sciences, Volume 565, 2021, pp. 146-176 Show abstract High dimensionality is an issue that affects most classification algorithms. This factor implies that the predictive performance of many traditional classifiers decreases considerably as the number of features increases. Therefore, there are numerous proposals that try to mitigate the effects of this issue. This study proposes ClEnDAE, a new classifier based on ensembles whose components incorporate denoising autoencoders (DAEs) to reduce the dimensionality of the input space. On the one hand, the use of ensembles improves the predictive performance by using several components that work jointly. On the other hand, the use of DAEs allows a new higher-level, smaller-sized feature space to be generated, reducing high dimensionality effects. Finally, an experimentation is conducted with the goal of evaluating the behavior of ClEnDAE. The first part of the test compares the performance of ClEnDAE to a model based on basic DAE and to the original untreated data. The second part analyzes the results of ClEnDAE and other traditional methods of dimensionality reduction in order to determine the improvement achieved with the proposed algorithm. In both parts of the experimentation, conclusions show that ClEnDAE offers better predictive performance than the other analyzed models. The main advantage of the ClEnDAE method is the combination of the potential of the ensemble-based methodology, where several components work in parallel, and DAEs, which generate new low-dimensional features that provide more relevant information. Therefore, the classification performance is better than with other classic proposals. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.02.056", "PubYear": 2021, "Volume": "572", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang 110169, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang 110169, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang 110169, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Northeastern University, Shenyang 110169, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Beijing System Design Institute of the Electro-mechanic Engineering, Beijing 100854, China"}, {"AuthorId": 6, "Name": "Xi Han", "Affiliation": "Beijing System Design Institute of the Electro-mechanic Engineering, Beijing 100854, China"}], "References": [{"Title": "Unbalanced breast cancer data classification using novel fitness functions in genetic programming", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112866", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fetal cardiac cycle detection in multi-resource echocardiograms using hybrid classification framework", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "825", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 86960519, "Title": "Outlier detection based on weighted neighbourhood information network for mixed-valued datasets", "Abstract": "Outlier detection is of great importance in industry as unexpected errors or faults, abnormal behaviours or phenomena, etc. can occur due to a variety of human, system, and environmental reasons. To identify and analyse these rare items, events or observations can find either anomalies or novelties and, as a result, can help avoid potential unexpected consequences or improve industrial system performance. The operating data collected from industrial systems in the Industry 4.0 era are characterized as multi-attribute (e.g., both numerical and categorical) compared to previous studies. Therefore, a new outlier detection method for mixed-valued datasets based on the weighted network model is proposed in this paper. Concretely, a weighted neighbourhood information network (WNIN) is constructed by considering the neighbourhood relations and similarities among objects to represent a dataset with mixed-valued attributes (DMA). A tailored Markov random walk method is employed to detect outlier on the predefined network model. After reaching the equilibrium, the inlier score is defined according to the out-degree of nodes in the WNIN to represent the inlier degree of objects. Experiments on two real datasets and a case study illustrate the effectiveness and adaptability of the proposed method. Introduction The realisation of knowledge discovery in databases (KDD) is an important issue in the development of knowledge-based and data-based systems [42]. Knowledge discovery includes four aspects, i.e., dependency detection, class identification, class description, and outlier detection [16], of which outlier detection aims at identifying and analysing rare observations that are far from the majority of the data [13]. Outliers can be anomalies, novelties, noise, deviations, and exceptions. An outlier usually represents a new perspective or a specific mechanism, and it attracts higher interest than the normal instances. The research on various techniques to implement outlier detection has been widely studied and applied by academia and industry. Therefore, it has been widely used in different domains, e.g., intrusion and fraud detection, medical diagnosis, weather prediction, and social networks [29], [18], [27], [40]; and can also be applied in binary classification, prediction, and event classification[28], [23], [31]. The approaches for outlier detection can be classified into five categories: distribution-based, depth-based, clustering, distance-based, and density-based approaches. However, these traditional methods usually require additional information or knowledge and can only be applied under specific conditions [42]. In recent years, outlier detection approaches based on graphs or networks have attracted increasing interest due to their robust expressiveness and extraordinary ability to capture the long-range correlations within datasets [1], [30]. Generally, each data object is modelled as a node in a graph or network, and the relationship between objects is defined as the edge that connects the nodes. The nodes and edges in the graph or network are analysed according to the substructure or correlation to define the outlier score of each object [36]. Zhu et al. [44] defined the natural neighbourhood based on the k -nearest neighbours model and proposed the weighted natural neighbourhood graph model to implement outlier detection with no neighbourhood parameters. Wang et al. [35] constructed weighted directed neighbourhood graphs by using a sequence of automatically determined neighbourhood sizes. A Markov random walk process is performed on the predefined graphs utilizing the different aspects of local information to calculate the outlier score of each object. Most of these approaches can be used to process only numerical data. However, for many real-world problems, the datasets tend to include both numerical and categorical attributes simultaneously; therefore, these datasets are datasets with mixed-valued attributes (DMAs) [17]. The main technological difficulties for outlier detection incorporating a DMA are the measurement of the neighbourhood relations among the objects and the outlier degree of each object. The existing methodologies for measuring neighbourhood relations can be classified into two categories. One category is the k -nearest neighbours in which the nearest k points are determined by calculating and sorting the similarity between the new sample points and all the training samples [9]. In this category, a larger k is used to avoid identifying large numbers of outliers whereas a smaller k is applied to ensure that the points are sufficiently close to the specific object [7]. The other category is the neighbourhood rough set in which the key is to define the neighbours and identify the number (affected by the neighbour radius) of neighbours for each object [32]. In this category, a larger r is used to obtain a better identification result whereas a smaller r is applied to acquire the nearest neighbours. In these methods, the use of a suitable method to measure the similarity is critical to identify whether an object is a neighbourhood of another object. The methods to measure the similarity between the objects in a DMA can be classified into two categories. The main idea of the first category is to convert one type of attribute to another type via transformation (for categorical attributes) or discretisation (for numerical attributes). However, this transformation can result in information loss, changes in the meaning of the data, and an increase in the noise level [26]. The key of the second category is to define a generalised criterion in which the numerical and categorical similarities are defined using the distance function and distribution centroid, respectively [15]. Several useful and valuable trials seeking to measure the outlier degree of an object have been reported in the literature. Moonesignhe and Tan [24] utilised the stationary distribution vector to express the outlier score based on the random walk. Bouguessa [3] proposed the use of the square of the distance between the objects and their k -nearest neighbours in the numerical space and the frequency of the objects in the categorical space to express the outlier score. Motivated and inspired by the above observations, we introduce a new outlier detection approach, which does not require any prior knowledge, to handle mixed-valued attribute data. A tailored Markov random walk process on a predefined weighted neighbourhood information network (WNIN) is employed to determine the inlier degree for an object. First, the WNIN is modelled based on the neighbourhood information system. Second, the weights of the edges and the state transition probability matrix are obtained according to the similarities and neighbourhood relations among the objects. Then, the inlier score of each object is obtained after the Markov random walk process reaches an equilibrium state. Finally, based on the UCI datasets and a real-world application, the proposed method is compared with other existing outlier detection methods, and the results show that the proposed method is more effective and accurate. The remainder of this paper is organized as follows. The related work is reviewed in Section 2. The neighbourhood information system and Markov chain are introduced in Section 3. The proposed approach is described in Section 4. The UCI dataset experiments are analysed in Section 5. A real case study is illustrated in Section 6. The conclusions and future work are summarized in Section 7. Figures Markov random walk process. Framework of the proposed methodology. Unweighted neighbourhood information network for Example 1. Line chart for the inlier scores of objects. Classification of the Hayes-Roth dataset. Sensitivity analysis for the changes of θ in the Hayes-Roth dataset. Show all figures Section snippets Literature review This section provides a review of the studies on outlier detection based on neighbourhood rough sets and network representations for datasets. The identification methods based on the rough set are completely data-based and never require prior knowledge. In recent years, neighbourhood rough set-based methods have been effectively and widely applied for attribute reduction, feature selection, classification, and gene selection [4], [43], [6], [32]. For outlier detection, some neighbourhood Neighbourhood information system Let NIS = ( X , C , V , f ) be a neighbourhood information system, where X = x 1 , x 2 , … , x n is a nonempty finite set of objects known as a universe, C = c 1 , c 2 , … , c m is a nonempty finite set of attributes, V = ⋃ c ∈ C V c is the union of attribute domains, and f : X × C → V is an information function [42]. For a DMA, x i can be characterized by k numerical attributes and l categorical attributes, where k + l = m . The value of the i th object on the h th attribute can be denoted as x i h ′ ( i = 1 , 2 , … , n ; h = 1 , 2 , … , k , k + 1 , … , k + l - 1 , m ) . The neighbour Proposed methodology The proposed methodology and its algorithm will be discussed in detail in this section, and then an example is demonstrated accordingly. Experimental analysis The UCI datasets are used to verify the effectiveness of the proposed method in this section. We evaluate our theory and the proposed algorithms by using a PC with a 2.40 GHz Intel Core i7-55000U CPU and 8.00 GB of memory operating Windows 10. The algorithms are programmed using Visual Studio 2013. Two datasets from the UCI repository [2] are used to verify the effectiveness and feasibility of the proposed outlier detection method considering the change in the radius adjustment parameter θ and A real case study This section is motivated by an engineering requirement for the warehousing management of a company. The warehouse for this electronic tool manufacturing enterprise produces wheat cutters and provides raw materials for 44 production lines, which include preparation, receiving and unloading, tallying, and handling. In the warehousing process, many outlier events (such as damaged labels, unscannable codes, quantities delivered not matching documents, and nonstandardized goods) may occur. The Conclusions In this paper, we proposed a new outlier detection method based on the neighbourhood rough set and Markov random walk to analyse datasets with mixed-valued attributes without any prior knowledge. The WNIN that represents the DMA is proposed using the neighbourhood relations and similarities among the objects, which guarantees strong connectivity. Outlier detection is implemented by using the tailored Markov random walk process. After the Markov random walk process reaches the equilibrium state, CRediT authorship contribution statement Yu Wang: Conceptualization, Methodology, Software, Validation, Investigation, Data curation, Writing - original draft, Writing - review & editing, Visualization. Yupeng Li: Conceptualization, Methodology, Formal analysis, Resources, Writing - review & editing, Supervision, Project administration, Funding acquisition. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgment This work was supported by the National Natural Science Foundation, China (No. 51505480 , 51875345 ). The authors would like to thank the anonymous referees for their valuable comments and suggestions. References (44) M. Bouguessa A practical outlier detection approach for mixed-attribute data Expert Syst. Appl. (2015) H. Chen et al. Parallel attribute reduction in dominance-based neighborhood rough set Inf. Sci. (2016) Y. Chen et al. Neighborhood outlier detection Expert Syst. Appl. (2010) Y. Chen et al. Gene selection for tumor classification using neighborhood rough sets and entropy measures J. Biomed. Inform. (2017) P. Zhou et al. Online streaming feature selection using adapted neighborhood rough set Inf. Sci. (2019) W. Wei et al. A comparative study of rough sets for hybrid data Inf. Sci. (2012) M. Fort et al. Finding influential location regions based on reverse k-neighbor queries Knowl.-Based Syst. (2013) J. Ji et al. An improved k-prototypes clustering algorithm for mixed numeric and categorical data Neurocomputing (2013) Y. Liu et al. Fortune teller: predicting your career path National conference on artificial intelligence (2016) Q. Zhu et al. Weighted natural neighborhood graph: an adaptive structure for clustering and outlier detection with no neighborhood parameter Cluster Comput. (2016) D. Preotiucpietro et al. Beyond binary labels: political ideology prediction of twitter users C.C. Aggarwal et al. Outlier detection for high dimensional data ACM Sigmod Record (2001) P.J. Rana et al. A survey on fraud detection techniques in ecommerce Int. J. Comput. Appl. (2015) P.Y. Goh et al. Anomaly detection using correctness matching through a neighborhood rough set S. Ranshous et al. Anomaly detection in dynamic networks: a survey Wiley Interdiscip. Rev. Comput. Stat. (2015) D.M. Hawkins Identification of outliers (1980) T. Reuter et al. Event-based classification of social media streams International conference on multimedia retrieval (2012) Geyer et al. Practical markov chain monte carlo Stat. Sci. (1992) L. Sun et al. A gene selection approach based on the fisher linear discriminant and the neighborhood rough set Bioengineered (2017) N. Li et al. Exceptional events classification in warehousing based on an integrated clustering method for a dataset with mixed-valued attributes Int. J. Comput. Integr. Manuf. (2018) C. Wang et al. Trajectory-based multi-dimensional outlier detection in wireless sensor networks using hidden markov models Wireless Netw. (2014) X. Li et al. Outlier detection using the information entropy of neighborhood rough sets J. Inf. Comput. Sci. (2012) View more references Cited by (0) Recommended articles (6) Research article Adaptive denoising algorithm using peak statistics-based thresholding and novel adaptive complementary ensemble empirical mode decomposition Information Sciences, Volume 563, 2021, pp. 269-289 Show abstract This paper proposes an adaptive denoising methodology for noisy signals that employs a novel adaptive complementary ensemble empirical mode decomposition (NACEEMD) and a peak statistics (PS)-based thresholding technique. The key idea in this paper is the peak statistics (PS)-based thresholding technique,which breaks the traditional strategy with respect to selecting more accurate and more adaptive thresholds. The NACEEMD algorithm is proposed to decompose the noisy signal into a series of intrinsic mode functions (IMFs). At the same time, NACEEMD is also used to verify the applicability of the PS-based thresholding technique in different decomposition algorithms. The PS-based threshold is used to remove the noise inherent in noise-dominant IMFs, and the denoised signal is reconstructed by combining the denoised noise-dominant IMFs and the signal-dominant IMFs. This paper uses a various of simulated signals in various noisy environments for experiments, the experimental results indicate that the proposed algorithm outperforms traditional threshold denoising methodologies in terms of signal-to-noise ratio, root mean square error, and percent root distortion. Moreover, through real ECG signal and multi-sensor data fusion experiments, the application of the proposed algorithm in the field of engineering is explored and expanded. Research article Distributed H ∞ state estimation for switched sensor networks with packet dropouts via persistent dwell-time switching mechanism Information Sciences, Volume 563, 2021, pp. 256-268 Show abstract This paper addresses the distributed H ∞ state estimation problem for a class of sensor networks with switching characteristics, where the switchings of parameters are presumed to obey persistent dwell-time switching mechanism rather than dwell time or average dwell-time ones in the discrete-time context. For the purpose of tracking the unavailable state of the target plant, a sensor network is formed by employing multiple sensor nodes distributed in space and worked cooperatively under a specific connection topology. The intention of the paper mainly centers on deriving some sufficient criteria for the addressed model to achieve the exponential mean-square stability with a prescribed H ∞ performance, and the estimator gains corresponding to differently constructed estimators are further solved by means of the convex optimization method. Finally, the validity of the proposed approach is illustrated by a numerical example. Research article Nonnegative matrix factorization with local similarity learning Information Sciences, Volume 562, 2021, pp. 325-346 Show abstract Existing nonnegative matrix factorization methods usually focus on learning global structure of the data to construct basis and coefficient matrices, which ignores the local structure that commonly exists among data. To overcome this drawback, in this paper, we propose a new type of nonnegative matrix factorization method, which learns local similarity and clustering in a mutually enhanced way. The learned new representation is more representative in that it better reveals inherent geometric property of the data. Moreover, the new representation is performed in the kernel space, which enhances the capability of the proposed model in discovering nonlinear structures of data. Multiplicative updating rules are developed with theoretical convergence guarantees. Extensive experimental results have confirmed the effectiveness of the proposed model. Research article Semi-supervised classification by graph p -Laplacian convolutional networks Information Sciences, Volume 560, 2021, pp. 92-106 Show abstract The graph convolutional networks (GCN) generalizes convolution neural networks into the graph with an arbitrary topology structure. Since the geodesic function in the null space of the graph Laplacian matrix is constant, graph Laplacian fails to preserve the local topology structure information between samples properly. GCN thus cannot learn better representative sample features by the convolution operation of the graph Laplacian based structure information and input sample information. To address this issue, this paper exploits the manifold structure information of data by the graph p -Laplacian matrix and proposes the graph p -Laplacian convolutional networks (GpLCN). As the graph p -Laplacian matrix is a generalization of the graph Laplacian matrix, GpLCN can extract more abundant sample features and improves the classification performance utilizing graph p -Laplacian to preserve the rich intrinsic data manifold structure information. Moreover, after simplifying and deducing the formula of the one-order spectral graph p-Laplacian convolution, we introduce a new layer-wise propagation rule based on the one-order approximation. Extensive experiment results on the Citeseer, Cora and Pubmed database demonstrate that our GpLCN outperforms GCN. Research article Active contour driven by adaptively weighted signed pressure force combined with Legendre polynomial for image segmentation Information Sciences, Volume 564, 2021, pp. 327-342 Show abstract This paper proposes an active contour driven by adaptively weighted signed pressure force (SPF) combined with the Legendre polynomial method for image segmentation. First, an adaptively weighted global average intensity (GAI) term is defined wherein GAI differences are the weighted factors of the interior and exterior region-driving centers. Second, an adaptively weighted Legendre polynomial intensity (LPI) term is defined which adopts the Legendre polynomial intensity average differences as the weighted factors of the interior and exterior region-driving centers. Finally, the GAI and LPI terms are introduced into a novel SPF function and a coefficient is applied to weight their effect degrees; a new edge stopping function (ESF) is defined and combined with the region-based method to robustly converge the curve to the boundary of the object. Experiments demonstrate that this method is highly accurate and computationally efficient for images with inhomogeneous intensity, blurred edge, low contrast, and noise problems. Moreover, the segmentation results are independent of the initial contour. Research article Image robust adaptive steganography adapted to lossy channels in open social networks Information Sciences, Volume 564, 2021, pp. 306-326 Show abstract Currently, the demand for covert communication in open social networks brings new opportunities and challenges to existing image steganography technology in terms of robustness and security. To this end, an image robust adaptive steganography is proposed with robustness against multiple image processing attacks and detection resistance. First, a robust embedding domain with theoretical foundation and optimal invisibility is constructed based on the compression resistance principle. Then, utilizing the robust image abstraction and saliency measurement, the embedding channel is selected to avoid modifications in smooth regions and enhance visual quality. On this basis, the proposed method is given combining with error-correcting and STC codes to realize message embedding with minimum costs and improve extraction accuracy. Lastly, after parameters discussion and selection, the performance experiments are conducted compared with previous representative steganography algorithms, concerning robustness and detection resistance, and the fault tolerance is deduced, thereby providing the recommended coding parameters to improve message extraction integrity. The experimental results show that the proposed method can realize message extraction with high accuracy after JPEG compression, Gaussian noising, and scaling attacks, while holding comparable detection resistance to adaptive steganography against statistical features, which indicates its application prospect for covert communication in open lossy channels. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.02.045", "PubYear": 2021, "Volume": "564", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, School of Mines, China University of Mining and Technology, Xuzhou, Jiangsu 221116, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, School of Mines, China University of Mining and Technology, Xuzhou, Jiangsu 221116, China;Corresponding author"}], "References": []}, {"ArticleId": 86960520, "Title": "Evolutionary continuous constrained optimization using random direction repair", "Abstract": "To solve constrained optimization problems (COPs), it is crucial to guide the infeasible solution to a feasible region. Gradient-based repair (GR) is a successful repair strategy, where the forward difference is often used to estimate the gradient. However, GR has major deficiencies. First, it is difficult to deal with individuals falling into the local optima. Second, large amounts of fitness evaluations are required to estimate the gradient. In this paper, we proposed a new repair strategy, random direction repair (RDR). RDR generates a set of random directions, and calculates the repair direction and the repair step size of infeasible individual to reduce its constraint violation. Since the introduction of randomness, RDR could deal with individuals falling into the local optima. Furthermore, RDR only requires a few number of fitness evaluation. To demonstrate the performance of RDR, RDR was embedded into two state-of-the-art evolutionary continuous constrained optimization algorithms, tested on the Congress on Evolutionary Computation 2017 constrained real-parameter optimization benchmark. Experimental results demonstrated that RDR combined with evolutionary algorithms are highly competitive. Introduction Constrained optimization problems (COPs) are important, because many real-world optimization problems are limited by constraints, such as scheduling [26], the knapsack problem [14], optimal power flow [46], and antenna design [23]. Among these, if the search space is continuous, it is called a continuous COP (CCOP). With CCOPs, constraints divide the search space into several feasible and infeasible regions, and the goal of the optimization algorithm is to find the optimal solution in the feasible regions. Evolutionary algorithms (EAs) [20], [2], [17] and other meta-heuristic algorithms have been widely used with CCOPs, including differential evolution (DE) [35], [1], [19], evolutionary strategy (ES) [18], [32], [24], and particle swarm optimization (PSO) [30]. Furthermore, hybrid algorithms were often considered for their advantages and different capabilities. For example, a hybrid algorithm of PSO and genetic algorithm (GA) [37] proposed by Takahama et al. , a hybrid algorithm of an EA and sequential quadratic programming (SQP) [10] proposed by Deb et al. , a hybrid algorithm of an EA and local search (LS) [8] proposed by Datta et al. , a hybrid algorithm of the gravitational search algorithm (GSA) and a GA [13] proposed by Garg, and a hybrid algorithm of a GA and the gradient descent method [11] proposed by D’Angelo and Palmieri. To solve CCOPs, apart from evolutionary operators, the constraint handling technique is a core component for finding feasible regions. Common constraint handling techniques were divided into three categories in [33]: ranking/selection, problem reformulation, and parent selection/recombination. Ranking/selection redefines comparison rules to select offspring, such as the superiority of feasible solution (SF) [9], ε -constraint (EC) [36], and individual-dependent feasibility rule (IDFR) [44]. Problem reformulation defines a new fitness function formed by constraint and objective functions, and a widely used method is the penalty function [15], which adds a penalty term to the objective function to indicate the constraint violation. Parent selection/recombination selects suitable parents to generate competitive offspring. For example, in [31], parts of infeasible individuals were reserved for recombination to generate potential offspring. Constrained optimization is a hot topic in the field of evolutionary computation. Recently, much work has been accomplished. Specifically, at the congress on evolutionary computation (CEC) 2017 and 2018 competitions on constrained real-parameter optimization, many competitive algorithms were proposed. In particular, some variants of success-history-based adaptive DE, including linear population size reduction (i.e., L-SHADE) [38], [39], [29] achieved competitive results. For example, Zamuda proposed an L-SHADE with adaptive constraint handling (i.e., CAL-SHADE) [47]. Polakova applied L-SHADE44 [28] to CCOPs. Tvrdik and Polakova proposed a single framework combining L-SHADE44 and IDE (DE with an individual-dependent mechanism) [42]. Fun et al. proposed an LSHADE44 with an improved EC (i.e., LSHADE44-IEpsilon) [12]. Moreover, Anupam et al. proposed a unified DE (UDE) in [40], drawing on the advantages of several DE variants, such as self-adaptive DE (SaDE) and composite DE (CoDE). Then, an improved version of UDE, called IUDE [41], was proposed in the following year, which improved the parameter adaptation and the offspring selection, winning the first place at the CEC 2018 competition. Apart from DE, a variant of matrix adaptation evolution strategy (MA-ES), ∊ MAg-ES, was proposed by Hellwig et al. [16], combining EC and a gradient-based repair with MA-ES, winning the second place. The repair strategy is a positive technique for finding feasible solutions, which guides infeasible solutions toward promising regions. It is often used with combinatorial optimization problems [27], [6], [21]. Regarding CCOPs, some work has been performed from the perspective of repair strategy. In [25], Michalewicz and Nazhiyath proposed a repair strategy to repair an infeasible individual along the direction of a feasible reference point. In [7], Chootinan and Chen proposed gradient-based repair (GR), which used the gradient information derived from the constraint set to reduce the constraint violation. Afterward, Koch et al. proposed a repair method (i.e., RI-2) [22], which used the gradient of each constraint of an infeasible individual to form a parallelepiped, and then randomly search for feasible solutions in it. Recently, Spettel and Beyer developed a repair method for nonlinear constraints in [34]. Moreover, there is also a repair strategy for specific application [5]. Exist work [36], [4] has demonstrated that GR has been effective for solving CCOPs. However, GR has two major drawbacks. First, it repairs the individual to move only along the gradient. Thus, it is difficult to deal with the individual falling into the local optima. Second, each repair of GR requires D fitness evaluations to estimate the gradient, where D is the problem dimension. Therefore, the cost of GR is expensive when the dimensions are higher. In this paper, a new repair strategy, random direction repair (RDR), is proposed to deal with infeasible individuals and to find feasible regions. Overall, the contributions of this paper are listed as follows: 1. The main idea of RDR is that the infeasible individual is repaired along a random direction. RDR could reduce the constraint violation of the infeasible individual within a certain range. This is based on the fact that a random direction with a non-zero direction derivative is always monotonic in a local interval. In this paper, the primary focus of this work is to determine the repair direction and to calculate the repair step size, and the details of their derivation are provided. The infeasible individual repaired by RDR have more opportunities to escape the local optima, since the randomness is introduced in RDR to generate the uncertain repair direction. Moreover, the function evaluation cost of RDR is low, due to only one fitness evaluation is required to estimate the directional derivative at once. 2. RDR could be embedded into various evolutionary constrained optimization algorithms, such as two state-of-the-art EAs, IUDE and ∊ MAg-ES. In this paper, RDR is first embedded into IUDE to generate the offspring. Then, RDR is embedded into ∊ MAg-ES as an alternative to GR. RDR combined with two EAs are tested on the CEC 2017 constrained real-parameter optimization benchmark, which is also used in the CEC 2017 and 2018 competitions on constrained real parameter optimization. The experimental results demonstrate that the performance of RDR is competitive. The rest of this paper is organized as follows. Related work is introduced in Section 2. Section 3 describes the derivation and implementation of RDR. In Section 4, RDR is separately embedded into IUDE and ∊ MAg-ES. Section 5 shows experimental results and analysis. Then, the performance of RDR and GR is discussed in Section 6. Finally, Section 7 concludes this work and presents future considerations. Figures The fitness contour maps on 30D functions. The fitness contour maps on 50D functions. Comparison of the path generated by GR and RDR on Ackley function. Section snippets Related work In this section, related work is discussed. First, the definition of a CCOP is shown. Then, the typical repair strategy, GR, and two popular constraint handling techniques are introduced. Finally, some related algorithms, including DE, IUDE, and ∊ MAg-ES, are reviewed. In this paper, we always assume that the objective of a CCOP is to find the feasible solution with the minimum value of the fitness function. Random direction repair In this section, a novel repair strategy based on the random direction, random direction repair (RDR), is proposed. We first derive the use of single and multiple random directions, then provide an implementation. EAs with RDR In this section, RDR is embedded into IUDE and ∊ MAg-ES in two different methods. On the one hand, RDR is used to generate trial vectors in IUDE. On the other hand, RDR is applied in ∊ MAg-ES as a repair strategy to replace GR. Algorithm 2 RDR 1: function [ x , evals ] = RDR ( x ) 2: evals = 0 ; 3: Calculate Δ V ( x ) by Eq. (3); 4: Generate q columns of the random direction matrix M ; 5: Calculate Ψ in the direction matrix M ; 6: Calculate S with Eq. (26); 7: Δ x = MS ; 8: Clamp the range of Δ x ; 9: x ′ = x + Δ x ; 10: Calculate the Benchmark problems In this paper, we use 28 CCOPs from the CEC 2017 constrained real-parameter optimization competition as the benchmark problems [45]. For each CCOP, the maximum function evaluation is set to 20 , 000 D , where D = { 10 , 30 , 50 , 100 } are problem dimensions. Algorithm 4 RDR- ∊ MA-ES 1: if mod ( g , D ) = 0 and rand ⩽ 0.2 2: h = 1 ; 3: while h ⩽ τ ′ and ϕ ( x l ( g ) > 0 ) 4: [ x l ( g ) , used _ evals ] = RDR ( x l ( g ) ) ; 5: evals = evals + used _ evals ; 6: h = h + 1 ; 7: end while 8: end if The algorithm runs 25 times on each problem. The mean of the objective function Discussions In this section, RDR and GR are compared and analyzed according to the experimental results. Then, the Ackley function is used to illustrate the difference of RDR and GR. The comparison results of RDR- ∊ MA-ES and ∊ MAg-ES are shown in Section 5.3, demonstrating that RDR is superior to GR. In fact, on 10 D problems, RDR has a similar performance as GR, but consumes fewer evaluations. On the other problems, the constraint space is more complicated, and RDR is more advantageous in dealing with the Conclusion In this paper, we propose RDR to repair infeasible solutions. RDR gradually reduces the constraint violation of the infeasible solution along a random direction. On the one hand, RDR introduces randomness so that individuals are not easy to fall into the local optima. On the other hand, RDR requires only a few number fitness evaluations to calculate the step size for each repair. Then, RDR is embedded into two state-of-the-art EAs, modified from ∊ MAg-ES and IUDE as RDR- ∊ MA-ES and RDR-IUDE, CRediT authorship contribution statement Peilan Xu: Methodology, Software, Writing - original draft. Wenjian Luo: Methodology, Writing - original draft, Funding acquisition, Project administration. Xin Lin: Software, Writing - review & editing. Yingying Qiao: Software, Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. References (47) B.C. Wang et al. Individual-dependent feasibility rule for constrained differential evolution Inf. Sci. (2020) G.C. Onwubolu et al. Scheduling flow shops using differential evolution algorithm Eur. J. Oper. Res. (2006) R.N. Khushaba et al. Feature subset selection using differential evolution and a statistical repair mechanism Expert Syst. Appl. (2011) M. Chih Self-adaptive check and repair operator-based particle swarm optimization for the multidimensional knapsack problem Appl. Soft Comput. (2015) P. Chootinan et al. Constraint handling in genetic algorithms using a gradient-based repair method Comput. OR (2006) P.Y. Ho et al. Evolutionary constrained optimization using an addition of ranking method and a percentage-based tolerance value adjustment scheme Inf. Sci. (2007) K. Deb An efficient constraint handling method for genetic algorithms Comput. Methods Appl. Mech. Eng. (2000) M. Hellwig et al. A matrix adaptation evolution strategy for constrained real-parameter optimization H. Garg A hybrid GSA-GA algorithm for constrained optimization problems Inf. Sci. (2019) E. Mezura-Montes et al. A simple multimembered evolution strategy to solve constrained optimization problems IEEE Trans. Evol. Comput. (2005) A. Zamuda Adaptive constraint handling and success history differential evolution for CEC 2017 constrained real-parameter optimization T. Ray et al. An intelligent information sharing strategy within a swarm for unconstrained and constrained optimization problems Soft. Comput. (2002) M. Asafuddoula et al. A differential evolution algorithm with constraint sequencing T. Ray et al. Infeasibility driven evolutionary algorithm for constrained optimization Z. Fan et al. LSHADE44 with an improved ∊ ) constraint-handling method for solving constrained single-objective optimization problems T.P. Runarsson et al. Stochastic ranking for constrained evolutionary optimization IEEE Trans. Evol. Comput. (2000) J. Gottlieb On the effectivity of evolutionary algorithms for the multidimensional knapsack problem H.K. Singh et al. Use of infeasible solutions during constrained evolutionary search: a short survey K. Deb et al. A hybrid evolutionary multi-objective and SQP based procedure for constrained optimization P. Spettel et al. Matrix adaptation evolution strategies for optimization under nonlinear equality constraints Swarm Evol. Comput. (2020) R. Datta et al. Individual penalty based constraint handling using a hybrid bi-objective and penalty function approach T. Takahama et al. Constrained optimization by ε constrained differential evolution with dynamic ε- level control A. Isaacs, T. Ray, W. Smith, Blessings of maintaining infeasible solutions for constrained multi-objective optimization... View more references Cited by (0) Recommended articles (6) Research article Robust guaranteed cost control for uncertain discrete-time systems with state and input quantizations Information Sciences, Volume 564, 2021, pp. 288-305 Show abstract The robust guaranteed cost control problem for uncertain discrete-time systems with state and input quantizations has been studied in this paper. The polytope type uncertainties are considered in the plants. Different from previous related works, a novel guaranteed cost control strategy has been put forward in this paper. The novelty and challenge lie in that the quantized state and quantized input are included in the guaranteed cost function. Through introducing some auxiliary scalars and combining with the S -procedure, new criteria are developed for the robust stability and guaranteed cost performance for discrete-time systems with state and input quantizations. Based on a new two-step design strategy, the controller and dynamic quantizers can be easily obtained by means of linear matrix inequalities. In the end, two examples are given to demonstrate the effectiveness and applicability of the proposed method. Research article Fixed-time time-varying formation tracking for nonlinear multi-agent systems under event-triggered mechanism Information Sciences, Volume 564, 2021, pp. 45-70 Show abstract This study focuses on the fixed-time event-triggered time-varying formation tracking issue for a class of nonlinear multi-agent systems with multi-dimensional dynamics, uncertain disturbances and non-zero control input of leader. Firstly, a distributed fixed-time event-triggered control scheme is proposed such that the time-varying formation tracking can be achieved with intermittent controller updates and intermittent communication. To reduce the chattering phenomenon, a novel control protocol with saturation function is designed. It should be noted that continuous triggering condition monitoring is needed in the triggering mechanism. To address above issue, a novel triggering mechanism is further proposed. Moreover, the fixed-time event-triggered time-varying formation containment with multiple leaders is considered. It is verified that the Zeno behavior can be excluded. Finally, two examples are presented to demonstrate the feasibility of the main theoretical findings. Research article Active contour driven by adaptively weighted signed pressure force combined with Legendre polynomial for image segmentation Information Sciences, Volume 564, 2021, pp. 327-342 Show abstract This paper proposes an active contour driven by adaptively weighted signed pressure force (SPF) combined with the Legendre polynomial method for image segmentation. First, an adaptively weighted global average intensity (GAI) term is defined wherein GAI differences are the weighted factors of the interior and exterior region-driving centers. Second, an adaptively weighted Legendre polynomial intensity (LPI) term is defined which adopts the Legendre polynomial intensity average differences as the weighted factors of the interior and exterior region-driving centers. Finally, the GAI and LPI terms are introduced into a novel SPF function and a coefficient is applied to weight their effect degrees; a new edge stopping function (ESF) is defined and combined with the region-based method to robustly converge the curve to the boundary of the object. Experiments demonstrate that this method is highly accurate and computationally efficient for images with inhomogeneous intensity, blurred edge, low contrast, and noise problems. Moreover, the segmentation results are independent of the initial contour. Research article A highly effective hybrid evolutionary algorithm for the covering salesman problem Information Sciences, Volume 564, 2021, pp. 144-162 Show abstract Covering salesman problem (CSP) is an extension of the popular traveling salesman problem (TSP) arising from a number of real-life applications. Given a set of vertices and a predetermined coverage radius associated with each vertex, the goal of CSP is to find a minimum cost Hamiltonian cycle across a subset of vertices, such that each unvisited vertex must be within the coverage radius of at least one vertex included in the tour. For this NP-hard problem, we present a highly effective hybrid evolutionary algorithm (HEA) that integrates a crossover operator based on solution reconstruction, a destroy-and-repair mutation operator to generate multiple distinct offspring solutions, and a two-phase tabu search procedure to seek for high-quality local optima. Another distinguishing feature of HEA is the use of the Lin–Kernighan TSP heuristic to find an improved node sequence of a CSP tour during multiple stages of HEA. Extensive experiments on a large set of benchmark instances show that the proposed approach is able to surpass the current best-performing CSP heuristics. In particular, it reports new upper bound (improved best-known solution) for 21 out of the 27 large instances, while matching the best-known result for the remaining small and medium instances. In addition to CSP, the proposed HEA is adapted to solve the generalized covering traveling salesman problem (GCTSP). Extensive experimental results on the GCTSP benchmark disclose that the proposed adaptation of HEA outperforms all the existing GCTSP heuristics from the literature. Research article Image robust adaptive steganography adapted to lossy channels in open social networks Information Sciences, Volume 564, 2021, pp. 306-326 Show abstract Currently, the demand for covert communication in open social networks brings new opportunities and challenges to existing image steganography technology in terms of robustness and security. To this end, an image robust adaptive steganography is proposed with robustness against multiple image processing attacks and detection resistance. First, a robust embedding domain with theoretical foundation and optimal invisibility is constructed based on the compression resistance principle. Then, utilizing the robust image abstraction and saliency measurement, the embedding channel is selected to avoid modifications in smooth regions and enhance visual quality. On this basis, the proposed method is given combining with error-correcting and STC codes to realize message embedding with minimum costs and improve extraction accuracy. Lastly, after parameters discussion and selection, the performance experiments are conducted compared with previous representative steganography algorithms, concerning robustness and detection resistance, and the fault tolerance is deduced, thereby providing the recommended coding parameters to improve message extraction integrity. The experimental results show that the proposed method can realize message extraction with high accuracy after JPEG compression, Gaussian noising, and scaling attacks, while holding comparable detection resistance to adaptive steganography against statistical features, which indicates its application prospect for covert communication in open lossy channels. Research article Viewpoint adaptation learning with cross-view distance metric for robust vehicle re-identification Information Sciences, Volume 564, 2021, pp. 71-84 Show abstract Many vehicle re-identification (Re-ID) problems require the robust recognition of vehicle instances across multiple viewpoints. Existing approaches for dealing with the vehicle re-ID problem are insufficiently robust because they cannot distinguish among vehicles of the same type nor recognize high-level representations in deep networks for identical vehicles with various views. To address these issues, this paper proposes a viewpoint adaptation network (VANet) with a cross-view distance metric for robust vehicle Re-ID. This method consists of two modules. The first module is the VANet with cross-view label smoothing regularization (CVLSR), which abstracts different levels of a vehicle’s visual patterns and subsequently integrates multi-level features. In particular, CVLSR based on color domains assigns a virtual label to the generated data to smooth image-image translation noise. Accordingly, this module supplies the viewing angle information of the training data and provides strong robust capability for vehicles across different viewpoints. The second module is the cross-view distance metric, which designs a cascaded cross-view matching approach to combine the original features with the generated ones, and thus, obtain additional supplementary viewpoint information for the multi-view matching of vehicles. Results of extensive experiments on two large scale vehicle Re-ID datasets, namely, VeRi-776 and VehiclelD demonstrate that the performance of the proposed method is robust and superior to other state-of-the-art Re-ID methods across multiple viewpoints. <sup> ☆ </sup> This work is supported by National Natural Science Foundation of China (No. 61573327 ). View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.02.055", "PubYear": 2021, "Volume": "566", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei 230027, Anhui, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Shenzhen 518055, Guangdong, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei 230027, Anhui, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei 230027, Anhui, China"}], "References": [{"Title": "Individual-dependent feasibility rule for constrained differential evolution", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "174", "JournalTitle": "Information Sciences"}, {"Title": "Matrix adaptation evolution strategies for optimization under nonlinear equality constraints", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100653", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "GGA: A modified genetic algorithm with gradient-based local search for solving constrained optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "136", "JournalTitle": "Information Sciences"}]}]