[{"ArticleId": 90141907, "Title": "Physical layer security of cooperative wireless networks with RF energy harvesting", "Abstract": "The paper derives the secrecy outage probability (SOP) and the probability of strictly positive secrecy capacity (SPSC) for wireless networks with energy harvesting. The Base Station (BS) uses the received RF signal to harvest energy. The harvested energy is used to transmit data to the receiver R. The eavesdropper E receives the signal from BS and try to decode it. We also derive the SPSC and SOP when there are K relay nodes. The best relay is selected to amplify the BS signal to the receiver R. Copyright © 2021 Inderscience Enterprises Ltd. We show that the SOP decreases as the number of relays is increased. Besides, the SPSC increases as the number of relays is increased. The proposed system model uses relay nodes and offers a better PLS than (<PERSON> et al., 2018; <PERSON><PERSON> and <PERSON>, 2011; <PERSON> et al., 2016; <PERSON> et al., 2016; <PERSON> et al., 2017; <PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; <PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; <PERSON> et al., 2020; Khojastehnia and Loyka, 2020; <PERSON> et al., 2020; <PERSON><PERSON><PERSON> and <PERSON>, 2020).", "Keywords": "CDF of SINR; Energy harvesting using radio frequency (RF) signals; Outage probability; PDF of SINR; Physical layer security; PLS; Probability of strictly positive secrecy capacity; Rayleigh channels; Secrecy outage probability; SOP; SPSC", "DOI": "10.1504/IJSNET.2021.117230", "PubYear": 2021, "Volume": "36", "Issue": "3", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, College of Engineering, Prince <PERSON> University, Saudi Arabia"}], "References": []}, {"ArticleId": 90141965, "Title": "Modification of the fuzzy analytic hierarchy process via different ranking methods", "Abstract": "<p>The use of fuzzy set theory in the analytic hierarchy process (AHP) has gained popularity in recent years as part of the multiple criteria decision-making (MCDM) process to more realistically reflect human judgment. However, due to the nature of fuzzy calculations, this situation imposes more computational load. The aim of this study is to propose methods for obtaining accurate weights from fuzzy pairwise comparison matrices with the least amount of computational load possible. In this context, two different fuzzy AHP (FAHP) methods based on fuzzy numbers ranking methods have been proposed and these proposed methods are compared with commonly accepted FAHP methods. Magnitude-based fuzzy AHP (MFAHP), which is one of the proposed methods, has outperformed all other methods according to accurate weight and computational load. Although the other proposed method, called the total difference-based fuzzy AHP (TDFAHP), gave better results than the frequently used <PERSON>'s fuzzy extent analysis method, it could not produce more accurate weight results than many other methods in general. But performance analysis shows that it is as good as the MFAHP in terms of computational load.</p>", "Keywords": "fuzzy analytic hierarchy process;fuzzy multiple criteria decision-making;performance analysis;ranking fuzzy numbers", "DOI": "10.1002/int.22628", "PubYear": 2022, "Volume": "37", "Issue": "1", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Dokuz Eylul University, Izmir, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Dokuz Eylul University, Izmir, Turkey"}], "References": [{"Title": "Dealing with the problem of null weights and scores in Fuzzy Analytic Hierarchy Process", "Authors": "<PERSON>-Junior; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "13", "Page": "9557", "JournalTitle": "Soft Computing"}, {"Title": "A review of fuzzy AHP methods for decision-making with subjective judgements", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113738", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Third-party reverse logistics provider selection: A computational semantic analysis-based multi-perspective multi-attribute decision-making approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "166", "Issue": "", "Page": "114051", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Online-review analysis based large-scale group decision-making for determining passenger demands and evaluating passenger satisfaction: Case study of high-speed rail system in China", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "22", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 90142005, "Title": "Analysis and Application of Energy Demand Forecasting Based on Optimized Grey Model Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2021.104062", "PubYear": 2021, "Volume": "10", "Issue": "4", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "", "Affiliation": ""}], "References": []}, {"ArticleId": 90142062, "Title": "Domain adversarial transfer for cross-domain and task-constrained grasp pose detection", "Abstract": "Transferring the grasping skills learned from simulated environments to the real world is favorable for many robotic applications, in which the collecting and labeling processes of real-world visual grasping datasets are often expensive or even impractical. However, the models purely trained on simulated data are often difficult to generalize well to the unseen real world due to the domain gap between the training and testing data. In this paper, we propose a novel domain adversarial transfer network to narrow the domain gap for cross-domain and task-constrained grasp pose detection. Generative adversarial training is exploited to constrain the generator to produce simulation-like data for extracting the shared features with the joint distribution. We also propose to improve the backbone by extracting task-constrained grasp candidates and constructing the grasp candidate evaluator with a lightweight structure and an embedded recalibration technique. To validate the effectiveness and superiority of our proposed method, grasping performance evaluation and task-oriented human–robot interaction experiments were investigated. The experiment results indicate that the proposed method achieves state-of-the-art performance in these experimental settings. An average task-constrained grasping success rate of 83.3% without using any real-world labels for the task-oriented human–robot interaction experiment was achieved especially.", "Keywords": "Adversarial transfer learning ; Domain adaptation ; Grasp pose detection ; Human–robot interaction", "DOI": "10.1016/j.robot.2021.103872", "PubYear": 2021, "Volume": "145", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Southeast University, Nanjing 210096, China The Key Laboratory of Measurement and Control of CSE, Ministry of Education, Southeast University, Nanjing 210096, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Southeast University, Nanjing 210096, China The Key Laboratory of Measurement and Control of CSE, Ministry of Education, Southeast University, Nanjing 210096, China;Corresponding author at: School of Automation, Southeast University, Nanjing 210096, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Southeast University, Nanjing 210096, China The Key Laboratory of Measurement and Control of CSE, Ministry of Education, Southeast University, Nanjing 210096, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> Bai", "Affiliation": "School of Automation, Southeast University, Nanjing 210096, China The Key Laboratory of Measurement and Control of CSE, Ministry of Education, Southeast University, Nanjing 210096, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Automation, Southeast University, Nanjing 210096, China The Key Laboratory of Measurement and Control of CSE, Ministry of Education, Southeast University, Nanjing 210096, China"}], "References": [{"Title": "Grasp Pose Detection with Affordance-based Task Constraint Learning in Single-view Point Clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "1", "Page": "145", "JournalTitle": "Journal of Intelligent & Robotic Systems"}]}, {"ArticleId": 90142262, "Title": "Enhancing learning engagement during\n COVID\n ‐19 pandemic:\n Self‐efficacy\n in time management, technology use, and online learning environments", "Abstract": "<h3 > Background</h3> <p>Due to the global COVID-19 pandemic, online learning became the only way to learn during this unprecedented crisis. This study began with a simple but vital question: What factors influenced the success of online learning during the COVID-19 pandemic with a focus on online learning self-efficacy?</p> <h3 > Objectives</h3> <p>The purpose of this study was to examine the structural relationship among self-efficacy (SE) in time management, SE in technology use, SE in an online learning environment, and learning engagement.</p> <h3 > Methods</h3> <p>The participants of the study were 1205 undergraduates who were enrolled in a residential undergraduate program in South Korea in spring semester, 2020. The online survey was administered to collect data for this research and the survey results were analyzed using structural equation modeling.</p> <h3 > Results and Conclusions</h3> <p>SE in technology use had a significant but negative influence on learning engagement and had a positive impact on SE in an online learning environment. SE in time management had a significant positive impact on SE in an online learning environment and learning engagement. SE in an online learning environment also significantly influenced learning engagement.</p> <h3 > Implications</h3> <p>SE in technology use itself did not enhance learning engagement. In addition, indirect effects of SE in technology use and SE in time management on learning engagement through SE in an online learning environment were confirmed in this study. This indicates the influential role of SE in an online learning environment on learning engagement of online learners.</p> <h3 >Lay Description</h3> <h3 > What is already known about this topic</h3> COVID-19 forced almost all students to learn online. Time management plays a major role in the success of online learning. Technology self-efficacy is a requirement in online learning. The learning engagement variable explains learning achievement and attitudes. <h3 > What this paper adds</h3> Self-efficacy (SE) in time management and SE in technology use enhanced SE in an online learning environment. SE in technology use itself did not enhance learning engagement. SE in time management and in SE an online learning environment significantly influenced learning engagement. SE in an online learning environment mediated the relationship between SE in technology use and learning engagement as well as between SE in time management and learning engagement. <h3 > Implications for practice and/or policy</h3> It is important to promote students' online learning self-efficacy to enhance learning engagement in online learning. Practitioners should be aware that SE in technology use improves learning engagement through SE in an online learning environment. SE in time management should be promoted for successful online learning.", "Keywords": "COVID-19;learning engagement;online learning readiness;online learning self-efficacy;technology use;time management", "DOI": "10.1111/jcal.12603", "PubYear": 2021, "Volume": "37", "Issue": "6", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Education, College of Education, Sunchon National University, Suncheon-si, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Instructional Systems Technology Department, School of Education, Indiana University, Bloomington, Indiana, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Education, College of Education, Kangwon National University, Chuncheon-si, South Korea"}], "References": []}, {"ArticleId": 90142295, "Title": "Predicting Driver Fatigue in Monotonous Automated Driving with Explanation using GPBoost and SHAP", "Abstract": "Research indicates that monotonous automated driving increases the incidence of fatigued driving. Although many prediction models based on advanced machine learning techniques were proposed to monitor driver fatigue, especially in manual driving, little is known about how these black-box machine learning models work. In this paper, we proposed a combination of Gaussian Process Boosting (GPBoost) and SHapley Additive exPlanations (SHAP) to predict driver fatigue with explanations. First, in order to obtain the ground truth of driver fatigue, we used PERCLOS (percentage of eyelid closure over the pupil over time) between 0 and 100 as the response variable. Second, we built a driver fatigue regression model using both physiological and behavioral measures with GPBoost that was able to address the within-subjects correlations. This model outperformed other selected machine learning models with root-mean-squared error (RMSE) = 2.965, mean absolute error (MAE) = 1.407, and adjusted R 2 = 0.996 . Third, we employed SHAP to identify the most important predictor variables and uncovered the black-box GPBoost model by showing the main effects of the most important predictor variables globally and explaining individual predictions locally. Such an explainable driver fatigue prediction model offered insights into how to intervene in automated driving when necessary, such as during the takeover transition period from automated driving to manual driving. Acknowledgments This work was supported by Ford Summer Sabbatical Program. Additional information Notes on contributors <PERSON> received his Ph.D. degrees in Mechanical Engineering and Human Factors from Gatech and Nanyang Technological University in 2014 and 2011, respectively. He is currently Assistant Professor at the Department of Industrial and Manufacturing Systems Engineering, University of Michigan-Dearborn, focusing on human factors, engineering design, and machine learning. Areen Alsaid Areen Alsaid received the Ph.D. degree from the Industrial and Systems Engineering Department at the University of Wisconsin-Madison, Madison, Wi in 2020. She is currently a research scientist at Ford Motor Company. Her research interests include affective computing, driver state estimation, and driving safety. Mike Blommer Mike Blommer received his Ph.D. degree in Electrical Engineering from the University of Michigan, Ann Arbor in 1995, specializing in signal processing and psychoacoustics. He is currently a Technical Leader at Ford Motor Company, with research interests in driver warning systems, automated driving, HMI, and vehicle dynamics. Reates Curry Reates Curry received the Ph.D. degree in Biomedical Engineering from Rutgers University, NJ. She has been with the Ford Research & Innovation Center since 1995 working with the driving simulator team. Her expertise includes driver safety, eye tracking research, etc. Radhakrishnan Swaminathan Radhakrishnan Swaminathan received the Ph.D. degree in Electrical and Computer Engineering from Oakland University, MI, USA, in 2012. He is currently a Research Engineer with Research and Advanced Engineering, Ford Motor Company. He conducts research on driver HMI, distraction, driver warning systems, and automated driving in Ford’s VIRTTEX Driving Simulator. Dev Kochhar Dev Kochhar received his Ph.D degree in System Design from the University of Waterloo, Canada, in 1974. He was a Professor of industrial engineering (12 years), and a Technical Staff at AT&T Bell Laboratories (6 years). He is currently a retired Technical Specialist with the Ford Motor Company. Walter Talamonti Walter Talamonti received the Ph.D. degree in Industrial Engineering from Wayne State University, Detroit, MI, USA, in 2017. He is currently a Research Engineer with Research and Advanced Engineering, Ford Motor Company. He conducts human factors research in the areas of human–machine interface, driver warning systems, and autonomous driving. Louis Tijerina Louis Tijerina received the Ph. D. degree in Experimental Psychology from The Ohio State University in 1989. He is currently a retired Senior Technical Specialist at Ford Motor Company. His research interests include driver HMI, distraction, driver warning systems, and automated driving.", "Keywords": "", "DOI": "10.1080/10447318.2021.1965774", "PubYear": 2022, "Volume": "38", "Issue": "8", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Michigan, Dearborn, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, The University of Wisconsin, Madison, WI, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ford Motor Company Research and Advanced Engineering, Dearborn, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Ford Motor Company Research and Advanced Engineering, Dearborn, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ford Motor Company Research and Advanced Engineering, Dearborn, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Ford Motor Company Research and Advanced Engineering, Dearborn, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Ford Motor Company Research and Advanced Engineering, Dearborn, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Ford Motor Company Research and Advanced Engineering, Dearborn, USA"}], "References": [{"Title": "Driver fatigue transition prediction in highly automated driving using physiological features", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "113204", "JournalTitle": "Expert Systems with Applications"}, {"Title": "From local explanations to global understanding with explainable AI for trees", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "56", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 90142303, "Title": "ViMantic, a distributed robotic architecture for semantic mapping in indoor environments", "Abstract": "Semantic maps augment traditional representations of robot workspaces, typically based on their geometry and/or topology, with meta-information about the properties, relations and functionalities of their composing elements. A piece of such information could be: fridges are appliances typically found in kitchens and employed to keep food in good condition. Thereby, semantic maps allow for the execution of high-level robotic tasks in an efficient way, e.g. “ Hey robot, Store the leftover salad ”. This paper presents ViMantic , a novel semantic mapping architecture for the building and maintenance of such maps, which brings together a number of features as demanded by modern mobile robotic systems, including: (i) a formal model, based on ontologies, which defines the semantics of the problem at hand and establishes mechanisms for its manipulation; (ii) techniques for processing sensory information and automatically populating maps with, for example, objects detected by cutting-edge CNNs; (iii) distributed execution capabilities through a client–server design, making the knowledge in the maps accessible and extendable to other robots/agents; (iv) a user interface that allows for the visualization and interaction with relevant parts of the maps through a virtual environment; (v) public availability, hence being ready to use in robotic platforms. The suitability of ViMantic has been assessed using [email protected] , a vast repository of data collected by a robot in different houses. The experiments carried out consider different scenarios with one or multiple robots, from where we have extracted satisfactory results regarding automatic population, execution times, and required size in memory of the resultant semantic maps.", "Keywords": "Semantic maps ; Robotic architecture ; Mobile robots ; Unity 3D ; ROS ; Object detection ; Detectron2 ; [email protected]", "DOI": "10.1016/j.knosys.2021.107440", "PubYear": 2021, "Volume": "232", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Machine Perception and Intelligent Robotics group (MAPIR), Dept. of System Engineering and Automation, Biomedical Research Institute of Malaga (IBIMA), University of Malaga, Spain;<PERSON> Institute of Mathematics and Computing Science, University of Groningen, The Netherlands;Corresponding author at: Machine Perception and Intelligent Robotics group (MAPIR), Dept. of System Engineering and Automation, Biomedical Research Institute of Malaga (IBIMA), University of Malaga, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>Sarmiento", "Affiliation": "Machine Perception and Intelligent Robotics group (MAPIR), Dept. of System Engineering and Automation, Biomedical Research Institute of Malaga (IBIMA), University of Malaga, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> Institute of Mathematics and Computing Science, University of Groningen, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Perception and Intelligent Robotics group (MAPIR), Dept. of System Engineering and Automation, Biomedical Research Institute of Malaga (IBIMA), University of Malaga, Spain"}], "References": [{"Title": "Building semantic grid maps for domestic robot navigation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "1", "Page": "172988141990006", "JournalTitle": "International Journal of Advanced Robotic Systems"}]}, {"ArticleId": 90142357, "Title": "Analysis of the influence sensitivity for the regulator parameters with approximating control on the factors of the regulation quality", "Abstract": "<p>The parametric sensitivity of the control quality factors is investigated; the most significant parameters of the regulator settings for the transient and steady-state modes of the system are validated. The range of minimum and maximum values of the sensitivity coefficient, at which the quality indicators are sensitive to variations in the tuning parameters of the investigated regulator is established. The results of the analysis permit to choose acceptable ranges for the parameters variation of the regulator settings at the stage of developing multi-mode control systems. Keywords sensitivity of quality indicators; regulator with approximating control function; multi-mode control system</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2021-75-8-377-383", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "SHahray E.A. Lubentsova E.V. Lubentsov V.F.", "Affiliation": ""}], "References": []}, {"ArticleId": 90142611, "Title": "A mortar formulation for frictionless line-to-line beam contact", "Abstract": "This paper describes the quasi-static formulation of frictionless line contact between flexible beams by employing the mortar finite element approach. Contact constraints are enforced in a weak sense along the contact region using Lagrange multipliers. A simple projection appropriate for thin beams with circular cross-sections is proposed for the computation of contact regions. It is combined with the geometrically exact beam formalism on the Lie group $SE(3)$ \n S \n E \n ( \n 3 \n ) \n . Interestingly, this framework leads to a constraint gradient and a tangent stiffness invariant under rigid body transformations. The formulation is tested in some numerical examples.", "Keywords": "Beam contact; Mortar; Special Euclidean group", "DOI": "10.1007/s11044-021-09799-5", "PubYear": 2022, "Volume": "54", "Issue": "1", "JournalId": 5013, "JournalTitle": "Multibody System Dynamics", "ISSN": "1384-5640", "EISSN": "1573-272X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aerospace and Mechanical Engineering, University of Liège, Liège, Belgium;Fraunhofer Institute for Industrial Mathematics, Kaiserslautern, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Aerospace and Mechanical Engineering, University of Liège, Liège, Belgium;CIMEC, Universidad Nacional del Litoral / CONICET, Santa Fe, Argentina"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Fraunhofer Institute for Industrial Mathematics, Kaiserslautern, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Aerospace and Mechanical Engineering, University of Liège, Liège, Belgium"}], "References": []}, {"ArticleId": ********, "Title": "Graph-based optimal routing in clustered WSNs", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.********", "PubYear": 2021, "Volume": "37", "Issue": "4", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Perception Engineering Learning With Virtual Reality", "Abstract": "Virtual reality (VR) technology provides new possibilities for educators to design and optimize learning experiences. However, it remains largely unclear how virtual learning environments can be engineered to achieve measurable improvements in the learners’ performance. To advance the development and application of VR learning technologies, we systematized instruction principles from the literature known to be congruent with cognitive and affective learning outcomes. We then combined these principles and designed a VR training program, which we subsequently tested in a large first-person VR experiment and real-world application to understand behavioral performance effects. In this article, we found that a purposefully engineered VR environment, which had been designed with an evidence-based selection of instruction principles, significantly improved the human performance compared to both a baseline training with textual instructions and a basic VR training. Our results indicate that perception engineering learning with VR is possible and offers substantial opportunities to improve learning outcomes.", "Keywords": "Cognitive load theory;instructions;manufacturing;training;virtual reality (VR).", "DOI": "10.1109/TLT.2021.3107407", "PubYear": 2021, "Volume": "14", "Issue": "4", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cyber-Human Lab, Department of Engineering, University of Cambridge, Cambridge, U.K"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory for Machine Tools and Production Engineering, RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>r Gurer<PERSON>", "Affiliation": "Faculty of Law, Social Sciences, and Economics, University of Erfurt, Erfurt, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for Production Systems, Technical University of Dortmund, Dortmund, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Technical University of Munich, Munich, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Education, University of Cambridge, Cambridge, U.K"}], "References": [{"Title": "Augmented Immersive Reality (AIR) for Improved Learning Performance: A Quantitative Evaluation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "2", "Page": "283", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": 90142958, "Title": "A Prevailing-Decree Verifier intended for Cryptographic Etiquettes", "Abstract": "<p>In recent years, a number of cryptographic etiquettes have been mechanically verified using a selection of inductive methods. These attestations typically want central a figure of recursive sets of messages, and need deep intuition into why the etiquette is correct. As a result, these proofs frequently require days to weeks of expert effort. We ensure advanced an involuntary verifier, which seems to overawe these glitches for many cryptographic protocols. The code of behavior text to concept a number of first-order invariant the proof commitments mitigating these invariants, along with any user-specified protocol properties are showed from the invariants with a tenacity theorem proved. The individual litheness in construction these invariants is to guesstimate, for each type of nonce and encryption engendered by the protocol, a formulary arresting conditions compulsory for that nonce encryption to be published.  </p>", "Keywords": "", "DOI": "10.18535/ijecs/v10i8.4611", "PubYear": 2021, "Volume": "10", "Issue": "8", "JournalId": 17925, "JournalTitle": "International Journal Of Engineering And Computer Science", "ISSN": "", "EISSN": "2319-7242", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": "M.Sc.,<PERSON>.Phil.,Ph.D., HOD & Assistant Professor, Department of Computer Science, P.K.N Art & Science College, Tirumangalam-625706 Tamil Nadu"}], "References": []}, {"ArticleId": 90142979, "Title": "A hybrid gene selection model for molecular breast cancer classification using a deep neural network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAPR.2021.117203", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 29319, "JournalTitle": "International Journal of Applied Pattern Recognition", "ISSN": "2049-887X", "EISSN": "2049-8888", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90143109, "Title": "Piezopermittivity for capacitance-based strain/stress sensing", "Abstract": "Piezopermittivity refers to the reversible change of the electric permittivity (the main material property that describes the dielectric behavior) with the elastic strain. This paper addresses the emerging field of piezopermittivity, which provides the basis for capacitance-based strain/stress sensing, including self-sensing in case of structural materials. Piezopermittivity differs from piezoresistivity, which allows resistance-based strain/stress sensing. It also differs from the direct piezoelectric effect, which does not require the permittivity to change. In order to establish piezopermittivity, this paper presents the piezopermittivity theory, sensing methodology, and piezopermittivity-related materials science. Piezopermittivity stems from the effect of strain on the microstructure, which affects the permittivity. It is exhibited by electrical conductors (metals, carbons, and carbon fiber polymer-matrix and carbon-matrix composite) and nonconductors (perovskite ceramic and 3D-printed polymer), which are comparably effective for capacitance-based sensing, as shown at frequencies ≤2 kHz. All materials are unpoled. Poling of the perovskite ceramic does not alter the piezopermittivity behavior. The magnitude of the fractional change in permittivity much exceeds the strain magnitude, as expected for piezopermittivity. The sensing effectiveness, as described by the fractional change in permittivity per unit strain, is positive for positive piezopermittivity and negative for negative piezopermittivity. The majority of the materials exhibit positive piezopermittivity. The sensing effectiveness is +1.99×10<sup>3</sup> and -4.81×10<sup>2</sup> for uncoated and nickel-coated carbon fibers, respectively. The value for the uncoated carbon fiber is close to the value of +1.21×10<sup>3</sup> for a perovskite ceramic. For a 3D-printed polymer, the value is -2.87×10<sup>6</sup> in the direction perpendicular to the printed layers. Capacitance-based sensing is advantageous to the widely reported resistance-based sensing in that it does not require intimate contact of the electrodes with the specimen. The measurement of the capacitance of a conductive material using an LCR meter requires a dielectric film between the electrode and specimen.", "Keywords": "Permittivity ; Dielectric constant ; Strain sensing ; Self-sensing ; Piezoelectric ; Piezopermittivity ; Capacitance", "DOI": "10.1016/j.sna.2021.113028", "PubYear": 2021, "Volume": "332", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "D.D.L<PERSON>", "Affiliation": "Composite Materials Research Laboratory, Department of Mechanical and Aerospace Engineering, University at Buffalo, The State University of New York, Buffalo, NY 14260-4400, USA;Corresponding author"}, {"AuthorId": 2, "Name": "Xiang Xi", "Affiliation": "Composite Materials Research Laboratory, Department of Mechanical and Aerospace Engineering, University at Buffalo, The State University of New York, Buffalo, NY 14260-4400, USA"}], "References": [{"Title": "Piezoelectret-based and piezoresistivity-based stress self-sensing in steel beams under flexure", "Authors": "<PERSON><PERSON>; D.D<PERSON>L<PERSON>", "PubYear": 2020, "Volume": "301", "Issue": "", "Page": "111780", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 90143342, "Title": "A multi-group dragonfly algorithm for application in wireless sensor network deployment problem", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.10040654", "PubYear": 2021, "Volume": "37", "Issue": "4", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> Chu<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90143367, "Title": "A novel implicit hybrid machine learning model and its application for reinforcement learning", "Abstract": "A novel methodology to develop implicit hybrid models is presented. PyTorch is used to integrate physics-based equations with machine learning models. Automatic differentiation of the hybrid model is leveraged to solve the implicit equations. Iterative solving enables gradient based updates to the machine learning model. The novel methodology is compared to an explicit hybrid approach on a continuously stirred tank reactor (CSTR). The novel method results in a lower modelling error. Both hybrid models effectively train with noisy data. To test the implicit hybrid model, it is employed as a reinforcement learning (RL) training model. The RL algorithm trained on the hybrid model outperforms real time optimization of the CSTR and performs nearly as well as RL trained directly on the CSTR and a traditional gradient based approach. Training RL directly on the CSTR requires over 60,000 system interactions compared to 6000 historical data points for hybrid model development.", "Keywords": "Hybrid model ; Gray-box model ; Implicit methods ; Reinforcement learning ; Automatic differentiation", "DOI": "10.1016/j.compchemeng.2021.107496", "PubYear": 2021, "Volume": "155", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Utah, Salt Lake City, UT, USA;Corresponding author"}], "References": [{"Title": "Reinforcement learning for batch bioprocess optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "106649", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Deep hybrid modeling of chemical process: Application to hydraulic fracturing", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "106696", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "A review On reinforcement learning: Introduction and applications in industrial process control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106886", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Hybrid Modeling in the Era of Smart Manufacturing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106874", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Real-time optimization using reinforcement learning", "Authors": "<PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "107077", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Gray-Box system modeling using symbolic regression and nonlinear model predictive control of a semibatch polymerization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "107204", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 90143386, "Title": "Bayesian inference for generalized linear model with linear inequality constraints", "Abstract": "Bayesian statistical inference for Generalized Linear Models (GLMs) with parameters lying on a constrained space is of general interest (e.g., in monotonic or convex regression), but often constructing valid prior distributions supported on a subspace spanned by a set of linear inequality constraints can be challenging, especially when some of the constraints might be binding leading to a lower dimensional subspace. For the general case with canonical link, it is shown that a generalized truncated multivariate normal supported on a desired subspace can be used. Moreover, it is shown that such prior distribution facilitates the construction of a general purpose product slice sampling method to obtain (approximate) samples from corresponding posterior distribution, making the inferential method computationally efficient for a wide class of GLMs with an arbitrary set of linear inequality constraints. The proposed product slice sampler is shown to be uniformly ergodic, having a geometric convergence rate under a set of mild regularity conditions satisfied by many popular GLMs (e.g., logistic and Poisson regressions with constrained coefficients). One of the primary advantages of the proposed Bayesian estimation method over classical methods is that uncertainty of parameter estimates is easily quantified by using the samples simulated from the path of the Markov Chain of the slice sampler. Numerical illustrations using simulated data sets are presented to illustrate the superiority of the proposed methods compared to some existing methods in terms of sampling bias and variances. In addition, real case studies are presented using data sets for fertilizer-crop production and estimating the SCRAM rate in nuclear power plants.", "Keywords": "Bayesian inference ; Generalized linear model ; Inequality constrained estimation", "DOI": "10.1016/j.csda.2021.107335", "PubYear": 2022, "Volume": "166", "Issue": "", "JournalId": 3314, "JournalTitle": "Computational Statistics & Data Analysis", "ISSN": "0167-9473", "EISSN": "1872-7352", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biostatistics, Johns Hopkins University, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, North Carolina State University, Raleigh, NC, USA"}], "References": []}, {"ArticleId": 90143388, "Title": "An adaptive leak localisation system based on a multi-level analytics framework in piping network", "Abstract": "Leak localisation is a growing concern in water distribution system (WDS). The conventional time-correlation analysis incorporates with acoustic sensing is a feasible leak localisation technique in single pipeline system. However, this technique is impractical and time-consuming in a piping network due to multi-directional transmission waves from the leak source. In this paper, we propose an adaptive leak localisation system incorporating a remote-acoustic sensor network and a multi-level analytics framework (MLAF) for piping networks. The MLAF overcomes the multi-directional waves issue in piping networks. The system is adopted with an automated flow control algorithm to ensure time-effective localisation without needs of human supervision. The performance of MLAF has been evaluated based on several emulated piping networks with various network topologies. The characterisation results demonstrated high adaptiveness and location accuracies. The excellent results of field prediction in a local district metered area (DMA) further validated the feasibility of the MLAF. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Clustering analysis; Data analytics; Leak localisation; Path analysis; Pipeline monitoring; Wireless sensor network; WSN", "DOI": "10.1504/IJSNET.2021.117231", "PubYear": 2021, "Volume": "36", "Issue": "3", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lee Kong Chian Faculty of Engineering and Science, Department of Electrical and Electronics Engineering, Universiti Tunku <PERSON>, Bandar Sungai Long, Selangor, Kajang, 43000, Malaysia"}], "References": []}, {"ArticleId": 90143500, "Title": "Traceability Management Strategy of the EV Power Battery Based on the Blockchain", "Abstract": "<p>Regulating and supervising the energy vehicle (EV) power battery recycling market, improving the utilization rate of EV power battery recycling, and guaranteeing the safety and control of all aspects of recycling treatment require the establishment of an effective traceability system. The decentralization and tamper-proof characteristics of the blockchain can ensure the safety and reliability of relevant data while realizing traceability management. This study establishes the Stackelberg game model to compare and analyze the effects of different government mechanisms on the profits of each subject before and after participating in power battery traceability management. The study further uses the model to explore strategies to improve the enthusiasm of EV power battery recycling subjects to participate in traceability management. The results show that (1) the participation of each recycling subject in EV power battery blockchain traceability can help move more spent power batteries into formal recycling channels; (2) the government should adopt appropriate mechanisms to promote its participation in EV power battery blockchain traceability, the best result being when the government adopts a subsidy mechanism for consumers; and (3) the profit of the EV power battery manufacturer is inversely proportional to the target recycling rate set by the government. Furthermore, the pursuit of a very high target recycling rate is not conducive to the normal implementation of initial EV power battery blockchain traceability management. Therefore, it is crucial for the government to set a reasonable target recycling rate.</p>", "Keywords": "", "DOI": "10.1155/2021/5601833", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "Yan<PERSON> Cheng", "Affiliation": "School of Economics and Management, Shanghai Polytechnic University, Shanghai 201209, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>o", "Affiliation": "School of Economics and Management, Shanghai Polytechnic University, Shanghai 201209, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Shanghai Polytechnic University, Shanghai 201209, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Shanghai Polytechnic University, Shanghai 201209, China"}], "References": []}, {"ArticleId": 90143515, "Title": "Lean Privacy Review: Collecting Users’ Privacy Concerns of Data Practices at a Low Cost", "Abstract": "<p> Today, industry practitioners (e.g., data scientists, developers, product managers) rely on formal privacy reviews (a combination of user interviews, privacy risk assessments, etc.) in identifying potential customer acceptance issues with their organization’s data practices. However, this process is slow and expensive, and practitioners often have to make ad-hoc privacy-related decisions with little actual feedback from users. We introduce Lean Privacy Review (LPR), a fast, cheap, and easy-to-access method to help practitioners collect direct feedback from users through the proxy of crowd workers in the early stages of design. LPR takes a proposed data practice, quickly breaks it down into smaller parts, generates a set of questionnaire surveys, solicits users’ opinions, and summarizes those opinions in a compact form for practitioners to use. By doing so, LPR can help uncover the range and magnitude of different privacy concerns actual people have at a small fraction of the cost and wait-time for a formal review. We evaluated LPR using 12 real-world data practices with 240 crowd users and 24 data practitioners. Our results show that (1) the discovery of privacy concerns saturates as the number of evaluators exceeds 14 participants, which takes around 5.5 hours to complete (i.e., latency) and costs 3.7 hours of total crowd work ( $80 in our experiments); and (2) LPR finds 89% of privacy concerns identified by data practitioners as well as 139% additional privacy concerns that practitioners are not aware of, at a 6% estimated false alarm rate. </p>", "Keywords": "", "DOI": "10.1145/3463910", "PubYear": 2021, "Volume": "28", "Issue": "5", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Forbes Ave, Pittsburgh"}, {"AuthorId": 2, "Name": "<PERSON> Shen", "Affiliation": "Carnegie Mellon University, Forbes Ave, Pittsburgh"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Forbes Ave, Pittsburgh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Forbes Ave, Pittsburgh"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Forbes Ave, Pittsburgh"}], "References": []}, {"ArticleId": 90143551, "Title": "A novel transversal processing model to build environmental big data services in the cloud", "Abstract": "This paper presents a novel transversal, agnostic-infrastructure, and generic processing model to build environmental big data services in the cloud. Transversality is used for building processing structures (PS) by reusing/coupling multiple existent software for processing environmental monitoring, climate, and earth observation data, even in execution time, with datasets available in cloud-based repositories. Infrastructure-agnosticism is used for deploying/executing PSs on/in edge, fog, and/or cloud. Genericity is used to embed analytic, merging information, machine learning, and statistic micro-services into PSs for automatically and transparently converting PSs into big data services to support decision-making procedures. A prototype was developed for conducting case studies based on the data climate classification, earth observation products, and making predictions of air data pollution by merging different monitoring climate data sources. The experimental evaluation revealed the efficacy and flexibility of this model to create complex environmental big data services.", "Keywords": "Big data ; Cloud computing ; Environmental data ; Climate data ; Machine learning ; Data analytic", "DOI": "10.1016/j.envsoft.2021.105173", "PubYear": 2021, "Volume": "144", "Issue": "", "JournalId": 3648, "JournalTitle": "Environmental Modelling & Software", "ISSN": "1364-8152", "EISSN": "1873-6726", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CINVESTAV, Tamaulipas, Mexico;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "CINVESTAV, Tamaulipas, Mexico;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "UC3M, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CINVESTAV, Tamaulipas, Mexico"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Naples Parthenope, Italy"}], "References": [{"Title": "Topic-based crossing-workflow fragment discovery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "1141", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Kulla, a container-centric construction model for building infrastructure-agnostic distributed and parallel applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>-<PERSON><PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "110665", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Distributed long-term hourly streamflow predictions using deep learning – A case study for State of Iowa", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "104761", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "A cloud-based framework for sensitivity analysis of natural hazard models", "Authors": "<PERSON><PERSON><PERSON><PERSON> KC; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "104800", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Interoperable web sharing of environmental models using OGC web processing service and Open Modeling Interface (OpenMI)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "104838", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "An efficient pattern-based approach for workflow supporting large-scale science: The DagOnStar experience", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "122", "Issue": "", "Page": "187", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 90143805, "Title": "Dr <PERSON><PERSON><PERSON><PERSON>: Professor at UC-Berkeley, pioneer of exoskeletons, founder and CEO of U.S. Bionics (DBA suitX), founder <PERSON><PERSON><PERSON> Bionics", "Abstract": "Purpose \nThe following paper is a “Q&A interview” conducted by <PERSON> of Industrial Robot Journal as a method to impart the combined technological, business and personal experience of a prominent, robotic industry PhD-turned innovator and entrepreneur regarding his pioneering efforts. The paper aims to discuss these issues.\n \n \n Design/methodology/approach \nThe interviewee is Dr <PERSON><PERSON><PERSON><PERSON>, Professor of Mechanical Engineering at the University of California (UC) Berkeley, pioneer and leading entrepreneur of robotic exoskeletons. He is a foremost expert in robotics, control sciences, exoskeletons, bioengineering and mechatronics design. <PERSON><PERSON><PERSON><PERSON> shares in this interview details on his second start-up, US Bionics DBA suitX.\n \n \n <PERSON><PERSON> \n<PERSON> received his MS and PhD in Mechanical Engineering from the Massachusetts Institute of Technology (MIT). He has been a Professor at UC Berkeley for over 30 years. He also serves as the Director of the Berkeley Robotics and Human Engineering Laboratory “KAZ LAB.” The lab’s early research focused on enhancing human upper extremity strength, and <PERSON><PERSON><PERSON><PERSON> led his team to successfully develop a new class of intelligent assist devices that are currently marketed worldwide and used by manual laborers in distribution centers and factories worldwide. Dr <PERSON>’s later work focused on the control of human–machine systems specific to human lower extremities. After developing BLEEX, ExoHiker and ExoClimber – three load-carrying exoskeletons – his team at Berkeley created Human Universal Load Carrier. It was the first energetically autonomous, orthotic, lower extremity exoskeleton that allowed its user to carry 100-pound weights in various terrains for an extended period, without becoming physically overwhelmed. The technology was initially licensed to Ekso Bionics and then Lockheed Martin. Kazerooni and his team also developed lower-extremity technology to aid persons who have experienced a stroke, spinal cord injuries or have health conditions that obligate them to use a wheelchair.\n \n \n Originality/value \nIn 2005, Kazerooni founded Ekso Bionics, the very first exoskeleton company in America, which went on to become a publicly owned company in 2014. Ekso, currently marketed by Ekso Bionics, was designed jointly between Ekso Bionics and Berkeley for paraplegics and those with mobility disorders to stand and walk with little physical exertion. In 2011, Austin Whitney, a Berkeley student suffering from lower limb paralysis, walked for commencement in one of Kazerooni’s exoskeletons, “The Austin Exoskeleton Project,” named in honor of Whitney. Kazerooni went on in 2011, to found US Bionics, DBA suitX, a venture capital, industry and government-funded robotics exoskeleton company. suitX’s core technology is focused on the design and manufacturing of affordable industrial and medical exoskeletons to improve the lives of workers and people with gait impairment. suitX has received investment from Wistron (Taiwan), been awarded several US government awards and won two Saint-Gobain NOVA Innovation Awards. suitX has also won the US$1m top prize in the “UAE AI and Robotics for Good” Competition. Its novel health-care exoskeleton Phoenix has recently received FDA approval. Kazerooni has won numerous awards including Discover magazine’s Technological Innovation Award, the McKnight-Land Grant Professorship and has been a recipient of the outstanding ASME Investigator Award. His research was recognized as the most innovative technology of the year in New York Times Magazine . He has served in a variety of leadership roles in the mechanical engineering community and served as editor of two journals: ASME Journal of Dynamics Systems and Control and IEEE Transaction on Mechatronics . Kazerooni has published more than 200 articles to date, delivered over 130 plenary lectures internationally and is the inventors of over 100 patents.", "Keywords": "Robot design;Exoskeletons;Prosthetics;Rehabilitation robots;Man machine interface (MMI);Medical robots", "DOI": "10.1108/IR-06-2021-0112", "PubYear": 2021, "Volume": "48", "Issue": "6", "JournalId": 4481, "JournalTitle": "Industrial Robot: An International Journal", "ISSN": "0143-991X", "EISSN": "1758-5791", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sonoma County, California, USA"}], "References": []}, {"ArticleId": 90143820, "Title": "Toward the Development of Versatile Brain–Computer Interfaces", "Abstract": "Recent advances in artificial intelligence demand an automated framework for the development of versatile brain–computer interface (BCI) systems. In this article, we proposed a novel automated framework that reveals the importance of multidomain features with feature selection to increase the performance of a learning algorithm for motor imagery electroencephalogram task classification on the utility of signal decomposition methods. A framework is explored by investigating several combinations of signal decomposition methods with feature selection techniques. Thus, this article also provides a comprehensive comparison among the aforementioned modalities and validates them with several performance measures, robust ranking, and statistical analysis (<PERSON><PERSON> and Friedman) on public benchmark databases. Among all the combinations, the variational mode decomposition, multidomain features obtained with linear regression, and the cascade-forward neural network provide better classification accuracy results for both subject-dependent and independent BCI systems in comparison with other state-of-the-art methods. <p xmlns:mml=\"http://www.w3.org/1998/Math/MathML\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"> Impact Statement —The brain–computer interface (BCI) is a revolutionary device that utilizes cognitive function explicitly for the interaction of external devices without any motor intervention. BCI systems based on motor imagery have shown efficacy for stroke patient treatment, but poor performance, nonflexible characteristics, and lengthy training sessions have limited their use in clinical practice. The proposed automated framework overcomes these limitations. With the significant improvement of up to 26.1% and 26.4% in comparison with the available literature, the proposed automated framework could offer help to BCI device developers to develop flexible BCI devices and provide interaction for motor-disabled users.</p>", "Keywords": "Artificial intelligence;brain–computer interface (BCI);feature selection (FS) methods;motor imagery (MI);neural networks;signal decomposition (SD) methods", "DOI": "10.1109/TAI.2021.3097307", "PubYear": 2021, "Volume": "2", "Issue": "4", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Automation, Northwestern Polytechnical University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "OPTICAL+: a frequency-based deep learning scheme for recognizing brain wave signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 90143952, "Title": "Where's the germs? The effects of using virtual reality on nursing students' hospital infection prevention during the\n COVID\n ‐19 pandemic", "Abstract": "<p><b>Background</b>:Virtual reality can be a useful substitute to improve nursing education outside of the hospital setting. In the wake of COVID-19, an opportunity to compare a virtual reality activity for nursing students in the traditional classroom and in an online environment took place.</p><p><b>Objectives</b>:The goal of this study was to examine the effects of incorporating a virtual reality activity into the traditional classroom and in an online environment to enhance nursing education.</p><p><b>Methods</b>:Fifty nursing students participated in an exploratory descriptive study. Twenty-five students participated face-to-face while in the traditional classroom setting and 25-students participated in an online environment using a communication platform. Through a virtual reality activity, nursing students successfully created a tour which demonstrated identifying infectious sites in a hospital room setting.</p><p><b>Results and Conclusions</b>:Results of the pre-and post-test showed students' positive perceptions and performances in both traditional classroom and online settings. However, the study revealed a statistical difference between the two group perceptions towards the use of the virtual reality tour creator. Nursing students who were in the traditional classroom group found that the use the virtual reality tour creator was easier.</p><p><b>Implications</b>:Designing a meaningful virtual reality activity has a positive impact of the student learning experience. Students may benefit from a more through orientation to the technology prior to engaging in the activity. A carefully constructed activity can enhance student learning outside of the hospital room.</p><p>© 2021 John Wiley &amp; Sons Ltd.</p>", "Keywords": "COVID‐19;distance;education;learning;nursing;students;virtual reality", "DOI": "10.1111/jcal.12601", "PubYear": 2021, "Volume": "37", "Issue": "6", "JournalId": 4537, "JournalTitle": "Journal of Computer Assisted Learning", "ISSN": "0266-4909", "EISSN": "1365-2729", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "eCampus, San José State University, San José, California, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The Valley Foundation School of Nursing, San José State University, San José, CA, USA"}], "References": [{"Title": "A meta-analysis of virtual reality training programs for social skill development", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "103707", "JournalTitle": "Computers & Education"}, {"Title": "A systematic review of immersive virtual reality applications for higher education: Design elements, lessons learned, and research agenda", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "103778", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 90143954, "Title": "Reliability of vertical-cavity surface-emitting laser arrays with redundancy", "Abstract": "This paper describes theoretical reliability analysis of a system containing n optical ports in which each optical port contains m redundant vertical-cavity surface-emitting lasers. We study the wearout failure statistics, modelled with lognormal distribution, for three different chip-level integration approaches: ( A ) each laser on its own chip, resulting in m·n chips, ( B ) m redundant lasers associated with one channel are on a single chip, resulting in n chips, and ( C ) all m·n lasers integrated on a single chip. We present a model that includes the run-to-run reliability parameter fluctuation and find that the three integration schemes consistently exhibit MTTF( C ) ≥ MTTF( A ) ≥ MTTF( B ) for lognormal distribution shape parameters observed in commercial vertical-cavity surface-emitting lasers. We also provide analytic approximations for the failure statistics for all three integration approaches enabling straightforward calculation of the failure statistics for any redundancy and channel number.", "Keywords": "Vertical-cavity surface-emitting laser arrays ; integration ; reliability ; redundancy ; failure statistics", "DOI": "10.1080/00051144.2021.1969148", "PubYear": 2021, "Volume": "62", "Issue": "3-4", "JournalId": 7651, "JournalTitle": "Automatika", "ISSN": "0005-1144", "EISSN": "1848-3380", "Authors": [{"AuthorId": 1, "Name": "Dubrav<PERSON>", "Affiliation": "Applied Optics Laboratory, Faculty of electrical engineering and computing, University of Zagreb, Zagreb, Croatia"}], "References": []}, {"ArticleId": 90144107, "Title": "Off-line fault detection of logical control networks", "Abstract": "This paper investigates the off-line fault detectability of logical control networks (LCNs) by the method of semi-tensor product. Firstly, two concepts of off-line fault detectability, that is, weak off-line fault detectability and strong off-line detectability, are presented. Secondly, based on a recursive algorithm, the verification matrix for off-line fault detection is proposed to verify the off-line detectability of LCNs. Thirdly, necessary and sufficient conditions are presented to analyse the off-line fault detectability of LCNs. Finally, two illustrative examples show the effectiveness of the obtained new results.", "Keywords": "Logical control network ; off-line fault detection ; semi-tensor product of matrices", "DOI": "10.1080/00207721.2021.1961914", "PubYear": 2022, "Volume": "53", "Issue": "3", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Shandong Normal University, Jinan, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhao", "Affiliation": "School of Mathematics and Statistics, Shandong Normal University, Jinan, People's Republic of China"}, {"AuthorId": 3, "Name": "Haitao Li", "Affiliation": "School of Mathematics and Statistics, Shandong Normal University, Jinan, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, Shandong Normal University, Jinan, People's Republic of China"}], "References": [{"Title": "Stabilizability analysis of logical networks with switching signal and control input", "Authors": "Tao Sun; <PERSON><PERSON><PERSON>; Yongfeng Gao", "PubYear": 2020, "Volume": "36", "Issue": "", "Page": "100875", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}, {"Title": "On feedback invariant subspace of Boolean control networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "12", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Optimal state estimation for finite-field networks with stochastic disturbances", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "414", "Issue": "", "Page": "238", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 90144207, "Title": "IBM: Cost of a Data Breach Report", "Abstract": "This is the 17th edition of the annual ‘Cost of a Data Breach Report’ and things aren't getting any better. The number of breaches climbs every day and the impact from them is becoming more devastating. It's almost as if we're not trying.", "Keywords": "", "DOI": "10.1016/S1361-3723(21)00082-8", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 11477, "JournalTitle": "Computer Fraud & Security", "ISSN": "1361-3723", "EISSN": "1873-7056", "Authors": [], "References": []}, {"ArticleId": 90144210, "Title": "Mind the gap: the cloud security skills shortage", "Abstract": "You don't need to know much about security to know that security skills are in short supply. Demand for cyber security expertise is exploding as cyberthreats become increasingly advanced, persistent and adept at evading detection.", "Keywords": "", "DOI": "10.1016/S1361-3723(21)00084-1", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 11477, "JournalTitle": "Computer Fraud & Security", "ISSN": "1361-3723", "EISSN": "1873-7056", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ExtraHop"}], "References": []}, {"ArticleId": 90144213, "Title": "Editorial", "Abstract": "It's not often I get to write about defrocked priests. And now my chance is here, it turns out not to be an opportunity to indulge in some gleeful yellow journalism. Instead, it's a cautionary tale about the evils of peddling data.", "Keywords": "", "DOI": "10.1016/S1361-3723(21)00079-8", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 11477, "JournalTitle": "Computer Fraud & Security", "ISSN": "1361-3723", "EISSN": "1873-7056", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90144219, "Title": "UAV path planning for backscatter communication with phase cancellation", "Abstract": "Backscatter communication has emerged as a promising technology to realize internet of things (IoT), which can fascinate the data transmission without energy supply and constitute a maintenance-free wireless network. When combining it with unmanned aerial vehicle (UAV), it can gather sensed data in extreme scenarios such as surveying in unreachable areas. When modulated by amplitude shift keying (ASK), the received signal at an UAV is the superposition of backscatter signal of tags and the signal of the RF source. As the UAV moves, the phase difference between the two signals changes. A phase cancellation effect will happen when the UAV enters a blind communication zone, which will deteriorate the performance of communication. In this paper, a two-step moving strategy for the UAV is proposed to eliminate the phase cancellation effect and find the optimal communication spot, so as to improve the reliability and stability of date transmission. The sampling-based UAV path planning algorithm and a median filter is used to adjust the moving direction and filter out environmental noise. By simulation, we can see that it is feasible for the UAV to find global optimal spot and the probability of finding the optimal spot can reach 96%.", "Keywords": "Backscatter communication ; Unmanned aerial vehicle (UAV) ; Phase cancellation ; Path planning", "DOI": "10.1016/j.comcom.2021.08.013", "PubYear": 2021, "Volume": "179", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, Guangzhou 510641, China"}, {"AuthorId": 4, "Name": "Feng Ke", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, Guangzhou 510641, China;Corresponding author"}], "References": []}, {"ArticleId": 90144224, "Title": "SD-WAN revolutionises IoT and edge security", "Abstract": "Network security is an ever-changing environment. In an age of growing reliance on Internet of Things (IoT) devices, multi-cloud environments and connected digital spaces, companies require robust security solutions to assure protection, especially at the enterprise edge, where vast data sets merge.", "Keywords": "", "DOI": "10.1016/S1353-4858(21)00090-8", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 12821, "JournalTitle": "Network Security", "ISSN": "1353-4858", "EISSN": "1872-9371", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Aruba Silver Peak"}], "References": []}, {"ArticleId": 90144242, "Title": "In brief", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1361-3723(21)00083-X", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 11477, "JournalTitle": "Computer Fraud & Security", "ISSN": "1361-3723", "EISSN": "1873-7056", "Authors": [], "References": []}, {"ArticleId": 90144244, "Title": "Western allies lay blame on China for cyber attacks", "Abstract": "Several Western governments, the European Union, NATO and multiple intelligence and law enforcement agencies have joined forces to blame Chinese state actors for the recent attacks against Microsoft Exchange servers.", "Keywords": "", "DOI": "10.1016/S1361-3723(21)00078-6", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 11477, "JournalTitle": "Computer Fraud & Security", "ISSN": "1361-3723", "EISSN": "1873-7056", "Authors": [], "References": []}, {"ArticleId": 90144336, "Title": "MoMA-LoopSampler: a web server to exhaustively sample protein loop conformations", "Abstract": "Summary \n MoMA-LoopSampler is a sampling method that globally explores the conformational space of flexible protein loops. It combines a large structural library of three-residue fragments and a novel reinforcement-learning-based approach to accelerate the sampling process while maintaining diversity. The method generates a set of statistically likely loop states satisfying geometric constraints, and its ability to sample experimentally observed conformations has been demonstrated. This paper presents a web user interface to MoMA-LoopSampler through the illustration of a typical use-case.\n \n \n Availability and implementation \n MoMA-LoopSampler is freely available at: https://moma.laas.fr/applications/LoopSampler/. We recommend users to create an account, but anonymous access is possible. In most cases, jobs are completed within a few minutes. The waiting time may increase depending on the server load, but it very rarely exceeds an hour. For users requiring more intensive use, binaries can be provided upon request.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btab584", "PubYear": 2022, "Volume": "38", "Issue": "2", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, F-31400 Toulouse, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science, James Madison University, Harrisonburg, VA 22181, USA;School of Biology, James Madison University, Harrisonburg, VA 22181, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, F-31400 Toulouse, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, F-31400 Toulouse, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, F-31400 Toulouse, France"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, F-31400 Toulouse, France"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, F-31400 Toulouse, France"}], "References": [{"Title": "A reinforcement-learning-based approach to enhance exhaustive protein loop sampling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1099", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 90144367, "Title": "Extracting complements and substitutes from sales data: a network perspective", "Abstract": "The complementarity and substitutability between products are essential concepts in retail and marketing. Qualitatively, two products are said to be substitutable if a customer can replace one product by the other, while they are complementary if they tend to be bought together. In this article, we take a network perspective to help automatically identify complements and substitutes from sales transaction data. Starting from a bipartite product-purchase network representation, with both transaction nodes and product nodes, we develop appropriate null models to infer significant relations, either complements or substitutes, between products, and design measures based on random walks to quantify their importance. The resulting unipartite networks between products are then analysed with community detection methods, in order to find groups of similar products for the different types of relationships. The results are validated by combining observations from a real-world basket dataset with the existing product hierarchy, as well as a large-scale flavour compound and recipe dataset.", "Keywords": "Product relationships;Network modelling;Role extraction;Sales data;Market basket analysis", "DOI": "10.1140/epjds/s13688-021-00297-4", "PubYear": 2021, "Volume": "10", "Issue": "1", "JournalId": 5454, "JournalTitle": "EPJ Data Science", "ISSN": "", "EISSN": "2193-1127", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematical Institute, University of Oxford, Oxford, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tesco PLC, Tesco House, Shire Park, Welwyn Garden City, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tesco PLC, Tesco House, Shire Park, Welwyn Garden City, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematical Institute, University of Oxford, Oxford, UK"}], "References": []}, {"ArticleId": ********, "Title": "Coordinated allocation production routing problem for mobile supply chains with shared factories", "Abstract": "The mobile supply chain (MSC) is a new development that aims to help companies implement these ideas. In MSCs, production, distribution, and delivery of a product family is performed by a mobile factory (MF), which can be carried by truck and shared among different production sites. In this paper, a real-world application of mobile factories in the chemical industry is studied. Critical production assets (e.g. reactor) are carried by truck and can produce a product family locally. For this purpose, a mixed-integer mathematical model is developed to optimize the logistics costs of MSCs, concerning retailer/job-order allocation, production scheduling, and MF routing. The proposed optimization model is overly complex due to the three NP-hard sub-problems. Therefore, a neighborhood search and an evolutionary algorithm are developed to solve the problem in large-scale data sets. The experimental results show that the proposed algorithms can find a near-optimal solution in a reasonable time.", "Keywords": "Vehicle routing problem ; Mobile supply chain ; Shared factory ; Production routing problem ; Shared economy", "DOI": "10.1016/j.compchemeng.2021.107501", "PubYear": 2021, "Volume": "155", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technische Universität Dresden, Boysen-TU Dresden-Research Training Group, Dresden, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technische Universität Dresden, Institute of Transport and Economics, Dresden, Germany"}], "References": [{"Title": "Reliable blood supply chain network design with facility disruption: A real-world application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103493", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Biomass waste-to-energy supply chain optimization with mobile production modules", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "150", "Issue": "", "Page": "107326", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Production scheduling for the reconfigurable modular pharmaceutical manufacturing processes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "107346", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 90144540, "Title": "Informal management of health and safety risks associated with alarm response by Australian firefighters", "Abstract": "<p>Fire-fighters use informal strategies to manage risks to health and safety during operations. It is not known whether such strategies are used during the high-risk alarm response period. The aim of this study was to determine if informal risk management strategies are employed by Australian firefighters during the alarm response procedure, and if these strategies differ between salaried and retained personnel. Forty-six metropolitan firefighters (all male; mean age 38y<u>+</u>10y; 22 salaried; 24 retained) participated in semi-structured group interviews. A general inductive data analysis approach revealed that firefighters use multiple informal risk management strategies. Some similar themes were reported by both salaried and retained personnel, for example leveraging team dynamics, communication about sleep and fatigue, stress adaptation, informal debriefs, and enhancing physical preparedness. These findings could be used by fire services to tailor risk management approaches during the alarm response period.<b>Practitioner Summary:</b> Identifying informal risk management strategies firefighters use during alarm response will allow their development, refinement and dissemination, and may help other firefighters and emergency service workers to manage these risks. This qualitative study reveals multiple informal strategies that firefighters employ during alarm response to keep themselves and their teammates safe.</p>", "Keywords": "alarm response;emergency services;firefighter;risk management", "DOI": "10.1080/00140139.2021.1967460", "PubYear": 2022, "Volume": "65", "Issue": "2", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Appleton Institute, Central Queensland University, Goodwood, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centre for Physical Activity Research, Deakin University, Burwood, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Appleton Institute, Central Queensland University, Goodwood, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Appleton Institute, Central Queensland University, Goodwood, Australia"}], "References": []}, {"ArticleId": 90144562, "Title": "SMART VISION-<PERSON><PERSON><PERSON><PERSON> REAL-TIME MONITORING FOR FITNESS\nFREAKS WITH A FEEDBACK SYSTEM", "Abstract": "", "Keywords": "", "DOI": "10.26634/jcc.7.2.18136", "PubYear": 2020, "Volume": "7", "Issue": "2", "JournalId": 49860, "JournalTitle": "i-manager’s Journal on Cloud Computing", "ISSN": "2349-6835", "EISSN": "2350-1308", "Authors": [{"AuthorId": 1, "Name": "B. MUTHU SENTHIL", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}, {"AuthorId": 2, "Name": "M. UVAN SHANKAR", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}, {"AuthorId": 3, "Name": "MATHEW TINTU", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}, {"AuthorId": 4, "Name": "G. SARANYA", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}], "References": []}, {"ArticleId": 90144609, "Title": "CLOUD COMPUTING SECURITY AND ITS CHALLENGES", "Abstract": "", "Keywords": "", "DOI": "10.26634/jcc.7.2.17965", "PubYear": 2020, "Volume": "7", "Issue": "2", "JournalId": 49860, "JournalTitle": "i-manager’s Journal on Cloud Computing", "ISSN": "2349-6835", "EISSN": "2350-1308", "Authors": [{"AuthorId": 1, "Name": "A. YASAR ARAFATH", "Affiliation": "Department of Computer Applications, Anna University Regional Campus, Tirunelveli, Tamil Nadu, India."}], "References": []}, {"ArticleId": 90144731, "Title": "Empirical analysis of practitioners' perceptions of test flakiness factors", "Abstract": "<p>Identifying the root causes of test flakiness is one of the challenges faced by practitioners during software testing. In other words, the testing of the software is hampered by test flakiness. Since the research about test flakiness in large-scale software engineering is scarce, the need for an empirical case-study where we can build a common and grounded understanding of the problem as well as relevant remedies that can later be evaluated in a large-scale context is a necessity. This study reports the findings from a multiple-case study. The authors conducted an online survey to investigate and catalogue the root causes of test flakiness and mitigation strategies. We attempted to understand how practitioners perceive test flakiness in closed-source development, such as how they define test flakiness and what practitioners perceive can affect test flakiness. The perceptions of practitioners were compared with the available literature. We investigated whether practitioners' perceptions are reflected in the test artefacts such as what is the relationship between the perceived factors and properties of test artefacts. This study reported 19 factors that are perceived by professionals to affect test flakiness. These perceived factors are categorized as test code , system under test , CI/test infrastructure , and organization-related . The authors concluded that some of the perceived factors in test flakiness in closed-source development are directly related to non-determinism, whereas other perceived factors concern different aspects, for example, lack of good properties of a test case, deviations from the established processes, and ad hoc decisions. Given a data set from investigated cases, the authors concluded that two of the perceived factors (i.e., test case size and test case simplicity) have a strong effect on test flakiness.</p>", "Keywords": "flaky tests;non-deterministic tests;practitioners' perceptions;software testing;test smells", "DOI": "10.1002/stvr.1791", "PubYear": 2021, "Volume": "31", "Issue": "8", "JournalId": 9990, "JournalTitle": "Software Testing, Verification and Reliability", "ISSN": "0960-0833", "EISSN": "1099-1689", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Linköping University, Linköping, Sweden"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Linköping University, Linköping, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Linköping University, Linköping, Sweden"}], "References": []}, {"ArticleId": 90144784, "Title": "A fault tolerant algorithm for integrated coverage and connectivity in wireless sensor networks", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.10040649", "PubYear": 2021, "Volume": "37", "Issue": "4", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90144801, "Title": "Circuit Design for k-Coloring Problem and Its Implementation in Any Dimensional Quantum System", "Abstract": "<p>With the evolution of quantum computing, researchers nowadays tend to incline to find solutions to NP-complete problems using quantum algorithms to gain asymptotic advantage. In this paper, we solve k -coloring problem (NP-complete problem) using <PERSON><PERSON>’s algorithm in any dimensional quantum system or any d -ary quantum system for the first time to the best of our knowledge, where \\(d \\ge 2\\) . Till date, k -coloring problem has been implemented only in binary and ternary quantum systems, hence, we abide to \\(d=2\\) or \\(d=3\\) , that is for binary and ternary quantum systems for comparing our proposed work with the state-of-the-art techniques. Our comparator-based approach has reduced the qubit cost, compared to the state-of-the-art binary quantum systems (<PERSON><PERSON> et al. in IEEE Int Symp Smart Electron Syst 1:17–22, 2020). Further in this paper, with the help of newly proposed ternary comparator, a substantial reduction in quantum gate count for the ternary oracle circuit of the k -coloring problem than the previous approaches has been obtained. Later, this proposed comparator-based approach helps to generalize the implementation of the k -coloring problem in any dimensional quantum system. An end-to-end automated framework has been put forward for implementing the k -coloring problem for any undirected and unweighted graph on any available near-term quantum devices or Noisy Intermediate-Scale Quantum (NISQ) devices or multi-valued quantum simulator, which helps in generalizing our approach.</p>", "Keywords": "k-Coloring problem; <PERSON><PERSON>’s algorithm; NISQ; Multi-valued quantum circuit synthesis", "DOI": "10.1007/s42979-021-00813-3", "PubYear": 2021, "Volume": "2", "Issue": "6", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> School of Information Technology, University of Calcutta, Calcutta, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> School of Information Technology, University of Calcutta, Calcutta, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> School of Information Technology, University of Calcutta, Calcutta, India"}], "References": []}, {"ArticleId": 90144856, "Title": "indelPost: harmonizing ambiguities in simple and complex indel alignments", "Abstract": "Summary \n Small insertions and deletions (indels) in nucleotide sequence may be represented differently between mapping algorithms and variant callers, or in the flanking sequence context. Representational ambiguity is especially profound for complex indels, complicating comparisons between multiple mappings and call sets. Complex indels may additionally suffer from incomplete allele representation, potentially leading to critical misannotation of variant effect. We present indelPost, a Python library that harmonizes these ambiguities for simple and complex indels via realignment and read-based phasing. We demonstrate that indelPost enables accurate analysis of ambiguous data and can derive the correct complex indel alleles from the simple indel predictions provided by standard small variant detectors, with improved performance over a specialized tool for complex indel analysis.\n \n \n Availability and implementation \n indelPost is freely available at: https://github.com/stjude/indelPost.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btab601", "PubYear": 2022, "Volume": "38", "Issue": "2", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computational Biology, St Jude Children’s Research Hospital, Memphis, TN 38105, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computational Biology, St Jude Children’s Research Hospital, Memphis, TN 38105, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computational Biology, St Jude Children’s Research Hospital, Memphis, TN 38105, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computational Biology, St Jude Children’s Research Hospital, Memphis, TN 38105, USA"}], "References": [{"Title": "RNAIndel: discovering somatic coding indels from tumor RNA-Seq data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "5", "Page": "1382", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 90144991, "Title": "Image dataset creation and networks improvement method based on CAD model and edge operator for object detection in the manufacturing industry", "Abstract": "<p>Creating image dataset for object detection is a time-consuming and laborious work, seriously hindering the rapid application of object detection in the industrial manufacturing field. To reduce time and cost of object detection application, a method of image dataset creation and networks improvement based on CAD model and edge extraction operators is proposed. It can quickly generate effective training dataset without any tedious work and make the object detection networks obtain good detection performance. The method consists of three steps: capture-images-automatically, cut-and-paste and networks-improvement. To improve the performance of the detection networks, edge extraction operators are used to obtain the common features of the synthetic images and the real images. These edge extraction operators include Sobel edge, Laplacian edge, Canny edge and adaptive threshold edge, and the experimental results show that the adaptive threshold edge achieves the best effect. In addition, a class-weights is adopted to improve the AP of hard-to-detect parts. Ten mechanical parts of a 3D-printed aero-engine are used to evaluate this method. The results show that the improved networks (yolov5s) trained with the synthetic images can achieve 99.08%, 93.83% and 98.91% of the average recall, average precision and mAP, respectively. Taking into account the time, cost and detection performance, the proposed method is much better than the traditional method and current advanced method. The proposed method is feasible for object detection in many industrial scenarios where CAD models of products can be easily obtained.</p>", "Keywords": "Object detection; Image synthesis; CAD model; Edge operator; Deep learning; Industrial manufacturing", "DOI": "10.1007/s00138-021-01237-y", "PubYear": 2021, "Volume": "32", "Issue": "5", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "Pengzhou Tang", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}], "References": [{"Title": "Deep Learning for Generic Object Detection: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "261", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Smart augmented reality instructional system for mechanical assembly towards worker-centered intelligent manufacturing", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "69", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Detection of powder bed defects in selective laser sintering using convolutional neural network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2485", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Object detection based on semi-supervised domain adaptation for imbalanced domain resources", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Repetitive assembly action recognition based on object detection and pose estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "325", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Task differentiation: Constructing robust branches for precise object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "199", "Issue": "", "Page": "103030", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "NCMS: Towards accurate anchor free object detection through ℓ2 norm calibration and multi-feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "103050", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Detection of 3D bounding boxes of vehicles using perspective transformation for accurate speed measurement", "Authors": "<PERSON>; Milan Ftáčnik", "PubYear": 2020, "Volume": "31", "Issue": "7-8", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Road obstacles positional and dynamic features extraction combining object detection, stereo disparity maps and optical flow data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7-8", "Page": "1", "JournalTitle": "Machine Vision and Applications"}]}, {"ArticleId": 90145081, "Title": "Sparse relation prediction based on hypergraph neural networks in online social networks", "Abstract": "<p>In recent years, online social networks (OSNs) have thoroughly penetrated people’s lives. Since information always flows along with various online relations in OSNs, analysing these relations becomes one of the most fundamental problems in various applications. Unfortunately, limited by the privacy concerns, data availability and the pow-law distributions of most OSNs, we can not observe enough relation links all the time, which is difficult to improve downstream tasks. To address this problem, many studies try to predict potential relations in online social networks via existing pair-wise links. However, when the observable pair-wise links are extremely sparse, most of them fail to learn the smoothness on the networks and make their proposed methods brittle. In light of this, we go beyond pair-wise relations and leverage hypergraphs to learn higher relations in the graphs. A hypergraph allows one hyperedge to connect multiple nodes, which is perfect to include more potential pair-wise links and can guarantee smooth node embeddings for better link prediction performance. In this paper, we aim at predicting potential links in sparsely observed networks. To achieve this goal, we first start from some blurry hyperedges and then proposed a novel hyperedge shrinking method to make the learned hyperedges more hierarchical. This method can learn hypergraph structure automatically from the given sparsely observed links and rely less on manual design. Following this, we further propose a novel hypergraph-based graph neural network to learn potential links in the graph. To address semantic fusion in the heterogeneous networks, we put forward multi-level reconstruction methods to preserve both specific semantics denoted by meta-paths, and high-level semantics denoted by hypergraphs. We compare our method with four state-of-the-art baselines. Extensive evaluations demonstrate that our method can achieve the best linking prediction results, especially when the networks are sparse.</p>", "Keywords": "Hypergraph neural network; Link prediction; Heterogeneous networks", "DOI": "10.1007/s11280-021-00936-w", "PubYear": 2023, "Volume": "26", "Issue": "1", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "Yuanshen <PERSON>uan", "Affiliation": "Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "Xiangguo Sun", "Affiliation": "Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "Yong<PERSON>ao Sun", "Affiliation": "Northeastern University, Shenyang, China"}], "References": [{"Title": "A comparative study on network alignment techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112883", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 90145126, "Title": "Launch of cybercrime-fighting group", "Abstract": "The US Cybersecurity & Infrastructure Security Agency (CISA) is teaming up with a number of major players in the information security industry in a bid to fight cybercrime.", "Keywords": "", "DOI": "10.1016/S1353-4858(21)00084-2", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 12821, "JournalTitle": "Network Security", "ISSN": "1353-4858", "EISSN": "1872-9371", "Authors": [], "References": []}, {"ArticleId": 90145130, "Title": "Investigation of corner diffraction for different materials in vehicular channels at urban street intersection", "Abstract": "This paper investigates diffraction around building corners in non line-of-sight vehicle-to-vehicle scenario at a typical urban street intersection. Diffraction from building corner depends on several geometrical parameters. Mobile vehicles moving towards a street intersection cause rapid changes in these geometrical parameters. The effect of varying geometrical parameters along with dependence of reflection coefficient on material constitutive parameters, angle of incidence and surface roughness causes the Diffraction coefficient to change differently for different building materials. The combined effect of varying Diffraction coefficients for all the corners in a typical urban street intersection is investigated. The results show that total diffracted power from materials with rough surfaces may become comparable to power from perfect electrical conductor near the street intersection.", "Keywords": "Radio propagation ; Uniform theory of diffraction ; V2V communications", "DOI": "10.1016/j.phycom.2021.101445", "PubYear": 2021, "Volume": "48", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National University of Sciences and Technology (NUST), Islamabad, Pakistan"}], "References": []}, {"ArticleId": 90145134, "Title": "Long term trend in aerosol direct radiative effects over Indian Ocean region from multi- satellite observations", "Abstract": "Long-term (2000–2020) trend in aerosol optical depth (AOD) and the associated radiative effect over Indian Ocean region (Arabian Sea and Bay of Bengal) during winter time south Asian outflow is investigated using multi-satellite data. Analysis revealed a statistically significant increasing trend of ~0.01 year<sup>−1</sup> over the study regions. This study revealed that aerosols are confined within 2 km from ocean surface, and the trend in columnar AOD is significantly modulated by aerosol loading at higher altitudes. Although there has been no significant change in aerosol direct radiative forcing efficiency, the significant enhancement in AOD over the study region has induced an increase in cooling at the top of the atmosphere by ~0.1 to 0.2 W m<sup>−2</sup> year<sup>−1</sup>. Acknowledgements This study has been carried out as part of the Aerosol Radiative Forcing over India (ARFI) project of ISRO-GBP. Dr <PERSON><PERSON> and Dr. <PERSON> acknowledges the Indian Space Research Organisation (ISRO) for the Research Associateship. Authors acknowledge principal investigators and science team of all satellite missions for the necessary data used in this study. In this study trajectories are generated out using HYSPLIT model and further classified to clusters based on a python-based tool kit for HYSPLIT trajectory model (PySPLIT, Warner ( 2018 Warner, M. S. C. 2018 . “ Introduction to PySPLIT: A Python Toolkit for NOAA ARL’s HYSPLIT Model .” Computing in Science and Engineering . doi: 10.1109/MCSE.2017.3301549 . [Crossref] , [Web of Science ®] , [Google Scholar] )). Authors acknowledge the anonymous reviewers for their insightful comments and suggestions. Author contributions SJ analysed the data and wrote the manuscript. LNB analysed and wrote the section on vertically resolved AOD trend. SSB reviewed and edited the manuscript. Data and code availability MODIS and SUOMI-NPP data are obtained from https://ladsweb.modaps.eosdis.nasa.gov/ . SeaWiFS, TOMS and OMI are available in https://giovanni.gsfc.nasa.gov/giovanni/ . AVHRR data can be obtained from https://www.ncdc.noaa.gov/cdr/atmospheric/avhrr-aerosol-optical-thickness . CERES sensor data can be obtained from https://ceres.larc.nasa.gov/ . The CALIPSO data is provided by LARC Atmospheric Sciences Data Center through their website at http://eosweb.larc.nasa.gov/.Python scripts used for analysis can be obtained from the corresponding author upon request.", "Keywords": "", "DOI": "10.1080/2150704X.2021.1957509", "PubYear": 2021, "Volume": "12", "Issue": "10", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Space Physics Laboratory, <PERSON><PERSON><PERSON> Space Centre, Thiruvananthapuram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Space Physics Laboratory, <PERSON><PERSON><PERSON> Space Centre, Thiruvananthapuram, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Space Physics Laboratory, <PERSON><PERSON><PERSON> Space Centre, Thiruvananthapuram, India"}], "References": []}, {"ArticleId": 90145242, "Title": "Research on enterprise knowledge service based on semantic reasoning and data fusion", "Abstract": "<p>In the era of big data, the field of enterprise risk is facing considerable challenges brought by massive multisource heterogeneous information sources. In view of the proliferation of multisource and heterogeneous enterprise risk information, insufficient knowledge fusion capabilities, and the low level of intelligence in risk management, this article explores the application process of enterprise knowledge service models for rapid responses to risk incidents from the perspective of semantic reasoning and data fusion and clarifies the elements of the knowledge service model in the field of risk management. Based on risk data, risk decision making as the standard, risk events as the driving force, and knowledge graph analysis methods as the power, the risk domain knowledge service process is decomposed into three stages: prewarning, in-event response, and postevent summary. These stages are combined with the empirical knowledge of risk event handling to construct a three-level knowledge service model of risk domain knowledge acquisition-organization-application. This model introduces the semantic reasoning and data fusion method to express, organize, and integrate the knowledge needs of different stages of risk events; provide enterprise managers with risk management knowledge service solutions; and provide new growth points for the innovation of interdisciplinary knowledge service theory.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2021.</p>", "Keywords": "Enterprise knowledge service;Knowledge fusion;Risk management;Semantic reasoning", "DOI": "10.1007/s00521-021-06382-z", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Management, Jiangxi University of Finance and Economics, Nanchang, 30013 China. ;Institute of Information Resources Management, Jiangxi University of Finance and Economics, Nanchang, 330013 China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Management, Jiangxi University of Finance and Economics, Nanchang, 30013 China. ;Institute of Information Resources Management, Jiangxi University of Finance and Economics, Nanchang, 330013 China."}], "References": [{"Title": "Domain knowledge graph-based research progress of knowledge representation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "681", "JournalTitle": "Neural Computing and Applications"}, {"Title": "KGAnet: a knowledge graph attention network for enhancing natural language inference", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "14963", "JournalTitle": "Neural Computing and Applications"}, {"Title": "GRL: Knowledge graph completion with GAN-based reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "209", "Issue": "", "Page": "106421", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 90145478, "Title": "<PERSON> hashing with maximum mean discrepancy quantization for image retrieval", "Abstract": "Hashing has been a promising technology for fast nearest neighbor retrieval in large-scale datasets due to the low storage cost and fast retrieval speed. Most existing deep hashing approaches learn compact hash codes through pair-based deep metric learning such as the triplet loss. However, these methods often consider that the intra-class and inter-class similarity make the same contribution, and consequently it is difficult to assign larger weights for informative samples during the training procedure. Furthermore, only imposing relative distance constraint increases the possibility of being clustered with larger average intra-class distance for similar pairs, which is harmful to learning a high separability Hamming space. To tackle the issues, we put forward deep Weibull hashing with maximum mean discrepancy quantization (DWH), which jointly performs neighborhood structure optimization and error-minimizing quantization to learn high-quality hash codes in a unified framework. Specifically, DWH learns the desired neighborhood structure in conjunction with a flexible pair similarity optimization strategy and a Weibull distribution-based constraint between anchors and their neighbors in Hamming space. More importantly, we design a maximum mean discrepancy quantization objective function to preserve the pairwise similarity when performing binary quantization. Besides, a class-level loss is introduced to mine the semantic structural information of images by using supervision information. The encouraging experimental results on various benchmark datasets demonstrate the efficacy of the proposed DWH.", "Keywords": "Image retrieval ; Hamming space ; Hashing ; Maximum mean discrepancy", "DOI": "10.1016/j.neucom.2021.08.090", "PubYear": 2021, "Volume": "464", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Anhui University, Anhui, Hefei 230601, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information Engineering, Anhui University, Anhui, Hefei 230601, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electronics and Information Engineering, Anhui University, Anhui, Hefei 230601, China;Corresponding author"}], "References": [{"Title": "Deep semantic cross modal hashing with correlation alignment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "240", "JournalTitle": "Neurocomputing"}, {"Title": "Anchor-Based Self-Ensembling for Semi-Supervised Deep Pairwise Hashing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "Page": "2307", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "A General Framework for Deep Supervised Discrete Hashing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "Page": "2204", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "DSRPH: Deep semantic-aware ranking preserving hashing for efficient multi-label image retrieval", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "539", "Issue": "", "Page": "145", "JournalTitle": "Information Sciences"}, {"Title": "Deep Hashing with Hash-Consistent Large Margin Proxy Embeddings", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "2", "Page": "419", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 90145513, "Title": "Transmission Comparison for Cooperative Robotic Applications", "Abstract": "<p>The development of powered assistive devices that integrate exoskeletal motors and muscle activation for gait restoration benefits from actuators with low backdrive torque. Such an approach enables motors to assist as needed while maximizing the joint torque muscles, contributing to movement, and facilitating ballistic motions instead of overcoming passive dynamics. Two electromechanical actuators were developed to determine the effect of two candidate transmission implementations for an exoskeletal joint. To differentiate the transmission effects, the devices utilized the same motor and similar gearing. One actuator included a commercially available harmonic drive transmission while the other incorporated a custom designed two-stage planetary transmission. Passive resistance and mechanical efficiency were determined based on isometric torque and passive resistance. The planetary-based actuator outperformed the harmonic-based actuator in all tests and would be more suitable for hybrid exoskeletons.</p>", "Keywords": "hybrid exoskeleton; joint actuators; passive resistance hybrid exoskeleton ; joint actuators ; passive resistance", "DOI": "10.3390/act10090203", "PubYear": 2021, "Volume": "10", "Issue": "9", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Case Western Reserve University, Cleveland, OH 44106, USA↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Case Western Reserve University, Cleveland, OH 44106, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Case Western Reserve University, Cleveland, OH 44106, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, Case Western Reserve University, Cleveland, OH 44106, USA↑APT Center, Louis Stokes VA Medical Center, Cleveland, OH 44106, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, Case Western Reserve University, Cleveland, OH 44106, USA↑Department of Physical Medicine and Rehabilitation, Metrohealth Medical Center, Cleveland, OH 44106, USA"}], "References": [{"Title": "Design and Evaluation of a Pediatric Lower-Limb Exoskeleton Joint Actuator", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "138", "JournalTitle": "Actuators"}, {"Title": "A Muscle-First, Electromechanical Hybrid Gait Restoration System in People With Spinal Cord Injury", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "645588", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 90145707, "Title": "Above-ground biomass change estimation using national forest inventory data with Sentinel-2 and Landsat", "Abstract": "This study aimed at estimating total forest above-ground net change (ΔAGB; Gg) over five years (2014–2019) based on model-assisted estimation utilizing freely available satellite imagery. The study was conducted for a boreal forest area (approx. 1.4 Mha) in Norway where bi-temporal national forest inventory (NFI), Sentinel-2, and Landsat data were available. Biomass change was modelled based on a direct approach. The precision of estimates using only the NFI data in a basic expansion estimator was compared to four different alternative model-assisted estimates using 1) Sentinel-2 or Landsat data, and 2) using bi- or uni-temporal remotely sensed data. We found that spaceborne optical data improved the precision of the purely field-based estimates by a factor of up to three. The most precise estimates were found for the model-assisted estimation using bi-temporal Sentinel-2 (standard error; SE = 1.7 Gg). However, the decrease in precision when using Landsat data was small (SE = 1.92 Gg). We also found that ΔAGB could be precisely estimated when remotely sensed data were available only at the end of the monitoring period. We conclude that satellite optical data can considerably improve ΔAGB estimates, when repeated and coincident field data are available. The free availability, global coverage, frequent update, and long-term time horizon make data from programs such as Sentinel-2 and Landsat a valuable data source for consistent and durable monitoring of forest carbon dynamics.", "Keywords": "Forest carbon dynamics ; Satellite imagery ; greenhouse-gas reporting ; model-assisted estimation", "DOI": "10.1016/j.rse.2021.112644", "PubYear": 2021, "Volume": "265", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Norwegian Institute for Bioeconomy Research (NIBIO), Division of Forest and Forest Resources, National Forest Inventory, Høgskoleveien 8, 1433 Ås, Norway;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Norwegian Institute for Bioeconomy Research (NIBIO), Division of Forest and Forest Resources, National Forest Inventory, Høgskoleveien 8, 1433 Ås, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Norwegian Institute for Bioeconomy Research (NIBIO), Division of Forest and Forest Resources, National Forest Inventory, Høgskoleveien 8, 1433 Ås, Norway"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Norwegian Institute for Bioeconomy Research (NIBIO), Division of Forest and Forest Resources, National Forest Inventory, Høgskoleveien 8, 1433 Ås, Norway"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Norwegian Mapping Authority (Kartverket), Land Mapping Division, P.O. Box 600, <PERSON><PERSON><PERSON>, 3507 Hønefoss, Norway"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Norwegian Institute for Bioeconomy Research (NIBIO), Division of Forest and Forest Resources, National Forest Inventory, Høgskoleveien 8, 1433 Ås, Norway"}], "References": [{"Title": "Modelling above-ground biomass stock over Norway using national forest inventory data with ArcticDEM and Sentinel-2 data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "236", "Issue": "", "Page": "111501", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 90145820, "Title": "A provably secure ECC-based access and handover authentication protocol for space information networks", "Abstract": "At present, space information networks are widely used in various fields and gradually attract more and more users to access. However, because the wireless channel in space information networks is public, it is extremely vulnerable to attacks such as eavesdropping, replay and impersonation attacks, which may lead to the leakage of privacy and secret information. In recent years, many authentication schemes have been proposed to prevent malicious nodes from accessing and stealing resources, but most of these schemes either do not take into account the security of satellite relay nodes or the handover problems caused by dynamic topology. To solve the above-mentioned problems, we propose a novel ECC-based authentication scheme, which not only confirms the validity of the satellite node when user and ground station authenticate each other, but also designs the handover schemes based on two possible signal handover scenarios. For the handover involving the ground station, we also design an efficient multi-user batch handover authentication scheme, which can greatly reduce the computation overhead and delay. In the security analysis, we prove the security of our protocol based on the ROM model and AVISPA formal verification tool, and verify that our protocol can meet the security attributes and resist various forms of attacks through informal security analysis. Finally, security and performance analysis results demonstrate that our protocol is more suitable for user access and handover in space information networks while ensuring higher security requirements.", "Keywords": "Space information network ; Authentication ; Handover ; ECC ; ROM ; AVISPA", "DOI": "10.1016/j.jnca.2021.103183", "PubYear": 2021, "Volume": "193", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Security and Privacy in Intelligent Transportation, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Beijing Key Laboratory of Security and Privacy in Intelligent Transportation, Beijing Jiaotong University, Beijing, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Spacecraft System Engineering, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Security and Privacy in Intelligent Transportation, Beijing Jiaotong University, Beijing, China"}], "References": []}, {"ArticleId": 90145862, "Title": "Trend Prediction of Event Popularity from Microblogs", "Abstract": "<p>Owing to rapid development of the Internet and the rise of the big data era, microblog has become the main means for people to spread and obtain information. If people can accurately predict the development trend of a microblog event, it will be of great significance for the government to carry out public relations activities on network event supervision and guide the development of microblog event reasonably for network crisis. This paper presents effective solutions to deal with trend prediction of microblog events’ popularity. Firstly, by selecting the influence factors and quantifying the weight of each factor with an information entropy algorithm, the microblog event popularity is modeled. Secondly, the singular spectrum analysis is carried out to decompose and reconstruct the time series of the popularity of microblog event. Then, the box chart method is used to divide the popularity of microblog event into various trend spaces. In addition, this paper exploits the Bi-LSTM model to deal with trend prediction with a sequence to label model. Finally, the comparative experimental analysis is carried out on two real data sets crawled from Sina Weibo platform. Compared to three comparative methods, the experimental results show that our proposal improves F1-score by up to 39%.</p>", "Keywords": "popularity of microblog event; information entropy model; singular spectrum analysis; Bi-LSTM popularity of microblog event ; information entropy model ; singular spectrum analysis ; Bi-LSTM", "DOI": "10.3390/fi13090220", "PubYear": 2021, "Volume": "13", "Issue": "9", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Southwest University of Science and Technology, Mianyang 621010, China ↑ Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Southwest University of Science and Technology, Mianyang 621010, China"}], "References": [{"Title": "Constructing a PM2.5 concentration prediction model by combining auto-encoder with Bi-LSTM neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "124", "Issue": "", "Page": "104600", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Deep learning based emotion analysis of microblog texts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "Near real-time topic-driven rumor detection in source microblogs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "207", "Issue": "", "Page": "106391", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Speech emotion recognition model based on Bi-GRU and Focal Loss", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "358", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Microblog sentiment analysis via embedding social contexts into an attentive LSTM", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "104048", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Speech emotion recognition using recurrent neural networks with directional self-attention", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "114683", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep sequence to sequence Bi-LSTM neural networks for day-ahead peak load forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "175", "Issue": "", "Page": "114844", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Speech neuromuscular decoding based on spectrogram images using conformal predictors with Bi-LSTM", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "451", "Issue": "", "Page": "25", "JournalTitle": "Neurocomputing"}, {"Title": "Detecting evolutionary stages of events on social media: A graph-kernel-based approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "123", "Issue": "", "Page": "219", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 90145924, "Title": "metID: an R package for automatable compound annotation for LC−MS-based data", "Abstract": "Summary \n Accurate and efficient compound annotation is a long-standing challenge for LC–MS-based data (e.g. untargeted metabolomics and exposomics). Substantial efforts have been devoted to overcoming this obstacle, whereas current tools are limited by the sources of spectral information used (in-house and public databases) and are not automated and streamlined. Therefore, we developed metID, an R package that combines information from all major databases for comprehensive and streamlined compound annotation. metID is a flexible, simple and powerful tool that can be installed on all platforms, allowing the compound annotation process to be fully automatic and reproducible. A detailed tutorial and a case study are provided in Supplementary Materials.\n \n \n Availability and implementation \n https://jaspershen.github.io/metID.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btab583", "PubYear": 2022, "Volume": "38", "Issue": "2", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Genetics, Stanford University School of Medicine, Stanford, CA 94304, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Genetics, Stanford University School of Medicine, Stanford, CA 94304, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Genetics, Stanford University School of Medicine, Stanford, CA 94304, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Genetics, Stanford University School of Medicine, Stanford, CA 94304, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Genetics, Stanford University School of Medicine, Stanford, CA 94304, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interdisciplinary Research Center on Biology and Chemistry, Shanghai Institute of Organic Chemistry, Chinese Academy of Sciences, Shanghai 200032, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Genetics, Stanford University School of Medicine, Stanford, CA 94304, USA"}], "References": []}, {"ArticleId": 90145954, "Title": "Medical Image Enhancement Algorithm for B-Mode Ultrasound Image Analysis of Neonatal Respiratory Distress Syndrome", "Abstract": "<p> The aim of this paper was to explore the imaging characteristics of lung B-ultrasound images under image enhancement algorithm for neonatal respiratory distress syndrome (NRDS) and the therapeutic effect of vitamin A (VA) on NDRS. 30 newborn babies with NRDS in hospital were selected as the experimental group and 30 healthy newborn babies were selected as the control group. All of them received the lung B-ultrasound based on the image enhancement algorithm under the partial differential equation (PDE). The subjects of the control group were given formula milk every day. On the basis of formula milk, the subjects of the experimental group took VA soft capsule orally once a day at noon. Oxidative stress indexes, blood gas indexes, and mechanical ventilation parameters were recorded in the subjects of the two groups. The results of 30 newborn babies with NRDS in the experimental group indicated that the images of 12 cases presented the disappearance of line A and dense or discontinuous distribution of line B; the abnormal pleural line was found in the images of 8 cases; there was lung consolidation under the pleural line, patchy hypoecho, and point-strip hyperecho in the images of 5 cases; the images of 2 cases showed alveolar edema and alveolar interstitial syndrome. Compared with before treatment, the arterial partial pressure of oxygen (PaO2) of subjects in the experimental group (87.61 ± 5.79) increased dramatically, but their arterial partial pressure of carbon dioxide (PaCO2) decreased sharply after treatment (40.07 ± 6.12), with statistically huge differences ( P 0.05 ). The respiratory rate (RR) and positive end expiratory pressure (PEEP) after treatment were greatly less than those before treatment of subjects in the experimental group ( P 0.05 ), and the difference was statistically obvious ( P 0.05 ). By comparing with before treatment, malondialdehyde (MDA) of subjects in the experimental group decreased after treatment while superoxide dismutase (SOD) and glutathione peroxidase (GSH-Px) increased considerably, with statistically marked differences ( P 0.05 ). In conclusion, lung B-ultrasound based on partial image enhancement algorithm could clearly display the imaging characteristics of NRDS, such as pleural abnormalities and alveolar stroma. Besides, VA could effectively improve the neonatal shortness of breath, which had a good clinical effect. </p>", "Keywords": "", "DOI": "10.1155/2021/8552537", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>ao", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>u", "Affiliation": "Department of Neonatology, Shijiazhuang No. 4 Hospital, 206 Zhongshan Dong Lu, Shijiazhuang City 050000, Hebei Province, China"}], "References": []}, {"ArticleId": 90146017, "Title": "Improving security in the manufacturing industry", "Abstract": "The global pandemic had business leaders navigating months of extreme uncertainty, and critical security vulnerabilities – known and unknown – were brought to light. While manufacturers are not necessarily easier targets than peers in other industry sectors, it is clear that modern hackers recognise the growing value associated with attacking industrial organisations.", "Keywords": "", "DOI": "10.1016/S1353-4858(21)00091-X", "PubYear": 2021, "Volume": "2021", "Issue": "8", "JournalId": 12821, "JournalTitle": "Network Security", "ISSN": "1353-4858", "EISSN": "1872-9371", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Plex Systems"}], "References": []}, {"ArticleId": 90146018, "Title": "Designing with biodiversity data: connections among design, materials, and technology", "Abstract": "Data visualisation plays an integral role in the communication of complex data between expert and non-expert audiences. However, heretofore, large uncertainties remain concerning how people understand and interact with massive amounts of data. In this paper, we describe the design and evaluation of a series of interactive data physicalising installations, aim to evaluate the potential influence of technology upon traditional comprehension of material items, its connection to meaning and value, and how technology that allows for an extension of this thinking builds an emotional connection between audiences and the intangible object, ‘data’. The design of prototypes was driven by data of the three least appearing species in Scotland. Analysis of 60 audience members’ responses reveals the positive design potential of further exploring innovative design methods to engage people with data. Likewise, the results provide empirical evidence regarding hands-on experience with integrative data visualisation in a realistic scenario and suggest that inventive forms of visualisation could potentially trigger people’s emotional and memorial reactions, which may affect their decision making at an unconscious level", "Keywords": "Data visualisation; Human–Data Interaction; Design with data", "DOI": "10.1007/s41870-021-00779-8", "PubYear": 2021, "Volume": "13", "Issue": "5", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Design, University of Leeds, Leeds, UK;Edinburgh College of Art, The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Design, University of Leeds, Leeds, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Design, University of Leeds, Leeds, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Language, Cultures and Societies, University of Leeds, Leeds, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Forest Science, University of Eastern Finland, Kuopio, Finland"}, {"AuthorId": 6, "Name": "Zongcheng Zhu", "Affiliation": "Edinburgh College of Art, The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 7, "Name": "Hongfu Li", "Affiliation": "School of Informatics, The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Edinburgh College of Art, The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Edinburgh College of Art, The University of Edinburgh, Edinburgh, UK"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Informatics, The University of Edinburgh, Edinburgh, UK"}], "References": [{"Title": "Understanding human-data interaction: Literature review and recommendations for design", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "13", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 90146037, "Title": "Learning semantic priors for texture-realistic sketch-to-image synthesis", "Abstract": "Sketch-to-image synthesis is a challenging task in the field of computer vision that generates photo-realistic images from given sketches. Existing methods of this kind are unable to discover the inherent semantic information contained in an image and use it to guide the synthesis process, substantially reduce their capacity to generate photo-realistic images. Accordingly, in this paper, we propose a novel framework that explores and leverages semantic information to generate realistic textures in synthesized images for this task. More specifically, the segmentation maps generation network is designed to learn the relationships between sketches and segmentation maps in order to obtain the semantic segmentation maps from the sketches. Taking semantic segmentation maps as the condition, a feature-wise affine transformation is then executed to change the feature maps of intermediate layers in the network, which can efficiently generate the texture required to synthesize more photo-realistic images. Extensive experiments demonstrate that when compared to other state-of-the-art sketch-to-image synthesis methods, our approach can not only synthesize images with significantly superior visual quality but is also able to achieve better results on quantitative metrics.", "Keywords": "Image-to-image translation ; Deep learning ; GANs ; Sketch-based image synthesis", "DOI": "10.1016/j.neucom.2021.08.085", "PubYear": 2021, "Volume": "464", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xidian University, Xi’an 710071, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronic Engineering, Xidian University, Xi’an 710071, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xidian University, Xi’an 710071, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Tencent AI Lab, Shenzhen 518057, China"}, {"AuthorId": 5, "Name": "Dacheng Tao", "Affiliation": "UBTECH Sydney Artificial Intelligence Centre and the School of Information Technologies, the Faculty of Engineering and Information Technologies, The University of Sydney, Darlington, NSW 2008, Australia"}], "References": [{"Title": "Image deblurring using tri-segment intensity prior", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "398", "Issue": "", "Page": "265", "JournalTitle": "Neurocomputing"}, {"Title": "Lightweight image super-resolution with feature enhancement residual network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "404", "Issue": "", "Page": "50", "JournalTitle": "Neurocomputing"}, {"Title": "Learning cascaded convolutional networks for blind single image super-resolution", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "371", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 90146038, "Title": "How to elicit and cease herding behaviour? On the effectiveness of a warning message as a debiasing decision support system", "Abstract": "Behavioural economics has been argued to be a productive basis for decision support system (DSS) research. Whereas traditional economics assumes that individuals make decisions independently of others, behavioural economists have shown that humans tend to follow the crowd in their decisions (i.e., exhibit herding bias). However, the literature is silent on how convincing the information on the decisions of the crowd needs to be to elicit herding bias and on whether herding can be reduced (i.e., debiased) by presenting a warning message. This paper addresses both questions in the contexts of financial decisions that were guided by two DSSs in the form of simulation tools. In particular, we conduct a randomised controlled trial with 768 respondents randomly assigned to peer information. The results indicate that the intervention successfully elicited herding bias and that herding occurs when respondents are informed that at least 50% of other people made a particular decision. The results further show that a DSS in the form of a warning message is not sufficient to debias herding. In conclusion, these findings showed that individuals are easily influenced by erroneous peer information and that this effect is robust against debiasing using a warning message. Hence, DSS developers need to consider more intense debiasing strategies to overcome herding.", "Keywords": "Behavioural economics ; Cognitive bias ; Debiasing ; Financial decision making ; Herding bias ; Randomised controlled trial", "DOI": "10.1016/j.dss.2021.113652", "PubYear": 2022, "Volume": "152", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Antwerp School of Education, Faculty of Social Sciences, University of Antwerp, Belgium;Leuven Economics of Education Research, Faculty of Economics and Business, KU Leuven, Belgium;Corresponding author at: Antwerp School of Education, Faculty of Social Sciences, University of Antwerp, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Leuven Economics of Education Research, Faculty of Economics and Business, KU Leuven, Belgium"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Antwerp School of Education, Faculty of Social Sciences, University of Antwerp, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Leuven Economics of Education Research, Faculty of Economics and Business, KU Leuven, Belgium;UNU-MERIT, Maastricht University, the Netherlands"}], "References": [{"Title": "The variable precision method for elicitation of probability weighting functions", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "", "Page": "113166", "JournalTitle": "Decision Support Systems"}, {"Title": "Preferences stability: A measure of preferences changes over time", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "113169", "JournalTitle": "Decision Support Systems"}, {"Title": "Interactive and revisable decision-support: doing more harm than good?", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "4", "Page": "845", "JournalTitle": "Behaviour & Information Technology"}]}, {"ArticleId": 90146066, "Title": "Harmonic biasing in a double-sided comb-drive resonator, for resolving feed-through issues in low-power driving", "Abstract": "We demonstrate a novel technique for operating a differentially-driven and differentially-sensed double-sided comb-drive resonator. The resonator is driven by application of two out-of-phase ac signals to the drive stators, while the rotor is subjected to a pure harmonic signal at the same frequency as of the ac driving signals. In this mode of operation, no dc bias is necessary. This strategy results in two frequency-mixing operations, such that a clear response can be sensed at the 3rd harmonic of the drive signal. Besides the responses in the 1st and 3rd harmonics of the drive frequency, no other higher-harmonic components are predicted from the analysis or observed in the measurements. With harmonic biasing, the resonator is insensitive to both feed-through currents that result from direct capacitive cross-coupling of sense and drive pads, and to feed-through currents that result from imbalanced sense ports due to packaging and setup. We show that a clear response can be achieved for low-power drive signals, whereas if the system were driven as a classic resonator with a dc bias on the rotor, the response would have been overshadowed by feed-through. We also demonstrate that the phase of the sensed response can be modified by varying the phase of the harmonic bias on the rotor.", "Keywords": "Electrostatic comb-drives ; Feed-through ; Electrostatic resonators ; Frequency mixing", "DOI": "10.1016/j.sna.2021.113031", "PubYear": 2021, "Volume": "332", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Technion – Israel Institute of Technology, Israel"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technion – Israel Institute of Technology, Israel;Corresponding author"}], "References": []}, {"ArticleId": 90146077, "Title": "Junction toehold: A novel approach for flexible DNA strand displacement and bioassay", "Abstract": "Toehold-mediated DNA strand displacement is a simple yet powerful approach to design various DNA devices, and also receives more and more attention for bio-assay applications. It is critical to activate toehold in a programmable and controllable way to regulate DNA strand displacement. Thus it is desirable to develop new toehold to promote the programmability and versatility of DNA devices, and enrich its bio-assay applications. Here a novel junction toehold is reported with low signal leakage based on a junction motif. The DNA strand displacement process is cooperatively and sequentially activated by two inputs with independent oligonucleotide sequences. The rate constants of individual reaction steps were characterized in detail, and the kinetics can be flexibly tuned independently by multi-parameters. Because of the diverse reaction sites in the junction toehold, we are able to construct various DNA devices with advanced architecture including a concatenated DNA circuit, a parallel-activated device and so on. Moreover, the activity of ribonuclease or endonuclease was sensitively and selectively assayed via enzyme-activated strand displacement method, demonstrating the power of junction toehold for strand displacement regulation and bio-assay.", "Keywords": "Toehold ; Junction motif ; Rate constants ; DNA devices ; Nucleases activity", "DOI": "10.1016/j.snb.2021.130645", "PubYear": 2021, "Volume": "347", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, China"}, {"AuthorId": 3, "Name": "Donghua Chen", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Henan Key Laboratory of Organic Functional Molecule and Drug Innovation, Collaborative Innovation Center of Henan Province for Green Manufacturing of Fine Chemicals, Key Laboratory of Green Chemical Media and Reactions, Ministry of Education, School of Chemistry and Chemical Engineering, Henan Normal University, Xinxiang, Henan 453007, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, China"}, {"AuthorId": 8, "Name": "Hanfen Cai", "Affiliation": "Key Laboratory of Environmentally Friendly Chemistry and Applications of Ministry of Education, College of Chemistry, Xiangtan University, Xiangtan 410005, China"}], "References": [{"Title": "Multiple signal amplification via coupling DNAzyme with strand displacement reaction for sensitive colorimetric analysis of MUC1", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "313", "Issue": "", "Page": "128046", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 90146109, "Title": "Building damage assessment for rapid disaster response with a deep object-based semantic change detection framework: From natural disasters to man-made disasters", "Abstract": "Sudden-onset natural and man-made disasters represent a threat to the safety of human life and property. Rapid and accurate building damage assessment using bitemporal high spatial resolution (HSR) remote sensing images can quickly and safely provide us with spatial distribution information and statistics of the damage degree to assist with humanitarian assistance and disaster response. For building damage assessment, strong feature representation and semantic consistency are the keys to obtaining a high accuracy. However, the conventional object-based image analysis (OBIA) framework using a patch-based convolutional neural network (CNN) can guarantee semantic consistency, but with weak feature representation, while the Siamese fully convolutional network approach has strong feature representation capabilities but is semantically inconsistent. In this paper, we propose a deep object-based semantic change detection framework, called ChangeOS, for building damage assessment. To seamlessly integrate OBIA and deep learning, we adopt a deep object localization network to generate accurate building objects, in place of the superpixel segmentation commonly used in the conventional OBIA framework. Furthermore, the deep object localization network and deep damage classification network are integrated into a unified semantic change detection network for end-to-end building damage assessment. This also provides deep object features that can supply an object prior to the deep damage classification network for more consistent semantic feature representation. Object-based post-processing is adopted to further guarantee the semantic consistency of each object. The experimental results obtained on a global scale dataset including 19 natural disaster events and two local scale datasets including the Beirut port explosion event and the Bata military barracks explosion event show that ChangeOS is superior to the currently published methods in speed and accuracy, and has a superior generalization ability for man-made disasters.", "Keywords": "Building damage assessment ; Change detection ; Disaster response ; Deep learning ; Remote sensing ; OBIA", "DOI": "10.1016/j.rse.2021.112636", "PubYear": 2021, "Volume": "265", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University, Wuhan 430074, China;Hubei Provincial Engineering Research Center of Natural Resources Remote Sensing Monitoring, Wuhan University, Wuhan 430079, China;Corresponding author at: State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University, Wuhan 430074, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University, Wuhan 430074, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University, Wuhan 430074, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University, Wuhan 430074, China;Hubei Provincial Engineering Research Center of Natural Resources Remote Sensing Monitoring, Wuhan University, Wuhan 430079, China"}], "References": [{"Title": "Scale Sequence Joint Deep Learning (SS-JDL) for land use and land cover classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111593", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A review on synthetic aperture radar-based building damage assessment in disasters", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111693", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Change detection using deep learning approach with object-based image analysis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "256", "Issue": "", "Page": "112308", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 90146240, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0957-4174(21)01154-4", "PubYear": 2021, "Volume": "182", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [], "References": []}, {"ArticleId": 90146336, "Title": "A framework for semi-automatically identifying fully occluded objects in 3D models: Towards comprehensive construction design review in virtual reality", "Abstract": "Virtual Reality (VR)-based construction design review applications have shown potential to enhance user performance in many research projects and experiments. Currently, visualizing occluded objects in VR is a challenge, and this function is indispensable for construction design review and coordination. This paper proposes an occlusion detection framework that semi-automatically identifies occluded objects in 3D construction models. The framework determines the visibility status of an object by converting the object to a point cloud and comparing the point cloud to the virtual laser scanning result of the original model. It exports models that are interoperable with VR development software so that visualization effects can be easily employed to occluded objects. The authors validated the framework using two building information models. The algorithm achieved a recall rate of 90.30% and a precision rate of 75.05% in a gasoline refinery facility model. It reached a higher 98.06% recall rate and a 97.53% precision rate in an academic building model. This paper contributes to the body of knowledge by proposing a semi-automatic occlusion detection framework and validating that point cloud-based algorithms are appropriate for this classification task.", "Keywords": "Virtual Reality ; Occluded Objects ; Construction Design Review ; Automation ; Visualization", "DOI": "10.1016/j.aei.2021.101398", "PubYear": 2021, "Volume": "50", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Sustainable Systems Program, Department of Civil, Architectural and Environmental Engineering, The University of Texas at Austin, 301 E. Dean <PERSON>eton St. Stop C1752, Austin, TX 78712-1094, United States;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Construction Engineering and Project Management Program, Department of Civil, Architectural and Environmental Engineering, The University of Texas at Austin, 301 E. Dean Keeton St. Stop C1752, Austin, TX 78712-1094, United States"}, {"AuthorId": 3, "Name": "Fernanda <PERSON>", "Affiliation": "<PERSON> Centennial Teaching Fellowship in Civil Engineering, Construction Engineering and Project Management Program, Sustainable Systems Program, Department of Civil, Architectural and Environmental Engineering, The University of Texas at Austin, 301 E. Dean Keeton St. Stop C1752, Austin, TX 78712-1094, United States"}], "References": [{"Title": "Vagueness visualization in building models across different design stages", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "", "Page": "101107", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An immersive virtual reality serious game to enhance earthquake behavioral responses and post-earthquake evacuation preparedness in buildings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "", "Page": "101118", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Towards a customizable immersive virtual reality serious game for earthquake emergency training", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101134", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 90146350, "Title": "A comprehensive survey on the reduction of the semantic gap in content-based image retrieval", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAPR.2021.117207", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 29319, "JournalTitle": "International Journal of Applied Pattern Recognition", "ISSN": "2049-887X", "EISSN": "2049-8888", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90146605, "Title": "High-frequency electromagnetic field analysis using pseudo-quadruple precision in subdomain local solver", "Abstract": "The purpose of this study is an improvement of convergence properties of an interface problem in the high-frequency electromagnetic field finite element analysis based on the iterative domain decomposition method by introducing a pseudo-quadruple precision into the subdomain solver. In the high-frequency electromagnetic field analysis, it is well known that when the numerical model becomes huge scale, the convergence properties of the interface problem become extremely poor. To improve the deterioration of the convergence property in the interface problem, we propose to introduce a pseudo-quadruple precision into the iterative solver based on the conjugate gradient method in the subdomain problem. The pseudo-quadruple precision is constructed by ordinary double precisions. In this study, we apply the pseudo-quadruple precision for solving subdomain problems in the iterative domain decomposition method. As a result, we can obtain improvement of convergence properties in the iterative solver for the interface problem.", "Keywords": "Pseudo-quadruple precision;Domain decomposition method;Parallel finite element method;High-frequency electromagnetic field analysis", "DOI": "10.15748/jasse.8.194", "PubYear": 2021, "Volume": "8", "Issue": "2", "JournalId": 34297, "JournalTitle": "Journal of Advanced Simulation in Science and Engineering", "ISSN": "", "EISSN": "2188-5303", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Miyazaki"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Toyo University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Toyo University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Tokyo"}], "References": []}, {"ArticleId": ********, "Title": "Scheduling of a class of partial routing FMS in uncertain environments with beam search", "Abstract": "<p>In this paper, incremental computation of schedules for complex discrete event systems in an uncertain environment is studied. Uncertainties are assumed to occur due to uncontrollable events. A particular class of flexible manufacturing systems (FMSs) with partial precedence constraints is proposed where some operations are performed with total precedence constraints and others with full routing flexibility (namely partial routing FMSs ). Interruptions may occur due to unavailability of resources and interruption of operations. Such interruptions may deviate the trajectory from the planed schedule. For the modeling of the partial routing FMS, a systematic multi-level formalism based on the hierarchical structuration of the operations is introduced. Then, the risk of deviation is integrated and a new cost function is defined accordingly. Finally, a modified beam search algorithm referred to as generation double filtered beam search algorithm that accelerates the convergence of the method is proposed. The new algorithm is based on a new filtering mechanism that uses the cost function to selectively explore the state space of Petri net model in order to find a control sequence from an initial state to a reference one with a trade-off between performance and robustness. Examples are used to illustrate the efficiency of the proposed scheduling approach.</p>", "Keywords": "Flexible manufacturing systems; Discrete event systems; Transition-timed petri nets; Scheduling; Uncertainties; Beam search", "DOI": "10.1007/s10845-021-01801-3", "PubYear": 2023, "Volume": "34", "Issue": "2", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "GREAH-UNILEHAVRE, Normandie Université, Le Havre, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "GREAH-UNILEHAVRE, Normandie Université, Le Havre, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "GREAH-UNILEHAVRE, Normandie Université, Le Havre, France"}], "References": [{"Title": "Time/sequence-dependent scheduling: the design and evaluation of a general purpose tabu-based adaptive large neighbourhood search algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "4", "Page": "1051", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "An improved genetic algorithm for the flexible job shop scheduling problem with multiple time constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100664", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Mathematical modeling and a hybrid evolutionary algorithm for process planning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3", "Page": "781", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Applications of artificial intelligence in engineering and manufacturing: a systematic review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "6", "Page": "1581", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Scheduling Problems for a Class of Hybrid FMS Using T-TPN and Beam Search", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3", "Page": "591", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": 90146853, "Title": "Artificial intelligence maturity model: a systematic literature review", "Abstract": "<p>Organizations in various industries have widely developed the artificial intelligence (AI) maturity model as a systematic approach. This study aims to review state-of-the-art studies related to AI maturity models systematically. It allows a deeper understanding of the methodological issues relevant to maturity models, especially in terms of the objectives, methods employed to develop and validate the models, and the scope and characteristics of maturity model development. Our analysis reveals that most works concentrate on developing maturity models with or without their empirical validation. It shows that the most significant proportion of models were designed for specific domains and purposes. Maturity model development typically uses a bottom-up design approach, and most of the models have a descriptive characteristic. Besides that, maturity grid and continuous representation with five levels are currently trending in maturity model development. Six out of 13 studies (46%) on AI maturity pertain to assess the technology aspect, even in specific domains. It confirms that organizations still require an improvement in their AI capability and in strengthening AI maturity. This review provides an essential contribution to the evolution of organizations using AI to explain the concepts, approaches, and elements of maturity models.</p>", "Keywords": "Artificial Intelligence;Maturity model;Organization;Systematic literature review", "DOI": "10.7717/peerj-cs.661", "PubYear": 2021, "Volume": "7", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Software Technology and Management, Universiti Kebangsaan Malaysia, Bangi, Selangor, Malaysia;College of Business Informatics, University of Information Technology and Communications, Baghdad, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Software Technology and Management, Universiti Kebangsaan Malaysia, Bangi, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Artificial Intelligence Technology, Universiti Kebangsaan Malaysia, Bangi, Selangor, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Artificial Intelligence Technology, Universiti Kebangsaan Malaysia, Bangi, Selangor, Malaysia"}], "References": [{"Title": "A Systematic Review of the Application of Maturity Models in Universities", "Authors": "Esteban <PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON>-<PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "10", "Page": "466", "JournalTitle": "Information"}]}, {"ArticleId": 90146865, "Title": "RETRACTED ARTICLE: Research and design of fresh agricultural product distribution service model and framework using IoT technology", "Abstract": "", "Keywords": "", "DOI": "10.1007/s12652-021-03447-8", "PubYear": 2024, "Volume": "15", "Issue": "S1", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Logistics Engineering, Dongguan Polytechnic, Dongguan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Dongguan Polytechnic, Dongguan, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical Education, Dongguan Polytechnic, Dongguan, China"}], "References": []}, {"ArticleId": 90147174, "Title": "Research on spam filtering algorithm based on mutual information and weighted naive Bayesian classification", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAHUC.2021.10040647", "PubYear": 2021, "Volume": "37", "Issue": "4", "JournalId": 23592, "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing", "ISSN": "1743-8225", "EISSN": "1743-8233", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>yun Yu", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90147261, "Title": "Data-driven robust optimization for the itinerary planning via large-scale GPS data", "Abstract": "In this paper, we propose a data-driven robust optimization for establishing reliable itineraries through the use of GPS trajectories. The goal of the study is to provide a robust solution that is able to maximize the probability of achieving the expected travel time and minimize the delay. The designed framework can be viewed as an incremental approach, where data-driven robust optimization cooperates with a learning procedure such that both the uncertainty set and the objective function are incrementally adjusted according to the current data analysis results. In fact, two types of training models are designed in order to adapt the robust optimization model through analyzing GPS-data. The first training model tries to generate the uncertainty set for establishing the model, and the second one establishes the best parameter-settings allowing to converge towards a robust solution. Finally, a data-based simulation framework is designed for analyzing the robustness of the proposed method, where achieved solutions are tested on a simulated traffic network by using real-world orders as the comparison targets.", "Keywords": "Data-driven ; Learning ; Optimization ; Robustness ; Uncertainty", "DOI": "10.1016/j.knosys.2021.107437", "PubYear": 2021, "Volume": "231", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Public Finance and Taxation, Zhongnan University of Economics and Law, China;Innovation and Talent Base for Income Distribution and Public Finance, Zhongnan University of Economics and Law, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "EPROAD EA 4669, Université de Picardue Jules <PERSON>, Amiens, France;Corresponding author"}], "References": [{"Title": "A robust bi-objective mathematical model for disaster rescue units allocation and scheduling with learning effect", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106790", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 90147274, "Title": "Protein–ligand docking using differential evolution with an adaptive mechanism", "Abstract": "The protein–ligand docking problem plays a crucial role in the drug discovery process and remains challenging in bioinformatics. A successful protein–ligand docking approach depends on two key factors: an efficient search strategy and an effective scoring function. In this study, we attempt to use an adaptive differential evolution (DE) algorithm as the search strategy. The search ability of the proposed DE algorithm is improved by incorporating a parameter adaptation scheme and a modified mutation strategy. In addition, the scoring function of the classical AutoDock Vina suite is employed as the fitness function of the proposed approach. Finally, the performance of the adaptive DE method in solving the protein–ligand docking problem is evaluated on 40 test docking instances. The experimental results and statistical analysis demonstrate the effectiveness of the proposed adaptive DE algorithm compared with five other classical evolutionary algorithms. The results of this study reveal that employing powerful evolutionary algorithms, such as adaptive DE, contributes to solving the protein–ligand docking problem.", "Keywords": "Protein–ligand docking ; Differential evolution ; Parameter adaptation ; Optimization ; Molecular docking", "DOI": "10.1016/j.knosys.2021.107433", "PubYear": 2021, "Volume": "231", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Song", "Affiliation": "Aliyun School of Big Data, Changzhou University, Changzhou 213164, China;Key Laboratory of Urban Big Data Analysis and Applied Technology, Changzhou University, Changzhou 213164, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, University of Toyama, Toyama-shi, 930-8555, Japan;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Aliyun School of Big Data, Changzhou University, Changzhou 213164, China;Key Laboratory of Urban Big Data Analysis and Applied Technology, Changzhou University, Changzhou 213164, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering, University of Toyama, Toyama-shi, 930-8555, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, Kanazawa University, Kanazawa-shi, 920-1192, Japan;Corresponding authors"}], "References": [{"Title": "Incorporating a multiobjective knowledge-based energy function into differential evolution for protein structure prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Junkai Ji", "PubYear": 2020, "Volume": "540", "Issue": "", "Page": "69", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 90147414, "Title": "REAL TIME ASSISTANCE FOR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'S PATIENTS USING IoT AND\nCLOUD COMPUTING", "Abstract": "", "Keywords": "", "DOI": "10.26634/jcc.7.2.18128", "PubYear": 2020, "Volume": "7", "Issue": "2", "JournalId": 49860, "JournalTitle": "i-manager’s Journal on Cloud Computing", "ISSN": "2349-6835", "EISSN": "2350-1308", "Authors": [{"AuthorId": 1, "Name": "B. MUTHU SENTHIL", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}, {"AuthorId": 2, "Name": "J. SABARISH", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}, {"AuthorId": 3, "Name": "S. SONALI", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}, {"AuthorId": 4, "Name": "P. T. R. VIDHYAA", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamil Nadu, India."}], "References": []}, {"ArticleId": 90147630, "Title": "An active detection of compromised nodes based on en-route trap in wireless sensor network", "Abstract": "<p>With the development and wide use of wireless sensor network, security arises as an essential issue since sensors with restrict resources are deployed in wild areas in an unattended manner. Most of current en-route filtering schemes could filter false data effectively; however, the compromised nodes could take use of the filtering scheme to launch Fictitious False data Dropping attack, the detection of this attack is extremely difficult since the previous hop node is unable to distinguish whether the forwarding node dropt a false data report with incorrect Message Authentication Codes or a legitimate report. This is the first attempt to address the Fictitious False data Dropping attack; in this article, we propose an Active Detection of compromised nodes based on En-route Trap to trap compromised nodes in the scenario of a false data dropping. A trust model is used to evaluate trust level of neighbor nodes with respect to their authentication behaviors. A detecting algorithm of compromised node is used to detect compromised nodes. Simulation results showed that our scheme can address the Fictitious False data Dropping attack and detect 60% of compromised nodes with a low false positive rate; consequently, the packet accuracy of an Active Detection of compromised nodes based on En-route Trap increases rapidly and reaches to 86%.</p>", "Keywords": "", "DOI": "10.1177/15501477211040367", "PubYear": 2021, "Volume": "17", "Issue": "8", "JournalId": 1187, "JournalTitle": "International Journal of Distributed Sensor Networks", "ISSN": "1550-1329", "EISSN": "1550-1477", "Authors": [{"AuthorId": 1, "Name": "Jiang<PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Applied Mathematics, Changsha University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering and Applied Mathematics, Changsha University, Changsha, China"}], "References": []}, {"ArticleId": 90147643, "Title": "A many-objective evolutionary algorithm based on dominance and decomposition with reference point adaptation", "Abstract": "Achieving balance between convergence and diversity is a challenge in many-objective optimization problems (MaOPs). Many-objective evolutionary algorithms (MaOEAs) based on dominance and decomposition have been developed successfully for solving partial MaOPs. However, when the optimization problem has a complicated Pareto front (PF), these algorithms show poor versatility in MaOPs. To address this challenge, this paper proposes a co-guided evolutionary algorithm by combining the merits of dominance and decomposition. An elitism mechanism based on cascading sort is exploited to balance the convergence and diversity of the evolutionary process. At the same time, a reference point adaptation method is designed to adapt to different PFs. The performance of our proposed method is validated and compared with seven state-of-the-art algorithms on 200 instances of 27 widely employed benchmark problems. Experimental results fully demonstrate the superiority and versatility of our proposed method on MaOPs with regular and irregular PFs.", "Keywords": "Many-objective optimization ; Evolutionary algorithm ; Pareto optimality ; Reference point adaptation", "DOI": "10.1016/j.knosys.2021.107392", "PubYear": 2021, "Volume": "231", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing and Information Processing, Ministry of Education, School of Computer Science and School of Cyberspace Science of Xiangtan University, Xiangtan, Hunan Province, China;Faculty of Informational Engineering University of Xiangtan, Xiangtan, 411105, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing and Information Processing, Ministry of Education, School of Computer Science and School of Cyberspace Science of Xiangtan University, Xiangtan, Hunan Province, China;Faculty of Informational Engineering University of Xiangtan, Xiangtan, 411105, China;Corresponding author at: Key Laboratory of Intelligent Computing and Information Processing, Ministry of Education, School of Computer Science and School of Cyberspace Science of Xiangtan University, Xiangtan, Hunan Province, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing and Information Processing, Ministry of Education, School of Computer Science and School of Cyberspace Science of Xiangtan University, Xiangtan, Hunan Province, China;Faculty of Informational Engineering University of Xiangtan, Xiangtan, 411105, China;Hunan Provincial Key Laboratory of Intelligent Information Processing and Application, Hengyang, 421002, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing and Information Processing, Ministry of Education, School of Computer Science and School of Cyberspace Science of Xiangtan University, Xiangtan, Hunan Province, China;School of Computer Science and Informatics, De Montfort University, Leicester LE1 9BH, UK"}], "References": [{"Title": "What Weights Work for You? Adapting Weights for Any Pareto Front Shape in Decomposition-Based Evolutionary Multiobjective Optimisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "2", "Page": "227", "JournalTitle": "Evolutionary Computation"}]}, {"ArticleId": 90147657, "Title": "An integrated FCM-FBWM approach to assess and manage the readiness for blockchain incorporation in the supply chain", "Abstract": "Despite the initial hype surrounding blockchain applications in the supply chain, real-world implementation of this disruptive technology faces significant challenges. To manage these challenges and ensure a smooth implementation, supply chain organizations must perform various activities to acquire readiness. This paper proposes a readiness assessment and management approach for blockchain implementation in the supply chain. The proposed approach allows supply chain decision-makers to (1) identify the readiness-relevant activities for blockchain implementation, (2) model the causal relationships among the identified activities, (3) assess the activities’ contribution weights to the overall readiness, and (4) develop an effective readiness improvement plan by prioritizing those activities with the most impact on the overall readiness. Fuzzy cognitive maps (FCMs) are employed to model the causal relationships between the activities. The fuzzy best–worst​ method (FBWM) is adopted to establish the contribution weights of the activities to the supply chain’s overall readiness. The FCM inference process is also used to incorporate feedback loops among the activities. The proposed approach is then illustrated through an empirical study.", "Keywords": "Blockchain technology ; Supply chain management (SCM) ; Readiness ; Fuzzy cognitive map (FCM) ; Fuzzy best–worst method (FBWM)", "DOI": "10.1016/j.asoc.2021.107832", "PubYear": 2021, "Volume": "112", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "Mandana <PERSON>", "Affiliation": "Department of Information Management, Faculty of Management and Accounting, Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Management, Faculty of Management and Accounting, Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Cyberspace Research Institute, Shahid Be<PERSON>hti University, Tehran, Iran;Corresponding author"}, {"AuthorId": 4, "Name": "Elpiniki I. <PERSON>", "Affiliation": "Faculty of Technology, Department of Energy Systems, University of Thessaly, Geopolis Campus, 41500 Larissa, Greece"}], "References": [{"Title": "Designated-verifier proof of assets for bitcoin exchange using elliptic curve cryptography", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "854", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A survey on the security of blockchain systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "841", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "A Comprehensive Survey on Attacks, Security Issues and Blockchain Solutions for IoT and IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102481", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Real-time supply chain—A blockchain architecture for project deliveries", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101909", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A Blockchain Tokenizer for Industrial IOT trustless applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "432", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "The impact of blockchain technology on business models – a taxonomy and archetypal patterns", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "285", "JournalTitle": "Electronic Markets"}, {"Title": "Understanding the creation of trust in cryptocurrencies: the case of Bitcoin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "259", "JournalTitle": "Electronic Markets"}]}, {"ArticleId": 90147700, "Title": "Nature inspired hybrid algorithms for binding shared key with user trait", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAPR.2021.117202", "PubYear": 2021, "Volume": "6", "Issue": "3", "JournalId": 29319, "JournalTitle": "International Journal of Applied Pattern Recognition", "ISSN": "2049-887X", "EISSN": "2049-8888", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 90147705, "Title": "Integration of fuzzy-weighted zero-inconsistency and fuzzy decision by opinion score methods under a q-rung orthopair environment: A distribution case study of COVID-19 vaccine doses", "Abstract": "<p>Owing to the limitations of Pythagorean fuzzy and intuitionistic fuzzy sets, scientists have developed a distinct and successive fuzzy set called the q-rung orthopair fuzzy set (q-ROFS), which eliminates restrictions encountered by decision-makers in multicriteria decision making (MCDM) methods and facilitates the representation of complex uncertain information in real-world circumstances. Given its advantages and flexibility, this study has extended two considerable MCDM methods the fuzzy-weighted zero-inconsistency (FWZIC) method and fuzzy decision by opinion score method (FDOSM) under the fuzzy environment of q-ROFS. The extensions were called q-rung orthopair fuzzy-weighted zero-inconsistency (q-ROFWZIC) method and q-rung orthopair fuzzy decision by opinion score method (q-ROFDOSM). The methodology formulated had two phases. The first phase 'development' presented the sequential steps of each method thoroughly.The q-ROFWZIC method was formulated and used in determining the weights of evaluation criteria and then integrated into the q-ROFDOSM for the prioritisation of alternatives on the basis of the weighted criteria. In the second phase, a case study regarding the MCDM problem of coronavirus disease 2019 (COVID-19) vaccine distribution was performed. The purpose was to provide fair allocation of COVID-19 vaccine doses. A decision matrix based on an intersection of 'recipients list' and 'COVID-19 distribution criteria' was adopted. The proposed methods were evaluated according to systematic ranking assessment and sensitivity analysis, which revealed that the ranking was subject to a systematic ranking that is supported by high correlation results over different scenarios with variations in the weights of criteria.</p><p>© 2021 Elsevier B.V. All rights reserved.</p>", "Keywords": "COVID-19;FDOSM;FWZIC;Multicriteria Decision-making;Vaccine;q-Rung Orthopair Fuzzy", "DOI": "10.1016/j.csi.2021.103572", "PubYear": 2022, "Volume": "80", "Issue": "", "JournalId": 739, "JournalTitle": "Computer Standards & Interfaces", "ISSN": "0920-5489", "EISSN": "1872-7018", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 4, "Name": "Alhamzah <PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 5, "Name": "H.A. Alsattar", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, University of Melbourne, 700 Swanston Street, Victoria 3010 Australia."}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, IT and Environment, Charles Darwin University, NT, Australia."}, {"AuthorId": 11, "Name": "Salem Garfan", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Faculty of Arts, Computing and Creative Industry, Universiti Pendidikan Sultan <PERSON>, Tanjung Malim 35900, Malaysia."}], "References": [{"Title": "Multi-agent learning neural network and Bayesian model for real-time IoT skin detectors: a new evaluation and benchmarking methodology", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "12", "Page": "8315", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An approach based on linguistic spherical fuzzy sets for public evaluation of shared bicycles in China", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103295", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A new standardisation and selection framework for real-time image dehazing algorithms from multi-foggy scenes based on fuzzy Delphi and hybrid multi-criteria decision analysis methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "4", "Page": "1029", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Fuzzy decision by opinion score method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106595", "JournalTitle": "Applied Soft Computing"}, {"Title": "Fuzzy decision by opinion score method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106595", "JournalTitle": "Applied Soft Computing"}, {"Title": "Entropy measure and TOPSIS method based on correlation coefficient using complex q-rung orthopair fuzzy information and its application to multi-attribute decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "1249", "JournalTitle": "Soft Computing"}, {"Title": "A novel fuzzy hybrid neutrosophic decision‐making approach for the resilient supplier selection problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "12", "Page": "1934", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "IoT-based telemedicine for disease prevention and health promotion: State-of-the-Art", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Zahraa K<PERSON>", "PubYear": 2021, "Volume": "173", "Issue": "", "Page": "102873", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Multidimensional benchmarking of the active queue management methods of network congestion control based on extension of fuzzy decision by opinion score method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "796", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multidimensional benchmarking of the active queue management methods of network congestion control based on extension of fuzzy decision by opinion score method", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "796", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "PSO–Blockchain-based image steganography: towards a new method to secure updating and sharing COVID-19 data in decentralised hospitals intelligence architecture", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "9", "Page": "14137", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Interval type 2 trapezoidal‐fuzzy weighted with zero inconsistency combined with VIKOR for evaluating smart e‐tourism applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "9", "Page": "4723", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Novel dynamic fuzzy Decision-Making framework for COVID-19 vaccine dose recipients", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "", "Page": "147", "JournalTitle": "Journal of Advanced Research"}]}, {"ArticleId": 90147724, "Title": "Survey data on land tenure and food security among farming households in northern Nigeria", "Abstract": "This dataset presents data collected from the households’ survey in Northern Nigeria to examine land tenure and property rights among smallholder rice farmers and the influence it has on household food security. Data collection was by personal interviews of adult members of the farmers’ households, focusing on the households’ socio-economics, United States Department of Agriculture’- 18 Household Food Security questions for households with children, land titling status and land tenure type on farmland cultivated during the 2016/17 farming season. The data were collected from 475 rice farmers selected by multistage sampling across 84 rice-growing communities, seven States and the three geopolitical zones in northern Nigeria. Household food security was assessed within the framework of the United States Department of Agriculture’ HFS Survey Module. Land Tenure and Property Rights (LTPRs) assessment was in terms of the type (source) and registration of titles to farmlands. The hypothesis that guided the cross-sectional survey conducted to generate these data is that insecure land tenure and property rights are important drivers of food insecurity.", "Keywords": "Household food security ; Household with children ; Land tenure ; Land titling", "DOI": "10.1016/j.dib.2021.107325", "PubYear": 2021, "Volume": "38", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Mojisola <PERSON>", "Affiliation": "Department of Agriculture, Landmark University, Omu Aran, Kwara State, Nigeria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Economics and Farm Management, Federal University of Agriculture, Abeokuta, Nigeria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Agricultural Economics and Farm Management, Federal University of Agriculture, Abeokuta, Nigeria"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Agriculture, Landmark University, Omu Aran, Kwara State, Nigeria"}], "References": []}, {"ArticleId": 90147742, "Title": "Metric learning with clustering-based constraints", "Abstract": "<p>In most of the existing metric learning methods, the relation is fixed throughout the metric learning process. However, the fixed relation may be harmful to learn a good metric. The adversarial metric learning implements a dynamic update of the pairwise constraints. Inspired by the idea of dynamically updating constraints, we propose in this paper a metric learning model with clustering-based constraints (ML-CC), wherein the triple constraints of large margin are iteratively generated with the clusters of data points. The proposed method can overcome the shortage of the fixed triple constraints constructed under the Euclidian distance. The experimental results on synthetic and real datasets indicate that the performance of the ML-CC is superior to that of the existing state-of-the-art metric learning methods.</p>", "Keywords": "Metric learning; Triple constraints; Clustering; Large margin; Dynamic constraint", "DOI": "10.1007/s13042-021-01408-3", "PubYear": 2021, "Volume": "12", "Issue": "12", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "@qq.com;Key Laboratory of Computational Intelligence and Chinese Information Processing of Ministry of Education, School of Computer and Information Technology, Shanxi University, Taiyuan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Computational Intelligence and Chinese Information Processing of Ministry of Education, School of Computer and Information Technology, Shanxi University, Taiyuan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Liang", "Affiliation": "Key Laboratory of Computational Intelligence and Chinese Information Processing of Ministry of Education, School of Computer and Information Technology, Shanxi University, Taiyuan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Computational Intelligence and Chinese Information Processing of Ministry of Education, School of Computer and Information Technology, Shanxi University, Taiyuan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Computational Intelligence and Chinese Information Processing of Ministry of Education, School of Computer and Information Technology, Shanxi University, Taiyuan, China"}], "References": [{"Title": "Semi-supervised clustering with deep metric learning and graph embedding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "2", "Page": "781", "JournalTitle": "World Wide Web"}, {"Title": "Deep face clustering using residual graph convolutional network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "106561", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 90147839, "Title": "Suspension Regulation of Medium-Low-Speed Maglev Trains Via Deep Reinforcement Learning", "Abstract": "This article studies the suspension regulation problem of medium-low-speed maglev trains (mlsMTs), which is not well solved by most model-based controllers. We propose a sample-based controller by reformulating it as a continuous Markov decision process (MDP) with unknown transition probabilities. Then, we propose a reinforcement learning algorithm with actor-critic neural networks to solve the MDP. To reduce estimation errors in the Q-function, we adopt a double Q-learning scheme and propose a novel initialization method to accelerate the convergence by exploiting the PID controller. Finally, we illustrate that the proposed controllers outperform the existing PID controller with a real dataset from the mlsMT in Changsha, China, and are even comparable to model-based controllers, which, however, assume that the complete information of the model is known, via simulations. <p xmlns:mml=\"http://www.w3.org/1998/Math/MathML\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"> Impact Statement —The control problem of levitation systems is essential for maglev trains. The advanced control methods require an exact dynamical model, which is difficult to establish in practice due to uncertainties and complex dynamics. Reinforcement learning (RL), as a model-free method, learns a controller directly from data. We are the first to propose deep RL algorithms to address the levitation control problem. By learning the real dataset provided by CRRC, the proposed algorithms outperform the well-known PID controller significantly. In particular, our controller responses 50 times faster than PID even without additional efforts on modeling. Moreover, the proposed initialization method can be applied in a variety of RL-based control problems to improve performance.</p>", "Keywords": "Deep reinforcement learning;markov decision procession;neural network;suspension regulation;the levitation control system", "DOI": "10.1109/TAI.2021.3097313", "PubYear": 2021, "Volume": "2", "Issue": "4", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, Beijing National Research Center for Information Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, Beijing National Research Center for Information Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, Beijing National Research Center for Information Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CRRC Zhuzhou Locomotive Co. Ltd., Zhuzhou, China"}, {"AuthorId": 5, "Name": "Laisheng Tong", "Affiliation": "CRRC Zhuzhou Locomotive Co. Ltd., Zhuzhou, China"}], "References": []}, {"ArticleId": 90147865, "Title": "Designing a Transferable Predictive Model for Online Learning Using a Bayesian Updating Approach", "Abstract": "Predictive modeling in online education is a popular topic in learning analytics research and practice. This study proposes a novel predictive modeling method to improve model transferability over time within the same course and across different courses. The research gaps addressed are limited evidence showing whether a predictive model built on historical data retrospectively can be directly applied to a future offering of the same course or to another different course; lacking interpretable data mining models to improve model transferability over time and across courses. Three datasets from two distinct online courses with one course offered two times over two years were applied using direct transferring of the predictive model and the proposed Bayesian updating technique for model transfer. The results showed that the direct transferring of predictive model to the subsequent offering of the course and to a totally different course did not work effectively. By contrast, the proposed Bayesian updating provided a robust and interpretable approach with improved model transferability results for both situations. This Bayesian updating model can be continuously updated with new collected data rather than building prediction model from scratch every time, which can serve as a new methodological framework to carry experience and knowledge from past and other courses forward to new courses.", "Keywords": "Learning analytics (LA);machine learning;model transferability;online learning;performance prediction.", "DOI": "10.1109/TLT.2021.3107349", "PubYear": 2021, "Volume": "14", "Issue": "4", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Teaching and Learning, University of Florida, Gainesville, FL, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial, Manufacturing, and Systems Engineering, Texas Tech University, Lubbock, TX, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Industrial, Manufacturing, and Systems Engineering, Texas Tech University, Lubbock, TX, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial, Manufacturing, and Systems Engineering, Texas Tech University, Lubbock, TX, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>ng Du", "Affiliation": "School of Teaching and Learning, University of Florida, Gainesville, FL, USA"}], "References": []}, {"ArticleId": 90147983, "Title": "Review on laser-induced etching processing technology for transparent hard and brittle materials", "Abstract": "<p>The high-efficiency processing and the high geometrical precision requirements on the transparent hard-brittle optoelectrical materials are attractive to the modern industrial’s interest. Laser-induced related ablation/etching technology is proved to be effective processing for micro/nanofabrication of the transparent hard and brittle materials, due to its unique advantages of mechanical micro-machining with controllable thermal damage. There are some influencing factors like laser duration, target-to-substrate distance (work distance), target materials (absorption resolution), laser fluence, and circumstance (pressure, air, resolution) which were discussed in this review. The processing qualities in various methods were compared and described in this review. The new development of laser-induced related ablation/etching and related advanced technologies is introduced at the end of this review.</p>", "Keywords": "Laser-induced etching processing technology; Transparent hard and brittle materials; Micro/nano-fabrication", "DOI": "10.1007/s00170-021-07853-2", "PubYear": 2021, "Volume": "117", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Manufacturing Engineering, National Huaqiao University, Xiamen, People’s Republic of China;Fujian Engineering Research Center of Intelligent Manufacturing for Brittle Materials, Xiamen, People’s Republic of China"}, {"AuthorId": 2, "Name": "Xizhao Lu", "Affiliation": "College of Mechanical Engineering and Automation, National Huaqiao University, Xiamen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Manufacturing Engineering, National Huaqiao University, Xiamen, People’s Republic of China;Fujian Engineering Research Center of Intelligent Manufacturing for Brittle Materials, Xiamen, People’s Republic of China"}, {"AuthorId": 4, "Name": "Feng <PERSON>", "Affiliation": "Institute of Manufacturing Engineering, National Huaqiao University, Xiamen, People’s Republic of China;Fujian Engineering Research Center of Intelligent Manufacturing for Brittle Materials, Xiamen, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Manufacturing Engineering, National Huaqiao University, Xiamen, People’s Republic of China"}, {"AuthorId": 6, "Name": "Dajiang Lei", "Affiliation": "Institute of Machinery Manufacturing Technology, China Academy of Engineering Physics (CAEP), Chengdu, China"}, {"AuthorId": 7, "Name": "Yongcheng Pan", "Affiliation": "Institute of Machinery Manufacturing Technology, China Academy of Engineering Physics (CAEP), Chengdu, China"}], "References": []}, {"ArticleId": 90148002, "Title": "A domain-specific modeling milestone", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10270-021-00921-x", "PubYear": 2021, "Volume": "20", "Issue": "4", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Alabama, Tuscaloosa, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "MetaCase, Jyväskylä, Finland"}], "References": []}, {"ArticleId": 90148373, "Title": "Projection Method for Saddle Points of Energy Functional in $$H^{-1}$$ Metric", "Abstract": "<p>Saddle points play important roles as the transition states of activated process in gradient systems driven by energy functional. However, for the same energy functional, the saddle points, as well as other stationary points, are different in different metrics such as the \\(L^2\\) metric and the \\(H^{-1}\\) metric. The saddle point calculation in \\(H^{-1}\\) metric is more challenging with much higher computational cost since it involves higher order derivative in space and the inner product calculation needs to solve another Possion equation to get the \\(\\Delta ^{-1}\\) operator. In this paper, we introduce the projection idea to the existing saddle point search methods, gentlest ascent dynamics (GAD) and iterative minimization formulation (IMF), to overcome this numerical challenge due to \\(H^{-1}\\) metric. Our new method in the \\(L^2\\) metric can locate the saddle point in \\(H^{-1}\\) metric only by carefully incorporating a simple linear projection step. We show that our projection method maintains the same convergence speed of the original GAD and IMF, but the new algorithm is much faster than the direct method for \\(H^{-1}\\) problems. The numerical results of saddle points in the one dimensional Ginzburg-Landau free energy and the two dimensional Landau-Brazovskii free energy in \\(H^{-1}\\) metric are presented to demonstrate the efficiency of this new method.</p>", "Keywords": "Saddle point; Transition state; Projection method; Gentlest ascent dynamics; Primary 65K05; Secondary 82B05", "DOI": "10.1007/s10915-021-01592-y", "PubYear": 2021, "Volume": "89", "Issue": "1", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "Shuting Gu", "Affiliation": "College of Big Data and Internet, Shenzhen Technology University, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mathematics, Sun Yat-sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science and Department of Mathematics, City University of Hong Kong, Kowloon, Hong Kong, SAR"}], "References": [{"Title": "Fast and stable schemes for Phase Fields models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "6", "Page": "1683", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 90148476, "Title": "PERIDOT: Modeling Execution Time of Spark Applications", "Abstract": "A data analytics application submitted to a Spark cluster often has to finish executing by a specified time target. To use cluster resources effectively, the key challenge is having the ability to gain quick insights on how the execution time of any given application is likely to be impacted by the resources allocated to the application, e.g., the number of Spark executor cores assigned, and the size of the data to be processed. Such insights can be used to quickly estimate the required resources and configure a Spark application for a desired execution time using the least amount of resources. Our paper proposes an automated execution time estimation approach called PERIDOT that involves executing a given application under a fixed resource setting with two different-sized, small subsets of its input data to offer fast, lightweight execution time predictions. It analyzes logs from these two executions to estimate the dependencies between internal stages of the application. Information on these dependencies combined with knowledge of Spark's data partitioning mechanisms is used to derive an analytic model that can estimate execution times for other resource settings and input data sizes. Our results from a wide range of applications and multiple Spark clusters show that PERIDOT can accurately estimate the execution time of an application from limited historical data, and suggest the minimum amount of resources required to closely meet an execution time target.", "Keywords": "Apache spark;Big Data processing;performance prediction;performance engineering", "DOI": "10.1109/OJCS.2021.3107228", "PubYear": 2021, "Volume": "2", "Issue": "", "JournalId": 68233, "JournalTitle": "IEEE Open Journal of the Computer Society", "ISSN": "", "EISSN": "2644-1268", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical, and Software Engineering, University of Calgary, Calgary, AB, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computing, Mount Royal University, Calgary, AB, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical, and Software Engineering, University of Calgary, Calgary, AB, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Calgary, Calgary, AB, Canada"}], "References": [{"Title": "Efficient Performance Prediction for Apache Spark", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "149", "Issue": "", "Page": "40", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 90148482, "Title": "Intelligent Algorithm-Based Computed Tomography Image Features in Diagnosis of the Effect of Radiofrequency Ablation in Liver Cancer", "Abstract": "<p> To provide a reference for finding a reasonable evaluation method for treatment effect of radiofrequency ablation (RFA), computed tomography (CT) image optimized by the intelligent segmentation algorithm was utilized to evaluate the liver condition of hepatocellular carcinoma (HCC) patients after RFA and to estimate the patient’s prognosis. Eighty-eight patients with HCC who needed RFA surgery after diagnosis in our hospital were selected. The CT images before optimization were set as the control group; the CT images after optimization were set as the observation group. Comprehensive diagnosis was taken as the gold standard to compare the ablation range and residual lesions under CT scans before and after surgery. The results showed that the consistency of the two sets of CT images was compared with comprehensive diagnosis under different diameters of the lesion. The difference between the two groups was not statistically considerable when the diameter of the lesion was less than 50 mm ( P > 0.05 ). For lesions larger than 50 mm in diameter, the consistency of the observation group (83%) was remarkably higher than that of the control group (40%), and the difference was substantial ( P 0.05 ). The kappa value of the observation group was 0.84 and that of the control group was 0.78. The kappa value of observation group was better than the control group, with considerable difference ( P 0.05 ). In conclusion, the diagnostic effect of CT image based on intelligent segmentation algorithm was superior to conventional diagnosis when the diameter of the lesion was larger than 50 mm. Moreover, the overall improvement rate of patients after RFA treatment was far greater than the recurrence rate, indicating that the clinical adoption of RFA was very meaningful. </p>", "Keywords": "", "DOI": "10.1155/2021/3422484", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "Yufeng Cha", "Affiliation": "Department of Liver and Gallbladder Surgery, The Second Affiliated Hospital of Shaanxi University of Chinese Medicine, Xianyang 712000, Shaanxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Liver and Gallbladder Surgery, The Second Affiliated Hospital of Shaanxi University of Chinese Medicine, Xianyang 712000, Shaanxi, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Liver and Gallbladder Surgery, Baoji People’s Hospital, Baoji 716000, Shaanxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of General Surgery, Affiliated Hospital of Yan’an University, Yan’an 721000, Shaanxi, China"}], "References": []}, {"ArticleId": 90148484, "Title": "Detection, Integration, and Optimization of Acoustic Field Simulation in the Closed Space", "Abstract": "<p>Noise pollution in the closed space such as railway carriage is an important problem because the noise pollution seriously affects comfort and health of people in the closed space. We propose the method to detection, integration, and optimization of acoustic field simulation in the closed space. First, we analyze the acoustic ﬁeld distribution in the virtual 3D close space. We use harmonic sound wave propagation in the closed space and present the distribution according to geometric analysis. Second, we introduce Delaunay triangulation and k-means clustering into visualization to form the quiet zone and show it in 3D perspective. Our method used acoustic simulation to develop the sound barrier system. The simulation results show that our method can improve the analysis of the noise problem in the closed space.</p>", "Keywords": "", "DOI": "10.1155/2021/9301571", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Information Engineering Institute, Guangzhou Railway Polytechnic, Guangzhou 510430, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yet-sen University, Guangzhou 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Sun Yet-sen University, Guangzhou 510006, China"}], "References": []}, {"ArticleId": 90148714, "Title": "Privacy-preserving Multimedia Data Analysis", "Abstract": "", "Keywords": "", "DOI": "10.1093/comjnl/bxab095", "PubYear": 2021, "Volume": "64", "Issue": "7", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "Guangxi Normal University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of North Carolina at Chapel Hill, NC, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of North Carolina at Greensboro, USA"}], "References": [{"Title": "Spectral clustering via half-quadratic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "1969", "JournalTitle": "World Wide Web"}, {"Title": "Robust SVM with adaptive graph learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "1945", "JournalTitle": "World Wide Web"}, {"Title": "Heterogeneous data fusion for predicting mild cognitive impairment conversion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "54", "JournalTitle": "Information Fusion"}, {"Title": "A Federated Learning Approach for Privacy Protection in Context-Aware Recommender Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1016", "JournalTitle": "The Computer Journal"}, {"Title": "LEDet: A Single-Shot Real-Time Object Detector Based on Low-Light Image Enhancement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1028", "JournalTitle": "The Computer Journal"}, {"Title": "Global and Local Structure Preservation for Nonlinear High-dimensional Spectral Clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "993", "JournalTitle": "The Computer Journal"}, {"Title": "Adaptive Laplacian Support Vector Machine for Semi-supervised Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1005", "JournalTitle": "The Computer Journal"}, {"Title": "An Optimized k-means Algorithm Based on Information Entropy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1130", "JournalTitle": "The Computer Journal"}, {"Title": "Non-negative Matrix Factorization: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1080", "JournalTitle": "The Computer Journal"}, {"Title": "Learning Representation From Concurrence-Words Graph For Aspect Sentiment Classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1069", "JournalTitle": "The Computer Journal"}, {"Title": "Adaptive Cross-Lingual Question Generation with Minimal Resources", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1056", "JournalTitle": "The Computer Journal"}, {"Title": "Precise Point Set Registration Based on Feature Fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "7", "Page": "1039", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 90148719, "Title": "Intelligent Narrative Summaries: From Indicative to Informative Summarization", "Abstract": "Perceiving data can be challenging as the analysis goal is subjective. Storytelling approaches try to address this challenge by focusing on the research problem of understanding the data in general and, more particularly in curation, summarization, and presentation of the big data, i.e., a massive number of small data islands from personal, shared, and business data. In this context, summarization can facilitate understanding and surfacing insights that are embedded within the data. In this paper, we propose a general hierarchical personalized summarization framework, called Narrative Summaries (NARS), to improve the drawbacks of traditional summarization methods in various aspects. We proposed two variants of NARS: a semi-structured summarization approach (SNARS) and a fully-structured summarization approach (FNARS). The goal is to enable intelligent narrative construction of summaries based on the important features extracted from users' engagement. To achieve this goal, we propose a hierarchical structure to prevent users from being overwhelmed with less important information at first glance and to facilitate the selection process. Afterward, instead of providing a short static summary, we present an intelligent and interactive summarization approach to enable users to navigate through the hierarchy to gain more elaborated information upon request. Our approach aims to: (i) engage users in the summarization process, to guarantee the interactive speed even for extensive text collections, and (ii) eliminate the need for reference summaries, which is one of the most challenging issues in the summarization problem. The results demonstrate that our approach fabricates a summary that helps the users understand the topic better and faster compared to the existing state-of-the-art methods.", "Keywords": "Summarization ; Hierarchical summary ; Personalized summary", "DOI": "10.1016/j.bdr.2021.100257", "PubYear": 2021, "Volume": "26", "Issue": "", "JournalId": 3265, "JournalTitle": "Big Data Research", "ISSN": "2214-5796", "EISSN": "2214-580X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Macquarie University, Sydney, Australia;Arizona State University, Arizona, United States;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Macquarie University, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Arizona State University, Arizona, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Macquarie University, Sydney, Australia"}], "References": []}, {"ArticleId": ********, "Title": "Securing wake up radio for green wireless sensor networks against denial of sleep attacks", "Abstract": "This paper proposes the distributed cooperation model (DCM) based hierarchical smart routing protocol (HSRP) to counter the denial of sleep (DoSL) attacks in Green Wireless Sensor Networks (GWSNs). DCM solution combined with the Dynamic Elliptic Curves-RSA (DECRSA) algorithm is used to provide high security level and protect Wake-Up Radio (WUR) for GWSNs against DoSL attack targeting the energy resources of sensors. The used distributed schema ensures the load balancing in the network through the selection of a new Supervisor Node at the beginning of each round based on a special equation taking into account different metrics such as the remaining energy and the number of neighbour nodes. Simulation results conducted under the NS3 simulator show that the proposed DCM helps to effectively protect the GWSNs against the DoSL attacks and improves the network lifetime by more than 60% when three malicious nodes are used. Obtained results show also that the DCM extends either the period of stability and instability to the maximum and helps to counter the fall in the number of alive nodes which cause the problem of isolated area. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Cryptography; DCM; DECRSA; Denial of sleep; Deny of sleep attack; Distributed cooperation model; DoSL; Dynamic elliptic curves-RSA; Hierarchical smart routing protocol; HSRP; Secure routing; Wireless sensor networks; WSNs", "DOI": "10.1504/IJSNET.2021.117228", "PubYear": 2021, "Volume": "36", "Issue": "3", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mathematical, Physical and Natural Sciences of Tunis, University of Tunis El Manar, Tunis, 2092, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Technology, <PERSON> University, P.O. Box 1982, Dammam, 31441, Saudi Arabia"}], "References": []}, {"ArticleId": ********, "Title": "Colored dissolved organic matter absorption at global scale from ocean color radiometry observation: Spatio-temporal variability and contribution to the absorption budget", "Abstract": "A semi-analytical model (CDOM-KD2) based on the light vertical attenuation coefficient ( K <sub>d</sub>(λ)) has been developed for estimating the absorption by colored dissolved organic matter, a <sub>cdom</sub>(443), from ocean color remote sensing at global scale. The performance of this new inversion model together with that of former models by <PERSON><PERSON><PERSON> (2011) (S2011), <PERSON> et al. (2017) (C2017) and <PERSON><PERSON> et al. (2018) (A2018) was evaluated from in situ and matchup validation data sets gathering worldwide distributed samples. An overall consistency in the a <sub>cdom</sub>(443) estimated from S2011, C2017 and CDOM-KD2 models with a slightly better performance of the latter method was observed (MAPD of 27.42% and 30.85% for open ocean with in situ and satellite data, respectively), emphasizing the possible specific assessment of a <sub>cdom</sub>(443) dynamics from satellite remote sensing over the global ocean including the most oligotrophic waters. At 443 nm the global average relative contribution of a <sub>cdom</sub>(443) to the absorption by colored detrital matter, a <sub>cdm</sub>(443) is of 61% ± 14%, while the contribution of a <sub>cdom</sub>(443) to the non-water absorption, a <sub>nw</sub>(443), is of 35% ± 26%. Strong spatial disparities are however observed for both a <sub>cdom</sub>(443) temporal dynamics and relative contribution in the absorption budget. A decoupling is observed between a <sub>cdom</sub>(443) and particulate detrital (i.e. non-living) matter and phytoplankton in the gyre areas where a low temporal variability is globally observed. This is contrasting with water masses influenced by terrestrial inputs as well as in equatorial and subtropical areas impacted by main oceanic currents where CDOM loads and a <sub>cdom</sub>(443) contribution in the water absorption budget are more variable.", "Keywords": "Colored dissolved organic matter ; a <sub>cdom</sub>(443) ; Ocean color remote sensing ; Open ocean", "DOI": "10.1016/j.rse.2021.112637", "PubYear": 2021, "Volume": "265", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Univ. Littoral Côte d'Opale, Univ. Lille, CNRS, UMR 8187, LOG, Laboratoire d'Océanologie et de Géosciences, F 62930 Wimereux, France;ACRI-ST, 260 Route du Pin Montard, Sophia-Antipolis, 06410 Biot, France;Corresponding author at: Univ. Littoral Côte d'Opale, Univ. Lille, CNRS, UMR 8187, LOG, Laboratoire d'Océanologie et de Géosciences, F 62930 Wimereux, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Univ. Littoral Côte d'Opale, Univ. Lille, CNRS, UMR 8187, LOG, Laboratoire d'Océanologie et de Géosciences, F 62930 <PERSON><PERSON><PERSON>, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Univ. Littoral Côte d'Opale, Univ. Lille, CNRS, UMR 8187, LOG, Laboratoire d'Océanologie et de Géosciences, F 62930 <PERSON><PERSON><PERSON>, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ACRI-ST, 260 Route du Pin Montard, Sophia-Antipolis, 06410 Biot, France"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ. Littoral Côte d'Opale, Univ. Lille, CNRS, UMR 8187, LOG, Laboratoire d'Océanologie et de Géosciences, F 62930 <PERSON><PERSON><PERSON>, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Univ. Littoral Côte d'Opale, Univ. Lille, CNRS, UMR 8187, LOG, Laboratoire d'Océanologie et de Géosciences, F 62930 <PERSON><PERSON><PERSON>, France"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "ACRI-ST, 260 Route du Pin Montard, Sophia-Antipolis, 06410 Biot, France"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ACRI-ST, 260 Route du Pin Montard, Sophia-Antipolis, 06410 Biot, France"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "EUMETSAT, Darmstadt, Germany"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Univ. Littoral Côte d'Opale, Univ. Lille, CNRS, UMR 8187, LOG, Laboratoire d'Océanologie et de Géosciences, F 62930 <PERSON><PERSON><PERSON>, France"}], "References": [{"Title": "ACIX-Aqua: A global assessment of atmospheric correction methods for Landsat-8 and Sentinel-2 over lakes, rivers, and coastal waters", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>lan <PERSON><PERSON>", "PubYear": 2021, "Volume": "258", "Issue": "", "Page": "112366", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A three-step semi analytical algorithm (3SAA) for estimating inherent optical properties over oceanic, coastal, and inland waters from remote sensing reflectance", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "263", "Issue": "", "Page": "112537", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 90148838, "Title": "Performance Assessment of Fuzzy Logic Control Approach for MR-Damper Based-Transfemoral Prosthetic Leg", "Abstract": "Transfemoral amputation commonly occurs due to some stroke, diabetes, physical or mental trauma, which reduces the person's movement capability. Therefore, an efficient prosthetic leg is essential to improve the life of an amputee by replacing the lost limb. This letter addresses the fuzzy logic-based control strategy for magneto-rheological damper based prosthetic leg for transfemoral amputees. The primary focus of this letter is to present the performance analysis of the control to achieve the entire gait cycle (both swing and stance phases) for a transfemoral prosthesis. The performance and robustness analysis of the developed prosthetic leg is tested for real-time gait data to obtain the precise and more natural gait by the transfemoral amputee.", "Keywords": "Cuckoo search algorithm (CSA);fuzzy logic control (FLC);gait analysis;prosthetic leg", "DOI": "10.1109/TAI.2021.3106884", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Biomedical Engineering, Indian Institute of Technology Delhi, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Instrumentation and Control Engineering, Netaji Subhas University of Technology, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Biomedical Engineering, Indian Institute of Technology Delhi, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Biomedical Engineering, Indian Institute of Technology Delhi, New Delhi, India"}], "References": [{"Title": "Wavelet Interval Type-2 Fuzzy Quad-Function-Link Brain Emotional Control Algorithm for the Synchronization of 3D Nonlinear Chaotic Systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Tien-Lo<PERSON> Le", "PubYear": 2020, "Volume": "22", "Issue": "8", "Page": "2546", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 90148991, "Title": "Defensible Stalking Practice the Grounded Continuously Silhouette up-to-the-minute Wireless Assessing in Tactful Linkages", "Abstract": "<p>Abstract— This paper proposes a Peripheral Silhouette Chasing Technique (PSCT) that lessens vigor feasting for stalking movable boards in wireless stratagem nets in footings of detecting and message vigor ingesting. PSCT preserves get-up-and-go by hire only a least quantity of feeler bulges contribute in announcement and accomplish detecting for objective pursuing. The trifling succeeding extent founded on the vehicular kinematics. The showing of objective’s kinematics agrees for clipping available share of the stalking part that cannot be instinctively visited by the movable objective within reserved time. So, sends the stalking expanse material to only the instrument bulges within negligible stalking expanse and wakes them up. Equated to the inheritance pattern which uses sphere-based pursuing expanse, our suggested structure uses not as much of integer of antennae for stalking in cooperation epistle and distinguishing deprived of objective gone. From side to side recreation, us show that PSCT leave behind the round-constructed arrangement with near 70% vitality exchangeable beneath convinced epitome circumstances.\r Index Term — Stratagem Linkage, Objective Stalking, Vigor, Stalking Expanse, Peripatetic Objective, Automobile, Kinematics, Round, Recognizing, Decree, Optimization.\r  \r  </p>", "Keywords": "", "DOI": "10.18535/ijecs/v10i8.4610", "PubYear": 2021, "Volume": "10", "Issue": "8", "JournalId": 17925, "JournalTitle": "International Journal Of Engineering And Computer Science", "ISSN": "", "EISSN": "2319-7242", "Authors": [{"AuthorId": 1, "Name": "Ms. <PERSON>. M", "Affiliation": "Research Scholar, Department of Computer Science, Defence Institute of Advanced Technology, Pune-411 025"}, {"AuthorId": 2, "Name": "Dr. <PERSON><PERSON>.", "Affiliation": "Researcher, MED & COS Defence Research & Development Organisation (DRDO) Pune- 411 021"}], "References": []}, {"ArticleId": 90149205, "Title": "A comprehensive survey on Green ICT with 5G-NB-IoT: Towards sustainable planet", "Abstract": "Rapid advancement in ICT is promoting us into an era of unprecedented prosperity & countless possibilities. However, there is one gloomy side of the ICT technology that contributes toward the inflation of carbon footprint. Research from 2020, estimates the ICT sector, carbon emission, to be 1,100 million tons. The future generation networks and IoT will further escalate this figure, as these would overburden the core ICT pillars i.e. Data Centers (DC's), and Mobile networks (NT). This in turn will inflate the ICT power consumption and leads to more carbon emission. Thus researchers and industries are continuously putting efforts to transform ICT into Green ICT. Apart from this, there is one bright side of ICT i.e. “Green BY ICT” that helps other industries to abate their carbon emission using smart IoT applications. However, the smart IoT devices/sensors/actuators used for this are mostly battery-operated. To reduce the battery waste, efforts are also being made to either prolong their battery life or to make them self-powered or battery-free. This survey discusses both aspects of ICT i.e. Green of ICT and Green by ICT. Firstly, the recent approaches for the Greening of ICT include techniques for Green-DC, Green-NT are discussed. Post discussing this, the paper also confers the energy harvesting solutions & energy-efficient techniques for the greening of user device/senor. In continuation of this, 5G green physical layer solution, Narrowband Internet of Things (NB-IoT) that prolongs battery life is also discussed, including its enhancement from release 13 to release 16, recent techniques to further optimize the NB-IoT performance, and future research challenges. Apart from this the recent advancement related to renewable energy solutions for Green ICT is also discussed. Overall this survey concludes that ICT's own environmental impact must be evaded, to utilize the ICT's tremendous potential.", "Keywords": "Green Information and communication technology (Green ICT) ; Data center ; Access network ; Narrow band internet of things (NB-IoT) ; Green communication ; Smart health", "DOI": "10.1016/j.comnet.2021.108433", "PubYear": 2021, "Volume": "199", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Scholar in School of Computer Science and Engineering, Shri <PERSON> Devi University, J&K, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology, Design and Manufacturing, Jabalpur, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology, Design and Manufacturing, Jabalpur, India"}], "References": [{"Title": "A software-defined caching scheme for the Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "158", "Issue": "", "Page": "178", "JournalTitle": "Computer Communications"}, {"Title": "IoT based smart agrotech system for verification of Urban farming parameters", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "104025", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A vehicle to vehicle relay-based task offloading scheme in Vehicular Communication Networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 90149226, "Title": "Analyzing default risk among P2P platforms based on the LAS-STACK method by considering multidimensional signals under specific economic contexts", "Abstract": "<p>P2P platform default risk seriously affects the returns of investors, which may cause systemic financial risks. The existing literature mostly focuses on borrower risk, ignoring the research on P2P platform default risk. This paper uses signal theory and data mining-related methods to study the default risk prediction of P2P platforms that integrate soft and hard information signals in different economic environments. First, using the cluster analysis method, the macroeconomic environment of P2P platforms is studied. Second, from the perspective of signal costs, signal theory is used to analyze the impacts of soft and hard information risk signals on platform default in different economic environments. Finally, by integrating the lasso and stacking methods, a LAS-STACK model is proposed to study the prediction of P2P platform default risk in the high-dimensional unbalanced data context. The conclusions of this paper show that the fusion of soft and hard information can better predict the default risk of P2P platforms, especially during periods with low economic levels. Additionally, the LAS-STACK model has a better prediction ability for the P2P platform default risk in the high-dimensional unbalanced data context. This study can improve the ability of regulators and P2P platforms to warn and manage default risks in a specific economic environment and protect investors' returns.</p>", "Keywords": "P2P platform; Default risk; LAS-STACK; Signal theory", "DOI": "10.1007/s10660-021-09505-9", "PubYear": 2022, "Volume": "22", "Issue": "1", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Anhui University, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business, Anhui University, Hefei, China"}, {"AuthorId": 3, "Name": "Cuiqing Jiang", "Affiliation": "School of Management, Hefei University of Technology, Hefei, China"}], "References": [{"Title": "Analyzing credit risk among Chinese P2P-lending businesses by integrating text-related soft information", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "40", "Issue": "", "Page": "100947", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Analyzing credit risk among Chinese P2P-lending businesses by integrating text-related soft information", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "40", "Issue": "", "Page": "100947", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Automatic clustering algorithms: a systematic review and bibliometric analysis of relevant literature", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Moyinoluwa B. Agbaje", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "6247", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Automatic clustering algorithms: a systematic review and bibliometric analysis of relevant literature", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Moyinoluwa B. Agbaje", "PubYear": 2021, "Volume": "33", "Issue": "11", "Page": "6247", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A stacking ensemble classifier with handcrafted and convolutional features for wafer map pattern classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "", "Page": "103450", "JournalTitle": "Computers in Industry"}, {"Title": "A stacking ensemble classifier with handcrafted and convolutional features for wafer map pattern classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "", "Page": "103450", "JournalTitle": "Computers in Industry"}, {"Title": "Multi-dimensional feature fusion and stacking ensemble mechanism for network intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "122", "Issue": "", "Page": "130", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-dimensional feature fusion and stacking ensemble mechanism for network intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "122", "Issue": "", "Page": "130", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 90149280, "Title": "Multiple-objective optimization applied in extracting multiple-choice tests", "Abstract": "Student evaluation is an essential part of education and is usually done through examinations. These examinations generally use tests consisting of several questions as crucial factors to determine the quality of the students. Test-making can be thought of as a multi-constraint optimization problem. However, the test-making process that is done by either manually or randomly picking questions from question banks still consumes much time and effort. Besides, the quality of the tests generated is usually not good enough. The tests may not entirely satisfy the given multiple constraints such as required test durations, number of questions, and question difficulties. In this paper, we propose parallel strategies, in which parallel migration is based on Pareto optimums, and applyan improved genetic algorithm called a genetic algorithm combined with simulated annealing, GASA, which improves diversity and accuracy of the individuals by encoding schemes and a new mutation operator of GA to handle the multiple objectives while generating multiple choice-tests from a large question bank. The proposed algorithms can use the ability to exploit historical information structure in the discovered tests, and use this to construct desired tests later. Experimental results show that the proposed approaches are efficient and effective in generating valuable tests that satisfy specified requirements. In addition, the results, when compared with those from traditional genetic algorithms, are improved in several criteria including execution time, search speed, accuracy, solution diversity, and algorithm stability.", "Keywords": "Multiple-choice test ; Test construction ; Multiple objective optimization ; Test-question bank ; Simulated annealing ; Genetic algorithm", "DOI": "10.1016/j.engappai.2021.104439", "PubYear": 2021, "Volume": "105", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Nong Lam University, Ho Chi Minh City, Viet Nam;Department of Computer Science, Faculty of Electrical Engineering and Computer Science, VŠB - Technical University of Ostrava, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Viet Nam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Andalusian Research Institute in Data Science and Computational Intelligence (DaSCI), University of Granada, Granada, Spain;Faculty of Software and Information Science, Iwate Prefectural University (IPU), Iwate, Japan"}, {"AuthorId": 4, "Name": "Tzung-<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Engineering, National University of Kaohsiung, Kaohsiung, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Viet Nam"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Electrical Engineering and Computer Science, VŠB - Technical University of Ostrava, Czech Republic"}, {"AuthorId": 7, "Name": "Bay Vo", "Affiliation": "Faculty of Information Technology, Ho Chi Minh City University of Technology (HUTECH), Ho Chi Minh City, Viet Nam;Corresponding author"}], "References": [{"Title": "Parallel Genetic Algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A new hybrid model for wind speed forecasting combining long short-term memory neural network, decomposition methods and grey wolf optimizer", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106996", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 90149285, "Title": "Prediction framework for upper body sedentary working behaviour by using deep learning and machine learning techniques", "Abstract": "<p>Public health experts and healthcare professionals are gradually identifying sedentary activity as a population-wide, pervasive health risk. The purpose of this paper is to propose a method to identify the changes in posture during sedentary work and to give feedback by analysing the identified posture of the upper body, i.e. hands, shoulder, and head positioning. After capturing the image of the human pose, pre-processing of the image takes place with a bandpass filter, which helps to reduce the noise and morphological operation, which is used to carry out the process of dilation, erosion and opening of an image. To predict the results easily with the use of texture feature extraction, it helps to extract the image's feature. Then, accuracy is predicted by using the deep neural network techniques, to predict the result accurately. After prediction and analysis, the feedback system is developed to alert individuals through the alarm system. The proposed method is formulated by using DNN for prediction in the MATLAB software tool. The results show accuracy, sensitivity and specificity of the prediction using a deep neural network are 97.2%, 88.7% and 99.1%. The proposed method is compared with the existing methods SVM, Random Forest and KNN algorithms. The accuracy, sensitivity and specificity of the existing algorithms are SVM with 77.6%, 57.4 and 97.8%; Random Forest with 80.6%, 63.7% and 97.5%; and KNN with 65.8%, 61.2%, and 95.1%. This concept helps to prevent the impact of sedentary activity on fatal and non-fatal cardiovascular and musculoskeletal diseases, respectively.</p><p>© The Author(s), under exclusive licence to Springer-Verlag GmbH Germany, part of Springer Nature 2021.</p>", "Keywords": "DNN;KNN;MATLAB software;Random forest;SVM;Sedentary behaviour", "DOI": "10.1007/s00500-021-06156-8", "PubYear": 2022, "Volume": "26", "Issue": "23", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Mechatronics, Kaunas University of Technology, Kaunas, Lithuania."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Mechatronics, Kaunas University of Technology, Kaunas, Lithuania."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mechatronics, Kaunas University of Technology, Kaunas, Lithuania."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Kaunas University of Technology, Kaunas, Lithuania."}], "References": [{"Title": "Monocular human pose estimation: A survey of deep learning-based methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "192", "Issue": "", "Page": "102897", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "BeAware: Convolutional neural network(CNN) based user behavior understanding through WiFi channel state information", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "397", "Issue": "", "Page": "457", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 90149294, "Title": "Link quality estimation method based on gradient boosting decision tree", "Abstract": "In the application of wireless sensor networks, link quality estimation is the primary problem to guarantee the reliable transmission of data and the performance of the upper layer network protocol. In order to accurately evaluate link quality, a link quality estimator based on gradient boosting decision tree (GBDT) was proposed. The physical layer parameter average received signal strength indication, mean link quality indicator and mean signal noise rate is selected as the input of the GBDT estimator and the nonlinear correlation between physical layer parameters and packet received rate is analysed by using the maximum information coefficient method. Considering the influence between outliers and different dimensionality of parameters, we used the boxplot method to carry out smoothing and normalisation processing to reduce the complexity of the estimator. At last, the improved particle swarm optimisation algorithm is used to select the optimal parameter combination in the GBDT estimator. The experimental results show that compared with the support vector machine (SVM) estimator, the estimator of this paper has higher accuracy and stability. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "GBDT; Gradient boosting decision tree; Link quality; Maximum information coefficient; Particle swarm optimisation; PSO; Wireless sensor networks", "DOI": "10.1504/IJSNET.2021.117232", "PubYear": 2021, "Volume": "36", "Issue": "3", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Software, Nanchang Hangkong University, 696 Fenghe South Avenue, Jiangxi Province, Nanchang, 330063, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Nanchang Hangkong University, 696 Fenghe South Avenue, Jiangxi Province, Nanchang, 330063, China"}], "References": []}]