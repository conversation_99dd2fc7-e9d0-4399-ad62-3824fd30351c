[{"ArticleId": 104711042, "Title": "Source Matching and Rewriting for MLIR Using String-Based Automata", "Abstract": "<p> A typical compiler flow relies on a uni-directional sequence of translation/optimization steps that lower the program abstract representation, making it hard to preserve higher-level program information across each transformation step. On the other hand, modern ISA extensions and hardware accelerators can benefit from the compiler’s ability to detect and raise program idioms to acceleration instructions or optimized library calls. Although recent works based on Multi-Level IR (MLIR) have been proposed for code raising, they rely on specialized languages, compiler recompilation, or in-depth dialect knowledge. This article presents Source Matching and Rewriting (SMR), a user-oriented source-code-based approach for MLIR idiom matching and rewriting that does not require a compiler expert’s intervention. SMR uses a two-phase automaton-based DAG-matching algorithm inspired by early work on tree-pattern matching. First, the idiom Control-Dependency Graph (CDG) is matched against the program’s CDG to rule out code fragments that do not have a control-flow structure similar to the desired idiom. Second, candidate code fragments from the previous phase have their Data-Dependency Graphs (DDGs) constructed and matched against the idiom DDG. Experimental results show that SMR can effectively match idioms from Fortran (FIR) and C (CIL) programs while raising them as BLAS calls to improve performance. Additional experiments also show performance improvements when using SMR to enable code replacement in areas like approximate computing and hardware acceleration. </p>", "Keywords": "", "DOI": "10.1145/3571283", "PubYear": 2023, "Volume": "20", "Issue": "2", "JournalId": 10048, "JournalTitle": "ACM Transactions on Architecture and Code Optimization", "ISSN": "1544-3566", "EISSN": "1544-3973", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing - UNICAMP, Campinas, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Computing - UNICAMP, Campinas, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing - UNICAMP, Campinas, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Computing - UNICAMP, Campinas, Brazil"}], "References": [{"Title": "KernelFaRer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}, {"Title": "NekRS, a GPU-accelerated spectral element Navier–Stokes solver", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "102982", "JournalTitle": "Parallel Computing"}]}, {"ArticleId": 104711053, "Title": "Android-Based Short Message Service Filtering using Long Short-Term Memory Classification Model", "Abstract": "<p>Short Message Service (SMS) is a technology for sending messages in text format between two mobile phones that support such a facility. Despite the emergence of many mobile text messaging applications, SMS still finds its use in communication among people and broadcasting messages by governments and mobile providers. SMS users often receive messages from parties, particularly for marketing and business purposes, advertisements, or elements of fraud. Many of those messages are irrelevant and fraudulent spam. This research aims at developing android-based applications that enable the filtering of SMS in Bahasa Indonesia. We investigate 1469 SMS text data and classify them into three categories: Normal, Fraudulent, and Advertisement. The classification or filtering method is the long short-term memory (LSTM) model from TensorFlow. The LSTM model is suitable because it has cell states in the architecture that are useful for storing previous information. The feature is applicable for use on sequential data such as SMS texts because every word in the texts constructs a sequential form to complete a sentence. The observation results show that the classification accuracy level is 95%. This model is then integrated into an Android-based mobile application to execute a real-time classification.</p>", "Keywords": "text filtering;recurrent neural network;long short term memory", "DOI": "10.23917/khif.v8i2.17995", "PubYear": 2022, "Volume": "8", "Issue": "2", "JournalId": 49224, "JournalTitle": "Khazanah Informatika: <PERSON><PERSON> Ilmu <PERSON> dan Informatika", "ISSN": "", "EISSN": "2477-698X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Mataram"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Mataram"}, {"AuthorId": 3, "Name": "I <PERSON> Bud<PERSON>", "Affiliation": "Universitas Mataram"}, {"AuthorId": 4, "Name": "Indira Puteri <PERSON>", "Affiliation": "Universitas Negeri Mataram"}], "References": []}, {"ArticleId": 104711060, "Title": "Deep learning-based dimensional emotion recognition combining the attention mechanism and global second-order feature representations", "Abstract": "For the emotion recognition tasks, the emotion-related features and the emotionally irrelevant ones are fully extracted by the deep neural network is particularly desirable. In this paper, a dimensional emotion recognition algorithm is proposed by combing the attention mechanism and global second-order feature representations. More specially, the residual attention network (RAN) is utilized to select the features related to the task, and then the global second-order pooling network is employed to model the correlation between these features to complete the modeling of the feature interdependence relationship, improving the deficiency of the RAN in this point so as to enhance the capacity of extracting the emotion-related features and the emotionally irrelevant ones. The experiments on the AffectNet dataset demonstrate the validity of the proposed model, which is comparable to and even superior to those from the support vector regression method, the convolutional neural network (CNN)-based method, and the recently-developed CNN-based variants.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108469", "PubYear": 2022, "Volume": "104", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Sun", "Affiliation": "Department of Communication Engineering, School of Automation and Information Engineering, Xi'an University of Technology, Xi'an 710048, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, School of Automation and Information Engineering, Xi'an University of Technology, Xi'an 710048, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, School of Automation and Information Engineering, Xi'an University of Technology, Xi'an 710048, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Communication Engineering, School of Automation and Information Engineering, Xi'an University of Technology, Xi'an 710048, China"}], "References": []}, {"ArticleId": 104711263, "Title": "Combined depth and heading control and experiment of ROV under the influence of residual buoyancy, current disturbance, and control dead zone", "Abstract": "<p>To improve the underwater control effect of a Remotely Operated Vehicle (ROV) with residual buoyancy, current disturbance, and control dead zone, the depth and heading combined control of ROV is studied to improve the control accuracy of the control system. First, the heading control with fixed depth is divided into heading control and depth control. The tanh -sigmoid-surface control laws for designed degrees of freedom are designed by using tanh function. To suppress the influence of residual buoyancy and control law dead zone in depth control, and to offset the influence of control law dead zone of ROV thruster control, a reserved control quantity is introduced to map the depth deviation and control dead zone with residual buoyancy into a control deviation quantity. An adaptive amplification factor method is proposed for the amplification factors of depth control, speed control, and heading control. The proportional coefficient is adopted to make that the balance among rise time, convergence speed, and overshoot can be achieved by adjusting the proportional coefficient. Then the corresponding tanh -sigmoid-surface controller module is designed in MOOS-IvP environment to track the desired heading and depth. The proposed controller refines fuzzy rules and reduces the complexity of parameter adjustment. Compared with the classical proportional, integral, and derivative control method, the experiment results show that the proposed method can resist the influence of residual buoyancy, current disturbance, and control dead zone and has a better control effect with less control error in depth and heading determination.</p>", "Keywords": "control dead zone;current disturbance;MOOS-IvP;residual buoyancy;ROV;tanh-sigmoid-surface control law", "DOI": "10.1002/rob.22132", "PubYear": 2023, "Volume": "40", "Issue": "2", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Oceanographic Instrumentation, Qilu University of Technology (Shandong Academy of Sciences), Shandong Provincial Key Laboratory of Ocean Environment Monitoring Technology National Engineering and Technological Research Center of Marine Monitoring Equipment Qingdao Shandong China;Key Laboratory of Ocean Observation Technology, MNR National Ocean Technology Center Tianjin China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Oceanographic Instrumentation, Qilu University of Technology (Shandong Academy of Sciences), Shandong Provincial Key Laboratory of Ocean Environment Monitoring Technology National Engineering and Technological Research Center of Marine Monitoring Equipment Qingdao Shandong China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Oceanographic Instrumentation, Qilu University of Technology (Shandong Academy of Sciences), Shandong Provincial Key Laboratory of Ocean Environment Monitoring Technology National Engineering and Technological Research Center of Marine Monitoring Equipment Qingdao Shandong China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Oceanographic Instrumentation, Qilu University of Technology (Shandong Academy of Sciences), Shandong Provincial Key Laboratory of Ocean Environment Monitoring Technology National Engineering and Technological Research Center of Marine Monitoring Equipment Qingdao Shandong China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering Qingdao City University  Qingdao China"}], "References": []}, {"ArticleId": 104711588, "Title": "Data sovereignty in genomics and medical research", "Abstract": "AI promises to bring many benefits to healthcare and research, but mistrust has built up owing to many instances of harm to under-represented communities. To amend this, participatory approaches can directly involve communities in AI research that will impact them. An important element of such approaches is ensuring that communities can take control over their own data and how they are shared.", "Keywords": "Computer science;Genomics;Scientific community;Engineering;general", "DOI": "10.1038/s42256-022-00578-1", "PubYear": 2022, "Volume": "4", "Issue": "11", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [], "References": [{"Title": "Secure, privacy-preserving and federated machine learning in medical imaging", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "6", "Page": "305", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 104711655, "Title": "Multiple time series database on microservice architecture for IoT-based sleep monitoring system", "Abstract": "<p>Monitoring health status requires collecting a large amount of data from the human body. The sensor can be used to collect data from the human body. The sensor transmits data for almost every second across the internet. The challenge of the health monitoring system is the massive amount of incoming data. Therefore, a system capable of sending, storing, analyzing, and visualizing vast amounts of data is required for health monitoring. A previous study proposed microservice and event-driven architecture. It also proposed a single database for all services and a relational database management system (RDBMS) for storing time series data, which might reduce the data transmission performance and reliability. This research intends to improve the monitoring system from the previous study to accommodate a greater throughput, faster database read and write operations, and a more reliable system design. The improvement consists of multiple changes in system architecture and technology. A multi-database is proposed in the system architecture to improve system reliability. Time series database and Message Queue Telemetry Protocol (MQTT) server are proposed as an upgrade on technology. As a result, the proposed system throughput is 2.43 times faster than the old system. On database performance, the new system's database write speed is 20.95 times faster and the database read speed is 1.64 times faster than the old system. The proposed system also achieves better scalability, resilience, and independence.</p>", "Keywords": "IoT;Time series database;Health monitoring;Microservice;MQTT protocol", "DOI": "10.1186/s40537-022-00658-4", "PubYear": 2022, "Volume": "9", "Issue": "1", "JournalId": 2151, "JournalTitle": "Journal of Big Data", "ISSN": "", "EISSN": "2196-1115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "BINUS Graduate Program-Master of Computer Science, Bina Nusantara University, Jakarta, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "BINUS Graduate Program-Master of Computer Science, Bina Nusantara University, Jakarta, Indonesia; Department of Electrical, Electronic and Communication Engineering, Faculty of Engineering, Tokyo City University, Tokyo, Japan"}], "References": []}, {"ArticleId": 104711739, "Title": "The optimal asset trading settlement based on Proof-of-Stake blockchains", "Abstract": "The Proof-of-Stake (PoS) protocol is booming in blockchain networks because of excessive energy consumption and slow block generation associated with the Proof-of-Work (PoW) protocol. In terms of transaction settlement, does the PoS protocol perform better than PoW? We build a transaction settlement model based on the PoS blockchain, describing how the staking income and costs affect settlement performance, and put forward a Successful Settlement Factor ( SSF ) to determine whether the settlement will be successful given the block size and block time. In addition, the system developers can adjust block size and block time according to SSF to achieve the best settlement performance. Finally, using historical data for a real-world blockchain network, our model demonstrates that the optimal block time, average settlement time, average settlement fees, and total settlement lag of the PoS blockchain are 23.41%, 23.29%, 23.23%, 23.31%, respectively, less than that of the PoW blockchain.", "Keywords": "", "DOI": "10.1016/j.dss.2022.113909", "PubYear": 2023, "Volume": "166", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Beihang University, Beijing 100191, China;Key Laboratory of Complex System Analysis, Management and Decision, Ministry of Education, Beihang University, Beijing 100191, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Beihang University, Beijing 100191, China;Key Laboratory of Complex System Analysis, Management and Decision, Ministry of Education, Beihang University, Beijing 100191, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Economics and Management, Beihang University, Beijing 100191, China;Beijing Advanced Innovation Center for Big Data and Brain Computing, Beihang University, Beijing 100191, China;Corresponding author at: No. 37, Xueyuan Road, Haidian District, Beijing 100191, China"}], "References": [{"Title": "Performance analysis and comparison of PoW, PoS and DAG based blockchains", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "480", "JournalTitle": "Digital Communications and Networks"}, {"Title": "A survey of blockchain consensus algorithms performance evaluation criteria", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "113385", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A survey of consensus algorithms in public blockchain systems for crypto-currencies", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "103035", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Aligning the interests of newsvendors and forecasters through blockchain-based smart contracts and proper scoring rules", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "113626", "JournalTitle": "Decision Support Systems"}, {"Title": "Bitcoin price forecasting: A perspective of underlying blockchain transactions", "Authors": "Hai<PERSON> Guo; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "113650", "JournalTitle": "Decision Support Systems"}]}, {"ArticleId": 104711930, "Title": "Maf1 is an intrinsic suppressor against spontaneous neural repair and functional recovery after ischemic stroke", "Abstract": "<b  >Introduction</b> Spontaneous recovery after CNS injury is often very limited and incomplete, leaving most stroke patients with permanent disability. Maf1 is known as a key growth suppressor in proliferating cells. However, its role in neuronal cells after stroke remains unclear. <b  >Objective</b> We aimed to investigate the mechanistic role of Maf1 in spontaneous neural repair and evaluated the therapeutic effect of targeting Maf1 on stroke recovery. <b  >Methods</b> We used mouse primary neurons to determine the signaling mechanism of Maf1, and the cleavage-under-targets-and-tagmentation-sequencing to map the whole-genome promoter binding sites of Maf1 in isolated mature cortical neurons. Photothrombotic stroke model was used to determine the therapeutic effect on neural repair and functional recovery by AAV-mediated Maf1 knockdown. <b  >Results</b> We found that Maf1 mediates mTOR signaling to regulate RNA polymerase III (Pol III)-dependent rRNA and tRNA transcription in mouse cortical neurons. mTOR regulates neuronal Maf1 phosphorylation and subcellular localization. Maf1 knockdown significantly increases Pol III transcription, neurite outgrowth and dendritic spine formation in neurons. Conversely, Maf1 overexpression suppresses such activities. In response to photothrombotic stroke in mice, Maf1 expression is increased and accumulates in the nucleus of neurons in the peripheral region of infarcted cortex, which is the key region for neural remodeling and repair during spontaneous recovery. Intriguingly, Maf1 knockdown in the peri-infarct cortex significantly enhances neural plasticity and functional recovery. Mechanistically, Maf1 not only interacts with the promoters and represses Pol III-transcribed genes, but also those of CREB-associated genes that are critical for promoting plasticity during neurodevelopment and neural repair. <b  >Conclusion</b> These findings indicate Maf1 as an intrinsic neural repair suppressor against regenerative capability of mature CNS neurons, and suggest that Maf1 is a potential therapeutic target for enhancing functional recovery after ischemic stroke and other CNS injuries.", "Keywords": "Maf1;axon regeneration;functional recovery and neural repair;ischemic stroke;mTOR", "DOI": "10.1016/j.jare.2022.11.007", "PubYear": 2023, "Volume": "51", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Clinical Neuroscience Institute, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China; Rutgers Cancer Institute of New Jersey, 195 Little Albany Street, New Brunswick, New Jersey 08903, USA. Electronic address:  ."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Clinical Neuroscience Institute, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China; Department of Neurology, The First Clinical Medical School of Jinan University, Guangzhou, China."}, {"AuthorId": 3, "Name": "Guangpu Su", "Affiliation": "Clinical Neuroscience Institute, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China; Department of Neurology, The First Clinical Medical School of Jinan University, Guangzhou, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cell Biology and Neuroscience, Rutgers, the State University of New Jersey, Piscataway, New Jersey 08854, USA; Present address: College of Pharmacy, Chosun University, Gwangju, South Korea."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clinical Neuroscience Institute, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China; Department of Neurology, The First Clinical Medical School of Jinan University, Guangzhou, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Cell Biology and Neuroscience, Rutgers, the State University of New Jersey, Piscataway, New Jersey 08854, USA."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Clinical Neuroscience Institute, The First Affiliated Hospital of Jinan University, Guangzhou 510630, China; Department of Neurology, The First Clinical Medical School of Jinan University, Guangzhou, China; Department of Neurology and Stroke Center, The First Affiliated Hospital, Jinan University Guangzhou, Guangdong, China. Electronic address:  ."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Rutgers Cancer Institute of New Jersey, 195 Little Albany Street, New Brunswick, New Jersey 08903, USA. Electronic address:  ."}], "References": []}, {"ArticleId": 104712022, "Title": "Neural referential form selection: Generalisability and interpretability", "Abstract": "In recent years, a range of Neural Referring Expression Generation (REG) systems have been built and they have often achieved encouraging results. However, these models are often thought to lack transparency and generality. Firstly, it is hard to understand what these neural REG models can learn and to compare their performance with existing linguistic theories. Secondly, it is unclear whether they can generalise to data in different text genres and different languages. To answer these questions, we propose to focus on a sub-task of REG: Referential Form Selection (RFS). We introduce the task of RFS and a series of neural RFS models built on state-of-the-art neural REG models. To address the issue of interpretability, we probe these RFS models using probing classifiers that consider information known to impact the human choice of Referential Forms. To address the issue of generalisability, we assess the performance of RFS models on multiple datasets in multiple genres and two different languages, namely, English and Chinese.", "Keywords": "Natural Language Generation ; Referring Expression Generation ; Deep learning ; Probing classifier ; Multilinguality", "DOI": "10.1016/j.csl.2022.101466", "PubYear": 2023, "Volume": "79", "Issue": "", "JournalId": 6012, "JournalTitle": "Computer Speech & Language", "ISSN": "0885-2308", "EISSN": "1095-8363", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Computing Sciences, Utrecht, The Netherlands;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Linguistics, University of Cologne, Cologne, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Computing Sciences, Utrecht, The Netherlands"}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}]}, {"ArticleId": *********, "Title": "Model of process control system in greenhouse agro-industrial complex", "Abstract": "<p>The model of an automated control system for microclimatic indicators and energy consumption of greenhouses has been developed. To design intelligent components and on their basis to synthesize adaptive computer systems for managing the microclimate and energy efficiency of greenhouses is an urgent problem. The purpose of the work is to analyze greenhouse management modes, to make comparative characteristic of the process automation systems, to develop an information scheme of greenhouse-environment interaction, as well as, a structural scheme of greenhouse microclimate control, to select modern technologies to implement automatic climate control systems and to analyze the capabilities of the developed control model of a technological process. The research methods are based on the basic principles of the theory of heat and mass transfer using modern elements of computerized control. The structure of the greenhouse microclimate control system with a variable composition of equipment that provides rapid adaptation to the management requirements of a particular greenhouse has been developed. It has been proposed to develop the components and the structure of a greenhouse microclimate control system with implementation of the comprehensive approach, which includes communication and information management systems and technologies, a modern element base, the Android software, decision support tools. This approach is based on the following principles: consistency, variability of the equipment composition, openness, modularity, and usage of a set of basic design solutions. The microcontroller is the central control device of the entire control system. An availability graph of the client-server communication for the greenhouse microclimate control system, which has two final states, has been developed.</p>", "Keywords": "microclimate;greenhouse;control;automatic control system;microprocessor;hardware;element base", "DOI": "10.26565/2304-6201-2020-47-08", "PubYear": 2020, "Volume": "", "Issue": "47", "JournalId": 68835, "JournalTitle": "Bulletin of V.N. Karazin Kharkiv National University, series «Mathematical modeling. Information technology. Automated control systems»", "ISSN": "2304-6201", "EISSN": "2524-2601", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "V.<PERSON><PERSON> Kharkiv National University, Kharkiv, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "V.<PERSON><PERSON> Kharkiv National University, Kharkiv, Ukraine"}], "References": []}, {"ArticleId": 104712321, "Title": "Model of control access process in wireless computer network", "Abstract": "<p>The problem of ensuring the minimum delivery time of information with prevention of collisions is a pressing problem in ACS TP, since collisions often occur during transmission or the delay time of information is not optimal. This problem is solved by the use of wireless computer networks and methods of information transmission with collision prevention, which affect the delay time of information transmission. The developed model of the access control process in wireless computer networks solves the problem of effective information transfer with collision prevention. The purpose of using this model is to reduce the delay in information transmission. ACS TP often needs modernization as technical innovations are becoming available. At the same time, the quality of production should not fluctuate. The most important element of modernization is the use of wireless technologies that save money and time, compared to the deployment of wired networks. The aim of the work is to develop the model of control access process in a wireless computer network with collision prevention. The process of forming delay slots for information transfer has been researched. The object of the study is a wireless computer network in the ACS TP. The subject of the study is a model of control access process in a wireless computer network with collision prevention. The research objectives are to create requirements for the model, to propose a structure, to create a model, to conduct tests and evaluate the obtained results. The model of access control process in a wireless computer network designed to transmit information with prevention of collisions in a wireless computer network has been developed.</p>", "Keywords": "wireless computer network;collision;frame;slot;station;access control model;process;access mode", "DOI": "10.26565/2304-6201-2020-47-06", "PubYear": 2020, "Volume": "", "Issue": "47", "JournalId": 68835, "JournalTitle": "Bulletin of V.N. Karazin Kharkiv National University, series «Mathematical modeling. Information technology. Automated control systems»", "ISSN": "2304-6201", "EISSN": "2524-2601", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "V.<PERSON><PERSON> Kharkiv National University, Kharkiv, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "V.<PERSON><PERSON> Kharkiv National University, Kharkiv, Ukraine"}], "References": []}, {"ArticleId": 104712350, "Title": "Dataset from healthy and defective spot welds in refill friction stir spot welding using acoustic emission", "Abstract": "The dataset presented in this paper deals with real-time measurements carried out during the welding of 78 spot welds including heathy and defective states. These measurements are composed of acoustic emission signals and welding parameters. Acoustic emission signals were captured by three different piezoelectric sensors, which are connected to a Vallen AMSY5 system through preamplifiers. Welding parameters where digitized using the M-SCOPE software. Both measurements can be used for the establishment of an automatic criterion able to detect defective spot welds in Refill Friction Stir Spot Welding.", "Keywords": "Refill friction stir spot welding ; Process monitoring ; Condition monitoring ; Defect detection ; Acoustic emission", "DOI": "10.1016/j.dib.2022.108750", "PubYear": 2022, "Volume": "45", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Equipe Monitoring et Intelligence Artificielle, Institut de Soudure, 4 boulevard <PERSON>, <PERSON>tz 57970, France;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Equipe Monitoring et Intelligence Artificielle, Institut de Soudure, 4 boulevard <PERSON>, <PERSON>tz 57970, France;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> El Mountassir", "Affiliation": "Equipe Monitoring et Intelligence Artificielle, Institut de Soudure, 4 boulevard <PERSON>, Yutz 57970, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Equipe CND avancés, Institut de Soudure, 4 boulevard <PERSON>, <PERSON>tz 57970, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Equipe Monitoring et Intelligence Artificielle, Institut de Soudure, 4 boulevard <PERSON>, Yutz 57970, France"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Equipe CND avancés, Institut de Soudure, 4 boulevard <PERSON>, <PERSON>tz 57970, France"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Materials Mechanics, Solid State Joining Processes, Helmholtz-Zentrum Hereon, Institute of Materials Research, Max-Planck-St.1, Geesthacht 21502, Germany"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Materials Mechanics, Solid State Joining Processes, Helmholtz-Zentrum Hereon, Institute of Materials Research, Max-Planck-St.1, Geesthacht 21502, Germany"}], "References": [{"Title": "A novel AE algorithm-based approach for the detection of cracks in spot welding in view of online monitoring: case study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "5-6", "Page": "1807", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 104712523, "Title": "Towards SDN-based smart contract solution for IoT access control", "Abstract": "Access control is essential for the IoT environment to ensure that only approved and trusted parties are able to configure devices, access sensor information, and command actuators to execute activities. The IoT ecosystem is subject to various access control complications due to the limited latency between IoT devices and the Internet, low energy requirements of IoT devices, the distributed framework, ad-hoc networks, and an exceptionally large number of heterogeneous IoT devices that need to be managed. The motivation for this proposed work is to resolve the incurring challenges of IoT associated with management and access control security. Each IoT domain implementation has particular features and needs separate access control policies to be considered in order to design a secure solution. This research work aims to resolve the intricacy of policies management, forged policies, dissemination, tracking of access control policies, automation, and central management of IoT nodes and provides a trackable and auditable access control policy management system that prevents forged policy dissemination by applying Software Defined Network (SDN) and blockchain technology in an IoT environment. Integration of SDN and blockchain provides a robust solution for IoT environment security. Recently, smart contracts have become one of blockchain technology’s most promising applications. The integration of smart contracts with blockchain technology provides the capability of designing tamper-proof and independently verifiable policies. In this paper, we propose a novel, scalable solution for implementing immutable, verifiable, adaptive, and automated access control policies for IoT devices together with a successful proof of concept that demonstrates the scalability of the proposed solution. The performance of the proposed solution is evaluated in terms of throughput and resource access delay between the blockchain component and the controller as well as from node to node. The number of nodes in the IoT network and the number of resource access requests were independently and systematically increased during the evaluations. The results illustrate that the resource access delay and throughput were affected neither linearly nor exponentially; hence, the proposed solution shows no significant degradation in performance with an increase in the number of nodes and/or requests.", "Keywords": "Access control ; Blockchain ; Internet of Things ; Smart contract ; Software-defined Networking", "DOI": "10.1016/j.comcom.2022.11.007", "PubYear": 2023, "Volume": "198", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, NUCES, Karachi, 75030, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, NUCES, Karachi, 75030, Pakistan;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, NUCES, Karachi, 75030, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computing and Digital Technology, Birmingham City University, Millennium Point, B4 7XG, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Information Systems Group, Tallinn University of Technology, Akadeemia Tee 15a, 12618 Tallinn, Estonia"}], "References": [{"Title": "A Comprehensive Survey on Attacks, Security Issues and Blockchain Solutions for IoT and IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "102481", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Blockchain-based access control management for Decentralized Online Social Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "41", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "A taxonomy of blockchain-enabled softwarization for secure UAV network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "304", "JournalTitle": "Computer Communications"}, {"Title": "Private blockchain-envisioned multi-authority CP-ABE-based user access control scheme in IIoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "99", "JournalTitle": "Computer Communications"}]}, {"ArticleId": *********, "Title": "User-driven Online Kernel Fusion for SYCL", "Abstract": "<p>Heterogeneous programming models are becoming increasingly popular to support the ever-evolving hardware architectures, especially for new and emerging specialized accelerators optimizing specific tasks. While such programs provide performance portability of the existing applications across various heterogeneous architectures to some extent, short-running device kernels can affect an application performance due to overheads of data transfer, synchronization and kernel launch. While in applications with one or two short-running kernels the overhead can be negligible, it can be noticeable when these short-running kernels dominate the overall number of kernels in an application, as it is the case in graph-based neural network models, where there are several small memory-bound nodes alongside few large compute-bound nodes.</p><p> To reduce the overhead, combining several kernels into a single, more optimized kernel is an active area of research. However, this task can be time-consuming and error-prone given the huge set of potential combinations. This can push programmers to seek a trade-off between (a) task-specific kernels with low overhead but hard to maintain and (b) smaller modular kernels with higher overhead but easier to maintain. While there are DSL-based approaches, such as those provided for machine learning frameworks, which offer the possibility of such a fusion, they are limited to a particular domain and exploit specific knowledge of that domain and, as a consequence, are hard to port elsewhere. This study explores the feasibility of a user-driven kernel fusion through an extension to the SYCL API to address the automation of kernel fusion. The proposed solution requires programmers to define the subgraph regions that are potentially suitable for fusion without any modification to the kernel code or the function signature. We evaluate the performance benefit of our approach on common neural networks and study the performance improvement in detail. </p>", "Keywords": "", "DOI": "10.1145/3571284", "PubYear": 2023, "Volume": "20", "Issue": "2", "JournalId": 10048, "JournalTitle": "ACM Transactions on Architecture and Code Optimization", "ISSN": "1544-3566", "EISSN": "1544-3973", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Codeplay Software Ltd., Scotland, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Codeplay Software Ltd., Scotland, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Codeplay Software Ltd., Scotland, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Codeplay Software Ltd., Scotland, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Codeplay Software Ltd., UK"}], "References": []}, {"ArticleId": 104712633, "Title": "Performance Assessment of a Low-Cost Miniature Electrohydrostatic Actuator", "Abstract": "<p>Low-cost small-scale (<100 W) electrohydrostatic actuators (EHAs) are not available on the market, largely due to a lack of suitable components. Utilizing plastic 3D printing, a novel inverse shuttle valve has been produced which, when assembled with emerging small-scale hydraulic pumps and cylinders from the radio-controlled hobby industry, forms a low-cost and high-performance miniature EHA. This paper presents experimental test results that characterize such a system and highlight its steady, dynamic, and thermal performance capabilities. The results indicate that the constructed EHA has good hydraulic efficiency downstream of the pump and good dynamic response but is limited by the efficiency of the pump and the associated heat generated from the pump’s losses. The findings presented in this paper validate the use of a 3D printed plastic inverse shuttle valve in the construction of a low-cost miniature EHA system.</p>", "Keywords": "", "DOI": "10.3390/act11110334", "PubYear": 2022, "Volume": "11", "Issue": "11", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Saskatchewan, Saskatoon, SK S7N 5A9, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Saskatchewan, Saskatoon, SK S7N 5A9, Canada; Corresponding author"}], "References": [{"Title": "A Low-Cost Miniature Electrohydrostatic Actuator System", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "4", "Page": "130", "JournalTitle": "Actuators"}]}, {"ArticleId": 104712655, "Title": "Density-Based Unsupervised Learning Algorithm to Categorize College Students into Dropout Risk Levels", "Abstract": "<p>Compliance with the basic conditions of quality in higher education implies the design of strategies to reduce student dropout, and Information and Communication Technologies (ICT) in the educational field have allowed directing, reinforcing, and consolidating the process of professional academic training. We propose an academic and emotional tracking model that uses data mining and machine learning to group university students according to their level of dropout risk. We worked with 670 students from a Peruvian public university, applied 5 valid and reliable psychological assessment questionnaires to them using a chatbot-based system, and then classified them using 3 density-based unsupervised learning algorithms, DBSCAN, K-Means, and HDBSCAN. The results showed that HDBSCAN was the most robust option, obtaining better validity levels in two of the three internal indices evaluated, where the performance of the Silhouette index was 0.6823, the performance of the Davies–Bouldin index was 0.6563, and the performance of the Calinski–Harabasz index was 369.6459. The best number of clusters produced by the internal indices was five. For the validation of external indices, with answers from mental health professionals, we obtained a high level of precision in the F-measure: 90.9%, purity: 94.5%, V-measure: 86.9%, and ARI: 86.5%, and this indicates the robustness of the proposed model that allows us to categorize university students into five levels according to the risk of dropping out.</p>", "Keywords": "", "DOI": "10.3390/data7110165", "PubYear": 2022, "Volume": "7", "Issue": "11", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-Coral", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru"}, {"AuthorId": 5, "Name": "<PERSON>-<PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Facultad de Ingeniería de Sistemas e Informática, Universidad Nacional de San Martín, <PERSON><PERSON>, Tarapoto 22200, Peru"}], "References": [{"Title": "A linear multivariate binary decision tree classifier based on K-means splitting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107521", "JournalTitle": "Pattern Recognition"}, {"Title": "An Effective Prediction Model for Online Course Dropout Rate", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "4", "Page": "94", "JournalTitle": "International Journal of Distance Education Technologies"}, {"Title": "A method of two-stage clustering learning based on improved DBSCAN and density peak algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "75", "JournalTitle": "Computer Communications"}, {"Title": "A density-based evolutionary clustering algorithm for intelligent development", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "104396", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Object-based cluster validation with densities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108223", "JournalTitle": "Pattern Recognition"}, {"Title": "Implementation of a Predictive Information System for University Dropout Prevention", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "566", "JournalTitle": "Procedia Computer Science"}, {"Title": "An improved probability propagation algorithm for density peak clustering based on natural nearest neighborhood", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "", "Page": "100232", "JournalTitle": "Array"}]}, {"ArticleId": 104712690, "Title": "How do context-aware artificial intelligence algorithms used in fitness recommender systems? A literature review and research agenda", "Abstract": "Recommender Systems (RS) help the user in the decision-making process when there is a problem of plenty or lack of information. The context-aware recommender systems (CARS) incorporate contextual information to improve the efficiency of RS. Prior works on CARS have compared algorithms and reported application domains. However, the practical challenges involved in CARS implementation with technological advances have not been adequately examined. We chose fitness as the application domain as there is increased user adoption, and the complexity is high. It involves the dynamic interplay of multi-dimensional contexts and technological advances in contextual attributes and incorporates machine learning algorithms in the process of context detection. We conduct textual analysis and conduct a systematic review to identify developments. The concept map demonstrates the components of contextuality, the methods adopted, and the role of AI techniques in amplifying contextuality in the algorithm. The paper identifies the challenges and presents the research opportunities.", "Keywords": "Fitness applications ; Context-aware algorithms ; Recommender systems ; Emerging technologies ; Artificial intelligence", "DOI": "10.1016/j.jjimei.2022.100139", "PubYear": 2022, "Volume": "2", "Issue": "2", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Management, Indore. India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Management, Indore. India"}], "References": [{"Title": "A survey on group recommender systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "2", "Page": "271", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Personalized weight loss strategies by mining activity tracker data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "447", "JournalTitle": "User Modeling and User-Adapted Interaction"}, {"Title": "A recommendation approach for user privacy preferences in the fitness domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "513", "JournalTitle": "User Modeling and User-Adapted Interaction"}, {"Title": "How do users interact with algorithm recommender systems? The interaction of users, algorithms, and performance", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "106344", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Explainable Recommendation: A Survey and New Perspectives", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "Foundations and Trends® in Information Retrieval"}, {"Title": "AI-based mobile context-aware recommender systems from an information management perspective: Progress and directions", "Authors": "<PERSON>-<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "215", "Issue": "", "Page": "106740", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Application of Internet of Things and artificial intelligence for smart fitness: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "189", "Issue": "", "Page": "107859", "JournalTitle": "Computer Networks"}, {"Title": "Applications of text mining in services management: A systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100008", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Addressing the complexity of personalized, context-aware and health-aware food recommendations: an ensemble topic modelling based approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "2", "Page": "229", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Applications of big data in emerging management disciplines: A literature review using text mining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100017", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Using topic models with browsing history in hybrid collaborative filtering recommender system: Experiments with user ratings", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100027", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Deep learning based semantic personalized recommendation system", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100028", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Artificial Intelligence in Tactical Human Resource Management: A Systematic Literature Review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100047", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "\"It's Great to Exercise Together on Zoom!\": Understanding the Practices and Challenges of Live Stream Group Fitness Classes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 104712694, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0747-7171(22)00111-0", "PubYear": 2023, "Volume": "116", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [], "References": []}, {"ArticleId": 104712713, "Title": "Achieving Digital Wellbeing Through Digital Self-control Tools: A Systematic Review and Meta-analysis", "Abstract": "<p> Public media and researchers in different areas have recently focused on perhaps unexpected problems that derive from an excessive and frequent use of technology, giving rise to a new kind of psychological “digital” wellbeing. Such a novel and pressing topic has fostered, both in the academia and in the industry, the emergence of a variety of digital self-control tools allowing users to self-regulate their technology use through interventions like timers and lock-out mechanisms. While these emerging technologies for behavior change hold great promise to support people’s digital wellbeing, we still have a limited understanding of their real effectiveness, as well as of how to best design and evaluate them. Aiming to guide future research in this important domain, this article presents a systematic review and a meta-analysis of current work on tools for digital self-control. We surface motivations, strategies, design choices, and challenges that characterize the design, development, and evaluation of digital self-control tools. Furthermore, we estimate their overall effect size on reducing (unwanted) technology use through a meta-analysis. By discussing our findings, we provide insights on how to (i) overcome a limited perspective that exclusively focuses on technology overuse and self-monitoring tools, (ii) evaluate digital self-control tools through long-term studies and standardized measures, and (iii) bring ethics in the digital wellbeing discourse and deal with the business model of contemporary tech companies. </p>", "Keywords": "", "DOI": "10.1145/3571810", "PubYear": 2023, "Volume": "30", "Issue": "4", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Politecnico di Torino"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Politecnico di Torino"}], "References": [{"Title": "Examining the psychometric properties of the Smartphone Addiction Scale and its short version for use with emerging adults in the U.S", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "100011", "JournalTitle": "Computers in Human Behavior Reports"}, {"Title": "Machine Learning in Mental Health", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "TimeToFocus", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Excessive use of technology", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "1", "Page": "42", "JournalTitle": "Communications of the ACM"}, {"Title": "Understanding User Contexts and Coping Strategies for Context-aware Phone Distraction Management System Design", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>rz<PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "\"Time to Take a Break\"", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 104712717, "Title": "CANOA: CAN Origin Authentication Through Power Side-Channel Monitoring", "Abstract": "<p>The lack of any sender authentication mechanism in place makes Controller Area Network (CAN) vulnerable to security threats. For instance, an attacker can impersonate an Electronic Control Unit (ECU) on the bus and send spoofed messages unobtrusively with the identifier of the impersonated ECU. To address this problem, we propose a novel source authentication technique that uses power consumption measurements of the ECU to authenticate the source of a message. A transmission of an ECU affects the power consumption and a characteristic pattern will appear. Our technique exploits the power consumption of each ECU during the transmission of a message to determine whether the message actually originated from the purported sender. We evaluate our approach in both a lab setup and a real vehicle. We also evaluate our approach against factors that can impact the power consumption measurement of the ECU. The results of the evaluation show that the proposed technique is applicable in a broad range of operating conditions with reasonable computational power requirements and attaining good accuracy.</p>", "Keywords": "", "DOI": "10.1145/3571288", "PubYear": 2024, "Volume": "8", "Issue": "2", "JournalId": 41094, "JournalTitle": "ACM Transactions on Cyber-Physical Systems", "ISSN": "2378-962X", "EISSN": "2378-9638", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Waterloo, Waterloo, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Waterloo, Waterloo, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Waterloo, Waterloo, Canada"}], "References": []}, {"ArticleId": 104712842, "Title": "Learning Deep Asymmetric Tolerant Part Representation", "Abstract": "Categorization objects at a subordinate level inevitably pose a significant challenge, i.e., interclass difference is very subtle and only exists in a few key parts. Therefore, how to localize these key parts for discriminative visual categorization without requiring expensive pixel-level annotations becomes a core question. To that end, this article introduces a novel asymmetric tolerant part segmentation network (ATP-Net). The ATP-Net simultaneously learns to segment parts and identify objects in an end-to-end manner using only image-level category labels. Given the intrinsic asymmetry property of part alignment, a desirable learning of part segmentation should be capable of incorporating such property. Despite the efforts toward regularizing weakly supervised part segmentation, none of them consider this vital and intrinsic property, i.e., the spatial asymmetry of part alignment. Our work, for the first time, proposes to explicitly characterize the spatial asymmetry of part alignment for visual tasks. We propose a novel asymmetry loss function to guide the part segmentation by encoding the spatial asymmetry of part alignment, i.e., restricting the upper bound of how asymmetric those self-similar parts are to each other in the network learning. Via a comprehensive ablation study, we verify the effectiveness of the proposed ATP-Net in driving the network learning toward semantically meaningful part segmentation and discriminative visual categorization. Consistently, superior/competitive performance is reported on 12 datasets covering crop cultivar classification, plant disease classification, bird/butterfly species classification, large-scale natural image classification, attribute recognition, and landmark localization.", "Keywords": "Asymmetric tolerant part representation;visual categorization;weakly supervised part segmentation", "DOI": "10.1109/TAI.2022.3222644", "PubYear": 2023, "Volume": "4", "Issue": "6", "JournalId": 89114, "JournalTitle": "IEEE Transactions on Artificial Intelligence", "ISSN": "", "EISSN": "2691-4581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering and Built Environment, Griffith University, Nathan, QLD, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Australian Institute for Machine Learning, The University of Adelaide, Adelaide, SA, Australia"}, {"AuthorId": 3, "Name": "Yongsheng Gao", "Affiliation": "School of Engineering and Built Environment, Griffith University, Nathan, QLD, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Technology, Wuhan, China"}], "References": [{"Title": "Learning deep part-aware embedding for person retrieval", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107938", "JournalTitle": "Pattern Recognition"}, {"Title": "MaskCOV: A random mask covariance network for ultra-fine-grained visual categorization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "108067", "JournalTitle": "Pattern Recognition"}, {"Title": "SPARE: Self-supervised part erasing for ultra-fine-grained visual categorization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "108691", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": *********, "Title": "An Online Doctor's and Patient help <PERSON>k Assistant for Early Diagnoses of Prostate Cancer in the Rural Areas of Bayelsa State", "Abstract": "<p>The shortage and unavailability of medical practitioners particularly medical doctors in the world has created deficiency in the health sector, which is even more prevalent in the developing countries. This has made rural dwellers in developing countries to experience chronic diseases like prostate cancer, lung cancer, breast cancer and so on which leads to mortality and disabilities. Prostate cancer is a male disease that occurs in the malignant tumors of the prostate epithelium. In this paper, we present an online doctor’s and patient help desk assistant for the diagnoses of prostate cancer at its early stage in the rural areas where there are no health professionals. The system offers medical assistance to patients by providing diagnostic information of their status based on a set of questions answered with respect to prostate cancer. The system is developed with Java Expert System Shell (JESS) alongside other web programming tools. The result from the system evaluation with 50 participants show greater than 95 percent accuracy on expert confirmation.</p>", "Keywords": "Prostate Cancer; Expert System; JESS; Disease", "DOI": "10.32628/CSEIT2283108", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " Wisdom Omote", "Affiliation": "Department of Computer Science, Niger Delta University, Wilberforce Island, PMB581, Bayelsa State, Nigeria"}, {"AuthorId": 2, "Name": " Biralatei Fawei", "Affiliation": "Department of Computer Science, Niger Delta University, Wilberforce Island, PMB581, Bayelsa State, Nigeria"}, {"AuthorId": 3, "Name": " <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 104712970, "Title": "On some bridges to complex evidence theory", "Abstract": "Complex evidence theory, an extension of <PERSON><PERSON><PERSON><PERSON> evidence theory, is a generalized evidence theory based on complex values, which has been widely used to solve decision making of uncertainty information. In order to make a step development based on previous researches, we set out to make a connection between CET and other uncertainty theories like possibility theory, modal logic, fuzzy set theory and probability theory. With a restricted condition of a special generalized consonant belief function, it is found that possibility and necessity measure can be regarded as generalized plausibility measure and belief measure in possibility theory, and the standard interpretation of fuzzy sets in complex evidence theory is obtained by summing up the experience of predecessors and modifying the upper bound of fuzzy sets in this paper. In addition, we established the relationship between generalized plausibility, belief function and modal logic, and elaborate how to explain the complex basic belief distribution with the general semantics of modern modal logic. Finally, the transformation algorithm between complex basic belief assignment and probability distribution is proposed by using the transformation of uncertainty invariance principle, and some properties of them are derived.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105605", "PubYear": 2023, "Volume": "117", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Big Data and Software Engineering, Chongqing University, Chongqing, 401331, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Big Data and Software Engineering, Chongqing University, Chongqing, 401331, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Big Data and Software Engineering, Chongqing University, Chongqing, 401331, China;Correspondence to: School of Big Data and Software Engineering, Chongqing University, No.55 Daxuecheng South Road, Shapingba District, Chongqing, 401331, China"}], "References": [{"Title": "Multiattribute group decision making based on neutrality aggregation operators of q-rung orthopair fuzzy sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "427", "JournalTitle": "Information Sciences"}, {"Title": "Generalization of <PERSON><PERSON><PERSON> theory: A complex mass function", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "10", "Page": "3266", "JournalTitle": "Applied Intelligence"}, {"Title": "Uncertainty measure in evidence theory", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Information Volume of Mass Function", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "6", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "A dynamic group MCDM model with intuitionistic fuzzy set: Perspective of alternative queuing method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "555", "Issue": "", "Page": "85", "JournalTitle": "Information Sciences"}, {"Title": "Multisource basic probability assignment fusion based on information quality", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "4", "Page": "1851", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A dynamic framework of multi-attribute decision making under Pythagorean fuzzy environment by using <PERSON><PERSON><PERSON><PERSON> theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104213", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Conflicting evidence combination from the perspective of networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "408", "JournalTitle": "Information Sciences"}, {"Title": "An improved belief structure satisfaction to uncertain target values by considering the overlapping degree between events", "Authors": "Xinyang <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "398", "JournalTitle": "Information Sciences"}, {"Title": "Probability transformation of mass function: A weighted network method based on the ordered visibility graph", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "104438", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Entropic Explanation of Power Set", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "4", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "An interactive consensus reaching model with updated weights of clusters in large-scale group decision making", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "107", "Issue": "", "Page": "104532", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Towards achieving consistent opinion fusion in group decision making with complete distributed preference relations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107740", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An intelligent quality-based fusion method for complex-valued distributions using POWA operator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "109", "Issue": "", "Page": "104618", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Combining time-series evidence: A complex network model based on a visibility graph and belief entropy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "10706", "JournalTitle": "Applied Intelligence"}, {"Title": "Exponential negation of a probability distribution", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "5", "Page": "2147", "JournalTitle": "Soft Computing"}, {"Title": "Random Permutation Set", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "1", "Page": "", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "Interval-valued intuitionistic fuzzy jenson-shannon divergence and its application in multi-attribute decision making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "14", "Page": "16168", "JournalTitle": "Applied Intelligence"}, {"Title": "Maximum entropy of random permutation set", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "21", "Page": "11265", "JournalTitle": "Soft Computing"}, {"Title": "An interval-valued linguistic Markov decision model with fast convergency", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105158", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An optimization model for rescuer assignments under an uncertain environment by using <PERSON><PERSON><PERSON><PERSON> theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "255", "Issue": "", "Page": "109680", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Complex interval number‐based uncertainty modeling method with its application in decision fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "12", "Page": "11926", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A complex Jensen–Shannon divergence in complex evidence theory with its application in multi-source information fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "105362", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 104713047, "Title": "Analysis of Biharmonic and Harmonic Models by the Methods of Iterative Extensions", "Abstract": "The article describes the results of recent years on the analysis of biharmonic and harmonic models by the methods of iterative extensions. In mechanics, hydrodynamics and heat engineering, various stationary physical systems are modeled using boundary value problems for inhomogeneous Sophie Germain and Poisson equations. Deflection of plates, flows during fluid flows are described using the biharmonic model, i.e. boundary value problem for the inhomogeneous Sophie Germain equation. Deflection of membranes, stationary temperature distributions near the plates are described using the harmonic model, i.e. boundary value problem for the inhomogeneous Poisson equation. With the help of the developed methods of iterative extensions, efficient algorithms for solving the problems under consideration are obtained. © 2022 South Ural State University. All rights reserved.", "Keywords": "biharmonic and harmonic models; methods of iterative extensions", "DOI": "10.14529/mmp220304", "PubYear": 2022, "Volume": "15", "Issue": "3", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South Ural State University, Chelyabinsk, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "South Ural State University, Chelyabinsk, Russian Federation"}], "References": []}, {"ArticleId": 104713113, "Title": "Assessment of L2 intelligibility: Comparing L1 listeners and automatic speech recognition", "Abstract": "<p>An increasing number of studies are exploring the benefits of automatic speech recognition (ASR)–based dictation programs for second language (L2) pronunciation learning (e.g. <PERSON>, Inceoglu & Lim, 2020; Liakin, Cardoso & Liakina, 2015; McC<PERSON><PERSON>, 2019), but how ASR recognizes accented speech and the nature of the feedback it provides to language learners is still largely under-researched. The current study explores whether the intelligibility of L2 speakers differs when assessed by native (L1) listeners versus ASR technology, and reports on the types of intelligibility issues encountered by the two groups. Twelve L1 listeners of English transcribed 48 isolated words targeting the /ɪ-i/ and /æ-ε/ contrasts and 24 short sentences that four Taiwanese intermediate learners of English had produced using Google’s ASR dictation system. Overall, the results revealed lower intelligibility scores for the word task (ASR: 40.81%, L1 listeners: 38.62%) than the sentence task (ASR: 75.52%, L1 listeners: 83.88%), and highlighted strong similarities in the error types – and their proportions – identified by ASR and the L1 listeners. However, despite similar recognition scores, correlations indicated that the ASR recognition of the L2 speakers’ oral productions mirrored the L1 listeners’ judgments of intelligibility in the word and sentence tasks for only one speaker, with significant positive correlations for one additional speaker in each task. This suggests that the extent to which ASR approaches L1 listeners at recognizing accented speech may depend on individual speakers and the type of oral speech.</p>", "Keywords": "automatic speech recognition; intelligibility; CALL; pronunciation learning; non-native speech", "DOI": "10.1017/S0958344022000192", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 12184, "JournalTitle": "ReCALL", "ISSN": "0958-3440", "EISSN": "1474-0109", "Authors": [{"AuthorId": 1, "Name": "Solène Inceoglu", "Affiliation": "Australian National University, Australia ()"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Central University, Taiwan ()"}, {"AuthorId": 3, "Name": "Hyojung Lim", "Affiliation": "Kwangwoon University, South Korea ()"}], "References": []}, {"ArticleId": *********, "Title": "Horn rule discovery with batched caching and rule identifier for proficient compressor of knowledge data", "Abstract": "<p>Knowledge data has been widely applied to artificial intelligence applications for interpretable and complex reasoning. Modern knowledge bases are constructed via automatic knowledge extraction from open-accessible sources. Thus the sizes of KBs are continuously growing, heavily burdening the maintenance and application of the knowledge data. Besides the grammatical redundancies, semantically repeated information also frequently appears in knowledge bases but is still under-explored. Existing semantic compressors fail to efficiently discover expressive patterns and thus perform unsatisfyingly on knowledge data. This article proposes SInC , a semantic inductive compressor, to efficiently induce first-order Horn rules and semantically compress knowledge bases. SInC improves the scalability of top-down rule mining by batching correlated records in the cache and further optimizes the pruning of duplication and specialization via an identifier structure of Horn rules. SInC was evaluated on real-world and synthetic datasets and compared against the state-of-the-art. The results show that the batched caching speed up the rule mining procedure by more than two orders while consuming fewer than three times memory space. The identifier technique speeds up the duplication and specialization pruning by orders of magnitude with less than 5‰ and 15% error rates, respectively. SInC outperforms the state-of-the-art from the perspective of overall compression on both scalability and compression effect.</p>", "Keywords": "caching;Horn rules;knowledge bases;pruning;relational databases;semantic compressor", "DOI": "10.1002/spe.3165", "PubYear": 2023, "Volume": "53", "Issue": "3", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering University of New South Wales  New South Wales Sydney Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering University of New South Wales  New South Wales Sydney Australia;Enhitech LLC.  Shanghai China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering University of New South Wales  New South Wales Sydney Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Newcastle University  Newcastle upon Tyne UK"}], "References": []}, {"ArticleId": *********, "Title": "非負値行列因子分解を用いた筋シナジー解析における筋電チャンネル数削減手法", "Abstract": "Muscle synergy is used for analysis of body movements or as input to a physical assistive device, because they can effectively express features of body movements with a smaller amount of information than the raw signals of surface EMG. Besides, if similar muscle synergy analysis can be achieved with a smaller number of myoelectric channels, it can improve the efficiency of the measurement and to reduce the computational cost. Thus, the autors have proposed a method to reduce the number of myoelectric channels in the muscle synergy analysis by selecting channels according to their magnitude of influence on the temporal patterns of muscle synergy. In this paper, first, the detailed algorithm of the proposed method to evaluate the influence of each myoelectric channel on the temporal patterns of muscle synergy and to select proper channels is presented. Second, as an example to examine the validity of the algorithm, results of muscle synergy analysis of an object lifting motion is described. Then, the presented algorithms is applied to the example to study the relationship between the number of the adopted EMG channels and the results of the revised muscle synergy analysis. Thereby, it is shown that the channel can be selected properly to the purpose.", "Keywords": "muscle synergy;reduction of myoelectric channels;object lifting task", "DOI": "10.9746/sicetr.58.495", "PubYear": 2022, "Volume": "58", "Issue": "11", "JournalId": 24132, "JournalTitle": "Transactions of the Society of Instrument and Control Engineers", "ISSN": "0453-4654", "EISSN": "1883-8189", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Tokyo University of Technology"}, {"AuthorId": 2, "Name": "Kei TSUCHIYA", "Affiliation": "Graduate School of Engineering, Tokyo University of Technology"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Tokyo University of Technology"}], "References": []}, {"ArticleId": 104713296, "Title": "Multi-label sequence generating model via label semantic attention mechanism", "Abstract": "<p>In recent years, a new attempt has been made to capture label co-occurrence by applying the sequence-to-sequence (Seq2Seq) model to multi-label text classification (MLTC). However, existing approaches frequently ignore the semantic information contained in the labels themselves. Besides, the Seq2Seq model is susceptible to the negative impact of label sequence order. Furthermore, it has been demonstrated that the traditional attention mechanism underperforms in MLTC. Therefore, we propose a novel Seq2Seq model with a different label semantic attention mechanism (S2S-LSAM), which generates fused information containing label and text information through the interaction of label semantics and text features in the label semantic attention mechanism. With the fused information, our model can select the text features that are most relevant to the labels more effectively. A combination of the cross-entropy loss function and the policy gradient-based loss function is employed to reduce the label sequence order effect. The experiments show that our model outperforms the baseline models.</p>", "Keywords": "Multi-label text classification; Seq2Seq; Label semantic attention mechanism; Policy gradient", "DOI": "10.1007/s13042-022-01722-4", "PubYear": 2023, "Volume": "14", "Issue": "5", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Engineering Research Center of the Ministry of Education for Intelligent Control System and Intelligent Equipment, Yanshan University, Qinhuangdao, China"}], "References": [{"Title": "Multi-label text classification with latent word-wise label information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "2", "Page": "966", "JournalTitle": "Applied Intelligence"}, {"Title": "Well-calibrated confidence measures for multi-label text classification with a large number of labels", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108271", "JournalTitle": "Pattern Recognition"}, {"Title": "LA-HCN: Label-based Attention for Hierarchical Multi-label Text Classification Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115922", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 104713547, "Title": "ChargEVal — A multi-user framework for simulating and analysing charging station deployment scenarios using agent-based modelling", "Abstract": "ChargEVal is a framework for simulating the performance of charging station deployment scenarios using agent-based modelling (ABM). The ABM utilizes behavioural models for simulating vehicle choice for the trip and charging choice during a trip. ChargEVal can be used by several users to submit multiple simulations simultaneously, using a graphical user interface or programmatically. ChargEVal also has a dedicated results viewer for viewing the simulation summary statistics and agent state values, facilitating detailed insight and simulation comparison. While the current implementation of ChargEVal is specific to the State of Washington, USA; the underlying framework is generic enough to be applied to any geography at any scale.", "Keywords": "Electric vehicles ; Fast-charging ; Electric vehicle supply equipment", "DOI": "10.1016/j.softx.2022.101254", "PubYear": 2022, "Volume": "20", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Chintan Pathak", "Affiliation": "University of Washington, Seattle, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Washington, Seattle, USA"}], "References": []}, {"ArticleId": 104713560, "Title": "Exploring Neuromorphic Computing Based on Spiking Neural Networks: Algorithms to Hardware", "Abstract": "<p>Neuromorphic Computing, a concept pioneered in the late 1980s, is receiving a lot of attention lately due to its promise of reducing the computational energy, latency, as well as learning complexity in artificial neural networks. Taking inspiration from neuroscience, this interdisciplinary field performs a multi-stack optimization across devices, circuits, and algorithms by providing an end-to-end approach to achieving brain-like efficiency in machine intelligence. On one side, neuromorphic computing introduces a new algorithmic paradigm, known as Spiking Neural Networks (SNNs), which is a significant shift from standard deep learning and transmits information as spikes (“1” or “0”) rather than analog values. This has opened up novel algorithmic research directions to formulate methods to represent data in spike-trains, develop neuron models that can process information over time, design learning algorithms for event-driven dynamical systems, and engineer network architectures amenable to sparse, asynchronous, event-driven computing to achieve lower power consumption. On the other side, a parallel research thrust focuses on development of efficient computing platforms for new algorithms. Standard accelerators that are amenable to deep learning workloads are not particularly suitable to handle processing across multiple timesteps efficiently. To that effect, researchers have designed neuromorphic hardware that rely on event-driven sparse computations as well as efficient matrix operations. While most large-scale neuromorphic systems have been explored based on CMOS technology, recently, Non-Volatile Memory (NVM) technologies show promise toward implementing bio-mimetic functionalities on single devices. In this article, we outline several strides that neuromorphic computing based on spiking neural networks (SNNs) has taken over the recent past, and we present our outlook on the challenges that this field needs to overcome to make the bio-plausibility route a successful one.</p>", "Keywords": "Neuromorphic Computing; Spiking Neural Networks; bio-plausible learning; spike-based backpropagation; event cameras; In-Memory Computing; Non-Volatile Memories; asynchronous communication", "DOI": "10.1145/3571155", "PubYear": 2023, "Volume": "55", "Issue": "12", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, Purdue University, West Lafayette, IN"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, Purdue University, West Lafayette, IN"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, Purdue University, West Lafayette, IN"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, Pennsylvania State University, State College, PA, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, Purdue University, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering, Yale University, New Haven, CT, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Computer Engineering, Purdue University, USA"}], "References": [{"Title": "Deep learning incorporating biologically inspired neural dynamics and in-memory computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "6", "Page": "325", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Introducing ‘Neuromorphic Computing and Engineering’", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "010401", "JournalTitle": "Neuromorphic Computing and Engineering"}, {"Title": "Accurate and efficient time-domain classification with adaptive spiking recurrent neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "10", "Page": "905", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Optical flow estimation using the <PERSON> metric", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "024004", "JournalTitle": "Neuromorphic Computing and Engineering"}]}, {"ArticleId": 104713594, "Title": "Gaze-based predictive models of deep reading comprehension", "Abstract": "<p>Eye gaze patterns can reveal user attention, reading fluency, corrective responding, and other reading processes, suggesting they can be used to develop automated, real-time assessments of comprehension. However, past work has focused on modeling factual comprehension, whereas we ask whether gaze patterns reflect deeper levels of comprehension where inferencing and elaboration are key. We trained linear regression and random forest models to predict the quality of users’ open-ended self-explanations (SEs) collected both during and after reading and scored on a continuous scale by human raters. Our models use theoretically grounded eye tracking features (number and duration of fixations, saccade distance, proportion of regressive and horizontal saccades, spatial dispersion of fixations, and reading time) captured from a remote, head-free eye tracker (Tobii TX300) as adult users read a long expository text (6500 words) in two studies ( N = 106 and 131; 247 total). Our models: (1) demonstrated convergence with human-scored SEs ( r = .322 and .354), by capturing both within-user and between-user differences in comprehension; (2) were distinct from alternate models of mind-wandering and shallow comprehension; (3) predicted multiple-choice posttests of inference-level comprehension ( r = .288, .354) measured immediately after reading and after a week-long delay beyond the comparison models; and (4) generalized across new users and datasets. Such models could be embedded in digital reading interfaces to improve comprehension outcomes by delivering interventions based on users’ level of comprehension.</p>", "Keywords": "Self-explanation; Gaze tracking; Reading; Comprehension; Machine learning; Automated assessment", "DOI": "10.1007/s11257-022-09346-7", "PubYear": 2023, "Volume": "33", "Issue": "3", "JournalId": 10998, "JournalTitle": "User Modeling and User-Adapted Interaction", "ISSN": "0924-1868", "EISSN": "1573-1391", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Cognitive Science, University of Colorado, Boulder, USA"}, {"AuthorId": 2, "Name": "Caitlin Mills", "Affiliation": "Department of Psychology, University of New Hampshire, Durham, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Cognitive Science, University of Colorado, Boulder, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Cognitive Science, University of Colorado, Boulder, USA"}], "References": []}, {"ArticleId": 104713786, "Title": "Type-2 intuitionistic fuzzy TODIM for intelligent decision-making under uncertainty and hesitancy", "Abstract": "<p>The classical TODIM considers the crisp numbers to handle the information. However, in a real-world applicative context, this information is bounded by noise and vagueness and hence uncertain. There are wide range of works in the literature which utilizes fuzzy sets to handle the uncertainty in the various dimensions. However, there is a constraint of hesitancy in such decision-making problems due to the involvement of various decision-makers. Also, in the TODIM method, decision-maker’s bounded rationality and psychological behavior are also taken into consideration which adds up the hesitation and considers the problem with higher dimension of uncertainty. There are various applications of fuzzy TODIM using type-2 fuzzy numbers where uncertainty is being handled in more than one dimensions and also the introduction of intuitionistic fuzzy numbers where hesitancy factor of a decision-maker is taken into account. This paper targets to handle the uncertainty in more than one dimension keeping the hesitancy part into consideration for intelligent decision-making. Therefore, a novel trapezoidal type-2 intuitionistic fuzzy set (TrT2 IFS) is proposed, which is an aggregation of several triangular IFSs having upper and lower membership along with a non-membership value. For this TrT2 IFS, we have defined the generation procedure, operations, comparison and distance between such TrT2 IFSs. In addition, the decision-maker weights and criterion weight computation are also presented with respect to the TODIM approach. Furthermore, we have applied this extended TrT2 IFS-based TODIM method into a renewable energy resource selection problem of multi-criteria decision-making.</p>", "Keywords": "TODIM; Intuitionistic fuzzy set; Intelligent decision-making; Type-2 fuzzy sets; Multi-criteria decision-making", "DOI": "10.1007/s00500-022-07482-1", "PubYear": 2023, "Volume": "27", "Issue": "18", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, University of Jyväskylä, Jyvaskyla, Finland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, South Asian University, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, South Asian University, New Delhi, India"}, {"AuthorId": 4, "Name": "Pranab K<PERSON>", "Affiliation": "Department of Computer Science, South Asian University, New Delhi, India"}], "References": [{"Title": "Energy efficient multi-objective scheduling of tasks with interval type-2 fuzzy timing constraints in an Industry 4.0 ecosystem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103257", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Finite-interval-valued Type-2 Gaussian fuzzy numbers applied to fuzzy TODIM in a healthcare problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "103352", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Towards asymmetric uncertainty modeling in designing General Type-2 Fuzzy classifiers for medical diagnosis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115370", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 104713791, "Title": "Logic Explained Networks", "Abstract": "The large and still increasing popularity of deep learning clashes with a major limit of neural network architectures, that consists in their lack of capability in providing human-understandable motivations of their decisions. In situations in which the machine is expected to support the decision of human experts, providing a comprehensible explanation is a feature of crucial importance. The language used to communicate the explanations must be formal enough to be implementable in a machine and friendly enough to be understandable by a wide audience. In this paper, we propose a general approach to Explainable Artificial Intelligence in the case of neural architectures, showing how a mindful design of the networks leads to a family of interpretable deep learning models called Logic Explained Networks (LENs). LENs only require their inputs to be human-understandable predicates, and they provide explanations in terms of simple First-Order Logic (FOL) formulas involving such predicates. LENs are general enough to cover a large number of scenarios. Amongst them, we consider the case in which LENs are directly used as special classifiers with the capability of being explainable, or when they act as additional networks with the role of creating the conditions for making a black-box classifier explainable by FOL formulas. Despite supervised learning problems are mostly emphasized, we also show that LENs can learn and provide explanations in unsupervised learning settings. Experimental results on several datasets and tasks show that LENs may yield better classifications than established white-box models, such as decision trees and Bayesian rule lists, while providing more compact and meaningful explanations.", "Keywords": "Explainable AI ; Neural networks ; Logic Explained Networks", "DOI": "10.1016/j.artint.2022.103822", "PubYear": 2023, "Volume": "314", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering, University of Florence, Italy;Department of Information Engineering and Mathematics, University of Siena, Italy;Maasai, Inria, I3S, CNRS, Université Côte d'Azur, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Technology, University of Cambridge, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy;Maasai, Inria, I3S, CNRS, Université Côte d'Azur, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Technology, University of Cambridge, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy;Corresponding author"}], "References": [{"Title": "The Computational Complexity of Understanding Binary Classifier Decisions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "351", "JournalTitle": "Journal of Artificial Intelligence Research"}]}, {"ArticleId": 104713809, "Title": "Metamorphic testing of Advanced Driver-Assistance System (ADAS) simulation platforms: Lane Keeping Assist System (LKAS) case studies", "Abstract": "<b  >Context:</b> Simulation-based testing is essential when developing Advanced Driver-Assistance Systems (ADASs) and autonomous driving (AD) systems, producing fast, high-quality test results, at relatively low cost. Simulation testing relies on the quality of the ADAS simulation platform: If the simulation platform is faulty, then the simulation results may be incorrect, and hence useless. However, because of the lack of suitable test oracles — mechanisms to determine the correctness of the software output or behavior — it can be too difficult (or expensive) to verify or validate ADAS/AD simulation platforms, a situation known as the oracle problem. <b  >Objective:</b> To alleviate the oracle problem and better understand ADAS simulation software. <b  >Methods:</b> We develop geometric-transformation-based metamorphic testing approaches, and report on empirical studies conducted on the verification and validation (V&amp;V) of three popular simulation platforms for ADAS development: Simulink, CarMaker and 51Sim-One Cloud. Our examination focused on the platforms’ Lane Keeping Assist Systems (LKASs). <b  >Results:</b> When tested with ordinary (traditional) test cases, no issues were identified on any simulation platform. However, after applying geometric-transformation-based metamorphic testing, issues were revealed, some of which were later confirmed by the MATLAB and IPG Automotive teams. To the best of our knowledge, this paper is the first to report on real bugs and issues in ADAS simulation platforms. <b  >Conclusion:</b> Our research shows the simplicity, effectiveness and applicability of the proposed approach for ADAS simulation testing. This paper also provides successful examples of incorporating metamorphic testing into the testing of ADAS standards and protocols, and shows how practitioners can design effective metamorphic relations (MRs) inspired by using the symmetry metamorphic relation pattern.", "Keywords": "", "DOI": "10.1016/j.infsof.2022.107104", "PubYear": 2023, "Volume": "155", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing and Information Technology, University of Wollongong, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Technology, University of Wollongong, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Technology, University of Wollongong, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University of Nottingham Ningbo China, Ningbo, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, Swinburne University of Technology, Australia"}], "References": [{"Title": "Using metamorphic relations to verify and enhance Artcode classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "111060", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": *********, "Title": "Network-coded uplink clustered NOMA relay networks: Models and performance comparisons", "Abstract": "In this paper, network sum-rate maximization (NSR-MAX) for network-coded (NC) uplink clustered non-orthogonal multiple-access (NOMA) relay networks is considered. In particular, the goal is to maximize network sum-rate via optimal power allocation, where the user clusters communicate with the base-station over an amplify-and-forward relay, subject to quality-of-service (QoS) constraints. Two NC-NOMA transmission models – namely NC-NOMA-I and NC-NOMA-II – are proposed, where in the first model, network-coding is applied at the relay only, while the second model applies network-coding for user clusters’ transmissions and at the relay to further minimize the transmission delay. The formulated NSR-MAX problem happens to be non-convex, and hence, is computationally-intensive. Thus, a low-complexity iterative two-layer algorithm (ITLA) is devised, which decouples the formulated problem and efficiently solves its over two-layers. Specifically, the inner-layer solves the NSR-MAX problem to obtain the optimal relay transmit power, while the outer-layer determines the optimal users’ transmit powers. Numerical results are presented, which illustrate that the proposed ITLA yields near-optimal solutions for all transmission models, in comparison to the formulated NSR-MAX problem (solved via global optimization package) as well as outperforming its OMA-based counterparts. Not only that, but the NC-NOMA-II is shown to be superior to the other NOMA and OMA schemes, while demonstrating resilience to timing offsets as well as successive interference cancellation (SIC) and channel state information (CSI) errors.", "Keywords": "", "DOI": "10.1016/j.comnet.2022.109465", "PubYear": 2023, "Volume": "220", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, College of Engineering and Petroleum, Kuwait University, Kuwait;Correspondence to: Electrical Engineering Department, Kuwait University, PO Box: 5969, Safat, 13060, Kuwait City, Kuwait"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, College of Engineering and Petroleum, Kuwait University, Kuwait"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Engineering, University of Manchester, Manchester, United Kingdom"}], "References": [{"Title": "Optimal power allocation for NOMA-enabled D2D communication with imperfect SIC decoding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "", "Page": "101296", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 104713908, "Title": "Almost periodic oscillation of octonion-valued neural networks with delays on time scales", "Abstract": "In this paper, we propose an octonion-valued neural network model governed by dynamic equations, and study the existence and stability of its almost periodic solutions by employing the fixed point method and the time scale calculus theory. The method we use is the direct one, that is, we do not split the considered systems into real-valued ones, but directly study octonion-valued systems. Even when the time scale T = R , in other words, even if we consider continuous time real-valued systems, our results are novel. Finally, we illustrate the effectiveness of our results through a numerical example and computer simulation.", "Keywords": "Octonion-valued neural network ; almost periodic solution ; global exponential stability ; time scale", "DOI": "10.1080/00207721.2022.2145859", "PubYear": 2023, "Volume": "54", "Issue": "4", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Yunnan University, Kunming, Yunnan, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Yunnan University, Kunming, Yunnan, People's Republic of China"}], "References": [{"Title": "Finite-Time Mittag-Leffler Stability of Fractional-Order Quaternion-Valued Memristive Neural Networks with Impulses", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "2", "Page": "1485", "JournalTitle": "Neural Processing Letters"}, {"Title": "Deep octonion networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "397", "Issue": "", "Page": "179", "JournalTitle": "Neurocomputing"}, {"Title": "Dissipativity of impulsive matrix-valued neural networks with leakage delay and mixed delays", "Authors": "Călin<PERSON><PERSON>", "PubYear": 2020, "Volume": "405", "Issue": "", "Page": "85", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 104713980, "Title": "Estimation of spatial uncertainty in material property distributions within heterogeneous structures using optimized convolutional neural networks", "Abstract": "Variations in material behaviors within structures built with heterogeneous materials lead to damage initiation and evolution in locally weak regions. Quantifying the property variability within the structure and forward propagation of the impact of the material property uncertainty on the structural response is critical for reliability analysis and structural performance maximization. Commonly, quantification of the variability requires either computationally expensive high-fidelity models of the underlying microstructure or extensive experimental testing. In this paper, we model the uncertainty with spatially correlated random fields and calibrate the model parameters from limited strain field observations using Neural Networks (NNs). The calibration is performed by trained NNs, which outputs best-fit parameters for the spatial correlation model by accepting filtered Digital Image Correlation (DIC) strain distributions as the input. We demonstrate that by training the NNs using simulated data, the resulting networks are able to calibrate the spatial distribution uncertainty models effectively for a set of Fused Filament Fabrication (FFF) printed structures. The methodology requires a limited number of experimental datasets and produces fast estimations of the best-fit parameters of the uncertainty model compared to optimization or inverse fitting methods. This method allows experimentalists to use the same DIC information routinely obtained during modulus or strength testing to calibrate a spatial property distribution uncertainty model for the underlying microstructure.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105603", "PubYear": 2023, "Volume": "117", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Stevens Institute of Technology, Castle Point on Hudson, Hoboken, NJ 07030, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Stevens Institute of Technology, Castle Point on Hudson, Hoboken, NJ 07030, United States of America"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Stevens Institute of Technology, Castle Point on Hudson, Hoboken, NJ 07030, United States of America"}], "References": []}, {"ArticleId": 104714035, "Title": "A large-scale performance study of entropy-based image thresholding techniques using new SAD metric", "Abstract": "<p>The paper presents a novel method to measure the performance of entropy-based image thresholding techniques using a new Sum of Absolute value of Differences (SAD) metric in the absence of ground-truth images. The metric is further applied to estimate the parameters of generalized Renyi, <PERSON><PERSON><PERSON>, Masi entropy measures and the optimal threshold automatically from the image histogram. This leads to a new entropy-based image thresholding algorithm with three variants—one for each generalized entropy. The SAD metric and proposed method are first validated using ground-truth images HYTA dataset. The SAD metric is compared with misclassification error metric, Jaccard and SSIM indices and is found to exhibit consistent behavior. It is further observed that the proposed new method with SAD metric produces same or less misclassification errors than the older algorithms. Inspired by the success of the results, a large-scale performance analysis of 8 image thresholding algorithms over diverse datasets containing 621 images is carried out. The investigation reveals that the variant of the new algorithm with <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> entropies segment images better than others.</p>", "Keywords": "Entropy; Entropic parameter estimation; Image thresholding; Performance measurement; SAD metric", "DOI": "10.1007/s10044-022-01121-z", "PubYear": 2023, "Volume": "26", "Issue": "2", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, South Asian University, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, South Asian University, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, South Asian University, New Delhi, India"}], "References": []}, {"ArticleId": 104714044, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S2352-2208(22)00090-6", "PubYear": 2023, "Volume": "130", "Issue": "", "JournalId": 781, "JournalTitle": "Journal of Logical and Algebraic Methods in Programming", "ISSN": "2352-2208", "EISSN": "2352-2216", "Authors": [], "References": []}, {"ArticleId": 104714100, "Title": "A predictive model based on user awareness and multi-type rumors forwarding dynamics", "Abstract": "Previous models for predicting rumor-forwarding trends were primarily focused on feature generation and model prediction in two independent directions: message text and user association features. However, the abstraction of user awareness, text contextual feature extraction limitation, and inefficiency of traditional hyperparameter search methods still pose numerous challenges. This study proposes a rumor-forwarding trend prediction model that combines user awareness and multi-type rumor to address such challenges. First, considering the abstraction of user awareness under multi-type rumors, we extract features by cascading user behavior, historical activities, interactions, and activity levels and by fusing features using a two-layer fully connected network to effectively quantify the relevant features of user awareness. Second, considering the limitations of traditional text representation in semantic context understanding, we use the Bidirectional Encoder Representation from Transformers (BERT) pre-training model to characterize the text in the topic, obtain text representation sequence with contextual relationships, and propose an Improved Cuckoo Search (ICS) method that optimizes the hyperparameters of the temporal convolutional network (TCN) model. Finally, an Improved Cuckoo Search-TCN-based rumor-forwarding trend prediction model is constructed based on user awareness features and text representation sequences to predict the rumor-forwarding trend. Certain rumors with a large potential impact range can be monitored at the early dissemination stage.", "Keywords": "", "DOI": "10.1016/j.ins.2022.11.072", "PubYear": 2023, "Volume": "619", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Chongqing University of Posts and Telecommunications, Chongqing 400065, China;Corresponding author"}], "References": [{"Title": "Hot topic prediction considering influence and expertise in social media", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "3", "Page": "671", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Exploring deep neural networks for rumor detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "4315", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Minimizing rumor influence in multiplex online social networks based on human individual and social behaviors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1458", "JournalTitle": "Information Sciences"}, {"Title": "Predicting the security threats on the spreading of rumor, false information of Facebook content based on the principle of sociology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "455", "JournalTitle": "Computer Communications"}, {"Title": "Deep Collaborative Embedding for information cascade prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "193", "Issue": "", "Page": "105502", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Near real-time topic-driven rumor detection in source microblogs", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "207", "Issue": "", "Page": "106391", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Rumor & Anti-rumor Propagation Model Based on Data Enhancement and Evolutionary Game", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Social rumor detection based on multilayer transformer encoding blocks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "e6083", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Rumor Detection Based on SAGNN: Simplified Aggregation Graph Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "84", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "BCMM: A novel post-based augmentation representation for early rumour detection on social media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107818", "JournalTitle": "Pattern Recognition"}, {"Title": "Rumor2vec: A rumor detection framework with joint text and propagation structure representation learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "137", "JournalTitle": "Information Sciences"}, {"Title": "Dynamical behaviors and control measures of rumor-spreading model in consideration of the infected media and time delay", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "564", "Issue": "", "Page": "237", "JournalTitle": "Information Sciences"}, {"Title": "Deep reinforcement learning based ensemble model for rumor tracking", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "101772", "JournalTitle": "Information Systems"}, {"Title": "The dynamics and control of 2I2SR rumor spreading models in multilingual online social networks", "Authors": "Shuzhen Yu; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "18", "JournalTitle": "Information Sciences"}, {"Title": "Modeling and analysis of rumor propagation in social networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "857", "JournalTitle": "Information Sciences"}, {"Title": "Multi‐scale graph capsule with influence attention for information cascades prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "3", "Page": "2584", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 104714590, "Title": "Modified light GBM based classification of malicious users in cooperative cognitive radio networks", "Abstract": "This paper proposes the Modified Light GBM to classify the Malicious Users (MUs) and legitimate Secondary Users (SUs) in the cognitive-radio network. The proposed method is to avoid the consequences of malicious users in Cooperative Spectrum Sensing (CSS) without the detection of MUs. The method is tested against the occurrence of Always No Malicious User (ANMU), always yes malicious user (AYMU), the Random Malicious User (RMU), and Opposite Malicious User (OMU) transmitting spectrum sensing data to the Fusion Center (FC) by normal secondary users. The efficiency of the proposed method is expressed via simulations and compared with other existing methods.", "Keywords": "Cooperative spectrum sensing ; cognitive radio network ; malicious user ; fusion centre ; secondary user ; false alarm rate ; LightGBM ; signal to noise ratio ; Hyper-parameter optimisation", "DOI": "10.1080/23335777.2022.2135610", "PubYear": 2024, "Volume": "10", "Issue": "1", "JournalId": 2756, "JournalTitle": "Cyber-Physical Systems", "ISSN": "2333-5777", "EISSN": "2333-5785", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Technology, SRM Valliammai Engineering College, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Artificial Intelligence and Data Science, SRM Valliammai Engineering College, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Artificial Intelligence and Data Science, SRM Valliammai Engineering College, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, SRM Valliammai Engineering College, Chennai, India"}], "References": [{"Title": "Opposition grasshopper optimizer based multimedia data distribution using user evaluation strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "19", "Page": "29875", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 104714599, "Title": "Geo-Tile2Vec: A Multi-Modal and Multi-Stage Embedding Framework for Urban Analytics", "Abstract": "<p>Cities are very complex systems. Representing urban regions are essential for exploring, understanding, and predicting properties and features of cities. The enrichment of multi-modal urban big data has provided opportunities for researchers to enhance urban region embedding. However, existing works failed to develop an integrated pipeline that fully utilizes effective and informative data sources within geographic units. In this paper, we regard a geo-tile as a geographic unit and propose a multi-modal and multi-stage representation learning framework, namely Geo-Tile2Vec, for urban analytics, especially for urban region properties identification. Specifically, in the early stage, geo-tile embeddings are firstly inferred through dynamic mobility events which are combinations of point-of-interest (POI) data and trajectory data by a Word2Vec-like model and metric learning. Then, in the latter stage, we use static street-level imagery to further enrich the embedding information by metric learning. Lastly, the framework learns distributed geo-tile embeddings for the given multi-modal data. We conduct experiments on real-world urban datasets. Four downstream tasks, i.e., main POI category classification task, main land use category classification task, restaurant average price regression task, and firm number regression task, are adopted for validating the effectiveness of the proposed framework in representing geo-tiles. Our proposed framework can significantly improve the performances of all downstream tasks. In addition, we also demonstrate that geo-tiles with similar urban region properties are geometrically closer in the vector space.</p>", "Keywords": "Multi-modal learning; representative learning; urban computing; unsupervised learning", "DOI": "10.1145/3571741", "PubYear": 2023, "Volume": "9", "Issue": "2", "JournalId": 22173, "JournalTitle": "ACM Transactions on Spatial Algorithms and Systems", "ISSN": "2374-0353", "EISSN": "2374-0361", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computing, the Hong Kong Polytechnic University, HKSAR"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, the Hong Kong Polytechnic University, HKSAR"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, the Hong Kong Polytechnic University, HKSAR"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, the Hong Kong Polytechnic University, HKSAR"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>, China"}], "References": [{"Title": "A Deep Learning Approach for Identifying User Communities Based on Geographical Preferences and Its Applications to Urban and Environmental Planning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Spatial Algorithms and Systems"}, {"Title": "NexT: A framework for next-place prediction on location based social networks", "Authors": "Carmela Comito", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106205", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Urban function classification at road segment level using taxi trajectory data: A graph convolutional neural network approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "87", "Issue": "", "Page": "101619", "JournalTitle": "Computers, Environment and Urban Systems"}]}, {"ArticleId": *********, "Title": "Multilingual News Feed Analysis using Intelligent Linguistic Particle Filtering Techniques", "Abstract": "<p> Analyzing real-time news feeds and their impacts in the real world is a complex task in the social networking arena. Particularly, countries with a multilingual environment have various patterns and perceptions of news reports considering the diversity of the people. Multilingual and multimodal news analysis is an emerging trend for evaluating news source neutralities. Therefore, in this work, four new deep news particle filtering techniques were developed, including generic news analysis, sequential importance re-sampling (SIR) -based news particle filtering analysis, reinforcement learning (RL) -based multimodal news analysis, and deep Convolution neural network (DCNN) -based multi-news filtering approach, for news classification. Results indicate that these techniques, which primarily employ particle filtering with multilevel sampling strategies, produce 15% to 20% better performance than conventional news analysis techniques. </p>", "Keywords": "Machine learning and deep learning; artificial intelligence; news feeds; particle filtering; sampling", "DOI": "10.1145/3569899", "PubYear": 2023, "Volume": "22", "Issue": "3", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, GITAM School of Technology Bengaluru Campus, GITAM (Deemed to be University), Karnataka, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, GITAM School of Technology Bengaluru Campus, GITAM (Deemed to be University), Karnataka, India"}, {"AuthorId": 3, "Name": "Muthuramalingam S", "Affiliation": "Department of Information Technology, Thiagarajar College of Engineering, Madurai, India"}, {"AuthorId": 4, "Name": "Fadi <PERSON>", "Affiliation": "Artificial Intelligence Engineering Dept., AI and Robotics Institute, Near East University, Mersin 10, Turkey and Research Center for AI and IoT, Faculty of Engineering, University of Kyrenia, Mersin 10, Turkey"}], "References": [{"Title": "Sentiment analysis on product reviews based on weighted word embeddings and deep neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "23", "Page": "e5909", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Multimodal News Feed Evaluation System with Deep Reinforcement Learning Approaches", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A Deep Learning–based Approach for Emotions Classification in Big Corpus of Imbalanced Tweets", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Modelling argumentation in short text: A case of social media debate", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "102446", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Bidirectional convolutional recurrent neural network architecture with group-wise enhancement mechanism for text sentiment classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "2098", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}]}, {"ArticleId": 104714640, "Title": "A Semantic Similarity-Based Identification Method for Implicit Citation Functions and Sentiments Information", "Abstract": "<p>Automated citation analysis is becoming increasingly important in assessing the scientific quality of publications and identifying patterns of collaboration among researchers. However, little attention has been paid to analyzing the scientific content of the citation context. This study presents an unsupervised citation detection method that uses semantic similarities between citations and candidate sentences to identify implicit citations, determine their functions, and analyze their sentiments. We propose different document vector models based on TF-IDF weights and word vectors and compare them empirically to calculate their semantic similarity. To validate this model for identifying implicit citations, we used deep neural networks and LDA topic modeling on two citation datasets. The experimental results show that the F1 values for the implicit citation classification are 88.60% and 86.60% when the articles are presented in abstract and full-text form, respectively. Based on the citation function, the results show that implicit citations provide background information and a technical basis, while explicit citations emphasize research motivation and comparative results. Based on the citation sentiment, the results showed that implicit citations tended to describe the content objectively and were generally neutral, while explicit citations tended to describe the content positively. This study highlights the importance of identifying implicit citations for research evaluation and illustrates the difficulties researchers face when analyzing the citation context.</p>", "Keywords": "citation text identification and classification; implicit citations; citation content analytics; sematic similarity; term frequency-inverse document frequency (TF-IDF); vector space model (VSM) citation text identification and classification ; implicit citations ; citation content analytics ; sematic similarity ; term frequency-inverse document frequency (TF-IDF) ; vector space model (VSM)", "DOI": "10.3390/info13110546", "PubYear": 2022, "Volume": "13", "Issue": "11", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology and Computer Science, Yarmouk University, Irbid 21163, Jordan; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Engineering and Information Technology, University of Dubai, Dubai 14143, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, Princess <PERSON><PERSON><PERSON> University for Technology, Amman 11195, Jordan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Economics-Varna, 9000 Varna, Bulgaria"}], "References": [{"Title": "Feature Engineering and Ensemble-Based Approach for Improving Automatic Short-Answer Grading Performance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "77", "JournalTitle": "IEEE Transactions on Learning Technologies"}, {"Title": "Readiness model for requirements change management in global software development", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "Citation recommendation: approaches and datasets", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "4", "Page": "375", "JournalTitle": "International Journal on Digital Libraries"}, {"Title": "SENTiVENT: enabling supervised information extraction of company-specific events in economic and financial news", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "56", "Issue": "1", "Page": "225", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Leveraging contextual embeddings and self-attention neural networks with bi-attention for sentiment analysis", "Authors": "Magdalena <PERSON>; Katarzyna Biesialska; <PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "3", "Page": "601", "JournalTitle": "Journal of Intelligent Information Systems"}]}, {"ArticleId": *********, "Title": "Hybrid MQTTNet: An Intrusion Detection System Using Heuristic-Based Optimal Feature Integration and Hybrid Fuzzy with 1DCNN", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2022.2145649", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Electronics Engineering (SENSE), Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics Engineering (SENSE), Vellore Institute of Technology, Vellore, India"}], "References": [{"Title": "Enhanced DTLS with CoAP-based authentication scheme for the internet of things in healthcare application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "6", "Page": "3963", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "RETRACTED: Detection of attacks in IoT sensors networks using machine learning algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103814", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A novel IoT network intrusion detection approach based on Adaptive Particle Swarm Optimization Convolutional Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "568", "Issue": "", "Page": "147", "JournalTitle": "Information Sciences"}, {"Title": "Intrusion Detection System for IOT Botnet Attacks Using Deep Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "African vultures optimization algorithm: A new nature-inspired metaheuristic algorithm for global optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "158", "Issue": "", "Page": "107408", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Anomaly-based intrusion detection system for IoT networks through deep learning model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "99", "Issue": "", "Page": "107810", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "PDAE: Efficient network intrusion detection in IoT using parallel deep auto-encoders", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "598", "Issue": "", "Page": "57", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 104714795, "Title": "An Advanced Hierarchical Identity-Based Security Mechanism by Blockchain in Named Data Networking", "Abstract": "<p>Named data networking (NDN) has been viewed as a promising future Internet architecture due to its data-centric design. It requires a new security model that is orienting data but not devices. In this paper, an advanced hierarchical identity-based security mechanism by blockchain (AHISM-B) is to be proposed for the NDN networks. On one hand, the hierarchical identity-based cryptology is used to bind the data name to a public key. The valid public parameters would be requested by consumers with the Interest packets so that consumers would compose producers’ public keys to authenticate producers and verify the integrity of the Data packets. On the other hand, a blockchain is employed to manage public parameters to avoid catastrophes due to a single node failure. Both of the security proof result and the formal validation result indicate that the proposed AHISM-B is secure. Moreover, the simulation results show that the performance of our AHISM-B outperforms that of the classic NDN scheme. Especially, the average response delay of the AHISM-B scheme is less by 8% than that of the classic NDN scheme. With the increase of the average arrival rate of Interest packets, the advantage of the AHISM-B could be enhanced further to 11%.</p>", "Keywords": "Named data networking; Security; Blockchain; Hierarchical identity-based cryptography", "DOI": "10.1007/s10922-022-09689-x", "PubYear": 2023, "Volume": "31", "Issue": "1", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shenzhen University, Shenzhen, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Qatar University, Doha, Qatar"}], "References": [{"Title": "A blockchain-based architecture for secure vehicular Named Data Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106715", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 104714916, "Title": "Measurements, Algorithms, and Presentations of Reality: Framing Interactions with AI-Enabled Decision Support", "Abstract": "<p>Bringing AI technology into clinical practice has proved challenging for system designers and medical professionals alike. The academic literature has, for example, highlighted the dangers of black-box decision-making and biased datasets. Further, end-users’ ability to validate a system’s performance often disappears following the introduction of AI decision-making. We present the MAP model to understand and describe the three stages through which medical observations are interpreted and handled by AI systems. These stages are Measurement, in which information is gathered and converted into data points that can be stored and processed; Algorithm, in which computational processes transform the collected data; and Presentation, where information is returned to the user for interpretation. For each stage, we highlight possible challenges that need to be overcome to develop Human-Centred AI systems. We illuminate our MAP model through complementary case studies on colonoscopy practice and dementia diagnosis, providing examples of the challenges encountered in real-world settings. By defining Human-AI interaction across these three stages, we untangle some of the inherent complexities in designing AI technology for clinical decision-making, and aim to overcome misalignment between medical end-users and AI researchers and developers.</p>", "Keywords": "Human-Centred AI; Measurement; Algorithms; Presentation; MAP; MAP model; case studies; healthcare; medicine; decision-making; cooperative AI; Human-AI", "DOI": "10.1145/3571815", "PubYear": 2023, "Volume": "30", "Issue": "2", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Aalborg University, Denmark"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University College London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aalborg University, Denmark"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University College London, United Kingdom"}], "References": [{"Title": "Overcoming compliance bias in self-report studies: A cross-study analysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "134", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Human-Centered Artificial Intelligence: Reliable, Safe & Trustworthy", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "6", "Page": "495", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Machine Learning in Mental Health", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "The challenges of deploying artificial intelligence models in a rapidly evolving pandemic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "6", "Page": "298", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Artificial Intelligence Is Trusted Less than a Doctor in Medical Treatment Decisions: Influence of Perceived Care and Value Similarity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "10", "Page": "981", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Trust in Artificial Intelligence: Meta-Analytic Findings", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "65", "Issue": "2", "Page": "337", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "What Do You Mean by Trust? Establishing Shared Meaning in Interdisciplinary Design for Assistive Technology", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "8", "Page": "1879", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Human-AI interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "6", "Page": "67", "JournalTitle": "interactions"}, {"Title": "Datasheets for datasets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "12", "Page": "86", "JournalTitle": "Communications of the ACM"}, {"Title": "Initial Responses to False Positives in AI-Supported Continuous Interactions: A Colonoscopy Case Study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Interactive Intelligent Systems"}]}, {"ArticleId": 104714953, "Title": "Corrigendum to “A signaling game-optimization algorithm for residential energy communities implemented at the edge-computing side” [Comput. Ind. Eng. 169 (2022) 108272]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.cie.2022.108812", "PubYear": 2022, "Volume": "174", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Simona-Vasilica Oprea", "Affiliation": "Bucharest University of Economic Studies, Department of Economic Informatics and Cybernetics, No. 6 Piaţa Romană, Bucharest 010374, Romania;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Bucharest University of Economic Studies, Department of Economic Informatics and Cybernetics, No. 6 Piaţa Romană, Bucharest 010374, Romania"}], "References": []}, {"ArticleId": 104714988, "Title": "Estimation of Volume of Felled Chinese Fir Trees Using Unmanned Aerial Vehicle Oblique Photography", "Abstract": "", "Keywords": "Chinese fir; cutting; root diameter; tree height; UAV remote sensing; volume", "DOI": "10.18494/SAM3996", "PubYear": 2022, "Volume": "34", "Issue": "11", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Natural Resources and Planning Bureau of Jingning She Autonomous County, Jingning County, Zhejiang, 323500, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Natural Resources and Planning Bureau of Jingning She Autonomous County, Jingning County, Zhejiang, 323500, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Natural Resources and Planning Bureau of Jingning She Autonomous County, Jingning County, Zhejiang, 323500, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Zhejiang Provincial Key Laboratory of Carbon Cycling in Forest Ecosystems and Carbon Sequestration, Zhejiang A&F University, Hangzhou, 311300, China, College of Environmental and Resource Sciences, Zhejiang A&F University, Hangzhou, 311300, China"}], "References": []}, {"ArticleId": 104715043, "Title": "Towards stable task assignment with preference lists and ties in spatial crowdsourcing", "Abstract": "Task assignment is a fundamental problem in spatial crowdsourcing. Since workers and tasks accept each other within a limited range, there are incomplete and tied preference lists for any vertex on two sides (workers and tasks). Many studies ignore that workers’ preferences may conflict with tasks’ preferences, which results in unstable matching. In this paper, we investigate the preferences-oriented online stable task assignment (POSTA) in which tasks arrive online and obey independent identical distribution (i.i.d.). First, we construct a weighted partite graph G and formulate the POSTA problem based on G . Then, prove it to be NP-complete. To measure the weight of edges, we design a utility estimation method. Second, we formulate the linear programming model of POSTA and obtain the solution. Next, we solve the maximum weighted matching of G . Based on these two solutions, we design the algorithm utilizing two suggested matching (TSM) technology to obtain the combination solution, which guides the algorithm for matching. Then, we analyze the competitive ratio. Finally, we examine the performance of the proposed method through extensive experiments. The experiment results show that our strategy outperforms other methods in total utility, competitive ratio and blocking pair ratio.", "Keywords": "", "DOI": "10.1016/j.ins.2022.11.048", "PubYear": 2023, "Volume": "620", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Science and Technology, Wuhan, China;Hubei Province Key Laboratory of Intelligent Information Processing and Real-time Industrial System, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Science and Technology, Wuhan, China;Hubei Province Key Laboratory of Intelligent Information Processing and Real-time Industrial System, Wuhan, China;Corresponding author at: School of Computer Science and Technology, Wuhan University of Science and Technology, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Science and Technology, Wuhan, China;Hubei Province Key Laboratory of Intelligent Information Processing and Real-time Industrial System, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Wuhan University of Science and Technology, Wuhan, China;Hubei Province Key Laboratory of Intelligent Information Processing and Real-time Industrial System, Wuhan, China"}, {"AuthorId": 5, "Name": "Haizhou Bao", "Affiliation": "School of Computer Science and Technology, Wuhan University of Science and Technology, Wuhan, China;Hubei Province Key Laboratory of Intelligent Information Processing and Real-time Industrial System, Wuhan, China"}], "References": [{"Title": "Spatial crowdsourcing: a survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "1", "Page": "217", "JournalTitle": "The VLDB Journal"}, {"Title": "Allocation Problems in Ride-sharing Platforms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Economics and Computation"}]}, {"ArticleId": 104715572, "Title": "A novel design of compact tilt stage with spatially distributed anti-symmetric compliant mechanism", "Abstract": "The tilt stage with high precision angular motions has emerged as one of the key enabling components in many advanced engineering applications, such as adaptive optics, space communication and ultra-high precision manufacturing, where the design requirements on large deflection angle, high natural frequency and compact size have general challenges. In this research, a 3D-printing compatible anti-symmetric compliant mechanism is proposed, composed of spatially distributed positive Poisson’s ratio structural unit (P-layer) and negative Poisson’s ratio structural unit (N-layer). Under the same tension, the P-layer and N-layer can generate transverse shrinkage deformation and expansion deformation respectively, thus driving the end-effector with a larger angular deflection without sacrificing the natural frequency. The proposed tilt stage with anti-symmetric compliant mechanism achieves a more compact size, as well as better motion behaviors. A theoretical model is also established to analyze the static performance and predict the output angle of the proposed design. The experiments show that the developed tilt stage can achieve a deflection range of 9.23 mrad and a natural frequency of 1086 Hz, with significant improvement over existing results.", "Keywords": "Anti-symmetric compliant mechanism ; PZT actuation ; Tilt stage ; 3D printing", "DOI": "10.1016/j.sna.2022.113995", "PubYear": 2023, "Volume": "349", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High-Efficiency and Clean Mechanical Manufacturing, Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan 250061, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Metrology, Beijing 100029, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High-Efficiency and Clean Mechanical Manufacturing, Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan 250061, China;Corresponding author"}], "References": [{"Title": "Design and characterisation of a compact 4-degree-of-freedom fast steering mirror system based on double Porro prisms for laser beam stabilization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "322", "Issue": "", "Page": "112639", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 104715615, "Title": "A Memory-Driven Neural Attention Model for Aspect-Based Sentiment Classification", "Abstract": "<p>Sentiment analysis techniques are becoming more and more important as the number of reviews on the World Wide Web keeps increasing. Aspect-based sentiment analysis (ABSA) entails the automatic analysis of sentiments at the highly fine-grained aspect level. One of the challenges of ABSA is to identify the correct sentiment expressed towards every aspect in a sentence. In this paper, a neural attention model is discussed and three extensions are proposed to this model. First, the strengths and weaknesses of the highly successful CABASC model are discussed, and three shortcomings are identified: the aspect-representation is poor, the current attention mechanism can be extended for dealing with polysemy in natural language, and the design of the aspect-specific sentence representation is upheld by a weak construction. We propose the Extended CABASC (E-CABASC) model, which aims to solve all three of these problems. The model incorporates a context-aware aspect representation, a multi-dimensional attention mechanism, and an aspect-specific sentence representation. The main contribution of this work is that it is shown that attention models can be improved upon using some relatively simple extensions, such as fusion gates and multi-dimensional attention, which can be implemented in many state-of-the-art models. Additionally, an analysis of the parameters and attention weights is provided.</p>", "Keywords": "Aspect sentiment classification; attention models; deep learning; sentiment analysis", "DOI": "10.13052/jwe1540-9589.2163", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 58533, "JournalTitle": "Journal of Web Engineering", "ISSN": "1540-9589", "EISSN": "1544-5976", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Erasmus School of Economics, Erasmus University Rotterdam, Rotterdam, 3062 PA, Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Erasmus School of Economics, Erasmus University Rotterdam, Rotterdam, 3062 PA, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Erasmus School of Economics, Erasmus University Rotterdam, Rotterdam, 3062 PA, Netherlands"}], "References": []}, {"ArticleId": 104715774, "Title": "PRBP: A prioritized replica balancing policy for HDFS balancer", "Abstract": "<p>Data replication is the main fault tolerance mechanism implemented by the Apache Hadoop Distributed File System (HDFS). The placement of the data across the cluster directly affects replica balancing and data locality. The HDFS Balancer is the native solution to rebalance the data distribution by moving the blocks from over-utilized to under-utilized nodes. Nevertheless, its current balancing policy does not address the characteristics and specific needs of the applications during data rearrangement. In this work, we present the PRBP, a customized replica balancing policy for the HDFS Balancer. The PRBP is based on a system of priorities, which can be adapted and configured according to different demands of use, either these are related to heterogeneous environments or focused on improving data reliability and availability. The priorities define whether system metrics or aspects of the cluster topology should be considered during the execution of the HDFS Balancer, thus making the process of replica balancing in HDFS more flexible. Based on the priority system, we determine association rules that allow the use of multiple priorities simultaneously. Along with these rules, we present guidelines for using the PRBP as a specialized solution in scenarios that can benefit from reactive replica balancing. In addition, we conducted a practical experimentation to highlight the behavior and the applicability of the guidelines of the PRBP to prioritize replica rearrangement in the file system.</p>", "Keywords": "balancing policy;data availability;data replication;distributed file systems;fault tolerance;reliability", "DOI": "10.1002/spe.3162", "PubYear": 2023, "Volume": "53", "Issue": "3", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Languages and Computer Systems Federal University of Santa Maria (UFSM)  Rio Grande do Sul Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Languages and Computer Systems Federal University of Santa Maria (UFSM)  Rio Grande do Sul Brazil"}], "References": []}, {"ArticleId": *********, "Title": "Enhanced Rank Attack Detection Algorithm (E-RAD) for securing RPL-based IoT networks by early detection and isolation of rank attackers", "Abstract": "<p>Internet of Things (IoT) is becoming a crucial requirement for linking numerous physical devices or gadgets to the web. Due to large volume of data exchanged, security in IoT still remains a key challenge. Additionally, maintaining security in routing protocols like Routing Protocol for Low-power and Lossy networks (RPL) is becoming more difficult because IoT devices are heterogeneous and resource-constrained. RPL builds a tree topology using control packets. Since more control packets are exchanged, modification in the content of control packets gives rise to a number of attacks including rank attacks, version attacks, flood attacks and so forth. Rank attack is a major attack that causes loop creation, energy depletion, decreased packet delivery, increased packet delivery latency and other problems. Since rank attacks have a significant adverse effect on the network, an effort has been made to enhance the default mechanism of RPL and so that the rank attackers can be quickly identified and isolated. The proposed E-RAD (Enhanced-Rank Attack Detection) algorithm deploys rate limiting technique to control the generation of DIO (DODAG Information Object) packet and also uses DIS (DODAG Information Solicitation) message solicitation to isolate the rank attackers. If an attacker is missed using above mechanism, it can be detected from the consistency check of hash value in DAO (Destination Advertisement Object) message. An alarm is set off against the rank attackers as soon as it is found. An alarm is an attachment to the control message itself and is not a standalone packet. E-RAD algorithm resulted in 1356 J of energy usage, 775 ms of packet delivery delay, 95.53% of delivery ratio, 97.23% of accuracy and 1023 packets of overhead in control packet generation.</p>", "Keywords": "IoT; RPL; Rank attack; Control packets; Hash; Alarm", "DOI": "10.1007/s11227-022-04921-6", "PubYear": 2023, "Volume": "79", "Issue": "6", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Kongu Engineering College, Perundurai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Vivekanandha College of Engineering for Women, Namakkal, India"}, {"AuthorId": 3, "Name": "S. Malliga", "Affiliation": "Department of Computer Science and Engineering, Kongu Engineering College, Perundurai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, KPR Institute of Engineering and Technology, Coimbatore, India"}], "References": [{"Title": "Security explorations for routing attacks in low power networks on internet of things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "5", "Page": "4778", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Opportunistic Transmission of Control Packets for Faster Formation of 6TiSCH Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet of Things"}, {"Title": "Routing protocol for low power and lossy network–load balancing time-based", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "3101", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "SAMP-RPL: secure and adaptive multipath RPL for enhanced security and reliability in heterogeneous IoT-connected low power and lossy networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "409", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A holistic framework for prediction of routing attacks in IoT-LLNs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "1", "Page": "1409", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "MFO-RPL: A secure RPL-based routing protocol utilizing moth-flame optimizer for the IoT applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "82", "Issue": "", "Page": "103622", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "A roadmap towards energy‐efficient data fusion methods in the Internet of Things", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "15", "Page": "e6959", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "CQARPL: Congestion and QoS-aware RPL for IoT applications under heavy traffic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "14", "Page": "16136", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Trust-based secure routing and message delivery protocol for signal processing attacks in IoT applications", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "3", "Page": "2882", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": *********, "Title": "Identification of Cardiovascular Disease Patients", "Abstract": "For the prevention and treatment of illness, accurate and timely investigation of any health-related problem is critical. The prevalence of cardiovascular illnesses is rising among Indians. Aging has long been recognized as one of the most significant risk factors for heart attacks, affecting men and women aged 50 and up. Cardiovascular attacks are increasingly becoming more common in people in their 20s, 30s, and 40s.. To detect and predict cardiovascular disease patients, starting with a pre-processing step in which we used feature selection to pick the most important features, we tested the accuracy of different models on a dataset with features like gender, age, blood pressure, and glucose levels. The model predicts whether a patient is likely to suffer from cardiovascular disease based on their medical records. Finally, we performed hyperparameter tuning to find the best parameter for the models. In comparison to the other algorithms, the XGBoost model produced the best results with an accuracy of 75.72%. © 2023, American Scientific Publishing Group (ASPG). All rights reserved.", "Keywords": "Cardiovascular disease; Disease Prediction; Machine Learning", "DOI": "10.54216/FPA.100101", "PubYear": 2023, "Volume": "10", "Issue": "1", "JournalId": 91518, "JournalTitle": "Fusion: Practice and Applications", "ISSN": "2770-0070", "EISSN": "2692-4048", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>’s College of Engineering, GGSIPU, Delhi, INDIA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> ..", "Affiliation": "<PERSON><PERSON><PERSON>’s College of Engineering, GGSIPU, Delhi, INDIA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> ..", "Affiliation": "<PERSON><PERSON><PERSON>’s College of Engineering, GGSIPU, Delhi, INDIA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>’s College of Engineering, GGSIPU, Delhi, INDIA"}], "References": []}, {"ArticleId": 104715989, "Title": "A Parallel Text Recognition in Electrical Equipment Nameplate Images Based on Apache Flink", "Abstract": "<p>Information on the equipment nameplate is important for the storage, transportation, verification and maintenance of electrical equipment. However, because a natural image of the device on the text nameplate may be multidirectional, curved, noisy or blurry, automatically recognizing the image from the device nameplate can be difficult. Meanwhile, image preprocessing methods are carried out in a serial manner, so the processing speed with regard to the above problems is slower and takes a longer time. Accordingly, this study proposes a parallel and deep-learning-based text automatic recognition method. In the proposed method, a pretreatment method comprising edge detection, morphological manipulation and projection transformation is used to obtain the corrected nameplate region. The connectionist text proposal network (CTPN) is then activated to detect text lines on the corrected nameplate area. Next, a deep-learning method is proposed to study the classification methods of convolutional recurrent neural networks and connectionist time classification for identifying text in each line of text detected by CTPN. Finally, we use Apache Flink to parallelize the above processes, including parallelization preprocessing and bidirectional long short-term memory parallelization in the process of text line detection and text recognition. Experimental results on the collected nameplate show that the proposed imaging processing method has a good recognition performance and that the parallelization method significantly reduces the data processing time cost.</p>", "Keywords": "Electrical equipment nameplate; deep learning; text detection; text recognition; parallel computing", "DOI": "10.1142/S0218126623501098", "PubYear": 2023, "Volume": "32", "Issue": "7", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Hebei Electric Power Company, Electric Power Research Institute, Shijiazhuang 050021, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Wuhan NARI Limited Liability Company of State Grid Electric POWER Research Institute, Wuhan 430074, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Grid Hebei Electric Power Company, Electric Power Research Institute, Shijiazhuang 050021, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Hebei Electric Power Company, Electric Power Research Institute, Shijiazhuang 050021, P. R. China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan 430074, P. R. China"}], "References": [{"Title": "Review of Scene Text Detection and Recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "2", "Page": "433", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Multifractal detrended fluctuation analysis parallel optimization strategy based on openMP for image processing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "10", "Page": "5599", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": *********, "Title": "Face mask recognition system using MobileNetV2 with optimization function", "Abstract": "The world has experienced a health crisis with the outbreak of the COVID-19 virus. The mask has been identified as the most effective way to prevent the spread of the virus. This has led to the need for a face mask recognition device that not only detects the presence of the mask but also provides the accuracy with which a person is wearing the face mask. In addition, the face mask should also be recognized from all angles. The project aims to create a new and improved real-time face mask recognition tool using image processing and computer vision approaches. A dataset consisting of images with and without a mask was used. For the purposes of this project, a pre-trained MobileNetV2 convolutional neural network was used. The performance of the given model was evaluated. The model presented in this project can detect the face mask with an accuracy of 99.21%. The face mask recognition tool can effectively detect the face mask in the side direction, which makes it more useful. The optimization function which contains the learning loops and the optimization function are also used.", "Keywords": "Deep learning; CNN; MobileNetV2; Face Mask; Covid-19", "DOI": "10.1080/08839514.2022.2145638", "PubYear": 2022, "Volume": "36", "Issue": "1", "JournalId": 7360, "JournalTitle": "Applied Artificial Intelligence", "ISSN": "0883-9514", "EISSN": "1087-6545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of computer science and information technology, University of Al-Qadisiyah, Diwaniyah, Iraq"}], "References": []}, {"ArticleId": 104716124, "Title": "Retraction Note to: Optimal feature-based multi-kernel SVM approach for thyroid disease classification", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11227-022-04941-2", "PubYear": 2023, "Volume": "79", "Issue": "6", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, Kalasalingam Academy of Research and Education, Krishnankoil, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Electronics and Instrumentation Engineering, BS Abdur Rahman Crescent Institute of Science and Technology, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> <PERSON><PERSON><PERSON> Institute of Technology, GGSIP University, Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics Management, STMIK Pringsewu, Lampung, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Graduate Program in Applied Informatics, University of Fortaleza, Fortaleza, Brazil"}], "References": []}, {"ArticleId": 104716135, "Title": "A surrogate assisted evolutionary multitasking optimization algorithm", "Abstract": "Evolutionary algorithms (EAs) have been applied with strong abilities to solve a wide range of applications, but it can solve one problem at a time. To improve efficiency, an emerging research paradigm in the field of evolutionary computation, Evolutionary multi-tasking (EMT) was proposed. EMT solves multiple optimization tasks simultaneously. The effectiveness of EMT is to improve the solutions for each task via inter-task knowledge transfer. Multifactorial evolutionary algorithms (MFEAs) is the first algorithm proposed to solve multi-task optimization problems. However, it tends to suffer from the issue of negative knowledge transfer. To address this issue and improve the performance of MFEA, we propose to construct a surrogate model as a helper task is optimized and target task simultaneously in MFEA. According to the proposed method, the surrogate model is a related task for each corresponding target task to enhance positive inter-task knowledge transfer. Besides, the surrogate model can reduce the number of local optima and has a simple structure. Experiments are conducted on benchmarks and real-world reservoir flood generation power problems to examine the performance of the proposed algorithm. Comparative experiments on several widely used test problems demonstrated that surrogate models as helper tasks enable significantly improve the performance of MFEA.", "Keywords": "", "DOI": "10.1016/j.asoc.2022.109775", "PubYear": 2023, "Volume": "132", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi’an, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Xidian University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Security, Xidian University, Xi’an, China"}], "References": [{"Title": "Surrogate-Assisted Evolutionary Framework with Adaptive Knowledge Transfer for Multi-Task Optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "4", "Page": "1930", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Multi-surrogate multi-tasking optimization of expensive problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106262", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 104716140, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0169-8141(22)00133-0", "PubYear": 2022, "Volume": "92", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [], "References": []}, {"ArticleId": 104716142, "Title": "Novel optimization approach for stock price forecasting using multi-layered sequential LSTM", "Abstract": "Stock markets can often be one of the most volatile places to invest. Statistical analysis of past stock performance and external factors play a major role in the decision to buy or sell stocks. These factors are all used to maximize profits. Stock price index forecasting has been a subject of great research for many years, and several machine learning and deep learning algorithms have been proposed to simplify this complex task, but little success has been found so far. In order to forecast stocks accurately, it is crucial to understand the context-specific dependence of stock prices on their past values. The use of Long Short Term Memory (LSTM), which is capable of understanding long-term data dependencies, can help overcome this obstacle. In this context, this paper proposes a novel optimization approach for stock price prediction that is based on a Multi-Layer Sequential Long Short Term Memory (MLS LSTM) model which makes use of the adam optimizer. Additionally, the MLS LSTM algorithm uses normalized time series data divided into time steps to determine the relationship between past values and future values in order to make accurate predictions. Furthermore, it eliminates the vanishing gradient problem associated with simple recurrent neural networks. The stock price index is forecasted by taking into account past performance information along with past trends and patterns. The results illustrate that a 95.9% prediction accuracy is achieved on the training data set and a 98.1% accuracy on the testing data set with the MLS LSTM algorithm, which dramatically exceeds the performance of other machine learning and deep learning algorithms. The mean absolute percentage error was observed to be 1.79% on the training set and 2.18% on the testing set, respectively. Moreover, the proposed model is able to estimate the stock price with a normalized root mean squared error of 0.019, thus giving an accurate forecast and making it a feasible real-world solution.", "Keywords": "", "DOI": "10.1016/j.asoc.2022.109830", "PubYear": 2023, "Volume": "134", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, 600127, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, 600127, India"}, {"AuthorId": 3, "Name": "<PERSON> A.V.", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, 600127, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Digital Engineering, Solution Center-H, Photo Inc. DLF Cyber City, Chennai, 600089, India"}, {"AuthorId": 5, "Name": "Kong Fah Tee", "Affiliation": "Faculty of Engineering and Quantity Surveying, INTI International University, 71800 Nilai, Malaysia;Corresponding author"}, {"AuthorId": 6, "Name": "Sabireen H.", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Chennai, 600127, India"}, {"AuthorId": 7, "Name": "Janakiraman N.", "Affiliation": "Department of Electronics and Communication Engineering, K.L.N. College of Engineering, Madurai, Tamil Nadu, India"}], "References": [{"Title": "Stock Market Prediction Using LSTM Recurrent Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "1168", "JournalTitle": "Procedia Computer Science"}, {"Title": "Optimizing LSTM for time series prediction in Indian stock market", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2091", "JournalTitle": "Procedia Computer Science"}, {"Title": "Analysis of look back period for stock price prediction with RNN variants: A case study on banking sector of NEPSE", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "788", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": *********, "Title": "Differences between remote and analog design thinking through the lens of distributed cognition", "Abstract": "<p>Due to the huge surge in remote work all over the world caused by the COVID-19 pandemic, today's work is largely defined by tools for information exchange as well as new complex problems that must be solved. Design Thinking offers a well-known and established methodological approach for iterative, collaborative and interdisciplinary problem solving. Still, recent circumstances shed a new light on how to facilitate Design Thinking activities in a remote rather than an analog way. Due to Design Thinking's high production of artifacts and its focus on communication and interaction between team members, the theory of Distributed Cognition, specifically the Distributed Cognition for Teamwork (DiCoT) framework, provides an interesting perspective on the recent going-remote of Design Thinking activities. For this, we first highlight differences of analog vs. remote Design Thinking by analyzing corresponding literature from the recent years. Next, we apply the DiCoT framework to those findings, pointing out implications for practical facilitation of Design Thinking activities in an analog and remote setting. Finally, we discuss opportunities through artificial intelligence-based technologies and methods.</p>", "Keywords": "Human-computer interaction (HCI); artificial intelligence - AI; Distributed Cognition for Teamwork; Design thinking (DT); Remote work", "DOI": "10.3389/frai.2022.915922", "PubYear": 2022, "Volume": "5", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department for Human-Centered Engineering and Design, Fraunhofer Institute for Applied Information Technology, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department for Human-Centered Engineering and Design, Fraunhofer Institute for Applied Information Technology, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department for Data Science and AI, Fraunhofer Institute for Applied Information Technology, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department for Human-Centered Engineering and Design, Fraunhofer Institute for Applied Information Technology, Germany"}], "References": []}, {"ArticleId": 104716354, "Title": "Prediction of stock price direction using the LASSO-LSTM model combines technical indicators and financial sentiment analysis", "Abstract": "<p>Correctly predicting the stock price movement direction is of immense importance in the financial market. In recent years, with the expansion of dimension and volume in data, the nonstationary and nonlinear characters in finance data make it difficult to predict stock movement accurately. In this article, we propose a methodology that combines technical analysis and sentiment analysis to construct predictor variables and then apply the improved LASSO-LASSO to forecast stock direction. First, the financial textual content and stock historical transaction data are crawled from websites. Then transfer learning Finbert is used to recognize the emotion of textual data and the TTR package is taken to calculate the technical indicators based on historical price data. To eliminate the multi-collinearity of predictor variables after combination, we improve the long short-term memory neural network (LSTM) model with the Absolute Shrinkage and Selection Operator (LASSO). In predict phase, we apply the variables screened as the input vector to train the LASSO-LSTM model. To evaluate the model performance, we compare the LASSO-LSTM and baseline models on accuracy and robustness metrics. In addition, we introduce the Wilcoxon signed rank test to evaluate the difference in results. The experiment result proves that the LASSO-LSTM with technical and sentiment indicators has an average 8.53% accuracy improvement than standard LSTM. Consequently, this study proves that utilizing historical transactions and financial sentiment data can capture critical information affecting stock movement. Also, effective variable selection can retain the key variables and improve the model prediction performance.</p>", "Keywords": "Stock price forecast;Financial sentiment analysis;LASSO-LSTM model;Technical indicators;Variable selection", "DOI": "10.7717/peerj-cs.1148", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Technology and Business University, School of Mathematics and Statistics, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Big Data Statistics, Guizhou University of Finance and Economic, Guiyang, Guizhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Technology and Business University, Economic Research Center of the Upper Yangtze River,  Chongqing, China"}], "References": [{"Title": "Stock price prediction based on deep neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>g Yan", "PubYear": 2020, "Volume": "32", "Issue": "6", "Page": "1609", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Stock market movement forecast: A Systematic review", "Authors": "O <PERSON>; <PERSON><PERSON>Quimbaya", "PubYear": 2020, "Volume": "156", "Issue": "", "Page": "113464", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-Factor RFG-LSTM Algorithm for Stock Sequence Predicting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "4", "Page": "1041", "JournalTitle": "Computational Economics"}, {"Title": "Predicting market movement direction for bitcoin: A comparison of time series modeling methods", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "89", "Issue": "", "Page": "106905", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "LSTM-based sentiment analysis for stock price forecast", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "A comprehensive survey on deep neural networks for stock market: The need, challenges, and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114800", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid approach of adaptive wavelet transform, long short-term memory and ARIMA-GARCH family models for the stock index prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115149", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Stock Prediction Based on Technical Indicators Using Deep Learning Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "1", "Page": "287", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 104716410, "Title": "PatrIoT: practical and agile threat research for IoT", "Abstract": "The Internet of things (IoT) products, which have been widely adopted, still pose challenges in the modern cybersecurity landscape. Many IoT devices are resource-constrained and almost constantly online. Furthermore, the security features of these devices are less often of concern, and fewer methods, standards, and guidelines are available for testing them. Although a few approaches are available to assess the security posture of IoT products, the ones in use are mostly based on traditional non-IoT-focused techniques and generally lack the attackers’ perspective. This study provides a four-stage IoT vulnerability research methodology built on top of four key elements: logical attack surface decomposition, compilation of top 100 weaknesses, lightweight risk scoring, and step-by-step penetration testing guidelines. Our proposed methodology is evaluated with multiple IoT products. The results indicate that PatrIoT allows cyber security practitioners without much experience to advance vulnerability research activities quickly and reduces the risk of critical IoT penetration testing steps being overlooked.", "Keywords": "IoT attack surfaces; Weaknesses; Risk scoring; Penetration testing guidelines; MITRE CWE; OWASP IoT Top 10", "DOI": "10.1007/s10207-022-00633-3", "PubYear": 2023, "Volume": "22", "Issue": "1", "JournalId": 25892, "JournalTitle": "International Journal of Information Security", "ISSN": "1615-5262", "EISSN": "1615-5270", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, KTH Royal Institute of Technology, Stockholm, Sweden"}], "References": [{"Title": "Cyber security threat modeling based on the MITRE Enterprise ATT&CK Matrix", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "1", "Page": "157", "JournalTitle": "Software & Systems Modeling"}]}, {"ArticleId": *********, "Title": "On One Routing Problem Oriented on the Problem of Dismantling Radiation-Hazardous Objects", "Abstract": "We consider a problem of sequential visiting of megalopolises under the preceding conditions and costs functions depending on the list of tasks currently unfulfilled. Selection of a routing process involving index permutation, trajectory and starting point is optimized; point of finish is optimized also. We use additive criterion consisting in summary costs of external (as for megalopolises) movings, costs of works related to visiting of megalopolises and assessments of the terminal state. Procedure of construction of optimal solution based on widely understood dynamic programming is investigated. The statement is focused on the problem of dismantling the system of radiation–hazardous sources; at the same time, it is assumed that not all sources are dismantled (it is possible when workers receive maximum doses of radiation), which requires evacuation in conditions of radiation exposure of sources that remain undismantled. A specific variant of the criterion is reduced to the summary dose of radiation received by an employee both at the stage of dismantling and at the stage of evacuation. An algorithm based on the theoretical constructions is proposed and realized on personal computer; a computational experiment is completed. © 2022 South Ural State University. All rights reserved.", "Keywords": "dynamic programming; preceding conditions; route; trace", "DOI": "10.14529/mmp220306", "PubYear": 2022, "Volume": "15", "Issue": "3", "JournalId": 26368, "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\"", "ISSN": "2071-0216", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "N.N. <PERSON>skii Institute of Mathematics and Mechanics, Yekaterinburg, Russian Federation; Ural Federal University, Yekaterinburg, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "N.N. K<PERSON>skii Institute of Mathematics and Mechanics, Yekaterinburg, Russian Federation"}], "References": []}, {"ArticleId": 104716472, "Title": "It’s all about the text: An experimental investigation of inconsistent reviews on restaurant booking platforms", "Abstract": "<p>Consumer-generated reviews play a decisive role in creating trust and facilitating transactions on digital platforms. However, prior research shows various problems, e.g., only a small number of consumers providing reviews, fake reviews, and inconsistent reviews. We use an experiment in the context of a restaurant booking platform to examine the impact of inconsistent reviews on the duration of consumers’ transaction decisions. In a second experiment, we investigate the relative importance of the review components in the case of inconsistent reviews. Drawing on the dual-process theory and media richness theory, we predict that inconsistent reviews result in a longer time required for consumers’ transaction decisions (H1) and lead to users’ transaction decisions being predominantly based on the qualitative component (H2). Although we do not find general support that inconsistent restaurant reviews negatively determine the duration of transaction decisions, we find evidence that in the case of inconsistent restaurant reviews, the polarity of the qualitative component is crucial for both the duration of the transaction decision and the decision itself.</p>", "Keywords": "Consumer reviews; Online reviews; Inconsistent reviews; Reputation; Digital platform; Consumer decision-making; M31", "DOI": "10.1007/s12525-022-00525-3", "PubYear": 2022, "Volume": "32", "Issue": "3", "JournalId": 16578, "JournalTitle": "Electronic Markets", "ISSN": "1019-6781", "EISSN": "1422-8890", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Business Analytics, Ulm University, Ulm, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Business Analytics, Ulm University, Ulm, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Business Analytics, Ulm University, Ulm, Germany"}], "References": [{"Title": "A picture is worth a thousand words: how images influence information quality and information load in online reviews", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "775", "JournalTitle": "Electronic Markets"}, {"Title": "Supporting customer-oriented marketing with artificial intelligence: automatically quantifying customer needs from social media", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "351", "JournalTitle": "Electronic Markets"}, {"Title": "Reputation portability – quo vadis?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "331", "JournalTitle": "Electronic Markets"}, {"Title": "Exploring thematic composition of online reviews: A topic modeling approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "791", "JournalTitle": "Electronic Markets"}, {"Title": "May we buy your love? psychological effects of incentives on writing likelihood and valence of online product reviews", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "805", "JournalTitle": "Electronic Markets"}, {"Title": "Effect of an e-retailer’s product category and social media platform selection on perceived quality of e-retail products", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "1", "Page": "139", "JournalTitle": "Electronic Markets"}]}, {"ArticleId": 104716571, "Title": "Constrained multimodal multi-objective optimization: Test problem construction and algorithm design", "Abstract": "Solving multimodal multi-objective optimization problems (MMOPs) has received increasing attention. However, recent studies only consider unconstrained MMOPs. Given the fact that there are usually constraints in real-world optimization problems, in this work, we propose a test problem construction approach for constrained multimodal multi-objective optimization. Based on the approach, a test suite, containing 14 instances with diverse features and difficulties, is created. Meanwhile, a new evolutionary framework is tailored for this kind of problem. We test the proposed framework in the experiments and compare it to state-of-the-art multimodal multi-objective optimization algorithms on the proposed test suite. The results reveal that the proposed test suite is challenging and it can motivate researchers to develop new algorithms. In addition, the superiority of our proposed framework demonstrates its effectiveness in handling constrained MMOPs.", "Keywords": "Constrained multimodal multi-objective optimization ; Evolutionary algorithm ; Test problem construction ; Algorithm design", "DOI": "10.1016/j.swevo.2022.101209", "PubYear": 2023, "Volume": "76", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, China University of Geosciences, Wuhan 430074, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Ningbo Power Supply Company, Ningbo 315000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beibu Gulf Ocean Development Research Center, Beibu Gulf University, Qinzhou 535000, China"}], "References": [{"Title": "Push and pull search embedded in an M2M framework for solving constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100651", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Purpose-directed two-phase multiobjective differential evolution for constrained multiobjective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100799", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Peeking beyond peaks: Challenges and research potentials of continuous multimodal multi-objective optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "136", "Issue": "", "Page": "105489", "JournalTitle": "Computers & Operations Research"}, {"Title": "A constrained multi-objective evolutionary algorithm using valuable infeasible solutions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "68", "Issue": "", "Page": "101020", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A grid-guided particle swarm optimizer for multimodal multi-objective problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "117", "Issue": "", "Page": "108381", "JournalTitle": "Applied Soft Computing"}, {"Title": "A two-archive model based evolutionary algorithm for multimodal multi-objective optimization problems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "", "Page": "108606", "JournalTitle": "Applied Soft Computing"}, {"Title": "A tri-population based co-evolutionary framework for constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "", "Page": "101055", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A reinforcement learning level-based particle swarm optimization algorithm for large-scale optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "602", "Issue": "", "Page": "298", "JournalTitle": "Information Sciences"}, {"Title": "Evolutionary Algorithm with Dynamic Population Size for Constrained Multiobjective Optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "101104", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 104716573, "Title": "Impacts of the spatial configuration of built-up areas and urban vegetation on land surface temperature using spectral and local spatial autocorrelation indices", "Abstract": "Understanding how the spatial configuration of land cover patterns of built-up areas and urban vegetation affect urban surface temperatures is crucial for improving the sustainability of cities as well as optimizing urban design and landscape planning. Because of their capability to detect distinct surface thermal features, satellite data have proved useful in exploring the impacts of spatial configuration of land cover on land surface temperature (LST). In this study, we examine how the spatial configuration of built-up and urban vegetation affects the LST in the Harare metropolitan city, Zimbabwe. In order to achieve this objective, we combined the LST, local spatial statistics of Getis-Ord Gi* and local Moran’s I statistic, Normalized Difference Vegetation Index (NDVI) and the Normalized Difference Built-Up Index (NDBI) derived from multi-date Landsat satellite data (1994, 2001 and 2017). The results of local Moran’s I statistic showed moderate and negative correlations between LST and Landsat derived NDVI. Overall, these results of local Moran’s I statistic demonstrate that clustered vegetation tend to lower LST, providing thermal comfort conditions. In contrast, clustered spatial arrangements of NDBI based on the Getis-Ord Gi* elevate LST, implying that continued clustered built-up expansion has the potential to increase urban surface temperatures. Acknowledgements The authors would like to thank colleagues at our various institutions for their valuable comments and suggestions on improving the quality of the paper. Disclosure statement No potential conflict of interest was reported by the author(s).", "Keywords": "", "DOI": "10.1080/2150704X.2022.2142073", "PubYear": 2022, "Volume": "13", "Issue": "12", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Geography, Environmental Sustainability and Resilience Building, Faculty of Social Sciences, Midlands State University, Gweru;Discipline of Geography, School of Agricultural, Earth and Environmental Sciences, University of KwaZulu-Natal, Private Bag X01, Scottsville, Pietermaritzburg 3209, South Africa"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Water Studies, Department of Earth Sciences, University of the Western Cape, Private Bag X17, Bellville, 7535, South Africa"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Discipline of Geography, School of Agricultural, Earth and Environmental Sciences, University of KwaZulu-Natal, Private Bag X01, Scottsville, Pietermaritzburg 3209, South Africa;Department of Space Science and Applied Physics, University of Zimbabwe, P.O. Box MP167, Pleasant, Harare, Zimbabwe"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Science and Technology, Parthenope University of Naples, Centro Direzionale, Isola C4, 80143, Naples, Italy"}, {"AuthorId": 5, "Name": "Tatenda Nyenda", "Affiliation": "Department of Geography, Environmental Sustainability and Resilience Building, Faculty of Social Sciences, Midlands State University, Gweru;Restoration and Conservation Biology Research Group, School of Animal, Plant and Environmental Sciences, University of the Witwatersrand, Private Bag 3, Wits 2050, Johannesburg, South Africa"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Physics, Geography and Environmental Science, Gary <PERSON> School of Agriculture And Natural Sciences, Great Zimbabwe University, P.O Box 1235, Masvingo, Zimbabwe"}, {"AuthorId": 7, "Name": "Tsikai <PERSON>", "Affiliation": "Information and Communication Technology (ICT) department, Zimbabwe Land Commission (ZLC), 19280, Borrowdale Road, Block 1, Celestial park, Borrowdale, Harare, Zimbabwe"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "OYAK and Cimpor Côte d’Ivoire, Department of Raw Material Production, PK 24 Akoupe, Abidjan, Côte d’Ivoire"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Satellite Communications and Remote Sensing, Informatics Institute, Istanbul Technical University, ITU Ayazağa Kampüsü, Sarıyer, 34467, İstanbul, Turkey"}], "References": []}, {"ArticleId": 104716585, "Title": "Super-resolution of Sentinel-2 images using Wasserstein GAN", "Abstract": "The Sentinel-2 satellites deliver 13 band multi-spectral imagery with bands having 10 m, 20 m or 60 m spatial resolution. The low-resolution bands can be upsampled to match the high resolution bands to extract valuable information at higher spatial resolution. This paper presents a Wasserstein Generative Adversarial Network (WGAN) based approach named as DSen2-WGAN to super-resolve the low-resolution (i.e., 20 m and 60 m) bands of Sentinel-2 images to a spatial resolution of 10 m. A proposed generator is trained in an adversarial manner using the min-max game to super-resolve the low-resolution bands with the guidance of available high-resolution bands in an image. The performance evaluated using metrics such as Signal Reconstruction Error (SRE) and Root Mean Squared Error (RMSE) shows the effectiveness of the proposed approach as compared to the state-of-the-art method, DSen2 as the DSen2-WGAN reduced RMSE by 14.68% and 7%, while SRE improved by almost 4% and 1.6% for 6 × and 2 × super-resolution. Lastly, for further evaluation, we have used trained DSen2-WGAN model to super-resolve the bands of EuroSAT dataset, a satellite image classification dataset based on Sentinel-2 images. The per band classification accuracy of low-resolution bands shows significant improvement after super-resolution using our proposed approach. Acknowledgement(s) The authors acknowledge the <PERSON><PERSON> <PERSON>, Prof<PERSON> <PERSON> and anonymous reviewers for their valuable suggestions in improving the manuscript. Disclosure statement No potential conflict of interest was reported by the author(s).", "Keywords": "", "DOI": "10.1080/2150704X.2022.2136019", "PubYear": 2022, "Volume": "13", "Issue": "12", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Institute of Space Technology, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Space Science, Institute of Space Technology, Islamabad, Pakistan;School of Geography and Sustainable Development, University of St Andrews, St Andrews, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical, Automotive and Materials Engineering, University of Windsor, Canada"}], "References": []}, {"ArticleId": 104716592, "Title": "Deep neural network-based spatial gap-filling of MODIS ice surface temperatures over the Arctic using satellite and reanalysis data", "Abstract": "Because ice surface temperature (IST) controls snow melt, sea ice growth and air–ocean heat exchange, it is a key parameter in analyses of the Arctic climate system. However, optical satellite-based IST data often have missing values due to the satellite orbit, clouds and polar night. In this study, we estimated IST using a deep neural network (DNN) algorithm based on meteorological, sea ice and geometric variables that are strongly correlated with IST. Moderate-Resolution Imaging Spectroradiometer (MODIS)/Terra IST data were used, and the input data were 2 m air temperature (Ta), 30-year averaged Ta (Ta climatology), total column water vapour (TCWV), solar zenith angle (SZA), local solar noon angle (LSN) and latitude. The data were classified into six cases according to sea ice age (SIA) and Ta to create an efficient DNN model that supplemented the IST data missing from the existing dataset. Model validation based on the MODIS IST data revealed a correlation coefficient of 0.94, root mean square error of 3.54 K and relative root mean square error of 1.35%, showing high accuracy.", "Keywords": "Ice surface temperature ; MODIS ; deep neural network ; Arctic", "DOI": "10.1080/2150704X.2022.2138620", "PubYear": 2022, "Volume": "13", "Issue": "12", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "Suyoung Sim", "Affiliation": "Division of Earth Environmental System Science (Major of Spatial Information System Engineering), Pukyong National University, Busan, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Korea Ocean Satellite Center, Korea Institute of Ocean Science and Technology, Busan, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Remote Sensing and GIS, Korea Polar Research Institute, Incheon, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Earth Environmental System Science (Major of Spatial Information System Engineering), Pukyong National University, Busan, Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Earth Environmental System Science (Major of Spatial Information System Engineering), Pukyong National University, Busan, Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Earth Environmental System Science (Major of Spatial Information System Engineering), Pukyong National University, Busan, Korea"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Han", "Affiliation": "Division of Earth Environmental System Science (Major of Spatial Information System Engineering), Pukyong National University, Busan, Korea"}], "References": [{"Title": "Characteristics of the Reanalysis and Satellite-Based Surface Net Radiation Data in the Arctic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Journal of Sensors"}]}, {"ArticleId": 104716720, "Title": "Predicting Credit Scores with Boosted Decision Trees", "Abstract": "<p>Credit scoring models help lenders decide whether to grant or reject credit to applicants. This paper proposes a credit scoring model based on boosted decision trees, a powerful learning technique that aggregates several decision trees to form a classifier given by a weighted majority vote of classifications predicted by individual decision trees. The performance of boosted decision trees is evaluated using two publicly available credit card application datasets. The prediction accuracy of boosted decision trees is benchmarked against two alternative machine learning techniques: the multilayer perceptron and support vector machines. The results show that boosted decision trees are a competitive technique for implementing credit scoring models.</p>", "Keywords": "", "DOI": "10.3390/forecast4040050", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 54589, "JournalTitle": "Forecasting", "ISSN": "", "EISSN": "2571-9394", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Lisbon School of Economics and Management (ISEG) and CEMAPRE/REM, Universidade de Lisboa, 1200-781 Lisboa, Portugal"}], "References": [{"Title": "Credit scoring based on tree-enhanced gradient boosting decision trees", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "189", "Issue": "", "Page": "116034", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 104716793, "Title": "Drivers of Mobile Payment Services Adoption: A Behavioral Reasoning Theory Perspective", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2022.2144122", "PubYear": 2024, "Volume": "40", "Issue": "7", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "Abdelkader M. <PERSON>", "Affiliation": "Department of Business Administration, Faculty of Commerce, Mansoura University, Mansoura, Egypt;Faculty of Business, New Mansoura University, Egypt"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Faculty of Commerce, Mansoura University, Mansoura, Egypt"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Faculty of Commerce, Mansoura University, Mansoura, Egypt"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Faculty of Commerce, Mansoura University, Mansoura, Egypt"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Business Administration, Faculty of Commerce, Mansoura University, Mansoura, Egypt;Department of Business Administration, College of Economics and Administrative Sciences, Imam <PERSON> Islamic University (IMSIU), Riyadh, Saudi Arabia"}], "References": [{"Title": "An affective response model for understanding the acceptance of mobile payment systems", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "", "Page": "100905", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Exploring barriers affecting eLearning usage intentions: an NLP-based multi-method approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "5", "Page": "1002", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "A qualitative study of consumer resistance to mobile payments for in-store purchases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "634", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 104716812, "Title": "Factors Determining Whether an Art Museum Will Offer Virtual Content: An Empirical Study in South Korea", "Abstract": "This study examines the academic definition of virtual museums and the factors determining whether South Korean art museums will offer virtual content. First, this paper collects definitions of virtual museums from previous studies and identifies the key attributes to then suggest a comprehensive definition. The final definition arrived upon is as follows: A museum built within a digital space with a collection of digitized objects, such as images, audio files, text-based documents, and using virtual reality to supplement, augment, and enrich the museum experience. Next, this study assesses the current digitalization level of South Korea’s registered art museums and examines which museum attributes determine whether there is virtual content offered by the art museum. The research findings show that the number of museums with virtual content are quite limited. Additionally, the presence of collection information, the size of the collection, and the exhibition space’s size all affect the presence of virtual content significantly. Considering these outcomes, this study suggests possible practical and policy measures to enhance the experience of immersive cultural heritage. 1. Introduction A museum is not simply a container of artifacts, but a complex reflection of culture that continuously changes based on its social and political context. Since the nineteenth century, art museums, once the privileged domain of the wealthy, have become public spaces that incorporate cultural entertainment (Günay, Citation 2012 ). For example, <PERSON><PERSON><PERSON> coins the phrase “post-museum,” emphasizing its role in actively communicating with visitors (Buffington, Citation 2005 ). <PERSON> also highlights the paradigm shift regarding art museums as “a transition from material to experience, from conservation-focus to education-focus, from enlightenment to edutainment, from supplier-focus to user-focus, from state-focus to region-focus, from standardization to specialization, from offline to online integration, from bureaucratism to business rationalism, from curatorial organization-focus to professional-focus, and from sedimentation of memories to creation of the future” (Park, Citation 2019 , p. 46). Another paradigm shift seems to be awaiting the art world. If the earlier change in a museum’s accessibility and role was driven by political upheaval, now digitalization hints at another change in how artwork is presented and displayed. While at an early developmental stage, a plethora of image-techniques, texts, and databases now make it possible to offer digital archives (Bovcon, Citation 2021 ). The content within a physical art museum has begun to take on a digitized form. The transition of digital art from digital video to software-driven installations further drives the need to digitize the conventional museum (Saaze et al., Citation 2018 ). One prominent case is Google Arts & Culture, the online art platform initiated by Google Cultural Institute in 2011, replete with collaborations from 17 world-class museums. Next, the Covid-19 outbreak and Virtual Reality (VR) advancement pushed this transformation even further. Given that most museums had to shut down during the pandemic due to social distancing regulations, a channel to reach viewers by another means than offline exhibitions turned out to be an important goal for the modern museum. For instance, while 13% of organizers held online exhibitions in 2019, in 2020, 62% of organizers offered online exhibition, showing an increase of 4.5 times the number of online exhibitions (CEIR, Citation 2021 ). Unfortunately, the sector’s adoption of technology has not been archived precisely in South Korea. In fact, domestic studies on the virtual museum itself are limited, with only 68 articles since 2000 in DBpia, a Korean academic database. Plus, in South Korea, the term virtual museum has been used interchangeably with conventional websites and museum-run social media. For instance, Park and Kim do not delineate between a 3 D simulated space and a museum website, describing both as “virtual museums” (M. Park & Kim, Citation 2016 ). Park also presents Daelim Museum’s social media and Leeum’s online website as virtual museums (M. K. Park, Citation 2017 ). Likewise, Yang and Shin also describe both Naver Museum and Seoul Museum of Art’s website as virtual museums (Yang & Shin, Citation 2012 ). In South Korea, the term virtual museum was first used predominantly in 2011 when Google Arts & Culture officially started its global service. Around the same time, Naver Museum and Savina Museum launched the first virtual museums in South Korea. Naver began its Museum View for the National Museum of Art in December 2011, opening the first South Korean virtual museum (D. Kim, Citation 2011 ; Yang & Shin, Citation 2012 ). Savina Museum is also described as the first domestic museum to adopt virtual reality photogrammetry, which it did in 2012. Two technology companies are notable with regard to the South Korean virtual museum: Matterport and Naver. Matterport, a US-based 3 D media technology company, provides 3 D VR tours with high-resolution spatial scanning. National Art Museums, Savina Museum, and museums utilizing panorama VR currently use Matterport as their main platform. Naver, a major Internet portal in South Korea, also plays a significant role. In creating the first virtual museum, Naver made an MOU with the National Museum of Korea in 2011, creating a cyber tour for the museum and its collection (M. Park & Kim, Citation 2016 ; Research Planning Department, Citation 2011 ). In 2020, South Korea’s D Museum broadcasted a private exhibition tour through Naver TV (J. Kim, Citation 2020 ). In fact, many art fairs and museum exhibitions still utilize the Naver Reservation system for booking tickets. However, Naver Z, a subsidiary of Naver Corporation launched the virtual museum with 69 historical artworks in Zepeto, a metaverse platform (H. Kim, Citation 2020 ). Thus, Naver not only positions itself within the museum sector as a useful platform and media solution for digitalizing conventional museums but also as an independent virtual museum. The previous literature discusses South Korea’s virtual museums after the Covid-19 pandemic. According to Park, the National Museum of Modern and Contemporary Art, Korea (MMCA) first initiated the virtual tour for the exhibition, “The Modern and Contemporary Korean Writing” on YouTube. As for the MMCA, an exhibition named, “Horizon of Axis” has been broadcasted on Instagram Live (S. Park, Citation 2021 ). Currently, the street views and exhibitions of MMCA Gwacheon, Seoul, and Deoksugung are published through Google Arts & Culture (S. Park, Citation 2021 ). Yoo introduced the Busan Museum of Art’s [ONOOOFF] exhibition as an integrational exhibition between offline and online space through virtual technology and a head-mounted device (HMD) (Yoo, Citation 2021 ). In 2020, Seoul Olympic Museum of Art (SOMA) also launched MOVE SOMA, the first full 3 D online museum in Korea built using 3 D Unreal Engine technology, which is differentiated from general panorama VR (Seoul Olympic Museum of Art, Citation 2021 ). Savina Museum, one of the first private museums to adopt VR technology in 2012, advanced its technology and opened its meta Savina Art museum in 2022 (Kim, Citation 2022 ). Notably, Ernst, alongside his colleagues, emphasize the importance of change in the institution, especially within this highly dynamic Internet Communication Technologies (ICT)-driven environment and pluralistic culture (Ernst, Citation 2016 ). The critical significance of the contemporary art museum’s digitalization process has prompted researchers to examine different themes, including customer satisfaction on the introduction of ICTs in museums, new business models sparked by digitalization, and the managerialization of museum activities (Aristova et al., Citation 2020 ; Güner & Gülaçti, Citation 2022 ; Ruggiero et al., Citation 2022 ). Preparing for change, meeting new social desires, and managing fear are no longer options. They are mandatory. However, despite the rising interest in the virtualization of art museums, how to define a virtual museum has not been actively discussed. Although the digitalization of cultural institutions has progressed continually since the early 2000s, its current stage has not been evaluated objectively. The question of whether the virtual museum is a real boom in the art market and whether it fully succeeds in its role as an art platform have not been answered. Therefore, this study attempts to define what the virtual museum really is, based on the academic literature, and whether Korean art museums are ready for such virtualization. First, this study aims to examine the definition of virtual museum from the established academic literature. Second, this study identifies the current stage of virtualization for South Korea’s registered art museums and explores the factors affecting the presence of virtual content. The remainder of this paper proceeds as follows. First, a literature review has been conducted to examine the academic perspective being presented. Given that there was a dramatic change during the Covid-19 outbreak, most reviews focus on the post-Covid-19 online exhibition and related approaches. Next, the research model is introduced, and the analysis method is explained to investigate the actual digitalization level of registered art museums and the factors that help determine whether there will be virtual content. Finally, this study’s limitation will be discussed. 2. Literature review and theoretical background 2.1. Changes to the concept of a museum The definition of a museum is always changing. Folga-Januszewska offers a historical overview of the concept of the museum (Folga-Januszewska, Citation 2020 ). According to the International Council of Museum (ICOM), the current definition is: “A museum is a non-profit, permanent institution in the service of society and its development, open to the public, which acquires, conserves, researches, communicates, and exhibits the tangible and intangible heritage of humanity and its environment for the purposes of education, study and enjoyment” ( Museum Definition , Citation 2007 ). This definition has been set by ICOM, “a non-governmental organization that establishes professional and ethical standards for museum activities” ( Museum Definition , Citation 2007 ). In addition to ICOM’s definition, each country also defines the role of the museum, and South Korea is no exception. In the Museum and Art Gallery Support Act, South Korea defines the museum thus: “a facility established to collect, manage, preserve, survey, research, exhibit, and offer educational material related to history, archaeology, the human race, folklore, arts, fauna and flora, minerals, science, technology, industries, etc., in order to contribute to developing culture, arts, and learning; enhancing the general public’s enjoyment of culture, and facilitating lifelong education” (Museum and Art Gallery Support Act, Citation 2018 ). Despite such efforts, the definition remains controversial for failing to incorporate the shared values of modern society (Byun, Citation 2020 ; Folga-Januszewska, Citation 2020 ; Fraser, Citation 2019 ; Mairesse, Citation 2020 ; Soares, Citation 2021 ). Indeed, there is a fierce debate over the definition of a museum, questioning its inherent role. Furthermore, the ICOM revised definition has been endlessly delayed, without ever reaching a consensus. 2.2. Virtualization and the virtual museum The concept of virtualization is in a state of continuous change, and various studies discuss different forms of virtualization. For instance, Girvan emphasizes that it is simulated spaces with certain levels of interactions, aimed at achieving a shared understanding (Girvan, Citation 2018 ). Nagy and Koles describe the virtuality as a computer-mediated world in persistent three-dimensional spaces (Nagy & Koles, Citation 2014 ). The growing dissemination and acceptance of VR technology has strengthened the emergence of virtual museums. In particular, their recent growth can be traced not only to technological advancement, but also to the development of the digital arts and, of course, the Covid-19 outbreak. This situational background has greatly accelerated the discussion about the virtual museum. For instance, Bovcon examines the case studies concerning the transitioning of new-media art exhibitions from real gallery spaces into virtual museum presentations (Bovcon, Citation 2021 ). Saaze and her colleagues also investigate the processes through which digital art has become embedded in the museum (Saaze et al., Citation 2018 ). Additionally, strategies and development for the digital-age museum have also been saliant topics at ICOM Kyoto (Mairesse, Citation 2020 ). While the term virtual museum is a buzzword in both academia and in real practice, scant attention has been paid to what a “virtual museum” actually is. In the established literature, the virtual museum is discussed regarding its different concepts, types, and levels. Notably, the concept has been around even before the Internet (Schweibenz, Citation 2019 ). The early virtual museum took the form of multimedia and hypermedia applications on CD-ROM and stand-alone computers (Schweibenz, Citation 2019 ). As Tanasi and his colleagues emphasize, 3 D digital imaging is growing more popular, generating virtual projects of cultural heritage experiences (Tanasi et al., Citation 2018 ). For instance, Cirulis et al. describe the virtualization of digitalized cultural heritage as a worldwide use of VR and augmented reality (AR), and gamification use for tourism and cultural heritage promotion (Cirulis et al., Citation 2015 ). Not only is its concept quite varied, but so is the virtual museum itself. According to Meier and his colleagues, starting with digitizing museum collections to create a 360-degree interactive virtual tour, various forms of virtual museums now exist (Meier et al., Citation 2021 ). Francisco also details three types of virtual museums: e-booklet, virtual world museum, and interactive online museums (Bittencourt-Francisco, Citation 2020 ). Lee et al. present that Google Arts & Culture, Louvre 360, Zepeto, and Matterport as four main cases of virtual exhibitions and virtual museum platforms (Lee et al., Citation 2021 ). Interestingly, Urbaneja distinguishes between online gallery tours and interactive exhibits (Urbaneja, Citation 2020 ). “Virtual walks” around monuments and museum centers in Poland, as Gutowski and his colleagues suggest, is a kind of virtual museum, thus widening the concept to include outdoor and public spaces (Gutowski & Kłos-Adamkiewicz, Citation 2020 ). In tandem with technological advancement, different forms of online exhibitions keep appearing, making it difficult to reach a consensus on the concept. Additionally, immersion levels of virtualization are also classified. The first is Desktop VR, which uses traditional means (e.g., a computer screen and a keyboard) to interact with the virtual world. The second level is a virtual environment achieved by using six degrees of freedom, generated by a VR system, such as a head-mounted display (HMD) and VR controller (Meier et al., Citation 2021 ). The level can also be classified as either fully-immersive and non-immersive (Y. Kim & Lee, Citation 2022 ). Therefore, it is critical to define the range of virtualization to evaluate the current situation accurately. RQ1. What is the virtual museum, based on the various definitions presented in academic studies? 2.3. Previous research on offering a virtual museum The body of research on the virtual museum is limited largely to case studies. Furthermore, no research has yet conducted a comprehensive review of the virtual museum. For instance, Urbaneja rightly questions the absence of a systematic definition of an online museum and focuses on six art museums in Spain, the United States, and the United Kingdom (Urbaneja, Citation 2020 ). Yook and his colleagues applied an online exhibition integrated model to five case studies in South Korea (Yook & Lee, Citation 2018 ). Additionally, given the concern about the limitation of tangible experiences, few researchers have conducted experiments on museum visitors. For instance, Sundar et al. experiment with the effect that communication technology has had on creating enjoyable museum experiences (Sundar et al., Citation 2015 ). In the Chinese context, Jin et al. examine the educational effects of VR technology in museums (Jin et al., Citation 2022 ). By employing the uses and gratifications approach, Kim and Lee try to ascertain the motives for—and consequences of—watching 360-degree VR art (Y. Kim & Lee, Citation 2022 ). Such studies not only focus on the visitor experience but, for some, also limit their subject area to virtual exhibitions. Kim and Yong analyze online exhibitions in the post-Covid-19 era and categorize them based on their relationship to offline exhibitions. Their categories are: (1) supportive exhibition, (2) parallel exhibition, (3) independent exhibition, and (4) alternative exhibition (B. Kim & Yong, Citation 2020 ). In a similar vein, Oh categorizes overseas exhibitions based on viewers’ perspectives: (1) omniscient, (2) professional, (3) second-person, and (4) first-person (Oh, Citation 2021 ). Lee and her colleagues conduct a general review, offering case studies of 15 domestic/international online exhibitions between March 2020 and June 2021, which they divide into three groups based on platform type: (1) web page, (2) exhibition booth, and (3) metaverse (Lee et al., Citation 2021 ). As for Yun, VR/AR exhibitions are classified in three ways: (1) museum-specific, (2) place-specific exhibitions, and (3) non-place-specific exhibitions (Yun, Citation 2021 ). Because the established literature focuses on visitors and content, a more comprehensive literature review covering the platform level is much needed to understand this ecosystem more fully. 2.4. The extent of digitalization in art museums Liao and his colleagues specifically look at the digital transformation happening in the museum and heritage sectors. Their study proposes four socio-technical layers: digitization, digitalization, digital transformation, and datafication. According to Liao et al., digitization refers to “the technological means for converting objects into digital resources, and for heritage and museum sector,” while digitalization means “the digital processes through which these resources have been put to use” (Liao et al., Citation 2020 , p. 476). Furthermore, they emphasize that digital transformation (DT) is a “more radical or systematic socio-technical change in organizational and social dimensions beyond mere digital resources or technological processes” (Liao et al., Citation 2020 , p. 476). The fourth layer, datafication, is about the “deeper data process where behaviors and actions have been data filed” (Liao et al., Citation 2020 , p. 476). Although their paper does not mention the hierarchical relationship among four socio-technical layers, it does imply an accumulated layer of technological progress. Creating such layers indeed corresponds to the earlier literature. For instance, one study distinguishes DT from digitalization, arguing that digitalization is more about the operational level, while DT occurs at the strategic level, thereby differentiating between the two concepts based on the scope of change (Gong & Ribiere, Citation 2021 ). In defining DT, Mergel et al. ( Citation 2019 ) elaborate on the concepts in relative progress. For Mergel, digitalization is about potential changes in the processes beyond merely digitizing existing processes and forms; rather, digital transformation emphasizes the cultural, organizational, and relational changes in the outcomes (Mergel et al., Citation 2019 ). Taking its inspiration from Mergel et al. and Liao, this study adopts Liao’s perspective on the four layers of socio-technical transitions to evaluate quantitatively the virtualization status of South Korea’s art museums. Based on these four layers, the current level of digitalization can be divided into another layer, making a total of five. First, if there is no website by which to approach a registered art museum, the level is 0, meaning it has not yet begun its socio-technical transition. Second, if there are converted digital resources, the digitization level is considered to have been stratified. According to Yoon and Lee, the art museum’s primary objective is to collect and exhibit (B. Yoon & Lee, Citation 2020 ). As such, this study categorizes digital resources in two ways: exhibition information and collection information. If there is no information regarding collections and exhibitions on a website, the level is 1. At this level, the art museum has begun its socio-technical transition but has not yet entered the digitization phase due to its lack of digitized resources. Next, if a museum has either collection or exhibition information on its website, its level is 2. If the website has both collection and exhibition information, its assigned level is 3. Lastly, level 4 denotes virtual content being offered by the registered art museum. In this study, virtualization is regarded as the layer of digitalization. A few other papers likewise regard virtualization as a form of digitization, describing it as the digitization of an already existing space (Mergel et al., Citation 2019 ). However, digitalization is about utilizing digital resources, and the virtual museum, in most cases, utilizes digitized resources like collection and exhibition information (Liao et al., Citation 2020 ). Additionally, as Mergel and her colleagues aptly describe, digitalization is “a potential change beyond [the] mere digitizing of existing processes and forms” (Mergel et al., Citation 2019 , p. 12). Virtual content is constructed by Mixed Reality (MR), which is achieved via photogrammetry, 3 D scanning, and 360-degree cameras. because it results in enhanced immersion between real and digital objects, MR allows for ubiquitous accessibility and facilitates the dissemination of information to the audience. Thus, MR can be viewed as a different form and process from traditional digital objects, possessing an immense potential to alter the art museum’s future strategy regarding interacting with viewers (Meier et al., Citation 2021 ). Considering the use of new technology, the level of immersion through its distinctive form, and the potential expansion it may achieve, this study proposes virtualization as a layer of digitalization, marking it as at a level 4. Digital transformation and datafication have been excluded. Digitization and digitalization are at the operational level, while DT lies at a strategic level; this results in a more significant change in its organizational culture and communication (Gong & Ribiere, Citation 2021 ). Because it is difficult to examine its strategy from the user-centric web, DT is excluded. As a result, a total of five levels would be employed to evaluate South Korea’s registered art museums. Thus, this study poses the following research question: RQ 2. What is the current stage of virtualization regarding South Korea’s registered art museums? 2.5. Art museums and related factors Some museums provide virtual content, while others do not. By connecting the virtualization of art museums with museum attributes—such as museum type, location, collection size, number of exhibitions, number of visitors, exhibition space, and digital resources—this study may provide further insight into what affects virtualization. 2.5.1. Museum type Museum type is a significant attribute influencing cultural organization in terms of funding sources and employees. Kim acknowledges how Korean art museums are largely categorized either as public (including national museums) or private (J. Kim, Citation 2021 ). Likewise, Cho also emphasizes the public–private divide in art museums in South Korea (A. Cho, Citation 2018 ). Per the Museum and Art Gallery Support Act of South Korea, officially registered art museums are categorized into four types: national, public, university, and private (Museum and Art Gallery Support Act, Citation 2018 ). Based on this categorization, this study then groups museums into public and private, depending on which institution manages the art museum. According to Guccio et al. ( Citation 2020 ), public expenditures may create a more favorable environment for museums. As such, this study hypothesizes that a publicly-funded art museum is expected to exhibit virtual content more than those that are privately-funded. H1. Publicly-funded art museums are more likely to exhibit virtual content than privately-funded ones. 2.5.2. Location In South Korea, the regional disparities in culture and arts infrastructure are a critical problem, and its unbalanced concentration is mainly in the Seoul Capital Region (SCA) (K. Kim & Hong, Citation 2021 ). As a result, there is a noticeable disparity in the consumption of art and culture between SCA and non-capital regions (K. Kim & Hong, Citation 2021 ; T. Park et al., Citation 2014 ). By considering different traits based on location, Kang focuses specifically on museums in South Korea’s capital region to examine exhibition trends after the Covid-19 pandemic (Kang, Citation 2021 ). Cho also analyzes inequalities concerning cultural capital depending on regional differences (A. Cho, Citation 2018 ). Yun also looks at using VR/AR in museum educational programs centered around SCA (Yun, Citation 2022 ). Location, therefore, can be used as a way to assess the possible cultural disparity between the capital and non-capital regions. Art museums in the capital region are expected to exhibit virtual content more than the art museums in areas outside the capital. For this reason, we propose the following hypothesis: H2. Art museums located in the capital region are more likely to exhibit virtual content than those located in the non-capital region. 2.5.3. Collection size The established academic literature uses the total number of collections as a museum evaluation index (Joo & Kim, Citation 2012 ; S. Kim & Soh, Citation 2017 ; T. Park et al., Citation 2014 ). Barrio and Herrero assess the number of collections to evaluate productivity and technical changes (Barrio & Herrero, Citation 2022 ). Yoon and Shin argue that those with larger collections communicate more efficiently with their community (H. Yoon & Shin, Citation 2014 ). Because websites are important for communicating with the public, museums with larger collections may have a stronger need for virtualization so as to interact effectively with potential visitors. Additionally, along with the exhibition, virtual collections are key digital resource displayed on the art museum website. Therefore, this study sees the number of museum collections as a possible factor of virtualization. Art museums with larger collections are expected to exhibit virtual content more than those with smaller collections. For this reason, we propose the following hypothesis: H3. Art museums with larger collections are more likely to exhibit virtual content. 2.5.4. Number of exhibitions The number of exhibitions is one of the factors having the most significant impact on how many people visit the museum annually (Nuykina et al., Citation 2016 ). Furthermore, the number of scheduled exhibitions is used to evaluate its productivity and technical changes (Barrio & Herrero, Citation 2022 ; S. Kim & Soh, Citation 2017 ). Because it is a key digital resource displayed on an art museum’s website, the museum with more exhibitions may have a stronger need for virtualization. Therefore, this study suggests the number of yearly exhibitions as a factor determining the presence of virtual content. For this reason, we propose the following hypothesis: H4. Art museums with more curated exhibitions per year are more likely to exhibit virtual content. 2.5.5. Number of visitors The total number of visitors is another important indicator of development. Academic studies on the museum and cultural sectors widely use visitor counts as an evaluation index (Barrio et al., Citation 2009 ; Barrio & Herrero, Citation 2014 , Citation 2022 ; Basso & Funari, Citation 2004 ; Joo & Kim, Citation 2012 ; S. Kim & Chung, Citation 2017 ; J. Kim et al., Citation 2019 ; S. Kim & Soh, Citation 2017 ). Nuykina et al. use the tally of museum visitors as an indicator of museum development (Nuykina et al., Citation 2016 ). Another study also uses this same factor to evaluate museum brand importance in an online community (Fronzetti Colladon et al., Citation 2020 ). Therefore, the number of visitors may trigger increased online accessibility to incorporate diverse viewers more thoroughly. Indeed, the study regards it as a possible factor affecting virtualization. For this reason, we propose the following hypothesis: H5. Art museums with a higher number of visitors are more likely to exhibit virtual content. 2.5.6. Exhibition space Exhibition space is yet another influential factor that significantly impacts the number of visitors (Nuykina et al., Citation 2016 ). Barrio and Herrero use the museum’s surface area as a proxy for the value of the capital endowment, emphasizing its importance as a permanent trait (Barrio & Herrero, Citation 2022 ). In fact, multiple studies utilize surface area as a direct measure of the building’s magnitude and an indirect measure of the collection scope (Barrio & Herrero, Citation 2014 , Citation 2022 ; Basso & Funari, Citation 2004 ). Although a museum with limited space may have a stronger need for digitalization, the literature largely views the surface area as an indirect index for museum efficiency. Therefore, those with larger exhibition space are more likely to seek out online channels for displaying their artwork. The study selects the dimension of exhibition space as one of the factors of virtualization. For this reason, we propose the following hypothesis H6. Art museums with larger exhibition spaces are more likely to exhibit virtual content. 2.5.7. Digital resource Digital resource, in this research, signifies the exhibition and collection information. These two have been selected because they are the main digital objects in the museum sector, especially considering that the art museum’s primary objective is collecting work and organizing exhibitions. As the Museum and Art Gallery Support Act emphasizes, a museum is “a facility established for the collection, management, preservation, surveying, research, exhibition of, and education of material” (Museum and Art Gallery Support Act, Citation 2018 ). The collection is a significant aspect of museum management, given that its primary aim is to form, document, and manage its collection (B. Yoon & Lee, Citation 2020 ). Plus, the exhibition is a vital asset for performing its public goal. Because these are traditional forms of museum digitization, as Liao suggests, they may precede virtual content. We thus propose the following hypotheses: H7a. Art museums with updated exhibition information on their websites are more likely to exhibit virtual content. H7b. Art museums with updated collection information on their websites are more likely to exhibit virtual content. To test these hypotheses, seven museum attributes have been selected to examine their relationship to the digitalization of South Korean art museums: museum type, location, collection size, number of exhibitions, number of visitors, size of exhibition space, and digital resource. 3. Methodology 3.1. Definition collection Although virtual content within cultural heritage has not yet been firmly proposed, one can delineate the boundaries of virtual museums from the academic literature. To examine the core defining features and linguistic clarity of virtual museums, this study considered the definition of virtual museum in the recent literature. The current definition of virtual museum was extracted from articles and review papers in three international databases: SCOPUS, Web of Science (WoS), and Proquest. The document search timeframe was limited from 2020 to 2021 because the effect of Covid-19 was attributed to the year 2020. The selected documents were written in English to avoid incorrect interpretations, and the collection criteria were based on the primary term museum . Considering the unfixed definition of virtual museum , a total of three prefixes were utilized: digital, online, and virtual. Conference papers, book chapters, and notes were excluded to ensure the collected data’s quality and reliability. This resulted in a total of 119 articles after duplicates were removed. Among the 119 collected articles, 18 defined virtual museum . The collected definitions of virtual museum are detailed in Table 1 . Table 1. List of collected definitions. Download CSV Display Table 3.2. Museum data collection Along with these academic definitions, this study attempts to examine current virtual museums in South Korea. South Korea is an attractive setting for evaluating art museums’ digitalization level and virtualization. In South Korea, 93.7% of the people have access to IT devices; 60.3% show certain capability and 74.8% show digital outcomes (J. Kim et al., Citation 2020 ). Comparably high accessibility and capability empower the public to access online exhibitions provided by an art museum. Thus, Korean visitors can access online exhibitions, leading to a more accurate assessment of its attributes. This study specifically focuses on authorized art galleries (art museums) in South Korea. As of 2020, there are 270 art museums in South Korea and, among them, 256 are officially registered. Four are excluded because they were closed during 2020. Theme-park-like art museums are intentionally omitted because no special exhibition was observed. An additional 18 are excluded because the number of collections has not been provided, either by the Ministry or other external sources. Table 2 details the inclusion and exclusion criteria for the final art museum sample and, as a result, a total of 230 museums were selected. Table 2. Inclusion and exclusion criteria for selecting art museums. Download CSV Display Table 3.3. Variables 3.3.1. Museum type The Museum and Art Gallery Support Act of South Korea stipulates that officially registered art museums are to be categorized into four types: national, public, university, and private (Museum and Art Gallery Support Act, Citation 2018 ). This study coded the museum types into two: private (0) and public (1). The national museum is a government-affiliated institution, which is funded by the state; therefore, it is categorized as public. The public art museum, which is operated by the municipal or provincial government and funded by the local government, is also classified as public. Private art museums are classified as private, but university galleries are classified either as public or private depending on which type of entity (i.e., public or private) manages the university. Each type of art museum is clarified in the National Cultural Infrastructure Overview (S. Cho, Citation 2021 ). 3.3.2. Location Location refers to the museum’s geographical area and is divided into two divisions: the non-capital region (0) and the capital region (1). The capital region indicates the Seoul Capital Area (SCA), which includes Seoul, Incheon, and Gyeonggi Province. The non-capital region includes the rest of the provinces. The registered art museums’ addresses were collected from the National Cultural Infrastructure Overview (S. Cho, Citation 2021 ). 3.3.3. Collection size Collection size is defined as the number of collections museums self-reported in the National Cultural Infrastructure Overview (S. Cho, Citation 2021 ). Those without this information have either been excluded from the sample or supplemented by the information provided by their websites. 3.3.4. Number of exhibitions The number of exhibitions is the number of curated exhibitions in 2020, and it is provided by the National Cultural Infrastructure Overview (S. Cho, Citation 2021 ). If the number of annual exhibitions is not stated in the report, it is manually tallied from the museum’s website. 3.3.5. Number of visitors The number of visitors is defined as the number of yearly visitors; this information is offered as an open resource by the Ministry (S. Cho, Citation 2021 ). 3.3.6. Exhibition space The exhibition space is the physical dimension of an exhibition space in an art museum. These details are accessed in the National Cultural Infrastructure Overview (S. Cho, Citation 2021 ). Those without such data, the information was collected manually from their websites. 3.3.7. Digital resource In this study, digital resource is limited to the exhibition and collection information. Such data were collected manually via the websites of registered art museums. In cases of a webpage and content being dedicated to collection and exhibition information, the assigned code is 1. For instance, the collection information is coded as 1 when the list of collections and its information—such as images and titles—are accessible to the public online. In most cases, these data are listed in a photo album format and have a dedicated page for the collection. The exhibition information is coded as 1 if a webpage mentioning the curated exhibition is available on the website. However, if there is no updated exhibition information on the webpage, the assigned code is 0. 3.3.8. Virtualization In this study, virtualization is coded based on the presence of virtual content on a museum’s website. Along with digital resources (i.e., exhibition and collection information), the virtual content is manually collected by a single researcher. Although the type and level of immersion vary, by considering the preliminary stage of virtualization in the art sector, this study covers a wide range of virtual content. Any content described as “virtual” and that has utilized virtual technology that enables interactive three-dimensional displays—such as photogrammetry, 3 D scanning, and 360-degree cameras—is coded 1. (An example is shown in Figure 1 .) However, this study excludes the virtual museum that uses the traditional form of an online museum. That is, if a website is described as a virtual museum, yet it shows no difference from a conventional list of museum collections, then it is coded 0. This study does not differentiate between 3 D scans of existing exhibitions and a full virtual museum. Figure 1. Example of a virtual exhibition in South Korea ( source : [Prayer for Life: Special Exhibition of Korean Polychrome Painting] Copyright 2022 by National Museum of Modern and Contemporary Art, Gwacheon, South Korea. https://prayer-forlife.kr/ ). Display full size The list of art museums provided by the Overview offers detailed managerial information about each institution. Table 3 offers descriptive statistics on the selected 230 art museums in South Korea as well as on the collected operational measures: museum type, location, collection size, number of exhibitions, number of visitors, and size of exhibition space. The presence of digital resources (i.e., collection and exhibition information) and virtual content were collected by a single researcher for data validity. Table 3. Operational measures with the number of art museums. Download CSV Display Table 3.4. Data analysis For RQ 1, a systematic review of the literature has been conducted. Equipped with the collected definition, the coder identified primitives based on their frequency and then suggested academic definitions based on the identified core attributes. For RQ 2, the descriptive statistical analysis was performed to examine the overall digitalization level of South Korea’s registered art museums. A binary logistic regression model has been adopted to test the research hypotheses. This statistical model is useful for a binary dependent variable, which, in this case, is the presence of virtual content on a museum’s website. As such, in this study, the dependent variable is the presence of virtual content offered online by a specific art museum. The independent variables are museum type, location, collection size, number of exhibitions, number of visitors, exhibition space, and digital resources. 4. Results 4.1. Definition The collected definitions hint at the common characteristics of virtual museums. First is the use of technological tools. This includes the application of computer technology, information technology, and VR. Second is the collection of digital objects, such as tangible and intangible resources, digital electronic artifacts, information resources, digital content, digitized objects, electronic media, and multimedia objects. Additionally, an explanation of the virtual space (Internet, cyberspace, webpage), its target (audience, visitors), its effect (learning, communication, access, openness, engagement, transfer), and its relationship with traditional/physical museums are all embedded within the definitions. The primitives and core attributes of virtual museums are detailed in Table 4 . Table 4. The common characteristics of virtual museums. Download CSV Display Table By analyzing recent definitions of virtual museums within the academic literature, this paper attempts to comprehend the current discussion on the virtual museum. Based on the recent 18 definitions of a virtual museum and the six frequently mentioned primitives, this study adopts the following definition of the virtual museum: A museum that is built within a digital space with a collection of digitized objects, such as images, audio files, text-based documents, and uses virtual reality technology to supplement, augment, and enrich the museum experience. 4.2. Virtualization stage Given the increasing demand for virtual exhibition and digital art, the importance of virtual museums continues to grow. Despite this burgeoning expectation, however, this study suggests that most art museums in South Korea are not yet ready to transform their online platforms into virtual museums. As Liao and his colleagues argue, South Korea’s art museums do not show signs of higher socio-technical layers. Below, Table 5 presents the simple descriptive statistics for the complete sample of 230 art museums. Table 5. Simple descriptive statistics. Download CSV Display Table On average, the extent of digitalization levels is low, with a value of 1.87, indicating that most South Korean art museums remain between Level 1 and Level 2. In fact, 17% do not have a website at all (Level 0), eliminating any chance for visitors to access the museum digitally. Although most art museums (83%) do have, only 65% have relevant information. The percentage of those with virtual content is only 8.7%. This signifies that over 91% of art museums in South Korea remain at the level of the conventional website platform without experimenting with new media content. This possibly results in a narrow visitor experience by limiting the display methods mainly to text, images, and video. This indicates that, despite the growing demand, art museums in South Korea are taking too little action to provide a rich experience to potential customers via their websites. Rather, they seem to regard their websites as simply an announcement board for upcoming exhibitions. 4.3. Factors affecting the presence of virtual content For logistic regression, either Cox and Snell R <sup>2</sup> or Nagelkerke R <sup>2</sup> could be used to estimate the variable’s contribution to the variability of the dependent variable (Mehrolia et al., Citation 2021 ). This study used Nagelkerke R <sup>2</sup> ( R <sup>2</sup> = 0.439). The test results show that the variables explain 43.9% of the variance in museum attributes regarding the presence of virtual content, suggesting a strong relationship between the predictors and the dependent variable. The Hosmer-Lemeshow’s goodness of fit was equal to 0.864, indicating that the model is acceptable. As for the logistic regression results, there is a statically significant relationship between the presence of virtual content and digital resource_collection, exhibition space, and collection size. Other variables, namely location, type, digital resource_exhibition, and the number of visitors, do not exhibit significant relation. Digital resource_collection, exhibition space, and collection size significantly determine whether there is virtual content, demonstrating these factors’ importance in progressing the digitalization stage. As a result, hypotheses 3, 6, and 7 b are supported, showing that the museum attributes indeed influence the virtualization level of art museums in South Korea. The model’s full description is offered in Table 6 . Table 6. The logistic regression results. Download CSV Display Table 5. Discussion For RQ 1, this research analyzes virtual museums’ key attributes and offers a comprehensive definition of virtual museum from the academic perspective. For RQ 2, this paper identifies the virtualization stage of South Korea’s museums by conducting a quantitative study. Instead of focusing on the content level (exhibition-level), the focus is on the platform level (museum-level) to understand its ecosystem better. The above research proves there has been a slow adoption of digital museums in South Korea in general. Not many art museums digitize and display their exhibitions and collections, and few offers virtual content on their online platform, showing a gradual virtualization process in a cultural organization. For the research hypotheses, binary logistic regression has demonstrated the significance of the size of exhibition space, collection, and digital resource (collection information) in determining whether there is virtual content. Unlike other variables, such as type, location, number of exhibitions, visitors, and digital resource (exhibition information), the three significant variables are connected to museum collections. Collection size indicates the number of collections, a digital resource of collected information is the digitized form of a museum collection, and the exhibition space is connected to a physical display of the collection. The outcomes show that the museums with more collections and larger exhibition spaces, and that information related to display collections on websites, are likely to initiate the virtualization process. This emphasizes the strong role of collections in triggering virtualization. Furthermore, these results imply the relative importance of a collection over an exhibition in terms of virtualization. While the number of exhibitions and the presence of exhibition information on a museum’s website do not influence virtualization, those related to collection do have statistical significance. This difference may be traced to the required effort of producing collection information. Compared to exhibition information, for which the yearly number is mostly fewer than 10 exhibitions, the number of collections is, in most cases, over 50, thus requiring additional effort in producing visual resources. Therefore, the presence of collection information on the website can be seen as the museum’s degree of action toward digitalization. Despite the observed public–private and regional disparity in the art museum sector in the academic literature, in terms of virtualization, no significance has been detected. Considering that this disparity is connected mostly to art museums’ funding, such outcomes may further imply that funding might not be a critical trigger in enhancing virtualization. 6. Implication and limitations First to be addressed is the academic implications of this study. The major contribution made here is offering an academic definition of a virtual museum. The term has been used interchangeably with conventional online interaction, and this study provides the groundwork for future studies on this subject. This study also applies digitization and digitalization to the museum sector, thereby expanding the scope of virtual museum literature. In addition, by conducting an extensive case study on 230 registered art museums in South Korea, this paper contributes to the previous literature, which, until now, had been limited to case studies. Several studies examine how information technologies are expected to alter the interpretation and construction of artistic projects (Bovcon, Citation 2021 ). However, interestingly, Saaze et al. ( Citation 2018 ) find that introducing digital art to the MoMA did not lead to disruptive or radical changes in existing institutional practices (Saaze et al., Citation 2018 ). This implies that, despite high expectations regarding the virtualization of art museums, any actual adoption is slow. The research on the digitalization level of South Korean art museums also corresponds to earlier findings. Another contribution is the focus on the museum level instead of on the exhibition and user level. With regard to subject matter, a significant number of research conducted on virtual/online/digital museums is based on the user orientation. Plus, the previous studies on categorizing virtual museums are limited to the exhibition level. Given that exhibitions are comparably short-term and vary according to the manager, a museum review provides a better overview of the ecosystem with more permanent features. This study also offers a timely subject for the South Korean context. Recently, diverse subjects—such as virtual agents, virtual tours, digital museum objects, digital displays, digital storytelling, digital museum business model, their educational effects, lighting in a virtual environment, and cryptocurrency in the digital museum—are attracting academic attention. However, as this study has shown, the developmental level of South Korea’s virtual museums remains low. Such preliminary research on its actual adoption, such as this study presents, can provide a direction for future research by objectively identifying and prioritizing what is needed for the current virtualization stage. This study also offers practical contributions. First, it examines the virtual museum after the Covid-19 outbreak, which began at the end of 2019. It therefore offers a timely overview for museum managers and curators—who need digitalization—by objectively locating one’s museum’s virtualization stage. For instance, this paper identifies the urgent need for advanced digitalization for those without any websites at all. However, this study also encourages technological advances to be made among those remain in levels 2 and 3. Additionally, for practitioners, this study highlights which sector is lacking at the proposed level, thereby emphasizing the need for collection information to be updated in preparation for virtualization. In 2021, the Ministry of Culture, Sports, and Tourism began the Smart Museum and Art Museum Foundation Construction Project to offer public funding for its digitalization. In fact, about 100 million won per museum has been provided for 65 public museums, and for developing virtual content, about 500 million won per museum has been provided for 20 public museums (J. Park, Citation 2020 ). This study, which shows the lack of virtual content and platforms in today’s South Korean art museums, calls for specified funding for digitalization in those museums for which virtualization can easily take place. In summation: despite the low applicability relevance, research on the virtual museum is crucial to cope better with current difficulties and to prepare for the next paradigm. Hopper-Greenhill pose important questions on museology from the modernist viewpoint: “How are objects and collections used by museums to construct knowledge?” and “How can the relationships of museum audiences to this knowledge be understood?” (Buffington, Citation 2005 , p. 275). What is more, Abe and Fukushima examine display strategies for offline exhibitions, focusing on the presentation of artwork captions (Abe & Fukushima, Citation 2020 ). Many studies on visitors’ motivations to visit offline exhibitions have been conducted (Cotter et al., Citation 2022 ; Stylianou-Lambert, Citation 2017 ). For instance, Cotter and her colleagues explore the differing motivations for visiting art museums by investigating pre-visit motivation and post-visit outcomes (Cotter et al., Citation 2022 ). Various questions about conventional exhibitions have been resolved over time and with effort. Nonetheless, neither enough questions nor answers regarding virtual museums have been posed or provided, making this transition more obscure. Therefore, more research on virtual museums, including their actual adoption as well as experiment-based user satisfaction studies, should be conducted to resolve the gaps between academia and the real-world context. This study is not without limitations. Among them, this paper is restricting its data to secondary data. For instance, the museum budget may have a significant impact on VR adoption. However, because such information, especially for private museums, is not publicly available, this study chose to examine possible factors that may influence its adoption. Logistic regression should be conducted to examine, in a more in-depth manner, visitor-centered and managerial data. Direct conversations with the museums themselves would be more valuable for yielding actionable results. Secondary, this research limits the virtualization level to 0 and 1, depending on the presence of virtual content. Considering the wide variation in the type of virtual technology and the immersion level, it may be differentiated further to yield more elaborate results. For instance, 360 simulated virtual spaces could be distinguished based on the method of constructing the space and the degree of user interaction (Taesoo et al., Citation 2020 ). Additionally, although this study examined all registered art museums in South Korea; thus, art galleries and auctions were omitted. Because major art auctions and galleries are taking initiatives in creating virtual museums, future studies may include them in their samples to glean insight into the overall ecosystem. Lastly, this study is limited to Korean art museums and, as such, may not be generalizable to other contexts. A future study might therefore cover a wider range to assess the global art industry more thoroughly. As for future research, managers’ perspectives on the virtual museum are also suggested. Although this study shows that the size of the exhibition space, collection, and collection information may affect the presence of virtual content, there may be managerial difficulties in offering a virtual museum. Although several studies examine VR usability, most are limited to the user’s viewpoint (De Cock et al., Citation 2022 ; Fang & Lin, Citation 2019 ; Rodrigues et al., Citation 2022 ). A qualitative usability study on curators and museum managers in constructing a virtual museum may yield additional insight into the adoption of virtual museums. Disclosure statement No potential conflict of interest was reported by the author(s).", "Keywords": "", "DOI": "10.1080/10447318.2022.2143769", "PubYear": 2024, "Volume": "40", "Issue": "6", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "Seungyeon Ha", "Affiliation": "School of Media and Communication, Korea University, Seoul, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Media and Communication, Korea University, Seoul, Korea"}], "References": [{"Title": "Development of e-service virtual museum tours in Poland during the SARS-CoV-2 pandemic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "2375", "JournalTitle": "Procedia Computer Science"}, {"Title": "How will the information change the sense of values during art appreciation?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "3083", "JournalTitle": "Procedia Computer Science"}, {"Title": "Storytelling Platform for Interactive Digital Content in Virtual Museum", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "1", "Page": "34", "JournalTitle": "ECTI Transactions on Computer and Information Technology (ECTI-CIT)"}, {"Title": "Immersive Spring Morning in the Han Palace: Learning Traditional Chinese Art Via Virtual Reality and Multi-Touch Tabletop", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "213", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Falling in Love with Virtual Reality Art: A New Perspective on 3D Immersive Virtual Reality for Future Sustaining Art Consumption", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "4", "Page": "371", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "An Inheritance Mode of Rural Cultural Heritage Based on Virtual Museum in China", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Computer Games Technology"}]}, {"ArticleId": 104716843, "Title": "Breast cancer classification and segmentation framework using multiscale CNN and U‐shaped dual decoded attention network", "Abstract": "", "Keywords": "", "DOI": "10.1111/exsy.13192", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science COMSATS University Islamabad, Wah Campus  Islamabad Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science COMSATS University Islamabad, Wah Campus  Islamabad Pakistan"}, {"AuthorId": 3, "Name": "Shu<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Actuarial Science University of Leicester  Leicester UK"}], "References": [{"Title": "A framework for offline signature verification system: Best features selection approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "50", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Facial expressions classification and false label reduction using LDA and threefold SVM", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "166", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A distinctive approach in brain tumor detection and classification using MRI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "118", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Breast tumor segmentation and shape classification in mammograms using generative adversarial and convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Santiago Romani", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112855", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dataset of breast ultrasound images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "", "Page": "104863", "JournalTitle": "Data in Brief"}, {"Title": "Multiscale fused network with additive channel–spatial attention for image segmentation", "Authors": "Chengling Gao; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106754", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A review on image-based approaches for breast cancer detection, segmentation, and classification", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115204", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Retracted: An integrated framework for COVID ‐19 classification based on classical and quantum transfer learning from a chest radiograph", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "20", "Page": "e6434", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "MDA-Net: Multiscale dual attention-based network for breast lesion segmentation using ultrasound images", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "7283", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A quantization assisted U-Net study with ICA and deep features fusion for breast cancer identification using ultrasonic data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e805", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 104716897, "Title": "Monitoring Open Science policy using a regional CRIS – the Flanders case with FRIS", "Abstract": "In Flanders the government adopted an ambitious policy initiative to promote Open Science and it appointed FRIS, the regional CRIS, as the main tool to monitor the progress. Using a CRIS as a monitoring tool for a regional policy initiative has some advantages like good transparency, consistency, comparability of the results and less administrative burden for the institutions once the process for gathering and sending the information is in place. The preparatory process to define concepts, create definitions, discuss about the scope and the goals, was quite laborious and time-consuming, but proved very useful and necessary to get a good common understanding of the context, yielding better metrics and better alignment of the stakeholders towards the same goals. The current metrics framework is not an endpoint, but only the beginning. It is a start to improve the way we will monitor Open Science as we gather more insights on the way. The metrics help to get all stakeholders pulling in the same direction towards more Open Science.", "Keywords": "FRIS ; regional CRIS ; policy monitoring ; KPI's ; metrics ; Open Science", "DOI": "10.1016/j.procs.2022.10.181", "PubYear": 2022, "Volume": "211", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Economy, Science and Innovation Department, Flemish government, Koning Albert II-laan 35, 1030 Brussels, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Economy, Science and Innovation Department, Flemish government, Koning Albert II-laan 35, 1030 Brussels, Belgium"}], "References": []}, {"ArticleId": 104716950, "Title": "A new situation assessment method for aerial targets based on linguistic fuzzy sets and trapezium clouds", "Abstract": "Situation assessment has attracted much attention. The chief aim of this paper is to research the problem from the perspective of multicriteria group decision-making (MCGDM). First of all, this paper expresses the threat information with linguistic fuzzy sets, including <PERSON><PERSON><PERSON><PERSON>’s intuitionistic fuzzy sets (AIFSs), <PERSON><PERSON><PERSON><PERSON>’s interval-valued intuitionistic fuzzy sets (AIVIFSs), and interval intuitionistic uncertain linguistic sets (IIULSs). Then, some relevant concepts and aggregation operators of trapezium clouds are proposed, meanwhile, a new approach that calculates the weights considering subjectivity and objectivity is presented with multi-objective programming. Next, the conversion methods between AIFSs, AIVIFSs, IIULSs and trapezium clouds are derived. Finally, the algorithm process is presented and an illustrative example is employed to prove the validity. Also, the comparison analysis with other widely used methods is conducted to verify the feasibility of the proposed algorithm.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105610", "PubYear": 2023, "Volume": "117", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Province Key Laboratory of Flight Control and Simulation Technology, Xi’an 710072, China;School of Automation, Northwestern Polytechnical University, Xi’an 710072, China;Corresponding author at: School of Automation, Northwestern Polytechnical University, Xi’an 710072, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Province Key Laboratory of Flight Control and Simulation Technology, Xi’an 710072, China;School of Automation, Northwestern Polytechnical University, Xi’an 710072, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Province Key Laboratory of Flight Control and Simulation Technology, Xi’an 710072, China;School of Automation, Northwestern Polytechnical University, Xi’an 710072, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shaanxi Province Key Laboratory of Flight Control and Simulation Technology, Xi’an 710072, China;School of Automation, Northwestern Polytechnical University, Xi’an 710072, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shaanxi Province Key Laboratory of Flight Control and Simulation Technology, Xi’an 710072, China;School of Automation, Northwestern Polytechnical University, Xi’an 710072, China"}], "References": [{"Title": "A novel solution approach for multiobjective linguistic optimization problems based on the 2-tuple fuzzy linguistic representation model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106395", "JournalTitle": "Applied Soft Computing"}, {"Title": "Distributed linguistic representations in decision making: Taxonomy, key elements and applications, and challenges in data science and explainable artificial intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "", "Page": "165", "JournalTitle": "Information Fusion"}, {"Title": "Development of artificial neural network for condition assessment of bridges based on hybrid decision making method – Feasibility study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114271", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A multicriteria group decision-making method based on AIVIFSs, Z-numbers, and trapezium clouds", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "566", "Issue": "", "Page": "38", "JournalTitle": "Information Sciences"}, {"Title": "Polar coordinate system to solve an uncertain linguistic Z-number and its application in multicriteria group decision-making", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "104437", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A novel method to research linguistic uncertain Z-numbers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "586", "Issue": "", "Page": "41", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 104716976, "Title": "Prior area searching for energy-based sound source localization", "Abstract": "<p>Traditional energy-based sound source localization methods have the problems of the large solution space and time-consuming calculation. Accordingly, this paper proposes to use the data collected by each acoustic sensor and their corresponding weights to adaptively initialize the prior area of a target. In this way, the potential existence range of the target is reduced and the location estimate can be determined in a small area. Specifically, we first determine the initial search point based on the current sound data and the set rules. Then, the prior location of the target is iteratively searched according to different sound energy circles’ weights. Next, the prior area of the target is determined around the prior location. Finally, the precise location of the target is further traversed to minimize the objective function, which is constructed by the weighted nonlinear least squares location (WNLS) algorithm. A series of indoor experiments are performed. The results show that our method can effectively improve the positioning accuracy by approximately 13% and greatly reduce the calculation time.</p>", "Keywords": "prior area searching; sound source localization; adaptively initialization; iterative search; indoor environment", "DOI": "10.1007/s11432-022-3568-2", "PubYear": 2022, "Volume": "65", "Issue": "12", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Decision of Complex Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Decision of Complex Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Decision of Complex Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Decision of Complex Systems, Beijing Institute of Technology, Beijing, China; Beijing Institute of Technology Chongqing Innovation Center, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Decision of Complex Systems, Beijing Institute of Technology, Beijing, China; Guohao Academy, Tongji University, Shanghai, China"}], "References": []}, {"ArticleId": *********, "Title": "Digital transformation of maritime logistics: Exploring trends in the liner shipping segment", "Abstract": "Rapidly evolving needs of shippers, rising competition, advancement in digital technologies and a quest to increase cost and operational efficiencies are all driving the digital transformation of maritime logistics. However, in contrast to other industries such as media, telecom, banking, retail and even other traffic modes, the often family-controlled and network-centric liner shipping industry has historically been conservative in adopting innovations; hence, it is still far behind in embracing digitalization. Based on semi-structured interviews with senior executives of liner shipping companies, this study explores the current digital maturity levels, the opportunities provided by digitalization and the underlying challenges that hinder its implementation in the liner shipping segment within the larger maritime logistics industry and identifies the essential leading strategies of digitalization in this segment. The digital maturity categories applied to liner shipping provide an opportunity for practitioners in this industry to evaluate their business functions’ digital maturity levels. Furthermore, based on interview data, digital transformation for the maritime logistics industry is defined, as well as 9 major barriers and 19 different pathways to digital transformation are identified. Understanding the key challenges and success factors in the industry is a key to approaching digitalization problems and developing a healthy digital transformation process.", "Keywords": "Maritime logistics ; Digital transformation ; Digitalization ; Digital maturity ; Challenges ; Success factors", "DOI": "10.1016/j.compind.2022.103811", "PubYear": 2023, "Volume": "145", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business, Economics and Law at University of Gothenburg, Sweden;Research Institute of Sweden, Gothenburg, Sweden;Corresponding author at: School of Business, Economics and Law at University of Gothenburg, Sweden"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Business, Economics and Law at University of Gothenburg, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Chalmers University of Technology, Gothenburg, Sweden"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chalmers University of Technology, Gothenburg, Sweden;Research Institute of Sweden, Gothenburg, Sweden"}], "References": [{"Title": "C-Ports: A proposal for a comprehensive standardization and implementation plan of digital services offered by the “Port of the Future”", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "134", "Issue": "", "Page": "103556", "JournalTitle": "Computers in Industry"}, {"Title": "Digital transformation of the maritime industry: A cybersecurity systemic approach", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "", "Page": "100526", "JournalTitle": "International Journal of Critical Infrastructure Protection"}]}, {"ArticleId": 104717036, "Title": "Development and assessment of a contactless 3D joystick approach to industrial manipulator gesture control", "Abstract": "This paper explores a novel design of ergonomic gesture control with visual feedback for the UR3 collaborative robot that aims to allow users with little to no familiarity with robots to complete basic tasks and programming. The principle behind the design mirrors that of a 3D joystick but utilises the Leapmotion device to track the user&#x27;s hands and prevents any need for a physical joystick or buttons. The Rapid Upper Limb Assessment (RULA) ergonomic tool was used to inform the design and ensure the system was safe for long-term use. The developed system was assessed using the RULA tool for an ergonomic score and through an experiment requiring 19 voluntary participants to complete a basic task with both the gesture system and the UR3&#x27;s RTP (Robot Teach Pendant), then filling out SUS (System Usability Scale) questionnaires to compare the usability of both systems. The task involved controlling the robot to pick up a pipe and then insert it into a series of slots of decreasing diameter, allowing for both the speed and accuracy of each system to be compared. The experiment found that even those with no previous robot experience were able to complete the tasks after only a brief description of how the gesture system works. Despite beating the RTP&#x27;s ergonomic score, the system narrowly lost on average usability scores. However, as a contactless gesture system it has other advantages over the RTP and through this experiment many potential improvements were identified, paving the way for future work into assessing the significance of including the visual feedback and comparing this system against other gesture-based systems. <b  >Relevance to Industry</b> In industrial environments where the robots may need to be frequently reprogrammed through complex RTPs, companies must call on those with specialist training even to make minor adjustments. The presented system takes advantage of gesture control to allow for easy interaction with industrial robots, even for untrained operators.", "Keywords": "Robotics ; Human-robot interaction ; Gesture control ; Ergonomics ; Usability ; RTP Robot Teach Pendant ; RULA Rapid Upper Limb Assessment ; SDK Software Development Kit ; IMU Inertia Measurement Unit ; HMI Human-Machine Interface ; AR Augmented Reality ; SAR Spatial Augmented Reality ; RTDE Real Time Data Exchange ; SUS System Usability Scale", "DOI": "10.1016/j.ergon.2022.103376", "PubYear": 2023, "Volume": "93", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cranfield University, College Road, Cranfield, MK43 0AL, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cranfield University, College Road, Cranfield, MK43 0AL, UK"}], "References": []}, {"ArticleId": 104717118, "Title": "RRCNet: Refinement residual convolutional network for breast ultrasound images segmentation", "Abstract": "Breast ultrasound images segmentation is one of the key steps in clinical auxiliary diagnosis of breast cancer, which seriously threatens women’s health. Currently, deep learning methods have been successfully applied to breast tumors segmentation. However, blurred boundaries, heterostructure and other factors can cause serious missed detections and false detections in the segmentation results. In this paper, we developed a novel refinement residual convolutional network to segment breast tumors accurately from ultrasound images, which mainly composed of SegNet with deep supervision module, missed detection residual network and false detection residual network. In SegNet, we add six side-out deep supervision modules to guide the network to learn to predict precise segmentation masks scale-by-scale. In missed detection residual network, the receptive field provided by different dilation rates can provide more global information, which is easily lost in deep convolutional layer. The introduction of false detection and missed detection residual network can promotes the network to make more efforts on those hardly-predicted pixels to help us obtain more accurate segmentation results of the breast tumor. To evaluate the segmentation performance of the network, we compared with several state-of-the-art segmentation approaches using five quantitative metrics on two public breast datasets. Experimental results demonstrate that our method achieves the best segmentation results, which indicates that our method has better adaptability on breast tumors segmentation.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105601", "PubYear": 2023, "Volume": "117", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Intelligent Robotics, College of Artificial Intelligence, Nankai University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tianjin Key Laboratory of Intelligent Robotics, College of Artificial Intelligence, Nankai University, Tianjin, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory of Intelligent Robotics, College of Artificial Intelligence, Nankai University, Tianjin, China"}], "References": [{"Title": "A novel machine learning technique for computer-aided diagnosis", "Authors": "Cheng Tang; Junkai Ji; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "103627", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep and machine learning techniques for medical imaging-based breast cancer: A comprehensive review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114161", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Integrate domain knowledge in training multi-task cascade deep learning model for benign–malignant thyroid nodule classification on ultrasound images", "Authors": "<PERSON><PERSON> Yang; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "104064", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "SDFNet: Automatic segmentation of kidney ultrasound images using multi-scale low-level structural feature", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115619", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A study on the use of Edge TPUs for eye fundus image segmentation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "104384", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "RCA-IUnet: a residual cross-spatial attention-guided inception U-Net model for tumor segmentation in breast ultrasound imaging", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "2", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Instance segmentation of biological images using graph convolutional network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "104739", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 104717220, "Title": "Climbing Keyboard: A Tilt-Based Selection Keyboard Entry for Virtual Reality", "Abstract": "Text input is one of the common interaction tasks in virtual environments. However, current inputting methods (e.g., laser-input: aim-and-shoot technique) have many limitations, such as inefficiency, lack of precision, and fatigue of long-text inputting. We propose a Climbing Keyboard method to allow easier, faster, and more accurate text input, and use tilt instead of precise aiming. The selected target changes from a specific letter to a group of letters with such a tilt-based interaction based on the QWERTY layout, which aims to reduce the learning cost, especially for novice users. Meanwhile, expert users can focus on the screen without looking at the keyboard. We designed three user studies to evaluate the performance of proposed method, including the verification of the usability of the tilt interaction method in the first study, optimization of the tilt angle range in the second study, and evaluation of the learning curve of Climbing keyboard in the last study. Our results showed that participants can reach 16.48 words per minute after an hours of training. 1. Introduction Text input is one of essential tasks for many applications. For example, typing account name and password when a user logs into a system. The performance of text inputting can be reached 40 WPM MacKenzie ( Citation 2002 ) with computer or 33 WPM Azenkot and <PERSON><PERSON> ( Citation 2012 ) with smartphone. However, the performance of text inputting in VR is only about 13 WPM, and the user gets tired quickly. There are some common methods to support text input in VR (e.g., laser-pointing, gestures, speech-based). However, some challenges occurred when using these common methods to input for VR. On one hand, the well-known method of text inputting in VR is the aim-and-shoot technique, however, jitter issue affects users’ performance in this method D. Yu et al. ( Citation 2018 ). When users press the button on the controller to confirm selection, the direction and position of the controller will be changed, which is known as Heisenberg effect Bowman et al. ( Citation 2001 ). Meanwhile, users can feel mental fatigue and physical fatigue quickly, because they need to aim the letters accurately by laser means they need to pay more attention to their body control. On the other hand, with development of artificial intelligence, some researchers have proposed speech Pick et al. ( Citation 2016 ) and mid-air input Gupta et al. ( Citation 2019 ); Hoste et al. ( Citation 2012 ) to avoid the shortcomings of typing with a handle controller. However, speech is not suitable for noisy scenes, meanwhile, it makes it difficult to protect users’ privacy as well. The input method for Head-Mounted Displays (HMDs) Jimenez ( Citation 2017 ) brings high physical demand and motion sickness, which makes users dizzy. Mid-Air input González et al. ( Citation 2009 ) requires additional cameras or gloves to capture the movements on the hand, cumbersome wearable devices are not suitable for long-text input. Some of them are slow and enable low accuracy, high learning costs, and even a low immersion of VR. Therefore, it is important to investigate a text entry method with the features of efficient, precise, and easy to learn. In response to the mentioned challenges, this article proposes a novel technique, Climbing Keyboard, that enables users to enter texts efficiently and easily. It is a tilt-based selection keyboard using two controllers with a QWERTY layout. We divide the characters on the QWERTY keyboard into 6 parts, each controller mapping three parts ( Figure 1 ). Compared to aiming the letters on the keyboard accurately, tilting controllers at different approximate angles makes users feel less fatigue and more fluently. After users choose one part by tilting the controller, the color of characters of this part changed to red to facilitate participants to choose. Record the parts selected by the user in order, and generate candidate words according to this order. Users can input the intended word by touching the corresponding area of the touchpad. The design of Climbing Keyboard is underpinned by three solution principles for VR text entry (efficient, easy to learn, comfortable to use), which we have distilled from the literature and prior experience in text entry design for VR. In our opinion, using the QWERTY keyboard layout can reduce the user’s learning cost-effectively. But there is no clear evidence that the QWERTY layout is effective for text input on a circular and small screen. Therefore, we designed a circular layout which is often used in small screen interaction for comparison. Finally, the experimental results show that the QWERTY layout is better than the circular layout in terms of speed and accuracy. Figure 1. The characters on QWERTY keyboard be divided into 6 parts, each controller mapping three parts. Display full size To evaluate Climbing Keyboard, we designed four candidate layouts, and carry out experiments to select the proper layout. The result shows users can input the intended word accurately and fast. Even novice users can reach 11.21 WPM. We are concerned that different tilt angles may affect the user’s typing performance. After analyzing the tilt operation of users in the first experiment, we conducted the second experiment with different angles. We divide the 90° into three angles as “up,” “middle,” and “down.” The first angle range is equally divided by 90°. The second angle range is based on dual hands. Thirdly, the tilt range of each handle is based on the tilt habit of each hand. Compare the three angle ranges through experiments. In terms of speed and accuracy, Finally, we chose 0°–26°, 26°–56°, 56°–90° as the optimal tilt angle of the layout. We conducted a five-block user study to evaluate the performance of the Climbing Keyboard (the best performing design found in the second study). The results showed that users could achieve 16.48 WPM after a short training. All the studies were granted ethical approval by the Jilin University College of Computer Science and Technology. Our contributions in this work include: (1) Integration of text entry in Virtual Reality. (2) Climbing Keyboard design and implementation. (3) optimize the Climbing Keyboard based on the tilt habits of users. 2. Related work 2.1. Typing in virtual environments There are various kinds of researches on text entry in virtual reality Environments. The most popular and conventional way of text entry in VR is the aim-and-shoot style, in which the user holds the handle and cast a virtual laser to select the letters on the keyboard accurately and make the final confirmation using a trigger of the controller Lee and Kim ( Citation 2017 ); Olofsson ( Citation 2017 ). Jigger affects users’ performance in this method. Another kind of technique, called Google Drum Keys, taps the virtual keyboard like a drum, which was proposed by Google Daydream Labs, but this technique is easy to cause fatigue quickly because it needs users to make a large spatial movement to type one character. Lee and Kim ( Citation 2017 ) added some buttons on the handles to remind users of their muscle memory in the usual touch typing, However, some characters must be aimed by ray. When users input a word, they need to push the button as well as aim the letter on a virtual keyboard. Recently, D. Yu et al. ( Citation 2018 ) presented a PizzaText, a circular layout-based text entry technique for VR systems using the two thumbsticks of an Xbox controller. By rotating the two joysticks of the game controller, users could easily enter text by using this circular keyboard. Instead of using controllers, several studies focus on typing with a physical keyboard in VR Knierim et al. ( Citation 2018 ); Lin et al. ( Citation 2017 ); Schneider et al. ( Citation 2019 ); Walker et al. ( Citation 2017 ). However, HMDs, such as HTC Vive, PlayStation, and Oculus Rift, which are used to display virtual scenes, obscures the position of the keyboard Jimenez ( Citation 2017 ). Despite McGill et al. ( Citation 2015 ), using a dedicated device a keyboard, they marked it to ease tracking by the virtual reality system. Kim and Kim ( Citation 2017 ) used the screen of a smartphone as a typing interface, users could type on the phone. From our point of view, using an extra device for inputting text will interrupt users’ immersion. Some researchers focus on Mid-air text-entry without handheld devices. Tap Systems proposed a new type of VR typing. The user wore a finger tap that could track the movement of the hand for text entry. Aakar et al. proposed a word-gesture typing using a ring technique called RotoSwype Gupta et al. ( Citation 2019 ) by rotating users’ wrists to draw a line on a virtual keyboard. These methods were lack of haptic feedback and required a lot of training costs. Tying in the air could be tiring for the user and was not suitable for long periods of time and a large amount of text input. Speech techniques Adhikary and Vertanen ( Citation 2021 ); Hoste et al. ( Citation 2012 ); Pick et al. ( Citation 2016 ) were undoubtedly the fastest and the most convenient of all input methods. However, it was not practical when entering passwords or speaking in public. It might be difficult to correct mistakes. Speicher et al. ( Citation 2018 ) evaluated several methods of text-entry in VR, such as Head pointing C. Yu et al. ( Citation 2017 ), Eye gaze selection Majaranta and Räihä ( Citation 2007 ), Finger gestures González et al. ( Citation 2009 ), and Physical keyboard Lee and Chung ( Citation 2008 ). His conclusion showed that pointing using tracked hand-held controllers outperformed all other methods. 2.2. Tilt-based text-entry for handheld devices The possibility of using tilt handles to text entry is given by an accurate tracker in VR. There are many types of research about tilt-based text-entry for the handheld device. Tilt is always used to be explored for interaction for replacing some specific gestures Hinckley and Song ( Citation 2011 ; as shake to confirm) or solving the problem with one-handed interaction Chang et al. ( Citation 2015 ); Yeo, Phang et al. ( Citation 2017 ). Partridge et al. ( Citation 2002 ) proposed TiltType which was the first research for using tilt in text-entry. TiltTpye combined the buttons of the device, direction, and the angle of tilt to the intended character. GesText Jones et al. ( Citation 2010 ) used the accelerometer to select the characters. To solve the problem of inconvenience with a single hand, Yeo et al. proposed SWIM technique Yeo et al. ( Citation 2017 ) which retrieved the orientation of the device and mapped it to absolute tilt magnitude and screen coordinates (x,y) for controlling a pointer to infer the intended word. Wigdor and Balakrishnan ( Citation 2003 ) proposed a tilt-based selection keyboard. Users pressed the buttons of mobile phones to select one part, then tilted the device in different directions to complete twice selection and confirm the intended characters. Aakar et al. proposed the RotoSwype technique Gupta et al. ( Citation 2019 ), which was a word-gesture tying method using a ring in VR and AR. Users needed to rotate their wrist and draw a line that contained the intended characters according to the QWERTY keyboard layout. The user’s action was tracked by a ring that was worn on the index finger. Tilt gave us a second dimension to extend the method of text entry. However, most of the current tilt-based text input methods are designed as gesture input. Users need to memory the specific gesture and it increases the users’ memory load. Our method only requires the user to input words by simply tilting the handle. 2.3. Layouts with multi-letter keys The multi-letter key layout design is usually used to solve the problem of limited space. For example, devices with small screens such as smartwatches. The layouts to solve this problem are circular layout and T9 layout. Few types of research apply a circular layout for text entry and menu selection. Arranging letters or buttons on a ring by a specific order is called a circular layout. Zhao et al. ( Citation 2007 ) proposed earPod, a touch-based auditory menu technique, which used a circle layout. And it proved that when compared with the iPod-like visual linear menu, earPod performed better. TUP Proschowsky et al. ( Citation 2006 ) was early research about text entry using a circular layout. Inspired by shorthand gesture recognizers, Mankoff presented a new system using a circle keyboard for pen-input called Currin Mankoff and Abowd ( Citation 1998 ). However, that was difficult to learn for users. Uta proposed a BubbleType Hinrichs et al. ( Citation 2008 ) technique by typing on the tabletop which designed a BubbleCircle layout using a circle keyboard. Anke proposed the pEYEs Huckauf and Urbina ( Citation 2008 ) technique which made two selections to input the intended word. Yu et al. ( Citation 2018 ) used a circle layout for text entry in VR. Novice users achieved an average of 8.59 WPM, while expert users achieved 15.85 WPM. Yi et al. ( Citation 2017 ) proposed a compass technique that allowed users to enter text on a smartwatch using a circle layout keyboard. Wong et al. ( Citation 2018 ) achieved One-handed T9 layout input using a smartwatch, and its speed reached 5.42 WPM. As mentioned above, we find that the circle layout is more suitable for text input on screens with a small size or circle shape. The circular keyboard can arrange the distribution of letters reasonably but requires a lot of learning costs. QWERTY keyboard layout is the most common typing layout in a century. Beginners prefer to use the QWERTY layout compared to other layouts. Once users learn QWERTY layout typing, it is hard for them to forget. Some researchers want to make some changes to the original QWERTY layout, hoping to achieve better results. For example, MacKenzie ( Citation 1992 ) proposed the half-QWERTY keyboard which divided the keyboard into two parts. Every single part was operated by a single hand. Liebowitz and Margolis ( Citation 1990 ) studied and compared a variety of keyboard layouts with QWERTY layout and concluded that other layouts did not perform well. They studied the history of the QWERTY layout and concluded that it was difficult to change the habit of using the QWERTY layout. In this way, novice users could master this technique. For multi-letter key inputthere are many candidate characters should be chosen as a final result. Therefore, a reasonable word disambiguation algorithm can effectively improve input performance. Variants of word disambiguation algorithms have been proposed Çetiner et al. ( Citation 2021 ); Gogoi et al. ( Citation 2021 ); Kaddoura and Ahmed ( Citation 2022 ) and they reduced segment error, diacritic error and word error rates. 3. Climbing keyboard In order to improve input efficiency and user satisfaction, a climbing keyboard is proposed. A keyboard layout based on QWERTY enables text entry by tilting the controller of two HTC controllers. Because no other equipment is used, this kind of design allows text input not to destroy the user’s immersion. Typing by tilting controllers is like a climber climbing a mountain, so we call it CLIMBING KEYBOARD. In the following, we will show the details of this method. First, Keyboard layout is described and the reason why we choose this layout is given. Secondly, a word disambiguation method is given to handle the problem of multiple candidate words and tries to decrease the workload of user. This part is very important for the user satisfaction. Finally, although word disambiguation can automatically choose the suitable candidate, the correct rate is not 100%. So the final selection method is discussed and designed. 3.1. Keyboard layout In daily text input, most people use the QWERTY keyboard layout on the computer or mobile phone. Most people do not like to learn a new layout, so according to the principle of easy learning. We chose to use the QWERTY keyboard layout in this technology. As shown in Figure 1 , we arrange the 26 letters according to the QWERTY layout and divide the QWERTY layout into two parts, corresponding to the left hand and the right hand. Three rows of letters can be selected by each hand. For example, the first row corresponding to the left hand contains “Q W E R T,” the second row contains “A S D F G,” and the third row contains “Z X C V B”…. We divide the angle of inclination into three degrees corresponding to three rows of letters. Take the left hand as an example. Tilting the handle up means that the line “Q W E R T” is selected, and tilting the handle down means that the line “Z X C V B” is selected. Keeping the handle horizontal means the line “A S D F G” is selected. According to the letter sequence of the word, select and confirm the corresponding part. After the entry sequence of the intended word, five candidate words will appear under the QWERTY keyboard. Finally, we touch different parts of the touch-pad to select the intended word. 3.2. Selection Letter selection: When the user tilts the handle to a suitable position, the corresponding part of the climbing keyboard will turn red. When we press the touch pad with our thumb, the candidate letters will appear below the keyboard. When the thumb moves left or right on the touchpads, the different letters will turn red ( Figure 2(b) ). We only need to press the trigger key when the intended letter turns red, and the letter will be entered into the input box ( Figure 2(c) ). There will be no spaces after the input of letters because the input of non-dictionary words is taken into account. Especially, If space is needed, the user can press the left trigger to input a “Space.” Figure 2. The screen shows interaction after YUIOP has been entered and working through the steps (d–f). (a) tilts the handle to choose a part. (b) Candidate letters appear after pressing touch-pad. (c) Enter the intended letter after pressing the trigger. (d–f) is the process of inputting words. Display full size Word selection: When a word sequence is an input by tilting the handles, five candidate words will be listed under the keyboard. The five candidate words are ranked by frequency of use. Swipe left or right on the touchpad with the user’s thumb. Different words will turn red. ( Figure 2(d) ) When the word we want to enter turns red, press the trigger of the left-hand key, and the word will be entered in the input box ( Figure 2(f) ). Then our algorithm detects that a word has been entered, and space will be automatically added after the word. Our algorithm also provides an implementation of auto-complete by using data structure trie. Delete: If the sequence of a word is entered incorrectly, we can use the trigger on the right handle to delete the sequence just entered one by one. Or we can use the menu button of the left handle to delete the sequence just entered. When the input sequence is deleted, press the trigger button on the right handle again, and the words in the input box will be deleted one by one. Or we can press the Grip button on the left handle to clear the input box. Confirm: When the user finishes inputting, just press the grip button on the right handle to complete the text submission. In actual application scenarios, this button can switch between typing and VR interaction. 3.3. Word disambiguation Each character group contains multiple characters, for example, a user selects a row, he may want to enter a letter from A, S, D, F, G. Entry sequence is input by titling handles. There are several different potential input results based on entry sequence. In order to ensure the correctness of word input, it is necessary to use a word disambiguation. We use our algorithm to encode each word. For example, the word “entry” is composed of letters “e, n, t, r, y.” we only need to enter the group number of each letter in the word. like “1,6,1,1,2.” In order to implement this algorithm, we have to encode a large number of words. The implementation of our algorithm is mainly based on ANC (American National Corpus) Ide and Macleod ( Citation 2001 ) which contains over 22,000,000 words of written and spoken American English. We choose the most frequently used top 5000 words in ANC as our corpus. After we encode these words, we will get a hash table of these words. When we enter a sequence, we get candidate words that satisfy this sequence. The result shows that as the number of candidate words increases, the number of words that our algorithm cover is increasing, as shown in Figure 3 . We list five candidate words to cover 98.14% of the words. If the number of candidate words is up to six, the word coverage rate can only increase by 0.84%, but the user needs to choose among the six candidate words. The more candidate words, the more time user may spend. Figure 3. With the increase of candidate words, the algorithm can cover the proportion of ANC words. Display full size 4. The first study: Tilt or no-tilt In order to evaluate the effectiveness of the tilt interaction for typing, we designed two interaction methods, namely NTI (no-tilt inputting), TI (tilt inputting). Both methods used word selection with the same disambiguation engine. 4.1. Layouts implementation 4.1.1. Non-tilt inputting (NTI) We divide the QWERTY keyboard into 6 parts which contain 5 characters, and each part is corresponding to a slice of the touch-pad on the handle (see Figure 4 ). The interaction is as follows. Figure 4. Handle key distribution of NTI layout. Display full size According to the ordinate position on the touchpad that the user touches, the corresponding part is highlighted. Users select the intended part by pressing the touchpad down. The word disambiguation algorithm will calculate the candidate words that meet the conditions based on the input sequence, and including autocomplete of current word. According to the abscissa of the touchpad of the user touching the handle, the candidate word is highlighted. Press the trigger key to enter candidate words. 4.1.2. Tilt inputting (TI) The layout of handles is the same as the NTI layout. Compared with the NTI, the interaction method is different, using a tilt handle instead of fingers touching different positions of the touchpad. For the specific interaction process, see the Section 3.3 . 4.2. Hypotheses We formulate three hypotheses for the experiment: H.1. In terms of typing speed, the use of touch selection on the touch-pad is better than tilted interaction. The touch action on the touchpad is smaller than the tilt handle. H.2. In terms of accuracy, the use of tilted interaction is better than touch selection on the touch-pad. Setting three key positions on a touchpad with a diameter of 4cm is too close, and accidental touches are inevitable. 4.3. Participants A total of 12 undergraduates and postgraduates from a local university were recruited for the experiment (7 males; 5 females). All participants with a mean age of 24 (range: 19–26, SD = 2.53) had normal vision or corrective lenses and were right-handed. 4.4. Apparatus The experiment used the following equipment: An Intel Core i7 processor PC with a dedicated NVIDIA GTX 1080TI graphics card. The program was developed in C#. NET and run on the Unity3D platform. A VR head show (HTC Vive, HTC Inc.), which includes a VR HMD helmet, two laser positioners, and brackets, was used for building and displaying VR environments. 4.5. Experiments design and procedure The study used a within-subject design with one factors: interactions (touch and tilt). For the whole experiment, we gathered 2(interaction) × 10(phrase) × 12(participant) × 3(block)=720 timed trails. The experiment lasted approximately 1 hour for one participant. After each trial, subjects had a possibility to have a break if they felt tired. The experiment meets the Latin square design. Each participant was first introduced to word typing and need to be trained by completing some learning tasks before the formal experiment. Tasks include entering words and sentences using different layouts and interactions based on prompt. If they can complete the tasks of training without difficulty, they can conduct formal experiments. In the formal experiment, participants need to enter the phrase given above the input box. The phrases used in experiments were all from the phrases provided by MacKenzie and Soukoreff ( Citation 2003 ). Participants were told in advance to entry the phrases as soon as possible. They were asked to correct the error if they notice the error immediately but ignore the error if they type the next word. Participants can rest for 5 minutes after completing 10 phrases. During the experiment, the time accuracy and the number of backspaces used by participants will be recorded. After the experiment is over, participants need to complete a NASA-TLX questionnaire, and answer their feelings about different interactions and make some suggestions about improving the way of interaction. We will record all experimental data during the experiment for subsequent experimental analysis. 4.6. Results WPM (words per minute) is used to measure participant typing speed, formally: WPM = | T | − 1 S × 60 ÷ 5 where S is the time (in seconds) from the first to the last key press and |T| is the number of characters in the transcribed text. The error rate is reported based on total error rate (TER) and not corrected error rate (NCER) Levenshtein ( Citation 1966 ) Uncorrected error rate is used to measure participant typing accuracy, formally: NCER = MSD ( P , T ) max(P,T) × 100 % where P is the present phrase, T is the transcribed phrase. MSD is a method using Levenshtein’s algorithm Levenshtein ( Citation 1966 ). Max is a method to calculate the maximum number of characters for two strings. Total error rate is used to measure participant typing accuracy, formally: TER = B P + B × 100 % + MSD ( P , T ) max ( P , T ) × 100 % where P is the lengthy of present phrase; and B is the number of backspaces used. T is the transcribed phrase. 4.6.1. Text entry speed There were significant effects of Interaction ( F 1,22 = 7.022, p = 0.015) on WPM. We can see from Figure 5 that using the tilt method was significantly faster than using the selection method. The average input speed of users using Tilt inputting can reach 14.05 WPM. But users can only reach 11.77 WPM with No-tilt Inputting. Figure 5. Demonstrates the performance of different technologies at typing speed. Error bars indicate 95% confidence intervals. Display full size 4.6.2. Error rate There were no significant effects of Interaction ( F 1,22 = 0.171, p = 0.68) on not corrected error rate. Furthermore, no significant interaction effect on Interaction ( F 1,22 = 0.651, p = 0.426) was found. We can see from Figure 6 that the two interaction methods have little difference in error rate. Figure 6. Demonstrates the performance of different technologies at the error rate. Error bars indicate 95% confidence intervals. Display full size 4.6.3. Workload As shown in Figure 7 , participants rated both interactions after the experiment. The full score is 5, the higher the score, the worse the performance. It is particularly important to note that the Temporal demand here are not obtained through the recorded time and accuracy, but the subjective feelings of the experimental participants. It can be seen from the results that NTI is not as good as TI in terms of mental fatigue. According to the participants in the experiment, NTI used the touchpad to select blocks, and the selection of candidate words also used the touchpad to confuse them. While TI does not perform as well as NTI on physical fatigue, we will adjust the tilt angle below. Figure 7. Demonstrates the performance of different technologies at the error rate. Display full size 4.7. Discussion The results support H.1. In terms of typing speed, the use of tilted interaction is better than touch selection on the touch-pad. For the H.2. , the results of ANOVA do not show there are significant between the two groups. This result shows that our word disambiguation algorithm is stable. According to subjective evaluation, TI is better than NTI. Moreover, according to the description of the participants, the current setting of the tilt angle is the cause of the error. And the tilt range is relatively large, not only need to rotate the wrist, sometimes need to swing the arms. Therefore, changing the tilt angle may benefit typing performance. It requires a further study. 5. The second study: Tilt angle When using tilt for text input, the size of the angle range is particularly important. When the setting angle range is too large, the user needs to swing his arm to increase fatigue. When the set angle is too small, the error rate will increase. The purpose of this experiment is to find a reasonable range of angles. 5.1. Pilot study We conducted a small, follow-up experiment (with 12 participants from the first study) to find a comfortable tilt setting. We asked twelve experiment participants to tilt the handle up 10 times and tilt the handle down 10 times and keep the handle in a horizontal position 10 times with their left hands. Afterward, the participants did this task again with their right hands. Press the trigger button when the user thinks the tilt is at a suitable angle. The tilt angle of the handle will be recorded. Figure 8 records the tilt handle angle of both hands. Figure 9(a) records the tilt angle of the left hand, and Figure 9(b) records the tilt angle of the right hand. We divide the tilt range of the handle into three angles as “up,” “middle” and” “down.” We calculate the average value for each range based on the data collected above. From Table 1 , we can see that 60°–90° for up, 30°–60° for middle, and 0°–30° for down is not the best comfortable layout. And we can find that the tilt angle of the left hand is lower than the tilt angle of the right hand. And judging from the degree of dispersion of the data, the left hand has a greater range of activity, which has a clear dividing line compared with the right hand. Figure 8. The points recorded by the participant by tilting the both handles. Display full size Figure 9. (a) The points recorded by the participant by tilting the left handles. (b) The points recorded by the participant by tilting the right handles. Display full size Table 1. the average value for each range Download CSV Display Table 5.2. Layout optimized Just like the conclusions drawn from the preliminary experiment, the left and right hands may have different control over tilt. The tilt angle for the left and right hands may also be different. We wanted to find a general conclusion for this property to enhance its usability for most users. Thus, the goal of this study was to investigate the performance of different angle range of tilt. We have designed three angle ranges (Bisect 90°, both hands average angle, the left and right-hand angles were calculated separately). This experiment is dedicated to finding the best angle. The results were shown in Table 2 . Table 2. Angle range of three layouts Download CSV Display Table 5.3. Hypotheses We formulate two hypotheses for the experiment: H.3. We believe that the angle range obtained from the scatter plot obtained by the user tilting the wrist is better than dividing 90° into three levels equally. H.4. We believe that the comfort angle of the right hand is higher than the comfort angle of the left hand. Therefore, we think it is better to calculate the angle range of both hands separately than to calculate the angle range of both hands together. 5.4. Experiment design and procedure The experiment requires each participant to do three input tests with different angle ranges. And this time the technology added Word disambiguation. The required input phrases are from the phrase test set in the first experiment. Participants in the experiment were allowed to practice as long as they wanted. The test task needs to be repeated in 3 groups of 10 sentences each. The experiment meets the Latin square design. Each experimental participant will practice the system set up from different angles. Each set of practice time is up to them. The whole experiment lasted approximately 40 minutes for each participant. Between two layouts, participants could have a break. 5.5. Result 5.5.1. Text entry speed Figure 10 showed the text entry speed of different angle ranges. The speed of angle range 2 was higher than the others. We used one-way ANOVA analysis with Bonferroni post-hoc test, which allowed us to discover which specific means differed. The results revealed that there were significant effects of angle ranges ( F 2,33 = 4.642, p = 0.017) on WPM. The significance between range 1 and range 2 ( p = 0.013), range 1 and range 3( p = 0.012) was found. But the difference between range 2 and range 3( p = 0.968) was not significant. Figure 10. Demonstrates the performance of different angle ranges at typing speed. Display full size 5.5.2. Error rate 13 shows the text entry error rate of different angle ranges. No significant differences were found between any ranges. And all NCER is not high. But for TER, there was significant effects of angle ranges ( F 2,33 = 3.568, p = 0.039). The difference between range 1 and range 2 ( p = 0.024) was found. And significant differences between range 1 and range 3( p = 0.030) were found too. No significant differences were found between range 2 and range 3( p = 0.929). The error rate of range 1 is higher than the other two groups. 5.6. Discussion According to the experimental results, we can see that Hypothesis 3 is correct. It is unreasonable to divide 90° into three levels equally. The tilt angle we designed according to the user’s habit of tilting the wrist is more reasonable. It can achieve faster speed and less error rate. According to what a participant said after the experiment “To satisfy typing accuracy and wrist comfort. I must raise my forearm to tilt in Range 1. And other angle ranges don’t need.” For Hypothesis 4, there is no significant difference between Range 2 and Range 3. No matter that the participant did Range 2 first or Range 3 first. Most users did not notice the change in angle. Therefore, we believe that only using the tilt angle change within 3° will not affect the performance. We choose Range 2 with the best performance in Figures 10 and 11 as the angle range of the Climbing keyboard. Figure 11. Demonstrates the performance of different angle ranges at an error rate. Display full size 6. The third study: Performance evaluation We conducted a five-block user study to evaluate the performance of Climbing Keyboard (the best performing design found in the second study). The goal of the experiment is to measure the learning cost of Climbing Keyboard and how well users could perform text entry using Climbing keyboard. We were also interested in how the performance would be improved after a period of practice. 6.1. Participants and apparatus Ten right-handed participants (8 males; 2 females) between the ages of 22–25 ( M = 23.5) were recruited from our university campus to participate in this study. All participants input using the right hand in the experiment. The apparatus and devices in this study were the same as in the first study. 6.2. Experiment design and procedure To evaluate the learnability, there were no participants from the second study. Before the experiment, participants were told how to input by using the Climbing keyboard. The whole experiment was divided into five blocks. In each block, participants were asked to enter 10 phrases as accurately and quickly as possible. Between the two blocks, participants can take a break. We asked participants to type words as accurately and fast as possible. The whole experiment lasted approximately 40 minutes for each participant. In all, we collected 10(participant) × 5(block) × 10(phrase) = 500 phrases. 6.3. Result We employed an ANOVA with Sessions (from block one to block five) as the within-subject. 6.3.1. Text entry speed Figure 12 shows the change in the typing speed of participants over five blocks. We can see that the average speed of 11.21wpm can be achieved for the first block using this technology, and after five blocks of practice, the speed can reach 16.48wpm. The results of ANOVA show us the significant differences between blocks ( F 4.538 = 23.95, p = 0.000). Meanwhile, the result also revealed significant differences between Block 2 and Block 3( p = 0.000). No significant differences were found between Block 3 and Block 4 ( p = 0.358), nor between Block 4 and Block 5 ( p = 0.232). However, significant differences were found between Block 3 and Block 5 ( p = 0.035). This trend shows that the learning curve was getting more and more stable after each block. Figure 12. Text entry speed over 5 blocks. Display full size 6.3.2. Total and uncorrected error rate Figure 13 shows the NCER and TER changed over five blocks. For NCER, there were no significant differences between any blocks ( F 4.538 = 0.718, p = 0.58). For TER, there were significant differences between blocks ( F 4.538 = 2.807, p = 0.025). There were no significant differences between Block 1 and Block 2, Block 2, and Block 3, Block 3 and Block 4, Block 4 and Block 5. However, Significant differences were found between Block 1 and Block 3, Block 1, and Block 4, Block 1and Block 5. It can be seen from Figure 13 that the error rate is declining. This trend combined with the ANOVA results can show that the participants are making steady progress. Figure 13. Mean total error rate (TER) and not corrected error rate (NCER) over 5 blocks. Display full size 6.4. Discussion With 40 mins of typing phrases, participants reached speeds exceed 16 WPM, with a NCER near 2%. This outperforms the existing typing techniques in VR using handles. Table 3 shows the comparison of existing technology focused on text-entry by controller with Climbing Keyboard in terms of speed and accuracy. For each technology, we take the average of novices and experts. Climbing keyboard speed is higher than most input methods, and NCER is at a medium level. In terms of learnability, we believe Climbing Keyboard is easy-to-learn according to the learning curves. After entering 40 phrases, participants’ typing speed can be increased to 16.48WPM. At the same time, the error rate is reduced to 1.22%. Compared with the method of inputting in letters Speicher et al. ( Citation 2018 ), inputting in words is faster. Same as the method of inputting words as units Jiang and Weng ( Citation 2020 ), QWERTY’s layout is more reasonable and suitable for beginners to learn. The method of using Pointing as the input method Speicher et al. ( Citation 2018 ) is fast but feels tired when typing for a long time. This table is only for meta comparison in different surveys, the degree of formality is far less than in Direct comparative study. We think that the climbing keyboard performs well mainly because the climbing keyboard uses two hands to operate. Two-hand interaction technology, relative to one-hand interaction technology, can improve text input performance and accuracy to some extent Boletsis and Kongsvik ( Citation 2019 ), and is more convenient when performing complex and bimanually demanding tasks. One-handed input is only used when both hands are not free. However, given the standard indicators of WPM and NCER, we think this is a reasonable way to correlate Climbing’s keyboard performance. Table 3. the average value for each range Download CSV Display Table 7. Future work There are some limitations, although this work has successfully produced a novel text entry technique in VR. For example, after using the tilt handle to input the word sequence, the way to select candidate words is not convenient. The problem is that the input speed of words out of the dictionary is slow. In the future, we will improve this technique from two aspects as follows: Candidate words : There are only five candidate words listed, and the order of the candidate words is determined by their frequency of use. Increasing the time of use, will play a big role, and we will enrich our corpus. Special symbols : For a complete input method, in addition to input letters and words, it is also possible to input special characters and numbers. In future work, we will consider completing. Dominant hand : In this study, all users were right-handed. In future work, we will ask left-handed users to join the study and evaluate the system. We will put the Climbing keyboard on the App Store if possible. With the user’s permission, collect individual user’s tilt preferences and make adaptive adjustments. 8. Conclusion In this article, we introduced a Climbing keyboard , a text input technology for virtual reality without the help of other devices. This technology allows users to tilt the handle and touch the touchpad on the handle to enter the intended letters. Its design follows three design standards: fast input speed, high accuracy, and easy to learn. We conducted two user studies to determine the best design capabilities of the technology before evaluating its performance and usability. The first study compared several potential layouts of the Climbing keyboard to provide a basis for its final design. We performed a second experiment on the tilt angle of the handle. Through the experiments conducted by 12 participants, the final layout of the Climbing keyboard was further evaluated through tilted interaction and linear layout. The third study shows that novice users can reach 16.48 words/minute (WPM) in one and a half hours’ training, while experts Users can reach 18.53 WPM. The fastest speed of Pizzatext D. Yu et al. ( Citation 2018 ) is 15.86 WPM. An additional device dual thumb sticks of a hand-held game controller is needed, while Climbing keyboard can be implemented with the default VR controller. We believe that the Climbing keyboard is an interaction metaphor with excellent user experience (high-speed, low error rate, and easy to learn) for many VR applications. Acknowledgements We thank the participants in the user study for their availability and significant comments. Disclosure statement No potential conflict of interest was reported by the author(s).", "Keywords": "", "DOI": "10.1080/10447318.2022.2144120", "PubYear": 2024, "Volume": "40", "Issue": "5", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, China;Key Laboratory of Symbolic Computation and Knowledge Engineering of Ministry of Education, Jilin University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Changchun University of Science and Technology, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jinan University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Jilin University, China"}], "References": [{"Title": "Assamese Word Sense Disambiguation using Cuckoo Search Algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "189", "Issue": "", "Page": "142", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 104717243, "Title": "A Learning Automata-Based Approach to Improve the Scalability of Clustering-Based Recommender Systems", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2022.2145650", "PubYear": 2024, "Volume": "55", "Issue": "7", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Qom Branch, Islamic Azad University, Qom, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Arak Branch, Islamic Azad University, Arak, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Arak Branch, Islamic Azad University, Arak, Iran"}], "References": [{"Title": "Enhancing recommendation stability of collaborative filtering recommender system through bio-inspired clustering ensemble method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "2141", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Collaborative filtering recommendation algorithm based on user correlation and evolutionary clustering", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "147", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "An Effective Recommender System Based on Clustering Technique for TED Talks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "1", "Page": "35", "JournalTitle": "International Journal of Information Technology and Web Engineering"}, {"Title": "A hybrid recommender system for recommending relevant movies using an expert system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "158", "Issue": "", "Page": "113452", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Distributed Network Coding-Aware Routing Protocol Incorporating Fuzzy-Logic-Based Forwarders in Wireless Ad hoc Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "4", "Page": "1279", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Dynamic clustering collaborative filtering recommendation algorithm based on double-layer network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "1097", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "WITHDRAWN: Collaborative filtering recommendation algorithm based on Bee Colony K-means clustering model", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "103424", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A workload clustering based resource provisioning mechanism using Biogeography based optimization technique in the cloud based systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "5", "Page": "3813", "JournalTitle": "Soft Computing"}, {"Title": "Differentially private user-based collaborative filtering recommendation based on k -means clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114366", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An autonomous computation offloading strategy in Mobile Edge Computing: A deep learning-based hybrid approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "102974", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "A hybrid recommender system based-on link prediction for movie baskets analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "A fuzzy weighted influence non-linear gauge system with application to advanced technology assessment at NASA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115274", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sign prediction in sparse social networks using clustering and collaborative filtering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "1", "Page": "596", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Advances and challenges in conversational recommender systems: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> He", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "100", "JournalTitle": "AI Open"}, {"Title": "Adaptive personalized recommender system using learning automata and items clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "106", "Issue": "", "Page": "101978", "JournalTitle": "Information Systems"}, {"Title": "Content-Based Collaborative Filtering with Hierarchical Agglomerative Clustering Using User/ Item based Ratings", "Authors": "Cha<PERSON> S. V. V. <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "Supp01", "Page": "", "JournalTitle": "Journal of Interconnection Networks"}]}, {"ArticleId": *********, "Title": "Machine Learning Techniques-Based Banking Loan Eligibility Prediction", "Abstract": "<p>In our daily life, it is difficult to meet financial demand while in crisis. This financial crisis may be solved with financial assistance from the banks. The financial assistance is nothing but availing loan from the bank with proper agreement to repay the amount including calculated interest within the loan approved tenure. The customer can only avail loans against the submission of some valid and important supportive documents. However, although the customer is aware of the whole process of repayment and installment along with loan approval tenure, most of the time it is hard to get the approved loan within a shorter period. Therefore, the objective of this paper is to automate this manual and long process by predicting the chance of approval of the loan. The novelty of this research article is to apply machine learning techniques and classification algorithms to predict loan eligibility through an automatic online loan application process</p>", "Keywords": "", "DOI": "10.4018/IJDAI.313935", "PubYear": 2022, "Volume": "14", "Issue": "2", "JournalId": 69379, "JournalTitle": "International Journal of Distributed Artificial Intelligence", "ISSN": "2637-7888", "EISSN": "2637-7896", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 104717394, "Title": "A robust feature reinforcement framework for heterogeneous graphs neural networks", "Abstract": "In the real world, various kinds of data are able to be represented as heterogeneous graph structures. Heterogeneous graphs with multi-typed nodes and edges contain rich messages of heterogeneity and complex semantic information. Recently, diverse heterogeneous graph neural networks (HGNNs) have emerged to solve a range of tasks in this advanced area, such as node classification, knowledge graphs, etc. Heterogeneous graph embedding is a crucial step in HGNNs. It aims to embed rich information from heterogeneous graphs into low-dimensional eigenspaces to improve the performance of downstream tasks. Yet existing methods only project high-dimensional node features into the same low-dimensional space and subsequently aggregate those heterogeneous features directly. This approach ignores the balance between the informative dimensions and the redundant dimensions in the hidden layers. Further, after the dimensionality has been reduced, all kinds of nodes features are projected into the same eigenspace but in a mixed up fashion. One final problem with HGNNs is that their experimental results are always unstable and not reproducible. To solve these issues, we design a general framework named Robust Feature Reinforcement (RFR) for HGNNs to optimize embedding performance. RFR consists of three mechanisms: separate mapping, co-segregating and population-based bandits. The separate mapping mechanism improves the ability to preserve the most informative dimensions when projecting high-dimensional vectors into a low-dimensional eigenspace. The co-segregating mechanism minimizes the contrastive loss to ensure there is a distinction between the features extracted from different types of nodes in the latent feature layers. The population-based bandits mechanism further assures the stability of the experimental results with classification tasks. Supported by rigorous experimentation on three datasets, we assessed the performance of the designed framework and can verify that our models outperform the current state-of-the-arts.", "Keywords": "", "DOI": "10.1016/j.future.2022.11.009", "PubYear": 2023, "Volume": "141", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Wu", "Affiliation": "Department of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China;Corresponding author"}, {"AuthorId": 4, "Name": "Danfeng Sun", "Affiliation": "Department of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing, Macquarie University, Sydney, Australia"}], "References": [{"Title": "A Unified Form of Fuzzy C-Means and K-Means algorithms and its Partitional Implementation", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106731", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "A stacked autoencoder‐based convolutional and recurrent deep neural network for detecting cyberattacks in interconnected power control systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "12", "Page": "7080", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Megnn: Meta-path extracted graph neural network for heterogeneous graph representation learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107611", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Heterogeneous graph neural networks with denoising for graph embeddings", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "238", "Issue": "", "Page": "107899", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 104717516, "Title": "Deposition geometrical characteristics of wire arc additive-manufactured AA2219 aluminium alloy with cold metal transfer pulse advance arc mode", "Abstract": "<p>A cold metal transfer pulse advance (CMT-PA) arc mode was employed in this paper for the wire arc additive manufacturing of Al alloy. The effects of process parameters on deposition surface morphologies and geometrical characteristics were investigated. And a deposition width model was built by the multiple linear regressions. Based on the principle that the volume of the sample is equal to that of filler wire, a deposition height model was simultaneously derived. The results indicate that the disparity between two trends of droplet spreading in horizontal and molten pool tangential direction determines directly the final deposition geometrical characteristics. And two trends would be mainly affected by the heat input and arc force closely related to process parameters. The influences of three factors on the effective width percentage show a trend of first increasing and then decreasing. So, it provides an optimal process window for good deposition forming. The effective width percentage reaches 83% and the machining allowance is only 0.71 mm, which significantly improves material utilization and reduces manufacturing costs. Besides, the error rates of deposition width and height models are less than 4% and 6%, respectively. Two models can facilitate fabricating different size complex parts and make a profit for the actual production.</p>", "Keywords": "Aluminium alloy; Wire arc additive manufacturing; Cold metal transfer pulse advance arc mode; Deposition geometrical characteristic; Dimension model", "DOI": "10.1007/s00170-022-10460-4", "PubYear": 2022, "Volume": "123", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "Yazhou Zhang", "Affiliation": "Wuhan National Laboratory for Optoelectronics (WNLO), Huazhong University of Science and Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Wuhan National Laboratory for Optoelectronics (WNLO), Huazhong University of Science and Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Wuhan National Laboratory for Optoelectronics (WNLO), Huazhong University of Science and Technology, Wuhan, People’s Republic of China"}, {"AuthorId": 4, "Name": "Wen<PERSON> Du", "Affiliation": "National Key Laboratory for Remanufacturing, Army Academy of Armored Forces, Beijing, People’s Republic of China"}], "References": []}, {"ArticleId": 104717818, "Title": "A new method for designing piezo transducers with connected two-phase electrode", "Abstract": "In this paper, we present a new method for designing piezoelectric transducers with connected two-phase electrode. The electrode design problem is formulated as an optimization problem, and the most significant contribution is modeling of connectivity constraints by using results from graph theory. Several examples are shown to corroborate that optimized electrode configurations lead to devices of easier manufacturability, still bearing in mind functionality.", "Keywords": "Optimal design ; Piezo transducers ; Connectivity ; Two-phase electrode", "DOI": "10.1016/j.compstruc.2022.106936", "PubYear": 2023, "Volume": "275", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Matemáticas, ETSII, Universidad de Castilla-La Mancha, Ciudad Real, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Matemáticas, ETSII, Universidad de Castilla-La Mancha, Ciudad Real, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Matemáticas, EIIA, Universidad de Castilla-La Mancha, Toledo, Spain"}], "References": [{"Title": "Numerical performance of <PERSON>isson method for restricting enclosed voids in topology optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "239", "Issue": "", "Page": "106337", "JournalTitle": "Computers & Structures"}]}, {"ArticleId": 104717832, "Title": "Team O2AC at the world robot summit 2020: towards jigless, high-precision assembly", "Abstract": "High-mix, low-volume assembly has been a long-standing challenge for robot systems. We present a complete 2-armed robot system with general-purpose grippers and hand-held tools, which can perform assembly for a wide variety of objects with tight tolerances. The complete source code and 3D-printed-part designs are available for download and can be executed in simulation and with physical robots using a regular desktop computer. Furthermore, we present the designs of multiple tools which can be grasped and used by the robots in a human-like fashion. The system uses no parts-specific jigs and grippers to be applied to many different parts. It achieves high precision and reliability by using the environment and gripper surfaces to position the grasped objects. The system obtained 3rd place and the Japanese Society for Artificial Intelligence Award at the World Robot Summit 2020 Assembly Challenge. GRAPHICAL", "Keywords": "Robots in manufacturing ; robot competitions ; factory automation ; assembly ; multi-stage planning", "DOI": "10.1080/01691864.2022.2138541", "PubYear": 2022, "Volume": "36", "Issue": "22", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "OMRON SINIC X Corporation, Kyoto, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": "Osaka University, Osaka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "OMRON Corporation, Kyoto, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Osaka University, Osaka, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chukyo University, Aichi, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "OMRON SINIC X Corporation, Kyoto, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chukyo University, Aichi, Japan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "OMRON SINIC X Corporation, Kyoto, Japan"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan"}, {"AuthorId": 10, "Name": "Weiwei Wan", "Affiliation": "Osaka University, Osaka, Japan;National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Osaka University, Osaka, Japan;National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan"}], "References": [{"Title": "Software development environment for collaborative research workflow in robotic system integration", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "11", "Page": "533", "JournalTitle": "Advanced Robotics"}]}, {"ArticleId": 104718018, "Title": "Evaluating cross-selling opportunities with recurrent neural networks on retail marketing", "Abstract": "<p>Recommender systems are considered to be capable of predicting what the next product a customer should purchase is. It is crucial to identify which customers are more suitable than others to target a product for cross-selling in the retail industry. Using recurrent neural networks with self-attention mechanisms, this study proposes a hybrid model. Furthermore, the proposed design is capable of handling both sequential and non-sequential features, which correspond to purchase behavior and non-behavioral customer specific information, respectively. This study represents an alternative solution to a well-known business problem: improving cross-selling effectiveness by estimating customers’ likelihood for which products or services to buy next time. A recommender system which works on additional data configurations is the core concept of the framework. With an online shopping data set, this study shows that concatenation of relevant features adds additional information to the model, and it is found that evaluation metrics are improved by approximately 12%.</p>", "Keywords": "Marketing management; Recurrent neural networks; Recommender systems; Cross-selling", "DOI": "10.1007/s00521-022-08019-1", "PubYear": 2023, "Volume": "35", "Issue": "8", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Cukurova University, Adana, Türkiye"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Cukurova University, Adana, Türkiye"}], "References": [{"Title": "Deep Learning Based Recommender System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Bidirectional LSTM with self-attention mechanism and multi-channel features for sentiment classification", "Authors": "Weijiang Li; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "387", "Issue": "", "Page": "63", "JournalTitle": "Neurocomputing"}, {"Title": "Measuring Customer Similarity and Identifying Cross-Selling Products by Community Detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "2", "Page": "132", "JournalTitle": "Big Data"}]}, {"ArticleId": 104718091, "Title": "A new fractional model applied to description of the viscoelastic creep behavior of two Brazilian oils and their w/o emulsions", "Abstract": "The objective of this work was to investigate and model the viscoelastic creep behavior of two Brazilian crude oils: A (API 16.8) and B (API 24.6), with different contents of aromatics, resins and asphaltenes and their W/O emulsions with water contents between 0 to 55%. Stable emulsions were prepared using an Ultra-Turrax homogenizer and the viscoelastic flow data were obtained from an Anton Paar controlled voltage Rheometer with plate-plate geometry. For modeling purposes, the fractional model was able to describe the experimental data very well obtaining a mean correlation coefficient ( R ¯ 2 ) equal to 0.9956 and root mean squared errors (RMSE) of the order of 10 − 13 for oil A and its emulsions and 10 − 12 for oil B and its emulsions.", "Keywords": "Viscoelastic creep ; Oil ; Emulsions", "DOI": "10.1016/j.dche.2022.100069", "PubYear": 2023, "Volume": "6", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal University of Paraná, Department of Chemical Engineering, Curitiba, PR 81531-980, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Paraná, Department of Chemical Engineering, Curitiba, PR 81531-980, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Paraná, Department of Chemical Engineering, Curitiba, PR 81531-980, Brazil"}, {"AuthorId": 4, "Name": "Montserrat Fortuny", "Affiliation": "Federal University of Paraná, Department of Chemical Engineering, Curitiba, PR 81531-980, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Ponta Grossa State University, Department of Physics, Ponta Grossa, PR 84030-990, Brazil"}], "References": []}, {"ArticleId": 104718118, "Title": "3D Quadratic ODE systems with an infinite number of limit cycles", "Abstract": "We consider an autonomous three-dimensional quadratic ODE system with nine parameters, which is a generalization of the <PERSON>ford system. We derive conditions under which this system has infinitely many limit cycles. First, we study the equilibrium points of such systems and their eigenvalues. Next, we prove the non-local existence of an infinite set of limit cycles emerging by means of <PERSON><PERSON><PERSON> – <PERSON> bifurcation.", "Keywords": "", "DOI": "10.1051/itmconf/20224902006", "PubYear": 2022, "Volume": "49", "Issue": "", "JournalId": 22598, "JournalTitle": "ITM Web of Conferences", "ISSN": "", "EISSN": "2271-2097", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Yanka Kupala State University of Grodno, Belarus"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Yanka Kupala State University of Grodno, Belarus"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Yanka Kupala State University of Grodno, Belarus"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Craiova, Romania"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Craiova, Romania"}], "References": []}, {"ArticleId": 104718131, "Title": "CyberSignature: A user authentication tool based on behavioural biometrics", "Abstract": "Behavioural biometrics, such as the way people type on computer keyboard and/or move the cursor are almost impossible to steal. This paper presents CyberSignature <sup>1</sup> , a tool that uses behavioural biometrics to create unique digital identities that can be used during online card transactions to distinguish legitimate users from fraudsters. The tool is implemented in Python, with a machine learning algorithm at its core. It receives user input data entries from a graphical user interface, similar to an online payment form, and transforms them into unique digital identities. The tool is freely available on Github and is entitled ‘CyberSignature’ .", "Keywords": "Behavioural biometrics ; Payment authentication ; Digital identity ; Cyber-security ; Identity fraud detection ; Machine learning", "DOI": "10.1016/j.simpa.2022.100443", "PubYear": 2022, "Volume": "14", "Issue": "", "JournalId": 66395, "JournalTitle": "Software Impacts", "ISSN": "2665-9638", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Edge Hill University, St Helens Road, Ormskirk, L39 4QP, United Kingdom;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Edge Hill University, St Helens Road, Ormskirk, L39 4QP, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Edge Hill University, St Helens Road, Ormskirk, L39 4QP, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Edge Hill University, St Helens Road, Ormskirk, L39 4QP, United Kingdom"}], "References": [{"Title": "A behaviour biometrics dataset for user identification and authentication", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "45", "Issue": "", "Page": "108728", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 104718207, "Title": "The changing scope of data quality and fit for purpose: evolution and adaption of a CRIS solution", "Abstract": "The effectiveness of a Current Research Information System (CRIS) is based on satisfying essential institutional needs, or purposes, regarding the capture, processing and reporting of research-related activities and outcomes. These needs, or purposes, exemplified in the Code of Good Practice and introduced in 1998, have remained relatively constant over time. However, the scope and nature of the underlying data supporting these needs have grown in complexity, thus necessitating a concurrent increase in sophistication for how data quality is addressed and improved. This publication aims to introduce and analyse the implications and solutions required to improve data quality within the scope of fit for purpose in a CRIS context. Drawing from, and building on, data and information quality foundations and descriptions, a data quality framework is introduced, and detailed product functionality is described. A discussion on the combination of framework and functionality highlights how data quality can be improved in CRIS products.", "Keywords": "Current Research Information System (CRIS) ; CRIS ; Research Information Management System (RIMS) ; RIMS ; Data quality ; Fit for purpose ; Data deduplication ; Open Access ; Pure", "DOI": "10.1016/j.procs.2022.10.192", "PubYear": 2022, "Volume": "211", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Elsevier , Radarweg 29 , 1043 NX , Amsterdam"}], "References": []}, {"ArticleId": 104718219, "Title": "An automated treatment plan alert system to safeguard cancer treatments in radiation therapy", "Abstract": "In radiation oncology, the intricate process of delivering radiation to a patient is detailed by the patient’s treatment plan, which is data describing the geometry, construction and strength of the radiation machine and the radiation beam it emits. The patient’s life depends upon the accuracy of the treatment plan, which is left in the hands of the vendor-specific software automatically generating the plan after an initial patient consultation and planning with a medical professional. However, corrupted and erroneous treatment plan data have previously resulted in severe patient harm when errors go undetected and radiation proceeds. The aim of this paper is to develop an automatic error-checking system to prevent the accidental delivery of radiation treatment to an area of the human body (i.e., the treatment site) that differs from the plan’s documented intended site. To this end, we develop a method for structuring treatment plan data in order to feed machine-learning (ML) classifiers and predict a plan’s treatment site. In practice, a warning may be raised if the prediction disagrees with the documented intended site. The contribution of this paper is in the strategic structuring of the complex, intricate, and nonuniform data of modern treatment planning and from multiple vendors in order to easily train ML algorithms. A three-step process utilizing up- and down-sampling and dimension reduction, the method we develop in this paper reduces the thousands of parameters comprising a single treatment plan to a single two-dimensional heat map that is independent of the specific vendor or construction of the machine used for treatment. Our heat-map structure lends itself well to feed well-established ML algorithms, and we train–test random forest, softmax, k-nearest neighbors, shallow neural network, and support vector machine using real clinical treatment plans from several hospitals in the United States. The paper demonstrates that the proposed method characterizes treatment sites so well that ML classifiers may predict head-neck, breast, and prostate treatment sites with an accuracy of about 94%. The proposed method is the first step towards a thorough, fully automated error-checking system in radiation therapy.", "Keywords": "Cancer classification ; Radiation heat map ; Nonuniform treatment data", "DOI": "10.1016/j.mlwa.2022.100437", "PubYear": 2022, "Volume": "10", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, SUNY Maritime College, 6 Pennyfield Ave., Bronx, NY 10465, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiation Oncology, Mount Sinai Hospital, New York City, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Radiation Oncology, University of Iowa, IA, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Iowa, Iowa City, IA, USA"}], "References": []}, {"ArticleId": 104718272, "Title": "Strategyproof Allocation Mechanisms with Endowments and M-convex Distributional Constraints", "Abstract": "We consider an allocation problem of multiple types of objects to agents, where each type of object has multiple copies (e.g., multiple seats in a school), each agent is endowed with an object, and some distributional constraints are imposed on the allocation (e.g., minimum/maximum quotas). We develop two mechanisms that are strategyproof, feasible (they always satisfy distributional constraints), and individually rational, assuming the distributional constraints are represented by an M-convex set. One mechanism, based on Top Trading Cycles, is Pareto efficient; the other, which belongs to the mechanism class specified by <PERSON> et al. [1] , satisfies a relaxed fairness requirement. The class of distributional constraints we consider contains many situations raised from realistic matching problems, including individual minimum/maximum quotas, regional maximum quotas, type-specific quotas, and distance constraints. Finally, we experimentally evaluate the performance of these mechanisms by a computer simulation.", "Keywords": "Controlled school choice ; M-convex set ; Strategyproofness ; Top trading cycles ; Deferred acceptance ; Distributional constraints", "DOI": "10.1016/j.artint.2022.103825", "PubYear": 2023, "Volume": "315", "Issue": "", "JournalId": 13748, "JournalTitle": "Artificial Intelligence", "ISSN": "0004-3702", "EISSN": "1872-7921", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Gifu Shotoku Gakuen University, Gifu, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Keio University, Yokohama, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Kyushu University, Fukuoka, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Kyushu University, Fukuoka, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Groningen, Groningen, the Netherlands;Corresponding author"}], "References": [{"Title": "Strategyproof and fair matching mechanism for ratio constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}]}, {"ArticleId": 104718274, "Title": "Influence of machining parameters in longitudinal-torsional ultrasonic vibration milling titanium alloy for milling force", "Abstract": "<p>To study the effect of different parameters on milling force for longitudinal-torsional ultrasonic vibration milling (LTUM) of titanium alloy, the kinematic theory of LTUM is combined with the model of milling transient cutting thickness to establish the milling force equation, and experiment was carried out. The experimental results showed that milling force was positively correlated with cutting speed, cutting depth, feed per tooth (milling force increased by about 40% in increasing the cutting speed from 40 to 100 m/min, 300% in increasing the depth of cut from 0.1 to 0.4 mm, and 25% in increasing the feed per tooth from 0.01 to 0.04 mm). Milling force was negatively correlated with ultrasonic amplitude, tool helix angle (milling force reduced by about 22% in increasing the ultrasonic amplitude from 1 to 4 μm, and 23% in increasing the tool helix angle from 30 to 45°). The milling force was minimized when the ultrasonic amplitude was 4 μm; cutting speed was 60 m/min; cutting depth was 0.1 mm; feed per tooth was 0.01 mm/z, and tool helix angle was 40°. Furthermore, the empirical model of milling force was established, and accuracy was verified by experimental data.</p>", "Keywords": "Longitudinal-torsional ultrasonic vibration; Empirical model of milling force; Cutting thickness instantaneously; Milling experiment·titanium alloy", "DOI": "10.1007/s00170-022-10509-4", "PubYear": 2022, "Volume": "123", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhu", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Power Engineering, Henan Polytechnic University, Jiaozuo, People’s Republic of China"}], "References": [{"Title": "Finite element simulation of ultrasonic-assisted machining: a review", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "9-10", "Page": "2777", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Investigation of tool-workpiece contact rate and milling force in elliptical ultrasonic vibration-assisted milling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "1-2", "Page": "585", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 104718310, "Title": "Enhancing short-term forecasting of daily precipitation using numerical weather prediction bias correcting with XGBoost in different regions of China", "Abstract": "Accurate precipitation (P) short-term forecasts are important for engineering studies and water allocation. This study evaluated a method for bias correction of the Numerical Weather Prediction (NWP) of Global Ensemble Forecast System V2 forecasts based on the extreme gradient boosting (XGBoost) model (M3) and 689 meteorological stations in seven different climatic regions of China. The method used a common deviation correction for multiple meteorological factors to forecast P for 1–8 d ahead. It was also compared with the equidistant cumulative distribution functions matching a single weather factor (EDCDFm, M1) and the XGBoost model (M2). The M3 method had the best forecast performance. M1, M2, and M3 methods had an average root mean square error (RMSE) ranging from 2.292–17.049 mm, 1.844–18.835 mm, and 1.819–13.608 mm, respectively. The performance of each method tended to decrease as the lead time was extended. The average false alarm ratio (increased from 55.3%, 52.8% and 50.1% to 75.8%, 82.3% and 76.0%, respectively) and miss ratio (increased from 60.9%, 53.5% and 50.3% to 76.6%, 77.7% and 71.2%, respectively) also increased with an increased lead time for all methods. The forecast performance trended downwards from northwest to southeast China. However, each method’s significance in forecasting P’s determination coefficient showed a contrary pattern to the forecast accuracy. There was a general underestimation across the methods. The best performance for forecasting P was achieved in winter, with root mean square error values of 2.0–3.4 mm, followed in order by autumn &gt; spring &gt; summer. Factor P contributed the most to forecast P after bias correction of the XGBoost model (average Gain, Cover, and Frequency values of 0.55, 0.45, and 0.29, respectively). In summary, satisfactory performance could be obtained using the XGBoost model combined with multi-factor bias correction for NWP data to forecast daily P.", "Keywords": "", "DOI": "10.1016/j.engappai.2022.105579", "PubYear": 2023, "Volume": "117", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "Jianhua Dong", "Affiliation": "State Key Laboratory of Water Resources and Hydropower Engineering Science, Wuhan University, Wuhan, 430072, China"}, {"AuthorId": 2, "Name": "Wen<PERSON> Zeng", "Affiliation": "State Key Laboratory of Water Resources and Hydropower Engineering Science, Wuhan University, Wuhan, 430072, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Hydraulic and Ecological Engineering, Nanchang Institute of Technology, Nanchang, 330099, China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Water Resources and Hydropower Engineering Science, Wuhan University, Wuhan, 430072, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Crop Science Group, Institute of Crop Science and Resource Conservation (INRES), University of Bonn, Katzenburgweg 5, D-53115 Bonn, Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Crop Science Group, Institute of Crop Science and Resource Conservation (INRES), University of Bonn, Katzenburgweg 5, D-53115 Bonn, Germany"}], "References": [{"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Exponential stability of nonlinear state-dependent delayed impulsive systems with applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "", "Page": "101088", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 104718334, "Title": "Detection of nonpolar n-dodecane at room temperature using multiphase MoS2 chemiresistive sensor: Investigation of charge transfer on nonpolar VOC molecule", "Abstract": "Room temperature chemiresistive sensors are potential for the noninvasive disease diagnostics by detecting volatile organic compound biomarkers from exhaled breath. In this work, we fabricated a molybdenum disulfide (MoS<sub>2</sub>) chemiresistive sensor for highly selective detection of the nonpolar n-dodecane at room temperature. X-ray diffraction and Raman spectroscopy studies confirmed the presence of the multiphase (metallic 1 T phase along with the semiconducting 2 H phase) MoS<sub>2</sub>. The field emission scanning electron microscopic images showed the flower-like interconnected MoS<sub>2</sub> sheets with an average size of 10–15 nm. Compared to the single-phase 2 H MoS<sub>2</sub> sensor, the multiphase (1 T/2 H) MoS<sub>2</sub> sensor showed a higher response and could detect the n -dodecane concentration as low as 400 ppb. The response and recovery time of the sensor were calculated as 40 s and 60 s, respectively. The multiphase MoS<sub>2</sub> sensor exhibited better sensing properties toward the detection of n -dodecane from the simulated breath sample with a relative error of ∼0.12. Interestingly, density functional theory analysis revealed that the presence of metallic 1 T phase in the multiphase MoS<sub>2</sub> is responsible for vapor adsorption and charge transfer in the n -dodecane sensing. The present results are promising for the development of room temperature nonpolar sensors.", "Keywords": "VOC sensor ; Lung cancer biomarker ; n -dodecane ; Multiphase MoS<sub>2</sub> ; Chemiresistive sensor", "DOI": "10.1016/j.snb.2022.132994", "PubYear": 2023, "Volume": "376", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Nanoscience and Technology, Advanced Materials and Devices Laboratory (AMDL), Bharathiar University, Coimbatore 641046, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Atomistic Simulation Laboratory (ASL), Bharathiar University, Coimbatore 6410 46, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Nanoscience and Technology, Advanced Materials and Devices Laboratory (AMDL), Bharathiar University, Coimbatore 641046, Tamil Nadu, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Nanoscience and Technology, Advanced Materials and Devices Laboratory (AMDL), Bharathiar University, Coimbatore 641046, Tamil Nadu, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Atomistic Simulation Laboratory (ASL), Bharathiar University, Coimbatore 6410 46, Tamil Nadu, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Nanoscience and Technology, Advanced Materials and Devices Laboratory (AMDL), Bharathiar University, Coimbatore 641046, Tamil Nadu, India;Corresponding author"}], "References": []}, {"ArticleId": 104718402, "Title": "Microblog sentiment analysis based on deep memory network with structural attention", "Abstract": "Microblog sentiment analysis has important applications in many fields, such as social media analysis and online product reviews. However, the traditional methods may be challenging to compute the long dependencies between them and easy to lose some semantic information due to low standardization of text and emojis in microblogs. In this paper, we propose a novel deep memory network with structural self-attention, storing long-term contextual information and extracting richer text and emojis information from microblogs, which aims to improve the performance of sentiment analysis. Specifically, the model first utilizes a bidirectional long short-term memory network to extract the semantic information in the microblogs, and considers the extraction results as the memory component of the deep memory network, storing the long dependencies and free of syntactic parser, sentiment lexicon and feature engineering. Then, we consider multi-step structural self-attention operations as the generalization and output components. Furthermore, this study also employs a penalty mechanism to the loss function to promote the diversity across different hops of attention in the model. This study conducted extensive comprehensive experiments with eight baseline methods on real datasets. Results show that our model outperforms those state-of-the-art models, which validates the effectiveness of the proposed model.", "Keywords": "Microblog sentiment analysis; Subjectivity recognition; Deep memory network; Structural self-attention", "DOI": "10.1007/s40747-022-00904-5", "PubYear": 2023, "Volume": "9", "Issue": "3", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Nanjing University of Science and Technology, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai, China"}], "References": [{"Title": "Classification of User Comment Using Word2vec and SVM Classifier", "Authors": "Rafly Indra Kurnia", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "643", "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering"}, {"Title": "Multi-entity sentiment analysis using self-attention based hierarchical dilated convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "116", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Would you notice if fake news changed your behavior? An experiment on the unconscious effects of disinformation", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "106633", "JournalTitle": "Computers in Human Behavior"}, {"Title": "User’s Review Habits Enhanced Hierarchical Neural Network for Document-Level Sentiment Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "2095", "JournalTitle": "Neural Processing Letters"}, {"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Evaluation of online emoji description resources for sentiment analysis purposes", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115279", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Attention-based BiLSTM models for personality recognition from user-generated content", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "596", "Issue": "", "Page": "460", "JournalTitle": "Information Sciences"}, {"Title": "Learning to rank complex network node based on the self-supervised graph convolution model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109220", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 104718471, "Title": "Issue Information", "Abstract": "", "Keywords": "", "DOI": "10.1002/net.22054", "PubYear": 2022, "Volume": "80", "Issue": "4", "JournalId": 7540, "JournalTitle": "Networks", "ISSN": "0028-3045", "EISSN": "1097-0037", "Authors": [], "References": []}, {"ArticleId": 104718511, "Title": "Improving customer satisfaction in proactive service design: A Kano model approach", "Abstract": "<p>As an emergent variant of digital and smart services, proactive services (PAS) do not wait for customers to make the first move, but proactively participate in customers’ lives and make decisions on their behalf. Due to their novelty, the literature on PAS is in its infancy. Specifically, there is a lack of guidance on designing PAS to meet customer needs. Hence, we examined how customers assess specific features of PAS and whether their assessments differ according to personality traits. To this end, we conducted an online survey via the crowdsourcing platform Prolific, which yielded 259 valid responses. We used a methodological combination of the Kano model, self-stated importance method, and the Five Factor model. Our results reveal that, at the moment, customers do not value features of PAS related to autonomy and that customers engage in paradoxical behavior when assessing the use of personal data. These results allow for a more precise classification and prioritization of the features of PAS tuned to a customer’s most prevalent personality trait.</p>", "Keywords": "Customer satisfaction; Kano model; Proactive services; Personality traits; Service design; M30; O30; L80; L86", "DOI": "10.1007/s12525-022-00565-9", "PubYear": 2022, "Volume": "32", "Issue": "3", "JournalId": 16578, "JournalTitle": "Electronic Markets", "ISSN": "1019-6781", "EISSN": "1422-8890", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Project Group Business & Information Systems Engineering, and Research Center Finance & Information Management, University of Bayreuth, Bayreuth, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research Center Finance & Information Management, University of Augsburg, Augsburg, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Project Group Business & Information Systems Engineering, and Research Center Finance & Information Management, University of Bayreuth, Bayreuth, Germany"}], "References": [{"Title": "Customer-centric prioritization of process improvement projects", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "113286", "JournalTitle": "Decision Support Systems"}, {"Title": "User preferences for privacy features in digital assistants", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "411", "JournalTitle": "Electronic Markets"}, {"Title": "Designing Recommendation or Suggestion Systems: looking to the future", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "2", "Page": "243", "JournalTitle": "Electronic Markets"}]}]