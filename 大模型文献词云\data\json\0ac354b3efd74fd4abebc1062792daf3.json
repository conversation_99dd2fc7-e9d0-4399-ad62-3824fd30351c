[{"ArticleId": 111961988, "Title": "FedAVE: Adaptive data value evaluation framework for collaborative fairness in federated learning", "Abstract": "Collaborative fairness in federated learning rewards high-contribution clients with high-performance models when multiple clients train a machine learning model cooperatively. Existing approaches ignore the information on data distribution when evaluating the clients’ data quality, resulting in a mismatch between the reward allocation and the real data quality of clients under different data heterogeneity settings. To address this problem, we propose a novel Federated learning framework with Adaptive data Value Evaluation mechanism (FedAVE) to ensure collaborative fairness without affecting the predictive performance of models. First, an adaptive reputation calculation module is designed to generate reputations that match the clients’ contributions based on the information of their data distribution, respectively. Second, a dynamic gradient reward distribution module is devised to allocate a certain number of aggregated model parameter updates/gradients as the rewards corresponding to the reputations and the data distribution information. Extensive experiments on three public benchmarks show that the proposed FedAVE outperforms all baseline methods in terms of fairness, and achieves comparable performance to the state-of-the-art methods in terms of accuracy. Code available at https://github.com/wangzihuixmu/FedAVE .", "Keywords": "", "DOI": "10.1016/j.neucom.2023.127227", "PubYear": 2024, "Volume": "574", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China;National Institute for Data Science in Health and Medicine, Xiamen University, PR China;Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, Xiamen University, 361005, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China;National Institute for Data Science in Health and Medicine, Xiamen University, PR China;Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, Xiamen University, 361005, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China;National Institute for Data Science in Health and Medicine, Xiamen University, PR China;Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, Xiamen University, 361005, PR China;Corresponding author at: Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China;National Institute for Data Science in Health and Medicine, Xiamen University, PR China;Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, Xiamen University, 361005, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China;National Institute for Data Science in Health and Medicine, Xiamen University, PR China;Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, Xiamen University, 361005, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China;National Institute for Data Science in Health and Medicine, Xiamen University, PR China;Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, Xiamen University, 361005, PR China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Fujian Key Laboratory of Sensing and Computing for Smart Cities, School of Informatics, Xiamen University, 361005, PR China;National Institute for Data Science in Health and Medicine, Xiamen University, PR China;Key Laboratory of Multimedia Trusted Perception and Efficient Computing, Ministry of Education of China, Xiamen University, 361005, PR China"}], "References": [{"Title": "FedSA: A staleness-aware asynchronous Federated Learning algorithm with non-IID data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "1", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "FedSim: Similarity guided model aggregation for Federated Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "483", "Issue": "", "Page": "432", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111961997, "Title": "Regular Abstractions for Array Systems", "Abstract": "<p>Verifying safety and liveness over array systems is a highly challenging problem. Array systems naturally capture parameterized systems such as distributed protocols with an unbounded number of processes. Such distributed protocols often exploit process IDs during their computation, resulting in array systems whose element values range over an infinite domain. In this paper, we develop a novel framework for proving safety and liveness over array systems. The crux of the framework is to overapproximate an array system as a string rewriting system (i.e. over a finite alphabet) by means of a new predicate abstraction that exploits the so-called indexed predicates. This allows us to tap into powerful verification methods for string rewriting systems that have been heavily developed in the last two decades or so (e.g. regular model checking). We demonstrate how our method yields simple, automatically verifiable proofs of safety and liveness properties for challenging examples, including <PERSON><PERSON><PERSON>'s self-stabilizing protocol and the Chang-Roberts leader election protocol.</p>", "Keywords": "", "DOI": "10.1145/3632864", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Chengchi University, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Max-Planck Institute for Software Systems (MPI-SWS), Kaiserslautern, Germany / University of Kaiserslautern-Landau, Kaiserslautern, Germany"}], "References": [{"Title": "Rely-guarantee bound analysis of parameterized concurrent shared-memory programs: With an application to proving that non-blocking algorithms are bounded lock-free", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "2", "Page": "270", "JournalTitle": "Formal Methods in System Design"}, {"Title": "Temporal prophecy for proving temporal properties of infinite-state systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "2", "Page": "246", "JournalTitle": "Formal Methods in System Design"}]}, {"ArticleId": 111962019, "Title": "EasyBC: A Cryptography-Specific Language for Security Analysis of Block Ciphers against Differential Cryptanalysis", "Abstract": "<p>Differential cryptanalysis is a powerful algorithmic-level attack, playing a central role in evaluating the security of symmetric cryptographic primitives. In general, the resistance against differential cryptanalysis can be characterized by the maximum expected differential characteristic probability. In this paper, we present generic and extensible approaches based on mixed integer linear programming (MILP) to bound such probability. We design a high-level cryptography-specific language EasyBC tailored for block ciphers and provide various rigorous procedures, as differential denotational semantics, to automate the generation of MILP from block ciphers written in EasyBC. We implement an open-sourced tool that provides support for fully automated resistance evaluation of block ciphers against differential cryptanalysis. The tool is extensively evaluated on 23 real-life cryptographic primitives including all the 10 finalists of the NIST lightweight cryptography standardization process. The experiments confirm the expressivity of EasyBC and show that the tool can effectively prove the resistance against differential cryptanalysis for all block ciphers under consideration. EasyBC makes resistance evaluation against differential cryptanalysis easily accessible to cryptographers.</p>", "Keywords": "", "DOI": "10.1145/3632871", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ShanghaiTech University, Shanghai, China"}, {"AuthorId": 2, "Name": "Fu Song", "Affiliation": "Chinese Academy of Sciences, Beijing, China / University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ShanghaiTech University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Birkbeck University of London, London, UK"}], "References": [{"Title": "A Hybrid Approach to Formal Verification of Higher-Order Masked Arithmetic Programs", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "STP models of optimal differential and linear trail for S-box based ciphers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> Liang; Muzhou Li", "PubYear": 2021, "Volume": "64", "Issue": "5", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Accelerating the Search of Differential and Linear Characteristics with the SAT Method", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "269", "JournalTitle": "IACR Transactions on Symmetric Cryptology"}, {"Title": "SuperBall: A New Approach for MILP Modelings of Boolean Functions", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "341", "JournalTitle": "IACR Transactions on Symmetric Cryptology"}, {"Title": "Towards Tight Differential Bounds of Ascon: A Hybrid Usage of SMT and MILP", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "303", "JournalTitle": "IACR Transactions on Symmetric Cryptology"}, {"Title": "Differential cryptanalysis of WARP", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "", "Page": "103316", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "EasyBC: A Cryptography-Specific Language for Security Analysis of Block Ciphers against Differential Cryptanalysis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "Page": "848", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111962027, "Title": "An Improved Analytical Model of a Thrust Stand with a Flexure Hinge Structure Considering Stiffness Drift and Rotation Center Offset", "Abstract": "<p>Micro-newton thrust stands are widely used in thruster ground calibration procedures for a variety of space missions. The conventional analytical model does not consider the gravity-induced extension effect and systematic error in displacement for thrust stands consisting of hanging pendulums based on flexure hinge structures. This paper proposes an improved analytical model of a hanging pendulum for thrust measurement, where an elliptical notched flexure hinge is the key component. A parametric model of the bending stiffness of the flexure hinge is developed. Equally, both the bending stiffness shift under the gravity-induced extension effect and the systematic error in displacement due to the assumed rotational center offset of the hinge are investigated. The presented stiffness equations for elliptical notched hinges can be degenerated into stiffness equations for circular notched as well as leaf-type hinges. The improved model aims to evaluate and highlight the influence of the two considered factors for use in thrust stand parameter design and thrust analysis. A finite element modeling solution is proposed to validate the proposed analytical model. The results show that the proposed model can quantify the hinge bending stiffness shift, which also demonstrates that even a small bending stiffness shift may introduce great uncertainty into the thrust analysis.</p>", "Keywords": "micro-newton thrust stand; analytical model; flexure hinge; bending stiffness; uncertainty micro-newton thrust stand ; analytical model ; flexure hinge ; bending stiffness ; uncertainty", "DOI": "10.3390/act13010021", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "Xingyu Chen", "Affiliation": "School of Instrument Science and Engineering, Southeast University, Nanjing 210096, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Instrument Science and Engineering, Southeast University, Nanjing 210096, China; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Instrument Science and Engineering, Southeast University, Nanjing 210096, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Instrument Science and Engineering, Southeast University, Nanjing 210096, China"}], "References": []}, {"ArticleId": 111962028, "Title": "Fully Composable and Adequate Verified Compilation with Direct Refinements between Open Modules", "Abstract": "<p>Verified compilation of open modules (i.e., modules whose functionality depends on other modules) provides a foundation for end-to-end verification of modular programs ubiquitous in contemporary software. However, despite intensive investigation in this topic for decades,the proposed approaches are still difficult to use in practice as they rely on assumptions about the internal working of compilers which make it difficult for external users to apply the verification results. We propose an approach to verified compositional compilation without such assumptions in the setting of verifying compilation of heterogeneous modules written in first-order languages supporting global memory and pointers. Our approach is based on the memory model of CompCert and a new discovery that a Kripke relation with a notion of memory protection can serve as a uniform and composable semantic interface for the compiler passes. By absorbing the rely-guarantee conditions on memory evolution for all compiler passes into this Kripke Memory Relation and by piggybacking requirements on compiler optimizations onto it, we get compositional correctness theorems for realistic optimizing compilers as refinements that directly relate native semantics of open modules and that are ignorant of intermediate compilation processes. Such direct refinements support all the compositionality and adequacy properties essential for verified compilation of open modules. We have applied this approach to the full compilation chain of CompCert with its Clight source language and demonstrated that our compiler correctness theorem is open to composition and intuitive to use with reduced verification complexity through end-to-end verification of non-trivial heterogeneous modules that may freely invoke each other (e.g.,mutually recursively).</p>", "Keywords": "", "DOI": "10.1145/3632914", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "Jinhua Wu", "Affiliation": "Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Yale University, New Haven, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Yale University, New Haven, USA"}], "References": [{"Title": "CompCertM: CompCert with C-assembly linking and lightweight modular verification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Interaction trees: representing recursive and impure programs in Coq", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A type system for extracting functional specifications from memory-safe imperative programs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Verified compilation of C programs with a nominal memory model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Choice Trees: Representing Nondeterministic, Recursive, and Impure Programs in Coq", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "1770", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "DimSum: A Decentralized Approach to Multi-language Semantics and Verification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "775", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Conditional Contextual Refinement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "1121", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111962040, "Title": "Generative Artificial Intelligence (AI) Technology Adoption Model for Entrepreneurs: Case of ChatGPT", "Abstract": "", "Keywords": "", "DOI": "10.1080/10875301.2023.2300114", "PubYear": 2024, "Volume": "28", "Issue": "2", "JournalId": 25067, "JournalTitle": "Internet Reference Services Quarterly", "ISSN": "1087-5301", "EISSN": "1540-4749", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Multidisciplinary Research Centre for Innovations in SMEs (MrciS), Gisma University of Applied Sciences, Potsdam, Germany;School of Computing and Mathematical Sciences, University of Leicester, Leicester, England;Department of Economics and Business Administration, University of Alcala, Plaza de la Victoria 2, Alcalá de Henares (Madrid), Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Mathematical Sciences, University of Leicester, Leicester, England"}], "References": [{"Title": "ChatGPT and Its Possible Impact on Library Reference Services", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "2", "Page": "121", "JournalTitle": "Internet Reference Services Quarterly"}, {"Title": "Leveraging AI Technologies in Libraries through Experimentation-Driven Frameworks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "4", "Page": "211", "JournalTitle": "Internet Reference Services Quarterly"}, {"Title": "Transforming entrepreneurial research: leveraging library research services and technology innovations for rapid information discovery", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "48", "Issue": "3", "Page": "491", "JournalTitle": "Online Information Review"}, {"Title": "Exploring the role of intrinsic motivation in ChatGPT adoption to support active learning: An extension of the technology acceptance model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "", "Page": "100178", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Navigating Innovation: An Enhanced Business Value Calculator and Its Impact on Library Service Innovations for Entrepreneurs and Businesses", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "1", "Page": "27", "JournalTitle": "Internet Reference Services Quarterly"}]}, {"ArticleId": 111962084, "Title": "Failure mode and effect analysis with ORESTE method under large group probabilistic free double hierarchy hesitant linguistic environment", "Abstract": "Failure Mode and Effect Analysis (FMEA) is a prospective and systematic analytical tool widely used to improve the reliability and safety of various industries. However, the existing FMEA methods have received criticism for their inherent deficiencies that limit their flexibility and effectiveness. To address these issues, we propose a novel FMEA model that combines Probabilistic Free Double Hierarchy Hesitant Linguistic Term Set (PFDHHLTS), Extended Grey Relation Analysis (EGRA), and ORESTE to determine the priorities of Failure Modes (FMs). Firstly, the PFDHHLTSs are proposed to represent experts’ assessments, improving the flexibility and accuracy of experts’ expressions. Secondly, the PFDHHLTS-EGRA is proposed to determine the evaluation matrix and the weights of risk factors in a large group environment. Furthermore, the ORESTE method is extended to the PFDHHLTS environment. Lastly, we verify the effectiveness of our method through a case study on the aluminum electrolysis process. The results demonstrate the effectiveness and flexibility of our method in expressing experts’ assessments and obtaining more accurate and reliable risk priorities.", "Keywords": "", "DOI": "10.1016/j.aei.2024.102353", "PubYear": 2024, "Volume": "59", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha 410083, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha 410083, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Zhengzhou University of Light Industry, Zhengzhou 450002, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha 410083, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha 410083, China"}, {"AuthorId": 6, "Name": "Weihua Gui", "Affiliation": "School of Automation, Central South University, Changsha 410083, China"}], "References": [{"Title": "Assessment of traffic congestion with ORESTE method under double hierarchy hesitant fuzzy linguistic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105864", "JournalTitle": "Applied Soft Computing"}, {"Title": "Failure mode and effects analysis (FMEA) for risk assessment based on interval type-2 fuzzy evidential reasoning method", "Authors": "Jindong Qin; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "106134", "JournalTitle": "Applied Soft Computing"}, {"Title": "Experiential knowledge representation and reasoning based on linguistic Petri nets with application to aluminum electrolysis cell condition identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "529", "Issue": "", "Page": "141", "JournalTitle": "Information Sciences"}, {"Title": "Double hierarchy hesitant fuzzy linguistic entropy-based TODIM approach using evidential theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "223", "JournalTitle": "Information Sciences"}, {"Title": "Improved failure mode and effect analysis with interval-valued intuitionistic fuzzy rough number theory", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103856", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A manufacturing failure mode and effect analysis based on fuzzy and probabilistic risk analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106689", "JournalTitle": "Applied Soft Computing"}, {"Title": "Double hierarchy linguistic term set and its extensions: The state‐of‐the‐art survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "2", "Page": "832", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "An interval 2-Tuple linguistic Fine-<PERSON><PERSON> model for risk analysis based on extended ORESTE method with cumulative prospect theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "", "Page": "40", "JournalTitle": "Information Fusion"}, {"Title": "A fuzzy rough number extended AHP and VIKOR for failure mode and effects analysis under uncertainty", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101454", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Efficiency evaluation with regret-rejoice cross-efficiency DEA models under the distributed linguistic environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "108281", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A new linguistic preference relation-based approach for failure mode and effect analysis with dynamic consensus reaching process", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "610", "Issue": "", "Page": "977", "JournalTitle": "Information Sciences"}, {"Title": "An ELICIT information-based ORESTE method for failure mode and effect analysis considering risk correlation with GRA-DEMATEL", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "93", "Issue": "", "Page": "396", "JournalTitle": "Information Fusion"}, {"Title": "An interval type-2 fuzzy ORESTE method for waste-to-energy plant site selection: A case study in China", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "110092", "JournalTitle": "Applied Soft Computing"}, {"Title": "Root cause analysis for process industry using causal knowledge map under large group environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "", "Page": "102057", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A hybrid HFACS model using DEMATEL-ORESTE method with linguistic Z-number for risk analysis of human error factors in the healthcare system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "235", "Issue": "", "Page": "121237", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": *********, "Title": "Distributed Experimental Design Networks", "Abstract": "<p>As edge computing capabilities increase, model learning deployments in a heterogeneous edge environment have emerged. We consider an experimental design network, as introduced by <PERSON> et al., in which network routing and rate allocation is designed to aid the transfer of data from sensors to heterogeneous learners. We generalize this setting by considering heterogeneous noisy Gaussian sources, incorporating multicast, but also-crucially-distributed algorithms in this setting. From a technical standpoint, we show that, assuming Gaussian sensor sources still yields an continuous DR-submodular experimental design objective. We also propose a distributed Frank-Wolfe algorithm yielding allocations within a 1-1/e factor from the optimal. Numerical evaluations show that our proposed algorithm outperforms competitors w.r.t. both objective maximization and model learning quality.</p>", "Keywords": "", "DOI": "10.1145/3639830.3639837", "PubYear": 2024, "Volume": "51", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "Yuanyuan Li", "Affiliation": "Northeastern University, Boston, MA, USA"}], "References": []}, {"ArticleId": 111962102, "Title": "Solving Infinite-State Games via Acceleration", "Abstract": "<p>Two-player graph games have found numerous applications, most notably in the synthesis of reactive systems from temporal specifications, but also in verification. The relevance of infinite-state systems in these areas has lead to significant attention towards developing techniques for solving infinite-state games.</p><p>We propose novel symbolic semi-algorithms for solving infinite-state games with temporal winning conditions. The novelty of our approach lies in the introduction of an acceleration technique that enhances fixpoint-based game-solving methods and helps to avoid divergence. Classical fixpoint-based algorithms, when applied to infinite-state games, are bound to diverge in many cases, since they iteratively compute the set of states from which one player has a winning strategy. Our proposed approach can lead to convergence in cases where existing algorithms require an infinite number of iterations. This is achieved by acceleration: computing an infinite set of states from which a simpler sub-strategy can be iterated an unbounded number of times in order to win the game. Ours is the first method for solving infinite-state games to employ acceleration. Thanks to this, it is able to outperform state-of-the-art techniques on a range of benchmarks, as evidenced by our evaluation of a prototype implementation.</p>", "Keywords": "", "DOI": "10.1145/3632899", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CISPA Helmholtz Center for Information Security, Saarbrücken, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CISPA Helmholtz Center for Information Security, Saarbrücken, Germany"}], "References": [{"Title": "Modular Primal-Dual Fixpoint Logic Solving for Temporal Verification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "2111", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111962133, "Title": "Total Type Error Localization and Recovery with Holes", "Abstract": "<p>Type systems typically only define the conditions under which an expression is well-typed, leaving ill-typed expressions formally meaningless. This approach is insufficient as the basis for language servers driving modern programming environments, which are expected to recover from simultaneously localized errors and continue to provide a variety of downstream semantic services. This paper addresses this problem, contributing the first comprehensive formal account of total type error localization and recovery: the marked lambda calculus. In particular, we define a gradual type system for expressions with marked errors, which operate as non-empty holes, together with a total procedure for marking arbitrary unmarked expressions. We mechanize the metatheory of the marked lambda calculus in Agda and implement it, scaled up, as the new basis for Hazel, a full-scale live functional programming environment with, uniquely, no meaningless editor states.</p><p>The marked lambda calculus is bidirectionally typed, so localization decisions are systematically predictable based on a local flow of typing information. Constraint-based type inference can bring more distant information to bear in discovering inconsistencies but this notoriously complicates error localization. We approach this problem by deploying constraint solving as a type-hole-filling layer atop this gradual bidirectionally typed core. Errors arising from inconsistent unification constraints are localized exclusively to type and expression holes, i.e. the system identifies unfillable holes using a system of traced provenances, rather than localized in an ad hoc manner to particular expressions. The user can then interactively shift these errors to particular downstream expressions by selecting from suggested partially consistent type hole fillings, which returns control back to the bidirectional system. We implement this type hole inference system in Hazel.</p>", "Keywords": "", "DOI": "10.1145/3632910", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Michigan, Ann Arbor, USA"}], "References": [{"Title": "Automatic syntax error reporting and recovery in parsing expression grammars", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "102373", "JournalTitle": "Science of Computer Programming"}, {"Title": "Live Pattern Matching with Typed Holes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA1", "Page": "609", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Getting into the Flow: Towards Better Type Error Messages for Constraint-Based Type Inference", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "431", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": *********, "Title": "Guided Equality Saturation", "Abstract": "Rewriting is a principled term transformation technique with uses across theorem \nproving and compilation. \nIn theorem proving, each rewrite is a proof step; in compilation, rewrites optimize a program term. \nWhile developing rewrite sequences manually is possible, this process does not \nscale to larger rewrite sequences. Automated rewriting \ntechniques, like greedy simplification or equality saturation, work well \nwithout requiring human input. \nYet, they do not scale to large search spaces, limiting the complexity of tasks where automated rewriting is effective, \nand meaning that just a small increase in term size or rewrite length may result in failure. \n \nThis paper proposes a semi-automatic rewriting technique as a means to scale rewriting by allowing \nhuman insight at key decision points. \nSpecifically, we propose\n guided equality saturation \n that embraces human guidance when fully automated equality saturation does not scale. \nThe rewriting is split into two simpler automatic equality saturation steps: from the original term to a human-provided intermediate\n guide \n , and from the guide to the target. Complex rewriting tasks may require multiple guides, resulting in a sequence of equality saturation steps. \nA guide can be a complete term, or a\n sketch \n containing undefined elements that are instantiated by the equality saturation search. \nSuch sketches may be far more concise than complete terms. \n \nWe demonstrate the generality and effectiveness of guided equality saturation using two case studies. \nFirst, we integrate guided equality saturation in the Lean 4 proof assistant. \nProofs are written in the style of textbook proof sketches, as a series of calculations omitting details and skipping steps. \nThese proofs conclude in less than a second instead of minutes when compared to unguided equality saturation, and can \nfind complex proofs that previously had to be done manually. \nSecond, in the compiler of the Rise array language, where unguided equality saturation fails to perform optimizations within an hour and using 60 GB of memory, guided equality saturation performs the same optimizations with at most 3 guides, within seconds using less than 1 GB memory.", "Keywords": "", "DOI": "10.1145/3632900", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Inria, Nancy, France / ICube lab - Université de Strasbourg - CNRS, Strasbourg, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Amsterdam, Amsterdam, Netherlands / University of Edinburgh, Edinburgh, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Edinburgh, Edinburgh, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Cambridge, Cambridge, United Kingdom / University of Edinburgh, Edinburgh, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Glasgow, Glasgow, United Kingdom"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "TU Berlin, Berlin, Germany / University of Edinburgh, Edinburgh, United Kingdom"}], "References": [{"Title": "Programming language foundations in Agda", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "102440", "JournalTitle": "Science of Computer Programming"}, {"Title": "Schedule Synthesis for Halide Pipelines on GPUs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}, {"Title": "egg: Fast and extensible equality saturation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Efficient automatic scheduling of imaging and vision pipelines for the GPU", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Mechanical Mathematicians", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "4", "Page": "80", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 111962178, "Title": "Die Datenschutz-Vorsorge (DS-V): Systematisierung eines neuen Instruments für das Datenschutzrecht", "Abstract": "Zusammenfassung</h3> <p>Das neu vorgeschlagene Instrument der Datenschutz-Vorsorge soll unvorhersehbare personenbezogene Datenverarbeitungen rechtskonform ermöglichen. Der vorliegende Beitrag systematisiert dieses neu vorgeschlagene Instrument. </p>", "Keywords": "", "DOI": "10.1007/s11623-023-1878-6", "PubYear": 2024, "Volume": "48", "Issue": "1", "JournalId": 6375, "JournalTitle": "Datenschutz und Datensicherheit - DuD", "ISSN": "1614-0702", "EISSN": "1862-2607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer SIT und ATHENE, Dortmund, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer SIT und ATHENE, Dortmund, Deutschland"}], "References": []}, {"ArticleId": 111962214, "Title": "Data Augmentation for Low-Level Vision: CutBlur and Mixture-of-Augmentation", "Abstract": "<p>Data augmentation (DA) is an effective way to improve the performance of deep networks. Unfortunately, current methods are mostly developed for high-level vision tasks (eg, image classification) and few are studied for low-level (eg, image restoration). In this paper, we provide a comprehensive analysis of the existing DAs in the frequency domain. We find that the methods that largely manipulate the spatial information can hinder the image restoration process and hurt the performance. Based on our analyses, we propose CutBlur and mixture-of-augmentation (MoA). CutBlur cuts a low-quality patch and pastes it to the corresponding high-quality image region, or vice versa. The key intuition is to provide enough DA effect while keeping the pixel distribution intact. This characteristic of CutBlur enables a model to learn not only “how” but also “where” to reconstruct an image. Eventually, the model understands “how much” to restore given pixels, which allows it to generalize better to unseen data distributions. We further improve the restoration performance by MoA that incorporates the curated list of DAs. We demonstrate the effectiveness of our methods by conducting extensive experiments on several low-level vision tasks on both single or a mixture of distortion tasks. Our results show that CutBlur and MoA consistently and significantly improve the performance especially when the model size is big and the data is collected under real-world environments. Our code is available at https://github.com/clovaai/cutblur .</p>", "Keywords": "Data augmentation; Image restoration; Image super-resolution; Image denoising; JPEG artifact removal; Multiple degradations restoration; Generalization", "DOI": "10.1007/s11263-023-01970-z", "PubYear": 2024, "Volume": "132", "Issue": "6", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NAVER WEBTOON AI, Seongnam, Republic of Korea; Department of Artificial Intelligence, Ajou University, Suwon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Artificial Intelligence, UNIST, Ulsan, Republic of Korea; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Artificial Intelligence, Ajou University, Suwon, Republic of Korea"}], "References": []}, {"ArticleId": 111962235, "Title": "When graph convolution meets double attention: online privacy disclosure detection with multi-label text classification", "Abstract": "With the rise of Web 2.0 platforms such as online social media, people’s private information, such as their location, occupation and even family information, is often inadvertently disclosed through online discussions. Therefore, it is important to detect such unwanted privacy disclosures to help alert people affected and the online platform. In this paper, privacy disclosure detection is modeled as a multi-label text classification (MLTC) problem, and a new privacy disclosure detection model is proposed to construct an MLTC classifier for detecting online privacy disclosures. This classifier takes an online post as the input and outputs multiple labels, each reflecting a possible privacy disclosure. The proposed presentation method combines three different sources of information, the input text itself, the label-to-text correlation and the label-to-label correlation. A double-attention mechanism is used to combine the first two sources of information, and a graph convolutional network is employed to extract the third source of information that is then used to help fuse features extracted from the first two sources of information. Our extensive experimental results, obtained on a public dataset of privacy-disclosing posts on Twitter, demonstrated that our proposed privacy disclosure detection method significantly and consistently outperformed other state-of-the-art methods in terms of all key performance indicators.", "Keywords": "Privacy disclosure detection; User generated content (UGC); Online social media; Graph convolutional network (GCN); Multi-label text classification (MLTC)", "DOI": "10.1007/s10618-023-00992-y", "PubYear": 2024, "Volume": "38", "Issue": "3", "JournalId": 3928, "JournalTitle": "Data Mining and Knowledge Discovery", "ISSN": "1384-5810", "EISSN": "1573-756X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Shanghai Jiao Tong University, Minhang District, Shanghai, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Shanghai Jiao Tong University, Minhang District, Shanghai, China; Corresponding author."}, {"AuthorId": 3, "Name": "Weidong Qiu", "Affiliation": "School of Cyber Science and Engineering, Shanghai Jiao Tong University, Minhang District, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Engineering, Shanghai Jiao Tong University, Minhang District, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Cyber Security for Society (iCSS) and School of Computing, University of Kent, Kent, Canterbury, UK; Corresponding author."}], "References": [{"Title": "A recommendation approach for user privacy preferences in the fitness domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "3", "Page": "513", "JournalTitle": "User Modeling and User-Adapted Interaction"}, {"Title": "Fine-Grained Privacy Detection with Graph-Regularized Hierarchical Attentive Representation Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": 111962237, "Title": "Direction based method for representing and querying fuzzy regions", "Abstract": "<p>Uncertainty management for geometric data is a fundamental issue for spatial databases, image databases, and spatial data systems, such as geographic information systems. Currently, spatial database systems can only handle geographical applications that interact with discrete spatial objects. In reality, many spatial objects do not have uniform interiors and sharp limits but rather have interiors and bounds that are partial, uncertain, or fuzzy. However, authors have numerous debates over the definition of the fuzzy region, fuzzy points, and lines. A method for modeling 2D fuzzy regions is introduced based on the proposed direction concept. The method, membership function, and fuzzy spatial set operations are formally defined and implemented using SQL Server 2014. The proposed method is compared with available methods of the grid (bitmap), vector-map, and extended triangulated irregular networks (ETIN) in terms of memory complexity, time complexity, and the accuracy of data storage (making noise), and its applicability. This proposed method outperforms the memory complexity of grid, vector-map, and ETIN methods, the time complexity of vector-map and ETIN methods, and the accuracy of the grid and vector-map methods. The image processing issues are not the subjects of this study.</p>", "Keywords": "Spatial fuzziness; Fuzzy spatial objects; Spatial database; Fuzzy set operations; Directional phenomena", "DOI": "10.1007/s11042-023-17121-y", "PubYear": 2024, "Volume": "83", "Issue": "21", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer and Information Technology, Foulad Institute of Technology, Isfahan, Iran; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}], "References": []}, {"ArticleId": 111962252, "Title": "A novel fuzzy-prospect theory approach for hydrogen fuel cell component supplier selection for automotive industry", "Abstract": "The burgeoning automotive sector is an energy-intensive industrial sector, amounting to 94 M tons of oil equivalents (MTOE). Owing to more significant environmental concerns, there is a dire need to transition the energy-intensive mobility space to sustainable mobility. In this direction, hydrogen is being researched as a potential alternative clean fuel for the automotive sector; however, in the context of developing countries, hydrogen fuel cell technology is at a very nascent stage. Various policies and research projects are underway to realize the shift, but they must be underpinned through a robust supply chain network. The selection of an optimal supplier is key to the development of a robust hydrogen supply chain network. To this end, this work proposes the selection of an appropriate supplier for supplying critical components for different research and development projects of hydrogen fuel cells. This is still a lacuna in the context of developing nations. To aid in addressing this issue, a novel TODIM (TOmada de Decisao Interativa Multicriterio) decision-making framework has been proposed to select the optimal supplier to supply critical hydrogen fuel cell components. Within the proposed approach, the linguistic evaluations provided by experts are modeled using a more generalized linear Diophantine linguistic uncertain fuzzy set that circumvents the algorithmic restrictions on the falsity and truth grades. The linguistic assessments from the experts were aggregated using the linear Diophantine uncertain linguistic power Einstein weighted averaging (LDULPEWA) operator. The power Einstein operations take care of the interaction between falsity and truth grades, which is important for the accuracy of the results. Finally, the TODIM approach was used to deduce the final ranking results. The proposed method considers the specialists&#x27; willingness to take risks during the decision-making process. Such a decision-making framework is still a lacuna in the domain of decision science. The supposed results were tested for their stability through sensitivity analysis. A past case study was solved using the proposed approach to validate the rankings. A comparison of the suggested strategy with the existing prominent decision-making frameworks was also carried out to clearly highlight the strengths and weaknesses of the proposed approach.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.123142", "PubYear": 2024, "Volume": "246", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Institute of Technology, Silchar, Assam-788010, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, <PERSON><PERSON><PERSON> (SSN) College of Engineering, Kalavakkam, Tamil Nadu -603110, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Institute of Technology, Silchar, Assam-788010, India;Corresponding author"}], "References": [{"Title": "Developing of a Novel Integrated MCDM MULTIMOOSRAL Approach for Supplier Selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "145", "JournalTitle": "Informatica"}, {"Title": "A probabilistic hesitant fuzzy Choquet integral-based TODIM method for multi-attribute group decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116266", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An extended multi-criteria group decision-making method with psychological factors and bidirectional influence relation for emergency medical supplier selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "202", "Issue": "", "Page": "117414", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sustainable supplier selection using an intuitionistic and interval-valued fuzzy MCDM approach based on cumulative prospect theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "626", "Issue": "", "Page": "710", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 111962337, "Title": "C3S-TTP: A Trusted Third Party for Configuration Security in TOSCA-Based Cloud Services", "Abstract": "<p>The large-scale deployment of cloud composite services distributed over heterogeneous environments poses new challenges in terms of security management. In particular, the migration of their resources is facilitated by recent advances in the area of virtualization techniques. This contributes to increase the dynamics of their configuration, and may induce vulnerabilities that could compromise the security of cloud resources, or even of the whole service. In addition, cloud providers may be reluctant to share precise information regarding the configuration of their infrastructures with cloud tenants that build and deploy cloud composite services. This makes the assessment of vulnerabilities difficult to be performed with only a partial view on the overall configuration. We therefore propose in this article an inter-cloud trusted third-party approach, called C3S-TTP, for supporting secure configurations in cloud composite services, more specifically during the migration of their resources. We describe the considered architecture, its main building blocks and their interactions based on an extended version of the TOSCA orchestration language. The trusted third party is capable to perform a precise and exhaustive vulnerability assessment, without requiring the cloud provider and the cloud tenant to share critical configuration information between each other. After designing and formalizing this third party solution, we perform large series of experiments based on a proof-of-concept prototype in order to quantify its benefits and limits.</p>", "Keywords": "Security management; Composite services; Cloud security; Orchestration language; Trusted third party; Resources migration", "DOI": "10.1007/s10922-023-09792-7", "PubYear": 2024, "Volume": "32", "Issue": "1", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "RESIST Research Team, LORIA / INRIA Nancy Grand Est, University of Lorraine, CNRS, Villers-les-Nancy, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "RESIST Research Team, LORIA / INRIA Nancy Grand Est, University of Lorraine, CNRS, Villers-les-Nancy, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "RESIST Research Team, LORIA / INRIA Nancy Grand Est, University of Lorraine, CNRS, Villers-les-Nancy, France"}], "References": [{"Title": "Cloud bursting galaxy: federated identity and access management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "1", "JournalTitle": "Bioinformatics"}, {"Title": "A Semi-Automatic and Trustworthy Scheme for Continuous Cloud Service Certification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "30", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 111962396, "Title": "Allocation and scheduling of deposition paths in a layer for multi-robot coordinated wire and arc additive manufacturing of large-scale parts", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2023.2300680", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, People’s Republic of China;State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, People’s Republic of China"}, {"AuthorId": 3, "Name": "Minglang Li", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People's Republic of China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Advanced Welding and Joining, Harbin Institute of Technology, Harbin, People's Republic of China"}], "References": [{"Title": "Plasma transfer arc additive manufacturing of 17-4 PH: assessment of defects", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "7-8", "Page": "2301", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 111962403, "Title": "Neural lumped parameter differential equations with application in friction-stir processing", "Abstract": "<p>Lumped parameter methods aim to simplify the evolution of spatially-extended or continuous physical systems to that of a “lumped” element representative of the physical scales of the modeled system. For systems where the definition of a lumped element or its associated physics may be unknown, modeling tasks may be restricted to full-fidelity physics simulations. In this work, we consider data-driven modeling tasks with limited point-wise measurements of otherwise continuous systems. We build upon the notion of the Universal Differential Equation (UDE) to construct data-driven models for reducing dynamics to that of a lumped parameter and inferring its properties. The flexibility of UDEs allow for composing various known physical priors suitable for application-specific modeling tasks, including lumped parameter methods. The motivating example for this work is the plunge and dwell stages for friction-stir welding; specifically, (i) mapping power input into the tool to a point-measurement of temperature and (ii) using this learned mapping for process control.</p>", "Keywords": "Machine learning; Lumped parameter method; Neural ordinary differential equations; Friction stir processing; Data-driven modeling", "DOI": "10.1007/s10845-023-02271-5", "PubYear": 2025, "Volume": "36", "Issue": "2", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, USA"}], "References": []}, {"ArticleId": 111962445, "Title": "Two new classes of conjugate gradient method based on logistic mapping", "Abstract": "Following the standard methods proposed by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (P-R), in this work we introduce two new non-linear conjugate gradient methods for solving unconstraint optimization problem, our new methods based on P-R. Standard method (P-R) have performance well in numerical result but does not satisfy global convergency condition. In this paper we modified double attractive and powerful parameters that have better performance and good numerical result than P-R method, also each of our robust method can satisfies the descent condition and global convergency condition by using wolf condition. More over the second method modified by logistic mapping form, the main novelty is their numerical results and demonstrate performance well with compare to a standard method.", "Keywords": "conjugate gradient method;descent condition;global convergence condition;logistic mapping;unconstrained optimization", "DOI": "10.12928/telkomnika.v22i1.25264", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Duhok"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Zakho"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Duhok"}, {"AuthorId": 4, "Name": "Salah Gazi Shareef", "Affiliation": "University of Zakho"}], "References": []}, {"ArticleId": 111962618, "Title": "Group decision-making method with trust-based weight and reliability parameters", "Abstract": "Recent studies on group decision-making (GDM) have highlighted the significance of the reliability parameter, which is considered to be the second critical parameter, after weight, of an information source. In practice, trust among decision makers (DMs) also should receive attention because of the interaction among DMs. Thus, in this study, we introduce trust as a third parameter and focus on differentiating among these three information parameters. First, we apply the generalized combination (GC) rule to extract and fuse individual information. Then, we implement the procedures of consensus measure and information selection based on similarity. Next, we adjust the selected inconsistent information using trust-based weight and reliability parameters to facilitate group consensus. During the consensus reaching process, the weight and reliability parameters are dynamically and differentially determined. Finally, the proposed method is summarized as a GDM framework. We introduce a case simulation study to verify the feasibility of the proposed method and conduct a numerical comparison and comparative discussion to demonstrate that the proposed method provides a fresh perspective on developing GDM with consensus. In particular, this framework is suitable for addressing GDM problems involving uncertainty in high-dimensional information with wide discrepancy.", "Keywords": "", "DOI": "10.1016/j.ins.2024.120089", "PubYear": 2024, "Volume": "662", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Management College, Ocean University of China, Qingdao 266100, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business and Tourism Management, Yunnan University, Kunming 650500, PR China;Corresponding author"}], "References": [{"Title": "Trust based group decision making in environments with extreme uncertainty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105168", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Discovering knowledge combinations in multidimensional collaboration network: A method based on trust link prediction and knowledge similarity", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105701", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Group decision making in manufacturing systems: An approach using spatial preference information and indifference zone", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "109", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Particle swarm optimization for trust relationship based social network group decision making under a probabilistic linguistic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "105999", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Managing noncooperative behaviors in large-scale group decision-making: Integration of independent and supervised consensus-reaching models", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "531", "Issue": "", "Page": "119", "JournalTitle": "Information Sciences"}, {"Title": "A trust-similarity analysis-based clustering method for large-scale group decision-making under a social network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Xu-<PERSON> Lin", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "13", "JournalTitle": "Information Fusion"}, {"Title": "Generalized combination rule for evidential reasoning approach and <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> theory of evidence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "1201", "JournalTitle": "Information Sciences"}, {"Title": "A maximum self-esteem degree based feedback mechanism for group consensus reaching with the distributed linguistic trust propagation in social network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "80", "JournalTitle": "Information Fusion"}, {"Title": "A consensus model to manage the non-cooperative behaviors of individuals in uncertain group decision making problems during the COVID-19 outbreak", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "106879", "JournalTitle": "Applied Soft Computing"}, {"Title": "A signed network analysis-based Consensus Reaching Process in Group Decision Making", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106926", "JournalTitle": "Applied Soft Computing"}, {"Title": "Trust and behavior analysis-based fusion method for heterogeneous multiple attribute group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "152", "Issue": "", "Page": "106992", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A knowledge coverage-based trust propagation for recommendation mechanism in social network group decision making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107005", "JournalTitle": "Applied Soft Computing"}, {"Title": "Group decision making with hesitant fuzzy linguistic preference relations based on modified extent measurement", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114235", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Consistency and trust relationship-driven social network group decision-making method with probabilistic linguistic information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107170", "JournalTitle": "Applied Soft Computing"}, {"Title": "Knowledge structure-based consensus-reaching method for large-scale multiattribute group decision-making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "219", "Issue": "", "Page": "106885", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A sentiment analysis-based expert weight determination method for large-scale group decision-making driven by social media data", "Authors": "<PERSON><PERSON> Wan; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115629", "JournalTitle": "Expert Systems with Applications"}, {"Title": "SF-GRA method based on cumulative prospect theory for multiple attribute group decision making and its application to emergency supplies supplier selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "104679", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 111962676, "Title": "Digital Literacy Inequities, Higher Education, and the New Digital Divide", "Abstract": "", "Keywords": "", "DOI": "10.20533/ijicr.2042.4655.2023.0144", "PubYear": 2023, "Volume": "14", "Issue": "1", "JournalId": 34574, "JournalTitle": "International Journal of Intelligent Computing Research", "ISSN": "", "EISSN": "2042-4655", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111962686, "Title": "Deadlock-free production using <PERSON><PERSON><PERSON><PERSON>Sha<PERSON> and preset methods in predictive scheduling for multiagent controlled flexible manufacturing systems", "Abstract": "Deadlocks can lead to indefinite delays and total production interruption, resulting in industrial losses. The control function is responsible for maintaining deadlock-free production schedules in manufacturing systems that permit product parallelism and resource sharing. Although some predictive scheduling algorithms can set optimal production schedules with parallel tasks for multiple products, they cannot detect or prevent deadlocks. This study proposes two approaches for minimizing the number of deadlocks in predictive production schedules defined by a flexible job-shop scheduling (FJS) algorithm. These approaches were evaluated in a virtual environment operated as a multi-agent controlled flexible manufacturing system (FMS). The first approach adopts the Dempster–Shafer evidence theory with a set acceptability value for the belief function . Agents convert information from different sources in a production system into decision outcomes that indicate the best control case from a case base to drive deadlock-free production. The second approach employs a decision table for product routing in the FMS and three corrective presetting methods to avoid deadlocks in production scheduling. A leaderless consensus is adopted in the proposed approaches to decision processes, ensuring that the shared and parallel activities executed by the multiple agents of the system remain promptly synchronized. In total, 32 FJS schedules containing 10–47 events were tested to evaluate the efficiency of the proposed approach. This paper presents the results of the experiments, an overall evaluation of the approaches, and data on the time complexity of the proposed algorithms. The second approach successfully avoids deadlocks in the FMS; however, both approaches can be adapted to other environments with order-driven production and predictive schedules.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.111234", "PubYear": 2024, "Volume": "152", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Santa Catarina State University (UDESC), São Bento do Sul, 89283-081, Santa <PERSON>, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Technology - Parana (UTFPR), Curitiba, 80230-901, Paraná, Brazil"}], "References": [{"Title": "Dynamic scheduling for flexible job shop with new job insertions by deep reinforcement learning", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "106208", "JournalTitle": "Applied Soft Computing"}, {"Title": "Stochastic models for performance analysis of multistate flexible manufacturing cells", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "94", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Petri-net-based dynamic scheduling of flexible manufacturing system via deep reinforcement learning with graph convolutional network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "1", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Comparison of schedule generation schemes for designing dispatching rules with genetic programming in the unrelated machines environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106637", "JournalTitle": "Applied Soft Computing"}, {"Title": "An effective backtracking search algorithm for multi-objective flexible job shop scheduling considering new job arrivals and energy consumption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106863", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "An effective MCTS-based algorithm for minimizing makespan in dynamic flexible job shop scheduling problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "155", "Issue": "", "Page": "107211", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": *********, "Title": "Explainable fraud detection of financial statement data driven by two-layer knowledge graph", "Abstract": "In modern economic activities, financial statement fraud will seriously mislead the economic decisions of investors and regulators, and will lead to huge investment losses even corporate bankruptcies. Although the powerful abilities have been gained by current machine learning methods in financial statement fraud detection problems, the explainability and the ability of extracting fraudulent patterns are still very scarce. In this study, an explainable F inancial S tatement data F raud D etection method is proposed by introducing a T wo- L ayer K nowledge G raph (FSFD-TLKG) and a fraudulent pattern mining strategy on two-layer knowledge graph. Wherein, a two-layer knowledge graph comprises a semantic layer and a syntactic layer. Concretely, the subordination relationships among financial variables are represented in the semantic layer, and their articulation relationships are represented in the syntactic layer. Moreover, an explainable approach is designed to extract financial statement fraudulent patterns for credible fraud assertion. Experimental results show that, FSFD-TLKG can effectively extract explainable financial statement fraudulent patterns and obtain better detection accuracy than almost all traditional machine learning and deep learning methods except an unexplainable method: Extreme Gradient Boosting (XGBoost). Even for XGBoost, the explainable financial statement fraudulent patterns extracted by our method still can further improve its performance. Clearly, FSFD-TLKG gains the optimal practical performance which is much better than existing methods.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.123126", "PubYear": 2024, "Volume": "246", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Siqi Cai", "Affiliation": "School of Artificial Intelligence and Computer Science, Jiangnan University, Wuxi 214122, China;Jiangsu Key Laboratory of Media Design and Software Technology, Jiangnan University, Wuxi 214122, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence and Computer Science, Jiangnan University, Wuxi 214122, China;Jiangsu Key Laboratory of Media Design and Software Technology, Jiangnan University, Wuxi 214122, China;Corresponding author at: School of Artificial Intelligence and Computer Science, Jiangnan University, Wuxi 214122, China"}], "References": [{"Title": "Interpretable multiclass classification by MDL-based rule lists", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1372", "JournalTitle": "Information Sciences"}, {"Title": "Knowledge graph construction and application in geosciences: A review", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "161", "Issue": "", "Page": "105082", "JournalTitle": "Computers & Geosciences"}]}, {"ArticleId": 111962788, "Title": "Partial observation learning-based task offloading and spectrum allocation in UAV collaborative edge computing", "Abstract": "Capable of flexibly supporting diverse applications and providing computation services, the Mobile Edge Computing (MEC)-assisted Unmanned Aerial Vehicle (UAV) network is emerging as an innovational paradigm. In this paradigm, the heterogeneous resources of the network, including computing and communication resources, should be allocated properly to reduce computation and communication latency as well as energy consumption. However, most existing works solely focus on the optimization issues with global information, which is generally difficult to obtain in real-world scenarios. In this paper, fully considering the incomplete information resulting from diverse types of tasks, we study the joint task offloading and spectrum allocation problem in UAV network, where free UAV nodes serve as helpers for cooperative computation. The objective is to jointly optimize offloading mode, collaboration pairing, and channel allocation to minimize the weighted network cost. To achieve the purpose with only partial observation, an extensive-form game is introduced to reformulate the problem, and a regret learning-based scheme is proposed to achieve the equilibrium solution. With retrospective improvement property and information set concept, the designed algorithm is capable of combating incomplete information and obtaining more precise allocation patterns for diverse tasks. Numerical results show that our proposed algorithm outperforms the benchmarks across various settings.", "Keywords": "UAV networks; Edge computing; Task offloading; Spectrum allocation; Partial observation; Regret learning", "DOI": "10.1016/j.dcan.2024.01.001", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing Normal University, Beijing, 100875, China;Guangdong Key Laboratory of Intelligent Information Processing and Shenzhen Key Laboratory of Media Security, Shenzhen, 518060, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Beijing Normal University, Beijing, 100875, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Engineering, Beijing University of Posts and Telecommunications, Beijing, 100876, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Beijing University of Posts and Telecommunications, Beijing, 100876, China"}], "References": []}, {"ArticleId": 111962829, "Title": "Responsive Nd-MOF nanorods based near-infrared photoelectrochemical cytosensor and real-time monitoring HClO releasing from cells and tumor", "Abstract": "Designing functionalized MOFs with specific reactivity and NIR photoelectrochemical and fluorescent response is critical for developing biosensing systems. Herein, SCy7, a conjugate macrocycle ligand with a specific fluorescence responsive group to HClO , was designed and synthesized. Responsive Nd-MOF nanorods were successfully prepared by using SCy7 as the organic ligand and Nd <sup>3+</sup> as the metal central ion. Nd-MOF nanorods show good absorption characteristics in the wavelength range of 200–1200 nm with a band gap of 0.89 eV. Nd-MOF nanorods were employed as a photoactive material to construct a near-infrared photoelectrochemical cytosensor for MCF-7 cells by integrated with an aptamer. The photocurrent variation exhibits a good linear response to MCF-7 cells in the concentration range of 50–1.0 × 10 <sup>7</sup> cells mL<sup>−1</sup>, and a comparatively low detection limit of 18 cells mL<sup>−1</sup> (S/N = 3). The NIR PEC cytosensor was also employed for monitoring endogenous HClO released from MCF-7 cells as being stimulated by PMA . HClO is an electron acceptor that can convert the anodic photocurrent into cathodic photocurrent, thus achieving monitoring HClO through the conversion of photocurrent polarity. Furthermore, Nd-MOF was coated with F127 to improve its hydrophilicity and biocompatibility, and to provide a Nd-MOF@F127 with a fluorescence emission peak at 830 nm. Nd-MOF@F127 show specific fluorescence response to HClO, and thus was successfully used in imaging HClO variation in cells and breast cancer bearing mice.", "Keywords": "", "DOI": "10.1016/j.snb.2024.135291", "PubYear": 2024, "Volume": "405", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Vascular Surgery, Renmin Hospital of Wuhan University, Wuhan 430060, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Neurosurgery, Renmin Hospital of Wuhan University, Wuhan 430060, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China;Corresponding authors"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China;Corresponding authors"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Catalysis and Energy Materials Chemistry of Ministry of Education & Key Laboratory of Analytical Chemistry of the State Ethnic Affairs Commission, School of Chemistry and Materials Science, South-Central Minzu University, Wuhan 430074, China;Corresponding authors"}], "References": [{"Title": "Construction of a multi-signal near-infrared fluorescent probe for sensing of hypochlorite concentration fluctuation in living animals", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "324", "Issue": "", "Page": "128732", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A ratiometric near-infrared fluorescent probe with a large emission peak shift for sensing and imaging hypochlorous acid", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "343", "Issue": "", "Page": "130063", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Visualization of biothiols and HClO in cancer therapy via a multi-responsive fluorescent probe", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "347", "Issue": "", "Page": "130620", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Bioimaging of hypochlorous acid using a near-infrared fluorescent probe derived from rhodamine dye with a large Stokes shift", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "364", "Issue": "", "Page": "131868", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "An ultra-sensitive ESIPT fluorescent probe for distinguishing cancerous cells and diagnosing APAP-induced liver injury via HClO fluctuation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "386", "Issue": "", "Page": "133749", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 111962850, "Title": "A broad learning model guided by global and local receptive causal features for online incremental machinery fault diagnosis", "Abstract": "Intelligent fault diagnosis (IFD) plays a significant function in ensuring the reliable operation of mechanical equipment. However, most existing IFD methods are trained in batch learning way and the feature extraction process is unexplainable and it is challenging to satisfy the complex diagnosis requirements. Thus, this paper proposes a causal broad learning model (CBLM) guided by global and multi-scale local causal features for incremental machinery fault diagnosis. Firstly, rich global and multi-scale local causal features are extracted for training CBLM. Then, incremental learning capability is developed to update model by expanding or modifying the original weights, considerably reducing the time consumption and greatly improving the computational efficiency. When the initial model has inadequate nodes for feature learning , CBLM performs structural incremental learning by adding extra nodes to improve the diagnostic performance. As new samples with various fault degrees are entered, sample incremental learning is built to rapidly update based on previous model without retraining. Finally, two experimental results on bearings indicate that CBLM improves the testing accuracy of the initial model by 0.13% to 52.22% and 1.95% to 57.64%, respectively, and remains above 97.22% and 99.77% for the updated models. The training time consumption is reduced by 25.06 s to 133.0 s, 20.78 s and 141.9 s, respectively, and the subsequent models update time are only about 12 s and 10 s. Consequently, the proposed CBLM is an effective online IFD method because it is obviously superior to the existing IFD methods in regards to time consumption and diagnosis accuracy.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.123124", "PubYear": 2024, "Volume": "246", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Yanshan University, Qinhuangdao 066004, Hebei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering and Mechanics, Ningbo University, Ningbo 315211, Zhejiang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Automation, Anhui University, Hefei 230601, Anhui, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Shi", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinhuangdao 066004, Hebei, China"}], "References": [{"Title": "A new hybrid deep signal processing approach for bearing fault diagnosis using vibration signals", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "542", "JournalTitle": "Neurocomputing"}, {"Title": "Domain generalization in rotating machinery fault diagnostics using deep neural networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "403", "Issue": "", "Page": "409", "JournalTitle": "Neurocomputing"}, {"Title": "Bearing fault diagnosis base on multi-scale CNN and LSTM model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "4", "Page": "971", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Explaining the black-box model: A survey of local interpretation methods for deep neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "419", "Issue": "", "Page": "168", "JournalTitle": "Neurocomputing"}, {"Title": "Intelligent fault diagnosis of rotating machinery based on continuous wavelet transform-local binary convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106796", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Broad learning system for semi-supervised learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "444", "Issue": "", "Page": "38", "JournalTitle": "Neurocomputing"}, {"Title": "Hierarchical diagnosis of bearing faults using branch convolutional neural network considering noise interference and variable working conditions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "230", "Issue": "", "Page": "107386", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep multiple auto-encoder with attention mechanism network: A dynamic domain adaptation method for rotary machine fault diagnosis under different working conditions", "Authors": "Shengkang Yang; Xianguang Kong; <PERSON><PERSON>", "PubYear": 2022, "Volume": "249", "Issue": "", "Page": "108639", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Unsupervised fault diagnosis of wind turbine bearing via a deep residual deformable convolution network based on subdomain adaptation under time-varying speeds", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "118", "Issue": "", "Page": "105656", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Automated broad transfer learning for cross-domain fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "", "Page": "27", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Cross-domain meta learning fault diagnosis based on multi-scale dilated convolution and adaptive relation module", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "261", "Issue": "", "Page": "110175", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Towards trustworthy rotating machinery fault diagnosis via attention uncertainty in transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "70", "Issue": "", "Page": "186", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Semi-supervised adversarial discriminative learning approach for intelligent fault diagnosis of wind turbine", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "648", "Issue": "", "Page": "119496", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 111962904, "Title": "Brain organoids and organoid intelligence from ethical, legal, and social points of view", "Abstract": "<p> Human brain organoids, aka cerebral organoids or earlier “mini-brains”, are 3D cellular models that recapitulate aspects of the developing human brain. They show tremendous promise for advancing our understanding of neurodevelopment and neurological disorders. However, the unprecedented ability to model human brain development and function in vitro also raises complex ethical, legal, and social challenges. Organoid Intelligence (OI) describes the ongoing movement to combine such organoids with Artificial Intelligence to establish basic forms of memory and learning. This article discusses key issues regarding the scientific status and prospects of brain organoids and OI, conceptualizations of consciousness and the mind–brain relationship, ethical and legal dimensions, including moral status, human–animal chimeras, informed consent, and governance matters, such as oversight and regulation. A balanced framework is needed to allow vital research while addressing public perceptions and ethical concerns. Interdisciplinary perspectives and proactive engagement among scientists, ethicists, policymakers, and the public can enable responsible translational pathways for organoid technology. A thoughtful, proactive governance framework might be needed to ensure ethically responsible progress in this promising field. </p>", "Keywords": "Microphysiological systems; Cognition; Intelligence; Social Sciences; learning & memory; Consciousness", "DOI": "10.3389/frai.2023.1307613", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Alternatives to Animal Testing (CAAT), Health and Whiting School of Engineering, Johns Hopkins University, United States; CAAT-Europe, University of Konstanz, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Center for Alternatives to Animal Testing (CAAT), Health and Whiting School of Engineering, Johns Hopkins University, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Alternatives to Animal Testing (CAAT), Health and Whiting School of Engineering, Johns Hopkins University, United States"}], "References": []}, {"ArticleId": 111962910, "Title": "Cluster target tracking based on multi-sensor continuous-discrete PMBM filter", "Abstract": "Coordination effectively enhances the detection capabilities of sensors towards cluster targets. However, due to the dense distribution of cluster members and the cooperative interaction among them, continuous tracking of these targets becomes challenging. To address these issues, we propose a novel multi-sensor continuous-discrete Poisson multi-Bernoulli mixture filter, abbreviated as MS-CDPMBM-CT filter. Based on the cooperative interaction rules, we use the multivariate stochastic differential equation (SDE) to model the dynamics of cluster members in continuous time. To adapt to the Bayesian recursive process, we derive a Gaussian state transition form of the above model in the state space. In addition, by utilizing a cluster structure undirected graph and a multi-dimensional assignment (MDA) algorithm, we improve the multi-sensor continuous-discrete PMBM filter, which can iteratively estimate the posterior probability density of the target based on the continuous time state model and discrete multi-sensor measurements. During prediction, the cluster structure is exploited to provide cooperative interaction inputs that constrain member motions. During the update, the partitioning problem of multi-sensor measurements is transformed into the MDA problem for updating the global hypotheses. Simulation experiments show that the proposed method can better adapt to the cluster interaction behavior, distinguish the members within the cluster, and achieve high filtering accuracy.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.123121", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate College, Air Force Engineering University, Xi’an 710051, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Air and Missile Defence College, Air Force Engineering University, Xi’an 710051, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Air and Missile Defence College, Air Force Engineering University, Xi’an 710051, PR China"}], "References": []}, {"ArticleId": 111962927, "Title": "Data on hematological parameters and generalized severe periodontitis in the United States", "Abstract": "This article describes an ensemble of datasets used to understand the relationship between generalized severe periodontitis and hematological parameters. This dataset combines public periodontal examination data, hematological parameters data, and demographic data from the National Center for Health Statistics from 2009 to 2014. The stage of periodontitis was identified by attachment loss conducted by dental examiners, who were dentists (D.D.S./ D.M.D.) licensed in at least one U.S. state, while matching current classification criteria from the American Academy of Periodontology and the European Federation of Periodontology. Based on the NHANES database, information on age, gender, education level (&lt; 9th grade, 9–11th grade, high school, college, graduate), race/ethnicity (Mexican American, Hispanic, non-Hispanic White, non-Hispanic Black, and other races), PIR (poverty income ratio) were acquired from the demographic data. Hematological parameters (including HB, HCT, mean cell volume, mean cell hemoglobin, mean cell hemoglobin concentration, red cell distribution width, platelet count, mean platelet volume, and red blood cell count) and glucose data had been obtained from laboratory data. Smoking data had been obtained from questionnaire data.", "Keywords": "Periodontitis ; Public health ; Statistics ; Retrospective study", "DOI": "10.1016/j.dib.2023.110010", "PubYear": 2024, "Volume": "52", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Oral Diseases Research of Anhui Province, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China;Department of Dental, Tongling Traditional Chinese Medicine Hospital, Taipinghu Road, Tongling 244000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Oral Diseases Research of Anhui Province, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Dental Implantology, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Oral Diseases Research of Anhui Province, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Oral and Maxillofacial Surgery, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Oral Diseases Research of Anhui Province, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China;Department of Dental Implantology, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China;Corresponding author at: Department of Dental Implantology, College & Hospital of Stomatology, Anhui Medical University, Key Laboratory of Oral Diseases Research of Anhui Province, Hefei 230032, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Oral Diseases Research of Anhui Province, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China;Department of Oral and Maxillofacial Surgery, College & Hospital of Stomatology, Anhui Medical University, Hefei 230032, China;Corresponding author at: Department of Maxillofacial surgery, College & Hospital of Stomatology, Anhui Medical University, Key Laboratory of Oral Diseases Research of Anhui Province, Hefei 230032, China"}], "References": []}, {"ArticleId": 111962929, "Title": "Enhanced industrial text classification <i>via</i> hyper variational graph-guided global context integration", "Abstract": "Background <p>Joint local context that is primarily processed by pre-trained models has emerged as a prevailing technique for text classification. Nevertheless, there are relatively few classification applications on small sample of industrial text datasets.</p> Methods <p>In this study, an approach of employing global enhanced context representation of the pre-trained model to classify industrial domain text is proposed. To achieve the application of the proposed technique, we extract primary text representations and local context information as embeddings by leveraging the BERT pre-trained model. Moreover, we create a text information entropy matrix through statistical computation, which fuses features to construct the matrix. Subsequently, we adopt BERT embedding and hyper variational graph to guide the updating of the existing text information entropy matrix. This process is subjected to iteration three times. It produces a hypergraph primary text representation that includes global context information. Additionally, we feed the primary BERT text feature representation into capsule networks for purification and expansion as well. Finally, the above two representations are fused to obtain the final text representation and apply it to text classification through feature fusion module.</p> Results <p>The effectiveness of this method is validated through experiments on multiple datasets. Specifically, on the CHIP-CTC dataset, it achieves an accuracy of 86.82% and an F1 score of 82.87%. On the CLUEEmotion2020 dataset, the proposed model obtains an accuracy of 61.22% and an F1 score of 51.56%. On the N15News dataset, the accuracy and F1 score are 72.21% and 69.06% respectively. Furthermore, when applied to an industrial patent dataset, the model produced promising results with an accuracy of 91.84% and F1 score of 79.71%. All four datasets are significantly improved by using the proposed model compared to the baselines. The evaluation result of the four dataset indicates that our proposed model effectively solves the classification problem.</p>", "Keywords": "Capsule network;Hyper variational graph;Industrial applications;Text information entropy matrix", "DOI": "10.7717/peerj-cs.1788", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Electrical Engineering, Shanghai University of Engineering Science, Shanghai,  Songjiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Electrical Engineering, Shanghai University of Engineering Science, Shanghai,  Songjiang, China"}], "References": [{"Title": "Document structure model for survey generation using neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "4", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Graph convolution machine for context-aware recommender system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "6", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "BGNN: Behavior-aware graph neural network for heterogeneous session-based recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "5", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Aspect-level sentiment analysis based on semantic heterogeneous graph convolutional network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "6", "Page": "1", "JournalTitle": "Frontiers of Computer Science"}]}, {"ArticleId": 111962993, "Title": "Shoggoth: A Formal Foundation for Strategic Rewriting", "Abstract": "<p> Rewriting is a versatile and powerful technique used in many domains. Strategic rewriting allows programmers to control the application of rewrite rules by composing individual rewrite rules into complex rewrite strategies. These strategies are semantically complex, as they may be nondeterministic, they may raise errors that trigger backtracking, and they may not terminate. </p><p>Given such semantic complexity, it is necessary to establish a formal understanding of rewrite strategies and to enable reasoning about them in order to answer questions like: How do we know that a rewrite strategy terminates? How do we know that a rewrite strategy does not fail because we compose two incompatible rewrites? How do we know that a desired property holds after applying a rewrite strategy?</p><p> In this paper, we introduce Shoggoth: a formal foundation for understanding, analysing and reasoning about strategic rewriting that is capable of answering these questions. We provide a denotational semantics of System S, a core language for strategic rewriting, and prove its equivalence to our big-step operational semantics, which extends existing work by explicitly accounting for divergence. We further define a location-based weakest precondition calculus to enable formal reasoning about rewriting strategies, and we prove this calculus sound with respect to the denotational semantics. We show how this calculus can be used in practice to reason about properties of rewriting strategies, including termination, that they are well-composed, and that desired postconditions hold. The semantics and calculus are formalised in Isabelle/HOL and all proofs are mechanised. </p>", "Keywords": "", "DOI": "10.1145/3633211", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Edinburgh, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Edinburgh, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Edinburgh, UK / UNSW, Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Australian National University, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Edinburgh, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "TU Berlin, Germany / University of Edinburgh, UK"}], "References": [{"Title": "Weakest preconditions in fibrations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "4", "Page": "472", "JournalTitle": "Mathematical Structures in Computer Science"}, {"Title": "Achieving High Performance the Functional Way", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "3", "Page": "89", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": *********, "Title": "Exploring the impact of human-robot interaction on workers' mental stress in collaborative assembly tasks", "Abstract": "Advances in robotics have contributed to the prevalence of human-robot collaboration (HRC). However, working and interacting with collaborative robots in close proximity can be psychologically stressful. Therefore, understanding the impacts of human-robot interaction (HRI) on mental stress is crucial for enhancing workplace well-being. To this end, this study investigated how the HRI factors – presence, complexity, and modality – affect the psychological stress of workers. We employed both the NASA-Task Load Index for subjective assessment and physiological metrics including galvanic skin responses , electromyography , and heart rate for objective evaluation. An experimental setup was implemented in which human operators worked together with a collaborative robot on Lego assembly tasks, using different interaction paradigms including pressing buttons, showing hand gestures, and giving verbal commands. The results revealed that the introduction of interactions during HRC helped reduce mental stress and that complex interactions resulted in higher mental stress than simple interactions. Meanwhile, using hand gestures led to significantly higher mental stress than pressing buttons and verbal commands. The findings provided practical insights for mitigating mental stress in the workplace and promoting wellness in the era of HRC.", "Keywords": "Human-robot collaboration;Interaction modality;Mental stress;Physiological responses", "DOI": "10.1016/j.apergo.2024.104224", "PubYear": 2024, "Volume": "116", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Edward <PERSON>. Fitts Department of Industrial and Systems Engineering, North Carolina State University, USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Edward <PERSON>. Fitts Department of Industrial and Systems Engineering, North Carolina State University, USA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Edward <PERSON>. Fitts Department of Industrial and Systems Engineering, North Carolina State University, USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Edward <PERSON>. Fitts Department of Industrial and Systems Engineering, North Carolina State University, USA."}, {"AuthorId": 5, "Name": "Liwei Qing", "Affiliation": "Edward <PERSON>. Fitts Department of Industrial and Systems Engineering, North Carolina State University, USA."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Edward <PERSON>. <PERSON>tts Department of Industrial and Systems Engineering, North Carolina State University, USA"}], "References": [{"Title": "Communication in Human-Robot Interaction", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "4", "Page": "279", "JournalTitle": "Current Robotics Reports"}, {"Title": "Let’s Work Together: A Meta-Analysis on Robot Design Features That Enable Successful Human–Robot Interaction at Work", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "6", "Page": "1027", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}, {"Title": "Perceived safety in physical human–robot interaction—A survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "151", "Issue": "", "Page": "104047", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Using accelerometry and heart rate data for real-time monitoring of soldiers’ stress in a dynamic military virtual reality scenario", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "17", "Page": "24739", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "User Experience and Physiological Response in Human-Robot Collaboration: A Preliminary Investigation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "106", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}]}, {"ArticleId": 111963063, "Title": "Subsumptions of Algebraic Rewrite Rules", "Abstract": "What does it mean for an algebraic rewrite rule to subsume another rule (that may then be called a subrule)? We view subsumptions as rule morphisms such that the simultaneous application of a rule and a subrule (i.e. the application of a subsumption morphism) yields the same result as a single application of the subsuming rule. Simultaneous applications of categories of rules are obtained by Global Coherent Transformations and illustrated on graphs in the DPO approach. Other approaches are possible since these transformations are formulated in an abstract Rewriting Environment, and such environments exist for various approaches to Algebraic Rewriting, including DPO, SqPO and PBPO.", "Keywords": "", "DOI": "10.4204/EPTCS.397.2", "PubYear": 2023, "Volume": "397", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Tour", "Affiliation": "Univ. Grenoble Alpes, CNRS, Grenoble INP, LIG"}], "References": [{"Title": "<PERSON><PERSON><PERSON> rewriting of attributed graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "848", "Issue": "", "Page": "106", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 111963133, "Title": "Disentanglement with Futures, State, and Interaction", "Abstract": "Recent work has proposed a memory property for parallel programs, \ncalled disentanglement, and showed that it is pervasive in a variety \nof programs, written in different languages, ranging from C/C++ to \nParallel ML, and showed that it can be exploited to improve \nthe performance of parallel functional programs. \nAll existing work on disentanglement, however, considers the \n\"fork/join\" model for parallelism and does not apply to \"futures\", \nthe more powerful approach to parallelism. \nThis is not surprising: \nfork/join parallel programs exhibit a reasonably strict dependency \nstructure (e.g., series-parallel DAGs), which disentanglement exploits. \nIn contrast, with futures, parallel computations become first-class \nvalues of the language, and thus can be created, and passed between \nfunctions calls or stored in memory, just like other ordinary values, \nresulting in complex dependency structures, especially in the \npresence of mutable state. \nFor example, parallel programs with futures can have deadlocks, which \nis impossible with fork-join parallelism.\n In this paper, we are interested in the theoretical question of \nwhether disentanglement may be extended beyond fork/join parallelism, \nand specifically to futures. \nWe consider a functional language with futures, Input/Output (I/O), \nand mutable state (references) and show that a broad range of programs \nwritten in this language are disentangled. \nWe start by formalizing disentanglement for futures and proving that \npurely functional programs written in this language are disentangled. \nWe then generalize this result in three directions. \nFirst, we consider state (effects) and prove that stateful programs \nare disentangled if they are race free. \nSecond, we show that race freedom is sufficient but not a necessary \ncondition and non-deterministic programs, e.g. those that use atomic \nread-modify-operations and some non-deterministic combinators, may \nalso be disentangled. \nThird, we prove that disentangled task-parallel programs written with \nfutures are free of deadlocks, which arise due to interactions between \nstate and the rich dependencies that can be expressed with futures. \nTaken together, these results show that disentanglement generalizes to \nparallel programs with futures and, thus, the benefits of disentanglement \nmay go well beyond fork-join parallelism.", "Keywords": "", "DOI": "10.1145/3632895", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Illinois Institute of Technology, Chicago, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}], "References": [{"Title": "Efficient Parallel Functional Programming with Effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1558", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111963160, "Title": "Reinforcement learning approaches for the stochastic discrete lot-sizing problem on parallel machines", "Abstract": "This paper addresses the stochastic discrete lot-sizing problem on parallel machines, which is a computationally challenging problem also for relatively small instances. We propose two heuristics to deal with it by leveraging reinforcement learning . In particular, we propose a technique based on approximate value iteration around post-decision state variables and one based on multi-agent reinforcement learning. We compare these two approaches with other reinforcement learning methods and more classical solution techniques, showing their effectiveness in addressing realistic size instances.", "Keywords": "", "DOI": "10.1016/j.eswa.2023.123036", "PubYear": 2024, "Volume": "246", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Escola Politécnica da Universidade de São Paulo, Av. Prof. <PERSON>, 380, São Paulo, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dipartimento di Scienze Matematiche Politecnico di Torino, Corso Duca degli Abruzzi 24, 10129 Torino, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Escola Politécnica da Universidade de São Paulo, Av. Prof. <PERSON>, 380, São Paulo, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Dipartimento di Scienze Matematiche Politecnico di Torino, Corso Duca degli Abruzzi 24, 10129 Torino, Italy"}], "References": [{"Title": "Production scheduling for the reconfigurable modular pharmaceutical manufacturing processes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "107346", "JournalTitle": "Computers & Chemical Engineering"}]}, {"ArticleId": 111963172, "Title": "SimuQ: A Framework for Programming Quantum Hamiltonian Simulation with Analog Compilation", "Abstract": "<p>Quantum Hamiltonian simulation, which simulates the evolution of quantum systems and probes quantum phenomena, is one of the most promising applications of quantum computing. Recent experimental results suggest that Hamiltonian-oriented analog quantum simulation would be advantageous over circuit-oriented digital quantum simulation in the Noisy Intermediate-Scale Quantum (NISQ) machine era. However, programming analog quantum simulators is much more challenging due to the lack of a unified interface between hardware and software. In this paper, we design and implement SimuQ, the first framework for quantum Hamiltonian simulation that supports Hamiltonian programming and pulse-level compilation to heterogeneous analog quantum simulators. Specifically, in SimuQ, front-end users specify the target quantum system with Hamiltonian Modeling Language, and the Hamiltonian-level programmability of analog quantum simulators is specified through a new abstraction called the abstract analog instruction set (AAIS) and programmed in AAIS Specification Language by hardware providers. Through a solver-based compilation, SimuQ generates executable pulse schedules for real devices to simulate the evolution of desired quantum systems, which is demonstrated on superconducting (IBM), neutral-atom (QuEra), and trapped-ion (IonQ) quantum devices. Moreover, we demonstrate the advantages of exposing the Hamiltonian-level programmability of devices with native operations or interaction-based gates and establish a small benchmark of quantum simulation to evaluate SimuQ's compiler with the above analog quantum simulators.</p>", "Keywords": "", "DOI": "10.1145/3632923", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Maryland, College Park, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Maryland, College Park, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Maryland, College Park, USA"}], "References": [{"Title": "A verified optimizer for Quantum circuits", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "MISTIQS: An open-source software for performing quantum dynamics simulations on quantum computers", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "", "Page": "100696", "JournalTitle": "SoftwareX"}, {"Title": "ArQTiC: A Full-stack Software Package for Simulating Materials on Quantum Computers", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Quantum Computing"}, {"Title": "OpenQASM 3: A Broader and Deeper Quantum Assembly Language", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Quantum Computing"}]}, {"ArticleId": 111963180, "Title": "Monotonicity and the Precision of Program Analysis", "Abstract": "It is widely known that the precision of a program analyzer is closely related to intensional program properties, \nnamely, properties concerning how the program is written. \nThis explains, for instance, the interest in code obfuscation techniques, namely, tools \nexplicitly designed to degrade the results of program analysis by operating syntactic program transformations. \nLess is known about a possible relation between what the program extensionally computes, \nnamely, its input-output relation, and the precision of a program analyzer. \nIn this paper we explore this potential connection in an effort to isolate program fragments \nthat can be precisely analyzed by abstract interpretation, namely, \nprograms for which there exists a complete abstract interpretation. \nIn the field of static inference of numeric invariants, this happens for programs, \nor parts of programs, that manifest a monotone (either non-decreasing or non-increasing) behavior. \nWe first formalize the notion of program monotonicity with respect to a given input and a set of numerical variables of interest. \nA sound proof system is then introduced with judgments specifying whether a \nprogram is monotone relatively to a set of variables and a set of inputs. \nThe interest in monotonicity is justified because we prove that the family \nof monotone programs admits a complete abstract interpretation over \na specific class of non-trivial numerical abstractions and inputs. \nThis class includes all non-relational abstract domains that refine interval analysis \n(i.e., at least as precise as the intervals abstraction) and that satisfy a topological convexity hypothesis.", "Keywords": "", "DOI": "10.1145/3632897", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Inria - ENS - Université PSL, Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Verona, Verona, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Arizona, Tucson, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Inria - ENS - Université PSL, Paris, France"}], "References": [{"Title": "Abstract extensionality: on the properties of incomplete abstract interpretations", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A simple differentiable programming language", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Deciding ω-regular properties on linear recurrence sequences", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Learning metamorphic malware signatures from samples", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "3", "Page": "167", "JournalTitle": "Journal of Computer Virology and Hacking Techniques"}, {"Title": "Introduction to Neural Network Verification", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "1–2", "Page": "1", "JournalTitle": "Foundations and Trends® in Programming Languages"}, {"Title": "Partial (In)Completeness in abstract interpretation: limiting the imprecision in program analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Interval universal approximation for neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Correctness and Incorrectness Program Logic", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "70", "Issue": "2", "Page": "", "JournalTitle": "Journal of the ACM"}]}, {"ArticleId": 111963264, "Title": "Trillium: Higher-Order Concurrent and Distributed Separation Logic for Intensional Refinement", "Abstract": "Expressive state-of-the-art separation logics rely on step-indexing to model semantically complex features \nand to support modular reasoning about imperative higher-order concurrent and distributed programs. Step- \nindexing comes, however, with an inherent cost: it restricts the adequacy theorem of program logics to a fairly \nsimple class of safety properties. \nIn this paper, we explore if and how intensional refinement is a viable methodology for strengthening \nhigher-order concurrent (and distributed) separation logic to prove non-trivial safety and liveness properties. \nSpecifically, we introduce Trillium, a language-agnostic separation logic framework for showing intensional \nrefinement relations between traces of a program and a model. We instantiate Trillium with a concurrent \nlanguage and develop Fairis, a concurrent separation logic, that we use to show liveness properties of concurrent \nprograms under fair scheduling assumptions through a fair liveness-preserving refinement of a model. We also \ninstantiate Trillium with a distributed language and obtain an extension of Aneris, a distributed separation \nlogic, which we use to show refinement relations between distributed systems and TLA+ models.", "Keywords": "", "DOI": "10.1145/3632851", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 3, "Name": "Léo <PERSON>", "Affiliation": "MPI-SWS, Kaiserslautern, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}], "References": [{"Title": "Igloo: soundly linking compositional refinement and separation logic for distributed system verification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Fully abstract from static to gradual", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Theorems for free from separation logic specifications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Simuliris: a separation logic framework for verifying concurrent program optimizations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Le temps des cerises: efficient temporal stack safety on capability machines using directed capabilities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Purity of an ST monad: full abstraction by semantically typed back-translation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111963269, "Title": "STGAFormer: Spatial–temporal Gated Attention Transformer based Graph Neural Network for traffic flow forecasting", "Abstract": "Traffic flow prediction is a critical component of Intelligent Transportation Systems (ITS). However, the dynamic temporal variations in traffic flow, especially in potential occurrence of unexpected incidents, pose challenges to the prediction of traffic flow. This paper proposes a Spatial–temporal Gated Attention Transformer (STGAFormer) model based Graph Neural Network(GNN), leveraging the encoder architecture of the transformer. The gated temporal self-attention in the model, a novel module, can improve the model’s ability to make long-term predictions and handle sudden traffic incidents by enhancing the extraction of both local and global temporal features. Additionally, this paper proposes a distance spatial self-attention module to extract spatial features , which employs thresholding to selectively identify crucial features from both nearby and distant regions. In this way, the model’s ability to assimilate critical spatial information is promoted. Moreover, our model incorporates a diverse range of inputs, including traffic flow attributes, periodicity, proximity adjacency matrix , and adaptive adjacency matrix. Experiments from four real datasets demonstrate that STGAFormer achieves state-of-the-art performance, especially the MAE value of the PeMS08 dataset experiment is improved by 3.82%. This method offers valuable insights and robust support for future transportation planning.", "Keywords": "", "DOI": "10.1016/j.inffus.2024.102228", "PubYear": 2024, "Volume": "105", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, University of Electronic Science and Technology of China, Chengdu 611731, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Chengdu University of Information Technology, Chengdu 610225, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer Science, Chengdu University, Chengdu 610106, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Chengdu University of Information Technology, Chengdu 610225, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Chengdu University of Information Technology, Chengdu 610225, China"}], "References": [{"Title": "Multi-model ensemble with rich spatial information for object detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "99", "Issue": "", "Page": "107098", "JournalTitle": "Pattern Recognition"}, {"Title": "Urban flow prediction from spatiotemporal data using machine learning: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "1", "JournalTitle": "Information Fusion"}, {"Title": "A fast human action recognition network based on spatio-temporal features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "441", "Issue": "", "Page": "350", "JournalTitle": "Neurocomputing"}, {"Title": "Spatial-temporal graph neural network for traffic forecasting: An overview and open research issues", "Authors": "Khac<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "3", "Page": "2763", "JournalTitle": "Applied Intelligence"}, {"Title": "Dynamic graph convolutional networks based on spatiotemporal data embedding for traffic flow forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "250", "Issue": "", "Page": "109028", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Graph neural network for traffic forecasting: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "117921", "JournalTitle": "Expert Systems with Applications"}, {"Title": "TYRE: A dynamic graph model for traffic prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119311", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hybrid deep learning models for traffic prediction in large-scale road networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "93", "JournalTitle": "Information Fusion"}, {"Title": "Long sequence time-series forecasting with deep learning: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "97", "Issue": "", "Page": "101819", "JournalTitle": "Information Fusion"}, {"Title": "Generic Dynamic Graph Convolutional Network for traffic flow forecasting", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101946", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 111963283, "Title": "Edge-sorter: A hardware sorting engine for area & power constrained edge computing devices", "Abstract": "In recent years, hardware sorters have been an attracted topic for researchers. Since hardware sorters play a crucial role in embedded systems , several attempts have been made to efficiently design and implement these sorters. Previous state-of-the-art hardware sorters are not suitable for embedded edge computing devices because they (1) consume high power, (2) occupy high area, (3) work for limited data-width numbers, (4) require many memory resources, and (5) finally, their architecture is not scalable with the number of input records. This paper proposes a hardware sorter for edge devices with limited hardware resources. The proposed hardware sorter, called Edge-Sorter, processes 4 bits of input records at each clock cycle. Edge-Sorter utilizes the unary processing in its main processing core. Edge-Sorter has valuable attributes compared to previous state-of-the-art techniques, including low power consumption , low area occupation, sorting numbers without storing their indices, sorting numbers with arbitrary data-width, and scalable with the number of input records. The proposed approach is evaluated and compared with previous state-of-the-art techniques with two different implementation and synthesis environments: Xilinx Vivado FPGA-based and Synopsys Design Compiler 45-nm ASIC-based. The Synthesis results of both environments indicate that both Edge-Sorter techniques reduces area and power consumption on average by 80% and 90%, respectively compared to previous techniques.", "Keywords": "", "DOI": "10.1016/j.micpro.2024.105006", "PubYear": 2024, "Volume": "105", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, Iran University of Science and Technology, Tehran, Iran;Department of Computer Science, Soran University, Kurdistan Region, Iraq;Corresponding author at: School of Computer Engineering, Iran University of Science and Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, Iran University of Science and Technology, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Engineering, Iran University of Science and Technology, Tehran, Iran"}], "References": [{"Title": "Methods in detection of median filtering in digital images: a survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "28", "Page": "43945", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 111963296, "Title": "Judicial intelligent assistant system: Extracting events from Chinese divorce cases to detect disputes for the judge", "Abstract": "In the formal procedure of Chinese civil cases, the textual materials provided by different parties describe the development process of the cases. It is a difficult but necessary task to extract the key information for the cases from these textual materials and to clarify the dispute focus of related parties. Currently, officers read the materials manually and use methods, such as keyword searching and regular matching, to get the target information. These approaches are time‐consuming and heavily depend on prior knowledge and the carefulness of the officers. To assist the officers in enhancing working efficiency and accuracy, we conduct a case study of detecting disputes from Chinese divorce cases based on proposing a Two‐Round‐Labeling (TRL) event extracting technique in this article. We implement the Judicial Intelligent Assistant (JIA) system according to the proposed approach to (1) automatically extract focus events from divorce case materials, (2) align events by identifying co‐reference among them, and (3) detect conflicts among events brought by the plaintiff and the defendant. With the JIA system, it is convenient for judges to determine the disputed issues in Chinese divorce cases. Experimental results demonstrate that the proposed approach and system can obtain the focus of Chinese divorce cases and detect conflicts more effectively and efficiently compared with the existing method.", "Keywords": "conflict detection;event extraction;expert systems;information retrieval;judicial procedure", "DOI": "10.1111/exsy.13540", "PubYear": 2024, "Volume": "41", "Issue": "7", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Software Institute Nanjing University  Nanjing China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Software Institute Nanjing University  Nanjing China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Software Institute Nanjing University  Nanjing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Ge", "Affiliation": "State Key Laboratory for Novel Software Technology, Software Institute Nanjing University  Nanjing China;State Key Laboratory of Networking and Switching Technology Beijing University of Posts and Telecommunications  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Software Institute Nanjing University  Nanjing China"}], "References": [{"Title": "Automatic keyphrase extraction: a survey and trends", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "2", "Page": "391", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "A tree-based neural network model for biomedical event trigger detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "175", "JournalTitle": "Information Sciences"}, {"Title": "Phrase2Vec: Phrase embedding based on parsing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "517", "Issue": "", "Page": "100", "JournalTitle": "Information Sciences"}, {"Title": "Legal Information Retrieval systems: State-of-the-art and open issues", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "106", "Issue": "", "Page": "101967", "JournalTitle": "Information Systems"}, {"Title": "MFEE: a multi-word lexical feature enhancement framework for Chinese geological hazard event extraction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 111963342, "Title": "Fall incidence prediction system for elderly people based on IoT and classification techniques", "Abstract": "Health monitoring systems based on the internet of things (IoT) improve patient well-being and reduce mortality risks. Machine learning techniques are most helpful in early fall prediction and detection. In this paper, fall prediction analysis and decision-making are done with existing benchmark clinical records. Classification techniques are incorporated to track the consistency and precision of data acquired by the IoT-based remote health monitoring for elderly people, especially those who are living alone. This work undertakes two approaches to early predicting a patient’s acute illness. The first approach has analyzed the existing benchmark patient activity data with different features. This approach builds the classification model for fall incidence with the help of machine learning models. In second approach, we collect real-time sensor data such as blood pressure and heart rate from IoT sensor gadgets which are transmitted to the prediction model for early prediction. Experimental results prove that the random forest (RF) classifiers and XGBoost provides the maximum accuracy.", "Keywords": "e-health monitoring;internet of things;machine learning;prediction model;ThingsSpeak", "DOI": "10.12928/telkomnika.v22i1.25248", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SRM Institute of Science and Technology"}], "References": []}, {"ArticleId": 111963454, "Title": "BER-performance evaluation for 5G-PD-NOMA system in multipath communication channels", "Abstract": "In this paper, a bit error rate (BER) performance is evaluated for power domain non-orthogonal multiple access (PD-NOMA) system. The performance test is examined considering; additive white gaussian noise (AWGN), flat and long-term evolution (LTE)-multipath selective channels such as; pedestrian channel model (EPA), vehicular channel model (EVA), and typical urban model (ETU). The proposed system considering two user equipment’s (UE1 and UE2) with a single base station (BS) for downlink channel. Two different powers were allocated to each user according to their positions from the BS. The superposition coding process is performed with successive-interference-cancelation (SIC) at both transmitter/receiver sides respectively to distinguish the desired received signal. The performance evaluations proves that the degree of power allocated to each user plays a significant rule in frequency selection environments. UE1 has a better BER performance than UE 2 by about 9 dB in EPA, 6 dB in EVA, and 7 dB in ETU environments respectively at a BER of 10 -3 . However, in flat fading environment, the distance for each user represents a significant factor affecting the BER performance, where, UE 2 has a better BER performance than UE 1 by about 10 dB at a BER of 10 -3 .", "Keywords": "5G;bit error rate;non-orthogonal multiple access;power domain non-orthogonal multiple access;successive-interference-cancelation", "DOI": "10.12928/telkomnika.v22i1.24955", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Middle Technical University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Al-Farabi College University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Middle Technical University"}], "References": []}, {"ArticleId": *********, "Title": "Design of intelligent financial sharing platform driven by consensus mechanism under mobile edge computing and accounting transformation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDMB.2024.********", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 15764, "JournalTitle": "International Journal of Data Mining and Bioinformatics", "ISSN": "1748-5673", "EISSN": "1748-5681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Theory of Timely Remote Estimation and Application toSituational Awareness", "Abstract": "<p>Brief Biography: <PERSON><PERSON><PERSON> is a PhD candidate at the Electrical and Computer Engineering Department of Auburn University. She joined the Real-time Networking lab in Fall 2018. Her PhD supervisor is Prof. <PERSON>. She completed her M.Sc. in Electrical Engineering from Auburn University in 2022. She received her B.Sc. degree in Electrical & Electronic Engineering from Bangladesh University of Engineering and Technology in 2017. Her research interests include information freshness, remote estimation, robotic control, and wireless communication networks. She has worked on designing novel sampling and scheduling policies for the remote estimation of diffusion processes involving single and multiple sources. In addition, she has worked on designing low-complexity scheduling algorithms for improving situational awareness in safety-critical systems. She received the IEEE/IFIP WiOpt Best Paper Award in 2019, one of her IEEE/ACM ToN journals was recommended for fast-tracked review, and she had an invited paper at IEEE SPAWC 2021. She is also one of the authors of a book chapter published by CUP. She is a graduate student member of IEEE and ACM.</p>", "Keywords": "", "DOI": "10.1145/3639830.3639850", "PubYear": 2024, "Volume": "51", "Issue": "3", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Auburn University"}], "References": []}, {"ArticleId": 111963545, "Title": "How does ESG disclosure promote technological innovation? Moderating effects based on product market competition", "Abstract": "Purpose The primary objective of this study is to unveil the relationships that interconnect ESG and three pillars disclosures with technological innovation while also investigating the moderating impact of product market competition. The paper seeks to identify the underlying mechanisms that facilitate technological innovation in sustainable management. Design/methodology/approach Using data from 8,738 Chinese firms from 2011 to 2019, this study employs quantitative analysis to examine the relationship between ESG disclosure and technological innovation and the moderating effect. Moreover, this study explores the heterogeneous impacts while considering factors such as property rights and firm size. Findings The findings reveal a positive correlation between ESG disclosure and technological innovation. The study also investigates the moderating role of product market competition and finds that increasing competition mitigates the positive effects of ESG disclosure on technological innovation. Additionally, the conclusions reveal that the relationship between ESG and three pillars disclosures and technological innovation, as well as the moderating role of product market competition, exhibits inconsistency across firms with different property rights and sizes. Originality/value This study offers a clear understanding of the relationship between ESG disclosures and technological innovation, and how it varies across businesses of different sizes and ownership structures. It also provides fresh perspectives on the influence of product market competition on this relationship, with implications for strategy development in corporations.", "Keywords": "", "DOI": "10.1108/K-08-2023-1398", "PubYear": 2025, "Volume": "54", "Issue": "4", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "R&D investment, ESG performance and green innovation performance: evidence from China", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "3", "Page": "737", "JournalTitle": "Kybernetes"}, {"Title": "Intellectual capital, corporate social responsibility and sustainable competitive performance of small and medium-sized enterprises: mediating effects of organizational innovation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "52", "Issue": "10", "Page": "4014", "JournalTitle": "Kybernetes"}]}, {"ArticleId": 111963553, "Title": "Outlier item detection in bundle recommendation via the attention mechanism", "Abstract": "Bundle recommendation offers users more holistic insights by recommending multiple compatible items at once. However, the intricate correlations between items, varied user preferences, and the pronounced data sparsity in combinations present significant challenges for bundle recommendation algorithms. Furthermore, current bundle recommendation methods fail to identify mismatched items within a given set, a process termed as “outlier item detection”. These outlier items are those with the weakest correlations within a bundle. Identifying them can aid users in refining their item combinations. While the correlation among items can predict the detection of such outliers, the adaptability of combinations might not be adequately responsive to shifts in individual items during the learning phase. This limitation can hinder the algorithm’s performance. To tackle these challenges, we introduce an encoder–decoder architecture tailored for outlier item detection. The encoder learns potential item correlations through a self-attention mechanism. Concurrently, the decoder garners efficient inference frameworks by directly assessing item anomalies. We have validated the efficacy and efficiency of our proposed algorithm using real-world datasets.", "Keywords": "Bundle recommendation; Outlier item detection; Attention mechanism; Decoder encoder", "DOI": "10.1016/j.hcc.2024.100200", "PubYear": 2024, "Volume": "4", "Issue": "3", "JournalId": 85789, "JournalTitle": "High-Confidence Computing", "ISSN": "2667-2952", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangxi Key Laboratory of Trusted Software, Guilin University of Electronic Technology, Guilin 541004, China;School of Information Engineering, Suqian University, Suqian 223800, China"}], "References": [{"Title": "Fashion Meets Computer Vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 111963606, "Title": "On Learning Polynomial Recursive Programs", "Abstract": "<p>We introduce the class of P-finite automata. These are a generalisation of weighted automata, in which the weights of transitions can depend polynomially on the length of the input word. P-finite automata can also be viewed as simple tail-recursive programs in which the arguments of recursive calls can non-linearly refer to a variable that counts the number of recursive calls. The nomenclature is motivated by the fact that over a unary alphabet P-finite automata compute so-called P-finite sequences, that is, sequences that satisfy a linear recurrence with polynomial coefficients. Our main result shows that P-finite automata can be learned in polynomial time in <PERSON><PERSON><PERSON>'s MAT exact learning model. This generalises the classical results that deterministic finite automata and weighted automata over a field are respectively polynomial-time learnable in the MAT model.</p>", "Keywords": "", "DOI": "10.1145/3632876", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-Margin<PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CNRS - IRIF - Université Paris Cité, Paris, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}], "References": []}, {"ArticleId": 111963691, "Title": "X-ray image-based pneumonia detection and classification using deep learning", "Abstract": "<p>Pneumonia is a dangerous lung disease that has affected millions of people worldwide. Several people have died as a result of incorrect pneumonia diagnosis and treatment. This has necessitated the urgent need for quick detection and classification methods of pneumonia detection for efficient treatment and quick recovery of affected persons. However, the causes of pneumonia are not accurately diagnosed using outmoded methods which use chest X-rays. This paper therefore presents a method for identifying and classifying chest X-ray images of normal and pneumonia-infected persons. The designed deep learning model first preprocesses the X-ray images to extract useful features, then segments them using a threshold segmentation technique, detects normal and pneumonia infected persons from X-ray images using the YOLOv3 detector, and classifies them as normal and with pneumonia using Support vector machine (SVM) and softmax. The suggested model was trained and evaluated using a dataset of chest X-ray images. The results show that the overall accuracy, precision, recall, and F1-score are all 99%. The findings show that deep features produced accurate and consistent characteristics for pneumonia detection. Using the presented approach, radiologists can assess pneumonia patients and deliver a rapid diagnosis.</p>", "Keywords": "Pneumonia; Deep learning; CNN; Image processing; Detection; Classification", "DOI": "10.1007/s11042-023-17965-4", "PubYear": 2024, "Volume": "83", "Issue": "21", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Debark University, Debark, Ethiopia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical/Electronics and Computer Engineering, Afe Babalola University, Ado- Ekiti, Nigeria; Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences, Chennai, India; Corresponding author."}, {"AuthorId": 3, "Name": "Aleka Melese Ayalew", "Affiliation": "Department of Information Technology, University of Gondar, Gondar, Ethiopia"}], "References": [{"Title": "Impact of fully connected layers on performance of convolutional neural networks for image classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "112", "JournalTitle": "Neurocomputing"}, {"Title": "Dropout vs. batch normalization: an empirical study of their impact to deep learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "12777", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "EDS pooling layer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "103923", "JournalTitle": "Image and Vision Computing"}, {"Title": "Synthesis of COVID-19 chest X-rays using unpaired image-to-image translation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "23", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Medical image analysis based on deep learning approach", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "16", "Page": "24365", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Identification and classification of pneumonia disease using a deep learning-based intelligent computational framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "20", "Page": "14473", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Pneumonia detection in chest X-ray images using compound scaled deep learning model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "62", "Issue": "3-4", "Page": "397", "JournalTitle": "Automatika"}, {"Title": "Deep Learning Model of Image Classification Using Machine Learning", "Authors": "Qing <PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Advances in Multimedia"}, {"Title": "A Deep Learning based model for the Detection of Pneumonia from Chest X-Ray Images using VGG-16 and Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "357", "JournalTitle": "Procedia Computer Science"}, {"Title": "X-Ray image-based COVID-19 detection using deep learning", "Authors": "Aleka Mel<PERSON>ew; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "28", "Page": "44507", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 111963714, "Title": "Extremum seeking control for the trajectory tracking of a skid steering vehicle via averaged sub-gradient integral sliding-mode theory", "Abstract": "This study introduces the development of a novel extremum-seeking controller designed to stabilize the tracking trajectory of a fully actuated four-wheeled skid steering vehicle. The controller design employs a class of averaged sub-gradient optimization strategies for a suitable convex function that depends on the enforced tracking trajectory achieved through an integral sliding mode implementation. The application of this controller necessitates a nonlinear coordinate transformation of the skid steering vehicle dynamics, resulting in a perturbed multi-input–multi-output linear system. Using the proposed controller, a multidimensional sliding surface remains remarkably stable despite tracking errors. The surface design defines a manifold that aligns with the sub-gradient of a convex function, contingent upon the integral of the tracking error. This connection establishes a link between the extremum-seeking problem and the sliding mode control design. Employing the cascade design strategy, commonly referred to as backstepping, enables the calculation of torques for the four wheels of the mobile vehicle using a sequence of auxiliary sliding surfaces, each with its corresponding complementary function for every stage. The dynamic formulation for the wheel torques is derived from the integral sliding mode methodology. A series of numerical evaluations are conducted to assess the controller’s effectiveness in terms of functional performance. A comparison between the control action based on sliding mode and the traditional state feedback formulation validates the efficacy of the proposed design. This comparison demonstrates advancements in robustness in the face of modeling uncertainties and optimization of the performance function, which are theoretically supported by the suggested control design. Implementing the proposed controller in an experimental platform for a wheeled skid steering vehicle reaffirms the control design methodology presented in this study. This platform includes the application of an image processing algorithm to determine the non-inertial position of the autonomous vehicle using integral sliding mode control. This information completes the necessary data for implementing the on-site version of the proposed controller.", "Keywords": "", "DOI": "10.1016/j.robot.2023.104609", "PubYear": 2024, "Volume": "174", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Advanced Materials for the Sustainable Manufacturing, Tecnologico de Monterrey, General <PERSON> 2514, Guadalajara, 45210, Jalisco, Mexico;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Automatic Control Department, Centro de Investigacion y Estudios Avanzados del Instituto Politecnico Nacional, Mexico City, 07360, Mexico"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Advanced Materials for the Sustainable Manufacturing, Tecnologico de Monterrey, General <PERSON> 2514, Guadalajara, 45210, Jalisco, Mexico"}], "References": []}, {"ArticleId": 111963730, "Title": "Anti-allergic drug screening in single living mast cells by means of a novel surface plasmon resonance imaging system", "Abstract": "In recent years, phenotypic drug screening has achieved renewed prominence, because it can develop drugs for some rare diseases with unknown etiology and without effective therapeutic drugs. Combined with biochip microarray technology, surface plasmon resonance imaging (SPRi) technology based on changes in reflected light intensity has been widely used to analyze interactions between biomolecules , especially for in vitro drug screening. However, changes in reflected light intensity are merely derivative of changes in surface plasmon resonance angles, so current SPRi technology suffers from low accuracy, poor sensitivity, and narrow linear range. In this study, we built an improved SPR microscopic imaging system capable of directly detecting SPR resonance angle (SPR-dip) shifts, with a sensitivity of 8.14 × 10 <sup>−6</sup> RIU, and a stability less than ± 2 m°. By optimizing the optical path system, the linear correlation coefficient of the system within the scanning range of 3800 m° is 0.9975. Based on this imaging system, we established a reliable screening system for single living mast cell allergy drugs. By developing small molecule microarray chips, we achieved high-throughput screening of 2400 sample points at one time. Our SPR microscopic imaging system integrates the comparative advantages of existing angle-scanning SPRi systems and opens the possibility of new applications in phenotypic drug screening.", "Keywords": "", "DOI": "10.1016/j.snb.2024.135286", "PubYear": 2024, "Volume": "404", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Pharmacy, Shandong University of Traditional Chinese Medicine, Jinan 250300, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering for Research, University of Toyama, 3190 Gofuku, Toyama 930-8555, Japan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON> Liu", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China;Institute of Pharmacy, Shandong University of Traditional Chinese Medicine, Jinan 250300, China;Corresponding author at: State Key Laboratory of Natural and Biomimetic Drugs, School of Pharmaceutical Sciences, Peking University, Beijing 100191, China"}], "References": []}, {"ArticleId": 111963793, "Title": "Graph Neural Network with curriculum learning for imbalanced node classification", "Abstract": "Graph Neural Network (GNN) stands as an emerging methodology for graph-based learning tasks, particularly for node classification. This study elucidates the susceptibility of GNN to discrepancies arising from imbalanced node labels. Conventional solutions for imbalanced classification, such as resampling, falter in node classification task, primarily due to their negligence of graph structure. Worse still, they often exacerbate the model’s inclination towards overfitting or underfitting, especially in the absence of adequate priori knowledge. To circumvent these limitations, we introduce a novel G raph N eural N etwork framework with C urriculum L earning (GNN-CL). This framework integrates two pivotal components. Initially, leveraging the principles of smoothness and homophily, we endeavor to procure dependable interpolation nodes and edges via adaptive graph oversampling. For another, we combine the Graph Classification Loss with the Metric Learning Loss, thereby refining the spatial proximity of nodes linked to the minority class in the feature space. Drawing inspiration from curriculum learning, the parameters of these components are dynamically modulated during the training phase to accentuate generalization and discrimination capabilities. Comprehensive evaluations on several widely used graph datasets affirm the superiority of our proposed model, which consistently outperforms the existing state-of-the-art methods.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.127229", "PubYear": 2024, "Volume": "574", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences & Key Laboratory of Network Information System Technology (NIST), Beijing, 100094, China"}, {"AuthorId": 2, "Name": "Zide Fan", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences & Key Laboratory of Network Information System Technology (NIST), Beijing, 100094, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences & Key Laboratory of Network Information System Technology (NIST), Beijing, 100094, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Tsinghua University, Beijing, 100084, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences & Key Laboratory of Network Information System Technology (NIST), Beijing, 100094, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences & Key Laboratory of Network Information System Technology (NIST), Beijing, 100094, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences & Key Laboratory of Network Information System Technology (NIST), Beijing, 100094, China"}], "References": [{"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Leveraging graph neural networks for point-of-interest recommendations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "462", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Structural context-based knowledge graph embedding for link prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "109", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111963832, "Title": "Probabilistic Programming Interfaces for Random Graphs: Markov Categories, Graphons, and Nominal Sets", "Abstract": "<p>We study semantic models of probabilistic programming languages over graphs, and establish a connection to graphons from graph theory and combinatorics. We show that every well-behaved equational theory for our graph probabilistic programming language corresponds to a graphon, and conversely, every graphon arises in this way.</p><p>We provide three constructions for showing that every graphon arises from an equational theory. The first is an abstract construction, using Markov categories and monoidal indeterminates. The second and third are more concrete. The second is in terms of traditional measure theoretic probability, which covers 'black-and-white' graphons. The third is in terms of probability monads on the nominal sets of Gabbay and Pitts. Specifically, we use a variation of nominal sets induced by the theory of graphs, which covers Erdős-Rényi graphons. In this way, we build new models of graph probabilistic programming from graphons.</p>", "Keywords": "", "DOI": "10.1145/3632903", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Harvard University, Cambridge, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, Cambridge, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Birmingham, Birmingham, UK"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Toronto, Toronto, Canada"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Oxford, Oxford, UK"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "KAIST, Daejeon, South Korea"}], "References": []}, {"ArticleId": 111963981, "Title": "Highly selective dual-band interdigital bandpass filter for C-band applications", "Abstract": "Miniaturization in the telecommunications field is deemed a current challenge and a critical demand in order to improve communication quality between the transmitter and the receiver while avoiding clutter issues. The main objective of this work is to design a miniature interdigital bandpass filter (IBF) using planar technology. The proposed bandpass filter is made up of three equidistant parallel-coupled lines carefully deposited on a small Rogers-5880 substrate possessing a full dimension of 10×10×1.6 mm 3 , a relative permittivity =2.2, and a loss tangen of 0.0009. The proposed IBF has been designed and simulated using the HFSS software, which is a simulator that studies the electromagnetic behavior of radio frequency structures using cutting-edge finite element solvers. The reached outcomes present good electrical performance in terms of insertion loss , reflection coefficient , voltage standing wave ratio (VSWR), and selectivity, making the proposed IBF suitable for integration in small electronic devices for C-band applications (4 GHz to 8 GHz).", "Keywords": "C-band;dual-band;interdigital bandpass filter;microwave filters;parallel-coupled lines", "DOI": "10.12928/telkomnika.v22i1.25601", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Zakaria El Ouadi", "Affiliation": "Cadi Ayyad University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Cadi Ayyad University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Cadi Ayyad University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Cadi Ayyad University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Cadi Ayyad University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 111964015, "Title": "The Logical Essence of Well-Bracketed Control Flow", "Abstract": "<p> A program is said to be well-bracketed if every called function must return before its caller can resume execution. This is often the case. Well-bracketedness has been captured semantically as a condition on strategies in fully abstract games models and multiple prior works have studied well-bracketedness by showing correctness/security properties of programs where such properties depend on the well-bracketed nature of control flow. The latter category of prior works have all used involved relational models with explicit state-transition systems capturing the relevant parts of the control flow of the program. In this paper we present the first Hoare-style program logic based on separation logic for reasoning about well-bracketedness and use it to show correctness of well-bracketed programs both directly and also through defining unary and binary logical relations models based on this program logic. All results presented in this paper are formalized on top of the Iris framework and mechanized in the Coq proof assistant. </p>", "Keywords": "", "DOI": "10.1145/3632862", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Paris-Saclay - CNRS - ENS Paris-Saclay - Inria - Laboratoire Méthodes Formelles, Gif-sur-Yvette, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}], "References": [{"Title": "A separation logic for effect handlers", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Efficient and provable local capability revocation using uninitialized capabilities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Theorems for free from separation logic specifications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111964083, "Title": "Efficient Campus Solutions: A Journal on Enhancing College Complaint Management", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2023.121213", "PubYear": 2023, "Volume": "12", "Issue": "12", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "Prof. <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111964114, "Title": "Proposing a new method for calculating DC sources in an extended multilevel converter", "Abstract": "In this paper, we propose a method for calculating the DC source amplitude in an extended multilevel inverter (MLI) structure so that the maximum number of levels and the output voltage waveform are as close as possible to the sinusoidal wave with minimum total harmonic distortion (THD). For the developed structure, three algorithms are suggested to determine the amounts of DC voltage resources. The first important point about choosing the right amounts for the DC resources is that the number of levels should be as large as possible, and the second important point is that the intervals between the levels should be the same throughout the waveform. By observing these two points, the output voltage waveform can be as near as possible to the sinusoidal wave that we want. In this study, we used iteration-based methods to find suitable values for DC sources. Simulation results are offered to confirm the capability of the extended multilevel converter. After we solved the problem through calculation and analysis, a code was written in MATLAB with the aim that this time the code will tell us for what values of DC sources we will have the largest number of levels and as we expected, the output of the MATLAB code confirmed the correctness of the calculations.", "Keywords": "cascaded;H-bridge;multilevel inverter;power elctronics;switches;topology", "DOI": "10.12928/telkomnika.v22i1.25707", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hakim <PERSON> University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hakim <PERSON> University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Islamic Azad University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Ahmad <PERSON>"}], "References": []}, {"ArticleId": 111964124, "Title": "Predicting an Optimal Medication/Prescription Regimen for Patient Discordant Chronic Comorbidities Using Multi-Output Models", "Abstract": "<p>This paper focuses on addressing the complex healthcare needs of patients struggling with discordant chronic comorbidities (DCCs). Managing these patients within the current healthcare system often proves to be a challenging process, characterized by evolving treatment needs necessitating multiple medical appointments and coordination among different clinical specialists. This makes it difficult for both patients and healthcare providers to set and prioritize medications and understand potential drug interactions. The primary motivation of this research is the need to reduce medication conflict and optimize medication regimens for individuals with DCCs. To achieve this, we allowed patients to specify their health conditions and primary and major treatment concerns, for example, costs of medication, interactions with current drugs, and weight gain. Utilizing data gathered from MTurk and Qualtrics, we gained insights into healthcare providers’ strategies for making/customizing medication regimens. We constructed a dataset and subsequently deployed machine learning algorithms to predict optimal medication regimens for DCC patients with specific treatment concerns. Following the benchmarking different models, Random forest emerged as the top performer, achieving an accuracy of 0.93. This research contributes significantly to the enhancement of decision-making processes, empowers patients to take a more active role in their healthcare, and promotes more informed and productive discussions between patients and their care teams.</p>", "Keywords": "", "DOI": "10.3390/info15010031", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Dayton, 300 College Park, Dayton, OH 45469, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Dayton, 300 College Park, Dayton, OH 45469, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Dayton, 300 College Park, Dayton, OH 45469, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Dayton, 300 College Park, Dayton, OH 45469, USA; Corresponding author"}], "References": [{"Title": "Assessment of Anxiety, Depression and Stress using Machine Learning Models", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "1989", "JournalTitle": "Procedia Computer Science"}, {"Title": "A practical tutorial on bagging and boosting based ensembles for machine learning: Algorithms, software tools, performance study, practical perspectives and opportunities", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "205", "JournalTitle": "Information Fusion"}, {"Title": "A Comparative Analysis of Machine Learning Algorithms to Predict Liver Disease", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "3", "Page": "917", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "A disease network-based recommender system framework for predictive risk modelling of chronic diseases and their comorbidities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "10330", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 111964130, "Title": "Laser additive manufacturing of Miura-origami tube inspired quasi-zero stiffness metamaterial with prominent longitudinal wave propagation", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2023.2299691", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Impact and Safety Engineering (Ningbo University), Ministry of Education, Ningbo, People’s Republic of China"}, {"AuthorId": 2, "Name": "Hongyu Chen", "Affiliation": "Key Laboratory of Impact and Safety Engineering (Ningbo University), Ministry of Education, Ningbo, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Impact and Safety Engineering (Ningbo University), Ministry of Education, Ningbo, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Impact and Safety Engineering (Ningbo University), Ministry of Education, Ningbo, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Impact and Safety Engineering (Ningbo University), Ministry of Education, Ningbo, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Leibniz Institute for Solid State and Materials Research Dresden, Institute for Materials Chemistry, Dresden, Germany"}], "References": [{"Title": "Influence of structural features on processability, microstructures, chemical compositions, and hardness of selective laser melted complex thin-walled components", "Authors": "<PERSON><PERSON>; Dongdong Gu; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "Page": "1643", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 111964183, "Title": "Philosophy design of single-trait based multi-feature biometric system", "Abstract": "This paper presents new techniques for designing a simple and reliable multifeatured biometric system based on a single trait source. First, a one-to-one relationship between the feature’s edge and its associated angle is utilized after extracting the contrast feature using the gray-level co-occurring matrix (GLCM) method. Secondly, the classifying stage is modified to process one-dimensional vectors rather than the whole feature’s template. That means whatever the template size is, the matching operation is always processing a one-dimensional vector called a mean-feature vector which requires low storage and less computation complexity. Finally, for comparison purposes, the performances of the three biometric systems are calculated for recognizing 170 subjects taken from four facial databases. These comparisons are made using three error distance measurements. The recognition rates of the angle-based feature were very competitive to the regular edge-based results; however, the overall recognition accuracy is highly improved after fusing the decision of the two unibiometric systems using the Logic-OR operator. The fused system performance was satisfactory and it shows that the decision fusion of the single source trait based multifeatured system has promising performance represented by accuracy improvement, low storage, and low matching time.", "Keywords": "biometric system;decision fusion;edge-angle;gray-level co-occurring matrix;multifeatured;multimodal;single source", "DOI": "10.12928/telkomnika.v22i1.25417", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "Rabab A. Ra<PERSON>ol", "Affiliation": "Mustansiriyah University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mustansiriyah University"}], "References": []}, {"ArticleId": 111964267, "Title": "Twin-screw extrusion of short Kevlar fiber–reinforced nylon composite filaments with enhanced mechanical properties and morphology", "Abstract": "<p>This paper presents the development and characterization of cost-effective short Kevlar fiber reinforced nylon composite filaments. The effect of Kevlar fiber content on the tensile properties and the fiber distribution morphology was explored. A small amount of short Kevlar fiber was twin-screw compounded with nylon to study the reinforcing effects. Optical microscopy was used to investigate the morphology and the fiber distribution in the nylon matrix. The tensile strength of Kevlar composites increased to 90 MPa and 99 MPa at the fiber content of 1 wt% and 3 wt%, respectively, compared with the tensile strength of 84 MPa for pure nylon. As the fiber content increased from 0 to 3 wt%, the tensile modulus increased from 3.28 to 3.77 GPa. The optical microscopy and SEM images indicate that the twin-screw compounder with a proper screw configuration could disperse and distribute the Kevlar fibers uniformly in the nylon matrix. Statistically, this study demonstrates that the tensile strength and tensile modulus of nylon filament can be effectively enhanced with the addition of small amount of Kevlar fiber (i.e., less than 3 wt%), without sacrificing the ductility.</p>", "Keywords": "Kevlar fiber; Nylon; Filament; Twin-screw compounding; Tensile properties", "DOI": "10.1007/s00170-023-12927-4", "PubYear": 2024, "Volume": "130", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial & Manufacturing Engineering & Technology, Bradley University, Peoria, USA; Corresponding author."}, {"AuthorId": 2, "Name": "Victoria L. Finkenstadt", "Affiliation": "US Department of Agriculture, National Center for Agricultural Utilization Research, Agricultural Research Service, Peoria, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, York University, Toronto, Canada"}], "References": [{"Title": "Direct injection molding of hybrid polypropylene/wood-fiber composites reinforced with glass fiber and carbon fiber", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "1-2", "Page": "201", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 111964282, "Title": "A review on artificial intelligence for the diagnosis of fractures in facial trauma imaging", "Abstract": "<p>Patients with facial trauma may suffer from injuries such as broken bones, bleeding, swelling, bruising, lacerations, burns, and deformity in the face. Common causes of facial-bone fractures are the results of road accidents, violence, and sports injuries. Surgery is needed if the trauma patient would be deprived of normal functioning or subject to facial deformity based on findings from radiology. Although the image reading by radiologists is useful for evaluating suspected facial fractures, there are certain challenges in human-based diagnostics. Artificial intelligence (AI) is making a quantum leap in radiology, producing significant improvements of reports and workflows. Here, an updated literature review is presented on the impact of AI in facial trauma with a special reference to fracture detection in radiology. The purpose is to gain insights into the current development and demand for future research in facial trauma. This review also discusses limitations to be overcome and current important issues for investigation in order to make AI applications to the trauma more effective and realistic in practical settings. The publications selected for review were based on their clinical significance, journal metrics, and journal indexing.</p>", "Keywords": "Facial trauma; Facial-bone fractures; diagnosis; Radiology; Articial intelligence", "DOI": "10.3389/frai.2023.1278529", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> and The London School of Medicine and Dentistry, Queen Mary University of London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> and The London School of Medicine and Dentistry, Queen Mary University of London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> and The London School of Medicine and Dentistry, Queen Mary University of London, United Kingdom"}], "References": [{"Title": "Addressing bias in big data and AI for health care: A call for open science", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "10", "Page": "100347", "JournalTitle": "Patterns"}]}, {"ArticleId": 111964313, "Title": "Type-Based Gradual Typing Performance Optimization", "Abstract": "<p>Gradual typing has emerged as a popular design point in programming languages, attracting significant interests from both academia and industry. Programmers in gradually typed languages are free to utilize static and dynamic typing as needed. To make such languages sound, runtime checks mediate the boundary of typed and untyped code. Unfortunately, such checks can incur significant runtime overhead on programs that heavily mix static and dynamic typing. To combat this overhead without necessitating changes to the underlying implementations of languages, we present discriminative typing. Discriminative typing works by optimistically inferring types for functions and implementing an optimized version of the function based on this type. To preserve safety it also implements an un-optimized version of the function based purely on the provided annotations. With two versions of each function in hand, discriminative typing translates programs so that the optimized functions are called as frequently as possible while also preserving program behaviors.</p><p>We have implemented discriminative typing in Reticulated Python and have evaluated its performance compared to guarded Reticulated Python. Our results show that discriminative typing improves the performance across 95% of tested programs, when compared to Reticulated, and achieves more than 4× speedup in more than 56% of these programs. We also compare its performance against a previous optimization approach and find that discriminative typing improved performance across 93% of tested programs, with 30% of these programs receiving speedups between 4 to 25 times. Finally, our evaluation shows that discriminative typing remarkably reduces the overhead of gradual typing on many mixed type configurations of programs.</p><p>In addition, we have implemented discriminative typing in Grift and evaluated its performance. Our evaluation demonstrations that DT significantly improves performance of Grift</p>", "Keywords": "", "DOI": "10.1145/3632931", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Quantinuum, Broomfield, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Louisiana, Lafayette, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Louisiana, Lafayette, USA"}], "References": [{"Title": "Corpse reviver: sound and efficient gradual typing via contract verification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111964376, "Title": "On the ideal number of groups for isometric gradient propagation", "Abstract": "Recently, various normalization layers have been proposed to stabilize the training of deep neural networks . Among them, group normalization is a generalization of layer normalization and instance normalization by allowing a degree of freedom in the number of groups it uses. However, to determine the optimal number of groups, trial-and-error-based hyperparameter tuning is required, and such experiments are time-consuming. In this study, we discuss a reasonable method for setting the number of groups. First, we find that the number of groups influences the gradient behavior of the group normalization layer. Based on this observation, we derive the ideal number of groups, which calibrates the gradient scale to facilitate gradient descent optimization. This paper is the first to propose an optimal number of groups that is theoretically grounded, architecture-aware, and can provide a proper value in a layer-wise manner for all layers. The proposed method exhibited improved performance over existing methods in numerous neural network architectures, tasks, and datasets.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.127217", "PubYear": 2024, "Volume": "573", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Electrical Engineering, Pohang University of Science and Technology, Pohang 790-784, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Pohang University of Science and Technology, Pohang 790-784, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Pohang University of Science and Technology, Pohang 790-784, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Pohang University of Science and Technology, Pohang 790-784, Republic of Korea;Corresponding author"}], "References": [{"Title": "Group Normalization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "3", "Page": "742", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Multiobjective ResNet pruning by means of EMOAs for remote sensing scene classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "298", "JournalTitle": "Neurocomputing"}, {"Title": "More trainable inception-ResNet for face recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "411", "Issue": "", "Page": "9", "JournalTitle": "Neurocomputing"}, {"Title": "Regularisation of neural networks by enforcing Lipschitz continuity", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "2", "Page": "393", "JournalTitle": "Machine Learning"}, {"Title": "Spherical perspective on learning with normalization layers", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "487", "Issue": "", "Page": "66", "JournalTitle": "Neurocomputing"}, {"Title": "UFKT: Unimportant filters knowledge transfer for CNN pruning", "Authors": "Sarvani CH; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "514", "Issue": "", "Page": "101", "JournalTitle": "Neurocomputing"}, {"Title": "SOR-TC: Self-attentive octave ResNet with temporal consistency for compressed video action recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "533", "Issue": "", "Page": "191", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111964381, "Title": "Flan: An Expressive and Efficient Datalog Compiler for Program Analysis", "Abstract": "<p>Datalog has gained prominence in program analysis due to its expressiveness and ease of use. Its generic fixpoint resolution algorithm over relational domains simplifies the expression of many complex analyses. The performance and scalability issues of early Datalog approaches have been addressed by tools such as Soufflé through specialized code generation. Still, while pure Datalog is expressive enough to support a wide range of analyses, there is a growing need for extensions to accommodate increasingly complex analyses. This has led to the development of various extensions, such as Flix, Datafun, and Formulog, which enhance Datalog with features like arbitrary lattices and SMT constraints.</p><p>Most of these extensions recognize the need for full interoperability between Datalog and a full-fledged programming language, a functionality that high-performance systems like Soufflé lack. Specifically, in most cases, they construct languages from scratch with first-class Datalog support, allowing greater flexibility. However, this flexibility often comes at the cost of performance due to the conflicting requirements of prioritizing modularity and abstraction over efficiency. Consequently, achieving both flexibility and compilation to highly-performant specialized code poses a significant challenge.</p><p>In this work, we reconcile the competing demands of expressiveness and performance with Flan, a Datalog compiler fully embedded in Scala that leverages multi-stage programming to generate specialized code for enhanced performance. Our approach combines the flexibility of Flix with Soufflé’s performance, offering seamless integration with the host language that enables the addition of powerful extensions while generating specialized code for the entire computation. <PERSON>lan’s simple operator interface allows the addition of an extensive set of features, including arbitrary aggregates, user-defined functions, and lattices, with multiple execution strategies such as binary and multi-way joins, supported by different indexing structures like specialized trees and hash tables, with minimal effort. We evaluate our system on a variety of benchmarks and compare it to established Datalog engines. Our results demonstrate competitive performance and speedups in the range of 1.4× to 12.5× compared to state-of-the-art systems for workloads of practical importance.</p>", "Keywords": "", "DOI": "10.1145/3632928", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Purdue University, West Lafayette, USA"}, {"AuthorId": 3, "Name": "Tiark Rompf", "Affiliation": "Purdue University, West Lafayette, USA"}], "References": [{"Title": "Debugging Large-scale Datalog", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "Formulog: Datalog for SMT-based static analysis", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Compiling symbolic execution with staging and algebraic effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "egg: Fast and extensible equality saturation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Relational e-matching", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Graph IRs for Impure Higher-Order Languages: Making Aggressive Optimizations Affordable with Precise Effect Dependencies", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "400", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Bring Your Own Data Structures to Datalog", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "1198", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111964382, "Title": "Deciding Asynchronous Hyperproperties for Recursive Programs", "Abstract": "<p>We introduce a novel logic for asynchronous hyperproperties with a new mechanism to identify relevant positions on traces. While the new logic is more expressive than a related logic presented recently by <PERSON><PERSON> et al., we obtain the same complexity of the model checking problem for finite state models. Beyond this, we study the model checking problem of our logic for pushdown models. We argue that the combination of asynchronicity and a non-regular model class studied in this paper constitutes the first suitable approach for hyperproperty model checking against recursive programs.</p>", "Keywords": "", "DOI": "10.1145/3632844", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Münster, Münster, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Münster, Münster, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Münster, Münster, Germany"}], "References": [{"Title": "Automata and fixpoints for asynchronous hyperproperties", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111964409, "Title": "Cutting condition effects on microstructure and mechanical characteristics of Ni-based superalloys—a review", "Abstract": "<p>Surface integrity is considered to be a significant factor in evaluating surface qualities. A wide range of applications of nickel-based superalloys can be attributed to a number of features such as mechanical and chemical characteristics at elevated temperatures, high durability and ductileness, great resistance to corrosion, high melting point, thermal shock, thermal fatigue, and erosion. However, the practical performance of the component particularly the fatigue life is critically influenced by the machined surface finish of Ni-based superalloys. The present review article provides the most recent information on various surface integrity properties while machining Ni-based superalloys. The surface integrity aspects contain the surface topography including machined surface defects (plucking, metal debris, feed marks, surface cavities, smeared material, grooves and laps, cracking, carbide particles, and redeposited materials) and surface roughness; the metallurgical phase consists of plasticity, grain refinement and orientation, and white layer formation, and mechanical characteristics comprise the residual stress and strain hardening. The impact of various cutting parameters, the cutting environment, and cutting tool materials have been carefully explained on surface metallurgy and mechanical characteristics. Moreover, the influence of surface integrity on the fatigue life of machined components has been studied.</p>", "Keywords": "Nickel-based superalloys; Surface integrity characteristics; Surface topography; Residual stress", "DOI": "10.1007/s00170-023-12910-z", "PubYear": 2024, "Volume": "130", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for High Efficiency and Clean Mechanical Manufacture of Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan, People’s Republic of China; Key National Demonstration Center for Experimental Mechanical Engineering Education, Shandong University, Jinan, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Qilu University of Technology (Shandong Academy of Sciences), Jinan, People’s Republic of China; Shandong Institute of Mechanical Design and Research, Jinan, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory for High Efficiency and Clean Mechanical Manufacture of Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan, People’s Republic of China; Key National Demonstration Center for Experimental Mechanical Engineering Education, Shandong University, Jinan, People’s Republic of China; Corresponding author."}], "References": [{"Title": "Machined surface integrity of inconel 718 in high-speed dry milling using SiAlON ceramic tools", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "7-8", "Page": "1941", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental study on surface integrity of Inconel 690 milled by coated carbide inserts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "5-6", "Page": "3025", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Experimental characterization of the performance of MQL-assisted turning of solution heat-treated and aged Inconel 718 alloy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "7-8", "Page": "3839", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 111964439, "Title": "Algebraic Effects Meet Hoare Logic in Cubical Agda", "Abstract": "<p> This paper presents a novel formalisation of algebraic effects with equations in Cubical Agda. Unlike previous work in the literature that employed setoids to deal with equations, the library presented here uses quotient types to faithfully encode the type of terms quotiented by laws. Apart from tools for equational reasoning, the library also provides an effect-generic Hoare logic for algebraic effects, which enables reasoning about effectful programs in terms of their pre- and post- conditions. A particularly novel aspect is that equational reasoning and Hoare-style reasoning are related by an elimination principle of Hoare logic. </p>", "Keywords": "", "DOI": "10.1145/3632898", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Imperial College London, London, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Imperial College London, London, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Imperial College London, London, United Kingdom"}], "References": [{"Title": "Interaction trees: representing recursive and impure programs in Coq", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Compiling effect handlers in capability-passing style", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Weakest Preconditions in Fibrations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "352", "Issue": "", "Page": "5", "JournalTitle": "Electronic Notes in Theoretical Computer Science"}, {"Title": "Dijkstra monads forever: termination-sensitive specifications for interaction trees", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "ReLoC Reloaded: A Mechanized Relational Logic for Fine-Grained Concurrency and Logical Atomicity", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "17, Issue 3", "Issue": "", "Page": "", "JournalTitle": "Logical Methods in Computer Science"}, {"Title": "Simuliris: a separation logic framework for verifying concurrent program optimizations", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Syntax and models of Cartesian cubical type theory", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "4", "Page": "424", "JournalTitle": "Mathematical Structures in Computer Science"}, {"Title": "Formal reasoning about layered monadic interpreters", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "ICFP", "Page": "254", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Program adverbs and Tlön embeddings", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "ICFP", "Page": "312", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "High-level effect handlers in C++", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA2", "Page": "1639", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Conditional Contextual Refinement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "1121", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Quotients, inductive types, and quotient inductive types", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "18, Issue 2", "Issue": "", "Page": "15:1", "JournalTitle": "Logical Methods in Computer Science"}, {"Title": "Modular Models of Monoids with Operations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "ICFP", "Page": "566", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111964476, "Title": "Approximate Inference via Fibrations of Statistical Games", "Abstract": "We characterize a number of well known systems of approximate inference as loss models: lax sections of 2-fibrations of statistical games, constructed by attaching internally-defined loss functions to Bayesian lenses. Our examples include the relative entropy, which constitutes a strict section, and whose chain rule is formalized by the horizontal composition of the 2-fibration. In order to capture this compositional structure, we first introduce the notion of 'copy-composition', alongside corresponding bicategories through which the composition of copy-discard categories factorizes. These bicategories are a variant of the Copara construction, and so we additionally introduce coparameterized Bayesian lenses, proving that coparameterized Bayesian updates compose optically, as in the non-coparameterized case.", "Keywords": "", "DOI": "10.4204/EPTCS.397.17", "PubYear": 2023, "Volume": "397", "Issue": "", "JournalId": 16594, "JournalTitle": "Electronic Proceedings in Theoretical Computer Science", "ISSN": "", "EISSN": "2075-2180", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Clere <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111964539, "Title": "DFN: A deep fusion network for flexible single and multi-modal action recognition", "Abstract": "Multi-modal action recognition methods can be generally classified into two categories: (1) fusing multi-modal features with simple concatenation or fusing the classification scores of individual modalities without considering the interaction among the multi-modalities; (2) using one of the modalities as privileged information in training to boost the recognition on the other modalities in inference. The former approach usually is not able to deal with the cases where one of the modalities is missing. In the latter, the trained classifier does not work on the privileged modality. To address these shortcomings, this paper presents a novel end-to-end trainable deep fusion network (DFN) that is able to improve the performance not only in the cases where all modalities are available and also in the cases where there is a missing modality. The DFN is simple yet effective with the capability of retrieving an estimation of one modality by using another modality through a Multilayer Perceptron (MLP). In order to better preserve structure information, the DFN first maps the individual modality features to a high dimensional Kronecker-product space and subsequently learns a low-dimensional discriminative space for classification. The effectiveness of the proposed DFN has been verified on three benchmark datasets: the large NTU RGB+D, UTD-MHAD, and SYSU-3D datasets and it has achieved state-of-the-art results.", "Keywords": "Multi-modal fusion ; Deep learning ; Human action recognition", "DOI": "10.1016/j.eswa.2024.123145", "PubYear": 2024, "Volume": "245", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, North University of China, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "Wanqing Li", "Affiliation": "Advanced Multimedia Research Lab, University of Wollongong, Australia;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Advanced Multimedia Research Lab, University of Wollongong, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Advanced Multimedia Research Lab, University of Wollongong, Australia"}], "References": [{"Title": "Graph convolutional network with structure pooling and joint-wise channel attention for action recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Chunfeng Yuan", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107321", "JournalTitle": "Pattern Recognition"}, {"Title": "Exploiting inter-frame regional correlation for efficient action recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "114829", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Effective action recognition with embedded key point shifts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108172", "JournalTitle": "Pattern Recognition"}, {"Title": "Sequential inter-hop graph convolution neural network (SIhGCN) for skeleton-based human action recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "195", "Issue": "", "Page": "116566", "JournalTitle": "Expert Systems with Applications"}, {"Title": "NLFFTNet: A non-local feature fusion transformer network for multi-scale object detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "493", "Issue": "", "Page": "15", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111964608, "Title": "Menger-Type Connectivity of Line Graphs of Generalized Hypercubes With Faulty Edges", "Abstract": "<p>A connected graph $G$ is called strongly Menger edge connected if $G$ has min{deg$_{G}(x)$, deg$_{G}(y)$} edge-disjoint paths between any two distinct vertices $x$ and $y$ in $G$. In this paper, we consider two types of the strongly Menger edge connectivity of the line graphs of generalized $n$-dimensional hypercubes with faulty edges, namely the $m$-edge-fault-tolerant and $m$-conditional edge-fault-tolerant strongly Menger edge connectivity. We show that the line graphs of all generalized $n$-dimensional hypercubes are $(2n-4)$-edge-fault-tolerant strongly Menger edge connected for $n\\geq 3$ and $(4n-10)$-conditional edge-fault-tolerant strongly Menger edge connected for $n\\geq 4$. The two bounds for the maximum numbers of faulty edges are best possible.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxad126", "PubYear": 2024, "Volume": "67", "Issue": "6", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences , Xiamen University, Xiamen 361005, P. R. China; School of Mathematics and Statistics , Qinghai Minzu University, Xining 810007, P. R. <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences , Xiamen University, Xiamen 361005, P. R. China; School of Mathematics and Statistics , Qinghai Minzu University, Xining 810007, P. R. <PERSON>"}], "References": [{"Title": "Strong Menger Connectedness of Augmented k -ary n -cubes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "5", "Page": "812", "JournalTitle": "The Computer Journal"}, {"Title": "The Conditional Reliability Evaluation of Data Center Network BCDC", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Fan", "PubYear": 2021, "Volume": "64", "Issue": "9", "Page": "1451", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 111964627, "Title": "HiRM: Hierarchical resource management for earth system models on many-core clusters", "Abstract": "", "Keywords": "", "DOI": "10.1007/s42514-023-00176-6", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 64694, "JournalTitle": "CCF Transactions on High Performance Computing", "ISSN": "2524-4922", "EISSN": "2524-4930", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Hongliang Li", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "Sicong Li", "Affiliation": ""}], "References": [{"Title": "APMT: an automatic hardware counter-based performance modeling tool for HPC applications", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "2", "Page": "135", "JournalTitle": "CCF Transactions on High Performance Computing"}, {"Title": "Coordinated process scheduling algorithms for coupled earth system models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "20", "Page": "e6346", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Design, analysis and implementation of electronically interfaced photovoltaic system using ARM Cortex-M4 microcontroller", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "98", "Issue": "", "Page": "107701", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 111964662, "Title": "WITHDRAWN: A post-quantum key exchange protocol from the intersection of conics", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.jsc.2024.102297", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111964675, "Title": "Single image dehazing enhancement based on retinal mechanism", "Abstract": "<p>Based on the hierarchical transmission and interaction response characteristics of visual information in retina, we propose a single image dehazing enhancement computational model that simulates the multiple level physiological mechanisms of retina. Firstly, according to the characteristics of gap junction between horizontal cells followed by the dynamic changing properties of light intensity, an adaptive adjustment model of receptive fields based on the luminance information is constructed to protect the detail information at the edge of hazy images. Secondly, simulating the crossover inhibition mechanism of ON and OFF pathways, the AII amacrine cell network model is constructed to inhibit the OFF pathway through the ON pathway, thus expanding the dynamic range of the hazy images. Finally, according to the dynamic correlation between the receptive field characteristics of ganglion cell and local contrast, a single-opponent receptive field (SORF) dynamic adjustment model based on local contrast information is constructed to enhance the contrast of hazy images. Natural image dataset and the composite image dataset RESIDE-OTS are used as the experimental subjects. In the natural image dataset, Fog Aware Density Evaluator (FADE) ranked 3rd in the average score and 1st in the Natural Image Quality Evaluator (NIQE) average score; Compared with the highest index in other methods, the peak signal-to-noise ratio (PSNR) and structural similarity index (SSIM) in the synthetic image dataset increased by 7.20% and 4.16%, respectively. Experiments demonstrate that our method enhances the details and contrast while retaining the original color characteristics of the image, and improves the problems of color distortion and halation, which provides a new idea for the internal mechanism and application of brain vision.</p>", "Keywords": "Single image dehazing; Gap junction coupling; Crossover inhibition; Single-opponent receptive field", "DOI": "10.1007/s11042-023-17935-w", "PubYear": 2024, "Volume": "83", "Issue": "21", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Pattern Recognition and Image Processing, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Pattern Recognition and Image Processing, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Pattern Recognition and Image Processing, Hangzhou Dianzi University, Hangzhou, China; Corresponding author."}], "References": [{"Title": "Real-time image and video dehazing based on multiscale guided filtering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "25", "Page": "36567", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Haze removal for single image: A comprehensive review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "537", "Issue": "", "Page": "85", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111964683, "Title": "Polymorphic Type Inference for Dynamic Languages", "Abstract": "We present a type system that combines, in a controlled way, \nfirst-order polymorphism with intersection types, union types, and \nsubtyping, and prove its safety. We then define a type reconstruction \nalgorithm that is sound and terminating. This yields a system in which \nunannotated functions are given polymorphic types (thanks to \n<PERSON><PERSON><PERSON><PERSON><PERSON>) that can express the overloaded behavior of the \nfunctions they type (thanks to the intersection introduction rule) and \nthat are deduced by applying advanced techniques of type narrowing \n(thanks to the union elimination rule). This makes the system a prime \ncandidate to type dynamic languages.", "Keywords": "", "DOI": "10.1145/3632882", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CNRS - Université Paris Cité, Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université Paris Cité, Paris, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Université Paris-Saclay, Gif-sur-Yvette, France"}], "References": [{"Title": "Compositional Programming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Bruno <PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "On type-cases, union elimination, and occurrence typing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Revisiting occurrence typing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "217", "Issue": "", "Page": "102781", "JournalTitle": "Science of Computer Programming"}, {"Title": "MLstruct: principal type inference in a Boolean algebra of structural types", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA2", "Page": "449", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "A Bowtie for a Beast: Overloading, Eta Expansion, and Extensible Data Types in F⋈", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "515", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Typing Records, Maps, and Structs", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "ICFP", "Page": "215", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Polymorphic Type Inference for Dynamic Languages", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "Page": "1179", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 111964720, "Title": "Systemic risk measurement: A Quantile Long Short-Term Memory network approach", "Abstract": "In finance, systemic risk is the risk that the crisis of an institution could trigger instability or bring down an entire system or market. The Delta Conditional Value-at-Risk is a market-based measure proposed by the recent literature to quantify the systematicity of some financial institutions. Several methods have been proposed to estimate this measure, and the choice of the best method is still an open question. The bivariate constant conditional correlation GARCH model represents one of the most preferred approaches since it allows the computation of the Delta Conditional Value-at-Risk in a closed form. Nevertheless, it requires strong distributional assumptions that are often considered unrealistic. We develop a Quantile Long Short-Term Memory network approach that allows the estimation of the Delta Conditional Value-at-Risk of several financial institutions simultaneously. The model consists of a multi-output neural network able to provide, at the same time, the log-return quantiles of different institutions useful to measure the systemic risk. Furthermore, the proposed model does not need any particular assumption, and it is specifically designed to avoid quantile crossing issues affecting the traditional quantile regression-based approach. Numerical experiments on data of some global systemically important banks reported in the Financial Stability Board validated our approach. We obtain Delta Conditional Value-at-Risk estimates that accurately capture market dynamics and produce a ranking of systemic banks that meets the desired properties of stability and persistence.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.111224", "PubYear": 2024, "Volume": "152", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Economics, Law, Cybersecurity and Sports Sciences, University of Naples “Parthenope”, Guglielmo Pepe Street, 80035 Naples, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Business and Quantitative Studies, University of Naples “Parthenope”, Generale Parisi Street, 80132 Naples, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Business and Quantitative Studies, University of Naples “Parthenope”, Generale Parisi Street, 80132 Naples, Italy"}], "References": [{"Title": "Measuring CoVaR: An Empirical Comparison", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "2", "Page": "511", "JournalTitle": "Computational Economics"}, {"Title": "Financial time series forecasting with deep learning : A systematic literature review: 2005–2019", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "106181", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning for financial applications : A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106384", "JournalTitle": "Applied Soft Computing"}, {"Title": "High-performance stock index trading via neural networks and trees", "Authors": "Chariton <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106567", "JournalTitle": "Applied Soft Computing"}, {"Title": "Smooth pinball loss nonparallel support vector machine for robust classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106840", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep learning for Arabic subjective sentiment analysis: Challenges and research opportunities", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106836", "JournalTitle": "Applied Soft Computing"}, {"Title": "Data augmentation based estimation for the censored composite quantile regression neural network model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "109381", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep Learning Forecasting for Supporting Terminal Operators in Port Business Development", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "8", "Page": "221", "JournalTitle": "Future Internet"}]}, {"ArticleId": *********, "Title": "Data fusion in neuromarketing: Multimodal analysis of biosignals, lifecycle stages, current advances, datasets, trends, and challenges", "Abstract": "The primary goal of any company is to increase its profits by improving both the quality of its products and how they are advertised. In this context, neuromarketing seeks to enhance the promotion of products and generate a greater acceptance on potential buyers. Traditionally, neuromarketing studies have relied on a single biosignal to obtain feedback from presented stimuli. However, thanks to new devices and technological advances studying this area of knowledge, recent trends indicate a shift towards the fusion of diverse biosignals. An example is the usage of electroencephalography for understanding the impact of an advertisement at the neural level and visual tracking to identify the stimuli that induce such impacts. This emerging pattern determines which biosignals to employ for achieving specific neuromarketing objectives. Furthermore, the fusion of data from multiple sources demands advanced processing methodologies. Despite these complexities, there is a lack of literature that adequately collates and organizes the various data sources and the applied processing techniques for the research objectives pursued. To address these challenges, the current paper conducts a comprehensive analysis of the objectives, biosignals, and data processing techniques employed in neuromarketing research. This study provides both the technical definition and a graphical distribution of the elements under revision. Additionally, it presents a categorization based on research objectives and provides an overview of the combinatory methodologies employed. After this, the paper examines primary public datasets designed for neuromarketing research together with others whose main purpose is not neuromarketing, but can be used for this matter. Ultimately, this work provides a historical perspective on the evolution of techniques across various phases over recent years and enumerates key lessons learned.", "Keywords": "Data fusion ; Neuromarketing ; Biosignals ; Life cycle ; Brain–computer interfaces ; Biosensors", "DOI": "10.1016/j.inffus.2024.102231", "PubYear": 2024, "Volume": "105", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information and Communications Engineering, University of Murcia, Murcia, 30100, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information and Communications Engineering, University of Murcia, Murcia, 30100, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information and Communications Engineering, University of Murcia, Murcia, 30100, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Bit&Brain Technologies S.L, Zaragoza, 50006, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Bit&Brain Technologies S.L, Zaragoza, 50006, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Architecture and Technology, University of Murcia, Murcia, 30100, Spain"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Communication Systems Group CSG, Department of Informatics IfI, University of Zurich UZH, CH—8050 Zürich, Switzerland"}], "References": [{"Title": "Multi-level information fusion for learning a blood pressure predictive model using sensor data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "24", "JournalTitle": "Information Fusion"}, {"Title": "DEAR-MULSEMEDIA: Dataset for emotion analysis and recognition in response to multiple sensorial media", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "", "Page": "37", "JournalTitle": "Information Fusion"}, {"Title": "Cognitive neuroscience in the design process of social advertising", "Authors": "Piwowarski Mateusz; Nermend <PERSON>ra", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "2959", "JournalTitle": "Procedia Computer Science"}, {"Title": "A review on transfer learning in EEG signal analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "421", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Emotion recognition based on convolutional neural networks and heterogeneous bio-signal data sources", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "107", "JournalTitle": "Information Fusion"}, {"Title": "Multi-modal bioelectrical signal fusion analysis based on different acquisition devices and scene settings: Overview, challenges, and novel orientation", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON>", "PubYear": 2022, "Volume": "79", "Issue": "", "Page": "229", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 111964760, "Title": "Moving-horizon estimation approach for nonlinear systems with measurement contaminated by outliers", "Abstract": "An application of moving-horizon strategy for nonlinear systems with possible outliers in measurements is addressed. With the increased success of movinghorizon strategy in the state estimation for linear systems with outliers acting on the measurement, investigating the nonlinear approach is highly required. In this paper we applied the nonlinear version which has been presented in the literature in term of discrete-time linear time-invariant systems, where the applied strategy considers minimizing a least-squares functions in which each measure possibly contaminated by outlier is left out in turn and the lowest cost is propagated. The moving horizon filter effectiveness as compared with the extended <PERSON><PERSON> filter is shown by means of simulation example and estimation error comparison. The moving horizon filter shows the feature of resisting outliers with robust estimation", "Keywords": "moving horizon estimation;nonlinear moving horizon estimation;outliers;state estimation;uncertainity", "DOI": "10.12928/telkomnika.v22i1.25230", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Higher Colleges of Technology (HCT)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Higher Colleges of Technology (HCT)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Higher Colleges of Technology (HCT)"}, {"AuthorId": 4, "Name": "Abdel Ilah Nour <PERSON>", "Affiliation": "Tafila Technical University"}, {"AuthorId": 5, "Name": "Rana <PERSON><PERSON> <PERSON>", "Affiliation": "Zarqa University"}], "References": []}, {"ArticleId": 111964802, "Title": "Real-time vehicle counting using custom YOLOv8n and DeepSORT for resource-limited edge devices", "Abstract": "Recently, there has been a significant increase in the use of deep learning and low-computing edge devices for analysis of video-based systems, particularly in the field of intelligent transportation systems (ITS). One promising application of computer vision techniques in ITS is in the development of low-computing and accurate vehicle counting systems that can be used to eliminate dependence on external cloud computing resources. This paper proposes a compact, reliable and real-time vehicle counting solution which can be deployed on low-computational requirement edge computing devices. The system makes use of a custom-built vehicle detection algorithm based on the you only look once version 8 nano (YOLOv8n), combined with a deep association metric (DeepSORT) object tracking algorithm and an efficient vehicle counting method for accurate counting of vehicles in highway scenes. The system is trained to detect, track and count four distinct vehicle classeses, namely: car, motorcycle, bus, and truck. The proposed system was able to achieve an average vehicle detection mean average precision (mAP) score of 97.5%, a vehicle counting accuracy score of 96.8% and an average speed of 19.4 frames per second (FPS), all while being deployed on a compact Nvidia Jetson Nano edge-computing device. The proposed system outperforms other previously proposed tools in terms of both accuracy and speed.", "Keywords": "edge computing;vehicle counting;vehicle detection;vehicle tracking;you only look once version 8 nano", "DOI": "10.12928/telkomnika.v22i1.25096", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "International Islamic University Malaysia (IIUM)"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "International Islamic University Malaysia (IIUM)"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "International Islamic University Malaysia (IIUM)"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "International Islamic University Malaysia (IIUM)"}], "References": []}, {"ArticleId": 111964835, "Title": "Decision and Complexity of Dolev-Yao Hyperproperties", "Abstract": "<p>The formal analysis of cryptographic protocols traditionally focuses on trace and equivalence properties, for which decision procedures in the symbolic (or Dolev-Yao, or DY) model are known. However, many relevant security properties are expressed as DY hyperproperties that involve quantifications over both execution paths and attacker computations (which are constrained by the attacker's knowledge in the underlying model of computation). DY hyperproperties generalise hyperproperties, for which many decision procedures exist, to the setting of DY models. Unfortunately, the subtle interactions between both forms of quantifications have been an obstacle to lifting decision procedures from hyperproperties to DY hyperproperties.</p><p>The central contribution of the paper is the first procedure for deciding DY hyperproperties, in the usual setting where the number of protocol sessions is bounded and where the equational theory modelling cryptography is subterm-convergent. We prove that our decision procedure can decide the validity of any hyperproperty in which quantifications over messages are guarded and quantifications over attacker computations are limited to expressing the attacker's knowledge. We also establish the complexity of the decision problem for several important fragments of the hyperlogic. Further, we illustrate the techniques and scope of our contributions through examples of related hyperproperties.</p>", "Keywords": "", "DOI": "10.1145/3632906", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "MPI-SP, Bochum, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "MPI-SP, Bochum, Germany / IMDEA Software Institute, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "MPI-SP, Bochum, Germany"}], "References": [{"Title": "Taming callbacks for smart contract modularity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "HyperATL*: A Logic for Hyperproperties in Multi-Agent Systems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "19, Issue 2", "Issue": "", "Page": "", "JournalTitle": "Logical Methods in Computer Science"}]}, {"ArticleId": 111964860, "Title": "A New Mixed Fractional Derivative with Applications in Computational Biology", "Abstract": "<p>This study develops a new definition of a fractional derivative that mixes the definitions of fractional derivatives with singular and non-singular kernels. This developed definition encompasses many types of fractional derivatives, such as the Riemann–Li<PERSON>ville and Caputo fractional derivatives for singular kernel types, as well as the Caputo–Fabrizio, the Atangana–Baleanu, and the generalized Hattaf fractional derivatives for non-singular kernel types. The associate fractional integral of the new mixed fractional derivative is rigorously introduced. Furthermore, a novel numerical scheme is developed to approximate the solutions of a class of fractional differential equations (FDEs) involving the mixed fractional derivative. Finally, an application in computational biology is presented.</p>", "Keywords": "", "DOI": "10.3390/computation12010007", "PubYear": 2024, "Volume": "12", "Issue": "1", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Equipe de Recherche en Modélisation et Enseignement des Mathématiques (ERMEM), Centre Régional des Métiers de l’Education et de la Formation (CRMEF), <PERSON><PERSON>hale<PERSON>, Casablanca 20340, Morocco ; Laboratory of Analysis, Modeling and Simulation (LAMS), Faculty of Sciences Ben M’Sick, Hassan II University of Casablanca, <PERSON><PERSON>, Casablanca P.O. Box 7955, Morocco"}], "References": [{"Title": "A New Generalized Definition of Fractional Derivative with Non-Singular Kernel", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "2", "Page": "49", "JournalTitle": "Computation"}, {"Title": "On the Stability and Numerical Scheme of Fractional Differential Equations with Application to Biology", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "6", "Page": "97", "JournalTitle": "Computation"}]}, {"ArticleId": 111964868, "Title": "Learning group-wise spatial attention and label dependencies for multi-task thoracic disease classification", "Abstract": "This paper considers the multi-label thoracic abnormality classification with chest X-ray images. In clinical settings, Chest X-ray imaging is a general diagnostic tool applied to visualize numerous thoracic pathological changes. While deep learning techniques have been extensively tested in this field, certain challenges persist. The data in existing thoracic abnormality datasets is insufficient, and some diseases are extremely imbalanced. Meanwhile, the dependencies between different labels are often ignored. To tackle these issues head-on, this paper introduces two crucial modules: the group-wise spatial attention (GWSA) module and the label co-occurrence dependency (LCD) module, integrated with DenseNet121 backbone. Specifically, GWSA enhances the spatial features within distinct groups while keeping the between-group feature discrimination. LCD models the correlations between different thoracic abnormalities to refine the predicted probabilities. In conjunction with the DenseNet121 backbone, these two modules reach an average AUC score of 0.818 on Chest X-ray14 dataset, achieving state-of-the-art. Source code is available at https://github.com/YujiaKCL/Chest-Xray14-GWSA-LCD .", "Keywords": "Thoracic abnormality ; Multi-label classification ; Convolutional neural networks ; Spatial attention ; Label dependency", "DOI": "10.1016/j.neucom.2023.127228", "PubYear": 2024, "Volume": "573", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering, King’s College London, Strand, London, WC2R 2LS, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Lam", "Affiliation": "Department of Engineering, King’s College London, Strand, London, WC2R 2LS, United Kingdom;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Engineering, King’s College London, Strand, London, WC2R 2LS, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering, King’s College London, Strand, London, WC2R 2LS, United Kingdom"}], "References": [{"Title": "Multi-label chest X-ray image classification via category-wise residual attention learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "259", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 111964910, "Title": "An Event-Centric Knowledge Graph Approach for Public Administration as an Enabler for Data Analytics", "Abstract": "<p>In a continuously evolving environment, organizations, including public administrations, need to quickly adapt to change and make decisions in real-time. This requires having a real-time understanding of their context that can be achieved by adopting an event-native mindset in data management which focuses on the dynamics of change compared to the state-based traditional approaches. In this context, this paper proposes the adoption of an event-centric knowledge graph approach for the holistic data management of all data repositories in public administration. Towards this direction, the paper proposes an event-centric knowledge graph model for the domain of public administration that captures these dynamics considering events as first-class entities for knowledge representation. The development of the model is based on a state-of-the-art analysis of existing event-centric knowledge graph models that led to the identification of core concepts related to event representation, on a state-of-the-art analysis of existing public administration models that identified the core entities of the domain, and on a theoretical analysis of concepts related to events, public services, and effective public administration in order to outline the context and identify the domain-specific needs for event modeling. Further, the paper applies the model in the context of Greek public administration in order to validate it and showcase the possibilities that arise. The results show that the adoption of event-centric knowledge graph approaches for data management in public administration can facilitate data analytics, continuous integration, and the provision of a 360-degree-view of end-users. We anticipate that the proposed approach will also facilitate real-time decision-making, continuous intelligence, and ubiquitous AI.</p>", "Keywords": "", "DOI": "10.3390/computers13010017", "PubYear": 2024, "Volume": "13", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business Administration, University of Macedonia, 54636 Thessaloniki, Greece; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business Administration, University of Macedonia, 54636 Thessaloniki, Greece"}], "References": [{"Title": "Public Service Models: A Systematic Literature Review and Synthesis", "Authors": "ALEXANDROS GERONTAS; VASSILIOS PERISTERAS; EFTHIMIOS TAMBOURIS", "PubYear": 2021, "Volume": "9", "Issue": "2", "Page": "637", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Enhancing Core Public Service Vocabulary to Enable Public Service Personalization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "5", "Page": "225", "JournalTitle": "Information"}]}, {"ArticleId": 111965019, "Title": "Hourly load prediction based feature selection scheme and hybrid CNN‐LSTM method for building's smart solar microgrid", "Abstract": "The short‐term load prediction is the critical operation in the peak demand administration and power generation scheduling of buildings that integrated the smart solar microgrid (SSM). Many research studies have proved that hybrid deep learning strategies achieve more accuracy and feasibility in practical applications than individual algorithms. Moreover, many buildings have integrated the SSM on the rooftop with the battery management system (BMS) to enhance energy efficiency management. However, the traditional methodologies only processed the weather parameters and power demand information for short‐term load prediction, ignoring the collected data from SSM and BMS by the advanced metering infrastructures (AMI), which probably improved prediction accuracy. In this research, many accumulated data of building and SSM are collected before methodology implementation. Considering the diversities of accumulated parameters from SSM and BMS, an adaptive convolution neural network long short‐term memory (CNN‐LSTM) is proposed for hourly electrical load prediction. The CNN could extract the critical large‐scale input feature, while the LSTM could achieve better accurate forecasts. The Pearson correlation matrix is calculated for the feature selection scheme from different data units. The hyperparameter tuning is utilized for obtaining the optimized hybrid CNN‐LSTM algorithm. The K‐fold cross‐validation is employed for deep learning algorithm verification, which includes LSTM, GRU, CNN, and Bi‐LSTM methodologies. The results prove that the hybrid CNN‐LSTM achieved outperformed improvements, which are 20.57%, 29.63%, 19.06% in MSE, MAE, MAPE, and 21.24%, 22.02%, 3.82% in validating MSE, MAE, MAPE, respectively. The hybrid CNN and LSTM combined with the feature selection scheme achieve superior predicting accuracies, proving the adaptability ability for integrating into the energy management system (EMS) of the building's SSM.", "Keywords": "convolution neural network algorithm;deep learning machine;feature selection scheme;gated recurrent unit;hyperparameter optimization;load prediction;long short-term memory methodology;recurrent neural network", "DOI": "10.1111/exsy.13539", "PubYear": 2024, "Volume": "41", "Issue": "7", "JournalId": 5612, "JournalTitle": "Expert Systems", "ISSN": "0266-4720", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business Intelligence National Kaohsiung University of Science and Technology  Kaohsiung City Taiwan, ROC;Faculty of Economics and Management Thai Binh Duong University  Nha Trang City, Khanh Hoa Vietnam"}, {"AuthorId": 2, "Name": "Ming‐Yuan Cho", "Affiliation": "Department of Electrical Engineering National Kaohsiung University of Science and Technology  Kaohsiung City Taiwan, ROC"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering National Kaohsiung University of Science and Technology  Kaohsiung City Taiwan, ROC;Department of Electronic and Electrical Engineering Nha Trang University  Nha Trang City, Khanh Hoa Vietnam"}], "References": [{"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 111965060, "Title": "A compact multiband antenna based on metamaterial for L-band, WiMax, C-band, X-band, and Ku-band applications", "Abstract": "A novel multiband metamaterial (MTM) unit cell antenna loaded with split ring resonator (SRR) slots that resonates at seven bands, which are (1.91 GHz), (3.6 GHz), (6.25 GHz), and (8.69 GHz, 9.69 GHz, 10.70 GHz), and 12.33 GHz of the spectrum, making it suitable for L-band, worldwide interoperability for microwave access (WiMax), C-band, X-band downlink, and Ku-band applications, respectively, is proposed and discussed in this work. The proposed antenna has a very compact size of 14×15×1.6 mm 3 with an FR4 substrate. The simulation results show that the presented antenna attains a reflection coefficient of less than -10 dB (S11 -10 dB) and a radiation pattern across all operating bands. In addition, the suggested antenna provides good gains over the resonant frequency signals with an average of 6.75 db. The antenna simulations and parametric studies have been done using both computer simulation technology microwave studio (CST microwave studio) and high frequency structure simulator (HFSS) to confirm the obtained simulation results.", "Keywords": "antenna;metamaterial;multiband;split ring resonator;unit cell", "DOI": "10.12928/telkomnika.v22i1.25204", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 18407, "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)", "ISSN": "1693-6930", "EISSN": "2302-9293", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ibn <PERSON> University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassan II University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Ibn <PERSON> University"}], "References": []}, {"ArticleId": 111965118, "Title": "The Complex(ity) Landscape of Checking Infinite Descent", "Abstract": "<p>Cyclic proof systems, in which induction is managed implicitly, are a promising approach to automatic verification. The soundness of cyclic proof graphs is ensured by checking them against a trace-based Infinite Descent property. Although the problem of checking Infinite Descent is known to be PSPACE-complete, this leaves much room for variation in practice. Indeed, a number of different approaches are employed across the various cyclic proof systems described in the literature. In this paper, we study criteria for Infinite Descent in an abstract, logic-independent setting. We look at criteria based on Büchi automata encodings and relational abstractions, and determine their parameterized time complexities in terms of natural dimensions of cyclic proofs: the numbers of vertices of the proof-tree graphs, and the vertex width—an upper bound on the number of components (e.g., formulas) of a sequent that can be simultaneously tracked for descent. We identify novel algorithms that improve upon the parameterised complexity of the existing algorithms. We implement the studied criteria and compare their performance on various benchmarks.</p>", "Keywords": "", "DOI": "10.1145/3632888", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ben-Gurion University of the Negev, Beersheba, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Ben-Gurion University of the Negev, Beersheba, Israel"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Sheffield, Sheffield, United Kingdom"}, {"AuthorId": 4, "Name": "Reuben N. S<PERSON> Rowe", "Affiliation": "Royal Holloway University of London, London, United Kingdom"}], "References": [{"Title": "Automatically Verifying Temporal Properties of Pointer Programs with Cyclic Proof", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "3", "Page": "555", "JournalTitle": "Journal of Automated Reasoning"}, {"Title": "Non-well-founded Proof Theory of Transitive Closure Logic", "Authors": "<PERSON><PERSON>; Reuben N. S. Rowe", "PubYear": 2020, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Computational Logic"}, {"Title": "E-Cyclist: Implementation of an Efficient Validation of FOLID Cyclic Induction Reasoning", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "342", "Issue": "", "Page": "129", "JournalTitle": "Electronic Proceedings in Theoretical Computer Science"}, {"Title": "Cyclic Hypersequent System for Transitive Closure Logic", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "67", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Automated Reasoning"}]}, {"ArticleId": 111965121, "Title": "Revised eight-step feasibility checking procedure with linear time complexity for the Dial-a-Ride Problem (DARP)", "Abstract": "In the context of Dial-a-Ride Problems (DARPs), the eight-step feasibility checking procedure is one of the most commonly used methods. The time complexity of this procedure is considered as O ( n 2 ) in the literature. In this study, a version of this procedure is proposed with O ( n l o g Q ) time complexity. Since, in DARPs, the capacity of each vehicle is a small constant, the method introduced in this paper performs as a linear-time technique in this context. The revised scheme is just as elegant as the original one, which makes this improvement in the time complexity more noteworthy. Our proposed technique has a lower time complexity and/or is noticeably easier to comprehend and implement than the other feasibility checking methods in the literature. Considering the standard benchmark instances, experimental results show that the revised method outperforms the classic one in terms of the computational time. Moreover, it is demonstrated that the execution time of our algorithm is competitive with that of the only technique with linear time complexity in the literature.", "Keywords": "", "DOI": "10.1016/j.cor.2024.106530", "PubYear": 2024, "Volume": "164", "Issue": "", "JournalId": 1828, "JournalTitle": "Computers & Operations Research", "ISSN": "0305-0548", "EISSN": "1873-765X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Engineering and Information Technology, Shiraz University, Shiraz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Engineering and Information Technology, Shiraz University, Shiraz, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Engineering and Information Technology, Shiraz University, Shiraz, Iran;Corresponding author"}], "References": [{"Title": "A hybrid algorithm for the multi-depot heterogeneous dial-a-ride problem", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "", "Page": "105196", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 111965137, "Title": "Reseña: El trance de entre la administración a la gestión educativa", "Abstract": "<p>Esta reseña del capítulo uno, El trance de entre la administración a la gestión educativa, del libro Liderazgo, Gestión, Administración e Investigación Educativa: Una Visión de Futuro, escrito por el Doctor Benja<PERSON>, resalta de manera importante la transición de administración a gestión educativa, el capítulo sigue un desarrollo acorde a su estructura, realizando un análisis desde una perspectiva profunda de conceptos, llegando a conclusiones objetivas sobre lo que implica la gestión educativa y lo que la administración puede aportar para su desarrollo y éxito en lo que a su área compete. El objetivo de esta obra es proporcionar un concepto bien definido sobre gestión y educación, desarrollando una visión correcta sobre la dirección de instituciones educativas. Este libro y en particular el capítulo uno es ampliamente recomendable para todos aquellos universitarios, quienes estudian algún posgrado y para los que actualmente ocupan algún puesto administrativo en cualquier nivel de institución educativa.</p>", "Keywords": "Administración;Gestión educativa;Empresa", "DOI": "10.29057/est.v9i18.10892", "PubYear": 2024, "Volume": "9", "Issue": "18", "JournalId": 52418, "JournalTitle": "Boletín Científico INVESTIGIUM de la Escuela Superior de Tizayuca", "ISSN": "", "EISSN": "2448-4830", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Universidad Linda Vista"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Linda Vista"}], "References": []}, {"ArticleId": 111965215, "Title": "“I don't think we're there yet”: The practices and challenges of organisational learning from cyber security incidents", "Abstract": "Learning from cyber incidents is crucial for organisations to enhance their cyber resilience and effectively respond to evolving threats. This study employs neo-institutional and organisational learning theories to examine how organisations learn from incidents and gain insights into the challenges they face. Drawing on qualitative research methods, interviews were conducted with 34 security practitioners from organisations operating in the UK spanning a range of industries. The findings highlight the importance of consciously evaluating learning practices and creating a culture of openness to hear about incidents from employees, customers and suppliers. Deciding which incidents to learn from, as well as who should participate in the learning process, emerged as critical considerations. Overcoming defensiveness and addressing systemic causes were recognised as barriers to effective learning. The study emphasises the need to assess the value and impact of identified lessons and to avoid superficial reviews that treat symptoms rather than underlying causes to improve resilience. While progress has been made in learning from incidents, further enhancements are needed. Practical recommendations have been proposed to suggest how organisations may gain valuable insights for maximising the benefits derived from incident learning. This research contributes to the existing knowledge on organisational learning and informs future studies exploring the social and political influences on the learning process. By considering the suggested recommendations, organisations may strengthen their cyber security, foster a culture of continuous improvement, and respond effectively to the dynamic cyber security landscape.", "Keywords": "Cyber security incidents ; Organisational learning ; Post-incident review ; Cyber resilience ; Learning practices ; Lessons learned ; Neo-institutional theory ; Isomorphic pressures", "DOI": "10.1016/j.cose.2023.103699", "PubYear": 2024, "Volume": "139", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Cyber Security for Society (iCSS), School of Computing, University of Kent, Canterbury, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Cyber Security for Society (iCSS), School of Computing, University of Kent, Canterbury, UK"}, {"AuthorId": 3, "Name": "Virginia N.L. <PERSON>", "Affiliation": "Institute of Cyber Security for Society (iCSS), School of Computing, University of Kent, Canterbury, UK"}], "References": [{"Title": "The cybersecurity behavioral research: A tertiary study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "102826", "JournalTitle": "Computers & Security"}, {"Title": "Governing cybersecurity from the boardroom: Challenges, drivers, and ways ahead", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "102840", "JournalTitle": "Computers & Security"}, {"Title": "Between a rock and a hard(ening) place: Cyber insurance in the ransomware era", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "128", "Issue": "", "Page": "103162", "JournalTitle": "Computers & Security"}, {"Title": "Learning from cyber security incidents: A systematic review and future research agenda", "Authors": "<PERSON>; <PERSON>; Virginia N.L. <PERSON>", "PubYear": 2023, "Volume": "132", "Issue": "", "Page": "103309", "JournalTitle": "Computers & Security"}, {"Title": "Lessons Learned from Automated Sharing of Intrusion Detection Alerts: The Case of the SABU Platform", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "4", "Page": "1", "JournalTitle": "Digital Threats: Research and Practice"}, {"Title": "Harnessing GPT-4 for generation of cybersecurity GRC policies: A focus on ransomware attack mitigation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "103424", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 111965267, "Title": "The secret of voice: How acoustic characteristics affect video creators' performance on Bilibili", "Abstract": "The importance of voice has been well acknowledged in sensory decision-making. Yet, past literature on video creators&#x27; performance did not shed much light on the impact of video creators&#x27; acoustic characteristics. Building on signaling theory of portfolios, we examine how the acoustic characteristics of a video creator and the signals of video quantity affect the number of likes a video creator receives and the change in the number of the creator&#x27;s followers. Using a longitudinal dataset obtained from Bilibili through automated speech recognition analytics and text analysis approach, econometrics models are employed to test the research model. Findings indicate that loudness variability of a video creator significantly increases the number of likes the creator receives, and exerts a positive effect on the change in the number of the creator&#x27;s followers. In contrast, vocal pitch has a significant positive impact on the number of likes but a significant negative impact on the change in the number of followers. Results further suggest that a video creator&#x27;s performance varies according to the signals of video quantity, i.e., the number of weekly published videos and the average length of videos. Our results could offer theoretical contributions and practical insights for video creators to enhance their performance by adjusting the acoustic characteristics and the signals of video quantity wisely.", "Keywords": "", "DOI": "10.1016/j.dss.2023.114167", "PubYear": 2024, "Volume": "179", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Management, University of Science and Technology Beijing, Beijing 100083, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Business School, Nanjing University, Nanjing 210093, China"}, {"AuthorId": 3, "Name": "Qianzhou Du", "Affiliation": "School of SciTech Business, School of Management, University of Science and Technology of China, Hefei 230026, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Intelligent Operations and Marketing, Xi'an Jiaotong-Liverpool University, Suzhou 215123, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Fan", "Affiliation": "Business Analytics, the University of Iowa, Iowa, IA 52242, USA"}], "References": [{"Title": "Physician voice characteristics and patient satisfaction in online health consultation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "5", "Page": "103233", "JournalTitle": "Information & Management"}, {"Title": "Dual effects of social support seeking in patient-centric online healthcare communities: A longitudinal study", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "8", "Page": "103270", "JournalTitle": "Information & Management"}, {"Title": "Continuous content contribution in virtual community: The role of status-standing on motivational mechanisms", "Authors": "Lingfeng Dong; <PERSON><PERSON><PERSON>; <PERSON><PERSON> (Jove) <PERSON><PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "113283", "JournalTitle": "Decision Support Systems"}, {"Title": "Effects of personalization and social role in voice shopping: An experimental study on product recommendation by a conversational voice agent", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "106359", "JournalTitle": "Computers in Human Behavior"}, {"Title": "The Importance of Interactions Between Content Characteristics and Creator Characteristics for Studying Virality in Social Media", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "576", "JournalTitle": "Information Systems Research"}, {"Title": "Herd behavior in social media: The role of Facebook likes, strength of ties, and expertise", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "8", "Page": "103370", "JournalTitle": "Information & Management"}, {"Title": "Music intelligence: Granular data and prediction of top ten hit songs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "113535", "JournalTitle": "Decision Support Systems"}, {"Title": "Which voice are you satisfied with? Understanding the physician–patient voice interactions on online health platforms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "113754", "JournalTitle": "Decision Support Systems"}, {"Title": "Get your report a thumb-up: An empirical investigation on crowd testing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "158", "Issue": "", "Page": "113781", "JournalTitle": "Decision Support Systems"}, {"Title": "An exploration of the relation between the visual attributes of thumbnails and the view-through of videos: The case of branded video content", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "160", "Issue": "", "Page": "113820", "JournalTitle": "Decision Support Systems"}, {"Title": "The influence of e-commerce live streaming affordance on consumer’s gift-giving and purchase intention", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "1", "Page": "13", "JournalTitle": "Journal of Information Technology and Data Management"}, {"Title": "Can social interaction-oriented content trigger viewers' purchasing and gift-giving behaviors? Evidence from live-streaming commerce", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "7", "Page": "46", "JournalTitle": "Internet Research"}, {"Title": "Consequences of Information Feed Integration on User Engagement and Contribution: A Natural Experiment in an Online Knowledge-Sharing Community", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "3", "Page": "1114", "JournalTitle": "Information Systems Research"}]}, {"ArticleId": 111965288, "Title": "Evaluation of limiting factors for SAR backscatter based cut detection of alpine grasslands", "Abstract": "Several studies utilized C-band Synthetic Aperture Radar (SAR) backscatter time series to detect cut events of grasslands. They identified several potential factors hindering the detection: Vegetation characteristics, precipitation, and the timing of salvage of the harvested grass. This study uses a comprehensive in situ database to assess the impact of those factors on the detection rate of cut events by performing a cut detection based on Sentinel-1 backscatter time series and relating the accuracy to the potentially limiting factors. The results can be summarized in the following key findings: (i) The detection rate decreases significantly with grass heights below 35 cm and a biomass of less than 2100 kg/ha. As the grass of the first growth is typically characterized by greater height and higher biomass, first cuts achieved a higher accuracy with 85% compared to re-growth cuts with 65%. (ii) False positive cut events were related to higher precipitation amounts, but adding precipitation data to the model led only to a slight increase of the accuracy of re-growth cuts, but a decrease of the overall accuracy. (iii) No relation was found between the timing of salvage and the backscatter behaviour. These insights contribute to a better utilization of C-band backscatter for vegetation analysis and agricultural applications, including cut detection. Further research with dense in situ measurements, including Vegetation Water Content (VWC) is required to fully understand the behaviour of C-band backscatter over managed grasslands.", "Keywords": "Remote sensing ; Sentinel-1 ; Grassland ; SAR ; Cut detection ; Deep learning ; Time series ; 0000 ; 1111", "DOI": "10.1016/j.srs.2024.100117", "PubYear": 2024, "Volume": "9", "Issue": "", "JournalId": 72499, "JournalTitle": "Science of Remote Sensing", "ISSN": "2666-0172", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geodesy and Geoinformation, TU Wien, Wiedner Hauptstraße 8-10, Wien, 1040, Wien, Austria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Geodesy and Geoinformation, TU Wien, Wiedner Hauptstraße 8-10, Wien, 1040, Wien, Austria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Geodesy and Geoinformation, TU Wien, Wiedner Hauptstraße 8-10, Wien, 1040, Wien, Austria;Umweltbundesamt GmbH (UBA-GmbH), Spittelauer Lände 5, Wien, 1090, Wien, Austria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geodesy and Geoinformation, TU Wien, Wiedner Hauptstraße 8-10, Wien, 1040, Wien, Austria"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Höhere Bundeslehr- und Forschungsanstalt für Landwirtschaft Raumberg-Gumpenstein, Raumberg 38, <PERSON><PERSON>ning, 8952, Steiermark, Austria"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Höhere Bundeslehr- und Forschungsanstalt für Landwirtschaft Raumberg-Gumpenstein, Raumberg 38, <PERSON><PERSON>ning, 8952, Steiermark, Austria"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "GeoSphere Austria, Hohe Warte 38, Wien, 1190, Wien, Austria"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Geodesy and Geoinformation, TU Wien, Wiedner Hauptstraße 8-10, Wien, 1040, Wien, Austria"}], "References": [{"Title": "Feasibility of tundra vegetation height retrieval from Sentinel-1 and Sentinel-2 data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111515", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Does ASCAT observe the spring reactivation in temperate deciduous broadleaf forests?", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "250", "Issue": "", "Page": "112042", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Mowing event detection in permanent grasslands: Systematic evaluation of input features from Sentinel-1, Sentinel-2, and Landsat 8 time series", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "267", "Issue": "", "Page": "112751", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "The influence of surface canopy water on the relationship between L-band backscatter and biophysical variables in agricultural monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "268", "Issue": "", "Page": "112789", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Mapping grassland mowing events across Germany based on combined Sentinel-2 and Landsat 8 time series", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "269", "Issue": "", "Page": "112795", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Mowing detection using Sentinel-1 and Sentinel-2 time series for large scale grassland monitoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113145", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Grassland cut detection based on Sentinel-2 time series to respond to the environmental and technical challenges of the Austrian fodder production for livestock feeding", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "292", "Issue": "", "Page": "113577", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Grassland mowing event detection using combined optical, SAR, and weather time series", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "295", "Issue": "", "Page": "113680", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 111965428, "Title": "A Survey of Incremental Deep Learning for Defect Detection in Manufacturing", "Abstract": "<p>Deep learning based visual cognition has greatly improved the accuracy of defect detection, reducing processing times and increasing product throughput across a variety of manufacturing use cases. There is however a continuing need for rigorous procedures to dynamically update model-based detection methods that use sequential streaming during the training phase. This paper reviews how new process, training or validation information is rigorously incorporated in real time when detection exceptions arise during inspection. In particular, consideration is given to how new tasks, classes or decision pathways are added to existing models or datasets in a controlled fashion. An analysis of studies from the incremental learning literature is presented, where the emphasis is on the mitigation of process complexity challenges such as, catastrophic forgetting. Further, practical implementation issues that are known to affect the complexity of deep learning model architecture, including memory allocation for incoming sequential data or incremental learning accuracy, is considered. The paper highlights case study results and methods that have been used to successfully mitigate such real-time manufacturing challenges.</p>", "Keywords": "", "DOI": "10.3390/bdcc8010007", "PubYear": 2024, "Volume": "8", "Issue": "1", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University of Limerick, V94 T9PX Limerick, Ireland; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering and Enterprise Research Centre, University of Limerick, V94 T9PX Limerick, Ireland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University of Limerick, V94 T9PX Limerick, Ireland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronic and Computer Engineering, University of Limerick, V94 T9PX Limerick, Ireland"}], "References": [{"Title": "The Open Images Dataset V4", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "7", "Page": "1956", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Lifelong generative modeling", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "404", "Issue": "", "Page": "381", "JournalTitle": "Neurocomputing"}, {"Title": "Image-Based Surface Defect Detection Using Deep Learning: A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "4", "Page": "", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Sequential targeting: A continual learning approach for data imbalance in text classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "115067", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Learning With Sharing: An Edge-Optimized Incremental Learning Method for Deep Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "2", "Page": "461", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "Human-machine Collaborative Decision-making: An Evolutionary Roadmap Based on Cognitive Intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "7", "Page": "1101", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 111965445, "Title": "Ascendancy of SNS information and age difference on intention to buy eco-friendly offerings: meaningful insights for e-tailers", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJEB.2024.10061502", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 8392, "JournalTitle": "International Journal of Electronic Business", "ISSN": "1470-6067", "EISSN": "1741-5063", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 111965464, "Title": "Path tracking of varying-velocity 4WS autonomous vehicles under tire force friction ellipse constraints", "Abstract": "In this paper, a control scheme is presented to solve the problem of path tracking for varying-velocity autonomous vehicles with higher robust performance while friction force constraints of vehicle tires and ground are considered to avoid appearing the steering lost situation actively in the limit of the tire friction forces by using four wheel steering. To achieve these goals, a new expression of the tire forces and a new controlled system are proposed for the three degree of freedom vehicle model. A longitudinal input-saturation controller is designed to track the desired longitudinal velocity and attenuate the influence of lateral and yaw motions. The virtual control input method is used to design the input-saturation controller and get the desired steering angles to track the desired path for the lateral and yaw motions while a new control problem with desired forces tracking is considered to autonomously avoid the limit of lateral tire friction forces and the steering lost situation appearing. To test these results, lane changing, U-corner turning and straight-line driving for a varying-velocity vehicle are simulated with external disturbances and model uncertainties. Simulation results show that the higher robustness and precise path tracking performances are obtained.", "Keywords": "", "DOI": "10.1016/j.robot.2024.104621", "PubYear": 2024, "Volume": "173", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "Mingxing Li", "Affiliation": "The Seventh Research Division and the Center for Information and Control, School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Seventh Research Division and the Center for Information and Control, School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Seventh Research Division and the Center for Information and Control, School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191, China"}], "References": []}, {"ArticleId": 111965485, "Title": "Deep learning models for bolus segmentation in videofluoroscopic swallow studies", "Abstract": "<p>One of the benefits of the videofluoroscopic swallow study (VFSS) is the visualization of the bolus transit during the swallowing process. This X-ray imaging technique allows clinicians to observe the occurrence of penetration and aspiration of a bolus into the airway, and to characterize possible post-swallow residue. This study aims to develop and analyze deep learning models for bolus segmentation in videofluoroscopic swallow study. This study utilized various encoder–decoder-based deep learning models to automatically segment a bolus. The models were developed with 6424 VFSS images from 270 swallow studies obtained from 28 patients (15 males, mean age: 59 . 87 ± 14 . 88 years; 13 females, mean age: 57 . 08 ± 17 . 21 years) suspected of dysphagia (swallowing difficulties). The data were split at patient level with a proportion of 80%, 10%, and 10% for training, validation, and testing, respectively. Model performance was mainly evaluated by dice score coefficient (DSC) and intersection-over-union (IoU). The InceptionResNetV2 encoder in the UNet + + architecture achieved the best performance with 81.16% of DSC and 68.29% of IoU, while the inference speed was 49.34 ms per image on a designated device. In addition, the UNet + + with MobileNetV2 encoder achieved a considerably faster inference speed of 10.08 ms per image and slightly lower performance of 80.98% and 68.04% for DSC and IOU, respectively. Our study demonstrated effective and accurate methods of segmenting and tracking a bolus on all frames of VFSS exams in real time, indicating the potential to reduce human error and contribute objective analysis to early dysphagia diagnosis and management.</p>", "Keywords": "Videofluoroscopic swallow studies; Dysphagia; Food bolus tracking; Deep learning; Real-time detection; Segmentation", "DOI": "10.1007/s11554-023-01398-1", "PubYear": 2024, "Volume": "21", "Issue": "1", "JournalId": 4616, "JournalTitle": "Journal of Real-Time Image Processing", "ISSN": "1861-8200", "EISSN": "1861-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> Department of Electrical and Computer Engineering, University of Toronto, Toronto, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Head and Neck Surgery, The University of Texas MD Anderson Cancer Center, Houston, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Communication Science and Disorders, School of Health and Rehabilitation Sciences, University of Pittsburgh, Pittsburgh, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Division of Engineering Science, University of Toronto, Toronto, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Communication Science and Disorders, School of Health and Rehabilitation Sciences, University of Pittsburgh, Pittsburgh, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON> Department of Electrical and Computer Engineering, University of Toronto, Toronto, Canada; North York General Hospital, Toronto, Canada"}], "References": [{"Title": "Modality specific U-Net variants for biomedical image segmentation: a survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "7", "Page": "5845", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 111965499, "Title": "An Experimental Approach to Detect Forest Fire Using Machine Learning Mathematical Models and IoT", "Abstract": "<p>Fire outbreak is a common issue which is occurring worldwide, causing significant damage to both nature and human life. Recently, vision-based fire detection systems have gained popularity over traditional sensor-based systems. However, the detection process using image processing techniques can be tedious. In the current study, we propose a technique for fire detection that utilizes optimal convolution neural networks (OPCNN) to achieve highly accurate detection of fire images in forest. The result of proposed model is compared with two other models: CNN and J48. The proposed model performs better than these models. The proposed algorithm was trained using a dataset consisting of 755 images of fire and 244 images of non-fire, for a total of 999 images. These images were obtained from Kaggle data set. We resized and reshaped 1380 of these images for use in training and 460 images for testing. The model was trained using convolution, activation functions, and max pooling operations with different batch sizes and epoch values. The resulting model achieved an accuracy of 95.11%, with 432 out of 460 images predicted correctly. The proposed approach thus provides a highly accurate and efficient method to detect forest fire accurately for a sustainable safety world. The proposed work provides a new direction towards accurate and early detection of fire not only in forest but also in case of agriculture field, rural, urban, and many more areas.</p>", "Keywords": "CNN; OPCNN; J48; Machine learning; Fire detection; Neural network; IoT; WSN", "DOI": "10.1007/s42979-023-02514-5", "PubYear": 2024, "Volume": "5", "Issue": "1", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>ju <PERSON> University of Technology, Rourkela, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>ju <PERSON> University of Technology, Rourkela, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Odisha University of Technology and Research, Bhubaneshwar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Odisha University of Technology and Research, Bhubaneshwar, India"}], "References": [{"Title": "Object detection using YOLO: challenges, architectural successors, datasets and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "6", "Page": "9243", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 111965503, "Title": "An AI-enabled secure framework for enhanced elder healthcare", "Abstract": "The healthcare sector has been revolutionized by Information and Communication Technology (ICT), leading to increased patient life expectancy and reduced healthcare costs. In the realm of cutting-edge research, Digital Twins (DT) technology holds great promise for improving healthcare. This study introduces a new approach to securing adult healthcare data by combining the Internet of Things (IoT), DT technology, and blockchain technology. Specifically, a context-aware physical activity monitoring framework is proposed for adult healthcare, incorporating an Artificial Intelligence-inspired Convolutional Neural Network (CNN) technique to analyze real-time abnormalities in the elderly. The CNN is trained on a large dataset, learning to recognize patterns and anomalies associated with abnormal conditions or behaviors in the elderly. The framework also ensures data security through the advanced features of blockchain, employing the Reputation-based Byzantine Fault Tolerance (RBFT) method for the consortium network. Experimental simulations validate the proposed technique, demonstrating its superior efficacy compared to state-of-the-art techniques. The results exhibit betters statistical measures of Delay Latency (121.23s), Prediction Efficacy (Precision (93.357%), Specificity (93.58%), Sensitivity (94.15%), and F-measure (94.58%)), Reliability (89.62%), and Stability (71%).", "Keywords": "", "DOI": "10.1016/j.engappai.2023.107831", "PubYear": 2024, "Volume": "131", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Applications, National Institute of Technology Kurukshetra, India"}], "References": [{"Title": "Improving data hiding within colour images using hue component of HSV colour space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "1", "Page": "56", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Local binary pattern‐based reversible data hiding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "4", "Page": "695", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Secure Internet of Things (IoT) using a novel Brooks Iyengar quantum Byzantine Agreement-centered blockchain Networking (BIQBA-BCN) model in smart healthcare", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "629", "Issue": "", "Page": "440", "JournalTitle": "Information Sciences"}, {"Title": "EHDHE: Enhancing security of healthcare documents in IoT-enabled digital healthcare ecosystems using blockchain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "629", "Issue": "", "Page": "703", "JournalTitle": "Information Sciences"}, {"Title": "Blockchain for healthcare systems: Architecture, security challenges, trends and future directions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "103633", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 111965519, "Title": "SemanticFormer: Hyperspectral image classification via semantic transformer", "Abstract": "Hyperspectral image (HSI) classification is an active research problem in computer vision and multimedia field. Contrary to traditional image data, HSIs contain rich spectral, spatial and semantic information. Thus, how to extract the discriminative features for HSIs by integrating spectral , spatial and semantic cues together is the core issue to address HSI classification task. Existing works mainly focus on exploring spectral and spatial information which usually fail to fully explore the rich semantic information in HSIs . To address this issue, in this paper, we first propose a novel semantic Transformer scheme, named SemanticFormer, which aims to learn discriminative visual representations for semantics by exploiting the interaction among different semantic tokens. Using the proposed SemanticFormer, we then propose a novel heterogeneous network that contains both spectral–spatial convolution network branch and SemanticFormer branch to extract spectral–spatial and semantic features simultaneously for HSIs. Experiments on two widely used datasets demonstrate the effectiveness of our SemanticFormer and HSI classification network method. Our codes will be available in https://github.com/SissiW/SemanticFormer .", "Keywords": "", "DOI": "10.1016/j.patrec.2023.12.023", "PubYear": 2024, "Volume": "179", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei, China;Institute of Artificial Intelligence, Hefei Comprehensive National Science Center, Hefei, China;Corresponding author at: School of Computer Science and Technology, Anhui University, Hefei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Anhui University, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei, China"}], "References": [{"Title": "Land contained sea area ship detection using spaceborne image", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "130", "Issue": "", "Page": "125", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Image captioning with transformer and knowledge graph", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "143", "Issue": "", "Page": "43", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "TrSeg: Transformer for semantic segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "29", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "ResT-ReID: Transformer block-based residual learning for person re-identification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "90", "JournalTitle": "Pattern Recognition Letters"}]}, {"ArticleId": 111965574, "Title": "Efficacy of online psychoeducation and relaxation training program (OnPR) on mental health problems in COVID-19 patients: A randomized controlled trial", "Abstract": "<b  >Purpose</b> Prior studies found that the prevalence of anxiety, depression, stress and insomnia were relatively high in COVID-19 patients. This study aimed to explore the efficacy of OnPR on mental health outcomes in patients with asymptomatic or mildly symptomatic COVID-19. <b  >Patients and methods</b> We employed a randomized controlled trial following the CONSORT guidelines. The Thai Clinical Trials Registry identification number of this study is TCTR20220729003. We used a block of 4 randomizations generated by a computer program. The intervention group (n = 38) received the OnPR program, and the control group (n = 36) received care as usual. OnPR was an online psychological intervention comprising psychoeducation, sleep hygiene education and relaxation techniques. OnPR was provided by qualified therapists trained with a standard protocol. The primary outcomes were depression, anxiety, and stress, which were determined by the Depression Anxiety and Stress Scale-21 (DASS-21). Sleep quality was measured by the Pittsburgh Sleep Quality Index (PSQI). Outcomes were compared between groups at pre-intervention and post-intervention at 1, 4, and 12 weeks using paired t -test or Wilcoxon signed-rank test. In addition, a linear mixed model was employed to demonstrate the effect changes of OnPR over time. All analyses were two-tailed, with a significance level of 0.05. <b  >Results</b> Of 74 Thai participants, 89.2 % were female, and 11.8 % were male. The average age was 31 years. Participants&#x27; baseline characteristics were not statistically significant between the intervention and control groups except for depression and stress scores from DASS-21. OnPR resulted in significantly better improvement in depression, anxiety, stress, and sleep quality. The mean differences between groups of DASS-21 scores in depression, anxiety and stress at 7-day follow-up were −4.69, −3.29, and −5.50 respectively. The differences continue to be significant at 4-week and 12-week follow-ups. The mean difference between groups of PSQI at 7-day follow-up is −0.91. <b  >Conclusion</b> OnPR improved mental health outcomes, and the effect on depression, anxiety and stress lasted for at least a 12-week follow-up period. In addition, it could enhance sleep quality after the intervention.", "Keywords": "Online ; Psychoeducation ; Relaxation training ; Mental health problems ; COVID-19 ; Randomized controlled trial", "DOI": "10.1016/j.invent.2023.100705", "PubYear": 2024, "Volume": "35", "Issue": "", "JournalId": 11817, "JournalTitle": "Internet Interventions", "ISSN": "2214-7829", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Faculty of Medicine Vajira Hospital, Navamindradhiraj University, Bangkok, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Faculty of Medicine Vajira Hospital, Navamindradhiraj University, Bangkok, Thailand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Faculty of Medicine Vajira Hospital, Navamindradhiraj University, Bangkok, Thailand"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Urban Medicine, Faculty of Medicine Vajira Hospital, Navamindradhiraj University, Bangkok, Thailand"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Psychiatry, Faculty of Medicine Vajira Hospital, Navamindradhiraj University, Bangkok, Thailand;Corresponding author at: 681 Samsen Rd, Wachira Phayaban, Dusit, Bangkok 10300, Thailand"}], "References": []}]