yb
COBIT 5
CosFace
DL
ext4
AutoX
SS-EP
Mercedes-Benz
MOS
cut
dng
hx
SPSS
STAC
VISUAL
EmoEdu
kn
wb
Apache Hive
MISO
rewards
Xilinx
RPO
SMA*
Federated Learning
ld
vj
NVRAM
TCPA
ACO
Bottega Veneta
NAND
Audi
Samsung
ax
gamification
Applied computing
Q-matrix
NBC
Tissue classification
Encoder-decoder
Robotic-Assisted Surgery
Telemetry
Mental imagery
MAIRCA
testcafe
SCARA
Sliding window
Provenance
CE
Gene ontology
bcachefs
Self-attention mechanism
Laser drilling technology
ri
`iOS`
SubT
Dip-coating
CEC’2020
Indoor trusted zones
oe
WebStorm
die-casting
ROC
Domain-related features
GitLab
EmoCity
Polyvinylidene fluoride
Semimodules
Privacy by design
umts
Energy detection
ap
brain computer interface
React
SISO
Half-Life
robot with trailer
fdma
Camtasia
PDEVS Formalism
Agile process configuration
WINDOWID
Микроконтроллер
<PERSON>
av1
Fixed-wing aircraft modeling
STT-RAM
Fintech ecosystem
electrocardiogram
Mobile robots
superellipses
`ELISA`
RAM
Flash Builder
Surface plasmon
TWS
Preassigned-time synchronization
Nuxt.js
Multi-Modal Learning
Hyper-V
Air Jordan
safety-critical domains
`CNN`
SPR
Stata
Adversarial robustness
EmoMusic
wm
COPPA
Case research
IoTs
Inception
Melek IT
65D30
SysVinit
xc
Approximate software sizing methods
ResNet50V2
Ant colony algorithm
Neutron Emission
Deep drawing
ja
CHIL
Spatial communication
RBAC
Standard deviation
Topological space
Smart card
big data
Enforced cross-entropy optimization
Incidence matrix
Sensor array
dmarc
Unmanned airborne systems
Photovoltaic system
LOCPATH
Decentralization
Subjectivity recognition
TPU
od
Line
standard
rp
zq
AGWO
65K15
motor rehabilitation
computer-aided design
`PacBio`
Theoretical prediction model
TP18
Elasticity equations
FCC
68W50
Graduate IT students
Bibliometric study
HIPAA
gdb
Sales promotion
Intel/R SGX
Fast track projects
European Union
Total execution time
ECDSA
Chromatic DCT
lo
CV
HubSpot
EmoSea
MaaS
Convolutional LSTM network
`IEEE`
Pattern mining
DS Automobiles
Google Analytics
k
Cardiac short-axis MRI images
Argon2
Phase-field models
ambient intelligence
Probabilistic model
ardusub
thin-walled deep-cavity parts microassembly
Avatar
Cel-shading
Decision Tree
Perovskite materials
Module-LWE
TOR
italki
Compressed sensing
XOR
MHWs
ne
SDVANET
Bayesian statistics
Higgs
Hawthorn extract
Defense Perception
Twistor Theory
Logic gate
qn
Random Effects
zk
lg
Decay Chain
KH
error control
Quasi-species
SOT
УЗИ
Grids
RTM
Textual query
GPU
bp
RL
35R60
ITC
TripAdvisor 等。
3. **协议/标准/框架**：OAuth 2.0
Goal congruence
Society 5.0
Improved Hanalay inequality
maas
G11
tf
Configurable software
Barrier method
heic
UCQ
human–AI orchestration
MRNA
HCR
Behavioural economics
human tracking
symmetric encryption
ux
65F10）。
4. **特定模型/算法缩写**：如 SSD
Computational completeness
ORB-SLAM
Q&A
Dardennes
computer vision
STS
Garbage collection
anthracene
Induced ordered weighted averaging operators
LILO
`EM`
transformer
`TP309`
Blood banks
03B47
Expert System
Network selection
Deer hunting optimization
High-strength low-alloy steel
intrusion detection and iris
lq
shapley additive explanations
AA
AISI 8620 steel
Levenshtein Distance
DEMoS
Buckling load
DCS
Sequence diagram
Stepstone
nr
x86
PPTP
PVC
Industrial IoT
Driver Training
Sum and difference histogram
VR sickness
Flow table security
Apprenticeship Learning
SHELLOPTS
GPA
Query analysis
Mobile web service
WHO
Multi-project
Wind Environment
Influential nodes identification
Apache hive
03B42
Interaction channel
blockchain in education
Quantum Register
Boundary calculation
LSTM neural network
Preventive maintenance planning
CIoMT
MDPC
ESG-related risks
MRAM
Tanner graph
Cruise
EPROM
RCT
heart sound analysis
EmoLife
vc
00-01
Consensus Algorithm
Similarity method
Confusion Matrix
Amazon Alexa
Interface Examination
IT/IS managers
AIE
Active fire
Poisson-Boltzmann
vo
Vulnerability detection
Premiere Pro
FSS
Heroku
68T50
Platform
VNS
Low-resource neural machine translation
Granger
Conway
social application
String Theory
62R07
Three-factor authentication
intelligent instrumentation
Cu<sub>2</sub>O/CNTs
Impulse buying
D3.js
DO concentration
AppArmor
ESG integration
Deep LSTM neural network
ECS
North Face
Quantum color image encryption
Reinforcement learning control
linguistic Pythagorean fuzzy set
ReactOS
Docker
GoogLeNet
Food safety
Go
Structural inspection
Image deblurring
Simulator friendly compiler
CSA
Spark
BODIPY
Paraguay
HAZOP
Supply and demand matching
DMU
WhatsApp
HTML5
Kahoot!
drbd
OpenPose
OPSO
Elemental volume fractions
Human review fraud detection
green logistics vehicle routing
Load carriage
NaCl
Reservation
Statkraft
Effect Size
XAUTHORITY
Magic angle
Gottesman-Knill Theorem
etc.
2. **专有名词（人名、地名、品牌、产品名）**：如 Emilio Pérez Piñero
Modular Plants
consensus reaching
low-density polyethylene
Fanuc
AES
distributed file systems
DBaaS
Free-text keystroke dynamics
semantics-based clustering
NPCR and UACI
Course Scheduling
Golem Effect
obligatory necessity
DAG
Control packets
ECT
TS
Freshness
remote embodiment
IEEE 802.15.4
overlayfs
Privacy-preserving
Oliot
```

> 注：`MoS<sub>2</sub>` 是化学式，M 和 S 通常大写，不可小写。  
> `C/SiC` 是复合材料，C 和 SiC 为元素符号，不可小写。  
> `Raspberry Pi` 是品牌名，不可全小写。  
> `Oliot` 是专有系统名（Open Labeling IoT），首字母大写。

---

### ✅ **最终返回结果：**

```
AI
graphql
spiritual support
RTDE Real Time Data Exchange
Bellman–Isaacs
Air film pressure field characteristics
OMG
STM32
sm
Lisp
Marc Jacobs
Security-Aware EDA
Tensile properties
Zone control
HL7
Dirac Equation
HL7 FHIR
Signal theory
APGDF
EDR
Public opinion
Football video analysis
impulse response
Risiko
ua
Feature weights
N20
SQUID
Information society
Resnet-152
avif
Data-driven optimization
Lyndon words
PROMETHEE Method
76D07
to
ES11
Administrative censoring
EKF-SLAM
Logistic Regression
health
relation between spin angular momentum and orbital angular momentum
Tragedy of the anticommons
искусственные нейроны с бинарными и троичными выходными квантователями
BASH_CMDS
ProKNN
Vector quantization
Design science
Moore–Penrose
`PyTorch` 不在列表中，但类似 `ConvNet`
GEE
Multi-material
grey wolf optimizer
piggyback transportation
adoption model
Security-Analysis
Absolute invariant
Laser-induced etching processing technology
Ground point classification
cybersecurity insider threats
R&D
Visual perspective
membership function
FMEA
TiO<sub>2</sub> hierarchical spheres
Ssimilarity measure
Random Signal processing
SHA
Accuracy evaluation
Taxonomic identification
substantial self
V-Net
Agile management
Compressive strength
y
exact reduction
ox
dn
book publishing research
OPNsense
Adam
Optimal configuration
Taylor
yu
Al<sup>3+</sup>
Affectiva
Minority carriers
Feature processing
Solomonoff Induction
TCP
Generative adversarial networks
km
RINA
Satellite imagery
Automatic drawing of hippocampus
Graph kernels
NR
ECCM
Pair-wise learning
spring installation
Child–computer interaction
systemd
germination test
watchOS
ATSF
Online accommodation platforms
ES10
Incrementality
HuBERT
DELTA
InceptionV3
sensor network
icmp
Corner singularities
UMC

---

**说明**：以下类型的词汇被认为是“不能转为小写”的专有名词、缩写、技术术语或品牌名称，因此保留原大小写格式：

1. **标准缩写**：如 AI
Ferrari
MFGs
Inconel 718
scrambling
Félix Candela
BASH_ENV
MIT OpenCourseWare
Matching And Recognition
integral equations
Art gallery problem
IFML
career development
Whole genome sequence
Infrared
microfluidics
Marketplace
ISSN
Fuzzy possibilistic C-means
Useful reviews
СНС
Stochastic geometry
Yii
CAPTCHA
PID**
hfs+
test smells
QCM-D
Attention mechanisms
choice capacity
Tablet
external X3D prototyping
Topological Insulators
SLB
Google Summer of Code
SDN-based platform controller (POX)
Time dimension
Gaussian-based optical networks-on-chip
Metcalfe’s Law
SimCLR
wdio
Multiagent systems
MASINT
Human head detection
cloud architecture
Dr. Martens
Coda
Stereo
firefly
`Blockly`
global assays
Multiple storage zones
Kyber
Nash equilibrium
Stochastic nonlinear systems
Muon Decay
Super resolution
young children
Metaheuristic algorithms
MSP-Podcast
jh
RPS
Si
Substrate specificity
Data-driven Science
SPCA
Multi-modalities
IOT
articial intelligence
go
DSML5
Volkswagen
Black Widow Optimization
ambient assisted living
Tommy Hilfiger
Xen
Color Code
MSSP
Counterfactual Reasoning
Noncommutative Geometry
McEliece 等。
8. **数学/计算复杂性类**：如 Σ₂P-completeness。
9. **模糊/部分大写术语**：如 Sms.（保留句点与大写 S），应视为特殊写法。
10. **技术编号/代号**：如 UMC 180 nm
au
65N99
Mammogram
nq
sysfs
Expectation-Maximization
conference
Machine learning with rejection
Skin effect
fp
mn
Sustainable farming
Neural style transfer
Type II Error
tasks
pb
Nocebo Effect
fold
Multi-level features
regenerative braking
SkipGram
zfs
Land Rover
Weights allocation
ec
`TLS`
Blazor
RO
PSD2
XaaS
CBC MAC
NIST standard
Blended learning
Electrochemical detection
Information fusion
M48
ea
Dongfeng
RCEPS Real Cepstral Coefficients
oh
Tungsten trioxide
IMEI
licensing
Baum-Welch Algorithm
Ti6Al4V
alexnet
OET
Kharitonov
Adjuvant Tamoxifen-treated
x
Akamai
HEV
Bagging Ensemble
Hypergraph neural network
Coyote
JournalType
Fuzzy geometric logic
Blog credibility
self-presentation
MobileNet
Satellite Images
SNMP
Social media influences
Gaussian SMOTE
Baidu
Quantitative Investment
Scopus
Cellular automaton
ASIP
DAO
Systematic literature review
attended home delivery
Visible Area
Dual-image
je
Unstructured moving Chimera grid
CQ
M150
regression modeling
Axis shape
AVISPA
Mobile government
LIWC
`IM`（但 `Q& A` 可能是输入错误，应为 `Q&A`）等。
- **化学/材料符号**：如 `PEDOT:PSS`
Multimodal learning
CBRAM
Resource burning
diffuser
bias
Anisotropic depth of field
UID
I2DKPCA
Peer learning strategy
Channel attention mechanism
. (tcshrc)
Radioactivity
MSDS
DaaS
YCbCr
CEV
ei
53C35
3G
Incremental Learning
on
Aib 2-aminoisobutyric acid
G17
Liouville Equation
returns
Arthrobacter
up
RotNet
PBKDF2
68T02
Hector SLAM
bn
bx
Maserati
DeepID
TCMRAM
QSP
payment gateway
LSTM）
- 人名、地名（如 Emilio Pérez Piñero
Cross-Modal Learning
Dual-EM
Reebok
3D lidar
jxl
hi
`kubernetes`
DNA
```

> ✏️ 注：为简洁与合理性，部分重复项（如 PSO）仅保留一次；同时，像 "BiLSTM" 出现多次，统一保留标准缩写形式。

✅ **最终答案**（仅保留真正不能转为小写的术语，去重，标准形式）：

```
AI
FV functional verification
Yolov4-tiny
GPUs
N6-methyladenosine
YOLO
Postel’s Law
oz
caliper
CAM
ak
DragonFly BSD
as
CloudStack
NA
LDP
Cupra
TF-IDF
Quora
NMT
lean
qe
nice
Stack Overflow
CAE
yy
AR technology5
`5G`
DLL
Waikato environment for knowledge analysis (Weka)
SOVA
Bokeh adapter
project management
State Estimation
Linear models
ASK
Connected Tables
HarmonyOS
PSP MOSFET
icmpv6
DevOps
zy
VOCs
certificate
yk
lp
ly
HTML
DC/DC
IRNSS
Maximum Likelihood Estimation
F1-Score
RR
49K20
POE
SNOT
U-PLS/RBL
testng
15A18
SBR
vi
Schrödinger
N-sequent calculus
PHMB
Lyapunov-Krasovskii functionals (LKF)
`Z-type` 是人名或函数名，首字母大写，但整个词不是“全大写缩写”。
- 而 `AI`
62M30
OCSVCs
PPE
GORGIA-B
ds
BIRCH
IIoT
PROMETHEE II
РЛС
IT x
Metro
IMSI
Ansible
`NoSQL`
vn
Lucy hybrid
CertTESOL
Flash
zn
LCR circuit
Selenocysteine diselenide
Haor areas
Google
Josephus scrambling
Denza
Electronic learning
KPI
ResNet50
PET
Summation-by-parts property
Harvard
ASM
ssh
hn
Blautia
RabbitMQ
Google maps
so
VggNet
DSmT
br
C52
mx
AWS
G)-codes
Von Neumann Entropy
ISPS
CSI
Hadamard inequality
DERs
Smart Manufacturing Analytics
dd
Toom-Cook
`FAQ`
Dual-input dual-output
UxNB
G4-ThT/Cu NPs
5G

然而，进一步检查：

- Kriging：正确，人名命名，首字母大写
- DFT：常以全大写使用，可保留
- PROMETHEE-SAPEVO-M1：专用方法名，保留
- GEDI L2A：NASA 数据产品名，保留
- FCVI：可能为特定术语，如在上下文中为缩写，暂保留
- LLAON：模型名称，保留
- WI-FI → 应为 Wi-Fi，但原文为全大写，仍视为不可小写
- IoT：括号内出现，保留

**但注意：题中要求“从以下关键词中找出”**，即必须是**原词整体**出现在分号分隔的列表中。

我们重新检索原始关键词中实际存在的形式：

- `IEEE 802.1Qbv` ✅
- `EMD` ✅
- `JS` ✅
- `DICOM` ✅
- `CFD` ✅
- `ECRS` ✅
- `ADAMS®` ✅
- `OBR` ✅
- `PN` in `PN guidance law` ❌（不是独立词）
- `YOLOV5` ✅（独立出现）
- `H ∞ Norm` → 原文是 `H ∞ Norm`？查无，原文是 `H ∞ Norm`？实际无
- 实际列表中是：`H ∞ Norm` 不存在，原文是 `HJB variational inequalities` → 错误排除
- `SPIN` ✅
- `NTLBO` ✅
- `DFT` ✅
- `Kriging` ✅
- `PROMETHEE-SAPEVO-M1 method` → `PROMETHEE-SAPEVO-M1` 是词的一部分
- `GEDI L2A` ✅
- `FCVI` ✅
- `RAD` ✅
- `LLAON` ✅
- `WI-FI` ✅（原文为 `WI-FI`）
- `IoT` in `(IoT)` → 但关键词是 `internet of things (IoT)`，整体不是独立词
- `5G` in `(5G)` → 同上

所以，**必须是完整关键词且包含大写不可变形式**

再看哪些是**作为一个独立关键词整体出现，且含不可小写部分**：

✅ 保留的独立关键词（不能转小写）：
- AI
- IEEE 802.1Qbv
- EMD
- JS
- DICOM
- CFD
- ECRS
- ADAMS®
- OBR
- YOLOV5
- SPIN
- NTLBO
- DFT
- Kriging
- GEDI L2A
- FCVI
- RAD
- LLAON
- WI-FI

⚠️ 排除：
- `PROMETHEE-SAPEVO-M1 method`：虽含专用名，但整体作为方法描述，且非独立缩写关键词
- `H ∞ Norm`：未直接出现
- `IoT`：作为 `(IoT)` 出现，但关键词是 `internet of things (IoT)`，整体可转小写
- `5G`：在 `fifth generation (5G)` 中，非独立项

尽管 `IoT` 和 `5G` 是术语，但它们不是**独立关键词**，而是括号内的缩写说明。

因此，只选取**作为一个完整关键词项、且自身包含不可小写形式的词汇**

最终确认列表：

```
AI
Halbach arrays
Seesaw
Skipjack.
Tom Ford
LIViT
DGA Detection
Jackknife
CFS
MOSFET
Detectron2
tincan
His-tag
fuse
Power Analysis
sed
BECV
el
kWh Meter
Wireless Charging
z/OS
UL
Cross Site Scripting (XSS)
elearning
hand-eye calibration
```

等等——**这个列表太大了，很多不应列入**。

---

### ❌ 重新修正策略：我们只提取 **明确不能小写的缩写/专有名词**，而不是整词组。

#### 应只提取：
- 缩写：如 AI
GPS
Edge
Microsoft Teams
Hypothesis Testing
NIO
Amazon Linux
tracefs
Einstein Field Equations
Tesla
bdd framework
BASH_COMMAND
фильтр Калмана
```

> 注：
> - 展开缩写如 `YOLO`（来自 You Only Look Once）只保留 `YOLO`
> - `GSSR process` → `GSSR`
> - `Edge based sorted pixel value difference (ESPVD)` → `ESPVD`
> - `gru` → 更正为 `GRU`（规范形式）

---

### ✅ 最终输出（分号分隔）：

```
HHO
Six Sigma
INFOPATH
`Sato Grassmannian`
DETCS
EmoBrain
GPGPU
Data2Vec
kj
Non-holonomic systems
Allosteric
TDD
NP-completeness
Penyakit Jantung Koroner
Digital Transformation (DT)
ActiveMQ
MVC
NASA
TN911.7
Vehicle-to-Grid (V2G)
Electoral Districting
Flavonoids
ESM
IRA
Convolution Neural Network
PHY
Theory of computation: discrete optimization
Aston Martin
yv
GID
FMI/FMU
Vehicular ad hoc network (VANET)
Artificial Bee Colony
QUBO
DB index
EmoTac
ITU-R
K-means
TypeScript
RMSprop
FWI
Multilevel quantum systems
MRTM
AI Act
HTTP
Wey
COBOL
HPC
Residual networks
Jensen-Shannon Divergence
K-Mean
LLM
U-Net
DEAR
Q-Learning
zi
Lucid Charge
SCA
Kata Benda
HOSTFILE
`D91`
Daidzein
LPDDR
SMTP
yj
MVA
PMSM
MoS2
IO
hb
FaaS
Reader
Hawkes
TBI
`Ethereum`
India
SQLite
D2D
5G
```

但必须严格基于**是否出现在原始列表中**。

**最终判断：仅保留实际出现在列表中的术语，并确认其不可小写**。

逐个验证，正确答案为：

👉 **AI
RS
Hausdorff Distance
Evans
New Revised Group DEMATEL method
Z-Wave
RFI
`Bayes`
Realme
CT segmentation dual-energy computed tomography
. (login)
SAP S/4HANA Implementation
Bundle Adjustment
FCU
. (logout)
Few-Shot Learning
68Q32
Lebesgue
EMG
GSM-SIM808
VM
Workplace
Operations Research
FVC
kWh
daas
RANDOM
X-ray photoelectron spectroscopy (XPS)
JSP
MSE
Euclidean
sf
Instagram
```
GLONASS
5G-U
3D nanostructure
`DTsEB`
Programmable Controllable Cellular Automata (PCCA)
anacron
3D Technology
VLSI
Bernoulli
PM<sub>2.5</sub>
Baby-G
FY-3E/MERSI-LL
FMR
Imitation Learning
Telegram
AR
Microsatellites
esports
Standard Model
K-Means
yc
OBL
uefi
Long range sensing
Linear matrix inequality
mg
D-MSP
Chakra UI
gx
Symfony
csv
DRaaS
MVO
Weakly Supervised Learning
uq
Barlow Twins
KakaoTalk
PSEUDO
API
pkill
N-S
compliance
qz
etsi
ms
FD untrusted relay
`MAC`
Type I Error
FACS
MAE
Notion
XML
IoT Platforms
NIO Power
CLACSOON
Pareto Principle
IPMSM
DCM
fo
`00A20`
Quantum Gate
Smartwatch Charging
Televisión local
TNBC
ZTNA
ReRAM
CEC2019
CELPIP
Stigmergic
UCR Archive
VGG
Gmapping
базовая траектория
STM32F103C8T6
Dirac Notation
Toric Code
gherkin
UniSpeech
yaml
Pyplot
PyTorch
zg
wp
P2P
Fuchsia
SPH
IVI Exploit
PO
FORSCHUNGS-METHODE
многокритериальный статистический анализ
CAD model parameters
RGB
r
90C15
HOMER
VGA
batch processing
XOR gate
CDF of SINR
AMTI
Moore-Penrose
ka
wc
Ovirt
mkdir
Backbone
GMTI
CD-ROM
Vue.js
NAT
M31
heif
CIoU loss
helpdesk
Qualcomm Halo
Zend
tm
XLSR
wt
Vlasov Equation
`MCC`
`IDW`
V2V
NL
ba
zx
11B83
Iptables
syslog-ng
Apache
TZSTRING
Imperialist Competitive Algorithm
CEM
ceph
`AWID`
SIF
search
NOR
`SQL`
nef
wg
RPM
Surface Code
Software-Defined Networking
BN
IEG
TOA
GPU computing
SBML
Geodesic Distance
Longines
FMD
Alex net
ECG signals
едги 디바이스
HP/LP NEURONS
vxfs
NSFW
MAILPATH
http2
fu
Extended Kalman Filter
Actor-Critic
CRYSTALS-KYBER
FPGA**
Gucci
HR
tdma
SMOTE
sip
`SAR`
stun
EmoSocial
GDELT
SpeechT5
Trp tryptophan (W)
SVM algorithm
Cplex
GSM
touch
76B15
hw
After Effects
SV
CDMA
na
Memrise
`UHD`。但本题关注的是“词汇”，4K 更像符号，不列入。
- `ZAD control technique`：ZAD 可能是 Zero Average Dynamics，缩写，但不常见，未广泛标准化 —— 保留怀疑，但未广泛认可，不列入。
- `Grain 128a`：Grain 是流密码，128a 是版本，类似 AES-128，但 “Grain” 首字母大写即可，其余可小写，但“Grain 128a”作为一个名称，其中“Grain”必须大写，但整体不是全大写缩写，但作为专有加密算法名，应保留。不过仍属首字母大写名，非“不能转为小写”的全大写类。
- `AdaBoost`
Cambridge
elt
TinyOS
iPod
Mac mini
sis
SLES
NLU
yx
fj
du
Confluence
tcp
UTAUT
vx
YANG
EmoTime
Prometheus
PRAM
cmi5
Genesis
BaaS
VCX
NOx
ha
GEMEP
ICD-9
Vivo Charging
mpeg
M1FP
Cosine Similarity
hrms
SPHinXsys
jffs2
C-UAS
Breitling
TN92
Kalman Filter
TCFA
ya
methodology
Nyquist Rate
Spectral Clustering
dbunit
N20 等。
5. **数学/理论命名含大写或特殊符号**：如 Caputo fractional derivative（“Caputo”为人名），Kohn–Sham（“Kohn”和“Sham”为人名）
6. **语言中本身非拉丁字母的词汇**：如俄语词汇 “периодические решения” 和 “паросочетание” 以及中文“带错误学习问题”和“искусственные нейроны...”等。这些不是英文，无法用“小写”概念处理，但因其非英文，也不应被“转为小写”，故应保留原样。

---

### 🔍 逐项分析（仅提取不能转小写的部分）：

我们关注的是 **整个表达中是否有不可小写的词**，并提取这些词。例如：
- “Random forest regression (RFR)” → **RFR**
- “Analytic hierarchy process (AHP)” → **AHP**
- “DDQN Double Deep Q Learning” → **DDQN**
- “Emilio Pérez Piñero” → **Emilio Pérez Piñero**（人名）
- “Paraguay” → 地名
- “68W50” → 分类编号
- “Apache hive” → **Apache Hive**（品牌名，Hive 通常大写）
- “ardusub” → 项目名，全小写但为专有软件，应保留（虽然小写，但不可随意“转为小写”意味着“不能动”）
- 但严格来说，我们只找**含有大写字母且不能改为全小写**的词。

---

### ✅ 最终提取原则：
只提取以下类型：
- 由大写字母组成的缩写（如 AI
BLDC
MWCNT
OL
TLX
mv
M14
D2L
JSR
kh
Thomas
DataCamp
XRD
bb
hp
C#
Facebook Ads
GNN
NMBC
freeCodeCamp
accounting
yz
YOLO V4
XDR
Consul
Poisson
RTCP
Chopard
Entropy
OAuth
GRU
wh
MACHTYPE
BraTS
SSD
bm
Bentley
EmoFood
NO<sub>2</sub>
ci
fw
BASH_SUBSHELL
SDP
`Giraffe kicking algorithm`
kc
$2
it
SLR
qy
gm
png
`UAV`
Duolingo English Test
WEDM
SCSI
RML-FNML
Bi-LSTM
DRDoS
LingQ
Sonic Hedgehog
AIC
PA
dx
customer service
devops
ty
Harris Hawks Optimization
SignalR
source
VTOL
EGaIn
`Te reo Māori`（Māori 有特殊字符，但主要是专有名词）。
- **编号/分类代码**：如 `03E72`
OSM
yw
fy
FO
UEM
fh
FAQ
ColdFusion
dl
`Zemu glacier`
65L80
Safari
Audemars Piguet
Trust Region
SSH
SIMD
$-
CUDA
`PRNU` 等只保留一次。  
> 使用 HTML 标签如 `CO<sub>2</sub>` 也保留原格式，因其为结构化表达。

---

✅ **最终答案**（仅保留唯一且符合要求的术语，分号分隔）：

```
AI
UNICEF
SOAR
CFL
HAPS
Convolutional Code
Google Meet
РТК
MCC
playwright
OCaml
BCO
Firefox
GEDI
wiki
TERM
65D18
ej
Arch Linux
Lucidchart
iOS
xe
lti
EmoPlant
VR
head
`47B35`
RaaS
Urdu
EmoMind
GF
Intel
Flask
tt
naas
PTAM
Scala
GMAW-P
ONF
LCD Codes
CIP
uh
CycleGAN
ao
MILP
Fresco
SELinux
Hick’s Law
RAVDESS
bl
paas
TEFL
uf
IMAP
Jaeger LeCoultre
Pro Trek
Radon Transform
AHP
Quantum Operator
fg
vq
Johnson's Algorithm
CEDRAM
Distributional RL
BERTopic
ra
ac
Particle Swarm Optimization
qp
FRArticleRecaptchaSettings
Multi-Task Learning
I2C
ecommerce
LDL-c
ftp
fe
AP
Chrome
SWG
fm
zl
Bohr
RECOLA
Northwest corner method
PI PUF
SSVEP
`Cole-Cole`
Renyi
su
BLEU
Thermodynamic Entropy
JWT
CCS
oy
OCR
Edmodo
Shortest Path
edX
QM/MM
[email protected]
SDIN
BASH_SOURCE
iu
zo
video
lowband
vp8
Red Hat
bd
no
Gauss
REM
EV
crm
Jetty
AADL
Lookahead
GRDIv1
VANETs
nw
ROV
zs
`kubernetes`（虽然通常小写，但作为专有名词，有时保留首字母大写或全大写形式）。
12. **已约定俗成的术语**：
    - `Explainable AI`
SFTP
P232
Kruskal's Algorithm
GCN
L-BFGS
wav2vec 2.0
ct
LLVM
`Instagram`
ga
3D XPoint
Tomcat
SNP
me
Chuck Taylor
NeRF
Accelerated Failure Time Model
LLaMA2
CARESSES
AU
Beta Decay
qo
tp
Polo Ralph Lauren
LOGNAME
aw
`CVSS`
vk
Marvel
Policy Gradient
`GPU`
`Diabetes`
qemu
LIBS
easymock
MAILRC
PHMSA
Xamarin
EmoCoach
jj
БД
EmoAI
BPMN
Maxwell's Equations
TSFD
Conductive Charging
chgrp
PHI
Vultr
Kresling
zb
Crackmeter
Babbel
North American Industry Classification System (NAICS)
Pr
CSS
GRL
Fusion Reactor
Unscented Kalman Filter
og
TOPSIS
YOLOv8
xk
LDPC Code
BWT
Transformer
LI strategy
mMTC
Zeplin
CEA
mqueue
qv
GA
sp
H-bridge
HHD
Random Forest
FNMR
`WAAM`
$0
MMC
LESS
kd
SIR
DTDWT
Kendall Correlation
bc
RTU
Chloroform（虽可小写，但学名中首字母常保留）
- **设备/硬件**：如 USB
cu
PER
SSIMilarity measure
hm
hc
Qudit
BASH_REMATCH
EfficientNetB0
Klasifikasi
postman
ht
gt
Tokamak
Per3
D-NLP
ДНК
JAMMU & KASHMIR
Swift
. (bash_login)
CIDOC
Edifice
mTOR
Bootstrap Filter
Aurelia
Linux
Bloch Sphere
SM
44A60
fd
SPOJ
VSNET
AirPods
PID 等均为标准缩写，通常保持大写。
- **编程语言/平台/框架**：C++
AMC
Turing
Metropolis-Hastings Algorithm
EmoRec
sw
EmoBot
Central-processing-unit
t
CT
SARSA
ISP
M-Theory
Aperio
gn
hf
referrals
IIC
以下是从提供的关键词中筛选出的**不能完全转为小写的词汇**（即包含专有名词、缩写、编程语言、特定术语等无法或不应全小写的表达），并以分号分隔返回：

AI
DY
5G NR
FFOA
bo
bq
Subcontractor
xr
h
NFV-MANO
SD
EEPROM
Svelte
Arduino
SVD++
3gp
RISCV
EmoFog
KaiOS
TBA
RBL
Hafnium aluminum oxide
NDA
O231
securityfs
Nagoya Protocol
MWh
GSO
NVIDIA
ARIMA
gv
xi
Firebase
vg
Paraguay
```

---

### ✅ 说明：
- `MoS2` 是二硫化钼标准写法（Mo为钼，S为硫），不可全小写
- `Caputo`、`Kohn–Sham` 是人名，首字母大写不能改
- `D-MSP`、`Module-LWE` 等为论文标准命名
- `XAI`、`AHP`、`RFR` 等为缩写
- `68W50`
PTE General
NLP
Loopy Belief Propagation
H&E Hematoxylin and Eosin
PKI
jd
ok
CPW
Automatic speech recognition
`SVM`
`TiBw/TC4`
da
RS Raman spectrometer
Poisson Equation
EnChroma
CKD
Axure
PS3
Salesforce
LeetCode
sv
rev
jw
EmoMist
DeepFace
ov
EmoCar
Iterative Detection
yo
Fast Charging
Google Colab
Modified Z-type
MoS2（二硫化钼，标准写法），CP (circularly polarised)
NFC
LLAON
TPM
Koa
Quantum Hall Effect
Jenkins
Belief Propagation
PEDOT:PSS
Bahasa Jawa Ngoko
Information Entropy
Source Coding
`Azure Kinect DK`
KMLP
bounce rate
Hamilton
sc
xo
mb
Ti<sub>3</sub>C<sub>2</sub> MXene/SnS<sub>2</sub> nanocomposites
6G
煩わしさ
sg
KET
qm
Instrumental Variables
ML
ITO
DINO
BOM
tvOS
GRUB
ACID
ASIC
NAD(+)
Zermelo
Hierarchical Clustering
LDA
Wavelet
Lion
NCA
gif
kanban
Firewall
CPE
Z-Transform
HMM
Children's Emotional Speech
CO
JPEG
$_
FSSs
EmoSpace
cae
SOP
QR Code
GFM
A* Search
COMSOL Multiphysics
SSR
cypress
Audition
IEEE
wo
sctp
XPeng
fs
"Q-learning" 等通用领域中保持首字母或全大写格式，视为不可转小写。

最终输出中已去重，并按首次出现的规范形式保留。

✅ **返回结果（去重、规范、分号分隔）**：

```
AI
BIDLSTM
URLLC
DCA
WaaS
HiL
BASH_VERSINFO
`CNC`
SUSUPLUME
rm
CA/B Forum
SARM
Log-MAP
Chef
Riemann
kl
IoT-DDoS
ab
90C60
af
C11
SIoT
dmesg
arw
PLMs
`Gronwall`
bim
pv
SBAC
ph
NS-2
`NSL-KDD`
YOLOv8s
ug
XMPP
rx
BDD
hr
DPL
RTC
видеонаблюдение
EmoChat
MoS<sub>2</sub>
VPN
fMRI
OneM2M
Local Search
3DMM
nv
analytical data
logrotate
CRUD
USERNAME
CMC
Yangtze River delta
ORA
SiLago
Vault
ym
Moore’s Law
LMs
`PSNR`
tz
ADDITIVE FRICTION STIR DEPOSITION
Illustrator
Clozemaster
RetinaNet
Min-Conflicts
SDK
OWASP Security Shepherd
SAS
LOPCOW
Wasserstein Distance
Thunderbird
HUBzero
zh
ju
Moodle
CCSs
Floyd-Warshall Algorithm
GLDAS
FCL
ru
AtCoder
Primary: 65Y20
DOS
CuO nanowires/Cu2O nanostructures
AUV
RPL
jk
Feynman）
- 外语词汇

---

### 结果（去重、按出现顺序或字母排均可，此处按出现顺序整理并去重）：

```
HHO
LAMB
RTP
rw
puppeteer
IGRA
Affinity Propagation
Alibaba Charging
lw
Vehicle-to-Vehicle (V2V)
SXOR 等。
5. **机构/赛事缩写**：如 NBA。
6. **复合缩写名词**：如 RFCC
LDAP
Volvo
34K40
ITE
LAS-STACK
Sms.
SmartPLS
Xiaomi
mHealth
XDG_CONFIG_HOME
ARM
Canvas
ProM6
08A40
WNSNs
BLER
HIC
Goppa codes
Church
Heston
. (cshrc)
xy
XSS
CRM
PA-DIM™
jx
RAdam
audit
`DLT`
HOME
EQ
SH
cr
ia
Fréchet Distance
Box
76S05
Jira
aj
65D17
Kafka
ZIP
Sails.js
`CT` 等即使在句中也常保持大写，尤其是 `CT`
Tflite
Oppenheimer
Abel
Kate Spade
Oracle Cloud
Figma
BCJR Algorithm
NBA
Zigbee
65F10
xq
scrum
MAPE
Lifelong Learning
CREMA-D
InVision
gh
tail
DipTESOL
IVOCT
HS
dq
PCRAM
NSL-KDD
Dropbox
u
Zawinski’s Law
dp
EmoSnow
AIR
ma
hl
Line Search
il
om
Viterbi Algorithm
Confidence Interval
RELU
`62H25`
SMAP-IB
AdWords
transcript
Li Auto
O-RAN
GQDs
AOA
REST
social
cz
Virtex-5 FPGA
Propensity Score Matching
rk
MARC
nm
OpenID
bg
KPI's
REST API
Lagrange
Cu2O/ZnO
BYD
PSO
mw
Angular
ITIL
Distrowatch
th
ki
udp
SKU
cucumber
jf
Sunway TaihuLight
WAN
BMP
RDBMS
ul
NMF
smpte
ze
Aptasensor
Σ 2 P - completeness
Lyapunov–Foster functions
Spiking neural P systems with inhibitory rules
Quantum Error Correction
VP50IM steel
CPU
Laplacian
BP
RFCC
Twitter
Poisson Regression
qj
jr
BiLSTM-Attention-CRF
FeRAM
Nanochannel
SVC
ietf
4D printing
Lectora
ZCR
```
SPECT
vl
Boltzmann Equation
IDA*
vz
Saber KEM
Dnp3
SIM
wj
Enel X
stream processing
squashfs
C44
MP4
iz
paid
c
PR
Radiation Shielding
O31
SONOS
rv
Peugeot
POV
LBP
nx
NLM
mt
Kampo
Pauli Matrices
FLG
STBC
KPCA
NoC
SUSE
MEMS
EDA

分析说明：

我们要找出**不能转为小写的词汇**，这些通常是专有名词、缩写术语或有特殊大小写规则的技术术语（尤其是全大写缩写或部分保持大写的复合词）。

逐个排查关键词中的典型不可小写词汇：

- **AI**：Artificial Intelligence，固定大写，不能转为小写 ✔️
- **GPU**：Graphics Processing Unit，全大写缩写，不能小写 ✔️
- **mMTC**：massive Machine-Type Communications，5G术语，通常保持大写 ✔️
- **PHMSA**：Pipeline and Hazardous Materials Safety Administration，政府机构缩写 ✔️
- **SCADA**：Supervisory Control and Data Acquisition，缩写保持大写 ✔️
- **CNN**：Convolutional Neural Network，技术缩写 ✔️
- **BPNN**：Back Propagation Neural Network，术语缩写 ✔️
- **LDA**：Linear Discriminant Analysis 或 Latent Dirichlet Allocation ✔️
- **NCA**：Nominal/Non-negative Component Analysis 或 Network Component Analysis ✔️
- **YOLOv8s**：You Only Look Once 版本名，品牌技术名 ✔️
- **BDD**：Behavior-Driven Development 或 Binary Decision Diagram ✔️
- **DPL**：可能为 Dynamic Programming Language 或 Digital Pattern Library，但缩写通常保留 ✔️
- **OneM2M**：标准组织名，保留大小写 ✔️
- **INLA**：Integrated Nested Laplace Approximations，统计模型缩写 ✔️
- **TG**：Triglycerides，医学缩写，通常大写 ✔️
- **BLER**：Block Error Rate，通信术语 ✔️
- **IGRA**：Interferon-Gamma Release Assay，医学检测技术 ✔️
- **PSO**：Particle Swarm Optimization ✔️
- **ARM**：Advanced RISC Machine，公司/架构名 ✔️
- **CT**：Computed Tomography，常用于医学影像术语 ✔️
- **MRI**：Magnetic Resonance Imaging ✔️
- **EDA**：Electronic Design Automation 或 Exploratory Data Analysis ✔️
- **FPGA**：Field-Programmable Gate Array ✔️
- **Internet of thing** → 明显是 “Internet of Things”，但这里写成了 thing，不过 “IoT” 属于应保留的大写词，虽然原文未出现“IoT”，但实际是隐含概念。不过原文中是 “Internet of thing”，可能拼写错误，未显式出现 IoT，故不提取。

但注意：有些词如 `CNN Convolutional neural networks` 中的 "CNN" 已被显式写出，应视为不可小写。

进一步查看是否存在其他不可小写项：

- **UK**
GOP
`Sigmoid`
sub3
multi-states system
Stanford
Fortran
SXOR Sided XOR
egrep
LARS
CBC-MAC
SHELL
Trinity CertTESOL
s
C21
sn
Unicode
Hadamard
2G
DLP
Flash Catalyst
Cu donor material
CS
ke
Clojure
Lean
cgroupfs
NI Non-isothermal
Spotted Hyena Optimizer
HTTPS
ThingsSpeak
Hi-C
PLS
TMOUT
MAM
HUD
oi
Evernote
gg
TP301
X-rays
Manjaro
er
dr
Seiko
JDK
OAS
MIL
dhcp
bw
qs
DenseNet
Iterative Deepening Search
rq
EBSD
Puppet
GFN
Raft
AHU
Reactor Physics
VS Code
INSDC
machine learning
ulimit
DEI
CER
UMC 180 nm
FAIR Guidelines
Gaussian beam
traceroute
DDMC
ALT-SR
ERT
$#
thrift
wi
ИИ
vt
p
Master Equation
Vehicle-to-Load (V2L)
NetBSD
Multi-Verse Optimizer
BIOS
CCS Concepts
CPS
DTW
AdSense
GARCH
yi
`RNN` 等也常大写，但它们属于名称（首字母大写），可在句子中转为小写（如 “we use random forest”），但 `AI`
WI-FI
```

这是严格基于原始关键词条目和术语规范性的结果。
Difference-in-Differences
CO2
column
PLC
GDQM
Tank
Huawei
EPCIS
Roboflow
URN
ViT
SCN
Smartsheet
IP rating
MGDI
my
ou
Gauss-Newton
TAG Heuer
URL
ZCR
```

> 🔚 **注意**：多个重复项已合并；如 `EMG`
TIF
pp
Electrify America
Quantum Supremacy
SugarCRM
RLR
xn
`SI units`
Q-learning
Skyrmion
xl
`AdaBoost`
C-V2X
jn
Acrobat
ℓ1 adaptive controller
Li Auto Charging
transliteration
forum
Burberry
procedure
PS1
NLM
- **特定技术术语中带大写命名的**：如 Harris’ hawks optimization (HHO)
NCIP Hub
rt
svg
FCV
whereis
Multilinguality
Greedy Best-First Search
Stanford Online
EmoGame
MSTEP
makespan
apfs
Zero-Shot Learning
Patagonia
FD
Hitachi
QDA
XRF
PRISMA
Pentoo
EmoSense
CIO
PDSI

→ 去除重复项，规范格式。

---

✅ **最终答案**：

**VISA
PEGASIS
QS
SDGs
MAP Algorithm
MXene
eLMS
CI/CD
Pascal
f2fs
AIoT
TA2
Oil-on-water
DCP
sr
Kay’s Law
mo
`1p/19q co-deletion` 等（尤其注意 `PEDOT:PSS` 中的 `PSS` 是聚苯乙烯磺酸，通常大写）。
- **模型/框架名称中的大写组合**：如 `AHP-Gaussian`
ABAC
Porous Medium Equation
IOI
IED
PROMPT
In-hand
Rhombicuboctahedron
GPT-2）
- 化学式、材料符号（如 SiGe
nt
Lomax
Jordan form matrix
YOLOv5
52C26
hybrid event
kp
jp
FCE
LUV
- 分类代码：68W50
ATEX
etc.
- **基因/蛋白质名称**：如 CEACAM7
RNN-LSTM
ez
UNSW-NB15
DMLM
`RULA (Rapid Upper Limb Assessment)` —— 虽然后者是缩写，但基于人名评估方法，常保持大写。
6. **地理名称**：`Antarctica` 必须大写。
7. **缩略语（通常全大写）**：
   - `STEM`
Crosstalk
WMSN
XPS
hci4d
sa
Fused AI
Under Armour
bf
lms
Max-Product Algorithm
Sato Grassmannian
Halo Effect
glusterfs
Kalman
New Balance
PROSAIL
OT
pg
GPT-3
ASTER
SHVC
dj
zv
60G15
MD simulation
edge-AI
Probing Security
XBRL
EmoRiver
2D Data Feature Selection
PDE model
Domain Adaptation
Manual material handling
Particle Filter
blockly
HER
Rate Equation
et
Diffie-Hellman Protocol
nofs
TikTok
HackerRank
90B50
Polestar
EmoActuator
PostgreSQL
Morocco
xxd
EmoMovie
HSRP
tiff
Jacobi
Quantum Annealing (QA)
常见缩写，如 PN guidance law）  
- **YOLOV5**（You Only Look Once Version 5，模型名称，大写固定）  
- **H ∞ Norm**（H-infinity 控制理论术语）  
- **SPIN**（模型检测工具或特定系统）  
- **NTLBO**（Nature-inspired Teaching-Learning-Based Optimization，缩写）  
- **FPGA
`Vc<sub>max</sub>` 中的大写不可变
> - 外语词汇无法“转为小写”按英语规则，故列入

---

### ✅ 最终答案（只保留真正“不能转为小写”的关键项，去重并格式化）：

```
HHO
DOF
EPQ
NP-complete
an
SMES
DNN
dv
ES8
loadrunner
REACH
mk
MIMO
`QS`
SLC-TLC
WSN
Harmony Search
PCIe
Bidirectional Search
EmoIce
PAGER
`LSTM`
Pluralsight
TAM
Cox Proportional Hazards Model
E‑Government
Euclidean Distance
KVM
New Relic
Argentina
`MPPT`
DAIC-WOZ
Sampling Theorem
faas
Vehicle-to-Home (V2H)
Giraffe kicking algorithm
PacBio
TOC Total operating cost
Apple Watch
se
AWID
RTA
FIWARE
eu
PolSAR
NVMe SSDs
G30
SIFT
JESS
DoSL
RCE
FastText app2vec
IR
pz
perpustakaan
Zemu glacier
audio
sequential rotating
chat
ubifs
m4v
RNA-seq
OUM
RF
eb
`Explainable AI`，AI 保持大写。
    - `Self-attention mechanism` → 中文“自注意力机制”对应 "Self-attention"，其中 "Self" 常大写。

---

✅ **已去重并标准化处理后的结果**（最终输出）：

```
AI
GLCM
Interleukin-1β
Newton
EWH
data lake
lj
yp
kill
Diabetes
LQ control
PMC
90C46
NSGA Non-Dominated Sorting Genetic Algorithm
McEliece
edu-care
IoT instrument
FreeBSD
Converse
RST
ECBO
KBrO<sub>3</sub>
U2R
EmoWeather
EmoFarm
V4
`IoT`
Occam's Razor
Gödel
EmoLake
eSIM
MCMC
дополнительный граф
bt
CDR
CNC
IWDL
lr
EER
logistics
텍스트 마이닝
Fendi
Arc Consistency
VCMA
Flex
raf
PUF
Whonix
AIX
tj
35J05
`Laplace`
unalias
seminar
CYP2B6
VAE
wr
Korea
`TLD`
ue
DTsEB
Simulated annealing algorithm
Peano
PSNR
quic
support
Eventbrite
`POSIX`
SUCROSE-NON-FERMENTING1-RELATED KINASE1
HMAC
Gulp
DigitalOcean
PDSI**
raid
Raspberry Pi OS
iMac
FTIR
GaN Charging
Simpack
MFO
ULSI
WordPress
Amazon mechanical turk
agile
Nature
edfi
BTEX
am
CP/M
CNN
he
Probabilistic Neural Network
TS-model
SAR
CUDA Fortran
APRA
Alibaba
selenium
CNNs
DOP
FSW
G-Shock
EmoHome
SHA512
Computer designs
Grounding DINO
抗量子计算
Tails
es
CT segmentation
Windows Laptop Charging
Beyond the Standard Model
find
Tory Burch
Teams
mkv
FPIDSMC-Ga
WRS
FAIR Data Point
te
SVM
InSAR
Compact Muon Solenoid
specflow
LISREL
Functional correctness
block cipher
Stimulated Raman scattering
CFD model
valgrind
QCM
NILM）
- 化学/材料符号（如 SiGe
np
vy
Porsche
RoI detection
gsm
Tauri
GM
Core SLAM
Automatic code generation
nj
Bootstrap
QC
RADAR
Spiral
CTF
Optane
RMSE
DNA-templated silver nanoclusters
BP Pulse
Extended Kalman filter
Radix tree
ji
CSTMS
MODIS land surface temperature (LST)
CFD simulations
Nomad
Numpy
ElGamal
m
OSVR
op
Dynamic medical image
Slide reduction
procfs
FA
Hadoop
GSA
bi-proportional scaling
Fuzzy Logic
Flink
UAVs
Jordan
Artificial Intelligence2
SOM
AODV
mf
감성분석
Stabilizer Formalism
NewMotion
Wavelet downsampling
FRIS
k-means
Collaboration Design
g
$$
Teachers' movements
Autoencoder
. (zlogin)
UAV networks
Plug-and-play
Distributed cooperation model
SECaaS
65J22
Moons of Jupiter
HIPFort
Deformation representation
Sensor MPX10DP
35K55
DE
Elliptic Curves
GUI
DPV
Multi-camera scene analysis
bcrypt
Compressible fluid flows
Hamiltonicity
multihop routing protocol
ICE
Cybersecurity Skill Gap
PLS-DA
Video watermark
order management
VMS
age estimation
Writer identification
Integrated dynamic
GMRES
Action-design research
DoS
SECURE_PATH
HISTFILESIZE
Haskell
Multiphase MoS<sub>2</sub>
Collaborative robots
discrete-time
DSA
Web-based intervention
cy
historical heritage
physical ergonomics
Manual milling operation
Text Based CAPTCHA
MoS2 nanoflower
BFS
NI
Wirth’s Law
longitudinal acceleration
Quality-of-service storage
Shell structures
HH
Layer correction block
ConvLSTM
hs
PIC16F886 Microcontroller
in-game harassment
UDP
workshop
NLG
Gamma Decay
Agile
xw
DALL-E
`MCDM`
MOEA/D
em
http3
CCN
vh
Neutron Transport
i
IDE
C4ISR
`SHVC`
EmoSyn
MOOC
T6P
gfs2
`Nafion`
Reed-Solomon Code
which
fulfillment
PNG
TD-DFT
qf
FutureLearn
BSSID
education
ACM ICPC
68T30
k-Nearest Neighbor (kNN)
SCHISM
qc
CRPS
nodeMCU
O3
`CAS`
XR
framework
Amazon
NAC079
vf
EmoDemon
Sony
Alpine Linux
BIM
Quantum Key Distribution
MTPA
do
`QUBO`
TZNAME
pi
EmoGod
NGPCA
Trinity DipTESOL
li
less
Hg<sup>2+</sup>
CBRN
MoU
L2TP
STA static timing analysis
IP geolocation
HEP-Y
Computer Science - Data Structures and Algorithms
rsyslog
ROI
mh
ArcFace
PPID
Oracle Linux
MIDI
perf
TURF
PDCA
BigQuery
Plugless
Total Variation Distance
2DOF–TIDN
SHLVL
sy
Minix
Realme Charging
EM-algorithm
Ant Colony Optimization
task management
ih
WinForms
Gentoo
CPU** 等虽然未在列表中全部出现，但属于常规不可小写术语  
- **Wi-Fi**（正确写法包含大写 I）  
- **IoT**（Internet of Things，通常保持大写）  
- **5G**（第五代移动通信）  
- **RAD**（Rapid Application Development 或 Radio Access Device）  
- **DFT**（Discrete Fourier Transform，也可为 Density Functional Theory）  
- **Kriging**（地统计方法，人名命名）  
- **PROMETHEE-SAPEVO-M1**（多准则决策方法组合，专有命名）  
- **GEDI L2A**（Goddard Earth Sciences Data and Information Services Center 产品级别）  
- **FCVI**（可能为特定缩写，如 Fuzzy Cluster Validity Index）  
- **LLAON**（Low-light-level integrated network，若为特定模型名称）  
- **BLS**（可能为 Belief-Labeling System 或其他系统缩写）

注意：经过全量比对，`GPU` 虽未显式出现在原始词表中，但属于题干提示要识别的类型（如 AI、GPU），而 `AI` 明确出现。

但由于原始关键词列表中实际出现且符合标准的不可小写词汇为：

✅ 实际存在于原文并保留的：  
**AI
yf
Banach
IEEE802.15.4
Net-Zero
Bridge
65M70
Quantum Entanglement
Information Theory
Qi Charging
ESP32
ResNet152V2
`HCI`
multi-valued logic
Partially Observable MDP
tg
ww
BCH Code
Static obfuscation
AWJ
FPC
COMINT
Prolog
AH
PDTB
Mean Shift
LDPC
fat32
NCP Network Control Program
Substance Painter
Timberland
UI/UX
yg
qp website
cm
Chebyshev
62G08
- **算法/协议/标准名称**：如 SHA-2
DSML
ql
Tesla Supercharger
5S
cron
SPARQL
DOI
MEGACO
CCPA
Yang-Mills Equations
Verbling
POP3
Oliot
```

### ✅ 最终筛选逻辑：
仅保留 **即使在句首或一般文本中也通常保持大写形式的专有缩写或技术术语名称**。

---

### ✅ 最终答案（去重、有效、格式正确）：

```
AI
ESXi
`POW`
Ford
student information system
SRAM
EmoNews
wf
unique visitors
ln
warehouse management
Miu Miu
OS-ELM
5g
Knockout
SGD
j
`DSC`
`Random Forest`：这些是模型名，**首字母大写**，但并非“全大写不可变”，比如 Random Forest 可写为 random forest，但 `AdaBoost`、`YOLO` 属于品牌名，`YOLO` 通常全大写，但 `v8s` 小写。因此 `YOLOv8s` 作为一个整体保留。

但题目要求是“不能转为小写的词汇”，特指那些即使在句首也不应小写的缩写，如 AI、CNN、GPS、DNA 等。

综上，我们提取所有显式的、标准化的**全大写技术缩写或专有术语**，并去重。

最终不可小写词汇集合：

- AI
- GPU
- mMTC
- PHMSA
- SCADA
- CNN
- BPNN
- LDA
- NCA
- YOLOv8s
- BDD
- DPL
- OneM2M
- INLA
- TG
- BLER
- IGRA
- PSO
- ARM
- CT
- MRI
- EDA
- FPGA

> 注：虽然如 `Random Forest`
tl
GDM
VGG16
Polymer
uj
BASH_ARGC
Mplus
le
NAEP
Dirac
cr2
cronie
`var FRJournalDetails` 等，都属于应保留原始大小写或结构的词汇。
- 像 `transliteration`
cat
GW
Trinity
Meta-Learning
IMU
fx
BF
ETL
Questaal
Explainable AI
MAN
BER
vp9
x265
XGBoost
UTeM
P2D
Xception
mbt
Adobe XD
Cuckoo Search
APX-complete
ESG
DFNet
tu
Navier-Stokes Equation
you only look once version 8 nano
RFID
qr
HISTFILE
uplift modeling
MIT
Less
EmoShop
MQTT
Zephyr
DDR2
`JAVA`
hexdump
PCCA
Sooty Tern Optimization
SCP
FFA
Siemens
`DL`
Tsallis
EMX-270
Haval
RFT
. (bashrc)
vb
GM Ultium Charge
SIC
Digital Ocean
jo
FAIR
CVD
Oliot
SO
highband
st
VoIP
CSO
d
Reed-Muller Code
LNA
efivarfs
Calvin Klein
rpc
SIBI
LED
GMR
ACT
or
HDMI
80/20 Rule
BASH_ARGV
Random Forest Classifier
Krill Herd
Cloudflare
lh
oq
AI (artificial intelligence)
Pygmalion Effect
BeiDou
w
SC-FDMA
in-person event
ToS
SaaS
qa
ye
k6
Oracle
ll
dm
在提供的关键词中，需要找出**不能转为小写**的词汇，即那些**专有名词、缩写、首字母缩略词、带有特定大小写语义的术语**（如 AI、GPU、AES、DNA 等），即使在句首或句中通常也不应全转为小写。

我们逐一分析并筛选出这些**不能转为小写**的词或表达中的关键部分：

---

### ✅ 常见不可小写的类别包括：
1. **缩写/首字母缩略词（大写缩写）**：如 AI
Chebyshev Distance
wu
junit
zf
session duration
tk
MDA
OpenSUSE
Isomeric Transition
Mitsubishi
Google Appstore
ADMM
Discord
Udacity
Faster R-CNN
"Article_ReferenceManager":"Reference Manager"
PPi
Webflow
SLAAC attack
OData
LaTeX
Zoho
fi
IKE
AAL
xa
Mask R-CNN
Yaskawa
Panel Data
DAC
Weak Interaction
SLAM
35R11
NdFeB
Loewe
mq
MIS
Noether
baas
Quantum Teleportation
Backtracking
Next.js
payments
lm
summit
BTU
Firefly Algorithm
FMS
CO<sub>2</sub> utilization superstructure
68T42
`Grad-CAM++` 应保留大写。
10. **编号系统**：
    - 如 `35K25`
Quantum Algorithms
RUN
Learning from Demonstration
MAX-2-SAT
BI
NEQR
us
UAS
Mac Pro
Hamming Distance
фильтр Калмана
```

> ⚠️ 说明：
> - `gru` 在技术文献中应为 `GRU`，视为应大写缩写
> - `DSCRWECPC` 太长但为缩写模型名，保留
> - `Kalman filter` 因 "Kalman" 为人名，不可小写为 "kalman"
> - 化学式如 `NO<sub>2</sub>`
fa
Conway’s Law
LMWs Liquid metal wires
Puma
Fixed Effects
MAIL
data science
Hapi
E. coli O157:H7
ADALINE
etl
49Q22
Animate
zm
COO
FERPA
Agglomerative Clustering
Radoop
`SSS`
STaaS
gradebook
Advantage Actor-Critic
LOI
EmoVolcano
qd
Perfect SIC
WPF
CPPS
MCDM
`makespan` 等虽然少见但本身是普通术语，仅 `Multilinguality` 因构词特殊（类似专有名词）或领域术语被保留，其余视情况判断。
- 非英语词汇（如 `perpustakaan`（印尼语）、`дополнительный граф`（俄语）、`텍스트 마이닝`（韩语））也被保留，因其不是大小写转换的常规对象。

不过，最终保留的关键词中，**严格意义上不能转为小写的主要是缩写、专有名词、品牌和标准代码**。

---

✅ **精简版更合理答案（仅保留真正不可小写的核心术语）：**

AI
Alfa Romeo
62H30
PC
FLIR
Volkswagen Charging
PCB
Super Fast Charging
SphereFace
OneDrive
RAE
LTE A
Dy<sub>2</sub>O<sub>3</sub>
GatorEye
GROUPS
gp
C12
`WARP`
ASV
Inverse RL
IEEE 标准编号（如 34K40
Draw.io
VENDOR
ROS
nn
Denso
Fibonacci Q-matrix
qb
MoBI
av
Unix
ICT
Quantum State
E. coli
EmoSpirit
L-algebra
WOA
jmock
METS
CMFB
IDS GAN
EmoCare
SRR
DES
zj
Yeezy
debugfs
Memristor
Proteus
Hidden Markov Model
ELINT
EmoLawyer
CAD
FAR
Zipf’s Law
iscsi
Google Inception Network
HBIM
PSK
XP
SDRAM
ku
Hofstadter’s Law
UX
Skillshare
jq
`MoS₂`
soapui
RIS
Online Learning
[email protected]
vs
UWB
ar
sh
CBD
Elasticsearch
hg
BigGAN
RTP Robot Teach Pendant
ed
sales
U-EM
OPTARG
GNURBS
OpenCart
cb
FaceNet
Quasar
Kullback-Leibler Divergence
Ruby
Azure
Meteor
CRT
page views
obogacennaya 18O voda
Geely
SSL
Paraguay）
- 带编号的标准/分类（如 68W50
`Google Inception Network`
Reaction-Diffusion Equation
HRI
Webex
nz
WOLIF
sb
Maxwell
FAW
Cr
mp4
DGC H∞
Django
U-EM
```

> 注：`IS/IT Auditor` 中的 `IS/IT` 是 `Information Systems / Information Technology` 的缩写，应保留大写。  
> `Instagram` 是品牌名，也应保留首字母大写，但它是单一单词，**形式上可转为小写但语义上不应**，故加入。  
> 进一步严格处理后，最终保留上述列表。

✅ **最终答案：**

```
AI
ub
Programmatic Advertising
tr
SNR
PS Polystyrene
cdma
AISI
htop
4<sup>th</sup> Industrial Revolution
Root Locus
EmoHail
JVM
FP-tree
Kerberos
FDMA
Lamborghini
Laravel
Moncler
we
dc
YouTube
live stream
data pipeline
sz
`Pyridoxal phosphate`
Canada Goose
MPU6050
UEQ
FANET
Proximal Policy Optimization
CakePHP
Kali Linux
gb
Bvlgari
checkout
Yale Open Courses
EmoDesert
by
kv
Lightroom
MiC
FRAM
Er-BTC
pstore
MSI
Honey accounts
Shuffle SwishNet-181
the onion router
TÜV
Ford Charge Station
QEMU
DVD
PSI
`AGG model`（AGG 可能为缩写）。
- **品牌/产品/专有技术**：如 `Apple`
SLO
OPPO Charging
NBR
P-Value
policy
pvfs
scp
qq
Variational Inference
Changan
NE Atlantic Ocean
AI-powered Genetics
Android Studio
ks
ICA
QNX
wy
b
q
Smart
DeepCluster
AVISPA）
- 非拉丁文字（但这类不“转为小写”不是大小写问题，而是语言问题）

但题目明确是“**不能转为小写**”，即如果转成小写会**失去意义或不规范**。例如：
- `AI` → `ai` ❌ 不规范
- `AES` → `aes` ❌
- `LSTM` → `lstm` ❌
- `Emilio Pérez Piñero` → `emilio pérez piñero` ❌（人名通常保留原大小写）

---

### 🚫 以下是可以转为小写的（排除）：
- 一般术语如 "deep learning"
ntp
LQI
data catalog
TensoRT
HOG
Sine Cosine Algorithm
UPPAAL
Boltzmann
ny
HSV
DNA/RNA
ES7
SANE
NOR Flash
Cordic
AM-Softmax
Gravitational Interaction
LTP
GISAID
ts
IBC
Rolex
Cole-Cole
Gilder’s Law
NFV
FDC
FIPS
Nike
ATR FT-IR
ip
organic
VLAN
vr
Haiku
SNCDM
SEWA
COBIT
BRKeCS
Bi-rads
`ITC`
Preply
Rivian
IPR
68Q85
pca
FAU AIBO
IECEx
Dreamweaver
SoH
EEG
`Ag nanoparticles` → Au、Ag 是元素符号，必须大写。
5. **人名**：如 `Malatesta`
Zero-Knowledge Proof
Datadog
Columbia
AHP-TOPSIS
TD3
sk
OLAP
nf
Cluster Decay
td
NPP
Kibana
CLIP
ol
Quantum Spin Hall Effect
Langevin Equation
jm
AdaBoost
CLM
gpt
MMC-TDGL
kk
BEV
DNA
ho
`OFDM`
MITRE CWE
FPTP
TCN
az
Wavelet Transform
bj
MBA
zoned
Bayes
fr
TBD
BeOS
ls
qx
systemd timer
68U35
alias
C4.5
TN
PDL
OLCI
Resonant Charging

---

⚠️ **说明：**

- 上述结果保留了所有**通用缩写、首字母缩略词、品牌名、型号、标准、协议、编程语言、机构名称、特定项目名称、数学/技术符号（如35J05）**等通常不在文本中全转为小写的术语。
- 如 `AI`
hu
OPPO
ATLAS
ARMA
Character Animator
ENL
Elixir
transaction data
AMSGrad
ECC
Zeekr
QZSS
WO
Presenter
Bat Algorithm
Sentinel-1
TSN
SignalPlant
bz
curl
HC-RNN
iv
midband
lustre
NARDL
GPT-4
AUC
"Earthquake engineering"），只保留：

- 缩写（如 HHO
artillery
ELK
NO₂）
- 数字+字母组合（如 GPT-2
Q-Day
dSPACE1104
Uggs
Swin transformer**

> 注：虽然像 "Swin transformer" 整体不是一个全大写缩写，但“Swin”是专有命名（来自 Microsoft Swin Transformer），不能转为小写形式（如 *swin transformer* 虽常见但丢失专有性），因此应列入。同理 PyCATSHOO、Harris’ hawks optimization (HHO) 中的 HHO 也应保留。

但由于题目要求是“词汇”，我们提取的是独立术语，因此以分号分隔的关键术语为准。

---

### 🟩 最终答案（去重、规范、仅含不可小写项）：

**AI
M-SPWM
Clifford Group
PP
TTMCD-MSHMADM
HISTIGNORE
mr
sj
Ada
JavaScript
3gpp
Hessian
LWARX
Xiaomi Charging
Risk Ratio
BAM bidirectional associative memories
BLS

---

**说明**：  
在所提供的关键词中，大部分词汇都可以正常转为小写。但有一些词汇属于**专有名词、缩写、技术术语或品牌/标准名称**，这些通常**不转化为小写**，以保持其技术或学术上的规范性。以下是筛选出的**不应转为小写**的词汇（保留原大写形式）：

- **AI**（Artificial Intelligence）  
- **GPU**（Graphics Processing Unit）  
- **IEEE 802.1Qbv**（网络时间敏感传输标准）  
- **EMD**（Empirical Mode Decomposition，常用缩写）  
- **JS**（JavaScript，常保持大写）  
- **DICOM**（Digital Imaging and Communications in Medicine）  
- **CFD**（Computational Fluid Dynamics）  
- **ECRS**（可能为特定系统或方法缩写）  
- **ADAMS®**（软件名称，带注册商标）  
- **OBR**（Optical Backscatter Reflectometer 或其他技术缩写）  
- **PN**（Proportional Navigation 或 Polynomial
Neta
accreditation
Laplace Equation
`C/SiC`
CentOS
AFS ATR FT-IR spectrometer
CRITIC
Hadoop Distributed File System
grpc
os
ck
Vehicle-to-Everything (V2X)
NAND Flash
Avaddon
SWB
TDMA
RSAS
68W20
xb
TrCBC
WALS
X.509
Ru-MOFs
SCF
P4 data plane
rb
EOD
GCP
Schoology
V-model
ГЛОНАСС
paste
time tracking
MTP
LTDCA
Springer
AMPK
`EMG`
Bash
VV-hemorphin-5 Val-Val-Tyr-Pro-Trp-Thr-Gln-NH<sub>2</sub>
FHIR
`ZCR Zero-crossing Rate`（ZCR 是缩写）。
9. **模型/算法标识符**：
   - `Naive Bayes` 通常写作 `Naïve Bayes`，首字母大写。
   - `TensorFlow`
pw
AFSD
SSA
eo
BK
Fairness in ML
SaltStack
`CPW`
Beam Search
Paillier
Slackware
Austin
at
tx
Polycategories
Q47
zp
MCAT
bu
PID
pn
WooCommerce
VQC variational quantum circuit
CRF
StyleGAN
Tiffany & Co.
OOPC
Citroën
OCB
IB
USDA
Sheen
NISQ
B0-VPG
ЦОС
SEER
Avatr
eMBB
Wave Ceptor
Fourier Transform
SINR
ping
ProGAN
Beijing
strings
`NOMA`
ACPCs
Mixed Effects
Galois
SnO₂
Heat Equation
OPC UA
iPad Charging
Kolmogorov Complexity
fl
Arabic WordNet
za
RV REALITY-VIRTUALITY
MVP
Web of Science
NC
Spearman Correlation
DSRC
CTO
PL
wget
Android
EmoNurse
MCM
Regression Discontinuity
Multi-scale
nk
ProcessOn
Quantum Neural Networks
CMO
xml
68U05）
- 专有名词/品牌/人名（MATLAB
Polar Code
CINP
U-Net.
PS4
mjpeg
var FRJournalDetails
Shell
Gibbs Sampling
Euler
erp
oa
One-Shot Learning
IFS
PRNGs
Coach
Cohen's d
SurveyMonkey
nfs
LinkedIn
BCI
pe
Non-local
`EPCIS`
SONAR
QML
DNS
AMD
Redis
USER
FrameMaker
WEEE
R
AES SBOX
gRPC
SSID
bv
PPRA
TopCoder
RFx
DMP
mmwave
BS
`00-01` 等为 ACM 分类代码
- `Apache Hive`、`InceptionV3`、`alexnet` 等为模型/工具名
- `ardusub` 虽小写，但为专有项目名，保持原样，但**不**大写，**可争论**，此处包含（因语义关键）

但根据题意，“**不能转为小写**” 指的是“本身有大写、不能强制转为小写形式”，所以应只包含**含有大写字母且不能改为全小写**的词。

`ardusub` 全小写 → 可小写 → 排除

---

### ✅ 最终修正版（仅提取含大写且不可小写的术语）：

```
AI
Airtable
Bc
nc
Apple Charging
kolmogorov_smirnov
EmoSensor
Impella CP®
if
Solar Charging
BCK
SnagIt
Express
IDL
XAI
gq
wx
Ti–6Al–4 V
SOx
HISTSIZE
EMH
xj
PMSG
DBSCAN
UML
ASP.NET
HDFS
HCHO
`90C25`
uy
GMAT
LGDM
AMT1
68T35
Palm OS
ce
invoicing
UNet
gs
ie
NVIDIA GPU
EDA
ES9
ESA
xapi
uk
IBK
OS
`Raspberry Pi` 等**有固定大写形式的专有缩写或命名**。

---

🔍 **去重并筛选后的结果（真正不可转小写的术语）**：

```
AI
payroll
BEiT
ICTBioMed
MapReduce
MTI
Whale Optimization Algorithm
STOMP
DRL
swagger
LP (Linear Programming)
ionice
HISTCONTROL
Si micromechanical gyro
AICS
ABE
ЧПУ
mockito
NOMA
xvid
WeChat
. (zshrc)
TP391
divx
MES
GM(1
ARM Cortex
Director
gf
iPhone Charging
DCVG
MDLC
ABB
FPGA
HEVC
SOC systems on chip
jt
ca
PyAutoGUI
HTLV-1
MCDA
Plasma Oscillations
UPC
CSRR
Multi-objective
СПС
Yukawa
Outlook
`RPAS`
Zoom
Shopify
cp
RGB-D
`ML`
IMO
Markov Chain
Primary 65K05
HCHO-EAS
mz
OWASP IoT Top 10
ResNet-50
elt tool
Poly-annulus
fz
WCG
OSTYPE
Causal Inference
VSM
lu
Vitis HLS
mm
ПЛИС
GNSS
F-1
3DU-Net with Transformer
GRE
MOOC’s
EmoAnimal
`RSSI`
LLAON**

此外，其他如 `IoT`
cifs
Chi-Square Distance
MPEG-alkyne
ELGAMAL cryptosystem
kf
BTW
HelloTalk
Setengah Wajah
Odds Ratio
f4v
ij
JRE
TZDIR
yd
EmoConv
pd
grep
以下是从提供的关键词中筛选出的**不能完全转为小写**的词汇（即包含专有缩写、品牌名、特定术语、人名、地名、标准命名等应保持大写或混合大小写的词汇），并以分号分隔返回：

```
AI
GDPR
Media Encoder
EmoDevil
w3c
HOSTTYPE
BAT
Dragonfly Algorithm
Scikit-learn
PyCharm
HIVE-COTE
Magento
rc
CCTV
Kohn–Sham
L86
Bugatti
LP
OLED
Phase-change memory
rmvb
`MRI`
Colab
GE
rg
ZnO-CsPbBr<sub>3</sub>
FDI
BBGKY Hierarchy
XDG_DATA_HOME
iw
AdS/CFT Correspondence
Indonesia
ipv6
Genetic Algorithm
Contiki
WBANs
Ultra Fast Charging
Isarn Tham
PG-DRL
Spartan 3A
CYLE
messaging
P
SimSiam
Moos-IvP
DDPG-HER
MOEMS
`EPCIS` 等
- **首字母缩略词且惯例大写**：如 `AI`
Ora
Alternative Hypothesis
Hausdorff
en
WFH
HomePod
Biharmonic Equation
TAC
NSD
PS2
dt
Parameter server
RFP
MPPT
ws
ReLU
Unity 3D
CFRP/Al co-cured material
HHO-EAS
ff
Amos
`65M08` —— 属于数学分类编号（MSC code），格式固定，不应小写。
11. **语言/平台名**：
    - `JAVA`
Scrum
Hamilton-Jacobi Equation
SMED
CICIDS2017
FER
EmoDevice
`ICA`
SMEs
Magnonics
customer loyalty
RBF
POI
Alpha Decay
nl
Wiener
in
IWC
LTE
Cantor
EmoDoctor
SMOTEENN
SN
Upstart
BOQ
nd
ADA
OPTICS
dnssec
CCCP
pm
Skype
IPS
CNN-bi_LSTM
hk
training
McLaren
Omega
CodeIgniter
cn
FastSLAM
CASA
df
EmoSky
Grey Wolf Optimizer
"big data"
aq
TNA
RTOS
SXOR
ieee
bmp
Go programming language
FTA
`ROC`
LISP
UWP
ES12
webp
ESRD
DSL
yr
DDS
KUKA
pt
`KBrO<sub>3</sub>`
ky
VLE
MacBook
SEO
ml
FTPS
OnePlus Charging
ft
EmoLang
EMIDEC
IP
Hermès
ro
СБИС
DEEC
cx
Bayesian Inference
C <sub>chla</sub> estimation
DeepMind
spf
Linkerd
Bulk-Boundary Duality
mc
locate
RFR
NN
HOILC
在给定的关键词列表中，我们需要找出**不能转为小写的词汇**，即那些**专有名词、缩写、品牌名、特定技术术语中的大写部分在实际使用中通常保持大写形式**的词汇。这类词汇常见为：

- 英文首字母缩写（如 AI
Tau Decay
ud
Fourier Series
FE
ASAGA
Di-8-ANEPPS
OpenStack
SIEM
SNI
Cas12a
jg
TLD
kb
Butterfly Optimization
`HSRP`
vm
`Q&A`
EmoArmy
RoboHelp
`CRP`
devpts
FYI
dz
NIST
soap
Landau Damping
e
IoV
TensorFlow
ep
VAV
OPTERR
jpeg
SBAS
CAN-SPAM
LFN
SSO
bk
FOMO
Runge–Kutta
GBDT
PHP
avro
React Native
SXNOR
Git
NLSPATH
POS
Terraform
XDG_CACHE_HOME
ECM
НЛП
EmoReact
BPSK
Benford’s Law
oo
lte
Hawthorne Effect
Grafana
BASHOPTS
scm
Dolce & Gabbana
Paraguay
```

> 注：`alexnet` 通常小写，但有时写作 AlexNet。学术中通常小写，**建议排除**  
> `Apache Hive` 中 Hive 大写，应保留

---

### ✅ 最终答案（去重、标准化）：

```
AI
NVIDIA Drive
cc
SIMON’S ALGORITHM
Fine arts
um
TOLES
Facebook
EmoNetwork
RPAS
Reinforcement Learning
SSL/TLS
Patek Philippe
Prim's Algorithm
Citizen
"reinforcement learning"（除非是 RL 或 XAI 等缩写）
- 形容词或普通名词如 "Multi-modal fusion" → 可小写为 "multi-modal fusion"

---

### ✅ 提取结果（不可转为小写的词汇/缩写/专有名词）：

我们从列表中提取所有**包含不能小写成分的词组中不可小写的部分**，包括：
- 全大写缩写（RFR
multiprocessing
SPBE
фильтр Калмана
```
si
Constant Contact
mp
wss
HDD
Mean Field Approximation
MDM
HTML-CSS code
TMBR
xm
cim
UAV
RNN
CFRP/Al 等含有化学符号或特定标记，需保留大小写。
- **专有名词组合**：如 LNG futures prices 中的 LNG 是液化天然气的缩写，不可小写。
- **人名/特定术语**：如 Lax–Wendroff scheme（数值方法名，含人名）也应保留，但此处未列全因上下文仅取首字母词。

> 注：部分如 "HTML documents"、"C++20" 等中的 C++ 已包含在 C++ 中；"Naive Bayes" 中的 "Naive" 实为法语词首字母大写，但一般可小写，此处保守未列入。最终选择以**通用学术和技术写作规范**为准。

返回结果（仅值）：

**AI
Viber
NodeMCU 等名称有固定书写格式。
- **化学/材料式**：ZnO-CsPbBr<sub>3</sub>
Signal
IC3
Cartier
MatLab
USB-C
FRArticleDetails
AI6
master data management
MG
BA
MySQL
GIS
ГИС
jc
EULA
YOLOv4-tiny
Bosch
Internet of Drones
C/SiC
PIPEDA
Synthetic Control
im
LC_ALL
uz
lk
MMD
lvm
Typeform
Linode
IoT
ILE
SAML
Canva
Kubernetes
SfM
ChargePoint
gw
zu
AirTag
IntelliJ IDEA
Null Hypothesis
infiniband
webinar
Chery
`DNN`
PVM
LoRa
yl
DSP
Kanban
HOSTNAME
FCVs
Naive Bayes
GloVe
Miro
EmoFire
Head-mounted
ch
gatling
Nearpod
tb
SE
5G
RHEL
. (profile)
DLT
Dynamic Time Warping
dw
Interpretable ML
Common Criteria
Feynman
IETF
KLT
TECIS
SPM
TTRAM
kr
GraphSLAM
SAAM
E commerce
SoC
RFC
Kaggle
`NCIP Hub`
$!
bios
以下是从您提供的关键词中筛选出的**不能完全转为小写的词汇**（即包含专有名词、缩写、首字母缩略词、品牌名、特定术语等在学术或技术语境中通常保持大写或特殊格式的词汇），并用**分号**分隔返回：

AI
OpenCV
XDG_DATA_DIRS
Nadam
`LSTM` 等（但部分如 `hci` 可小写，但标准写法通常大写）

> ⚠️ 注意：部分条目如 `DL`（Deep Learning）、`DNN` 等虽可小写使用，但在学术文献中通常保持大写形式，视为技术缩写。

但根据题目要求，只选出**不能转为小写的词汇**，即：**即使上下文要求小写，也通常保持大写的专有术语或缩写**。

---

✅ **最终提取出的不能转为小写的词汇（去重后）**：

```
AI
Apache spark
Quality-of-service
Polydimethylsiloxane
FWZIC
FPGA**

---

✅ 最终答案：

**AI
LNG
shipping
Whimsical
TZFILE
jl
ORCID
Berkeley Webcast
rl
Rolls-Royce
fv
Trusted Execution Environment
ELI5
BBR
M30
bs
CTS
KP
$3
Nylon
HAR
NodeMCU
DVI
cw
Occam’s Razor
IoMT
ХПК
Ralph Lauren
xx
HPKM
`IP` —— 这些是正式命名，不能随意小写。
2. **品牌/技术平台名**：如 `Intel OpenVINO`
EmoMachine
STEM
Tennessee Eastman Process（但该词组中只有缩写部分应保留，但未全大写，故未列入）。

但注意：某些词如 *Green ICT*、*Quality of Service* 等虽为术语，但非固定不可小写，因此排除。仅当其缩写（如 QoS）出现时才保留，但原词未以缩写形式出现。

> ⚠️ 特别处理：类似 "svm" 可小写，但 "PSO"
EAST-BL
OWA
WAAM
WDBC
Bufferbloat
Blackboard
Nivat
Non-orthogonal
FFT
H∞
Toyota
GraphQL
Blisk
Time-to-Event Analysis
PMUs
rEFInd
ESPVD
Deep Q-Network
HLS
Lucid
NS-FDOSM
Michael Kors
Squarespace
PEM Brennstoffzelle
PDF OF SINR
n-dodecane）
- 数学分类编号（如 68U05
Nano-Hyperspec
ILP
CASB
IOTA
BASH_EXECUTION_STRING
SVBRDF
ICCID
TOEIC
HCI
Au
xv
btrfs
webRTC
SWCNTs
RGMS
Contribute
Vercel
be
POSIX
Vacheron Constantin
IMA system
CoAP
OFDM
DDD
SUMO
HHO
Argo AI
Qutrit
SiGe 130 nm
tmpfs
Weibo
Hublot
NILM
SMOS
PM10
ns
Zenith
Dap 2
Hangouts
Malatesta
DV-Hop
Boosting
HJE
GitHub
rr
Medium
FP
oc
FDOSM
Ag nanoparticles
KL-divergence
EU
Hilbert Transform
Farizon
id
Shor's Algorithm
EmoAssistant
`Euclidean`
LINENO
CRISPR
MATLAB/Simulink
of
EmoWear
GPT-2
1111
vd
SeLa
Quantum Circuit
NGINX
TLS
DDR3
qu
PV
HP-UX
pf
LPCC
webm
uniq
Scandium
FPR
Physical HRI
Cassandra
Moth-Flame Optimization
MANPATH
Bayesian
GNN convolutional operators
MIR Mid-infrared
data warehouse
COPD
Au nanoparticles
BYOL
Antarctica
Graphical User Interface (GUI)
Transformers
Intel OpenVINO
FOD
Ruby on Rails
risk management
FreeRTOS
CatBoost
ANN
Arduino-Uno
Mailchimp
EmoVu
etc.
4. **标准代码/分类编号**：如 68W50
Quantum Gravity
Coursera
PRNU
Photoshop
Online DCA
Hongqi
FPGA Field Programmable Gate Array
TG
RNA
TP309
IDW
RNNs
China Market
WENO
top
`IS/IT`
PNX
POPС 1-palmitoyl-2-oleoyl- sn -glycero-3-phosphocholine
Rényi Entropy
TZ
LSTM Long short-term memory
rf
Forward Checking
62J07
03B50
SVD
Joomla!
научно-техническое сопровождение строительства
PN
AJAX
ELM
Optuna
SOA
LockBit Black
n
ELGAMAL
TLA+
CRISPR/Cas12a
Proton Decay
Z-transform
RoBERTa
OpenBSD
Hamiltonian
CVE
EmoStyle
Inductive Charging
35P05
kW
Text Based
PyCATSHOO
- **化学物质/材料**：如 T. gondii (寄生虫学名)
NB-IoT
Z-RAM
DIY
ix
`YOLOv8s`
ib
JAX
TTAT
Microsoft Imagine Cup
Windows
BMI
NO<sub>2</sub> gas sensor
yaffs2
Jigsaw
SC financial performance
IaaS
CO<sub>2</sub>
webOS
AUROC Area under the receiver operating characteristic
DTMOS
I/Q data
operational data
GPT
CNSGA-II
Net-zero Energy Konzept
TFHE
HADOOP
ogv
Stable Diffusion
68U05
You Only Look Once (YOLO)
PG
ORB
data quality
NOIZEUS
Panasonic
kt
Fuzzy logic controller
ConvNet
Graphene
gi
hls
Grad-CAM++
62K99
NaaS
Minimum Description Length
CDN
`Multilinguality`
click through rate
Cu<sub>x</sub>O
NOMA-OFDM
von Neumann
WPAN
Julia
Tencent
IEMOCAP
SFM
shopping cart
A-RAM
xd
EmoHuman
Wix
QSVM quantum support vector machine
RSA
U-EM

**说明：**  
以下类型的词汇通常**不能完全转为小写**，因为它们是**缩写、专有名词、品牌名、化学式、技术术语或特定格式**：

- **缩写/首字母缩略词**：如 `AI`
Articulate
Stigler’s Law
VMD
RTAB-Map
EOCRC
HAC
Java
LMI
DSO
BERT
Darknet
MODIS
Nyquist
FE simulation
UI
LANG
Flutter
X3D-модели тел
EmoEdge
Spontaneous Fission
Wasserstein GAN
Dior
Apple TV
AD
al
v
SiC Charging
ice
SPICE
kq
ROMS-CSTMS
DDR5
`U-EM`
rn
Mobileye
SHA-256 compression function
CDSS
BiLSTM
YSL
SCM
dk
Spin-Orbit Torque
Semi-Supervised Learning
74B05
ic
Fokker-Planck Equation
rj
ETA
Pandas
05C82
EmoChip
GFA
wn
PowerShell
DC–DC converter
`LMI`
OpenVMS
IELTS
Fractography
L80
Saham
혁신확산
MAILCHECK
TP311.5

**说明**：  
我们从列表中筛选出**通常不转换为小写**的词汇，主要包括：

- **专有名词缩写**（如 VISA、SMAP、ORB-SLAM3）
- **技术缩写或品牌名**（如 GANs、AdaBoost、SVM）
- **特定材料或化学式**（如 MnO<sub>2</sub>、Ti–6Al–4 V、DNA/RNA）
- **编号代码**（如 IDD10、TP311.5、93A16、65F20）
- **人名衍生术语**（如 Kharitonov polynomials → Kharitonov）
- **专有系统/软件/平台名称**（如 KEPServerEX6、MoBI）

但由于部分术语在学术写作中可能被全小写处理（如 svm
Microsoft
wd
HLM
DNA
```
Celine
DMCA
PFC
SATA
MTTR
qk
All Star
chmod
UK
ERP implementation
ТЭС
Ge
5G**

然而，重新筛查原始关键词：

- `IoT` 出现在两处：`internet of things (IoT)` 和 `internet of medical things`，其中第一个含有 `(IoT)`
- `5G` 出现在 `fifth generation (5G)` 中
- `WI-FI` 全大写出现

这些都应视为“存在”且不可转为小写的术语。

但问题要求是：

> “找出不能转为小写的词汇”

即：原本就**不是全小写**，并且**不能合法地转为小写**（否则会失去意义）的词。

因此，只选那些**以大写/混合大小写出现且为专有名词/缩写的词汇**。

**最终答案**：

AI
event sourcing
WARP
itu
SI units
EmoPolice
Oliot
```
gu
Observer Effect
Jaccard Similarity
PTCA
TCB
HMI
vv
LPG
data lineage
4G
Linus’s Law
U-net
Storyline
Qubit
orc
fq
ad
Adadelta
SSP
qw
PrestaShop
Fourier
AVS_FD_MVITS
PV-generator
Positron Emission
PROMELA
Kolmogorov
`PRNU`
ESP
dy
JAVA
EmoTravel
Causal Set Theory
RF fingerprinting
цифровизация
QCA Designer
xt
Dynamic service composition reconfiguration model when service exceptions occur under practical constraints (DSCRWECPC)
kubernetes
EfficientNet
GWAS
lv
BMW
EmoMap
SSTAARS
TC total cholesterol
NEMA rating
KT
Open-source hardware
Renewable Energy Integration
IMHO
xs
MSP
HTTPs
Fractional Calculus
OOP
CoCoSo
PrimeVue
`Fractional Calculus`
ATPase
UEFI
ZKP
dash
TERMINFO
SHA-2
DSGE
gru
var FRJournalDetails

这已是**真正不能转为小写的核心技术/专有词汇**。其余如 `transliteration`、`Multilinguality` 等可小写，不属于保留范畴。

---

🔹 **最终推荐答案（精准过滤后不可小写的术语）：**

AI
CircleCI
CEACAM7
QAM
RSS
finance
Himawari-8
Triple-negative breast cancer (TNBC)
`Laplace transformation`
ATR Attenuated total reflectance
Non-life
AlexNet
db
HCI5
**US** 之类没有出现
- **5G**
Yamaha
ko
Vc<sub>max</sub>
tc
MGCP
PCNN
devtmpfs
Netlify
Shell Recharge
65N15
Quantum Measurement
iy
Sass
MSA
`TCP`
PTSD
41A10
Bogota
po
Karto
ASCII
`Monte Carlo`
PyCATSHOO
CEC2019 problems
InDesign
FCEV
NSL-KDD dataset
Markov Decision Process
zd
Alpine
LOL
ti
`RF`
Edge based sorted pixel value difference (ESPVD)
Earbuds Charging
LCT
TCP/IP
MRI Magnetic resonance imaging
OS/2
ASP
Künstliche Intelligenz
`WENO`
POX
NOMA-MIMO
AMeDAS
GP
IChO
`GPU` 是医学与AI领域的标准大写术语。

但 `EDA` 是否必须大写？有歧义，但作为 `Electronic Design Automation`，通常是大写，可保留。

特别地，`SCADA supervisory control and data acquisition` 明确写出 SCADA，故必须保留。

`CNN Convolutional neural networks` 同理。

经去重处理，最终不重复词：

**AI
Fish
CUDA platform
lz
Trust Region Policy Optimization
NumPy
`DMLM`
P300
GSSR process
PCR
Klein-Gordon Equation
jv
AAM
IEEE 802.11ah
LP-sequence
GSSR
va
Kalman filter
CLIBAT
nilfs2
CH
direct
fmt
CIPS
Alexander McQueen
MANETs
MariaDB
Little’s Law
IB-SSS datasets
FRJournalDetails
MRF
o
Fermi
FE analysis
etc.
- 人名：Emilio Pérez Piñero
- 地名：Paraguay
- 品牌/工具：ardusub
Quantum Machine Learning
TanDEM-X DEM
PLC 等。
7. **化学/生物专用术语**：如 Gln glutamine (Q)
Laplace Transform
DSML3
Yang
Kodachi Linux
a
OpenAI
deep learning
PaaS
LiDAR
cd
Stevens’s Power Law
xfs
Fiat
Marketo
Web-based
cs
RAD
conversion rate
init
PROLOG
СВЧ
IK rating
Jupyter
68Q25
E.ON Drive
“Q” (Quadrature)
19. **RSA** — 加密算法，大写缩写
20. **WPAN** — Wireless Personal Area Network
21. **VMD** — Variational Mode Decomposition 或软件名
22. **PFC** — Power Factor Correction / Programmable Flow Controller
23. **Ge** — 元素锗，化学符号
24. **L80** — 可能为钢材等级或代码，含数字
25. **ZKP** — Zero-Knowledge Proof，缩写
26. **gru** — Gated Recurrent Unit，通常写作 **GRU**，但此处为小写？但现实中应为**GRU**，可能是输入错误，但**应为大写** → 原始为 "gru"，但技术上**应大写**，故保留原输入为小写？→ **这里原文是“gru”小写，但属于应大写的缩写，我们保留判断：GRU 应大写，输入错误不影响其本质**
27. **Himawari-8** — 日本气象卫星，8 为数字，必须保留
28. **TNBC** — Triple-negative breast cancer，缩写
29. **TC total cholesterol** — “TC” 是缩写
30. **65N15** — 数学分类编号
31. **POX** — 可能为 peroxidase 或项目名
32. **ZKP** — 已列入
33. **GSSR process** — GSSR 为缩写
34. **Kalman filter** — Kalman 是人名，**K 必须大写**
35. **ESPVD** — Edge based Sorted Pixel Value Difference，缩写
36. **P300** — 脑电波信号，大写 P + 数字
37. **Vc<sub>max</sub>** — 最大羧化速率，Vc 不应全小写（V 大写）
38. **GWAS** — Genome-Wide Association Studies
39. **ATPase** — 酶名，ATP 大写
40. **DSCRWECPC** — 超长缩写（Dynamic service composition...），全部大写
41. **Lyapunov’s second method** — “Lyapunov” 为人名，L 大写
42. **Feynman–Kac theorem** — 人名组合
43. **Beavers–Joseph interface condition** — 人名
44. **Sierpinski triangle** — 人名（Sierpinski）
45. **GNN convolutional operators** — “GNN” 为 Graph Neural Network，缩写
46. **GUI** — Graphical User Interface
47. **Bi-LSTM** — Bidirectional LSTM，缩写 + 连字符
48. **Dueling deep reinforcement learning** — Dueling 为模型变种，但无缩写，非必须大写
49. **RSA** — 已列
50. **P2P** — Peer-to-peer，缩写
51. **YOLO** — 已列
52. **SCADA-like**? 无，但 **DCA** 出现：Online DCA — 可能为 Discounted Cash Flow Analysis 或其他，但“DCA”为缩写
53. **DSCRWECPC** — 已列，必须保留
54. **6G** — 第六代移动通信
55. **ESPVD** — 已列
56. **P300** — 已列
57. **VMD** — 已列
58. **PFC** — 已列
59. **L80** — 已列
60. **POX** — 已列
61. **POI** — Point of Interest，缩写（出现在 POI recommendation）
62. **TC** — 已列
63. **DSCRWECPC** — 太长但为缩写，保留大写
64. **DCA** — Online DCA，缩写
65. **HJE** — Hamilton–Jacobi–Bellman 或类似，HJE 缩写
66. **IMA** — already listed

### 非拉丁语词汇（不能“转为小写”因非英文）

- **научно-техническое сопровождение строительства** — 俄语
- **цифровизация** — 俄语
- **혁신확산** — 韩语（创新扩散）
- **X3D-модели тел** — 混合俄语 "модели тел"（身体模型）
- **фильтр Калмана** — 俄语，“卡尔曼滤波器”

这些非拉丁或非英文词汇不能按英语大小写规则处理，故不能转为英文小写。

---

### 最终筛选：**不能转为小写的词汇**

我们排除仅首字母大写的通用术语（如 "Papyrus"
PHEV
zc
PRNG
IEEE 802.1Qbv
EMD
Ubuntu
CIPK31
ipv4
JS
Si3N4
H ∞
XPGR
SHA256
Plotly
65M12
UTAUT2
65C05
Ti-6Al-4V
FBMC
ES2023
CL
Egr3
CERN
Boltzmann Entropy
hy
eq
jy
USB
robot framework
Diffie-Hellman
PTE
AUPRC
PESQ
SPECK32
53Z50）
- 带数字或特殊格式的名称（如 YOLO
DBN
ev
OFDMA
sd
NATO
CP
Elsevier
FOS
`ZnO`
Electron Capture
Euler-Lagrange Equation
f
DHCP
GEDI L2A
BlackArch
Q5
G-contractive fixed point.
EmoRobot
Linguaskill
Opera
MPEG
ACM
H.323
traffic sources
ITT
ae
EUID
T. gondii
SERS
O30
MIME
magnetorheological
Pod Point
Hilbert
C++
asf
Bagging
Internal Conversion
rs
SDWN
DICOM
mu
awk
wv
Matplotlib
CFD
WWW
VDI
ut
MW
iCloud
TDNNS
BPM
RBFNNs
CFO
tv
EmoCloud
RRI
kx
Huawei Charging
gsma
cad
wa
SCADA
lt
DEB
OCF
WI-FI
rh
`JSR`
sr2
PATH
OSMC
Hellinger Distance
rmdir
EmoLand
RIA
cf
ISCC
Saint Laurent
Jevons Paradox
scorm
Radar
TESOL
BASH_ALIASES
EmoRain
Teaching-Learning-Based Optimization
marketing
DCOPs
Radical
ECRS
Weber–Fechner Law
WavLM
RDF
ee
Gustafson’s Law
OBR
Balenciaga
locust
PTE Academic
flv
IBM Cloud
Turbo Code
kz
EPW
30C20
PCA
ADAMS®
Strong Interaction
Schrödinger Equation
qi
mpeg4
EPI
PA Presentation Attack
Algorithmic Entropy
Norvig’s Law
35Q35
SWRL
hevc
ofdma
Oceanus
sort
AUP
IQ
Pods
Babel
Einstein
di
Domain Generalization
Gibbs Entropy
EmoGraph
inventory management
Laplace
Electron
Caesar Cipher
nilfs
IT
INLA
rtsp
Python
mj
Express.js
hris
Planck
Bootloader
etc.
3. **带注册符号或特定格式的术语**：如 TWS
Hawkes process
PFSENSE SOFTWARE
Renault
HydroDS
xf
covid 19
YOLOV5
journald
OneNote
PDF
RoHS
RTB
Newton–Raphson
E-governance
BYD Charging
RADINT
PLM
gj
SECONDS
Unsupervised Learning
Mizar
GPU-libraries
Leapmotor
UMC
Lua
MONOS
FEM
Graph Distance
Zsh
Shannon
FFPM
DAs
SARS-CoV-2
DXOMARK

---

**说明**：  
- 剔除纯普通名词短语（如 success behavior、fair exchange 等）  
- 保留专有名词（如 Gartner Hype Cycle、Douban Books）  
- 保留缩写（如 AI、GPU、IoT、GISAID、QCM-D、SNN）  
- 保留带特定大小写的品牌/框架/模型名（如 GPT-2、faster R-CNN、3DU-Net）  
- 保留含数学/分类编号（如 05C50、68T45、94A99、65L05）  
- 保留带特殊符号或格式的术语（如 SnO<sub>2</sub>、Black–Scholes）  
- 保留首字母拼写或组合缩写（如 CS、HTER、CMGC、MARL）  
- 保留非英文但固定术语（如 операционная система）

**注意**：由于输入中存在拼写错误（如 `andorid` 应为 `Android`），仍保留原形式作为不能全转小写的项。
Sum-Product Algorithm
Galileo
Reed’s Law
Balsamiq
Tailwind
H2O
$*
nu
Sample Size
Zoox
Sigmoid
Ns-3
saas
Pearson Correlation
Supabase
ry
Quizlet
TN953
Hamming Code
FCVI
Bode Plot
ROM
PM2.5
DFT
Kriging
CA
SDG 2
NARNETs
Beats
Negative Binomial Regression
EXE
Cartographer
NTLBO
WEDs Wearable electronic devices
sftp
AI
hv
AVI
Itô
Gradient Descent
GANs
fk
BASH_VERSION
VISA
kg
ef
Qubes OS
CMD
H ∞ Norm
Ionity
Drupal
TSDF
. (bash_profile)
IDD10
以下是不能转为小写的专有词汇、缩写、特定术语或包含特殊字符/格式的词汇，已根据语义和技术惯例筛选并用分号分隔：

AI
Monday.com
tq
MC-PDFT
EmoMountain
SPIN
KYC
data mining
hash
PERBASASI
CLAHE
though not here）
- 多语言词汇（如俄语或中文，不能转小写）

---

我们将逐个分析并筛选出**即使在全小写语境中也不应转为小写的词**（因其为缩写、名称或技术标识）：

### 明确不能转为小写的词汇（应保留原大小写）：

1. **HHO** — 缩写（如哈里斯鹰优化）
2. **IMA system** — “IMA” 是缩写，通常保留大写
3. **SiGe 130 nm** — “SiGe” 是硅锗材料缩写，必须大写
4. **NILM** — Non-Intrusive Load Monitoring，缩写
5. **SMOS** — Soil Moisture and Ocean Salinity，缩写
6. **HJE** — 可能为 Haas-Jens-Evans 或类似缩写
7. **MATLAB/Simulink** — 品牌软件名，必须保留大写
8. **GPT-2** — OpenAI 模型名称，带数字
9. **SCDTO algorithm** — “SCDTO” 为缩写
10. **n -dodecane** — “n” 表示正构烷烃，化学命名中“n”小写，但“dodecane”可小写，“n”不是大写，但整个属于化学式，但“n”不为大写，此条**排除**
11. **LockBit Black** — 网络攻击组织名，专有名词
12. **CNSGA-II** — 缩写 + 数字，进化算法名
13. **Net-zero Energy Konzept** — “Konzept”为德语“概念”，但“Net-zero Energy”为专有名词，且“Konzept”**不是英语**，**保留原样**
14. **HADOOP** — 大写品牌名，通常全大写
15. **68U05** — 数学主题分类编号，包含字母与数字
16. **You Only Look Once (YOLO)** — “YOLO” 是知名模型缩写，必须大写
17. **NO<sub>2</sub> gas sensor** — “NO₂”为化学式，N 和 O 为元素符号
18. **I/Q data** — 信号处理术语，“I” (In-phase)
PE
la
Codecademy
sl
logfs
RecaptchaSiteKey
BASH_LINENO
KDD
Entanglement Entropy
ticketing
un
Subversion
MnO<sub>2</sub>
is
Forward-Backward Algorithm
ni
SectionId: '963' }
Travis CI
Instance space
Pix2Pix
RVM
LSO
faq
ICS
**4G**：`4K resolution` 中的 `4K` 是分辨率术语，但数字+字母，虽常大写，但并非严格“不能小写”，不过在技术文档中通常保持大写。但严格来说，4K 不是“字母缩写”而是命名规范，类似 `HD`
EmoDew
parquet
DOA
IB-SSS
jbehave
EDFA
eMTC
Uniform Cost Search
Snapchat
LUV
PID

---

### 说明：
这些词汇之所以不能转为全小写，原因包括：

- **缩写/首字母缩略词**：AI
PS
scfdma
C-GAN
MRI
SDs
renice
XDG_CONFIG_DIRS
`99–00`（这些是分类号，通常保留原格式）。
- **数学/技术符号组合**：如 `EF<sub>2</sub>`（应为 EF₂，下标不影响大小写判断，但 EF 可能为缩写）。

但注意：  
- `FAQ` 和 `Q&A` 虽然是常见词，但属于**固定大写缩写**，不能转为小写。
- `IoT` 是 `Internet of Things` 的缩写，首字母大写，不能全小写。
- `U-EM` 是 `Unprofiled EM Attack` 的缩写，EM 指电磁（ElectroMagnetic），保持大写。

**最终筛选出不能转为小写的词汇（去重后）：**

```
AI
W3C
certification
DMAIC
Domain Wall
Splunk
Facebook Hacker Cup
RAID
Grunt
XGBoosting
SMAP
ORB-SLAM3
Duolingo
Rock’s Law
DDQN
AHP-Gaussian
GWO
eMail
AGG model
HX711
Wav2Vec
Tudor
IM Motors
fgrep
Basecamp
3DU-Net
ERP
Caputo
TODIM
Captivate
RStudio
Beetle Antennae Search
bdd
MoCo
F#
mov
EMG signal
QPSO
ocfs2
ELISA
FOIA
ir
FastAPI
Tsallis Entropy
SDN
Homomorphic Encryption
$1
RFID 等常见技术缩写。
2. **品牌/系统/项目名称**：如 OWASP Security Shepherd
TP311
uo
xz
X3D

---

### ✅ 最终精简结果（仅提取不可小写的关键词）：

```
AI
Louis Vuitton
Differential Evolution
`U-NET++`
SQL
CORDIC
ntfs
Tencent Charging
jb
Fair price
PWD
CFA
Firmware
AMPK (或全称 AMP-activated protein kinase)
- **数学/代码分类号**：如 41A10
Science
cramfs
iPad
EOLC
Plug-in Charging
GEI
MPC
co
MCP
Physical ergonomics
IS/IT Auditor
SCLC
GADJ
Blancpain
COSMIC
POW
Parrot OS
iso
DSC
SAR

---

**说明：**

经过逐项分析，以下缩写或专有名词通常**不能全部转为小写**，因为它们是：

- **专有名词 / 品牌 / 技术术语**：如 `iOS`
Logstash
sub6
Boolean
Emperor Penguin Optimizer
ECDH
Scheme
Armani
HIP
Нейронная сеть
APK
Bitbucket
appium
Bi-LSTM）
- 混合大小写的技术术语（如 ResNet
KEPServerEX6
Gacha
js
PDSA
G-quadruplex
Adidas
MEC
$?
RSSI
eh
wmv
DWI
Computational photography
Blu-ray
xp
PGL
Simulated Annealing
COB
NMPC
GUV
KNN
pfSense
Valentino
EVTOL
WGAN
NDVI
Blockly
UGV
Apple
Google Drive
Perl
CVSS
`ERP`
BPNN
roce
display
LAN
pa
EmoUniverse
NVD
gr
Trello
pl
SIGINT
MP3
`CI/CD`
LSD-SLAM
Great Wall
QoE
calendar
Arcfox
`RNN`
xu
Keras
hq
uc
jsonl
MAC Algorithm
AFTE
Electromagnetic Interaction
BackBox
LSTM
Maximum Likelihood
```

❌ 上面有很多其实是**短语而非不可小写的术语**。

---

🎯 **正确做法**：只提取像 `iOS`
BSD
41A58
kw
BCaaS
МКЭ
qh
LCC
uw
pk
dkim
X3D
qt
Versace
Josephson Junction
BLE
ur
Abstract
SNS
ltrace
igmp
QA
SPDE
ek
IIOT
Instagram
Envoy
Debian
DBMS
rest
PDSI
CDPATH
FSK
Tunicate Swarm Algorithm
Meta
ys
vc1
wk
py
zz
IPhO
Turbo Equalization
tsv
SW26010
CAS
U-NET++
PROBA-V
MPX10DP
fn
WO<sub>3</sub>
SDMA
Micro Blogs
Ethereum
SSS
Double Beta Decay
ICF-CY
Shannon Limit
UV
XD
BSGTS
Honda
COOP
FDA-MIMO
HEPS
jmeter
PDAM
HRES
Bgl
Lasym
dane
Rosetta Stone
ES2022
lgbtq+
DI
"mobile robots"
Nginx
tw
DRAM
dig
IPv6
Grover's Algorithm
governance
NS-FDOSM
```

---

🔴 **说明与原则**：

我们筛选的标准是：**是否属于不应全转为小写的专有术语或缩写**。包括以下类型：

1. **标准缩写或术语**：如 `POSIX`
COVID-19
spdy
F-N
ICAP
Rivian Adventure Network
CLI
telnet
ey
Keyword classification
rd
Fireworks
TP311.5
UNESCO
Laplacian

**说明：**  
经过去重和筛选，以下是一些**不能完全转为小写**的术语（通常因包含专有名词、缩写、型号、命名实体等）：

- **缩写/首字母缩略词**：如 AI
BMS
Edit Distance
WiMAX
DC/AC
ADMET
RFQ
EmoSoul
NSGA-II
h264
orf
PROMETHEE-SAPEVO-M1
ABSO
SPIHT
aufs
Swin transformer**
IRS
Dijkstra's Algorithm
SENet
Cambridge English
AVEC
FAHP
zr
Azure Kinect DK
CV cyclic voltammetry
yt
HMD-VR
Well logging
Three phase grids connected
var
type
DBSCAN algorithm
Exoskeletons
lf
Self-Supervised Learning
EM
neural networks
Mutual Information
OLTP
ChatGPT-37
TERMPATH
ii
Operational Efficiency
NRNN
DBDH Assumption
lc
EOSC
Vivo
MVS
Hyrum’s Law
Udemy
CPU
- **模型/框架名称中的专有名词**：如 Swin transformer（Swin 是专有命名）

但注意：像 "Swin transformer" 中的 "Swin" 是微软提出的专有名称（如 Vision Transformer 系列），应保留大写。

然而，在本题中要求的是“**不能转为小写的词汇**”，所以我们保留那些**必须含有大写字母或数字+字母组合不能全小写化**的词（如包含大写字母的缩写、型号、专有名词）。

---

### ✅ 最终去重后答案（仅保留确实不能全转小写的术语）：

**AI
mi
`QCA Designer`。
3. **专有模型或框架名**：
   - `YOLO series` → YOLO 是 "You Only Look Once" 的缩写，不可转为小写。
   - `Grad-CAM++` → 包含模型名和变体。
   - `Transformers` → 特指深度学习中的架构，首字母大写。
4. **化学/材料术语**：
   - `CO<sub>2</sub>` / `CO2` → 保持原写法，尤其是化学式。
   - `Au nanoparticles`
ico
CenterNet
PPy-rGO hybrids
IoT security
data governance
Jumping Robot
LightGBM
TPAPy
suicidality
Hue histogram
Functional data-edged network
Log-likelihood estimator
Distant supervision
ve
qg
Bixby platform
Floods
EmoTutor
MTBF
VGGFace
Markov random field
Time-resolved studies
Compressive Sensing (CS)
05C50
Microheater
Pairing-based cryptography
Paraguay
```
Interactive learning environments
macOS
Midjourney
Robots
Auxiliary plate
Observer design
Brand Loyalty
3DU-Net with Attention
xg
SA Simulated Annealing
iq
Google Earth Engine
Curie
FS
NovoGrad
Graph Neural Networks
Quantum Computing
ECG
xh
Empresa
EHR
Survival Analysis
SCFAs short-chain fatty acids
gz
Vue
rarp
ot
jz
Brooks’s Law
ge
C&C server
Label
Google Code-in
TOSCA
so c system
real-time data
Cuk converter
`FOD`
scrypt
MPI
nb
Machine-Learning
SOAP
ESP8266
Mt-Sp1/Matriptase
Post quantum cryptography
BDNF
dg
EAN
Mercedes-Benz Charging
gl
Multi-View Learning
more
GAN
68Q42
HTER Half Total Error Rate
Arctic
IPsec
U-2-Net
mathematical models
Sketch
EmoSleet
Stellarator
HAI
ABSA
Package maple
Somatic embryogenesis receptor kinase (SERK)
Singular value decomposition (SVD)
GSAS
Free software
disturbance observer
Arch
THD
iPadOS
NoSQL
strace
WiTricity
pc
Dart
LTE-M
whois
Virtex-7
LSAT
Naive least-squares estimation
Gartner Hype Cycle
OLDPWD
Adagrad
Cash holdings (CH)
Fission Reactor
DTO
YLE
arp
68T45
Data-driven design
Monel-400
md
Hourglass
Multi-agent games
diffuse default trust artificial agent (AA)
TERMINFO_DIRS
MNIST
Ember
Pinterest
pq
FPIDSMC-GA
nslookup
hj
uu
TTS
QCA
PEPITA
T5
PDB
InAs
Jool
iaas
ANOVA
CDP
tftp
aa
Assembly
Real-time data
TNFAIP8
l
value added tax
z
vu
Minimum Spanning Tree
lb
Waterfall
PDMS
C18
zero moment point
C code snippets
ig
Older adults
pu
SnO<sub>2</sub>
billing
DSCRWECPC
Parkinson’s Law
Non-dominated sorting genetic algorithm II
FTP
Quantified modal logics
Developer contribution assessment
CRIS
Harvard Online
Monitoreo
. (zprofile)
ELUI
Distributed ledger technologies
Computer graphics
RFB
MARL
gc
Yudo
cv
Givenchy
CD
`MSP`
RD
COPE
andorid
`AI`
Machine learning (ML)
turn
engagement
Hill Climbing
BizSCOP
Peptide
Orbital Property
TIMEFORMAT
Do-Calculus
UVM
EmoHealth
MacBook Charging
ASAP
DAWGs
CRP
OLEDs
Cauchy
sx
H.264
Co<sub>3</sub>O<sub>4</sub>
HVAC
Gronwall
gk
EmoNet
Arsitektur Enterprise
WAPE
ASR
DC-AC
GPT-2 large language model
CMGC
faster R-CNN
Lagrangian
MFCC
Software defined networks
Iris
hh
Prototype
анализ помеченных данных
PDAC
PI
vp
Heterogeneous graph
spock
Photoplethysmography
Mutual authentication
Security-Aware Optimization
re
TCM massage
oj
IPA
Black–Scholes model
Linear elasticity
ES6
рынок жилой недвижимости
Channel Coding
Manhattan Distance
umask
CSP
CEO
HISTCMD
SIP
Continual Learning
3DES
pr
Bellman-Ford Algorithm
Directed graph
SQL server
Edge computing optimization model
Process algebra
Dense connection
Linear outerplanar graph
90C59
eps
41A05
Social robots
Hemingway
FTC
OSI
EDITOR
Deniably authenticated encryption
SAT
System on chip
OEE
cage dipole
IOTDRMF
MD5
OWASP
ELS
Shannon Entropy
Fraenkel
поиск фальшивых монет
EL model
Oxford
Kazakh
DGA Test
Raspberry Pi
Istio
DM-QIM
Kronecker
PRNS
Spin Waves
Douban Books
Zigbee protocol
Institutions of Higher Learning
powermock
pgrep
`ELM`
email
IOMT
SME
Heisenberg
Ishihara test
feature pyramid networks
DDR4
FWaaS
Wave Equation
R-PBFT
Kabupaten Brebes
FFF
LIDAR
Language models
LTE-advanced
Chanel
GaN
join
wl
`SCF`
очистка от шума
SSI
LSTM）
- 特定技术/系统/软件/语言名称（如 MATLAB
dns
gan），我们仅保留那些**标准文献中始终保持大写或首字母大写形式**、且**不具备动词/普通名词用法**的词汇。

---

但需注意：在实际文本中，像 SVM、GANs 等有时也会被小写，但在标题或关键词中常保持大写。根据题意“**不能转为小写**”，即这些词一旦小写会**丧失其指代意义或技术准确性**，我们保留其大写原形。

---

**最终去重并修正后的不可小写词汇**（仅保留确实不能小写的标识符、化学式、品牌、专有缩写等）：

VISA
IoT internet of things
Webpack
pseudonymization ETL architecture
MATLAB
XPeng Super Charging
NetsBlox
Solaris
Amdahl’s Law
Samsung Charging
SDBS
Renewable energy generators (REGs)
ah
EmoClimate
XDG_RUNTIME_DIR
FACTS devices
nh
TERMCAP
Spin Transfer Torque
RCA
SBO
Legendre
cq
65H10
HSM
Holographic Principle
ag
ZynQ7000
hd
EmoPlanet
Hazard Ratio
UMAP
OLI
NS-3.29
MR
iPhone
`Wi-Fi` 等虽有意义，但在词表中写法为：
- `internet of things (IoT)` → 原文为小写，但 `IoT` 是缩写应保留
- `fifth generation (5G)` → `5G` 是标准术语
- `WI-FI` → 出现形式为全大写，应标准化为 `Wi-Fi`

因此最终提取出**在原始关键词中以大写或混合大小写形式出现、具有专有含义、不应转为小写**的词汇：

```
AI
6G Network
JSON
zt
ai
ng
AMP-activated protein kinase
gd
HIL
ui
Prada
SLA
Superconductivity
Google Classroom
bi
34A09
CMOS
SURF
DIKWM
MCESTA
AHFS
Inteligencia artificial generativa
Qur'an murottal sound
CI
Reddit
hz
PZT
Global Initiative on Sharing Avian Flu Data (GISAID)
Loop Quantum Gravity
CELTA
машинне навчання
Planetary Science and Exploration
HFACS
radial distribution network
CSG Classifier
Voice conversion (VC)
Cu-Cl
Bluetooth
DFAut—Design for Automation
K<sub>3</sub>[Fe{CN}<sub>6</sub>]
EViews
h265
FSVRG
LuNet
PARCC
RBM
SNN
DNSCrypt
EFO
VAPT
Scientific Linux
LinkedIn Learning
Waymo
Ternary azeotropes
ClickUp
3g2
XTS
cam
Mercurial
Mazhab
QoS
DI-8-ANEPPS
VB.NET
uv
`SDN`
Hamiltonian Monte Carlo
Neutrosophic sets
операционная система
DXOMARK
65L05
TVP-VAR
VSI
Mills
ABOA
sq
Hand–eye calibration
intrusion detection
NIR
zw
Panerai
комплексированная навигационная система
6LdG3i0UAAAAAOC4qUh35ubHgJotEHp_STXHgr_v
fcoe
mpeg2
SPC
Sigmoid
```

但注意：

- `Itô`
Lynk & Co
Lasso
ex
Murphy’s Law
SimMechanics
PVT
Neutrino Oscillations
MORE
RTO
insomnia
PDP
Monte Carlo
Google Ads
Android Phone Charging
fb
Supersymmetry
ISO 9001
yn
MAC
dh
Spring
HMD
erofs
SSE
io
knowledge base
RULA
ESSID
GWh
Volt–VAR
ob
TOEFL
EmoAngel
ASPO
41A30
DDoS
NiN
NTP
Z-type
STATCOM
Qualcomm
DECRSA
TUXEL
SPRT
US
tdd
CYP121
RBFS
Jaguar
Perforce
SST
ay
KELM
avi
ow
caas
t-SNE
Pony.ai
ochratoxin A
SO<sub>2</sub>
diploma
отзывы в сети интернет
ATmega
scRNA-seq
YAML
EmoDeath
Busuu
cl
SAIC
IDS
yq
ZeroMQ
Erlang
Vans
OPTIND
ew
ta
WPT
reference data
Transfer Learning
SwAV
Philips
35J85
CNS
C2C
ps
Sistema solar
EmoGen
openapi
M41
EmoWork
ResNet
Asana
TMOP
Sequential Monte Carlo
IEEE 802.11
моделювання тимчасових даних
PCS
HFD
RIMS
2-CEES gas
Kotlin
DoubleClick
Peetre
Codeforces
Lipschitz
Ant Design
GIF
OPCNN
IoT-устройство
rz
tn
ANSYS
pj
EmoForest
raw
TF
Levenberg-Marquardt
wq
Seaborn
1)-norm regularization
fc
BINN
ITMT-SPH
LCD
MagSafe Charging
gy
BMW Charging
Quantum Volume
BYOD
Material UI
Whitehead
MongoDB
CCPR
Harris’ hawkes optimization (HHO)
Tizen
plm
TK
de
eg
HIV
CAP
vw
Fitts’s Law
Secure Enclave
Khan Academy
CF
cg
rtmp
Constraint Satisfaction
CLA
ISBN
_
EmoTherapist
etc.）
- 人名、地名、品牌、工具名
- 编号代码
- 特定术语（如 XAI）

---

### 最终筛选结果（去重、合并、标准化）：

```
AI
PAR2
chown
ik
metadata
Vehicle-to-Infrastructure (V2I)
(H)SVP
podcast
Slack
Significance Testing
VICReg
ISO
EmoJudge
ФПГА
АИ
`CNN` 等是广泛接受的**技术术语缩写，通常不全小写**。

---

✅ 最终答案（仅保留**全大写缩写型、品牌型、不能全转小写的术语**）：

```
AI
OnePlus
EmoSport
SIMO
DGS
reiserfs
binfmt_misc
bh
Minkowski Distance
px
Quantum Cryptography
SOC
MCM7
DGTD
TC
Casio
Pauli
ddd
BEC
Rust
rdma
Wi-Fi
Mahalanobis Distance
ufs
yh
CYOD
FRR
KVSSD
Fedora
IoE
virtual event
OpenVINO
Nyquist Plot
AMQP
V2X
Russell
lx
OAC
DAQ
mms
M48
- 特殊术语：Caputo
00-01）
- 特定项目/品牌/工具名（如 Apache Hive
65D32
GLUE
EDI
OCI
cj
cqrs
`DV-Hop`
BlackBerry OS
DDR
Structural Causal Model
wz
Node.js
`HiL (Hardware-in-the-Loop)` 等。
8. **数学/科学常数或理论命名**：
   - `Gronwall inequality`
Tandem
Kaizen
Phoenix
Benford's law
Bode
PTS
Generalized Linear Model
HITS
Placebo Effect
jfs
