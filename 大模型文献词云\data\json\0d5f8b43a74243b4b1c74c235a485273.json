[{"ArticleId": 96028166, "Title": "(Un) scaling computing", "Abstract": "", "Keywords": "", "DOI": "10.1145/3554926", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Microsoft Research Cambridge"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Sussex and Malmö University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Stockholm University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IT University of Copenhagen"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Stockholm University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cornell University"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Stockholm University"}], "References": [{"Title": "Political Ecologies of Participation", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "CSCW1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Processes of Proliferation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "GROUP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}]}, {"ArticleId": 96028180, "Title": "Critical perspectives on ABCD", "Abstract": "", "Keywords": "", "DOI": "10.1145/3550063", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "Akwugo Emejulu", "Affiliation": "University of Warwick"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of California, Irvine"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "San Francisco State University"}], "References": []}, {"ArticleId": 96028181, "Title": "Recommendation system using a deep learning and graph analysis approach", "Abstract": "<p>When a user connects to the Internet to fulfill his needs, he often encounters a huge amount of related information. Recommender systems are the techniques for massively filtering information and offering the items that users find them satisfying and interesting. The advances in machine learning methods, especially deep learning, have led to great achievements in recommender systems, although these systems still suffer from challenges such as cold-start and sparsity problems. To solve these problems, context information such as user communication network is usually used. In this article, we have proposed a novel recommendation method based on matrix factorization and graph analysis methods, namely Louvain for community detection and HITS for finding the most important node within the trust network. In addition, we leverage deep autoencoders to initialize users and items latent factors, and the Node2vec deep embedding method gathers users' latent factors from the user trust graph. The proposed method is implemented on Ciao and Epinions standard datasets. The experimental results and comparisons demonstrate that the proposed approach is superior to the existing state-of-the-art recommendation methods. Our approach outperforms other comparative methods and achieves great improvements, that is, 15.56% RMSE improvement for Epinions and 18.41% RMSE improvement for Ciao.</p>", "Keywords": "community detection;deep learning;HITS algorithm;network embedding recommender systems;trust networks", "DOI": "10.1111/coin.12545", "PubYear": 2022, "Volume": "38", "Issue": "5", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering University of Qom  Qom Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering University of Qom  Qom Iran"}], "References": [{"Title": "Deep Learning Based Recommender System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Learning social representations with deep autoencoder for recommender system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "2259", "JournalTitle": "World Wide Web"}, {"Title": "Social movie recommender system based on deep autoencoder network using Twitter data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "1607", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96028182, "Title": "GranularNF", "Abstract": "<p>In this paper, we consider the challenges that arise from the need to scale virtualized network functions (VNFs) at 100 Gbps line speed and beyond. Traditional VNF designs are monolithic in state management and scheduling: internally maintaining all states and operations associated with them. Without proper design considerations, it suffers from limitations when scaling at 100 Gbps link speed and beyond: the inability of efficient utilization of the cache because of the contention due to the frequent control plane activities, computational/memory-intensive tasks taking up CPU times, shares states causing the synchronization among the cores.</p><p>We address these limitations by arguing for the need to granularly decompose a VNF into data/control components that are co-located within a server but can be independently scaled among the cores. To realize the approach, we design a \"serverless\" programming framework with novel abstraction to optimize the data components that must process packets at the line speed, reduce the contention of the data states and enable run-time scheduling of different components for improved resource utilization. The abstractions, combined with the runtime system that we design, help NFV developers focus on the logic and correctness of VNF programming without worrying about how VNFs may be scaled in or out. We evaluate our platform by comparing it with monolithic approaches using different workloads and by analyzing its advantages of separation on scalability, performance determinism, and feature velocity.</p>", "Keywords": "", "DOI": "10.1145/3561074.3561092", "PubYear": 2022, "Volume": "50", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota - Twin Cities"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota - Twin Cities"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota - Twin Cities"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Minnesota - Twin Cities"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota - Twin Cities"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of Minnesota - Twin Cities"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Minnesota - Twin Cities"}], "References": []}, {"ArticleId": 96028190, "Title": "Elevating strengths and capacities", "Abstract": "", "Keywords": "", "DOI": "10.1145/3549068", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Escuela Superior Politécnica del Litoral"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Maryland College Park"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "San Francisco State University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Georgia Tech"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Georgia Tech"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of California, Irvine"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Washington"}, {"AuthorId": 8, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "University of California Davis Health"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Central Florida"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Irvine"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Cornell University"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "George <PERSON> University"}], "References": []}, {"ArticleId": 96028193, "Title": "Ergodicity of Time Reversal Process of Stochastic Consensus Formation and Its Application", "Abstract": "<p>The consensus reached in stochastic consensus formation is a random variable whose distribution is generally difficult to determine analytically. We show that the time reversal process for the stochastic consensus formation process is ergodic. This fact allows us to numerically obtain the distribution for the consensus by observing the time reversal process for consensus formation for a fixed sample path.</p>", "Keywords": "", "DOI": "10.1145/3561074.3561079", "PubYear": 2022, "Volume": "50", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chiba University, 1-33 <PERSON><PERSON><PERSON>, Inage, Chiba 263-8, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chiba University, 1-33 <PERSON><PERSON><PERSON>, Inage, Chiba 263-8, Japan"}], "References": [{"Title": "Distribution of Consensus in a Broadcasting-based Consensus-forming Algorithm", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "3", "Page": "91", "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review"}]}, {"ArticleId": 96028198, "Title": "What community asset mapping can teach us about power and design", "Abstract": "", "Keywords": "", "DOI": "10.1145/3554988", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DePaul University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "DePaul University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DePaul University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Goldin Institute"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chicago Peace Fellows"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of Maryland College Park"}], "References": []}, {"ArticleId": 96028269, "Title": "An approach for combining multimodal fusion and neural architecture search applied to knowledge tracing", "Abstract": "<p>Knowledge Tracing is the process of tracking mastery level of different skills of students for a given learning domain. It is one of the key components for building adaptive learning systems and has been investigated for decades. The empirical success of deep neural networks in the past few years has encouraged researchers in the learning science community to take similar approaches. However, most existing deep learning based knowledge tracing models have the following limitations: (1) Only use the correct/incorrect response, ignoring useful information from other modalities. (2) For works do consider multimodality use simple concatenation, which might not be the best way for modality fusion. (3) Design their network architectures manually via trial and error. To solve these problems, we propose Multimodal Fusion and Neural Architecture Search (MFNAS) in this paper. The commonly used neural architecture search technique could be considered as a special case of our proposed approach when there is only one modality involved. We further propose to use a new metric called time-weighted Area Under the Curve (weighted AUC) to measure how a sequence model performs with time. Our proposed approach MFNAS allows more efficient design of Knowledge Tracing models. Besides, evaluated on two public real datasets, the discovered model is able to achieve around 12% improvement in coefficient of determination, 2% improvement in AUC and weighted AUC compared with state of the art models. What’s more, unlike most existing works, we conduct <PERSON><PERSON><PERSON><PERSON><PERSON>’s test on the model predictions and the results are statistically significant.</p>", "Keywords": "Knowledge tracing; Multimodal fusion; Neural architecture search", "DOI": "10.1007/s10489-022-04095-x", "PubYear": 2023, "Volume": "53", "Issue": "9", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Zhejiang Gongshang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "Tao Han", "Affiliation": "School of Computer and Information Engineering, Zhejiang Gongshang University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Zhejiang Gongshang University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Southern Methodist University, Dallas, USA"}], "References": [{"Title": "JKT: A joint graph convolutional network based Deep Knowledge Tracing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "510", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96028419, "Title": "Meta-Heuristic-Based Hybrid Resnet with Recurrent Neural Network for Enhanced Stock Market Prediction", "Abstract": "<p>This paper is to design a new hybrid deep learning model for stock market prediction. Initially, the collected stock market data from the benchmark sources are pre-processed using empirical wavelet transform (EWT). This pre-processed data is subjected to the prediction model based on hybrid deep learning approach by adopting Resnet and recurrent neural network (RNN). Here, the fully connected layer of Resnet is replaced with the RNN. In both the Resnet and RNN structures, the parameter is optimized using the probabilistic spider monkey optimization (P-SMO) for attaining accurate prediction. When analyzing the proposed P-SMO-ResRNN, it secures 6.27%, 12.26%, 15.13%, 13.61%, and 14.10% more than RNN, DNN, NN, KNN, and SVM, respectively, regarding the MASE analysis. Hence, the proposed model shows enhanced performance. With the elaborated model and estimation of prediction term based on several analyses, this work supports the stock analysis research community.</p>", "Keywords": "", "DOI": "10.4018/IJDST.307152", "PubYear": 2022, "Volume": "13", "Issue": "1", "JournalId": 17968, "JournalTitle": "International Journal of Distributed Systems and Technologies", "ISSN": "1947-3532", "EISSN": "1947-3540", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> <PERSON>", "Affiliation": ""}], "References": [{"Title": "Stock Market Prediction Using LSTM Recurrent Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "170", "Issue": "", "Page": "1168", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 96028430, "Title": "Design of an Assistant Decision Support System for Sports Training Based on Association Rules", "Abstract": "<p>In order to quantitatively evaluate the effect of sports training, it is necessary to track the dynamic characteristics of sports by using sports training aided decision support system. When the existing sports training assistant decision support system extracts the features of decision association rules, some redundant features will appear when establishing the global association rules, which increases the amount and difficulty of data calculation and affects the effect of assistant decision support. On this basis, the data fusion of assistant decision support information is carried out, and the optimal assistant decision support scheme is obtained according to the fusion results. The experimental results show that the design system is superior to the existing auxiliary decision-making system in motion recognition rate, motion result accuracy rate, and decision-making accuracy rate, which can provide users with auxiliary decision-making support for sports training and has good practical application effect.</p>", "Keywords": "", "DOI": "10.4018/IJDST.307959", "PubYear": 2022, "Volume": "13", "Issue": "7", "JournalId": 17968, "JournalTitle": "International Journal of Distributed Systems and Technologies", "ISSN": "1947-3532", "EISSN": "1947-3540", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A novel hybrid GA–PSO framework for mining quantitative association rules", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "4645", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 96028515, "Title": "Introduction to the Special Issue: Data Intelligence on Patient Health Records", "Abstract": "", "Keywords": "", "DOI": "10.1162/dint_e_00165", "PubYear": 2022, "Volume": "4", "Issue": "4", "JournalId": 64768, "JournalTitle": "Data Intelligence", "ISSN": "", "EISSN": "2641-435X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Leiden University Medical Centre (LUMC), Leiden University, 1310 Leiden, the Netherlands"}, {"AuthorId": 2, "Name": "Barend Mons", "Affiliation": "Leiden University Medical Centre, Poortgebouw N-01, Rijnsburgerweg 10 2333 AA Leiden, the Netherlands"}], "References": []}, {"ArticleId": 96028519, "Title": "Eigenanalysis and non-modal analysis of collocated discontinuous Galerkin discretizations with the summation-by-parts property", "Abstract": "Guided by the von Neumann and non-modal analyses, we investigate the dispersion and diffusion properties of collocated discontinuous Galerkin methods with the summation-by-parts property coupled with the simultaneous approximation technique. We use the linear advection and linear advection-diffusion equations as model problems. The analysis is carried out by varying the order of the spatial discretization, the Péclet number, and looking at the effect of the upwind term. The eigenanalysis is verified to provide insights into the numerical errors based on the behavior of the primary mode. The dispersion and diffusion errors associated with the spatial discretization are shown to behave according to the primary- or physical- mode in the range of low wavenumbers. In this context, the discretization of the model problems is verified to be stable for all flow regimes and independent of the solution polynomial degree. The effect of the upwind term shows that its effect decreases by increasing the accuracy of the discretization. Further, two analyses that includes all modes are used to get better insights into the diffusion and robustness of the scheme. From the non-modal analysis, the short-term diffusion is computed for different flow regimes and solution polynomial degrees. The energy decay or long-term diffusion based on all eigenmodes based on the eigenmodes matrix is analyzed for t &gt; 0 and different spatial discretizations. The results are validated against under-resolved turbulence simulations of the Taylor–Green vortex at two Reynolds numbers, i.e. , Re = 100 and 1600, and a Mach number Ma = 0.1 , and the decaying of compressible homogeneous isotropic turbulence at a Reynolds number based on the Taylor microscale of Re λ = 100 and a turbulent Mach number of Ma t = 0.6 .", "Keywords": "Collocated discontinuous Galerkin discretizations ; Summation-by-parts property ; Simultaneous-approximation-term ; Von Neumann analysis ; Non-modal analysis ; Compressible turbulent flows", "DOI": "10.1016/j.camwa.2022.08.005", "PubYear": 2022, "Volume": "124", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "King <PERSON> University of Science and Technology (KAUST), Computer Electrical and Mathematical Science and Engineering Division (CEMSE), Extreme Computing Research Center (ECRC), Thuwal, Saudi Arabia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "King <PERSON> University of Science and Technology (KAUST), Computer Electrical and Mathematical Science and Engineering Division (CEMSE), Extreme Computing Research Center (ECRC), Thuwal, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "King <PERSON> University of Science and Technology (KAUST), Computer Electrical and Mathematical Science and Engineering Division (CEMSE), Extreme Computing Research Center (ECRC), Thuwal, Saudi Arabia;Mohammed VI Polytechnic University (UM6P), MSDA Group, Benguerir, Morocco"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematics and Statistic Department, Faculty of Science, King Faisal University, Al-Hassa, P.O. Box 400, <PERSON><PERSON><PERSON> 31982, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "King <PERSON> University of Science and Technology (KAUST), Computer Electrical and Mathematical Science and Engineering Division (CEMSE), Extreme Computing Research Center (ECRC), Thuwal, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "King <PERSON> University of Science and Technology (KAUST), Computer Electrical and Mathematical Science and Engineering Division (CEMSE), Extreme Computing Research Center (ECRC), Thuwal, Saudi Arabia"}], "References": [{"Title": "Nektar++: Enhancing the capability and application of high-fidelity spectral/hp element methods", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "249", "Issue": "", "Page": "107110", "JournalTitle": "Computer Physics Communications"}, {"Title": "Design of a <PERSON><PERSON><PERSON><PERSON><PERSON> spectral Vanishing Viscosity turbulence model for discontinuous Galerkin methods", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "104440", "JournalTitle": "Computers & Fluids"}, {"Title": "Entropy-Stable, High-Order Summation-by-Parts Discretizations Without Interface Penalties", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Performance study of sustained petascale direct numerical simulation on Cray XC40 systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Optimized geometrical metrics satisfying free-stream preservation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "207", "Issue": "", "Page": "104555", "JournalTitle": "Computers & Fluids"}, {"Title": "Scalability of high-performance PDE solvers", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "5", "Page": "562", "JournalTitle": "The International Journal of High Performance Computing Applications"}, {"Title": "Efficient exascale discretizations: High-order finite element methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "6", "Page": "527", "JournalTitle": "The International Journal of High Performance Computing Applications"}]}, {"ArticleId": 96028524, "Title": "Point attention network for point cloud semantic segmentation", "Abstract": "<p>We address the point cloud semantic segmentation problem through modeling long-range dependencies based on the self-attention mechanism. Existing semantic segmentation models generally focus on local feature aggregation. By comparison, we propose a point attention network (PA-Net) to selectively extract local features with long-range dependencies. We specially devise two complementary attention modules for the point cloud semantic segmentation task. The attention modules adaptively integrate the semantic inter-dependencies with long-range dependencies. Our point attention module adaptively integrates local features of the last layer of the encoder with a weighted sum of the long-range dependency features. Regardless of the distance of similar features, they are all correlated with each other. Meanwhile, the feature attention module adaptively integrates inter-dependent feature maps among all local features in the last layer of the encoder. Extensive results prove that our two attention modules together improve the performance of semantic segmentation on point clouds. We achieve better semantic segmentation performance on two benchmark point cloud datasets (i.e., S3DIS and ScanNet). Particularly, the IoU on 11 semantic categories of S3DIS is significantly boosted.</p>", "Keywords": "point cloud; semantic segmentation; self-attention mechanism; deep learning; long-range dependencies", "DOI": "10.1007/s11432-021-3387-7", "PubYear": 2022, "Volume": "65", "Issue": "9", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Lab for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Lab for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Lab for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 4, "Name": "Piaopiao Yu", "Affiliation": "National Key Lab for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Lab for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Lab for Novel Software Technology, Nanjing University, Nanjing, China"}], "References": [{"Title": "PCT: Point cloud transformer", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "187", "JournalTitle": "Computational Visual Media"}]}, {"ArticleId": 96028533, "Title": "Terminology for a FAIR Framework for the Virus Outbreak Data Network-Africa", "Abstract": "<p>The field of health data management poses unique challenges in relation to data ownership, the privacy of data subjects, and the reusability of data. The FAIR Guidelines have been developed to address these challenges. The Virus Outbreak Data Network (VODAN) architecture builds on these principles, using the European Union's General Data Protection Regulation (GDPR) framework to ensure compliance with local data regulations, while using information knowledge management concepts to further improve data provenance and interoperability. In this article we provide an overview of the terminology used in the field of FAIR data management, with a specific focus on FAIR compliant health information management, as implemented in the VODAN architecture.</p>", "Keywords": "Data governance; Data management; Distributed data; FAIR Data and Services; FAIR Data Point; FAIR framework; FAIR Guidelines; Federated data", "DOI": "10.1162/dint_a_00167", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 64768, "JournalTitle": "Data Intelligence", "ISSN": "", "EISSN": "2641-435X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Leiden University, 2331 GL Leiden, the Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Leiden University, 2331 GL Leiden, the Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Leiden University, 2331 GL Leiden, the Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Leiden University, 2331 GL Leiden, the Netherlands"}, {"AuthorId": 5, "Name": "Francisca <PERSON>", "Affiliation": "Kampala International University, 260101 Kampala, Uganda"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Leiden University, 2331 GL Leiden, the Netherlands;Leiden University Medical Centre (LUMC), Leiden University, 1310 Leiden, the Netherlands"}], "References": []}, {"ArticleId": 96028638, "Title": "Autonomous service robot for human-aware restock, straightening and disposal tasks in retail automation", "Abstract": "The workforce shortage in the service industry, recently highlighted by the pandemic, has increased the need for automation. We propose an autonomous robot to fulfill this purpose. Our mobile manipulator includes an extendable and compliant end effector design, as well as a custom-made automated shelf, and it is capable of manipulating food products such as lunch boxes, while traversing narrow spaces and reacting to human interventions. We benchmarked the solution in the international robotics competition Future Convenience Store Challenge (FCSC) where we obtained the first place in the 2020 edition, as well as in a laboratory setting, both situated in a convenience store scenario. We reported the results evaluated in terms of the score of the FCSC 2020 and further discussed the real-world applicability of the current system and open challenges. GRAPHICAL", "Keywords": "Retail automation ; service robot ; restock ; human-aware ; Future Convenience Store Challenge", "DOI": "10.1080/01691864.2022.2109429", "PubYear": 2022, "Volume": "36", "Issue": "17-18", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan;College of Information Science and Engineering, Ritsumeikan University, Kusatsu, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information Science and Engineering, Ritsumeikan University, Kusatsu, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Panasonic Corporation, Osaka, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Panasonic Corporation, Osaka, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Panasonic Corporation, Osaka, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Panasonic Corporation, Osaka, Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Panasonic Corporation, Osaka, Japan"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}, {"AuthorId": 12, "Name": "Pattaraporn <PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}, {"AuthorId": 13, "Name": "Bunyapon <PERSON>l", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Ritsumeikan University, Kusatsu, Japan"}, {"AuthorId": 15, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Panasonic Corporation, Osaka, Japan"}, {"AuthorId": 16, "Name": "<PERSON><PERSON>", "Affiliation": "Panasonic Corporation, Osaka, Japan"}, {"AuthorId": 17, "Name": "<PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}, {"AuthorId": 18, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Ritsumeikan University, Kusatsu, Japan"}, {"AuthorId": 19, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Information Science, Nara Institute of Science and Technology, Ikoma, Japan"}], "References": [{"Title": "Portable compact suction pad unit for parallel grippers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "202", "JournalTitle": "Advanced Robotics"}, {"Title": "System for augmented human–robot interaction through mixed reality and robot training by non-experts in customer service environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "157", "JournalTitle": "Advanced Robotics"}, {"Title": "Restock and straightening system for retail automation using compliant and mobile manipulation", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "235", "JournalTitle": "Advanced Robotics"}, {"Title": "Adaptive motion generation using imitation learning and highly compliant end effector for autonomous cleaning", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "189", "JournalTitle": "Advanced Robotics"}, {"Title": "Reusable robot system for display and disposal tasks at convenience stores based on a SysML model and RT Middleware", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "250", "JournalTitle": "Advanced Robotics"}, {"Title": "A mobile dual-arm manipulation robot system for stocking and disposing of items in a convenience store by using universal vacuum grippers for grasping items", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "219", "JournalTitle": "Advanced Robotics"}, {"Title": "Q-bot: heavy object carriage robot for in-house logistics based on universal vacuum gripper", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "173", "JournalTitle": "Advanced Robotics"}, {"Title": "Semiotically adaptive cognition: toward the realization of remotely-operated service robots for the new normal symbiotic society", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "11", "Page": "664", "JournalTitle": "Advanced Robotics"}, {"Title": "Optical laser microphone for human-robot interaction: speech recognition in extremely noisy service environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "5-6", "Page": "304", "JournalTitle": "Advanced Robotics"}, {"Title": "Evaluating quality in human-robot interaction: A systematic search and classification of performance and human-centered factors, measures and metrics towards an industry 5.0", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "392", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Software development environment for collaborative research workflow in robotic system integration", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "11", "Page": "533", "JournalTitle": "Advanced Robotics"}]}, {"ArticleId": 96028653, "Title": "Mathematical and Machine Learning Models for Groundwater Level Changes: A Systematic Review and Bibliographic Analysis", "Abstract": "<p>With the effects of climate change such as increasing heat, higher rainfall, and more recurrent extreme weather events including storms and floods, a unique approach to studying the effects of climatic elements on groundwater level variations is required. These unique approaches will help people make better decisions. Researchers and stakeholders can attain these goals if they become familiar with current machine learning and mathematical model approaches to predicting groundwater level changes. However, descriptions of machine learning and mathematical model approaches for forecasting groundwater level changes are lacking. This study picked 117 papers from the Scopus scholarly database to address this knowledge gap. In a systematic review, the publications were examined using quantitative and qualitative approaches, and the Preferred Reporting Items for Systematic Reviews and Meta-Analyses (PRISMA) was chosen as the reporting format. Machine learning and mathematical model techniques have made significant contributions to predicting groundwater level changes, according to the study. However, the domain is skewed because machine learning has been more popular in recent years, with random forest (RF) methods dominating, followed by the methods of support vector machine (SVM) and artificial neural network (ANN). Machine learning ensembles have also been found to help with aspects of computational complexity, such as performance and training times. Furthermore, compared to mathematical model techniques, machine learning approaches achieve higher accuracies, according to our research. As a result, it is advised that academics employ new machine learning techniques while also considering mathematical model approaches to predicting groundwater level changes.</p>", "Keywords": "machine learning; mathematical model; statistical model; climate; systematic review; groundwater; groundwater level machine learning ; mathematical model ; statistical model ; climate ; systematic review ; groundwater ; groundwater level", "DOI": "10.3390/fi14090259", "PubYear": 2022, "Volume": "14", "Issue": "9", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information and Communication Engineering, Tianjin University, Tianjin 300072, China;Department of Computer Science and Informatics, University of Energy and Natural Resources, Sunyani 00233, Ghana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information and Communication Engineering, Tianjin University, Tianjin 300072, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Informatics, University of Energy and Natural Resources, Sunyani 00233, Ghana"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of New South Wales, Sydney, NSW 2052, Australia"}], "References": [{"Title": "Groundwater level prediction using machine learning models: A comprehensive review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "489", "Issue": "", "Page": "271", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 96028713, "Title": "The Design of Academic Programs Using Rough Set Association Rule Mining", "Abstract": "<p>Program accreditation is important for determining whether or not a program or institution meets quality standards. It helps employers to evaluate the programs and qualifications of their graduates as well as to achieve its strategic goals and its continuous improvement plans. Preparing for accreditation requires extensive effort. One of the required documents is the program’s self-study report (SSR), which includes the PEO-SO map (which allocates the program’s educational objectives (PEOs) to student learning outcomes (SOs)). It influences program structure design, performance monitoring, assessment, and continuous improvement. Professionals in each academic engineering program have designed their PEO-SO maps in accordance with their experiences. The problem with the incorrect design of map design is that the SOs are either missing altogether or cannot be assigned to the correct PEOs. The objective of this work is to use a hybrid data mining approach to design the correct PEO-SO map. The proposed hybrid approach utilizes three different data mining techniques: classification to find the similarities between PEOs, crisp association rules to find the crisp rules for the PEO-SO map, and rough set association rules to find the coarse association rules for the PEO-SO map. The work collected 200 SSRs of accredited engineering programs by the ABET-EAC. The paper presents the different phases of the work, such as data collection and preprocessing, building of three data mining models (classification, crisp association rules, and rough set association rules), and analysis of the results and comparison with related work. The validation of the obtained results by different fifty specialists (from the academic engineering field) and their recommendations were also presented. The comparison with other related works proved the success of the proposed approach to discover the correct PEO-SO maps with higher performance.</p>", "Keywords": "", "DOI": "10.1155/2022/1699976", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 10191, "JournalTitle": "Applied Computational Intelligence and Soft Computing", "ISSN": "1687-9724", "EISSN": "1687-9732", "Authors": [{"AuthorId": 1, "Name": "Mofreh A. Hogo", "Affiliation": "Department of Electrical Engineering, Benha Faculty of Engineering, Benha University, Benha, Egypt;Department of Computer Science, University College in Umluj, University of Tabuk, Tabuk, Saudi Arabia"}], "References": [{"Title": "Ensemble human movement sequence prediction model with Apriori based Probability Tree Classifier (APTC) and Bagged J48 on Machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>.", "PubYear": 2021, "Volume": "33", "Issue": "4", "Page": "408", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "An Intelligent Prediction System for Educational Data Mining Based on Ensemble and Filtering approaches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1471", "JournalTitle": "Procedia Computer Science"}, {"Title": "Systematic ensemble model selection approach for educational data mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "105992", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 96028727, "Title": "Dynamic event-triggered security control for networked control systems with cyber-attacks: A model predictive control approach", "Abstract": "This article studies a dynamic event-triggered security control problem for networked control systems subject to deception attacks and packet dropouts . First, a combined cyber-attack model is proposed, which utilises two sets of independent stochastic sequences to reflect randomly occurring cyber-attacks. Subsequently, a dynamic event-triggered protocol is constructed to relieve the restricted bandwidth pressure by reducing the data transmission of the communication channel from the plant to the controller. With the consideration of randomly occurring deception attacks, packet dropouts, and dynamic event-triggered protocols, an online model predictive control algorithm is established to ensure the stochastic stability of the closed-loop model with expected H 2 / H ∞ performance. Finally, two examples are simulated to interpret the validity and effectiveness of the proposed design strategy.", "Keywords": "", "DOI": "10.1016/j.ins.2022.08.093", "PubYear": 2022, "Volume": "612", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sichuan University, Chengdu 610207, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sichuan University, Chengdu 610207, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sichuan University, Chengdu 610207, China;College of Information Engineering, Henan University of Science and Technology, Luoyang 471023, China;Corresponding author at: School of Aeronautics and Astronautics, Sichuan University, Chengdu 610207, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, The Hong Kong Polytechnic University, Hong Kong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Mathematics, The Hong Kong Polytechnic University, Hong Kong, China"}], "References": [{"Title": "Resilient and secure remote monitoring for a class of cyber-physical systems against attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "1592", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic event-triggered security control and fault detection for nonlinear systems with quantization and deception attack", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "594", "Issue": "", "Page": "43", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96028746, "Title": "Android and Web-Based Learning Research During the Last 10 Years: How Does It Impact Physics Learning?", "Abstract": "<p>There are many benefits from using AbL and WbL, especially in the learning process through all subjects. Also, it is necessary always to discover novelty, innovation, improvement, and development of AbL or WbL to increase the quality of the study environment. This research analyzes bibliometrics on ‘AbL’ and 'WbL' keywords and compares them. Scopus is used to collect the metadata, and the VOSViewer application will be an assist tool. This research is expected to reach trends, patterns, novelty, and future research in the AbL and WbL through broad fields of Education during the past ten years (2012-2021). The research results show that trends of AbL and WbL research tend to increase each year. Finally, based on the analysis of the selected papers, the use of AbL, should be more boarder and widely in the classroom. Furthermore, the use of WbL also tends to be effective and efficient. Using WbL in classrooms is believed can improve several skills of students. But, it is necessary to find a better design to improve the learning concepts in integrating WbL. These findings also can be a recommendation for future research that optimizes AbL for the teaching and learning process in schools or universities/institutions. However, the use of appropriate learning media can improve and provide a positive response from many aspects of education in the classroom, especially in generating knowledge in abstract material like a physics subject.</p>", "Keywords": "Android-based Learning;bibliometric;web-based Learning;education;technology", "DOI": "10.3991/ijim.v16i16.32985", "PubYear": 2022, "Volume": "16", "Issue": "16", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "Binar Kurn<PERSON>", "Affiliation": "Universitas Negeri Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Negeri Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Negeri Surabaya"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universitas Negeri Surabaya"}], "References": []}, {"ArticleId": 96028750, "Title": "ViCT—Virtual Campus Tour Environment with Spherical Panorama: A Preliminary Exploration", "Abstract": "<p>ViCT or spelled \"VISIT\", is the name of a web-based application that is being developed to display various points and locations from the campus environment that can be accessed virtually. ViCT aims to help users, students, and the public explore campus with digital freedom of movement anytime and anywhere, educating users about campus facilities and services with an immersive technology experience that is eco-friendly and fun. The application development concept is unlimited visits and easily accessible via desktop and mobile, saving resources and costs. In this chapter, the focus of the researchers discusses the initial digital mapping of the campus environment, which is the object or study area of the development of virtual campus applications with 360-degree panoramic image techniques. Spherical panorama images are the primary basis of the virtual reality environment in this application. Each panoramic image is processed and merged into a unified whole on a web page. Spherical panorama photos captured using ultra-wide-angle lenses at the front and rear of the camera (dual fisheye) generate a visual field with a horizontal and vertical angle of view close to 360 degrees.</p>", "Keywords": "virtual campus tour;spherical panorama;preliminary exploration;MDLC", "DOI": "10.3991/ijim.v16i16.32889", "PubYear": 2022, "Volume": "16", "Issue": "16", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "Agariadne Dwinggo Samala", "Affiliation": "Universitas Negeri Padang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Negeri Padang"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Negeri Padang"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indonesian National Police Headquarters"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Negeri Padang"}], "References": []}, {"ArticleId": 96028775, "Title": "iTalk–iSee: A participatory visual learning analytical tool for productive peer talk", "Abstract": "<p>Productive peer talk moves have a fundamental role in structuring group discussions and promoting peer interactions. However, there is a lack of comprehensive technical support for developing young learners’ skills in using productive peer talk moves. To address this, we designed iTalk–iSee, a participatory visual learning analytical tool that supports students’ learning and their use of productive peer talk moves in dialogic collaborative problem-solving (DCPS). This paper discusses aspects of the design of iTalk–iSee, including its underlying theoretical framework, visualization, and the learner agency it affords. Informed by the theory of Bakhtinian dialogism, iTalk–iSee maps productive peer talk moves onto learning goals in DCPS. It applies well-established visualization design principles to connect with students, hold and direct their attention, and enhance their understanding. It also follows a three-step (code → visualize → reflect) macro-script to strengthen students’ agency in analyzing and interpreting their talk. This paper also discusses the progressive modifications of iTalk–iSee and evaluates its usability in a field study. We present the implications of essential design features of iTalk–iSee and the challenges of using it (relating to, for example, teacher guidance, data collection, transcription, and coding). We also provide suggestions and directions for future research.</p>", "Keywords": "Productive peer talk; Dialogic collaborative problem solving; Talk moves; Computer-supported collaborative learning; Visual learning analytics", "DOI": "10.1007/s11412-022-09374-w", "PubYear": 2022, "Volume": "17", "Issue": "3", "JournalId": 29983, "JournalTitle": "International Journal of Computer-Supported Collaborative Learning", "ISSN": "1556-1607", "EISSN": "1556-1615", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Education, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Education, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Education, The University of Hong Kong, Hong Kong, China"}], "References": [{"Title": "Dialogic intervisualizing in multimodal inquiry", "Authors": "<PERSON>; <PERSON>-<PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "283", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "Promoting regulation of equal participation in online collaboration by combining a group awareness tool and adaptive prompts. But does it even matter?", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "1", "Page": "67", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "Educational dialogues and computer supported collaborative learning: critical analysis and research perspectives", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "4", "Page": "583", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}]}, {"ArticleId": 96028816, "Title": "One Health-inspired early implementation of airborne disease spread mitigation protocols aided by IoT-based biosensor network", "Abstract": "The implementation of mitigation protocols in the event of the outbreak of viral epidemics typically comes only after a significant number of infected persons have been diagnosed, within which period the infection may have spread significantly over the population. The One Health initiative provides an alternative approach to this challenge. Based on the recent advances in biosensor technology capable of accurate real-time viral diagnosis, and the advances on the internet of things technology (IoT), this paper proposes the development of a pervasive biosensor network deployed at the animal habitats in communities and strategic positions in the environments. This enables the early detection of infectious diseases and the implementation of mitigation protocols. The model simulation results show that the use of IoT-based biosensor network to inform the implementation of the epidemic mitigation protocol ensures the reduction in infection rate and the cumulative number of infected persons in each population. © 2022 Inderscience Enterprises Ltd.", "Keywords": "biosensor network; epidemics; internet of things; IoT; One Health", "DOI": "10.1504/IJSNET.2022.125112", "PubYear": 2022, "Volume": "39", "Issue": "4", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "Uche K. Chude <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96028819, "Title": "Improved fault tolerant SPRT detection method for node replication attacks in wireless sensor networks", "Abstract": "As the internet of things (IoT) emerges, the application and usage of the wireless sensor network (WSN) have been increasing quickly. Meanwhile, there are many threats and risks of information security that need to be effectively dealt with. One of these network attacks is the node replication attack. The attackers may catch sensor nodes, clone them, release them into the original network, and launch various internal attacks. Many replica detection instruments/methods are proposed for this type of attack. However, most detection are characterised as high computation and communication costs. Some detection methods based on the sequential probability ratio test (SPRT) indicate much lower requirements of system overhead, but these prior works may sacrifice efficiency due to frequent retransmission of the message. In this paper, we propose a fault-tolerant method for replica detection based on the SPRT in WSNs. To improve the efficiency and reliability, we use the residual energy and slope of energy consumption of the node as appendices and then apply the SPRT to adjust the detection rate dynamically. The simulation results show that our proposed scheme achieves a better performance on the efficiency of detection and reduction of error rates. © 2022 Inderscience Enterprises Ltd.", "Keywords": "clone attack; node replication detection; sequential probability ratio test; SPRT; wireless sensor network; WSN", "DOI": "10.1504/IJSNET.2022.125109", "PubYear": 2022, "Volume": "39", "Issue": "4", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "JHJ School of Business, Texas Southern University, 3100 Cleburne Street, Houston, TX  77004, United States"}], "References": []}, {"ArticleId": 96028918, "Title": "Physiological and perceptual consequences of trust in collaborative robots: An empirical investigation of human and robot factors", "Abstract": "Measuring trust is an important element of effective human-robot collaborations (HRCs). It has largely relied on subjective responses and thus cannot be readily used for adapting robots in shared operations, particularly in shared-space manufacturing applications. Additionally, whether trust in such HRCs differ under altered operator cognitive states or with sex remains unknown. This study examined the impacts of operator cognitive fatigue, robot reliability, and operator sex on trust symptoms in collaborative robots through both objective measures (i.e., performance, heart rate variability) and subjective measures (i.e., surveys). Male and female participants were recruited to perform a metal surface polishing task in partnership with a collaborative robot (UR10), in which they underwent reliability conditions (reliable, unreliable) and cognitive fatigue conditions (fatigued, not fatigued). As compared to the reliable conditions, unreliable robot manipulations resulted in perceived trust, an increase in both sympathetic and parasympathetic activity, and operator-induced reduction in task efficiency and accuracy but not precision. Cognitive fatigue was shown to correlate with higher fatigue scores and reduced task efficiency, more severely impacting females. The results highlight key interplays between operator states of fatigue, sex, and robot reliability on both subjective and objective responses of trust. These findings provide a strong foundation for future investigations on better understanding the relationship between human factors and trust in HRC as well as aid in developing more diagnostic and deployable measures of trust.", "Keywords": "Cognitive fatigue;ECG;Human-robot collaboration;Reliability;Sex;Trust", "DOI": "10.1016/j.apergo.2022.103863", "PubYear": 2023, "Volume": "106", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The Industrial and Systems Engineering Department, Texas A&amp;M University, College Station, Tx, USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Industrial and Systems Engineering Department, Texas A&amp;M University, College Station, Tx, USA; The Mechanical Engineering Department, Texas A&amp;M University, College Station, Tx, USA. Electronic address:  ."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "The Mechanical Engineering Department, Texas A&amp;M University, College Station, Tx, USA."}], "References": [{"Title": "Friend or Foe? Understanding Assembly Workers’ Acceptance of Human-robot Collaboration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}, {"Title": "Dual-edge robotic gear chamfering with registration error compensation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "102082", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Trust in Shared-Space Collaborative Robots: Shedding Light on the Human Brain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "2", "Page": "490", "JournalTitle": "Human Factors: The Journal of the Human Factors and Ergonomics Society"}]}, {"ArticleId": 96028919, "Title": "Assessment of spectrum sensing using support vector machine combined with principal component analysis", "Abstract": "Cognitive radio (CR) is an up-and-coming technology to rectify the problem of under-utilisation of the allocated spectrum and meet the increasing demand for free spectrum. Spectrum sensing empowers the CR to adjust to its surroundings by locating free spectrum. Although spectrum sensing using a support vector machine (SVM) is already found in literature, an SVM combined with principal component analysis (PCA) and varying the kernel scale is yet to be investigated. In this paper, we perform spectrum sensing using an SVM and evaluate the performances of various kernel functions used in the SVM as well as how the performances of the learning algorithm change as we apply PCA and vary the kernel scales. We then compare the training time of the SVM kernels. Finally, we calculate the contributions of power, variance, skewness, and kurtosis of the received signal towards the decision-making process of the learning algorithm. © 2022 Inderscience Enterprises Ltd.", "Keywords": "cognitive radio; feature importance; internet of things; IoT; kernel scale value; large margin classifier; machine learning; OFDM; orthogonal frequency division multiplexing; PCA; principal component analysis; spectrum scarcity; spectrum sensing; support vector machine; SVM", "DOI": "10.1504/IJSNET.2022.125115", "PubYear": 2022, "Volume": "39", "Issue": "4", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ICT Department, School of Engineering and Technology, Asian Institute of Technology, P.O. Box 4, 58 Moo 9, Km. 42, Paholyothin Highway, Klong Luang, Pathum Thani, 12120, Thailand"}, {"AuthorId": 2, "Name": "Attaphongse Taparugssanagorn", "Affiliation": "ICT Department, School of Engineering and Technology, Asian Institute of Technology, P.O. Box 4, 58 Moo 9, Km. 42, Paholyothin Highway, Klong Luang, Pathum Thani, 12120, Thailand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Pharmaceutical Sciences, Chulalongkorn University, 254 Phayathai Road, Wang Mai Subdistrict, Pathum Wan District, Bangkok, 10330, Thailand"}], "References": []}, {"ArticleId": 96028940, "Title": "ANALYSIS OF THE DAMAGE COUPLING", "Abstract": "", "Keywords": "", "DOI": "10.2316/J.2022.206-0580", "PubYear": 2022, "Volume": "37", "Issue": "", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "MODEL OF REINFORCED CONCRETE", "Affiliation": ""}], "References": []}, {"ArticleId": 96029104, "Title": "“Thinstagram”: Image content and observer body satisfaction influence the when and where of eye movements during instagram image viewing", "Abstract": "Selective Exposure Theory (Arugue<PERSON> &amp; Cal<PERSON>, 2018; <PERSON><PERSON><PERSON> et al., 2020) suggests that on social media, viewers pay most attention to content which aligns with their values and preferences. Individuals engage in self-assessment by comparing themselves to others (Social comparison theory: <PERSON><PERSON>, 1954). We predicted that the characteristics of Instagram arrays and participants&#x27; own body satisfaction would combine to influence their visual processing of computer-based images. A 3 (Body Shape: Underweight, Average, Overweight) × 2 (Body Part: Face-only; Body-only) repeated measures design was used. We recruited 60 (young) women to view arrays of images as displayed on Instagram [ M <sub>age</sub> = 20.75 years, SD <sub>age</sub> = 2.74 years]. A separate, naïve group of 37 participants rated 165 stimulus images on a scale of under-to-over-weight. These normed images were used to create artificial, ecologically-valid 3 × 4 Instagram image arrays containing two of each type of stimulus image. We recorded participants&#x27; eye movements with a high degree of spatial and temporal resolution while participants freely engaged with these arrays. We then collected participants&#x27; body satisfaction data (<PERSON> et al., 1990). Results demonstrated inter-relationships between eye movement behaviour and Body Shape, Body Part, and body satisfaction. In short, both bottom-up stimulus characteristics and top-down satisfaction impacted measures of processing. Image content was particularly relevant to ‘when’ measures of processing time, whereas body satisfaction was more-influential upon ‘where’ measurements (fixations counts, number of visits per stimulus image). Our study is the first of its kind to show such effects. Future research is needed to understand such effects in clinical and/or non-female users of Instagram and other platforms.", "Keywords": "Body satisfaction ; Eye movements ; Instagram ; Self perception ; Social comparison ; Social media", "DOI": "10.1016/j.chb.2022.107464", "PubYear": 2023, "Volume": "138", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Applied Psychology Research Group, School of Education and Social Sciences, University of the West of Scotland, United Kingdom;Corresponding author. School of Education and Social Science, University of the West of Scotland, Paisley, PA1 2BE, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer & Information Sciences, University of Strathclyde, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Applied Psychology Research Group, School of Education and Social Sciences, University of the West of Scotland, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Education, University of Glasgow, United Kingdom"}], "References": []}, {"ArticleId": 96029243, "Title": "Who we are and what we have", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3557984", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of California, Irvine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "ESPOL"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Maryland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Washington"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "City, University of London"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Umeå University"}], "References": []}, {"ArticleId": 96029244, "Title": "Latine health and development in the digital age", "Abstract": "", "Keywords": "", "DOI": "10.1145/3550199", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Irvine"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "University of California, Davis Health"}], "References": []}, {"ArticleId": 96029253, "Title": "Special Issue on The Workshop on MAthematical performance Modeling and Analysis (MAMA 2022)", "Abstract": "<p>The complexity of computer systems, networks and applications, as well as the advancements in computer technology, continue to grow at a rapid pace. Mathematical analysis, modeling and optimization have been playing, and continue to play, an important role in research studies to investigate fundamental issues and tradeoffs at the core of performance problems in the design and implementation of complex computer systems, networks and applications.</p>", "Keywords": "", "DOI": "10.1145/3561074.3561078", "PubYear": 2022, "Volume": "50", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Thomas J. Watson Research Center, Yorktown Heights, NY, USA"}], "References": []}, {"ArticleId": 96029254, "Title": "Building space for feminist solidarity", "Abstract": "<p> In this forum we explore different perspectives for how to apply intersectionality as a critical framework for design across multiple contexts. --- <PERSON><PERSON><PERSON> <PERSON> and <PERSON><PERSON><PERSON>, Editors </p>", "Keywords": "", "DOI": "10.1145/3554759", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Michigan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DePaul University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Michigan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Maryland College Park"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Auburn University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Florida State University"}], "References": [{"Title": "\"The Personal is Political\"", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "CSCW2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "I Can't Breathe", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "CSCW3", "Page": "1", "JournalTitle": "Proceedings of the ACM on Human-Computer Interaction"}, {"Title": "Using black feminist epistemologies and activist frameworks to counter structural racism in design", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "5", "Page": "56", "JournalTitle": "interactions"}]}, {"ArticleId": 96029257, "Title": "Designing for connection with local threatened species", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3555722", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Queensland University of Technology"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Queensland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Queensland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Digital Wando"}], "References": []}, {"ArticleId": 96029259, "Title": "On Quantum Algorithms for Random Walks in the Nonnegative Quarter Plane", "Abstract": "<p>It is well known that strong connections exist between random walks (RWs) in the nonnegative quarter plane and the mathematical performance modeling, analysis and optimization of computer systems and communication networks. Examples include adaptive threshold-based load balancing in concert with affinity scheduling (e.g., [16, 10]), scheduling parallel jobs on parallel computer systems (e.g., [15]), and asymptotic scalabilty in wireless and linear loss networks (e.g., [9]).</p>", "Keywords": "", "DOI": "10.1145/3561074.3561089", "PubYear": 2022, "Volume": "50", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Thomas J. Watson Research Center, Yorktown Heights, NY, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Thomas J. Watson Research Center, Yorktown Heights, NY, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Thomas J. Watson Research Center, Yorktown Heights, NY, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Thomas J. Watson Research Center, Yorktown Heights, NY, USA"}], "References": []}, {"ArticleId": 96029286, "Title": "Fault diagnosis and prediction with industrial internet of things on bearing and gear assembly", "Abstract": "In the era of automation, mechanical components such as bearings and gears are widely used in industrial machinery to transmit power and motion. Failure in these components directly affects the functioning of the machinery and causes the loss of money and time. Therefore, fault diagnosis and prediction of these components in advance are necessary to avoid catastrophic consequences. In this research, an experimental set-up is developed to predict the fault for various cases such as proper configuration, defective bearing, and defective gear configuration. An IIoT and conventional time and frequency domain-based techniques are used for condition-based monitoring of bearing-gear assembly. IIoT-based systems can perform three major tasks; measuring and displaying the real-time vibrational responses of bearing-gear assembly, comparing it with the prescribed threshold value, and sending a warning message to the end-user using the Blynk application, if the acquired acceleration values are greater than the prescribed threshold value. © 2022 Inderscience Enterprises Ltd.", "Keywords": "bearing; Blynk application; gear; IIoT; industrial internet of things; NodeMCU; vibration analysis", "DOI": "10.1504/IJSNET.2022.125114", "PubYear": 2022, "Volume": "39", "Issue": "4", "JournalId": 12292, "JournalTitle": "International Journal of Sensor Networks", "ISSN": "1748-1279", "EISSN": "1748-1287", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Punjab Engineering College (Deemed to be University), Chandigarh, 160012, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Punjab Engineering College (Deemed to be University), Chandigarh, 160012, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Punjab Engineering College (Deemed to be University), Chandigarh, 160012, India"}], "References": []}, {"ArticleId": 96029416, "Title": "A Systematic Literature Review of Driver Inattention Monitoring Systems for Smart Car", "Abstract": "<p>In recent years, a significant increase in road accidents worldwide has been observed. This can partly be due to either driver distraction or fatigue. Therefore, a reliable alerting system that can detect the driver's inattention including fatigue, sleep, and distraction is necessarily required to prevent any potential accidents. The aim of this paper is to conduct a systematic review of literature (SLR) on monitoring driver inattention. In particular, the present study deals with different aspects of prior studies such as the sensors used; the types of data, the feature engineering techniques, the machine-learning techniques applied and their performance along with, the dataset used, etc. anotherFour approaches can be depicted from literature according to indicators they are based on: physiological, physical, driver performance and hybrid approach. We will focus on these different approaches in order to answer different questions, starting with the type of indicators used in the case of distraction or fatigue detection, the different datasets employed, the feature extraction techniques and the machine learning models applied. Furthermore, the study examines the practicality and reliability of each of the four approaches, as well as possible future prospects in the area, and highlights new challenges in the field of driver inattention detection with both forms of fatigue and distraction.</p>", "Keywords": "driver inattention;driver distraction;driver fatigue;driver drowsiness;machine-learning;deep-learning;Systematic Literature Review", "DOI": "10.3991/ijim.v16i16.33075", "PubYear": 2022, "Volume": "16", "Issue": "16", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Modeling and Information Technology Faculty of Sciences Ben M’SIK University Hassan II"}, {"AuthorId": 2, "Name": "Faouzia Benabbou", "Affiliation": "Laboratory of Modeling and Information Technology Faculty of Sciences Ben M’SIK University Hassan II of Casablanca, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96029435, "Title": "Augmented Reality Books: Motivation, Attitudes, and Behaviors of Young Readers", "Abstract": "<p>Augmented Reality (AR) books show potential to increase young learners’ reading motivation, which is important given children's declining reading motivation over the school years. Previous studies measured reading motivation only in higher education and only after users’ experience with AR. Few empirical studies have focused on school-aged children and those examined attitudes, not motivation. This study aimed to: a) examine how young children's motivation changes through the experience of reading AR books and b) document their attitudes and behaviors after this experience. Participants in this pre-test post-test case study were 40 fourth and fifth graders. Data sources included validated questionnaires and an observation protocol of children’s behavior while interacting with AR books. The results of paired samples t-tests for children's motivation indicated a statistically significant increase in attention (t (39) = - 3.07, p = 0.004), confidence (t (39) = - 2.44, p = 0.019) and satisfaction (t (39) = - 3.26, p = 0.002). Children read comfortably, seemed focused, and were eager to read more. They showed a high level of enthusiasm with AR technology when interacting with the first AR book, which notably decreased with the second book. The children maintained positive attitudes and behaviors towards AR. The study showed that even through short-term interactions, AR books have the potential to increase young students’ reading motivation. It adds to our knowledge concerning the use of AR books by primary school children, who are under-represented in the literature. Directions for future research are drawn.</p>", "Keywords": "Augmented Reality;reading motivation;attitudes;behaviors;primary school;young children", "DOI": "10.3991/ijim.v16i16.31741", "PubYear": 2022, "Volume": "16", "Issue": "16", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cyprus University of Technology"}], "References": []}, {"ArticleId": 96029481, "Title": "Learner behaviors associated with uses of resources and learning pathways in blended learning scenarios", "Abstract": "The objective of this research is to analyze learner behaviors, uses of resources and learning pathways in blended learning scenarios. Key principles and resources for blended learning practices are addressed in the theoretical framework, along with their relationships with learning pathways, student performance and ICT skills. A within-subject design was adopted, consisting in the application of an ICT skills survey to 92 low-income secondary school students from a Brazilian Northeastern public school, followed by the implementation of series of lessons comprising digital resources based on blended learning practices. Behaviors and uses of resources identified in screen recordings of learners&#x27; responses to tasks were contrasted among groups, learning scenarios and with regard to students’ performance scores and ICT skills. Nonparametric tests pointed to significant differences between groups in terms of ICT skills and to no significant differences regarding performance. There were predominantly no significant differences within observed uses of resources and pathways in terms of associated performance or ICT scores. Differences between patterns of use and pathways observed in each lesson are analyzed. Data suggests pedagogical practices learners were previously exposed to impacted their engagement, and uses of different resources were interrelated in a systemic perspective. Task-orientedness, rather than approaches to digital tools or choices of pathway, emerges in discussions as a key factor for learner performance, reinforcing the importance of designing learning scenarios which promote behavioral engagement.", "Keywords": "Blended learning ; Student behaviors ; Learning resources ; Learning pathways", "DOI": "10.1016/j.compedu.2022.104625", "PubYear": 2022, "Volume": "191", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "Felipe de Brito Lima", "Affiliation": "Unidade de Educação a Distância e Tecnologia, Universidade Federal Rural de Pernambuco, Recife, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pós-graduação Em Psicologia Cognitiva, Universidade Federal de Pernambuco, Recife, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centro de Informática, Universidade Federal de Pernambuco, Recife, Brazil;Corresponding author"}], "References": [{"Title": "Lurking and participation in the virtual classroom: The effects of gender, race, and age among graduate students in computer science", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "103854", "JournalTitle": "Computers & Education"}, {"Title": "Sustaining online academic discussions: Identifying the characteristics of messages that receive responses", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "156", "Issue": "", "Page": "103938", "JournalTitle": "Computers & Education"}, {"Title": "Emanant themes of blended learning in K-12 educational environments: Lessons from the Every Student Succeeds Act", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "163", "Issue": "", "Page": "104116", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 96029534, "Title": "Determinants of Customer Loyalty in a Cloud Computing Environment", "Abstract": "<p>This study's two objectives are first to assess the relationships between customer loyalty and potential determinants (customer satisfaction with the website, after sale customer service, and product return handling) in the special PaaS environment where users can produce a greater variety of website features compared to the more widely studied SaaS environment and second to test the impact of the website's customer decision support system as a potential moderator for the relationship between customer satisfaction with the website and customer loyalty. To test the hypotheses, 138 CC client organizations participated by collecting data from their website customers accessing their order entry website applications. The results confirmed the importance of the proposed relationships and enabled several important managerial insights, including the importance of client organization choosing the appropriate CC approach to improve customer loyalty to the website.</p>", "Keywords": "", "DOI": "10.4018/IJCAC.308278", "PubYear": 2022, "Volume": "12", "Issue": "1", "JournalId": 29741, "JournalTitle": "International Journal of Cloud Applications and Computing", "ISSN": "2156-1834", "EISSN": "2156-1826", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Study of Relationship Among Service Quality of E-Commerce Websites, Customer Satisfaction, and Purchase Intention", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "3", "Page": "42", "JournalTitle": "International Journal of E-Business Research"}, {"Title": "Cloud-based services and customer satisfaction in the small and medium-sized businesses (SMBs)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "6", "Page": "1991", "JournalTitle": "Kybernetes"}, {"Title": "Research on influencing factors of customer satisfaction of e-commerce of characteristic agricultural products", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "199", "Issue": "", "Page": "1505", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 96029556, "Title": "Optimization of the oil rectification process by the selection criterion of the light petroleum products", "Abstract": "<p>The article is devoted to solving the problem of increasing the yield of light oil products at the primary oil refining unit by the methods of mathematical and simulation modeling. An overview of the software packages for technological modeling used for petroleum refineries is given. An optimization criterion, that allows determining the maximum yield of light oil products is received, as well as restrictions on the process parameters are established. The adequacy assessment of the initial data dependence according to the Fisher criterion is given. A mathematical model of the petroleum distillation process for low-capacity installations is obtained. The model can be useful at optimizing the variable composition and consumption of raw materials (petroleum) according to the selection criterion for light oil products, taking into account seasonal requirements for the quality of petroleum products. The use of the proposed optimization criterion and the mathematical model on small-capacity oil distillation units improves the efficiency of process units. Keywords sectional model, petroleum product, quality indicators, optimization, boiling point, potential fraction content, selection</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2022-76-8-347-352", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Improvement of piston machines based on the <PERSON><PERSON><PERSON><PERSON> mechanism", "Abstract": "<p>The basic structure of the <PERSON><PERSON><PERSON><PERSON> mechanism is described. The distinctive features of this mechanism in comparison with the crank mechanism are considered. The devices that can be retrofitted using the <PERSON><PERSON><PERSON>kov mechanism and the areas where it can be applied are shown: small shipbuilding, agricultural machinery, aircraft, instrumentation, internal combustion engine and pumps for oil wells. The parameters, which can be improved using a fundamentally new mechanism, are given. Keywords Gorshkov mechanism, internal gearing, rack and pinion, planetary gearing, piston machines, connecting rod</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2022-76-8-355-358", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96029597, "Title": "Development and research of an automated control system for an automatic bottling line for chemical products", "Abstract": "<p>The article is devoted to the use of automatic control in the process equipment system. The purpose of the work is the production line automation for the manufacture of the largest number of finished products. The practical significance is to ensure full automation of bottling and speed up the process. Keywords reactor, bottling line, plugging, conveyor, control system automation</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2022-76-8-339-342", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96029604, "Title": "Tape drive speed and vibration acceleration sensor", "Abstract": "<p>The design of the speed and vibration acceleration sensor of the tape drive mechanism (DSVAS) for the parameters control, in particular, the vibration and speed of the tape drive mechanism from ferromagnetic and electrically conductive bodies using the Hall sensor as the main sensitive element is considered. DSVAS has an increased linearity of the metrological characteristics and sensitivity, is easy to operate and, with further transformation, can be used as a non-destructive testing flaw detector. A mathematical model with conclusions based on the results obtained is developed. The sensor can be put to use in the field of instrumentation, mechanical engineering, energy, diagnostics, etc. Keywords metrology, technology, information systems, linearity, magnetic Reynolds number, mathematical model</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2022-76-8-379-384", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96029719, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0020-0255(22)00975-6", "PubYear": 2022, "Volume": "610", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [], "References": []}, {"ArticleId": 96029724, "Title": "FastCache: A write-optimized edge storage system via concurrent merging cache for IoT applications", "Abstract": "To tackle the microwrite issue in Internet-of-Things (IoT) applications, i.e., massive small size and concurrent random write requests, a straightforward approach is to establish cache mechanisms at the edge to reconstruct writing operations and flush data efficiently. In spite of recent research efforts on cache mechanisms, existing approaches still face unsolvable problems like frequent competition on cache blocks, massive fragments caused by merging, and cache pollution due to cache updating, especially in IoT scenarios with highly concurrent microwrites. To address these problems, we design and implement FastCache, a write-optimized edge storage system via concurrent microwrites merging. By leveraging an optimistic lock scheme, we implement a two-level cache structure that dispatches the concurrent write threads efficiently and significantly mitigates the cache block competition problem. Further, we propose a flexible merging scheme to avoid excessive fragments and the corresponding cache updating policy based on a throughput-aware threshold and a Poisson Distribution Sampling scheme. Extensive experiments on both synthetic workloads and a real-world trace from 20 million power meters with highly concurrent microwrites demonstrate that FastCache outperforms the state-of-the-art approaches, e.g., up to 14.6 × and 5 × improvement in terms of IOPS on synthetic and real-world workloads, respectively.", "Keywords": "Internet of Things ; Edge storage system ; Write-optimized ; Cache ; Optimistic lock", "DOI": "10.1016/j.sysarc.2022.102718", "PubYear": 2022, "Volume": "131", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Key Laboratory for Novel Software Technology, Nanjing University, Xianlin Street 163, Nanjing, 210023, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Water Big Data Technology of Ministry of Water Resources and School of Computer and Information, Hohai University, Focheng West Road 8, Nanjing, 211100, China;National Key Laboratory for Novel Software Technology, Nanjing University, Xianlin Street 163, Nanjing, 210023, China;Corresponding author at: Key Laboratory of Water Big Data Technology of Ministry of Water Resources and School of Computer and Information, Hohai University, Focheng West Road 8, Nanjing, 211100, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Water Big Data Technology of Ministry of Water Resources and School of Computer and Information, Hohai University, Focheng West Road 8, Nanjing, 211100, China;National Key Laboratory for Novel Software Technology, Nanjing University, Xianlin Street 163, Nanjing, 210023, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory for Novel Software Technology, Nanjing University, Xianlin Street 163, Nanjing, 210023, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory for Novel Software Technology, Nanjing University, Xianlin Street 163, Nanjing, 210023, China;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory for Novel Software Technology, Nanjing University, Xianlin Street 163, Nanjing, 210023, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Southern Power Grid Digital Grid Research Institute Co. Ltd, Huangpu District, Yunsheng Science Part, Guangzhou, 510530, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "China Southern Power Grid Digital Grid Research Institute Co. Ltd, Huangpu District, Yunsheng Science Part, Guangzhou, 510530, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "China Southern Power Grid Digital Grid Research Institute Co. Ltd, Huangpu District, Yunsheng Science Part, Guangzhou, 510530, China"}], "References": [{"Title": "PHDFS: Optimizing I/O performance of HDFS in deep learning cloud computing platform", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "101810", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Light-weight AI and IoT collaboration for surveillance video pre-processing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "101934", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Edge-based auditing method for data security in resource-constrained Internet of Things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "101971", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Improving in-memory file system reading performance by fine-grained user-space cache mechanisms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "101994", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "NVMM-Oriented Hierarchical Persistent Client Caching for Lustre", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Storage"}, {"Title": "Stabilizing and boosting I/O performance for file systems with journaling on NVMe SSD", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "3", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Hadoop Perfect File: A fast and memory-efficient metadata access archive file to face small files problem in HDFS", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "156", "Issue": "", "Page": "119", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "SSD internal cache management policies: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "102334", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Resource provisioning in edge/fog computing: A Comprehensive and Systematic Review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "102362", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "A novel centralized coded caching scheme for edge caching basestation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "102556", "JournalTitle": "Journal of Systems Architecture"}]}, {"ArticleId": 96029730, "Title": "An integrity-preserving technique for range queries over data streams in two-tier sensor networks", "Abstract": "Two-tier sensor networks enable energy and computation saving due to the introduction of non-constrained storage nodes acting as intermediates between the sensors, which provide data, and the sink, which needs to access these data by submitting queries to the storage nodes. However, the storage nodes maintain a lot of data coming from different sensors and represent a single point of failure targeted by the attackers. In this scenario, robust guarantees about the integrity of the query result (i.e., completeness, freshness, and correctness) should be provided. This is a very well-known problem, traditionally faced through Merkle-Hash-Tree-based data structures . This paper presents a technique more efficient than the state-of-the-art methods for data insertions and deletions, supporting range queries over even non-temporal dimensions. Thus, the proposed solution appears suitable to the considered scenario, in which data can be updated with high frequency and insertions and deletions are executed by constrained devices.", "Keywords": "", "DOI": "10.1016/j.comnet.2022.109316", "PubYear": 2022, "Volume": "217", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University Mediterranea of Reggio Calabria, Via dell’Università, 25, Reggio Calabria, 89124, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University Mediterranea of Reggio Calabria, Via dell’Università, 25, Reggio Calabria, 89124, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University Mediterranea of Reggio Calabria, Via dell’Università, 25, Reggio Calabria, 89124, Italy"}], "References": [{"Title": "Securing top-k query processing in two-tiered sensor networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "62", "JournalTitle": "Connection Science"}]}, {"ArticleId": 96029825, "Title": "SIGACT news complexity theory column 114", "Abstract": "<p> This issue's column (which comes immediately after some memories of and comments on <PERSON><PERSON>, who passed away a few days ago), by <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>, is a tour--- A Panorama of Counting Problems the Decision Version of which is in P ---of the exciting world of counting functions whose decision version (the set of inputs on which the function evaluates to zero) is in P. </p>", "Keywords": "", "DOI": "10.1145/3561064.3561071", "PubYear": 2022, "Volume": "53", "Issue": "3", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Rochester"}], "References": []}, {"ArticleId": 96029826, "Title": "Between metaphor and meaning", "Abstract": "", "Keywords": "", "DOI": "10.1145/3551669", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Cambridge"}], "References": []}, {"ArticleId": 96029835, "Title": "The book review column", "Abstract": "<p>My predecessor as book review editor, <PERSON>, held the post for 17 years. In his final column in January 2015, he said that that's longer than anyone should have the job. I count myself as lucky to have been nominated as his successor. At the time, <PERSON> advised me not to stay on for more than 5 years. Well, here it is 6.5 years out and some 26 columns later, so it is high time for me to retire from the position.</p>", "Keywords": "", "DOI": "10.1145/3561064.3561065", "PubYear": 2022, "Volume": "53", "Issue": "3", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Clark University"}], "References": []}, {"ArticleId": 96029840, "Title": "Technical report column", "Abstract": "<p>Welcome to the Technical Reports Column. If your institution publishes technical reports that you'd like to have included here, please contact me at the email address above.</p>", "Keywords": "", "DOI": "10.1145/3561064.3561069", "PubYear": 2022, "Volume": "53", "Issue": "3", "JournalId": 10099, "JournalTitle": "ACM SIGACT News", "ISSN": "0163-5700", "EISSN": "1943-5827", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Minnesota State University"}], "References": []}, {"ArticleId": 96029860, "Title": "Mentoring across SIGCHI", "Abstract": "No abstract available.", "Keywords": "", "DOI": "10.1145/3557823", "PubYear": 2022, "Volume": "29", "Issue": "5", "JournalId": 7772, "JournalTitle": "interactions", "ISSN": "1072-5520", "EISSN": "1558-3449", "Authors": [{"AuthorId": 1, "Name": " SIGCHI Executive Committee", "Affiliation": ""}], "References": []}, {"ArticleId": 96029864, "Title": "Engaging educators in the ideation of scenarios for cross-reality game-based learning experiences", "Abstract": "Cross-reality media technology creates alternate reality experiences in which the physical and the virtual world are interconnected and influence each other through a network of sensors and actuators. Despite technological advances, the landscape of cross-reality technology as an enabler of alternate reality educational experiences has not been explored yet. The technical expertise required to set up and program such mixed environments is too high to engage the problem owners (i.e. educational experts) in the design process and, hence, user-driven innovation remains challenging. In this paper we explore the co-creation of cross-reality experiences for educational games. We created a no-programming toolkit that provides a visual language and interface abstractions to quickly build prototypes of cross-reality interactions. The toolkit supports experience prototyping and allows designers to coproduce, with educational experts, meaningful scenarios while they create, try out and reconfigure their prototypes. We report on a workshop with 36 educators where the toolkit was used to ideate cross-reality games for education. We discuss use cases of game-based learning applications developed by the participants that follow different pedagogical strategies and combine different physical and virtual spaces and times. We outline implications for the design of cross-reality interactions in educational settings that trigger further research and technological developments.", "Keywords": "Cross-reality; Digital game based learning; Prototyping", "DOI": "10.1007/s11042-022-13632-2", "PubYear": 2024, "Volume": "83", "Issue": "15", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "Telmo <PERSON>", "Affiliation": "DEI Lab – Computer Science Department of the Universidad Carlos III de Madrid, Madrid, Spain; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DEI Lab – Computer Science Department of the Universidad Carlos III de Madrid, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "DEI Lab – Computer Science Department of the Universidad Carlos III de Madrid, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "DEI Lab – Computer Science Department of the Universidad Carlos III de Madrid, Madrid, Spain"}], "References": [{"Title": "A mixed reality simulation offers strategic practice for pre-service teachers", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "103696", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 96029871, "Title": "A discrimination model for dead cocoons using near-infrared transmission spectra analyses", "Abstract": "Silk from cocoons is widely used as a functional raw material for alleviating skin diseases, improving memory, preventing diabetes, and as antioxidants. Dead cocoons are screened to utilize silk, resulting in poor marketability and cross-contamination of other cocoons. Considering that the screening of cocoons is performed manually to date, there is a high demand for automating this process. Spectroscopic analyses are actively used for non-destructive analysis methods. In this study, a discrimination model was developed to determine whether a cocoon is dead or alive using the near-infrared transmission spectra. A total of 367 normal cocoons and 152 dead cocoons were used in our experiment, and the cocoons were cut to determine whether they were dead or alive. The near-infrared transmission spectra were obtained for each cocoon for the wavelength band 900–1700 nm. Nine pre-processing methods were applied to eliminate spectral noise; pre-processing related to normalization demonstrated significant difference compared to other pre-processing methods. Furthermore, principal component analysis (PCA) was used to visualize the distribution of spectra, and discrimination models were developed using the wavelength range of the highest transmittance intensity and partial least squares-discriminant analysis (PLS-DA). PCA successfully discriminated between normal and dead cocoons. The discrimination model according to the wavelength range of the highest transmittance intensity demonstrated a discriminant accuracy of 94.80 %. Using PLS-DA with range normalization pre-processing spectra, the discriminant accuracies were 94.56 % and 94.64 % for calibration and validation, respectively. Thus, the proposed method can help discriminate between normal and dead cocoons non-destructively using the near infrared transmission spectra.", "Keywords": "Near-infrared transmission spectra analyses ; Discrimination ; Cocoon ; PCA ; PLS-DA", "DOI": "10.1016/j.sna.2022.113857", "PubYear": 2022, "Volume": "346", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Engineering, National Institute of Agricultural Sciences, 310 Nongsaengmy<PERSON>–ro, Deokjin–gu, Jeonju 54875, the Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Biosystems Engineering, College of Agricultural and Life Sciences, Seoul National University, 1 Gwanak-ro, Gwanak–gu, Seoul 08826, the Republic of Korea;Global Smart Farm Convergence Major, Seoul National University, 1 Gwanak-ro, Gwanak–gu, Seoul, 08826, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Biology, National Institute of Agricultural Sciences, 166 Nongsaen<PERSON>ro, <PERSON><PERSON><PERSON><PERSON>, Wanju 55365, the Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Engineering, National Institute of Agricultural Sciences, 310 Nongsaeng<PERSON>ro, Deokjin–gu, Jeonju 54875, the Republic of Korea;Corresponding author"}], "References": []}, {"ArticleId": 96029877, "Title": "A linear haptic motor with cogging force optimization", "Abstract": "This paper proposes a new linear haptic motor design that considers a new magnetic circuit, cogging-force optimization, and spring shape. The force distribution on the haptic motor is calculated using the Maxwell stress tensor. To reduce the spring stiffness of the mechanical system, the cogging force is treated as a linear negative stiffness. Several new designs have been proposed to meet the design targets. The design of center and side core widths is used in the optimization process to obtain the maximum cogging force and current force. The final design of the linear haptic motor provides good acceleration performance on a dummy jig that is greater than 1.0 G. The experimental and analysis results are in good agreement.", "Keywords": "Acceleration ; Cogging force ; Electromagnetic force ; Magnetic circuit ; Vibrations", "DOI": "10.1016/j.sna.2022.113860", "PubYear": 2022, "Volume": "346", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "Zhi-<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Pusan National University, Busan 46241, South Korea"}, {"AuthorId": 2, "Name": "Ji-Hun Park", "Affiliation": "School of Mechanical Engineering, Pusan National University, Busan 46241, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering and Automation, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Pusan National University, Busan 46241, South Korea;Corresponding author"}], "References": [{"Title": "Design and analysis of watch speaker to enhance waterproof performance by using liquid silicone rubber side diaphragm", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; Ki-<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "338", "Issue": "", "Page": "113452", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 96029901, "Title": "GAN-based Technology of Generating Spoofing-jamming I/Q Signal", "Abstract": "<p lang=\"zh\"><p><p>The existing wireless communication interference methods rely heavily on the characteristics of the target signals obtained in communication reconnaissance, require complex prior knowledge, and have problems in keeping up with the dynamic changes of relevant parameters. This paper proposes a GAN-based tech-nology of generating spoofing-jamming I/Q signals. The jammer will be able to deceive and interfere with op-ponents through GAN by generating spoofing-jamming I/Q signals highly correlated to real I/Q signals without prior knowledge. This paper first introduces the principle of GAN and the GAN model to be adopted; uses a software radio system composed of LabView and NI USRP software and hardware plat-forms to simulate real communication scenarios to collect real I/Q signal data; generates spoofing-jamming I/Q signals through GAN model; uses t-SNE algorithm to perform dimensionality reduction on both the real and the spoofing-jamming I/Q signal data to visualize their distributions; finally, tests the spoofing-jamming I/Q signals on receiver’s pre-trained classifier. The experimental results show that GAN provides an effective method for generating high-quality spoofing-jamming I/Q signals.</p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022083304011", "PubYear": 2022, "Volume": "33", "Issue": "4", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>v", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96029951, "Title": "Design of a FPGA accelerator for the FIVE fuzzy interpolation method", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2022.10050320", "PubYear": 2022, "Volume": "68", "Issue": "4", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96029970, "Title": "No means ‘No’: a non-improper modeling approach, with embedded speculative context", "Abstract": "Motivation <p>The medical data are complex in nature as terms that appear in records usually appear in different contexts. Through this paper, we investigate various bio model’s embeddings(BioBERT, BioELECTRA, PubMedBERT) on their understanding of \"negation and speculation context\" wherein we found that these models were unable to differentiate \"negated context\" vs \"non-negated context\". To measure the understanding of models, we used cosine similarity scores of negated sentence embeddings vs non-negated sentence embeddings pairs. For improving these models, we introduce a generic super tuning approach to enhance the embeddings on \"negation and speculation context\" by utilizing a synthesized dataset.</p> Results <p>After super-tuning the models we can see that the model’s embeddings are now understanding negative and speculative contexts much better. Furthermore, we fine-tuned the super tuned models on various tasks and we found that the model has outperformed the previous models and achieved state-of-the-art (SOTA) on negation, speculation cue, and scope detection tasks on BioScope abstracts and Sherlock dataset. We also confirmed that our approach had a very minimal trade-off in the performance of the model in other tasks like Natural Language Inference after super-tuning.</p> Availability <p>The source code and the models are available at: https://github.com/comprehend/engg-airesearch/tree/uncertainty-super-tuning.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btac593", "PubYear": 2022, "Volume": "38", "Issue": "20", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Saama AI Research Lab , Pune 411057, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Saama AI Research Lab , Pune 411057, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Saama AI Research Lab , Pune 411057, India"}], "References": []}, {"ArticleId": 96029990, "Title": "Cyber-attacks visualisation and prediction in complex multi-stage network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2022.10050315", "PubYear": 2022, "Volume": "68", "Issue": "4", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Waleed Bander <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96030026, "Title": "On a secured channel selection in cognitive radio networks", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2022.10050307", "PubYear": 2022, "Volume": "18", "Issue": "3/4", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96030029, "Title": "Secure zero-effort two-factor authentication based on time-frequency audio analysis", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2022.10050306", "PubYear": 2022, "Volume": "18", "Issue": "3/4", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Shen Yan", "Affiliation": ""}], "References": []}, {"ArticleId": 96030097, "Title": "Fintech Services and the Drivers of Their Implementation in Small and Medium Enterprises", "Abstract": "<p>Fintech has been one of the biggest agents of change in the financial sector worldwide, deserving an in-depth analysis as the aim of this study (including factors leading to its adoption, consequences, etc.). During the COVID-19 pandemic, the financial area and Fintech services allied to technology has increased efficiency, convenience, and security. To better understand this type of service, the research follows a quantitative methodology. The quantitative method included a questionnaire survey of companies that are Fintech customers, totaling 49 valid responses from firms (collected over a three-month period and which involved sending over a thousand emails to numerous companies). The response rate was low due to both the pandemic and the conjuncture with major war, which are generating uncertainty in business. The analysis was based on descriptive statistics, an assessment of the metric qualities of the scales, reliability and an Exploratory Factor Analysis, Pearson correlations and Hypothesis testing. The positive and significant effect of the technological context (perceived convenience, usefulness and effectiveness and perceived safety and trust) and the organizational context (ecological footprint reduction and internal cost reduction) on Fintech service adoption intention was confirmed. Hypothesis Three was partially confirmed since only consumer trends and reputation perception have a positive and significant effect on the intention to adopt Fintech by SMEs. The moderating effect of the environmental context in the relationship between the technological context and the intention to adopt Fintech by SMEs was partially proven, but the same was not verified in the relationship between the organizational context and the intention to adopt Fintech by SMEs. Portugal seems to be on the same adoption path as the rest of the western world, and Fintech services will undoubtedly increase, in a kind of revolution in which the strongest and those able to adapt to the markets and their needs will survive.</p>", "Keywords": "Fintech; finance; digital finance; SaaS; digital innovation; COVID-19; e-commerce; disruptive innovation; Fintech ecosystem Fintech ; finance ; digital finance ; SaaS ; digital innovation ; COVID-19 ; e-commerce ; disruptive innovation ; Fintech ecosystem", "DOI": "10.3390/info13090409", "PubYear": 2022, "Volume": "13", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Economics, Management, Industrial Engineering and Tourism (DEGEIT), Campus Universitário de Santiago, University of Aveiro, 3810-193 Aveiro, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Economics, Management, Industrial Engineering and Tourism (DEGEIT), Campus Universitário de Santiago, University of Aveiro, 3810-193 Aveiro, Portugal↑INESC TEC, Institute for Systems and Computer Engineering, Technology and Science, 4200-465 Porto, Portugal↑GOVCOPP, The Research Unit on Governance, Competitiveness and Public Policies, 3810-193 Aveiro, Portugal"}, {"AuthorId": 3, "Name": "Ana <PERSON>-<PERSON>", "Affiliation": "School of Psychology, ISPA—Instituto Universitário, Rua do Jardim do Tabaco 34, 1149-041 Lisboa, Portugal; Corresponding author"}], "References": []}, {"ArticleId": 96030100, "Title": "Semantic Connections in the Complex Sentences for Post-Editing Machine Translation in the Kazakh Language", "Abstract": "<p>The problems of machine translation are constantly arising. While the most advanced translation platforms, such as Google and Yandex, allow for high-quality translations of languages with simple grammatical structures, more morphologically rich languages still suffer from the translation of complex sentences, and translation services leave many structural errors. This study focused on designing the rules for the grammatical structures of complex sentences in the Kazakh language, which has a difficult grammar with many rules. First, the types of complex sentences in the Kazakh language were thoroughly observed with the use of templates from the FuzzyWuzzy library. Then, the correction of complex sentences was completed with parallel corpora. The sentences were translated into English and Russian by existing machine translation systems. Therefore, the grammar of both Kazakh–English and Kazakh–Russian language pairs was considered. They both used the rules specifically designed for the post-editing steps. Finally, the performance of the developed algorithm was evaluated for an accuracy score for each pair of languages. This approach was then proposed for use in other corpora generation, post-editing, and analysis systems in future works.</p>", "Keywords": "semantic connections; complex sentences; post-editing; machine translation; Kazakh language semantic connections ; complex sentences ; post-editing ; machine translation ; Kazakh language", "DOI": "10.3390/info13090411", "PubYear": 2022, "Volume": "13", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information and Computational Technologies, Al-Farabi Kazakh National University, Almaty 050010, Kazakhstan; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Information and Computational Technologies, Al-Farabi Kazakh National University, Almaty 050010, Kazakhstan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The Department of Information Systems, Faculty of Information Technology, Al-Farabi Kazakh National University, Almaty 050040, Kazakhstan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The Department of Information Systems, Faculty of Information Technology, Al-Farabi Kazakh National University, Almaty 050040, Kazakhstan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The Department of Information Systems, Faculty of Information Technology, Al-Farabi Kazakh National University, Almaty 050040, Kazakhstan"}], "References": []}, {"ArticleId": 96030147, "Title": "Development and research of an automated control system for a lathe DIP-500", "Abstract": "<p>Ways for upgrading the DIP-500 lathe for improving the quality and speed up the process of workpiece electric-spark alloying of parts are proposed. Keywords electric-spark alloying (ESA), roll, wear-resistant coatings, lathe, automated control system</p>", "Keywords": "", "DOI": "10.36652/0869-4931-2022-76-8-343-346", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 70277, "JournalTitle": "Automation. Modern Techologies", "ISSN": "0869-4931", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 96030231, "Title": "A Discrete Particle Swarm Optimization Algorithm Based on Neighbor Cognition to Solve the Problem of Social Influence Maximization", "Abstract": "<p lang=\"zh\"><p><p>In view of the problem that the estimation method of node influence in social network is not comprehen-sive and the Particle Swarm Optimization (PSO) algorithm is easy to fall into the local optimal and the lo-cal search ability is insufficient. In this paper, we proposed a Neighbor Cognitive Discrete Particle Swarm Optimization (NCDPSO) algorithm. Aiming at the problem of influence in social networks, a new node influence measure method is proposed, the three-degree theory is introduced to comprehensively estimate the influence of nodes. In order to improve the global search ability of the PSO, the “neighbor cognition” factor is proposed to enhance the breadth of learning; and the following bee strategy is introduced to pro-pose particle density and survivability to control the number of elite clones, so as to solve the problem of insufficient local search ability of the algorithm. Finally, the validity of the proposed algorithm is verified by testing on real data sets and comparing with other algorithms.</p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022083304009", "PubYear": 2022, "Volume": "33", "Issue": "4", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Qiao<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96030257, "Title": "Model-free reinforcement learning for robust locomotion using demonstrations from trajectory optimization", "Abstract": "<p>We present a general, two-stage reinforcement learning approach to create robust policies that can be deployed on real robots without any additional training using a single demonstration generated by trajectory optimization. The demonstration is used in the first stage as a starting point to facilitate initial exploration. In the second stage, the relevant task reward is optimized directly and a policy robust to environment uncertainties is computed. We demonstrate and examine in detail the performance and robustness of our approach on highly dynamic hopping and bounding tasks on a quadruped robot.</p>", "Keywords": "legged locomotion; deep reinforcement learning; Trajectory optimization; robust control policies; contact uncertainty", "DOI": "10.3389/frobt.2022.854212", "PubYear": 2022, "Volume": "9", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Movement Generation and Control Group, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> ", "Affiliation": "Movement Generation and Control Group, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> ", "Affiliation": "Movement Generation and Control Group, Germany; Machines in Motion Laboratory, United States"}], "References": [{"Title": "Learning quadrupedal locomotion over challenging terrain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "47", "Page": "eabc5986", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 96030286, "Title": "From data to interpretable models: machine learning for soil moisture forecasting", "Abstract": "Soil moisture is critical to agricultural business, ecosystem health, and certain hydrologically driven natural disasters. Monitoring data, though, is prone to instrumental noise, wide ranging extrema, and nonstationary response to rainfall where ground conditions change. Furthermore, existing soil moisture models generally forecast poorly for time periods greater than a few hours. To improve such forecasts, we introduce two data-driven models, the Naive Accumulative Representation (NAR) and the Additive Exponential Accumulative Representation (AEAR). Both of these models are rooted in deterministic, physically based hydrology, and we study their capabilities in forecasting soil moisture over time periods longer than a few hours. Learned model parameters represent the physically based unsaturated hydrological redistribution processes of gravity and suction. We validate our models using soil moisture and rainfall time series data collected from a steep gradient, post-wildfire site in southern California. Data analysis is complicated by rapid landscape change observed in steep, burned hillslopes in response to even small to moderate rain events. The proposed NAR and AEAR models are, in forecasting experiments, shown to be competitive with several established and state-of-the-art baselines. The AEAR model fits the data well for three distinct soil textures at variable depths below the ground surface (5, 15, and 30 cm). Similar robust results are demonstrated in controlled, laboratory-based experiments. Our AEAR model includes readily interpretable hydrologic parameters and provides more accurate forecasts than existing models for time horizons of 10–24 h. Such extended periods of warning for natural disasters, such as floods and landslides, provide actionable knowledge to reduce loss of life and property.", "Keywords": "Data analysis;Interpretable machine learning;Model optimization and fitting;Monitoring;Post-fire landslides;Soil moisture forecasting", "DOI": "10.1007/s41060-022-00347-8", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 27236, "JournalTitle": "International Journal of Data Science and Analytics", "ISSN": "2364-415X", "EISSN": "2364-4168", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Geology, Minerals, Energy, and Geophysics Science Center, U. S. Geological Survey, Moffett Field, California USA."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Norwegian University of Science and Technology, Trondheim, Norway."}], "References": [{"Title": "Iterative multiscale dynamic time warping (IMs-DTW): a tool for rainfall time series comparison", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "65", "JournalTitle": "International Journal of Data Science and Analytics"}]}, {"ArticleId": 96030287, "Title": "Putting ridesharing to the test: efficient and scalable solutions and the power of dynamic vehicle relocation", "Abstract": "<p>We study the optimization of large-scale, real-time ridesharing systems and propose a modular design methodology, Component Algorithms for Ridesharing (CAR). We evaluate a diverse set of CARs (14 in total), focusing on the key algorithmic components of ridesharing. We take a multi-objective approach, evaluating 10 metrics related to global efficiency , complexity , passenger , and platform incentives, in settings designed to closely resemble reality in every aspect, focusing on vehicles of capacity two. To the best of our knowledge, this is the largest and most comprehensive evaluation to date. We (i) identify CARs that perform well on global, passenger, or platform metrics, (ii) demonstrate that lightweight relocation schemes can significantly improve the Quality of Service by up to (50%) , and (iii) highlight a practical , scalable , on-device CAR that works well across all metrics.</p>", "Keywords": "Ridesharing; Mobility-on-demand; Relocation; Transportation; Online matching; k-server; Coordination and cooperation; On-device", "DOI": "10.1007/s10462-022-10145-0", "PubYear": 2022, "Volume": "55", "Issue": "7", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne (EPFL), Artificial Intelligence Laboratory, Lausanne, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne (EPFL), Artificial Intelligence Laboratory, Lausanne, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Liverpool, Liverpool, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "École Polytechnique Fédérale de Lausanne (EPFL), Artificial Intelligence Laboratory, Lausanne, Switzerland"}], "References": [{"Title": "Optimization of Ride Sharing Systems Using Event-driven Receding Horizon Control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "411", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 96030290, "Title": "Reward optimization of spatial crowdsourcing for coalition‐based maintenance task", "Abstract": "<p>Spatial crowdsourcing (SC) can use the moving workers to achieve location-based tasks. It has been widely used in takeout, data labeling, organizing activities, security and artificial intelligence. Currently, many manufacturers wish to explore SC in maintenance business, as SC can reduce maintenance time, reduce labor costs, and improve customer satisfaction. In maintenance business scenarios, there are two kinds of cooperated participators. One is freedom workers who are sensitive to distance, and the other is employed by manufacturers who are insensitive to distance. Therefore, some methods of SC with one type worker are challenging to apply to maintenance business scenarios directly. To match this scenario, we model the maintenance scenario and prove that this scenario is an NP-hard problem; then, both greedy and Nash equilibrium methods are proposed to complete the tasks for making a high total reward. The greedy algorithm (GA) first assigns the nearest available workers to each task, and the employee will try to join the task to help GM to complete more tasks, on the condition that the task cannot be completed and reaches a particular proportion. The Nash equilibrium algorithm (NA) is used to find a Nash equilibrium for all the workers and employees. The experiments demonstrate the efficiency and effectiveness of the synthetic data set of gMission in small and large data sets. The finished task number of NA is about 5% more than that of GA, and the reward of NA is about 10% more than that of GA.</p>", "Keywords": "cooperated;greedy algorithm;maintenance business;Nash equilibrium algorithm;spatial crowdsourcing", "DOI": "10.1002/int.23047", "PubYear": 2022, "Volume": "37", "Issue": "12", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science & Technology Beijing Institute of Technology Beijing China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science & Technology Beijing Institute of Technology Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science & Technology Beijing Institute of Technology Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Blockchain Guangzhou University Guangzhou China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science & Technology Beijing Institute of Technology Beijing China;Institute of Artificial Intelligence and Blockchain Guangzhou University Guangzhou China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Media Engineering Communication University of Zhejiang Hangzhou China"}], "References": [{"Title": "Adversarial attacks on deep-learning-based SAR image target recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "102632", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Adversarial attacks on deep-learning-based radar range profile target recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "531", "Issue": "", "Page": "159", "JournalTitle": "Information Sciences"}, {"Title": "Worker Collaborative group estimation in spatial crowdsourcing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "385", "JournalTitle": "Neurocomputing"}, {"Title": "Secure video retrieval using image query on an untrusted cloud", "Authors": "<PERSON><PERSON> Yan; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106782", "JournalTitle": "Applied Soft Computing"}, {"Title": "PPCL: Privacy-preserving collaborative learning for mitigating indirect information leakage", "Authors": "Hongyang Yan; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "548", "Issue": "", "Page": "423", "JournalTitle": "Information Sciences"}, {"Title": "MHAT: An efficient model-heterogenous aggregation training scheme for federated learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "493", "JournalTitle": "Information Sciences"}, {"Title": "Hybrid sequence‐based Android malware detection using natural language processing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "10", "Page": "5770", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "An efficient adversarial example generation algorithm based on an accelerated gradient iterative fast gradient", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "82", "Issue": "", "Page": "103612", "JournalTitle": "Computer Standards & Interfaces"}]}, {"ArticleId": 96030308, "Title": "Hierarchical feature aggregation network with semantic attention for counting large‐scale crowd", "Abstract": "<p>The purpose of crowd counting is to estimate the number of people in an image. Due to the unconstrained imaging conditions, the scale variation and background occlusion in the images make it still challenging to achieve counting accurately. To tackle the two key challenges, we design a Hierarchical Feature Aggregation Network (HFANet) for accurate crowd counting in complex scenarios. The proposed method can extract multiple features at different levels and then aggregate them hierarchically to generate a high-quality density map. To highlight the crowd regions effectively from the cluttered background, we propose the Semantic Attention module to preserve the useful feature information through the attention mechanism for the low-level features extracted by VGG-16. Meanwhile, we employ convolutional kernels of different sizes to extract multi-scale features and global average pooling operations to preserve contextual information. Furthermore, we design the Feature Aggregation module to integrate the extracted multiple features through a progressive approach, which aims to take full advantage of the complementary properties between low-level and high-level features for efficient features aggregation. Finally, we evaluate the performance of the proposed HFANet on four challenging datasets. Extensive experimental results demonstrate that our proposed approach has better performance compared with most state-of-the-art approaches.</p>", "Keywords": "attention mechanism;computer vision;crowd counting;feature aggregation;multi-scale feature extraction", "DOI": "10.1002/int.23023", "PubYear": 2022, "Volume": "37", "Issue": "11", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Engineering Shandong Normal University Jinan Shandong China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering Shandong Normal University Jinan Shandong China;Shandong Provincial Key Laboratory for Distributed Computer Software Novel Technology Jinan Shandong China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering Shandong Normal University Jinan Shandong China;Shandong Provincial Key Laboratory for Distributed Computer Software Novel Technology Jinan Shandong China"}], "References": []}, {"ArticleId": 96030383, "Title": "Review on Cryptography and Network Security Zero Knowledge Technique in Blockchain Technology", "Abstract": "<p>A huge amount of data has been generated every day across the world by organizations with the emergence of the World Wide Web, social networks, along with e-commerce applications. To safeguard the network along with data transmission that occurs over a wireless network, cryptography along with network security (NS) is utilized. In data transmission over a wireless untrustworthy network, securing data is a major concern. Providing security to the wireless sensor networks is highly significant; NS provides security not only to the end system but also to all over the network system. Consequently, NS, cryptography, present progress in NS, linear cryptanalysis (LC), together with differential cryptanalysis (DC) are explicated in this work. The outcomes of the encryption time (ET) versus file size for data encryption standard (DES), advanced encryption standard (AES), 3DES, Rivest-Shamir-Adleman (RSA), blowfish, and the decryption time (DT) versus file size for RSA, AES, modified RSA (MRSA), and nth degree truncated polynomial ring units (NTRU) are also evaluated in this paper.</p>", "Keywords": "", "DOI": "10.4018/IJISP.308306", "PubYear": 2022, "Volume": "16", "Issue": "2", "JournalId": 13525, "JournalTitle": "International Journal of Information Security and Privacy", "ISSN": "1930-1650", "EISSN": "1930-1669", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Securing color image transmission using compression-encryption model with dynamic key generator and efficient symmetric key distribution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "486", "JournalTitle": "Digital Communications and Networks"}, {"Title": "A survey on security and authentication in wireless body area networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "101883", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Local thinning of 3D stereo images based on symmetric decryption algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103803", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Secrecy performance of multi-user multi-hop cluster-based network with joint relay and jammer selection under imperfect channel state information", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "102193", "JournalTitle": "Performance Evaluation"}, {"Title": "Physical layer secrecy performance analysis of relay selection in a cooperative wireless network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "", "Page": "101352", "JournalTitle": "Physical Communication"}, {"Title": "Secrecy performance for underlay cognitive multi-relaying MISO-RF/SIMO-FSO networks with outdated CSI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101423", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 96030447, "Title": "Numerical investigation of machining of SiC/Al matrix composites by a coupled SPH and FEM", "Abstract": "<p>The machining process of SiC/Al matrix composites is characterized by strong nonlinearity, and thus, there are great challenges resulting from excessive deformation and stress concentration at the tool-workpiece interface in solving such problems. Smoothed particle hydrodynamics (SPH) as a particle-based algorithm can efficiently tackle mesh distortion due to large deformation using finite element method (FEM) for cutting simulations. However, the computational efficiency by SPH is far below the counterpart by FEM. As a result, to address such issues with individual use of SPH or FEM, the coupled SPH-FEM algorithm is presented to calculate large deformation of aluminum matrix using SPH and small deformation of SiC particles using FEM. This paper aims to develop a SPH-FEM coupling model of machining SiC/Al matrix composites and compare the results with an equivalent FE model. A good agreement between numerical results from the SPH-FEM model and those from the FE model is achieved, which shows that the SPH-FEM coupling method is an alternative to FEM for predicting the cutting force, chip formation, and machined surface morphology. The developed SPH-FEM model is also employed to investigate the influence of the cutting parameters including SiC volume fraction, cutting velocity, and uncut chip thickness on the cutting force. Finally, the orthogonal cutting experiments were conducted to validate the presented SPH-FEM model. Numerical results are in good agreement with experimental results, which confirms that SPH-FEM can accurately predict the resulting cutting force and machined surface morphology.</p>", "Keywords": "Coupled SPH and FEM; Finite element model; Metal matrix composites; Orthogonal cutting; Cutting parameter", "DOI": "10.1007/s00170-022-09985-5", "PubYear": 2022, "Volume": "122", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical and Electrical Engineering College, Harbin University of Engineering, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical and Power Engineering College, Harbin University of Science and Technology, Harbin, China"}, {"AuthorId": 3, "Name": "Xudong Jiang", "Affiliation": "Mechanical and Power Engineering College, Harbin University of Science and Technology, Harbin, China"}], "References": [{"Title": "SPH/FEM modeling for laser-assisted machining of fused silica", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2049", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 96030482, "Title": "Optimization of Innovation and Entrepreneurship Education and Training System in Colleges and Universities Based on OpenStack Cloud Computing", "Abstract": "<p>With economic globalization and rapid development of science and technology, many colleges and universities pay more and more attention to the cultivation of students’ innovative thinking and creativity, and innovation and entrepreneurship education has also become an important part of the education system. Due to the current unevenness of teachers in innovation and entrepreneurship education in colleges and universities, high training cost, and lack of strong atmosphere, this paper optimizes the innovation and entrepreneurship education and training system in colleges and universities through OpenStack cloud computing. This paper optimizes the cloud computing platform according to the OpenStack virtual machine and the multiobjective ant colony improvement algorithm and then designs the innovation and entrepreneurship education and training system. The multiobjective ant colony improvement algorithm uses the way ants find food to find the best information resource route from the traces left by the information trend in the cloud platform. In order to test the effectiveness of these methods, this paper uses the simulation method to test. The results show that, sometimes, the load utilization rate of the innovation and entrepreneurship education and training system in colleges and universities exceeds 80%, which is in line with the expected settings. Through the OpenStack cloud computing platform, it can provide a good innovation and entrepreneurship training environment for more users at low cost and low risk and promote the development of innovation and entrepreneurship education.</p>", "Keywords": "", "DOI": "10.1155/2022/2868499", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Zhengzhou Preschool Education College, Zhengzhou 450000, Henan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Zhengzhou Preschool Education College, Zhengzhou 450000, Henan, China"}], "References": [{"Title": "Cultural intelligence as education contents: Exploring the pedagogical aspects of effective functioning in higher education", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Developing an XR based Hyper-realistic Counter-Terrorism, Education, Training, and Evaluation System", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "5", "Page": "65", "JournalTitle": "Jo<PERSON><PERSON>l of Information and Security"}, {"Title": "Impact of Self-Directed Learning and Educational Technology Readiness on Synchronous E-Learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "1", "JournalTitle": "Journal of Organizational and End User Computing"}]}, {"ArticleId": 96030492, "Title": "Manufacturing process classification based on heat kernel signature and convolutional neural networks", "Abstract": "<p>The problem of manufacturing process classification is to identify manufacturing processes that are suitable for a given part design. Previous research on automating manufacturing process classification was limited by small datasets and low accuracy. To solve the first problem, a larger dataset composed of 12,463 examples with four manufacturing processes was constructed. To improve classification accuracy, a deep learning-based method for CAD models was proposed. To begin with, the heat kernel signature (HKS) is computed from a triangle mesh representation of the part. To deal with different numbers of vertices within different models, i.e., different sizes for HKS within each model, two alternative methods are proposed. The first one applies binning to sort all vertices into constant bins, which is further fed into a conventional CNN for classification. The other method searches the most representative local HKS by farthest point sampling, and the representative local HKS are then sent into a pointwise CNN for classification. The scope of this paper focuses on the use of only part shapes for manufacturing process classification, while acknowledging that other information such as size scales, tolerances, and materials, play important roles in manufacturing process selection. Results demonstrate excellent process classification performance with only part shape information.</p>", "Keywords": "Manufacturing process classification; Heat kernel signature; Convolutional neural network; Triangle mesh; Cybermanufacturing", "DOI": "10.1007/s10845-022-02009-9", "PubYear": 2023, "Volume": "34", "Issue": "8", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Georgia Institute of Technology, Atlanta, USA"}], "References": [{"Title": "Automated Classification of Manufacturing Process Capability Utilizing Part Shape, Material, and Quality Attributes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Intelligent feature recognition for STEP-NC-compliant manufacturing based on artificial bee colony algorithm and back propagation neural network", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "792", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Part machining feature recognition based on a deep learning method", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "2", "Page": "809", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 96030543, "Title": "Flexible and scalable privacy assessment for very large datasets, with an application to official governmental microdata", "Abstract": "<p>We present a systematic refactoring of the conventional treatment of privacy analyses, basing it on mathematical concepts from the framework of Quantitative Information Flow (QIF ). The approach we suggest brings three principal advantages: it is flexible, allowing for precise quantification and comparison of privacy risks for attacks both known and novel; it can be computationally tractable for very large, longitudinal datasets; and its results are explainable both to politicians and to the general public. We apply our approach to a very large case study: the Educational Censuses of Brazil, curated by the governmental agency inep, which comprise over 90 attributes of approximately 50 million individuals released longitudinally every year since 2007. These datasets have only very recently (2018–2021) attracted legislation to regulate their privacy — while at the same time continuing to maintain the openness that had been sought in Brazilian society. inep’s reaction to that legislation was the genesis of our project with them. In our conclusions here we share the scientific, technical, and communication lessons we learned in the process.</p>", "Keywords": "", "DOI": "10.56553/popets-2022-0114", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 30919, "JournalTitle": "Proceedings on Privacy Enhancing Technologies", "ISSN": "", "EISSN": "2299-0984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UFMG, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Macquarie University, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Macquarie University, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UNSW and Trustworthy Systems, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "UFMG, Brazil"}], "References": []}, {"ArticleId": 96030562, "Title": "A novel reconstruction attack on foreign-trade official statistics, with a Brazilian case study", "Abstract": "<p>In this paper we describe, formalize, implement, and experimentally evaluate a novel transaction re-identification attack against official foreigntrade statistics releases in Brazil. The attack’s goal is to re-identify the importers of foreign-trade transactions (by revealing the identity of the company performing that transaction), which consequently violates those importers’ fiscal secrecy (by revealing sensitive information: the value and volume of traded goods). We provide a mathematical formalization of this fiscal secrecy problem using principles from the framework of quantitative information flow (QIF), then carefully identify the main sources of imprecision in the official data releases used as auxiliary information in the attack, and model transaction re-construction as a linear optimization problem solvable through integer linear programming (ILP). We show that this problem is NP-complete, and provide a methodology to identify tractable instances. We exemplify the feasibility of our attack by performing 2,003 transaction re-identifications that in total amount to more than $137M, and affect 348 Brazilian companies. Further, since similar statistics are produced by other statistical agencies, our attack is of broader concern.</p>", "Keywords": "", "DOI": "10.56553/popets-2022-0124", "PubYear": 2022, "Volume": "2022", "Issue": "4", "JournalId": 30919, "JournalTitle": "Proceedings on Privacy Enhancing Technologies", "ISSN": "", "EISSN": "2299-0984", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade Federal de Minas Gerais, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Macquarie University, Australia"}], "References": []}, {"ArticleId": 96030665, "Title": "Explaining Semantics and Extension Membership in Abstract Argumentation", "Abstract": "This paper explores the computation of explanations in the specific context of abstract argumentation. The explanations that we define are designed to be visual, in the sense that they take the form of subgraphs of the argumentation graph. Moreover, these explanations rely on the modular aspects of abstract argumentation semantics and can consequently be either aggregated or decomposed. We investigate graph properties of these explanations, and their adequacy to desirable explanatory criteria.", "Keywords": "Abstract argumentation ; Semantics ; Explanabilit", "DOI": "10.1016/j.iswa.2022.200118", "PubYear": 2022, "Volume": "16", "Issue": "", "JournalId": 89889, "JournalTitle": "Intelligent Systems with Applications", "ISSN": "2667-3053", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IRIT, CNRS, Toulouse, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IRIT, Université Toulouse 1, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IRIT, Université Toulouse 3, France;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>Sc<PERSON><PERSON>", "Affiliation": "IRIT, Université Toulouse 3, France"}], "References": []}, {"ArticleId": 96030710, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0020-0255(22)00969-0", "PubYear": 2022, "Volume": "609", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [], "References": []}, {"ArticleId": 96030727, "Title": "Wavelet Entropy-Based Method for Migration Imaging of Hidden Microcracks by Using the Optimal Wave Velocity", "Abstract": "<p>Exploring the shape and direction of hidden cracks in a tunnel lining structure is one of the main objectives of ground penetrating radar (GPR) map interpretation. The most important factor that restricts the migration imaging of hidden cracks is the propagation velocity of electromagnetic waves. Determining the optimal electromagnetic wave velocity is the key to truthfully restoring the actual shape of hidden cracks. To study the GPR characteristic response signals of hidden cracks, forward simulation and model experiments of different cracks were performed. Subsequently, a method to determine the optimal electromagnetic wave velocity based on the wavelet entropy theory was proposed, and the frequency wavenumber domain migration (F-K) and Kirchhoff integral migration imaging method were combined. Horizontal, S-type, and inclined hidden fractures were examined by migration imaging. The results show that the radar characteristic response images of different cracks can be simulated forward by using the finite difference time domain method to write the fracture model instruction. Based on the wavelet entropy theory, the error range between the estimated value and true value was controlled within 4%. Taking the optimal electromagnetic wave velocity as the velocity parameter of the conventional migration method can make the migration more effective and suppress the interference of echo signals so that the diffraction wave converges, and the energy is more concentrated; thus, the real fracture morphology can be restored to the greatest extent. The research results can provide technical support for the fine detection of hidden quality defects in tunnel lining structures by GPR mapping.</p>", "Keywords": "Tunnel engineering; hidden cracks; ground penetrating radar (GPR); wavelet entropy; migration imaging", "DOI": "10.1142/S0218001422540210", "PubYear": 2022, "Volume": "36", "Issue": "15", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Changsha University of Science and Technology, Changsha 410114, P. R. China"}, {"AuthorId": 2, "Name": "Tonghua Ling", "Affiliation": "Department of Civil Engineering, Changsha University of Science and Technology, Changsha 410114, P. R. China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Changsha University of Science and Technology, Changsha 410114, P. R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Room 501, Office Building Changsha 410114, P. R. China"}], "References": []}, {"ArticleId": 96030735, "Title": "CenterNet Plus: Multiscale Prediction with FPN for CenterNet", "Abstract": "Although the CenterNet object detection method can be used to achieve high accuracy, it cannot be used to detect objects with overlapping or almost overlapping centers. However, a good trade-off between detection speed and accuracy cannot be achieved when utilizing this method. We improved a series of backbones using CenterNet, output the fusion of multiple feature maps in backbone, and used the feature pyramid network (FPN) mechanism for multiscale object detection. Additionally, we improved the head of the detector and considered adding intersection over union (IoU) branches. Finally, the loss function was improved to improve detection accuracy. Based on the above research, the CenterNet Plus object detection method is proposed in this paper. Through experiments on the COCO dataset, it can be seen that the use of a multiscale FPN mechanism not only solves the problem of center point overlap but also helps improve the accuracy of detection. CenterNet Plus can be used to greatly improve the detection accuracy on the premise of having a higher detection speed.", "Keywords": "Object detection; deep learning; feature fusion; feature pyramid networks", "DOI": "10.1142/S0218001422590248", "PubYear": 2022, "Volume": "36", "Issue": "13", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96030757, "Title": "Secret key generation over a Nakagami-m fading channel with correlated eavesdropping channel", "Abstract": "<p>The analysis of secret key capacity is an important investigation on the design of secret key agreement protocol. In this paper, we characterize the secret key capacity based on received signal envelopes obeying Nakagami- m distribution between two legitimate users, in the presence of a passive eavesdropper, when the eavesdropping channel is correlated with the legitimate channel. The expression of secret key capacity is derived based on mutual information and applies to both integer and non-integer m . Simulation results indicate that the secret key capacity is proportional to m -fading parameter and average signal-to-noise ratio (SNR) and inversely proportional to the number of paths. In addition, some behaviors of secret key capacity over a high mobility fading channel, and microcell and macrocell environments are provided. These results cover the performance of secret key capacity when the received signal envelope follows Rayleigh, Rician, and Gaussian distributions.</p>", "Keywords": "physical layer security; physical layer secret key generation; Nakagami-m fading; correlated eavesdropping channel; secret key capacity", "DOI": "10.1007/s11432-021-3353-5", "PubYear": 2022, "Volume": "65", "Issue": "9", "JournalId": 7406, "JournalTitle": "Science China Information Sciences", "ISSN": "1674-733X", "EISSN": "1869-1919", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Mobile Network Technologies, Beijing University of Posts and Telecommunications, Beijing, China; Beijing University of Posts and Telecommunications Research Institute, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Tao", "Affiliation": "National Engineering Laboratory for Mobile Network Technologies, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Engineering Laboratory for Mobile Network Technologies, Beijing University of Posts and Telecommunications, Beijing, China; Beijing University of Posts and Telecommunications Research Institute, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Laboratory for Mobile Network Technologies, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Engineering Laboratory for Mobile Network Technologies, Beijing University of Posts and Telecommunications, Beijing, China"}], "References": [{"Title": "A data analysis of political polarization using random matrix theory", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "2", "Page": "129303", "JournalTitle": "Science China Information Sciences"}, {"Title": "An area based physical layer authentication framework to detect spoofing attacks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "12", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Ergodic rate analysis for full-duplex NOMA networks with energy harvesting", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "8", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 96030769, "Title": "End-to-End Automated UI Testing Workflow for Web Sites with Intensive User–System Interactions", "Abstract": "The user interface (UI) is the software component with which the end users interact the most in the mobile and web world today. Therefore, when evaluating end-user satisfaction with a software, besides the functional features, the possibilities provided by the UIs are significant. End-user interfaces offer several capabilities today, necessitating a complex structure. Examples include interfaces developed in technologies such as Google Flutter and React. Therefore, we foresee a need for UI testing business workflows allowing comprehensive testing of UIs to improve customer satisfaction. Within the scope of this research, we propose an end-to-end approached automated UI testing business workflow for testing UI components on websites with intense user actions.", "Keywords": "", "DOI": "10.1142/S0218194022500541", "PubYear": 2022, "Volume": "32", "Issue": "10", "JournalId": 12900, "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering", "ISSN": "0218-1940", "EISSN": "1793-6403", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Izzettin Erdem", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Pattern2Vec: Representation of clickstream data sequences for learning user navigational behavior", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "e6546", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "On the Use of Generative Deep Learning Approaches for Generating Hidden Test Scripts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "10", "Page": "1447", "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering"}]}, {"ArticleId": 96030778, "Title": "A CNN-Bi_LSTM parallel network approach for train travel time prediction", "Abstract": "Convolutional neural networks (CNNs) offer a broad technical framework to deal with spatial feature extraction and nonlinearity capture, whereas they cannot process sequence data and cannot capture the dependencies between the sequence information. Therefore, this paper proposes an improved deep learning model CNN-Bi_LSTM that combines the CNN, Bi_LSTM (i.e., bidirectional long short-term memory network), and fully connected neural network (FCNN) to process the complex dataset for the train travel time prediction. As a result, the presented deep learning framework can capture both the long- and short-term features of complex datasets and the characteristics of time series data. Besides, the multi-feature data fusion processing method is realized with the help of a parallel learning mechanism and the fully connected neural network. Based on a real-life case study of China Railway Express (Chengdu–Europe), the superiority of the CNN-Bi_LSTM model on the train travel time prediction is systemically evaluated and demonstrated, compared with the baseline models of Holt-Winters model, random forest (RF), support vector regression (SVR), LSTM, Bi_LSTM, LSTM with attention mechanism (LSTM_Attention), convolution-based LSTM (CLSTM), CNN_LSTM, hybrid deep learning model (CNN_GRU1), temporal convolutional network (TCN), and parallel deep learning model (CNN_GRU2). Moreover, the values of MSE, RMSE, MAPE, and MAE obtained from the CNN-Bi_LSTM model are equal to 4.647, 2.156, 2.643, and 1.769 respectively Consequently, it is concluded that our proposed CNN-Bi_LSTM model has good prediction results, and it is suitable for the train travel time prediction of China Railway Express.", "Keywords": "Railway transportation ; Train travel time prediction ; Deep learning ; China Railway Express ; CNN-bi_LSTM", "DOI": "10.1016/j.knosys.2022.109796", "PubYear": 2022, "Volume": "256", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Luoyang Polytechnic, Luoyang 471000, China;School of Energy Science and Engineering, Henan Polytechnic University, Jiaozuo 454000, Henan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Energy Science and Engineering, Henan Polytechnic University, Jiaozuo 454000, Henan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Transportation and Logistics, Southwest Jiaotong University, Chengdu 611756, China;National United Engineering Laboratory of Integrated and Intelligent Transportation, Southwest Jiaotong University, Chengdu 610031, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Transportation and Logistics, Southwest Jiaotong University, Chengdu 611756, China;National United Engineering Laboratory of Integrated and Intelligent Transportation, Southwest Jiaotong University, Chengdu 610031, China;Corresponding author at: School of Transportation and Logistics, Southwest Jiaotong University, Chengdu 611756, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Transportation & Economics Research Institute, China Academy of Railway Sciences Co. Ltd, Beijing 100081, China"}], "References": [{"Title": "EO-CNN: An Enhanced CNN Model Trained by Equilibrium Optimization for Traffic Transportation Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "176", "Issue": "", "Page": "800", "JournalTitle": "Procedia Computer Science"}, {"Title": "Taxi and Mobility: Modeling Taxi Demand Using ARMA and Linear Regression", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "177", "Issue": "", "Page": "186", "JournalTitle": "Procedia Computer Science"}, {"Title": "Multi-hour and multi-site air quality index forecasting in Beijing using CNN, LSTM, CNN-LSTM, and spatiotemporal clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "169", "Issue": "", "Page": "114513", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Temporal convolutional networks for just-in-time design smells prediction using fine-grained software metrics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "463", "Issue": "", "Page": "454", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 96030780, "Title": "Laser printing based colorimetric paper sensors for glucose and ketone detection: Design, fabrication, and theoretical analysis", "Abstract": "Regular monitoring of glucose and ketone contents in the body is vital for diabetic patients. Although estimation of the ketone content is necessary for proper healthcare monitoring, the research on the detection of ketone bodies is still nascent. Moreover, inaccurate eye-estimations and reliability are the key limitations of the widely used methodologies for estimating glucose in urine. Hence, simple procedures for fabricating reliable sensors are crucial for on-demand healthcare monitoring. Herein, we posit that a commercially available laser printer, without any added modifications, can be used to make paper-based sensors for colorimetric estimation of glucose and ketone in urine. We conducted a comprehensive experimental investigation to optimize the device designs for rapid estimation of the biomarkers. Also, for the first time, we present a detailed dynamic model of the flow-field in a variable cross-section paper device while considering the species transport and reaction kinetics within the porous media. Finally, we performed experiments with real urine samples to validate that our devices could detect results with zero false negatives. We believe that our present investigation and methodology can enable rapid and reliable fabrication of paper-based sensors for several fundamental studies and applications on affordable and non-invasive healthcare monitoring.", "Keywords": "Paper device ; Glucose detection ; Ketone detection ; Richards’ equation ; Laser printing ; Colorimetry", "DOI": "10.1016/j.snb.2022.132599", "PubYear": 2022, "Volume": "371", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology Kharagpur, West Bengal 721302, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology Kharagpur, West Bengal 721302, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology Kharagpur, West Bengal 721302, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Calcutta University, Kolkata, West Bengal 700009, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Indian Institute of Technology Kharagpur, West Bengal 721302, India;Corresponding author"}], "References": [{"Title": "Paper-based microfluidics: Simplified fabrication and assay methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "336", "Issue": "", "Page": "129681", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Recent advances in microfluidic paper-based assay devices for diagnosis of human diseases using saliva, tears and sweat samples", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "342", "Issue": "", "Page": "130078", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 96030789, "Title": "Parameterized complexity of multi-node hubs", "Abstract": "Hubs are high-degree nodes within a network, ubiquitous in complex networks such as telecommunication, biological, social and semantic networks. Here, we do not seek a hub that is a single node, but a hub consisting of k nodes. Formally, given a graph G = ( V , E ) , we a seek a set A ⊆ V of size k that induces a connected subgraph from which at least p edges emanate. Thus, we identify k nodes which can act as a unit (due to the connectivity constraint) that is a hub (due to the cut constraint). This problem, which we call Multi-Node Hub (MNH), is a variant of the classic Max Cut problem. While it is easy to see that MNH is W[1]-hard with respect to the parameter k , our main contribution is a parameterized algorithm that shows that MNH is FPT with respect to the parameter p .", "Keywords": "Hub ; Parameterized complexity ; Bisection decomposition", "DOI": "10.1016/j.jcss.2022.08.001", "PubYear": 2023, "Volume": "131", "Issue": "", "JournalId": 4857, "JournalTitle": "Journal of Computer and System Sciences", "ISSN": "0022-0000", "EISSN": "1090-2724", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mathematical Sciences, HBNI, Chennai, India;IRL 2000 ReLaX, Chennai, India;University of Bergen, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ben-Gurion University of the Negev, Beersheba, Israel;Corresponding author"}], "References": []}, {"ArticleId": 96030828, "Title": "Neurons on amoebae", "Abstract": "We apply methods of machine-learning, such as neural networks, manifold learning and image processing, in order to study 2-dimensional amoebae in algebraic geometry and string theory. With the help of embedding manifold projection, we recover complicated conditions obtained from so-called lopsidedness. For certain cases it could even reach ∼ 99 % accuracy, in particular for the lopsided amoeba of F 0 with positive coefficients which we place primary focus. Using weights and biases, we also find good approximations to determine the genus for an amoeba at lower computational cost. In general, the models could easily predict the genus with over 90% accuracies. With similar techniques, we also investigate the membership problem, and image processing of the amoebae directly.", "Keywords": "Algebraic geometry ; Amoeba ; Machine learning", "DOI": "10.1016/j.jsc.2022.08.021", "PubYear": 2023, "Volume": "116", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, City, University of London, EC1V 0HB, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, City, University of London, EC1V 0HB, UK;London Institute for Mathematical Sciences, Royal Institution of GB, W1S 4BS, UK;Merton College, University of Oxford, OX1 4JD, UK;School of Physics, NanKai University, Tianjin, 300071, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, City, University of London, EC1V 0HB, UK"}], "References": []}, {"ArticleId": 96030966, "Title": "Visually-guided motion planning for autonomous driving from interactive demonstrations", "Abstract": "The successful integration of autonomous robots in real-world environments strongly depends on their ability to reason from context and take socially acceptable actions. Current autonomous navigation systems mainly rely on geometric information and hard-coded rules to induce safe and socially compliant behaviors. Yet, in unstructured urban scenarios these approaches can become costly and suboptimal. In this paper, we introduce a motion planning framework consisting of two components: a data-driven policy that uses visual inputs and human feedback to generate socially compliant driving behaviors (encoded by high-level decision variables), and a local trajectory optimization method that executes these behaviors (ensuring safety). In particular, we employ Interactive Imitation Learning to jointly train the policy with the local planner, a Model Predictive Controller (MPC), which results in safe and human-like driving behaviors. Our approach is validated in realistic simulated urban scenarios. Qualitative results show the similarity of the learned behaviors with human driving. Furthermore, navigation performance is substantially improved in terms of safety, i.e., number of collisions, as compared to prior trajectory optimization frameworks, and in terms of data-efficiency as compared to prior learning-based frameworks, broadening the operational domain of MPC to more realistic autonomous driving scenarios.", "Keywords": "Interactive Imitation Learning ; Model Predictive Control ; Autonomous driving ; Deep learning ; Motion planning ; Human in the loop", "DOI": "10.1016/j.engappai.2022.105277", "PubYear": 2022, "Volume": "116", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cognitive Robotics (CoR) department, Delft University of Technology, Mekelweg 2, 2628 CD, Delft, The Netherlands;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Cognitive Robotics (CoR) department, Delft University of Technology, Mekelweg 2, 2628 CD, Delft, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Cognitive Robotics (CoR) department, Delft University of Technology, Mekelweg 2, 2628 CD, Delft, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Cognitive Robotics (CoR) department, Delft University of Technology, Mekelweg 2, 2628 CD, Delft, The Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Cognitive Robotics (CoR) department, Delft University of Technology, Mekelweg 2, 2628 CD, Delft, The Netherlands"}], "References": [{"Title": "Distributed multi-robot collision avoidance via deep reinforcement learning for navigation in complex scenarios", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "7", "Page": "856", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "HyP-DESPOT: A hybrid parallel algorithm for online planning under uncertainty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2-3", "Page": "558", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Neural algorithmic reasoning", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "7", "Page": "100273", "JournalTitle": "Patterns"}, {"Title": "Expert Intervention Learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "46", "Issue": "1", "Page": "99", "JournalTitle": "Autonomous Robots"}, {"Title": "Reinforcement Learning-based control using Q-learning and gravitational search algorithm with experimental validation on a nonlinear servo system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "583", "Issue": "", "Page": "99", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96031072, "Title": "A Novel SDMFO-MBSVM-Based Segmentation and Classification Framework for Glaucoma Detection Using OCT and Fundus Images", "Abstract": "Glaucoma is an eye disease that causes loss of vision and blindness by damaging a nerve in the back of the eye called optic nerve. The optic nerve collects the visual information from the eyes and transmits to the brain. Glaucoma is mainly caused by an abnormal high pressure in the eyes. Over time, the increased pressure can erode the tissues of optic nerve, leading to vision loss or blindness. If it is diagnosed in advance, then only it can prevent the vision loss. To diagnose the glaucoma, it must accurately differentiate between the optic disc (OD), optic cup (OC), and the retinal nerve fiber layer (RNFL). The segmentation of the OD, OC, and RNFL remains a challenging issue under a minimum contrast image of boundaries. Therefore, in this study, an innovative method of Hybrid Symbiotic Differential Evolution Moth-Flame Optimization (SDMFO)-Multi-Boost Ensemble and Support Vector Machine (MBSVM)-based segmentation and classification framework is proposed for accurately detecting the glaucoma disease. By using Group Search Optimizer (GSO), the affected parts of the OD, OC and RNFL are segmented. The proposed SDMFO-MBSVM method is executed in MATLAB site, its performance is analyzed with three existing methods. From the comparison, the accuracy of the proposed method in OD segmentation gives better results of 3.37%, 4.54% and 2.22%, OC segmentation gives better results of 2.22%, 3.37% and 4.54%, and RNFL segmentation gives the better results of 3.37%, 97.21% and 5.74%.", "Keywords": "Glaucoma; optic disc; optic cup; retinal nerve fiber layer; group search optimizer (GSO); segmentation; classification", "DOI": "10.1142/S0218001422500380", "PubYear": 2022, "Volume": "36", "Issue": "14", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Assistant Professor, Department of Computer Science and Engineering, Sri Sairam Institute of Technology, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Associate Professor, Velammal Engineering College, Chennai, India"}], "References": [{"Title": "Multi-atlas segmentation of optic disc in retinal images via convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "16537", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "RETRACTED ARTICLE: Group search optimizer: a nature-inspired meta-heuristic optimization algorithm with its results, variants, and applications", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2949", "JournalTitle": "Neural Computing and Applications"}, {"Title": "HPWO-LS-based deep learning approach with S-ROA-optimized optic cup segmentation for fundus image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "9677", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96031077, "Title": "Method for Detection of Ripe Navel Orange Fruit on Trees in Various Weather", "Abstract": "<p lang=\"zh\"><p><p>The algorithm used is based on the Faster R-CNN network. It uses resnet101_vd as the backbone network to extract navel orange image features. It minimizes the error between the inferred bounding box and the actual labeled bounding box through the RPN network and the ROI Pooling layer and non-maximum suppression method. The AP during model training reached 92.34% on sunny days, 96.84% on cloudy days, and 90.05% on foggy days. When evaluating the model, it reached 92.34% on sunny days, 96.89% on cloudy days, and 89.2% on foggy days. The processing time of this model is 63.84fps on sunny days, 68.6fps on cloudy days, and 56.29fps on foggy days. It meets the requirements of rapid and accurate identification in actual picking. In addition, it compares this model with the Faster RCNN with vgg16 as the backbone network and YOLO-v4 models. It effectively improves detection accuracy and speed. Moreover, it reduces the number of false detection and missed detection of navel orange detection. It im-proves position accuracy. This paper realizes the efficient detection of ripe navel orange fruits on trees in various weather. </p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022083304004", "PubYear": 2022, "Volume": "33", "Issue": "4", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>ang<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96031114, "Title": "Human Computer Interaction System for Teacher-Student Interaction Model Using Machine Learning", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2022.2115645", "PubYear": 2025, "Volume": "41", "Issue": "3", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Pedagogy, Ideological and Political Education, Zhoukou Normal University, Zhoukou, China"}], "References": [{"Title": "Predicting at-risk university students in a virtual learning environment via a machine learning algorithm", "Authors": "Kwok <PERSON>; <PERSON>; Miltiadis D. Lytras", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "105584", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Predicting academic performance of students from VLE big data using deep learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "106189", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Optimal Deep Learning based Convolution Neural Network for digital forensics Face Sketch Synthesis in internet of things (IoT)", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "3249", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Optimal Deep Learning based Convolution Neural Network for digital forensics Face Sketch Synthesis in internet of things (IoT)", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "3249", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Pairs trading on different portfolios based on machine learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "3", "Page": "e12649", "JournalTitle": "Expert Systems"}, {"Title": "An ensemble machine learning approach through effective feature extraction to classify fake news", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "47", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "RETRACTED ARTICLE: Secure prediction and assessment of sports injuries using deep learning based convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "3399", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Research on Intelligent Trash Can Garbage Classification Scheme Based on Improved YOLOv3 Target Detection Algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "Supp03", "Page": "", "JournalTitle": "Journal of Interconnection Networks"}]}, {"ArticleId": 96031242, "Title": "Chinese News Text Classification and Its Application Based on Combined-Convolutional Neural Network", "Abstract": "<p lang=\"zh\"><p><p>A method based on combined-convolutional neural network (Combined-CNN) for Chinese news text classification is proposed. First of all, in order to solve the problem of a lack of special term set for Chi-nese news classification, a vocabulary suitable for Chinese long text classification is made by construct-ing a data index method. The Word2Vec pre-trained model was used to embed the text features word vectors. Second, by optimizing the structure of the classical convolutional neural network (CNN) model, a new idea of Combined-CNN model is proposed, which solves the problem of incomplete feature ex-traction of local text blocks and improves the accuracy rate of Chinese news text classification. Effective model regularization and RAdam optimization algorithm are designed in the model to enhance the model training effect. The experimental results show that the precision of the Combined-CNN model for Chi-nese news text classification reaches 93.69%. Compared with traditional machine learning methods and deep learning algorithms, the accuracy rate is improved by a maximum of 11.82% and 1.9%, respectively, and it is better than the comparison model in Recall and F-Measure. Finally, the Chinese news classifica-tion algorithm of the Combined-CNN is applied to realize a personalized recommendation system.</p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022083304001", "PubYear": 2022, "Volume": "33", "Issue": "4", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96031346, "Title": "Event-triggered model predictive control for grid-connected three-phase inverter", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCIP.2021.125158", "PubYear": 2021, "Volume": "3", "Issue": "4", "JournalId": 25167, "JournalTitle": "International Journal of System Control and Information Processing", "ISSN": "1759-9334", "EISSN": "1759-9342", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96031347, "Title": "Deploying the Minimum Number of Rechargeable UAVs for a Quarantine Barrier", "Abstract": "To control the rapid spread of COVID-19, we consider deploying a set of Unmanned Aerial Vehicles (UAVs) to form a quarantine barrier such that anyone crossing the barrier can be detected. We use a charging pile to recharge UAVs. The problem is scheduling UAVs to cover the barrier, and, for any scheduling strategy, estimating the minimum number of UAVs needed to cover the barrier forever. We propose breaking the barrier into subsegments so that each subsegment can be monitored by a single UAV. We then analyze two scheduling strategies, where the first one is simple to implement and the second one requires fewer UAVs. The first strategy divides UAVs into groups with each group covering a subsegment. For this strategy, we derive a closed-form formula for the minimum number of UAVs. In the case of insufficient UAVs, we give a recursive function to compute the exact coverage time and give a dynamic-programming algorithm to allocate UAVs to subsegments to maximize the overall coverage time. The second strategy schedules all UAVs dynamically. We prove a lower and an upper bound on the minimum number of UAVs. We implement a prototype system to verify the proposed coverage model and perform simulations to investigate the performance.", "Keywords": "Rechargeable UAVs; COVID-19; quarantine; barrier coverage; scheduling; monitoring; energy limitation", "DOI": "10.1145/3561303", "PubYear": 2023, "Volume": "19", "Issue": "2", "JournalId": 12743, "JournalTitle": "ACM Transactions on Sensor Networks", "ISSN": "1550-4859", "EISSN": "1550-4867", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 2, "Name": "Zhouqing Han", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> School of Management, University of Texas at Dallas, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Key Laboratory of Big Data Security & Intelligent Processing, Nanjing University of Posts and Telecommunications, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Electronic and Information Engineering, Nanjing University of Aeronautics and Astronautics, China"}], "References": [{"Title": "Energy-Constrained Multi-UAV Coverage Path Planning for an Aerial Imagery Mission Using Column Generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "1", "Page": "125", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Exact Algorithms for the Minimum Load Spanning Tree Problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "", "Issue": "", "Page": "1431", "JournalTitle": "INFORMS Journal on Computing"}]}, {"ArticleId": 96031360, "Title": "Effects of celebrity, social media influencer, and peer endorsements on consumer responses toward a celebrity-owned brand: the role of source credibility and congruency", "Abstract": "Considering the growing popularity of celebrity-owned brands through social media advertising, this study investigates how different types of product endorsers [i.e., celebrities, social media influencers (SMI) and peer internet users] influence consumers’ judgements toward celebrity-owned brands on Instagram. Results indicated that perceived expertise was important in consumers’ evaluation of product endorsement messages, whereas perceptions of endorsers’ trustworthiness did not differ across the three conditions. In terms of endorsement effectiveness, celebrities and SMIs had more significant impacts on consumer responses than fellow consumers when endorsing a brand owned by the celebrity, in which perceived expertise mediated the relationship between endorser type and advertising effectiveness. In addition, the endorser-consumer congruency is more prominent to SMI endorsements than to peer endorsements, whereas the endorser-product congruency is more crucial to peer endorsements than to celebrity endorsements. Theoretical and practical implications for researchers and marketers are discussed. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "advertising; celebrity endorsements; celebrity-owned brand; endorser-consumer congruency; endorser-product congruency; expertise; Instagram; peer; product endorsements; social media influencer; source credibility; trustworthiness", "DOI": "10.1504/IJIMA.2022.125146", "PubYear": 2022, "Volume": "17", "Issue": "1/2", "JournalId": 16124, "JournalTitle": "International Journal of Internet Marketing and Advertising", "ISSN": "1477-5212", "EISSN": "1741-8100", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marketing, Welch College of Business and Technology, Sacred Heart University, 3135 Easton Turnpike, West Campus, Fairfield, CT  06825, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Strategic Communication, School of Communication, University of Miami, 5100 Brunson Dr, Coral Gables, FL  33146, United States"}], "References": []}, {"ArticleId": 96031361, "Title": "A truthful mechanism for crowdsourcing-based tourist spot detection in smart cities", "Abstract": "With the advent of new technologies and the internet around the globe, many cities in different countries are involving the local residents (or city dwellers) for making decisions in various government policies and projects. In this paper, the problem of detecting tourist spots in a city with the help of city dwellers, in strategic setting, is addressed. The city dwellers vote against the different locations that may act as a potential candidate for the tourist spot. For the purpose of voting, the concept of single peaked preferences is utilised, where each city dweller reports a privately held single peaked value that signifies the location in a city. Given the above discussed scenario, the goal is to determine the location in the city as a tourist spot. For this purpose, we have designed the mechanisms (one of which is truthful). For measuring the efficacy of the proposed mechanisms the simulations are done. Copyright © 2022 Inderscience Enterprises Ltd.", "Keywords": "city dwellers; crowdsourcing; single peaked preferences; smart cities; tourism; truthful; voting", "DOI": "10.1504/IJGUC.2022.125136", "PubYear": 2022, "Volume": "13", "Issue": "4", "JournalId": 19428, "JournalTitle": "International Journal of Grid and Utility Computing", "ISSN": "1741-847X", "EISSN": "1741-8488", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Applications, Techno India University, West Bengal, Kolkata, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Andhra Pradesh, Amaravati, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, West Bengal, Durgapur, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, West Bengal, Durgapur, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, West Bengal, Durgapur, India; Intel Technologies, Karnataka, Bengaluru, India"}], "References": []}, {"ArticleId": 96031385, "Title": "KD‐GAN: An effective membership inference attacks defence framework", "Abstract": "<p>Over the past few years, a variety of membership inference attacks against deep learning models have emerged, raising significant privacy concerns. These attacks can easily infer whether a sample exists in the training set of the target model with little adversary knowledge, and the inference accuracy is often much higher than random guessing, which causes serious privacy leakage. To this end, defenses against membership inference attacks have attracted great interest. However, the current available defense methods such as regularization, differential privacy, and knowledge distillation are unable to balance the trade-off between privacy and utility well. In this paper, we combine knowledge distillation and generative adversarial networks to propose a novel training framework that can effectively defend against membership inference attacks, called KD-GAN. Extensive experiments show that our method implements an attack success rate of nearly 0.5 (random guesses) which can successfully defend against membership inference attacks without causing significant damage to model utility, and consistently outperforming other defense methods in the balance of privacy and utility.</p>", "Keywords": "data privacy;generating adversarial network;knowledge distillation;membership inference attacks", "DOI": "10.1002/int.23021", "PubYear": 2022, "Volume": "37", "Issue": "11", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Blockchain Guangzhou University Guangdong China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Blockchain Guangzhou University Guangdong China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Blockchain Guangzhou University Guangdong China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Artificial Intelligence and Blockchain Guangzhou University Guangdong China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Institute of Artificial Intelligence and Blockchain Guangzhou University Guangdong China"}, {"AuthorId": 6, "Name": "Hongyang Yan", "Affiliation": "Institute of Artificial Intelligence and Blockchain Guangzhou University Guangdong China"}], "References": [{"Title": "Secure video retrieval using image query on an untrusted cloud", "Authors": "<PERSON><PERSON> Yan; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106782", "JournalTitle": "Applied Soft Computing"}, {"Title": "PPCL: Privacy-preserving collaborative learning for mitigating indirect information leakage", "Authors": "Hongyang Yan; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "548", "Issue": "", "Page": "423", "JournalTitle": "Information Sciences"}, {"Title": "Camdar‐adv: Generating adversarial patches on 3D object", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "1441", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Adversarial examples: attacks and defenses in the physical world", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> Yan", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "3325", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "CSRT rumor spreading model based on complex network", "Authors": "Shan Ai; Sheng Hong; <PERSON><PERSON><PERSON> Zheng", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "1903", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Fighting fire with fire: A spatial–frequency ensemble relation network with generative adversarial learning for adversarial image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "2081", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "MHAT: An efficient model-heterogenous aggregation training scheme for federated learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "493", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96031482, "Title": "Reconfigurable fully constrained cable-driven parallel mechanism for avoiding collision between cables with human", "Abstract": "<p>Productivity can be increased by manipulators tracking the desired trajectory with some constraints. Humans as moving obstacles in a shared workspace are one of the most challenging problems for cable-driven parallel mechanisms (CDPMs) that are considered in this research. One of the essential primary issues in CDPM is collision avoidance among cables and humans in the shared workspace. This paper presents a model and simulation of a reconfigurable, fully constrained CDPM enabling detection and avoidance of cable–human collision. In this method, unlike conventional CDPMs where the attachment points are fixed, the attachment points on the rails can be moved (up and down on their rails), and then the geometric configuration is adapted. <PERSON><PERSON>–<PERSON> method is proposed, which focuses on estimating the shortest distance among moving obstacles (human limbs) and all cables. When cable and limbs are close to colliding, the new idea of reconfiguration is presented by moving the cable’s attachment point on the rail to increase the distance between the cables and human limbs while they are both moving. Also, the trajectory of the end effector remains unchanged. Some simulation results of reconfiguration theory as a new approach are shown for the eight-cable-driven parallel manipulator, including the workspace boundary variation. The proposed method could find a collision-free predefined path, according to the simulation results.</p>", "Keywords": "cable-driven parallel mechanism; <PERSON><PERSON><PERSON>; collision avoidance; reconfiguration; workspace", "DOI": "10.1017/S0263574722000996", "PubYear": 2022, "Volume": "40", "Issue": "12", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "LAR.i Lab, Applied Science Department, University of Québec at Chicoutimi, Quebec, G7H 2B1, Canada ITMI (Technological Institute of Industrial Maintenance), Sept-iles College, Sept-Îles, G4R 5B7, Canada; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAR.i Lab, Applied Science Department, University of Québec at Chicoutimi, Quebec, G7H 2B1, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "LAR.i Lab, Applied Science Department, University of Québec at Chicoutimi, Quebec, G7H 2B1, Canada ITMI (Technological Institute of Industrial Maintenance), Sept-iles College, Sept-Îles, G4R 5B7, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LAR.i Lab, Applied Science Department, University of Québec at Chicoutimi, Quebec, G7H 2B1, Canada"}], "References": [{"Title": "Moving obstacle avoidance for cable-driven parallel robots using improved RRT", "Authors": "<PERSON><PERSON><PERSON>; Kyoung-Su Park", "PubYear": 2021, "Volume": "27", "Issue": "6", "Page": "2281", "JournalTitle": "Microsystem Technologies"}, {"Title": "Industry 4.0 and Industry 5.0—Inception, conception and perception", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "530", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Reconfigurable fully constrained cable-driven parallel mechanism for avoiding collision between cables with human", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "12", "Page": "4405", "JournalTitle": "Robotica"}, {"Title": "Reconfigurable fully constrained cable-driven parallel mechanism for avoiding collision between cables with human", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "12", "Page": "4405", "JournalTitle": "Robotica"}]}, {"ArticleId": 96031506, "Title": "Cybersecurity Behavior among Government Employees: The Role of Protection Motivation Theory and Responsibility in Mitigating Cyberattacks", "Abstract": "<p>This study examines the factors influencing government employees’ cybersecurity behavior in Malaysia. The country is considered the most vulnerable in Southeast Asia. Applying the protection motivation theory, this study addresses the gap by investigating how government employees behave toward corresponding cyberrisks and threats. Using partial least-squares structural equation modeling (PLS-SEM), 446 respondents participated and were analyzed. The findings suggest that highly motivated employees with high severity, vulnerability, response efficacy, and self-efficacy exercise cybersecurity. Incorporating the users’ perceptions of vulnerability and severity facilitates behavioral change and increases the understanding of cybersecurity behavior’s role in addressing cybersecurity threats—particularly the impact of the threat response in predicting the cybersecurity behavior of government employees. The implications include providing robust information security protection to the government information systems.</p>", "Keywords": "cybersecurity behavior; protective motivation theory; government employee; threat awareness; protection habit cybersecurity behavior ; protective motivation theory ; government employee ; threat awareness ; protection habit", "DOI": "10.3390/info13090413", "PubYear": 2022, "Volume": "13", "Issue": "9", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Industrial Management, University Malaysia of Pahang (UMP), Gambang 26300, Malaysia; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Industrial Management, University Malaysia of Pahang (UMP), Gambang 26300, Malaysia; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Industrial Management, University Malaysia of Pahang (UMP), Gambang 26300, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Business and Communications, INTI International University, Nilai 71800, Malaysia"}], "References": [{"Title": "Help, I need somebody: Examining the antecedents of social support seeking among cybercrime victims", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "106310", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 96031559, "Title": "Sentiment analysis techniques, challenges, and opportunities: Urdu language-based analytical study", "Abstract": "<p>Sentiment analysis in research involves the processing and analysis of sentiments from textual data. The sentiment analysis for high resource languages such as English and French has been carried out effectively in the past. However, its applications are comparatively few for resource-poor languages due to a lack of textual resources. This systematic literature explores different aspects of Urdu-based sentiment analysis, a classic case of poor resource language. While Urdu is a South Asian language understood by one hundred and sixty-nine million people across the planet. There are various shortcomings in the literature, including limitation of large corpora, language parsers, and lack of pre-trained machine learning models that result in poor performance. This article has analyzed and evaluated studies addressing machine learning-based Urdu sentiment analysis. After searching and filtering, forty articles have been inspected. Research objectives have been proposed that lead to research questions. Our searches were organized in digital repositories after selecting and screening relevant studies. Data was extracted from these studies. Our work on the existing literature reflects that sentiment classification performance can be improved by overcoming the challenges such as word sense disambiguation and massive datasets. Furthermore, Urdu-based language constructs, including language parsers and emoticons, context-level sentiment analysis techniques, pre-processing methods, and lexical resources, can also be improved.</p>", "Keywords": "Digital repositories;Opinion mining;Poor resource language;Sentiment analysis;Urdu-based language constructs;Word sensedisambiguation", "DOI": "10.7717/peerj-cs.1032", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Engineering and Technology Lahore, Lahore, Punjab,  Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Engineering and Technology Lahore, Lahore, Punjab,  Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Engineering and Technology Lahore, Lahore, Punjab,  Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Engineering and Technology Lahore, Lahore, Punjab,  Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Dept. of Mechanical Engineering, Faculty of Engineering Technology, Future University in Egypt, New Cairo, Eygpt"}], "References": [{"Title": "Effective lexicon-based approach for Urdu sentiment analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "2521", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A novel method for sentiment classification of drug reviews using fusion of deep and machine learning techniques", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "198", "Issue": "", "Page": "105949", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A survey on sentiment analysis in Urdu: A resource-poor language", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "1", "Page": "53", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "Roman Urdu News Headline Classification Empowered With Machine Learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "2", "Page": "1221", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "ABCDM: An Attention-based Bidirectional CNN-RNN Deep Model for sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "279", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Sentiment analysis for Urdu online reviews using deep learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "8", "Page": "e12751", "JournalTitle": "Expert Systems"}, {"Title": "Semantic Analysis of Urdu English Tweets Empowered by Machine Learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "175", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "A Review of Urdu Sentiment Analysis with Multilingual Perspective: A Case of Urdu and Roman Urdu Language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "3", "JournalTitle": "Computers"}]}, {"ArticleId": 96031682, "Title": "Determining ultimate bearing capacity of shallow foundations by using multi expression programming (MEP)", "Abstract": "This study presents an artificial intelligence approach, namely multi expression programming (MEP), for determining ultimate bearing capacity of shallow foundations on cohesionless soils. Five governing parameters (i.e., internal friction angle, soil unit weight, the length to width ratio of foundation, foundation depth and foundation width) were used as input variables to develop the MEP model. Through the determination of the optimal parameter setting of MEP, a group of expressions were proposed. Then, the MEP model was compared with linear multiple regression, non-linear multiple regression and several previous models, and three statistical indices (i.e., coefficient of determination ( R 2 ), root mean squared error ( RMSE ) and mean absolute error ( MAE )) were employed to evaluate the prediction accuracy of these models. The results show that the proposed model has higher prediction precision than the other models, with higher R 2 value and lower RMSE and MAE values. Additionally, a monotonicity analysis was performed to verify the correct relationship between ultimate bearing capacity and various factors. From the monotonicity analysis, the ultimate bearing capacity increases with the increase of internal friction angle ( φ ), soil unit weight ( γ ), foundation width ( B ) and foundation depth ( D ), whereas it decreases with the increase of the length to width ratio of foundation ( L/B ). Then, a sensitivity analysis was performed. Through the sensitivity analysis, the effect rank of the five input parameters on ultimate bearing capacity is φ &gt; B &gt; D &gt; γ &gt; L/B . Finally, a graphical user interface (GUI) of the MEP model is developed for practical application.", "Keywords": "Ultimate bearing capacity ; Shallow foundations ; Multi expression programming ; Cohesionless soil", "DOI": "10.1016/j.engappai.2022.105255", "PubYear": 2022, "Volume": "115", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, College of Water Resource and Hydropower, Sichuan University, Chengdu 610065, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Hydraulics and Mountain River Engineering, College of Water Resource and Hydropower, Sichuan University, Chengdu 610065, PR China;Corresponding author"}], "References": []}, {"ArticleId": 96031714, "Title": "Deep autoregressive models with spectral attention", "Abstract": "Time series forecasting is an important problem across many domains, playing a crucial role in multiple real-world applications. In this paper, we propose a forecasting architecture that combines deep autoregressive models with a Spectral Attention (SA) module, which merges global and local frequency domain information in the model’s embedded space. By characterizing in the spectral domain the embedding of the time series as occurrences of a random process, our method can identify global trends and seasonality patterns. Two spectral attention models, global and local to the time series, integrate this information within the forecast and perform spectral filtering to remove time series’s noise. The proposed architecture has a number of useful properties: it can be effectively incorporated into well-known forecast architectures, requiring a low number of parameters and producing explainable results that improve forecasting accuracy. We test the Spectral Attention Autoregressive Model (SAAM) on several well-known forecast datasets, consistently demonstrating that our model compares favorably to state-of-the-art approaches.", "Keywords": "Attention models ; Deep learning ; Filtering ; Global-local contexts ; Signal processing ; Spectral domain attention ; Time series forecasting", "DOI": "10.1016/j.patcog.2022.109014", "PubYear": 2023, "Volume": "133", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Signal Theory and Communications, Universidad Carlos III de Madrid, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Signal Theory and Communications, Universidad Carlos III de Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Signal Theory and Communications, Universidad Carlos III de Madrid, Spain"}], "References": [{"Title": "GRATIS: GeneRAting TIme Series with diverse and controllable characteristics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "354", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}, {"Title": "Explainable deep learning for efficient and robust pattern recognition: A survey of recent developments", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108102", "JournalTitle": "Pattern Recognition"}, {"Title": "Financial time series forecasting with multi-modality graph neural network", "Authors": "Dawei Cheng; Fangzhou Yang; Sheng <PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108218", "JournalTitle": "Pattern Recognition"}, {"Title": "Hierarchical electricity time series prediction with cluster analysis and sparse penalty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108555", "JournalTitle": "Pattern Recognition"}, {"Title": "A novel hybrid model for short-term prediction of wind speed", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "108623", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 96031726, "Title": "A full data augmentation pipeline for small object detection based on generative adversarial networks", "Abstract": "Object detection accuracy on small objects, i.e., objects under 32  ×  32 pixels, lags behind that of large ones. To address this issue, innovative architectures have been designed and new datasets have been released. Still, the number of small objects in many datasets does not suffice for training. The advent of the generative adversarial networks (GANs) opens up a new data augmentation possibility for training architectures without the costly task of annotating huge datasets for small objects. In this paper, we propose a full pipeline for data augmentation for small object detection which combines a GAN-based object generator with techniques of object segmentation, image inpainting, and image blending to achieve high-quality synthetic data. The main component of our pipeline is DS-GAN, a novel GAN-based architecture that generates realistic small objects from larger ones. Experimental results show that our overall data augmentation method improves the performance of state-of-the-art models up to 11.9% AP s @ . 5 on UAVDT and by 4.7% AP s @ . 5 on iSAID, both for the small objects subset and for a scenario where the number of training instances is limited.", "Keywords": "Small object detection ; Data augmentation ; Generative adversarial network", "DOI": "10.1016/j.patcog.2022.108998", "PubYear": 2023, "Volume": "133", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, Santiago de Compostela, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, Santiago de Compostela, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Media Integration and Communication Center (MICC), University of Florence, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, Santiago de Compostela, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Universidade de Santiago de Compostela, Santiago de Compostela, Spain;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Media Integration and Communication Center (MICC), University of Florence, Italy"}], "References": [{"Title": "The Unmanned Aerial Vehicle Benchmark: Object Detection, Tracking and Baseline", "Authors": "<PERSON><PERSON> Yu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "5", "Page": "1141", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "MDFN: Multi-scale deep feature learning network for object detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "100", "Issue": "", "Page": "107149", "JournalTitle": "Pattern Recognition"}, {"Title": "STDnet: Exploiting high resolution feature maps for small object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "91", "Issue": "", "Page": "103615", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A Shape Transformation-based Dataset Augmentation Framework for Pedestrian Detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "4", "Page": "1121", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "STDnet-ST: Spatio-temporal ConvNet for small object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107929", "JournalTitle": "Pattern Recognition"}, {"Title": "Small object detection via dual inspection mechanism for UAV visual images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "4", "Page": "4244", "JournalTitle": "Applied Intelligence"}]}]