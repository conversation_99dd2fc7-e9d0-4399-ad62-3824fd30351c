[{"ArticleId": 92650842, "Title": "The Ethernet-APL Engineering Process", "Abstract": "<p>The vision “Industrial Ethernet down to the sensors and actors” became reality. At the Achema fair in June 2021 Ethernet-APL got introduced to the market. Base of this technology is a 2-wire Ethernet that conveys information as well as energy to the sensors and actors of the automation system. Ethernet-APL is based on 2 wire Ethernet standard IEEE 802.3cg [1] running at 10 Mbit/s. An additional specification, the Ethernet-APL Port Profile Specification [2], defines additional parameters for the use of the technology in the process industry, especially in areas with potentially explosive atmosphere. As a next step, potential users need to become familiar with the engineering process of Ethernet-APL networks. For this purpose, the APL project provides the Ethernet-APL Engineering Guideline [3] that covers the main areas of planning, installation and acceptance test. This article will give an overview on the Ethernet-APL engineering process and show the relevant planning steps.</p>", "Keywords": "Ethernet-APL;zweidraht-Ethernet;2-draht-Ethernet;Engineering", "DOI": "10.17560/atp.v63i09.2565", "PubYear": 2021, "Volume": "63", "Issue": "9", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92650843, "Title": "A deadline-based elastic approach for balanced task scheduling in computing cloud environment", "Abstract": "The cloud servicing environment allows sharing of parallelised virtual resource among tasks based on quality of service requirements. Proficient resource management and scheduling of tasks to execute within deadline is a challenging issue. Several approaches available at present for scheduling and workload balancing among the virtual machines (VM) in cloud, but most of them are not conforming to emerging features like elasticity for dynamic provisioning or deprovisioning of VM's while allocating workload. The proposed deadline based elastic approach for load balancing and scheduling (DL_ELBalTSch) considers the percentage of VM's overloaded or underloaded as a supporting threshold at that movement and takes decision either to raise or cut the VM's. This approach is competent enough to meet established deadline by raising successful execution rate of tasks on variable number of resources. The extensive simulations performed on CloudSim obtained higher task execution-ratio, and lower makespan-time and execution-cost when compared with existing approaches. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Computing cloud; Deadline; Deprovisioning; Elastic scaling; Execution ratio; Makespan time; Provisioning; Resources; Scheduling; Utilisation; Virtual machines; Workload balancing", "DOI": "10.1504/IJCC.2021.120396", "PubYear": 2021, "Volume": "10", "Issue": "5/6", "JournalId": 18415, "JournalTitle": "International Journal of Cloud Computing", "ISSN": "2043-9989", "EISSN": "2043-9997", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Chhattisgarh, Raipur, India"}], "References": []}, {"ArticleId": 92650853, "Title": "Use of internet of things for monitoring and evaluating water's quality: a comparative study", "Abstract": "Over the past decade, water resources have faced some challenges, including pollution, drought, etc. Thus, the monitoring of this vital resource becomes significant. On the other hand, the internet of things (IoT) has known a significant evolution nowadays; it is adopted in various fields in order to improve human life. In this paper, we present the results of our comparison study, that aim to review and compare between various proposed system for monitoring water quality using the internet of things technologies. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "Internet of things; IoT; Monitoring; Water; Wireless network", "DOI": "10.1504/IJCC.2021.120399", "PubYear": 2021, "Volume": "10", "Issue": "5/6", "JournalId": 18415, "JournalTitle": "International Journal of Cloud Computing", "ISSN": "2043-9989", "EISSN": "2043-9997", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratory of Spectroscopy, Molecular Modelling, Materials, Nanomaterial, Water and Environment, CERNE2D, Faculty of Science, Mohammed V University in Rabat, Rabat, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "IDMS Team, Department of Computer Science, Faculty of Sciences and Techniques, Moulay Ismail University, Errachidia, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Spectroscopy, Molecular Modelling, Materials, Nanomaterial, Water and Environment, CERNE2D, Faculty of Science, Mohammed V University in Rabat, Rabat, Morocco"}], "References": []}, {"ArticleId": 92650966, "Title": "A Naïve Bayes prediction model on location-based recommendation by integrating multi-dimensional contextual information", "Abstract": "<p>In recent years, researchers have been trying to create recommender systems. There are many different recommender systems. Point of Interest (POI) is a new type of recommender systems that focus on personalized and context-aware recommendations to improve user experience. Recommender systems use different types of recommendation methods to obtain information on POI. In this research paper, we introduced a Naïve Bayes Prediction Model based on Bayesian Theory for POI recommendation. Then, we used the Brightkite dataset to make predictions on POI recommendation and compared it with the other two different recommendation methods. Experimental results confirm that our proposed method outperforms on Location-based POI recommendation.</p>", "Keywords": "Recommendation algorithms; Collaborative filtering; Factorization; Big data analysis; Location-based social networks; Naïve <PERSON> theorem", "DOI": "10.1007/s11042-021-11676-4", "PubYear": 2022, "Volume": "81", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering, Altinbas University, İstanbul, Turkey"}, {"AuthorId": 2, "Name": "Oğuz Bayat", "Affiliation": "Electrical and Computer Engineering, Altinbas University, İstanbul, Turkey"}], "References": [{"Title": "Personalized travel route recommendation from multi-source social media data", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "45-46", "Page": "33365", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Performing the Digital Self", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}]}, {"ArticleId": 92650969, "Title": "Interactive image segmentation based on the appearance model and orientation energy", "Abstract": "<PERSON> et al. (2013) proposed a graph-based image segmentation model by minimizing the distance between the object and background appearance overlap models. This model is very effective for interactive image segmentation. However, it is prone to isolated nodes when the colors or other appearances characteristic of the object and background are very similar. To improve the performance of this algorithm and related algorithms, we add new spatial distance and contour orientation energy terms to the energy function. Accordingly, we modify the construction of the energy graph. We add terminal nodes S and T and add prior constraints. Finally, we use the pseudoflow algorithm proposed by <PERSON><PERSON><PERSON> to calculate the maximum flow of the new energy graph. A large number of experiments on the MSRA dataset, BSD dataset and GrabCut dataset show that the results of the proposed method are better than those of many recently proposed image segmentation methods. The code is available at https://github.com/powerhope/AMOE .", "Keywords": "Graph cut ; Appearance model ; Orientation energy ; Pseudoflow ; Interactive image segmentation ; Binary segmentation", "DOI": "10.1016/j.cviu.2022.103371", "PubYear": 2022, "Volume": "217", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hunan Normal University, 36 Lushan Rd, Yuelu District, Changsha, 410081, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hunan Normal University, 36 Lushan Rd, Yuelu District, Changsha, 410081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hunan Normal University, 36 Lushan Rd, Yuelu District, Changsha, 410081, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan Youmei Technology Development Co., Ltd., Chuanggu Industrial Park, No. 568 Queyuan Rd, Tianxin District, Changsha, China"}], "References": [{"Title": "An improved GrabCut on multiscale features", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107292", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 92650974, "Title": "Overtaking decision and trajectory planning in highway via hierarchical architecture of conditional state machine and chance constrained model predictive control", "Abstract": "An overtaking trajectory planning algorithm is an essential part of autonomous vehicles, but maximizing trip efficiency (minimum travel time) while guaranteeing safety is non-trivial. In particular, to achieve optimal trajectory results in all situations using one algorithm is challenging because overtaking is a complex maneuver in which several behaviors are combined. In this paper, an overtaking algorithm that employs a finite state machine as a high-level decision maker and chance constrained model predictive control as a trajectory planner is proposed to optimize trip efficiency and ride comfort while guaranteeing safety. By combining two methods in a hierarchical structure, the proposed algorithm takes advantage of each method to realize optimality and real-time performance. Using the conditional state machine (CSM), algorithm classifies maneuver states that can ensure safety, and sets the optimal multi-vehicle constraints in each state. The chance constrained model predictive control (MPC) plans an optimal trajectory that considers the prediction uncertainty, safety, trip efficiency and ride comfort. To rigorously evaluate both trip efficiency and safety, the performance of the proposed overtaking algorithm is evaluated in a statistical manner for various level of service (LOS) scenarios. Simulation results show that the optimal trajectory was generated in a multi-vehicle situation while ensuring higher safety than the rule-based algorithm.", "Keywords": "Autonomous vehicle ; Collision avoidance ; Model predictive control ; Overtaking ; Path planning", "DOI": "10.1016/j.robot.2021.104014", "PubYear": 2022, "Volume": "151", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Autonomous Vehicle, MORAI Inc., Gyeonggi, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Autonomous Systems Laboratory, the Department of Future Mobility, Gachon University, Gyeonggi, Republic of Korea;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Vehicular Systems Design and Control Lab at the Graduate School for Green Transportation, Korea Advanced Institute of Science and Technology (KAIST), Daejeon, Republic of Korea;Corresponding authors"}], "References": []}, {"ArticleId": 92651011, "Title": "Stable matching with uncertain pairwise preferences", "Abstract": "We study a two-sided matching problem under preferences, where the agents have independent pairwise comparisons on their possible partners and these preferences may be uncertain. Preferences may be intransitive and agents may even have cycles in their preferences; e.g. an agent a may prefer b to c , c to d , and d to b , all with probability one. If an instance has such a cycle, then there may not exist any matching that is stable with positive probability. We focus on the computational problems of checking the existence of possibly and certainly stable matchings, i.e., matchings whose probability of being stable is positive or one, respectively. We show that finding possibly stable matchings is NP-hard, even if only one side can have cyclic preferences. On the other hand we show that the problem of finding a certainly stable matching is polynomial-time solvable if only one side can have cyclic preferences and the other side has transitive preferences, but that this problem becomes NP-hard when both sides can have cyclic preferences.", "Keywords": "Two-sided matching ; Stability ; Uncertain preferences", "DOI": "10.1016/j.tcs.2022.01.028", "PubYear": 2022, "Volume": "909", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "UNSW Sydney, Sydney, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hungarian Academy of Sciences, and Corvinus University of Budapest, Hungary"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Theory, Budapest University of Technology and Economics, Budapest, Hungary"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "UNSW Sydney, Sydney, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Amsterdam, the Netherlands"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Tulane University, New Orleans, USA"}, {"AuthorId": 7, "Name": "Baharak <PERSON>", "Affiliation": "University of Southampton, Southampton, UK"}], "References": [{"Title": "Stable Matching with Uncertain Linear Preferences", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "5", "Page": "1410", "JournalTitle": "Algorithmica"}, {"Title": "Pairwise Preferences in the Stable Marriage Problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Economics and Computation"}]}, {"ArticleId": 92651056, "Title": "A Resource Sharing Game for the Freshness of Status Updates", "Abstract": "<p>Timely information is a crucial factor in a wide range of information, communication, and control systems. For instance, in autonomous driving systems, the state of the traffic and the location of the vehicles must be as recent as possible. The Age of Information is a relatively new metric that measures the freshness of the knowledge we have about the status of a remote system. More specifically, the Age of Information is the time elapsed since the generation of the last successfully received packet by the monitor containing information about the source. Since the seminal paper [2], in several models it has been observed that the policies that optimize performance metrics of interest in queueing theory do not necessarily minimize the Age of Information. Hence, there is a large number of queueing models that are open research problems regarding the Age of Information metric. We refer to [6] for a recent survey of the Age of Information.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512805", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "BCAM- Basque Center for Applied Mathematics, Bilbao, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "UPV/EHU, University of the Basque Country, Leioa , Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UPV/EHU, University of the Basque Country, Donostia, Spain"}], "References": []}, {"ArticleId": 92651064, "Title": "Network Traffic Classification Using Deep Learning Networks and Bayesian Data Fusion", "Abstract": "<p>The rapid growth of current computer networks and their applications has made network traffic classification more important. The latest approach in this field is the use of deep learning. But the problem of deep learning is that it needs a lot of data for training. On the other hand, the lack of a sufficient amount of data for different types of network traffic has a negative effect on the accuracy of the traffic classification. In this regard, one of the appropriate solutions to address this challenge is the use of data fusion methods in decision level. Data fusion techniques make possible to achieve better results by combining classifiers. In this paper, a network traffic classification approach based on deep learning and data fusion techniques is presented. The proposed method can identify encrypted traffic and distinguish between VPN and non-VPN network traffic. In the proposed approach, first, a preprocessing on the dataset is carried out, then three deep learning networks, namely, Deep Belief Network, Convolution Neural Network, and Multi-layer Perceptron to classify network traffic are employed. Finally, the results of all three classifiers using Bayesian decision fusion are combined. The experimental results on the ISCX VPN-nonVPN dataset show that the proposed method improves the classification accuracy and performs well on different network traffic types. The average accuracy of the proposed method is 97%.</p>", "Keywords": "Encrypted traffic identification; Decision level; Data fusion; Deep learning; Traffic classification", "DOI": "10.1007/s10922-021-09639-z", "PubYear": 2022, "Volume": "30", "Issue": "2", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering and Information Technology, Razi University, Kermanshah, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering and Information Technology, Razi University, Kermanshah, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering and Information Technology, Razi University, Kermanshah, Iran"}], "References": [{"Title": "Deep packet: a novel approach for encrypted traffic classification using deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "3", "Page": "1999", "JournalTitle": "Soft Computing"}, {"Title": "A survey on machine learning for data fusion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "115", "JournalTitle": "Information Fusion"}, {"Title": "Toward effective mobile encrypted traffic classification through deep learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "409", "Issue": "", "Page": "306", "JournalTitle": "Neurocomputing"}, {"Title": "Network traffic classification for data fusion: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "72", "Issue": "", "Page": "22", "JournalTitle": "Information Fusion"}, {"Title": "Deep Learning in IoT Intrusion Detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Towards Model Generalization for Intrusion Detection: Unsupervised Machine Learning Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}]}, {"ArticleId": 92651067, "Title": "Detection and Management of P2P Traffic in Networks using Artificial Neural Networksa", "Abstract": "<p>Peer-to-Peer (P2P) technology is a popular tool for sharing files and multimedia services on networks. While the technology has been serving a good purpose of facilitating sharing of large volumes of data on networks, in other aspects, it has also become a potential source through which attackers could ride on to launch various malicious attacks on the networks. In networks with limited bandwidth resources, uncontrolled P2P activities may also come with problems of congestion in such networks. As P2P continues to evolve on the internet in more complex forms, the need for dynamic mechanisms with the ability to learn the evolving P2P behavior will be essential for accurate monitoring and detection of the P2P traffic to minimize its effects on networks. Supervised machine learning classifiers have been used in recent times, as potential tools for monitoring and detection of the P2P traffic. Incidentally, the capabilities of such classifiers decline over time due to the changing dynamics of the P2P features, making it necessary for the classifiers to undergo continuous retraining in order to maintain their capability of providing effective detection of new P2P traffic features in real-time operations. This paper presents a hybrid machine-learning framework that combines the capabilities of self-organizing map (SOM) model with a multilayer perceptron (MLP) network to achieve real-time detection of P2P traffic in networks. The SOM model generates sets of clustered features contained in the traffic flows and organizes the features into P2P and non-P2P, which are used for training the MLP model for subsequent detection and control of the P2P traffic. The proposed P2P detection framework was tested using real traffic data from the University of Ghana campus network. The test results revealed an average detection rate of 99.89% of the observed instances of P2P traffic in the experimental data. The good detection rate from the detection framework suggests its capability to serve as a potential tool for dynamic monitoring, detection, and control of P2P traffic to manage bandwidth resources and isolation of undesirable P2P-driven traffic in networks.</p>", "Keywords": "P2P traffic detection; Self-organizing map; Multilayer perceptron network; P2P traffic management; Machine-learning", "DOI": "10.1007/s10922-021-09637-1", "PubYear": 2022, "Volume": "30", "Issue": "2", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, University of Ghana, Accra, Ghana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Ghana Computing Systems, University of Ghana, Accra, Ghana"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Ghana Computing Systems, University of Ghana, Accra, Ghana"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, University of Ghana, Accra, Ghana"}], "References": [{"Title": "RETRACTED ARTICLE: Traffic identification and traffic analysis based on support vector machine", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "1903", "JournalTitle": "Neural Computing and Applications"}, {"Title": "An efficient reinforcement learning-based Botnet detection approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "102479", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Sustainable consumption behaviours in P2P accommodation platforms: an exploratory study", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "18", "Page": "13863", "JournalTitle": "Soft Computing"}]}, {"ArticleId": ********, "Title": "Effectiveness of anti-cyberbullying educational programs: A socio-ecologically grounded systematic review and meta-analysis", "Abstract": "The effectiveness of anti-cyberbullying interventions reported in the literature has been mixed even when the pedagogical organization of the programs were taken into account. To gain deeper insight into the relationship between pedagogical design principles of anti-cyberbullying interventions and their program effectiveness, this study comprises a systematic review and analysis of the pedagogical characteristics and meta-analyses of anti-cyberbullying educational programs for adolescents published up to March 2020. Nineteen independent studies from over 2700 articles resulting from systematic searches of six database services met the study selection criteria. A detailed integrated socio-ecological framework was constructed to reveal, for each program component in the selected intervention studies, whether intrapersonal learning, interpersonal interactions, or community-oriented events were involved, and the stakeholders targeted. This pedagogical analysis identified five subgroups. Meta-analysis results of the 19 selected programs found small effect sizes overall for cyber-aggression and cyber-victimization. Further explorations showed that the pedagogical design features statistically significantly moderated the effectiveness of the anti-cyberbullying intervention. The subgroup analysis showed that only programs involving interpersonal interactions and stakeholder agency demonstrated superior program effectiveness. Due to the small number of studies that provided data on long-term programs effectiveness, the meta-analysis findings were inconclusive.", "Keywords": "Socio-ecological framework ; Anti-Cyberbullying ; Pedagogical design ; Systematic review ; meta-Analysis", "DOI": "10.1016/j.chb.2022.107200", "PubYear": 2022, "Volume": "130", "Issue": "", "JournalId": 3864, "JournalTitle": "Computers in Human Behavior", "ISSN": "0747-5632", "EISSN": "1873-7692", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Educational Technology, College of Teacher Education, Zhejiang Normal University, 216, Building 17, 688 Yingbin Road, Jinhua, Zhejiang Province, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Teacher Education and Learning Leadership, Faculty of Education, The University of Hong Kong, 111A, Runme Shaw Building, Pokfulam Road, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Human Communication, Development, and Information Sciences, Faculty of Education, The University of Hong Kong, 401B, Runme Shaw Building, Pokfulam Road, Hong Kong"}], "References": [{"Title": "Being a cybervictim and a cyberbully – The duality of cyberbullying: A meta-analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "106444", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Longitudinal associations among neuroticism, depression, and cyberbullying in early adolescents", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "112", "Issue": "", "Page": "106475", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Cyberbullying victimization and suicide ideation: A crumbled belief in a just world", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "106679", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": 92651074, "Title": "Time-series forecasting of seasonal items sales using machine learning – A comparative analysis", "Abstract": "There has been a growing interest in the field of neural networks for prediction in recent years. In this research, a public dataset including the sales history of a retail store is investigated to forecast the sales of furniture. To this aim, several forecasting models are applied. First, some classical time-series forecasting techniques such as Seasonal Autoregressive Integrated Moving Average (SARIMA) and Triple Exponential Smoothing are utilized. Then, more advanced methods such as Prophet, Long Short-Term Memory (LSTM), and Convolutional Neural Network (CNN) are applied. The performances of the models are compared using different accuracy measurement methods (e.g., Root Mean Squared Error (RMSE) and Mean Absolute Percentage Error (MAPE)). The results show the superiority of the Stacked LSTM method over the other methods. In addition, the results indicate the good performances of the Prophet and CNN models.", "Keywords": "Time-series forecasting ; Sales forecasting ; Seasonal items ; Neural network ; Big data", "DOI": "10.1016/j.jjimei.2022.100058", "PubYear": 2022, "Volume": "2", "Issue": "1", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Ryerson University, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Industrial Engineering, Ryerson University, ON, Canada;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Department of Mechanical, Automotive and Materials Engineering, University of Windsor, ON, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON> School of Management, Ryerson University, ON, Canada"}], "References": [{"Title": "A leading macroeconomic indicators’ based framework to automatically generate tactical sales forecasts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106169", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Prediction of probable backorder scenarios in the supply chain using Distributed Random Forest and Gradient Boosting Machine learning techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Improving time series forecasting using information fusion in local agricultural markets", "Authors": "<PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "355", "JournalTitle": "Neurocomputing"}, {"Title": "Fake news detection: A hybrid CNN-RNN based deep learning approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100007", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Predicting energy cost of public buildings by artificial neural networks, CART, and random forest", "Authors": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "439", "Issue": "", "Page": "223", "JournalTitle": "Neurocomputing"}, {"Title": "Time series analysis and forecasting of coronavirus disease in Indonesia using ARIMA model and PROPHET", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "524", "JournalTitle": "Procedia Computer Science"}, {"Title": "A network traffic forecasting method based on SA optimized ARIMA–BP neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "193", "Issue": "", "Page": "108102", "JournalTitle": "Computer Networks"}, {"Title": "Deep learning based semantic personalized recommendation system", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100028", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "How can we predict the impact of the social media messages on the value of cryptocurrency? Insights from big data analytics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100035", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Introspecting predictability of market fear in Indian context during COVID-19 pandemic: An integrated approach of applied predictive modelling and explainable AI", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100039", "JournalTitle": "International Journal of Information Management Data Insights"}]}, {"ArticleId": 92651123, "Title": "A multilayer system to boost the robustness of fingerprint authentication against presentation attacks by fusion with heart-signal", "Abstract": "Vulnerability to presentation attacks (PAs) remains one of the main security concerns of the widely used fingerprint-based authentication systems, especially for unattended and remote applications. PAs can be carried out by presenting artifact, corpse, or conformant samples to a biometric sensor with the intention of circumventing the system policy. In this study, we develop a multilayer biometric authentication system robust against PAs by the fusion of fingerprint and heart-signal. In the first layer, artifact attacks are prevented by using a fine-tuned convolutional neural network (CNN). In the second layer, a lightweight CNN is used for the prevention of corpse attacks by using heart-signal (also known as ECG signal) with duration of 0.5 s. In the subsequent layers, robust fingerprint matcher at a specific threshold are utilized for the prevention of conformant attacks. In the final layer, a score-level fusion of the fingerprint and heart-signal is used for biometric authentication. The proposed system was evaluated by different authentication and attack scenarios using a multimodal dataset comprising two public databases of fingerprints and heart-signals available online. The experimental results yielded a false match rate (FMR) of approximately zero (0.1%) with an acceptable false non-match rate (FNMR). The obtained results are encouraging for the incorporation of the system into applications requiring high-security authentication.", "Keywords": "Biometric authentication ; Presentation attack detection ; Convolutional neural network ; Fingerprint ; Heart-signal ; PA Presentation Attack ; CNN Convolutional Neural Network ; FMR False Match Rate ; FNMR False Non-Match Rate ; CWT Continuous Wavelet Transform ; MCC Minutia Cylinder-Code ; CV Cross-Validation ; IAPMR Impostor Attack Presentation Match Rate ; HTER Half Total Error Rate", "DOI": "10.1016/j.jksuci.2022.01.004", "PubYear": 2022, "Volume": "34", "Issue": "8", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer and Information Sciences, King Saud University, Riyadh 11543, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer and Information Sciences, King Saud University, Riyadh 11543, Saudi Arabia;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, College of Computer and Information Sciences, King Saud University, Riyadh 11543, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer and Information Sciences, King Saud University, Riyadh 11543, Saudi Arabia"}], "References": [{"Title": "A Survey on Heart Biometrics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "PlexNet: A fast and robust ECG biometric system for human recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "558", "Issue": "", "Page": "208", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92651224, "Title": "Sicherheitslebenszyklen für die modulare Automation in der Prozessindustrie", "Abstract": "<p>Die Flexibilisierung modularer Anlagen stellt die Vorgehensweisen zur Risikoreduktion mit PLT-Sicherheitseinrichtungen vor neue Herausforderungen. Um Flexibilitätseinbußen zu verringern, muss der gesamte Sicherheitslebenszyklus sowie die darin enthaltenen Tätigkeiten für die Anforderungen der modularen Automation angepasst werden. In diesem Beitrag werden die bestehenden Lebenszyklusmodelle aus IEC 61508 und IEC 61511 hinsichtlich ihrer Eignung für die Anwendung in modularen Anlagen untersucht. Dafür werden die Anforderungen aus Sicht der Process Equipment Assemblies und modularen Anlagen aufgelistet, die einzelnen Phasen der Normen dagegen verglichen und entsprechend ihrer Eignung bewertet. Zur Evaluierung wurden die abgeleiteten Sicherheitslebenszyklen im Rahmen mehrerer Fokusgruppen-Workshops mit Fachexperten auf die Erfüllung der normativen Anforderungen sowie auf Verständlichkeit und Durchführbarkeit geprüft. Das Ergebnis ist jeweils ein Sicherheitslebenszyklus für das PEA- sowie das Anlagenengineering, in dem die Anforderungen umgesetzt wurden.</p>", "Keywords": "Functional Safety;Modular Plants;Modular Process Automation;Modular SIS-Safetylifecylce;Safety-MTP;Functional Safety Orchestration", "DOI": "10.17560/atp.v63i09.2552", "PubYear": 2021, "Volume": "63", "Issue": "9", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92651272, "Title": "Erratum regarding missing Declaration of Competing Interest statements inpreviously published article", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.array.2021.100125", "PubYear": 2022, "Volume": "13", "Issue": "", "JournalId": 66362, "JournalTitle": "Array", "ISSN": "2590-0056", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 92651301, "Title": "A survey of discourse parsing", "Abstract": "<p>Discourse parsing is an important research area in natural language processing (NLP), which aims to parse the discourse structure of coherent sentences. In this survey, we introduce several different kinds of discourse parsing tasks, mainly including RST-style discourse parsing, PDTB-style discourse parsing, and discourse parsing for multiparty dialogue. For these tasks, we introduce the classical and recent existing methods, especially neural network approaches. After that, we describe the applications of discourse parsing for other NLP tasks, such as machine reading comprehension and sentiment analysis. Finally, we discuss the future trends of the task.</p>", "Keywords": "discourse parsing; discourse structure; RST; PDTB; STAC", "DOI": "10.1007/s11704-021-0500-z", "PubYear": 2022, "Volume": "16", "Issue": "5", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China;Peng Cheng Laboratory, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China;Peng Cheng Laboratory, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Harbin, China;Peng Cheng Laboratory, Shenzhen, China"}], "References": []}, {"ArticleId": 92651310, "Title": "Bilateral filter-oriented multi-scale CNN fusion model for single image dehazing", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCVR.2022.10044506", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 15056, "JournalTitle": "International Journal of Computational Vision and Robotics", "ISSN": "1752-9131", "EISSN": "1752-914X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Jiangjiang Li", "Affiliation": ""}], "References": []}, {"ArticleId": 92651314, "Title": "Geräteintegration für OPC UA angeschlossene Feldgeräte", "Abstract": "<p>Dieser Artikel beschäftigt sich mit einem in der OPC Foundation standardisierten Informationsmodell PA-DIM™ im Hinblick auf dessen Praxisrelevanz und angrenzende Technologien, die in Kombination in der Instrumentierung angewendet werden.</p>", "Keywords": "OPC UA;Information Model;PA-DIM™;Device Integration", "DOI": "10.17560/atp.v63i11-12.2567", "PubYear": 2021, "Volume": "63", "Issue": "11-12", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ABB AG"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ABB AG"}], "References": []}, {"ArticleId": 92651322, "Title": "Brennstoffzellensysteme: die Regelung als zentraler Wegbereiter", "Abstract": "<p>Die Elektromobilität nimmt immer weiter an Fahrt auf und mit ihr rücken Brennstoffzellen als effiziente Energiewandler für Nutz- und Personenfahrzeuge in den Fokus, da diese einen emissionsfreien Betrieb mit langen Reichweiten und kurzen Tankzeitenversprechen. Damit diese Versprechen auch eingehalten werden, müssen  Brennstoffzellensysteme effizient, robust undvor allem sicher betrieben werden. Dies bringt Herausforderungen an die Betriebs- und Prozessführung mit sich und somit an die Regelung dieser Systeme. Teil 1 einer zweiteiligen Beitragsfolge stellt das Brennstoffzellensystem für die automobileAnwendung aus regelungstechnischer Sicht vor und verdeutlicht die Notwendigkeit und das Potential einer Regelung.</p>", "Keywords": "PEM Brennstoffzelle;Brennstoffzellensystem im Fahrzeug;Regelung von Brennstoffzellensystemen", "DOI": "10.17560/atp.v63i11-12.2580", "PubYear": 2021, "Volume": "63", "Issue": "11-12", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "RWTH Aachen"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "RWTH Aachen"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "RWTH Aachen"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "RWTH Aachen"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "RWTH Aachen"}], "References": []}, {"ArticleId": 92651349, "Title": "Integrating Manifold Knowledge for Global Entity Linking with\n Heterogeneous Graphs", "Abstract": "<p>Entity Linking (EL) aims to automatically link the mentions in unstructured documents to corresponding entities in a knowledge base (KB), which has recently been dominated by global models. Although many global EL methods attempt to model the topical coherence among all linked entities, most of them failed in exploiting the correlations among manifold knowledge helpful for linking, such as the semantics of mentions and their candidates, the neighborhood information of candidate entities in KB and the fine-grained type information of entities. As we will show in the paper, interactions among these types of information are very useful for better characterizing the topic features of entities and more accurately estimating the topical coherence among all the referred entities within the same document. In this paper, we present a novel HEterogeneous Graph-based Entity Linker (HEGEL) for global entity linking, which builds an informative heterogeneous graph for every document to collect various linking clues. Then HEGEL utilizes a novel heterogeneous graph neural network (HGNN) to integrate the different types of manifold information and model the interactions among them. Experiments on the standard benchmark datasets demonstrate that HEGEL can well capture the global coherence and outperforms the prior state-of-the-art EL methods.</p>", "Keywords": "Entity disambiguation; Entity linking; Graph neural network; Heterogeneous graph; Knowledge base", "DOI": "10.1162/dint_a_00116", "PubYear": 2022, "Volume": "4", "Issue": "1", "JournalId": 64768, "JournalTitle": "Data Intelligence", "ISSN": "", "EISSN": "2641-435X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wangxuan Institute of Computer Technology, Peking University, Beijing, China;Center for Data Science, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Wangxuan Institute of Computer Technology, Peking University, Beijing, China;The MOE Key Laboratory of Computational Linguistics, Peking University, Beijing, China"}, {"AuthorId": 3, "Name": "Yansong Feng", "Affiliation": "Wangxuan Institute of Computer Technology, Peking University, Beijing, China;The MOE Key Laboratory of Computational Linguistics, Peking University, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Wangxuan Institute of Computer Technology, Peking University, Beijing, China;The MOE Key Laboratory of Computational Linguistics, Peking University, Beijing, China"}], "References": [{"Title": "Graph neural entity disambiguation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105620", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 92651364, "Title": "Certainty-based Preference Completion", "Abstract": "As from time to time it is impractical to ask agents to provide linear orders over all alternatives, for these partial rankings it is necessary to conduct preference completion. Specifically, the personalized preference of each agent over all the alternatives can be estimated with partial rankings from neighboring agents over subsets of alternatives. However, since the agents' rankings are nondeterministic, where they may provide rankings with noise, it is necessary and important to conduct the certainty-based preference completion. Hence, in this paper firstly, for alternative pairs with the obtained ranking set, a bijection has been built from the ranking space to the preference space, and the certainty and conflict of alternative pairs have been evaluated with a well-built statistical measurement Probability-Certainty Density Function on subjective probability, respectively. Then, a certainty-based voting algorithm based on certainty and conflict has been taken to conduct the certainty-based preference completion. Moreover, the properties of the proposed certainty and conflict have been studied empirically, and the proposed approach on certainty-based preference completion for partial rankings has been experimentally validated compared to state-of-arts approaches with several datasets.", "Keywords": "Certainty; Conflict; Nondeterministic; Preference completion; Subjective probability", "DOI": "10.1162/dint_a_00115", "PubYear": 2022, "Volume": "4", "Issue": "1", "JournalId": 64768, "JournalTitle": "Data Intelligence", "ISSN": "", "EISSN": "2641-435X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), Ministry of Education, Hefei, China and School of Computer Science and Information Engineering, Hefei, University of Technology, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Engineering, Hefei University of Technology, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Information Engineering, Hefei University of Technology, Hefei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, University of Science and Technology of China, Hefei, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Engineering with Big Data (Hefei University of Technology), Ministry of Education, Hefei, China"}], "References": []}, {"ArticleId": 92651389, "Title": "Structural identifiability of series-parallel LCR systems", "Abstract": "We consider the identifiability problem for the parameters of series-parallel LCR circuit networks. We prove that for networks with only two classes of components (inductor-capacitor (LC), inductor-resistor (LR), and capacitor-resistor (RC)), the parameters are identifiable if and only if the number of non-monic coefficients of the constitutive equations equals the number of parameters. The notion of the “type” of the constitutive equations plays a key role in the identifiability of LC, LR, and RC networks. We also investigate the general series-parallel LCR circuits (with all three classes of components), and classify the types of constitutive equations that can arise, showing that there are 22 different types. However, we produce an example that shows that the basic notion of type that works to classify identifiability of two class networks is not sufficient to classify the identifiability of general series-parallel LCR circuits.", "Keywords": "Identifiability ; Structural identifiability ; LCR circuit ; Resultant ; Algebraic geometry ; Algebraic statistics", "DOI": "10.1016/j.jsc.2022.01.002", "PubYear": 2022, "Volume": "112", "Issue": "", "JournalId": 6603, "JournalTitle": "Journal of Symbolic Computation", "ISSN": "0747-7171", "EISSN": "1095-855X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "North Carolina State University, United States of America"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "North Carolina State University, United States of America"}], "References": [{"Title": "Computing all identifiable functions of parameters for ODE models", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "105030", "JournalTitle": "Systems & Control Letters"}, {"Title": "Computing all identifiable functions of parameters for ODE models", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "105030", "JournalTitle": "Systems & Control Letters"}]}, {"ArticleId": 92651403, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1084-8045(22)00007-8", "PubYear": 2022, "Volume": "199", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [], "References": []}, {"ArticleId": 92651435, "Title": "Introduction to reversal fuzzy switch graph", "Abstract": "Fuzzy Switch Graphs ( FSG ) generalize the notion of Fuzzy Graphs by adding high-order arrows and aggregation functions which update the fuzzy values of arrows whenever a zero-order arrow is crossed. In this paper, we propose a more general structure called Reversal Fuzzy Switch Graph ( R F S G ), which promotes other actions in addition to updating the fuzzy values of the arrows, like activation and deactivation of the arrows. R F S G s are able to model dynamical aspects of some systems which generally appear in engineering, computer science and some other fields. The paper also provides the relationship between R F S G s and fuzzy graphs, a logic to verify properties of the modeled system and closes with an application.", "Keywords": "Reversal fuzzy switch graphs ; Reversal fuzzy reactive graphs ; Fuzzy switch graphs ; Fuzzy systems ; Reactive systems", "DOI": "10.1016/j.scico.2022.102776", "PubYear": 2022, "Volume": "216", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidade Federal Rural do Semi-Árido, Centro de Ciências Exatas e Naturais, Mossoró, Brazil;Universidade Federal do Rio Grande do Norte, Departamento de Informática e Matemática Aplicada, Natal, Brazil;Corresponding author at: Universidade Federal do Rio Grande do Norte, Departamento de Informática e Matemática Aplicada, Natal, Brazil"}, {"AuthorId": 2, "Name": "Regivan Santiago", "Affiliation": "Universidade Federal do Rio Grande do Norte, Departamento de Informática e Matemática Aplicada, Natal, Brazil;Principal corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidade de Aveiro, CIDMA e Departamento de Matemática, Aveiro, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Universidade de Aveiro, CIDMA e Departamento de Matemática, Aveiro, Portugal"}], "References": [{"Title": "A Fuzzy Modal Logic for Fuzzy Transition Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "348", "Issue": "", "Page": "85", "JournalTitle": "Electronic Notes in Theoretical Computer Science"}, {"Title": "Introducing fuzzy reactive graphs: a simple application on biology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "9", "Page": "6759", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 92651488, "Title": "Artificial intelligence in early childhood education: A scoping review", "Abstract": "Artificial intelligence (AI) tools are increasingly being used in the field of early childhood education (ECE) to enhance learning and development among young children. Previous proof-of-concept studies have demonstrated that AI can effectively improve teaching and learning in ECE; however, there is a scarcity of knowledge about how these studies are conducted and how AI is used across these studies. We conducted this scoping review to evaluate, synthesize and display the latest literature on AI in ECE. This review analyzed 17 eligible studies conducted in different countries from 1995 to 2021. Although few studies on this critical issue have been found, the existing references provide up-to-date insights into different aspects (knowledge, tools, activities, and impacts) of AI for children. Most studies have shown that AI has significantly improved children&#x27;s concepts regarding AI, machine learning, computer science, and robotics and other skills such as creativity, emotion control, collaborative inquiry, literacy skills, and computational thinking. Future directions are also discussed for researching AI in ECE.", "Keywords": "Artificial intelligence ; Early childhood education ; Teaching and learning ; Machine learning ; Computer science", "DOI": "10.1016/j.caeai.2022.100049", "PubYear": 2022, "Volume": "3", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Su", "Affiliation": "Faculty of Education, The University of Hong Kong, Hong Kong SAR, China;Corresponding author. The University of Hong Kong, Room 219, Runme Shaw Building, Pokfulam Road, Hong Kong SAR, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Early Childhood Education, The Education University of Hong Kong, Hong Kong SAR, China"}], "References": [{"Title": "Learning machine learning with very young children: Who is teaching whom?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "", "Page": "100182", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "Design of online intelligent English teaching platform based on artificial intelligence techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "1166", "JournalTitle": "Computational Intelligence"}, {"Title": "RETRACTED: Wireless sensors application in smart English classroom design based on artificial intelligent system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "103798", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 92651503, "Title": "Clustering sequence graphs", "Abstract": "In application domains ranging from social networks to e-commerce, it is important to cluster users with respect to both their relationships (e.g., friendship or trust) and their actions (e.g., visited locations or rated products). Motivated by these applications, we introduce here the task of clustering the nodes of a sequence graph , i.e., a graph whose nodes are labeled with strings (e.g., sequences of users’ visited locations or rated products). Both string clustering algorithms and graph clustering algorithms are inappropriate to deal with this task, as they do not consider the structure of strings and graph simultaneously. Moreover, attributed graph clustering algorithms generally construct poor solutions because they need to represent a string as a vector of attributes, which inevitably loses information and may harm clustering quality. We thus introduce the problem of clustering a sequence graph. We first propose two pairwise distance measures for sequence graphs, one based on edit distance and shortest path distance and another one based on SimRank. We then formalize the problem under each measure, showing also that it is NP-hard. In addition, we design a polynomial-time 2-approximation algorithm, as well as a heuristic for the problem. Experiments using real datasets and a case study demonstrate the effectiveness and efficiency of our methods.", "Keywords": "Sequence clustering ; Graph clustering ; Sequential data", "DOI": "10.1016/j.datak.2022.101981", "PubYear": 2022, "Volume": "138", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, King’s College London, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, King’s College London, London, UK;Corresponding author"}, {"AuthorId": 3, "Name": "Solon <PERSON><PERSON>", "Affiliation": "CWI, Amsterdam, The Netherlands;Vrije Universiteit, Amsterdam, The Netherlands"}], "References": []}, {"ArticleId": 92651521, "Title": "AdaBoost-based transfer learning method for positive and unlabelled learning problem", "Abstract": "Positive and unlabelled learning (PU learning) is a problem that the training of a classifier only utilizes labelled positive examples and unlabelled examples. Recently, PU learning has been widely studied and used in a number of areas. In this paper, we present an AdaBoost-based transfer learning method to solve PU Learning problem, which is briefly called AdaTLPU. In the proposed model, by sharing SVM parameters and regularization terms, the source task knowledge is transferred to the target task. At the same time, the similarity of the ambiguous examples towards the positive and negative classes is taken into account to refine the decision boundary of the classifier. Meanwhile, we adopt the AdaBoost method to ensemble the obtained weak classifiers to form a strong classifier for prediction. In addition, we put forward an iterative optimization method to obtain the classifier and present the proof of training error bound for the proposed method. Finally, we organize experiments to explore the performance of AdaTLPU and the results indicate that AdaTLPU can achieve the better performance compared with previous PU learning methods.", "Keywords": "Transfer learning ; PU learning ; AdaBoost method", "DOI": "10.1016/j.knosys.2022.108162", "PubYear": 2022, "Volume": "241", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, 510006, China;Corresponding author"}, {"AuthorId": 2, "Name": "Chang<PERSON> Liu", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, 510006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, 510006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, 510006, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, 510006, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Chen", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, 510006, China"}], "References": [{"Title": "Multi-label transfer learning for the early diagnosis of breast cancer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "392", "Issue": "", "Page": "168", "JournalTitle": "Neurocomputing"}, {"Title": "A multi-task transfer learning method with dictionary learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "105233", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 92651531, "Title": "A novel autonomous staircase cleaning system with robust 3D-Deep Learning-based perception technique for Area-Coverage", "Abstract": "Cleaning the staircases is the next big leap for every commercial cleaning robot in order to accomplish a full-fledged cleaning of a constructed buildings. Such an effort could be witnessed in the academic literature where a robotic system can autonomously clean the staircase by ascending. However, none of the existing staircase traversing platforms demonstrated the ability to perform both ascending and descending motion while cleaning, which can significantly improvise the overall robot’s performance. In this paper, we propose a novel reconfigurable cleaning robotic platform called sTetro_plotter, which can perform both ascend and descend motion in the staircases. Pointedly, in this work, we presented a perception framework for the developed robot to traverse on the staircase and perform area coverage autonomously. The framework was constructed with a pointNet++ based feature extractor and classification and regression network to generate a bounding box on the targeted feature. Also, we discussed the process of a staircase descending through tracking the generated bounding box. We implemented a sweeping-based lidar device that can generate a 3D point cloud by sensing its environment. We evaluated the performance of the proposed robot and its perception system through conducting experiments in real-world scenarios. The experimental trials successfully demonstrate the ability of the sTetro_plotter robot to perform autonomous area coverage while traversing on the staircase using the developed perception framework.", "Keywords": "Cleaning System ; Building Maintenance ; Staircase Traversing ; Area Coverage ; Perception for Autonomy ; 3D Point Cloud", "DOI": "10.1016/j.eswa.2022.116528", "PubYear": 2022, "Volume": "194", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ROAR Lab, Engineering Product Development Pillar, Singapore University of Technology and Design, Singapore 487372, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ROAR Lab, Engineering Product Development Pillar, Singapore University of Technology and Design, Singapore 487372, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ROAR Lab, Engineering Product Development Pillar, Singapore University of Technology and Design, Singapore 487372, Singapore"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ROAR Lab, Engineering Product Development Pillar, Singapore University of Technology and Design, Singapore 487372, Singapore"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering and Technology, Nguyen Tat Thanh University, 300A - Ng<PERSON>en <PERSON>t Thanh, Ward 13, District 4, Ho Chi Minh City, Vietnam"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Optoelectronics Research Group, Faculty of Electrical and Electronics Engineering, Ton Duc Thang University, Ho Chi Minh City 700000, Viet Nam;Corresponding author"}], "References": [{"Title": "Cleaning Tasks Knowledge Transfer Between Heterogeneous Robots: a Deep Learning Approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "1", "Page": "191", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Development of a single-wheeled inverted pendulum robot capable of climbing stairs", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "10", "Page": "674", "JournalTitle": "Advanced Robotics"}, {"Title": "Robot perception of static and dynamic objects with an autonomous floor scrubber", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "3", "Page": "403", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Autonomous Floor and Staircase Cleaning Framework by Reconfigurable sTetro Robot with Perception Sensors", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}]}, {"ArticleId": 92651533, "Title": "Detection of sludge bulking using adaptive fuzzy neural network and mechanism model", "Abstract": "The frequent occurrence of sludge bulking can influence the effluent qualities and destroy the stable operation of activated sludge process (ASP). In order to accurately detect the sludge bulking, a detection method, based on adaptive fuzzy neural network and mechanism model, is proposed in this paper. First, a novel detection scheme is designed, where hybrid detection model and intelligent identification algorithm, are designed to describe the dynamics of sludge bulking. Second, an error compensation model, by using adaptive fuzzy neural network, is established to make up for the errors caused by the assumptions set in hybrid detection model. Finally, an error-assisted detection strategy is designed to evaluate sludge bulking. To verify the effectiveness of the proposed detection method, operating data from ASP are applied. The results show that this proposed method can efficiently detect sludge bulking.", "Keywords": "Sludge bulking ; Hybrid detection model ; Intelligent identification algorithm ; Error compensation model ; Error-assisted detection", "DOI": "10.1016/j.neucom.2022.01.060", "PubYear": 2022, "Volume": "481", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao 266590, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering and Automation, Shandong University of Science and Technology, Qingdao 266590, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing 100124, China"}], "References": [{"Title": "Soft-sensing of Wastewater Treatment Process via Deep Belief Network with Event-triggered Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "436", "Issue": "", "Page": "103", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 92651637, "Title": "A secure blockchain system for Internet of Vehicles based on 6G-enabled Network in Box", "Abstract": "Network in Box (NIB) is a self-organizing and portable device. The six-generation wireless communication technologies (6G) can empower NIB with better spectrum efficiency by integrating satellite broadcasting. 6G-enabled NIB is promising to promote the communication efficiency of Internet of Vehicles (IoV). IoV has emerged as the concrete practice of intelligent transportation. However, IoV is vulnerable to attacks from quantum computers because they use traditional RSA and elliptic cure cryptographic systems. Therefore, it is critical to improving the security of IoV against quantum computer attacks. This paper proposes the first secure scheme based on post-quantum techniques for 6G-enabled NIB to protect IoV against quantum attacks. On the one hand, a blockchain-based public key infrastructure is proposed to authenticate the IoV devices securely. On the other hand, we design a blockchain-based multi-party key agreement and communication system to support multi-party communication among IoV devices. The extensive theoretical analysis and experimental results indicate that the proposed blockchain system based on 6G-enabled NIB can achieve high security and efficiency for IoV.", "Keywords": "Blockchain ; Internet of Vehicles ; 6G-enabled Network in Box ; Multi-party communication ; Post-quantum secure scheme", "DOI": "10.1016/j.comcom.2022.01.007", "PubYear": 2022, "Volume": "186", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "Haibo Yi", "Affiliation": "School of Artificial Intelligence, Shenzhen Polytechnic, Shenzhen, China"}], "References": []}, {"ArticleId": 92651681, "Title": "Bounding Mean Slowdown in Multiserver Systems", "Abstract": "<p>Recent progress in queueing theory has made it possible to analyze the mean response time of multiserver queueing systems under advanced scheduling policies. However, this progress has so far been limited to the metric of mean response time. In practice, there are a wide variety of other metrics that can be more important. One such metric is mean slowdown, which is the average ratio between a job's response time and its size. While it is known that the \"RS\" policy minimizes mean slowdown in the single-server M/G/1, the problem is open for multiserver systems, including the M/G/k and load-balancing systems.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512812", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}], "References": []}, {"ArticleId": 92651684, "Title": "Measurement and classification of inter-actor dependencies in goal models", "Abstract": "<p>Goal-oriented requirements engineering approaches aim to capture desired goals and strategies of relevant stakeholders during early requirements engineering stages, using goal models. Socio-technical systems (STSs) involve a rich interplay of human actors (traditional stakeholders, described as actors in goal models) and technical systems. Actors may depend on each other for goals to be achieved, activities to be performed, and resources to be supplied. These dependencies create new opportunities by extending actors’ capabilities but may make the actor vulnerable if the dependee fails to deliver the dependum (knowingly or unintentionally). This paper proposes a novel quantitative metric, called Actor Interaction Metric (AIM), to measure inter-actor dependencies in Goal-oriented Requirements Language (GRL) models. The metric is used to categorize inter-actor dependencies into positive (beneficial), negative (harmful), and neutral (no impact). Furthermore, the AIM metric is used to identify the most harmful/beneficial dependency for each actor. The proposed approach is implemented in a tool targeting the textual GRL language, part of the User Requirements Notation (URN) standard. We evaluate experimentally our approach using 13 GRL models, with positive results on applicability and scalability.</p>", "Keywords": "Goal-oriented requirements; Metric; Inter-actor dependencies; GRL; URN", "DOI": "10.1007/s10270-021-00961-3", "PubYear": 2022, "Volume": "21", "Issue": "6", "JournalId": 6704, "JournalTitle": "Software & Systems Modeling", "ISSN": "1619-1366", "EISSN": "1619-1374", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Computer Science, KFUPM, Dhahran, Kingdom of Saudi Arabia; Interdisciplinary Research Center for Intelligent Secure Systems, KFUPM, Dhahran, Kingdom of Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information and Computer Science, KFUPM, Dhahran, Kingdom of Saudi Arabia"}], "References": [{"Title": "A Game-theoretic approach to analyze interacting actors in GRL goal models", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "3", "Page": "399", "JournalTitle": "Requirements Engineering"}]}, {"ArticleId": 92651698, "Title": "Intelligente Prozessregelung von Schleifprozessen", "Abstract": "<p>Ein<PERSON> Hauptfehlerquelle bei Schleifprozessen ist die thermische Randzonenschädigung (sog. Schleifbrand) an der Kontaktstelle zwischen Werkstück und Schleifkörper, welche ungewollte Eigenspannungen und Veränderungen der Materialhärte in der Werkstückoberfläche bedingt. Da dieser Effekt bisher nicht im Prozess erfasst werden kann, wird in der Praxis auf hohe Sicherheitsfaktoren bei der Zerspanrate und den Abrichtintervallen des Schleifkörpers zurückgegriffen. Die vorliegende Arbeit stellt einen Lösungsansatz für einen geregelten Schleifprozess mit der thermischen Randzonenbeeinflussung als Hauptregelgröße vor. Die in-prozess-Erfassung der Randzonenschädigung des Werkstückes erfolgt mittels des mikromagnetischen Messverfahrens Barkhausenrauschen. Das Ergebnis ist ein neuartiges Prozessregelungskonzept für das Schleifen.</p>", "Keywords": "Geregelter Schleifenprozess;Schleifbrand;Barkhausenrauschen", "DOI": "10.17560/atp.v63i08.2532", "PubYear": 2021, "Volume": "63", "Issue": "8", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92651837, "Title": "Exoskeletons for workers: A case series study in an enclosures production line", "Abstract": "This case-series study aims to investigate the effects of a passive shoulder support exoskeleton on experienced workers during their regular work shifts in an enclosures production site. Experimental activities included three sessions, two of which were conducted in-field (namely, at two workstations of the painting line, where panels were mounted and dismounted from the line; each session involved three participants), and one session was carried out in a realistic simulated environment (namely, the workstations were recreated in a laboratory; this session involved four participants). The effect of the exoskeleton was evaluated through electromyographic activity and perceived effort. After in-field sessions, device usability and user acceptance were also assessed. Data were reported individually for each participant. Results showed that the use of the exoskeleton reduced the total shoulder muscular activity compared to normal working conditions, in all subjects and experimental sessions. Similarly, the use of the exoskeleton resulted in reductions of the perceived effort in the shoulder, arm, and lower back. Overall, participants indicated high usability and acceptance of the device. This case series invites larger validation studies, also in diverse operational contexts.", "Keywords": "Upper-limb exoskeleton ; Occupational exoskeleton ; Shoulder support ; Muscular activity reduction ; In-the-field validation", "DOI": "10.1016/j.apergo.2022.103679", "PubYear": 2022, "Volume": "101", "Issue": "", "JournalId": 4895, "JournalTitle": "Applied Ergonomics", "ISSN": "0003-6870", "EISSN": "1872-9126", "Authors": [{"AuthorId": 1, "Name": "Ilaria Pacifico", "Affiliation": "The BioRobotics Institute, Scuola Superiore Sant’Anna, Viale Rinaldo <PERSON> 34, 56025, Pontedera, Pisa, Italy;Department of Excellence in Robotics & AI, Scuola Superiore Sant’Anna, Piazza Martiri della Libertà, 33, 56127, Pisa, Italy;Corresponding author. The BioRobotics Institute, Scuola Superiore Sant’Anna, Viale Rinaldo <PERSON>ag<PERSON> 34, 56025, Pontedera, Pisa, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IUVO S.r.l., via Puglie 9, 56025, Pontedera, Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ABB S.p.A. PG Breakers & Enclosures, Hub Italy, Electrification Business Area, Smart Power Division, Via Italia, 58, 23846, Garbagnate Monastero, Lecco, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The BioRobotics Institute, Scuola Superiore Sant’Anna, Viale Rinaldo <PERSON> 34, 56025, Pontedera, Pisa, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Division of Occupational Medicine, IRCCS Azienda Ospedaliero-Universitaria di Bologna, Italy;Occupational Medicine Unit, Department of Medical and Surgical Sciences, Alma Mater Studiorum University of Bologna, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Villa Beretta Rehabilitation Center, Valduce Hospital, Via N. Sauro 17, 23845, Costa Masnaga, Lecco, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "IUVO S.r.l., via Puglie 9, 56025, Pontedera, Pisa, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "The BioRobotics Institute, Scuola Superiore Sant’Anna, Viale Rinaldo <PERSON> 34, 56025, Pontedera, Pisa, Italy;Department of Excellence in Robotics & AI, Scuola Superiore Sant’Anna, Piazza Martiri della Libertà, 33, 56127, Pisa, Italy;IRCCS Fondazione Don Carlo <PERSON>, 50143, Florence, Italy"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "The BioRobotics Institute, Scuola Superiore Sant’Anna, Viale Rinaldo <PERSON> 34, 56025, Pontedera, Pisa, Italy;Department of Excellence in Robotics & AI, Scuola Superiore Sant’Anna, Piazza Martiri della Libertà, 33, 56127, Pisa, Italy;IRCCS Fondazione Don <PERSON>, 50143, Florence, Italy;Corresponding author. The BioRobotics Institute, Scuola Superiore Sant’Anna, Viale Rinaldo Piaggio 34, 56025, Pontedera, Pisa, Italy"}], "References": []}, {"ArticleId": 92651854, "Title": "Mixed-Reality-in-the-Loop Simulation", "Abstract": "<p>Der X-in-the-Loop Entwicklungsprozess nimmt einen großen Stellenwert in der heutigen Entwicklung und Inbetriebnahme von Produktionssystemen im Maschinenbau ein. In Zukunft wird die Bedeutung der Aus- und Weiterbildung technischer Fachkräfte z.B. für den Betrieb der Anlagen weiter zunehmen. Zu diesem Zweck werden bislang im Engineering entstehende virtuelle Anlagenmodelle in Kombination mit modernen Visualisierungstechnologien nur vereinzelt eingesetzt. Dies ist neben dem großen Erstellungsaufwand auch auf die fehlende Konsistenz der Modelle zurückzuführen. Der Beitrag stellt dazu einen Lösungsansatz zur Verknüpfung von Hardware-in-the-Loop Simulationen mit modernen Visualisierungsmethoden zu einer Mixed-Reality-in-the-Loop Simulation vor. Die Funktionsfähigkeit des Konzepts wird an einem ausgewählten Produktionssystem nachgewiesen.</p>", "Keywords": "Mixed Reality;Hardware-in-the-Loop;Aus- und Weiterbildung", "DOI": "10.17560/atp.v63i6-7.2538", "PubYear": 2021, "Volume": "63", "Issue": "6-7", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hochschule Esslingen"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92651908, "Title": "GENDA: A Graph Embedded Network Based Detection Approach on encryption algorithm of binary program", "Abstract": "The cryptographic techniques are commonly used in software protection against malicious re-engineering. How to efficiently detect encryption algorithms used in the software to determine if they meet protection requirements is an interesting and significant task. However, existing encryption algorithm detection methods suffer from a high alarm rate or low efficiency as they fail to extract the complete program structure and semantic features of the encryption algorithms. In this article, we proposed GENDA, a graph embedding network-based detection method on encrypted binary code. We first analyze the characteristics of various encryption algorithms and construct the program graph for each encryption algorithm. Then the program graph is recursively embedded into the graph neural network as a basic unit, and the vector representation of the encryption algorithm graph is obtained. Finally, the type of encryption algorithm is determined by comparing the distance between these vectors. To evaluate GENDA, we collected a number of cryptographic libraries and real application programs from the open-source software. The experimental results show that GENDA can reach over a detection success rate of 92%. We also compared GENDA to existing state-of-the-art detection methods. The comparison results show that GENDA outperforms most of the existing methods.", "Keywords": "Software security ; Binary code analysis ; Cryptographic algorithm analysis ; Deep learning", "DOI": "10.1016/j.jisa.2021.103088", "PubYear": 2022, "Volume": "65", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, 710127, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Chang", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, 710127, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, 710127, China;Corresponding author at: School of Information Science and Technology, Northwest University, China"}, {"AuthorId": 4, "Name": "Xiaoqing Gong", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, 710127, China;Corresponding author at: School of Information Science and Technology, Northwest University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, 710127, China"}], "References": []}, {"ArticleId": 92651920, "Title": "Research on Promoting Visual Communication of Local Folk Culture by Using Digital Technology", "Abstract": "<p>Through the visual analysis of local customs and cultures by using digital technology, the total index of digital artificial intelligence, the environmental support of artificial intelligence (AI), and the competitiveness of AI industry have developed well, while the creativity of AI knowledge has developed steadily. According to the survey of digital technology audience education, it can be seen that the respondents with high school to undergraduate education have a higher audience rate of digital technology, while those with other academic qualifications have a lower audience rate, so digital technology can be popularized through education. Through the investigation of audience jobs, it is known that most of the audience of digital technology are engaged in enterprise staff, but they are less exposed to digital technology in political party organs. It can be seen that the loading time of visual images after clustering processing will be reduced by about 200 compared with that without clustering processing, which greatly improves the efficiency of visual graphics loading. As well as the analysis of the development of visualization, we can see that from the sixteenth century to the present, the visualization efficiency has increased from about 50% to 98%, which greatly improves the efficiency. According to the survey of local folk customs, from 2013 to 2019, local residents’ awareness of local folk customs has been continuously improved, from 30% to about 48%, while the awareness of completely unclear folk customs has dropped from 10% to about 1%. Through the investigation of local folk culture, it can be seen that in the promotion of local folk culture, the governance system and measures are insufficient, and the governance effect cannot be effectively fed back.</p>", "Keywords": "", "DOI": "10.1155/2022/8058390", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>xiong Jia", "Affiliation": "School of Marxism, Hebei University of Engineering, Handan, Hebei 056038, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Marxism, Hebei University of Engineering, Handan, Hebei 056038, China"}], "References": []}, {"ArticleId": 92651967, "Title": "Local certification of graphs on surfaces", "Abstract": "A proof labelling scheme for a graph class C is an assignment of certificates to the vertices of any graph in the class C , such that upon reading its certificate and the certificates of its neighbors, every vertex from a graph G ∈ C accepts the instance, while if G ∉ C , for every possible assignment of certificates, at least one vertex rejects the instance. It was proved recently that for any fixed surface Σ, the class of graphs embeddable in Σ has a proof labelling scheme in which each vertex of an n -vertex graph receives a certificate of at most O ( log ⁡ n ) bits. The proof is quite long and intricate and heavily relies on an earlier result for planar graphs. Here we give a very short proof for any surface. The main idea is to encode a rotation system locally, together with a spanning tree supporting the local computation of the genus via Euler&#x27;s formula.", "Keywords": "Local certification ; Proof labelling schemes ; Planar graphs ; Graphs on surfaces", "DOI": "10.1016/j.tcs.2022.01.023", "PubYear": 2022, "Volume": "909", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Laboratoire G-SCOP (CNRS, Univ. Grenoble Alpes), Grenoble, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratoire G-SCOP (CNRS, Univ. Grenoble Alpes), Grenoble, France"}], "References": []}, {"ArticleId": 92651994, "Title": "Sharp Zero-Queueing Bounds for Multi-Server Jobs", "Abstract": "<p>Multi-server jobs, which are jobs that occupy multiple servers simultaneously during service, are prevalent in today's computing clusters. But little is known about the delay performance of systems with multi-server jobs. In this paper, we consider queueing models for multi-server jobs in a scaling regime where the number of servers in the system becomes large. Prior work has derived upper bounds on the queueing probability in this scaling regime. But without proper lower bounds, the results cannot be used to differentiate between policies. We focus on the mean queueing time of multi-server jobs, and establish both upper and lower bounds under various scheduling policies. Our results show that a Priority policy achieves order optimality for minimizing mean queueing time, and the Priority policy is strictly better than the First-Come-First-Serve policy.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512822", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}], "References": []}, {"ArticleId": 92652001, "Title": "A Review on Applications of CFD Modeling in COVID-19 Pandemic", "Abstract": "<p>COVID-19 pandemic has started a big challenge to the world health and economy during recent years. Many efforts were made to use the computation fluid dynamic (CFD) approach in this pandemic. CFD was used to understanding the airborne dispersion and transmission of this virus in different situations and buildings. The effect of the different conditions of the ventilation was studied by the CFD modeling to discuss preventing the COVID-19 transmission. Social distancing and using the facial masks were also modeled by the CFD approach to study the effect on reducing dispersion of the microdroplets containing the virus. Most of these recent applications of the CFD were reviewed for COVID-19 in this article. Special applications of the CFD modeling such as COVID-19 microfluidic biosensors, and COVID-19 inactivation using UV radiation were also reviewed in this research. The main findings of each research were also summarized in a table to answer critical questions about the effectiveness levels of applying the COVID-19 health protocols. CFD applications for modeling of COVID-19 dispersion in an airplane cabin, an elevator, a small classroom, a supermarket, an operating room of a hospital, a restaurant, a hospital waiting room, and a children's recovery room in a hospital were discussed briefly in different scenarios. CFD modeling for studying the effect of social distancing with different spaces, using and not using facial masks, difference of sneezing and coughing, different inlet/outlet ventilation layouts, combining air-conditioning and sanitizing machine, and using general or local air-conditioning systems were reviewed.</p><p>© The Author(s) under exclusive licence to International Center for Numerical Methods in Engineering (CIMNE) 2022.</p>", "Keywords": "", "DOI": "10.1007/s11831-021-09706-3", "PubYear": 2022, "Volume": "29", "Issue": "6", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical Engineering, Caspian Faculty of Engineering, College of Engineering, University of Tehran, Tehran, Iran."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Chemical Engineering, Caspian Faculty of Engineering, College of Engineering, University of Tehran, Tehran, Iran."}], "References": [{"Title": "High-Fidelity Simulation of Pathogen Propagation, Transmission and Mitigation in the Built Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "6", "Page": "4237", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 92652005, "Title": "Modellierung elektrischer Schnitt¬stellen und Kabel mit AutomationML", "Abstract": "Elektrische Verbindungen sind wesentliche Bestandteile jedes Automati­sierungssystems. Dieser Beitrag stellt eine Modellierungsmethodik und erste Bibliotheken elektrischer Schnittstellen für Automatisierungs­geräte vor, die mit dieser Methodik ent­lang vorhandener Standards für elektrischer Schnitt­stellen entwickelt wurden. Diese Bibliotheken können als digitale Repräsen­ta­tionen der zugehörigen Standards verstanden werden, d.h. sie bilden die zugrundeliegenden Normen als elektronisches Datenmodell ab, eine Lösung für die künftige Einbindung von Normen und Standards in Industrie 4.0 Verwaltungsschalen. Die dabei entwickelte Methodik ist generisch, her­stellerneutral und kann für weitere elektri­sche Schnittstellen fortgesetzt wer­den. Interes­sen­ten sind eingeladen, die Ergeb­nisse an­zu­wenden, zu kom­men­tie­ren und zu ergänzen.", "Keywords": "AutomationML;elektrische Schnittstellen;AutomationML Editor", "DOI": "10.17560/atp.v63i08.2546", "PubYear": 2021, "Volume": "63", "Issue": "8", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92652015, "Title": "Electrical Impedance Spectroscopy", "Abstract": "<p>The electrical impedance measurement of a suspension is a valid method to monitor crystallization processes. As it allows measurement of conductivity and permittivity it enables the characterization of non-conductive suspensions. The results obtained show that the concentration of an organic compound of interest can be determined by evaluating its electrical and thermal properties. As the analytical analysis of independent process parameters is a challenging task, a machine learning approach is investigated to extract essential parameter dependency for automated process control purposes.</p>", "Keywords": "Electrical impedance spectroscopy;crystallization;supervised learning", "DOI": "10.17560/atp.v63i08.2556", "PubYear": 2021, "Volume": "63", "Issue": "8", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Ruhruniversität Bochum"}], "References": []}, {"ArticleId": 92652020, "Title": "<PERSON><PERSON><PERSON><PERSON>", "Abstract": "<p>Der vorliegende Artikel geht auf die Fragen der Interoperabilität zwischen Komponenten einer modular gestalteten Prozessautomatisierungsanlage mit dem Schwerpunkt Kommunikation über OPC UA ein. Die Richtlinie VDI/VDE/NAMUR 2658-5.1 ist technische Grundlage, deren praxisnahe Umsetzung hier erläutert werden soll.</p>", "Keywords": "Kommunikation;MTP;Modulare Automation;OPC UA", "DOI": "10.17560/atp.v63i09.2562", "PubYear": 2021, "Volume": "63", "Issue": "9", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92652023, "Title": "Wide High-resolution Projection System Using High-speed Gaze Point Estimation", "Abstract": "Although high-resolution and wide-area display holds promise for high immersion feelings, conventional projection methods have difficulty to manage both of them. We propose gaze oriented projection system using high-speed gaze point estimation and high-resolution tracking projection. Experimental results showed millisecond-order speed, sufficient accuracy, and wide-area of the gaze point estimation and low latency of the proposed system. We also showed two demonstrations: pseudo gaze-oriented view and user-demo without fixing the eye camera.", "Keywords": "augmented reality;high-speed projection;eye-tracking;image processing", "DOI": "10.9746/sicetr.58.42", "PubYear": 2022, "Volume": "58", "Issue": "1", "JournalId": 24132, "JournalTitle": "Transactions of the Society of Instrument and Control Engineers", "ISSN": "0453-4654", "EISSN": "1883-8189", "Authors": [{"AuthorId": 1, "Name": "Ayumi MATSUMOTO", "Affiliation": "Information Technology Center, The Univ. of Tokyo"}, {"AuthorId": 2, "Name": "Masashi NITTA", "Affiliation": "Graduate School of Information Science and Technology, The Univ. of Tokyo"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> SUEISHI", "Affiliation": "Information Technology Center, The Univ. of Tokyo"}, {"AuthorId": 4, "Name": "Ma<PERSON>oshi ISHIKAWA", "Affiliation": "Information Technology Center, The Univ. of Tokyo"}], "References": []}, {"ArticleId": 92652041, "Title": "A hybrid hierarchical agent-based simulation approach for buildings indoor layout evaluation based on the post-earthquake evacuation", "Abstract": "In the aftermath of severe earthquakes, building occupants evacuation behaviour is a vital indicator of the performance of an indoor building design. However, earthquake evacuation has been systematically neglected in the current building design practice. Arguably, one of the primary reasons for this is that post-earthquake evacuation behaviour is complex and distinct from all other types of evacuation behaviours such as fire. Thus, a comprehensive approach to considering the integration of human evacuation behaviour and a building&#x27;s indoor layout design, mainly focused on non-structural damage, has been consistently neglected in the literature. In this paper, a hierarchical hybrid Agent-Based Model (ABM) framework integrated with a Cellular Automata (CA) and a 2D Building Information Model (BIM) damage visualisation to consider an approximation of non-structural damage has been developed. The proposed ABM incorporates learning mechanisms and human psychological aspects influencing evacuees&#x27; utility during the navigation process. The proposed approach was verified by comparing the results to previous real-life post-earthquake evacuation data and a “model to model” comparison of results from the existing relevant studies. The model prototype was successfully tested to simulate the pedestrian evacuation process from one floor of the new engineering building at The University of Auckland, New Zealand. The proposed simulation approach has been carried out for two different internal layout design alternatives where five population sizes are evacuated through different scenarios. The outputs from this study can be used to improve the design&#x27;s compatibility of the building&#x27;s indoor layout with the occupants&#x27; post-earthquake evacuation behaviour.", "Keywords": "Evacuation simulation ; Indoor layout design evaluation ; Agent-based models ; Reinforcement learning", "DOI": "10.1016/j.aei.2022.101531", "PubYear": 2022, "Volume": "51", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, The University of Auckland, Auckland, New Zealand;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, The University of Auckland, Auckland, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, The University of Auckland, Auckland, New Zealand"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, The University of Auckland, Auckland, New Zealand"}, {"AuthorId": 5, "Name": "<PERSON>-Guerrero", "Affiliation": "Escuela de Ingeniería Informática, Pontificia Universidad Católica de Valparaíso, Chile"}], "References": [{"Title": "A BIM-based simulation framework for fire safety management and investigation of the critical factors affecting human evacuation performance", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "", "Page": "101093", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An immersive virtual reality serious game to enhance earthquake behavioral responses and post-earthquake evacuation preparedness in buildings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "", "Page": "101118", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 92652054, "Title": "Special Issue on The Workshop on MAthematical performance Modeling and Analysis (MAMA 2021)", "Abstract": "<p>The complexity of computer systems, networks and applications, as well as the advancements in computer technology, continue to grow at a rapid pace. Mathematical analysis, modeling and optimization have been playing, and continue to play, an important role in research studies to investigate fundamental issues and tradeoffs at the core of performance problems in the design and implementation of complex computer systems, networks and applications.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512800", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematical Sciences, IBM Research Thomas J. Watson Research Center, Yorktown Heights, NY, USA"}], "References": []}, {"ArticleId": 92652056, "Title": "A Framework for Dynamic Configuration of TLS Connections Based on Standards", "Abstract": "The Transport Layer Security (TLS) protocol is widely used for protecting end-to-end communications between network peers (applications or nodes). However, the administrators usually have to configure parameters (e.g., cryptography algorithms or authentication credentials) to establish TLS connections manually. However, this way of managing security connections becomes infeasible when the number of network peers is high. This paper proposes a TLS management framework that configures and manages TLS connections in a dynamic and autonomous manner. The solution is based on well-known standardized protocols and models that allow providing the necessary configuration parameters to establish a TLS connection between two network nodes. Nowadays, this is required in several application scenarios such as virtual private networks, virtualized network functions, or service function chains. Our framework is based on standard elements of the Software Defined Networking paradigm, widely adopted to provide flexibility to network management, such as for the scenarios aforementioned. The proposed framework has been implemented in a proof of concept to validate the suitability of the proposed solution to manage the dynamic configuration of TLS connections. The experimental results confirm that the implementation of this framework enables an operable and flexible procedure to manage TLS connections between network nodes in different scenarios.", "Keywords": "TLS; Management; SDN; YANG", "DOI": "10.1007/s10922-021-09640-6", "PubYear": 2022, "Volume": "30", "Issue": "2", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communications Engineering, University of Murcia, Murcia, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information and Communications Engineering, University of Murcia, Murcia, Spain"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON>", "Affiliation": "Department of Information and Communications Engineering, University of Murcia, Murcia, Spain"}, {"AuthorId": 4, "Name": "<PERSON>-<PERSON>", "Affiliation": "Department of Engineering and Applied Technologies, University Defense Center - Spanish Air Force Academy, Murcia, Spain"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, University of Murcia, Murcia, Spain"}], "References": [{"Title": "SDN-Based Traffic Management Middleware for Spontaneous WMNs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "4", "Page": "1575", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Scalability, Consistency, Reliability and Security in SDN Controllers: A Survey of Diverse SDN Controllers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "A survey of data center consolidation in cloud computing systems", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "", "Page": "100366", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 92652082, "Title": "Expert vs novice collaborative heuristic evaluation (CHE) of a smartphone app for cultural heritage sites", "Abstract": "<p>This study was conducted to compare CHE between Human-Computer Interaction (HCI) experts and novices in evaluating the Smartphone app for the cultural heritage site. It uses the Smartphone Mobile Application heuRisTics (SMART), focusing on smartphone applications and traditional Nielsen heuristics, focusing on a wider range of interactive systems. Six experts and six novices used the severity rating scale to categorise the severity of the usability issues. These issues were mapped to both heuristics. The study found that experts and novice evaluators identified 19 and 14 usability issues, respectively, with ten as the same usability issues. However, these same usability issues have been rated differently. Although the t-test indicates no significant differences between experts and novices in their ratings for usability issues, these results nevertheless indicate the need for both evaluators in CHE to provide a more comprehensive perspective on the severity of the usability issues. Furthermore, the mapping of the usability issues for Nielsen and SMART heuristics concluded that more issues with the smartphone app could be addressed through smartphone-specific heuristics than general heuristics, indicating a better tool for heuristic evaluation of the smartphone app. This study also provides new insight into the required number of evaluators needed for CHE.</p>", "Keywords": "Collaborative heuristic evaluation; Cultural heritage site; Expert; Heuristics; Novice; Smartphone app", "DOI": "10.1007/s11042-022-11991-4", "PubYear": 2022, "Volume": "81", "Issue": "5", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Cognitive Sciences and Human Development, Universiti Malaysia Sarawak, Kota Samarahan, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Cognitive Sciences and Human Development, Universiti Malaysia Sarawak, Kota Samarahan, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Cognitive Sciences and Human Development, Universiti Malaysia Sarawak, Kota Samarahan, Malaysia"}], "References": [{"Title": "Enhancement and Application of a UAV Control Interface Evaluation Technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}, {"Title": "A hybrid augmented reality guide for underwater cultural heritage sites", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "6", "Page": "815", "JournalTitle": "Personal and Ubiquitous Computing"}]}, {"ArticleId": 92652108, "Title": "Improved self‐attentive Musical Instrument Digital Interface content‐based music recommendation system", "Abstract": "<p>Automatic music recommendation is an open research problem that has seen much work in recent years. A common and successful music recommendation approach is collaborative filtering, which has worked well in this domain. One major drawback of this method is that it suffers from a cold-start problem, and it requires a lot of user-personalized information. It is an ineffective mechanism for recommending new and unpopular songs as well as for new users. In this article, we report a hybrid methodology that uses the song's content information. We use MIDI (Musical Instrument Digital Interface) content data, a compressed version of an audio song that contains digital information about a song and is machine-readable. We describe a model called MSA-SRec (MIDI Based Self Attentive Sequential Music Recommendation), a latent factor-based self-attentive deep learning model that uses a substantial amount of sequential information as content information of the song for recommendation generation. We use MIDI data of a song that is under-explored content information for music recommendation. We show that using MIDI as content data with user and item latent vector produces reasonable recommendations. We also demonstrate that using MIDI over other music metadata performs better with various state-of-the-art models of recommendation systems.</p>", "Keywords": "cold-start recommendation;deep sequential model;matrix factorization;music recommender system;Musical Instrument Digital Interface;self-head attention", "DOI": "10.1111/coin.12501", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 7497, "JournalTitle": "Computational Intelligence", "ISSN": "0824-7935", "EISSN": "1467-8640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Indian Institute of Technology (BHU) Varanasi  Varanasi India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Indian Institute of Technology (BHU) Varanasi  Varanasi India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Indian Institute of Technology (BHU) Varanasi  Varanasi India"}], "References": [{"Title": "Long- and short-term self-attention network for sequential recommendation", "Authors": "<PERSON><PERSON> Xu; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "580", "JournalTitle": "Neurocomputing"}, {"Title": "TLSAN: Time-aware long- and short-term attention network for next-item recommendation", "Authors": "Ji<PERSON><PERSON> Zhang; <PERSON><PERSON><PERSON>; Dongjin Yu", "PubYear": 2021, "Volume": "441", "Issue": "", "Page": "179", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 92652117, "Title": "Evaluation Model of Online and Offline Mixed Teaching Quality in Colleges and Universities Based on BP Neural Network", "Abstract": "<p>Hybrid online and offline teaching is becoming the mainstream teaching method in the postepidemic era. However, research on assessing its teaching quality is still limited. This article thus develops a teaching quality evaluation model based on the BP neural network. A three-dimensional indicator system involving 19 indicators is set in the model. The established model was demonstrated and validated by a case study. The results show that the developed model can accurately assess the teaching quality of hybrid online and offline teaching. Findings from this study can provide valuable references for improving the quality of hybrid online and offline teaching.</p>", "Keywords": "", "DOI": "10.1155/2022/7560227", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Preparatory Education, Xinjiang Normal University, Urumqi, Xinjiang 830000, China"}, {"AuthorId": 2, "Name": "Qingqing Chai", "Affiliation": "School of Preparatory Education, Xinjiang Normal University, Urumqi, Xinjiang 830000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Translation and Interpretation, Lomonosov Moscow State University, Moscow 119991, Russia"}], "References": [{"Title": "A systematic review of research on online teaching and learning from 2009 to 2018", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "104009", "JournalTitle": "Computers & Education"}, {"Title": "Lecture quality assessment based on the audience reactions using machine learning and neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "", "Page": "100022", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Artificial neural networks in drought prediction in the 21st century–A scientometric analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "108080", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 92652195, "Title": "Net-zero Energy Systems", "Abstract": "<p>Das hier vorgestellte „Net-zero Energy“-Konzept beschreibt eine Methode, wie die Energieversorgung industrieller Standorte nachhaltig und ressourcenschonend gestaltet werden kann. Ausgehend von den Anforderungen beschreibt der Artikel zunächst das Verfahren eines vereinheitlichten Screenings. Anschließend kann mit Hilfe von gewichteten Entscheidungsmetriken die standortspezifische Systemauswahl erfolgen. Grundlage hierzu ist eine vergleichende Bewertung von Maßnahmen und Technologien nach technischen, wirtschaftlichen und ökologischen Kenngrößen. Eine exemplarische Fallstudie verdeutlicht die Systematik.</p>", "Keywords": "Net-zero Energy Konzept;Grüne Produktion;Energieversorgung;Ressourceneffizienz", "DOI": "10.17560/atp.v63i11-12.2573", "PubYear": 2021, "Volume": "63", "Issue": "11-12", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Fraunhofer IFF"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Fraunhofer IFF"}, {"AuthorId": 3, "Name": "Przem<PERSON><PERSON>", "Affiliation": "Fraunhofer IFF"}], "References": []}, {"ArticleId": 92652312, "Title": "Bayesian Updating in Property Protection System", "Abstract": "<p>The article focuses on the implementation of the Bayesian updating method in creating a model of the protection system. This implementation makes it possible to incorporate the new information obtained during the lifetime of the protection system. The inclusion of new information ultimately makes it possible to refine the results of the model and thus increase its effectiveness to support decision-making. The approach of using the Bayesian updating method is given in a specific example. The possibility of expert estimation of input parameter values and application of Bayesian updating for the purposes of quantitative revision of security level assessment is also presented.</p>", "Keywords": "", "DOI": "10.46300/9108.2021.15.22", "PubYear": 2021, "Volume": "15", "Issue": "", "JournalId": 75453, "JournalTitle": "International Journal of Computers", "ISSN": "", "EISSN": "1998-4308", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of security engineering, University of Zilina Univerzitna 8215/1, 010 26 Zilina Slovakia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of security engineering, University of Zilina Univerzitna 8215/1, 010 26 Zilina Slovakia"}], "References": []}, {"ArticleId": 92652318, "Title": "Optimal joint design of two-dimensional warranty and preventive maintenance policies for new products considering learning effects", "Abstract": "Providing free preventive maintenance (PM) for products during the warranty period is a prevalent type of combinational warranty design in the market, particularly in the automobile industry. The characteristics of certain new products, such as short life cycles, frequent updates, and complex functions require customers and manufacturers to continuously learn during the use and maintenance process, which leads to learning effects of both parties in the warranty period. In this study, we investigate the “PM–two-dimensional (2D) warranty design” problem with the aim of maximizing the profit of the manufacturer. Profit is affected by the number of PMs as well as the age- and usage-based PM intervals. Moreover, we explore the influence of the learning effects on the 2D warranty design by introducing two scenarios. A heuristic algorithm is applied to solve the optimization models for various policies under different scenarios. A comparison of the results of the studied models reveals that simultaneous consideration of PM planning and 2D warranty design is a win-win strategy for the manufacturer and customers, and learning effects are critical elements in the design process, which can improve the accuracy and rationality of the design. A few management recommendations are provided based on the sensitivity analysis results.", "Keywords": "Two-dimensional warranty ; Learning effects ; Warranty period optimization ; Preventive maintenance planning ; Profit maximization", "DOI": "10.1016/j.cie.2022.107958", "PubYear": 2022, "Volume": "166", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hangzhou Dianzi University, 310018 Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Hangzhou Dianzi University, 310018 Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hangzhou Dianzi University, 310018 Hangzhou, China;Corresponding author"}], "References": [{"Title": "Two-dimensional extended warranty strategy including maintenance level and purchase time: A win-win perspective", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "106294", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Pricing and two-dimensional warranty policy of multi-products with online and offline channels using a value-at-risk approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "106674", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Integrated optimization of quality and maintenance: A literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "106924", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 92652352, "Title": "A Stretchable Fiber with Tuna<PERSON> Stiffness for Programmable Shape Change of Soft Robots", "Abstract": "<p>All soft robots require the same functionality, that is, controlling the shape of a structure made from soft materials. However, existing approaches for shape control of soft robots are primarily dominated by modular pneumatic actuators, which require multichambers and complex flow control components. Nature shows exciting examples of manipulation (shape change) in animals, such as worms, using a single-chambered soft body and programmable stiffness changes in the skin; controlling the spatial distribution of changes in stiffness enables achieving complex shape evolutions. However, such stiffness control requires a drastic membrane stiffness contrast between stiffened and nonstiffened states. Generally, this is extremely challenging to accomplish in stretchable materials. Inspired by longitudinal muscle fibers in the skin of worms, we developed a new concept for fabricating a hybrid fiber with tunable stiffness, that is, a fiber comprising both stiff and soft parts connected in a series. A substantial change in membrane stiffness was then observed by the locking/unlocking of the soft part. Our proposed hybrid fiber cyclically produced a membrane stiffness contrast of more than 100 × in less than 6 s using an input power of 3 W. A network of these hybrid fibers with tunable stiffness could manipulate a single-chambered soft body in multiple directions and transform it into a complex shape by selectively varying the stiffness at different locations.</p>", "Keywords": "soft robotics; stretchable fiber; tunable stiffness; shape change", "DOI": "10.1089/soro.2021.0032", "PubYear": 2022, "Volume": "9", "Issue": "6", "JournalId": 12246, "JournalTitle": "Soft Robotics", "ISSN": "2169-5172", "EISSN": "2169-5180", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanics of Composites for Energy and Mobility Laboratory, Physical Science and Engineering Division, King Abdullah University of Science and Technology (KAUST), Thuwal, Saudi Arabia."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mechanics of Composites for Energy and Mobility Laboratory, Physical Science and Engineering Division, King Abdullah University of Science and Technology (KAUST), Thuwal, Saudi Arabia."}], "References": [{"Title": "Shape Memory Alloy-Based Soft Finger with Changeable Bending Length Using Targeted Variable Stiffness", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "283", "JournalTitle": "Soft Robotics"}, {"Title": "Phase Changing Materials-Based Variable-Stiffness Tensegrity Structures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "362", "JournalTitle": "Soft Robotics"}, {"Title": "Twisted Rubber Variable-Stiffness Artificial Muscles", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "386", "JournalTitle": "Soft Robotics"}, {"Title": "Low-Voltage-Driven Large-Amplitude Soft Actuators Based on Phase Transition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "6", "Page": "688", "JournalTitle": "Soft Robotics"}, {"Title": "Hybrid Jamming for Bioinspired Soft Robotic Fingers", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "292", "JournalTitle": "Soft Robotics"}]}, {"ArticleId": 92652364, "Title": "Modellbasierte Security-Analyse", "Abstract": "<p><PERSON><PERSON>r die Bewertung der Security eines Systems wird eine passende Beschreibung benötigt. In diesem Artikel wird eine Methode vorgestellt, um die Security eines Systems zu bewerten. Der Ansatz beginnt mit einem einfachen Modell und verfeinert dieses schrittweise, um das System so vollständig wie nötig abzubilden, wobei die einzelnen Schritte überschaubar bleiben. Nach jedem  Anpassungsschritt (Verfeinerung) des Modells kann eine Analyse des bis zu diesem Zeitpunkt  abgebildeten Systems durchgeführtwerden. Bei der Modellierung und Analyse können auch Systemteile oder deren Eigenschaften (noch) nichtverfügbar oder ungenau beschrieben sein.</p>", "Keywords": "Industrial-Security;Control System Model;Security-Analysis", "DOI": "10.17560/atp.v63i11-12.2568", "PubYear": 2021, "Volume": "63", "Issue": "11-12", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "infoteam SET GmbH"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "T<PERSON>"}], "References": []}, {"ArticleId": 92652519, "Title": "Enhanced cuckoo search algorithm for industrial winding process modeling", "Abstract": "<p>Modeling of nonlinear industrial systems embraces two key stages: selection of a model structure with a compact parameter list, and selection of an algorithm to estimate the parameter list values. Thus, there is a need to develop a sufficiently adequate model to characterize the behavior of industrial systems to represent experimental data sets. The data collected for many industrial systems may be subject to the existence of high non-linearity and multiple constraints. Meanwhile, creating a thoroughgoing model for an industrial process is essential for model-based control systems. In this work, we explore the use of a proposed Enhanced version of the Cuckoo Search (ECS) algorithm to address a parameter estimation problem for both linear and nonlinear model structures of a real winding process. The performance of the developed models was compared with other mainstream meta-heuristics when they were targeted to model the same process. Moreover, these models were compared with other models developed based on some conventional modeling methods. Several evaluation tests were performed to judge the efficiency of the developed models based on ECS, which showed superior performance in both training and testing cases over that achieved by other modeling methods.</p>", "Keywords": "Cuckoo search algorithm; Industrial winding process; Nonlinear model; Linear model", "DOI": "10.1007/s10845-021-01900-1", "PubYear": 2023, "Volume": "34", "Issue": "4", "JournalId": 6457, "JournalTitle": "Journal of Intelligent Manufacturing", "ISSN": "0956-5515", "EISSN": "1572-8145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Al-Balqa Applied University, Al-Salt, Jordan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Southern Connecticut State University, New Haven, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Al-Balqa Applied University, Al-Salt, Jordan"}, {"AuthorId": 4, "Name": "Sultan <PERSON><PERSON>", "Affiliation": "Taif University, Taif, Saudi Arabia"}], "References": [{"Title": "A novel lifetime scheme for enhancing the convergence performance of salp swarm algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "181", "JournalTitle": "Soft Computing"}, {"Title": "Artificial neural networks training via bio-inspired optimisation algorithms: modelling industrial winding process, case study", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "6", "Page": "4545", "JournalTitle": "Soft Computing"}, {"Title": "A Hybrid Multi-gene Genetic Programming with Capuchin Search Algorithm for Modeling a Nonlinear Challenge Problem: Modeling Industrial Winding Process, Case Study", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "2873", "JournalTitle": "Neural Processing Letters"}, {"Title": "The coefficient of determination R-squared is more informative than SMAPE, MAE, MAPE, MSE and RMSE in regression analysis evaluation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e623", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 92652530, "Title": "Two-grid finite volume element method for the time-dependent <PERSON><PERSON><PERSON><PERSON><PERSON> equation", "Abstract": "In this paper, we construct a backward Euler full-discrete two-grid finite volume element scheme for solving time-dependent <PERSON><PERSON><PERSON><PERSON><PERSON> equation. Combining the idea of the two-grid discretization, the original coupling system is solved in the coarse grid space, and the decoupling system with two independent Poisson problems is solved in the fine grid space, which ensures the accuracy and improves the computational efficiency. We further prove the optimal error estimate of the scheme rigorously. Finally, the numerical simulation results show that the two-grid method is more effective than the standard finite volume element method in solving coupled partial differential problems.", "Keywords": "Two-grid method ; Finite volume element method ; Backward Euler scheme ; Error estimates ; Time-dependent Schrödinger equation", "DOI": "10.1016/j.camwa.2022.01.008", "PubYear": 2022, "Volume": "108", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Sciences, Yantai University, Yantai, 264005, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Information Sciences, Yantai University, Yantai, 264005, PR China;@qq.com"}, {"AuthorId": 3, "Name": "<PERSON>z<PERSON> Hu", "Affiliation": "School of Mathematics, Jiaying University, Meizhou 514015, PR China"}], "References": [{"Title": "A two-grid MMOC finite element method for nonlinear variable-order time-fractional mobile/immobile advection–diffusion equations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Zheng", "PubYear": 2020, "Volume": "79", "Issue": "9", "Page": "2771", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "Superconvergence analysis of two-grid FEM for <PERSON>’s equations with a thermal effect", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "12", "Page": "3378", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "A leap-frog finite element method for wave propagation of <PERSON> equations with nonlocal effect in metamaterials", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "25", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 92652604, "Title": "Recent progress in hydrogel-based sensors and energy harvesters", "Abstract": "Hydrogels are three-dimensional polymeric networks that exhibit impressive flexibility through appropriate chemical modification. Hydrogels can be prepared from natural or synthetic polymers which have shown promise in forming different components of stretchable electronic devices such as sensors and energy harvesters. Extensive efforts have been dedicated to creating novel designs, syntheses, and applications of hydrogels. This review presents a classification of hydrogels and describes the main approaches used to develop hydrogels. Moreover, the recent application of hydrogels in creating sensors, including hydrogel-based strain, gas, humidity, pH, and temperature sensors. Next, the current application of hydrogels as soft electrodes for energy harvesters is discussed. In addition, low-temperature tolerant, ultraflexible, biocompatible, and stretchable energy harvesters are illustrated by considering different compositions of hydrogels. The key challenges associated with the creation of hydrogel-based sensors and energy harvesters have been identified and discussed. Finally, a guideline for overcoming the current issues with hydrogel development and applications is provided for future research.", "Keywords": "Hydrogel ; Polymerization ; Sensor ; Energy harvester ; Triboelectric nanogenerator", "DOI": "10.1016/j.sna.2022.113382", "PubYear": 2022, "Volume": "335", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Deakin University, Geelong, VIC 3216, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering, Deakin University, Geelong, VIC 3216, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Engineering, Deakin University, Geelong, VIC 3216, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Engineering, Deakin University, Geelong, VIC 3216, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "School of Engineering, Deakin University, Geelong, VIC 3216, Australia;Corresponding author"}], "References": []}, {"ArticleId": 92652667, "Title": "ALCH: An imperative language for chemical reaction network-controlled tile assembly", "Abstract": "<p><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> recently introduced the chemical reaction network-controlled tile assembly model (CRN-TAM), a variant of the abstract tile assembly model (aTAM). In the CRN-TAM, tile reactions are mediated via non-local chemical signals controlled by a chemical reaction network. This paper introduces ALCH, an imperative programming language for specifying CRN-TAM programs that can be compiled and simulated. ALCH includes standard language features such as Boolean variables, conditionals, loops, and CRN-TAM-specific constructs such as adding and removing tiles. ALCH also includes the branch and parallel structures which harness the nondeterministic and parallel nature of the CRN-TAM. ALCH also supports functional tileset specification. Using ALCH, we show that the discrete Sierpinski triangle and the discrete Sierpinski carpet can be strictly self-assembled in the CRN-TAM, which shows the CRN-TAM can self-assemble infinite shapes at scale 1 that the aTAM cannot. <PERSON>CH allows us to present these constructions at a high level, abstracting species and reactions into C-like code that is simpler to understand. We employ two new CRN-TAM techniques in our constructions. First, we use <PERSON><PERSON>’s nondeterministic branching feature to probe previously placed tiles of the assembly and detect the presence and absence of tiles. Second, we use scaffolding tiles to precisely control tile placement by occluding any undesired binding sites. This paper is an extension of our previous work, updated to include a <PERSON><PERSON><PERSON><PERSON> carpet construction and the parallel command.</p>", "Keywords": "Tile assembly; Chemical reaction network; Sierpinski triangle; Sierpinski carpet; Molecular programming language", "DOI": "10.1007/s11047-021-09878-8", "PubYear": 2024, "Volume": "23", "Issue": "2", "JournalId": 1251, "JournalTitle": "Natural Computing", "ISSN": "1567-7818", "EISSN": "1572-9796", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Drake University, Des Moines, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Iowa State University, Ames, USA; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Northfield, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Iowa State University, Ames, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of British Columbia, Vancouver, Canada"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Iowa State University, Ames, USA"}], "References": [{"Title": "CRN++: Molecular programming language", "Authors": "<PERSON><PERSON>; <PERSON>; Sa<PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "2", "Page": "391", "JournalTitle": "Natural Computing"}, {"Title": "Robust biomolecular finite automata", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "816", "Issue": "", "Page": "114", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Self-assembly of and optimal encoding within thin rectangles at temperature-1 in 3D", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "872", "Issue": "", "Page": "55", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 92652686, "Title": "A novel framework for single-minute exchange of die (SMED) assisted by lean tools", "Abstract": "<p>Nowadays, the survival and success of companies depend on improving on-time delivery and cost reduction. To reach this target, it is necessary to get improvements, standardization, and controlling of the manufacturing systems. Therefore, the objective of this scientific work is to perform a case study, based on the implementation of new single-minute exchange of die (SMED) framework in an oil and gas company. This novel framework was developed based on a practical application of strategies such as improvements by ECRS (eliminate, combine, reduce, and simplify), standardized work (SW), and OEE (overall equipment effectiveness) control applied to the production processes. The findings achieved in this work showed setup time was improved by 91.6%, it moved from 1 h 44 min 56 s (6,296 s) to 8 min 52 s (532 s), and OEE increase 44.6%. And, the setup activities were standardized. Besides, on every workpiece manufactured, its setup time was measured and compared against to the target achieved. So, the novel framework developed has potential to provide competitiveness’ strategies to different branches of the industry.</p>", "Keywords": "SMED; ECRS; Standardized work; Overall equipment effectiveness; Setup time reduction", "DOI": "10.1007/s00170-021-08534-w", "PubYear": 2022, "Volume": "119", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Manufacturing and Materials Engineering, School of Mechanical Engineering, Campinas State University, Campinas, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Manufacturing and Materials Engineering, School of Mechanical Engineering, Campinas State University, Campinas, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Sao Carlos School of Engineering, University of Sao Paulo, Sao Carlos, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Manufacturing and Materials Engineering, School of Mechanical Engineering, Campinas State University, Campinas, Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Federal University of Sao Carlos, Sao Carlos, Brazil"}], "References": []}, {"ArticleId": 92652689, "Title": "Can Competitive Digital Games Support Real-Time Music Creation?", "Abstract": "<p>This article presents practice-based research exploring the interplay of real-time music creation and competitive gameplay. Musically creative video games, apps, and sound art are first surveyed to highlight their characteristic avoidance of competitive game elements. The relationship between play, games, and musical activity is then examined with reference to theoretical perspectives from ludomusicology and game studies, revealing a series of mechanical and aesthetic design tensions emerging between competitive gameplay and music creation. Two original music games are presented to explore this interplay across contrasting design approaches: EvoMusic engenders an abstract competitive dialogue between the player and system for authorial control, while Idea presents a more explicit ludic framework with goals, progression, danger, and victory. The games are evaluated in a comparative user study to capture the player experience of composing within competitive game settings.</p><p>Participant responses revealed conflicting expectations for ludic and compositional experiences. Idea was the preferred game, yet its strong ludic elements distracted from or disincentivized music creation; EvoMusic offered more focused music creation yet also a weaker gameplay experience for lacking these same competitive elements. This relationship reflects the theoretical design tensions suggested by ludomusical scholarship. Further, a majority of participants characterized EvoMusic as being simultaneously competitive and creatively stimulating. The implication is that competitive games can support music creation for certain players, though it remains challenging to satisfy expectations for both within any one system. Design recommendations are drawn from these insights, and the potential for future research into creative music games is discussed.</p>", "Keywords": "game studies; interactive composition; ludomusicology; ludus; music games", "DOI": "10.1525/jsmg.2022.3.1.1", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 73378, "JournalTitle": "Journal of Sound and Music in Games", "ISSN": "", "EISSN": "2578-3432", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92652692, "Title": "Online facility location with mobile facilities", "Abstract": "We examine the Online Facility Location problem in an extended version. <PERSON><PERSON><PERSON> showed a lower bound of Ω ( log ⁡ n log ⁡ log ⁡ n ) for the original Online Facility Location problem, where n is the number of clients. This bound holds even on the real line and for randomized algorithms against oblivious adversaries. We propose randomized online algorithms in the following setting: We consider the Euclidean space of arbitrary dimension and allow the facilities to either move arbitrarily or to move at most a constant distance m in each time step. The costs for moving a facility from a to b is D ⋅ d ( a , b ) where D ≥ 1 is a constant. Clients are assigned to facilities instantly and irreversibly. The cost for serving the clients is either calculated as soon as a client arrives or at the end of the computation. The algorithms for these two cost models achieve the same competitiveness on the line, which, in contrast to the original Online Facility Location problem, is independent of n . In the case of arbitrary movement, it only depends on D . In the case of a limited movement distance m , it additionally depends on m and the opening costs of the facilities. We show that our results are asymptotically tight on the real line. For the Euclidean space of higher dimensions, we give an algorithm for the model where costs for clients are evaluated immediately. The competitive ratio of our algorithm depends on the above parameters and additionally on the number of optimal facilities.", "Keywords": "Facility location ; Online algorithms ; Resource augmentation", "DOI": "10.1016/j.tcs.2022.01.019", "PubYear": 2022, "Volume": "907", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Heinz <PERSON> Institute & Department of Computer Science, Paderborn University, Fürstenallee 11, 33102 Paderborn, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Heinz <PERSON> Institute & Department of Computer Science, Paderborn University, Fürstenallee 11, 33102 Paderborn, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> auf der He<PERSON>", "Affiliation": "Heinz <PERSON> Institute & Department of Computer Science, Paderborn University, Fürstenallee 11, 33102 Paderborn, Germany"}], "References": []}, {"ArticleId": 92652699, "Title": "Chinese named-entity recognition via self-attention mechanism and position-aware influence propagation embedding", "Abstract": "Chinese Named Entity Recognition (NER) has received extensive research attention in recent years. However, Chinese texts lack delimiters to divide the boundaries of words, and some existing approaches cannot capture the long-distance interdependent features. In this paper, we propose a novel end-to-end model for Chinese NER. A new global word boundary detection approach is designed to capture the semantic dependency via a self-attention mechanism to represent character embedding by assigning compatible weights for each character in a sentence. To improve the representation ability of Chinese named-entity boundaries, we introduce position-aware influence propagation with the Gaussian kernel for each character, which combines convergence propagation and radiation propagation. Convergence propagation mainly measures the influence of surrounding characters on the target character. The purpose of radiation propagation is to measure the range of influence of the target character on surrounding characters. The proposed method has been evaluated and shown to offer strong performance in two Chinese NER datasets: MSRA and PFR.", "Keywords": "Chinese named-entity recognition ; Self-attention ; Position-aware influence propagation ; Gaussian kernel", "DOI": "10.1016/j.datak.2022.101983", "PubYear": 2022, "Volume": "139", "Issue": "", "JournalId": 5635, "JournalTitle": "Data & Knowledge Engineering", "ISSN": "0169-023X", "EISSN": "1872-6933", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China;The Institute of Artificial Intelligence on Education, Shanghai Normal University, Shanghai, China;The Shanghai Engineering Research Center of Intelligent Education and Bigdata, Shanghai Normal University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "The College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China;The Department of Electronic and Computer Engineering, Brunel University, London, Uxbridge UB8 3PH, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The College of Information, Mechanical and Electrical Engineering, Shanghai Normal University, Shanghai, China;Corresponding authors"}], "References": []}, {"ArticleId": 92652707, "Title": "TCCT: Tightly-coupled convolutional transformer on time series forecasting", "Abstract": "Time series forecasting is essential for a wide range of real-world applications. Recent studies have shown the superiority of Transformer in dealing with such problems, especially long sequence time series input (LSTI) and long sequence time series forecasting (LSTF) problems. To improve the efficiency and enhance the locality of Transformer, these studies combine Transformer with CNN in varying degrees. However, their combinations are loosely-coupled and do not make full use of CNN. To address this issue, we propose the concept of tightly-coupled convolutional Transformer (TCCT) and three TCCT architectures which apply transformed CNN architectures into Transformer: (1) CSPAttention: through fusing CSPNet with self-attention mechanism, the computation cost of self-attention mechanism is reduced by 30% and the memory usage is reduced by 50% while achieving equivalent or beyond prediction accuracy. (2) Dilated causal convolution: this method is to modify the distilling operation proposed by Informer through replacing canonical convolutional layers with dilated causal convolutional layers to gain exponentially receptive field growth. (3) Passthrough mechanism: the application of passthrough mechanism to stack of self-attention blocks helps Transformer-like models get more fine-grained information with negligible extra computation costs. Our experiments on real-world datasets show that our TCCT architectures could greatly improve the performance of existing state-of-the-art Transformer models on time series forecasting with much lower computation and memory costs, including canonical Transformer, LogTrans and Informer.", "Keywords": "Time series forecasting ; Transformer ; CNN", "DOI": "10.1016/j.neucom.2022.01.039", "PubYear": 2022, "Volume": "480", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Beihang University, RM<PERSON>807, 8th Dormitory, Dayuncun Residential Quarter, No.29, Zhichun Road, Beijing 100191, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beihang University, RM<PERSON>807, 8th Dormitory, Dayuncun Residential Quarter, No.29, Zhichun Road, Beijing 100191, PR China"}], "References": []}, {"ArticleId": 92652720, "Title": "Assessing measures implemented by export-oriented RMG firms in an emerging economy during COVID-19", "Abstract": "Manufacturing firms that continued production activities during the COVID-19 have been taking necessary measures to cope with the risks imposed by the pandemic. This study assesses the measures implemented by the Ready-Made Garments (RMG) sector in Bangladesh. With the increase in COVID-19 cases in Bangladesh, following government order, along with firms in other manufacturing sectors, the RMG firms had to shut-down their production between March 26 and April 25, 2020. Soon after the factories reopened, they had to take necessary actions to ensure employee safety, supply of raw materials, and purchase orders from buyers. Using a semi-structured interview approach, we identify 16 measures that have been implemented in the RMG sector in Bangladesh for the employees, suppliers and buyers. Then, we assess the degree of implementation of these measures using the Bayesian Best-Worst method. We find that providing healthcare safety, bringing previously outsourced activities in-house, and ensuring smooth delivery of existing orders were the three most implemented measures for employees, suppliers and buyers, respectively. On a higher level, the RMG industry professionals prioritised buyer-related measures the most, followed by employee and supplier-related. The analysed measures provide a blueprint for supply chain risk management during future waves of COVID-19 transmission and for other potential large-scale natural disasters.", "Keywords": "Best-Worst Method;COVID-19;Fashion Industry;Risk Management;Supply Chain Disruption", "DOI": "10.1016/j.cie.2022.107963", "PubYear": 2022, "Volume": "165", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Z<PERSON>ul Haque Munim", "Affiliation": "Faculty of Technology, Natural and Maritime Sciences, University of South-Eastern Norway, Horten, Norway."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science, Vrije Universiteit Amsterdam, the Netherlands."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Business Administration, East Delta University, Chittagong, Bangladesh. ;Taylor's Business School, Taylor's University, Subang Jaya, Malaysia."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Production Engineering, Bangladesh University of Engineering and Technology, Dhaka, Bangladesh."}], "References": [{"Title": "Barriers to lean six sigma implementation in the supply chain: An ISM model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "106843", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 92652875, "Title": "Governance of executive personal characteristics and corporate performance based on empirical evidence based on machine learning", "Abstract": "<p>In the current corporate governance literature, most studies on executive characteristics only focus on the relationship between a single executive characteristic and company performance, and lack a comprehensive analysis of executive characteristics; on the other hand, they mainly focus on causal inference. This article uses the Boosting regression tree algorithm in machine learning to thoroughly investigate the relationship between the company's multidimensional performance characteristics and performance, while avoiding the weaknesses of traditional linear models and is more suitable for analyzing nonlinear and interactive relationships between variables. This article uses listed companies from 2015 to 2020 as a sample to empirically evaluate the ability of executives to predict company performance, further dig out the personal characteristics of executives with strong ability to predict company performance, and describe their prediction mechanism. Experiments have shown that the cross-term coefficient of the proportion of senior management’s shareholding and age is − 0.028, showing a significant correlation (P = 0.005 < 0.05), and the cross-term coefficient of the proportion of senior management holdings and education is − 0.003, showing a significant correlation (P = 0.005 < 0.05), which shows that the management analysis of executives' personal characteristics and corporate performance based on machine learning is more accurate than other traditional technical analyzes, which also opens the future corporate governance performance a new direction of research, and the use machine learning and deep learning algorithms for governance decisions can also reduce the involvement of human factors in future corporate performance governance.</p>", "Keywords": "Machine learning; Personal characteristics of executives; Corporate governance; Corporate performance; Embedded system architecture; Boosting regression tree", "DOI": "10.1007/s12652-021-03623-w", "PubYear": 2023, "Volume": "14", "Issue": "7", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Economics and Management, Beijing Institute of Petrochemical Technology, Beijing, China; Enterprise development research center of Beijing Institute of Petrochemical Technology, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Accounting, Shanghai Lixin University of Accounting and Finance, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Huijia Private School, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Economics and Management, Beijing Institute of Petrochemical Technology, Beijing, China; Enterprise development research center of Beijing Institute of Petrochemical Technology, Beijing, China"}], "References": []}, {"ArticleId": ********, "Title": "Analysis of Priority Preemptive Scheduling Algorithm: Case Study", "Abstract": "", "Keywords": "", "DOI": "10.17148/IJARCCE.2022.11105", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 10500, "JournalTitle": "IJARCCE", "ISSN": "2319-5940", "EISSN": "2278-1021", "Authors": [{"AuthorId": 1, "Name": "Tri Dharma Putra", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Scalable recommendation systems based on finding similar items and sequences", "Abstract": "<p>The rapid growth in the airline industry, which started in 2009, continued until the COVID-19 era, with the annual number of passengers almost doubling in 10 years. This situation has led to increased competition between airline companies, whose profitability has decreased considerably. They aimed to increase their profitability by making services like seat selection, excess baggage, Wi-Fi access optional under the name of ancillary services. To the best of our knowledge, there is no recommendation system for recommending ancillary services for airline companies. Also, to the best of our knowledge, there is no testing framework to compare recommendation algorithms considering their scalabilities and running times. In this paper, we propose a framework based on Lambda architecture for recommendation systems that run on a big data processing platform. The proposed method utilizes association rule and sequential pattern mining algorithms that are designed for big data processing platforms. To facilitate testing of the proposed method, we implement a prototype application. We conduct an experimental study on the prototype to investigate the performance of the proposed methodology using accuracy, scalability, and latency related performance metrics. The results indicate that the proposed method proves to be useful and has negligible processing overheads.</p>", "Keywords": "airline ancillary services;apache spark;association rule mining;distributed systems;sequential pattern mining", "DOI": "10.1002/cpe.6841", "PubYear": 2022, "Volume": "34", "Issue": "20", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "BiletBank Research and Development Center Akdeniz PE‐TUR A.Ş.  Istanbul Turkey;Computer Engineering Department Istanbul Health and Technology University  Istanbul Turkey"}, {"AuthorId": 2, "Name": "Ahmet Volkan Gurel", "Affiliation": "BiletBank Research and Development Center Akdeniz PE‐TUR A.Ş.  Istanbul Turkey"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "BiletBank Research and Development Center Akdeniz PE‐TUR A.Ş.  Istanbul Turkey"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department Yildiz Technical University  Istanbul Turkey"}], "References": [{"Title": "Performance evaluation of support vector machine and convolutional neural network algorithms in real-time vehicle type and color classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Fatmanur <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "83", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "A Spark-based Apriori algorithm with reduced shuffle overhead", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "1", "Page": "133", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Multimodel anomaly detection on spatio-temporal logistic datastream with open anomaly detection architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>ğlu; <PERSON><PERSON>ı<PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "115755", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Pattern2Vec: Representation of clickstream data sequences for learning user navigational behavior", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "e6546", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 92652991, "Title": "Automated diagnosis of diverse coffee leaf images through a stage-wise aggregated triple deep convolutional neural network", "Abstract": "<p>Due to the struggles of developing countries in coping with widespread coffee leaf diseases and infestations, the quality and quantity of coffee-based commodities have reduced significantly. This paper proposes a solution to this problem using Deep Convolutional Neural Networks (DCNN) that classifies seven coffee leaf conditions. Unlike other studies, this work proposed a novel Triple-DCNN (T-DCNN) composed of three aggregated DCNN models formed in an ensemble to produce lesser bias and better accuracy than standard models. Added to the proposed T-DCNN, an employed stage-wise approach narrowed down the classification options through a multi-staged structure and diversified the entire feature pool. Upon evaluation, the proposed Stage-Wise Aggregated T-DCNN (SWAT-DCNN) yielded successful diagnoses of diverse coffee leaf conditions in various environmental settings. Furthermore, with an overall accuracy of 95.98%, the SWAT-DCNN outperformed most state-of-the-art DCNNs that performed the same task.</p>", "Keywords": "Deep convolutional neural networks; Coffee leaf diseases; Stage-wise model; Image classification; Deep learning", "DOI": "10.1007/s00138-022-01277-y", "PubYear": 2022, "Volume": "33", "Issue": "1", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Informatics and Computing Science, Batangas State University, Batangas City, Philippines"}], "References": []}, {"ArticleId": 92653030, "Title": "Artificial neural networks for resources optimization in energetic environment", "Abstract": "Resource Planning Optimization (RPO) is a common task that many companies need to face to get several benefits, like budget improvements and run-time analyses. However, even if it is often solved by using several software products and tools, the great success and validity of the Artificial Intelligence-based approaches, in many research fields, represent a huge opportunity to explore alternative solutions for solving optimization problems. To this purpose, the following paper aims to investigate the use of multiple Artificial Neural Networks (ANNs) for solving a RPO problem related to the scheduling of different Combined Heat & Power (CHP) generators. The experimental results, carried out by using data extracted by considering a real Microgrid system, have confirmed the effectiveness of the proposed approach.", "Keywords": "Artificial Neural network; Resources planning optimization; Energetic environment; Energetic generators; Microgrid system; Artificial intelligence", "DOI": "10.1007/s00500-022-06757-x", "PubYear": 2022, "Volume": "26", "Issue": "4", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano (SA), Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano (SA), Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of Salerno, Fisciano (SA), Italy"}], "References": [{"Title": "A machine learning evolutionary algorithm-based formula to assess tumor markers and predict lung cancer in cytologically negative pleural effusions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "10", "Page": "7281", "JournalTitle": "Soft Computing"}, {"Title": "A machine learning evolutionary algorithm-based formula to assess tumor markers and predict lung cancer in cytologically negative pleural effusions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "10", "Page": "7281", "JournalTitle": "Soft Computing"}, {"Title": "Knowledge elicitation based on genetic programming for non destructive testing of critical aerospace systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "633", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Knowledge elicitation based on genetic programming for non destructive testing of critical aerospace systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "633", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Malware detection in mobile environments based on Autoencoders and API-images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "26", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Malware detection in mobile environments based on Autoencoders and API-images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "137", "Issue": "", "Page": "26", "JournalTitle": "Journal of Parallel and Distributed Computing"}]}, {"ArticleId": 92653053, "Title": "Wireless powered hybrid backscatter-active communications with hardware impairments", "Abstract": "Wireless powered hybrid backscatter-active communication can full make use of the different tradeoff between power consumption and achievable rate of the active and backscatter communications, and thus achieving a better performance than wireless powered active or backscatter communications. In this paper, we design a throughput maximization-based resource allocation scheme for a wireless powered hybrid backscatter-active communication network, while considering the hardware impairments at all RF front ends of each transceiver. Towards this end, we formulate a problem by jointly optimizing the transmit power of the dedicated energy source, the time for pure energy harvesting, backscatter and active communications, the power reflection coefficient, and the transmit power of each IoT node during active communications. The formulated problem is non-convex and different to solve. Subsequently an iterative algorithm based on the block coordinated decent technology is proposed to address the above problem. Simulation results verify that our proposed iterative algorithm converges very fast and that the proposed scheme outperforms the baseline schemes in terms of the throughput.", "Keywords": "Backscatter communications ; Energy harvesting ; Active communications ; Hardware impairments", "DOI": "10.1016/j.phycom.2022.101604", "PubYear": 2022, "Volume": "52", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information and Electrical Engineering, Heilongjiang Bayi Agricultural University, Daqing, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information and Electrical Engineering, Heilongjiang Bayi Agricultural University, Daqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Information and Electrical Engineering, Heilongjiang Bayi Agricultural University, Daqing, China"}], "References": [{"Title": "Outage performance of UAV-assisted AF relaying with hardware impairments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "", "Page": "101334", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 92653105, "Title": "Experimental fatigue dataset for additive-manufactured 3D-printed Polylactic acid biomaterials under fully-reversed rotating-bending bending loadings", "Abstract": "In this dataset, experimental fatigue testing results have been presented for the additive-manufactured 3D-printed Polylactic acid (PLA) biomaterials under fully-reversed rotating-bending loadings. For such an objective, a fused deposition modeling (FDM) 3D-printer was utilized to fabricate the standard cylindrical samples with different printing parameters in the horizontal direction. For the demonstration of printing parameter effects on the PLA fatigue lifetime, the nozzle diameters were from 0.2 to 0.6 mm, the extruder temperatures were from 180 to 240 °C, and finally, the printing speeds were from 5 to 15 mm/s. Then after 3D-printing of specimens, fatigue testing was performed on various samples under fully-reversed rotating-bending loadings. Then, the fatigue data were presented in tables through the high-cycle fatigue regime, under the load-controlled condition. For further works, these dataset tables could be used to draw the S-N (stress-lifetime) diagram, to find the fatigue strength coefficient and exponent.", "Keywords": "Fatigue dataset ; Additive manufacturing ; 3D-printing ; Polylactic acid biomaterial ; Cyclic loading", "DOI": "10.1016/j.dib.2022.107846", "PubYear": 2022, "Volume": "41", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Mechanical Engineering, Semnan University, Semnan, Iran;Corresponding author's email address and Twitter handle"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Mechanical Engineering, Semnan University, Semnan, Iran"}], "References": [{"Title": "Monotonic load datasets for additively manufactured thermoplastic reinforced composites", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "", "Page": "105295", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 92653160, "Title": "Reinforcement Learning for Datacenter Congestion Control", "Abstract": "<p>We approach the task of network congestion control in datacenters using Reinforcement Learning (RL). Successful congestion control algorithms can dramatically improve latency and overall network throughput. Until today, no such learning-based algorithms have shown practical potential in this domain. Evidently, the most popular recent deployments rely on rule-based heuristics that are tested on a predetermined set of benchmarks. Consequently, these heuristics do not generalize well to newly-seen scenarios. Contrarily, we devise an RL-based algorithm with the aim of generalizing to different configurations of real-world datacenter networks. We overcome challenges such as partial-observability, nonstationarity, and multi-objectiveness. We further propose a policy gradient algorithm that leverages the analytical structure of the reward function to approximate its derivative and improve stability. We show that this scheme outperforms alternative popular RL approaches, and generalizes to scenarios that were not seen during training. Our experiments, conducted on a realistic simulator that emulates communication networks' behavior, exhibit improved performance concurrently on the multiple considered metrics compared to the popular algorithms deployed today in real datacenters. Our algorithm is being productized to replace heuristics in some of the largest datacenters in the world.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512815", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "Gal Chechik", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92653245, "Title": "Quasi oppositional Aquila optimizer-based task scheduling approach in an IoT enabled cloud environment", "Abstract": "<p>Large-scale applications of the Internet of Things (IoT) necessitate significant computing tasks and storage resources that are progressively installed in the cloud environment. Related to classical computing models, the features of the cloud, such as pay-as-you-go, indefinite expansions, and dynamic acquisition, signify various services to these applications utilizing the IoT structure. A major challenge is to fulfill the quality of service necessities but schedule tasks to resources. The resource allocation scheme is affected by different undefined reasons in real-time platforms. Several works have considered the factors in the design of effective task scheduling techniques. In this context, this research addresses the issue of resource allocation and management in an IoT-enabled CC environment by designing a novel quasi-oppositional Aquila optimizer-based task scheduling (QOAO-TS) technique. The QOAO technique involves the integration of quasi-oppositional-based learning with an Aquila optimizer (AO). The traditional AO is stimulated by Aquila’s behavior while catching the prey, and the QOAO is derived to improve the performance of the AO. The QOAO-TS technique aims to fulfill the makespan by accomplishing the optimum task scheduling process. The proposed QOAO-TS technique considers the relationship among task scheduling and satisfies the client’s needs by minimizing the makespan. A wide range of simulations take place, and the results are investigated in terms of the span, throughput, flow time, lateness, and utilization ratio.</p>", "Keywords": "Cloud computing; Internet of Things; Task scheduling; Objective function; Makespan; Bioinspired algorithm", "DOI": "10.1007/s11227-022-04311-y", "PubYear": 2022, "Volume": "78", "Issue": "7", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Aditya Engineering College, Surempalem, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Velammal Institute of Technology, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Holycross Engineering College, Thoothukudi, India"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Medical Equipment Technology, College of Applied Medical Sciences, Majmaah University, Al Majmaah, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Medical Equipment Technology, College of Applied Medical Sciences, Majmaah University, Al Majmaah, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Vel Tech Multi Tech Dr. <PERSON><PERSON><PERSON><PERSON> Dr. Sakunthala Engineering College, Chennai, India"}], "References": [{"Title": "Task scheduling in Internet of Things cloud environment using a robust particle swarm optimization", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Security-Critical Energy-Aware Task Scheduling for Heterogeneous Real-Time MPSoCs in IoT", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "745", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Mobility-aware task scheduling in cloud-Fog IoT-based healthcare architectures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "179", "Issue": "", "Page": "107348", "JournalTitle": "Computer Networks"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Task allocation optimization model in mechanical product development based on Bayesian network and ant colony algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "12", "Page": "13963", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Amended hybrid multi-verse optimizer with genetic algorithm for solving task scheduling problem in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "1", "Page": "740", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Using a task dependency job-scheduling method to make energy savings in a cloud computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "3", "Page": "4550", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 92653282, "Title": "A Distinctive Approach on the Usage of Edge Computing Concept on Humidity Dataset through Regression Analysis", "Abstract": "Edge computing is a paradigm that can distribute the complexity of predictive analysis into smaller pieces, physically placed at the source of contextual information. This allows in processing large amounts of data where it is intricate to use a centralized cloud. Edge Computing makes this possible by taking control of data and services from central hubs, which reduces computational latency on servers. Humidity is one of the main factors that maintain the life of the surface. This article explains how to perform computational analysis at the \"edge\" by using humidity data sets, and also shows that the most modern data is sufficient for data analysis. Linear Regression and Random Forest Regression algorithms are utilized for data analysis. In addition, this article illustrates the importance of data series for predicting humidity by comparing the analysis of unshuffled data and shuffled data. Metrics have been used to assess the accuracy and point out the importance of sequential data feeds for analysis.", "Keywords": "", "DOI": "10.47760/ijcsmc.2022.v11i01.004", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92653291, "Title": "Neighborhood Rough Neural Network Approach for COVID-19 Image Classification", "Abstract": "<p>The rapid spread of the new Coronavirus, COVID-19, causes serious symptoms in humans and can lead to fatality. A COVID-19 infected person can experience a dry cough, muscle pain, headache, fever, sore throat, and mild to moderate respiratory illness, according to a clinical report. A chest X-ray (also known as radiography) or a chest CT scan are more effective imaging techniques for diagnosing lung cancer. Computed Tomography (CT) scan images allow for fast and precise COVID-19 screening. In this paper, a novel hybridized approach based on the Neighborhood Rough Set Classification method (NRSC) and Backpropagation Neural Network (BPN) is proposed to classify COVID and NON-COVID images. The proposed novel classification algorithm is compared with other existing benchmark approaches such as Neighborhood Rough Set, Backpropagation Neural Network, Decision Tree, Random Forest Classifier, Naive Bayes Classifier, K- Nearest Neighbor, and Support Vector Machine. Various classification accuracy measures are used to assess the efficacy of the classification algorithms.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2021.</p>", "Keywords": "COVID-19;CT scan images;Neighborhood Rough Neural Network (NRNN);Neighborhood rough set", "DOI": "10.1007/s11063-021-10712-6", "PubYear": 2022, "Volume": "54", "Issue": "3", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Periyar University, Salem, Tamil Nadu India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Periyar University, Salem, Tamil Nadu India."}], "References": [{"Title": "Infrared Image Extraction Algorithm Based on Adaptive Growth Immune Field", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "3", "Page": "2575", "JournalTitle": "Neural Processing Letters"}, {"Title": "The ensemble deep learning model for novel COVID-19 on CT images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106885", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 92653298, "Title": "Image-text interaction graph neural network for image-text sentiment analysis", "Abstract": "<p>As various social platforms are experiencing fast development, the volume of image-text content generated by users has grown rapidly. Image-text based sentiment of social media analysis has also attracted great interest from researchers in recent years. The main challenge of image-text sentiment analysis is how to construct a model that can promote the complementarity between image and text. In most previous studies, images and text were simply merged, while the interaction between them was not fully considered. This paper proposes an image-text interaction graph neural network for image-text sentiment analysis. A text-level graph neural network is used to extract the text features, and a pre-trained convolutional neural network is employed to extract the image features. Then, an image-text interaction graph network is constructed. The node features of the graph network are initialized by the text features and the image features, while the node features in the graph are updated based on the graph attention mechanism. Finally, combined with image-text aggregation layer to realize sentiment classification. The results of the experiments prove that the presented method is more effective than existing methods. In addition, a large-scale Twitter image-text sentiment analysis dataset was built by us and used in the experiments.</p>", "Keywords": "Multi-modal sentiment analysis; Sentiment analysis; Social data mining; Graph neural network", "DOI": "10.1007/s10489-021-02936-9", "PubYear": 2022, "Volume": "52", "Issue": "10", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computers, Guangdong University of Technology, Guangzhou, China"}], "References": [{"Title": "SACPC: A framework based on probabilistic linguistic terms for short text sentiment analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105572", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Transformer based Deep Intelligent Contextual Embedding for Twitter sentiment analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "113", "Issue": "", "Page": "58", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An improved aspect-category sentiment analysis model for text sentiment analysis based on RoBERTa", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "3522", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 92653433, "Title": "Rumor containment in peer-to-peer message sharing online social networks", "Abstract": "<p>Rumors in online social networks (OSNs) create social chaos, financial losses, and endanger property, which makes rumor containment an important issue. We consider an OSN in which the users communicate via private peer-to-peer messages. We consider the proposed peer-to-peer linear threshold (PLT) and peer-to-peer independent cascade-variant (PICV) models for information diffusion in OSNs, which are variants of the classic IC and LT models, respectively. To combat the rumor spread in the OSN with peer-to-peer message sharing, we employ blocking and positive information diffusion strategies. While in blocking strategy, few users of the OSN called the blocked seed nodes are blocked from spreading the rumor, in positive information diffusion strategy, correct information is introduced into few users of the OSN called positive seed nodes. The positive seed nodes further spread the correct information to other users with time. For a given time-period called the rumor-relevance interval, we determine average number of rumor-influenced nodes for the random, the max-degree, the greedy, the proximity heuristic, and the proposed proximity-weight-degree (PWD)-based containment seed node selection schemes for both blocking and positive information diffusion strategies for PLT and PICV models. We compare the effect of the rumor-relevance interval duration and number of seed nodes on the average number of rumor-influenced nodes for different seed selection algorithms. Our experimental results show that proximity-weight-degree-based seed selection algorithm performs on par with the high-complexity greedy scheme.</p>", "Keywords": "", "DOI": "10.1007/s41060-021-00293-x", "PubYear": 2022, "Volume": "13", "Issue": "3", "JournalId": 27236, "JournalTitle": "International Journal of Data Science and Analytics", "ISSN": "2364-415X", "EISSN": "2364-4168", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, IIIT Guwahati, Guwahati, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, IIIT Guwahati, Guwahati, India"}], "References": [{"Title": "Containment of rumor spread in complex social networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "113", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 92653471, "Title": "An empirical analysis of cloud based robotics: challenges and applications", "Abstract": "<p>Applications of robots play an important role in autonomous systems, and with the dawning of cloud computing technologies, it can be upgraded to another level. This paper presents an in-depth review of the works based on cloud robotics, applications which were upgraded using cloud computing platforms. Some of the most utilized cloud platforms are AWS, Google Cloud, Azure and many more. This paper mainly focuses on the different applications of robotics deployed in recent times on to the cloud platform. After adding the computational capabilities of cloud platform a significant improvement in terms of robotics application parameters has been observed. This particular improvement is being keenly observed and discussed in the paper. This paper intends to review the several parameters of robotics application and their performance with the cloud computing platforms and bring the researchers’ attention to opt for cloud technology for robotics application. To validate the hypothesis, a small contribution is also being presented in the paper. The implementation of path planning for single robots and coordination mechanism for multi-robot using the cloud is discussed and presented in the paper. A thorough comparison of robotics application parameters and their specific cloud technology is analysed and presented in the paper. In the end, the paper proves and concludes that cloud-based robots are the future technology that will make robot more smarter and efficient which will make it more acceptable in the coming time.</p>", "Keywords": "Cloud; Robotics; Application; Path planning; Coordination", "DOI": "10.1007/s41870-021-00842-4", "PubYear": 2022, "Volume": "14", "Issue": "2", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Raipur, Raipur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology Raipur, Raipur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Technology Raipur, Raipur, India"}], "References": [{"Title": "A survey of deep learning techniques for autonomous driving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "3", "Page": "362", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Follow me Robot-Mind: Cloud brain based personalized robot service with migration", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "324", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "An effective service-oriented networking management architecture for 5G-enabled internet of things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "173", "Issue": "", "Page": "107208", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 92653472, "Title": "Improving the prediction of continuous integration build failures using deep learning", "Abstract": "<p>Continuous Integration (CI) aims at supporting developers in integrating code changes constantly and quickly through an automated build process. However, the build process is typically time and resource-consuming as running failed builds can take hours until discovering the breakage; which may cause disruptions in the development process and delays in the product release dates. Hence, preemptively detecting when a software state is most likely to trigger a failure during the build is of crucial importance for developers. Accurate build failures prediction techniques can cut the expenses of CI build cost by early predicting its potential failures. However, developing accurate prediction models is a challenging task as it requires learning long- and short-term dependencies in the historical CI build data as well as extensive feature engineering to derive informative features to learn from. In this paper, we introduce DL-CIBuild a novel approach that uses Long Short-Term Memory (LSTM)-based Recurrent Neural Networks (RNN) to construct prediction models for CI build outcome prediction. The problem is comprised of a single series of CI build outcomes and a model is required to learn from the series of past observations to predict the next CI build outcome in the sequence. In addition, we tailor Genetic Algorithm (GA) to tune the hyper-parameters for our LSTM model. We evaluate our approach and investigate the performance of both cross-project and online prediction scenarios on a benchmark of 91,330 CI builds from 10 large and long-lived software projects that use the Travis CI build system. The statistical analysis of the obtained results shows that the LSTM-based model outperforms traditional Machine Learning (ML) models with both online and cross-project validations. DL-CIBuild has shown also a less sensitivity to the training set size and an effective robustness to the concept drift. Additionally, by considering several Hyper-Parameter Optimization (HPO) methods as baseline for GA, we demonstrate that the latter performs the best</p>", "Keywords": "Continuous integration; Build prediction; Travis CI; Genetic algorithm; Long short term memory; Machine learning; Hyper-parameters optimization; Concept drift", "DOI": "10.1007/s10515-021-00319-5", "PubYear": 2022, "Volume": "29", "Issue": "1", "JournalId": 1834, "JournalTitle": "Automated Software Engineering", "ISSN": "0928-8910", "EISSN": "1573-7535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "ETS Montreal, University of Quebec, Montreal, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ETS Montreal, University of Quebec, Montreal, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Rochester Institute of Technology, Rochester, USA"}], "References": [{"Title": "On hyperparameter optimization of machine learning algorithms: Theory and practice", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "415", "Issue": "", "Page": "295", "JournalTitle": "Neurocomputing"}, {"Title": "Predicting continuous integration build failures using evolutionary search", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "", "Page": "106392", "JournalTitle": "Information and Software Technology"}, {"Title": "On the impact of Continuous Integration on refactoring practice: An exploratory study on TravisTorrent", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "138", "Issue": "", "Page": "106618", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 92653519, "Title": "Exact Response Time Analysis of Preemptive Priority Scheduling with Switching Overhead", "Abstract": "<p>The study of preemptive scheduling is essential to computer systems [15, 12, 3, 4]. Motivated by this, decades of queueing theory research have been done on the subject [19, 18, 16, 13, 21, 8, 17, 2, 11, 20, 10, 1]. However, almost all queuing theoretic literature on preemptive scheduling concerns systems without switching overhead - pausing or resuming a job is assumed to be instant. Practically speaking, switching in computer systems incurs some overhead [14], which causes a divide between models in research and the real world.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512824", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}], "References": []}, {"ArticleId": 92653522, "Title": "Performance Analysis of A Queueing System with Server Arrival and Departure", "Abstract": "<p>In many systems, in order to fulfill demand (computing or other services) that varies over time, service capacities often change accordingly. In this paper, we analyze a simple two dimensional Markov chain model of a queueing system in which multiple servers can arrive to increase service capacity, and depart if a server has been idle for too long. It is well known that multi-dimensional Markov chains are in general difficult to analyze. Our focus is on an approximation method of stationary performance of the system via the Stein method. For this purpose, innovative methods are developed to estimate the moments of the Markov chain, as well as the solution to the Poisson equation with a partial differential operator.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512807", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "Yingdong Lu", "Affiliation": "IBM Research, Yorktown Heights, NY, USA"}], "References": []}, {"ArticleId": 92653561, "Title": "Using scaffolded feedforward and peer feedback to improve problem-based learning in large classes", "Abstract": "The growing demand for access to higher education has seen institutions turn increasingly towards large classes. Implementing active, problem-based learning in this context can be difficult as it requires the lecturer to attend to every student&#x27;s individual needs. Given the lack of tools for providing personalized feedback, this represents a significant challenge. The aim of this study is to see how best to support lecturers in giving timely feedback to students in a large class during problem-based learning. To meet this goal, we propose a model that combines feedforward, scaffolded using an automated summarization tool, with peer feedback. In this sense, the lecturer first provides feedforward through a series of general comments before an anonymous peer gives personalized feedback. The results show that, despite not giving personalized feedback, the lecturer is able to provide enriched formative feedforward thanks to the summary generated by the automated system. Furthermore, in more qualitative terms, the students show that they appreciate the opportunity to both give and receive feedback. Finally, the students&#x27; critical thinking skills are also shown to improve progressively from one activity to the next. Given the research gap regarding how lecturers use the reports generated by automated summarization tools, our study contributes to the literature by proposing a strategy for lecturers to use such reports to provide feedforward. Additionally, this study also contributes to the literature by proposing a model that can be fully integrated in both synchronous and asynchronous online learning.", "Keywords": "", "DOI": "10.1016/j.compedu.2022.104446", "PubYear": 2022, "Volume": "182", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Engineering, Pontificia Universidad Católica de Chile, Avenida Vicuña Mackenna, 4860, Macul, Región Metropolitana, Chile;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering, Pontificia Universidad Católica de Chile, Avenida Vicuña Mackenna, 4860, Macul, Región Metropolitana, Chile"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Pontificia Universidad Católica de Chile, Avenida Vicuña Mackenna, 4860, Macul, Región Metropolitana, Chile"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Pontificia Universidad Católica de Chile, Avenida Vicuña Mackenna, 4860, Macul, Región Metropolitana, Chile"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mathematics, Pontificia Universidad Católica de Chile, Avenida Vicuña Mackenna, 4860, Macul, Región Metropolitana, Chile"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "School of Engineering, Pontificia Universidad Católica de Chile, Avenida Vicuña Mackenna, 4860, Macul, Región Metropolitana, Chile"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Pontificia Universidad Católica de Chile, Avenida Vicuña Mackenna, 4860, Macul, Región Metropolitana, Chile"}], "References": [{"Title": "Automatic text summarization: A comprehensive survey", "Authors": "Waf<PERSON> <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113679", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 92653626, "Title": "Artificial neural network-based decision support systems in manufacturing processes: A systematic literature review", "Abstract": "The use of artificial neural network models to enrich the analytical and predictive capabilities of decision support systems in manufacturing has increased. The growing complexity and uncertainty in the manufacturing sector demand improved decision-making to ensure low operations costs, high productivity, and sustainable use of resources. Artificial neural networks have the inherent capacity to analyze the most uncertain and complex patterns in unstructured decision problems. This review aims to synthesize and provide a comprehensive summary of recent studies on artificial neural network-based decision support systems as applied in manufacturing processes. First, the specific processes in manufacturing where artificial neural network-based decision support systems are used are analyzed. A total of 99 multi-disciplinary publications on artificial neural network-based decision support systems published between 2011 and 2021 are retrieved and processed following a rigorous execution of the designated acceptance criteria and quality assessment. A review of the selected studies indicates a growing interest in applying artificial neural networks in decision support systems. Product and process design, performance evaluation, and predictive maintenance are the main application areas identified. A growing tendency to combine artificial neural network models with other intelligent tools, notably fuzzy logic, and genetic algorithm, is noted to overcome drawbacks such as slow convergence when training the algorithms. Further research should extend to other tools for enriching the performance of artificial neural networks in manufacturing processes.", "Keywords": "Decision support systems ; Intelligent decision support ; Artificial neural networks ; Manufacturing ; Systematic literature review", "DOI": "10.1016/j.cie.2022.107964", "PubYear": 2022, "Volume": "165", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Management and Information Systems, Faculty of Engineering Management, Poznań University of Technology, 60-965 Poznań, Poland"}], "References": [{"Title": "Literature review of Industry 4.0 and related technologies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "1", "Page": "127", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Fault classification in three-phase motors based on vibration signal analysis and artificial neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "18", "Page": "15171", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Sensitivity analysis of the artificial neural networks in a system for durability prediction of forging tools to forgings made of C45 steel", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "Page": "1385", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Demand forecasting with color parameter in retail apparel industry using artificial neural networks (ANN) and support vector machines (SVM) methods", "Authors": "<PERSON><PERSON><PERSON>; Fuat Şimşir", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "106678", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Predictive maintenance in the Industry 4.0: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "106889", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Optimization strategies of neural networks for impact damage classification of RC panels in a small dataset", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107100", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 92653662, "Title": "Experimental evaluation of autonomous map-based Spot navigation in confined environments", "Abstract": "In this article, we address the task of experimental evaluation of autonomous map-based navigation of a Boston Dynamics Spot quadruped robot in confined environments equipped with the developed autonomy package that incorporates a 3D lidar, an IMU, and an onboard computer. For that, we propose an integrated software and hardware system that is considered as an enabler for robot localization and risk-aware path planning, based on the known map. The system design itself is modular and incorporates the perception capabilities required for autonomous navigation. The Spot robot first is utilized to build the offline map of the environment by utilizing the Google Cartographer simultaneous localization and mapping (SLAM) package. During the next step, the online environmental information from the autonomy package sensors and the offline map are provided to the onboard computer to localize the robot on the known map by utilizing means provided by <PERSON><PERSON><PERSON>. Finally, the occupancy information is provided to the online grid-based path planner that generates risk-aware paths. The extensive experimental evaluation of the proposed system is performed in corridors and SubT environments.", "Keywords": "Spot ; SubT ; Map-based navigation ; 3D lidar ; Autonomy package", "DOI": "10.1016/j.birob.2022.100035", "PubYear": 2022, "Volume": "2", "Issue": "1", "JournalId": 85921, "JournalTitle": "Biomimetic Intelligence and Robotics", "ISSN": "2667-3797", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Robotics & AI Team, Department of Computer, Electrical and Space Engineering, Luleå University of Technology, Luleå SE-97187, Sweden;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Robotics & AI Team, Department of Computer, Electrical and Space Engineering, Luleå University of Technology, Luleå SE-97187, Sweden"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Robotics & AI Team, Department of Computer, Electrical and Space Engineering, Luleå University of Technology, Luleå SE-97187, Sweden"}], "References": [{"Title": "Deploying MAVs for autonomous navigation in dark underground mine environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "126", "Issue": "", "Page": "103472", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Subterranean MAV Navigation based on Nonlinear MPC with Collision Avoidance Constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "9650", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 92653663, "Title": "An optimization-based approach to assess non-interference in labeled and bounded Petri net systems", "Abstract": "An optimization-based approach to assess both strong non-deterministic non- interference (SNNI) and bisimulation SNNI (BSNNI) in discrete event systems modeled as labeled Petri nets is presented in this paper. The assessment of SNNI requires the solution of feasibility problems with integer variables and linear constraints, which is derived by extending a previous result given in the case of unlabeled net systems. Moreover, the BSNNI case can be addressed in two different ways. First, similarly to the case of SNNI, a condition to assess BSNNI, which is necessary and sufficient, can be derived from the one given in the unlabeled framework, requiring the solution of feasibility problems with integer variables and linear constraints. Then, a novel necessary and sufficient condition to assess BSNNI is given, which requires the solution of integer feasibility problems with nonlinear constraints. Furthermore, we show how to recast these problems into equivalent mixed-integer linear programming (MILP) ones. The effectiveness of the proposed approaches is shown by means of several examples. It turns out that there are relevant cases where the new condition to assess BSNNI that requires the solution of MILP problems is computationally more efficient, when compared to the one that requires the solution of feasibility problems.", "Keywords": "System privacy ; Non-interference ; Labeled Petri nets ; MILP and ILP problems", "DOI": "10.1016/j.nahs.2022.101153", "PubYear": 2022, "Volume": "44", "Issue": "", "JournalId": 5866, "JournalTitle": "Nonlinear Analysis: Hybrid Systems", "ISSN": "1751-570X", "EISSN": "1878-7460", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DIEM, Università degli Studi di Salerno, 84084, Fisciano, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DIETI, Università degli Studi di Napoli Federico II, 80125 Napoli, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DIETI, Università degli Studi di Napoli Federico II, 80125 Napoli, Italy;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "DIETI, Università degli Studi di Napoli Federico II, 80125 Napoli, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "DIETI, Università degli Studi di Napoli Federico II, 80125 Napoli, Italy"}], "References": [{"Title": "Industrial Control Systems: Cyberattack trends and countermeasures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "155", "Issue": "", "Page": "1", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 92653666, "Title": "ACM SIGMETRICS 2021 Student Research Competition", "Abstract": "<p>Every year, the Association for Computation Machinery (ACM) spearheads a series of Student Research Competitions (SRCs) at ACM-sponsored or co-sponsored conferences. These SRCs, which are sponsored by Microsoft Research, provide undergraduate and graduate students an opportunity to present their original research to a panel of judges. Participating students present posters and oral presentations of their research for the opportunity to win cash prizes. In each category (graduate and undergraduate), the 1st, 2nd, and 3rd place winners receive prizes of $500, $300, and $200, respectively. Additionally, first place undergraduate and graduate (Masters or PhD program) student winners from the SRCs held during the year are invited to present their work at the SRC Grand Finals, where a different panel of judges evaluates these winners against each other. To date, Microsoft Research has generously provided $120,000 per competition year since 2003.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512820", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "G<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, PA, USA"}], "References": []}, {"ArticleId": 92653682, "Title": "Are Covert DDoS Attacks Facing Multi-Feature Detectors Feasible?", "Abstract": "<p>We state and prove the square root scaling laws for the amount of traffic injected by a covert attacker into a network from a set of homes under the assumption that traffic descriptors follow a multivariate Gaussian distribution. We numerically evaluate the obtained result under realistic settings wherein traffic is collected from real users, leveraging detectors that exploit multiple features. Under such circumstances, we observe that phase transitions predicted by the model still hold.</p>", "Keywords": "", "DOI": "10.1145/3512798.3512811", "PubYear": 2022, "Volume": "49", "Issue": "2", "JournalId": 17445, "JournalTitle": "ACM SIGMETRICS Performance Evaluation Review", "ISSN": "0163-5999", "EISSN": "1557-9484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Massachusetts at Amherst, Amherst, MA, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Massachusetts at Amherst, Amherst, MA, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INRIA, , France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "UFRJ, , Brazil"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "UFRJ, , Brazil"}], "References": []}, {"ArticleId": 92653734, "Title": "#PraCegoVer: A Large Dataset for Image Captioning in Portuguese", "Abstract": "<p>Automatically describing images using natural sentences is essential to visually impaired people’s inclusion on the Internet. This problem is known as Image Captioning. There are many datasets in the literature, but most contain only English captions, whereas datasets with captions described in other languages are scarce. We introduce the #PraCegoVer, a multi-modal dataset with Portuguese captions based on posts from Instagram. It is the first large dataset for image captioning in Portuguese. In contrast to popular datasets, #PraCegoVer has only one reference per image, and both mean and variance of reference sentence length are significantly high, which makes our dataset challenging due to its linguistic aspect. We carry a detailed analysis to find the main classes and topics in our data. We compare #PraCegoVer to MS COCO dataset in terms of sentence length and word frequency. We hope that #PraCegoVer dataset encourages more works addressing the automatic generation of descriptions in Portuguese.</p>", "Keywords": "#PraCegoVer; image captioning in Portuguese; image captioning; image-to-text #PraCegoVer ; image captioning in Portuguese ; image captioning ; image-to-text", "DOI": "10.3390/data7020013", "PubYear": 2022, "Volume": "7", "Issue": "2", "JournalId": 48259, "JournalTitle": "Data", "ISSN": "", "EISSN": "2306-5729", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Computing, University of Campinas (Unicamp), Campinas 13083-852, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Computing, University of Campinas (Unicamp), Campinas 13083-852, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Author to whom correspondence should be addressed"}], "References": [{"Title": "Datasheets for datasets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "12", "Page": "86", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 92653805, "Title": "A polynomial algorithm for deciding the validity of an electrical distribution tree", "Abstract": "We consider power distribution networks containing source nodes producing electricity and nodes representing electricity consumers interconnected by a switched network. Configuring this network consists in deciding which switches are activated and the orientation of the links between these switches, so as to obtain a directed graph from the producer nodes to the consumer nodes. This graph is valid if the electric flow it induces satisfies the demand of each consumer without exceeding the production capacity of each source and the flow capacity of each switch. We show that deciding if such a valid configuration exists is polynomial in a tree.", "Keywords": "Computational complexity ; Graph algorithms ; Electrical network flow ; Dynamic programming", "DOI": "10.1016/j.ipl.2022.106249", "PubYear": 2022, "Volume": "176", "Issue": "", "JournalId": 5748, "JournalTitle": "Information Processing Letters", "ISSN": "0020-0190", "EISSN": "1872-6119", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "DAVID, University of Versailles-St Quentin, Paris-Saclay University, 45 avenue des États-Unis, 78035, Versailles, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DAVID, University of Versailles-St Quentin, Paris-Saclay University, 45 avenue des États-Unis, 78035, Versailles, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ENSIIE, 1 square de la Résistance, 91000, Evry, France;SAMOVAR, Telecom SudParis, 9 Rue Charles Fourier, 91000, <PERSON><PERSON><PERSON>, France;Corresponding author at: ENSIIE, 1 square de la Résistance, 91000, Evry, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LISN, CentraleSupelec, Paris-Saclay University, Rue Noetzlin, 91190, Gif-sur-Yvette, France"}], "References": [{"Title": "Optimisation of electrical network configuration: Complexity and algorithms for ring topologies", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "859", "Issue": "", "Page": "162", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 92653815, "Title": "Task-specific image summaries using semantic information and self-supervision", "Abstract": "<p>Large annotated datasets are needed for successful Deep Learning methodologies to achieve human-level performance. These needs restrict the impact of Deep Learning and build the necessity to create smaller and richer representative datasets that can offer a potential solution to this problem. In this paper, we propose task-specific image corpus summarization using semantic information and self-supervision. Our methodology makes use of GAN for the generation of features and leverages rotational invariance for employing self-supervision. All these objectives are facilitated on features from Resnet34. A summary can be obtained efficiently by using k -means clustering on the semantic embedding space and then selecting examples nearest to centroids. In comparison to end-to-end trained models, the proposed model does not require retraining to obtain summaries of different lengths. We also test our model by extensive qualitative and quantitative experiments.</p>", "Keywords": "Deep learning; Multimedia; Task-specific image summarization; Generative adversarial networks; k-means clustering", "DOI": "10.1007/s00500-021-06603-6", "PubYear": 2022, "Volume": "26", "Issue": "16", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Netaji Subhas University of Technology, New Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Technical University of Munich, Munich, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Institute of Information Technology and Management, New Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Math and Computer Science, Brandon University, Brandon, Canada; Research Centre for Interneural Computing, China Medical University, Taichung, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Western Norway University of Applied Sciences, Bergen, Norway"}], "References": []}, {"ArticleId": 92653824, "Title": "Mechanical and microstructural properties of additively manufactured Ti–6Al–4 V stents with CO2 laser postannealing treatment", "Abstract": "<p>Because of its excellent machining characteristics suited for fabricating complex and hollow three-dimensional components, additive-manufactured selective laser melting (SLM) technology has been increasingly applied in the production of medical implants. In this study, SLM combined with Ti–6Al–4 V powder was used to produce titanium alloy wires and vascular stents; various laser processing parameters were employed (laser average power and exposure time). A line-shaped CO<sub>2</sub> laser postannealing treatment was used to improve the ductility of the Ti–6Al–4 V wires and stents. A universal testing system and a field-emission scanning electron microscope were used to compare the tensile strengths of the Ti–6Al–4 V wires, the compressive strength of the vascular stents, and the microstructural properties under various SLM parameters. The experimental results revealed the Ti–6Al–4 V wires fabricated using a laser power and exposure time of 100 W and 70 μs, respectively, to have the highest tensile strength (489.09 ± 15.27 MPa). Microtensile tests showed that the strain of the Ti–6Al–4 V wires annealed with the optimal laser power of 5 W increased from 1.18 ± 0.07 to 1.5 ± 0.41%. The compressive strength of the horizontal (S-2) structured stents before and after laser annealing was significantly higher than that of the circular arc (S-1) structured stents at both time points. The compressive strength of laser-annealed S-1 and S-2 structured stents was greater than that of unannealed stents. Compared with the unannealed stents, the compressive strength of the 5 W laser-annealed S-1 and S-2 structured stents was 58.9% and 124.4% higher, and the compression of S-1 and S-2 was 11.5% and 13.5% higher, respectively.</p>", "Keywords": "Selective laser melting (SLM); Metal additive manufacturing; Ti–6Al–4 V powder; Line-shaped CO2 laser postannealing treatment; Ti–6Al–4 V wires and vascular stents", "DOI": "10.1007/s00170-021-08381-9", "PubYear": 2022, "Volume": "119", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taipei University of Technology, Taipei, Taiwan;Graduate Institute of Manufacturing Technology, National Taipei University of Technology, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "Ting<PERSON><PERSON> Hung", "Affiliation": "Graduate Institute of Manufacturing Technology, National Taipei University of Technology, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Taiwan Instrument Research Institute, National Applied Research Laboratories, Hsinchu, Taiwan"}], "References": [{"Title": "Prediction of lack-of-fusion porosity in laser powder-bed fusion considering boundary conditions and sensitivity to laser power absorption", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "1-2", "Page": "61", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Research on gradient additive remanufacturing of ultra-large hot forging die based on automatic wire arc additive manufacturing technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "7-8", "Page": "2243", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 92653826, "Title": "A framework of genetic algorithm-based CNN on multi-access edge computing for automated detection of COVID-19", "Abstract": "<p>This paper designs and develops a computational intelligence-based framework using convolutional neural network (CNN) and genetic algorithm (GA) to detect COVID-19 cases. The framework utilizes a multi-access edge computing technology such that end-user can access available resources as well the CNN on the cloud. Early detection of COVID-19 can improve treatment and mitigate transmission. During peaks of infection, hospitals worldwide have suffered from heavy patient loads, bed shortages, inadequate testing kits and short-staffing problems. Due to the time-consuming nature of the standard RT-PCR test, the lack of expert radiologists, and evaluation issues relating to poor quality images, patients with severe conditions are sometimes unable to receive timely treatment. It is thus recommended to incorporate computational intelligence methodologies, which provides highly accurate detection in a matter of minutes, alongside traditional testing as an emergency measure. CNN has achieved extraordinary performance in numerous computational intelligence tasks. However, finding a systematic, automatic and optimal set of hyperparameters for building an efficient CNN for complex tasks remains challenging. Moreover, due to advancement of technology, data are collected at sparse location and hence accumulation of data from such a diverse sparse location poses a challenge. In this article, we propose a framework of computational intelligence-based algorithm that utilize the recent 5G mobile technology of multi-access edge computing along with a new CNN-model for automatic COVID-19 detection using raw chest X-ray images. This algorithm suggests that anyone having a 5G device (e.g., 5G mobile phone) should be able to use the CNN-based automatic COVID-19 detection tool. As part of the proposed automated model, the model introduces a novel CNN structure with the genetic algorithm (GA) for hyperparameter tuning. One such combination of GA and CNN is new in the application of COVID-19 detection/classification. The experimental results show that the developed framework could classify COVID-19 X-ray images with 98.48% accuracy which is higher than any of the performances achieved by other studies.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2021.</p>", "Keywords": "CNN;COVID-19;Classification;Genetic Algorithm;Multi-access edge", "DOI": "10.1007/s11227-021-04222-4", "PubYear": 2022, "Volume": "78", "Issue": "7", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "College of Arts and Sciences, University of Maine at Presque Isle, Presque Isle, ME04769 USA."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computers and Information, Minia University, Minia, Egypt."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Imagine Consulting Services LLC, Dayton, NJ USA."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Simply Retrofits, Ontario, Canada."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology, Deakin University, Melbourne, Australia."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, College of Computer and Information Sciences, King Saud University, Riyadh, 11543 Saudi Arabia."}], "References": [{"Title": "Hyperparameter optimization in CNN for learning-centered emotion recognition for intelligent tutoring systems", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "10", "Page": "7593", "JournalTitle": "Soft Computing"}, {"Title": "Automatic detection of coronavirus disease (COVID-19) using X-ray images and deep convolutional neural networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "3", "Page": "1207", "JournalTitle": "Pattern Analysis and Applications"}]}, {"ArticleId": 92653828, "Title": "A novel web ranking algorithm based on pages multi-attribute", "Abstract": "<p>The size of the Internet is rapidly increasing. It has become a necessity to access information on the web correctly for a short time. For this reason, search engines have arisen out of meeting this need. In this study, we propose a ranking algorithm based on page multi-attribute (PMARank). The proposed algorithm uses a novel index calculation system that acts as a pre-rank process for web pages. In the ranking procedure, the featured meta-tag of a page and its contents were extracted to locate words as ranking features. The proposed web ranking algorithm has been compared with PageRank (PR) and Hyperlink-Induced Topic Search (HITS) algorithms. Experimental results show that the proposed ranking algorithm performs better than PR and HITS algorithms according to user clickstreams on the search results page.</p>", "Keywords": "Page extraction; Text mining; Web ranking algorithm; Word frequency; Meta-tag parse; Query analysis; Information retrieval", "DOI": "10.1007/s41870-021-00833-5", "PubYear": 2022, "Volume": "14", "Issue": "2", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Engineering Department, Gazi University, Ankara, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Gazi University, Ankara, Turkey"}], "References": [{"Title": "Gawk web search personalization using dynamic user profile", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "3", "Page": "1199", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Web URLs retrieval with least execution time using MPV clustering approach", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "3", "Page": "1211", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Efficiency measures for ranked pages by Markov Chain Principle", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "2", "Page": "1099", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Content and link-structure perspective of ranking webpages: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100397", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 92653850, "Title": "Modellierung einer Fernwirkaußenstation mit AutomationML", "Abstract": "<p>Zur Kommunikation zwischen einer zentralen Leistelle und weit entfernten Anlagenteilen (Außenstationen) wird sogenannte Fernwirktechnik eingesetzt. Ein weit verbreitetes Fernwirkprotokoll ist Dnp3. Fernwirkaußenstationen werden heute oft SPS-basiert realisiert. Bei Fernwirktechnik bestehen die gleichen Anforderungen an das Engineering wie bei zentralen automatisierungstechnischen Anlagen: Das Engineering muss möglichst effizient, daher ohne unnötigen Aufwand, erfolgen. In der Automatisierungstechnik hilft AutomationML als Datenaustauschformat, die Interoperabilität von Engineering-Werkzeugen zu erhöhen und hierdurch den Engineering-Aufwand zu reduzieren. Dieser Beitrag zeigt, wie eine Dnp3-Außenstation in AutomationML modelliert werden kann. Hierbei werden Modellierungsregeln und -vorgaben zunächst allgemeingültig vorgestellt, so dass diese möglichst einfach auf andere Protokolle der Fernwirktechnik adaptiert werden können.</p>", "Keywords": "AutomationML;Fernwirktechnik;Dnp3", "DOI": "10.17560/atp.v63i6-7.2548", "PubYear": 2021, "Volume": "63", "Issue": "6-7", "JournalId": 35495, "JournalTitle": "atp edition", "ISSN": "2190-4111", "EISSN": "2364-3137", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 92653864, "Title": "Semantic integration of Moroccan cultural heritage using CIDOC CRM: case of Drâa-Tafilalet zone", "Abstract": "This paper presents the approach adopted and the results obtained as a part of a project aiming at publishing the data of the Moroccan cultural heritage (CH) of the Drâa-Tafilalet zone. It highlights the complex relationship that exists between the different objects of tangible and intangible heritage and proposes and tests a semantic model based on the CIDOC CRM standard for the integration of data specific to the studied zone. The choice of the CIDOC CRM was natural because of its great flexibility and capacity for extension to take into account new cultural heritage objects specially designed for the region and which have so far remained ignored, even if they are declared world cultural heritage by UNESCO. Copyright © 2021 Inderscience Enterprises Ltd.", "Keywords": "CIDOC CRM; Cultural heritage; Drâa-Tafilalet; Ontology; Preservation; RDF; Semantic", "DOI": "10.1504/IJCC.2021.120393", "PubYear": 2021, "Volume": "10", "Issue": "5/6", "JournalId": 18415, "JournalTitle": "International Journal of Cloud Computing", "ISSN": "2043-9989", "EISSN": "2043-9997", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "LISAC Laboratory, Department of Computer Sciences, Faculty of Science Dhar-<PERSON><PERSON><PERSON>, <PERSON><PERSON>h University, Fez, 30000, Morocco"}, {"AuthorId": 2, "Name": "Badrad<PERSON>", "Affiliation": "Informatics and Applications Laboratory, Department of Computer Science, Science Faculty of Meknes, Moulay Ismail University, Meknes, 50070, Morocco"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "LISAC Laboratory, Department of Computer Sciences, Faculty of Science Dhar-<PERSON><PERSON><PERSON>, <PERSON><PERSON>h University, Fez, 30000, Morocco"}], "References": []}, {"ArticleId": 92653965, "Title": "Language Design as Information Renormalization", "Abstract": "<p>Here we consider some well-known facts in syntax from a physics perspective, allowing us to establish analogies between both fields with many consequences. Mainly, we observe that the operation MERGE, put forward by <PERSON><PERSON><PERSON> (in: Evolution and Revolution in Linguistic Theory, Essays in honor of <PERSON>, eds. <PERSON> and <PERSON>, 1995), can be interpreted as a physical information coarse-graining. Thus, MERGE in linguistics entails information renormalization in physics, according to different time scales. We make this point mathematically formal in terms of statistical language models. In this setting, MERGE amounts to a probability tensor implementing a coarse-graining, akin to a probabilistic context-free grammar. The probability vectors of meaningful sentences are given by stochastic tensor networks (TN) built from diagonal tensors and which are mostly loop-free, such as Tree Tensor Networks and Matrix Product States, thus being computationally very efficient to manipulate. We show that this implies the polynomially-decaying (long-range) correlations experimentally observed in language, and also provides arguments in favour of certain types of neural networks for language processing. Moreover, we show how to obtain such language models from quantum states that can be efficiently prepared on a quantum computer, and use this to find bounds on the perplexity of the probability distribution of words in a sentence. Implications of our results are discussed across several ambits.</p>", "Keywords": "Syntax; Merge; Renormalization; Tensor Network", "DOI": "10.1007/s42979-021-01002-y", "PubYear": 2022, "Volume": "3", "Issue": "2", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departament de Filologia Espanyola, Facultat de Filosofia i Lletres, Universitat Autònoma de Barcelona, Bellaterra, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Donostia International Physics Center, Paseo Manuel de Lardizabal 4, San Sebastián, Spain;Ikerbasque Foundation for Science, Maria Diaz de Haro 3, Bilbao, Spain;Multiverse Computing, Paseo de Miramón 170, San Sebastián, Spain"}], "References": []}, {"ArticleId": 92653977, "Title": "PRISED tangle: a privacy-aware framework for smart healthcare data sharing using IOTA tangle", "Abstract": "Healthcare has evolved significantly in recent years primarily due to the advancements in and increasing adoption of technology in healthcare processes such as data collection, storage, diagnostics, and treatment. The emergence of the industrial internet of things (IIoT) has further evolved e-Health by facilitating the development of connected healthcare systems which can significantly improve data connectivity, visibility, and interoperability leading to improved quality of service delivered to patients. However, such technological advancements come with their perils—there are growing concerns with regards to the security and privacy of healthcare data especially when collected, shared, and processed using cutting-edge connected sensor devices affecting the adoption of next-generation e-healthcare systems. In particular, during the front-end and back-end data transfer in health information exchange (HIE) there exist a security risk in term of confidentiality, integrity, authentication and access control of the data due to the limited capabilities of IoT devices involved. In this paper, we investigate the use of distributed ledger technologies (DLT) to address such security concerns for emerging healthcare systems. In particular, we use masked authenticated messaging (MAM) over the Tangle to achieve secure data sharing within a healthcare system and provide a proof-of-concept of applying the proposed approach for securing healthcare data in a connected IIoT environment. Further, we have performed the evaluation and analysis of data communication against the metrics of encryption and efficiency in transaction time.", "Keywords": "Digital healthcare; Distributed ledger technologies; Industrial internet of things; IOTA; Tangle; Masked authenticated messaging", "DOI": "10.1007/s40747-021-00610-8", "PubYear": 2023, "Volume": "9", "Issue": "3", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and IT, NED University of Engineering and Technology, Karachi, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Digital Technology, Birmingham City University, Birmingham, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and IT, NED University of Engineering and Technology, Karachi, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Engineering, IT and Environment at Charles Darwin University, Darwin, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Khalifa University, Abu Dhabi, UAE"}], "References": []}, {"ArticleId": 92653979, "Title": "Perangkat Lunak untuk Kontrol dan <PERSON>sian QR Code pada Robot Mobil di Prototipe Pabrik Garmen", "Abstract": "<p>FTI UK Petra akan membangun pabrik garmen mini yang membutuhkan robot otonom. Robot otonom ini akan melakukan tugas material handling dalam pabrik tersebut. <PERSON><PERSON> sebab itu, penelitian ini bertujuan untuk merancang perangkat lunak untuk mendukung tugas tersebut. Adapun robot otonom ini tergolong sebagai AMR (Autonomous Mobile Robot) karena dapat bergerak bebas pada lingkungan tanpa memerlukan bantuan garis atau tanda lainnya. Robot ini dilengkapi dengan sensor LIDAR untuk mendeteksi keadaan lingkungan sekitarnya secara 360 derajat. Dengan adanya sensor ini robot dapat melakukan pemetaan terhadap pabrik garmen dengan memanfaatkan metode SLAM. Robot juga dapat melakukan navigasi secara otonom dengan memanfaatkan pembacaan Sensor LIDAR dan algoritma DWA. Dari penelitian yang telah dilakukan, penulis menemukan bahwa perangkat lunak yang telah didesain mampu melakukan navigasi otonom dengan waktu yang kurang lebih sama dengan navigasi secara teleoperasi dan robot mampu mendeteksi QR Code pada jarak 10-20 cm menggunakan kamera. Dengan demikian, penelitian ini telah berhasil menghasilkan perangkat lunak untuk robot otonom yang akan digunakan pada pabrik garmen mini.</p>", "Keywords": "QR Code;Material Handling;Autonomous Mobile Robot;Navigasi", "DOI": "10.25077/TEKNOSI.v7i3.2021.164-171", "PubYear": 2021, "Volume": "7", "Issue": "3", "JournalId": 32016, "JournalTitle": "Jurnal Teknologi dan Sistem Informasi", "ISSN": "2460-3465", "EISSN": "2476-8812", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Program Studi Teknik Elektro, Universitas Kristen Petra"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Program Studi Teknik Elektro, Universitas Kristen Petra"}], "References": []}, {"ArticleId": 92654131, "Title": "Fast forest fire smoke detection using MVMNet", "Abstract": "Forest fires are a huge ecological hazard, and smoke is an early characteristic of forest fires. Smoke is present only in a tiny region in images that are captured in the early stages of smoke occurrence or when the smoke is far from the camera. Furthermore, smoke dispersal is uneven, and the background environment is complicated and changing, thereby leading to inconspicuous pixel-based features that complicate smoke detection. In this paper, we propose a detection method called multioriented detection based on a value conversion-attention mechanism module and Mixed-NMS (MVMNet). First, a multioriented detection method is proposed. In contrast to traditional detection techniques, this method includes an angle parameter in the data loading process and calculates the target’s rotation angle using the classification prediction method, which has reference significance for determining the direction of the fire source. Then, to address the issue of inconsistent image input size while preserving more feature information, Softpool-spatial pyramid pooling (Soft-SPP) is proposed. Next, we construct a value conversion-attention mechanism module (VAM) based on the joint weighting strategy in the horizontal and vertical directions, which can specifically extract the colour and texture of the smoke. Ultimately, the DIoU-NMS and Skew-NMS hybrid nonmaximum suppression methods are employed to address the issues of smoke false detection and missed detection. Experiments are conducted using the homemade forest fire multioriented detection dataset, and the results demonstrate that compared to the traditional detection method, our model’s mAP reaches 78.92%, mAP <sup>50</sup> reaches 88.05%, and FPS reaches 122.", "Keywords": "Forest fire smoke detection ; Multioriented ; Value conversion-attention mechanism module ; Softpool-spatial pyramid pooling ; Mixed-NMS", "DOI": "10.1016/j.knosys.2022.108219", "PubYear": 2022, "Volume": "241", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer & Information Engineering, Central South University of Forestry and Technology, Changsha 410004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer & Information Engineering, Central South University of Forestry and Technology, Changsha 410004, China;@qq.com"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer & Information Engineering, Central South University of Forestry and Technology, Changsha 410004, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer & Information Engineering, Central South University of Forestry and Technology, Changsha 410004, China;@qq.com"}, {"AuthorId": 5, "Name": "Weiwei Cai", "Affiliation": "College of Computer & Information Engineering, Central South University of Forestry and Technology, Changsha 410004, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer & Information Engineering, Central South University of Forestry and Technology, Changsha 410004, China;@qq.com"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Plant Protection Research Institute, Henan Academy of Agricultural Sciences, Changsha 410125, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil, Architectural and Environmental Engineering, University of Missouri-Rolla, Rolla, MO 65401, USA"}], "References": [{"Title": "R4 Det: Refined single-stage detector with feature recursion and refinement for rotating object detection in aerial images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "104036", "JournalTitle": "Image and Vision Computing"}, {"Title": "Efficient attention based deep fusion CNN for smoke detection in fog environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "434", "Issue": "", "Page": "224", "JournalTitle": "Neurocomputing"}, {"Title": "DeepSmoke: Deep learning model for smoke detection and segmentation in outdoor environments", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "115125", "JournalTitle": "Expert Systems with Applications"}]}]