[{"ArticleId": *********, "Title": "Evaluation of the Effect of Operational Scenarios on a Train Driver Performance", "Abstract": "<p>This paper aims to numerically evaluate the effect of existing actions to assist a train driver in various operational situations, as well as select the indicators of error-free operation ending on the form of activity and other factors. The effect of each individual examined factor on the resulting indicator was evaluated, operational situations were examined taking into account the proportion of times when the action has a positive effect. A few practical cases were examined, whereas the method can be used.</p>", "Keywords": "", "DOI": "10.7251/JIT2302072S", "PubYear": 2023, "Volume": "25", "Issue": "2", "JournalId": 41532, "JournalTitle": "JITA - Journal of Information Technology and Applications (Banja Luka) - APEIRON", "ISSN": "2232-9625", "EISSN": "2233-0194", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "UniTrans: Unified Parameter-Efficient Transfer Learning and Multimodal Alignment for Large Multimodal Foundation Model", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2025.059745", "PubYear": 2025, "Volume": "83", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Jiakang Sun", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143451186, "Title": "Enhancing robustness of object detection: <PERSON><PERSON><PERSON> model connected with deep learning", "Abstract": "With the widespread application of deep learning, object detection has become an increasingly important aspect of this field. As research on neural networks continues to advance, many effective models have emerged, improving both the accuracy and the training speed of these tasks. However, enhancing the robustness of models remains a challenging task in deep learning. Most of the suggested methods have been tailored to enhance robustness for specific models and tasks, leaving few options applicable to universal models and datasets. We propose an artificial visual system derived from the <PERSON><PERSON><PERSON> model. This model is a widely recognized neurobiological theory that explains how cortical neurons achieve selective responses to visual stimuli in terms of their orientation. However, it primarily elucidates the organization and facilitation of visual information processing within the visual cortex. To simulate simple and complex cells found in the visual cortex, we have employed the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (MP) neuron model. By doing so, we have constructed an artificial visual system (AVS) focused on object detection and noise reduction. Our study significantly improves the robustness of the model on a universal model and dataset through a series of experiments. Through these experiments, we demonstrate that our scheme exhibits substantial advantages in noise resistance compared to a single neural network. These results not only validate the effectiveness of our scheme but also indirectly support the rationale behind the <PERSON><PERSON><PERSON> model and highlight its biological resemblance to the natural vision system.", "Keywords": "", "DOI": "10.1016/j.knosys.2025.112984", "PubYear": 2025, "Volume": "311", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Electrical Engineering and Computer Science, Graduate School of Natural Science & Technology, Kanazawa University, Kanazawa, 920-1192, Ishikawa, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Division of Electrical Engineering and Computer Science, Graduate School of Natural Science & Technology, Kanazawa University, Kanazawa, 920-1192, Ishikawa, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical, Information and Communication Engineering, Kanazawa University, Kanazawa, 920-1192, Ishikawa, Japan;Corresponding author"}], "References": []}, {"ArticleId": 143451249, "Title": "Panoptic Segmentation for Indoor Environments using MaskDINO: An Experiment on the Impact of Contrast", "Abstract": "<p>Robot perception involves recognizing the surrounding environment, particularly in indoor spaces like kitchens, classrooms, and dining areas. This recognition is crucial for tasks such as object identification. Objects in indoor environments can be categorized into \"things,\" with fixed and countable shapes (e.g., tables, chairs), and \"stuff,\" which lack a fixed shape and cannot be counted (e.g., sky, walls). Object detection and instance segmentation methods excel in identifying \"things,\" with instance segmentation providing more detailed representations than object detection. However, semantic segmentation can identify both \"things\" and \"stuff\" but lacks segmentation at the object level. Panoptic segmentation, a fusion of both methods, offers comprehensive object and stuff identification and object-level segmentation. Considerations need to be made regarding the variabilities of room conditions in contrast to implementing panoptic segmentation indoors. High or low contrast in the room potentially reduces the clarity of the shape of an object, thus affecting the segmentation results of that object. We experimented with how contrast varieties impact the panoptic segmentation performance using the MaskDINO model, the first on the panoptic quality (PQ) leaderboard. We then improved the model generalization on the various contrasts by re-optimizing it using a contrast-augmented dataset.</p>", "Keywords": "", "DOI": "10.5565/rev/elcvia.1861", "PubYear": 2025, "Volume": "24", "Issue": "1", "JournalId": 19976, "JournalTitle": "ELCVIA Electronic Letters on Computer Vision and Image Analysis", "ISSN": "", "EISSN": "1577-5097", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> -", "Affiliation": ""}], "References": []}, {"ArticleId": 143451463, "Title": "Disentangled Sparse Graph Attention Networks with Multi-Intent Fusion for Session-based Recommendation", "Abstract": "Session-based Recommendation has garnered considerable interests recently due to providing personalized recommendations based on anonymous behavior sequences. Most of existing models learn user preferences from a holistic perspective without delving into the key factors that drive user–item interactions. This results in an insufficient capture of user intent, as they rely solely on session data for predictions. Additionally, these models are more susceptible to the negative impact of noise on account of the limited short-term interactions. To tackle these problems, we propose a novel model called D isentangled S parse G raph Attention Networks with M ulti- I ntent F usion for Session-based Recommendation to learn item embeddings from factor level and model user intent for better inferring the user preferences. Specifically, we map item embeddings into multiple factors using disentanglement techniques and utilize gated graph neural network to learn the embeddings based on the item adjacent similarity matrix calculated for each factor. An innovative position information generation module is designed to encode the order of items. Subsequently, we model user intent from three perspectives and apply intent-aware fusion module to integrate them into a unified intent representation for incorporating intent information into the current session. Sparse attention networks are employed to denoise and extract the intent pattern of the current session. Furthermore, sessions exhibiting similar intent pattern are identified to augment the representation of the current session. Extensive experiments on five datasets indicate that our model outperforms state-of-the-art methods consistently.", "Keywords": "", "DOI": "10.1016/j.knosys.2025.113082", "PubYear": 2025, "Volume": "311", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Yunnan Key Laboratory of Intelligent Systems and Computing, Kunming, Yunnan, China;School of Software Engineering, Xi’an Jiaotong University, Xi’an, 710049, Shaanxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, 710049, Shaanxi, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Yunnan Key Laboratory of Intelligent Systems and Computing, Kunming, Yunnan, China"}, {"AuthorId": 4, "Name": "Ansong Li", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, 710049, Shaanxi, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, 710049, Shaanxi, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, 710049, Shaanxi, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, Xi’an Jiaotong University, Xi’an, 710049, Shaanxi, China"}], "References": [{"Title": "Time enhanced graph neural networks for session-based recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109204", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Session-based recommendation with time-aware neural attention network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "118395", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Transition Information Enhanced Disentangled Graph Neural Networks for session-based recommendation", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Li", "PubYear": 2022, "Volume": "210", "Issue": "", "Page": "118336", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Position-aware graph neural network for session-based recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "262", "Issue": "", "Page": "110201", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Self-supervised Dual Hypergraph learning with Intent Disentanglement for session-based recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "270", "Issue": "", "Page": "110528", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Semantic-enhanced Contrastive Learning for Session-based Recommendation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "111001", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Dual-channel representation consistent recommender for session-based new item recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> Li", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123681", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 143451488, "Title": "Kinetic and macroscopic modelling for dense gas flow simulations", "Abstract": "This study delves into the complexities of modelling dense gases near thermodynamic critical points, where conventional gas dynamic assumptions prove inadequate. Within this unique regime, non-linear waves exhibit behaviours divergent from classical phenomena, like expansion shocks which remain consistent with entropy conditions. Accurately capturing these phenomena mandates sophisticated equation of state (EoS) that surpasses the ideal gas assumptions, presenting challenges for numerical simulations. In this paper, we propose a simple modification to the Boltzmann equation (with the BGK framework), which, upon taking moments, leads to Euler equations for dense gas flows. We consider van der Waals EoS. Further, we develop a three-velocity model based Kinetic Flux Difference Splitting (KFDS) scheme for the Euler system, with adaptable diffusion coefficients suitable to capture compressible flow phenomena specific to ideal and dense gases. This innovative approach diverges from traditional algorithms, which are tailored for ideal gas EoS and struggle to accommodate the inherent variations. A comparative analysis with macroscopic efficient central solvers designed to be independent of the eigen-structure, such as MOVERS+ and RICCA, is conducted to validate the results against benchmark tests from the data in the literature. It is important to note that the kinetic schemes also possess the advantage of being independent of the eigen-structure, a feature that distinguishes them from traditional Riemann solvers. This effort significantly enhances computational modelling capabilities and fosters deeper insights into the behaviour of dense gases. The proposed advancements enhance numerical methods tailored for real gas EoS simulations by ensuring precise capture of grid-aligned steady discontinuities and effectively mitigating numerical diffusion across these discontinuities in inviscid compressible flows.", "Keywords": "", "DOI": "10.1016/j.compfluid.2024.106539", "PubYear": 2025, "Volume": "290", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hindustan Aeronautics Limited, Bangalore, India;Department of Aerospace Engineering, Indian Institute of Science, Bangalore, India;Corresponding author at: Department of Aerospace Engineering, Indian Institute of Science, Bangalore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, Indian Institute of Science, Bangalore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, Indian Institute of Science, Bangalore, India;Department of Mechanical Engineering, University of South Africa (UNISA), Johannesburg, South Africa;Visiting faculty at UNISA; permanent affiliation: Indian Institute of Science, Bangalore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, University of South Africa (UNISA), Johannesburg, South Africa"}, {"AuthorId": 5, "Name": "Sekhar G.N.", "Affiliation": "Department of Mathematics, BMS College of Engineering, Bangalore, India"}], "References": []}, {"ArticleId": 143451540, "Title": "Fault diagnosis using liquid state machine with spiking-timing-dependent plasticity learning rule", "Abstract": "Spiking Neural Networks (SNNs) are highly efficient in processing temporal and spatiotemporal data due to their event-driven nature and biological plausibility. Liquid State Machines (LSMs), a variant of SNNs, excel at capturing complex temporal dependencies through their dynamic reservoir of spiking neurons. While LSMs have been explored in other domains, their application to fault diagnosis has been limited due to challenges in training the network effectively and extracting meaningful features from high-dimensional, noisy sensor data. In this paper, we propose a novel fault diagnosis method based on LSMs, enhanced by integrating the Spike-Timing-Dependent Plasticity (STDP) learning rule. This integration enables adaptive learning and fine-tuning of temporal features, addressing the challenges of dynamic and complex time-series data. To overcome the difficulty of weight optimization between the liquid and output layers, we employ a Support Vector Machine (SVM) for direct classification in the high-dimensional state space of the liquid layer. The proposed method demonstrates exceptional diagnostic performance across three widely used benchmark datasets. On the CWRU and MFPT datasets, the model achieved accuracies of 99.58% and 100.00%, illustrating its effectiveness in identifying well-defined fault categories. Furthermore, on the more challenging PU dataset, which involved detecting previously unseen fault categories, the model achieved an average of 81.25%. Ablation studies confirm the significant contributions of the integrated STDP and LSM components to model’s performance. These findings emphasize the generalization, adaptability, and practical utility of the proposed approach in addressing diverse fault diagnosis challenges in industrial systems.", "Keywords": "", "DOI": "10.1016/j.eswa.2025.126736", "PubYear": 2025, "Volume": "271", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Wan", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191 China;Ningbo Institute of Technology, Beihang University, Ningbo 315800 China;0000-0002-8102-3436"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, Beijing 100191 China;Ningbo Institute of Technology, Beihang University, Ningbo 315800 China;Corresponding author"}], "References": [{"Title": "A spiking neural network-based approach to bearing fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "714", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Reliability prediction-based improved dynamic weight particle swarm optimization and back propagation neural network in engineering systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114952", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Hardware-aware liquid state machine generation for 2D/3D Network-on-Chip platforms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "102429", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "A Time Series Transformer based method for the rotating machinery fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "494", "Issue": "", "Page": "379", "JournalTitle": "Neurocomputing"}, {"Title": "Improved spiking neural network for intershaft bearing fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "", "Page": "208", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A reliability estimation method based on signal feature extraction and artificial neural network supported Wiener process with random effects", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "110044", "JournalTitle": "Applied Soft Computing"}, {"Title": "A transformer-based approach for novel fault detection and fault classification/diagnosis in manufacturing: A rotary system application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "67", "Issue": "", "Page": "439", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Reliability assessment of manufacturing systems: A comprehensive overview, challenges and opportunities", "Authors": "<PERSON>; <PERSON><PERSON>-<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "72", "Issue": "", "Page": "38", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Harnessing attention mechanisms in a comprehensive deep learning approach for induction motor fault diagnosis using raw electrical signals", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "129", "Issue": "", "Page": "107643", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Wavelet-driven differentiable architecture search for planetary gear fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "74", "Issue": "", "Page": "587", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Prognostics and health management for predictive maintenance: A review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "75", "Issue": "", "Page": "78", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A fault diagnosis method for hydraulic system based on multi-branch neural networks", "Authors": "Huizhou Liu; Shibo Yan; Mengxing Huang", "PubYear": 2024, "Volume": "137", "Issue": "", "Page": "109188", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A digital twin system for centrifugal pump fault diagnosis driven by transfer learning based on graph convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "163", "Issue": "", "Page": "104155", "JournalTitle": "Computers in Industry"}, {"Title": "MRCFN: A multi-sensor residual convolutional fusion network for intelligent fault diagnosis of bearings in noisy and small sample scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "259", "Issue": "", "Page": "125214", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 143451653, "Title": "The role of Internet marketing in improving enterprise economic benefits under the background of big data and Internet of Things", "Abstract": "<p>This study aims to explore how big data and the Internet of Things can improve the economic benefits of enterprises in online marketing. We first introduced the concept and development trend of big data, Internet of Things, and Internet marketing and then collected a large number of empirical data through the design and implementation of questionnaire survey. Our data analysis results show that the level of use of big data and the Internet of Things has a significant positive impact on business sales. However, there are also some problems and challenges in the implementation of big data and the Internet of Things, such as resource and technology limitations, and legal and ethical issues such as data security and privacy protection. Therefore, we suggest that enterprises should reasonably plan the use of big data and the Internet of Things according to their own resources and capabilities, while constantly upgrading their own technical level, focusing on data security and privacy protection, and tracking the latest technology and market trends. In addition, the government and industry organizations should also formulate and improve relevant policies and standards to support the development of big data and the Internet of Things and protect the interests of consumers. In general, big data and the Internet of Things provide new opportunities and challenges for enterprise online marketing, which need further research and discussion.</p>", "Keywords": "", "DOI": "10.1177/14727978241296747", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Jiaozuo University, Jiaozuo, China;Graduate School, University of the East, Manila, Philippines"}], "References": []}, {"ArticleId": 143451654, "Title": "Research progress of overhead conductors with carbon fiber composite core", "Abstract": "<p>Carbon fiber composite core overhead conductor has a series of excellent performance, such as high strength, light weight, corrosion resistance, high temperature resistance, small sag, and so on. It is the preferred product for large span, large capacity transmission and capacity improvement lines. However, due to the limitations of structure and material properties, this conductor has some technical problems, such as poor bending performance and lack of effective means to diagnose defects in the core. The development of carbon fiber composite core overhead conductors is introduced, and the structures and properties of different types of conductors are analyzed. It is pointed out that using multi-strand core instead of single core is an effective means to improve the bending performance of the conductor. By implanting optical fiber in the composite core and using the optical fiber as a sensor, the internal defects in the core can be diagnosed. In addition, from the perspective of environmental protection, the multi-strand thermoplastic composite core conductor is expected to achieve great development in this field in the future.</p>", "Keywords": "", "DOI": "10.1177/14727978241295538", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Hebei Energy Technology Service Co., Ltd., Shijiazhuang, China;Research Institute, State Grid Hebei Electric Power Co., Ltd., Shijiazhuang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Hebei Energy Technology Service Co., Ltd., Shijiazhuang, China;Research Institute, State Grid Hebei Electric Power Co., Ltd., Shijiazhuang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Hebei Energy Technology Service Co., Ltd., Shijiazhuang, China;Research Institute, State Grid Hebei Electric Power Co., Ltd., Shijiazhuang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Hebei Energy Technology Service Co., Ltd., Shijiazhuang, China;Research Institute, State Grid Hebei Electric Power Co., Ltd., Shijiazhuang, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Hebei Electric Power Co., Ltd., Shijiazhuang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>ying Sun", "Affiliation": "State Grid Hebei Energy Technology Service Co., Ltd., Shijiazhuang, China;Research Institute, State Grid Hebei Electric Power Co., Ltd., Shijiazhuang, China"}], "References": []}, {"ArticleId": 143451657, "Title": "Design and parameters optimization for solid organic fertilizer loading and spreading machines based on the discrete element method", "Abstract": "<p>The application of solid organic fertilizer is primarily a manual process, leading to inefficiencies and substantial labor demands. The complexity of the task, combined with the market’s lack of machinery capable of both loading and spreading, presents significant challenges. This study introduced the design of an integrated machine that facilitates the loading, transporting, and spreading of solid organic fertilizer. Utilizing the discrete element method, we simulated these processes to refine the machine’s efficiency. EDEM software was used for simulation calculation. The goal was to identify optimal parameters for the machine’s loading and spreading functions. Factors such as forward speed, spreading roller speed, and spreading height were evaluated for their impact on spreading uniformity, using Design-Expert software for experimental design. Through the Box–Behnken experimental approach, a second-order regression model was developed and refined. This optimization process identified the most effective combination of operational parameters: a forward speed of 0.42 meters per second, a spreading roller speed of 216 revolutions per minute, and a spreading height of 545 millimeters. These specifications were intended for the integrated machine, enhancing the efficiency of organic fertilizer loading, transport, and spreading activities.</p>", "Keywords": "", "DOI": "10.1177/14727978241299185", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "Xu<PERSON> Liang", "Affiliation": "College of Mechanical and Electrical Engineering, Hebei Agricultural University, Baoding, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Hebei Agricultural University, Baoding, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Hebei Agricultural University, Baoding, China"}], "References": []}, {"ArticleId": 143451660, "Title": "Empirical Bayesian estimation of parameters in step-stress accelerated life testing under exponential distribution with constant censoring", "Abstract": "<p>This study explores the Parametric Empirical Bayesian (PEB) estimation of average lifetime and acceleration factor parameters under the framework of exponential distribution with constant censoring, specifically when the hyperparameters of the prior distribution are completely unknown. Employing a weighted squared loss function, the performance of PEB estimations is compared against that of Maximum Likelihood Estimations (MLE) through numerical simulations. The results demonstrate the superiority of PEB estimations over MLE, particularly notable in scenarios involving small sample sizes, where it exhibits robust estimation capabilities. This paper extends the existing methodology by providing a comprehensive examination of the advantages and potential applications of empirical Bayesian approaches in the context of step-stress accelerated life testing, thereby offering valuable insights for researchers and practitioners in the field of reliability engineering.</p>", "Keywords": "", "DOI": "10.1177/14727978241295537", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Teacher Education, Hezhou University, Hezhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Teacher Education, Hezhou University, Hezhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> He", "Affiliation": "School of Teacher Education, Hezhou University, Hezhou, China"}], "References": []}, {"ArticleId": 143451661, "Title": "Teaching optimization of virtual reality and augmented reality technology in vocational education", "Abstract": "<p>With the increasing progress of science and technology, the field of education is actively exploring new teaching techniques to optimize the learning experience. In this context, this research focuses on exploring the applications and benefits of virtual reality (VR) and augmented reality (AR) technologies in vocational education. Through comparative analysis, the study aims to evaluate the teaching effects of these emerging technologies and traditional teaching methods in vocational education, so as to provide an empirical basis for educators and policymakers. The findings suggest that VR and AR provide an immersive learning environment that significantly increases student engagement and motivation. In addition, compared with traditional teaching methods, these techniques show clear advantages in some specific areas, especially those requiring simulation of practical operations. However, the best teaching strategy may involve using these techniques in combination with traditional methods to take full advantage of each. Overall, this study provides useful insights into how VR and AR technologies can be effectively utilized in the field of vocational education, highlighting the importance of an integrated teaching approach to meet diverse learning needs and achieve instructional optimization.</p>", "Keywords": "", "DOI": "10.1177/14727978241297013", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, Guangzhou Kangda Vocational Technical College, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Lanfang Technology (Guangzhou) Co., Ltd, Guangzhou, China"}], "References": [{"Title": "Application of 5g multimedia technology in Vocational Education Management based on BGP anomaly detection", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 143451663, "Title": "Design of electronic slogging control system for double tricot machine", "Abstract": "<p>The work aims to construct a complete electronic slogging system in order to enrich the characteristics of pattern and get large slogging movement distance. Herein, first, the knitting principle of double tricot machine is studied, and the conclusion is drawn that the looping process is composed of the swing of guide bar and the slogging of guide bar. Second, taking the RD4N tricot machine as the research object, the minimum overlap angle of 25° and the minimum underlap angle of 80° are obtained, which occur on the GB1 and GB4 bar, respectively. Third, discussions are carried out over the scheme and principles of the slogging system, the controller, servo motor, etc., are analyzed and designed, an electronic slogging execution program is drafted, and the electronic cam curve adopts the non-dwell corrected trapezoidal acceleration motion curve, which completely meets the requirements of production. The actual test proves the smooth operation, precise slogging, and fast response of the control systems.</p>", "Keywords": "", "DOI": "10.1177/14727978241292896", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "East Pudong Branch, Shanghai Open University, Shanghai, China"}], "References": [{"Title": "Development of a servo-hydraulic system with a self-tuning fuzzy PID controller to simulate injection molding process", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "1217", "JournalTitle": "Microsystem Technologies"}]}, {"ArticleId": *********, "Title": "Framework of Digital Transformation Readiness at a Major Company", "Abstract": "<p>Modern business, based on modern technologies, require strategy initiative that incorporates different forms of digital technologies across all areas of the organisation, with aim to identify ways to improve operational efficiency and faster comercialisation of products or services. Digital transformation is becoming important strategy for each organisation to meet these objectives. This study presents an in-depth evaluation of a company’s readiness for digital transformation. Employing a methodical approach that includes extensive stakeholder interviews and a meticulously designed digital readiness survey, this research identifies critical areas where technological enhancement and strategic digital deployment could significantly improve organizational efficiency and competitiveness on a global scale. The paper outlines a framework tailored to guide enterprises through their digital transformation journeys effectively, facilitating better operational efficiencies and sustainable growth.</p>", "Keywords": "", "DOI": "10.7251/JIT2401047R", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 41532, "JournalTitle": "JITA - Journal of Information Technology and Applications (Banja Luka) - APEIRON", "ISSN": "2232-9625", "EISSN": "2233-0194", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143451696, "Title": "Using big data to analyze the mental health status of college students", "Abstract": "<p>In recent years, with the increase of the number of college students and the change of social pressure, the problem of students’ mental health has gradually been widely concerned by the society. The purpose of this study is to systematically evaluate and analyze the mental health status of college students and explore the core factors that affect their mental health. Using big data technology and SCL-90 scale, this study assessed the mental health of a sample of students within a certain range. The results show that academic pressure, interpersonal relationship, living habits, and personal experience are the main influencing factors of students’ mental health. Based on these findings, the study provides a series of specific mental health intervention recommendations and strategies for colleges and universities. This study not only provides targeted mental health strategy suggestions for colleges and universities but also provides a new research perspective and method for mental health research.</p>", "Keywords": "", "DOI": "10.1177/14727978241296987", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Overseas Education, Jiangsu University of Science and Technology, Zhenjiang, China"}], "References": [{"Title": "Assessment of college students’ mental health status based on temporal perception and hybrid clustering algorithm under the impact of public health events", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1586", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": *********, "Title": "Rewriting for Symmetric Monoidal Categories with Commutative (Co)Monoid Structure", "Abstract": "<p>String diagrams are pictorial representations for morphisms of symmetric monoidal categories. They constitute an intuitive and expressive graphical syntax, which has found application in a very diverse range of fields including concurrency theory, quantum computing, control theory, machine learning, linguistics, and digital circuits. Rewriting theory for string diagrams relies on a combinatorial interpretation as double-pushout rewriting of certain hypergraphs. As previously studied, there is a `tension' in this interpretation: in order to make it sound and complete, we either need to add structure on string diagrams (in particular, Frobenius algebra structure) or pose restrictions on double-pushout rewriting (resulting in 'convex' rewriting). From the string diagram viewpoint, imposing a full Frobenius structure may not always be natural or desirable in applications, which motivates our study of a weaker requirement: commutative monoid structure. In this work we characterise string diagram rewriting modulo commutative monoid equations, via a sound and complete interpretation in a suitable notion of double-pushout rewriting of hypergraphs.</p>", "Keywords": "", "DOI": "10.46298/lmcs-21(1:12)2025", "PubYear": 2025, "Volume": "21, Issue 1", "Issue": "", "JournalId": 10075, "JournalTitle": "Logical Methods in Computer Science", "ISSN": "", "EISSN": "1860-5974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143451827, "Title": "Cyberbullying Detection using Machine Learning Models", "Abstract": "<p>Cyberbullying is a significant and increasing problem in online communities, and the detection system should also be effective in addressing it. The research presents an in-depth comparison of image classification systems such as Logistic Regression, Naive Bayes, XGBoost, Decision Tree, and Random Forest in the detection of cyberbullying. The evaluation of the five machine learning algorithms with respect to: Logistic Regression, Naive Bayes, XGBoost, Decision Tree, and Random Forest, will be within the framework of large-scale dataset collection about cyberbullying. This will be done based on the evaluation of the metadata file using accuracy, precision, recall, and F1 score, which represent the overall performance level. The results presented help determine the weaknesses and strengths of the individual algorithms and narrow the search for the right approach to cyberbullying detection. Moreover, best-performing algorithms were integrated into a Stream -lit- based front end for real-time prediction and display of the capabilities of the model. This study contributes significantly to the research on the development of new machine-learning solutions for cyberbullying detection and provides a solid evaluation of various classification strategies that are ultimately well-suited for effective detection systems in the future.</p>", "Keywords": "", "DOI": "10.36548/jitdw.2024.4.004", "PubYear": 2024, "Volume": "6", "Issue": "4", "JournalId": 71072, "JournalTitle": "Journal of Information Technology and Digital World", "ISSN": "", "EISSN": "2582-418X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Dhanya K.R.", "Affiliation": ""}, {"AuthorId": 3, "Name": "Karpagam C", "Affiliation": ""}], "References": [{"Title": "Natural language processing and machine learning based cyberbullying detection for Bangla and Romanized Bangla texts", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "20", "Issue": "1", "Page": "89", "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)"}, {"Title": "A study of machine learning-based models for detection, control, and mitigation of cyberbullying in online social media", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "6", "Page": "1409", "JournalTitle": "International Journal of Information Security"}, {"Title": "Cyberbullying Detection using Machine Learning and Deep Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "10", "Page": "424", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "A review of multimodal-based emotion recognition techniques for cyberbullying detection in online social media platforms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "35", "Page": "21923", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 143451854, "Title": "Ultra-fast computation of fractal dimension for RGB images", "Abstract": "<p>The fractal dimension (FD) is a quantitative parameter widely used to analyze digital images in many application fields such as image segmentation, feature extraction, object recognition, texture analysis, and image compression and denoising, among many others. A variety of algorithms have been previously proposed for estimating the FD, however most of them are limited to binary or gray-scale images only. In recent years, several authors have proposed algorithms for computing the FD of color images. Nevertheless, almost all these methods are computationally inefficient when analyzing large images. Nowadays, color images can be very large in size, and there is a growing trend toward even larger datasets. This implies that the time required to calculate the FD of such datasets can become extremely long. In this paper we present a very efficient GPU algorithm, implemented in CUDA, for computing the FD of RGB color images. Our solution is an extension to RGB of the differential box-counting (DBC) algorithm for gray-scale images. Our implementation simplifies the box-counting computation to very simple operations which are easily combined across iterations. We evaluated our algorithm on two distinct hardware/software platforms using a set of images of increasing size. The performance of our method was compared against two recent FD algorithms for RGB images: a fast box-merging GPU algorithm, and the most advanced approach based on extending the DBC method. The results showed that our GPU algorithm performed very well and achieved speedups of up to 7.9× and 6172.6× regarding these algorithms, respectively. In addition, our algorithm achieved average error rates similar to those obtained by the two reference algorithms when estimating the FD for synthetic images with known FD values, and even outperformed them when processing large images. These results suggest that our GPU algorithm offers a highly reliable and ultra-fast solution for estimating the FD of color images.</p>", "Keywords": "Fractal dimension; Box-counting; CUDA; GPU; Color image", "DOI": "10.1007/s10044-025-01415-y", "PubYear": 2025, "Volume": "28", "Issue": "1", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Software Engineering Department, University of Granada, Granada, Spain; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Southwestern University of Finance and Economics, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Software Engineering Department, University of Granada, Granada, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Software Engineering Department, University of Granada, Granada, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Software Engineering Department, University of Granada, Granada, Spain"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Software Engineering Department, University of Granada, Granada, Spain"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Software Engineering Department, University of Granada, Granada, Spain"}], "References": [{"Title": "Fractal dimension of synthesized and natural color images in Lab space", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "2", "Page": "819", "JournalTitle": "Pattern Analysis and Applications"}, {"Title": "Fractal Dimension Estimation for Color Texture Images", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "1", "Page": "37", "JournalTitle": "Journal of Mathematical Imaging and Vision"}, {"Title": "Fast differential box-counting algorithm on GPU", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "1", "Page": "204", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Image splicing detection system using intensity-level multi-fractal dimension feature engineering and twin support vector machine based classifier", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "25", "Page": "39745", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "MTFD-Net: Left atrium segmentation in CT images through fractal dimension estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "173", "Issue": "", "Page": "108", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "New version of Hyper-Fractal Analysis application for estimating the fuzzy fractal dimension of hyperspectral satellite ocean color images", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "301", "Issue": "", "Page": "109215", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": 143451868, "Title": "The Dependability and Safety Indicators of a Train Driver-Machine System", "Abstract": "<p>The paper aims to assess the effect of the existing actions to assist a train driver in various operational situations, as well as to numerically evaluate the effect of such assistance on the resultant indicator of an error-free driver performance. The paper calculates and analyses the probability of at least one of the independent events or actions aimed at improving the quality of driver performance and reduction of the probability of error. The model of an environment was created, in which the probability of error-free driver performance is affected by a number of factors.</p>", "Keywords": "", "DOI": "10.7251/JIT2302066S", "PubYear": 2023, "Volume": "25", "Issue": "2", "JournalId": 41532, "JournalTitle": "JITA - Journal of Information Technology and Applications (Banja Luka) - APEIRON", "ISSN": "2232-9625", "EISSN": "2233-0194", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143451903, "Title": "Multi-robot movement based on a new modified source-seeking algorithm", "Abstract": "<p>The two main sources of difficulty for a group of mobile robots employing sensors to find a source are robot collisions and wireless ambient noise, such as light, sound, and other sounds. This paper introduces a novel approach to multi-robot system cooperation and collision avoidance: the new modified source-seeking control with noise cancelation technology. The robot team works together on an incline of a light source field; the team’s mobility is dependent upon following the upward gradient’s direction and forming a particular movement pattern. The proposed program also takes into account each robot’s size, speed limit, obstacles, and noise. The noise cancelation technique has been used to avoid the delay and false decisions to find the target point of the source. When the noise is canceled, all control inputs to the algorithm are accurate, and the feedback decision will be true. In this study, we use the MATLAB simulation tools to test the velocity, position, time delay, and performance of each robot in the used group of robots. The simulation and practical results of the robots in searching for a light source showed very satisfactory performance compared with the results in the literature.</p>", "Keywords": "", "DOI": "10.1017/S0263574724002200", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> A<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A novel hybrid gravitational search particle swarm optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "104263", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Real-time path planning for autonomous vehicle based on teaching–learning-based optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "381", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Humanoid robot path planning using memory-based gravity search algorithm and enhanced differential evolution approach in a complex environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119423", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-robot path planning using a hybrid dynamic window approach and modified chaotic neural oscillator-based hyperbolic gravitational search algorithm in a complex terrain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "2", "Page": "213", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "A collision-free path planning method for industrial robot manipulators considering safe human–robot interaction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "3", "Page": "323", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Development of source seeking algorithm for mobile robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "3", "Page": "393", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "A novel multiple targets detection method for service robots in the indoor complex scenes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "4", "Page": "453", "JournalTitle": "Intelligent Service Robotics"}]}, {"ArticleId": 143451915, "Title": "A systematic review of deep learning techniques for apple leaf diseases classification and detection", "Abstract": "<p>Agriculture sustains populations and provides livelihoods, contributing to socioeconomic growth. Apples are one of the most popular fruits and contains various antioxidants that reduce the risk of chronic diseases. Additionally, they are low in calories, making them a healthy snack option for all ages. However, several factors can adversely affect apple production. These issues include diseases that drastically lower yield and quality and cause farmers to lose millions of dollars. To minimize yield loss and economic effects, it is essential to diagnose apple leaf diseases accurately and promptly. This allows targeted pesticide and insecticide use. However, farmers find it difficult to distinguish between different apple leaf diseases since their symptoms are quite similar. Computer vision applications have become an effective tool in recent years for handling these issues. They can provide accurate disease detection and classification through massive image datasets. This research analyzes and evaluates datasets, deep learning methods and frameworks built for apple leaf disease detection and classification. A systematic analysis of 45 articles published between 2016 and 2024 was conducted to evaluate the latest developments, approaches, and research needs in this area.</p>", "Keywords": "Apple;Classification;Deep learning;Detection;Leaf diseases", "DOI": "10.7717/peerj-cs.2655", "PubYear": 2025, "Volume": "11", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, University of N’djamena, N’djamena, Chad"}, {"AuthorId": 2, "Name": "Bulent Tugrul", "Affiliation": "Deparment of Computer Engineering, Ankara University, Ankara, Türkiye"}], "References": [{"Title": "Evolutionary Feature Optimization for Plant Leaf Disease Detection by Deep Neural Networks", "Authors": "Jalal <PERSON>; Burak Berk Üstündağ", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "12", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Review of deep learning: concepts, CNN architectures, challenges, applications, future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "53", "JournalTitle": "Journal of Big Data"}, {"Title": "Deep learning for apple diseases: classification and identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computational Intelligence Studies"}, {"Title": "An Optimized Convolution Neural Network Architecture for Paddy Disease Classification", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "3", "Page": "6053", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "AlexNet architecture based convolutional neural network for toxic comments classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "7547", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Apple leaf disease identification via improved CycleGAN and convolutional neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "14", "Page": "9773", "JournalTitle": "Soft Computing"}, {"Title": "Artificial Driving based EfficientNet for Automatic Plant Leaf Disease Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "13", "Page": "38209", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Diagnosis of fungi affected apple crop disease using improved ResNeXt deep learning model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "24", "Page": "64879", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Exploring the trend of recognizing apple leaf disease detection through machine learning: a comprehensive analysis using bibliometric techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "2", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Multi-classification of disease induced in plant leaf using chronological Flamingo search optimization with transfer learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "", "Page": "e1972", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 143451986, "Title": "Dynamic simulation and fatigue life prediction of locomotive traction drive system", "Abstract": "<p>In this work, a rigid-flexible coupling dynamic model has been established to describe a specific type of locomotive traction drive system, by a parametric modeling method utilizing Creo, Ansys, and RecurDyn software systems comprehensively. With this model, the dynamic characteristics, stress, and deformation results of the system under various working conditions were evaluated via dynamic simulation and modal analysis. These results enable the prediction of contact fatigue strength of driving gear tooth surface for the estimation of its service life. On the basis of theoretical analysis, combined with the performance test of the laboratory bench, the working condition of the test gear pair’s tooth surface under the condition of equivalent load was tested, and the pitting position of the tooth surface was found to be consistent with the simulation analysis. Through the dynamic simulation and fatigue life prediction of the transmission system, it provides a theoretical basis for reasonably improving the efficiency of transmission system parts and setting up the inspection and maintenance cycle.</p>", "Keywords": "", "DOI": "10.1177/14727978241292930", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "Junhua Bao", "Affiliation": "Mechanical Engineering, Dalian <PERSON>g University, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical Engineering, Dalian <PERSON>g University, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mechanical Engineering, Dalian <PERSON>g University, Dalian, China"}, {"AuthorId": 4, "Name": "Wei<PERSON> He", "Affiliation": "Mechanical Engineering, Dalian <PERSON>g University, Dalian, China"}], "References": []}, {"ArticleId": 143451991, "Title": "Exploiting Skin Melanin Network for Skin Pigmentation Classification", "Abstract": "", "Keywords": "", "DOI": "10.2352/EI.2025.37.9.IQSP-256", "PubYear": 2025, "Volume": "37", "Issue": "9", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143451995, "Title": "Study on defect detection in lightweight insulators based on improved YOLOv8", "Abstract": "<p>Aiming at the problems of insulator detection such as low recognition accuracy for small targets and the algorithm model is too large and difficult to be deployed to the edge devices. In this paper, a lightweight YOLOv8-ASF-P2 insulator defect detection model is designed. The model introduces the ASF and P2 detection layer, and at the same time according to the idea of ASF to add the P2 detection layer, the new network structure is trained and then pruned. After pruning, the mAP of this algorithm is 89.5%, the model size is 2.1 MB, and the detection speed is 144.9FPS. 1.7% improvement in mAP, 64.7% reduction in model size, and 40% improvement in detection speed compared with the YOLOv8 algorithm, which verifies the effectiveness of the improved method.</p>", "Keywords": "", "DOI": "10.1177/14727978241304269", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Grid Jiangsu Electric Power Co., Ltd., Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Grid Jiangsu Electric Power Co., Ltd., Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Jiangsu Electric Power Co., Ltd., Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Grid Jiangsu Electric Power Co., Ltd., Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Jiangsu Electric Power Co., Ltd., Nanjing, China"}], "References": [{"Title": "R-SSD: refined single shot multibox detector for pedestrian detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "10430", "JournalTitle": "Applied Intelligence"}, {"Title": "A deep learning approach for insulator instance segmentation and defect detection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "9", "Page": "7253", "JournalTitle": "Neural Computing and Applications"}, {"Title": "YOLOX with CBAM for insulator detection in transmission lines", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "14", "Page": "43419", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Research on improved YOLOv8 algorithm for insulator defect detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "ASF-YOLO: A novel YOLO model with attentional scale sequence fusion for cell instance segmentation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "147", "Issue": "", "Page": "105057", "JournalTitle": "Image and Vision Computing"}]}, {"ArticleId": 143452002, "Title": "The role of cloud computing in supporting the internationalization of higher education", "Abstract": "<p>With the advancement of globalization and the rapid development of technology, higher education is facing unprecedented challenges and opportunities. Cloud computing, as the core of modern technology, has brought earth-shaking changes to college education. This study explores in depth how cloud computing can play its potential role in supporting the internationalization of higher education. Through empirical research and model analysis, the study found that hybrid cloud computing strategies perform well in improving the utilization of educational resources and providing flexible and efficient learning and teaching experiences for students and teachers. At the same time, the mixed teaching model is particularly effective under the support of cloud computing. However, this comes with data security and privacy challenges. To this end, this study proposes targeted improvement strategies and provides valuable suggestions for educational institutions and technology providers. In general, cloud computing has not only brought great opportunities for higher education but also presented new challenges, requiring the education and technology communities to work together and deepen cooperation.</p>", "Keywords": "", "DOI": "10.1177/14727978241299628", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON>yan <PERSON>v", "Affiliation": "College of International Education, Shanghai University of Engineering Science, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Informatization Office, Shanghai University of Engineering Science, Shanghai, China"}], "References": [{"Title": "Students' use of technology and their perceptions of its usefulness in higher education: International comparison", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "5", "Page": "1321", "JournalTitle": "Journal of Computer Assisted Learning"}]}, {"ArticleId": 143452007, "Title": "Water resource brand marketing strategy based on cloud computing", "Abstract": "<p>In the context of the development of the digital era, cloud computing technology has gradually penetrated into many industry fields and has had a profound impact on its structure and operation mode. Especially in the field of water brand marketing, cloud computing provides a range of advanced tools and platforms to help companies implement marketing strategies more efficiently and flexibly. The purpose of this study is to deeply explore the water resource brand marketing strategy based on cloud computing and make a comparative analysis with traditional methods. After an exhaustive literature review, this study summarizes the basic definition, core characteristics, and application of cloud computing in the water resources market. In order to more systematically understand the actual effects of cloud computing in marketing strategies, this study designed an experiment to compare the effects of cloud-based marketing strategies with traditional methods in practical applications. After rigorous data collection and processing, further analysis showed that the cloud-based strategy had relatively high performance on several key metrics such as sales growth and customer satisfaction. Based on the research results, it can be concluded that the cloud-based water resource brand marketing strategy provides a new research direction and practice opportunity for this field. This study provides useful theoretical support and practical guidance for water resource enterprises that expect to adjust and optimize brand strategy in the contemporary market environment.</p>", "Keywords": "", "DOI": "10.1177/14727978241295908", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Jiaozuo University, Jiaozuo, China"}], "References": [{"Title": "An overview of visualization and visual analytics applications in water resources management", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "153", "Issue": "", "Page": "105396", "JournalTitle": "Environmental Modelling & Software"}]}, {"ArticleId": 143452012, "Title": "Leveraging big data in art education: Navigating the intelligent interaction journey with CNN algorithms for steering paths", "Abstract": "<p>This paper firstly investigates the interaction mode of art education and compares the effect of smart interaction with that of traditional classroom. The purpose of the research is using the weighting operation of the original feature map by the convolutional layer to extract the features of the image, integrating the features in the fully connected layer after several convolutional layers, and finally outputting the classification results. Then, the feature extraction backbone network DetNet-59 is constructed by using deformable convolution instead of traditional regular square convolution and combined with cavity convolution to make the model effectively increase the perceptual field. And the CNN intelligence algorithm is improved by adopting the guided region proposal network GA-RPN. Interaction detection performance of the proposed method outperforms all other methods; finally, comparison experiments and ablation experiments are conducted on the classical dataset V-COCO dataset and HICO-DET dataset to verify the effectiveness of the improved algorithm on the recognition of intelligent interaction behaviors in art education. On the V-COCO dataset, we achieved 62.5% mAP and a 3.7% mAP improvement, and the method in this paper outperforms other models by 10.5% mAP. Models utilizing CNN intelligence algorithms are notably more proficient in discerning and reasoning about intelligent interactions.</p>", "Keywords": "", "DOI": "10.1177/14727978241292918", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Administration and Education, China Academy of Art Hangzhou, Hangzhou, China"}, {"AuthorId": 2, "Name": "Chaoyu Yan", "Affiliation": "Li Siguang College, China University of Geosciences, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Academy of Fine Arts, Nanjing Normal University, Nanjing, China"}], "References": []}, {"ArticleId": 143452013, "Title": "Fuzzy clustering algorithm-based teaching resource recommendation method for automotive engine overhaul", "Abstract": "<p>At present, the traditional teaching resource recommendation algorithm mainly constructs the evaluation matrix of teaching resources and determines the recommendation order according to the evaluation value, which lacks the grasp of user preferences and leads to poor recommendation effect. Therefore, a method of recommending teaching resources for automobile engine overhaul based on fuzzy clustering algorithm is proposed. This paper constructs the algorithm flow of automobile engine overhaul teaching resources recommendation, describes the K-means clustering algorithm and the improved K-means clustering algorithm, analyzes the user characteristics by using the clustering fuzzy algorithm, calculates the similarity between user interests and resources, and completes the recommendation of automobile engine overhaul teaching resources. Experiments verify the recommendation accuracy of this method, and the results show that this method has low MAE value and high recommendation accuracy when it is used to recommend teaching resources.</p>", "Keywords": "", "DOI": "10.1177/14727978241298387", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Intelligent Manufacturing and Automobile Department, Chongqing Technology and Business Institute, Chongqing, China;Intelligent Manufacturing and Automobile Department, Chongqing Open University, Chongqing, China"}], "References": [{"Title": "An incremental density-based clustering framework using fuzzy local clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "404", "JournalTitle": "Information Sciences"}, {"Title": "Optimized fuzzy clustering using moth-flame optimization algorithm in wireless sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Moazam <PERSON>ki", "PubYear": 2022, "Volume": "55", "Issue": "3", "Page": "1915", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Integrating User-Group relationships under interest similarity constraints for social recommendation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "249", "Issue": "", "Page": "108921", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "New feature analysis-based elastic net algorithm with clustering objective function", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "258", "Issue": "", "Page": "110004", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 143452015, "Title": "Anomaly detection system based on K-nearest neighbors and Naive Bayes", "Abstract": "<p>This paper conducts a study and analysis of the SEA dataset, grouping the data and digitizing features to obtain corresponding labels. Subsequently, the K-Nearest Neighbors (KNN) algorithm is applied to the dataset to investigate its performance, revealing that the Manhattan distance is the optimal distance formula for researching this problem using the KNN algorithm. The study explicitly selects frequent words chosen by individual users as features for calculation. During the computation process, grid search is employed to find the optimal parameters, and a model is created using these optimal parameters. The paper then applies the Naive Bayes algorithm to the dataset, comparing the strengths and weaknesses of different types of Naive Bayes methods. An analysis is conducted on the differences and advantages/disadvantages of various feature extraction methods, highlighting that the accuracy of Bernoulli Naive Bayes is higher than that of Multinomial Naive Bayes.</p>", "Keywords": "", "DOI": "10.1177/14727978241293274", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Engineering, Beijing Information Science and Technology University, Beijing, China"}], "References": [{"Title": "Research on advertising content recognition based on convolutional neural network and recurrent neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "4", "Page": "398", "JournalTitle": "International Journal of Computational Science and Engineering"}, {"Title": "Real-time anomaly attack detection based on an improved variable length model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "3", "Page": "1179", "JournalTitle": "Journal of Computational Methods in Sciences and Engineering"}]}, {"ArticleId": 143452016, "Title": "Infinite-ISP: An open source hardware image signal processor platform for all imaging needs", "Abstract": "", "Keywords": "", "DOI": "10.2352/EI.2025.37.7.ISS-279", "PubYear": 2025, "Volume": "37", "Issue": "7", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Tai<PERSON>r <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 11, "Name": "Sarf<PERSON>", "Affiliation": ""}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143452020, "Title": "Comparison of Agile and Devops Methodologies: Analysis of Efficiency, Flexibility, and Application in Software Development", "Abstract": "<p>This paper provides a concise overview of Agile and DevOps methodologies in software engineering. It aims to introduce readers to the fundamental principles of Agile and DevOps, accompanied by brief descriptions and practical examples. The advantages and disadvantages of each methodology are discussed, followed by a comparative analysis highlighting key differences. Understanding these methodologies is crucial in today’s IT landscape, as they are commonly employed in various organizations, impacting project management, team collaboration, and product delivery. This paper serves as a valuable resource for individuals seeking a basic understanding of Agile and DevOps methodologies in software engineering.</p>", "Keywords": "", "DOI": "10.7251/JIT2401078R", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 41532, "JournalTitle": "JITA - Journal of Information Technology and Applications (Banja Luka) - APEIRON", "ISSN": "2232-9625", "EISSN": "2233-0194", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143452029, "Title": "Synthesizing Spreading-out features for generative zero-shot image classification", "Abstract": "Suffering from the singularity of generated samples, the most of the existing generative models cannot achieve satisfactory performance on Generalized Zero-shot Learning tasks. Generative methods applied to generalized zero-shot learning mainly focus on the data generation of the entire class domain, and do not pay enough attention to the intra-instance relationships, which severely limits the robustness of generators. Therefore, we propose a model that mainly utilizes <PERSON><PERSON><PERSON> distance and Wasserstein Generative Adversarial Network -divergence to generate various diversified visual features, which effectively alleviate the domain shift problem and facilitates better classification than traditional Wasserstein distance and its variants. In addition, we also use perturbation-based attack strategy to standardize the range of generated features, so as not to generate too grotesque features and lead to incorrect classification results. Finally, supported by the above, we utilize two different classifiers to obtain better Generative Zero-shot Learning performance, i.e., the traditional softmax classifier and the normalized prototypes classifier. Extensive experimental results show that our proposed method can outperform the most of state-of-the-art methods on five benchmark datasets in generalized zero-shot learning setting and various measurement criteria.", "Keywords": "", "DOI": "10.1016/j.engappai.2025.110151", "PubYear": 2025, "Volume": "144", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, 210094, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON>ov College of Marine Engineering, Jiangsu Ocean University, Lianyungang, 222005, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Shenzhen, 518055, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Durham University, Durham, DH1 3LE, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Southeast University, Nanjing, 210096, China"}, {"AuthorId": 6, "Name": "Yunyang Yan", "Affiliation": "School of Computer and Software Engineering, Huaiyin Institute of Technology, Huaian, 223003, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, 210094, China;Corresponding author"}], "References": [{"Title": "Zero-shot learning by mutual information estimation and maximization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>ng Lv", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105490", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Guided CNN for generalized zero-shot and open-set recognition using visual and semantic prototypes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107263", "JournalTitle": "Pattern Recognition"}, {"Title": "Learning discriminative domain-invariant prototypes for generalized zero shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105796", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep transductive network for generalized zero shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107370", "JournalTitle": "Pattern Recognition"}, {"Title": "Pseudo distribution on unseen classes for generalized zero shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Ya<PERSON> Yao", "PubYear": 2020, "Volume": "135", "Issue": "", "Page": "451", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A plug-in attribute correction module for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107767", "JournalTitle": "Pattern Recognition"}, {"Title": "Joint Visual and Semantic Optimization for zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "215", "Issue": "", "Page": "106773", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Novel virtual sample generation using conditional GAN for developing soft sensor with small data", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104497", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Learning discriminative and representative feature with cascade GAN for generalized zero-shot learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "236", "Issue": "", "Page": "107780", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Coarse-to-fine SVD-GAN based framework for enhanced frame synthesis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "104699", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "VAE4RSS: A VAE-based neural network approach for robust soft sensor with application to zinc roasting process", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "105180", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Thermographic fault diagnosis of electrical faults of commutator and induction motors", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "121", "Issue": "", "Page": "105962", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Visual-semantic consistency matching network for generalized zero-shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "536", "Issue": "", "Page": "30", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 143452041, "Title": "Co-designing conversational agents with older people: A scoping review of methods, challenges, and a way forward", "Abstract": "Conversational agents are increasingly recognized as potential solutions to the challenges of aging. However, adapting these technologies to the specific needs of the older adults requires effective design methods that integrate end-users and relevant stakeholders throughout the development process. Co-design methods have gained popularity in this context, but the criteria for evaluating their effectiveness remain limited to basic measures of acceptability and ease of use, with little focus on their actual impact on design outcomes. This study addresses this gap by aiming to: 1) identify the structures of co-design methods tailored for older adults, 2) analyze their strengths and weaknesses, and 3) provide recommendations to optimize future applications. To achieve these goals, we conducted a scoping study using the PRISMA methodology. Searches were carried out on PsycInfo, Google Scholar, PubMed, ACM, IEEE, Web of Science and DBLP. Of the 982 articles identified, 27 projects were selected. The analysis revealed great variability in methods and highlighted the lack of consensus on optimal co-design structures for this population. In addition, most studies lacked measures to assess the influence of method diversity on design outcomes and participant experience. These findings underscore the need for a scientifically grounded design framework and guidelines to support best practices in co-design for aging-related conversational agents. Establishing such standards would reinforce methodological rigor, enable better comparability across studies, and allow for more accurate measurement of co-design impact on product effectiveness and user satisfaction.", "Keywords": "Co-design; Conversational agent; Older adults; Scoping review", "DOI": "10.1016/j.chbr.2025.100606", "PubYear": 2025, "Volume": "17", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS, INRIA, LIG, 38000, Grenoble, France;Univ. Grenoble Alpes, CNRS, TIMC, 38000, Grenoble, France;Corresponding author. Univ. Grenoble Alpes, CNRS, TIMC, UFR SHS, 1251 avenue Centrale, CS 4070, 38000, cedex 9, Grenoble, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS, INRIA, LIG, 38000, Grenoble, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Univ. Grenoble Alpes, CNRS, TIMC, 38000, Grenoble, France"}], "References": [{"Title": "Use of Intelligent Voice Assistants by Older Adults with Low Technology Use", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Exploring older adults’ perception and use of smart speaker-based voice assistants: A longitudinal study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "106914", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Designing Kitchen Technologies for Ageing in Place", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Robot-Delivered Cognitive Stimulation Games for Older Adults", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}, {"Title": "Personal Narratives in Technology Design: The Value of Sharing Older Adults’ Stories in the Design of Social Robots", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "315", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Participatory Design, Development, and Testing of Assistive Health Robots with Older Adults: An International Four-year Project", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Human-Robot Interaction"}, {"Title": "Fridolin: participatory design and evaluation of a nutrition chatbot for older adults", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "1", "Page": "33", "JournalTitle": "i-com"}, {"Title": "Designing Multi-Modal Conversational Agents for the Kitchen with Older Adults: A Participatory Design Study", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "9-10", "Page": "1507", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Promising directions for human-robot interactions defined by older adults", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "", "Page": "1289414", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Talk2Care: An LLM-based Voice Assistant for Communication between Healthcare Providers and Older Adults", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Recommendations for designing conversational companion robots with older adults through foundation models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "", "Page": "1363713", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 143452056, "Title": "New energy vehicles sales forecasting using machine learning: The role of media sentiment", "Abstract": "Media news and the abundance of user-generated content (UGC) play an important role in consumers’ purchasing decisions. This paper establishes three media sentiment indices to forecast Chinese new energy vehicles (NEVs) sales with various machine learning-based models. Using a natural language processing (NLP) technique, media sentiment indices are derived from over half a million social media reviews and 15,000 news headlines. Subsequently, a hybrid approach is proposed for NEVs forecasting, integrating sentiment analysis, data decomposition, and machine learning models. For verification, various learning paradigms are accordingly developed. The experimental results indicate that: 1) News sentiment and Weibo sentiment exhibit bi-directional causality with NEVs sales. 2) Media sentiments can significantly improve the prediction performance of NEVs sales, particularly news sentiment. 3) Machine learning models reveal superior performance in short-term forecasting of NEVs sales, while the proposed approach is promising for mid-term forecasting.", "Keywords": "", "DOI": "10.1016/j.cie.2025.110928", "PubYear": 2025, "Volume": "201", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing 400045, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing 400045, China;Corresponding author at: Chongqing University, No. 174, Shazheng Road, Chongqing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing 400045, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Management Science and Real Estate, Chongqing University, Chongqing 400045, China;School of Business, Chizhou University, Chizhou, Anhui 247000, China;School of Business, Anhui University of Technology, Ma’anshan, Anhui 243032, China;Corresponding author at: Anhui University of Technology, No. 59, Hudong Road, Ma’anshan City, Anhui Province, China"}], "References": [{"Title": "Forecasting the sales and stock of electric vehicles using a novel self-adaptive optimized grey model", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104148", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Box office sales and social media: A cross-platform comparison of predictive ability and mechanisms", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "113517", "JournalTitle": "Decision Support Systems"}, {"Title": "Macroeconomic forecasting through news, emotions and narrative", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "175", "Issue": "", "Page": "114760", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A memory-trait-driven decomposition–reconstruction–ensemble​ learning paradigm for oil price forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107699", "JournalTitle": "Applied Soft Computing"}, {"Title": "Forecasting the box offices of movies coming soon using social media analysis: A method based on improved Bass models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116241", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Considering economic indicators and dynamic channel interactions to conduct sales forecasting for retail sectors", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "165", "Issue": "", "Page": "107965", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Approaching sales forecasting using recurrent neural networks and transformers", "Authors": "<PERSON><PERSON>-<PERSON>; <PERSON>; <PERSON><PERSON>-<PERSON><PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "116993", "JournalTitle": "Expert Systems with Applications"}, {"Title": "The role of governmental policy in game between traditional fuel and new energy vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "169", "Issue": "", "Page": "108292", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A comparative online sales forecasting analysis: Data mining techniques", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "176", "Issue": "", "Page": "108935", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A combination model based on multi-angle feature extraction and sentiment analysis: Application to EVs sales forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "224", "Issue": "", "Page": "119986", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Which product description phrases affect sales forecasting? An explainable AI framework by integrating WaveNet neural network models with multiple regression", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "176", "Issue": "", "Page": "114065", "JournalTitle": "Decision Support Systems"}, {"Title": "Sentiment and attention of the Chinese public toward electric vehicles: A big data analytics approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "127", "Issue": "", "Page": "107216", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Novel deterministic and probabilistic forecasting methods for crude oil price employing optimized deep learning, statistical and hybrid models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "658", "Issue": "", "Page": "120021", "JournalTitle": "Information Sciences"}, {"Title": "Exploring the technology changes of new energy vehicles in China: Evolution and trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "191", "Issue": "", "Page": "110178", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A multi-scale analysis method with multi-feature selection for house prices forecasting", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "171", "Issue": "", "Page": "112779", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 143452226, "Title": "NOMA-MIMO in 5G network: a detailed survey on enhancing data rate", "Abstract": "<p>Non-orthogonal multiple access (NOMA) is a technology that leverages user channel gains, offers higher spectral efficiency, improves user fairness, better cell-edge throughput, increased reliability, and low latency, making it a potential technology for the next generation of cellular networks. The application of NOMA in the power domain (NOMA-PD) with multiple-input multiple-output (MIMO) and other emerging technologies allows to achieve the demand for higher data rates in next-generation networks. This survey aims to funnel down NOMA MIMO resource allocation issues and different optimization problems that exist in the literature to enhance the data rate. We examine the most recent NOMA-MIMO clustering, power allocation, and joint allocation schemes and analyze various parameters used in optimization methods to design 5G systems. We finally identify a promising research problem based on the signal-to-interference-plus-noise ratio (SINR) parameter in the context of NOMA-PD with MIMO configuration.</p>", "Keywords": "5G;Beamforming;DC programming;Dinklebach;Lagrange;NOMA-MIMO;Power allocation;SINR", "DOI": "10.7717/peerj-cs.2388", "PubYear": 2025, "Volume": "11", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Wireless Technology/Faculty of Engineering, Multimedia University, Cyberjaya, Cyberjaya, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Wireless Technology/Faculty of Engineering, Multimedia University, Cyberjaya, Cyberjaya, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Next Generation Network Research Institute, Telekom Malaysia Research & Development, Cyberjaya, Cyberjaya, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Next Generation Network Research Institute, Telekom Malaysia Research & Development, Cyberjaya, Cyberjaya, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON> & <PERSON> (M) Sdn Bhd, Cyberjaya, Cyberjaya, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, Universiti Teknologi MARA, <PERSON>, Shah <PERSON>, Malaysia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "International Islamic University, Islamabad, Pakistan"}], "References": [{"Title": "Efficient power allocation for NOMA-enabled IoT networks in 6G era", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "", "Page": "101043", "JournalTitle": "Physical Communication"}, {"Title": "Optimal power allocation for NOMA-enabled D2D communication with imperfect SIC decoding", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "", "Page": "101296", "JournalTitle": "Physical Communication"}, {"Title": "Resource allocation for sum-rate maximization in NOMA-based generalized spatial modulation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "6", "Page": "1077", "JournalTitle": "Digital Communications and Networks"}, {"Title": "Heterogeneous Computational Resource Allocation for NOMA: Toward Green Mobile Edge-Computing Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "2", "Page": "1225", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 143452443, "Title": "LWSEE: Lightweight Secured Software-Based Execution Environment", "Abstract": "The Internet of Things (IoT) has become increasingly prevalent and used to handle sensitive and critical data. This demands mechanisms to ensure data security, protect privacy, and promote the general safety of IoT-based systems. Currently, hardware-based trusted execution environments (TEEs) are used to provide data protection, but they are not suitable for low-cost devices lacking hardware-assisted security features. To address this issue, this paper proposes a Lightweight Secured Software-based Execution Environment (LWSEE) for embedded devices. LWSEE is designed to be supported by low-cost, low-end devices without specific hardware requirements. It consists of a lightweight distributed solution that offers protection against hardware attacks, provides a comprehensive security check mechanism, enables secure application execution, and supports secure application updates to ensure the continued security of IoT devices. LWSEE comprises a secure architecture and communication protocol specially tailored to devices with constrained resources. Our experimental evaluation underlines the minimal overhead introduced by LWSEE while showing its performance in terms of execution time, CPU time, and memory usage. We examine the flexibility and adaptability of LWSEE by demonstrating that it can be configured to achieve minimal overhead ( e.g. , 39 . 8 ms per message for the general integrity verification of a node). This approach enables IoT devices to remain secure without dedicated hardware, allowing for the widespread adoption of IoT technology while maintaining data safety.", "Keywords": "IoT security; Trusted execution environment; Software protection; Data Integrity", "DOI": "10.1016/j.iot.2025.101513", "PubYear": 2025, "Volume": "30", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "LASIGE, Faculty of Sciences, University of Lisbon, Lisbon, Portugal;Correspondence to: LASIGE Computer Science and Engineering Research Centre, Campo Grande, 1749-016 Lisboa, Portugal.; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LASIGE, Faculty of Sciences, University of Lisbon, Lisbon, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Computer Science, TU Bergakademie Freiberg, Freiberg, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "LASIGE, Faculty of Sciences, University of Lisbon, Lisbon, Portugal"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LASIGE, Faculty of Sciences, University of Lisbon, Lisbon, Portugal"}], "References": [{"Title": "A Survey on Thwarting Memory Corruption in RISC-V", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 143452614, "Title": "Evaluation of optimum process parameters based on specific cutting energy in turning for sustainability", "Abstract": "<p>In response to growing concerns about environmental sustainability and the need to reduce carbon emissions in manufacturing, this study investigates the optimization of turning process parameters with a focus on minimizing energy consumption. The research develops a comprehensive mathematical model to identify the optimal cutting conditions, considering specific cutting energy, tool wear, and the application of minimum quantity lubrication (MQL) using eco-friendly canola oil. Experiments conducted on AISI 1080 steel using an uncoated carbide tool under varying machining conditions reveal that lubrication and tool wear significantly influence energy consumption. The results demonstrate that MQL not only reduces friction and cutting forces but also prolongs tool life, thereby lowering the specific cutting energy required for machining. By identifying the optimal combination of cutting speed, feed rate, and depth of cut, the study provides a pathway to achieving more sustainable machining practices. The findings highlight the potential for substantial energy savings in turning operations, contributing to the reduction of the carbon footprint associated with manufacturing processes.</p>", "Keywords": "Sustainability; Specific cutting energy; Embodied energy; Turning; Minimum quantity lubrication (MQL); Tool wear; Machining optimization", "DOI": "10.1007/s00170-025-15068-y", "PubYear": 2025, "Volume": "136", "Issue": "10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Engineering & Technical Management, University of Northern Iowa, Cedar Falls, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Institute of Technology Warangal, Telangana, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Institute of Technology Warangal, Telangana, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Institute of Technology Warangal, Telangana, India"}], "References": []}, {"ArticleId": 143452757, "Title": "Robust Fault Detection for Unmanned Aerial Vehicles Subject to Denial-of-Service Attacks", "Abstract": "<p>During the flight of unmanned aerial vehicles (UAVs), potential system faults can lead to mission failure or even crashes. Therefore, it is important to equip the ground control station (GCS) with a fault detection module. However, malicious attackers may launch denial-of-service (DoS) attacks to interfere with the network communication between UAVs and GCS, which can result in the failure of the fault detection mechanism. This study presents a robust fault detection scheme for UAVs in the presence of DoS attacks. Specifically, a fault detection filter (FDF) is devised to produce residual signals, while a resilient event-triggered mechanism (ETM) is implemented to enhance network bandwidth utilization efficiency and alleviate the adverse effects of DoS attacks. By considering the H∞ performance index and analyzing the exponential stability of the switching residual system, the event triggering parameters and the filter gain matrix are obtained.Furthermore, a detection logic utilizing residuals and thresholds is introduced to facilitate fault detection. Simulation results confirm the viability of this fault detection approach, which is grounded in a resilient event trigger mechanism.</p>", "Keywords": "", "DOI": "10.1115/1.4067772", "PubYear": 2025, "Volume": "147", "Issue": "3", "JournalId": 9493, "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control", "ISSN": "0022-0434", "EISSN": "1528-9028", "Authors": [{"AuthorId": 1, "Name": "Kunpeng Pan", "Affiliation": "School of Automation, Northwestern Polytechnical University, Xi'an, Shaanxi, 710129, China; Innovation Center NPU Chongqing, Northwestern Polytechnical University, Chongqing 400000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Northwestern Polytechnical University, Xi'an, Shaanxi, 710129, China; Innovation Center NPU Chongqing, Northwestern Polytechnical University, Chongqing 400000, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Automation, Northwestern Polytechnical University, Xi'an, Shaanxi, 710129, China; Key Laboratory of Information Fusion Technology, Ministry of Education, Xi'an, 710129, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Northwestern Polytechnical University, Xi'an, Shaanxi, 710129, China; Key Laboratory of Information Fusion Technology, Ministry of Education, Xi'an, 710129, China"}], "References": [{"Title": "Adaptive fault-tolerant control for nonlinear multi-agent systems with DoS attacks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "526", "Issue": "", "Page": "39", "JournalTitle": "Information Sciences"}, {"Title": "Detection, estimation, and compensation of false data injection attack for UAVs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "723", "JournalTitle": "Information Sciences"}, {"Title": "Adaptive fault estimation for cyber-physical systems with intermittent DoS attacks", "Authors": "Jing<PERSON><PERSON>; G<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "547", "Issue": "", "Page": "746", "JournalTitle": "Information Sciences"}, {"Title": "A game-based deep reinforcement learning approach for energy-efficient computation in MEC systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107660", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 143452763, "Title": "Research and implementation of an online platform for efficient and accurate ship hull design", "Abstract": "<b >Motivation</b> With the development of computer graphics in front-end technology and internet applications, there are many universal online modeling tools like Spline and Vectary. However, ship hull design requires specific constraints like displacement volume and standard deformation algorithms such as Lackenby and Free-Form deformation (FFD). <b >Solution</b> This study developed an online platform of ship hull parametric modeling and design called Ship Hull Iterative Parametrization Online (SHIP Online). Firstly, three deformation methods—linear,interactive FFD, and combined deformation—were implemented using JavaScript and Python. Fast Quadric Mesh Simplification (FQMS) and Nelder-Mead optimization were introduced for combined deformation. Secondly, the user interface was built with HTML5, JavaScript, and CSS. Thirdly, the synchronization and data transmission issues between front and back-end were handled by Django and $. ajax. Finally, the platform would be deployed on a cloud server for multi-system access. <b >Result</b> The DTMB-5415 standard model was used to evaluate the accuracy, efficiency, and functions of calculations and deformations in SHIP Online. The errors of hydrostatic parameters were nearly 0.0 %. With a 90 % optimal simplification rate and parameter errors under 0.001, combined deformation efficiency improved by 87.78 % and 55.15 % compared to deformation without FQMS and other software. Functional and user testing shows that the functions can be stably and normally executed. <b >Conclusion</b> By integrating ship deformation algorithms and design constraints with the front-end technology, SHIP Online provides a Web-based rapid hull design platform instead of traditional 3D modeling client software.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2025.103870", "PubYear": 2025, "Volume": "202", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Maritime and Transportation, Ningbo University, Ningbo 315211, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Maritime and Transportation, Ningbo University, Ningbo 315211, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Maritime and Transportation, Ningbo University, Ningbo 315211, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Maritime and Transportation, Ningbo University, Ningbo 315211, China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Maritime and Transportation, Ningbo University, Ningbo 315211, China"}], "References": [{"Title": "Additive Manufacturing Path Generation for Robot Manipulators Based on CAD Models", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "10037", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "I-STL2MOOSE: From STL data to integrated volumetrical meshes for MOOSE", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "21", "Issue": "", "Page": "101273", "JournalTitle": "SoftwareX"}, {"Title": "An integrated-hull design assisted by artificial intelligence-aided design method", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "297", "Issue": "", "Page": "107320", "JournalTitle": "Computers & Structures"}]}, {"ArticleId": 143452892, "Title": "Torque Prediction In Deep Hole Drilling: Artificial Neural Networks Versus Nonlinear Regression Model", "Abstract": "", "Keywords": "", "DOI": "10.1080/08839514.2025.2459482", "PubYear": 2025, "Volume": "39", "Issue": "1", "JournalId": 7360, "JournalTitle": "Applied Artificial Intelligence", "ISSN": "0883-9514", "EISSN": "1087-6545", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Thai Nguyen University of Economics-Technology; Thai Nguyen University of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Ng<PERSON>en", "Affiliation": "Hanoi University of Science and Technology"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Thai Nguyen University of Technology"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Thai Nguyen University of Economics-Technology"}], "References": [{"Title": "A mechanistic prediction model for thrust force and torque during drilling of CFRP/Ti stacks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "7-8", "Page": "3105", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study on chip morphology and surface roughness in ultrasonically assisted drilling of 304 stainless steel", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "7-8", "Page": "2079", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Finite element simulation of drilling operation and theoretical analysis of drill stresses with the deform-3D", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON> Yağmur", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "102153", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Modeling of thrust and torque for drilling PTFE materials", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "1-2", "Page": "215", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study on dynamic drilling forces of CFRP/Ti stacks during longitudinal-torsional ultrasonic vibration drilling", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "7-8", "Page": "2989", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 143452917, "Title": "Optimizing flow and heat transfer in industrial processes: The potential of trihybrid nanofluid and thermal-radiation using Hamilton-Crosser and Xue models", "Abstract": "The present study uses the Hamilton-Crosser thermal conductivity and Xue models to study the impacts of thermal radiation on thermal boundary layer convective flow of propylene glycol-based trihybrid nanofluid across a spinning disk with thermal slip and velocity slip conditions. This recommended model evaluates the performance of two popular trihybrid nanofluid models, the <PERSON><PERSON> model and Hamilton-Crosser model. In chemical reactors, thermal power plants, and advanced cooling systems, this model allows for the accurate prediction and improvement of thermal conductivity and energy efficiency. Especially in high-temperature settings, companies can improve heat dissipation, lower energy consumption, and increase process stability by utilizing the outstanding thermal capabilities of trihybrid nanofluids. When effective heat management is essential, such as in microelectronics cooling, automotive thermal management, and renewable energy systems, this method is especially helpful. By comparing the Xue and Hamilton-Crosser models, it is possible to optimize the flow parameters and nanoparticle composition, which improves thermal systems' stability and energy efficiency. The transformation of significant similarity is used to build ordinary differential equations for the nonlinear dimensionless system. This problem can be resolved mathematically using the Bvp4c approach. The outcomes demonstrate that although the thermal profile improves as the <PERSON>rinkman's number increases, the rate of heat transmission decreases.", "Keywords": "Thermal radiation; Velocity and thermal slip conditions; Trihybrid nanofluid; Heat generation; <PERSON><PERSON> and <PERSON> Crosser models", "DOI": "10.1016/j.jrras.2025.101322", "PubYear": 2025, "Volume": "18", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, College of Engineering in Wadi Alddawasir, Prince <PERSON><PERSON><PERSON> bin Abdul<PERSON> University, Saudi Arabia;Production Engineering and Mechanical Design Department, Faculty of Engineering, Mansoura University, P.O 35516, Mansoura, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, College of Engineering, Northern Border University, Arar, 73222, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Petroleum Engineering, College of Engineering, Knowledge University, Erbil, Iraq;Department of Petroleum Technology, Erbil Technology College, Erbil Polytechnic University, Erbil, Kurdistan Region, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, The Islamia University of Bahawalpur, Bahawalpur, 63100, Pakistan;Corresponding author. Department of Mathematics, The Islamia University of Bahawalpur, Bahawalpur, 63100, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Fundamental and Applied Sciences Department, Universiti Teknologi PETRONAS, Perak, 32610, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science Knowledge Unit of Science and Technology University of Science and Technology Sialkot, Pakistan;Corresponding author"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, College of Engineering and computer science, Jazan University, P. O. Box 706, Jazan, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, College of Engineering, University of Ha'il, Ha'il City, 81451, Saudi Arabia"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Fundamental and Applied Sciences Department, Universiti Teknologi PETRONAS, Perak, 32610, Malaysia"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Saveetha School of Engineering, SIMATS, Chennai, Tamil Nadu, India;Hourani Center for Applied Scientific Research, Al-Ahliyya Amman University, Amman, Jordan;Department of Mathematics, College of Science, Al-Zulfi Majmaah University, Al-Majmaah, 11952, Saudi Arabia"}], "References": [{"Title": "Computation analysis of unsteady second grade biomagnetic micropolar blood base nanofluid flow with motile gyrotactic microorganisms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Modelling and Simulation"}, {"Title": "Computational analysis of elastic deformation on radiative flow of trihybrid nanofluid over vertical cylinder with Du<PERSON>ur and Soret effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "4", "Page": "101177", "JournalTitle": "Journal of Radiation Research and Applied Sciences"}, {"Title": "Estimation of induction effects on electrophoresis and thermophoresis particles deposition in radiative flow of trihybrid nanofluid across cylinder", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "18", "Issue": "1", "Page": "101222", "JournalTitle": "Journal of Radiation Research and Applied Sciences"}, {"Title": "Characteristics of thermal radiation on Darcy-Forchheimer flow of trihybrid nanofluid over Riga plate with bioconvection and viscous dissipation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2025, "Volume": "18", "Issue": "1", "Page": "101248", "JournalTitle": "Journal of Radiation Research and Applied Sciences"}]}, {"ArticleId": 143453160, "Title": "DWSD: Dense Waste Segmentation Dataset", "Abstract": "<p>Waste disposal is a global challenge, especially in densely populated areas. Efficient waste segregation is critical for separating recyclable from non-recyclable materials. While developed countries have established and refined effective waste segmentation and recycling systems, our country still uses manual segregation to identify and process recyclable items. This study presents a dataset intended to improve automatic waste segmentation systems. The dataset consists of 784 images that have been manually annotated for waste classification. These images were primarily taken in and around Jadavpur University, including streets, parks, and lawns. Annotations were created with the Labelme program and are available in color annotation formats. The dataset includes 14 waste categories: plastic containers, plastic bottles, thermocol, metal bottles, plastic cardboard, glass, thermocol plates, plastic, paper, plastic cups, paper cups, aluminum foil, cloth, and nylon. The dataset includes a total of 2350 object segments.</p><p>© 2025 The Author(s).</p>", "Keywords": "Classification and segmentation;Computer vision;Smart cities;Waste management", "DOI": "10.1016/j.dib.2025.111340", "PubYear": 2025, "Volume": "59", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Telecommunication Engineering, Jadavpur University, Kolkata 700032, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Narula Institute of Technology, Kolkata, India."}, {"AuthorId": 3, "Name": "Md. Manarul Sk.", "Affiliation": "Department of Electronics and Telecommunication Engineering, Jadavpur University, Kolkata 700032, India."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering, College of Computing, Umm AL-Qura University, Mecca 24381, Kingdom of Saudi Arabia."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Telecommunication Engineering, Jadavpur University, Kolkata 700032, India."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computing and Information Technology, University of Doha for Science and Technology, Doha 24449, Qatar."}], "References": []}, {"ArticleId": 143453195, "Title": "Exploiting Entity Information for Robust Prediction Over Event Knowledge Graphs", "Abstract": "", "Keywords": "", "DOI": "10.1109/TETC.2025.3534243", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 17260, "JournalTitle": "IEEE Transactions on Emerging Topics in Computing", "ISSN": "", "EISSN": "2168-6750", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Software, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 2, "Name": "Hongming Cai", "Affiliation": "School of Software, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 3, "Name": "Shengtung Tsai", "Affiliation": "School of Software, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "Pan Hu", "Affiliation": "School of Software, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Manchester, Manchester, U.K."}, {"AuthorId": 7, "Name": "Bingqing Shen", "Affiliation": "Shanghai International Studies University, Shanghai, China"}], "References": []}, {"ArticleId": 143453358, "Title": "Air Quality Monitoring and Controlling System Using Dust Sensor", "Abstract": "", "Keywords": "", "DOI": "10.18494/SAM5274", "PubYear": 2025, "Volume": "37", "Issue": "1", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Tian-<PERSON><PERSON>n", "Affiliation": ""}], "References": []}, {"ArticleId": 143453452, "Title": "Linear active disturbance rejection control with hybrid energy storage system for frequency regulation", "Abstract": "", "Keywords": "", "DOI": "10.1080/17445760.2025.2450661", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 14122, "JournalTitle": "International Journal of Parallel, Emergent and Distributed Systems", "ISSN": "1744-5760", "EISSN": "1744-5779", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Inner Mongolia Power (Group) Co. Ltd."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Inner Mongolia Power (Group) Co. Ltd."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inner Mongolia Power (Group) Co. Ltd."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Inner Mongolia Power (Group) Co. Ltd."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "North China Electric Power University"}, {"AuthorId": 6, "Name": "Feng Hong", "Affiliation": "North China Electric Power University"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "North China Electric Power University"}], "References": [{"Title": "A novel structure adaptive discrete grey <PERSON>lli model and its application in renewable energy power generation prediction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "255", "Issue": "", "Page": "124481", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 143453492, "Title": "Finite-time cluster synchronisation of complex networks with multi-weights and nonlinear couplings via semi-intermittent control", "Abstract": "", "Keywords": "", "DOI": "10.1080/00207721.2025.2450095", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chongqing Normal University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Normal University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Henan University of Technology"}], "References": [{"Title": "Adaptive finite-time cluster synchronization of neutral-type coupled neural networks with mixed delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "384", "Issue": "", "Page": "11", "JournalTitle": "Neurocomputing"}, {"Title": "ℋ <sub>∞</sub> /passive non-fragile synchronisation of Markovian jump stochastic complex dynamical networks with time-varying delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "7", "Page": "1270", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Fixed-Time Synchronization of Neural Networks with Parameter Uncertainties via Quantized Intermittent Control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "2303", "JournalTitle": "Neural Processing Letters"}, {"Title": "Global fixed-time synchronization for coupled time-varying delayed neural networks with multi-weights and uncertain couplings via periodically semi-intermittent adaptive control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "4", "Page": "1685", "JournalTitle": "Soft Computing"}, {"Title": "Quantized adaptive pinning control for fixed/preassigned-time cluster synchronization of multi-weighted complex networks with stochastic disturbances", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "44", "Issue": "", "Page": "101157", "JournalTitle": "Nonlinear Analysis: Hybrid Systems"}, {"Title": "Cluster synchronisation of nonlinear singular complex networks with multi-links and time delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "15", "Page": "3226", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Fixed-time stabilization of discontinuous spatiotemporal neural networks with time-varying coefficients via aperiodically switching control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "5", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Projective synchronisation of bipartite networks with nonlinear couplings via quantised event-triggered control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "55", "Issue": "13", "Page": "2604", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Practical Finite-Time Synchronization of T-S Fuzzy Complex Networks with Different Couplings via Semi-intermittent Control", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "26", "Issue": "5", "Page": "1507", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 143453567, "Title": "Explanatory Model of Awareness Factors of Smart Technologies for Independent Living at Home in Later Life", "Abstract": "", "Keywords": "", "DOI": "10.18267/j.aip.258", "PubYear": 2025, "Volume": "14", "Issue": "1", "JournalId": 32235, "JournalTitle": "Acta Informatica Pragensia", "ISSN": "1805-4951", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Natalija Rebrica", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Self-efficacy and anxiety as determinants of older adults’ use of Internet Banking Services", "Authors": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>; Ángel <PERSON>-<PERSON>; <PERSON>-<PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "4", "Page": "825", "JournalTitle": "Universal Access in the Information Society"}, {"Title": "Older adults' perspectives of smart home technology: Are we developing the technology that older people want?", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "102571", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Model of Acceptance and Use of Market-Ready Home-Based e-Care Services: A Qualitative Study with Care Receivers and Informal Caregivers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Vesna <PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "5", "Page": "988", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": *********, "Title": "A visualization system for dam safety monitoring with application of digital twin platform", "Abstract": "For decades, research on dam safety monitoring systems has received considerable critical attention due to their significance in ensuring public safety and social economic development. In view of the rapid development of computational technologies, it’s becoming increasingly difficult to ignore the urgent need to accelerate the intelligent development of dam safety monitoring systems. The emphasis of the prior studies usually lay in the dam safety monitoring data and structural health analysis. However, they neglect the importance of the real-time interactions of the systems, which makes the presented work not suitable for today’s digital age. As such, based on digital twin platform, this paper aims to propose a visualization system for dam safety monitoring. Primarily, the next-generation and real-time rendering technologies are introduced to construct digital twin platform. The digital twin platform serves as a digital sand table to accurately reflect the dam current state and real-time monitoring information. Subsequently, artificial intelligence algorithms and numerical simulation are applied in monitoring data analysis, prediction model construction, structural safety evaluation, and risk early warning. Based on the windows platform, all of the above system functions are implemented in C++, Python languages, and OpenGL. Eventually, this presented real-time digital twin system was applied in a real dam in China. The direct outcome shows its superiority and effectiveness in a continuous and accurate dam safety evaluation.", "Keywords": "", "DOI": "10.1016/j.eswa.2025.126740", "PubYear": 2025, "Volume": "271", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Water Conservancy and Hydropower Engineering, Hohai University, Nanjing 210098, China;The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China;National Engineering Research Center of Water Resources Efficient Utilization and Engineering Safety, Hohai University, Nanjing 210098, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Water Conservancy and Hydropower Engineering, Hohai University, Nanjing 210098, China;The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China;National Engineering Research Center of Water Resources Efficient Utilization and Engineering Safety, Hohai University, Nanjing 210098, China;Corresponding author at: College of Water Conservancy and Hydropower Engineering, Hohai University, Nanjing 210098, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Water Science and Technology, Hohai University, Nanjing 210098, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Water Conservancy and Hydropower Engineering, Hohai University, Nanjing 210098, China;The National Key Laboratory of Water Disaster Prevention, Hohai University, Nanjing 210098, China;National Engineering Research Center of Water Resources Efficient Utilization and Engineering Safety, Hohai University, Nanjing 210098, China"}, {"AuthorId": 5, "Name": "Xiangnan Qin", "Affiliation": "School of Water Conservancy and Transportation, Zhengzhou University, Zhengzhou 450001, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Civil Engineering and Architecture, Nanchang University, Nanchang 330031, China"}], "References": [{"Title": "An approach using random forest intelligent algorithm to construct a monitoring model for dam safety", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Su", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "39", "JournalTitle": "Engineering with Computers"}, {"Title": "A Comparative Study of Unstructured Data with SQL and NO-SQL Database Management Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "4", "Page": "59", "JournalTitle": "Journal of Computer and Communications"}, {"Title": "Digital Twins: Review and Challenges", "Authors": "<PERSON>; <PERSON>; Adriana <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Dynamic early-warning model of dam deformation based on deep learning and fusion of spatiotemporal features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "233", "Issue": "", "Page": "107537", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel outlier detection method for monitoring data in dam engineering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116476", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 143453654, "Title": "Machining-induced geometric errors in thin-walled parts—a review of mitigation strategies and development of application guidelines", "Abstract": "Abstract </h3> <p>The growing demand for fuel efficiency and emission reduction in the aviation industry has significantly driven the adoption of weight-reduction strategies, notably through the use of thin-walled parts (TWPs). These parts are fabricated from structurally efficient materials such as light alloys (e.g., aluminum) and hard-to-machine alloys (e.g., titanium and nickel). However, the machining of thin-walled parts presents significant challenges, including high material removal rates, reduced rigidity, elevated vibration levels, residual stresses, and dimensional deformations, all of which complicate the processing of these components. To address these challenges, recent research has led to the development of several innovative machining solutions. In order to implement the findings of these researches in the industry, there is a need for guideline development that can be helpful for both practitioners and researchers by comparing these solutions in terms of the extent of changes required in the current machining setup, level of controls achieved for different dimensional and material categories of workpieces. Hence, the current work reviews causes of machining deflection of thin-walled parts by systematically reviewing all major seven countermeasures proposed by researchers. Based on this, a decision support table has been developed to aid in deciding a deflection mitigation strategy based on the categorization of workpiece thickness, machinability, and level of changes required in the existing machining setup to implement the mitigation strategy and reported extent of control achieved on different machining quality parameters. The novelty of current research is the development of a decision support table and comprehensive review of thin-wall machining based on a mitigation strategy. The findings of this research will be useful for machining technologists to identify thin-wall machining-related challenges and will assist in deciding available solutions for implementation to enable accurate and efficient machining practices.</p>", "Keywords": "Thin-wall deflection; Parameter optimization; Vibration; Chatter; Stability; Compensation; Stress; Part stiffening; Machining strategies", "DOI": "10.1007/s00170-024-14917-6", "PubYear": 2025, "Volume": "136", "Issue": "10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Engineering and Technology Taxila, Taxila, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Engineering and Technology Taxila, Taxila, Pakistan; Corresponding author."}], "References": [{"Title": "Surface finish improvement using a damping-alloy sleeve-insert tool holder in the end milling process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2433", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Design and experimental evaluation of an impact damper to be used in a slender end mill tool in the machining of hardened steel", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "5-6", "Page": "2553", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Research on vibration suppression by a multi-point flexible following support head in thin-walled parts mirror milling", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "7-8", "Page": "3335", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study on vibration suppression performance of a flexible fixture for a thin-walled casing", "Authors": "<PERSON><PERSON>; <PERSON>g<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "9-10", "Page": "4281", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "An efficient method to predict the chatter stability of titanium alloy thin-walled workpieces during high-speed milling by considering varying dynamic parameters", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "5407", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Prediction of machining-induced residual stress in orthogonal cutting of Ti6Al4V", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2375", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A time-varying geometry modeling method for parts with deformation during machining process", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "15", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Machining vibration monitoring based on dynamic clamping force measuring in thin-walled components milling", "Authors": "<PERSON><PERSON> Sun; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "5-6", "Page": "2211", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Modal parameter identification of general cutter based on milling stability theory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "221", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Machining deformation prediction of large fan blades based on loading uneven residual stress", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "9-10", "Page": "4345", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A prediction model for the milling of thin-wall parts considering thermal-mechanical coupling and tool wear", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> Pan", "PubYear": 2020, "Volume": "107", "Issue": "11-12", "Page": "4645", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "The effect of cutting tool material on chatter vibrations and statistical optimization in turning operations", "Authors": "<PERSON><PERSON> Gök; <PERSON><PERSON>; Me<PERSON>et Alper Sofuoğlu", "PubYear": 2020, "Volume": "24", "Issue": "22", "Page": "17319", "JournalTitle": "Soft Computing"}, {"Title": "Comprehensive effect of multi-parameters on vibration in high-speed precision milling", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "7-8", "Page": "2187", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A machining deformation control method of thin-walled part based on enhancing the equivalent bending stiffness", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "9-10", "Page": "2775", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Vibration recognition for peripheral milling thin-walled workpieces using sample entropy and energy entropy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "9-10", "Page": "3251", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Micromachining of hardened hot-work tool steel: effects of milling strategies", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "9-10", "Page": "2839", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A framework for accuracy enhancement in milling thin-walled narrow-vane turbine impeller of NiAl-based superalloy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "11-12", "Page": "3925", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "State of the art in milling process of the flexible workpiece", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "5-6", "Page": "1695", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effect of controllable magnetic field-induced MRF solidification on chatter suppression of thin-walled parts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "9-12", "Page": "2881", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A semi-analytical model for predicting the machining deformation of thin-walled parts considering machining-induced and blank initial residual stress", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "1-2", "Page": "139", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "FEM-based optimization approach to machining strategy for thin-walled parts made of hard and brittle materials", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "5-6", "Page": "1399", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Rapid prediction and compensation method of cutting force-induced error for thin-walled workpiece", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "11-12", "Page": "5453", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Layout optimization of auxiliary support for deflection errors suppression in end milling of flexible blade", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "5-6", "Page": "1889", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Modeling and compensation of comprehensive errors for thin-walled parts machining based on on-machine measurement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "11-12", "Page": "3645", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "The influence of ice-based fixture on suppressing machining-induced deformation of cantilever thin-walled parts: a novel and green fixture", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "1-2", "Page": "329", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Study on deformation and compensation for micromilled thin walls with high aspect ratios", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "5-6", "Page": "1797", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Effect of component configuration on geometric tolerances during end milling of thin-walled parts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "11-12", "Page": "3617", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Preparation of Papers for IFAC Conferences & Symposia: Adaptive fixture system for reducing machining distortion caused by residual stresses in milling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "264", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Effect of cryogenic cooling on deformation of milled thin-walled titanium alloy parts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "9-10", "Page": "3683", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Chatter-free milling strategy of a slender Blisk blade via stock distribution optimization and continuous spindle speed change", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "3-4", "Page": "1273", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Special Milling Tools to Suppress Self-Excited Chatter Vibrations in Milling", "Authors": "Faraz <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "36", "Page": "115", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Chaotic marine predators algorithm for global optimization of real-world engineering problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "261", "Issue": "", "Page": "110192", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Swift feedback and immediate error control using a lightweight simulation approach – A case study of the digital-twin-in-the-loop for machining thin-wall structures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "71", "Issue": "", "Page": "309", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 143453815, "Title": "MetapathVis: Inspecting the Effect of Metapath in Heterogeneous Network Embedding via Visual Analytics", "Abstract": "In heterogeneous graphs (HGs), which offer richer network and semantic insights compared to homogeneous graphs, the Metapath technique serves as an essential tool for data mining. This technique facilitates the specification of sequences of entity connections, elucidating the semantic composite relationships between various node types for a range of downstream tasks. Nevertheless, selecting the most appropriate metapath from a pool of candidates and assessing its impact presents significant challenges. To address this issue, our study introduces MetapathVis , an interactive visual analytics system designed to assist machine learning (ML) practitioners in comprehensively understanding and comparing the effects of metapaths from multiple fine‐grained perspectives. MetapathVis allows for an in‐depth evaluation of various models generated with different metapaths, aligning HG network information at the individual level with model metrics. It also facilitates the tracking of aggregation processes associated with different metapaths. The effectiveness of our approach is validated through three case studies and a user study, with feedback from domain experts confirming that our system significantly aids ML practitioners in evaluating and comprehending the viability of different metapath designs.", "Keywords": "", "DOI": "10.1111/cgf.15285", "PubYear": 2025, "Volume": "44", "Issue": "1", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology ShanghaiTech University Shanghai China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CFETS Information Technology (Shanghai) Co., Ltd. Shanghai China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology ShanghaiTech University Shanghai China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology ShanghaiTech University Shanghai China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "WeChat Tencent Technology (Shenzhen) Co., Ltd. Shenzhen China"}, {"AuthorId": 6, "Name": "Lingling Yi", "Affiliation": "WeChat Tencent Technology (Shenzhen) Co., Ltd. Shenzhen China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering The Hong Kong University of Science and Technology Kowloon Hong Kong China"}], "References": [{"Title": "Cloud Pricing Models", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Personalised meta-path generation for heterogeneous graph neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "6", "Page": "2299", "JournalTitle": "Data Mining and Knowledge Discovery"}, {"Title": "Heterogeneous graph neural networks analysis: a survey of techniques, evaluations and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "8", "Page": "8003", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Self-supervised learning for heterogeneous graph via structure information based on metapath", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "110388", "JournalTitle": "Applied Soft Computing"}, {"Title": "VA + Embeddings STAR: A State‐of‐the‐Art Report on the Use of Embeddings in Visual Analytics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "3", "Page": "539", "JournalTitle": "Computer Graphics Forum"}, {"Title": "A Comparative Analysis of Centrality Measures in Complex Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "85", "Issue": "8", "Page": "685", "JournalTitle": "Automation and Remote Control"}]}, {"ArticleId": 143454078, "Title": "Influence of Industry 4.0 on the sustainable development of urban economy under the background of fuzzy control and soft computing", "Abstract": "<p>Industry 4.0 has not only brought about profound changes to the manufacturing industry itself but has also had a significant impact on the sustainable development of urban economies. By 2021, the urbanization rate of China’s resident population will reach 64.72%, with the urban population rising from 170 million in 1978 to 910 million, and the built-up area of cities reaching 63,000 square kilometers. Industry 4.0 is an important driver for sustainable urban economic development, which can improve the efficiency, quality, innovation, and competitiveness of urban manufacturing industries and promote urban economic growth, employment, environment, and social welfare. This paper analyzes the mechanism of the impact of Industry 4.0 on the sustainable development of China’s urban economy from the perspective of fuzzy control and soft computing, as well as the application practice of Industry 4.0 in the sustainable development of urban economy. The study shows that fuzzy control and soft computing, as an intelligent computing method capable of dealing with uncertainty, nonlinearity, and complexity, can effectively support the implementation of Industry 4.0 and improve the flexibility, adaptive, and innovative capabilities of the manufacturing industry, thus promoting the green growth, social equity, and resource efficiency of urban economy. The theoretical research and practical exploration with the sustainable development of urban economy provide some insights and lessons.</p>", "Keywords": "", "DOI": "10.1177/14727978241295900", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Jiaozuo University, Jiaozuo, China"}], "References": [{"Title": "RETRACTED ARTICLE: Soft computing-based fuzzy\n integral sliding mode control: a real-time investigation on a conical tank\n process", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "17", "Page": "13135", "JournalTitle": "Soft Computing"}, {"Title": "A new expert system in prediction of lung cancer disease based on fuzzy soft sets", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "18", "Page": "14179", "JournalTitle": "Soft Computing"}, {"Title": "RETRACTED ARTICLE: A novel user interaction middleware component system for ubiquitous soft computing environment by using fuzzy agent computing system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "4827", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "On soft computing with random fuzzy sets in econometrics and machine learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "12", "Page": "7745", "JournalTitle": "Soft Computing"}, {"Title": "Development of fuzzy $$ \\bar{X} - S $$ control charts with unbalanced fuzzy data", "Authors": "Akın Özdemir", "PubYear": 2021, "Volume": "25", "Issue": "5", "Page": "4015", "JournalTitle": "Soft Computing"}, {"Title": "Soft Computing Techniques and Their Applications in Intel-ligent Industrial Control Systems: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "Actuator Fluid Control Using <PERSON><PERSON> Feedback for Soft Robotics Activities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "3", "Page": "1855", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Machine learning soft computing fuzzy set to identify ubiquitous integrated network state at different AM operations", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "4", "Page": "219", "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing"}]}, {"ArticleId": 143454092, "Title": "Hypergraph denoising neural network for session-based recommendation", "Abstract": "<p>Session-based recommendation (SBR) predicts the next interaction of users based on their clicked items in a session. Previous studies have shown that hypergraphs are superior in capturing complex item transitions which contribute to SBR performance. However, existing hypergraph-based methods fail to model item co-occurrence and sequential patterns simultaneously, limiting the improvement of recommendation performance. Moreover, they are more sensitive to noisy items than conventional graph models due to the item association mechanism. In this paper, we propose a novel hypergraph-based method named Hypergraph Denoising Neural Network (HDNN) for SBR to tackle the abovementioned problems. The proposed method involves two newly-designed modules: a sequential pattern learning module (SPLM) and an adaptive attention selection module (AASM). In particular, SPLM models item sequential patterns to complement the hypergraph-based models which only focus on co-occurrence patterns. Meanwhile, AASM employs learnable attention score thresholds to exclude items with low attention scores, mitigating the impact of noisy items in hypergraphs. Furthermore, the sequential denoising unit (SDU) designed in SPLM is employed to eliminate noise in item sequential patterns, thus realizing the dual denoising purpose. Extensive experiments are conducted on three real-world datasets. The results of the experiments show that our HDNN framework shows better performance than the state-of-the-art models. In particular, all evaluation metrics in Tmall and RetailRocket showed improvements of over 15% and 5%, respectively.</p>", "Keywords": "Hypergraph; Denoising; Sequential patterns; Session-based recommendation", "DOI": "10.1007/s10489-025-06283-x", "PubYear": 2025, "Volume": "55", "Issue": "6", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Communications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Communications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "School of Communications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 4, "Name": "Jinsheng Wei", "Affiliation": "School of Communications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}], "References": [{"Title": "DAN: a deep association neural network approach for personalization recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "7", "Page": "963", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "Exploiting intra- and inter-session dependencies for session-based recommendations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "1", "Page": "425", "JournalTitle": "World Wide Web"}, {"Title": "GC–HGNN: A global-context supported hypergraph neural network for enhancing session-based recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101129", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Enhanced graph neural network for session-based recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "118887", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sustainable computing across datacenters: A review of enabling models and techniques", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "52", "Issue": "", "Page": "100620", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": *********, "Title": "Multi-objective optimal routing algorithm translation conversion theory subtitle translation research", "Abstract": "<p>At present, the traditional translation learning model lacks the construction of model network, which leads to low translation accuracy. In this paper, a multi-objective optimization routing algorithm is proposed to study Translation Shifts Theory subtitle translation. The multi-objective configuration method is used to optimize the parameters of the model, and the multi-objective constrained evolutionary feature solution is obtained by combining the semantic ontology feature decomposition method. The semantic association distribution model between ontologies is established, and the optimal output conversion control of English translation learning model is realized according to multi-objective optimization routing design. The experimental results show that the model has good convergence and high translation accuracy when this method is used in translation data processing, and this method has good performance.</p>", "Keywords": "", "DOI": "10.1177/*****************", "PubYear": 2024, "Volume": "24", "Issue": "6", "JournalId": 18115, "JournalTitle": "Journal of Computational Methods in Sciences and Engineering", "ISSN": "1472-7978", "EISSN": "1875-8983", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Huang", "Affiliation": "School of English Studies, Zhejiang Yuexiu University, Shaoxing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shenzhen Institute of Information Technology, Shenzhen, China"}], "References": [{"Title": "Multi-layer interaction preference based multi-objective evolutionary algorithm through decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "420", "JournalTitle": "Information Sciences"}, {"Title": "On the Linguistic Representational Power of Neural Machine Translation Models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "1", "Page": "1", "JournalTitle": "Computational Linguistics"}, {"Title": "Addressing topic modeling with a multi-objective optimization approach based on swarm intelligence", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "225", "Issue": "", "Page": "107113", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Enhancing low-resource neural machine translation with syntax-graph guided self-attention", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "246", "Issue": "", "Page": "108615", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Muformer: A long sequence time-series forecasting model based on modified multi-head attention", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "254", "Issue": "", "Page": "109584", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": *********, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0898-1221(25)00043-4", "PubYear": 2025, "Volume": "180", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [], "References": []}, {"ArticleId": *********, "Title": "A real-time machine learning-based methodology for short-term frequency nadir prediction in low-inertia power systems", "Abstract": "In the modern era, electricity is vital for societal advancement, driving economic growth and essential functions. However, the landscape of power systems is swiftly changing due to the integration of renewable energy sources and the decline of traditional synchronous generation, which reduces the total rotational inertia of the systems. This reduction in inertia leads to more frequent and severe frequency deviations, directly impacting power system behavior. Therefore, there is a pressing need to anticipate frequency grid disturbances to maintain stability and prevent disruptions. A machine learning approach is proposed to address this issue, providing accurate and responsive frequency forecasting in power systems. This paper introduces a novel methodology that leverages machine learning for short-term minimum frequency prediction, emphasizing efficiency and rapid response. A comprehensive experimentation process was conducted using several popular machine learning models, with their hyperparameters optimized through a Bayesian algorithm and evaluated via cross-validation. Results highlight the effectiveness of Decision Trees, offering a balance between efficiency and efficacy. Validation was conducted using the SCADA of a Typhoon HIL real-time simulator, verifying that the proposed methodology is suitable for real-time applications.", "Keywords": "", "DOI": "10.1016/j.neucom.2025.129583", "PubYear": 2025, "Volume": "626", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Division of Computer Science, University of Sevilla, Av. <PERSON><PERSON>, s/n, Seville, 41012, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>z", "Affiliation": "Electrical Engineering Department, Escuela Técnica Superior de Ingeniería, Universidad de Sevilla, Camino de los Descubrimientos, Seville, 41092, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Division of Computer Science, University of Sevilla, Av. <PERSON>ina <PERSON>, s/n, Seville, 41012, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Loughborough, Centre for Renewable Energy Systems Technology (CREST), Loughborough, LE11 3TU, United Kingdom"}], "References": [{"Title": "A Hybrid Intelligent Modeling approach for predicting the solar thermal panel energy production", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "565", "Issue": "", "Page": "126997", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 143454839, "Title": "EHR-based prediction modelling meets multimodal deep learning: A systematic review of structured and textual data fusion methods", "Abstract": "Electronic Health Records (EHRs) have transformed healthcare by digitally consolidating patient medical history, encompassing structured data (e.g., demographic data, lab results), and unstructured textual data (e.g., clinical notes). These data hold significant potential for predictive modelling, and recent studies have dedicated efforts to leverage the different modalities in a cohesive and effective manner to improve predictive accuracy. This Systematic Literature Review (SLR) addresses the application of Multimodal Deep Learning (MDL) methods in EHR-based prediction modelling, specifically through the fusion of structured and textual data. Following PRISMA guidelines, we conducted a comprehensive literature search across six article databases, using a carefully designed search string. After applying inclusion and exclusion criteria, we selected 77 primary studies. Data extraction was standardized using a structured form based on the CHARMS checklist. We categorized and analysed the fusion strategies employed across the studies. By combining structured and textual data at the input level, early fusion enabled models to learn joint feature representations from the beginning, whether in vectorized representations or data textualization. Intermediate fusion, which delays integration, was particularly useful for tasks where each modality provides unique insights that need to be processed independently before being combined. Late fusion enabled modularity by integrating outputs from unimodal models, which is suitable when EHR structured and textual data have varying quality or reliability. We also identified trends and open issues that need attention. This review contributes a comprehensive understanding of EHR data fusion practices using MDL, highlighting potential pathways for future research and development in health informatics.", "Keywords": "", "DOI": "10.1016/j.inffus.2025.102981", "PubYear": 2025, "Volume": "118", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "Ariel Soares Teles", "Affiliation": "Department of Biostatistics and Health Informatics, Institute of Psychiatry, Psychology & Neuroscience, King’s College London, London, UK;Federal Institute of Maranhão, Maranhão, Brazil;Corresponding author at: Federal Institute of Maranhão, Maranhão, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal Institute of Piauí, Piauí, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Federal University of Maranhão, Maranhão, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and Health Informatics, Institute of Psychiatry, Psychology & Neuroscience, King’s College London, London, UK;The authors share joint last authorship"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and Health Informatics, Institute of Psychiatry, Psychology & Neuroscience, King’s College London, London, UK;The authors share joint last authorship"}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "A review of deep learning with special emphasis on architectures, applications and recent trends", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105596", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Measuring the Quality of Explanations: The System Causability Scale (SCS)", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2", "Page": "193", "JournalTitle": "KI - Künstliche Intelligenz"}, {"Title": "Clinical Implementation of Predictive Models Embedded within Electronic Health Record Systems: A Systematic Review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "25", "JournalTitle": "Informatics"}, {"Title": "Hospital readmission prediction based on long-term and short-term information fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106690", "JournalTitle": "Applied Soft Computing"}, {"Title": "AutoML: A survey of the state-of-the-art", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106622", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Deep multi-view learning methods: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "448", "Issue": "", "Page": "106", "JournalTitle": "Neurocomputing"}, {"Title": "Review of deep learning: concepts, CNN architectures, challenges, applications, future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "53", "JournalTitle": "Journal of Big Data"}, {"Title": "A Survey on Bias and Fairness in Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "An overview of deep learning methods for multimodal medical data mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "200", "Issue": "", "Page": "117006", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Natural language processing: state of the art, current trends and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "3", "Page": "3713", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Explainable Artificial Intelligence (XAI): What we know and what is left to attain Trustworthy Artificial Intelligence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101805", "JournalTitle": "Information Fusion"}, {"Title": "Fairness in Machine Learning: A Survey", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "7", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "EHR-KnowGen: Knowledge-enhanced multimodal learning for disease diagnosis generation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "102", "Issue": "", "Page": "102069", "JournalTitle": "Information Fusion"}, {"Title": "Multi-modality approaches for medical support systems: A systematic review of the last decade", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "103", "Issue": "", "Page": "102134", "JournalTitle": "Information Fusion"}, {"Title": "Deep Multimodal Data Fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Foundations & Trends in Multimodal Machine Learning: Principles, Challenges, and Open Questions", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "10", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Automated machine learning: past, present and future", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "5", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Heart failure prognosis prediction : Let’s start with the MDL-HFP model", "Authors": "<PERSON>ting Ma; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "125", "Issue": "", "Page": "102408", "JournalTitle": "Information Systems"}, {"Title": "Deep learning based multimodal biomedical data fusion: An overview and comparative review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "112", "Issue": "", "Page": "102536", "JournalTitle": "Information Fusion"}, {"Title": "A review of deep learning models and online healthcare databases for electronic health records and their use for health prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "9", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Review of multimodal machine learning approaches in healthcare", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2025, "Volume": "114", "Issue": "", "Page": "102690", "JournalTitle": "Information Fusion"}, {"Title": "A systematic review of fairness in machine learning", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "AI and Ethics"}]}, {"ArticleId": *********, "Title": "Exploring the drivers of digital technology adoption for enhancing domestic tax mobilization in Ghana", "Abstract": "<b >Purpose</b> This study investigates the determinants of tax compliance through the lens of performance expectancy, effort expectancy, social influence, facilitating conditions and hedonic motivation. <b >Design/methodology/approach</b> The study adopted both quantitative and qualitative research methods to gather data on the adoption of tax technologies. Based on the five determinants, a conceptual framework was developed consisting of seven proposed hypotheses tested through a structural equation model. Interviews were conducted to gain further insight into the drivers of the taxpayers’ portal in Ghana. <b >Findings</b> The study finds that performance expectancy and effort expectancy are the most significant factors predicting tax compliance intentions, indicating that taxpayers consider the portal as a useful tool in managing their taxes and very easy to use. It reduces their exposure to corrupt government officials and lessens their cost of paying taxes. It is also very convenient and serves as a useful way to avoid the long queues they experience at the tax offices. The study recommends that the Ghana Revenue Authority (GRA) and the Ministry of Finance (MoF) should promote more revenue collection technologies and create more awareness among taxpayers to utilise the portal. <b >Originality/value</b> The taxpayers’ portal in Ghana was recently introduced to enhance revenue mobilisation. No empirical research has been conducted to identify the adoption factors which will aid its smooth implementation. This paper thus provides significant value to both literature and practice.", "Keywords": "Adoption; Tax mobilisation technologies; Tax compliance", "DOI": "10.1016/j.jjimei.2025.100327", "PubYear": 2025, "Volume": "5", "Issue": "1", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Heritage Christian University, Accra, Ghana;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Heritage Christian University, Accra, Ghana"}, {"AuthorId": 3, "Name": "Dzifa Bibi", "Affiliation": "Heritage Christian University, Accra, Ghana"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Heritage Christian University, Accra, Ghana"}], "References": [{"Title": "Meta-Analysis of the Unified Theory of Acceptance and Use of Technology (UTAUT): Challenging its Validity and Charting a Research Agenda in the Red Ocean", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "23", "Issue": "1", "Page": "13", "JournalTitle": "Journal of the Association for Information Systems"}, {"Title": "Quality of E-Tax System and Tax Compliance Intention: The Mediating Role of User Satisfaction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "1", "Page": "22", "JournalTitle": "Informatics"}]}, {"ArticleId": *********, "Title": "DSADA: Detecting Spoofing Attacks in Driver Assistance Systems Using Objects’ Spatial Shapes", "Abstract": "<p>Object detection algorithms suffer from a perceptual vulnerability where they cannot differentiate between counterfeit and real objects. In this paper, we investigate the perceptual vulnerability in advanced driver assistance systems (ADAS) when faced with physical and digital spoofing attacks. To address this vulnerability, we propose a method named DSADA (Detecting Spoofing Attacks in Driver Assistance) to mitigate creation and misclassification spoofing attacks against object detection algorithms utilizing the LiDAR point clouds and objects’ spatial shapes. DSADA receives the outcomes of the object detection algorithm along with the corresponding LiDAR point clouds for each scene. DSADA exploits the spatial shapes of objects obtained from the point clouds to cross-validate the outcomes of the object detection algorithm. Any discrepancy results in generating an alert to warn about the spoofing attack. We analyze defense-aware and unaware attacks against DSADA. The evaluation results show the effectiveness of the suggested method with a true positive rate of 100% and a low false positive rate of only 3.97%. The comparative evaluation validates that the suggested method identifies a broader range of spoofed objects, including projected, displayed and printed ones, while narrowing the scope of potential attacks to familiar objects in the driving context.</p>", "Keywords": "", "DOI": "10.1145/3716139", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 18461, "JournalTitle": "ACM Transactions on Autonomous and Adaptive Systems", "ISSN": "1556-4665", "EISSN": "1556-4703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing, Queen’s University, Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing, Queen’s University, Canada"}], "References": [{"Title": "IoU-aware single-stage object detector for accurate localization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "103911", "JournalTitle": "Image and Vision Computing"}, {"Title": "Applying Machine Learning in Self-adaptive Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Autonomous and Adaptive Systems"}, {"Title": "Autonomous Driving Architectures: Insights of Machine Learning and Deep Learning Algorithms", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "", "Page": "100164", "JournalTitle": "Machine Learning with Applications"}, {"Title": "Adaptive momentum variance for attention-guided sparse adversarial attacks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "108979", "JournalTitle": "Pattern Recognition"}, {"Title": "Adversarial examples based on object detection tasks: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "519", "Issue": "", "Page": "114", "JournalTitle": "Neurocomputing"}, {"Title": "Protecting Autonomous Cars from Phantom Attacks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "4", "Page": "56", "JournalTitle": "Communications of the ACM"}, {"Title": "Bayesian evolutionary optimization for crafting high-quality adversarial examples with limited query budget", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110370", "JournalTitle": "Applied Soft Computing"}, {"Title": "A multi-objective memetic algorithm for automatic adversarial attack optimization design", "Authors": "Jialiang Sun; <PERSON>; Tingsong Jiang", "PubYear": 2023, "Volume": "547", "Issue": "", "Page": "126318", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 143454943, "Title": "Temporal Data Simulation and Drift-Resilient Machine Learning in Cardiovascular Disease Management: A Technical Analysis", "Abstract": "", "Keywords": "", "DOI": "10.32628/CSEIT251112128", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Evaluating the Performance of Automated Machine Learning (AutoML) Tools for Heart Disease Diagnosis and Prediction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "4", "Page": "1036", "JournalTitle": "AI"}, {"Title": "Continual Learning in Medicine: A Systematic Literature Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2025, "Volume": "57", "Issue": "1", "Page": "1", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 143454972, "Title": "Analysis of 6G and B5G waveforms using hybrid MF-ED and ECG-ED spectrum sensing techniques", "Abstract": "", "Keywords": "", "DOI": "10.1080/00051144.2025.2460879", "PubYear": 2025, "Volume": "66", "Issue": "2", "JournalId": 7651, "JournalTitle": "Automatika", "ISSN": "0005-1144", "EISSN": "1848-3380", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "New Horizon College of Engineering"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Prince of Songkla University"}], "References": [{"Title": "Enhanced spectrum sensing using a combination of energy detector, matched filter and cyclic prefix", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "4", "Page": "534", "JournalTitle": "Digital Communications and Networks"}, {"Title": "AoI minimization of ambient backscatter-assisted EH-CRN with cooperative spectrum sensing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "245", "Issue": "", "Page": "110389", "JournalTitle": "Computer Networks"}, {"Title": "A Hybrid Deep Learning Based Approach for Spectrum Sensing in Cognitive Radio", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "67", "Issue": "", "Page": "102497", "JournalTitle": "Physical Communication"}, {"Title": "A review of deep learning techniques for enhancing spectrum sensing and prediction in cognitive radio systems: approaches, datasets, and challenges", "Authors": "Noured<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "46", "Issue": "12", "Page": "1104", "JournalTitle": "International Journal of Computers and Applications"}]}, {"ArticleId": *********, "Title": "Motion planning and searching strategy of a transverse ledge climbing robot based on force feedback", "Abstract": "<p>A transverse ledge climbing robot inspired by athletic locomotion is a customized robot aiming to travel through horizontal ledges in vertical walls. Due to the safety issue and complex configurations in graspable ledges such as horizontal, inclined ledges, and gaps between ledges, existing well-known vision-based navigation methods suffering from occlusion problems may not be applicable to this special kind of application. This study develops a force feedback-based motion planning strategy for the robot to explore and make feasible grasping actions as it continuously travels through reachable ledges. A contact force detection algorithm developed using a momentum observer approach is implemented to estimate the contact force between the robot’s exploring hand and the ledge. Then, to minimize the detection errors due to dynamic model uncertainties and noises, a time-varying threshold is integrated. When the estimated contact force exceeds the threshold value, the robot control system feeds the estimated force into the admittance controller to revise the joint motion trajectories for a smooth transition. To handle the scenario of gaps between ledges, several ledge-searching algorithms are developed to allow the robot to grasp the next target ledge and safely overcome the gap transition. The effectiveness of the proposed motion planning and searching strategy has been justified by simulation, where the four-link transverse climbing robot successfully navigates through a set of obstacle scenarios modeled to approximate the actual environment. The performance of the developed grasping ledge searching methods for various obstacle characteristics has been evaluated.</p>", "Keywords": "", "DOI": "10.1017/S0263574725000062", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "Reno Pangestu", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Force tracking smooth adaptive admittance control in unknown environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "7", "Page": "1991", "JournalTitle": "Robotica"}, {"Title": "A brief survey of observers for disturbance estimation and compensation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "12", "Page": "3818", "JournalTitle": "Robotica"}, {"Title": "Fractional order inspired iterative adaptive control", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "2", "Page": "482", "JournalTitle": "Robotica"}]}, {"ArticleId": 143455240, "Title": "Hybrid quantum-classical 3D object detection using multi-channel quantum convolutional neural network", "Abstract": "<p>3D object detection has recently shown remarkable progress in the computer vision field, enabling advanced understanding of the surrounding environment by identifying objects’ shape, position, and depth. However, processing high-dimensional data using classical convolutional neural networks (CNNs) introduces considerable computational challenges. This paper proposes a novel hybrid quantum-classical 3D object detection (HQCOD) approach, integrating a multi-channel quantum convolutional neural network (MC-QCNN) to significantly reduce computational complexity by leveraging quantum computing advantages. Additionally, knowledge distillation (KD) is applied to enhance detection accuracy and model robustness. Experimental evaluations using the Karlsruhe institute of technology and Toyota technological institute (KITTI) dataset validate the scalability and effectiveness, demonstrating the HQCOD as a practical quantum-assisted solution. This study establishes a foundation for extending quantum-enhanced 3D computer vision methods into real-world applications.</p>", "Keywords": "Quantum machine learning; Multi-channel quantum convolutional neural network; Hybrid quantum-classical 3D object detection; Quantum keypoint feature pyramid network; Knowledge distillation", "DOI": "10.1007/s11227-025-06968-7", "PubYear": 2025, "Volume": "81", "Issue": "3", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Korea University, Seoul, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Korea University, Seoul, Republic of Korea; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Korea University, Seoul, Republic of Korea; Corresponding author."}, {"AuthorId": 4, "Name": "Soohyun Park", "Affiliation": "Division of Computer Science, Sookmyung Women’s University, Seoul, Republic of Korea; Corresponding author."}], "References": [{"Title": "Challenges and opportunities in quantum machine learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "9", "Page": "567", "JournalTitle": "Nature Computational Science"}, {"Title": "3D Object Detection for Autonomous Driving: A Comprehensive Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "8", "Page": "1909", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Quantum convolutional neural networks for multi-channel supervised learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "Quantum Machine Intelligence"}, {"Title": "A review of convolutional neural networks in computer vision", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "4", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Quantum Face Recognition with Multi-Gate Quantum Convolutional Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "12", "Page": "6330", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Quantum Face Recognition with Multi-Gate Quantum Convolutional Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "12", "Page": "6330", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}]}, {"ArticleId": 143455242, "Title": "Microdroplet dynamics on solid surface: The effect of wetting and surface tension using lattice Boltzmann method", "Abstract": "In this study, we investigated the micro-scale dynamics of droplet impact on solid surfaces with varying wettability, using a 3D modeling approach to capture the intricate behavior of microdroplets. We employed the multi-relaxation times pseudopotential lattice <PERSON>mann method to simulate the interaction between fluids of different densities, with interface tension playing a key role. The analysis focused on two distinct wetting scenarios: hydrophobic (non-wetting) and hydrophilic (wetting) surfaces, examining the droplet dynamics during both the spreading (propagation) and recoiling phases of impact. By manipulating the bulk modulus parameter κ and the corresponding surface tension γ, we were able to explore how wettability and surface tension influence droplet behavior, including deformation and stability. The study also validated key aspects of our computational framework through reference validations such as contact angle measurements and <PERSON><PERSON>'s law. Our results provide valuable comprehension of the mixed effects of wettability and surface tension, offering a comprehensive understanding of droplet interactions on different surfaces. This work contributes to the broader knowledge of fluid dynamics and surface engineering, with implications for applications in fields such as inkjet printing, coating technologies, and material science.", "Keywords": "", "DOI": "10.1016/j.jocs.2025.102537", "PubYear": 2025, "Volume": "85", "Issue": "", "JournalId": 628, "JournalTitle": "Journal of Computational Science", "ISSN": "1877-7503", "EISSN": "1877-7511", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Mechanics and Energy, Mohammed First University, Oujda 60000, Morocco;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory of Mechanics and Energy, Mohammed First University, Oujda 60000, Morocco"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Laboratory of Mechanics and Energy, Mohammed First University, Oujda 60000, Morocco"}], "References": []}, {"ArticleId": 143455250, "Title": "Multi-component gas sensing via spectral feature engineering", "Abstract": "We present a straightforward yet powerful spectral feature engineering technique designed to improve multi-species detection in complex mixtures. By applying convolutions of first derivatives with the composite spectra of target species before feeding the data into a convolutional neural network (CNN) model, this method significantly enhances the detection of weak absorbers and overlapping spectral features. To validate the approach, we developed a laser-based sensor that integrates wavelength tuning with a 1-D CNN model. The system utilizes a distributed feedback inter-band cascade laser operating near 3 . 34 μ m , enabling selective and simultaneous measurement of C 1 − C 3 hydrocarbons. Experiments were conducted at ambient conditions with a temporal resolution of 10 ms, while (intentionally) keeping the signal-to-noise ratio at relatively low levels. Gaseous mixtures contained methane, ethane, propane and propyne ranging in mole fraction values of 0%–1%, and ethylene mole fraction below 200 ppm. Ethylene was deliberately kept at very low levels to demonstrate the effectiveness of the feature engineering technique in detecting a weak absorbing species. The proposed method reduced the mean squared error by ten times compared to standard models. This demonstrates its potential for accurate detection in challenging environments.", "Keywords": "", "DOI": "10.1016/j.snb.2025.137285", "PubYear": 2025, "Volume": "430", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Physical Science and Engineering Division (PSE), King <PERSON> University of Science and Technology (KAUST), Department and Organization, Thuwal 23955-6900, Saudi Arabia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "The KAUST School (TKS), Thuwal 23955-6900, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Physical Science and Engineering Division (PSE), King <PERSON> University of Science and Technology (KAUST), Department and Organization, Thuwal 23955-6900, Saudi Arabia"}], "References": [{"Title": "High-sensitive double incidence multi-pass cell for trace gas detection based on TDLAS", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "412", "Issue": "", "Page": "135829", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Laser absorption spectroscopy based on dual-convolutional neural network algorithms for multiple trace gases analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "420", "Issue": "", "Page": "136476", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 143455251, "Title": "Human activity recognition: A review of deep learning‐based methods", "Abstract": "Human Activity Recognition (HAR) covers methods for automatically identifying human activities from a stream of data. End‐users of HAR methods cover a range of sectors, including health, self‐care, amusement, safety and monitoring. In this survey, the authors provide a thorough overview of deep learning based and detailed analysis of work that was performed between 2018 and 2023 in a variety of fields related to HAR with a focus on device‐free solutions. It also presents the categorisation and taxonomy of the covered publication and an overview of publicly available datasets. To complete this review, the limitations of existing approaches and potential future research directions are discussed.", "Keywords": "", "DOI": "10.1049/cvi2.70003", "PubYear": 2025, "Volume": "19", "Issue": "1", "JournalId": 11350, "JournalTitle": "IET Computer Vision", "ISSN": "1751-9632", "EISSN": "1751-9640", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Aberystwyth University  Aberystwyth UK"}, {"AuthorId": 2, "Name": "Tossapon <PERSON>", "Affiliation": "Department of Computer Science Aberystwyth University  Aberystwyth UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science Aberystwyth University  Aberystwyth UK"}], "References": [{"Title": "Imaging and fusing time series for wearable sensor-based human activity recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "80", "JournalTitle": "Information Fusion"}, {"Title": "A multimodal approach for human activity recognition based on skeleton and RGB data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "293", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "CAVIAR: Context-driven Active and Incremental Activity Recognition", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "196", "Issue": "", "Page": "105816", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Transferable two-stream convolutional neural network for human action recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "605", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Exploring a rich spatial–temporal dependent relational model for skeleton-based action recognition by bidirectional LSTM-CNN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "414", "Issue": "", "Page": "90", "JournalTitle": "Neurocomputing"}, {"Title": "Deep learning for pedestrian collective behavior analysis in smart cities: A model of group trajectory outlier detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "", "Page": "13", "JournalTitle": "Information Fusion"}, {"Title": "Learning temporal coherence via self-supervision for GAN-based video generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "A GAN‐based data augmentation method for human activity recognition via the caching ability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "5", "Page": "e257", "JournalTitle": "Internet Technology Letters"}, {"Title": "Efficient activity recognition using lightweight CNN and DS-GRU network for surveillance applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107102", "JournalTitle": "Applied Soft Computing"}, {"Title": "A robust and fast multispectral pedestrian detection deep network", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "227", "Issue": "", "Page": "106990", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Prediction and Description of Near-Future Activities in Video", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "210", "Issue": "", "Page": "103230", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Human interaction recognition framework based on interacting body part attention", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "108645", "JournalTitle": "Pattern Recognition"}, {"Title": "A Review of Deep Learning-based Human Activity Recognition on Benchmark Video Datasets", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "1", "Page": "2093705", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Investigating (re)current state-of-the-art in human activity recognition datasets", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "", "Page": "119", "JournalTitle": "Frontiers in Computer Science"}, {"Title": "Deep reinforcement learning for traffic signal control with consistent state and reward design approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "267", "Issue": "", "Page": "110440", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "From detection to understanding: A survey on representation learning for human-object interaction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "543", "Issue": "", "Page": "126243", "JournalTitle": "Neurocomputing"}, {"Title": "Understanding the vulnerability of skeleton-based Human Activity Recognition via black-box attack", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "110564", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 143455255, "Title": "Enhancing Music Audio Signal Recognition through CNN-BiLSTM Fusion with De-noising Autoencoder for Improved Performance", "Abstract": "This study presents an advanced framework for music audio signal recognition that combines Convolutional Neural Networks (CNNs), Bidirectional Long Short-Term Memory (BiLSTM) networks, and Noise Reduction Auto-encoder models to significantly improve accuracy and robustness. The core innovation is a novel noise reduction auto-encoder that integrates CNN and BiLSTM architectures, enabling superior recognition performance under varying noise levels and environmental conditions. The proposed framework, validated on several datasets including the Zhvoice, Common Voice, and LibriSpeech, demonstrates higher accuracy compared to existing methods. In addition, an optimized CNN architecture called Faster Region-based CNN with Multi-scale Information (FRCNN-MSI) is developed for efficient speech feature extraction, which shows significant improvements in noisy environments. The BiLSTM model is further enhanced with an attention mechanism that improves sequence modeling and contextual relationship capture. Together, these advances establish our approach as a robust solution to real-world speech recognition challenges, with potential implications for improving speech recognition systems in diverse applications.", "Keywords": "", "DOI": "10.1016/j.neucom.2025.129607", "PubYear": 2025, "Volume": "625", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Music, Soochow University, Suzhou, Jiangsu 215123, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Experimental arts school, Sichuan Conservatory, Chengdu, Sichuan 610021, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Arts, Sichuan University, Chengdu, Sichuan 610207, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Child Development and Education, Zhejiang Normal University, Hangzhou, Zhejiang 310012, China;Corresponding author"}], "References": [{"Title": "Combining CNN and LSTM for activity of daily living recognition with a 3D matrix skeleton representation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "175", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Research on hyper-spectral remote sensing image classification by applying stacked de-noising auto-encoders neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "14", "Page": "21219", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "CNN and LSTM based ensemble learning for human emotion recognition using EEG recordings", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "4", "Page": "4883", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "State of the art: a review of sentiment analysis based on sequential transfer learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "1", "Page": "749", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Model-based Reinforcement Learning: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; Aske Plaat", "PubYear": 2023, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "Foundations and Trends® in Machine Learning"}, {"Title": "Self-supervised Learning: A Succinct Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "4", "Page": "2761", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "When Deep is not Enough: Towards Understanding Shallow and Continual Learning Models in Realistic Environmental Sound Classification for Robots", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "20", "Issue": "5", "Page": "", "JournalTitle": "International Journal of Humanoid Robotics"}, {"Title": "TRNN: An efficient time-series recurrent neural network for stock price prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "657", "Issue": "", "Page": "119951", "JournalTitle": "Information Sciences"}, {"Title": "Bearing fault diagnosis base on multi-scale CNN and LSTM model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "4", "Page": "971", "JournalTitle": "Journal of Intelligent Manufacturing"}]}, {"ArticleId": 143455374, "Title": "ZEN-garden: Optimizing energy transition pathways with user-oriented data handling", "Abstract": "Welcome to the ZEN-garden: ZEN-garden is an open-source optimization software to model multi-year energy system transition pathways. To support research focused on the transition of sector-coupled energy systems toward net-zero emissions, ZEN-garden is built upon two principles: Optimizing highly complex sector-coupled energy transition pathways and supporting user-friendly data handling through small, flexible, and robust input datasets. ZEN-garden separates the codebase from the input data to allow for very diverse case studies. Lightweight and intuitive input datasets and unit consistency checks reduce user errors and facilitate using ZEN-garden for both novice and experienced energy system modelers.", "Keywords": "Energy system optimization; Transition pathway modeling; Sector-coupled energy systems; Open-source modeling; Mathematical modeling; Energy transition", "DOI": "10.1016/j.softx.2025.102059", "PubYear": 2025, "Volume": "29", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland;Authors contributed equally"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland;Authors contributed equally"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland;Huawei Technologies Switzerland, Zurich Research Center, Zurich, 8050 Zurich, Switzerland"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Institute of Energy and Process Engineering, ETH Zurich, Zurich, 8092 Zurich, Switzerland;Corresponding author"}], "References": [{"Title": "EMPIRE: An open-source model based on multi-horizon programming for energy transition analyses", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "", "Page": "100877", "JournalTitle": "SoftwareX"}, {"Title": "LEGO: The open-source Low-carbon Expansion Generation Optimization model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "101141", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 143455375, "Title": "Period3 modulates the NAD+-SIRT3 axis to alleviate depression-like behaviour by enhancing NAMPT activity in mice", "Abstract": "<p><b>INTRODUCTION</b>:PERIOD (PER)3 deficiency is associated with depression-like behaviors, but the underlying mechanisms remain unclear.</p><p><b>OBJECTIVES</b>:This study aims to elucidate the role and mechanism of PER3 in regulating depression-like behaviors in mice.</p><p><b>METHODS</b>:Depression-like behaviors were assessed using the sucrose preference test, tail suspension test, and forced swimming test. Metabolomic analysis was conducted on hippocampal tissues from Per3 knockout mice using chromatography-mass spectrometry. The regulatory role of PER3 on the expression of nicotinamide phosphoribosyltransferase (Nampt) was investigated through co-immunoprecipitation and chromatin immunoprecipitation assays.</p><p><b>RESULTS</b>:Metabolomic analysis revealed that Per3 deficiency disrupts mitochondrial function, as evidenced by reduced activities of key tricarboxylic acidcycle enzymes (succinate dehydrogenase, citrate synthase, and α-ketoglutarate dehydrogenase), diminished expression of mitochondrial respiratory chain complexes I-V, and decreased nicotinamide adenine dinucleotide (NAD)<sup>+</sup> levels in Per3 knockout mice. Supplementation with the NAD<sup>+</sup> precursor nicotinamide rescued mitochondrial function and alleviated depression-like behaviors in Per3 knockout mice. Similar effects were observed with intraperitoneal administration of the NAMPT activator P7C3-A20, while these effects were abolished by the NAMPT inhibitor FK866. Mechanistically, PER3 was found to regulate Nampt expression by binding to E-box elements within its intronic regions in conjunction with BMAL1. This interaction enhanced NAD<sup>+</sup> production, activating SIRT3 to mitigate mitochondrial dysfunction in Per3 knockout mice.</p><p><b>CONCLUSIONS</b>:These findings uncover a novel mechanism by which PER3 ameliorates depressive behaviors through the regulation of NAMPT-controlled NAD<sup>+</sup> levels and mitochondrial function, underscoring the critical role of PER3 in depression-related pathophysiology.</p><p>Copyright © 2025. Published by Elsevier B.V.</p>", "Keywords": "Depression-like behavior;Energy metabolism;Mitochondrial complex;NAD(+);Per3;SIRT3", "DOI": "10.1016/j.jare.2025.01.043", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Mental Health Center, Shanghai Jiao Tong University, School of Medicine, Shanghai 201108, China; Department of Pharmacology, University of Oxford, Mansfield Road OX1 3QT, Oxford, UK; Shanghai Key Laboratory of Psychotic Disorders, Brain Health Institute, Shanghai Mental Health Center, Shanghai 201108, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Biotechnology and Bioengineering, Zhejiang University of Technology, Hangzhou 310032, China."}, {"AuthorId": 3, "Name": "Ruonan Shu", "Affiliation": "College of Biotechnology and Bioengineering, Zhejiang University of Technology, Hangzhou 310032, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Nutritional and Metabolic Psychiatry, Affliated Brain Hospital, Guangzhou Medical University, Guangzhou, China; Key Laboratory of Neurogenetics and Channelopathies of Guangdong Province and the Ministry of Education of China, Guangzhou Medical University, Guangzhou, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Animal Science, South China Agricultural University, Guangzhou 510640, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Biotechnology and Bioengineering, Zhejiang University of Technology, Hangzhou 310032, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Biotechnology and Bioengineering, Zhejiang University of Technology, Hangzhou 310032, China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Biotechnology and Bioengineering, Zhejiang University of Technology, Hangzhou 310032, China."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Ecology, Lishui University, Lishui 323000, China."}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Nutritional and Metabolic Psychiatry, Affliated Brain Hospital, Guangzhou Medical University, Guangzhou, China; Key Laboratory of Neurogenetics and Channelopathies of Guangdong Province and the Ministry of Education of China, Guangzhou Medical University, Guangzhou, China"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Pharmacology, University of Oxford, Mansfield Road OX1 3QT, Oxford, UK."}], "References": []}, {"ArticleId": 143455415, "Title": "Task-adaptive unbiased regularization meta-learning for few-shot cross-domain fault diagnosis", "Abstract": "Model-agnostic meta-learning (MAML) has been successfully integrated with deep learning models and applied to few-shot cross-domain fault diagnosis. However, existing meta-learning strategies often lead to issues such as task conflict and task bias in deep learning models when dealing with new tasks. Therefore, a task-adaptive unbiased regularized meta-learning (TUBR-ML) method is proposed. The proposed method includes the following three innovations: First, we constructed a structured discard feature extractor (SDFE) based on convolutional neural networks (CNN), incorporating a shared feature extraction module to extract correlated features across different tasks, thereby reducing the impact of task differences on training stability and robustness. Second, a task-adaptive attenuation module is introduced to adjust the model's initialization parameters, optimizing the meta-inner loop learning strategy and resolving conflicts between new and old tasks by dynamically adjusting for task-specific requirements. Finally, to address the task bias effect, we propose a task-unbiased regularized loss (TURL), which utilizes a pre-extracted task-unbiased prior to constrain parameter updates, preventing the model from overfitting to existing tasks and enhancing its generalization ability to new tasks. TUBR-ML demonstrates diagnostic accuracy of 92.52%, 96.44%, and 99.26% for the Case Western Reserve University (CWRU) datasets, wind turbine datasets, and rail transit datasets, respectively, under few-shot conditions. Compared to other methods, TUBR-ML shows significant improvement.", "Keywords": "", "DOI": "10.1016/j.engappai.2025.110200", "PubYear": 2025, "Volume": "144", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical & Electrical Engineering, Beijing University of Chemical Technology, Beijing, 100029, People's Republic of China"}, {"AuthorId": 2, "Name": "Dongrui Lv", "Affiliation": "College of Mechanical & Electrical Engineering, Beijing University of Chemical Technology, Beijing, 100029, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical & Electrical Engineering, Beijing University of Chemical Technology, Beijing, 100029, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical & Electrical Engineering, Beijing University of Chemical Technology, Beijing, 100029, People's Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical & Electrical Engineering, Beijing University of Chemical Technology, Beijing, 100029, People's Republic of China;State Key Laboratory of High-end Compressor and System Technology, Beijing University of Chemical Technology, Beijing, People's Republic of China;Corresponding author. College of Mechanical & Electrical Engineering, Beijing University of Chemical Technology, Beijing, 100029, People's Republic of China"}], "References": [{"Title": "Feature selection in image analysis: a survey", "Authors": "<PERSON>erón<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "4", "Page": "2905", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A comprehensive review on convolutional neural network in machine fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "36", "JournalTitle": "Neurocomputing"}, {"Title": "A metrics-based meta-learning model with meta-pretraining for industrial knowledge graph construction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "143", "Issue": "", "Page": "103753", "JournalTitle": "Computers in Industry"}, {"Title": "Automated broad transfer learning for cross-domain fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "", "Page": "27", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Attention-based deep meta-transfer learning for few-shot fine-grained fault diagnosis", "Authors": "Chuanjiang Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "264", "Issue": "", "Page": "110345", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Generalized MAML for few-shot cross-domain fault diagnosis of bearing driven by heterogeneous signals", "Authors": "<PERSON><PERSON>; Haidong Shao; Xiangdong Zhou", "PubYear": 2023, "Volume": "230", "Issue": "", "Page": "120696", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A meta-learning network with anti-interference for few-shot fault diagnosis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "552", "Issue": "", "Page": "126551", "JournalTitle": "Neurocomputing"}, {"Title": "Novel joint transfer fine-grained metric network for cross-domain few-shot fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "279", "Issue": "", "Page": "110958", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A federated recommendation algorithm based on user clustering and meta-learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "158", "Issue": "", "Page": "111483", "JournalTitle": "Applied Soft Computing"}, {"Title": "Wavelet-driven differentiable architecture search for planetary gear fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "74", "Issue": "", "Page": "587", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Frontiers and developments of data augmentation for image: From unlearnable to learnable", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2025, "Volume": "114", "Issue": "", "Page": "102660", "JournalTitle": "Information Fusion"}, {"Title": "Generalization in neural networks: A broad survey", "Authors": "<PERSON>", "PubYear": 2025, "Volume": "611", "Issue": "", "Page": "128701", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 143455416, "Title": "A parameter-free self-training algorithm based on the three successive confirmation rule", "Abstract": "Semi-supervised learning is a popular research topic today, and self-training is a classical semi-supervised learning framework. How to select high-confidence samples in self-training is a critical step. However, the existing algorithms do not consider both global and local information of the data. In the paper, we propose a parameter-free self-training algorithm based on the three successive confirmation rule, which integrates global and local information to identify high-confidence samples. Concretely, the local information is obtained by using k nearest neighbors and global information is derived from the three successive confirmation rule. This dual selection strategy helps to improve the quality of high-confidence samples and further improve the performance of classification. We conduct experiments on 14 benchmark datasets, comparing our method with other self-training algorithms. We use accuracy and F-score as performance metrics. The experimental results demonstrate that our algorithm significantly improves classification performance, proving its effectiveness and superiority in semi-supervised learning.", "Keywords": "", "DOI": "10.1016/j.engappai.2025.110165", "PubYear": 2025, "Volume": "144", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering and Artificial Intelligence, Lanzhou University of Finance and Economics, Lanzhou 730020, Gansu, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information Engineering and Artificial Intelligence, Lanzhou University of Finance and Economics, Lanzhou 730020, Gansu, China"}, {"AuthorId": 3, "Name": "Qingsheng Shang", "Affiliation": "College of Information Engineering and Artificial Intelligence, Lanzhou University of Finance and Economics, Lanzhou 730020, Gansu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Optics and ElectroNics(iOPEN), Northwestern Polytechnical University, Xi’an 710072, Shaanxi, China"}], "References": [{"Title": "Graph-based semi-supervised learning: A review", "Authors": "<PERSON><PERSON>; <PERSON>; Qing Yan", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "216", "JournalTitle": "Neurocomputing"}, {"Title": "A semi-supervised self-training method based on density peaks and natural neighbors", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "2939", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Joint exploring of risky labeled and unlabeled samples for safe semi-supervised clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "176", "Issue": "", "Page": "114796", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Self-training method based on GCN for semi-supervised short text classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "611", "Issue": "", "Page": "18", "JournalTitle": "Information Sciences"}, {"Title": "A survey of multi-label classification based on supervised and semi-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "697", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Fast semi-supervised self-training algorithm based on data editing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "626", "Issue": "", "Page": "293", "JournalTitle": "Information Sciences"}, {"Title": "Recent Advances in Natural Language Processing via Large Pre-trained Language Models: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 143455443, "Title": "Strategic technological innovation through ChatMu: transforming information accessibility in Muhammadiyah", "Abstract": "<p>This study examines the effectiveness of the ChatMu application in improving access to information for members of Muhammadiyah, a prominent socio-religious organization. The research employs a mixed-methods approach, combining qualitative and quantitative analyses to evaluate the application’s performance, usability, and user satisfaction. Findings reveal that ChatMu significantly enhances the accessibility and accuracy of Muhammadiyah-related information, highlighting its potential as an innovative tool for addressing community-specific information needs. However, several usability challenges were identified, including navigation inefficiencies and inconsistencies in content delivery. These limitations suggest the need for further refinement to optimize user experience and functionality. Despite these issues, ChatMu demonstrates strong capabilities in providing relevant and reliable information, fostering digital literacy, and supporting information dissemination within the Muhammadiyah community. The study concludes that ChatMu represents a promising application of chatbot technology in empowering communities through improved access to knowledge. Future development efforts should focus on comprehensive usability testing, maintaining information relevance, and incorporating advanced interactive features to enhance engagement. With continuous improvements, ChatMu has the potential to become an effective medium for advancing literacy and knowledge-sharing in the Muhammadiyah community.</p>", "Keywords": "artificial intelligence; digital literacy; ChatMu application; chatbot technology; Muhammadiyah; system usability scale; user engagement", "DOI": "10.3389/frai.2025.1446590", "PubYear": 2025, "Volume": "8", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Communication and Informatics, Universitas Muhammadiyah Suraka<PERSON>, Indonesia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Communication and Informatics, Universitas Muhammadiyah Surakarta, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Teacher Training and Education, Universitas Muhammadiyah Surakarta, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Teacher Training and Education, Universitas Muhammadiyah Surakarta, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Teacher Training and Education, Universitas Muhammadiyah Surakarta, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Teacher Training and Education, Universitas Muhammadiyah Maumere, Indonesia"}], "References": [{"Title": "The Design and Implementation of XiaoIce, an Empathetic Social Chatbot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "1", "Page": "53", "JournalTitle": "Computational Linguistics"}, {"Title": "The Usability Analysis Online Learning Site for Supporting Computer programming Course Using System Usability Scale (SUS) in a University", "Authors": "<PERSON><PERSON><PERSON> Derisma", "PubYear": 2020, "Volume": "14", "Issue": "9", "Page": "182", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "The Chatbot Usability Scale: the Design and Pilot of a Usability Scale for Interaction with AI-Based Conversational Agents", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "95", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Future directions for chatbot research: an interdisciplinary research agenda", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "12", "Page": "2915", "JournalTitle": "Computing"}, {"Title": "Turning the blackbox into a glassbox: An explainable machine learning approach for understanding hospitality customer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "2", "Page": "100050", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Automatic detection of cognitive impairment in elderly people using an entertainment chatbot with Natural Language Processing capabilities", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "12", "Page": "16283", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Speech Classification to Recognize Emotion Using Artificial Neural Network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Khazanah Informatika: <PERSON><PERSON> Ilmu <PERSON> dan Informatika"}, {"Title": "Assessing Students' Perceptions of Mobile Applications Usability using System Usability Scale", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "19", "Issue": "1", "Page": "11", "JournalTitle": "Journal of Computer Science"}, {"Title": "When Operation Technology Meets Information Technology: Challenges and Opportunities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "95", "JournalTitle": "Future Internet"}, {"Title": "Validation of system usability scale as a usability metric to evaluate voice user interfaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "", "Page": "e1918", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Effect of Chatbot-Assisted Learning on Students’ Learning Motivation and Its Pedagogical Approaches", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "1", "Page": "69", "JournalTitle": "Khazanah Informatika: <PERSON><PERSON> Ilmu <PERSON> dan Informatika"}]}, {"ArticleId": 143455481, "Title": "Learning and generating vehicle motion primitives from human-driving data", "Abstract": "<p>Motion primitives play an important role in motion planning for autonomous vehicles, as they effectively address the sampling challenges inherent in nonholonomic motion planning. Employing motion primitives (MPs) is a widely accepted approach in nonholonomic motion planning based on sampling. This study specifically addresses the problem of learning from human-driving data to create human-like trajectories from predefined start-to-end states, which then serve as MP within the sampling-based nonholonomic motion planning framework. In this paper, we propose a deep learning-based method for generating MP that capture human-driving trajectory data features. By processing human-driving trajectory data, we create a Motion Primitive dataset that uniformly covers typical urban driving scenarios. Based on this dataset, a vehicle model long short-term memory neural network model is constructed to learn the features of the human-driving trajectory data. Finally, a framework for the generation of MP for practical applications is given based on this neural network. Our experiments, which focus on the dataset, the MMP generation network, and the generation process, demonstrate that our method significantly improves the training efficacy of the MP generation network. Additionally, the MP generated by our method exhibit higher accuracy compared to traditional methods.</p>", "Keywords": "", "DOI": "10.1017/S0263574724001681", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 10996, "JournalTitle": "Robotica", "ISSN": "0263-5747", "EISSN": "1469-8668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Zhang", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Tao", "Affiliation": ""}], "References": [{"Title": "Motion Adaptation Based on Learning the Manifold of Task and Dynamic Movement Primitive Parameters", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "7", "Page": "1299", "JournalTitle": "Robotica"}, {"Title": "Human-like motion planning of robotic arms based on human arm motion patterns", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "41", "Issue": "1", "Page": "259", "JournalTitle": "Robotica"}, {"Title": "Path planning for robots in preform weaving based on learning from demonstration", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "4", "Page": "1153", "JournalTitle": "Robotica"}]}, {"ArticleId": 143455673, "Title": "THE INFLUENCE OF MAJOR EXPERTISE COURSES ON ALUMNI EMPLOYMENT USING THE APRIORI METHOD", "Abstract": "<p>The role of alumni in university progress and quality is vital. This study used data from the tracer study application to analyze the relationship between skill courses and alumni employment. The data mining technique of association was employed to find linkages between different parameters. The Apriori algorithm was used to identify patterns that described the relationship between skill courses and alumni employment. The findings revealed that the most sought-after professions by alumni of the Informatics Engineering Study Program were educators, such as teachers and lecturers, with a support value of 18.7692%. Programmers were also in high demand, with a support value of 15.3846%. The subjects that were found to have the greatest influence on employment were Database, Computer Network, Computer Human Interaction, and Software Engineering. These findings provide valuable insights for the Informatics Engineering Study Program to prioritize and enhance these influential courses in terms of curriculum, teaching methods, and teaching materials, with the aim of improving the relevancy and quality of the courses in supporting alumni employment.</p>", "Keywords": "", "DOI": "10.24014/coreit.v10i2.34144", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 61586, "JournalTitle": "Jurnal CoreIT: <PERSON><PERSON> <PERSON><PERSON> dan Teknologi Informasi", "ISSN": "2460-738X", "EISSN": "2599-3321", "Authors": [{"AuthorId": 1, "Name": "<PERSON> (Scopus ID: 57204261647)", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Siska Kurnia Gusti", "Affiliation": ""}], "References": []}, {"ArticleId": 143455680, "Title": "Comparative Analysis Between Advanced Encryption Standard and Fully Homomorphic Encryption Algorithm to Secure Data in Financial Technology Applications", "Abstract": "<p>This research discusses the comparison between two encryption algorithms, namely Advanced Encryption Standard (AES) and Fully Homomorphic Encryption (FHE), in the context of data security in Financial Technology (Fintech) applications. The main aim of this research is to analyze the speed and efficiency of the two algorithms to provide information and motivation to Fintech Application business actors to determine the right algorithm for securing data. The research results show that AES is faster and more efficient in terms of encryption and decryption compared to FHE. For encryption, the AES algorithm is 1,100 times faster than the FHE algorithm. For decryption, the AES algorithm is 581 times faster than the FHE algorithm. For arithmetic processing, AES is 132 times faster than FHE. CPU consumption for AES encryption is 35.93% lower CPU usage than FHE. In AES decryption 10.31% lower than FHE for CPU usage. In the arithmetic process AES is 9.33% lower in usage than FHE. For memory usage in the FHE encryption process, it has an advantage, namely 2.3 times lower than AES for memory usage. During decryption, AES memory usage is superior with memory consumption 54 times lower than FHE. For the arithmetic process, AES uses 4.3 times lower memory than FHE. Overall AES provides speed and low resource consumption, this makes AES very suitable for use in Fintech applications that require speed and efficiency. Even though FHE has advantages in memory usage during encryption alone, this is not enough because it takes a long time to carry out the encryption process. This research suggests that further research will attempt to make the FHE algorithm more efficient and faster in processing data, this is considering the potential of FHE which is able to process encrypted data</p>", "Keywords": "", "DOI": "10.24014/coreit.v10i2.30809", "PubYear": 2024, "Volume": "10", "Issue": "2", "JournalId": 61586, "JournalTitle": "Jurnal CoreIT: <PERSON><PERSON> <PERSON><PERSON> dan Teknologi Informasi", "ISSN": "2460-738X", "EISSN": "2599-3321", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143455755, "Title": "Direct Rendering of Intrinsic Triangulations", "Abstract": "<p>Existing intrinsic triangulation frameworks represent powerful tools for geometry processing; however, they all require the extraction of the common subdivision between extrinsic and intrinsic triangulations for visualization and optimized data transfer. We describe an efficient and effective algorithm for directly rendering intrinsic triangulations that avoids extracting common subdivisions. Our strategy is to use GPU shaders to render the intrinsic triangulation while rasterizing extrinsic triangles. We rely on a point-location algorithm supported by a compact data structure, which requires only two values per extrinsic triangle to represent the correspondence between extrinsic and intrinsic triangulations. This data structure is easier to maintain than previous proposals while supporting all the standard topological operations for improving the intrinsic mesh quality, such as edge flips, triangle refinements, and vertex displacements. Computational experiments show that the proposed data structure is numerically robust and can process nearly degenerate triangulations. We also propose a meshless strategy to accurately transfer data from intrinsic to extrinsic triangulations without relying on the extraction of common subdivisions.</p>", "Keywords": "", "DOI": "10.1145/3716314", "PubYear": 2025, "Volume": "44", "Issue": "1", "JournalId": 15014, "JournalTitle": "ACM Transactions on Graphics", "ISSN": "0730-0301", "EISSN": "1557-7368", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science, Pontifical Catholic University of Rio de Janeiro, Rio de Janeiro, Brazil"}], "References": [{"Title": "A Laplacian for Nonmanifold Triangle Meshes", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "69", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Nonlinear spectral geometry processing via the TV transform", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "You can find geodesic paths in triangle meshes by just flipping edges", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Integer coordinates for intrinsic geometry processing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Compatible intrinsic triangulations", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 143455790, "Title": "Conceptual structure of a decision support system for situational video analytics for multi-object recognition", "Abstract": "<p>Information is provided on the growing interest in situational video analytics (SVA) technologies in various fields of activity. The paper describes a trend towards the implementation of SVA systems based on cognitive artificial intelligence technologies, which make it possible to detect an object and its location in a video stream in real time with a high degree of accuracy. The architecture of a decision support system (DSS) with the possibility of multi-object recognition is proposed. The key logical components of the DSS in the field of SVA are highlighted, as well as the functions and purpose of each component are described. The role of the decision maker for implementing multi-object recognition when searching for an object in a video stream is particularly emphasized. The classification of neural networks by types and fields of application is given, and it is revealed that convolutional neural networks are used to solve video analytics problems. Examples of using the YOLOv5 convolutional model in the DSS model database management system in IAS tasks for detecting the presence of objects on video data are considered. A block diagram of the SVA object recognition algorithm has been developed. A series of experiments has been conducted to train a convolutional neural network model on a unique dataset, on an expanded dataset, and with a new additional object. This organization of experiments is aimed at improving the quality and accuracy of object recognition and exploring the possibility of multi-object recognition. As a result of the experiment, the final trained neural network model was tested and its potential capabilities were analyzed for use in the SVA DSS, taking into account the accuracy of the model. The accuracy was 91.6% for the validation set containing 2 objects. The results obtained using a trained neural network of the YOLOv5 architecture confirms the importance of convolutional neural networks as a key component of the database of SVA DSS models.</p>", "Keywords": "", "DOI": "10.24143/2072-9502-2025-1-69-79", "PubYear": 2025, "Volume": "2025", "Issue": "1", "JournalId": 47501, "JournalTitle": "VESTNIK OF ASTRAKHAN STATE TECHNICAL UNIVERSITY. SERIES: MANAGEMENT, COMPUTER SCIENCE AND INFORMATICS", "ISSN": "2072-9502", "EISSN": "2224-9761", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Astrakhan State Technical  University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Astrakhan State Technical University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Astrakhan <PERSON>hchev State University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Astrakhan <PERSON>hchev State University"}], "References": []}, {"ArticleId": 143455805, "Title": "Analysis of the nearest attack moment in critical infrastructure objects with a gradual change in the intensity of attacks over time", "Abstract": "<p>Based on the apparatus of regenerating processes, a model is analyzed that describes the process of malicious attacks on a protected object of critical information infrastructure (CII). It is assumed that the intervals between the successive moments of preparation and implementation of malicious attacks are completely independent. These intervals are quite large for each of the attack sources, and the sources do not communicate with each other. The probability of a successful attack from a single source is quite low, so the moments of a successful attack for each of the attack sources are far enough apart from each other. As a result, the event associated with the successful completion of the attack for the attacker is a rare event. However, due to the fact that a successful attack on a CII facility generates large losses and costs, it becomes necessary to study the process under consideration. The main characteristic under study within the framework of the described model is the first (nearest) moment of successful malicious attack. This characteristic is one of the most important for the process of countering attacks. Numerical estimates of the parameters of the moment of the first successful attack will make it possible to more adaptively organize the process of countering attacks by implementing additional information security measures at the most dangerous and vulnerable time intervals. The analysis of this characteristic was performed in the context of the heterogeneity of the behavior of the entire process of countering attacks and countering them. It is assumed that the intervals between successive moments of attacks and the probabilities of attack success vary over time according to a power law on average. This assumption reflects the process of dynamic change in the sources of attacks and in the protection system of the CII facility. An asymptotic relationship is derived for the normal value of the moment of the first attack in conditions when the intervals between successive moments of attacks grow indefinitely, but at the same time the number of attack sources increases so that the average proportion of successful attacks tends to a certain non-zero limit. Asymptotic expressions for the average time to the nearest attack are obtained.</p>", "Keywords": "", "DOI": "10.24143/2072-9502-2025-1-46-55", "PubYear": 2025, "Volume": "2025", "Issue": "1", "JournalId": 47501, "JournalTitle": "VESTNIK OF ASTRAKHAN STATE TECHNICAL UNIVERSITY. SERIES: MANAGEMENT, COMPUTER SCIENCE AND INFORMATICS", "ISSN": "2072-9502", "EISSN": "2224-9761", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Astrakhan State Technical University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Astrakhan State Technical University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Astrakhan State Technical  University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Astrakhan State Technical University"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Astrakhan State Technical  University"}], "References": []}, {"ArticleId": 143455926, "Title": "Predicted label distribution guided optimal transport for classification of remote sensing smoke-like scenes with noisy labels", "Abstract": "Remote sensing (RS) images have been used in a large amount of applications due to their wide coverage and good timeliness. For fire monitoring, smoke in RS images is regarded as the indicator of fire disasters and it is of great significance to recognize RS smoke. However, some RS scenes are visually similar to smoke, e.g, cloud, dust and haze. The high inter-class similarities not only make classification of these smoke-like scenes a tough task, but also will lead to the appearance of inaccurately labeled samples when collecting corresponding datasets. The supervised classification task relies highly on accurately labeled samples, and models will overfit to noisy labels when training on inaccurately labeled datasets. In this work, the optimal transport (OT) theory is utilized to deal with noisy labels in classification of RS smoke-like scenes. Served as a loss function, OT objective enables closer distance between samples which are mapped with each other in the transportation plan. In scene classification, each sample has a class label and the OT objective could be computed from both the feature space and the label space. Considering the existence of noisy labels, we use predicted label distributions to amplify the dissimilarities between feature representations, thereby reducing mis-mappings in the OT transportation plan. The proposed algorithm is named as predicted label distribution guided optimal transport (PLDGOT) and an extensive experiments show PLDGOT achieves remarkable performances in classification of RS smoke-like scenes with noisy labels.", "Keywords": "", "DOI": "10.1016/j.neucom.2025.129540", "PubYear": 2025, "Volume": "625", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Southeast University, Nanjing, 210096, PR China;Key Laboratory of Measurement and Control of Complex Systems of Engineering, Ministry of Education, Nanjing, 210096, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Southeast University, Nanjing, 210096, PR China;Key Laboratory of Measurement and Control of Complex Systems of Engineering, Ministry of Education, Nanjing, 210096, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Bank of Nanjing, Nanjing, 210019, PR China"}, {"AuthorId": 4, "Name": "Xiaobo Lu", "Affiliation": "School of Automation, Southeast University, Nanjing, 210096, PR China;Key Laboratory of Measurement and Control of Complex Systems of Engineering, Ministry of Education, Nanjing, 210096, PR China;Corresponding author at: School of Automation, Southeast University, Nanjing, 210096, PR China"}], "References": [{"Title": "An Entropic Optimal Transport loss for learning deep neural networks under label noise in remote sensing images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "191", "Issue": "", "Page": "102863", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "Global2Salient: Self-adaptive feature aggregation for remote sensing smoke detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "466", "Issue": "", "Page": "202", "JournalTitle": "Neurocomputing"}, {"Title": "Unsupervised Domain Adaptation via Deep Conditional Adaptation Network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109088", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 143455934, "Title": "Interval many-objective evolutionary algorithm guided by dynamic dual-sequence mechanism", "Abstract": "Interval many-objective evolutionary algorithms (IMaOEAs) have received significant achievements in recent years. However, it is difficult for the algorithm to quickly select good interval individuals since the uncertainty affects the definition of interval dominance relations. To further reduce the computational burden of uncertainty on the interval optimization process, this paper proposes a dual-sequence mechanism-guided interval many-objective evolutionary algorithm. First, the interval binary R2 evaluation indicator IR2 was designed, which can effectively evaluate the convergence and diversity of interval individuals. Second, an uncertainty dominance relation for interval individuals is proposed and uncertainty is quantified using the weighted LP norm (WLP). Finally, the dynamic dual-sequence (DDS) mechanism was ultimately employed to retain the most exceptional individuals within the population, while simultaneously eliminating those with subpar performance in terms of convergence, diversity, and uncertainty. To extensively evaluate the performance of the proposed approach, 16 benchmark problems were used as the test suite. The experimental results demonstrate that the approach outperforms five advanced interval many-objective evolutionary algorithms, showcasing its superior performance and competitiveness.", "Keywords": "", "DOI": "10.1016/j.swevo.2025.101870", "PubYear": 2025, "Volume": "94", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>ju<PERSON>", "Affiliation": "Shanxi Key Laboratory of Big Data Analysis and Parallel Computing, Taiyuan University of Science and Technology, Taiyuan, 030024, Shanxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanxi Key Laboratory of Big Data Analysis and Parallel Computing, Taiyuan University of Science and Technology, Taiyuan, 030024, Shanxi, China;Correspondence to: Shanxi Key Laboratory of Big Data Analysis and Parallel Computing, Taiyuan University of Science and Technology, China.; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shanxi Key Laboratory of Big Data Analysis and Parallel Computing, Taiyuan University of Science and Technology, Taiyuan, 030024, Shanxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanxi Key Laboratory of Big Data Analysis and Parallel Computing, Taiyuan University of Science and Technology, Taiyuan, 030024, Shanxi, China"}, {"AuthorId": 5, "Name": "Qingyuan Xu", "Affiliation": "Shanxi Key Laboratory of Big Data Analysis and Parallel Computing, Taiyuan University of Science and Technology, Taiyuan, 030024, Shanxi, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Control and Management of Complex Systems, Institute of Automation Chinese Academy of Sciences, Beijing, 100190, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, Swinburne University of Technology, Melbourne, Victoria, Australia"}], "References": [{"Title": "A credibility-based fuzzy programming model for the hierarchical multimodal hub location problem with time uncertainty in cargo delivery systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "1413", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "An efficient interval many-objective evolutionary algorithm for cloud task scheduling problem under uncertainty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "583", "Issue": "", "Page": "56", "JournalTitle": "Information Sciences"}, {"Title": "Kernel-based hybrid multi-objective optimization algorithm (KHMO)", "Authors": "<PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON><PERSON>-<PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "416", "JournalTitle": "Information Sciences"}, {"Title": "Solving capacitated vehicle routing problem with route optimisation based on equilibrium optimiser algorithm", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Rizk M. Riz<PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "1", "Page": "13", "JournalTitle": "International Journal of Computing Science and Mathematics"}, {"Title": "An enhanced multi-objective particle swarm optimisation with <PERSON> flight", "Authors": "<PERSON>; <PERSON>; <PERSON> qun <PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "1", "Page": "79", "JournalTitle": "International Journal of Computing Science and Mathematics"}, {"Title": "Global optimisation procedures for solving systems of Hölder equations-inequations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "3", "Page": "266", "JournalTitle": "International Journal of Computing Science and Mathematics"}, {"Title": "An option pricing model with adaptive interval-valued fuzzy numbers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "4", "Page": "371", "JournalTitle": "International Journal of Computing Science and Mathematics"}, {"Title": "Application of particle swarm optimisation algorithm in manipulator compliance control", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "2", "Page": "113", "JournalTitle": "International Journal of Computing Science and Mathematics"}]}, {"ArticleId": 143456008, "Title": "DSC-GRUNet: A Lightweight Neural Network Model for Multimodal Gesture Recognition Based on Depthwise Separable Convolutions and GRU", "Abstract": "With the advancement of human-computer interaction (HCI) technology, gesture recognition methods based on electromyography (EMG) signals have garnered widespread attention, particularly in fields such as rehabilitation medicine and smart prosthetics. However, traditional EMG-based gesture recognition methods face challenges, including insufficient accuracy and poor noise resistance when handling complex gestures and diverse scenarios. To address these challenges, this study proposes a lightweight gesture recognition network based on multimodal signal fusion, combining surface EMG and Acceleration (ACC) signals. The proposed model integrates Depthwise Separable Convolutions (DSC) and Gated Recursive Units (GRU) to achieve a lightweight design while maintaining recognition performance. Experimental results demonstrate that the proposed method achieves recognition accuracies of 92.03±3.28 % and 77.48±4.38 % on the NinaPro DB2 and DB5 datasets, respectively, outperforming other state-of-the-art methods in terms of efficiency and computational cost. Additionally, the fusion of multimodal data significantly enhances the recognition performance of dynamic gestures. This study provides new insights into the design of embedded, real-time gesture recognition systems and holds important practical implications.", "Keywords": "", "DOI": "10.1016/j.patrec.2025.02.008", "PubYear": 2025, "Volume": "190", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electric Engineering, University of Yanshan, Qinhuangdao, Hebei, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electric Engineering, University of Yanshan, Qinhuangdao, Hebei, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electric Engineering, University of Yanshan, Qinhuangdao, Hebei, China;Key Laboratory of Measurement Technology and Instrumentation of Hebei Province, Institute of Electric Engineering, Yanshan University, Qinhuangdao 066004, China;Corresponding authors at: School of Electric Engineering, University of Yanshan, Qinhuangdao, Hebei, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electric Engineering, University of Yanshan, Qinhuangdao, Hebei, China;Key Laboratory of Measurement Technology and Instrumentation of Hebei Province, Institute of Electric Engineering, Yanshan University, Qinhuangdao 066004, China;Corresponding authors at: School of Electric Engineering, University of Yanshan, Qinhuangdao, Hebei, China"}], "References": [{"Title": "Human-robot cooperative control based on sEMG for the upper limb exoskeleton robot", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "125", "Issue": "", "Page": "103350", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Transformer-based network with temporal depthwise convolutions for sEMG recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "145", "Issue": "", "Page": "109967", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 143456067, "Title": "Laser-ultrasound cognitive transformation of energy and information in an electric drive", "Abstract": "<p>Energy-efficiency in contemporary electromachine technology depend on characteristics electrodrives, that requires optimization his exist and new structural elements. A model of optoelectronic electrodrive with laser-ultrasound cognitive transformation of energy and information is investigated. Model includes following basic elements: electromotor, driving a working mechanism; measuring-information complex, controlling and operating electromotor; amplifier on basis multi-modes solid laser with ultrasound quality modulator (UQML); transformer of laser pulses into voltage either current or magnetic field of electromotor. Researches dynamics of generating depend on pumping power and ultrasound intensity executioned on the rubin laser. Transition characteristic of electrodrive is defines by inertia of electromechanic elements, considerably exceed inertia of optoelectronic equipments. Inertia of electrodrive smooth out pulses of frequency-impulse energy (FIE). When estimation transition characteristic of electrodrive, take into account his inertia, FIE of laser radiation was accepted quasyconstant in time interval constant energy of pumping source and estimates alteration of rotation frequency and current force of electromotor were conducting on basic laws Kirchgof and Newton in approximation constant current by medium Matlab, Simulink. In the absence of ultrasound UQML radiates a chaotic optical impulse. By ultrasound control UQML passes into establish regime of frequency-impulse radiation. Repetition frequency of regular pulses is determination by pumping level when ultrasound power is constant, but did not depend on ultrasound frequency. Examined questions: self-regulation frequency-pulse generation, cognitive transformation of UQML; dynamics of electrodrive (ED) with UQML; equivalent powers sources of frequency-pulse and constant current; inertness ED with UQML; structure scheme of automatic ED with UQML. Self-regulation of UQML attach to increase energy-efficiency, possibility automatical of electrodrive.</p>", "Keywords": "", "DOI": "10.24143/2072-9502-2025-1-39-45", "PubYear": 2025, "Volume": "2025", "Issue": "1", "JournalId": 47501, "JournalTitle": "VESTNIK OF ASTRAKHAN STATE TECHNICAL UNIVERSITY. SERIES: MANAGEMENT, COMPUTER SCIENCE AND INFORMATICS", "ISSN": "2072-9502", "EISSN": "2224-9761", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Admiral <PERSON><PERSON><PERSON> State University of Maritime and Inland Shipping"}], "References": []}, {"ArticleId": 143456106, "Title": "DeepMine-multi-TTS: a Persian speech corpus for multi-speaker text-to-speech", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10579-025-09807-6", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 3989, "JournalTitle": "Language Resources and Evaluation", "ISSN": "1574-020X", "EISSN": "1574-0218", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143456250, "Title": "Quantum algorithms used in trust-centric AI applications", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJGUC.2024.10069180", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 19428, "JournalTitle": "International Journal of Grid and Utility Computing", "ISSN": "1741-847X", "EISSN": "1741-8488", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143456268, "Title": "Intelligent Virtual Reality and Augmented Reality Technologies: An Overview", "Abstract": "<p>The research into artificial intelligence (AI), the metaverse, and extended reality (XR) technologies, such as augmented reality (AR), virtual reality (VR), and mixed reality (MR), has been expanding over the recent years. This study aims to provide an overview regarding the combination of AI with XR technologies and the metaverse through the examination of 880 articles using different approaches. The field has experienced a 91.29% increase in its annual growth rate, and although it is still in its infancy, the outcomes of this study highlight the potential of these technologies to be effectively combined and applied in various domains transforming and enriching them. Through content analysis and topic modeling, the main topics and areas in which this combination is mostly being researched and applied are as follows: (1) “Education/Learning/Training”, (2) “Healthcare and Medicine”, (3) “Generative artificial intelligence/Large language models”, (4) “Virtual worlds/Virtual avatars/Virtual assistants”, (5) “Human-computer interaction”, (6) “Machine learning/Deep learning/Neural networks”, (7) “Communication networks”, (8) “Industry”, (9) “Manufacturing”, (10) “E-commerce”, (11) “Entertainment”, (12) “Smart cities”, and (13) “New technologies” (e.g., digital twins, blockchain, internet of things, etc.). The study explores the documents through various dimensions and concludes by presenting the existing limitations, identifying key challenges, and providing suggestions for future research.</p>", "Keywords": "", "DOI": "10.3390/fi17020058", "PubYear": 2025, "Volume": "17", "Issue": "2", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Informatics, School of Information Sciences, University of Macedonia, 54636 Thessaloniki, Greece; Department of Education, School of Education, University of Nicosia, Nicosia 2417, Cyprus"}], "References": [{"Title": "Applications of Artificial Intelligence and Machine learning in smart cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "313", "JournalTitle": "Computer Communications"}, {"Title": "Applications of virtual reality in maintenance during the industrial product lifecycle: A systematic review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "56", "Issue": "", "Page": "525", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A review on Virtual Reality and Augmented Reality use-cases of Brain Computer Interface based applications for smart cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "88", "Issue": "", "Page": "104392", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "AI-Based Modeling: Techniques, Applications and Research Issues Towards Automation, Intelligent and Smart Systems", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Artificial Intelligence‐Enabled Sensing Technologies in the 5G/Internet of Things Era: From Virtual Reality/Augmented Reality to the Digital Twin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "7", "Page": "2100228", "JournalTitle": "Advanced Intelligent Systems"}, {"Title": "Definition, roles, and potential research issues of the metaverse in education: An artificial intelligence perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100082", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Fusing Blockchain and AI With Metaverse: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "122", "JournalTitle": "IEEE Open Journal of the Computer Society"}, {"Title": "Artificial intelligence for the metaverse: A survey", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105581", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Systematic literature review on opportunities, challenges, and future research recommendations of artificial intelligence in education", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "", "Page": "100118", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Artificial intelligence for industry 4.0: Systematic review of applications, challenges, and opportunities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "119456", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A systematic review on the use of augmented reality in management and business", "Authors": "Dorota Walentek; Leszek Ziora", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "861", "JournalTitle": "Procedia Computer Science"}, {"Title": "A Moving Metaverse: QoE challenges and standards requirements for immersive media consumption in Autonomous Vehicles", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "159", "Issue": "", "Page": "111577", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 143456270, "Title": "A Frugal Approach Toward Modeling of Defects in Metal 3D Printing Through Statistical Methods in Finite Element Analysis", "Abstract": "<p>Metal additive manufacturing has emerged as a revolutionary technology for the fabrication of high-complexity components. However, this technique presents unique challenges related to the structural integrity and final strength of the parts produced due to inherent defects, such as porosity, cracks, and geometric deviations. These defects significantly impact the fatigue life of the material by acting as stress concentrators that accelerate failure under cyclic loading. On the one hand, this type of model is very complicated in its approach, since, even with encouraging results, the complexity of the calculation with these variables makes it difficult to obtain a simple result that allows for a generalized interpretation. On the other hand, using more familiar methods, it is possible to qualitatively guess the behavior that helps obtain results with better applicability, even at limited levels of precision. This paper presents a simplified finite element method combined with a statistical approach to model the presence of porosity in metal components produced by additive manufacturing. The proposed model considers a two-dimensional square plate subjected to tensile stress, with randomly introduced defects characterized by size, shape, and orientation. The percentage of porosity that affects each aspect determines the adjustment of the mechanical properties of finite elements. A series of simulations were performed to generate multiple models with random defect distributions to estimate maximum stress values. This approach demonstrates that complex models are not always necessary for a preliminary practical estimate of the effects of new manufacturing techniques. Furthermore, it demonstrates the potential for the extension of frugal computational techniques, which aim to minimize computational and experimental costs in the engineering field. The article discusses future research directions, particularly those related to potential business applications, including commercial uses. This follows a discussion of the existing limitations of this study.</p>", "Keywords": "", "DOI": "10.3390/computation13020035", "PubYear": 2025, "Volume": "13", "Issue": "2", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Organizational Engineering, Business Administration and Statistics, Technical University of Madrid—Universidad Politécnica de Madrid (UPM), 28040 Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Fluid Mechanics and Aerospace Propulsion, Technical University of Madrid—Universidad Politécnica de Madrid (UPM), 28040 Madrid, Spain; Research Group GREEN, University of Nebrija—Universidad Nebrija (UAN), 28015 Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, Rey Juan Carlos University—Universidad Rey Juan Carlos (URJC), 28032 Madrid, Spain"}, {"AuthorId": 4, "Name": "Vicente <PERSON>-Albuix<PERSON>", "Affiliation": "Department of Physics Applied to Aeronautical and Naval Engineering, Technical University of Madrid—Universidad Politécnica de Madrid (UPM), 28040 Madrid, Spain"}], "References": [{"Title": "Passive Control of Boundary Layer on Wing: Numerical and Experimental Study of Two Configurations of Wing Surface Modification in Cruise and Landing Speed", "Authors": "<PERSON><PERSON><PERSON>; Panagiota-Vasiliki N. <PERSON>; Dionissios <PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "3", "Page": "67", "JournalTitle": "Computation"}, {"Title": "Acoustic feature based geometric defect identification in wire arc additive manufacturing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "18", "Issue": "1", "Page": "e2210553", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "High-Compression Crash Simulations and Tests of PLA Cubes Fabricated Using Additive Manufacturing FDM with a Scaling Strategy", "Authors": "Andres-<PERSON><PERSON>-Granada", "PubYear": 2024, "Volume": "12", "Issue": "3", "Page": "40", "JournalTitle": "Computation"}]}, {"ArticleId": 143456298, "Title": "Rapid generation method of anime character images based on conditional depth convolutional network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2024.10069182", "PubYear": 2024, "Volume": "75", "Issue": "1", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 143456375, "Title": "Enhancing DataOps practices through innovative collaborative models: A systematic review", "Abstract": "The rapidly evolving field of Data Operations (DataOps) is essential for enhancing data management within large-scale enterprises. However, persistent challenges, such as inefficiencies in data integration, delivery, and governance, limit its potential impact. These obstacles hamper the seamless implementation of DataOps strategies, slowing down operational processes and affecting organizational performance in data-driven environments. To address these issues, this research employs a systematic literature review, analyzing contributions from 2004 to 2024, to identify relevant solutions and innovations. The study highlights the value of frameworks, methodologies, and advanced technologies—such as automation, cloud platforms, and continuous delivery pipelines—that have reshaped the DataOps landscape. These contributions guide enterprises toward best practices in data strategy and foster improved collaboration across business and IT teams. Building on this analysis, our research also proposes a personal framework designed to offer a comprehensive approach to DataOps strategy. This framework integrates key insights from existing research and provides practical recommendations and best practices to streamline workflows, enhance data governance, and align IT operations with business goals. The enhanced DataOps practices derived from our framework demonstrate significant potential to boost operational efficiency, accelerate decision-making processes, and unlock new growth opportunities. Furthermore, the implementation of such practices sets the foundation for future innovations in data management and offers a path forward for organizations seeking sustainable, long-term value.", "Keywords": "DataOps; Agile methodologies; DevOps; Data Integration; Data Governance; Data Quality; Collaborative models", "DOI": "10.1016/j.jjimei.2025.100321", "PubYear": 2025, "Volume": "5", "Issue": "1", "JournalId": 83128, "JournalTitle": "International Journal of Information Management Data Insights", "ISSN": "2667-0968", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Engineering Science, Ibn Tofail University, Kenitra, Morocco;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Laboratory of Engineering Science, Ibn Tofail University, Kenitra, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Engineering Science, Ibn Tofail University, Kenitra, Morocco"}], "References": [{"Title": "AI System Engineering—Key Challenges and Lessons Learned", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "56", "JournalTitle": "Machine Learning and Knowledge Extraction"}, {"Title": "DataOps for Cyber-Physical Systems Governance: The Airport Passenger Flow Case", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "DataOps Lifecycle with a Case Study in Healthcare", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "1", "Page": "136", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Continuous Metadata in Continuous Integration, Stream Processing and Enterprise DataOps", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "1", "Page": "275", "JournalTitle": "Data Intelligence"}, {"Title": "Cloud-based virtual flow metering system powered by a hybrid physics-data approach for water production monitoring in an offshore gas field", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "100124", "JournalTitle": "Digital Chemical Engineering"}, {"Title": "An Automated Big Data Quality Anomaly Correction Framework Using Predictive Analysis", "Authors": "W<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "12", "Page": "182", "JournalTitle": "Data"}, {"Title": "Dynamic datasets and market environments for financial reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "113", "Issue": "5", "Page": "2795", "JournalTitle": "Machine Learning"}, {"Title": "Data Governance in AI - Enabled Healthcare Systems: A Case of the Project Nightingale", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "5", "Page": "85", "JournalTitle": "Asian Journal of Research in Computer Science"}, {"Title": "Integrating Generative AI for Advancing Agile Software Development and Mitigating Project Management Challenges", "Authors": "Anas BAHI; <PERSON>hane <PERSON>; Youssef GAHI", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "54", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Data pipeline approaches in serverless computing: a taxonomy, review, and research trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}]}, {"ArticleId": *********, "Title": "Design factors of the tag-based review summarization system: Perspective of the consumers’ intention to use", "Abstract": "<p>The tag-based review summarization system (TBRSS) has been introduced and widely adopted by e-commerce platforms in recent years. However, limited research has been conducted to understand the design factors that influence consumers’ intention to use the system. Considering both the content and appearance attributes of the system, we developed an integrated model to investigate what affects consumers’ intention to use. The model combines both the content and appearance attributes of the factors and pathways. We used structural equation modeling (SEM) to analyse the 297 samples obtained through an online experiment. Results demonstrate that both the helpfulness of the tags’ content and the consistency between tags and original reviews positively influence the intention to use through information quality. Design aesthetics affects the intention to use through system quality. Moreover, our findings indicate that confidence in initial beliefs serves as an important moderating variable, exerting a positive influence on the relationship between information quality and intention to use.</p>", "Keywords": "Tag-based review summarization system; E-commerce; Online review; Review summarization; Initial belief; M31", "DOI": "10.1007/s12525-025-00761-3", "PubYear": 2025, "Volume": "35", "Issue": "1", "JournalId": 16578, "JournalTitle": "Electronic Markets", "ISSN": "1019-6781", "EISSN": "1422-8890", "Authors": [{"AuthorId": 1, "Name": "Han Yan", "Affiliation": "School of Management, University of Science and Technology of China, Hefei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, Hefei, China"}, {"AuthorId": 3, "Name": "Jin <PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, Hefei, China; Corresponding author."}], "References": [{"Title": "Evaluating E-learning systems success: An empirical study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "67", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Electronic word-of-mouth and consumer purchase intentions in social e-commerce", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "", "Page": "100980", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Assessing the product review helpfulness: Affective-Cognitive evaluation and the moderating effect of feedback mechanism", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "7", "Page": "103359", "JournalTitle": "Information & Management"}, {"Title": "Examining gifting behavior on live streaming platforms: An identity-based motivation model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "6", "Page": "103406", "JournalTitle": "Information & Management"}, {"Title": "Focus Within or On Others: The Impact of Reviewers’ Attentional Focus on Review Helpfulness", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3", "Page": "801", "JournalTitle": "Information Systems Research"}, {"Title": "Review Summary Generation in Online Systems: Frameworks for Supervised and Unsupervised Scenarios", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}, {"Title": "Cultural differences in the perception of credible online reviews – The influence of presentation format", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "154", "Issue": "", "Page": "113710", "JournalTitle": "Decision Support Systems"}, {"Title": "An orthogonal-space-learning-based method for selecting semantically helpful reviews", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "53", "Issue": "", "Page": "101154", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Decide Now or Later: Making Sense of Incoherence Across Online Reviews", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "3", "Page": "1211", "JournalTitle": "Information Systems Research"}, {"Title": "Generating extractive sentiment summaries for natural language user queries on products", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "2", "Page": "5", "JournalTitle": "ACM SIGAPP Applied Computing Review"}, {"Title": "Beyond a bunch of reviews: The quality and quantity of electronic word-of-mouth", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "60", "Issue": "3", "Page": "103777", "JournalTitle": "Information & Management"}, {"Title": "Aspect-based sentiment analysis on multi-domain reviews through word embedding", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "1", "Page": "", "JournalTitle": "Journal of Intelligent Systems"}, {"Title": "An eye-tracking study on the role of attractiveness on consumers’ purchase intentions in e-commerce live streaming", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Electronic Commerce Research"}, {"Title": "“Sorry, too much information”—Designing online review systems that support information search and processing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "33", "Issue": "1", "Page": "1", "JournalTitle": "Electronic Markets"}, {"Title": "Determinants and consequences of routine and advanced use of business intelligence (BI) systems by management accountants", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "61", "Issue": "1", "Page": "103888", "JournalTitle": "Information & Management"}, {"Title": "Exploring aspect-based sentiment quadruple extraction with implicit aspects, opinions, and ChatGPT: a comprehensive survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "2", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": *********, "Title": "AI-Powered Predictive Analytics for Dynamic Cloud Resource Optimization: A Technical Implementation Framework", "Abstract": "This article explores the transformative impact of AI-driven predictive analytics on cloud resource optimization, presenting a comprehensive technical implementation framework. The article shows how artificial intelligence and machine learning architectures revolutionize traditional cloud management approaches through advanced predictive capabilities and dynamic resource allocation. It examines the evolution from static threshold-based systems to sophisticated AI-driven solutions, analyzing their implementation strategies across various organizational contexts. The article delves into multiple optimization domains, including capacity provisioning, cost management, performance enhancement, and energy efficiency, while presenting real-world applications and impact analyses across different industries. Through extensive case studies and empirical evidence, the article demonstrates how organizations leverage AI-powered solutions to address complex cloud resource management challenges, achieve operational efficiencies, and maintain competitive advantages in the digital marketplace. The article also explores future developments and provides strategic recommendations for organizations implementing cloud optimization frameworks, emphasizing the importance of standardized approaches, stakeholder engagement, and sustainable practices in cloud resource management.", "Keywords": "", "DOI": "10.32628/CSEIT251112122", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Real-Time Challenges and Opportunities for an Effective Resource Management in Multi-cloud Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 143456568, "Title": "Estimating global transpiration from TROPOMI SIF with angular normalization and separation for sunlit and shaded leaves", "Abstract": "Gross primary productivity (GPP) is more accurately estimated by total canopy solar-induced chlorophyll fluorescence ( SIF total ) compared to raw sensor observed SIF signals ( SIF obs ). The use of two-leaf strategy, which distinguishes between SIF from sunlit ( SIF sunlit ) and shaded ( SIF shaded ) leaves, further improves GPP estimates. However, the two-leaf strategy, along with SIF corrections for bidirectional effects, has not been applied to transpiration (T) estimation. In this study, we used the angular normalization method to correct the bidirectional effects and separate SIF sunlit and SIF shaded . Then we developed SIF sunlit and SIF shaded driven semi-mechanistic and hybrid models, comparing their T estimates with those from a SIF obs driven semi-mechanistic model at both site and global scales. All three types of SIF-driven T models integrate canopy conductance ( g c ) with the Penman-Monteith model, differing in how g c is derived: from a SIF obs driven semi-mechanistic equation, a SIF sunlit and SIF shaded driven semi-mechanistic equation, and a SIF sunlit and SIF shaded driven machine learning model. When evaluated against partitioned T using the underlying water use efficiency method at 72 eddy covariance sites and two global T remote sensing products, a consistent pattern emerged: SIF sunlit and SIF shaded driven hybrid model > SIF sunlit and SIF shaded driven semi-mechanistic model > SIF obs driven semi-mechanistic model. The SIF sunlit and SIF shaded driven hybrid model demonstrated a notable proficiency under high vapor pressure deficit and low soil water content conditions. The SIF obs driven semi-mechanistic model tends overestimate T at low T values, and this issue is significantly alleviated by the SIF sunlit and SIF shaded driven semi-mechanistic and hybrid models. Our findings demonstrate that correcting the bidirectional effects and using the two-leaf strategy on GPP estimation can improve T estimation and provide a new global T product incorporating vegetation physiological signal.", "Keywords": "", "DOI": "10.1016/j.rse.2024.114586", "PubYear": 2025, "Volume": "319", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Ecosystem Network Observation and Modeling, Institute of Geographic Sciences and Natural Resources Research, Beijing 100101, China;College of Resources and Environment, University of Chinese Academy of Sciences, Beijing 100049, China;Department of Geography and Program in Planning, University of Toronto, Toronto, ON M5S 3G3, Canada;Corresponding authors at: Key Laboratory of Ecosystem Network Observation and Modeling, Institute of Geographic Sciences and Natural Resources Research, Beijing 100101, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Ecosystem Network Observation and Modeling, Institute of Geographic Sciences and Natural Resources Research, Beijing 100101, China;College of Resources and Environment, University of Chinese Academy of Sciences, Beijing 100049, China;Key Laboratory of Regional Ecological Process and Environment Evolution, School of Geography and Information Engineering, Chinese University of Geosciences, Wuhan 430074, China;Corresponding authors at: Key Laboratory of Ecosystem Network Observation and Modeling, Institute of Geographic Sciences and Natural Resources Research, Beijing 100101, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Geography and Program in Planning, University of Toronto, Toronto, ON M5S 3G3, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Xiao", "Affiliation": "Earth Systems Research Center, Institute for the Study of Earth, Oceans, and Space, University of New Hampshire, Durham, NH 03824, USA"}, {"AuthorId": 5, "Name": "Jinghua Chen", "Affiliation": "Key Laboratory of Ecosystem Network Observation and Modeling, Institute of Geographic Sciences and Natural Resources Research, Beijing 100101, China;College of Resources and Environment, University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "International Institute for Earth System Sciences, Jiangsu Center for Collaborative Innovation in Geographical Information Resource Development and Application, Nanjing University, Nanjing, Jiangsu, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Florence, Florence, Italy"}], "References": [{"Title": "Evolution of evapotranspiration models using thermal and shortwave remote sensing data", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111594", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "The potential of satellite FPAR product for GPP estimation: An indirect evaluation using solar-induced chlorophyll fluorescence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111686", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Sun-induced fluorescence closely linked to ecosystem transpiration as evidenced by satellite data and radiative transfer models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "249", "Issue": "", "Page": "112030", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A model for estimating transpiration from remotely sensed solar-induced chlorophyll fluorescence", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "252", "Issue": "", "Page": "112134", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "TROPOMI observations allow for robust exploration of the relationship between solar-induced chlorophyll fluorescence and terrestrial gross primary production", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> Xiao", "PubYear": 2022, "Volume": "268", "Issue": "", "Page": "112748", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Non-linearity between gross primary productivity and far-red solar-induced chlorophyll fluorescence emitted from canopies of major biomes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "112896", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Coupling physical constraints with machine learning for satellite-derived evapotranspiration of the Tibetan Plateau", "Authors": "<PERSON>; <PERSON><PERSON>; Zhenhua Di", "PubYear": 2023, "Volume": "289", "Issue": "", "Page": "113519", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Global estimates of daily evapotranspiration using SMAP surface and root-zone soil moisture", "Authors": "Youngwook Kim; Hotaek <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "298", "Issue": "", "Page": "113803", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Improving estimates of sub-daily gross primary production from solar-induced chlorophyll fluorescence by accounting for light distribution within canopy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "300", "Issue": "", "Page": "113919", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Modeling transpiration using solar-induced chlorophyll fluorescence and photochemical reflectance index synergistically in a closed-canopy winter wheat ecosystem", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "302", "Issue": "", "Page": "113981", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Estimation of global transpiration from remotely sensed solar-induced chlorophyll fluorescence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "303", "Issue": "", "Page": "113998", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": *********, "Title": "Medical Image Hybrid Watermark Algorithm Based on Frequency Domain Processing and Inception v3", "Abstract": "With the widespread use of digital medical images in recent years, the efficient and secure protection of sensitive data has become an urgent issue. Existing research has mostly focused on optimizing individual techniques, lacking comprehensive solutions that integrate the strengths of different methods. This article proposes a hybrid digital watermarking algorithm for medical images based on frequency domain transformation and deep learning convolutional neural networks. The algorithm combines the advantages of traditional frequency domain watermarking methods and deep learning techniques. Specifically, the algorithm first extracts feature vectors from medical images using discrete wavelet transform and discrete cosine transform, enhancing the robustness of the image watermark. It then integrates an improved Inception v3 network, optimizing the convolutional kernel design, thereby effectively enhancing the robustness of the watermarking process. To further improve the security of the watermark, the Logistic Map is used to scramble and encrypt the watermark information, and a hash function and XOR operation are employed for zero embedding of the watermark. The watermark extraction and decryption process relies on a key provided by a third party, enabling blind extraction of the watermark without requiring the original image. Through extensive experimental data analysis and comparisons with multiple existing algorithms, the results demonstrate that the proposed hybrid watermarking algorithm excels in terms of attack resistance and robustness, significantly improving the security and reliability of medical image watermarking.", "Keywords": "", "DOI": "10.1002/aisy.202400654", "PubYear": 2025, "Volume": "", "Issue": "", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Engineering Hainan University  Haikou 570100 China;ZX‐YZ School of Network Science Haikou University of Economics  Haikou 571127 China"}, {"AuthorId": 2, "Name": "Jingbing Li", "Affiliation": "School of Information and Communication Engineering Hainan University  Haikou 570100 China;State Key Laboratory of Marine Resource Utilization in the South China Sea Hainan University  Haikou 570100 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering Hainan University  Haikou 570100 China;State Key Laboratory of Marine Resource Utilization in the South China Sea Hainan University  Haikou 570100 China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering Hainan University  Haikou 570100 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Information Science and Engineering Ritsumeikan University  Kyoto 5258577 Japan"}], "References": [{"Title": "Machine learning based blind color image watermarking scheme for copyright protection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "145", "Issue": "", "Page": "171", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A Multi-Watermarking Algorithm for Medical Images Using Inception V3燼nd燚CT", "Authors": "<PERSON>; <PERSON><PERSON> Li; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "74", "Issue": "1", "Page": "1279", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Zero Watermarking Algorithm for Medical Image Based on Resnet50-DCT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "75", "Issue": "1", "Page": "293", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Hybrid watermarking algorithm for medical images based on digital transformation and MobileNetV2", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "653", "Issue": "", "Page": "119810", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 143456665, "Title": "Machine Learning for Master Production Scheduling: Combining probabilistic forecasting with stochastic optimisation", "Abstract": "This research paper delves into the challenges of Supply Chain (SC) planning under demand uncertainty, focussing on Master Production Scheduling (MPS) with capacity constraints. Traditional SC planning methods, often based on point forecasts and basic safety stock calculations, overlook the distinct demand distribution of each product in each planning period. Considering the demand uncertainty is crucial for robust decision making, especially for time series with seasonal and non-stationary demand. To address this gap, the paper introduces a pioneering Separated Estimation and Optimisation (SEO) approach that estimates demand uncertainty using Machine Learning (ML)-based probabilistic forecasting, subsequently solved by stochastic optimisation. Through a comprehensive analysis involving 17 datasets with a total of 303 products, the study confirms the robustness of stochastic optimisation approaches. It also demonstrates the superiority of ML-based forecasting, which is particularly adept at capturing the intricacies of complex demand patterns. The research challenges the conventional reliance on the Gaussian distribution, instead advocating for the adoption of more flexible parametric distributions such as the Negative Binomial (Neg.-Bin.) distribution. Furthermore, it illustrates how to leverage advances in the research areas of ML-based probabilistic demand forecasting and stochastic MPS, as well as providing a basis for future research. Such avenues include the exploration of testing these approaches with a rolling planning horizon and incorporating both demand and supply uncertainties.", "Keywords": "Master Production Scheduling; Stochastic optimisation; Supply chain optimisation; Probabilistic forecasting; Machine learning forecasting; Master planning", "DOI": "10.1016/j.eswa.2025.126586", "PubYear": 2025, "Volume": "271", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Mannheim, B6, 26, Mannheim, 68159, Baden-Württemberg, Germany;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Mannheim, B6, 26, Mannheim, 68159, Baden-Württemberg, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Mannheim, B6, 26, Mannheim, 68159, Baden-Württemberg, Germany"}], "References": [{"Title": "Smart Master Production Schedule for the Supply Chain: A Conceptual Framework", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "12", "Page": "156", "JournalTitle": "Computers"}]}, {"ArticleId": 143456728, "Title": "Assessment of Knowledge and Utilization of Computers Among Healthcare Workers in Benue South, Nigeria", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijitcs.2025.01.03", "PubYear": 2025, "Volume": "17", "Issue": "1", "JournalId": 8584, "JournalTitle": "International Journal of Information Technology and Computer Science", "ISSN": "2074-9007", "EISSN": "2074-9015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Federal University of Health Sciences, P.M.B. 145, Otukpo, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Oga Ode", "Affiliation": ""}], "References": []}, {"ArticleId": 143456818, "Title": "Corrigendum to “Assessment of undergraduates nursing students’ knowledge toward MRI safety: Cross-sectional study” [Journal of Radiation Research and Applied Sciences Volume 17, Issue 1, March 2024, 100801]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.jrras.2025.101323", "PubYear": 2025, "Volume": "18", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 8, "Name": "Turkey Refaee", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 10, "Name": "Bandar Alwadani", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiography Technology, College of Applied Medical Sciences, Jazan University, Jazan, 45142, Saudi Arabia"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Department of Radiological Sciences and Medical Imaging, College of Applied Medical Sciences, Majmaah University, Majmaah, 11952, Saudi Arabia"}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Diagnostic Radiology Department, Jazan Armed Forces Hospital, Saudi Arabia"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "Department of Medical Imaging, Faculty of Applied Medical Sciences, The Hashemite University, Zarqa, Jordan;Department of Medical Radiography, School of Health Sciences, University of Doha for Science and Technology, Doha, Qatar"}], "References": []}, {"ArticleId": 143456851, "Title": "Robust motorcycle graph construction and simplification for semi-structured quad mesh generation", "Abstract": "Motorcycle graph is widely adopted as an intermediate block in state-of-art semi-structured quad meshing methods. However, constructing and simplifying it on 3D triangle meshes still face challenges in performance and stability. To address these challenges, we present a novel motorcycle graph construction and simplification method for semi-structured quad mesh generation. First, we introduce a piecewise advancing algorithm on parameterized triangle meshes with specially designed data structures to ensure reliable and high-performing motorcycle graph tracing. Second, we enhance the existing zero-collapse procedure with non-intersecting paths creation and feature preserving for T-mesh simplification. Third, we integrate our motorcycle graph construction and simplification algorithm into the state-of-art semi-structured quad meshing pipeline. A comparison with typical state-of-art methods proves that our method can generate quad meshes with superior topological quality and feature preservation capability. We also conduct batch experiments to demonstrate the efficiency, robustness of the proposed method.", "Keywords": "", "DOI": "10.1016/j.cag.2025.104173", "PubYear": 2025, "Volume": "127", "Issue": "", "JournalId": 3909, "JournalTitle": "Computers & Graphics", "ISSN": "0097-8493", "EISSN": "1873-7684", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, 37 Xueyuan Road, Haidian District, Beijing 100191, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, 37 Xueyuan Road, Haidian District, Beijing 100191, China;State Key Laboratory of Virtual Reality Technology and Systems, Beihang University, 37 Xueyuan Road, Haidian District, Beijing 100191, China;Zhongguancun Laboratory, Beijing 100191, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering, Beihang University, 37 Xueyuan Road, Haidian District, Beijing 100191, China;Corresponding author"}], "References": [{"Title": "Layout Embedding via Combinatorial Optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "277", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Quad Layouts via Constrained T‐Mesh Quantization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "2", "Page": "305", "JournalTitle": "Computer Graphics Forum"}, {"Title": "<PERSON><PERSON> Meshing with Coarse Layouts for Planar Domains", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "103084", "JournalTitle": "Computer-Aided Design"}, {"Title": "Reliable feature-line driven quad-remeshing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Q-zip: singularity editing primitive for quad meshes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "The 3D Motorcycle Complex for Structured Volume Decomposition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "221", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Closed-form quadrangulation of n -sided patches", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "107", "Issue": "", "Page": "60", "JournalTitle": "Computers & Graphics"}, {"Title": "Structure simplification of planar quadrilateral meshes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "109", "Issue": "", "Page": "1", "JournalTitle": "Computers & Graphics"}, {"Title": "The Method of Moving Frames for Surface Global Parametrization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Min-Deviation-Flow in Bi-directed Graphs for T-Mesh Quantization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Fabrication-aware strip-decomposable quadrilateral meshes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "168", "Issue": "", "Page": "103666", "JournalTitle": "Computer-Aided Design"}, {"Title": "An automatic framework for quadrilateral surface reconstruction with partitions from 3D point clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "7", "Page": "4725", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 143457146, "Title": "Rainflow evolution model: A holistic method of complex product functional design", "Abstract": "Product complexity increases along with the increase of product functions. Effective methods are required in complex product development. Axiomatic design can reduce product complexity, but there are deficiencies in innovation improvement. A rainflow evolution model is proposed based on the technological evolution law and scientific effect: it can realize the innovative improvement of complex product functional design processes. Firstly, the functional design direction and the corresponding substitutability technical knowledge are mined for complex products. Then, the technological evolution law and su-field models are introduced to construct the field-combination selection matrix, which defines the design path of the new structure. The scientific effects are retrieved based on the conversion of energy forms, and a new scheme is designed using the analogy method. The introduction of cross-domain knowledge for complex product improves the innovation of functional design. The proposed method is applied to develop a powder dryer machine, to prove its feasibility and effectiveness.", "Keywords": "", "DOI": "10.1016/j.aei.2025.103162", "PubYear": 2025, "Volume": "65", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, China Jiliang University, Hangzhou 310018, China;National Engineering Research Center for Technological Innovation Method and Tool, Tianjin 300401, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, China Jiliang University, Hangzhou 310018, China"}, {"AuthorId": 3, "Name": "Run<PERSON> Tan", "Affiliation": "National Engineering Research Center for Technological Innovation Method and Tool, Tianjin 300401, China;Corresponding author"}], "References": [{"Title": "Customized and knowledge-centric service design model integrating case-based reasoning and TRIZ", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "113062", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Radical innovation of product design using an effect solving method", "Authors": "<PERSON>; <PERSON><PERSON>; Qing<PERSON>", "PubYear": 2021, "Volume": "151", "Issue": "", "Page": "106970", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A holistic method of complex product development based on a neural network-aided technological evolution system", "Authors": "<PERSON>; <PERSON><PERSON>; Qing<PERSON>", "PubYear": 2021, "Volume": "48", "Issue": "", "Page": "101294", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A new computational method for acquiring effect knowledge to support product innovation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "231", "Issue": "", "Page": "107410", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "TRIZ trend of engineering system evolution: A review on applications, benefits, challenges and enhancement with computer-aided aspects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "174", "Issue": "", "Page": "108833", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A contradiction solving method for complex product conceptual design based on deep learning and technological evolution patterns", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101825", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Exploring the technology changes of new energy vehicles in China: Evolution and trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "191", "Issue": "", "Page": "110178", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 143457148, "Title": "Intermediately discretized extended α -level-optimization – An advanced fuzzy analysis approach", "Abstract": "Appropriate uncertainty models are required for realistic representations of quantities in real world engineering tasks. Uncertainty quantification is applied to estimate the uncertainty of system responses, with respect to uncertain input quantities. In contrast to aleatoric uncertainty, which is based on natural variability, epistemic uncertainty is caused by lack of knowledge, incertitudes or inaccuracy. In this contribution, epistemic uncertainties are modeled by fuzzy quantities and corresponding uncertainty quantification approaches are investigated. The propagation of fuzzy quantities is based on the extension principle. For numerical analyses, a discretization of the extension principle is required, which can be reformulated as an optimization problem. Two different approaches are state-of-the-art for formulating the optimization problem of the extension principle, which are referred to as α -level optimization and sampling-based approach (SBA). A comparison of these two approaches is presented, highlighting their advantages and deficits with respect to efficiency and accuracy of the fuzzy analyses. Based on the advantages of both α -level optimization and SBA, a novel approach, the intermediately discretized extended α -level optimization (IDEALO), is developed. In IDEALO, advantages of α -level optimization and SBA are combined to a hybrid approach. The superiority of IDEALO over the other two approaches is demonstrated in benchmark examples.", "Keywords": "Epistemic uncertainty; Uncertainty quantification; α-level-discretization; Fuzzy analysis; Optimization; Structural analysis", "DOI": "10.1016/j.advengsoft.2025.103865", "PubYear": 2025, "Volume": "202", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Structural Analysis, Technische Universität Dresden, 01062 Dresden, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Structural Analysis, Technische Universität Dresden, 01062 Dresden, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute for Structural Analysis, Technische Universität Dresden, 01062 Dresden, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute for Structural Analysis, Technische Universität Dresden, 01062 Dresden, Germany;Corresponding author"}], "References": [{"Title": "Fuzzy multi-objective programming: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "116663", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 143457205, "Title": "A Texture‐Free Practical Model for Realistic Surface‐Based Rendering of Woven Fabrics", "Abstract": "Rendering woven fabrics is challenging due to the complex micro geometry and anisotropy appearance. Conventional solutions either fully model every yarn/ply/fibre for high fidelity at a high computational cost, or ignore details, that produce non‐realistic close‐up renderings. In this paper, we introduce a model that shares the advantages of both. Our model requires only binary patterns as input yet offers all the necessary micro‐level details by adding the yarn/ply/fibre implicitly. Moreover, we design a double‐layer representation to handle light transmission accurately and use a constant timed () approach to accurately and efficiently depict parallax and shadowing‐masking effects in a tandem way. We compare our model with curve‐based and surface‐based, on different patterns, under different lighting and evaluate with photographs to ensure capturing the aforementioned realistic effects.", "Keywords": "", "DOI": "10.1111/cgf.15283", "PubYear": 2025, "Volume": "44", "Issue": "1", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> Khattar", "Affiliation": "University of Manchester Manchester UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of California Santa Barbara Santa Barbara USA"}, {"AuthorId": 3, "Name": "Ling‐Qi Yan", "Affiliation": "University of California Santa Barbara Santa Barbara USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Manchester Manchester UK"}], "References": [{"Title": "A practical ply-based appearance model of woven fabrics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "NeuMIP", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Path replay backpropagation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "A Practical and Hierarchical Yarn‐based Shading Model for Cloth", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "e14894", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Neural Appearance Model for Cloth Rendering", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "4", "Page": "", "JournalTitle": "Computer Graphics Forum"}]}, {"ArticleId": 143457246, "Title": "A Methodological Framework for AI-Driven Textual Data Analysis in Digital Media", "Abstract": "<p>The growing volume of textual data generated on digital media platforms presents significant challenges for the analysis and interpretation of information. This article proposes a methodological approach that combines artificial intelligence (AI) techniques and statistical methods to explore and analyze textual data from digital media. The framework, titled DAFIM (Data Analysis Framework for Information and Media), includes strategies for data collection through APIs and web scraping, textual data processing, and data enrichment using AI solutions, including named entity recognition (people, locations, objects, and brands) and the detection of clickbait in news. Sentiment analysis and text clustering techniques are integrated to support content analysis. The potential applications of this methodology include social networks, news aggregators, news portals, and newsletters, offering a robust framework for studying digital data and supporting informed decision-making. The proposed framework is validated through a case study involving data extracted from the Google News aggregation platform, focusing on the Israel–Lebanon conflict. This demonstrates the framework’s capability to uncover narrative patterns, content trends, and clickbait detection while also highlighting its advantages and limitations.</p>", "Keywords": "", "DOI": "10.3390/fi17020059", "PubYear": 2025, "Volume": "17", "Issue": "2", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information and Communication, Federal University of Goiás, Goiânia 74690-900, GO, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Information and Audiovisual Media, University of Barcelona, 08193 Barcelona, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Information and Audiovisual Media, University of Barcelona, 08193 Barcelona, Spain"}], "References": [{"Title": "Social Media Data and Users' Preferences: A Statistical Analysis to Support Marketing Communication", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "", "Page": "100189", "JournalTitle": "Big Data Research"}, {"Title": "WEClustering: word embeddings based text clustering technique for large datasets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "6", "Page": "3211", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Abusive Bangla comments detection on Facebook using transformer-based deep learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "BERT- and BiLSTM-Based Sentiment Analysis of Online Chinese Buzzwords", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Shengwei Ji", "PubYear": 2022, "Volume": "14", "Issue": "11", "Page": "332", "JournalTitle": "Future Internet"}, {"Title": "Fine-grained classification of drug trafficking based on Instagram hashtags", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "165", "Issue": "", "Page": "113896", "JournalTitle": "Decision Support Systems"}, {"Title": "Machine learning techniques for emotion detection and sentiment analysis: current state, challenges, and future directions", "Authors": "Alaa Alslaity; <PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "1", "Page": "139", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Survey on sentiment analysis: evolution of research methods and topics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "8", "Page": "8469", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Research on Ethical Issues and Coping Strategies of Artificial Intelligence Algorithms Recommending News with the Support of Wireless Sensing Technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "2023", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Sensors"}, {"Title": "An artificial intelligence based news feature mining system based on the Internet of Things and multi-sensor fusion", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "", "Page": "e1428", "JournalTitle": "PeerJ Computer Science"}, {"Title": "The Value of Web Data Scraping: An Application to TripAdvisor", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "3", "Page": "121", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "An effective approach for identifying keywords as high-quality filters to get emergency-implicated Twitter Spanish data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "84", "Issue": "", "Page": "101579", "JournalTitle": "Computer Speech & Language"}, {"Title": "Named entity recognition and emotional viewpoint monitoring in online news using artificial intelligence", "Authors": "<PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "", "Page": "e1715", "JournalTitle": "PeerJ Computer Science"}, {"Title": "The Problem of Data Extraction in Social Media: A Theoretical Framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "4", "Page": "1363", "JournalTitle": "Journal of Information Systems and Informatics"}, {"Title": "Detection of depressive comments on social media using RNN, LSTM, and random forest: comparison and optimization", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "Analyzing Views on Presidential Candidates for Election 2024 Based on the Instagram and X Platforms with Text Clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "245", "Issue": "", "Page": "730", "JournalTitle": "Procedia Computer Science"}, {"Title": "A comprehensive survey on pretrained foundation models: a history from BERT to ChatGPT", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 143457308, "Title": "Text swin transformer: a new transformer model for enterprise management text classification", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2024.10069181", "PubYear": 2024, "Volume": "1", "Issue": "1", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}]