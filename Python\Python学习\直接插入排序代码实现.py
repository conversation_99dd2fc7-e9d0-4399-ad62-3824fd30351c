# # 待排序序列
# data = [3,2,6,1,4,5]

# # 排序前
# print("排序前：", data)

# # 直接插入排序算法
# for i in range(1,len(data)):
#     # 当前元素
#     current = data[i]
#     # 当前元素的前一个元素
#     pre_index = i - 1
#     # 如果当前元素小于前一个元素，则将前一个元素后移一位
#     while pre_index >= 0 and current < data[pre_index]:
#         data[pre_index + 1] = data[pre_index]
#         pre_index -= 1
#     # 将当前元素插入到合适的位置
#     data[pre_index + 1] = current

# # 排序后
# print("排序后：", data)




# # 待排序序列
# data = [3,2,6,1,4,5]

# # 排序前
# print("排序前：", data)

# # 直接插入排序算法
# for i in range(1,len(data)):
#     # 当前元素
#     current = data[i]
#     # 二分查找
#     left = 0
#     right = i - 1
#     while left <= right:
#         mid = (left + right) // 2
#         if current < data[mid]:
#             right = mid - 1
#         else:
#             left = mid + 1
#     # 将当前元素插入到合适的位置
#     for j in range(i, left, -1):
#         data[j] = data[j - 1]
#     data[left] = current

# # 排序后
# print("排序后：", data)





# 创建一个Person类
class Person:
    def __init__(self, name, age, score):
        self.name = name
        self.age = age # 年龄
        self.score = score # 分数

    def __str__(self):
        return f"{self.name}, {self.age},{self.score}"

# 准备数据
p1 = Person("张三", 18, 90)
p2 = Person("李四", 20, 85)
p3 = Person("王五", 19, 95)
p4 = Person("赵六", 21, 80)
p5 = Person("钱七", 20, 88)

data = [p1, p2, p3, p4, p5]

# 排序前
print("排序前：")
for p in data:
    print(p)

# 直接插入排序算法
for i in range(1, len(data)):
    # 当前元素
    current = data[i]
    # 当前元素的前一个元素
    pre_index = i - 1
    # 如果当前元素小于前一个元素，则将前一个元素后移一位
    while pre_index >= 0 and current.age <= data[pre_index].age: # 按照年龄排序
        if current.age == data[pre_index].age and current.score >= data[pre_index].score:
            break
        data[pre_index + 1] = data[pre_index]
        pre_index -= 1
    # 将当前元素插入到合适的位置
    data[pre_index + 1] = current

# 排序后
print("排序后：")
for p in data:
    print(p)