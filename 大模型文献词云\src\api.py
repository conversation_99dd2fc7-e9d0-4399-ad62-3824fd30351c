import requests
import orjson

class Api:
    def __init__(self):
        self.url = "https://api.siliconflow.cn/v1/chat/completions"
        self.headers = {
            "Authorization": "Bearer sk-fbfqbgvqlpkoljhylnhqiuqvqjkaqjyidlqljixyokmakikx",
            "Content-Type": "application/json",
        }

    # 获取api响应
    def get_api_response(self, prompt, model):
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "stream": False
        }
        

        # response = requests.post(self.url, headers=self.headers, json=payload)
        # print(response)
        # return response.json()

        try:
            response = requests.post(self.url, headers=self.headers, json=payload)
            print(f"HTTP状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"错误响应: {response.text}")
                print(f"请求长度: {len(prompt)} 字符")
                return None
            
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"其他错误: {e}")
            return None
    
    # 处理访问api得到的response
    def process_api_response(self, response):
        # response已经是字典对象，不需要再解析
        answer_content = response["choices"][0]["message"]["content"]
        return answer_content
    
    # 根据传入的期刊名称，调用api获取最可能期刊名称
    def get_journal_title(self, journal_title, title_list):
        prompt = f"请根据该期刊名称：{journal_title}，返回最可能的存在于以下列表中的期刊名称：\n{title_list}\n请返回一个期刊名称，不要返回任何其他内容。"
        response = self.get_api_response(prompt, "deepseek-ai/DeepSeek-R1")
        return self.process_api_response(response)
    
if __name__ == "__main__":
    api = Api()
    prompt = "请从以下的关键词中提取出所有不能将大写字母转换为小写的词汇（如：AI、AI-based、AI-based等），返回格式为：[词汇];[词汇];[词汇];[词汇]……只需要返回这些关键词，不需要其他内容。关键词：\n"
    prompt += "AI;AI-based;Bayesian optimization;Ensemble stacking classifier;Ethereum;Fraud detection;Machine learning algorithms;SMOTEENNCYP2B6;HIV;efavirenz;isoniazid;neurotoxicityTensor completion; Nuclear norm; Schatten p-norm; Block coordinate descentMulti-order fractional differential equations; Chebyshev Tau method; Convergence analysis; 34A09; 65L05; 65L20; 65L60; 65L80Heterogeneous Computing; GPU; CFD"
    response = api.get_api_response(prompt, "deepseek-ai/DeepSeek-R1")
    print(response)
