[{"ArticleId": 116813418, "Title": "Enhancing mobile data security using red panda optimized approach with chaotic fuzzy encryption in mobile cloud computing", "Abstract": "Smartphone devices have occupied an indispensable place in human life. These devices have some restrictions, like short lifetime of battery, imperfect computation power, less memory size and unpredictable network connectivity. Hence, a number of methods previously presented to decrease these restrictions as well as increase the battery lifespan with the help of offloading strategy. This manuscript proposes a new enhancing mobile data security using red panda optimized approach with chaotic fuzzy encryption in mobile cloud computing (RPO‐CFE‐SMC) to offload intensive computation tasks from mobile device to the cloud. The proposed model utilizes a red panda optimization algorithm (RPOA) to scale dynamically the offloading decision under energy consumption, CPU utilization, execution time, memory usage parameters. Before the work is transferred to the cloud, an innovative security layer is applied for encrypting the data using AES chaotic fuzzy encryption (CFE) technology. The proposed RPO‐CFE‐SMC method provides 20.63%, 25.25%, 25.28%, and 32.47% lower encryption time and 23.66%, 24.25%, and 26.47% lower energy consumption compared with existing EFFORT‐SMC, EESH‐SMC, and CP‐ABE‐SMC models respectively. In conclusion, the simulation results prove that the improved efficiency of proposed model in offloading computation to the cloud with enhanced data protection using chaotic fuzzy encryption.", "Keywords": "chaotic fuzzy encryption;cloud computing;cloud storage;CPU utilization;offloading;red panda optimization;security", "DOI": "10.1002/cpe.8243", "PubYear": 2024, "Volume": "36", "Issue": "23", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering IKG Punjab Technical University  Jalandhar Punjab India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering Punjabi University  Patiala Punjab India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science GTB College  Bhawanigarh (Sangrur) Punjab India"}], "References": [{"Title": "Detection of distributed denial of service attack in cloud computing using the optimization-based deep networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "405", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "EFFORT\n : Energy efficient framework for offload communication in mobile cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "9", "Page": "1896", "JournalTitle": "Software: Practice and Experience"}, {"Title": "An edge based hybrid intrusion detection framework for mobile edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "5", "Page": "3719", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Energy-efficient and secure mobile fog-based cloud for the Internet of Things", "Authors": "<PERSON>; <PERSON><PERSON>; Bandar Alotaibi", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "1", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "FPGA Realization of a Reversible Data Hiding Scheme for 5G MIMO-OFDM System by Chaotic Key Generation-Based Paillier Cryptography Along with LDPC and Its Side Channel Estimation Using Machine Learning Technique", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "5", "Page": "", "JournalTitle": "Journal of Circuits, Systems and Computers"}, {"Title": "Security enhancement of the cloud paradigm using a novel optimized crypto mechanism", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "11", "Page": "15983", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "PIRAP: Chaotic Fuzzy Encryption (CFE) Technique and Greedy Chemical Reaction Optimization (GCRO) Algorithm-Based Secured Mobi-Cloud Framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "1n02", "Page": "", "JournalTitle": "International Journal of Cooperative Information Systems"}, {"Title": "Hybrid cryptographic approach to enhance the mode of key management system in cloud environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "7", "Page": "7377", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An efficient and secure key management with the extended convolutional neural network for intrusion detection in cloud storage", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "23", "Page": "e7806", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Deep learning models for cloud, edge, fog, and IoT computing paradigms: Survey, recent advances, and future directions", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "49", "Issue": "", "Page": "100568", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 116813426, "Title": "1D3-5　ランニングフォーム実時間フィードバックデバイス: －初心者のランニングフォーム改善－", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1D3-5", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kaoru SUZUKI", "Affiliation": "Faculty of Science and Engineering, Hosei University"}], "References": []}, {"ArticleId": 116813429, "Title": "Privacy-Preserving Healthcare and Medical Data Collaboration Service System Based on Blockchain and Federated Learning", "Abstract": "", "Keywords": "", "DOI": "10.32604/cmc.2024.052570", "PubYear": 2024, "Volume": "80", "Issue": "2", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Blockchain-based business process management (BPM) framework for service composition in industry 4.0", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7", "Page": "1737", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A novel blockchain based electronic health record automation system for healthcare", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "693", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Artificial intelligence-assisted blockchain-based framework for smart and secure EMR management", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "31", "Page": "22959", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Blockchain-empowered Federated Learning: Challenges, Solutions, and Future Directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "11", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Fed-ESD: Federated learning for efficient epileptic seizure detection in the fog-assisted internet of medical things", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "630", "Issue": "", "Page": "403", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 116813435, "Title": "2G4-2　製品と空間を創造するオカムラの人間工学: －認定人間工学専門家の取り組み－", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2G4-2", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Haruyuki ASADA", "Affiliation": "Okamura corporation"}], "References": []}, {"ArticleId": 116813443, "Title": "An adaptive stabilized trace finite element method for surface PDEs", "Abstract": "The paper introduces an adaptive version of the stabilized Trace Finite Element Method (TraceFEM) designed to solve low-regularity elliptic problems on level-set surfaces using a shape-regular bulk mesh in the embedding space. Two stabilization variants, gradient-jump face and normal-gradient volume, are considered for continuous trace spaces of the first and second degrees, based on the polynomial families Q 1 and Q 2 . We propose a practical error indicator that estimates the ‘jumps’ of finite element solution derivatives across background mesh faces and it avoids integration of any quantities along implicitly defined curvilinear edges of the discrete surface elements. For the Q 1 family of piecewise trilinear polynomials on bulk cells, the solve-estimate-mark-refine strategy, combined with the suggested error indicator, achieves optimal convergence rates typical of two-dimensional problems. We also provide a posteriori error estimates, establishing the reliability of the error indicator for the Q 1 and Q 2 elements and for two types of stabilization. In numerical experiments, we assess the reliability and efficiency of the error indicator. While both stabilizations are found to deliver comparable performance, the lowest degree finite element space appears to be the more robust choice for the adaptive TraceFEM framework.", "Keywords": "", "DOI": "10.1016/j.camwa.2024.07.016", "PubYear": 2024, "Volume": "171", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical and Statistical Sciences, Clemson University, Clemson, SC 29634-0975, USA"}, {"AuthorId": 2, "Name": "Maxim A<PERSON>", "Affiliation": "Department of Mathematics, University of Houston, Houston, TX 77204-3008, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mathematical and Statistical Sciences, Clemson University, Clemson, SC 29634-0975, USA;Corresponding author"}], "References": [{"Title": "The deal.II finite element library: Design, features, and insights", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "81", "Issue": "", "Page": "407", "JournalTitle": "Computers & Mathematics with Applications"}, {"Title": "A finite element method for <PERSON> equation on deforming surface", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "90", "Issue": "", "Page": "148", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 116813491, "Title": "Development of an Arduino-based field heat regulator for fruit storage and transportation", "Abstract": "<p>Fruit spoilage during transportation is a major problem that results in significant economic losses for fruit producers and distributors. One of the primary causes of fruit spoilage is heat buildup inside the storage container during transportation. Hence, this study was done to design and develop an Arduino-based field heat regulator for fruit storage and transportation, regulate field heat in terms of temperature and humidity monitoring; and assess its influence in terms of the skin color, firmness, and bruising of the fruit specimen. After the conduct of the study, it was found that the regulator underwent several iterations during product development and was tried out in an actual transportation procedure. The results revealed that during transportation the product was subjected to fluctuations in temperature and relative humidity, but the storage regulated heat by maintaining desired conditions. Additionally, there was a significant difference found in terms of the fruit's quality parameters when transported using the proposed storage and the traditional method. In conclusion, this storage has the potential to be used in fruit storage facilities, helping reduce post-harvest losses and decrease the chances of fruits being spoiled easily.</p>", "Keywords": "Field heat regulator; Fruit transportation; Fruit storage; Temperature; Humidity", "DOI": "10.11591/ijra.v13i3.pp314-329", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 46243, "JournalTitle": "IAES International Journal of Robotics and Automation (IJRA)", "ISSN": "2089-4856", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Liceo de Cagayan University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Liceo de Cagayan University"}, {"AuthorId": 3, "Name": "Eshtriel Sunday T. Natividad", "Affiliation": "Liceo de Cagayan University"}, {"AuthorId": 4, "Name": "Brylle F. Cabelita", "Affiliation": "Liceo de Cagayan University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Mindanao State University"}, {"AuthorId": 6, "Name": "Jas Felicisimo A. Cane", "Affiliation": "Liceo de Cagayan University"}], "References": []}, {"ArticleId": 116813501, "Title": "1G3-4　教育用途を想定した防眩フィルムの光学特性及び筆記特性", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1G3-4", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Makio KURASHIGE", "Affiliation": "Dai Nippon Printing Co., Ltd."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dai Nippon Printing Co., Ltd."}, {"AuthorId": 3, "Name": "Kazutoshi ISHIDA", "Affiliation": "Dai Nippon Printing Co., Ltd."}, {"AuthorId": 4, "Name": "Takashi SHIBATA", "Affiliation": "School of Information Science and Technology, Tokai University"}], "References": []}, {"ArticleId": 116813512, "Title": "Coverage Axis++: Efficient Inner Point Selection for 3D Shape Skeletonization", "Abstract": "<p> We introduce Coverage Axis++, a novel and efficient approach to 3D shape skeletonization. The current state‐of‐the‐art approaches for this task often rely on the watertightness of the input [LWS*15; PWG*19; PWG*19] or suffer from substantial computational costs [DLX*22; CD23], thereby limiting their practicality. To address this challenge, Coverage Axis++ proposes a heuristic algorithm to select skeletal points, offering a high‐accuracy approximation of the Medial Axis Transform (MAT) while significantly mitigating computational intensity for various shape representations. We introduce a simple yet effective strategy that considers shape coverage, uniformity, and centrality to derive skeletal points. The selection procedure enforces consistency with the shape structure while favoring the dominant medial balls, which thus introduces a compact underlying shape representation in terms of MAT. As a result, Coverage Axis++ allows for skeletonization for various shape representations (e.g., water‐tight meshes, triangle soups, point clouds), specification of the number of skeletal points, few hyperparameters, and highly efficient computation with improved reconstruction accuracy. Extensive experiments across a wide range of 3D shapes validate the efficiency and effectiveness of Coverage Axis++. Our codes are available at https://github.com/Frank-ZY-Dou/Coverage_Axis . </p>", "Keywords": "CCS Concepts;• Computing methodologies → Shape analysis", "DOI": "10.1111/cgf.15143", "PubYear": 2024, "Volume": "43", "Issue": "5", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Hong Kong;TransGP"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shandong University"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "The University of Hong Kong"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "The University of Hong Kong"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Hong Kong"}, {"AuthorId": 7, "Name": "Shiqing Xin", "Affiliation": "Shandong University"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Hong Kong"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Hong Kong"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Texas A&M University"}], "References": [{"Title": "P2MAT-NET: Learning medial axis transform from sparse point clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "80", "Issue": "", "Page": "101874", "JournalTitle": "Computer Aided Geometric Design"}, {"Title": "Opening and closing surfaces", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Restricted Delaunay Triangulation for Explicit Surface Reconstruction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "IMMAT: Mesh Reconstruction from Single View Images by Medial Axis Transform Prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "150", "Issue": "", "Page": "103304", "JournalTitle": "Computer-Aided Design"}, {"Title": "Computing Medial Axis Transform with Feature Preservation via Restricted Power Diagram", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Neural skeleton: Implicit neural representation away from the surface", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "114", "Issue": "", "Page": "368", "JournalTitle": "Computers & Graphics"}, {"Title": "Point2MM: Learning medial mesh from point clouds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "115", "Issue": "", "Page": "511", "JournalTitle": "Computers & Graphics"}, {"Title": "3D Gaussian Splatting for Real-Time Radiance Field Rendering", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "EPCS: Endpoint-based part-aware curve skeleton extraction for low-quality point clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "209", "JournalTitle": "Computers & Graphics"}, {"Title": "Medial hex-meshing: high-quality all-hexahedral mesh generation based on medial mesh", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "40", "Issue": "4", "Page": "2537", "JournalTitle": "Engineering with Computers"}, {"Title": "MATTopo: Topology-preserving Medial Axis Transform with Restricted Power Diagram", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Medial Skeletal Diagram: A Generalized Medial Axis Approach for Compact 3D Shape Representation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": *********, "Title": "Influence of random geometrical imperfection on loading capacity of scaffold based on stochastic numerical model", "Abstract": "The existing data indicate that two-thirds of engineering accidents occur during construction among which engineering accidents caused by scaffold collapse account for a large proportion. Due to the complex mechanical behavior of connection and random nature of scaffold system caused by random geometrical imperfection, the reliability of scaffold system is lower than other kinds of building structures. However, the method considering the random geometrical imperfection is limited. To facilitate the analysis of random geometrical imperfection, the original numerical algorithm is proposed based on ANSYS Parametric Design Language. Through proposed method, two types of geometrical imperfections, i.e., the nodal location error and initial curvature can be automatically considered. The randomness in initial curvature includes random magnitude and random direction. The established numerical model is as close to reality as possible and the process of establishing stochastic numerical model can be automatically finished. The only work that needs to be done is to enter the dimensions of the scaffold. Except the propose of numerical algorithm, the objective of this study is to reveal the influence of geometrical imperfection on random distribution of loading capacity of scaffold system under different load conditions. The influence of random geometrical imperfection on probabilistic distribution of loading capacity is systematically investigated. The results indicated that there may be several buckling modes exist and the buckling mode occurred in actual condition is closely related to the random distribution of geometrical imperfection. The load factor of internal post (point 3) is 8 %–12 % larger than that of corner post. The load factor of side post is 4.7 %–7.2 % larger than that of corner post. The ultimate bending capacity M <sub>u</sub> has little influence on the loading capacity of scaffold system when the initial bending stiffness k <sub>o</sub> is small enough.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2024.103737", "PubYear": 2024, "Volume": "197", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Civil Engineering, Liaoning Technical University, Fuxin 123000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Liaoning Technical University, Fuxin 123000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Liaoning Technical University, Fuxin 123000, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>zhen Zhao", "Affiliation": "Shaanxi Construction Engineering Group Co., Ltd, Xi'an 710082, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The 9th Engineering Co. Ltd. of CCCC First Highway Engineering Co. Ltd., Guangzhou 510000, China"}], "References": []}, {"ArticleId": 116813566, "Title": "Adaptive multi-stage evolutionary search for constrained multi-objective optimization", "Abstract": "In this paper, we propose a multi-stage evolutionary framework with adaptive selection (MSEFAS) for efficiently handling constrained multi-objective optimization problems (CMOPs). MSEFAS has two stages of optimization in its early phase of evolutionary search: one stage that encourages promising infeasible solutions to approach the feasible region and increases diversity, and the other stage that enables the population to span large infeasible regions and accelerates convergence. To adaptively determine the execution order of these two stages in the early process, MSEFAS treats the optimization stage with higher validity of selected solutions as the first stage and the other as the second one. In addition, at the late phase of evolutionary search, MSEFAS introduces a third stage to efficiently handle the various characteristics of CMOPs by considering the relationship between the constrained Pareto fronts (CPF) and unconstrained Pareto fronts. We compare the proposed framework with eleven state-of-the-art constrained multi-objective evolutionary algorithms on 56 benchmark CMOPs. Our results demonstrate the effectiveness of the proposed framework in handling a wide range of CMOPs, showcasing its potential for solving complex optimization problems.", "Keywords": "Multi-stage evolutionary framework with adaptive selection; Constrained multiobjective optimization problems; Evolutionary search; Infeasible solutions", "DOI": "10.1007/s40747-024-01542-9", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "Huiting Li", "Affiliation": "State Key Laboratory of Synthetical Automation for Process Industries, Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Synthetical Automation for Process Industries, Northeastern University, Shenyang, China; School of Engineering, Westlake University, Hangzhou, China; Department of Computer Science, University of Surrey, Guildford, UK; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Guangdong Provincial Key Laboratory of Brain-Inspired Intelligent Computation, Southern University of Science and Technology, Shenzhen, China"}], "References": [{"Title": "Difficulty Adjustable and Scalable Constrained Multiobjective Test Problem Toolkit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "3", "Page": "339", "JournalTitle": "Evolutionary Computation"}, {"Title": "Push and pull search embedded in an M2M framework for solving constrained multi-objective optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100651", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Purpose-directed two-phase multiobjective differential evolution for constrained multiobjective optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "100799", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A multi-stage evolutionary algorithm for multi-objective optimization with complex constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "68", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 116813607, "Title": "Estimation of winter wheat yield from sentinel-1A time-series images using ensemble deep learning and a Gaussian process", "Abstract": "", "Keywords": "", "DOI": "10.1080/2150704X.2024.2384094", "PubYear": 2024, "Volume": "15", "Issue": "8", "JournalId": 1790, "JournalTitle": "Remote Sensing Letters", "ISSN": "2150-704X", "EISSN": "2150-7058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Henan University, Kaifeng, China;Henan Province Engineering Research Center of Spatial Information Processing, Kaifeng, China;Henan Key Laboratory of Big Data Analysis and Processing, Kaifeng, China;Academy for Advanced Interdisciplinary Studies, Henan University, Kaifeng, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Land Satellite Remote Sensing Application Center, Ministry of Natural Resources, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Henan University, Kaifeng, China;Henan Province Engineering Research Center of Spatial Information Processing, Kaifeng, China;Henan Key Laboratory of Big Data Analysis and Processing, Kaifeng, China;Academy for Advanced Interdisciplinary Studies, Henan University, Kaifeng, China"}, {"AuthorId": 4, "Name": "Hui<PERSON> Yang", "Affiliation": "School of Computer and Information Engineering, Henan University, Kaifeng, China;Henan Province Engineering Research Center of Spatial Information Processing, Kaifeng, China;Henan Key Laboratory of Big Data Analysis and Processing, Kaifeng, China;Academy for Advanced Interdisciplinary Studies, Henan University, Kaifeng, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information Engineering, Henan University, Kaifeng, China;Henan Province Engineering Research Center of Spatial Information Processing, Kaifeng, China;Henan Key Laboratory of Big Data Analysis and Processing, Kaifeng, China;Academy for Advanced Interdisciplinary Studies, Henan University, Kaifeng, China"}], "References": [{"Title": "Winter wheat planting area extraction using SAR change detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "951", "JournalTitle": "Remote Sensing Letters"}]}, {"ArticleId": 116813640, "Title": "<PERSON><PERSON><PERSON> dan Media Sosial Terhadap <PERSON><PERSON><PERSON><PERSON><PERSON>", "Abstract": "<p>Ke<PERSON><PERSON>usahaan di Indonesia mendapat perhatian sebagai cara untuk merangsang pertumbuhan ekonomi dan menciptakan pekerja<PERSON>, khusus<PERSON> di kalangan lulusan perguruan tinggi, dimana tingkat pengangguran masih tinggi. Terlepas dari upaya yang dilakukan, masih ada kesenjangan antara persiapan mahasiswa dan kesiapan berwirausaha yang sesungguhnya. Penelitian ini bertujuan untuk menganalisis pengaruh pengetahuan kewirausahaan, dan penggunaan media sosial, terhadap motivasi berwirausaha mahasiswa di Universitas Negeri Padang. Menggunakan metode survei dengan kuesioner yang disebar secara online kepada 40 mahasiswa, penelitian ini mengumpulkan data yang kemudian dianalisis dengan regresi linear berganda menggunakan SPSS 29. Hasil uji normalitas Kolmogorov-Smirnov menunjukkan data berdistribusi normal, memungkinkan analisis lebih lanjut. <PERSON><PERSON> penelitian menunjukkan bahwa pengetahuan kewirausahaan dan penggunaan media sosial secara signifikan meningkatkan motivasi berwirausaha. Dengan pemahaman yang lebih baik tentang faktor-faktor ini, institusi pendidikan dapat merancang program yang lebih efektif untuk mendorong kewirausahaan di kalangan mahasiswa, mengoptimalkan peran media sosial sebagai alat pendorong kesiapan wirausaha.</p>", "Keywords": "", "DOI": "10.33022/ijcs.v13i3.3969", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Universitas Negeri Padang"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "universitas negeri padang"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "universitas negeri padang"}, {"AuthorId": 4, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "universitas negeri padang"}, {"AuthorId": 5, "Name": " <PERSON><PERSON>", "Affiliation": "universitas negeri padang"}], "References": []}, {"ArticleId": 116813665, "Title": "1G1-3　中学校部活動の地域移行に伴う指導サービスの可能性と課題: －千歳市の大学生による支援活動を中心にして－", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1G1-3", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kanato KOMAI", "Affiliation": "Chitose Institute of Science and Technology, Graduate School of Science and Technology"}, {"AuthorId": 2, "Name": "Toshioki SOGA", "Affiliation": "Chitose Institute of Science and Technology, Graduate School of Science and Technology"}, {"AuthorId": 3, "Name": "Daiji KOBAYASII", "Affiliation": "Chitose Institute of Science and Technology, Graduate School of Science and Technology"}], "References": []}, {"ArticleId": 116813669, "Title": "<PERSON><PERSON><PERSON> Pelatihan Pada PT XYZ", "Abstract": "<p>Learning Management System (LMS) merupakan sistem penting yang dirancang untuk mengelola konten, pela<PERSON>han, dan kinerja karyawan secara elektronik. LMS memungkinkan perusahaan untuk mengelola dan mengevaluasi program pelatihan secara sistematis, serta memberikan akses mudah ke konten pelatihan bagi pengguna. <PERSON><PERSON>, banyak implementasi LMS yang gagal karena kurang memperhatikan kesiapan pengguna dalam mengadopsi teknologi baru. Penelitian ini bertujuan untuk menganalisis pengaruh kesiapan pengguna (self-efficacy, individual innovativeness, self-directed learning, dan motivation) terhadap penerimaan modul pelatihan berbasis LMS di PT XYZ. Penelitian menggunakan kerangka Technology Acceptance Model (TAM) dan metode Partial Least Square Structural Equation Modelling (PLS-SEM) untuk 69 responden. Hasil penelitian menunjukkan bahwa self-efficacy, self-directed learning, dan motivation berpengaruh signifikan terhadap persepsi kemudahan penggunaan modul pelatihan. Sedangkan motivation berpengaruh signifikan terhadap manfaat yang dirasakan pada modul pelatihan tersebut. Temuan penelitian diharapkan bermanfaat dalam merencanakan peningkatan kesiapan karyawan sebelum implementasi LMS guna mendorong penerimaan dan keberhasilan sistem pelatihan berbasis teknologi</p>", "Keywords": "E-learning Readiness;TAM;Learning Management System", "DOI": "10.33022/ijcs.v13i3.4090", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "Vinra Pandia", "Affiliation": "Universitas Indonesia"}], "References": []}, {"ArticleId": 116813725, "Title": "Microservices simulator: An object-oriented framework for transactional causal consistency", "Abstract": "The development of microservice systems is complex due to the impact consistency problems have on the business logic design. On the other hand, the implementation of a microservice requires extensive use of middleware technology, which delays the test and identification of the cases where consistency problems can occur. We introduce the Microservices Simulator object-oriented framework that supports the rapid prototyping of a microservice system designed according to the Transactional Causal Consistency model.", "Keywords": "", "DOI": "10.1016/j.scico.2024.103181", "PubYear": 2025, "Volume": "239", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "INESC-ID, Instituto Superior Técnico, University of Lisbon, Rua Alves Redol 9, 1000-029 Lisbon, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "INESC-ID, Instituto Superior Técnico, University of Lisbon, Rua Alves Redol 9, 1000-029 Lisbon, Portugal;Corresponding author"}], "References": []}, {"ArticleId": 116813798, "Title": "NP-Completeness and Physical Zero-Knowledge Proofs for Sumplete, a Puzzle Generated by ChatGPT", "Abstract": "Sumplete is a logic puzzle generated by ChatGPT in March 2023. The puzzle consists of a rectangular grid, with each cell containing an integer. Each row and column also has an integer called target value assigned to it. The objective of this puzzle is to cross out some numbers in the grid such that the sum of uncrossed numbers in each row and column is equal to the corresponding target value. In this paper, we prove that Sumplete is NP-complete. We also propose a physical zero-knowledge proof protocol for the puzzle using physical cards.", "Keywords": "Physical zero-knowledge proof; Card-based cryptography; NP-completeness; Sumplete; Puzzle", "DOI": "10.1007/s00354-024-00267-0", "PubYear": 2024, "Volume": "42", "Issue": "3", "JournalId": 6245, "JournalTitle": "New Generation Computing", "ISSN": "0288-3635", "EISSN": "1882-7055", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, Chofu, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, Chofu, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, Chofu, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, Chofu, Japan; National Institute of Advanced Industrial Science and Technology, Koto-ku, Japan"}], "References": [{"Title": "Practical card-based implementations of <PERSON>'s millionaire protocol", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "803", "Issue": "", "Page": "207", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Efficient card-based zero-knowledge proof for Sudoku", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "839", "Issue": "", "Page": "135", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Card-based protocols for secure ranking computations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "845", "Issue": "", "Page": "122", "JournalTitle": "Theoretical Computer Science"}, {"Title": "Two Standard Decks of Playing Cards are Sufficient for a ZKP for Sudoku", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1", "Page": "49", "JournalTitle": "New Generation Computing"}, {"Title": "NP-Completeness and Physical Zero-Knowledge Proofs for Sumplete, a Puzzle Generated by ChatGPT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "3", "Page": "429", "JournalTitle": "New Generation Computing"}]}, {"ArticleId": 116813838, "Title": "Data-driven PC-chair-in-the-loop Formation of Program Committees: An EDBT 2023 Experience", "Abstract": "<p>The formation of a quality program committee (PC) for a conference venue is critical for a high-quality scientific program. Traditionally, PC chairs take a \"manual\" approach to form a PC. In practice, however, such an approach, might not create a diverse PC w.r.t. certain dimensions. Furthermore, it has been reported that the traditional manual approach may lead to dense coauthorship networks among PC members. All these aspects can easily make it challenging in practice to ensure fair and quality assignments of reviewers to submissions. In this article, we share our experiences and results of installing a novel data-driven PC-chair-inthe- loop PC formation framework for EDBT 2023 to mitigate some of the challenges brought by traditional PC formation methods.</p>", "Keywords": "", "DOI": "10.1145/3685980.3685998", "PubYear": 2024, "Volume": "53", "Issue": "2", "JournalId": 23659, "JournalTitle": "ACM SIGMOD Record", "ISSN": "0163-5808", "EISSN": "1943-5835", "Authors": [{"AuthorId": 1, "Name": "Sourav S Bhowmick", "Affiliation": "Nanyang Technological University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "TU Wien, Austria"}], "References": []}, {"ArticleId": 116813882, "Title": "Sufficient Convergence Conditions for Conflict Controlled Objects with Different Inertia", "Abstract": "<p>The problem of approach of controlled objects with different inertia in dynamic game problems is considered. For such controlled objects, it is characteristic that the Pontryagin condition is not satisfied on a time interval. To solve the problem, a special multivalued mapping and a matrix function are introduced to equalize the players’ control resources, and then the additional resource is compensated by the body component of the cylindrical terminal set. With the use of the lower resolving function for objects with different inertia, two modified schemes of the first direct Pontryagin method are proposed, which guarantee the successful completion of the conflict-controlled process in the class of countercontrols. The upper resolving function is introduced, and the corresponding modified schemes of the method of resolving functions are presented for controlled objects with different inertia in the class of quasi-strategies and counter-controls. New theoretical results are illustrated by a model example.</p>", "Keywords": "controlled objects with different inertia; quasi-linear differential game; multivalued mapping; measurable selector; stroboscopic strategy; resolving function", "DOI": "10.1007/s10559-024-00698-z", "PubYear": 2024, "Volume": "60", "Issue": "4", "JournalId": 17407, "JournalTitle": "Cybernetics and Systems Analysis", "ISSN": "1060-0396", "EISSN": "1573-8337", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Institute of Cybernetics, National Academy of Sciences of Ukraine, Kyiv, Ukraine; Corresponding author."}, {"AuthorId": 2, "Name": "I. S. Rappoport", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Institute of Cybernetics, National Academy of Sciences of Ukraine, Kyiv, Ukraine"}], "References": [{"Title": "Modifications of the Pontryagin Conditions in the Problem of Approach of Conflict-Controlled Objects", "Authors": "<PERSON><PERSON>; I. S. <PERSON>ort", "PubYear": 2022, "Volume": "58", "Issue": "6", "Page": "935", "JournalTitle": "Cybernetics and Systems Analysis"}, {"Title": "Collaborative pursuit-evasion game of multi-UAVs based on Apollonius circle in the environment with obstacle", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "1", "Page": "2168253", "JournalTitle": "Connection Science"}, {"Title": "Modified Resolving-Functions Method for Game Problems of Approach of Controlled Objects with Different Inertia", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; I. S. <PERSON>ort", "PubYear": 2023, "Volume": "59", "Issue": "2", "Page": "251", "JournalTitle": "Cybernetics and Systems Analysis"}, {"Title": "Direct Method for Solving Game Problems of Approach of Controlled Objects", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; I. S. <PERSON>ort", "PubYear": 2023, "Volume": "59", "Issue": "5", "Page": "812", "JournalTitle": "Cybernetics and Systems Analysis"}]}, {"ArticleId": 116813900, "Title": "TWO APPROACHES TO THE FORMATION OF A QUANTITATIVE MEASURE OF STABILITY BASED ON MULT<PERSON><PERSON> ESTIMATES OF THE PARAMETERS OF AN ENSEMBLE OF TRANSIENT PROCESSES", "Abstract": "<p>The article is devoted to the further development of the theory of stability of dynamic systems, namely of quantitative methods of stability assessment. A review and critical analysis of various approaches, which allow to introduce a quantitative measure of stability of dynamic systems to one degree or another, is given. The limitations of the existing methods, which are primarily related to the assessment of the behavior of the transient processes of individual trajectories, as well as the difficulty of obtaining an assessment of the behavior of the ensemble of transient processes when trying to apply the methods of <PERSON><PERSON> <PERSON><PERSON> are substantiated. A method of quantitative assessment of a dynamic system stability based on the numerical estimates of the behavior of the area of initial deviations from the equilibrium position on the trajectories of the dynamic system is substantiated. Based on the <PERSON><PERSON><PERSON> formula, it is shown that changes in the volume of the area of the initial deviations on the trajectories of the system does not depend on the form of the latter one. This allowed to limit the area of initial deviations in the shape of a hypersphere and to obtain a simple expression for a quantitative measure of the stability of a linear stationary dynamic system, the geometric sense of which is to estimate the rate of change of the volume of the control surface. The article proposes and substantiates the criterion of uniformity of deformation of the area of initial deviations. The essence of the problem is that in the transient process, the values of some components of the phase vector may reach unacceptable deviations from the equilibrium position. A theoretical estimate of deformation non-uniformity for linear systems is obtained, which is taken to be the deviation of the trace of the ellipsoid matrix from the deviations of the trace of the hypersphere matrix of the corresponding volume. A method for a quantitative measure of the stability based on an integral quadratic functional calculated on a set of transient processes of initial deviations in the form of a set of ellipsoids with a normalized volume is proposed and substantiated. Diagonal positive normalized matrices are considered as a set of matrices of the integral quadratic criterion. A simple algorithm for calculation of the multiple integral quadratic criterion is proposed.</p>", "Keywords": "", "DOI": "10.20998/2079-0023.2024.01.04", "PubYear": 2024, "Volume": "", "Issue": "1 (11)", "JournalId": 60745, "JournalTitle": "Bulletin of National Technical University \"KhPI\". Series: System Analysis, Control and Information Technologies", "ISSN": "2079-0023", "EISSN": "2410-2857", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116813920, "Title": "Anomalous data detection in cognitive IoT sensor network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJNVO.2024.140208", "PubYear": 2024, "Volume": "30", "Issue": "4", "JournalId": 14131, "JournalTitle": "International Journal of Networking and Virtual Organisations", "ISSN": "1470-9503", "EISSN": "1741-5225", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116813998, "Title": "Structuring Success", "Abstract": "<p>The problem with software structure is people do not really learn it until they really need it.</p>", "Keywords": "", "DOI": "10.1145/3665518", "PubYear": 2024, "Volume": "67", "Issue": "8", "JournalId": 10706, "JournalTitle": "Communications of the ACM", "ISSN": "0001-0782", "EISSN": "1557-7317", "Authors": [{"AuthorId": 1, "Name": "<PERSON>-<PERSON>", "Affiliation": "Neville<PERSON><PERSON>, Brooklyn, NY, USA"}], "References": []}, {"ArticleId": 116814002, "Title": "Comparative Analysis of High Speed and Area Efficient Full-Adder using CMOS and MGDI Techniques", "Abstract": "<p>Extremely quick processing is crucial for very large-scale integrated (VLSI) circuits in the arithmetic logic unit. Complementary metal oxide semiconductor (CMOS) and modified gate diffusion input (MGDI) are beneficial technologies for designing high-speed circuits with better reliability and performance with regard to area and power consumption. This research work provides a comparative performance analysis of a full adder that is implemented with CMOS and MGDI technology. This work aims to develop a CMOS-based full-adder and a MGDI-based full-adder circuit for area-efficient and high-speed applications. First, the analysis and implementation of the XOR and NAND logic gates are designed using CMOS technology and the MGDI technique to create a full-adder design. The comparative analysis of integrated circuits in CMOS and MGDI technologies is primarily determined by the number of transistors and delay time. The full-adder is designed and analyzed using 90 nm technology, with performance characteristics evaluated using Cadence Virtuoso Tools.</p>", "Keywords": "Full adder; CMOS; MGDI; High speed; Delay; Area efficient", "DOI": "10.33022/ijcs.v13i3.4003", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mandalay Technological University"}, {"AuthorId": 2, "Name": "Tin Tin Hla", "Affiliation": "Mandalay Technological University"}], "References": []}, {"ArticleId": *********, "Title": "Extracting process-aware decision models from object-centric process data", "Abstract": "Organizations execute decisions within business processes on a daily basis whilst having to take into account multiple stakeholders who might require multiple point of views of the same process. Moreover, the complexity of the information systems running these business processes is generally high as they are linked to databases storing all the relevant data and aspects of the processes. Given the presence of multiple objects within an information system which support the processes in their enactment, decisions are naturally influenced by both these perspectives, logged in object-centric process logs. However, the discovery of such decisions from object-centric process logs is not straightforward as it requires to correctly link the involved objects whilst considering the sequential constraints that business processes impose as well as correctly discovering what a decision actually does. This paper proposes the first object-centric decision-mining algorithm called Integrated Object-centric Decision Discovery Algorithm (IODDA). IODDA is able to discover how a decision is structured as well as how a decision is made. Moreover, IODDA is able to discover which activities and object types are involved in the decision-making process. Next, IODDA is demonstrated with the first artificial knowledge-intensive process logs whose log generators are provided to the research community.", "Keywords": "", "DOI": "10.1016/j.ins.2024.121263", "PubYear": 2024, "Volume": "682", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Leuven Institute for Research on Information Systems (LIRIS), KU Leuven, Naamsestraat 69, <PERSON><PERSON><PERSON>, 3000, Belgium;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Leuven Institute for Research on Information Systems (LIRIS), KU Leuven, Naamsestraat 69, Leuven, 3000, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Leuven Institute for Research on Information Systems (LIRIS), KU Leuven, Naamsestraat 69, Leuven, 3000, Belgium"}], "References": [{"Title": "Discovering Object-centric Petri Nets", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "175", "Issue": "1-4", "Page": "1", "JournalTitle": "Fundamenta Informaticae"}, {"Title": "Object-centric process predictive analytics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119173", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 116814129, "Title": "Unlocking inclusive education: A quality assessment of software design in applications for children with autism", "Abstract": "Digital technologies are an essential resource for maximizing education opportunities, yet the COVID-19 pandemic has exposed learning inequities, particularly among underrepresented groups such as children with autism. In this study, we have evaluated the quality of a distinctive dataset of multiplatform software applications, encompassing both assistive and mainstream software, first-hand acquired from special education professionals and families of children with autism. Through a heuristic evaluation based on a system indicators, and an in-depth analysis, we aimed to (1) assess the quality and effectiveness of assistive technologies in supporting the education of children with autism; (2) determine the adaptability of mainstream applications to the unique educational needs of children with autism; and (3) explore the features and constraints of applications targeting children with ASD, categorized according to the needs they cover. The resulting quality ranking, organized by cognitive domains, provides insights into the engagement and effectiveness of applications supporting the learning of children with autism. Furthermore, the findings delineating the functionalities and limitations of these applications contribute to the identification of necessary software engineering best practices. These practices align with user-centered design principles and drive the development of accessible software, thereby fostering high-quality inclusive education for children with autism.", "Keywords": "Quality assessment; Software engineering education; Autism; Inclusive software", "DOI": "10.1016/j.jss.2024.112164", "PubYear": 2024, "Volume": "217", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Universidad Autónoma de Madrid, Ciudad Universitaria de Cantoblanco, Madrid, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Autónoma de Madrid, Ciudad Universitaria de Cantoblanco, Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidad Autónoma de Madrid, Ciudad Universitaria de Cantoblanco, Madrid, Spain"}], "References": [{"Title": "Serious games to improve social and emotional intelligence in children with autism", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "", "Page": "100417", "JournalTitle": "Entertainment Computing"}, {"Title": "Educational Software as Assistive Technologies for Children with Autism Spectrum Disorder", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "6", "JournalTitle": "Procedia Computer Science"}, {"Title": "Using emotion recognition technologies to teach children with autism spectrum disorder how to identify and express emotions", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "809", "JournalTitle": "Universal Access in the Information Society"}, {"Title": "Towards more supportive ICT for children with autism spectrum disorders: lessons learned from COVID-19 pandemic", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "43", "Issue": "12", "Page": "3027", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Inclusion of individuals with autism spectrum disorder in software engineering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "170", "Issue": "", "Page": "107434", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 116814163, "Title": "2G3-2　製造業におけるDX推進が精神的作業負荷に及ぼす影響について", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2G3-2", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>uki IZUMI", "Affiliation": "Japan Steel Works M&amp;E, Inc."}], "References": []}, {"ArticleId": 116814169, "Title": "High-resolution wide-swath SAR imaging with multifrequency pulse diversity mode in azimuth multichannel system", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2024.2379517", "PubYear": 2024, "Volume": "45", "Issue": "17", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 2, "Name": "Hongyi Lu", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Control Engineering, China University of Mining and Technology, Xuzhou, China"}], "References": [{"Title": "Study on coding scheme for space-pulse-phase-coding-based high-resolution and wide-swath SAR imaging", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "18", "Page": "7202", "JournalTitle": "International Journal of Remote Sensing"}]}, {"ArticleId": 116814227, "Title": "Credit Card Fraud Detection Based on Machine Learning Classification Algorithm", "Abstract": "<p>Credit risk analysis is a critical process in the financial industry, as it helps lenders assess the likelihood of borrowers defaulting on their loans. With the advent of machine learning algorithms, there has been a growing interest in leveraging these techniques for more accurate and efficient credit risk prediction. Traditional credit risk models often rely on manual processes and limited data sources, resulting in potential biases and inaccuracies. Additionally, the rapid growth of credit card usage and the increasing complexity of financial transactions have made it challenging to accurately assess credit risk using conventional methods. This review paper aims to provide a comprehensive overview of machine learning algorithms used for credit risk prediction in the context of credit card lending. It explores classification techniques and their applications in credit risk analysis. The paper also discusses the challenges and limitations associated with these algorithms, including data quality, overfitting, and interpretability.</p>", "Keywords": "Risk Analysis; Prediction; Credit Card; Machine Learning Algorithm; Credit Card Risk", "DOI": "10.33022/ijcs.v13i3.3996", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technical College of Duhok, Duhok Polytechnic University, Kurdistan Region, Iraq"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116814239, "Title": "AN ADAPTIVE METHOD FOR BUILDING A MULTIVARIATE REGRESSION", "Abstract": "<p>We propose an adaptive method for building a multivariate regression given by a weighted linear convolution of known scalar functions of deterministic input variables with unknown coefficients. As, for example, when multivariate regression is given by a multivariate polynomial. In contrast to the general procedure of the least squares method that minimizes only a single scalar quantitative measure, the adaptive method uses six different quantitative measures and represents a systemically connected set of different algorithms which allow each applied problem to be solved on their basis by an individual adaptive algorithm that, in the case of an active experiment, even for a relatively small volume of experimental data, implements a strategy of a statistically justified solving. The small amount of data of the active experiment we use in the sense that, for such an amount, the variances of estimates of unknown coefficients obtained by the general procedure of the least squares method do not allow to guarantee the accuracy acceptable for practice. We also proposed to significantly increase the efficiency of the proposed by <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON> modified group method of data handling for building a multivariate regression which is linear with respect to unknown coefficients and given by a redundant representation. We improve it by including some criteria and algorithms of the adaptive method for building a multivariate regression. For the multivariate polynomial regression problem, the inclusion of a partial case of the new version of the modified group method of data handling in the synthetic method proposed by <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, for building a multivariate polynomial regression given by a redundant representation, also significantly increases its efficiency.</p>", "Keywords": "multivariate regression;integral measure;adaptive algorithm;regression analysis;expert coefficients;linear programming", "DOI": "10.20998/2079-0023.2024.01.01", "PubYear": 2024, "Volume": "", "Issue": "1 (11)", "JournalId": 60745, "JournalTitle": "Bulletin of National Technical University \"KhPI\". Series: System Analysis, Control and Information Technologies", "ISSN": "2079-0023", "EISSN": "2410-2857", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Technical University of Ukraine \"Igor <PERSON> Kyiv Polytechnic Institute\""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Technical University of Ukraine \"Igor <PERSON> Kyiv Polytechnic Institute\""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Technical University of Ukraine \"Igor <PERSON> Kyiv Polytechnic Institute\""}], "References": []}, {"ArticleId": 116814280, "Title": "Design, Simulation, and Experimental Validation of a New Fuzzy Logic-Based Maximal Power Point Tracking Strategy for Low Power Wind Turbines", "Abstract": "<p>With the fast-growing demand for wind energy, small wind turbines are becoming a viable and cost-effective source of clean and renewable electricity for residential, businesses, small industries, and remote applications. To harness the maximum power, wind turbines (WT) should be operated at full capacity; hence, it is crucial to design an effective maximum power point tracking (MPPT) control scheme. This paper presents an effective MPPT control algorithm based on fuzzy logic control (FLC) which tracks the slope of the mechanical power of the WT as a function of the rotational speed. The output of this MPPT generates the duty cycle for the DC–DC converter to allow the WT to extract the maximum power under a wide range of wind speed and loading conditions. The electrical generator used in the studied wind energy conversion system is the permanent magnet synchronous generator (PMSG). The proposed FLC-MPPT strategy is simple to implement and does not require any prior knowledge of the WT parameters. The proposed FLC-MPPT technique for WT is simulated using MATLAB/Simulink and validated experimentally on a laboratory setup using a dSPACE1104 board. In addition, a comparison between the proposed FLC-MPPT and other methods under significant wind speed fluctuations and variable load conditions is presented to demonstrate its effectiveness. The results show that the proposed MPPT has a fast convergence with a high tracking accuracy and exhibits low steady-state oscillations.</p>", "Keywords": "Wind turbine; Fuzzy controller; Boost converter; MPPT; PMSG; Load change; dSPACE1104", "DOI": "10.1007/s40815-024-01747-7", "PubYear": 2024, "Volume": "26", "Issue": "8", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "IRECOM Laboratory, Faculty of Electrical Engineering, Djillali Liabes University, Sidi Bel-Abbes, Algeria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IRECOM Laboratory, Faculty of Electrical Engineering, Djillali Liabes University, Sidi Bel-Abbes, Algeria; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "UNIROUEN, ESIGELEC, IRSEEM, Normandy University, Rouen, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Higher School of Electrical Engeneering and Energetics, Oran, Algeria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical & Electronics Engineering Technology Department, Royal Commission Yanbu Colleges & Institutes, Yanbu Industrial College (YIC), Yanbu, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Electrical & Electronics Engineering Technology Department, Royal Commission Yanbu Colleges & Institutes, Yanbu Industrial College (YIC), Yanbu, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Electrical Engineering Department, Faculty of Energy Engineering, Aswan University, Aswan, Egypt"}], "References": []}, {"ArticleId": 116814331, "Title": "The Small Gain Theorem in the Context of Sampled-Data Systems", "Abstract": "<p>This paper is entirely devoted to analyzing robust control of sampled-data systems. For that, conditions for robust stability under static and dynamic norm-bounded uncertainty are presented and are all expressed through differential linear matrix inequality, a useful mathematical tool able to keep convex all problems to be dealt with. A suitable version of the celebrated small gain theorem (SGT) is then presented and proved in the context of sampled-data systems with inputs and outputs defined in both, continuous- and discrete-time domains. Some academic examples are solved to illustrate the theory and to put in evidence the computational burden needed to calculate their solutions. Since the SGT is only sufficient, the examples are used to discuss and evaluate its conservatism.</p>", "Keywords": "Sampled-data linear systems; Differential linear matrix inequality; Robustness; Small gain theorem; Numerical methods", "DOI": "10.1007/s40313-024-01108-7", "PubYear": 2024, "Volume": "35", "Issue": "5", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Computer Engineering, UNICAMP, Campinas, Brazil; Corresponding author."}], "References": []}, {"ArticleId": 116814359, "Title": "High-dimensional multi-objective optimization of coupled cross-laminated timber walls building using deep learning", "Abstract": "This paper introduces a deep learning-based multi-objective optimization framework, applying for advancing the design of the Cross-Laminated Timber Coupled Wall system. While traditional optimization methods often struggle with the curse of dimensionality in high-dimensional problems, the approach proposed in this study employs an autoencoder to effectively reduce the dimensionality of the design space. Subsequently, a neural network establishes a mapping between input variables and latent spaces, with another neural network forming the crucial link between these latent variables and the output responses. The proposed framework is integrated into the design process of a 20-story Cross-Laminated Timber Coupled Wall system, where uncertainties inherent in connection elements are systematically addressed to optimize the structural parameters. The non-dominated sorting genetic algorithm-II is utilized to estimate optimal design variables by minimizing three conflicting objective functions, thereby generating a Pareto front. This optimized design is then bench-marked against three deterministic models with varying coupling beam shear strengths. A two-dimensional numerical model developed in OpenSees facilitates nonlinear time history analysis using 50 bi-directional ground motion records, representative of the seismicity of Vancouver, Canada. The results of this study not only highlight the efficacy of the deep learning-based framework in enhancing the structural integrity and resilience of high-rise timber structures in seismic regions but also significantly contribute to the evolution of computational approaches in structural engineering.", "Keywords": "Deep learning; Autoencoder; Dimension reduction; Multi-objective optimization; Cross-laminated timber (CLT); Coupling beams; Uncertainty", "DOI": "10.1016/j.engappai.2024.109055", "PubYear": 2024, "Volume": "136", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Waterloo, 200 University Ave W, Waterloo, N2L 3G1, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Waterloo, 200 University Ave W, Waterloo, N2L 3G1, ON, Canada;School of Engineering, The University of British Columbia, Okanagan Campus, 3333 University Way, Kelowna, V1V 1V7, BC, Canada"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of Waterloo, 200 University Ave W, Waterloo, N2L 3G1, ON, Canada;Corresponding author"}], "References": [{"Title": "Potential-based multiobjective reinforcement learning approaches to low-impact agents for AI safety", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "104186", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "An efficient nonlinear interval uncertain optimization method using Legendre polynomial chaos expansion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107454", "JournalTitle": "Applied Soft Computing"}, {"Title": "A radial basis function surrogate model assisted evolutionary algorithm for high-dimensional expensive optimization problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "108353", "JournalTitle": "Applied Soft Computing"}, {"Title": "An automatic kriging machine learning method to calibrate meta-heuristic algorithms for solving optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "113", "Issue": "", "Page": "104940", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Evolutionary feature selection on high dimensional data using a search space reduction approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105556", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A Kriging model-based evolutionary algorithm with support vector machine for dynamic multimodal optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106039", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Investigating the influence of dimensionality reduction on force estimation in robotic-assisted surgery using recurrent and convolutional networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107045", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep learning-driven pavement crack analysis: Autoencoder-enhanced crack feature extraction and structure classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "132", "Issue": "", "Page": "107949", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 116814394, "Title": "2E1-2　鎌倉行き来バリアフリー：バリアフリートイレとその案内標識", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2E1-2", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kanagawa University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ohara Memorial Research Institute of Labour"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kanagawa University"}], "References": []}, {"ArticleId": 116814404, "Title": "A Review on Utilizing Data Mining Techniques for Chronic Kidney Disease Detection", "Abstract": "<p>This comprehensive study delves into the application of machine learning (ML) and data mining techniques for the prognosis and diagnosis of Chronic Kidney Disease (CKD), a significant global health concern characterized by the gradual loss of kidney function. Through a detailed examination of various predictive models, the research evaluates the efficacy of different ML algorithms and data mining methodologies in classifying and diagnosing CKD. Utilizing datasets from the UCI machine learning repository and other sources, this study explores a range of ML algorithms-including logistic regression, decision trees, support vector machines, random forest, and deep learning networks-alongside feature selection techniques to enhance prediction accuracy and facilitate early diagnosis. Despite facing challenges such as dataset limitations and the need for external validation, the findings reveal remarkable potential in using ML and data mining to improve CKD diagnosis, with some models achieving accuracy rates exceeding 99%. The research underscores the critical role of technology in advancing CKD diagnosis and management, paving the way for more personalized and effective healthcare solutions.</p>", "Keywords": "", "DOI": "10.33022/ijcs.v13i3.4062", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Duhok Polytechnic University, Duhok, Kurdistan Region, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Duhok Technical College, Duhok Polytechnic University, Iraq"}], "References": []}, {"ArticleId": 116814408, "Title": "Retaining remote workers: factors that affect virtual and hybrid workers' job retention", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJNVO.2024.140215", "PubYear": 2024, "Volume": "30", "Issue": "4", "JournalId": 14131, "JournalTitle": "International Journal of Networking and Virtual Organisations", "ISSN": "1470-9503", "EISSN": "1741-5225", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Nopadol Rompho", "Affiliation": ""}], "References": []}, {"ArticleId": 116814444, "Title": "An Optimal Model for Medical Text Classification Based on Adaptive Genetic Algorithm", "Abstract": "Automatic text classification, in which textual data is categorized into specified categories based on its content, is a classic issue in the science of Natural Language Processing. In recent years, there has been a notable surge in research on medical text classification due to the increasing availability of medical data like patient medical records and medical literature. Machine learning and statistical methods, such as those used in medical text classification, have proven to be highly efficient for these tasks. However, a significant amount of manual labor is still required to categorize the extensive dataset utilized for training. Recent research have demonstrated the effectiveness of pretrained language models, including machine learning models, in reducing the time and effort required for feature engineering by medical experts. However, there is no statistically significant enhancement in performance when directly applying the machine learning model to the classification task. In this paper, we present a hybrid machine learning model that combines individual traditional algorithms augmented by a genetic algorithm. However, the improved model is designed to enhance performance by optimizing the weight parameter. In this context, the best single model demonstrated commendable accuracy. In addition, when applying the hybridization approach and optimizing the weight parameters, the results were substantially enhanced. The results underscore the superiority of our augmented hybrid model over individual traditional algorithms. We conduct experiments using two distinct types of datasets: one comprising medical records, such as the Heart Failure Clinical Record and another consisting of medical literature, such as PubMed 20k RCT. So, the objective is to clearly showcase the effectiveness of our approach by highlighting the significant enhancements in accuracy, precision, F1-score and Recall achieved through our improved model.", "Keywords": "Medical text classification; Ensemble learning; Optimization; Genetic algorithm", "DOI": "10.1007/s41019-024-00257-8", "PubYear": 2024, "Volume": "9", "Issue": "4", "JournalId": 15887, "JournalTitle": "Data Science and Engineering", "ISSN": "2364-1185", "EISSN": "2364-1541", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Team in Intelligent Machines (RTIM), National School of Engineering of Gabes, Gabes University, Gabes, Tunisia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer and Information Sciences, Jouf University, Sakkaka, Saudi Arabia; Research laboratory on Development and Control of Distributed Applications (REDCAD), ENIS, Sfax University, Sfax, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Team in Intelligent Machines (RTIM), National School of Engineering of Gabes, Gabes University, Gabes, Tunisia"}], "References": [{"Title": "A survey on ensemble learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "241", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Neighbor-aware review helpfulness prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "113581", "JournalTitle": "Decision Support Systems"}, {"Title": "Genetic Algorithms in the Fields of Artificial Intelligence and Data Sciences", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "10", "Issue": "4", "Page": "1007", "JournalTitle": "Annals of Data Science"}, {"Title": "MDDE: multitasking distributed differential evolution for privacy-preserving database fragmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "5", "Page": "957", "JournalTitle": "The VLDB Journal"}, {"Title": "Classification of movie reviews using term frequency-inverse document frequency and optimized machine learning algorithms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "e914", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Pima Indians diabetes mellitus classification based on machine learning (ML) algorithms", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "22", "Page": "16157", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A knowledge graph empowered online learning framework for access control decision-making", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "2", "Page": "827", "JournalTitle": "World Wide Web"}, {"Title": "Covid-19 vaccine hesitancy: Text mining, sentiment analysis and machine learning on COVID-19 vaccination Twitter dataset", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118715", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Detecting ham and spam emails using feature union and supervised machine learning models", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "17", "Page": "26545", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An Integrated Statistical and Clinically Applicable Machine Learning Framework for the Detection of Autism Spectrum Disorder", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "5", "Page": "92", "JournalTitle": "Computers"}]}, {"ArticleId": 116814460, "Title": "1E4-4　若年者の安全衛生意識を向上させるためのミーティングツールアプローチの提案", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1E4-4", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Fumiko MATSUDA", "Affiliation": "The Ohara Memorial Institute for Science of Labour"}, {"AuthorId": 2, "Name": "Thor IKEGAMI", "Affiliation": "The Ohara Memorial Institute for Science of Labour"}, {"AuthorId": 3, "Name": "Masayo YAMAMURA", "Affiliation": "The Ohara Memorial Institute for Science of Labour"}], "References": []}, {"ArticleId": 116814604, "Title": "Use and impact of ChatGPT on undergraduate engineering students: A case from the Philippines", "Abstract": "", "Keywords": "", "DOI": "10.1080/10875301.2024.2384028", "PubYear": 2024, "Volume": "28", "Issue": "4", "JournalId": 25067, "JournalTitle": "Internet Reference Services Quarterly", "ISSN": "1087-5301", "EISSN": "1540-4749", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "General Education Department, Colegio de Muntinlupa, Muntinlupa City, Philippines;School of Chemical, Biological and Materials Engineering and Sciences, Mapua University, Manila, Philippines"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "General Education Department, Colegio de Muntinlupa, Muntinlupa City, Philippines;School of Chemical, Biological and Materials Engineering and Sciences, Mapua University, Manila, Philippines"}], "References": [{"Title": "ChatGPT and Its Possible Impact on Library Reference Services", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "2", "Page": "121", "JournalTitle": "Internet Reference Services Quarterly"}, {"Title": "ChatGPT: Jack of all trades, master of none", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101861", "JournalTitle": "Information Fusion"}, {"Title": "ChatGPT References Unveiled: Distinguishing the Reliable from the Fake", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "1", "Page": "9", "JournalTitle": "Internet Reference Services Quarterly"}]}, {"ArticleId": 116814617, "Title": "CTNet: an efficient coupled transformer network for robust hyperspectral unmixing", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2024.2371084", "PubYear": 2024, "Volume": "45", "Issue": "17", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Changchun University, Changchun, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Changchun University, Changchun, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Changchun University, Changchun, China"}, {"AuthorId": 4, "Name": "Tingfa Xu", "Affiliation": "School of Optics and Photonics, Beijing Institute of Technology, Beijing, China"}], "References": []}, {"ArticleId": 116814635, "Title": "Multi-scale task-aware structure graph modeling for few-shot image recognition", "Abstract": "The Few-shot image recognition attempts to recognize images from a novel class with only a limited number of labeled training images, which is a few-shot learning (FSL) task. FSL is very challenging. Limited labeled training samples cannot adequately represent the distribution of classes, and the base and novel classes in the training and testing stages do not intersect and have different distributions, leading to a domain shift problem in generalizing the learned model to the novel class dataset. In this paper, we propose multi-scale task-aware structure graph modeling for few-shot image recognition. We train a meta-filter learner to generate task-aware local structure filters for each scale and adaptively capture the local structures at each scale. Moreover, we introduce a novel multi-scale graph attention network (MGAT) module to model the multi-scale local structures of an image, fully exploring the correlations between different local structures at all scales of the image. Finally, we leverage the attention mechanism of graph attention network to achieve information aggregation and propagation, aiming to obtain more representative and discriminative local structure features that integrate both local and global information. We conducted comprehensive experiments on four benchmark datasets widely adopted in FSL tasks. The experimental results demonstrate that the MTSGM obtains state-of-the-art performance, which validates the effectiveness of MTSGM.", "Keywords": "Few-shot learning; Multi-scale representation; Task-aware; Graph attention network", "DOI": "10.1016/j.patcog.2024.110855", "PubYear": 2024, "Volume": "156", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei 230601, China;Key Laboratory of Intelligent Computing and Signal Processing of Ministry of Education, Anhui University, Hefei 230601, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Ye", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei 230601, China;Key Laboratory of Intelligent Computing and Signal Processing of Ministry of Education, Anhui University, Hefei 230601, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei 230601, China;Key Laboratory of Intelligent Computing and Signal Processing of Ministry of Education, Anhui University, Hefei 230601, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Liu", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei 230601, China;Key Laboratory of Intelligent Computing and Signal Processing of Ministry of Education, Anhui University, Hefei 230601, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Anhui University, Hefei 230601, China;Key Laboratory of Intelligent Computing and Signal Processing of Ministry of Education, Anhui University, Hefei 230601, China"}], "References": [{"Title": "Local descriptor-based multi-prototype network for few-shot Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107935", "JournalTitle": "Pattern Recognition"}, {"Title": "Unsupervised descriptor selection based meta-learning networks for few-shot classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108304", "JournalTitle": "Pattern Recognition"}, {"Title": "Learning attention-guided pyramidal features for few-shot fine-grained recognition", "Authors": "Hao Tang; Chengcheng Yuan; Zechao Li", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "108792", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-granularity episodic contrastive learning for few-shot learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108820", "JournalTitle": "Pattern Recognition"}, {"Title": "Self-guided information for few-shot classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108880", "JournalTitle": "Pattern Recognition"}, {"Title": "Few-shot learning with unsupervised part discovery and part-aligned similarity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "108986", "JournalTitle": "Pattern Recognition"}, {"Title": "Self-regularized prototypical network for few-shot semantic segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109018", "JournalTitle": "Pattern Recognition"}, {"Title": "Query-guided networks for few-shot fine-grained classification and person search", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109049", "JournalTitle": "Pattern Recognition"}, {"Title": "SAPENet: Self-Attention based Prototype Enhancement Network for Few-shot Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109170", "JournalTitle": "Pattern Recognition"}, {"Title": "Global- and local-aware feature augmentation with semantic orthogonality for few-shot image classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "109702", "JournalTitle": "Pattern Recognition"}, {"Title": "Edge-labeling based modified gated graph network for few-shot learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "150", "Issue": "", "Page": "110264", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 116814667, "Title": "2F1-6　目視内ドローン操縦中における視覚的ノイズによる空間認識精度に関する研究", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2F1-6", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kotaro YAMAMOTO", "Affiliation": "Graduate school of science and engineering, Kindai University"}, {"AuthorId": 2, "Name": "Koji TANIDA", "Affiliation": "Graduate school of science and engineering, Kindai University"}], "References": []}, {"ArticleId": 116814686, "Title": "Customer service chatbot enhancement with attention-based transfer learning", "Abstract": "Customer service is an important and expensive aspect of business, often being the largest department in most companies. With growing societal acceptance and increasing cost efficiency due to mass production, service robots are beginning to cross from the industrial domain to the social domain. Currently, customer service robots tend to be digital and emulate social interactions through on-screen text, but state-of-the-art research points towards physical robots soon providing customer service in person. This article explores the feasibility of Transfer Learning different customer service domains to improve chatbot models. In our proposed approach, transfer learning-based chatbot models are initially assigned to learn one domain from an initial random weight distribution. Each model is then tasked with learning another domain by transferring knowledge from the previous domains. To evaluate our approach, a range of 19 companies from domains such as e-Commerce, telecommunications, and technology are selected through social interaction with X (formerly Twitter) customer support accounts. The results show that the majority of models are improved when transferring knowledge from at least one other domain, particularly those more data-scarce than others. General language transfer learning is observed, as well as higher-level transfer of similar domain knowledge. For each of the 19 domains, the Wilcoxon signed-rank test suggests that 16 have statistically significant distributions between transfer and non-transfer learning. Finally, feasibility is explored for the deployment of chatbot models to physical robot platforms including “Pepper”, a semi-humanoid robot manufactured by SoftBank Robotics, and <PERSON><PERSON><PERSON><PERSON>, a personal assistant robot.", "Keywords": "Chatbot; Customer service robotics; Social robotics; Human–robot interaction; Natural language processing", "DOI": "10.1016/j.knosys.2024.112293", "PubYear": 2024, "Volume": "301", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science at Nottingham Trent University, Nottingham, United Kingdom;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science at Nottingham Trent University, Nottingham, United Kingdom"}], "References": [{"Title": "Chinese sentiment classification based on Word2vec and vector arithmetic in human–robot conversation", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "95", "Issue": "", "Page": "107423", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 116814864, "Title": "Correction: Instant3D: Instant Text-to-3D Generation", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11263-024-02193-6", "PubYear": 2025, "Volume": "133", "Issue": "1", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Data Science, National University of Singapore, Singapore, Singapore; Sea AI Lab, Singapore, Singapore"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing and Information Systems, Singapore Management University, Singapore, Singapore"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Show Lab, National University of Singapore, Singapore, Singapore"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, National University of Singapore, Singapore, Singapore"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Sea AI Lab, Singapore, Singapore"}, {"AuthorId": 6, "Name": "Shuicheng Yan", "Affiliation": "Skywork AI, Singapore, Singapore"}, {"AuthorId": 7, "Name": "Xiangyu Xu", "Affiliation": "<PERSON><PERSON>an Jiaotong University, Xi’an, China; Corresponding author."}], "References": []}, {"ArticleId": 116814882, "Title": "2E3-1　パイロット訓練データからの知識抽出に関する基礎研究", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2E3-1", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Yuji HARATAKE", "Affiliation": "Graduate School of Engineering, Tohoku University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Engineering, Tohoku University"}], "References": []}, {"ArticleId": 116815000, "Title": "Research on Information Fusion Method for Human Computer Interaction Interface of Command and Control System in MR Environment", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2383028", "PubYear": 2025, "Volume": "41", "Issue": "4", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Air and Missile Defense College, Air Force Engineering University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Air and Missile Defense College, Air Force Engineering University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Air and Missile Defense College, Air Force Engineering University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Air and Missile Defense College, Air Force Engineering University, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Air and Missile Defense College, Air Force Engineering University, Xi’an, China"}], "References": [{"Title": "Adaptive feature guidance: Modelling visual search with graphical layouts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "102376", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Mixed Reality Based Visual Analytics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "821", "JournalTitle": "Journal of Computer-Aided Design & Computer Graphics"}, {"Title": "The Impact of Golden Ratio Application on User Satisfaction: A Study on Horizontal Scrolling in User Interface (UI) Design", "Authors": "<PERSON>", "PubYear": 2025, "Volume": "41", "Issue": "1", "Page": "445", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 116815060, "Title": "How Design Researchers Make Sense of Data Visualizations in Data-Driven Design: An Uncertainty-aware Sensemaking Model", "Abstract": "<p>While data is the cornerstone of modern design strategies, design researchers frequently struggle when performing data work. This creates a need to design tools that enable design researchers to actively engage with data. However, this presupposes understanding how design researchers create meaning from data representations, as the way of visualizing the data, along with other factors, can significantly impact the extracted insights, increasing uncertainty about the quality of the outcome. As a response to this problem, we explore how design researchers make sense of data in a case study: making sense of paired subjective and objective sleep and stress data visualizations. By synthesizing our findings from two user studies, we construct a sensemaking model which highlights how uncertainty related to data qualities, visualization parameters, and the viewer’s background, affects the insight-generation process. Our findings have implications for the future development of tools and techniques for visual data sensemaking for designers.</p>", "Keywords": "", "DOI": "10.1145/3685268", "PubYear": 2024, "Volume": "31", "Issue": "6", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Eindhoven University of Technology, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Eindhoven University of Technology, The Netherlands"}], "References": [{"Title": "Foregrounding data in co-design – An exploration of how data may become an object of design", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "102505", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "Sunspot Plots: Model‐based Structure Enhancement for Dense Scatter Plots", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "3", "Page": "551", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Talking datasets – Understanding data sensemaking behaviours", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "102562", "JournalTitle": "International Journal of Human-Computer Studies"}, {"Title": "In Search for Design Elements: A New Perspective for Employing Ethnography in Human-Computer Interaction Design Research", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "8", "Page": "783", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "The agency of algorithms: Understanding human-algorithm interaction in administrative decision-making", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "4", "Page": "507", "JournalTitle": "Information Polity"}, {"Title": "The Evolution of HCI and Human Factors: Integrating Human and Artificial Intelligence", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}, {"Title": "Seeing Through Things: Exploring the Design Space of Privacy-aware Data-enabled Objects", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Computer-Human Interaction"}]}, {"ArticleId": 116815125, "Title": "Rancang Bangun IoT Based Monitoring System pada Multi Conveyor Untuk Perpindahan Benda", "Abstract": "<p>Sistem multi-konveyor rentan terhadap kegagalan titik transfer yang dapat mengganggu rute transportasi dan menciptakan bottleneck dalam produksi. Penelitian ini merancang dan mengimplementasikan sistem pemantauan berbasis IoT untuk mendeteksi kemacetan dan benda terguling pada multi-konveyor dengan sudut 90 derajat. Menggunakan metodologi VDI2206, desain mekanik diprioritaskan untuk memperkecil jarak transfer benda antar konveyor sabuk. Sistem ini dilengkapi sensor presisi dan algoritma kontrol untuk memantau proses transfer. Antarmuka pengguna yang intuitif memudahkan pemantauan data sensor dan animasi real-time. Dua mikrokontroler ESP32 digunakan untuk mengoordinasikan fungsi sensor dan komunikasi data secara asinkron dengan waktu respons rata-rata 449,508 ms. Hasilnya adalah sistem pemantauan yang efisien, meningkatkan efisiensi dan mengurangi biaya serta waktu pemantauan manual dalam industri.</p>", "Keywords": "", "DOI": "10.33022/ijcs.v13i3.4098", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Manufaktur Bandung"}, {"AuthorId": 2, "Name": "<PERSON> <PERSON>", "Affiliation": "Politeknik Manufaktur Bandung"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Polman Bandung"}], "References": []}, {"ArticleId": 116815165, "Title": "1F1-1　内視鏡外科手術の進歩と人間工学的課題", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1F1-1", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>INOH<PERSON>", "Affiliation": "Tokyo University of Technology, School of Health Sciences"}], "References": []}, {"ArticleId": 116815190, "Title": "2023 ACM Fellow: <PERSON>, Microsoft.", "Abstract": "<p>My friend and Microsoft colleague <PERSON> was recently named an ACM Fellow. I had the pleasure of interviewing him over email about his work and career.</p>", "Keywords": "", "DOI": "10.1145/3686138.3686142", "PubYear": 2024, "Volume": "28", "Issue": "2", "JournalId": 41950, "JournalTitle": "GetMobile: Mobile Computing and Communications", "ISSN": "2375-0529", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Microsoft"}], "References": []}, {"ArticleId": 116815192, "Title": "2E3-2　運転支援機能による車線内ふらつき低減の操作支援効果", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2E3-2", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Takemi TSUKADA", "Affiliation": "Honda Motor Co., Ltd."}, {"AuthorId": 2, "Name": "Kentaro KASUYA", "Affiliation": "Honda Motor Co., Ltd."}, {"AuthorId": 3, "Name": "Hiroyasu KUBOTA", "Affiliation": "Honda Motor Co., Ltd."}, {"AuthorId": 4, "Name": "Shuichi OKADA", "Affiliation": "Honda Motor Co., Ltd."}, {"AuthorId": 5, "Name": "Yukiyo KURIYAGAWA", "Affiliation": "Nihon University"}, {"AuthorId": 6, "Name": "Motonori ISHIBASHI", "Affiliation": "Nihon University"}], "References": []}, {"ArticleId": 116815200, "Title": "A Double-Interactively Recurrent Fuzzy Cerebellar Model Articulation Controller Model Combined with an Improved Particle Swarm Optimization Method for Fall Detection", "Abstract": "", "Keywords": "", "DOI": "10.32604/csse.2024.052931", "PubYear": 2024, "Volume": "48", "Issue": "5", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A sequential deep learning application for recognising human activities in smart homes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "396", "Issue": "", "Page": "501", "JournalTitle": "Neurocomputing"}, {"Title": "Convolutional neural network: a review of models, methodologies and applications to object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "85", "JournalTitle": "Progress in Artificial Intelligence"}, {"Title": "RETRACTED ARTICLE: Fall detection based on posture classification for smart home environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "3581", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Online Fall Detection Using Recurrent Neural Networks on Smart Wearable Devices", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "3", "Page": "1276", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}, {"Title": "A multimodal approach using deep learning for fall detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114226", "JournalTitle": "Expert Systems with Applications"}, {"Title": "ARFDNet: An efficient activity recognition & fall detection system using latent feature pooling", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "239", "Issue": "", "Page": "107948", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 116815238, "Title": "Superior ethanol sensing performance by in-situ construction of porous Zn2SnO4/CdSnO3 nanocubes n-n heterostructure", "Abstract": "Metal oxides with perovskite crystal structure have draw much attentions due to their designable component and suitable energy band structure, which are regard as promising gas-sensing materials. Herein, porous Zn<sub>2</sub>SnO<sub>4</sub>/CdSnO<sub>3</sub> nanocubes based heterostructure consisted of Zn<sub>2</sub>SnO<sub>4</sub> and CdSnO<sub>3</sub> nanoparticles were prepared by a facile co-precipitation method followed by an annealed process, in which Zn<sub>2</sub>SnO<sub>4</sub> was in-situ generated during the Zn doping into CdSnO<sub>3</sub>. It is showed that the Zn<sub>2</sub>SnO<sub>4</sub>/CdSnO<sub>3</sub> heterostructure displayed superior ethanol gas sensing performance including high response (214.38 toward 100 ppm ethanol), low detect limit (285 ppb), good selectivity, and excellent longterm working stability. The superior ethanol sensing properties of Zn<sub>2</sub>SnO<sub>4</sub>/CdSnO<sub>3</sub> are attributed to the high specific surface area, n-n heterojunctions, and abundant oxygen vacancies, which promotes surface oxygen adsorption/ionization process and carrier migration. This paper provides a novel strategy for enhanced gas sensing performance by in-situ generated heterostructure using perovskite as matrix.", "Keywords": "Perovskite; CdSnO<sub>3</sub> nano-cubes; Ethanol; Gas sensor; Heterostructure", "DOI": "10.1016/j.snb.2024.136397", "PubYear": 2024, "Volume": "419", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Materials Science and Engineering, Shandong University of Science and Technology, Qingdao, Shandong 266590, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Materials Science and Engineering, Shandong University of Science and Technology, Qingdao, Shandong 266590, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Civil Engineering and Architecture, Shandong University of Science and Technology, Qingdao, Shandong 266590, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Materials Science and Engineering, Shandong University of Science and Technology, Qingdao, Shandong 266590, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technology Center of Qingdao Customs, Qingdao 266114, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Materials Science and Engineering, Shandong University of Science and Technology, Qingdao, Shandong 266590, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Materials Science and Engineering, Shandong University of Science and Technology, Qingdao, Shandong 266590, China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of Materials Science and Engineering, Shandong University of Science and Technology, Qingdao, Shandong 266590, China;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "College of Materials Science and Engineering, Shandong University of Science and Technology, Qingdao, Shandong 266590, China;Corresponding authors"}], "References": [{"Title": "VOC gas sensor based on hollow cubic assembled nanocrystal Zn2SnO4 for breath analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "302", "Issue": "", "Page": "111834", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "High sensitivity and low detection limit of acetone sensor based on NiO/Zn2SnO4 p-n heterojunction octahedrons", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "339", "Issue": "", "Page": "129912", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Core-double shell ZnO@In2O3@ZnO hollow microspheres for superior ethanol gas sensing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "341", "Issue": "", "Page": "130002", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A comparative study on the VOCs gas sensing properties of Zn2SnO4 nanoparticles, hollow cubes, and hollow octahedra towards exhaled breath analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "343", "Issue": "", "Page": "130147", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Synthesis of the ZnFe2O4/ZnSnO3 nanocomposite and enhanced gas sensing performance to acetone", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "346", "Issue": "", "Page": "130524", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Porous CdSnO3 nanocubics synthesized under suitable pH value for targeted H2S detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "336", "Issue": "", "Page": "113408", "JournalTitle": "Sensors and Actuators A: Physical"}, {"Title": "Decoration of laser-ablated ZnO nanoparticles over sputtered deposited SnO2 thin film based formaldehyde sensor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "367", "Issue": "", "Page": "132114", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A mixed-potential type NH3 sensors based on spinel Zn2SnO4 sensing electrode", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "367", "Issue": "", "Page": "132154", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Carrier and oxygen vacancy engineering of aliovalent ion modified BiFeO3 and their gas sensing properties", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "370", "Issue": "", "Page": "132400", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Controllable construction of multi-shelled ZnSnO3 hollow microspheres as high-performance sensing material for acetone detection", "Authors": "<PERSON>yong Du; Jing Pan; Qing Dong", "PubYear": 2022, "Volume": "372", "Issue": "", "Page": "132605", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Highly sensitive NO2 gas sensor based on ZnO nanoarray modulated by oxygen vacancy with Ce doping", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "379", "Issue": "", "Page": "133294", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A highly efficient acetone gas sensor based on 2D porous ZnFe2O4 nanosheets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "379", "Issue": "", "Page": "133287", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Synthesis of MoO3 (1D) @SnO2 (2D) core-shell heterostructures for enhanced ethanol gas sensing performance", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "382", "Issue": "", "Page": "133484", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Conductometric ethanol gas sensor based on a bilayer film consisting of SnO2 film and SnO2/ZnSnO3 porous film prepared by magnetron sputtering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "382", "Issue": "", "Page": "133562", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Defect engineering of nanostructured ZnSnO3 for conductometric room temperature CO2 sensors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "384", "Issue": "", "Page": "133628", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Defect engineering of nanostructured ZnSnO3 for conductometric room temperature CO2 sensors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "384", "Issue": "", "Page": "133628", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Influence of La doping on the ethanol gas sensing properties of CdSnO3 micro-cubes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "394", "Issue": "", "Page": "134447", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Influence of La doping on the ethanol gas sensing properties of CdSnO3 micro-cubes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "394", "Issue": "", "Page": "134447", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Modulated crystalline ZnO/amorphous ZnSnO3 heterogeneous via In doping for improved butanone sensing performance with enhanced the basic sites", "Authors": "Hongmin Zhu; Hanyang Ji; Lu Kong", "PubYear": 2023, "Volume": "394", "Issue": "", "Page": "134473", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "ZnO/GaN n-n heterojunction porous nanosheets for ppb-level NO2 gas sensors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "396", "Issue": "", "Page": "134629", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Triethylamine gas sensor based on Zn2SnO4 polyhedron decorated with Au nanoparticles and density functional theory investigation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; Junkai <PERSON>; Guofeng Pan", "PubYear": 2024, "Volume": "408", "Issue": "", "Page": "135510", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Improvement of isopropanol sensing performance by surface sensitization of CdSnO3 nanocubes with CdS quantum dots", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "409", "Issue": "", "Page": "135640", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 116815258, "Title": "1E1-5　ゲームメカニクスを用いたボードゲームの効果の分析", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1E1-5", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Haruto INOUE", "Affiliation": "Shibaura Institute of Technology"}, {"AuthorId": 2, "Name": "R<PERSON>ji <PERSON>", "Affiliation": "Shibaura Institute of Technology"}], "References": []}, {"ArticleId": 116815280, "Title": "Multi-agent secure payment model of e-commerce based on blockchain perspective", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICS.2024.140221", "PubYear": 2024, "Volume": "24", "Issue": "1/2", "JournalId": 22256, "JournalTitle": "International Journal of Information and Computer Security", "ISSN": "1744-1765", "EISSN": "1744-1773", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116815286, "Title": "2E2-6　良い作業システムを設計するための人間工学JIS規格の改正について: －JIS Z 8501:2024 とJIS Z 8502-1:2024－", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2E2-6", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kazuo AOKI", "Affiliation": "Nihon University"}, {"AuthorId": 2, "Name": "Motoharu YOK<PERSON>", "Affiliation": "Certified Professional Ergonomist"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Tokai University, Institute of Sports Medical Science"}], "References": []}, {"ArticleId": 116815295, "Title": "Heterogeneous domain adaptation via incremental discriminative knowledge consistency", "Abstract": "Heterogeneous domain adaptation is a challenging problem in transfer learning since samples from the source and target domains reside in different feature spaces with different feature dimensions. The key problem is how to minimize some gaps (e.g., data distribution mismatch) presented in two heterogeneous domains and produce highly discriminative representations for the target domain. In this paper, we attempt to address these challenges with the proposed incremental discriminative knowledge consistency (IDKC) method, which integrates cross-domain mapping, distribution matching, discriminative knowledge preservation, and domain-specific geometry structure consistency into a unified learning model. Specifically, we attempt to learn a domain-specific projection to project original samples into a common subspace in which the marginal distribution is well aligned and the discriminative knowledge consistency is preserved by leveraging the labeled samples from both domains. Moreover, domain-specific structure consistency is enforced to preserve the data manifold from the original space to the common feature space in each domain. Meanwhile, we further apply pseudo labeling to unlabeled target samples based on the feature correlation and retain pseudo labels with high feature correlation coefficients for the next iterative learning. Our pseudo-labeling strategy expands the number of labeled target samples in each category and thus enforces class-discriminative knowledge consistency to produce more discriminative feature representations for the target domain. Extensive experiments on several standard benchmarks for object recognition, cross-language text classification, and digit classification tasks verify the effectiveness of our method.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.110857", "PubYear": 2024, "Volume": "156", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, South China Normal University, Foshan 528225, PR China;College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, PR China;Guangdong Provincial Key Laboratory of Intelligent Information Processing, Shenzhen University, Shenzhen 518060, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, PR China;Guangdong Provincial Key Laboratory of Intelligent Information Processing, Shenzhen University, Shenzhen 518060, PR China;Corresponding author at: College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, PR China;Guangdong Provincial Key Laboratory of Intelligent Information Processing, Shenzhen University, Shenzhen 518060, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>ng Li", "Affiliation": "School of Artificial Intelligence, Optics and Electronics (iOPEN), Northwestern Polytechnical University, Xian 710072, PR China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, PR China;Guangdong Provincial Key Laboratory of Intelligent Information Processing, Shenzhen University, Shenzhen 518060, PR China"}], "References": [{"Title": "Discriminative distribution alignment: A unified framework for heterogeneous domain adaptation", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "107165", "JournalTitle": "Pattern Recognition"}, {"Title": "Graph regularized locally linear embedding for unsupervised feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108299", "JournalTitle": "Pattern Recognition"}, {"Title": "Cross-domain structure preserving projection for heterogeneous domain adaptation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108362", "JournalTitle": "Pattern Recognition"}, {"Title": "Improving pseudo labels with intra-class similarity for unsupervised domain adaptation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "109379", "JournalTitle": "Pattern Recognition"}, {"Title": "Contrasting augmented features for domain adaptation with limited target domain data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110145", "JournalTitle": "Pattern Recognition"}, {"Title": "Information filtering and interpolating for semi-supervised graph domain adaptation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "153", "Issue": "", "Page": "110498", "JournalTitle": "Pattern Recognition"}, {"Title": "Bridging the gap: Active learning for efficient domain adaptation in object detection", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "254", "Issue": "", "Page": "124403", "JournalTitle": "Expert Systems with Applications"}, {"Title": "AMMD: Attentive maximum mean discrepancy for few-shot image classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "155", "Issue": "", "Page": "110680", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 116815394, "Title": "2E4-1　少数ウェアラブル慣性センサを用いた立脚期足関節モーメントのAI推定法の提案", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2E4-1", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Kyoko SHIBATA", "Affiliation": "Kochi University of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>i WATANABE", "Affiliation": "Kochi University of Technology"}], "References": []}, {"ArticleId": 116815410, "Title": "2G3-3　自動車整備工場における疲労の一例 ： パワースーツ活用の実態", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2G3-3", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Product Planning Advisor; Chiropractor"}], "References": []}, {"ArticleId": 116815484, "Title": "Design and Analysis of Load Frequency Control for a Two-Area Power System Using Conventional PID and FPID Controllers", "Abstract": "<p>Power system stability is required to maintain a continuous balance between power generation and load demand. Frequency control is also a major function of automatic generation control and one of the most important control problems in power system design and operation. So, a robust control system must be implemented for controlling the actual power and frequency. This paper mainly focuses on the design of a two-area power system with a rated frequency of 50 Hz, which is used in Myanmar, using the fuzzy-PID method to control the load frequency. It also aims to imply from one area to another when demand suddenly increases or decreases. It involves applying mathematical formulas and models to analyze how an automatic generation control works. The plan is first implemented in a single area before being modified to a two-area power system with and without controllers. The output results are simulated with MATLAB/ SIMULINK.</p>", "Keywords": "", "DOI": "10.33022/ijcs.v13i3.4066", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mandalay Technological University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mandalay Technological University"}, {"AuthorId": 3, "Name": "Tin Tin Hla", "Affiliation": "Mandalay Technological University"}], "References": []}, {"ArticleId": 116815490, "Title": "2C4-1　脳波リアルタイム解析アプリの開発と、開発アプリを用いた製品画像の印象評価の検討", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2C4-1", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Ryo MIZUNO", "Affiliation": "TOKAI OPTICAL CO., LTD"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> KOZAKI", "Affiliation": "TOKAI OPTICAL CO., LTD"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>a SUZUKI", "Affiliation": "TOKAI OPTICAL CO., LTD"}], "References": []}, {"ArticleId": 116815661, "Title": "2C3-4　持ち上げ動作におけるアシストスーツDARWING Hakobeludeの補助効果検証", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2C3-4", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Daiya Industry Co., Ltd."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Daiya Industry Co., Ltd."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Daiya Industry Co., Ltd."}], "References": []}, {"ArticleId": 116815686, "Title": "Mesh Parameterization Meets Intrinsic Triangulations", "Abstract": "<p>A parameterization of a triangle mesh is a realization in the plane so that all triangles have positive signed area. Triangle mesh parameterizations are commonly computed by minimizing a distortion energy, measuring the distortions of the triangles as they are mapped into the parameter domain. It is assumed that the triangulation is fixed and the triangles are mapped affinely. We consider a more general setup and additionally optimize among the intrinsic triangulations of the piecewise linear input geometry. This means the distortion energy is computed for the same geometry, yet the space of possible parameterizations is enlarged. For minimizing the distortion energy, we suggest alternating between varying the parameter locations of the vertices and intrinsic flipping. We show that this process improves the mapping for different distortion energies at moderate additional cost. We also find intrinsic triangulations that are better starting points for the optimization of positions, offering a compromise between the full optimization approach and exploiting the additional freedom of intrinsic triangulations.</p>", "Keywords": "CCS Concepts;• Computing methodologies → Computer graphics;Mesh models;Mesh geometry models", "DOI": "10.1111/cgf.15134", "PubYear": 2024, "Volume": "43", "Issue": "5", "JournalId": 6134, "JournalTitle": "Computer Graphics Forum", "ISSN": "0167-7055", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "TU Berlin;ETH Zürich"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "TU Berlin"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETH Zürich"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "TU Berlin"}], "References": [{"Title": "Efficient piecewise higher-order parametrization of discrete surfaces with local and global injectivity", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "127", "Issue": "", "Page": "102862", "JournalTitle": "Computer-Aided Design"}, {"Title": "Lifting simplices to find injectivity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "You can find geodesic paths in triangle meshes by just flipping edges", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "WRAPD", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Foldover-free maps in 50 lines of code", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Integer coordinates for intrinsic geometry processing", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "ARAP Revisited Discretizing the Elastic Energy using Intrinsic Voronoi Cells", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "6", "Page": "e14790", "JournalTitle": "Computer Graphics Forum"}, {"Title": "Efficient Embeddings in Exact Arithmetic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 116815784, "Title": "SOFTWARE ARCHITECTURE SYSTEM DESIGN FOR THE MASS SERVICE SYSTEMS MODELING TO BE IMPLEMENTED IN GO PROGRAMMING LANGUAGE", "Abstract": "<p>The subject of the article is the methods and approaches to organizing the architecture of software implementation designed for modeling the behavior of mass service systems. The goal of the work is to design a software architecture for implementation in Go language, intended to replicate the behavior of various types of mass service systems, without considering the failure of individual service channels, using parallel computing. The article addresses the following tasks: consider the basis for designing the architecture and conclude its appropriateness; develop requirements for the future software product for more effective resource use and clear definition of successful completion; analyze the approaches to organizing software architecture and make a justified decision on the application of one of them; design a general algorithm scheme taking into account all requirements; identify the components of the modeled system and their interactions; build process diagrams considering the specifics of the Go programming language; define the method and contracts of interaction with the software. The research will utilize the following methods: Go programming language, concurrency, architectural UML diagrams, C4 diagrams, process diagrams. The following results were obtained: the requirements for the software for modeling mass service operations (SMO) were defined; common approaches to organizing architecture were considered and a comparative analysis was conducted; the structure of the future program was developed at the necessary levels of abstraction; for the first time, an architecture of the software product for modeling various mass service systems using parallel computing and the concurrency approach under the implementation in the Go programming language was proposed.</p>", "Keywords": "software architecture;computer modeling;mass service system;Go programming language;concurrency;parallelism", "DOI": "10.20998/2079-0023.2024.01.14", "PubYear": 2024, "Volume": "", "Issue": "1 (11)", "JournalId": 60745, "JournalTitle": "Bulletin of National Technical University \"KhPI\". Series: System Analysis, Control and Information Technologies", "ISSN": "2079-0023", "EISSN": "2410-2857", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Харківський національний університет радіоелектроніки"}], "References": []}, {"ArticleId": 116815887, "Title": "A dataset of Citizen Science practitioners’ experiences and practices", "Abstract": "<p>There has been renewed interest in Citizen Science (CS) in recent years as it offers an intriguing vision of enabling a scientifically literate population engage in scientific investigations and policy formation. Nonetheless, citizen scientists remain an understudied population, possibly due to the voluntary and part-time nature of their endeavours. Here, a dataset of CS practitioners' experiences collected using an online survey is presented. The survey sample comprises 100 adults (18+) active in diverse CS projects. The survey contains 47 questions designed for quantitative analysis. Questions cluster around several broad themes - participant demographics, project profiles, experience in citizen science, data collection practices, management, dissemination, knowledge of open research principles, and training received. The dataset offers the potential for further empirical research or as a baseline for subsequent surveys, and will interest anybody planning a CS initiative. The questionnaire constitutes a ready-to-deploy instrument for additional country, region, or initiative-level surveys.</p><p>© 2024 The Author(s).</p>", "Keywords": "Citizen science;Data management;Open science;Participatory science", "DOI": "10.1016/j.dib.2024.110779", "PubYear": 2024, "Volume": "56", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science, University College Dublin, Belfield, Dublin 4, Ireland."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University College Dublin, Belfield, Dublin 4, Ireland."}], "References": []}, {"ArticleId": 116815929, "Title": "1C3-4　指定討論", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1C3-4", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "晋司 三宅", "Affiliation": "公立千歳科学技術大学"}], "References": []}, {"ArticleId": 116815983, "Title": "1E3-6　VRを用いた安全教育の効果に関する研究", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1E3-6", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Ayaka SHIRAISHI", "Affiliation": "UBE Corporation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>ro SHO<PERSON>", "Affiliation": "School of Heath Sciences, University of Occupational and Environmental Health"}], "References": []}, {"ArticleId": 116816012, "Title": "Monitoring and Governance of Illegal Urban Construction", "Abstract": "", "Keywords": "", "DOI": "10.18494/SAM5171", "PubYear": 2024, "Volume": "36", "Issue": "7", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116816040, "Title": "1C1-6　人に優しく安全なラバーアクチュエータの活用検討", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1C1-6", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Atsushi KITAHARA", "Affiliation": "Bridgestone Corporation"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>ro MIYAZAK<PERSON>", "Affiliation": "The University of Tokyo"}, {"AuthorId": 3, "Name": "Kenji KAWASHIMA", "Affiliation": "The University of Tokyo"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Bridgestone Corporation"}], "References": []}, {"ArticleId": 116816064, "Title": "Integration of Machine Learning with Fog Computing for Health Care Systems Challenges and Issues: A Review", "Abstract": "<p>Fog computing, a distributed cloud computing model, extends the traditional cloud paradigm to the network's edge, reducing latency and alleviating congestion. It addresses challenges in classical cloud architectures exacerbated by real-time IoT applications, which produce massive amounts of data that traditional cloud computing struggles to process due to limited bandwidth and high propagation delays. Fog computing is crucial in latency-sensitive applications like health monitoring and surveillance, where it processes vast volumes of data, minimizing delays and boosting performance. This technology brings computation, storage, monitoring, and services closer to the end-user, enhancing real-time decision-making capabilities. This paper presents the challenges of IoT applications. It also demonstrates the role of two emerging technologies fog computing and machine learning in health care scenarios.</p>", "Keywords": "Machine Learning;Fog Computing;Cloud;Healthcare", "DOI": "10.33022/ijcs.v13i3.3986", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Duhok"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116816077, "Title": "Leukemia Detection and Classification Based on Machine Learning and CNN: A Review", "Abstract": "<p>Advancements in data mining methods have significantly improved disease diagnosis, particularly in the realm of leukemia detection. Leukemia, a complex cancer affecting white blood cells, poses significant challenges in diagnosis and management due to its diverse manifestations. Various machine learning algorithms, including Convolutional Neural Networks (CNNs), Support Vector Machines (SVMs), Random Forests (RF), Decision Trees (DTs), K-Nearest Neighbors (K-NN), Logistic regression (LR) and Naïve Bayes (NB) classifiers, have been employed to accurately classify leukemia cases based on diverse datasets and image analyses. This paper provides a comprehensive overview and comparison of these classification techniques, highlighting their effectiveness in diagnosing different leukemia subtypes. Additionally, the paper discusses the methodology and findings of several studies focusing on leukemia detection, emphasizing the significance of machine learning in enhancing diagnostic accuracy and treatment planning. Furthermore, it explores the challenges and future directions in leveraging machine learning for leukemia diagnosis, including the need for standardized datasets, algorithm refinement, and integration with clinical data for personalized treatment strategies.</p>", "Keywords": "", "DOI": "10.33022/ijcs.v13i3.4044", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Akre University for Applied Sciences/ Technical College of Informatics -Akre/ Department of Information Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Duhok Polytechnic University, Duhok, Kurdistan Region, Iraq"}], "References": []}, {"ArticleId": 116816187, "Title": "1C4　デジタルヘルステクノロジの普及とヘルスケアの未来: －日本人間工学会が参画するエビデンス整備発信事業のコロキウム－", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1C4", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The UOEH"}, {"AuthorId": 2, "Name": "Hisashi EGUCHI", "Affiliation": "The UOEH"}, {"AuthorId": 3, "Name": "Satoru <PERSON>", "Affiliation": "Teikyo University"}, {"AuthorId": 4, "Name": "Naomichi TANI", "Affiliation": "The UOEH"}, {"AuthorId": 5, "Name": "Mieko OHSUGA", "Affiliation": "Osaka Institute of Technology"}], "References": []}, {"ArticleId": 116816306, "Title": "Unsupervised discriminative projection based on contrastive learning", "Abstract": "Feature extraction can effectively alleviate the curse of dimensionality on high-dimensional data. Contrastive learning, as a self-supervised learning method, has garnered widespread attention in recent years. In this study, we propose a new unsupervised feature extraction model based on contrastive learning. We integrate the principle of contrastive learning into the feature extraction model in a novel way without data augmentation or negative samples, defining positive samples more accurately and flexibly. Additionally, we propose new structure preserving terms based on the selected positive samples to preserve the distribution structure of specific categories. An iterative algorithm is designed to solve the proposed model. Numerical experiments on four real datasets demonstrate that our method outperforms existing methods, highlighting its potential in handling high-dimensional data and extracting discriminative features.", "Keywords": "Feature extraction; Unsupervised learning; Contrastive learning; Dimensionality reduction; Discriminative information", "DOI": "10.1016/j.knosys.2024.112296", "PubYear": 2024, "Volume": "301", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science, China Agricultural University, Beijng, 100083, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information and Electrical Engineering, China Agricultural University, Beijng, 100083, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Innovation Center for Digital Fishery, China Agricultural University, Beijng, 100083, PR China;Key Laboratory of Smart Farming Technologies for Aquatic Animal and Livestock, Ministry of Agriculture and Rural Affairs, Beijng, 100083, PR China;College of Information and Electrical Engineering, China Agricultural University, Beijng, 100083, PR China"}, {"AuthorId": 4, "Name": "Zhuang<PERSON><PERSON><PERSON>o", "Affiliation": "National Innovation Center for Digital Fishery, China Agricultural University, Beijng, 100083, PR China;Beijing Engineering and Technology Research Center for Internet of Things in Agriculture, Beijng, 100083, PR China;College of Information and Electrical Engineering, China Agricultural University, Beijng, 100083, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Science, China Agricultural University, Beijng, 100083, PR China;National Innovation Center for Digital Fishery, China Agricultural University, Beijng, 100083, PR China;Key Laboratory of Smart Farming Technologies for Aquatic Animal and Livestock, Ministry of Agriculture and Rural Affairs, Beijng, 100083, PR China;Beijing Engineering and Technology Research Center for Internet of Things in Agriculture, Beijng, 100083, PR China;Corresponding author"}], "References": [{"Title": "Discriminative globality and locality preserving graph embedding for dimensionality reduction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "113079", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Locality adaptive preserving projections for linear dimensionality reduction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "113352", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Self-adaptive manifold discriminant analysis for feature extraction from hyperspectral imagery", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107487", "JournalTitle": "Pattern Recognition"}, {"Title": "Low-rank adaptive graph embedding for unsupervised feature extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107758", "JournalTitle": "Pattern Recognition"}, {"Title": "Semi-supervised feature selection via adaptive structure learning and constrained graph learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109243", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Locality preserving projection with symmetric graph embedding for unsupervised dimensionality reduction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108844", "JournalTitle": "Pattern Recognition"}, {"Title": "Unsupervised dimensionality reduction by jointing dynamic hypergraph and low-rank embedding for classification and clustering", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "208", "Issue": "", "Page": "118225", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Robust discriminative projection with dynamic graph regularization for feature extraction and classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "253", "Issue": "", "Page": "109563", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Robust unsupervised feature selection via sparse and minimum-redundant subspace learning with dual regularization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "511", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-label disaster text classification via supervised contrastive learning for social media data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "", "Page": "108401", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Unified feature extraction framework based on contrastive learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "258", "Issue": "", "Page": "110028", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A new contrastive learning framework for reducing the effect of hard negatives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "260", "Issue": "", "Page": "110121", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Strongly augmented contrastive clustering", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "109470", "JournalTitle": "Pattern Recognition"}, {"Title": "Fast and Robust Unsupervised Dimensionality Reduction with Adaptive Bipartite Graphs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "276", "Issue": "", "Page": "110680", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Discriminative multi-label feature selection with adaptive graph diffusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110154", "JournalTitle": "Pattern Recognition"}, {"Title": "Unsupervised feature selection with high-order similarity learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "285", "Issue": "", "Page": "111317", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-label feature selection via latent representation learning and dynamic graph constraints", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "151", "Issue": "", "Page": "110411", "JournalTitle": "Pattern Recognition"}, {"Title": "Discriminative label correlation based robust structure learning for multi-label feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "154", "Issue": "", "Page": "110583", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 116816328, "Title": "Experimental Study on the Longitudinal Motion Performance of a Spherical Robot Rolling on Sandy Terrain", "Abstract": "<p>To provide the necessary theoretical models of sphere–soil interaction for the structural design, motion control, and simulation of spherical robots, this paper derives analytical expressions for traction force and driving torque when spherical robots slide and sink into sandy terrain, based on terramechanics and multibody dynamics. Furthermore, orthogonal experimental analysis identifies the load, joint angular acceleration, and maximum joint angular velocity of spherical robots as influencing factors, highlighting that the load significantly affects their longitudinal motion performance. Experimental results indicate that rolling friction and additional resistance on sandy terrain cannot be ignored. The corrected theoretical model effectively replicates the temporal variation of driving torque exerted by spherical robots on sandy terrain. Numerical computations and experimental analyses demonstrate that increasing the radius of the sphere shell, the load, and the slip ratio all lead to increased traction force and driving torque. However, traction force and driving torque begin to decrease once the slip ratio reaches approximately 0.5. Therefore, in the design of spherical robot structures and control laws, appropriate parameters such as load and slip ratio should be chosen based on the established sphere–soil interaction theoretical model to achieve high-quality longitudinal motion performance on sandy terrain.</p>", "Keywords": "", "DOI": "10.3390/act13080289", "PubYear": 2024, "Volume": "13", "Issue": "8", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligent Engineering and Automation, Beijing University of Posts and Telecommunications, Beijing 100876, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligent Engineering and Automation, Beijing University of Posts and Telecommunications, Beijing 100876, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "China Coal Research Institute, Beijing 100013, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligent Engineering and Automation, Beijing University of Posts and Telecommunications, Beijing 100876, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Intelligent Engineering and Automation, Beijing University of Posts and Telecommunications, Beijing 100876, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Intelligent Engineering and Automation, Beijing University of Posts and Telecommunications, Beijing 100876, China"}], "References": [{"Title": "Special spherical mobile robot for planetary surface exploration: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "20", "Issue": "2", "Page": "172988062311622", "JournalTitle": "International Journal of Advanced Robotic Systems"}]}, {"ArticleId": 116816367, "Title": "Upscaling the production of sterile male mosquitoes with an automated pupa sex sorter", "Abstract": "Editor’s summary <p> Several techniques have been developed to fight mosquito-borne diseases, including the release of sterile or incompatible male mosquitoes into the wild to reduce population growth. However, sorting mosquitoes by sex is a laborious process. <PERSON> et al. have now developed an automated mosquito pupa sex sorter, which was tested on three mosquito species and shown to increase the production of males 17-fold when compared with a manual separation process. A field trial in Guangzhou, China showed that the process was capable of producing sufficient incompatible males without reducing their quality, which led to the suppression of wild populations of mosquitoes. —<PERSON> </p>", "Keywords": "", "DOI": "10.1126/scirobotics.adj6261", "PubYear": 2024, "Volume": "9", "Issue": "92", "JournalId": 15892, "JournalTitle": "Science Robotics", "ISSN": "", "EISSN": "2470-9476", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Insect Pest Control Sub-programme, Joint FAO/IAEA Centre of Nuclear Techniques in Food and Agriculture, Vienna, Austria.;Institut de Recherche Agricole pour le Développement (IRAD), Yaoundé, Cameroun."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pathogen Biology, School of Medicine, Jinan University, Guangzhou, China."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 7, "Name": "Qixian <PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China."}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Insect Pest Control Sub-programme, Joint FAO/IAEA Centre of Nuclear Techniques in Food and Agriculture, Vienna, Austria.;Institut de Recherche en Sciences de la Santé, Direction Régionale de l’Ouest (IRSS-DRO), Bobo-Dioulasso, Burkina Faso."}, {"AuthorId": 12, "Name": "Nan<PERSON><PERSON><PERSON> S<PERSON><PERSON>", "Affiliation": "Insect Pest Control Sub-programme, Joint FAO/IAEA Centre of Nuclear Techniques in Food and Agriculture, Vienna, Austria.;Unité de Formation et de Recherche en Science et Technologie (UFR/ST), Université Norbert ZONGO (UNZ), Koudougou, Burkina Faso."}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": "Insect Pest Control Sub-programme, Joint FAO/IAEA Centre of Nuclear Techniques in Food and Agriculture, Vienna, Austria."}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Insect Pest Control Sub-programme, Joint FAO/IAEA Centre of Nuclear Techniques in Food and Agriculture, Vienna, Austria."}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "Insect Pest Control Sub-programme, Joint FAO/IAEA Centre of Nuclear Techniques in Food and Agriculture, Vienna, Austria."}, {"AuthorId": 16, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Insect Pest Control Sub-programme, Joint FAO/IAEA Centre of Nuclear Techniques in Food and Agriculture, Vienna, Austria.;ASTRE, CIRAD, Montpellier, France.;ASTRE, CIRAD, INRAE, University of Montpellier, Plateforme Technologique CYROI, Sainte-Clotilde, La Réunion, France."}, {"AuthorId": 17, "Name": "<PERSON><PERSON><PERSON>g Xi", "Affiliation": "Guangzhou Wolbaki Biotech Co. Ltd., Guangzhou, China.;Department of Microbiology, Genetics, and Immunology, Michigan State University, East Lansing, MI, USA."}], "References": [{"Title": "Field performance of sterile male mosquitoes released from an uncrewed aerial vehicle", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "43", "Page": "eaba6251", "JournalTitle": "Science Robotics"}]}, {"ArticleId": 116816412, "Title": "2F3-6　創造的な会議に向けたスキル抽出のためのCritical Decision Methodの開発", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.2F3-6", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "To<PERSON>ki <PERSON>", "Affiliation": "The University of Tokyo, School of Engineering"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Tokyo, School of Interdisciplinary Information Studies"}, {"AuthorId": 3, "Name": "Taro K<PERSON>", "Affiliation": "The University of Tokyo, School of Engineering"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> HACHISU<PERSON>", "Affiliation": "The University of Tokyo, Interfaculty Initiative in Information Studies"}, {"AuthorId": 5, "Name": "Yuta YOSHINO", "Affiliation": "Ricoh Co., Ltd."}, {"AuthorId": 6, "Name": "Shuhei WATANABE", "Affiliation": "Ricoh Co., Ltd."}], "References": []}, {"ArticleId": 116816438, "Title": "A comprehensive transformation-thermomechanical model on deformation history in directed energy deposition of high-speed steel", "Abstract": "", "Keywords": "", "DOI": "10.1080/17452759.2024.2382164", "PubYear": 2024, "Volume": "19", "Issue": "1", "JournalId": 7390, "JournalTitle": "Virtual and Physical Prototyping", "ISSN": "1745-2759", "EISSN": "1745-2767", "Authors": [{"AuthorId": 1, "Name": "Ke Ren", "Affiliation": "Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, People’s Republic of China;Beijing Key Lab of Precision/Ultra-precision Manufacturing Equipments and Control, Tsinghua University, Beijing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Beijing Key Lab of Precision/Ultra-precision Manufacturing Equipments and Control, Tsinghua University, Beijing, People’s Republic of China"}, {"AuthorId": 3, "Name": "Yuelan Di", "Affiliation": "National Key Laboratory for Remanufacturing, Army Academy of Armored Forces, Beijing, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, People’s Republic of China;Beijing Key Lab of Precision/Ultra-precision Manufacturing Equipments and Control, Tsinghua University, Beijing, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Lab of Precision/Ultra-precision Manufacturing Equipments and Control, Tsinghua University, Beijing, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, People’s Republic of China"}], "References": [{"Title": "Towards a comprehensive understanding of distortion in additive manufacturing based on assumption of constraining force", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "sup1", "Page": "S85", "JournalTitle": "Virtual and Physical Prototyping"}, {"Title": "High-density direct laser deposition (DLD) of CM247LC alloy: microstructure, porosity and cracks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "11-12", "Page": "8063", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 116816441, "Title": "<PERSON><PERSON><PERSON>n Layanan Hotel Menggunakan Algoritma Extra Trees: <PERSON><PERSON> pada <PERSON> Pelanggan", "Abstract": "<p>Pen<PERSON>tian ini bertujuan untuk menganalisis sentimen layanan hotel berdasarkan ulasan pelanggan menggunakan algoritma Extra Trees. Metode ini diujicobakan pada dataset yang berisi ulasan-ulasan pelanggan tentang layanan hotel. <PERSON>luasi dilakukan dengan memperhitungkan akurasi, presisi, recall, dan skor <PERSON> dari model yang dikembangkan. Hasil penelitian menunjukkan bahwa algoritma Extra Trees mampu mencapai akurasi sebesar 85.05%, dengan presisi sebesar 84.46%, recall sebesar 97.00%, dan skor F1 sebesar 90.17%. Temuan ini mengindikasikan bahwa algoritma Extra Trees memiliki kinerja yang baik dalam menganalisis sentimen layanan hotel berdasarkan ulasan pelanggan. Implikasi dari penelitian ini adalah memberikan panduan kepada hotel untuk memahami dan meningkatkan kualitas layanan mereka berdasarkan umpan balik dari pelanggan. <PERSON><PERSON> itu, penelitian ini juga dapat menjadi dasar untuk pengembangan lebih lanjut dalam bidang analisis sentimen dan pelayanan pelanggan di industri pariwisata.</p>", "Keywords": "", "DOI": "10.33022/ijcs.v13i3.4014", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "STMIK Amik <PERSON>"}, {"AuthorId": 2, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "STMIK Amik <PERSON>"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "STMIK Amik <PERSON>"}, {"AuthorId": 4, "Name": " <PERSON><PERSON>", "Affiliation": "STMIK Amik <PERSON>"}], "References": []}, {"ArticleId": 116816452, "Title": "1B4-3　ヒューマンシステムインタラクション関連の国際標準: －IS0/TC159 SC4 WG5における標準化活動の概要－", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1B4-3", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mitsubishi Electric Corporation"}], "References": []}, {"ArticleId": 116816581, "Title": "1F4-4　基底面からのベクトルの異なる身体部位によるライトタッチが静止立位姿勢に与える影響", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1F4-4", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "Dongyoung GOH", "Affiliation": "Waseda University"}, {"AuthorId": 2, "Name": "Naota YANO", "Affiliation": "Waseda University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 4, "Name": "Mio HATA", "Affiliation": "Waseda University"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Waseda University"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Waseda University"}], "References": []}, {"ArticleId": 116816600, "Title": "Prostate Cancer: MRI Image Detection Based on Deep Learning: A Review", "Abstract": "<p>This comprehensive study delves into the transformative role of artificial intelligence (AI) and deep learning (DL) in the realm of prostate cancer care, an issue of paramount importance in men’s health worldwide. Prostate cancer, marked by the unchecked growth of cells in the prostate gland, poses risks of tumor formation and eventual metastasis. The crux of combating this disease lies in its early detection and precise diagnosis, for which traditional screening methodologies like Prostate-Specific Antigen (PSA) tests and multiparametric Magnetic Resonance Imaging (mp-MRI) are fundamental. The introduction of AI and DL into these diagnostic avenues has been nothing short of revolutionary, enhancing the precision of medical imaging and significantly reducing the rates of unnecessary biopsies. The advancements in DL, particularly through the use of convolutional neural networks (CNNs) and the application of multiparametric MRI, have been instrumental in improving the accuracy of diagnoses, foreseeing the progression of the disease, and tailoring individualized treatment regimens. This paper meticulously examines various DL models and their successful application in the detection, classification, and segmentation of prostate cancer, establishing their superiority over conventional diagnostic techniques. Despite the promising horizon these technologies offer, their implementation is not without challenges. The requisite for specialized expertise to handle these advanced tools and the ethical dilemmas they present, such as data privacy and potential biases, are significant hurdles. Nevertheless, the potential of AI and DL to inaugurate a new chapter in prostate cancer management is undeniable. The emphasis on interdisciplinary collaboration among scientists, clinicians, and technologists is crucial for pushing the boundaries of current research and clinical practice, ensuring the ethical deployment of AI and DL technologies. This collaborative effort is vital for realizing the full potential of these innovations in providing more accurate, efficient, and patient-centric care in the fight against prostate cancer, heralding a future where the burden of this disease is significantly mitigated.</p>", "Keywords": "", "DOI": "10.33022/ijcs.v13i3.4045", "PubYear": 2024, "Volume": "13", "Issue": "3", "JournalId": 61928, "JournalTitle": "Indonesian Journal of Computer Science", "ISSN": "2302-4364", "EISSN": "2549-7286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Akre University for Applied Sciences/ Technical College of Informatics -Akre/ Department of Information Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Duhok Polytechnic University, Duhok, Kurdistan Region, Iraq"}], "References": []}, {"ArticleId": 116816619, "Title": "Remote-sensing-based Approach to Analysis of Thermal Environment for Island-type City", "Abstract": "", "Keywords": "", "DOI": "10.18494/SAM5024", "PubYear": 2024, "Volume": "36", "Issue": "7", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Taohua Ren", "Affiliation": ""}], "References": []}, {"ArticleId": 116816651, "Title": "Bidirectional brain image translation using transfer learning from generic pre-trained models", "Abstract": "Brain imaging plays a crucial role in the diagnosis and treatment of various neurological disorders, providing valuable insights into the structure and function of the brain. Techniques such as magnetic resonance imaging (MRI) and computed tomography (CT) enable non-invasive visualization of the brain, aiding in the understanding of brain anatomy, abnormalities, and functional connectivity. However, cost and radiation dose may limit the acquisition of specific image modalities, so medical image synthesis can be used to generate required medical images without actual addition. CycleGAN and other GANs are valuable tools for generating synthetic images across various fields. In the medical domain, where obtaining labeled medical images is labor-intensive and expensive, addressing data scarcity is a major challenge. Recent studies propose using transfer learning to overcome this issue. This involves adapting pre-trained CycleGAN models, initially trained on non-medical data, to generate realistic medical images. In this work, transfer learning was applied to the task of MR-CT image translation and vice versa using 18 pre-trained non-medical models, and the models were fine-tuned to have the best result. The models’ performance was evaluated using four widely used image quality metrics: Peak-signal-to-noise-ratio, Structural Similarity Index, Universal Quality Index, and Visual Information Fidelity. Quantitative evaluation and qualitative perceptual analysis by radiologists demonstrate the potential of transfer learning in medical imaging and the effectiveness of the generic pre-trained model. The results provide compelling evidence of the model’s exceptional performance, which can be attributed to the high quality and similarity of the training images to actual human brain images. These results underscore the significance of carefully selecting appropriate and representative training images to optimize performance in brain image analysis tasks.", "Keywords": "", "DOI": "10.1016/j.cviu.2024.104100", "PubYear": 2024, "Volume": "248", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Zarqa University, Zarqa 13110, Jordan"}, {"AuthorId": 2, "Name": "Rizik <PERSON>", "Affiliation": "<PERSON> School for Information Technology, University of Jordan, Amman 11942, Jordan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Diagnostic Radiology, Jordan University Hospital, Amman 11942, Jordan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "<PERSON> School for Information Technology, University of Jordan, Amman 11942, Jordan;Corresponding author"}], "References": [{"Title": "Deep convolutional neural networks with transfer learning for automated brain image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "3", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Automated Categorization of Brain Tumor from MRI Using CNN features and SVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8357", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "PSNR vs SSIM: imperceptibility quality assessment for image steganography", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "6", "Page": "8423", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Transfer Learning–Based Active Learning Framework for Brain Tumor Classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "61", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Performance Comparison of Different Pre-Trained Deep Learning Models in Classifying Brain MRI Images", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "141", "JournalTitle": "Acta INFOLOGICA"}]}, {"ArticleId": 116816652, "Title": "On the Response Entropy of APUFs", "Abstract": "<p>A physically unclonable function (PUF) is a hardware security primitive used for authentication and key generation. It takes an input bit-vector challenge and produces a single-bit response, resulting in a challenge-response pair (CRP). The truth table of all challenge-response pairs of each manufactured PUF should look different due to inherent manufacturing randomness, forming a digital fingerprint. A PUF’s entropy (the entropy of all the responses, taken over the manufacturing randomness and uniformly selected challenges) has been studied before and is a challenging problem. Here, we explore a related notion—the response entropy , which is the entropy of an arbitrary response given knowledge of one (and later two) other responses. This allows us to explore how knowledge of some CRP(s) impacts the ability to guess another response. The arbiter PUF (APUF) is a well-known PUF architecture based on accumulated delay differences between two paths. In this paper, we obtain in closed form the probability mass function of any arbitrary response given knowledge of one or two other arbitrary CRPs for the APUF architecture. This allows us to obtain the conditional response entropy and then to define and obtain the size of the entropy bins (challenge sets with the same conditional response entropy) given knowledge of one or two CRPs. All of these results depend on the probability that two different challenge vectors yield the same response, termed the response similarity of those challenges. We obtain an explicit closed-form expression for this. This probability depends on the statistical correlations induced by the PUF architecture together with the specific known and to-be-guessed challenges. As a by-product, we also obtain the optimal (minimizing probability of error) predictor of an unknown challenge given access to one (or two) challenges and the associated predictability.</p>", "Keywords": "Arbiter PUF; CRP correlation; Entropy bins; Expected entropy; Response entropy", "DOI": "10.1007/s41635-024-00151-9", "PubYear": 2024, "Volume": "8", "Issue": "3", "JournalId": 7099, "JournalTitle": "Journal of Hardware and Systems Security", "ISSN": "2509-3428", "EISSN": "2509-3436", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Illinois Chicago, Chicago, USA; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Illinois Chicago, Chicago, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Illinois Chicago, Chicago, USA"}], "References": [{"Title": "An Analysis of FPGA LUT Bias and Entropy for Physical Unclonable Functions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "4", "Page": "110", "JournalTitle": "Journal of Hardware and Systems Security"}, {"Title": "A Practical Approach to Estimate the Min-Entropy in PUFs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "4", "Page": "138", "JournalTitle": "Journal of Hardware and Systems Security"}]}, {"ArticleId": 116816670, "Title": "1B4-2　視覚表示および映像の生体安全性におけるヒューマンシステムインタラクションの課題と国際標準化: －ISO/TC 159/SC 4/WG 2及び/WG 12における国際標準化活動－", "Abstract": "", "Keywords": "", "DOI": "10.5100/jje.60.1B4-2", "PubYear": 2024, "Volume": "60", "Issue": "Supplement", "JournalId": 21274, "JournalTitle": "The Japanese Journal of Ergonomics", "ISSN": "0549-4974", "EISSN": "1884-2844", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>u UJIKE", "Affiliation": "Tokyo Information Design Professional University, Faculty of Information Design; AIST"}, {"AuthorId": 2, "Name": "Kei HYODO", "Affiliation": "AIST; Yuasa System Co. Ltd"}, {"AuthorId": 3, "Name": "Shin-ich UEHARA", "Affiliation": "AGC Inc."}, {"AuthorId": 4, "Name": "Mitsunori TADA", "Affiliation": "AIST"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>hi WATANABE", "Affiliation": "AIST"}], "References": []}, {"ArticleId": 116816896, "Title": "Design and Improvement of Tandem Twin-Rotor Aerial-Aquatic Vehicle Based on Numerical Analysis", "Abstract": "To enhance the air–water adaptability of the aerial-aquatic vehicles (AAVs), an improved tandem twin-rotor AAV design is proposed based on the characteristics and application requirements of air–water cross-domain movement. Computational fluid dynamics (CFD) software was used to simulate the underwater cruising state and dynamic water entry process of the tandem twin-rotor AAV. Results indicate that the underwater cruise resistance of the improved tandem twin-rotor AAV is relatively small. Among them, the two sets of tandem air power systems account for a relatively large proportion of the underwater drag, about 29.8%, while the drag reduction achieved by improving the head shape is around 15.4%. The head shape, water entry angle and speed have a great influence on the water entry trajectory and attitude of the tandem twin-rotor AAV. Following improvement, the tandem twin-rotor AAV demonstrates an enhancement in reducing the deviation of the water entry trajectory, mitigating water surface ricochet and enhancement of water entry depth to a certain extent. The inclination angle should be in the range of 20–30 ∘ to alleviate unfavorable outcomes, including prolonged water entry time caused by water surface ricochet at lower entry angles, while concurrently mitigating the heightened impact pressure resulting from steeper entry angles. Higher entry velocities lead to greater impact pressures, necessitating careful consideration of water entry depth and impact pressure to prevent adverse effects on structural integrity.", "Keywords": "", "DOI": "10.1142/S2301385025500578", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 26691, "JournalTitle": "Unmanned Systems", "ISSN": "2301-3850", "EISSN": "2301-3869", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering, Guangdong Laboratory, Zhuhai 519000, P. R. China;School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen 518107, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen 518107, P. R. <PERSON>"}, {"AuthorId": 3, "Name": "Sihuan Wu", "Affiliation": "School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen 518107, P. R. <PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen 518107, P. R. <PERSON>"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen 518107, P. R. <PERSON>"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Southern Marine Science and Engineering, Guangdong Laboratory, Zhuhai 519000, P. R. China;School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen 518107, P. R. China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "School of Aeronautics and Astronautics, Sun Yat-sen University, Shenzhen 518107, P. R. <PERSON>"}], "References": []}, {"ArticleId": 116816959, "Title": "Interoperable node integrity verification for confidential machines based on AMD SEV-SNP", "Abstract": "<p>Confidential virtual machines (CVMs) are cloud providers' most recent security offer, providing confidentiality and integrity features. Although confidentiality protects the machine from the host operating system, firmware, and cloud operators, integrity protection is even more useful, enabling protection for a wider range of security issues. Unfortunately, CVM integrity verification depends on remote attestation protocols, which are not trivial for operators and differ largely among cloud providers. We propose an approach for abstracting CVM attestation that leverages an open-source standard, Cloud Native Foundation's Secure Production Identity Framework for Everyone (SPIFFE). Our approach can integrate smoothly even when applications are unaware of CVMs or the SPIFFE standard. Nevertheless, our implementation inherits SPIFFE flexibility for empowering access control when applications support SPIFFE. In terms of performance, CVMs incur an additional 1.3 s to 21.9 s in boot times (it varies with the cloud environment), a marginal degradation for CPU, RAM, and IO workloads (maximum degradation of 2.6%), and low but not imperceptible degradation for database workloads (between 3.6% to 7.13%). Finally, we provide usability mechanisms and a threat analysis to help users navigate cloud providers' different CVM implementations and resulting guarantees.</p>", "Keywords": "", "DOI": "10.5753/jisa.2024.3905", "PubYear": 2024, "Volume": "15", "Issue": "1", "JournalId": 19980, "JournalTitle": "Journal of Internet Services and Applications", "ISSN": "1867-4828", "EISSN": "1869-0238", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116816976, "Title": "Quantum Algorithms for the Multiplication of Circulant Matrices and Vectors", "Abstract": "<p>This article presents two quantum algorithms for computing the product of a circulant matrix and a vector. The arithmetic complexity of the first algorithm is O(Nlog2N) in most cases. For the second algorithm, when the entries in the circulant matrix and the vector take values in C or R, the complexity is O(Nlog2N) in most cases. However, when these entries take values from positive real numbers, the complexity is reduced to O(log3N) in most cases, which presents an exponential speedup compared to the classical complexity of O(NlogN) for computing the product of a circulant matrix and vector. We apply this algorithm to the convolution calculation in quantum convolutional neural networks, which effectively accelerates the computation of convolutions. Additionally, we present a concrete quantum circuit structure for quantum convolutional neural networks.</p>", "Keywords": "", "DOI": "10.3390/info15080453", "PubYear": 2024, "Volume": "15", "Issue": "8", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Cyberspace Security Defense, Institute of Information Engineering, CAS, Beijing 100085, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing 100085, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Cyberspace Security Defense, Institute of Information Engineering, CAS, Beijing 100085, China; School of Cyber Security, University of Chinese Academy of Sciences, Beijing 100085, China"}, {"AuthorId": 3, "Name": "<PERSON>v", "Affiliation": "Key Laboratory of Cyberspace Security Defense, Institute of Information Engineering, CAS, Beijing 100085, China"}], "References": [{"Title": "Quanvolutional neural networks: powering image recognition with quantum circuits", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "1", "JournalTitle": "Quantum Machine Intelligence"}]}, {"ArticleId": 116817003, "Title": "Falling through the net again: exploring the characteristics of internet dropout in China", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10209-024-01137-8", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1397, "JournalTitle": "Universal Access in the Information Society", "ISSN": "1615-5289", "EISSN": "1615-5297", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Not every line is connected equally: evidence from Deyang’s mobile users", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "26", "Issue": "1", "Page": "162", "JournalTitle": "Information Technology for Development"}, {"Title": "Toward a refined conceptualization of IS discontinuance: Reflection on the past and a way forward", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "2", "Page": "103167", "JournalTitle": "Information & Management"}]}, {"ArticleId": 116817080, "Title": "Enhanced domain transfer deep fuzzy echo state network for rotating machinery fault diagnosis based on current signal", "Abstract": "Although vibration-based fault diagnosis methods have achieved remarkable results in rotating machinery, they are still limited by various factors, such as environment noise and additional sensor installation. Meanwhile, due to inevitable distribution differences in practical engineering, deep transfer learning models are commonly used in fault diagnostic fields. Although deep architectures can extract representative domain-invariant features, they typically result in substantial computational burdens and training time. Therefore, to address the above problems, an enhanced domain transfer deep fuzzy echo state network (EDFESN) is proposed for rotating machinery fault diagnosis based on current signals. Firstly, current signals derived from the driving motor are directly collected without additional sensors. Secondly, an enhanced domain mapping method is raised to solve the problem of data distribution discrepancies between cross domain. Furthermore, a deep fuzzy echo state network (DFESN) integrated with fuzzy clustering is constructed, which utilizes layer-wise fuzzy-tuning learning paradigm instead of backpropagation step to reduce training time. Two datasets from gearbox and bearing both verify the effectiveness of the proposed method. Compared with other popular transfer methods, EDFESN not only achieves highest accuracy, but also has fastest training speed.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112033", "PubYear": 2024, "Volume": "165", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dongguan University of Technology, Dongguan 523808, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dongguan University of Technology, Dongguan 523808, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dongguan University of Technology, Dongguan 523808, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dongguan University of Technology, Dongguan 523808, PR China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Dongguan University of Technology, Dongguan 523808, PR China"}, {"AuthorId": 6, "Name": "Weihua Li", "Affiliation": "School of Mechanical and Automotive Engineering, South China University of Technology, Guangzhou 510640, PR China"}], "References": [{"Title": "Wasserstein distance based deep adversarial transfer learning for intelligent fault diagnosis with unlabeled or insufficient labeled data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "409", "Issue": "", "Page": "35", "JournalTitle": "Neurocomputing"}, {"Title": "Intelligent fault diagnosis of machine under noisy environment using ensemble orthogonal contractive auto-encoder", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117408", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel intelligent fault diagnosis method of rotating machinery based on signal-to-image mapping and deep Gabor convolutional adaptive pooling network", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "205", "Issue": "", "Page": "117716", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An open-set fault diagnosis framework for MMCs based on optimized temporal convolutional network", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109959", "JournalTitle": "Applied Soft Computing"}, {"Title": "Domain adaptation meta-learning network with discard-supplement module for few-shot cross-domain rotating machinery fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "268", "Issue": "", "Page": "110484", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A collaborative central domain adaptation approach with multi-order graph embedding for bearing fault diagnosis under few-shot samples", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "110243", "JournalTitle": "Applied Soft Computing"}, {"Title": "A novel deep clustering network using multi-representation autoencoder and adversarial learning for large cross-domain fault diagnosis of rolling bearings", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "120066", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multiscale reduction clustering of vibration signals for unsupervised diagnosis of machine faults", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110358", "JournalTitle": "Applied Soft Computing"}, {"Title": "Gradient flow-based meta generative adversarial network for data augmentation in fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110313", "JournalTitle": "Applied Soft Computing"}, {"Title": "Intelligent fault diagnosis scheme for rolling bearing based on domain adaptation in one dimensional feature matching", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "146", "Issue": "", "Page": "110669", "JournalTitle": "Applied Soft Computing"}, {"Title": "An adaptive imbalance modified online broad learning system-based fault diagnosis for imbalanced chemical process data stream", "Authors": "Jinkun Men; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "234", "Issue": "", "Page": "121159", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Generative adversarial network to alleviate information insufficiency in intelligent fault diagnosis by generating continuations of signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110784", "JournalTitle": "Applied Soft Computing"}, {"Title": "Integrating adaptive input length selection strategy and unsupervised transfer learning for bearing fault diagnosis under noisy conditions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110870", "JournalTitle": "Applied Soft Computing"}, {"Title": "Integrating adaptive input length selection strategy and unsupervised transfer learning for bearing fault diagnosis under noisy conditions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "148", "Issue": "", "Page": "110870", "JournalTitle": "Applied Soft Computing"}, {"Title": "Deep semi-supervised transfer learning method on few source data with sensitivity-aware decision boundary adaptation for intelligent fault diagnosis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Hongbing Xu", "PubYear": 2024, "Volume": "249", "Issue": "", "Page": "123714", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 116817139, "Title": "Predicting the seismic performance of large-scale dome structures with hybrid uncertainties based on Bayesian inference", "Abstract": "Machine learning has demonstrated an impressive performance in data-intensive applications and its performance is significantly affected by the availability of the training samples. For the stochastic finite element time-history response analyses of large-scale structures subjected to earthquakes, a large number of training samples are difficult to obtain. The Bayesian inference, as a successful artificial intelligence technique, has gained more attention in this context. In this study, a new Bayesian inference framework is developed to predict the response distribution and failure probability of a real large-scale dome structure subjected to minor, moderate, and major earthquakes with both aleatory and epistemic uncertainties. First, a Bayesian inference framework is proposed, in which an efficient adaptive sampling method for model parameters is developed and validated. Secondly, a small number of response samples are generated by a proposed stochastic finite element model. Thirdly, the statistical distribution of the responses and the failure probability are predicted using the inference framework with the small number of samples, and the results are compared with those by the large number of response samples. Lastly, several important aspects related to Bayesian inference are discussed. The results show that the proposed approach is efficient and accurate in identifying the model parameters and predicting the response distribution and failure probability without the complex prior knowledge and a large number of samples. It could significantly improve the computational efficiency for the probabilistic seismic performance evaluation of large and super-large structures with high dimensions in practice.", "Keywords": "Bayesian inference; Large-scale dome; Hybrid uncertainties; Stochastic excitation; Seismic performance", "DOI": "10.1016/j.engappai.2024.109031", "PubYear": 2024, "Volume": "136", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "Hui<PERSON> Zhang", "Affiliation": "School of Civil Engineering, Tianjin Chengjian University, Tianjin, 300384, China;Tianjin Key Laboratory of Civil Buildings Protection and Reinforcement, Tianjin, 300384, China;Corresponding author. School of Civil Engineering, Tianjin Chengjian University, Tianjin 300384, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Civil and Environmental Engineering, University of Technology Sydney, NSW, 2007, Australia;Corresponding author. School of Civil and Environmental Engineering, University of Technology Sydney, NSW 2007, Australia"}], "References": [{"Title": "Deep learning techniques for predicting nonlinear multi-component seismic responses of structural buildings", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "252", "Issue": "", "Page": "106570", "JournalTitle": "Computers & Structures"}, {"Title": "Diagnosing with a hybrid fuzzy–Bayesian inference approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "104345", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Deep convolutional transfer learning-based structural damage detection with domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": *********, "Title": "SECON: Maintaining Semantic Consistency in Data Augmentation for Code Search", "Abstract": "<p> Efficient code search techniques are crucial in accelerating software development by aiding developers in locating specific code snippets and understanding code functionalities. This study investigates code search methodologies, focusing on the emerging significance of semantic consistency in data augmentation techniques. While existing approaches predominantly enhance raw data, often requiring additional preprocessing and incurring higher training costs, this research introduces a pioneering method operating at the code and query representation levels. By bypassing the need for extensive data processing, this novel approach fosters an interactive alignment between code and query, augmenting the semantic coherence crucial for effective code search. An extensive empirical evaluation of a diverse dataset across multiple programming languages substantiates the efficacy of this approach in significantly enhancing code search model performance compared to traditional methodologies. The implementation is publicly available on GitHub <sup>1</sup> , offering an accessible resource for further exploration and application. </p>", "Keywords": "", "DOI": "10.1145/3686151", "PubYear": 2025, "Volume": "43", "Issue": "2", "JournalId": 12861, "JournalTitle": "ACM Transactions on Information Systems", "ISSN": "1046-8188", "EISSN": "1558-2868", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Key Laboratory of New Generation Artificial Intelligence Technology and Its Interdisciplinary Applications, Ministry of Education, Southeast University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Key Laboratory of New Generation Artificial Intelligence Technology and Its Interdisciplinary Applications, Ministry of Education, Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Key Laboratory of New Generation Artificial Intelligence Technology and Its Interdisciplinary Applications, Ministry of Education, Southeast University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Key Laboratory of Computing Power Network and Information Security, Ministry of Education, Shandong Computer Science Center (National Supercomputer Center in Jinan), Qilu University of Technology (Shandong Academy of Sciences), Jinan, China"}, {"AuthorId": 5, "Name": "<PERSON>peng Lu", "Affiliation": "School of Computer Science and Technology, Key Laboratory of Computing Power Network and Information Security, Ministry of Education, Shandong Computer Science Center (National Supercomputer Center in Jinan), Qilu University of Technology (Shandong Academy of Sciences), Jinan, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Key Laboratory of New Generation Artificial Intelligence Technology and Its Interdisciplinary Applications, Ministry of Education, Southeast University, Nanjing, China"}], "References": [{"Title": "deGraphCS : Embedding Variable-based Flow Graph for Neural Code Search", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "I2R: Intra and inter-modal representation learning for code search", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "28", "Issue": "3", "Page": "807", "JournalTitle": "Intelligent Data Analysis"}]}, {"ArticleId": 116817200, "Title": "A Machine Learning Based Intelligent Diabetic and Hypertensive Patient Prediction Scheme and A Mobile Application for Patients Assistance", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijitcs.2024.04.02", "PubYear": 2024, "Volume": "16", "Issue": "4", "JournalId": 8584, "JournalTitle": "International Journal of Information Technology and Computer Science", "ISSN": "2074-9007", "EISSN": "2074-9015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of computer science and engineering of the Chittagong University of Engineering and technology, Chittagong-4349, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 116817219, "Title": "“I Want It That Way”: Enabling Interactive Decision Support Using Large Language Models and Constraint Programming", "Abstract": "<p>A critical factor in the success of many decision support systems is the accurate modeling of user preferences. Psychology research has demonstrated that users often develop their preferences during the elicitation process, highlighting the pivotal role of system-user interaction in developing personalized systems. This paper introduces a novel approach, combining Large Language Models (LLMs) with Constraint Programming to facilitate interactive decision support. We study this hybrid framework through the lens of meeting scheduling, a time-consuming daily activity faced by a multitude of information workers. We conduct three studies to evaluate the novel framework, including a diary study to characterize contextual scheduling preferences, a quantitative evaluation of the system’s performance, and a user study to elicit insights with a technology probe that encapsulates our framework. Our work highlights the potential for a hybrid LLM and optimization approach for iterative preference elicitation, and suggests design considerations for building systems that support humansystem collaborative decision-making processes.</p>", "Keywords": "", "DOI": "10.1145/3685053", "PubYear": 2024, "Volume": "14", "Issue": "3", "JournalId": 13925, "JournalTitle": "ACM Transactions on Interactive Intelligent Systems", "ISSN": "2160-6455", "EISSN": "2160-6463", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cornell University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Texas at Austin, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Macalester College, USA"}, {"AuthorId": 6, "Name": "Cristina St. Hill", "Affiliation": "Microsoft, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft Research, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Microsoft, USA"}], "References": [{"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "What do we want from Explainable Artificial Intelligence (XAI)? – A stakeholder perspective on XAI and a conceptual model guiding interdisciplinary XAI research", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "296", "Issue": "", "Page": "103473", "JournalTitle": "Artificial Intelligence"}, {"Title": "A Survey on Conversational Recommender Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": *********, "Title": "Designing Student Internship Information System Interfaces Using the Design Thinking Method", "Abstract": "<p>Internship programs are essential for tertiary students to gain relevant experience before entering the workforce. Managing these programs, especially in institutions such as the East Java Province Marine and Fisheries Agency (DKP), poses challenges due to the reliance on paper-based processes and the need for centralized management across 17 branches. Therefore, this research developed a \"Centralized Internship Information System\" (CIIS) program designed with the aim of simplifying internship registration, student placement, document management, and program monitoring at DKP and its branches. This research focuses on designing a user-friendly interface for CIIS to address these issues. The design process follows the Design Thinking method, which emphasizes deeply understanding user needs and creating innovative solutions. The effectiveness of CIIS was evaluated using the Short User Experience Questionnaire (UEQ), which assesses user experience across key aspects. The UEQ results showed an average pragmatic quality score of 1.954 (excellent) and an average hedonic quality score of 1.856, with an overall average of 1.91. These results indicate that CIIS provides a high-quality user experience, facilitating easier access to internship opportunities for students, efficient candidate selection for companies, and effective monitoring for internship managers at DKP.</p>", "Keywords": "Design Thinking;Internship;Interface Design;Shor UEQ;Website", "DOI": "10.51519/journalisi.v6i2.765", "PubYear": 2024, "Volume": "6", "Issue": "2", "JournalId": 63632, "JournalTitle": "Journal of Information Systems and Informatics", "ISSN": "2656-5935", "EISSN": "2656-4882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Telkom University Surabaya"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Telkom University Surabaya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Telkom University Surabaya"}], "References": []}, {"ArticleId": 116817235, "Title": "An Efficient and Versatile Variational Method for High-Dimensional Data Classification", "Abstract": "<p>High-dimensional data classification is a fundamental task in machine learning and imaging science. In this paper, we propose an efficient and versatile multi-class semi-supervised classification method for classifying high-dimensional data and unstructured point clouds. To begin with, a warm initialization is generated by using a fuzzy classification method such as the standard support vector machine or random labeling. Then an unconstraint convex variational model is proposed to purify and smooth the initialization, followed by a step which is to project the smoothed partition obtained previously to a binary partition. These steps can be repeated, with the latest result as a new initialization, to keep improving the classification quality. We show that the convex model of the smoothing step has a unique solution and can be solved by a specifically designed primal–dual algorithm whose convergence is guaranteed. We test our method and compare it with the state-of-the-art methods on several benchmark data sets. <PERSON>ough experimental results demonstrate that our method is superior in both the classification accuracy and computation speed for high-dimensional data and point clouds.</p>", "Keywords": "Semi-supervised clustering; Point cloud classification; Variational methods; Graph Laplacian", "DOI": "10.1007/s10915-024-02644-9", "PubYear": 2024, "Volume": "100", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Computer Science, University of Southampton, Southampton, UK; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Operations and Risk Management and School of Data Science, Lingnan University, Tuen Mun, Hong Kong; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Applied Mathematics, Brown University, Providence, USA; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, The Chinese University of Hong Kong, Shatin, Hong Kong; Corresponding author."}], "References": []}, {"ArticleId": 116817251, "Title": "Comparative Analysis of Machine Learning Techniques Using RGB Imaging for Nitrogen Stress Detection in Maize", "Abstract": "<p>Proper nitrogen management in crops is crucial to ensure optimal growth and yield maximization. While hyperspectral imagery is often used for nitrogen status estimation in crops, it is not feasible for real-time applications due to the complexity and high cost associated with it. Much of the research utilizing RGB data for detecting nitrogen stress in plants relies on datasets obtained under laboratory settings, which limits its usability in practical applications. This study focuses on identifying nitrogen deficiency in maize crops using RGB imaging data from a publicly available dataset obtained under field conditions. We have proposed a custom-built vision transformer model for the classification of maize into three stress classes. Additionally, we have analyzed the performance of convolutional neural network models, including ResNet50, EfficientNetB0, InceptionV3, and DenseNet121, for nitrogen stress estimation. Our approach involves transfer learning with fine-tuning, adding layers tailored to our specific application. Our detailed analysis shows that while vision transformer models generalize well, they converge prematurely with a higher loss value, indicating the need for further optimization. In contrast, the fine-tuned CNN models classify the crop into stressed, non-stressed, and semi-stressed classes with higher accuracy, achieving a maximum accuracy of 97% with EfficientNetB0 as the base model. This makes our fine-tuned EfficientNetB0 model a suitable candidate for practical applications in nitrogen stress detection.</p>", "Keywords": "", "DOI": "10.3390/ai5030062", "PubYear": 2024, "Volume": "5", "Issue": "3", "JournalId": 69255, "JournalTitle": "AI", "ISSN": "", "EISSN": "2673-2688", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Kansas State University, Manhattan, KS 66506, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Kansas State University, Manhattan, KS 66506, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, Florida Atlantic University, Boca Raton, FL 33431, USA"}], "References": [{"Title": "Hyperspectral imagery to monitor crop nutrient status within and across growing seasons", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "255", "Issue": "", "Page": "112303", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Nitrogen deficiency in maize: Annotated image classification dataset", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "", "Page": "109625", "JournalTitle": "Data in Brief"}]}, {"ArticleId": 116817280, "Title": "Global Realism with Bipolar Strings: From Bell Test to Real-World Causal–Logical Quantum Gravity and Brain–Universe Similarity for Entangled Machine Thinking and Imagination", "Abstract": "<p>Following <PERSON>’s prediction that “Physics constitutes a logical system of thought” and “Nature is the realization of the simplest conceivable mathematical ideas”, this topical review outlines a formal extension of local realism limited by the speed of light to global realism with bipolar strings (GRBS) that unifies the principle of locality with quantum nonlocality. The related literature is critically reviewed to justify GRBS which is shown as a necessary and inevitable consequence of the Bell test and an equilibrium-based axiomatization of physics and quantum information science for brain–universe similarity and human-level intelligence. With definable causality in regularity and mind–light–matter unity for quantum superposition/entanglement, bipolar universal modus ponens (BUMP) in GRBS makes quantum emergence and submergence of spacetime logically ubiquitous in both the physical and mental worlds—an unexpected but long-sought simplification of quantum gravity with complete background independence. It is shown that GRBS forms a basis for quantum intelligence (QI)—a spacetime transcendent, quantum–digital compatible, analytical quantum computing paradigm where bipolar strings lead to bipolar entropy as a nonlinear bipolar dynamic and set–theoretic unification of order and disorder as well as linearity and nonlinearity for energy/information conservation, regeneration, and degeneration toward quantum cognition and quantum biology (QCQB) as well as information-conservational blackhole keypad compression and big bang data recovery. Subsequently, GRBS is justified as a real-world quantum gravity (RWQG) theory—a bipolar relativistic causal–logical reconceptualization and unification of string theory, loop quantum gravity, and M-theory—the three roads to quantum gravity. Based on GRBS, the following is posited: (1) life is a living bipolar superstring regulated by bipolar entropy; (2) thinking with consciousness and memory growth as a prerequisite for human-level intelligence is fundamentally mind–light–matter unitary QI logically equivalent to quantum emergence (entanglement) and submergence (collapse) of spacetime. These two posits lead to a positive answer to the question “If AI machine cannot think, can QI machine think?”. Causal–logical brain modeling (CLBM) for entangled machine thinking and imagination (EMTI) is proposed and graphically illustrated. The testability and falsifiability of GRBS are discussed.</p>", "Keywords": "", "DOI": "10.3390/info15080456", "PubYear": 2024, "Volume": "15", "Issue": "8", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Independent Researcher, Statesboro, GA 30460, USA"}], "References": [{"Title": "Seven Properties of Self-Organization in the Human Brain", "Authors": "Birgitta <PERSON>-Langley", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "10", "JournalTitle": "Big Data and Cognitive Computing"}, {"Title": "If AI machine cannot think, can QI machine think?—from negative numbers to quantum intelligence for mind-light-matter unity", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Quantum Machine Intelligence"}, {"Title": "Quantum Bit Commitment Without Quantum Memory", "Authors": "Lidong Xu; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "67", "Issue": "3", "Page": "1163", "JournalTitle": "The Computer Journal"}, {"Title": "MABAC framework for logarithmic bipolar fuzzy multiple attribute group decision-making for supplier selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "1", "Page": "273", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 116817312, "Title": "Cascade Classifier for the Detection and Identification of Birds in a Videostream", "Abstract": "<p>A method and a prototype of the program for detecting the presence of birds in the video data flow in real time are presented in the paper. The method is based on the cascade classifier solving the problem of bird detection and identification with the use of a bioacoustic bird scaring system deployed at the Tomsk airport. In our research, the Viola-Jones cascade classifier representing one of the implementations of the Haar cascade algorithm has been used. This algorithm allows objects to be detected in images and videos with high accuracy and rate. In this case, the classifier was leaned on the data set containing images of birds that allowed us to reach high accuracy of bird detection and identification in the videos. The possibilities of the developed classifier are also estimated, and its high productivity is shown. In this study, various methods of machine learning and video data analysis are used to obtain exact and reliable results. As a whole, the present work is an innovative approach to a solution to the urgent problem of airport protection from birds. The application of the developed method has allowed the operating efficiency of the bioacoustic bird scaring system to be increased together with the safety of flights at the Tomsk airport, thereby decreasing the probability of airplane collisions with birds. The novelty of the work consists of the application of the <PERSON><PERSON>Jones method for solving the problem of bird detection and identification and estimating its efficiency. Thus, this work is an important contribution to the development of methods for detecting and identifying objects in videos and can also be used in other fields of science and technology in which the automatic detection and classification of objects in the video data flow is required.</p>", "Keywords": "авиационная орнитология и безопасность;видеонаблюдение;обнаружение и идентификация птиц", "DOI": "10.15622/ia.23.4.10", "PubYear": 2024, "Volume": "23", "Issue": "4", "JournalId": 82239, "JournalTitle": "Информатика и автоматизация", "ISSN": "2713-3192", "EISSN": "2713-3206", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Институт мониторинга климатических и экологических систем Сибирского отделения РАН (ИМКЭС СО РАН)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Институт мониторинга климатических и экологических систем Сибирского отделения РАН (ИМКЭС СО РАН)"}], "References": []}, {"ArticleId": *********, "Title": "Learning Optimal Dynamic Treatment Regime from Observational Clinical Data through Reinforcement Learning", "Abstract": "<p>In medicine, dynamic treatment regimes (DTRs) have emerged to guide personalized treatment decisions for patients, accounting for their unique characteristics. However, existing methods for determining optimal DTRs face limitations, often due to reliance on linear models unsuitable for complex disease analysis and a focus on outcome prediction over treatment effect estimation. To overcome these challenges, decision tree-based reinforcement learning approaches have been proposed. Our study aims to evaluate the performance and feasibility of such algorithms: tree-based reinforcement learning (T-RL), DTR-Causal Tree (DTR-CT), DTR-Causal Forest (DTR-CF), stochastic tree-based reinforcement learning (SL-RL), and Q-learning with Random Forest. Using real-world clinical data, we conducted experiments to compare algorithm performances. Evaluation metrics included the proportion of correctly assigned patients to recommended treatments and the empirical mean with standard deviation of expected counterfactual outcomes based on estimated optimal treatment strategies. This research not only highlights the potential of decision tree-based reinforcement learning for dynamic treatment regimes but also contributes to advancing personalized medicine by offering nuanced and effective treatment recommendations.</p>", "Keywords": "", "DOI": "10.3390/make6030088", "PubYear": 2024, "Volume": "6", "Issue": "3", "JournalId": 51955, "JournalTitle": "Machine Learning and Knowledge Extraction", "ISSN": "", "EISSN": "2504-4990", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "European Centre for Living Technology, Ca’ Foscari University of Venice, 30123 Venice, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "European Centre for Living Technology, Ca’ Foscari University of Venice, 30123 Venice, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "European Centre for Living Technology, Ca’ Foscari University of Venice, 30123 Venice, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "European Centre for Living Technology, Ca’ Foscari University of Venice, 30123 Venice, Italy"}], "References": [{"Title": "Decision support analysis for risk identification and control of patients affected by COVID-19 based on Bayesian Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "116547", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A survey of Bayesian Network structure learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "8", "Page": "8721", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 116817422, "Title": "実用的なソフトロボットを目指して: —ソフトロボティクスの持続的な発展に向けて—", "Abstract": "", "Keywords": "Soft Robotics;Earthworm Robot;Start-up", "DOI": "10.7210/jrsj.42.513", "PubYear": 2024, "Volume": "42", "Issue": "6", "JournalId": 11671, "JournalTitle": "Journal of the Robotics Society of Japan", "ISSN": "0289-1824", "EISSN": "1884-7145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chuo University"}], "References": []}, {"ArticleId": 116817463, "Title": "Regulating generative AIs: (Re)defining video games as cultural products", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-02034-7", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}]