[{"ArticleId": 105511826, "Title": "The Effect of Speed Limits and Traffic Signal Control on Emissions", "Abstract": "Traffic signals strongly influence traffic in cities – in terms of delay but also in terms of the emissions emitted by traffic. This work analyzes the effect of different traffic control strategies on emissions like NO<sub>x</sub> and PM<sub>x</sub>. Simulations of an inner-city arterial show that reducing the speed limit leads to a lower production of NO<sub>x</sub> and PM<sub>x</sub>, but to an increase in delay. The impact of the traffic signal control on the emissions is less distinct: A carefully designed co-ordination reduces emissions, while a simple actuated control cannot improve traffic flow and therefore increases pollutant production.", "Keywords": "Traffic control ; Emissions ; Traffic simulation ; Speed limit ; Traffic signal control", "DOI": "10.1016/j.procs.2022.03.073", "PubYear": 2022, "Volume": "201", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Technische Universität Berlin, Transport Systems Planning and Transport Telematics, Salzufer 17-19, 10587 Berlin, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Transport Systems, German Aerospace Center (DLR), Rutherfordstrasse 2, 12489 Berlin, Germany;Technische Universität Berlin, Transport Systems Planning and Transport Telematics, Salzufer 17-19, 10587 Berlin, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Technische Universität Berlin, Transport Systems Planning and Transport Telematics, Salzufer 17-19, 10587 Berlin, Germany"}], "References": []}, {"ArticleId": 105511858, "Title": "Predicting Temperature-induced Deflection and Abnormality Recognition of Cable-stayed Bridge Based on Machine Learning", "Abstract": "<p lang=\"zh\"><p><p>The vertical deflection of the main girder on a cable-stayed bridge is a direct reflection of the vertical stiffness of bridge structure, which represents the comprehensive mechanical performance of cable-stayed bridge. Compared with the deflection caused by vehicles, the deflection caused by temperature is often more significant and the change frequency is very low, which is easy to extract from raw data, and can be used as an index to evaluate the state of cable-stayed bridge. To obtain the control value for recognizing the abnormal deflection, it is necessary to establish an accurate input-output relationship between temperature and temperature-induced deflection. However, because of the high-order nonlinear relationship between the temperature and the temperature-induced deflection, the traditional linear regression is not accurate enough in modeling this relationship. To establish a high-precision model for the deflection, this paper uses the machine learning tools with the highly nonlinear fitting performance to further model the project. Considering both the precision and modeling efficiency, the Long-Short Term Memory (LSTM) network can build the optimal model between temperature and temperature-induced deflection. Use the regression value output by LSTM as the control value combining with the statistical pattern of t-test, the 6% abnormal deflection can be recognized. The 6% sensitivity can help to recognize bridge abnormalities earlier. </p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022123306003", "PubYear": 2022, "Volume": "33", "Issue": "6", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105511890, "Title": "AN APPROACH TO ORGANIZING A SINGLE MONITORING CENTER WH<PERSON><PERSON> MAINTAINING THE PROPERTY OF PHYSICAL ISOLATION OF SEGMENTS FOR INITIALLY NON-OVERLAPPING NETWORKS", "Abstract": "<p>На сегодняшний день задача контроля функционирования инфраструктуры является актуальной для многих организаций. Осуществление централизованного контроля позволяет улучшить показатели реагирования на инциденты и снизить потенциальный ущерб. Задача сохранения безопасности инфраструктуры является важным аспектом, которых необходимо учитывать при внедрении новых элементов в функционирующую информационную систему. В наше время эта тема имеет большую значимость в силу постоянно развивающихся способов атак на инфраструктуру организации. В данной статье описаны методы сохранения уровня безопасности инфраструктуры на прежнем уровне после внедрения единого центра мониторинга, использование которого позволяет ускорить реагирование на инциденты.</p><p>Today, the task of monitoring the functioning of the infrastructure is relevant for many organizations. Implementation of centralized control allows you to improve incident response rates and reduce potential damage. The task of maintaining infrastructure security is an important aspect that must be taken into account when introducing new elements into a functioning information system. In our time, this topic is of great importance due to the constantly evolving methods of attacks on the infrastructure of the organization. This article describes methods for maintaining the security level of the infrastructure at the same level after the implementation of a single monitoring center, the use of which allows you to accelerate incident response.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.2.012", "PubYear": 2022, "Volume": "", "Issue": "2(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "За<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Юл<PERSON>я Дмитриевна", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105511924, "Title": "More buyers or more sellers: on marketing resource allocation strategies of competing two-sided platforms", "Abstract": "<p>Two-sided platforms enable and supplement transactions between buyers and sellers. We consider a decision problem facing two such platform firms competing in a market. Each firm needs to divide its budget of a planning period between promotion towards attracting new sellers and new buyers. We propose a generalized Nash equilibrium problem (GNEP) model to find optimal allocations. The GNEP approach provides an eloquent framework for analysis and theory development. An intuitive result from the interpretation of optimality conditions is that a firm’s focus should be higher towards the group whose presence is less on the platform. This focus can shift depending on competitors’ ability to dissuade new customers. Interestingly, the model recommends that a firm should focus on getting new sellers when its customer-focused promotion adversely impacts competitors’ customer acquisition. Predatory promotion strategy adversely affects both. More useful implications can be drawn from the equilibrium analysis. We have assumed that a limited number of sellers are available in the market, whereas no restrictions are imposed on new customer acquisitions. This situation is typical during the entry phase of a two-sided platform.</p>", "Keywords": "Two-sided platforms; Platform competition; Resources allocation; E-commerce; Generalized Nash equilibrium", "DOI": "10.1007/s10660-022-09643-8", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 2555, "JournalTitle": "Electronic Commerce Research", "ISSN": "1389-5753", "EISSN": "1572-9362", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Management Studies, University of Delhi, Delhi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Management Studies, University of Delhi, Delhi, India"}], "References": [{"Title": "A choice of selling format in the online marketplace with cross-sales supply chain: Platform selling or traditional reselling?", "Authors": "<PERSON><PERSON><PERSON> Li; <PERSON><PERSON><PERSON>g Ai", "PubYear": 2021, "Volume": "21", "Issue": "2", "Page": "393", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Pricing and personal data collection strategies of online platforms in the face of privacy concerns", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "2", "Page": "539", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Platform entry and homing as competitive strategies under cross-sided network effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "113428", "JournalTitle": "Decision Support Systems"}, {"Title": "Investment decisions and pricing strategies of crowdfunding players: In a two-sided crowdfunding market", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "2", "Page": "1209", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Information services and omnichannel retailing strategy choices of e-commerce platforms with supplier competition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "4", "Page": "2647", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Coopetition in a platform ecosystem: from the complementors’ perspective", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "3", "Page": "1509", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Personalized pricing with persuasive advertising and the value of consumer information: a duopoly framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "24", "Issue": "3", "Page": "1533", "JournalTitle": "Electronic Commerce Research"}]}, {"ArticleId": 105511929, "Title": "Comprehensive survey on hierarchical clustering algorithms and the recent developments", "Abstract": "<p>Data clustering is a commonly used data processing technique in many fields, which divides objects into different clusters in terms of some similarity measure between data points. Comparing to partitioning clustering methods which give a flat partition of the data, hierarchical clustering methods can give multiple consistent partitions of the data at different levels for the same data without rerunning clustering, it can be used to better analyze the complex structure of the data. There are usually two kinds of hierarchical clustering methods: divisive and agglomerative. For the divisive clustering, the key issue is how to select a cluster for the next splitting procedure according to dissimilarity and how to divide the selected cluster. For agglomerative hierarchical clustering, the key issue is the similarity measure that is used to select the two most similar clusters for the next merge. Although both types of the methods produce the dendrogram of the data as output, the clustering results may be very different depending on the dissimilarity or similarity measure used in the clustering, and different types of methods should be selected according to different types of the data and different application scenarios. So, we have reviewed various hierarchical clustering methods comprehensively, especially the most recently developed methods, in this work. The similarity measure plays a crucial role during hierarchical clustering process, we have reviewed different types of the similarity measure along with the hierarchical clustering. More specifically, different types of hierarchical clustering methods are comprehensively reviewed from six aspects, and their advantages and drawbacks are analyzed. The application of some methods in real life is also discussed. Furthermore, we have also included some recent works in combining deep learning techniques and hierarchical clustering, which is worth serious attention and may improve the hierarchical clustering significantly in the future.</p>", "Keywords": "Hierarchical clustering; Divisive; Agglomerative; Dissimilarity; Similarity", "DOI": "10.1007/s10462-022-10366-3", "PubYear": 2023, "Volume": "56", "Issue": "8", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "Xingcheng Ran", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou, China; Center of Information Technology, Hexi University, Zhangye, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou, China"}, {"AuthorId": 3, "Name": "Yonggang Lu", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou, China"}, {"AuthorId": 5, "Name": "Zhenyu Lu", "Affiliation": "School of Information Science and Engineering, Lanzhou University, Lanzhou, China"}], "References": [{"Title": "Incremental Hierarchical Clustering for Data Insertion and Its Evaluation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "2", "Page": "1", "JournalTitle": "International Journal of Software Innovation"}, {"Title": "Incremental Hierarchical Clustering driven Automatic Annotations for Unifying IoT Streaming Data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "InPress", "Issue": "InPress", "Page": "56", "JournalTitle": "International Journal of Interactive Multimedia and Artificial Intelligence"}, {"Title": "Graph Similarity-based Hierarchical Clustering of Trajectory Data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "32", "JournalTitle": "Procedia Computer Science"}, {"Title": "KdMutual: A novel clustering algorithm combining mutual neighboring and hierarchical approaches using a new selection criterion", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "204", "Issue": "", "Page": "106220", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hierarchical clustering that takes advantage of both density-peak and density-connectivity", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "101871", "JournalTitle": "Information Systems"}, {"Title": "A dynamic hierarchical incremental learning-based supervised clustering for data stream with considering concept drift", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "2983", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "PIFHC: The Probabilistic Intuitionistic Fuzzy Hierarchical Clustering Algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "108584", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 105511934, "Title": "Multi-feature contrastive learning for unpaired image-to-image translation", "Abstract": "Unpaired image-to-image translation for the generation field has made much progress recently. However, these methods suffer from mode collapse because of the overfitting of the discriminator. To this end, we propose a straightforward method to construct a contrastive loss using the feature information of the discriminator output layer, which is named multi-feature contrastive learning (MCL). Our proposed method enhances the performance of the discriminator and solves the problem of model collapse by further leveraging contrastive learning. We perform extensive experiments on several open challenge datasets. Our method achieves state-of-the-art results compared with current methods. Finally, a series of ablation studies proved that our approach has better stability. In addition, our proposed method is also practical for single image translation tasks. Code is available at https://github.com/gouayao/MCL.", "Keywords": "Generative model; Image translation; Contrastive learning; Multi-feature", "DOI": "10.1007/s40747-022-00924-1", "PubYear": 2023, "Volume": "9", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Xi’an High-Tech Research Institute, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Xi’an High-Tech Research Institute, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Xi’an High-Tech Research Institute, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xi’an High-Tech Research Institute, Xi’an, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Xi’an High-Tech Research Institute, Xi’an, China"}], "References": [{"Title": "A comprehensive survey and analysis of generative models in machine learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "", "Page": "100285", "JournalTitle": "Computer Science Review"}, {"Title": "A wavelet convolutional capsule network with modified super resolution generative adversarial network for fault diagnosis and classification", "Authors": "<PERSON> Monday; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "6", "Page": "4831", "JournalTitle": "Complex & Intelligent Systems"}]}, {"ArticleId": 105511943, "Title": "The problem of synthesis of one class of binary 3d-multidimensional nonlinear modular dynamic systems", "Abstract": "In this paper, binary 3D-multidimensional nonlinear modular dynamic systems (3D-MNMDS) are considered: (Formula Presented) Here (Formula Presented) (2) and y[n, c] ∊ GFk (2) are input and output sequences, where GF(2) is a finite field, GF k (2) and GFr (2) are k and r-dimensional linear spaces respectively over the finite field GF(2) . Let arbitrary unknown binary sequences enter to the input 3D-MNMDS (1): (Formula Presented) The problem of the synthesis of binary 3D-MNMDS (1) consists in finding for all (Formula Presented), for which the following functional is minimized: (Formula Presente) where (Formula Presented), are desired output sequences. Based on the sequence (2) and on the elements of the impulse characteristics of the system, matrix U and vectors H are constructed respectively, with the help of which problem (1), (3) has the matrix form: Y = UH, GF(2) J = (Y - Y0 )Τ (Y - Y0 ) → min . If the orthogonal sequences υ ,i,η̄, w̄[n, c1, c2 ], ℓ∊ Q0 (η̄), w̄ ∊ Ψ(η̄), η̄ ∊ Λ(i), i∊{1,..., S}, enter to input o 3D-MNMDS (1) and the matrix V is formed from them, then the solution of the problem Y = VH, GF(2), J = (Y - Y0 )Τ (Y - Y0 ) → min can be found by solving the continual problem of the quadratic optimization Y = VK , J = (Y - Y0 )Τ (Y - Y0 ) → min . Let kα and hα be the α-th components of the vectors K and H respectively. From here, if kα &gt; 0.5, then hα = 1, else hα = 0. The vector K is determined by the formula K = (V ΤV )-1V ΤY0 . If the input sequence (2) is not orthogonal, then it is ortogonalized and the solution of the problem continues as in the case of orthogonal input sequences. © 2022 Tomsk State University. All rights reserved.", "Keywords": "3D-multidimensional nonlinear modular dynamic system; continual quadratic optimization problem; orthogonal input sequences; problem of synthesis; <PERSON><PERSON><PERSON>’s polynomial", "DOI": "10.17223/********/58/2", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 20377, "JournalTitle": "Vestnik Tomskogo gosudarstvennogo universiteta. Upravlenie, vychislitel'naya tekhnika i informatika", "ISSN": "1998-8605", "EISSN": "2311-2085", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Physical and Mathematical Sciences, Sumgait State University, Sumgait, Azerbaijan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Baku State University, Baku, Azerbaijan"}], "References": []}, {"ArticleId": 105511962, "Title": "Multi-Robot Area Coverage Strategy Based on Reinforcement Learning QMIX", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.1212287", "PubYear": 2022, "Volume": "12", "Issue": "12", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "磊磊 段", "Affiliation": ""}], "References": []}, {"ArticleId": 105512050, "Title": "Successful management of cloud‐based global software development projects: A multivocal study", "Abstract": "Software industry is continuously exploring better ways to develop applications. A new phenomenon to achieve this is cloud‐based global software development (CGSD), which refers to the adoption of cloud computing services by organizations to support global software development projects. The CGSD approach affects the strategic and operational aspects of the way projects are managed. The objective of the study is to identify the success factors which contribute to management of CGSD projects. We carried out a multivocal literature review (MLR) to identify the success factors from the state‐of‐the‐art and the state‐of‐the‐practice in project management of CGSD projects. We identified 32 success factors that contribute to the management of CGSD projects. The findings of MLR indicate that time to market, continuous development, financial restructuring, and scalability are the most critical success factors for CGSD. Moreover, the findings of the study show that there is a positive correlation between the success factors reported in both formal literature and industry based gray literature. The findings of this study can assist the practitioners to develop the strategies needed for effective project management of CGSD projects.", "Keywords": "cloud-based global software development;multivocal review;project management;success factors", "DOI": "10.1002/smr.2527", "PubYear": 2024, "Volume": "36", "Issue": "4", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering LUT University  Lappeenranta Finland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "M3S Empirical Software Engineering Research Unit University of Oulu  Oulu Finland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information and Computer Science Department King Fahd University of Petroleum and Minerals  Dhahran Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software Engineering LUT University  Lappeenranta Finland"}], "References": [{"Title": "A robust framework for cloud‐based software development outsourcing factors using analytical hierarchy process", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "2", "Page": "e2275", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "Prioritization based Taxonomy of Cloud-based Outsource Software Development Challenges: Fuzzy AHP analysis", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "106557", "JournalTitle": "Applied Soft Computing"}, {"Title": "Toward successful DevSecOps in software development organizations: A decision-making framework", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "147", "Issue": "", "Page": "106894", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": *********, "Title": "Breast Cancer Grading using Machine Learning Approach Algorithms", "Abstract": "Recently, Breast Cancer (BC) becomes a more common cancer disease in women and it considers the most important sign which leads to death among women. Therefore, it requires efficient methods for detecting it to reduce the risk of death. A positive prognosis and greater chances of survival are improved if the BC is detected early. Currently, machine learning plays an important role in diagnosing BC disease. The various techniques in artificial intelligence and machine learning persuade the researchers in exploring their classification systems in classifying and detecting the BC disease. The algorithms are the K-Nearest Neighbor (KNN), the Support Vector Machine (SVM), random forest, logistic regression, and decision tree. In this study, various algorithms of the machine are proposed in designing the classification system for detecting the BC diseases. To improve the resulting quality, the Principal Component Analysis Algorithm (PCA) is applied. The system was tested and evaluated on the Wisconsin BC dataset from the University of Wisconsin Hospitals. The results were interesting and very good. The accuracy, recall, precision, and F-score of the SVM algorithm were obtained by up to 98% compared to previous work © 2022 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>. This openaccess article is distributed under a Creative Commons Attribution (CC-BY) 4.0 license", "Keywords": "Bc; K-nearest neighbor (knn); Machine learning; Principal component analysis (pca); Support vector machine (svm)", "DOI": "10.3844/jcssp.2022.1213.1218", "PubYear": 2022, "Volume": "18", "Issue": "12", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of IT, Palestine Ahliya University, Bethlehem, Palestine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of IT, Palestine Ahliya University, Bethlehem, Palestine"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of IT, Palestine Ahliya University, Bethlehem, Palestine"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of IT, Palestine Ahliya University, Bethlehem, Palestine"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Engineering and Science, Prince <PERSON>, Khobar, 31952, Saudi Arabia"}], "References": []}, {"ArticleId": 105512120, "Title": "Comparison of Different Classification Algorithms for Prediction of Heart Disease by Machine Learning Techniques", "Abstract": "<p>Cardiovascular disease commonly referred as heart disease, encompasses diverse conditions that the heart undergoes which in turn leads to sudden death or prolonged sickness worldwide over the past decades. More recently, foreseeing heart disease is the stimulating responsibility in the health arena. In recent eras, every minute approximately one person expires due to heart ailment. Data Science processes big volumes of healthcare data and researchers apply a variety of datamining and machine learning techniques to analyze vast and complex medical data to help health care professionals predict heart disease. This tabloid collects heart disease dataset from UCI machine learning source analyzing which envisages the accuracy of heart disease by considering major risk factors based on different classifier algorithms. This research paper objective is to diagnose imminent heart disease via scrutinizing data of patients and analyzing if heart disease is pestilent with machine-learning algorithm.</p>", "Keywords": "Logistic regression; Random Forest; Heart disease prediction; Classification algorithm", "DOI": "10.1007/s42979-022-01512-3", "PubYear": 2023, "Volume": "4", "Issue": "2", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "GSSSIETW, Mysuru, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "GSSSIETW, Mysuru, India"}, {"AuthorId": 3, "Name": "B. <PERSON><PERSON>", "Affiliation": "GSSSIETW, Mysuru, India"}], "References": []}, {"ArticleId": 105512137, "Title": "Quantum Creativity and Cognition in Humans and Robots", "Abstract": "<p>In this research, we present a categorical framework to connect research on creativity and cognition for humans and robots, in light of the quantum paradigm. These fields and their relationships suggest a wider vision: modeling human creativity/cognition through quantum computing, and creating robots that can help us learn more about the humans themselves. We represent the human–robot comparison through functors (function generalization). Fundamental elements to understand human creativity are motivation and feedback as aesthetic pleasure. Is it possible to model it? Can the quantum paradigm help us in such an endeavor? We envisage the concept of emergence and quantum computing as decisive keys for creativity. Investigation of robotic models, including swarms of robots, might help shed new light on human creative interactions.</p>", "Keywords": "Quantum Computing; Swarm Intelligence; Creativity Modeling", "DOI": "10.1142/S2705078522500102", "PubYear": 2022, "Volume": "9", "Issue": "3", "JournalId": 75350, "JournalTitle": "Journal of Artificial Intelligence and Consciousness", "ISSN": "2705-0785", "EISSN": "2705-0793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Engineering, University of Palermo, Palermo 90128, Italy;European Centre for Living Technology (ECLT), Ca’ Foscari University of Venice, Venice 30123, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Engineering, University of Palermo, Palermo 90128, Italy"}], "References": [{"Title": "From Classical Logic to Fuzzy Logic and Quantum Logic: A General View", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}]}, {"ArticleId": 105512205, "Title": "Predictive Analytics to support diabetic patient detection", "Abstract": "The strong growth in the number of diabetics in recent years has become a major health concern. The dependence on sugar consumption has caused a rapid growth in the level of diagnoses and in the number of deaths associated. In this context, the project developed allowed a study on how Diabetes can be detected in a timely manner, through the existence of pre-indicators of the disease, defining factors that may determine its onset. For this study, data are collected from Hospital de Santa Luzia (ULSAM), considering aspects such as patient profile, prescribed drugs and previous diagnoses. The results prove that machine learning models using profile data with medical drugs produced the best results, optimizing the predictive ability of Diabetes.", "Keywords": "Diabetes ; Predictive Analytics ; Artificial Intelligence", "DOI": "10.1016/j.procs.2022.03.092", "PubYear": 2022, "Volume": "201", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centro ALGORITMI, University of Minho"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centro ALGORITMI, University of Minho"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centro ALGORITMI, University of Minho"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Centro ALGORITMI, University of Minho"}], "References": []}, {"ArticleId": 105512212, "Title": "Automatic delineation of hippocampus in CT images based on deep learning and dosimetry study in whole brain radiotherapy", "Abstract": "<b  >Objective</b> The artificial intelligence algorithm based on deep learning, combined with multi-modal brain magnetic resonance imaging (MRI) and computed tomography (CT) images, was used to develop an automatic delineation technology of the hippocampal structure from CT images to provide an efficient and accurate automatic delineation method for hippocampal protection during cranial radiotherapy. <b  >Methods</b> The MR and CT images of 20 patients with brain metastases were collected. After registering the MR and CT images, the hippocampus was delineated. Aunet, Unet, and Pix2pix deep learning models were trained on the CT-MRI dataset, and the differences between three automatically segmented hippocampus and the hippocampus manually segmented by senior chief physicians were calculated. The VMAT plan was designed separately for each patient. <b  >Results</b> The prediction results of the proposed method were more accurate and closer to the real segmentation results. The Dice, P, and R values were 0.8529, 0.8560, and 0.8632, respectively, indicating that the prediction results of Aunet were the closest to the real one. For the dose results, the differences between the maximum dose of the real hippocampus and three different methods were 75.2 (Aunet), 354.0 (Unet), and 462.1 (Pix2pix), and the differences between the average doses of the real hippocampus and three different methods were 30.3 (Aunet), 31.0 (Unet), and 66.6 (Pix2pix). <b  >Conclusion</b> The Aunet model can achieve efficient and accurate automatic delineation of the hippocampus on CT images. The dosimetric difference between the segmented and real hippocampi is extremely small, which is convenient to protect the hippocampus during brain radiotherapy.", "Keywords": "Deep learning ; CT image ; Automatic drawing of hippocampus ; Hippocampal protection ; Radiotherapy plan", "DOI": "10.1016/j.jrras.2022.100517", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "QianYi Xi", "Affiliation": "School of Microelectronics and Control Engineering, Changzhou University, Changzhou, 213164, China;Department of Radiotherapy the Second People's Hospital of Changzhou Affiliated to Nanjing Medical University, Changzhou, 213003, China;Central Laboratory of Medical Physics, Nanjing Medical University, Changzhou, 213003, China;Jiangsu Medical Physical Engineering Research Center, Changzhou, 213003, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Radiotherapy the Second People's Hospital of Changzhou Affiliated to Nanjing Medical University, Changzhou, 213003, China;Central Laboratory of Medical Physics, Nanjing Medical University, Changzhou, 213003, China;Jiangsu Medical Physical Engineering Research Center, Changzhou, 213003, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Microelectronics and Control Engineering, Changzhou University, Changzhou, 213164, China;Department of Radiotherapy the Second People's Hospital of Changzhou Affiliated to Nanjing Medical University, Changzhou, 213003, China;Central Laboratory of Medical Physics, Nanjing Medical University, Changzhou, 213003, China;Jiangsu Medical Physical Engineering Research Center, Changzhou, 213003, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Microelectronics and Control Engineering, Changzhou University, Changzhou, 213164, China;Department of Radiotherapy the Second People's Hospital of Changzhou Affiliated to Nanjing Medical University, Changzhou, 213003, China;Central Laboratory of Medical Physics, Nanjing Medical University, Changzhou, 213003, China;Jiangsu Medical Physical Engineering Research Center, Changzhou, 213003, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Microelectronics and Control Engineering, Changzhou University, Changzhou, 213164, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Radiotherapy the Second People's Hospital of Changzhou Affiliated to Nanjing Medical University, Changzhou, 213003, China;Central Laboratory of Medical Physics, Nanjing Medical University, Changzhou, 213003, China;Jiangsu Medical Physical Engineering Research Center, Changzhou, 213003, China;Corresponding author. Department of Radiotherapy the Second People's Hospital of Changzhou Affiliated to Nanjing Medical University, Changzhou, 213003, China"}], "References": []}, {"ArticleId": 105512225, "Title": "A systematic mapping study and practitioner insights on the use of software engineering practices to develop MVPs", "Abstract": "<b  >Background:</b> Many startup environments and even traditional software companies have embraced the use of MVPs (Minimum Viable Products) to allow quickly experimenting solution options. The MVP concept has influenced the way in which development teams apply Software Engineering (SE) practices. However, the overall understanding of this influence of MVPs on SE practices is still poor. <b  >Objective:</b> Our goal is to characterize the publication landscape on practices that have been used in the context of software MVPs and to gather practitioner insights on the identified practices. <b  >Method:</b> We conducted a systematic mapping study using a hybrid search strategy that consists of a database search and parallel forward and backward snowballing. Thereafter, we discussed the mapping study results in two focus groups sessions involving twelve industry practitioners that extensively use MVPs in their projects to capture their perceptions on the findings of the mapping study. <b  >Results:</b> We identified 33 papers published between 2013 and 2020. We observed some trends related to MVP ideation (or MVP conception) and evaluation practices. For instance, regarding ideation, we found six different approaches ( e.g. , Design Thinking, Lean Inception) and mainly informal end-user involvement practices ( e.g. , workshops, interviews). Regarding evaluation, there is an emphasis on end-user validations based on practices such as usability tests, A/B testing, and usage data analysis. However, there is still limited research related to MVP technical feasibility assessment and effort estimation. Practitioners of the focus group sessions reinforced the confidence in our results regarding ideation and evaluation practices, being aware of most of the identified practices. They also reported how they deal with the technical feasibility assessments (involving developers during the ideation and conducting informal experiments) and effort estimation in practice (based on expert opinion and using practices common to agile methodologies, such as Planning Poker). <b  >Conclusion:</b> Our analysis suggests that there are opportunities for solution proposals and evaluation studies to address literature gaps concerning technical feasibility assessment and effort estimation. Overall, more effort needs to be invested into empirically evaluating the existing MVP-related practices.", "Keywords": "", "DOI": "10.1016/j.infsof.2022.107144", "PubYear": 2023, "Volume": "156", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics - Pontifical Catholic University of Rio de Janeiro (PUC-Rio), Rio de Janeiro, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Informatics - Pontifical Catholic University of Rio de Janeiro (PUC-Rio), Rio de Janeiro, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics - Pontifical Catholic University of Rio de Janeiro (PUC-Rio), Rio de Janeiro, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Informatics - Pontifical Catholic University of Rio de Janeiro (PUC-Rio), Rio de Janeiro, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics - Pontifical Catholic University of Rio de Janeiro (PUC-Rio), Rio de Janeiro, Brazil"}], "References": [{"Title": "On the performance of hybrid search strategies for systematic literature reviews in software engineering", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "106294", "JournalTitle": "Information and Software Technology"}]}, {"ArticleId": 105512246, "Title": "Towards improving the computational efficiency of the phase field model", "Abstract": "In this paper, we propose novel numerical schemes to improve the computational efficiency of the phase field model for brittle fracture. In order to reduce computation time, an adaptive update scheme is proposed. To maximize its computational efficiency, an effective adaptive mesh refinement scheme using variable-node elements is adopted. The use of the adaptive update scheme reduces computation time by 40% to 50% while maintaining the desired solution accuracy. The effectiveness of the proposed methods is demonstrated through various numerical examples.", "Keywords": "Adaptive mesh refinement ; Phase field model ; Brittle fracture ; Variable-node element", "DOI": "10.1016/j.compstruc.2022.106951", "PubYear": 2023, "Volume": "277-278", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Korea Advanced Institute of Science and Technology, 291 Daehak-ro, Yuseong-gu, Daejeon 34141, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Korea Advanced Institute of Science and Technology, 291 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Daejeon 34141, Republic of Korea;Corresponding author"}], "References": []}, {"ArticleId": 105512275, "Title": "Aspect-Level Sentiment Analysis Based on Bi-Channel Interactive Graph Convolutional Network", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.1212291", "PubYear": 2022, "Volume": "12", "Issue": "12", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "雪晴 信", "Affiliation": ""}], "References": [{"Title": "GL-GCN: Global and Local Dependency Guided Graph Convolutional Networks for aspect-based sentiment classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "115712", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Transparent Aspect-Level Sentiment Analysis Based on Dependency Syntax Analysis and Its Application on COVID-19", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Data and Information Quality"}]}, {"ArticleId": *********, "Title": "Converse extensionality and apartness", "Abstract": "<p>In this paper we try to find a computational interpretation for a strong form of extensionality, which we call \"converse extensionality\". Converse extensionality principles, which arise as the Dialectica interpretation of the axiom of extensionality, were first studied by <PERSON>. In order to give a computational interpretation to these principles, we reconsider <PERSON><PERSON><PERSON>'s apartness relation, a strong constructive form of inequality. Formally, we provide a categorical construction to endow every typed combinatory algebra with an apartness relation. We then exploit that functions reflect apartness, in addition to preserving equality, to prove that the resulting categories of assemblies model a converse extensionality principle.</p>", "Keywords": "Mathematics - Logic;Computer Science - Logic in Computer Science;Mathematics - Category Theory", "DOI": "10.46298/lmcs-18(4:13)2022", "PubYear": 2022, "Volume": "18, Issue 4", "Issue": "", "JournalId": 10075, "JournalTitle": "Logical Methods in Computer Science", "ISSN": "", "EISSN": "1860-5974", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Logic, Language and Computation, Universiteit van Amsterdam, Postbus 90242, Amsterdam, 1090 GE, Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Logic, Language and Computation, Universiteit van Amsterdam, Postbus 90242, Amsterdam, 1090 GE, Netherlands"}], "References": []}, {"ArticleId": 105512496, "Title": "Automatic breast cancer detection using HGMMEM algorithm with DELMA classification", "Abstract": "<p>Breast cancer detection is a challenging task in the field of medical image processing. Nowadays huge amount of research is happening in this field. Usually, for an abnormal growth of tissues in the breast, one may examine their status by a doctor through mammography images. Here, in this research article, the experimental analysis work mammography images are taken from the both Public Digital Database of Screening Mammography (DDSM), the MIAS dataset, and the in-house clinical dataset from Metro scans and laboratories. The first stage is to remove noise from the input image and boost the contrast of the image’s anomalous region. For noise reduction, a Weiner filter with unsharp masking is utilized first, followed by contrast enhancement utilizing adjusted intensity-based adaptive histogram equalization. Identifying the accurate breast tumor position is a challenge, for analyzing and segmenting the ROI Hierarchical Gaussian Mixture Model with Expectation-Maximization algorithm is adopted. Then GLCM and FOS features are extracted. Finally, images of benign and malignant breast cancer are classified using the machine learning classifier SVM, and a deep learning model based on Deep Extreme Learning Machine with Autoencoder classifier is developed. Using SVM, an accuracy of 70.2% is obtained. Deep Extreme Learning Machine with Autoencoder classifier has an accuracy of 99.1%, which aids in the diagnosis of cancer cells through the automatic feature extraction process.</p>", "Keywords": "Breast cancer; <PERSON><PERSON> filter with Unsharp masking; Adjusted intensity based adaptive histogram equalization; Hierarchical Gaussian mixture model with expectation maximization; GLCM; FOS; SVM classifier & deep extreme learning machine with autoencoder classifier", "DOI": "10.1007/s11042-022-14310-z", "PubYear": 2023, "Volume": "82", "Issue": "17", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, Noorul Islam Centre for Higher Education, Kumaracoil, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Noorul Islam Centre for Higher Education, Kumaracoil, India"}], "References": [{"Title": "Images data practices for Semantic Segmentation of Breast Cancer using Deep Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "11", "Page": "15227", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Systematic Review of Computing Approaches for Breast Cancer Detection Based Computer Aided Diagnosis Using Mammogram Images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "15", "Page": "2157", "JournalTitle": "Applied Artificial Intelligence"}]}, {"ArticleId": 105512509, "Title": "A multi-view clustering algorithm for attributed weighted multi-edge directed networks", "Abstract": "<p>Graph clustering acts as a critical topic for solving decision situations in networks. Different node clustering methods for undirected and directed graphs have been proposed in the literature, but less attention has been paid to the case of attributed weighted multi-edge digraphs (AWMEDiG). Nowadays, multi-source and multi-attributed data are used increasingly in decision sciences; however, traditional methods usually consider single-attributed and single-view data as the input. This type of directed network, whose nodes are described by a list of attributes and directed links are viewed as directed multi-edge, is a new challenge to graph clustering. This paper proposes a new approach to detecting and evaluating clusters of AWMEDiG based on the maximum clique method. Our algorithm first converts the given AWMEDiG into a new weighted graph. This transformation is carried out through a new structural-attributed similarity measurement, an improved version of our previous model. Then, the concept of the maximum clique is adopted to complete the clustering task. The main objective of this new approach is to improve the clustering quality by grouping the highly cohesive and homogeneous vertices. So, the entropy strongly tends to be zero. Moreover, since the number of clusters is not predefined, it has the ability to find out the natural number of clusters within a graph. The performance of the proposed algorithm is tested on a synthetic, provided for a three-view attributed weighted directed network including 50 nodes, and two real-world datasets: UK faculty and Seventh graders. The clustering results are analyzed based on the clusters’ entropy and density, meanwhile the precision and F-score indexes measure the accuracy of them.</p>", "Keywords": "Weighted multi-edge graph; Attributed graph; Directed network; Similarity measurement; k-Clique clustering algorithm", "DOI": "10.1007/s00521-022-08086-4", "PubYear": 2023, "Volume": "35", "Issue": "10", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Mathematical Research, Universiti Putra Malaysia, Serdang, Malaysia; Department of Applied Mathematics, Faculty of Mathematical Sciences, Shahrood University of Technology, Shahrood, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Mathematical Research, Universiti Putra Malaysia, Serdang, Malaysia; Department of Mathematics, Faculty of Science, Universiti Putra Malaysia, Serdang, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Mathematical Research, Universiti Putra Malaysia, Serdang, Malaysia"}], "References": [{"Title": "Clustering of graphs using pseudo-guided random walk", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>;  <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "", "Page": "101281", "JournalTitle": "Journal of Computational Science"}, {"Title": "Discovering the maximum k-clique on social networks using bat optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "1", "JournalTitle": "Computational Social Networks"}, {"Title": "Fuzzy community detection on the basis of similarities in structural/attribute in large-scale social networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "2", "Page": "1373", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A fast local community detection algorithm in complex networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "6", "Page": "1929", "JournalTitle": "World Wide Web"}, {"Title": "Multi-objective optimization algorithm based on characteristics fusion of dynamic social networks for community discovery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "79", "Issue": "", "Page": "110", "JournalTitle": "Information Fusion"}, {"Title": "Transitive Fuzzy Similarity Multigraph-Based Model for Alternative Clustering in Multi-criteria Group Decision-Making Problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "5", "Page": "2569", "JournalTitle": "International Journal of Fuzzy Systems"}]}, {"ArticleId": 105512561, "Title": "Blockchain-Domain-Namen – <PERSON><PERSON><PERSON> braucht man die (im Metaverse)?", "Abstract": "", "Keywords": "", "DOI": "10.38023/72bd205e-c6fc-4ef3-9683-6edeccee8491", "PubYear": 2022, "Volume": "", "Issue": "20-Dezember-2022", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105512572, "Title": "The Agile Working Method for Legal Departments", "Abstract": "", "Keywords": "", "DOI": "10.38023/fc0c658d-1230-45ca-bf08-bf3113e2d678", "PubYear": 2022, "Volume": "", "Issue": "20-Dezember-2022", "JournalId": 77473, "JournalTitle": "Jusletter IT", "ISSN": "", "EISSN": "1664-848X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105512583, "Title": "Fully Connected Convolutional Neural Network in PCB Soldering Point Inspection", "Abstract": "In Electronics Manufacturing Services (EMS) industry, Printed Circuit Board (PCB) inspection is tricky and hard, especially for soldering point inspection due to the extremely tiny size and inconsistent appearance for uneven heating in reflow soldering process. Conventional computer vision technique based on OpenCV or Halcon usually cause false positive call for originally good soldering point on PCB because OpenCV or Halcon use the pre-defined threshold in color proportion for deciding whether the specific soldering point is OK or NG (not good). However, soldering point forms are various after heating in reflow soldering process. This paper puts forward a VGG structure deep convolutional neural network, which is named SolderNet for processing soldering point after reflow heating process to effectively inspect soldering point status, reduce omission rate and error rate, and increase first pass rate. SolderNet consists of 11 hidden convolution layers and 3 densely connected layers. Accuracy reports are divided into OK point recognition and NG point recognition. For OK soldering point recognition, 92% is achieved. For NG soldering point recognition, 99% is achieved. The dataset is collected from KAGA Co. Ltd Plant in Suzhou. First pass rate at KAGA plant is increased from 25% to 80% in general.", "Keywords": "Deep Learning;Soldering Point;Computer Vision;Pattern Recognition;Convolutional Neural Network;Printed Circuit Board;Electronics Manufacturing Services", "DOI": "10.4236/jcc.2022.1012005", "PubYear": 2022, "Volume": "10", "Issue": "12", "JournalId": 14102, "JournalTitle": "Journal of Computer and Communications", "ISSN": "2327-5219", "EISSN": "2327-5227", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shanghai Xiashu Intelligent Science Company Co., Ltd., Shanghai, China"}], "References": [{"Title": "IC solder joint inspection via generator-adversarial-network based template", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "4", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "IC solder joint inspection via generator-adversarial-network based template", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "4", "Page": "1", "JournalTitle": "Machine Vision and Applications"}]}, {"ArticleId": *********, "Title": "Mathematical and software for numerical modeling of the geomechanical state of the carbon mass", "Abstract": "Underground mining is associated with mine workings and subsequent arrangement of free space in accordance with the purpose of the designed object. Man-made impact on the massif leads to violation of the natural field of stresses and formation in the field of mining of zones of high mountain pressure and rock unloading, which can lead to mountain impacts, sudden emissions of coal, rock and gas, collapses. Analysis of results of mathematical simulation of stress-strain state of the developed section of the massif is necessary for detection and prevention of pre-accident situations. The results of experiments confirm that the stress field of undisturbed rocks, formed under the influence of natural forces, is determined by the relations of the theory of elasticity. Based on studies, it was found that under the influence of anthropogenic forces, a zone is formed around the developed space, in which the connection between stresses and deformations is non-linear. Therefore, taking into account the properties of rocks in the mathematical model, the deformation conditions of which are determined by nonlinear equations, will allow to obtain qualitatively new information on the stressed-deformed state of rocks in the field of influence of mine workings. The article proposes an approach for constructing a nonlinear mathematical model of the stress-strain state of the carbon mass, which takes into account the integral effect on rocks of natural and man-made forces. To study the proposed nonlinear model, the author developed a set of problem-oriented programs and presented the results of computational experiments. © 2022 Tomsk State University. All rights reserved.", "Keywords": "nonlinear mathematical model; rock mass; stress-strain state", "DOI": "10.17223/********/60/6", "PubYear": 2022, "Volume": "", "Issue": "60", "JournalId": 20377, "JournalTitle": "Vestnik Tomskogo gosudarstvennogo universiteta. Upravlenie, vychislitel'naya tekhnika i informatika", "ISSN": "1998-8605", "EISSN": "2311-2085", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Technical Sciences, The Department of Applied Mathematics and Informatics, Institute of Information Technologies and Automated Systems, Siberian State Industrial University, Novokuznetsk, Russian Federation"}], "References": []}, {"ArticleId": *********, "Title": "Analytical study of a single-line system queuing with incoming synchronous events flow", "Abstract": "In this article, we study a single-server queuing system with an incoming correlated synchronous doubly stochastic two-state event stream, which is a special case of the MAP events stream. The mathematical expectation of the duration of the intervals between the events of the stream has the form: (Formula Presented) The formula for the intensity of the considered synchronous flow is obtained: (Formula Presented) An analytical stationary probability distribution of process states (i, j) is derived, where i is the queue length in the system, i = 0,1,..., j, is the state of the synchronous events flow, j = 1,2, for three different cases: 1) p + q = 1, 2) 0 &lt; p + q &lt; 1, 3) 1 &lt; p + q ≤ 2, where p is the probability of transition of the synchronous flow from the first state (flow intensity in the first state λ) to the second (flow intensity in the second state λ2, λ1 &gt; λ); q is the probability of a synchronous flow transition from the second state (flow intensity in the second state λ2) to the first (flow intensity in the first state λ2). Numerical characteristics of the system are found explicitly: the probability of the system downtime P(-1), the average queue length in the system M (I ) and the average number of requests in the system M (I + 1) . Numerical calculations of characteristics with different initial data are given. The tables present the results of the dependence of the found numerical characteristics on the parameters of the investigated queuing system. © 2022 Authors. All rights reserved.", "Keywords": "numerical characteristics; single line queuing system (QS); stationary probability distribution of system states; synchronous flow of events (requests, messages)", "DOI": "10.17223/********/59/4", "PubYear": 2022, "Volume": "", "Issue": "59", "JournalId": 20377, "JournalTitle": "Vestnik Tomskogo gosudarstvennogo universiteta. Upravlenie, vychislitel'naya tekhnika i informatika", "ISSN": "1998-8605", "EISSN": "2311-2085", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Technical Sciences, Department of PM IPMKN, National Research Tomsk State University, Tomsk, Russian Federation"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Research Tomsk State University, Tomsk, Russian Federation"}], "References": []}, {"ArticleId": 105512658, "Title": "Think Twice: First for Tech, Then for Ed", "Abstract": "<p>The embodiment of technology in education can make learning easier, more enjoyable, and more accessible. From Learning Machines to artificial intelligence (AI), educational technology has repeatedly tested its strength as an aider or a substitute to in-person teaching. During the COVID-19 pandemic international organisations promoted the idea of the transformation of education using technology. Comparison of their texts published in 2020 with texts published in 2021 indicates that much of the early enthusiasm concerning the transition from in-person to remote learning gave its position to more thoughtful accounts after considering the learning losses and students' disappointment from the disruption of in-person relationships. This publication highlights aspects of education technology usually overlooked in futuristic accounts of education. Adopting a non-deterministic view of technology attempts to contribute to the more human-centred incorporation of technologies in education.</p><p>© The Author(s) 2022.</p>", "Keywords": "Blended learning;COVID-19;Face-to-face education;Online learning;Technological determinism;Technology driven change", "DOI": "10.1007/s42979-022-01538-7", "PubYear": 2023, "Volume": "4", "Issue": "2", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of West Attica, Athens, Greece."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronic Engineering, University of West Attica, Athens, Greece."}], "References": []}, {"ArticleId": *********, "Title": "Evolutionary computation-based machine learning for Smart City high-dimensional Big Data Analytics", "Abstract": "Science and technology development promotes Smart City Construction (SCC) as a most imminent problem. This work aims to improve the comprehensive performance of the Smart City-oriented high-dimensional Big Data Management (BDM) platform and promote the far-reaching development of SCC. It comprehensively optimizes the calculation process of the BDM platform through Machine Learning (ML), reduces the dimension of the data, and improves the calculation effect. To this end, this work first introduces the concept of SCC and the BDM platform application design. Then, it discusses the design concept of using ML technology to optimize the calculation effect of the BDM platform. Finally, the Tensor Train Support Vector Machine (TT-SVM) model is designed based on dimension reduction data processing. The proposed model can comprehensively optimize the BDM platform, and the model is compared with other models and evaluated. The research results show that the accuracy of the reduced dimension classification of the TT-SVM model is more than 95. The lowest average processing time for the model’s reduced dimension classification is about 1ms. The model’s highest data processing accuracy is about 98%, and the average processing time is between 1.0–1.5ms. Compared with traditional models and BDM platforms, the proposed model has a breakthrough performance improvement, so it plays an important role in future SCC. This work has achieved a great breakthrough in big data processing, and innovatively improved the application mode of high-dimensional big data technology by integrating multiple technologies. Therefore, the finding provides targeted technical reference for algorithms in BDM platform and contributes to the construction and improvement of Smart City.", "Keywords": "", "DOI": "10.1016/j.asoc.2022.109955", "PubYear": 2023, "Volume": "133", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Institute for Smart Cities, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Shenzhen Key Laboratory of Digital Twin Technologies for Cities, Shenzhen 518060, China;Guangdong–Hong Kong-Macau Joint Laboratory for Smart Cities, Shenzhen 518060, China;Shenzhen Key Laboratory of Spatial Smart Sensing and Services, Shenzhen 518060, China;MNR Technology Innovation Center of Territorial & Spatial Big Data, Shenzhen 518060, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Research Institute for Smart Cities, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Shenzhen Key Laboratory of Digital Twin Technologies for Cities, Shenzhen 518060, China;Guangdong–Hong Kong-Macau Joint Laboratory for Smart Cities, Shenzhen 518060, China;Shenzhen Key Laboratory of Spatial Smart Sensing and Services, Shenzhen 518060, China;MNR Technology Innovation Center of Territorial & Spatial Big Data, Shenzhen 518060, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical Engineering and Computer Science, Ningbo University, Ningbo, 315211, China"}, {"AuthorId": 4, "Name": "Wuyang Hong", "Affiliation": "Research Institute for Smart Cities, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Shenzhen Key Laboratory of Digital Twin Technologies for Cities, Shenzhen 518060, China;Guangdong–Hong Kong-Macau Joint Laboratory for Smart Cities, Shenzhen 518060, China;Shenzhen Key Laboratory of Spatial Smart Sensing and Services, Shenzhen 518060, China;MNR Technology Innovation Center of Territorial & Spatial Big Data, Shenzhen 518060, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Research Institute for Smart Cities, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Shenzhen Key Laboratory of Digital Twin Technologies for Cities, Shenzhen 518060, China;Guangdong–Hong Kong-Macau Joint Laboratory for Smart Cities, Shenzhen 518060, China;Shenzhen Key Laboratory of Spatial Smart Sensing and Services, Shenzhen 518060, China;MNR Technology Innovation Center of Territorial & Spatial Big Data, Shenzhen 518060, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON>z<PERSON> Xia", "Affiliation": "Research Institute for Smart Cities, School of Architecture and Urban Planning, Shenzhen University, Shenzhen 518060, China;Shenzhen Key Laboratory of Digital Twin Technologies for Cities, Shenzhen 518060, China;Guangdong–Hong Kong-Macau Joint Laboratory for Smart Cities, Shenzhen 518060, China;Shenzhen Key Laboratory of Spatial Smart Sensing and Services, Shenzhen 518060, China;MNR Technology Innovation Center of Territorial & Spatial Big Data, Shenzhen 518060, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>han Lv", "Affiliation": "Department of Game design, Faculty of Arts, Uppsala University, Sweden"}], "References": [{"Title": "Big data management in participatory sensing: Issues, trends and future directions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "942", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Big data analytics on enterprise credit risk evaluation of e-Business platform", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "3", "Page": "311", "JournalTitle": "Information Systems and e-Business Management"}, {"Title": "Big data and stream processing platforms for Industry 4.0 requirements mapping for a predictive maintenance use case", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "138", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "A comprehensive survey and taxonomy of the SVM-based intrusion detection systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "102983", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Machine Learning: Algorithms, Real-World Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Machine learning and deep learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "3", "Page": "685", "JournalTitle": "Electronic Markets"}, {"Title": "OBPP: An ontology-based framework for privacy-preserving in IoT-based smart city", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "123", "Issue": "", "Page": "1", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Smart city as a smart service system: Human-computer interaction and smart city surveillance systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "124", "Issue": "", "Page": "106923", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Machine learning‐based model for prediction of power consumption in smart grid‐ smart way towards smart city", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "5", "Page": "e12832", "JournalTitle": "Expert Systems"}]}, {"ArticleId": 105512681, "Title": "Software Implementation Solutions of A Lightweight Block Cipher to Secure Restricted IoT Environment: A Review", "Abstract": "With the development of the Internet of Things (IoT) technology, IoT devices are integrated into many of our daily lives, including industrial, security, medical, and personal applications. Many violations of IoT safety have appeared due to the critical physical infrastructure, and network vulnerabilities. Considering the nature of the restricted and limited resources of these devices in terms of size, capacity, and energy, Security is becoming increasingly important. Lightweight cryptography is one of the directions that offer security solutions in resource-constrained environments such as Radio-frequency identification (RFID) and wireless sensor network (WSN).This paper discusses the security issues of these resource-constrained IoT devices and reviews the most prominent Lightweight Bock Cipher suitable for software implementation. Through studying thespecifications and the inner structure for each cipher and their implementation of the performance evaluation on some kind of platform, we provide a design strategies guideline for cryptographic developers to design improved Lightweight Block cipher solutions and compact software implementation for resource-constrained environments.", "Keywords": "", "DOI": "10.33899/csmj.2022.176594", "PubYear": 2022, "Volume": "16", "Issue": "2", "JournalId": 72797, "JournalTitle": "AL-Rafidain Journal of Computer Sciences and Mathematics", "ISSN": "", "EISSN": "2311-7990", "Authors": [{"AuthorId": 1, "Name": "RUAH AL_AZZAWI", "Affiliation": "University of Mosul / Computer and Internet Center"}, {"AuthorId": 2, "Name": "SUFYAN AL-DABBAGH", "Affiliation": "Department of Computer Science, College of computer science and mathematics, Mosul University"}], "References": []}, {"ArticleId": 105512810, "Title": "FedPOIRec: Privacy-preserving federated poi recommendation with social influence", "Abstract": "With the growing number of Location-Based Social Networks, privacy-preserving point-of-interest (POI) recommendation has become a critical challenge when helping users discover potentially interesting new places. Traditional systems take a centralized approach that requires the transmission and collection of private user data. In this work, we present FedPOIRec, a privacy-preserving federated learning approach enhanced with features from user social circles to generate top- N POI recommendations. First, the FedPOIRec framework is built on the principle that local data never leave the owner’s device, while a parameter server blindly aggregates the local updates. Second, the local recommender results are personalized by allowing users to exchange their learned parameters, enabling knowledge transfer among friends. To this end, we propose a privacy-preserving protocol for integrating the preferences of the user’s friends, after the federated computation, by exploiting the properties of the Cheon-Kim-Kim-Song (CKKS) fully homomorphic encryption scheme. To evaluate FedPOIRec, we apply our approach to five real-world datasets using two recommendation models. Extensive experiments demonstrate that FedPOIRec achieves comparable recommendation quality to centralized approaches, while the social integration protocol incurs low computation and communication overhead on the user device.", "Keywords": "", "DOI": "10.1016/j.ins.2022.12.024", "PubYear": 2023, "Volume": "623", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute for Language and Speech Processing, Athena Research Center, Kimmeria, Xanthi 67100, Greece;Department of Electrical and Computer Engineering, Democritus University of Thrace, Kimmeria, Xanthi 67100, Greece;Corresponding author at: Institute for Language and Speech Processing, Athena Research Center, Kimmeria, Xanthi 67100, Greece"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Language and Speech Processing, Athena Research Center, Kimmeria, Xanthi 67100, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Language and Speech Processing, Athena Research Center, Kimmeria, Xanthi 67100, Greece;Department of Electrical and Computer Engineering, Democritus University of Thrace, Kimmeria, Xanthi 67100, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Language and Speech Processing, Athena Research Center, Kimmeria, Xanthi 67100, Greece;Department of Electrical and Computer Engineering, Democritus University of Thrace, Kimmeria, Xanthi 67100, Greece"}], "References": [{"Title": "PGAS: Privacy-preserving graph encryption for accurate constrained shortest distance queries", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "506", "Issue": "", "Page": "325", "JournalTitle": "Information Sciences"}, {"Title": "Providing privacy preserving in next POI recommendation for Mobile edge computing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Privacy-preserving point-of-interest recommendation based on geographical and social influence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "202", "JournalTitle": "Information Sciences"}, {"Title": "Practical Privacy Preserving POI Recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "A survey on federated learning", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "216", "Issue": "", "Page": "106775", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An attention‐based category‐aware GRU model for the next POI recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "7", "Page": "3174", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "PREFER", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "PFLM: Privacy-preserving federated learning with membership proof", "Authors": "<PERSON><PERSON><PERSON> Jiang; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "288", "JournalTitle": "Information Sciences"}, {"Title": "Sequential-Knowledge-Aware Next POI Recommendation: A Meta-Learning Approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Fairness and accuracy in horizontal federated learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "589", "Issue": "", "Page": "170", "JournalTitle": "Information Sciences"}, {"Title": "CORK: A privacy-preserving and lossless federated learning scheme for deep neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "603", "Issue": "", "Page": "190", "JournalTitle": "Information Sciences"}, {"Title": "Dynamically aggregating individuals’ social influence and interest evolution for group recommendations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "614", "Issue": "", "Page": "223", "JournalTitle": "Information Sciences"}, {"Title": "RTGA: Robust ternary gradients aggregation for federated learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "616", "Issue": "", "Page": "427", "JournalTitle": "Information Sciences"}, {"Title": "Heterogeneity-aware fair federated learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "619", "Issue": "", "Page": "968", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 105512813, "Title": "Spatiotemporal modeling to predict soil moisture for sustainable smart irrigation", "Abstract": "In this paper, we have proposed spatiotemporal soil moisture modeling to improve understanding of soil moisture and variability for sustainable irrigation practices. Increasing population growth and climatic changes have intensified the need to implement effective measures to conserve water in irrigation practices. Effective irrigation in parkland influences the overall plant growth processes including the final appearance of the plants. Furthermore, over-irrigation and poor water management in parks and gardens lead to wastage of water which may result in seepage, runoff, and leaching of nutrients into nearby streams. Conversely, under-irrigation results in reduced plant growth and unappealing appearance. Thus, appropriate irrigation management is required to maintain a lush green landscape. Due to large variability in soil properties, environmental conditions and landscape features, the soil moisture level might not be uniformly distributed within a given landscape. Hence, it is essential to understand the soil moisture distribution in the field. There are few existing soil moisture modeling exist but these modelings have not considered multiple vertical depth and time domain. Using Internet of Things(IoT) enabled Cyber–physical system and machine learning techniques, this paper has presented a soil moisture modeling for multiple vertical soil depth for a robust understanding of soil’s and plants need for sustainable sprinkling of the water. The proposed model has used both statistical and deep learning techniques to understand moisture variability in both vertical depth and time. The proposed model has used Correlation Index (CI) to understand multi depth moisture variability on the influence of soil types and local weather parameters, such as precipitation and temperature. To predict moisture data in multi depth, the model has evaluated the use of both statistical machine learning models, such as Support vector regression(SVR) and linear regression (LR), and deep learning models, such as Long Short-Term Memory (LSTM) using seasonal and non-seasonal dataset. The experiment has revealed closely related variability pattern between wind speed and soil moisture in multi depth whereas soil type shows a loosely related variability pattern with moisture in higher depth. With these diverse datasets, the proposed machine learning model has achieved almost 90% success rate to predict moisture content in higher depth using data of lower depth to reduce cumbersome sensors array deployment in higher depth for live moisture visualization. This proposed multiple depth and time domain-based model can enable a smart water dispersing system to conserve water and make a positive contribution to a sustainable future.", "Keywords": "Smart irrigation ; IoT ; Artificial intelligence ; Soil moisture ; Dual-EM ; Electromagnetic survey", "DOI": "10.1016/j.iot.2022.100671", "PubYear": 2023, "Volume": "21", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Intelligent Systems (CIS), School of Engineering and Technology, Central Queensland University, Rockhampton, 4700, Queensland, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "Biplob Ray", "Affiliation": "Centre for Intelligent Systems (CIS), School of Engineering and Technology, Central Queensland University, Rockhampton, 4700, Queensland, Australia;Institute for Future Farming Systems, Central Queensland University, Rockhampton, 4700, Queensland, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Health, Medical and Applied Sciences, Central Queensland University, Rockhampton, 4700, Queensland, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Health, Medical and Applied Sciences, Central Queensland University, Rockhampton, 4700, Queensland, Australia"}], "References": []}, {"ArticleId": *********, "Title": "Buyers versus borrowers: a look at the finances of Shakespeare and Company", "Abstract": "<p><PERSON> is known for her ‘imperfect record keeping’ and often indecipherable business accounts (<PERSON><PERSON>, <PERSON><PERSON> (1985). <PERSON> and the Lost Generation: A History of Literary Paris in the Twenties and Thirties. New York: Norton, p. 161). That said, data from her lending library cards and logbooks provide key insight into Shakespeare and Company’s cash flow. While this financial information is not always presented in a systematic or exhaustive manner, it can still be used to develop a more nuanced understanding of the inner workings of Beach’s literary enterprise. To that end, in this article, I use the Shakespeare and Company Project data sets to examine the exchange of books in Beach’s bookshop. More specifically, my analysis centers on the details surrounding purchases and borrows of books from the events data set. By limiting the scope of my study to only those records that contain transactional data—whether in the form of membership fees or actual book purchases—I provide a case study of how to approach incomplete data that unearths a new array of networks that were central to the daily operations of Shakespeare and Company. As a result, in contrast with the common focus on solely the most notable lending library members (or members of the Lost Generation in general), this financial approach brings to light invisible networks and underexplored figures whose monetary contributions were essential to keeping Beach’s business afloat.</p>", "Keywords": "", "DOI": "10.1093/llc/fqac082", "PubYear": 2023, "Volume": "38", "Issue": "2", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Romance Languages, University of Georgia , Athens, GA, USA"}], "References": []}, {"ArticleId": *********, "Title": "How Feature Changes of a Dominant Ad Platform Shape Advertisers’ Human Agency", "Abstract": "Businesses are increasingly delegating activities in the advertising process to dominant online advertising platforms. This delegation yields the ad platforms tremendous power, akin to the principal–agent dilemma discussed in economics. One of the major platforms is called Google Ads—this platform is the focal point of our study. Over the years, Google has made substantial changes to its platform’s features, which, in turn, govern what is possible and what is not for the advertisers. These changes impact the advertisers’ ability to act independently and make their own choices , referred to as human agency . To better understand this impact, we examined 362 industry news articles reporting changes in Google Ads from 2015 to 2020. The findings indicate that while most changes increase human agency, this effect is becoming weaker over time, driven by automation. To better understand advertisers’ attitudes towards automation, we surveyed 193 advertisers with Google Ads experience. Contrary to the popular belief that marketers are afraid of being replaced by algorithms, we found this to not be the case. Even though most advertisers indicated appreciation for maintaining their human agency, they did not perceive this agency being violated by the ad platform. However, we did observe interesting variability among respondents, reflected in three computational advertising attitude types: tinkerers, instrumentalists , and shepherds . We discuss the implications for advertisers in terms of strategizing in the face of reduced human agency and for ad platforms in terms of designing features that advertisers perceive as fair.", "Keywords": "", "DOI": "10.1080/10864415.2022.2158594", "PubYear": 2023, "Volume": "27", "Issue": "1", "JournalId": 383, "JournalTitle": "International Journal of Electronic Commerce", "ISSN": "1086-4415", "EISSN": "1557-9301", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Optimal Keywords Grouping in Sponsored Search Advertising Under Uncertain Environments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "1", "Page": "107", "JournalTitle": "International Journal of Electronic Commerce"}, {"Title": "“It wouldn't happen to me”: Privacy concerns and perspectives following the Cambridge Analytica scandal", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "102498", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 105513061, "Title": "Fast and Accurate Deep Learning Model for Stamps Detection for Embedded Devices", "Abstract": "<p>The search for stamps on images is necessary to verify the authenticity of a document and extract valuable textual information contained in them. Despite the vast number of methods for detecting stamps, most of them are not universal and are limited either by the characteristics of the stamp or by the computational complexity of the approach. The last criterion is extremely relevant, since document recognition is actively transferred from server solutions to mobile devices with low computing power and limited battery life. In view of this, in this paper we propose a compact and computationally efficient neural network model for searching for stamps of any shape and color. Based on the popular neural network detector YOLO, our model contains only 128 thousand parameters. We demonstrate the effectiveness of the model in an experiment on the public dataset SPODS (scanned pseudo-official dataset), containing high-resolution scans of documents with stamps.</p>", "Keywords": "deep learning; object detection; stamp detection; document processing; neural networks optimization", "DOI": "10.1134/S1054661822040046", "PubYear": 2022, "Volume": "32", "Issue": "4", "JournalId": 20107, "JournalTitle": "Pattern Recognition and Image Analysis", "ISSN": "1054-6618", "EISSN": "1555-6212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Smart Engines Service LLC, Moscow, Russia; Federal Research Center “Computer Science and Control” of the Russian Academy of Sciences, Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Smart Engines Service LLC, Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Smart Engines Service LLC, Moscow, Russia; Federal Research Center “Computer Science and Control” of the Russian Academy of Sciences, Moscow, Russia"}], "References": [{"Title": "Evolution of the Viola-Jones Object Detection Method: A Survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "52", "JournalTitle": "Bulletin of the South Ural State University. Series \"Mathematical Modelling, Programming and Computer Software\""}, {"Title": "Detection of objects in the images: from likelihood relationships towards scalable and efficient neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "46", "Issue": "1", "Page": "139", "JournalTitle": "Computer Optics"}]}, {"ArticleId": 105513317, "Title": "Near real-time detection of winter cover crop termination using harmonized Landsat and Sentinel-2 (HLS) to support ecosystem assessment", "Abstract": "Cover crops are planted to reduce soil erosion, increase soil fertility, and improve watershed management. In the Delmarva Peninsula of the eastern United States, winter cover crops are essential for reducing nutrient and sediment losses from farmland. Cost-share programs have been created to incentivize cover crops to achieve conservation objectives. This program required that cover crops be planted and terminated within a specified time window. Usually, farmers report cover crop termination dates for each enrolled field (∼28,000 per year), and conservation district staff confirm the report with field visits within two weeks of termination. This verification process is labor-intensive and time-consuming and became restricted in 2020–2021 due to the COVID-19 pandemic. This study used Harmonized Landsat and Sentinel-2 (HLS, version 2.0) time-series data and the within-season termination (WIST) algorithm to detect cover crop termination dates over Maryland and the Delmarva Peninsula. The estimated remote sensing termination dates were compared to roadside surveys and to farmer-reported termination dates from the Maryland Department of Agriculture database for the 2020–2021 cover crop season. The results show that the WIST algorithm using HLS detected 94% of terminations (statuses) for the enrolled fields (n = 28,190). Among the detected terminations, about 49%, 72%, 84%, and 90% of remote sensing detected termination dates were within one, two, three, and four weeks of agreement to farmer-reported dates, respectively. A real-time simulation showed that the termination dates could be detected one week after termination operation using routinely available HLS data, and termination dates detected after mid-May are more reliable than those from early spring when the Normalized Difference Vegetation Index (NDVI) was low. We conclude that HLS imagery and the WIST algorithm provide a fast and consistent approach for generating near-real-time cover crop termination maps over large areas, which can be used to support cost-share program verification.", "Keywords": "Winter cover crop ; Agroecosystem service ; Time series ; Harmonized Landsat and Sentinel-2 (HLS) ; Near-real-time ; Phenology", "DOI": "10.1016/j.srs.2022.100073", "PubYear": 2023, "Volume": "7", "Issue": "", "JournalId": 72499, "JournalTitle": "Science of Remote Sensing", "ISSN": "2666-0172", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "USDA, Agricultural Research Service, Hydrology and Remote Sensing Laboratory, Beltsville, MD, 20705, USA;Corresponding author. USDA-ARS Hydrology & Remote Sensing Lab, Bldg. 007, BARC-WEST, Rm. 101, 10300 Baltimore Ave., Beltsville, MD, 20705, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "USDA, Agricultural Research Service, Sustainable Agricultural Systems Laboratory, Beltsville, MD, 20705, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "U.S. Geological Survey, Lower Mississippi-Gulf Water Science Center, Beltsville, MD, 20705, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "U.S. Geological Survey, Maryland-Delaware-District of Columbia Water Science Center, Catonsville, MD, 21228, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "USDA, Agricultural Research Service, Sustainable Agricultural Systems Laboratory, Beltsville, MD, 20705, USA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Office of Resource Conservation, Maryland Department of Agriculture, Annapolis, MD, 21401, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Office of Resource Conservation, Maryland Department of Agriculture, Annapolis, MD, 21401, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "USDA, Agricultural Research Service, Sustainable Agricultural Systems Laboratory, Beltsville, MD, 20705, USA"}, {"AuthorId": 9, "Name": "Uvir<PERSON><PERSON>", "Affiliation": "USDA, Agricultural Research Service, Hydrology and Remote Sensing Laboratory, Beltsville, MD, 20705, USA"}], "References": [{"Title": "A within-season approach for detecting early growth stages in corn and soybean using high temporal and spatial resolution imagery", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "242", "Issue": "", "Page": "111752", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Using NASA Earth observations and Google Earth Engine to map winter cover crop conservation performance in the Chesapeake Bay watershed", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "248", "Issue": "", "Page": "111943", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 105513354, "Title": "Route Planning for Autonomous Mobile Robots Using a Reinforcement Learning Algorithm", "Abstract": "<p>This research suggests a new robotic system technique that works specifically in settings such as hospitals or emergency situations when prompt action and preserving human life are crucial. Our framework largely focuses on the precise and prompt delivery of medical supplies or medication inside a defined area while avoiding robot collisions or other obstacles. The suggested route planning algorithm (RPA) based on reinforcement learning makes medical services effective by gathering and sending data between robots and human healthcare professionals. In contrast, humans are kept out of the patients’ field. Three key modules make up the RPA: (i) the Robot Finding Module (RFM), (ii) Robot Charging Module (RCM), and (iii) Route Selection Module (RSM). Using such autonomous systems as RPA in places where there is a need for human gathering is essential, particularly in the medical field, which could reduce the risk of spreading viruses, which could save thousands of lives. The simulation results using the proposed framework show the flexible and efficient movement of the robots compared to conventional methods under various environments. The RSM is contrasted with the leading cutting-edge topology routing options. The RSM’s primary benefit is the much-reduced calculations and updating of routing tables. In contrast to earlier algorithms, the RSM produces a lower AQD. The RSM is hence an appropriate algorithm for real-time systems.</p>", "Keywords": "", "DOI": "10.3390/act12010012", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Learning and Information Retrieval Department, Faculty of Artificial Intelligence, Kafrelsheikh University, Kafrelsheikh 33511, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering and Control Systems Department, Faculty of Engineering, Mansoura University, Mansoura 35516, Egypt; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Communications and Electronics, Delta Higher Institute of Engineering and Technology, Mansoura 35111, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Computer Science, College of Computing and Information Technology, Shaqra University, Shaqra 11961, Saudi Arabia↑Department of Computer Science, Faculty of Computer and Information Sciences, Ain Shams University, Cairo 11566, Egypt"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Software Engineering, Faculty of Engineering, Misr University for Science and Technology (MUST), 6th of October City 3236101, Egypt"}], "References": [{"Title": "Hybrid Path Planning Based on Safe A* Algorithm and Adaptive Window Approach for Mobile Robot in Large-Scale Dynamic Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "99", "Issue": "1", "Page": "65", "JournalTitle": "Journal of Intelligent & Robotic Systems"}]}, {"ArticleId": 105513521, "Title": "IPFS based storage Authentication and access control model with optimization enabled deep learning for intrusion detection", "Abstract": "Network security has benefited from intrusion detection, which may spot unexpected threats from network traffic. Modern methods for detecting network anomalies typically rely on conventional machine learning models. The human construction of traffic features that these systems mainly rely on, which is no longer relevant in the age of big data, results in relatively low accuracy and certain exceptional features. A storage authentication and access control model based on Interplanetary File System (IPFS) and a network intrusion detection system based on Chronological Anticorona Virus Optimization are hence the main goals of this research (CACVO-based DRN).The setup, user registration, initialization, data encryption and storage, authentication, testing, access control, and decryption stages are used here to perform the blockchain authentication and access control. After then, DRN is used to perform network intrusion detection. To do this, the recorded data log file is initially sent to the feature fusion module, which uses Deep Belief Network and hybrid correlation factors (DBN). After the feature fusion is complete, the proposed optimization technique, CACVO, which was recently developed by fusing the Chronological Concept with Anti Corona virus Optimization (ACVO) algorithm, is used to perform intrusion detection utilizing DRN. The experimental outcome shows that, based on the f-measure value of 0.939 and 0.938, respectively, the developed model achieved greater performance.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2022.103369", "PubYear": 2023, "Volume": "176", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, St<PERSON> College of Engineering, Sriperumbudur, Chennai 602117, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Saveetha Engineering College, Thandalam, Chennai 602105, India"}], "References": [{"Title": "A network intrusion detection method based on semantic Re-encoding and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "164", "Issue": "", "Page": "102688", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Network Intrusion Detection System using Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "239", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 105513579, "Title": "Rates of weighted statistical convergence for a generalization of positive linear operators", "Abstract": "In the present paper, some direct and inverse theorems relating to a generalization of positive linear operators are given. Also some rates of weighted statistical convergence are computed by means of a weighted modulus of continiuty.", "Keywords": "Sequence of positive linear operators; modulus of continuity; weighted modulus of continuity; <PERSON><PERSON><PERSON>'s K-functional; Zygmund modulus", "DOI": "10.3934/mfc.2022059", "PubYear": 2023, "Volume": "6", "Issue": "3", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>han <PERSON>atan Ilbey", "Affiliation": "Baskent University, Kahramankazan Vocational School, Kahramankazan 06980, Ankara, Turkey"}, {"AuthorId": 2, "Name": "<PERSON>g<PERSON><PERSON>", "Affiliation": "Gazi University, Faculty of Science, Department of Mathematics, Teknik Okullar 06500, Ankara, Turkey"}], "References": [{"Title": "Approximation of functions and Mihesan operators", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "3", "Page": "369", "JournalTitle": "Mathematical Foundations of Computing"}]}, {"ArticleId": 105513716, "Title": "Hybridization of Swarm for Features Selection to Modeling Heart Attack Data", "Abstract": "Predicting heart attacks using machine learning is an important topic. Medical data sets contain different features, some of which are related to the target group for prediction and some are not. In addition, the data sets are excessively unbalanced, which leads to the bias of machine learning models when modeling heart attacks. To model the unbalanced heart attack data set, this paper proposes the hybridization of Particle swarm optimization (PSO), BAT, and Cuckoo Search (CS) to select the features and adopt the precision for minority classes as a fitness function for each swarm to select the influential features. In order to model the data, set in which the features were selected, it was proposed to use the boosting (Catboost) as a classifier for predicting heart attacks. The proposed method to select features has been compared with each of the three swarms, and the Catboost algorithm has been compared to traditional classification algorithms (naive Bayes, decision trees). The study found that the proposed method of hybridization of the results of the (PSO,  BAT, and BCS) algorithms in selecting features is a promising solution in the field of selecting features and increases the accuracy of the system, and that traditional machine learning models are biased in the case of unbalanced data sets and that selecting the important features according to the target class has an impact on the performance of the models, In addition, the definition of hyperparameters reduces the bias of the selected model. The final model achieved anoverall accuracy of 96% on the Accuracy scale and 56% on the Precision scale for the minority class</p>", "Keywords": "", "DOI": "10.33899/csmj.2022.176587", "PubYear": 2022, "Volume": "16", "Issue": "2", "JournalId": 72797, "JournalTitle": "AL-Rafidain Journal of Computer Sciences and Mathematics", "ISSN": "", "EISSN": "2311-7990", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Sciences and Mathematics University of Mosul, Mosul, Iraq"}], "References": []}, {"ArticleId": 105513822, "Title": "An efficient online citrus counting system for large‐scale unstructured orchards based on the unmanned aerial vehicle", "Abstract": "<p>The accurate detection and counting of fruits in natural environments are key steps for the early yield estimation of orchards and the realization of smart orchard production management. However, existing citrus counting algorithms have two primary limitations: the performance of counting algorithms needs to be improved, and their system operation efficiency is low in practical applications. Therefore, in this paper, we propose a novel end-to-end orchard fruit counting pipeline that can be used by multiple unmanned aerial vehicles (UAVs) in parallel to help overcome the above problems. First, to obtain on-board camera images online, an innovative UAV live broadcast platform was developed for the orchard scene. Second, for this challenging specific scene, a detection network named Citrus-YOLO was designed to detect fruits in the video stream in real-time. Then, the DeepSort algorithm was used to assign a specific ID to each citrus fruit in the online UAV scene and track the fruits across video frames. Finally, a nonuniform distributed counter was proposed to correct the fruit count during the tracking process, and this can significantly reduce the counting errors caused by tracking failure. This is the first work to realize online and end-to-end counting in a field orchard environment. The experimental results show that the F1 score and mean absolute percentage error of the method are 89.07% and 12.75%, respectively, indicating that the system can quickly and accurately achieve fruit counting in large-scale unstructured citrus orchards. Although our work is discussed in the context of fruit counting, it can be extended to the detection, tracking and counting of a variety of other objects of interest in UAV application scenarios</p>", "Keywords": "citrus;counting system;DeepSort;online;UAV;YOLOV5", "DOI": "10.1002/rob.22147", "PubYear": 2023, "Volume": "40", "Issue": "3", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, College of Mathematics and Informatics South China Agricultural University Guangzhou China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, College of Mathematics and Informatics South China Agricultural University Guangzhou China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering, College of Mathematics and Informatics South China Agricultural University Guangzhou China"}, {"AuthorId": 4, "Name": "Zexing Li", "Affiliation": "Department of Information Engineering, College of Mathematics and Informatics South China Agricultural University Guangzhou China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering, College of Mathematics and Informatics South China Agricultural University Guangzhou China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering, College of Mathematics and Informatics South China Agricultural University Guangzhou China"}, {"AuthorId": 7, "Name": "Yonglin Han", "Affiliation": "Department of Information Engineering, College of Mathematics and Informatics South China Agricultural University Guangzhou China"}], "References": [{"Title": "A comparative study of fruit detection and counting methods for yield mapping in apple orchards", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "263", "JournalTitle": "Journal of Field Robotics"}, {"Title": "A comparative study of fruit detection and counting methods for yield mapping in apple orchards", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "263", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 105513866, "Title": "A New Approach Based on Intelligent Method to Classify Quality of Service", "Abstract": "Computer networks are used more frequently for time-sensitive applications like voice over internet protocol and other communications. In computer networks, quality of service (QoS) can be crucial since it makes it easier to assess a network's performance and offers mechanisms for enhancing its performance. As a result, understanding the QoS offered by networks is essential for both network users and network service providers in order to assess how well the transmission requirements of different applications are satisfied and to implement improvements to network performance. Next-generation monitoring systems must not only detect network performance deterioration instantly but also pinpoint the underlying cause of quality of service problems in order to achieve strict network standards. A brand-new fuzzy logic-based algorithm is suggested as a solution to this issue. Thus, the proposed approach was evaluated and compared with probabilistic neural networks (PNN) and Bayesian classification as well as network performance measurement, latency, jitter, and packet loss. All approaches correctly classified the QoS categories, although generally, the fuzzy approach outperformed PNN and Bayesian. An improved comprehension of the network performance is acquired by precisely determining its QoS.", "Keywords": "Bayesian; Fuzzy Logic; Network Performance; Probabilistic Neural Network; Quality of service", "DOI": "10.31449/inf.v46i4.4323", "PubYear": 2022, "Volume": "46", "Issue": "9", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "Dhuha <PERSON><PERSON>", "Affiliation": "Department of Computer Science, Shatt Al-Arab University College, Basra, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technology Engineering, Al-Kunooze University College, Basrah, IRAQ"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer Science and Information Technology, University of Basrah, Basrah, IRAQ"}], "References": []}, {"ArticleId": 105513914, "Title": "Length-Based Curriculum Learning for Efficient Pre-training of Language Models", "Abstract": "<p>Recently, pre-trained language models (PLMs) have become core components in a wide range of natural language processing applications. However, PLMs like BERT and RoBERTa are typically trained with a large amount of unlabeled text corpora which requires extremely high computational cost. Curriculum learning (CL) is a learning strategy for training a model from easy samples to hard ones that has potential to alleviate this problem. Nevertheless, how to determine the difficulty measure of training samples for PLMs and an effective training scheduler are still open questions. In this study, we focus on the length of input text as the difficulty measure and propose a new CL approach called length-based CL. We analyze the effectiveness of the length-based difficulty measure in terms of convergence speed and GLUE scores using a limited amount of corpus. By combining maximum available batch size with the length-based difficulty measure, we show that our length-based CL model can achieve 1.5 times faster convergence speed in pre-training and better performances on downstream tasks. Furthermore, we expand the corpus to evaluate various pacing functions (training schedulers) for the length-based CL with respect to the computational time and generalization performance. Through experiments with a larger corpus, we find that our proposed Square scheduler achieved less computational time in pre-training and obtained the best generalization performance on downstream tasks.</p>", "Keywords": "Curriculum learning (CL); Pre-trained language models (PLMs); Length-based CL; Natural language processing (NLP)", "DOI": "10.1007/s00354-022-00198-8", "PubYear": 2023, "Volume": "41", "Issue": "1", "JournalId": 6245, "JournalTitle": "New Generation Computing", "ISSN": "0288-3635", "EISSN": "1882-7055", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Soka University, Hachioji-shi, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Soka University, Hachioji-shi, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Soka University, Hachioji-shi, Japan"}], "References": [{"Title": "Curriculum Learning: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "6", "Page": "1526", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 105513931, "Title": "Hydrogen sulfide activatable NIR-II fluorescent probe for highly specific imaging of breast cancer", "Abstract": "Breast cancer has the highest incidence rate and one of the highest mortality rates for malignant tumors in women. Early detection of breast cancer is essential to improve patient survival and quality of life. To this end, the present study sought to develop a second near-infrared (NIR-II) fluorescent probe ( BP-A ) to detect breast cancer by monitoring the concentration changes of H<sub>2</sub>S, whose intracellular levels are closely related to both the proliferation and death of breast cancer cells. BP-A exhibited a large Stokes shift, fast response time, and excellent NIR-II emission at 1032 nm, and was able to sensitively and specifically detect exogenous and endogenous H<sub>2</sub>S in living mice. BP-A also displayed strong NIR-II fluorescence in tumor-bearing mice and no fluorescence in healthy mice, exhibiting excellent ability to distinguish between healthy mice and those with breast cancer. Thus, the probe developed in this study represents a promising and powerful tool for the early clinical detection of breast cancer.", "Keywords": "Second near-infrared ; Fluorescent probe ; Breast cancer ; Tumor imaging", "DOI": "10.1016/j.snb.2022.133251", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center of Tumor Marker Detection Technology, Equipment and Diagnosis-Therapy Integration in Universities of Shandong, Shandong Provincial Key Laboratory of Detection Technology for Tumor Markers, College of Chemistry and Chemical Engineering, Linyi University, Linyi 276000, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Collaborative Innovation Center of Tumor Marker Detection Technology, Equipment and Diagnosis-Therapy Integration in Universities of Shandong, Shandong Provincial Key Laboratory of Detection Technology for Tumor Markers, College of Chemistry and Chemical Engineering, Linyi University, Linyi 276000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center of Tumor Marker Detection Technology, Equipment and Diagnosis-Therapy Integration in Universities of Shandong, Shandong Provincial Key Laboratory of Detection Technology for Tumor Markers, College of Chemistry and Chemical Engineering, Linyi University, Linyi 276000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center of Tumor Marker Detection Technology, Equipment and Diagnosis-Therapy Integration in Universities of Shandong, Shandong Provincial Key Laboratory of Detection Technology for Tumor Markers, College of Chemistry and Chemical Engineering, Linyi University, Linyi 276000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center of Tumor Marker Detection Technology, Equipment and Diagnosis-Therapy Integration in Universities of Shandong, Shandong Provincial Key Laboratory of Detection Technology for Tumor Markers, College of Chemistry and Chemical Engineering, Linyi University, Linyi 276000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center of Tumor Marker Detection Technology, Equipment and Diagnosis-Therapy Integration in Universities of Shandong, Shandong Provincial Key Laboratory of Detection Technology for Tumor Markers, College of Chemistry and Chemical Engineering, Linyi University, Linyi 276000, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Collaborative Innovation Center of Tumor Marker Detection Technology, Equipment and Diagnosis-Therapy Integration in Universities of Shandong, Shandong Provincial Key Laboratory of Detection Technology for Tumor Markers, College of Chemistry and Chemical Engineering, Linyi University, Linyi 276000, China;Corresponding authors"}, {"AuthorId": 8, "Name": "Xiangzhi Song", "Affiliation": "College of Chemistry & Chemical Engineering, Central South University, Changsha, Hunan 410083, China;Corresponding authors"}], "References": []}, {"ArticleId": 105513932, "Title": "Differentially private stochastic gradient descent via compression and memorization", "Abstract": "We propose a novel approach for achieving differential privacy for neural network training models through compression and memorization of gradients. The compression technique, which makes gradient vectors sparse, reduces the sensitivity so that differential privacy can be achieved with less noise; whereas the memorization technique, which remembers unused gradient parts, keeps track of the descent direction and thereby maintains the accuracy of the proposed algorithm. Our differentially private algorithm, called dp-memSGD for short, converges mathematically at the same rate of 1 / T as standard stochastic gradient descent (SGD) algorithm, where T is the number of training iterations. Experimentally, we demonstrate that dp-memSGD converges with reasonable privacy losses on many benchmark datasets.", "Keywords": "Differential privacy ; Neural network ; Stochastic gradient descent ; Gradient compression and memorization", "DOI": "10.1016/j.sysarc.2022.102819", "PubYear": 2023, "Volume": "135", "Issue": "", "JournalId": 1411, "JournalTitle": "Journal of Systems Architecture", "ISSN": "1383-7621", "EISSN": "1873-6165", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Institute of Information and Communications Technology (NICT), Tokyo 184-8795, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "Tran Thi <PERSON>", "Affiliation": "Meiji University, Kanagawa, 214-8571, Japan;National Institute of Information and Communications Technology (NICT), Tokyo 184-8795, Japan"}], "References": [{"Title": "Privacy-preserving based task allocation with mobile edge clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "288", "JournalTitle": "Information Sciences"}, {"Title": "PrivateDL: Privacy‐preserving collaborative deep learning against leakage from gradient sharing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "8", "Page": "1262", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "An optimal (∊,δ)-differentially private learning of distributed deep fuzzy models", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "546", "Issue": "", "Page": "87", "JournalTitle": "Information Sciences"}, {"Title": "Scaling up Differentially Private Deep Learning with Fast Per-Example Gradient Clipping", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2021", "Issue": "1", "Page": "128", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}, {"Title": "Correlated tuple data release via differential privacy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "560", "Issue": "", "Page": "347", "JournalTitle": "Information Sciences"}, {"Title": "CORK: A privacy-preserving and lossless federated learning scheme for deep neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "603", "Issue": "", "Page": "190", "JournalTitle": "Information Sciences"}, {"Title": "Distributed differentially-private learning with communication efficiency", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "102555", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Combinatorial resources auction in decentralized edge-thing systems using blockchain and differential privacy", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "607", "Issue": "", "Page": "211", "JournalTitle": "Information Sciences"}, {"Title": "Machine Learning with Differentially Private Labels: Mechanisms and Frameworks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "4", "Page": "332", "JournalTitle": "Proceedings on Privacy Enhancing Technologies"}]}, {"ArticleId": 105513933, "Title": "A visual detection strategy for SARS-CoV-2 based on dual targets-triggering DNA walker", "Abstract": "SARS-CoV-2, a highly transmissible and mutagenic virus, made huge threats to global public health. The detection strategies, which are free from testing site requirements, and the reagents and instruments are portable, are vital for early screening and play a significant role in curbing the spread. This work proposed a silver-coated glass slide (SCGS)/DNA walker based on a dual targets-triggering mechanism, enzyme-catalyzed amplification, and smartphone data analysis, which build a portable visual detection strategy for the SARS-CoV-2 RNA-dependent RNA polymerase (RdRp) gene. By this method, the detection was reflected by the ultraviolet absorbance changes and visible color changes to the naked eye which was analyzed by Red-Green-Blue (RGB) data analysis via smartphone within 30 min, simplifying the detection process and shortening the detection time. Meanwhile, the dual targets-triggering mechanism and dual signal amplification strategy ensured detection specificity and sensitivity. Further, the practicability was verified by the detection of the real sample which provided this method an application potential in SARS-CoV-2 rapid detection.", "Keywords": "Biosensor;DNA Walker;SARS-CoV-2;Silver coated glass slide", "DOI": "10.1016/j.snb.2022.133252", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Chemistry &amp; Environmental Science, Guangdong Ocean University, Zhanjiang 524088, China."}, {"AuthorId": 2, "Name": "Ciling Li", "Affiliation": "Faculty of Chemistry &amp; Environmental Science, Guangdong Ocean University, Zhanjiang 524088, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Chemistry &amp; Environmental Science, Guangdong Ocean University, Zhanjiang 524088, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Chemistry &amp; Environmental Science, Guangdong Ocean University, Zhanjiang 524088, China. ;Research Center for Coastal Environmental Protection and Ecological Resilience, Guangdong Ocean University, Zhanjiang 524088, China. ;Laboratory of Climate, Resources and Environment in Continental Shelf Sea and Deep Sea of Department of Education of Guangdong Province, Guangdong Ocean University, Zhanjiang, Guangdong 524088, China."}], "References": []}, {"ArticleId": *********, "Title": "Improved terrain estimation from spaceborne lidar in tropical peatlands using spatial filtering", "Abstract": "Tropical peatlands are estimated to hold carbon stocks of 70 Pg C or more as partly decomposed organic matter, or peat. Peat may accumulate over thousands of years into gently mounded deposits called peat domes with a relief of several meters over distances of kilometers. The mounded shapes of tropical peat domes account for much of the carbon storage in these landscapes, but their subtle topographic relief is difficult to measure. As many of the world&#x27;s tropical peatlands are remote and inaccessible, spaceborne laser altimetry data from missions such as NASA&#x27;s Global Ecosystem Dynamics Investigation (GEDI) on the International Space Station (ISS) and the Advanced Topographic Laser Altimeter System (ATLAS) instrument on the Ice, Cloud and land Elevation Satellite-2 (ICESat-2) observatory could help to describe these deposits. We evaluate retrieval of ground elevations derived from GEDI waveform data, as well as single-photon data from ATLAS, with reference to an airborne lidar dataset covering an area of over 300 km<sup>2</sup> in the Belait District of Brunei Darussalam on the island of Borneo. Spatial filtering of GEDI L2A version 2, algorithm 1 quality data reduced mean absolute deviations from airborne-lidar-derived ground elevations from 8.35 m to 1.83 m, root-mean-squared error from 15.98 m to 1.97 m, and unbiased root-mean-squared error from 13.62 m to 0.72 m. Similarly, spatial filtering of ATLAS ATL08 version 3 ground photons from strong beams at night reduced mean absolute deviations from 1.51 m to 0.64 m, root-mean-squared error from 3.85 m to 0.77 m, and unbiased root-mean-squared error from 3.54 m to 0.44 m. We conclude that despite sparse ground retrievals, these spaceborne platforms can provide useful data for tropical peatland surface altimetry if postprocessed with a spatial filter.", "Keywords": "Tropical peatlands ; Spaceborne laser altimetry ; Ground point classification ; Aerial laser scanning ; ATLAS ; GEDI ; Spatial filter", "DOI": "10.1016/j.srs.2022.100074", "PubYear": 2023, "Volume": "7", "Issue": "", "JournalId": 72499, "JournalTitle": "Science of Remote Sensing", "ISSN": "2666-0172", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Environmental Sensing and Modeling, Singapore-MIT Alliance for Research and Technology, 138602, Singapore;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Asian School of the Environment, Nanyang Technological University, 639798, Singapore;Human Origins Program, National Museum of Natural History, Smithsonian Institution, Washington, DC, 20013, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Biodiversity and Environmental Research, Universiti Brunei Darussalam, Bandar Seri Begawan, BE1410, Brunei Darussalam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Environmental and Life Sciences Programme, Universiti Brunei Darussalam, Bandar Seri Begawan, BE1410, Brunei Darussalam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Geosciences, University of Potsdam, 14476, Potsdam, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Center for Environmental Sensing and Modeling, Singapore-MIT Alliance for Research and Technology, 138602, Singapore;Department of Civil and Environmental Engineering, Massachusetts Institute of Technology, Cambridge, MA, 02139, USA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geography, National University of Singapore, 117570, Singapore"}], "References": [{"Title": "Validation of ICESat-2 terrain and canopy heights in boreal forests", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "251", "Issue": "", "Page": "112110", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Performance evaluation of GEDI and ICESat-2 laser altimeter data for terrain and canopy height retrievals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112571", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 105513936, "Title": "Modelling internal tree attributes for breeding applications in Douglas-fir progeny trials using RPAS-ALS", "Abstract": "Coastal Douglas-fir ( <PERSON><PERSON><PERSON><PERSON><PERSON> (Mirb.) Franco) is one of the most commercially important softwood species in North America. In British Columbia, Canada, breeding has increased volume gains between 20 and 30%, while 97% of seedlings come from improved seed sources. Branching traits in particular, have a strong influence on strength and stiffness of Douglas-fir wood; however, they are rarely measured. Remotely Piloted Aerial Systems and Airborne Laser Scanning Systems (RPAS-LS) produce high-density three-dimensional point clouds that can be used for the creation of internal geometric features describing individual tree branching structures. We analyzed a Coastal Douglas-fir progeny test trial located in British Columbia, Canada, and developed a new method to estimate branch attributes from RPAS-LS data for inclusion as selection criteria in tree improvement programs. Branch length, angle, width, and volume were estimated for each tree. Narrow-sense heritability (the proportion of variation due to genetics) and genetic correlations were also estimated. The method extracted branch length with a correlation (r) of 0.93 compared to manual measurements. Using these branch attributes, results then show that branch angle had the highest heritability (0.277), while tree height and branch length had the highest genetic correlation (0.668). These findings are encouraging for forest managers as they indicate that branch level metrics should be considered when selecting trees in breeding programs.", "Keywords": "Remotely piloted aerial systems (RPAS) ; Airborne laser scanning ; UAV ; UAS ; Drone ; Internal geometric features ; Branching structure ; Tree phenotyping ; Douglas-fir", "DOI": "10.1016/j.srs.2022.100072", "PubYear": 2023, "Volume": "7", "Issue": "", "JournalId": 72499, "JournalTitle": "Science of Remote Sensing", "ISSN": "2666-0172", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Forest Resources Management, Faculty of Forestry, University of British Columbia, 2424 Main Mall, Vancouver, BC V6T 1Z4, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Forest Resources Management, Faculty of Forestry, University of British Columbia, 2424 Main Mall, Vancouver, BC V6T 1Z4, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Forest and Conservation Sciences, Faculty of Forestry, University of British Columbia, 2424 Main Mall, Vancouver, BC V6T 1Z4, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Forest and Conservation Sciences, Faculty of Forestry, University of British Columbia, 2424 Main Mall, Vancouver, BC V6T 1Z4, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Geography, Planning, and Spatial Sciences, University of Tasmania, Geography & Environmental Studies Building, Private Bag 78, Sandy Bay, TAS, 7001, Australia"}], "References": []}, {"ArticleId": *********, "Title": "OVERVIEW OF METHODS FOR BUILDING A RISK-BASED ACCESS CONTROL MODEL IN INTERNET OF THINGS SYSTEMS", "Abstract": "<p>В работе рассматривается понятие риска безопасности и способы его оценки. Проводится подробный обзор методов количественной и качественной оценки рисков и анализ их преимуществ и недостатков. Проведен детальный анализ применений методов оценки рисков в различных риск-ориентированных моделях контроля доступа, с выделением их преимуществ и недостатков. Приведены результаты сравнительного анализа преимуществ и ограничений применимости различных методов оценки риска. Рассмотрен подход на основе нечеткой логики на основе экспертных оценок. На основании проведенного анализа и результатов сравнения выбран метод нечеткой логики с экспертным оценками в качестве подходящего метода для реализации риск-ориентированной модели контроля доступа для систем на построенных на базе технологии Интернета Вещей.</p><p>The paper discusses the concept of security risk and ways to assess it. A detailed review of methods of quantitative and qualitative risk assessment and analysis of their advantages and disadvantages is carried out. A detailed analysis of the applications of risk assessment methods in various risk-oriented access control models has been carried out, highlighting their advantages and disadvantages. The results of a comparative analysis of the advantages and limitations of the applicability of various risk assessment methods are presented. An approach based on fuzzy logic based on expert assessments is considered. Based on the analysis and comparison results, the fuzzy logic method with expert assessments was chosen as a suitable method for implementing a risk-based access control model for systems based on Internet of Things technology.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.2.010", "PubYear": 2022, "Volume": "", "Issue": "2(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Сергей Але<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Андрей Але<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105513978, "Title": "An Empirical Criterion for Transitive Closure", "Abstract": "The paper establishes a simple and computationally economical criterion for determining whether a given adjacency matrix represents a transitive closure or not. The criterion is based on a new iterative method for finding the transitive closure adjacency matrix. This method is equivalent to but computationally more efficient than the traditional Boolean sum of successive powers of the original adjacency matrix to determine the transitive closure matrix. However, it is computationally less efficient than the Warshall algorithm and the recent more advanced algorithms. Nevertheless, in many cases, in which a given matrix is different from the transitive closure, but may not differ much, a few of the proposed looping steps may suffice to find the transitive closure, avoiding the computational burden of the best-in-class algorithms. Thus, the criterion and the recursive relation are apt in complementing the extant methods to establish an efficient evaluation engine for the determination of the transitive closure © 2022 <PERSON>. This open-access article is distributed under a Creative Commons Attribution (CC-BY) 4.0 license", "Keywords": "Graph theory; Search algorithms; Transitive closure; Warshall algorithm", "DOI": "10.3844/jcssp.2022.1232.1236", "PubYear": 2022, "Volume": "18", "Issue": "12", "JournalId": 8656, "JournalTitle": "Journal of Computer Science", "ISSN": "1549-3636", "EISSN": "1552-6607", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Virginia Polytechnic Institute and State University, Blacksburg, United States"}], "References": []}, {"ArticleId": 105514015, "Title": "Research on Mutual Information Feature Selection Algorithm Based on Genetic Algorithm", "Abstract": "<p lang=\"zh\"><p><p>Feature selection is an important part of data preprocessing. Feature selection algorithms that use mutual information as evaluation can effectively handle different types of data, so it has been widely used. However, the potential relationship between relevance and redundancy in the evaluation criteria is often ignored, so that effective feature subsets cannot be selected. Optimize the evaluation criteria of the mutual information feature selection algorithm and propose a mutual information feature selection algorithm based on dynamic penalty factors (Dynamic Penalty Factor Mutual Information Feature Selection Algorithm, DPMFS). The penalty factor is dynamically calculated with different selected features, so as to achieve a relative balance between relevance and redundancy, and effectively play the synergy between relevance and redundancy, and select a suitable feature subset. Experimental results verify that the DPMFS algorithm can effectively improve the classification accuracy of the feature selection algorithm. Compared with the traditional chi-square, MIM and MIFS feature selection algorithms, the average classification accuracy of the random forest classifier for the six standard datasets is increased by 3.73%, 3.51% and 2.44%, respectively.</p> <p> </p></p></p>", "Keywords": "", "DOI": "10.53106/199115992022123306011", "PubYear": 2022, "Volume": "33", "Issue": "6", "JournalId": 90611, "JournalTitle": "電腦學刊", "ISSN": "1991-1599", "EISSN": "1991-1599", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>-<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "The Readiness of Lasem Batik Small and Medium Enterprises to Join the Metaverse", "Abstract": "<p>Today’s business competitiveness necessitates the capacity of all company players, particularly small and medium enterprises (SMEs), to enter a broader market through information technology. However, the Lasem Batik SMEs have endured a great deal of turmoil during the COVID-19 pandemic. Marketing has been conducted through physical and internet channels, but the results have not been maximized. The purpose of this research was to consider the possibilities of Lasem Batik SMEs adopting metaverse technology as a marketing medium to enhance sales. The investigation was conducted on 40 Lasem Batik SMEs who met the requirements of using online media to sell their products, having a medium-sized firm, and displaying marketing that has reached the provincial level. The findings of this study are as follows: (1) The majority of participants stated that the metaverse is a virtual 3D space. This understanding is deepened by discussions about virtual 3D spaces that combine VR and AR, which today is often referred to as the metaverse. (2) Batik business owners hope that by using the metaverse, they will be able to obtain many benefits, especially related to market expansion. (3) Lasem Batik SMEs show great interest in expanding their marketing channels to a wider area; Lasem Batik entrepreneurs also accept the challenge of studying the metaverse with new knowledge and techniques they have never considered. (4) Overall, 75% of participants were ready to use the metaverse, and 25% still required guidance. (5) Local communities, universities, and large corporations provide great support for the use of the metaverse. (6) The commercial success of Lasem Batik SMEs is defined by product quality; ongoing online and offline advertising; originality and innovation; and the capacity to capitalize on possibilities, retain local wisdom, and preserve strong customer connections. The main conclusion is that the readiness of batik entrepreneurs to use the metaverse is highly dependent on the support of various parties. A strong desire to progress and develop one’s business is the main factor determining one’s intention to use the metaverse. As a result of the research, a prototype of a metaverse platform for Lasem Batik exhibitions has been developed. SMEs can use the room template provided by the platform and join other SMEs to hold a metaverse exhibition to attract global customers. These results can be connected to create a metaverse exhibition to attract global customers.</p>", "Keywords": "", "DOI": "10.3390/computers12010005", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Accounting Department, Faculty of Economics and Business, Soegijapranata Catholic University, Semarang 50234, Indonesia; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Systems Department, Faculty of Computer Science, Soegijapranata Catholic University, Semarang 50234, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Accounting Department, Faculty of Economics and Business, Soegijapranata Catholic University, Semarang 50234, Indonesia"}], "References": []}, {"ArticleId": *********, "Title": "Bibliometric Analysis and Visualization of Scientific Literature on Random Forest Regression", "Abstract": "Random forest regression (RFR) is a versatile, easy-to-use and efficient tree based machine-learning algorithm that utilizes the power of multiple decision trees for making decisions. So random forest is a subject of a great deal of research, in many of the machine intelligence applications. The objective of this research is to investigate the scientific output of research based on RFR and to explore its hotspots and frontiers through bibliometric analysis for the years 2007 to 2019. The data are collected from the Web of Science database. The total number of publications, the citations, and types of publications, publication countries, productive authors, prominent journals, and keyword co-occurrence of RFR research are examined, using VOSviewer software. There are 516 papers, published in 299 journals, of which researchers from the USA published 162 articles. The most prolific author, with 6 publications and 240 citations, is <PERSON>. The most cited article is the research article entitled \"Genomic selection in wheat breeding using genotyping by sequencing”. Among the journals, the most articles (41 publications) are published by the Remote Sensing journal. © 2022 National Research Nuclear University. All rights reserved.", "Keywords": "Bibliometric analysis; Machine learning; Random forest; Random forest regression; VOSviewer", "DOI": "10.26583/sv.14.5.04", "PubYear": 2022, "Volume": "14", "Issue": "5", "JournalId": 48183, "JournalTitle": "Scientific Visualization", "ISSN": "", "EISSN": "2079-3537", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "A Department of Computer Science, Assumption College Autonomous, Kottayam, Kerala, Changanacherry, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "B Department of Computer Applications, Marian College Autonomous, Idukki, Kerala, Kuttikanam, India; C School of Computer Sciences, Mahatma Gandhi University, Kerala, Kottayam, India"}], "References": []}, {"ArticleId": 105514184, "Title": "AUTOMATED INFORMATION - PS<PERSON>CHOL<PERSON>GICA<PERSON> RISK - M<PERSON>EL OF REGIONAL INTERNET - USER IN THE CONTEXT OF FIGHTING DESTRUCTIONS", "Abstract": "<p>В работе приводится методическое, алгоритмическое и программное обеспечение для построения автоматизированной информационно-психологической риск-модели регионального Интернет-пользователя из молодежной среды, а также оценка эффективности ее применения. Для построения модели были определены значимые параметры и структура информационной базы, разработаны алгоритмы сбора данных и архитектура программно-технического комплекса. Рассмотрены стадии процесса распространения деструктивного контента: мутация деструктивного контента, вовлеченность, участие пользователя, приведены расчетные формулы для этих стадий, представлены графики динамики рисков Интернет-пользователей на этих стадиях. В результате получена действующая автоматизированная информационно-психологическая риск-модель регионального Интернет-пользователя, которая может быть использована в целях противодействия формированию деструктивного поведения в молодежной среде.</p><p>The work provides methodological, algorithmic and software for building an automated information and psychological risk model of a regional Internet user from a youth environment, as well as an assessment of the effectiveness of its application. To build the model, significant parameters and the structure of the information base were determined, data collection algorithms and the architecture of the software and hardware complex were developed. As a result, an effective automated information and psychological risk model of a regional Internet user has been obtained, which can be used to counteract the formation of destructive behavior in the youth environment.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.3.013", "PubYear": 2022, "Volume": "", "Issue": "3(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Макар <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>евич", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Пахомова, Анна Степановна", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Лариса Владимировна", "Affiliation": ""}], "References": []}, {"ArticleId": 105514216, "Title": "AUTOMATED INFORMATION SYSTEM FOR DISCRETE MODELING OF NETWORK EPIDEMIC PROCESSES. PART 1", "Abstract": "<p>Стремительное развитие информационных технологий и внедрение их в различные сферы деятельности обуславливает их применение при построении современных корпоративных сетей, что значительно расширяет их функционал и повышает эффективность. Вместе с тем, постоянно возрастает количество атак с применением вредоносного программного обеспечения (ВПО) различного типа, способных наносить значительный ущерб. Особую угрозу для корпоративных сетей представляют вирусы, способные порождать масштабные сетевые эпидемии, деструктивное воздействие которых за последние десятилетия нанесло значительный финансовый ущерб как организациям, так и частным лицам. В рамках представленного исследования было разработано методическое обеспечение автоматизированной информационной системы (АИС) дискретного моделирования сетевых эпидемических процессов на основе глубокой формализации многоэтапного моделирования с учётом таких процессов как: применения карантинных мер, вакцинация элементов сети, а также возможные несинхронные и распределённые по топологии сети вбросы инфекции.</p><p>The rapid development of information technologies and their introduction into various fields of activity determines their use in the construction of modern corporate networks, which significantly expands their functionality and increases efficiency. At the same time, the number of attacks using various types of malware that can cause significant damage is constantly increasing. A particular threat to corporate networks is viruses that can generate large-scale network epidemics, the destructive impact of which over the past decades has caused significant financial damage to both organizations and individuals. As part of the presented study, methodological support was developed for an automated information system (AIS) for discrete modeling of network epidemic processes based on deep formalization of multi-stage modeling, taking into account such processes as: the application of quarantine measures, vaccination of network elements, as well as possible non-synchronous and distributed over the network topology injection of infection.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.3.007", "PubYear": 2022, "Volume": "", "Issue": "3(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Алекс<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Шварцк<PERSON><PERSON><PERSON>, Евгения Андреевна", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Игор<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Machine Learning Modeling of GPS Features with Applications to UAV Location Spoofing Detection and Classification", "Abstract": "In this paper, machine learning (ML) modeling is proposed for the detection and classification of global positioning system (GPS) spoofing in unmanned aerial vehicles (UAVs). Three testing scenarios are implemented in an outdoor yet controlled setup to investigate static and dynamic attacks. In these scenarios, authentic sets of GPS signal features are collected, followed by other sets obtained while the UAV is under spoofing attacks launched with a software-defined radio (SDR) transceiver module. All sets are standardized, analyzed for correlation, and reduced according to feature importance prior to their exploitation in training, validating, and testing different multiclass ML classifiers. The resulting performance evaluation of these classifiers shows a detection rate (DR), misdetection rate (MDR), and false alarm rate (FAR) better than 92%, 13%, and 4%, respectively, together with a sub-millisecond detection time. Hence, the proposed modeling facilitates accurate real-time GPS spoofing detection and classification for UAV applications.", "Keywords": "Global positioning system (GPS) ; machine learning (ML) ; software-defined radio (SDR) ; spoofing detection ; unmanned aerial vehicles (UAVs)", "DOI": "10.1016/j.cose.2022.103085", "PubYear": 2023, "Volume": "126", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Purdue University Northwest, Hammond, IN 46375, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Purdue University Northwest, Hammond, IN 46375, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Purdue University Northwest, Hammond, IN 46375, USA;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, The University of Maine, Orono, ME 04469, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, The University of North Dakota, Grand Forks, ND 58202, USA"}], "References": [{"Title": "A compilation of UAV applications for precision agriculture", "Authors": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "172", "Issue": "", "Page": "107148", "JournalTitle": "Computer Networks"}]}, {"ArticleId": *********, "Title": "Optimize along the way: An industrial case study on web performance", "Abstract": "<b  >Context:</b> Fast loading web apps can be a key success factor in terms of user experience. However, improving the performance of a web app is not trivial, since it requires a deep understanding of both the browser engine and the specific usage scenarios of the web app under consideration. <b  >Aims:</b> In this paper, we present an industrial case study at 30 MHz, an agricultural technology company, in which we target a large web-based dashboard, where its performance was improved via 13 distinct interventions over a four-month period. Moreover, we conduct a user study to analyse whether web performance metrics correlate with the user perceived page load time in optimization scenarios. <b  >Methods:</b> First, we design a replicable performance engineering plan, where the technical realization of each intervention is reported in detail along with its development effort. Second, we develop a benchmarking tool that supports 11 widely used web performance metrics. Finally, we use the benchmarking tool to quantitatively evaluate the performance of the target web app and measure the effect of 13 interventions on both desktop and mobile devices. For the user study, we record six videos of different page loads and ask participants about their opinion about the time a web page is considered ready. We calculate the correlation of the user perceived data with each web performance metric. <b  >Results:</b> We observe a considerable performance improvement over the course of the 13 interventions. Among others, we achieve 98.37% and 97.56% time reductions on desktop and mobile, respectively, for the First Contentful Paint metric. In addition, we achieve 48.25% and 19.85% improvements for the Speed Index (SI) metric on desktop and mobile, respectively. Our user study shows that the Lowest Time to Widget metric, a product-specific web performance metric, is perfectly correlated with perceived performance during the optimization process. <b  >Conclusion:</b> This study shows the importance of a continuous focus on performance engineering in the context of large-scale web apps to improve user browsing experience. We recommend developers to carefully plan their performance engineering activities, since different interventions might require different efforts and can have different effects on the overall performance of the web application.", "Keywords": "Performance ; Web browsers ; Industrial case study", "DOI": "10.1016/j.jss.2022.111593", "PubYear": 2023, "Volume": "198", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Vrije Universiteit Amsterdam, The Netherlands;Correspondence to: <PERSON><PERSON>je Universiteit, Faculty of Sciences, De Boelelaan 1081a, 1081 HV Amsterdam, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, University of Ottawa, Canada"}], "References": []}, {"ArticleId": *********, "Title": "Person identification based on voice biometric using deep neural network", "Abstract": "<p>Nowadays in all everyday transactions, technological progress has become an intrinsic characteristic that depends on such electronic applications as financial and banking transfers, health care, project management, and other crucial life aspects. The core of these applications is person Identification and/or verification steps which can be considered one of the complicated limitations. Accordingly, the employment of biometric attributes can yield promising outcomes in these fields. A One’s voice is a unique bio-feature whereby people can be authenticated and precludes others from assuming a one’s identity without their previous knowing or assent. This work proposes a model with a new architecture to identify the person by exploiting the unique individual characteristics available in one’s voice based on deep learning. An augmentation method is utilized to increase the samples in the available dataset. The available temporal information at an input audio file is analysed then feature maps from this information are extracted which represent the salient temporal feature (time-domain features). The decision is made based on tracking these voice features over time. Successful and promising results are achieved through this work, the accuracy is close to 99.81% (± 1.78%) and the values of loss function are close to 0.009 over VoxCeleb1 dataset for identifying 40 subjects.</p>", "Keywords": "One Dimensional Convolutional Neural Networks (1DCNN); Voice biometric; Person identification PI; Rectified linear units (ReLU); Pooling layer (Pool); Batch normalization layer (BN)", "DOI": "10.1007/s41870-022-01142-1", "PubYear": 2023, "Volume": "15", "Issue": "2", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "Noor D. AL-Shakarchy", "Affiliation": "Department of Computer Science, Faculty of Computer Science and Information Technology, Kerbala University, Kerbala, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Education for Humanities Studies, University of Babylon, Babylon, Iraq"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computer Science and Information Technology, Kerbala University, Kerbala, Iraq"}], "References": [{"Title": "CNN based keyword spotting: An application for context based voiced Odia words", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "3647", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 105514274, "Title": "Model-based design and optimization of GSSR chromatography for peptide purification", "Abstract": "Gradient with Steady State Recycle (GSSR) is a recently developed process for center-cut separation by solvent-gradient chromatography. The process comprises a multicolumn, open-loop system with cyclic steady-state operation that simulates a solvent gradient moving countercurrently with respect to the solid phase. However, the feed is always injected into the same column and the product always collected from the same column as in single-column batch chromatography. Here, three-column GSSR chromatography for peptide purification is optimized using state-of-the-art mathematical programming tools. The optimization problem is formulated using a full-discretization approach for steady periodic dynamics. The resulting nonlinear programming problem is solved by an efficient open-source interior-point solver coupled to a high-performance parallel linear solver for sparse symmetric indefinite matrices. The procedure is successfully employed to find optimal solutions for a series of process design problems with increasing number of decision variables. In addition to productivity and recovery, process performance is analyzed in terms of two key performance indicators: dilution ratio and solvent consumption ratio. Finally, the problem of robust process design under uncertainty in the solvent gradient manipulation is examined. The best solution is chosen only among candidate solutions that are robust feasible, i.e., remain feasible for all modifier gradient perturbations within the accuracy range of the gradient pump. This gives rise to a robust approach to optimal design in which the nominal problem is replaced by a worst case problem. Overall, our work illustrates the advantages of using advanced mathematical programming tools in designing and optimizing a GSSR process for which it is difficult to deduce sufficiently general heuristic design rules.", "Keywords": "Multicolumn chromatography ; Solvent gradient ; GSSR process ; Process optimization ; Interior point method ; Optimization under uncertainty", "DOI": "10.1016/j.dche.2022.100081", "PubYear": 2023, "Volume": "6", "Issue": "", "JournalId": 90723, "JournalTitle": "Digital Chemical Engineering", "ISSN": "2772-5081", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Tiago P.D. Santos", "Affiliation": "LAQV-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Departamento de Química, NOVA School of Science and Technology, FCT NOVA, 2829-516 Caparica, Portugal"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IBET – Instituto de Biologia Experimental e Tecnológica, Apartado 12, 2780-901 Oeiras, Portugal"}, {"AuthorId": 3, "Name": "Rui P.P.L. <PERSON>", "Affiliation": "LAQV-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Departamento de Química, NOVA School of Science and Technology, FCT NOVA, 2829-516 Caparica, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IBET – Instituto de Biologia Experimental e Tecnológica, Apartado 12, 2780-901 Oeiras, Portugal"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "LAQV-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Departamento de Química, NOVA School of Science and Technology, FCT NOVA, 2829-516 Caparica, Portugal;Corresponding author"}], "References": []}, {"ArticleId": 105514295, "Title": "Automated algorithm design using proximal policy optimisation with identified features", "Abstract": "Automated algorithm design is attracting considerable recent research attention in solving complex combinatorial optimisation problems, due to that most metaheuristics may be particularly effective at certain problems or certain instances of the same problem but perform poorly at others. Within a general algorithm design framework, this study investigates reinforcement learning on the automated design of metaheuristic algorithms. Two groups of features, namely search-dependent and instance-dependent features, are firstly identified to represent the search space of algorithm design to support effective reinforcement learning on the new task of algorithm design. With these key features, a state-of-the-art reinforcement learning technique, namely proximal policy optimisation, is employed to automatically combine the basic algorithmic components within the general framework to develop effective metaheuristics. Patterns of the best designed algorithm, in particular the utilisation and transition of algorithmic components, are investigated. Experimental results on the capacitated vehicle routing problem with time windows benchmark dataset demonstrate the effectiveness of the identified features in assisting automated algorithm design with the proposed reinforcement learning model.", "Keywords": "", "DOI": "10.1016/j.eswa.2022.119461", "PubYear": 2023, "Volume": "216", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham, Nottingham, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, University of Nottingham, Nottingham, UK"}, {"AuthorId": 3, "Name": "Licheng Jiao", "Affiliation": "School of Artificial Intelligence, Xidian University, China"}], "References": [{"Title": "Feature Construction for Meta-heuristic Algorithm Recommendation of Capacitated Vehicle Routing Problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Evolutionary Learning and Optimization"}, {"Title": "Automated design of search algorithms: Learning on algorithmic components", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "115493", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": *********, "Title": "A hybrid Fifth Generation based approaches on extracting and analyzing customer requirement through online mode in healthcare industry", "Abstract": "The proposed hybrid fifth Generation-based approaches are employed to analyze and investigate the feedback of 37,855 patients from 65 hospitals worldwide and gathered through various online modes. The collected data was analyzed with various cloud-computing technologies such as Artificial Intelligence (AI) based algorithms like natural language pre-processing (NLP), text mining (TM), and sentiment analysis (SA). This work discusses the new opportunities in the healthcare sector and the capabilities that 5G influences can bring to the healthcare industry. According to a recent investigation, the overall assessment ranking agrees with the personal view (sentiment) rating analysis for the title and full element of the web-based feedback obtained from the patients of different hospitals worldwide through an online survey. There are five main categories in the healthcare sector for which patients give priority and expect good services. They are treatment, physical facilities, dependability, timely committed service, and responsiveness. This research makes theoretical and practical contributions to the interdisciplinary areas comprising computer science, data management, and healthcare services by utilizing recent analytic techniques to attain optimized results.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108550", "PubYear": 2023, "Volume": "106", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Industrial Engineering, Faculty of Engineering, King Abdulaziz University, Jeddah, Saudi Arabia"}], "References": [{"Title": "PTS-PAPR Reduction Technique for 5G Advanced Waveforms Using BFO Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "3", "Page": "713", "JournalTitle": "Intelligent Automation & Soft Computing"}]}, {"ArticleId": 105514322, "Title": "An innovative time-varying particle swarm-based Salp algorithm for intrusion detection system and large-scale global optimization problems", "Abstract": "<p> Particle swarm optimization (PSO) suffers from delayed convergence and stagnation in the local optimal solution, as do most meta-heuristic algorithms. This study proposes a time-based leadership particle swarm-based Salp (TPSOSA) to address the PSO's limitations. The TPSOSA is a novel search technique that addresses population diversity, an imbalance between exploitation and exploration, and the premature convergence of the PSO algorithm. Hybridization in TPSOSA is divided into two stages: The PSO hierarchy of leaders and followers is first represented as a time-varying dynamic structure. Because we need much exploration at the beginning and many exploitative steps at the end, this method raises the number of leaders while decreasing the number of follower particles linearly. In the time-varying form of the PSO (TPSOSA), unlike the PSO, the number of leaders and followers changes over time. PSO's robust search strategy is used to update the leaders' positions. Second, the SSA's powerful exploitation is utilized to update the followers' swarm population position. The purpose of tweaking the particle swarm optimizer algorithm is to aid the fundamental method in avoiding premature convergence and quickly directing the search to the most promising likely search space. The proposed TPSOSA method is tested using the CEC 2017 benchmark, seven CEC2008lsgo test functions with 200, 500, and 1000 decision variables, and 19 datasets (including three high-dimensional datasets and the NSL-KDD Dataset for Intrusion Detection System). In each experiment, TPSOSA is compared to various state-of-the-art metaheuristics methods. <PERSON> and <PERSON><PERSON> rank-sum statistical tests are also used to analyze the data. Experimental data and statistical tests show that the TPSOSA algorithm is very competitive and often superior to the algorithms used in the studies. According to the results, TPSOSA can also find an optimal feature subset that enhances classification accuracy while reducing the number of features employed.</p>", "Keywords": "Swarm intelligence; Feature selection; Salp swarm algorithm; Particle swarm optimization; NSL-KDD database; Anomaly detection", "DOI": "10.1007/s10462-022-10322-1", "PubYear": 2023, "Volume": "56", "Issue": "8", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "TIMS, FS, Abdelmalek Essaadi University, Tétouan, Morocco; Department of Computer Science, Faculty of Science, Amran University, Amran, Yemen"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "TIMS, FS, Abdelmalek Essaadi University, Tétouan, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, College of Computer Sciences and Mathematics, Tikrit University, Tikrīt, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Artificial Intelligence Research and Optimisation, Torrens University Australia, Brisbane, Australia; Yonsei Frontier Lab, Yonsei University, Seoul, South Korea"}, {"AuthorId": 5, "Name": "Mostafa <PERSON>", "Affiliation": "College of Computer Science and Engineering, Taibah University, Yanbu, Saudi Arabia; Computers and Control Systems Engineering Department, Faculty of Engineering, Mansoura University, Mansoura, Egypt"}], "References": [{"Title": "A new fusion of salp swarm with sine cosine for optimization of non-linear functions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "185", "JournalTitle": "Engineering with Computers"}, {"Title": "A new fusion of grey wolf optimizer algorithm with a two-phase mutation for feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112824", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Time-varying hierarchical chains of salps with random weight networks for feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112898", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Improved Salp Swarm Algorithm based on opposition based learning and novel local search algorithm for feature selection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "113122", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Slime mould algorithm: A new method for stochastic optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "300", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Hybrid PSO-GSA for energy efficient spectrum sensing in cognitive radio network", "Authors": "<PERSON>; <PERSON>.", "PubYear": 2020, "Volume": "40", "Issue": "", "Page": "101091", "JournalTitle": "Physical Communication"}, {"Title": "Hybrid PSO and ALNS algorithm for software and mobile application for transportation in ice manufacturing industry 3.5", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "106461", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Boosted hunting-based fruit fly optimization and advances in real-world problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "113502", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved Jaya optimization algorithm with <PERSON><PERSON><PERSON> flight", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113902", "JournalTitle": "Expert Systems with Applications"}, {"Title": "MTDE: An effective multi-trial vector-based differential evolution algorithm and its applications for engineering design problems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "106761", "JournalTitle": "Applied Soft Computing"}, {"Title": "Effect of parametric enhancements on naked mole-rat algorithm for global optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "4", "Page": "3351", "JournalTitle": "Engineering with Computers"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Orca predation algorithm: A novel bio-inspired algorithm for global optimization problems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "188", "Issue": "", "Page": "116026", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 105514363, "Title": "Studying fake news spreading, polarisation dynamics, and manipulation by bots: A tale of networks and language", "Abstract": "With the explosive growth of online social media, the ancient problem of information disorders interfering with news diffusion has surfaced with a renewed intensity threatening our democracies, public health, and news outlets’ credibility. Therefore, thousands of scientific papers have been published in a relatively short period, making researchers of different disciplines struggle with an information overload problem. The aim of this survey is threefold: (1) we present the results of a network-based analysis of the existing multidisciplinary literature to support the search for relevant trends and central publications; (2) we describe the main results and necessary background to attack the problem under a computational perspective; (3) we review selected contributions using network science as a unifying framework and computational linguistics as the tool to make sense of the shared content. Despite scholars working on computational linguistics and networks traditionally belong to different scientific communities, we expect that those interested in the area of fake news should be aware of crucial aspects of both disciplines.", "Keywords": "Disinformation ; Network analysis ; Natural language processing ; Opinion dynamics ; Fake news spreading ; Social bots", "DOI": "10.1016/j.cosrev.2022.100531", "PubYear": 2023, "Volume": "47", "Issue": "", "JournalId": 21523, "JournalTitle": "Computer Science Review", "ISSN": "1574-0137", "EISSN": "1876-7745", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "DISIT, Università degli Studi del Piemonte Orientale “<PERSON><PERSON>, <PERSON><PERSON>, 11, Alessandria, 15121, Italy;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dipartimento di Informatica, Università degli Studi di Torino, Corso Svizzera, 185, Torino, 10149, Italy;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Utrecht University, Heidelberglaan 8, Utrecht, 3584, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "PRHLT Research Center, Universitat Politècnica de València, Camino de Vera, València, 46022, Spain"}], "References": [{"Title": "Fake news, rumor, information pollution in social media and web: A contemporary survey of state-of-the-arts, challenges and opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "112986", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An Emotional Analysis of False Information in Social Media and News Articles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "A Survey of Fake News", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A decade of social bot detection", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "10", "Page": "72", "JournalTitle": "Communications of the ACM"}, {"Title": "Immigration as a Divisive Topic: Clusters and Content Diffusion in the Italian Twitter Debate", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "10", "Page": "173", "JournalTitle": "Future Internet"}, {"Title": "FakeBERT: Fake news detection in social media with a BERT-based deep learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "11765", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Fake news detection based on explicit and implicit signals of a hybrid crowd: An approach inspired in meta-learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115414", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Using fake news as means of cyber-bullying: The link with compulsive internet use and online moral disengagement", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "107032", "JournalTitle": "Computers in Human Behavior"}, {"Title": "FakeNewsLab: Experimental Study on Biases and Pitfalls Preventing Us from Distinguishing True from False News", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "10", "Page": "283", "JournalTitle": "Future Internet"}]}, {"ArticleId": 105514465, "Title": "Ready-To-Eat Rice in Retort Pouch Packaging as an Alternative Emergency Food Product", "Abstract": "<p>Recently, the rising incidence of natural catastrophe has increased the disaster preparedness, aiming to mitigate its devastating effect. Developing an emergency food is one of meaningful attempts to rise the preparedness. This research aimed to determine the best formula of ready-to-eat rice in retort pouch packaging accepted by consumers, and to determine the operating time to reach a lethality value (F0) to meet the commercial shelf-stable food requirements as an emergency food. The thermal process adequacy (F0) was used to determine the commercial shelf-stable products according to Indonesian regulation. The results showed that the determination of ready-to-eat rice was dependent on the ratio of rice and water. The most accepted product was determined according to quality attributes and organoleptic tests applied to meet the criteria for emergency food, namely color, flavor, and texture and best perceived by consumers. A formula with 140 g of half-cooked rice and 60 g of water was attributed to the best sample, having hardness of 7305.45 gf, elasticity of 36.40%, gumminess of 2185.720 gf, and adhesiveness of -167.975 g.s. In terms of microbiological quality, the TPC for the half-cooked rice sample reached 7.2×107 CFU/mL, while cooked rice in retort pouch packaging was <25 CFU/mL. Using heat distribution curve, heating at 110°C produced a come up time (CUT) after 40 min. Furthermore, the F0 value was 4.12 which was in accordance with the Indonesian regulation.</p>", "Keywords": "formula;lethality value;retort pouch;rice;water", "DOI": "10.6066/jtip.2022.33.2.129", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Rahayu", "Affiliation": "Food Science Study Program, Graduate School, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Food Science and Technology, Faculty of Agricultural Engineering Technology, IPB University, Bogor, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Food Science and Technology, Faculty of Agricultural Engineering Technology, IPB University, Bogor, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Food Science and Technology, University of Natural Resources and Life Science, Vienna, Austria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Plant Production and Processing, University of Applied Sciences, Hochschule Weihenstephan-Triesdorf, Steingruberstraße, Weidenbach, Germany"}], "References": []}, {"ArticleId": 105514481, "Title": "Numerical data on fire in the cavity of naturally ventilated double skin façade with Venetian blinds", "Abstract": "This Data Article presents simulation data and methodology on fire in the cavity of naturally ventilated Double Skin Façade (DSF) with Venetian blinds. The simulation data includes glazing surface temperature data and the Input and Output Source Code files. The data for the validation of the model is also presented along with its methodology, input source code file and output temperature results. The comprehensive methodology used to obtain this data from the National Institute of Standards and Technology&#x27;s (NIST) Fire Dynamics Simulator (FDS) and PyroSim are presented. The data presented can provide theoretical benchmarks for architects, engineers, researchers, and designers when incorporating Venetian blinds in DSFs. It can also help fire fighters and engineers to theoretically assess the spread of fire in buildings with DSFs incorporating Venetian blinds.", "Keywords": "Building fire safety;Double skin façade;Fire Dynamics Simulation;Venetian blinds", "DOI": "10.1016/j.dib.2022.108859", "PubYear": 2023, "Volume": "46", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Architecture and Built Environment, Faculty of Science and Engineering, The University of Nottingham Ningbo China, 199 Taikang East Road, Ningbo 315100, PR China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Architecture and Built Environment, Faculty of Science and Engineering, The University of Nottingham Ningbo China, 199 Taikang East Road, Ningbo 315100, PR China. ;School of the Built Environment and Architecture, London South Bank University, 103 Borough Road, London SE1 0A, UK."}, {"AuthorId": 3, "Name": "Jing<PERSON>g <PERSON>", "Affiliation": "Ningbo University of Technology, 201Fenghua Road, Ningbo, Zhejiang 315211, PR China."}], "References": []}, {"ArticleId": *********, "Title": "Estimasi Risiko Okratoksin A dari Konsumsi Kopi Bubuk di Indonesia", "Abstract": "<p>Ochratoxin A (OTA) is a nephrotoxic and carcinogenic mycotoxin that can be found in coffee. This study aimed to obtain the processing steps commonly applied by coffee shops in Indonesia, calculate the level of OTA in coffee bean and ground coffee, and the risk estimate of OTA exposure from ground coffee in Indonesia. The processing steps were determined through an online survey while the level of OTA in coffee was calculated from available references. The consumption level of ground coffee was determined from the Indonesia total diet study report and the exposure assessment was carried out by deterministic approach. The risk estimates were expressed as % risk towards provisional tolerable weekly intake (PTWI) and margin of exposure (MOE). Based on the survey of coffee shops (n=20), ground coffee is commonly processed using dry method consisting of cherries sorting, sun drying, roasting and grinding. Ground coffee was the most common coffee consumed by adults. Based on references from countries with climate similar to Indonesia, the level of OTA in coffee bean ranged from 0.033 to 168 μg/kg with an average of 12.25 μg/kg and 0.018-55 μg/kg in ground coffee averaging at 5.60 μg/kg. The individual exposure to OTA from drinking coffee is 0.014-0.744 ng/kg bw/day. The risk estimates shows that risk of ochratoxin A from ground coffee consumption is low, with risk percentage of <100 % provisional tolerable weekly intake (PTWI) and a MOE of higher than 10000 for all age groups. The study suggested that adults (19-55 years) have higher exposure and risk than the other age groups.</p>", "Keywords": "coffee;deterministic;exposure;ochratoxin A;risk assessment", "DOI": "10.6066/jtip.2022.33.2.100", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}], "References": []}, {"ArticleId": 105514518, "Title": "Potensi Probiotik Bakteri Asam Laktat Asal Madu dari Tiga Jen<PERSON> yang Be<PERSON>eda", "Abstract": "<p>Certain strains of Lactic acid bacteria (LAB) especially from the genus of Lactobacillus and Bifidobacteria have been recognized to have health beneficial effect as probiotics. Honey has been known to have health beneficial effects and contains lactic acid bacteria. However, information pertaining the characteristics of LAB from honey is still limited. The present research aimed to isolate LAB from different types of honey and to evaluate their potency as probiotic. The LAB were enumerated and isolated from honey produced by three different honeybees: Apis cerana, Heterotrigona itama, and Trigona laeviceps. The results showed the count of LAB in three different honey ranged from 5.0x101 to 2.3x107 CFU/mL and affected by different time of sampling. The highest of average LAB count was found in honey of Heterotrigona itama. There were 48 Gram positive catalase-negative bacterial isolates obtained from the three different honey types. Twelve isolates were selected based on their survival in bile salt. The twelve selected isolates were capable of growing in MRSB pH 2.5, and MRSB containing 0.3% bile salt. They also exhibited strong antibacterial activity against pathogenic bacteria. Identification based on 16S rRNA revealed that of the twelve isolates, nine were identified as <PERSON><PERSON>lant<PERSON><PERSON>llus plantarum and three others as Pediococcus acidilactici. The twelve isolates showed high survival at low pH dan bile salt and exhibited antimicrobial activity against pathogen, hence they are considered as probiotic candidates.</p>", "Keywords": "honey;lactic acid bacteria;lactobacillus;pediococcus;probiotic", "DOI": "10.6066/jtip.2022.33.2.189", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "Iffa Illiyya Fatma", "Affiliation": "Program <PERSON><PERSON>, Sekolah <PERSON>, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}], "References": []}, {"ArticleId": 105514659, "Title": "Towards an understanding of the relationship between institutional theory, affective events theory, negative discrete emotions, and the development of feral systems when using human resource information systems", "Abstract": "This article is theoretical in nature and sets out to explore how human resources software systems create “hassles” for human resource practitioners (HRPs) that elicit negative discrete emotions (e.g., anger, frustration, exasperation, etc.) and how HRPs create feral systems (workarounds) to alleviate these elicited negative discrete emotions. Feral systems have the potential to undermine wider organisational strategic efforts aimed at successful human resource information systems (HRIS) implementation, and the overall intended benefits (e.g., productivity) that may be derived from these systems’ use in HR workflows. As such, HRPs are bound by normative pressures (i.e., conforming to the demands of others) that are presented to them by their professional governing body (e.g., human resource institutions) and their organizations. Therefore, Institutional Theory is used as the primary theoretical framework for guiding the themes of this article. Affective Event Theory (AET) forms the secondary basis for additional arguments.", "Keywords": "Affective events theory ; Feral systems ; Affectivity ; HRIS systems ; Institutional theory ; Workarounds", "DOI": "10.1016/j.chbr.2022.100264", "PubYear": 2023, "Volume": "9", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON> (Stace) Kent", "Affiliation": "Independent Researcher;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Griffith University, Nathan, Queensland, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Otago, Dunedin, New Zealand"}], "References": [{"Title": "Learning from Workaround Practices: the Challenge of Enterprise System Implementations in Multinational Corporations", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "4", "Page": "639", "JournalTitle": "Information Systems Journal"}, {"Title": "The relationship between ERP capabilities, use, and value", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "117", "Issue": "", "Page": "103209", "JournalTitle": "Computers in Industry"}, {"Title": "Leaving the Shadow: A Configurational Approach to Explain Post-identification Outcomes of Shadow IT Systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "63", "Issue": "2", "Page": "97", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Shadow IT – Systematic Literature Review", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "49", "Issue": "1", "Page": "144", "JournalTitle": "Information Technology And Control"}, {"Title": "The mediating role of social presence in the relationship between shadow IT usage and individual performance: a social presence theory perspective", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "427", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Generative mechanisms of workarounds, discontinuance and reframing: a study of negative disconfirmation with consumerised\n IT", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "3", "Page": "384", "JournalTitle": "Information Systems Journal"}, {"Title": "Towards a theoretical understanding of workarounds emerging from use of a referral mobile application: a developing country context", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "196", "Issue": "", "Page": "533", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 105514673, "Title": "Automatic crack detection in the pavement with lion optimization algorithm using deep learning techniques", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-022-10724-z", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Image-based porosity classification in Al-alloys by laser metal deposition using random forests", "Authors": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON>Oroz<PERSON>; <PERSON><PERSON><PERSON>-Medina", "PubYear": 2020, "Volume": "110", "Issue": "9-10", "Page": "2827", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A Novel UHF RFID Sensor Based Crack Detection Technique for Coal Mining Conveyor Belt", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "", "Page": "19", "JournalTitle": "IEEE Journal of Radio Frequency Identification"}, {"Title": "Image processing-based automatic detection of asphalt pavement rutting using a novel metaheuristic optimized machine learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "20", "Page": "12839", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 105514685, "Title": "AIE biofluorescent probe based on twisted cucurbit[14]uril for the detection of Fe(CN)63- anion in solutions and live kidney cells", "Abstract": "The kidney is one of the important organs for removing metabolites to maintain the stability of the internal environment. In this study, an AIE effect supramolecular fluorescent probe ( [email&#160;protected] t Q[14]) was constructed based on twisted cucurbit[14]uril ( t Q[14]) and a triphenylamine derivative (TPAPy) through host-guest interaction. T Q[14] could effectively inhibit the rotation of the TPAPy molecule, which triggered the AIE effect. This effect was manifested as the emission of bright yellow fluorescence. [email&#160;protected] t Q[14] could ultrasensitively detect potential cyanide (K<sub>3</sub>[Fe{CN}<sub>6</sub>]) in an aqueous solution at a detection limit as low as 3.28 × 10<sup>−7</sup> M. The unique nature of the positive charge of [email&#160;protected] t Q[14] could rapidly cross the glomerular filtration barrier into HK-2 cells. In addition, this probe was successfully applied to distinguish excess Fe(CN)<sub>6</sub><sup>3−</sup> in HK-2 cells for cell imaging, providing a new detection method for early clinical identification of acute kidney injury (AKI).", "Keywords": "Cucurbit[ n ]urils ; Acute kidney injury ; Sensitive Detection ; Potential cyanide ; Self-assembly ; t Q[14] twisted cucurbit[14]uril ; AKI acute kidney injury ; TPAPy triphenylamine derivative ; AIE aggregation-induced emission ; K<sub>3</sub>[Fe{CN}<sub>6</sub>] Potassium ferricyanide", "DOI": "10.1016/j.snb.2022.133255", "PubYear": 2023, "Volume": "379", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory Breeding Base of Green Pesticide and Agricultural Bioengineering, Key Laboratory of Macrocyclic and Supramolecular Chemistry of Guizhou Province, Institute of Applied Chemistry, Guizhou University, Guiyang 550025, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Nephrology, Children’s Hospital of Chongqing Medical University, National Clinical Research Center for Child Health and Disorders, Ministry of Education Key Laboratory of Child Development and Disorders, Chongqing Key Laboratory of Pediatrics, Chongqing 400014, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory Breeding Base of Green Pesticide and Agricultural Bioengineering, Key Laboratory of Macrocyclic and Supramolecular Chemistry of Guizhou Province, Institute of Applied Chemistry, Guizhou University, Guiyang 550025, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Nephrology, Children’s Hospital of Chongqing Medical University, National Clinical Research Center for Child Health and Disorders, Ministry of Education Key Laboratory of Child Development and Disorders, Chongqing Key Laboratory of Pediatrics, Chongqing 400014, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Nephrology, Children’s Hospital of Chongqing Medical University, National Clinical Research Center for Child Health and Disorders, Ministry of Education Key Laboratory of Child Development and Disorders, Chongqing Key Laboratory of Pediatrics, Chongqing 400014, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory Breeding Base of Green Pesticide and Agricultural Bioengineering, Key Laboratory of Macrocyclic and Supramolecular Chemistry of Guizhou Province, Institute of Applied Chemistry, Guizhou University, Guiyang 550025, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "State Key Laboratory Breeding Base of Green Pesticide and Agricultural Bioengineering, Key Laboratory of Macrocyclic and Supramolecular Chemistry of Guizhou Province, Institute of Applied Chemistry, Guizhou University, Guiyang 550025, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory Breeding Base of Green Pesticide and Agricultural Bioengineering, Key Laboratory of Macrocyclic and Supramolecular Chemistry of Guizhou Province, Institute of Applied Chemistry, Guizhou University, Guiyang 550025, China;Correspondence to: Key Laboratory of Macrocyclic and Supramolecular Chemistry of Guizhou Province, Department of Chemistry, Guizhou University, Guiyang 550025, China"}], "References": [{"Title": "A novel aggregation induced emission (AIE) fluorescence probe by combining tetraphenylethylene and 2′,3′-O-isopropylideneadenosine for localizing Golgi apparatus", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129245", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "tQ[14]-based AIE supramolecular network polymers as potential bioimaging agents for the detection of Fe3+ in live HeLa cells", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "354", "Issue": "", "Page": "131189", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Supramolecular fluorescence nanoprobe loaded with azobenzene for the detection of azoreductase: Selective light-up of hypoxic cells", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "363", "Issue": "", "Page": "131860", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 105514720, "Title": "Sistem pengawasan kelembaban tanah dan penyiraman tanaman otomatis berbasis iot via telegram", "Abstract": "<p><PERSON><PERSON> adalah salah satu jenis organisme yang umum ditanam oleh orang dan tanaman ini membutuhkan air dalam melakukan pertumbuhan, yang akhirnya proses pertumbuhan tanaman sangat berpengaruh terhadap banyaknya air serta unsur lain yang dapat diserap secara baik oleh tanaman dalam melakukan proses pertumbuhan. Tanaman bisa mendapatkan air melewati penyerapan yang dilakukan oleh akar,namun tidak semua tanaman dapat melakukan penyerapan air menggunakan akarkarena terdapat beberapa tanaman yang memiliki akar pendek. Dal<PERSON> menjaga kelembaban tanah tetap stabil tentunya dibutuhkan perawatan khusus dengan cara penyiraman tanaman secara rutin dengan mengawasi tingkat kadar air. Saat ini, pengawasan proses penyiraman pada tanaman masih dilakukan secara manual. Proses secara manual masih memiliki beberapa kekurangan, diantaranya masih dibutuhkannya banyak tenaga manusia untuk mengawasi proses penyiraman, keuangan yang dikeluarkan masih terbilang tinggi. Atas dasar tersebut maka penulis memiliki tujuan untuk melakukan penelitian secara langsung agar dapat memberikan jalan keluar atau solusi yaitu dengan merancang suatu system alat yang dapat mendeteksi kelembaban tanah dan dapat juga melakukan penyiram tanaman secara otomatis memanfaatkan teknologi berjenis IoT via telegram bot, dari perencanaan penelitian tersebut didapat hasil: Sensor Kelembaban Tanah yaitu Soil Moisture Sensor yang dapat memiliki fungsi sebagai input data kelembaban tanah sesuai dengan keadaan, dan informasi dari sesnsor tersebut dapat diinformasikan kepada pengguna sistem atau pemilik lahan memalui telegram bot dan pemilik lahan dapat menyalakan pompa air yang sudah terpasang dalam sistem melalui telegram bot untuk melakukan penyiraman jika lahan yang dipasangi sistem dinyatakan kering oleh sesnsor. Sistem tersebut nantinya berfungsi untuk mengawasi proses penyiraman lahan pada tanaman secara otomatis berdasarkan dengan perintah pengguna sistem dilihat dari keadaan dan kondisi lahan. Dengan hadirnya sistem yang penulis rancang maka proses penyiraman lahan pada tumbuhan atau tanaman menggunakan telegram bot akan dapat meningkatkan efektivitas dan efesiensi para pemilik lahan dalam hal ini petani agar bisa memonitor kelembaban tanah terjaga dengan baik dan hasil tanamannya menjadi jauh lebih maksimal.</p>", "Keywords": "", "DOI": "10.37859/coscitech.v3i3.4429", "PubYear": 2022, "Volume": "3", "Issue": "3", "JournalId": 83284, "JournalTitle": "Jurnal CoSciTech (Computer Science and Information Technology)", "ISSN": "2723-567X", "EISSN": "2723-5661", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Nusa Putra"}], "References": []}, {"ArticleId": 105514803, "Title": "Business model innovation and development path selection of international cultural trade under circular economy", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCSYSE.2022.10053096", "PubYear": 2022, "Volume": "7", "Issue": "2", "JournalId": 28963, "JournalTitle": "International Journal of Computational Systems Engineering", "ISSN": "2046-3391", "EISSN": "2046-3405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105514836, "Title": "<PERSON><PERSON><PERSON> Indeks Glikemi<PERSON> Na<PERSON> Put<PERSON> dengan Penambahan Ekstrak Serai dan <PERSON>", "Abstract": "<p>Diabetes mellitus (DM) is one of the non-communicable diseases that could be prevented by consumption of foods capable of maintaining blood glucose at a safe level. Phenolic compounds are  components in food that affect blood glucose levels. Lemongrass and bay leaf are Indonesian spices commonly used for cooking and contain phenolic compounds that have potential as antidiabetic compounds. The aim of this study was to evaluate the effect of adding lemongrass and bay leaves water extracts on the GI value of cooked white rice. Lemongrass and bay leaves containing phenolic compounds were extracted with water and added to white rice during the cooking process or sprayed on cooked rice. The glycemic index of the tested food measured using the ISO 26642 method showed that the addition of lemongrass extract and a combination of lemongrass and bay leaf extract with total phenolic content (TPC) of 570 mg GAE/100 g and 565 mg GAE/100 g, respectively, on cooked white rice IR 64 resulted in the GI reduction in the cooked rice by 23 and 27%, respectively. These reduction was higher than those resulted from the addition of lemon grass or the combination of lemon grass and bay leaf extract during the cooking process, i.e. 9 and 13%, respectively.</p>", "Keywords": "glycemic index;Indonesian bay leaves;lemongrass;total phenolic content", "DOI": "10.6066/jtip.2022.33.2.169", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}], "References": []}, {"ArticleId": 105514950, "Title": "A Study on Aspect-Level Sentiment Analysis Based On ACSA-CapNet", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2022.116136", "PubYear": 2022, "Volume": "11", "Issue": "6", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "阁阁 孙", "Affiliation": ""}], "References": []}, {"ArticleId": 105514969, "Title": "Optimization of chest X-ray exposure factors using machine learning algorithm", "Abstract": "A better quality radiographic image helps the radiologist to make a proper diagnosis of the disease. In general, the use of more radiation provides a better quality image, but it gives the patient a higher radiation dose, which shows the need for optimization of imaging conditions to minimize the risk to patients from excessive radiation exposure. In this study, the chest X-ray exposure factors for 178 patients with different body mass index (BMI) values have been analyzed using the Python Machine Learning algorithm. Patient data were collected from the King <PERSON> bin Abdulaziz University Hospital, Saudi Arabia. The role of BMI in the selection of radiation exposure factors (kVp, mAs) was evaluated. It has been found that the BMI of each patient has specific exposure factors, and that if it gets higher than the specific value it could harm the patient&#x27;s health. The obtained results provide detailed information about the relation between BMI and optimal chest X-ray exposure factors without affecting the quality of the X-ray image.", "Keywords": "Exposure factors ; Body mass index ; Chest X-ray ; Machine learning", "DOI": "10.1016/j.jrras.2022.100518", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Radiological Sciences, College of Health and Rehabilitation Sciences, Princess <PERSON><PERSON><PERSON>rahman University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, College of Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Applied College, Shaqra University, Shaqra, 15551, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, College of Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, College of Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, College of Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, College of Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh 11671, Saudi Arabia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Applied Physics and Radiation Technologies, School of Engineering and Technology, Sunway University, Bandar Sunway, 47500, Malaysia;Department of General Educational Development, Faculty of Science and Information Technology, Daffodil International University, DIU Rd, Dhaka, 1341, Bangladesh;Corresponding author. Centre for Applied Physics and Radiation Technologies, School of Engineering and Technology, Sunway University, Bandar Sunway 47500, Malaysia"}], "References": []}, {"ArticleId": 105515060, "Title": "An Improved Algorithm for Label Propagation Based on Rough Core", "Abstract": "<p>Complex networks are a hot topic in social science research today. Analyzing the structure of the relationships embedded in them through community discovery techniques is an important tool for the study of complex networks. To address the randomness problem of label propagation in label propagation algorithms, this paper proposes an improved algorithm for label propagation based on rough kernels. The algorithm first performs the extraction of rough kernels and assigns unique labels to each rough kernel to achieve fast label preprocessing; then intervenes in the label propagation process through the ranking of node influence to reduce its randomness and make the division results more stable; finally, the community division is performed by the distribution of network labels. Experiments are conducted in real networks, and the results show that the combined performance of the method in this paper is better than the traditional labeling algorithm in terms of modularity and NMI metrics, and the proposed improved algorithm leads to improved community division results.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.051212", "PubYear": 2022, "Volume": "5", "Issue": "12", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105515077, "Title": "A cross-view UAV image localization method based on directional feature alignment and visual transformer", "Abstract": "<p>Using satellite images and UAV images to locate the same geographical target can provide new ideas for UAV positioning and navigation. However, the images from the two different remote sensing platforms, UAV and satellite, have a huge difference in appearance, which is a huge challenge for this task. Existing methods are usually limited to convolutional neural networks, leading to a lack of utilization of global information of images, and these methods do not focus on the spatial information of images. To address these issues, this research propose a method that extracts posture information using SFM(Structure-from-Motion) and then uses the posture to align the spatial features of the image, and introduces a visual transformer to focus the network on acquiring the common feature space shared by the viewpoint sources. In this study, a large number of experiments have been carried out on the large datum dataset University-1652. The experimental results show that the method proposed in this paper outperforms the baseline and has the same advantages as other advanced methods.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.051204", "PubYear": 2022, "Volume": "5", "Issue": "12", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105515125, "Title": "Cue prompt adapting model for relation extraction", "Abstract": "Prompt-tuning models output relation types as verbalised-type tokens instead of predicting the confidence scores for each relation type. However, existing prompt-tuning models cannot perceive named entities of a relation instance because they are normally implemented on raw input that is too weak to encode the contextual features and semantic dependencies of a relation instance. This study proposes a cue prompt adapting (CPA) model for relation extraction (RE) that encodes contextual features and semantic dependencies by implanting task-relevant cues in a sentence. Additionally, a new transformer architecture is proposed to adapt pre-trained language models (PLMs) to perceive named entities in a relation instance. Finally, in the decoding process, a goal-oriented prompt template is designed to take advantage of the potential semantic features of a PLM. The proposed model is evaluated using three public corpora: ACE, ReTACRED, and Semeval. The performance achieves an impressive improvement, outperforming existing state-of-the-art models. Experiments indicate that the proposed model is effective for learning task-specific contextual features and semantic dependencies in a relation instance.", "Keywords": "Relation extraction ; Prompt tuning ; Pre-trained language model", "DOI": "10.1080/09540091.2022.2161478", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 9414, "JournalTitle": "Connection Science", "ISSN": "0954-0091", "EISSN": "1360-0494", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Text Computing & Cognitive Intelligence Engineering Research Center of National Education Ministry, State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Text Computing & Cognitive Intelligence Engineering Research Center of National Education Ministry, State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Text Computing & Cognitive Intelligence Engineering Research Center of National Education Ministry, State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, People's Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Text Computing & Cognitive Intelligence Engineering Research Center of National Education Ministry, State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, People's Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, Xi'an Jiaotong University, Xi'an, People's Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Xi'an Jiaotong University, Xi'an, People's Republic of China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Text Computing & Cognitive Intelligence Engineering Research Center of National Education Ministry, State Key Laboratory of Public Big Data, College of Computer Science and Technology, Guizhou University, Guiyang, People's Republic of China"}], "References": [{"Title": "Semantic relation extraction using sequential and tree-structured LSTM with attention", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "183", "JournalTitle": "Information Sciences"}, {"Title": "Representation iterative fusion based on heterogeneous graph neural network for joint entity and relation extraction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "219", "Issue": "", "Page": "106888", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Joint extraction of entities and overlapping relations using source-target entity labeling", "Authors": "Tingting Hang; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "114853", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Joint extraction of entities and relations via an entity correlated attention neural model", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "581", "Issue": "", "Page": "179", "JournalTitle": "Information Sciences"}, {"Title": "Entity and relation collaborative extraction approach based on multi-head attention and gated mechanism", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "1", "Page": "670", "JournalTitle": "Connection Science"}, {"Title": "Novel target attention convolutional neural network for relation classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "597", "Issue": "", "Page": "24", "JournalTitle": "Information Sciences"}, {"Title": "Knowledge guided distance supervision for biomedical relation extraction in Chinese electronic medical records", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "204", "Issue": "", "Page": "117606", "JournalTitle": "Expert Systems with Applications"}, {"Title": "PTR: Prompt Tuning with Rules for Text Classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "182", "JournalTitle": "AI Open"}]}, {"ArticleId": 105515127, "Title": "Requirements and architecture design for cloud PaaS orchestrator", "Abstract": "Cloud technologies provide abilities for simple and reliable scaling of resources, due to which they have become widespread. The task of managing distributed services in a cloud environment is especially relevant today. Special programs are used for that purpose named “orchestrators” which implement the functions of lifecycle management for applications. However, the existing solutions have many limitations and are not applicable in the general case. Also there is no single standard or protocol for interaction with such tools which requires adaptation of programs for each particular case. The main objectives of this paper are to identify the requirements for a platform-level cloud computing (PaaS) orchestrator, as well as to propose flexible architecture patterns for such tools.", "Keywords": "cloud computing;orchestration;PaaS;distributed systems;TOSCA;OCCI;облачные вычисления;оркестрация;распределенные системы", "DOI": "10.15514/ISPRAS-2022-34(4)-15", "PubYear": 2022, "Volume": "34", "Issue": "4", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}], "References": []}, {"ArticleId": 105515129, "Title": "Experimental evaluation of the text documents marking algorithm based on interword distances shifting", "Abstract": "The article presents the experimental parameter evaluation results of the electronic documents marking algorithm, based on interword distances shifting. The developed marking algorithm is designed to increase the security of electronic documents containing textual information from leakage through channels caused by printing, scanning or photographing, followed by sending the generated image. The algorithm analyzed parameters are such characteristics as embedding capacity, invisibility, undetectability, extractability and robustness. In the course of embedding capacity estimation of the developed algorithm, analytical expressions are given that make it possible to calculate the maximum achievable embedding capacity value. The obtained quantitative estimates and the experiments carried out made it possible to substantiate the admissible values choice of the embedded marker. To determine the embedded information invisibility in the source document, an invisibility and undetectability assessment of the embedded marker was carried out. During the expert evaluation, the developed algorithm invisibility to visual analysis was substantiated, as well as the absence of significant statistical deviations in the distribution of the analyzed parameters in the process of assessing the resistance of the developed marking algorithm to the potentially best steganographic analysis method. The quantitative extractability of the developed marking algorithm was carried out by assessing the extraction accuracy. The analysis performed showed accuracy high values of marker extraction from scanned images, which makes it possible to reliably extract embedded data, as well as determine directions for improving the extraction accuracy from photographed images. In the assessing process the stability of the developed marking algorithm to the transformations implementation and distortions introduction, the main robustness parameters of the developed marking algorithm to the printing, scanning and photographing processes are determined. Conclusions are formulated on the using possibility the developed marking algorithm and directions for further researches are identified.", "Keywords": "information leakage protection;marking;pattern recognition;image processing;steganographic analysis;защита от утечки информации;маркирование;распознавание образов;обработка изображений;стеганографический анализ", "DOI": "10.15514/ISPRAS-2022-34(4)-11", "PubYear": 2022, "Volume": "34", "Issue": "4", "JournalId": 9407, "JournalTitle": "Proceedings of the Institute for System Programming of the RAS", "ISSN": "2079-8156", "EISSN": "2220-6426", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Академия Федеральной службы охраны Российской Федерации"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Академия Федеральной службы охраны Российской Федерации"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Академия Федеральной службы охраны Российской Федерации"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Институт системного программирования им. В.П. Иванникова РАН"}], "References": []}, {"ArticleId": 105515148, "Title": "Fog and Cloudiness Monitoring Using Global Navigation Satellite System Precipitable Water Vapor", "Abstract": "Fog is formed when water vapor in the atmosphere condenses into miniscule floating water droplets, rendering a visibility of less than 1 km. Fog causes economic damage and poses a hazard to people owing to limited visibility. Fog forecasts depend on the judgment and experience of the forecaster, who bases the forecast on analysis results from meteorological models and satellite images. However, such forecasts are complicated by spatiotemporal changes and fine physical processes. To address this issue, a variety of fog studies have used the Global Navigation Satellite System (GNSS) tropospheric signal delay as an alternative. In this study, we monitored fog and cloudiness by analyzing precipitable water vapor (PWV) extracted from GNSS tropospheric delay. The analysis of the change in PWV during fog generation showed that it is difficult to detect fog from PWV using GNSS; more precise GNSS processing is required. However, it was found that cloudiness affects the change in PWV. In particular, the change in PWV is affected by high-level and middle-level clouds rather than low-level clouds. In other words, we conducted research to detect fog and cloudiness, but it was difficult to monitor them using PWV calculated from GNSS, although the use of GNSS to monitor the high-level and middle-level clouds generated good results. The results of this study will aid in quantitative cloudiness measurements, which at present are mostly performed through non-quantitative visual inspection. Furthermore, these results are expected to be helpful in the development of automatic observation devices for cloudiness measurements, satellite image processing, and weather forecasting. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "cloudiness; fog; GNSS; precipitable water vapor (PWV); tropospheric delay", "DOI": "10.18494/SAM3954", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geoinformatics, University of Seoul, 163 Seoulsiripdae-ro, Dongdaemun-gu, Seoul, 02504, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Geoinformatics, University of Seoul, 163 Seoulsiripdae-ro, Dongdaemun-gu, Seoul, 02504, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Geodesy, National Geographic Information Institute, 92 Worldcup-ro, Yeongtong-gu, Suwon-si, Gyeonggi-do443-772, South Korea"}], "References": []}, {"ArticleId": 105515169, "Title": "Multi-path Back-propagation Method for Neural Network Verification", "Abstract": "", "Keywords": "", "DOI": "10.21655/ijsi.1673-7288.00281", "PubYear": 2022, "Volume": "12", "Issue": "4", "JournalId": 87794, "JournalTitle": "International Journal of Software and Informatics", "ISSN": "1673-7288", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen 518060, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105515214, "Title": "Dual-Horizon Reciprocal Collision Avoidance for Aircraft and Unmanned Aerial Systems", "Abstract": "<p>The aircraft conflict detection and resolution problem has been addressed with a wide range of centralised methods in the past few decades, e.g. constraint programming, mathematical programming or metaheuristics. In the context of autonomous, decentralized collision avoidance without explicit coordination, geometric methods provide an elegant, cost-effective approach to avoid collisions between mobile agents, provided they all share a same logic and a same view of the traffic. The Optimal Reciprocal Collision Avoidance (ORCA) algorithm is a state-of-the art geometric method for robot collision avoidance, which can be used as a Detect & Avoid logic on-board aircraft or Unmanned Aerial Vehicles. However, ORCA does not handle well some degenerate situations where agents operate at constant or near-constant speeds, which is a widespread feature of commercial aircraft or fixed-winged Unmanned Airborne Systems. In such degenerate situations, pairs of aircraft could end up flying parallel tracks without ever crossing paths to reach their respective destination. The Constant Speed ORCA (CS-ORCA) was proposed in 2018 to better handle these situations. In this paper, we discuss the limitations of both ORCA and CS-ORCA, and introduce the Dual-Horizon ORCA (DH-ORCA) algorithm, where two time horizons are used respectively for short-term collision avoidance and medium-term path-crossing. We show that this new approach mitigates the main issues of ORCA and CS-ORCA and yields better performances with dense traffic scenarios.</p>", "Keywords": "Collision avoidance; Aircraft conflict resolution; Self-separation; Optimal reciprocal collision avoidance; Air traffic control; Unmanned airborne systems", "DOI": "10.1007/s10846-022-01782-2", "PubYear": 2023, "Volume": "107", "Issue": "1", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ENAC, Université de Toulouse, Toulouse, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ENAC, Université de Toulouse, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ENAC, Université de Toulouse, Toulouse, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "ONERA DTIS, Université de Toulouse, Toulouse, France"}], "References": [{"Title": "Distributed Reactive Model Predictive Control for Collision Avoidance of Unmanned Aerial Vehicles in Civil Airspace", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "1", "Page": "185", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "Repulsion-Oriented Reciprocal Collision Avoidance for Multiple Mobile Robots", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}]}, {"ArticleId": 105515286, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0167-6911(22)00224-9", "PubYear": 2023, "Volume": "171", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [], "References": []}, {"ArticleId": 105515351, "Title": "Undersampling of approaching the classification boundary for imbalance problem", "Abstract": "<p>Using imbalanced data in classification affect the accuracy. If the classification is based on imbalanced data directly, the results will have large deviations. A common approach to dealing with imbalanced data is to re-structure the raw dataset via undersampling method. The undersampling method usually uses random or clustering approaches to trimming the majority class in the dataset, since some data in the majority class makes not contribute to classification model. In this paper a revised undersampling approach is proposed. First, we perform space compression in the vertical direction of the separating hyperplane. Then, a weighted random sampling hybrid ensemble learning method is carried out to make the sampled objects spread more widely near the separating hyperplane. Experiments with 7 under-sampling methods on 21 imbalanced datasets show that our method has achieved good results.</p>", "Keywords": "classification;imbalanced data;separation hyperplane;undersampling", "DOI": "10.1002/cpe.7586", "PubYear": 2023, "Volume": "35", "Issue": "6", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Processing and Networked Manufacture Hunan University of Science and Technology  Xiangtan China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Processing and Networked Manufacture Hunan University of Science and Technology  Xiangtan China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Knowledge Processing and Networked Manufacture Hunan University of Science and Technology  Xiangtan China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Processing and Networked Manufacture Hunan University of Science and Technology  Xiangtan China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Knowledge Processing and Networked Manufacture Hunan University of Science and Technology  Xiangtan China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science State University of New York  New Paltz USA"}], "References": [{"Title": "Radial-Based Undersampling for imbalanced data classification", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107262", "JournalTitle": "Pattern Recognition"}, {"Title": "Resampling ensemble model based on data distribution for imbalanced credit risk evaluation in P2P lending", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "536", "Issue": "", "Page": "120", "JournalTitle": "Information Sciences"}, {"Title": "On the class overlap problem in imbalanced data classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106631", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "EUSC: A clustering-based surrogate model to accelerate evolutionary undersampling in imbalanced classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107033", "JournalTitle": "Applied Soft Computing"}, {"Title": "CSS\n : Handling imbalanced data by improved clustering with stratified sampling", "Authors": "Lu Cao; Hong Shen", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "e6071", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Kernel local outlier factor‐based fuzzy support vector machine for imbalanced classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "13", "Page": "e6235", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Constructing classifiers for imbalanced data using diversity optimisation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "565", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "A review of methods for imbalanced multi-label classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "107965", "JournalTitle": "Pattern Recognition"}, {"Title": "Imbalanced classification: A paradigm‐based review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "5", "Page": "383", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}, {"Title": "Boosting the oversampling methods based on differential evolution strategies for imbalanced learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "112", "Issue": "", "Page": "107787", "JournalTitle": "Applied Soft Computing"}, {"Title": "A Gaussian process‐based approach toward credit risk modeling using stationary activations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "e6692", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Imbalanced data classification: A KNN and generative adversarial networks-based hybrid approach for intrusion detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "240", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 105515551, "Title": "Characterization of Chemical Properties of Inulin Isolated from Yacón Tuber", "Abstract": "<p>Inulin is a polysaccharide with linear fructan chain structure. Food industries widely use inulin as a low calorie sugar substitutions. Yacón (Smallanthus sonchifolius) tuber that are easily grown and contain higher inulin can be explored as local inulin sources. This research aimed to characterize the chemical properties of inulin isolated from yacón tuber. The methods of this study include proximate analysis of yacón tuber, inulin isolation, and characterization of purified inulin using FTIR. The results showed that yacón consisted  of 91.23 water (analyzed as moisture), 0.12 proteins, 0.58 fats, 0.52 crude fibers, and 7.34% carbohydrates. The yield of inulin extracted from yacón tuber was 4.86% whereas its purity and actual content of inulin were 44.23 and 2.15%, respectively. The Osazon test revealed that the isolated inulin has similar crystalline with that from chicory. Based on characterization using the FTIR spectrophotometer, the isolated inulin had functional groups of C-O, C-H, CH2, O-H, and C=O. The spectrum of the isolated inulin has similarities with chicory inulin. Therefore, yacón tuber could be considered as a potential local inulin source in Indonesia.</p>", "Keywords": "chemical characterization;inulin;prebiotics;tuber;yacón", "DOI": "10.6066/jtip.2022.33.2.111", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biochemistry, Faculty of Mathematics and Natural Sciences, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Biochemistry, Faculty of Mathematics and Natural Sciences, IPB University, Bogor, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biochemistry, Faculty of Mathematics and Natural Sciences, IPB University, Bogor, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biochemistry, Faculty of Mathematics and Natural Sciences, IPB University, Bogor, Indonesia"}], "References": []}, {"ArticleId": 105515562, "Title": "Early detection of liver disorders using hybrid soft computing techniques for optimal feature selection and classification", "Abstract": "<p>Human liver disease is a sort of illness that starts in the ovaries and is particularly dangerous for women. As a consequence, aberrant cells develop that have the potential to spread to other parts of the body. Liver disease is a serious disorder that affects women's ovaries and is difficult to identify early on, which is why it is still one of the leading causes of mortality. The significance of unequivocal confirmation of intrinsic and typical components in establishing new frameworks to detect and eliminate danger is substantial. In this study, we present a hybrid soft computing approach for early diagnosis of liver problems (EDLD-HS). For high and low level feature extraction models, we first employ an improved ant swarm optimization (IASO) approach. Then, for optimum feature selection, we develop a modified whale search optimization (MWSO) technique that combines features that may reflect both texture patterns and semantic backdrop scattered in the data. After that, we employed a hybrid swallow swarm intelligent-deep neural network (HSSI-DNN) classifier to determine the stage of liver illness. Finally, we used MATLAB R2014a to test our proposed EDLD-HS method using well-known benchmark datasets including BUPA, ILPD, and MPRLPD. The simulation findings are compared to existing state-of-the-art methodologies in terms of accuracy, specificity, sensitivity, precision, recall, F1-score, G-mean, and area under curve (AUC). The detection accuracy of the proposed HSSI-DNN classifier is 83.26% (BUPA), 84.26% (ILPD), and 91.23% (ILPD) (MPRLPD).</p>", "Keywords": "classification;early detection;feature extraction;liver disease;optimal feature selection", "DOI": "10.1002/cpe.7576", "PubYear": 2023, "Volume": "35", "Issue": "6", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of computing and information technology REVA University  Bangalore India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science and Engineering SJB Institute of Technology  Bangalore India"}], "References": [{"Title": "Liver disorder detection using variable- neighbor weighted fuzzy K nearest neighbor approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "11", "Page": "16515", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 105515599, "Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON> dan <PERSON> Bogor Asal Jampang-Suka<PERSON><PERSON> Bar<PERSON>", "Abstract": "<p>The traditional cultivation and limited use of bambara groundnut (Vigna subterranea) seed and coat have encouraged the development of this commodity. The aim of this research was to characterize the seed and coat of bambara groundnut from Jampang, Kab. Sukabumi, West Java. Analysis on bambara groundnut seed including proximate analysis, in vitro protein digestibility, starch content, and dietary fiber, as well as analysis on its coat including anthocyanin, total phenolic, antioxidant activity, phytic acid and tannin, were examined in this study. The results showed that bambara groundnut seed from Jampang-Sukabumi contained 16.53% proteins, 3.04% ash, 7.83% fats and 55.22% carbohydrates in dry basis (db). The carbohydrates consisted of starch 52.71% and dietary fiber 7.47% (db). The protein had an in vitro protein digestibility of 41.65% db. The purple seed coat contained of 1.51% anthocyanin, 25.85 mg/g total phenolic content (as gallic acid equivalent), antioxidant activity at 82.75% inhibition of free radical DPPH, 6.37 mg/g phytic acid, and 96.79 mg/g tannin (as tannic acid equivalent) in dry basis. The relatively high content of tannin and antioxidant activity but very low phytic acid content, make the bambara seed coat a potential source for tannin, meanwhile the bambara groundnut is potential as a nutrition source.</p>", "Keywords": "antioxidant;bambara groundnut;food nutrition;proximate composition", "DOI": "10.6066/jtip.2022.33.2.178", "PubYear": 2022, "Volume": "33", "Issue": "2", "JournalId": 36981, "JournalTitle": "Jurnal Teknologi dan Industri Pangan", "ISSN": "1979-7788", "EISSN": "2087-751X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 2, "Name": "Nurheni Sri Palupi", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departemen Ilmu dan Te<PERSON>logi <PERSON>gan, Fakultas Teknologi Pertanian, IPB University, Bogor, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Bogor, Indonesia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Program Studi Ilmu dan Te<PERSON>log<PERSON>gan, Fakultas Teknik dan <PERSON><PERSON>, Universitas Bakrie, Jakarta, Indonesia"}], "References": []}, {"ArticleId": 105515623, "Title": "Data Model Design of Underground Utilities Using General Feature Model in Korea", "Abstract": "In this study, we present a logical spatial data model according to the International Organization for Standardization (ISO) 19109 targeting nine types of underground utilities (wide-area water, water, sewage, heating, communication, electric power, gas, garbage, and oil) among underground information. To this end, we reviewed the integrated underground utility systems in Korea, the integrated underground spatial maps, and previous studies in South Korea and abroad. Subsequently, the design direction of the underground utility data model is constructed on the basis of the problems derived from the review of previous studies and current status. According to the design direction, the terminologies of underground utilities, classification systems, and topological association between objects are defined. The ISO and Open Geospatial Consortium (OGC) standards established as international standards are referenced to design applicable spatial data models suitable for Korea's underground space environment using Unified Modeling Language (UML) diagrams. The resulting design is expected to provide consistency to the underground utility data and play a role in the interoperability of domestic and foreign underground utility data. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "data model; GFM; Korea; underground utilities", "DOI": "10.18494/SAM4053", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Smart Urban Space Institute, Anyang University, 22, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 37 be<PERSON>-gil, Manan-gu, Gyeonggi-do, Anayang-si, 14028, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Smart City Engineering, Anyang University, 22, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 37 beon-gil, Manan-gu, Gyeonggi-do, Anayang-si, 14028, South Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dept. of Smart City Engineering, Anyang University, 22, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 37 beon-gil, Manan-gu, Gyeonggi-do, Anayang-si, 14028, South Korea"}], "References": []}, {"ArticleId": 105515646, "Title": "X-ray sensing characteristics of a spin-coated n-ZnO film", "Abstract": "We report X-ray sensing characteristics of a thick n -ZnO film. The film was synthesised using a spin-coating method from a specific solution of zinc acetate dihydrate, ethanol and triethanolamine (TEA). An X-ray diffraction analysis confirmed that the film has crystalline nature having hexagonal crystal structure. The thickness of the film was determined using a scanning electron microscope (SEM) and found to be ∼29–45 µm. An analysis of absorption spectrum revealed the optical bandgap of the sample to be 3.45 eV. The current-voltage ( I-V ) characteristics were recorded under dark condition and X-ray (at 30 keV, 8 mA) illumination condition. The resistivity of the sensor under the dark condition at bias voltage 2.0 V was found to be 6.4 × 10<sup>7</sup> Ωcm and that under the X-ray illumination, 4.8 × 10<sup>6</sup> Ωcm. The sensor shows high signal-to-noise ratio within 0.5–6.0 V bias voltage. The analysis of response time of the sensor shows a rise time of 0.87 s and fall time of 1.08 s at 3.0 V bias voltage. The sensor is also found to be visible blind within 370–700 nm. A quantitative analysis of photoresponse characteristics with dose rate reveals that the sensor shows sublinear response between 0.015 and 0.220 Gy/s. The sensitivity for 0.220 Gy/s dose rate at a bias voltage of V = 6.0 V is found to be ∼ 155.1 μ C . G y − 1 c m − 3 . The sensor shows a pseudo rectifying type I-V characteristic under dark condition. The I-V characteristic under dark condition was further analysed quantitatively using space-charge-limited current (SCLC) model.", "Keywords": "ZnO film ; X-ray sensor ; Pseudo rectifying nature ; Space-charge-limited current (SCLC) ; Ohmic contact ; Response time", "DOI": "10.1016/j.sna.2022.114142", "PubYear": 2023, "Volume": "350", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Cotton University, Guwahati 781001, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Physics, Cotton University, Guwahati 781001, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Physics, Cotton University, Guwahati 781001, India"}], "References": [{"Title": "High sensitivity X-ray detector based on a 25 µm-thick ZnO film", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "334", "Issue": "", "Page": "113310", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 105515718, "Title": "Calculation and Comparison of Earthwork Volume Using Unmanned Aerial Vehicle Photogrammetry and Traditional Surveying Method", "Abstract": "Point cloud data obtained by unmanned aerial vehicle (UAV) photogrammetry were used as the basis for creating a 3D model. In this study, we calculated earthwork volume using a 3D model based on such point cloud data at a construction site. An expressway construction site was selected as the study area, and UAV photogrammetry was performed three times, and for comparison purposes, traditional surveying was also conducted the same number of times. Comparison of the earthwork volume calculated by the two methods showed that the volume calculated by UAV photogrammetry was larger in the range of 2.36-2.51% than that obtained by the traditional surveying method (TSM). Therefore, it was found that the method of calculating the earthwork volume at the construction site could be changed to UAV photogrammetry. In addition, comparison of the work efficiency of the two methods showed that UAV photogrammetry in the first work in the study area consumed twice the work time and 1.4 times the work cost over TSM; however, from the second work onward in the study area, the work time and work cost were reduced by approximately half. Therefore, it was found that work efficiency improved as the number of jobs increased. © 2022 M Y U Scientific Publishing Division. All rights reserved.", "Keywords": "3D model; earthwork volume; point cloud data; traditional surveying method; UAV photogrammetry", "DOI": "10.18494/SAM4192", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil & Infrastructure Engineering, College of Construction and Environmental Engineering, Gyeongsang National University, 33 Dongjin-ro, Gyeongsangnam-do, Jinju, 52725, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Chonnam National University, 77 Yongbongro, Bukgu, Gwangju, 61186, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ICT Convergence Research Division, Korea Expressway Corporation Research Institute, 208-96, Dongbu-<PERSON><PERSON> 922 beon-gil, Dongtan<PERSON>myeon, Hwaseong-si, Gyeonggi-do18489, South Korea"}], "References": []}, {"ArticleId": 105515840, "Title": "An IoT based environment conscious green score meter towards smart sustainable cities", "Abstract": "In today’s modern world, use of natural resources is expanding certainly because of the refined lifestyle of the individuals. The unjustifiable use of these non-renewable resources can affect the stability between environmental and biological diversity which leads to greenhouse effect. This situation requires awareness and motivation to encourage people towards sustainable energy use and conservation in order to reduce global warming. For effective implementation, government initiatives require a systematic public participation to assess household’s level of energy self-sustainability and reward accordingly. On this regard, we introduce an IoT powered green architecture to assign an optimal green score to a household by assessing the households’ level of energy self-sustainability using a novel green score algorithm based green score meter. The green score algorithm will results high green score rating to a household with high renewable energy production than the non-renewable energy consumption with less air contamination. Based the household energy data, the green score meter computes the green score rating of a household using the green score algorithm and forward the score to the authorized third party like cloud server for green rank calculation and virtual display. Here, we present a tax exemption scheme which benefits the households with high green score rating. The existing frameworks (HEMS, iCHEMS, SHEMS) monitor and aware the user/household for efficient energy management, cost management but in contrast our proposed green architecture is an integrated platform for collective energy management to evaluate energy self-sustainability of the household, so as to give financial incentives. Thus, our proposed green score based tax exemption scheme will appreciate and urge the residents to burn-through less natural assets and forestall less contamination.", "Keywords": "Internet of things ; Smart sustainable city ; Wireless sensor network ; Cloud computing", "DOI": "10.1016/j.suscom.2022.100839", "PubYear": 2023, "Volume": "37", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, E.G.S. Pillay Engineering College, Nagapattinam 611 002, Tamil Nadu, India;Corresponding author"}, {"AuthorId": 2, "Name": "Manivannan R", "Affiliation": "Department of Computer Science and Engineering, E.G.S. Pillay Engineering College, Nagapattinam 611 002, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technology-UG, Kongu Engineering College, Perundurai, Erode 638 060, Tamil Nadu, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Sona College of Technology, Salem 636 005, Tamil Nadu, India"}], "References": [{"Title": "Cloud-based robotic system for crowd control in smart cities using hybrid intelligent generic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "12", "Page": "6293", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Design and optimization of integrated energy management network system based on internet of things technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "", "Page": "100502", "JournalTitle": "Sustainable Computing: Informatics and Systems"}, {"Title": "Hybrid NN-based green cognitive radio sensor networks for next-generation IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "33", "Page": "23819", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A predictive analytics framework for Sustainable Water Governance", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "", "Page": "100604", "JournalTitle": "Sustainable Computing: Informatics and Systems"}]}, {"ArticleId": 105515905, "Title": "Study of YOLOX Target Detection Method Based on Stand-Alone Self-Attention", "Abstract": "<p>A target detection method based on an improved network of stand-alone self-attention mechanisms (YOLOX_SASA) is proposed to address the problems of complex picture backgrounds, slow detection speed, and low detection accuracy. The method firstly improves the speed of target detection by introducing the stand-alone self-attention module in the multi-scale feature fusion part of YOLOX, so that the network can increase the perceptual field while aggregating the neighborhood information. Secondly, by changing the YOLOX binary classification loss function BCE Loss to MultiLabelMargin Loss for label complementation, which in turn improves the target detection accuracy, and by introducing CutMix data enhancement in the training phase to expand the training set and increase the number of samples. Finally, to test the detection effectiveness of the algorithm, simulation experiments are conducted on a homemade small garbage classification dataset and the PASCAL VOC 2007 public dataset. The experimental results show that the method achieves an average accuracy of 93.81% based on satisfying the real-time performance, which is 4.53% better than the original YOLOX algorithm.</p>", "Keywords": "", "DOI": "10.25236/AJCIS.2022.051205", "PubYear": 2022, "Volume": "5", "Issue": "12", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105515930, "Title": "VIDEOCONFERENCING TECHNOLOGIES AND INFORMATION SECURITY", "Abstract": "<p>Рассматривается подход к обеспечению информационной безопасности в технологиях реализации видеоконференцсвязи и прочих технологиях медиакомунникаций. А также вопрос обеспечения безопасности при проведении видеоконференций и передаче данных как между двумя пользователями, так и групп пользователей. Рассматриваются типы ВКС и способы их реализаций, а также примеры программных решений, их возможностей и полного функционала. Подходы к созданию защищенного соединения канала передачи данных, а также базовые правила безопасности при организации и администрировании видеоконференций пользователей. В работе так же уделяется внимание развернутой политике сервисов по предоставлению услуг ВКС как для рядовых пользователей, так и для бюджетных и коммерческих организаций, отвечающих современным нормам информационной безопасности при проведении ВКС. Представлены, помимо традиционно рассматриваемых технических аспектов, вопросы обеспечения информационной безопасности с позиций организации проведения сессий, а также обеспечения режима конфиденциальности процессов ВКС.</p><p>An approach to ensuring information security in video conferencing and other media communication technologies is considered. As well as the issue of ensuring security during videoconferences and data transmission between two users and groups of users. The types of VCS and methods of their implementations are considered, as well as examples of software solutions, their capabilities and full functionality. Approaches to creating a secure data link connection, as well as basic security rules for organizing and administering user video conferences. The work also pays attention to the detailed policy of services for the provision of VCS services for both ordinary users and for commercial and budgetary institutions that meet modern information security standards when conducting VCS. In addition to the traditionally considered technical aspects, the issues of ensuring information security from the standpoint of organizing sessions, as well as ensuring the confidentiality of VCS processes, are presented.</p>", "Keywords": "", "DOI": "10.36622/VSTU.2022.25.3.005", "PubYear": 2022, "Volume": "", "Issue": "3(-)", "JournalId": 85292, "JournalTitle": "ИНФОРМАЦИЯ И БЕЗОПАСНОСТЬ", "ISSN": "1682-7813", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, Петр <PERSON><PERSON><PERSON>евич", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 105516073, "Title": "RETRACTED ARTICLE: An Effective Hybrid Mobility Aware Energy Efficient Low Latency Protocol (HMEL-MAC) for Wireless Sensor Network", "Abstract": "", "Keywords": "", "DOI": "10.1080/01969722.2022.2157598", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 25981, "JournalTitle": "Cybernetics and Systems", "ISSN": "0196-9722", "EISSN": "1087-6553", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, Loyola Institute of Technology, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Computational Modeling, Chennai Institute of Technology, Chennai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, SRM Institute of Science and Technology, Chennai, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Gnanamani College of Technology, Namakkal, India"}], "References": []}, {"ArticleId": 105516076, "Title": "Cultural Viewpoint Metaphors Resignification to Teach Programming", "Abstract": "<p>Technologies permeate contemporary society, and the ability to use computer science concepts in problem-solving is essential to everyone. This paper presents an epistemic tool of semiotic engineering whose meaning is resignified to the context of teaching programming to initial learners. Aiming to help these students absorb programming concepts, we structured a gradual form of presentation using the interaction with a new system as a journey through a new culture, based on the Cultural Viewpoint Metaphors theory. After that, we applied this resignification in an introductory programming workshop using visual programming and the BBC Micro:bit embedded device. Results from the workshop revealed that this gradual introduction could help novices in the programming concepts learning process, showing the potential of this approach in teaching programming.</p>", "Keywords": "", "DOI": "10.1093/iwc/iwac041", "PubYear": 2023, "Volume": "35", "Issue": "2", "JournalId": 5476, "JournalTitle": "Interacting with Computers", "ISSN": "0953-5438", "EISSN": "1873-7951", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Pará R<PERSON> <PERSON> , 01 Belém Pará Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Federal University of Pará R<PERSON> <PERSON> , 01 Belém Pará Brazil"}], "References": []}, {"ArticleId": 105516166, "Title": "Experimental research on new hole-making method assisted with asynchronous mixed frequency vibration in TiBw/TC4 composites", "Abstract": "<p>With the gradual promotion and the application of difficult-to-machine materials such as titanium matrix composites in the aerospace field, high-quality hole-making technology has become a major demand in aviation manufacturing. In order to improve the hole-making quality of TiBw/TC4 composites, asynchronous mixed frequency vibration-assisted hole-making (AMFVAHM) method is proposed. The process consists of two steps: ultrasonic vibration-assisted drilling (UVAD) base hole and low-frequency torsional vibration-assisted helical milling (LFTVAHM) target hole. Based on this process, the cutting trajectory modeling is established, and the hole-making experiment on TiBw/TC4 composites is conducted. The experimental data show that during the hole expansion stage, the maximum XY -plane average milling force decreases by 30.96% and the maximum axial average milling force decreases by 24.49% compared with conventional helical milling (HM) when the torsional vibration frequency and the milling frequency are the same in LFTVAHM. The hole-making experiment shows that AMFVAHM can reduce the chip size, tool wear, and some other defects such as entrance/exit burrs, scratches, and fractures of the hole wall. Comparing with HM and UVAD, the verticality of hole wall increases by 71.43% and 86.21%, the inlet damage decreases by 27.98% and 31.60%, the outlet damage decreases by 2.80% and 14.47%, the hole wall roughness ( R <sub> a </sub>) decreases by 36.29% and 63.43%, and the maximum white layer thickness decreases by 19.99% and 67.66%. Meanwhile, AMFVAHM process not only reduces the cutting force and cutting temperature but also improves the hole-making quality due to the fretting friction effect of LFTVAHM in secondary hole expansion, which meets the need for high-quality hole-making of difficult-to-machine materials in practical engineering applications.</p>", "Keywords": "TiBw/TC4 composites; Asynchronous mixed frequency vibration-assisted hole-making; Ultrasonic vibration-assisted drilling; Low-frequency torsional vibration-assisted helical milling; Fretting friction effect", "DOI": "10.1007/s00170-022-10754-7", "PubYear": 2023, "Volume": "125", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Nanjing Institute of Technology, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Nanjing Institute of Technology, Nanjing, China; School of Mechanical Engineering, Changshu Institute of Technology, Suzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Nanjing Institute of Technology, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Nanjing Institute of Technology, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Nanjing Institute of Technology, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Nanjing Institute of Technology, Nanjing, China"}], "References": [{"Title": "Delamination in rotary ultrasonic machining of CFRP composites: finite element analysis and experimental implementation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "9-10", "Page": "3847", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 105516168, "Title": "<PERSON><PERSON>dy on Fault Analysis Method of Optical Transmission Network Based on Deep Learning", "Abstract": "", "Keywords": "", "DOI": "10.25236/AJCIS.2022.051307", "PubYear": 2022, "Volume": "5", "Issue": "13", "JournalId": 61009, "JournalTitle": "Academic Journal of Computing & Information Science", "ISSN": "2616-5775", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 105516259, "Title": "Detecting Deepfake Videos in Data Scarcity Conditions by Means of Video Coding Features", "Abstract": "The most powerful deepfake detection methods developed so far are based on deep learning, requiring that large amounts of training data representative of the specific task are available to the trainer. In this paper, we propose a feature-based method for video deepfake detection that can work in data scarcity conditions, that is, when only very few examples are available to the forensic analyst. The proposed method is based on video coding analysis and relies on a simple footprint obtained from the motion prediction modes in the video sequence. The footprint is extracted from video sequences and used to train a simple linear Support Vector Machine classifier. The effectiveness of the proposed method is validated experimentally on three different datasets, namely, a synthetic street video dataset and two datasets of Deepfake face videos. © 2022 <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>.", "Keywords": "deep learning for forensics; Deepfake detection; video coding; video forensics", "DOI": "10.1561/116.00000032", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 10204, "JournalTitle": "APSIPA Transactions on Signal and Information Processing", "ISSN": "", "EISSN": "2048-7703", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering and Mathematics, University of Siena, Italy"}], "References": []}, {"ArticleId": 105516507, "Title": "A Survey on Hardware Performance Counter Based Malware Detection Technology", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.1212294", "PubYear": 2022, "Volume": "12", "Issue": "12", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "彦飞 户", "Affiliation": ""}], "References": [{"Title": "Dynamic Malware Analysis in the Modern Era—A State of the Art Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 105516526, "Title": "Introduction to the special section “pattern recognition for Recent and Future Advances On intelligent systems” (SS:ISPR22)", "Abstract": "Calculating global illumination in computer graphics is difficult, especially for complex scenes. This is due to the interreflections of the rays of light and the interactions with the materials of the objects composing the scene. Solutions based on ambient light approximations have been implemented. However, these are computationally intensive and produce images with less precision, as these solutions ignore the ambient lighting component by adopting coarse approximations. In this paper, we propose a method capable of approximating the global illumination effect. Our idea is to compute the global illumination by adding three images (direct illumination, environmental light, and ambient occlusion). Direct illumination is calculated by a reference method. Environmental illumination is computed using an adversarial neural network from a single 2D image. Ambient occlusion is generated using conditional adversarial neural networks with an attention mechanism to pay more attention to the relevant image features during the training step. We use two image masks to keep the object’s position in the screen space, which allows efficient reconstruction of the final result. Our solution produces quality images compared to reference images and does not require any computation in the 3D scene or screen space. Research article Conference on graphics, patterns and images Pattern Recognition Letters, Volume 166, 2023, pp. 172-173 Research article Locating robust patterns based on invariant of LTP-based features Pattern Recognition Letters, Volume 165, 2023, pp. 9-16 Show abstract Efficiently representing Dynamic Textures (DTs) based on salient features is one of the considerable challenges in computer vision. Locating these features can be obstructed due to the impact of encoding factors. In this article, a novel concept of Robust Local Ternary Patterns (RLTP) is introduced in consideration of the invariance of Local Ternary Patterns (LTP) subject to the deviation of thresholds. Our locating process is able to simultaneously encapsulate the discrimination of local features, and deal with the noise sensibility caused by a small gray-scale change of local neighbors. RLTP is then adapted to the completed LTP model to form an efficient operator for capturing completely properties of RLTP. Finally, RLTP is taken into account for the DT description, where the robust patterns of spatial-temporal features and optical-flow-based motions are exploited to improve the performance. Experiments have clearly corroborated the efficacy of our proposed approach. Research article Wavelet detail perception network for single image super-resolution Pattern Recognition Letters, Volume 166, 2023, pp. 16-23 Show abstract Single image super-resolution (SR) is an important topic in computer vision because of its ability to generate high-resolution (HR) images. Traditional SR methods do not pay attention to high-frequency detail perception in the reconstruction process, resulting in unrealistic high-frequency details of images. To address the problem of over-smoothing of details, a novel wavelet detail perception network (WDPNet) is proposed in this study. Different from traditional SR methods that directly restore high-resolution images, the proposed WDPNet decomposes images into low-frequency and high-frequency sub-images by wavelet transform and then uses different models to train these sub-images. Moreover, low-frequency structures are also provided to the high-frequency model to further recover and enhance high-frequency details through the proposed low-to-high information delivery (L2HID) and detail perception enhancement (DPE) mechanisms. Finally, the low-frequency and high-frequency models are fused and weighted to different degrees to enhance image details further. Compared with the state-of-the-art methods, the experimental results show that the proposed WDPNet achieves better performance and visual results in image detail perception. Research article Exploiting enhanced and robust RGB-D face representation via progressive multi-modal learning Pattern Recognition Letters, Volume 166, 2023, pp. 38-45 Show abstract Existing RGB-based 2D face recognition approaches are sensitive to facial variations, posture, occlusions, and illumination. Current depth-based methods have been proved to alleviate the above sensitivity by introducing geometric information but rely heavily on high-quality depth from high-cost RGB-D cameras. To this end, we propose a Progressive Multi-modal Fusion framework to exploit enhanced and robust face representation for RGB-D facial recognition based on low-cost RGB-D cameras, which also deals with incomplete RGB-D modal data. Due to the defects such as holes caused by low-cost cameras, we first design a depth enhancement module to refine the low-quality depth and correct depth inaccuracies. Then, we extract and aggregate augmented feature maps of RGB and depth modality step-by-step. Subsequently, the masked modeling scheme and iterative inter-modal feature interaction module aim to fully exploit the implicit relations among these two modalities. We perform comprehensive experiments to verify the superior performance and robustness of the proposed solution over other FR approaches on four challenging benchmark databases. View full text © 2022 Published by Elsevier B.V. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.patrec.2022.12.025", "PubYear": 2023, "Volume": "166", "Issue": "", "JournalId": 1591, "JournalTitle": "Pattern Recognition Letters", "ISSN": "0167-8655", "EISSN": "1872-7344", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON>, Tebessa, Algeria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Arkansas Tech University, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ain Chams University, Egypt"}], "References": []}, {"ArticleId": 105516532, "Title": "Inferring attitudinal spaces in social networks", "Abstract": "<p>Ideological scaling methods have shown that behavioral traces in social platforms can be used to mine opinions at a massive scale. Current methods exploit one-dimensional left–right opinion scales, best suited for two-party socio-political systems and binary social divides such as those observed in the US. In this article, we introduce a new method to overcome limitations of existing methods by producing multidimensional network embeddings and align them with referential attitudinal for a few nodes. This allows us to infer a larger set of opinion dimensions from social graphs, embedding users in spaces where dimensions stand for indicators of several social dimensions including (in addition to left–right cleavages) attitudes towards elites, or ecology among many other issues. Our method does not rely on text data and is thus language-independent. We illustrate this approach approach on a Twitter follower network. Finally, we show how our method allows us to analyze the opinions shared within various communities of social networks. Our analyses show that communities of users that have extreme political opinions are also more homogeneous ideologically.</p>", "Keywords": "Network scaling; Graph embedding; Ideology; Political attitude data; Party systems; Polarization", "DOI": "10.1007/s13278-022-01013-4", "PubYear": 2023, "Volume": "13", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Médialab, Sciences Po, Paris, France; LPI – Université Paris Cité, Paris, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Médialab, Sciences Po, Paris, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, P. Catholic University of Chile, Santiago, Chile"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Network and Data Science, CEU, Vienna, Austria"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Network and Data Science, CEU, Vienna, Austria"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Médialab, Sciences Po, Paris, France; Max Planck Institute for Mathematics in Sciences, Leipzig, Germany; ENS-PSL & Sorbonne Nouvelle, Lattice CNRS, Paris, France"}], "References": [{"Title": "FrameAxis: characterizing microframe bias and intensity with word embedding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "e644", "JournalTitle": "PeerJ Computer Science"}, {"Title": "I tag, you tag: the role of tagging in the formation of topic-based communities of video game channels in YouTube (2016)", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}]}]