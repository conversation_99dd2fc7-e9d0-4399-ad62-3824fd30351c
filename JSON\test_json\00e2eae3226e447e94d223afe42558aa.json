[{"ArticleId": 106042858, "Title": "Learning How to Teach Languages Online: Voices from the Field", "Abstract": "<p>At this moment, fully online language courses are being taught all around the globe. In addition, blended courses offer an integration of online and live instructional experiences. This study examines who is instructing online language courses and what they point to as additive and advantageous to their online language teaching development. We set out to survey higher education faculty as to how they developed their knowledge and practices. Through an online survey and follow-up synchronous interviews, we asked practicing online language educators about the sources of their expertise. The resulting dataset has much to inform professional development and instructional support generally, as well as broader research and educator communities regarding the ongoing, open-ended, peer-supported, and social nature of this kind of educator learning. As more language educators move their instruction online, the question of how to maximize the pedagogical potential of the new medium can best be addressed by those who have successfully made and embraced this move.</p>", "Keywords": "Online language teaching;professional development in online teaching;learning to teach online;teaching languages online;pre-covid", "DOI": "10.24059/olj.v26i4.2964", "PubYear": 2022, "Volume": "26", "Issue": "4", "JournalId": 37128, "JournalTitle": "Online Learning", "ISSN": "2472-5749", "EISSN": "2472-5730", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University at Albany, State University of New York"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hudson Valley Community College"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kazan Federal University"}], "References": []}, {"ArticleId": 106042873, "Title": "Compilation of drilling load spectrum based on the characteristics of drilling force", "Abstract": "<p>In order to overcome the problem that the existing methods of compiling load spectrum of spindle or machine tool mainly aim at the cutting force spectrum, torque spectrum and speed spectrum, respectively, which ignore the connection between each spectrum, in this paper, a method for compiling drilling load spectrum of motorized spindle in CNC machine tool based on the characteristics of drilling force is proposed. Firstly, drilling tests under different processing technologies are carried out to measure its load, and the correction coefficient in the empirical formula of drilling force is obtained through fitting the measured drilling force, which makes the calculation of the axial force and torque more reasonable. Secondly, compared with the extended factor method, the transcendental probability method is optimized to solve the ultimate load of the axial force. Then, after setting the axial force as the main load of drilling, an eight-stage load spectrum for the main load is compiled. Finally, according to the relationship between the axial force and other loads, the eight-stage loading spectrum is transformed into a multidimensional drilling load spectrum.</p>", "Keywords": "Drilling load spectrum; Characteristics of drilling force; Transcendental probability method; Empirical formula; NC machine tools", "DOI": "10.1007/s00170-022-08977-9", "PubYear": 2023, "Volume": "124", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>g Jin", "Affiliation": "Key Laboratory of CNC Equipment Reliability, Ministry of Education, Jilin University, Changchun, China; School of Mechanical and Aerospace Engineering, Jilin University, Changchun, Jilin, China"}, {"AuthorId": 2, "Name": "Chuliang Yan", "Affiliation": "Key Laboratory of CNC Equipment Reliability, Ministry of Education, Jilin University, Changchun, China; School of Mechanical and Aerospace Engineering, Jilin University, Changchun, Jilin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of CNC Equipment Reliability, Ministry of Education, Jilin University, Changchun, China; School of Mechanical and Aerospace Engineering, Jilin University, Changchun, Jilin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of CNC Equipment Reliability, Ministry of Education, Jilin University, Changchun, China; School of Mechanical and Aerospace Engineering, Jilin University, Changchun, Jilin, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of CNC Equipment Reliability, Ministry of Education, Jilin University, Changchun, China; School of Mechanical and Aerospace Engineering, Jilin University, Changchun, Jilin, China"}], "References": [{"Title": "New domain adaptation method in shallow and deep layers of the CNN for bearing fault diagnosis under different working conditions", "Authors": "<PERSON><PERSON>g Jin; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "11-12", "Page": "3701", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A general approach for the machining quality evaluation of S-shaped specimen based on POS-SQP algorithm and <PERSON> method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "553", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Rapid construction method of equipment model for discrete manufacturing digital twin workshop system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "102309", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Reliability allocation method based on linguistic neutrosophic numbers weight Muirhead mean operator", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "193", "Issue": "", "Page": "116504", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106042887, "Title": "S3QLRDF: distributed SPARQL query processing using Apache Spark—a comparative performance study", "Abstract": "<p>The proliferation of semantic data in the form of Resource Description Framework (RDF) triples demands an efficient, scalable, and distributed storage along with a highly available and fault-tolerant parallel processing strategy. There are three open issues with distributed RDF data management systems that are not well addressed altogether in existing work. First is the querying efficiency, second is that solutions are optimized for certain types of query patterns and don’t necessarily work well for all types, and third is concerned with reducing pre-processing cost. More precisely, the rapid growth of RDF data raises the need for an efficient partitioning strategy over distributed data management systems to improve SPARQL (SPARQL Protocol and RDF Query Language) query performance regardless of its pattern shape with minimized pre-processing time. In distributed RDF systems, both the data and the query processing are highly distributed. On the other hand, SPARQL workloads are dynamic and structurally diverse that can have different degrees of complexity. A complex SPARQL query over a large RDF graph in distributed systems requires combining a lot of distributed pieces of data through join operations. Therefore, designing an efficient data-partitioning schema and join strategy to minimize data transfer is the fundamental challenge in distributed RDF data management systems. In this context, we propose a new relational partitioning schema called Property Table Partitioning (PTP) for RDF data, that further partitions existing Property Table into multiple tables based on distinct properties (comprising of all subjects with non-null values for those distinct properties) in order to minimize input size and number of join operations of a query. This paper proposed a distributed RDF data management system called S3QLRDF, which is built on top of Spark and utilizes SQL to execute SPARQL queries over PTP schema. The experimental analysis with respect to preprocessing costs and query performance, using synthetic and real datasets shows that S3QLRDF outperforms state-of-the-art distributed RDF management systems.</p>", "Keywords": "RDF; SPARQL; Data partitioning; Spark", "DOI": "10.1007/s10619-023-07422-4", "PubYear": 2023, "Volume": "41", "Issue": "3", "JournalId": 3983, "JournalTitle": "Distributed and Parallel Databases", "ISSN": "0926-8782", "EISSN": "1573-7578", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Arizona State University, Tempe, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Arizona State University, Tempe, USA"}], "References": []}, {"ArticleId": 106042894, "Title": "Proposed Multi-Layer Firewall to Improve the Security of Software Defined Networks", "Abstract": "<p>one of the most important motivations behind the use of software-defined networking (SDN) is the desire to move from the approach of traditional networks to a more flexible and intelligent software development technology. This paper focuses on the importance of the SDN-based platform POX controller security firewall modules and its effectiveness on networks including the central administration of the SDN-based platform controller for protecting the network from possible attacks. The work is performed using the Mininet emulator with the aid of iperf bandwidth measurement tool.  Result reveals that the proposed multi-layer firewall does not compromise the flexibility of the network in any way.</p>", "Keywords": "Software defined networks (SDN);SDN-based platform controller (POX);Network Control Program (NCP);Open Networking Foundation (ONF);SDN-based platform controller OpenDayLight (ODL);Quality of service (QoS);a protection architecture for enterprise networks (SANE).", "DOI": "10.3991/ijim.v17i02.36387", "PubYear": 2023, "Volume": "17", "Issue": "2", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Middle Technical University"}], "References": []}, {"ArticleId": 106043052, "Title": "Multi-receptive field attention for person re-identification", "Abstract": "<p>Person re-identification is a challenging yet meaningful task to match two pedestrian images captured from non-overlapping cameras for public security. Attention schemes have been widely applied to deep learning based person re-identification because they help deep networks dynamically focus on salient regions on person images containing large scale variations and unconstrained auto-detection errors. However, previous attention approaches typically are of a single receptive field, which are difficult to capture rich structural affinities from different scales, harming salient region inferring effect. In this paper, we propose a multi-receptive field attention (MRFA) for person re-identification. MRFA captures multi-scale structural affinities among spatial positions in feature maps via weighted summing over correlations among positions represented with convolutional features of different receptive fields. The multi-scale structural affinities are further applied to infer the importance of different spatial locations. The MRFA is embedded into popular deep architectures (e.g., ResNet and Res2Net) to enhance the feature learning effect for person re-identification. The main contribution of this paper is to extend single receptive field attention to multi-receptive field attention to improve person re-identification effectively. Experiments on three public datasets, i.e., Market-1501, DukeMTMC-reID, and MSMT17, demonstrate that our method is superior to state-of-the-art person re-identification approaches, e.g., on the largest MSMT17 dataset, our method’s rank-1 identification rate is 83.9%.</p>", "Keywords": "Attention; Deep learning; Intelligent video surveillance; Multi-receptive field; Person re-identification", "DOI": "10.1007/s11042-022-14321-w", "PubYear": 2023, "Volume": "82", "Issue": "13", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "Ji<PERSON><PERSON> Zhu", "Affiliation": "College of Engineering, Huaqiao University, Quanzhou, China"}], "References": [{"Title": "A novel framework and concept-based semantic search Interface for abnormal crowd behaviour analysis in surveillance videos", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17579", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "PrGCN: Probability prediction with graph convolutional network for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "57", "JournalTitle": "Neurocomputing"}, {"Title": "Learning generalizable deep feature using triplet-batch-center loss for person re-identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "2", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 106043061, "Title": "Lightweight ResGRU: a deep learning-based prediction of SARS-CoV-2 (COVID-19) and its severity classification using multimodal chest radiography images", "Abstract": "<p>The new COVID-19 emerged in a town in China named Wuhan in December 2019, and since then, this deadly virus has infected 324 million people worldwide and caused 5.53 million deaths by January 2022. Because of the rapid spread of this pandemic, different countries are facing the problem of a shortage of resources, such as medical test kits and ventilators, as the number of cases increased uncontrollably. Therefore, developing a readily available, low-priced, and automated approach for COVID-19 identification is the need of the hour. The proposed study uses chest radiography images (CRIs) such as X-rays and computed tomography (CTs) to detect chest infections, as these modalities contain important information about chest infections. This research introduces a novel hybrid deep learning model named <i>Lightweight ResGRU</i> that uses residual blocks and a bidirectional gated recurrent unit to diagnose non-COVID and COVID-19 infections using pre-processed CRIs. <i>Lightweight ResGRU</i> is used for multi-modal two-class classification (normal and COVID-19), three-class classification (normal, COVID-19, and viral pneumonia), four-class classification (normal, COVID-19, viral pneumonia, and bacterial pneumonia), and COVID-19 severity types' classification (i.e., atypical appearance, indeterminate appearance, typical appearance, and negative for pneumonia). The proposed architecture achieved f-measure of 99.0%, 98.4%, 91.0%, and 80.5% for two-class, three-class, four-class, and COVID-19 severity level classifications, respectively, on unseen data. A large dataset is created by combining and changing different publicly available datasets. The results prove that radiologists can adopt this method to screen chest infections where test kits are limited.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2023, Springer Nature or its licensor (e.g. a society or other partner) holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "COVID-19;COVID-19 severity levels;Chest radiography images;Deep learning;Residual blocks;SARS-CoV-2", "DOI": "10.1007/s00521-023-08200-0", "PubYear": 2023, "Volume": "35", "Issue": "13", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Machine Perception and Visual Intelligence Research Group, Department of Computer Science, COMSATS University Islamabad, Lahore Campus. 1.5 KM Defence Road off Raiwind Road, Lahore, Pakistan."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Perception and Visual Intelligence Research Group, Department of Computer Science, COMSATS University Islamabad, Lahore Campus. 1.5 KM Defence Road off Raiwind Road, Lahore, Pakistan."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Machine Perception and Visual Intelligence Research Group, Department of Computer Science, COMSATS University Islamabad, Lahore Campus. 1.5 KM Defence Road off Raiwind Road, Lahore, Pakistan."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Machine Perception and Visual Intelligence Research Group, Department of Computer Science, COMSATS University Islamabad, Lahore Campus. 1.5 KM Defence Road off Raiwind Road, Lahore, Pakistan."}], "References": [{"Title": "COVIDetectioNet: COVID-19 diagnosis system based on X-ray images using features selected from pre-learned deep features ensemble", "Authors": "<PERSON><PERSON><PERSON>lu; Turk<PERSON>lu, Muammer", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1213", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep learning approaches for COVID-19 detection based on chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114054", "JournalTitle": "Expert Systems with Applications"}, {"Title": "DeepCoroNet: A deep LSTM approach for automated detection of COVID-19 cases from chest X-ray images", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107160", "JournalTitle": "Applied Soft Computing"}, {"Title": "SARS-Net: COVID-19 detection from chest x-rays by combining graph convolutional network and convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "122", "Issue": "", "Page": "108255", "JournalTitle": "Pattern Recognition"}, {"Title": "A Fine-tuned deep convolutional neural network for chest radiography image classification on COVID-19 cases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "1", "Page": "1055", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106043229, "Title": "A wideband G-shaped array antenna for X and Ku band applications", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJSCC.2023.10053673", "PubYear": 2023, "Volume": "1", "Issue": "1", "JournalId": 19270, "JournalTitle": "International Journal of Systems, Control and Communications", "ISSN": "1755-9340", "EISSN": "1755-9359", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106043375, "Title": "Predicting the total Unified Parkinson’s Disease Rating Scale (UPDRS) based on ML techniques and cloud-based update", "Abstract": "<p>Nowadays, smart health technologies are used in different life and environmental areas, such as smart life, healthcare, cognitive smart cities, and social systems. Intelligent, reliable, and ubiquitous healthcare systems are a part of the modern developing technology that should be more seriously considered. Data collection through different ways, such as the Internet of things (IoT)-assisted sensors, enables physicians to predict, prevent and treat diseases. Machine Learning (ML) algorithms may lead to higher accuracy in medical diagnosis/prognosis based on health data provided by the sensors to help physicians in tracking symptom significance and treatment steps. In this study, we applied four ML methods to the data on Parkinson’s disease to assess the methods’ performance and identify the essential features that may be used to predict the total Unified Parkinson’s disease Rating Scale (UPDRS). Since accessibility and high-performance decision-making are so vital for updating physicians and supporting IoT nodes (e.g., wearable sensors), all the data is stored, updated as rule-based, and protected in the cloud. Moreover, by assigning more computational equipment and memory in use, cloud computing makes it possible to reduce the time complexity of the training phase of ML algorithms in the cases we want to create a complete structure of cloud/edge architecture. In this situation, it is possible to investigate the approaches with varying iterations without concern for system configuration, temporal complexity, and real-time performance. Analyzing the coefficient of determination and Mean Square Error (MSE) reveals that the outcomes of the applied methods are mostly at an acceptable performance level. Moreover, the algorithm’s estimated weight indicates that Motor UPDRS is the most significant predictor of Total UPDRS.</p>", "Keywords": "Machine learning;Lineal regression;Linear least square;Conjugate gradient;Adam optimization algorithm;Ridge regression;Machine learning for healthcare;Parkinson’s disease", "DOI": "10.1186/s13677-022-00388-1", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Telecommunications, Polytechnic University of Turin, Turin, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Telecommunications, Polytechnic University of Turin, Turin, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Energy Engineering, Zarqa University, Zarqa, Jordan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Meybod University, Meybod, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Telecommunications, Polytechnic University of Turin, Turin, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Weifang University of Science and Technology, Weifang, Shandong, China"}], "References": [{"Title": "AoI-aware energy control and computation offloading for industrial IoT", "Authors": "<PERSON><PERSON>; <PERSON>; S<PERSON><PERSON>", "PubYear": 2023, "Volume": "139", "Issue": "", "Page": "29", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 106043433, "Title": "Forecasting the Number of Patient Visits by <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> Method at the Public Health Center", "Abstract": "<p>As the number of human populations increases and the economy becomes more advanced, people's awareness of health increases. This can increase the number of patient visits if the community will visit for treatment, therefore it is necessary to pay special attention from the health center to carry out readiness in the fulfillment of facilities and service support equipment, such as services in the outpatient registration place where registration documents must be adjusted to the number of existing patients, if the documents are lacking or have not been made, there can be long queues or accumulation of patients which leads to inadequate service. For this reason, the public health center must carry out careful planning activities, one of which is by conducting forecasting activities in order to overcome these problems.This study compares the best method among the 2 time series methods, then the forecasting results will be compared with the actual data to find which forecasting is the best.The final results showed the MAPE value of the arima method for Direct Patient Visits data was worth 22.55% while the Referral Patient Visits were valued at 47.40% with the Moderate/Feasible category, the Holwinters method for Direct Patient Visits data was worth 7.90% while the Referral Patient Visits were worth 11.90% with the excellent category.can be said that the smallest error value is Holtwinters from Direct Patient Visit data with MAPE 7.90% and from Referral Patient Visit data with MAPE 11.90%. Which is where it is said to be an excellent forecasting category</p>", "Keywords": "", "DOI": "10.47709/cnahpc.v5i1.2008", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 80232, "JournalTitle": "Journal Of Computer Networks, Architecture and High Performance Computing", "ISSN": "", "EISSN": "2655-9102", "Authors": [{"AuthorId": 1, "Name": "Ilham Basri K", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106043551, "Title": "KVRangeDB: Range Queries for a Hash-based Key–Value Device", "Abstract": "<p>Key-value (KV) software has proven useful to a wide variety of applications including analytics, time-series databases, and distributed file systems. To satisfy the requirements of diverse workloads, KV stores have been carefully tailored to best match the performance characteristics of underlying solid-state block devices. Emerging KV storage device is a promising technology for both simplifying the KV software stack and improving the performance of persistent storage-based applications. However, while providing fast, predictable put and get operations, existing KV storage devices don’t natively support range queries which are critical to all three types of applications described above.</p><p>In this paper, we present KVRangeDB, a software layer that enables processing range queries for existing hash-based KV solid-state disks (KVSSDs). As an effort to adapt to the performance characteristics of emerging KVSSDs, KVRangeDB implements log-structured merge tree key index that reduces compaction I/O, merges keys when possible, and provides separate caches for indexes and values. We evaluated the KVRangeDB under a set of representative workloads, and compared its performance with two existing database solutions: a Rocksdb variant ported to work with the KVSSD, and Wisckey, a key-value database that is carefully tuned for conventional block devices. On filesystem aging workloads, KVRangeDB outperforms <PERSON><PERSON><PERSON> by 23.7x in terms of throughput and reduce CPU usage and external write amplifications by 14.3x and 9.8x, respectively.</p>", "Keywords": "Key value stores; KVSSD; Range Queries", "DOI": "10.1145/3582013", "PubYear": 2023, "Volume": "19", "Issue": "3", "JournalId": 19571, "JournalTitle": "ACM Transactions on Storage", "ISSN": "1553-3077", "EISSN": "1553-3093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Texas A&M University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Los Alamos National Laboratory, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Los Alamos National Laboratory, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Nvidia, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Texas A&M University, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Texas A&M University, USA"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Texas A&M University, USA"}], "References": []}, {"ArticleId": 106043627, "Title": "Validation and optimization of two models for the magnetic restoring forces using a multi-stable piezoelectric energy harvester", "Abstract": "<p>This article presents a tunable multi-stable piezoelectric energy harvester. The apparatus consists of a stationary magnet and a cantilever beam whose free end is attached by an assembly of two cylindrical magnets that can be moved along the beam and a small cylindrical magnet that is fixed at the beam tip. By varying two parameters, the system can assume three stability states: tri-stable, bi-stable, and mono-stable, respectively. The developed apparatus is used to validate two models for the magnetic restoring force: the equivalent magnetic point dipole approach and the equivalent magnetic 2-point dipole approach. The study focuses on comparing the accuracy of the two models for a wide range of the tuning parameters. The restoring forces of the apparatus are determined dynamically and compared with their analytical counterparts based on each of the models. To improve the model accuracy, a model optimization is carried out by using the multi-population genetic algorithm. With the optimum models, the parametric sensitivity of each of the models is investigated. The stability state region is generated by using the optimum second model.</p>", "Keywords": "Multi-stable energy harvester;equivalent magnetic 2-point dipole approach;equivalent magnetic point dipole approach;magnetic restoring force;model validation and optimization", "DOI": "10.1177/1045389X221151064", "PubYear": 2023, "Volume": "34", "Issue": "14", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "Haining <PERSON>", "Affiliation": "Department of Mechanical Engineering, Lakehead University, Thunder Bay, ON, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Lakehead University, Thunder Bay, ON, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil Engineering, Lakehead University, Thunder Bay, ON, Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Harbin Institute of Technology, Shenzhen, Guangdong, China"}], "References": [{"Title": "A modified magnetic force model and experimental validation of a tri-stable piezoelectric energy harvester", "Authors": "<PERSON><PERSON><PERSON> Wang; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7", "Page": "967", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}, {"Title": "Dynamic analysis of a functionally graded piezoelectric energy harvester under magnetic interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "9", "Page": "986", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}]}, {"ArticleId": 106043640, "Title": "Design optimization and experimental evaluation of a large capacity magnetorheological damper with annular and radial fluid gaps", "Abstract": "<p>This paper presents an optimal design of a large-capacity Magnetorheological (MR) damper suitable for off-road vehicle applications. The damper includes an MR fluid bypass valve with both annular and radial gaps to generate a large damping force and dynamic range. An analytical model of the proposed damper is formulated based on the Bingham plastic model of MR fluids. To establish a relationship between the applied current and magnetic flux density in the MR fluid active regions, an analytical magnetic circuit is formulated and further compared with a magnetic finite element model. The MR valve geometrical parameters are subsequently optimized to maximize the damper dynamic range under specific volume and magnetic field constraints. The optimized MR valve can theoretically generate off-state and on-state damping forces of 1.1 and 7.41 kN, respectively at 12.5 mm/s damper piston velocity. The proposed damper has been also designed to allow a large piston stroke of 180 mm. The proof-of-concept of the optimally designed MR damper was subsequently fabricated and experimentally characterized to investigate its performance and validate the models. The results show that the proposed MR damper is able to provide large damping forces with a high dynamic range under different excitation conditions.</p>", "Keywords": "Magnetorheological damper;bypass MR valve;design optimization;performance evaluation", "DOI": "10.1177/1045389X221151075", "PubYear": 2023, "Volume": "34", "Issue": "14", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical, Industrial and Aerospace Engineering, Concordia University, Montreal, QC, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical, Industrial and Aerospace Engineering, Concordia University, Montreal, QC, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical, Industrial and Aerospace Engineering, Concordia University, Montreal, QC, Canada"}], "References": []}, {"ArticleId": 106043671, "Title": "A new perspective of higher order compact nonuniform Padé approximation based finite difference scheme for solving incompressible flows directly on polar grids", "Abstract": "In this paper, we propose a compact scheme directly on polar grid by combine virtues of computing implicit form of first order derivatives on nonuniform grids with variable coefficients and compact discretization of higher-order derivatives in terms of first order derivatives of the unknown variables. Our present scheme is third order accurate in space. We have solved the two dimensional (2D) fluid flow problems in cylindrical polar coordinates on non-uniform grids. The main efforts are focused on curvilinear grids. Our newly developed scheme is used to solve four different problems, namely a problem having analytical solution, Stokes flow in a half filled annulus, the problem of driven polar cavity and finally the flow past an impulsively started circular cylinder problem. The Navier-Stokes (N-S) and the Stokes equations are efficiently solved with Dirichlet as well as Neumann boundary conditions. For all the test cases, detailed comparison data are reproduced by the proposed scheme and compared with analytical as well as established numerical results available in the literature.", "Keywords": "Compact scheme ; Padé approximation ; Convection–diffusion equation ; N-S equations ; Curvilinear polar grid ; Driven polar cavity ; Annulus ; Impulsively started circular cylinder", "DOI": "10.1016/j.compfluid.2023.105793", "PubYear": 2023, "Volume": "254", "Issue": "", "JournalId": 1376, "JournalTitle": "Computers & Fluids", "ISSN": "0045-7930", "EISSN": "1879-0747", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Integrated Science Education and Research Centre (ISERC), Visva-Bharati, Santiniketan, West Bengal 731235, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Integrated Science Education and Research Centre (ISERC), Visva-Bharati, Santiniketan, West Bengal 731235, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Basic Sciences, Indian Institute of Technology Mandi, Mandi 175005, Kamand, Himachal Pradesh, India"}], "References": []}, {"ArticleId": 106043685, "Title": "The effects of graphical encodings on reading performance of the data chart displayed on the periphery of attention", "Abstract": "The accuracy and efficiency of estimating the true data value in visualizations is critical to the user interface design for large displays. As a variable, the attention engagement has not been the focus of existing related studies. In the current study, we intended to measure the performance users estimate proportions from a visualization under the low-attention condition. On this purpose, the dual-task paradigm was employed to simulate the scenario that data charts were read simultaneously with searching and memorizing information on the display. We evaluated eight data charts with three graphical encodings (shape, orientation and area) for the proportion value. The experimental results show that the vertical stacked chart is relatively more suitable for quantitative reading under the situation of temporary distraction. This is reflected by the high accuracy of estimation of this chart with relative less time consumption. When data charts are difficult to read, the users tend to pay less attention to information processing and make more inaccurate inferences of this primary task. Graphical encoding and the task time have a significant effect on task performances overall, while the reading accuracy seems not to vary with the difference of the primary task. The present study can be a supplement to the understanding of graphical perception and provide implications for the design of data visualization in display-human interfaces.", "Keywords": "Data chart ; Graphical encoding ; Attention ; Peripheral display ; Reading performance", "DOI": "10.1016/j.displa.2023.102378", "PubYear": 2023, "Volume": "77", "Issue": "", "JournalId": 3272, "JournalTitle": "Displays", "ISSN": "0141-9382", "EISSN": "1872-7387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Communication and Art Design, University of Shanghai for Science and Technology, Shanghai, China;Corresponding author at: No. 516, Jungong Road, Shanghai 200093, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Communication and Art Design, University of Shanghai for Science and Technology, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Communication and Art Design, University of Shanghai for Science and Technology, Shanghai, China"}], "References": [{"Title": "The intuitive grasp interface: design and evaluation of micro-gestures on the steering wheel for driving scenario", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "19", "Issue": "2", "Page": "433", "JournalTitle": "Universal Access in the Information Society"}, {"Title": "Effects of text space of Chinese-English bilingual traffic sign on driver reading performance", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "102002", "JournalTitle": "Displays"}]}, {"ArticleId": 106043750, "Title": "Intelligent digital twin for federated learning in AIoT networks", "Abstract": "Federated Learning (FL) promises to solve the data privacy problem by training the local model on each node and sharing the model parameters instead of the data itself. Next, the FL server applies model aggregation techniques to aggregate the received models and broadcast the resulting model to the connected clients. This study proposes a Digital Twin-based Federated Learning (DT-FL) framework to virtually monitor and controls the remotely deployed physical clients and their training process. The connection-oriented protocol, Open Connectivity Foundation (OCF) Iotivity, connects the FL clients with the FL server to ensure packet delivery. OCF Iotivity sends/receives the models’ weights to/from the server, and Hyper Text Transfer Protocol (HTTP) is used to monitor clients’ local training. After receiving partially trained models from clients, the server performs the optimal model selection using the normal distribution method by considering the performance of the model. Finally, the best-selected models are aggregated, and the final model is broadcasted to the clients. The framework utilizes Raspberrypi 4 devices as clients with limited computational capabilities, due to which the experiments are conducted with structured energy consumption data. The dataset comprises of 8 multistory residential buildings located in different geographical locations of the Republic of Korea. Each residential building is treated as an FL client and registered on DT using the IP address and port number. The DT-FL framework can be used with classification and regression datasets, and the model architecture for that data can be designed on the DT platform. The experiments are conducted with the partial and full participation of clients. The results show the minimum delay time in physical and virtual object synchronization and better performance and generalization of the global model for each client. The source code of the proposed DT-FL framework is available on GitHub .", "Keywords": "Digital twin ; Federated learning ; AIoT networks ; OCF IoTivity", "DOI": "10.1016/j.iot.2023.100698", "PubYear": 2023, "Volume": "22", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Jeju National University, Jejusi 63243, Jeju Special Self-Governing Province, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Attock Campus 43600, Pakistan;Bigdata Research Center, Jeju National University, Jeju 63243, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Jeju National University, Jejusi 63243, Jeju Special Self-Governing Province, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bigdata Research Center, Jeju National University, Jeju 63243, Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering (and Advanced Technology Research Institute), Jeju National University, Jeju 63243, Republic of Korea;Corresponding author"}], "References": [{"Title": "Blockchain-assisted access for federated Smart Grid domains: Coupling and features", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "144", "Issue": "", "Page": "124", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Big data analytics for intelligent manufacturing systems: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "62", "Issue": "", "Page": "738", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 106044116, "Title": "Faulty Detection System Based on SPC and Machine Learning Techniques", "Abstract": "Starting from a worrying observation, that companies have difficulties controlling the anomalies of their manufacturing processes, in order to have a better control over them, we have realized a case study on the practical data of the Fertial Complex to analyze the main parameters of the ammonia neutralization by nitric acid process. This article proposes a precise diagnostic of this process to detect dysfunction problems affecting the final product. We start with a general diagnosis of the process using the SPC method, this approach is considered an excellent way to monitor and improve the product quality and provides very useful observations that allowed us to detect the parameters that suffer from problems affecting the quality. After the discovery of the parameters incapable to produce the quality required by the standards, we applies two machine learning technologies dedicated to the type of data of these parameters for detected the anomaly, the first technique called The kernel connectivity-based outlier factor (COF) algorithm consists in recording for each object the degree of being an outlier, the second technique called the Isolation Forest, its principle is to establish a forest to facilitate the calculation and description. The results obtained were compared in order to choose which is the best algorithm to monitor and detect the problems of these parameters, we find that the COF method is more efficient than the isolation forest which leads us to rely on this technology in this kind of process in order to avoid passing a bad quality to the customer in future. © 2022 Lavoisier. All rights reserved.", "Keywords": "anomaly detection algorithms; case study; diagnostic; manufacturing process; SPC", "DOI": "10.18280/ria.360619", "PubYear": 2022, "Volume": "36", "Issue": "6", "JournalId": 21796, "JournalTitle": "Revue d'intelligence artificielle", "ISSN": "0992-499X", "EISSN": "1958-5748", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Automatics and Production Laboratory, Department of Industrial Engineering, Batna 2 University, Chahid Bokhlof Street, Batna, 5000, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Batna 2 University, Chahid Bokhlof Street, Batna, 5000, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Automatics and Production Laboratory, Department of Industrial Engineering, Batna 2 University, Chahid Bokhlof Street, Batna, 5000, Algeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department CPST, Higher School of Industrial Technologies, P.O. Box 218, Annaba, 23000, Algeria"}], "References": []}, {"ArticleId": 106044158, "Title": "A two-stage adaptive penalty method based on co-evolution for constrained evolutionary optimization", "Abstract": "Penalty function method is popular for constrained evolutionary optimization. However, it is non-trivial to set a proper penalty factor for a constrained optimization problem. This paper takes advantage of co-evolution to adjust the penalty factor and proposes a two-stage adaptive penalty method. In the co-evolution stage, the population is divided into multiple subpopulations, each of which is associated with a penalty factor. Through the co-evolution of these subpopulations, the performance of penalty factors can be evaluated. Since different penalty factors are used, the subpopulations will evolve along different directions. Thus, exploration can be enhanced. In the shuffle stage, all subpopulations are merged into a population and the best penalty factor from the co-evolution stage is used to guide the evolution. In this manner, the information interaction among subpopulations can be facilitated; thus, exploitation can be promoted. By executing these two stages iteratively, the feasible optimum could be obtained finally. In the two-stage evolutionary process, the search algorithm is designed based on two trial vector generation strategies of differential evolution. Additionally, a restart mechanism is designed to help the population avoid stagnating in the infeasible region. Extensive experiments demonstrate the effectiveness of the proposed method.", "Keywords": "Constrained evolutionary optimization; Penalty function; Co-evolution; Subpopulation; Shuffle", "DOI": "10.1007/s40747-022-00965-6", "PubYear": 2023, "Volume": "9", "Issue": "4", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Central South University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electromechanical Engineering, Guangdong University of Technology, Guangzhou, China"}], "References": [{"Title": "Accelerating evolutionary computation using a convergence point estimated by weighted moving vectors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "55", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A review of energy-efficient scheduling in intelligent production systems", "Authors": "Kaizhou Gao; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "237", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Trade-off between exploration and exploitation with genetic algorithm using a novel selection operator", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "1", "Page": "1", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "An effective co-evolutionary algorithm based on artificial bee colony and differential evolution for time series predicting optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "2", "Page": "299", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "An improved differential evolution algorithm and its application in optimization problem", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "7", "Page": "5277", "JournalTitle": "Soft Computing"}, {"Title": "An adaptive fuzzy penalty method for constrained evolutionary optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "571", "Issue": "", "Page": "358", "JournalTitle": "Information Sciences"}, {"Title": "A survey on evolutionary computation for complex continuous optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "1", "Page": "59", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Self-adaptive resources allocation-based differential evolution for constrained evolutionary optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107653", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 106044232, "Title": "Digital Weather Information in an Embodied World", "Abstract": "<p>We review the emergence of digital weather information, the history of human embodied knowing about weather, and two perspectives on cognition, one of which is symbolic (amodal, abstract, and arbitrary) and the other being embodied (embodied, extended, embedded, and enacted) to address the question: Beyond the general weather information they provide, to what extent can digital devices be used in an embodied way to extend a person’s pick-up of weather information? This is an interesting question to examine because human weather information and knowledge has a long past in our evolutionary history. Our human ancestors had to pick-up immediate information from the environment (including the weather) to survive. Digital weather information and knowing has a comparatively short past and a promising future. After reviewing these relevant topics, we concluded that, with the possible exception of weather radar apps, nothing currently exists in the form of digital products than can extend the immediate sensory reach of people to alert them about just-about-to-occur weather—at least not in the embodied forms of information. We believe that people who are weather salient (i.e., have a strong psychological attunement to the weather) may be in the best position going forward to integrate digital weather knowing with that which is embodied.</p>", "Keywords": "cognition; digital technology; embodied cognition; informatics; smartphones; weather cognition ; digital technology ; embodied cognition ; informatics ; smartphones ; weather", "DOI": "10.3390/informatics10010013", "PubYear": 2023, "Volume": "10", "Issue": "1", "JournalId": 40001, "JournalTitle": "Informatics", "ISSN": "", "EISSN": "2227-9709", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Education, University of Georgia, Athens, GA 30602, USA; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Education and Social Services, Saint Leo University, Saint Leo, FL 33574, USA"}], "References": []}, {"ArticleId": 106044333, "Title": "Weighted synchronous automata", "Abstract": "<p> This paper introduces a class of automata and associated languages, suitable to model a computational paradigm of fuzzy systems, in which both vagueness and simultaneity are taken as first-class citizens. This requires a weighted semantics for transitions and a precise notion of a synchronous product to enforce the simultaneous occurrence of actions. The usual relationships between automata and languages are revisited in this setting, including a specific <PERSON><PERSON><PERSON> theorem. </p>", "Keywords": "Weighted automata; regular languages; synchronous languages", "DOI": "10.1017/S0960129522000421", "PubYear": 2022, "Volume": "32", "Issue": "9", "JournalId": 10211, "JournalTitle": "Mathematical Structures in Computer Science", "ISSN": "0960-1295", "EISSN": "1469-8072", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université de Lille, CNRS, Inria, Centrale Lille, UMR 9189 CRIStAL, F-59000 Lille, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CIDMA, University of Aveiro, Aveiro, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "HASLab INESC TEC, University of Minho, INL, Braga, Portugal"}], "References": [{"Title": "A semantics and a logic for Fuzzy Arden Syntax", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "9", "Page": "6789", "JournalTitle": "Soft Computing"}]}, {"ArticleId": *********, "Title": "Fake review identification and utility evaluation model using machine learning", "Abstract": "<p>Due to the structural growth of e-commerce platforms, the frequency of exchange of opinions and the number of online reviews of platform participants related to products are increasing. However, given the growth of fake reviews, the corresponding growth in the quality of online reviews seems to be slow, at best. The number of cases of harm to retailers and customers caused by malicious false reviews is steadily increasing every year. In this context, it is becoming difficult for users to determine useful reviews amid a flood of information. As a result, the intrinsic value of online reviews that reduce uncertainty in pre-purchase decisions is blurred, and e-commerce platforms are on the verge of losing credibility and traffic. Through this study, we intend to present solutions related to review filtering and classification by constructing a model for judging the authenticity and usefulness of online reviews using machine learning.</p>", "Keywords": "Machine Learing; Fake Review; Fake review detection technique; E commerce; Useful reviews; SVC; Logistic regression", "DOI": "10.3389/frai.2022.1064371", "PubYear": 2023, "Volume": "5", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Business Administration, Sungkyunkwan University, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, Sungkyunkwan University, South Korea"}, {"AuthorId": 3, "Name": "Minwoo Park", "Affiliation": "Department of Business Administration, Sungkyunkwan University, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, Sungkyunkwan University, South Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Business Administration, Sungkyunkwan University, South Korea"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Oh", "Affiliation": "College of Computing and Informatics, Sungkyunkwan University, South Korea"}], "References": []}, {"ArticleId": 106044457, "Title": "Real-time hand rubbing quality estimation using deep learning enhanced by separation index and feature-based confidence metric", "Abstract": "Hand hygiene plays a crucial role in healthcare environments which can cease infections and diseases from spreading. It is also regarded as the second most effective way to control the transmission of COVID-19. The World Health Organization (WHO) recommends a 12-step guideline for alcohol-based hand rubbing. Compliance with this guideline is vital in order to clean the hands thoroughly. Hence, an automated system can help to improve the quality of this procedure. In this study, a large-scale and diverse dataset for both real and fake hand rubbing motions is collected as the first stage of building a reliable hand hygiene system. In the next stage, various pre-trained networks were analyzed and compared using a swift version of the Separation Index (SI) method. The proposed Swift SI method facilitates choosing the best pre-trained network without fine-tuning them on the whole dataset. Accordingly, the Inception-ResNet architecture achieved the highest SI among Inception, ResNet, Xception, and MobileNet networks. Fine-tuning the Inception-ResNet model led to an accuracy of 98% on the test dataset, which is the highest score in the literature. Therefore, from the proposed approach, a lightweight version of this model with fewer layers but almost the same accuracy is produced and examined. In the final stage, a novel metric, called Feature-Based Confidence (FBC), is devised for estimating the confidence of models in prediction. The proposed confidence measure is able to profoundly differentiate models with similar accuracy and determine the superior one. Based on the metrics results, the Inception-ResNet model is about 2x slower but 5% more confident than its lightweight version. Putting all together, by addressing the real-time application concerns, a Deep Learning based method is offered to qualify the hand rubbing process. The model is also employed in a commercial machine, called DeepHARTS, to estimate the quality of the hand rubbing procedure in different organizations and healthcare environments.", "Keywords": "Computer vision;Confidence;Deep learning;Hand hygiene;Infection;Machine learning;Real-time classification;Separation index;Transfer learning", "DOI": "10.1016/j.eswa.2023.119588", "PubYear": 2023, "Volume": "218", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Human and Robot Interaction Laboratory, School of Electrical and Computer Engineering, University of Tehran, Tehran, Iran."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Human and Robot Interaction Laboratory, University of Tehran, Tehran, Iran."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational Engineering Lappeenranta University of Technology, Lappeenranta, Finland."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Human and Robot Interaction Laboratory, School of Electrical and Computer Engineering, University of Tehran, Tehran, Iran."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Human and Robot Interaction Laboratory, School of Electrical and Computer Engineering, University of Tehran, Tehran, Iran."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tavanresan Company, Tehran, Iran."}], "References": [{"Title": "Hand-Washing Video Dataset Annotated According to the World Health Organization’s Hand-Washing Guidelines", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "38", "JournalTitle": "Data"}]}, {"ArticleId": *********, "Title": "Overflow management with self-eliminations", "Abstract": "We study admission control with packet redundancy, where large data items, called superpackets , exceed some Maximum Transmission Unit (MTU) and therefore are broken into several smaller packets. It is assumed that each superpacket is comprised of k packets, and that a superpacket is considered useful if at least ( 1 − β ) k of its packets arrive at the receiving end, for some redundancy parameter β ∈ [ 0 , 1 ) . Our goal is to maximize the total profit of useful superpackets, under an overloaded network, where we are forced to drop packets. Our starting point is an algorithm, called priority , which is based on assigning a random priority to each superpacket. This algorithm was shown to perform well when β = 0 , with and without a buffer. However, the performance of priority deteriorates with the increase of β , since it delivers too many packets for high priority superpackets. To tackle this issue, we propose an online algorithm which introduces randomized self-elimination of packets, called pse . When there is no buffer, we show that the competitive ratio of pse is better than the competitive ratio of priority , for the case where ( 1 − β ) 3 ⋅ ρ max ≤ 1 , where ρ max is the maximal burst size-router capacity ratio. For real-world values ( ρ max ≤ 1.5 ), pse outperforms priority for β ≥ 0.14 . We also present simulation results, with a buffer, that demonstrate the behavior of pse in comparison with priority and tail-drop . It is shown that pse performs much better than priority when β is large. In fact, it is shown that pse behaves at least as good as both algorithms.", "Keywords": "Competitive analysis ; Online algorithms ; Randomized algorithms ; Packet fragmentation ; Packet drop", "DOI": "10.1016/j.tcs.2023.113732", "PubYear": 2023, "Volume": "948", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Bar Ilan University, Ramat-Gan 52900, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Bar Ilan University, Ramat-Gan 52900, Israel;Corresponding author"}], "References": []}, {"ArticleId": 106045031, "Title": "Classification of Banana Ripeness Based on Color and Texture Characteristics", "Abstract": "<p>Banana is one of the most consumed fruits globally and is a rich source of vitamins, minerals and carbohydrates. With the many benefits that bananas have, many farmers cultivate this fruit. The problem that occurs when the harvest is produced on a large scale is the process of selecting bananas that are still unripe or ripe. Usually farmers carry out the selection process manually by visually identifying ripeness based on the color of the fruit skin. However, direct observation has several drawbacks such as subjectivity, takes a long time and is inaccurate. For this reason, we need a system that can help determine the maturity level of bananas automatically through a series of banana image processing processes. One way that can be used to determine the maturity level of bananas is by looking at the color and texture of the bananas. This study aims to classify the maturity level of bananas based on the color and texture characteristics of the banana image using the Gray Level Co-occurrence Matrix and K-Nearest Neighbor methods for the classification process. Based on the results of the research analysis that has been carried out, using the parameter k which has a value of 3 obtains very high accuracy.</p>", "Keywords": "Classification;Fruit Maturity;Gray Level Co-Occurrence Matrix;K-Means Clustering;K-Nearest Neighbor", "DOI": "10.47709/cnahpc.v5i1.1985", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 80232, "JournalTitle": "Journal Of Computer Networks, Architecture and High Performance Computing", "ISSN": "", "EISSN": "2655-9102", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universitas Nusa Mandiri"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Nusa Mandiri"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Nusa Mandiri"}], "References": []}, {"ArticleId": 106045050, "Title": "Efficient thread‐to‐core mapping alternatives for application‐level redundant multithreading", "Abstract": "<p>Redundant multithreading (RMT) is an effective thread-level replication method to improve the reliability requirements of applications. Although it significantly improves the robustness of applications, it comes with additional performance overhead since the redundant threads might share the same core resources. In our previous study [Efficient selective replication of critical code regions for SDC mitigation leveraging redundant multithreading. J Supercomput 2021;77:14130-14160], we presented an efficient software-level RMT approach, where we execute the most critical code regions with three threads to correct errors. In this study, we focus on further improving the performance of our software-level RMT method by presenting a set of different thread-to-core mapping alternatives. We provide different static mapping methods, which require preliminary information about the applications, such as execution time, instruction-per-cycle (IPC), or cache usage patterns, and a set of dynamic mapping methods, which map threads to cores dynamically at runtime without requiring any additional information. The dynamic mapping methods decide which threads are mapped to which cores at each scheduling point based on the IPC, cache miss rate, or cache access values of each thread as well as each core. Experimental results show that the dynamic mapping method, which maps threads to cores based on IPC values, outperforms all other static and dynamic methods. It also outperforms our baseline model, where the operating system handles the thread-to-core mappings by 8%, 7%, and 20% based on average speedup, harmonic speedup, and mean slowdown metrics.</p>", "Keywords": "performance evaluation;redundant multithreading;reliability;thread-to-core mapping", "DOI": "10.1002/cpe.7622", "PubYear": 2023, "Volume": "35", "Issue": "24", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Department Marmara University  Istanbul Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science ‐ Computer Architecture For Parallel Paradigms Barcelona Supercomputing Center  Barcelona Spain"}], "References": [{"Title": "Efficient thread‐to‐core mapping alternatives for application‐level redundant multithreading", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "24", "Page": "e7622", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 106045077, "Title": "Continuous quality control evaluation during manufacturing using supervised learning algorithm for Industry 4.0", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-023-10847-x", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Classification of Elephant Sounds Using Parallel Convolutional Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "3", "Page": "1415", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Driver monitoring and passenger interaction system using wearable device in intelligent vehicle", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "103", "Issue": "", "Page": "108323", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 106045104, "Title": "HUMAN BACK ACUPUNCTUR<PERSON> POINTS LOCATION USING RGB-D IMAGE FOR TCM MASSAGE ROBOTS, 67-75.", "Abstract": "", "Keywords": "Acupuncture points location; human posture estimation recognition;3D image; gradient boosting decision tree; TCM massage", "DOI": "10.2316/J.2023.206-0790", "PubYear": 2023, "Volume": "38", "Issue": "1", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhao", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106045242, "Title": "AUTONOMOUS QUADROTOR UAV SYSTEMS FOR <PERSON>YNAMIC PLATFORM LANDING WITH ONBOARD SENSORS, 296-305.", "Abstract": "", "Keywords": "", "DOI": "10.2316/J.2023.206-0807", "PubYear": 2023, "Volume": "38", "Issue": "4", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>qua<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Wuxi Shi", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106045420, "Title": "A generalized control chart for anomaly detection in SAR imagery", "Abstract": "Synthetic aperture radar (SAR) imagery is widely used in Earth observation (EO) applications such as land-use change monitoring and target detection. For such applications, it is important to have accurate knowledge of the statistical properties of SAR images to make good use of the information available. Therefore, it is essential to have appropriate statistical distributions to model the SAR data. In this context, the goal of this paper is threefold. First, a new parameterization of the Burr XII distribution that generalizes some widely used distributions for modeling SAR data is proposed. Second, based on the reparameterized distribution, a generalized control chart for anomaly detection in digital images is introduced. Using Monte Carlo simulations, the power detection of the proposed control chart by the empirical run length distribution is evaluated. Finally, the proposed methodology is applied to a real problem of anomaly detection. The problem of interest is the detection of military targets concealed in a forest region. The results suggest the relevance of the proposal to detect anomalies in SAR imagery, evidencing its practical applicability in EO problems.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109030", "PubYear": 2023, "Volume": "177", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Programa de Pós-graduação em Engenharia de Produção, Universidade Federal de Santa Maria, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departamento de Estatística, Universidade Federal de Santa Maria, Brazil;Programa de Pós-graduação em Estatística, Universidade Federal do Rio Grande do Sul, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Telecommunications, Aeronautics Institute of Technology, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Programa de Pós-graduação em Engenharia de Produção, Universidade Federal de Santa Maria, Brazil;Departamento de Estatística, Universidade Federal de Santa Maria, Brazil;Programa de Pós-graduação em Estatística, Universidade Federal do Rio Grande do Sul, Brazil;Santa Maria Space Science Laboratory (LACESM), Universidade Federal de Santa Maria, Brazil"}], "References": [{"Title": "On designing a new VEWMA control chart for efficient process monitoring", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "162", "Issue": "", "Page": "107751", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Detection of melanoma in dermoscopic images by integrating features extracted using handcrafted and deep learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "168", "Issue": "", "Page": "108060", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 106045586, "Title": "Using Toulmin's Argumentation Model to Enhance Trust in Analytics-Based Advice Giving Systems", "Abstract": "<p>Ecommerce websites increasingly provide predictive analytics-based advice (PAA), such as advice about future potential price reductions. Establishing consumer-trust in these advice-giving systems imposes unique and novel challenges. First, PAA about future alternatives that can benefit the consumer appears to inherently contradict the business goal of selling a product quickly and at high profit margins. Second, PAA is based on mathematical models that are non-transparent to the user. Third, PAA advice is inherently uncertain, and can be perceived as subjectively imposed in algorithms. Utilizing <PERSON><PERSON><PERSON>'s argumentation-model, we investigate the influence of advice-justification statements in overcoming these difficulties. Based on three experimental studies, in which respondents are provided with the advice of PAA systems, we show evidence for the different roles <PERSON><PERSON><PERSON>'s statement-types play in enhancing various trusting-beliefs in PAA systems. Provision of warrants is mostly associated with enhanced competence beliefs; rebuttals with integrity beliefs; backings both competence and benevolence; and data statements enhance competence, integrity and benevolence beliefs. Implications of the findings for research and practice are provided.</p>", "Keywords": "trust; analytics advice; <PERSON><PERSON><PERSON>'s model; predictive recommendations; trusting beliefs", "DOI": "10.1145/3580479", "PubYear": 2023, "Volume": "14", "Issue": "3", "JournalId": 24450, "JournalTitle": "ACM Transactions on Management Information Systems", "ISSN": "2158-656X", "EISSN": "2158-6578", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Cleveland State University, Ohio, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of British Columbia, Vancouver, Canada"}], "References": []}, {"ArticleId": 106045589, "Title": "Software Engineering and AI for Data Quality in Cyber-Physical Systems/Internet of Things - SEA4DQ'22 Report", "Abstract": "<p>Cyber-physical systems (CPS)/Internet of Things (IoT) are omnipresent in many industrial sectors and application domains in which the quality of the data acquired and used for decision support in common factor.</p>", "Keywords": "", "DOI": "10.1145/3573074.3573103", "PubYear": 2023, "Volume": "48", "Issue": "1", "JournalId": 12882, "JournalTitle": "ACM SIGSOFT Software Engineering Notes", "ISSN": "0163-5948", "EISSN": "1943-5843", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "SINTEF, Oslo, Norway"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "SINTEF, Oslo, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Darmstadt, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Darmstadt, Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Milan, Italy"}], "References": []}, {"ArticleId": 106045606, "Title": "Improvement of corrosion resistance of additive manufactured AISI 316L stainless steel in a physiological environment by TiN surface coating", "Abstract": "<p>In this work, we investigate the effect of additive manufacturing parameters on the corrosion resistance of AISI 316L stainless steel used to fabricate orthopedic implants. Samples were produced by selective laser melting, where the volumetric energy density, controlled by process parameters, was varied from 19 J/mm<sup>3</sup> to 167 J/mm<sup>3</sup>. A scanning electron microscope reveals that the increase of the volumetric energy density improves the material continuities by reducing the fraction of porosities from 54 to 0.3% and changing their morphologies from large pores with an irregular elongated shape to small size spherical shape. This beneficial effect is related to a reduction of parts roughness which varies from 5 to 12 µm. A Physical Vapor Deposited (PVD) TiN coating was applied to the manufactured parts to reduce surface roughness and to close porosities. Corrosion tests were carried out in <PERSON><PERSON>’s solution at 37 °C, corresponding to normal human biological temperature. It has been proved that increasing the volume energy density enhances corrosion resistance considerably and the pitting of additive manufactured parts. The application of PVD TiN coating provides additional protection against physiologic corrosion. The protective layer has successfully increased the corrosion potential Ecorr, reduced pitting corrosion, and improved the corrosion rate significantly. These results allow prosthesis manufacturers to extend the service life of prostheses obtained by additive manufacturing and subjected to physiological corrosion.</p>", "Keywords": "Selective laser melting; AISI 316L Stainless steel; Porosity; Corrosion resistance; Physiological environment", "DOI": "10.1007/s00170-023-10879-3", "PubYear": 2023, "Volume": "125", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical, Material and Processes Laboratory (LR99ES05), University of Tunis, ENSIT, Tunis, Tunisia; Preparatory Institute for Engineering Studies of Bizerte, University of Carthage, IPEIB, Zarzouna, Tunisia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Mechanical, Material and Processes Laboratory (LR99ES05), University of Tunis, ENSIT, Tunis, Tunisia; Preparatory Institute for Engineering Studies of Bizerte, University of Carthage, IPEIB, Zarzouna, Tunisia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, University of Québec Trois-Rivières, Trois-Rivières, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical, Material and Processes Laboratory (LR99ES05), University of Tunis, ENSIT, Tunis, Tunisia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical, Material and Processes Laboratory (LR99ES05), University of Tunis, ENSIT, Tunis, Tunisia"}], "References": [{"Title": "Selective laser melting (SLM) of AISI 316L—impact of laser power, layer thickness, and hatch spacing on roughness, density, and microhardness at constant input energy density", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "5-6", "Page": "1551", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Improvement of the corrosion behavior of AISI 304L stainless steel by deep rolling treatment under cryogenic cooling", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "11-12", "Page": "3841", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 106045657, "Title": "Telemedicine Development for Health Center Services Using Agile Methods", "Abstract": "<p>Puskesmas are the spearhead of health services throughout Indonesia, puskesmas are at the forefront in breaking the chain of transmission of COVID-19 because they are located in every sub-district and have a regional concept. Being at the forefront in handling COVID19 and continuing to provide other primary health services to the community is a tough task for puskesmas throughout Indonesia. In urgent situations and rapidly changing regulations regarding the handling of COVID19 and non-COVID19 patients, telemedicine application development researchers are required to work quickly and precisely according to the needs of the Puskesmas. This study discusses the development of telemedicine applications using the Agile development method, with the Scrum framework. Based on the problems above, researchers will develop telemedicine applications with several health service features, namely; COVID19 independent health checks, village doctor and midwife consultations, medicine orders, maternity services, and dental consultation services, the features in the telemedicine application were developed using the agile scrum method. Telemedicine development begins with system design analysis, UI/UX design, develop, functional testing, usability testing and launching. From the system design analysis, it produces output use case diagrams and class diagrams, according to the needs of the puskesmas business process. UI/UX design is carried out using figma tools, then application development is carried out focusing on the frontend and backend, after the application has been developed, functional testing is carried out three times and usability testing. The results of the usability test conducted on 35 respondents obtained an average score through the SUS questionnaire with a score of 79. In terms of the Acceptability Range, this application program is in the Acceptable category, while on the Grade Scale it is in Grade C position and on the Adjective Rating it is in a Good position . The results show that the telemedicine application for Pusline is good and can be accepted by users.</p>", "Keywords": "Telemedicine;information sistem;agile;agile scrum;usability testing", "DOI": "10.47709/cnahpc.v5i1.1987", "PubYear": 2022, "Volume": "5", "Issue": "1", "JournalId": 80232, "JournalTitle": "Journal Of Computer Networks, Architecture and High Performance Computing", "ISSN": "", "EISSN": "2655-9102", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}, {"AuthorId": 4, "Name": "R<PERSON><PERSON>", "Affiliation": "Universitas Sebelas Maret"}], "References": []}, {"ArticleId": 106045799, "Title": "EGNN: Graph structure learning based on evolutionary computation helps more in graph neural networks", "Abstract": "In recent years, graph neural networks (GNNs) have been successfully applied in many fields due to their characteristics of neighborhood aggregation and have achieved state-of-the-art performance. While most GNNs process graph data, the original graph data is frequently noisy or incomplete, resulting in suboptimal GNN performance. In order to solve this problem, a Graph Structure Learning (GSL) method has recently emerged to improve the performance of graph neural networks by learning a graph structure that conforms to the ground truth. However, the current strategy of GSL is to iteratively optimize the optimal graph structure and a single GNN, which will encounter several problems in training, namely vulnerability and overfitting. A novel GSL approach called evolutionary graph neural network (EGNN) has been introduced in this work in order to improve defense against adversarial attacks and enhance GNN performance. Unlike the existing GSL method, which optimizes the graph structure and enhances the parameters of a single GNN model through alternating training methods, evolutionary theory has been applied to graph structure learning for the first time in this work. Specifically, different graph structures generated by mutation operations are used to evolve a set of model parameters in order to adapt to the environment (i.e., to improve the classification performance of unlabeled nodes). An evaluation mechanism is then used to measure the quality of the generated samples in order to retain only the model parameters (progeny) with good performance. Finally, the progeny that adapt to the environment are retained and used for further optimization. Through this process, EGNN overcomes the instability of graph structure learning and always evolves the best progeny, providing new solutions for the advancement and development of GSL. Extensive experiments on various benchmark datasets demonstrate the effectiveness of EGNN and the benefits of evolutionary computation-based graph structure learning.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110040", "PubYear": 2023, "Volume": "135", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Yantai University, YanTai, 264005, ShanDong, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Yantai University, YanTai, 264005, ShanDong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yantai University, YanTai, 264005, ShanDong, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yantai University, YanTai, 264005, ShanDong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Yantai University, YanTai, 264005, ShanDong, China"}], "References": [{"Title": "Deep neural networks compression learning based on multiobjective evolutionary algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "260", "JournalTitle": "Neurocomputing"}, {"Title": "Evolving deep neural networks using coevolutionary algorithms with multi-population strategy", "Authors": "<PERSON>een<PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "13051", "JournalTitle": "Neural Computing and Applications"}, {"Title": "AST-GNN: An attention-based spatio-temporal graph neural network for Interaction-aware pedestrian trajectory prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Huaxia Xia", "PubYear": 2021, "Volume": "445", "Issue": "", "Page": "298", "JournalTitle": "Neurocomputing"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}]}, {"ArticleId": *********, "Title": "A Multi-Module Machine Learning Approach to Detect Tax Fraud", "Abstract": "Tax fraud is one of the substantial issues affecting governments around the world. It is defined as the intentional alteration of information provided on a tax return to reduce someone's tax liability. This is done by either reducing sales or increasing purchases. According to recent studies, governments lose over $500 billion annually due to tax fraud. A loss of this magnitude motivates tax authorities worldwide to implement efficient fraud detection strategies. Most of the work done in tax fraud using machine learning is centered on supervised models. A significant drawback of this approach is that it requires tax returns that have been previously audited, which constitutes a small percentage of the data. Other strategies focus on using unsupervised models that utilize the whole data when they search for patterns, though ignore whether the tax returns are fraudulent or not. Therefore, unsupervised models are limited in their usefulness if they are used independently to detect tax fraud. The work done in this paper focuses on addressing such limitations by proposing a fraud detection framework that utilizes supervised and unsupervised models to exploit the entire set of tax returns. The framework consists of four modules: A supervised module, which utilizes a tree-based model to extract knowledge from the data; an unsupervised module, which calculates anomaly scores; a behavioral module, which assigns a compliance score for each taxpayer; and a prediction module, which utilizes the output of the previous modules to output a probability of fraud for each tax return. We demonstrate the effectiveness of our framework by testing it on existent tax returns provided by the Saudi tax authority. © 2023 Authors. All rights reserved.", "Keywords": "applied machine learning; feature engineering; Tax fraud", "DOI": "10.32604/csse.2023.033375", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "King Saud University, Riyadh, 11451, Saudi Arabia"}], "References": [{"Title": "Value-added tax fraud detection with scalable anomaly detection techniques", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105895", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": *********, "Title": "Designing and testing systems of systems: From variability models to test cases passing through desirability assessment", "Abstract": "<p>In the early stages of a system of systems (SoS) conception, several constituent systems could be available that provide similar functionalities. An SoS design methodology should provide adequate means to model variability in order to support the opportunistic selection of the most desirable SoS configuration. We propose the VANTESS approach that (i) supports SoS modeling taking into account the variation points implied by the considered constituent systems; (ii) includes a heuristics to weight benefits and costs of potential architectural choices (called as SoS variants) for the selection of the constituent systems; and finally (iii) also helps test planning for the selected SoS variant by deriving a simulation model on which test objectives and scenarios can be devised. We illustrate an application example of VANTESS to the “educational” SoS and discuss its pros and cons within a focus group.</p>", "Keywords": "design;software product line;system of systems;test case generation;testing;variability model", "DOI": "10.1002/smr.2427", "PubYear": 2022, "Volume": "34", "Issue": "10", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of information science and technologies “A. <PERSON>” CNR Pisa Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universidade Federal Fluminense Niterói RJ Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of information science and technologies “A. <PERSON>” CNR Pisa Italy"}], "References": [{"Title": "Dynamic-SoS: An Approach for the Simulation of Systems-of-Systems Dynamic Architectures", "Authors": "<PERSON>; Val<PERSON>mar <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "5", "Page": "709", "JournalTitle": "The Computer Journal"}, {"Title": "Model-based testing of software product lines: Mapping study and research roadmap", "Authors": "K<PERSON><PERSON> L<PERSON>; <PERSON><PERSON>; Avelino F. Zorzo", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "110608", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Automating system test case classification and prioritization for use case-driven testing in product lines", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "5", "Page": "3711", "JournalTitle": "Empirical Software Engineering"}]}, {"ArticleId": 106046131, "Title": "Technological pedagogical content ethical knowledge (TPCEK): The development of an assessment instrument for pre-service teachers", "Abstract": "Although a variety of studies have explored the framework of Technological Pedagogical Content Knowledge (TPACK), these studies did not incorporate (1) what ethical issues have arisen from the application of digital technologies in teaching, and (2) what kind of knowledge teachers need to face these issues. These questions remain largely unanswered in existing research. This study proposes a newly developed conceptual framework called Technological Pedagogical Content Ethical Knowledge (TPCEK) that introduces ethical knowledge as a new component based on the TPACK framework. A quantitative study was conducted to develop and validate an assessment instrument for Chinese pre-service teachers based on the TPCEK framework. Both exploratory and confirmatory factor analyses were conducted to test the validity and reliability of the measures. Our results indicated that the final version of the TPCEK scale is a reliable and valid one for measuring pre-service teachers’ TPCEK. These insights allow us to understand the importance of ethical knowledge for teaching with digital technologies and the relationships between ethical knowledge and knowledge components of the TPACK framework.", "Keywords": "", "DOI": "10.1016/j.compedu.2023.104740", "PubYear": 2023, "Volume": "197", "Issue": "", "JournalId": 6427, "JournalTitle": "Computers & Education", "ISSN": "0360-1315", "EISSN": "1873-782X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Education, GuiYang University, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Education, GuiYang University, China"}], "References": [{"Title": "Developing a short assessment instrument for Technological Pedagogical Content Knowledge (TPACK.xs) and comparing the factor structure of an integrative and a transformative model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "103967", "JournalTitle": "Computers & Education"}]}, {"ArticleId": 106046138, "Title": "Introduction to the Special Issue on Domain-Specific System-on-Chip Architectures and Run-Time Management Techniques", "Abstract": "", "Keywords": "", "DOI": "10.1145/3567834", "PubYear": 2023, "Volume": "22", "Issue": "2", "JournalId": 17571, "JournalTitle": "ACM Transactions on Embedded Computing Systems", "ISSN": "1539-9087", "EISSN": "1558-3465", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "University of Wisconsin"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Texas"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Michigan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Intel Corporation"}], "References": []}, {"ArticleId": 106046140, "Title": "Assessing environmental performance with big data: A DEA model with multiple data resources", "Abstract": "Big data generated by environmental monitoring equipment create a good opportunity for improving performance evaluation results while also posing a challenge for DEA (Data Envelopment Analysis) model construction. This paper constructs four DEA models to deal with streaming data combined with traditional statistical data when considering undesirable output. Classic ways of transforming streaming data and LASSO (Least Absolute Shrinkage and Selection Operator) regression are both used for transforming streaming data in the new DEA approach. An empirical study shows the results of dimension reduction of big data and the difference in efficiency scores obtained based on them. Also, a robustness analysis illustrates how the number of variables influences the efficiency result. The models presented in this paper are utilized to calculate the environmental efficiency of 252 of China’s cities in 2020, considering both statistical data and daily air quality index data. The efficiency results also show a link between efficiency and city size by dividing all cities into five categories.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109041", "PubYear": 2023, "Volume": "177", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, Hefei 230026, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, University of Science and Technology of China, Hefei 230026, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Economics, Hefei University of Technology, Hefei 230009, China;Corresponding author"}], "References": [{"Title": "Analysis of the environmental efficiency in China based on the DEA cross‐efficiency approach under different policy objectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "3", "Page": "", "JournalTitle": "Expert Systems"}, {"Title": "Big data efficiency analysis: Improved algorithms for data envelopment analysis involving large datasets", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "105553", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 106046190, "Title": "Contact detection algorithm for needle puncturing robot", "Abstract": "", "Keywords": "", "DOI": "10.4310/CIS.2022.v22.n4.a3", "PubYear": 2022, "Volume": "22", "Issue": "4", "JournalId": 32773, "JournalTitle": "Communications in Information and Systems", "ISSN": "1526-7555", "EISSN": "2163-4548", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University, Okayama City, Okayama, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Natural Science and Technology, Okayama University, Okayama City, Okayama, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Interdisciplinary Science and Engineering in Health Systems, Okayama University, Okayama City, Okayama, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Medicine, Dentistry and Pharmaceutical Sciences, Okayama University, Okayama City, Okayama, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Graduate School of Science and Technology, Meijo University, Nagoya, Aichi, Japan"}], "References": []}, {"ArticleId": 106046329, "Title": "Fine-Grained Interaction Modeling with Multi-Relational Transformer for Knowledge Tracing", "Abstract": "<p>Knowledge tracing, the goal of which is predicting students’ future performance given their past question response sequences to trace their knowledge states, is pivotal for computer-aided education and intelligent tutoring systems. Although many technical efforts have been devoted to modeling students based on their question-response sequences, fine-grained interaction modeling between question-response pairs within each sequence is underexplored. This causes question-response representations less contextualized and further limits student modeling. To address this issue, we first conduct a data analysis and reveal the existence of complex cross effects between different question-response pairs within a sequence. Consequently, we propose MRT-KT, a multi-relational transformer for knowledge tracing, to enable fine-grained interaction modeling between question-response pairs. It introduces a novel relation encoding scheme based on knowledge concepts and student performance. Comprehensive experimental results show that MRT-KT outperforms state-of-the-art knowledge tracing methods on four widely-used datasets, validating the effectiveness of considering fine-grained interaction for knowledge tracing.</p>", "Keywords": "knowledge tracing; multi-relational transformer; user behavior modeling", "DOI": "10.1145/3580595", "PubYear": 2023, "Volume": "41", "Issue": "4", "JournalId": 12861, "JournalTitle": "ACM Transactions on Information Systems", "ISSN": "1046-8188", "EISSN": "1558-2868", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, East China Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, East China Normal University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Shanghai Institute for AI Education, East China Normal University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Shanghai Institute for AI Education, East China Normal University, Shanghai, China"}], "References": []}, {"ArticleId": 106046428, "Title": "Air film pressure field characteristics of aerostatic thrust bearing with orifice blockage", "Abstract": "<p>Aerostatic bearings are the key component of ultra-precision machine tools, and the bearing performance is critical to the machining accuracy and reliability. The characteristics of the air film pressure field (AFPF) mainly determine the bearing performance and are closely related to parameters such as the air supply pressure, the number, and position distribution of orifices. However, the orifices of aerostatic bearing were usually assumed to be ideal for the available related research. The phenomenon of orifice blockage is common in the actual working conditions, which has great impact on the bearing performance. In this study, the AFPF characteristics of aerostatic thrust bearing was analyzed by the theoretical and experimental way with the commonly single-orifice blockage (SOB) or double-orifice blockage (DOB) cases. The air film pressure distribution, load capacity, and tilting moment are obtained for each case. The effectiveness of simulation results is verified by the experiments. The results indicates that the air film pressure decreases significantly in the area of about three orifices adjacent to the blocked orifice in the condition of SOB. Different cases of orifice blockage have different bearing tilting moment, and resulting in different bearing tilting angle. According to different bearing tilting direction and tilting angle, a preliminary prediction method of the orifice blockage is proposed to judge the different cases of orifice blockage.</p>", "Keywords": "Aerostatic bearing; Orifice blockage; Air film pressure field characteristics; Load capacity; Tilting moment", "DOI": "10.1007/s00170-022-09641-y", "PubYear": 2023, "Volume": "124", "Issue": "11-12", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou, China; Key Laboratory of Special Purpose Equipment and Advanced Processing Technology, Ministry of Education and Zhejiang Province, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou, China; Key Laboratory of Special Purpose Equipment and Advanced Processing Technology, Ministry of Education and Zhejiang Province, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou, China; Key Laboratory of Special Purpose Equipment and Advanced Processing Technology, Ministry of Education and Zhejiang Province, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou, China; Key Laboratory of Special Purpose Equipment and Advanced Processing Technology, Ministry of Education and Zhejiang Province, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou, China; Key Laboratory of Special Purpose Equipment and Advanced Processing Technology, Ministry of Education and Zhejiang Province, Zhejiang University of Technology, Hangzhou, China"}], "References": [{"Title": "A hierarchical evaluation index system for FMS reliability considering coupling relations between system elements", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "11-12", "Page": "3737", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 106046474, "Title": "An evolutionary fault diagnosis algorithm for interconnection networks under the PMC model", "Abstract": "<p>Fault diagnosis of interconnection networks is vital to ensuring the reliability and maintenance of multiprocessor systems. Based on graph theory, diagnostic algorithms for solving the problem of fault diagnosis in interconnection networks have been widely studied. As the number of processors in multiprocessor systems has increased in recent years, fault diagnosis algorithms based on graph theory cannot meet the current diagnosis requirements of some interconnection networks, such as t -diagnosable systems. In this paper, we use the evolution diagnosis approach to study fault diagnosis in t -diagnosable systems under the PMC model. First, for a t -diagnosable system G with a syndrome \\(\\sigma\\) , we use a simple centralized algorithm to generate its simplified diagnosis graph \\(G_\\textrm{f}\\) , which contains all the faulty nodes in G . Based on the graph, we prove that the faulty node set in G is the minimum cover set of \\(G_\\textrm{f}\\) . Next, we prove that the problem of computing the minimum cover set for \\(G_\\textrm{f}\\) is equivalent to the problem of computing the optimal solution of a zero-one integer program. Using this result, we propose a novel genetic algorithm to solve the problem of the zero-one integer program. The simulation results show that the diagnostic accuracy of our proposed algorithm is equal to or greater than 96% and that the proposed algorithm outperforms its competitors in terms of diagnostic accuracy, number of iterations and running time.</p>", "Keywords": "t-diagnosable systems; Fault diagnosis; Genetic algorithm; Zero-one integer program; Large-scale network systems", "DOI": "10.1007/s11227-023-05054-0", "PubYear": 2023, "Volume": "79", "Issue": "9", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Engineering, Nanning University, Nanning, China; School of Computer, Electronics and Information, Guangxi Key Laboratory of Multimedia Communications and Network Technology, Guangxi University, Nanning, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi Key Laboratory of Multimedia Communications and Network Technology, Guangxi University, Nanning, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi Key Laboratory of Multimedia Communications and Network Technology, Guangxi University, Nanning, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer, Electronics and Information, Guangxi Key Laboratory of Multimedia Communications and Network Technology, Guangxi University, Nanning, China"}], "References": [{"Title": "Structure fault tolerance of balanced hypercubes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "4", "Page": "3885", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Conditional diagnosability of <PERSON><PERSON><PERSON> graphs generated by wheel graphs under the PMC model", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "849", "Issue": "", "Page": "163", "JournalTitle": "Theoretical Computer Science"}]}, {"ArticleId": 106046485, "Title": "Robotik und Sensortechnik", "Abstract": "<p lang=\"de\"> Zusammenfassung <p>Sensorsysteme und Feldrobotik sind sowohl wichtige Hilfsmittel als auch Innovationstreiber zur Gestaltung einer nachhaltigeren Landwirtschaft. Insbesondere der Einsatz bildgebender Sensoren mit räumlicher und zeitlicher Auflösung hat in Verbindung mit dem landwirtschaftlichen Wissen das Potenzial, ökologische und ökonomische Zielkonflikte aufzulösen. Die Feldrobotik hat aktuell eine hohe Dynamik im Bereich innovativer Lösungsansätze und erster im Markt befindlicher Produkte. Im Bereich der Systemintegration in den landwirtschaftlichen Betrieb sowie in der Weiterbildung und im Service besteht noch erheblicher Handlungsbedarf für die Feldrobotik, auch ist eine stärkere Fokussierung auf den Nutzen der Systeme für signifikante Marktanteile erforderlich.</p></p>", "Keywords": "", "DOI": "10.1007/s00287-022-01517-5", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 7707, "JournalTitle": "Informatik-Spektrum", "ISSN": "0170-6012", "EISSN": "1432-122X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Fakultät Ingenieurwissenschaften und Informatik, Hochschule Osnabrück, Osnabrück, Deutschland"}], "References": []}, {"ArticleId": 106046536, "Title": "Integrated risk averse and possibilistic humanitarian logistic model with social costs and material convergence", "Abstract": "A stepwise development is proposed for the construction of a model that comprehensively solves the disaster planning and response problem. In addition to traditional logistics costs, the suggested approach incorporates response time sensitivity, social costs related to post-disaster deprivation, the impact of material convergence, and measurable and epistemic uncertainty. Starting from a stochastic model based on previously published scenarios, the global costs of the scenarios are minimized using the expected value of the parameters, in a risk-averse bi-objective function that also optimizes the robustness of the model by minimizing the variability of the parameters whose probabilities are known. Finally, the complexity arising from imprecise parameter values with unknown probabilities is addressed by using chance-constrained fuzzy programming to optimize the risk associated with inter-scenario cost variability and feasibility of the constraints. This formulation is applied to real case study data, demonstrating that both the overall costs and the social cost component of the costs decrease progressively from the base formulation. The model allows the decision-maker to test the sensitivity of the results and thus set the uncertainty parameters and the incorporation of multiple objectives accordingly.", "Keywords": "Humanitarian logistic ; possibilistic modeling ; robust optimization ; sociotechnical problem ; fuzzy optimization", "DOI": "10.1080/03155986.2023.2168583", "PubYear": 2023, "Volume": "61", "Issue": "2", "JournalId": 12238, "JournalTitle": "INFOR: Information Systems and Operational Research", "ISSN": "0315-5986", "EISSN": "1916-0615", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Escuela de Ingeniería Industrial, Universidad Santo Tomás, Santiago, Chile;Doctorado en Sistemas de Ingeniería, Universidad de Chile, Santiago, Chile"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Departamento de Ingeniería Civil, Universidad de Chile, Santiago, Chile;Instituto Sistemas Complejos de Ingeniería (ISCI), Santiago, Chile"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Departamento de Industria and Programa Institucional de Fomento a la Investigación, Desarrollo e Innovación, Universidad Tecnológica Metropolitana, Santiago, Chile"}], "References": []}, {"ArticleId": 106046886, "Title": "LuNet-LightGBM: An Effective Hybrid Approach for Lesion Segmentation and DR Grading", "Abstract": "Diabetes problems can lead to an eye disease called Diabetic Retinopathy (DR), which permanently damages the blood vessels in the retina. If not treated early, DR becomes a significant reason for blindness. To identify the DR and determine the stages, medical tests are very labor-intensive, expensive, and time-consuming. To address the issue, a hybrid deep and machine learning technique-based autonomous diagnostic system is provided in this paper. Our proposal is based on lesion segmentation of the fundus images based on the LuNet network. Then a Refined Attention Pyramid Network (RAPNet) is used for extracting global and local features. To increase the performance of the classifier, the unique features are selected from the extracted feature set using Aquila Optimizer (AO) algorithm. Finally, the LightGBM model is applied to classify the input image based on the severity. Several investigations have been done to analyze the performance of the proposed framework on three publically available datasets (MESSIDOR, APTOS, and IDRiD) using several performance metrics such as accuracy, precision, recall, and f1-score. The proposed classifier achieves 99.29%, 99.35%, and 99.31% accuracy for these three datasets respectively. The outcomes of the experiments demonstrate that the suggested technique is effective for disease identification and reliable DR grading. © 2023 CRL Publishing. All rights reserved.", "Keywords": "Deep learning; diabetic retinopathy; DR grading; lesion segmentation; lightGBM; LuNet", "DOI": "10.32604/csse.2023.034998", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Sesikala Bapatla", "Affiliation": "School of Computer Science and Engineering, VIT-AP University, Andhra Pradesh, Amaravati, 522237, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, VIT-AP University, Andhra Pradesh, Amaravati, 522237, India"}], "References": [{"Title": "Adaptive machine learning classification for diabetic retinopathy", "Authors": "Laxmi Math; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "4", "Page": "5173", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Composite deep neural network with gated-attention mechanism for diabetic retinopathy severity classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; V<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "9825", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Diabetic retinopathy detection and stage classification in eye fundus images using active deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "8", "Page": "11691", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "MVDRNet: Multi-view diabetic retinopathy detection by combining DCNNs and attention mechanisms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108104", "JournalTitle": "Pattern Recognition"}, {"Title": "An automated exudate detection scheme supporting diabetic retinopathy screening using spatial-spectral-statistical feature maps", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "7", "Page": "9829", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Fovea localization by blood vessel vector in abnormal fundus images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "108711", "JournalTitle": "Pattern Recognition"}, {"Title": "Hemorrhage semantic segmentation in fundus images for the diagnosis of diabetic retinopathy by using a convolutional neural network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "Diabetic Retinopathy Classification Using Hybrid Deep Learning Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "5", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 106046887, "Title": "Video Transmission Secrecy Improvement Based on Fractional Order Hyper Chaotic System", "Abstract": "In the Digital World scenario, the confidentiality of information in video transmission plays an important role. Chaotic systems have been shown to be effective for video signal encryption. To improve video transmission secrecy, compressive encryption method is proposed to accomplish compression and encryption based on fractional order hyper chaotic system that incorporates Compressive Sensing (CS), pixel level, bit level scrambling and nucleotide Sequences operations. The measurement matrix generates by the fractional order hyper chaotic system strengthens the efficiency of the encryption process. To avoid plain text attack, the CS measurement is scrambled to its pixel level, bit level scrambling decreases the similarity between the adjacent measurements and the nucleotide sequence operations are done on the scrambled bits, increasing the encryption. Two stages are comprised in the reconstruction technique, the first stage uses the intra-frame similarity and offers robust preliminary retrieval for each frame, and the second stage iteratively improves the efficiency of reconstruction by integrating inter frame Multi Hypothesis (MH) estimation and weighted residual sparsity modeling. In each iteration, the residual coefficient weights are modified using a mathematical approach based on the MH predictions, and the Split Bregman iteration algorithm is defined to resolve weighted l1 regularization. Experimental findings show that the proposed algorithm provides good compression of video coupled with an efficient encryption method that is resistant to multiple attacks. © 2023 CRL Publishing. All rights reserved.", "Keywords": "bit level scrambling; compressive sensing; Fractional order hyper chaotic system; nucleotide sequences; pixel level scrambling", "DOI": "10.32604/csse.2023.032381", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SRM Institute of Science and Technology, Kattankulathur, 603203, India"}, {"AuthorId": 2, "Name": "<PERSON>. <PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, SRM Institute of Science and Technology, Kattankulathur, 603203, India"}], "References": [{"Title": "A novel encrypted compressive sensing of images based on fractional order hyper chaotic Chen system and DNA operations", "Authors": "<PERSON><PERSON>; S. <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "5-6", "Page": "3957", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106046944, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0950-7051(23)00068-0", "PubYear": 2023, "Volume": "262", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [], "References": []}, {"ArticleId": 106046949, "Title": "PSCSO: Enhanced sand cat swarm optimization inspired by the political system to solve complex problems", "Abstract": "The Sand Cat Swarm Optimization (SCSO) algorithm is a recently introduced metaheuristic with balanced behavior in the exploration and exploitation phases. However, it is not fast in convergence and may not be successful in finding the global optima, especially for complex problems since it starts the exploitation phase late. Moreover, the performance of SCSO is also affected by incorrect position as it depends on the location of the global optimum. Therefore, this study proposes a new method for the SCSO algorithm with a multidisciplinary principle inspired by the Political (Parliamentary) system, which is named PSCSO. The suggested algorithm increases the chances of finding the global solution by randomly choosing positions between the position of the candidate&#x27;s best solution available so far and the current position during the exploitation phase. In this regard, a new coefficient is defined that affects the exploration and exploitation phases. In addition, a new mathematical model is introduced to use in the exploitation phase. The performance of the PSCSO algorithm is analyzed on a total of 41 benchmark functions from CEC2015, 2017, and 2019. In addition, its performance is evaluated in four classical engineering problems. The proposed algorithm is compared with the SCSO, Stochastic variation and Elite collaboration in SCSO (SE-SCSO), Hybrid SCSO (HSCSO), Parliamentary Optimization Algorithm (POA), and Arithmetic Optimization Algorithm (AOA) algorithms, which have been proposed in recent years. The obtained results depict that the PSCSO algorithm performs better or equivalently to the compared optimization algorithms.", "Keywords": "", "DOI": "10.1016/j.advengsoft.2023.103423", "PubYear": 2023, "Volume": "178", "Issue": "", "JournalId": 3474, "JournalTitle": "Advances in Engineering Software", "ISSN": "0965-9978", "EISSN": "1873-5339", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Engineering Dept., Faculty of Engineering, <PERSON><PERSON> Sultan Mehmet <PERSON>f University, Istanbul, Turkey;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Political Sciences and Public Administration Dept., Faculty of Economics, Administrative and Social Sciences, Istinye University, Istanbul, Turkey"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Political Sciences and Public Administration Dept., Faculty of Economics, Administrative and Social Sciences, Istinye University, Istanbul, Turkey"}], "References": [{"Title": "I-GWO and Ex-GWO: improved algorithms of the Grey Wolf Optimizer to solve global optimization problems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "509", "JournalTitle": "Engineering with Computers"}, {"Title": "An improved invasive weed optimization algorithm for solving dynamic economic dispatch problems with valve-point effects", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "805", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Political Optimizer: A novel socio-inspired meta-heuristic for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105709", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Battle royale optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "4", "Page": "1139", "JournalTitle": "Neural Computing and Applications"}, {"Title": "MAP-ACO: An efficient protocol for multi-agent pathfinding in real-time WSN and decentralized IoT systems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "", "Page": "103325", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Hybrid algorithms based on combining reinforcement learning and metaheuristic methods to solve global optimization problems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "223", "Issue": "", "Page": "107044", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Adapted-RRT: novel hybrid method to solve three-dimensional path planning problem using sampling and metaheuristic-based algorithms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15569", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Improving the performance of hierarchical wireless sensor networks using the metaheuristic algorithms: efficient cluster head selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "41", "Issue": "4", "Page": "368", "JournalTitle": "Sensor Review"}, {"Title": "A closed-loop supply chain configuration considering environmental impacts: a self-adaptive NSGA-II algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "12", "Page": "13478", "JournalTitle": "Applied Intelligence"}, {"Title": "Marine predator inspired naked mole-rat algorithm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "212", "Issue": "", "Page": "118822", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106046983, "Title": "Ra<PERSON><PERSON> Alat Penyemprot Herbisida (Knapsack Sprayer) Elektrik Berbasis Panel Surya 20 Wp Paralel", "Abstract": "<p><PERSON><PERSON><PERSON><PERSON> klasik yang menghambat petani dalam melakukan penyemprotan cairan herbisida karena masih menggunakan sistem penyemprot cairan herbisida secara manual. <PERSON><PERSON><PERSON> bertujuan untuk menghasilkan alat penyemprot herbisida elektrik yang efisien dan efektif bagi masyarakat, juga untuk mengenalkan serta memanfaatkan energi terbarukan sebagai energi yang mudah di dapatkan dan ramah lingkungan khususnya di bidang pertanian. Sumber yang dihasilkan oleh panel surya 20Wp yaitu berup<PERSON>gan, <PERSON><PERSON>, Daya dan Energi Listrik. Pengukuran panel surya diambil selama 7 hari, mulai dari pukul 08.00 WIB sampai pukul 15.00 WIB. Hasil yang didapatkan untuk nilai rata-rata terbesar Tegangan, Amper<PERSON>, dan <PERSON>a berada pada hari ke-7 dimana Tegangan sebesar 13,72V, <PERSON>rus  sebesar 0,43A, dan  <PERSON>a sebesar 5,88W. Sedangkan untuk nilai rata-rata Energi Listrik terbesar berada pada hari ke-2 dan hari ke-5 sebesar 0,60Wh. Adapun yang berpengaruh dalam pengambilan data panel surya pada penelitian ini yaitu Intensitas Cahaya, dengan nilai rata-ratanya paling besar berada pada hari ke-7 yaitu sebesar 905,8 W/m2. Pembuatan alat penyemprot herbisida elektrik berbasis panel surya 20 Wp telah dilakukan dengan hasil yang sangat efektif dan berfungsi sesuai dengan tujuan pembuatan alat yaitu mengenalkan energi terbarukan sebagai energi alternatif yang mudah didapatkan khususnya dibidang pertanian.</p>", "Keywords": "Knapsack Sprayer;Renewable Energy;Solar Panel", "DOI": "10.55893/epsilon.v20i2.97", "PubYear": 2022, "Volume": "20", "Issue": "2", "JournalId": 100911, "JournalTitle": "EPSILON: Journal of Electrical Engineering and Information Technology", "ISSN": "1693-4989", "EISSN": "2745-5688", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jurusan Teknik Elektro Fakultas Teknik Universitas Bangka Belitung"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jurusan Teknik Elektro Fakultas Teknik Universitas Bangka Belitung"}, {"AuthorId": 3, "Name": "<PERSON> <PERSON>", "Affiliation": "Jurusan Teknik Elektro Fakultas Teknik Universitas Bangka Belitung"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Bangka Belitung"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Telkom"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Medan Area"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Andalas"}], "References": []}, {"ArticleId": 106047055, "Title": "HM-SMF: An Efficient Strategy Optimization using a Hybrid Machine Learning Model for Stock Market Prediction", "Abstract": "<p>Stock market forecasting is a significant task, and investing in the stock marketplace is a significant part of monetary research due to its high risk. Therefore, accurate forecasting of stock market analysis is still a challenge. Due to stable and volatile data, stock market forecasting remains a major challenge for investors. Recent machine learning (ML) models have been able to reduce the risk of stock market forecasting. However, diversity remains a key challenge in developing better erudition models and extracts more intellectually priceless qualities to auxiliary advanced predictability. In this paper, we propose an efficient strategy optimization using a hybrid ML model for stock market prediction (HM-SMP). The first contribution of the proposed HM-SMP model is to introduce chaos-enhanced firefly bowerbird optimization (CEFBO) algorithm for optimal feature selection among multiple features which reduce the data dimensionality. Second, we develop a hybrid multi-objective capuchin with a recurrent neural network (HC-RNN) for the prediction of the stock market which enhances the prediction accuracy. We use supervised RNN to predict the closing price. Finally, to estimate the presence of the proposed HM-SMP model through the benchmark, stock market datasets and the performance can be compared with the existing state-of-the-art models in terms of accuracy, precision, recall, and [Formula: see text]-measure.</p>", "Keywords": "Stock market prediction; CEFBO; HC-RNN; optimal feature selection; prediction", "DOI": "10.1142/S021946782450013X", "PubYear": 2024, "Volume": "24", "Issue": "2", "JournalId": 14884, "JournalTitle": "International Journal of Image and Graphics", "ISSN": "0219-4678", "EISSN": "1793-6756", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, JNTUA, Ananthapuramu 515002, Andhra Pradesh, India"}, {"AuthorId": 2, "Name": "B. Venkata Rama<PERSON> Reddy", "Affiliation": "Department of CSE, KSRM College of Engineering, Kadapa 516003, Andhra Pradesh, India"}], "References": [{"Title": "A comprehensive evaluation of ensemble learning for stock-market prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Short-term stock market price trend prediction using a comprehensive deep learning system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "1", "Page": "66", "JournalTitle": "Journal of Big Data"}, {"Title": "Applications of deep learning in stock market prediction: Recent progress", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115537", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106047082, "Title": "Aircraft landing gear system with magnetorheological shock strut: Performance evaluation via drop test", "Abstract": "<p>This work presents a magnetorheological (MR) damper applicable to the landing gear system in the aircraft. The proposed MR landing gear (MRLG) is modeled as a two degrees of freedom (2-DOF) system for the drop performance evaluation. A mathematical model of the governing equations is then formulated considering a flow path whose pressure depends on the major and minor losses. The flow path of MR fluid is composed of annular orifice and bypass as the required damping force for compression and extension is much different, and relief valve operates in conformity with the motion direction of the piston rod. An entry region is added to guarantee precise controllability of the field-dependent damping force. In addition, the magnetic intensity and total loss coefficient are estimated using magnetic analysis and computational fluid dynamics tools, respectively. It is demonstrated from the drop test based on the Federal Aviation Regulation (FAR) Part 23 that the shock struct efficiency (SSE) of the proposed MRLG can be increased from 42% at zero current to 84% at 1 A, and the agreement between simulation and experimental results is excellent showing similar pneumatic force, strut force, and jerk behavior which validates the accuracy of the proposed model.</p>", "Keywords": "", "DOI": "10.1177/1045389X221147632", "PubYear": 2023, "Volume": "34", "Issue": "13", "JournalId": 953, "JournalTitle": "Journal of Intelligent Material Systems and Structures", "ISSN": "1045-389X", "EISSN": "1530-8138", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Inha University, Incheon, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Inha University, Incheon, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, The State University of New York, Korea (SUNY Korea), Incheon, Korea;Department of Mechanical Engineering, Industrial University of Ho Chi Minh City (IUH), Ho Chi Minh City, Vietnam"}], "References": [{"Title": "Effects of magnetic core parameters on landing stability and efficiency of magnetorheological damper-based landing gear system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "2", "Page": "198", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}, {"Title": "Landing efficiency control of a six degrees of freedom aircraft model with magneto-rheological dampers: Part 2—control simulation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "12", "Page": "1303", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}, {"Title": "Landing efficiency control of a six-degree-of-freedom aircraft model with magnetorheological dampers: Part 1—Modeling", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "12", "Page": "1290", "JournalTitle": "Journal of Intelligent Material Systems and Structures"}]}, {"ArticleId": 106047148, "Title": "User Acceptance of Autonomous Vehicles: Review and Perspectives on the Role of the Human-machine Interfaces", "Abstract": "Although autonomous driving has significantly developed in the last years, its acceptance by users is still low, even due to the different interaction modalities between the human agent and Autonomous Vehicles (AVs). Therefore, this paper proposes an analysis of the existing research on the influence of Human-Machine Interfaces (HMIs) on the user acceptance of AVs from the perspective of interaction design. The authors reviewed the fundamental changes in the way users interact with AVs. The paper focuses on the transfer of the vehicle control between the human and the artificial intelligence agent, the user experience of Non-Driving-Related Tasks (NDRTs) and sharing autonomous driving in public transportation, and the impact of external HMI on Vulnerable Road Users (VRUs). In addition, the paper analyzes the concept of acceptability and describes the existing user acceptance models. Finally, the paper explores the future challenges for promoting the design potential of autonomous vehicle HMIs and proposes areas worthy of research to increase the user’s acceptance of this technology. © 2023 CAD Solutions, LLC.", "Keywords": "autonomous vehicles; human-machine interface; user acceptance model; vulnerable road users", "DOI": "10.14733/cadaps.2023.987-1004", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "Ming Yan", "Affiliation": "Politecnico di Milano, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Politecnico di Milano, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Politecnico di Milano, Italy"}], "References": []}, {"ArticleId": 106047169, "Title": "Retraction Note: A novel flexible data analytics model for leveraging the efficiency of smart education", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-023-07866-x", "PubYear": 2023, "Volume": "27", "Issue": "6", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology, Skyline University College, Sharjah, United Arab Emirates"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology, Skyline University College, Sharjah, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology, Skyline University College, Sharjah, United Arab Emirates"}], "References": []}, {"ArticleId": 106047277, "Title": "A vehicle detection method based on disparity segmentation", "Abstract": "<p>The detection of small objects has always been one of the key challenges in vehicle detection. In this work, a standard for dividing the object more accurately than traditional methods is presented. Based on the division standard of disparity segmentation, we propose a novel multi-scale detection network aiming to reduce the transmission of redundant information between each scale. We divide the objects by depth, which is the distance from the object to the viewpoint. Then, a multi-branch architecture providing specialized detection for objects of each scale separately is constructed. Through ablation experiments, our method achieves an increase of 1.63 to 2.01 mAP compared with the baseline method. On the KITTI dataset, our method combined with state-of-arts achieves an increase of 3.54 mAP for small objects and 0.79 mAP for medium objects.</p>", "Keywords": "Object detection; Multi-scale; Disparity segmentation; Stereovision", "DOI": "10.1007/s11042-023-14360-x", "PubYear": 2023, "Volume": "82", "Issue": "13", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Tongji Univerity, Shanghai, China"}], "References": [{"Title": "Multiple attention networks for stereo matching", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "18", "Page": "28583", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 106047439, "Title": "Conditional autoencoder pre-training and optimization algorithms for personalized care of hemophiliac patients", "Abstract": "<p>This paper presents the use of deep conditional autoencoder to predict the effect of treatments for patients suffering from hemophiliac disorders. Conditional autoencoder is a semi-supervised model that learns an abstract representation of the data and provides conditional reconstruction capabilities. Such models are suited to problems with limited and/or partially observable data, common situation for data in medicine. Deep conditional autoencoders allow the representation of highly non-linear functions which makes them promising candidates. However, the optimization of parameters and hyperparameters is particularly complex. For parameter optimization, the classical approach of random initialization of weight matrices works well in the case of simple architectures, but is not feasible for deep architectures. For hyperparameter optimization of deep architectures, the classical cross-validation method is costly. In this article, we propose solutions using a conditional pre-training algorithm and incremental optimization strategies. Such solutions reduce the variance of the estimation process and enhances convergence of the learning algorithm. Our proposal is applied for personalized care of hemophiliac patients. Results show better performances than generative adversarial networks (baseline) and highlight the benefits of your contribution to predict the effect of treatments for patients.</p>", "Keywords": "deep learning; Autoencoder; Incremental optimisation; Conditional pre-training; global assays; Hemophilia", "DOI": "10.3389/frai.2023.1048010", "PubYear": 2023, "Volume": "6", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ENIB, France; IRL 2010, CNRS, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "ENIB, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ENIB, France"}], "References": [{"Title": "DEVDAN: Deep evolving denoising autoencoder", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "390", "Issue": "", "Page": "297", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": *********, "Title": "Retraction Note: Multi-criteria-based approach for job scheduling in industry 4.0 in smart cities using fuzzy logic", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00500-023-07869-8", "PubYear": 2023, "Volume": "27", "Issue": "6", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Kyung Hee University, Seoul, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Kongu Engineering College, Erode, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Information Sciences, Northumbria University, Newcastle Upon Tyne, UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The PNG University of Technology, Lae, Papua New Guinea"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Oviedo, Oviedo, Spain"}], "References": []}, {"ArticleId": 106047501, "Title": "A Novel Soft Clustering Method for Detection of Exudates", "Abstract": "One of the earliest indications of diabetes consequence is Diabetic Retinopathy (DR), the main contributor to blindness worldwide. Recent studies have proposed that Exudates (EXs) are the hallmark of DR severity. The present study aims to accurately and automatically detect EXs that are difficult to detect in retinal images in the early stages. An improved Fusion of Histogram-Based Fuzzy C-Means Clustering (FHBFCM) by a New Weight Assignment Scheme (NWAS) and a set of four selected features from stages of pre-processing to evolve the detection method is proposed. The features of DR train the optimal parameter of FHBFCM for detecting EXs diseases through a stepwise enhancement method through the coarse segmentation stage. The histogram-based is applied to find the color intensity in each pixel and performed to accomplish Red, Green, and Blue (RGB) color information. This RGB color information is used as the initial cluster centers for creating the appropriate region and generating the homogeneous regions by Fuzzy C-Means (FCM). Afterward, the best expression of NWAS is used for the delicate detection stage. According to the experiment results, the proposed method successfully detects EXs on the retinal image datasets of DiaretDB0 (Standard Diabetic Retinopathy Database Calibration level 0), DiaretDB1 (Standard Diabetic Retinopathy Database Calibration level 1), and STARE (Structured Analysis of the Retina) with accuracy values of 96.12%, 97.20%, and 93.22%, respectively. As a result, this study proposes a new approach for the early detection of EXs with competitive accuracy and the ability to outperform existing methods by improving the detection quality and perhaps significantly reducing the segmentation of false positives. © 2023 CRL Publishing. All rights reserved.", "Keywords": "Diabetic retinopathy; exudates; histogram-based fuzzy C-means clustering; retinal images", "DOI": "10.32604/csse.2023.034901", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Kittipol W<PERSON>", "Affiliation": "Mahasarakham Business School, Mahasarakham University, Mahasarakham, 44150, Thailand"}], "References": [{"Title": "A new deep learning approach for the retinal hard exudates detection based on superpixel multi-feature extraction and patch-based CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "521", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 106047513, "Title": "Regression Based Performance Analysis and Fault Detection in Induction Motors by Using Deep Learning Technique", "Abstract": "<p>The recent improvements related to the area of electric locomotive, power electronics, assembly processes and manufacturing of machines have increased the robustness and reliability of induction motors. Regardless of the increased availability, the application of induction motors in many fields alleges the need for operating state supervision and condition monitoring. In other words, fault identification at the initial stage helps make appropriate control decisions, influencing product quality as well as providing safety. Inspired by these demands, this work proposes a regression based modeling for the analysis of performance in induction motors. In this approach, the feature extraction process is combined with classification for efficient fault detection. Deep Belief Network (DBN) stacked with multiple Restricted Boltzmann Machine (RBM) is exploited for the robust diagnosis of faults with the adoption of training process. The influences of harmonics over induction motors are identified and the losses are mitigated. The simulation of the suggested approach and its comparison with traditional approaches are executed. An overall accuracy of 99.5% is obtained which in turn proves the efficiency of DBN in detecting faults.</p>", "Keywords": "induction motor;DBN;RBM;Fast Fourier Transform (FFT);regression modeling", "DOI": "10.14201/adcaij.28435", "PubYear": 2023, "Volume": "11", "Issue": "3", "JournalId": 20117, "JournalTitle": "ADCAIJ: <PERSON><PERSON><PERSON><PERSON> IN DISTRIBUTED COMPUTING AND ARTIFICIAL INTELLIGENCE JOURNAL", "ISSN": "", "EISSN": "2255-2863", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Communication Engineering"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Communication Engineering"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Communication Engineering"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Communication Engineering"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Communication Engineering"}], "References": []}, {"ArticleId": 106047557, "Title": "Al-Biruni Earth Radius Optimization for COVID-19 Forecasting", "Abstract": "Several instances of pneumonia with no clear etiology were recorded in Wuhan, China, on December 31, 2019. The world health organization (WHO) called it COVID-19 that stands for \"Coronavirus Disease 2019,\" which is the second version of the previously known severe acute respiratory syndrome (SARS) Coronavirus and identified in short as (SARSCoV-2). There have been regular restrictions to avoid the infection spread in all countries, including Saudi Arabia. The prediction of new cases of infections is crucial for authorities to get ready for early handling of the virus spread. Methodology: Analysis and forecasting of epidemic patterns in new SARSCoV-2 positive patients are presented in this research using metaheuristic optimization and long short-term memory (LSTM). The optimization method employed for optimizing the parameters of LSTM is Al-Biruni Earth Radius (BER) algorithm. Results: To evaluate the effectiveness of the proposed methodology, a dataset is collected based on the recorded cases in Saudi Arabia between March 7th, 2020 and July 13th, 2022. In addition, six regression models were included in the conducted experiments to show the effectiveness and superiority of the proposed approach. The achieved results show that the proposed approach could reduce the mean square error (MSE), mean absolute error (MAE), and R2 by 5.92%, 3.66%, and 39.44%, respectively, when compared with the six base models. On the other hand, a statistical analysis is performed to measure the significance of the proposed approach. Conclusions: The achieved results confirm the effectiveness, superiority, and significance of the proposed approach in predicting the infection cases of COVID-19. © 2023 CRL Publishing. All rights reserved.", "Keywords": "Al-Biruni earth radius algorithm; COVID-19 prediction; LSTM; meta-heuristic optimization", "DOI": "10.32604/csse.2023.034697", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Communications and Electronics, Delta Higher Institute of Engineering and Technology, Mansoura, 35111, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Department of Computer Science, Faculty of Computer and Information Sciences, Ain Shams University, Cairo, 11566, Egypt; Department of Computer Science, College of Computing and Information Technology, Shaqra University11961, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering and Control Systems Department, Faculty of Engineering, Mansoura University, Mansoura, 35516, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of System Programming, South Ural State University, Chelyabinsk, 454080, Russian Federation"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of System Programming, South Ural State University, Chelyabinsk, 454080, Russian Federation"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh, 11671, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P.O. Box 84428, Riyadh, 11671, Saudi Arabia"}], "References": [{"Title": "A self-adaptive virus optimization algorithm for continuous optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "17", "Page": "13147", "JournalTitle": "Soft Computing"}, {"Title": "Forecasting of COVID19 per regions using ARIMA models and polynomial functions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106610", "JournalTitle": "Applied Soft Computing"}, {"Title": "COVID-19 Pandemic: ARIMA and Regression Model-Based Worldwide Death Cases Predictions", "Authors": "<PERSON><PERSON>s <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "5", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Forecasting the dynamics of cumulative COVID-19 cases (confirmed, recovered and deaths) for top-16 countries using statistical machine learning models: Auto-Regressive Integrated Moving Average (ARIMA) and Seasonal Auto-Regressive Integrated Moving Average (SARIMA)", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "107161", "JournalTitle": "Applied Soft Computing"}, {"Title": "Time series predicting of COVID-19 based on deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "468", "Issue": "", "Page": "335", "JournalTitle": "Neurocomputing"}, {"Title": "Robust Prediction of the Bandwidth of Metamaterial Antenna Using Deep Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "2", "Page": "2305", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Optimized Two-Level Ensemble Model for Predicting the Parameters of Metamaterial Antenna", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "1", "Page": "917", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 106047570, "Title": "Student Perceptions of Hybrid Courses in Higher Education", "Abstract": "<p>Online open-ended and closed-ended surveys were conducted in 2014-2016 among 191 students at a small, private university located in South Florida. Our main goals were to evaluate student perceptions of in-class and out-of-class assignments in hybrid courses, determine what students value most about these modes of learning, and recommend ways to maximize advantages and minimize disadvantages of each. We discovered that students value instant feedback and interacting with their peers when they are in class as in-class assignments were rated significantly higher than out-of-class assignments (p < 0.05) and higher ratings were significantly associated with responses associated with student-student interaction (p <0.05). However, the time and place constraints of in-class work limits their ability to formulate their thoughts. Out-of-class assignments were appreciated for their flexibility of pace, time, and place, although students reported time-management problems as well. Like for in-class work, students valued the opportunity of reading their peers’ answers as higher assignment ratings for out-of-class assignments were significantly associated with students’ ability to read the responses of others. Although participants did not report an effect from specific learning differences, having to write for out-of-class work (as opposed to speaking in class) was reported as a hurdle. We discuss strategies for improving in-class and out-of-class assignments based on our study results.</p>", "Keywords": "Hybrid learning;student perceptions;logistic regression;qualitative analysis", "DOI": "10.24059/olj.v26i4.2939", "PubYear": 2022, "Volume": "26", "Issue": "4", "JournalId": 37128, "JournalTitle": "Online Learning", "ISSN": "2472-5749", "EISSN": "2472-5730", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Lynn University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Lynn University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Lynn University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Lynn University"}], "References": []}, {"ArticleId": 106047800, "Title": "Energy-Aware Medium Access Control Protocol and Clustering Algorithm for Clustered Wireless Sensor Networks with High Altitude Platforms as the Base Station", "Abstract": "This paper presents the work of the Medium Access Control (MAC) protocol for clustered wireless sensor networks (WSN) with High Altitude Platforms (HAPS) as the base station. To reduce energy consumption in WSN, clustering is carried out so that the sensors only send data to their respective cluster heads rather than directly to the HAPS. Unlike base stations which have a relatively fixed position and coverage, HAPS can shift horizontally, vertically, and even inclination movements which will affect the coverage of the nodes that can be covered. Therefore, an adaptive clustering algorithm was developed that can adapt to this issue. The main consideration in designing a MAC that is integrated with clustering algorithms is energy efficiency so that sensor nodes can last as long as possible. To achieve this goal the formation of clusters is based on a cost function based on the remaining energy, distance, and the number of sensor nodes in each cluster. Additionally, MAC in intra-cluster communication adopts the concept of scheduling based on the ratio between the distance and the remaining energy at the sensor node to avoid sensor nodes from idle listening, thereby saving energy. Furthermore, the sensor node behavior is modeled as consisting of four states, namely sleep, contention, back-off, and active Tx/Rx. The performance of the proposed MAC Protocol is evaluated through several simulations based on lifetime and energy consumption. In addition, simulations are carried out to test how far the HAPS can shift in position and keep all sensor nodes connected to the network. The simulation results show that the HAPS displacement tolerance of the proposed MAC protocol is better than the recommendations given by ITU-R F.1891. © 2022, School of Electrical Engineering and Informatics. All rights reserved.", "Keywords": "clustering; HAPS; MAC; scheduling; WSN", "DOI": "10.15676/ijeei.2022.14.4.10", "PubYear": 2022, "Volume": "14", "Issue": "4", "JournalId": 23441, "JournalTitle": "International Journal on Electrical Engineering and Informatics", "ISSN": "2085-6830", "EISSN": "2087-5886", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics Bandung Institute of Technology Bandung, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics Bandung Institute of Technology Bandung, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics Bandung Institute of Technology Bandung, Indonesia"}], "References": []}, {"ArticleId": 106047812, "Title": "Scaled Conjugate Gradient Function Fitting Based Artificial Neural Network to Mitigate Power Quality Issues in an AC Micro Grid", "Abstract": "The scarcity of conventional energy sources leads to the exponentially use of renewable energy sources to meet the huge power demand. However, using renewable sources is challenging because of grid integration problems, energy loss, and emission of polluting agents. Micro Grid (MG) plays a vital role by replacing the traditional depleting resources with renewable and non-traditional resources. The alternate energy sources like solar, wind and fuel cell are environmentally friendly and used as Distributed Generators (DG) in the MG concept, but due to the nonlinear characteristics of the sources and their environmental dependency, the Power Quality (PQ) issues arise during MG operation. Thus, it is essential to design an intelligent controller to maintain the PQ issues within the specified values. In this paper, a Scaled Conjugate Gradient Function Fitting based Artificial Neural Network (SCGFANN) with centralized distribution static compensator controller has been designed for the small and large MG and is simulated in MATLAB/Simulink environment. The effectiveness of the proposed controller has been investigated in various conditions such as voltage sag/swell, unbalance, disturbances, and frequency distortion in the case of a simple MG. The analysis of the switching delay in the controller action has been performed to scrutinize the significance of the suggested controller in the case of a complex practical MG. It can be observed from the simulation results that the proposed controller outperforms the conventional Fuzzy-PI controller in terms of better stability and enhanced PQ mitigation, thus justifying its real-time implementation. © 2022, School of Electrical Engineering and Informatics. All rights reserved.", "Keywords": "Distributed Energy Resources (DERs); Distribution Static Compensator (D-STATCOM); Micro Grid (MG); renewable energy sources; Scaled Conjugate Gradient Function Fitting Artificial Neural Network (SCGFANN)", "DOI": "10.15676/ijeei.2022.14.4.1", "PubYear": 2022, "Volume": "14", "Issue": "4", "JournalId": 23441, "JournalTitle": "International Journal on Electrical Engineering and Informatics", "ISSN": "2085-6830", "EISSN": "2087-5886", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of EEE ITER, SOA Deemed to be University Bhubaneswar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of EEE ITER, SOA Deemed to be University Bhubaneswar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of EEE ITER, SOA Deemed to be University Bhubaneswar, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of EEE ITER, SOA Deemed to be University Bhubaneswar, India"}], "References": []}, {"ArticleId": 106047820, "Title": "Energy Loss Allocation in Radial Distribution Networks Considering Average Hourly Variation of Load with Renewable DGs", "Abstract": "This paper introduces a new method of energy loss allocation (ELA) from the direct relationship existing between node injected complex power and energy loss in terms of system node voltages. It eliminates the complexity associated with the bifurcation of the cross-term, analytically, without any assumptions and approximations. The proposed approach does not require any reconciliation procedure for final settlement of losses as it provides exact allocations as obtained by load flow calculation. To fulfil power demand of the consumers, three renewable distributed generators (DGs) are incorporated into the distribution system and energy allocations are performed with due consideration to location, size and time evolution of the consumers and DGs over an entire day. To verify effectiveness, investigations are carried out on the ELA results of a 51-bus radial distribution network (RDN) at different load models and are compared with that of the other established methods. Further, the responses as regard to DG power injections are also investigated at both unit and lagging power factor conditions of the DG units to validate superiority of the developed procedure over other discussed methods. Thus, the novelty of this paper is about introduction of a new method of ELA without any assumptions, approximations and reconciliations implemented in 51 bus RDN with renewable DGs and average hourly variation of loads. © 2022, School of Electrical Engineering and Informatics. All rights reserved.", "Keywords": "distributed generators; Energy loss allocation; radial distribution network", "DOI": "10.15676/ijeei.2022.14.3.8", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 23441, "JournalTitle": "International Journal on Electrical Engineering and Informatics", "ISSN": "2085-6830", "EISSN": "2087-5886", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, GITA Autonomous College, Bhubaneswar, Odisha, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Centre for Advance Post Graduate Studies,  Biju Patnaik University of Technology, Odisha, Rourkela, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Gandhi Engineering College, Bhubaneswar, Odisha, India"}], "References": []}, {"ArticleId": 106047821, "Title": "Design of SWB-BPF with Notched Band Using MMR for UWB Applications", "Abstract": "A super wideband (SWB) (3.1GHz – 24.58 GHz) BPF is proposed with a notch-band using stub-loaded multi-mode resonator (MMR). The MMR is created by two pairs of stepped impedance coupling strip lines with open stubs, tightly coupled with four identical high impedance transmission lines. The center of each pair of symmetric high impedance transmission lines is connected with single open-ended shunt stub. The first responses of UWB with frequency range 3.1 – 10.5 GHz and the second response of 14.86 – 24.58 GHz are reported here for some part of FSS (Fixed-satellite radio-communication service ITU band), Space Research, and Radio Navigation purposes. A notch band appears at 12 GHz which could effectively suppress the interference of the fixed and broadcasting satellite services of the radio communication equipment. The filter is realized on FR-4 epoxy substrate (εr = 4.4 and thickness = 1.6 mm) and measured to validate the predicted results. The group delay of the filter is nearly constant and varies in the range 0.26 – 0.6 ns over the desired passband. © 2022, School of Electrical Engineering and Informatics. All rights reserved.", "Keywords": "Band Pass Filter (BPF); Fixed-satellite service (FSS); Multimode Resonator (MMR); Super Wide Band (SWB); Ultra Wide Band (UWB)", "DOI": "10.15676/ijeei.2022.14.3.5", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 23441, "JournalTitle": "International Journal on Electrical Engineering and Informatics", "ISSN": "2085-6830", "EISSN": "2087-5886", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Telecommunication Engineering Indian Institute of Engineering Science & Technology, Shibpur, Howrah, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics & Telecommunication Engineering Ramarao Adik Institute of Technology, D. Y. Patil University Campus, Navi Mumbai, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Telecommunication Engineering Indian Institute of Engineering Science & Technology, Shibpur, Howrah, India"}], "References": []}, {"ArticleId": 106047843, "Title": "SADetection: Security Mechanisms to Detect SLAAC Attack in IPv6 Link-Local Network", "Abstract": "Neighbour Discovery Protocol (NDP) attacks are a serious security concern for IPv6. Attackers utilise the Stateless Address Auto-configuration (SLAAC) NDP attack type to target the SLAAC process. SLAAC attacks can compromise an IPv6 link-local network and expose private data. Attack detection mechanisms including RA-Guard, Snort IPv6 Plugin, SLAAC detection method by <PERSON><PERSON><PERSON><PERSON> et al., and SLAAC Security Method by <PERSON><PERSON><PERSON> et al. have been proposed by researchers to address this issue. However, the detection algorithms have a number of shortcomings, including a complete reliance on preconfigured router databases. Additionally, fragment packets and packets with Hop-by-Hop Options and Destination Options extension headers are not detectable by the detection techniques for hidden RA messages. In this study, a rule-based detection method called SADetection is proposed for use in IPv6 link-local networks to identify SLAAC attacks. Both an illegal Router Advertisement (RA) message and a concealed RA message in a packet with an extension header have been found by SADetection. SADetection has demonstrated a detection accuracy of 98\\% percent and the capacity to defend an IPv6 link-local network from SLAAC attacks.", "Keywords": "IPv6; SADetection; Security mechanism; SLAAC attack", "DOI": "10.31449/inf.v46i9.4441", "PubYear": 2022, "Volume": "46", "Issue": "9", "JournalId": 60928, "JournalTitle": "Informatica", "ISSN": "0350-5596", "EISSN": "1854-3871", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Advanced IPv6 Centre, Universiti Sains Malaysia, 11800, Penang, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Advanced IPv6 Centre, Universiti Sains Malaysia, Penang, 11800, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technology Engineering, Shatt Al-Arab University College, Basrah, Iraq"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Advanced IPv6 Centre, Universiti Sains Malaysia, Penang, 11800, Malaysia"}], "References": []}, {"ArticleId": 106048246, "Title": "Enhancement of Partial Discharge Resistance and Breakdown Strength Characteristics of Low-Density Polyethylene Nanocomposites Using Plasma Treatment Method", "Abstract": "Insulations in the power cable system are prone to ageing and degradation, eventually leading to a complete breakdown. One of the solutions to reduce insulation breakdown in polymeric insulation is by adding nanofillers into the polymer matrices of the insulation to form polymer nanocomposites. However, the addition of the nanofiller into the polymer usually results in agglomeration inside the nanocomposites. Recently, atmospheric pressure plasma (APP) has been introduced by adopting the nanofiller's surface modification method to hinder agglomeration formation. The aims of using APP are to enhance the nanofiller-polymer interfaces and improve the dielectric properties, emphasizing partial discharge (PD) resistance and AC breakdown strength. In this study, APP has been used to treat boron nitride (BN) and silicon dioxide (SiO2) nanoparticle surfaces for the purpose of enhancing the compatibility with low-density polyethylene (LDPE) matrices. Untreated and plasma-treated nanoparticles have been added into LDPE with different filler loading of 1 wt%, 3 wt% and 5 wt% via the direct compounding method. Compared with untreated nanocomposites, the 30-minutes plasma-treated nanocomposites could improve the PD resistance by reducing the PD magnitude up to 513 pC and reducing the PD number to 11661. Moreover, the AC breakdown strength of the plasma-treated nanocomposites had increased from 0.53 kV/mm to 26.65 kV/mm. If compared to LDPE/BN nanocomposites, it was discovered that the LDPE/SiO2 nanocomposites displayed significantly better dielectric characteristics. In addition, plasma treatment of the nanoparticles could produce nanocomposites with better formulation stability and promising dielectric performance, which can prolong the insulation's lifetime and ensure the reliability of the power supply. © 2022, School of Electrical Engineering and Informatics. All rights reserved.", "Keywords": "atmospheric pressure plasma treatment; breakdown strength; low-density polyethylene; partial discharge; Polymer nanocomposites", "DOI": "10.15676/ijeei.2022.14.3.11", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 23441, "JournalTitle": "International Journal on Electrical Engineering and Informatics", "ISSN": "2085-6830", "EISSN": "2087-5886", "Authors": [{"AuthorId": 1, "Name": "Rizda Fitri Kurnia", "Affiliation": "Institute of High Voltage and High Current, Universiti Teknologi Malaysia,  Johor Bahru, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of High Voltage and High Current, Universiti Teknologi Malaysia,  Johor Bahru, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Institute of High Voltage and High Current, Universiti Teknologi Malaysia,  Johor Bahru, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of High Voltage and High Current, Universiti Teknologi Malaysia,  Johor Bahru, Malaysia"}, {"AuthorId": 5, "Name": "Zolkafle Buntat", "Affiliation": "Institute of High Voltage and High Current, Universiti Teknologi Malaysia,  Johor Bahru, Malaysia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of High Voltage and High Current, Universiti Teknologi Malaysia,  Johor Bahru, Malaysia"}], "References": []}, {"ArticleId": 106048302, "Title": "On COVID-19 Pandemic-Induced Attitudinal Changes in Software Engineering Teaching and Learning", "Abstract": "<p>This note outlines certain notable changes observed in the attitudes towards and approaches in teaching and learning software engineeringrelated courses following the end of the COVID-19 pandemic. It views these changes from the interrelated perspectives of the primary stakeholders of software engineering education (SEE), namely administrators, educators, and students. It seems that while some changes may be ephemeral and transient, the others may be essential and permanent. From a students' perspective, the transformation of SEE appears to be generally for the better.</p>", "Keywords": "accessibility; cybersecurity; pandemic; software engineering education", "DOI": "10.1145/3573074.3573078", "PubYear": 2023, "Volume": "48", "Issue": "1", "JournalId": 12882, "JournalTitle": "ACM SIGSOFT Software Engineering Notes", "ISSN": "0163-5948", "EISSN": "1943-5843", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Concordia University, Montreal, PQ, Canada"}], "References": [{"Title": "The Experience of Tests during the COVID-19 Pandemic-Induced Emergency Remote Teaching", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "4", "Page": "481", "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering"}, {"Title": "DevOps \n benefits: A systematic literature review", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "9", "Page": "1905", "JournalTitle": "Software: Practice and Experience"}]}, {"ArticleId": 106048332, "Title": "Attention‐based neural network for end‐to‐end music separation", "Abstract": "The end‐to‐end separation algorithm with superior performance in the field of speech separation has not been effectively used in music separation. Moreover, since music signals are often dual channel data with a high sampling rate, how to model long‐sequence data and make rational use of the relevant information between channels is also an urgent problem to be solved. In order to solve the above problems, the performance of the end‐to‐end music separation algorithm is enhanced by improving the network structure. Our main contributions include the following: (1) A more reasonable densely connected U‐Net is designed to capture the long‐term characteristics of music, such as main melody, tone and so on. (2) On this basis, the multi‐head attention and dual‐path transformer are introduced in the separation module. Channel attention units are applied recursively on the feature map of each layer of the network, enabling the network to perform long‐sequence separation. Experimental results show that after the introduction of the channel attention, the performance of the proposed algorithm has a stable improvement compared with the baseline system. On the MUSDB18 dataset, the average score of the separated audio exceeds that of the current best‐performing music separation algorithm based on the time‐frequency domain (T‐F domain).", "Keywords": "", "DOI": "10.1049/cit2.12163", "PubYear": 2023, "Volume": "8", "Issue": "2", "JournalId": 26444, "JournalTitle": "CAAI Transactions on Intelligence Technology", "ISSN": "2468-6557", "EISSN": "2468-2322", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Technology  Beijing China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Communication University of China  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "China Electronics Standardization Institute  Beijing China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Gachon University  Seongnam South Korea"}], "References": []}, {"ArticleId": 106048494, "Title": "Handwritten digits recognition using transfer learning", "Abstract": "Researchers often focus on building models that maximize overall predictive accuracy. In practice, however, it can be important for a model to yield good accuracy with each class value. Toward this end, a new recognition with a one-step verification methodology is proposed. It emphasizes the accuracy of each class value. The proposed discriminative system constructs an ensemble using several deep Convolutional Neural Networks (CNNs) with the help of statistical information. To the best of our knowledge, this is the first ensemble model that combines many deep CNNs with a focus on maximizing the accuracy for each class, rather than just overall accuracy. Experimental results show that the demonstration models achieved accuracy in the range of 97.82% to 99.72% within only a few epochs, rivaling the state-of-the-art. These results indicate that the performance of the proposed approach substantially improves the intra-class correlation, leading to improved classification accuracy for each class.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2023.108604", "PubYear": 2023, "Volume": "106", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Engineering Department, Mustansiriyah University, Baghdad, Iraq"}], "References": [{"Title": "An adaptive fractional-order BP neural network based on extremal optimization for handwritten digits recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "391", "Issue": "", "Page": "260", "JournalTitle": "Neurocomputing"}, {"Title": "Hybrid CNN-SVM Classifier for Handwritten Digit Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2554", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 106048622, "Title": "The Use of Personal Learning Environment to Support an Online Collaborative Strategy in Vocational Education Pedagogy Course", "Abstract": "<p>Online distance learning, on the one side, makes the learning process more efficient and flexible. However, on the other hand, it produces learning loss within a particular educational period and makes students less enthusiastic about building a collaborative attitude. In general, online learning platform providers have provided support for collaboration activity features, but in reality, they have not been able to support effective interaction. Experts believe that using the Personal Learning Environment (PLE) as an IT tool in the learning process can help create an effective online collaboration environment. This study found that PLE was proven to increase collaborative activities in online learning significantly. The reflection feature is the type of PLE that has the most significant influence on creating a good collaboration environment. The research also found that suitable collaborative activities can increase students' understanding of TVET pedagogy.</p>", "Keywords": "Personal learning environment;online collaborative strategy;TVET pedagogy", "DOI": "10.3991/ijim.v17i02.34565", "PubYear": 2023, "Volume": "17", "Issue": "2", "JournalId": 15508, "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)", "ISSN": "", "EISSN": "1865-7923", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Vocational Teacher Education, Universitas Ahmad Da<PERSON>, Yogyakarta, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electronic Engineering Vocational Education, Universitas Ahmad Da<PERSON>, Yogyakarta, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Universitas Negeri Yogyakarta, Yogyakarta, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automotive Vocational Education, Universitas Ahmad <PERSON>, Yogyakarta, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sultan Idris Education University, Perak, Malaysia"}], "References": []}, {"ArticleId": 106048842, "Title": "The Choice of Textual Knowledge Base in Automated Claim Checking", "Abstract": "<p> Automated claim checking is the task of determining the veracity of a claim given evidence retrieved from a textual knowledge base of trustworthy facts. While previous work has taken the knowledge base as given and optimized the claim-checking pipeline, we take the opposite approach—taking the pipeline as given, we explore the choice of the knowledge base. Our first insight is that a claim-checking pipeline can be transferred to a new domain of claims with access to a knowledge base from the new domain. Second, we do not find a “universally best” knowledge base—higher domain overlap of a task dataset and a knowledge base tends to produce better label accuracy. Third, combining multiple knowledge bases does not tend to improve performance beyond using the closest-domain knowledge base. Finally, we show that the claim-checking pipeline’s confidence score for selecting evidence can be used to assess whether a knowledge base will perform well for a new set of claims, even in the absence of ground-truth labels. </p>", "Keywords": "Automated Claim Verification; Textual Knowledge Bases; Evidence Selection; Information Retrieval; Content Management", "DOI": "10.1145/3561389", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 21716, "JournalTitle": "Journal of Data and Information Quality", "ISSN": "1936-1955", "EISSN": "1936-1963", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Geneva, Geneva, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Zurich, Switzerland"}], "References": [{"Title": "A Review on Fact Extraction and Verification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 106048853, "Title": "Integrating individual preferences into collective argumentation", "Abstract": "<p>In the field of collective argumentation, multiple agents may have different knowledge representations and individual preferences. In order to obtain a reasonable collective outcome for the group, either individual frameworks should be merged or individual preferences should be aggregated. However, framework merging and preference aggregation are different procedures, leading to disagreements on collective outcomes. In this paper, we figure out a solution to combine framework merging, argumentative reasoning and incomplete preference aggregation together. Furthermore, a couple of rational postulates are proposed to be the criteria for the rationality of collective outcomes obtained based on our approach.</p>", "Keywords": "", "DOI": "10.1093/logcom/exac097", "PubYear": 2023, "Volume": "33", "Issue": "2", "JournalId": 2581, "JournalTitle": "Journal of Logic and Computation", "ISSN": "0955-792X", "EISSN": "1465-363X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Philosophy, Zhejiang University , China; ZLAIRE, Zhejiang University, China; The State Key Lab of Brain-Machine Intelligence, Zhejiang University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Philosophy, Zhejiang University, China; ZLAIRE, Zhejiang University , China; The State Key Lab of Brain-Machine Intelligence, Zhejiang University, China"}], "References": [{"Title": "Representation, justification, and explanation in a value-driven agent: an argumentation-based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "5", "JournalTitle": "AI and Ethics"}]}, {"ArticleId": 106049096, "Title": "Malayalam Natural Language Processing: Challenges in Building a Phrase-Based Statistical Machine Translation System", "Abstract": "<p>Statistical Machine Translation (SMT) is a preferred Machine Translation approach to convert text in a specific language into another by automatically learning translations using a parallel corpus. SMT has been successful in producing quality translations in many foreign languages, but there are only a few works attempted in South Indian languages. The paper discusses on experiments conducted with SMT for Malayalam language and analyzes how the methods defined for SMT in foreign languages affect a Dravidian language, Malayalam. The baseline SMT model does not work for Malayalam due to its unique characteristics like agglutinative nature and morphological richness. Hence, the challenge is to identify where precisely the SMT model has to be modified such that it adapts the challenges of the language peculiarity into the baseline model and give better translations for English to Malayalam translation. The alignments between English and Malayalam sentence pairs, subjected to the training process in SMT, plays a crucial role in producing quality output translation. Therefore, this work focuses on improving the translation model of SMT by refining the alignments between English-Malayalam sentence pairs. The phrase alignment algorithms align the verb and noun phrases in the sentence pairs and develop a new set of alignments for the English-Malayalam sentence pairs. These alignment sets refines the alignments formed from Giza++ produced as a result of EM training algorithm. The improved Phrase Based SMT model trained using these refined alignments resulted in better translation quality, as indicated by the AER and BLUE scores.</p>", "Keywords": "Statistical Machine Translation; Malayalam; Machine Translation; Natural Language Processing; Dravidian Language; Alignments", "DOI": "10.1145/3579163", "PubYear": 2023, "Volume": "22", "Issue": "4", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Rajagiri School of Engineering & Technology Kerala, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Natural Language Processing Lab, Department of Computer Science, Cochin University of Science and Technology, Kerala, India"}], "References": [{"Title": "Deep learning-based techniques to enhance the precision of phrase-based statistical machine translation system for Indian languages", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1/2", "Page": "239", "JournalTitle": "International Journal of Computer Aided Engineering and Technology"}]}, {"ArticleId": 106049217, "Title": "Corrigendum", "Abstract": "Corrigendum to: <PERSON><PERSON>; Kill the Orchestra: On Music, Mods, and I<PERSON><PERSON> in <PERSON> Elder Scrolls on the Nexus Mods Platform. Journal of Sound and Music in Games 1 April 2021; 2 (2): 22–41. doi: https://doi.org/10.1525/jsmg.2021.2.2.22 The author would like to make the following addition: This article was made with the support of CESEM – Centro de Estudos de Sociologia e Estética Musical – NOVA FCSH, UIDB/00693/2020 with the financial support of FCT, I.P. through National funds and I’m currently being funded by the Portuguese public agency for science and education Fundação para a Ciência e Tecnologia (FCT) with a PhD Studentship referenced as SFRH/BD/139120/2018. © 2023 by the Society for the Study of Sound and Music in Games. All rights reserved.", "Keywords": "", "DOI": "10.1525/jsmg.2023.4.1.91", "PubYear": 2023, "Volume": "4", "Issue": "1", "JournalId": 73378, "JournalTitle": "Journal of Sound and Music in Games", "ISSN": "", "EISSN": "2578-3432", "Authors": [], "References": []}, {"ArticleId": 106049240, "Title": "Advancing 3D medical image analysis with variable dimension transform based supervised 3D pre-training", "Abstract": "The difficulties in both data acquisition and annotation substantially restrict the sample sizes of training datasets for 3D medical imaging applications. Therefore, it is non-trivial to build well-performing 3D convolutional neural networks from scratch. Previous efforts on 3D pre-training have frequently relied on self-supervised approaches, which use either predictive or contrastive learning on unlabeled data to build invariant 3D representations. However, because of the unavailability of large-scale supervision information, obtaining semantically invariant and discriminative representations from these learning frameworks remains problematic. In this paper, we revisit an innovative yet simple fully-supervised 3D network pre-training framework to take advantage of semantic supervision from large-scale 2D natural image datasets. With a redesigned 3D network architecture , reformulated natural images are used to address the problem of data scarcity and develop powerful 3D representations. Comprehensive experiments on five benchmark datasets demonstrate that the proposed pre-trained models can effectively accelerate convergence while also improving accuracy for a variety of 3D medical imaging tasks such as classification, segmentation, and detection. In addition, as compared to training from scratch, it can save up to 60% of annotation efforts. On the NIH DeepLesion dataset, it also achieves state-of-the-art detection performance, outperforming earlier self-supervised and fully-supervised pre-training approaches, as well as methods that do training from scratch. To facilitate further development of 3D medical models, our code and pre-trained model weights are publicly available at https://github.com/urmagicsmine/CSPR.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.01.012", "PubYear": 2023, "Volume": "529", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Deepwise Artificial Intelligence Laboratory, No.8, Haidian Avenue, Haidian District, Beijing, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Deepwise Artificial Intelligence Laboratory, No.8, Haidian Avenue, Haidian District, Beijing, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, The University of Hong Kong, Pokfulam, Hong Kong"}, {"AuthorId": 4, "Name": "Jiechao Ma", "Affiliation": "Deepwise Artificial Intelligence Laboratory, No.8, Haidian Avenue, Haidian District, Beijing, PR China"}, {"AuthorId": 5, "Name": "Yizhou Yu", "Affiliation": "Deepwise Artificial Intelligence Laboratory, No.8, Haidian Avenue, Haidian District, Beijing, PR China;Department of Computer Science, The University of Hong Kong, Pokfulam, Hong Kong;Corresponding author at: Department of Computer Science, The University of Hong Kong, Pokfulam, Hong Kong (Y. Yu)"}], "References": []}, {"ArticleId": 106049246, "Title": "Suppression of resilient targets via coordinated swarms", "Abstract": "Suppression of resilient targets via a network of agents is considered. The agents’ goal is to repeatedly return the states of the targets to a set that corresponds to all targets being sufficiently attenuated. Because the agents have their own inertia, which limits how quickly they can move from one target to another, a hysteresis gap is required between what is considered sufficient attenuation and the attenuation that is induced before moving on to a new target. The size of the hysteresis gap must also be large enough to account for the fact that there may be some stochastic delay between deciding to move on to a new target and agreeing on which new target to attenuate. This delay is typically induced by the dynamics of a stochastic, distributed agreement algorithm that the agents use to select the next target to attenuate. Hybrid, and stochastic hybrid, systems are used to model the closed-loop suppression dynamics, the mathematical property of recurrence is used to characterize the control goal, and <PERSON><PERSON><PERSON><PERSON>–<PERSON> functions are used to certify controller success when a suitable hysteresis gap is employed.", "Keywords": "Hybrid systems ; Stochastic hybrid systems ; Distributed control ; Recurrence ; <PERSON><PERSON><PERSON><PERSON>–<PERSON> functions", "DOI": "10.1016/j.sysconle.2023.105460", "PubYear": 2023, "Volume": "173", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ECE Department, University of California, Santa Barbara CA 93106-9560, USA"}], "References": []}, {"ArticleId": 106049247, "Title": "Quality-aware pattern diffusion for video object segmentation", "Abstract": "Recently, great progresses have been achieved under the support of memory mechanism when dealing with video object segmentation (VOS) problem. Despite its achievements, existing VOS approaches still suffer from abnormal samples, which derive from intrinsic video artifacts such as occlusion and motion blur. To mitigate the above issue, in this paper, we propose a quality-aware pattern diffusion (QPD) framework to boost the VOS performance. To achieve quality-aware pattern diffusion, a quality alignment mechanism is proposed, and it aims to promote the contributions of those normal samples while suppressing those abnormal ones during the feature propagation/diffusion processes. With our proposed quality alignment mechanism, the diffused instance features could be kept staying in the normal feature space, keeping from feature contamination caused by those low-quality samples. We first introduce a learnable quality evaluator to assess the sample qualities in both the temporal domain (i.e., across the historical frames), as well as the spatial domain (i.e., within the current frame). To achieve adaptive historical feature propagation into the current instance, a quality-aware long-term context propagation module is proposed, with which more stable instance representations could be achieved through the established quality-aware feature propagation process. A quality-aware pattern diffusion module is further introduced to address the spatial-domain abnormal samples, resulting in effective decoder feature refinement through building the quality-aware correspondence weights. Extensive experiments have demonstrated that our proposed quality alignment mechanism could boost the performance by a great margin over a strong baseline while achieving state-of-the-art performances on public VOS benchmarks.", "Keywords": "", "DOI": "10.1016/j.neucom.2023.01.044", "PubYear": 2023, "Volume": "528", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, PR China;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing, PR China;Corresponding authors"}], "References": [{"Title": "Contrastive predictive coding with transformer for video representation learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "482", "Issue": "", "Page": "154", "JournalTitle": "Neurocomputing"}, {"Title": "Motion cues guided feature aggregation and enhancement for video object segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "493", "Issue": "", "Page": "176", "JournalTitle": "Neurocomputing"}, {"Title": "Interaction augmented transformer with decoupled decoding for video captioning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "492", "Issue": "", "Page": "496", "JournalTitle": "Neurocomputing"}, {"Title": "Video object segmentation based on multi-level target models and feature integration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "492", "Issue": "", "Page": "396", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 106049368, "Title": "Hybrid bat–grasshopper and bat–modified multiverse optimization for solar photovoltaics maximum power generation", "Abstract": "A hybrid BAT with Grasshopper (GH) algorithm and BAT-MMVO (Modified Multiverse Optimization) are exhibited for harvesting maximum power from photovoltaics (PV) using the Xilinx System Generator (XSG) implanted controller. Using a hybrid BAT-GH and BAT-MMVO algorithm, the proposed implanted controller finds the best switching pulse for the boost converter. The implanted controller, switching schemes, and the Photovoltaic (PV) supported boost converter were built using the XSG domain. The hardware implementation of the best two cases were done using a microcontroller in a smaller scale. This aims to gather the maximum amount of power by a PV array for solar irradiation and cell temperature under varied environmental situations. The PV structure in the XSG domain is used to construct the system model for prediction. The major emphasis of this work is to keep the difference of actual power and reference power as minimum. Finally, the implanted controller&#x27;s performance is compared to that of other existing hybrid controllers. The performance of the proposed algorithm is found to yield good results in terms of power extraction. The theoretical and experimental results are presented. The computational efforts for the implementation of the algorithm are found to be less complex when compared to other existing methods.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2023.108596", "PubYear": 2023, "Volume": "106", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Vel Tech Rangarajan Dr. <PERSON><PERSON> R&D Institute of Science and Technology, Chennai, Tamil Nadu, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, Hindustan Institute of Technology and Science, Chennai, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering and Technology, Universidad Internacional de La Rioja, Logroño, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Vellore Institute of Technology, Vellore 632 014, Tamil Nadu, India"}], "References": [{"Title": "RETRACTED ARTICLE: Generation of maximum power in PV system using EHO based embedded controller", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "5", "Page": "5161", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A novel approach based on Grasshopper optimization algorithm for medical image fusion", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "171", "Issue": "", "Page": "114576", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106049457, "Title": "Software implementation of the enterprise protection system", "Abstract": "", "Keywords": "", "DOI": "10.34121/1028-9763-2022-4-62-67", "PubYear": 2022, "Volume": "4", "Issue": "", "JournalId": 69186, "JournalTitle": "Mathematical machines and systems", "ISSN": "1028-9763", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State University of Trade and Economics"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State University of Trade and Economics"}], "References": []}, {"ArticleId": *********, "Title": "Mixed multi-echelon location routing problem with differentiated intermediate depots", "Abstract": "With the increasing of E-commerce parcels in China in recent years, the logistics networks of many express companies are more and more unreasonable. Considering the distribution of parcels is regionally related, we describe a mixed multi-echelon (MME) urban logistics network to handle the regionally distributed parcels. The MME network contains limited urban distribution centers (UDCs), many differentiated intermediate depots (IDs), and a large number of terminals. This research constructs a mixed integer linear programming (MILP) to solve the mixed multi-echelon location routing problem (MME-LRP) where the location and the type of IDs are determined. A hybrid heuristic algorithm based on iterated local search and intensified by path relinking (HH-ILS-PR) is proposed to tackle the problem. The quality of our algorithm and the practicability of the MME network are verified by three sets of instances: two of them are adapted from benchmark instances of 2E-LRP and one is developed from real-world data. In the real-world instance, MME urban logistics network reduces the cost by 11.08% compared with two-echelon urban logistics network. We also present several managerial implications to express companies.", "Keywords": "", "DOI": "10.1016/j.cie.2023.109026", "PubYear": 2023, "Volume": "177", "Issue": "", "JournalId": 2751, "JournalTitle": "Computers & Industrial Engineering", "ISSN": "0360-8352", "EISSN": "1879-0550", "Authors": [{"AuthorId": 1, "Name": "Yun<PERSON> Chen", "Affiliation": "School of Economics and Business Administration, Chongqing University, 400030, PR China;College of Mechanical Engineering, Chongqing University, 400030, PR China;Chongqing Key Laboratory of Logistics, Chongqing University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics and Business Administration, Chongqing University, 400030, PR China;Chongqing Key Laboratory of Logistics, Chongqing University, Chongqing, China;Corresponding author at: School of Economics and Business Administration, Chongqing University, 400030, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Logistics and Maritime Studies, The Hong Kong Polytechnic University, 999077, Hong Kong Special Administrative Region;Chongqing Key Laboratory of Logistics, Chongqing University, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Economics and Business Administration, Chongqing University, 400030, PR China;Chongqing Key Laboratory of Logistics, Chongqing University, Chongqing, China"}], "References": []}, {"ArticleId": 106049507, "Title": "Survey data of public awareness on climate change and the value of marine and coastal ecosystems", "Abstract": "The long-term provision of ocean ecosystem services depends on healthy ecosystems and effective sustainable management. Understanding public opinion about marine and coastal ecosystems is important to guide decision-making and inform specific actions. However, available data on public perceptions on the interlinked effects of climate change, human impacts and the value and management of marine and coastal ecosystems are rare. This dataset presents raw data from an online, self-administered, public awareness survey conducted between November 2021 and February 2022 which yielded 709 responses from 42 countries. The survey was released in four languages (English, French, Spanish and Italian) and consisted of four main parts: (1) perceptions about climate change; (2) perceptions about the value of, and threats to, coasts, oceans and their wildlife, (3) perceptions about climate change response; and (4) socio-demographic information. Participation in the survey was voluntary and all respondents provided informed consent after reading a participant information form at the beginning of the survey. Responses were anonymous unless respondents chose to provide contact information. All identifying information has been removed from the dataset. The dataset can be used to conduct quantitative analyses, especially in the area of public perceptions of the interlinkages between climate change, human impacts and options for sustainable management in the context of marine and coastal ecosystems. The dataset is provided with this article, including a copy of the survey and participant information forms in all four languages, data and the corresponding codebook.", "Keywords": "Ecosystem services;Environmental perceptions;Human threats;Marine conservation;Nature-based solutions;Ocean management;Public opinion;Sustainable development", "DOI": "10.1016/j.dib.2023.108924", "PubYear": 2023, "Volume": "47", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "cE3c - Centre for Ecology, Evolution and Environmental Changes, Azorean Biodiversity Group, CHANGE - Global Change and Sustainability Institute, Faculty of Sciences and Technology, University of the Azores, Ponta Delgada 9500-321, Portugal. ;CICS.NOVA - Interdisciplinary Center of Social Sciences, Faculty of Social Sciences and Humanities (FCSH/NOVA), Lisboa 1070-312, Portugal."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Centre for Blue Governance, Faculty of Business and Law, University of Portsmouth, Richmond Building, Portland Street, Portsmouth PO1 3DE, UK."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Blue Governance, Faculty of Business and Law, University of Portsmouth, Richmond Building, Portland Street, Portsmouth PO1 3DE, UK. ;Geneva Science-Policy Interface, University of Geneva, Uni Mail, Boulevard du Pont-d'Arve 28, Geneva CH-1211, Switzerland."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Centre for Geocomputation, Maynooth University, Co. Kildare, Maynooth, Ireland."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Environment and Sustainability Institute, Faculty of Environment, Science and Economy, University of Exeter, Penryn Campus, Penryn TR10 9FE, UK."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Centre for Blue Governance, Faculty of Business and Law, University of Portsmouth, Richmond Building, Portland Street, Portsmouth PO1 3DE, UK."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Resource Management and Environmental Studies, University of the West Indies, Cave Hill Campus, Barbados."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Centre for Blue Governance, Faculty of Business and Law, University of Portsmouth, Richmond Building, Portland Street, Portsmouth PO1 3DE, UK."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Blue Governance, Faculty of Business and Law, University of Portsmouth, Richmond Building, Portland Street, Portsmouth PO1 3DE, UK."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Centro Euro-Mediterraneosui Cambiamenti Climatici and Università Ca' Foscari Venezia, CMCC@Ca'Foscari - Edificio Porta dell'Innovazione, 2nd floor - Via della Libertà, 12, Venice 30175, Italy."}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Ecology & Conservation, Faculty of Environment, Science and Economy, University of Exeter, Penryn Campus, Penryn TR10 9FE, UK."}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mediterranean Institute for Advanced Studies (IMEDEA CSIC-UIB), <PERSON><PERSON> <PERSON><PERSON> 21, Balearic Islands, Esporles 07190, Spain."}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": "Lund University Centre for Sustainability Studies, P.O. Box 170, Lund 221-00, Sweden."}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "National Centre for Geocomputation, Maynooth University, Co. Kildare, Maynooth, Ireland."}, {"AuthorId": 15, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institut océanographique Paul Ricard, Île des Embiez - 83140 Six-Fours-les-Plages, France."}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": "Department of Ecology & Conservation, Faculty of Environment, Science and Economy, University of Exeter, Penryn Campus, Penryn TR10 9FE, UK."}, {"AuthorId": 17, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Blue Governance, Faculty of Business and Law, University of Portsmouth, Richmond Building, Portland Street, Portsmouth PO1 3DE, UK."}, {"AuthorId": 18, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Ecology & Conservation, Faculty of Environment, Science and Economy, University of Exeter, Penryn Campus, Penryn TR10 9FE, UK. ;Department of Environment and Geography, University of York, York YO10 5NG, UK."}], "References": []}, {"ArticleId": 106049536, "Title": "Teacher or supervisor? Effective online knowledge distillation via guided collaborative learning", "Abstract": "Knowledge distillation is a widely-used and effective technique to boost the performance of a lightweight student network, by having it mimic the behavior of a more powerful teacher network. This paper presents an end-to-end online knowledge distillation strategy, in which several peer students are trained together and their predictions are aggregated into a powerful teacher ensemble via an effective ensembling technique that uses an online supervisor network to determine the optimal way of combining the student logits. Intuitively, this supervisor network learns the area of expertise of each student and assigns a weight to each student accordingly►it has knowledge of the input image, the ground truth data, and the predictions of each individual student, and tries to answer the following question: “how much can we rely on each student’s prediction, given the current input image with this ground truth class?”. The proposed technique can be thought of as an inference optimization mechanism as it improves the overall accuracy over the same number of parameters. The experiments we performed show that the proposed knowledge distillation consistently improves the performance of the knowledge-distilled students vs. the independently trained students.", "Keywords": "", "DOI": "10.1016/j.cviu.2023.103632", "PubYear": 2023, "Volume": "228", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Babes Bolyai University, Faculty of Mathematics and Computer Science, 1 Kogalniceanu, Cluj-Napoca, Romania"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Babes Bolyai University, Faculty of Mathematics and Computer Science, 1 Kogalniceanu, Cluj-Napoca, Romania"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Babes Bolyai University, Faculty of Mathematics and Computer Science, 1 Kogalniceanu, Cluj-Napoca, Romania"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Babes Bolyai University, Faculty of Mathematics and Computer Science, 1 Kogalniceanu, Cluj-Napoca, Romania;Corresponding author"}], "References": [{"Title": "Adversarial co-distillation learning for image recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107659", "JournalTitle": "Pattern Recognition"}, {"Title": "Multiple knowledge representation for big data artificial intelligence: framework, applications, and case studies", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "12", "Page": "1551", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}]}, {"ArticleId": 106049545, "Title": "Trustworthiness models to categorize and prioritize code for security improvement", "Abstract": "The exploitation of software security vulnerabilities can have severe consequences. Thus, it is crucial to devise new processes, techniques, and tools to support teams in the development of secure code from the early stages of the software development process, while potentially reducing costs and shortening the time to market. In this paper, we propose an approach that uses security evidences (e.g., software metrics, bad smells) to feed a set of trustworthiness models, which allow characterizing code from a security perspective. In practice, the goal is to identify the code units that are more prone to be vulnerable (i.e., are less trustworthy from a security perspective), thus helping developers to improve their code. A clustering-based approach is used to categorize the code units based on the combination of the scores provided by several trustworthiness models and taking into account the criticality of the code. To instantiate our proposal, we use a dataset of software metrics (e.g., CountLine, Cyclomatic Complexity, Coupling Between Objects) for files and functions of the Linux Kernel and Mozilla Firefox projects, and a set of machine learning algorithms (i.e., Random Forest, Decision Tree, SVM Linear, SVM Radial, and Xboost) to build the trustworthiness models. Results show that code that is more prone to be vulnerable can be effectively distinguished, thus demonstrating the applicability and usefulness of the proposed approach in diverse scenarios.", "Keywords": "", "DOI": "10.1016/j.jss.2023.111621", "PubYear": 2023, "Volume": "198", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CISUC, Department of Informatics Engineering, University of Coimbra, Portugal;Corresponding author"}, {"AuthorId": 2, "Name": "Naghmeh I<PERSON>ki", "Affiliation": "CISUC, Department of Informatics Engineering, University of Coimbra, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CISUC, Department of Informatics Engineering, University of Coimbra, Portugal;ISCAC, Polytechnic Institute of Coimbra, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CISUC, Department of Informatics Engineering, University of Coimbra, Portugal"}], "References": [{"Title": "Sensei: Enforcing secure coding guidelines in the integrated development environment", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "9", "Page": "1682", "JournalTitle": "Software: Practice and Experience"}]}, {"ArticleId": 106049547, "Title": "An efficient pattern-based approach for insider threat classification using the image-based feature representation", "Abstract": "Insider threats are expensive, difficult to detect, and sadly, on the rise. Despite significant research efforts, existing approaches are inadequate in accuracy and precision. They also suffer from a high false-positive rate in detecting insider attacks due to the heterogeneous nature of available insider threat data. Researchers have attempted an image-based approach for detecting insider threats to overcome the challenges from existing techniques. Most existing image-based approaches utilized Convolutional neural networks (CNN) to detect insider threats. Moreover, the CNN-based model lost the important user behavioral features due to the pooling operation. Also, they are often unsuitable for predictive modeling with features that lack spatial correlations. Aiming to address this issue, the Wavelet convolutional neural network (WCNN) is proposed. The WCNN model takes advantage of spectral and spatial analysis to classify insider threats using image-based feature representations. The proposed approach combines the scenario-specific single-day features from the user activity logs into a one-dimensional feature vector. It is then represented as images that reveal visual patterns effectively to identify malicious insiders using WCNN. In addition, the proposed approach adopts the SMOTEENN sampling technique to solve the class imbalance problem. The performance of the proposed approach is evaluated on the benchmark dataset. Experimental results show the improvement of the proposed approach over the current state-of-the-art techniques in terms of classification accuracy (97.19%), AUC (97.30%), and low false positives to identify malicious insiders.", "Keywords": "Insider threat ; Multi-perspective feature selection ; Class imbalance ; Image-based feature representation ; Wavelet CNN ; Insider threat classification", "DOI": "10.1016/j.jisa.2023.103434", "PubYear": 2023, "Volume": "73", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Tiruchirappalli, 620015, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Tiruchirappalli, 620015, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Tiruchirappalli, 620015, India"}], "References": [{"Title": "Insight Into Insiders and IT", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "IMCFN: Image-based malware classification using fine-tuned convolutional neural network architecture", "Authors": "Danish Vasan; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "107138", "JournalTitle": "Computer Networks"}, {"Title": "Insider Threat Risk Prediction based on Bayesian Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "101908", "JournalTitle": "Computers & Security"}, {"Title": "Formal approach to thwart against insider attacks: A bio-inspired auto-resilient policy regulation framework", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "412", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Deep learning for insider threat detection: Review, challenges and opportunities", "Authors": "<PERSON><PERSON>; Xi<PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "102221", "JournalTitle": "Computers & Security"}, {"Title": "LIO-IDS: Handling class imbalance using LSTM and improved one-vs-one technique in intrusion detection system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "192", "Issue": "", "Page": "108076", "JournalTitle": "Computer Networks"}, {"Title": "Effect of the Pixel Interpolation Method for Downsampling Medical Images on Deep Learning Accuracy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "9", "Issue": "11", "Page": "150", "JournalTitle": "Journal of Computer and Communications"}]}, {"ArticleId": 106049648, "Title": "Tolerance problems for generalized eigenvectors of interval fuzzy matrices", "Abstract": "Fuzzy algebra is a special type of algebraic structure in which classical addition and multiplication are replaced by maximum and minimum (denoted and , respectively). The eigenproblem is the search for a vector x (an eigenvector) and a constant λ (an eigenvalue) such that A x = λ x, where A is a given matrix. This paper investigates a generalization of the eigenproblem in fuzzy algebra. We solve the equation Ax = λBx with given matrices A, B and unknown constant λ and vector x. Generalized eigenvectors have interesting and useful properties in the various computational tasks with inexact (interval) matrix and vector inputs. This paper studies the properties of generalized interval eigenvectors of interval matrices. Three types of generalized interval eigenvectors: strongly tolerable generalized eigenvectors, tolerable generalized eigenvectors and weakly tolerable generalized eigenvectors are proposed and polynomial procedures for testing the obtained equivalent conditions are presented. © 2022 Institute of Information Theory and Automation of The Czech Academy of Sciences. All rights reserved.", "Keywords": "Fuzzy matrix; interval generalized eigenvector", "DOI": "10.14736/kyb-2022-5-0760", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 24195, "JournalTitle": "Kybernetika", "ISSN": "0023-5954", "EISSN": "1805-949X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Hradec Králové, Faculty of Informatics and Management, Rokitanského 62, Hradec Králové, 50003, Czech Republic"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technical University in Košice, Faculty of Electrical Engineering and Informatics, Nemcovej 32, Košice, 04200, Slovakia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Technical University in Košice, Faculty of Electrical Engineering and Informatics, Nemcovej 32, Košice, 04200, Slovakia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Hradec Králové, Faculty of Informatics and Management, Rokitanského 62, Hradec Králové, 50003, Czech Republic"}], "References": []}, {"ArticleId": 106049744, "Title": "Volitional control of upper-limb exoskeleton empowered by EMG sensors and machine learning computing", "Abstract": "Processing multiple channels of bioelectrical signals for bionic assistive robot volitional motion control is still a challenging task due to the interference of systematic noise, artifacts, individual bio-variability, and other factors. Emerging machine learning (ML) provides an enabling technology for the development of the next generation of smart devices and assistive systems and edging computing. However, the integration of ML into a robotic control system faces major challenges. This paper presents ML computing to process twelve channels of shoulder and upper limb myoelectrical signals for shoulder motion pattern recognition and real-time upper arm exoskeleton volitional control. Shoulder motion patterns included drinking, opening a door, abducting, and resting. ML algorithms included support vector machine (SVM), artificial neural network (ANN), and Logistic regression (LR). The accuracy of the three ML algorithms was evaluated respectively and compared to determine the optimal ML algorithm. Results showed that overall SVM algorithms yielded better accuracy than the LR and ANN algorithms. The offline accuracy was 96 ± 3.8% for SVM, 96 ± 3.8% for ANN, and 93 ± 6.3% for LR, while the online accuracy was 90 ± 9.1% for SVM, 86 ± 12.0% for ANN, and 85 ± 11.3% for LR respectively. The offline pattern recognition had a higher accuracy than the accuracy of real-time exoskeleton motion control. This study demonstrated that ML computing provides a reliable approach for shoulder motion pattern recognition and real-time exoskeleton volitional motion control.", "Keywords": "Exoskeleton ; EMG ; Sensor ; Wearable device ; Machine learning ; Volitional control ; Motion pattern recognition", "DOI": "10.1016/j.array.2023.100277", "PubYear": 2023, "Volume": "17", "Issue": "", "JournalId": 66362, "JournalTitle": "Array", "ISSN": "2590-0056", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Wayne State University, Detroit, MI, 48201, USA;State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, 200240, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, Wayne State University, Detroit, MI, 48201, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Department of Biomedical Engineering, Wayne State University, Detroit, MI, 48201, USA;Orthopaedic Surgery and Sports Medicine, Detroit Medical Center, Detroit, MI, 48201, USA;Corresponding author. Department of Biomedical Engineering, Wayne State University, Detroit, MI, 48201, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Orthopaedic Surgery and Sports Medicine, Detroit Medical Center, Detroit, MI, 48201, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, 200240, China;Corresponding author"}, {"AuthorId": 6, "Name": "<PERSON> Qi", "Affiliation": "State Key Laboratory of Mechanical System and Vibration, Shanghai Jiao Tong University, Shanghai, 200240, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Orthopaedic Surgery and Sports Medicine, Detroit Medical Center, Detroit, MI, 48201, USA"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Orthopaedic Surgery and Sports Medicine, Detroit Medical Center, Detroit, MI, 48201, USA"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "South Texas Health System – McAllen Department of Trauma, McAllen, TX, 78503, USA"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, Wayne State University, Detroit, MI, 48201, USA;Department of Electrical and Computer Engineering, University of Alabama, Tuscaloosa, AL, 35487, USA"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "South Texas Health System – McAllen Department of Trauma, McAllen, TX, 78503, USA"}], "References": [{"Title": "Attention Enhancement for Exoskeleton-Assisted Hand Rehabilitation Using Fingertip Haptic Stimulation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "144", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "A physiological and biomechanical investigation of three passive upper-extremity exoskeletons during simulated overhead work", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "1", "Page": "105", "JournalTitle": "Ergonomics"}, {"Title": "Effects of Upper-Limb Exoskeletons Designed for Use in the Working Environment—A Literature Review", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "", "Page": "82", "JournalTitle": "Frontiers in Robotics and AI"}]}, {"ArticleId": 106050140, "Title": "OSPEN: an open source platform for emulating neuromorphic hardware", "Abstract": "<p>This paper demonstrates a framework that entails a bottom-up approach to accelerate research, development, and verification of neuro-inspired sensing devices for real-life applications. Previous work in neuromorphic engineering mostly considered application-specific designs which is a strong limitation for researchers to develop novel applications and emulate the true behaviour of neuro-inspired systems. Hence to enable the fully parallel brain-like computations, this paper proposes a methodology where a spiking neuron model was emulated in software and electronic circuits were then implemented and characterized. The proposed approach offers a unique perspective whereby experimental measurements taken from a fabricated device allowing empirical models to be developed. This technique acts as a bridge between the theoretical and practical aspects of neuro-inspired devices. It is shown through software simulations and empirical modelling that the proposed technique is capable of replicating neural dynamics and post-synaptic potentials. Retrospectively, the proposed framework offers a first step towards open-source neuro-inspired hardware for a range of applications such as healthcare, applied machine learning and the internet of things (IoT).</p>", "Keywords": "Artificial intelligence chips;Chip design;Neural computing;Open-source hardware;Silicon neurons;Spike response model;Spiking neurons", "DOI": "10.11591/ijres.v12.i1.pp1-8", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 46991, "JournalTitle": "International Journal of Reconfigurable and Embedded Systems (IJRES)", "ISSN": "2089-4864", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "American University of Ras Al Khaimah"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University College London"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Ulster"}], "References": []}, {"ArticleId": 106050368, "Title": "Convolutional Neural Networks Inference Memory Optimization with Receptive Field-Based Input Tiling", "Abstract": "Currently, deep learning plays an indispensable role in many fields, including computer vision, natural language processing, and speech recognition. Convolutional Neural Networks (CNNs) have demonstrated excellent performance in computer vision tasks thanks to their powerful feature-extraction capability. However, as the larger models have shown higher accuracy, recent developments have led to state-of-the-art CNN models with increasing resource consumption. This paper investigates a conceptual approach to reduce the memory consumption of CNN inference. Our method consists of processing the input image in a sequence of carefully designed tiles within the lower subnetwork of the CNN, so as to minimize its peak memory consumption, while keeping the end-to-end computation unchanged. This method introduces a trade-off between memory consumption and computations, which is particularly suitable for high-resolution inputs. Our experimental results show that MobileNetV2 memory consumption can be reduced by up to 5.3 times with our proposed method. For ResNet50, one of the most commonly used CNN models in computer vision tasks, memory can be optimized by up to 2.3 times. © 2023 <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> and Y. <PERSON>.", "Keywords": "Convolutional neural network; memory optimization; receptive field", "DOI": "10.1561/116.00000015", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 10204, "JournalTitle": "APSIPA Transactions on Signal and Information Processing", "ISSN": "", "EISSN": "2048-7703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kobe University, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Kobe University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kobe University, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kobe University, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kobe University, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Kobe University, Japan"}], "References": []}, {"ArticleId": 106050515, "Title": "Identification of Potential Allergens of Atractylodes japonica and Addition of Panels for Allergic Diseases", "Abstract": "<p>The use of herbal medicines is increasing significantly worldwide. However, studies on side effects such as allergic reactions of herbal medicines are lacking. To provide an effective prescription, a diagnostic test to determine whether an allergic reaction is induced in the patient must be preceded. In this study, the purpose of this study is to investigate whether Atractylodes japonica extract, used as herbal medicine, binds to human serum IgE and induces an allergic reaction as an immune response complex, and to add a panel of allergens to the Allergy Q kit. First, using the Allergy-Q test kit, 253 sera samples were screened and 4 samples each from positive and negative serogroups were randomly selected. AJ extracts were separated by size through sodium dodecyl sulfate–polyacrylamide gel electrophoresis and stained with Coomassie blue dye. We then used Western blotting and in-gel digest to identify proteins that bind to human serum IgE, and predicted six proteins, including putative chitinases, proteasomes, and hypothetical proteins, using MASCOT program matching. Consequently, we demonstrated the potential of AJ as an allergen by confirming the sensitivity between AJ-derived proteins and human IgE.</p>", "Keywords": "Atractylodes japonica Koidz.; Food allergy; Allergy-Q; Herbal allergen; Diagnostic tests", "DOI": "10.1007/s13206-022-00094-9", "PubYear": 2023, "Volume": "17", "Issue": "1", "JournalId": 20968, "JournalTitle": "BioChip Journal", "ISSN": "1976-0280", "EISSN": "2092-7843", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Korean Medicine, Kyung Hee University, Seoul, Korea; Department of Science in Korean Medicine, Graduate School, Kyung Hee University, Seoul, Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Min Ko", "Affiliation": "College of Korean Medicine, Kyung Hee University, Seoul, Korea; Department of Science in Korean Medicine, Graduate School, Kyung Hee University, Seoul, Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Korean Medicine, Kyung Hee University, Seoul, Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Korean Medicine, Kyung Hee University, Seoul, Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Korean Medicine, Kyung Hee University, Seoul, Korea; Department of Science in Korean Medicine, Graduate School, Kyung Hee University, Seoul, Korea"}], "References": [{"Title": "Proteins Derived from Cnidium officinale Makino React with Serum IgE of Allergic Patients and Stimulate ERK/NF-kB Activation in Human Mast Cell Line HMC-1 Cells", "Authors": "Jiyoung Park; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "2", "Page": "135", "JournalTitle": "BioChip Journal"}]}, {"ArticleId": 106050528, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0743-7315(23)00010-2", "PubYear": 2023, "Volume": "174", "Issue": "", "JournalId": 1602, "JournalTitle": "Journal of Parallel and Distributed Computing", "ISSN": "0743-7315", "EISSN": "1096-0848", "Authors": [], "References": []}, {"ArticleId": 106050562, "Title": "Plant leaf disease detection using CNN with transfer learning and XGBoost", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJDATS.2022.128273", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 27737, "JournalTitle": "International Journal of Data Analysis Techniques and Strategies", "ISSN": "1755-8050", "EISSN": "1755-8069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 106050567, "Title": "Improved Hamming-space-based similarity search algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIIDS.2023.128275", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 10198, "JournalTitle": "International Journal of Intelligent Information and Database Systems", "ISSN": "1751-5858", "EISSN": "1751-5866", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Combining SLAM, GDM, and Anemotaxis for Gas Source Localization in Unknown and GPS-denied Environments", "Abstract": "This paper discusses an autonomous robot navigation system for the gas source localization problem in an unknown and GPS-denied environment to find a single gas source transported in turbulent wind flow using mobile robot olfaction (MRO). A new autonomous navigation system method is proposed by combining simultaneous localization and mapping (SLAM), gas distribution mapping (GDM), and anemotaxis. We developed two destination evaluation algorithms for the navigation system, frontier-multi criteria decision making (MCDM) and Anemotaxis-GDM, to drive the robot towards a gas source. The frontier-MCDM uses to select destination candidates for gas contaminant discovery. The Anemotaxis-GDM is used for gas source tracking when the robot's gas sensor detects gas contamination. The GSL process ends after the declaration of the gas source. The simulation results for three different environments with one gas source validate the effectiveness of the frontier-MCDM and anemotaxis-GDM algorithms. The simulation results show that the frontier-MCDM algorithm has a 60% success rate in finding gas contamination compared to the FFD-IG_CL strategy (13.3%) and frontier-IG_CL strategy (20%). The evaluation of anemotaxis-GDM algorithm simulation results based on the average accuracy index indicates that the robot's final pose is close to the gas source. The average simulation success ratio is 71.1%. © 2022, School of Electrical Engineering and Informatics. All rights reserved.", "Keywords": "anemotaxis; gas distribution mapping; Gas source localization; mobile robot olfaction; multicriteria decision-making; simultaneous localization and mapping", "DOI": "10.15676/ijeei.2022.14.3.3", "PubYear": 2022, "Volume": "14", "Issue": "3", "JournalId": 23441, "JournalTitle": "International Journal on Electrical Engineering and Informatics", "ISSN": "2085-6830", "EISSN": "2087-5886", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung,  Bandung 40132, Indonesia"}, {"AuthorId": 2, "Name": "Bambang R<PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung,  Bandung 40132, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung,  Bandung 40132, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung,  Bandung 40132, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Informatics, Institut Teknologi Bandung,  Bandung 40132, Indonesia"}], "References": []}, {"ArticleId": 106050610, "Title": "Methodology of Estimation and Analysis of the Number of Actuations of Relay Equipment of Railway Automation and Telemechanic", "Abstract": "", "Keywords": "", "DOI": "10.17587/it.29.51-56", "PubYear": 2023, "Volume": "29", "Issue": "1", "JournalId": 55442, "JournalTitle": "INFORMACIONNYE TEHNOLOGII", "ISSN": "1684-6400", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Sintez AT, Saint Petersburg, 199004, Russian Federation"}], "References": []}, {"ArticleId": 106050647, "Title": "Symbol Error Probability Optimization of OFDM Bidirectional AF Relaying Systems", "Abstract": "<p>This paper considers that two transceivers exchange orthogonal frequency division multiplexing (OFDM) signals with the aid of multiple bidirectional amplify and forward (AF) relays. Unlike most researchers who care about sum rate maximization issues to improve transmission efficiency, we formulate a symbol error probability (SEP) minimization problem under total power constraint because high data rate may degrade SEP performance. A joint algorithm of power allocation and relay selection is proposed to study their impact on the SEP performance. The closed-form solution of power allocation is not a form of water filling widely used in the sum rate maximum problem. Both analytical and simulation results clearly demonstrate the superiority of our algorithm.</p>", "Keywords": "amplify-and-forward;symbol error probability;power allocation", "DOI": "10.20965/jaciii.2023.p0096", "PubYear": 2023, "Volume": "27", "Issue": "1", "JournalId": 13600, "JournalTitle": "Journal of Advanced Computational Intelligence and Intelligent Informatics", "ISSN": "1343-0130", "EISSN": "1883-8014", "Authors": [{"AuthorId": 1, "Name": "Dong Qin", "Affiliation": "School of Information Engineering, Nanchang University, No.999 Xuefu Avenue, Honggutan District, Nanchang City, Jiangxi 330031, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Zhou", "Affiliation": "School of Information Engineering, East China Jiaotong University, No.808 Shuanggang Road, Nanchang, Jiangxi 330013, China"}], "References": []}, {"ArticleId": 106050888, "Title": "The dynamic relaxation form finding method aided with advanced recurrent neural network", "Abstract": "How to establish a self‐equilibrium configuration is vital for further kinematics and dynamics analyses of tensegrity mechanism. In this study, for investigating tensegrity form‐finding problems, a concise and efficient dynamic relaxation‐noise tolerant zeroing neural network (DR‐NTZNN) form‐finding algorithm is established through analysing the physical properties of tensegrity structures. In addition, the non‐linear constrained optimisation problem which transformed from the form‐finding problem is solved by a sequential quadratic programming algorithm. Moreover, the noise may produce in the form‐finding process that includes the round‐off errors which are brought by the approximate matrix and restart point calculating course, disturbance caused by external force and manufacturing error when constructing a tensegrity structure. Hence, for the purpose of suppressing the noise, a noise tolerant zeroing neural network is presented to solve the search direction, which can endow the anti‐noise capability to the form‐finding model and enhance the calculation capability. Besides, the dynamic relaxation method is contributed to seek the nodal coordinates rapidly when the search direction is acquired. The numerical results show the form‐finding model has a huge capability for high‐dimensional free form cable‐strut mechanisms with complicated topology. Eventually, comparing with other existing form‐finding methods, the contrast simulations reveal the excellent anti‐noise performance and calculation capacity of DR‐NTZNN form‐finding algorithm.", "Keywords": "", "DOI": "10.1049/cit2.12177", "PubYear": 2023, "Volume": "8", "Issue": "3", "JournalId": 26444, "JournalTitle": "CAAI Transactions on Intelligence Technology", "ISSN": "2468-6557", "EISSN": "2468-2322", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Electrical Engineering Changchun University of Technology  Changchun China"}, {"AuthorId": 2, "Name": "Zhongbo Sun", "Affiliation": "Department of Control Engineering Changchun University of Technology  Changchun China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Control Engineering Changchun University of Technology  Changchun China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic and Electrical Engineering The University of Sheffield  Sheffield England"}], "References": [{"Title": "Rolling Locomotion of Cable-Driven Soft Spherical Tensegrity Robots", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "3", "Page": "346", "JournalTitle": "Soft Robotics"}, {"Title": "A unifying framework for form-finding and topology-finding of tensegrity structures", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "247", "Issue": "", "Page": "106486", "JournalTitle": "Computers & Structures"}, {"Title": "Design and analysis of recurrent neural network models with non‐linear activation functions for solving time‐varying quadratic programming problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "6", "Issue": "4", "Page": "394", "JournalTitle": "CAAI Transactions on Intelligence Technology"}, {"Title": "Five-step discrete-time noise-tolerant zeroing neural network model for time-varying matrix inversion with application to manipulator motion generation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "", "Page": "104306", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Noise-suppressing zeroing neural network for online solving time-varying matrix square roots problems: A control-theoretic approach", "Authors": "<PERSON><PERSON><PERSON> Sun; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "116272", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Robustness of rank minimization heuristics for form-finding of tensegrity structures", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "266", "Issue": "", "Page": "106786", "JournalTitle": "Computers & Structures"}, {"Title": "Recursive recurrent neural network: A novel model for manipulator control with different levels of physical constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "3", "Page": "622", "JournalTitle": "CAAI Transactions on Intelligence Technology"}]}, {"ArticleId": 106051139, "Title": "Nonlinear Dynamic System Identification of ARX Model for Speech Signal Identification", "Abstract": "System Identification becomes very crucial in the field of nonlinear and dynamic systems or practical systems. As most practical systems don't have prior information about the system behaviour thus, mathematical modelling is required. The authors have proposed a stacked Bidirectional Long-Short Term Memory (Bi-LSTM) model to handle the problem of nonlinear dynamic system identification in this paper. The proposed model has the ability of faster learning and accurate modelling as it can be trained in both forward and backward directions. The main advantage of Bi-LSTM over other algorithms is that it processes inputs in two ways: one from the past to the future, and the other from the future to the past. In this proposed model a backward-running Long-Short Term Memory (LSTM) can store information from the future along with application of two hidden states together allows for storing information from the past and future at any moment in time. The proposed model is tested with a recorded speech signal to prove its superiority with the performance being evaluated through Mean Square Error (MSE) and Root Means Square Error (RMSE). The RMSE and MSE performances obtained by the proposed model are found to be 0.0218 and 0.0162 respectively for 500 Epochs. The comparison of results and further analysis illustrates that the proposed model achieves better performance over other models and can obtain higher prediction accuracy along with faster convergence speed. © 2023 Authors. All rights reserved.", "Keywords": "auto-regressive with exogenous; bidirectional-long-short term memory; long-short term memory; Nonlinear dynamic system identification", "DOI": "10.32604/csse.2023.029591", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of ECE, ITER, <PERSON><PERSON><PERSON> <PERSON><PERSON> (Deemed to be University), Bhubaneswar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of ECE, ITER, <PERSON><PERSON><PERSON> <PERSON><PERSON> (Deemed to be University), Bhubaneswar, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, Chitkara University Institute of Engineering & Technology, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, ITER, <PERSON><PERSON><PERSON> <PERSON><PERSON> (Deemed to be University), Bhubaneswar, India"}], "References": [{"Title": "Nonlinear black-box system identification through coevolutionary algorithms and radial basis function artificial neural networks", "Authors": "Helon <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "105990", "JournalTitle": "Applied Soft Computing"}, {"Title": "Nonlinear system identification using BBO-based multilayer perceptron network method", "Authors": "<PERSON>;  <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "4", "Page": "1497", "JournalTitle": "Microsystem Technologies"}, {"Title": "Deep Learning Bidirectional LSTM based Detection of Prolongation and Repetition in Stuttered Speech using Weighted MFCC", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "345", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Deep Learning and System Identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "1175", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Nonlinear MIMO System Identification with Echo-State Networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "3", "Page": "743", "JournalTitle": "Journal of Control, Automation and Electrical Systems"}]}, {"ArticleId": 106051140, "Title": "An Intelligent Adaptive Dynamic Algorithm for a Smart Traffic System", "Abstract": "Due to excessive car usage, pollution and traffic have increased. In urban cities in Saudi Arabia, such as Riyadh and Jeddah, drivers and air quality suffer from traffic congestion. Although the government has implemented numerous solutions to resolve this issue or reduce its effect on the environment and residents, it still exists and is getting worse. This paper proposes an intelligent, adaptive, practical, and feasible deep learning method for intelligent traffic control. It uses an Internet of Things (IoT) sensor, a camera, and a Convolutional Neural Network (CNN) tool to control traffic in real time. An image segmentation algorithm analyzes inputs from the cameras installed in designated areas. This study considered whether CNNs and IoT technologies could ensure smooth traffic flow in high-speed, high-congestion situations. The presented algorithm calculates traffic density and cars' speeds to determine which lane gets high priority first. A real case study has been conducted on MATLAB to verify and validate the results of this approach. This algorithm estimates the reduced average waiting time during the red light and the suggested time for the green and red lights. An assessment between some literature works and the presented algorithm is also provided. In contrast to traditional traffic management methods, this intelligent and adaptive algorithm reduces traffic congestion, automobile waiting times, and accidents. © 2023 CRL Publishing. All rights reserved.", "Keywords": "artificial intelligence; CNN; IoT; smart roads; Smart traffic control; traffic congestion", "DOI": "10.32604/csse.2023.035135", "PubYear": 2023, "Volume": "46", "Issue": "1", "JournalId": 83012, "JournalTitle": "Computer Systems Science and Engineering", "ISSN": "0267-6192", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electrical Engineering Department, College of Engineering, Northern Border University, Arar, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, College of Engineering, Northern Border University, Arar, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Faculty of Engineering at Rabigh, King Abdulaziz University, Jeddah, Saudi Arabia"}], "References": []}, {"ArticleId": 106051172, "Title": "How typical is your project? The need for a no-model approach for information management in AEC", "Abstract": "<p>This paper discusses the merit of using a no-model approach (no common product models or ontologies, etc.) for managing information in the AEC. It proposes an option for such an approach through the generation and analysis of semantic and social networks of communication between project stakeholders. The proposed approach advocates for a bottom-up discovery of knowledge constructs from stakeholder communication. Knowledge constructs are mini two-mode networks containing, on the one hand, clusters of concepts that appear frequently in the semantic networks of stakeholder communication; and, on the other hand, the social networks of stakeholders discussing these concepts. Using common models (such as IFC) has several limitations, including inflexibility to recognize and accommodate project contexts (which vary constantly), inability to timely capture the emergence of knowledge, and the scope creep problem (the ever-existing need to add more concepts to the common model from within and outside ACE domain). The no-model approach presented here is meant to complement and not replace the established model-based approach. This approach is built on the belief in the ontological agency of project stakeholders: knowledge is a social phenomenon that emerges through interactions between people. It advocates a shift from a top-down format where experts or standards clearinghouses tell (force) practitioners what should be true about their project. In every project, stakeholders customize (the structure of) established knowledge and adopt elements from emerging knowledge to address project-specific needs. They use the more superior intelligence (the human one) to innovate a ‘model of what they know’ to guide the management of the project in a manner specific to its context. By studying projects’ communication, we tell (inform) project stakeholders what knowledge constructs can be found in their communication. Unlike generic/static models, the resulting knowledge constructs are by default sensitive to project conditions. We should re-design our information management systems to be able to recognize and adaptively use the constructs established by project teams to facilitate their sharing of data (along with the established scheme, such as IFC). Relatedly used constructs can be nominated as AEC-wide prototype constructs, representing what we know about a typical project. At the initiation of a new project, these can be the starting scheme used by information and communication systems. As the project evolves and the project's own constructs are generated, the project-specific constructs should guide the flow of information. Contrasting project constructs against prototypes should inform the stakeholders of not only what is factual about their view/model of knowledge, but also how unique are they (from generic/base knowledge). This approach to no-model thinking is advantageous for several reasons. First, addressing the model rigidity problem. Because of the increasing complexity of projects, no single/standardized model can capture all contexts. Second, the increasing need for handling project unstructured data. The proposed approach helps formalize knowledge constructs from such data using network science. Third, recognizing and tracking the evolutionary nature of knowledge. Fourth, supporting innovation: instead of forcing knowers (people) to comply with a static model of reality, the new approach encourages them to imagine new possible futures/ worlds—after all, the true essence of digital twinning is to virtualize futures not just to digitize the present.</p>", "Keywords": "no-model approach;knowledge constructs;information models;interoperability;semantic networks;social networks;evolutionary systems", "DOI": "10.36680/j.itcon.2023.002", "PubYear": 2023, "Volume": "28", "Issue": "", "JournalId": 70075, "JournalTitle": "Journal of Information Technology in Construction", "ISSN": "", "EISSN": "1874-4753", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Combining ontology and probabilistic models for the design of bio-based product transformation processes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "117406", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 106051500, "Title": "Extremely boosted neural network for more accurate multi-stage Cyber attack prediction in cloud computing environment", "Abstract": "<p>There is an increase in cyberattacks directed at the network behind firewalls. An all-inclusive approach is proposed in this assessment to deal with the problem of identifying new, complicated threats and the appropriate countermeasures. In particular, zero-day attacks and multi-step assaults, which are made up of a number of different phases, some malicious and others benign, illustrate this problem well. In this paper, we propose a highly Boosted Neural Network to detect the multi-stageattack scenario. This paper demonstrated the results of executing various machine learning algorithms and proposed an enormously boosted neural network. The accuracy level achieved in the prediction of multi-stage cyber attacks is 94.09% (Quest Model), 97.29% (Bayesian Network), and 99.09% (Neural Network). The evaluation results of the Multi-Step Cyber-Attack Dataset (MSCAD) show that the proposed Extremely Boosted Neural Network can predict the multi-stage cyber attack with 99.72% accuracy. Such accurate prediction plays a vital role in managing cyber attacks in real-time communication.</p>", "Keywords": "Zero-day attack;Multi-stage cyber attack;Neural network;Quest;Bayesian network;Intrusion detection;Security Investigation", "DOI": "10.1186/s13677-022-00356-9", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 29812, "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications", "ISSN": "2192-113X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Amity University Haryana, Gurugram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Information and Computing Technology, College of Science and Engineering, Hamad Bin Khalifa University, Doha, Qatar"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, University of Louisiana, Louisiana, USA; Department of Computer Science and Engineering, Chandigarh University, Mohali, Punjab, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, B. M. Institute of Engineering & Technology, Sonipat, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Chandigarh University, Mohali, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing, University of Louisiana, Louisiana, USA; Department of Computer Science and Engineering, Chandigarh University, Mohali, Punjab, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Information and Computing Technology, College of Science and Engineering, Hamad Bin Khalifa University, Doha, Qatar"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information Sciences and Technology, Data Science and Artificial Intelligence Program, State College, Penn StateUniversity, USA; School of Optometry and Vision Science, Faculty of Science, University of Waterloo, 200 University, Waterloo, Canada; Faculty of Engineering, University of Waterloo, Waterloo, Canada"}], "References": [{"Title": "Security in product lifecycle of IoT devices: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "102779", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "RETRACTED ARTICLE: Diagnosis and combating COVID-19 using wearable Oura smart ring with deep learning methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "25", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "SPEAR SIEM: A Security Information and Event Management system for the Smart Grid", "Authors": "Panagi<PERSON><PERSON>-Grammat<PERSON>; <PERSON>agi<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "193", "Issue": "", "Page": "108008", "JournalTitle": "Computer Networks"}, {"Title": "Abnormal Event Correlation and Detection Based on Network Big Data Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "1", "Page": "695", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Stakeholder perspectives and requirements on cybersecurity in Europe", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "102916", "JournalTitle": "Journal of Information Security and Applications"}, {"Title": "Using data mining techniques to explore security issues in smart living environments in Twitter", "Authors": "<PERSON>; <PERSON>; <PERSON>-<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "179", "Issue": "", "Page": "285", "JournalTitle": "Computer Communications"}, {"Title": "AI-empowered, blockchain and SDN integrated security architecture for IoT network of cyber physical systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "274", "JournalTitle": "Computer Communications"}, {"Title": "The VALU3S ECSEL project: Verification and validation of automated systems safety and security", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "87", "Issue": "", "Page": "104349", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Towards an internet-scale overlay network for latency-aware decentralized workflows at the edge", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "108654", "JournalTitle": "Computer Networks"}, {"Title": "Security concerns on machine learning solutions for 6G networks in mmWave beam prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "", "Page": "101626", "JournalTitle": "Physical Communication"}, {"Title": "RETRACTED ARTICLE: An adaptive traffic routing approach toward load balancing and congestion control in Cloud–MANET ad hoc networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "11", "Page": "5377", "JournalTitle": "Soft Computing"}, {"Title": "Testing anticipatory systems: A systematic mapping study on the state of the art", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "111387", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Systematic survey of advanced metering infrastructure security: Vulnerabilities, attacks, countermeasures, and future vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "136", "Issue": "", "Page": "358", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "The big picture on the internet of things and the smart city: a review of what we know and what we need to know", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "", "Page": "100565", "JournalTitle": "Internet of Things"}, {"Title": "A novel trust-based security and privacy model for Internet of Vehicles using encryption and steganography", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "102", "Issue": "", "Page": "108205", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "The future of computing paradigms for medical and emergency applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "45", "Issue": "", "Page": "100494", "JournalTitle": "Computer Science Review"}, {"Title": "RETRACTED: SVM‐based generative adverserial networks for federated learning and edge computing attack model and outpoising", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "40", "Issue": "5", "Page": "e13072", "JournalTitle": "Expert Systems"}, {"Title": "Design of Intrusion Detection System based on Cyborg intelligence for security of Cloud Network Traffic of Smart Cities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}]}]