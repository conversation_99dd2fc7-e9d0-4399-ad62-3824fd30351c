[{"ArticleId": 115571187, "Title": "Sparse Multi-view Image Clustering with Complete Similarity Information", "Abstract": "Multi-view image clustering aims to efficiently divide the collection of images by studying the characteristics of different views. Many studies performed Laplacian dimensionality reduction on the original image to avoid noise interference in constructing the similarity matrix. However, they ignored the problem that local similarity information and isolated image information are lost due to dimensionality reduction. These problems will result in the similarity matrix being insufficient to accurately describe the similarity between images, which in turn affects the clustering accuracy. Therefore, we propose a sparse multi-view spectral clustering model with complete similarity information between images (SMSC-CSI). The model combines the original image space and the low-dimensional spectral embedding space based on the adaptive neighbors method to learn the initial similarity matrices of each view jointly. On the one hand, the original space can retain all the similarity information between images so that the initial similarity matrix can accurately describe the similarity between images, which is conducive to accurate clustering. On the other hand, the low-dimensional space can avoid noise interference and retain the main structure of high-dimensional data, improving the robustness of the initial similarity matrix. Meanwhile, to ensure consistency among the views, the model minimizes the difference between the initial similarity matrix and the central fusion matrix by alternately updating to obtain the optimal weights and central fusion matrix for each view. Finally, we can obtain ideal clustering results directly with low model complexity and without post-processing steps such as K-means by adding non-negative Laplace rank constraints and ℓ 0 \" role=\"presentation\" style=\"font-size: 90%; display: inline-block; position: relative;\"> ℓ 0 ℓ 0 -norm constraints to the model objective function. Experimental results on different real image datasets show that SMSC-CSI can outperform some traditional clustering models and recent models on multi-view image clustering.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127945", "PubYear": 2024, "Volume": "596", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Ministry of Education, Chongqing University of Posts and Telecommunications, Chongqing 400065, PR China;Correspondence to: Chongqing University of Posts and Telecommunications, Chongqing 400065, PR China; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Ministry of Education, Chongqing University of Posts and Telecommunications, Chongqing 400065, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Ministry of Education, Chongqing University of Posts and Telecommunications, Chongqing 400065, PR China"}, {"AuthorId": 4, "Name": "<PERSON>ghao Fu", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Ministry of Education, Chongqing University of Posts and Telecommunications, Chongqing 400065, PR China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Industrial Internet of Things and Networked Control, Ministry of Education, Chongqing University of Posts and Telecommunications, Chongqing 400065, PR China"}], "References": [{"Title": "Multi-view spectral clustering via sparse graph learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "384", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "Constrained bilinear factorization multi-view subspace clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105514", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Self-weighting multi-view spectral clustering based on nuclear norm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "124", "Issue": "", "Page": "108429", "JournalTitle": "Pattern Recognition"}, {"Title": "A new method to build the adaptive k-nearest neighbors similarity graph matrix for spectral clustering", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "493", "Issue": "", "Page": "191", "JournalTitle": "Neurocomputing"}, {"Title": "Consensus graph learning for auto-weighted multi-view projection clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "816", "JournalTitle": "Information Sciences"}, {"Title": "Robust dimensionality reduction method based on relaxed energy and structure preserving embedding for multiview clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "621", "Issue": "", "Page": "506", "JournalTitle": "Information Sciences"}, {"Title": "Consensus cluster structure guided multi-view unsupervised feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "271", "Issue": "", "Page": "110578", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 115571230, "Title": "Cross-Domain Collaborative Filtering: A Deep Neural Network Approach for Accurate and Diverse Recommendations", "Abstract": "The rapid proliferation of data and the intricate nature of user behavior in the online realm have presented new hurdles for recommendation systems, which aim to suggest pertinent items to users. Among the notable challenges, ensuring the provision of accurate recommendations across diverse domains stands out. This research paper proposes an innovative solution to tackle this challenge by developing a cross-domain recommendation system that leverages the collaborative filtering technique to generate precise and varied recommendations spanning multiple domains. The system gathers user behavior and item attribute data from various domains and employs a collaborative filtering algorithm integrated with a deep neural network. The model makes use of a Neural Network with CReLU activation along with the embeddings of the user and the item followed by concealed layers. The output layer employs tanh activation to guarantee that recommendations fall within [-1, 1]. Adam optimizer, MSE loss, and an accuracy metric are utilised in training. This architecture captures user-item interactions effectively, resulting in precise personalised recommendations resulting an accuracy of 96.5% - 97.5%. To validate the effectiveness of the model, extensive evaluations are conducted on real-world datasets encompassing movies and books. The results demonstrate that the proposed system outperforms state-of-the-art recommendation systems in terms of accuracy, as measured by precision(89%), recall(90%), and AUC(0.74) scores. Furthermore, the system exhibits robust performance even in scenarios characterized by cold-start problems and data sparsity.", "Keywords": "Optima; Mapping; Tree-structured Parzen Estimator Algorithm; Cosine Similarity", "DOI": "10.1016/j.procs.2024.04.321", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Information Technology Ranchi, 834010, Jharkhand, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Indian Institute of Information Technology Ranchi, 834010, Jharkhand, India"}], "References": [{"Title": "Evolution of recommender paradigm optimization over time", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "1047", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "DNN-MF: deep neural network matrix factorization approach for filtering information in multi-criteria recommender systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "13", "Page": "10807", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 115571296, "Title": "Analysis of BLER and throughput for 5G System", "Abstract": "5G is basically a new technology which had been already launched in the year 2020. These technologies have a huge development in the real world. Now 5G have been upgraded to 5G Advanced. The usage of 5G is increased than the previous 4G LTE. There will be increase in massive number of devices. The 5G devices can be utilized in buildings, pedestrian, vehicles etc. which means that the devices should be utilized in static as well as dynamic purpose. In case of vehicles, velocity is the important parameter. The analysis of throughput, BLER and throughput fraction can be done by varying the velocity. On the other hand, due to variation in velocity, there will be Doppler Effect which causes the shift in frequency. The main cause of Doppler Effect is due to the movement of vehicles. The shift in frequency creates the variation of carrier frequency instantly and affected the throughput and BLER. For ignoring the Doppler Effect, a counter balance must be inserted at the receiver by following the Doppler Effect which will able to keep carrier frequency constant and further there will be no effect in Doppler frequency due to variation in frequency. This scenario has been specified the evaluation of throughput and BLER during the movement of vehicles without Doppler effect. It had been estimated that after a certain stage, with the increase in velocity, the throughput is decreased whereas the BLER is increased. The way of communication between device and base station can be done both in uplink and downlink mode.", "Keywords": "4G LTE; LTE A; 5G NR; eMBB; URLLC; mMTC; BLER", "DOI": "10.1016/j.procs.2024.04.314", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Sikkim Manipal Instituite of Technology, Sikkim Manipal University, Majitar, Rangpo, East Sikkim, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sikkim Manipal Instituite of Technology, Sikkim Manipal University, Majitar, Rangpo, East Sikkim, India"}, {"AuthorId": 3, "Name": "Sanjib Sil", "Affiliation": "Calcutta Institute of Engineering and Management, Kolkata, West Bengal, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sikkim Manipal Instituite of Technology, Sikkim Manipal University, Majitar, Rangpo, East Sikkim, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aliah University, Kolkata, West Bengal, India"}], "References": [{"Title": "The impact of 5G on the evolution of intelligent automation and industry digitization", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "5977", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 115571300, "Title": "Modified Keypoint-Based Copy Move Area Detection", "Abstract": "Detecting and identifying manipulated portions within images poses a formidable challenge in research. Manipulated images, often created using image editing software such as Picasa or Photoshop, serve to obscure information and intentionally mislead viewers. Consequently, ensuring the authenticity of images becomes imperative prior to extracting meaningful data. One prevalent form of tampering is copy-move forgery, where objects are intentionally duplicated within an image using region of the same image. This study introduces a method for detecting copy-move forgery areas based on locating Scale-Invariant Feature Transform (SIFT) keypoints in images. The SIFT technique is employed for feature extraction, while feature descriptors are matched using brute force matching. Subsequently, a clustering algorithm is applied to group spatially proximate keypoints, enabling the detection of cloned regions. Specifically, Density-Based Spatial Clustering of Applications with Noise (DBSCAN) is utilized to identify forged areas within the image. To mitigate erroneous forgery detection and reduce false positives, outlier detection techniques are employed. Experimental evaluations are conducted on the MICC-F220 and MICC-F600 datasets, with comparisons drawn against previously established methodologies.", "Keywords": "Brut Force Matching; DBSCAN; Image Tampering; Outliers; SIFT", "DOI": "10.1016/j.procs.2024.04.319", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Karnataka State Akkamahadevi Women’s University Vijayapura, Karnataka-586108 India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Karnataka State Akkamahadevi Women’s University Vijayapura, Karnataka-586108 India"}, {"AuthorId": 3, "Name": " <PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Rani Channamma University, Belagavi, Karnataka-591156, India"}], "References": [{"Title": "An improved block based copy-move forgery detection technique", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "19-20", "Page": "13011", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel deep learning framework for copy-moveforgery detection in images", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19167", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A robust copy-move forgery detection technique based on discrete cosine transform and cellular automata", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "102510", "JournalTitle": "Journal of Information Security and Applications"}]}, {"ArticleId": 115571309, "Title": "Improving stability and adaptability of automotive electric steering systems based on a novel optimal integrated algorithm", "Abstract": "Purpose Design a novel optimal integrated control algorithm for the automotive electric steering system to improve the stability and adaptation of the system. Design/methodology/approach Simulation and calculation. Findings The output signals follow the reference signal with high accuracy. Originality/value The optimal integrated algorithm is established based on the combination of PID and SMC. The parameters of the PID controller are adjusted using a fuzzy algorithm. The optimal range of adjustment values is determined using a genetic algorithm.", "Keywords": "Electric steering system;FPIDSMC-GA;Intelligent optimal integrated control;Motor angle", "DOI": "10.1108/EC-10-2023-0675", "PubYear": 2024, "Volume": "41", "Issue": "4", "JournalId": 30507, "JournalTitle": "Engineering Computations", "ISSN": "0264-4401", "EISSN": "1758-7077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mechanical Engineering , Thuyloi University , Hanoi, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Science and Engineering , School of Computer Science , University of Hull , Hull, UK"}], "References": [{"Title": "Design of a robust LQG Compensator for an Electric Power Steering", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "6624", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 115571406, "Title": "Advanced Deep Learning Models for Accurate Retinal Disease State Detection", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijitcs.2024.03.06", "PubYear": 2024, "Volume": "16", "Issue": "3", "JournalId": 8584, "JournalTitle": "International Journal of Information Technology and Computer Science", "ISSN": "2074-9007", "EISSN": "2074-9015", "Authors": [{"AuthorId": 1, "Name": "Hossein<PERSON> <PERSON><PERSON>", "Affiliation": "Islamic Azad University, South Tehran Branch, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Behrouz. <PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Mostafa. <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115571432, "Title": "A Survey of Reversible Data Hiding in Encrypted Images", "Abstract": "The creation and application of multimedia has undergone a revolution in the last several years. This is a result of the rise in internet-based communications, which involves the exchange of digital data in the forms of text files, audio files, video files, and image files. For this reason, multimedia has emerged as a vital aspect of people’s everyday existence. Information security is crucial since there are several threats that target multime-dia integrity, confidentiality, and authentication.Multimedia data needs to be safeguarded, perhaps using encryption, in order to solve these numerous issues. Reversible data hiding in encrypted pictures (RDHEI) is investigate in this survey. (RDHEI) process, which functions by adding extra data to a picture, has surfaced. Employers and academics alike are becoming more interested in and focused on the RDHEI due to its vast range of applications. The purpose of this review is to introduce the various RDHEI schemes, identify the most important RDHEI techniques with varying embedding rates, and then examine the applications and future prospects of RDHEI. The main characteristics of each representative RDHEI Technique taken into consideration in this survey are enumerated in a comparison table.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505133", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115571482, "Title": "The Application of Interactive Design Technology in Digital Intelligent Exhibition Display", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2024.S28.296-308", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115571505, "Title": "Defending against Misinformation: Evaluating Transformer Architectures for Quick Misinformation Detection on Social Media", "Abstract": "Prior to the technological revolution, reliance on traditional sources such as newspapers and other mass communication channels was the predominant method of consuming news. With the pervasive influence of social media, traditional news sources have undergone significant displacement, yielding predominance to platforms rooted in social media networks. Informal news propagation through social media has been observed to be fast in nature. With the information propagating fast, the misinformation also propagates rapidly on social media. If the misinformation is not detected early, it can cause serious problems. Thus the problem of identifying misinformation at the onset needs much attention. This paper explores various architectures for detecting misinformation, employing deep transfer learning techniques. The study utilizes transformer architectures, namely MPNet, SentenceT5, and Generalizable T5-based dense Retrievers, to generate embeddings. These architectures have undergone pre-training and fine-tuning on diverse tasks. The system’s performance is assessed using a benchmark dataset of misinformation from Reddit, referred to as ‘Fakeddit’. When tested on unseen data, the models perform well, and outperform previously reported baselines and other models on the given dataset with textual modality. The results indicate that transfer learning with zero-shot or few-shot learning can prove very cost-effective and efficient for the problem of misinformation detection.", "Keywords": "Misinformation; Reddit; Transfer Learning; Deep Learning; Text Embeddings", "DOI": "10.1016/j.procs.2024.04.275", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Aligarh Muslim University, Aligarh 202002, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, Aligarh Muslim University, Aligarh 202002, India;Interdisciplinary Centre for Artificial Intelligence, Aligarh Muslim University, Aligarh 202002, India"}], "References": [{"Title": "Towards automatically filtering fake news in Portuguese", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Tiago A. <PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113199", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A systematic mapping on automatic classification of fake news in social media", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "1", "JournalTitle": "Social Network Analysis and Mining"}]}, {"ArticleId": 115571510, "Title": "The Impact of Various Factors on the Convolutional Neural Networks Model on Arabic Handwritten Character Recognition", "Abstract": "Recognizing Arabic handwritten characters (AHCR) poses a significant challenge due to the intricate and variable nature of the Arabic script. However, recent advancements in machine learning, particularly through Convolutional Neural Networks (CNNs), have demonstrated promising outcomes in accurately identifying and categorizing these characters. While numerous studies have explored languages like English and Chinese, the Arabic language still requires further research to enhance its compatibility with computer systems. This study investigates the impact of various factors on the CNN model for AHCR, including batch size, filter size, the number of blocks, and the number of convolutional layers within each block. A series of experiments were conducted to determine the optimal model configuration for the AHCD dataset. The most effective model was identified with the following parameters: <PERSON><PERSON> (BS) = 64, Number of Blocks (NB) = 3, Number of Convolution Layers in Block (NC) = 3, and <PERSON>lter <PERSON>ze (FS) = 64. This model achieved an impressive training accuracy of 98.29% and testing accuracy of 97.87%.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505125", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Alsayed", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115571530, "Title": "Optimizing network slicing in 6G networks through a hybrid deep learning strategy", "Abstract": "<p>The sixth generation (6G) networks demand high security, low latency, and highly dependable standards and capacity. One of the essential components of 6G networks is flexible wireless network slicing. In this paper, we propose a hybrid model that combines a convolutional neural network (CNN) and a bidirectional long short-term memory (BiLSTM). The hybrid model is applied to the Unicauca IP Flow Version2 dataset. The CNN handles the automated feature section, while the BiLSTM is utilized for categorizing the suitable network slices. This hybrid model is capable of offering a reliable and effective network slice to the end user. The proposed hybrid model has an overall recognition rate of 97.21%, which reflects the applicability of the proposed approach. A stratified 10-fold cross-validation is used to assess the applicability of the proposed model. The main challenge for network service providers is to assign slices correctly. A clever method is needed to make a standard for accurately assigning network slices to an unidentified device when it asks for them. For each incoming request for new traffic, the proposed model forecasts the suitable network slice</p>", "Keywords": "6G Network; Network slicing; Optimization; Deep learning; CNN; BiLSTM", "DOI": "10.1007/s11227-024-06238-y", "PubYear": 2024, "Volume": "80", "Issue": "14", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing Science and Engineering, VIT Bhopal University, Bhopal, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computing Science and Engineering, VIT Bhopal University, Bhopal, India"}], "References": [{"Title": "Optimal 5G network slicing using machine learning and deep learning concepts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "76", "Issue": "", "Page": "103518", "JournalTitle": "Computer Standards & Interfaces"}, {"Title": "Efficient and reliable hybrid deep learning-enabled model for congestion control in 5G/6G networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "182", "Issue": "", "Page": "31", "JournalTitle": "Computer Communications"}, {"Title": "Highly Accurate and Reliable Wireless Network Slicing in 5th Generation Networks: A Hybrid Deep Learning Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "30", "Issue": "2", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "ML-Based 5G Network Slicing Security: A Comprehensive Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "4", "Page": "116", "JournalTitle": "Future Internet"}]}, {"ArticleId": 115571586, "Title": "Optimizing Oil-Source Correlation Analysis Using Support Vector Machines and Sensory Attention Networks", "Abstract": "Oil source correlation can be used to identify the origin of crude oil by linking crude oil to source rocks; however, the manual methods, which are limited by the sample or parameter quantity or imbalanced datasets, are facing uncertainties. Although the existing multivariate statistical techniques can alleviate this problem, they are facing difficulties in processing imbalanced datasets and quantifying source beds. Therefore, a novel oil-source correlation analysis model called SVM-SelectKBest combining a support vector machine (SVM) with a feature selection algorithm to mitigate the common issue of dataset imbalance in oil-source correlations is proposed in this paper. The SVM-SelectKBest offers advantages over normal SVM by dynamically selecting the most relevant features and fine-tuning model parameters to achieve higher accuracy and better generalizability in complex datasets. SVM compensates for class imbalances by heavily penalizing the misclassification of the minority class, and SelectKBest streamlines the feature set to enhance SVM's effectiveness on critical variables. Furthermore, a shallow neural network (SensoryAttentionNet) is introduced into the proposed model to address the issue of quantifying the source bed proportions in crude oil. The result show that SVM-SelectKBest has better performance in identifying key geochemical parameters and discriminating oil source correlation, its accuracy in unbalanced datasets is improved by near 40% compared to SVM. The model obtains 25 key geochemical parameters such as C17 n-heptadecane, Pr pristane, and C18 n-octadecane, it achieves F1 scores of 1.0 on the training, validation, and test sets. SensoryAttentionNet also performs robustly, with a low variance of 0.05 between its predicted and actual values. All the results indicate the effectiveness of the proposed method in dealing with the imbalance problem in oil-source source correlation datasets and in determining the proportional contribution of source beds in crude oil.", "Keywords": "", "DOI": "10.1016/j.cageo.2024.105641", "PubYear": 2024, "Volume": "189", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Yangtze University, Jingzhou, 434020, Hubei, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Yangtze University, Jingzhou, 434020, Hubei, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, Yangtze University, Jingzhou, 434020, Hubei, China"}], "References": []}, {"ArticleId": 115571701, "Title": "Estimating Stock Market Prices with Histogram-based Gradient Boosting Regressor: A Case Study on Alphabet Inc", "Abstract": "One of the most important and common activities mentioned while discussing the financial markets is stock market trading. An investor is constantly searching for methods to estimate future trends to minimize losses and maximize profits due to the unavoidable volatility in stock prices. It is undeniable, nonetheless, that there is currently no mechanism for accurately estimating future market patterns despite numerous approaches being investigated to enhance model performance as much as feasible. Findings indicate notable improvements in accuracy compared to traditional Histogram-based gradient-boosting models. Experiments conducted on historical stock price datasets verify the efficacy of the proposed method. The combined strength of HGBoost and optimization techniques, including Particle Swarm Optimization, Slime Mold Algorithm, and Grey Wolf Optimization, not only increases prediction accuracy but also fortifies the model's ability to adjust to changing market conditions. The results for HGBoost, PSO- HGBoost, SMA- HGBoost, and GWO- HGBoost were 0.964, 0.973, 0.981, and 0.988, in that order. Compared to HGBoost, the result of GWO- HGBoost shows how combining with the optimizer can enhance the output of the given model.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150553", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Li", "Affiliation": ""}], "References": []}, {"ArticleId": 115571730, "Title": "The use of reinforcement learning algorithms in object tracking: A systematic literature review", "Abstract": "Object tracking is a computer vision task that aims to locate and continuously follow the movement of an object in video frames, given an initial annotation. Despite its importance, this task can prove to be challenging due to factors such as occlusion, deformations, and fast motion. Reinforcement Learning (RL) has been proposed as a viable solution for addressing these challenges by adapting to changes in object appearance and effectively handling occlusions, which can improve system performance. This study carries out a Systematic Literature Review on the use of Reinforcement Learning in object tracking between 2015 and 2023, by collecting and analyzing current trends, metrics, and benchmarks used in the field. Guidelines proposed by <PERSON><PERSON> were used to conduct the research, resulting in 75 studies being accepted based on their score on the quality scale attributed by the authors of this review. The studies were categorized to present the current state of research based on metadata, trends for publication, RL approach, RL algorithm, Deep Learning use, object tracking type, and camera control. Additionally, an analysis was performed on the evaluation process for system performance, focusing on benchmarks and metrics for Single Object Tracking, Multiple Object Tracking, and Active Object Tracking. This study addresses a gap by conducting a comprehensive Systematic Literature Review focusing exclusively on Reinforcement Learning for Object Tracking. The review offers researchers an updated, detailed, and objective scientific overview of the field that can be incorporated into future studies.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.127954", "PubYear": 2024, "Volume": "596", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON> R.", "Affiliation": "Escola Politécnica de Pernambuco, Universidade de Pernambuco, Recife, Brazil;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Escola Politécnica de Pernambuco, Universidade de Pernambuco, Recife, Brazil"}, {"AuthorId": 3, "Name": "<PERSON> J<PERSON>", "Affiliation": "Escola Politécnica de Pernambuco, Universidade de Pernambuco, Recife, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Sony, Brussels, Belgium"}], "References": [{"Title": "Three-step action search networks with deep Q-learning for real-time object tracking", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "107188", "JournalTitle": "Pattern Recognition"}, {"Title": "A Meta-Q-Learning Approach to Discriminative Correlation Filter based Visual Tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Intelligent & Robotic Systems"}, {"Title": "MOTChallenge: A Benchmark for Single-Camera Multiple Target Tracking", "Authors": "<PERSON>; Aljos̆a Os̆ep; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "4", "Page": "845", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Multiple object tracking: A literature review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "293", "Issue": "", "Page": "103448", "JournalTitle": "Artificial Intelligence"}, {"Title": "AEVRNet: Adaptive exploration network with variance reduced optimization for visual tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "449", "Issue": "", "Page": "48", "JournalTitle": "Neurocomputing"}, {"Title": "Enhancing continuous control of mobile robots for end-to-end visual active tracking", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "142", "Issue": "", "Page": "103799", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "Recent advances of single-object tracking methods: A brief survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "455", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}, {"Title": "I-VITAL: Information aided visual tracking with adversarial learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "77", "Issue": "", "Page": "102372", "JournalTitle": "Displays"}, {"Title": "A new approach for drone tracking with drone using Proximal Policy Optimization based distributed deep reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "", "Page": "101497", "JournalTitle": "SoftwareX"}]}, {"ArticleId": 115571731, "Title": "Computer-Aided Brand Design: Interactive Aesthetic Experience of Virtual Reality and Emotional Identification Model", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2024.S28.41-55", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115571766, "Title": "Edge AI Enabled IoT Framework for Secure Smart Home Infrastructure", "Abstract": "The synergy of Edge Computing, Artificial Intelligence, and Internet of Things in smart city camera-based surveillance offers notable advantages in resource allocation. Traditional approaches involving constant video data streaming to central servers incur significant bandwidth and storage costs. Our contribution involves proposing a cost-effective, lightweight Edge AI Enabled IoT Framework for Secure Smart Home Infrastructure, utilising a Raspberry Pi single-board computer and the open-source software motion for camera surveillance. The motion program monitors video signals from various cameras and triggers specific actions upon detecting movement. The framework efficiently notifies the smart home owner via email and smartphone message when motion is detected. We integrated four advanced motion detection and alert notification methodologies, conducting a thorough evaluation that positioned our framework as superior to existing solutions. Our research showcases impressive accuracy rates of 91% and 85% in indoor and outdoor scenarios, with minimal average delays of 12.8 seconds for email alerts and 1.6 seconds for messages which is approximately 41.6 % less than the state of the art methodologies. This innovative integration not only elevates surveillance capabilities but also establishes a swift and reliable alert system, contributing significantly to the efficiency and security of smart home environments.", "Keywords": "Edge AI; IoT framework; Smart home; surveillance; motion", "DOI": "10.1016/j.procs.2024.04.317", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, JUIT, Waknaghat, and Solan, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, JUIT, Waknaghat, and Solan, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics and Communication Engineering, JUIT, Waknaghat, and Solan, India"}], "References": [{"Title": "Home Security against Human Intrusion using Raspberry Pi", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "1811", "JournalTitle": "Procedia Computer Science"}, {"Title": "Edge Computing with Artificial Intelligence: A Machine Learning Perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "SCVS: On AI and Edge Clouds Enabled Privacy-preserved Smart-city Video Surveillance Services", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Internet of Things"}]}, {"ArticleId": 115571776, "Title": "Who wrote the first <i>Constitutions</i> of Freemasonry?", "Abstract": "<p>This article addresses the problematic authorship of The Constitutions of the Free-Masons (1723). Traditionally associated with <PERSON>, using stylometry, we examine whether and, if so, where <PERSON>, the prime mover of early English institutionalized Freemasonry, contributed to this publication. Our corpus includes writings by <PERSON>, <PERSON><PERSON><PERSON>, and two contemporary Freemasons used as distractors. The transcribed works contain texts from different genres and of varying lengths. In our methodology, we employ a wide range of robust, multivariate, unsupervised, and cross-validated supervised tests, verified through significance testing, which can hopefully contribute to the establishment of standards for historical authorship attribution. Our results suggest, in line with historical evidence, that the legendary history of the Constitutions was most likely primarily authored by <PERSON>. However, several of the Charges including the first one ‘Concerning God and religion’, one of the most disputed texts in the history of Freemasonry, are closer to the style of Desaguliers. The General Regulations concerning the organization of the lodges, hitherto attributed to <PERSON>, played a fundamental role in spreading Freemasonry worldwide. Our analyses show that the stylistic affinity of fifteen of the thirty-nine regulations has a pronounced closeness to <PERSON>’s style, five align more closely with Desaguliers’ style. The authorship of the rest remains inconclusive partly due to the insufficient length of texts by <PERSON>. These novel findings are also supported by a close reading of the Constitutions and other contemporary primary sources.</p>", "Keywords": "", "DOI": "10.1093/llc/fqae023", "PubYear": 2024, "Volume": "39", "Issue": "2", "JournalId": 2361, "JournalTitle": "Digital Scholarship in the Humanities", "ISSN": "2055-7671", "EISSN": "2055-768X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of English and American Studies, University of Szeged , Szeged, Hungary"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "English Language and Applied Linguistics, University of Birmingham, Birmingham , United Kingdom"}], "References": []}, {"ArticleId": 115571779, "Title": "HybridGCN: An Integrative Model for Scalable Recommender Systems with Knowledge Graph and Graph Neural Networks", "Abstract": "Graph Neural Networks (GNNs) have emerged as a state-of-the-art approach in building modern Recommender Systems (RS). By leveraging the complex relationships among items, users, and their attributes, which can be represented as a Knowledge Graph (KG), these models can explore implicit semantic sub-structures within graphs, thereby enhancing the learning of user and item representations. In this paper, we propose an end-to-end architectural framework for developing recommendation models based on GNNs and KGs, namely Hy-bridGCN. Our proposed methodologies aim to address three main challenges: (1) making graph-based RS scalable on large-scale datasets, (2) constructing domain-specific KGs from unstructured data sources, and (3) tackling the issue of incomplete knowledge in constructed KGs. To achieve these goals, we design a multi-stage integrated procedure, ranging from user segmentation and LLM-supported KG construction process to interconnectedly propagating between the KG and the Interaction Graph (IG). Our experimental results on a telecom e-commerce domain dataset demonstrate that our approach not only makes existing GNN-based recommender baselines feasible on large-scale data but also achieves comparative performance with the HybridGCN core.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505134", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115571842, "Title": "Decision Making Systems for Pneumonia Detection using Deep Learning on X-Ray Images", "Abstract": "This research paper investigates the application of Convolutional Neural Networks (CNNs) for the classification of pneumonia using chest X-ray images. Through rigorous experimentation and data analysis, the study demonstrates the model's impressive learning capabilities, achieving a notable accuracy of 96% in pneumonia classification. The consistent decrease in training and validation losses across 25 learning epochs underscores the model's adaptability and proficiency. However, the research also highlights the challenge of dataset imbalance and the need for improved model interpretability. These findings emphasize the potential of deep learning models in enhancing pneumonia diagnosis but also underscore the importance of addressing existing limitations. The study calls for future research to explore techniques for addressing dataset imbalances, enhance model interpretability, and extend the scope to address nuanced diagnostic challenges within the field of pneumonia classification. Ultimately, this research contributes to the advancement of medical image analysis and the potential for deep learning models to aid in early and accurate pneumonia diagnosis, thereby improving patient care and clinical outcomes.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150569", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115571862, "Title": "Bringing the field into the lab: a novel virtual reality outdoor march simulator for evaluating cognitive and physical performance", "Abstract": "<p>Soldiers, athletes, and rescue personnel must often maintain cognitive focus while performing intense, prolonged, and physically demanding activities. The simultaneous activation of cognitive and physical functions can disrupt their performance reciprocally. In the current study, we developed and demonstrated the feasibility of a virtual reality (VR)-based experimental protocol that enables rigorous exploration of the effects of prolonged physical and cognitive efforts. A battery of established neurocognitive tests was used to compare novel cognitive tasks to simulated loaded marches. We simulated a 10-km loaded march in our virtual reality environment, with or without integrated cognitive tasks (VR-COG). During three experimental visits, participants were evaluated pre- and post-activity, including the Color Trail Test (CTT), the Synthetic Work Environment (SYNWIN) battery for assessing multitasking, and physical tests (i.e., time to exhaustion). Results show that Strong or moderate correlations (r ≥ 0.58, p ≤ 0.05) were found between VR-COG scores and scores on the cognitive tests. Both the SYNWIN and CTT showed no condition effects but significant time effects, indicating better performance in the post-activity assessment than in the pre-activity assessment. This novel protocol can contribute to our understanding of physical-cognitive interactions, since virtual environments are ideal for studying high performance professional activity in realistic but controlled settings.</p>", "Keywords": "Physical effort; Cognitive load; Virtual reality; Military; Load carriage", "DOI": "10.1007/s10055-024-01013-z", "PubYear": 2024, "Volume": "28", "Issue": "2", "JournalId": 19337, "JournalTitle": "Virtual Reality", "ISSN": "1359-4338", "EISSN": "1434-9957", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The Center of Advanced Technologies in Rehabilitation, Sheba Medical Center, Tel Hashomer, Ramat Gan, Israel"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The Institute of Military Physiology, Israel Defense Forces, Medical Corps, Tel Hashomer, Ramat Gan, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Military Medicine, Faculty of Medicine, The Hebrew University of Jerusalem, Jerusalem, Israel; Department of Physical Education and Movement, Faculty of Sciences, Kibbutzim College of Education, Technology and the Arts, Tel Aviv, Israel"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The Center of Advanced Technologies in Rehabilitation, Sheba Medical Center, Tel Hashomer, Ramat Gan, Israel"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The Center of Advanced Technologies in Rehabilitation, Sheba Medical Center, Tel Hashomer, Ramat Gan, Israel"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "The Center of Advanced Technologies in Rehabilitation, Sheba Medical Center, Tel Hashomer, Ramat Gan, Israel"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Military Medicine, Faculty of Medicine, The Hebrew University of Jerusalem, Jerusalem, Israel; Department of Physical Education and Movement, Faculty of Sciences, Kibbutzim College of Education, Technology and the Arts, Tel Aviv, Israel"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "The Center of Advanced Technologies in Rehabilitation, Sheba Medical Center, Tel Hashomer, Ramat Gan, Israel; Department of Physiology and Pharmacology, Faculty of Medicine, Tel Aviv University, Tel Aviv, Israel; Sagol School of Neuroscience, Tel Aviv University, Tel Aviv, Israel; Corresponding author."}], "References": []}, {"ArticleId": 115571951, "Title": "CVE Severity Prediction From Vulnerability Description - A Deep Learning Approach", "Abstract": "The Common Vulnerabilities and Exposures (CVE) system is a widely used standard for identifying and tracking known vulnerabilities in software systems. The severity of these vulnerabilities must be determined in order to prioritize mitigation efforts. However, assigning severity to a vulnerability is a challenging task that requires careful analysis of its characteristics and potential impact. Considering the vast number of vulnerabilities identified every year, it is vital to automate the severity assignment, thereby reducing manual effort. This paper proposes a novel approach for predicting the severity of vulnerabilities based on their CVE description using GPT-2, a state-of-the-art language model. The CVSS severity values distribution imbalance is addressed using oversampling and contextual data augmentation techniques. This approach leverages the large-scale language modeling capabilities of GPT-2 to automatically extract relevant features from CVE descriptions and predict the severity level of the vulnerability. The model is evaluated on a test data set of 7,765 CVEs and achieves a high accuracy of 84.2% and an F<sub>1</sub> score of 0.82 in predicting the severity of the vulnerabilities on the test data. A comparative analysis of this approach was done against state-of-the-art methods, demonstrating the superior performance of the proposed approach. Based on the results, the proposed approach could be considered a valuable tool for quickly and accurately identifying high-severity vulnerabilities, facilitating more efficient and effective vulnerability management practices. Furthermore, this approach could be extended to other natural language processing tasks related to vulnerability analysis and management.", "Keywords": "Common Vulnerabilities; Exposures (CVE); National Vulnerability Database (NVD); Common Vulnerability Scoring System (CVSS); GPT-2 large language model; Natural Language Processing (NLP); Vulnerability Severity Prediction", "DOI": "10.1016/j.procs.2024.04.294", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Amrita School of Computing, Amrita Vishwa Vidyapeetham, Amritapuri, India;ICU Medical, Cybersecurity, Device R&D, Chennai, 600006"}, {"AuthorId": 2, "Name": "<PERSON>than <PERSON>", "Affiliation": "ICU Medical, Cybersecurity, Device R&D, Chennai, 600006"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Amrita School of Computing, Amrita Vishwa Vidyapeetham, Amritapuri, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Vivek S", "Affiliation": "ICU Medical, Cybersecurity, Device R&D, Chennai, 600006"}], "References": [{"Title": "Apply transfer learning to cybersecurity: Predicting exploitability of vulnerabilities by description", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "210", "Issue": "", "Page": "106529", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Common vulnerability scoring system prediction based on open source intelligence information sources", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "", "Page": "103286", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 115571991, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0952-1976(24)00892-3", "PubYear": 2024, "Volume": "133", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [], "References": []}, {"ArticleId": 115572018, "Title": "Reactive bipedal balance: Coordinating compliance and stepping through virtual model imitation for enhanced stability", "Abstract": "Compliance and step location adjustments are both important for robots to maintain balance when encountering external disturbances. Compared with controlling separately, unifying compliance with step location adjustments and integrating them into real-time pattern planning gets obvious advantages. However, the controllers’ models differ significantly from each other and from the dynamics of real position-controlled robots, compromising the stabilizing performance. In this paper, we propose a Virtual Model Imitation (VMI) strategy that unifies the controllers’ models and aligns them with actual robot dynamics. Based on this, a reactive pattern planning method is proposed which simultaneously coordinates compliance and step location adjusting strategies. The approach involves emulating the passive dynamic motion of a Linear Inverted Pendulum Model (LIPM) to enable the robot to respond to external disturbances with compliant motions automatically. Additionally, Zero Moment Point Intervened (ZI) estimation is employed to precisely determine the Center of Mass (CoM) state. Real-time optimization of step location is then conducted to stabilize the estimated CoM, actively responding to external disturbances. Importantly, the decision to adjust step location is quantitatively defined. To enhance efficiency, explicit solutions for both ZI Estimation and Step Location Optimization are provided to reduce computational costs. To validate our approach, we conducted simulations and experiments using the biped robot BHR-T, subjecting it to unexpected external pushes and obstacles during walking.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.124334", "PubYear": 2024, "Volume": "254", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "Chencheng Dong", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing, 100081, China;Key Laboratory of Biomimetic Robots and Systems, Ministry of Education, Beijing, 100081, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing, 100081, China;Key Laboratory of Biomimetic Robots and Systems, Ministry of Education, Beijing, 100081, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 5, "Name": "Qingqing Li", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechatronical Engineering, Beijing Institute of Technology, Beijing, 100081, China"}], "References": [{"Title": "Foot-guided control of a biped robot through ZMP manipulation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "21-22", "Page": "1472", "JournalTitle": "Advanced Robotics"}, {"Title": "Global footstep planning with greedy and heuristic optimization guided by velocity for biped robot", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121798", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 115572026, "Title": "IDD10-NAC079 transcription factor complex regulates sheath blight resistance by inhibiting ethylene signaling in rice", "Abstract": "<p><b>INTRODUCTION</b>:Rhizoctonia so<PERSON> is a pathogen causing rice sheath blight (ShB). Ammonium transporter 1 (AMT1) promotes resistance of rice to ShB by activating ethylene signaling. However, how AMT1 activates ethylene signaling remains unclear.</p><p><b>OBJECTIVE</b>:In this study, the indeterminate domain 10 (IDD10)-NAC079 interaction model was used to investigate whether ethylene signaling is modulated downstream of ammonium signaling and modulates ammonium-mediated ShB resistance.</p><p><b>METHODS</b>:RT-qPCR assay was used to identify the relative expression levels of nitrogen and ethylene related genes. Yeast two-hybrid assays, Bimolecular fluorescence complementation (BiFC) and Co-immunoprecipitation (Co-IP) assay were conducted to verify the IDD10-NAC079-calcineurin B-like interacting protein kinase 31 (CIPK31) transcriptional complex. Yeast one-hybrid assay, Chromatin immunoprecipitation (ChIP) assay, and Electrophoretic mobility shift assay (EMSA) were used to verify whether ETR2 was activated by IDD10 and NAC079. Ethylene quantification assay was used to verify ethylene content in IDD10 transgenic plants. Genetic analysis is used to detect the response of IDD10, NAC079 and CIPK31 to ShB infestation.</p><p><b>RESULTS</b>:IDD10-NAC079 forms a transcription complex that activates ETR2 to inhibit the ethylene signaling pathway to negatively regulating ShB resistance. CIPK31 interacts and phosphorylates NAC079 to enhance its transcriptional activation activity. In addition, AMT1-mediated ammonium absorption and subsequent N assimilation inhibit the expression of IDD10 and CIPK31 to activate the ethylene signaling pathway, which positively regulates ShB resistance.</p><p><b>CONCLUSION</b>:The study identified the link between ammonium and ethylene signaling and improved the understanding of the rice resistance mechanism.</p><p>Copyright © 2024. Production and hosting by Elsevier B.V.</p>", "Keywords": "AMT1;CIPK31;IDD10;NAC079;Resistance;Rice;Sheath blight", "DOI": "10.1016/j.jare.2024.05.032", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Graduate School of Green-Bio Science and Crop Biotech Institute, Kyung Hee University, Yongin 17104, Republic of Korea."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China."}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "National Pesticide Engineering Research Center (Tianjin), College of Chemistry, Nankai University, Tianjin 300071, China."}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "College of Plant Protection, Shenyang Agricultural University, Shenyang 110866, China; State Key Laboratory of Elemento-Organic Chemistry and Department of Plant Protection, National Pesticide Engineering Research Center (Tianjin), Nankai University, Tianjin 300071, China"}], "References": [{"Title": "Rhizoctonia solani transcriptional activator interacts with rice WRKY53 and grassy tiller 1 to activate SWEET transporters for nutrition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "50", "Issue": "", "Page": "1", "JournalTitle": "Journal of Advanced Research"}]}, {"ArticleId": 115572032, "Title": "Exploring Cutting-Edge Developments in Deep Learning for Biomedical Signal Processing", "Abstract": "Biomedical condition monitoring devices are progressing quickly by incorporating cost-effective and non-invasive sensors to track vital signs, record medical circumstances, and deliver meaningful responses. These sophisticated innovations rely on breakthrough technology to provide intelligent platforms for health monitoring, quick illness recognition, and precise treatment. Biomedical signal processing determines patterns of signals and serves as the backbone for reliable applications, medical diagnostics, and research. Deep Learning (DL) methods have brought significant innovation in biomedical signal processing, leading to the transformation of the health sector and medical diagnostics. This article covers an entire range of technical innovations evolved for DL-based biomedical signal processing where different modalities have been considered, including Electrocardiography (ECG), Electromyography (EMG), and Electroencephalography (EEG). A vast amount of biomedical data in various forms is available, and DL concepts are required to extract and model this data in order to identify hidden complex patterns that can be utilized to improve the diagnosis, prognosis, and personalized treatment of diseases in an individual. The nature of this developing topic certainly gives rise to a number of challenges. First, the application of sensitive and noisy time series data requires truly robust models. Second, many inferences made at the bedside must have interpretability by design. Third, the field will require that processing be performed in real-time if used for therapeutic interventions. We systematically evaluate these challenges and highlight areas where continued research is needed. The general expansion of DL technologies into the biomedical domain gives rise to novel concerns about accountability and transparency of algorithmic decision-making, a subject which we briefly touch upon as well.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.********", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "A multimodal stepwise-coordinating framework for pedestrian trajectory prediction", "Abstract": "Pedestrian trajectory prediction from the first-person view has still been considered one of the challenging problems in automatic driving due to the difficulty of understanding and predicting pedestrian actions. Observing that pedestrian motion naturally contains the repetitive pattern of the gait cycle and global intention information, we design a Multimodal Stepwise-Coordinating Network, namely MSCN, to sufficiently leverage the underlying human motion properties. Specifically, we first design a multimodal spatial-frequency encoder, which encodes the periodicity of pedestrian motion with a frequency-domain enhanced Transformer and other visual information with a spatial-domain Transformer. Then, we propose a stepwise-coordinating decoder structure, which leverages both local and global information in sequence decoding through a two-stage decoding process. After generating a coarse sequence from the stepwise trajectory predictor, we design a coordinator to aggregate the corresponding representations used to generate the coarse sequence. Subsequently, the coordinator learns to output a refined sequence through a knowledge distillation process based on the aggregated representations. In this way, MSCN can adequately capture the representations of short-term motion behaviors, thus modeling better long-term sequence prediction. Extensive experiments show that the proposed model can achieve significant improvements over state-of-the-art approaches on the PIE and JAAD datasets by 16.1% and 16.4% respectively.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112038", "PubYear": 2024, "Volume": "299", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Microsoft Research Asia, Beijing, 100080, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Electronic Engineering, Hunan University, Changsha, 410082, China;Corresponding author"}], "References": [{"Title": "Knowledge Distillation: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "6", "Page": "1789", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Bayesian Spatio-Temporal grAph tRansformer network (B-STAR) for multi-aircraft trajectory prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "249", "Issue": "", "Page": "108998", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Continual learning-based trajectory prediction with memory augmented networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "258", "Issue": "", "Page": "110022", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "SPU-BERT: Faster human multi-trajectory prediction from socio-physical understanding of BERT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "110637", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Static-dynamic global graph representation for pedestrian trajectory prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "277", "Issue": "", "Page": "110775", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A multi-modal vehicle trajectory prediction framework via conditional diffusion model: A coarse-to-fine approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "110990", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 115572035, "Title": "A data tamper-proof method of cloud computing platform based on blockchain", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.138786", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572056, "Title": "Image Generation of Animation Drawing Robot Based on Knowledge Distillation and Semantic Constraints", "Abstract": "With the development of robot technology, animation drawing robots have gradually appeared in the public eye. Animation drawing robots can generate many types of images, but there are also problems such as poor quality of generated images and long image drawing time. In order to improve the quality of images generated by animation drawing robots, an animation face line drawing generation algorithm based on knowledge distillation was designed to reduce computational complexity through knowledge distillation. To further raise the quality of images generated by robots, the research also designed an unsupervised facial caricature image generation algorithm based on semantic constraints, which uses facial semantic labels to constrain the facial structure of the generated images. The outcomes denote that the max values of the peak signal-to-noise ratio and feature similarity index measurements of the line drawing generation model are 39.45 and 0.7660 respectively, and the mini values are 37.51 and 0.7483 respectively. The average values of the gradient magnitude similarity bias and structural similarity of the loss function used in this model are 0.2041 and 0.8669 respectively. The max and mini values of Fréchet Inception Distance of the face caricature image generation model are 81.60 and 71.32 respectively, and the max and mini time-consuming values are 15.21s and 13.24s respectively. Both the line drawing generation model and the face caricature image generation model have good performance and can provide technical support for the image generation of animation drawing robots.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505122", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572068, "Title": "Autoencoder evolutionary algorithm for large-scale multi-objective optimization problem", "Abstract": "<p>Multi-objective optimization problems characterized by a substantial number of decision variables, which are also called large-scale multi-objective optimization problems (LSMOPs), are becoming increasingly prevalent. Traditional evolutionary algorithms may deteriorate drastically when tackling a large number of decision variables. For LSMOPs, the dimensionality of the decision variables needs to be reduced and the algorithm needs to be designed according to the characteristics of divide-and-conquer. The autoencoder evolutionary algorithm (AEEA) is proposed based on autoencoder dimensionality reduction, the grouping of decision variables, and the application of divide-and-conquer strategies. The proposed algorithm is compared with other classical algorithms. The experiment result shows that AEEA achieves excellent convergence and diversity, and still performs well in decision variables of higher dimensions. Finally, it is verified that the autoencoder improves the running time of the proposed algorithm.</p>", "Keywords": "Large-scale; Multi-objective optimization; Evolutionary algorithms; Dimensionality reduction", "DOI": "10.1007/s13042-024-02221-4", "PubYear": 2024, "Volume": "15", "Issue": "11", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinghuangdao, People’s Republic of China; Key Lab of Industrial Computer Control Engineering of Hebei Province, Yanshan University, Qinghuangdao, People’s Republic of China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinghuangdao, People’s Republic of China; Key Lab of Industrial Computer Control Engineering of Hebei Province, Yanshan University, Qinghuangdao, People’s Republic of China"}, {"AuthorId": 3, "Name": "Hao Sun", "Affiliation": "School of Electrical Engineering, Yanshan University, Qinghuangdao, People’s Republic of China; Key Lab of Industrial Computer Control Engineering of Hebei Province, Yanshan University, Qinghuangdao, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Tianjin Research Institute of Electric Science, Tianjin, People’s Republic of China"}], "References": [{"Title": "Solving large-scale many-objective optimization problems by covariance matrix adaptation evolution strategy with scalable small subpopulations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "457", "JournalTitle": "Information Sciences"}, {"Title": "Applying graph-based differential grouping for multiobjective large-scale optimization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "100626", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Solve large‐scale many‐objective optimization problems based on dual analysis of objective space and decision space", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "", "Page": "101045", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A fast sampling based evolutionary algorithm for million-dimensional multiobjective optimization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101181", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 115572080, "Title": "Approximate and exact controllability criteria for linear one-dimensional hyperbolic systems", "Abstract": "This paper deals with the controllability of linear one-dimensional hyperbolic systems. Reformulating the problem in terms of linear difference equations and making use of infinite-dimensional realization theory, we obtain both necessary and sufficient conditions for approximate and exact controllability, expressed in the frequency domain. The results are applied to flows in networks.", "Keywords": "", "DOI": "10.1016/j.sysconle.2024.105834", "PubYear": 2024, "Volume": "190", "Issue": "", "JournalId": 6628, "JournalTitle": "Systems & Control Letters", "ISSN": "0167-6911", "EISSN": "1872-7956", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, CNRS, CentraleSupélec, Laboratoire des signaux et systèmes, Gif-sur-Yvette, 91190, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Tel Aviv University, Ramat Aviv, Tel Aviv, 69978, Israel;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université Paris-Saclay, CNRS, CentraleSupélec, Inria, Laboratoire des signaux et systèmes, Gif-sur-Yvette, 91190, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Sorbonne Université, Inria, CNRS, Laboratoire Jacques-Louis Lions, Paris, 75005, France"}], "References": [{"Title": "Null-controllability of linear hyperbolic systems in one dimensional space", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "104851", "JournalTitle": "Systems & Control Letters"}]}, {"ArticleId": 115572142, "Title": "Cooperative MAC Protocol based on Best Data Rate (CMAC-DR)", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijcnis.2024.03.03", "PubYear": 2024, "Volume": "16", "Issue": "3", "JournalId": 20260, "JournalTitle": "International Journal of Computer Network and Information Security", "ISSN": "2074-9090", "EISSN": "2074-9104", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "VTU, Belgavi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572169, "Title": "A knowledge sharing method for virtual academic community based on social network analysis", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2023.138841", "PubYear": 2023, "Volume": "73", "Issue": "4", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572171, "Title": "Self-supervised monocular depth estimation with self-distillation and dense skip connection", "Abstract": "Monocular depth estimation (MDE) is crucial in a wide range of applications, including robotics, autonomous driving and virtual reality. Self-supervised monocular depth estimation has emerged as a promising MDE approach without requiring hard-to-obtain depth labels during training, and multi-scale photometric loss is widely used for self-supervised monocular depth estimation as the self-supervised signal. However, multi-photometric loss is a weak training signal and might disturb the good intermediate features representation. In this paper, we propose a successive depth map self-distillation(SDM-SD) loss, which combines with the single-scale photometric loss to replace the multi-scale photometric loss. Moreover, considering that multi-stage feature representations are essential for dense prediction tasks such as depth estimation, we also propose a dense skip connection, which can efficiently fuse the intermediate features of the encoder and fully utilize them in each stage of the decoder in our encoder–decoder architecture. By applying successive depth map self-distillation loss and dense skip connection, our proposed method can achieve state-of-the-art performance on the KITTI benchmark, and exhibit the best generalization ability on the challenging indoor dataset NYUv2 dataset.", "Keywords": "", "DOI": "10.1016/j.cviu.2024.104048", "PubYear": 2024, "Volume": "246", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Harbin Engineering University, Harbin 150001, China;Key Laboratory of Advanced Marine Communication and Information Technology, Ministry of Industry and Information Technology, Harbin 150001, China;Corresponding author at: School of Information and Communication Engineering, Harbin Engineering University, Harbin 150001, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Engineering, Harbin Engineering University, Harbin 150001, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Engineering, Harbin Engineering University, Harbin 150001, China"}, {"AuthorId": 4, "Name": "Abdulmotaleb El Saddik", "Affiliation": "School of Electrical Engineering and Computer Science, University of Ottawa, Ottawa, ON K1N 6N5, Canada"}], "References": [{"Title": "Consistent video depth estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 115572176, "Title": "Received signal strength-based power map generation in a 2-D obstructed wireless sensor network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.138777", "PubYear": 2024, "Volume": "24", "Issue": "4", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572221, "Title": "Application of Graph Neural Network and Virtual Reality Based on the Concept of Sustainable Design", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2024.S28.15-27", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572257, "Title": "New 3D Shape Descriptor Extraction using CatBoost Classifier for Accurate 3D Model Retrieval", "Abstract": "Given the wide application of 3D model analysis, covering domains such as medicine, engineering, and virtual reality, the demand for innovative content-based 3D shape retrieval systems capable of handling complex 3D data efficiently have significantly increased. This paper proposes a new 3D shape retrieval method that uses the CatBoost classifier, a machine learning algorithm, to capture a unique descriptor for each 3D mesh. The main idea of our method is to get a specific and a unique signature or descriptor for each 3D model by training the CatBoost classifier with features obtained directly from the 3D models. This idea not only accelerates the training process, but also ensures the consistency and relevance of the data fed to the classifier during the training process. Once fully trained, the classifier generates a descriptor that is used during the indexing and retrieval process. The efficiency of our method is demonstrated by conducting extensive experiments on the Princeton shape benchmark database. The results demonstrate high retrieval accuracy in comparison to various existing methods in the literature. Our method's ability to outperform these methods shows its potential as highly useful tool in the field of content-based 3D shape retrieval.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505107", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "Mohcine BOUKSIM", "Affiliation": ""}, {"AuthorId": 2, "Name": "Fatima RAFII ZAKANI", "Affiliation": ""}, {"AuthorId": 3, "Name": "Khadija ARHID", "Affiliation": ""}, {"AuthorId": 4, "Name": "Azzeddine DAHBI", "Affiliation": ""}, {"AuthorId": 5, "Name": "Taoufiq GADI", "Affiliation": ""}, {"AuthorId": 6, "Name": "Mohamed ABOULFATAH", "Affiliation": ""}], "References": []}, {"ArticleId": 115572292, "Title": "Balanced allocation of teaching information resources based on discrete particle swarm optimisation algorithm", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2023.138840", "PubYear": 2023, "Volume": "73", "Issue": "4", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572355, "Title": "Construction of Cloud Computing Task Scheduling Model Based on Simulated Annealing Hybrid Algorithm", "Abstract": "With the development of cloud computing technology, effective task scheduling can help people improve work efficiency. Therefore, this study presented a hybrid algorithm on the grounds of simulated annealing and taboo search to optimize task scheduling in cloud computing. This study presented a hybrid algorithm for optimizing the cloud computing task scheduling model. The model used simulated annealing algorithm and taboo search algorithm to convert the objective function into an energy function, allowing atoms to quickly arrange in terms of a certain rule for obtaining the optimal solution. The study analyzed the model through simulation experiments, and the experiment showed that the optimal value of the hybrid algorithm in high-dimensional unimodal testing was 7.15E-247, far superior to the whale optimization algorithm's 3.99E-28 and the grey wolf optimization algorithm's 1.10E-28. The completion time of the hybrid algorithm decreased with the growth of virtual machines, and the shortest time was 8.6 seconds. However, the load balancing degree of the hybrid algorithm increased with the growth of virtual machines. The final results indicated that the proposed hybrid algorithm exhibits high efficiency and superior performance in cloud computing task scheduling, especially when dealing with large-scale and complex optimization problems.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150509", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "Kejin Lv", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572359, "Title": "Enhancing Whale Optimization Algorithm with Differential Evolution and Lévy Flight for Robot Path Planning", "Abstract": "Path planning is a prominent and essential part of mobile robot navigation in robotics. It allows robots to determine the optimal path from a given beginning point to a desired end goal. Additionally, it enables robots to navigate around obstacles, recognize secure pathways, and select the optimal route to follow, considering multiple aspects. The Whale Optimization Algorithm (WOA) is a frequently adopted approach to planning mobile robot paths. However, conventional WOA suffers from drawbacks such as a sluggish convergence rate, inefficiency, and local optimization traps. This study presents a novel methodology integrating WOA with Lévy flight and Differential Evolution (DE) to plan robot paths. As WOA evolves, the Levy flight promotes worldwide search capabilities. On the other hand, DE enhances WOA's ability to perform local searches and exploitation while also maintaining a variety of solutions to avoid getting stuck in local optima. The simulation results demonstrate that the proposed approach offers greater planning efficiency and enhanced route quality.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150540", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "Rongrong TANG", "Affiliation": ""}, {"AuthorId": 2, "Name": "Xuebang TANG", "Affiliation": ""}, {"AuthorId": 3, "Name": "Hongwang ZHAO", "Affiliation": ""}], "References": []}, {"ArticleId": 115572413, "Title": "Dynamic Display of Ceramic Model Based on Virtual Reality Environment", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2024.S28.69-82", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572422, "Title": "Analyzing Test Performance of BSIT Students and Question Quality: A Study on Item Difficulty Index and Item Discrimination Index for Test Question Improvement", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijitcs.2024.03.01", "PubYear": 2024, "Volume": "16", "Issue": "3", "JournalId": 8584, "JournalTitle": "International Journal of Information Technology and Computer Science", "ISSN": "2074-9007", "EISSN": "2074-9015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information and Communications Technology, Nueva Ecija University of Science and Technology, Cabanatuan City, Philippines"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572448, "Title": "Table of Contents Vol 24, No 1 (2023)", "Abstract": "", "Keywords": "", "DOI": "10.4102/sajhivmed.v24i1.1563", "PubYear": 2023, "Volume": "24", "Issue": "1", "JournalId": 16322, "JournalTitle": "Southern African Journal of HIV Medicine", "ISSN": "1608-9693", "EISSN": "2078-6751", "Authors": [{"AuthorId": 1, "Name": "Editorial Office", "Affiliation": "Southern African Journal of HIV Medicine, AOSIS"}], "References": []}, {"ArticleId": 115572462, "Title": "Document-Level Relation Extraction Based on Machine Reading Comprehension and Hybrid Pointer-sequence Labeling", "Abstract": "<p>Document-level relational extraction requires reading, memorization and reasoning to discover relevant factual information in multiple sentences. It is difficult for the current hierarchical network and graph network methods to fully capture the structural information behind the document and make natural reasoning from the context. Different from the previous methods, this paper reconstructs the relation extraction task into a machine reading comprehension task. Each pair of entities and relationships is characterized by a question template, and the extraction of entities and relationships is translated into identifying answers from the context. To enhance the context comprehension ability of the extraction model and achieve more precise extraction, we introduce large language models (LLMs) during question construction, enabling the generation of exemplary answers. Besides, to solve the multi-label and multi-entity problems in documents, we propose a new answer extraction model based on hybrid pointer-sequence labeling, which improves the reasoning ability of the model and realizes the extraction of zero or multiple answers in documents. Extensive experiments on three public datasets show that the proposed method is effective.</p>", "Keywords": "", "DOI": "10.1145/3666042", "PubYear": 2024, "Volume": "23", "Issue": "7", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "xia<PERSON>i wang", "Affiliation": "China Language Intelligence Research Center, Capital Normal University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, North China University of Technology, Beijing China;China Language Intelligence Research Center, Capital Normal University, Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Engineering, Capital Normal University, Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, North China University of Technology, Beijing China"}, {"AuthorId": 5, "Name": "guixia guan", "Affiliation": "School of Information Engineering, Capital Normal University, Beijing China"}, {"AuthorId": 6, "Name": "qing zhang", "Affiliation": "School of Information Science and Technology, CNONIX National Standard Application and Promotion Laboratory,, North China University of Technology, Beijing China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China Language Intelligence Research Center, Capital Normal University, Beijing China"}], "References": [{"Title": "A Survey on Machine Reading Comprehension Systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "28", "Issue": "6", "Page": "683", "JournalTitle": "Natural Language Engineering"}]}, {"ArticleId": 115572464, "Title": "Deep Learning Techniques for Pancreatic Cancer Analysis: A Systematic Review and Implantation Prerequisites", "Abstract": "Of all the cases of pancreatic cancer reported in 2022, 52% of patients belonged to stage IV with a 2.9% 5-year survival rate. Lower survival rates are reported because the advanced-stage incidences leave only <20% of patients eligible for in-patient tumor resect-ability. Due to the fatal consequences, researchers have been rigorously implementing artificial intelligence techniques for early detection and analysis of cancer. This systematic review scrutinizes published research from 2020 to 2023 to map key concepts based on objectives, techniques, imaging modalities, datasets, and current limitations in the emerging field of deep learning strategies applied to image analysis of the ailment. To achieve this aim, a research methodology was developed to investigate articles pertinent to deep learning deployment, radiological imaging modalities, and publicly available imaging datasets for pancreatic cancer classification. Based on the findings, the authors discussed major applications, the maximum applied computed tomography imaging modality, and 3 datasets. The authors emphasized future enhancements including multimodal detection, dataset generation, and ways to improve clinical applications. The summarized results also enlist optimum implementation prerequisites for the disease’s detection which are substantial to aid researchers, scholars, and academicians in gaining deep insights into related strategies for future experiments.", "Keywords": "Pancreatic cancer; Deep learning; Computer-aided diagnosis; Datasets", "DOI": "10.1016/j.procs.2024.04.295", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> Delhi Technical University for Women, Delhi, India;Maharaja Surajmal Institute of Technology, Delhi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>dira Gandhi Delhi Technical University for Women, Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Maharaja <PERSON><PERSON>mal Institute of Technology, Delhi, India"}], "References": [{"Title": "Deep learning convolutional neural network (CNN) With Gaussian mixture model for predicting pancreatic cancer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "15-16", "Page": "10233", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Automatic captioning for medical imaging (MIC): a rapid review of literature", "Authors": "Djamila-<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "5", "Page": "4019", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Pancreatic Cancer Segmentation and Classification in CT Imaging using Antlion Optimization and Deep Learning Mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "50", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "A Novel Method for Human MRI Based Pancreatic Cancer Prediction Using Integration of Harris Hawks Varients & VGG16: A Deep Learning Approach", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "1", "Page": "115", "JournalTitle": "Informatica"}, {"Title": "Novel computer aided diagnostic system using hybrid neural network for early detection of pancreatic cancer", "Authors": "<PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "64", "Issue": "4", "Page": "815", "JournalTitle": "Automatika"}]}, {"ArticleId": 115572467, "Title": "A Framework for Cardiac Arrest Prediction via Application of Ensemble Learning Using Boosting Algorithms", "Abstract": "Cardiovascular diseases are a major setback for human life and a critical problem in hospitals for public health. The sedentary lifestyle of every individual lead to various heart diseases. Cardio diseases malfunction the electrical beating of the heart and result in sudden death. Thus, a novel early warning system needed to be developed for cardio patients with higher sensitivity and lower false positive rates. This work helps in the early detection of cardiac arrest by analyzing the dataset. Our work has mainly two aspects: analyzing the dataset and deriving relations among features and early diagnosis of cardiac arrest with enhanced F1-score. A number of Machine Learning algorithms were applied to conduct the experiment. The work also compares various boosting algorithms named AdaBoost, LightGBM, CatBoost and XgBoost. We used different Machine Learning algorithms in which CatBoost gave an enhanced and best prediction score 98.08%. In the case of CatBoost the predicted positive value is 98 % and Sensitivity is 97.8%.", "Keywords": "Heart disease; Machine Learning; Boosting; CatBoost; AdaBoost; XgBoost;LightGBM", "DOI": "10.1016/j.procs.2024.04.311", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Jamia Millia Islamia, New Delhi, 110025, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Cagliari, Cagliari, 09124, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jamia Millia Islamia, New Delhi, 110025, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology, New Delhi, 110016, India"}], "References": [{"Title": "Predictive model of cardiac arrest in smokers using machine learning technique based on Heart Rate Variability parameter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "19", "Issue": "3/4", "Page": "174", "JournalTitle": "Applied Computing and Informatics"}, {"Title": "Ensemble Deep Learning Models for Heart Disease Classification: A Case Study from Mexico", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "207", "JournalTitle": "Information"}, {"Title": "Heart Disease Prediction using Machine Learning Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "6", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 115572525, "Title": "Enhanced Arachnid Swarm-Tuned Convolutional Neural Network Model for Efficient Intrusion Detection", "Abstract": "Digital systems in the connected world of today bring convenience but also complicated cyber security challenges. The inadequacies of conventional intrusion detection techniques are exposed by the constant adaptation and exploitation of vulnerabilities by advanced cyber threats. Identifying dangers in massive data flows gets more difficult as networks grow, necessitating innovative methods. With the aim of minimizing these concerns, a new ID model is created utilizing cutting-edge machine learning to proactively and flexibly combat dynamic cyber attacks, with regard to evolving cyber attackers, this model seeks to improve accuracy and protection systems. This research develops an arachnid swarm optimization-based Convolutional neural network (ASO opt CNN) model to improve ID performance. An improved modified residual CNN is employed in the model to lessen the vanishing and exploding gradient problems in deep networks and facilitates the optimization process, making it easier for deep networks to learn. The developed model is adjusted using arachnid swarm optimization (ASO), which is the hybridization particle swarm optimization (PSO) and social spider optimization (SSO). Utilizing test data, the model's efficacy is evaluated at last. This test data is also subjected to preprocessing, which leads to the creation of a robust detection model that can identify the presence of network attacks. Experimentation and comparison indicate the approach's effectiveness by attaining accuracies of 95.95%, 95.61%, and 95.00% for three datasets respectively. This highlights the developed model’s potential to detect intrusions more effectively.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505117", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572527, "Title": "Automated Motor Imagery Detection Through EEG Analysis and Deep Learning Models for Brain-Computer Interface Applications", "Abstract": "The classification of motor imagery holds significant importance within brain-computer interface (BCI) research as it allows for the identification of a person's intention, such as controlling a prosthesis. Motor imagery involves the brain's dynamic activities, commonly captured using electroencephalography (EEG) to record nonstationary time series with low signal-to-noise ratios. While various methods exist for extracting features from EEG signals, the application of deep learning techniques to enhance the representation of EEG features for improved motor imagery classification performance has been relatively unexplored. This research introduces a new deep learning approach based on two-dimensional CNNs with different architectures. Specifically, time-frequency domain representations of EEGs obtained by the wavelet transform method with different mother wavelets (Mexicanhat, Cmor, and Cgaus). The BCI competition IV-2a dataset held in 2008 was utilized for testing the proposed deep learning approaches. Several experiments were conducted and the results showed that the proposed method achieved better performance than some state-of-the-art methods. The findings of this study showed that the architecture of CNN and specifically the number of convolution layers in this deep learning network has a significant effect on the classification performance of motor imagery brain data. In addition, the mother wavelet in the wavelet transform is very important in the classification performance of motor imagery EEG data.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150514", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Bo<PERSON> Liu", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572580, "Title": "Diagnosis of NEC using a Multi-Feature Fusion Machine Learning Algorithm", "Abstract": "Necrotizing enterocolitis (NEC) is a severe gastrointestinal emergency in neonates, marked by its complex etiology, ambiguous clinical manifestations, and significant morbidity and mortality, profoundly affecting long-term pediatric health outcomes. The prevailing diagnostic approaches for NEC, including traditional manual auscultation of bowel sounds, suffer from limited sensitivity and specificity, leading to potential misdiagnoses and delayed treatment. In this paper, we introduce a groundbreaking NEC diagnostic framework employing machine learning algorithms that utilize multi-feature fusion of bowel sounds, significantly improving the diagnostic accuracy. Bowel sounds from NEC patients and healthy newborns are meticulously captured using a specialized acquisition system, designed to overcome the inherent challenges associated with the low amplitude, substantial background noise, and high variability of neonatal bowel sounds. To enhance the diagnostic framework, we extract mel-frequency cepstral coefficient (MFCC), short-time energy (STE), and zero-crossing rate (ZCR) to capture comprehensive frequency and time domain features, ensuring a robust representation of bowel sound characteristics. These features are then integrated using a multi-feature fusion technique to form a singular feature vector, providing a rich, integrated dataset for the machine learning algorithm. Employing the support vector machine (SVM), the algorithm achieved an accuracy (ACC) of 88.00%, sensitivity (SEN) of 100.00%, and an area under the receiver operating characteristic (ROC) curve (AUC) of 97.62%, achieving high accuracy in diagnosing NEC. This innovative approach not only improves the accuracy and objectivity of NEC diagnosis but also shows promise in revolutionizing neonatal care through facilitating early and precise diagnosis. It significantly enhances clinical outcomes for affected neonates.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505114", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Yunzhou Li", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572590, "Title": "Automatic Skull Shape Completion of Defective Skulls Using Transformers for Cranial Implant Design", "Abstract": "Cranioplasty is a surgical method that restores the aesthetic and protecting function of a damaged skull by implanting material into the damaged area.Fast and accurate design of patient specific cranial implants is very much required in the process of cranioplasty.The time consumption for designing and manufacturing of patient specific cranial implant has become an obstruction for cranioplasty procedures. Hence, a fully automatic and fast design of cranial implant becomes very important. The cranial implant design processmainly comprises of two steps. The former step concentrates on the automatic skull shape completion of defective skulls to fill the gaps and the cracks created in the skull. While the second step computes the difference between defective input and the completed skull for generating the implant. Currently computer aided design is used for the skull shape completion task which is a time consuming process. The application of deep learning techniques may result to faster and accurate skull shape completionwhich can be used for the design of patient specific cranial implants. This work proposes a novel approach combining 3D U-Net with Transformers for the automatic skull shape completion task.We are using a vision transformer in the encoder section of the 3D U-Net architecture to consider the volumetric skull reconstruction as sequence- to-sequence prediction problem and to efficaciously grasp the global contextual information. The work also compares its performance with the famous variants of 3D U-Net deep network model namely, 3D U-Net and 3D U-Net with attention. From the values resulted for the dice coefficient metric it is clear that the proposed 3D U-Net with transformer approach performs better than the other two models on test images.", "Keywords": "Cranioplasty; 3DU-Net; 3DU-Net with Attention; 3DU-Net with Transformer; Dice coefficient", "DOI": "10.1016/j.procs.2024.04.312", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department. of Computer Science and Engineering, Amrita School of Computing, Bengaluru, Amrita Vishwa Vidyapeetham, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department. of Computer Science and Engineering, Amrita School of Computing, Bengaluru, Amrita Vishwa Vidyapeetham, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Electrical Engineering and Computer Science, University of Missouri, Missouri, USA"}], "References": []}, {"ArticleId": 115572596, "Title": "A multimodal attention-fusion convolutional neural network for automatic detection of sleep disorders", "Abstract": "<p>Sleep is essential for human physical and mental health. Sleep disorders are a significant threat to human health, and a large number of people in the world suffer from sleep disorders. Effective detection of sleep disorders is essential for the treatment of sleep disorders. Questionnaires and scale assessments are traditional methods of sleep disorder detection, which are subjective, time-consuming and prone to misdiagnosis. To detect sleep disorders quickly and accurately, a Multimodal Attention-Fusion Convolutional Neural Network is proposed in this paper. The network uses electroencephalography, electrooculography, electrocardiography, and electromyography signals to automatically identify healthy and five sleep disorders, namely insomnia, narcolepsy, periodic leg movement, rapid eye movement behaviour disorder and nocturnal frontal lobe epilepsy. First, multiple convolutional neural network branches are used to extract time-invariant features of multimodal signals. Then, a multi-scale attention module based on dilated convolutional networks and a squeeze and excite block is proposed for further extracting features with different scales and fusing feature information. Finally, a prediction module consisting of fully connected layers is used to detect sleep disorders. The accuracy, F1 score, and Kappa coefficient obtained on the Cyclic Alternating Pattern sleep dataset are 99.56%, 99.49% and 0.9942, respectively. Compared to the existing state-of-the-art studies, the method proposed in this paper has higher performance in sleep disorder detection.</p>", "Keywords": "Sleep disorders; Multimodal signals; Attentional-Fusion; Convolutional neural network", "DOI": "10.1007/s10489-024-05499-7", "PubYear": 2024, "Volume": "54", "Issue": "11-12", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Wang", "Affiliation": "School of Electrical and Electronic Information, Xihua University, Chengdu, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Electronic Information, Xihua University, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Electronic Information, Xihua University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Grid Sichuan Electric Power Research Institute, Chengdu, China"}, {"AuthorId": 5, "Name": "Fang You", "Affiliation": "Department of Cardiology, Chengdu First People’s Hospital, Chengdu, China; Corresponding author."}], "References": [{"Title": "Automated identification of insomnia using optimal bi-orthogonal wavelet transform technique with single-channel EEG signals", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "224", "Issue": "", "Page": "107078", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 115572654, "Title": "Development of a Broadband Photodetector Utilizing ZnO Nanorods with Grating Structure Fabricated via Nanoimprint Lithography", "Abstract": "Nanoimprint lithography (NIL) has emerged as a powerful technique for integrating nanostructures into semiconductor devices, particularly optoelectronic devices such as solar cells, light-emitting diodes (LEDs), lasers, and photodetectors (PDs). This study presents the successful fabrication of nano-patterned ZnO nanorods (ZnO NRs) using NIL, resulting in a grating structure with a linewidth of approximately 600 nm and a periodicity of around 1080 nm. Morphological characterizations, including scanning electron microscopy (SEM), atomic force microscopy (AFM), and laser confocal microscopy, reveal well-defined grating structures with ZnO NRs deposited on the substrate. Transmission electron microscopy (TEM) and X-ray diffraction (XRD) further confirm the crystallinity of ZnO NRs, both with and without NIL treatment. X-ray photoemission spectroscopy (XPS) shows that the NIL-patterned samples with ZnO NRs exhibit fewer defects, attributed to the O<sub>2</sub> plasma treatment during the NIL process. The NIL-defined grating structure of ZnO NRs is then employed in the fabrication of photodetectors, which demonstrate higher sensitivity than the pure ZnO-NRs-based PDs, presumably due to the reduce defects. Moreover, the NIL-defined PDs exhibit broadband photoresponses across the UV and visible light spectrum. This work introduces a novel device design for photodetectors and highlights the potential of NIL in creating broadband ZnO-based PDs, promising applications in future optoelectronics.", "Keywords": "", "DOI": "10.1016/j.sna.2024.115530", "PubYear": 2024, "Volume": "375", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Materials and Optoelectronic Engineering, National Chi Nan University, Nantou 54561, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Materials and Optoelectronic Engineering, National Chi Nan University, Nantou 54561, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Materials and Optoelectronic Engineering, National Chi Nan University, Nantou 54561, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Materials and Optoelectronic Engineering, National Chi Nan University, Nantou 54561, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Photonics, National Cheng Kung University, Tainan City 70101, Taiwan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Chemistry, National Chi Nan University, Nantou, 54561, Taiwan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Photonics, National Cheng Kung University, Tainan City 70101, Taiwan;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Materials and Optoelectronic Engineering, National Chi Nan University, Nantou 54561, Taiwan;Corresponding authors"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering, Yale University, New Haven, CT 06511, USA"}], "References": [{"Title": "The Enhancement of Visible Photodetector Performance based on Mn doped ZnO Nanorods by Substrate Architecting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "311", "Issue": "", "Page": "112085", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 115572655, "Title": "Retraction Note: Three dimensional modelling of MRI knee images using improved edge detection and finite element modelling", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11042-024-19574-1", "PubYear": 2024, "Volume": "83", "Issue": "27", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Anna University, Chennai, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Mahendra College of Engineering, Salem, India"}], "References": []}, {"ArticleId": 115572697, "Title": "Improving recognition of deteriorated historical Persian geometric patterns by fusion decision methods", "Abstract": "<p>Historical architecture has different special styles attributed to each era, dynasty, or region. These styles are common features such as geometric properties, ratios, scales, colors, and artistic techniques. Historical geometric ornaments have an enormous capability for classification based on their geometric characteristics. Smart pattern recognition allows researchers to classify huge databases of heritage for useful internet searches. So, our main goal in this paper is to implement the detection of categories in geometric patterns for classification and documentation in which by the photography of ornaments in every monument, the type of patterns and the number of every type of pattern would be estimated quickly. Furthermore, due to occurring deterioration in these patterns, our method also contributes to recognizing the deteriorated patterns. When we encounter numerous pieces of deteriorated patterns, manual recognition in order to reassemble and reconstruct is usually impossible or time-consuming. With the aid of artificial intelligence, in this paper, our aim is to seek to solve the automatic recognition of historical geometric patterns, even patterns having deterioration as an occlusion via image processing and machine learning methods. A challenging issue that researchers would tackle in detecting historical geometric pattern’s types is the variety in geometric textures, especially when they have occlusion such as deterioration. This issue leads to limited success in classifying via extracting only one feature. The other issue is that the extracted feature must be invariant to the transformation, such as scale, rotation, and noise variation. To cope with the challenges mentioned above and accurately classify, we plan to use the fusion method based on extracting global and local features. So, the features extracted from images in this research are based on local and global. In other words, the proposed fusion strategy lies both in feature and decision level, but the core is the proposed three combination methods in fusion decision methods. In this method, the dataset is composed of four main Persian geometric pattern types: Tond dah, Kond tablghenas, Hashtva 4 lengeh, and HashtvatablKond. So, the model will be trained by extracting global and local features of the images separately. Random forest, as the prevalent machine learning algorithm, is proposed for training data and predicting the class of input images. Finally, the probability of prediction for random forest classifiers is fused by the Decision Templates (DT) combiner, Naïve Bayes (NB) combiner, and Dempster–Shafer combination methods. In comparison with the individual classifier accuracy results of 80% and 85% for global and local features respectively, our proposed approach achieves an improved accuracy of 90%, 88%, and 90% in three fusion decision methods including DT combiner, NB combiner, and Dempster–Shafer combination methods.</p>", "Keywords": "Geometric Patterns; Fusion decision Methods; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> combination Method; Decision Templates (DT); <PERSON><PERSON><PERSON> (NB) combiner", "DOI": "10.1007/s00521-024-09932-3", "PubYear": 2024, "Volume": "36", "Issue": "20", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Architectural and Urban Conservation, Art University of Isfahan, Hakim Nezami, Isfahan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, Yazd University, University Boulevard, Yazd, Yazd, Iran; Department of Mechanical Engineering, Isfahan University of Technology, Isfahan, Iran; Corresponding author."}], "References": [{"Title": "Notions of explainability and evaluation approaches for explainable artificial intelligence", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "76", "Issue": "", "Page": "89", "JournalTitle": "Information Fusion"}, {"Title": "Intelligent Restoration of Historical Parametric Geometric Patterns by Zernike Moments and Neural Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "4", "Page": "1", "JournalTitle": "Journal on Computing and Cultural Heritage"}, {"Title": "Fine-tuning SalGAN and PathGAN for extending saliency map and gaze path prediction from natural images to websites", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116282", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Defining a deep neural network ensemble for identifying fabric colors", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "109687", "JournalTitle": "Applied Soft Computing"}, {"Title": "Representation and compression of Residual Neural Networks through a multilayer network based approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119391", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 115572740, "Title": "Parameter Estimation of Cellular Communication Systems Models in Computational MATLAB Environment: A Systematic Solver-based Numerical Optimization Approaches", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijcnis.2024.03.06", "PubYear": 2024, "Volume": "16", "Issue": "3", "JournalId": 20260, "JournalTitle": "International Journal of Computer Network and Information Security", "ISSN": "2074-9090", "EISSN": "2074-9104", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Physics, Covenant University, Ota, Nigeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Theophilus <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Agbotiname Lucky Imoize", "Affiliation": ""}], "References": []}, {"ArticleId": 115572772, "Title": "Identity-preserving editing of multiple facial attributes by learning global edit directions and local adjustments", "Abstract": "Semantic facial attribute editing using pre-trained Generative Adversarial Networks (GANs) has attracted a great deal of attention and effort from researchers in recent years. Due to the high quality of face images generated by StyleGANs, much work has focused on the StyleGANs’ latent space and the proposed methods for facial image editing. Although these methods have achieved satisfying results for manipulating user-intended attributes, they have not fulfilled the goal of preserving the identity, which is an important challenge. We present ID-Style, a new architecture capable of addressing the problem of identity loss during attribute manipulation. The key components of ID-Style include a Learnable Global Direction (LGD) module, which finds a shared and semi-sparse direction for each attribute, and an Instance-Aware Intensity Predictor (IAIP) network, which finetunes the global direction according to the input instance. Furthermore, we introduce two losses during training to enforce the LGD and IAIP to find semi-sparse semantic directions that preserve the identity of the input instance. Despite reducing the size of the network by roughly 95% as compared to similar state-of-the-art works, ID-Style outperforms baselines by 10% and 7% in identity preserving metric (FRS) and average accuracy of manipulation (mACC), respectively.", "Keywords": "", "DOI": "10.1016/j.cviu.2024.104047", "PubYear": 2024, "Volume": "246", "Issue": "", "JournalId": 4830, "JournalTitle": "Computer Vision and Image Understanding", "ISSN": "1077-3142", "EISSN": "1090-235X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Amirkabir University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Amirkabir University of Technology, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Amirkabir University of Technology, Tehran, Iran;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Amirkabir University of Technology, Tehran, Iran"}], "References": [{"Title": "Generative adversarial networks", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "139", "JournalTitle": "Communications of the ACM"}, {"Title": "StyleFlow: Attribute-conditioned Exploration of StyleGAN-Generated Images using Conditional Continuous Normalizing Flows", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Designing an encoder for StyleGAN image manipulation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 115572776, "Title": "Optimal Uniform Strength Design of Frame and Lattice Structures", "Abstract": "This paper provides a procedure to obtain the uniform strength of frame and lattice structures. Uniform strength condition is achieved by performing the shape optimization of all beam elements of the structure. The beam shape which guarantees uniform strength is analytically deduced from the one-dimensional <PERSON><PERSON><PERSON> model. The optimization problem presents itself as the search for the zeros of the objective-functions vector, which is a non-linear system of equations representing the kinematic-congruence and forces balance at every node of the structure. The analytical formulation of the optimization problem allows to construct the objective-functions vector without the use of external structural computation, i.e. not recurring to any Finite Element Analysis to accomplish iterations. This latter feature entails a great advantage in terms of computing time required to perform optimization. The proposed analytical formulation allows to directly insert the uniform strength condition into the objective-functions vector, transforming the optimization into an unconstrained problem. Some examples are shown in which the performance of the optimization procedure is discussed in terms of robustness and rate of computational complexity while increasing the degrees of freedom of the structure. The reliability and the quality of the optimization are verified through Finite Element Analysis.", "Keywords": "Shape Optimization; Uniform-strength; Optimal Design; Frame Structures; Lattice Structures; Structural Optimization", "DOI": "10.1016/j.compstruc.2024.107430", "PubYear": 2024, "Volume": "301", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Enterprise Engineering, University of Rome “Tor Vergata”, Via del Politecnico 1, 00133 Rome, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Enterprise Engineering, University of Rome “Tor Vergata”, Via del Politecnico 1, 00133 Rome, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Enterprise Engineering, University of Rome “Tor Vergata”, Via del Politecnico 1, 00133 Rome, Italy"}], "References": [{"Title": "A review on genetic algorithm: past, present, and future", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "5", "Page": "8091", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 115572780, "Title": "Lossless Text Compression Using Recurrent Neural Networks", "Abstract": "Lossless Data compression is the process of reducing the size or the number of bits required to represent data, and Arithmetic coding is one of the popular lossless text compression techniques. This project focuses on lossless data compression using an innovative approach that combines arithmetic coding and Recurrent Neural Networks (RNN) with the help of encoder-decoder architecture. The proposed method aims to improve compression ratios compared to standard techniques. By leveraging the power of RNNs and arithmetic coding, encoder-decoder architecture effectively captures patterns and dependencies in the input data, enabling more efficient compression. The proposed approach achieved a remarkable improvement, achieving an impressive compression ratio that is, on average, 3.5 times smaller than that achieved by traditional methods. These results showcase the potential of developing improved general-purpose compressors based on neural networks and hybrid modeling. This demonstrates the potential of the proposed method for significantly enhancing lossless data compression performance.", "Keywords": "Data compression; Deep Neural Networks; Recurrent Neural Networks; encoding; decoding", "DOI": "10.1016/j.procs.2024.04.315", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, KLE Technological University, Hubli, Karnataka, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, KLE Technological University, Hubli, Karnataka, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>th KJ", "Affiliation": "School of Computer Science, KLE Technological University, Hubli, Karnataka, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science, KLE Technological University, Hubli, Karnataka, India"}, {"AuthorId": 5, "Name": "Ritik Poonia", "Affiliation": "School of Computer Science, KLE Technological University, Hubli, Karnataka, India"}, {"AuthorId": 6, "Name": "S G Totad", "Affiliation": "School of Computer Science, KLE Technological University, Hubli, Karnataka, India"}], "References": []}, {"ArticleId": 115572821, "Title": "Enhancing Brain Tumor Assessment: A Comprehensive Approach using Computerized Diagnostic Tool and Advanced MRI Techniques", "Abstract": "Assessment of brain tumour using Three-Dimensional Magnetic Resonance Imaging (3D MRI) is computationally multifaceted task. Currently, hospitals employ 2D MRI scans, followed by manual evaluation by experienced doctors, aided by a Computerized Diagnostic Tool (CDT). This research aims to develop an advanced CDT to significantly enhance the accuracy of brain tumor assessment. The CDT presented in this study evaluates Axial-View (AV), Coronal-View (CV), and Sagittal-View (SV) MRI images. It encompasses a comprehensive pipeline, including pre-processing, post-processing, feature extraction, feature selection, and categorization phases. Various tumor segmentation techniques, including active contour, level-set, watershed, and region growing, are thoroughly explored. Additionally, a comparative analysis of classification methods such as SVM, ANFIS, k-NN, Random Forest, and Adaboost is conducted. Experimental validation using the BRATS 2016 dataset and real-time 2D MRI data demonstrates that the proposed CDT consistently achieves an average classification accuracy exceeding 95% in tumor-based categorization. This research represents a significant advancement in brain tumor assessment, leveraging machine learning and advanced MRI techniques to improve diagnostic precision.", "Keywords": "Machine learning; Brain Tumor; 3D MRI; Firefly Algorithm", "DOI": "10.1016/j.procs.2024.04.316", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Science & Technology, Muscat, PO 620, PC 130, Sultanate of Oman"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Science & Technology, Muscat, PO 620, PC 130, Sultanate of Oman"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Science & Technology, Muscat, PO 620, PC 130, Sultanate of Oman"}, {"AuthorId": 4, "Name": "Naserya Al Hinai", "Affiliation": "National University of Science & Technology, Muscat, PO 620, PC 130, Sultanate of Oman"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National University of Science & Technology, Muscat, PO 620, PC 130, Sultanate of Oman"}], "References": [{"Title": "Contrast enhanced medical MRI evaluation using Tsallis entropy and region growing segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "1", "Page": "961", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A reliable framework for accurate brain image examination and treatment planning based on early diagnosis support for clinicians", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "20", "Page": "15897", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 115572889, "Title": "Animation Design and Virtual Reality Dynamic Scene Rendering Optimization Based on Transformer Model", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2024.S28.1-14", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "Yannan Xie", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572895, "Title": "Road Accident Detection using SVM and Learning: A Comparative Study", "Abstract": "Everyday, a great deal of children and young adults (aged five to 29) lives are lost in road accidents. The most frequent causes are a driver’s behavior, the streets infrastructure is of lower quality and the delayed response of emergency services especially in rural areas. There is a need for automatics road accident systems detection that can assist in recognizing road accidents and determining their positions. This work reviews existing machine learning approaches for road accidents detection. We propose three distinct classifiers: Convolutional Neural Network CNN, Recurrent Convolution Neural Network R-CNN and Support Vector Machine SVM, using a CCTV footage dataset. These models are evaluated based on ROC curve, F1 measure, precision, accuracy and recall, and the achieved accuracies were 92%, 82%, and 93%, respectively. In addition, we suggest using an ensemble learning strategy to maximize the strengths of individual classifiers, raising detection accuracy to 94%.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150565", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115572954, "Title": "FLBlock: A Sustainable Food Supply Chain Approach Through Federated Learning and Blockchain", "Abstract": "Blockchain technology has become a pivotal asset in many industries, particularly Food Supply Chain Management. This technology, renowned for its capacity to preserve data immutably and expedite tracking across the intricate stages of the food supply chain, plays a pivotal role in enhancing transparency across the food industry. We proposed a Blockchain-based Federated Learning model. This model amalgamates the strengths of Blockchain and federated learning within the food supply chain, focusing on sustainable and high-quality food production. Our primary objective is to fortify data security within various facets of the food supply chain, encompassing raw material suppliers, producers, distributors, and consumers. Simultaneously, we strive to enhance the precision of our model, aiming to outperform existing Blockchain-based solutions in terms of accuracy. The proposed model demonstrates superior accuracy compared to existing methods.", "Keywords": "Blockchain; Federated Learning; Supply Chain; Food Supply Chain", "DOI": "10.1016/j.procs.2024.04.290", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Assistant Professor, School of Computer Science & Engineering, Presidency University, Bangalore-560064, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Associate Professor, School of Computer Science & Engineering, RV University, Bangalore-560059, India"}], "References": [{"Title": "Securing federated learning with blockchain: a systematic literature review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "5", "Page": "3951", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "FusionFedBlock: Fusion of blockchain and federated learning to preserve privacy in industry 5.0", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "90", "Issue": "", "Page": "233", "JournalTitle": "Information Fusion"}, {"Title": "Recent Advances of Blockchain and Its Applications", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "4", "Page": "363", "JournalTitle": "Journal of Social Computing"}, {"Title": "LAFED: A lightweight authentication mechanism for blockchain-enabled federated learning system", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "56", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 115572960, "Title": "Anomaly identification of English online learning data based on local outlier factor", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJCAT.2023.138839", "PubYear": 2023, "Volume": "73", "Issue": "4", "JournalId": 9574, "JournalTitle": "International Journal of Computer Applications in Technology", "ISSN": "0952-8091", "EISSN": "1741-5047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573014, "Title": "Modified Artificial Bee Colony Algorithm for Load Balancing in Cloud Computing Environments", "Abstract": "Task scheduling in cloud computing is a complex optimization problem influenced by the ever-changing user requirements and the different architectures of cloud systems. Efficiently distributing workloads across Virtual Machines (VMs) is critical to mitigate the negative consequences of inadequate and excessive workloads, such as higher power consumption and possible machine malfunctions. This paper presents a novel method for dynamic load balancing using a Modified Artificial Bee Colony (MABC) algorithm. The ABC algorithm has exceptional competence in solving complex nonlinear optimization problems based on bee colonies' foraging behavior. Nevertheless, the traditional version of the ABC algorithm cannot effectively use resources, resulting in a rapid decline in population diversity and an ineffective spread of knowledge about the best solution between generations. To address these limitations, this study integrates a genetic model into the algorithm, enhancing population diversity through crossover and mutation operators. The developed algorithm is compared with the prevailing algorithms to confirm its effectiveness. The results of the proposed MABC algorithm for the load balancing method are compared with the current ones, and it is observed that this algorithm is more beneficial in terms of cost and energy as well as resource utilization.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505103", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "Qian LI", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573038, "Title": "An encoder-decoder-based image segmentation method for abrasive height detection of diamond wire", "Abstract": "<p>The diamond wire (DW) is widely used in the field of slicing semiconductor materials such as monocrystalline silicon and polycrystalline silicon. The quality of the wafer is greatly affected by the protrusion height consistency of the abrasives consolidated on the surface of the DW. However, the online detection accuracy of the abrasive protrusion height is still unsatisfactory, which is mainly caused by the rough DW image segmentation results. In this paper, a diamond wire image segmentation model (DWISM) is proposed based on deep learning. To improve the segmentation accuracy of the DW image, a hard-points selection component is designed to adaptively select difficult-to-predict pixels in high-frequency regions such as the edge of the abrasives. The feature vector extraction and category re-prediction of these points are implemented in a specially designed refine head. Extensive comparative experiments are implemented to verify the performance of the proposed method. The results show that DWISM could achieve 90.8 mean Intersection over Union (mIoU) with 11.22 Frames Per Second (FPS) inference speed. The promising results demonstrate the great potential of DWISM for improving the protrusion height online detection accuracy of the abrasives consolidated on the surface of DW.</p>", "Keywords": "Diamond wire; Semantic segmentation; Abrasive height; Detection", "DOI": "10.1007/s00170-024-13829-9", "PubYear": 2024, "Volume": "133", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China"}, {"AuthorId": 2, "Name": "Peiqi Ge", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China; Key Laboratory of High-Efficiency and Clean Mechanical Manufacture, Ministry of Education, Shandong University, Jinan, China; Corresponding author."}, {"AuthorId": 3, "Name": "Wenbo Bi", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China; Key Laboratory of High-Efficiency and Clean Mechanical Manufacture, Ministry of Education, Shandong University, Jinan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Shandong University, Jinan, China"}], "References": [{"Title": "Deep learning-based method for SEM image segmentation in mineral characterization, an example from Duvernay Shale samples in Western Canada Sedimentary Basin", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "104450", "JournalTitle": "Computers & Geosciences"}, {"Title": "Machine vision online detection for abrasive protrusion height on the surface of electroplated diamond wire saw", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "11-12", "Page": "7923", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A novel method for cage whirl motion capture of high-precision bearing inspired by U-Net", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105552", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A hybrid deep learning pavement crack semantic segmentation", "Authors": "<PERSON><PERSON>; <PERSON>; Riyadh Nazar <PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106142", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Rider Water Wave-enabled deep learning for disease detection in rice plant", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "182", "Issue": "", "Page": "103472", "JournalTitle": "Advances in Engineering Software"}]}, {"ArticleId": 115573043, "Title": "Enhancing Myocardial Disease Prediction with DOC-NET+ Architecture: A Custom Data Analysis Approach for the EMIDEC Challenge", "Abstract": "Public health experts are deeply concerned about cardiovascular diseases, including numerous heart-related ailments that can prove fatal. Distinguishing between Myocarditis and myocardial infarction (MI) is difficult due to comparable symptoms and diagnostic complications. While endomyocardial biopsy (EMB), cardiac troponin indicators, electrocardiography (ECG), and echocardiography are useful in making preliminary diagnoses, their effectiveness is limited. The EMIDEC challenge initiative stands out as an important development in this context. It demonstrates how clinical physiological parameters, combined with delayed enhancement magnetic resonance imaging (DE-MRI), can significantly enhance classification reliability. The aim of this research is to provide a thorough assessment of the present state of the art in EMIDEC-related research. Subsequently, our intention is to establish a new database that will serve as the framework for comprehensive testing and evaluation of the architectural models drawn from the literature. We established a constructive collaboration with the Military Hospital of Tunis, which was crucial for patient data collection and access to a diverse group of patients diagnosed with Myocarditis and MI. We proceeded to select the appropriate architectural models for our study. We chose the DOC-NET and DOC-NET+ models based on prior research that demonstrated their effectiveness in similar classification contexts. DOC-NET and DOC-NET+ demonstrated accuracy rates of 95% and 100% in previous studies with the EMIDEC dataset. When these models were applied to our newly produced custom dataset, DOC-NET maintained its impressive performance with an accuracy score of 97%, while DOC-NET+ had an accuracy score of 98%. However, as we increased both the number of patients and the complexity of scenarios in our new dataset, the accuracy of DOC-NET+ dropped significantly to 91%. The CMR with AI combination allows for more customized and efficient evaluation, ultimately increasing patient outcomes and altering the cardiovascular healthcare environment. However, problems connected to the algorithm’s generalizability to complicated data continue to be a crucial concern in further refining these improvements.", "Keywords": "Classification; EMIDEC Challenge; Custom Database; DOC-Net+", "DOI": "10.1016/j.procs.2024.04.304", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tunis El Manar, Higher Institute of Medical Technologies of Tunis, Research Laboratory of Biophysics and Medical Technologies, 1006 Tunis, Tunisia"}, {"AuthorId": 2, "Name": "Rost<PERSON>", "Affiliation": "<PERSON>pard Monge Computer Science Laboratory. ESIEE Paris, Gustave Eiffel University, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Tunis El Manar, Higher Institute of Medical Technologies of Tunis, Research Laboratory of Biophysics and Medical Technologies, 1006 Tunis, Tunisia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Departement of Radiology of the Military Hospital of Tunis, Bab alioua, 1008 Montfleury, Tunis, Tunisia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tunis El Manar, Higher Institute of Medical Technologies of Tunis, Research Laboratory of Biophysics and Medical Technologies, 1006 Tunis, Tunisia"}], "References": [{"Title": "Emidec: A Database Usable for the Automatic Evaluation of Myocardial Infarction from Delayed-Enhancement Cardiac MRI", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "4", "Page": "89", "JournalTitle": "Data"}]}, {"ArticleId": 115573055, "Title": "Enhancing Cyber Threat Detection with an Improved Artificial Neural Network Model", "Abstract": "Identifying cyberattacks that attempt to compromise digital systems is a critical function of intrusion detection systems (IDS). Data labeling difficulties, incorrect conclusions, and vulnerability to malicious data injections are only a few drawbacks of using machine learning algorithms for cybersecurity. To overcome these obstacles, researchers have created several network IDS models, such as the Hidden Naive Bayes Multiclass Classifier and supervised/unsupervised machine learning techniques. This study provides an updated learning strategy for artificial neural network (ANN) to address data categorization problems caused by unbalanced data. Compared to traditional approaches, the augmented ANN’s 92% accuracy is a significant improvement owing to the network’s increased resilience to disturbances and computational complexity, brought about by the addition of a random weight and standard scaler. Considering the ever-evolving nature of cybersecurity threats, this study introduces a revolutionary intrusion detection method.", "Keywords": "Cybersecurity; Intrusion detection; Deep learning; Artificial neural network; Imbalanced data classification", "DOI": "10.1016/j.dsm.2024.05.002", "PubYear": 2025, "Volume": "8", "Issue": "1", "JournalId": 84310, "JournalTitle": "Journal of Information Technology and Data Management", "ISSN": "2666-7649", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Toluwase Sunday Oyinloye", "Affiliation": "Department of Computer Science, African University of Science and Technology, Abuja, 900107, Nigeria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Computer Science, University of Missouri, Columbia, MO 65201, USA;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, African University of Science and Technology, Abuja, 900107, Nigeria;Corresponding author"}], "References": [{"Title": "The Role of Machine Learning in Cybersecurity", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "1", "Page": "1", "JournalTitle": "Digital Threats: Research and Practice"}, {"Title": "Cybersecurity Threats and Their Mitigation Approaches Using Machine Learning—A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2", "Issue": "3", "Page": "527", "JournalTitle": "Cybersecurity"}]}, {"ArticleId": 115573084, "Title": "Synergistic Optimization of CAD and Virtual Reality Technology in Visual Communication and Information Visualization Design", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2024.S28.224-237", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "Xiang<PERSON> Zhu", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573092, "Title": "Exploring Music Style Transfer and Innovative Composition using Deep Learning Algorithms", "Abstract": "Automatic music generation represents a challenging task within the field of artificial intelligence, aiming to harness machine learning techniques to compose music that is appreciable by humans. In this context, we introduce a text-based music data representation method that bridges the gap for the application of large text-generation models in music creation. Addressing the characteristics of music such as smaller note dimensionality and longer length, we employed a deep generative adversarial network model based on music measures (MT-CHSE-GAN). This model integrates paragraph text generation methods, improves the quality and efficiency of music melody generation through measure-wise processing and channel attention mechanisms. The MT-CHSE-GAN model provides a novel framework for music data processing and generation, offering an effective solution to the problem of long-sequence music generation. To comprehensively evaluate the quality of the generated music, we used accuracy, loss rate, and music theory knowledge as evaluation metrics and compared our model with other music generation models. Experimental results demonstrate our method's significant advantages in music generation quality. Despite progress in the field of automatic music generation, its application still faces challenges, particularly in terms of quantitative evaluation metrics and the breadth of model applications. Future research will continue to explore expanding the model's application scope, enriching evaluation methods, and further improving the quality and expressiveness of the generated music. This study not only advances the development of music generation technology but also provides valuable experience and insights for research in related fields.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505101", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573156, "Title": "Comments on “An application of parametric approach for interval differential equation in inventory model for deteriorating items with selling-price-dependent demand”", "Abstract": "<p>This paper points out the interval form deficiencies in the recent paper “An application of parametric approach for interval differential equation in inventory model for deteriorating items with selling-price-dependent demand” by <PERSON><PERSON> et al. (Neural Comput Appl 32(17):14069–14085, 2020). The comments in this paper pertain mainly to the drawbacks of interval arithmetic (SIA) presented in Section 3 of <PERSON><PERSON> et al. (2020) referencing the convention of converting an interval-valued model into a parametric form. Proposed corrections to the overestimation of the interval form through examples are presented. </p>", "Keywords": "CIA; Interval differential equation; Lagrange multiplier; Interval-valued cost factors", "DOI": "10.1007/s00521-024-10003-w", "PubYear": 2024, "Volume": "36", "Issue": "21", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "A<PERSON>s <PERSON>", "Affiliation": "Centre for Advanced Studies in Pure and Applied Mathematics, <PERSON><PERSON><PERSON> University, Multan, Pakistan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Advanced Studies in Pure and Applied Mathematics, <PERSON><PERSON><PERSON> University, Multan, Pakistan"}], "References": [{"Title": "An application of parametric approach for interval differential equation in inventory model for deteriorating items with selling-price-dependent demand", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "17", "Page": "14069", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Interval valued demand related inventory model under all units discount facility and deterioration via parametric approach", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "55", "Issue": "3", "Page": "2455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Interval valued demand and prepayment-based inventory model for perishable items via parametric approach of interval and meta-heuristic algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "242", "Issue": "", "Page": "108343", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Inventory model for perishable items for interval-valued price dependent demand and advance payment policy with parametric approach via particle swarm optimization", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "International Journal of Modelling and Simulation"}]}, {"ArticleId": 115573212, "Title": "Optimization Strategy of Cultural Legacy Tourism Design Combining Virtual Reality and Genetic Algorithm", "Abstract": "", "Keywords": "", "DOI": "10.14733/cadaps.2024.S28.196-210", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 12852, "JournalTitle": "Computer-Aided Design and Applications", "ISSN": "", "EISSN": "1686-4360", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Likelihood Ratio Based Voice Comparison Using Cepstral Coefficients and GAN", "Abstract": "In order to identify suspects in digital forensics, Forensic Voice Comparison (FVC) entails comparing trace voices with known suspect voice samples. The FVC uses semi-automatic approaches to identify speakers using acoustic features. Two Australian datasets such as forensic evaluation dataset 1 & English dataset 2 were used. The current research on FVC samples is noise-reduced using a stationary noise reduction algorithm within a likelihood ratio-based framework. To demonstrate the power of knowledge representation, acoustic features like Mel Frequency Cepstral Coefficients(MFCC) and Linear Predictive Cepstral Coefficients (LPCC) were extracted. The suspect is then identified by classifying the model using the Generative Adversarial Network(GAN) and Artificial Neural Network (ANN). The accuracy is 80% & 90% for MFCC for dataset 1 & 2. Similarly, accuracy is 20% & 40% for LPCC for dataset 1 & 2. The overall comparison of acoustic features with two different datasets the MFCC performs better accurate results for Australian English dataset 2 with varying sample sizes.", "Keywords": "Generative Adversarial Network (GAN); Acoustic Features; Mel Frequency Cepstral Coefficients (MFCC); Linear Predictive Cepstral Coefficients (LPCC); Forensic Voice Comparison (FVC); Likelihood Ratio", "DOI": "10.1016/j.procs.2024.04.287", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Kruthika S.G.", "Affiliation": "Department of Computer Science and Engineering, S.J. College of Engineering, JSS Science & Technology University, Mysore, Karnataka, India"}, {"AuthorId": 2, "Name": "Trisiladevi <PERSON>", "Affiliation": "Department of Computer Science and Engineering, S.J. College of Engineering, JSS Science & Technology University, Mysore, Karnataka, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, S.J. College of Engineering, JSS Science & Technology University, Mysore, Karnataka, India"}], "References": []}, {"ArticleId": 115573253, "Title": "Digital strategy in Brazilian family business", "Abstract": "The study aimed to analyze the effect of digitalization strategies on business performance, mediated by organizational innovation in Brazilian family Micro, Small, and Medium Enterprises (MSMEs). This research adopted a descriptive approach with a quantitative methodology, using a sample of 301 Brazilian family MSMEs. Data were collected from studies by the Foundation for Strategic Analysis and Development of Small and Medium Enterprises (FAEDPYME). The theoretical model generated four research hypotheses. Data were processed and analyzed using the structural equation method in SmartPLS 4.0 software. The results confirm the existence of a positive and significant relationship between digitalization strategies, business performance, and organizational innovation, including the mediating effect. The findings highlight the importance of digitalization strategies and organizational innovation in driving the business performance of Brazilian family MSMEs. This study contributes to strategic management practices in family businesses by identifying that effective digitalization strategies and innovation practices allow significant advancements in business performance and subsequent organizational innovation. Thus, they can guide the development of more effective business policies and practices, contributing to the competitiveness of family businesses in the contemporary market.", "Keywords": "", "DOI": "10.1002/isd2.12338", "PubYear": 2024, "Volume": "90", "Issue": "6", "JournalId": 6046, "JournalTitle": "The Electronic Journal of Information Systems in Developing Countries", "ISSN": "1681-4835", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Management University of the West of Santa Catarina  Chapecó Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Management University of the West of Santa Catarina  Chapecó Brazil;Department of Management Federal University of Fronteira Sul  Chapecó Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of the West of Santa Catarina  Chapecó Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of the West of Santa Catarina  Chapecó Brazil"}], "References": [{"Title": "Digital transformation in family-owned Mittelstand firms: A dynamic capabilities perspective", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "6", "Page": "676", "JournalTitle": "European Journal of Information Systems"}]}, {"ArticleId": 115573256, "Title": "A high-resolution hydrodynamics and sediment dataset in the coastline waters of the Northern Bay of Bengal", "Abstract": "Regional Ocean Modeling System (ROMS) - Community Sediment Transport Modeling System (CSTMS) model used to acquire a dataset of physical variables and sediment on the continental shelf of India and countries adjacent to the Northern Bay of Bengal. The high-resolution model resolved the complex bathymetry taken from ETOPO2, forced by COADS climatological winds. Furthermore, the tides are taken from TPXO7 and lateral boundaries from SODA with initial condition from WOA09 and sediment concentration set to zero in the simulation. The river discharge as the point source of nine rivers, which are in our domain was obtained from Dai and Trenberth climatology. The raw data of two alternative simulations, with river and without river was obtained. The Large-McWilliams-Doney (LMD) vertical mixing scheme was used in both scenarios for the simulations. The output data was stored in the NetCDF format for 12 months with daily data availability of two different cases: with river and without river simulation. This dataset can be accessed from the following link https://www.scidb.cn/en/detail?dataSetId=0218045285984221bef97b0834c5eaf4 .", "Keywords": "ROMS-CSTMS; Temperature-salinity; Sand; Silt; Bottom stress", "DOI": "10.1016/j.dib.2024.110577", "PubYear": 2024, "Volume": "55", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Ocean, River, Atmosphere and Land Sciences (CORAL), Indian Institute of Technology Kharagpur 721302, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Ocean, River, Atmosphere and Land Sciences (CORAL), Indian Institute of Technology Kharagpur 721302, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Geology and Geophysics (GG), Indian Institute of Technology Kharagpur 721302, India"}], "References": []}, {"ArticleId": 115573276, "Title": "A hybrid convolutional neural network and support vector machine classifier for Amharic character recognition", "Abstract": "<p>Optical character recognition is a way of converting scanned images of printed or handwritten documents into machine-encoded text, making it easier to store, browse, retrieve, and process electronic data. In this research, a Printed Amharic Characters Recognition dataset is prepared to train and test a model. Images in the dataset only contain 231 basic Amharic characters that are normalized to 32 × 32 pixels. In this work, a hybrid model of the two super classifiers is developed: the convolutional neural network (CNN) and the support vector machine (SVM). In this novel hybrid CNN-SVM model, CNN works as an automatic feature extractor from the raw images, and then, the extracted feature vectors are given as input to SVM for classification and recognition. A 99.84% accuracy was achieved on the own-prepared dataset and 95.59% accuracy on the benchmark Amharic Optical Character Recognition Database in classifying the testing dataset images. The proposed hybrid CNN-SVM model gave better results than the CNN with a fully connected layer. Moreover, the proposed model outperforms previously existing works attempted by others to recognize printed Amharic characters on the same and different datasets.</p>", "Keywords": "Amharic character recognition; Convolution neural network (CNN); Support vector machine (SVM); CNN-SVM model", "DOI": "10.1007/s00521-024-09657-3", "PubYear": 2024, "Volume": "36", "Issue": "27", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, Andhra University College of Engineering, Visakhapatnam, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Andhra University College of Engineering, Visakhapatnam, India"}], "References": [{"Title": "Convolutional neural network: a review of models, methodologies and applications to object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "2", "Page": "85", "JournalTitle": "Progress in Artificial Intelligence"}, {"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A comprehensive survey on convolutional neural network in medical image analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "29", "Page": "41361", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Intelligent handwritten recognition using hybrid CNN architectures based-SVM classifier with dropout", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "6", "Page": "3294", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A comprehensive survey on deep learning based malware detection techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "47", "Issue": "", "Page": "100529", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 115573424, "Title": "EFL Students’ Views about Distance Education at Higher Education in Turkey", "Abstract": "<p>This study was carried out to look into the preferences and opinions of English preparatory students about distance education. These students, after completing their first semester of instruction in person, had their second semester of instruction online as a result of the two earthquakes of magnitude 7.7 and 7.6 which struck Kahramanmaraş in Türkiye in 2023. A Google Form questionnaire with both open-ended and closed-ended questions was administered to 55 students in the preparatory class. According to the study, the advantages of distance learning for EFL students are that it is a convenient and affordable form of education, while the disadvantages involve technical issues, a lack of interaction between teachers and students, a lack of a conducive environment for instruction, and health issues brought on by distance learning. The study also reveals that while 72% of the preparatory students preferred face-to-face instruction, 28% favoured the distance education model. The study's final finding is that 58% of the participants, EFL preparatory students, believe that 30-minute is the right length for a distance learning course. These important results will contribute to a better understanding of the distance education model, which is frequently offered as a substitute for in-person instruction.</p>", "Keywords": "", "DOI": "10.5824/ajite.2024.02.001.x", "PubYear": 2024, "Volume": "15", "Issue": "2", "JournalId": 36830, "JournalTitle": "AJIT-e: Online Academic Journal of Information Technology", "ISSN": "", "EISSN": "1309-1581", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573450, "Title": "Information Security based on IoT for e-Health Care Using RFID Technology and Steganography", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijitcs.2024.03.03", "PubYear": 2024, "Volume": "16", "Issue": "3", "JournalId": 8584, "JournalTitle": "International Journal of Information Technology and Computer Science", "ISSN": "2074-9007", "EISSN": "2074-9015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "K.L.E. College of Engineering & Technology/Department of Computer Science and Engineering, Chikodi, 591201, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573484, "Title": "A Novel Privacy Preservation Scheme by Matrix Factorized Deep Autoencoder", "Abstract": "", "Keywords": "", "DOI": "10.5815/ijcnis.2024.03.07", "PubYear": 2024, "Volume": "16", "Issue": "3", "JournalId": 20260, "JournalTitle": "International Journal of Computer Network and Information Security", "ISSN": "2074-9090", "EISSN": "2074-9104", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Application, Kurukshetra University, Kurukshetra, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573490, "Title": "Content Based Image Retrieval System Using CNN based Deep Learning Models", "Abstract": "Digital information search archives on the internet and other platforms have been an essential part of our daily lives. With the use of huge amounts of digital images, it is paramount to develop efficient solutions to retrieve images seamlessly. Due to inadequate textual description, the task of automated image retrieval is quite complicated. Therefore, retrieval of images by analysing their visual content is an exciting task and research-worthy. Text based Image Search (TIBR) is time consuming as initially, every image has to be linked with a keyword or text to be extracted on query search. In the proposed system, an efficient algorithm for Content Based Image Retrieval (CBIR) using pre-trained CNN-based Deep Learning models to extract deep features of an image has been developed, which significantly increases the performance of the image retrieval process. Convolutional Neural Networks (CNN) has evolved as an efficient deep learning solution for the CBIR systems that work in image recognition applications. The results of retrieval are evaluated in terms of precision performance parameters. Emphasis has been given to the comparison of the efficiency of the system using two different pre-trained CNN-based Deep Learning models based on VGG16 and ResNet-50 architecture. Transfer learning enables us to develop robust and efficient working systems using pre-trained models. In the proposed system, pre-trained models have been used to derive features using a deep learning CNN network trained for a large image classification dataset ImageNet. This approach outperforms many contemporary CBIR systems. As most CBIR systems depend on query images and as most users of such systems are non-professional, we have developed a user interface for the built prototype systems. The efficiency of the CBIR systems with pre-trained models VGG16, and ResNet-50 are compared for better image retrieval and reliability. The proposed system has addressed a few existing problems in CBIR system design and developed a suitable solution with less semantic gap and better efficiency in image retrieval.", "Keywords": "CBIR; Transfer Learning; VGG16; ResNet-50", "DOI": "10.1016/j.procs.2024.04.296", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Dept. Of Electronics and Communication Engineering, Guru <PERSON>ishwavidyalaya, Koni, Bilaspur 495009, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Dept. Of Electronics and Communication Engineering, Guru <PERSON>ishwavidyalaya, Koni, Bilaspur 495009, India"}], "References": [{"Title": "An efficient bi-layer content based image retrieval system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17731", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Two-layer content-based image retrieval technique for improving effectiveness", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "20", "Page": "31423", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 115573628, "Title": "Full-waveform hyperspectral LiDAR data decomposition via ranking central locations of natural target echoes (Rclonte) at different wavelengths", "Abstract": "The novel hyperspectral LiDAR (HSL) system exhibits the aptitude to simultaneously capture both spectral and geometric information from the hyperspectral waveform data. However, conventional single-wavelength decomposition methods may not be compatible with HSL waveforms due to higher levels of unstable noise, more complex waveform shapes, and inconsistent time delay effects at different wavelengths within the hyperspectral waveforms. These limitations pose significant challenges for quantitative applications of the HSL system. To overcome these issues, an imperative and pressing need is to search for a suitable waveform processing algorithm for the HSL system. Therefore, we propose a novel method called Ranking Central Locations of Natural Target Echoes (Rclonte) to decompose full-waveform hyperspectral LiDAR data. The Rclonte introduces a new parameter initialization strategy that includes rough estimation and refined estimation steps, preventing the optimization process from being trapped in a local optimum state. Subsequently, a re-optimization step over ranking central locations of natural target echoes at different wavelengths compensates for the missing detection or false detection of hidden weak and overlapping components within the waveform at some wavelengths. Two data collections, including the synthetic and measured HSL waveform data, were employed in the decomposition. The results indicate that (1) <PERSON><PERSON>lonte detected components and parameters much more accurately with the highest R<sup>2</sup> and the lowest RMSE and rRMSE values, outperforming the Hofton GD and MSWD methods. (2) Both the synthetic and measured data decomposition results highlight the effectiveness and the apparent superiority of <PERSON><PERSON><PERSON><PERSON> over Hofton GD and MSWD regarding compensating for the hidden weak or overlapping components. (3) The ranging results indicate that Rclonte achieves the highest ranging precision with low relative neighbor distance error (RNDE) (0.026∼0.085) for the measured data. (4) The spectra derived from Rclonte are superior to Hofton GD and MSWD methods. The smoothed version of the retrieved spectrum using Rclonte decomposition results presents a spectral similarity to the HSL-measured reflectance spectrum of a single leaf. The proposed method comprehensively utilizes the invariance of the central location orders of multiple targets at different wavelengths to ensure accurate detection. It not only facilitates the development of decomposition algorithms for full-waveform hyperspectral LiDAR data but also holds promise for adoption in other full-waveform multispectral LiDAR (MSL) and HSL systems.", "Keywords": "", "DOI": "10.1016/j.rse.2024.114227", "PubYear": 2024, "Volume": "310", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China;University of Chinese Academy of Sciences, Beijing 100049, China;Corresponding author at: State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing 100049, China;Key Laboratory of Digital Earth Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100094, China"}, {"AuthorId": 4, "Name": "Kaiyi B<PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China;University of Chinese Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China;Corresponding author"}], "References": [{"Title": "Individual tree crown segmentation from airborne LiDAR data using a novel Gaussian filter and energy function minimization-based approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "256", "Issue": "", "Page": "112307", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Design of supercontinuum laser hyperspectral light detection and ranging (LiDAR) (SCLaHS LiDAR)", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "42", "Issue": "10", "Page": "3731", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "A CNN-based approach for the estimation of canopy heights and wood volume from GEDI waveforms", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "265", "Issue": "", "Page": "112652", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Direct use of large-footprint lidar waveforms to estimate aboveground biomass", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113147", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 115573660, "Title": "An Online Home Energy Management System Using Q-Learning and Deep Q-Learning", "Abstract": "The users of home energy management systems schedule their real-time energy consumption thanks to advancements in communication technology and smart metering infrastructures. In this paper, a data-driven strategy is proposed, which is an Online Home Energy Management System (ON-HEM) that uses reinforcement learning algorithms (Q-Learning and Deep Q-Learning) to control the optimal energy consumption of a smart home system. The proposed system comprises power resources (grid, photovoltaic), communication networks, and appliances with their agents classified into four groups: deferrable, non-deferrable, power level controllable, and electric vehicle. The system reduces electricity costs and high peak demands while considering the cost of user dissatisfaction with real-life data. Simulations are performed on the proposed ON-HEM considering different pricing approaches (Real Time Pricing and Time of Use Pricing) with Q-Learning and Deep Q-Learning (DQL) algorithms using PyCharm Professional Edition software. The findings demonstrate both the superiority of DQL over Q-Learning and the efficiency of the proposed ON-HEM in decreasing high peak demand, electricity costs, and customer dissatisfaction costs. The efficiency and dependability of the proposed system were verified by utilizing simulation-based findings with real-life data using IBM SPSS Statistics software.", "Keywords": "", "DOI": "10.1016/j.suscom.2024.101005", "PubYear": 2024, "Volume": "43", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electrical and Electronics Engineering, Eskişehir Osmangazi University, Eskişehir, 26040, Turkiye;R&D Department, Tusas Engine Industries, Inc., Eskişehir, 26210, Turkiye;Corresponding author at: Electrical and Electronics Engineering, Eskişehir Osmangazi University, Eskişehir, 26040, Turkiye"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>mancıoğ<PERSON>", "Affiliation": "Electrical and Electronics Engineering, Eskişehir Osmangazi University, Eskişehir, 26040, Turkiye"}], "References": []}, {"ArticleId": 115573695, "Title": "Application of Decision Support System for Selection of Residential Criteria using the fuzzy Method in Majene Regency", "Abstract": "<p>In this digital era, information technology is growing rapidly so that it is used to market various things, including homes. There is a lot of information technology about houses being marketed, but it takes a long time to collect information and compare one house to another. However, some of the housing marketed no longer prioritizes comfort, but rather cheap or economical prices. Due to the fact that sometimes unclear data is needed to solve problems, the fuzzy method is a decision-making approach that uses standard relationships but applies fuzzy set theory to the database. Decision making regarding the selection of comfortable housing criteria according to consumers is carried out using the fuzzy Tahani model approach. The fuzzy holdi technique uses house data that has been processed to produce output in the form of house data that is recommended for customers.</p>", "Keywords": "<PERSON><PERSON>;<PERSON><PERSON><PERSON><PERSON>;Sistem Pendukung Keput<PERSON>n", "DOI": "10.35143/jkt.v10i1.5870", "PubYear": 2024, "Volume": "10", "Issue": "1", "JournalId": 67706, "JournalTitle": "Jurnal Komputer Terapan", "ISSN": "2443-4159", "EISSN": "2460-5255", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> <PERSON>", "Affiliation": "Universitas Sulawesi Barat"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universitas Sulawesi Barat"}], "References": []}, {"ArticleId": 115573714, "Title": "BrainNet: A Deep Learning Approach for Brain Tumor Classification", "Abstract": "Cancer, regardless of its type, represents a formidable threat to human life and disrupts the delicate balance of normal bodily functions. Among the various forms of cancer, malignant brain tumors stand out as a leading cause of mortality in both adult and pediatric populations. The timely identification of these brain tumors is crucial for attaining precise diagnoses. Brain tumour identification and diagnosis are now made possible by the use of magnetic resonance imaging (MRI). However, the intricate and irregular shapes and locations of these tumors often pose challenges for complete comprehension. Typically, the expertise of neurosurgical specialists is required for the precise analysis of MRI scans. Unfortunately, in many developing countries, a shortage of skilled medical professionals and limited awareness about brain tumors compound the difficulties associated with obtaining timely and accurate MRI results. To address these notable challenges, this research introduces BrainNet, an innovative Convolutional Neural Network (CNN) architecture specifically designed for the classification of brain tumors into distinct categories. The established transfer learning models VGG13, VGG19, VGG16, InceptionResV2, and Squeeznet, all of which were pretrained on the Imagenet dataset, are outperformed by BrainNet in both how well it handles these problems and how well it outperforms them. The performance of the BrainNet CNN architecture is particularly impressive, with a precision score of 94.75 percent and accuracy rates of 99.96 percent during training and 97.71 percent during testing. This accomplishment has the potential to significantly improve brain tumour diagnosis and classification, particularly in areas with limited access to medical resources and knowledge.", "Keywords": "Brain tumor; Deep Learning; Transfer Learning; Brain MRI scans; Convolution Neural Network", "DOI": "10.1016/j.procs.2024.04.310", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Amrita School of Computing, Bengaluru, Amrita Vishwa Vidyapeetham, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Alliance University, Bengaluru, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, Alliance University, Bengaluru, India"}, {"AuthorId": 4, "Name": "Atharwa Wagh", "Affiliation": "Department of Computer Science and Engineering, Amrita School of Computing, Bengaluru, Amrita Vishwa Vidyapeetham, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Amrita School of Computing, Bengaluru, Amrita Vishwa Vidyapeetham, India"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Wisconsin-Green Bay Wisconsin, USA"}], "References": [{"Title": "Robust Magnification Independent Colon Biopsy Grading System over Multiple Data Sources", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "1", "Page": "99", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Stacking approach for accurate Invasive Ductal Carcinoma classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "100", "Issue": "", "Page": "107937", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 115573721, "Title": "An Intelligent and Deep Learning Approach for Pothole Surveillance Smart Application", "Abstract": "The frequency of potholes and speed bumps on the roads has linearly increased due to ageing, inadequate maintenance, and a growth in the number of vehicles. We have discussed a prototype for the identification of potholes and speed bumps in order to guarantee safe mobility. Such critical road quality data may be made public or shared with local governments and other government agencies for the purpose of corrective action and road maintenance. A pothole detection system using image processing and cloud storage can be used to detect potholes from footage provided by a device, store the information into a cloud database, and analyze the data to identify areas that need repair. The system can also send alerts to users about upcoming potholes and notify road maintenance authorities of new pothole detections. In brief, the system uses image processing to detect potholes and cloud storage to store and analyze the data. It can also send alerts to users and road maintenance authorities. The model was built using YOLOv5 and trained on 70% of the dataset images. It was able to detect potholes with good accuracy in the output images.", "Keywords": "Pothole; Roboflow; YOLOv5; UAV; Tflite; Image Processing; Cloud", "DOI": "10.1016/j.procs.2024.04.309", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of SCET, MIT WPU, Pune 411038"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of SCET, MIT WPU, Pune 411038, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of SCET, MIT WPU, Pune 411038"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of SCET, MIT WPU, Pune 411038"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of SCET, MIT WPU, Pune 411038"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of SCET, MIT WPU, Pune 411038, India"}], "References": [{"Title": "Feature Vector Creation Using Hierarchical Data Structure for Spatial Domain Image Retrieval", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2458", "JournalTitle": "Procedia Computer Science"}, {"Title": "Early detection of Parkinson's disease using machine learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "218", "Issue": "", "Page": "249", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 115573729, "Title": "Enhanced U-Net Architecture for Lung Segmentation on Computed Tomography and X-Ray Images", "Abstract": "In the expanding field of medical imaging, precise segmentation of anatomical structures is critical for accurate diagnosis and therapeutic interventions. This research paper introduces an innovative approach, building upon the established U-Net architecture, to enhance lung segmentation techniques applied to Computed Tomography (CT) images. Traditional methods of lung segmentation in CT scans often confront challenges such as heterogeneous tissue densities, variability in human anatomy, and pathological alterations, necessitating an approach that embodies greater robustness and precision. Our study presents a modified U-Net model, characterized by an integration of advanced convolutional layers and innovative skip connections, improving the reception field and facilitating the retention of high-frequency details essential for capturing the lung's intricate structures. The enhanced U-Net architecture demonstrates substantial improvements in dealing with the subtleties of lung parenchyma, effectively distinguishing between precarious nuances of tissues, and pathologies. Rigorous quantitative evaluations showcase a significant increase in the Dice coefficient and a decrease in the Hausdorff distance, indicating a more refined segmentation output compared to predecessor models. Additionally, the proposed model manifests exceptional versatility and computational efficiency, making it conducive for real-time clinical applications. This research underlines the transformative potential of employing advanced deep learning architectures for biomedical imaging, paving the way for early intervention, accurate diagnosis, and personalized treatment paradigms in pulmonary disorders. The findings have profound implications, propelling forward the nexus of artificial intelligence and healthcare towards unprecedented horizons.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150594", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573816, "Title": "Heart Sound Classification using a Hybrid of CNN and GRU Deep Learning Models", "Abstract": "Auscultation is a process where a stethoscope is used to listen to the heart sound signal to analyse the heart’s functionality. Due to the stethoscope’s non-invasiveness, convenience, and cost-effectiveness, it is the most common primary screening tool medical fraternities use. However, the scarcity of medical experts and the subjectivity in the analysis hinders the reliability of diagnosis using auscultation. Therefore, computer-aided analysis of heart sound signals will be helpful in this scenario. This paper presents a hybrid deep learning-based method to classify the heart sound signal into five classes. The method begins with the signal pre-processing followed by decomposition using Discrete Wavelet Transform (DWT) up to five levels. The obtained DWT coefficients are used to train the hybrid model, composed of two Convolution neural network (CNN) layers following one Gated Recurrent Unit (GRU) network layer. CNN models are suitable for extracting meaningful features, while the GRU exploits the time-dependent features. This combination helps classify the heart sound signal since they exhibit complex quasi-cyclic features. An overall accuracy of 99.3% is obtained for a publicly available dataset. It shows the proposed method’s efficacy for classifying heart sound signal and superiority over the existing methods. Such a method will be beneficial in reducing the burden of heart valve diseases by early detection of diseases and initiating the proper medication.", "Keywords": "Heart Sound Signal; Phonocardiogram; Discrete Wavelet Transform; Gated Recurrent Unit; Convolution Neural Network", "DOI": "10.1016/j.procs.2024.04.292", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science, Central University of Rajasthan, Rajasthan, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science, Central University of Rajasthan, Rajasthan, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, NIT Rourkela, Odisha, India"}], "References": [{"Title": "RETRACTED ARTICLE: Classification of unsegmented phonocardiogram signal using scalogram and deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "17", "Page": "12677", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 115573826, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0034-4257(24)00254-2", "PubYear": 2024, "Volume": "309", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [], "References": []}, {"ArticleId": 115573840, "Title": "In pursuit of the agile organisation: a review and framework development on assessing organisational agility", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASM.2024.138846", "PubYear": 2024, "Volume": "17", "Issue": "2", "JournalId": 10974, "JournalTitle": "International Journal of Agile Systems and Management", "ISSN": "1741-9174", "EISSN": "1741-9182", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Paulo <PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Lígia Conceiç�ã", "Affiliation": ""}, {"AuthorId": 5, "Name": "N.A. o", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "N.A<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573921, "Title": "Non intrusive load monitoring using additive time series modeling via finite mixture models aggregation", "Abstract": "<p>Energy disaggregation, or Non-Intrusive Load Monitoring (NILM), involves different methods aiming to distinguish the individual contribution of appliances, given the aggregated power signal. In this paper, the application of finite Generalized Gaussian and finite Gamma mixtures in energy disaggregation is proposed and investigated. The procedure includes approximation of the distribution of the sum of two Generalized Gaussian random variables (RVs) and the approximation of the distribution of the sum of two Gamma RVs using Method-of-Moments matching. By adopting this procedure, the probability distribution of each combination of appliances consumption is acquired to predict and disaggregate the specific device data from the aggregated data. Moreover, to make the models more practical we propose a deep version, that we call DNN-Mixture, as a cascade model, which is a combination of a deep neural network and each of the proposed mixture models. As part of our extensive evaluation process, we apply the proposed models on three different datasets, from different geographical locations, that had different sampling rates. The results indicate the superiority of proposed models as compared to the Gaussian mixture model and other widely used approaches. In order to investigate the applicability of our models in challenging unsupervised settings, we tested them on unseen houses with unlabeled data. The outcomes proved the extensibility and robustness of the proposed approach. Finally, the evaluation of the cascade model against the state of the art shows that by benefiting from the advantages of both neural networks and finite mixtures, cascade model can produce promising and competing results with RNN without suffering from its inherent disadvantages.</p>", "Keywords": "Energy disaggregation; NILM; Mixture models; Generalized Gaussian; Gamma; Deep learning; RNN", "DOI": "10.1007/s12652-024-04814-x", "PubYear": 2024, "Volume": "15", "Issue": "9", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "Soudabeh Tabarsaii", "Affiliation": "Concordia Institute for Information Systems Engineering (CIISE), Concordia University, Montreal, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Concordia Institute for Information Systems Engineering (CIISE), Concordia University, Montreal, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Concordia Institute for Information Systems Engineering (CIISE), Concordia University, Montreal, Canada; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Building, Civil and Environmental Engineering (BCEE), Concordia University, Montreal, Canada"}], "References": [{"Title": "Explainable K-Means Clustering for Occupancy Estimation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "203", "Issue": "", "Page": "326", "JournalTitle": "Procedia Computer Science"}, {"Title": "ICA and IVA bounded multivariate generalized Gaussian mixture based hidden Markov models", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106345", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Adventures in data analysis: a systematic review of Deep Learning techniques for pattern recognition in cyber-physical-social systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "8", "Page": "22909", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 115573935, "Title": "A Smart System Facilitating Emotional Regulation in Neurodivergent Children", "Abstract": "This paper acknowledges the need for a user-centric solution that helps with emotional regulation and stress management in children with ADHD. The paper presents a unique and comprehensive solution that integrates Reinforcement Learning (RL) algorithms to enhance user experience and aid children with ADHD to regulate their emotions and behaviours through a reward-based system. Through careful analysis of existing literature, and user requirements assessment, a comprehensive framework that integrates machine learning algorithms, physical and digital solution components through a user-centric design approach has been proposed. The core objective is to design and develop a sensory regulation system specifically tailored to the requirements of children with ADHD. Through the development of an engaging and impactful sensory regulation system, children can experience social and academic aspects of school positively while also having the opportunity to expand their social circle through inclusive play environments and ultimately improving their daily experiences. This paper aims to address the imminent need for emotional regulation and stress management tools catering to children with ADHD. By incorporating Reinforcement Learning (RL) algorithms with a reward-based interaction, this paper aims to solve critical challenges faced by children with ADHD, like emotional regulation difficulties, stress management, poor social skills, and academic performance issues so that they can lead more holistic lives.", "Keywords": "ADHD; children; Reinforcement learnin; fidget toy; emotional regulation; neurodivergence; reward system", "DOI": "10.1016/j.procs.2024.04.308", "PubYear": 2024, "Volume": "235", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PES University, Bengaluru, Karnataka, 560085, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Product Design and Manufacturing, Indian Institute of Science, Bengaluru, 560012, India"}], "References": [{"Title": "Empowering soft skills in children with ADHD through the co-creation of tangible tabletop games", "Authors": "<PERSON>; <PERSON><PERSON>-<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "1", "Page": "3", "JournalTitle": "Universal Access in the Information Society"}]}, {"ArticleId": 115573938, "Title": "Image Segmentation in Complex Backgrounds using an Improved Generative Adversarial Network", "Abstract": "As technology advances, solving image segmentation challenges in complex backgrounds has become a key issue across various fields. Traditional image segmentation methods underperform in addressing these challenges, and existing generative adversarial networks (GANs) also face several problems when applied in complex environments, such as low generation quality and unstable model training. To address these issues, this study introduces an improved GAN approach for image segmentation in complex backgrounds. This method encompasses preprocessing of complex background image datasets, feature reduction encoding based on cerebellar neural networks, image data augmentation in complex backgrounds, and the application of an improved GAN. In this paper, new generator and discriminator network structures are designed and image data enhancement is implemented through self-play learning. Experimental results demonstrate significant improvements in image segmentation tasks in various complex backgrounds, enhancing the accuracy and robustness of segmentation. This research offers new insights and methodologies for image processing in complex backgrounds, holding substantial theoretical and practical significance.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150543", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573950, "Title": "Tendon-Driven Robotic Arm Control Method Based on Radial Basis Function Adaptive Tracking Algorithm", "Abstract": "With the rapid development of intelligent technology, robotic arms are widely used in different fields. The study combines the tendon drive theory and radial basis function neural network to construct the robotic arm model, and then combines the back-stepping method and non-singular fast terminal sliding mode to improve the controller and system optimization of the tendon drive robotic arm model. Simulation tests on commercial mathematical software platforms yielded that joint 2 achieves stable overlap of position trajectory and velocity trajectory after 0.2s and 0.5s with errors of 1° and 1°/s, respectively. Radial basis function neural network approximation of robotic arm error converged to the true value at 14s. The optimized joint achieved the accuracy of trajectory tracking after 0.2s. Also the control torque of joint 2 changes at 1.5s, 4.5s and 8s and its change is small. The tendon tension curve was smoother and more stable in the range of -0.05N~0.0.5N to show that the robotic arm model has superiority after the optimization of the controller, and the interference observer had accurate estimation of the tracking trajectory of the tendon-driven robotic arm. Therefore, the radial basis function-based adaptive tracking algorithm had higher accuracy for the tendon-driven robotic arm model and provided technical reference for the control system of the intelligent robotic arm.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150508", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115573964, "Title": "AN ANTIPODAL THEOREM FOR PARAMETRIC OPTIMIZATION PROBLEMS", "Abstract": "", "Keywords": "", "DOI": "10.5109/7178785", "PubYear": 2024, "Volume": "56", "Issue": "4", "JournalId": 74991, "JournalTitle": "Bulletin of informatics and cybernetics", "ISSN": "0286-522X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Hidefumi Kawasaki", "Affiliation": "Kyushu University"}], "References": []}, {"ArticleId": 115574018, "Title": "Active convolutional neural networks sign language (ActiveCNN-SL) framework: a paradigm shift in deaf-mute communication", "Abstract": "<p>Real-time speech-to-text and text-to-speech technologies have significantly influenced the accessibility of communication for individuals who are deaf or mute. This research aims to assess the efficacy of these technologies in facilitating communication between deaf or mute individuals and those who are neither deaf nor mute. A mixed-method approach will incorporate qualitative and quantitative data collection and analysis techniques. The study will involve participants from deaf or mute and non-deaf or non-mute communities. The research will scrutinize the precision and efficiency of communication using these technologies and evaluate user experience and satisfaction. Furthermore, the study intends to pinpoint potential obstacles and limitations of these technologies and offer suggestions for enhancing their effectiveness in fostering inclusivity. The study proposes an active learning framework for sign language gesture recognition, termed Active Convolutional Neural Networks—Sign Language (ActiveCNN-SL). ActiveCNN-SL aims to minimize the labeled data required for training and augment the accuracy of sign language gesture recognition through iterative human feedback. This proposed framework holds the potential to enhance communication accessibility for deaf and mute individuals and encourage inclusivity across various environments. The proposed framework is trained using two primary datasets: (i) the Sign Language Gesture Images Dataset and (ii) the American Sign Language Letters (ASL)—v1. The framework employs Resnet50 and YoloV.8 to train the datasets. It has demonstrated high performance in terms of precision and accuracy. The ResNet model achieved a remarkable accuracy rate of 99.98% during training, and it also exhibited a validation accuracy of 100%, surpassing the baseline CNN and RNN models. The YOLOv8 model outperformed previous methods on the ASL alphabet dataset, achieving an overall mean average accuracy for all classes of 97.8%.</p>", "Keywords": "Communication Accessibility and Inclusivity; Deaf-Mute Communication; Deep Learning; Real-time speech-to-text; Quality of life for the deaf/Mute", "DOI": "10.1007/s10462-024-10792-5", "PubYear": 2024, "Volume": "57", "Issue": "6", "JournalId": 4759, "JournalTitle": "Artificial Intelligence Review", "ISSN": "0269-2821", "EISSN": "1573-7462", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Computers and Control Systems Engineering Department, Faculty of Engineering, Mansoura University, Mansoura, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Nursing Management and Education Department, College of Nursing, Princess <PERSON><PERSON><PERSON> University, Riyadh, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Electronics & Communication Engineering Department, Faculty of Engineering, Horus University, New Damietta, Egypt"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Taibah University, Yanbu, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Artificial Intelligence, Kafrelsheikh University, Kafrelsheikh, Egypt; Faculty of Computer Science & Engineering, New Mansoura University, Gamasa, Egypt; Nile Higher Institute for Engineering and Technology, Mansoura, Egypt"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Taibah University, Yanbu, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computers and Control Systems Engineering Department, Faculty of Engineering, Mansoura University, Mansoura, Egypt; Computer Science and Information Department, Applied College, Taibah University, Madinah, Saudi Arabia"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Taibah University, Yanbu, Saudi Arabia; Computers and Control Systems Engineering Department, Faculty of Engineering, Mansoura University, Mansoura, Egypt"}], "References": [{"Title": "Research on gesture recognition of smart data fusion features in the IoT", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "22", "Page": "16917", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Hand Gesture Recognition using Image Processing and Feature Extraction Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "173", "Issue": "", "Page": "181", "JournalTitle": "Procedia Computer Science"}, {"Title": "CNN based feature extraction and classification for sign language", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "2", "Page": "3051", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Gesture recognition based on multi‐modal feature weight", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "5", "Page": "e5991", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Occlusion gesture recognition based on improved SSD", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "e6063", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "American sign language recognition and training method with recurrent neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114403", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Indian Sign Language recognition system using SURF with SVM and CNN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "", "Page": "100141", "JournalTitle": "Array"}, {"Title": "Gesture recognition from RGB images using convolutional neural network‐attention based system", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "24", "Page": "e7230", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Static hand gesture recognition in sign language based on convolutional neural network with feature extraction method using ORB descriptor and Gabor filter", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118559", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sign language recognition system for communicating to people with disabilities", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "13", "JournalTitle": "Procedia Computer Science"}, {"Title": "Increasing Crop Quality and Yield with a Machine Learning-Based Crop Monitoring System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "2", "Page": "2401", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 115574038, "Title": "Fusion Lightweight Steel Surface Defect Detection Algorithm Based on Improved Deep Learning", "Abstract": "In industrial production, timely and accurate detection and identification of surface defects in steel materials were crucial for ensuring product quality, enhancing production efficiency, and reducing production costs. This study addressed the problem of surface defect detection in steel materials by proposing an algorithm based on an improved version of YOLOv5. The algorithm achieved lightweight and high efficiency by incorporating the MobileNet series network. Experimental results demonstrated that the improved algorithm significantly reduced inference time and model file size while maintaining performance. Specifically, the YOLOv5-MobileNet-Small model exhibited slightly lower performance but excelled in inference time and model file size. On the other hand, the YOLOv5-MobileNet-Large model achieved a slight performance improvement while significantly reducing inference time and model file size. These results indicated that the improved algorithm could achieve lightweighting while maintaining performance, showing promising applications in steel surface defect detection tasks. It provided an efficient and feasible solution for this important domain, offering new insights and methods for similar surface defect detection problems and contributing to research and applications in related fields.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.0150537", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "HongSheng Li", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115574039, "Title": "Improved SegNet with Hybrid Classifier for Lung Cancer Segmentation and Classification", "Abstract": "Prompt diagnosis is crucial globally to save lives, underscoring the urgent need in light of lung cancer's status as a leading cause of death. While CT scans serve as a primary imaging tool for LC detection, manual analysis is laborious and prone to inaccuracies. Recognizing these challenges, computational techniques, particularly ML and DL algorithms, are being increasingly explored as efficient alternatives to enhance the precise identification of cancerous and non-cancerous regions within CT scans, aiming to expedite diagnosis and mitigate errors. The proposed model employs Preprocessing to standardize image features, followed by segmentation using an Improved SegNet framework to delineate cancerous regions. Features like LGXP and MBP are then extracted, facilitating classification with a hybrid classifier which combines LSTM and LinkNet models. Implemented in Python, the model's performance is evaluated against conventional methods, showcasing superior accuracy, sensitivity, and precision. This framework promises to revolutionize LC diagnosis, enabling early intervention and improved patient outcomes.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505106", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115574042, "Title": "Inclusive Smart Cities: IoT-Cloud Solutions for Enhanced Energy Analytics and Safety", "Abstract": "Securing smart cities in the evolving Internet of Things (IoT) demands innovative security solutions that extend beyond conventional theft detection. This study introduces temporal convolutional networks and gated recurrent units (TCGR), a pioneering model tailored for the dynamic IoT-SM dataset, addressing eight distinct forms of theft. In contrast to conventional techniques, TCGR utilizes Jaya tuning (TCGRJ), ensuring improved accuracy and computational efficiency. The technique employs ResNeXt for feature extraction to extract important patterns from IoT device-generated data and Edited Nearest Neighbors for data balancing. Empirical evaluations validate TCGRJ’s greater precision (96.7%) and accuracy (97.1%) in detecting theft. The model significantly aids in preventing theft-related risks and is designed for real-time Internet of Things applications in smart cities, aligning with the broader goal of creating safer spaces by reducing hazards associated with unauthorized electrical connections. TCGRJ promotes sustainable energy practices that benefit every resident, particularly those with disabilities, by discouraging theft and encouraging economical power consumption. This research underscores the crucial role of advanced theft detection technologies in developing smart cities that prioritize inclusivity, accessibility, and an enhanced quality of life for all individuals, including those with disabilities.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505128", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Faisal S. Alsubaei", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115574056, "Title": "Advancements in Federated Learning: Models, Methods, and Privacy", "Abstract": "<p>Federated learning (FL) is a promising technique for resolving the rising privacy and security concerns. Its main ingredient is to cooperatively learn the model among the distributed clients without uploading any sensitive data. In this paper, we conducted a thorough review of the related works, following the development context and deeply mining the key technologies behind FL from the perspectives of theory and application. Specifically, we first classify the existing works in FL architecture based on the network topology of FL systems with detailed analysis and summarization. Next, we abstract the current application problems, summarize the general techniques and frame the application problems into the general paradigm of FL base models. Moreover, we provide our proposed solutions for model training via FL. We have summarized and analyzed the existing FedOpt algorithms, and deeply revealed the algorithmic development principles of many first-order algorithms in depth, proposing a more generalized algorithm design framework. With the instantiation of these frameworks, FedOpt algorithms can be simply developed. As privacy and security is the fundamental requirement in FL, we provide the existing attack scenarios and the defense methods. To the best of our knowledge, we are among the first tier to review the theoretical methodology and propose our strategies since there are very few works surveying the theoretical approaches. Our survey targets motivating the development of high-performance, privacy-preserving, and secure methods to integrate FL into real-world applications.</p>", "Keywords": "", "DOI": "10.1145/3664650", "PubYear": 2025, "Volume": "57", "Issue": "2", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Chen", "Affiliation": "Tsinghua University,  Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University,  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University,  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tsinghua University,  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Tsinghua University,  Beijing China"}], "References": [{"Title": "A survey on security and privacy of federated learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "", "Page": "619", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Industrial Federated Topic Modeling", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Privacy-Preserving Individual-Level COVID-19 Infection Prediction via Federated Graph Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}]}, {"ArticleId": 115574131, "Title": "Spectral Efficiency Improvement in Cooperative RIS Aided OFDM-IM system with Hybrid Indexing", "Abstract": "Increasing demand for wireless communication leads to significant challenges in achieving high spectral efficiency (SE) and energy efficiency (EE). Orthogonal Frequency Division Multiplexing with Index Modulation (OFDM-IM) is one of the considerations, although it has the inherent disadvantage of a trade-off between SE and EE. In this work, frequency-dependent control on reconfigurable intelligent surfaces (RIS) and indexing to activate or deactivate the elements of RIS have been proposed to improve the SE of OFDM-IM systems. The Upper bound pairwise error probability has been derived and confirmed by simulation. The proposed work enhances the SE and EE compared to the conventional OFDM-IM systems. For instance, SE loss of 0.5 b p c u \" role=\"presentation\" style=\"font-size: 90%; display: inline-block; position: relative;\"> 0.5 b p c u 0.5 b p c u in an OFDM-IM system with quadrature phase shift keying (QPSK) compared to an OFDM system has been compensated by the proposed work by maintaining EE.", "Keywords": "", "DOI": "10.1016/j.phycom.2024.102398", "PubYear": 2024, "Volume": "66", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department Of ECE, College of Engineering, Anna University, Chennai, Tamil Nadu, India;Corresponding author"}, {"AuthorId": 2, "Name": "Laxmikandan T", "Affiliation": "Department Of ECE, College of Engineering, Anna University, Chennai, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "Manimekalai T", "Affiliation": "Department Of ECE, College of Engineering, Anna University, Chennai, Tamil Nadu, India"}], "References": [{"Title": "Adaptive bit and power allocation for dual mode index modulation based OFDM system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "40", "Issue": "", "Page": "101093", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 115574159, "Title": "Organisational agility as a competitive advantage: a systematic literature review", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJASM.2024.138804", "PubYear": 2024, "Volume": "17", "Issue": "2", "JournalId": 10974, "JournalTitle": "International Journal of Agile Systems and Management", "ISSN": "1741-9174", "EISSN": "1741-9182", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON> d", "Affiliation": ""}, {"AuthorId": 6, "Name": "a <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 115574226, "Title": "Identifying Competition Characteristics of Athletes Through Video Analysis", "Abstract": "The vast repositories of training and competition video data serve as indispensable resources for athlete training and competitor analysis, providing a solid foundation for strategic competition analysis and tactics formulation. However, the effectiveness of these analyses hinges on the abundance and precision of data, often requiring costly professional systems for existing video analysis techniques. Meanwhile, readily accessible non-professional data frequently lacks standardization, compelling manual analysis and experiential judgments, thus limiting the widespread adoption of video analysis technologies. To address these challenges, we have devised an intelligent video analysis technology and a methodology for identifying athletes' competition characteristics. Initially, we employed target detection models, such as You Only Look Once (YOLO), renowned for their ease of deployment and low environmental dependency, to perform fundamental detection tasks. This was further complemented by the intelligent selection of standardized scenes through customizable scene rules, leading to the formation of a standardized scene dataset. On this robust foundation, we achieved classification and identification of competition participants as well as sideline recognition, ultimately compiling a comprehensive competitive dataset. Subsequently, we constructed an athlete posture estimation method utilizing OpenPose, aimed at minimizing interference caused by obstructions and enhancing the accuracy of feature extraction. In experimental validation, we gathered a diverse collection of table tennis competition video data from the internet, serving as a validation dataset. The results were impressive, with a detection success rate for standardized scenes exceeding 94% and an identification success rate for competitors surpassing 98%. The accuracy of posture reconstruction for obstructed individuals exceeded 60%, and the effectiveness of identifying athletes' main features exceeded 90%, convincingly demonstrating the effectiveness of the proposed video analysis method.", "Keywords": "", "DOI": "10.14569/IJACSA.2024.01505109", "PubYear": 2024, "Volume": "15", "Issue": "5", "JournalId": 8157, "JournalTitle": "International Journal of Advanced Computer Science and Applications", "ISSN": "2158-107X", "EISSN": "2156-5570", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Mengshuang Ma", "Affiliation": ""}], "References": []}]