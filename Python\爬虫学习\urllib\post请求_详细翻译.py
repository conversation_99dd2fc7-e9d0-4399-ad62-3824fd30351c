import urllib.request
import urllib.parse
import json

url = 'https://fanyi.baidu.com/ait/text/translate'

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0',
    'Cookie': '__bid_n=18bf1cfb03161285e1b9c6;BDUSS=WJ4WH55cnE0dHRGbW1xRlVVWE1Ob1diTmgwSkZWeXAtLUdkUnBaQlRPZFRVWVJsSVFBQUFBJCQAAAAAAQAAAAEAAADltkBTAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFPEXGVTxFxlV;BDUSS_BFESS=WJ4WH55cnE0dHRGbW1xRlVVWE1Ob1diTmgwSkZWeXAtLUdkUnBaQlRPZFRVWVJsSVFBQUFBJCQAAAAAAQAAAAEAAADltkBTAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFPEXGVTxFxlV;BAIDUID_BFESS=E9D367230A64AC157166E9BACB043DD5:FG=1;BIDUPSID=E9D367230A64AC157166E9BACB043DD5;PSTM=1726492292;ZFY=6:A4:BbjDvcbBo0hAEegNChsP1H6lgaAEZemqnrlPgg4I:C;H_PS_PSSID=60278_60941_61027_61035_61053_61098_60853_61129_61128_61114_61134_61141; ab_sr=1.0.1_NmJjYzMzYzE2NmQ5NTllZDdkNWQyNmViNjdiMTViZGQ0NTgxZmJlNmE2YjFlYWZlMDgyYmEwY2EwNWZhMjdjYmFlNmQyNDMzNGUwNDQ1YjUxMDY5ODFmN2NmZGU4YzUzYTk4MDg1M2QxNTkzOTZiODAzMzJjMGEzMDJmMGY1YjU0OTRhNWM5YmIwNDM5MmJmMGY3OWI5YWZkN2YzZWNhZg==; RT="z=1&dm=baidu.com&si=c509fec0-77bd-4385-99fe-'
}

data = {
    'corpusIds': [],
    'domain': "common",
    'from': "en",
    'milliTimestamp': '1732110961280',
    'needPhonetic': 'false',
    'query': "love",
    'reference': "",
    'to': "zh"
}
# post请求的参数 必须进行编码 并且要调用encode方法
data = urllib.parse.urlencode(data).encode('utf-8')

# post请求必须创建一个Request对象
request = urllib.request.Request(url=url, data=data, headers=headers)

# 发送请求
response = urllib.request.urlopen(request)

# 获取响应内容
content = response.read().decode('utf-8')

print(content)

lines = content.splitlines()

    # 解析每一行为JSON
for line in lines:
    if line.startswith('event: message'):
        # 去掉 'event: message' 前缀
        json_str = line[len('event: message'):].strip()
        try:
            # 尝试解析JSON
            obj = json.loads(json_str)
            print(obj)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")

# obj = json.loads(content)
# print(obj)