[{"ArticleId": 118176963, "Title": "Lean Manufacturing and Process Optimization : Enhancing Efficiency in Modern Production", "Abstract": "This comprehensive article explores the transformative impact of lean manufacturing and process optimization in modern industrial settings. It delves into these methodologies' principles, tools, and benefits, highlighting their crucial role in enhancing operational efficiency, reducing costs, and improving product quality. The article presents key statistics and case studies, including Japanese automotive manufacturer's benchmark lean manufacturing model, to demonstrate the significant improvements in productivity, quality, and cost-effectiveness achieved through these approaches. It also discusses the implementation strategies for lean and process optimization, emphasizing the importance of leadership commitment, employee training, and technology integration. Integrating Industry 4.0 technologies with lean principles, termed Lean 4.0, is introduced as the next frontier in operational excellence.", "Keywords": "Lean Manufacturing;Process Optimization;Operational Efficiency;Continuous Improvement;Industry 4.0", "DOI": "10.32628/CSEIT241051024", "PubYear": 2024, "Volume": "10", "Issue": "5", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "St Cloud State University, USA"}], "References": []}, {"ArticleId": 118176965, "Title": "Reinforcement Learning in AI-Driven Assessments : Enhancing Continuous Learning and Accessibility", "Abstract": "This article explores the transformative potential of Reinforcement Learning (RL) in AI-driven assessments, examining its impact on personalized learning experiences and continuous skill development. We investigate how RL algorithms enable adaptive learning systems to create dynamic, individualized learning paths, provide real-time feedback, and optimize long-term educational outcomes. The article delves into key aspects such as personalized learning trajectories, adaptive difficulty adjustments, and the integration of gamification elements to enhance engagement. We discuss the application of these technologies in various contexts, from traditional educational settings to professional development and certification preparation. The article also addresses the challenges and limitations of implementing RL in educational technology, including concerns about algorithmic bias and the complexity of modeling human learning processes. By analyzing current research and potential future directions, this paper provides a comprehensive overview of how RL is reshaping the landscape of education and assessment, offering insights into the future of adaptive learning systems and their role in fostering lifelong learning in an increasingly digital world.", "Keywords": "Reinforcement Learning;Adaptive Assessment;Personalized Learning;AI-driven Education;Cognitive Load Management", "DOI": "10.32628/CSEIT241051014", "PubYear": 2024, "Volume": "10", "Issue": "5", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON>", "Affiliation": "Kakatiya University, India"}], "References": [{"Title": "Educational data mining and learning analytics: An updated survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "3", "Page": "", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}]}, {"ArticleId": 118177069, "Title": "Efficient Kriging Using Interleaved Lattice-Based Designs with Low Fill and High Separation Distance Properties", "Abstract": "", "Keywords": "design of experiment; Gaussian process model; interleaved lattice; 62K99", "DOI": "10.1137/23M156940X", "PubYear": 2024, "Volume": "12", "Issue": "4", "JournalId": 11089, "JournalTitle": "SIAM/ASA Journal on Uncertainty Quantification", "ISSN": "", "EISSN": "2166-2525", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "MADIS, Academy of Mathematics and Systems Science, Chinese Academy of Sciences, Beijing, China."}], "References": []}, {"ArticleId": 118177120, "Title": "Estimating 3D Hand Poses and Shapes from Silhouettes", "Abstract": "", "Keywords": "Computer Graphics; Computer Vision; 3D reconstruction and image-based modeling; Rendering; Shape; Shape representation; Learning and statistical methods", "DOI": "10.1561/116.20240013", "PubYear": 2024, "Volume": "13", "Issue": "5", "JournalId": 10204, "JournalTitle": "APSIPA Transactions on Signal and Information Processing", "ISSN": "", "EISSN": "2048-7703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Tsing Hua University, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Tsing Hua University, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Tsing Hua University, Taiwan"}, {"AuthorId": 4, "Name": "Shys-<PERSON>", "Affiliation": "National Tsing Hua University, Taiwan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Tsing Hua University, Taiwan"}], "References": []}, {"ArticleId": 118177303, "Title": "Investment risk assessment based on improved BP neural network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAAC.2024.142093", "PubYear": 2024, "Volume": "18", "Issue": "6", "JournalId": 16160, "JournalTitle": "International Journal of Automation and Control", "ISSN": "1740-7516", "EISSN": "1740-7524", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118177422, "Title": "Evaluation of The Poedji <PERSON> Score Card (PRSC) on Digital Platform @hamilku.id Based on The Delphi Method", "Abstract": "<p>The number of cases and deaths of mothers and babies in Indonesia is increasing, which is mediated by low-risk detection in early pregnancy, and a lack of knowledge resulting in the dissemination of pregnancy-related information tends to be poorly understood. As a solution to this problem, the purpose of this study was to analyze the effectiveness and usability of the Poedji Rochjati Score Card (PRSC) feature on the @hamilku.id Digital Platform based on the Delphi method. Qualitative research methods with technical observations were carried out online by obstetricians and gynecologists. The main focus of this research was usability testing involving 46 pregnant women who used the application and 9 randomly selected respondents. The assessment and evaluation were guided by the Delphi method, which involved two rounds of testing by six obstetricians and gynecologists. The results were descriptively analyzed. The findings showed that pregnant female respondents aged between 17 and 34 years had a higher education level, were dominated by people without jobs/housewives, were domiciled in Sidoarjo, had undergone antenatal care (ANC) ≤ 6 times, and had undergone ≥ 5 pregnancies. According to the PRS, 52.2% of pregnant women were classified as having high-risk pregnancies (HRPs). Based on the evaluation of the application from the usability aspect, 83.3% of the participants stated that the information was comprehensive and that the medical terminology was easy to understand. However, only half of them considered visualization in the form of images or animations to be very helpful in illustrating pregnancy risks. Delphi testing with obstetricians and gynecologists revealed that the digital PRSC features generated positive ratings, indicating that the tool is accurate, informative, easy to understand, and effective at improving the quality of health services. The second round showed an improvement in the quality and relevance of the digital PRSC features, with more diverse feedback from the respondents providing a broader perspective for future research and feature development. As a result, the digital PRSC feature can help individuals precisely and accurately identify pregnancy risks.</p>", "Keywords": "", "DOI": "10.30864/eksplora.v14i1.1108", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 58802, "JournalTitle": "Eksplora Informatika", "ISSN": "2089-1814", "EISSN": "2460-3694", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118177690, "Title": "Exploring Automated Assertion Generation via Large Language Models", "Abstract": "<p> Unit testing aims to validate the correctness of software system units and has become an essential practice in software development and maintenance. However, it is incredibly time-consuming and labor-intensive for testing experts to write unit test cases manually, including test inputs ( i.e., prefixes) and test oracles ( i.e., assertions). Very recently, some techniques have been proposed to apply Large Language Models (LLMs) to generate unit assertions and have proven the potential in reducing manual testing efforts. However, there has been no systematic comparison of the effectiveness of these LLMs, and their pros and cons remain unexplored. </p><p> To bridge this gap, we perform the first extensive study on applying various LLMs to automated assertion generation. The experimental results on two independent datasets show that studied LLMs outperform six state-of-the-art techniques with a prediction accuracy of 51.82% \\(\\sim\\) 58.71% and 38.72% \\(\\sim\\) 48.19%. The improvements achieve 29.60% and 12.47% on average. Besides, as a representative LLM, CodeT5 consistently outperforms all studied LLMs and all baselines on both datasets, with an average improvement of 13.85% and 26.64%, respectively. We also explore the performance of generated assertions in detecting real-world bugs, and find LLMs are able to detect 32 bugs from Defects4J on average, with an improvement of 52.38% against the most recent approach EditAS . Inspired by the findings, we construct a simplistic retrieval-and-repair-enhanced LLM-based approach by transforming the assertion generation problem into a program repair task for retrieved similar assertions. Surprisingly, such a simplistic approach can further improve the prediction accuracy of LLMs by 9.40% on average, leading to new records on both datasets. Besides, we provide additional discussions from different aspects ( e.g., the impact of assertion types and test lengths) to illustrate the capacity and limitations of LLM-based approaches. Finally, we further pinpoint various practical guidelines ( e.g., the improvement of multiple candidate assertions) for advanced LLM-based assertion generation in the near future. Overall, our work underscores the promising future of adopting off-the-shelf LLMs to generate accurate and meaningful assertions in real-world test cases and reduce the manual efforts of unit testing experts in practical scenarios. </p>", "Keywords": "", "DOI": "10.1145/3699598", "PubYear": 2025, "Volume": "34", "Issue": "3", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, China"}, {"AuthorId": 2, "Name": "Weifeng Sun", "Affiliation": "School of Big Data & Software Engineering, Chongqing University, China"}, {"AuthorId": 3, "Name": "Chunrong Fang", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Big Data & Software Engineering, Chongqing University, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Big Data & Software Engineering, Chongqing University, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huawei Cloud Computing Technologies Co., Ltd., China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Novel Software Technology, Nanjing University, China"}], "References": [{"Title": "MeMo: Automatically identifying metamorphic relations in Javadoc comments for test automation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "111041", "JournalTitle": "Journal of Systems and Software"}, {"Title": "A Survey of Learning-based Automated Program Repair", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Yuxiang Ma", "PubYear": 2024, "Volume": "33", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "An Extractive-and-Abstractive Framework for Source Code Summarization", "Authors": "<PERSON><PERSON>g Sun; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "33", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 118177701, "Title": "Physics-Informed Computer Vision: A Review and Perspectives", "Abstract": "<p>The incorporation of physical information in machine learning frameworks is opening and transforming many application domains. Here the learning process is augmented through the induction of fundamental knowledge and governing physical laws. In this work, we explore their utility for computer vision tasks in interpreting and understanding visual data. We present a systematic literature review of more than 250 papers on formulation and approaches to computer vision tasks guided by physical laws. We begin by decomposing the popular computer vision pipeline into a taxonomy of stages and investigate approaches to incorporate governing physical equations in each stage. Existing approaches are analyzed in terms of modeling and formulation of governing physical processes, including modifying input data (observation bias), network architectures (inductive bias), and training losses (learning bias). The taxonomy offers a unified view of the application of the physics-informed capability, highlighting where physics-informed learning has been conducted and where the gaps and opportunities are. Finally, we highlight open problems and challenges to inform future research. While still in its early days, the study of physics-informed computer vision has the promise to develop better computer vision models that can improve physical plausibility, accuracy, data efficiency, and generalization in increasingly realistic applications.</p>", "Keywords": "", "DOI": "10.1145/3689037", "PubYear": 2025, "Volume": "57", "Issue": "1", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engneering and Robotics, Queensland University of Technology, Brisbane, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engneering and Robotics, Queensland University of Technology, Brisbane, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical Engneering and Robotics, Queensland University of Technology, Brisbane, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, Brown University, Providence, United States"}], "References": [{"Title": "Physics Informed Synthetic Image Generation for Deep Learning-Based Detection of Wrinkles and Folds", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Applications of Generative Adversarial Networks (GANs): An Updated Review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "2", "Page": "525", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Explainable Artificial Intelligence (XAI): Concepts, taxonomies, opportunities and challenges toward responsible AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "58", "Issue": "", "Page": "82", "JournalTitle": "Information Fusion"}, {"Title": "Pan-GAN: An unsupervised pan-sharpening method for remote sensing image fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "110", "JournalTitle": "Information Fusion"}, {"Title": "MedSRGAN: medical images super-resolution using generative adversarial networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "21815", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Deep Journey into Super-resolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep learning for tomographic image reconstruction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "12", "Page": "737", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Learning nonlinear operators via DeepONet based on the universal approximation theorem of operators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "3", "Page": "218", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Human face super-resolution on poor quality surveillance video footage", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "20", "Page": "13505", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Fashion Meets Computer Vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Adaptive and stabilized real-time super-resolution control for UAV-assisted smart harbor surveillance platforms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "5", "Page": "1815", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "Semantic segmentation based stereo visual servoing of nonholonomic mobile robot in intelligent manufacturing environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "190", "Issue": "", "Page": "116203", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fault Cause Assignment with Physics Informed Transfer Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "20", "Page": "53", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Review the state-of-the-art technologies of semantic segmentation based on deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "493", "Issue": "", "Page": "626", "JournalTitle": "Neurocomputing"}, {"Title": "Unified medical image segmentation by learning from uncertainty in an end-to-end manner", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108215", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Using Physics-Informed Generative Adversarial Networks to Perform Super-Resolution for Multiphase Fluid Simulations", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "4", "Page": "1", "JournalTitle": "Journal of Computing and Information Science in Engineering"}, {"Title": "Physics informed neural fields for smoke reconstruction with sparse data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Exposing unseen GAN-generated image using unsupervised domain adaptation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109905", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Physics-informed convolutional neural networks for temperature field prediction of heat source layout without labeled data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105516", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 118177770, "Title": "Domain disentanglement and contrastive learning with source-guided sampling for unsupervised domain adaptation person re-identification", "Abstract": "<p>In recent years, fully supervised Person re-id methods have already been well developed. Still, they cannot be easily applied to real-life applications because of the domain gap between real-world databases and training datasets. And annotating ground truth label for the entire surveillance system with multiple cameras and videos are labor-intensive and impracticable in the real application. Besides, as the awareness of the right to privacy is rising, it becomes more challenging to collect sufficient training data from the public. Thence, the difficulty of constructing a new dataset for deployment not only arises from the labor cost of labeling but also because the raw data from the public are hard to come by. To be better adapted to real-life system deployment, we proposed an unsupervised domain adaptation based method, which involves Domain Disentanglement Network and Source-Guided Contrastive learning (SGCL). DD-Net first narrows down the domain gap between two datasets, and then SGCL utilizes the labeled source dataset as the clue to guide the training on the target domain. With these two modules, the knowledge transfer can be completed successfully from the training dataset to real-world scenarios. The conducted experiment shows that the proposed method is competitive with the state-of-the-art methods on two public datasets and even outperforms them under the setting of the small-scale target dataset. Therefore, not only the Person Re-ID, but also the object tracking in video or surveillance system can benefit from our new approach when we went to deploy to different environments.</p>", "Keywords": "Deep learning; Person re-identification; Domain adaptation; Domain disentanglement; Contrastive learning", "DOI": "10.1007/s00138-024-01613-4", "PubYear": 2024, "Volume": "35", "Issue": "6", "JournalId": 1175, "JournalTitle": "Machine Vision and Applications", "ISSN": "0932-8092", "EISSN": "1432-1769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Taiwan University, Taipei, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, National Taiwan University, Taipei, Taiwan; NTU Center for Artificial Intelligence and Advanced Robotics (AIROBO), Taipei, Taiwan; Corresponding author."}], "References": [{"Title": "Enhancing collaborative road scene reconstruction with unsupervised domain alignment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Hierarchical contrastive adaptation for cross-domain object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "4", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Multi-focus image fusion based on unsupervised learning", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "5", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Cascaded attention-guided multi-granularity feature learning for person re-identification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Ubiquitous vision of transformers for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "2", "Page": "1", "JournalTitle": "Machine Vision and Applications"}]}, {"ArticleId": 118177776, "Title": "Wind Turbine Bearing Failure Diagnosis Using Multi-Scale Feature Extraction and Residual Neural Networks with Block Attention", "Abstract": "<p>Wind turbine rolling bearings are crucial components for ensuring the reliability and stability of wind power systems. Their failure can lead to significant economic losses and equipment downtime. Therefore, the accurate diagnosis of bearing faults is of great importance. Although existing deep learning fault diagnosis methods have achieved certain results, they still face limitations such as inadequate feature extraction capabilities, insufficient generalization to complex working conditions, and ineffective multi-scale feature capture. To address these issues, this paper proposes an advanced fault diagnosis method named the two-stream feature fusion convolutional neural network (TSFFResNet-Net). Firstly, the proposed method combines the advantages of one-dimensional convolutional neural networks (1D-ResNet) and two-dimensional convolutional neural networks (2D-ResNet). It transforms one-dimensional vibration signals into two-dimensional images through the empirical wavelet transform (EWT) method. Then, parallel convolutional kernels in 1D-ResNet and 2D-ResNet are used to extract multi-scale features, respectively. Next, the Convolutional Block Attention Module (CBAM) is introduced to enhance the network’s ability to capture key features by focusing on important features in specific channels or spatial areas. After feature fusion, CBAM is introduced again to further enhance the effect of feature fusion, ensuring that the features extracted by different network branches can be effectively integrated, ultimately providing more accurate input features for the classification task of the fully connected layer. The experimental results demonstrate that the proposed method outperforms other traditional methods and advanced convolutional neural network models on different datasets. Compared with convolutional neural network models such as LeNet-5, AlexNet, and ResNet, the proposed method achieves a significantly higher accuracy on the test set, with a stable accuracy of over 99%. Compared with other models, it shows better generalization and stability, effectively improving the overall performance of rolling bearing vibration signal fault diagnosis. The method provides an effective solution for the intelligent fault diagnosis of wind turbine rolling bearings.</p>", "Keywords": "", "DOI": "10.3390/act13100401", "PubYear": 2024, "Volume": "13", "Issue": "10", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "Yuanqing Luo", "Affiliation": "School of Environmental and Chemical Engineering, Shenyang University of Technology, Shenyang 110870, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Environmental and Chemical Engineering, Shenyang University of Technology, Shenyang 110870, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Control Engineering, Baicheng Normal University, Baicheng 137000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>ong <PERSON>", "Affiliation": "School of Environmental and Chemical Engineering, Shenyang University of Technology, Shenyang 110870, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Environmental and Chemical Engineering, Shenyang University of Technology, Shenyang 110870, China"}, {"AuthorId": 6, "Name": "Feng Sun", "Affiliation": "School of Mechanical Engineering, Shenyang University of Technology, Shenyang 110870, China"}], "References": [{"Title": "Deep learning fault diagnosis method based on global optimization GAN for unbalanced data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "187", "Issue": "", "Page": "104837", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Traffic sign detection based on multi-scale feature extraction and cascade feature fusion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "79", "Issue": "2", "Page": "2137", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 118178021, "Title": "LSKNet: A Foundation Lightweight Backbone for Remote Sensing", "Abstract": "<p>Remote sensing images pose distinct challenges for downstream tasks due to their inherent complexity. While a considerable amount of research has been dedicated to remote sensing classification, object detection, semantic segmentation and change detection, most of these studies have overlooked the valuable prior knowledge embedded within remote sensing scenarios. Such prior knowledge can be useful because remote sensing objects may be mistakenly recognized without referencing a sufficiently long-range context, which can vary for different objects. This paper considers these priors and proposes a lightweight Large Selective Kernel Network (LSKNet) backbone. LSKNet can dynamically adjust its large spatial receptive field to better model the ranging context of various objects in remote sensing scenarios. To our knowledge, large and selective kernel mechanisms have not been previously explored in remote sensing images. Without bells and whistles, our lightweight LSKNet backbone network sets new state-of-the-art scores on standard remote sensing classification, object detection, semantic segmentation and change detection benchmarks. Our comprehensive analysis further validated the significance of the identified priors and the effectiveness of LSKNet. The code is available at https://github.com/zcablii/LSKNet .</p>", "Keywords": "Remote sensing; CNN backbone; Large kernel; Attention; Object detection; Semantic segmentation.", "DOI": "10.1007/s11263-024-02247-9", "PubYear": 2025, "Volume": "133", "Issue": "3", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PCA Lab, VCIP, College of Computer Science, Nankai University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, VCIP, College of Computer Science, Nankai University, Tianjin, China; NKIARI, Futian, Shenzhen, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PCA Lab, VCIP, College of Computer Science, Nankai University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, VCIP, College of Computer Science, Nankai University, Tianjin, China; NKIARI, Futian, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Academy of Advanced Technology Research of Hunan, Changsha, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Academy of Advanced Technology Research of Hunan, Changsha, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PCA Lab, VCIP, College of Computer Science, Nankai University, Tianjin, China; NKIARI, Futian, Shenzhen, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, VCIP, College of Computer Science, Nankai University, Tianjin, China; Corresponding author."}], "References": [{"Title": "Efficient semantic segmentation with pyramidal fusion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107611", "JournalTitle": "Pattern Recognition"}, {"Title": "Attention mechanisms in computer vision: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "331", "JournalTitle": "Computational Visual Media"}, {"Title": "PVT v2: Improved baselines with Pyramid Vision Transformer", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "415", "JournalTitle": "Computational Visual Media"}, {"Title": "TINYCD: a (not so) deep learning model for change detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "11", "Page": "8471", "JournalTitle": "Neural Computing and Applications"}, {"Title": "ViTAEv2: Vision Transformer Advanced by Exploring Inductive Bias for Image Recognition and Beyond", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "131", "Issue": "5", "Page": "1141", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "D2ANet: Difference-aware attention network for multi-level change detection from satellite imagery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "3", "Page": "563", "JournalTitle": "Computational Visual Media"}, {"Title": "Visual attention network", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "4", "Page": "733", "JournalTitle": "Computational Visual Media"}]}, {"ArticleId": 118178057, "Title": "An application of deep choice modeling for engagement maximization on Twitter/X", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10844-024-00893-6", "PubYear": 2025, "Volume": "63", "Issue": "2", "JournalId": 7081, "JournalTitle": "Journal of Intelligent Information Systems", "ISSN": "0925-9902", "EISSN": "1573-7675", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Assortment Optimisation Under a General Discrete Choice Model: A Tight Analysis of Revenue-Ordered Assortments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "82", "Issue": "4", "Page": "681", "JournalTitle": "Algorithmica"}, {"Title": "What social characteristics enhance recommender systems? The effects of network embeddedness and preference heterogeneity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "3", "Page": "1807", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Directional user similarity model for personalized recommendation in online social networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "10", "Page": "10205", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A survey on large language models for recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "27", "Issue": "5", "Page": "1", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 118178059, "Title": "Isogeometric analysis of the Laplace eigenvalue problem on circular sectors: Regularity properties and graded meshes", "Abstract": "The Laplace eigenvalue problem on circular sectors has eigenfunctions with corner singularities. Standard methods may produce suboptimal approximation results. To address this issue, a novel numerical algorithm that enhances standard isogeometric analysis is proposed in this paper by using a single-patch graded mesh refinement scheme. Numerical tests demonstrate optimal convergence rates for both the eigenvalues and eigenfunctions. Furthermore, the results show that smooth splines possess a superior approximation constant compared to their C 0 -continuous counterparts for the lower part of the Laplace spectrum. This is an extension of previous findings about excellent spectral approximation properties of smooth splines on rectangular domains to circular sectors. In addition, graded meshes prove to be particularly advantageous for an accurate approximation of a limited number of eigenvalues. Finally, a hierarchical mesh structure is presented to avoid anisotropic elements in the physical domain and to omit redundant degrees of freedom in the vicinity of the singularity. Numerical results validate the effectiveness of hierarchical mesh grading for simulating eigenfunctions of low and high regularity.", "Keywords": "Isogeometric analysis; Eigenvalue problems; Corner singularities; Graded mesh refinement; Singular parameterizations; Circular sectors", "DOI": "10.1016/j.camwa.2024.09.018", "PubYear": 2024, "Volume": "175", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Mathematics and Computer-Based Simulation, Universität der Bundeswehr München, Werner-Heisenberg-Weg 39, 85577 Neubiberg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Mathematics and Computer-Based Simulation, Universität der Bundeswehr München, Werner<PERSON>Heisenberg-Weg 39, 85577 Neubiberg, Germany;Corresponding author"}], "References": [{"Title": "A Posteriori Error Estimates for Elliptic Eigenvalue Problems Using Auxiliary Subspace Techniques", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "88", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}, {"Title": "Mathematical Foundations of Adaptive Isogeometric Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "29", "Issue": "7", "Page": "4479", "JournalTitle": "Archives of Computational Methods in Engineering"}]}, {"ArticleId": 118178108, "Title": "FedITA: A cloud–edge collaboration framework for domain generalization-based federated fault diagnosis of machine-level industrial motors", "Abstract": "Adequate samples are necessary for establishing a high-performance supervised learning model for intelligent fault diagnosis. Startup companies may only have normal devices and therefore there exists extreme class imbalance of training samples. Lack of faulty devices makes it difficult to independently establish supervised learning. The ideal aggregated training using raw data from multiple client sources may lead to potential conflicts of interest, making it difficult to implement. In addition, individual difference caused by manufacturing inconsistencies and dynamic testing environments is a special interference for machine-level industrial motors, which is more significant in the information flow of multiple client sources. This article proposes a federated iterative learning algorithm (FedITA) as a cloud–edge collaboration framework for domain generalization-based federated fault diagnosis of machine-level industrial motors. The proposed FedITA utilizes progressive training and iterative weight updates to enhance secure interaction between different clients, effectively reducing the risk of overfitting caused by extreme class imbalance. A hybrid perception mechanism is implemented by developing complementary perception modules and integrated into a hybrid perception field network (HPFNet) as a recommended global federated model. The proposed method and model are performed on real production line signals and can achieve mean cross-machine F1-score of 96.50% in limited communication.", "Keywords": "", "DOI": "10.1016/j.aei.2024.102853", "PubYear": 2024, "Volume": "62", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Manufacturing Equipment and Technology, Huazhong University of Science and Technology, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Intelligent Manufacturing Equipment and Technology, Huazhong University of Science and Technology, Wuhan 430074, China;Corresponding author"}], "References": [{"Title": "Federated adversarial domain generalization network: A novel machinery fault diagnosis method with data privacy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "256", "Issue": "", "Page": "109880", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "In-situ fault detection for the spindle motor of CNC machines via multi-stage residual fusion convolution neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "145", "Issue": "", "Page": "103810", "JournalTitle": "Computers in Industry"}, {"Title": "In-situ fault diagnosis for the harmonic reducer of industrial robots via multi-scale mixed convolutional neural networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "66", "Issue": "", "Page": "233", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "CNN parameter design based on fault signal analysis and its application in bearing fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "", "Page": "101877", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A novel deep clustering network using multi-representation autoencoder and adversarial learning for large cross-domain fault diagnosis of rolling bearings", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "225", "Issue": "", "Page": "120066", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A federated learning approach to mixed fault diagnosis in rotating machinery", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "68", "Issue": "", "Page": "687", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "MSRCN: A cross-machine diagnosis method for the CNC spindle motors with compound faults", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "120957", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Novel extended NI-MWMOTE-based fault diagnosis method for data-limited and noise-imbalanced scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121799", "JournalTitle": "Expert Systems with Applications"}, {"Title": "MJAR: A novel joint generalization-based diagnosis method for industrial robots with compound faults", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "86", "Issue": "", "Page": "102668", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "FedHIP: Federated learning for privacy-preserving human intention prediction in human-robot collaborative assembly tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102411", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "FTSDC: A novel federated transfer learning strategy for bearing cross-machine fault diagnosis based on dual-correction training", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "61", "Issue": "", "Page": "102499", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A federated cross-machine diagnostic framework for machine-level motors with extreme label shortage", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "61", "Issue": "", "Page": "102511", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Fault vibration model driven fault-aware domain generalization framework for bearing fault diagnosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "62", "Issue": "", "Page": "102620", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 118178110, "Title": "A novel approach for an energy-efficient traffic monitoring system using wireless sensor network and CupCarbon simulator (V 5.0) for a smart city", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.142038", "PubYear": 2024, "Volume": "25", "Issue": "4", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118178127, "Title": "Modifier-adaptation-based RTO scheme for water distribution networks under demand and parametric uncertainties", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJAAC.2024.142044", "PubYear": 2024, "Volume": "18", "Issue": "6", "JournalId": 16160, "JournalTitle": "International Journal of Automation and Control", "ISSN": "1740-7516", "EISSN": "1740-7524", "Authors": [{"AuthorId": 1, "Name": "Jaivik Mankad", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118178202, "Title": "Motor Fault Detection and Isolation for Multi-Rotor UAVs Based on External Wrench Estimation and Recurrent Deep Neural Network", "Abstract": "<p>Fast detection of motor failures is crucial for multi-rotor unmanned aerial vehicle (UAV) safety. It is well established in the literature that UAVs can adopt fault-tolerant control strategies to fly even when losing one or more rotors. We present a motor fault detection and isolation (FDI) method for multi-rotor UAVs based on an external wrench estimator and a recurrent neural network composed of long short-term memory nodes. The proposed approach considers the partial or total motor fault as an external disturbance acting on the UAV. Hence, the devised external wrench estimator trains the network to promptly understand whether the estimated wrench comes from a motor fault (also identifying the motor) or from unmodelled dynamics or external effects (i.e., wind, contacts, etc.). Training and testing have been performed in a simulation environment endowed with a physic engine, considering different UAV models operating under unknown external disturbances and unexpected motor faults. To further assess this approach’s effectiveness, we compare our method’s performance with a classical model-based technique. The collected results demonstrate the effectiveness of the proposed FDI approach.</p>", "Keywords": "Fault detection and isolation; Long short-term memory networks; External wrench estimation; Safe aerial robotics", "DOI": "10.1007/s10846-024-02176-2", "PubYear": 2024, "Volume": "110", "Issue": "4", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Eurecat, Centre Tecnologic de Catalunya Robotics and Automation Unit, Cerdanyola del Vallès, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology, University of Naples Federico II, Naples, Italy; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology, University of Naples Federico II, Naples, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Information Technology, University of Naples Federico II, Naples, Italy"}], "References": []}, {"ArticleId": 118178252, "Title": "Code and Data Repository for Domain-Independent Dynamic Programming and Constraint Programming Approaches for Assembly Line Balancing Problems with Setups", "Abstract": "", "Keywords": "domain-independent dynamic programming; constraint programming; assembly line balancing", "DOI": "10.1287/ijoc.2024.0603.cd", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118178283, "Title": "Maximising Synergy: The Benefits of a Joint Implementation of Knowledge Management and Artificial Intelligence System Standards", "Abstract": "<p>Implementing management systems in organisations of all types and sizes often raises the following question: “What benefits will this bring?” Initial resistance and criticism are common as potential challenges are identified during the implementation process. To address this, it is essential to highlight the advantages of these systems and engage stakeholders in supporting management efforts. While the planning, implementation, use, maintenance, auditing, and improvement of management systems are generally voluntary, certification is frequently driven by external factors, particularly customer demands. Employees also stand to gain significantly, with knowledge and information serving as valuable resources, especially for leveraging artificial intelligence. This article explores the management’s readiness to adopt and fully utilise two management systems based on international standards: the ISO 30401 Knowledge management system (KMS) and the ISO/IEC 42001 Artificial intelligence management system (AIMS). Through interviews, we assess the challenges and solutions associated with implementing these systems, whether planned or partially adopted. The findings illustrate the synergistic benefits of integrating the KMS and AIMS, demonstrating how their combined use can enhance Integrated Management Systems (IMSs). Such integration supports comprehensive planning, operation, and performance evaluation of processes and services while also promoting continuous improvement.</p>", "Keywords": "", "DOI": "10.3390/make6040112", "PubYear": 2024, "Volume": "6", "Issue": "4", "JournalId": 51955, "JournalTitle": "Machine Learning and Knowledge Extraction", "ISSN": "", "EISSN": "2504-4990", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Management, Saint Petersburg School of Economics and Management, HSE University, 194100 St. Petersburg, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Industrial Engineering and Management, Faculty of Materials Science and Technology in Trnava, The Slovak University of Technology in Bratislava, 917 24 Trnava, Slovakia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Industrial Engineering and Management, Faculty of Materials Science and Technology in Trnava, The Slovak University of Technology in Bratislava, 917 24 Trnava, Slovakia"}], "References": [{"Title": "Towards CRISP-ML(Q): A Machine Learning Process Model with Quality Assurance Methodology", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "2", "Page": "392", "JournalTitle": "Machine Learning and Knowledge Extraction"}]}, {"ArticleId": 118178313, "Title": "A Quantum-Safe Authentication Scheme for IoT Devices using Homomorphic Encryption and Weak Physical Unclonable Functions with no Helper Data", "Abstract": "Physical Unclonable Functions (PUFs) are widely used to authenticate electronic devices because they take advantage of random variations in the manufacturing process that are unique to each device and cannot be cloned. Therefore, each device can be uniquely identified and counterfeit devices can be detected. Weak PUFs, which support a relatively small number of challenge-response pairs (CRPs), are simple and easy to construct. Device authentication with weak PUFs typically uses helper data to obfuscate and recover a cryptographic key that is then required by a cryptographic authentication scheme. However, these schemes are vulnerable to helper-data attacks and many of them do not protect conveniently the PUF responses, which are sensitive data, as well as are not resistant to attacks performed by quantum computers. This paper proposes an authentication scheme that avoids the aforementioned weaknesses by not using helper data, protecting the PUF response with a quantum-safe homomorphic encryption, and by using a two-server setup. Specifically, the CRYSTALS-Kyber public key cryptographic algorithm is used for its quantum resistance and suitability for resource-constrained Internet-of-Things (IoT) devices. The practicality of the proposal was tested on an ESP32 microcontroller using its internal SRAM as a SRAM PUF. For PUF responses of 512 bits, the encryption execution time ranges from 16.41 ms to 41.08 ms, depending on the desired level of security. In terms of memory, the device only needs to store between 800 and 1,568 bytes. This makes the solution post-quantum secure, lightweight and affordable for IoT devices with limited computing, memory, and power resources.", "Keywords": "", "DOI": "10.1016/j.iot.2024.101389", "PubYear": 2024, "Volume": "28", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Instituto de Microelectrónica de Sevilla IMSE-CNM (Universidad de Sevilla, CSIC), Seville, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "Rosario Arjona", "Affiliation": "Instituto de Microelectrónica de Sevilla IMSE-CNM (Universidad de Sevilla, CSIC), Seville, Spain"}, {"AuthorId": 3, "Name": "Iluminada Baturone", "Affiliation": "Instituto de Microelectrónica de Sevilla IMSE-CNM (Universidad de Sevilla, CSIC), Seville, Spain"}], "References": [{"Title": "PUF-derived IoT identities in a zero-knowledge protocol for blockchain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "", "Page": "100057", "JournalTitle": "Internet of Things"}, {"Title": "Computational fuzzy extractors", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "275", "Issue": "", "Page": "104602", "JournalTitle": "Information and Computation"}, {"Title": "Cybersecurity awareness in the context of the Industrial Internet of Things: A systematic literature review", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "137", "Issue": "", "Page": "103614", "JournalTitle": "Computers in Industry"}, {"Title": "When the Decoder Has to Look Twice: Glitching a PUF Error Correction", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "26", "JournalTitle": "IACR Transactions on Cryptographic Hardware and Embedded Systems"}]}, {"ArticleId": 118178516, "Title": "An unsupervised domain adaptation method for detecting blades icing for multiple wind turbines", "Abstract": "Icing on wind turbine blades leads to significant challenges, including reduced power generation and increased mechanical stress. Detecting blade icing is crucial for maintaining turbine efficiency and safety. Current research primarily focuses on enhancing the ability of icing detection for a single turbine, overlooking solutions applicable to multi-turbines scenarios. Acquiring the ability to detect icing on the blades of multiple wind turbines will help in operation and maintenance to extend capabilities from the equipment level to the wind farm level, improving maintenance efficiency. However, building icing sample sets for multi-turbines is difficult because icing events are rare and hard to be observed directly in real time. Usually, a small number of labelled sample sets can be obtained, while the majority of turbines remain unlabelled. Meanwhile, models trained on labelled samples from a few turbines cannot be generalized well to others due to the existence of feature shift between different turbines. Therefore, we propose a novel Residual-based Unsupervised Domain Adaptation (ReUDA) method for multi-turbines blade icing detection. This method combines a deep residual network with maximum mean discrepancy (MMD) to facilitate unsupervised domain adaptation. Evaluation using an open and qualified dataset and a real dataset from Windey Energy Technology Group Company Limited demonstrates that ReUDA effectively mitigates dataset imbalance and feature shift, significantly improving icing detection performance in multi-turbines scenarios.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109396", "PubYear": 2024, "Volume": "138", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou 310023, China;Taizhou Key Laboratory of Advanced Manufacturing Technology, Taizhou Research Institute, Zhejiang University of Technology, Taizhou, 318000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "College of Mechanical Engineering, Zhejiang University of Technology, Hangzhou 310023, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Zhejiang University of Technology, Hangzhou 310023, China;Corresponding author"}], "References": [{"Title": "A hierarchical adversarial multi-target domain adaptation for gear fault diagnosis under variable working condition based on raw acoustic signal", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106449", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Transferable dynamic enhanced cost-sensitive network for cross-domain intelligent diagnosis of rotating machinery under imbalanced datasets", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "", "Page": "106670", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Adaptive manifold partial domain adaptation for fault transfer diagnosis of rotating machinery", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "126", "Issue": "", "Page": "107082", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A locally weighted multi-domain collaborative adaptation for failure prediction in SSDs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "280", "Issue": "", "Page": "111012", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 118178652, "Title": "Equity Spotlight: <PERSON>", "Abstract": "<p><PERSON> is a Computer Science and Engineering PhD student at UC San Diego and a Ford Foundation Predoctoral Fellow.</p>", "Keywords": "", "DOI": "10.1145/3699853.3699860", "PubYear": 2024, "Volume": "56", "Issue": "3", "JournalId": 19941, "JournalTitle": "ACM SIGCSE Bulletin", "ISSN": "0097-8418", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118178662, "Title": "Towards Efficiency in Bilateral Trade: An Annotated Reading List", "Abstract": "<p>This is an annotated reading list on research towards designing socially efficient mechanisms for bilateral trade and its generalizations.</p>", "Keywords": "", "DOI": "10.1145/3699804.3699812", "PubYear": 2022, "Volume": "20", "Issue": "2", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Simons Institute, UC Berkeley"}], "References": []}, {"ArticleId": 118178664, "Title": "Deciding to Stop Early or Continue the Experiment After Checking <i>p</i> -Values at Interim Points: Introducing Group Sequential Designs to UI-Based Comparative Studies", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2407662", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "LY Corporation, Chiyoda-ku, Tokyo, Japan"}], "References": [{"Title": "Relative Merits of Nominal and Effective Indexes of Difficulty of Fitts’ Law: Effects of Sample Size and the Number of Repetitions on Model Fit", "Authors": "<PERSON><PERSON>", "PubYear": 2025, "Volume": "41", "Issue": "1", "Page": "574", "JournalTitle": "International Journal of Human–Computer Interaction"}]}, {"ArticleId": 118178949, "Title": "Enfoque multitareas implementado en una minicomputadora para el control y monitoreo de un motor a pasos industrial", "Abstract": "", "Keywords": "", "DOI": "10.36825/RITI.12.27.002", "PubYear": 2024, "Volume": "12", "Issue": "27", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON> Je<PERSON> del Carmen", "Affiliation": ""}, {"AuthorId": 3, "Name": "Antonio de Jesús <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON> Con<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118178951, "Title": "Predicción de lectura en instrumento patrón para una empresa de metrología a través de un dashboard empleando algoritmos de regresión", "Abstract": "", "Keywords": "", "DOI": "10.36825/RITI.12.27.004", "PubYear": 2024, "Volume": "12", "Issue": "27", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118179008, "Title": "Settling the Efficiency of the First-Price Auction", "Abstract": "<p> We survey the main result from [<PERSON> and <PERSON> 2022]: For the first-price auction, the tight Price of Anarchy is 1 - 1/e <sup>2</sup> ≈ 0.8647, which closes the gap between the best known bounds [0.7430, 0.8689]. </p>", "Keywords": "", "DOI": "10.1145/3699804.3699810", "PubYear": 2022, "Volume": "20", "Issue": "2", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Yaonan Jin", "Affiliation": "Columbia University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai University of Finance and Economics"}], "References": []}, {"ArticleId": 118179060, "Title": "Impartial Peer Selection: An Annotated Reading List", "Abstract": "<p>The study of peer selection mechanisms presents a unique opportunity to understand and improve the practice of a group selecting its best members, despite each member of that group wanting to be selected. A prime example of such a setting is academic peer review, for which peer selection offers a variety of improvement directions. We present an annotated reading list covering the foundations of peer selection as well as recent and emerging work within the field.</p>", "Keywords": "", "DOI": "10.1145/3699824.3699834", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Ben-Gurion University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Tulane University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tulane University"}], "References": [{"Title": "Challenges, experiments, and computational solutions in peer review", "Authors": "<PERSON><PERSON> B<PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "6", "Page": "76", "JournalTitle": "Communications of the ACM"}]}, {"ArticleId": 118179131, "Title": "Code and Data Repository for An Efficient Optimization Model and Tabu Search-Based Global Optimization Approach for the Continuous p-Dispersion Problem", "Abstract": "", "Keywords": "circle packing; continuous dispersion problem; global optimization; tabu search; nonlinear optimization", "DOI": "10.1287/ijoc.2023.0089.cd", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "Xiangjing Lai", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Qinghua Wu", "Affiliation": ""}], "References": []}, {"ArticleId": 118179133, "Title": "Beyond principlism: practical strategies for ethical AI use in research practices", "Abstract": "", "Keywords": "", "DOI": "10.1007/s43681-024-00585-5", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 79924, "JournalTitle": "AI and Ethics", "ISSN": "2730-5953", "EISSN": "2730-5961", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "The uselessness of AI ethics", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "3", "Page": "869", "JournalTitle": "AI and Ethics"}, {"Title": "All that glitters is not gold: trustworthy and ethical AI principles", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "4", "Page": "1241", "JournalTitle": "AI and Ethics"}, {"Title": "Human–AI collaboration enables more empathic conversations in text-based peer-to-peer mental health support", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "5", "Issue": "1", "Page": "46", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "From ethical AI frameworks to tools: a review of approaches", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "3", "Page": "699", "JournalTitle": "AI and Ethics"}, {"Title": "Guidance for researchers and peer-reviewers on the ethical use of Large Language Models (LLMs) in scientific research workflows", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "4", "Page": "969", "JournalTitle": "AI and Ethics"}, {"Title": "Does the sun rise for ChatGPT? Scientific discovery in the age of generative AI", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "AI and Ethics"}, {"Title": "Worldwide AI ethics: A review of 200 guidelines and recommendations for AI governance", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "10", "Page": "100857", "JournalTitle": "Patterns"}, {"Title": "Friend or foe? Exploring the implications of large language models on the science system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2025, "Volume": "40", "Issue": "2", "Page": "447", "JournalTitle": "AI & SOCIETY"}, {"Title": "Using proprietary language models in academic research requires explicit justification", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "4", "Issue": "1", "Page": "2", "JournalTitle": "Nature Computational Science"}, {"Title": "Social construction of XAI: Do we need one definition to rule them all?", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "2", "Page": "100926", "JournalTitle": "Patterns"}, {"Title": "The ethics of using artificial intelligence in scientific research: new guidance needed for a new tool", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "AI and Ethics"}]}, {"ArticleId": 118179142, "Title": "GSKTM: efficient of query search for spatial keyword in text mining", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.142068", "PubYear": 2024, "Volume": "25", "Issue": "4", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "S.S. Iyengar", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118179195, "Title": "Area-driven Boolean bi-decomposition by function approximation", "Abstract": "Bi-decomposition rewrites logic functions as the composition of simpler components. It is related to Boolean division, where a given function is rewritten as the product of a divisor and a quotient, but bi-decomposition can be defined for any Boolean operation of two operands. The key questions are how to find a good divisor and then how to compute the quotient. In this article, we select the divisor by approximation of the original function and then characterize by an incompletely specified function the full flexibility of the quotient for each binary operator. We target area-driven exact bi-decomposition, and we apply it to the bi-decomposition of Sum-of-Products (SOP) forms. We report experiments that exhibit significant gains in literals of SOP forms when rewritten as bi-decompositions with respect to the product operator. This suggests the application of this framework to other logic forms and binary operations, both for exact and approximate implementations.", "Keywords": "", "DOI": "10.1145/3698879", "PubYear": 2025, "Volume": "30", "Issue": "1", "JournalId": 8858, "JournalTitle": "ACM Transactions on Design Automation of Electronic Systems", "ISSN": "1084-4309", "EISSN": "1557-7309", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Università di Pisa,  Pisa, Italy"}, {"AuthorId": 2, "Name": "Valentina Ciriani", "Affiliation": "Università degli Studi di Milano,  Milano, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Universitat Politecnica de Catalunya,  Barcelona, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Università di Pisa,  Pisa, Italy"}, {"AuthorId": 5, "Name": "Tiziano <PERSON>", "Affiliation": "Università degli Studi di Verona,  Verona, Italy"}], "References": []}, {"ArticleId": 118179352, "Title": "Poverty and freedom: philosophical reflection on the future development of artificial intelligence", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00146-024-02073-0", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 15029, "JournalTitle": "AI & SOCIETY", "ISSN": "0951-5666", "EISSN": "1435-5655", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Zhu", "Affiliation": ""}], "References": [{"Title": "Meaning–thinking–AI", "Authors": "<PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "5", "Page": "2213", "JournalTitle": "AI & SOCIETY"}]}, {"ArticleId": 118179382, "Title": "A systematic quality-integrated diagnostic method for complex product assembly using multi-task spatial–temporal transfer learning", "Abstract": "The assembly process is generally considered one of the primary factors influencing the quality of complex products. Currently, most existing quality-integrated diagnostic methods for products tend to deteriorate over different processes and degrade over time. To address this issue, this paper introduces a systematic quality-integrated diagnostic method for complex product assembly processes. First, the influence factors and error sources in the complex assembly process are analyzed using the 5M1E and FAHP methodologies. Next, similarity-based multi-task clustering and dismantling using RGMM is applied to divide the assembly tasks. Finally, MMD-MSE is employed to develop a quality prediction model for the complex assembly process using a spatial–temporal transfer learning approach. Experiments were conducted on an array antenna assembly task, comparing the proposed method with conventional methods. The results show that the accuracy and PrUP of the proposed model are 97.6% and 95.2%, respectively, for the quality-integrated diagnostic of complex assembly processes, with a fluctuation in accuracy of less than 6%. The diagnostic results effectively meet expert evaluations and provide a stable, reliable, and practical solution for addressing quality fluctuations in complex production assembly processes.", "Keywords": "Quality-integrated diagnostic; Spatial–temporal transfer learning; Assembly process; FAHP; Complex product", "DOI": "10.1007/s00170-024-14433-7", "PubYear": 2024, "Volume": "135", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Software Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematical Science, Queensland University of Technology, Brisbane, Australia; Corresponding author."}], "References": [{"Title": "Digital twin-based assembly data management and process traceability for complex products", "Authors": "<PERSON><PERSON><PERSON>; Jing<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "118", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Recent advances in surface defect inspection of industrial products using deep learning techniques", "Authors": "<PERSON><PERSON>; <PERSON>; Yaguang <PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "1-2", "Page": "35", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "A New assembly precision prediction method of aeroengine high-pressure rotor system considering manufacturing error and deformation of parts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "112", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "An intelligent decision-making system for assembly process planning based on machine learning considering the variety of assembly unit and assembly process", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "1-2", "Page": "805", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Machine learning and deep learning based predictive quality in manufacturing: a systematic review", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "7", "Page": "1879", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Digital twin modeling", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "", "Page": "372", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Production quality prediction of multistage manufacturing systems using multi-task joint deep learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "70", "Issue": "", "Page": "48", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Coaxiality prediction for aeroengines precision assembly based on geometric distribution error model and point cloud deep learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "71", "Issue": "", "Page": "681", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Quality-integrated diagnostic platform for aerospace complex product assembly processes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "189", "Issue": "", "Page": "109796", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A dynamic feature selection-based data-driven quality prediction method for soft sensing in the diesel engine assembly system", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102433", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Time series prediction for production quality in a machining system using spatial-temporal multi-task graph learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "74", "Issue": "", "Page": "157", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Digital twin-driven assembly accuracy prediction method for high performance precision assembly of complex products", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "61", "Issue": "", "Page": "102495", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Onto-SAGCN: Ontology modeling and spatial attention-based graph convolution networks for aircraft assembly quality prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "60", "Issue": "", "Page": "102531", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 118179386, "Title": "Structure optimization and contact law study for transmission linkage of manipulator grasping fragile parts", "Abstract": "<p>According to the grasping requirements for fragile workpieces with cylindrical inner walls, a manipulator with internal bracing has been designed. The manipulator utilizes a constant-speed push cylinder to drive the linkage mechanism and accomplish the related works. Considering the fragile nature of the workpieces, reducing the impact velocity between the manipulator finger and the workpiece is a primary objective in the mechanism design. To achieve this, an optimization design is carried out, which maintains the overall average speed of the manipulator, while reducing the average speed only during the contact between the finger and the workpiece. The goal of optimization is to minimize the impact velocity at the contact point, using the linkage structure parameters as variables. The genetic algorithm (GA) is employed for the optimization process. The results show that optimization reduces the impact velocity at the contact point by 44.80%. To gain further insight into the influence of the process, a finite element model of a finger grasping a fragile object was created using joint modeling with the SolidWorks, Hyper Mesh, and LS-DYNA software. Simulations reveal that, after optimization, the maximum internal stress of three workpieces with thicknesses of 2.00 mm, 1.00 mm, and 0.50 mm was reduced by 47.42%, 41.53%, and 44.20%, respectively. To study the general law of the mechanical hand grasping fragile workpieces, the relationship between the cylinder driving speed and the impact speed is established. Based on the simulation results of maximum contact impact velocity, workpiece wall thickness, and maximum contact force, a prediction model of workpiece internal force is established using the Kriging prediction model theory. Additionally, the feasible range of grasping parameters under different wall thickness conditions is determined and discussed. The reliability of the designed structure is experimentally verified, providing valuable insights for ensuring the lifting work reliability of manipulators handling fragile workpieces.</p>", "Keywords": "", "DOI": "10.1177/17298806241286854", "PubYear": 2024, "Volume": "21", "Issue": "5", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Henan Provincial Key Laboratory of Intelligent Manufacturing of Mechanical Equipment, Zhengzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Henan Provincial Key Laboratory of Intelligent Manufacturing of Mechanical Equipment, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of International Education, Zhengzhou University of Light Industry, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Materials Science and Engineering, Zhengzhou University, Zhongyuan Critical Metals Laboratory, Zhengzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Henan Provincial Key Laboratory of Intelligent Manufacturing of Mechanical Equipment, Zhengzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Henan Provincial Key Laboratory of Intelligent Manufacturing of Mechanical Equipment, Zhengzhou, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Henan Provincial Key Laboratory of Intelligent Manufacturing of Mechanical Equipment, Zhengzhou, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Zhengzhou University of Light Industry, Henan Provincial Key Laboratory of Intelligent Manufacturing of Mechanical Equipment, Zhengzhou, China"}], "References": [{"Title": "Maximizing natural frequencies of inhomogeneous cellular structures by Kriging-assisted multiscale topology optimization", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "230", "Issue": "", "Page": "106197", "JournalTitle": "Computers & Structures"}, {"Title": "Evolutionary algorithms and their applications to engineering problems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "16", "Page": "12363", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Design and tests of a non-contact <PERSON><PERSON><PERSON> gripper for rough-surfaced and fragile objects gripping", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "40", "Issue": "5", "Page": "735", "JournalTitle": "Assembly Automation"}, {"Title": "A robotic grasping approach with elliptical cone-based potential fields under disturbed scenes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "1", "Page": "172988142098573", "JournalTitle": "International Journal of Advanced Robotic Systems"}, {"Title": "Observer-based boundary control for an asymmetric output-constrained flexible robotic manipulator", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "3", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Design and Feasibility Tests of a Lightweight Soft Gripper for Compliant and Flexible Envelope Grasping", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "9", "Issue": "2", "Page": "376", "JournalTitle": "Soft Robotics"}, {"Title": "Study of grasp-energy based optimal distribution of contact forces on a humanoid robotic hand during object grasp", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "5", "Page": "1501", "JournalTitle": "Robotica"}, {"Title": "A review of soft manipulator research, applications, and opportunities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "3", "Page": "281", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Distilled neural state-dependent Riccati equation feedback controller for dynamic control of a cable-driven continuum robot", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "20", "Issue": "3", "Page": "172988062311747", "JournalTitle": "International Journal of Advanced Robotic Systems"}]}, {"ArticleId": 118179400, "Title": "Serverless Computing in the Edge-Cloud Continuum : Challenges, Opportunities, and a Novel Framework", "Abstract": "This article explores the integration of serverless computing across the edge-cloud continuum, addressing the growing demand for low-latency, data-intensive applications in the era of the Internet of Things (IoT). We present EdgeServe, a novel framework that extends the serverless paradigm to encompass edge devices and cloud resources, overcoming the limitations of traditional cloud-centric approaches. Through comprehensive simulations and real-world case studies, including a smart city traffic management system, an industrial IoT predictive maintenance application, and a mobile augmented reality gaming platform, we evaluate the performance, scalability, and cost-effectiveness of our proposed framework. Our results demonstrate significant improvements in application response times, with latency reductions of up to 82% in time-critical functions, and an average cost reduction of 43% compared to cloud-only serverless deployments. We also observe a 28% decrease in overall energy consumption in certain scenarios. The article addresses key challenges in resource management, data consistency, adaptive function placement, and security in distributed environments. Our findings have important implications for application architects and cloud service providers, paving the way for a new generation of edge-native serverless platforms. This research contributes to the growing body of knowledge on distributed systems and offers insights into the future evolution of serverless computing in heterogeneous computing environments.", "Keywords": "Edge-Cloud Continuum;Serverless Computing;Distributed Function Placement;IoT Application Optimization;Adaptive Resource Management", "DOI": "10.32628/CSEIT241051010", "PubYear": 2024, "Volume": "10", "Issue": "5", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "McCombs School of Business, USA"}], "References": []}, {"ArticleId": 118179531, "Title": "Leveraging Reviews: Learning to Price with Buyer and <PERSON><PERSON> Uncertainty", "Abstract": "<p> Customers can access hundreds of reviews for a single product in online marketplaces. Buyers often use reviews from other customers that share their type---such as height for clothing or skin type for skincare products---to estimate their values, which they may not know a priori. Customers with few relevant reviews may hesitate to purchase except at a low price, so for the seller, there is a tension between setting high prices and ensuring that there are enough reviews so buyers can confidently estimate their values. Simultaneously, sellers may use reviews to gauge the demand for items they wish to sell. In this work, we study this pricing problem in an online setting where the seller interacts with a set of buyers of finitely many types, one by one, over a series of T rounds. At each round, the seller first sets a price. Then, a buyer arrives and examines the reviews of the previous buyers with the same type, which reveal those buyers' ex-post values. Based on the reviews, the buyer decides to purchase if they have good reason to believe their ex-ante utility is positive. Crucially, the seller does not know the buyer's type when setting the price, nor even the distribution over types. We provide a no-regret algorithm that the seller can use to obtain high revenue. When there are d types, after T rounds, our algorithm achieves a problem-independent Õ ( T <sup>2/3</sup> d <sup>1/3</sup> ) regret bound. However, when the smallest probability q <sub>min</sub> that any given type appears is large, specifically when q <sub>min</sub> ∈ Ω( d <sup>−2/3</sup> T <sup>−1/3</sup> ), the same algorithm achieves a [EQUATION] regret bound. We complement these upper bounds with matching lower bounds in both regimes, showing that our algorithm is minimax optimal up to lower-order terms. </p><p> This is a summary of work that won the Exemplary AI Track Paper Award at EC'24. </p>", "Keywords": "", "DOI": "10.1145/3699824.3699830", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of California, Berkeley"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of California, Berkeley"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Wisconsin, Madison"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Stanford University"}], "References": []}, {"ArticleId": 118179537, "Title": "Overview of Open Dataset Sessions and Benchmarking Competitions in 2022 - Part 1 (QoMEX 2022, ODS at MMSys '22)", "Abstract": "<p>In this Dataset Column, we present a review of some of the notable events related to open datasets and benchmarking competitions in the field of multimedia. This year's selection highlights the wide range of topics and datasets currently of interest to the community. Some of the events covered in this review include special sessions on open datasets and competitions featuring multimedia data. While this list is not exhaustive and contains an overview of about 40 datasets, it is meant to showcase the diversity of subjects and datasets explored in the field. This year's review follows similar efforts from the previous year (https://records.sigmm.org/2022/01/12/overview-of-open-dataset-sessions-and-benchmarking-competitions-in-2021/), highlighting the ongoing importance of open datasets and benchmarking competitions in advancing research and development in multimedia. The column is divided into three parts, in this one we focus on QoMEX 2022 and ODS at MMSys '22.</p>", "Keywords": "", "DOI": "10.1145/3699843.3699847", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 26893, "JournalTitle": "ACM SIGMultimedia Records", "ISSN": "", "EISSN": "1947-4598", "Authors": [], "References": []}, {"ArticleId": 118179583, "Title": "Efficient band reduction for hyperspectral imaging with dependency-based segmented principal component analysis", "Abstract": "", "Keywords": "", "DOI": "10.1080/01431161.2024.2408493", "PubYear": 2024, "Volume": "45", "Issue": "24", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Degree programs in Systems and Information Engineering, Graduate School of Science and Technology, University of Tsukuba, Tsukuba, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Degree programs in Systems and Information Engineering, Graduate School of Science and Technology, University of Tsukuba, Tsukuba, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering, Information and Systems, University of Tsukuba, Tsukuba, Japan"}], "References": []}, {"ArticleId": 118179628, "Title": "SIGecom Winter Meeting 2023 Highlights", "Abstract": "<p>The third annual ACM SIGecom Winter Meeting took place on February 22, 2023. Organized by <PERSON> and <PERSON>, this year's meeting brought together researchers from economics, computer science, and adjacent fields to focus on Web3, blockchains, and cryptocurrencies. The virtual meeting included talks from and discussions with leading experts on getting into the research space, interesting technical questions, and exciting challenges and opportunities that lie ahead. The day also included interactive exercises that gave participants the opportunity to gain hands-on experience and have fun with NFTs.</p>", "Keywords": "", "DOI": "10.1145/3699814.3699815", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Cornell University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Princeton University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "a16z crypto"}], "References": []}, {"ArticleId": *********, "Title": "Students Report from ACM Multimedia 2022", "Abstract": "<p>ACM Multimedia 2022 was held in a hybrid format in Lisbon, Portugal from October 10-14, 2022.</p><p>This was the first local participation in three years for many participants, as the strict travel restrictions associated with Covid-19 in 2020 and 2021 made it difficult to participate locally by travelling out of the host and neighbouring countries.</p><p>In Portugal, the Covid-19 restrictions were almost lifted, and the city was bustling with tourists. Participants were careful to avoid infectious diseases and enjoyed Lisbon's local wine \"Vinho Verde\" and cod dishes with their colleagues and engaged in lively discussions about multimedia research.</p><p>For many students, this was their first time presenting at an international conference, and it was a wonderful experience.</p><p>To encourage student authors to participate on-site, SIGMM has sponsored a group of students with Student Travel Grant Awards. Students who wanted to apply for this travel grant needed to submit an online form before the submission deadline. The selected students received either 1,000 or 2,000 USD to cover their airline tickets as well as accommodation costs for this event. Of the recipients, 25 were able to attend the conference. We asked them to share their unique experience attending ACM Multimedia 2022. In this article, we share their reports of the event.</p>", "Keywords": "", "DOI": "10.1145/3699843.3699845", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 26893, "JournalTitle": "ACM SIGMultimedia Records", "ISSN": "", "EISSN": "1947-4598", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Tokyo, Japan"}], "References": []}, {"ArticleId": *********, "Title": "Recent Trends in Information Elicitation", "Abstract": "<p> This note provides a survey for the Economics and Computation community of some recent trends in the field of information elicitation. At its core, the field concerns the design of incentives for strategic agents to provide accurate and truthful information. Such incentives are formalized as proper scoring rules , and turn out to be the same object as loss functions in machine-learning settings, providing many connections. More broadly, the field concerns the design of mechanisms to obtain information from groups of agents and aggregate it or use it for decision making. Recently, work on information elicitation has expanded and been connected to online no-regret learning, mechanism design, fair division, and more. </p>", "Keywords": "", "DOI": "10.1145/3699824.3699836", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder"}], "References": [{"Title": "Dominantly Truthful Peer Prediction Mechanisms with a Finite Number of Tasks", "Authors": "Yuqing Kong", "PubYear": 2024, "Volume": "71", "Issue": "2", "Page": "1", "JournalTitle": "Journal of the ACM"}]}, {"ArticleId": 118179817, "Title": "Working Memory Capacity for Mid-Air Gestures in Human–Computer Interaction", "Abstract": "", "Keywords": "", "DOI": "10.1080/10447318.2024.2411469", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "Xiao<PERSON> Zhou", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 2, "Name": "Zhengyang Lu", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "Ruidong Bai", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "China National Aeronautical Radio Electronics Research Institute, Science and Technology on Avionics Integration Laboratory, Shanghai, China"}], "References": [{"Title": "User-defined gesture interaction for in-vehicle information systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "Page": "263", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "User-Defined Gestures for Mid-Air Interaction: A Comparison of Upper Limb Muscle Activity, Wrist Kinematics, and Subjective Preference", "Authors": "<PERSON><PERSON> Huang; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "16", "Page": "1516", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Working Memory Capacity for Gesture-Command Associations in Gestural Interaction", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "15", "Page": "3045", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "H-GOMS: a model for evaluating a virtual-hand interaction system in virtual environments", "Authors": "<PERSON><PERSON> Zhou; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "2", "Page": "497", "JournalTitle": "Virtual Reality"}, {"Title": "Projected augmented reality assembly assistance system supporting multi-modal interaction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "3-4", "Page": "1353", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": *********, "Title": "Hospital Web Quality Multicriteria Analysis Model (HWQ): Development and Application Test in Spanish Hospitals", "Abstract": "<p>The Hospital Web Quality Multicriteria Analysis Model (HWQ) is constructed, designed, and validated in this research. For this purpose, we examined the web quality analysis models specialized in hospitals and health centers through a literature review and the most current taxonomies to analyze digital media. Based on the benchmarking and walkthrough methods, the analysis model was built and validated by a panel of experts (X = 3.54, CVI = 0.88, Score Σ = 45.58). To test its applicability and reliability, the model was pilot-tested on the websites of the ten public and private hospitals with the best reputation in Spain in 2022, according to the Merco Sanitario ranking. The results showed very similar web structures divided by specific proposals or sections of some centers. In this regard, this study identifies a general communication proposal in hospitals that does not adapt to the guidelines of screen-mediated communication, as well as a lack of personalization and disruptive storytelling ideation. In addition, the work concludes that Spanish hospitals, for the moment, have not opted for formats and technological developments derived from the possibilities of gamified content, 360° immersion, Virtual Reality (V.R), or Augmented Reality (A.R).</p>", "Keywords": "", "DOI": "10.3390/bdcc8100131", "PubYear": 2024, "Volume": "8", "Issue": "10", "JournalId": 41646, "JournalTitle": "Big Data and Cognitive Computing", "ISSN": "", "EISSN": "2504-2289", "Authors": [{"AuthorId": 1, "Name": "Santiago Tejedor", "Affiliation": "Department of Journalism and Communication Sciences, Autonomous University of Barcelona, 08193 Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Communication Sciences and Sociology, Rey Juan Carlos University, 28942 Madrid, Spain; ESAI Business School, Espiritu Santo University, Samborondón 092301, Ecuador"}], "References": [{"Title": "Objective Design to Subjective Evaluations: Connecting Visual Complexity to Aesthetic and Usability Assessments of eHealth", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "95", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Evaluating the accessibility of public health websites: An exploratory cross-country study", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "3", "Page": "771", "JournalTitle": "Universal Access in the Information Society"}]}, {"ArticleId": 118179869, "Title": "SIGCSE Speaker Fund Report: Funding for Pre-Conference Workshop at CCSC-CP 2024", "Abstract": "<p>On April 5, 2024, the SIGCSE Speaker Fund supported Mr. <PERSON> of Harvard University and Mr. <PERSON> of Yale University who conducted a 3-hour pre-conference workshop entitled \"Distributing, Collecting, and Autograding Assignments with GitHub Classroom\" at the Consortium for Computing Sciences in Colleges - Central Plains Region (CCSC-CP) Conference at Graceland University, Lamoni, Iowa. The presenters shared their knowledge, expertise, and classroom material (CS50 at Harvard) on GitHub Classroom with the audience. There were about 28 participants in the workshop. Mr. <PERSON>, Mr. <PERSON>, and other colleagues had previously presented and conducted the workshop at SIGCSE 2023 on March 17, 2023, in Toronto, Canada.</p>", "Keywords": "", "DOI": "10.1145/3699853.3699854", "PubYear": 2024, "Volume": "56", "Issue": "3", "JournalId": 19941, "JournalTitle": "ACM SIGCSE Bulletin", "ISSN": "0097-8418", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118180193, "Title": "Protectionism or let go? Evidence from IT investment in Chinese manufacturing firms", "Abstract": "", "Keywords": "", "DOI": "10.1080/1097198X.2024.2410677", "PubYear": 2024, "Volume": "27", "Issue": "4", "JournalId": 39199, "JournalTitle": "Journal of Global Information Technology Management", "ISSN": "1097-198X", "EISSN": "2333-6846", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Lanzhou University, Lanzhou, China"}, {"AuthorId": 2, "Name": "Hui Ji", "Affiliation": "School of Management, Lanzhou University, Lanzhou, China"}, {"AuthorId": 3, "Name": "Lianchao Yu", "Affiliation": "School of Management, Lanzhou University, Lanzhou, China"}], "References": [{"Title": "The Impact of Enterprise IT Investment on Corporate Performance: Evidence from China", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "176", "JournalTitle": "Journal of Global Information Technology Management"}, {"Title": "Cooperation of Cross-border E-commerce: A reputation and trust perspective", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "1", "Page": "7", "JournalTitle": "Journal of Global Information Technology Management"}, {"Title": "ICT Leapfrogging and Economic Growth Among SAARC Economies: Evidence From Method of Moments Quantile Regression", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "3", "Page": "230", "JournalTitle": "Journal of Global Information Technology Management"}]}, {"ArticleId": 118180261, "Title": "Instance Space Analysis of Testing of Autonomous Vehicles in Critical Scenarios", "Abstract": "<p> Before being deployed on roads, Autonomous Vehicles (AVs) must undergo comprehensive testing. Safety-critical situations, however, are infrequent in usual driving conditions, so simulated scenarios are used to create them. A test scenario comprises static and dynamic features related to the AV and the test environment; the representation of these features is complex and makes testing a heavy process. A test scenario is effective if it identifies incorrect behaviors of the AV. In this article, we present a technique for identifying key features of test scenarios associated with their effectiveness using Instance Space Analysis (ISA). ISA generates a ( \\(2D\\) ) representation of test scenarios and their features. This visualization helps to identify combinations of features that make a test scenario effective. We present a graphical representation of each feature that helps identify how well each testing technique explores the search space. While identifying key features is a primary goal, this study specifically seeks to determine the critical features that differentiate the performance of algorithms. Finally, we present metrics to assess the robustness of testing algorithms and the scenarios generated. Collecting essential features in combination with their values associated with effectiveness can be used for selection and prioritization of effective test cases. </p>", "Keywords": "", "DOI": "10.1145/3699596", "PubYear": 2025, "Volume": "34", "Issue": "3", "JournalId": 14907, "JournalTitle": "ACM Transactions on Software Engineering and Methodology", "ISSN": "1049-331X", "EISSN": "1557-7392", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information Technology, Monash University, Clayton, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Monash University, Clayton, Australia"}, {"AuthorId": 3, "Name": "Aldeida Aleti", "Affiliation": "Faculty of Information Technology, Monash University, Clayton, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of ITEE, University of Oulu, Oulu, Finland and Monash University, Clayton, Australia"}], "References": [{"Title": "E-APR: Mapping the effectiveness of automated program repair techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "5", "Page": "1", "JournalTitle": "Empirical Software Engineering"}, {"Title": "Single and Multi-objective Test Cases Prioritization for Self-driving Cars in Virtual Environments", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Parameter Coverage for Testing of Autonomous Driving Systems under Uncertainty", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Identifying and Explaining Safety-critical Scenarios for Autonomous Vehicles via Key Features", "Authors": "Neelofar Neelofar; Aldeida Aleti", "PubYear": 2024, "Volume": "33", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 118180344, "Title": "Users do not trust recommendations from a large language model more than AI-sourced snippets", "Abstract": "Background <p>The ability of large language models to generate general purpose natural language represents a significant step forward in creating systems able to augment a range of human endeavors. However, concerns have been raised about the potential for misplaced trust in the potentially hallucinatory outputs of these models.</p> Objectives <p>The study reported in this paper is a preliminary exploration of whether trust in the content of output generated by an LLM may be inflated in relation to other forms of ecologically valid, AI-sourced information.</p> Method <p>Participants were presented with a series of general knowledge questions and a recommended answer from an AI-assistant that had either been generated by an ChatGPT-3 or sourced by Google’s AI-powered featured snippets function. We also systematically varied whether the AI-assistant’s advice was accurate or inaccurate.</p> Results <p>Trust and reliance in LLM-generated recommendations were not significantly higher than that of recommendations from a non-LLM source. While accuracy of the recommendations resulted in a significant reduction in trust, this did not differ significantly by AI-application.</p> Conclusion <p>Using three predefined general knowledge tasks and fixed recommendation sets from the AI-assistant, we did not find evidence that trust in LLM-generated output is artificially inflated, or that people are more likely to miscalibrate their trust in this novel technology than another commonly drawn on form of AI-sourced information.</p>", "Keywords": "Trust1; Artificial Intelligence2; large language models3; trust calibration4; HCI5; generative AI6; ChatGPT-37; hallucination8", "DOI": "10.3389/fcomp.2024.1456098", "PubYear": 2024, "Volume": "6", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Commonwealth Scientific and Industrial Research Organisation (CSIRO), Australia; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Commonwealth Scientific and Industrial Research Organisation (CSIRO), Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Commonwealth Scientific and Industrial Research Organisation (CSIRO), Australia"}], "References": [{"Title": "Survey of Hallucination in Natural Language Generation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "12", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": *********, "Title": "Attribute reduction based on directional semi-neighborhood rough set", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13042-024-02406-x", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Discernible neighborhood counting based incremental feature selection for heterogeneous data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1115", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "An improvement of rough sets’ accuracy measure using containment neighborhoods with a medical application", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "569", "Issue": "", "Page": "110", "JournalTitle": "Information Sciences"}, {"Title": "Dynamic fuzzy neighborhood rough set approach for interval-valued information systems with fuzzy decision", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107679", "JournalTitle": "Applied Soft Computing"}, {"Title": "A data-level fusion model for unsupervised attribute selection in multi-source homogeneous data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "87", "JournalTitle": "Information Fusion"}, {"Title": "Neighborhood rough set-based multi-attribute prediction approach and its application of gout patients", "Authors": "Juncheng Bai; Bingzhen Sun; <PERSON><PERSON>", "PubYear": 2022, "Volume": "114", "Issue": "", "Page": "108127", "JournalTitle": "Applied Soft Computing"}, {"Title": "Fusing attribute reduction accelerators", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "587", "Issue": "", "Page": "354", "JournalTitle": "Information Sciences"}, {"Title": "Multi-label feature selection based on fuzzy neighborhood rough sets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "3", "Page": "2105", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Covering rough set-based incremental feature selection for mixed decision system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "6", "Page": "2651", "JournalTitle": "Soft Computing"}, {"Title": "ASFS: A novel streaming feature selection for multi-label data based on neighborhood rough set", "Authors": "<PERSON><PERSON> Liu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "2", "Page": "1707", "JournalTitle": "Applied Intelligence"}, {"Title": "A new approach to generalized neighborhood system-based rough sets via convex structures and convex matroids", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "612", "Issue": "", "Page": "1187", "JournalTitle": "Information Sciences"}, {"Title": "Noise-resistant multilabel fuzzy neighborhood rough sets for feature subset selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "621", "Issue": "", "Page": "200", "JournalTitle": "Information Sciences"}, {"Title": "Some neighborhood-related fuzzy covering-based rough set models and their applications for decision making", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "621", "Issue": "", "Page": "799", "JournalTitle": "Information Sciences"}, {"Title": "A soft neighborhood rough set model and its applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "185", "JournalTitle": "Information Sciences"}, {"Title": "TFSFB: Two-stage feature selection via fusing fuzzy multi-neighborhood rough set with binary whale optimization for imbalanced data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "91", "JournalTitle": "Information Fusion"}, {"Title": "Glee: A granularity filter for feature selection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "122", "Issue": "", "Page": "106080", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A class-specific feature selection and classification approach using neighborhood rough set and K-nearest neighbor theories", "Authors": "<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "110366", "JournalTitle": "Applied Soft Computing"}, {"Title": "Attribute reduction based on neighborhood constrained fuzzy rough sets", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "110632", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Fuzzy rough feature selection using a robust non-linear vague quantifier for ordinal classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "230", "Issue": "", "Page": "120480", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Feature selection in threes: Neighborhood relevancy, redundancy, and granularity interactivity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "146", "Issue": "", "Page": "110679", "JournalTitle": "Applied Soft Computing"}, {"Title": "Covering based multi-granulation rough fuzzy sets with applications to feature selection", "Authors": "<PERSON><PERSON><PERSON><PERSON> Huang; <PERSON><PERSON> Li", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121908", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A new method for feature selection based on weighted k -nearest neighborhood rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122324", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A multi-scale information fusion-based multiple correlations for unsupervised attribute selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "106", "Issue": "", "Page": "102276", "JournalTitle": "Information Fusion"}, {"Title": "Semi-supervised feature selection by minimum neighborhood redundancy and maximum neighborhood relevancy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "54", "Issue": "17-18", "Page": "7750", "JournalTitle": "Applied Intelligence"}, {"Title": "A novel adaptive neighborhood rough sets based on sparrow search algorithm and feature selection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "679", "Issue": "", "Page": "121099", "JournalTitle": "Information Sciences"}, {"Title": "Attribute Reduction Using Self-information Uncertainty Measures in Optimistic Neighborhood Extreme-granulation Rough Set", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "686", "Issue": "", "Page": "121340", "JournalTitle": "Information Sciences"}, {"Title": "Rapid and optimized parallel attribute reduction based on neighborhood rough sets and MapReduce", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "260", "Issue": "", "Page": "125323", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Collaborative graph neural networks for augmented graphs: A local-to-global perspective", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2025, "Volume": "158", "Issue": "", "Page": "111020", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 118180646, "Title": "Multi-robot task allocation for optional tasks with hidden workload: Using a model-based hyper-heuristic strategy", "Abstract": "Multi-robot task allocation (MRTA) is a classical problem in multi-robot systems. This paper analyzes the situations where the objective of the robots is to minimize the time cost of completing a certain proportion of tasks instead of completing all tasks, i.e., the tasks are optional. Besides, in this problem, the true workload of each task is initially hidden and can only be known after the preliminary workload is completed. As the tasks are optional, selecting a suitable combination of the tasks is quite important. The main challenge in this problem is that the robots cannot exactly select the tasks that are easy to complete because the true workload is hidden. In previous similar problems (i.e., MRTA with incomplete information), reallocation-based method is a general method. However, in this problem, if the robots exactly reallocate the tasks after collecting enough information, the sunk costs (i.e., the tasks that have been partially performed but are not selected in reallocation) limit the decrease of the time cost. Thus, we design a new hyper-heuristic strategy. In detail, a simple heuristic method that lets the robots appropriately give up some allocated tasks is combined with reallocation to design the low-level heuristic (LLH). The high-level strategy (HLS) seeks the optimal setting of LLH based on a meta-heuristic algorithm <sup>1</sup> . The strategies are tested based on various random instances, and the hyper-heuristic strategy can outperform the benchmark strategies in most instances. In some instances, the maximum improvement of results led by the hyper-heuristic is more than 9%.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109423", "PubYear": 2024, "Volume": "138", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China;Key Laboratory of Big Data Intelligent Computing, Chongqing University of Posts and Telecommunications, Chongqing, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Southeast University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Computational Intelligence, Chongqing University of Posts and Telecommunications, Chongqing, China"}], "References": [{"Title": "An evolutionary hyper-heuristic to optimise deep belief networks for image reconstruction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "105510", "JournalTitle": "Applied Soft Computing"}, {"Title": "Auctions for multi-robot task allocation in communication limited environments", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "3-4", "Page": "547", "JournalTitle": "Autonomous Robots"}, {"Title": "An effective discrete artificial bee colony algorithm for multi-AGVs dispatching problem in a matrix manufacturing workshop", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113675", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A hybrid many-objective competitive swarm optimization algorithm for large-scale multirobot task allocation problem", "Authors": "<PERSON><PERSON>; Tingting Dong; Siqing You", "PubYear": 2021, "Volume": "12", "Issue": "4", "Page": "943", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Towards addressing dynamic multi-agent task allocation in law enforcement", "Authors": "<PERSON><PERSON>; Sofia Amador", "PubYear": 2021, "Volume": "35", "Issue": "1", "Page": "1", "JournalTitle": "Autonomous Agents and Multi-Agent Systems"}, {"Title": "The object-oriented dynamic task assignment for unmanned surface vessels", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "104476", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A mixed closed-open multi-depot routing and scheduling problem for homemade meal delivery incorporating drone and crowd-sourced fleet: A self-adaptive hyper-heuristic approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "120", "Issue": "", "Page": "105876", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Green location routing problem with flexible multi-compartment for source-separated waste: A Q-learning and multi-strategy-based hyper-heuristic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "121", "Issue": "", "Page": "105954", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Solving the Multi-robot task allocation with functional tasks based on a hyper-heuristic algorithm", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "146", "Issue": "", "Page": "110628", "JournalTitle": "Applied Soft Computing"}, {"Title": "An effective collaboration evolutionary algorithm for multi-robot task allocation and scheduling in a smart farm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; JC Ji", "PubYear": 2024, "Volume": "289", "Issue": "", "Page": "111474", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": *********, "Title": "Robustness of linear time‐invariant systems with feedback control protocols under random disturbances", "Abstract": "", "Keywords": "", "DOI": "10.1002/asjc.3513", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Huangshi Key Laboratory of Metaverse and Virtual Simulation, School of Mathematics and Statistics Hubei Normal University  Huangshi Hubei China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Huangshi Key Laboratory of Metaverse and Virtual Simulation, School of Mathematics and Statistics Hubei Normal University  Huangshi Hubei China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Fang", "Affiliation": "Huangshi Key Laboratory of Metaverse and Virtual Simulation, School of Mathematics and Statistics Hubei Normal University  Huangshi Hubei China"}], "References": [{"Title": "Output feedback controller design for discrete LTI systems with polytopic uncertainty via direct searching of the design space", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "6", "Page": "3080", "JournalTitle": "Asian Journal of Control"}, {"Title": "Secure output synchronization of heterogeneous multi-agent systems against false data injection attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "6", "Page": "1", "JournalTitle": "Science China Information Sciences"}]}, {"ArticleId": 118180788, "Title": "A Decision Framework for Selecting Emergency Logistics Suppliers Based on an Extended PHFS-TODIM Method", "Abstract": "", "Keywords": "", "DOI": "10.1007/s40815-024-01848-3", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Uncertainty measures for probabilistic hesitant fuzzy sets in multiple criteria decision making", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "11", "Page": "1646", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Interval Type-2 Fuzzy Cognitive Map-Based Flight Control System for Quadcopters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "8", "Page": "2504", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "TODIM Dynamic Emergency Decision-Making Method Based on Hybrid Weighted Distance Under Probabilistic Hesitant Fuzzy Information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "474", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Extended Cumulative Residual Entropy for Emergency Group Decision-Making Under Probabilistic Hesitant Fuzzy Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "1", "Page": "159", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Identification and prioritization of strategies to tackle COVID-19 outbreak: A group-BWM based MCDM approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107642", "JournalTitle": "Applied Soft Computing"}, {"Title": "The logistics service providers during the COVID-19 pandemic: The prominence and the cause-effect structure of uncertainties and risks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "165", "Issue": "", "Page": "107950", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A comprehensive framework and literature review of supplier selection under different purchasing strategies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "108010", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Research on the Selection of Green Cold Chain Logistics Service Providers Based on Combined Weighting-Cloud Model", "Authors": "Weifeng Sun; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "214", "Issue": "", "Page": "1409", "JournalTitle": "Procedia Computer Science"}, {"Title": "Implementation of art therapy assisted by the internet of medical things based on blockchain and fuzzy set theory", "Authors": "Xiaofeng Lu", "PubYear": 2023, "Volume": "632", "Issue": "", "Page": "776", "JournalTitle": "Information Sciences"}, {"Title": "A fuzzy multiobjective team decision model for CODP and supplier selection in customized logistics service supply chain", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "237", "Issue": "", "Page": "121387", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Supplier selection under disruption risk with hybrid procurement", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2024, "Volume": "165", "Issue": "", "Page": "106593", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": 118180933, "Title": "HardTaint: Production-Run Dynamic Taint Analysis via Selective Hardware Tracing", "Abstract": "<p>Dynamic taint analysis (DTA), as a fundamental analysis technique, is widely used in security, privacy, and diagnosis, etc. As DTA demands to collect and analyze massive taint data online, it suffers extremely high runtime overhead. Over the past decades, numerous attempts have been made to lower the overhead of DTA. Unfortunately, the reductions they achieved are marginal, causing DTA only applicable to the debugging/testing scenarios. In this paper, we propose and implement HardTaint, a system that can realize production-run dynamic taint tracking. HardTaint adopts a hybrid and systematic design which combines static analysis, selective hardware tracing and parallel graph processing techniques. The comprehensive evaluations demonstrate that HardTaint introduces only around 8% runtime overhead which is an order of magnitude lower than the state-of-the-arts, while without sacrificing any taint detection capability.</p>", "Keywords": "", "DOI": "10.1145/3689768", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 4, "Name": "Yun <PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 5, "Name": "Kai <PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University, Nanjing, China"}], "References": []}, {"ArticleId": 118180942, "Title": "HybridSA: GPU Acceleration of Multi-pattern Regex Matching using Bit Parallelism", "Abstract": "<p>Multi-pattern matching is widely used in modern software for applications requiring high throughput such as protein search, network traffic inspection, virus or spam detection. Graphics Processor Units (GPUs) excel at executing massively parallel workloads. Regular expression (regex) matching is typically performed by simulating the execution of deterministic finite automata (DFAs) or nondeterministic finite automata (NFAs). The natural implementations of these automata simulation algorithms on GPUs are highly inefficient because they give rise to irregular memory access patterns. This paper presents HybridSA, a heterogeneous CPU-GPU parallel engine for multi-pattern matching. HybridSA uses bit parallelism to efficiently simulate NFAs on GPUs, thus reducing the number of memory accesses and increasing the throughput. Our bit-parallel algorithms extend the classical shift-and algorithm for string matching to a large class of regular expressions and reduce automata simulation to a small number of bitwise operations. We have developed a compiler to translate regular expressions into bit masks, perform optimizations, and choose the best algorithms to run on the GPU. The majority of the regular expressions are accelerated on the GPU, while the patterns that exhibit random memory accesses are executed on the CPU in parallel. We evaluate HybridSA against state-of-the-art CPU and GPU engines, as well as a hybrid combination of the two. HybridSA achieves between 4 and 60 times higher throughput than the state-of-the-art CPU engine and between 4 and 233 times better than the state-of-the-art GPU engine across a collection of real-world benchmarks.</p>", "Keywords": "", "DOI": "10.1145/3689771", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Rice University, Houston, USA"}, {"AuthorId": 2, "Name": "Lingkun Kong", "Affiliation": "Rice University, Houston, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Rice University, Houston, USA"}], "References": [{"Title": "Asynchronous Automata Processing on GPUs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Measurement and Analysis of Computing Systems"}, {"Title": "Regular Expression Matching using Bit Vector Automata", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA1", "Page": "492", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Efficient Matching of Regular Expressions with Lookaround Assertions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>ttopadhya<PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "Page": "2761", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Static Analysis for Checking the Disambiguation Robustness of Regular Expressions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "PLDI", "Page": "2073", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118180948, "Title": "Type Inference Logics", "Abstract": "<p>Type inference is essential for statically-typed languages such as OCaml and Haskell. It can be decomposed into two (possibly interleaved) phases: a generator converts programs to constraints; a solver decides whether a constraint is satisfiable. Elaboration, the task of decorating a program with explicit type annotations, can also be structured in this way. Unfortunately, most machine-checked implementations of type inference do not follow this phase-separated, constraint-based approach. Those that do are rarely executable, lack effectful abstractions, and do not include elaboration. To close the gap between common practice in real-world implementations and mechanizations inside proof assistants, we propose an approach that enables modular reasoning about monadic constraint generation in the presence of elaboration. Our approach includes a domain-specific base logic for reasoning about metavariables and a program logic that allows us to reason abstractly about the meaning of constraints. To evaluate it, we report on a machine-checked implementation of our techniques inside the Coq proof assistant. As a case study, we verify both soundness and completeness for three elaborating type inferencers for the simply typed lambda calculus with Booleans. Our results are the first demonstration that type inference algorithms can be verified in the same form as they are implemented in practice: in an imperative style, modularly decomposed into constraint generation and solving, and delivering elaborated terms to the remainder of the compiler chain.</p>", "Keywords": "", "DOI": "10.1145/3689786", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "KU Leuven, Leuven, Belgium"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Inria, Paris, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Vrije Universiteit Brussel, Brussels, Belgium"}], "References": [{"Title": "Dijkstra monads forever: termination-sensitive specifications for interaction trees", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Sikkel: Multimode Simple Type Theory as an Agda Library", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "360", "Issue": "", "Page": "93", "JournalTitle": "Electronic Proceedings in Theoretical Computer Science"}, {"Title": "Verified symbolic execution with Kripke specification monads (and no meta-programming)", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "ICFP", "Page": "194", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Intrinsically-typed definitional interpreters à la carte", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA2", "Page": "1903", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "PureCake: A Verified Compiler for a Lazy Functional Language", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "952", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Type Inference Logics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "Page": "2125", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118180957, "Title": "Efficient mapping of phase diagrams with conditional Boltzmann Generators", "Abstract": "<p>The accurate prediction of phase diagrams is of central importance for both the fundamental understanding of materials as well as for technological applications in material sciences. However, the computational prediction of the relative stability between phases based on their free energy is a daunting task, as traditional free energy estimators require a large amount of simulation data to obtain uncorrelated equilibrium samples over a grid of thermodynamic states. In this work, we develop deep generative machine learning models based on the Boltzmann Generator approach for entire phase diagrams, employing normalizing flows conditioned on the thermodynamic states, e.g., temperature and pressure, that they map to. By training a single normalizing flow to transform the equilibrium distribution sampled at only one reference thermodynamic state to a wide range of target temperatures and pressures, we can efficiently generate equilibrium samples across the entire phase diagram. Using a permutation-equivariant architecture allows us, thereby, to treat solid and liquid phases on the same footing. We demonstrate our approach by predicting the solid-liquid coexistence line for a Lennard-Jones system in excellent agreement with state-of-the-art free energy methods while significantly reducing the number of energy evaluations needed.</p>", "Keywords": "", "DOI": "10.1088/2632-2153/ad849d", "PubYear": 2024, "Volume": "5", "Issue": "4", "JournalId": 72803, "JournalTitle": "Machine Learning: Science and Technology", "ISSN": "", "EISSN": "2632-2153", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Normalizing flows for atomic solids", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "2", "Page": "025009", "JournalTitle": "Machine Learning: Science and Technology"}, {"Title": "Estimating Gibbs free energies via isobaric-isothermal flows", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "3", "Page": "035039", "JournalTitle": "Machine Learning: Science and Technology"}, {"Title": "Conditioning <PERSON><PERSON>mann generators for rare event sampling", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "3", "Page": "035050", "JournalTitle": "Machine Learning: Science and Technology"}]}, {"ArticleId": 118180991, "Title": "Toward stable astronaut following of extravehicular activity assistant robots using deep reinforcement learning", "Abstract": "<p>The use of mobile robots for assisting astronauts in extravehicular activities could be an effective option for improving mission productivity and crew safety. It is thus critical that these robots follow the astronaut and maintain a stable distance to provide personalized and timely assistance. However, most extraterrestrial bodies exhibit rugged terrain that can impede a robot’s movements. As such, a novel predictive-guide following strategy is proposed to improve the stability of astronaut–robot distance in obstructive environments. This strategy combines a deep reinforcement learning navigator and a Kalman filter-based predictor to generate optimized motion sequences for safely following the astronaut and acquire predictive guidance concerning future astronaut movements. The proposed model achieved a success rate of 95.0% in simulated navigation tasks and adapted well to untrained complex environments and varied robot movement settings. Comparative tests indicated our strategy managed to stabilize the following distance to within ±1.0 m of the reference value in obstructed environments, significantly outperforming other following strategies. The feasibility and advantage of the proposed approach was validated with a physical robotic follower in a Mars-like environment.</p><p>[Formula: see text]</p>", "Keywords": "", "DOI": "10.1177/17298806221108606", "PubYear": 2022, "Volume": "19", "Issue": "3", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Aerospace Science and Engineering, National University of Defense Technology, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Control Science and Engineering, Zhejiang University, Hangzhou, China"}, {"AuthorId": 3, "Name": "Chuanxiang Li", "Affiliation": "College of Control Science and Engineering, Zhejiang University, Hangzhou, China"}], "References": [{"Title": "Efficient Hybrid-Supervised Deep Reinforcement Learning for Person Following Robot", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "2", "Page": "299", "JournalTitle": "Journal of Intelligent & Robotic Systems"}]}, {"ArticleId": 118181130, "Title": "Unified Fault-Tolerant Control and Adaptive Velocity Planning for 4WID-4WIS Vehicles under Multi-Fault Scenarios", "Abstract": "<p>Four-wheel independent drive and four-wheel independent steering (4WID-4WIS) vehicles provide increased redundancy in fault-tolerant control (FTC) schemes, enhancing heterogeneous fault-tolerant capabilities. This paper addresses the challenge of maintaining vehicle safety and maneuverability in the presence of actuator faults in autonomous vehicles, focusing on 4WID-4WIS systems. A novel unified hierarchical active FTC strategy is proposed to handle various actuator failures. The strategy includes an upper-layer motion controller that determines resultant force requirements based on trajectory tracking errors and a middle-layer allocation system that redistributes tire forces to fault-free actuators using fault information. This study, for the first time, considers multi-fault scenarios involving longitudinal and lateral coupling, calculating FTC boundaries for each fault type. Additionally, a fault tolerance index is introduced for 256 fault scenarios, using singular value decomposition to linearly represent the vehicle attainable force domain. Based on this, an adaptive velocity planning strategy is developed to balance safety and maneuverability under fault conditions. Matlab 2021a/Simulink and Carsim 2019 co-simulation results validate the proposed strategies, demonstrating significant improvements in fault-tolerant performance, particularly in complex and emergency scenarios.</p>", "Keywords": "", "DOI": "10.3390/act13100407", "PubYear": 2024, "Volume": "13", "Issue": "10", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The State Key Laboratory of Intelligent Green Vehicle and Mobility, School of Vehicle and Mobility, Tsinghua University, Beijing 100084, China"}, {"AuthorId": 2, "Name": "Guangyu Tian", "Affiliation": "The State Key Laboratory of Intelligent Green Vehicle and Mobility, School of Vehicle and Mobility, Tsinghua University, Beijing 100084, China"}], "References": [{"Title": "Toward Automated Vehicle Control Beyond the Stability Limits: Drifting Along a General Path", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "2", "Page": "", "JournalTitle": "Journal of Dynamic Systems, Measurement, and Control"}, {"Title": "Fault-Tolerant Control of Skid Steering Vehicles Based on Meta-Reinforcement Learning with Situation Embedding", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "3", "Page": "72", "JournalTitle": "Actuators"}, {"Title": "Research on Fault-Tolerant Control of Distributed-Drive Electric Vehicles Based on Fuzzy Fault Diagnosis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "6", "Page": "246", "JournalTitle": "Actuators"}, {"Title": "Research on Path Tracking Fault-Tolerant Control Strategy for Intelligent Commercial Vehicles Based on Brake Actuator Failure", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "13", "Issue": "3", "Page": "97", "JournalTitle": "Actuators"}]}, {"ArticleId": 118181170, "Title": "Utilizing LSTM-GRU for IOT-Based Water Level Prediction Using Multi-Variable Rainfall Time Series Data", "Abstract": "<p>This research describes experiments using LSTM, GRU models, and a combination of both to predict floods in Semarang based on time series data. The results show that the LSTM model is superior in capturing long-term dependencies, while GRU is better in processing short-term patterns. By combining the strengths of both models, this hybrid approach achieves better accuracy and robustness in flood prediction. The LSTM-GRU hybrid model outperforms the individual models, providing a more reliable prediction framework. This performance improvement is due to the complementary strengths of LSTM and GRU in handling various aspects of time series data. These findings emphasize the potential of advanced neural network models in addressing complex environmental challenges, paving the way for more effective flood management strategies in Semarang. The performance graph of the LSTM, GRU, and LSTM-GRU models in various scenarios shows significant differences in the performance of predicting river water levels based on rainfall input. The MAPE, MSE, RMSE, and MAD metrics are presented for training and validation data in six scenarios. Overall, the GRU model and the LSTM-GRU combination provide good performance when using more complete input variables, namely, downstream and upstream rainfall, compared to only using downstream rainfall.</p>", "Keywords": "", "DOI": "10.3390/informatics11040073", "PubYear": 2024, "Volume": "11", "Issue": "4", "JournalId": 40001, "JournalTitle": "Informatics", "ISSN": "", "EISSN": "2227-9709", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics Engineering, Faculty of Information Technology, Satya Wacana Christian University, Salatiga 50711, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics Engineering, Faculty of Information Technology, Satya Wacana Christian University, Salatiga 50711, Indonesia"}], "References": [{"Title": "Interpretable spatio-temporal attention LSTM model for flood forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "403", "Issue": "", "Page": "348", "JournalTitle": "Neurocomputing"}, {"Title": "Deep Learning for Time Series Forecasting: Advances and Open Problems", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "11", "Page": "598", "JournalTitle": "Information"}]}, {"ArticleId": 118181203, "Title": "Edge-assisted U-Shaped Split Federated Learning with privacy-preserving for Internet of Things", "Abstract": "In the realm of the Internet of Things (IoT), deploying deep learning models to process data generated or collected by IoT devices is a critical challenge. However, direct data transmission can cause network congestion and inefficient execution, given that IoT devices typically lack computation and communication capabilities. Centralized data processing in data centers is also no longer feasible due to concerns over data privacy and security. To address these challenges, we present an innovative Edge-assisted U-Shaped Split Federated Learning (EUSFL) framework, which harnesses the high-performance capabilities of edge servers to assist IoT devices in model training and optimization process. In this framework, we leverage Federated Learning (FL) to enable data holders to collaboratively train models without sharing their data, thereby enhancing data privacy protection by transmitting only model parameters. Additionally, inspired by Split Learning (SL), we split the neural network into three parts using U-shaped splitting for local training on IoT devices. By exploiting the greater computation capability of edge servers, our framework effectively reduces overall training time and allows IoT devices with varying capabilities to perform training tasks efficiently. Furthermore, we proposed a novel noise mechanism called LabelDP to ensure that data features and labels can securely resist reconstruction attacks, eliminating the risk of privacy leakage. Our theoretical analysis and experimental results demonstrate that EUSFL can be integrated with various aggregation algorithms, maintaining good performance across different computing capabilities of IoT devices, and significantly reducing training time and local computation overhead.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.125494", "PubYear": 2025, "Volume": "262", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Beijing Wuzi University, Beijing, 101149, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Beijing Wuzi University, Beijing, 101149, China;Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, 100124, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information, Beijing Wuzi University, Beijing, 101149, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Beijing Wuzi University, Beijing, 101149, China"}, {"AuthorId": 6, "Name": "Siqing You", "Affiliation": "School of Information, Beijing Wuzi University, Beijing, 101149, China;Corresponding author"}], "References": [{"Title": "Advances and Open Problems in Federated Learning", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1–2", "Page": "1", "JournalTitle": "Foundations and Trends® in Machine Learning"}, {"Title": "A survey of federated learning for edge computing: Research problems and solutions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100008", "JournalTitle": "High-Confidence Computing"}, {"Title": "A framework for privacy-preservation of IoT healthcare data using Federated Learning and blockchain technology", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "380", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "ARES: Adaptive Resource-Aware Split Learning for Internet of Things", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "218", "Issue": "", "Page": "109380", "JournalTitle": "Computer Networks"}, {"Title": "Internet of Thing (IoT) review of review: Bibliometric overview since its foundation", "Authors": "Abolghasem Sadeghi-<PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "361", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "FedSL: Federated split learning on distributed sequential data in recurrent neural networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "10", "Page": "28891", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Comprehensive review on congestion detection, alleviation, and control for IoT networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>.", "PubYear": 2024, "Volume": "221", "Issue": "", "Page": "103749", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Federated split learning for sequential data in satellite–terrestrial integrated networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "103", "Issue": "", "Page": "102141", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 118181856, "Title": "Formalising the Double-Pushout Approach to Graph Transformation", "Abstract": "<p>In this paper, we utilize Isabelle/HOL to develop a formal framework for the basic theory of double-pushout graph transformation. Our work includes defining essential concepts like graphs, morphisms, pushouts, and pullbacks, and demonstrating their properties. We establish the uniqueness of derivations, drawing upon <PERSON> 1975 research, and verify the <PERSON><PERSON><PERSON> theorem using <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> 1976 proof, thereby demonstrating the effectiveness of our formalisation approach. The paper details our methodology in employing Isabelle/HOL, including key design decisions that shaped the current iteration. We explore the technical complexities involved in applying higher-order logic, aiming to give readers an insightful perspective into the engaging aspects of working with an Interactive Theorem Prover. This work emphasizes the increasing importance of formal verification tools in clarifying complex mathematical concepts.</p>", "Keywords": "Computer Science - Logic in Computer Science", "DOI": "10.46298/lmcs-20(4:3)2024", "PubYear": 2024, "Volume": "20, Issue 4", "Issue": "", "JournalId": 10075, "JournalTitle": "Logical Methods in Computer Science", "ISSN": "", "EISSN": "1860-5974", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118181869, "Title": "Special Issue on Conversational Information Seeking", "Abstract": "<p>In this article, we provide an overview of ACM TWEB’s Special Issue on Conversational Information Seeking. It highlights both research and practical applications in this field. The article also discusses the future potential of conversational information seeking technology.</p>", "Keywords": "", "DOI": "10.1145/3688392", "PubYear": 2024, "Volume": "18", "Issue": "4", "JournalId": 10867, "JournalTitle": "ACM Transactions on the Web", "ISSN": "1559-1131", "EISSN": "1559-114X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science, Sichuan University, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hefei University of Technology, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Microsoft, Redmond, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "PolyAI, London United Kingdom of Great Britain and Northern Ireland"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Amazon AWS AI/ML, Seattle, United States"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University College London, London United Kingdom of Great Britain and Northern Ireland"}], "References": [{"Title": "Conversational Search and Recommendation: Introduction to the Special Issue", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Caching Historical Embeddings in Conversational Search", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}, {"Title": "User Experience and The Role of Personalization in Critiquing-Based Conversational Recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}, {"Title": "Conversational Information Seeking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "17", "Issue": "3-4", "Page": "244", "JournalTitle": "Foundations and Trends® in Information Retrieval"}, {"Title": "Multi-stage reasoning on introspecting and revising bias for visual question answering", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}, {"Title": "OPERA: Harmonizing Task-Oriented Dialogs and Information Seeking Experience", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}, {"Title": "Edge Caching Placement Strategy based on Evolutionary Game for Conversational Information Seeking in Edge Cloud Computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "18", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on the Web"}]}, {"ArticleId": 118181897, "Title": "Boost clustering with Gaussian Boson <PERSON>: a quantum-classical hybrid approach", "Abstract": "<p>Gaussian Boson Sampling (GBS) is a recently developed paradigm of quantum computing consisting of sending a Gaussian state through a linear interferometer and then counting the number of photons in each output mode. When the system encodes a symmetric matrix, GBS can be viewed as a tool to sample subgraphs: the most sampled are those with a large number of perfect matchings, and thus are the densest ones. This property has been the foundation of the novel clustering approach we propose in this work, called GBS-based clustering , which relies solely on GBS, without the need of classical algorithms. The GBS-based clustering has been tested on several datasets and benchmarked with two well-known classical clustering algorithms. Results obtained by using a GBS simulator show that on average, our approach outperforms the two classical algorithms in two out of the three chosen metrics, proposing itself as a viable quantum-classical hybrid clustering option.</p>", "Keywords": "Quantum computing; Quantum photonics; Gaussian <PERSON>; Clustering; Graph theory", "DOI": "10.1007/s42484-024-00185-w", "PubYear": 2024, "Volume": "6", "Issue": "2", "JournalId": 64156, "JournalTitle": "Quantum Machine Intelligence", "ISSN": "2524-4906", "EISSN": "2524-4914", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Data Reply s.r.l., Turin, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Data Reply s.r.l., Turin, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Enel Global Services s.r.l., Rome, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Enel Global Services s.r.l., Rome, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Data Reply s.r.l., Turin, Italy; Corresponding author."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Data Reply s.r.l., Turin, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Data Reply s.r.l., Turin, Italy"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Enel Global Services s.r.l., Rome, Italy"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Enel Global Services s.r.l., Rome, Italy"}], "References": []}, {"ArticleId": 118181941, "Title": "Enhancing Data Integrity with Efficient Retention-Refilling Programming Schemes", "Abstract": "<p> The retention error has become one of the most challenging reliability issues of flash memory due to the shrinking of the technology nodes. To enhance data integrity by resolving the retention error issues for 3D MLC flash memory devices (e.g., SSDs and SD cards), many excellent works that exploited in-place reprogramming and data refreshing concepts have been proposed in recent years. However, these approaches could result in additional issues, such as programming disturbance and performance overhead (e.g., unavoidable data refreshing and a larger amount of program and verify shots). This work is motivated by the need to explore a low-cost solution to resolve retention error issues without incurring negative impacts caused by conventional refresh-based and in-place reprogramming approaches. As a result, this work exploits the characteristics of the cell's V <sub>t</sub> distribution and proposes the novel concept of \"retention-refilling\" to enhance data integrity. With such an idea, a retention-refillable programming scheme is proposed to improve flash reliability and mitigate performance overheads by trading data refreshing with retention-refilling. The capability of the proposed scheme is evaluated by a series of experiments, for which we have very encouraging results. </p>", "Keywords": "", "DOI": "10.1145/3699839.3699842", "PubYear": 2024, "Volume": "24", "Issue": "3", "JournalId": 18042, "JournalTitle": "ACM SIGAPP Applied Computing Review", "ISSN": "1559-6915", "EISSN": "1931-0161", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Tsing Hua University, Hsinchu, Taiwan (R.O.C)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Macronix international Co., LTD., Hsinchu, Taiwan (R.O.C)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Massachusetts Institute of Technology, Massachusetts, U.S.A."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Tsing Hua University, Hsinchu, Taiwan (R.O.C)"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Cheng Kung University, Tainan, Taiwan (R.O.C)"}], "References": []}, {"ArticleId": 118181944, "Title": "SIGecom Job Market Candidate Profiles 2024", "Abstract": "<p>This is the ninth annual collection of profiles of the junior faculty job market candidates of the SIGecom community. The twenty seven candidates for 2024 are listed alphabetically and indexed by research areas that define the interests of the community. The candidates can be contacted individually, or collectively via the moderated <NAME_EMAIL>.</p><p>Shortly before publishing these candidate profiles, we received the shocking news that one of the candidates from this collection, <PERSON><PERSON><PERSON>, unexpectedly passed away. Apart from a great researcher, <PERSON><PERSON><PERSON> was a very kind, pleasant, and fun person and will be sorely missed. His profile remains in this collection in his memory, despite his untimely passing.</p>", "Keywords": "", "DOI": "10.1145/3699824.3699825", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118182369, "Title": "A Comprehensive Survey of Studies on Predicting Anatomical Therapeutic Chemical Classes of Drugs", "Abstract": "<p>Drug classification plays a crucial role in contemporary drug discovery, design, and development. Determining the Anatomical Therapeutic Chemical (ATC) classes for new drugs is a laborious, costly, and intricate process, often requiring multiple clinical trial phases. Computational models offer significant benefits by accelerating drug evaluation, reducing complexity, and lowering costs; however, challenges persist in the drug classification system. To address this, a literature survey of computational models used for predicting ATC classes was conducted, covering research from 2008 to 2024. This study reviews numerous research articles on drug classification, focusing on drug descriptors, data sources, tasks, computational methods, model performance, and challenges in predicting ATC classes. It also examines the evolution of computational techniques and their application in identifying ATC classes. Finally, the study highlights open problems and research gaps, suggesting areas for further investigation in ATC class prediction.</p><p> CCS Concepts: Applied computing → Life and medical sciences → Bioinformatics </p>", "Keywords": "", "DOI": "10.1145/3699713", "PubYear": 2025, "Volume": "57", "Issue": "3", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, NIT Nagaland, Dimapur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science and Engineering, NIT Nagaland, Dimapur India"}], "References": [{"Title": "iATC-NRAKEL: an efficient multi-label classifier for recognizing anatomical therapeutic chemical classes of drugs", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "5", "Page": "1391", "JournalTitle": "Bioinformatics"}, {"Title": "iATC-FRAKEL: a simple multi-label web server for recognizing anatomical therapeutic chemical classes of drugs with their fingerprints only", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "11", "Page": "3568", "JournalTitle": "Bioinformatics"}, {"Title": "A convolutional neural network and graph convolutional network-based method for predicting the classification of anatomical therapeutic chemicals", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "18", "Page": "2841", "JournalTitle": "Bioinformatics"}, {"Title": "Computational resources in healthcare", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "3", "Page": "e1437", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Transformers in Vision: A Survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "10s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Machine learning in postgenomic biology and personalized medicine", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "2", "Page": "e1451", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Neural networks for anatomical therapeutic chemical (ATC) classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Applied Computing and Informatics"}, {"Title": "Epidemiological challenges in pandemic coronavirus disease ( COVID ‐19): Role of artificial intelligence", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "12", "Issue": "4", "Page": "e1462", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "The role of\n AI \n for developing digital twins in healthcare: The case of cancer care", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "1", "Page": "e1480", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "Remote patient monitoring using artificial intelligence: Current state, applications, and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "13", "Issue": "2", "Page": "e1485", "JournalTitle": "Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery"}, {"Title": "A Comparative Survey of Instance Selection Methods applied to Non-Neural and Transformer-Based Text Classification", "Authors": "Washington C<PERSON>ha; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "13s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A Survey of Controllable Text Generation Using Transformer-based Pre-trained Language Models", "Authors": "<PERSON><PERSON> Zhang; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Drug-drug interaction relation extraction based on deep learning: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "K1K2NN: A novel multi-label classification approach based on neighbors for predicting COVID-19 drug side effects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "110", "Issue": "", "Page": "108066", "JournalTitle": "Computational Biology and Chemistry"}]}, {"ArticleId": 118182389, "Title": "Survey on Knowledge Distillation for Large Language Models: Methods, Evaluation, and Application", "Abstract": "<p>Large Language Models (LLMs) have showcased exceptional capabilities in various domains, attracting significant interest from both academia and industry. Despite their impressive performance, the substantial size and computational demands of LLMs pose considerable challenges for practical deployment, particularly in environments with limited resources. The endeavor to compress language models while maintaining their accuracy has become a focal point of research. Among the various methods, knowledge distillation has emerged as an effective technique to enhance inference speed without greatly compromising performance. This paper presents a thorough survey from three aspects: method, evaluation, and application, exploring knowledge distillation techniques tailored specifically for LLMs. Specifically, we divide the methods into white-box KD and black-box KD to better illustrate their differences. Furthermore, we also explored the evaluation tasks and distillation effects between different distillation methods, and proposed directions for future research. Through in-depth understanding of the latest advancements and practical applications, this survey provides valuable resources for researchers, paving the way for sustained progress in this field.</p>", "Keywords": "", "DOI": "10.1145/3699518", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences & School of Cyber Security, University of Chinese Academy of Sciences, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Zhejiang University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Tsinghua University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Peking University, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, University of Chinese Academy of Sciences, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, University of Chinese Academy of Sciences, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Computing Technology, Chinese Academy of Sciences, China"}], "References": [{"Title": "Knowledge Distillation: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "6", "Page": "1789", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Knowledge distillation in deep learning and its applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Self-knowledge distillation via dropout", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "233", "Issue": "", "Page": "103720", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "A Survey on Evaluation of Large Language Models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 118182489, "Title": "New Comer in the Bakery Store: A Long-Term Exploratory Study Toward Design of Useful Service Robot Applications", "Abstract": "In this study, we report a 6-month empirical study on a service robot deployed in a bakery shop. Recently, potential applications of service robots have been increasingly explored. However, further empirical knowledge is required to determine the optimal approach to design service robots for useful applications. We also address “usefulness” from two perspectives: the effects of a robot on customers’ shopping behavior and the practical benefits the robot could provide for human workers in its working environment. The results show that our robot achieved long-term effects on product recommendations for customers who visited the bakery store on a regular basis (weekly) but not for other customers. A thematic analysis of the interviews reflected the practical values that the staff expected from the robot. Based on these findings, we we outline key considerations for designing effective long-term service robot applications.", "Keywords": "Service robot; Sales promotion; Product recommendation; Long-term; In the wild; Real-world applications", "DOI": "10.1007/s12369-024-01119-z", "PubYear": 2024, "Volume": "16", "Issue": "9-10", "JournalId": 5388, "JournalTitle": "International Journal of Social Robotics", "ISSN": "1875-4791", "EISSN": "1875-4805", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AI Lab, CyberAgent, Inc., Tokyo, Japan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "AI Lab, CyberAgent, Inc., Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "AI Lab, CyberAgent, Inc., Tokyo, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Graduation School of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduation School of Engineering Science, Osaka University, Toyonaka, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduation School of Engineering Science, Osaka University, Toyonaka, Japan"}], "References": [{"Title": "Local vs. Avatar Robot: Performance and Perceived Workload of Service Encounters in Public Space", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "377", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Behavioral Assessment of a Humanoid Robot When Attracting Pedestrians in a Mall", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "1731", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Towards a Socio-Legal Robotics: A Theoretical Framework on Norms and Adaptive Technologies", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Social Robotics"}]}, {"ArticleId": 118182664, "Title": "The Analytics Paradigm: Balancing Innovation and Ethics in a Data-Centric World", "Abstract": "This article examines data analytics's profound and multifaceted influence on contemporary society, exploring its transformative impact across various sectors and the challenges it presents. By synthesizing current research and case studies, we demonstrate how data analytics enhances decision-making through personalization and predictive insights, drives innovation in business and technology, and improves public services, particularly in healthcare and urban planning. The article also critically addresses the ethical implications and privacy concerns associated with the proliferation of data-driven approaches, including data security, algorithmic bias, and fairness. As data analytics continues to reshape societal structures and individual experiences, this article argues for a balanced approach that maximizes its potential benefits while mitigating risks. Our analysis concludes by considering the long-term societal implications of this data revolution, emphasizing the need for adaptive policies, education, and ethical frameworks to guide the future development and application of data analytics in an increasingly connected world.", "Keywords": "Data Analytics;Societal Transformation;Ethical Implications;Innovation;Decision-Making", "DOI": "10.32628/CSEIT241051034", "PubYear": 2024, "Volume": "10", "Issue": "5", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Infosys Ltd, USA"}], "References": [{"Title": "A Survey on Bias and Fairness in Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 118182737, "Title": "The Road Ahead: Emerging Trends, Unresolved Issues, and Concluding Remarks in Generative AI—A Comprehensive Review", "Abstract": "<p>The field of generative artificial intelligence (AI) is experiencing rapid advancements, impacting a multitude of sectors, from computer vision to healthcare. This paper provides a comprehensive review of generative AI’s evolution, significance, and applications, including the foundational architectures such as generative adversarial networks (GANs), variational autoencoders (VAEs), autoregressive models, flow‐based models, and diffusion models. We delve into the impact of generative algorithms on computer vision, natural language processing, artistic creation, and healthcare, demonstrating their revolutionary potential in data augmentation, text and speech synthesis, and medical image interpretation. While the transformative capabilities of generative AI are acknowledged, the paper also examines ethical concerns, most notably the advent of deepfakes, calling for the development of robust detection frameworks and responsible use guidelines. As generative AI continues to evolve, driven by advances in neural network architectures and deep learning methodologies, this paper provides a holistic overview of the current landscape and a roadmap for future research and ethical considerations in generative AI.</p>", "Keywords": "diffusion models;generative adversarial networks (GANs);generative AI;large language models (LLMs);transformers;variational auto encoders (VAEs)", "DOI": "10.1155/2024/4013195", "PubYear": 2024, "Volume": "2024", "Issue": "1", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "Balasubramaniam S.", "Affiliation": "School of Computer Science and Engineering, Kerala University of Digital Sciences, Innovation and Technology (Digital University Kerala), Thiruvananthapuram, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of ISE, Dayananda Sagar Academy of Technology and Management, Bangalore, India, dayanandasagar.edu"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Data Science, Noroff University College, Kristiansand, Norway, noroff.no; Department of Computer Science and Mathematics, Lebanese American University, Beirut, Lebanon, lau.edu.lb"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences (SIMATS), Saveetha University, Chennai 602105, India, saveetha.com"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences (SIMATS), Saveetha University, Chennai 602105, India, saveetha.com"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Digital Sciences, Kerala University of Digital Sciences, Innovation and Technology (Digital University Kerala), Thiruvananthapuram, India"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>. <PERSON>.", "Affiliation": "Faculty of Engineering and Technology, Villa College, Malé, Maldives; Corresponding author."}], "References": [{"Title": "On the evaluation of generative models in music", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "4773", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Defending Deep Learning Models Against Adversarial Attacks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Software Science and Computational Intelligence"}, {"Title": "Artificial Neural Networks and Deep Learning in the Visual Arts: a review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "121", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Generative adversarial network: An overview of theory and applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "1", "Page": "100004", "JournalTitle": "International Journal of Information Management Data Insights"}, {"Title": "Hybrid model with optimization tactics for software defect prediction", "Authors": "Shantappa <PERSON>; S Balasubramaniam", "PubYear": 2023, "Volume": "14", "Issue": "2", "Page": "", "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing"}, {"Title": "Bar transformer: a hierarchical model for learning long-term structure and generating impressive pop music", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "9", "Page": "10130", "JournalTitle": "Applied Intelligence"}, {"Title": "Proximal policy optimization algorithm for dynamic pricing with online reviews", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119191", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Deep variational models for collaborative filtering-based recommender systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "10", "Page": "7817", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Using ChatGPT to navigate ambivalent and contradictory research findings on artificial intelligence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "6", "Issue": "", "Page": "1195797", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Computer-Aided Optimization Design of Intelligent Commodity Packaging Based on Generative Adversarial Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "", "Issue": "", "Page": "107", "JournalTitle": "Computer-Aided Design and Applications"}, {"Title": "Decoding ChatGPT: A taxonomy of existing research, current challenges, and possible future directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "8", "Page": "101675", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A Survey of Controllable Text Generation Using Transformer-based Pre-trained Language Models", "Authors": "<PERSON><PERSON> Zhang; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deepfake Attacks: Generation, Detection, Datasets, Challenges, and Research Directions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "10", "Page": "216", "JournalTitle": "Computers"}, {"Title": "The scholarly footprint of ChatGPT: a bibliometric analysis of the early outbreak phase", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "6", "Issue": "", "Page": "1270749", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Fairness issues, current approaches, and challenges in machine learning models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "8", "Page": "3095", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Soft Margin Spectral Normalization for GANs", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "1", "Page": "1", "JournalTitle": "Computing and Software for Big Science"}]}, {"ArticleId": 118182975, "Title": "Fast and Optimal Extraction for Sparse Equality Graphs", "Abstract": "<p>Equality graphs (e-graphs) are used to compactly represent equivalence classes of terms in symbolic reasoning systems. Beyond their original roots in automated theorem proving, e-graphs have been used in a variety of applications. They have become particularly important as the key ingredient in the popular technique of equality saturation, which has notable applications in compiler optimization, program synthesis, program verification, and symbolic execution, among others. In a typical equality saturation workflow, an e-graph is used to store a large number of equalities that are generated by local rewrites during a saturation phase, after which an optimal term is extracted from the e-graph as the output of the technique. However, despite its crucial role in equality saturation, e-graph extraction has received relatively little attention in the literature, which we seek to start addressing in this paper. Extraction is a challenging problem and is notably known to be NP-hard in general, so current equality saturation tools rely either on slow optimal extraction algorithms based on integer linear programming (ILP) or on heuristics that may not always produce the optimal result. In fact, in this paper, we show that e-graph extraction is hard to approximate within any constant ratio. Thus, any such heuristic will produce wildly suboptimal results in the worst case. Fortunately, we show that the problem becomes tractable when the e-graph is sparse, which is the case in many practical applications. We present a novel parameterized algorithm for extracting optimal terms from e-graphs with low treewidth, a measure of how “tree-like” a graph is, and prove its correctness. We also present an efficient Rust implementation of our algorithm and evaluate it against ILP on a number of benchmarks extracted from the Cranelift benchmark suite, a real-world compiler optimization library based on equality saturation. Our algorithm optimally extracts e-graphs with treewidths of up to 10 in a fraction of the time taken by ILP. These results suggest that our algorithm can be a valuable tool for equality saturation users who need to extract optimal terms from sparse e-graphs.</p>", "Keywords": "", "DOI": "10.1145/3689801", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Hong Kong, China"}], "References": [{"Title": "egg: Fast and extensible equality saturation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Don’t run on fumes—Parametric gas bounds for smart contracts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "176", "Issue": "", "Page": "110923", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Rewrite rule inference using equality saturation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Relational e-matching", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Better Together: Unifying Datalog and Equality Saturation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "468", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Exploiting the Sparseness of Control-Flow and Call Graphs for Efficient and On-Demand Algebraic Program Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "1993", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "The Bounded Pathwidth of Control-Flow Graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA2", "Page": "292", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Super-optimization of Smart Contracts", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 118182976, "Title": "Dependency-Aware Code Naturalness", "Abstract": "<p>Code naturalness, which captures repetitiveness and predictability in programming languages, has proven valuable for various code-related tasks in software engineering. However, precisely measuring code naturalness remains a fundamental challenge. Existing methods measure code naturalness over individual lines of code while ignoring the deep semantic relations among different lines, e.g., program dependency, which may negatively affect the precision of the measure. Despite the intuitive appeal of extending the code naturalness measure to the code dependency domain (as there are some work that have initiated the utilization of code dependency for diverse code-related tasks), this assumption remains unexplored and warrants direct investigation. In this study, we aim to perform the first empirical study to investigate whether incorporating code dependency, instead of analyzing individual lines, can enhance the precision of measuring code naturalness. To achieve that, we first propose a new method named DAN for measuring code naturalness by incorporating the rich dependency information in the code. Specifically, DAN extracts multiple sequences of code lines by traversing the program dependency graph, where different code lines are connected by dependencies in each sequence, and then the code naturalness will be measured by taking each sequence as a whole. In this way, the dependency information can be well captured. Finally, we have conducted an extensive study to evaluate the influence of code dependency for measuring code naturalness with DAN, and compared it with the state-of-the-art methods under three emerging application scenarios of code naturalness. The results demonstrate that DAN can not only better distinguish natural and unnatural code, but also substantially boost two important downstream applications of code naturalness, i.e., distinguishing buggy and non-buggy code lines and data cleansing for training better code models, reflecting the significance of code dependency in measuring code naturalness.</p>", "Keywords": "", "DOI": "10.1145/3689794", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin University, Tianjin, China"}], "References": [{"Title": "Exploring Better Black-Box Test Case Prioritization via Log Analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "How Important are Good Method Names in Neural Code Generation? A Model Robustness Perspective", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "33", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}]}, {"ArticleId": 118182988, "Title": "Compilation of Shape Operators on Sparse Arrays", "Abstract": "<p>We show how to build a compiler for a sparse array language that supports shape operators such as reshaping or concatenating arrays, in addition to compute operators. Existing sparse array programming systems implement generic shape operators for only some sparse data structures, reduce shape operators on other data structures to those, and do not support fusion. Our system compiles sparse array expressions to code that efficiently iterates over reshaped views of irregular sparse data structures, without needing to materialize temporary storage for intermediates. Our evaluation shows that our approach generates sparse array code competitive with popular sparse array libraries: our generated shape operators achieve geometric mean speed-ups of 1.66×–15.3× when compared to hand-written kernels in scipy.sparse and 1.67×–651× when compared to generic implementations in pydata/sparse. For operators that require data structure conversions in these libraries, our generated code achieves geometric mean speed-ups of 7.29×–13.0× when compared to scipy.sparse and 21.3×–511× when compared to pydata/sparse. Finally, our evaluation demonstrates that fusing shape and compute operators improves the performance of several expressions by geometric mean speed-ups of 1.22×–2.23×.</p>", "Keywords": "", "DOI": "10.1145/3689752", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Google Research, Seattle, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Stanford University, Stanford, USA"}, {"AuthorId": 5, "Name": "Aart J.C. Bik", "Affiliation": "Google Research, Mountain View, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Stanford University, Stanford, USA"}], "References": [{"Title": "A sparse iteration space transformation framework for sparse tensor algebra", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Compilation of sparse array programming models", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Verified tensor-program optimization via high-level scheduling rewrites", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Compiler Support for Sparse Tensor Computations in MLIR", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "19", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}, {"Title": "Polyhedral Specification and Code Generation of Sparse Tensor Contraction with Co-iteration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}, {"Title": "Indexed Streams: A Formal Intermediate Representation for Fused Contraction Programs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; Tian<PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1169", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118183016, "Title": "Merging <PERSON><PERSON><PERSON>", "Abstract": "<p>Programming language mechanisms with a type-directed semantics are nowadays common and widely used. Such mechanisms include gradual typing, type classes, implicits and intersection types with a merge operator. While sharing common challenges in their design and having complementary strengths, type-directed mechanisms have been mostly independently studied. This paper studies a new calculus, called λM⋆, which combines two type-directed mechanisms: gradual typing and a merge operator based on intersection types. Gradual typing enables a smooth transition between dynamically and statically typed code, and is available in languages such as TypeScript or Flow. The merge operator generalizes record concatenation to allow merges of values of any two types. Recent work has shown that the merge operator enables modelling expressive OOP features like first-class traits/classes and dynamic inheritance with static type-checking. These features are not found in mainstream statically typed OOP languages, but they can be found in dynamically or gradually typed languages such as JavaScript or TypeScript. In λM⋆, by exploiting the complementary strengths of gradual typing and the merge operator, we obtain a foundation for modelling gradually typed languages with both first-class classes and dynamic inheritance. We study a static variant of λM⋆ (called λM); prove the type-soundness of λM⋆; show that λM⋆ can encode gradual rows and all well-typed terms in the GTFL≲ calculus; and show that λM⋆ satisfies gradual typing criteria. The dynamic gradual guarantee (DGG) is challenging due to the possibility of ambiguity errors. We establish a variant of the DGG using a semantic notion of precision based on a step-indexed logical relation.</p>", "Keywords": "", "DOI": "10.1145/3689734", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON> C<PERSON>", "Affiliation": "University of Hong Kong, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Chile, Santiago, Chile"}], "References": [{"Title": "Consistent Subtyping for All", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; Bruno C. D<PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "Graduality and parametricity: together again for the first time", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Abstracting gradual references", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "197", "Issue": "", "Page": "102496", "JournalTitle": "Science of Computer Programming"}, {"Title": "Abstracting gradual typing moving forward: precise and space-efficient", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Compositional Programming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Bruno <PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "Transitioning from structural to nominal code with efficient gradual typing", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Taming the Merge Operator", "Authors": "XUEJING HUANG; JINXU ZHAO; BRUNO C. D. S. OLIVEIRA", "PubYear": 2021, "Volume": "31", "Issue": "", "Page": "e28", "JournalTitle": "Journal of Functional Programming"}]}, {"ArticleId": 118183018, "Title": "Automated Verification of Parametric Channel-Based Process Communication", "Abstract": "<p>A challenge of writing concurrent message passing programs is ensuring the absence of partial deadlocks, which can cause severe memory leaks in long running systems. Several static analysis techniques have been proposed for automatically detecting partial deadlocks in Go programs. For a large enterprise code base, we found these tools too imprecise to reason about process communication that is parametric, i.e., where the number of channel communication operations or the channel capacities are determined at runtime. We present a novel approach to automatically verify the absence of partial deadlocks in Go program fragments with such parametric process communication. The key idea is to translate Go fragments to a core language that is sufficiently expressive to represent real-world parametric communication patterns and can be encoded into Dafny programs annotated with postconditions enforcing partial deadlock freedom. In situations where a fragment is partial deadlock free only when the concurrency parameters satisfy certain conditions, a suitable precondition can often be inferred. Experimental results on a real-world code base containing 583 program fragments that are beyond the reach of existing techniques have shown that the approach can verify the absence of partial deadlocks in 145 cases. For an additional 228 cases, a nontrivial precondition is inferred that the surrounding code must satisfy to ensure partial deadlock freedom.</p>", "Keywords": "", "DOI": "10.1145/3689784", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "Georgian-<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON> Holloway, University of London, Egham, United Kingdom"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Aarhus University, Aarhus, Denmark"}], "References": []}, {"ArticleId": 118183162, "Title": "Theoretical analysis and numerical scheme of local conservative characteristic finite difference for 2-d advection diffusion equations", "Abstract": "In this paper, the mass conservative characteristic finite difference scheme for 2-d advection diffusion equations is analyzed. Firstly, along x -direction, we obtain the solutions { U ˜ i , j n } by applying the piecewise parabolic method (PPM) on the Lagrangian grid where x ¯ is solved using the first-order Runge <PERSON> scheme. Secondly, the mass M ¯ i , j n over Ω ¯ i , j ( t n ) are solved by the PPM scheme along y -direction. Finally, the local conservative characteristic finite difference scheme is constructed. By some auxiliary lemmas, we prove our scheme is stable and obtain the optimal error estimate. Our scheme is proved to be of second order convergence in space and of first order in time. Numerical experiments are used to verify the theoretical analysis.", "Keywords": "", "DOI": "10.1016/j.camwa.2024.09.032", "PubYear": 2024, "Volume": "175", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Agricultural University, Taian, Shandong, 271018, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Engineering, Shandong Agricultural University, Taian, Shandong, 271018, China;Corresponding author"}], "References": []}, {"ArticleId": 118183255, "Title": "Feature correlation fusion and feature selection under adaptive neighborhood group approximation space", "Abstract": "<p>In real-world scenarios, features often exhibit dynamic interdependence and interaction. While neighborhood rough sets in feature selection have been extensively studied, approaches focusing on searching over feature groups have received less attention. Drawing inspiration from this premise, a feature fusion method grounded in minimum redundancy is proposed to integrate fragmented features. Maximizing Jeffrey divergence constructs a metric function, facilitating the inscription of knowledge granules on the feature groups. This distance function effectively coordinates the importance of feature groups by mapping samples into an adaptive approximation space. Subsequently, traditional uncertainty measures are extended to the neighborhood granules formed by the feature groups. An objective function based on these metrics of neighborhood uncertainty measures is designed to ascertain the importance of feature groups, presenting a novel feature selection algorithm based on this function. Empirical evaluations of the proposed algorithms are conducted using various datasets sourced from the University of California, Irvine (UCI), providing a comprehensive assessment of the efficacy and performance. The experimental results demonstrate the effectiveness of the algorithm.</p>", "Keywords": "Neighborhood rough set; Granular computing; Attribute reduction; Feature selection", "DOI": "10.1007/s13042-024-02362-6", "PubYear": 2025, "Volume": "16", "Issue": "3", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer and Information Science, Chongqing Normal University, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Artificial Intelligence, Southwest Jiaotong University, Chengdu, China"}], "References": [{"Title": "Discernible neighborhood counting based incremental feature selection for heterogeneous data", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "5", "Page": "1115", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Incremental feature selection for dynamic hybrid data using neighborhood rough set", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105516", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel hybrid feature selection method based on dynamic feature importance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106337", "JournalTitle": "Applied Soft Computing"}, {"Title": "Accelerating information entropy-based feature selection using rough set theory with classified nested equivalence classes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107517", "JournalTitle": "Pattern Recognition"}, {"Title": "Feature selection using self-information and entropy-based uncertainty measure for fuzzy neighborhood rough set", "Authors": "Jiucheng Xu; Meng <PERSON>; Yuanyuan Ma", "PubYear": 2022, "Volume": "8", "Issue": "1", "Page": "287", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A feature selection method via analysis of relevance, redundancy, and interaction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "183", "Issue": "", "Page": "115365", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Feature selection using Fisher score and multilabel neighborhood rough sets for multilabel classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "887", "JournalTitle": "Information Sciences"}, {"Title": "A data-level fusion model for unsupervised attribute selection in multi-source homogeneous data", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "87", "JournalTitle": "Information Fusion"}, {"Title": "TFSFB: Two-stage feature selection via fusing fuzzy multi-neighborhood rough set with binary whale optimization for imbalanced data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "95", "Issue": "", "Page": "91", "JournalTitle": "Information Fusion"}, {"Title": "Bi-level ensemble method for unsupervised feature selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "100", "Issue": "", "Page": "101910", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 118183306, "Title": "H‐PME: Development of a Robot Skin Using Halbach Array Permanent Magnet Elastomer", "Abstract": "This article presents a novel 3‐axis Halbach permanent magnet elastomer (H‐PME) sensor for the robotic application, which effectively reduces crosstalk along when two sensors are used simultaneously in close proximity, for example, during grasping of thin and delicate objects, needle threading, etc. This sensor integrates a Halbach‐array magnetic elastomer, a 3×3 Hall sensor matrix, and a silicone layer. The magnetic elastomer is produced by combining NdFeB powders with a diameter of 5 μm into silicone, following a weight ratio of 50%, and then magnetized using 2D Halbach‐array magnets. Simulation results reveal the capability to adjust magnetic field strength and distribution by altering the magnet's orientation. The sensor's efficacy in 3‐axis sensing is validated through calibration with a linear model, achieving a good root‐mean‐square error below 0.7 N in force measurement. The H‐PME sensor, with a thickness of merely 4.5 mm, can detect forces up to 50 N. It's simple 3‐layer design allows the thickness to be reduced to as low as 2 mm, while also offering ease of replacement. Crucially, crosstalk evaluation experiments show that the proposed H‐PME sensor can dramatically mitigate crosstalk interference.", "Keywords": "", "DOI": "10.1002/aisy.202400325", "PubYear": 2025, "Volume": "7", "Issue": "2", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research Innovation Center Waseda University  Shinjuku‐ku Tokyo 162‐0041 Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Innovation Center Waseda University  Shinjuku‐ku Tokyo 162‐0041 Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Research Innovation Center Waseda University  Shinjuku‐ku Tokyo 162‐0041 Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Innovation Center Waseda University  Shinjuku‐ku Tokyo 162‐0041 Japan"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Research Innovation Center Waseda University  Shinjuku‐ku Tokyo 162‐0041 Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Innovation Center Waseda University  Shinjuku‐ku Tokyo 162‐0041 Japan"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Innovation Center Waseda University  Shinjuku‐ku Tokyo 162‐0041 Japan;Graduate School of Engineering The University of Tokyo  7‐3‐1 Hongo, Bunkyo‐ku Tokyo 113‐8656 Japan"}], "References": [{"Title": "SensAct: The Soft and Squishy Tactile Sensor with Integrated Flexible Actuator", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "3", "Page": "1900145", "JournalTitle": "Advanced Intelligent Systems"}]}, {"ArticleId": 118183494, "Title": "Overview of Open Dataset Sessions and Benchmarking Competitions in 2022 - Part 2 (MDRE at MMM 2022, ACM MM 2022)", "Abstract": "<p>In this Dataset Column, we present a review of some of the notable events related to open datasets and benchmarking competitions in the field of multimedia. This year's selection highlights the wide range of topics and datasets currently of interest to the community. Some of the events covered in this review include special sessions on open datasets and competitions featuring multimedia data. While this list is not exhaustive and contains an overview of about 40 datasets, it is meant to showcase the diversity of subjects and datasets explored in the field. This year's review follows similar efforts from the previous year (https://records.sigmm.org/2022/01/12/overview-of-open-dataset-sessions-and-benchmarking-competitions-in-2021/), highlighting the ongoing importance of open datasets and benchmarking competitions in advancing research and development in multimedia. The column is divided into three parts, in this one we focus on MDRE at MMM 2022 and ACM MM 2022.</p>", "Keywords": "", "DOI": "10.1145/3699843.3699848", "PubYear": 2023, "Volume": "15", "Issue": "1", "JournalId": 26893, "JournalTitle": "ACM SIGMultimedia Records", "ISSN": "", "EISSN": "1947-4598", "Authors": [], "References": []}, {"ArticleId": 118183609, "Title": "Imperative Compositional Programming: Type Sound Distributive Intersection Subtyping with References via Bidirectional Typing", "Abstract": "<p>Compositional programming is a programming paradigm that emphasizes modularity and is implemented in the CP programming language. The foundations for compositional programming are based on a purely functional variant of System F with intersection types, called Fi+, which includes distributivity rules for subtyping. This paper shows how to extend compositional programming and CP with mutable references, enabling a modular, imperative compositional programming style. A technical obstacle solved in our work is the interaction between distributive intersection subtyping and mutable references. <PERSON> and <PERSON> [2000] studied this problem in standard formulations of intersection type systems and argued that, when combined with references, distributive subtyping rules lead to type unsoundness. To recover type soundness, they proposed dropping distributivity rules in subtyping. CP cannot adopt this solution, since it fundamentally relies on distributivity for modularity. Therefore, we revisit the problem and show that, by adopting bidirectional typing, a more lightweight and type sound restriction is possible: we can simply restrict the typing rule for references. This solution retains distributivity and an unrestricted intersection introduction rule. We present a first calculus, based on <PERSON> and <PERSON>'s work, which illustrates the generality of our solution. Then we present an extension of Fi+ with references, which adopts our restriction and enables imperative compositional programming. We implement an extension of CP with references and show how to model a modular live-variable analysis in CP. Both calculi and their proofs are formalized in the <PERSON>q proof assistant.</p>", "Keywords": "", "DOI": "10.1145/3689782", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 3, "Name": "<PERSON> C<PERSON>", "Affiliation": "The University of Hong Kong, Hong Kong, China"}], "References": [{"Title": "Bidirectional Typing", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Compositional Programming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Bruno <PERSON>", "PubYear": 2021, "Volume": "43", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "Taming the Merge Operator", "Authors": "XUEJING HUANG; JINXU ZHAO; BRUNO C. D. S. OLIVEIRA", "PubYear": 2021, "Volume": "31", "Issue": "", "Page": "e28", "JournalTitle": "Journal of Functional Programming"}, {"Title": "Extensible Metatheory Mechanization via Family Polymorphism", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Yizhou Zhang", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1608", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118183614, "Title": "A distributed load balancing architecture based on in‐band network telemetry", "Abstract": "", "Keywords": "", "DOI": "10.1002/itl2.587", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 5643, "JournalTitle": "Internet Technology Letters", "ISSN": "2476-1508", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Mingfa Li", "Affiliation": "Key Laboratory of Computing Power Network and Information Security, Ministry of Education Shandong Computer Science Center (National Supercomputer Center in Jinan), Qilu University of Technology (Shandong Academy of Sciences)  Jinan China;Shandong Provincial Key Laboratory of Computing Power Internet and Service Computing Shandong Fundamental Research Center for Computer Science  Jinan China"}, {"AuthorId": 2, "Name": "Huiling Shi", "Affiliation": "Key Laboratory of Computing Power Network and Information Security, Ministry of Education Shandong Computer Science Center (National Supercomputer Center in Jinan), Qilu University of Technology (Shandong Academy of Sciences)  Jinan China;Shandong Provincial Key Laboratory of Computing Power Internet and Service Computing Shandong Fundamental Research Center for Computer Science  Jinan China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Computing Power Network and Information Security, Ministry of Education Shandong Computer Science Center (National Supercomputer Center in Jinan), Qilu University of Technology (Shandong Academy of Sciences)  Jinan China;Shandong Provincial Key Laboratory of Computing Power Internet and Service Computing Shandong Fundamental Research Center for Computer Science  Jinan China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Computing Power Network and Information Security, Ministry of Education Shandong Computer Science Center (National Supercomputer Center in Jinan), Qilu University of Technology (Shandong Academy of Sciences)  Jinan China;Shandong Provincial Key Laboratory of Computing Power Internet and Service Computing Shandong Fundamental Research Center for Computer Science  Jinan China"}], "References": [{"Title": "In-band Network Telemetry: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "186", "Issue": "", "Page": "107763", "JournalTitle": "Computer Networks"}, {"Title": "An Energy-optimized Embedded load balancing using DVFS computing in Cloud Data centers", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "197", "Issue": "", "Page": "255", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 118183706, "Title": "Uso del framework de ISTQB para aplicaciones desarrolladas en una entidad bancaria: Caso de uso “Portabilidad de Nómina”", "Abstract": "", "Keywords": "", "DOI": "10.36825/RITI.12.27.003", "PubYear": 2024, "Volume": "12", "Issue": "27", "JournalId": 70037, "JournalTitle": "Revista de Investigación en Tecnologías de la Investigaión", "ISSN": "", "EISSN": "2387-0893", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118183816, "Title": "Hypra: A Deductive Program Verifier for Hyper Hoare Logic", "Abstract": "<p>Hyperproperties relate multiple executions of a program and are useful to express common correctness properties (such as determinism) and security properties (such as non-interference). While there are a number of powerful program logics for the deductive verification of hyperproperties, their automation falls behind. Most existing deductive verification tools are limited to safety properties, but cannot reason about the existence of executions, for instance, to prove the violation of a safety property. Others support more flexible hyperproperties such as generalized non-interference, but have limitations in terms of the programs and proof structures they support. In this paper, we present the first deductive verification technique for arbitrary hyperproperties over multiple executions of the same program. Our technique automates the generation of verification conditions for Hyper Hoare Logic. Our key insight is that arbitrary hyperproperties and the corresponding proof rules can be encoded into a standard intermediate verification language by representing sets of states of the input program explicitly in the states of the intermediate program. Verification is then automated using an existing SMT-based verifier for the intermediate language. We implement our technique in a tool called Hypra and demonstrate that it can reliably verify complex hyperproperties.</p>", "Keywords": "", "DOI": "10.1145/3689756", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "ETH Zurich, Zürich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ETH Zurich, Zürich, Switzerland"}], "References": [{"Title": "Modular Product Programs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Programming Languages and Systems"}, {"Title": "The next 700 relational program logics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; Exequiel R<PERSON>s", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Incorrectness logic", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Finding real bugs in big programs with incorrectness logic", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Proving hypersafety compositionally", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA2", "Page": "289", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "An Algebra of Alignment for Relational Verification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "POPL", "Page": "573", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Outcome Logic: A Unifying Foundation for Correctness and Incorrectness Reasoning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA1", "Page": "522", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "CommCSL: Proving Information Flow Security for Concurrent Programs using Abstract Commutativity", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1682", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Mechanised Hypersafety Proofs about Structured Data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "PLDI", "Page": "647", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Hyper Hoare Logic: (Dis-)Proving Program Hyperproperties", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "PLDI", "Page": "1485", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118184458, "Title": "Deepfake Detection: A Comprehensive Survey from the Reliability Perspective", "Abstract": "<p>The mushroomed Deepfake synthetic materials circulated on the internet have raised a profound social impact on politicians, celebrities, and individuals worldwide. In this survey, we provide a thorough review of the existing Deepfake detection studies from the reliability perspective. We identify three reliability-oriented research challenges in the current Deepfake detection domain: transferability, interpretability, and robustness. Moreover, while solutions have been frequently addressed regarding the three challenges, the general reliability of a detection model has been barely considered, leading to the lack of reliable evidence in real-life usages and even for prosecutions on Deepfake-related cases in court. We, therefore, introduce a model reliability study metric using statistical random sampling knowledge and the publicly available benchmark datasets to review the reliability of the existing detection models on arbitrary Deepfake candidate suspects. Case studies are further executed to justify the real-life Deepfake cases including different groups of victims with the help of the reliably qualified detection models as reviewed in this survey. Reviews and experiments on the existing approaches provide informative discussions and future research directions for Deepfake detection.</p>", "Keywords": "", "DOI": "10.1145/3699710", "PubYear": 2025, "Volume": "57", "Issue": "3", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science, The University of Hong Kong, Hong Kong, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Hunan University,  Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Hong Kong,  Hong Kong Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "University of Guelph,  Guelph, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Qilu University of Technology (Shandong Academy of Sciences),  Jinan, China"}], "References": [{"Title": "Deepfakes and beyond: A Survey of face manipulation and fake detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "131", "JournalTitle": "Information Fusion"}, {"Title": "The Creation and Detection of Deepfakes", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Fake face detection via adaptive manipulation traces extraction network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "204", "Issue": "", "Page": "103170", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "PRRNet: Pixel-Region relation network for face forgery detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107950", "JournalTitle": "Pattern Recognition"}, {"Title": "Exposing Vulnerabilities of Deepfake Detection Systems with Robust Attacks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "3", "Page": "1", "JournalTitle": "Digital Threats: Research and Practice"}, {"Title": "DIPPAS: a deep image prior PRNU anonymization scheme", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "1", "Page": "1", "JournalTitle": "EURASIP Journal on Information Security"}, {"Title": "DeepFake detection algorithm based on improved vision transformer", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "7", "Page": "7512", "JournalTitle": "Applied Intelligence"}, {"Title": "Deepfacelab: Integrated, flexible and extensible face-swapping framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "109628", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 118184530, "Title": "Code and Data Repository for Disjunctive Rule Lists", "Abstract": "", "Keywords": "interpretable machine learning; decision rules; regression", "DOI": "10.1287/ijoc.2022.1242.cd", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118184626, "Title": "A Generalized approach to the operationalization of Software Quality Models", "Abstract": "<p>Comprehensive measures of quality are a research imperative, yet the development of software quality models is a wicked problem. Definitive solutions do not exist and quality is subjective at its most abstract. Definitional measures of quality are contingent on a domain, and even within a domain, the choice of representative characteristics to decompose quality is subjective. Thus, the operationalization of quality models brings even more challenges. A promising approach to quality modeling is the use of hierarchies to represent characteristics, where lower levels of the hierarchy represent concepts closer to real-world observations. Building upon prior hierarchical modeling approaches, we developed the Platform for Investigative software Quality Understanding and Evaluation (PIQUE). PIQUE surmounts several quality modeling challenges because it allows modelers to instantiate abstract hierarchical models in any domain by leveraging organizational tools tailored to their specific contexts. Here, we introduce PIQUE; exemplify its utility with two practical use cases; address challenges associated with parameterizing a PIQUE model; and describe algorithmic techniques that tackle normalization, aggregation, and interpolation of measurements.</p>", "Keywords": "Data aggregation;Data science;Quality assurance;Quality models;Software engineering;Software quality", "DOI": "10.7717/peerj-cs.2357", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Idaho National Laboratory, Idaho Falls, United States of America;Pacific Northwest National Laboratory, Richland, United States of America;Gianforte School of Computing, Montana State University, Bozeman, United States of America"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Gianforte School of Computing, Montana State University, Bozeman, United States of America"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Gianforte School of Computing, Montana State University, Bozeman, United States of America"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Montana State University, Bozeman, MT, United States of America"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Gianforte School of Computing, Montana State University, Bozeman, United States of America"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Montana State University, Bozeman, MT, United States of America"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Pacific Northwest National Laboratory, Richland, United States of America;Gianforte School of Computing, Montana State University, Bozeman, United States of America"}], "References": []}, {"ArticleId": 118184629, "Title": "Deep neural network-based robotic visual servoing for satellite target tracking", "Abstract": "<p>In response to the costly and error-prone manual satellite tracking on the International Space Station (ISS), this paper presents a deep neural network (DNN)-based robotic visual servoing solution to the automated tracking operation. This innovative approach directly addresses the critical issue of motion decoupling, which poses a significant challenge in current image moment-based visual servoing. The proposed method uses DNNs to estimate the manipulator’s pose, resulting in a significant reduction of coupling effects, which enhances control performance and increases tracking precision. Real-time experimental tests are carried out using a 6-DOF Denso manipulator equipped with an RGB camera and an object, mimicking the targeting pin. The test results demonstrate a 32.04% reduction in pose error and a 21.67% improvement in velocity precision compared to conventional methods. These findings demonstrate that the method has the potential to improve efficiency and accuracy significantly in satellite target tracking and capturing.</p>", "Keywords": "deep learning;deep neural networks;pose estimation;robot vision systems;visual servoing", "DOI": "10.3389/frobt.2024.1469315", "PubYear": 2024, "Volume": "11", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical, Industrial and Aerospace Engineering, Concordia University, Montréal, QC, Canada."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical, Industrial and Aerospace Engineering, Concordia University, Montréal, QC, Canada."}, {"AuthorId": 3, "Name": "Abolfazl Mohebbi", "Affiliation": "Department of Mechanical Engineering, Polytechnique Montréal, Montréal, QC, Canada."}], "References": []}, {"ArticleId": *********, "Title": "Enabling personalized smart tourism with location-based social networks", "Abstract": "<p> With the rapid advance of mobile internet, communication technology and the Internet of Things (IoT), the tourism industry is undergoing unprecedented transformation. Smart tourism offers users personalized and customized services for travel planning and recommendations. Location-based social networks (LBSNs) play a crucial role in smart tourism industry by providing abundant data sources through their social networking attributes. However, applying LBSNs to smart tourism is a challenge due to the need to deal with complex multi-source information modeling and tourism data sparsity. In this article, to fully harness the potential of LBSNs using deep learning technologies, we propose an knowledge-driven personalized recommendation method for smart tourism. Representation learning techniques can effectively modeling the contextual information ( e.g. , time, space, and semantics) in LBSNs, while the data augmentation strategy of contrastive learning techniques can explore user personalized travel behaviors and alleviate data sparsity. To demonstrate the effectiveness of the proposed approach, we conducted a case study on trip recommendation. Furthermore, the patterns of human mobility are revealed by exploring the effect of contextual data and tourist potential preferences. </p>", "Keywords": "Artificial intelligence;Location-based social networks;Personalized recommendation system;Smart tourism", "DOI": "10.7717/peerj-cs.2375", "PubYear": 2024, "Volume": "10", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software, Dalian University of Technology, Dalian, China"}, {"AuthorId": 3, "Name": "Jing<PERSON> Song", "Affiliation": "School of Arts, Tourism College of Zhejiang, Hangzhou, China"}, {"AuthorId": 4, "Name": "Xiangjie Kong", "Affiliation": "College of Computer Science and Technology, Zhejiang University of Technology, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering and Architecture, Kore University of Enna, Enna, Italy"}], "References": [{"Title": "Trust-aware location recommendation in location-based social networks: A graph-based approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "213", "Issue": "", "Page": "119048", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 118184802, "Title": "Model-predictive optimal control of ferrofluidic microrobots in three-dimensional space", "Abstract": "<p>Ferrofluid microrobots have emerged as promising tools for minimally invasive medical procedures. Their unique properties to navigate complex fluids and reach otherwise inaccessible regions of the human body have enabled new applications in targeted drug delivery, tissue engineering, and diagnostics. This paper proposes a model-predictive controller for the external magnetic manipulation of ferrofluid microrobots in three dimensions (3D). The internal optimization routine of the controller determines appropriate changes in the applied electromagnetic field to minimize the deviation between the actual and desired trajectories of the microrobot. A linear system governing locomotion is derived and used as the equality constraints of the optimization problems associated with the feedback index. In addition to ferrofluid droplets, the controller presented in this work may be applied to other magnetically-pulled microrobots. Several experiments are performed to validate the controller and showcase its ability to adapt to changes in system parameters such as the desired tracking trajectory and the size, orientation, deformation, and velocity of the microrobot. The accuracy of the controller is analyzed for each experiment, and the average error is found to be within 0.25 mm for small velocities. An additional experiment is performed to demonstrate significant improvement over a PID controller that is optimally tuned using Bayesian optimization. The results presented in this paper suggest that the proposed control algorithm could enable new microrobotic capabilities in minimally invasive medical procedures, lab-on-a-chip applications, and microfluidics.</p>", "Keywords": "", "DOI": "10.1177/02783649241285051", "PubYear": 2025, "Volume": "44", "Issue": "5", "JournalId": 943, "JournalTitle": "The International Journal of Robotics Research", "ISSN": "0278-3649", "EISSN": "1741-3176", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": "School for Engineering of Matter, Transport and Energy at Arizona State University, Tempe, AZ, USA;College of Engineering and Computer Science, University of Texas Rio Grande Valley, Edinburg, TX, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center for Nonlinear Studies, Theoretical Division, Los Alamos National Laboratory, Los Alamos, NM, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School for Engineering of Matter, Transport and Energy at Arizona State University, Tempe, AZ, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School for Engineering of Matter, Transport and Energy at Arizona State University, Tempe, AZ, USA"}], "References": [{"Title": "A Shapeshifting Ferrofluidic Robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "6", "Page": "687", "JournalTitle": "Soft Robotics"}, {"Title": "Surface-only ferrofluids", "Authors": "<PERSON><PERSON> Huang; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Learning to Control a Three-Dimensional Ferrofluidic Robot", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "2", "Page": "218", "JournalTitle": "Soft Robotics"}]}, {"ArticleId": 118184903, "Title": "Keyword extraction method based on complex network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.142110", "PubYear": 2024, "Volume": "25", "Issue": "4", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Liu", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118184925, "Title": "Smart Hospital Patient Monitoring System Aided by Edge-Fog-Cloud Continuum: A Performability Evaluation Focusing on Distinct Sensor Sources", "Abstract": "<p>The variety of sensors for health monitoring helps professionals in the field make decisions. For certain clinical conditions, it is interesting to monitor the patient’s health after discharge. Wearable health monitoring devices (smartwatches) can send data to the hospital. However, to monitor many patients (external and internal), a resilient and high-performance computing infrastructure is required. Such characteristics require high monetary cost equipment. This paper presents an SPN (Stochastic Petri Net) model for evaluating the performance and performability of a multi-tier hospital system architecture (edge-fog-cloud). The model allows you to evaluate the Mean Response Time (MRT), resource utilization level (U), and probability of data loss (DP). A very specific characteristic is to consider two data sources (internal and external). Performance metrics are evaluated in two scenarios based on varying the number of containers for simultaneous processing in the cloud and fog. The model evaluates performability of the MRT metric over the variation of parameters that have the greatest influence on availability (container MTTF) and performance (cloud processing capacity).</p>", "Keywords": "Internet of things (IoT); Performance evaluation; Performability; E-health; Smart hospital; Stochastic petri nets", "DOI": "10.1007/s10922-024-09872-2", "PubYear": 2024, "Volume": "32", "Issue": "4", "JournalId": 20591, "JournalTitle": "Journal of Network and Systems Management", "ISSN": "1064-7570", "EISSN": "1573-7705", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Federal University of Piauí (UFPI), Picos, Brazil"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Federal University of Alagoas, Maceió, Brazil"}, {"AuthorId": 3, "Name": "Glauber Gonçalves", "Affiliation": "Federal University of Piauí (UFPI), Picos, Brazil"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Federal University of Piauí (UFPI), Picos, Brazil; Corresponding author."}], "References": [{"Title": "Dependability evaluation of a disaster recovery solution for IoT infrastructures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1828", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "IoT Inspector", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "2", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}, {"Title": "Analysis of factors affecting IoT-based smart hospital design", "Authors": "Banu Çalış Uslu; Ertuğ Okay; Erkan Dursun", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "67", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Data Processing on Edge and Cloud: A Performability Evaluation and Sensitivity Analysis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Network and Systems Management"}, {"Title": "Availability Modeling and Performance Improving of a Healthcare Internet of Things (IoT) System", "Authors": "<PERSON><PERSON><PERSON> Tang; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "310", "JournalTitle": "IoT"}, {"Title": "Performance and availability evaluation of an smart hospital architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Iure <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "10", "Page": "2401", "JournalTitle": "Computing"}, {"Title": "Performance and availability evaluation of an smart hospital architecture", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Iure <PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "10", "Page": "2401", "JournalTitle": "Computing"}, {"Title": "Maximising the availability of an internet of medical things system using surrogate models and nature-inspired approaches", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "2/3", "Page": "291", "JournalTitle": "International Journal of Grid and Utility Computing"}]}, {"ArticleId": 118184988, "Title": "Correction: What topics and emotions expressed by glaucoma patients? A sentiment analysis perspective", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13278-024-01346-2", "PubYear": 2024, "Volume": "14", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Coventry University, Coventry, UK; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, Community College, King Saud University, Riyadh, Hosam Al-Samarraie, Saudi Arabia"}, {"AuthorId": 3, "Name": "Hosam Al-Samarraie", "Affiliation": "School of Design, University of Leeds, Leeds, UK; Centre for Instructional Technology and Multimedia, Universiti Sains Malaysia, Penang, Malaysia"}], "References": []}, {"ArticleId": 118184989, "Title": "Code and Data Repository for Stability Representations of Many-to-One Matching Problems: An Integer Optimization Approach", "Abstract": "", "Keywords": "many-to-one stable matching; integer optimization; computational optimization", "DOI": "10.1287/ijoc.2022.1237.cd", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Hoda Atef Yekta", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 118185051, "Title": "Assortment Optimization: An Annotated Reading Assortment", "Abstract": "<p> Which varieties or brands of a product should a retailer stock on its shelf? Carrying a large variety caters to more customers' needs, but could cannibalize the sales of high-end brands and also cause an inventory nightmare. Assortment optimization aims to formalize these tradeoffs, with the basic problem being as follows. There is a universe of brands j ∈ U , each with a market-accepted price r <sub>j</sub> . For any S ⊆ U , a function ϕ ( j, S ) indicates the probability that a representative customer from the population would purchase j when given the choice from assortment S , under the market prices. The optimization problem is to maximize the average revenue per customer, i.e. </p><p>[EQUATION] (1)</p><p> possibly with constraints on S due to shelf size. Assortment optimization started out by showing how to efficiently find the optimal S from the exponentially many possibilities, under well-established parametric forms for the function ϕ that are called ( discrete) choice models. Since then, the literature has developed choice models of its own that are specialized for assortment optimization. The basic problem has also been extended, and connected with topics such as online algorithms, machine learning, and mechanism design that are mainstream in the Economics and Computation community, with a vast horizon for future directions. </p><p>This is an annotated reading list about assortment optimization, that aims to provide broad coverage while facing a \"cardinality constraint\" on the number of papers in the assortment.</p>", "Keywords": "", "DOI": "10.1145/3699824.3699835", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Columbia University"}], "References": []}, {"ArticleId": 118185079, "Title": "Crabtree: Rust API Test Synthesis Guided by Coverage and Type", "Abstract": "<p>Rust type system constrains pointer operations, preventing bugs such as use-after-free. However, these constraints may be too strict for programming tasks such as implementing cyclic data structures. For such tasks, programmers can temporarily suspend checks using the unsafe keyword. Rust libraries wrap unsafe code blocks and expose higher-level APIs. They need to be extensively tested to uncover memory-safety bugs that can only be triggered by unexpected API call sequences or inputs. While prior works have attempted to automatically test Rust library APIs, they fail to test APIs with common Rust features, such as polymorphism, traits, and higher-order functions, or they have scalability issues and can only generate tests for a small number of combined APIs. We propose Crabtree, a testing tool for Rust library APIs that can automatically synthesize test cases with native support for Rust traits and higher-order functions. Our tool improves upon the test synthesis algorithms of prior works by combining synthesis and fuzzing through a coverage- and type-guided search algorithm that intelligently grows test programs and input corpus towards testing more code. To the best of our knowledge, our tool is the first to generate well-typed tests for libraries that make use of higher-order trait functions. Evaluation of Crabtree on 30 libraries found four previously unreported memory-safety bugs, all of which were accepted by the respective authors.</p>", "Keywords": "", "DOI": "10.1145/3689733", "PubYear": 2024, "Volume": "8", "Issue": "OOPSLA2", "JournalId": 39163, "JournalTitle": "Proceedings of the ACM on Programming Languages", "ISSN": "", "EISSN": "2475-1421", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}, {"AuthorId": 5, "Name": "Corina S. <PERSON>", "Affiliation": "Carnegie Mellon University, Pittsburgh, USA"}], "References": [{"Title": "Stacked borrows: an aliasing model for Rust", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "4", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Memory-Safety Challenge Considered Solved? An In-Depth Study with All Rust CVEs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Software Engineering and Methodology"}, {"Title": "Modular specification and verification of closures in Rust", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "OOPSLA", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Aeneas: Rust verification by functional translation", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "ICFP", "Page": "711", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Verus: Verifying Rust Programs using Linear Ghost Types", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "OOPSLA1", "Page": "286", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Leveraging Rust Types for Program Synthesis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1414", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Flux: Liquid Types for Rust", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "7", "Issue": "PLDI", "Page": "1533", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "API-Driven Program Synthesis for Testing Static Typing Implementations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "8", "Issue": "POPL", "Page": "1850", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 118185107, "Title": "Causal Inference under Incentives: An Annotated Reading List", "Abstract": "<p>We provide an overview of research on causal inference in the presence of strategic agents. Work in this area uses tools from econometrics, statistics, machine learning, and game theory to infer causal relationships between treatments and outcomes of interest when the treated individuals have an incentive to behave strategically.</p>", "Keywords": "", "DOI": "10.1145/3699824.3699833", "PubYear": 2024, "Volume": "22", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Carnegie Mellon University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Stanford University"}], "References": []}, {"ArticleId": 118185189, "Title": "Navigating STEM careers with AI mentors: a new IDP journey", "Abstract": "Introduction <p>Mentoring is crucial to the success of STEM higher education. The Individual Development Plan (IDP) is a common career development tool in STEM graduate education that facilitates structured mentor-mentee interactions and goal setting. This study examined the integration of AI mentors into the myIDP framework to provide real-time support and career insights.</p> Methods <p>Using Google Gemini as an AI mentor, this study developed and assessed AI prompts within the myIDP framework. Eighteen STEM graduate students, primarily from underrepresented groups, were trained to engage with the AI mentor. Their interactions, feedback, and comments were analyzed using sentiment and thematic analysis.</p> Results <p>Participants reported positive experiences with AI mentors, noting benefits, such as immediate responses, up-to-date information, access to multiple AI mentors, enhanced ownership of career development, and time savings. However, concerns about misinformation, bias, privacy, equity, and algorithmic influences have also been raised. The study identified two hybrid human-AI mentoring models—Sequential Integration and Concurrent Collaboration—that combine the unique strengths of human and AI mentors to enhance the mentoring process.</p> Discussion <p>This study underscores the potential of AI mentors to enhance IDP practices by providing timely feedback and career information, thereby empowering students in their STEM career development. The proposed human-AI mentoring models show promise in supporting underrepresented minorities, potentially broadening participation in STEM fields.</p>", "Keywords": "STEM mentoring;career development;career planning;human-AI mentoring;individual development plan;large language model;mentorship;myIDP", "DOI": "10.3389/frai.2024.1461137", "PubYear": 2024, "Volume": "7", "Issue": "", "JournalId": 61789, "JournalTitle": "Frontiers in Artificial Intelligence", "ISSN": "", "EISSN": "2624-8212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Education, Virginia Commonwealth University, Richmond, VA, United States."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Education, Virginia Commonwealth University, Richmond, VA, United States."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Education, Virginia Commonwealth University, Richmond, VA, United States."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Education, Virginia Commonwealth University, Richmond, VA, United States."}], "References": [{"Title": "Ethical Questions Raised by AI-Supported Mentoring in Higher Education", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "624050", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Chatbots as a Tool to Scale Mentoring Processes: Individually Supporting Self-Study in Higher Education", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "64", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Are We There Yet? - A Systematic Literature Review on Chatbots in Education", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "4", "Issue": "", "Page": "89", "JournalTitle": "Frontiers in Artificial Intelligence"}, {"Title": "Supporting Teachers’ Professional Development With Generative AI: The Effects on Higher Order Thinking and Self-Efficacy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "", "Page": "1267", "JournalTitle": "IEEE Transactions on Learning Technologies"}]}, {"ArticleId": 118185212, "Title": "Comparing Machine Learning Techniques for Detecting Chronic Kidney Disease in Early Stage", "Abstract": "<p>CKD is a gradual disease that affects millions of people throughout the United States and results in high morbidity and mortality rates. Chronic Kidney Disease is an ailment that culminates in a gradual loss of kidney function over t,ime. Early detection is essential since timely interventions may prevent the progression of CKD, improve outcomes and survival for patients with CKD, and reduce healthcare costs. In the recent decade, machine learning models have emerged as a game-changing tool in medical diagnostics, leveraging big data and complex algorithms to find patterns almost invisible to clinicians and physicians. This study deployed and evaluated various machine learning approaches for the early detection of CKD, focusing on their comparative performance, strengths, and weaknesses. Machine learning transforms medical diagnosis by leveraging big data and sophisticated algorithms to find patterns that might otherwise elude healthcare professionals. The dataset used for this research will be the CKD dataset, which was contributed to by the Cleveland Clinic in 2021. The dataset can be accessed publicly through the University of California, Irvine's UCI Machine Learning Repository. In this project, the analyst compared and contrasted the performance of Logistic Regression, Decision Trees, and Random Forests. Experimentation results demonstrated that logistic regression had the best performance, yielding a perfect F1 score and accuracy, closely followed by random forest. This result showed that the Logistic model ideally classified all the instances in the test set. Consolidating machine learning algorithms into the early detection of Chronic Kidney Disease (CKD) holds substantial promise for transforming clinical practice. Healthcare professionals can enhance diagnostic accuracy and facilitate timely interventions by leveraging proposed algorithms such as logistic regression.</p>", "Keywords": "Chronic Kidney Disease; Early CKD Detection; Medical Diagnosis; Machine Learning algorithms; Logistic Regression; Random Forest; Decision Tree", "DOI": "10.32996/jcsts.2024.6.4.11", "PubYear": 2024, "Volume": "6", "Issue": "4", "JournalId": 88928, "JournalTitle": "Journal of Computer Science and Technology Studies", "ISSN": "", "EISSN": "2709-104X", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Master of Arts in Physics, Western Michigan University, USA"}, {"AuthorId": 2, "Name": " <PERSON><PERSON>", "Affiliation": "PhD student in Information Technology, University of the Cumberlands, KY, USA"}, {"AuthorId": 3, "Name": " Md <PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Science and Quantitative Methods, Gannon University, USA"}, {"AuthorId": 4, "Name": " <PERSON>", "Affiliation": "Master of Science in Business Analytics, Grand Canyon University"}, {"AuthorId": 5, "Name": " <PERSON>", "Affiliation": "Master of Science in Business Analytics, Grand Canyon University"}, {"AuthorId": 6, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Management Science and Quantitative Methods, Gannon University, USA"}], "References": []}, {"ArticleId": *********, "Title": "A Survey on Mobile Digital Forensic: Taxonomy, Tools, and Challenges", "Abstract": "Modern time largely rely on mobile gadgets as a means of communication, and entertainment. These gadgets now plays a vital role in forensic analysis and criminal investigations as it store a plethora of private and sensitive data. Investigating feasible digital incidents and preserving mobile device security became crucial as the proliferation of mobile devices continues to influence modern society. In this survey; mobile threats, investigative process models for mobile forensics, evidence sources, forensics tools, and field problems are all covered in‐depth. Further, a comprehensive analysis examining the capabilities, features, and efficiency of available distinct mobile forensic tools namely Belkasoft, MOBILedit, and Magnet AXIOM for retrieving, analyzing, and presenting digital evidence is being presented. The objective of the study is to assist forensic investigators in choosing the best tool for specific investigations. The findings of our study reveals the complexity of mobile operating systems, investigative process supported with sources of evidence, and forensic tools which makes it clear that the area of mobile forensics is dynamic and require modern skill adaptation along and innovative development in future. Finally, the study presents current challenges encountered during mobile forensic investigations as researchers can enhance their procedures and tactics for mobile device examination and evidence extraction by fostering a greater knowledge of the difficulties involved in mobile forensics.", "Keywords": "", "DOI": "10.1002/spy2.470", "PubYear": 2025, "Volume": "8", "Issue": "2", "JournalId": 7086, "JournalTitle": "Security and Privacy", "ISSN": "2475-6725", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Enginering and Technology Gujarat Technological University  Ahmedabad Gujarat India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Enginering and Technology Gujarat Technological University  Ahmedabad Gujarat India"}], "References": [{"Title": "A comprehensive survey of tools and techniques mitigating computer and mobile malware attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "92", "Issue": "", "Page": "107143", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 118185311, "Title": "Recent Developments in Pandora's Box Problem: Variants and Applications", "Abstract": "<p>In 1979, <PERSON><PERSON><PERSON> introduced Pandora's box problem as a framework for sequential search with costly inspections. Recently, there has been a surge of interest in Pandora's box problem, particularly among researchers working at the intersection of economics and computation. This survey provides an overview of the recent literature on Pandora's box problem, including its latest extensions and applications in areas such as market design, decision theory, and machine learning.</p>", "Keywords": "", "DOI": "10.1145/3699814.3699817", "PubYear": 2023, "Volume": "21", "Issue": "1", "JournalId": 21194, "JournalTitle": "ACM SIGecom Exchanges", "ISSN": "1551-9031", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Carnegie Mellon University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Princeton University"}], "References": []}]