[{"ArticleId": 119996094, "Title": "Large language models to process, analyze, and synthesize biomedical texts: a scoping review", "Abstract": "<p>The advent of large language models (LLMs) such as BERT and, more recently, GPT, is transforming our approach of analyzing and understanding biomedical texts. To stay informed about the latest advancements in this area, there is a need for up-to-date summaries on the role of LLM in Natural Language Processing (NLP) of biomedical texts. Thus, this scoping review aims to provide a detailed overview of the current state of biomedical NLP research and its applications, with a special focus on the evolving role of LLMs. We conducted a systematic search of PubMed, EMBASE, and Google Scholar for studies and conference proceedings published from 2017 to December 19, 2023, that develop or utilize LLMs for NLP tasks in biomedicine. We evaluated the risk of bias in these studies using a 3-item checklist. From 13,823 references, we selected 199 publications and conference proceedings for our review. LLMs are being applied to a wide array of tasks in the biomedical field, including knowledge management, text mining, drug discovery, and evidence synthesis. Prominent among these tasks are text classification, relation extraction, and named entity recognition. Although BERT-based models remain prevalent, the use of GPT-based models has substantially increased since 2023. We conclude that, despite offering opportunities to manage the growing volume of biomedical data, LLMs also present challenges, particularly in clinical medicine and evidence synthesis, such as issues with transparency and privacy concerns.</p>", "Keywords": "Natural language processing; Bioinformatics; Biomedicine; Large language models; BERT; Evidence synthesis", "DOI": "10.1007/s44163-024-00197-2", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 91443, "JournalTitle": "Discover Artificial Intelligence", "ISSN": "", "EISSN": "2731-0809", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Reproducible Science, University of Zurich, Zurich, Switzerland; Division of Biostatistics, EBPI, University of Zurich, Zurich, Switzerland; Corresponding author."}, {"AuthorId": 2, "Name": "Sijing Qin", "Affiliation": "Center for Reproducible Science, University of Zurich, Zurich, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Biostatistics, EBPI, University of Zurich, Zurich, Switzerland; ZHAW School of Engineering, Zurich University of Applied Science (ZHAW), Winterthur, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computational Linguistics, University of Zurich, Zurich, Switzerland"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational Linguistics, University of Zurich, Zurich, Switzerland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computational Linguistics, University of Zurich, Zurich, Switzerland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Center for Reproducible Science, University of Zurich, Zurich, Switzerland; Clinical Neuroscience Center, University of Zurich, Zurich, Switzerland"}], "References": [{"Title": "BioBERT: a pre-trained biomedical language representation model for biomedical text mining", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1234", "JournalTitle": "Bioinformatics"}, {"Title": "An open source machine learning framework for efficient and transparent systematic reviews", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "2", "Page": "125", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Multi-class classification of COVID-19 documents using machine learning algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "60", "Issue": "2", "Page": "571", "JournalTitle": "Journal of Intelligent Information Systems"}, {"Title": "Pre-trained Language Models in Biomedical Domain: A Systematic Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "56", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A survey of the recent trends in deep learning for literature based discovery in the biomedical domain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "568", "Issue": "", "Page": "127079", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 119996111, "Title": "Assessment of civic education in universities from a multidimensional perspective: the Integration of OBE and CIPP models", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.143413", "PubYear": 2024, "Volume": "25", "Issue": "11", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "A Comparison of Penguin Swarm Optimization Algorithms for Enhancing Network Throughput", "Abstract": "", "Keywords": "", "DOI": "10.31645/JISRC.*********", "PubYear": 2024, "Volume": "22", "Issue": "2", "JournalId": 57366, "JournalTitle": "Journal of Independent Studies and Research - Computing", "ISSN": "2412-0448", "EISSN": "1998-4154", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Technology Group National Bank of Pakistan Karachi, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, UIT University, Karachi, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, NUCES-FAST, Karachi, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Karakoram International University, Pakistan Karachi, Pakistan"}], "References": []}, {"ArticleId": *********, "Title": "Intelligent judgement of calligraphy and painting image categories based on integrated classifier learning", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.143414", "PubYear": 2024, "Volume": "25", "Issue": "11", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119996158, "Title": "Health state assessment based on the Parallel–Serial Belief Rule Base for industrial robot systems", "Abstract": "To ensure industrial robots function correctly and implement adequate safety and maintenance strategies, it is crucial to accurately and promptly assess their health. Belief Rule Base (BRB) expert systems excel in modeling complex systems. However, BRB can lead to a “combinatorial explosion” of rules during optimization, reducing interpretability and increasing model complexity. Consequently, this paper introduces a Parallel–Serial Belief Rule Base (PS-BRB) model for assessing health states. Through structural and parameter optimization, the model’s interpretability is enhanced, and its complexity is minimized. To quantify the interpretability of the BRB model, we propose two evaluation criteria: rule simplicity and conflict. To further the modeling performance, we introduce a feature selection strategy and a feature grouping strategy of PS-BRB to construct a BRB model with enhanced interpretability. Finally, a versatile industrial robot system operating in a company’s body-in-white welding production line will be used as a case study to demonstrate the method’s effectiveness.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109856", "PubYear": 2025, "Volume": "142", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Changchun University of Technology, Changchun, 130012, China"}, {"AuthorId": 2, "Name": "Wei<PERSON> He", "Affiliation": "Changchun University of Technology, Changchun, 130012, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Changchun University of Technology, Changchun, 130012, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Changchun University of Technology, Changchun, 130012, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "High-Tech Institute of Qingzhou, Qingzhou, 262500, China"}, {"AuthorId": 6, "Name": "Bangcheng Zhang", "Affiliation": "Changchun University of Technology, Changchun, 130012, China;Changchun Institute of Technology, Changchun, 130012, China;Corresponding author at: Changchun University of Technology, Changchun, 130012, China"}], "References": [{"Title": "Introducing autonomous aerial robots in industrial manufacturing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "60", "Issue": "", "Page": "312", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Knowledge-driven framework for industrial robotic systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "2", "Page": "771", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "HMM‐TCN‐based health assessment and state prediction for robot mechanical axis", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "12", "Page": "10476", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "A practical and synchronized data acquisition network architecture for industrial robot predictive maintenance in manufacturing assembly lines", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2022, "Volume": "74", "Issue": "", "Page": "102287", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "A novel hybrid framework for single and multi-robot path planning in a complex industrial environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "35", "Issue": "2", "Page": "587", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Lithium-ion battery health assessment method based on belief rule base with interpretability", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "138", "Issue": "", "Page": "110160", "JournalTitle": "Applied Soft Computing"}, {"Title": "An interval construction belief rule base with interpretability for complex systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "229", "Issue": "", "Page": "120485", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A double inference engine belief rule base for oil pipeline leakage", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "240", "Issue": "", "Page": "122587", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Efficacy assessment for multi-vehicle formations based on data augmentation considering reliability", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "61", "Issue": "", "Page": "102504", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 119996260, "Title": "Grasping of Cylindrical Structures Using an Underwater Snake Robot Without Force/Torque Sensors and Actuator Waterproofing", "Abstract": "<p>This paper presents an underwater snake robot composed of submersible actuators designed for minimal friction, a lubricant-free gear reducer, and no waterproof sealing. This makes it suitable for direct exposure to water. In particular, this paper focuses on underwater interactive tasks with an object. Static force analysis for straightforward tasks, such as the wrapping of a pole structure, is conducted. Experiments were performed to evaluate the snake robot outside a water environment. The results indicated that the static model was valid, although the errors were not negligible. The potential of executing various tasks with this sensorless underwater snake robot, such as wrapping around the pole and its collection or turning on/off a lever underwater, is presented.</p>", "Keywords": "underwater robot;snake robot;sensorless force control;interactive task", "DOI": "10.20965/jrm.2024.p1458", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Robotics, Ritsumeikan University, 1-1-1 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ga 525-8577, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Robotics, Ritsumeikan University, 1-1-1 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ga 525-8577, Japan"}, {"AuthorId": 3, "Name": "Yuto Iwasaki", "Affiliation": "Department of Robotics, Ritsumeikan University, 1-1-1 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ga 525-8577, Japan"}], "References": []}, {"ArticleId": 119996262, "Title": "Development of Automated Display Shelf System for New Purchasing Experience by Dynamic Product Layout Changes", "Abstract": "<p>This study proposes a micro-logistics node as a new infrastructure envisioned for future convenience stores. This system automates the management of products on shelves, reduces the workload on store clerks, and provides an entirely new shopping experience with optimal displays based on the time and customers visiting the store. Automated warehouse technology has recently advanced, and the storage and removal of items from warehouse racks have been successfully automated. However, when considering implementation in convenience stores, robotic systems must adapt to their unique conditions: operation in limited backyard space, handling a wide variety of products with different sizes and shapes, and minimization of dead space in product displays. To address these challenges, this study developed a robot system that travels on vertical and horizontal tracks installed behind display shelves, and places products stored in special trays at any coordinate on the shelves.</p>", "Keywords": "product stocking robot;automated display shelf;convenience store;product layout;planogram", "DOI": "10.20965/jrm.2024.p1527", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Systems Design, Department of Mechanical Systems Engineering, Tokyo Metropolitan University, 6-6 Asahigaoka, Hino, Tokyo 191-0065, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Systems Design, Department of Mechanical Systems Engineering, Tokyo Metropolitan University, 6-6 Asahigaoka, Hino, Tokyo 191-0065, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Institute of Technology, Tokyo College, 1220-2 Kunugida-machi, Hachioji, Tokyo 193-0997, Japan"}], "References": [{"Title": "A robot hand with free-rotating grips for switching three different grasping modes", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "17-18", "Page": "920", "JournalTitle": "Advanced Robotics"}, {"Title": "Development of a restroom cleaning system for convenience stores", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "23", "Page": "1230", "JournalTitle": "Advanced Robotics"}, {"Title": "Mobile Module in Reconfigurable Intelligent Space: Applications and a Review of Developed Versions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "1130", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}]}, {"ArticleId": 119996266, "Title": "Bio-Inspired Undulatory Locomotion Control Strategy for Novel Soft Robot Based on Auxetic Structures", "Abstract": "<p>This paper presents a novel locomotion soft robot that exploits the properties of auxetic structures to achieve bio-inspired undulatory locomotion. To reduce the dependence on computation and control strategies, we propose to develop a soft structure using a combination of positive <PERSON><PERSON><PERSON>’s ratio lattice structure and negative <PERSON><PERSON><PERSON>’s ratio lattice structure that creates a dorsoventral undulating wave pattern under compressive load. This is combined with a laterally undulating gait pattern exhibited by giant salamanders. The soft structure is actuated with nylon cables attached to servo motors, mimicking muscles. We use finite element analysis (FEA) methods to accurately model the soft structure’s deflection pattern, which is then used to create a control strategy for the robot. We develop a mathematical model and a subsequent gait pattern based on the sequential actuation of the nylon cables. The gait was experimentally tested and further improved with closed-loop error compensation. The research proves linear locomotion is possible through the proposed design with the lowest computational requirements.</p>", "Keywords": "soft robot;undulatory locomotion;auxetic structures;negative Poisson’s ratio", "DOI": "10.20965/jrm.2024.p1428", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Division of Human Mechanical Systems and Design, Graduate School of Engineering, Hokkaido University, Kita 13, <PERSON><PERSON> 9, Kita-ku, Sapporo, Hokkaido 060-0813, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Human Mechanical Systems and Design, Graduate School of Engineering, Hokkaido University, Kita 13, <PERSON><PERSON> 9, Kita-ku, Sapporo, Hokkaido 060-0813, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Mechanical and Aerospace Engineering, Faculty of Engineering, Hokkaido University, Kita 13, Nishi 8, Kita-ku, Sapporo, Hokkaido 060-8628, Japan"}], "References": [{"Title": "Soft Robotics: Research, Challenges, and Prospects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "45", "JournalTitle": "Journal of Robotics and Mechatronics"}, {"Title": "Review of snake robots in constrained environments", "Authors": "<PERSON><PERSON> Liu; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "141", "Issue": "", "Page": "103785", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "From a Deployable Soft Mechanism Inspired by a Nemertea Proboscis to a Robotic Blood Vessel Mechanism", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "234", "JournalTitle": "Journal of Robotics and Mechatronics"}]}, {"ArticleId": 119996272, "Title": "Detecting Deceptive Identities: A Machine Learning Approach to Unveiling Fake Profiles on Social Media", "Abstract": "<p>In the digital age, online security and authenticity face serious issues due to the widespread use of fraudulent personas on social media sites. This research article outlines a comprehensive methodology for detecting fake profiles, starting with the gathering of user profile data from diverse social media sites. Pertinent characteristics such as completeness markers, posting frequency, network diversity, and language patterns are extracted from profiles to distinguish between real and fraudulent accounts. This work involves assessing various classifiers suitable for fake profile classification task, including Decision Trees (DT), Random Forest (RF), Logistic Regression (LR), Support Vector Machines (SVM), Adaptive Boosting (AdaBoost), Gradient Boosting (GB) and Voting Classifier (VC). The effectiveness of Principal Component Analysis (PCA) in improving model performance and reducing dimensionality is also investigated. Each classifier is trained on the Kaggle dataset after thorough evaluation, exploring both the raw features and those reduced by PCA. The Random Forest with PCA has the highest prediction rate in detecting fake profiles at 92.5%. The article concludes with insights into classifier performance and suggestions for further research to bolster fake profile detection accuracy and online community security.</p>", "Keywords": "Fake profile detection; Social media; Machine learning; Principal component analysis", "DOI": "10.1007/s42979-024-03562-1", "PubYear": 2025, "Volume": "6", "Issue": "1", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Mepco Schlenk Engineering College, Sivakasi, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Kamaraj College of Engineering and Technology, K.vellakulam, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, Kamaraj College of Engineering and Technology, K.vellakulam, India"}], "References": [{"Title": "Twitter spam account detection based on clustering and classification methods", "Authors": "<PERSON><PERSON>; <PERSON>; Wan<PERSON> Wu", "PubYear": 2020, "Volume": "76", "Issue": "7", "Page": "4802", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An ensemble machine learning approach through effective feature extraction to classify fake news", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "47", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multiple features based approach for automatic fake news detection on social networks using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106983", "JournalTitle": "Applied Soft Computing"}, {"Title": "Fake Profile Detection on Social Networking Websites: A Comprehensive Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "3", "Page": "271", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Fake news detection on Pakistani news using machine learning and deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118558", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Fake Profile Detection Using Machine Learning Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "10", "Issue": "10", "Page": "74", "JournalTitle": "Journal of Computer and Communications"}, {"Title": "A novel machine learning and face recognition technique for fake accounts detection system on cyber social networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "17", "Page": "26353", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Behavioral biometrics to detect fake expert profiles during negotiation", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>dr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "32", "Page": "78293", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Detection of Fake Profiles on Online Social Network Platforms: Performance Evaluation of Artificial Intelligence Techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "5", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "A novel approach to fake news detection in social networks using genetic algorithm applying machine learning classifiers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "82", "Issue": "6", "Page": "9029", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 119996275, "Title": "Face Mask Surveillance Using Mobile Robot Equipped with an Omnidirectional Camera", "Abstract": "<p>Detecting humans in images not only provides vital data for a wide array of applications in intelligent systems but also allows for the classification of specific groups of individuals for authorization through various methods based on several examples. This paper presents a novel approach to classify persons wearing a face mask as an example. The system utilizes an omnidirectional camera on the mobile robot. This choice is driven by the camera’s ability to capture a complete 360° scene in a single shot, enabling the system to gather a wide range of information within its operational environment. Our system classifies persons using a deep learning model by gathering information from the equirectangular panoramic images, estimating a person’s position, and computing robot path planning without using any distance sensors. In the proposed method, the robot can classify two groups of persons: those facing the camera but without face masks and those not facing the camera. In both cases, the robot approaches the persons, inspects their face masks, and issues warnings on its screen. The evaluation experiments are designed to validate our system performance in a static indoor setting. The results indicate that our suggested method can successfully classify persons in both cases while approaching them.</p>", "Keywords": "human classification;face mask detection;mobile robot;omnidirectional camera", "DOI": "10.20965/jrm.2024.p1495", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> E<PERSON>", "Affiliation": "Degree Programs in Systems and Information Engineering, Graduate School of Science and Technology, University of Tsukuba, 1-1-1 Tennodai, Tsukuba, Ibaraki 305-8573, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Degree Programs in Systems and Information Engineering, Graduate School of Science and Technology, University of Tsukuba, 1-1-1 Tennodai, Tsukuba, Ibaraki 305-8573, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Degree Programs in Systems and Information Engineering, Graduate School of Science and Technology, University of Tsukuba, 1-1-1 Tennodai, Tsukuba, Ibaraki 305-8573, Japan"}], "References": [{"Title": "RETRACTED ARTICLE: Automatic alert generation in a surveillance systems for smart city environment using deep learning algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "2", "Page": "635", "JournalTitle": "Evolutionary Intelligence"}, {"Title": "Optimized visual recognition algorithm in service robots", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "3", "Page": "172988142092530", "JournalTitle": "International Journal of Advanced Robotic Systems"}, {"Title": "A deep learning approach to building an intelligent video surveillance system", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "4", "Page": "5495", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Outdoor Human Detection with Stereo Omnidirectional Cameras", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "6", "Page": "1193", "JournalTitle": "Journal of Robotics and Mechatronics"}, {"Title": "Minimum distance calculation using skeletal tracking for safe human-robot interaction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102253", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Detection and Following of Moving Target by an Indoor Mobile Robot using Multi-sensor Information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "13", "Page": "357", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Automatic Human Detection Using Reinforced Faster-RCNN for Electricity Conservation System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "1261", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Real-Time Human Detection for Intelligent Video Surveillance: An Empirical Research and In-depth Review of its Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Path Planning Using a Flow of Pedestrian Traffic in an Unknown Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "6", "Page": "1460", "JournalTitle": "Journal of Robotics and Mechatronics"}]}, {"ArticleId": 119996278, "Title": "Knot Tying, Hanging, and Transporting Motions of Snake Robots by Utilizing Half-Hitch Knot", "Abstract": "<p>This paper proposes a hanging motion for snake robots by utilizing half-hitch knot tying, which is a type of rope work. The proposed method prevents a robot from falling off a pipe by tying it to the pipe. In addition, the robot can hang from a pipe by grasping it using the knotting part. The parts other than the knotting part can be operated arbitrarily and applied to various actions. We propose a transporting motion as one of the applications of the hanging motion. The effectiveness of the proposed motion was experimentally verified using an actual robot.</p>", "Keywords": "snake robot;motion planning;locomotion on pipes", "DOI": "10.20965/jrm.2024.p1396", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, 1-5-1 Chofugaoka, Chofu, Tokyo 182-8585, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Tokyo Denki University, 5 <PERSON><PERSON>, <PERSON><PERSON>-ku, Tokyo 120-8551, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, 1-5-1 Chofugaoka, Chofu, Tokyo 182-8585, Japan"}], "References": [{"Title": "Motion planning of a snake robot that moves in crowded pipes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "16", "Page": "781", "JournalTitle": "Advanced Robotics"}, {"Title": "Principle of object support by Rope deformation and its application to Rope climbing by snake robot", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "9", "Page": "591", "JournalTitle": "Advanced Robotics"}]}, {"ArticleId": 119996428, "Title": "Advancing Software Project Effort Estimation: Leveraging a NIVIM for Enhanced Preprocessing", "Abstract": "Software development effort estimation (SDEE) is essential for effective project planning and relies heavily on data quality affected by incomplete datasets. Missing data (MD) are a prevalent problem in machine learning, yet many models treat it arbitrarily despite its significance. Inadequate handling of MD may introduce bias into the induced knowledge. It can be challenging to choose optimal imputation approaches for software development projects. This article presents a novel incomplete value imputation model (NIVIM) that uses a variational autoencoder (VAE) for imputation and synthetic data. By combining contextual and resemblance components, our approach creates an SDEE dataset and improves the data quality using contextual imputation. The key feature of the proposed model is its applicability to a wide variety of datasets as a preprocessing unit. Comparative evaluations demonstrate that NIVIM outperforms existing models such as VAE, generative adversarial imputation network (GAIN), ‐nearest neighbor (K‐NN), and multivariate imputation by chained equations (MICE). Our proposed model NIVIM produces statistically substantial improvements on six benchmark datasets, that is, ISBSG, Albrecht, COCOMO81, Desharnais, NASA, and UCP, with an average improvement in RMSE of 11.05% to 17.72% and MAE of 9.62% to 21.96% .", "Keywords": "", "DOI": "10.1002/smr.2745", "PubYear": 2025, "Volume": "37", "Issue": "1", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering Beihang University  Beijing China;Faculty of Engineering, Environment and Computing Coventry University  Coventry UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Beihang University  Beijing China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering Beihang University  Beijing China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering Beihang University  Beijing China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering Beihang University  Beijing China"}], "References": [{"Title": "A systematic review of studies on use case points and expert‐based estimation of software development effort", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "Missing value imputation in multivariate time series with end-to-end generative adversarial networks", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "551", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}, {"Title": "On the value of filter feature selection techniques in homogeneous ensembles effort estimation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "6", "Page": "e2343", "JournalTitle": "Journal of Software: Evolution and Process"}, {"Title": "Fuzzy C-mean Missing Data Imputation for Analogy-based Effort Estimation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "628", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "A survey on missing data in machine learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "140", "JournalTitle": "Journal of Big Data"}, {"Title": "Deep learning versus conventional methods for missing data imputation: A review and comparative study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "227", "Issue": "", "Page": "120201", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119996474, "Title": "Hybrid-sched: a QoS adaptive offline–online scheduler for real-time tasks on multi-cores", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10951-024-00831-y", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 9977, "JournalTitle": "Journal of Scheduling", "ISSN": "1094-6136", "EISSN": "1099-1425", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Contention-aware optimal scheduling of real-time precedence-constrained task graphs on heterogeneous distributed systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "101706", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Scheduling directed acyclic graphs with optimal duplication strategy on homogeneous multiprocessor systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "115", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "An improved list-based task scheduling algorithm for fog computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "7", "Page": "1353", "JournalTitle": "Computing"}, {"Title": "SLAQA", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "Optimal task scheduling for partially heterogeneous systems", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "107", "Issue": "", "Page": "102815", "JournalTitle": "Parallel Computing"}, {"Title": "A novel hybrid heuristic-based list scheduling algorithm in heterogeneous cloud computing environment for makespan optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "102828", "JournalTitle": "Parallel Computing"}]}, {"ArticleId": 119996850, "Title": "Efficient alternating and joint distance minimization methods for adaptive spline surface fitting", "Abstract": "We propose a new paradigm for scattered data fitting with adaptive spline constructions based on the key interplay between parameterization and adaptivity. Specifically, we introduce two novel adaptive fitting schemes that combine moving parameterizations with adaptive spline refinement for highly accurate CAD models reconstruction from real-world scattered point clouds. The first scheme alternates surface fitting and data parameter optimization. The second scheme jointly optimizes the parameters and the surface control points. To combine the proposed fitting methods with adaptive spline constructions, we present a key treatment of boundary points. Industrial examples show that updating the parameterization, within an adaptive spline approximation framework, significantly reduces the number of degrees of freedom needed for a certain accuracy, especially if spline adaptivity is driven by suitably graded hierarchical meshes. The numerical experiments employ THB-splines, thus exploiting the existing CAD integration within the considered industrial setting, nevertheless, any adaptive spline construction can be chosen.", "Keywords": "Adaptive surface fitting; Parameter correction; L-BFGS; THB-splines", "DOI": "10.1016/j.gmod.2024.101251", "PubYear": 2025, "Volume": "137", "Issue": "", "JournalId": 6576, "JournalTitle": "Graphical Models", "ISSN": "1524-0703", "EISSN": "1524-0711", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Matematica e Informatica “Ulisse Dini”, Università degli Studi di Firenze, Viale Morgani 67/A, Firenze, 50134, Italy"}, {"AuthorId": 2, "Name": "Sofia Imperatore", "Affiliation": "Dipartimento di Matematica e Informatica “Ulisse Dini”, Università degli Studi di Firenze, Viale Morgani 67/A, Firenze, 50134, Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Inria Centre at Université Côte d’Azur, 2004 route des Lucioles - BP 93, Sophia Antipolis, 06902, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MTU Aero Engines AG, Dachauer Strasse 665, Munich, 80995, Germany"}], "References": [{"Title": "Reconstruction of 3D shapes with B-spline surface using diagonal approximation BFGS methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "26", "Page": "38091", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Customizable adaptive regularization techniques for B-spline modeling", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "71", "Issue": "", "Page": "102037", "JournalTitle": "Journal of Computational Science"}]}, {"ArticleId": 119996858, "Title": "Dictionary-based extraction of hyperbole and swear words for sarcasm detection in Indonesian Tweets", "Abstract": "", "Keywords": "", "DOI": "10.1007/s41870-024-02361-4", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "Novitasari Arlim", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "Shi<PERSON>q Al <PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "Re<PERSON>no <PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>n <PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "A machine learning approach in analysing the effect of hyperboles using negative sentiment tweets for sarcasm detection", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "5110", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Investigating the role of swear words in abusive language detection tasks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "57", "Issue": "1", "Page": "155", "JournalTitle": "Language Resources and Evaluation"}, {"Title": "Hybrid Deep Learning Model for Sarcasm Detection in Indian Indigenous Language Using Word-Emoji Embeddings", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Hate speech recognition in multilingual text: hinglish documents", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "15", "Issue": "3", "Page": "1319", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Leveraging contextual features to enhanced machine learning models in detecting COVID-19 fake news", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "5", "Page": "3233", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Identification of domain-specific euphemistic tweets using clustering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "16", "Issue": "1", "Page": "21", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Designing algorithm for context based analysis using deep learning (CNN + RNN) with image dataset", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "17", "Issue": "1", "Page": "599", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Enhancing sarcasm detection through grasshopper optimization with deep learning based sentiment analysis on social media", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "17", "Issue": "3", "Page": "1785", "JournalTitle": "International Journal of Information Technology"}, {"Title": "A semantic approach for sarcasm identification for preventing fake news spreading on social networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "International Journal of Information Technology"}]}, {"ArticleId": 119996859, "Title": "GFIDF:gradual fusion intent detection framework", "Abstract": "<p>Multimodal intent detection integrates various types of information to identify user intent, which is crucial for developing dialog systems that function effectively in complex, real-world environments. Current methods show potential for improving the exploration of connections between patterns and extracting key semantic features from nontextual data. Many researchers opt to fuse data at a single level. In this paper, the gradual fusion intent detection framework (GFIDF), which consists of two main modules, is proposed. The first module, the conical multilayer convolutional attention (CMCA) module, uses a conical multilayer convolutional architecture. This architecture allows the module to capture both local and global contextual information, refining feature representations. The CMCA module is designed to eliminate noise and enhance feature quality by leveraging adaptive convolutional operations. These operations produce a clearer characterization of multimodal data that facilitates alignment and fusion in subsequent processing stages. The second module, the multimodal split and recombination attention (MSRA) module, matches and integrates augmented features from the CMCA module with textual information. This module segments multimodal features into distinct blocks to focus attention on individual segments. By utilizing a block-level attention mechanism, the MSRA module captures interdependencies between modalities, aiding in the understanding of user intent. Four performance metrics are employed for evaluation: accuracy (ACC), F1 score, precision (P), and recall (R). Compared with the baseline model, all the metrics show improvements ranging from 1% to 3%. Experiments validate the CMCA module’s noise reduction effects when processing video and audio modalities. Additionally, the results demonstrate the effectiveness of the MSRA module in fusing the three modal features.</p>", "Keywords": "Multimodal intent detection; Nontextual data; Noise reduction; Feature fusion", "DOI": "10.1007/s11227-024-06708-3", "PubYear": 2025, "Volume": "81", "Issue": "1", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Software, Xinjiang University, Urumqi, China"}], "References": [{"Title": "Multimodal research in vision and language: A review of current and emerging trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "77", "Issue": "", "Page": "149", "JournalTitle": "Information Fusion"}, {"Title": "Joint intent detection and slot filling using weighted finite state transducer and BERT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "15", "Page": "17356", "JournalTitle": "Applied Intelligence"}, {"Title": "A Survey of Joint Intent Detection and Slot Filling Models in Natural Language Understanding", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "8", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A survey of transformers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "111", "JournalTitle": "AI Open"}, {"Title": "Multitask learning for multilingual intent detection and slot filling in dialogue systems", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "91", "Issue": "", "Page": "299", "JournalTitle": "Information Fusion"}, {"Title": "Emotion recognition from unimodal to multimodal analysis: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "99", "Issue": "", "Page": "101847", "JournalTitle": "Information Fusion"}, {"Title": "MBCFNet: A Multimodal Brain–Computer Fusion Network for human intention recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "296", "Issue": "", "Page": "111826", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 119996993, "Title": "Non-local physics informed neural networks for forward and inverse problems containing non-local operators", "Abstract": "<p>A novel idea, physics informed neural networks introduced a few years back to solve forward and inverse problems for differential equations using the physics information that lies inside them. The central pillar of the physics informed neural networks is automatic differentiation, which is based on the chain rule of differentiation. Automatic differentiation is not applicable to non-local operators because the standard chain rule of differentiation is not valid for non-local operators. Therefore, this work presents non-local physics informed neural networks, which use standard approximation methods for non-local operators and automatic differentiation for local operators to solve differential equations containing non-local operators (forward problems) as well as learn differential equations involving non-local operators (inverse problems). In this work, we consider the Caputo fractional derivative, Volterra integral, and Itô integral as non-local operators. Moreover, we demonstrate the efficiency of the non-local physics informed neural networks with different test examples like the time-fractional diffusion equation in one and two dimensions, the time-fractional Burgers’ equation (both equations involving Caputo fractional derivative as non-local operators), the fractional integro-differential equation (<PERSON>uto fractional derivative and <PERSON><PERSON>ra integral as non-local operators), and the stochastic fractional integro-differential equation (Caputo fractional derivative, <PERSON><PERSON> integral, and <PERSON><PERSON><PERSON> integral as non-local operators). Furthermore, for the non-smooth solution, we use the approximation method on non-uniform mesh for non-local operators and compare the results with the approximation method on uniform mesh. We also discuss the error analysis and convergence of the proposed non-local physics informed neural networks. Finally, we take real-world data, which is described by the differential equation containing non-local operators, and show the effectiveness of non-local physics-informed neural networks in addressing practical applications.</p>", "Keywords": "Physics informed neural networks; Fractional differential equations; Non-local operators; Itô integral", "DOI": "10.1007/s00521-024-10752-8", "PubYear": 2025, "Volume": "37", "Issue": "6", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Delhi, New Delhi, India; Institute of Mathematics and Computer Science, Universität Greifswald, Greifswald, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology Delhi, New Delhi, India; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Mathematics and Computer Science, Universität Greifswald, Greifswald, Germany"}], "References": []}, {"ArticleId": 119996997, "Title": "Detection of the SARS-CoV-2 S protein using AuNPs assisted differential-phase surface plasmon resonance biosensor", "Abstract": "Developing an efficient and sensitive method for detecting the coronavirus disease (COVID-19) is crucial. In this study, a phase-dependent SPR biosensor has been developed to detect the SARS-CoV-2 S protein with high sensitivity. The phase shifts of the SPR spectral interferogram are extracted using the windowed Fourier transform (WFT) method. The summation of the phase shifts of the prominent frequency, which make up the SPR spectral interferogram, is used as a numerical index to reflect the “negative” or “positive” results. The SARS-CoV-2 S protein antibody is modified onto an Au film, which serves as a sensing surface when detecting the SARS-CoV-2 S protein. The SARS-CoV-2 S protein antibody functionalized Au nanoparticles are used to form a (anti-S@AuNPs)-(SARS-CoV-2 S protein)-(Au film) sandwich structure, which reduces the SPR detection limitation to 1 ag/mL. An external laser is then used to induce the localized surface plasmon resonance (LSPR) between the AuNPs and the Au film in the sandwich structure, enhancing the detecting sensitivity by 357 %. This study provides a label-free method for detecting the SARS-CoV-2 S protein with low concentrations, indicating its potential in detecting the SARS-CoV-2 virus and viral disease diagnosis.", "Keywords": "", "DOI": "10.1016/j.sna.2024.116156", "PubYear": 2025, "Volume": "382", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China;<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> contributed to the work equally and should be regarded as co-first authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China;School of Physics and Optoelectronic Engineering, Guangdong University of Technology, Guangzhou, China;<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> contributed to the work equally and should be regarded as co-first authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Liu", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China;Correspondence to: School of Microelectronics and Materials Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545616, China.; Corresponding author;https://orcid.org/0000-0001-6817-5390"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China;Correspondence to: School of Microelectronics and Materials Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545616, China.; Corresponding author"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "School of Electronic Engineering, Guangxi University of Science and Technology, No.2, Wenchang Road, Liuzhou City, Guangxi 545006, China"}], "References": [{"Title": "Ultrasensitive supersandwich-type electrochemical sensor for SARS-CoV-2 from the infected COVID-19 patients using a smartphone", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "327", "Issue": "", "Page": "128899", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Self-referencing SPR biosensing with an ultralow limit-of-detection using long-wavelength excitation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "327", "Issue": "", "Page": "128935", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 119997035, "Title": "Process Knowledge Graphs (PKG): Towards unpacking and repacking AI applications", "Abstract": "In the past years, a new generation of systems has emerged, which apply recent advances in generative Artificial Intelligence (AI) in combination with traditional technologies. Specifically, generative AI is being delegated tasks in natural language or vision understanding within complex hybrid architectures that also include databases, procedural code, and interfaces. Process Knowledge Graphs (PKG) have a long-standing tradition within symbolic AI research. On the one hand, PKGs can play an important role in describing complex, hybrid applications, thus opening the way for addressing fundamental challenges such as explaining and documenting such systems (unpacking). On the other hand, by organising complex processes in simpler building blocks, PKGs can potentially increase accuracy and control over such systems (repacking). In this position paper, we discuss opportunities and challenges of PGRs and their potential role towards a more robust and principled design of AI applications.", "Keywords": "Knowledge graphs; Prompt engineering; Data science pipelines; Data pipelines documentation; Data pipelines design", "DOI": "10.1016/j.websem.2024.100846", "PubYear": 2025, "Volume": "84", "Issue": "", "JournalId": 7442, "JournalTitle": "Journal of Web Semantics", "ISSN": "1570-8268", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Knowledge Media Institute, The Open University, Walton Hall, Milton Keynes, MK7 6AA, United Kingdom;Research Fellow"}], "References": [{"Title": "On the role of knowledge graphs in explainable AI", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "41", "JournalTitle": "Semantic Web"}, {"Title": "The Ethics of AI Ethics: An Evaluation of Guidelines", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "99", "JournalTitle": "Minds and Machines"}, {"Title": "Knowledge Graphs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Knowledge graphs as tools for explainable machine learning: A survey", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "302", "Issue": "", "Page": "103627", "JournalTitle": "Artificial Intelligence"}, {"Title": "Semantic-enabled architecture for auditable privacy-preserving data analysis", "Authors": "<PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "3", "Page": "675", "JournalTitle": "Semantic Web"}, {"Title": "Methods included", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "6", "Page": "54", "JournalTitle": "Communications of the ACM"}, {"Title": "Trustworthy AI: From Principles to Practices", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "9", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Knowledge Graph Construction with a\n Façade \n : A Unified Method to Access Heterogeneous Data Sources on the Web", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "Accountability in artificial intelligence: what it is and how it works", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "39", "Issue": "4", "Page": "1871", "JournalTitle": "AI & SOCIETY"}, {"Title": "Combining Machine Learning and Semantic Web: A Systematic Mapping Study", "Authors": "<PERSON>; <PERSON>; <PERSON>aja<PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "14s", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Data journeys: Explaining AI workflows through abstraction", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "4", "Page": "1057", "JournalTitle": "Semantic Web"}, {"Title": "Revolutionizing personalized medicine with generative AI: a systematic review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "57", "Issue": "5", "Page": "1", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": *********, "Title": "Joint radical embedding and detection for zero-shot Chinese character recognition", "Abstract": "Radical-based Zero-shot Chinese character recognition (ZSCCR) has attracted much attentions in recent years as radicals are common mid-level primitives that can bridge seen and unseen classes. However, while existing radical detection and sequence matching based methods have the difficulty of training a high-performance radical detection model due to the lacking of positional information of radicals in the character images, the attribute embedding based methods directly drop the radical detection procedure and overlooked the rich structural and spatial information of radicals in the visual perspective. In this paper, we proposed a novel joint radical embedding and detection method (JRED) for ZSCCR, which integrates the radical detection into the radical-level attribute embedding to explore and utilize the radicals’ structural and spatial information for ZSCCR. Specifically, we maintain a learnable embedding vector for representing each radical and learn their representations without the positional information of radicals. The learned radical representations are regarded as the radical detectors to obtain the radical-level attributes by sliding the detector on the visual feature maps. The attributes are then used for the character representation by attribute embedding. Comprehensive experiments on various datasets show that the proposed JRED method can achieve the state-of-the-art performance, demonstrating the effectiveness of our proposed approach.", "Keywords": "", "DOI": "10.1016/j.patcog.2024.111286", "PubYear": 2025, "Volume": "161", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Pattern Recognition and Image Understanding, School of Computer and Information Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Pattern Recognition and Image Understanding, School of Computer and Information Engineering, Xiamen University of Technology, Xiamen, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The State Key Laboratory of Multimodal Artificial Intelligence Systems, Institute of Automation of Chinese Academy of Sciences, Beijing, China;School of Artificial Intelligence, University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Pattern Recognition and Image Understanding, School of Computer and Information Engineering, Xiamen University of Technology, Xiamen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Fujian Key Laboratory of Pattern Recognition and Image Understanding, School of Computer and Information Engineering, Xiamen University of Technology, Xiamen, China"}], "References": [{"Title": "Radical analysis network for learning hierarchies of Chinese characters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "103", "Issue": "", "Page": "107305", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep Matching Network for Handwritten Chinese Character Recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107471", "JournalTitle": "Pattern Recognition"}, {"Title": "Zero-shot Handwritten Chinese Character Recognition with hierarchical decomposition embedding", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "107488", "JournalTitle": "Pattern Recognition"}, {"Title": "Hippocampus-heuristic character recognition network for zero-shot learning in Chinese character recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "108818", "JournalTitle": "Pattern Recognition"}, {"Title": "Cross-modal prototype learning for zero-shot handwritten character recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "131", "Issue": "", "Page": "108859", "JournalTitle": "Pattern Recognition"}, {"Title": "Self-information of radicals: A new clue for zero-shot Chinese character recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "140", "Issue": "", "Page": "109598", "JournalTitle": "Pattern Recognition"}, {"Title": "SideNet: Learning representations from interactive side information for zero-shot Chinese character recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "148", "Issue": "", "Page": "110208", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 119997076, "Title": "High-performance hydrogen sulfide gas sensor based on ZnFe2O4/MoO2 in chip", "Abstract": "Hydrogen sulfide (H<sub>2</sub>S) is a highly toxic gas commonly encountered in industrial settings, posing substantial health risks to workers. Therefore, the development of sensors with optimized operating temperatures, rapid detection speeds, and broad measurement ranges is of paramount engineering significance. In this study, ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> nanocomposites were synthesized using a hydrothermal method, and, for the first time, MEMS-based gas sensors utilizing ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> nanocomposite films were employed for hydrogen sulfide detection. The structural composition and morphology of the ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> (2:1) samples were characterized through X-ray diffraction (XRD), scanning electron microscopy (SEM), transmission electron microscopy (TEM), and X-ray photoelectron spectroscopy (XPS). The gas sensitivity of ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> toward H₂S was systematically investigated. Gas sensitivity tests demonstrated that the ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> composite sensor exhibited a response value of 19.177 at 5 ppm hydrogen sulfide, which is 5.2 times greater than that of ZnFe<sub>2</sub>O<sub>4</sub>, the current dominant material for H₂S detection. Furthermore, the ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> composite sensor demonstrated a detection limit as low as 1 ppm and exhibited significantly faster response and recovery times (13 s and 11 s, respectively, for 5 ppm H₂S). The ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> (2:1) nanocomposite sensor exhibited excellent stability, sensitivity, and remarkable selectivity. This enhanced gas-sensing performance is attributed to the formation of n-n heterojunctions between ZnFe<sub>2</sub>O<sub>4</sub> and MoO<sub>2</sub>, as well as the optimized nanomorphology of the composite. Therefore, the ZnFe<sub>2</sub>O<sub>4</sub>/MoO<sub>2</sub> sensor presents a promising solution for hydrogen sulfide detection in industrial applications.", "Keywords": "", "DOI": "10.1016/j.sna.2024.116120", "PubYear": 2025, "Volume": "382", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "Rongcai Jia", "Affiliation": "College of Information and Control Engineering, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Information and Control Engineering, China University of Petroleum (East China), Qingdao 266580, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Information and Control Engineering, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Information and Control Engineering, China University of Petroleum (East China), Qingdao 266580, China"}, {"AuthorId": 5, "Name": "<PERSON>z<PERSON>", "Affiliation": "College of Information and Control Engineering, China University of Petroleum (East China), Qingdao 266580, China"}], "References": [{"Title": "Synthesis of the ZnFe2O4/ZnSnO3 nanocomposite and enhanced gas sensing performance to acetone", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "346", "Issue": "", "Page": "130524", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Room temperature detection of low-concentration H2S based on CuO functionalized ZnFe2O4 porous spheres", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "368", "Issue": "", "Page": "132100", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 119997079, "Title": "Design of Triband Circularly Polarized Hexagon Shaped Patch Antenna using Optimized Siamese Heterogeneous Convolutional Neural Networks for 5G Wireless Communication System", "Abstract": "The advent of 5G wireless communication systems necessitates the development of advanced antenna designs that offer superior performance across multiple frequency bands. Traditional patch antenna design methods, involving iterative simulations, are time-consuming and often insufficient in fully exploring the vast design space and provide less efficiency. To overcome these issues, this work proposes a novel approach for designing a triband circularly polarized hexagon-shaped patch antenna optimized for 5G applications using an Optimized Siamese Heterogeneous Convolutional Neural Network (SHCNN) coupled with a Circle-Inspired Optimization Algorithm (CIOA). Initially, the triband circularly polarized hexagon-shaped patch antenna is designed. The proposed approach leverages SHCNN to learn the relationship between antenna geometry and performance characteristics, utilizing two identical subnetworks with heterogeneous convolutional layers for efficient feature extraction from varied hexagonal antenna geometries. The CIOA, inspired by the properties of circles such as uniformity and symmetry, refines the antenna design suggested by the SHCNN to achieve optimal triband CP performance. This methodology significantly reduces design time by suggesting promising geometries, explores a vast design space for potential novel configurations, and ensures efficient optimization for optimal performance within the desired frequency bands. Applications include compact, high-performance antennas for 5G base stations and user equipment, enhancing multi-band signal transmission and reception. The introduced antenna design is compiled using MATLAB and HFSS platforms. The simulation results of the proposed antenna, employing SHCNN CIOA methods and operating across three frequency bands (triband) such as low (600 MHz - 1 GHz), mid (2.5GHz - 3.7 GHz), and high (24 GHz - 28 GHz), achieve a gain of 8–10 dB, a return loss of less than -20 dB, higher efficiency at 98 %, and a lower VSWR of 1.5 compared with existing designs.", "Keywords": "", "DOI": "10.1016/j.nancom.2024.100562", "PubYear": 2025, "Volume": "43", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [{"AuthorId": 1, "Name": "Venkat S", "Affiliation": "Assistant Professor, Department of Electronics and Communication Engineering, S. A. Engineering College, Poonamallee-Avadi Main Road, Thiruverkadu, Chennai-77, Tamil Nadu, India;Corresponding author"}, {"AuthorId": 2, "Name": "Tapas Bapu B R", "Affiliation": "Professor, Department of Electronics and Communication Engineering, S. A. Engineering College, Poonamallee-Avadi Main Road, Thiruverkadu, Chennai-77, Tamil Nadu, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Professor, Department of Electronics and Communication Engineering, S. A. Engineering College, Poonamallee-Avadi Main Road, Thiruverkadu, Chennai-77, Tamil Nadu, India"}, {"AuthorId": 4, "Name": "Aruna V V", "Affiliation": "PG Student, ME (Communication Systems), S. A. Engineering College, Poonamallee-Avadi Main Road, Thiruverkadu, Chennai-77, Tamil Nadu, India"}], "References": [{"Title": "Computational intelligence paradigms for UWB antennas: a comprehensive review of analysis, synthesis and optimization", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; Fazal A<PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "1", "Page": "655", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 119997085, "Title": "An online learning framework for UAV search mission in adversarial environments", "Abstract": "The rapid evolution of Unmanned Aerial Vehicles (UAVs) has revolutionized target search operations in various fields, including military applications, search and rescue missions, and post-disaster management. This paper presents the application of a multi-armed bandit algorithm for UAV search mission. The UAV’s mission is to locate a mobile target formation, operating under the assumption of an unknown and potentially non-stationary probability distribution, by learning the formation’s strategy over time. To achieve this, we formulate an optimization problem and leverage the Exp3 algorithm (exponential-weighted exploration and exploitation) for its solution. To enhance the learning process, we integrate environment observations as context, resulting in a variant referred to as C-Exp3. However, C-Exp3 is not designed for scenarios where the target formation strategy changes over time. Therefore, AC-Exp3 is proposed as an adaptive solution, featuring a human-centric drift detection mechanism to detect the changes in the formation strategy and adjust the learning process accordingly. Furthermore, the Exp4 algorithm is proposed as a self-adjustment meta-learner to address changes in the formation’s strategy. We evaluate the performance of C-Exp3, AC-Exp3, and Exp4 through a series of experiments with a focus on non-stationary environments. Our primary objective is reaching the unknown optimal-in-hindsight policy as the time t approaches the horizon T , thereby reflecting the UAV’s capacity to learn formation’s strategy. AC-Exp3 demonstrates enhanced adaptability compared to C-Exp3. Meanwhile, Exp4 emerges as a robust performer, swiftly adapting to new strategies.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126136", "PubYear": 2025, "Volume": "267", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Qatar University, Qatar;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electrical Engineering, Mathematics, and Computer Science, TU Delft, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Arts and Sciences, Qatar University, Qatar"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering, Qatar University, Qatar"}], "References": [{"Title": "UAV search-and-rescue planning using an adaptive memetic algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "11", "Page": "1477", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "Helicopter–UAVs search and rescue task allocation considering UAVs operating environment and performance", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "167", "Issue": "", "Page": "107994", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Human-in-the-loop machine learning: a state of the art", "Authors": "<PERSON>-<PERSON>; <PERSON>-<PERSON>; <PERSON>-<PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "4", "Page": "3005", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A novel UAV path planning approach: Heuristic crossing search and rescue optimization algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "215", "Issue": "", "Page": "119243", "JournalTitle": "Expert Systems with Applications"}, {"Title": "AI-based UAV navigation framework with digital twin technology for mobile target visitation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106318", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "AI-based UAV navigation framework with digital twin technology for mobile target visitation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106318", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Reinforcement learning framework for UAV-based target localization applications", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "23", "Issue": "", "Page": "100867", "JournalTitle": "Internet of Things"}, {"Title": "Drone Swarm Coordination Using Reinforcement Learning for Efficient Wildfires Fighting", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "5", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}]}, {"ArticleId": 119997127, "Title": "Graph-based multi-label feature selection with dynamic graph constraints and latent representation learning", "Abstract": "<p>Currently, multi-label feature selection with joint manifold learning and linear mapping has received much attention. However, the low-quality graph matrix used by existing methods leads to model limitations. Traditional linear mapping cannot learn the coupling relationship between different outputs. In addition, existing approaches ignore latent supervisory information in label correlation. To this end, we obtain a dynamic graph matrix with Laplace rank constraints by the (L_{1}) norm with a conventional graph matrix. We also mine more reliable supervised information from label correlations by introducing latent representation learning. Moreover, we integrate all the above terms into a linear mapping learning framework based on improved matrix decomposition, and design a simple and effective scheme based on alternating iterations to optimize this framework. Numerous experimental results validate the competitive advantage of the proposed method over existing state-of-the-art methods.</p>", "Keywords": "Multi-label learning; Feature selection; Latent representation learning; Dynamic graph; Manifold learning", "DOI": "10.1007/s10489-024-06116-3", "PubYear": 2025, "Volume": "55", "Issue": "3", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Jian<PERSON> Bai", "Affiliation": "Department of Mathematics, Tianjin Renai College, Tianjin, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Basic Education Department, Shandong Huayu University of Technology, Dezhou, China"}], "References": [{"Title": "Robust multi-label feature selection with dual-graph regularization", "Authors": "Jun<PERSON> Hu; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106126", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "MFS-MCDM: Multi-label feature selection using multi-criteria decision making", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2020, "Volume": "206", "Issue": "", "Page": "106365", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-label feature selection via manifold regularization and dependence maximization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "120", "Issue": "", "Page": "108149", "JournalTitle": "Pattern Recognition"}, {"Title": "Dynamic subspace dual-graph regularized multi-label feature selection", "Authors": "<PERSON><PERSON> Hu; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "467", "Issue": "", "Page": "184", "JournalTitle": "Neurocomputing"}, {"Title": "Dynamic network embedding survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "472", "Issue": "", "Page": "212", "JournalTitle": "Neurocomputing"}, {"Title": "Non-negative multi-label feature selection with dynamic graph constraints", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "238", "Issue": "", "Page": "107924", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Multi-label feature selection based on logistic regression and manifold learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "8", "Page": "9256", "JournalTitle": "Applied Intelligence"}, {"Title": "Label correlations variation for robust multi-label feature selection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "609", "Issue": "", "Page": "1075", "JournalTitle": "Information Sciences"}, {"Title": "Multi-label feature selection via robust flexible sparse regularization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109074", "JournalTitle": "Pattern Recognition"}, {"Title": "Sparse multi-label feature selection via dynamic graph manifold regularization", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "3", "Page": "1021", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Robust sparse and low-redundancy multi-label feature selection with dynamic local and global structure preservation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109120", "JournalTitle": "Pattern Recognition"}, {"Title": "Gradient-based multi-label feature selection considering three-way variable interaction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "145", "Issue": "", "Page": "109900", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-label feature selection via latent representation learning and dynamic graph constraints", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "151", "Issue": "", "Page": "110411", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 119997147, "Title": "DCAFusion: A novel general image fusion framework based on reference image reconstruction and dual-cross attention mechanism", "Abstract": "In this study, a novel end-to-end image fusion method, DCAFusion, is proposed. The method is based on Swin Transformer and introduces a dual cross-attention mechanism for the task of fusing infrared and visible, multi-focus and medical images. Although infrared and visible datasets contain source image pairs, they lack corresponding labels and cannot be trained by a unified supervised learning framework. To address this problem, DCAFusion designs image reconstruction blocks that generate reconstructed images as labels to guide model feature learning and provide dynamic information retention. The reconstructed images enable the full reference loss function to intervene in a supervised learning manner and participate in the computation of cross-attention scores through information mapping to more efficiently integrate complementary information between source images. In the comparison experiments of infrared and visible image fusion, DCAFusion's fusion result reaches 13.9420 and 1.0895 in the two metrics, ahead of the second-ranked 13.2547 and 0.9983, respectively. These metrics also maintain the lead in comparison experiments of other fusion tasks, which proves DCAFusion's unique advantages in fusion results.", "Keywords": "Image fusion; <PERSON><PERSON> transformer; Infrared and visible images", "DOI": "10.1016/j.ins.2024.121772", "PubYear": 2025, "Volume": "698", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Fang", "Affiliation": "Department of Marine Technology, Ocean University of China, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marine Technology, Ocean University of China, Qingdao, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Laoshan Laboratory, Qingdao, China;College of Computer Science and Technology, Qingdao University, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marine Technology, Ocean University of China, Qingdao, China;Laoshan Laboratory, Qingdao, China;Corresponding authors at: Department of Marine Technology, Ocean University of China, Qingdao, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Marine Technology, Ocean University of China, Qingdao, China;Laoshan Laboratory, Qingdao, China;Corresponding authors at: Department of Marine Technology, Ocean University of China, Qingdao, China"}], "References": [{"Title": "IFCNN: A general image fusion framework based on convolutional neural network", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "99", "JournalTitle": "Information Fusion"}, {"Title": "MFF-GAN: An unsupervised generative adversarial network with adaptive and gradient joint constraints for multi-focus image fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "40", "JournalTitle": "Information Fusion"}, {"Title": "Image fusion based on generative adversarial network consistent with perception", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "72", "Issue": "", "Page": "110", "JournalTitle": "Information Fusion"}, {"Title": "RFN-Nest: An end-to-end residual fusion network for infrared and visible images", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "73", "Issue": "", "Page": "72", "JournalTitle": "Information Fusion"}, {"Title": "DSAGAN: A generative adversarial network based on dual-stream attention mechanism for anatomical and functional image fusion", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "484", "JournalTitle": "Information Sciences"}, {"Title": "SDNet: A Versatile Squeeze-and-Decomposition Network for Real-Time Image Fusion", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "10", "Page": "2761", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "MMFGAN: A novel multimodal brain medical image fusion based on the improvement of generative adversarial network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "4", "Page": "5889", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "PIAFusion: A progressive infrared and visible image fusion network based on illumination aware", "Authors": "<PERSON><PERSON> Tang; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "83-84", "Issue": "", "Page": "79", "JournalTitle": "Information Fusion"}, {"Title": "Current advances and future perspectives of image fusion: A comprehensive review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> Li", "PubYear": 2023, "Volume": "90", "Issue": "", "Page": "185", "JournalTitle": "Information Fusion"}, {"Title": "MUFusion: A general unsupervised image fusion network based on memory unit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "92", "Issue": "", "Page": "80", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 119997164, "Title": "Mining converging patterns over streaming trajectories of moving objects in road networks", "Abstract": "A converging pattern represents the process in which a collection of moving objects gradually converges toward a target area from various directions and eventually forms a dense group. Unlike most existing group patterns, it indicates the early formation of group events, which has a significant application for predicting and detecting emergency events. Existing studies of the converging pattern merely discover patterns from historical trajectories in an offline manner. However, online mining over streaming trajectories has a more practical impact in some real-world scenarios like real-time traffic monitoring. In this paper, we investigate online algorithms that enable converging pattern mining over network-constrained streaming trajectories of moving objects. To achieve synchronization with the speed of trajectory updates, we propose an incremental density-based clustering algorithm in the road network called I D C R N and a converging monitoring method to detect converging patterns in real-time. To efficiently retrieve the constantly evolving spatial relationship among objects in road networks with a large search space and an intractable computation complexity for network distance, we propose a dual index called M O R N to support continuous neighborhood query and cluster pruning in the road network. Extensive experiments with real and synthetic datasets validate the efficiency of our proposed index and methods.", "Keywords": "", "DOI": "10.1016/j.knosys.2024.112883", "PubYear": 2025, "Volume": "309", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data Science and Engineering, East China Normal University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Electronic Information, Nanjing Normal University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer and Electronic Information, Nanjing Normal University, Nanjing, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Electronic Information, Nanjing Normal University, Nanjing, China"}], "References": []}, {"ArticleId": *********, "Title": "Targeting ATM enhances radiation sensitivity of colorectal cancer by Potentiating radiation-induced cell death and antitumor immunity", "Abstract": "<p><b>INTRODUCTION</b>:The efficacy of radiotherapy in colorectal cancer (CRC) is often limited by radiation resistance. Ataxia telangiectasia mutated (ATM) is well known for its role in repairing double-strand DNA breaks within the DNA damage response (DDR) pathway. However, whether ATM mediates other mechanisms contributing to radiation resistance remains insufficiently investigated.</p><p><b>OBJECTIVES</b>:This study investigates how targeting ATM enhances CRC radiation sensitivity and evaluates combination strategies to improve radiotherapy outcomes.</p><p><b>METHODS</b>:Clinical specimens were analyzed to correlate ATM activation with radiotherapy response. Functional assays, including EdU, cell viability, clonogenic survival, and apoptosis assays, were used to assess the impact of ATM inhibition on radiation sensitivity. Mechanistic insights were gained through RNA-seq, RT-qPCR, western blotting, ELISA, immunofluorescence, flow cytometry, ChIP-qPCR, and co-immunoprecipitation. In vivo efficacy was evaluated using subcutaneous tumor models in nude, BALB/c, and C57BL/6J mice.</p><p><b>RESULTS</b>:High ATM phosphorylation levels correlated with poor radiotherapy response in CRC patients. ATM inhibition enhanced radiation sensitivity in both in vitro and in vivo models. Mechanistically, ATM inhibition increased radiation-induced ROS accumulation and mitochondrial damage, leading to the release of mitochondrial DNA (mtDNA) into the cytosol and activation of the STING-type I interferon pathway. This enhanced CD8+ T cell infiltration and boosted antitumor immunity. Additionally, ATM inhibition partially alleviated the radiation-induced upregulation of PD-L1, likely through the ATM/NEMO/NF-κB pathway. Notably, triple therapy combining radiotherapy, an ATM inhibitor, and anti-PD-L1 achieved superior tumor control and remission in mouse models, including large, treatment-resistant tumors.</p><p><b>CONCLUSION</b>:Targeting ATM enhances radiation-induced tumor cell death and boosts antitumor immune responses, offering a promising strategy to overcome CRC radiation resistance. The synergy of radiotherapy, ATM inhibitior, and immune checkpoint blockade highlights a novel therapeutic approach for CRC management.</p><p>Copyright © 2024. Published by Elsevier B.V.</p>", "Keywords": "Antitumor Immunity;Combination strategies;Radiotherapy;STING pathway;Targeting ATM Therapy", "DOI": "10.1016/j.jare.2024.12.023", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China; Department of Radiation Oncology, Hainan General Hospital, Hainan Affiliated Hospital of Hainan Medical University, Haikou 570311, Hainan, China."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Anorectal Surgery, Hainan General Hospital, Hainan Affiliated Hospital of Hainan Medical University, Haikou 570311, Hainan, China."}, {"AuthorId": 4, "Name": "Zhenkang Li", "Affiliation": "Department of Surgery, Zhujiang Hospital, Southern Medical University Guangzhou 510280, Guangdong, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of General Surgery & Guangdong Provincial Key Laboratory of Precision Medicine for Gastrointestinal Tumor, Nanfang Hospital, The First School of Clinical Medicine, Southern Medical University, Guangzhou 510515, Guangdong, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China."}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Department of General Surgery & Guangdong Provincial Key Laboratory of Precision Medicine for Gastrointestinal Tumor, Nanfang Hospital, The First School of Clinical Medicine, Southern Medical University, Guangzhou 510515, Guangdong, China"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Department of Radiation Oncology, Nanfang Hospital, Southern Medical University, Guangzhou 510515, Guangdong, China"}], "References": []}, {"ArticleId": *********, "Title": "Simulation and assimilation of the digital human brain", "Abstract": "<p>Here we present the Digital Brain (DB)-a platform for simulating spiking neuronal networks at the large neuron scale of the human brain on the basis of personalized magnetic resonance imaging data and biological constraints. The DB aims to reproduce both the resting state and certain aspects of the action of the human brain. An architecture with up to 86 billion neurons and 14,012 GPUs-including a two-level routing scheme between GPUs to accelerate spike transmission in up to 47.8 trillion neuronal synapses-was implemented as part of the simulations. We show that the DB can reproduce blood-oxygen-level-dependent signals of the resting state of the human brain with a high correlation coefficient, as well as interact with its perceptual input, as demonstrated in a visual task. These results indicate the feasibility of implementing a digital representation of the human brain, which can open the door to a broad range of potential applications.</p><p>© 2024. The Author(s), under exclusive licence to Springer Nature America, Inc.</p>", "Keywords": "", "DOI": "10.1038/s43588-024-00731-3", "PubYear": 2024, "Volume": "4", "Issue": "12", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "the DTB Consortium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China. ;School of Computer Science, Fudan University, Shanghai, China. ;School of Software Technology, Zhejiang University, Hangzhou, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China."}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;State Key Laboratory of Integrated Chips and Systems, Fudan University, Shanghai, China."}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON> Feng", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China.  . ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China.  . ;Shanghai Center for Mathematical Sciences, Fudan University, Shanghai, China.  . ;School of Computer Science, Fudan University, Shanghai, China.  . ;Department of Computer Science, University of Warwick, Coventry, United Kingdom.  . ;Zhangjiang Fudan International Innovation Centre, Shanghai, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 15, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 16, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 17, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China. ;School of Computer Science, Fudan University, Shanghai, China. ;School of Software Technology, Zhejiang University, Hangzhou, China."}, {"AuthorId": 18, "Name": "<PERSON><PERSON><PERSON> Feng", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China.  . ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China.  . ;Shanghai Center for Mathematical Sciences, Fudan University, Shanghai, China.  . ;School of Computer Science, Fudan University, Shanghai, China.  . ;Department of Computer Science, University of Warwick, Coventry, United Kingdom.  . ;Zhangjiang Fudan International Innovation Centre, Shanghai, China"}, {"AuthorId": 19, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 20, "Name": "Mingda Ji", "Affiliation": ""}, {"AuthorId": 21, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 22, "Name": "Chong Li", "Affiliation": ""}, {"AuthorId": 23, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 24, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 25, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 26, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Science and Technology for Brain-Inspired Intelligence, Fudan University, Shanghai, China. ;Key Laboratory of Computational Neuroscience and Brain-Inspired Intelligence (Fudan University), Ministry of Education, Shanghai, China. ;Shanghai Center for Mathematical Sciences, Fudan University, Shanghai, China."}, {"AuthorId": 27, "Name": "Zhihui Lv", "Affiliation": ""}, {"AuthorId": 28, "Name": "Heng<PERSON> Ma", "Affiliation": ""}, {"AuthorId": 29, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 30, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 31, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 32, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 33, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 34, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 35, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 36, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 37, "Name": "Xiangyang Xue", "Affiliation": ""}, {"AuthorId": 38, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 39, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 40, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 41, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 42, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Full-scale scaffold model of the human hippocampus CA1 area", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "3", "Issue": "3", "Page": "264", "JournalTitle": "Nature Computational Science"}]}, {"ArticleId": 119997472, "Title": "A novel biomechanical model for predicting ankle moments and assessing static balance in users of shoulder-support exoskeletons", "Abstract": "Shoulder work-related musculoskeletal disorders (WMSDs) pose significant challenges, leading to substantial economic burdens and negatively impacting worker well-being. Shoulder-support exoskeletons have emerged as a promising solution to prevent shoulder WMSDs by providing ergonomic support to workers. However, the exoskeletons may restrict joint motion and affect the user balance, necessitating a thorough understanding of balance effects during the exoskeleton design and evaluation process. Currently, the absence of a balance prediction model for the shoulder-support exoskeleton users poses challenges in incorporating balance considerations into the early design stages. To fill this gap, this study proposes a novel biomechanical model to assess static balance by predicting ankle moment changes in shoulder-support exoskeleton users. Our model integrates principles from the Stiff Rod Inverted Pendulum model and introduces a novel function to compute the ankle moment resulting from exoskeleton reaction forces. The model's performance was validated by comparing it with experimental data and a widely used commercial benchmark software, the 3D Static Strength Prediction Program (3DSSPP). Follow-up experimental validation demonstrated that the model predicted ankle moments closely aligned with the measured body sway and subjective instability ratings. Strong correlations were observed between the predicted ankle moment and subjective instability ratings. The mean difference between predicted ankle moments of the proposed model and those of the 3DSSPP in the no-exoskeleton condition was less than 1.15%. These findings indicate that the proposed model offers valuable insights to facilitate the design and evaluation of exoskeletons for improved static balance.", "Keywords": "", "DOI": "10.1016/j.ergon.2024.103688", "PubYear": 2025, "Volume": "105", "Issue": "", "JournalId": 19885, "JournalTitle": "International Journal of Industrial Ergonomics", "ISSN": "0169-8141", "EISSN": "1872-8219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Human Factors and Ergonomics Lab, Department of Industrial and Systems Engineering, Korea Advanced Institute of Science and Technology (KAIST), 291 Daehak-ro, Yuseong-gu, Daejeon, 34141, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Human Factors and Ergonomics Lab, Department of Industrial and Systems Engineering, Korea Advanced Institute of Science and Technology (KAIST), 291 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>u, <PERSON><PERSON>eon, 34141, Republic of Korea;Corresponding author"}], "References": [{"Title": "Effects of working posture, lifting load, and standing surface on postural instability during simulated lifting tasks in construction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "12", "Page": "1571", "JournalTitle": "Ergonomics"}, {"Title": "Digital Human Modeling: A Review and Reappraisal of Origins, Present, and Expected Future Methods for Representing Humans Computationally", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "10", "Page": "897", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Three passive arm-support exoskeletons have inconsistent effects on muscle activity, posture, and perceived exertion during diverse simulated pseudo-static overhead nutrunning tasks", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "110", "Issue": "", "Page": "104015", "JournalTitle": "Applied Ergonomics"}, {"Title": "Factors influencing the adoption of passive exoskeletons in the construction industry: Industry perspectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "100", "Issue": "", "Page": "103549", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Assessment of an arm-support exoskeleton on physical demands, task performance, and usability during simulated agricultural tasks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "101", "Issue": "", "Page": "103569", "JournalTitle": "International Journal of Industrial Ergonomics"}, {"Title": "Influence of passive arm-support exoskeleton on static postural balance in load-holding tasks: effects of supportive force, weight and load location", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "68", "Issue": "4", "Page": "588", "JournalTitle": "Ergonomics"}]}, {"ArticleId": 119997480, "Title": "Non-tandem enzymatic modules-encapsulated metal-organic framework cubes with mutual-validated operation for dual-modal competitive immunoassay of antibiotics", "Abstract": "Adaptive fusion of nanoscale metal-organic frameworks (nMOFs) and enzyme-based immunoassay is an insightful combination for inspired establishment of antibiotic assay, typically in a competitive format, in which inherent enzymes in nMOFs can serve as generators for amplifying catalytic signals. Albeit important, there is currently a lack of explorations of enzymatic signal outputs with multiple modalities in typical competitive immunoassays, which may face the false signals and lack cross-validation. Herein, non-tandem bi-enzymatic modules such as urease and horseradish peroxidase were co-encapsulated into nMOF cubes and functionalized with antibodies into entities for dual-modal competitive immunoassay of oxytetracycline (OTC), which can convert the bi-enzymatic catalytic signals into fluorogenic and chromogenic reflection and can gain the mutual validation merits. The fluorescence mode can achieve an OTC detection range from 5 ng/mL to 2000 ng/mL, with a detection limit (LOD) of 2.14 ng/mL; complementally, the absorbance mode can achieve a relatively narrow detection range from 1 ng/mL to 200 ng/mL, but a lower LOD of 0.32 ng/mL. The dual-mode sensing parameters of the competitive immunoassay in responding to OTC analysis differed in terms of the linear range and detection limit, which can be integrated to offer reciprocal authentication and expand the detection diversity. Overall, our proposed method may provide a new idea in dual-modal competitive immunoassay of antibiotics, signifying its promising expansion for rational substitution of enzymatic modules and its potential performance in wide applications.", "Keywords": "", "DOI": "10.1016/j.snb.2024.137160", "PubYear": 2025, "Volume": "427", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Suzhou Institute of Biomedical Engineering and Technology, Chinese Academy of Sciences, Suzhou 215163, China;College of Chemistry, Tianjin Key Laboratory of Biosensing and Molecular Recognition, Nankai University, Tianjin 300071, China"}, {"AuthorId": 2, "Name": "Licheng Yu", "Affiliation": "College of Chemistry, Tianjin Key Laboratory of Biosensing and Molecular Recognition, Nankai University, Tianjin 300071, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Suzhou Institute of Biomedical Engineering and Technology, Chinese Academy of Sciences, Suzhou 215163, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Suzhou Institute of Biomedical Engineering and Technology, Chinese Academy of Sciences, Suzhou 215163, China;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Chemistry, Tianjin Key Laboratory of Biosensing and Molecular Recognition, Nankai University, Tianjin 300071, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Chen", "Affiliation": "College of Chemistry, Tianjin Key Laboratory of Biosensing and Molecular Recognition, Nankai University, Tianjin 300071, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry, Tianjin Key Laboratory of Biosensing and Molecular Recognition, Nankai University, Tianjin 300071, China;Dalian Institute of Chemical Physics, Chinese Academy of Sciences, Dalian 116011, China"}], "References": [{"Title": "A novel immunocolorimetric probe for aflatoxin B1 based on multifunctional metal−organic frameworks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "369", "Issue": "", "Page": "132362", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Dissipative enzymatic catalysis-driven modulation of coordination mode and cation valence in MOFs responsive for dual-modality enzyme assay", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "401", "Issue": "", "Page": "135090", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 119997611, "Title": "Chang-E: A High-Quality Motion Capture Dataset of Chinese Classical Dunhuang Dance", "Abstract": "<p> Derived from the mural drawings in the UNESCO-listed Mogao Caves, Dunhuang dance has unique cultural value but faces challenges of digitization and preservation. In this paper, we introduce the first open comprehensive motion capture dataset of Dunhuang dance, Chang-E , including full-body movements documented across eight categories, totaling 40 minutes of professional dance (preview available at https://cislab.hkust-gz.edu.cn/projects/chang-e/ ). This dataset contains three formats: skeleton data acquired from motion capture, body mesh generated from skeleton using machine learning, and multiview videos recorded on site. The dataset supports various creative applications for Dunhuang dance culture, as demonstrated by an immersive new media exhibition. Through the curation process, we applied motion inbetweening algorithms to concatenate different dance sequences for choreography. Also, these reinterpreted dance sequences are synchronized with music using retiming techniques, augmenting the rhythms and harmony between the music and dance performance. Furthermore, we applied visual effects on the regenerated motion sequences of digital dancers, achieving artistic and appealing visual results echoing Buddhist discourses of meditation and bodily cognition. The Chang-E dataset enables digital preservation and creative reimagination of Dunhuang dance, offering not only high-quality data but also an interdisciplinary collaboration framework for future graphics and cultural heritage research. </p>", "Keywords": "", "DOI": "10.1145/3709000", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 23137, "JournalTitle": "Journal on Computing and Cultural Heritage", "ISSN": "1556-4673", "EISSN": "1556-4711", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 13, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "SMPLR: Deep learning based SMPL reverse for 3D human pose and shape recovery", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "106", "Issue": "", "Page": "107472", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep 3D human pose estimation: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "210", "Issue": "", "Page": "103225", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "ChoreoMaster", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Listen, <PERSON><PERSON>, Action! Audio-Driven Motion Synthesis with Diffusion Models", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 119997640, "Title": "Review of Techniques for Integrating Security in Software Development Lifecycle", "Abstract": "Software-related security aspects are a growing and legitimate concern, especially with 5G data available just at our palms. To conduct research in this field, periodic comparative analysis is needed with the new techniques c... | Find, read and cite all the research you need on Tech Science Press", "Keywords": "", "DOI": "10.32604/cmc.2024.057587", "PubYear": 2025, "Volume": "82", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Estimation of Software Development Effort: A Differential Evolution Approach", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "2643", "JournalTitle": "Procedia Computer Science"}, {"Title": "Agile Software Development: Methodologies and Trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "11", "Page": "246", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "Quality Assessment in Systematic Literature Reviews: A Software Engineering Perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "130", "Issue": "", "Page": "106397", "JournalTitle": "Information and Software Technology"}, {"Title": "Software defect prediction using hybrid model (CBIL) of convolutional neural network (CNN) and bidirectional long short-term memory (Bi-LSTM)", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Successful combination of database search and snowballing for identification of primary studies in systematic literature studies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "147", "Issue": "", "Page": "106908", "JournalTitle": "Information and Software Technology"}, {"Title": "FACTORS AFFECTING THE ADOPTION OF SECURE SOFTWARE PRACTICES IN SMALL AND MEDIUM ENTERPRISES THAT BUILD SOFTWARE IN-HOUSE", "Authors": "<PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "2", "Page": "1", "JournalTitle": "International Journal of Advanced Research in Computer Science"}, {"Title": "Software security with natural language processing and vulnerability scoring using machine learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "4", "Page": "2641", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Advancing cybersecurity: a comprehensive review of AI-driven detection techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2024, "Volume": "11", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Big Data"}, {"Title": "A Comprehensive Review and Assessment of Cybersecurity Vulnerability Detection Methodologies", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>ri <PERSON>drissi", "PubYear": 2024, "Volume": "4", "Issue": "4", "Page": "853", "JournalTitle": "Cybersecurity"}]}, {"ArticleId": 119997647, "Title": "Improve Hospital Management Through Process Mining, Optimization, and Simulation: the CH4I-PM Project", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13218-024-00882-5", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119997717, "Title": "Lossless Data Compression of Wireless Sensor in Bridge Inspection System", "Abstract": "", "Keywords": "", "DOI": "10.18494/SAM5120", "PubYear": 2024, "Volume": "36", "Issue": "12", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Ni", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119997959, "Title": "An interactive multi-task ESG classification method for Chinese financial texts", "Abstract": "<p>In view of the problems existing in the ESG classification task of Chinese financial texts, such as feature loss caused by excessively long texts, this paper proposes an interactive multi-task model AmultiESG for ESG classification of Chinese financial texts. The model divides Chinese financial text ESG classification and financial sentiment dictionary expansion into primary and secondary tasks. First, BiLSTM model is used to learn the original representation of the text. Then, in the secondary task, the attention mechanism and full connection layers are combined with the domain dictionary to realize the extraction of emotional words. In the main task, in order to prevent feature loss due to the excessively long texts, we process the text again and divide it into blocks according to the period. Meanwhile, we learned new feature representation of the text by combining text label representation, text block representation, BiLSTM output features and domain dictionary features. And we introduce an interactive information transfer mechanism to iteratively improve the predicted results of the two tasks and strengthen the association between them. It has been experimentally demonstrated that the proposed method shows superior performance compared to other baselines for the ESG classification task of Chinese financial text, especially for long-text classification tasks.</p>", "Keywords": "ESG classification of Chinese financial text; Long-text classification; Interactive multi-task; Financial sentiment dictionary expansion", "DOI": "10.1007/s10489-024-06068-8", "PubYear": 2025, "Volume": "55", "Issue": "3", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 2, "Name": "Yazhou Zhang", "Affiliation": "Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhengzhou University, Zhengzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Zhengzhou Central Sub-branch, The People’s Bank of China, Zhengzhou, China"}, {"AuthorId": 5, "Name": "Lixia Ji", "Affiliation": "Zhengzhou University, Zhengzhou, China; Corresponding author."}], "References": [{"Title": "Fusion of heterogeneous attention mechanisms in multi-view convolutional neural network for text classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "548", "Issue": "", "Page": "295", "JournalTitle": "Information Sciences"}, {"Title": "Attention-based BiLSTM fused CNN with gating mechanism model for Chinese long text classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "", "Page": "101182", "JournalTitle": "Computer Speech & Language"}, {"Title": "Deep Learning--based Text Classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "54", "Issue": "3", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 119997999, "Title": "Knowledge Error Detection via Textual and Structural Joint Learning", "Abstract": "", "Keywords": "", "DOI": "10.26599/BDMA.2024.9020040", "PubYear": 2025, "Volume": "8", "Issue": "1", "JournalId": 54040, "JournalTitle": "Big Data Mining and Analytics", "ISSN": "", "EISSN": "2096-0654", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Institute of Advanced Technology, Zhengzhou University,Zhengzhou,China,450003"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CAS,Key Laboratory of AI Safety,Beijing,China,100190"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "CAS,Key Laboratory of AI Safety,Beijing,China,100190"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CAS,Key Laboratory of AI Safety,Beijing,China,100190"}, {"AuthorId": 5, "Name": "<PERSON> He", "Affiliation": "Henan Institute of Advanced Technology, Zhengzhou University,Zhengzhou,China,450003"}], "References": []}, {"ArticleId": 119998005, "Title": "IEEE Transactions on Learning Technologies", "Abstract": "", "Keywords": "", "DOI": "10.1109/TLT.2024.3370428", "PubYear": 2024, "Volume": "17", "Issue": "", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 119998125, "Title": "Leveraging Observability-Driven Predictive Analytics for Cost-Effective Hybrid Cloud Migration on AWS", "Abstract": "This article presents an innovative framework that revolutionizes hybrid cloud migration strategies by integrating advanced observability tools with predictive analytics capabilities on AWS infrastructure. The article introduces a comprehensive approach that combines real-time monitoring, machine learning-driven prediction models, and automated risk mitigation strategies to optimize migration success rates and reduce operational costs. Through extensive experimentation across multiple enterprise environments encompassing numerous virtual machines and applications, the framework demonstrated significant improvements in migration outcomes. The implementation of predictive monitoring resulted in a substantial reduction in unplanned downtime and high accuracy in failure prediction, while the cost optimization model achieved a meaningful reduction in overall infrastructure expenses. The article establishes a new paradigm for cloud migration by emphasizing proactive risk management and resource optimization through advanced observability techniques. This article provides valuable insights for organizations planning hybrid cloud migrations and establishes a foundation for future research in quantum-inspired optimization and cross-platform compatibility. The framework's success in addressing common migration challenges while maintaining scalability and adaptability makes it a significant contribution to the field of cloud computing and infrastructure management.", "Keywords": "Predictive Cloud Observability;Hybrid Migration Analytics;AWS Infrastructure Optimization;Machine Learning Risk Mitigation;Cloud Migration Automation", "DOI": "10.32628/CSEIT241061216", "PubYear": 2024, "Volume": "10", "Issue": "6", "JournalId": 59992, "JournalTitle": "International Journal of Scientific Research in Computer Science, Engineering and Information Technology", "ISSN": "", "EISSN": "2456-3307", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "J P Morgan & Chase, USA"}], "References": []}, {"ArticleId": 119998212, "Title": "Domain adaptation to enhance (2 + 1)D CNN dynamic analysis of cell collective behavior in time-lapse microscopy videos", "Abstract": "<p>In recent years, 2D CNNs have excelled in analyzing single-frame video sequences, prompting the evolution of standard architectures toward full 3D CNNs. This transition, while enhancing the modeling of spatial and temporal information in video activities, demanded substantial data for effective training. To alleviate this challenge, we introduced a switched multitask training strategy. This approach involves factorizing 3D layers into (2 + 1)D convolutions, training only 2D (1D) layers for spatial (temporal) tasks and switching off the training of the 1D (2D) layers. Additionally, we addressed data scarcity by generating synthetic stylized video sequences. These were crafted using stochastic particle models of collective cell motions, further modified through neural style transfer to mimic real video data. Such a domain adaptation strategy facilitated the creation of training data impractical to obtain in the real world. Transferring knowledge from the switched (2 + 1) CNN to real video data, we encoded wound healing experiments of three distinct cell lines—human melanocytes cells M14, mouse neuroblastoma cells N1, and human prostate cells PC3—into deep features. Employing a novel feature selection strategy based on robustness to disturbances, we discriminated the three wound healing processes. Average classification accuracy of 92.91% (0.11%), 91.50% (0.37%), and 88.81% (0.29%) was obtained for the original real videos, the real videos with progressive altered levels of focus, and levels of brightness, respectively. The proposed approach proved to be a powerful tool for analyzing the spatiotemporal dynamics of biological systems, even in the presence of fluctuations.</p>", "Keywords": "Domain adaptation; 3D CNN; Synthetic stylized video sequences; Robust feature selection; Time-lapse video microscopy", "DOI": "10.1007/s00521-024-10767-1", "PubYear": 2025, "Volume": "37", "Issue": "6", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy; Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy; Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy; Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy; Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Biology, University of Rome Tor Vergata, Rome, Italy; Department of Chemical Science and Technologies, University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy; Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy; Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biology, University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, University of Rome Tor Vergata, Rome, Italy; Interdisciplinary Center of Advanced Study of Organ-on-Chip and Lab-on-Chip Applications (IC-LOC), University of Rome Tor Vergata, Rome, Italy"}], "References": [{"Title": "A comprehensive survey on support vector machine classification: Applications, challenges and trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "189", "JournalTitle": "Neurocomputing"}, {"Title": "Multi-scale generative adversarial network for improved evaluation of cell–cell interactions observed in organ-on-chip experiments", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "8", "Page": "3671", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Spatiotemporal multi-graph convolutional networks with synthetic data for traffic volume forecasting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "115992", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Structure optimization of prior-knowledge-guided neural networks", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "491", "Issue": "", "Page": "464", "JournalTitle": "Neurocomputing"}, {"Title": "Machine learning microfluidic based platform: Integration of Lab-on-Chip devices and data analysis algorithms for red blood cell plasticity evaluation in Pyruvate Kinase Disease monitoring", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "351", "Issue": "", "Page": "114187", "JournalTitle": "Sensors and Actuators A: Physical"}]}, {"ArticleId": 119998341, "Title": "YOLOv10-pose and YOLOv9-pose: Real-time strawberry stalk pose detection models", "Abstract": "In the computer-aided industry, particularly within the domain of agricultural automation, fruit pose detection is critical for optimizing efficiency across various applications such as robotic harvesting, aerial crop surveillance, precision pruning, and automated sorting. These technologies enhance productivity and precision, addressing challenges posed by an aging labor force and the increasing demand for sophisticated robotic applications in agriculture. This is particularly crucial for strawberries, which are globally recognized for their high nutritional value. The strawberry pickting robots generally cut the stems, so knowing the pose of the strawberry stalks before cutting can effectively adjust the pose of the end effector, thereby improving the success rate of picking. This paper referred to the keypoint detection branch and loss function of the YOLOv8-pose model, and combined the latest YOLOv9 and YOLOv10 object detection models to propose YOLOv9-pose and YOLOv10-pose. The experimental results showed that YOLOv9-base-pose had the best comprehensive performance, reaching 0.962 in Box_mAP50 and 0.914 in Pose_mAP50, and the speed met the real-time requirement of FPS 51. The entire YOLOv10-pose series did not achieve satisfactory accuracy, but not using non-maximum suppression did indeed speed up the post-processing. In the YOLOv10-pose series, YOLOv10m-pose achieved the best comprehensive performance with Box_mAP50 of 0.954, Pose_ mAP50 of 0.903, and a speed of 61 FPS. Comparing YOLOv9-base-pose with the entire series of YOLOv8-pose and YOLOv5-pose also demonstrated the superior performance of YOLOv9-base-pose. YOLOv9-pose and YOLOv10-pose can provide a theoretical basis for pose detection and a reference for other similar fruit pose detection.", "Keywords": "", "DOI": "10.1016/j.compind.2024.104231", "PubYear": 2025, "Volume": "165", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Zhejiang Sci-Tech University, Hangzhou 310018, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Zhejiang Sci-Tech University, Hangzhou 310018, China;Zhejiang Key Laboratory of Intelligent Sensing and Robotics for Agriculture, Hangzhou 310018, China;Key Laboratory of Agricultural Equipment for Hilly and Mountainous Areas in Southeastern China (Co-construction by Ministry and Province), Ministry of Agriculture and Rural Affairs, Hangzhou 310018, China;Corresponding author at: School of Mechanical Engineering, Zhejiang Sci-Tech University, Hangzhou 310018, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Precision & Automated Agricultural Systems, Washington State University, 24106 N Bunn Rd, Prosser, WA 99350, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Zhejiang Sci-Tech University, Hangzhou 310018, China;Zhejiang Key Laboratory of Intelligent Sensing and Robotics for Agriculture, Hangzhou 310018, China;Key Laboratory of Agricultural Equipment for Hilly and Mountainous Areas in Southeastern China (Co-construction by Ministry and Province), Ministry of Agriculture and Rural Affairs, Hangzhou 310018, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Zhejiang Sci-Tech University, Hangzhou 310018, China"}], "References": [{"Title": "An autonomous strawberry‐harvesting robot: Design, development, integration, and field evaluation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "2", "Page": "202", "JournalTitle": "Journal of Field Robotics"}, {"Title": "A detection algorithm for cherry fruits based on the improved YOLO-v4 model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "19", "Page": "13895", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Fruit Detection and Pose Estimation for Grape Cluster–Harvesting Robot Using Binocular Imagery Based on Deep Neural Networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "163", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Segmentation and recognition of filed sweet pepper based on improved self-attention convolutional neural networks", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "1", "Page": "223", "JournalTitle": "Multimedia Systems"}, {"Title": "Fruit detection and positioning technology for a Camellia oleifera C. Abel orchard based on improved YOLOv4-tiny model and binocular stereo vision", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118573", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Data augmentation: A comprehensive survey of modern approaches", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "", "Page": "100258", "JournalTitle": "Array"}, {"Title": "Mobile robotics platform for strawberry sensing and harvesting within precision indoor farming systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "41", "Issue": "7", "Page": "2047", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Statistical Analysis of Design Aspects of Various YOLO-Based Deep Learning Models for Object Detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "PG-YOLO: An efficient detection algorithm for pomegranate before fruit thinning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "134", "Issue": "", "Page": "108700", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 119998349, "Title": "Improper Gaussian signaling for low-resolution active RIS-assisted rate-splitting multiple access multiuser MIMO system", "Abstract": "This paper investigates a low-resolution active reconfigurable intelligent surface (aRIS)-assisted rate-splitting multiple access (RSMA) multiple-input multiple-output (MIMO) downlink system with improper Gaussian signaling (IGS). A max–min achievable log determinant (log-det) rate optimization problem is formulated by jointly optimizing the transmit beamforming, low-resolution aRIS’s power-amplified reconfigurable elements (aPREs), and rate-splitting (RS) vector. To address the nonsmooth optimization objective function, the formulated problem is decoupled into two subproblems, namely, transmit beamforming and RS vector optimization, and aPREs’ vector and RS vector optimization. For the large-scale mixed discrete-continuous problem imposed by the aPREs’ vector optimization, we introduce a novel penalized optimization framework that employs a cubic complexity quadratic solver for alternately optimizing the RS vector, transmit beamforming, and the aPREs’ vector. Simulation results show that the consideblack RSMA system outperforms the space-division multiple access (SDMA) scheme with respect to rate fairness, and the use of IGS is remarkably superior to the traditional proper Gaussian signaling (PGS) in improving the rate fairness among users.", "Keywords": "", "DOI": "10.1016/j.phycom.2024.102580", "PubYear": 2025, "Volume": "69", "Issue": "", "JournalId": 3585, "JournalTitle": "Physical Communication", "ISSN": "1874-4907", "EISSN": "1876-3219", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Institute for Advanced Communication and Data Science, Key Laboratory of Specialty Fiber Optics and Optical Access Networks, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Institute for Advanced Communication and Data Science, Key Laboratory of Specialty Fiber Optics and Optical Access Networks, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical Engineering and Center for Communication Systems and Sensing, King <PERSON> University of Petroleum and Minerals, Dhahran 31261, Saudi Arabia;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Institute for Advanced Communication and Data Science, Key Laboratory of Specialty Fiber Optics and Optical Access Networks, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Electrical Engineering and Computer Science, National University of Sciences and Technology, Islamabad 44000, Pakistan"}], "References": []}, {"ArticleId": 119998350, "Title": "BananaImageBD: A Comprehensive Banana Image Dataset for Classification of Banana Varieties and Detection of Ripeness Stages in Bangladesh.", "Abstract": "Bananas are among the most widely consumed fruits globally due to their appealing flavor, high nutritional value, and ease of digestion. In Bangladesh, bananas hold significant agricultural importance, being one of the most extensively cultivated fruits in terms of land coverage and ranking third in production volume. The banana image dataset presented in this article includes clear and detailed images of four common banana varieties in Bangladesh: <PERSON><PERSON> ( Musa acuminate ), <PERSON><PERSON><PERSON> ( Musa sapientum ), <PERSON><PERSON> ( Musa sp.), and <PERSON><PERSON><PERSON> ( Musa sapientum ), as well as four key stages of banana ripeness: Green, Semi-ripe, Ripe, and Overripe. The bananas were collected from wholesale markets and retail fruit shops located in different places in Bangladesh. Overall, the dataset has 2471 original images of different varieties of bananas and 820 original images of varying ripeness stages of bananas. All the images were carefully captured using a high-quality smartphone camera. Later, each image was manually reviewed, maintaining the quality standard throughout the dataset. The augmented version of the banana variety classification dataset contains 7413 images and the augmented banana ripeness stages dataset contains 2457 images. The dataset possesses immense potential in driving innovation and development of automated and efficient processes and mechanisms in several fields, including precision agriculture, food processing, and supply chain management. Machine Learning (ML) and Deep Learning (DL) models can be trained on this dataset to accurately categorize banana varieties and determine their ripeness stages. Such ML and DL models can be leveraged to develop automated systems to determine the optimal harvest time, establish standards for quality control of bananas, develop products and marketing strategies through analysis of consumer preferences for various banana varieties and ripeness levels, and streamline the banana supply chain through improvements in harvesting, sorting, packaging, and inventory management. Additionally, researchers aiming to contribute to developing Computer Vision technologies in food and agricultural sciences will find this dataset valuable in advancing precision farming and food processing mechanisms. Therefore, the dataset has a vast capacity for automating banana production and processing, minimizing the costs of manual labor, and improving overall efficiency.", "Keywords": "Machine learning; Computer vision; Image classification; Object detection; Horticulture; Food processing; Precision agriculture; Deep learning", "DOI": "10.1016/j.dib.2024.111239", "PubYear": 2025, "Volume": "58", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh;Corresponding author"}, {"AuthorId": 2, "Name": "Riz<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural Extension, Ministry of Agriculture, Bogura, Bangladesh"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, East West University, Dhaka, Bangladesh"}], "References": []}, {"ArticleId": 119998639, "Title": "Human vs. Machine: A Comparative Study on the Detection of AI-Generated Content", "Abstract": "<p>The surge in advancements in large language models (LLMs) has expedited the generation of synthetic text imitating human writing styles. This, however, raises concerns about the potential misuse of synthetic textual data, which could compromise trust in online content. Against this backdrop, the present research aims to address the key challenges of detecting LLMs-generated texts. In this study, we used ChatGPT (v 3.5) because of its widespread and capability to comprehend and keep conversational context, allowing it to produce meaningful and contextually suitable responses. The problem revolves around the task of discerning between authentic and artificially generated textual content. To tackle this problem, we first created a dataset containing both real and DeepFake text. Subsequently, we employed transfer-learning (TL) and conducted DeepFake-detection utilizing SOTA large pre-trained LLMs. Furthermore, we conducted validation using benchmark datasets comprising unseen data samples to ensure that the model's performance reflects its ability to generalize to new data. Finally, we discussed this study's theoretical contributions, practical implications, limitations and potential avenues for future research, aiming to formulate strategies for identifying and detecting large-generative-models’ produced texts. The results were promising, with accuracy ranging from 94% to 99%. The comparison between automatic detection and the human ability to detect DeepFake text revealed a significant gap in the human capacity for its identification, emphasizing an increasing need for sophisticated automated detectors. The investigation into AI-generated content detection holds central importance in the age of LLMs and technology convergence. This study is both timely and adds value to the ongoing discussion regarding the challenges associated with the pertinent theme of \"DeepFake text detection\", with a special focus on examining the boundaries of human detection.</p>", "Keywords": "", "DOI": "10.1145/3708889", "PubYear": 2025, "Volume": "24", "Issue": "2", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Université Ferhat Abbas, Setif, Algeria"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Sciences, Universite Fer<PERSON>, Setif, Algeria"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics, British University - Faculty of Engineering and IT, Dubai, United Arab Emirates"}], "References": [{"Title": "Biomedical-domain pre-trained language model for extractive summarization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "199", "Issue": "", "Page": "105964", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Output-based transfer learning in genetic programming for document classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "106597", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Sentiment Analysis Using XLM-R Transformer and Zero-shot Transfer Learning on Resource-poor Indian Language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "How well do pre-trained contextual language representations recommend labels for GitHub issues?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "232", "Issue": "", "Page": "107476", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Arabic Fake News Detection: A Fact Checking Based Deep Learning Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Aspect-level sentiment classification based on attention-BiLSTM model and transfer learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "245", "Issue": "", "Page": "108586", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "FacTeR-Check: Semi-automated fact-checking through semantic similarity and natural language inference", "Authors": "<PERSON>; <PERSON>-<PERSON><PERSON>; <PERSON><PERSON><PERSON>-<PERSON>", "PubYear": 2022, "Volume": "251", "Issue": "", "Page": "109265", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Turkish Data-to-Text Generation Using Sequence-to-Sequence Neural Networks", "Authors": "<PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "The interaction of normalisation and clustering in sub-domain definition for multi-source transfer learning based time series anomaly detection", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "257", "Issue": "", "Page": "109894", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Automated Generation of Human-readable Natural Arabic Text from RDF Data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "DictPrompt: Comprehensive dictionary-integrated prompt tuning for pre-trained language model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "273", "Issue": "", "Page": "110605", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "So2al-wa-Gwab: A New Arabic Question-Answering Dataset Trained on Answer Extraction Models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "22", "Issue": "8", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Sentiment Analysis of Turkish Drug Reviews with Bidirectional Encoder Representations from Transformers", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "Fighting Fire with Fire: Can ChatGPT Detect AI-generated Text?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "25", "Issue": "2", "Page": "14", "JournalTitle": "ACM SIGKDD Explorations Newsletter"}, {"Title": "A Bibliometric Review of Large Language Models Research from 2017 to 2023", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 119999388, "Title": "How do mental models affect cybersecurity awareness? The roles of questioning styles, need for cognition, and graphical representations", "Abstract": "This study, grounded in psychological model theory, investigated the influence of psychological models on cybersecurity awareness. To achieve this, two online experiments were conducted with college students. Experiment 1 examined the impact of various questioning methods on cybersecurity awareness within different problem situations among 479 college students. Experiment 2 explored the interplay of cognitive needs and graphic representations in shaping cybersecurity awareness among 468 college students. Our findings revealed that both problem situations and questioning methods significantly affect cybersecurity awareness. Notably, in criminal scenarios, a four-step questioning approach demonstrated the most pronounced positive impact on cybersecurity awareness. Additionally, an interaction effect was observed between cognitive needs and graphic representations on cybersecurity awareness. Specifically, graphic representations were more effective in promoting cybersecurity awareness among individuals with high cognitive needs. These results underscore the importance of questioning methods and cognitive needs in mediating the impact of psychological models on cybersecurity awareness, while also highlighting the conditional influence of graphic representations.", "Keywords": "", "DOI": "10.1016/j.cose.2024.104292", "PubYear": 2025, "Volume": "150", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Psychology, Changsha Normal University, Changsha, China;Corresponding author at: Department of Applied Psychology, Changsha Normal University, No.9 Wanhuayuan Road, Ansha, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Psychology, Changsha Normal University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Applied Psychology, Changsha Normal University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Psychology, Changsha Normal University, Changsha, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Applied Psychology, Changsha Normal University, Changsha, China"}, {"AuthorId": 6, "Name": "Yuanyuan Long", "Affiliation": "Department of Applied Psychology, Changsha Normal University, Changsha, China"}], "References": [{"Title": "Efficiency improvement of genetic network programming by tasks decomposition in different types of environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "2", "Page": "229", "JournalTitle": "Genetic Programming and Evolvable Machines"}, {"Title": "Advancing the concept of cybersecurity as a public good", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "116", "Issue": "", "Page": "102493", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "The role of national cybersecurity strategies on the improvement of cybersecurity education", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "119", "Issue": "", "Page": "102754", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 119999421, "Title": "Blockchain-empowered multi-skilled crowdsourcing for mobile web 3.0", "Abstract": "As the next generation of the world wide web, web 3.0 is envisioned as a decentralized internet which improves data security and self-sovereign identity. The mobile web 3.0 mainly focuses on decentralized internet for mobile users and mobile applications. With the rapid development of mobile crowdsourcing research, existing mobile crowdsourcing models can achieve efficient allocation of tasks and responders. Benefiting from the inherent decentralization and immutability, more and more crowdsourcing models over mobile web 3.0 have been deployed on blockchain systems to enhance data verifiability. However, executing these crowdsourcing-oriented smart contracts on a blockchain may incur a large amount of gas consumption, leading to significant costs for the system and increasing users’ expenses. In addition, the existing crowdsourcing model does not take into account the expected quality of task completion in the matching link between tasks and responders, which will cause some tasks to fail to achieve effects and damage the interests of task publishers. In order to solve these problems, this paper proposes a decentralized multi-skill mobile crowdsourcing model with guaranteed task quality and gas optimization (DMCQG), which performs task matching while considering skill coverage and expected quality of task completion, and guarantees the final completion quality of each task. In addition, DMCQG also optimizes the gas value consumed by smart contracts at the code level, reducing the cost of crowdsourcing task participation. In order to verify whether DMCQG is effective, we deployed the model on the Ethereum platform for testing. Through inspection, it was proved that the final expected quality of the tasks matched by DMCQG was better than other models. And it is verified that after optimization, the gas consumption of DMCQG is significantly reduced.", "Keywords": "", "DOI": "10.1016/j.comcom.2024.108037", "PubYear": 2025, "Volume": "232", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computing, Hangzhou Dianzi University, Hangzhou, 310018, Zhejiang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Hangzhou Dianzi University, Hangzhou, 310018, Zhejiang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Hangzhou Dianzi University, Hangzhou, 310018, Zhejiang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information and Electronic Engineering, Zhejiang University of Science and Technology, Hangzhou 310023, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University, Hong Kong, China;The Hong Kong Polytechnic University Shenzhen Research Institute, Shenzhen, 518057, China;Corresponding author"}], "References": [{"Title": "A survey of blockchain technology on security, privacy, and trust in crowdsourcing services", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "393", "JournalTitle": "World Wide Web"}, {"Title": "Empowering mobile crowdsourcing apps with user privacy control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "147", "Issue": "", "Page": "1", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Profiling gas consumption in solidity smart contracts", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "186", "Issue": "", "Page": "111193", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Towards trusted node selection using blockchain for crowdsourced abnormal data detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "133", "Issue": "", "Page": "320", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Blockchain-based mobile crowdsourcing model with task security and task assignment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> Ai; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118526", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Blockchain-based mobile crowdsourcing model with task security and task assignment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> Ai; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "211", "Issue": "", "Page": "118526", "JournalTitle": "Expert Systems with Applications"}, {"Title": "On Monetizing Personal Wearable Devices Data: A Blockchain-based Marketplace for Data Crowdsourcing and Federated Machine Learning in Healthcare", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "2", "Page": "", "JournalTitle": "Artificial Intelligence Advances"}]}, {"ArticleId": 119999586, "Title": "Unified Planning: Modeling, manipulating and solving AI planning problems in Python", "Abstract": "Automated planning is a branch of artificial intelligence aiming at finding a course of action that achieves specified goals, given a description of the initial state of a system and a model of possible actions. There are plenty of planning approaches working under different assumptions and with different features (e.g. classical, temporal, and numeric planning). When automated planning is used in practice, however, the set of required features is often initially unclear. The Unified Planning (UP) library addresses this issue by providing a feature-rich Python API for modeling automated planning problems, which are solved seamlessly by planning engines that specify the set of features they support. Once a problem is modeled, UP can automatically find engines that can solve it, based on the features used in the model. This greatly reduces the commitment to specific planning approaches and bridges the gap between planning technology and its users.", "Keywords": "Automated planning and scheduling; Python library; Interoperability", "DOI": "10.1016/j.softx.2024.102012", "PubYear": 2025, "Volume": "29", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Fondazione Bruno <PERSON>, Trento, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, INSA, Toulouse, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Basel, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Brescia, Italy"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Fondazione Bruno <PERSON>, Trento, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Fondazione Bruno <PERSON>, Trento, Italy"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "University of Brescia, Italy"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Sapienza University of Rome, Italy"}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": "University of Brescia, Italy"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "University of Brescia, Italy"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "Sapienza University of Rome, Italy"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "LAAS-CNRS, Université de Toulouse, CNRS, INSA, Toulouse, France"}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": "Örebro University, Sweden"}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sapienza University of Rome, Italy"}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "University of Brescia, Italy"}, {"AuthorId": 16, "Name": "<PERSON>", "Affiliation": "University of Brescia, Italy"}, {"AuthorId": 17, "Name": "<PERSON>", "Affiliation": "DFKI, Osnabrück, Germany"}], "References": []}, {"ArticleId": 119999623, "Title": "Multi-granular approach to learn user mobility preferences for next Point-of-Interest recommendation", "Abstract": "As a popular research topic in recommendation systems, the next Point of Interest (POI) recommendation aims to discover users’ personalized preferences based on their recent activities and suggest the next suitable location. Existing methods model POI sequences to extract sequential and geographical dependencies and utilize contextual information or graph neural networks to enhance the representation of POI nodes. However, due to data sparsity and noise in POI sequences, these methods often do not consider user mobility, reducing the accuracy of the recommendations. This paper proposes a novel multi-granular mobility preference-aware network (MMPAN) model for the next POI recommendation that assesses mobility preferences based on user trajectories at different granularities. We expand the user’s POI trajectory into a multi-level grid trajectory to model the coarse-grained dynamic mobility patterns of users in different regions. We then utilize the clustering results and access frequency to re-encode the POIs and their categories to reduce the impact of noise in POI trajectories and to learn the user’s fine-grained preferences. The multi-granular mobility preferences are combined with a beam search strategy to recommend the most likely POI to users. Experiments conducted on three datasets, Foursquare-NYC, Foursquare-TKY and Weeplaces-SF, demonstrate that the proposed network outperforms state-of-the-art baseline models.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126189", "PubYear": 2025, "Volume": "267", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Yunnan University, 2 Cuihu North Road, Kunming, 650091, Yunnan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Yunnan University, 2 Cuihu North Road, Kunming, 650091, Yunnan, China"}, {"AuthorId": 3, "Name": "Hai <PERSON>", "Affiliation": "School of Computer Science and Engineering, Yunnan University, 2 Cuihu North Road, Kunming, 650091, Yunnan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Yunnan University, 2 Cuihu North Road, Kunming, 650091, Yunnan, China;Corresponding author"}], "References": [{"Title": "An Attention-Based Spatiotemporal LSTM Network for Next POI Recommendation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "6", "Page": "1585", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "CHA: Categorical Hierarchy-based Attention for Next POI Recommendation", "Authors": "Hongyu Zang; Dongcheng Han; Xin <PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Attentive sequential model based on graph neural network for next poi recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "6", "Page": "2161", "JournalTitle": "World Wide Web"}, {"Title": "Building and exploiting spatial–temporal knowledge graph for next POI recommendation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "258", "Issue": "", "Page": "109951", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Context-and category-aware double self-attention model for next POI recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; Dongjin Yu", "PubYear": 2023, "Volume": "53", "Issue": "15", "Page": "18355", "JournalTitle": "Applied Intelligence"}, {"Title": "GNN-based long and short term preference modeling for next-location prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "629", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "Point-of-interest Recommendation using Deep Semantic Model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "231", "Issue": "", "Page": "120727", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Feature-based POI grouping with transformer for next point of interest recommendation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "147", "Issue": "", "Page": "110754", "JournalTitle": "Applied Soft Computing"}, {"Title": "Modified node2vec and attention based fusion framework for next POI recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "101", "Issue": "", "Page": "101998", "JournalTitle": "Information Fusion"}, {"Title": "Contrastive graph learning long and short-term interests for POI recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "121931", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119999669, "Title": "EdgeFormer: local patch-based edge detection transformer on point clouds", "Abstract": "<p>Edge points on 3D point clouds can clearly convey 3D geometry and surface characteristics, therefore, edge detection is widely used in many vision applications with high industrial and commercial demands. However, the fine-grained edge features are difficult to detect effectively as they are generally densely distributed or exhibit small-scale surface gradients. To address this issue, we present a learning-based edge detection network, named EdgeFormer, which mainly consists of two stages. Based on the observation that spatially neighboring points tend to exhibit high correlation, forming the local underlying surface, we convert the edge detection of the entire point cloud into a point classification based on local patches. Therefore, in the first stage, we construct local patch feature descriptors that describe the local neighborhood around each point. In the second stage, we classify each point by analyzing the local patch feature descriptors generated in the first stage. Due to the conversion of the point cloud into local patches, the proposed method can effectively extract the finer details. The experimental results show that our model demonstrates competitive performance compared to six baselines.</p>", "Keywords": "Edge detection; Point cloud; Local patch; Feature descriptor", "DOI": "10.1007/s10044-024-01386-6", "PubYear": 2025, "Volume": "28", "Issue": "1", "JournalId": 6461, "JournalTitle": "Pattern Analysis and Applications", "ISSN": "1433-7541", "EISSN": "1433-755X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Northwest University, Xi’an, China; Corresponding author."}], "References": [{"Title": "Learning Part Boundaries from 3D Point Clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "5", "Page": "183", "JournalTitle": "Computer Graphics Forum"}, {"Title": "PCT: Point cloud transformer", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "187", "JournalTitle": "Computational Visual Media"}, {"Title": "Self-Sampling for Neural Point Cloud Consolidation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "PCEDNet: A Lightweight Neural Network for Fast and Interactive Edge Detection in 3D Point Clouds", "Authors": "Chems-<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "SGLBP: Subgraph‐based Local Binary Patterns for Feature Extraction on Point Clouds", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "6", "Page": "51", "JournalTitle": "Computer Graphics Forum"}, {"Title": "DEF", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Deep Shape Representation with Sharp Feature Preservation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "157", "Issue": "", "Page": "103468", "JournalTitle": "Computer-Aided Design"}]}, {"ArticleId": 119999727, "Title": "MSDM: multi-space diffusion with dynamic loss weight", "Abstract": "<p>Diffusion models have achieved remarkable results in image generation. However, due to the slow convergence speed, room for enhancement remains in existing loss weight strategies. In one aspect, the predefined loss weight strategy based on signal-to-noise ratio (SNR) transforms the diffusion process into a multi-objective optimization problem. However, it takes a long time to reach the Pareto optimal. In contrast, the unconstrained optimization weight strategy can achieve lower objective values, but the loss weights of each task change unstably, resulting in low training efficiency. In addition, the imbalance of lossy compression and semantic information in latent space diffusion also leads to missing image details. To solve these problems, a new loss weight strategy combining the advantages of predefined and learnable loss weights is proposed, effectively balancing the gradient conflict of multi-objective optimization. A high-dimensional multi-space diffusion method called Multi-Space Diffusion is also introduced, and a loss function that considers both structural information and robustness is designed to achieve a good balance between lossy compression and fidelity. The experimental results indicate that the proposed model and strategy significantly enhance the convergence speed, being 3.7 times faster than the Const strategy, and achieve an advanced FID = 3.35 score on the ImageNet512.</p>", "Keywords": "Diffusion models; Multi-objective optimization; Image generation; Loss weight; Convergence acceleration", "DOI": "10.1007/s10489-024-06043-3", "PubYear": 2025, "Volume": "55", "Issue": "3", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, South-Central Minzu University, Wuhan, China; Hubei Provincial Engineering Research Center of Agricultural Blockchain and Intelligent Management, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, South-Central Minzu University, Wuhan, China; Hubei Provincial Engineering Research Center of Agricultural Blockchain and Intelligent Management, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, South-Central Minzu University, Wuhan, China; Hubei Provincial Engineering Research Center for Intelligent Management of Manufacturing Enterprises, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, South-Central Minzu University, Wuhan, China; Hubei Provincial Engineering Research Center for Intelligent Management of Manufacturing Enterprises, Wuhan, China; Corresponding author."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Insight Centre for Data Analytics, Dublin City University, Dublin, Ireland"}], "References": [{"Title": "Image synthesis with adversarial networks: A comprehensive survey and case studies", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "72", "Issue": "", "Page": "126", "JournalTitle": "Information Fusion"}, {"Title": "Reconstruction probability-based anomaly detection using variational auto-encoders", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "45", "Issue": "3", "Page": "231", "JournalTitle": "International Journal of Computers and Applications"}, {"Title": "Convolutional block attention based network for copy-move image forgery detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "83", "Issue": "1", "Page": "2383", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Image synthesis from an ethical perspective", "Authors": "<PERSON>", "PubYear": 2025, "Volume": "40", "Issue": "2", "Page": "437", "JournalTitle": "AI & SOCIETY"}]}, {"ArticleId": 119999728, "Title": "Constructing understanding: on the constructional information encoded in large language models", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10579-024-09799-9", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 3989, "JournalTitle": "Language Resources and Evaluation", "ISSN": "1574-020X", "EISSN": "1574-0218", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 119999752, "Title": "Operational Verification of a Parallel Open/Closeable Forceps Tip Mechanism for Forceps-Type Mini-PET", "Abstract": "<p>Forceps-type mini-positron emission tomography (mini-PET) has been proposed as an intraoperative device for examining metastatic lymph nodes in the treatment of esophageal cancer. Although this forceps-type mini-PET detects cancer by radiation measurement, the scissor-like tip of the device affects the measurement accuracy. Therefore, to improve the detection sensitivity of the forceps-type mini-PET without depending on the operator, we fabricated a forceps tip mechanism using a parallel-link for parallel opening and closing motions, and verified the parallel motion and the force required for opening and closing. Consequently, we confirmed parallel motion from the opening and closing widths of the tip detectors. In addition, we confirmed that the forces required for opening and closing were significantly smaller than those required for the conventional mechanism.</p>", "Keywords": "parallel link mechanism;forceps;positron emission tomography", "DOI": "10.20965/jrm.2024.p1550", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Science and Engineering, Chiba University, 1-33 Yayoi-cho, Inage-ku, Chiba, Chiba 263-8522, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Frontier Medical Engineering, Chiba University, 1-33 Yayoi-cho, Inage-ku, Chiba, Chiba 263-8522, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Mirai-Imaging Corporation, 1-24 Uchigomimayamachi, Iwaki, Fukushima 973-8402, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Institutes for Quantum Science and Technology, 4-9-1 Anagawa, Inage-ku, Chiba, Chiba 263-8555, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Institutes for Quantum Science and Technology, 4-9-1 Anagawa, Inage-ku, Chiba, Chiba 263-8555, Japan"}], "References": []}, {"ArticleId": 119999753, "Title": "Automatic Generation of Dynamic Arousal Expression Based on Decaying Wave Synthesis for Robot Faces", "Abstract": "<p>The automatic generation of dynamic facial expressions to transmit the internal states of a robot, such as mood, is crucial for communication robots. In contrast, conventional methods rely on patchwork-like replaying of recorded motions, which makes it difficult to achieve adaptive smooth transitions of the facial expressions of internal states that easily fluctuate according to the internal and external circumstances of the robots. To achieve adaptive facial expressions in robots, designing and providing deep structures that dynamically generate facial movements based on the affective state of the robot is more effective than directly designing superficial facial movements. To address this issue, this paper proposes a method for automatically synthesizing complex but organized command sequences. The proposed system generated temporal control signals for each facial actuator as a linear combination of intermittently reactivating decaying waves. The forms of these waves were automatically tuned to express the internal state, such as the arousal level. We introduce a mathematical formulation of the system using arousal expression in a child-type android as an example, and demonstrate that the system can transmit different arousal levels without deteriorating human-like impressions. The experimental results support our hypothesis that appropriately tuned waveform facial movements can transmit different arousal state levels, and that such movements can be automatically generated as superimposed decaying waves.</p>", "Keywords": "dynamic facial expression;android robot;affective expression;autonomous system;arousal level", "DOI": "10.20965/jrm.2024.p1481", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Graduate School of Engineering, Osaka University, 2-1 Yamadaoka, Suita, Osaka 565-0871, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Graduate School of Engineering, Osaka University, 2-1 Yamadaoka, Suita, Osaka 565-0871, Japan;Department of Smart Design, Faculty of Architecture and Design, Aichi Sangyo University, 12-5 Harayama, Okacho, Okazaki, Aichi 444-0005, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Télécom Physique Strasbourg, University of Strasbourg, 300 Boulevard Sébastien <PERSON>, Illkirch-Graffenstaden 67400, France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Graduate School of Engineering, Osaka University, 2-1 Yamadaoka, Suita, Osaka 565-0871, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Graduate School of Engineering, Osaka University, 2-1 Yamadaoka, Suita, Osaka 565-0871, Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Graduate School of Engineering, Osaka University, 2-1 Yamadaoka, Suita, Osaka 565-0871, Japan"}], "References": [{"Title": "Comparison Between the Facial Flow Lines of Androids and Humans", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "", "Page": "29", "JournalTitle": "Frontiers in Robotics and AI"}, {"Title": "Development of ‘ibuki’ an electrically actuated childlike android with mobility and its potential in the future society", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "40", "Issue": "4", "Page": "933", "JournalTitle": "Robotica"}, {"Title": "Facial Emotion Expressions in Human–Robot Interaction: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "7", "Page": "1583", "JournalTitle": "International Journal of Social Robotics"}, {"Title": "Objective evaluation of mechanical expressiveness in android and human faces", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "16", "Page": "767", "JournalTitle": "Advanced Robotics"}, {"Title": "Dynamic movement primitives in robotics: A tutorial survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "42", "Issue": "13", "Page": "1133", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 119999760, "Title": "Effective Stemmers Using Trie Data Structure for Enhanced Processing of Gujarati Text", "Abstract": "<p>Stemming plays a crucial role in natural language processing and information retrieval. It is challenging for the Gujarati language due to the complex morphology of several stemming algorithms for the Gujarati language that have been developed using rule-based, dictionary-based, or hybrid approaches. However, they are computationally expensive, produce more over-stemming errors and have limited accuracy. This paper introduces three novel optimized Gujarati stemmers using a trie data structure to overcome the above-mentioned limitations. The significant contributions to this paper are as follows. First, three optimized Gujarati stemmers, namely Optimized Gujarati Stemmer using Suffix Stripping Approach (OGS_SSA), Optimized Gujarati Stemmer using Rule-Based Approach (OGS_RBA), and Optimized Gujarati Stemmer using Re-parsing Based Approach (OGS_RPA), are proposed. Second, a novel algorithm to create a Gujarati dictionary using the trie data structure is proposed. Third, the proposed stemmers are rigorously assessed using three standard datasets, namely entertainment, health, and agriculture. The performance of the proposed stemmers is measured using evaluation parameters such as precision, recall, F 1 score, accuracy, number of stemming errors and processing time. The results show that OGS_RPA consistently exceeds the OGS_SSA and OGS_RBA for precision, recall, F 1 score, and accuracy. In addition, it exhibits a lower number of stemming errors. Moreover, the performance of the proposed stemmer is compared with the existing Gujarati hybrid stemmer. The results show a 14–16% improvement in accuracy and less processing time compared to the Gujarati hybrid stemmer. OGS_SSA demonstrated enhanced processing time, making it a feasible option for applications that prioritize prompt response time. Furthermore, it demonstrates 10–11% enhancement in accuracy and a reduction in processing time than the Gujarati hybrid stemmer. OGS_RBA exhibits moderate performance due to its rule-based methodology compared to OGS_RPA and OGS_SSA. However, it shows 10–13% improvement in accuracy than the Gujarati hybrid stemmer.</p>", "Keywords": "Natural language processing; Information retrieval; Stemming; Rule-based stemmer; Suffix-stripping; Based stemmer; Reparsing-based stemmer; Over-stemming error; Under-stemming error; Gujarati stemmer; Optimized stemmer; Trie data structure", "DOI": "10.1007/s44196-024-00679-2", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Vishwakarma Government Engineering College, Ahmedabad, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Sarvajanik College of Engineering and Technology, Surat, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Symbiosis Centre for Applied Artificial Intelligence, Symbiosis Institute of Technology, Symbiosis International University, Pune, India; Corresponding author."}], "References": [{"Title": "Improving Semantic Coherence of Gujarati Text Topic Model Using Inflectional Forms Reduction and Single-letter Words Removal", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A Trie based lemmatizer for Assamese language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "5", "Page": "2355", "JournalTitle": "International Journal of Information Technology"}, {"Title": "KreolStem: A hybrid language-dependent stemmer for Kreol Morisien", "Authors": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "8", "Page": "1561", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Building a text retrieval system for the Sanskrit language: Exploring indexing, stemming, and searching issues", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "81", "Issue": "", "Page": "101518", "JournalTitle": "Computer Speech & Language"}, {"Title": "Adaptive operator selection with dueling deep Q-network for evolutionary multi-objective optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "581", "Issue": "", "Page": "127491", "JournalTitle": "Neurocomputing"}, {"Title": "SUSTEM: An Improved Rule-Based Sundanese Stemmer", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}, {"Title": "A hyper-heuristic with deep Q-network for the multi-objective unmanned surface vehicles scheduling problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "596", "Issue": "", "Page": "127943", "JournalTitle": "Neurocomputing"}, {"Title": "A hyper-heuristic algorithm via proximal policy optimization for multi-objective truss problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "256", "Issue": "", "Page": "124929", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 119999764, "Title": "On predicting an NBA game outcome from half-time statistics", "Abstract": "<p>Predicting the outcome of an NBA game is a major concern for betting companies and individuals who are willing to bet. We attack this task by employing various advanced machine learning algorithms and techniques, utilizing simple half-time statistics from both teams. Data collected from 3 seasons, from 2020/21 up to 2022/23 were used to assess the predictive performance of the algorithms at two axes. For each season separately, apply the algorithms and estimate the outcomes of the games of the same season and secondly, apply the algorithms in one season and estimate the outcomes of the games in the next season. The results showed high levels of accuracy as measured by the area under the curve. The analysis was repeated after performing variable selection using a non-linear algorithm that selected the most important half-time statistics, while retaining the predictive performance at high levels of accuracy.</p>", "Keywords": "NBA; Half-time statistics; Game outcome; Machine learning", "DOI": "10.1007/s44163-024-00201-9", "PubYear": 2024, "Volume": "4", "Issue": "1", "JournalId": 91443, "JournalTitle": "Discover Artificial Intelligence", "ISSN": "", "EISSN": "2731-0809", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Crete, Rethymno, Greece; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Crete, Rethymno, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Crete, Rethymno, Greece"}], "References": []}, {"ArticleId": 119999768, "Title": "Toilet Floor Trash Detection System for Unidentified Solid Waste", "Abstract": "<p>The maintenance of public toilets, such as those found in convenience stores, presents challenges in the era of limited human resources. Consequently, the development of an automatic toilet cleaning system is necessary. The detection of trash on the toilet floor is an essential component of the automatic toilet cleaning system. However, this process presents its own set of challenges, including the unpredictability of the types and locations of the trash that may be present. This study proposes a system for detecting solid waste on the toilet floor by applying the structure and feature similarity index method of image. The difference in the structure and features of the reference and actual images can indicate the trash that appears on the toilet floor. This study also proposes a method for determining the threshold value of similarity feature measurement. The experimental results demonstrate that the proposed detection system is able to produce a detection success rate of up to 96.5%. Additionally, the system proves capable of detecting small objects, such as human hair, under specific conditions. This method offers a resource-efficient solution to the challenges faced in maintaining public toilet cleanliness.</p>", "Keywords": "toilet floor trash detection;toilet solid trash;toilet floor;structure and feature similarity of image", "DOI": "10.20965/jrm.2024.p1558", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate School of Systems Design, Tokyo Metropolitan University, 6-6 Asahigaoka, Hino, Tokyo 191-0065, Japan;Department of Electrical Engineering, Universitas Muhammadiyah Yogyakarta, Jln. <PERSON>, Kasihan, Bantul, Yogyakarta 55183, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Systems Design, Tokyo Metropolitan University, 6-6 Asahigaoka, Hino, Tokyo 191-0065, Japan"}], "References": [{"Title": "Adaptive motion generation using imitation learning and highly compliant end effector for autonomous cleaning", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "3-4", "Page": "189", "JournalTitle": "Advanced Robotics"}, {"Title": "Toward Autonomous Garbage Collection Robots in Terrains with Different Elevations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "6", "Page": "1164", "JournalTitle": "Journal of Robotics and Mechatronics"}, {"Title": "Garbage Detection Using YOLOv3 in Nakanoshima Challenge", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "6", "Page": "1200", "JournalTitle": "Journal of Robotics and Mechatronics"}, {"Title": "Filter pruning by quantifying feature similarity and entropy of feature maps", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "544", "Issue": "", "Page": "126297", "JournalTitle": "Neurocomputing"}, {"Title": "Trash Detection Algorithm Suitable for Mobile Robots Using Improved YOLO", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "27", "Issue": "4", "Page": "622", "JournalTitle": "Journal of Advanced Computational Intelligence and Intelligent Informatics"}, {"Title": "Water Droplet Detection System on Toilet Floor Using Heat Absorption Capacity of Liquid", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "2", "Page": "388", "JournalTitle": "Journal of Robotics and Mechatronics"}]}, {"ArticleId": 119999775, "Title": "Local Shape Transformation of a Snake Robot by Changing Approximation Range on Continuous Curve", "Abstract": "<p>A snake robot can form various shapes by fitting to an arbitrary continuous curve thanks to its numerous degrees of freedom. When traversing an unknown complex environment, a snake robot may need to perform local shape transformation to avoid obstacles or perform specific tasks. In this study, we present a local shape transformation control method for expanding the mobility of a snake robot. The proposed control method lifts a local part of the robot away from the target continuous curve while the leading part and the trailing part shift accordingly to remain fitted to the continuous curve without twisting. The proposed local shape transformation is realized by changing the approximation range on the continuous curve without changing the shape of the continuous curve. Simulations were conducted to evaluate the effectiveness of the local hump-shaped transformation control on various types of continuous curves. We also evaluated the proposed control method by comparing it with other local shape transformation control methods through simulations. Additionally, we propose two examples of applications which are the recovery from a stuck state and recovery from joint failure. Experiments were conducted to verify the effectiveness of the proposed control method.</p>", "Keywords": "biologically-inspired robots;articulated mobile robot;motion control;local body shape control;search and rescue robots", "DOI": "10.20965/jrm.2024.p1315", "PubYear": 2024, "Volume": "36", "Issue": "6", "JournalId": 33418, "JournalTitle": "Journal of Robotics and Mechatronics", "ISSN": "0915-3942", "EISSN": "1883-8049", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The University of Electro-Communications, 1-5-1 Chofugaoka, Chofu, Tokyo 182-8585, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "The University of Electro-Communications, 1-5-1 Chofugaoka, Chofu, Tokyo 182-8585, Japan"}], "References": [{"Title": "Local body shape control of an articulated mobile robot and an application for recovery from a stuck state", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "10", "Page": "488", "JournalTitle": "Advanced Robotics"}, {"Title": "Development and control of an articulated mobile robot T<sup>2</sup> snake-4.2 for plant disaster prevention – development of M2 arm and C-hand", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "36", "Issue": "21", "Page": "1134", "JournalTitle": "Advanced Robotics"}, {"Title": "Semiautonomous recovery system from a stuck state of an articulated mobile robot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "37", "Issue": "17", "Page": "1112", "JournalTitle": "Advanced Robotics"}]}, {"ArticleId": 119999849, "Title": "Pivotal role of digital twins in the metaverse: A review", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.dcan.2024.12.003", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 5621, "JournalTitle": "Digital Communications and Networks", "ISSN": "2352-8648", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Review of digital twin about concepts, technologies, and industrial applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "346", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Digital Twin—Cyber Replica of Physical Things: Architecture, Applications and Future Research Directions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "2", "Page": "64", "JournalTitle": "Future Internet"}, {"Title": "Metaverse: Perspectives from graphics, interactions and visualization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "1", "Page": "56", "JournalTitle": "Visual Informatics"}, {"Title": "BlockNet: Beyond reliable spatial Digital Twins to Parallel Metaverse", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "5", "Page": "100468", "JournalTitle": "Patterns"}, {"Title": "Immersive virtual reality in the age of the Metaverse: A hybrid-narrative review based on the technology affordance perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "101717", "JournalTitle": "The Journal of Strategic Information Systems"}, {"Title": "Security Risks of the Metaverse World", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "16", "Issue": "13", "Page": "4", "JournalTitle": "International Journal of Interactive Mobile Technologies (iJIM)"}, {"Title": "Application of AR/VR Technology in Industry 4.0.", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "2990", "JournalTitle": "Procedia Computer Science"}, {"Title": "A Study on Factors Affecting the User Experience of Metaverse Service: ", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Information Systems in the Service Sector"}, {"Title": "Hooked on the metaverse? Exploring the prevalence of addiction to virtual reality applications", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "172", "JournalTitle": "Frontiers in Virtual Reality"}, {"Title": "Development of metaverse for intelligent healthcare", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "11", "Page": "922", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "Blockchain for the metaverse: A Review", "Authors": "<PERSON><PERSON><PERSON>-<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "401", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Digital twins for building industrial metaverse", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "66", "Issue": "", "Page": "31", "JournalTitle": "Journal of Advanced Research"}, {"Title": "Customer Service Ethics in the Metaverse: A Value-Sensitive Design Science Approach", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2025, "Volume": "41", "Issue": "1", "Page": "254", "JournalTitle": "International Journal of Human–Computer Interaction"}, {"Title": "Trust framework for self-sovereign identity in metaverse healthcare applications", "Authors": "<PERSON>; <PERSON>", "PubYear": 2024, "Volume": "7", "Issue": "4", "Page": "304", "JournalTitle": "Journal of Information Technology and Data Management"}, {"Title": "Navigating the Metaverse: A Comprehensive Analysis of Consumer Electronics Prospects and Challenges", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "ACM Transactions on Internet Technology"}]}, {"ArticleId": 120000232, "Title": "A bi-objective data-driven chance-constrained optimization for sustainable urban medical waste management", "Abstract": "The processing and transportation of medical waste pose uncertain threats to the surrounding people and the environment in urban road networks. This paper aims to mitigate such risks under an emergency system with uncertain response times. In more detail, we first formulate an integrated pollution-population risk assessment that estimates the dynamic impact on the exposed population by embedding the emergency response time into the risk measure. Given the variability in traffic conditions, the response time is uncertain, which also affects the associated risks. Taking this randomness into consideration, a bi-objective chance-constrained model is developed to seek optimal facility locations, vehicle acquisitions, as well as route and tour plans, such that both the risk and cost are simultaneously minimized. To meet practical restrictions on medical waste collection, continuously accumulative vehicle load and volume constraints are added to the two-commodity flow formulation. Then, we propose a comprehensive solution procedure that integrates a Back Propagation Neural Network approach within the fuzzy chance constraint framework to address uncertainties. Two multi-objective methods, an augmented ɛ -constraint solution technique and a nearest-neighbor Non-Dominated Sorting Genetic Algorithm II (NSGA-II) algorithm, are implemented respectively for small- and large-scale problem instances. A series of numerical experiments are conducted on a real-life situation in Shanghai city of China to demonstrate the workability of the proposed model and approach. The numerical results show that our recommended system can effectively prevent the overall capacity shortage, reduce the total cost and risk respectively by more than 8% and 11%, as well as lower the transportation risk and distance respectively by nearly 15% and 23%.", "Keywords": "Logistics; Sustainability; Location-routing; Chance constraint; Data-driven", "DOI": "10.1016/j.eswa.2024.126213", "PubYear": 2025, "Volume": "267", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil and Transportation Engineering, Guangdong University of Technology, Guangzhou, 510006, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil and Transportation Engineering, Guangdong University of Technology, Guangzhou, 510006, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Business Administration, Memorial University of Newfoundland, St. John’s, Newfoundland and Labrador, Canada A1B 3X5;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Civil and Transportation Engineering, Guangdong University of Technology, Guangzhou, 510006, China"}], "References": [{"Title": "Medical waste management during coronavirus disease 2019 (COVID-19) outbreak: A mathematical programming model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "162", "Issue": "", "Page": "107668", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Managing reliable emergency logistics for hazardous materials: A two-stage robust optimization approach", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "138", "Issue": "", "Page": "105557", "JournalTitle": "Computers & Operations Research"}, {"Title": "A mathematical model for designing a network of sustainable medical waste management under uncertainty", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "171", "Issue": "", "Page": "108372", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Infectious waste management during a pandemic: A stochastic location-routing problem with chance-constrained time windows", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "177", "Issue": "", "Page": "109066", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Digital twin-driven robust bi-level optimisation model for COVID-19 medical waste location-transport under circular economy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "186", "Issue": "", "Page": "109107", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Emergency Logistics Management for Hazardous Materials with Demand Uncertainty and Link Unavailability", "Authors": "<PERSON>; <PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "2", "Page": "175", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "Designing reverse logistics network for healthcare waste management considering epidemic disruptions under uncertainty", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2023, "Volume": "142", "Issue": "", "Page": "110372", "JournalTitle": "Applied Soft Computing"}, {"Title": "A multi-stage decision framework for managing hazardous waste logistics with random release dates", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "232", "Issue": "", "Page": "120750", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Design of urban medical waste recycling network considering loading reliability under uncertain conditions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "183", "Issue": "", "Page": "109471", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Sparrow search algorithm with adaptive t distribution for multi-objective low-carbon multimodal transportation planning problem with fuzzy demand and fuzzy time", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "238", "Issue": "", "Page": "122042", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 120000264, "Title": "Low complexity adaptive neural network three-dimensional tracking control for autonomous underwater vehicles considering uncertain dynamics", "Abstract": "This paper proposes a low complexity adaptive neural network control scheme for three-dimensional tracking control of autonomous underwater vehicles (AUVs) in the presence of uncertain dynamics and external disturbances. The virtual stabilizing functions are proposed by combining error nonlinear transformation and backstepping control strategy, which ensures that the transient tracking errors are confined in the prescribed range. A low complexity error-driven adaptive control strategy that only employs one neural network is presented to eliminate the uncertain dynamics in different free degrees, thereby effectively reducing the computational complexity of the AUV controller. Theoretical analyses indicate that the tracking error signals of the AUV system are bounded. The comparative simulation studies are conducted to substantiate the effectiveness of our proposed control scheme.", "Keywords": "", "DOI": "10.1016/j.engappai.2024.109860", "PubYear": 2025, "Volume": "142", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Xu", "Affiliation": "School of Automation, Qingdao University, Qingdao 266071, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Qingdao University, Qingdao 266071, China;Shandong Key Laboratory of Industrial Control Technology, Qingdao 266071, China;Corresponding author at: School of Automation, Qingdao University, Qingdao 266071, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation, Qingdao University, Qingdao 266071, China;Shandong Key Laboratory of Industrial Control Technology, Qingdao 266071, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation, Qingdao University, Qingdao 266071, China"}], "References": [{"Title": "Control of an AUV with completely unknown dynamics and multi-asymmetric input constraints via off-policy reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "5255", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Fixed‐time extended state observer‐based trajectory tracking control for autonomous underwater vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "686", "JournalTitle": "Asian Journal of Control"}, {"Title": "Fixed‐time extended state observer‐based trajectory tracking control for autonomous underwater vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "686", "JournalTitle": "Asian Journal of Control"}, {"Title": "Command filtered-based neuro-adaptive robust finite-time trajectory tracking control of autonomous underwater vehicles under stochastic perturbations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "519", "Issue": "", "Page": "158", "JournalTitle": "Neurocomputing"}, {"Title": "Fault-tolerant control based on adaptive dynamic programming for reentry vehicles subjected to state-dependent actuator fault", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "123", "Issue": "", "Page": "106450", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Adaptive finite-time fault-tolerant control for the full-state-constrained robotic manipulator with novel given performance", "Authors": "<PERSON><PERSON> Li; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "125", "Issue": "", "Page": "106650", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Adaptive neural network control of manipulators with uncertain kinematics and dynamics", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "133", "Issue": "", "Page": "107935", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 120000353, "Title": "Multi-UAV air combat cooperative game based on virtual opponent and value attention decomposition policy gradient", "Abstract": "In the multi-unmanned aerial vehicle (UAV) air combat confrontation environment, deriving the cooperative policy of friendly aircraft is still a challenge, owing to the higher-order differential dynamics model of aircraft and the confidence assignment problem in multi-UAV air combat with conflict and cooperation. In this paper, a novel reinforcement learning method that combines virtual opponent and value attention decomposition is proposed. In particular, to reduce the difficulty in training induced by the higher order differential dynamics model, the actions of aircraft are abstracted into actions of the game layer and maneuvering actions of the bottom layer, in which the actions of the game layer are modeled as the pose of the virtual opponent. In the training process, only the policy of the game layer is trained, and the maneuvering policy of the bottom layer is the default policy or the rule-based policy. To address the confidence assignment problem encountered during multi-UAV cooperative training, the total value function of the team is decomposed into individual value functions based on the attention mechanism, and the policy of the game layer is optimized by integrating the individual value into the gradient computation as the baseline. Finally, the algorithm is verified on the dynamic high-fidelity training platform. The results indicate that the algorithm outperforms the state-of-the-art method in typical multi-UAV air combat scenarios such as 4V4, 5V5, and 6V6.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126069", "PubYear": 2025, "Volume": "267", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Robotics and Automatic Information System, College of Artificial Intelligence, Nankai University, Tianjin, China;Tianjin Key Laboratory of Intelligent Robotics, Nankai University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Robotics and Automatic Information System, College of Artificial Intelligence, Nankai University, Tianjin, China;Tianjin Key Laboratory of Intelligent Robotics, Nankai University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Robotics and Automatic Information System, College of Artificial Intelligence, Nankai University, Tianjin, China;Tianjin Key Laboratory of Intelligent Robotics, Nankai University, China;Corresponding author at: Institute of Robotics and Automatic Information System, College of Artificial Intelligence, Nankai University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory for Big Data and Decision, College of Systems Engineering, National University of Defense Technology, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Robotics and Automatic Information System, College of Artificial Intelligence, Nankai University, Tianjin, China;Tianjin Key Laboratory of Intelligent Robotics, Nankai University, China"}], "References": [{"Title": "Sliding mode control of multi-agent system with application to UAV air combat", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "96", "Issue": "", "Page": "107491", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "UAV Cooperative Air Combat Maneuvering Confrontation Based on Multi-agent Reinforcement Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "11", "Issue": "3", "Page": "273", "JournalTitle": "Unmanned Systems"}, {"Title": "Hierarchical Reinforcement Learning for Air Combat at DARPA's AlphaDogfight Trials", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "4", "Issue": "6", "Page": "1371", "JournalTitle": "IEEE Transactions on Artificial Intelligence"}, {"Title": "Mean policy-based proximal policy optimization for maneuvering decision in multi-UAV air combat", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2024, "Volume": "36", "Issue": "31", "Page": "19667", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 120000354, "Title": "RSH-BU: Revocable secret handshakes with backward unlinkability from VLR group signatures", "Abstract": "Secret handshake scheme is a bi-directional authentication method that enables two participants from the same organization to identify both sides in private. A new generic construction of secret handshakes is proposed in this paper, which is primarily derived from Verifier-Local Revocation Group Signature ( VLR-GS ). An instance of the secret handshake scheme, drawn from a short VLR-GS with backward unlinkability, is presented. Our scheme incorporates an efficient revocation mechanism that guarantees both traceability and unlinkability. Moreover, the past actions of revoked users remain confidential due to the backward unlinkability mechanism. We have also enhanced the communication protection between the Group Authority ( GA ) and its members to prevent malicious <PERSON> from forging group members. Compared to previous secret handshake schemes, our scheme significantly reduces both communication and computation overhead, making it particularly suitable for mobile environments. Our proposal’s security can be proven under the random oracle model, given the difficulty of Decision Linear (DLIN) and q-Strong Diffie<PERSON> (q-SDH) problems.", "Keywords": "", "DOI": "10.1016/j.csi.2024.103966", "PubYear": 2025, "Volume": "93", "Issue": "", "JournalId": 739, "JournalTitle": "Computer Standards & Interfaces", "ISSN": "0920-5489", "EISSN": "1872-7018", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Statistics and Mathematics, Guangdong University of Finance and Economics, Guangzhou 510320, China;Guangdong Provincial Key Laboratory of Information Security Technology, Sun Yat-sen University, Guangzhou 510006, China;Big Data and Education Statistics Application Laboratory, Guangdong University of Finance and Economics, Guangzhou 510320, China;Corresponding author at: School of Statistics and Mathematics, Guangdong University of Finance and Economics, Guangzhou 510320, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information, Guangdong University of Finance and Economics, Guangzhou 510320, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Sangfor Technologies Inc., Changsha 410006, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science, South China Normal University, Guangzhou 510631, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Statistics and Mathematics, Guangdong University of Finance and Economics, Guangzhou 510320, China;Guangdong Provincial Key Laboratory of Information Security Technology, Sun Yat-sen University, Guangzhou 510006, China;Big Data and Education Statistics Application Laboratory, Guangdong University of Finance and Economics, Guangzhou 510320, China"}], "References": [{"Title": "Intersection-policy private mutual authentication from authorized private set intersection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "2", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "A new secret handshake scheme with multi-symptom intersection for mobile healthcare social networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "142", "JournalTitle": "Information Sciences"}, {"Title": "Unlinkable and Revocable Secret Handshake", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "8", "Page": "1303", "JournalTitle": "The Computer Journal"}, {"Title": "Secret handshakes: Full dynamicity, deniability and lattice-based design", "Authors": "<PERSON><PERSON><PERSON> An; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "940", "Issue": "", "Page": "14", "JournalTitle": "Theoretical Computer Science"}, {"Title": "ISH : Isogeny-based Secret Handshakes with friendly communication costs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2025, "Volume": "91", "Issue": "", "Page": "103880", "JournalTitle": "Computer Standards & Interfaces"}]}, {"ArticleId": 120000531, "Title": "The politics of the digital transition: lessons from slash fan fiction communities at the turn of the millennium", "Abstract": "", "Keywords": "", "DOI": "10.1080/24701475.2024.2442884", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 31570, "JournalTitle": "Internet Histories", "ISSN": "2470-1475", "EISSN": "2470-1483", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Utrecht University, Utrecht, The Netherlands"}], "References": []}, {"ArticleId": 120000743, "Title": "Polyhedral Clinching Auctions for Indivisible Goods", "Abstract": "<p>In this study, we propose a polyhedral clinching auction for indivisible goods, which has so far been studied for divisible goods. As in the divisible setting by <PERSON><PERSON> et al. (2015), our mechanism enjoys incentive compatibility, individual rationality, and Pareto optimality, and works with polymatroidal environments. A notable feature of this mechanism for the indivisible setting is that the entire procedure can be conducted in time polynomial of the number of buyers and goods. Moreover, we show additional efficiency guarantees, recently established by <PERSON> for the divisible setting: the liquid welfare (LW) of our mechanism achieves more than half of the optimal LW, and the social welfare is more than the optimal LW.</p>", "Keywords": "", "DOI": "10.1145/3708506", "PubYear": 2025, "Volume": "13", "Issue": "1", "JournalId": 22883, "JournalTitle": "ACM Transactions on Economics and Computation", "ISSN": "2167-8375", "EISSN": "2167-8383", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Mathematics, Nagoya University, Nagoya, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science and Technology, Keio University, Yokohama, Japan"}], "References": []}, {"ArticleId": 120000750, "Title": "Flash-oriented Coded Storage: Research Status and Future Directions", "Abstract": "<p>Flash-based solid-state drives (SSDs) have been widely adopted in various storage systems, manifesting better performance than their forerunner HDDs. However, the characteristics of flash media post some drawbacks when deploying SSD-based storage systems. First, flash media have limited program/erase cycles, making them vulnerable to media failures. Second, SSD foreground I/Os can suffer from inconsistent performance due to interference from background operations like garbage collection (GC). The major solution to the above problems is to introduce data redundancy. Redundant data can not only detect raw bit errors and recover lost data but also enable I/O scheduling to sidestep SSDs that are under performance degradation.</p><p>Compared with multi-replica, data coding is a more space-efficient way to provide redundancy. However, it is more challenging to simultaneously achieve low access latency, consistent performance and fast recovery. This paper examines the design of coded storage in existing storage systems, with a focus on flash storage systems, and how they address these challenges. The coded storage techniques are categorized into in-device coding, cross-device coding, and cross-machine coding. They are designed for different scenarios and purposes, but share some design rationales in common. For each type of coded storage, we begin by presenting the theoretical bases, followed by an overview of how existing studies address the performance and endurance issues of coded storage from a systemic perspective. Finally, we review the history of coded storage, list several key insights from existing works, and speculate some promising directions for flash-oriented coded storage systems.</p>", "Keywords": "", "DOI": "10.1145/3708995", "PubYear": 2025, "Volume": "21", "Issue": "1", "JournalId": 19571, "JournalTitle": "ACM Transactions on Storage", "ISSN": "1553-3077", "EISSN": "1553-3093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Tsinghua University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, The Ohio State University, Columbus, United States"}], "References": [{"Title": "PBS", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Storage"}, {"Title": "Exploring Performance Characteristics of the Optane 3D Xpoint Storage Technology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Modeling and Performance Evaluation of Computing Systems"}, {"Title": "An empirical study of I/O separation for burst buffers in HPC systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "96", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Efficient erasure-coded data updates based on file class predictions and hybrid writes", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "104", "Issue": "", "Page": "108441", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "开放通道固态硬盘的设计与应用研究综述", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "24", "Issue": "5", "Page": "637", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "Explorations and Exploitation for Parity-based RAIDs with Ultra-fast SSDs", "Authors": "Shucheng Wang; <PERSON><PERSON>; Hong Jiang", "PubYear": 2024, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Storage"}]}, {"ArticleId": 120000779, "Title": "Correction: Large Process Models: A Vision for Business Process Management in the Age of Generative AI", "Abstract": "", "Keywords": "", "DOI": "10.1007/s13218-024-00884-3", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Lukas N. P. <PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 13, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 14, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "Deadline-constrained routing based on power-law and exponentially distributed contacts in DTNs", "Abstract": "During a large-scale disaster, there is a severe destruction to physical infrastructures such as telecommunication and power lines, which result in the disruption of communication, making timely emergency response challenging. Since Delay Tolerant Networks (DTNs) are infrastructure-less, they tolerate physical destruction and thus can serve as an emergency response network during a disaster scenario. To be effective, DTNs need a routing protocol that maximizes the number of messages delivered within deadline. One obvious approach is to broadcast messages everywhere. However, this approach is impractical as DTNs are resource-constrained. In this work, we propose a cost-effective routing protocol based on the expected delivery delay that optimizes the number of messages delivered within deadline with a significantly low network overhead. Simulations using real-life mobility traces show that with our scheme, up to 95% of messages are delivered within deadline, while requiring on average less than three message copies.", "Keywords": "", "DOI": "10.1016/j.comcom.2024.108038", "PubYear": 2025, "Volume": "231", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, UCLA, Los Angeles, USA"}], "References": [{"Title": "Effective social-context based message delivery using ChitChat in sparse delay tolerant networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "2", "Page": "401", "JournalTitle": "Distributed and Parallel Databases"}, {"Title": "An adaptive multiple spray-and-wait routing algorithm based on social circles in delay tolerant networks", "Authors": "<PERSON>bing Wu; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "189", "Issue": "", "Page": "107901", "JournalTitle": "Computer Networks"}, {"Title": "A new DTN routing strategies ensuring high message delivery ratio while keeping low power consumption", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "17", "Issue": "", "Page": "100463", "JournalTitle": "Internet of Things"}, {"Title": "Emergence of urban growth patterns from human mobility behavior", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "12", "Page": "791", "JournalTitle": "Nature Computational Science"}]}, {"ArticleId": *********, "Title": "A Novel Method for Consumer Preference Extraction Based on Perceived Usefulness and De-Neutral Sentiment", "Abstract": "The accurate assessment of consumer preferences has become increasingly crucial for effective business decision-making. However, merchants face significant challenges in accurately analyzing these preferences due to the overwhelming volume of online consumer feedback, the growing complexity of customer preferences, and the rapid evolution of market trends. Current methodologies are often hampered by various errors and biases, such as the misleading influence of irrelevant information within overloaded data, challenges in extracting latent features from big data, and issues related to neutral sentiment factors. Additionally, the usefulness of information is frequently dependent on voting mechanisms, which are susceptible to biases like the early bird effect. To address these challenges, we propose a novel preference extraction method based on perceived useful information. This approach integrates sentiment analysis rooted in deep learning with neutral sentiment processing and combines multi-criteria decision-making to extract hidden features while mitigating the misleading impact of irrelevant word frequencies on preference calculations. Furthermore, our method employs the principle of information entropy to extract information utility, thereby avoiding common biases associated with traditional voting methods. Experimental results demonstrate the superiority of our method across two case studies: for search-based products, the method achieved an F1 score of 89.6 % and an AUC of 79.4 %, while for experience-based products, it recorded 84.6 % and 80.6 %, respectively. The primary contribution of this research lies in providing a systematic approach to uncover product features and accurately analyze consumer preferences, offering valuable insights for business decision-making. This has significant theoretical and practical implications for product development, marketing, and customer service.", "Keywords": "", "DOI": "10.1016/j.neucom.2024.129197", "PubYear": 2025, "Volume": "619", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai 200093, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Management, Shanghai University, Shanghai 200444, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Business School, University of Shanghai for Science and Technology, Shanghai 200093, China;Corresponding author"}], "References": [{"Title": "Incorporating token-level dictionary feature into neural model for named entity recognition", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "375", "Issue": "", "Page": "43", "JournalTitle": "Neurocomputing"}, {"Title": "A review-driven customer preference measurement model for product improvement: sentiment-based importance–performance analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "1", "Page": "61", "JournalTitle": "Information Systems and e-Business Management"}, {"Title": "Processes and methods of information fusion for ranking products based on online reviews: An overview", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "87", "JournalTitle": "Information Fusion"}, {"Title": "Processes and methods of information fusion for ranking products based on online reviews: An overview", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "87", "JournalTitle": "Information Fusion"}, {"Title": "Revealed preference in online reviews: Purchase verification in the tablet market", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "132", "Issue": "", "Page": "113281", "JournalTitle": "Decision Support Systems"}, {"Title": "Examining the role of emotion in online consumer reviews of various attributes in the surprise box shopping model", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "113344", "JournalTitle": "Decision Support Systems"}, {"Title": "A novel dictionary learning method for sparse representation with nonconvex regularizations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "128", "JournalTitle": "Neurocomputing"}, {"Title": "The impact of service attributes and category on eWOM helpfulness: An investigation of extremely negative and positive ratings using latent semantic analytics and regression analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "", "Page": "106527", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Predicting long-term returns of individual stocks with online reviews", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "406", "JournalTitle": "Neurocomputing"}, {"Title": "Gazing at the stars is not enough, look at the specific word entropy, too!", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "8", "Page": "103388", "JournalTitle": "Information & Management"}, {"Title": "Retaining customers with in-store mobile usage experience in omni-channel retailing: The moderating effects of product information overload and alternative attractiveness", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "46", "Issue": "", "Page": "101028", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "BERT-JAM: Maximizing the utilization of BERT for neural machine translation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "460", "Issue": "", "Page": "84", "JournalTitle": "Neurocomputing"}, {"Title": "An introduction to Deep Learning in Natural Language Processing: Models, techniques, and tools", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "470", "Issue": "", "Page": "443", "JournalTitle": "Neurocomputing"}, {"Title": "Deriving customer preferences for hotels based on aspect-level sentiment analysis of online reviews", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101094", "JournalTitle": "Electronic Commerce Research and Applications"}, {"Title": "Elaboration likelihood model, endogenous quality indicators, and online review helpfulness", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "153", "Issue": "", "Page": "113683", "JournalTitle": "Decision Support Systems"}, {"Title": "Software-defined DDoS detection with information entropy analysis and optimized deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "129", "Issue": "", "Page": "99", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Sourcing product innovation intelligence from online reviews", "Authors": "<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "113751", "JournalTitle": "Decision Support Systems"}, {"Title": "A systematic review of hate speech automatic detection using natural language processing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "546", "Issue": "", "Page": "126232", "JournalTitle": "Neurocomputing"}, {"Title": "Knowledge-based BERT word embedding fine-tuning for emotion recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "552", "Issue": "", "Page": "126488", "JournalTitle": "Neurocomputing"}, {"Title": "Bi-preference Learning Heterogeneous Hypergraph Networks for Session-based Recommendation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "42", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Integrating machine learning and robust optimization for new product development: A consumer and expert preference-based approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "197", "Issue": "", "Page": "110520", "JournalTitle": "Computers & Industrial Engineering"}]}, {"ArticleId": 120000858, "Title": "An unstructured geometrical un-split VOF method for viscoelastic two-phase flows", "Abstract": "Since viscoelastic two-phase flows arise in various industrial and natural processes, developing accurate and efficient software for their detailed numerical simulation is a highly relevant and challenging research task. We present a geometrical unstructured Volume-of-Fluid (VOF) method for handling two-phase flows with viscoelastic liquid phase, where the latter is modeled via generic rate-type constitutive equations and a one-field description is derived by conditional volume averaging of the local instantaneous bulk equations and interface jump conditions. The method builds on the plicRDF-isoAdvector geometrical VOF solver that is extended and combined with the modular framework DeboRheo for viscoelastic computational fluid dynamics (CFD). A piecewise-linear geometrical interface reconstruction technique on general unstructured meshes is employed for discretizing the viscoelastic stresses across the fluid interface. DeboRheo facilitates a flexible combination of different rheological models with appropriate stabilization methods to address the high Weissenberg number problem. <b >Program summary</b> Program Title: DeboRheo CPC Library link to program files: https://doi.org/10.17632/gsgdrjm2md.1 Developer's repository link: https://gitlab.com/deborheo/deborheorelease/ Licensing provisions: GPLv3 Programming language: C++ Nature of problem: DNS of viscoelastic two-phase flows encounters major challenges due to abrupt changes of physical properties and rheological behaviors of the two phases at the fluid interface, and viscoelastic flows characterized with high Weissenberg numbers introduce additional numerical challenges. Solution method: A geometrical unstructured Volume-of-Fluid (VOF) method for handling two-phase flows with a viscoelastic liquid phase, where the latter is modeled by generic rate-type constitutive equations. Appropriate stabilization techniques are included to address the High Weissenberg Number Problem (HWNP).", "Keywords": "Viscoelastic two-phase flows; Direct numerical simulation (DNS); Geometric Volume-of-Fluid (VOF) method; Unstructured Finite Volume method; High Weissenberg number stabilization", "DOI": "10.1016/j.cpc.2024.109475", "PubYear": 2025, "Volume": "309", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Mathematical Modeling and Analysis, Technische Universität Darmstadt, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Mathematical Modeling and Analysis, Technische Universität Darmstadt, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematical Modeling and Analysis, Technische Universität Darmstadt, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Mathematical Modeling and Analysis, Technische Universität Darmstadt, Germany;Corresponding author"}], "References": [{"Title": "SAAMPLE: A Segregated Accuracy-driven Algorithm for Multiphase Pressure-Linked Equations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "104450", "JournalTitle": "Computers & Fluids"}, {"Title": "Inconsistencies in unstructured geometric volume-of-fluid methods for two-phase flows with high density ratios", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "281", "Issue": "", "Page": "106375", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 120000887, "Title": "Dimensionality reduction with deep learning classification for botnet detection in the Internet of Things", "Abstract": "As the concept of Internet of Things (IoT) becomes dominant in today’s lives, manufacturers are rapidly developing IoT devices with primarily focusing on the efficiency and reducing overall cost of their products. However, security issues remain with less or no attention making these devices vulnerable to attackers. One crucial security issue in IoT environments is the intrusion of malicious users. Due to the diversity of IoT devices, previous methods for Intrusion Detection Systems (IDS) do not perform well or efficient in these environments. Furthermore, decreasing the processing power and real-time detection of intrusion in IoT devices require reducing different dimensions of data and preserving the most critical features for detection. Therefore, new machine learning models and feature reduction solutions are being developed to address the problem of intrusion and anomaly detection in IoT-based devices. In this article, after a thorough investigation of previous machine learning methods and datasets for intrusion detection in IoT, an efficient and straightforward feature selection technique, the Fisher’s score, is used to remove the unnecessary features. Next, an auto-encoder model and a deep neural network are proposed for binary and multi-class classifications. For performance evaluation, data from several IoT devices are used from the N-BaIoT dataset, one of the most specific datasets for IoT. Simulation results, reported separately for each device, are compared with other methods, in terms of accuracy, precision, recall and F1-score. These results show that using the Fisher’s score leads to more accuracy especially for binary classification. Moreover, using the proposed auto-encoder, an accuracy of 99% is achieved for all five devices according to the desired criteria, which is higher than the previous compared methods.", "Keywords": "", "DOI": "10.1016/j.eswa.2024.126149", "PubYear": 2025, "Volume": "267", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Shahid Chamran University of Ahvaz, Ahvaz, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Shahid <PERSON>ran University of Ahvaz, Ahvaz, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Shahid Chamran University of Ahvaz, Ahvaz, Iran"}], "References": [{"Title": "DeL-IoT: A deep ensemble learning approach to uncover anomalies in IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "", "Page": "100391", "JournalTitle": "Internet of Things"}, {"Title": "Network optimization using defender system in cloud computing security based intrusion detection system withgame theory deep neural network (IDSGT-DNN)", "Authors": "E Balamurugan; Abolfazl Mehbodniya; <PERSON><PERSON>", "PubYear": 2022, "Volume": "156", "Issue": "", "Page": "142", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "DNNBoT: Deep Neural Network-Based Botnet Detection and Classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "1729", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Development of PCCNN-Based Network Intrusion Detection System for EDGE Computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "1769", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Comparative evaluation of machine learning algorithms for phishing site detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2024, "Volume": "10", "Issue": "", "Page": "e2131", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 120001025, "Title": "Corrigendum to “LFG: An easy-to-use realistic synthetic LandFill Generator” [Software X Volume 28, December 2024, 101936]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.softx.2024.102021", "PubYear": 2025, "Volume": "29", "Issue": "", "JournalId": 33301, "JournalTitle": "SoftwareX", "ISSN": "2352-7110", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Democritus University of Thrace, Xanthi, 67100, Greece;Information Technologies Institute, The Centre for Research & Technology, Hellas, Thessaloniki, 57001, Greece;Corresponding author at: Information Technologies Institute, The Centre for Research & Technology, Hellas, Thessaloniki, 57001, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Technologies Institute, The Centre for Research & Technology, Hellas, Thessaloniki, 57001, Greece"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Democritus University of Thrace, Xanthi, 67100, Greece;Information Technologies Institute, The Centre for Research & Technology, Hellas, Thessaloniki, 57001, Greece"}], "References": []}, {"ArticleId": 120001179, "Title": "Designing a Fuzzy Logic-based Carbon Emission Cost-incorporated Inventory Model: A Comparative Analysis of Different Machine Learning Algorithms for Demand Forecasting with Memory Effects", "Abstract": "", "Keywords": "", "DOI": "10.24818/18423264/58.4.24.20", "PubYear": 2024, "Volume": "58", "Issue": "4/2024", "JournalId": 48696, "JournalTitle": "ECONOMIC COMPUTATION AND ECONOMIC CYBERNETICS STUDIES AND RESEARCH", "ISSN": "0424-267X", "EISSN": "1842-3264", "Authors": [{"AuthorId": 1, "Name": "KESWANI Mamta", "Affiliation": ""}], "References": []}, {"ArticleId": 120001183, "Title": "Detecting Misinformation on Social Media Using Community Insights and Contrastive Learning", "Abstract": "Social media users are more likely to be exposed to similar views and tend to avoid contrasting views, especially when they are part of a community of social media users. In this study, we investigate the presence of user communities and leverage them as a tool to detect misinformation on social media, specifically on X (formerly known as Twitter). We propose a misinformation detection framework, namely Similarity-based Misinformation Detection (SiMiD) that employs microblogs and utilizes user-follower interactions within a social network. Our approach extracts important textual features of social media posts using a transformer-based language model. We use contrastive learning and pseudo-labeling to fine-tune the language model. Then, we measure the similarity for each social media post based on its relevance to each user in the communities. Finally, we train a machine learning model to identify the truthfulness of social media posts using these similarity scores. We evaluate our approach on three social media datasets, compare our method with twelve state-of-the-art approaches, and answer five research questions. The experimental results, supported by statistical tests, show that contrastive learning and user communities can enhance the detection of misinformation on social media. Our model can identify misinformation content by achieving a consistently high weighted F1 score of over 90% across all datasets, even employing only a small number of users in communities. We make our implementations publicly available and provide all details that are necessary for the reproducibility of experiments.\n \n 1", "Keywords": "", "DOI": "10.1145/3709009", "PubYear": 2025, "Volume": "16", "Issue": "2", "JournalId": 14324, "JournalTitle": "ACM Transactions on Intelligent Systems and Technology", "ISSN": "2157-6904", "EISSN": "2157-6912", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Bilkent Information Retrieval Group, Department of Computer Engineering, Bilkent University, Turkey and ASELSAN Inc., Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Middle East Technical University, Turkey"}, {"AuthorId": 3, "Name": "Fazli Can", "Affiliation": "Bilkent Information Retrieval Group, Department of Computer Engineering, Bilkent University, Turkey"}], "References": [{"Title": "<PERSON><PERSON> Detection", "Authors": "Dilek Küçük; Fazli Can", "PubYear": 2021, "Volume": "53", "Issue": "1", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "FakeNewsNet: A Data Repository with News Content, Social Context, and Spatiotemporal Information for Studying Fake News on Social Media", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "3", "Page": "171", "JournalTitle": "Big Data"}, {"Title": "The Future of False Information Detection on Social Media", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "A Survey of Fake News", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "5", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "Deep learning for misinformation detection on online social networks: a survey and new perspectives", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "10", "Issue": "1", "Page": "82", "JournalTitle": "Social Network Analysis and Mining"}, {"Title": "A novel self-learning semi-supervised deep learning network to detect fake news on social media", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "14", "Page": "19341", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "MetaDetector: Meta Event Knowledge Transfer for Fake News Detection", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Towards COVID-19 fake news detection using transformer-based models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "274", "Issue": "", "Page": "110642", "JournalTitle": "Knowledge-Based Systems"}]}, {"ArticleId": 120001206, "Title": "Learning Discriminative Features for Visual Tracking via Scenario Decoupling", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11263-024-02307-0", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Qianjin Yu", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "LaSOT: A High-quality Large-scale Single Object Tracking Benchmark", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "2", "Page": "439", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Adaptive Channel Selection for Robust Visual Object Tracking with Discriminative Correlation Filters", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "5", "Page": "1359", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Fully convolutional online tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "224", "Issue": "", "Page": "103547", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 120001558, "Title": "Big data-driven deep mining of online teaching assessment data under affective factor conditions", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.143412", "PubYear": 2024, "Volume": "25", "Issue": "11", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "Ruiting Bai", "Affiliation": ""}], "References": []}, {"ArticleId": 120001575, "Title": "Investigating the Feasibility of Statistically Measuring Anxiety using Wireless Electromyography Sensor via Wireless Personal Communication", "Abstract": "Anxiety and stress result in a disorder that can deteriorate a child’s integrative progress socially, emotionally and cognitively. If not measured, they can lead to undesirable prolonged effects of depressive disorders. Although anxiety and stress can be due to many different reasons but there is still a need of some experimental procedure for quantifying it. In this paper we have investigated such a feasibility to measure anxiety using electromyography sensors. The subjects were children, male and female and were taken from the two different age groups between 4 and 6 years and between 8 and 10 years. The data was collected on 16 subjects using repeated measure design. It was then statistically analyzed to finally provide a generalized linear model for measuring anxiety. The procedure proposed in this research requires an EMG sensor to be placed on the subject’s frontalis muscles and the muscular movements are the quick responses to the stress stimulus. This is easy to implement and is also cost effective. Analysis showed significant difference in EMG activity between stresses and unstressed states. The obtained statistical model thus provided a true measure of anxiety with linear coefficients that are statistically significant at 95% confidence interval (CI).", "Keywords": "Anxiety;Stress;Electromyography;Sensor;Personal Communication;Confidence Interval", "DOI": "10.31645/JISRC.24.22.2.1", "PubYear": 2024, "Volume": "22", "Issue": "2", "JournalId": 57366, "JournalTitle": "Journal of Independent Studies and Research - Computing", "ISSN": "2412-0448", "EISSN": "1998-4154", "Authors": [{"AuthorId": 1, "Name": "Ikram-e Khuda", "Affiliation": "Faculty of Engineering Sciences and Technology (FEST), Iqra University, Karachi, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Engineering Sciences and Technology (FEST), Iqra University, Karachi, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering Sciences and Technology (FEST), Iqra University, Karachi, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering Sciences and Technology (FEST), Iqra University, Karachi, Pakistan"}], "References": []}, {"ArticleId": 120001588, "Title": "Network selection model of terminal security based on ultra-dense heterogeneous network", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2024.143409", "PubYear": 2024, "Volume": "25", "Issue": "11", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 120001622, "Title": "Table of Contents", "Abstract": "", "Keywords": "", "DOI": "10.1109/TLT.2023.3340908", "PubYear": 2024, "Volume": "17", "Issue": "", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 120001658, "Title": "Guest Editorial of the Special Issue on the 3rd IEEE International Conference on Digital Twins and Parallel Intelligence (IEEE DTPI 2023)", "Abstract": "", "Keywords": "", "DOI": "10.1109/JRFID.2024.3515612", "PubYear": 2024, "Volume": "8", "Issue": "", "JournalId": 33601, "JournalTitle": "IEEE Journal of Radio Frequency Identification", "ISSN": "2469-7281", "EISSN": "2469-729X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Multimodal Artificial Intelligence Systems, Institute of Automation, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Anhui University, Hefei, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MEPSS LLC, Indian Harbour Beach, FL, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory for Management and Control of Complex Systems, Chinese Academy of Sciences, Beijing, China"}], "References": []}, {"ArticleId": 120001751, "Title": "Generative AI in Higher Education: A Global Perspective of Institutional Adoption Policies and Guidelines", "Abstract": "Integrating generative AI (GAI) into higher education is crucial for preparing a future generation of GAI-literate students. However, a comprehensive understanding of global institutional adoption policies remains absent, with most prior studies focusing on the Global North and lacking a theoretical lens. This study utilizes the Diffusion of Innovations Theory to examine GAI adoption strategies in higher education across 40 universities from six global regions. It explores the characteristics of GAI innovation, including compatibility, trialability, and observability, and analyses the communication channels and roles and responsibilities outlined in university policies and guidelines. The findings reveal that universities are proactively addressing GAI integration by emphasising academic integrity, enhancing teaching and learning practices, and promoting equity. Key policy measures include the development of guidelines for ethical GAI use, the design of authentic assessments to mitigate misuse, and the provision of training programs for faculty and students to foster GAI literacy. Despite these efforts, gaps remain in comprehensive policy frameworks, particularly in addressing data privacy concerns and ensuring equitable access to GAI tools. The study underscores the importance of clear communication channels, stakeholder collaboration, and ongoing evaluation to support effective GAI adoption. These insights provide actionable insights for policymakers to craft inclusive, transparent, and adaptive strategies for integrating GAI into higher education.", "Keywords": "Generative artificial intelligence; Diffusion of innovations theory; Higher education; Adoption policy; Global perspective", "DOI": "10.1016/j.caeai.2024.100348", "PubYear": 2025, "Volume": "8", "Issue": "", "JournalId": 79689, "JournalTitle": "Computers and Education: Artificial Intelligence", "ISSN": "2666-920X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Centre for Learning Analytics at Monash, Faculty of Information Technology, Monash University, Clayton, Victoria, 3168, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "Lixiang Yan", "Affiliation": "Centre for Learning Analytics at Monash, Faculty of Information Technology, Monash University, Clayton, Victoria, 3168, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centre for Learning Analytics at Monash, Faculty of Information Technology, Monash University, Clayton, Victoria, 3168, Australia;Centro de Tecnologías de Información, Escuela Superior Politécnica del Litoral, Guayaquil, Guayas, Ecuador"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Learning Analytics at Monash, Faculty of Information Technology, Monash University, Clayton, Victoria, 3168, Australia"}, {"AuthorId": 5, "Name": "<PERSON>Mal<PERSON>", "Affiliation": "Centre for Learning Analytics at Monash, Faculty of Information Technology, Monash University, Clayton, Victoria, 3168, Australia"}], "References": [{"Title": "Using artificial intelligence in craft education: crafting with text-to-image generative models", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "34", "Issue": "1", "Page": "1", "JournalTitle": "Digital Creativity"}]}, {"ArticleId": *********, "Title": "Comprehensive application of transfer learning, unsupervised learning and supervised learning in debris flow susceptibility mapping", "Abstract": "Machine learning based debris flow susceptibility mapping (DFSM) is usually a simple supervised learning problem. Inadequate reliable samples in a single study area are usually one of the main factors limiting the performance of a model. This study is to provide a comprehensive and innovative approach for DFSM based on transfer learning, unsupervised learning and supervised learning, which is expected to reduce the limitations of the sample problem. A transfer learning approach called transfer component analysis (TCA) was utilized to project samples from different study areas into a common latent feature space to form a unified study area. The fuzzy C-mean (FCM) clustering algorithm belonging to unsupervised learning was used to cluster the unified study area into several homogeneous regions for independent processing to solve the spatial stratification heterogeneity problem. Another unsupervised learning algorithm named isolation forest (IF) was used to perform anomaly detection on all the samples to improve the reliability of negative samples. With all the datasets prepared, multiple random forest (RF) models representing supervised learning could be built. Traditional supervised learning models based on a single study area were also prepared for comparison. All the models were assessed based on the area under receiver operating characteristic curves (AUC) and statistical results. The results showed that the TCA method could effectively reduce the differences in feature distribution for different study areas. The application of FCM and IF could effectively deal with the problem of spatial stratification heterogeneity and improve the reliability of the negative samples respectively. The comprehensive model (AUC=0.93) proposed in this study is significantly better than that of traditional models (AUC=0.90 and 0.87) in terms of generalization ability, which could be widely applied when performing DFSM on a large scale.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112612", "PubYear": 2025, "Volume": "170", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Civil Engineering and Construction College, Huanghe Science and Technology University, Zhengzhou 450063, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Construction Engineering, Jilin University, Changchun 130012, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Construction Engineering, Jilin University, Changchun 130012, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Construction Engineering, Jilin University, Changchun 130012, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Construction Engineering, Jilin University, Changchun 130012, China"}], "References": [{"Title": "Learning from positive and unlabeled data: a survey", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "4", "Page": "719", "JournalTitle": "Machine Learning"}, {"Title": "A virtual sample generation approach based on a modified conditional GAN and centroidal Voronoi tessellation sampling to cope with small sample size problems: Application to soft sensing for chemical process", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107070", "JournalTitle": "Applied Soft Computing"}, {"Title": "Multi-scene ancient Chinese text recognition with deep coupled alignments", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107475", "JournalTitle": "Applied Soft Computing"}, {"Title": "A probabilistic generalization of isolation forest", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "584", "Issue": "", "Page": "433", "JournalTitle": "Information Sciences"}, {"Title": "Discriminative transfer feature learning based on robust-centers", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "500", "Issue": "", "Page": "39", "JournalTitle": "Neurocomputing"}, {"Title": "Sparse random projection isolation forest for outlier detection", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "163", "Issue": "", "Page": "65", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Personalized federated learning with model interpolation among client clusters and its application in smart home", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "4", "Page": "2175", "JournalTitle": "World Wide Web"}, {"Title": "Landslide susceptibility mapping based on the reliability of landslide and non-landslide sample", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "243", "Issue": "", "Page": "122933", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Joint Learning Strategy of Multi-scale Multi-task Convolutional Neural Network for Aero-engine Prognosis", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "160", "Issue": "", "Page": "111726", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 120001852, "Title": "Mid-infrared spectra of dried and roasted cocoa (Theobroma cacao L.): A dataset for machine learning-based classification of cocoa varieties and prediction of theobromine and caffeine content", "Abstract": "This paper presents a comprehensive dataset of mid-infrared spectra for dried and roasted cocoa beans ( Theobroma cacao L.), along with their corresponding theobromine and caffeine content. Infrared data were acquired using Attenuated Total Reflectance-Fourier Transform Infrared (ATR-FTIR) spectroscopy, while High-Performance Liquid Chromatography (HPLC) was employed to accurately quantify theobromine and caffeine in the dried cocoa beans. The theobromine/caffeine relationship served as a robust chemical marker for distinguishing between different cocoa varieties. This dataset provides a basis for further research, enabling the integration of mid-infrared spectral data with HPLC (as a standard) to fine-tune machine learning and deep learning models that could be used to simultaneously predict the theobromine and caffeine content, as well as cocoa variety in both dried and roasted cocoa samples using a non-destructive approach based on spectral data. The tools developed from this dataset could significantly advance automated processes in the cocoa industry and support decision-making on an industrial scale, facilitating real-time quality control of cocoa-based products, improving cocoa variety classification, and optimizing bean selection, blending strategies, and product formulation, while reducing the need for labor-intensive and costly quantification methods. The dataset is organized into Excel sheets and structured according to experimental conditions and replicates, providing a valuable framework for further analysis, model development, and calibration of multivariate statistical models.", "Keywords": "Functional groups; Cocoa quality monitoring; Multivariate statistical tools; Artificial intelligence; Real-time decision-making", "DOI": "10.1016/j.dib.2024.111243", "PubYear": 2025, "Volume": "58", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Gentil <PERSON>Escobar", "Affiliation": "Centro Surcolombiano de Investigación en Café (CESURCAFÉ), Departamento de Ingeniería Agrícola, Universidad Surcolombiana, Neiva-Huila 410001, Colombia;Grupo de Análisis y Simulación de Procesos Agroalimentarios (ASPA), Instituto Universitario de Ingeniería de Alimentos–FoodUPV, Universitat Politècnica de València, Camí de Vera s/n, Edificio 3F, València 46022, España;Corresponding author at: Centro Surcolombiano de Investigación en Café (CESURCAFÉ), Departamento de Ingeniería Agrícola, Universidad Surcolombiana, Neiva-Huila 410001, Colombia"}, {"AuthorId": 2, "Name": "Andrés <PERSON>", "Affiliation": "Centro Surcolombiano de Investigación en Café (CESURCAFÉ), Departamento de Ingeniería Agrícola, Universidad Surcolombiana, Neiva-Huila 410001, Colombia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centro Surcolombiano de Investigación en Café (CESURCAFÉ), Departamento de Ingeniería Agrícola, Universidad Surcolombiana, Neiva-Huila 410001, Colombia"}], "References": []}, {"ArticleId": 120001955, "Title": "Digital Transformation in the Offer of Cultural Consumption Services - Digitalisation and Best Practices", "Abstract": "", "Keywords": "", "DOI": "10.24818/18423264/**********", "PubYear": 2024, "Volume": "58", "Issue": "4/2024", "JournalId": 48696, "JournalTitle": "ECONOMIC COMPUTATION AND ECONOMIC CYBERNETICS STUDIES AND RESEARCH", "ISSN": "0424-267X", "EISSN": "1842-3264", "Authors": [{"AuthorId": 1, "Name": "MEȘTER Ioana <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "ȘIPOȘ Ciprian", "Affiliation": ""}, {"AuthorId": 3, "Name": "BEGU Andreea <PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "VASILE Răzvan", "Affiliation": ""}], "References": []}, {"ArticleId": 120002081, "Title": "Spatial modeling algorithms for reactions and transport in biological cells", "Abstract": "<p>Biological cells rely on precise spatiotemporal coordination of biochemical reactions to control their functions. Such cell signaling networks have been a common focus for mathematical models, but they remain challenging to simulate, particularly in realistic cell geometries. Here we present Spatial Modeling Algorithms for Reactions and Transport (SMART), a software package that takes in high-level user specifications about cell signaling networks and then assembles and solves the associated mathematical systems. SMART uses state-of-the-art finite element analysis, via the FEniCS Project software, to efficiently and accurately resolve cell signaling events over discretized cellular and subcellular geometries. We demonstrate its application to several different biological systems, including yes-associated protein (YAP)/PDZ-binding motif (TAZ) mechanotransduction, calcium signaling in neurons and cardiomyocytes, and ATP generation in mitochondria. Throughout, we utilize experimentally derived realistic cellular geometries represented by well-conditioned tetrahedral meshes. These scenarios demonstrate the applicability, flexibility, accuracy and efficiency of SMART across a range of temporal and spatial scales.</p><p>© 2024. The Author(s).</p>", "Keywords": "", "DOI": "10.1038/s43588-024-00745-x", "PubYear": 2025, "Volume": "5", "Issue": "1", "JournalId": 83518, "JournalTitle": "Nature Computational Science", "ISSN": "", "EISSN": "2662-8457", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Pharmacology, University of California San Diego School of Medicine, La Jolla, CA, USA. ;Department of Mechanical and Aerospace Engineering, University of California San Diego, La Jolla, CA, USA."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, University of California San Diego, La Jolla, CA, USA. ;Computational Engineering Division, Lawrence Livermore National Laboratory, Livermore, CA, USA."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Numerical Analysis and Scientific Computing, Simula Research Laboratory, Oslo, Norway."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computational Physiology, Simula Research Laboratory, Oslo, Norway."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Mechanical and Aerospace Engineering, University of California San Diego, La Jolla, CA, USA. ;Department of Molecular Biology, University of California San Diego, La Jolla, CA, USA."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Numerical Analysis and Scientific Computing, Simula Research Laboratory, Oslo, Norway.  . ;K. G. <PERSON>en Centre for Brain Fluid Research, University of Oslo, Oslo, Norway"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Pharmacology, University of California San Diego School of Medicine, La Jolla, CA, USA.  . ;Department of Mechanical and Aerospace Engineering, University of California San Diego, La Jolla, CA, USA"}], "References": [{"Title": "Fast tetrahedral meshing in the wild", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "", "JournalTitle": "ACM Transactions on Graphics"}, {"Title": "Abstractions and Automated Algorithms for Mixed Domain Finite Element Methods", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Mathematical Software"}]}, {"ArticleId": 120002215, "Title": "POE-Net: EXPAND-LAPLACIAN ATTENTION NETWORK FOR LARGE-SCALE PLACE RECOGNITION IN POINT CLOUD", "Abstract": "", "Keywords": "Point cloud retrieval; simultaneous localisation and mapping (SLAM); loop closure; place recognition", "DOI": "10.2316/J.2025.206-1142", "PubYear": 2025, "Volume": "40", "Issue": "", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Bongrae Park", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "Zhibo Wan", "Affiliation": ""}], "References": []}, {"ArticleId": 120002217, "Title": "DEVELOPMENT OF INTELLIGENT SEWING EQUIPMENT BASED ON THE COLLABORATION OF MACHINE VISION AND ROBOT ARM, 133-143.", "Abstract": "", "Keywords": "Machine vision; hand-eye calibration; error compensation; contour extraction; synchronous control; intelligent sewing", "DOI": "10.2316/J.2025.206-1122", "PubYear": 2025, "Volume": "40", "Issue": "2", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Jin", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 120002238, "Title": "AN IMPROVED ILLUMINATION ADAPTIVE ORB-SLAM3 ALGORITHM, 115-123.", "Abstract": "", "Keywords": "ORB-SLAM3; visual SLAM; feature extraction; adaptive threshold; illumination", "DOI": "10.2316/J.2025.206-1111", "PubYear": 2025, "Volume": "40", "Issue": "2", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 120002243, "Title": "ST<PERSON><PERSON><PERSON>Y ANALYSIS AND OPTIMI<PERSON><PERSON><PERSON> OF GRASSHOPPER HOPPING ROBOT WITH DAMPING SYSTEM, 1-14.", "Abstract": "", "Keywords": "Grasshopper hopping robot; landing stability; damping system; zero moment point; response surface method; multi-island genetic algorithm", "DOI": "10.2316/J.2025.206-0890", "PubYear": 2025, "Volume": "40", "Issue": "1", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 120002279, "Title": "DESIGN AND OP<PERSON><PERSON>SATION OF THE DAMPING SYSTEM FOR OPTICAL SCANNING EQUIPMENT", "Abstract": "", "Keywords": "Optical device; collision safety; collision absorption", "DOI": "10.2316/J.2025.206-1118", "PubYear": 2025, "Volume": "40", "Issue": "", "JournalId": 18562, "JournalTitle": "International Journal of Robotics and Automation", "ISSN": "0826-8185", "EISSN": "1925-7090", "Authors": [{"AuthorId": 1, "Name": "<PERSON>hat <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Hai Tran", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 120002396, "Title": "Mixed-Level Modeling and Evaluation of a Cache-less Grid of Processing Cells", "Abstract": "<p>Modern processors experience memory contention when the speed of their computational units exceeds the rate at which new data is available to be processed. This phenomenon is well known as the memory wall and is a great challenge in computer engineering. The reason for this phenomenon is the unequal growth rate in memory access speeds compared to processor clock rates. In order to mitigate the memory bottleneck in classic computer architectures, a scalable parallel computing platform called the Grid of Processing Cells (GPC) has been proposed. To evaluate its effectiveness, the GPC is modeled at the instruction-level and functional-level using SystemC TLM-2.0, with a focus on memory contention. Individual GPC cells can be switched between the two abstraction levels. Our mixed-level system model enables fast and accurate simulations. We test multiple streaming applications on the GPC, analyze software-based optimization methods and their effects on the GPC, at both abstraction levels. The performance is then compared against the traditional shared memory processor (SMP) architecture. Experimental results show improved execution times on the GPC primarily due to a large decrease in main memory contention.</p>", "Keywords": "", "DOI": "10.1145/3708988", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 17571, "JournalTitle": "ACM Transactions on Embedded Computing Systems", "ISSN": "1539-9087", "EISSN": "1558-3465", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Embedded Computer Systems, University of California Irvine, Irvine, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical Engineering and Computer Science, University of California, Irvine, Irvine, United States;Center for Embedded Computer Systems, University of California, Irvine, Irvine, United States"}], "References": [{"Title": "RISC-V based virtual prototype: An extensible and configurable platform for the system-level", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "101756", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Fast Loosely-Timed Deep Neural Network Models with Accurate Memory Contention", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "23", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}]}, {"ArticleId": 120002433, "Title": "A novel approach to Fuzzy mixed graph structure with application towards trade relations between countries", "Abstract": "<p>Many of the phenomena around us are a combination of directed and undirected relationships between different subjects, which will be more complex despite the existence of multiple relationships between objects. For example, in business relations between countries and social networks, communication is sometimes one-way or two-way. Checking and processing such information is managed in mixed graphs. Previous research has been based on the assumption that there is a general relationship between all vertices in a mixed graph. In this article, a new framework for fuzzy information management is introduced by combining fuzzy mixed graph and graph structure along with mentioning its properties. The concept of connectedness has been considered as one of the topics in this study. In this regard, some of their properties were examined by introducing some basic concepts such as paths, cycles, bridges, and cut vertices. Also, some attributes such as degree, neighborhood, order and size are defined. The results showed that although there existed a close relationship between the fuzzy mixed graph structure and the graph structure, the existence of multiple and distinct relationships between the vertices has created new definitions of concepts. This change can be seen especially in the degree of nodes and neighborhoods. Finally, its application in the field of trade relations between countries is presented.</p>", "Keywords": "Fuzzy mixed graph structure; Fuzzy mixed ; Connectivity", "DOI": "10.1007/s40747-024-01701-y", "PubYear": 2025, "Volume": "11", "Issue": "1", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Science and Technology, Guangzhou University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computing Science and Technology, Guangzhou University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, University of Mazandaran, Babolsar, Iran; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics, Damghan University, Damghan, Iran"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Mathematics, University of Mazandaran, Babolsar, Iran"}], "References": [{"Title": "Modified artificial bee colony algorithm for solving mixed interval-valued fuzzy shortest path problem", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "3", "Page": "1527", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "A Study on Semi-directed Graphs for Social Media Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1034", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Decision-making with q-rung orthopair fuzzy graph structures", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "505", "JournalTitle": "Granular Computing"}, {"Title": "New concepts of inverse fuzzy mixed graphs and its application", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "3", "Page": "549", "JournalTitle": "Granular Computing"}]}, {"ArticleId": 120002463, "Title": "WFIL-NET: image inpainting based on wavelet downsampling and frequency integrated learning module", "Abstract": "<p>The purpose of image inpainting is to restore and fill missing areas, and how to restore delicate and reasonable missing content has always been one key issue. In the past decade, remarkable achievements have been made in image inpainting based on deep learning. However, when faced with large and irregular missing areas, there are still some problems such as semantic inconsistency, blurred edges and artifacts in the inpainted images. To address these problems, this paper proposes a novel image inpainting algorithm WFIL-NET which is based on wavelet downsampling and frequency integrated learning module. The WFIL-NET adopts the generative adversarial network (GAN) structure, where the Encoder–Decoder network is used in the generator part. To retain rich information while reducing the image resolution, we propose to use wavelet downsampling module in the encoder part to enhance the capacity of subsequent operations to learn representative features. Moreover, the wavelet transform extracts image features at different frequency levels: low-frequency information encapsulates the primary content and structure, whereas high-frequency information captures details and texture. The proposed frequency integrated learning module employs the attention mechanism to allocate appropriate weights to high and low frequency information, effectively integrating them to ensure a more coherent structure and semantic consistency in the inpainted image. Experimental results on the CelebA-HQ and Places2 datasets demonstrate that the proposed method effectively fills large and irregular missing areas, significantly enhances the visual quality of inpainted images, and mitigates edge blurring and artifacts.</p>", "Keywords": "Image inpainting; Generative adversarial network; Encoder–decoder network; Wavelet downsampling; Frequency integrated", "DOI": "10.1007/s00530-024-01609-0", "PubYear": 2025, "Volume": "31", "Issue": "1", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Communication and Information Engineering, Shanghai University, Shanghai, China; Shanghai Institute for Advanced Communication and Data Science, Shanghai University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Communication and Information Engineering, Shanghai University, Shanghai, China; Shanghai Institute for Advanced Communication and Data Science, Shanghai University, Shanghai, China; Corresponding author."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Communication and Information Engineering, Shanghai University, Shanghai, China; Shanghai Institute for Advanced Communication and Data Science, Shanghai University, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Communication and Information Engineering, Shanghai University, Shanghai, China; Shanghai Institute for Advanced Communication and Data Science, Shanghai University, Shanghai, China"}], "References": [{"Title": "Haar wavelet downsampling: A simple but effective downsampling module for semantic segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "143", "Issue": "", "Page": "109819", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 120002504, "Title": "The role of trehalose metabolism in plant stress tolerance", "Abstract": "<p><b>BACKGROUND</b>:Trehalose is a nonreducing disaccharide containing two glucose molecules linked through an α,α-1,1-glycosidic bond. This unique chemical structure causes trehalose levels to fluctuate significantly in plants under stress, where it functions as an osmoprotectant, enhancing plant resistance to stress. Previous studies have confirmed that the trehalose synthesis pathway is widely conserved across most plants. However, the protective role of trehalose is limited only to organelles or tissues where the concentration is sufficiently high.</p><p><b>AIM OF REVIEW</b>:In this review, we summarize previous reports on improving plant stress tolerance (drought, cold, heat, salt, pathogen, etc.) by applying trehalose-6-phosphate (T6P) or trehalose and manipulating the expression of trehalose metabolism-related genes. The molecular mechanisms underlying T6P, trehalose, and their related genes that regulate plant stress resistance are reviewed. More progressive studies on the spatiotemporal control of trehalose metabolism will provide a novel tool that allows for the simultaneous enhancement of crop yield and stress tolerance.</p><p><b>KEY SCIENTIFIC CONCEPTS OF REVIEW</b>:We introduce the history of trehalose and discuss the possibility of trehalose and its metabolity-related genes binding to T6P to participate in stress response through unknown signaling pathways. In addition, the effects of trehalose metabolism regulation on plant growth and stress resistance were reviewed, and the molecular mechanism was fully discussed. In particular, we came up with new insights that the molecular mechanism of trehalose metabolism to enhance plant stress resistance in the future and we propose the need to use biotechnology methods to cultivate crops with stress resistance and high yield potential.</p><p>Copyright © 2024. Published by Elsevier B.V.</p>", "Keywords": "Plant stress tolerance;SUCROSE-NON-FERMENTING1-RELATED KINASE1;Trehalose;Trehalose-6-phosphate (T6P);Trehalose-6-phosphate phosphatase (TPP);Trehalose-6-phosphate synthase (TPS)", "DOI": "10.1016/j.jare.2024.12.025", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 1002, "JournalTitle": "Journal of Advanced Research", "ISSN": "2090-1232", "EISSN": "2090-1224", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Forestry and Grasslands, Jilin Provincial Key Laboratory of Tree and Grass Genetics and Breeding, Jilin Agriculture University, Changchun 130118, China; College of Life Science, Northeast Forestry University, Harbin 150040, China; School of Life Sciences, Kim Il Sung University, Pyongyang 999093, People's Republic of Korea."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Life Science, Northeast Forestry University, Harbin 150040, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jilin Province Product Quality Supervision and Inspection Institute, Changchun 130022, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Forestry and Grasslands, Jilin Provincial Key Laboratory of Tree and Grass Genetics and Breeding, Jilin Agriculture University, Changchun 130118, China."}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Forestry and Grasslands, Jilin Provincial Key Laboratory of Tree and Grass Genetics and Breeding, Jilin Agriculture University, Changchun 130118, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Forestry and Grasslands, Jilin Provincial Key Laboratory of Tree and Grass Genetics and Breeding, Jilin Agriculture University, Changchun 130118, China."}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Life Science, Northeast Forestry University, Harbin 150040, China; School of Life Sciences, Kim Il Sung University, Pyongyang 999093, People's Republic of Korea."}, {"AuthorId": 8, "Name": "<PERSON>yang Zhao", "Affiliation": "College of Forestry and Grasslands, Jilin Provincial Key Laboratory of Tree and Grass Genetics and Breeding, Jilin Agriculture University, Changchun 130118, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Forestry and Grasslands, Jilin Provincial Key Laboratory of Tree and Grass Genetics and Breeding, Jilin Agriculture University, Changchun 130118, China"}], "References": []}, {"ArticleId": 120002778, "Title": "Publisher Correction: Reduced-Order Observer-Based Event-Triggered Adaptive Fuzzy Backstepping Control of Uncertain Fractional-Order Strict Feedback Nonlinear Systems", "Abstract": "", "Keywords": "", "DOI": "10.1007/s40815-024-01916-8", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 120002800, "Title": "Detection of ovarian cancer using a methodology with feature extraction and selection with genetic algorithms and machine learning", "Abstract": "<p>Ovarian cancer is one of the most lethal forms of gynecological cancer, mainly due to its diagnosis at advanced stages. This study presents a method to predict ovarian cancer by combining machine learning and feature selection using the genetic algorithm GALGO. The research focuses on creating an optimized predictive model that uses fewer features without data imputation to minimize biases and provide a more accurate representation of clinical data variability and natural characteristics.</p><p>The dataset consists of 309 patients with 47 variables, including demographics, routine blood tests, general chemistry, and tumor markers. 75% of the data are used for feature extraction and training of machine learning models, and 25% are used for blind testing. The GALGO feature selection method is applied to identify the most relevant features, with which three models are built: Support Vector Machine, Random Forest, and Logistic Regression. Each model employed cross-validation with three folds (k-folds=3).</p><p>GALGO selected six relevant features. The machine learning models also achieved competitive AUCs: Logistic Regression had the best performance at 0.9055, while Support Vector Machine and Random Forest scored 0.8616 and 0.8854, respectively.</p><p>The proposed methodology generated a promising model for early detection of ovarian cancer and demonstrated that it is possible to maintain high diagnostic accuracy using a reduced number of features. This reduction decreases the computational complexity and costs associated with laboratory tests and improves the efficiency and speed of diagnosis, making the model more practical and applicable in clinical settings. This approach offers a transparent and clinically relevant alternative to improve early detection of ovarian cancer, facilitating its integration into daily clinical practice.</p>", "Keywords": "Ovarian cancer; Feature selection; Genetic algorithms; Machine learning", "DOI": "10.1007/s13721-024-00497-8", "PubYear": 2025, "Volume": "14", "Issue": "1", "JournalId": 25861, "JournalTitle": "Network Modeling Analysis in Health Informatics and Bioinformatics", "ISSN": "2192-6662", "EISSN": "2192-6670", "Authors": [{"AuthorId": 1, "Name": "Samara Acosta-<PERSON><PERSON><PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico"}, {"AuthorId": 3, "Name": "<PERSON>-<PERSON><PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico; Corresponding author."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico"}, {"AuthorId": 7, "Name": "Hamurabi Gamboa-<PERSON><PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Unidad Académica de Ingeniería Eléctrica, Universidad Autónoma de Zacatecas, Zacatecas, Mexico"}], "References": [{"Title": "A comprehensive survey on support vector machine classification: Applications, challenges and trends", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "408", "Issue": "", "Page": "189", "JournalTitle": "Neurocomputing"}, {"Title": "Galgo: a bi-objective evolutionary meta-heuristic identifies robust transcriptomic classifiers associated with patient outcome across multiple cancer types", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON>; B J <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "20", "Page": "5037", "JournalTitle": "Bioinformatics"}, {"Title": "Improving Recurrence Prediction Accuracy of Ovarian Cancer Using Multi-phase Feature Selection Methodology", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "3", "Page": "206", "JournalTitle": "Applied Artificial Intelligence"}, {"Title": "Feature optimization and identification of ovarian cancer using internet of medical things", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "9", "Page": "e12987", "JournalTitle": "Expert Systems"}, {"Title": "Multivariate feature selection and autoencoder embeddings of ovarian cancer clinical and genetic data", "Authors": "<PERSON>; <PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "206", "Issue": "", "Page": "117865", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Genetic algorithms: theory, genetic operators, solutions, and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "3", "Page": "1245", "JournalTitle": "Evolutionary Intelligence"}]}, {"ArticleId": 120002863, "Title": "Synchronization of fractional order supply chain model and financial model", "Abstract": "A supply chain model's interaction with economic forces can be better understood by integrating a financial model with it. In the present article, a fractional-order financial model is synchronized with the fractional-order supply chain model to understand their interplay. The fractional order supply chain model is used as a drive system, and the fractional order financial model is used as a response system. A nonlinear controller has been designed for the synchronisation of both models. The synchronization of the two dynamical systems is achieved through the application of the Adams-Bashforth-Mo<PERSON> method. The synchronisation of these models offers a deeper understanding of the complex interdependencies and highlights potential opportunities and dangers related to the supply chain in the context of supply chain analytics. The findings of this study have important insights into the area of supply chain management and decision making.", "Keywords": "Synchronisation; supply chain analytics; fractional order; financial system; nonlinear controller; stability analysis", "DOI": "10.3934/mfc.2024050", "PubYear": 2025, "Volume": "8", "Issue": "4", "JournalId": 56257, "JournalTitle": "Mathematical Foundations of Computing", "ISSN": "2577-8838", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Alliance School of Business, Alliance University, Bengaluru, 560102, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dept. of Applied Science, <PERSON><PERSON><PERSON>apeeth College of Engineering, New Delhi-110063, India"}], "References": []}, {"ArticleId": 120002901, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0304-3975(24)00659-5", "PubYear": 2025, "Volume": "1026", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 120003313, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1084-8045(24)00276-5", "PubYear": 2025, "Volume": "234", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [], "References": []}, {"ArticleId": 120003337, "Title": "Longitudinal analysis of sitting time and impact on wellbeing and quality-of-life of sedentary workers", "Abstract": "<p>Sitting time (ST) in the occupational domain has been linked to reduced wellbeing and quality-of-life. However, studies investigating the impact of reducing ST in these outcomes are scarce. An ancillary analysis using data from a RCT containing a 6-month sit-stand desk-based intervention, evaluated workers' changes in ST (objectively measured), and subjective wellbeing and quality-of-life. Two groups were created based on changes in ST. Independent and paired-sample T-tests were used to evaluate the differences between and within groups, and analysis of covariance (ANCOVA) was conducted to evaluate the effects of ST reduction in the outcomes. Our analyses indicated that 13 participants out of 38 reduced ST (-72min/day [±40.0; <i>p</i> < 0.001], while 25 participants slightly reduced or increased ST (+36.7 min/day [±40.3; <i>p</i> < 0.001]). Both groups had an improvement in quality-of-life scores, but only those who reduced ST improved subjective wellbeing (<i>p</i> = 0.028). Despite these results, no time*group interaction was found.</p>", "Keywords": "Sedentary behaviour;intervention;quality-of-life;wellbeing;work-related", "DOI": "10.1080/00140139.2024.2441451", "PubYear": 2024, "Volume": "", "Issue": "", "JournalId": 4650, "JournalTitle": "Ergonomics", "ISSN": "0014-0139", "EISSN": "1366-5847", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CIDEFES, Faculdade de Educação Física e Desporto, Universidade Lusófona, Lisboa, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CIDEFES, Faculdade de Educação Física e Desporto, Universidade Lusófona, Lisboa, Portugal;Programa Nacional para a Promoção da Atividade Física, Direção-Geral da Saúde, Lisboa, Portugal;CIFI2D Universidade do Porto, Porto, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CIDEFES, Faculdade de Educação Física e Desporto, Universidade Lusófona, Lisboa, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "CIDEFES, Faculdade de Educação Física e Desporto, Universidade Lusófona, Lisboa, Portugal;CIFI2D Universidade do Porto, Porto, Portugal"}], "References": []}, {"ArticleId": 120003395, "Title": "Neural Ordinary Differential Equations for Robust Parameter Estimation in Dynamic Systems with Physical Priors", "Abstract": "This study introduces a novel parameter estimation method based on Neural Ordinary Differential Equations (Neural ODE). The method addresses the challenges of limited data and noise interference in dynamic system modeling.By integrating neural networks with physical prior knowledge, the framework encodes the system's physical parameters as neural network parameters. This approach enables end-to-end learning from limited observational data. In numerical experiments on a damped oscillator system with Gaussian noise of standard deviation 0.2, the method estimates system parameters with relative errors below 2 %. For the complex nonlinear Lorenz system, it estimates key parameters σ, ρ, and β with relative errors of 0.1 %, 0.4 %, and 1.17 %, respectively. These results significantly outperform traditional methods.The numerical experiments demonstrate that the method provides accurate parameter estimation and effective system dynamics reconstruction. It performs well under small sample sizes and significant noise conditions.", "Keywords": "", "DOI": "10.1016/j.asoc.2024.112649", "PubYear": 2025, "Volume": "169", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Science, Inner Mongolia University of Technology, Hohhot, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Science, Inner Mongolia University of Technology, Hohhot, China;Corresponding author.;ORCID: , https://orcid.org/0000-0002-5363-5670"}], "References": [{"Title": "Real-time prediction of COVID-19 patients health situations using Artificial Neural Networks and Fuzzy Interval Mathematical modeling", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107643", "JournalTitle": "Applied Soft Computing"}, {"Title": "Neural ordinary differential gray algorithm to forecasting nonlinear systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "173", "Issue": "", "Page": "103199", "JournalTitle": "Advances in Engineering Software"}, {"Title": "An optimal neural network design for fractional deep learning of logistic growth", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "15", "Page": "10837", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 120003397, "Title": "TRADE-5G: A Blockchain-Based Transparent and Secure Resource Exchange for 5G Network Slicing", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.bcra.2024.100246", "PubYear": 2025, "Volume": "6", "Issue": "1", "JournalId": 83117, "JournalTitle": "Blockchain: Research and Applications", "ISSN": "2096-7209", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Khaldoun Al Agha", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "5G network slicing using SDN and NFV: A survey of taxonomy, architectures and future challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "106984", "JournalTitle": "Computer Networks"}, {"Title": "Revenue-maximizing virtualized network function chain placement in dynamic environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "108", "Issue": "", "Page": "650", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Economic feasibility of virtual operators in 5G via network slicing", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "172", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Blockchain for 5G and beyond networks: A state of the art survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "166", "Issue": "", "Page": "102693", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Anonymity on blockchain based e-cash protocols—A survey", "Authors": "<PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100394", "JournalTitle": "Computer Science Review"}, {"Title": "Towards end-to-end application slicing in Multi-access Edge Computing systems: Architecture discussion and proof-of-concept", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "136", "Issue": "", "Page": "110", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Tree-ORAP: A Tree-Based Oblivious Random-Access Protocol for Privacy-Protected Blockchain", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "17", "Issue": "3", "Page": "1252", "JournalTitle": "IEEE Transactions on Services Computing"}]}]