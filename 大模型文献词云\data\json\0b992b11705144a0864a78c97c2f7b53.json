[{"ArticleId": 96379762, "Title": "Visual Saliency and Quality Evaluation for 3D Point Clouds and Meshes: An Overview", "Abstract": "Three-dimensional (3D) point clouds (PCs) and meshes have increasingly become available and indispensable for diversified applications in work and life. In addition, 3D visual data contain information from any viewpoint when needed, introducing new challenges and opportunities. As in the cases of 2D images and videos, computationally modeling saliency and quality for 3D PCs and meshes are important for widespread, economical adaption and optimization. This paper aims to provide a comprehensive overview of the related signal presentation and existing saliency and quality models, with major perspectives from the ultimate users (i.e., humans or machines), modeling methodology (with handcrafted features or machine learning), and modeling scope (generic or utility-oriented models). Possible future research directions are also discussed. © 2022 W. Lin and S. Lee.", "Keywords": "3D visual data; handcrafted features; human uses; keypoints; learning-based modeling; machine uses; meshes; metaverse; Point clouds; quality; quality of experience (QoE); saliency; utility-oriented evaluation", "DOI": "10.1561/116.00000125", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 10204, "JournalTitle": "APSIPA Transactions on Signal and Information Processing", "ISSN": "", "EISSN": "2048-7703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanyang Technological University, Singapore"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Electronic Engineering, Yonsei University, Korea"}], "References": []}, {"ArticleId": 96379840, "Title": "Hitting Sets Give Two-Sided Derandomization of Small Space", "Abstract": "A hitting set is a \"one-sided\" variant of a pseudorandom generator (PRG), naturally suited to derandomizing algorithms that have one-sided error. We study the problem of using a given hitting set to derandomize algorithms that have two-sided error, focusing on space-bounded algorithms. For our first result, we show that if there is a log-space hitting set for polynomial-width read-once branching programs (ROBPs), then not only does L = RL hold, but L = BPL as well. This answers a question raised by <PERSON><PERSON> and <PERSON> (SICOMP 2020). Next, we consider constant-width ROBPs. We show that if there are log-space hitting sets for constant-width ROBPs, then given black-box access to a constant-width ROBP /, it is possible to deterministically estimate E[f] to within ±∈ in space 0(log(n/∈)). Unconditionally, we give a deterministic algorithm for this problem with space complexity О (log2 n + log(l /∈)), slightly improving over previous work. Finally, we investigate the limits of this line of work. Perhaps the strongest reduction along these lines one could hope for would say that for every explicit hitting set generator, there is an explicit PRG with similar parameters. In the setting of constant-width ROBPs over a large alphabet, we prove that establishing such a strong reduction is at least as difficult as constructing a good PRG outright. Quantitatively, we prove that if the strong reduction holds, then for every constant α 0, there is an explicit PRG for constant-width ROBPs with seed length О(log1+α n). Along the way, unconditionally, we construct an improved hitting set for ROBPs over a large alphabet. © 2022 <PERSON><PERSON> and <PERSON>. <PERSON>za.", "Keywords": "derandomization; pseudorandomness; hitting set; branching programs; space complexity", "DOI": "10.4086/toc.2022.v018a021", "PubYear": 2022, "Volume": "18", "Issue": "1", "JournalId": 34450, "JournalTitle": "Theory of Computing", "ISSN": "", "EISSN": "1557-2862", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Center on Frontiers of Computing Studies, Peking University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Simons Institute for the Theory of Computing, University of California, Berkeley, CA, United States"}], "References": []}, {"ArticleId": 96379893, "Title": "Is International Qualified Teacher Status (iQTS) A Solution to the Growing Demand for Teachers Globally?", "Abstract": "<p>Is International Qualified Teacher Status (iQTS) A Solution to the Growing Demand for Teachers Globally?</p>", "Keywords": "", "DOI": "10.5750/tbje.v3i1.2060", "PubYear": 2022, "Volume": "3", "Issue": "1", "JournalId": 79347, "JournalTitle": "The Buckingham Journal of Education", "ISSN": "2633-4909", "EISSN": "2633-4933", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96379970, "Title": "Motion Planning Under Uncertainty with Complex Agents and Environments via Hybrid Search", "Abstract": "As autonomous systems and robots are applied to more real world situations, they must reason about uncertainty when planning actions. Mission success oftentimes cannot be guaranteed and the planner must reason about the probability of failure. Unfortunately, computing a trajectory that satisfies mission goals while constraining the probability of failure is difficult because of the need to reason about complex, multidimensional probability distributions. Recent methods have seen success using chance-constrained, model-based planning. However, the majority of these methods can only handle simple environment and agent models. We argue that there are two main drawbacks of current approaches to goal-directed motion planning under uncertainty. First, current methods suffer from an inability to deal with expressive environment models such as 3D non-convex obstacles. Second, most planners rely on considerable simplifications when computing trajectory risk including approximating the agent’s dynamics, geometry, and uncertainty. In this article, we apply hybrid search to the risk-bound, goal-directed planning problem. The hybrid search consists of a region planner and a trajectory planner. The region planner makes discrete choices by reasoning about geometric regions that the autonomous agent should visit in order to accomplish its mission. In formulating the region planner, we propose landmark regions that help produce obstacle-free paths. The region planner passes paths through the environment to a trajectory planner; the task of the trajectory planner is to optimize trajectories that respect the agent’s dynamics and the user’s desired risk of mission failure. We discuss three approaches to modeling trajectory risk: a CDF-based approach, a sampling-based collocation method, and an algorithm named Shooting Method Monte Carlo. These models allow computation of trajectory risk with more complex environments, agent dynamics, geometries, and models of uncertainty than past approaches. A variety of 2D and 3D test cases are presented including a linear case, a Dubins car model, and an underwater autonomous vehicle. The method is shown to outperform other methods in terms of speed and utility of the solution. Additionally, the models of trajectory risk are shown to better approximate risk in simulation. © 2022 AI Access Foundation. All rights reserved.", "Keywords": "autonomous agents;robotics;uncertainty", "DOI": "10.1613/jair.1.13361", "PubYear": 2022, "Volume": "75", "Issue": "", "JournalId": 56494, "JournalTitle": "Journal of Artificial Intelligence Research", "ISSN": "", "EISSN": "1076-9757", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, 77 Massachusetts Avenue, Cambridge, MA  02139, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Massachusetts Institute of Technology, 77 Massachusetts Avenue, Cambridge, MA  02139, United States"}], "References": []}, {"ArticleId": 96379972, "Title": "Dealing with the Heterogeneity of Interpersonal Relationships in the Middle Ages. A Multi-Layer Network Approach", "Abstract": "<p>Investigating the case of the Investiture Struggle in the diocese of Cambrai–Arras (c. 1100), this article aims at exploring some crucial issues for historians using social network analysis in the study of heterogeneous relationships. The study proceeds along three lines of enquiry. First, by establishing a hierarchy in the different types of relationships mentioned in the sources, it determines which of them are the most important to model and understand the structure of the network. Second, it demonstrates it is unnecessary to consider co-witnessing relationships (i.e. to be witnesses of a same charter) in the modelling of networks. Indeed, co-witnessing relationships do not help to improve our understanding of the structure of the parties at stake in a conflict. Finally, this paper deals with the importance of rank order in the witness lists. It demonstrates that, in the case of Cambrai, rank order does not have an influence on the global structure of the network. In other words, all individuals in the same witness list play a similar role in the network in terms of party structuring.</p>", "Keywords": "social network analysis; methodological issues; political history; medieval charters; witness lists", "DOI": "10.16995/dm.8070", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 37830, "JournalTitle": "Digital Medievalist", "ISSN": "", "EISSN": "1715-0736", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université libre de Bruxelles"}, {"AuthorId": 2, "Name": "<PERSON>-<PERSON><PERSON><PERSON>", "Affiliation": "Université catholique de Louvain and UNamur"}, {"AuthorId": 3, "Name": "Étienne <PERSON>", "Affiliation": "ICHEC Brussels management school"}], "References": [{"Title": "Exploring dynamic multilayer graphs for digital humanities", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Applied Network Science"}]}, {"ArticleId": 96379976, "Title": "The first global soil moisture and vegetation optical depth product retrieved from fused SMOS and SMAP L-band observations", "Abstract": "ESA&#x27;s Soil Moisture Ocean Salinity (SMOS, since 2009) and NASA&#x27;s Soil Moisture Active Passive (SMAP, since 2015) are the only two space-borne L-band radiometer missions currently in orbit, which provide key information on global surface soil moisture (SM) and vegetation water content (via the vegetation optical depth, VOD). However, to date very few studies considered merging SMOS and SMAP for both SM and VOD retrievals simultaneously. This study presents the first global long-term and continuous SM and L-band VOD (L-VOD) dataset retrieved after merging the SMOS and SMAP brightness temperature (TB) observations, called the SMOS-SMAP-INRAE-BORDEAUX or SMOSMAP-IB product. We first developed a fitted SMOS TB dataset at a fixed incidence angle of 40°, and next applied a monthly linear rescaling of SMAP TB to SMOS TB for each polarization to produce a merged SMOS/SMAP TB (θ = 40°) dataset. The retrievals were then based on a mono-angular retrieval algorithm sharing a similar forward model with the SMOS-IC and the official SMOS retrieval algorithms. Results showed that the inter-calibration approach we used here could effectively remove the bias between the SMAP TB and fitted SMOS TB, with bias values reduced to 0.01 K (−0.02 K) compared to 3.45 K (1.65 K) for V (H) polarization before inter-calibration. The SMOSMAP-IB SM and L-VOD retrievals based on this new inter-calibrated SMOS/SMAP TB led to metrics that were equally good or better than those of other products (i.e., ESA CCI, SMOS-IC and the official SMAP products). When considering only long duration products, SMOSMAP-IB SM retrievals exhibited (i) the highest overall median R value of 0.72 with in-situ data from ISMN (International Soil Moisture Network) during 2013–2018, followed by SMOS-IC ( R  = 0.68) and CCI ( R  = 0.67), and (ii) the same smallest ub RMSD values as CCI ( ub RMSD = 0.057 m<sup>3</sup>/m<sup>3</sup> vs 0.061 m<sup>3</sup>/m<sup>3</sup> for SMOS-IC). L-VOD retrievals from SMOSMAP-IB were found to have comparable spatial and temporal skills to SMOS-IC. Spatially, they both correlated well with aboveground biomass ( R  = 0.87), and temporally, they both showed a good representation of the short vegetation NDVI signal and of the forest area loss in the Brazilian Amazon from 2011 to 2019. Developing SMOSMAP-IB is a step forward towards building a time-continuous L-band SM and VOD products in response to the possible failure of one of the SMOS or SMAP sensors in the future.", "Keywords": "SMOS ; SMAP ; SMAP-IB ; L-band ; Merging ; Soil moisture ; Vegetation optical depth", "DOI": "10.1016/j.rse.2022.113272", "PubYear": 2022, "Volume": "282", "Issue": "", "JournalId": 384, "JournalTitle": "Remote Sensing of Environment", "ISSN": "0034-4257", "EISSN": "1879-0704", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "INRAE, UMR 1391 ISPA, Université de Bordeaux, F-33140 Villenave d'Ornon, France"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "INRAE, UMR 1391 ISPA, Université de Bordeaux, F-33140 Villenave d'Ornon, France;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "INRAE, UMR 1391 ISPA, Université de Bordeaux, F-33140 Villenave d'Ornon, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Earth and Environmental Sciences, KU Leuven, Heverlee B-3001, Belgium"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Jinfo Mountain Karst Ecosystem National Observation and Research Station, School of Geographical Sciences, Southwest University, Chongqing 400715, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Agroecosystem Sustainability Center, Institute for Sustainability, Energy, and Environment, University of Illinois at Urbana-Champaign, Urbana, IL 61801, USA"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Earth Surface Processes of the Ministry of Education, Institute of Ecology, College of Urban and Environmental Sciences, Peking University, Beijing 100871, China"}, {"AuthorId": 9, "Name": "Honglian<PERSON> Ma", "Affiliation": "State Key Laboratory of Information Engineering in Surveying, Mapping, and Remote Sensing, Wuhan University, Wuhan 430079, China"}, {"AuthorId": 10, "Name": "Zhiqing Peng", "Affiliation": "State Key Laboratory of Remote Sensing Science, Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100101, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "INRAE, UMR 1391 ISPA, Université de Bordeaux, F-33140 Villenave d'Ornon, France"}, {"AuthorId": 12, "Name": "<PERSON><PERSON>", "Affiliation": "INRAE, UMR 1391 ISPA, Université de Bordeaux, F-33140 Villenave d'Ornon, France;Key Laboratory for Earth Surface Processes of the Ministry of Education, Institute of Ecology, College of Urban and Environmental Sciences, Peking University, Beijing 100871, China"}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Remote Sensing Science, Faculty of Geographical Science, Beijing Normal University, Beijing 100875, China"}, {"AuthorId": 14, "Name": "<PERSON>", "Affiliation": "INRAE, UMR 1391 ISPA, Université de Bordeaux, F-33140 Villenave d'Ornon, France"}, {"AuthorId": 15, "Name": "<PERSON>", "Affiliation": "Laboratoire des Sciences du Climat et de l'Environnement, CEA/CNRS/UVSQ/Université Paris Saclay, Gif-sur-Yvette, France"}], "References": [{"Title": "A comparison of SMAP and SMOS L-band brightness temperature observations over the global landmass", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "41", "Issue": "2", "Page": "399", "JournalTitle": "International Journal of Remote Sensing"}, {"Title": "Compared performances of SMOS-IC soil moisture and vegetation optical depth retrievals based on Tau-Omega and Two-Stream microwave emission models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "236", "Issue": "", "Page": "111502", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "SMOS brightness temperature forward modelling and long term monitoring at ECMWF", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "237", "Issue": "", "Page": "111424", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Soil moisture experiment in the Luan River supporting new satellite mission opportunities", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON> Shi; Liqing Lv", "PubYear": 2020, "Volume": "240", "Issue": "", "Page": "111680", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Comparison of microwave remote sensing and land surface modeling for surface soil moisture climatology estimation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "242", "Issue": "", "Page": "111756", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Validation practices for satellite soil moisture retrievals: What are (the) errors?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "244", "Issue": "", "Page": "111806", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A global near-real-time soil moisture index monitor for food security using integrated SMOS and SMAP", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "246", "Issue": "", "Page": "111864", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Global-scale assessment and inter-comparison of recently developed/reprocessed microwave satellite vegetation optical depth products", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "253", "Issue": "", "Page": "112208", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "SMOS-IC data record of soil moisture and L-VOD: Historical development, applications and perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "254", "Issue": "", "Page": "112238", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Uncertainty analysis of eleven multisource soil moisture products in the third pole environment based on the three-corned hat method", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "255", "Issue": "", "Page": "112225", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Retrievals of soil moisture and vegetation optical depth using a multi-channel collaborative algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "257", "Issue": "", "Page": "112321", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "An alternative AMSR2 vegetation optical depth for monitoring vegetation at large scales", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "263", "Issue": "", "Page": "112556", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "ASCAT IB: A radar-based vegetation optical depth retrieved from the ASCAT scatterometer satellite", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112587", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Evaluation of six satellite- and model-based surface soil temperature datasets using global ground-based observations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "264", "Issue": "", "Page": "112605", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A first assessment of satellite and reanalysis estimates of surface and root-zone soil moisture over the permafrost region of Qinghai-Tibet Plateau", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "265", "Issue": "", "Page": "112666", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Assessment of 24 soil moisture datasets using a new in situ network in the Shandian River Basin of China", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "112891", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A new SMAP soil moisture and vegetation optical depth product (SMAP-IB): Algorithm, assessment and inter-comparison", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "271", "Issue": "", "Page": "112921", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "Large loss and rapid recovery of vegetation cover and aboveground biomass over forest areas in Australia during 2019–2020", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "278", "Issue": "", "Page": "113087", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A multi-temporal and multi-angular approach for systematically retrieving soil moisture and vegetation optical depth from SMOS data", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "280", "Issue": "", "Page": "113190", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 96379977, "Title": "Distortion Evaluation of EMP Sensors Using Associated-Hermite Functions", "Abstract": "Electromagnetic pulse (EMP) is a kind of transient electromagnetic phenomenon with short rise time of the leading edge and wide spectrum, which usually disrupts communications and damages electronic equipment and system. It is challenging for an EMP sensor to measure a wideband electromagnetic pulse without distortion for the whole spectrum. Therefore, analyzing the distortion of EMP measurement is crucial to evaluating the sensor distortion characteristics and correcting the measurement results. Waveform fidelity is usually employed to evaluate the distortion of an antenna. However, this metric depends on specific signal waveforms, thus is unsuitable for evaluating and analyzing the distortion of EMP sensors. In this paper, an associated-hermite-function based distortion analysis method including system transfer matrices and distortion rates is proposed, which is general and independent from individual waveforms. The system transfer matrix and distortion rate can be straightforwardly calculated by the signal orthogonal transformation coefficients using associated-hermite functions. Distortion of a sensor vs. frequency is then visualized via the system transfer matrix, which is convenient in quantitative analysis of the distortion. Measurement of a current probe, a coaxial pulse voltage probe and a B-field sensor were performed, based on which the feasibility and effectiveness of the proposed distortion analysis method is successfully verified. © 2023 Tech Science Press. All rights reserved.", "Keywords": "associated-hermite functions; distortion rate; Electromagnetic pulse; system transfer matrix", "DOI": "10.32604/cmc.2023.030979", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Command and Control Engineering, Army Engineering University, Nanjing, 210007, China, Department of Computer Information and Cyber Security, Jiangsu Police Institute, Nanjing, 210031, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, National University of Singapore119260, Singapore"}], "References": [{"Title": "An Intelligent Tumors Coding Method Based on Drools", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "3", "Page": "111", "JournalTitle": "Journal of New Media"}, {"Title": "A Real-time Cutting Model Based on Finite Element and Order Reduction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "1", "Page": "1", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 96379978, "Title": "A Deep Learning Approach for Detecting Covid-19 Using the Chest X-Ray營mages", "Abstract": "Real-time detection of Covid-19 has definitely been the most widely-used world-wide classification problem since the start of the pandemic from 2020 until now. In the meantime, airspace opacities spreads related to lung have been of the most challenging problems in this area. A common approach to do on that score has been using chest X-ray images to better diagnose positive Covid-19 cases. Similar to most other classification problems, machine learning-based approaches have been the first/most-used candidates in this application. Many schemes based on machine/deep learning have been proposed in recent years though increasing the performance and accuracy of the system has still remained an open issue. In this paper, we develop a novel deep learning architecture to better classify the Covid-19 X-ray images. To do so, we first propose a novel multi-habitat migration artificial bee colony (MHMABC) algorithm to improve the exploitation/exploration of artificial bee colony (ABC) algorithm. After that, we optimally train the fully connected by using the proposed MHMABC algorithm to obtain better accuracy and convergence rate while reducing the execution cost. Our experiment results on Covid-19 X-ray image dataset show that the proposed deep architecture has a great performance in different important optimization parameters. Furthermore, it will be shown that the MHMABC algorithm outperforms the state-of-the-art algorithms by evaluating its performance using some well-known benchmark datasets. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Chest X-ray image processing; covid-19; evolutionary deep learning", "DOI": "10.32604/cmc.2023.031519", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Sharif University of Technology, Tehran, 14588-89694, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, University of Houston, Houston, TX  77204, United States"}, {"AuthorId": 3, "Name": "Myung-Kyu Yi", "Affiliation": "Department of Computer Engineering, Gachon University, Seongnam, 13120, South Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Intelligent firefly-based algorithm with Levy distribution (FF-L) for multicast routing in vehicular communications", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "112889", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Electrocardiogram soft computing using hybrid deep learning CNN-ELM", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105778", "JournalTitle": "Applied Soft Computing"}, {"Title": "Parameters Compressing in Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "1", "Page": "321", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Chimp optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113338", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Semantic-k-NN algorithm: An enhanced version of traditional k-NN algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "113374", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel meta-heuristic search algorithm for solving optimization problems: capuchin search algorithm", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2515", "JournalTitle": "Neural Computing and Applications"}, {"Title": "COVIDetectioNet: COVID-19 diagnosis system based on X-ray images using features selected from pre-learned deep features ensemble", "Authors": "<PERSON><PERSON><PERSON>lu; Turk<PERSON>lu, Muammer", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1213", "JournalTitle": "Applied Intelligence"}, {"Title": "Woodland Labeling in Chenzhou, China, via Deep Learning Approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "1393", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Using CFW-Net Deep Learning Models for X-Ray Images to Detect COVID-19 Patients", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "199", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Small Object Detection via Precise Region-Based Fully Convolutional Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "2", "Page": "1503", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Dynamic Levy Flight Chimp Optimization", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "235", "Issue": "", "Page": "107625", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A Robust 3-D Medical Watermarking Based on Wavelet Transform for Data Protection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "3", "Page": "1043", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Deformation Expression of Soft Tissue Based on BP Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "1041", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "X-ray image based COVID-19 detection using evolutionary deep learning approach", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "201", "Issue": "", "Page": "116942", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 96379984, "Title": "Fuzzy Firefly Based Intelligent Algorithm for Load Balancing in Mobile Cloud Computing", "Abstract": "This paper presents a novel fuzzy firefly-based intelligent algorithm for load balancing in mobile cloud computing while reducing makespan. The proposed technique implicitly acts intelligently by using inherent traits of fuzzy and firefly. It automatically adjusts its behavior or converges depending on the information gathered during the search process and objective function. It works for 3-tier architecture, including cloudlet and public cloud. As cloudlets have limited resources, fuzzy logic is used for cloudlet selection using capacity and waiting time as input. Fuzzy provides human-like decisions without using any mathematical model. Firefly is a powerful meta-heuristic optimization technique to balance diversification and solution speed. It balances the load on cloud and cloudlet while minimizing makespan and execution time. However, it may trap in local optimum; levy flight can handle it. Hybridization of fuzzy firefly with levy flight is a novel technique that provides reduced makespan, execution time, and Degree of imbalance while balancing the load. Simulation has been carried out on the Cloud Analyst platform with National Aeronautics and Space Administration (NASA) and Clarknet datasets. Results show that the proposed algorithm outperforms Ant Colony Optimization Queue Decision Maker (ACOQDM), Distributed Scheduling Optimization Algorithm (DSOA), and Utility-based Firefly Algorithm (UFA) when compared in terms of makespan, Degree of imbalance, and Figure of Merit. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Cloud computing; cloudlet; degree of imbalance; firefly; fuzzy; load balancing; makespan; mobile cloud computing", "DOI": "10.32604/cmc.2023.031729", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": " Poonam", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Ram University of Science and Technology, Sonepat, Murthal, 131039, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CSE Department, Deenbandhu Chhotu Ram University of Science and Technology, Sonepat, Murthal, 131039, India"}], "References": [{"Title": "Utility based load balancing using firefly algorithm in cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "215", "JournalTitle": "Journal of Data, Information and Management"}, {"Title": "An improved firefly algorithm for global continuous optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "113340", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimal Resource Allocation and Quality of Service Prediction in Cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "1", "Page": "253", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Heuristics-Based Cost Model for Scientific Workflow Scheduling in Cloud", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "3265", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Hybrid Cuckoo Search Algorithm for Scheduling in Cloud Computing", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>; S<PERSON>醠ovsk� P. Trojovsk� P. Prabu", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "1641", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96379986, "Title": "Robust Fingerprint Construction Based on Multiple Path Loss Model (M-PLM) for Indoor Localization", "Abstract": "A robust radio map is essential in implementing a fingerprint-based indoor positioning system (IPS). However, the offline site survey to manually construct the radio map is time-consuming and labour-intensive. Various interpolation techniques have been proposed to infer the virtual fingerprints to reduce the time and effort required for offline site surveys. This paper presents a novel fingerprint interpolator using a multi-path loss model (M-PLM) to create the virtual fingerprints from the collected sample data based on different signal paths from different access points (APs). Based on the historical signal data, the poor signal paths are identified using their standard deviations. The proposed method reduces the positioning errors by smoothing out the wireless signal fluctuations and stabilizing the signals for those poor signal paths. By considering multipath signal propagations from different APs, the inherent noise from these signal paths can be alleviated. Firstly, locations of the signal data with standard deviations higher than the threshold are identified. The new fingerprints are then generated at these locations based on the proposed M-PLM interpolation function to replace the old fingerprints. The proposed technique interpolates virtual fingerprints based on good signal paths with more stable signals to improve the positioning performance. Experimental results show that the proposed scheme enhances the positioning accuracy by up to 44% compared to the conventional interpolation techniques such as the Inverse Distance Weighting, Kriging, and single Path Loss Model. As a result, we can overcome the site survey problems for IPS by building an accurate radio map with more reliable signals to improve indoor positioning performance. © 2023 Tech Science Press. All rights reserved.", "Keywords": "fingerprinting; indoor positioning system; interpolation; Path loss model; radio map", "DOI": "10.32604/cmc.2023.032710", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Fine-grained vehicle type classification using lightweight convolutional neural network with feature optimization and joint learning strategy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "20", "Page": "30803", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A Multi-Feature Learning Model with Enhanced Local Attention for Vehicle Re-Identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3549", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96379987, "Title": "Conformal Marked Bisection for Local Refinement of n -Dimensional Unstructured Simplicial Meshes", "Abstract": "We present an n -dimensional marked bisection method for unstructured conformal meshes. We devise the method for local refinement in adaptive n -dimensional applications. To this end, we propose a mesh marking pre-process and three marked bisection stages. The pre-process marks the initial mesh conformingly. Then, in the first n − 1 bisections, the method accumulates in reverse order a list of new vertices. In the second stage, the n -th bisection, the method uses the reversed list to cast the bisected simplices as reflected simplices, a simplex type suitable for newest vertex bisection. In the final stage, beyond the n -th bisection, the method switches to newest vertex bisection. To allow this switch, after the second stage, we check that under uniform bisection the mesh simplices are conformal and reflected. These conditions are sufficient to use newest vertex bisection, a bisection scheme guaranteeing key advantages for local refinement. Finally, the results show that the proposed bisection is well-suited for local refinement of unstructured conformal meshes.", "Keywords": "Unstructured conformal mesh ; Adaption ; Mesh refinement ; Local bisection ; n -dimensional bisection", "DOI": "10.1016/j.cad.2022.103419", "PubYear": 2023, "Volume": "154", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "Guillem Belda-Ferrín", "Affiliation": "Computer Applications in Science and Engineering, Barcelona Supercomputing Center - BSC, 08034 Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>és", "Affiliation": "Computer Applications in Science and Engineering, Barcelona Supercomputing Center - BSC, 08034 Barcelona, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer Applications in Science and Engineering, Barcelona Supercomputing Center - BSC, 08034 Barcelona, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Applications in Science and Engineering, Barcelona Supercomputing Center - BSC, 08034 Barcelona, Spain;Corresponding author"}], "References": []}, {"ArticleId": 96379991, "Title": "A Two-Phase Paradigm for Joint Entity-Relation Extraction", "Abstract": "An exhaustive study has been conducted to investigate span-based models for the joint entity and relation extraction task. However, these models sample a large number of negative entities and negative relations during the model training, which are essential but result in grossly imbalanced data distributions and in turn cause suboptimal model performance. In order to address the above issues, we propose a two-phase paradigm for the span-based joint entity and relation extraction, which involves classifying the entities and relations in the first phase, and predicting the types of these entities and relations in the second phase. The two-phase paradigm enables our model to significantly reduce the data distribution gap, including the gap between negative entities and other entities, as well as the gap between negative relations and other relations. In addition, we make the first attempt at combining entity type and entity distance as global features, which has proven effective, especially for the relation extraction. Experimental results on several datasets demonstrate that the span-based joint extraction model augmented with the two-phase paradigm and the global features consistently outperforms previous state-of-the-art span-based models for the joint extraction task, establishing a new standard benchmark. Qualitative and quantitative analyses further validate the effectiveness the proposed paradigm and the global features. © 2023 Tech Science Press. All rights reserved.", "Keywords": "data distribution; global features; Joint extraction; named entity recognition; relation extraction; span-based", "DOI": "10.32604/cmc.2023.032168", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, 410073, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "The Affiliated Eye Hospital of Nanjing Medical University, Nanjing, 210029, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer, National University of Defense Technology, Changsha, 410073, China"}], "References": [{"Title": "Joint entity and relation extraction model based on rich semantics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "429", "Issue": "", "Page": "132", "JournalTitle": "Neurocomputing"}, {"Title": "A Knowledge-Enriched and Span-Based Network for Joint Entity and Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "1", "Page": "377", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Lexicalized Dependency Paths Based Supervised Learning for Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "3", "Page": "861", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 96380045, "Title": "Optimization Task Scheduling Using Cooperation Search Algorithm for Heterogeneous Cloud Computing Systems", "Abstract": "Cloud computing has taken over the high-performance distributed computing area, and it currently provides on-demand services and resource polling over the web. As a result of constantly changing user service demand, the task scheduling problem has emerged as a critical analytical topic in cloud computing. The primary goal of scheduling tasks is to distribute tasks to available processors to construct the shortest possible schedule without breaching precedence restrictions. Assignments and schedules of tasks substantially influence system operation in a heterogeneous multiprocessor system. The diverse processes inside the heuristic-based task scheduling method will result in varying makespan in the heterogeneous computing system. As a result, an intelligent scheduling algorithm should efficiently determine the priority of every subtask based on the resources necessary to lower the makespan. This research introduced a novel efficient scheduling task method in cloud computing systems based on the cooperation search algorithm to tackle an essential task and schedule a heterogeneous cloud computing problem. The basic idea of this method is to use the advantages of meta-heuristic algorithms to get the optimal solution. We assess our algorithm’s performance by running it through three scenarios with varying numbers of tasks. The findings demonstrate that the suggested technique beats existing methods New Genetic Algorithm (NGA), Genetic Algorithm (GA), Whale Optimization Algorithm (WOA), Gravitational Search Algorithm (GSA), and Hybrid Heuristic and Genetic (HHG) by 7.9%, 2.1%, 8.8%, 7.7%, 3.4% respectively according to makespan. © 2023 Tech Science Press. All rights reserved.", "Keywords": "cloud computing; cooperation search algorithm; Heterogeneous processors; task scheduling", "DOI": "10.32604/cmc.2023.032215", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Faisal S. Alsubaei", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "RETRACTED ARTICLE: A new whale optimizer for workflow scheduling in cloud computing environment", "Authors": "<PERSON>er <PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "3807", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Cooperation search algorithm: A novel metaheuristic evolutionary intelligence algorithm for numerical optimization and engineering optimization problems", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "98", "Issue": "", "Page": "106734", "JournalTitle": "Applied Soft Computing"}, {"Title": "An Evolutionary Computing-Based Efficient Hybrid Task Scheduling Approach for Heterogeneous Computing Environment", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "19", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Grid Computing"}, {"Title": "Amended hybrid multi-verse optimizer with genetic algorithm for solving task scheduling problem in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "78", "Issue": "1", "Page": "740", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Task Scheduling Optimization in Cloud Computing Based on Genetic Algorithms", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3289", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96380046, "Title": "Motion Analysis and Real‐Time Trajectory Prediction of Magnetically Steerable Catalytic Janus Micromotors", "Abstract": "<p>Chemically driven micromotors display unpredictable trajectories due to the rotational Brownian motion interacting with the surrounding fluid molecules. This hampers the practical applications of these tiny robots, particularly where precise control is a requisite. To overcome the rotational Brownian motion and increase motion directionality, robots are often decorated with a magnetic composition and guided by an external magnetic field. However, despite the straightforward method, explicit analysis and modeling of their motion have been limited. Here, catalytic Janus micromotors are fabricated with distinct magnetizations and a controlled self-propelled motion with magnetic steering is shown. To analyze their dynamic behavior, a dynamic model that can successfully predict the trajectory of micromotors in uniform viscous flows in real time by incorporating a form of state-dependent-coefficient with a robust two-stage Kalman filter is theoretically developed. A good agreement is observed between the theoretically predicted dynamics and experimental observations over a wide range of model parameter variations. The developed model can be universally adopted to various designs of catalytic micro-/nanomotors with different sizes, geometries, and materials, even in diverse fuel solutions. Finally, the proposed model can be used as a platform for biosensing, detecting fuel concentration, or determining small-scale motors’ propulsion mechanisms in an unknown environment.</p>", "Keywords": "bubble recoil propulsion;catalytic swimmers;directionality control;magnetic Janus particles;real-time trajectory prediction", "DOI": "10.1002/aisy.202200192", "PubYear": 2022, "Volume": "4", "Issue": "11", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Multi-Scale Robotics Lab Institute of Robotics and Intelligent Systems ETH Zurich  Tannenstrasse 3 CH 8092 Zurich Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "PRISME Laboratory INSA Centre Val de Loire University of Orleans  EA 4229 Bourges France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Multi-Scale Robotics Lab Institute of Robotics and Intelligent Systems ETH Zurich  Tannenstrasse 3 CH 8092 Zurich Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Robotics Hanyang University  ERICA Campus Ansan-si 15588 Republic of Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON> Chen", "Affiliation": "Multi-Scale Robotics Lab Institute of Robotics and Intelligent Systems ETH Zurich  Tannenstrasse 3 CH 8092 Zurich Switzerland"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Materials ETH Zurich  Honggerbergring 64 CH 8093 Zurich Switzerland"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Materials ETH Zurich  Honggerbergring 64 CH 8093 Zurich Switzerland"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Departament de Física Universitat Autònoma de Barcelona  08193 Bellaterra Spain;Institució Catalana de Recerca i Estudis Avançats (ICREA)  Pg. Lluís Companys 23 08010 Barcelona Spain"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>-<PERSON>", "Affiliation": "Institució Catalana de Recerca i Estudis Avançats (ICREA)  Pg. Lluís Companys 23 08010 Barcelona Spain;Departament de Ciència dels Materials i Química Física Institut de Química Teòrica i Computacional  08028 Barcelona Spain"}, {"AuthorId": 10, "Name": "<PERSON><PERSON>", "Affiliation": "Multi-Scale Robotics Lab Institute of Robotics and Intelligent Systems ETH Zurich  Tannenstrasse 3 CH 8092 Zurich Switzerland"}, {"AuthorId": 11, "Name": "<PERSON>", "Affiliation": "PRISME Laboratory INSA Centre Val de Loire University of Orleans  EA 4229 Bourges France"}, {"AuthorId": 12, "Name": "Salvador Pané", "Affiliation": "Multi-Scale Robotics Lab Institute of Robotics and Intelligent Systems ETH Zurich  Tannenstrasse 3 CH 8092 Zurich Switzerland"}], "References": []}, {"ArticleId": 96380157, "Title": "SLViT: Shuffle-convolution-based lightweight Vision transformer for effective diagnosis of sugarcane leaf diseases", "Abstract": "Farmers must accurately and promptly identify sugarcane leaf diseases with identical symptoms. RGB images have a beneficial function in disease identification. Nevertheless, complex backgrounds and identical symptoms can significantly reduce the recognition accuracy and robustness. To overcome these challenges, the SLViT hybrid network is presented, in which the transformer encoder is converted to a flexible plug-in (LViT) that is subsequently integrated into several locations of a lightweight CNN architecture (SHDC). SLViT is initially trained on the publicly available disease dataset Plant Village before being moved to the self-created sugarcane leaf disease dataset SLD10k, which consists of seven classes and 10,309 images. The ablation experiments demonstrate that all the adjustments to SLViT have contributed positively to its overall performance. SLViT outperforms six SOTA models and three custom-designed leaf-disease recognition models on Plant Village in terms of speed (1,832 FPS), weight (2 MB), consumption (50 M), and precision (98.84 %). SLViT also outperformed MobileNetV3_small on the SLD10k dataset with an accuracy bonus of 1.87 % and a size reduction of 66.3 %. The experiment also reveals that SLViT has absorbed the advantages of both the lightweight CNN and the noise-resistant transformer. This study demonstrates the applicability of SLViT for sugarcane leaf diagnosis in the field.", "Keywords": "Sugarcane leaf disease ; Lightweight model ; CNN ; Vision Transformer ; Plant Village", "DOI": "10.1016/j.jksuci.2022.09.013", "PubYear": 2023, "Volume": "35", "Issue": "6", "JournalId": 687, "JournalTitle": "Journal of King Saud University - Computer and Information Sciences", "ISSN": "1319-1578", "EISSN": "2213-1248", "Authors": [{"AuthorId": 1, "Name": "Xuechen Li", "Affiliation": "School of Electrical Engineering, Guangxi University, Nanning 530004, China;Guangxi Key Laboratory of Sugarcane Biology, Guangxi University, Nanning 530004, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Guangxi University, Nanning 530004, China;Guangxi Key Laboratory of Sugarcane Biology, Guangxi University, Nanning 530004, China;Corresponding author at: Guangxi Key Laboratory of Sugarcane Biology, Guangxi University, Nanning 530004, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering, Guangxi University, Nanning 530004, China;Guangxi Key Laboratory of Sugarcane Biology, Guangxi University, Nanning 530004, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Sugarcane Biology, Guangxi University, Nanning 530004, China;School of Agriculture, Guangxi University, Nanning 530004, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Sugarcane Biology, Guangxi University, Nanning 530004, China;State Key Laboratory for Conservation and Utilization of Subtropical Agro-bioresources, Guangxi University, Nanning 530004, China;School of Agriculture, Guangxi University, Nanning 530004, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Guangxi Key Laboratory of Sugarcane Biology, Guangxi University, Nanning 530004, China;School of Agriculture, Guangxi University, Nanning 530004, China"}], "References": [{"Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "336", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Industry 4.0 and Industry 5.0—Inception, conception and perception", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "530", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 96380407, "Title": "Recursive convex approximations for optimal power flow solution in direct current networks", "Abstract": "<p><p>The optimal power flow problem in direct current (DC) networks considering dispersal generation is addressed in this paper from the recursive programming point of view. The nonlinear programming model is transformed into two quadratic programming approximations that are convex since the power balance constraint is approximated between affine equivalents. These models are recursively (iteratively) solved from the initial point v<sup>t</sup> equal to 1.0 pu with t equal to 0, until that the error between both consecutive voltage iterations reaches the desired convergence criteria. The main advantage of the proposed quadratic programming models is that the global optimum finding is ensured due to the convexity of the solution space around v<sup>t</sup>. Numerical results in the DC version of the IEEE 69-bus system demonstrate the effectiveness and robustness of both proposals when compared with classical metaheuristic approaches such as particle swarm and antlion optimizers, among others. All the numerical validations are carried out in the MATLAB programming environment version 2021b with the software for disciplined convex programming known as CVX tool in conjuction with the Gurobi solver version 9.0; while the metaheuristic optimizers are directly implemented in the MATLAB scripts.</p></p>", "Keywords": "Direct current networks;Metaheuristic optimization techniques;Optimal power flow problem;Quadratic convex programming;Recursive convex approximation", "DOI": "10.11591/ijece.v12i6.pp5674-5682", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>-Toro", "Affiliation": "Insitutución Universitaria Pascual Bravo"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Distrital Francisco José <PERSON>"}, {"AuthorId": 3, "Name": "<PERSON>-Noreña", "Affiliation": "Instituto Tecnológico Metropolitano"}], "References": []}, {"ArticleId": 96380423, "Title": "Analytical transformations software for stationary modes of induction motors and electric drives", "Abstract": "<p> A program was developed in the package of symbolic transformations Maple. It provides automatic analytical transformation and derivation of formulas and plotting of the main characteristics of induction motors (IM) in a convenient form for an electrical engineer and student: torque=f(slip) T=f(s), angular speed=f(Torque) ω=f(T), angular speed=f(Current) ω=f(I), current=f(slip) I=f(s); cos(φ) and phase angle (phi) φ for stator currents and rotor currents, and magnetizing circuit, machine efficiency η=f(s) and a number of other characteristics. The calculation is based on the equivalent circuit of IM motors in its different variants: with one cage in the rotor, with two or more cages in the rotor, taking into account the skin effect in the rotor rods and without it. The user can build up the equivalent circuit to the desired configuration. The algorithm of further transformations is based on analytical obtaining of amplitude/frequency and phase/frequency characteristics in the nodes of the equivalent circuits with further calculation by power and slip. Online animation of the graphs with alternate variations of all resistances R and inductances L values of the model is provided. The article contains screenshots of important parts of the programs and illustrates the complete set of graphs. </p>", "Keywords": "Curve characteristic;Equivalent circuit;Induction motor;Package maple;Skin effect;Two-cage rotor", "DOI": "10.11591/ijece.v12i6.pp5738-5753", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Al-Balqa Applied University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Biotechnological University"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Technical University \"Kharkiv Polytechnic Institute&quot"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Technical University \"Kharkiv Polytechnic Institute&quot"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Technical University \"Kharkiv Polytechnic Institute&quot"}], "References": []}, {"ArticleId": 96380439, "Title": "Achieving Reliable Causal Inference with Data-Mined Variables: A Random Forest Approach to the Measurement Error Problem", "Abstract": "Combining machine learning with econometric analysis is becoming increasingly prevalent in both research and practice. A common empirical strategy uses predictive modeling techniques to “mine” variables of interest from available data and then includes those variables into an econometric framework to estimate causal effects. However, because the predictions from machine learning models are inevitably imperfect, econometric analyses based on the predicted variables likely suffer from bias due to measurement error. We propose a novel approach to mitigate these biases, leveraging the random forest technique. We propose using random forest not just for prediction but also for generating instrumental variables for bias correction. The random forest algorithm performs best when comprised of a set of trees that are individually accurate in their predictions, yet which also make “different” mistakes, that is, have weakly correlated prediction errors. A key observation is that these properties are closely related to the relevance and exclusion requirements of valid instrumental variables. We design a data-driven procedure to select tuples of individual trees from a random forest, in which one tree serves as the endogenous covariate and the others serve as its instruments. Simulation experiments demonstrate its efficacy in mitigating estimation biases and its superior performance over alternative methods. History: <PERSON> served as the senior editor for this article. Data Ethics & Reproducibility Note: The code capsule is available on Code Ocean at https://codeocean.com/capsule/7039927/tree/v1 and in the e-Companion to this article (available at https://doi.org/10.1287/ijds.2022.0019 ).", "Keywords": "machine learning; econometric analysis; instrumental variable; random forest; causal inference", "DOI": "10.1287/ijds.2022.0019", "PubYear": 2022, "Volume": "1", "Issue": "2", "JournalId": 83397, "JournalTitle": "INFORMS Journal on Data Science", "ISSN": "2694-4022", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Decision Sciences, Carlson School of Management, University of Minnesota, Minneapolis, Minnesota 55455"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Technology and Operations Management, Harvard Business School, Boston, Massachusetts 02163"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, Questrom School of Business, Boston University, Boston, Massachusetts 02215"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Decision Sciences, Carlson School of Management, University of Minnesota, Minneapolis, Minnesota 55455"}], "References": []}, {"ArticleId": 96380450, "Title": "Distributed Clustering Approach by Apache Pyspark Based on SEER for Clinical Data", "Abstract": "<p>Data clustering is a thoroughly studied data mining issue. As the amount of information being analyzed grows exponentially, there are several problems with clustering diagnostic large datasets like the monitoring, microbiology, and end results (SEER) carcinoma feature sets. These traditional clustering methods are severely constrained in terms of speed, productivity, and adaptability. This paper summarizes the most modern distributed clustering algorithms, organized according to the computing platforms used to process vast volumes of data. The purpose of this work was to offer an optimized distributed clustering strategy for reducing the algorithm’s total execution time. We obtained, preprocessed, and analyzed clinical SEER data on liver cancer, respiratory cancer, human immunodeficiency virus (HIV)-related lymphoma, and lung cancer for large-scale data clustering analysis. Three major contributions and their effects were covered in this paper: To begin, three current Pyspark distributed clustering algorithms were evaluated on SEER clinical data using a simulated New York cancer dataset. Second, systemic inflammatory response syndrome (SIRS) model inference was done and described using three SEER cancer datasets. Third, employing lung cancer data, we suggested an optimized distributed bisecting [Formula: see text]-means method. We have shown the outcomes of our suggested optimized distributed clustering technique, demonstrating the performance enhancement.</p>", "Keywords": "Computational epidemiology; big data analytics; spark partitions; Apache spark; Hadoop clustering", "DOI": "10.1142/S0218001422400067", "PubYear": 2022, "Volume": "36", "Issue": "16", "JournalId": 9818, "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence", "ISSN": "0218-0014", "EISSN": "1793-6381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Applications, Cochin University of Science and Technology (CUSAT) Cochin, Kerala 682022, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Applications, Cochin University of Science and Technology (CUSAT) Cochin, Kerala 682022, India"}], "References": []}, {"ArticleId": 96380467, "Title": "Design of an Intelligent Network Intrusion Detection Model", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.129216", "PubYear": 2022, "Volume": "12", "Issue": "9", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "宇杰 姚", "Affiliation": ""}], "References": [{"Title": "Network intrusion detection based on IE-DBN model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "131", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 96380541, "Title": "Coupling effect of high‐altitude electromagnetic pulse of two‐pin electric explosive device based on a V‐type antenna model", "Abstract": "<p>Concentrating on the two-pin electric explosive devices (EEDs), this paper proposes a coupling model of a V-type antenna according to the typical scenario that the pin wire can present a certain angle. Based on theoretical analysis, numerical simulations are performed to obtain the different angles of antenna patterns, the changes of the reflection coefficients, the gain of coupling current, and the influence law of coupling energy, which are verified by experimental measurements. The results show that the coupling current and energy increase rapidly with the increase of the angle when the included angle between the two-pin wires exceeds 60°. When the angle reaches 90°, the coupling energy would increase to tens of μJ, which will bring certain safety risks. When the included angle rises to 180°, the coupling energy will reach the peak. The conclusion of this investigation is helpful to the environmental adaptability of high-altitude electromagnetic pulse (HEMP) for EEDs.</p>", "Keywords": "antenna model;coupling effect;electric explosive devices;high-altitude electromagnetic pulse;radiation pattern", "DOI": "10.1002/mmce.23417", "PubYear": 2022, "Volume": "32", "Issue": "12", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechano‐Electronic Engineering Xidian University  Xi'an China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechano‐Electronic Engineering Xidian University  Xi'an China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechano‐Electronic Engineering Xidian University  Xi'an China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechano‐Electronic Engineering Xidian University  Xi'an China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Mechano‐Electronic Engineering Xidian University  Xi'an China"}], "References": []}, {"ArticleId": 96380560, "Title": "Advanced modeling method for quantifying cumulative subjective fatigue in mid-air interaction", "Abstract": "Interaction in mid-air can be fatiguing. A model-based method to quantify cumulative subjective fatigue for such interaction was recently introduced in HCI research. This model separates muscle units into three states: active ( M A ) fatigued ( M F ) or rested ( M R ) and defines transition rules between states. This method demonstrated promising accuracy in predicting subjective fatigue accumulated in mid-air pointing tasks. In this paper, we introduce an improved model that additionally captures the variations of the maximum arm strength based on arm postures and adds linearly-varying model parameters based on current muscle strength. To validate the applicability and capabilities of the new model, we tested its performance in various mid-air interaction conditions, including mid-air pointing/docking tasks, with shorter and longer rest and task periods, and a long-term evaluation with individual participants. We present results from multiple cross-validations and comparisons against the previous model and identify that our new model predicts fatigue more accurately. Our modeling approach showed a 42.5% reduction in fatigue estimation error when the longitudinal experiment data is used for an individual participant’s fatigue. Finally, we discuss the applicability and capabilities of our new approach.", "Keywords": "Mid-air interaction ; Cumulative fatigue model ; Maximum arm strength ; Brain effort", "DOI": "10.1016/j.ijhcs.2022.102931", "PubYear": 2023, "Volume": "169", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, Purdue University, West Lafayette, IN, USA;Correspondence to: School of Mechanical Engineering, 585 Purdue Mall, Purdue University, West Lafayette, IN 47907, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Samsung Advanced Institute of Technology, Suwon-si, Gyeonggi-do, South Korea"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Interactive Arts + Technology, Simon Fraser University, Vancouver, CA, Canada"}, {"AuthorId": 4, "Name": "Satyajit Am<PERSON>ke", "Affiliation": "Department of Health and Kinesiology, Purdue University, West Lafayette, IN, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Purdue University, West Lafayette, IN, USA"}], "References": [{"Title": "Muscle fatigue modelling: Solving for fatigue and recovery parameter values using fewer maximum effort assessments", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103104", "JournalTitle": "International Journal of Industrial Ergonomics"}]}, {"ArticleId": 96380577, "Title": "Textual adversarial attacks by exchanging text‐self words", "Abstract": "<p>Adversarial attacks expose the vulnerability of deep neural networks. Compared to image adversarial attacks, textual adversarial attacks are more challenging due to the discrete nature of texts. Recent synonym-based methods achieve the current state-of-the-art results. However, these methods introduce new words against the original text, leading to that humans easily perceive the difference between the adversarial example and the original text. Motivated by the fact that humans are usually unaware of chaotic word order in some cases, we propose exchange-attack (EA), a concise and effective word-level textual adversarial attack model. Specifically, the EA model generates adversarial examples by exchanging words of the original text itself according to the contributions that these words make regarding classification results. Intuitively, the smaller the distance between the two exchanged words, the more difficult the chaotic word order to be perceived by humans. We thus take the word distance into consideration when generating the chaotic word orders. Extensive experiments on several text classification data sets show that the EA model consistently outperforms the selected baselines in terms of averaged after-attack accuracy, modification rate, query number, and semantic similarity. And human evaluation results reveal that humans difficultly perceive the adversarial examples generated by the EA model. In addition, quantitative and qualitative analyses further validate the effectiveness of the EA model, including that the generated adversarial examples are grammatically correct and semantically preserved.</p>", "Keywords": "adversarial example;exchange-attack;grammatically correct;semantically preserved;target model", "DOI": "10.1002/int.23083", "PubYear": 2022, "Volume": "37", "Issue": "12", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}, {"AuthorId": 6, "Name": "Zibo Yi", "Affiliation": "Information Research Center of Military Science, PLA Academy of Military Science  Beijing China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer National University of Defense Technology  Changsha China"}], "References": [{"Title": "Adversarial Attacks on Deep-learning Models in Natural Language Processing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 96380634, "Title": "Double sliding window variance detection-based time-of-arrival estimation in ultra-wideband ranging systems", "Abstract": "<p> Ultra-wideband (UWB) ranging via time-of-arrival (TOA) estimation method has gained a lot of research interests because it can take full advantage of UWB capabilities. Energy detection (ED) based TOA estimation technique is widely used in the area due to its low cost, low complexity and ease of implementation. However, many factors affect the ranging performance of the ED-based methods, especially, non-line-of-sight (NLOS) condition and the integration interval. In this context, a new TOA estimation method is developed in this paper. Firstly, the received signal is denoised using a five-level wavelet decomposition, next, a double sliding window algorithm is applied to detect the change in the variance information of the received signal, the first path (FP) TOA is then calculated according to the first variance sharp increase. The simulation results using the CM1 and CM2 IEEE 802.15.4a channel models, prove that our proposed approach works effectively compared with the conventional ED-based methods. </p>", "Keywords": "Energy detection;Ranging;Sliding window;Time-of-arrival;Ultra-wideband;Variance detection", "DOI": "10.11591/ijece.v12i6.pp6303-6310", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Tlemcen University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>j <PERSON> University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tlemcen University"}], "References": []}, {"ArticleId": 96380635, "Title": "Modeling and analysis of field-oriented control based permanent magnet synchronous motor drive system using fuzzy logic controller with speed response improvement", "Abstract": "<p> The permanent magnet synchronous motor (PMSM) acts as an electrical motor mainly used in many diverse applications. The controlling of the PMSM drive is necessary due to frequent usage in various systems. The conventional proportional-integral-derivative (PID) controller’s drawbacks are overcome with fuzzy logic controller (FLC) and adopted in the PMSM drive system. In this manuscript, an efficient field-oriented control (FOC) based PMSM drive system using a fuzzy logic controller (FLC) is modeled to improve the speed and torque response of the PMSM. The PMSM drive system is modeled using abc to αβ and αβ to abc transformation, 2-level space vector pulse width modulation (SVPWM), AC to DC rectifier with an inverter, followed by PMSM drive, proportional integral (PI) controller along with FLC. The FLC’s improved fuzzy rule set is adopted to provide faster speed response, less % overshoot time, and minimal steady-state error of the PMSM drive system. The simulation results of speed response, torque response, speed error, and phase currents are analyzed. The FLC-based PMSM drive is compared with the conventional PID-based PMSM drive system with better improvements in performance metrics. </p>", "Keywords": "Field-oriented control;Fuzzy logic controller;Permanent magnet synchronous motor;Proportional-integral-derivative controller;Speed response;Torque", "DOI": "10.11591/ijece.v12i6.pp6010-6021", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CMR Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sapthagiri College of Engineering"}], "References": []}, {"ArticleId": 96380636, "Title": "Permutation based load balancing technique for long term evolution advanced heterogeneous networks", "Abstract": "<p> Traffic congestion has been one of the major performance limiting factors of heterogeneous networks (HetNets). There have been several load balancing schemes put up to solve this by balancing load among base stations (BSs), but they appear to be unfeasible due to the complexity required and other unsatisfactory performance aspects. Cell range extension (CRE) has been a promising technique to overcome this challenge. In this paper, a permutation based CRE technique is proposed to find the best possible formation of bias for BSs to achieve load balance among BSs. In comparison to the baseline scheme, results depict that the suggested method attains superior performance in terms of network load balancing and average throughput. The complexity of the suggested algorithm is considerably reduced in comparison to the proposed permutation based CRE method it is further modified from. </p>", "Keywords": "Cell range extension;Heterogeneous network;Interference mitigation;Load balancing;Long term evolution advanced", "DOI": "10.11591/ijece.v12i6.pp6311-6319", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Central Queensland University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "International Islamic University Chittagong"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "International Islamic University Chittagong"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pabna University of Science and Technology"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Pabna University of Science and Technology"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Pabna University of Science and Technology"}], "References": []}, {"ArticleId": 96380846, "Title": "Monolithically Assembled 3D Soft Transformable Robot", "Abstract": "<p>With the growing demand for soft robots capable of various degrees of locomotion based on reversibly actuating liquid crystalline networks (LCNs), the fabrication of complex 3D architectures from 2D LCN films via shape reconfiguration and/or assembly techniques are studied recently. However, once a system is formed and fixed into a specific 3D structure, only certain movements can be implemented using the fixed structure, and disassembly into the original 2D films is challenging. Therefore, studies to overcome this irreversible fabrication process become increasingly important. Herein, an effective and simple preparation of static and dynamic covalent dual-cross-linked, photo-controllable LCN (pc-LCN) films as building blocks for lego-like, monolithically assembled 3D soft transformable robots is presented. By tailoring the static and dynamic covalent linkages in the networks, pc-LCN films can be readily reconfigured and assembled into complex 3D structures under ultraviolet (UV) irradiation. Such monoliths can also be disassembled into their constituent building block films and reassembled into different architectures under the same UV stimulation. Moreover, by adopting selective visible-light-responsive dopant dyes to actuate pc-LCN building blocks, 3D soft transformable robots with versatile motion capabilities, including rolling, gripping, and cargo transport in multiple directions, are demonstrated.</p>", "Keywords": "dynamic covalent bonds;liquid crystalline networks;reversible assembly;shape reconfiguration;soft robots", "DOI": "10.1002/aisy.202200051", "PubYear": 2022, "Volume": "4", "Issue": "9", "JournalId": 64217, "JournalTitle": "Advanced Intelligent Systems", "ISSN": "2640-4567", "EISSN": "2640-4567", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Materials Division Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea;School of Chemical and Biological Engineering and Institute of Chemical Processes Seoul National University  599 Gwanak-ro, Gwanak-gu Seoul 08826 Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Advanced Materials Division Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea"}, {"AuthorId": 3, "Name": "Jiseok Han", "Affiliation": "Advanced Materials Division Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Composite Materials Application Research Center Korea Institute of Science and Technology  92 Chudong-ro, Bongdong-eup, Wanju-gun Jeonbuk 55324 Republic of Korea"}, {"AuthorId": 5, "Name": "Sungmin Park", "Affiliation": "Advanced Materials Division Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Advanced Materials Division Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea;Advanced Materials and Chemical Engineering KRICT School University of Science & Technology (UST)  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Advanced Materials Division Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea;Advanced Materials and Chemical Engineering KRICT School University of Science & Technology (UST)  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Advanced Materials and Chemical Engineering KRICT School University of Science & Technology (UST)  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea;Interface Materials and Chemical Engineering Research Center Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Advanced Materials Division Korea Research Institute of Chemical Technology  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea;Advanced Materials and Chemical Engineering KRICT School University of Science & Technology (UST)  141 Gajeong-ro, Yuseong-gu Daejeon 34114 Republic of Korea"}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Chemical and Biological Engineering and Institute of Chemical Processes Seoul National University  599 Gwanak-ro, Gwanak-gu Seoul 08826 Republic of Korea"}], "References": []}, {"ArticleId": 96380871, "Title": "Building construction based on video surveillance and deep reinforcement learning using smart grid power system", "Abstract": "New trendy neighborhoods require trimming scientific and technological methods and equipment. Smart buildings (SB) use resources efficiently, save energy, and provide services to the community more easily for their occupants while reducing their environmental footprint . Smart cities have benefited from this growth in terms of smart buildings. Maximum accuracy and reduced latency are both required for smart building monitoring systems. Poor scheduling rules can lead to network congestion and latency that is too high for real-time monitoring on construction sites, which have restricted computing and networking capabilities. These devices can collect the data on on-site actions, achievements, and circumstances and send it back to the central dashboard for analysis. Model predictive control and Deep Reinforcement Learning (DRL) have significant drawbacks, and DRL addresses some drawbacks. Researchers are intrigued by DRL, a brand-new approach to quality control. The most important considerations for developing smart power grid systems are energy conservation, renewable energy integration, and a streamlined control system. Experiments have shown that the new video surveillance has a low loss rate and a consistent latency. The DRL-SB-IoT technique can successfully track multiple cameras in a wide monitoring situation. This technique results in excellent tracking performance and meets the criteria for developing an intelligent campus in the best way possible. Researchers analyzed studies using supervised learning to solve common building issues, such as health monitoring, security on building sites, accommodation modeling, and energy consumption prediction. Reinforcement learning has been used to solve these issues. The proposed method advances the smart gateway channel of 97.5%, the energy storage ratio of 96.9%, and the overall surveillance performance ratio of 98.6%.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108273", "PubYear": 2022, "Volume": "103", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IT Programs Center, Faculty of IT Department, Institute of Public Administration, Riyadh 11141, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Bolton, United Kingdom;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Information Systems, College of Applied Sciences, AlMaarefa University, Riyadh 13713, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Computer Sciences and Information Technology College, Majmaah University, Al-Majmaah 11952, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Technology, College of Computer and Information Sciences, Majmaah University, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON> <PERSON><PERSON><PERSON>", "Affiliation": "Department of Natural and Applied Sciences, Faculty of Community College, Majmaah University, Majmaah 11952, Saudi Arabia"}], "References": [{"Title": "An intelligent video analytics model for abnormal event detection in online surveillance video", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "4", "Page": "915", "JournalTitle": "Journal of Real-Time Image Processing"}, {"Title": "RETRACTED ARTICLE: Intelligent manufacturing model of construction industry based on Internet of Things technology", "Authors": "Lingji Kong; Biao Ma", "PubYear": 2020, "Volume": "107", "Issue": "3-4", "Page": "1025", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Construction of intelligent building sky-eye system based on multi-camera and speech recognition", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "23", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "RETRACTED ARTICLE: Enhanced pedestrian detection using optimized deep convolution neural network for smart building surveillance", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "22", "Page": "17081", "JournalTitle": "Soft Computing"}, {"Title": "Construction and application of digital creative platform for digital creative industry based on smart city concept", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106748", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "A pattern recognition model for static gestures in malaysian sign language based on machine learning techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "95", "Issue": "", "Page": "107383", "JournalTitle": "Computers & Electrical Engineering"}]}, {"ArticleId": 96380978, "Title": "Process plant layout optimization considering domino effects and safety devices in a large-scale chemical plant", "Abstract": "The layout of a process plant is an essential factor in designing a chemical process which can lead to enhance the performance of the plant as well as reducing economic damages in the case of any incident in the plant. In this study, a mathematical formulation of the plant layout problem considering possible scenarios including fire, explosion, domino effects, and the existence of safety devices is proposed. Benefiting from Bat metaheuristic algorithm at the onset, the mathematical model was validated with a case study from one of our references and was then implemented on a large-scale chemical process plant. The results illustrate that the proposed model can reduce piping costs, land cost, domino hazard index, and the costs associated with domino escalation up to 30%. In addition, Lack of safety devices can increase the piping cost of the plant up to 20%. It was concluded that the proposed model can effectively improve the safety of a plant and reduces its layout costs.", "Keywords": "Layout optimization ; Domino hazard index ; Risk assessment ; Safety devices ; Domino escalation", "DOI": "10.1016/j.compchemeng.2022.108006", "PubYear": 2022, "Volume": "167", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical, Petroleum and Gas Engineering, Semnan University, Semnan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemical, Petroleum and Gas Engineering, Semnan University, Semnan, Iran;Corresponding author"}], "References": []}, {"ArticleId": 96381003, "Title": "Monte Carlo simulation convergences’ percentage and position in future reliability evaluation", "Abstract": "<p> Reliability assessment is a needed assessment in today's world. It is required not only for system design but also to ensure the power delivered reaches the consumer. It is usual for fault to occur, but it is best if the fault can be predicted and the way to overcome it can be prepared in advance. Monte Carlo simulation is a standard method of assessing reliability since it is a time-based evaluation that nearly represents the actual situation. However, sequential Monte Carlo (SMC) typically took long-time simulation. A convergence element can be implemented into the simulation to ensure that the time taken to compute the simulation can be reduced. The SMC can be done with and without convergence. SMC with convergence has high accuracy compared to the SMC without convergence, as it takes a long time and has a high possibility of not getting accurate output. In this research, the SMC is subjected to five different convergence items to determine which converge simulation is the fastest while providing better performance for reliability evaluation. There are two types of convergence positions, namely input convergence and output convergence. Overall, output convergence shows the best result compared to input convergence. </p>", "Keywords": "Convergence position;Monte Carlo;Percentage convergence", "DOI": "10.11591/ijece.v12i6.pp6218-6227", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Malaysia Pahang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Universiti Malaysia Pahang"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Universiti Malaysia Pahang"}], "References": []}, {"ArticleId": 96381115, "Title": "Psychological Security and Its Relationship to Empathy Among a Sample of Early Childhood in Jubail Industrial City", "Abstract": "The current research aims at revealing the relationship between psychological security and empathy in the stage of early childhood at the Jubail Industrial City. Its significance can be attributed to the importance of developing empathy among children, enlightening the community and educators about the importance of psychological security and its relationship to empathy among children in the early childhood stage. An analytical descriptive approach was employed as it suits the nature of the current research. A random sample comprising 204 children in the early childhood stage. Having applied the psychological security [1] and empathy scales [2] to the research sample, the following result was reached. There is a statistically significant correlation between psychological security and empathy in a sample of children in the early childhood stage in Jubail Industrial City. © 2023 NSP.", "Keywords": "Early Childhood Stage; Empathy; Psychological Security", "DOI": "10.18576/isl/120139", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96381348, "Title": "Visual and light detection and ranging-based simultaneous localization and mapping for self-driving cars", "Abstract": "<p> In recent years, there has been a strong demand for self-driving cars. For safe navigation, self-driving cars need both precise localization and robust mapping. While global navigation satellite system (GNSS) can be used to locate vehicles, it has some limitations, such as satellite signal absence (tunnels and caves), which restrict its use in urban scenarios. Simultaneous localization and mapping (SLAM) are an excellent solution for identifying a vehicle’s position while at the same time constructing a representation of the environment. SLAM-based visual and light detection and ranging (LIDAR) refer to using cameras and LIDAR as source of external information. This paper presents an implementation of SLAM algorithm for building a map of environment and obtaining car’s trajectory using LIDAR scans. A detailed overview of current visual and LIDAR SLAM approaches has also been provided and discussed. Simulation results referred to LIDAR scans indicate that SLAM is convenient and helpful in localization and mapping. </p>", "Keywords": "Camera;Light detection and ranging;Localization;Mapping;Self-driving cars;Simultaneous localization and mapping", "DOI": "10.11591/ijece.v12i6.pp6284-6292", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Farnane <PERSON>", "Affiliation": "Hassan First University of Settat"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassan First University of Settat"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassan First University of Settat"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassan First University of Settat"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Hassan First University of Settat"}], "References": []}, {"ArticleId": 96381523, "Title": "The Attitudes of Working Mothers Towards the Negative Aspects of Working for Long Periods of Time on Behavioral Counseling for Their Children", "Abstract": "The study aimed to identify the negative aspects or impact of mothers working outside the home over extensive periods on the behavioral counseling of their children. The study was done from the point of view of working mothers in the United Arab Emirates (Al-Ain region). The study utilized an analytically descriptive method to achieve its goals on an intended sample that consisted of (314) working mothers via a questionnaire consisting of (30) items. The tool was applied after it exhibited acceptable validity and reliability; the data was processed using arithmetic means, standard deviations and statistical tests appropriate to the study questions and their variables. The negative effects of mothers working for long periods outside the home that resulted in a need for behavioral remediation for their children came to a moderate degree on the scale. The results of the study also showed that there were statistically significant differences between the average answers of the study sample owing to the variable differences of age, educational qualification, private/public sector type, monthly income, and number of children in the family. There were no statistically significant differences in regards due to the disadvantages of being a working mother and its effect on the need for behavioral counseling of children due to the total number of daily working hours. Based on the results, the study recommends the necessity of adopting social policies that would provide more time for the working mother to care for her children in all sectors, provided that they are officially legislated policies. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Al-Ain region; Behavioral Counseling; Working Mother", "DOI": "10.18576/isl/120123", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96381524, "Title": "Electrical and Magnetic Properties of Mn-Bi-Sb Alloys", "Abstract": "MnBi1-xSbx alloys were prepared by the conventional melt technique. The Seebeck coefficient (S), electrical resistivity (ρ), and magnetic susceptibility (χ) were measured at various temperatures ranging from ~100 to 400 K. The electrical resistivity of x ≤ 0.15 shows both semiconducting and metallic behavior depending on temperature and Sb content, whereas samples x ≥ 0.2 have only semiconductor behavior in all the temperature range. The negative sign of the Seebeck coefficient increases, i.e., the positivity decreases with the increasing Sb content. The magnetic susceptibility (χ) shows that alloys undergo ferro-paramagnetic transition at a certain temperature (TC) and the TC values decrease with increasing Sb content. From thermoelectric measurements and electronic thermal conductivity calculated, it was observed that Sb doping increases the power factor (PF) and the figure of merit (ZT). Thus, Sb content plays an essential role in making these alloys applicable in the thermoelectric industry. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Crystal structure; Figure of merit; Magnetic properties; Ternary alloy system; Thermoelectric power", "DOI": "10.18576/isl/120111", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96381529, "Title": "Deep Learning Model Based on ResNet-50 for Beef Quality Classification", "Abstract": "Food quality measurement is one of the most essential topics in agriculture and industrial fields. To classify healthy food using computer visual inspection, a new architecture was proposed to classify beef images to specify the rancid and healthy ones. In traditional measurements, the specialists are not able to classify such images, due to the huge number of beef images required to build a deep learning model. In the present study, different images of beef including healthy and rancid cases were collected according to the analysis done by the Laboratory of Food Technology, Faculty of Agriculture, Kafrelsheikh University in January of 2020. The texture analysis of the beef surface of the enrolled images makes it difficult to distinguish between the rancid and healthy images. Moreover, a deep learning approach based on ResNet-50 was presented as a promising classifier to grade and classify the beef images. In this work, a limited number of images were used to present the research problem of image resource limitation; eight healthy images and ten rancid beef images. This number of images is not sufficient to be retrained using deep learning approaches. Thus, Generative Adversarial Network (GAN) was proposed to augment the enrolled images to produce one hundred eighty images. The results obtained based on ResNet-50 classification achieve accuracy of 96.03%, 91.67%, and 88.89% in the training, testing, and validation phases, respectively. Furthermore, a comparison of the current model (ResNet-50) with the classical and deep learning architecture is made to demonstrate the efficiency of ResNet-50, in image classification. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Beef classification; Deep learning; Diet and food quality; Generative adversarial network; ResNet50", "DOI": "10.18576/isl/120124", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96381677, "Title": "X-ray sensors based on micro/ nano CeO2 segregated AgBiS2 for low dose detection", "Abstract": "Semiconductor-based direct conversion X-ray sensors have widespread applications spanning from medical diagnosis to industrial inspection. However, current findings on X-ray detection based on perovskites shows high sensitivity but exhibit large leakage current and poor stability, thus, inhibiting them from commercialization. Highly stable AgBiS<sub>2</sub> with large attenuation coefficient (μ = 3.07 g cm<sup>−2</sup> at 70 keV), shows excellent charge conversion due to its higher atomic number (Bi, Z = 83), and density (ρ = 7.02 g cm<sup>−3</sup>) was used for X-ray detection. Here, we approach to enhance the net sensitivity of AgBiS<sub>2</sub> by tailoring all three parameters by segregating micro/ nanocrystals of cerium oxide (CeO<sub>2</sub>) at the AgBiS<sub>2</sub> grain boundary (GB) region. From the X-ray impinged photocurrent response, substantial GB segregation of n-CeO<sub>2</sub> at the AgBiS<sub>2</sub> interface leads to improved attenuation and promotes the conversion of multiple scattered X-ray photons into electrons by interacting with the adjacent grains, thus resulting in enhanced photocurrent generation. The sensitivity (S) and noise equivalent dose (NED) ratio were calculated to determine the lowest detectivity of the sensor with less generated noise signals. From these experimental findings, 10 % of n-CeO<sub>2</sub> segregation leads to an improvement in the sensitivity of AgBiS<sub>2</sub> to 29 μC mGy<sup>−1</sup> cm<sup>−3</sup>.", "Keywords": "Low-dose (mGy) X-ray detection ; Grain boundary segregation ; Nano-AgBiS<sub>2</sub> ; Micro/ nano CeO<sub>2</sub> ; X-ray detector", "DOI": "10.1016/j.sna.2022.113893", "PubYear": 2022, "Volume": "347", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "National Centre for Nanoscience and Nanotechnology (NCNSNT), University of Madras, Guindy Campus, Chennai 600025, Tamil Nadu, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Centre for Nanoscience and Nanotechnology (NCNSNT), University of Madras, Guindy Campus, Chennai 600025, Tamil Nadu, India;Corresponding author"}], "References": []}, {"ArticleId": 96381740, "Title": "Robust tracking control for operator‐based uncertain micro‐hand actuator with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hysteresis", "Abstract": "<p>Since the hysteresis property inherently exists in the rubber material, it is necessary to deal with the control issues for the micro-hand by considering the hysteresis property. Therefore, in this paper, the robust tracking control for the micro-hand systems is discussed from the aspect of the Prandtl–<PERSON>hl<PERSON>kii hysteresis property which is more applicable for the real applications. Firstly, a new model is obtained by combining the dynamic model of the micro-hand with Prandtl–<PERSON>ki<PERSON> hysteresis property. Secondly, a new stability condition based on bounded input and bounded output stability is proposed for the Prandtl–Ishl<PERSON>kii hysteresis modeled micro-hand system from two different cases. Thirdly, by designing the robust controllers based on the internal model control method, the tracking performance can be improved by eliminating the effect from the disturbance. Finally, simulation is used to further demonstrate the effectiveness of the proposed design scheme.</p>", "Keywords": "internal model control;nonlinear system;<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hysteresis;robust control;robust right coprime factorization", "DOI": "10.1002/asjc.2951", "PubYear": 2023, "Volume": "25", "Issue": "3", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Automation and Electronic Engineering Qingdao University of Science and Technology  Qingdao 266061 People's Republic of China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Automation and Electronic Engineering Qingdao University of Science and Technology  Qingdao 266061 People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Automation and Electronic Engineering Qingdao University of Science and Technology  Qingdao 266061 People's Republic of China"}], "References": []}, {"ArticleId": 96381785, "Title": "Removal mechanism of submerged air jet polishing considering the state of abrasive particles", "Abstract": "<p>In this paper, a novel polishing method, submerged air jet polishing (SAJP), is proposed. In this method, the nozzle and workpiece are completely immersed in the abrasive fluid. When the compressed air is sprayed from the nozzle in the abrasive fluid at high velocity onto the workpiece surface, the material removal occurs on the workpiece surface due to abrasive contact and cavitation. The main finding of this paper is to establish the microscopic theoretical model of SAJP on the basis of the micro-cutting model of the single abrasive particle. The erosion removal mechanism of SAJP processing titanium alloy was studied by simulation combined with experiments, and the material removal mechanism under different process parameters was explored from the perspective of microscopic evolution.</p>", "Keywords": "Submerged air jet polishing; Micro-cutting model; Material removal mechanism; Titanium alloy", "DOI": "10.1007/s00170-022-10116-3", "PubYear": 2022, "Volume": "122", "Issue": "9-10", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering & Jiangsu Provincial Key Laboratory of Advanced Robotics, Soochow University, Suzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering & Jiangsu Provincial Key Laboratory of Advanced Robotics, Soochow University, Suzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering & Jiangsu Provincial Key Laboratory of Advanced Robotics, Soochow University, Suzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering & Jiangsu Provincial Key Laboratory of Advanced Robotics, Soochow University, Suzhou, China"}], "References": []}, {"ArticleId": 96381790, "Title": "Recurrent self-optimizing proposals for weakly supervised object detection", "Abstract": "<p>Weakly supervised object detection (WSOD) has attracted attention increasingly in object detection, as it only requires image-level annotations to train the detector. A typical paradigm for WSOD is to first generate candidate region proposals for the training data, and then each image is treated as a bag of proposals to conduct the training based on the multiple instance learning (MIL). Most methods focus on optimizing the training process, but rarely consider the influence of pre-generated proposals that directly affect the learning of the detector, due to the overwhelming noisy proposals (e.g., negative or background proposals) and positive proposals with inaccurate locations. In this paper, we focus on improving the quality of proposals, and propose a recurrent self-optimizing proposal framework, a new paradigm for WSOD, to iteratively optimize the pre-generated proposals. In each iteration, all detection results (i.e., the object-aware coordinate offsets and the confidence scores) are accumulated for proposal optimization. To achieve accurate object location, we design a proposal self-transformation module to transform the locations of pre-generated proposals based on the coordinate offsets. To alleviate the impact of noise proposals, we design a proposal self-sampling module to mine object instances through confidence scores to filter out noisy proposals. Furthermore, these optimized proposals are fed into a decoupled proposal learner, which contains two parallel proposal training branches. A MIL module and an instance refinement module are supervised by the image label and the mined object instances, respectively. In addition, the instance refinement module contains an instance regression refinement module, which is proposed to generate object-aware coordinate offsets. In turn, the decoupled proposal learner produces the new detection results to optimize proposals in the next iteration. Extensive experiments on PASCAL VOC and MS-COCO datasets demonstrate the effectiveness of our method.</p>", "Keywords": "Weakly supervised object detection; Recurrent self-optimizing proposals; Proposal self-transformation; Proposal self-sampling", "DOI": "10.1007/s00521-022-07818-w", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Engineering, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information and Communication Engineering, University of Electronic Science and Technology of China, Chengdu, China"}], "References": [{"Title": "Person re-identification with features-based clustering and deep features", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "14", "Page": "10519", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A survey of deep learning techniques for autonomous driving", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "37", "Issue": "3", "Page": "362", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Fuzzy-aided solution for out-of-view challenge in visual tracking under IoT-assisted complex environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "4", "Page": "1055", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Statistically correlated multi-task learning for autonomous driving", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "19", "Page": "12921", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Scale-free heterogeneous cycleGAN for defogging from a single image for autonomous driving in fog", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "5", "Page": "3737", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Possibilistic rank-level fusion method for person re-identification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "17", "Page": "14151", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Online Active Proposal Set Generation for weakly supervised object detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "237", "Issue": "", "Page": "107726", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A fast accurate fine-grain object detection model based on YOLOv4 deep neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "3895", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Accelerated duality-aware correlation filters for visual tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "8", "Page": "6241", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96381797, "Title": "Challenges and future directions for energy, latency, and lifetime improvements in NVMs", "Abstract": "<p>Recently, non-volatile memory (NVM) technology has revolutionized the landscape of memory systems. With many advantages, such as non volatility and near zero standby power consumption, these byte-addressable memory technologies are taking the place of DRAMs. Nonetheless, they also present some limitations, such as limited write endurance, which hinders their widespread use in today’s systems. Furthermore, adjusting current data management systems to embrace these new memory technologies and all their potential is proving to be a nontrivial task. Because of this, a substantial amount of research has been done, from both the database community and the storage systems community, that tries to improve various aspects of NVMs to integrate these technologies into the memory hierarchy. In this work, which is the extended version of <PERSON><PERSON><PERSON> and Nawab (Proc. VLDB Endowment 14(12):3194–3197, 2021), we explore state-of-the-art work on deploying NVMs in database and storage systems communities and the ways their limitations are being handled within these communities. In particular, we focus on (1) the challenges that are related to high energy consumption, low write endurance and asymmetric read/write costs and (2) how these challenges can be solved using hardware and software solutions, especially by reducing the number of bit flips in write operations. We believe that this area has not gained enough attention in the data management community and this tutorial will provide information on how to integrate recent advances from the NVM storage community into existing and future data management systems.</p>", "Keywords": "Non-volatile memory; Write endurance; Energy consumption; Reducing bit flips", "DOI": "10.1007/s10619-022-07421-x", "PubYear": 2023, "Volume": "41", "Issue": "3", "JournalId": 3983, "JournalTitle": "Distributed and Parallel Databases", "ISSN": "0926-8782", "EISSN": "1573-7578", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CSE Department, UC Santa Cruz, Santa Cruz, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSE Department, UC Irvine, Irvine, USA"}], "References": [{"Title": "Towards the design of efficient hash-based indexing scheme for growing databases on non-volatile memory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "1", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Understanding and analysis of B+ trees on NVM towards consistency and efficiency", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "36", "JournalTitle": "CCF Transactions on High Performance Computing"}, {"Title": "Improving the Performance of Hybrid Caches Using Partitioned Victim Caching", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "A dynamic clustering technique based on deep reinforcement learning for Internet of vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3", "Page": "757", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Performance Evaluation of Intel Optane Memory for Managed Workloads", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Architecture and Code Optimization"}, {"Title": "Robust deep k -means: An effective and simple method for data clustering", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "107996", "JournalTitle": "Pattern Recognition"}, {"Title": "Leveraging NVMe SSDs for Building a Fast, Cost-effective, LSM-tree-based KV Store", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "17", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Storage"}, {"Title": "An Energy-Efficient DRAM Cache Architecture for Mobile Platforms With PCM-Based Main Memory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}, {"Title": "An Efficient Sorting Algorithm for Non-Volatile Memory", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "31", "Issue": "11n12", "Page": "1603", "JournalTitle": "International Journal of Software Engineering and Knowledge Engineering"}, {"Title": "Unified Holistic Memory Management Supporting Multiple Big Data Processing Frameworks over Hybrid Memories", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "1-4", "Page": "1", "JournalTitle": "ACM Transactions on Computer Systems"}, {"Title": "WDBT: Non-volatile memory wear characterization and mitigation for DBT systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "187", "Issue": "", "Page": "111247", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Design-Technology Co-Optimization for NVM-Based Neuromorphic Processing Elements", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Embedded Computing Systems"}]}, {"ArticleId": 96381839, "Title": "Neuro-semantic prediction of user decisions to contribute content to online social networks", "Abstract": "<p>Understanding at microscopic level the generation of contents in an online social network (OSN) is highly desirable for an improved management of the OSN and the prevention of undesirable phenomena, such as online harassment. Content generation, i.e., the decision to post a contributed content in the OSN, can be modeled by neurophysiological approaches on the basis of unbiased semantic analysis of the contents already published in the OSN. This paper proposes a neuro-semantic model composed of (1) an extended leaky competing accumulator (ELCA) as the neural architecture implementing the user concurrent decision process to generate content in a conversation thread of a virtual community of practice, and (2) a semantic modeling based on the topic analysis carried out by a latent Dirichlet allocation (LDA) of both users and conversation threads. We use the similarity between the user and thread semantic representations to built up the model of the interest of the user in the thread contents as the stimulus to contribute content in the thread. The semantic interest of users in discussion threads are the external inputs for the ELCA, i.e., the external value assigned to each choice.. We demonstrate the approach on a dataset extracted from a real life web forum devoted to fans of tinkering with musical instruments and related devices. The neuro-semantic model achieves high performance predicting the content posting decisions (average <i>F</i> score 0.61) improving greatly over well known machine learning approaches, namely random forest and support vector machines (average <i>F</i> scores 0.19 and 0.21).</p><p>© The Author(s) 2022.</p>", "Keywords": "Information diffusion;Leaky competing accumulator;Microscopic model of social interaction;Multi-topic text preferences;Social interaction decision making", "DOI": "10.1007/s00521-022-07307-0", "PubYear": 2022, "Volume": "34", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Business Intelligence Research Center, Universidad de Chile, Beauchef 851, P.O. Box 8370459, Santiago, Chile."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Industrial Engineering Department, Universidad de Chile, Beauchef 851, Santiago, Chile."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Business Intelligence Research Center, Universidad de Chile, Beauchef 851, P.O. Box 8370459, Santiago, Chile."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Computational Intelligence Group, University of the Basque Country, San Sebastian, Spain."}], "References": [{"Title": "On the use of distributed semantics of tweet metadata for user age prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "437", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "DST-HRS: A topic driven hybrid recommender system based on deep semantics", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "156", "Issue": "", "Page": "183", "JournalTitle": "Computer Communications"}, {"Title": "Modeling information diffusion in online social networks using a modified forest-fire model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "56", "Issue": "2", "Page": "355", "JournalTitle": "Journal of Intelligent Information Systems"}]}, {"ArticleId": 96381919, "Title": "The moving target tracking and segmentation method based on space-time fusion", "Abstract": "<p>At present, the target tracking method based on the correlation operation mainly uses deep learning to extract spatial information from video frames and then performs correlations on this basis. However, it does not extract the motion features of tracking targets on the time axis, and thus tracked targets can be easily lost when occlusion occurs. To this end, a spatiotemporal motion target tracking model incorporating Kalman filtering is proposed with the aim of alleviating the problem of occlusion in the tracking process. In combination with the segmentation model, a suitable model is selected by scores to predict or detect the current state of the target. We use an elliptic fitting strategy to evaluate the bounding boxes online. Experiments demonstrate that our approach performs well and is stable in the face of multiple challenges (such as occlusion) on the VOT2016 and VOT2018 datasets with guaranteed real-time algorithm performance.</p>", "Keywords": "Target tracking; Kalman filtering; Segmentation; Elliptic fitting", "DOI": "10.1007/s11042-022-13703-4", "PubYear": 2023, "Volume": "82", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Guangxi Minzu university, Nanning, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Guangxi Minzu university, Nanning, China; Guangxi Key Laboratory of Hybrid Computation and IC Design Analysis, Nanning, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Guangxi Minzu university, Nanning, China"}, {"AuthorId": 4, "Name": "Xuyang Qin", "Affiliation": "School of Artificial Intelligence, Guangxi Minzu university, Nanning, China"}], "References": [{"Title": "Spatial and semantic convolutional features for robust visual object tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "21-22", "Page": "15095", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Video Object Segmentation and Tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}]}, {"ArticleId": 96381921, "Title": "Regularized online tensor factorization for sparse knowledge graph embeddings", "Abstract": "Knowledge Graphs represent real-world facts and are used in several applications; however, they are often incomplete and have many missing facts. Link prediction is the task of completing these missing facts from existing ones. Embedding models based on Tensor Factorization attain state-of-the-art results in link prediction. Nevertheless, the embeddings they produce can not be easily interpreted. Inspired by previous work on word embeddings, we propose inducing sparsity in the bilinear tensor factorization model, RESCAL, to build interpretable Knowledge Graph embeddings. To overcome the difficulties that stochastic gradient descent has when producing sparse solutions, we add $$l_1$$ \n \n l \n 1 \n \n regularization to the learning objective by using the generalized Regularized Dual Averaging online optimization algorithm. The proposed method substantially improves the interpretability of the learned embeddings while maintaining competitive performance in the standard metrics.", "Keywords": "Knowledge graph embedding; Sparse learning; Interpretable embeddings", "DOI": "10.1007/s00521-022-07796-z", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering - DeustoTech, University of Deusto, Bilbao, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Engineering - DeustoTech, University of Deusto, Bilbao, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Engineering - DeustoTech, University of Deusto, Bilbao, Spain"}], "References": [{"Title": "Adaptive knowledge subgraph ensemble for robust and trustworthy knowledge graph completion", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "1", "Page": "471", "JournalTitle": "World Wide Web"}, {"Title": "Survey on graph embeddings and their applications to machine learning problems on graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 96381922, "Title": "Analytical study on users’ awareness and acceptability towards adoption of multimodal biometrics (MMB) mechanism in online transactions: a two-stage SEM-ANN approach", "Abstract": "<p>The study analyses user awareness of multimodal biometrics and its acceptability for online transactions in the current dynamic world. The study was performed on the five underlying perspectives: User Acceptability, Cognizant Factors towards Biometrics, Technological factors, Perceptional Factors (Fingerprints, Iris, Face Recognition and Voice) and Data Privacy Factors. A questionnaire was prepared and circulated to the 530 biometrics users; on that basis, the corresponding answer was obtained for analysis. SEM is first employed to gauge the research model and test the prominent hypothesized predictors, which are then used as inputs in the neural network to evaluate the relative significance of each predictor variable. By considering the standardized significance of the feed-for-back-propagation of ANN algorithms, the study found a significant effect of DPF_3 (93%), DPF_2 (50%) and DPF_4 (34%) on the adoption of MMB. In the Perceptional construct, PRF_2 (49%) and PRF_3 (33%) was relatively the most important predictor, whereas, in User Acceptability, UAC_2 (37%), UAC_3 &amp; UAC_5 (41%) was vital to be considered. Only one item, TCF_2 (35%), from Technological Factors, followed by Cognizant factors, i.e., CFG_1 (33%), confirmed the best fit model to adopt MMB. The research is a novel effort when compared to past studies as it considered cognizant and perceptual factors in the proposed model, thereby expanding the analytical outlook of MMB literature. Thus, the study also explored several new and valuable practical implications for adopting multimodal instruments of biometrics along with certain limitations.</p><p>© The Author(s), under exclusive licence to Springer Science+Business Media, LLC, part of Springer Nature 2022, Springer Nature or its licensor holds exclusive rights to this article under a publishing agreement with the author(s) or other rightsholder(s); author self-archiving of the accepted manuscript version of this article is solely governed by the terms of such publishing agreement and applicable law.</p>", "Keywords": "ANN;Biometrics adoption (BA);CFA;Fusion;Multi-model biometrics (MMB)", "DOI": "10.1007/s11042-022-13786-z", "PubYear": 2023, "Volume": "82", "Issue": "9", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "EEE, Birla Institute of Technology &amp; Science Pilani, Pilani, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "SOM, Sir <PERSON><PERSON><PERSON>, Udaipur, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "EEE, Birla Institute of Technology &amp; Science Pilani, Pilani, India."}], "References": [{"Title": "A survey on smartphone user’s security choices, awareness and education", "Authors": "<PERSON>; <PERSON>-<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "88", "Issue": "", "Page": "101647", "JournalTitle": "Computers & Security"}, {"Title": "A comprehensive survey on the biometric recognition systems based on physiological and behavioral modalities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "113114", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Convolutional neural networks approach for multimodal biometric identification system using the fusion of fingerprint, finger-vein and face images", "Authors": "<PERSON>hdi <PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "1", "JournalTitle": "PeerJ Computer Science"}, {"Title": "Human emotion recognition using intelligent approaches: A review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "417", "JournalTitle": "Intelligent Decision Technologies"}, {"Title": "A comprehensive security analysis of match-in-database fingerprint biometric system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "247", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Analytical outlook on customer awareness towards biometrics mechanism of unimodal and multimodal in online transactions", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "41-42", "Page": "31691", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Behavioral biometrics & continuous user authentication on mobile devices: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "", "Page": "76", "JournalTitle": "Information Fusion"}, {"Title": "Analysis of factors that influence the performance of biometric systems based on EEG signals", "Authors": "<PERSON>-<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "165", "Issue": "", "Page": "113967", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Assessing cloud computing value in firms through socio-technical determinants", "Authors": "<PERSON>; <PERSON><PERSON>; June Wei", "PubYear": 2020, "Volume": "57", "Issue": "8", "Page": "103369", "JournalTitle": "Information & Management"}, {"Title": "Adaptation of the idea of concept drift to some behavioral biometrics: Preliminary studies", "Authors": "<PERSON><PERSON><PERSON>wik; Rafal <PERSON>", "PubYear": 2021, "Volume": "99", "Issue": "", "Page": "104135", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Optimal feature level fusion for secured human authentication in multimodal biometric system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "Evaluating the benefits of Cloud Computing in Small, Medium and Micro-sized Enterprises (SMMEs)", "Authors": "<PERSON><PERSON><PERSON>; O<PERSON>den Jo<PERSON>", "PubYear": 2021, "Volume": "181", "Issue": "", "Page": "784", "JournalTitle": "Procedia Computer Science"}, {"Title": "Comments on biometric-based non-transferable credentials and their application in blockchain-based identity management", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "102243", "JournalTitle": "Computers & Security"}, {"Title": "Adaptive Management of Multimodal Biometrics—A Deep Learning and Metaheuristic Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "106", "Issue": "", "Page": "107344", "JournalTitle": "Applied Soft Computing"}, {"Title": "Factors Influencing Patient Adoption of the IoT for E-Health Management Systems (e-HMS) Using the UTAUT Model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Ambient Computing and Intelligence"}]}, {"ArticleId": 96381923, "Title": "Phase plane analysis applied to non-explicit multibody vehicle models", "Abstract": "A new methodology for constructing stability maps (phase-plane analysis) is presented and validated for application to complex multibody vehicle models implemented in Multibody Dynamics simulation software (Adams ® ). Traditional methodologies are developed to be applied to explicit mathematical models. Given the complexity of some special multibody systems, particularly in vehicle dynamics, simplifications are needed to apply this stability analysis technique. The main limitation when using simplified models is the need to neglect components which could have a significant influence on the dynamic behavior of the system and therefore on its stability. In the proposed methodology it is not necessary to have access to explicit mathematical models of multibody systems. Thus, the stability map of a vehicle model can be constructed by considering highly nonlinear dynamic elements, such as tires and silent-blocks components, modeled using the nonlinear finite element technique.", "Keywords": "ADAMS® ; Modeling; Multibody; Phase plane; Vehicle dynamics", "DOI": "10.1007/s11044-022-09846-9", "PubYear": 2022, "Volume": "56", "Issue": "2", "JournalId": 5013, "JournalTitle": "Multibody System Dynamics", "ISSN": "1384-5640", "EISSN": "1573-272X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Transportation Engineering, Oviedo University, Gijón, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Transportation Engineering, Oviedo University, Gijón, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Transportation Engineering, Oviedo University, Gijón, Spain"}], "References": []}, {"ArticleId": 96382154, "Title": "Real-time detection of crop rows in maize fields based on autonomous extraction of ROI", "Abstract": "The current crop rows detection based on machine vision generally has the problems of low detection accuracy and poor real-time performance. Moreover, crop rows detection remains a challenging problem in complex field conditions, such as high weeds pressure, poor illumination conditions, and vegetation foliage shading. We propose a crop rows detection algorithm based on autonomous extraction of ROI (Region of interest). The prior method computes the feature points of the entire image and groups them into the crop rows which they belong to. Instead, we consider the core of crop rows detection problem to be the extraction of the travelling area of agricultural machinery in maize fields. A YOLO (You Only Look Once) neural network is employed to predict the travelling area of the agricultural machinery end-to-end. The prediction boxes are unified into ROI and the crop and soil background are segmented in the ROI by Excess Green operator and <PERSON><PERSON>’s method. Then, the feature points of crops are extracted using FAST (Features from Accelerated Segment Test) corner point detection, and finally the detection lines of crop rows are fitted with least squares method. Because image recognition is limited to a valid region after the ROI is extracted, the processing speed of our algorithm is remarkably fast. It takes only about 25 ms to process a single image (640*360 pixels) and the frame rate of video stream exceeds 40FPS. Meanwhile, it can achieve high accuracy and robust extraction of ROI in various maize fields. The average error angle of the detection lines is 1.88°, which can meet the real-time and accuracy requirements of field navigation. The proposed algorithm can provide a new solution to the current machine vision-based navigation technology for agricultural machinery. Code is available at: https://github.com/WoodratTradeCo/crop-rows-detection .", "Keywords": "Crop rows detection ; ROI ; Deep learning ; Navigation", "DOI": "10.1016/j.eswa.2022.118826", "PubYear": 2023, "Volume": "213", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Engineering, Anhui Agricultural University, Hefei 230036, China;Institute of Artificial Intelligence, Hefei Comprehensive National Science Center, Hefei 230088, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Engineering, Anhui Agricultural University, Hefei 230036, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Anhui Agricultural University, Hefei 230036, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Engineering, Anhui Agricultural University, Hefei 230036, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Engineering, Anhui Agricultural University, Hefei 230036, China"}, {"AuthorId": 6, "Name": "Biao Ma", "Affiliation": "Nanjing Institute of Agricultural Mechanization, Ministry of Agriculture and Rural Affairs, Nanjing 210014, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON> Xu", "Affiliation": "School of Engineering, Anhui Agricultural University, Hefei 230036, China"}, {"AuthorId": 8, "Name": "Liqing Chen", "Affiliation": "School of Engineering, Anhui Agricultural University, Hefei 230036, China;Institute of Artificial Intelligence, Hefei Comprehensive National Science Center, Hefei 230088, China;Corresponding author at: School of Engineering, Anhui Agricultural University, Hefei 230036, China"}], "References": []}, {"ArticleId": 96382158, "Title": "Robust cross-network node classification via constrained graph mutual information", "Abstract": "The recent methods for cross-network node classification mainly exploit graph neural networks (GNNs) as feature extractor to learn expressive graph representations across the source and target graphs. However, GNNs are vulnerable to noisy factors, such as adversarial attacks or perturbations on the node features or graph structure, which can cause a significant negative impact on their learning performance. To this end, we propose a robust graph domain adaptive learning framework RGDAL which exploits an information-theoretic principle to filter the noisy factors for cross-network node classification. Specifically, RGDAL utilizes graph convolutional network (GCN) with constrained graph mutual information and an adversarial learning component to learn noise-resistant and domain-invariant graph representations. To overcome the difficulties of estimating the mutual information for the non independent and identically distributed (non-i.i.d.) graph structured data, we design a dynamic neighborhood sampling strategy that can discretize the graph and incorporate the graph structural information for mutual information estimation. Experimental results on two real-world graph datasets demonstrate that RGDAL shows better robustness for cross-network node classification compared with the SOTA graph adaptive learning methods.", "Keywords": "Graph domain adaptive learning ; Node classification ; Graph neural networks ; Mutual information", "DOI": "10.1016/j.knosys.2022.109852", "PubYear": 2022, "Volume": "257", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of New South Wales, Sydney, 2032, NSW, Australia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology, Deakin University, Burwood, 3125, VIC, Australia"}, {"AuthorId": 3, "Name": "Taotao Cai", "Affiliation": "School of Computing, Macquarie University, Macquarie Park, 2109, NSW, Australia"}, {"AuthorId": 4, "Name": "Xiangyu Song", "Affiliation": "School of Information Technology, Deakin University, Burwood, 3125, VIC, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, University of New South Wales, Sydney, 2032, NSW, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Center for Frontier AI Research (CFAR), Agency for Science, Technology and Research (A*STAR), Singapore"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology, Deakin University, Burwood, 3125, VIC, Australia;Corresponding author"}], "References": [{"Title": "Deep fusion of multimodal features for social media retweet time prediction", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "4", "Page": "1027", "JournalTitle": "World Wide Web"}, {"Title": "JKT: A joint graph convolutional network based Deep Knowledge Tracing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "510", "JournalTitle": "Information Sciences"}, {"Title": "Bi-CLKT: Bi-Graph Contrastive Learning based Knowledge Tracing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "241", "Issue": "", "Page": "108274", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Sentiment analysis and topic modeling for COVID-19 vaccine discussions", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "25", "Issue": "3", "Page": "1067", "JournalTitle": "World Wide Web"}, {"Title": "Learning to rank method combining multi-head self-attention with conditional generative adversarial nets", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "", "Page": "100205", "JournalTitle": "Array"}]}, {"ArticleId": 96382180, "Title": "Predefined‐time distributed event‐triggered algorithms for resource allocation", "Abstract": "The resource allocation problem in a distributed multi‐agent system is considered in this study. First, the authors develop a predefined‐time distributed algorithm and analyse its convergence analysis using the <PERSON><PERSON><PERSON>nov stability theory, in which the local constraint is ensured by a differential projection operator. Thus, a predefined time is obtained by a time‐varying time‐based generator. Second, to reduce the communication consumption between agents, the authors develop a static as well as a dynamic‐based event‐triggered control scheme, where the information broadcast only occurs at some discrete time instants. Moreover, the three proposed algorithms converge precisely to the global optimal solution. Besides, the Zeno behaviour is excluded in the above static and dynamic event‐triggered mechanisms. Finally, the authors test the proposed algorithms' efficiency based on the provided numerical examples.", "Keywords": "", "DOI": "10.1049/cps2.12036", "PubYear": 2022, "Volume": "7", "Issue": "4", "JournalId": 36956, "JournalTitle": "IET Cyber-Physical Systems: Theory & Applications", "ISSN": "", "EISSN": "2398-3396", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Optimization for Industrial Equipment Ministry of Education Dalian University of Technology  Dalian Liaoning China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The State Key Laboratory of Synthetical Automation for Process Industries Northeastern University  Shenyang Liaoning China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The State Key Laboratory of Synthetical Automation for Process Industries Northeastern University  Shenyang Liaoning China"}], "References": [{"Title": "Distributed optimization for economic power dispatch with event‐triggered communication", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "6", "Page": "2412", "JournalTitle": "Asian Journal of Control"}, {"Title": "An exponentially convergent distributed algorithm for resource allocation problem", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "2", "Page": "1072", "JournalTitle": "Asian Journal of Control"}, {"Title": "Consensus-based economic dispatch algorithm in a microgrid via distributed event-triggered control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "15", "Page": "3044", "JournalTitle": "International Journal of Systems Science"}, {"Title": "Quantized event-triggered communication based multi-agent system for distributed resource allocation optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "336", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96382182, "Title": "BioBulkFoundary: a customized webserver for exploring biosynthetic potentials of bulk chemicals", "Abstract": "Summary \n Advances in metabolic engineering have boosted the production of bulk chemicals, resulting in tons of production volumes of some bulk chemicals with very low prices. A decrease in the production cost and overproduction of bulk chemicals makes it necessary and desirable to explore the potential to synthesize higher-value products from them. It is also useful and important for society to explore the use of design methods involving synthetic biology to increase the economic value of these bulk chemicals. Therefore, we developed ‘BioBulkFoundary’, which provides an elaborate analysis of the biosynthetic potential of bulk chemicals based on the state-of-art exploration of pathways to synthesize value-added chemicals, along with associated comprehensive technology and economic database into a user-friendly framework.\n \n \n Availability and implementation \n Freely available on the web at http://design.rxnfinder.org/biobulkfoundary/.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btac640", "PubYear": 2022, "Volume": "38", "Issue": "22", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "CAS Key Laboratory of Computational Biology, Shanghai Institute of Nutrition and Health, University of Chinese Academy of Sciences, Chinese Academy of Sciences, Shanghai, 200031, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Wuhan LifeSynther Science and Technology Co. Limited, Wuhan, 430000, China."}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "CAS Key Laboratory of Computational Biology, Shanghai Institute of Nutrition and Health, University of Chinese Academy of Sciences, Chinese Academy of Sciences, Shanghai, 200031, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ecological Systems Design, Institute of Environmental Engineering, ETH Zurich, Zurich, 8093, Switzerland."}, {"AuthorId": 5, "Name": "Mengying Han", "Affiliation": "CAS Key Laboratory of Computational Biology, Shanghai Institute of Nutrition and Health, University of Chinese Academy of Sciences, Chinese Academy of Sciences, Shanghai, 200031, China."}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CAS Key Laboratory of Computational Biology, Shanghai Institute of Nutrition and Health, University of Chinese Academy of Sciences, Chinese Academy of Sciences, Shanghai, 200031, China."}], "References": []}, {"ArticleId": 96382195, "Title": "Lung cancer subtype diagnosis using weakly-paired multi-omics data", "Abstract": "Motivation \n Cancer subtype diagnosis is crucial for its precise treatment and different subtypes need different therapies. Although the diagnosis can be greatly improved by fusing multiomics data, most fusion solutions depend on paired omics data, which are actually weakly paired, with different omics views missing for different samples. Incomplete multiview learning-based solutions can alleviate this issue but are still far from satisfactory because they: (i) mainly focus on shared information while ignore the important individuality of multiomics data and (ii) cannot pick out interpretable features for precise diagnosis.\n \n \n Results \n We introduce an interpretable and flexible solution (LungDWM) for Lung cancer subtype Diagnosis using Weakly paired Multiomics data. LungDWM first builds an attention-based encoder for each omics to pick out important diagnostic features and extract shared and complementary information across omics. Next, it proposes an individual loss to jointly extract the specific information of each omics and performs generative adversarial learning to impute missing omics of samples using extracted features. After that, it fuses the extracted and imputed features to diagnose cancer subtypes. Experiments on benchmark datasets show that LungDWM achieves a better performance than recent competitive methods, and has a high authenticity and good interpretability.\n \n \n Availability and implementation \n The code is available at http://www.sdu-idea.cn/codes.php?name=LungDWM.\n \n \n Supplementary information \n Supplementary data are available at Bioinformatics online.", "Keywords": "", "DOI": "10.1093/bioinformatics/btac643", "PubYear": 2022, "Volume": "38", "Issue": "22", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Ji'nan, 250100, China. ;SDU-NTU Joint Centre for AI Research, Shandong University, Ji'nan, 250100, China."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software, Shandong University, Ji'nan, 250100, China. ;SDU-NTU Joint Centre for AI Research, Shandong University, Ji'nan, 250100, China."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "SDU-NTU Joint Centre for AI Research, Shandong University, Ji'nan, 250100, China."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Big Data Centre, University Teknologi Malaysia, Skudai, 81310, Malaysia."}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Software, Shandong University, Ji'nan, 250100, China. ;SDU-NTU Joint Centre for AI Research, Shandong University, Ji'nan, 250100, China."}], "References": [{"Title": "Subtype-GAN: a deep learning approach for integrative cancer subtyping of multi-omics data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "16", "Page": "2231", "JournalTitle": "Bioinformatics"}, {"Title": "Multi-omics data integration by generative adversarial network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "1", "Page": "179", "JournalTitle": "Bioinformatics"}]}, {"ArticleId": 96382427, "Title": "Survey: Exploiting Data Redundancy for Optimization of Deep Learning", "Abstract": "<p>Data redundancy is ubiquitous in the inputs and intermediate results of Deep Neural Networks (DNN). It offers many significant opportunities for improving DNN performance and efficiency and has been explored in a large body of work. These studies have scattered in many venues across several years. The targets they focus on range from images to videos and texts, and the techniques they use to detect and exploit data redundancy also vary in many aspects. There is not yet a systematic examination and summary of the many efforts, making it difficult for researchers to get a comprehensive view of the prior work, the state of the art, differences and shared principles, and the areas and directions yet to explore. This article tries to fill the void. It surveys hundreds of recent papers on the topic, introduces a novel taxonomy to put the various techniques into a single categorization framework, offers a comprehensive description of the main methods used for exploiting data redundancy in improving multiple kinds of DNNs on data, and points out a set of research opportunities for future to explore.</p>", "Keywords": "Data redundancy; representation redundancy; deep neural network; convolutional neural network; transformer", "DOI": "10.1145/3564663", "PubYear": 2023, "Volume": "55", "Issue": "10", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, North Carolina State University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, William & Mary, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Northeastern University, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, North Carolina State University, USA"}], "References": [{"Title": "Effective node selection technique towards sparse learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "10", "Page": "3239", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 96382429, "Title": "A review of recent image processing techniques for liver tumour diagnosis from CT images", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJIM.2022.10050792", "PubYear": 2022, "Volume": "1", "Issue": "1", "JournalId": 35524, "JournalTitle": "International Journal of Image Mining", "ISSN": "2055-6039", "EISSN": "2055-6047", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96382436, "Title": "A new windings design for improving single-phase induction motor performance", "Abstract": "<p> Single-phase induction (asynchronous) motors are widely used at home. These motors have two windings and usually operate at a lower performance than 3-phase asynchronous motors which have three windings. For this reason, this study aims to design a new winding of a single-phase asynchronous motor by increasing the number of phases in the motor windings in order to increase the performance of the motor. This research was focused on 36 slot capacitor-start capacitor-run asynchronous motor. The design used 4 non-identical windings in the motor, where three windings acted as auxiliary windings and one winding acted as main winding. The rated current of the designed motor winding was 2.74 A for the main winding and 3.15 A for the auxiliary winding. The performance of the designed motor compared to the traditional single-phase asynchronous motor with the same structure of stator, rotor, and rated current. A traditional single-phase asynchronous motor had data: 1 HP, 220 V, 8.3 A, 1440 RPM, 50 Hz, and 4 poles. The results of this study indicated that the designed motor operated with power factors almost close to unity and had higher output power, torque, and efficiency than the traditional single-phase asynchronous motors. </p>", "Keywords": "Capacitor motor;Four-phase winding;Induction motor performance;Motor winding design;Single-phase induction motor", "DOI": "10.11591/ijece.v12i6.pp5789-5798", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Technology Padang"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Technology Padang"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Technology Padang"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Institute of Technology Padang"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Technology Padang"}, {"AuthorId": 6, "Name": "Sepannur Bandri", "Affiliation": "Institute of Technology Padang"}], "References": []}, {"ArticleId": 96382437, "Title": "Simulation and performance analysis of self-powered piezoelectric energy harvesting system for low power applications", "Abstract": "<p> Energy harvesting is a process of extracting energy from surrounding environments. The extracted energy is stored in the supply power for various applications like wearable, wireless sensor, and internet of thing (IoT) applications. The electricity generation using conventional approaches is very costly and causes more pollution in the environmental surroundings. In this manuscript, an energy-efficient, self-powered battery-less piezoelectric-based energy harvester (PE-EH) system is modeled using maximum power point tracking (MPPT) module. The MPPT is used to track the optimal voltage generated by the piezoelectric (PE) sensor and stored across the capacitor. The proposed PE system is self-operated without additional microarchitecture to harvest the Power. The experimental simulation results for the overall PE-EH systems are analyzed for different frequency ranges with variable input source vibrations. The optimal voltage storage across the storing capacitor varies from 1.12 to 1.6 V. The PE-EH system can harvest power up to 86 µW without using any voltage source and is suitable for low-power applications. The proposed PE-EH module is compared with the existing similar EH system with better improvement in harvested power. </p>", "Keywords": "Battery-less;Energy harvesting;Maximum power point tracking;Modeling;Piezoelectric sensors;Rectifier;Self-power", "DOI": "10.11591/ijece.v12i6.pp5861-5871", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dr. <PERSON><PERSON><PERSON> Institute of Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Dr. <PERSON><PERSON><PERSON> Institute of Technology"}], "References": []}, {"ArticleId": 96382438, "Title": "Design of an efficient binary phase-shift keying based IEEE 802.15.4 transceiver architecture and its performance analysis", "Abstract": "<p> The IEEE 802.15.4 physical layer (PHY) standard is one of the communication standards with wireless features by providing low-power and low-data rates in wireless personal area network (WPAN) applications. In this paper, an efficient IEEE 802.15.4 digital transceiver hardware architecture is designed using the binary phase-shift keying (BPSK) technique. The transceiver mainly has transmitter and receiver modules along with the error calculation unit. The BPSK modulation and demodulation are designed using a digital frequency synthesizer (DFS). The DFS is used to generate the in-phase (I) and quadrature-phase (Q) signals and also provides better system performance than the conventional voltage-controlled oscillator (VCO) and look up table (LUT) based memory methods. The differential encoding-decoding mechanism is incorporated to recover the bits effectively and to reduce the hardware complexity. The simulation results are illustrated and used to find the error bits. The design utilizes less chip area, works at 268.2 MHz, and consumes 108 mW of total power. The IEEE 802.15.4 transceiver provides a latency of 3.5 clock cycles and works with a throughput of 76.62 Mbps. The bit error rate (BER) of 2×10-5 is achieved by the proposed digital transceiver and is suitable for real-time applications. The work is compared with existing similar approaches with better improvement in performance parameters. </p>", "Keywords": "Binary phase-shift keying modulation;Digital transceiver;Field-programmable gate array;IEEE 802.15.4;Physical layer", "DOI": "10.11591/ijece.v12i6.pp6332-6340", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>nda Sagar College of Engineering"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON>nda Sagar College of Engineering"}], "References": []}, {"ArticleId": 96382439, "Title": "Robusta coffee leaf diseases detection based on MobileNetV2 model", "Abstract": "<p> Indonesia is a major exporter and producer of coffee, and coffee cultivation adds to the nation's economy. Despite this, coffee remains vulnerable to several plant diseases that may result in significant financial losses for the agricultural industry. Traditionally, plant diseases are detected by expert observation with the naked eye. Traditional methods for managing such diseases are arduous, time-consuming, and costly, especially when dealing with expansive territories. Using a model based on transfer learning and deep learning model, we present in this study a technique for classifying Robusta coffee leaf disease photos into healthy and unhealthy classes. The MobileNetV2 network serves as the model since its network design is simple. Therefore, it is likely that the suggested approach will be deployed further on mobile devices. In addition, the transfer learning and experimental learning paradigms. Because it is such a lightweight net, the MobileNetV2 system serves as the foundational model. Results on Robusta coffee leaf disease datasets indicate that the suggested technique can achieve a high level of accuracy, up to 99.93%. The accuracy of other architectures besides MobileNetV2 such as DenseNet169 is 99.74%, ResNet50 architecture is 99.41%, and InceptionResNetV2 architecture is 99.09%. </p>", "Keywords": "Convolutional neural network;Deep learning;Image detection;MobileNetV2;Plant diseases recognition", "DOI": "10.11591/ijece.v12i6.pp6675-6683", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Hasnur"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Politeknik Hasnur"}], "References": []}, {"ArticleId": 96382483, "Title": "An efficient scanning algorithm for photovoltaic systems under partial shading", "Abstract": "<p> This paper proposes a new technique of maximum power point tracking (MPPT) for a photovoltaic (PV) system connected to three phase grids under partial shading condition (PSC), based on a new combined perturb and observe (P&amp;O) with scanning algorithm. This new algorithm main advantages are the high-speed tracking compared to existing algorithms, high accuracy and simplicity which makes it ideal for hardware implementation. Simulation was carried on MATLAB/Simulink. Results showed the effectiveness in speed and accuracy of our algorithm over the existing ones either during standard condition (STC) or PSC. Furthermore, conventional direct power control (DPC) was applied to synchronize successfully the injected power with the grid, which makes our algorithm global and works efficiently under severe conditions. </p>", "Keywords": "Direct power control;Partial shading conditions;Perturb and observe;Photovoltaic system;Scanning algorithm;Synchronization;Three phase grids connected", "DOI": "10.11591/ijece.v12i6.pp5799-5807", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hassan II University of Casablanca"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Hassan II University of Casablanca"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Hassan II University of Casablanca"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University Hassan I"}], "References": []}, {"ArticleId": 96382490, "Title": "General and Domain-adaptive Chinese Spelling Check with Error-consistent Pretraining", "Abstract": "<p> The lack of label data is one of the significant bottlenecks for Chinese Spelling Check (CSC). Existing researches use the automatic generation method by exploiting unlabeled data to expand the supervised corpus. However, there is a big gap between the real input scenario and automatically generated corpus. Thus, we develop a competitive general speller ECSpell which adopts the E rror C onsistent masking strategy to create data for pretraining. This error consistency masking strategy is used to specify the error types of automatically generated sentences consistent with the real scene. The experimental result indicates that our model outperforms previous state-of-the-art models on the general benchmark. </p><p> Moreover, spellers often work within a particular domain in real life. Due to many uncommon domain terms, experiments on our built domain specific datasets show that general models perform terribly. Inspired by the common practice of input methods, we propose to add an alterable user dictionary to handle the zero-shot domain adaption problem. Specifically, we attach a U ser D ictionary guided inference module ( UD ) to a general token classification based speller. Our experiments demonstrate that ECSpell <sup> UD </sup> , namely ECSpell combined with UD, surpasses all the other baselines broadly, even approaching the performance on the general benchmark. </p>", "Keywords": "Chinese spelling check; domain-adaptive; user dictionary", "DOI": "10.1145/3564271", "PubYear": 2023, "Volume": "22", "Issue": "5", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Lv", "Affiliation": "School of Computer Science and Technology, Soochow University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Institution of Artificial Intelligence, Soochow University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Soochow University, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Soochow University, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Soochow University, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Institution of Artificial Intelligence, Soochow University, China"}], "References": []}, {"ArticleId": 96382491, "Title": "Deep learning for cancer tumor classification using transfer learning and feature concatenation", "Abstract": "<p> Deep convolutional neural networks (CNNs) represent one of the state-of-the-art methods for image classification in a variety of fields. Because the number of training dataset images in biomedical image classification is limited, transfer learning with CNNs is frequently applied. Breast cancer is one of most common types of cancer that causes death in women. Early detection and treatment of breast cancer are vital for improving survival rates. In this paper, we propose a deep neural network framework based on the transfer learning concept for detecting and classifying breast cancer histopathology images. In the proposed framework, we extract features from images using three pre-trained CNN architectures: VGG-16, ResNet50, and Inception-v3, and concatenate their extracted features, and then feed them into a fully connected (FC) layer to classify benign and malignant tumor cells in the histopathology images of the breast cancer. In comparison to the other CNN architectures that use a single CNN and many conventional classification methods, the proposed framework outperformed all other deep learning architectures and achieved an average accuracy of 98.76%. </p>", "Keywords": "Breast cancer;Cancer tumor;Classification;Deep learning;Feature concatenation;Transfer learning", "DOI": "10.11591/ijece.v12i6.pp6736-6743", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Al-Azhar University"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Al-Azhar University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Egyptian Atomic Energy Authority"}], "References": []}, {"ArticleId": 96382805, "Title": "Fuzzy rule-based models via space partition and information granulation", "Abstract": "<p>Fuzzy rule-based model (FRBM) has attracted significant attention in various fields due to its accuracy and high level of interpretability. In this study, two granular Takagi–Sugeno (T–S) FRBMs are designed by employing fuzzy space partition and the principle of allocation of information granularity. The designed models considering different abstraction levels concentrate on the balance of interpretability and accuracy and reflect the rational granularity of rules’ output. According to the layered partition results, the granular T–S FRBMs are generated under two different granularity allocation strategies: uniformly and non-uniformly allocation of information granularity to the T–S FRBM’s parameters. Meanwhile, a unified index incorporating the principle of justifiable granularity is introduced for serving as examining the performance of the granular T–S FRBM and judging whether the obtained partitions need to be further divided in the next layer. The designed models with different types of allocating information granularity are compared with state-of-the-art granular rule modeling way on synthetic datasets and publicly available datasets to illustrate the study’s effectiveness. Under the same information granularity allocation strategy, the designed models in this study can achieve prediction intervals with sound robustness and granular performance. As an application example, a real-world dataset is analyzed to exhibit the potential practicality of the designed models.</p>", "Keywords": "Fuzzy rule-based model; Principle of justifiable granularity; Information granules; Space partition", "DOI": "10.1007/s00521-022-06974-3", "PubYear": 2022, "Volume": "34", "Issue": "19", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Dalian Maritime University, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Science, Dalian Maritime University, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Dalian Maritime University, Dalian, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science, Dalian Maritime University, Dalian, China"}], "References": [{"Title": "Granulated deep learning and Z-numbers in motion detection and object recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "21", "Page": "16533", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Information granule-based classifier: A development of granular imputation of missing data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "214", "Issue": "", "Page": "106737", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A hierarchical local-model tree for predicting roof displacement in longwall tailgates", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "21", "Page": "14909", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A development framework of granular prototypes with an allocation of information granularity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "573", "Issue": "", "Page": "154", "JournalTitle": "Information Sciences"}, {"Title": "Interval prediction of short-term building electrical load via a novel multi-objective optimized distributed fuzzy model", "Authors": "Hongchang Sun; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "22", "Page": "15357", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Adaptive fuzzy modeling of interval-valued stream data and application in cryptocurrencies prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "10", "Page": "7149", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 96382818, "Title": "Preemptive and non-preemptive scheduling on two unrelated parallel machines", "Abstract": "<p>In this paper, for the problem of minimizing the makespan on two unrelated parallel machines we compare the quality of preemptive and non-preemptive schedules. It is known that there exists an optimal preemptive schedule with at most two preemptions. We show that the power of preemption, i.e., the ratio of the makespan computed for the best non-preemptive schedule to the makespan of the optimal preemptive schedule is at most 3/2. We also show that the ratio of the makespan computed for the best schedule with at most one preemption to the makespan of the optimal preemptive schedule is at most 9/8. For both models, we present polynomial-time algorithms that find schedules of the required quality. The established bounds match those previously known for a less general problem with two uniform machines. We have found one point of difference between the uniform and unrelated machines: if an optimal preemptive schedule contains exactly one preemption then the ratio of the makespan computed for the best non-preemptive schedule to the makespan of the optimal preemptive schedule is at most 4/3 if the two machines are uniform and remains 3/2 if the machines are unrelated.</p>", "Keywords": "Unrelated parallel machines; Power of preemption; Quality of a single preemption", "DOI": "10.1007/s10951-022-00753-7", "PubYear": 2022, "Volume": "25", "Issue": "6", "JournalId": 9977, "JournalTitle": "Journal of Scheduling", "ISSN": "1094-6136", "EISSN": "1099-1425", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computing and Mathematical Sciences, University of Greenwich, London, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Welling, UK"}], "References": []}, {"ArticleId": 96382842, "Title": "Research on filtering and measurement algorithms based on human point cloud data", "Abstract": "<p>To obtain the data of noncontact measurement of the human body, the depth camera is used to collect the human body, and the obtained initial data are transformed into the required point cloud data for processing through coordinate transformation, and then the collected three-dimensional point cloud data are preprocessed. The preprocessing includes point cloud downsampling, point cloud filtering, plane segmentation, outlier removal, point cloud surface estimation, and so forth. A new solution for point cloud filtering is proposed, which combines sliding least squares and unification and radius filtering. Compared with the traditional filtering, the effect is smoother, and finally the complete outline of the human body is obtained, and then the human body is measured. The results show that the human body data measured by this scheme is within the range of the relevant standard measurement accuracy.</p>", "Keywords": "depth camera;measurement;point cloud data;point cloud filtering", "DOI": "10.1002/int.23085", "PubYear": 2022, "Volume": "37", "Issue": "12", "JournalId": 2999, "JournalTitle": "International Journal of Intelligent Systems", "ISSN": "0884-8173", "EISSN": "1098-111X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Guangdong University of Technology  Guangzhou China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Li", "Affiliation": "School of Automation Guangdong University of Technology  Guangzhou China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Guangdong University of Technology  Guangzhou China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Automation Guangdong University of Technology  Guangzhou China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Guangdong University of Technology  Guangzhou China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Guangdong University of Technology  Guangzhou China"}], "References": [{"Title": "Multi-sensor information fusion based on machine learning for real applications in human activity recognition: State-of-the-art and research challenges", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "80", "Issue": "", "Page": "241", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 96382980, "Title": "The Effect of the Flipped Classroom on the Academic Achievement in Physics and Motivation Among Students of Secondary Stage in Jordan", "Abstract": "Learning physics outside the classroom has experienced low consideration in the past, but it has gained popularity in recent times. The current study aimed to explore the effect of using the flipped classroom strategy in teaching physics on academic achievement and motivating secondary school students in Jordan. A total of 84 students from the eleventh grade of secondary education from the Jordanian Ministry of Education participated in the study, divided into two groups, the experimental group (41) and the control group (43). Educational materials and study tools; comprehending scientific flipped classroom strategy designed and charted content, the Physics academic achievement test as well as the Physics motivation scale; were developed and prepared. Study instruments were appropriately statistically pre-checked for validity and reliability. The results revealed that there were statistically significant differences between the experimental group and the control group in favor of the experimental group. The results also showed that there were no statistically significant differences between the mean scores of the experimental group students and the scores of the control group students in the post-application of the motivation scale due to the teaching method. The study recommends further studies on the flipped classroom strategy. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Academic achievement; flipped classroom strategy; motivation; secondary education", "DOI": "10.18576/isl/120116", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96382992, "Title": "Cybersecurity Awareness in African Higher Education Institutions: A Case Study of Sudan", "Abstract": "The crisis caused by the rapid spread of the coronavirus (COVID-19) has imposed a swift and profound change on teaching and learning methods. Consequently, most higher education institutions around the world, including African higher education institutions, have moved from face-to-face teaching to online learning and teaching, which has made the use of the internet by university students necessary and obligatory regardless of the risks associated with unsafe use. This quick move to online teaching and learning has exposed African universities to a greater risk of cybercrime. This prompted the researchers to investigate the cybersecurity awareness levels among undergraduate students at African higher education institutions based in the case country, Sudan. In an exploratory research approach, a survey was conducted on a convenience sample of 1,200 undergraduate students at six public universities in Sudan. The results show that most undergraduate students in Sudan higher educational institutions have low cybersecurity awareness levels. Further investigation using inferential statistics reveals that male students at the universities in Sudan have slightly higher levels of cybersecurity awareness than female students. Most of the participants believe that cybersecurity should be taught in schools; they are also willing to learn about cybersecurity. In addition, the results showed that students with advanced computer skills significantly differ from students with intermediate or basic computer skills in practicing cybersecurity. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Africa; awareness; COVID-19; cybersecurity", "DOI": "10.18576/isl/120113", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96382993, "Title": "Evaluation of Media Ethics Courses in Jordanian Universities and their Impacts on Students’ Social Responsibility", "Abstract": "The article aims to evaluate the impact of media ethics courses considering the values of social responsibility among students, and their relevance to the reality of practice from the point of view of media faculties staff in Jordanian universities. The study adopted the descriptive method and the interview (2) and questionnaire (39) of professors of media ethics courses. The main results of the article are: Most topics of media ethics courses adopted in faculties of media in Jordanian universities are principles and concepts of media ethics and codes (4.88). More indicators of fulfilling the media ethics courses with social responsibility is providing the student with cognitive, analytical, and applied skills in media practices (4.46). The most prominent weaknesses in media ethics courses in Jordanian universities are a Lack of legal culture (4.61), as well as a Lack of lecturers, specializing in media ethics. The media professors’ aspirations are to concentrate on “adopting the local law of each country as a main topic well as media ethics in the curriculum of early levels of school” (4.78). The researcher recommends that ministries of higher education and universities update and adopt media ethics curricula at various academic levels. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "media ethics courses; media honor codes; social responsibility values; universities", "DOI": "10.18576/isl/120108", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96382996, "Title": "The Mediating Role of Passion in the Relationships Between Academic Identity and Psychological Flourishing of University Students", "Abstract": "This research aims at identifying the relationships between academic identity and both academic passion and psychological flourishing and exploring the mediating role of academic passion between academic identity and psychological flourishing. Four hundred and twelve female university students at the Faculty of Islamic Girls, Assuit Al-Azhar University, Egypt. participated in the research (age mean= 20.24, SD= 1.13). The academic identity scale (by the researcher), the academic passion scale (by <PERSON><PERSON> et al.,2003) and the psychological flourishing scale (by <PERSON><PERSON> et al.,2010) were applied to the participants. Data were analyzed by Pearsons correlation coefficients and path analysis. Results revealed a statistically significant positive correlation between academic identity and both academic passion and psychological flourishing. Besides, results demonstrated the mediating role of academic passion between academic identity and psychological flourishing. These results can be utilized in developing psychological flourishing among university students and providing those in charge of educational planning and policies with some practical results that are useful in developing academic identity and passion through training and educational programs and strategies. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Academic Identity; Academic Passion; Female University Students; Mediating Role; Psychological Flourishing", "DOI": "10.18576/isl/120125", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383001, "Title": "Factors Related to Performance Problems of Distance Education Among Primary School Pupils Under COVID-19 and Confrontation Mechanisms (A Field Study)", "Abstract": "The present research paper aims to identify the factors related to performance problems of distance education among primary school pupils under COVID-19 and their confrontation mechanisms. It adopted the descriptive analytical method and applied a questionnaire to collect data. The population of the search covered all primary school teachers numbered (13364) in (487) primary public schools in Riyadh, whereas the sample included (695) teachers in public schools in Riyadh. The results showed that the high effect size of the problems-related social factors scored (4.18), the high effect size of the problems-related economic factors scored (4.09), the high effect size of the problems-related cultural factors scored (4.18). Moreover, the arithmetic means of the most significant confrontation mechanisms of the social factors ranged (4.30:4.87), the economic factors ranged (4.26:4.69), and the cultural factors ranged (4.22:4.80). The study recommends highlighting the means of getting alternatives of the comprehensive vision required for decision-makers when developing planning scenarios for confronting the social, economic, and cultural factors related to the performance problems of distance education among primary school pupils under COVID-19. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "COVID-19; Distance education; Performance problems", "DOI": "10.18576/isl/120104", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383002, "Title": "Nature Is the Teacher: Rational Ecocentrism for Ultimate Self-Realization in “Moby Dick” and “The Old Man and the Sea”", "Abstract": "<PERSON><PERSON><PERSON> and <PERSON> offer a wide range of diversity of human beings’ conduct towards nature through their novels The Old Man and the Sea and <PERSON><PERSON>, respectively. Both novels describe the characters’ interactions with nature at distinct ecocentric levels. While one protagonist shows positive interactivity towards nature (positively ecocentric), another shows a considerable disregard for nature’s majesty by placing oneself first (Anthropocentric). This paper seeks to investigate the characterization of the protagonist in the two novels through an ecocentric perceptive. In essence, the characters’ interactivity with nature will reveal their characters. This discourse places them side by side to demonstrate their similarities and dissimilarities in character, will and determination, and obsession in achieving their quest. The paper is an ecocentric analysis of the two novels to conclude whether the quest of the protagonists shows their preoccupations with revenge or self-realization. © 2023 NSP.", "Keywords": "Anthropocentric; Ecocentric; Hemingway; Melville; Nature", "DOI": "10.18576/isl/120137", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383079, "Title": "Optimal and Fully Connected Deep Neural Networks Based Classification Model for Unmanned Aerial Vehicle Using Hyperspectral Remote Sensing Images", "Abstract": "Unmanned Aerial Vehicle (UAV) is treated as an effective technique for gathering high resolution aerial images. The UAV based aerial image collection is highly preferred due to its inexpensive and effective nature. However, automatic classification of aerial images poses a major challenging issue in the design of UAV, which could be handled by the deep learning (DL) models. This study designs a novel UAV assisted DL based image classification model (UAVDL-ICM) for Industry 4.0 environment. The proposed UAVDL-ICM technique involves an ensemble of voting based three DL models, namely Residual network (ResNet), Inception with ResNetv2, and Densely Connected Networks (DenseNet). Also, the hyperparameter tuning of these DL models takes place using a genetic programming (GP) approach. Finally, Oppositional Water Wave Optimization (OWWO) with Fully Connected Deep Neural networks (FCDNN) is employed for the classification of aerial images. A wide range of simulations takes place and the results are examined in terms of different parameters. A detailed comparative study highlighted the betterment of the UAVDL-ICM technique compared to other recent approaches. Le drone (UAV) est considéré comme une technique efficace pour recueillir des images aériennes de haute résolution. La collection d’images aériennes basée sur des drones est préférée en raison de sa nature peu coûteuse et efficace. Toutefois, la classification automatique d’images aériennes pose un problème majeur dans le design des drones, qui peut être géré par des modèles d’apprentissage profond (DL). Cette étude conçoit un nouveau modèle de classification d’images UAV (UAVDL-ICM) basé sur des modèles DL pour l’environnement industriel 4.0. La technique UAVDL-ICM proposée implique un ensemble de trois modèles DL basés sur le vote, à savoir le réseau résiduel (ResNet), l’Inception avec ResNetv2 et les réseaux densément connectés (DenseNet). En outre, le réglage de l’hyperparamètre de ces modèles DL s’effectue à l’aide de l’approche de programmation génétique (GP). Enfin, l’optimisation oppositionnelle des ondes d’eau (OWWO) avec des réseaux de neurones profonds entièrement connectés (FCDNN) est utilisée pour la classification des images aériennes. Un large éventail de simulations a eu lieu et les résultats sont examinés au moyen de différents paramètres. Une étude comparative détaillée a mis en évidence l’amélioration de la technique UAVDL-ICM par rapport à d’autres approches récentes. Acknowledgment The authors extend their appreciation to the Deanship of Scientific Research at King Khalid University for funding this work through Large Groups Project under grant number (180/43). Princess Nourah bint Abdulrahman University Researchers Supporting Project number (PNURSP2022R235), Princess Nourah bint Abdulrahman University, Riyadh, Saudi Arabia. The authors would like to thank the Deanship of Scientific Research at Umm Al-Qura University for supporting this work by Grant Code: (22UQU4340237DSR34). Disclosure statement No potential conflict of interest was reported by the author(s). The manuscript was written through contributions of all authors. All authors have given approval to the final version of the manuscript. Ethical approval This article does not contain any studies with human participants performed by any of the authors. Data availability statement Data sharing not applicable to this article as no datasets were generated during the current study.", "Keywords": "", "DOI": "10.1080/07038992.2022.2116566", "PubYear": 2022, "Volume": "48", "Issue": "5", "JournalId": 4152, "JournalTitle": "Canadian Journal of Remote Sensing", "ISSN": "0703-8992", "EISSN": "1712-7971", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer and Self Development, Prince <PERSON> University, AlKharj, Saudi Arabia"}, {"AuthorId": 2, "Name": "Jaber S. <PERSON>", "Affiliation": "Department of Industrial Engineering, College of Engineering at Alqunfudah, Umm Al-Qura University, Makkah, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Computer and Information Sciences, Princess <PERSON><PERSON><PERSON>man University, P. O. Box 84428, Riyadh, 11671, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computers and Information Technology, Taif University, P. O. Box 11099, Taif, 21944, Saudi Arabia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Information Systems, College of Science & Art at Mahayil, King Khalid University, Abha, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Self Development, Prince <PERSON> University, AlKharj, Saudi Arabia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer and Self Development, Prince <PERSON> University, AlKharj, Saudi Arabia"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Department of Information System, College of Computer Engineering and Sciences, Prince <PERSON> University, AlKharj, Saudi Arabia"}], "References": [{"Title": "Aerial image classification by learning quality-aware spatial pyramid model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "271", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Wetland Mapping Using Multi-Spectral Satellite Imagery and Deep Convolutional Neural Networks: A Case Study in Newfoundland and Labrador, Canada", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "47", "Issue": "2", "Page": "243", "JournalTitle": "Canadian Journal of Remote Sensing"}, {"Title": "EANDC: An explainable attention network based deep adaptive clustering model for mental health treatment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "106", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 96383092, "Title": "Development and evaluation of a search-and-rescue robot Paripreksya 2.0 for WRS 2020", "Abstract": "In recent years, the influence of robotics in search-and-rescue missions has become relevant. Robots can provide human rescue teams with valuable information about the disaster area, track victims and speed up the rescue operation. The disaster robot category challenge at the World Robot Summit (WRS) is a platform where emerging technologies in search-and-rescue robot developments are evaluated. This paper describes the design, development and implementation of a teleoperated all-terrain search-and-rescue robot, Paripreksya 2.0, for participation in the Standard Disaster Challenge. The robot was integrated with a 4-DoF robotic manipulator for performing dexterity tasks along with tracks and four assistive flippers for locomotion. The design of the robot was done in SolidWorks and the stability constraints of the robot while climbing stairs and slopes are also calculated. The software of the robot was built on the ROS framework. The robot was designed to perform various tasks including staircase climbing, negotiation of obstacles, opening and closing of valves, manoeuvring, plant inspection, etc. Our team has secured the best-in-class dexterity award at the WRS 2020 challenge. The performance of the robot was evaluated using the scores from different challenges held at the WRS competition. GRAPHICAL", "Keywords": "WRS 2020 ; dexterity ; ROS ; robotic arm ; disaster response", "DOI": "10.1080/01691864.2022.2117575", "PubYear": 2022, "Volume": "36", "Issue": "21", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "HuT Labs, Department of Electronics and Communication Engineering, Amrita Vishwa Vidyapeetham, Amritapuri, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HuT Labs, Department of Electronics and Communication Engineering, Amrita Vishwa Vidyapeetham, Amritapuri, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "HuT Labs, Department of Electronics and Communication Engineering, Amrita Vishwa Vidyapeetham, Amritapuri, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HuT Labs, Department of Electronics and Communication Engineering, Amrita Vishwa Vidyapeetham, Amritapuri, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HuT Labs, Department of Electronics and Communication Engineering, Amrita Vishwa Vidyapeetham, Amritapuri, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "HuT Labs, Department of Electronics and Communication Engineering, Amrita Vishwa Vidyapeetham, Amritapuri, India"}], "References": [{"Title": "Plant inspection by using a ground vehicle and an aerial robot: lessons learned from plant disaster prevention challenge in world robot summit 2018", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "34", "Issue": "2", "Page": "104", "JournalTitle": "Advanced Robotics"}, {"Title": "Self-E: a self-driving wheelchair for elders and physically challenged", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "4", "Page": "477", "JournalTitle": "International Journal of Intelligent Robotics and Applications"}]}, {"ArticleId": 96383127, "Title": "Distance Learning in Emergencies: Social and Pedagogical Relations in the Context of the COVID-19 Pandemic", "Abstract": "The current study aims to investigate social and pedagogical relations during the experience of distance learning in emergencies, in the context of the spread of the Covid-19 pandemic. The study identifies the impact of the transition towards this new system in rebuilding social and pedagogical links between professors and students through a case study at Ajman University, as one of the institutions of higher education in the United Arab Emirates. The study relied on the quantitative approach and the sample of the study consisted of (730) students selected in a simple random manner. The study found that most of the sample members had advanced infrastructure that would enable them to keep up with the transition to the distance learning system, and that the level of access to electronic tools and distance learning platforms and the ability to deal with them were high. The study also found that the distance learning system increases the level of interaction, discussion and communication between students and between students and teachers, which extends beyond the lesson period, as an attempt to replace direct interaction. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Covid-19; Emergencies; pandemic Distance Learning; Social and pedagogical relations", "DOI": "10.18576/isl/120119", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383278, "Title": "Detection of Left Ventricular Cavity from Cardiac MRI Images Using Faster R-CNN", "Abstract": "The automatic localization of the left ventricle (LV) in short-axis magnetic resonance (MR) images is a required step to process cardiac images using convolutional neural networks for the extraction of a region of interest (ROI). The precise extraction of the LV’s ROI from cardiac MRI images is crucial for detecting heart disorders via cardiac segmentation or registration. Nevertheless, this task appears to be intricate due to the diversities in the size and shape of the LV and the scattering of surrounding tissues across different slices. Thus, this study proposed a region-based convolutional network (Faster R-CNN) for the LV localization from short-axis cardiac MRI images using a region proposal network (RPN) integrated with deep feature classification and regression. The model was trained using images with corresponding bounding boxes (labels) around the LV, and various experiments were applied to select the appropriate layers and set the suitable hyper-parameters. The experimental findings show that the proposed model was adequate, with accuracy, precision, recall, and F1 score values of 0.91, 0.94, 0.95, and 0.95, respectively. This model also allows the cropping of the detected area of LV, which is vital in reducing the computational cost and time during segmentation and classification procedures. Therefore, it would be an ideal model and clinically applicable for diagnosing cardiac diseases. © 2023 Tech Science Press. All rights reserved.", "Keywords": "automatic left ventricle localization; Cardiac short-axis MRI images; deep learning models; faster R-CNN", "DOI": "10.32604/cmc.2023.031900", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Electronic Engineering, Universiti Tun Hussein <PERSON>, <PERSON><PERSON>, Jo<PERSON>, <PERSON><PERSON>, 86400, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, College of Computer Science and Information Systems, Najran University, Najran, 61441, Saudi Arabia"}], "References": [{"Title": "Left ventricle landmark localization and identification in cardiac MRI by deep metric learning-assisted CNN regression", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "399", "Issue": "", "Page": "153", "JournalTitle": "Neurocomputing"}, {"Title": "A Lightweight CNN Based on Transfer Learning for COVID-19 Diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "1", "Page": "1123", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Prediction of Cardiovascular Disease Using Machine Learning Technique—A Modern Approach", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "1", "Page": "855", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383279, "Title": "Deep Learning-Based Program-Wide Binary Code Similarity for Smart Contracts", "Abstract": "Recently, security issues of smart contracts are arising great attention due to the enormous financial loss caused by vulnerability attacks. There is an increasing need to detect similar codes for hunting vulnerability with the increase of critical security issues in smart contracts. Binary similarity detection that quantitatively measures the given code diffing has been widely adopted to facilitate critical security analysis. However, due to the difference between common programs and smart contract, such as diversity of bytecode generation and highly code homogeneity, directly adopting existing graph matching and machine learning based techniques to smart contracts suffers from low accuracy, poor scalability and the limitation of binary similarity on function level. Therefore, this paper investigates graph neural network to detect smart contract binary code similarity at the program level, where we conduct instruction-level normalization to reduce the noise code for smart contract pre-processing and construct contract control flow graphs to represent smart contracts. In particular, two improved Graph Convolutional Network (GCN) and Message Passing Neural Network (MPNN) models are explored to encode the contract graphs into quantitatively vectors, which can capture the semantic information and the program-wide control flow information with temporal orders. Then we can efficiently accomplish the similarity detection by measuring the distance between two targeted contract embeddings. To evaluate the effectiveness and efficient of our proposed method, extensive experiments are performed on two real-world datasets, i.e., smart contracts from Ethereum and Enterprise Operation System (EOS) blockchain-based platforms. The results show that our proposed approach outperforms three state-of-the-art methods by a large margin, achieving a great improvement up to 6.1% and 17.06% in accuracy. © 2023 Tech Science Press. All rights reserved.", "Keywords": "neural network; similarity detection; Smart contract", "DOI": "10.32604/cmc.2023.028058", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Harbin Engineering University, Harbin, 150000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Engineering University, Harbin, 150000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sanya, Sanya, 572000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Harbin Engineering University, Harbin, 150000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Harbin Engineering University, Harbin, 150000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "University of Alberta, Edmonton, T5J4P6, Canada"}], "References": [{"Title": "A comprehensive survey on smart contract construction and execution: paradigms, tools, and systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "100179", "JournalTitle": "Patterns"}, {"Title": "Multi-Head Attention Graph Network for Few Shot Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "2", "Page": "1505", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Robust 3-D Medical Watermarking Based on Wavelet Transform for Data Protection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "3", "Page": "1043", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Robust Reversible Audio Watermarking Scheme for Telemedicine and Privacy Protection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "2", "Page": "3035", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383280, "Title": "Characteristic of Line-of-Sight in Infrastructure-to-Vehicle Visible Light Communication Using MIMO Technique", "Abstract": "Visible Light Communication (VLC) technology is aggressive research for the next generation of communication. Currently, Radio Frequency (RF) communication has crowed spectrum. An Intelligent Transportation System (ITS) has been improved in the communication network for Vehicle-to-Vehicle (V2 V), Vehicle-to-Infrastructure (V2I), and Infrastructure-to-Vehicle (I2 V) by using the visible light spectrum instead of the RF spectrum. This article studies the characterization of Line-of-Sight (LOS) optical performance in an Outdoor Wireless Visible Light Communication (OWVLC) system employing a Multiple-Input Multiple-Output (MIMO) technique for I2 V communications in ITS regulations. We design the new configuration of the OWVLC-I2 V system, which is an alternative approach to communication for I2 V system at nighttime. The results show the Channel Impulse Response (CIR) of the LOS links in visible light communication for I2 V system in ITS by investigating the receiver on the vehicle moving along the coverage communication area. Furthermore, the OWVLC-I2 V system using the MIMO technique depicts the performance of throughput and Bit Error Rate (BER) vs. vehicle speed while the vehicle passes a street light. © 2023 Tech Science Press. All rights reserved.", "Keywords": "channel impulse response; Infrastructure to vehicle communication; intelligent transportation system; visible light communication", "DOI": "10.32604/cmc.2023.032569", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Suranaree University of Technology, Nakhon Ratchasima, 30000, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Telecommunication Engineering, Suranaree University of Technology, Nakhon Ratchasima, 30000, Thailand"}], "References": [{"Title": "Fine-grained vehicle type classification using lightweight convolutional neural network with feature optimization and joint learning strategy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "20", "Page": "30803", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 96383282, "Title": "Improved Key Node Recognition Method of Social Network Based on PageRank Algorithm", "Abstract": "The types and functions of social networking sites are becoming more abundant with the prevalence of self-media culture, and the number of daily active users of social networking sites represented by <PERSON><PERSON> and <PERSON><PERSON><PERSON> continues to expand. There are key node users in social networks. Compared with ordinary users, their influence is greater, their radiation range is wider, and their information transmission capabilities are better. The key node users playimportant roles in public opinion monitoring and hot event prediction when evaluating the criticality of nodes in social networking sites. In order to solve the problems of incomplete evaluation factors, poor recognition rate and low accuracy of key nodes of social networking sites, this paper establishes a social networking site key node recognition algorithm (SNSKNIS) based on PageRank (PR) algorithm, and evaluates the importance of social networking site nodes in combination with the influence of nodes and the structure of nodes in social networks. This article takes the Sina Weibo platform as an example, uses the key node identification algorithm system of social networking sites to discover the key nodes in the social network, analyzes its importance in the social network, and displays it visually. © 2023 Tech Science Press. All rights reserved.", "Keywords": "key node; PageRank algorithm; Social networking site", "DOI": "10.32604/cmc.2023.029180", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Police Institute, Nanjing, 210000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jiangsu Police Institute, Nanjing, 210000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing Police Station, Nanjing, 210000, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jiangsu Police Institute, Nanjing, 210000, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "System Consulting Pty Ltd., Sydney, 201101, Australia"}], "References": [{"Title": "Improved Short-video User Impact Assessment Method Based on PageRank Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "2", "Page": "437", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "A PageRank-Based WeChat User Impact Assessment Algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "3", "Page": "89", "JournalTitle": "Journal of New Media"}, {"Title": "Social Network Rumor Recognition Based on Enhanced <PERSON><PERSON>", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "3", "Page": "99", "JournalTitle": "Journal of New Media"}, {"Title": "Advanced Community Identification Model for Social Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "2", "Page": "1687", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "CGraM: Enhanced Algorith<PERSON> for Community Detection in Social Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "749", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Deformation Expression of Soft Tissue Based on BP Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "32", "Issue": "2", "Page": "1041", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "A Lightweight CNN Based on Transfer Learning for COVID-19 Diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "1", "Page": "1123", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Automatic Botnet Attack Identification Based on Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "2", "Page": "3847", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383283, "Title": "Vertical Pod Autoscaling in Kubernetes for Elastic Container Collaborative燜ramework", "Abstract": "Kubernetes is an open-source container management tool which automates container deployment, container load balancing and container(de)scaling, including Horizontal Pod Autoscaler (HPA), Vertical Pod Autoscaler (VPA). HPA enables flawless operation, interactively scaling the number of resource units, or pods, without downtime. Default Resource Metrics, such as CPU and memory use of host machines and pods, are monitored by Kubernetes. Cloud Computing has emerged as a platform for individuals beside the corporate sector. It provides cost-effective infrastructure, platform and software services in a shared environment. On the other hand, the emergence of industry 4.0 brought new challenges for the adaptability and infusion of cloud computing. As the global work environment is adapting constituents of industry 4.0 in terms of robotics, artificial intelligence and IoT devices, it is becoming eminent that one emerging challenge is collaborative schematics. Provision of such autonomous mechanism that can develop, manage and operationalize digital resources like CoBots to perform tasks in a distributed and collaborative cloud environment for optimized utilization of resources, ensuring schedule completion. Collaborative schematics are also linked with Bigdata management produced by large scale industry 4.0 setups. Different use cases and simulation results showed a significant improvement in Pod CPU utilization, latency, and throughput over Kubernetes environment. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Autoscaling; container; kubernetes; orchestration; pods; query optimization", "DOI": "10.32604/cmc.2023.032474", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, National College of Business Administration and Economics, Lahore, 54000, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, National College of Business Administration and Economics, Lahore, 54000, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Digital, Technologies and Art, Staffordshire UniversityST42DF, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Lahore Garrison University, Lahore, 54000, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Higher Colleges of Technology, Abu Dhabi, United Arab Emirates"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Enterprise Computing, Skyline University College, Sharjah, United Arab Emirates"}], "References": [{"Title": "Serverless execution of scientific workflows: Experiments with HyperFlow, AWS Lambda and Google Cloud Functions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "502", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Manufacturing big data ecosystem: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "101861", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Key influencing factors of the Kubernetes auto-scaler for computing-intensive microservice-native cloud-based applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "102734", "JournalTitle": "Advances in Engineering Software"}, {"Title": "Modeling and solving cloud service purchasing in multi-cloud environments", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "147", "Issue": "", "Page": "113165", "JournalTitle": "Expert Systems with Applications"}, {"Title": "On Byzantine fault tolerance in multi-master Kubernetes clusters", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "", "Page": "407", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "IntMA: Dynamic Interaction-aware resource allocation for containerized microservices in cloud environments", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "111", "Issue": "", "Page": "101785", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Geo-distributed efficient deployment of containers with Kubernetes", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "161", "JournalTitle": "Computer Communications"}, {"Title": "Intelligent Software-Defined Network for Cognitive Routing Optimization using Deep Extreme Learning Machine Approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "1", "Page": "1269", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Simulation, Modeling, and Optimization of Intelligent Kidney Disease Predication Empowered with Computational Intelligence Approaches", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "2", "Page": "1399", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Prediction of Cloud Ranking in a Hyperconverged Cloud Ecosystem Using Machine Learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "3129", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Modelling Intelligent Driving Behaviour Using Machine Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "3", "Page": "3061", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Semantic Analysis of Urdu English Tweets Empowered by Machine Learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "175", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Live Migration of Virtual Machines Using a Mamdani Fuzzy Inference System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "2", "Page": "3019", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383284, "Title": "Replication Strategy with Comprehensive Data Center Selection Method in燙loud Environments", "Abstract": "As the amount of data continues to grow rapidly, the variety of data produced by applications is becoming more affluent than ever. Cloud computing is the best technology evolving today to provide multi-services for the mass and variety of data. The cloud computing features are capable of processing, managing, and storing all sorts of data. Although data is stored in many high-end nodes, either in the same data centers or across many data centers in cloud, performance issues are still inevitable. The cloud replication strategy is one of best solutions to address risk of performance degradation in the cloud environment. The real challenge here is developing the right data replication strategy with minimal data movement that guarantees efficient network usage, low fault tolerance, and minimal replication frequency. The key problem discussed in this research is inefficient network usage discovered during selecting a suitable data center to store replica copies induced by inadequate data center selection criteria. Hence, to mitigate the issue, we proposed Replication Strategy with a comprehensive Data Center Selection Method (RS-DCSM), which can determine the appropriate data center to place replicas by considering three key factors: Popularity, space availability, and centrality. The proposed RS-DCSM was simulated using CloudSim and the results proved that data movement between data centers is significantly reduced by 14% reduction in overall replication frequency and 20% decrement in network usage, which outperformed the current replication strategy, known as Dynamic Popularity aware Replication Strategy (DPRS) algorithm. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Cloud computing; data center merits; data replication; replica placement; replication algorithm", "DOI": "10.32604/cmc.2023.020764", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "M. <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Universiti Putra Malaysia, Selangor, Serdang, 43400, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Universiti Putra Malaysia, Selangor, Serdang, 43400, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Computer Science and Information Technology, Universiti Putra Malaysia, Selangor, Serdang, 43400, Malaysia"}], "References": [{"Title": "A novel dynamic data replication strategy to improve access efficiency of cloud storage", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "3", "Page": "405", "JournalTitle": "Information Systems and e-Business Management"}, {"Title": "An efficient fault tolerant workflow scheduling approach using replication heuristics and checkpointing in the cloud", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "136", "Issue": "", "Page": "14", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Energy aware edge computing: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "556", "JournalTitle": "Computer Communications"}, {"Title": "Cost effective dynamic data placement for efficient access of social networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "82", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Dynamic replication factor model for Linux containers-based cloud systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "9", "Page": "7219", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A multi-objective optimized replication using fuzzy based self-defense algorithm for cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "102811", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Toward security as a service: A trusted cloud service architecture with policy customization", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "149", "Issue": "", "Page": "76", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Hierarchical data replication strategy to improve performance in cloud computing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "2", "Page": "152501", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Achieving query performance in the cloud via a cost-effective data replication strategy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "7", "Page": "5437", "JournalTitle": "Soft Computing"}, {"Title": "Consensus-based data replication protocol for distributed cloud", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "8", "Page": "8653", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Towards reducing delegation overhead in replication-based verification: An incentive-compatible rational delegation computing scheme", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "568", "Issue": "", "Page": "286", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 96383285, "Title": "Optimization Scheme of Trusted Task Offloading in IIoT Scenario Based爋n燚QN", "Abstract": "With the development of the Industrial Internet of Things (IIoT), end devices (EDs) are equipped with more functions to capture information. Therefore, a large amount of data is generated at the edge of the network and needs to be processed. However, no matter whether these computing tasks are offloaded to traditional central clusters or mobile edge computing (MEC) devices, the data is short of security and may be changed during transmission. In view of this challenge, this paper proposes a trusted task offloading optimization scheme that can offer low latency and high bandwidth services for IIoT with data security. Blockchain technology is adopted to ensure data consistency. Meanwhile, to reduce the impact of low throughput of blockchain on task offloading performance, we design the processes of consensus and offloading as a Markov decision process (MDP) by defining states, actions, and rewards. Deep reinforcement learning (DRL) algorithm is introduced to dynamically select offloading actions. To accelerate the optimization, we design a novel reward function for the DRL algorithm according to the scale and computational complexity of the task. Experiments demonstrate that compared with methods without optimization, our mechanism performs better when it comes to the number of task offloading and throughput of blockchain. © 2023 Tech Science Press. All rights reserved.", "Keywords": "blockchain; deep reinforcement learning (DRL) network; industrial internet of things (IIoT); mobile-edge computing (MEC); Task offloading", "DOI": "10.32604/cmc.2023.031750", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, 100876, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, 100876, China"}, {"AuthorId": 3, "Name": "Siyuan Sun", "Affiliation": "China United Network Communications Corporation, Beijing Branch100800, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing University of Posts and Telecommunications, Beijing, 100876, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Information Science and Technology University, Beijing, 100192, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Quebec at Montreal, Montreal, H2X 3X2, Canada"}], "References": [{"Title": "Data Secure Storage Mechanism of Sensor Networks Based on Blockchain", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "3", "Page": "2365", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "An Optimization Scheme for Task Offloading and Resource Allocation in Vehicle Edge Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "163", "JournalTitle": "Journal on Internet of Things"}, {"Title": "A blockchain-based Roadside Unit-assisted authentication and key agreement protocol for Internet of Vehicles", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "149", "Issue": "", "Page": "29", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "A Storage Optimization Scheme for Blockchain Transaction Databases", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "3", "Page": "521", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "State-Based Offloading Model for Improving Response Rate of IoT Services", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "3", "Page": "3721", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Investigating and Modelling of Task Offloading Latency in Edge-Cloud Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "3", "Page": "4143", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Survey on Task Offloading in Multi-access Edge Computing", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "102225", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Exploring and Modelling IoT Offloading Policies in Edge Cloud Environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "2", "Page": "611", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": ********, "Title": "Metaheuristics-based Clustering with Routing Technique for Lifetime Maximization in Vehicular Networks", "Abstract": "Recently, vehicular ad hoc networks (VANETs) finds applicability in different domains such as security, rescue operations, intelligent transportation systems (ITS), etc. VANET has unique features like high mobility, limited mobility patterns, adequate topology modifications, and wireless communication. Despite the benefits of VANET, scalability is a challenging issue which could be addressed by the use of cluster-based routing techniques. It enables the vehicles to perform intercluster communication via chosen CHs and optimal routes. The main drawback of VANET network is the network unsteadiness that results in minimum lifetime. In order to avoid reduced network lifetime in VANET, this paper presents an enhanced metaheuristics based clustering with multihop routing technique for lifetime maximization (EMCMHR-LM) in VANET. The presented EMCMHR-LM model involves the procedure of arranging clusters, cluster head (CH) selection, and route selection appropriate for VANETs. The presented EMCMHR-LM model uses slime mold optimization based clustering (SMO-C) technique to group the vehicles into clusters. Besides, an enhanced wild horse optimization based multihop routing (EWHO-MHR) protocol by the optimization of network parameters. The presented EMCMHR-LM model is simulated using Network Simulator (NS3) tool and the simulation outcomes reported the enhanced performance of the proposed EMCMHR-LM technique over the other models. © 2023 Tech Science Press. All rights reserved.", "Keywords": "clustering; fitness function; metaheuristics; multihop routing; route selection; Scalability; VANET", "DOI": "10.32604/cmc.2023.031962", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences, Saveetha University, Saveetha Nagar, Thandalam, Tamilnadu, Chennai, 602105, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Saveetha School of Engineering, Saveetha Institute of Medical and Technical Sciences, Saveetha University, Saveetha Nagar, Thandalam, Tamilnadu, Chennai, 602105, India"}], "References": [{"Title": "QMM-VANET: An efficient clustering algorithm based on QoS and monitoring of malicious vehicles in vehicular ad hoc networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "165", "Issue": "", "Page": "110561", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Fine-grained vehicle type classification using lightweight convolutional neural network with feature optimization and joint learning strategy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "20", "Page": "30803", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Optimized Node Clustering based on Received Signal Strength with Particle Ordered-filter Routing Used in VANET", "Authors": "<PERSON>. <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "2", "Page": "262", "JournalTitle": "Webology"}, {"Title": "The importance of selecting clustering parameters in VANETs: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100392", "JournalTitle": "Computer Science Review"}, {"Title": "A Multi-Feature Learning Model with Enhanced Local Attention for Vehicle Re-Identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3549", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A novel approach to select cluster head and optimising the data transmission in VANET", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "21", "Issue": "3/4", "Page": "235", "JournalTitle": "International Journal of Advanced Intelligence Paradigms"}]}, {"ArticleId": 96383293, "Title": "A U-Net-Based CNN Model for Detection and Segmentation of Brain Tumor", "Abstract": "Human brain consists of millions of cells to control the overall structure of the human body. When these cells start behaving abnormally, then brain tumors occurred. Precise and initial stage brain tumor detection has always been an issue in the field of medicines for medical experts. To handle this issue, various deep learning techniques for brain tumor detection and segmentation techniques have been developed, which worked on different datasets to obtain fruitful results, but the problem still exists for the initial stage of detection of brain tumors to save human lives. For this purpose, we proposed a novel U-Net-based Convolutional Neural Network (CNN) technique to detect and segmentizes the brain tumor for Magnetic Resonance Imaging (MRI). Moreover, a 2-dimensional publicly available Multimodal Brain Tumor Image Segmentation (BRATS2020) dataset with 1840 MRI images of brain tumors has been used having an image size of 240 × 240 pixels. After initial dataset preprocessing the proposed model is trained by dividing the dataset into three parts i.e., testing, training, and validation process. Our model attained an accuracy value of 0.98 % on the BRATS2020 dataset, which is the highest one as compared to the already existing techniques. © 2023 Tech Science Press. All rights reserved.", "Keywords": "brain tumor; convolutional neural network; magnetic resonance images; segmentation; U-net", "DOI": "10.32604/cmc.2023.031695", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Sahiwal Campus, Sahiwal, 57000, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Sahiwal Campus, Sahiwal, 57000, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Sahiwal Campus, Sahiwal, 57000, Pakistan"}, {"AuthorId": 4, "Name": "Nazi<PERSON> <PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "Samar <PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Survey on the Explainability of Supervised Machine Learning", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "70", "Issue": "", "Page": "245", "JournalTitle": "Journal of Artificial Intelligence Research"}]}, {"ArticleId": 96383294, "Title": "Jellyfish Search Optimization with Deep Learning Driven Autism Spectrum Disorder Classification", "Abstract": "Autism spectrum disorder (ASD) is regarded as a neurological disorder well-defined by a specific set of problems associated with social skills, recurrent conduct, and communication. Identifying ASD as soon as possible is favourable due to prior identification of ASD permits prompt interferences in children with ASD. Recognition of ASD related to objective pathogenic mutation screening is the initial step against prior intervention and efficient treatment of children who were affected. Nowadays, healthcare and machine learning (ML) industries are combined for determining the existence of various diseases. This article devises a Jellyfish Search Optimization with Deep Learning Driven ASD Detection and Classification (JSODL-ASDDC) model. The goal of the JSODL-ASDDC algorithm is to identify the different stages of ASD with the help of biomedical data. The proposed JSODL-ASDDC model initially performs min-max data normalization approach to scale the data into uniform range. In addition, the JSODL-ASDDC model involves JSO based feature selection (JFSO-FS) process to choose optimal feature subsets. Moreover, Gated Recurrent Unit (GRU) based classification model is utilized for the recognition and classification of ASD. Furthermore, the Bacterial Foraging Optimization (BFO) assisted parameter tuning process gets executed to enhance the efficacy of the GRU system. The experimental assessment of the JSODL-ASDDC model is investigated against distinct datasets. The experimental outcomes highlighted the enhanced performances of the JSODL-ASDDC algorithm over recent approaches. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Autism spectral disorder; biomedical data; data classification; deep learning; feature selection; hyperparameter optimization; machine learning", "DOI": "10.32604/cmc.2023.032586", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, Aditya Engineering College, Andhra Pradesh, Surampalem, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science & Engineering, <PERSON><PERSON> Engineering College, Uttar Pradesh, Ghaziabad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Human Resource Management, Moscow Aviation Institute, Moscow, Russian Federation"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Vignan’s Institute of Information Technology, Visakhapatnam, 530049, India"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "College of Technical Engineering, The Islamic University, Najaf, Iraq"}], "References": [{"Title": "Automated detection and classification of fundus diabetic retinopathy images using synergic deep learning model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "133", "Issue": "", "Page": "210", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Analysis and Detection of Autism Spectrum Disorder Using Machine Learning Techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "994", "JournalTitle": "Procedia Computer Science"}, {"Title": "RETRACTED ARTICLE: Deep learning based an automated skin lesion segmentation and intelligent classification model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "3", "Page": "3245", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Autism Spectrum Disorder Diagnosis using Optimal Machine Learning Methods", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "252", "JournalTitle": "International Journal of Advanced Computer Science and Applications"}, {"Title": "Synergic Deep Learning for Smart Health Diagnosis of COVID-19 for Connected Living and Smart Cities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "22", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "A study of brain networks for autism spectrum disorder classification using resting-state functional connectivity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "8", "Issue": "", "Page": "100290", "JournalTitle": "Machine Learning with Applications"}]}, {"ArticleId": 96383296, "Title": "Intelligent Machine Learning Enabled Retinal Blood Vessel Segmentation and Classification", "Abstract": "Automated segmentation of blood vessels in retinal fundus images is essential for medical image analysis. The segmentation of retinal vessels is assumed to be essential to the progress of the decision support system for initial analysis and treatment of retinal disease. This article develops a new Grasshopper Optimization with Fuzzy Edge Detection based Retinal Blood Vessel Segmentation and Classification (GOFED-RBVSC) model. The proposed GOFED-RBVSC model initially employs contrast enhancement process. Besides, GOAFED approach is employed to detect the edges in the retinal fundus images in which the use of GOA adjusts the membership functions. The ORB (Oriented FAST and Rotated BRIEF) feature extractor is exploited to generate feature vectors. Finally, Improved Conditional Variational Auto Encoder (ICAVE) is utilized for retinal image classification, shows the novelty of the work. The performance validation of the GOFED-RBVSC model is tested using benchmark dataset, and the comparative study highlighted the betterment of the GOFED-RBVSC model over the recent approaches. © 2023 Tech Science Press. All rights reserved.", "Keywords": "blood vessel segmentation; deep learning; Edge detection; image classification; retinal fundus images", "DOI": "10.32604/cmc.2023.030872", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Joint learning of visual and spatial features for edit propagation from a single image", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "3", "Page": "469", "JournalTitle": "The Visual Computer"}, {"Title": "Local and nonlocal constraints for compressed sensing video and multi-view image recovery", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "406", "Issue": "", "Page": "34", "JournalTitle": "Neurocomputing"}, {"Title": "Fuzzy based image edge detection algorithm for blood vessel detection in retinal images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106452", "JournalTitle": "Applied Soft Computing"}, {"Title": "Detecting COVID-19 Patients in X-Ray Images Based on MAI-Nets", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "1607", "JournalTitle": "International Journal of Computational Intelligence Systems"}]}, {"ArticleId": 96383297, "Title": "Automatic Diagnosis of COVID-19 Patients from Unstructured Data Based on a Novel Weighting Scheme", "Abstract": "The extraction of features from unstructured clinical data of Covid-19 patients is critical for guiding clinical decision-making and diagnosing this viral disease. Furthermore, an early and accurate diagnosis of COVID-19 can reduce the burden on healthcare systems. In this paper, an improved Term Weighting technique combined with Parts-Of-Speech (POS) Tagging is proposed to reduce dimensions for automatic and effective classification of clinical text related to Covid-19 disease. Term Frequency-Inverse Document Frequency (TF-IDF) is the most often used term weighting scheme (TWS). However, TF-IDF has several developments to improve its drawbacks, in particular, it is not efficient enough to classify text by assigning effective weights to the terms in unstructured data. In this research, we proposed a modification term weighting scheme: RTF-C-IEF and compare the proposed model with four extraction methods: TF, TF-IDF, TF-IHF, and TF-IEF. The experiment was conducted on two new datasets for COVID-19 patients. The first dataset was collected from government hospitals in Iraq with 3053 clinical records, and the second dataset with 1446 clinical reports, was collected from several different websites. Based on the experimental results using several popular classifiers applied to the datasets of Covid-19, we observe that the proposed scheme RTF-C-IEF achieves is a consistent performer with the best scores in most of the experiments. Further, the modified RTF-C-IEF proposed in the study outperformed the original scheme and other employed term weighting methods in most experiments. Thus, the proper selection of term weighting scheme among the different methods improves the performance of the classifier and helps to find the informative term. © 2023 Tech Science Press. All rights reserved.", "Keywords": "clinical text; Covid-19; machine learning; natural language processing; TWS", "DOI": "10.32604/cmc.2023.032671", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Evaluation of feature selection methods for text classification with small datasets using multiple criteria decision-making methods", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105836", "JournalTitle": "Applied Soft Computing"}, {"Title": "An improved term weighting scheme for text classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Sentiment analysis on product reviews based on weighted word embeddings and deep neural networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "23", "Page": "e5909", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Machine learning based approaches for detecting COVID-19 using clinical text data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "3", "Page": "731", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Enhancing web service clustering using Length Feature Weight Method for service description document vector space representation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113682", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Several alternative term weighting methods for text representation and classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "207", "Issue": "", "Page": "106399", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Automatic Surveillance of Pandemics Using Big Data and Text Mining", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "1", "Page": "303", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "A Lightweight CNN Based on Transfer Learning for COVID-19 Diagnosis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "72", "Issue": "1", "Page": "1123", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Bidirectional convolutional recurrent neural network architecture with group-wise enhancement mechanism for text sentiment classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "5", "Page": "2098", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Lexicalized Dependency Paths Based Supervised Learning for Relation Extraction", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "43", "Issue": "3", "Page": "861", "JournalTitle": "Computer Systems Science and Engineering"}]}, {"ArticleId": 96383301, "Title": "Calf Posture Recognition Using Convolutional Neural Network", "Abstract": "Dairy farm management is crucial to maintain the longevity of the farm, and poor dairy youngstock or calf management could lead to gradually deteriorating calf health, which often causes premature death. This was found to be the most neglected part among the management workflows in Malaysia and has caused continuous loss over the recent years. Calf posture recognition is one of the effective methods to monitor calf behaviour and health state, which can be achieved by monitoring the calf behaviours of standing and lying where the former depicts active calf, and the latter, passive calf. Calf posture recognition module is an important component of some automated calf monitoring systems, as the system requires the calf to be in a standing posture before proceeding to the next stage of monitoring, or at the very least, to monitor the activeness of the calves. Calf posture such as standing or resting can easily be distinguished by human eye, however, to be recognized by a machine, it will require more complicated frameworks, particularly one that involves a deep learning neural networks model. Large number of high-quality images are required to train a deep learning model for such tasks. In this paper, multiple Convolutional Neural Network (CNN) architectures were compared, and the residual network (ResNet) model (specifically, ResNet-50) was ultimately chosen due to its simplicity, great performance, and decent inference time. Two ResNet-50 models having the exact same architecture and configuration have been trained on two different image datasets respectively sourced by separate cameras placed at different angle. There were two camera placements to use for comparison because camera placements can significantly impact the quality of the images, which is highly correlated to the deep learning model performance. After model training, the performance for both CNN models were 99.7% and 99.99% accuracies, respectively, and is adequate for a real-time calf monitoring system. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Calf posture; deep learning; machine vision; transfer learning", "DOI": "10.32604/cmc.2023.029277", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Malaysia-Japan International Institute of Technology, Universiti Teknologi Malaysia, Kuala Lumpur, 54100, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "A Multi-Feature Learning Model with Enhanced Local Attention for Vehicle Re-Identification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "3549", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383311, "Title": "Short-term power load forecasting based on gray relational analysis and support vector machine optimized by artificial bee colony algorithm", "Abstract": "<p> Short-term power load forecasting is essential in ensuring the safe operation of power systems and a prerequisite in building automated power systems. Short-term power load demonstrates substantial volatility because of the effect of various factors, such as temperature and weather conditions. However, the traditional short-term power load forecasting method ignores the influence of various factors on the load and presents problems of limited nonlinear mapping ability and weak generalization ability to unknown data. Therefore, a short-term power load forecasting method based on GRA and ABC-SVM is proposed in this study. First, the Pearson correlation coefficient method is used to select critical influencing factors. Second, the gray relational analysis (GRA) method is utilized to screen similar days in the history, construct a rough set of similar days, perform K -means clustering on the rough sets of similar days, and further construct the set of similar days. The artificial bee colony (ABC) algorithm is then utilized to optimize penalty coefficient and kernel function parameters of the support vector machine (SVM). Finally, the above method is applied on the basis of actual load data in Nanjing for simulation verification, and the results show the effectiveness of the proposed method. </p>", "Keywords": "Artificial bee colony algorithm;Gray relational analysis;Short-term power load forecasting;Similar days;Support vector machine", "DOI": "10.7717/peerj-cs.1108", "PubYear": 2022, "Volume": "8", "Issue": "", "JournalId": 18478, "JournalTitle": "PeerJ Computer Science", "ISSN": "", "EISSN": "2376-5992", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Energy Saving and Controlling in Power System of Liaoning Province, Shenyang Institute of Engineering, Shenyang, Liaoning, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Energy Saving and Controlling in Power System of Liaoning Province, Shenyang Institute of Engineering, Shenyang, Liaoning, China"}, {"AuthorId": 3, "Name": "Haibo Li", "Affiliation": "Key Laboratory of Energy Saving and Controlling in Power System of Liaoning Province, Shenyang Institute of Engineering, Shenyang, Liaoning, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Energy Saving and Controlling in Power System of Liaoning Province, Shenyang Institute of Engineering, Shenyang, Liaoning, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Yingkou Power Supply Company, State Grid Liaoning Electric Power Co., Ltd., Yingkou, Liaoning, China"}], "References": [{"Title": "A weight initialization method based on neural network with asymmetric activation function", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "483", "Issue": "", "Page": "171", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 96383394, "Title": "Implementation of face recognition system using BioCryptosystem as template protection scheme", "Abstract": "This paper has designed and implemented a secure face recognition system using novel Bio-Cryptographic template protection schemes. The implementation of the proposed system has been divided into three components. The face regions are detected from the input images in the first component. Then some discriminant and distinctive feature extraction techniques are applied to extract features from the facial region. In the second component, the extracted features undergo a classification task to verify or identify the person based on their face biometric. The Hu<PERSON>man coding technique has been incorporated with the biometric face feature as BioCryptosystem for template protection scheme in the third component. The proposed BioCryptosystem works at the feature level in two steps: dictionary-based and user-key-based approaches for encryption followed by decryption processes. During experimentation, three benchmark facial databases, CVL, CASIA-FaceV5, and FERET have been employed. The performances have been compared with some existing state-of-the-art methods concerning each database which shows the superiority of the proposed system. Moreover, some comparisons have been performed for the security analysis of the employed BioCryptosystem, which shows that the proposed system takes less time for authentication and generates longer and stronger keys than other existing Bio-cryptographic techniques.", "Keywords": "BioCryptosystem ; <PERSON><PERSON><PERSON> coding ; Encryption ; Face recognition ; Key generation ; Identification", "DOI": "10.1016/j.jisa.2022.103317", "PubYear": 2022, "Volume": "70", "Issue": "", "JournalId": 3508, "JournalTitle": "Journal of Information Security and Applications", "ISSN": "2214-2126", "EISSN": "2214-2134", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Aliah University, Kolkata, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Aliah University, Kolkata, India;Corresponding author"}], "References": [{"Title": "Multimodal biometric cryptosystem for human authentication using fingerprint and ear", "Authors": "Padira S. V. <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "1-2", "Page": "659", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Cancelable Biometrics: a comprehensive survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3403", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A comprehensive survey on the biometric recognition systems based on physiological and behavioral modalities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "113114", "JournalTitle": "Expert Systems with Applications"}, {"Title": "ENHANCING home security through visual CRYPTOGRAPHY", "Authors": "<PERSON><PERSON>; Dr <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "", "Page": "103355", "JournalTitle": "Microprocessors and Microsystems"}]}, {"ArticleId": 96383407, "Title": "Unsupervised Deep learning-based Feature Fusion Approach for Detection and Analysis of COVID-19 using X-ray and CT Images", "Abstract": "Aims: <p>This study investigates an unsupervised deep learning-based feature fusion approach for the detection and analysis of COVID-19 using chest X-ray (CXR) and Computed tomography (CT) images.</p> Background: <p> The outbreak of COVID-19 has affected millions of people all around the world and the disease is diagnosed by the reverse transcription-polymerase chain reaction (RT-PCR) test which suffers from a lower viral load, and sampling error, etc . Computed tomography (CT) and chest X-ray (CXR) scans can be examined as most infected people suffer from lungs infection. Both CT and CXR imaging techniques are useful for the COVID-19 diagnosis at an early stage and it is an alternative to the RT-PCR test. </p> Objective: <p>The manual diagnosis of CT scans and CXR images are labour-intensive and consumes a lot of time. To handle this situation, many AI-based solutions are researched including deep learning-based detection models, which can be used to help the radiologist to make a better diagnosis. However, the availability of annotated data for COVID-19 detection is limited due to the need for domain expertise and expensive annotation cost. Also, most existing state-of-the-art deep learning-based detection models follow a supervised learning approach. Therefore, in this work, we have explored various unsupervised learning models for COVID-19 detection which does not need a labelled dataset.</p> Methods: <p>In this work, we propose an unsupervised deep learning-based COVID-19 detection approach that incorporates the feature fusion method for performance enhancement. Four different sets of experiments are run on both CT and CXR scan datasets where convolutional autoencoders, pre-trained CNNs, hybrid, and PCA-based models are used for feature extraction and K-means and GMM techniques are used for clustering.</p> Results: <p>The maximum accuracy of 84% is achieved by the model Autoencoder3-ResNet50 (GMM) on the CT dataset and for the CXR dataset, both Autoencoder1-VGG16 (KMeans and GMM) models achieved 70% accuracy.</p> Conclusion: <p>Our proposed deep unsupervised learning, feature fusion-based COVID-19 detection approach achieved promising results on both datasets. It also outperforms four well-known existing unsupervised approaches.</p>", "Keywords": "", "DOI": "10.2174/18750362-v15-e2207290", "PubYear": 2022, "Volume": "15", "Issue": "1", "JournalId": 16317, "JournalTitle": "The Open Bioinformatics Journal", "ISSN": "1875-0362", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "Deep transfer learning-based automated detection of COVID-19 from lung CT scan slices", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>; Bijaya Ketan Panigrahi", "PubYear": 2021, "Volume": "51", "Issue": "1", "Page": "571", "JournalTitle": "Applied Intelligence"}, {"Title": "COVID-19 detection and disease progression visualization: Deep learning on chest X-rays for classification and coarse localization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "2", "Page": "1010", "JournalTitle": "Applied Intelligence"}, {"Title": "OptCoNet: an optimized convolutional neural network for an automatic diagnosis of COVID-19", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1351", "JournalTitle": "Applied Intelligence"}, {"Title": "Deep learning based detection and analysis of COVID-19 on chest X-ray images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>, <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1690", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 96383418, "Title": "Faculty Members’ Perspectives on the Role of Palestinian Universities in Environmental Awareness and Culture", "Abstract": "The current study aimed to investigate the role of Palestinian universities in environmental awareness and culture from the perspective of faculty members. Depending on the quantitative approach, the researchers used in this study a questionnaire consisting of (43) items previously validated and reliable, divided into three areas: the administrative system (15) items, the educational system, and general culture (21) items, and the school curriculum (7) items. The study population consisted of all university faculty members, the number was (1418) faculty members from An-Najah University, Polytechnic University, and Al Istiqlal University. (180) faculty members were selected using a simple random method during the second semester of the academic year 2021/2020. Study data were analyzed using SPSS. Findings of the study revealed that the role of Palestinian universities in promoting environmental culture among faculty members was high, with a mean of 3.55. The arithmetic means for the educational system domain were (3.77), the curriculum domain (3.62), and the administrative system domain was (3.27). The results also displayed that the perspectives of the faculty members varies according to the university variable (in favor of An-Najah University) and the college variable (in favor of the scientific faculties), and the educational degree variable (in favor of a master’s degree), and the experience years variable (in favor of 10 years or more). The study suggested that Palestinian universities need to take a greater role in environmental awareness and culture, and similar studies must be conducted. Keywords:. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "Environmental awareness; faculty members; higher education; Palestinian universities", "DOI": "10.18576/isl/120120", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383419, "Title": "Stumbling Blocks of Online Learning During COVID 19 Pandemic – Perspectives of Students of Selected Universities in London", "Abstract": "COVID 19 Pandemic has led to mayhem across the Planet. Educational institutions are the worst affected arena. There is a paradigm shift from conventional classroom teaching to online methods. But it has its own obstructions. Thus, this research is undertaken to study the impediments of online learning faced by the students of selected universities of London. The questionnaire was administered among 200 students out of which 196 responded. The results of the Study reveal that the major obstructions which hindered online learning were lack of computer skills, internet connectivity issues, difficulty in operating the software, absence of social bonding between teachers and students, difficulty in recording lectures, difficulty in grasping practical courses such as mathematics, finance, accounting, engineering etc. To cope up with the Stumbling Blocks, the Study advocates some of the most innovative and creative ways such as application of Bloom’s Digital Taxonomy, VARK Model, 5/5/5 rule etc. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "5/5/5 rule; Bloom’s Digital Taxonomy; COVID 19 Pandemic; Online Learning; Stumbling Blocks; VARK Model", "DOI": "10.18576/isl/120132", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "A Cyclical Approach to Synthetic and Natural Speech Mismatch Refinement of Neural Post-filter for Low-cost Text-to-speech System", "Abstract": "Neural-based text-to-speech (TTS) systems achieve very high-fidelity speech generation because of the rapid neural network developments. However, the huge labeled corpus and high computation cost requirements limit the possibility of developing a high-fidelity TTS system by small companies or individuals. On the other hand, a neural vocoder, which has been widely adopted for the speech generation in neural-based TTS systems, can be trained with a relatively small unlabeled corpus. Therefore, in this paper, we explore a general framework to develop a neural post-filter (NPF) for low-cost TTS systems using neural vocoders. A cyclical approach is proposed to tackle the acoustic and temporal mismatches (AM and TM) of developing an NPF. Both objective and subjective evaluations have been conducted to demonstrate the AM and TM problems and the effectiveness of the proposed framework. © 2022 Y.-<PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>.", "Keywords": "acoustic mismatch; cyclical voice conversion; Neural post-filter for text-to-speech; neural vocoder; temporal mismatch", "DOI": "10.1561/116.00000020", "PubYear": 2022, "Volume": "11", "Issue": "1", "JournalId": 10204, "JournalTitle": "APSIPA Transactions on Signal and Information Processing", "ISSN": "", "EISSN": "2048-7703", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Information Technology Center, Nagoya University, Japan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Information Technology Center, Nagoya University, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "AI, Inc., Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AI, Inc., Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "AI, Inc., Japan"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology Center, Nagoya University, Japan"}], "References": []}, {"ArticleId": 96383477, "Title": "An Enhanced Population Selection Algorithm for Timetabling System", "Abstract": "The timetable preparation for systems that are based on credit hours is a challenge for heads of departments. Departments have to prepare their timetables, where the problem is increased when a department serves other departments with some courses. Also, university’s management cannot assure that the offered courses are more than the needs or not. Many algorithms have been tested without an optimal solution. A new proposed algorithm which is called the Enhanced Population Selection (EPS) Algorithm has been implemented and tested with a suitable number of students, courses, lecturers, and venues that is based on the harmony search algorithm and genetic algorithm. The new proposed EPS algorithm has scheduled the timetables for two semesters with academic advisors satisfaction without conflicts. Furthermore, all specified constraints are tested and satisfied. © 2023 NSP.", "Keywords": "and genetic algorithm; Bees algorithm; harmony search algorithm; Timetabling", "DOI": "10.18576/isl/120127", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383478, "Title": "Selected Samples of Avant-garde Designs to Redefying the Concepts of (Fashion) and (Figure)", "Abstract": "In every era, and in every nation throughout history, there have been varying criteria for the ideal body dimensions and ideal external appearance, this diversity of beauty criteria greatly affected the dimensions and shape of the figure, especially for the female, forced the appearance to amplification and minimization or reduction and lifting, for decades this last diversity was keen to keep every area of the body as its connotation, this is chest, this is waist, etc.. (Victorian fashion) in the nineteenth century indicates this system, also the (Edwardian fashion) in the start of the twentieth century, where the tight waist, high chest, and low full hips represent the iconic beauty. Fashion of the twentieth century continued to emphasize the aesthetics of the human figure with an increase to bare parts and a tendency to aesthetics of thinness. With the emergence of the avant-garde fashion designers at the end of this century and in the beginning of the twenty-first century, the dimension and shape of the human figure strongly had been manipulated, unprecedented formations appeared on the scene, it can be seen clearly through the external lines and silhouettes. Avant-garde put the entire fashion system in inquiry, revealing that the ideal philosophy of beauty is far from the prevailing reality, some of avant-garde merged the body with architectural structures, some criticized the concept of fashion in sarcastic formulations, others took fashion as an information medium to support humanitarian causes. The current study aims to identify the visions of fashion avant-garde toward the relationship between fashion and human figure, how did they consider the figure through fashion? and how did some of avant-garde redefined the concepts of fashion itself, which affected the shape of the figure? Also driven by the researcher observation of fashion students' weakness understanding for many contemporary fashion projects, the need for the current study comes with analysis of what is behind the designs, expanding the perceptions of fashion scholars, keeping pace with global trends in thought, design, and production which corresponds to Arabic culture. The study follows the descriptive analytical method. The results were represented in extracting eleven criteria that can be as a measure instrument of redefining (fashion) and (figure) in avant-garde designs. © 2023 NSP Natural Sciences Publishing Cor.", "Keywords": "(Fashion) redefinition; (Figure) redefinition; Avant-garde", "DOI": "10.18576/isl/120138", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383490, "Title": "Peer Social Acceptance of Students with Special Needs", "Abstract": "The purpose of this research is to investigate how kids with ASD are accepted socially by their peers. Peer social acceptability may be seen in the manner in which autistic kids are treated by their peers as well as in how they show and demonstrate their desire to participate in various activities. In addition, the many kinds of connections that may be seen between ordinary students and pupils who have ASD. This research is a qualitative study that focuses on description. The participants in this research were classmates and their respective professors. Interviews and observations were the methods of data collection that were employed for this investigation. The method of data analysis that was employed was called descriptive analysis, and it included reducing the amount of data, presenting the data, and deriving conclusions from the data. Verifying the accuracy of the author's claims by using methods such as triangulation, extended observations, and consultation with others. Students who have ASD spectrum disorder may participate in social activities with their peers. The classroom instructor's support and understanding helps ordinary students better appreciate the condition of students with ASD. The teacher also understands children with ASD when they have tantrums and may be an aid when students with ASD are having problems. Social interactions and group relations are the types of relationships that may develop between children with ASD and their typical classmates. Regular students are able to create group interactions with students who have ASD with the assistance of the class teacher's promotion of the formation of study groups. © 2023 NSP.", "Keywords": "social acceptance of peers; Special Needs; Students", "DOI": "10.18576/isl/120128", "PubYear": 2023, "Volume": "12", "Issue": "1", "JournalId": 25728, "JournalTitle": "Information Sciences Letters", "ISSN": "2090-9551", "EISSN": "2090-956X", "Authors": [], "References": []}, {"ArticleId": 96383579, "Title": "A survey on deep learning techniques in real-time applications", "Abstract": "<p xml:lang=\"en\">In recent years, machine learning and Deep Learning have increased and gathered epic success in traditional application domains and new areas of Artificial Intelligence. The performance using Deep Learning has dominated experimental results compared to conventional machine learning algorithms. This paper presents an overview of the progress that has occurred in Deep Learning (DL) concerning some application domains like Autonomous Driving, Healthcare, Voice Recognition, Image Recognition, Advertising, Predicting Natural Calamities, National Stock Exchange and many more. Additionally, deeper insights into several Deep Learning techniques, their working principles, and experimental results are scrutinized. The survey covers Convolutional Neural Network (CNN), Recurrent Neural Network (RNN), including Long Short-Term Memory (LSTM) and Gated Recurrent Units (GRU), Auto-Encoder (AE), Deep Belief Network (DBN), Generative Adversarial Network (GAN), and Deep Reinforcement Learning (DRL).</p>", "Keywords": "", "DOI": "10.26634/jpr.9.1.18858", "PubYear": 2022, "Volume": "9", "Issue": "1", "JournalId": 46195, "JournalTitle": "i-manager’s Journal on Pattern Recognition", "ISSN": "2349-7912", "EISSN": "2350-112X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96383645, "Title": "Bridging the gap between the semantic web and big data: answering SPARQL queries over NoSQL databases", "Abstract": "<p>Nowadays, the database field has gotten much more diverse, and as a result, a variety of non-relational (NoSQL) databases have been created, including JSON-document databases and key-value stores, as well as extensible markup language (XML) and graph databases. Due to the emergence of a new generation of data services, some of the problems associated with big data have been resolved. In addition, in the haste to address the challenges of big data, NoSQL abandoned several core databases features that make them extremely efficient and functional, for instance the global view, which enables users to access data regardless of how it is logically structured or physically stored in its sources. In this article, we propose a method that allows us to query non-relational databases based on the ontology-based access data (OBDA) framework by delegating SPARQL protocol and resource description framework (RDF) query language (SPARQL) queries from ontology to the NoSQL database. We applied the method on a popular database called Couchbase and we discussed the result obtained.</p>", "Keywords": "Big data;NoSQL;Ontology;Semantic web;SPARQL", "DOI": "10.11591/ijece.v12i6.pp6829-6835", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 29337, "JournalTitle": "International Journal of Electrical and Computer Engineering (IJECE)", "ISSN": "2088-8708", "EISSN": "2088-8708", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON>"}], "References": []}, {"ArticleId": 96383691, "Title": "Formal Modeling of Self-Adaptive Resource Scheduling in Cloud", "Abstract": "A self-adaptive resource provisioning on demand is a critical factor in cloud computing. The selection of accurate amount of resources at run time is not easy due to dynamic nature of requests. Therefore, a self-adaptive strategy of resources is required to deal with dynamic nature of requests based on run time change in workload. In this paper we proposed a Cloud-based Adaptive Resource Scheduling Strategy (CARSS) Framework that formally addresses these issues and is more expressive than traditional approaches. The decision making in CARSS is based on more than one factors. The MAPE-K based framework determines the state of the resources based on their current utilization. Timed-Arc Petri Net (TAPN) is used to model system formally and behaviour is expressed in TCTL, while TAPAAL model checker verifies the underline properties of the system. © 2023 Tech Science Press. All rights reserved.", "Keywords": "cloud computing; Formal modeling; multi-agent; self-adaptive", "DOI": "10.32604/cmc.2023.032691", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Government College University, Lahore, 54000, Pakistan"}], "References": [{"Title": "An Intelligent Machine Learning and Self Adaptive Resource Allocation Framework for Cloud Computing Environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; M<PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "18", "Page": "165501", "JournalTitle": "EAI Endorsed Transactions on Cloud Systems"}, {"Title": "Self - Adaptive Load Balancing Using Live Migration of Virtual Machines in Cloud Environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "2", "Page": "735", "JournalTitle": "Webology"}, {"Title": "Intelligent Cloud Based Load Balancing System Empowered with Fuzzy Logic", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "1", "Page": "519", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Optimal Resource Allocation in Fog Computing for Healthcare Applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "71", "Issue": "3", "Page": "6147", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383692, "Title": "Employment Quality Evaluation Model Based on Hybrid Intelligent Algorithm", "Abstract": "In order to solve the defect of large error in current employment quality evaluation, an employment quality evaluation model based on grey correlation degree method and fuzzy C-means (FCM) is proposed. Firstly, it analyzes the related research work of employment quality evaluation, establishes the employment quality evaluation index system, collects the index data, and normalizes the index data; Then, the weight value of employment quality evaluation index is determined by Grey relational analysis method, and some unimportant indexes are removed; Finally, the employment quality evaluation model is established by using fuzzy cluster analysis algorithm, and compared with other employment quality evaluation models. The test results show that the employment quality evaluation accuracy of the design model exceeds 93%, the employment quality evaluation error can meet the requirements of practical application, and the employment quality evaluation effect is much better than the comparison model. The comparison test verifies the superiority of the model. © 2023 Tech Science Press. All rights reserved.", "Keywords": "comparative test; Employment quality; evaluation model; fuzzy c-means clustering algorithm; grey correlation analysis method; index system", "DOI": "10.32604/cmc.2023.028756", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Henan Mechanical Electrical Vocational College, Xinzheng,Hennan451191, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Henan Mechanical Electrical Vocational College, Xinzheng,Hennan451191, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Florence, Firenze, 50041, Italy"}], "References": [{"Title": "Multiple-attribute decision making problems based on SVTNH methods", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "9", "Page": "3717", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Multi objective taguchi–grey relational analysis and krill herd algorithm approaches to investigate the parametric optimization in abrasive water jet drilling of stainless steel", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "107075", "JournalTitle": "Applied Soft Computing"}, {"Title": "New Fuzzy Fractional Epidemic Model Involving Death Population", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "3", "Page": "331", "JournalTitle": "Computer Systems Science and Engineering"}, {"Title": "Grey Wolf Optimization Based Tuning of Terminal Sliding Mode Controllers for a Quadrotor", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Soufiene Bouall鑗ue", "PubYear": 2021, "Volume": "68", "Issue": "2", "Page": "2265", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Grey Wolf Optimizer-Based Fractional MPPT for Thermoelectric Generator", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "729", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Application of Grey Model and Neural Network in Financial Revenue Forecast", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "3", "Page": "4043", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Employing a Fuzzy Approach for Monitoring Fish Pond Culture Environment", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "987", "JournalTitle": "Intelligent Automation & Soft Computing"}, {"Title": "Multi-Objective Grey Wolf Optimization Algorithm for Solving Real-World BLDC Motor Design Problem", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "70", "Issue": "2", "Page": "2435", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383694, "Title": "Profiling Astronomical Objects Using Unsupervised Learning Approach", "Abstract": "Attempts to determine characters of astronomical objects have been one of major and vibrant activities in both astronomy and data science fields. Instead of a manual inspection, various automated systems are invented to satisfy the need, including the classification of light curve profiles. A specific Kaggle competition, namely Photometric LSST Astronomical Time-Series Classification Challenge (PLAsTiCC), is launched to gather new ideas of tackling the abovementioned task using the data set collected from the Large Synoptic Survey Telescope (LSST) project. Almost all proposed methods fall into the supervised family with a common aim to categorize each object into one of pre-defined types. As this challenge focuses on developing a predictive model that is robust to classifying unseen data, those previous attempts similarly encounter the lack of discriminate features, since distribution of training and actual test datasets are largely different. As a result, well-known classification algorithms prove to be sub-optimal, while more complicated feature extraction techniques may help to slightly boost the predictive performance. Given such a burden, this research is set to explore an unsupervised alternative to the difficult quest, where common classifiers fail to reach the 50% accuracy mark. A clustering technique is exploited to transform the space of training data, from which a more accurate classifier can be built. In addition to a single clustering framework that provides a comparable accuracy to the front runners of supervised learning, a multiple-clustering alternative is also introduced with improved performance. In fact, it is able to yield a higher accuracy rate of 58.32% from 51.36% that is obtained using a simple clustering. For this difficult problem, it is rather good considering for those achieved by well-known models like support vector machine (SVM) with 51.80% and <PERSON><PERSON><PERSON> (NB) with only 2.92%. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Astronomy; classification; data clustering; light curve data; sky survey", "DOI": "10.32604/cmc.2023.026739", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Theerapat <PERSON>", "Affiliation": "Center of Excellence in AI and Emerging Technologies, School of Information Technology, Mae Fah Luang University, Chiang Rai, 57100, Thailand"}, {"AuthorId": 2, "Name": "Tossapon <PERSON>", "Affiliation": "Center of Excellence in AI and Emerging Technologies, School of Information Technology, Mae Fah Luang University, Chiang Rai, 57100, Thailand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>-On", "Affiliation": "Department of Computer Science, Aberystwyth University, Ceredigion, Aberystwyth, United Kingdom"}], "References": [{"Title": "Improving consensus clustering with noise-induced ensemble generation", "Authors": "Patcharaporn Panwong; Tossapon <PERSON>n; Natthakan <PERSON>-On", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113138", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Distributed Privacy Preservation Approach for Big Data in Public Health Emergencies Using Smart Contract and SGX", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "65", "Issue": "1", "Page": "723", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Using Semantic Web Technologies to Improve the Extract Transform Load Model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "2", "Page": "2711", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "An Optimal Big Data Analytics with Concept Drift Detection on High-Dimensional Streaming Data", "Authors": "<PERSON><PERSON> <PERSON>; <PERSON><PERSON>; <PERSON>al <PERSON>", "PubYear": 2021, "Volume": "68", "Issue": "3", "Page": "2843", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 96383701, "Title": "Big Data Analytics Using Graph Signal Processing", "Abstract": "The networks are fundamental to our modern world and they appear throughout science and society. Access to a massive amount of data presents a unique opportunity to the researcher’s community. As networks grow in size the complexity increases and our ability to analyze them using the current state of the art is at severe risk of failing to keep pace. Therefore, this paper initiates a discussion on graph signal processing for large-scale data analysis. We first provide a comprehensive overview of core ideas in Graph signal processing (GSP) and their connection to conventional digital signal processing (DSP). We then summarize recent developments in developing basic GSP tools, including methods for graph filtering or graph learning, graph signal, graph Fourier transform (GFT), spectrum, graph frequency, etc. Graph filtering is a basic task that allows for isolating the contribution of individual frequencies and therefore enables the removal of noise. We then consider a graph filter as a model that helps to extend the application of GSP methods to large datasets. To show the suitability and the effeteness, we first created a noisy graph signal and then applied it to the filter. After several rounds of simulation results. We see that the filtered signal appears to be smoother and is closer to the original noise-free distance-based signal. By using this example application, we thoroughly demonstrated that graph filtration is efficient for big data analytics. © 2023 Tech Science Press. All rights reserved.", "Keywords": "Big data; big data processing; data science; graph signal processing; social networks", "DOI": "10.32604/cmc.2023.030615", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, 38541, South Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 96383729, "Title": "Estimating Construction Material Indices with ARIMA and Optimized NARNETs", "Abstract": "Construction Industry operates relying on various key economic indicators. One of these indicators is material prices. On the other hand, cost is a key concern in all operations of the construction industry. In the uncertain conditions, reliable cost forecasts become an important source of information. Material cost is one of the key components of the overall cost of construction. In addition, cost overrun is a common problem in the construction industry, where nine out of ten construction projects face cost overrun. In order to carry out a successful cost management strategy and prevent cost overruns, it is very important to find reliable methods for the estimation of construction material prices. Material prices have a time dependent nature. In order to increase the foreseeability of the costs of construction materials, this study focuses on estimation of construction material indices through time series analysis. Two different types of analysis are implemented for estimation of the future values of construction material indices. The first method implemented was Autoregressive Integrated Moving Average (ARIMA), which is known to be successful in estimation of time series having a linear nature. The second method implemented was Non-Linear Autoregressive Neural Network (NARNET) which is known to be successful in modeling and estimating of series with non-linear components. The results have shown that depending on the nature of the series, both these methods can successfully and accurately estimate the future values of the indices. In addition, we found out that Optimal NARNET architectures which provide better accuracy in estimation of the series can be identified/discovered as result of grid search on NARNET hyperparameters. © 2023 Tech Science Press. All rights reserved.", "Keywords": "ARIMA; Construction material indices; NARNETs; non-linear autoregressive neural network", "DOI": "10.32604/cmc.2023.032502", "PubYear": 2023, "Volume": "74", "Issue": "1", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "Ümit Işıkdağ", "Affiliation": ""}, {"AuthorId": 2, "Name": "Aycan Hepsağ", "Affiliation": ""}, {"AuthorId": 3, "Name": "Süreyya İmre Bıyıklı", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computing HND Prog., Nisantasi University, Istanbul, Turkey"}, {"AuthorId": 5, "Name": "Gebrail Bekdaş", "Affiliation": "Department of Civil Engineering, Istanbul University-Cerrahpaşa, Istanbul, Turkey"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}]