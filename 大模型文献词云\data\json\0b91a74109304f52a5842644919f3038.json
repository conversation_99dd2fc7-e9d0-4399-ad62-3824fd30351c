[{"ArticleId": 109401792, "Title": "State-of-the-Art Review of Computational Static and Dynamic Behaviors of Small-Scaled Functionally Graded Multilayer Shallow Arch Structures from Design to Analysis", "Abstract": "<p>Functionally graded multilayer shallow arch structures have gained significant attention due to their unique mechanical properties and potential applications. This review aims to provide a comprehensive overview of the static and dynamic behaviors of small-scaled functionally graded multilayer shallow arch structures, covering design and analysis aspects. The review begins by introducing functionally graded materials (FGMs) and their advantages in tailoring material properties. It then focuses on fabrication procedures for these structures. The design considerations are explored in detail, discussing various approaches for material composition grading and layer configuration. The influence of key parameters on structural behavior is examined, including material properties, layer thickness, and arch geometry. Mechanical analysis techniques for studying the static and dynamic behaviors of these structures are discussed. The review highlights the potential applications of FGMs in small-scaled shallow arch structures, such as bridges, tunnels, spaceships, and medical implants. This information is valuable for researchers interested in real-life applications. The review concludes by summarizing key findings and suggesting future research directions. It emphasizes investigating additional factors like temperature effects, geometric nonlinearities, and environmental conditions. The integration of advanced computational techniques with experimental validation is recommended for more accurate predictions and reliable design guidelines. Challenges in designing cost-effective and practical structures for real-world applications are acknowledged. Future research may focus on addressing these challenges and developing new techniques for large-scale engineering projects. The review provides insights into the fundamental scientific considerations and limitations of these structures. Overall, this state-of-the-art review serves as a valuable resource for researchers, engineers, and practitioners interested in designing and analyzing small-scaled functionally graded multilayer shallow arch structures. It offers insights into their computational static and dynamic behaviors and their potential applications in diverse fields.</p>", "Keywords": "", "DOI": "10.1007/s11831-023-09983-0", "PubYear": 2024, "Volume": "31", "Issue": "1", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanotechnology and Multifunctional Structures Research Center (NMSRC), Eastern Mediterranean University, Famagusta, Turkey; Department of Mechanical Engineering, Eastern Mediterranean University, Famagusta, Turkey"}, {"AuthorId": 2, "Name": "Baba<PERSON>", "Affiliation": "Nanotechnology and Multifunctional Structures Research Center (NMSRC), Eastern Mediterranean University, Famagusta, Turkey; Department of Mechanical Engineering, Eastern Mediterranean University, Famagusta, Turkey; Department of Mechanical Engineering Science, University of Johannesburg, Gauteng, South Africa"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Science and Technology, The University of Georgia, Tbilisi, Georgia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Nanotechnology and Multifunctional Structures Research Center (NMSRC), Eastern Mediterranean University, Famagusta, Turkey; Department of Mechanical Engineering, Eastern Mediterranean University, Famagusta, Turkey"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Tribology, Department of Mechanical Engineering, Tsinghua University, Beijing, China"}], "References": [{"Title": "Finite element analysis of functionally graded hyperelastic beams under plane stress", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "4", "Page": "1265", "JournalTitle": "Engineering with Computers"}, {"Title": "Free vibration and buckling analyses of CNT reinforced laminated non-rectangular plates by discrete singular convolution method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "489", "JournalTitle": "Engineering with Computers"}, {"Title": "A third order shear deformable model and its applications for nonlinear dynamic response of graphene oxides reinforced curved beams resting on visco-elastic foundation and subjected to moving loads", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "2805", "JournalTitle": "Engineering with Computers"}, {"Title": "A novel size-dependent nonlocal strain gradient isogeometric model for functionally graded carbon nanotube-reinforced composite nanoplates", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S3", "Page": "2027", "JournalTitle": "Engineering with Computers"}, {"Title": "Quasi-3D large deflection nonlinear analysis of isogeometric FGM microplates with variable thickness via nonlocal stress–strain gradient elasticity", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "4", "Page": "3691", "JournalTitle": "Engineering with Computers"}, {"Title": "A temperature-insensitive FBG acceleration sensor with sinusoid-shaped curved beams", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "42", "Issue": "1", "Page": "115", "JournalTitle": "Sensor Review"}, {"Title": "Couple stress-based moving Kriging meshfree shell model for nonlinear free oscillations of random checkerboard reinforced microshells", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "2", "Page": "1519", "JournalTitle": "Engineering with Computers"}, {"Title": "Modified strain gradient-based nonlinear building sustainability of porous functionally graded composite microplates with and without cutouts using IGA", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "2147", "JournalTitle": "Engineering with Computers"}, {"Title": "On the Advances of Computational Nonclassical Continuum Theories of Elasticity for Bending Analyses of Small-Sized Plate-Based Structures: A Review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "30", "Issue": "5", "Page": "2959", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Magneto-thermoelastic interactions in an unbounded orthotropic viscoelastic solid under the Hall current effect by the fourth-order Moore<PERSON> equation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "141", "Issue": "", "Page": "102", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": *********, "Title": "Financing Decisions and the Role of CSR in Donation-Based Crowdfunding", "Abstract": "<p>Donation-based crowdfunding and corporate social responsibility (CSR) activities have potential symbiotic ramifications to raise funds, but campaigners are confronted with challenges and competition to accomplish their charitable target. For instance, CSR activities could warrant the possibility of using crowdfunding to raise money. On the other hand, a company's CSR objectives can be achieved by using crowdfunding to micro-fund various social initiatives. Current research investigates the relationship between fundraisers in donation-based crowdfunding activities, which become potential CSR activities. Exclusively, the study analyzes the correlation among the value raised at the end of fundraising activity, the amounts targeted by the fundraiser, and CSR-Type activities on the project's success in donation-based crowdfunding. Based on this, a research taxonomy has been established for a comparative analysis between Pakistan and Indonesia. Secondary data is collected from donation-based platforms and analyzed through Ordinary Least Square (OLS) regression and the models are validated using a robustness check. The outcomes show that a higher value raised (V) correlates more positively with project success in Pakistan (164) as compared with Indonesia (122). The Target fund (T) has a significant and negative association with the project's success in the Pakistani market, however, the significant and negative effect on the project’s success in the Indonesian market. Lastly, CSR-related activities such as education, environment, community, and health have a positive relationship with project success in Pakistan, except for the product which has a negative, however significant relationship. In contrast, for Indonesia, CSR-type activities such as education, environment, community, product, and health have a positive and significant relationship with the project's success. This study contributes to the donation-based crowdfunding literature to develop a vivid understanding of different CSR activities and their impact on the project's success. The current study is one of the first to examine the significance of CSR activities and will enrich the body of knowledge regarding crowdfunding in diverse economies.</p>", "Keywords": "Corporate social responsibility; Donation-based crowdfunding; Finance technology; Pakistan; Indonesia", "DOI": "10.1007/s12599-023-00827-6", "PubYear": 2024, "Volume": "66", "Issue": "1", "JournalId": 5738, "JournalTitle": "Business & Information Systems Engineering", "ISSN": "2363-7005", "EISSN": "1867-0202", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Management Sciences, COMSATS University Islamabad (CUI), Islamabad, Pakistan; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "NUST Business School (NBS), National University of Science and Technology, Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Management Sciences, University of Management Technology (UMT), Lahore, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Zhejiang University, Hangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HITEC University Taxila, Taxila, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "HITEC University Taxila, Taxila, Pakistan"}], "References": []}, {"ArticleId": *********, "Title": "A comprehensive survey on automatic speech recognition using neural networks", "Abstract": "<p>The continuous development in Automatic Speech Recognition has grown and demonstrated its enormous potential in Human Interaction Communication systems. It is quite a challenging task to achieve high accuracy due to several parameters such as different dialects, spontaneous speech, speaker’s enrolment, computation power, dataset, and noisy environment that decrease the performance of the speech recognition system. It has motivated various researchers to make innovative contributions to the development of a robust speech recognition system. The study presents a systematic analysis of current state-of-the-art research work done in this field during 2015-2021. The prime focus of the study is to highlight the neural network-based speech recognition techniques, datasets, toolkits, and evaluation metrics utilized in the past seven years. It also synthesizes the evidence from past studies to provide empirical solutions for accuracy improvement. This study highlights the current status of speech recognition systems using neural networks and provides a brief knowledge to the new researchers.</p>", "Keywords": "Speech recognition; Dataset; Tools; Neural network; Deep learning", "DOI": "10.1007/s11042-023-16438-y", "PubYear": 2024, "Volume": "83", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Punjabi University, Patiala, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Punjabi University, Patiala, India"}], "References": [{"Title": "A comparative analysis of pooling strategies for convolutional neural network based Hindi ASR", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "675", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A comparative analysis of pooling strategies for convolutional neural network based Hindi ASR", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "675", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A Survey of Deep Learning and Its Applications: A New Paradigm to Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "1071", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "A Survey of Deep Learning and Its Applications: A New Paradigm to Machine Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "4", "Page": "1071", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Adaptive windows multiple deep residual networks for speech recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112840", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sichuan dialect speech recognition with deep LSTM network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "2", "Page": "378", "JournalTitle": "Frontiers of Computer Science"}, {"Title": "Speech recognition in English cultural promotion via recurrent neural network", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "237", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "Speech recognition in English cultural promotion via recurrent neural network", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "237", "JournalTitle": "Personal and Ubiquitous Computing"}, {"Title": "English speech recognition based on deep learning with multiple features", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "3", "Page": "663", "JournalTitle": "Computing"}, {"Title": "English speech recognition based on deep learning with multiple features", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "3", "Page": "663", "JournalTitle": "Computing"}, {"Title": "ASRoIL: a comprehensive survey for automatic speech recognition of Indian languages", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "5", "Page": "3673", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "An emergent deep developmental model for auditory learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "4", "Page": "665", "JournalTitle": "Journal of Experimental & Theoretical Artificial Intelligence"}, {"Title": "Annealed gradient descent for deep learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "380", "Issue": "", "Page": "201", "JournalTitle": "Neurocomputing"}, {"Title": "A survey on automatic speech recognition systems for Portuguese language and its variations", "Authors": "T<PERSON>; M<PERSON>rjory <PERSON>u", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "101055", "JournalTitle": "Computer Speech & Language"}, {"Title": "Classification of aspirated and unaspirated sounds in speech using excitation and signal level information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "", "Page": "101057", "JournalTitle": "Computer Speech & Language"}, {"Title": "Confusion analysis in phoneme based speech recognition in Hindi", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "10", "Page": "4213", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Transfer learning from adult to children for speech recognition: Evaluation, analysis and recommendations", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "101077", "JournalTitle": "Computer Speech & Language"}, {"Title": "Optimisation of phonetic aware speech recognition through multi-objective evolutionary algorithms", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "113402", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A comparative case study of neural network training by using frame-level cost functions for automatic speech recognition purposes in Spanish", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19669", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A comparative case study of neural network training by using frame-level cost functions for automatic speech recognition purposes in Spanish", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "Page": "19669", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Keyword retrieving in continuous speech using connectionist temporal classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "A survey of the recent architectures of deep convolutional neural networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "8", "Page": "5455", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Hybrid-task learning for robust automatic speech recognition", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "64", "Issue": "", "Page": "101103", "JournalTitle": "Computer Speech & Language"}, {"Title": "RETRACTED ARTICLE: Preserving learnability and intelligibility at the point of care with assimilation of different speech recognition techniques", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "2", "Page": "265", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Automatic Speech Recognition System for Tonal Languages: State-of-the-Art Survey", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "3", "Page": "1039", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "Bengali Spoken Digit Classification: A Deep Learning Approach Using Convolutional Neural Network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "171", "Issue": "", "Page": "1381", "JournalTitle": "Procedia Computer Science"}, {"Title": "Text-independent speaker recognition using LSTM-RNN and speech enhancement", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "Page": "24013", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Geological big data acquisition based on speech recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "33-34", "Page": "24413", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A novel privacy-preserving speech recognition framework using bidirectional LSTM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "1", "Page": "1", "JournalTitle": "Journal of Cloud Computing: Advances, Systems and Applications"}, {"Title": "Deep learning application in smart cities: recent development, taxonomy, challenges and research prospects", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "7", "Page": "2973", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Survey on Deep Neural Networks in Speech and Vision Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "302", "JournalTitle": "Neurocomputing"}, {"Title": "AutoSSR: an efficient approach for automatic spontaneous speech recognition model for the Punjabi Language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "1617", "JournalTitle": "Soft Computing"}, {"Title": "AutoSSR: an efficient approach for automatic spontaneous speech recognition model for the Punjabi Language", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "2", "Page": "1617", "JournalTitle": "Soft Computing"}, {"Title": "Advancement from neural networks to deep learning in software effort estimation: Perspective of two decades", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "", "Page": "100288", "JournalTitle": "Computer Science Review"}, {"Title": "Performing predefined tasks using the human–robot interaction on speech recognition for an industrial robot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103903", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Performing predefined tasks using the human–robot interaction on speech recognition for an industrial robot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "95", "Issue": "", "Page": "103903", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Pattern recognition and features selection for speech emotion recognition model using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "799", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "A comprehensive and systematic look up into deep learning based object detection techniques: A review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "", "Page": "100301", "JournalTitle": "Computer Science Review"}, {"Title": "Understanding Lombard speech: a review of compensation techniques towards improving speech based recognition systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "4", "Page": "2495", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Understanding Lombard speech: a review of compensation techniques towards improving speech based recognition systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "54", "Issue": "4", "Page": "2495", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "Machine learning techniques for hate speech classification of twitter data: State-of-the-art, future challenges and research directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Friday <PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "", "Page": "100311", "JournalTitle": "Computer Science Review"}, {"Title": "Machine learning techniques for hate speech classification of twitter data: State-of-the-art, future challenges and research directions", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; Friday <PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "", "Page": "100311", "JournalTitle": "Computer Science Review"}, {"Title": "Persian speech recognition using deep learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "4", "Page": "893", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "FuzzyGCP: A deep learning architecture for automatic spoken language identification from speech signals", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114416", "JournalTitle": "Expert Systems with Applications"}, {"Title": "FuzzyGCP: A deep learning architecture for automatic spoken language identification from speech signals", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114416", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Mutual-learning sequence-level knowledge distillation for automatic speech recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "259", "JournalTitle": "Neurocomputing"}, {"Title": "Mutual-learning sequence-level knowledge distillation for automatic speech recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "428", "Issue": "", "Page": "259", "JournalTitle": "Neurocomputing"}, {"Title": "Deep learning approaches for speech emotion recognition: state of the art and research challenges", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "16", "Page": "23745", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Evaluation of the effectiveness and efficiency of state-of-the-art features and models for automatic speech recognition error detection", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Open-source toolkit for end-to-end Korean speech recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "100054", "JournalTitle": "Software Impacts"}, {"Title": "Open-source toolkit for end-to-end Korean speech recognition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "", "Page": "100054", "JournalTitle": "Software Impacts"}, {"Title": "Exploring end-to-end framework towards Khasi speech recognition system", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "2", "Page": "419", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "An acoustic model and linguistic analysis for Malayalam disyllabic words: a low resource language", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "2", "Page": "483", "JournalTitle": "International Journal of Speech Technology"}, {"Title": "Convolution neural network based automatic speech emotion recognition using Mel-frequency Cepstrum coefficients", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "10", "Page": "15563", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Enhancing accuracy of long contextual dependencies for Punjabi speech recognition system using deep LSTM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "24", "Issue": "2", "Page": "517", "JournalTitle": "International Journal of Speech Technology"}]}, {"ArticleId": 109402172, "Title": "OPTIMAL TRACKING CONTROL FOR ROBOT MANIPULATORS WITH ASYMMETRIC SATURATION TORQUES BASED ON REINF<PERSON><PERSON>MENT LEARNING", "Abstract": "<p>This paper introduces an optimal tracking controller for robot manipulators with asymmetrically saturated torques and partially - unknown dynamics based on a reinforcement learning method using a neural network. Firstly, the feedforward control inputs are designed based on the backstepping technique to convert the tracking control problem into the optimal tracking control problem. Secondly, a cost function of the system with asymmetrically saturated input is defined, and the constrained Hamilton-<PERSON> equation is built, which is solved by the online reinforcement learning algorithm using only a single neural network. Then, the asymmetric saturation optimal control rule is determined. Additionally, the concurrent learning technique is used to relax the demand for the persistence of excitation conditions. The built algorithm ensures that the closed-loop system is asymptotically stable, the approximation error is uniformly ultimately bounded (UUB), and the cost function converges to the near-optimal value. Finally, the effectiveness of the proposed algorithm is shown through comparative simulations.</p>", "Keywords": "Robot manipulators;reinforcement learning;optimal control;competitive learning;asymmetry saturation", "DOI": "10.15625/1813-9663/17641", "PubYear": 2023, "Volume": "39", "Issue": "1", "JournalId": 41141, "JournalTitle": "Journal of Computer Science and Cybernetics", "ISSN": "1813-9663", "EISSN": "1813-9663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Economics – Technology for Industry, 456 <PERSON>, <PERSON>, Ha <PERSON>, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ho Chi Minh City University of Technology--VNU--HCM, 268 Ly Thuong Kiet street, District 10, Ho Chi Minh City, Viet Nam"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Thai Nguyen University of Technology, 666, 3/2 street, Tich <PERSON>ong ward, Thai Nguyen, Viet Nam"}], "References": []}, {"ArticleId": 109402208, "Title": "A review of intelligent interactive learning methods", "Abstract": "<p>The development of intelligent interactive learning approaches has received a lot of attention in recent years due to the trend of incorporating intelligent algorithms. Intelligent interactive education tools have a significant positive impact on user engagement, motivation, and social outcomes. This study attempts to explore the existing models, methods and technologies of intelligent interactive learning including virtual and augmented reality. The method of the current study was a semi-systematic literature review. This study is expected to make a substantial contribution to research on the use of intelligent interactive learning applications in e-learning. It supports earlier research and suggests a wide range of pertinent subject areas that might be investigated to progress the field. These findings lead to recommendations for future research on intelligent interactive learning techniques in e-learning.</p>", "Keywords": "e-learning1; intelligent interactive system2; MOOC3; VR technology4; AR technology5; gamification6", "DOI": "10.3389/fcomp.2023.1141649", "PubYear": 2023, "Volume": "5", "Issue": "", "JournalId": 66297, "JournalTitle": "Frontiers in Computer Science", "ISSN": "", "EISSN": "2624-9898", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technologies, <PERSON><PERSON><PERSON><PERSON> Eurasian National University, Kazakhstan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technologies, <PERSON><PERSON><PERSON><PERSON> Eurasian National University, Kazakhstan; Department of Digital Development and Distance Learning, L.<PERSON><PERSON> Eurasian National University, Kazakhstan"}], "References": [{"Title": "A systematic literature review on Internet of things in education: Benefits and challenges", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "36", "Issue": "2", "Page": "115", "JournalTitle": "Journal of Computer Assisted Learning"}, {"Title": "Design review of MOOCs: application of e-learning design principles", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "3", "Page": "455", "JournalTitle": "Journal of Computing in Higher Education"}, {"Title": "Investigating educational affordances of virtual reality for simulation-based teaching training with graduate teaching assistants", "Authors": "Feng<PERSON> Ke; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "3", "Page": "607", "JournalTitle": "Journal of Computing in Higher Education"}, {"Title": "Educational scalability in MOOCs: Analysing instructional designs to find best practices", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "161", "Issue": "", "Page": "104054", "JournalTitle": "Computers & Education"}, {"Title": "Assessing program-level learning strategies in MOOCs", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "106674", "JournalTitle": "Computers in Human Behavior"}, {"Title": "RETRACTED: Three-dimensional simulation of swimming training based on Android mobile system and virtual reality technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103908", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Virtual reality sports auxiliary training system based on embedded system and computer technology", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "82", "Issue": "", "Page": "103944", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Intelligent query optimization and course recommendation during online lectures in E-learning system", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "11", "Page": "10375", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Personalized training model for organizing blended and lifelong distance learning courses and its effectiveness in Higher Education", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "3", "Page": "668", "JournalTitle": "Journal of Computing in Higher Education"}, {"Title": "Internet of Things for education: A smart and secure system for schools monitoring and alerting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "93", "Issue": "", "Page": "107275", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "CLSA: A novel deep learning model for MOOC dropout prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "94", "Issue": "", "Page": "107315", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Interactive visual computer vision analysis based on artificial intelligence technology in intelligent education", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "12", "Page": "9315", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Taking action to reduce dropout in MOOCs: Tested interventions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "179", "Issue": "", "Page": "104412", "JournalTitle": "Computers & Education"}, {"Title": "Intelligent Interactive English Teaching Discrete Data Modeling and Simulation", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "Real-time prediction of science student learning outcomes using machine learning classification of hemodynamics during virtual reality and online learning sessions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100078", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Smart MOOC integrated with intelligent tutoring: A system architecture and framework model proposal", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "3", "Issue": "", "Page": "100092", "JournalTitle": "Computers and Education: Artificial Intelligence"}, {"Title": "Intelligent Interactive English Teaching System for Engineering Education", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Advances in Multimedia"}, {"Title": "Effects of artificial Intelligence–Enabled personalized recommendations on learners’ learning engagement, motivation, and outcomes in a flipped classroom", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "194", "Issue": "", "Page": "104684", "JournalTitle": "Computers & Education"}, {"Title": "A Systematic literature review on implementation of virtual reality for learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "216", "Issue": "", "Page": "260", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 109402264, "Title": "Integrated Waveform Design Based on UAV MIMO Joint Radar Communication", "Abstract": "<p>The problem of orthogonal waveform construction in multiple input/multiple output (MIMO) radar communication integration greatly limits the realization of integration technology. In the unmanned aerial vehicle (UAV) MIMO antenna scenario, an orthogonal integrated waveform suitable for a MIMO antenna is designed using a sub−LFM−BPSK waveform combined with a chaotic spread spectrum code. After spread spectrum processing, each MIMO antenna transmits different communication data for orthogonal spread spectrum processing, which is suitable for the omnidirectional detection of MIMO application scenarios; moreover, the closed-form expressions of the integrated orthogonal waveform under certain constraints are derived. Finally, the simulation proves that the integrated orthogonal waveform set in the UAV MIMO scenario has excellent radar detection and communication capabilities.</p>", "Keywords": "", "DOI": "10.3390/info14080455", "PubYear": 2023, "Volume": "14", "Issue": "8", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China; Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, Beijing Jiaotong University, Beijing 100044, China"}], "References": []}, {"ArticleId": 109402274, "Title": "SAFE: EFFICIE<PERSON> DDOS ATTACK DEFENSE WITH ELASTIC TRAFFIC FLOW INSPECTION IN SDN-BASED DATA CENTERS", "Abstract": "<p>In this paper, we propose an efficient distributed denial-of-Service (DDoS) Attack deFEnse solution, namely SAFE, which utilizes an elastic traffic flow inspection mechanism, for Software-Defined Networking (SDN) based data centers. In particular, we first examine a leaf-spine SDN-based data center network, which is highly vulnerable to volumetric DDoS attacks. Next, we develop a rank-based anomaly detection algorithm to recognize anomalies in the amount of incoming traffic. Then, for the traffic flow inspection, we introduce a component called DFI (Deep Flow Inspection) running an Open vSwitch (OvS) that can be dynamically initiated (as a virtual machine) on-demand to collect traffic flow statistics. By utilizing deep reinforcement learning-based traffic monitoring from our previous study, the DFIs can be protected from the flow-table overflow problem while providing more detailed traffic flow information. Afterward, a machine learning-based attack detector analyzes the gathered flow rule statistics to identify the attack, and appropriate policies are implemented if an attack is recognized. The experiment results show that the SAFE can effectively defend against volumetric DDoS attacks while assuring a reliable Quality-of-Service level for benign traffic flows in SDN-based data center networks. Specifically, for TCP SYN and UDP floods, the SAFE attack detection performance is improved by approximately 40% and 30%, respectively, compared to the existing SATA solution. Furthermore, the attack mitigation performance, the ratio of dropped malicious packets obtained by the SAFE is superior by approximately 48% (for TCP SYN flood) and 52% (for UDP flood) to the SATA.</p>", "Keywords": "Traffic flow inspection;;Distributed denial-of-service attacks;;Software-defined networking;;Data centers.", "DOI": "10.15625/1813-9663/16629", "PubYear": 2023, "Volume": "39", "Issue": "1", "JournalId": 41141, "JournalTitle": "Journal of Computer Science and Cybernetics", "ISSN": "1813-9663", "EISSN": "1813-9663", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "FPT University, Ha Noi, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Vietnam - Korea University of Information and Communication Technology, The University of Danang"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Chair of Communication Networks, 09126 Chemnitz, Germany"}], "References": []}, {"ArticleId": 109402292, "Title": "Markov chain Monte Carlo approach to the analysis of response patterns in data collection process", "Abstract": "Survey research, such as telephone, mail, or online questionnaires, is one of the most widely used tools for collecting sample data. We are often interested in the total number of replies that would be received during a given time period. Many researchers have developed a wide variety of curve-fitting methods to predict the response rate of recipients over time. However, previous models are based on some assumptions that are hardly justified in practice. In this paper, a new response model is proposed that is based on meaningful parameters such as the ultimate response rate of questionnaire recipients, delay rate of respondents, and average delivery time of responses. To estimate those model parameters, we use the Markov chain Monte Carlo (MCMC) method, which is increasingly popular in the operational research community. With mail survey data in marketing research, we test our Bayesian response model and compare its performance with those of traditional curve-fitting models. ABSTRACT IN FRENCH La recherche par sondage, comme le téléphone, le courrier ou les questionnaires en ligne, est l‘un des outils les plus largement utilisés pour collecter les données. On s‘intéresse souvent au total nombre de réponses reçues au cours d‘une période. De nombreux chercheurs ont développé une grande variété de méthodes d‘ajustement de courbe pour dévoiler le taux de réponse des bénéficiaires. Cependant, les modèles précédents sont souvent basés sur des hypothèses qui ne sont absolument pas justifiées en pratique. Dans cet article, un nouveau modèle de réponse est proposé, basé sur trois paramètres de modèle significatifs tels que l’ultime taux de réponse des destinataires du questionnaire, le taux de retard des répondants et le moyen délai de livraison des réponses. Pour estimer ces paramètres de modèle, on va utiliser la méthode de la Markov Chain Monte Carlo (MCMC), qui devient de plus en plus populaire dans la communauté de la recherche opérationnelle. Dans la recherche du marketing, en utilise les données d‘enquête par courrier pour tester notre modèle de réponse bayésien, puis on compare ses performances exceptionnelles à celles des modèles traditionnels d‘ajustement de courbe.", "Keywords": "Markov chain Monte Carlo simulation ; marketing analytics ; probabilistic model ; survey research design ; data collection ; Bayesian estimation", "DOI": "10.1080/03155986.2023.2245304", "PubYear": 2023, "Volume": "61", "Issue": "4", "JournalId": 12238, "JournalTitle": "INFOR: Information Systems and Operational Research", "ISSN": "0315-5986", "EISSN": "1916-0615", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> College of Business, Louisiana State University, Baton Rouge, Louisiana, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> College of Business, Louisiana State University, Baton Rouge, Louisiana, USA"}], "References": []}, {"ArticleId": 109402359, "Title": "EVOLUTIONARY ALG<PERSON>ITHM FOR TASK OFFLOADING IN VEHICULAR FOG COMPUTING", "Abstract": "<p>Internet of Things technology was introduced to allow many physical devices to connect over the Internet. The data and tasks generated by these devices put pressure on the traditional cloud due to high resource and latency demand. Vehicular Fog Computing (VFC) is a concept that utilizes the computational resources integrated into the vehicles to support the processing of end-user-generated tasks. This research first proposes a bag of tasks offloading framework that allows vehicles to handle multiple tasks and any given time step. We then implement an evolution-based algorithm called Time-Cost-aware Task-Node Mapping (TCaTNM) to optimize completion time and operating costs simultaneously. The proposed algorithm is evaluated on datasets of different tasks and computing node sizes. The results show that our scheduling algorithm can save more than $60\\%$ of monetary cost than the Particle Swarm Optimization (PSO) algorithm with competitive computation time. Further evaluations also show that our algorithm has a much faster learning rate and can scale its performance as the number of tasks and computing nodes increases.</p>", "Keywords": "Evolutionary algorithm;Task offloading;Vehicular fog computing.", "DOI": "10.15625/1813-9663/38/3/17012", "PubYear": 2023, "Volume": "38", "Issue": "4", "JournalId": 41141, "JournalTitle": "Journal of Computer Science and Cybernetics", "ISSN": "1813-9663", "EISSN": "1813-9663", "Authors": [{"AuthorId": 1, "Name": "Do Bao Son", "Affiliation": "University of Transport Technology, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON>u Tri An", "Affiliation": "School of Information Communication and Technology, Hanoi University of Science and Technology, Vietnam"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Technology Sydney, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Communication and Technology, Hanoi University of Science and Technology, Vietnam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Communication and Technology, Hanoi University of Science and Technology, Vietnam"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Communication and Technology, Hanoi University of Science and Technology, Vietnam"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Communication and Technology, Hanoi University of Science and Technology, Vietnam"}, {"AuthorId": 8, "Name": "Huynh Thi Thanh Binh", "Affiliation": "School of Information Communication and Technology, Hanoi University of Science and Technology, Vietnam"}], "References": []}, {"ArticleId": *********, "Title": "Synthesis of Modular Multipliers", "Abstract": "", "Keywords": "", "DOI": "10.17587/prin.14.377-387", "PubYear": 2023, "Volume": "14", "Issue": "8", "JournalId": 24080, "JournalTitle": "PROGRAMMNAYA INGENERIA", "ISSN": "2220-3397", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "P. N. Bibilo", "Affiliation": "The United Institute of Informatics Problems of the National Academy of Sciences of Belarus, Minsk, 220012, Belarus"}], "References": []}, {"ArticleId": 109402420, "Title": "Improving Sequence-to-sequence Tibetan Speech Synthesis with Prosodic Information", "Abstract": "<p>There are about 6,000 languages worldwide, most of which are low-resource languages. Although the current speech synthesis (or text-to-speech, TTS) for major languages (e.g., Mandarin, English, French) has achieved good results, the voice quality of TTS for low-resource languages (e.g., Tibetan) still needs to be further improved. Because prosody plays a significant role in natural speech, the article proposes two sequence-to-sequence (seq2seq) Tibetan TTS models with prosodic information fusion to improve the voice quality of synthesized Tibetan speech. We first constructed a large-scale Tibetan corpus for seq2seq TTS. Then we designed a prosody generator to extract prosodic information from the Tibetan sentences. Finally, we trained two seq2seq Tibetan TTS models by fusing prosodic information, including feature-level and model-level prosodic information fusion. The experimental results showed that the proposed two seq2seq Tibetan TTS models, which fuse prosodic information, could effectively improve the voice quality of synthesized speech. Furthermore, the model-level prosodic information fusion only needs 60% ~ 70% of the training data to synthesize a voice similar to the baseline seq2seq Tibetan TTS. Therefore, the proposed prosodic information fusion methods can improve the voice quality of synthesized speech for low-resource languages.</p>", "Keywords": "", "DOI": "10.1145/3616012", "PubYear": 2023, "Volume": "22", "Issue": "9", "JournalId": 12315, "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing", "ISSN": "2375-4699", "EISSN": "2375-4702", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Physics and Electronic Engineering, Northwest Normal University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Educational Technology, Northwest Normal University, China"}], "References": []}, {"ArticleId": 109402461, "Title": "High‐performance MEMS shutter display with metal‐oxide thin‐film transistors and optimized MEMS element", "Abstract": "Active matrix prestressed microelectromechanical shutter displays enable outstanding optical properties as well as robust operating performance. The microelectromechanical systems (MEMS) shutter elements have been optimized for higher light outcoupling efficiency with lower operation voltage and higher pixel density. The MEMS elements have been co‐fabricated with self‐aligned metal‐oxide thin‐film transistors (TFTs). Several optimizations were required to integrate MEMS process without hampering the performance of both elements. The optimized display process requires only seven photolithographic masks with ensuring proper compatibility between MEMS shutter and metal‐oxide TFT process.", "Keywords": "MEMS shutter;metal-oxide TFT;transmissive shutter display", "DOI": "10.1002/jsid.1252", "PubYear": 2023, "Volume": "31", "Issue": "9", "JournalId": 7210, "JournalTitle": "Journal of the Society for Information Display", "ISSN": "1071-0922", "EISSN": "1938-3657", "Authors": [{"AuthorId": 1, "Name": "Sheikh <PERSON>", "Affiliation": "Institute for Large Area Microelectronics and Research Centre SCoPE University of Stuttgart  Stuttgart Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute for Large Area Microelectronics and Research Centre SCoPE University of Stuttgart  Stuttgart Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Large Area Microelectronics and Research Centre SCoPE University of Stuttgart  Stuttgart Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute for Large Area Microelectronics and Research Centre SCoPE University of Stuttgart  Stuttgart Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute for Large Area Microelectronics and Research Centre SCoPE University of Stuttgart  Stuttgart Germany"}], "References": []}, {"ArticleId": 109402473, "Title": "Election of Cooperative Chairman Using the Moora Method (Multi Objective Optimization On The Basis Of Ratio Analysis)", "Abstract": "<p>The Padrepio Helvetia Medan Parish Mandiri Savings and Loans Association Cooperative is one of the cooperatives engaged in the savings and loan sector by prioritizing members' desire to save so that they can create joint capital for members to borrow with appropriate loan services for welfare purposes. The Annual Members' Meeting is an annual agenda as a media for management to be accountable for their performance for one year. The chairman of the cooperative is the highest structural position of the administrator who has duties and responsibilities both inside and outside the cooperative in controlling all cooperative activities. The process of electing the chairman of the Padre Pio Parish Savings and Loans Association cooperative is still being carried out only by pointing to and mentioning someone's name from among the names of the management and then conveying it to members to approve. The determination of the chairman which is carried out using the arbitrary model is not in accordance with the terms and criteria of an optimal cooperative chairman because the elected chairman is not necessarily the most competent of the candidates and often happens just because of friendship so the results are not objective. With the reasons described, the researcher applied the MOORA method. The application of the MOORA method to the election of the chairman of the Padre Pio Helvetia Medan Parish Independent Savings and Loans cooperative can help determine a more objective chairman based on terms and criteria by solving problems through complex mathematical calculations, determining the weight value for each attribute, then proceed with the ranking process which will select the alternative that has been given. In this study the criteria determined consisted of background and educational level, certificate ownership, age, and length of time being a member. In the objective approach, the weight value is calculated mathematically so that it ignores the subjectivity of the decision maker. With the application of the MOORA method, several alternatives that meet the requirements and criteria can be determined which is the best to serve as chairman. The best ranking results are those who are most worthy of being the head of a more objective cooperative.</p>", "Keywords": "", "DOI": "10.47709/cnahpc.v5i2.2660", "PubYear": 2023, "Volume": "5", "Issue": "2", "JournalId": 80232, "JournalTitle": "Journal Of Computer Networks, Architecture and High Performance Computing", "ISSN": "", "EISSN": "2655-9102", "Authors": [{"AuthorId": 1, "Name": "Megaria Purba", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109402523, "Title": "Benchmarking methodology for stateful NAT64 gateways", "Abstract": "The benchmarking of Network Address and Protocol Translation from IPv6 clients to IPv4 servers (stateful NAT64) gateways is challenging from a methodological point of view because the state of the art benchmarking standards have some requirements that are conflicting when applied to stateful NAT64 gateways. In this paper, several methodological gaps are pointed out and a benchmarking methodology is proposed, which is applicable for any stateful NATxy gateways, where x and y are in {4, 6}. It bridges all the gaps by reconciling the conflicting requirements and facilitating the execution of the industry standard benchmarking measurement procedures (throughput, latency, frame loss rate, packet delay variation) with stateful NATxy gateways. New performance metrics specific to stateful testing are also defined: maximum connection establishment rate, connection tear down rate, and connection tracking table capacity. The proposed methodology is suitable for examining the scalability of the stateful NATxy gateways, too. The methodology is validated by applying it to the benchmarking of three radically different stateful NAT64 implementations: Jool, tayga plus iptables, and OpenBSD Packet Filter (PF). The details of the measurements and their results are fully disclosed.", "Keywords": "Benchmarking ; Iptables ; Jool ; OpenBSD PF ; Scalability ; Stateful NAT64 ; Tayga", "DOI": "10.1016/j.comcom.2023.08.009", "PubYear": 2023, "Volume": "210", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Telecommunications, Széchenyi István University, 1 Egyetem tér, Győr, H-9026, Hungary;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "SoftBank Corporation, 1-7-1 Kaigan, Minato-ku, Tokyo 105-7529, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Internet Initiative Japan, <PERSON><PERSON><PERSON><PERSON>, 2-10-2 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tokyo 102-0071, Japan"}], "References": [{"Title": "Performance analysis of SIIT implementations: Testing and improving the methodology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "156", "Issue": "", "Page": "54", "JournalTitle": "Computer Communications"}, {"Title": "Design and implementation of a software tester for benchmarking stateful NATxy gateways: Theory and practice of extending siitperf for stateful tests", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "192", "Issue": "", "Page": "75", "JournalTitle": "Computer Communications"}, {"Title": "Benchmarking methodology for stateful NAT64 gateways", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "210", "Issue": "", "Page": "256", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 109402536, "Title": "Distribution transforms for guiding center orbit coordinates in axisymmetric tokamak equilibria", "Abstract": "Accurate distribution transforms between common 4D axisymmetric tokamak guiding center coordinates { energy , pitch , R , Z } , and orbit-space coordinates (made up of a particle&#x27;s kinetic energy E , maximum radius in the tokamak R m , pitch at the point of maximum radius p m , and time-normalised phase τ = t / t o r b i t ) are vital for the verification and implementation of orbit tomography. These transforms have proven difficult in the past due to discontinuities across topological boundaries in orbit-space. In this work we exhaustively investigate these transforms, comparing existing and novel transform methods in speed, accuracy and specific sources of error. A distribution transform that samples { energy , pitch , R , Z } -space along pre-computed orbit paths, and relies on Jacobians calculated with autodifferentiation, is benchmarked against a Monte Carlo sampling and binning algorithm. Our new transform demonstrates a hundredfold increase in speed, and better preserves natural discontinuities across the orbit trapped-passing boundary. A potentially damaging source of transform error caused by a Jacobian singularity that occurs for vanishingly small orbits is addressed, ensuring repeated transforms are well-behaved. The application of smoothing splines in { energy , pitch , R , Z } -space and orbit-space is also discussed. A Jacobian-based transform utilising thin-plate polyharmonic splines restricted to subdomains of similar orbit class is presented and benchmarked against its equivalent non-splined transform. This new smoothing transform correctly avoids interpolating across the trapped/passing boundary, doing so without the prohibitively slow computation and hyperparameter tuning required by previous orbit-space splines.", "Keywords": "Energetic particles ; Constant of motion coordinates ; Fast ion orbits ; Distribution transforms ; Orbit tomography", "DOI": "10.1016/j.cpc.2023.108893", "PubYear": 2023, "Volume": "292", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Australian National University, Canberra, ACT, Australia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Technical University of Denmark, Department of Physics, DK-2800 Kgs. Lyngby, Denmark"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Technical University of Denmark, Department of Physics, DK-2800 Kgs. Lyngby, Denmark"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "General Atomics, San Diego, CA, United States of America"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Australian National University, Canberra, ACT, Australia"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "The University of Western Australia, Perth, WA, Australia"}], "References": [{"Title": "Representation and modeling of charged particle distributions in tokamaks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "275", "Issue": "", "Page": "108305", "JournalTitle": "Computer Physics Communications"}]}, {"ArticleId": *********, "Title": "Effective and efficient community search with size constraint on bipartite graphs", "Abstract": "Community search on bipartite graphs has been extensively studied in suspicious-group detection and team formation. However, the existing studies focused on the cohesiveness of the community, but ignored the size constraint on bipartite graphs, potentially leading to large community sizes and high costs. In this study, a size-constrained ( α , β )–community (SCC) containing a query vertex on a bipartite graph was investigated, where the upper layer size of the community cannot exceed threshold s and the lower layer size cannot exceed threshold t . For supporting SCC search in different situations, two search methods—peeling and expansion—are proposed by peeling from the ( α , β )-core containing the query vertex and expanding from the query vertex respectively. An efficient lower bound based on degree gap is proposed by terminating unpromising search branches early to increase the efficiency of the community search. The experimental results indicated that the proposed methods can be used to find communities within the size thresholds, with the efficiency of the search increased based on the lower bound.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119511", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science & Engineering, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science & Engineering, Northeastern University, Shenyang 110167, China;Key Laboratory of Big Data Management and Analytics (Liaoning Province), Northeastern University, Shenyang, China;Corresponding author at: School of Computer Science & Engineering, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science & Engineering, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science & Engineering, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science & Engineering, Northeastern University, Shenyang 110167, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Medicine and Biological Information Engineering, Northeastern University, Shenyang 110167, China"}], "References": [{"Title": "A survey of community search over big graphs", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "1", "Page": "353", "JournalTitle": "The VLDB Journal"}, {"Title": "Efficient ($$\\alpha $$, $$\\beta $$)-core computation in bipartite graphs", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "5", "Page": "1075", "JournalTitle": "The VLDB Journal"}, {"Title": "Towards efficient solutions of bitruss decomposition for large-scale bipartite graphs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "31", "Issue": "2", "Page": "203", "JournalTitle": "The VLDB Journal"}, {"Title": "I/O efficient k-truss community search in massive graphs", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "5", "Page": "713", "JournalTitle": "The VLDB Journal"}]}, {"ArticleId": 109402592, "Title": "ARAI-MVSNet: A multi-view stereo depth estimation network with adaptive depth range and depth interval", "Abstract": "Multi-View Stereo (MVS) is a fundamental problem in geometric computer vision which aims to reconstruct a scene using multi-view images with known camera parameters. However, the mainstream approaches represent the scene with a fixed all-pixel depth range and equal depth interval partition, which will result in inadequate utilization of depth planes and imprecise depth estimation. In this paper, we present a novel multi-stage coarse-to-fine framework to achieve adaptive all-pixel depth range and depth interval. We predict a coarse depth map in the first stage, then an Adaptive Depth Range Prediction module is proposed in the second stage to zoom in the scene by leveraging the reference image and the obtained depth map in the first stage and predict a more accurate all-pixel depth range for the following stages. In the third and fourth stages, we propose an Adaptive Depth Interval Adjustment module to achieve adaptive variable interval partition for pixel-wise depth range. The depth interval distribution in this module is normalized by Z-score, which can allocate dense depth hypothesis planes around the potential ground truth depth value and vice versa to achieve more accurate depth estimation. Extensive experiments on four widely used benchmark datasets (DTU, TnT, BlendedMVS, ETH 3D) demonstrate that our model achieves state-of-the-art performance and yields competitive generalization ability. Particularly, our method achieves the highest Acc and Overall on the DTU dataset, while attaining the highest Recall and F 1 -score on the Tanks and Temples intermediate and advanced dataset. Moreover, our method also achieves the lowest e 1 and e 3 on the BlendedMVS dataset and the highest Acc and F 1 -score on the ETH 3D dataset, surpassing all listed methods. Project website: https://github.com/zs670980918/ARAI-MVSNet Introduction Multi-View Stereo (MVS) is a fundamental problem in computer vision that aims to reconstruct a 3D scene from a collection of multiple images captured from different viewpoints. It addresses the challenge of inferring the 3D geometry of a scene by leveraging the visual information contained in these images. The MVS problem has garnered significant attention due to its wide range of applications, including 3D reconstruction [1], robot navigation [2], [3] and so on. To tackle the MVS problem, various techniques have been developed over the years. Early traditional MVS methods [4] leveraging multi-view consistency measures have achieved considerable performance, but they still demonstrate limited representation capability in low-texture regions, reflections, etc [5]. Recent advancements in deep learning have revolutionized the field of MVS. MVSNet [5] that utilizes 2D CNN to extract features and uses 3D CNN to regularize the cost volume yields significant improvements on many MVS benchmarks. However, the pipeline still suffers from low efficiency and high memory consumption. Following works which aim to balance the effectiveness and the efficiency are mainly categorized as recurrent methods [6], [7] and multi-stage methods [8], [9]. The recurrent methods utilize the GRU or convolutional LSTM to regularize the cost volume sequentially. While the multi-stage methods propose a novel cascade formulation using multi-scale cost volumes. Following a similar cascade strategy, we have designed our framework to achieve an effective and efficient MVSNet. The basic idea of our framework is that the surfaces of the objects are usually heterogeneous and their sizes are diverse. However, the above methods all represent the object with a fixed all-pixel depth range and equal depth interval partition and will result in two problems. First, since the objects may have different sizes, the fixed all-pixel depth range may not be a suitable fit for the range of each object, as depicted in Fig. 1. As a result, if the fixed all-pixel depth range is larger than the actual depth range of the object, certain depth hypothesis planes may become redundant (the higher left part of Fig. 1), whereas objects that fall beyond the fixed depth range cannot be reconstructed and may lead to a subpar reconstruction quality. Second, since the object surface is usually heterogeneous, employing the equal depth interval partition strategy can result in disparities between predicted depth values and the actual ground truth (GT) depth values (the lower left part of Fig. 1). Insufficient depth hypothesis planes in close proximity to the GT depth value can hinder the ability to accurately estimate depth values, ultimately leading to the imprecise reconstruction of specific details and negatively impacting the overall quality of the reconstruction. To tackle the aforementioned problems, we propose a novel multi-stage coarse-to-fine framework named ARAI-MVSNet, which mainly consists of two novel modules. As depicted in Fig. 1, instead of using a common fixed all-pixel depth range, we propose an innovative Adaptive Depth Range Prediction (ADRP) module, which enables a more precise zoom-in of the scene. The ADRP module predicts a more accurate all-pixel depth range for each object from a large-scale depth range by leveraging the reference image and the depth map of the former stage. This mechanism effectively utilizes the depth hypothesis planes, leading to the attainment of superior reconstruction outcomes, as evidenced by its high quality. In line with previous studies such as UCS-Net [10] and CFNet [11], we establish the pixel-wise depth range from the all-pixel depth range. However, in contrast to the equal depth interval partition employed in UCS-Net and CFNet, we introduce an Adaptive Depth Interval Adjustment (ADIA) module to reallocate more depth hypothesis planes close to the potential GT depth value. This mechanism utilizes the Z-score formulation [12] to calculate the offset for each depth plane, taking advantage of the depth map from the previous stage to achieve adaptive depth interval partition for the pixel-wise depth range. Additionally, it is also non-parametric to achieve adaptive depth interval for pixel-wise depth range, thus can significantly improve the efficiency of our module with performance maintained. Furthermore, to extract robust image features, we also design an Atrous Spatial Pyramid Feature Extraction Network (ASPFNet), where the context-aware features are extracted and fused with larger receptive fields. We evaluate our method on four different competitive benchmarks to demonstrate the SOTA performance and generalization ability. The results demonstrate that our model achieves the state-of-the-art Acc score and Overall score compared to other pioneer works on DTU dataset, the highest Recall score and F 1 -score score on Tanks and Temples intermediate and advanced dataset respectively. Moreover, our method also achieve the lowest e 1 score and e 3 score on the BlendedMVS dataset and the highest Acc score and F 1 -score on the ETH 3D dataset, surpassing all listed methods. In summary, our main contributions are four folds: (1) We present a novel multi-stage coarse-to-fine framework to achieve more accurate depth estimation. (2) We propose an ADRP module to predict an adaptive all-pixel depth range for more reliable reconstruction. (3) We propose an ADIA module to achieve adaptive variable depth interval partition to estimate more accurate depth values. (4) We evaluate our method on four benchmarks and significantly advance the state-of-the-art methods in evaluation metrics. Section snippets Traditional MVS methods The core idea of MVS is to estimate the depth or disparity map for each pixel in the images, which represents the corresponding 3D point’s distance from the camera(s). This estimation is achieved by analyzing the spatial and photometric consistencies across the multiple views [13]. The key assumption is that the same 3D point in the scene should project to similar positions in different images, and its appearance should exhibit consistent color or intensity values. By exploiting these Problem formulation In the deep learning-based MVS task, the goal is to use an end-to-end trainable model to infer a depth map L from N − 1 adjacent views with their corresponding camera poses. Assume the reference image I 1 and source images I i i = 2 N , where the features F i i = 1 N are extracted from them. Then the differentiable homography warping based on the depth hypothesis planes D and the extracted F i i = 1 N is used to construct a cost volume V . After regularization process, the regularized cost volume V r e g regresses a Dataset We evaluate our method on four datasets widely used in MVS. These include: (1) DTU [23], captured in a laboratory setting, it consists of 124 scenes and 7 lighting conditions. (2) Tanks and Temples [24], captured from real outdoor sensors, with more complex and realistic scenes; (3) BlendedMVS [25], with over 17k indoor and outdoor images of 113 scenes split into training and testing sets; and (4) ETH 3D [26], with high-resolution calibrated images of scenes containing significant viewpoint Limitations and discussion Although our model achieves better or comparable performance than most of the state-of-the-art methods on the four benchmarks [23], [24], [25], [26], it has several limitations: (1) Most extensive multi-view stereo (MVS) datasets, such as DTU and TnT, rely on numerous overlapped views for high-quality point cloud reconstruction. However, the performance of our model may decrease with fewer views, leading to poorer reconstruction results. This problem can be alleviated by integrating a module Conclusion In this paper, we propose a novel multi-stage coarse-to-fine framework ARAI-MVSNet for high-quality reconstruction. Our ADRP module leverages the reference image and depth map of the previous stage to compute range adjustment parameters to achieve adaptively depth range adjustment, which can effectively zoom in an accurate all-pixel depth range of the scene. Further, the ADIA module can reallocate the pixel-wise depth interval distribution to obtain more accurate depth values by utilizing the Declaration of competing interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Song Zhang received the BEng degree with honors from Anhui University, China, in 2020. Currently, he is pursuing a Ph.D. degree at the University of the Chinese Academy of Sciences. His research interests focused on 3D point cloud reconstruction, Vision Transformer, and image processing. References (35) Song Z. et al. Nonstructured light-based sensing for 3D reconstruction Pattern Recognit. (2010) Fang B. et al. Visual SLAM for robot navigation in healthcare facility Pattern Recognit. (2021) Fan Y. et al. Blitz-SLAM: A semantic SLAM in dynamic environments Pattern Recognit. (2022) Galliani S. et al. Massively parallel multiview stereopsis by surface normal diffusion Yao Y. et al. Mvsnet: Depth inference for unstructured multi-view stereo Yao Y. et al. Recurrent MVSNet for high-resolution multi-view stereo depth inference Yan J. et al. Dense hybrid recurrent multi-view stereo net with dynamic consistency checking Gu X. et al. Cascade cost volume for high-resolution multi-view stereo and stereo matching Yang J. et al. Cost volume pyramid based depth inference for multi-view stereo Cheng S. et al. Deep stereo using adaptive thin volume representation with uncertainty awareness Shen Z. et al. Cfnet: Cascade and fused cost volume for robust stereo matching Altman E.I. ZETATM analysis A new model to identify bankruptcy risk of corporations J. Bank. Financ. (1977) Furukawa Y. et al. Multi-view stereo: A tutorial Found. Trends® Comput. Graph. Vis. (2015) Kutulakos K.N. et al. A theory of shape by space carving Int. J. Comput. Vis. (2000) Curless B. et al. A volumetric method for building complex models from range images Lhuillier M. et al. A quasi-dense approach to surface reconstruction from uncalibrated images IEEE TPAMI (2005) Campbell N.D. et al. Using multiple hypotheses to improve depth-maps for multi-view stereo View more references Cited by (0) Recommended articles (1) Research article HPM-TDP: An efficient hierarchical PatchMatch depth estimation approach using tree dynamic programming ISPRS Journal of Photogrammetry and Remote Sensing, Volume 155, 2019, pp. 37-57 Show abstract Accurate and efficient estimation of the dense depth information from a pair of stereo images is a key step for many applications such as digital surface model production, 3D reconstruction and visualization, autonomous driving, and robotic navigation. Although great progress has been achieved in stereo matching over the past decade, the matching difficulties in poor and repetitive texture regions remain an issue. Aiming at solving the shortcomings of the current methods, this paper proposes HPM-TDP, which is an efficient hierarchical PatchMatch depth estimation approach that integrates a coarse-to-fine image pyramid strategy with a continuous Markov random field (MRF)-based global energy optimization framework, and minimizes the energy function by combining a hierarchical PatchMatch (HPM) framework and local α -expansion based tree dynamic programming (TDP). Firstly, the coarse-to-fine image pyramid strategy is integrated with the PatchMatch filter algorithm to quickly generate the hierarchical disparity plane prior for initializing each pixel’s disparity plane of the energy function optimization. Secondly, a multi-resolution cost aggregation strategy is adopted to boost the robustness of the matching cost function in the poor and repetitive texture areas. Finally, the HPM framework and local α -expansion based TDP are adopted to solve the non-submodular energy optimization problem, resulting in a globally optimized disparity plane map. Three benchmark datasets—the Middlebury 3.0, KITTI 2015, and Vaihingen datasets—were used to test the performance of HPM-TDP. The comprehensive experimental results demonstrate that HPM-TDP obtains a good performance on all datasets in terms of the (“Out-Noc”, “Avg-Noc”, “Out-All”, “Avg-All”) of (15.45%, 4.16px, 24.26%, 12.14px) and (5.46%, 1.20px, 6.55%, 1.54px) for Middlebury 3.0 and KITTI 2015 training datasets, and the (“Out-All”, “Avg-All”) of (26.32%, 4.04px) for Vaihingen dataset, respectively. Song Zhang received the BEng degree with honors from Anhui University, China, in 2020. Currently, he is pursuing a Ph.D. degree at the University of the Chinese Academy of Sciences. His research interests focused on 3D point cloud reconstruction, Vision Transformer, and image processing. Wenjia Xu received the BEng degree with honors from the Beijing Institute of Technology in 2016, and the Ph.D. degree from the University of Chinese Academy of Sciences in 2022. She worked as a visiting Ph.D. student at the MaxPlanck Institute for Informatics in Germany between 2019–2020. Currently, she is a research associate professor at the Beijing University of Posts and Telecommunications. Her research interest includes learning with limited supervision for computer vision tasks and remote sensing images, and explainable machine learning. Zhiwei Wei received a B.S. degree and Ph.D. degrees in GIS from Wuhan University, China, in 2015 and 2020. He is currently an Assistant Professor at the Aerospace Information Research Institute, Chinese Academy of Sciences. His research interests include creating explanatory and exploratory animations for spatial data, automated understanding, and processing of spatial data via spatial knowledge graph, and 3D Reconstruction for Enhanced VR. Lili Zhang received a B.S. degree from Northwestern Polytechnical University, China, and a Ph.D. degree from the University of Chinese Academy of Sciences in 2022. She is currently an Associated Professor and a master advisor Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing, China. His research interests include semantic segmentation and image processing. Yang Wang received a Ph.D. degree in cartography from Pe-king University, China, in 2014. She is currently an Associated Professor and a master advisor Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing, China. Her research interests include the analysis and visualization of temporal and geographical information. Junyi Liu received a B.S. degree and Ph.D. degrees from the National University of Defense Technology, China, in 1995 and 2001. She is currently a Professor and a doctoral supervisor Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing, China. His research interests focused on network information systems, multi-source information fusion, and image information processing. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.109885", "PubYear": 2023, "Volume": "144", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Network Information System Technology(NIST), Institute of Electronics, Chinese Academy of Sciences, Beijing 100190, China;School of Electronic, Electrical and Communication Engineering, University of Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing 100876, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Network Information System Technology(NIST), Institute of Electronics, Chinese Academy of Sciences, Beijing 100190, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Network Information System Technology(NIST), Institute of Electronics, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Network Information System Technology(NIST), Institute of Electronics, Chinese Academy of Sciences, Beijing 100190, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace Information Research Institute, Chinese Academy of Sciences, Beijing 100190, China;Key Laboratory of Network Information System Technology(NIST), Institute of Electronics, Chinese Academy of Sciences, Beijing 100190, China"}], "References": [{"Title": "Visual SLAM for robot navigation in healthcare facility", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "113", "Issue": "", "Page": "107822", "JournalTitle": "Pattern Recognition"}, {"Title": "Blitz-SLAM: A semantic SLAM in dynamic environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "121", "Issue": "", "Page": "108225", "JournalTitle": "Pattern Recognition"}, {"Title": "Prior depth-based multi-view stereo network for online 3D model reconstruction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "136", "Issue": "", "Page": "109198", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 109402868, "Title": "TransOSV: Offline Signature Verification with Transformers", "Abstract": "Signature verification is a frequently-used forensics technology in numerous safety-critical situations. Although convolutional neural networks (CNNs) have made significant advancements in the field of signature verification, their reliance on local neighborhood operations poses limitations in capturing the global contextual relationships among signature strokes. To overcome this weakness, in this paper, we propose a novel holistic-part unified model named TransOSV based on the vision transformer framework to solve offline signature verification problem. The signature images are first encoded into patch sequences by the proposed transformer-based holistic encoder to learn the global signature representation. Second, considering the subtle local difference between the genuine signature and forged signature, we design a contrast based part decoder along with a sparsity loss, which are utilized to learn the discriminative part features. With the learned holistic features and part features, the proposed model is optimized by the contrast loss function. To reduce the influence of sample imbalance, we also formulate a new focal contrast loss function. Furthermore, we conduct the proposed model to learn signature representations for writer-dependent signature verification task. The experimental results demonstrate the potential of the proposed TransOSV model for both writer-independent and writer-dependent signature verification tasks, achieving remarkable performance improvements and competitive results on four widely-used offline signature datasets.", "Keywords": "", "DOI": "10.1016/j.patcog.2023.109882", "PubYear": 2024, "Volume": "145", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Human-Machine Hybrid Augmented Intelligence, Xi’an Jiaotong University, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Human-Machine Hybrid Augmented Intelligence, Xi’an Jiaotong University, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Human-Machine Hybrid Augmented Intelligence, Xi’an Jiaotong University, China"}, {"AuthorId": 4, "Name": "Changkai Li", "Affiliation": "National Key Laboratory of Human-Machine Hybrid Augmented Intelligence, Xi’an Jiaotong University, China"}, {"AuthorId": 5, "Name": "<PERSON>ning <PERSON>", "Affiliation": "National Key Laboratory of Human-Machine Hybrid Augmented Intelligence, Xi’an Jiaotong University, China"}], "References": [{"Title": "Offline signature verification using a region based deep metric learning network", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "118", "Issue": "", "Page": "108009", "JournalTitle": "Pattern Recognition"}, {"Title": "SVC-onGoing: Signature verification competition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>-<PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "108609", "JournalTitle": "Pattern Recognition"}, {"Title": "Multimodal channel-wise attention transformer inspired by multisensory integration mechanisms of the brain", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "130", "Issue": "", "Page": "108837", "JournalTitle": "Pattern Recognition"}, {"Title": "2C2S: A two-channel and two-stream transformer based framework for offline signature verification", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "118", "Issue": "", "Page": "105639", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 109402897, "Title": "A study of dynamic populations in geometric semantic genetic programming", "Abstract": "Allowing the population size to variate during the evolution can bring advantages to evolutionary algorithms (EAs), retaining computational effort during the evolution process. Dynamic populations use computational resources wisely in several types of EAs, including genetic programming. However, so far, a thorough study on the use of dynamic populations in Geometric Semantic Genetic Programming (GSGP) is missing. Still, GSGP is a resource-greedy algorithm, and the use of dynamic populations seems appropriate. This paper adapts algorithms to GSGP to manage dynamic populations that were successful for other types of EAs and introduces two novel algorithms. The novel algorithms exploit the concept of semantic neighbourhood. These methods are assessed and compared through a set of eight regression problems. The results indicate that the algorithms outperform standard GSGP, confirming the suitability of dynamic populations for GSGP. Interestingly, the novel algorithms that use semantic neighbourhood to manage variation in population size are particularly effective in generating robust models even for the most difficult of the studied test problems.", "Keywords": "Dynamic populations ; Genetic programming ; Geometric semantic genetic programming ; Semantic neighbourhood", "DOI": "10.1016/j.ins.2023.119513", "PubYear": 2023, "Volume": "648", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade Nova de Lisboa, Campus de Campolide, 1070-312 Lisboa, Portugal;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade Nova de Lisboa, Campus de Campolide, 1070-312 Lisboa, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Universidade Nova de Lisboa, Campus de Campolide, 1070-312 Lisboa, Portugal"}], "References": [{"Title": "Genetic programming for stacked generalization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "65", "Issue": "", "Page": "100913", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "A novel binary classification approach based on geometric semantic genetic programming", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "69", "Issue": "", "Page": "101028", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "GSGP-CUDA — A CUDA framework for Geometric Semantic Genetic Programming", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "", "Page": "101085", "JournalTitle": "SoftwareX"}, {"Title": "Genetic programming benchmarks: looking back and looking forward", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "3", "Page": "1", "JournalTitle": "ACM SIGEVOlution"}]}, {"ArticleId": 109402907, "Title": "A Tangent Release Manipulation Controlled by a Dual-Arm Space Robot", "Abstract": "<p>As people further develop space with advanced technology, space robots have played a significant role in on-orbit servicing missions. Space robots can carry out more risky and complicated missions with less cost than astronauts. Dual-arm space robots can perform complex on-orbit space missions more effectively than single-arm space robots. Since the coupled dynamics between the free-floating base and the arms exist in space robots, accurate coordinate control of the base and the arms is essential. Spacecraft release missions have been proposed to berth/deberth a spacecraft to a space station. Based on the existing release missions, a tangent release strategy is introduced in this paper, which can release a space object in the tangent direction of the final link of a space manipulator. This strategy can control a dual-arm space robot to deploy cargo/spacecraft in variable directions in 3D space without thrusters and the associated fuel consumption. For instance, this tangent release operation can transport cargo or modules of large-scale spacecraft needing on-orbit assembly. Considering model uncertainties, robust controllers again model uncertainties that are used to control the dual-arm space robot with high accuracy. Hence, a robust sliding mode controller (SMC) is utilized to accurately control the space robot to carry out the proposed tangent release strategy. For comparison, we select a conventional computed torque control (CTC) implemented by a PD-type controller. In the simulations, the SMC performs better in tracking accuracy and robustness against the model uncertainties than the PD controller. Numerical simulations indicate the feasibility and effectiveness of the tangent release manipulation of a space object by a dual-arm space robot.</p>", "Keywords": "", "DOI": "10.3390/act12080325", "PubYear": 2023, "Volume": "12", "Issue": "8", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical Engineering and Telecommunications, University of New South Wales, Sydney, NSW 2052, Australia; Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Manufacturing Engineering, University of New South Wales, Sydney, NSW 2052, Australia"}], "References": [{"Title": "An Obstacle-Avoidance Motion Planning Method for Redundant Space Robot via Reinforcement Learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "2", "Page": "69", "JournalTitle": "Actuators"}, {"Title": "Trajectory Tracking and Adaptive Fuzzy Vibration Control of Multilink Space Manipulators with Experimental Validation", "Authors": "<PERSON><PERSON> Feng; Wei<PERSON> Chen; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "4", "Page": "138", "JournalTitle": "Actuators"}, {"Title": "Dynamic Modeling and Attitude–Vibration Cooperative Control for a Large-Scale Flexible Spacecraft", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "12", "Issue": "4", "Page": "167", "JournalTitle": "Actuators"}]}, {"ArticleId": *********, "Title": "Fixed-time adaptive tracking control of two moving targets for a class of non-linear multi-agent systems", "Abstract": "In this research paper, the fixed-time adaptive tracking of two moving targets for a class of unknown non-linear multi-agent systems based on a distributed competition approach is studied. Each agent simultaneously participates in two competitions based on the k-Winner Take All (k-WTA) approach to obtain its allocation status. Using a mean value estimator, the agent can calculate its allocation status only by its neighbor&#x27;s information. Since an agent can only track one target, an extended tracking error is introduced, where if the agent wins one of the competitions, it will track the assigned target, and if the agent wins both competitions, by selecting one of the targets, track the selected target. Also, the agent which is not win any competitions, return to a specified point. Then, using neural networks and adaptive techniques, an adaptive fixed-time controller is introduced for the agents to track the target based on the allocation status or to return to the specified point. Analysis of the stability of the closed-loop system through the <PERSON><PERSON><PERSON><PERSON> theorem is investigated. Finally, a simulation study to show the efficiency of the theoretical results is presented.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119503", "PubYear": 2023, "Volume": "648", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, Malek-Ashtar University of Technology, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, Malek-Ashtar University of Technology, Tehran, Iran;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical and Computer Engineering, Malek-Ashtar University of Technology, Tehran, Iran"}], "References": [{"Title": "Distributed assignment with limited communication for multi-robot multi-target tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "44", "Issue": "1", "Page": "57", "JournalTitle": "Autonomous Robots"}, {"Title": "Distributed guaranteed two-target tracking over heterogeneous sensor networks under bounded noises and adversarial attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "535", "Issue": "", "Page": "187", "JournalTitle": "Information Sciences"}, {"Title": "Fixed-time control design for nonlinear uncertain systems via adaptive method", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "104704", "JournalTitle": "Systems & Control Letters"}, {"Title": "Multi-robot competitive tracking based on k-WTA neural network with one single neuron", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "460", "Issue": "", "Page": "1", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 109402929, "Title": "Semi-supervised adversarial discriminative learning approach for intelligent fault diagnosis of wind turbine", "Abstract": "Wind turbines play a crucial role in renewable energy generation systems and are frequently exposed to challenging operational environments . Monitoring and diagnosing potential faults during their operation is essential for improving reliability and reducing maintenance costs. Supervised learning using data-driven techniques, particularly deep learning , offers a viable approach for developing fault diagnosis models. However, a significant challenge in practical wind power equipment lies in the scarcity of annotated samples required to train these models effectively. This paper proposes a semi-supervised fault diagnosis approach specifically designed for wind turbines, aiming to address this challenge. Initially, a semi-supervised deep neural network is constructed using adversarial learning, where a limited set of annotated samples is used in conjunction with a vast amount of unannotated samples. The health status features present in the unannotated samples are leveraged to capture a generalized representation of the underlying features. Subsequently, a metric learning-guided discriminative features enhancement technique is employed to improve the separability of different manifolds, thereby enhancing the performance of the semi-supervised training process. By employing this methodology, it becomes possible to develop a fault diagnosis model with superior accuracy using only a limited amount of annotated samples. Comprehensive fault diagnosis experiments were conducted on a wind turbine fault dataset, revealing the efficacy and superiority of the presented methodology.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119496", "PubYear": 2023, "Volume": "648", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "Te Han", "Affiliation": "Center for Energy and Environmental Policy Research, Beijing Institute of Technology, Beijing, 100081, China;School of Management and Economics, Beijing Institute of Technology, Beijing, 100081, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Energy and Power Engineering, Tsinghua University, Beijing, 100084, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Engineering Research Center for Big Data Software, Beijing, 100084, China;School of Software, Tsinghua University, Beijing, 100084, China"}], "References": [{"Title": "Fine-grained action segmentation using the semi-supervised action GAN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "98", "Issue": "", "Page": "107039", "JournalTitle": "Pattern Recognition"}, {"Title": "A survey on semi-supervised learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "2", "Page": "373", "JournalTitle": "Machine Learning"}, {"Title": "Single and simultaneous fault diagnosis of gearbox via a semi-supervised and high-accuracy adversarial learning framework", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "198", "Issue": "", "Page": "105895", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A comprehensive review on convolutional neural network in machine fault diagnosis", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "417", "Issue": "", "Page": "36", "JournalTitle": "Neurocomputing"}, {"Title": "Adversarial feature distribution alignment for semi-supervised learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "202", "Issue": "", "Page": "103109", "JournalTitle": "Computer Vision and Image Understanding"}, {"Title": "A novel deep metric learning model for imbalanced fault diagnosis and toward open-set classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "220", "Issue": "", "Page": "106925", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Vector of Locally and Adaptively Aggregated Descriptors for Image Feature Representation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107952", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-class fuzzy support matrix machine for classification in roller bearing fault diagnosis", "Authors": "Haiyang Pan; Haifeng Xu; <PERSON><PERSON>", "PubYear": 2022, "Volume": "51", "Issue": "", "Page": "101445", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Semisupervised image classification by mutual learning of multiple self‐supervised models", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "37", "Issue": "5", "Page": "3117", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Intelligent health indicator construction for prognostics of composite structures utilizing a semi-supervised deep neural network and SHM data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "117", "Issue": "", "Page": "105502", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A nearly end-to-end deep learning approach to fault diagnosis of wind turbine gearboxes under nonstationary conditions", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105735", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Non-parallel bounded support matrix machine and its application in roller bearing fault diagnosis", "Authors": "Haiyang Pan; Haifeng Xu; <PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "395", "JournalTitle": "Information Sciences"}, {"Title": "Fast semi-supervised self-training algorithm based on data editing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "626", "Issue": "", "Page": "293", "JournalTitle": "Information Sciences"}, {"Title": "DML-PL: Deep metric learning based pseudo-labeling framework for class imbalanced semi-supervised learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "626", "Issue": "", "Page": "641", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 109402934, "Title": "Timing and Performance Metrics for TWR-K70F120M Device", "Abstract": "<p>Currently, single-board computers (SBCs) are sufficiently powerful to run real-time operating systems (RTOSs) and applications. The purpose of this research was to investigate the timing performance of an NXP TWR-K70F120M device with μClinux OS on concurrently running tasks with real-time features and constraints, and provide new and distinct technical data not yet available in the literature. Towards this goal, a custom-built multithreaded application with specific compute-intensive sorting and matrix operations was developed and applied to obtain measurements in specific timing metrics, including task execution time, thread waiting time, and response time. In this way, this research extends the literature by documenting performance results on specific timing metrics. The performance of this device was additionally benchmarked and validated against commonly used platforms, a Raspberry Pi4 and BeagleBone AI SBCs. The experimental results showed that this device stands well both in terms of timing and efficiency metrics. Execution times were lower than with the other platforms, by approximately 56% in the case of two threads, and by 29% in the case of 32-thread configurations. The outcomes could be of practical value to companies which intend to use such low-cost embedded devices in the development of reliable real-time industrial applications.</p>", "Keywords": "", "DOI": "10.3390/computers12080163", "PubYear": 2023, "Volume": "12", "Issue": "8", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CSLab Computer Systems Laboratory, Department of Digital Systems, University of Thessaly, 41500 Larisa, Greece"}], "References": [{"Title": "Co-Design of Multicore Hardware and Multithreaded Software for Thread Performance Assessment on an FPGA", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "5", "Page": "76", "JournalTitle": "Computers"}, {"Title": "Real-time performance analysis of distributed multithreaded applications in a cluster of ARM-based embedded devices", "Authors": "<PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "2", "Page": "105", "JournalTitle": "International Journal of High Performance Systems Architecture"}, {"Title": "The Potential of Low-Power, Cost-Effective Single Board Computers for Manufacturing Scheduling", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "217", "Issue": "", "Page": "904", "JournalTitle": "Procedia Computer Science"}]}, {"ArticleId": 109402947, "Title": "Revisiting the Software-Efficient Stream Ciphers RCR-64 and RCR-32", "Abstract": "<p>The synchronous stream ciphers RCR-64 and RCR-32 designed by <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON> are strengthened variants of the ciphers TPy and TPypy (designed by <PERSON><PERSON><PERSON> and <PERSON><PERSON>), respectively. The RCR ciphers have remained unbroken since they were published in 2007. In this paper, we present arguments that not only support the designers’ security claims but suggest, in general, that the ciphers are secure against several classes of cryptanalytic attacks. We find that the ciphers are best used with 256-bit keys and 384-bit IVs. We also suggest ways to protect software implementations of the RCR ciphers against (cache-)timing and processor flag attacks. Our performance evaluation suggests that the protected implementation of the RCR-64 encrypts long messages at speeds comparable to some of the fastest stream ciphers available today. Consequently, we find that the RCR ciphers may be well suited for PC-based applications in general and streaming audio / video applications in particular. This is the first paper to present a detailed study on the security and performance of the RCR ciphers.</p>", "Keywords": "", "DOI": "10.1093/comjnl/bxad084", "PubYear": 2024, "Volume": "67", "Issue": "4", "JournalId": 3416, "JournalTitle": "The Computer Journal", "ISSN": "0010-4620", "EISSN": "1460-2067", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Indira Gandhi Centre for Atomic Research , Kalpakkam , India;Homi Bhabha National Institute (HBNI) , Mumbai , India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Madras Fintech Services Pvt. Ltd , Chennai , India;Madras School of Economics , Chennai , India"}, {"AuthorId": 3, "Name": "<PERSON>ramanian", "Affiliation": "<PERSON><PERSON> Bhabha National Institute (HBNI) , Mumbai , India;The Institute of Mathematical Sciences , Chennai , India"}], "References": [{"Title": "Side channel analysis of SPECK", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "6", "Page": "655", "JournalTitle": "Journal of Computer Security"}, {"Title": "On the Security of the Stream Ciphers RCR-64 and RCR-32", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "12", "Page": "3091", "JournalTitle": "The Computer Journal"}]}, {"ArticleId": 109402963, "Title": "Experimental investigation of cutting performance for circular saw blade body modification by resin layers toward green manufacturing", "Abstract": "<p>Cemented carbide circular saw blades are widely used in the metal and nonmetal manufacturing industry for cut-off and cut-tail production. High energy consumption and intense noise emission from conventional-type blades are urgent environmental issues to be solved. Hence, a laminated-resin blade is proposed to study the effect of adding a damping structure to the blade body on its cutting performance. Apart from reduced energy consumption, lower noise and high-quality surfaces are required by manufacturers. In this work, a laminated-resin blade and a conventional blade were compared regarding the details of cutting force and energy consumption, noise emission, and surface integrity. In addition, the effect of cutting fluid on conventional saw blades was analyzed in terms of cutting force and noise emission. Experimental results show that the cutting force and noise of the laminated-resin blade were less than that of the conventional one, indicating reductions of 27.8% and 18 dB, respectively. Especially a reduction of more than 20 dB sound level was achieved in the high-frequency band. Lower cutting force attained by the laminated-resin blade showed that smoother cuts could be achieved. Cutting with cutting fluid is superior to dry cutting in cutting performance. The research sheds new light on the design of damping structures on saw blade bodies and quantitative analysis of the cutting performance.</p>", "Keywords": "Circular saw blade; Laminated-resin; Cutting force; Noise; Surface integrity", "DOI": "10.1007/s00170-023-11979-w", "PubYear": 2023, "Volume": "128", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High-Efficiency and Clean Mechanical Manufacture, Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan, China; National Demonstration Center for Experimental Mechanical Engineering Education, Shandong University, Jinan, China; Rizhao Research Institute, Shandong University, Rizhao, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of High-Efficiency and Clean Mechanical Manufacture, Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan, China; National Demonstration Center for Experimental Mechanical Engineering Education, Shandong University, Jinan, China; Rizhao Research Institute, Shandong University, Rizhao, China"}, {"AuthorId": 3, "Name": "Depeng Sun", "Affiliation": "Key Laboratory of High-Efficiency and Clean Mechanical Manufacture, Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan, China; National Demonstration Center for Experimental Mechanical Engineering Education, Shandong University, Jinan, China; Rizhao Research Institute, Shandong University, Rizhao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of High-Efficiency and Clean Mechanical Manufacture, Ministry of Education, School of Mechanical Engineering, Shandong University, Jinan, China; National Demonstration Center for Experimental Mechanical Engineering Education, Shandong University, Jinan, China"}], "References": [{"Title": "Modeling for prediction of sawing force based on the maximum undeformed chip distribution in the granite sawing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "124", "Issue": "1-2", "Page": "111", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 109402972, "Title": "Energy efficient selection of spreading factor in LoRaWAN-based WBAN medical systems", "Abstract": "Recently, the Internet of Things has been leading the technological revolution in several fields and applications. This revolution affected healthcare services adding more value to the concept of e-health. To improve the quality of these services, remote monitoring allows healthcare professionals to observe and diagnose their patients without being physically present. Since the spreading factor affects the energy consumption, the data rate, the receiver sensitivity and the time on air. A monitoring healthcare system using LoRa technology is proposed on this work to adopt the data transmission in a reliable and energy-efficient way. We propose a method to select the most convenient spreading factor based on the patients medical state. This method takes in consideration the distance to gateway, the packet error rate and the energy consumption requirements. We first determine the criticality level of the patient using the Early Warning Score system. Second, a decision about the optimal spreading factor is taken based on this level. We use a method based on the technique for order preference by similarity to an ideal solution to select a spreading factor. We use real data to show that the results reflect an acceptable increase in energy consumption while applying our selection method. In contrast, the results show that the selection method reduces the error rate especially for patients with a high critical level.", "Keywords": "", "DOI": "10.1016/j.iot.2023.100896", "PubYear": 2023, "Volume": "24", "Issue": "", "JournalId": 3566, "JournalTitle": "Internet of Things", "ISSN": "2543-1536", "EISSN": "2542-6605", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nantes Université, CNRS, IETR, UMR 6164, F-85000, La Roche-sur-Yon, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Nantes Université, CNRS, IETR, UMR 6164, F-85000, La Roche-sur-Yon, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "ICCS-Lab, Computer Science Department, American University of Culture and Education (AUCE), Beirut, Lebanon"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "ICCS-Lab, Computer Science Department, American University of Culture and Education (AUCE), Beirut, Lebanon"}], "References": []}, {"ArticleId": 109403038, "Title": "SIMILARITY ALGOR<PERSON><PERSON>MS FOR FUZZY JOIN COMPUTATION IN BIG DATA PROCESSING ENVIRONMENT", "Abstract": "<p>Big data processing is attracting the interest of many researchers to process large-scale datasets and extract useful information for supporting and providing decisions. One of the biggest challenges is the problem of querying large datasets. It becomes even more complicated with similarity queries instead of exact match queries. A fuzzy join operation is a typical operation frequently used in similarity queries and big data analysis. Currently, there is very little research on this issue, thus it poses significant barriers to the efforts of improving query operations on big data efficiently. As a result, this study overviews the similarity algorithms for fuzzy joins, in which the data at the join key attributes may have slight differences within a fuzzy threshold. We analyze six similarity algorithms including <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LCS, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON> <PERSON>, to show the difference between these algorithms through the three criteria: output enrichment, false positives/negatives, and the processing time of the algorithms. Experiments of fuzzy joins algorithms are implemented in the Spark environment, a popular big data processing platform. The algorithms are divided into two groups for evaluation: group 1 (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and LCS) and group 2 (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON> <PERSON>). For the former, Levenshtein has an advantage over the other two algorithms in terms of output enrichment, high accuracy in the result set (false positives/negatives), and acceptable processing time. In the letter, <PERSON><PERSON><PERSON> is considered the worst algorithm considering all three criteria mean while <PERSON><PERSON> <PERSON> <PERSON> algorithm has more output richness and higher accuracy in the result set. The overview of the similarity algorithms in this study will help users to choose the most suitable algorithm for their problems.</p>", "Keywords": "Fuzzy joins;similarity algorithms;set-similarity joins;big data processing;Spark", "DOI": "10.15625/1813-9663/17589", "PubYear": 2023, "Volume": "39", "Issue": "2", "JournalId": 41141, "JournalTitle": "Journal of Computer Science and Cybernetics", "ISSN": "1813-9663", "EISSN": "1813-9663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>h Long University of Technology and Education, Viet Nam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Can Tho University, Viet Nam"}], "References": []}, {"ArticleId": 109403040, "Title": "An end-to-end DNN-HMM based system with duration modeling for robust earthquake detection", "Abstract": "Developing a reliable and robust automatic earthquake detection system is quite a challenging and highly necessary task as two conditions can make this task a difficult one. First, earthquake detection systems may perform more poorly if they are employed in a region that is different from the one where the training database corresponds to. Systems trained with local databases are assumed to perform better. Nevertheless, these databases are usually limited. Second, the performance of such systems worsens when the SNR of the seismogram signals decreases. This paper proposes an end-to-end DNN-HMM based scheme to address these limitations, i.e. it does not require previous phase-picking, backed by engineered features and combined with duration modeling of states and seismic events. The proposed engine requires 10- or 15-times fewer parameters than state-of-the-art methods and therefore needs a smaller training database. Modeling duration can improve the noise robustness of the detection system significantly, particularly with limited training data; having a negligible increase in the number of training parameters. The system described here provides a F1-score 101% higher on average than schemes published elsewhere with Iquique and North Chile databases. It provides a reduction in F1-score equal to 10% when the average SNR is reduced by approximately 18 dB. This reduction in F1-score is at least half of the one observed with the state-of-the-art schemes in the same testing conditions. With respect to the detection of small earthquakes at short epicenter-station distances, the averaged precision provided by the DNN-HMM system with duration modeling is at least 5% higher than other systems. Introduction It is important to detect earthquakes of all magnitudes including those that are not perceptible, since a large number of small earthquakes and their frequency makes them the key to understand the sequence of events that link previous, main earthquakes and aftershocks (Brodsky, 2019). The evaluation of local seismicity can be a valuable tool to mitigate the risk of industrial activities since it would provide feedback to engineers about pre-existing fractures and faults of the exploitation site. Several studies already applied microseismic monitoring to characterize the source and improve the understanding of natural fracture systems. This can be seen in studies related to slope stability analysis (Xu et al., 2011), gas dynamic disaster warning (Wang et al., 2019, Wang et al., 2019), collapse prediction in underground power plants, mines, tunnels, and other projects (Ma et al., 2018), water inrush (Cheng et al., 2018), and civil engineering standards (Zaalishvili et al., 2016), among others. Generally, seismic monitoring comprises of the following stages: seismic detection and phase picking, magnitude estimation, event association and hypocenter determination. It is paramount to mention that seismic detection and phase picking are related, but they are not the same. Detection refers to identifying seismic signals among a wide variety of non-seismic signals and noise recorded by a seismic sensor. Phase selection is the measurement on a seismogram of the arrival times of the P-wave and S-wave. Notably, as the detection can use a more global view of seismic waves (Mousavi et al., 2020), it is interesting to introduce this technique on an end-to-end basis where earthquakes could be found within a continuous signal without explicitly picking the seismic phases. This kind of end-to-end approach can be applied to magnitude estimation (Cofré et al., 2022; Mousavi and Beroza, 2019), and epicenter determination (Zhang et al., 2022; Yoma et al., 2022). In recent years, the seismological community has refined seismic monitoring, expanding the number of seismic stations and improving measurement resolution, which has generated an increase in the volume of data (Kanamori, 1988). This makes it necessary to develop efficient and practical tools that can detect automatic seismic events. Experts can identify seismic events by correctly recognizing the beginning and end of the earthquake, even in the presence of noise in the signals. However, given a large amount of data, this is a problem as it demands a great load of human resources and time. Current solutions to analyze large amounts of noisy data have mostly been signal analysis algorithms with statistical elements or pattern recognition (Akazawa, 2004). An example is the STA/LTA algorithm (Allen, 1978), which continuously compares over time the amplitude averages of two consecutive windows of a seismic signal. This method encounters difficulties in setting parameters and is prone to false positives in the presence of high background noise (Mousavi et al., 2019). Another example is “Fingerprint and Similarity Thresholding” (FAST) (Yoon et al., 2015), which extracts features from continuous waveforms and converts them into a fingerprint database. These fingerprints are compact representations of short segments of continuous waveform data and are organized in a dictionary structure for efficient similarity searching through locality-sensitive hashing (Perol, 2018; Kong et al., 2019).FAST requires the configuration of many parameters and is vulnerable to false detections in the presence of frequent local noise signals with a single station (Bergen and Beroza, 2018). Since 1990s (Joswig, 1990), different machine learning (ML) algorithms have been proposed to cope with the rapid increase in the volume of seismic data available to carry out widespread earthquake detection. For example, methods based on Gaussian Mixture Model-Hidden Markov Model (GMM-HMM), which have been employed successfully in speech recognition (Young, 2002), have also been employed in seismology. Most of the research papers using HMM on seismic monitoring has aimed to detect snow avalanches (Heck et al., 2018), classify microseisms in underground mines (Peng et al., 2019), detect landslide (Dammeier et al., 2016), detect and classify volcano events (Alasonati et al., 2006; Gutiérrez et al., 2009; Cortés et al., 2014, 2021; Ohrnberger, 2001; Bicego et al., 2013; Bhatti et al., 2016) and detect tectonic earthquakes (Benítez, 2014; Beyreuther et al., 2012; Beyreuther and Wassermann, 2008, 2011). Even though the application of HMM's to seismology does not consider the duration of the analyzed events, there are some exceptions. Benítez et al. (2007) proposed that the number of states per model is a parameter that can be tuned in the training procedure, and that the number of HMM states is equivalent to the minimum duration of the corresponding event. However, the distributions of event durations and their inner stages are not modeled. Beyreuther et al. (2012) proposed a detection system based on GMM-HMM; whereby the states are grouped according to the similarity of the Gaussians, which indirectly incorporates a minimum duration for some segments of the seismic signal. Beyreuther and Wassermann (2011) employed HSMM (Hidden semi-Markov model) to approximate the duration probabilities with Gaussian distributions, whose parameters are estimated with the Forward-Backward algorithm applied to GMM observation probabilities. The maximum and minimum durations are obtained as a percentile of the Gaussian duration distributions. HSMM was previously proposed in (Oura et al., 2006; Oura et al., 2008) where a weighted finite state transducer (WFST) is used as a decoder that applies the minimum and maximum length criteria for seismic events. In this paper, the scheme proposed in (Yoma, et al., 2001; Yoma and Sánchez, 2002) is used, where the durations of the states and events can be fully characterized by the forced Viterbi alignment after training the models. This is regardless of how the observation probabilities are modeled, which in our case corresponds to DNN (Deep Neural Network), without the need of the Forward-Backward algorithm for GMM. In other words, the average, minimum and maximum durations, and the corresponding standard deviation are obtained directly from the HMM trained with, for example, DNN. The incorporation of the event duration and state restrictions is performed in a very direct way with the Viterbi decoding algorithm, which in turn more efficient than WFST as mentioned in (Beyreuther and Wassermann, 2011). Bhatti et al. (2016) presented an automatic detection system for volcanic events based on GMM-HMM with event state and duration models. The incorporation of state and event duration restrictions reduced the false positive rate by up to 31%, with a true positive accuracy equal to 94%. HMM is capable of modeling time-varying series like those representing seismic events by explicitly considering the time dependence of data very efficiently with state chains (Danişman and Kocer, 2021). The Viterbi algorithm employs a trained HMM to obtain the most likely alignment of observed frames and states accomplishing the detection, segmentation and classification functionalities simultaneously. Currently, several deep learning-based methods have been applied to seismology. CNN-based approaches have been employed to address the phase picking problem in (Woollam et al., 2019; Soto and Schurr, 2021; Ross et al., 2018; Zhu and Beroza, 2019; Wang J. et al., 2019; Zhou et al., 2019). The detection of seismic events has been approached with methods based on the combinations of Recurrent Neural Network (RNN), Long Short-Term Memory (LSTM), transformers or Convolutional Neural Network (CNN) in (Perol et al., 2018; Lomax et al., 2019; Mousavi et al., 2019; Chin et al., 2020; Saad and Chen, 2020; Shen and Shen, 2021; Huang et al., 2020; Mousavi et al., 2020; Saad et al., 2021; Xiao et al., 2021, Xiao et al., 2021; Li et al., 2022; Zhu et al., 2022).Not withstanding, RNN and LSTM have been adopted because of their capability to handle time series. However, these architectures present at least three important drawbacks. First, their ability to handle extremely long features is reduced (Kim and Lee, 2019; Fu et al., 2022; Xiao Y. et al., 2021) and the back-propagation algorithm may result in vanishing gradients (Kumari et al., 2021). Second, because of the limitation to process long time sequences and their standard definitions, these recurrent neural networks cannot model state and event duration, i.e. states cannot be indexed discretely and their duration cannot be constrained. Third, solutions based on recurrent networks that are found in the literature use multiple layers to achieve the purpose that they were designed for in other fields. Combining CNN layers to extract salient features from the raw data with LSTM layers to capture short and long-term dependency is a popular strategy found elsewhere (Li et al., 2020; Kim and Cho, 2019). As a result, most deep learning-based methods for earthquake detection are complex networks that use many parameters to be trained; meaning that these models require large training databases to avoid overfitting (Perol et al., 2018). Globally, most seismogenic zones exhibit close spatial relationships with plate tectonic boundaries (Pankow et al., 2020). For centuries, Chile has experienced extreme seismic activity due to the subduction of the oceanic plate over the South American plate. Records of Chilean seismicity range from earthquakes with magnitudes greater than M1.2 and those whose magnitudes exceed M9, such as the 1960 Valdivia earthquake considered the largest in history, M9.5 (Lomnitz, 2004). Mega-earthquakes in this region can occur offshore, with a tsunami potential (Cesca et al., 2016), or on land. Chile has been positioned as one of the most seismogenic regions in the world (Lomnitz, 2004). Considering the variability of the Chilean seismic records, it is important to generate models that consider these local effects. Thus, these schemes must be trained with local databases. According to (Iaccarino et al., 2021), the variability of the data is better represented when models exported from other regions are not used. This problematic is shared with any seismogenic region in the world, and it raises the problem of training robust and precise models with limited training data. One of the challenges that stand out in the study of local seismicity is the problem of the limited record of earthquakes. Generally, the number of available events taken from earthquake catalogs is insufficient to build an accurate detector (Bergen and Beroza, 2017). It restricts the possibility of retraining a model that requires an extensive database with local data. According to the Gutenberg-Richter relationship (Gutenberg and Richter, 1954), larger earthquakes are less frequent, so it is almost impossible to generate a large database with the same frequency of different magnitudes. Another major limitation is the low signal-to-noise ratio (SNR) in microseismic recordings. Below a given magnitude, detection at an adequate number of seismic stations may not be achieved; leading to uncatalogued earthquakes and training data biased with respect to larger events (Van der Elst, 2021). It is required that precise characterization is carried out with close observations as the amplitude of small magnitude earthquakes expresses a solid sensitivity for local geological heterogeneity (Kintner et al., 2021), i.e. short event-station distances. Given the relative sparsity of earthquakes recorded locally, training ML based methods for earthquake search or detection can be challenging due to limited training data (Kong et al., 2019). A possible solution for the problem of limited training data would be the use of engineered features instead of raw data. Crafted or engineered features can reduce the input dimensionality and represent better the characteristic of the target process (Zhou et al., 2017). Engineered features lead to a more compact description of the process that is being modeled because they compress the raw original signal into fewer parameters, while preserving the most important features and reducing redundancy (Kao et al., 2010). As a result, ML architectures should require less coefficients to be trained. This paper proposes an end-to-end system for automatic detection of seismic events based on a DNN-HMM system with state and event duration models. The resulting small number of trainable parameters stands out from this model since it was reduced by more than one order of magnitude compared to deep learning based methods published elsewhere. Interestingly, the restriction regarding the trained data size is softened. The proposed system was evaluated with databases from the north of Chile. The state and event duration approach allows to introduce constraints for the minimum and maximum durations of the HMM states and the entire event. Incorporating state and event duration modeling in the Viterbi decoding procedure increases the detection precision complementarily with respect to spectral features and decreases the false positive rate. It is worth highlighting that the combination of DNN-HMM with state and event duration constraints to address the problem of tectonic earthquake detection has not been explored in depth in the literature, particularly with limited training data. Section snippets Database The proposed system was evaluated with three data sets: North Chile, Iquique, and Nearby Location. All the earthquakes that are composed of these data sets were observed in stations located in northern Chile as shown in Fig. 1. The P, S, surface, and coda waves were labelled by analysts. Databases were downloaded from the Incorporated Research Institutions for Seismology (IRIS). A summary of their most relevant characteristics such as magnitude ranges, event-station distance, depth, and average Performance metrics The metrics employed to evaluate and compare the performance of the proposed earthquake detection system are: precision, recall and F1-score, which are defined as: r e c a l l = T P T P + F N p r e c i s i o n = T P T P + F P F 1 − s c o r e = 2 ∙ p r e c i s i o n ∙ r e c a l l p r e c i s i o n + r e c a l l where: TP are the true positive earthquake detections; FP are the false positive earthquake detections; and FN or false negative are the non-detected earthquake. The criterion adopted to define whether an earthquake is detected or not is when the absolute time Hyperparameter and training optimization A batch size and the number of epochs were made equal to 56 and 20, respectively. ReLU and Softmax were employed as the hidden and output activation functions, respectively. Cross-entropy was employed as a loss function and the learning rate was made equal to 0.0001. The hyperparameters that were tuned corresponded to the context window, as well as the number of hidden layers and nodes per layer. Fig. 10 shows F1-score vs the number of training parameters obtained with the validation subset of Conclusions Given the increase in seismic data volume, it is necessary to develop efficient, reliable, and robust tools that can detect earthquakes automatically. Two conditions make the task particularly difficult. First, earthquake detection systems may perform more poorly or at least below the optimal potential accuracy if they are tested in a region that is different from the one where the training database was observed. In other words, systems trained with local databases should perform better. Code availability section The source codes are available for downloading at the link: https://github.com/lptvUchile/DH-DM-Earthquake-detection . Contact: [email protected] Hardware requirements The results report here was obtained with a computer with an Intel Core i7-7700 processor, 32 GB Ram. The software that is provided with this submission does not need any further GMM-HMM training. Program language: Python.Software required: Python. Optional for research purposes: Kaldi. Authorship contribution statement Catalina Murúa : Methodology, software, writing. Marcelo Marín : Software, writing. Aarón Cofré : Data curation, writing. Jorge Wuth : Software methodology support. Oscar Vásquez Pino : Data labeling and review. Néstor Becerra Yoma : PI, research coordinator, writing and methodology support. Declaration of competing interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments The research reported here was funded by grant ANID-FONDEF ID20i-10212 . The seismicity catalogues were provided by the Chilean National Seismological Center (Centro Sismologico Nacional, CSN) . References (80) S.M. Bhatti et al. Automatic detection of volcano-seismic events by modeling state and event duration in hidden Markov models J. Volcanol. Geoth. Res. (2016) G. Cortés et al. Parallel System Architecture (PSA): an efficient approach for automatic recognition of volcano-seismic events J. Volcanol. Geoth. Res. (2014) E. Fu et al. Temporal self-attention-based Conv-LSTM network for multivariate time series prediction Neurocomputing (2022) W.C. Kao et al. Local contrast enhancement and adaptive feature extraction for illumination-invariant face recognition Pattern Recogn. (2010) T.Y. Kim et al. Predicting residential energy consumption using CNN-LSTM neural networks Energy (2019) J.G. Kim et al. Appliance classification by power signal analysis based on multi-feature combination multi-layer LSTM Energies (2019) P. Li et al. (2020) T.H. Ma et al. Rockburst mechanism and prediction based on microseismic monitoring Int. J. Rock Mech. Min. Sci. (2018) N. Xu et al. Microseismic monitoring and stability analysis of the left bank slope in Jinping first stage hydropower station in Southwestern China Int. J. Rock Mech. Min. Sci. (2011) N.B. Yoma et al. End-to-end LSTM based estimation of volcano event epicenter localization J. Volcanol. Geoth. Res. (2022) L. Zhou et al. Machine learning on big data: opportunities and challenges Neurocomputing (2017) T. Akazawa A Technique for Automatic Detection of Onset Time of P-And S-Phases in Strong Motion Records,” Presented at the 13th World Conf (2004) P. Alasonati et al. Signal classification by wavelet-based hidden Markov models: application to seismic signals of volcanic origin R.V. Allen Automatic earthquake recognition and timing from single traces Bull. Seismol. Soc. Am. (1978) M.C. Benítez et al. Continuous HMM-based seismic-event classification at deception island, Antarctica IEEE Trans. Geosci. Rem. Sens. (2007) C. Benítez et al. A comparative study of classifiers based on HMM, GMM, and SVM for the VT, LP, and Noises discrimination task K.J. Bergen et al. Automatic earthquake detection by active learning K.J. Bergen et al. Detecting earthquakes over a seismic network using single-station similarity measures Geophys. J. Int. (2018) M. Beyreuther et al. Continuous earthquake detection and classification using discrete Hidden Markov Models Geophys. J. Int. (2008) M. Beyreuther et al. Hidden semi-Markov Model based earthquake classification system using Weighted Finite-State Transducers Nonlinear Process Geophys. (2011) M. Beyreuther et al. Constructing a Hidden Markov Model based earthquake detector: application to induced seismicity Geophys. J. Int. (2012) M. Bicego et al. Classification of seismic volcanic signals using hidden-markov-model-based generative embeddings IEEE Trans. Geosci. Rem. Sens. (2013) E.E. Brodsky The importance of studying small earthquakes Science (2019) S. Cesca et al. The Mw 8.1 2014 Iquique, Chile, seismic sequence: a tale of foreshocks and aftershocks Geophys. J. Int. (2016) S. Cheng et al. Study on energy band characteristic of microseismic signals in water inrush channel J. Geophys. Eng. (2018) T.-L. Chin et al. Intelligent real-time earthquake detection by recurrent neural networks IEEE Trans. Geosci. Rem. Sens. (2020) A. Cofré et al. End-to-End LSTM-based earthquake magnitude estimation with a single station Geosci. Rem. Sens. Lett. IEEE (2022) G. Cortés et al. Practical Volcano-independent recognition of seismic events: VULCAN.ears project Front. Earth Sci. (2021) F. Dammeier et al. Automatic detection of alpine rockslides in continuous seismic data using hidden Markov models J. Geophys. Res.: Earth Surf. (2016) O. Danişman et al. Fitting hidden Markov model to earthquake data: a case study in the aegean sea Karaelmas Science and Engineering Journal (2021) T.G. Dietterich Approximate statistical tests for comparing supervised classification learning algorithms Neural Comput. (1998) B. Gutenberg et al. Seismicity of the Earth and Associated Phenomena (1954) L. Gutiérrez et al. Volcano-seismic signal detection and classification processing using hidden Markov models. Application to San Cristóbal volcano, Nicaragua M. Heck et al. Automatic detection of snow avalanches in continuous seismic data using hidden Markov models Nat. Hazards Earth Syst. Sci. (2018) X.D. Huang et al. Hidden Markov Models for Speech Recognition (1990) X. Huang et al. CrowdQuake: a networked system of low-cost sensors for earthquake detection via deep learning A.G. Iaccarino et al. Earthquake early warning system for structural drift prediction using machine learning and linear regressors Front. Earth Sci. (2021) M. Joswig Pattern recognition for earthquake detection Bull. Seismol. Soc. Am. (1990) H. Kanamori Importance of historical seismograms for geophysical research J.A. Kintner et al. Local‐distance seismic event relocation and relative magnitude estimation, applications to mining related seismicity in the powder river basin, Wyoming Bull. Seismol. Soc. Am. (2021) View more references Cited by (0) Recommended articles (6) Research article Determining the ecological compensation standards based on willingness to accept (WTA) for intensive agricultural production areas: A case in China Applied Geography, Volume 158, 2023, Article 103051 Show abstract Ecological compensation is an effective way to encourage farmers to use minimal chemical fertilizers by paying for their losses when agricultural planting methods change. In this research, we conducted field surveys and created a conceptual framework to determine the compensation standard based on farmers' willingness to accept (WTA) and ecosystem service values in a rotational double cropping area of winter wheat–summer maize, Huantai County, China. The results showed that the farmers’ average WTA on the chemical fertilizer reduction application fluctuates within a range of 250.00–7740.00 CNY/hm<sup>2</sup>. The limiting compensation standards range from 362.74 to 4823.43 CNY/hm<sup>2</sup> and the ceiling standard is from 367.29 to 4851.83 CNY/hm<sup>2</sup> by reducing chemical fertilizer application from 5% to 95%. Huantai County has a potential of 15%–35% for chemical fertilizer reduction, and the compensation standard is the lowest (362.74 CNY/hm<sup>2</sup>) when 25% of the current chemical fertilizer usage is reduced. Our research provided a valid approach to determining ecological compensation standards toward the aims of efficiency and sustainability for protecting farmland ecology and highlighting the critical ecosystem service changes caused by the farmland fertilizer reduction program in China. Research article Structural embeddedness, entrepreneurial behavior, and firm performance in the industry network of small tourism enterprises: The moderating role of relational embeddedness and leadership self-efficacy Journal of Hospitality and Tourism Management, Volume 56, 2023, pp. 431-442 Show abstract The survival and economic sustainability of small tourism enterprises (STEs) represent pressing industry issues that require attention. Drawing on network embedding theory, this paper proposes a conceptual framework that links the structural embeddedness of STEs' industry networks to entrepreneurial behavior and firm performance, while exploring the moderating roles of relational embeddedness and leadership self-efficacy. Empirical analysis using a sample of 420 STEs in China revealed several key findings: (1) STEs with a dominant position in the industry network tend to exhibit higher firm performance, and do so through timely and effective entrepreneurial behavior; (2) maintaining a dominant position in the industry network structure requires not only deep cooperation between business owners and other industry participants, but also the advisory role of family, relatives, and friends, who are close and responsible partners providing practical advice to small business owners that influence their entrepreneurial behavior; (3) however, the aforementioned benefits are conditional on entrepreneurs possessing a strong sense of leadership self-efficacy. Theoretical and practical implications are further discussed in this regard. Research article Modeling cross-cultural gender role in tourist self-presentation Journal of Hospitality and Tourism Management, Volume 56, 2023, pp. 367-375 Show abstract The study examines tourist self-presentational concern between home and destination environments with a sociopsychological lens by delineating the role of genders in cross-cultural settings. It is found that tourists of different genders and from different cultures exhibit varying degree of self-presentational concern at destination vs. home, conforming to the social role theory. Specifically, female tourists experience more significant relief of self-presentational concern than their male counterparts while away from home in a Western cultural context such as the United States. However, such difference between female and male tourists is not as significant in an Eastern society such as China. The findings result in the proposition of a model of cross-cultural gender role in tourist self-presentation. The study concludes with discussions of its theoretical and practical contributions as well as implications for future research. Research article Unraveling the uncertainty of geological interfaces through data-knowledge-driven trend surface analysis Computers & Geosciences, Volume 178, 2023, Article 105419 Show abstract Modeling complex geological interfaces is a common task in geosciences. Many data sources are available for geological interface modeling, including borehole data and geophysical surveys. Geological knowledge, such as the delineation from geologists, is difficult to quantify but likely adds value to geological interface modeling. To integrate all information, we present a data-knowledge-driven trend surface analysis method to construct stochastic geological interfaces. We design a Metropolis–Hastings sampling framework to sample stochastic trend interfaces and quantify the uncertainty of geological interfaces given all information sources. This method is suitable for both explicit and implicit representations of geological interfaces. We demonstrate our method in three different test cases: modeling stochastic interfaces of Greenland subglacial topography, magmatic intrusion, and buried river valleys in Australia. Research article Losing the meaning of being a socially responsible service worker: Moderating effects of customer and coworker incivility Journal of Hospitality and Tourism Management, Volume 56, 2023, pp. 420-430 Show abstract This study investigates how a service company's corporate social responsibility (CSR) activities enhance frontline service employees' (FSEs) job performance and the contextual variables that moderate the effect of CSR on FSEs. It is proposed that FSEs' perceptions of their company's level of CSR involvement, called CSR perceptions, positively affect their customer orientation and job performance. It is also suggested that two types of harmful workplace contexts—customer and coworker incivility—reduce the positive impact of CSR perceptions. Three-month time-lagged data collected from 435 frontline employees in 21 hotels in South Korea showed that FSEs' CSR perceptions lead to higher levels of customer orientation and supervisor-rated job performance. It was also shown that customer incivility moderates the positive effect of CSR perceptions but coworker incivility does not. These results suggest that negative experiences related to customers decrease the positive effect of CSR more than those related to the workplace atmosphere or culture. Further, the theoretical and practical implications of the results are discussed. Research article Will agricultural land scale management aggravate non-point source pollution? – Chaohu Lake Basin, China as a case study Applied Geography, Volume 158, 2023, Article 103056 Show abstract In this study, we investigated the relationship between scale management and non-point source (NPS) total phosphorus (TP) load in Chaohu Lake Basin. We used agricultural point of interest (POI) density to characterize scale management and employed the Soil and Water Assessment Tool (SWAT) model for simulations. Our findings in 2018 showed that there was a segmented relationship between agricultural POI density and TP load in townships. Significant negative correlations between POI density and TP load were observed in medium-density (0.076 < POI<0.172) and high-density (POI>0.172) scale management townships, but not in low-density (POI<0.076) townships. The slope coefficient for high-density townships was approximately one-tenth of that for medium-density townships, indicating the prevalence of agricultural land scale management at only a certain density can effectively inhibit NPS TP emissions, and this inhibitory effect has a marginal diminishing effect. These observations also applied to vegetable, fruit, and flower nurseries bases. Notably, low-density scale management of farms still had a significant inhibitory effect on TP load. However, scale management of forest farms and tea plantations did not inhibit TP loads due to the influence of high natural background phosphorus content, low hills with large gradients, and low POI density. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.cageo.2023.105434", "PubYear": 2023, "Volume": "179", "Issue": "", "JournalId": 4833, "JournalTitle": "Computers & Geosciences", "ISSN": "0098-3004", "EISSN": "1873-7803", "Authors": [{"AuthorId": 1, "Name": "Catalina Murúa", "Affiliation": "Speech and Processing Transmission Lab., Dept. of Electrical Engineering, Universidad de Chile, Av. <PERSON> 2007, Santiago, Chile"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Speech and Processing Transmission Lab., Dept. of Electrical Engineering, Universidad de Chile, Av. <PERSON> 2007, Santiago, Chile"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Speech and Processing Transmission Lab., Dept. of Electrical Engineering, Universidad de Chile, Av. <PERSON> 2007, Santiago, Chile"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Speech and Processing Transmission Lab., Dept. of Electrical Engineering, Universidad de Chile, Av. <PERSON> 2007, Santiago, Chile"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Speech and Processing Transmission Lab., Dept. of Electrical Engineering, Universidad de Chile, Av. <PERSON> 2007, Santiago, Chile"}, {"AuthorId": 6, "Name": "Néstor <PERSON>", "Affiliation": "Speech and Processing Transmission Lab., Dept. of Electrical Engineering, Universidad de Chile, Av. <PERSON> 2007, Santiago, Chile;Corresponding author"}], "References": [{"Title": "A dual‐stage attention‐based Conv‐LSTM network for spatio‐temporal correlation and multivariate time series prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "2036", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Temporal self-attention-based Conv-LSTM network for multivariate time series prediction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "501", "Issue": "", "Page": "162", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 109403043, "Title": "An overview of developments and challenges for unmanned surface vehicle autonomous berthing", "Abstract": "With the continuous progress of contemporary science and technology and the increasing requirements for marine vehicles in various fields, the intelligence and automation of ships have become a general trend. The autonomous control of surface Unmanned Surface Vessel (USV) generally covers the USV path planning, path tracking control, and autonomous collision avoidance control. But in the whole navigation process of USV, autonomous berthing is also a crucial part. And the research on the algorithm of the automatic berthing process of the USV is less. Mature USV autonomous berthing technology can effectively reduce the cost of human and material resources and financial resources while reducing the accident rate reasonably and safely. Therefore, it is of great importance to comprehensively promote the development of USV autonomous berthing technology.", "Keywords": "Autonomous berthing; Unmanned Surface Vessel; Berthing control algorithm; Path planning", "DOI": "10.1007/s40747-023-01196-z", "PubYear": 2024, "Volume": "10", "Issue": "1", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Ocean Science and Engineering, Shanghai Maritime University, Shanghai, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Ocean Science and Engineering, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Merchant Marine College, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 4, "Name": "Danda Shi", "Affiliation": "College of Ocean Science and Engineering, Shanghai Maritime University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Shanghai Ship and Shipping Research Institute Co., Ltd., Shanghai, China"}], "References": [{"Title": "Computational Methods of Acquisition and Processing of 3D Point Cloud Data for Construction Applications", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "27", "Issue": "2", "Page": "479", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "RETRACTED: Algorithm application based on the infrared image in unmanned ship target image recognition", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "", "Page": "103554", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "Cooperative Control of Autonomous Tugs for Ship Towing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "14470", "JournalTitle": "IFAC-PapersOnLine"}, {"Title": "Cooperative Control of Autonomous Tugs for Ship Towing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "2", "Page": "14470", "JournalTitle": "IFAC-PapersOnLine"}]}, {"ArticleId": 109403054, "Title": "Digital forensic analysis of the private mode of browsers on Android", "Abstract": "The smartphone has become an essential electronic device in our daily lives. We carry our most precious and important data on it, from family videos of the last few years to credit card information so that we can pay with our phones. In addition, in recent years, mobile devices have become the preferred device for surfing the web, already representing more than 50% of Internet traffic. As one of the devices we spend the most time with throughout the day, it is not surprising that we are increasingly demanding a higher level of privacy. One of the measures introduced to help us protect our data by isolating certain activities on the Internet is the private mode integrated in most modern browsers. Of course, this feature is not new, and has been available on desktop platforms for more than a decade. Reviewing the literature, one can find several studies that test the correct functioning of the private mode on the desktop. However, the number of studies conducted on mobile devices is incredibly small. And not only is it small, but also most of them perform the tests using various emulators or virtual machines running obsolete versions of Android. Therefore, in this paper we apply the methodology we presented in a previous work to Google Chrome, Brave, Mozilla Firefox, and Tor Browser running on a tablet with Android 13 and on two virtual devices created with Android Emulator. The results confirm that these browsers do not store information about the browsing performed in private mode in the file system. However, the analysis of the volatile memory made it possible to recover the username and password used to log in to a website or the keywords typed in a search engine, even after the devices had been rebooted.", "Keywords": "Android forensics ; Mobile device forensics ; Digital forensics ; Browsing artifacts ; Private browsing ; Internet privacy", "DOI": "10.1016/j.cose.2023.103425", "PubYear": 2023, "Volume": "134", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Departamento de Electrónica e Computación, Universidade de Santiago de Compostela, 15782 Santiago de Compostela, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "Tomás F. Pena", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Departamento de Electrónica e Computación, Universidade de Santiago de Compostela, 15782 Santiago de Compostela, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Centro Singular de Investigación en Tecnoloxías Intelixentes (CiTIUS), Departamento de Electrónica e Computación, Universidade de Santiago de Compostela, 15782 Santiago de Compostela, Spain"}], "References": [{"Title": "Digital forensic analysis methodology for private browsing: Firefox and Chrome on Linux as a case study", "Authors": "<PERSON><PERSON><PERSON>; Tomás F<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "115", "Issue": "", "Page": "102626", "JournalTitle": "Computers & Security"}]}, {"ArticleId": 109403060, "Title": "Message similarity as a proxy to repetitive thinking: Associations with non-suicidal self-injury and suicidal ideation on social media", "Abstract": "Detecting signs of rumination (specifically negative repetitive thinking) in social media data could be beneficial to predicting mental health, as rumination is associated with mental health problems. The objective of this study is to evaluate message similarity as a proxy measure of repetitive thinking (a core component of rumination), using data from Vent — a social media platform dedicated to emotion sharing. Our analyses suggest that automatic assessment of message similarity aligns with human judgements. Furthermore, message similarity is positively associated with signs of suicidal ideation/non-suicidal self-injury, and this association is stronger in users who post predominantly negative content, in line with observations from the literature. This effect has a relatively small magnitude and may be less visible in machine learning models with a large number of predictors. Our data suggests that message similarity may be a useful way of capturing repetitive content, which can potentially be used as a stepping stone to automatic rumination detection in social media data.", "Keywords": "Rumination ; Non-suicidal self-injury ; Suicidal ideation ; Social media", "DOI": "10.1016/j.chbr.2023.100320", "PubYear": 2023, "Volume": "11", "Issue": "", "JournalId": 75723, "JournalTitle": "Computers in Human Behavior Reports", "ISSN": "2451-9588", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Data61, CSIRO, Marsfield Site: Corner Vimiera & Pembroke Roads, Marsfield, 2122, NSW, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Data61, CSIRO, Sandy Bay Site: 15 College Road, Sandy Bay, 7005, TAS, Australia;Corresponding author. CSIRO, 15 College Road, Sandy Bay, TAS, 7005, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, Macquarie University, 16 University Ave, 2109, NSW, Australia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computing, Macquarie University, 18 Wally's Walk, 2109, NSW, Australia;Data61, CSIRO, Marsfield Site: Corner Vimiera & Pembroke Roads, Marsfield, 2122, NSW, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Data61, CSIRO, Marsfield Site: Corner Vimiera & Pembroke Roads, Marsfield, 2122, NSW, Australia;School of Computing, Macquarie University, 18 Wally's Walk, 2109, NSW, Australia"}], "References": [{"Title": "Machine learning for suicidal ideation identification: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "128", "Issue": "", "Page": "107095", "JournalTitle": "Computers in Human Behavior"}]}, {"ArticleId": *********, "Title": "Nanosecond pulsed laser surface processing of AISI 301LN steel: effect on surface topography and mechanical properties", "Abstract": "<p>Samples of the metastable austenitic stainless steel AISI 301LN were subjected to laser treatment with a nanosecond pulsed laser. Scanning speed and laser power were considered as the main process input variables, while the geometrical dimensions of laser tracks (depth and width) and surface roughness were the process responses. The effects of the input parameters on the responses were statistically investigated using analysis of variance (ANOVA). Microstructural characterization of the laser-treated zone was carried out via optical and focus ion beam microscopy, X-ray diffraction, and electron backscattered diffraction, mainly to discern the induced martensitic transformation. Also, tensile tests were performed in specimens with and without laser modification, in order to assess a possible effect on the mechanical response of the steel. The results show that, by increasing the laser power and decreasing the scanning speed, the geometrical dimensions of the laser tracks augment, the surface becomes rougher, and the higher heat input induces more martensitic transformation, whereas tensile properties are not significantly affected.</p>", "Keywords": "Laser surface modification; Metastable steel; Martensitic transformation", "DOI": "10.1007/s00170-023-12120-7", "PubYear": 2023, "Volume": "128", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Center for Structural Integrity, Micromechanics, and Reliability of Materials (CIEFMA) Department of Materials Science and Engineering, Universitat Politècnica de Catalunya-BarcelonaTECH, Barcelona, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Arts, Science and Technology, University of Northampton, Northampton, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Center for Structural Integrity, Micromechanics, and Reliability of Materials (CIEFMA) Department of Materials Science and Engineering, Universitat Politècnica de Catalunya-BarcelonaTECH, Barcelona, Spain; Barcelona Research Center in Multiscale Science and Engineering, Politècnica de Catalunya-BarcelonaTECH, Barcelona, Spain"}], "References": [{"Title": "Laser surface texturing and characterization of austenitic stainless steel for the improvement of its surface properties", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "115", "Issue": "5-6", "Page": "1795", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Micro-texturing of polymer surfaces using lasers: a review", "Authors": "Amar<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "1-2", "Page": "103", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 109403126, "Title": "Code and Data Repository for Distributionally Robust Chance-Constrained p-Hub Center Problem", "Abstract": "This archive is distributed in association with the INFORMS Journal on Computing under the MIT License. This repository contains supporting material for the paper Distributionally Robust Chance Constrained p-Hub Center Problem by <PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.", "Keywords": "", "DOI": "10.1287/ijoc.2022.0113.cd", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109403206, "Title": "Code and Data Repository for PaPILO: A Parallel Presolving Library for Integer and Linear Optimization with Multiprecision Support", "Abstract": "PaPILO, a C++14-based software package, provides parallel presolve routines for (mixed integer) linear programming problems. The routines are implemented using templates which allows switching to higher precision or rational arithmetic using the boost multiprecision package. Additionally to the distribution here under the LGPLv3, PaPILO is also distributed as part of the SCIP Optimization Suite which is available under https://scipopt.org/. PaPILO can be used as a header-based library and also provides an executable. Using the executable it is possible to presolve and postsolve MILP instances based on files. Additionally, PaPILO can be linked to SCIP, SoPlex, and HiGHS (https://github.com/ERGO-Code/HiGHS) solvers and act as a frontend. In this setting PaPIL<PERSON> passes the presolved problem to those solvers and applies the postsolve step to the optimal solution. When PaPIL<PERSON> is compiled as part of the SCIP Optimization Suite linking of SoPlex and SCIP solvers is performed automatically. Note: The original instance of this repository is hosted at git.zib.de and a read-only mirror is available at github.com/scipopt/papilo.", "Keywords": "", "DOI": "10.1287/ijoc.2022.0171.cd", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109403233, "Title": "Study on Optical Positioning Using Experimental Visible Light Communication System", "Abstract": "<p>Visible light positioning systems (VLP) have attracted significant commercial and research interest because of the many advantages they possess over other applications such as radio frequency (RF) positioning systems. In this work, an experimental configuration of an indoor VLP system based on the well-known Lambertian light emission, is investigated. The corresponding results are also presented, and show that the system retains high enough accuracy to be operational, even in cases of low transmitted power and high background noise.</p>", "Keywords": "", "DOI": "10.3390/computation11080161", "PubYear": 2023, "Volume": "11", "Issue": "8", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Section of Electronic Physics and Systems, Department of Physics, National and Kapodistrian University of Athens, 15784 Athens, Greece"}, {"AuthorId": 2, "Name": "Argyris <PERSON>", "Affiliation": "Department of Computer Science and Biomedical Informatics, University of Thessaly, 35131 Lamia, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Section of Electronic Physics and Systems, Department of Physics, National and Kapodistrian University of Athens, 15784 Athens, Greece"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Section of Electronic Physics and Systems, Department of Physics, National and Kapodistrian University of Athens, 15784 Athens, Greece"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Section of Electronic Physics and Systems, Department of Physics, National and Kapodistrian University of Athens, 15784 Athens, Greece"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, Ajman University, Ajman P.O. Box 346, United Arab Emirates"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Section of Electronic Physics and Systems, Department of Physics, National and Kapodistrian University of Athens, 15784 Athens, Greece; Corresponding author"}], "References": []}, {"ArticleId": 109403348, "Title": "FSS2022開催報告", "Abstract": "", "Keywords": "", "DOI": "10.3156/jsoft.35.1_3", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 16268, "JournalTitle": "Journal of Japan Society for Fuzzy Theory and Intelligent Informatics", "ISSN": "1347-7986", "EISSN": "1881-7203", "Authors": [{"AuthorId": 1, "Name": "祐樹 柴田", "Affiliation": "Tokyo Metropolitan University"}], "References": []}, {"ArticleId": 109403471, "Title": "Photoelectric factor prediction using automated learning and uncertainty quantification", "Abstract": "<p>The photoelectric factor (PEF) is an important well-logging tool to distinguish between different types of reservoir rocks because PEF measurement is sensitive to elements with high atomic numbers. Furthermore, the ratio of rock minerals could be determined by combining PEF log with other well logs. However, PEF logs could be missing in some cases such as in old well logs and wells drilled with barite-based mud. Therefore, developing models for estimating missing PEF logs is essential in those circumstances. In this work, we developed various machine learning models to predict PEF values using the following well logs as inputs: bulk density (RHOB), neutron porosity (NPHI), gamma ray (GR), compressional and shear velocity. The predictions of PEF values using adaptive-network-fuzzy inference system (ANFIS) and artificial neural network (ANN) models have errors of about 16% and 14% average absolute percentage error (AAPE) in the testing dataset, respectively. Thus, a different approach was proposed that is based on the concept of automated machine learning. It works by automatically searching for the optimal model type and optimizes its hyperparameters for the dataset under investigation. This approach selected a Gaussian process regression (GPR) model for the accurate estimation of PEF values. The developed GPR model decreases the AAPE of the predicted PEF values in the testing dataset to about 10% AAPE. This error could be further decreased to about 2% by modeling the potential noise in the measurements using the GPR model.</p>", "Keywords": "Machine learning algorithms; Well logging; Photoelectric factor; Automated learning; Uncertainty quantification; Gaussian process regression; Fuzzy logic; Artificial neural network (ANN)", "DOI": "10.1007/s00521-023-08911-4", "PubYear": 2023, "Volume": "35", "Issue": "30", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Petroleum Engineering, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Petroleum Engineering, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia; Center for Integrative Petroleum Research, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Petroleum Engineering, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia; Center for Integrative Petroleum Research, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Petroleum Engineering, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia; Center for Integrative Petroleum Research, King Fahd University of Petroleum & Minerals, Dhahran, Saudi Arabia"}], "References": [{"Title": "Unconfined compressive strength (UCS) prediction in real-time while drilling using artificial intelligence tools", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "13", "Page": "8043", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Gaussian process machine learning and Kriging for groundwater salinity interpolation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "105170", "JournalTitle": "Environmental Modelling & Software"}, {"Title": "Traditional kriging versus modern Gaussian processes for large‐scale mining data", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "16", "Issue": "5", "Page": "488", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}]}, {"ArticleId": 109403508, "Title": "The quasi-frame of the rational and polynomial <PERSON><PERSON> curve by algorithm method in Euclidean space", "Abstract": "<h3>Purpose</h3> <p>Frames play an important role in determining the geometric properties of the curves such as curvature and torsion. In particular, the determination of the point types of the curve, convexity or concavity is also possible with the frames. The Serret-Frenet frames are generally used in curve theory. However, the Serret-Frenet frame does not work when the second derivative is zero. In order to eliminate this problem, the quasi-frame was obtained. In this study, the quasi frames of the polynomial and rational Bezier curves are calculated by an algorithmic method. Thus, it will be possible to construct the frame even at singular points due to the second derivative of the curve. In this respect, the contribution of this study to computer-aided geometric design studies is quite high.</p> <h3>Design/methodology/approach</h3> <p>In this study, the quasi frame which is an alternative for all intermediate points of the rational Bezier curves was generated by the algorithm method, and some variants of this frame were analyzed. Even at the points where the second derivative of such rational Bezier curves is zero, there is a curve frame.</p> <h3>Findings</h3> <p>Several examples presented at the end of the paper regarding the quasi-frame of the rational Bezier curve, polynomial Bezier curve, linear, quadratic and cubic Bezier curves emphasize the efficacy and preciseness.</p> <h3>Originality/value</h3> <p>The quasi-frame of a rational Bezier curve is first computed. Owing to the quasi frame, it will have been found a solution for the nonsense rotation of the curve around the tangent.</p>", "Keywords": "Rational bezier curve;Algorithm method;Quasi frame;Serret-Fr<PERSON>t frame", "DOI": "10.1108/EC-04-2022-0193", "PubYear": 2023, "Volume": "40", "Issue": "7/8", "JournalId": 30507, "JournalTitle": "Engineering Computations", "ISSN": "0264-4401", "EISSN": "1758-7077", "Authors": [{"AuthorId": 1, "Name": "Hatice Kuşak Samancı", "Affiliation": "Department of Mathematics, Faculty of Art and Science , Bitlis Eren University , Bitlis, Turkey"}], "References": [{"Title": "A low order, torsion deformable spatial beam element based on the absolute nodal coordinate formulation and Bishop frame", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "247", "JournalTitle": "Multibody System Dynamics"}]}, {"ArticleId": 109403511, "Title": "Shrinkage Estimation for Location and Scale Parameters of Logistic Distribution Under Record Values", "Abstract": "", "Keywords": "", "DOI": "10.1007/s40745-023-00492-2", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 6800, "JournalTitle": "Annals of Data Science", "ISSN": "2198-5804", "EISSN": "2198-5812", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109403555, "Title": "Design of acceptance sampling plans based on interval valued neutrosophic sets", "Abstract": "<p>Acceptance sampling plans (ASPs) are conducted by inspecting a small set of items instead of all outputs. Although traditional ASPs use certain plan parameters, it is clear that quality characteristics or definitions may not be certain in some real case applications because of uncertainties. The fuzzy set theory (FST) is a popular technique to model uncertainty in the engineering problems. It is known that ASPs have been successfully formulated based on FST in the literature. However, the uncertainty is generally more complex in cases including human evaluations. Neutrosophic sets (NSs) that is one of the fuzzy set extensions bring some advantages to manage more complicated uncertainties in quality problems especially uncertainty based on human’s hesitancy. Since the NSs include three terms as truthiness (t), indeterminacy (i), and falsity (f), they can successfully model the human thinking and inspectors’ evaluations under uncertainty. In this paper, traditional attribute ASPs have been extended based on interval NSs to combine the computational and interpretational advantages of the interval statistics with the advantages of NSs. Additionally, two well-known distributions for ASPs called Binomial and Poisson distributions are redesigned by using NSs. For this aim, NSs are converted to interval NSs by using α-cut technique and some characteristic functions of ASPs such as acceptance probability ( ({P}_{a}) ), average sample number ( (ASN) ), and average total inspection ( (ATI) ) have been designed for single and double ASPs based on interval NSs. The proposed ASPs based on NSs have been tested on some numerical applications from a manufacturing process, and results obtained based on real cases have been compared.</p>", "Keywords": "Acceptance sampling plans; Binomial distribution; Fuzzy sets; Interval neutrosophic sets; Neutrosophic sets; Poisson distribution", "DOI": "10.1007/s00500-023-09027-6", "PubYear": 2023, "Volume": "27", "Issue": "20", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "Gürkan Işık", "Affiliation": "Department of Industrial Engineering, Bursa Technical University, Bursa, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial Engineering, Yıldız Technical University, Istanbul, Turkey"}], "References": [{"Title": "Possibility mean, variance and standard deviation of single-valued neutrosophic numbers and its applications to multi-attribute decision-making problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "24", "Page": "18795", "JournalTitle": "Soft Computing"}, {"Title": "Inspection of the Production Lot Using Two Successive Occasions Sampling Under Neutro<PERSON>phy", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computational Intelligence Systems"}]}, {"ArticleId": 109403566, "Title": "A NOVEL METHOD FOR WEATHER NOWCASTING BASED ON SPATIAL COMPLEX FUZZY INFERENCE WITH MULTIPLE BAND INPUT DATA", "Abstract": "<p>The prediction of weather changes, such as rainfall, clouds, floods, and storms, is critical in weather forecasting. There are several sources of input data for this purpose, including radar and observational data, but satellite remote sensing images are the most commonly used due to their ease of collection. In this paper, we present a novel method for weather nowcasting based on Mamdani complex fuzzy inference with multiple band input data. The proposed approach splits the process into two parts: the first part converts the multiple band satellite images into real and imaginary parts to facilitate the rule process, and the second part uses the Spatial CFIS+ algorithm to generate the predicted weather state, taking into account factors such as cloud, wind, and temperature. The use of MapReduce helps to speed up the algorithm's performance. Our experimental results show that this new method outperforms other relevant methods and demonstrates improved prediction accuracy.</p>", "Keywords": "Weather forecast;Complex fuzzy inference system;remote sensing images;multiple band satellite images", "DOI": "10.15625/1813-9663/18028", "PubYear": 2023, "Volume": "39", "Issue": "1", "JournalId": 41141, "JournalTitle": "Journal of Computer Science and Cybernetics", "ISSN": "1813-9663", "EISSN": "1813-9663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology and Digital Economics, National Economics University, Ha Noi, Viet Nam"}, {"AuthorId": 2, "Name": " Le Truong Giang", "Affiliation": "Quality Assurance Center, Hanoi University of Industry (HaUI), Ha Noi, Viet Nam"}, {"AuthorId": 3, "Name": " <PERSON><PERSON>", "Affiliation": "Information Technology Institute, Vietnam National University, Ha Noi, Viet Nam"}, {"AuthorId": 4, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": "Quality Assurance Center, Hanoi University of Industry (HaUI), Ha Noi, Viet Nam"}, {"AuthorId": 5, "Name": " <PERSON>", "Affiliation": "National Academy of Public Administration, Ha Noi, Viet Nam"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Post and Telecommunications Institute of Technology (PTIT), Ha Noi, Viet Nam"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Testing and Assessment Centre, Hanoi University of Industry (HaUI), Ha Noi, Viet Nam"}], "References": [{"Title": "A TIME SERIES FORECASTING M<PERSON><PERSON> BASED ON LINGUISTIC FORECASTING RULES", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "1", "Page": "23", "JournalTitle": "Journal of Computer Science and Cybernetics"}, {"Title": "Compression and regularized optimization of modules stacked residual deep fuzzy system with application to time series prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "551", "JournalTitle": "Information Sciences"}, {"Title": "A new co-learning method in spatial complex fuzzy inference systems for change detection from satellite images", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "6", "Page": "4519", "JournalTitle": "Neural Computing and Applications"}, {"Title": "A HYBRID PSO-SA SCHEME FOR IMPROVING ACCURACY OF FUZZY TIME SERIES FORECASTING MODELS", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "3", "Page": "257", "JournalTitle": "Journal of Computer Science and Cybernetics"}]}, {"ArticleId": 109403643, "Title": "The role of contextual and contentual signals for online trust: Evidence from a crowd work experiment", "Abstract": "<p>Platform workers can typically not take their ratings from one platform to another. This creates lock-in as building up reputation anew can come at prohibitively high cost. A system of portable reputation may mitigate this problem but poses several new challenges and questions. This study reports the results of an online experiment among 180 actual clients of five gig economy platforms to disentangle the importance of two dimensions of worker reputation: (1) contextual fit (i.e., the ratings’ origin from the same or another platform) and (2) contentual fit (i.e., the ratings’ origin from the same or a different job type). By and large, previous work has demonstrated the potential of imported ratings for trust-building but usually confounded these two dimensions. Our results provide a more nuanced picture and suggest that there exist two important boundary conditions for reputation portability: While imported ratings can have an effect on trust, they may only do so for matching job types and in the absence of within-platform ratings.</p>", "Keywords": "Gig economy; Crowd work; Platform economy; Reputation portability; Trust; Signaling theory; Lock-in effects; D81; D820", "DOI": "10.1007/s12525-023-00655-2", "PubYear": 2023, "Volume": "33", "Issue": "1", "JournalId": 16578, "JournalTitle": "Electronic Markets", "ISSN": "1019-6781", "EISSN": "1422-8890", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Sociology, Utrecht University, Utrecht, the Netherlands"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Utrecht, Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Trust in Digital Services, Einstein Center Digital Future, TU Berlin, Berlin, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Utrecht, Netherlands"}], "References": [{"Title": "Reputation portability – quo vadis?", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "2", "Page": "331", "JournalTitle": "Electronic Markets"}, {"Title": "Unlocking Online Reputation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "62", "Issue": "6", "Page": "501", "JournalTitle": "Business & Information Systems Engineering"}, {"Title": "Conceptualizing the Gig Economy and Its Regulatory Problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "525", "JournalTitle": "Policy & Internet"}, {"Title": "Dynamic, Multidimensional, and Skillset-Specific Reputation Systems for Online Work", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "3", "Page": "688", "JournalTitle": "Information Systems Research"}, {"Title": "In Stars We Trust – A Note on Reputation Portability Between Digital Platforms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "64", "Issue": "3", "Page": "349", "JournalTitle": "Business & Information Systems Engineering"}]}, {"ArticleId": 109403666, "Title": "Corrigendum to “Adaptive stick–slip friction and backlash compensation using dynamic fuzzy logic system” [Appl. Soft Comput. 6 (2005) 26–37]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110685", "PubYear": 2023, "Volume": "146", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>ni S.", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology, Hauz Khas, New Delhi 110016, India"}, {"AuthorId": 2, "Name": "Kar I.N.", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology, Hauz Khas, New Delhi 110016, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> O<PERSON>.", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology, Hauz Khas, New Delhi 110016, India;Corresponding author"}, {"AuthorId": 4, "Name": "Bhatt R.K.P.", "Affiliation": "Department of Electrical Engineering, Indian Institute of Technology, Hauz Khas, New Delhi 110016, India"}], "References": []}, {"ArticleId": 109403698, "Title": "Self-training and Multi-level Adversarial Network for Domain Adaptive Remote Sensing Image Segmentation", "Abstract": "<p>Unsupervised domain adaptive (UDA) image segmentation has received more and more attention in recent years. Domain adaptive methods can align the features of data in different domains, so that segmentation models can be migrated to data in other domains without incurring additional labeling costs. The traditional adversarial training uses the global alignment strategy to align the feature space, which may cause some categories to be incorrectly mapped. At the same time, in high-spatial resolution remote sensing images (RSI), the same category from different scenes (such as urban and rural areas) may have completely different distributions, which severely limits the accuracy of UDA. In order to solve these problems, in this paper: (1) a multi-level adversarial network at category-level is proposed, aiming at integrating feature information in different dimensions, studying the joint distribution at category-level, and aligning each category with adaptive adversarial loss in different dimensional spaces. (2) Use covariance regularization to optimize self-training. A method combining self-training with adversarial training is proposed, optimizes the domain adaptation effect, reduces the negative impact of false pseudo-label iteration caused by self-training. We demonstrated the latest performance of semantic segmentation on challenging LoveDA datasets. Experiments on “urban-to-rural” and “rural-to-urban” show that our method has better performance than the most advanced methods.</p>", "Keywords": "Domain adaptation; Semantic segmentation; Remote sensing images; Self-training; Adversarial network", "DOI": "10.1007/s11063-023-11341-x", "PubYear": 2023, "Volume": "55", "Issue": "8", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, China Jiliang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, China Jiliang University, Hangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Engineering, China Jiliang University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, China Jiliang University, Hangzhou, China"}], "References": [{"Title": "Manipulator grabbing position detection with information fusion of color image and depth image using deep learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "12", "Page": "10809", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Detection algorithm of safety helmet wearing based on deep learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "13", "Page": "e6234", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Semantic segmentation for multiscale target based on object recognition using the improved Faster-RCNN model", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "123", "Issue": "", "Page": "94", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Multi-level adversarial network for domain adaptive semantic segmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "123", "Issue": "", "Page": "108384", "JournalTitle": "Pattern Recognition"}, {"Title": "Deep learning based 3D target detection for indoor scenes", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "9", "Page": "10218", "JournalTitle": "Applied Intelligence"}, {"Title": "A hybrid domain learning framework for unsupervised semantic segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "516", "Issue": "", "Page": "133", "JournalTitle": "Neurocomputing"}, {"Title": "Shape robustness in style enhanced cross domain semantic segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "135", "Issue": "", "Page": "109143", "JournalTitle": "Pattern Recognition"}, {"Title": "MLCB-Net: a multi-level class balancing network for domain adaptive semantic segmentation", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "3", "Page": "1405", "JournalTitle": "Multimedia Systems"}, {"Title": "Combining Pixel-Level and Structure-Level Adaptation for Semantic Segmentation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "55", "Issue": "7", "Page": "9669", "JournalTitle": "Neural Processing Letters"}]}, {"ArticleId": 109403773, "Title": "A stepwise audit framework for keyword selection and estimating visibility for low performing websites: a study of cmsdu.org", "Abstract": "Search engines like Google scans millions of websites for processing a given query. The results presented to any user are the outcome of a very complex backend mechanism which includes factors like the keyword and historical performance of the website. Therefore, a website with better historical performance can easily outrun a ‘low-performance’ website while competing for a similar keyword. A website with low rating across audit tools has been considered as ‘low-performance’ website in this study. Therefore, this study suggests an audit framework; namely the relative competitive indexing framework for keyword selection through systematic website auditing and statistical tests like structural equation modelling. The proposed framework suggests factors like ‘keyword frequency’ and ‘SEO difficulty per keyword’ as crucial and which can impact search visibility significantly. It also highlights other search variables which are identified as crucial for website search visibility. The study illustrates the procedure for selecting keywords based on a website’s own ability to compete for those keywords and also highlights the minimum required frequency of keywords based on the intensity of competition. This study specifically fills the gap between website competitive strength, keyword competition and required number of frequencies within the metadata of a website. © 2023 Inderscience Enterprises Ltd.", "Keywords": "core web vitals; keyword classification; keyword research; search engine marketing; search engine optimisation; SEO; SERP; website audit; website link network", "DOI": "10.1504/IJWET.2023.132870", "PubYear": 2023, "Volume": "18", "Issue": "2", "JournalId": 26853, "JournalTitle": "International Journal of Web Engineering and Technology", "ISSN": "1476-1289", "EISSN": "1741-9212", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Management Studies, Dibrugarh University, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Management Studies, Dibrugarh University, India"}], "References": []}, {"ArticleId": 109403781, "Title": "Semi-Automatic Generation of Assembly Instructions for Open Source Hardware", "Abstract": "", "Keywords": "", "DOI": "10.5334/joh.56", "PubYear": 2023, "Volume": "7", "Issue": "1", "JournalId": 44318, "JournalTitle": "Journal of Open Hardware", "ISSN": "", "EISSN": "2514-1708", "Authors": [{"AuthorId": 1, "Name": "J. <PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109403852, "Title": "Code and Data Repository for Learning Symbolic Expressions: Mixed-Integer Formulations, Cuts, and Heuristics", "Abstract": "This archive is distributed in association with the INFORMS Journal on Computing under the GNU GPLv3. The software and data in this repository are a snapshot of the software and data that were used in the research reported on in the paper Learning Symbolic Expressions: Mixed-Integer Formulations, Cuts, and Heuristics by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>. The snapshot is based on this SHA in the development repository.", "Keywords": "", "DOI": "10.1287/ijoc.2022.0050.cd", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109403880, "Title": "A multiphysics dynamic model for shape memory alloy actuators", "Abstract": "Shape Memory Alloys (SMAs) are increasingly being used in actuator technologies owing to their high reliability, advantageous specific power and fast actuation capabilities. This results from the extremely simple architecture and actuation mechanisms, which are linked to the thermally-induced shape recovery properties of SMAs. Owing to the elevated power-to-weight ratio, small diameter wires can be directly used as linear actuators, whose activation can be achieved by electric currents, exploiting the Joule effect. Fast actuation represents a design requirement in several engineering fields, ranging from aerospace to automotive applications, and it can be easily obtained by a high electric current pulse owing to the extremely small thermal inertia of SMA wires. However, dynamic effects caused by fast actuations, in terms of overloads and stroke oscillations, could represent a threat to material integrity at both structural and functional levels. For these reasons the thermal-mechanical response of the SMA wires under both static and dynamic conditions must be taken into account in designing SMA-based actuators, especially when dealing with fast actuation applications. Unfortunately, SMAs exhibit a very complex constitutive response, with intricated electro-thermal-mechanical coupling mechanisms, whose modeling represents one of the main challenges within the technical and scientific communities. Within this context, an effective multiphysics simulation model was developed combing basic underlying equations for electric, thermal, and mechanical problems. The model was implemented in MatLab/SimuLink environment and solved numerically. Experiments were also carried out, by using an ad-hoc developed testing rig, to capture the dynamic response of SMA wires during fast actuation experiments. The accuracy of the model was validated by systematic comparisons with experimental results, that is under different applied mechanical stresses and electric actuation conditions.", "Keywords": "Shape memory alloys ; Actuators ; NiTi ; MatLab ; Dynamic response ; Experimental", "DOI": "10.1016/j.sna.2023.114602", "PubYear": 2023, "Volume": "362", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Energy and Management Engineering, University of Calabria, Rende, CS, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Energy and Management Engineering, University of Calabria, Rende, CS, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Energy and Management Engineering, University of Calabria, Rende, CS, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Energy and Management Engineering, University of Calabria, Rende, CS, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Energy and Management Engineering, University of Calabria, Rende, CS, Italy"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Energy and Management Engineering, University of Calabria, Rende, CS, Italy;Corresponding author"}], "References": [{"Title": "Use the Force: Review of High-Rate Actuation of Shape Memory Alloys", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "7", "Page": "140", "JournalTitle": "Actuators"}, {"Title": "A lightweight variable stiffness knee exoskeleton driven by shape memory alloy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "49", "Issue": "5", "Page": "994", "JournalTitle": "Industrial Robot: An International Journal"}, {"Title": "High-Rate, High-Precision Wing Twist Actuation for Drone, Missile, and Munition Flight Control", "Authors": "<PERSON>-<PERSON>; <PERSON>", "PubYear": 2022, "Volume": "11", "Issue": "8", "Page": "239", "JournalTitle": "Actuators"}]}, {"ArticleId": 109403919, "Title": "Forward- or reverse-mode automatic differentiation: What's the difference?", "Abstract": "Automatic differentiation (AD) has been a topic of interest for researchers in many disciplines, with increased popularity since its application to machine learning and neural networks. Although many researchers appreciate and know how to apply AD, it remains a challenge to truly understand the underlying processes. From an algebraic point of view, however, AD appears surprisingly natural: it originates from the differentiation laws. In this work we use Algebra of Programming techniques to reason about different AD variants, leveraging <PERSON><PERSON> to illustrate our observations. Our findings stem from three fundamental algebraic abstractions : (1) the notion of semimodule, (2) Nagata&#x27;s construction of the ‘idealization of a module’, and (3) Kronecker&#x27;s delta function, that together allow us to write a single-line abstract definition of AD. From this single-line definition, and by instantiating our algebraic structures in various ways, we derive different AD variants, that have the same extensional behaviour, but different intensional properties, mainly in terms of (asymptotic) computational complexity. We show the different variants equivalent by means of <PERSON><PERSON><PERSON> isomorphisms , a further elaboration of our Haskell infrastructure which guarantees correctness by construction. With this framework in place, this paper seeks to make AD variants more comprehensible, taking an algebraic perspective on the matter.", "Keywords": "Automatic differentiation ; <PERSON><PERSON>ley representation ; Semimodules ; Nagata idealization ; <PERSON><PERSON><PERSON>", "DOI": "10.1016/j.scico.2023.103010", "PubYear": 2024, "Volume": "231", "Issue": "", "JournalId": 12043, "JournalTitle": "Science of Computer Programming", "ISSN": "0167-6423", "EISSN": "1872-7964", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "KU Leuven, Celesti<PERSON>enla<PERSON> 200A, Leuven, Belgium;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "KU Leuven, Celestijnenlaan 200A, Leuven, Belgium"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Heriot-Watt University, Earl <PERSON> Building G.52, Edinburgh EH14 4AS, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Standard Chartered, 1 Basinghall Avenue, London, EC2V 5DD, United Kingdom"}], "References": [{"Title": "Symbolic and automatic differentiation of languages", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "ICFP", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Provably correct, asymptotically efficient, higher-order reverse-mode automatic differentiation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "POPL", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}, {"Title": "Functional collection programming with semi-ring dictionaries", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "6", "Issue": "OOPSLA1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Programming Languages"}]}, {"ArticleId": 109403939, "Title": "pyMune: A Python package for complex clusters detection", "Abstract": "We introduce pyMune, an open-source Python library for robust clustering of complex real-world datasets without density cutoff parameters. It implements DenMune (<PERSON> et al., 2021), a mutual nearest neighbor algorithm that uses dimensionality reduction and approximate nearest neighbor search to identify and expand cluster cores. Noise is removed with a mutual nearest-neighbor voting system. In addition to clustering, pyMune provides classification, visualization, and validation functionalities. It is fully compatible with scikit-learn and has been accepted into the scikit-learn-contrib repository. The code, documentation, and demos are available on GitHub, PyPi, and CodeOcean for easy use and reproducibility.", "Keywords": "Machine learning ; Pattern recognition ; Dimensionality reduction ; Mutual nearest neighbors ; Nearest neighbors approximation ; DenMune", "DOI": "10.1016/j.simpa.2023.100564", "PubYear": 2023, "Volume": "17", "Issue": "", "JournalId": 66395, "JournalTitle": "Software Impacts", "ISSN": "2665-9638", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Information Technology Department, Institute of Graduate Studies and Research, Alexandria University, Alexandria, Egypt;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Technology Department, Institute of Graduate Studies and Research, Alexandria University, Alexandria, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer and Systems Engineering Department, Faculty of Engineering, Alexandria University, Alexandria, Egypt"}], "References": [{"Title": "DenMune: Density peak based clustering using mutual nearest neighbors", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "109", "Issue": "", "Page": "107589", "JournalTitle": "Pattern Recognition"}, {"Title": "DCSNE: Density-based Clustering using Graph Shared Neighbors and Entropy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "137", "Issue": "", "Page": "109341", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 109403946, "Title": "RETRACTED ARTICLE: A hybrid non-local enhanced network for lightweight single image super-resolution", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00170-023-11913-0", "PubYear": 2024, "Volume": "133", "Issue": "1-2", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China; School of Aeronautics and Astronautics, Sichuan University, Chengdu, China; Department of Infectious Diseases and Public Health, City University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China; School of Aeronautics and Astronautics, Sichuan University, Chengdu, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China; School of Aeronautics and Astronautics, Sichuan University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Electronic Engineering, Xidian University, Xi’an, China; Department of Embedded Systems Engineering, Incheon National University, Incheon, Korea"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Sichuan University, Chengdu, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu, China; School of Aeronautics and Astronautics, Sichuan University, Chengdu, China; Corresponding author."}], "References": [{"Title": "Lightweight adaptive weighted network for single image super-resolution", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "211", "Issue": "", "Page": "103254", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 109403952, "Title": "Benchmarking surrogate-based optimisation algorithms on expensive black-box functions", "Abstract": "Surrogate algorithms such as Bayesian optimisation are especially designed for black-box optimisation problems with expensive objectives, such as hyperparameter tuning or simulation-based optimisation. In the literature, these algorithms are usually evaluated with synthetic benchmarks which are well established but have no expensive objective, and only on one or two real-life applications which vary wildly between papers. There is a clear lack of standardisation when it comes to benchmarking surrogate algorithms on real-life, expensive, black-box objective functions. This makes it very difficult to draw conclusions on the effect of algorithmic contributions and to give substantial advice on which method to use when. A new benchmark library, EXPObench, provides first steps towards such a standardisation. The library is used to provide an extensive comparison of six different surrogate algorithms on four expensive optimisation problems from different real-life applications. This has led to new insights regarding the relative importance of exploration, the evaluation time of the objective, and the used model. We also provide rules of thumb for which surrogate algorithm to use in which situation. A further contribution is that we make the algorithms and benchmark problem instances publicly available, contributing to more uniform analysis of surrogate algorithms. Most importantly, we include the results of the six algorithms on all evaluated problem instances. This unique new dataset lowers the bar for researching new methods as the number of expensive evaluations required for comparison and for the creation of new surrogate models is significantly reduced.", "Keywords": "Expensive optimisation ; Surrogate-based optimisation ; Bayesian optimisation ; Benchmarking", "DOI": "10.1016/j.asoc.2023.110744", "PubYear": 2023, "Volume": "147", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Industrial Engineering, Eindhoven University of Technology, PO Box 513, 5600 MB, Eindhoven, The Netherlands;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, Delft University of Technology, PO Box 5, 2600 AA, Delft, The Netherlands"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, Delft University of Technology, PO Box 5, 2600 AA, Delft, The Netherlands"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, Delft University of Technology, PO Box 5, 2600 AA, Delft, The Netherlands"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Electrical Engineering, Mathematics and Computer Science, Delft University of Technology, PO Box 5, 2600 AA, Delft, The Netherlands"}], "References": [{"Title": "High-dimensional Bayesian optimization using low-dimensional feature spaces", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "109", "Issue": "9-10", "Page": "1925", "JournalTitle": "Machine Learning"}]}, {"ArticleId": 109403957, "Title": "Archimedean t -norm and t -conorm based intuitionistic fuzzy WASPAS method to evaluate health-care waste disposal alternatives with unknown weight information", "Abstract": "Health-care waste (HCW) contains numerous hazardous, infectious, and radioactive contents that may cause serious health issues for society and drastic environmental problems. Therefore, it is crucial to dispose of HCW safely and appropriately. Four commonly used HCW disposal methods are incineration , steam sterilization , microwave, and landfill disposal. To select an appropriate HCW disposal method, there are several conflicting criteria. Furthermore, when information is lacking due to uncertainty and criteria weighing conditions are also unknown, the decision to choose an appropriate HCW disposal method becomes more challenging. So, the objective of this paper is to introduce a novel integrated multi-criteria group decision-making (MCGDM) approach to evaluate HCW disposal methods in which incomplete information is quantified using intuitionistic fuzzy sets (IFSs), unknown criteria weights are evaluated using the maximizing deviation method, and the evaluation of HCW disposal methods is done by applying the Archimedean t -norm and t -conorm-based interactive intuitionistic fuzzy weighted aggregated sum product assessment (WASPAS) method. Herein, the Archimedean t -norm and t -conorm cover a wide range of t -norms and t -conorms, including the Algebraic, <PERSON>, <PERSON>, and <PERSON>, etc., by adopting prescribed combinations of additive generators. The proposed MCGDM approach has been applied to evaluate the above-listed four HCW disposal methods for a case study conducted by <PERSON><PERSON><PERSON> et al. (2020) in the context of India. A sensitivity analysis is performed to examine the impact of changes in the parameters involved in the proposed MCGDM approach. A comparative study is also performed with some well-known multi-criteria decision making(MCDM) and MCGDM methods to demonstrate the consistency and stability of the proposed approach. Findings suggest that the newly developed MCGDM approach is more general, flexible, and provides realistic results to evaluate HCW disposal methods under uncertainty and unknown criteria weight information.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110751", "PubYear": 2023, "Volume": "146", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON>", "Affiliation": "Department of Mathematics, School of Physical Sciences, Doon University, Dehradun - 248001, Uttarakhand, India"}], "References": [{"Title": "Healthcare evaluation in hazardous waste recycling using novel interval-valued intuitionistic fuzzy information based on complex proportional assessment method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "106140", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A rough based multi-criteria evaluation method for healthcare waste disposal location decisions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106394", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Solving green supplier selection problem using q-rung orthopair fuzzy-based decision framework with unknown weight information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "94", "Issue": "", "Page": "106431", "JournalTitle": "Applied Soft Computing"}, {"Title": "Algorithm for Multiple Attribute Decision-Making with Interactive Archimedean Norm Operations Under Pythagorean Fuzzy Uncertainty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1", "Page": "503", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "MARCOS technique under intuitionistic fuzzy environment for determining the COVID-19 pandemic performance of insurance companies in terms of healthcare services", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "107199", "JournalTitle": "Applied Soft Computing"}, {"Title": "A spherical fuzzy methodology integrating maximizing deviation and TOPSIS methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "104212", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A new intelligent MCDM model for HCW management: The integrated BWM–MABAC model based on D numbers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "175", "Issue": "", "Page": "114862", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Multi-criteria healthcare waste disposal location selection based on Fermatean fuzzy WASPAS method", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "5", "Page": "2469", "JournalTitle": "Complex & Intelligent Systems"}, {"Title": "Evaluation of government strategies against COVID-19 pandemic using q-rung orthopair fuzzy TOPSIS method", "Authors": "<PERSON><PERSON><PERSON><PERSON> Alkan; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107653", "JournalTitle": "Applied Soft Computing"}, {"Title": "Archimedean t-Norm and t-Conorm-Based Aggregation Operators of HFNs, with the Approach of Improving Education", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "1", "Page": "310", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A q-rung orthopair fuzzy ARAS method based on entropy and discrimination measures: an application of sustainable recycling partner selection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "14", "Issue": "6", "Page": "6897", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Pythagorean fuzzy entropy measure-based complex proportional assessment technique for solving multi-criteria healthcare waste treatment problem", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "7", "Issue": "4", "Page": "917", "JournalTitle": "Granular Computing"}, {"Title": "Multicriteria q-Rung orthopair fuzzy decision analysis: a novel approach based on Archimedean aggregation operators with the confidence levels", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "9", "Page": "4375", "JournalTitle": "Soft Computing"}, {"Title": "An extended interval-valued Pythagorean fuzzy WASPAS method based on new similarity measures to evaluate the renewable energy sources", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "120", "Issue": "", "Page": "108689", "JournalTitle": "Applied Soft Computing"}, {"Title": "An intuitionistic fuzzy multi-distance based evaluation for aggregated dynamic decision analysis (IF-DEVADA): Its application to waste disposal location selection", "Authors": "<PERSON><PERSON><PERSON><PERSON> Alkan; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "111", "Issue": "", "Page": "104809", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A q-rung orthopair fuzzy decision-making model with new score function and best-worst method for manufacturer selection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "608", "Issue": "", "Page": "153", "JournalTitle": "Information Sciences"}, {"Title": "A framework of LR fuzzy AHP and fuzzy WASPAS for health care waste recycling technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "127", "Issue": "", "Page": "109388", "JournalTitle": "Applied Soft Computing"}, {"Title": "A new decision model with integrated approach for healthcare waste treatment technology selection with generalized orthopair fuzzy information", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "610", "Issue": "", "Page": "1010", "JournalTitle": "Information Sciences"}, {"Title": "Novel Fermatean Fuzzy Bonferroni Mean aggregation operators for selecting optimal health care waste treatment technology", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "119", "Issue": "", "Page": "105752", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": *********, "Title": "The News Media Bargaining Code: Impacts on Australian journalism one year on", "Abstract": "<p lang=\"zh\"> 摘要 <p>澳大利亚《新闻媒体议价法》(NMBC)是强制数字平台向新闻媒体机构支付第三方新闻内容费用的首次成功立法尝试。本文聚焦于NMBC实施第一年后的情况，探究NMBC是否成功实现了其公开声明的目标之一:支持公共利益新闻。我们论证，NMBC取得的成果符合澳大利亚竞争与消费者委员会(ACCC)关于行业权力失衡的政策目标，但仍有几个问题尚未解决。通过对“在NMBC最初实施期间参与Meta和Google谈判的澳大利亚新闻媒体高管”进行的半结构化访谈，我们探究了:a)新闻机构如何使用NMBC协议提供的经济补偿，b)各个组织的优先事项如何被建构和阐述，c)这些行动是否支持了澳大利亚的公共利益新闻。我们的研究结果表明，NMBC的许多问题主要是由于NMBC的意外影响而产生的，这包括:法律中缺乏平台指定、有资格参与谈判的新闻媒体的登记标准、以及法律中关于内部利益攸关方关系的定义。</p></p> <p lang=\"es\"> Resume <p>El Código de Negociación de Medios de Noticias de Australia (NMBC) es el primer intento legislativo exitoso de obligar a las plataformas digitales a pagar a las organizaciones de medios de comunicación por el contenido de noticias de terceros. Este documento se centra en el NMBC después de su primer año para explorar si el Código logró cumplir con uno de sus propósitos declarados públicamente; apoyar el periodismo de interés público. Argumentamos que el Código entregó resultados que cumplieron con los objetivos de la política de la ACCC en torno a un desequilibrio de poder de la industria, pero hay varios problemas que siguen sin abordarse. Usando entrevistas semiestructuradas de ejecutivos de medios de comunicación australianos involucrados en negociaciones con Meta y Google durante la implementación inicial del Código, exploramos: a) cómo las organizaciones de noticias usaron la compensación financiera proporcionada por los acuerdos bajo el Código, b) cómo las prioridades organizacionales individuales fueron enmarcado y articulado, y c) si estas acciones han apoyado el periodismo de interés público en Australia. Nuestros hallazgos indican que muchos de los problemas con el Código surgieron principalmente de los impactos no deseados del Código, que incluyen: la falta de designación de plataformas dentro de la legislación, los criterios de registro para los medios de comunicación elegibles para entrar en negociación y la definición de las relaciones de las partes interesadas dentro la legislación.</p></p>", "Keywords": "digital platforms;journalism;media law;media regulation;NMBC;platform regulation;public interest journalism;《新闻媒体议价法》;媒体法;媒体监管;平台监管;数字平台;新闻业;公共利益新闻;NMBC;Ley de Medios;Regulación de Medios;regulación de plataformas;plataformas digitales;periodismo;periodismo de interés público", "DOI": "10.1002/poi3.361", "PubYear": 2023, "Volume": "15", "Issue": "4", "JournalId": 4443, "JournalTitle": "Policy & Internet", "ISSN": "1944-2866", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Swinburne University Melbourne Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Swinburne University Melbourne Australia"}], "References": [{"Title": "Australia's News Media Bargaining Code and the global turn towards platform regulation", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "14", "Issue": "1", "Page": "136", "JournalTitle": "Policy & Internet"}]}, {"ArticleId": *********, "Title": "3D Face Recognition: Two Decades of Progress and Prospects", "Abstract": "<p>Three-dimensional (3D) face recognition has been extensively investigated in the last two decades due to its wide range of applications in many areas, such as security and forensics. Numerous methods have been proposed to deal with the challenges faced by 3D face recognition, such as facial expressions, pose variations, and occlusions. These methods have achieved superior performances on several small-scale datasets, including FRGC v2.0, Bosphorus, BU-3DFE, and Gavab. However, deep learning–based 3D face recognition methods are still in their infancy due to the lack of large-scale 3D face datasets. To stimulate future research in this area, we present a comprehensive review of the progress achieved by both traditional and deep learning–based 3D face recognition methods in the last two decades. Comparative results on several publicly available datasets under different challenges of facial expressions, pose variations, and occlusions are also presented.</p>", "Keywords": "", "DOI": "10.1145/3615863", "PubYear": 2024, "Volume": "56", "Issue": "3", "JournalId": 12172, "JournalTitle": "ACM Computing Surveys", "ISSN": "0360-0300", "EISSN": "1557-7341", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON>-sen University and National University of Defense Technology, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Information Engineering University, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Aviation University of Air Force, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sichuan University, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National University of Defense Technology, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "University of Western Australia, Australia"}], "References": [{"Title": "Voxel-based 3D face reconstruction and its application to face recognition using sequential deep learning", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "25-26", "Page": "17303", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Complement component face space for 3D face recognition from range images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2500", "JournalTitle": "Applied Intelligence"}, {"Title": "PCT: Point cloud transformer", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "7", "Issue": "2", "Page": "187", "JournalTitle": "Computational Visual Media"}, {"Title": "A comprehensive survey on 3D face recognition methods", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "104669", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "3D face recognition: A comprehensive survey in 2022", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "9", "Issue": "4", "Page": "657", "JournalTitle": "Computational Visual Media"}]}, {"ArticleId": 109404111, "Title": "Complex quantified formulas in SPARQL 1.1: formalisation, translation, and partial verbalisation", "Abstract": "In this paper, we propose quantified SPARQL 1.1 filter expressions, called also q-SPARQL filter expressions for short, based on a specific grammar that uses not only EXISTS and NOT EXISTS keywords but also FORALL-IMPLIES. These filter expressions is a syntactic sugar to SPARQL 1.1 filter expressions since they have a translation to them. However through various examples, we demonstrate their user-friendliness. Additionally, we formally define q-SPARQL queries which use q-SPARQL filter expressions. We further analyse which variables of q-SPARQL filter expressions are universally quantified and which are existential. We also indicate the places where these variables can appear. We translate two nested NOT EXISTS filter expressions into FORALL-IMPLIES form, when it is possible, improving user friendliness of the query. Finally, we partially verbalise q-SPARQL filter expressions, making easier their understanding from the user. © 2023 Inderscience Enterprises Ltd.", "Keywords": "analysis of variables; partial verbalisation; quantified SPARQL 1.1 filter expressions; translation to SPARQL 1.1", "DOI": "10.1504/IJWET.2023.132875", "PubYear": 2023, "Volume": "18", "Issue": "2", "JournalId": 26853, "JournalTitle": "International Journal of Web Engineering and Technology", "ISSN": "1476-1289", "EISSN": "1741-9212", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Computer Science, FORTH-ICS, Greece"}], "References": []}, {"ArticleId": 109404120, "Title": "Optimization of the Genetic Algorithm using Parallel Computing", "Abstract": "", "Keywords": "", "DOI": "10.17587/prin.14.401-406", "PubYear": 2023, "Volume": "14", "Issue": "8", "JournalId": 24080, "JournalTitle": "PROGRAMMNAYA INGENERIA", "ISSN": "2220-3397", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Volga Region State University of Telecommunications and Informatics, Samara, 443011, Russian Federation"}], "References": []}, {"ArticleId": 109404155, "Title": "Long Method Detection Using Graph Convolutional Networks", "Abstract": "Long Method is a code smell that frequently happens in software development, which refers to the com-plex method with multiple functions. Detecting and refactoring such problems has been a popular topic in software refactoring, and many detection approaches have been proposed. In past years, the approaches based on metrics or rules have been the leading way in long method detection. However, the approach based on deep learning has also attracted extensive attention in recent studies. In this paper, we propose a graph-based deep learning approach to de-tect Long Method. The key point of our approach is that we extended the PDG (Program Dependency Graph) into a Directed-Heterogeneous Graph as the input graph and used the GCN (Graph Convolutional Network) to build a graph neural network for Long Method detection. Moreover, to get substantial data samples for the deep learning task, we propose a novel semi-automatic approach to generate a large number of data samples. Finally, to prove the validity of our approach, we compared our approach with the existing approaches based on five groups of datasets manually reviewed. The evaluation result shows that our approach achieved a good performance in Long Method detection. © 2023, Information Processing Society of Japan. All rights reserved.", "Keywords": "code smell; deep learning; graph convolutional networks; Long Method; software refactoring", "DOI": "10.2197/ipsjjip.31.469", "PubYear": 2023, "Volume": "31", "Issue": "", "JournalId": 18954, "JournalTitle": "Journal of Information Processing", "ISSN": "", "EISSN": "1882-6652", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Inner Mongolia University of Science & Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Waseda University"}], "References": []}, {"ArticleId": 109404164, "Title": "Multi-objective Optimization Approach for Allocation and Sizing of Distributed Energy Resources Preserving the Protection Scheme in Distribution Networks", "Abstract": "<p>In this paper, a multi-objective optimization approach to solve the problem of optimal allocation and sizing of inverter-based distributed energy resources (DERs) in distribution systems is presented. The objectives of this allocation and sizing problem consist of the minimization of the investment and operation costs, the voltage deviation and the short-circuit currents. The recloser–fuse coordination constraints were included in the mathematical formulation of the problem, in order to preserve the original network protection scheme. It is important to mention that this protection scheme was also carried out in this paper, using the multi-objective approach, to minimize the operating time of the reclosers and fuses. The distribution system considered to evaluate the proposed methodology was the IEEE 34-Node Test Feeder, and the non-dominated sorting genetic algorithm II was used to solve the proposed multi-objective problems. Furthermore, a time-series-based probabilistic approach, through the Monte Carlo simulation, was adopted to deal with the uncertainties of the load and power generated by each DER. Finally, from the results, it was possible to reduce the investment and operation costs by 15.61% when compared to the system without DERs, improve the voltage profile and preserve the original protection scheme present in the network.</p>", "Keywords": "Distributed energy resource; Non-dominated sorting genetic algorithm II; Protection system; Short-circuit currents", "DOI": "10.1007/s40313-023-01030-4", "PubYear": 2023, "Volume": "34", "Issue": "5", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "Renato S. F. Ferraz", "Affiliation": "Postgraduate Program in Electrical Engineering, Federal University of Espírito Santo, Vitória, Brazil"}, {"AuthorId": 2, "Name": "Rafael S. F. <PERSON>", "Affiliation": "Postgraduate Program in Electrical Engineering, Federal University of Espírito Santo, Vitória, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>–Medina", "Affiliation": "Postgraduate Program in Electrical Engineering, Federal University of Espírito Santo, Vitória, Brazil"}], "References": []}, {"ArticleId": 109404247, "Title": "Dynamic Event-Triggered Adaptive Fixed-Time Fuzzy Tracking Control for Stochastic Nonlinear Systems Under Asymmetric Time-Varying State Constraints", "Abstract": "<p>In this article, an adaptive fixed-time tracking control scheme is investigated for stochastic nonlinear systems with asymmetric time-varying state constraints based on the dynamic event-triggered control (DETC) mechanism. First, a unified barrier function is employed to constrain the states into asymmetric time-varying functions directly. Then, through fuzzy logic systems (FLS) and DETC strategy, the issues of unknown nonlinear functions and computation burden are solved. Meanwhile, with the aid of backstepping technology and Lyapunov stability theory, it is proved that all state variables are successfully within the asymmetric time-varying functions and all signals in the closed-loop systems are bounded in a fixed time. In the final, two simulation experiments illustrate the feasibility and suitability of the controller.</p>", "Keywords": "Fuzzy logic systems; Dynamic event-triggered control; Stochastic nonlinear systems; State constraints", "DOI": "10.1007/s40815-023-01576-0", "PubYear": 2024, "Volume": "26", "Issue": "1", "JournalId": 4985, "JournalTitle": "International Journal of Fuzzy Systems", "ISSN": "1562-2479", "EISSN": "2199-3211", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Public Foundation, Bengbu Medical College, Bengbu, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer and Information, Anqing Normal University, Anqing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electronic and Information Engineering, Anhui Jianzhu University, Hefei, China"}], "References": [{"Title": "Adaptive neural consensus tracking control for a class of 2-order multi-agent systems with nonlinear dynamics", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "404", "Issue": "", "Page": "84", "JournalTitle": "Neurocomputing"}, {"Title": "Fast finite-time dynamic surface tracking control of a single-joint manipulator system with prescribed performance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "52", "Issue": "8", "Page": "1551", "JournalTitle": "International Journal of Systems Science"}]}, {"ArticleId": 109404291, "Title": "Improved power sharing in inverter based microgrid using multi-objective optimization", "Abstract": "The voltage-source inverter has an important role in electrical power sharing in microgrids , although, it requires a tight control and optimization technique to enhance performance and to improve power sharing. A sudden load change is a common phenomenon which may lead to fluctuations in the terminal voltages and load currents and eventually the system might become unstable. The PI control law represents one of the preferred control algorithms in industry, meanwhile its parameters need to be tuned to ensure its superiority. Two multi-objective optimization algorithms, the Genetic Algorithm , and the Water Cycle Algorithm, are proposed in this article to tune the controller gains . A microgrid consisting of two-parallel inverters is used to investigate the performance of the proposed tuning techniques where the system is analyzed in the dq-frame and the Space Vector Pulse Width Modulation was undertaken as a switching technique. MATLAB/SIMULINK simulations demonstrated the improved performance of the proposed approach. Introduction Global warming as well as depletion of conventional energy sources are the key driving forces for the widespread use of renewable energy technology. Renewable energy sources are characterized by intermittent nature which introduces challenges when they are connected to the grid. Microgrids (MGs) have emerged as small-scale replicas of the power grid to facilitate the integration of renewable energy sources. The MGs nowadays provide promising clean energy solutions for the worldwide increased energy demands. The MGs have sufficient resilience to be connected to the main grid as well as operating independently. In either configuration, the MGs might be AC, DC, or DC/AC networks; in all these configurations, MGs require a reliable control policy to maintain the system stability and to retain smooth power flow considering the nature of load uncertainty and plug and play mode of operation. As reported in several articles, Microgrids control strategies might be classified based on the considered reference frame which might be abc, dq , or αß configuration. Communication and non-communication-based power flow is another control strategy type of classification, the algorithm type exploited in the inner loop control is also viewed as an approach of classification. The classifications of MGs have been discussed intensively in recent articles; additional highlights of this subject can be found in [1], [2], [3]. The PID controller is still maintaining its popularity in a wide range of applications. It can speed up the response, increase the system bandwidth, and eliminate or minimize the steady state error. However, its performance completely depends on its parameters setting where the setting can be derived with conventional methodology or with advanced techniques which include artificial intelligence and nonconventional optimization techniques. Artificial intelligence, evolutionary optimization, and machine learning algorithms have become fundamental tools in solving many complex engineering problems. Particularly, the evolutionary optimization routines are successfully implemented with versatile methods to address the control strategies and power sharing of MGs. Multi-objective optimization techniques have become a notable approach in recent studies, and they are extensively discussed as effective methodologies to provide significant performance improvement when they are utilized in PID controller setting. The PI (Proportional-Integral) control algorithm combined with a genetic algorithm (GA) was introduced in [4] to address voltage control mode of the boost inverter. The contribution published in [5] confirms that the controller optimization with a GA can significantly minimize the total harmonic distortion in cascaded multilevel inverter with unequal DC sources. H-infinity control technique with GA was implemented in [6] to address the current control problem of a three-phase inverter. The work introduced in [7] considers a MG model with hybrid power sources and suggests a GA based on the Tabu search algorithm to maintain the system working at optimum mode which also satisfies economic operation perspectives. The work published in [8], introduces the Multi-Agent System (MAS)-based distributed coordinated control strategy. The article revises topologies and mathematical models including the non-cooperative game model and graph topology model, it also considers the particle swarm optimization algorithm and genetic algorithm. The authors of [9] discussed the dual-layer optimal dispatching model of MG. The load curve optimizing was implemented in the first layer to achieve the maximum load satisfaction. Meanwhile, in the second layer, the power utilization ratio was improved. The research also discussed the MG isolation issue and proposed utilizing the Non-dominated Sorting Genetic Algorithm-II (NSGA-II) to address the optimal scheduling problem. A bi-level optimization model was presented in [10]; the authors revealed that optimum operation can be achieved, and the voltage profile can be enhanced through interactions of the voltage and power exchange at the PCC (Point of Common Connection) in an iterative approach. The study published in [11] compares the control and optimization techniques that are widely implemented to enhance MGs transient stability in terms of benefits and drawbacks. The comparison includes Linear Quadratic Control (LQC), Sliding Mode Control (SMC), Droop Control, and Model Predictive Control (MPC). The Harmony Search (HS) algorithm was deployed in [12] to calculate the droop control parameters to yield appropriate load sharing. Application of the Swarm Intelligence (SI) optimization technique to AC MG control was a topic of [13]; the article argues that with the SI optimization algorithm, the power quality and transient response of MG can be improved for both standalone and grid-connected configuration. The research carried out in [14] explains how to address the economic dispatch problem (EDP) with capacity constraints as a solution to islanded DC MGs. Furthermore, the work highlights that the deployed distributed control requires only distributed communication between the decentralized generations to maintain the global voltage restored. A multi-microgrid day-ahead scheduling optimization model was applied in [15] to control the interactive power, where the authors proposed to use the two-layer optimization model to retain system stability and security. This also involves suppression of the interaction power variation which may occur between MGs and the main network. Implementation of PSO in various multi-agent structures was the core material published in [16]; the research undertakes an isolated MG to validate the performance of the proposed methodology. In [17] the micro-source output power was regulated through an integrated technique that consists of the chaotic of multi-agent system combined with the PSO algorithm; the developed technique is denoted by (MACPSO). The authors in [18] adopted the salp swarm inspired algorithm in conjunction with PSO (SSIA-PSO) for microgrid droop control design. The work published in [19] also uses PSO to address the point of the power sharing stabilization problem in MGs; the developed idea relies on the error minimization of the power sharing which can be accomplished through frequency deviation reduction. The power management and optimization in AC/DC microgrid using Multi-Objective Particle Swarm Optimization (MOPSO) algorithm was investigated in [20]. Model predictive control (MPC) is a model-based control approach which has been intensively utilized in industrial applications. Finite-Control-Set MPC (FCS-MPC) has become a popular technique in control of power converters. MPC was introduced in [21] to perform the energy distribution control of MGs, whereas [22] shows the MPC performance in smart grids compared with a compact control technique that uses three levels of hierarchical control. Fuzzy logic systems are also considered in MG control; for instance, the work developed in [23] exploits fuzzy logic control incorporated with PSO to accomplish the droop control strategy for islanded DGs. The conventional automatic generation control techniques which are principally inherited and modified from classical power system control are also conducted to control the power in MGs. Multi-objective optimization recently received many considerations, and it represents an active area of research in the control synthesis of MGs. In realistic scenarios, where the time delay and nonlinear loads are commonly encountered, the controller design becomes a more challenging problem. Based on the author's knowledge, there is no study presenting the application of Multi-Objective with the GA and the Water Cycle Algorithms for the distributed control strategies of two parallel inverters with loading change. This paper proposes application of Multi-Objective optimization with the GA and the Water Cycle Algorithms, the developed techniques are named MOGA and MOWCA respectively. The two techniques are utilized in PI controller tuning of a two-parallel inverters microgrid; the control methodology is introduced as an advanced development to find the optimum values of the PI controller parameters that can match the distributed control strategy requirements in both perspectives- system stability and power sharing. The rest of the paper is organized into four sections: a detailed model of the MG considering a two-parallel inverters MG is presented in Section II and supported with a description of the structure of the distributed control strategy. Section III introduces the details of the multi-objective optimization techniques based on MOGA and MOWCA algorithms. The obtained simulation results are demonstrated and compared in Section IV with appropriate discussion and analysis. Section V provides the research comments and the drawn conclusions. Section snippets Mathematical model of the two-parallel inverters The parallel connected inverters with distributed control strategy form a nonlinear and time-varying system. According to [24], its small signal state-space model can be derived based on a number of assumptions: the d - q axis components of the system voltage and current ( v<sub>d</sub>, v<sub>q</sub>, i <sub> d 1</sub>, i <sub> q 1</sub>, i <sub> d 2</sub>, i <sub> q 2</sub>) are selected as the state variables. Note that in the d -q variable components, the number in subscripts, are used to distinguish between the first inverter the second one. The d - q duty ratios ( d <sub> d 1</sub>, d <sub> q 1</sub> Multi-objective optimization Multi-objective optimization routines have become popular techniques in solving many sophisticated real-life engineering problems. In multi-objective optimization, there are more than one objective function to be either minimized or maximized, and the optimization decision variables are calculated by means of manipulating them so that all the defined cost functions are optimized, or in other words to determine the decision variables that provide the best trade-off between the different Simulation results and interpretations The target to be achieved in this research is to find the optimum controller setting that is supposed to maintain the controlled system stability and enhances the performance. The system is considered to be supplied with a DC input of 400 V and the parameters of the filters for each inverter are, C = 22μ F and L <sub>1</sub> = L <sub>2</sub> = 4 mH , and the system feeds a resistive load of 4.25 ohm at a frequency of 50 Hz. The two techniques, MOGA and MOWCA, are developed to calculate the controller gains according to: Conclusions The research presents multi-objective optimization as an efficient tuning technique for the MGs control strategy in order to maintain the system working inside the stable boundary with a dynamic power sharing mechanism. The suggested optimization incorporates several system variables derived on the basis of the d -q reference frame; it has exploited a distributed control strategy that involves control loops of the voltage, current as well as the delivered power. This was undertaken to ensure Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Asma Mohamed Najem Alfergani is a Lecturer at Electrical and Electronic Engineering Department, University of Benghazi, Libya. She received her M. Sc . from University of Benghazi, Libya in 2017. Her research focuses on networked control systems, time delay systems stability, application of artificial intelligence optimization techniques and Industrial Automation. References (29) B.E. Sedhom et al. Hierarchical control technique-based harmony search optimization algorithm versus model predictive control for autonomous smart microgrids Int J Electr Power Energy Syst (2020) A. Konak et al. Multi-objective optimization using genetic algorithms: a tutorial Reliab Eng Syst Saf (2006) H. Eskandar et al. Water cycle algorithm–A novel metaheuristic optimization method for solving constrained engineering optimization problems Comput Struct (2012) A. Sadollah et al. Water cycle algorithm for solving constrained multi-objective optimization problems Appl Soft Comput (2015) M. Prodanovic et al. A survey of control methods for three-phase inverters in parallel connection A. Alfergani et al. Control strategies in AC microgrid: a brief review S.M. Kaviri et al. A review of AC microgrid control methods G. Arunkumar et al. Genetic algorithm based voltage mode controlled boost inverter-using small signal modelling J Comput Theor Nanosci (2016) S. Debnath et al. THD optimization in 13 level photovoltaic inverter using genetic algorithm Int J Eng Res Appl (2012) N. Nimpitiwan et al. Robust control design for three-phase power inverters using genetic algorithm M. Xu et al. GA based multi-objective operation optimization of power microgrid Y. Han et al. MAS-based distributed coordinated control and optimization in microgrid and microgrid clusters: a comprehensive overview IEEE Trans Power Electron (2017) T. Pan et al. Dual-layer optimal dispatching strategy for microgrid energy management systems considering demand response Math Probl Eng (2018) X. Sun et al. Coordinated optimal voltage control strategy in distribution networks with multi-microgrids View more references Cited by (0) Recommended articles (6) Research article Design of reactive power optimization control for electromechanical system based on fuzzy particle swarm optimization algorithm Microprocessors and Microsystems, Volume 82, 2021, Article 103865 Show abstract Due to low precision and premature tendency of traditional particle swarm optimization, the reactive power optimization control of electromechanical system based on fuzzy particle swarm optimization algorithm was designed. The premise is to meet the constraints of operation conditions. The active network loss was reduced and the static reactive power optimization mathematical model of electromechanical system was constructed by changing the voltage and reactive power distribution of system. Meanwhile, the voltage did not exceed the limit, and the discrete control variables were limited by the maximum allowable action times, so that the dynamic reactive power optimization mathematical model of electromechanical system was built by minimizing the sum of network loss in twenty-four hours of a day. The particle swarm algorithm was optimized by adaptive adjustment strategy, and then the particle position of particle swarm optimization algorithm was updated. Moreover, the static and dynamic reactive power optimization mathematical model of electromechanical system was solved. Finally, the reactive power optimization control of the electromechanical system is realized. Experimental results show that the proposed method has high convergence performance, so it is able to realize the precise control of reactive power optimization for electromechanical system and eliminate the voltage exceeding specified limits of electromechanical system. In this way, the node voltage can always be within the specified range. Research article Revolution of frequency regulation in the converter-dominated power system Renewable and Sustainable Energy Reviews, Volume 111, 2019, pp. 145-156 Show abstract With the increase of converter-fed devices such as renewable energy sources, energy storage, electric vehicles, direct current transmissions and power electronic loads in the power system, the conventional synchronous generator (SG)-dominated power system has been gradually evolving toward a converter-dominated system. Power electronic interfaces (PEIs) can be controlled to emulate the frequency responses of SGs, but their inherent control mechanisms are quite different. As a result, the revolution of frequency regulation in the future converter-dominated power system is foreseeable from theory to practice. In this paper, the principle of frequency responses and the corresponding control strategies of PEIs are summarized. The major inherent differences between PEIs and SGs are clarified, and the distinctive releasable energy reserves and frequency response performances are revealed. The state-of-the-art frequency regulation strategies adopted by PEIs in different operation modes are reviewed. The multiple time-scale frequency behavior in converter-dominated power systems is presented. Some major technical challenges for future work are highlighted. Research article Optimal multi-objective sizing of a residential microgrid in Egypt with different ToU demand response percentages Sustainable Cities and Society, Volume 75, 2021, Article 103293 Show abstract Many countries encourage the establishment of microgrids to support the spread of renewable energy sources, as it is the best option for dealing with electricity problems and air pollution. In this study, four sizing scenarios of a residential microgrid in a northern Egyptian city surrounded by rural areas are introduced as an interpretative example to explore the optimal scheduling strategy. This paper’s novelty originated from the techno-economic and ecological performance comparison of different sizing strategies for a residential load based on renewable sources, considering load shifting algorithms and using rice straw as biomass fuel instead of its open-field burning. The proposed optimization algorithm illustrates that the PV/WG/Biomass/two-ways-grid-connection microgrid is the best investable-reliable sizing option with a minimum net present cost of about ($ 0.7073 m i l l i o n ), which is reduced by ($ 0.1664 m i l l i o n ) when 30% demand response participation is applied. Also, the PV/WG/Biomass system has the most significant environmental impact with a C O 2 emissions reduction of around ( 3.465 * 10 8 kg). Research article Hybrid standalone microgrid for agricultural last-mile: A techno-economic analysis Energy Reports, Volume 8, Supplement 15, 2022, pp. 980-990 Show abstract Last-mile communities require electricity for socio-economic development, but the access is limited and majorly depends on environmentally damaging fossil fuels. Therefore, strategic energy access planning is needed to advance such communities towards sustainable development Goals (SDGs). Driven by technological advancement, falling cost of installation, growing recognition of benefits, and proven track record; microgrid networks, and green energy initiatives are evolving into a full-fledged solution for the grid disconnected remote communities. Thus, this study proposes a grid-independent hybrid micro-grid alternative for a remote South African community. It comprises photovoltaic (PV) arrays and a diesel generator. Two scenarios were evaluated vis-a-vis the inclusion or non-inclusion of a diesel-fuelled electricity generator. In addition, the CO<sub>2</sub> emissions are calculated to determine the environmental benefit of the proposed scenarios. The modelling was performed using a commercial software called HOMER Pro. It can be concluded that decreasing fuel prices and increasing solar irradiation can lead to a reduction in the total NPC. Also, it was concluded from a sensitivity analysis that decreasing fuel price and increasing solar irradiation could reduce the total NPC. A significant cost difference was observed between the two system scenarios, with PV-Genset having the lowest LCOE (R4.01) though the cost of operation of PV system is lower (R14,859). Overall, the proposed system would provide affordable and sustainable energy access to the last mile residential communities. Research article Bi-objective economic feasibility of hybrid micro-grid systems with multiple fuel options for islanded areas in Egypt Renewable Energy, Volume 128, Part A, 2018, pp. 37-56 Show abstract The main target of this research is to allow modern distributed energy resources (DERs) to contribute effectively in the economic feasibility of hybrid renewable power generation system. There are several factors such as the net present cost (NPC), levelized cost of energy (COE), amount of greenhouse gases (GHG) emissions, and the ability of the hybrid system to meet the load at different meteorological conditions to consider when evaluating the effectiveness of hybrid generation system within microgrids. A multi-objective based optimization algorithm to reduce cost, emissions, and a combined solution between cost and emissions is investigated in this research. This research presents an approach to optimize a hybrid microgrid (HMG) system with different fuel options. The power management approach determines the optimal sizing of DERs based on ant colony optimization (ACO) algorithm. In order to find the best configuration, the obtained results are compared with genetic algorithm (GA), particle swarm optimization (PSO), and HOMER. Three isolated areas in Egypt with different metrological conditions are selected for optimization of HMG system, namely: Kharga, Saint Katherine, and Qussair. The results show that the combined optimal configuration of HMG system is better in satisfying load demands without violating any restraints. Research article Design and experimental validation of an intelligent controller for DC–DC buck converters Journal of the Franklin Institute, Volume 357, Issue 15, 2020, pp. 10353-10366 Show abstract In this paper, an adaptive fuzzy synergetic controller(AFSC) is designed around a dSpace based experimental setup to provide robust DC–DC buck converter voltage control. Upholding high performance is a challenging task when operating a system under varying operating conditions and parameters uncertainties as is the case when dealing with DC–DC converter control. Fuzzy systems are used to approximate converter dynamics assumed, in this paper, to be unknown (accurate mathematical model is thus considered unavailable). Synergetic control is used to construct a robust continuous control law easy to implement despite varying operating conditions. The combination of these two techniques results in an intelligent robust DC–DC converter control. Unlike many works published, the present paper provides experimental results which corroborate the given analytical stability proof of the closed-loop system and confirm the system signals boundedness. Simulation results followed by experimental validation show that the proposed controller is able to achieve satisfactory voltage regulation performances over system parameters uncertainties, reference output voltage changes, load changes and source voltage variations. Asma Mohamed Najem Alfergani is a Lecturer at Electrical and Electronic Engineering Department, University of Benghazi, Libya. She received her M. Sc . from University of Benghazi, Libya in 2017. Her research focuses on networked control systems, time delay systems stability, application of artificial intelligence optimization techniques and Industrial Automation. Saied Alaaesh was born in Benghazi, Libya, in 1984. He received the M. Sc . degrees in electrical and electronic engineering from the University of Benghazi, Libya, in 2018. He is currently an engineer at General Electricity Company of Libya. His-research interests include the power system stability and control, microgrids and applications of artificial intelligence. Awad Rasheed Shamekh is a Professor in control systems at Electrical and Electronic Engineering Department, University of Benghazi, Libya. He received his PhD from University of Manchester, UK. His-primary research interests lie in the area of advanced control techniques, system identification, and optimization. Ashraf Khalil is an associate professor at Department of Engineering Technology at Technical University of Denmark. He received his M. Sc . from university of Benghazi, Libya in 2004 and he received his PhD from University of Birmingham, UK in 2012. His-current research areas are power system stability, integration of renewable energy and application of artificial intelligence in power system. Ali Asheibi received his BSc. and MSc. degrees in electrical engineering from University of Benghazi Libya and PhD from University of Wollongong, Australia. His work experience was with G.E.C of Libya as a projects and planning engineer in distribution systems. He was an academic at University of Benghazi. He is currently an assistant professor A'Sharqiyah University, Ibra, Oman. This paper is for special section VSI-irep3. Reviews were processed by Guest Editor Dr. Ahmad Harb and recommended for publication. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2023.108902", "PubYear": 2023, "Volume": "110", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Electrical and Electronics Engineering Department, University of Benghazi, Benghazi, Libya;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "General Electricity Company of Libya (GECOL), Benghazi, Libya"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Electronics Engineering Department, University of Benghazi, Benghazi, Libya"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "DTU Engineering Technology, Technical University of Denmark, Ballerup 2750, Denmark"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, A'sharqyiah University, Ibra, Oman"}], "References": []}, {"ArticleId": *********, "Title": "Kinematics and stiffness analysis of a wheelchair-based cable-actuated ankle rehabilitation robot with flexure-based variable-stiffness devices", "Abstract": "<p>To improve the therapeutic effect of robot-assisted rehabilitation for stroke patients, a novel ankle rehabilitation robot was developed based on a cable-actuated parallel mechanism and wheelchair, considering its mechanical design, actuation and control scheme. Due to the unidirectional force transmission property of the cable, the robot was redundantly actuated, and the stiffness could be regulated by adjusting the cable forces. The variable-stiffness property improved the compliance and safety of the ankle rehabilitation robot during human–robot interactions. In order to increase the stiffness variation range, a flexure-based variable-stiffness device (VSD) was proposed and placed in the cable actuation unit. The large deflection behavior of the VSD was modeled based on Eule<PERSON>-<PERSON><PERSON><PERSON> beam theory, and the stiffness-force relationship was analyzed by finite element methods. The resulting VSD had a simple and compact structure and yielded a low nonlinear stiffness-force relationship. Due to the redundant actuation, the pose and stiffness of the robot could be controlled simultaneously. In the stiffness control of the robot according to rehabilitation program of the patients, the cable force distribution problem for stiffness control should be solved. Since the stiffness model of the robot was nonlinear, it was difficult to find out the cable forces using analytical methods. Thus, the cable force distribution problem for stiffness control was formulated as an optimization model and solved using numerical methods. A simulation example was implemented to verify the effectiveness of the analysis and algorithm.</p>", "Keywords": "Ankle rehabilitation robot; Human–robot interaction; Cable-actuated robot; Variable-stiffness device; Finite element method", "DOI": "10.1007/s11370-023-00479-1", "PubYear": 2024, "Volume": "17", "Issue": "1", "JournalId": 2152, "JournalTitle": "Intelligent Service Robotics", "ISSN": "1861-2776", "EISSN": "1861-2784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Part Rolling Technology, Ningbo University, Ningbo, China; Zhejiang Key Laboratory of Robotics and Intelligent Manufacturing Equipment Technology, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences (CAS), Ningbo, China; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Part Rolling Technology, Ningbo University, Ningbo, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Part Rolling Technology, Ningbo University, Ningbo, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Part Rolling Technology, Ningbo University, Ningbo, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Zhejiang Key Laboratory of Robotics and Intelligent Manufacturing Equipment Technology, Ningbo Institute of Materials Technology and Engineering, Chinese Academy of Sciences (CAS), Ningbo, China; Corresponding author."}], "References": [{"Title": "Kinematic and dynamic design and optimization of a parallel rehabilitation robot", "Authors": "<PERSON><PERSON> <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "3", "Page": "365", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Tension reduction method for a modular cable-driven robotic arm with co-shared cables", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "27", "JournalTitle": "Intelligent Service Robotics"}, {"Title": "Performance analysis and trajectory planning of multi-locomotion mode ankle rehabilitation robot", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "157", "Issue": "", "Page": "104246", "JournalTitle": "Robotics and Autonomous Systems"}]}, {"ArticleId": 109404430, "Title": "LDPC Codes Based on α-Resolvable Designs", "Abstract": "<p>Earlier regular low-density parity-check (LDPC) codes have been constructed using resolvable combinatorial designs. Here we have constructed LDPC codes from α -resolvable ( α > 1) designs which are generalization of resolvable designs. The LDPC codes obtained here from α -resolvable ( α > 1) designs have much higher code rate in comparison to the earlier codes for the same code length but constructed from resolvable designs.</p>", "Keywords": "LDPC codes; Tanner graph; Resolvable designs; Balanced incomplete block designs; Group divisible designs", "DOI": "10.1007/s42979-023-02088-2", "PubYear": 2023, "Volume": "4", "Issue": "5", "JournalId": 66134, "JournalTitle": "SN Computer Science", "ISSN": "", "EISSN": "2661-8907", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tata College, Kolhan University, Chaibasa, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Birsa Agricultural University, Ranchi, India; Bangalore, India"}], "References": [{"Title": "New construction of partial geometries based on group divisible designs and their associated LDPC codes", "Authors": "Hengzhou Xu; Zhongyang Yu; Dan Feng", "PubYear": 2020, "Volume": "39", "Issue": "", "Page": "100970", "JournalTitle": "Physical Communication"}]}, {"ArticleId": 109404434, "Title": "A 3D pose estimation framework for preterm infants hospitalized in the Neonatal Unit", "Abstract": "Infant pose estimation is crucial in different clinical applications, including preterm automatic general movements assessment. Recent infant pose estimation methods are limited by a lack of real clinical data and are mainly focused on 2D detection. We introduce a stereoscopic system for infants’ 3D pose estimation, based on fine-tuning state-of-the-art 2D human pose estimation networks on a large, real, and manually annotated dataset of infants’ images. Our dataset contains over 88k images, collected from 175 videos from 53 premature infants born <33 weeks of gestational age (GA), acquired within the Neonatology department of the Centre Hospitalier Universitaire de Saint Etienne, France, between 32 and 41 weeks of GA. This framework significantly reduced the pose estimation error compared to existing 2D infant pose estimation networks. It achieved a mean error of 1.72 cm on 18000 stereoscopic images in the 3D pose estimation task. This framework is the first 3D pose estimation tool dedicated to preterm infants hospitalized in the Neonatal Unit that does not depend on any visual markers or infrared cameras.", "Keywords": "Infant pose estimation; 3D pose; Stereoscopic vision; General movements assessment; Premature infants; Preterm birth; Video analysis", "DOI": "10.1007/s11042-023-16333-6", "PubYear": 2024, "Volume": "83", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Laborato<PERSON>, CNRS UMR 5516, Université Jean Monnet, IOGS, Saint-Étienne, France; INSERM, U1059 SAINBIOSE, Université Jean Monnet, Saint-Étienne, France; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratoire <PERSON>, CNRS UMR 5516, Université Jean Monnet, IOGS, Saint-Étienne, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "INSERM, U1059 SAINBIOSE, Université Jean Monnet, Saint-Étienne, France; Service de Néonatalogie, Centre Hospitalier Universitaire de Saint-Étienne, Saint-Étienne, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "INSERM, U1059 SAINBIOSE, Université Jean Monnet, Saint-Étienne, France; Service de Néonatalogie, Centre Hospitalier Universitaire de Saint-Étienne, Saint-Étienne, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Laboratoire <PERSON>, CNRS UMR 5516, Université Jean Monnet, IOGS, Saint-Étienne, France"}], "References": [{"Title": "EfficientPose: Scalable single-person pose estimation", "Authors": "<PERSON>; <PERSON><PERSON>; Espen AF Ihlen", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2518", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 109404448, "Title": "FogLBS: Utilizing fog computing for providing mobile Location-Based Services to mobile customers", "Abstract": "The growth of Location-Based Services (LBSs) has been made possible by the widespread use of GPS-enabled devices. Some important LBSs require the ability to quickly process moving spatial-keyword queries over moving objects, such as when a moving customer is looking for a nearby mobile fuel delivery service. While there have been solutions proposed for scenarios where either the queries or the objects being queried are moving, there is still a need for solutions that can handle scenarios where both are in motion. This research focuses on the application of fog computing to provide real-time processing of moving spatial-keyword queries for LBSs. Specifically, the research proposes a new model, FogLBS, designed to efficiently process moving continuous top-k spatial-keyword queries over moving objects in a directed streets network, with a particular emphasis on the use case of a mobile service provider. FogLBS computes queries’ answer sets for time intervals and incrementally updates them using novel optimization techniques and indexing structures. By implementing FogLBS in a fog computing architecture, the model is able to meet the real-time requirements of the service provider application and other similar LBSs. The results of extensive experiments demonstrate the effectiveness of the proposed model in terms of efficiency, scalability, and accuracy, making it a valuable contribution to the field of fog computing in LBSs.", "Keywords": "", "DOI": "10.1016/j.pmcj.2023.101832", "PubYear": 2023, "Volume": "94", "Issue": "", "JournalId": 4135, "JournalTitle": "Pervasive and Mobile Computing", "ISSN": "1574-1192", "EISSN": "1873-1589", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computing and Informatics, University of Sharjah, United Arab Emirates;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Al Aghbari", "Affiliation": "College of Computing and Informatics, University of Sharjah, United Arab Emirates"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computing and Informatics, University of Sharjah, United Arab Emirates"}], "References": [{"Title": "Efficient processing of moving collective spatial keyword queries", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "29", "Issue": "4", "Page": "841", "JournalTitle": "The VLDB Journal"}, {"Title": "Continuous top-k spatial–keyword search on dynamic objects", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "2", "Page": "141", "JournalTitle": "The VLDB Journal"}, {"Title": "Fog computing: A taxonomy, systematic review, current trends and research challenges", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "56", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "Continuous spatial keyword query processing over geo-textual data streams", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "3", "Page": "889", "JournalTitle": "World Wide Web"}, {"Title": "Reverse spatial top-k keyword queries", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "32", "Issue": "3", "Page": "501", "JournalTitle": "The VLDB Journal"}]}, {"ArticleId": *********, "Title": "Code and Data Repository for Decision Rule Approaches for Pessimistic Bilevel Linear Programs Under Moment Ambiguity with Facility Location Applications", "Abstract": "This archive is distributed in association with the INFORMS Journal on Computing under the GNU GPLv3. The software and data in this repository are a snapshot of the software and data that were used in the research reported on in the paper Decision Rule Approaches for Pessimistic Bilevel Linear Programs under Moment Ambiguity with Facility Location Applications by <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. The snapshot is based on this SHA in the development repository.", "Keywords": "", "DOI": "10.1287/ijoc.2022.0168.cd", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109404740, "Title": "Modeling of flexible metal wheel for pressurized lunar rover and traction performance prediction", "Abstract": "<p>The pressurized lunar rover has become one of the most important equipment in lunar exploration and resource utilization missions. On the terrain of soft lunar regolith, the rover wheels are easy to slip, sink, or even fail to move. To improve the traction performance of the rover on soft lunar soil, it is necessary to study the interaction between the wheels and the lunar soil. The deformation of the flexible wheel provides a larger contact area, which results in greater tractive force. Moreover, flexible wheels have better comfort and stability than rigid wheels, which are needed for manned rovers. However, there are few studies on the wheel soil interaction model of the heavy flexible wheel for the pressurized lunar rover. In this paper, a metal flexible wheel soil interaction model for pressurized lunar rovers was established, and the traction performance of the flexible wheel was predicted by using this model. Then, the accuracy of the wheel soil interaction model was verified by the soil bin test. The experimental results showed that the average error between the theoretical value of sinkage and the experimental value was 13.9%, and the average error between the theoretical and experimental value of drawbar pull was 11.5%, indicating that the model has high prediction accuracy. The new model can be used to predict the traction performance of flexible wheels and the experimental results can provide a reference for the flexible wheel design lunar rovers.</p>", "Keywords": "flexible metal wheel;pressurized lunar rover;traction performance;wheel soil interaction model", "DOI": "10.1002/rob.22239", "PubYear": 2023, "Volume": "40", "Issue": "8", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory for Advanced Mechatronic System Design and Intelligent Control School of Mechanical Engineering, Tianjin University of Technology Tianjin China;National Demonstration Center for Experimental Mechanical and Electrical Engineering Education Tianjin University of Technology Tianjin China;Key Laboratory for Bionics Engineering of Education Ministry Jilin University Changchun China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory for Bionics Engineering of Education Ministry Jilin University Changchun China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory for Advanced Mechatronic System Design and Intelligent Control School of Mechanical Engineering, Tianjin University of Technology Tianjin China;National Demonstration Center for Experimental Mechanical and Electrical Engineering Education Tianjin University of Technology Tianjin China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Tianjin Key Laboratory for Advanced Mechatronic System Design and Intelligent Control School of Mechanical Engineering, Tianjin University of Technology Tianjin China;National Demonstration Center for Experimental Mechanical and Electrical Engineering Education Tianjin University of Technology Tianjin China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory for Mechanics in Fluid Solid Coupling Systems, Institute of Mechanics Chinese Academy of Sciences Beijing China;Guangdong Aerospace Research Academy Guangdong China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Spacecraft System Engineering CAST Beijing China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Bionics Engineering of Education Ministry Jilin University Changchun China"}], "References": [{"Title": "A Brief Review of Some Interesting Mars Rover Image Enhancement Projects", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "10", "Issue": "9", "Page": "111", "JournalTitle": "Computers"}]}, {"ArticleId": 109404773, "Title": "LSTM-BASED SERVER AND ROUTE SELECTION IN DISTRIBUTED AND HETEROGENEOUS SDN NETWORK", "Abstract": "<p>Today, the Software-defined Network, with its advantages such as greater reliability via automation, more efficient network management, cost-savings, and faster scalability, is increasingly being deployed in many network systems and network operators. The most common deployment architecture is a distributed system with the existence of many independent domains, each controlled by an SDN controller. One of the well-known applications in SDN is server selection and routing. However, deploying server and route selection in distributed and heterogeneous SDN networks faces two issues. First, the lack of global views of the whole system is because the inter-communication between SDN domains has not been standardized for the distributed and heterogeneous SDN network. To solve this issue, we use our previous work, an open East-West interface called SINA, to adaptively guarantee the network state consistency of the distributed SDN network with multiple domains. Secondly, selecting the path for packet transmission based only on the current network states of a local SDN domain is ineffective as it can bring over-utilization to several links and under-utilization to others. Predicting the link cost of the whole path from the source to the destination is necessary. Therefore, this paper proposes an LSTM-based link cost prediction for the server and route selection mechanism in a distributed and heterogeneous SDN network. The experimental results show that our proposal improves up to 15% of link utilization, reduces 10% of packet loss, and obtains the lowest servers’response time compared to benchmarks</p>", "Keywords": "SDN;Inter-SDN domain;LSTM;Network state prediction;QoS;server and route selection algorithm", "DOI": "10.15625/1813-9663/17591", "PubYear": 2023, "Volume": "39", "Issue": "1", "JournalId": 41141, "JournalTitle": "Journal of Computer Science and Cybernetics", "ISSN": "1813-9663", "EISSN": "1813-9663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Hanoi University of Civil Engineering (HUCE), Ha Noi, Viet Nam}"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Hai Anh Tran", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Son Duong", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": [{"Title": "SDN-based real-time urban traffic analysis in VANET environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "162", "JournalTitle": "Computer Communications"}, {"Title": "A Survey on Distributed Machine Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "53", "Issue": "2", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": *********, "Title": "An Approximation Algorithm for k-Depot Split Delivery Vehicle Routing Problem", "Abstract": "", "Keywords": "", "DOI": "10.1287/ijoc.2021.0193.cd", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 7822, "JournalTitle": "INFORMS Journal on Computing", "ISSN": "1091-9856", "EISSN": "1526-5528", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": *********, "Title": "A temporally resolved DNA framework state machine in living cells", "Abstract": "The environments in living cells are highly heterogeneous and compartmentalized, posing a grand challenge for the deployment of theranostic agents with spatiotemporal precision. Despite rapid advancements in creating nanodevices responsive to various cues in cellular environments, it remains difficult to control their operations based on the temporal sequence of these cues. Here, inspired by the temporally resolved process of viral invasion in nature, we design a DNA framework state machine (DFSM) that can target specific chromatin loci in living cells in a temporally controllable manner. The DFSM is composed of a six-helix DNA framework with multiple locks that can be opened via DNA strand displacement. The opening of locks at different locations results in distinct structural configurations of the DFSM. We show that the DFSM can switch among up to six structural states with reversibility, in response to the temporally ordered molecular inputs, including DNA keys, adenosine triphosphate or nucleolin. By implementing state switching of the DFSM in living cells, we demonstrate temporally controlled CRISPR–Cas9 targeting towards specific chromatin loci, which sheds light on biocomputing and smart theranostics in complex biological environments. The heterogeneous and compartmentalized environments within living cells make it difficult to deploy theranostic agents with precise spatiotemporal accuracy. <PERSON> et al. demonstrate a DNA framework state machine that can switch among multiple structural states according to the temporal sequence of molecular cues, enabling temporally controlled CRISPR–Cas9 targeting in living mammalian cells.", "Keywords": "", "DOI": "10.1038/s42256-023-00707-4", "PubYear": 2023, "Volume": "5", "Issue": "9", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute of Materiobiology, Department of Chemistry, College of Science, Shanghai University, Shanghai, China; The Interdisciplinary Research Center, Shanghai Synchrotron Radiation Facility, Zhangjiang Laboratory, Shanghai Advanced Research Institute, Chinese Academy of Sciences, Shanghai, China; Institute of Biomedical Health Technology and Engineering, Shenzhen Bay Laboratory, Shenzhen, China"}, {"AuthorId": 2, "Name": "Shuting <PERSON>", "Affiliation": "Division of Physical Biology, CAS Key Laboratory of Interfacial Physics and Technology, Shanghai Institute of Applied Physics, Chinese Academy of Sciences, University of Chinese Academy of Sciences, Shanghai, China; Xiangfu Laboratory, Jiashan, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Physical Biology, CAS Key Laboratory of Interfacial Physics and Technology, Shanghai Institute of Applied Physics, Chinese Academy of Sciences, University of Chinese Academy of Sciences, Shanghai, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Institute of Molecular Medicine, Shanghai Key Laboratory for Nucleic Acid Chemistry and Nanomedicine, Renji Hospital, School of Medicine, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Division of Physical Biology, CAS Key Laboratory of Interfacial Physics and Technology, Shanghai Institute of Applied Physics, Chinese Academy of Sciences, University of Chinese Academy of Sciences, Shanghai, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Materiobiology, Department of Chemistry, College of Science, Shanghai University, Shanghai, China; The Interdisciplinary Research Center, Shanghai Synchrotron Radiation Facility, Zhangjiang Laboratory, Shanghai Advanced Research Institute, Chinese Academy of Sciences, Shanghai, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Chemistry and Chemical Engineering, New Corner Stone Science Laboratory, Frontiers Science Center for Transformative Molecules, National Center for Translational Medicine, Shanghai Jiao Tong University, Shanghai, China; Zhangjiang Laboratory, Shanghai, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory for Organic Electronics and Information Displays (KLOEID), Institute of Advanced Materials (IAM) and School of Materials Science and Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 9, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Molecular Medicine, Shanghai Key Laboratory for Nucleic Acid Chemistry and Nanomedicine, Renji Hospital, School of Medicine, Shanghai Jiao Tong University, Shanghai, China; School of Chemistry and Chemical Engineering, New Corner Stone Science Laboratory, Frontiers Science Center for Transformative Molecules, National Center for Translational Medicine, Shanghai Jiao Tong University, Shanghai, China"}, {"AuthorId": 10, "Name": "<PERSON>", "Affiliation": "Institute of Materiobiology, Department of Chemistry, College of Science, Shanghai University, Shanghai, China; The Interdisciplinary Research Center, Shanghai Synchrotron Radiation Facility, Zhangjiang Laboratory, Shanghai Advanced Research Institute, Chinese Academy of Sciences, Shanghai, China"}, {"AuthorId": 11, "Name": "<PERSON><PERSON> Wang", "Affiliation": "Institute of Materiobiology, Department of Chemistry, College of Science, Shanghai University, Shanghai, China; The Interdisciplinary Research Center, Shanghai Synchrotron Radiation Facility, Zhangjiang Laboratory, Shanghai Advanced Research Institute, Chinese Academy of Sciences, Shanghai, China; Zhangjiang Laboratory, Shanghai, China"}, {"AuthorId": 12, "Name": "<PERSON>", "Affiliation": "Institute of Materiobiology, Department of Chemistry, College of Science, Shanghai University, Shanghai, China; The Interdisciplinary Research Center, Shanghai Synchrotron Radiation Facility, Zhangjiang Laboratory, Shanghai Advanced Research Institute, Chinese Academy of Sciences, Shanghai, China"}, {"AuthorId": 13, "Name": "<PERSON><PERSON> Fan", "Affiliation": "School of Chemistry and Chemical Engineering, New Corner Stone Science Laboratory, Frontiers Science Center for Transformative Molecules, National Center for Translational Medicine, Shanghai Jiao Tong University, Shanghai, China"}], "References": [{"Title": "Molecular convolutional neural networks with DNA regulatory circuits", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "4", "Issue": "7", "Page": "625", "JournalTitle": "Nature Machine Intelligence"}]}, {"ArticleId": 109405095, "Title": "Dual-valved skin-interfaced microfluidic device for programmed time-control sweat collection", "Abstract": "Biomarkers in human sweat can provide valuable insights into physiological states, identify diseases, and help clinicians understand patients without invasive measurements. However, obtaining accurate long-term data from sweat using current sweat collection methodology raises doubts. Here, we describe a soft-bioelectronic platform that uses a thermal expansion valve to collect sweat under programmed time control. By incorporating expandable microspheres , the skin-interfaced microfluidic device platform merges capillary burst valves and irreversible-thermal expansion valves. An expansion layer below the microfluidic channels expands irreversibly upon heat delivery from underlying microheaters. The thermal expansion valve isolates the collected sweat into separate chambers to minimize internal mixing. The human study demonstrates that our novel time-programmed sweat collection device improved sweat analyte data (sweat volume, pH, lactate, and cortisol) from the conventional sweat collection. In addition, the human study results indicate a correlation between sweat rate and sweat lactate with aerobic and anaerobic exercises. Our approach can be integrated with past and current microfluidic systems and other chemical sensing modalities, such as colorimetric and electrochemical measurements of sweat analytes present in current sweat devices for advanced on-board diagnostics.", "Keywords": "", "DOI": "10.1016/j.snb.2023.134441", "PubYear": 2023, "Volume": "395", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Biomedical Engineering, State University of New York at Binghamton, Binghamton, NY 13902, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Nursing and Department of Electrical and Computer Engineering, University of Massachusetts, Amherst, MA 01003, USA"}, {"AuthorId": 3, "Name": "Yeon Sik Noh", "Affiliation": "College of Nursing and Department of Electrical and Computer Engineering, University of Massachusetts, Amherst, MA 01003, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, State University of New York at Binghamton, Binghamton, NY 13902, USA;Corresponding author"}], "References": [{"Title": "Wearable multifunctional sweat-sensing system for efficient healthcare monitoring", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "328", "Issue": "", "Page": "129017", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Recent progress, challenges, and opportunities for wearable biochemical sensors for sweat analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "332", "Issue": "", "Page": "129447", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "In-situ admittance sensing of sweat rate and chloride level in sweat using wearable skin-interfaced microfluidic patch", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "379", "Issue": "", "Page": "133213", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Microfluidic paper-based wearable electrochemical biosensor for reliable cortisol detection in sweat", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "379", "Issue": "", "Page": "133258", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Wearable molecularly imprinted electrochemical sensor with integrated nanofiber-based microfluidic chip for in situ monitoring of cortisol in sweat", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "381", "Issue": "", "Page": "133451", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A flexible nonenzymatic sweat glucose sensor based on Au nanoflowers coated carbon cloth", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2023, "Volume": "388", "Issue": "", "Page": "133798", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 109405098, "Title": "Personalized federated learning: A Clustered Distributed Co-Meta-Learning approach", "Abstract": "Federated Learning (FL) aims to train a model across multiple parties while preserving the privacy of users&#x27; data. Traditional FL only develops a common model for users, and does not adapt the model to each user. Therefore, personalized FL approaches emerged that can further adapt the model to users, thus showing better performance. Among these personalized FL methods, meta-learned personalized FL methods achieve the advanced performance. However, this personalization scheme adapts model to each user according to their own data, and the features it learned are not enough and not rich, especially when there are extremely little data in some users. In this paper, we study a more effective variant of personalization federated learning. We first formalize a new learning problem and propose a Distributed Co-Meta-Learning approach for this learning problem. Then, we show how to design a new personalized FL framework based on this Distributed Co-Meta-Learning approach. To optimize our proposed personalized FL framework, while reducing the computational cost in the optimization, we study a chain-estimation aggregation method for our framework. It also reduces the computational load in the clients. Further, we give the theoretical convergence analysis of our method on the most complex case, non-convex and non-IID problems, and analyze some parameters&#x27; properties within it. Experiments demonstrate that our method achieves the state-of-the-art performance in the personalization FL area.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119499", "PubYear": 2023, "Volume": "647", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical and Power Engineering, East China University of Science and Technology, Shanghai, 200237, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Smart Manufacturing in Energy Chemical Process, Ministry of Education, China;Department of Computer Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China;Corresponding author at: Department of Computer Science and Engineering, East China University of Science and Technology, Shanghai, 200237, China"}, {"AuthorId": 3, "Name": "Xinhai Yu", "Affiliation": "Department of Mechanical and Power Engineering, East China University of Science and Technology, Shanghai, 200237, China;Corresponding author"}], "References": [{"Title": "Advances and Open Problems in Federated Learning", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "1–2", "Page": "1", "JournalTitle": "Foundations and Trends® in Machine Learning"}, {"Title": "Geometric imbalanced deep learning with feature scaling and boundary sample mining", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "126", "Issue": "", "Page": "108564", "JournalTitle": "Pattern Recognition"}, {"Title": "Multi-center federated learning: clients clustering for better personalization", "Authors": "<PERSON><PERSON> Long; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "26", "Issue": "1", "Page": "481", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 109405105, "Title": "Nonlinear disturbance observer-based robust predefined time tracking and vibration suppression control for the rigid-flexible coupled robotic mechanisms with large beam-deformations", "Abstract": "In order to establish the dynamic equations of the rigid-flexible coupled robotic mechanisms with large beam-deformations over the horizontal plane, a thorough modeling technique founded on the virtual work concept has been proposed. Based on the transformed full-actuated model, the predefined-time robust sliding mode control strategy is developed to track the prescribed angular positions of the rigid-flexible coupled robotic mechanisms. In addition to compensating the time-varying external disturbances, the nonlinear disturbance observer is also integrated and resolved into predefined-time robust sliding mode schemes. By introducing the virtual input, the robust linear quadratic state feedback controller is developed to eliminate the tracking errors and vibration modes of the rigid-flexible coupled robotic mechanisms simultaneously. Furthermore, it is proved that the tracking errors and vibration modes can be uniformly stable under the developed scheme based on <PERSON><PERSON><PERSON><PERSON>'s stability theory. Finally, three numerical examples are conducted to access the effectiveness and excellence of the presented nonlinear disturbance observer-based predefined-time robust sliding mode and robust linear quadratic state feedback strategies in comparison to nonlinear disturbance observer-based backstepping control and slide mode control schemes. Introduction Recently, since the rigid-flexible coupled robotic mechanisms (RFCRMs) are lighter and more agile than rigid-rigid ones [1], [2], and they may cover a larger work area, this kind of RFCRMs is employed in a variety of industrial scenarios [3], [4], and is particularly significant for space missions and maritime engineering [5], [6]. Nevertheless, the light weight might result in elastic deformation and vibration because of the apparent inadequate stiffness [7]. As the flexibility dynamics include partial differential equations (PDEs) with infinite dimensions, the RFCRMs is essentially a distributed parameter system. This presents substantial challenges for precise dynamic modeling, tracking, and vibration control design [8]. The discretization of the flexible beam in the dynamic modeling of RFCRMs is a major problem [9]. The presumptive vibration modes of structures under static boundary conditions have served as the foundation for a number of discrete deformation formulations up to this point, including the kineto-elasto dynamics (KED) [10], assumed mode method (AMM) [11], [12], finite element method (FEM) [13], [14], etc. The vibration modes in AMM are easily derived from the conventional structural dynamics theory, yet only flexible bodies with regular forms are meaningful to them. Furthermore, a factor that is frequently disregarded but obviously has an impact on computation efficiency is the number of modes truncation. The fundamental cause of the FEM drawbacks which have led is its reliance on elements needed for finite element interpolations and numerical integration [15]. The primary advantage of FEM is that, as compared to the AMM technique, it requires less calculations to estimate the Euler-Bernoulli linkages of intricate geometrical structures and of flexible robotic arms [16]. This technique is now better suited for implementation in real-time dynamically model-based control system designs. But some of them could have intrinsic limitations in regards to large beam-deflection. [17]. For illustrations, the chained algorithms and FEM are reliable and accurate but rely on the premise that computation costs would increase [18], [19]. Besides, due to their high-dimensional models, it is thus difficult to design a real-time controller [20]. As a result, it is essential to enhance the current modeling approaches for RFCRMs under the consideration of big deformations since doing so will provide the groundwork for the control design that will follow. Thus far, many researchers have dedicated themselves to seeking various control techniques for the trajectory tracking and vibration suppression of the RFCRMs with small beam-deformations [21], such as robust boundary control [22], iterative learning control [23], singular perturbation method [24], and optimal control via neural networks [25], etc. In the form of an Euler-Bernoulli beam-ODE for a class of RMCRMs with control-matched unknown faults, the backstepping-based sliding mode approach is designed to eliminate vibration in [26]. The authors in [24] addressed the trajectory tracking and vibration attenuation handling in the planar RFCRMs based on singular perturbation theory, where the visual servoing control and linear quadratic regulator were applied into slow and fast subsystems, respectively. Nevertheless, the controlled system can only be made to be asymptotically stable using the aforementioned control strategies [24], [25], [26]. It is known that the capacity to react quickly plays a key role in determining how well a control system performs. Numerous finite-time control methods like nonsingular terminal sliding mode, fixed time sliding mode, such as those for Euler-Lagrange systems [27], marine vehicles [28], and other control domains [22], [29], have been developed in an effort to do this. Even though a stable sliding manifold can be reached in a finite period of time due to variable structure and sliding mode control approaches, the settling-time function is unbounded with regard to the initial states of systems [30]. This issue has to be solved by taking into account the more specific argument of predefined-time stability, which has been researched in [31]. An explicit adjust parameter for the control design of a predefined-time stable system includes a (least) upper bound for the fixed stabilization time [32]. By using the predefined-time control strategy, several pioneers have only undertaken relevant study [30], free-floating space robots [33]. To our knowledge, no research has been conducted on the robust predefined-time control integrated with the sliding mode of the RFCRMs with large beam deformations, though. It is noteworthy that the majority of control strategies listed above were created on the basis that the controlled object is the full actuated RFCRMs with small beam-deformations [23], [24], [25]. The dynamics equations for the RFCRMs with large beam deformations are first developed in this work and they are regarded as underactuated systems. Specifically, due to the influence of underactuation, the direct control scheme cannot be applied to the unactuated part of the RFCRMs with large beam deformations [34], [35]. As control objectives considered in this work are to follow the prescribed angular target and to eliminate the vibration of the RFCRMs with large beam deformations simultaneously. In order to accomplish the two control goals mentioned above, it is necessary to fully utilize the complex nonlinear internal connection between the actuated and unactuated system states, which typically calls for equal transformations and in-depth analysis [36]. Hence, the trajectory tracking and vibration suppression control for underactuated RFCRMs with large beam deformations subject to external disturbances are still quite challenging and opening problems are required to solve. Motivated by the above observation, the dynamic equations of RFCRMs with large deformations will be firstly established by a comprehensive modeling methodology, and the nonlinear disturbance observer-based predefined time sliding mode control incorporated with robust linear quadratic state feedback strategy will be proposed in this work. The following lists a summary of main contributions in this work: 1. Unlike the traditional flexible mechanism theory with small beam deformations, the novel dynamic equations of RFCRMs with large beam deformations are established over the horizontal plane. 2. The dynamic coupling relationships among the actuated and un-actuated parts in the novel established RFCRMs model are decomposed in this work, providing the theoretical underpinnings for the nonlinear disturbance observer (NDO) based predefined time robust sliding mode control (PTRSMC) strategy. 3. Different from the previous singular perturbation control technology with two control laws, based on the virtual input, only one control law is required to be designed, that is the NDO-based PTRSMC incorporated with a robust linear quadratic state feedback strategy, which has the remarkable advantage that the trajectory tracking and the flexible vibration suppression can be guaranteed simultaneously. The following parts comprise the remaining portions of this work. Section 2 discusses the research objectives and model equations of RFCRMs with large deflections. Then, in Section 3, the predefined-time robust sliding mode control scheme and linear quadratic state feedback controllers are constructed. The associated stabilization analysis of tracking errors and vibration modes are also demonstrated. In Section 4, three numerical examples using the proposed techniques are presented; Section 5 draws conclusions. Section snippets Dynamic modeling and control objectives The RFCRMs consist of two links, the first of which is rigid (shown in Fig. 1) and the second of which is flexible (beam-like), both of which are powered by motors over a horizontal plane. The right side of Fig. 1 shows the RFCRMs' geometric model. The inertial axis is represented by XOY , and x o 1 y denotes the moving body axis. The space-time variables x ( t , s ) and y ( t , s ) are being used to represent the horizontal and vertical beam deflection in the follow-up x o 1 y coordinate system, respectively. NDO-PTRSMC design for vibration suppression and trajectory tracking In this study, the nonlinear disturbance observer (NDO) based predefined time robust sliding mode control (PTRSMC) technique is developed based on the idea of virtual force. The proposed algorithm, which is depicted in Fig. 2, not only maintains the robustness to unknown external disturbances but also actively suppresses the flexible vibration in the trajectory tracking process. As shown in Fig. 2, the PTRSMC scheme is mainly composed of three parts, the first part is the estimation of the Numerical simulation To better show the superiority of the developed NDO-based PTRSMC scheme, the following two control approaches (Backstepping method and traditional sliding mode control) incorporated with a nonlinear disturbance observer are utilized. The nonlinear disturbance observer-based backstepping control (NDO-BSC) law is designed as follows: τ B S C ( t ) = D ( β , θ ) [ Λ 1 y ˙ 1 ( t ) − Λ 2 y ˙ 2 ( t ) + y 1 ( t ) + θ ¨ h ( t ) ] − d ˆ ( t ) + h ( θ , θ ˙ , β , β ˙ ) , where y 1 ( t ) = θ h ( t ) − θ ( t ) , y 2 ( t ) = θ ˙ ( t ) − Λ 1 y 1 ( t ) − θ ( t ) , Λ j , j = 1 , 2 denote two positive definite matrices, Conclusions The model equations of the RFCRMs with large beam-deformations over the horizontal plane has been established via the virtual work principle. Based on the transformed full-actuated model and nonlinear disturbance observer (NDO), the predefined time robust sliding mode control (PTRSMC) has been developed to follow the desired angular positions of two links for the RFCRMs with large deformations in presence of external disturbance. By introducing the virtual input and proposing the robust linear Acknowledgements The work was partially supported by the National Natural Science Foundation of China (Nos. 62173182 , 61773212 ), the International Science & Technology Cooperation Program of China (No. 2021YFE0102700 ), and the Scholarship from the China Scholarship Council (No. 202006840089 ). References (43) A. Kumar et al. Trajectory control of a two DOF rigid-flexible space robot by a virtual space vehicle Robot. Auton. Syst. (2013) Z.J. Zhao et al. Boundary adaptive fault-tolerant control for a flexible Timoshenko arm with backlash-like hysteresis Automatica (2021) X. Zhang et al. Numerical simulation of separated flows around a hybrid rigid-flexible thin plate Appl. Ocean Res. (2022) B. Wang et al. Rigid-flexible coupling dynamic modeling and analysis of dumbbell-shaped spacecraft Aerosol Sci. Technol. (2022) W. He et al. Unified iterative learning control for flexible structures with input constraints Automatica (2018) Z.X. Jiang et al. Event-driven observer-based control for distributed parameter systems using mobile sensor and actuator Comput. Math. Appl. (2016) S.Y. Jung et al. Comparison of flow structures behind rigid and flexible finite cylinders Int. J. Mech. Sci. (2018) A. Fenili et al. The rigid-flexible nonlinear robotic manipulator: modeling and control Commun. Nonlinear Sci. Numer. Simul. (2011) A.G. Souza et al. Design of a controller for a rigid-flexible satellite using the H-infinity method considering the parametric uncertainty Mech. Syst. Signal Process. (2019) M. Oleksy et al. An improved multiscale FEM for the free vibrations of heterogeneous solids Comput. Math. Appl. (2022) H.H. Yoo et al. Dynamics of flexible beams undergoing large overall motions J. Sound Vib. (1995) K. Wu et al. Insight into numerical solutions of static large deflection of general planar beams for compliant mechanisms Mech. Mach. Theory (2022) K. Wu et al. Solutions to large beam-deflection problems by Taylor series and Pade approximant for compliant mechanisms Mech. Mach. Theory (2022) S. Choura et al. Control of a two-link rigid-flexible manipulator with a moving payload mass J. Sound Vib. (2001) F.F. Cao et al. An adaptive iterative learning algorithm for boundary control of a coupled ODE-PDE two-link rigid-flexible manipulator J. Franklin Inst. (2017) D. Zhao et al. A sliding mode fault compensation scheme for a coupled rigid-flexible system in PDE-ODE form J. Franklin Inst. (2020) M. Galicki Finite-time control of robotic manipulators Automatica (2015) B. Zhou et al. Fixed-time neural network trajectory tracking control for underactuated surface vessels Ocean Eng. (2021) X.Y. Zhou et al. Neural network state observer-based robust adaptive fault-tolerant quantized iterative learning control for the rigid-flexible coupled robotic systems with unknown time delays Appl. Math. Comput. (2022) R. Jin et al. Predefined-time control for free-floating space robots in task space J. Franklin Inst. (2021) D. Shang et al. Dynamic modeling and fuzzy compensation sliding mode control for flexible manipulator servo system Appl. Math. Model. (2022) View more references Cited by (0) Recommended articles (6) Research article Weak Galerkin finite element methods for H (curl;Ω) and H (curl,div;Ω)-elliptic problems Computers & Mathematics with Applications, Volume 147, 2023, pp. 210-221 Show abstract Weak Galerkin finite element methods (WG-FEMs) for H ( curl ; Ω ) and H ( curl , div ; Ω ) -elliptic problems are investigated in this paper. The WG method as applied to curl-curl and grad-div problems uses two operators: discrete weak curl and discrete weak divergence, with appropriately defined stabilizations that enforce a weak continuity of the approximating functions. This WG method is highly flexible by allowing the use of discontinuous approximating functions on the arbitrary shape of polyhedra and, at the same time, is parameter-free. The optimal order of convergence is established for the WG approximations in discrete H 1 norm and L 2 norm. In fact, theoretical convergence analysis holds under low regularity requirements of the analytical solution. Results of numerical experiments that corroborate the theoretical results are also presented. Research article Unconditionally convergent and superconvergent analysis of second-order weighted IMEX FEMs for nonlinear Ginzburg-Landau equation Computers & Mathematics with Applications, Volume 146, 2023, pp. 84-105 Show abstract In this paper, the second-order weighted implicit-explicit (IMEX) finite element method (FEM) is presented for the nonlinear Ginzburg-Landau equation (GLE). We mainly focus on a rigorous analysis of unconditionally convergent analysis of the discrete schemes by using time-space error splitting technique combined with the G -stability of weighted IMEX scheme. Subsequently, due to the relationship between the Ritz and interpolation projection operators for the bilinear finite element, we obtain the superclose estimate of the numerical scheme. Then, the global superconvergence is derived by the interpolation postprocessing technique. Furthermore, in order to solve the generalized GLE with logarithmic nonlinearity, a regularized FEM is constructed. At last, several numerical experiments, including the FEMs of the cubic, quintic and logarithmic GLEs, are provided to confirm our theoretical results. Research article An immersed weak Galerkin method for elliptic interface problems on polygonal meshes Computers & Mathematics with Applications, Volume 147, 2023, pp. 185-201 Show abstract In this paper we present an immersed weak Galerkin method for solving second-order elliptic interface problems on polygonal meshes, where the meshes do not need to be aligned with the interface. The discrete space consists of constants on each edge and broken linear polynomials satisfying the interface conditions in each element. For triangular meshes, such broken linear polynomials coincide with the basis functions in immersed finite element methods [33] . We establish some approximation properties of the broken linear polynomials and the discrete weak gradient of a certain projection of the solution on polygonal meshes. We then prove an optimal error estimate of our scheme in the discrete H 1 -seminorm under some assumptions on the exact solution. Numerical experiments are provided to confirm our theoretical analysis. Research article Graphical representation and hierarchical decomposition mechanism for vertex-cover solution space Applied Mathematics and Computation, Volume 458, 2023, Article 128264 Show abstract NP problems act essential roles in modelling and analysing various complex systems, and representation learning of system individuals and relations has faced the kernel difficulty in understanding the complexity and solving the NP problems. In this paper, solution space organisation of minimum vertex-cover problem is deeply investigated using the famous König-Egérvary (KE) graph and theorem, in which a hierarchical decomposition mechanism named KE-layer structure of general graphs is proposed to reveal the complexity of vertex-cover. To achieve the graphical representation and hierarchical decomposition, an algorithm to verify the KE graph is given by the solution space expression of vertex-cover, and the relation between multi-layer KE graphs and maximal matching is illustrated and proved. Furthermore, a framework to calculate the KE-layer number and approximate the minimal vertex-cover is provided, with different strategies of switching nodes and counting energy. The phase transition phenomenon between different KE-layers is studied with the transition points located, and searching of vertex-cover got by this strategy presents comparable advantage against several other methods. Its efficiency outperforms the existing ones just before the transition point. The graphical representation and hierarchical decomposition provide a new perspective to illustrate the structural organisations of graphs better, and its formation mechanism can help reveal the intrinsic complexity and establish heuristic strategy for large-scale graphs/systems recognition. Research article Error estimates of divergence-free generalized moving least squares (Div-Free GMLS) derivatives approximations in Sobolev spaces Applied Numerical Mathematics, Volume 192, 2023, pp. 373-388 Show abstract The divergence-free generalized moving least squares (Div-Free GMLS) approximation has recently been utilized to solve some incompressible fluid flows problems. In our recent work (Mohammadi and Dehghan (2021) [28] ), we have presented its formulation more precisely, and also the error estimates of derivatives have been carried out in L ∞ ( Ω ) , where Ω ⊂ R d is a bounded set satisfying an interior cone condition. However, the error estimates of this vector-valued approximation in Sobolev spaces are not done. So, in this paper we make the error estimates of Div-Free GMLS derivatives approximations in L q ( Ω ) , where 1 ≤ q ≤ ∞ , using a stable local divergence-free polynomial reproduction property. Note that, the method is a direct approximants of exact derivatives of a divergence-free vector field, which possesses the optimal rates of convergence. This vector-valued technique can also be developed to find the numerical solution of the incompressible fluid flows problems easier than the other available mesh-dependent methods. Finally, we have shown how the proposed approximation can recover the velocity field variable of the well-known Darcy's problem in a two-dimensional space. Research article Parallel matrix-free polynomial preconditioners with application to flow simulations in discrete fracture networks Computers & Mathematics with Applications, Volume 146, 2023, pp. 60-70 Show abstract We develop a robust matrix-free, communication avoiding parallel, high-degree polynomial preconditioner for the Conjugate Gradient method for large and sparse symmetric positive definite linear systems. We discuss the selection of a scaling parameter aimed at avoiding unwanted clustering of eigenvalues of the preconditioned matrices at the extrema of the spectrum. We use this preconditioned framework to solve a 3 × 3 block system arising in the simulation of fluid flow in large-size discrete fractured networks. We apply our polynomial preconditioner to a suitable Schur complement related with this system, which can not be explicitly computed because of its size and density. Numerical results confirm the excellent properties of the proposed preconditioner up to very high polynomial degrees. The parallel implementation achieves satisfactory scalability by taking advantage from the reduced number of scalar products and hence of global communications. View full text © 2023 Elsevier Ltd. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2023 Elsevier B.V. or its licensors or contributors. ScienceDirect® is a registered trademark of Elsevier B.V. ScienceDirect® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.camwa.2023.08.003", "PubYear": 2023, "Volume": "148", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sino-French International Joint Laboratory of Automatic Control and Signal Processing (LaFCAS), School of Automation, Nanjing University of Science and Technology, Nanjing 210094, China;School of Electrical Engineering, Nantong University, Nantong 226019, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sino-French International Joint Laboratory of Automatic Control and Signal Processing (LaFCAS), School of Automation, Nanjing University of Science and Technology, Nanjing 210094, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INRIA-Lille Nord Europe, 40 avenue Halley Villeneuve-d'Ascq, Lille, F-59000, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Sino-French International Joint Laboratory of Automatic Control and Signal Processing (LaFCAS), School of Automation, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "INRIA-Lille Nord Europe, 40 avenue Halley Villeneuve-d'Ascq, Lille, F-59000, France"}], "References": [{"Title": "An improved Multiscale FEM for the free vibrations of heterogeneous solids", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "110", "Issue": "", "Page": "110", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 109405361, "Title": "Publisher Correction: Algorithm selection for SMT", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10009-023-00714-1", "PubYear": 2023, "Volume": "25", "Issue": "5-6", "JournalId": 15615, "JournalTitle": "International Journal on Software Tools for Technology Transfer (STTT)", "ISSN": "1433-2779", "EISSN": "1433-2787", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Waterloo, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Stanford, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Stanford, USA"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Waterloo, Canada"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Waterloo, Canada"}], "References": []}, {"ArticleId": 109405406, "Title": "Adversarial Coreset Selection for Efficient Robust Training", "Abstract": "<p>It has been shown that neural networks are vulnerable to adversarial attacks: adding well-crafted, imperceptible perturbations to their input can modify their output. Adversarial training is one of the most effective approaches to training robust models against such attacks. Unfortunately, this method is much slower than vanilla training of neural networks since it needs to construct adversarial examples for the entire training data at every iteration. By leveraging the theory of coreset selection, we show how selecting a small subset of training data provides a principled approach to reducing the time complexity of robust training. To this end, we first provide convergence guarantees for adversarial coreset selection. In particular, we show that the convergence bound is directly related to how well our coresets can approximate the gradient computed over the entire training data. Motivated by our theoretical analysis, we propose using this gradient approximation error as our adversarial coreset selection objective to reduce the training set size effectively. Once built, we run adversarial training over this subset of the training data. Unlike existing methods, our approach can be adapted to a wide variety of training objectives, including TRADES, ( ll _p) -PGD, and Perceptual Adversarial Training. We conduct extensive experiments to demonstrate that our approach speeds up adversarial training by 2–3 times while experiencing a slight degradation in the clean and robust accuracy.</p>", "Keywords": "Adversarial training; Coreset selection; Efficient training; Robust deep learning; Image classification", "DOI": "10.1007/s11263-023-01860-4", "PubYear": 2023, "Volume": "131", "Issue": "12", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Melbourne Connect, Carlton, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Melbourne Connect, Carlton, Australia"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing and Information Systems, The University of Melbourne, Melbourne Connect, Carlton, Australia"}], "References": [{"Title": "Green AI", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "12", "Page": "54", "JournalTitle": "Communications of the ACM"}, {"Title": "A survey on data‐efficient algorithms in big data era", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}]}, {"ArticleId": *********, "Title": "PL-Net: towards deep learning-based localization for underwater terrain", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00521-023-08931-0", "PubYear": 2023, "Volume": "", "Issue": "", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109405411, "Title": "A non-revisiting framework for evolutionary multi-task optimization", "Abstract": "<p>Multi-task optimization is an emerging research topic in evolutionary computation, which aims to solve multiple optimization tasks simultaneously through knowledge transfer. However, existing multi-task evolutionary algorithms suffer from the re-evaluation problem, leading to unnecessary consumption of computing resources. To address this issue, a non-revisiting framework is proposed, which allows the non-revisiting scheme to be aided by historical information during the evolutionary search process. Moreover, an individual updating strategy is designed to improve the search efficiency of the algorithm and enhance the ability to escape local optima. Furthermore, a parallel scheme of the proposed framework is developedNational Frontiers Science Center for Industrial Intelligence and Systems Optimizationcomputation time on the CUDA architecture. To evaluate the effectiveness of the proposed framework, it is integrated with success-history based adaptive differential evolution. A comparative study of the proposed algorithm with eight state-of-the-art multi-task evolutionary algorithms is performed on nine benchmark problems. The experimental results demonstrate that the proposed algorithm outperforms the existing algorithms, highlighting its potential for solving multi-task optimization problems.</p>", "Keywords": "Multi-task optimization; Non-revisiting scheme; CUDA platform", "DOI": "10.1007/s10489-023-04918-5", "PubYear": 2023, "Volume": "53", "Issue": "21", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Software College, Northeastern University, Shenyang, People’s Republic of China; National Frontiers Science Center for Industrial Intelligence and Systems Optimization, Northeastern University, Shenyang, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Data Analytics and Optimization for Smart Industry (Northeastern University), Ministry of Education, Shenyang, People’s Republic of China"}], "References": [{"Title": "A non-revisiting genetic algorithm based on a novel binary space partition tree", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "512", "Issue": "", "Page": "661", "JournalTitle": "Information Sciences"}, {"Title": "Non-revisiting stochastic search revisited: Results, perspectives, and future directions", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "100828", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Multifactorial evolutionary optimization to maximize lifetime of wireless sensor network", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "576", "Issue": "", "Page": "355", "JournalTitle": "Information Sciences"}, {"Title": "Differential evolution with mixed mutation strategy based on deep reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107678", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evolutionary multi-task optimization with hybrid knowledge transfer strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "580", "Issue": "", "Page": "874", "JournalTitle": "Information Sciences"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Golden jackal optimization: A novel nature-inspired optimizer for engineering applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "198", "Issue": "", "Page": "116924", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Sand Cat swarm optimization: a nature-inspired algorithm to solve global optimization problems", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "4", "Page": "2627", "JournalTitle": "Engineering with Computers"}, {"Title": "Differential evolution with hybrid parameters and mutation strategies based on reinforcement learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "75", "Issue": "", "Page": "101194", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "Multitasking optimization via an adaptive solver multitasking evolutionary framework", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "630", "Issue": "", "Page": "688", "JournalTitle": "Information Sciences"}, {"Title": "Adaptive niching particle swarm optimization with local search for multimodal optimization", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "133", "Issue": "", "Page": "109923", "JournalTitle": "Applied Soft Computing"}, {"Title": "Evolutionary competitive multitasking optimization via improved adaptive differential evolution", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "217", "Issue": "", "Page": "119550", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 109405564, "Title": "Metaverse in the tourism sector for talent management: a technology in practice lens", "Abstract": "<p>In an era of talent shortages in the hospitality and tourism sector, the increasing use of technology adoption, including the metaverse, is critical for providing organizations with a competitive advantage. While metaverse adoption is becoming increasingly important for enabling customer experience in tourism, there is a surprising dearth of research on applications in the learning and development of the workforce engaged in this sector. The paper examines how hospitality and tourism HRM practices leveraging the metaverse can meaningfully increase learning engagement with the distributed workforce. Using a qualitative case study research design, we draw on practice theory and attempt to address the changing structures, practices, norms, and interpretive schemes while using the metaverse for learning and development within organizations. The study finds that the metaverse serves as augmenting technology or assistive technology, and its use with partial or wholly immersive environments enables asynchronous and synchronous learning. The implications of the study are discussed.</p>", "Keywords": "Metaverse; Learning & development; Practice theory; Human–machine agency", "DOI": "10.1007/s40558-023-00258-9", "PubYear": 2023, "Volume": "25", "Issue": "3", "JournalId": 6239, "JournalTitle": "Information Technology & Tourism", "ISSN": "1098-3058", "EISSN": "1943-4294", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Management Bangalore, Bangalore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MakeMyTrip-GoIbibo, Gurgaon, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Institute of Technology, Srinagar, India"}], "References": [{"Title": "Technological metaworlds in travel", "Authors": "<PERSON><PERSON> <PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "273", "JournalTitle": "Information Technology & Tourism"}, {"Title": "The impact of automation on tourism and hospitality jobs", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "205", "JournalTitle": "Information Technology & Tourism"}, {"Title": "e-Tourism beyond COVID-19: a call for transformative research", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "187", "JournalTitle": "Information Technology & Tourism"}, {"Title": "Does virtual reality attract visitors? The mediating effect of presence on consumer response in virtual reality tourism advertising", "Authors": "<PERSON><PERSON>; <PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "4", "Page": "537", "JournalTitle": "Information Technology & Tourism"}, {"Title": "Immersive technologies for tourism: a systematic review", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "181", "JournalTitle": "Information Technology & Tourism"}, {"Title": "Human centric platforms for personalized value creation in metaverse", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "65", "Issue": "", "Page": "653", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 109405787, "Title": "A multi-period fuzzy portfolio optimization model with investors’ loss aversion", "Abstract": "<p>This paper considers the problem of how to construct the optimal multi-period portfolio for investors with loss aversion in fuzzy environment. Firstly, we regard the return rates of the risky assets as fuzzy numbers and use the value function in prospect theory to transform the return rate of a portfolio into perceived value, which can reflect investors’ loss aversion. Moreover, due to the fact that investors’ perception level toward risk may vary with the loss aversion degree, we propose a new risk measure based on the perceived value. Then, we formulate the objectives of maximizing the cumulative expected perceived value and minimizing the cumulative perceived risk and propose a multi-period portfolio selection model with diversification constraint. Furthermore, to solve the proposed model, we design a multiple particle swarm optimization algorithm with respect to its specific situation. Finally, using the data from real financial market, we construct a real case to illustrate the effectiveness of the model and algorithm. The results show that loss aversion has an important effect on investors’ investment decisions, and the proposed model could provide more reasonable strategies for investors with different loss aversion degrees.</p>", "Keywords": "Fuzzy portfolio selection; Loss aversion; Prospect theory; Multiple particle swarm optimization", "DOI": "10.1007/s00500-023-09030-x", "PubYear": 2023, "Volume": "27", "Issue": "24", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Economics, Guangdong University of Technology, Guangzhou, China"}], "References": [{"Title": "A multi-period fuzzy mean-minimax risk portfolio model with investor’s risk attitude", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "4", "Page": "2949", "JournalTitle": "Soft Computing"}, {"Title": "An improved QPSO algorithm and its application in fuzzy portfolio model with constraints", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "12", "Page": "7695", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 109405934, "Title": "Robust watermark based on Schur decomposition and dynamic weighting factors", "Abstract": "Since multimedia data are now more widely distributed in digital form and easier to copy and change, copyright protection has become an essential need. One of the most common copyright protection techniques is watermarking. Invisibility and robustness are crucial aspects of watermarking; however, many of the currently used techniques simply focus more on invisibility than how robust they are. Moreover, the trade-off between invisibility and robustness is challenging. To this end, this paper proposes a novel watermark technique that efficiently overcomes the idea of a trade-off between robustness and invisibility, thereby increasing both under most attacks. Schur decomposition and a dynamic weighting factors matrix are added to the embedding process to improve the robustness of the proposed technique. Besides that, the embedding function is improved to simultaneously maximize imperceptibility and robustness. Another key contribution of the proposed approach is its use of a trajectory-based optimization algorithm rather than the more prevalent population-based algorithms to determine the optimal scaling factors. Consequently, the proposed technique rapidly identifies the best scaling factors for the embedding function. Statistical analysis is performed using the Friedman test. Experimental results show that the proposed technique outperforms other existing techniques for different sizes, shapes, and types of watermarks.", "Keywords": "Image watermarking; Discrete wavelet transform (DWT); Schur decomposition; Singular value decomposition (SVD); Pattern search optimization (PS); <PERSON>’s test", "DOI": "10.1007/s00371-023-03028-0", "PubYear": 2024, "Volume": "40", "Issue": "5", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Engineering Mathematics and Physics Department, Faculty of Engineering, Alexandria University, Alexandria, Egypt; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Mathematics and Physics Department, Faculty of Engineering, Alexandria University, Alexandria, Egypt"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Engineering Mathematics and Physics Department, Faculty of Engineering, Alexandria University, Alexandria, Egypt"}], "References": [{"Title": "Fractional discrete Tchebyshev moments and their applications in image encryption and watermarking", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "516", "Issue": "", "Page": "545", "JournalTitle": "Information Sciences"}, {"Title": "An intelligent and blind image watermarking scheme based on hybrid SVD transforms using human visual system characteristics", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "2", "Page": "385", "JournalTitle": "The Visual Computer"}, {"Title": "An intelligent and blind dual color image watermarking for authentication and copyright protection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1701", "JournalTitle": "Applied Intelligence"}, {"Title": "Implementation of Secured and Robust DFT-Based Image Watermark Through Hybridization with Decomposition Algorithm", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "3", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Domain-flexible selective image encryption based on genetic operations and chaotic maps", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "39", "Issue": "3", "Page": "1057", "JournalTitle": "The Visual Computer"}]}, {"ArticleId": 109405965, "Title": "A pilot study to estimate the Pediatric radiation dose during thyroid, renal, and bone scan", "Abstract": "Pediatric patients benefited from nuclear medicine (NM) results. NM provides information regarding the general and localized function of the body's organs, systems, and lesions. This includes applications in the bone, neuro, renal, cardiovascular, endocrine, and other organ systems, as well as in detecting and managing cancer. The administration of radiopharmaceuticals associated with exposure to ionizing radiation is a necessary component of NM practice. This study aims to estimate the Pediatric radiation dose during thyroid, renal, and bone scans. Methods The study involved 161 pediatric patients who underwent thyroid, renal, and bone scans using Tc-99m radionuclide. Effective dose has been estimated using web-based software depending on conductive activity. Effective dose has been estimated using computer software depending on conductive activity. The results show patient dose information from thyroid, renal, and bone scans. For a thyroid scan, the patient's age was 12.26 ± 3.65 years, the patient's height was 1.43 ± 0.20 cm, and the patient's weight was 41.87 ± 16.56 kg. The activity was 2.89 ± 1.12 mCi, and the effective dose for thyroid was 1.92 ± 0.81 mSv. For the renal scan, the patient's age was 6.55 ± 4.83 years, height was 1.09 ± 0.37 cm, and weight was 24.01 ± 18.19 kg. The activity was 2.41 ± 0.88 mCi, and the effective dose was 0.72 ± 0.19 mSv. for a bone scan patient's age was 11 ± 4.12 years, the patient's height was 1.4 ± 0.19 cm, the patient's 33.64 ± 14.3 kg. the activity 11.46 ± 3.64 mCi and the effective dose 3.1 ± 0.61 mSv. Implementing national guidelines must improve the practice and patient safety, and the relationship between administered activity and patient weight needs to be better understood.", "Keywords": "Nuclear medicine ; Activity ; Effective dose ; Pediatric", "DOI": "10.1016/j.jrras.2023.100646", "PubYear": 2023, "Volume": "16", "Issue": "4", "JournalId": 7613, "JournalTitle": "Journal of Radiation Research and Applied Sciences", "ISSN": "1687-8507", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Hu<PERSON> <PERSON>", "Affiliation": "Department of Radiological Sciences, College of Health and Rehabilitation Sciences, Princess <PERSON><PERSON><PERSON>rahman University, P.0 .84428, Riyadh, 11671, Saudi Arabia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Diagnostic Radiology Department, College of Medical Radiological Science, Sudan University of Science and Technology, Khartoum, Sudan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Medical Diagnostic Imaging, College of Health Sciences, University of Sharjah, Sharjah, United Arab Emirates"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Diagnostic Radiology Department, College of Medical Radiological Science, Sudan University of Science and Technology, Khartoum, Sudan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Radiological Sciences, College of Health and Rehabilitation Sciences, Princess <PERSON><PERSON><PERSON>rahman University, P.0 .84428, Riyadh, 11671, Saudi Arabia;Corresponding author"}], "References": []}, {"ArticleId": 109406087, "Title": "Backpropagation-free 4D continuous ant-based neural topology search", "Abstract": "Continuous Ant-based Topology Search (CANTS) is a previously introduced novel nature-inspired neural architecture search (NAS) algorithm that is based on ant colony optimization (ACO). CANTS utilizes a continuous search space to indirectly-encode a neural architecture search space. Synthetic ant agents explore CANTS’ continuous search space based on the density and distribution of pheromones, strongly inspired by how ants move in the real world. This continuous search space allows CANTS to automate the design of artificial neural networks (ANNs) of any size, removing a key limitation inherent to many current NAS algorithms that must operate within structures of a size that is predetermined by the user. This work expands CANTS by adding a fourth dimension to its search space representing potential neural synaptic weights . Adding this extra dimension allows CANTS agents to optimize both the architecture as well as the weights of an ANN without applying backpropagation (BP), which leads to a significant reduction in the time consumed in the optimization process: at least an average of 96% less time consumption with very competitive optimization performance , if not better. The experiments of this study – using real-world data – demonstrate that the BP-Free CANTS algorithm exhibits highly competitive performance compared to both CANTS and ANTS while requiring significantly less operation time.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110737", "PubYear": 2023, "Volume": "147", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of North Carolina Wilmington, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, University of North Carolina Wilmington, United States of America"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Software Engineering, Rochester Institute of Technology, United States of America"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, Rochester Institute of Technology, United States of America"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, Rochester Institute of Technology, United States of America"}], "References": [{"Title": "A survey of swarm and evolutionary computing approaches for deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "3", "Page": "1767", "JournalTitle": "Artificial Intelligence Review"}]}, {"ArticleId": 109406110, "Title": "Multi-colorful ratio fluorescent biosensing of β-glucosidase activity based on the rapidly synthetic gold nanoclusters", "Abstract": "Given the undeniable importance of robust analysis of β-glucosidase (β-GLU) activity in clinical diagnosis and therapeutics applications, in this work, a multi-colorful ratio fluorescent assay for monitoring β-GLU activity by applying gold nanoclusters (AuNCs) as the probes was proposed. The AuNCs were prepared via a convenient chemical reduction procedure within 15 min at 70 °C. In the catalytic system of enzymatic substrate 3-indoxyl-β- D -glucopyranoside (IDG) and β-GLU, the generations of hydrogen peroxide (H<sub>2</sub>O<sub>2</sub> ) and fluorescent indoxyl oxidation product (op-ID) were demonstrated. The generated op-ID exhibited a fluorescence emission peak at 478 nm, and H <sub>2</sub>O<sub>2</sub> quenched the fluorescence of AuNCs at 645 nm, forming a typical ratio fluorescence and multiple fluorescence color change responses to β-GLU. The ratio sensing platform determined β-GLU activity with a limit of detection (LOD) as low as 0.051 U/L. Further, β-GLU activity could also be detected in real human serum samples using the prepared biosensor. This sensing strategy was also used to construct a simple and low-cost hydrogel kit for portable monitoring of β-GLU activity, holding great potential for point-of-care testing of β-GLU-related clinical investigation.", "Keywords": "", "DOI": "10.1016/j.snb.2023.134460", "PubYear": 2023, "Volume": "394", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Medical Engineering & the Key Laboratory for Medical Functional Nanomaterials, Jining Medical University, Jining 272067, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Shi", "Affiliation": "College of Medical Engineering & the Key Laboratory for Medical Functional Nanomaterials, Jining Medical University, Jining 272067, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Chemistry and Chemical Engineering, Liaoning Normal University, Dalian 116029, PR China"}, {"AuthorId": 4, "Name": "Yaqing Han", "Affiliation": "College of Medical Engineering & the Key Laboratory for Medical Functional Nanomaterials, Jining Medical University, Jining 272067, PR China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Medical Engineering & the Key Laboratory for Medical Functional Nanomaterials, Jining Medical University, Jining 272067, PR China"}, {"AuthorId": 6, "Name": "Fu Ren", "Affiliation": "Key Laboratory of Human Ethnic Specificity and Phenomics of Critical Illness in Liaoning Province, Shenyang Medical College, Shenyang 110034, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Medical Engineering & the Key Laboratory for Medical Functional Nanomaterials, Jining Medical University, Jining 272067, PR China;Corresponding author"}], "References": [{"Title": "AuPt nanocrystals/polydopamine supported on open-pored hollow carbon nanospheres for a dual-signaling electrochemical ratiometric immunosensor towards h-FABP detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "346", "Issue": "", "Page": "130501", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 109406112, "Title": "Detection and ISI mitigation in mobile molecular communication system for targeted drug delivery", "Abstract": "This paper proposes a targeted drug delivery system based on mobile molecular communication (MMC). The system consists of a mobile transmitter and a mobile reactive receiver. The transmitter can sense the required drug concentration and send commands to the receiver for drug delivery in the extracellular fluid (ECF). The commands are sent in the form of bits and the received signal is prone to noise and inter-symbol interference (ISI). Hence, at the receiver, two detection techniques, differential amplitude detector (DAD) and differential energy detector (DED) with ISI mitigation are proposed for MMC. Manchester-coded bits are transmitted using modified concentration shift keying (MCSK). In the proposed detection mechanism, an adaptive threshold technique is used for estimating the number of signaling molecules using the maximum a posteriori probability (MAP) rule. Further in each bit interval, dynamic distance estimation, signal reconstruction, and ISI mitigation are performed. Particle-based simulation for reactive receiver is also carried out to validate the results. A low bit error rate (BER) in the MMC system signifies the promising performance of the drug delivery system.", "Keywords": "", "DOI": "10.1016/j.nancom.2023.100476", "PubYear": 2023, "Volume": "38", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, KL University Hyderabad, Telangana, 500075, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, IIIT Naya Raipur, Chhattisgarh, 493661, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, IIIT Naya Raipur, Chhattisgarh, 493661, India"}], "References": []}, {"ArticleId": 109406118, "Title": "An optimized equation based on the gene expression programming method for estimating tunnel construction costs considering a variety of variables and indexes", "Abstract": "Accurate cost estimation in tunneling is key to the project’s success. Such information is critical for the early conceptual and design phases when key choices must be made. Numerous variables influence the cost of tunnel construction, and limited information is available at the early stages of design when the possible use of tunnels is being studied. Therefore, there is a limited number of models at engineers’ disposal to develop a proper cost estimate for tunneling projects. This study aimed to offer a model for estimating the construction cost of drilling and blasting tunnels in the preliminary stage of a project. For this purpose, an optimized gene expression programming (GEP) method was used based on the study of 900 data points obtained from ten drilling and blasting tunnels, which were randomly split into the training (800 data points) and testing (100 data points) datasets. With the experience of previously constructed tunnels, eleven parameters were considered the most effective for the tunnel’s construction cost. The best fit on predictions generated an equation for the optimized GEP model. Finally, by comparing the equation’s outputs with the actual costs and its behavior with practice, its potential ability to estimate the construction cost of drilling and blasting tunnels was approved. Moreover, the Graphical User Interface (GUI) of the GEP model was created as a practical tool for estimating the construction cost of tunnels. This model can reduce tunnel uncertainties and give ML development in tunnel planning.", "Keywords": "", "DOI": "10.1016/j.asoc.2023.110749", "PubYear": 2023, "Volume": "147", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Rock Mechanics Division, School of Engineering, Tarbiat Modares University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Rock Mechanics Division, School of Engineering, Tarbiat Modares University, Tehran, Iran;Corresponding author"}], "References": [{"Title": "Forecasting tunnel geology, construction time and costs using machine learning methods", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "1", "Page": "321", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 109406122, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S1878-7789(23)00038-8", "PubYear": 2023, "Volume": "37", "Issue": "", "JournalId": 6324, "JournalTitle": "Nano Communication Networks", "ISSN": "1878-7789", "EISSN": "1878-7797", "Authors": [], "References": []}, {"ArticleId": 109406141, "Title": "Activity–weight duality in feed-forward neural networks reveals two co-determinants for generalization", "Abstract": "Generalization is a fundamental problem in machine learning. For overparameterized deep neural network models, there are many solutions that can fit the training data equally well. The key question is which solution has a better generalization performance measured by test loss (error). Here we report the discovery of exact duality relations between changes in activities and changes in weights in any fully connected layer in feed-forward neural networks. By using the activity–weight duality relation, we decompose the generalization loss into contributions from different directions in weight space. Our analysis reveals that two key factors, sharpness of the loss landscape and size of the solution, act together to determine generalization. In general, flatter and smaller solutions have better generalization. By using the generalization loss decomposition, we show how existing learning algorithms and regularization schemes affect generalization by controlling one or both factors. Furthermore, by applying our analysis framework to evaluate different algorithms for realistic large neural network models in the multi-learner setting, we find that the decentralized algorithms have better generalization performance as they introduce additional landscape-dependent noise that leads to flatter solutions without changing their sizes. A challenging problem in deep learning consists in developing theoretical frameworks suitable to study generalization. <PERSON> and colleagues uncover a duality relation between neuron activities and weights in deep learning neural networks, and use it to show that sharpness of the loss landscape and norm of the solution act together in determining its generalization performance.", "Keywords": "Statistical physics;Statistics;Engineering;general", "DOI": "10.1038/s42256-023-00700-x", "PubYear": 2023, "Volume": "5", "Issue": "8", "JournalId": 60458, "JournalTitle": "Nature Machine Intelligence", "ISSN": "", "EISSN": "2522-5839", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "IBM T. J. Watson Research Center, Yorktown Heights, USA; Department of Physics, Duke University, Durham, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IBM T. J. Watson Research Center, Yorktown Heights, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IBM T. J. Watson Research Center, Yorktown Heights, USA"}], "References": []}, {"ArticleId": 109406233, "Title": "Lightweight symmetric key encryption for text using XOR operation and permutation matrix", "Abstract": "<p>Traditionally, symmetric key encryption has been computationally intensive, and the balance between security and computation costs has necessitated the use of systems with simpler computations and variable-sized keys. This paper introduces a lightweight technique for symmetric key encryption of text, which utilizes less complex operations such as XOR and permutation matrices. The proposed method encrypts and decrypts text based on the ASCII values of plain text characters, using two symmetric keys to provide security. The use of lightweight and computationally efficient operations like XOR and permutation matrices ensures efficient encryption. One of the advantages of this approach is that it allows for the use of any key size without increasing computational complexity, while also enhancing security. Our statistical findings demonstrate that while increasing input size increases computation complexity, increasing key size does not affect it. Thus, any key size can be used for greater security. In summary, our proposed technique provides a simple, lightweight, and efficient method for data encryption and decryption with enhanced security using variable-sized keys.</p>", "Keywords": "Encryption; Decryption; ASCII; Symmetric encryption; Plain text; Cipher text; XOR matrix; Permutation matrix; Variable-sized key", "DOI": "10.1007/s41870-023-01407-3", "PubYear": 2023, "Volume": "15", "Issue": "7", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Uttarakhand, Srinagar, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Uttarakhand, Srinagar, India"}], "References": []}, {"ArticleId": 109406243, "Title": "学会から", "Abstract": "", "Keywords": "", "DOI": "10.3156/jsoft.35.1_14", "PubYear": 2023, "Volume": "35", "Issue": "1", "JournalId": 16268, "JournalTitle": "Journal of Japan Society for Fuzzy Theory and Intelligent Informatics", "ISSN": "1347-7986", "EISSN": "1881-7203", "Authors": [], "References": []}, {"ArticleId": 109406268, "Title": "Facial expression recognition via transfer learning in cooperative game paradigms for enhanced social AI", "Abstract": "<p>Facial Expression Recognition (FER) is an effortless task for humans, and such non-verbal communication is intricately related to how we relate to others beyond the explicit content of our speech. Facial expressions can convey how we are feeling, as well as our intentions, and are thus a key point in multimodal social interactions. Recent computational advances, such as promising results from Convolutional Neural Networks (CNN), have drawn increasing attention to the potential of FER to enhance human–agent interaction (HAI) and human–robot interaction (HRI), but questions remain as to how “transferrable” the learned knowledge is from one task environment to another. In this paper, we explore how FER can be deployed in HAI cooperative game paradigms, where a human subject interacts with a virtual avatar in a goal-oriented environment where they must cooperate to survive. The primary question was whether transfer learning (TL) would offer an advantage for FER over pre-trained models based on similar (but the not exact same) task environment. The final results showed that TL was able to achieve significantly improved results (94.3% accuracy), without the need for an extensive task-specific corpus. We discuss how such approaches could be used to flexibly create more life-like robots and avatars, capable of fluid social interactions within cooperative multimodal environments.</p>", "Keywords": "Facial expression recognition; Emotion detection; Human–robot interaction; Computer vision; Transfer learning; Social intelligence", "DOI": "10.1007/s12193-023-00410-z", "PubYear": 2023, "Volume": "17", "Issue": "3", "JournalId": 28285, "JournalTitle": "Journal on Multimodal User Interfaces", "ISSN": "1783-7677", "EISSN": "1783-8738", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Universidad Carlos III de Madrid, Madrid, Spain; Department of Intelligence Computing, Hanyang University, Seoul, Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Intelligence Computing, Hanyang University, Seoul, Korea; Department of Computing and Digital Media, DePaul University, Chicago, USA"}], "References": [{"Title": "Facial emotion recognition using deep learning: review and insights", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "175", "Issue": "", "Page": "689", "JournalTitle": "Procedia Computer Science"}, {"Title": "FER-net: facial expression recognition using deep neural net", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "15", "Page": "9125", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Deep Learning: A Comprehensive Overview on Techniques, Taxonomy, Applications and Research Directions", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "6", "Page": "420", "JournalTitle": "SN Computer Science"}, {"Title": "Performance enhancement of facial electromyogram-based facial-expression recognition for social virtual reality applications using linear discriminant analysis adaptation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "1", "Page": "385", "JournalTitle": "Virtual Reality"}]}, {"ArticleId": *********, "Title": "Development of tablet defect detection model using biaxial planes discrete scanning algorithm", "Abstract": "<p>Oral administration is the most broadly used dosage form for drug delivery in a disease treatment. However, the tablet imperfections known as visual defects may significantly impact the efficacy and therapeutic effect. Accordingly, the tablet defect identified before bottling is a crucial issue in the pharmaceutical industry. Presently, the defect checking usually relies on either manual sampling, visual image processing or combination of deep learning methods in the manufactory. Nevertheless, currently it still lacks both simple and effective mechanism to tackle this problem in industry. For this reason, the tablet defect detection model is developed using a biaxial planes discrete scanning algorithm based on simple algebraic calculation. Therefore, actual tablet shape can be quickly identified, and the defect status can be thus evaluated accurately. Initially, the standard tablet shape is formed using biaxial planes discrete scanning process from both front and side views, respectively. Second, analog output signal generated from the image sensor is transformed into a series digital code in an array. Third, every tablet for investigation is processed following up the same steps as the standard tablet does. Finally, the defect status is determined using similarity gap and square mean error (SME) between the standard tablet and detected one. The experimental results reveal that the defect tablet has a significantly big similarity gap over the standard one up to 20% and 100% in front and side views, respectively. On the other hand, the square mean error (SME) reaches 7903 and 5393 in front and side views, respectively. It verifies that the proposed model can effectively justify the defect status in term of rapidness, accuracy, and robustness.</p>", "Keywords": "Tablet; Discrete scanning; Defect; Image processing", "DOI": "10.1007/s00170-023-12126-1", "PubYear": 2023, "Volume": "128", "Issue": "7-8", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Chin-Yi University of Technology, Taichung, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronic Engineering, National Chin-Yi University of Technology, Taichung, Taiwan"}], "References": [{"Title": "Internal crack detection of castings: a study based on relief algorithm and Adaboost-SVM", "Authors": "Cheng Jin; Xianguang Kong; Jiantao Chang", "PubYear": 2020, "Volume": "108", "Issue": "9-10", "Page": "3313", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Surface Quality Automatic Inspection for Pharmaceutical Capsules Using Deep Learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "2022", "Issue": "", "Page": "1", "JournalTitle": "Journal of Sensors"}]}, {"ArticleId": 109406568, "Title": "Weight initialization algorithm for physics-informed neural networks using finite differences", "Abstract": "<p>With physics-informed neural networks (PINNs), inverse problems involving differential equations can be solved despite noise, sparsity, and varying levels of fidelity. The objective of PINNs is to minimize a loss function using all available information, including physical laws, initial conditions, boundary conditions, and observed quantities. This is accomplished by turning the original problem into a problem of optimization based on the available data. Our paper presents a method for initializing weights in PINNs using finite differences. We consider two neural networks, feed-forward neural network (FNN) (u_1) and PINN (u_2) with the same architecture. First, we construct a dataset and fit FNN (u_1) to it. To generate our dataset, we use an approximate solution of the partial differential equation using finite differences. In our PINN, the initial weights are determined by the weights obtained from fitting FNN (u_1) to the constructed dataset. The use of coarse mesh resolutions has proven to be viable as well. In contrast to the vanilla PINN training strategy, the proposed weight initialization strategy reduces PINN solution errors and training loss. We also propose a method named Progressive Unfreezing (PU) to reduce the computational cost of training our model. PU gradually fine-tunes the layers of the PINN (u_2) while keeping already fine-tuned layers frozen. This allows the model to leverage pre-trained knowledge and adapt to new task-specific data while reducing computational cost. We solve the one-dimensional diffusion (heat) equation, Fisher equation also known as the <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON> non-linear partial differential equation.</p>", "Keywords": "Finite difference; Deep learning; Physics-informed neural networks; Neural network weights; Scientific machine learning", "DOI": "10.1007/s00366-023-01883-y", "PubYear": 2024, "Volume": "40", "Issue": "3", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Institute for Advanced Studies in Basic Sciences (IASBS), Zanjan, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Institute for Advanced Studies in Basic Sciences (IASBS), Zanjan, Iran; Corresponding author."}], "References": [{"Title": "Scientific Machine Learning Through Physics–Informed Neural Networks: Where we are and What’s Next", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "92", "Issue": "3", "Page": "1", "JournalTitle": "Journal of Scientific Computing"}]}, {"ArticleId": 109406588, "Title": "About the Method for Constructing External Estimates of the Limit Controllability Set for the Linear Discrete-Time System with Bounded Control", "Abstract": "<p>This article discusses the problem of constructing an external estimate of the limit set of controllability for a linear discrete system with convex control constraints. We have proposed a decomposition method that allows us to reduce the problem for the initial system to subsystems of smaller dimension by switching to the normal Jordan basis of the matrix of the system. The statement about the structure of the reference hyperplane to the limit set of controllability is formulated and proved. A method for constructing an external estimate of the limit set of controllability with an arbitrary order of accuracy in the sense of the Hausdorff distance is proposed based on the principle of contraction mappings. The paper provides examples.</p>", "Keywords": "discrete control system; limit set of controllability; reference half-space; principle of contraction mappings; convex set; polyhedral approximation", "DOI": "10.1134/S0005117923020030", "PubYear": 2023, "Volume": "84", "Issue": "2", "JournalId": 9249, "JournalTitle": "Automation and Remote Control", "ISSN": "0005-1179", "EISSN": "1608-3032", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Moscow Aviation Institute (National Research University), Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Moscow Aviation Institute (National Research University), Moscow, Russia"}], "References": []}, {"ArticleId": 109406695, "Title": "Graph Neural Network Operators: a Review", "Abstract": "<p>Graph Neural Networks (GNN) is one of the promising machine learning areas in solving real world problems such as social networks, recommender systems, computer vision and pattern recognition. One of the important component of GNN is GNN operators which are responsible to train GNN graph structured data and forward learning nodes information to other layers. This review focus on recent advancements of GNN operators in detail. The rich Mathematical nature of GNN operators has been discussed for selected GNN operators. The review also highlights different benchmark graph structured datasets and presents results using different GNN operators. We have included thorough discussion for state-of-the-art in this field including limitations and future directions. Overall, the review covers important areas of GNN as GNN operators from future research directions point of view and real world applications perspective.</p>", "Keywords": "Graph neural networks; GNN convolutional operators; Deep learning; Graph structural representation", "DOI": "10.1007/s11042-023-16440-4", "PubYear": 2024, "Volume": "83", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, Panjab University, Chandigarh, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, D.M.College, Moga, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, Panjab University, Chandigarh, India"}], "References": [{"Title": "Hypergraph convolution and hypergraph attention", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107637", "JournalTitle": "Pattern Recognition"}, {"Title": "Graph Representation Learning", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "14", "Issue": "3", "Page": "1", "JournalTitle": "Synthesis Lectures on Artificial Intelligence and Machine Learning"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Graph neural networks: A review of methods and applications", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "", "Page": "57", "JournalTitle": "AI Open"}, {"Title": "Graph Kernels: A Survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "72", "Issue": "", "Page": "943", "JournalTitle": "Journal of Artificial Intelligence Research"}, {"Title": "Data Augmentation for Deep Graph Learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "61", "JournalTitle": "ACM SIGKDD Explorations Newsletter"}, {"Title": "Learning Weight Signed Network Embedding with Graph Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "1", "Page": "36", "JournalTitle": "Data Science and Engineering"}, {"Title": "Learning Weight Signed Network Embedding with Graph Neural Networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "8", "Issue": "1", "Page": "36", "JournalTitle": "Data Science and Engineering"}]}, {"ArticleId": 109406717, "Title": "Clock tree generation by abutment in synchoros VLSI design", "Abstract": "Synchoros VLSI design style has been proposed as an alternative to standard cell-based design. Standard cells are replaced by synchoros, large grain, VLSI design objects called SiLago (Silicon Lego) blocks. This new design style eliminates the need to synthesise ad hoc wires of any type: functional and infrastructural. SiLago blocks are organised into region instances. In a region instance, communication amongst SiLago blocks is synchronous and happens over a regional network on chip (NoC), whose fragments are also absorbed into SiLago blocks. Consequently, the regional NoCs get created by the abutment of SiLago blocks. The clock tree used in a region is called a regional clock tree (RCT). The synchoros VLSI design style requires that the RCT, like the regional NoCs, is also created by abutting its fragments. The RCT fragments are absorbed within the SiLago blocks. The RCT created by the abutment is not an ad-hoc clock tree but a structured and predictable design with known cost metrics. The design of such an RCT is the focus of this paper. The scheme is scalable, and we demonstrate that the proposed RCT can be generated for valid VLSI designs of ∼1.5 million gates. The RCT created by abutment is correct by construction, and its properties are predictable. Additionally, we present an in-depth description of the method used to find the optimal configuration for the proposed design. We have validated the generated RCTs with static timing analysis to validate the correct-by-construction claim. Finally, we show that the cost metrics of the SiLago RCT is comparable to the one generated by commercial EDA tools.", "Keywords": "CTS ; EDA ; SiLago; VLSI design ; Synchoricity ; ASIC application specific integrated circuit ; CV constraints verification ; DiMArch distributed memory architecture ; DRRA dynamically reconfigurable resource array ; EDA electronic design automation ; FV functional verification ; GCT global clock tree ; LCT local clock tree ; NoC network on chip ; OCV on-chip variations ; PFWS prefabricated wall segments ; PVT process; voltage and temperature ; PDL programmable delay line ; RCT regional clock tree ; STA static timing analysis ; SOC systems on chip ; VLSI very large scale integration", "DOI": "10.1016/j.micpro.2023.104913", "PubYear": 2023, "Volume": "102", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "KTH Royal Institute of Technology, Stockholm, Sweden;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "KTH Royal Institute of Technology, Stockholm, Sweden"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "KTH Royal Institute of Technology, Stockholm, Sweden"}], "References": []}, {"ArticleId": 109406750, "Title": "An indirect multicriteria group decision-making method with heterogeneous preference relations and reliabilities of decision-makers", "Abstract": "This study proposes a new indirect method to solve multicriteria group decision-making (MCGDM) problems with heterogeneous preference relations and decision-makers’ reliabilities. To unify the heterogeneous preference relations, the transformation of multiplicative preference relations (MPRs), fuzzy preference relations (FPRs), intuitionistic fuzzy preference relations (IFPRs), and linguistic preference relations (LPRs) into distributed preference relations (DPRs) is analyzed. As consistency among DPRs is constructed under the assumption that decision-makers are bounded rationality, the abstract transformation of MPR, FPR, IFPR, and LPR into DPR is defined, and its reality depends on the preferences of decision-makers. Two important properties of abstract transformation, namely, internal consistency and restricted max-max transitivity, were theoretically verified. The reliabilities of decision-makers are involved in MCGDM by estimating the reliability of each criterion to improve solution quality. Based on the four types of abstract transformations, a process for analyzing MCGDM problems with heterogeneous preference relations and decision-makers’ reliabilities is developed. The proposed method is used to analyze a new business selection problem for an enterprise located in Changzhou, Jiangsu, China to demonstrate its applicability and validity.", "Keywords": "", "DOI": "10.1016/j.ins.2023.119492", "PubYear": 2023, "Volume": "648", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Box 270, Hefei 230009, Anhui, PR China;Key Laboratory of Process Optimization and Intelligent Decision-making, Ministry of Education, Hefei 230009, Anhui, PR China;Ministry of Education Engineering Research Center for Intelligent Decision-Making & Information System Technologies, Hefei 230009, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Box 270, Hefei 230009, Anhui, PR China;Key Laboratory of Process Optimization and Intelligent Decision-making, Ministry of Education, Hefei 230009, Anhui, PR China;Ministry of Education Engineering Research Center for Intelligent Decision-Making & Information System Technologies, Hefei 230009, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Box 270, Hefei 230009, Anhui, PR China;Key Laboratory of Process Optimization and Intelligent Decision-making, Ministry of Education, Hefei 230009, Anhui, PR China;Ministry of Education Engineering Research Center for Intelligent Decision-Making & Information System Technologies, Hefei 230009, PR China;Corresponding author at: School of Management, Hefei University of Technology, Hefei, Box 270, Hefei 230009, Anhui, PR China"}], "References": [{"Title": "Comparison of Evidential Reasoning Algorithm with Linear Combination in Decision Making", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "2", "Page": "686", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "Group decision making with heterogeneous intuitionistic fuzzy preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "523", "Issue": "", "Page": "197", "JournalTitle": "Information Sciences"}, {"Title": "An evidential reasoning approach based on risk attitude and criterion reliability", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "199", "Issue": "", "Page": "105947", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel method to derive the intuitionistic fuzzy priority vectors from intuitionistic fuzzy preference relations", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "25", "Issue": "1", "Page": "147", "JournalTitle": "Soft Computing"}, {"Title": "Comprehensive minimum cost models for large scale group decision making with consistent fuzzy preference relations", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "215", "Issue": "", "Page": "106780", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A geometrical method for consensus building in GDM with incomplete heterogeneous preference information", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "107224", "JournalTitle": "Applied Soft Computing"}, {"Title": "Additive consistency exploration of linguistic preference relations with self-confidence", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "56", "Issue": "1", "Page": "257", "JournalTitle": "Artificial Intelligence Review"}, {"Title": "A method for improving the multiplicative inconsistency based on indeterminacy of an intuitionistic fuzzy preference relation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "602", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}, {"Title": "Selection of a solar water heater for large-scale group decision making with hesitant fuzzy linguistic preference relations based on the best-worst method", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "53", "Issue": "4", "Page": "4462", "JournalTitle": "Applied Intelligence"}, {"Title": "Managing uncertain preferences of consumers in product ranking by probabilistic linguistic preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "262", "Issue": "", "Page": "110240", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Unit consensus cost-based approach for group decision-making with incomplete probabilistic linguistic preference relations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "624", "Issue": "", "Page": "849", "JournalTitle": "Information Sciences"}, {"Title": "Attribute reductions of quantitative dominance-based neighborhood rough sets with A-stochastic transitivity of fuzzy preference relations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "134", "Issue": "", "Page": "109994", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 109406771, "Title": "Robotics in Food Manufacturing Industry in the Industry 4.0 Era", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2023.v12i08.009", "PubYear": 2023, "Volume": "12", "Issue": "8", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 109406917, "Title": "Deep Learning with a Novel Concoction Loss Function for Identification of Ophthalmic Disease", "Abstract": "As ocular computer-aided diagnostic (CAD) tools become more widely accessible, many researchers are developing deep learning (DL) methods to aid in ocular disease (OHD) diagnosis. Common eye diseases like cataracts (CATR), glaucoma (GLU), and age-related macular degeneration (AMD) are the focus of this study, which uses DL to examine their identification. Data imbalance and outliers are widespread in fundus images, which can make it difficult to apply many DL algorithms to accomplish this analytical assignment. The creation of efficient and reliable DL algorithms is seen to be the key to further enhancing detection performance. Using the analysis of images of the color of the retinal fundus, this study offers a DL model that is combined with a one-of-a-kind concoction loss function (CLF) for the automated identification of OHD. This study presents a combination of focal loss (FL) and correntropy-induced loss functions (CILF) in the proposed DL model to improve the recognition performance of classifiers for biomedical data. This is done because of the good generalization and robustness of these two types of losses in addressing complex datasets with class imbalance and outliers. The classification performance of the DL model with our proposed loss function is compared to that of the baseline models using accuracy (ACU), recall (REC), specificity (SPF), Kappa, and area under the receiver operating characteristic curve (AUC) as the evaluation metrics. The testing shows that the method is reliable and efficient. © 2023 Tech Science Press. All rights reserved.", "Keywords": "CNN; Deep learning; eye disease; focal loss; multi-classification", "DOI": "10.32604/cmc.2023.041722", "PubYear": 2023, "Volume": "76", "Issue": "3", "JournalId": 60809, "JournalTitle": "Computers, Materials & Continua", "ISSN": "1546-2218", "EISSN": "1546-2226", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, TIMES Institute, Multan, 60000, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Software Engineering, Faculty of Computer Science, Lahore Garrison University, Lahore, 54000, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Applied College, University of Tabuk, Tabuk, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Systems, College of Computer Sciences and Information Technology, King Faisal University, Al Hofuf, 31982, Saudi Arabia"}, {"AuthorId": 5, "Name": "Qazi Mudassar Ilyas", "Affiliation": "Department of Information Systems, College of Computer Sciences and Information Technology, King Faisal University, Al Hofuf, 31982, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, College of Engineering, King Faisal University, Al Hofuf, Saudi Arabia"}], "References": [{"Title": "Spatial Correlation Module for Classification of Multi-Label Ocular Diseases Using Color Fundus Images", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "76", "Issue": "1", "Page": "133", "JournalTitle": "Computers, Materials & Continua"}]}, {"ArticleId": 109406965, "Title": "TumorAwareNet: Deep representation learning with attention based sparse convolutional denoising autoencoder for brain tumor recognition", "Abstract": "<p>Learning discriminate representations from images plays crucial role in medical image analysis. The attention mechanism, on the other hand, leads to breakthrough results in the computer vision field by allowing models to provide varying levels of focus across image regions. In this work, we present Tumor Aware Net, an end-to-end trainable attention based Convolutional Neural Network that learns effective representations from Magnetic Resonance (MR) images suitable for effective tumor recognition. The proposed model employs a Sparse Convolutional Denoising Autoencoder (SCDA) to project the higher dimensional MR image representations to a lower dimensional space with improved discrimination. These lower dimensional descriptors are passed through an attention module, which prioritizes tumor descriptors over the rest. Furthermore, the proposed SCDA is trained jointly with the Neural induced Support Vector Classifier (NSVC) to achieve maximum margin separation. The proposed model has been validated on several publicly available benchmark datasets for tumor recognition. Based on the outcomes of the experimental studies, we claim that the proposed model favours stability and complements the learned representations when combined with attention. Despite its simplicity in terms of model parameters, the proposed model outperforms existing models for tumor type categorization.</p>", "Keywords": "Brain tumor recognition; Pre-trained Convolutional Neural Networks (CNNs); Sparse Convolutional Denoising Autoencoder (SCDA); Deep features; Spatial attention; Reconstruction convolutional auto-encoder; Convolutional support vector classifier", "DOI": "10.1007/s11042-023-15557-w", "PubYear": 2024, "Volume": "83", "Issue": "8", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "VFS<PERSON> Deemed to be University, Guntur, India; Corresponding author."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>ji", "Affiliation": "IEEE Member, Bengaluru, India"}], "References": [{"Title": "Active deep neural network features selection for segmentation and recognition of brain tumors using MRI images", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "129", "Issue": "", "Page": "181", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Automated Categorization of Brain Tumor from MRI Using CNN features and SVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8357", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Automated Categorization of Brain Tumor from MRI Using CNN features and SVM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "8", "Page": "8357", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Composite deep neural network with gated-attention mechanism for diabetic retinopathy severity classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; V<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "10", "Page": "9825", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Brain tumor classification using modified kernel based softplus extreme learning machine", "Authors": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "9", "Page": "13513", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "MSENet: Multi-Modal Squeeze-and-Excitation Network for Brain Tumor Severity Prediction", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "35", "Issue": "7", "Page": "2157005", "JournalTitle": "International Journal of Pattern Recognition and Artificial Intelligence"}, {"Title": "RETRACTED: An efficient approach for brain tumor detection and segmentation in MR brain images using random forest classifier", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "29", "Issue": "3", "Page": "266", "JournalTitle": "Concurrent Engineering"}, {"Title": "Rib segmentation algorithm for X-ray image based on unpaired sample augmentation and multi-scale network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "16", "Page": "11583", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Stacked convolutional auto-encoder representations with spatial attention for efficient diabetic retinopathy diagnosis", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "22", "Page": "32033", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 109407138, "Title": "Application of Al-decorated ZnO Nanorods as Gas Sensor to Detect H2 and CO", "Abstract": "In this paper, the synthesis of ZnO nanorods on a glass substrate using a hydrothermal method is described. First, spin coating was used to prepare a ZnO seed layer on a glass substrate. Then, hydrothermal synthesis was performed using a 0.03 M solution of Zn(CH3COO)2-2H2O, CH3OCH2CH2OH, and C2H7N and different synthesis times (30, 45, and 60 min) to synthesize ZnO nanorods on the ZnO seed layer. To improve the detection efficiency of both H2 and CO, a 5-nm-thick aluminum layer was deposited on the surfaces of the synthesized ZnO nanorods by thermal evaporation. To create a metal–semiconductor–metal gas sensor, 350-nm-thick interdigitated aluminum electrodes were deposited on the ZnO nanorods. The thus-obtained sensor was used to measure the concentrations of H2 and CO at temperatures ranging from 50 to 300 ℃ and at concentrations ranging from 100 to 2000 ppm. We compared the measurement results for pure and Al-decorated ZnO nanorods to determine the optimum sensing parameters. The measurement results showed the enhancements of the sensing efficiencies of H2 and CO when Al was used to decorate ZnO nanorods. © MYU K.K.", "Keywords": "Al-decorated ZnO; gas sensor; hydrothermal method; nanoarrays", "DOI": "10.18494/SAM4458", "PubYear": 2023, "Volume": "35", "Issue": "8", "JournalId": 34409, "JournalTitle": "Sensors and Materials", "ISSN": "0914-4935", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangxi Key Laboratory of Ocean Engineering Equipment and Technology, 12 Binhai Avenue, Qinzhou, 535011, China; Key Laboratory of Beibu Gulf Offshore Engineering Equipment and Technology (Beibu Gulf University), Education Department of Guangxi Zhuang Autonomous Region, 12 Binhai Avenue, Qinzhou, 535011, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Optoelectronic Engineering, National Chung Hsing University, 145 Xingda Rd., South Dist., Taichung City, 40227, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate Institute of Optoelectronic Engineering, National Chung Hsing University, 145 Xingda Rd., South Dist., Taichung City, 40227, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemical and Materials Engineering, National University of Kaohsiung, 700, Kaohsiung University Rd., Nanzih District, Kaohsiung, 811, Taiwan; Department of Aeronautical Engineering, Chaoyang University of Technology, 168, Jifeng E. Rd., Wufeng District, Taichung, 413310, Taiwan"}], "References": []}]