"""
演示pyecharts的基础入门
"""

# 导包
from pyecharts.charts import Line
from pyecharts.options import TitleOpts,LegendOpts,TooltipOpts,VisualMapOpts

# 创建一个折线图对象
line = Line()
# 给折线图对象添加x轴的数据
line.add_xaxis(["中国","美国","英国"])
# 给折线图添加y轴的数据
line.add_yaxis("GDP",[30,20,10])

# 设置全局配置set_global_opts来设置
line.set_global_opts(
    title_opts = TitleOpts(title="GDP展示",pos_left = "center",pos_bottom = "1%"),
    legend_opts = LegendOpts(is_show = True),
    tooltip_opts = TooltipOpts(is_show = True),
    visualmap_opts = VisualMapOpts(is_show = False),
)

# 通过render方法，将代码生成图像
line.render("test_3_5.html")