[{"ArticleId": 89949028, "Title": "Can news with positive or negative content affect and a relaxation pause improve the emotional state of health care professionals? A randomized online experiment during COVID-19 pandemic", "Abstract": "A cause of mental distress during the COVID-19 pandemic is media exposure, which can impact health care professionals (HCPs) who must keep up to date with the statistics and procedures to fight the outbreak. This study aimed to evaluate the effects of listening to negative and positive news about COVID-19 pandemic and a relaxation pause audio. For that, we measured the emotional state through Likert items in a scale developed to assess how anxious, stressed, hopeful, conscious about emotions, irritated, despondent, joyful, optimistic, and preoccupied, he or she was feeling in the moment of evaluation. In an online experiment, an HCPs sample of 245 participants were randomly assigned to either listen to negative or positive news contents about COVID-19. After that, both groups were guided by a relaxation pause activity in which they paid attention to the body and breath. They were assessed before and after listening to each audio. After listening to negative news, participants entered in a more negative emotional state than at baseline ( p < 0.001) and compared with participants who listened to positive news (p < 0.001). Both groups improved their emotional state after performing the proposed brief relaxation ( p < 0.001). These results show the importance of HCPs being aware and controlling the content of consumed news. A brief relaxation practice can mitigate the negative effects of consuming information with negative content.", "Keywords": "Emotion ; Health care professional ; Pandemic ; COVID-19 ; Relaxation ; Pause", "DOI": "10.1016/j.invent.2021.100441", "PubYear": 2021, "Volume": "26", "Issue": "", "JournalId": 11817, "JournalTitle": "Internet Interventions", "ISSN": "2214-7829", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hospital Israelita Albert Einstein, São Paulo, SP, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>o <PERSON>", "Affiliation": "Hospital Israelita Albert Einstein, São Paulo, SP, Brazil"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Hospital Israelita Albert Einstein, São Paulo, SP, Brazil"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hospital Israelita Albert Einstein, São Paulo, SP, Brazil"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hospital Israelita Albert Einstein, São Paulo, SP, Brazil"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Hospital Israelita Albert Einstein, São Paulo, SP, Brazil"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Hospital Israelita <PERSON>, São Paulo, SP, Brazil;Corresponding author"}], "References": [{"Title": "The COVID-19 pandemic: The ‘black swan’ for mental health care and a turning point for e-health", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "20", "Issue": "", "Page": "100317", "JournalTitle": "Internet Interventions"}]}, {"ArticleId": 89949055, "Title": "A joint energy efficiency optimization scheme based on marginal cost and workload prediction in data centers", "Abstract": "With the widespread development of cloud computing, the dramatic increase in the number and size of data centers (DCs) has resulted in substantial energy consumption and serious environmental problems. This creates a challenge for the further development of DCs. It is imperative to improve the overall energy efficiency of DCs. According to the statistics, servers and cooling systems are the main energy-consuming components of DCs. Recently, several energy-efficient strategies have been developed to address these problems. However, most of these works only consider the energy optimization of servers or cooling systems separately. Therefore, a Jo int E nergy E fficiency Optimization S cheme (JEES) is proposed in this paper, where the energy consumed by servers and the cooling system is jointly considered, and coordinately optimized. JEES includes a dynamic online task scheduling algorithm based on marginal cost evaluation, a resource management strategy that integrates the workload prediction technique to manage resources, and a task migration method using marginal cost evaluation. By using the proposed techniques, the total energy consumption of DCs can be reduced. Extensive experiments have been conducted based on real-world workload traces, and the results demonstrate that compared with other techniques, the proposed scheme effectively improves the overall resource utilization and reduces the total energy consumption of DCs.", "Keywords": "Data center ; Energy efficiency ; Task scheduling ; Cooling system ; Marginal cost ; Workload prediction", "DOI": "10.1016/j.suscom.2021.100596", "PubYear": 2021, "Volume": "32", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "Kaixuan Ji", "Affiliation": "High Performance Computer Research Center, Institute of Computing Technology, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "High Performance Computer Research Center, Institute of Computing Technology, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "High Performance Computer Research Center, Institute of Computing Technology, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Capital Normal University, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Information Engineering, Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "High Performance Computer Research Center, Institute of Computing Technology, Chinese Academy of Sciences, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "High Performance Computer Research Center, Institute of Computing Technology, Chinese Academy of Sciences, Beijing, China;Corresponding author"}], "References": []}, {"ArticleId": 89949180, "Title": "Implementation of Agglomerative Hierarchical Clustering Based on The Classification of Food Ingredients Content of Nutritional Substances", "Abstract": "<p>Health is something very precious. Maintaining health can be done in many ways, one of them by keeping your diet. The correct diet will keep your immune system so that it can avoid various diseases. The proper diet will also put the body in a balanced nutrition state, which all need to be nourished. Nutrient requirements include calories, protein, fat, carbohydrates, calcium, phosphorus, iron, vitamin A, vitamin B, and vitamin C with a mass of 100 grams each. To facilitate the search for nutrients needed, then build a system that can categorize food based on its nutritional status and calculate the average value of nutrients in agglomerative hierarchical clustering using average linkage. Calculation of intermediate linkage methods produces data that has some similarities to the data sought nutrients that can be seen from its index, so precise data are in each group.</p>", "Keywords": "Agglomerative hierarchical;Clustering;Average Linkage;Nutrients;Silhouette Index", "DOI": "10.25299/itjrd.2021.6872", "PubYear": 2021, "Volume": "6", "Issue": "1", "JournalId": 58679, "JournalTitle": "IT JOURNAL RESEARCH AND DEVELOPMENT", "ISSN": "2528-4061", "EISSN": "2528-4053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Departement of Computer Engineering, Politeknik Caltex Riau"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Departement of Informatic Engineering, Universitas Islam Riau"}], "References": []}, {"ArticleId": 89949357, "Title": "Quantitative Analysis of the Impact of Wireless Internet Technology on College Students’ Innovation and Entrepreneurship under the Background of “Internet Plus”", "Abstract": "<p>Under the background of “Internet plus,” the opportunities and challenges that college students face in the process of innovation and entrepreneurship coexist. College students should make full use of the powerful function of the Internet to excavate the huge business opportunities hidden under the background of “Internet plus.” In the context of “Internet plus” of mass entrepreneurship and innovation, the quantitative analysis method is studied in the context of wireless network technology on college students’ innovation and entrepreneurship. This paper proposes a combined weight model and an evaluation model based on genetic fuzzy optimization neural network. This research initially establishes an evaluation index system (EIS) by analyzing the influence factors of wireless network technology on college students’ innovation and entrepreneurship. In addition, EIS is also analyzed by combining the objective weight of each index obtained by the entropy with the subjective weight of each index obtained by the analytic hierarchy process to construct a combined weight model. A genetic algorithm is used to optimize fuzzy optimization neural networks and establish an evaluation index system of wireless network technology based on genetic fuzzy optimization neural network. To minimize the output error, the function of output error is used as the fitness evaluation function to output the score after several iterations. The experimental results show that the evaluation model can determine the importance of the influencing factors of wireless network technology on college students’ innovation and entrepreneurship. It is further evident from the experiments that the proposed model has high accuracy, with the average relative error always less than 1%, which can further improve the effect of quantitative analysis. The proposed model also has a fast convergence speed that can prevent local minima.</p>", "Keywords": "", "DOI": "10.1155/2021/9282092", "PubYear": 2021, "Volume": "2021", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Business Administration, Ningbo University of Finance & Economics, Ningbo 315175, China"}], "References": []}, {"ArticleId": 89949458, "Title": "IoT based a Smart Home Automation System Design: Simulation Case", "Abstract": "Today, solutions developed with the Internet of Things have started to find more and more application areas to make human life easier. Internet of Things solutions, which include many different types of new technologies, will be very useful to use simulation tools before prototyping for the most appropriate technology selection. In this study, IoT-based smart home design applications have been developed and analyzed. In this context, 3 different scenarios have been developed for smart home automation system design in the Cisco Packet Tracer simulation environment, and the use of tools has been demonstrated with examples according to different scenarios.", "Keywords": "Smart home automation,IoT solutions,Packet tracer,Simulation,Internet of Things.", "DOI": "10.17694/bajece.918826", "PubYear": 2021, "Volume": "9", "Issue": "3", "JournalId": 27857, "JournalTitle": "Balkan Journal of Electrical and Computer Engineering", "ISSN": "2147-284X", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Erdal ÖZDOĞAN", "Affiliation": "MİLLİ EĞİTİM BAKANLIĞI"}, {"AuthorId": 2, "Name": "Resul DAŞ", "Affiliation": "Firat University, Technology Faculty, Department of Software Engineering"}], "References": []}, {"ArticleId": 89949469, "Title": "A broadband circularly polarized stacked microstrip patch antenna with parasitic elements", "Abstract": "<p>In this article, a wideband circularly polarized stacked microstrip patch antenna (CP SMPA) with continuous phase feed characteristics is presented. The designed antenna consists of a square-loop feeding structure as driven element, four rectangular-patches and four rotated square-patches as parasitic elements. First, a square-loop with arc-shaped strip is designed to obtain 90° phase difference. Second, four rectangular-patches are placed next to the square-loop for generating a CP resonant mode through a capacitive coupling method. Third, four rotated square-patches are arranged at the top of the rectangular-patches to excite an additional CP resonant mode. Eventually, the designed CP SMPA features a wide −10-dB impedance bandwidth (IBW) of 32% (5.93 GHz, 4.98–6.88 GHz), and a broad 3-dB axial ratio bandwidth of 22.8% (6.15 GHz, 5.45–6.85 GHz).</p>", "Keywords": "circularly polarized antenna;sequential phase;parasitic elements;wideband antenna", "DOI": "10.1002/mmce.22842", "PubYear": 2021, "Volume": "31", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "Wen<PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Hainan Vocational University of Science and Technology, Haikou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Physics and Electronic Engineering, Jiaying University, Meizhou, China; School of Automation, Guangdong University of Technology, Guangzhou, China"}], "References": []}, {"ArticleId": ********, "Title": "A bootstrapping approach to social media quantification", "Abstract": "<p>This work considers the use of classifiers in a downstream aggregation task estimating class proportions, such as estimating the percentage of reviews for a movie with positive sentiment. We derive the bias and variance of the class proportion estimator when taking classification error into account to determine how to best trade off different error types when tuning a classifier for these tasks. Additionally, we propose a method for constructing confidence intervals that correctly adjusts for classification error when estimating these statistics. We conduct experiments on four document classification tasks comparing our methods to prior approaches across classifier thresholds, sample sizes, and label distributions. Prior approaches have focused on providing the most accurate point estimate while this work focuses on the creation of correct confidence intervals that appropriately account for classifier error. Compared to the prior approaches, our methods provide lower error and more accurate confidence intervals.</p>", "Keywords": "Quantification; Classification bias; Confidence interval; Uncertainty; Social media; Public health", "DOI": "10.1007/s13278-021-00760-0", "PubYear": 2021, "Volume": "11", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Los Alamos National Laboratory, Los Alamos, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Colorado, Boulder, USA"}], "References": []}, {"ArticleId": ********, "Title": "Big data medical behavior analysis based on machine learning and wireless sensors", "Abstract": "<p>To improve the scientificity and reliability of medical behavior analysis, this paper combines machine learning and wireless sensor technology to construct an intelligent data mining system that can be used for medical behavior analysis and uses association rules to analyze and mine the implicit relationships between structural monitoring parameters. Moreover, this paper establishes strong association rules between different monitoring variables based on historical monitoring data under normal structural conditions to predict whether the structural conditions are normal. In addition, this paper constructs a system function module according to actual needs, obtains the overall system architecture, and implements the system function module in combination with algorithms. Finally, this paper designs experiments to verify the performance of the system constructed in this paper and discusses the experimental results through mathematical graph analysis methods. From the research point of view, it can be observed that the system constructed in this paper has a specific effect.</p>", "Keywords": "Machine learning; Wireless sensors; Big data; Medical behavior", "DOI": "10.1007/s00521-021-06369-w", "PubYear": 2022, "Volume": "34", "Issue": "12", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Glorious Sun School of Business and Management, Donghua University, Shanghai, China"}], "References": [{"Title": "WOA + BRNN: An imbalanced big data classification framework using Whale optimization and deep neural network", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; Labib. <PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "8", "Page": "5573", "JournalTitle": "Soft Computing"}]}, {"ArticleId": 89949951, "Title": "SPECIAL ISSUE EDITORIAL: Next Generation ICTs - How distant is ubiquitous computing?", "Abstract": "<p>Traditional design development processes have come a long way from the use of drawing boards. The accelerated use of ICT-based digital systems means that the industry has steadily moved towards a digitized future. A future where increasingly unstructured information is created, shared, manipulated, stored, and archived in various digital media support the four pillars of visualization, integration, communication, and intelligence on which typical construction projects currently stand. The ICT field's growth, combined with the unprecedented advances in communication and network media usage, has resulted in hyper-interconnectivity globally. This hyper-connectivity through developments such as the Internet of Things (IoT) creates global opportunities for collaboration, which was not previously possible. It links 'human' and 'social' networks with 'technical' systems. With the vast volumes of digitally connected systems and the systems' data, new opportunities for learning have surfaced in the construction domain. The special issue targeted the state-of-the-art developments of next generation ICTs in the global arena. As with any new developments, new opportunities emerge and new challenges surface. The cautious late majority and the laggards, adopt a skeptical approach, that which is reserved and strewn in doubt. The innovators and the early majority on the other hand pave the way to technology adoption and help drive change in the industry. This special issue recognises that the emergence of next-generation ICTs, combined with developments in ubiquitous computing, presentopportunities that challenge the current status quo of the construction sector. This special issue recognizes that for an industry to remain truly competitive, due consideration need to be given to the ongoing and emerging technological developments, and a deep understanding of which would lead to novel responsive approaches for their significant uptake. The collection of papers in this special issue gives a comprehensive overview of research and developments in the field of next-generation ICTs. It bridges the gap between the two domains of construction and computer science. Of the ten papers in this collection, two (by Akamu et al, and Karmarkar and Delhi) were invited papers and include a world view on thewider applicability of next-generation ICTs in the construction domain, the opportunities they present and the challenges that emerge. The remaining papers, amongst other aspects, cover theinnovative application of next-gen ICTs in specific industry sectors(e.g., in the water industry by Alani et al, 2021)or in meeting specific project goals such as to manage energy consumption (Watfa et al), improve information retrieval (Wang et al), integrate AR and BIM for specific building submission processes (Schranz et al), identify challenges to collaborative working within globally dispersed virtual project teams (Anderson and Ramalingam), and ontology for robot navigation and data fusion (Karimi et al).</p>", "Keywords": "", "DOI": "10.36680/j.itcon.2021.033", "PubYear": 2021, "Volume": "26", "Issue": "", "JournalId": 70075, "JournalTitle": "Journal of Information Technology in Construction", "ISSN": "", "EISSN": "1874-4753", "Authors": [], "References": [{"Title": "From BIM to digital twins: a systematic review of the evolution of intelligent building representations in the AEC-FM industry", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "58", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "Potentials of Augmented Reality in a BIM based building submission process", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "441", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "Information and Communication Technologies applied to intelligent buildings: a review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "458", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "A socio-technical intervention in BIM projects – an experimental study in global virtual teams", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "489", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "Construction 4.0: what we know and where we are headed?", "Authors": "<PERSON><PERSON>; Ven<PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "526", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "Towards next generation cyber-physical systems and digital twins for construction", "Authors": "Abiola <PERSON>; <PERSON><PERSON><PERSON>; Omobolanle O<PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "505", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "Multi-scale Information Retrieval for BIM using Hierarchical Structure Modelling and Natural Language Processing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "409", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "Using Building Information & Energy Modelling for Energy Efficient Designs", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "427", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "A semantic common model for product data in the water industry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "566", "JournalTitle": "Journal of Information Technology in Construction"}, {"Title": "Ontology-based approach to data exchanges for robot navigation on construction sites", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "26", "Issue": "", "Page": "546", "JournalTitle": "Journal of Information Technology in Construction"}]}, {"ArticleId": 89949952, "Title": "ANALYSIS ON DEEP LEARNING AND ITS TYPES", "Abstract": "", "Keywords": "", "DOI": "10.26634/jse.15.2.18164", "PubYear": 2020, "Volume": "15", "Issue": "2", "JournalId": 39941, "JournalTitle": "i-manager’s Journal on Software Engineering", "ISSN": "0973-5151", "EISSN": "2230-7168", "Authors": [{"AuthorId": 1, "Name": "A. LAKSHMI PRABA", "Affiliation": "Department of Electronics and Communication Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamilnadu, India."}, {"AuthorId": 2, "Name": "S. R. PREETHI", "Affiliation": "Department of Electronics and Communication Engineering, SRM Valliammai Engineering College, Kattankulathur, Tamilnadu, India."}], "References": []}, {"ArticleId": 89949998, "Title": "IDENTIFICATION OF STUDENT<PERSON> <PERSON><PERSON><PERSON> FOR CHOOSING EFFECTIVE\nCAREER USING DATA MINING TECHNIQUES – A REVIEW", "Abstract": "", "Keywords": "", "DOI": "10.26634/jse.15.2.18318", "PubYear": 2020, "Volume": "15", "Issue": "2", "JournalId": 39941, "JournalTitle": "i-manager’s Journal on Software Engineering", "ISSN": "0973-5151", "EISSN": "2230-7168", "Authors": [{"AuthorId": 1, "Name": "R. THIRUMALAISELVI", "Affiliation": "Department of Computer Science, Government Arts College for Men, Chennai, Tamilnadu, India."}, {"AuthorId": 2, "Name": "P. NARAYANAN", "Affiliation": "Department of Computer Science, Government Arts College for Men, Chennai, Tamilnadu, India."}], "References": []}, {"ArticleId": 89950031, "Title": "Correction: Impact Vector Guidance", "Abstract": "Correction Notice HIS correction pertains to a missing reference in the original article when it was first published online [https://doi.org/10.2514/1.G006087]. The details of the correction are as follows. T * The sentence “It should be noted that aEPPN Nvc _λ ” in page 2 after Eq. (4) needs to be replaced with the following: “It should be noted that aEPPN Nvc _λ and this guidance law appears in [17] (but with N0 ).” * Consequently, the list of references needs to be updated as below. [17] <PERSON><PERSON>, M. W., “The Development of Radar Homing Missiles,” Journal of Guidance, Control, and Dynamics, Vol. 7, No. 6, 1984, pp. 641–651. https://doi.org/10.2514/3.19908 [18] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, J. Z., and Farber, N., “Near-Optimal Spatial Midcourse Guidance Law with an Angular Constraint,” Journal of Guidance, Control, and Dynamics, Vol. 37, No. 1, 2014, pp. 214–223. https://doi.org/10.2514/1.60356 [19] <PERSON>, <PERSON><PERSON>, Radar Homing Guidance for Tactical Missiles, Macmillan, London, 1986, pp. 106–127. * Moreover, [17] in page 4 and page 6 needs to appear as [18] as shown below. © 2021, AIAA International. All rights reserved.", "Keywords": "Guidance Laws; Millimeter Wave Radar", "DOI": "10.2514/1.G006087.c1", "PubYear": 2021, "Volume": "44", "Issue": "10", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Roketsan Missiles, Inc., 06780 Ankara, Turkey"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Roketsan Missiles, Inc., 06780 Ankara, Turkey"}], "References": []}, {"ArticleId": 89950140, "Title": "AVA: A component-oriented abstraction layer for virtual plug&produce automation systems engineering", "Abstract": "The prevailing system and software model in automation systems engineering is defined by the IEC 61131 norm. It is to date the best way we know how to express low-level logic and manipulate electrical hardware signals. However, the exponential technological growth is continuing to raise the expectations on what automation systems are supposed to be capable of doing. Fulfilling rising requirements and managing the exploding complexity requires a systematic support for high-level descriptions, structuring, and communication, which the original approach was not built to provide. This work proposes the introduction of an abstraction layer, a component-container infrastructure, defined on top of standard system and software models in automation and mirroring the world of cyber–physical systems, where independent components are interconnected to realize the systems’ purpose by using each other’s functionalities. The concept is implemented in the form of a domain-specific modeling language, applying a classical two-level Model-driven Software Engineering (MDSE) approach. By engineering distinct industrial use cases in accordance with the proposed approach, it is shown that the defined abstractions and mechanisms are capable of expressing the nuances of software design in different domains and can enable the streamlining of the automation systems engineering workflow into a virtual plug&amp;produce process.", "Keywords": "Automation ; Domain language ; Architectural pattern ; Framework", "DOI": "10.1016/j.jii.2021.100251", "PubYear": 2022, "Volume": "26", "Issue": "", "JournalId": 43270, "JournalTitle": "Journal of Industrial Information Integration", "ISSN": "2467-964X", "EISSN": "2452-414X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Computer Engineering, Automation Systems, TU Wien, Vienna, Austria;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Computer Engineering, Automation Systems, TU Wien, Vienna, Austria"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Computer Engineering, Automation Systems, TU Wien, Vienna, Austria"}], "References": []}, {"ArticleId": 89950146, "Title": "Data for absorption, evaluation and effectiveness of R&D projects in Greece", "Abstract": "This data article presents primary data for the absorption, evaluation, and effectiveness of 1949 research projects implemented by 3259 research institutions in the framework of funding actions of the operational programmes of the Partnership Agreement for the Development Framework for the 2007–2013 programming period [1 , 2] . The authors collected the data in the period 2009–2017, after the study and processing of 37 national funding actions [2] , [3] , [4] , [5] managed during the same period by the General Secretariat for Research and Technology (G.S.R.T.) and the Special Managing and Implementation Service in the areas of Research, Technological Development and Innovation (R.T.D.I.). The presented data can be used by researchers and research project management bodies studying the absorption, management and distribution of research resources and the design and implementation of multi-criteria project evaluation methods.", "Keywords": "R&D funding ; Absorption ; Ex-ante evaluation ; Effectiveness", "DOI": "10.1016/j.dib.2021.107273", "PubYear": 2021, "Volume": "37", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Laboratory of Industrial & Energy Economics, School of Chemical Engineering, National Technical University of Athens, Zografou Campus, Athens 15780, Greece;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Laboratory of Industrial & Energy Economics, School of Chemical Engineering, National Technical University of Athens, Zografou Campus, Athens 15780, Greece;Corresponding authors"}], "References": []}, {"ArticleId": 89950161, "Title": "Organizational process maturity model for IoT data quality management", "Abstract": "Data quality management (DQM) is one of the most critical aspects to ensure successful applications of the Internet of Things (IoT). So far, most of the approaches for assuring data quality are typically data-centric, i.e., mainly focus on fixing data issues for specific values. However, organizations can also benefit from improving their capabilities of their DQM processes by developing organizational best DQM practices. In this regard, our investigation addresses how well organizations perform their DQM processes in the IoT domain. The main contribution of this study is to establish a framework for IoT DQM maturity. This framework is compliant with ISO 8000-61 (DQM: process reference model) and ISO 8000-62 (DQM: organizational process maturity assessment) and can be used to assess and improve the capabilities of the DQM processes for IoT data. The framework is composed of two elements. First, a process reference model (PRM) for IoT DQM is proposed by extending the PRM for DQM defined in ISO 8000-61, tailoring some existing processes and adding new ones. Second, a maturity model suitable for IoT data is proposed based on the PRM for IoT DQM. The maturity model, named IoT DQM3, is proposed by extending the maturity model defined in ISO 8000-62. However, in order to increase the usability of IoT DQM3, we consider adequate the proposition of a simplification of the IoT DQM3, by introducing a lightweight version to reduce assessment indicators and facilitate its industrial adoption. A simplified method to measure the capability of a process is also suggested considering the relationship of process attributes with the measurement stack defined in ISO 8000-63. The empirical validation of the maturity model is twofold. First, the appropriateness of the two models is surveyed with data quality experts who are currently working in various organizations around the world. Second, in order to demonstrate the feasibility of the proposal, the light-weight version is applied to a manufacturing company as a case study.", "Keywords": "Data quality ; Data quality management ; IoT ; ISO 8000 ; Process-centric ; Process reference model ; Maturity ; Process maturity ; process attribute", "DOI": "10.1016/j.jii.2021.100256", "PubYear": 2022, "Volume": "26", "Issue": "", "JournalId": 43270, "JournalTitle": "Journal of Industrial Information Integration", "ISSN": "2467-964X", "EISSN": "2452-414X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial & Management Engineering, Myongji University, Yongin, Gyeonggido 17058, Korea"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Information Technology and Systems, University of Castilla-La Mancha, 45600 Talavera de la Reina, Spain"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Information Technologies & Systems Institute (ITSI), University of Castilla-La Mancha, 13071 Ciudad Real, Spain"}, {"AuthorId": 4, "Name": "Downgwo<PERSON> Lee", "Affiliation": "GT<PERSON>ne, 2-50 Ace Hightech City Bldg, 775 <PERSON><PERSON>ongin-ro,Yongdungpogu, Seoul 07299, Korea"}], "References": []}, {"ArticleId": 89950185, "Title": "VIC — A Tangible User Interface to train memory skills in children with Intellectual Disability", "Abstract": "Memory is defined as the capability of encoding, storing, and retrieving information and is a pillar of our cognitive functions. Memory is one the most investigated processes in people with Intellectual Disability (ID), and studies have documented severe deficits in its functioning. While there are several attempts to exploit GUIs (Graphical User Interfaces) to support memory training for children with ID, limited research explores Tangible User Interfaces (TUIs) for this purpose. The paper describes the design and technology of a novel TUI named VIC (VIsual spatial Cubes for memory training), a system composed by a set of digitally enhanced cubes that emit light and sound, a sensorized board and a mobile app. VIC enables children to perform multiple, configurable memory training activities that involve block manipulation and block placements. The research is based on a vast analysis of the state of the art and on validated methods adopted in memory rehabilitation contexts. From this analysis, and from the theories underlying TUIs, we distilled a set of design principles that informed the design of the multi-sensory affordances of VIC, and can be exploited for other researchers to develop TUIs in this field. The paper also reports an exploratory study that involved 12 children with ID and 3 therapists from a specialized daycare Center. The study focused on the evaluation of the quality of the system in terms of usability, likability and potential for adoption. Although preliminary, the results suggest that our design approach was sound and VIC has the potential to become a valid tool to complement existing practices in memory training and can be expanded to support also memory assessment for children with ID.", "Keywords": "Smart tangible interface ; Children ; Intellectual disability ; Memory training ; Short-term working memory", "DOI": "10.1016/j.ijcci.2021.100376", "PubYear": 2022, "Volume": "32", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department. of Electronics, Information and Bio-engineering Politecnico di Milano, Milan, Italy;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department. of Electronics, Information and Bio-engineering Politecnico di Milano, Milan, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department. of Electronics, Information and Bio-engineering Politecnico di Milano, Milan, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department. of Electronics, Information and Bio-engineering Politecnico di Milano, Milan, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department. of Electronics, Information and Bio-engineering Politecnico di Milano, Milan, Italy"}], "References": [{"Title": "Phygital interfaces for people with intellectual disability: an exploratory study at a social care center", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "26-27", "Page": "34843", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 89950201, "Title": "A study of the generalizability of self-supervised representations", "Abstract": "Recent advancements in self-supervised learning (SSL) made it possible to learn generalizable visual representations from unlabeled data. The performance of Deep Learning models fine-tuned on pretrained SSL representations is on par with models fine-tuned on the state-of-the-art supervised learning (SL) representations. Irrespective of the progress made in SSL, its generalizability has not been studied extensively. In this article, we perform a deeper analysis of the generalizability of pretrained SSL and SL representations by conducting a domain-based study for transfer learning classification tasks. The representations are learned from the ImageNet source data, which are then fine-tuned using two types of target datasets: similar to the source dataset, and significantly different from the source dataset. We study generalizability of the SSL and SL-based models via their prediction accuracy as well as prediction confidence. In addition to this, we analyze the attribution of the final convolutional layer of these models to understand how they reason about the semantic identity of the data. We show that the SSL representations are more generalizable as compared to the SL representations. We explain the generalizability of the SSL representations by investigating its invariance property, which is shown to be better than that observed in the SL representations.", "Keywords": "Self-supervised learning ; Supervised learning ; Transfer learning ; Generalizability ; Invariance", "DOI": "10.1016/j.mlwa.2021.100124", "PubYear": 2021, "Volume": "6", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Nebraska-Lincoln, NE, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Engineering, University of Nebraska-Lincoln, NE, USA;Corresponding author"}], "References": [{"Title": "A survey on deep learning and its applications", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "", "Page": "100379", "JournalTitle": "Computer Science Review"}]}, {"ArticleId": 89950717, "Title": "Color2Hatch: conversion of color to hatching for low-cost printing", "Abstract": "In this paper, we propose Color2Hatch, a decolorization method for business/presentation graphics. In Color2Hatch, each region represented as a closed path and uniformly colored in scalable vector graphics (SVG) is converted to a region hatched in black and white. From the characteristics of business graphics, the hatching patterns are designed to represent mainly the hue in the region; additionally, lightness and saturation can also be reflected. To discriminate subtle differences between colors, attached short line segments, zigzag lines, and wave lines are used in hatching by analogy to a clock. Compared with the existing decolorization methods, for example, grayscale conversion and texturing, our method is superior in the discrimination of regions, suitable for low-cost black and white printing that meets real-world needs.", "Keywords": "Decolorization; Color to hatching; Vector image processing; Low-cost black and white printing", "DOI": "10.1007/s00371-021-02268-2", "PubYear": 2021, "Volume": "37", "Issue": "12", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Computer Science and Systems Engineering, Kyushu Institute of Technology, Iizuka, Fukuoka, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Artificial Intelligence, School of Computer Science and Systems Engineering, Kyushu Institute of Technology, Iizuka, Fukuoka, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Artificial Intelligence, School of Computer Science and Systems Engineering, Kyushu Institute of Technology, Iizuka, Fukuoka, Japan"}], "References": []}, {"ArticleId": 89950718, "Title": "DwiMark: a multiscale robust deep watermarking framework for diffusion-weighted imaging images", "Abstract": "<p>To prevent diffusion-weighted imaging (DWI) images from being illegally used or maliciously attacked during the transmission of a remote diagnosis, the copyright of DWI images urgently needs to be protected. In this paper, an end-to-end robust deep watermarking framework is proposed for DWI images, which combines a generative adversarial structure with multiscale features. First, the DWI images are reconstructed by connecting full-scale features to make the fibers and texture highly similar to the original DWI images. Watermarks are also embedded into the multiscale reconstructed features. Then, an optimized BEGAN discriminator is proposed to improve the convergence speed and the visual quality of the reconstructed image. Finally, pyramid filters and multiscale max-pooling are applied to fully learn the watermark distribution features. The experiments show that the proposed framework achieves higher watermark robustness under various common image distortions. Specifically, for rotation and dropout, the robustness can compete with traditional methods. The average PSNR of watermarked DWI images is 58.69 dB, and the diffusion features change indistinguishably, which can effectively protect them without affecting a doctor’s diagnosis.</p>", "Keywords": "DWI images; Robust blind watermarking; Deep learning; Multiscale features; Generative adversarial networks; Deep supervision", "DOI": "10.1007/s00530-021-00835-0", "PubYear": 2022, "Volume": "28", "Issue": "1", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "@qq.com;College of Computer Science and Technology, Guizhou University, Guiyang City, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Guizhou University, Guiyang City, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Guizhou University, Guiyang City, China"}], "References": [{"Title": "Chaotic based secure watermarking approach for medical images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "7-8", "Page": "4263", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "ReDMark: Framework for residual diffusion watermarking based on deep networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113157", "JournalTitle": "Expert Systems with Applications"}, {"Title": "An improved DWT-SVD domain watermarking for medical information security", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "152", "Issue": "", "Page": "72", "JournalTitle": "Computer Communications"}, {"Title": "Lightweight image super-resolution with enhanced CNN", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "205", "Issue": "", "Page": "106235", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Embedding in medical images with contrast enhancement and tamper detection capability", "Authors": "<PERSON><PERSON>kat; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "2", "Page": "2009", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Feature-transfer network and local background suppression for microaneurysm detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "32", "Issue": "1", "Page": "1", "JournalTitle": "Machine Vision and Applications"}, {"Title": "DiCyc: GAN-based deformation invariant cross-domain information fusion for medical image synthesis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "147", "JournalTitle": "Information Fusion"}, {"Title": "Reversible data hiding in adjacent zeros", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "27", "Issue": "2", "Page": "229", "JournalTitle": "Multimedia Systems"}]}, {"ArticleId": 89950756, "Title": "Model-Free-Based Single-Dimension Fuzzy SMC Design for Underactuated Quadrotor UAV", "Abstract": "<p>The underactuated quadrotor unmanned aerial vehicle (UAV) is one of the nonlinear systems that have few actuators as compared to the degree of freedom (DOF); thus, it is a strenuous task to stabilize its attitude and positions. Moreover, an induction of unmodelled dynamic factors and uncertainties make it more difficult to control its maneuverability. In this paper, a model-free based single-dimension fuzzy sliding mode control (MFSDF-SMC) is proposed to control the attitude and positions of underactuated quadrotor UAV. The paper discusses the kinematic and dynamic models with unmodelled dynamic factors and unknown external disturbances. These unmodelled factors and disturbances may lead the quadrotor towards failure in tracking specific trajectory and may also generate some serious transient and steady-state issues. Furthermore, to avoid the problem of gimbal lock, the model is amalgamated with hyperbolic function to resolve the singularity issues dully developed due to <PERSON>’s dynamic modeling. The simulation results performed for MFSDF-SMC using MATLAB software R2020a are compared with conventional sliding mode control, fuzzy-based sliding control and single-dimension fuzzy-based sliding mode control without a model-free approach. The design and implementation of the model-free single dimension-based fuzzy sliding mode control (MFSDF-SMC) with an updated Lyapunov stability theorem is presented in this work. It is observed that MFSDF-SMC produces robust trajectory performance therefore, and the manuscript suggests the experimental setup to test the proposed algorithm in a noisy environment keeping the same conditions. The verification of the equipment used and its effective demonstration is also available for the reader within the manuscript.</p>", "Keywords": "model-free approach; quadrotor; single-dimension fuzzy; sliding mode control; unmodelled dynamics; underactuated system model-free approach ; quadrotor ; single-dimension fuzzy ; sliding mode control ; unmodelled dynamics ; underactuated system", "DOI": "10.3390/act10080191", "PubYear": 2021, "Volume": "10", "Issue": "8", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, Universiti Teknologi PETRONAS, Seri Is<PERSON>dar 32610, Malaysia↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Azrin B. M. <PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, Universiti Teknologi PETRONAS, Seri Iskandar 32610, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical & Electronic Engineering, Universiti Teknologi PETRONAS, Seri Iskandar 32610, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Systems Science, Beijing Normal University, Beijing 100875, China"}], "References": []}, {"ArticleId": 89950848, "Title": "Variable Time-of-Flight Spacecraft Maneuver Targeting Using State Transition Tensors", "Abstract": "", "Keywords": "Spacecraft Maneuvers; Lagrange Points; Sequential Quadratic Programming; Numerical Integration; Computing; Earth; Boundary Value Problems; Reinforcement Learning; Convex Optimization; Spacecraft Guidance", "DOI": "10.2514/1.G005890", "PubYear": 2021, "Volume": "44", "Issue": "11", "JournalId": 11847, "JournalTitle": "Journal of Guidance, Control, and Dynamics", "ISSN": "0731-5090", "EISSN": "1533-3884", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, Colorado 80309"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Colorado Boulder, Boulder, Colorado 80309"}], "References": [{"Title": "Orbital Guidance Using Higher-Order State Transition Tensors", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "44", "Issue": "3", "Page": "493", "JournalTitle": "Journal of Guidance, Control, and Dynamics"}]}, {"ArticleId": 89951240, "Title": "Dual‐band and omnidirectional miniaturized planar composite dipole antenna for\n WLAN \n applications", "Abstract": "<p>A novel planar omnidirectional dipole antenna for wireless local area network (WLAN) applications is proposed. The antenna consists of a sleeve dipole antenna for the lower band and a cage dipole antenna for the upper band. Employing a unique planar composite structure, good dual-band impedance matching can be achieved without an external matching circuit. The lower-band dipole antenna adopts a sleeve structure, which reduces the size by bending in the middle and thickening at the end as inductance and capacitance loading, respectively. The upper-band dipole antenna is embedded in the lower-band dipole antenna, and the impedance bandwidth is broadened by adopting a cage structure and coupling with the lower-band dipole antenna. The proposed antenna occupies an area of 0.38 λ<sub>0</sub> × 0.08 λ<sub>0</sub> (λ<sub>0</sub> represents the wavelength of the lower frequency) on FR4 substrate of thickness 0.8 mm. The measured results show that the impedance bandwidths with a return loss more than 10 dB are 2.27–2.65 GHz and 4.89–6.02 GHz. The antenna has an omnidirectional radiation pattern, and both E-plane and H-plane have lower cross-polarization levels. The average gain reaches 2.4 and 3.3 dBi in the 2.4- and 5-GHz bands, respectively. The antenna is compact and easy to fabricate, and can be used for small omnidirectional WLAN terminal device.</p>", "Keywords": "cage dipole;composite antenna;dual-band;miniaturization;sleeve dipole;wireless local area network", "DOI": "10.1002/mmce.22863", "PubYear": 2021, "Volume": "31", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>ang Fu", "Affiliation": "School of Information Science and Technology, Dalian Maritime University, Dalian, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime University, Dalian, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime University, Dalian, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Dalian Maritime University, Dalian, China"}], "References": []}, {"ArticleId": 89951243, "Title": "An alternative pressure-dependent velocity boundary condition for modeling self-reacting friction stir welding", "Abstract": "<p>A pressure-dependent velocity boundary condition is developed based on wear theory for modeling self-reacting friction stir welding using computational fluid dynamics approach, which provides a new perspective in understanding the physics of sliding/sticking transition condition. The importance of shear layer in weld formation is emphasized. Effects of welding speed on the weld cross-section geometry can be robustly captured with this newly developed boundary condition. Computational results show that at higher welding speed, the TMAZ boundary moves towards the pin periphery at the advancing side, which corresponds to the experimental observations. This tendency could serve as a numerical criterion to predict void defect formation.</p>", "Keywords": "Self-reacting friction stir welding; Wear theory; Shear layer; Boundary condition; CFD model", "DOI": "10.1007/s00170-021-07589-z", "PubYear": 2021, "Volume": "117", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Material Science and Engineering, The Ohio State University, Columbus, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Material Science and Engineering, The Ohio State University, Columbus, USA"}], "References": []}, {"ArticleId": 89951247, "Title": "Stories, journeys and smart maps: an approach to universal access", "Abstract": "This paper proposes a new approach to universal access based on the premise that humans have the universal capacity to engage emotionally with a story, whatever their ability. Our approach is to present the “story” of museum resources and knowledge as a journey, and then represent this journey physically as a smart map. The key research question is to assess the extent to which our “story” to journey to smart map’ (SJSM) approach provides emotional engagement as part of the museum experience. This approach is applied through the creation of a smart map for blind and partially sighted (BPS) visitors. Made in partnership with Titanic Belfast, a world-leading tourist attraction, the interactive map tells the story of Titanic ’s maiden voyage. The smart map uses low-cost technologies such as laser-cut map features and software-controlled multi-function buttons for the audio description (AD). The AD is enhanced with background effects, dramatized personal stories and the ship’s last messages. The results of a reception study show that the approach enabled BPS participants to experience significant emotional engagement with museum resources. The smart model also gave BPS users a level of control over the AD which gave them a greater sense of empowerment and independence, which is particularly important for BPS visitors with varying sight conditions. We conclude that our SJSM approach has considerable potential as an approach to universal access, and to increase emotional engagement with museum collections. We also propose several developments which could further extend the approach and its implementation.", "Keywords": "Audio description; Stories; Smart map; Journeys; Emotion; Universal access", "DOI": "10.1007/s10209-021-00832-0", "PubYear": 2022, "Volume": "21", "Issue": "2", "JournalId": 1397, "JournalTitle": "Universal Access in the Information Society", "ISSN": "1615-5289", "EISSN": "1615-5297", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Arts, English and Languages, Queen’s University Belfast, Belfast, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Electronics, Electrical Engineering and Computer Science, Queen’s University Belfast, Belfast, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Arts, English and Languages, Queen’s University Belfast, Belfast, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Arts, English and Languages, Queen’s University Belfast, Belfast, UK"}], "References": []}, {"ArticleId": 89951540, "Title": "Chinese address standardisation of POIs based on GRU and spatial correlation and applied in multi-source emergency events fusion", "Abstract": "A large number of users’ microblogs and various Points of Interest (POIs) of public service facilities in social media provide abundant data resources for the emergency events detection, fusion analysis and post-incident rescue. With the correlation analysis of these complex data resources based on the address information or location, people can instantly understand, rescue and make decisions for emergency events. This paper aims to propose an unsupervised method of multi-source POIs addresses segmentation and standardisation based on the Gated Recurrent Unit (GRU) neural network and spatial correlation. First, we use GRU neural network to automatically segment Chinese POIs addresses. Then, according to the spatial correlation between address elements, we can remove incorrect address elements, and construct a hierarchy address element map with the semantic relationship. Finally, the addresses of POIs or emergency events will be standardised by fuzzy matching, which uses the multi-source emergency events fusion of the first step. The propsed method is verified to a relatively high accuracy rate of address segment and standardisation, and it can be applied for the emergency event fusion and spatio-temporal analysis from multi-social media sites.", "Keywords": "Address standardisation ; pois ; gru ; spatial correlation ; multi-source data fusion", "DOI": "10.1080/19479832.2021.1961314", "PubYear": 2021, "Volume": "12", "Issue": "4", "JournalId": 26673, "JournalTitle": "International Journal of Image and Data Fusion", "ISSN": "1947-9832", "EISSN": "1947-9824", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Center of Geospatial Big Data Application, Chinese Academy of Surveying and Mapping, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Research Center of Geospatial Big Data Application, Chinese Academy of Surveying and Mapping, Beijing, China;Faculty of Geosciences and Environmental Engineering, Southwest Jiaotong University, Chengdu, China"}, {"AuthorId": 3, "Name": "Pengpeng Li", "Affiliation": "Faculty of Geosciences and Environmental Engineering, Southwest Jiaotong University, Chengdu, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Research Center of Geospatial Big Data Application, Chinese Academy of Surveying and Mapping, Beijing, China;Faculty of Geosciences and Environmental Engineering, Southwest Jiaotong University, Chengdu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Center of Geospatial Big Data Application, Chinese Academy of Surveying and Mapping, Beijing, China;Faculty of Geosciences and Environmental Engineering, Southwest Jiaotong University, Chengdu, China"}], "References": []}, {"ArticleId": 89951697, "Title": "Receding Horizon Task and Motion Planning in Changing Environments", "Abstract": "Complex manipulation tasks require careful integration of symbolic reasoning and motion planning. This problem, commonly referred to as Task and Motion Planning (TAMP), is even more challenging if the workspace is non-static, e.g. due to human interventions and perceived with noisy non-ideal sensors. This work proposes an online approximated TAMP method that combines a geometric reasoning module and a motion planner with a standard task planner in a receding horizon fashion. Our approach iteratively solves a reduced planning problem over a receding window of a limited number of future actions during the implementation of the actions. Thus, only the first action of the horizon is actually scheduled at each iteration, then the window is moved forward, and the problem is solved again. This procedure allows to naturally take into account potential changes in the scene while ensuring good runtime performance. We validate our approach within extensive experiments in a simulated environment. We showed that our approach is able to deal with unexpected changes in the environment while ensuring comparable performance with respect to other recent TAMP approaches in solving traditional static benchmarks. We release with this paper the open-source implementation of our method.", "Keywords": "Task and Motion Planning ; Robot manipulation ; Non-static Environments", "DOI": "10.1016/j.robot.2021.103863", "PubYear": 2021, "Volume": "145", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering, University of Padua, Padua, Italy;IT+Robotics Srl, Vicenza, Italy;Corresponding author at: IT+Robotics Srl, Vicenza, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "IT+Robotics Srl, Vicenza, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information Engineering, University of Padua, Padua, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Information Engineering, University of Padua, Padua, Italy"}], "References": []}, {"ArticleId": 89951699, "Title": "Unbalanced abnormal traffic detection based on improved Res-BIGRU and integrated dynamic ELM optimization", "Abstract": "Problems such as a vanishing gradient and overfitting will occur when a recurrent neural network (RNN) is exploited to detect abnormal network traffic. In addition, some network traffic is unbalanced, which leads to low detection accuracy. Therefore, an unbalanced abnormal traffic detection method has been proposed. It is composed of the improved bidirectional residual gated recurrent unit (Res-BIGRU) and integrated dynamic extreme learning machine (IDELM). First, the candidate hidden state activation function of the GRU is changed into an unsaturated activation function. The residual connection is used to avoid the vanishing gradient. The purpose of alleviating network degradation is achieved, and the traffic features extracted are better. Second, an IDELM is proposed to solve the unbalanced classification. The minority samples are generated by the IDELM model. The set model in game theory is used to compute the combined weight, which improves the fitting effect. Third, two IDELMs are used to update the final classification results. Fourth, four network datasets and IoT datasets are used to verify the performance. The average accuracy on four network datasets is 91.11% when samples are unbalanced. Furthermore, it can be concluded that the improved Res-BIGRU and IDELM strategy is effective. Better classification results can be achieved when network traffic is unbalanced. In particular, the performance is better in unbalanced NSL-KDD datasets. The index values obtained are the best compared with other methods. It is also suitable for intrusion detection of the Internet of Things, which has good performance. The further advantage lies in that the robustness is better when there are other sample interferences.", "Keywords": "Unbalanced traffic ; Vanishing gradient ; Improved Res-BIGRU ; Integrated dynamic ELM ; Classification", "DOI": "10.1016/j.comcom.2021.08.005", "PubYear": 2021, "Volume": "179", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, 611756, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, 611756, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Science and Technology, Southwest Jiaotong University, Chengdu, 611756, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Management, Xihua University, Chengdu 610039, China"}], "References": []}, {"ArticleId": 89951727, "Title": "Mobile computing and communications-driven fog-assisted disaster evacuation techniques for context-aware guidance support: A survey", "Abstract": "The importance of an optimal solution for disaster evacuation has recently raised attention from researchers across multiple disciplines. This is not only a serious, but also a challenging task due to the complexities of the evacuees’ behaviors, route planning, and demanding coordination services. Although existing studies have addressed these challenges to some extent, mass evacuation in natural disasters tends to be difficult to predict and manage due to the limitation of the underlying models to capture realistic situations. It is therefore desirable to have on-demand mechanisms of locally-driven computing and data exchange services in order to enable near real-time capture of the disaster area during the evacuation. For this purpose, this paper comprehensively surveys recent advances in information and communication technology-enabled disaster evacuations, with the focus on fog computation and communication services to support a massive evacuation process. A numerous variety of tools and techniques are encapsulated within a coordinated on-demand strategy of an evacuation platform, which is aimed to provide a situational awareness and response. Herein fog services appear to be one of the viable options for responsive mass evacuation because they enable low latency data processing and dissemination. They can additionally provide data analytics support for autonomous learning for both the short-term guidance supports and long-term usages. This work extends the existing data-oriented framework by outlining comprehensive functionalities and providing seamless integration. We review the principles, challenges, and future direction of the state-of-the-art strategies proposed to sit within each functionality. Taken together, this survey highlights the importance of adaptive coordination and reconfiguration within the fog services to facilitate responsive mass evacuations as well as open up new research challenges associated with analytics-embedding network and computation, which is critical for any disaster recovery activities.", "Keywords": "Disaster recovery ; Evacuation guidance ; Fog ; Fog computing ; Fog communications ; Collaborative analytics", "DOI": "10.1016/j.comcom.2021.07.020", "PubYear": 2021, "Volume": "179", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Computational Science and Mathematical Modelling, Coventry University, Coventry, CV1 2JH, UK;Department of Informatics, Universitas Negeri Surabaya, Surabaya, 60231, Indonesia;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Digital Technology, Birmingham City University, Birmingham, B4 7XG, UK;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Centre for Computational Science and Mathematical Modelling, Coventry University, Coventry, CV1 2JH, UK;School of Computing, Electronics and Mathematics, Coventry University, Coventry, CV1 5FB, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Communications and Networking, School of Advanced Technology, Xi’an Jiaotong-Liverpool University, Suzhou, 215123, China"}], "References": [{"Title": "Crowd sensing aware disaster framework design with IoT technologies", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; Ahmet Furkan Sonmez", "PubYear": 2020, "Volume": "11", "Issue": "4", "Page": "1709", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": ********, "Title": "Multi-grained and multi-layered gradient boosting decision tree for credit scoring", "Abstract": "<p>Credit scoring is an important process for banks and financial institutions to manage credit risk. Tree-based ensemble algorithms have made promising progress in credit scoring. However, tree-based ensemble algorithms lack representation learning, making them cannot well express the potential distribution of loan data. In this study, we propose a multi-grained and multi-layered gradient boosting decision tree (GBDT) for credit scoring. Multi-layered GBDT considers the advantages of the explicit learning process of tree-based model and the representation learning ability to discriminate good/bad applicants; multi-grained scanning augments original credit features while enhancing the representation learning ability of multi-layered GBDT. The experimental results on 6 credit scoring datasets show that the hierarchical structure can effectively reduce the intra-class distance and increase the inter-class distance of the credit scoring dataset. In addition, Multi-grained feature augmentation effectively increases the diversity of prediction and further improves the performance of credit scoring, providing more precise credit scoring results.</p>", "Keywords": "Credit scoring; Representation learning; Multi-layered; GBDT", "DOI": "10.1007/s10489-021-02715-6", "PubYear": 2022, "Volume": "52", "Issue": "5", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>an <PERSON>", "Affiliation": "Glorious Sun School of Business and Management, Donghua University, Shanghai, China"}, {"AuthorId": 2, "Name": "Hong Fan", "Affiliation": "Glorious Sun School of Business and Management, Donghua University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of automation, Nanjing University of Science Information & Technology, Nanjing, China"}], "References": [{"Title": "Machine learning and decision support system on credit scoring", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "14", "Page": "9809", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Adaptive stock trading strategies with deep reinforcement learning methods", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "538", "Issue": "", "Page": "142", "JournalTitle": "Information Sciences"}, {"Title": "Step-wise multi-grained augmented gradient boosting decision trees for credit scoring", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "97", "Issue": "", "Page": "104036", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 89951755, "Title": "Designing secure and lightweight user access to drone for smart city surveillance", "Abstract": "The Internet of drones (IoD) is a very useful application of the Internet of things (IoT) and it can help the daily life comfort through various functions including the smart city surveillance. The IoD can enhance the comfort to reach inaccessible and hard to access sites and can save lot of effort, time and cost. However, in addition to traditional threats, the IoD may suffer from new threats and requires customized methods to combat the security weaknesses. Very recently, <PERSON><PERSON><PERSON> et al. proposed a security solution for securing IoD application scenario and claimed its security. However, in this paper we show that their scheme cannot resist stolen verifier and traceability attacks. Moreover, an attacker with access to the verifier, can impersonate any user, drone or server of the system. An enhanced scheme is then proposed to cope with these weaknesses. The security claims of proposed scheme are endorsed by formal and informal security analysis. Moreover, the performance and security comparisons show that proposed scheme completes a cycle of authentication with a slight increase in computation time, but it offers all the required security features as compared with the scheme of <PERSON><PERSON><PERSON> et al.", "Keywords": "Three-factor authentication ; Smart-card capture attack ; Drone capture attack ; Provable security ; Smart City security ; IoT security ; Key-agreement ; Internet of Drones ; Authentications", "DOI": "10.1016/j.csi.2021.103566", "PubYear": 2022, "Volume": "80", "Issue": "", "JournalId": 739, "JournalTitle": "Computer Standards & Interfaces", "ISSN": "0920-5489", "EISSN": "1872-7018", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Software Engineering, International Islamic University, Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, COMSATS University Islamabad, Sahiwal Campus 57000, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "College of Computer & Information Sciences, King Saud University, Riyadh, Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Engineering, Shandong University of Science and Technology, Shandong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Faculty of Computing and Information Technology, King Abdulaziz University, Jeddah, Saudi Arabia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering and Architecture, Istanbul Gelisim University, Istanbul, Turkey;Corresponding author"}], "References": [{"Title": "A lightweight authentication and key agreement scheme for Internet of Drones", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "154", "Issue": "", "Page": "455", "JournalTitle": "Computer Communications"}, {"Title": "A clogging resistant secure authentication scheme for fog computing services", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "185", "Issue": "", "Page": "107731", "JournalTitle": "Computer Networks"}, {"Title": "GCACS-IoD: A certificate based generic access control scheme for Internet of drones", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "191", "Issue": "", "Page": "107999", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 89951780, "Title": "Localization in Wireless Sensor Networks with Mobile Anchor Node Path Planning Mechanism", "Abstract": "Determining the accurate locations of randomly deployed sensors in a given geographical region is a critical problem in the field of Wireless Sensor Networks . Various approaches have been proposed in the past to tackle this problem. Most of the existing techniques rely on the use of a beacon node which traverses throughout the region with limited power sources. Sensors use the beacon node positions for self localization . The trajectory of the beacon node has an influence on the accuracy, time and efficiency of the localization algorithm. In this paper, we propose Cosine Rule based Localization (CRL) algorithm for static trajectories of the mobile beacon node. Existing schemes use trilateration for position estimation of sensor nodes . The proposed method uses the Cosine rule on received beacon positions and distances obtained from RSSI in such a way that lines representing distances intersect at one point for the application of the Cosine rule. Simulation results reveal that CRL enables the unknown sensors to locate themselves with better accuracy in comparison with the existing trilateration technique for all the trajectories. Introduction Wireless Sensor Networks (WSNs) consist of a large number of sensors used for monitoring and controlling the environment. These sensors can be densely or sparsely scattered depending on the application [1]. Sensor networks can be used in health care, industry, home, disaster relief services, etc. The sensor node locations can be used in tracking, coverage and deployment domains of WSNs. Usually, sensors are equipped with a GPS device for tracking the place of origin of the information. The process of deriving the exact location of the unknown sensor using signal properties or network connectivity is known as localization. The detailed classification of localization algorithms is provided in [2]. Localization algorithms are generally classified on the basis of their triggering nature, range, computation and usage of an anchor. Based on the usage of signal properties, localization algorithms are classified into range based and range free localization techniques [1]. Range based techniques calculate positions by using time, angle and distance measurements of the arriving signal. Time of Arrival (TOA) [3], Time Difference of Arrival (TDOA) [4], Angle of Arrival (AOA) [5] and Received Signal Strength Indicator (RSSI) [6] are examples of range based localization. TOA, TDOA and AOA algorithms provide better accuracy but also require extra hardware [7]. In RSSI, the received signal strength is used for location estimation [8]. Further, the RSSI technique does not need any specialized hardware. Range free localization techniques use information about network connectivity, energy consumption and supervised area. Range free algorithms include neighborhood and hop counting techniques. These techniques use the connectivity information between sensors and the beacon node for localization. Range free techniques are more cost-effective as no extra hardware is required. Range free schemes can only provide a coarse-grained estimation of unknown sensors locations. Range based localization techniques provide more accuracy as compared to range free techniques [1]. In centralized localization, the central base station calculates the position of the unknown sensors with the help of the information collected from all the nodes. It can process large amounts of data and provide better accuracy. However, it also has disadvantages like single point of failure and scalability issues. In a distributed environment, a sensor calculates its own position by using anchors, relays or other sensor positions [9]. Anchor free approaches do not use anchor node positional information. Anchor free algorithms provide the relative node position with respect to other deployed nodes. The position estimation is carried out with the help of connectivity information between sensor nodes. Localization is carried out with complex calculations as more number of message exchanges are required to increase the accuracy. The techniques which use relative distance information about the beacon or anchor node positions are anchor based approaches [10]. Anchor based algorithms have higher accuracy, limited exchange of packets and position estimation is carried out by the anchor node without any complex computations. Most of the existing localization algorithms employ the anchor based approach to provide better accuracy. Localization methods based on the usage of anchor nodes can be categorized as: a) static anchor with static nodes [11], [12] b) mobile anchor with static nodes [1] [13] c) static anchor with mobile nodes [14] and d) mobile anchor with mobile nodes [1]. Mobile beacon assisted localization is the most promising approach for static sensor nodes with numerous applications. Generally, while implementing a cost effective WSN, it is important to minimize the number of beacons used for localization. The most effective way is to exploit a single mobile beacon for localization [15]. Beacon node broadcast its location at regular intervals. Unknown sensors can estimate their own position by using the beacon node positions. Introducing beacon nodes for localization, makes it crucial to identify an optimum beacon node trajectory such that all unknown sensors will get sufficient number of beacon node positions for localization. A few mobile anchor node based techniques have been proposed in the past. These can be classified into two categories: mobility model based localization and path planning scheme based localization. Mobility models are used to define the movement pattern of mobile anchor nodes. These models are classified into individual and group mobility models. The path planning schemes may be static or dynamic. In individual mobility models, anchor nodes move randomly and independently of each other. A group of anchors share a common mobility pattern in a group mobility model. When unknown sensors are assumed to be uniformly deployed, static path planning schemes are suitable. Dynamic path planning schemes make full use of the sensor density and distribution information of WSNs [1]. Scan, Double Scan and Hilbert trajectories are proposed in [16]. The Scan method traverses the region only along one dimension. The Double Scan technique starts traversing the region along one dimension. After completing one scan trajectory, it will repeat the process on the second dimension. Despite getting sufficient number of beacon node positions, non-collinear beacon positions are necessary to differentiate the side on which the sensor node is located [17]. The Scan method fails to satisfy this condition while the Double Scan technique ensures the retrieval of non-collinear beacon positions by doubling the length of the traversal. The collinearity problem in the Scan method and the increased length of the Double Scan method is tackled by Hilbert trajectory with an increasing number of turns taken by the beacon along the path [16]. LMAT traces the region in the form of an equilateral triangle and moves along the border region to locate sensors more accurately [18]. A path planning algorithm, SLMAT, combining LMAT and Scan algorithm is proposed in [13]. The path planning mechanism which follows a Z-curve is presented in [19]. It does not traverse the border region, but it still gets sufficient number of non-collinear beacon positions. H-curves derived its name from its property to connect different H-shaped paths while traversing the region [20]. The MAALRH trajectory starts at the center point of the region and moves by forming concentric hexagons while taking one diagonal step at a time [21]. It fails to obtain non-collinear beacon positions for unknown sensors located in the corner regions. In most of the exising trajectories all the beacon messages are transmitted using a single transmission power. Z-power [8] and Anchor-power [22] schemes provide a Transmission Power Control (TPC) strategy for a static mobile beacon trajectory. The Z-power scheme takes advantage of the Z-curve trajectory to adjust the transmission power. Anchor-power is the extension of Hilbert trajectory with increased number of transmission points for the anchor node. Three transmission power levels are defined in both trajectories to provide high localization success ratio and low energy consumption. The ∑ –Scan beacon trajectory using the combination of Z-curve and Hilbert trajectories is proposed in [23]. ∑ – Scan considers the arbitrary shaped region of interest by forming arbitrary rectangle to minimize the bounding region of the trajectory. D-connect trajectory presented in [15] defines two transmission powers as a function of distance. It guarantees the localization of all unknown sensor nodes from a certain geographical region with minimized trajectory length. Most of the existing algorithms use trilateration for position estimations [21]. The cosine rule based Bary-Hilbert localization algorithm for Hilbert trajectory is explained in [24] which provides better accuracy compared to trilateration technique. In this paper, we have proposed a new localization algorithm for localization of unknown sensors. After traversing the region with a given static trajectory, positions are estimated using RSSI and Cosine rule. The RSSI method is used to calculate the distance between the sender and receiver. We use Cosine rule for triangles formed between sensors and beacon positions. After getting the direction using the Cosine rule, we are able to locate the sensors using geometric calculations. Lines representing distances may not intersect at one point, leading to an increase in the localization error and lowering of the localization success ratio. The proposed scheme calculates the intersecting point for these non intersecting lines with the help of the strongest received signals. Moreover, we consider distances obtained from RSSI and directions by measuring angles for those distances using the Cosine rule to increase accuracy. Existing trajectories use trilateration wherein distances from anchor nodes are used for position estimation. The rest of the paper is organized as follows: Section 2 discusses the existing work on different localization algorithms and trajectories. Section 3 defines the problem and describes the proposed CRL algorithm. The simulation results are discussed in Section 4. Section 5 concludes the paper. Section snippets Related work There have been several research efforts on tackling the localization problem in Wireless Sensor Networks over the last decade. Accurate location information is required in various applications such as geographical information based routing, object tracking and coverage area management. Location services are also necessary for surveillance, intrusion detection, inventory and supply chain management. The data obtained from a deployed sensor is often meaningless without the knowledge of the Problem definition Given a region R , with m sensors deployed S = { s 1 , s 2 , s 3 , … , s m } , the objective is to locate all sensors with minimum localization error compared to existing trilateration technique with help of a single anchor node A . A sensor node s i , ( 1 ⩽ i ⩽ m ) located at ( x j , y j ) is said to be located by an anchor node with location ( x k , y k ) if and only if the sensor node lies within the communication range r of anchor node A . That is, ( x k - x j ) 2 + ( y k - y j ) 2 ⩽ r The localization error is the difference between the estimated and Performance evaluation The performance of the proposed localization algorithm is evaluated through a series of simulations in MATLAB. We have compared the performance of the proposed CLR algorithm with Accuracy-Priority Trilateration (APT) and Bary-Hilbert localization algorithms. The nearest beacons provide more RSS compared to other beacon positions. In APT technique, the locations of unknown sensors are calculated with the help of three nearest received messages from the anchor nodes for obtaining higher Conclusion and future work Mobile beacon-assisted localization is an efficient approach for localization in WSNs. In this paper we propose CRL algorithm that enables different trajectories to locate an unknown sensor when at least three non-collinear anchor node positions are available. It employs the Cosine rule on the obtained beacon positions for the localization of unknown sensor nodes. The performance of the CRL algorithm is compared with the performance of existing APT and Bary-Hilbert localization algorithms. The CRediT authorship contribution statement Ketan Sabale: Methodology, Software, Writing - original draft. S. Mini: Writing - review & editing, Supervision. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgment The authors would like to thank Ministry of Education, Government of India for providing financial support to carry out this research work. References (32) D. Koutsonikolas et al. Path planning of mobile landmarks for localization in wireless sensor networks Computer Communications (2007) K. Li et al. A mobile node localization algorithm based on an overlapping self-adjustment mechanism Information Sciences (2019) J. Lee et al. A new kernelized approach to wireless sensor network localization Information Sciences (2013) G. Han et al. A survey on mobile anchor node assisted localization in wireless sensor networks IEEE Communications Surveys and Tutorials (2016) K. Sabale et al. An analysis of path planning mechanisms in wireless sensor networks A. Harter et al. The anatomy of a context-aware application Wireless Networks (2002) L. Girod, D. Estrin, Robust range estimation using acoustic and multimodal sensing, in: IEEE/RSJ International... D. Niculescu, B. Nath, Ad hoc positioning system (APS) using AOA, in: Twenty-Second Annual Joint Conference of the IEEE... F. Shang et al. A location estimation algorithm based on RSSI vector similarity degree International Journal of Distributed Sensor Networks (2014) M. Wu, J. Zhao, W. Dai, X. Gui, A range-based adaptive target localization method in wireless sensor networks with... J. Rezazadeh et al. Transmission power adjustment scheme for mobile beacon-assisted sensor localization IEEE Transactions on Industrial Informatics (2018) A. El Assaf et al. Low-cost localization for multihop heterogeneous wireless sensor networks IEEE Transactions on Wireless Communications (2016) T. Bui et al. An accurate and energy-efficient localization algorithm for wireless sensor networks G. Han et al. A disaster management-oriented path planning for mobile anchor node-based localization in wireless sensor networks IEEE Transactions on Emerging Topics in Computing (2017) J. Rezazadeh et al. Efficient localization via middle-node cooperation in wireless sensor networks K. Sabale et al. Anchor node path planning for localization in wireless sensor networks Wireless Networks (2019) View more references Cited by (0) Recommended articles (4) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.08.004", "PubYear": 2021, "Volume": "579", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Goa, India;Corresponding author"}, {"AuthorId": 2, "Name": "S. Mini", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Goa, India"}], "References": [{"Title": "A Disaster Management-Oriented Path Planning for Mobile Anchor Node-Based Localization in Wireless Sensor Networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "8", "Issue": "1", "Page": "115", "JournalTitle": "IEEE Transactions on Emerging Topics in Computing"}]}, {"ArticleId": 89951797, "Title": "Pelive Floor Myofascisl Therapy is Associated with Improved VAS Pain Scores and FSFI Scores in Women with Dyspareunia 6 Months Post-partum", "Abstract": "<p>The incidence of dyspareunia at 6 months post-partum is very high. However, effective treatments are limited. Pelvic floor myofascial therapy is a useful method, we investigated the efficacy of it. 72 post-partum women with dyspareunia between 6 weeks and 3 months after delivering from July 2018 to June 2020 were researched. 30 received no treatment and 42 underwent 15 min, twice a week, totally 3 weeks. The scores of FSFI and VAS pain were completed at enrollment and at 6 months. In the observation group, the VAS score had no significant difference at 6 months, but there was a higher FSFI score at 6 months (17.0, IQR 15.3–18.0 vs. 18.4, IQR 15.8–19.9, p = 0.06). But for the treatment group, at the time of 6 moths, VAS scores were significantly lower and FSFI scores were significantly higher (2.0, IQR 1.5–2.8 vs. 7.3, IQR 6.0–8.4, p < .0001 and 20.4, IQR 18.1–21.8 vs. 18.4, IQR 15.8–19.9, p < .01). So pelvic floor myofascisl improves pelvic floor muscle function, exercise appears to have positive effects on female sexual function.</p>", "Keywords": "Dyspareunia; Pelive floor myofascisl therapy; Post-partum; FSFI", "DOI": "10.1007/s11063-021-10609-4", "PubYear": 2023, "Volume": "55", "Issue": "1", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Guangzhou Women and Children’s Medical Center, Guangzhou Medical University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Guangzhou Women and Children’s Medical Center, Guangzhou Medical University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Guangzhou Women and Children’s Medical Center, Guangzhou Medical University, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Guangzhou Women and Children’s Medical Center, Guangzhou Medical University, Guangzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Guangzhou Women and Children’s Medical Center, Guangzhou Medical University, Guangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Guangzhou Women and Children’s Medical Center, Guangzhou Medical University, Guangzhou, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Guangzhou Women and Children’s Medical Center, Guangzhou Medical University, Guangzhou, China"}], "References": []}, {"ArticleId": 89951849, "Title": "Just-in-time-learning based prediction model of BOF endpoint carbon content and temperature via vMF mixture model and weighted extreme learning machine", "Abstract": "Basic oxygen furnace (BOF) steelmaking is a complicated physical chemical process, in which the endpoint carbon content and temperature are two important indicators. In BOF steelmaking, the quality of raw materials varies greatly between different batches, which would lead to the inaccurate predictions for these two indicators. Additionally, there are imbalance problems in production process data of BOF steelmaking. For the time-varying problem, a novel similarity criterion based on von-Mises Fisher mixture model (VMM) is proposed in this paper and applied for sample selection of just-in-time-learning (JITL)-based endpoint carbon content and temperature prediction model. The V-shaped transfer function is utilized to develop weighted extreme learning machine (WELM) as local regression model to address the imbalance problems. The performance of the proposed methods is compared with other methods under JITL framework. The experimental results show that the proposed online model can provide a more accurate prediction.", "Keywords": "Basic oxygen furnace ; Just-in-time-learning ; Von-Mises Fisher mixture model ; Weighted extreme learning machine", "DOI": "10.1016/j.compchemeng.2021.107488", "PubYear": 2021, "Volume": "154", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Yunnan Key Laboratory of Artificial Intelligence, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Yunnan Key Laboratory of Artificial Intelligence, Kunming University of Science and Technology, Kunming, China;Corresponding author at: Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Yunnan Key Laboratory of Artificial Intelligence, Kunming University of Science and Technology, Kunming, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming, China;Yunnan Key Laboratory of Artificial Intelligence, Kunming University of Science and Technology, Kunming, China"}], "References": [{"Title": "A just-in-time-learning based two-dimensional control strategy for nonlinear batch processes", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "220", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 89951852, "Title": "Wind-up precision pump for portable microfluidics", "Abstract": "Herein, a wind-up precision pump is proposed in which the pump is powered by the manual winding of a helical spring, similar to that in a mechanical watch. The unwinding of the wound helical spring provides a constant torque. The connecting gears at the spring convert the rotational movement of the spring into linear motion to generate a constant force for pushing a syringe. Incorporating the high-precision mechanism of a mechanical watch into the pump can generate precise, steady, and pulse-less flows over the range of 0.1 mL/h to 0.8 mL/h. The flow rate can be easily adjusted by changing the gear ratio, syringe diameter, and balance wheel moment of inertia. The wind-up pump can perform the same functions as a syringe pump, but is smaller, lightweight, and disposable, making it suitable for field-portable microfluidic applications. Thus, it is named precise, accurate, little microfluidic (PALM) pump. The utility of the PALM pump is demonstrated through the on-site fabrication of injectable radioactive hydrogel microparticles for local radiotherapy and monitoring of their anti-cancer effects on HeLa cells. The PALM pump can not only completely replace syringe pumps, but can also contribute greatly to the widespread use of microfluidic platforms.", "Keywords": "Droplet microfluidics ; 3D printed ; Syringe pump ; Non-electric ; Portable", "DOI": "10.1016/j.snb.2021.130592", "PubYear": 2021, "Volume": "347", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Won Han", "Affiliation": "Department of Biomedical Engineering, Pukyong National University, Busan, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomaterials Science (BK21 Four Program), Pusan National University, Miryang, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Department of Electronic Engineering, Hanyang University, Seoul, Republic of Korea"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomaterials Science (BK21 Four Program), Pusan National University, Miryang, Republic of Korea;Corresponding authors"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Department of Electronic Engineering, Hanyang University, Seoul, Republic of Korea;Corresponding authors"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Biomedical Engineering, Pukyong National University, Busan, Republic of Korea;Corresponding authors"}], "References": []}, {"ArticleId": ********, "Title": "An anomalous co‐operative trust & PG‐DRL based vampire attack detection & routing", "Abstract": "<p>Sensor nodes in WSN play a vital role in communication, IoTs and many other emergencies too. However, the energy consumption of nodes is a major setback to these, which incites various malicious nodes/attacks. This article studies and presents the solution to Vampire attack- one of those kinds of attacks. It depletes the energy by route elongation of data transmission. This article has suggested a novel two-fold mechanism to detect the attack by integrating co-operation trust mechanism and the mitigation of the attack by selecting the secure route by policy gradient-deep reinforcement learning. The designed protocol also ensures the selection of a secure hop even in the presence of the vampire attack. The results are compared with various other existing state-of-the-art schemes and have improved the detection ratio by 20% compared to the forecasting methods applied for detecting the vampire node's behavior. The network lifetime has also improved by 3% than the benchmark dynamic source routing.</p>", "Keywords": "PG-DRL;reinforcement learning;sensor network;vampire attack", "DOI": "10.1002/cpe.6557", "PubYear": 2022, "Volume": "34", "Issue": "3", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, <PERSON> Lal Institute of Engineering and Technology (JMIT), Yamunanagar, India; Uttarakhand Technical University, Dehradun, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Applications, <PERSON><PERSON>d <PERSON> Pant Institute of Engineering and Technology, Pauri Garhwal, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, College of Engineering Roorkee (COER), Roorkee, India"}], "References": [{"Title": "An energy-efficient distributed adaptive cooperative routing based on reinforcement learning in wireless multimedia sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "178", "Issue": "", "Page": "107313", "JournalTitle": "Computer Networks"}, {"Title": "An energy-efficient distributed adaptive cooperative routing based on reinforcement learning in wireless multimedia sensor networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "178", "Issue": "", "Page": "107313", "JournalTitle": "Computer Networks"}, {"Title": "VNE solution for network differentiated QoS and security requirements: from the perspective of deep reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "6", "Page": "1061", "JournalTitle": "Computing"}, {"Title": "VNE solution for network differentiated QoS and security requirements: from the perspective of deep reinforcement learning", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "103", "Issue": "6", "Page": "1061", "JournalTitle": "Computing"}]}, {"ArticleId": ********, "Title": "Special issue on ‘‘artificial intelligence in cloud computing’’", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00607-021-00985-z", "PubYear": 2023, "Volume": "105", "Issue": "3", "JournalId": 10374, "JournalTitle": "Computing", "ISSN": "0010-485X", "EISSN": "1436-5057", "Authors": [{"AuthorId": 1, "Name": "<PERSON> Mohammed", "Affiliation": "Lakehead University, Thunder Bay, Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Chiao Tung University, Hsinchu City, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "ISEP, Polytechnic of Porto, Porto, Portugal"}], "References": []}, {"ArticleId": 89952521, "Title": "Assessing in real-time the credibility of Arabic blog posts using traditional and deep learning models", "Abstract": "<p>Blogging websites are growing globally with a fast pace allowing online users to express their views and engage in discussions related to various domains such as politics, technology, and lifestyle. While some blog posts state facts and genuine personal views, others tend to spread rumors or support certain propagandas. This has triggered the need to develop models to automatically rate the credibility of blog posts. Arabic blog posts in particular, have recently drawn a lot of attention following the recent uprisings in the Arab world. To the best of our knowledge, little work has been done to predict the credibility of Arab blogs, which faces many challenges including: the subjectivity and complexity inherent in assessing credibility, the rich morphology of the Arabic language, and the lack of the appropriate lexicons and corpora to conduct credibility analysis. In this paper, we focus on developing a fully automated system to assess the credibility of Arabic blog posts. We collected Arabic blog posts, annotated them, extracted and reduced the important features, then employed various machine learning models (e.g., Support Vector Machines) and deep learning models (e.g., Long Short-Term Memory—LSTM and Convolution Neural Network—CNN), under various input settings. We conclude that LSTM performs the best with accuracy reaching 74%, when the input is composed of the full blog posts along with a set of syntactic and morphological features. The incorporation of hand-crafted features and the addition of CNN to try and extract complex features did not improve the accuracy.</p>", "Keywords": "Blog credibility; Corpus development; Machine learning; Deep learning", "DOI": "10.1007/s13278-021-00782-8", "PubYear": 2021, "Volume": "11", "Issue": "1", "JournalId": 16784, "JournalTitle": "Social Network Analysis and Mining", "ISSN": "1869-5450", "EISSN": "1869-5469", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, American University of Beirut, Beirut, Lebanon"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, Prince <PERSON> University, Al-Khobar, Saudi Arabia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, American University of Beirut, Beirut, Lebanon"}], "References": []}, {"ArticleId": 89952644, "Title": "Modelling and comparison of different types of random fields: case of a real earth dam", "Abstract": "<p>The random field (RF) theory is widely used for describing the soil spatial variability in geotechnical engineering. This article presents soil variability modellings by using different types of RF-based on the available measurements of an earth dam. The effects of these RFs on dam reliability are investigated as well. The studied dam is well-documented, and there are many geo-localized measurements for the dry density. These measurements are firstly used to estimate the basic parameters of unconditional-stationary RFs and are then explored to define two more complex RFs (one is conditional RF and the other considers the mean variation with depth). The three mentioned types of RFs are all implemented in the same reliability analysis procedure for comparison. The results demonstrate that using different RFs for soil spatial modelling would induce insignificant differences in terms of reliability results if the dam construction was well controlled (careful selection for the construction material and controlled compaction). Therefore, a simple RF (unconditional-stationary) is enough to obtain satisfactory results in the case of carefully controlled dams during their construction. Otherwise, conditional RFs are recommended if more accurate results are needed, given that this type of RF is conditioned on the available data and can consider the non-stationarity of a soil property.</p>", "Keywords": "Reliability analysis; Earth dam; Conditional random field; Nonstationary random field; Monte Carlo simulation", "DOI": "10.1007/s00366-021-01495-4", "PubYear": 2022, "Volume": "38", "Issue": "S5", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Guo", "Affiliation": "Université Grenoble Alpes, CNRS, Grenoble INP (Institute of Engineering Université Grenoble Alpes), 3SR, Grenoble, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Université Grenoble Alpes, CNRS, Grenoble INP (Institute of Engineering Université Grenoble Alpes), 3SR, Grenoble, France; Antea Group, Antony, France"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "INRAE, Aix-Marseille University, RECOVER, Aix-en-Provence, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "INRAE, Aix-Marseille University, RECOVER, Aix-en-Provence, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Université Clermont Auvergne, Institut Pascal, CNRS, Clermont-Ferrand, France"}], "References": [{"Title": "Non-intrusive reliability analysis of unsaturated embankment slopes accounting for spatial variabilities of soil hydraulic and shear strength parameters", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "38", "Issue": "S1", "Page": "1", "JournalTitle": "Engineering with Computers"}]}, {"ArticleId": ********, "Title": "Research on workpiece location algorithm based on improved SSD", "Abstract": "Purpose \nTo improve production efficiency, industrial robots are expected to replace humans to complete the traditional manual operation on grasping, sorting and assembling workpieces. These implementations are closely related to the accuracy of workpiece location. However, workpiece location methods based on conventional machine vision are sensitive to the factors such as light intensity and surface roughness. To enhance the robustness of the workpiece location method and improve the location accuracy, a workpiece location algorithm based on improved Single Shot MultiBox Detector (SSD) is proposed.\n \n \n Design/methodology/approach \nThe proposed algorithm integrates a weighted bi-directional feature pyramid network into SSD. A feature fusion architecture is structured by the combination of low-resolution, strong semantic features and high-resolution, weak semantic features. The architecture is built through a top-down pathway, bottom-up pathway, lateral connections and skip connections. To avoid treating all features equally, learnable weights are introduced into each feature layer to characterize its importance. More detailed information from the low-level layers is injected into the high-level layers, which could improve the accuracy of workpiece location.\n \n \n Findings \nIt is found that the maximum location error at the center point calculated from the proposed algorithm is decreased by more than 22% compared with that of the SSD algorithm. Besides, the average location error evolves a decrease by at least 5%. In the trajectory prediction experiment of the workpiece center point, the results of the proposed algorithm demonstrate that the average location error is below 0.13 mm and the maximum error is no more than 0.23 mm.\n \n \n Originality/value \nIn this work, a workpiece location algorithm based on improved SSD is developed to extract the center point of the workpiece. The results demonstrate that the proposed algorithm is beneficial for workpiece location. The proposed algorithm can be readily used in a variety of workpieces or adapted to other similar tasks.", "Keywords": "Machine vision;Robot vision;Image Processing;Neural networks", "DOI": "10.1108/IR-01-2021-0005", "PubYear": 2022, "Volume": "49", "Issue": "1", "JournalId": 4481, "JournalTitle": "Industrial Robot: An International Journal", "ISSN": "0143-991X", "EISSN": "1758-5791", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mechanical and Automative Engineering, South China University of Technology , Guangzhou, China"}, {"AuthorId": 2, "Name": "Mingheng Fu", "Affiliation": "School of Mechanical and Automative Engineering, South China University of Technology , Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical and Automative Engineering, South China University of Technology , Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON> <PERSON>", "Affiliation": "Guangzhou CNC Equipment Co., Ltd., Guangzhou, China"}], "References": []}, {"ArticleId": 89952967, "Title": "Achieve efficient position-heap-based privacy-preserving substring-of-keyword query over cloud", "Abstract": "The cloud computing technique, which was initially used to mitigate the explosive growth of data, has been required to take both data privacy and users’ query functionality into consideration. Symmetric searchable encryption (SSE) is a popular solution to supporting efficient keyword queries over encrypted data in the cloud. However, most of the existing SSE schemes focus on the exact keyword query and cannot work well when the user only remembers the substring of a keyword, i.e., substring-of-keyword query. This paper aims to investigate this issue by proposing an efficient and privacy-preserving substring-of-keyword query scheme over cloud. First, we employ the position heap technique to design a novel tree-based index to match substrings with corresponding keywords. Then based on the tree-based index, we introduce our substring-of-keyword query scheme, which contains two consecutive phases. The first phase queries the keywords that match a given substring, and the second phase queries the files that match a keyword in which people are really interested. In addition, detailed security analysis and experimental results demonstrate the security and efficiency of our proposed scheme.", "Keywords": "Cloud computing ; Outsourced encrypted data ; Substring-of-keyword query ; Position heap ; Efficiency", "DOI": "10.1016/j.cose.2021.102432", "PubYear": 2021, "Volume": "110", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "The Information Security and National Computing Grid Laboratory, Southwest Jiaotong University, Chengdu, 611756, China;The Canadian Institute for Cybersecurity, Faculty of Computer Science, University of New Brunswick, Fredericton, E3B 5A3 Canada"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON> Lu", "Affiliation": "The Canadian Institute for Cybersecurity, Faculty of Computer Science, University of New Brunswick, Fredericton, E3B 5A3 Canada;Corresponding author"}, {"AuthorId": 3, "Name": "Yan<PERSON> Zheng", "Affiliation": "The Canadian Institute for Cybersecurity, Faculty of Computer Science, University of New Brunswick, Fredericton, E3B 5A3 Canada"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer and Information Engineering, Zhejiang Gongshang University, Hangzhou, 310018 China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "The Tsinghua Shenzhen International Graduate School, Tsinghua University, Shenzhen, 518055, China;The PCL Research Center of Networks and Communications, Peng Cheng Laboratory, Shenzhen, 518055, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "The Information Security and National Computing Grid Laboratory, Southwest Jiaotong University, Chengdu, 611756, China"}], "References": []}, {"ArticleId": 89952971, "Title": "TDCMF: Two-dimensional complex mass function with its application in decision-making", "Abstract": "How to efficiently handle uncertain information has always been a problem which puzzles some practitioners and researchers. The complex evidence theory has many applications in handling uncertain information. However, the complex evidence theory is incapable of considering the situation that when the system is becoming more and more complex. In this paper, therefore, a two-dimensional complex mass function (TDCMF) is proposed. The TDCMF are modeled as complex numbers and some basic concepts are newly defined. Moreover, the proposed TDCMF considers not only the support degree but also non-support degree and hesitation degree. Based on that, a combination rule of TDCMF is proposed. Then an algorithm for decision-making based on the proposed TDCMF and combination rule is proposed. Finally, two applications of the proposed algorithm are carried out to address the issue of target identification and medical diagnosis in a comparative way, which validates the rationality of the proposed algorithm.", "Keywords": "Uncertainty information ; Two-dimensional complex mass function ; Information fusion ; Decision-making ; Target identification ; Medical diagnosis", "DOI": "10.1016/j.engappai.2021.104409", "PubYear": 2021, "Volume": "105", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Information Science, Southwest University, Chongqing 400715, China;School of Big Data and Software Engineering, Chongqing University, Chongqing 401331, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Big Data and Software Engineering, Chongqing University, Chongqing 401331, China;Corresponding author"}], "References": [{"Title": "A Deng-Entropy-Based Evidential Reasoning Approach for Multi-expert Multi-criterion Decision-Making with Uncertainty", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "1", "Page": "1281", "JournalTitle": "International Journal of Computational Intelligence Systems"}, {"Title": "Self-adaptive combination method for temporal evidence based on negotiation strategy", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Uncertainty measure in evidence theory", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Evidential combination of augmented multi-source of information based on domain adaptation", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "11", "Page": "1", "JournalTitle": "Science China Information Sciences"}, {"Title": "Information Volume of Mass Function", "Authors": "<PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "6", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "Information Volume of Fuzzy Membership Function", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "1", "Page": "1", "JournalTitle": "International Journal of Computers Communications & Control"}, {"Title": "CaFtR: A Fuzzy Complex Event Processing Method", "Authors": "<PERSON><PERSON>", "PubYear": 2022, "Volume": "24", "Issue": "2", "Page": "1098", "JournalTitle": "International Journal of Fuzzy Systems"}, {"Title": "A new hesitant fuzzy linguistic approach for multiple attribute decision making based on <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> evidence theory", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "105897", "JournalTitle": "Applied Soft Computing"}, {"Title": "Quantum model of mass function", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "35", "Issue": "2", "Page": "267", "JournalTitle": "International Journal of Intelligent Systems"}, {"Title": "Multiple criteria group decision making based on group satisfaction", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "309", "JournalTitle": "Information Sciences"}, {"Title": "Multi-classifier information fusion in risk analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "60", "Issue": "", "Page": "121", "JournalTitle": "Information Fusion"}, {"Title": "Generalization of <PERSON><PERSON><PERSON> theory: A complex mass function", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "10", "Page": "3266", "JournalTitle": "Applied Intelligence"}, {"Title": "Multi-level information fusion to alleviate network congestion", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "248", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 89952972, "Title": "Universal transformer Hawkes process with adaptive recursive iteration", "Abstract": "Asynchronous events sequences are widely distributed in the natural world and human activities, such as earthquakes records, users’ activities in social media, and so on. How to distill the information from these seemingly disorganized data is a persistent topic that researchers focus on. One of the most useful models is the point process model, and on the basis, the researchers obtain many noticeable results. Moreover, in recent years, point process models on the foundation of neural networks, especially recurrent neural networks (RNN) are proposed and compare with the traditional models, their performance is greatly improved. Enlighten by transformer model, which can learn sequence data efficiently without recurrent and convolutional structure, transformer Hawkes process comes out, and achieves state-of-the-art performance. However, there is some research proving that the re-introduction of recursive calculations in transformer can further improve transformer’s performance. Thus, we come out with a new kind of transformer Hawkes process model, universal transformer Hawkes process (UTHP), which contains both recursive mechanism and self-attention mechanism, and to improve the local perception ability of the model, we also introduce convolutional neural network (CNN) in the position-wise-feed-forward part. We conduct experiments on several datasets to validate the effectiveness of UTHP and explore the changes after the introduction of the recursive mechanism. These experiments on multiple datasets demonstrate that the performance of our proposed new model has a certain improvement compared with the previous state-of-the-art models.", "Keywords": "<PERSON><PERSON> process ; Universal transformer Hawkes process ; Convolutional neural network ; Recursive calculation ; Self-attention mechanism", "DOI": "10.1016/j.engappai.2021.104416", "PubYear": 2021, "Volume": "105", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, College of Information Science and Engineering, China University of Petroleum, Beijing Campus (CUP), Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, College of Information Science and Engineering, China University of Petroleum, Beijing Campus (CUP), Beijing, China;Correspondence to: 260 mailbox China University of Petroleum, Changping District Beijing, 102249, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, College of Information Science and Engineering, China University of Petroleum, Beijing Campus (CUP), Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, College of Information Science and Engineering, China University of Petroleum, Beijing Campus (CUP), Beijing, China"}], "References": [{"Title": "Survival analysis of failures based on Hawke<PERSON> process with Weibull base intensity", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "103709", "JournalTitle": "Engineering Applications of Artificial Intelligence"}]}, {"ArticleId": 89953186, "Title": "Investigating Differences in Gaze and Typing Behavior Across Writing Genres", "Abstract": "Writing is one of the most common activities undertaken on a computer, and the activity of writing has been widely studied. Given that writing is an intensively cognitive process, it makes sense that the type of writing that is being produced would have an effect on the writer’s gaze and typing behaviors. However, only a few studies have explored this relationship. In this paper, we study the gaze-typing behaviors, specifically, the coordination between eye gaze and typing dynamics, of writers who are producing original articles in different genres: reminiscent, logical and creative. Our study focuses on Chinese typing, particularly via the Pinyin input method, which generates text via a two step method, and requires additional cognitive processes compared to typing in phonographic languages such as English. Our study involves 46 native Chinese speakers of varying ages from children to elderly. Our method deploys statistics- and sequence-based features to infer the mental state of the author during the writing process. The statistics-based features focus on modeling the overall gaze-typing behaviors during the process and the sequence-based features focus on the transition of the gaze-typing behaviors as the piece of writing progresses. Using a linear support-vector machine, we achieve an overall accuracy over 88% for the article-genre detection by using a leave-one-subject-out cross-validation evaluation. Additional information Funding This work was partially supported by the Hong Kong Research Grant Council and the Hong Kong Polytechnic University under Grants PolyU 5222/13E and 156002/19H. Notes on contributors <PERSON> is a Ph.D. Candidate at the Hong Kong Polytechnic University. His research interests are human–computer interaction, data mining and affective computing. <PERSON> is a postdoctoral fellow in the Department of Computing at the Hong Kong Polytechnic University. He received his Ph.D. degree from the Hong Kong Polytechnic University in 2019. His research interests include multimedia, human-centered computing, human-computer interaction, and machine learning. Grace <PERSON>ai Grace Ngai received her Ph.D. from Johns Hopkins University in Computer Science in 2001. She is currently the Head of the Service-Learning and Leadership Office and associate professor at the Department of Computing at Hong Kong Polytechnic University. Her research interests are in affective computing, human-computer interaction and education. Hong Va Leong Hong Va Leong received his PhD degree from University of California at Santa Barbara. He is currently a faculty member in Department of Computing, Hong Kong Polytechnic University. He has served on the OC and PC of numerous international conferences. His research areas include mobile computing and affective computing.", "Keywords": "", "DOI": "10.1080/10447318.2021.1952801", "PubYear": 2022, "Volume": "38", "Issue": "6", "JournalId": 1896, "JournalTitle": "International Journal of Human–Computer Interaction", "ISSN": "1044-7318", "EISSN": "1532-7590", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computing, Hong Kong Polytechnic University, Hong Kong"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computing, Hong Kong Polytechnic University, Hong Kong"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computing, Hong Kong Polytechnic University, Hong Kong"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computing, Hong Kong Polytechnic University, Hong Kong"}], "References": []}, {"ArticleId": 89953199, "Title": "Using Blockchain to Ensure Trust between Donor Agencies and NGOs in Under-Developed Countries", "Abstract": "<p>Non-governmental organizations (NGOs) in under-developed countries are receiving funds from donor agencies for various purposes, including relief from natural disasters and other emergencies, promoting education, women empowerment, economic development, and many more. Some donor agencies have lost their trust in NGOs in under-developed countries, as some NGOs have been involved in the misuse of funds. This is evident from irregularities in the records. For instance, in education funds, on some occasions, the same student has appeared in the records of multiple NGOs as a beneficiary, when in fact, a maximum of one NGO could be paying for a particular beneficiary. Therefore, the number of actual beneficiaries would be smaller than the number of claimed beneficiaries. This research proposes a blockchain-based solution to ensure trust between donor agencies from all over the world, and NGOs in under-developed countries. The list of National IDs along with other keys would be available publicly on a blockchain. The distributed software would ensure that the same set of keys are not entered twice in this blockchain, preventing the problem highlighted above. The details of the fund provided to the student would also be available on the blockchain and would be encrypted and digitally signed by the NGOs. In the case that a record inserted into this blockchain is discovered to be fake, this research provides a way to cancel that record. A cancellation record is inserted, only if it is digitally signed by the relevant donor agency.</p>", "Keywords": "blockchain; ensuring trust; NGOs; encryption blockchain ; ensuring trust ; NGOs ; encryption", "DOI": "10.3390/computers10080098", "PubYear": 2021, "Volume": "10", "Issue": "8", "JournalId": 41644, "JournalTitle": "Computers", "ISSN": "", "EISSN": "2073-431X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Systems, Institute of Business Management, Karachi 75190, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Information Systems, Institute of Business Management, Karachi 75190, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Information Systems, Institute of Business Management, Karachi 75190, Pakistan↑Author to whom correspondence should be addressed. Academic Editors: <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Management, Canadian University of Dubai, Dubai 117781, United Arab Emirates"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of IT, Skyline University College, Sharjah 1797, United Arab Emirates"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of IT, Skyline University College, Sharjah 1797, United Arab Emirates↑Center for Cyber Security, Faculty of Information Science and Technology, Universiti Kebansaan Malaysia, Bangi 43600, Malaysia"}], "References": [{"Title": "A framework to make charity collection transparent and auditable using blockchain technology", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "83", "Issue": "", "Page": "106588", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Block-ED: The Proposed Blockchain Solution for Effectively Utilising Educational Resources", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "1", "Page": "1", "JournalTitle": "Applied Computer Systems"}]}, {"ArticleId": 89953208, "Title": "Applying cross-data set identity reasoning for producing URI embeddings over hundreds of RDF data sets", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJMSO.2021.10040245", "PubYear": 2021, "Volume": "15", "Issue": "1", "JournalId": 11390, "JournalTitle": "International Journal of Metadata, Semantics and Ontologies", "ISSN": "1744-2621", "EISSN": "1744-263X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89953291, "Title": "Development of a novel motion capture and gait analysis system for rat locomotion", "Abstract": "This paper proposes a marker-based motion capture system for rat motion detection and gait analysis. Motion capture in small animals such as rodents is more challenging than in humans because of their small bodies and rapid motion. Existing algorithms have poor applicability in rat motion capture in environments outside the studio. Moreover, gait analysis targeting on the rat is not performed by existing motion capture software. Our method consists of four procedures. First, Region of Interest (ROI) is extracted from the background using the inter-frame difference method and a depth filter. Second, a double-threshold marker detection method is used to detect markers in ROI and a marker shape filter is used to classify the markers. Third, a marker corrector is designed to modify missing and incorrect markers. Finally, a deep learning network is used to analyse the gait trajectory to classify rat as healthy, injured, or rehabilitated. The experimental result shows that marker recognition accuracy is 99.33%, higher than that of most existing software. The validation accuracy of the network is 100% and the loss is 0.0001. This method is conductive to the development of motion capture systems for small animals and research into the gait kinematics of rodents. GRAPHICAL", "Keywords": "Rodents kinematics ; motion capture ; gait analysis ; image processing ; artificial neural networks", "DOI": "10.1080/01691864.2021.1957013", "PubYear": 2021, "Volume": "35", "Issue": "16", "JournalId": 5595, "JournalTitle": "Advanced Robotics", "ISSN": "0169-1864", "EISSN": "1568-5535", "Authors": [{"AuthorId": 1, "Name": "Chuankai Dai", "Affiliation": "Beijing Institute of Technology, Beijing, People's Republic of China;Beijing Advanced Innovation Center for Intelligent Robots and Systems, Beijing, People's Republic of China"}, {"AuthorId": 2, "Name": "Xiaodong Lyu", "Affiliation": "Beijing Institute of Technology, Beijing, People's Republic of China;Beijing Advanced Innovation Center for Intelligent Robots and Systems, Beijing, People's Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing, People's Republic of China;Beijing Advanced Innovation Center for Intelligent Robots and Systems, Beijing, People's Republic of China"}, {"AuthorId": 4, "Name": "Jiping <PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing, People's Republic of China;Beijing Advanced Innovation Center for Intelligent Robots and Systems, Beijing, People's Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing, People's Republic of China;Beijing Advanced Innovation Center for Intelligent Robots and Systems, Beijing, People's Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Technology, Beijing, People's Republic of China;Beijing Advanced Innovation Center for Intelligent Robots and Systems, Beijing, People's Republic of China"}], "References": []}, {"ArticleId": ********, "Title": "A chance constrained programming model and an improved large neighborhood search algorithm for the electric vehicle routing problem with stochastic travel times", "Abstract": "<p>The Electric Vehicle Routing Problem (EVRP) is an extension to the well-known Vehicle Routing Problem (VRP) where the fleet consists of electric vehicles, which may need to visit recharging stations while servicing the customers due to their battery capacities. This paper solves the Electric Vehicle Routing Problem with Stochastic Travel Times (EVRPSTT) by proposing a Chance Constrained Programming (CCP) Model, as well as a new scheme based on an Improved Large Neighborhood Search (ILS) algorithm and a Monte Carlo Sampling (MCS) procedure. The proposed approach is firstly tested in the deterministic environment using the EVRP benchmark data set, where the numerical results show that this approach is able to provide EVRP optimal solutions, within a very short computational time, for 39 out of 48 used benchmark instances with 20, 50, 75 and 100 customers. Thereafter, to show the efficiency of the proposed approach for solving the CCP model of the EVRPSTT, others tests are performed on the same set of instances, while taking into consideration a large number of scenarios.</p>", "Keywords": "Electric vehicle routing problem; Stochastic travel times; Chance constrained programming; Large neighborhood search; Monte carlo sampling", "DOI": "10.1007/s12065-021-00648-0", "PubYear": 2023, "Volume": "16", "Issue": "1", "JournalId": 5202, "JournalTitle": "Evolutionary Intelligence", "ISSN": "1864-5909", "EISSN": "1864-5917", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Logistics, Transport and Road Safety Research Team, Hassania School of Public Works, Casablanca, Morocco"}], "References": [{"Title": "A simulation-based heuristic for the electric vehicle routing problem with time windows and stochastic waiting times at recharging stations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "125", "Issue": "", "Page": "105060", "JournalTitle": "Computers & Operations Research"}]}, {"ArticleId": ********, "Title": "Improving the staircase approximation for wettability implementation of phase-field model: Part 1 – Static contact angle", "Abstract": "In this study, two schemes to treat fluid-solid interaction on complex boundaries are proposed and solved in the framework of lattice Boltzmann method. The schemes are applied on the phase-field-based wettability implementation methods for binary and ternary fluids to simulate static contact angle on a circular surface generated by the staircase approximation. For the binary system, surface-energy and geometric wetting conditions are adopted, while for the ternary system, surface-energy model with two discretization schemes resulting in explicit and implicit wetting conditions are developed and examined. It is proven that the implicit wetting condition approximating the value of order parameters on the boundary node, as the average of their value between the neighboring fluid node and solid node, enhances the accuracy of predicted contact angles. For modeling of contact angle on the circular surface and identifying the direction of normal vectors required for incorporating wetting condition, the gradient of fluid-solid field is calculated. Two methods namely, round-off and interpolation describing how the information of neighboring nodes given the direction of normal vector on the boundary nodes should be used, are discussed. It is illustrated that results of the interpolation method exhibit a good agreement with the analytical solution as opposed to the round-off when tested in the case of static contact angle on a circular surface for both binary and ternary systems. Finally, it is shown incorporating different wetting conditions with the appropriate way of handling normal vectors and in combination with the conservative phase-field model can model static contact angle at high-density ratios for both binary and ternary fluids with good accuracy.", "Keywords": "Phase-field ; <PERSON><PERSON><PERSON> method ; Wetting condition ; Surface-energy model ; Contact angle ; Staircase approximation", "DOI": "10.1016/j.camwa.2021.07.013", "PubYear": 2021, "Volume": "98", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mechanical, Process and Energy Engineering, School of Engineering & Physical Sciences, Heriot-Watt University, Edinburgh EH14 4AS, UK"}, {"AuthorId": 2, "Name": "Sorush Khajepor", "Affiliation": "Institute for Materials and Processes, School of Engineering, The University of Edinburgh, Edinburgh EH9 3FB, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Mechanical, Process and Energy Engineering, School of Engineering & Physical Sciences, Heriot-Watt University, Edinburgh EH14 4AS, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Mechanical, Process and Energy Engineering, School of Engineering & Physical Sciences, Heriot-Watt University, Edinburgh EH14 4AS, UK;Corresponding author"}], "References": [{"Title": "Modified curved boundary scheme for two-phase lattice Boltzmann simulations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "208", "Issue": "", "Page": "104638", "JournalTitle": "Computers & Fluids"}]}, {"ArticleId": 89953936, "Title": "Spectrum-Free and Meshless <PERSON>vers of Parabolic PDEs", "Abstract": "<p>We propose a novel collocation method with Radial Basis Functions for the solution of the inhomogeneous parabolic equation \\((\\partial _{t}+\\mathcal {L})u(\\cdot ,t)=f\\) on \\(\\Omega \\subseteq \\mathbb {R}^{d}\\) , with \\(\\mathcal {L}\\) elliptic operator. As original contribution, we rewrite the solution in terms of the exponential operator \\(\\exp (-t\\mathcal {L})\\) , which is then computed through the Padè-Chebyshev approximation of the 1D Gaussian function. The resulting meshless solver uniformly converges to the ground-truth solution, as the degree of the rational polynomial increases, and is independent of the evaluation of the spectrum of \\(\\mathcal {L}\\) (i.e., spectrum-free ), of the discretisation of the temporal derivative, and of user-defined parameters. Since the solution is approximated as a linear combination of Radial Basis Functions, we study the conditions on the generating kernel that guarantee the \\(\\mathcal {L}\\) -differentiability of the meshless solution. In our tests, we compare the proposed meshless and spectrum-free solvers with the meshless spectral eigen-decomposition and the meshless \\(\\theta \\) -method on the heat equation in a transient regime. With respect to these previous works, at small scales the <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> method has a higher numerical stability and approximation accuracy, which are expressed in terms of the selected degree of the rational polynomial and of the spectral properties of the matrix that discretises the parabolic operator.</p>", "Keywords": "Parabolic PDEs; Heat equation; Collocation methods; Radial basis functions; Spectrum; Spectrum-free solvers", "DOI": "10.1007/s10915-021-01604-x", "PubYear": 2021, "Volume": "88", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Consiglio Nazionale delle Ricerche(CNR-IMATI), Istituto di Matematica Applicata e Tecnologie Informatiche, Genova, Italy"}], "References": []}, {"ArticleId": 89953971, "Title": "A Missing QoS Prediction Approach via Time-Aware Collaborative Filtering", "Abstract": "Quality of Service (QoS) guarantee is an important issue in building service-oriented applications. Generally, some QoS values of a service are unknown to its users who have never invoked the service before. Fortunately, collaborative filtering ( CF )-based methods are proved feasible for missing QoS prediction and have been widely used. However, these methods seldom took the temporal factors into consideration. Indeed, historical QoS values contain more information about user (or service) similarity. Furthermore, as the application environment is dynamic, obtained QoS values usually have short timeliness. Hence, using outdated QoS values will largely decrease the prediction accuracy. In order to resolve this issue, we proposed a time-aware collaborative filtering approach. First, we proposed a QoS model to filter out outdated QoS values, and divided the obtained QoS values into several time slices. Then, we computed the average value of historical QoS as temporal QoS forecast. In addition, by introducing time-aware similarity computation mechanism, we succeeded to select real similar neighbor users (or services) and further predict the CF -based QoS based on CF technology. Finally, we can predict the final missing QoS by combining temporal QoS forecast and CF -based QoS prediction. Experiment results show that our approach can receive better prediction precision.", "Keywords": "QoS prediction;time-aware;collaborative filtering;service recommendation", "DOI": "10.1109/TSC.2021.3103769", "PubYear": 2022, "Volume": "15", "Issue": "6", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Security and Privacy in Intelligent Transportation, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Security and Privacy in Intelligent Transportation, Beijing Jiaotong University, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Key Laboratory of Security and Privacy in Intelligent Transportation, Beijing Jiaotong University, Beijing, China"}], "References": [{"Title": "Web service recommendation based on time‐aware users clustering and multi‐valued QoS prediction", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "9", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}]}, {"ArticleId": 89954078, "Title": "Image tampering detection based on a statistical model", "Abstract": "<p>This paper presents a novel method for image manipulation iden- tification of natural images in JPEG format. The image forgery detection technique is based on a signal-dependent noise model that is relevant to de- scribe a natural image acquired by a digital camera. This parametric model is characterized by two fingerprints which are used to falsification identification. The problem is cast in the framework of the hypothesis testing theory. For practical use, the Generalized Likelihood Ratio Tests (GLRT) are presented and their performance is theoretically established. There are different types of image forgery which have been considered in this paper for example re- sampling, Gaussian filtering and median filtering. Experiments with real and simulated images highlight the relevance of the proposed approach.</p>", "Keywords": "Image forgery detection signal-dependent noise model hypothesis testing statistical image modelling", "DOI": "10.1007/s11042-021-11213-3", "PubYear": 2021, "Volume": "80", "Issue": "21-23", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Technology of Troyes, Troyes, France"}, {"AuthorId": 2, "Name": "Florent Retraint", "Affiliation": "University of Technology of Troyes, Troyes, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "EPF College of Engineering-Montpellier, Montpellier, France"}], "References": []}, {"ArticleId": 89954309, "Title": "High gain and wideband circularly polarized\n S‐shaped \n patch antenna with reactive impedance surface and frequency‐selective surface configuration for\n Wi‐Fi \n and\n Wi‐Max \n applications", "Abstract": "<p>In this paper, broadband circularly polarized S-shaped patch antenna is designed with a reactive impedance surface (RIS). Besides the RIS structure, the frequency-selective surface (FSS) superstrate is deployed to enrich the antenna gain. The S-shaped patch is modeled on an FR4 glass epoxy and its feed position is set to resonate at 5.3 GHz. The 6 × 6 array size of square patches forms the RIS structure which is inserted in the middle of the S-shaped patch and ground plane to enhance −10 dB impedance bandwidth and 3 dB axial ratio bandwidth. The 6 × 6 array of unit cells forms the FSS superstrate which is deployed approximately half-wavelength (≈ λ /2) height above the ground plane to act like a Fabry Perot cavity (FPC) resonator antenna for improving the antenna gain. The combination of RIS and FSS with an S-shaped patch antenna improves the −10 dB impedance bandwidth, 3 dB axial ratio bandwidth, and the gain simultaneously. The proposed antenna resonates over a wide frequency range with a −10 dB impedance bandwidth of 31.8% (4.81–6.63 GHz) and the antenna has a good 3 dB axial ratio bandwidth of 18.9% (4.99–6.03 GHz). The antenna peak gain is around 10 dBi. The proposed antenna prototype is measured in the anechoic chamber and the measured results mimic the simulation results.</p>", "Keywords": "circular polarization;Fabry <PERSON> cavity (FPC) resonator antennas;frequency-selective surface (FSS);high gain and wideband antennas;reactive impedance surface (RIS)", "DOI": "10.1002/mmce.22865", "PubYear": 2021, "Volume": "31", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology, Warangal, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology, Warangal, India"}], "References": [{"Title": "A new broadband circularly polarized antenna with a single‐layer metasurface", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>‐<PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "7", "Page": "e22226", "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering"}]}, {"ArticleId": 89954319, "Title": "Design and analysis of high isolated super compact 2 × 2\n MIMO \n antenna for\n WLAN \n application", "Abstract": "<p>In this article, a DGS structure is applied to enhance the inter-port isolation of super compact 2 × 2 MIMO antenna. Each element of MIMO antenna configuration is designed using a planar inverted-F antenna (PIFA) having dimensions 7 × 7 × 0.8 mm<sup>3</sup> and elevated at 2.8 mm from the ground plane of dimensions 100 × 50 × 0.8 mm<sup>3</sup> (comparable to the size of mobile handsets). The proposed antenna operates from 5.2 to 6 GHz resonating at 5.65 GHz which is suitable for WLAN applications. The proposed design with and without the isolation enhancement technique is resonating at 5.65 GHz. Therefore, the proposed DGS structure is acting as a perfect isolator without affecting the reflection coefficient of the MIMO antenna configuration. It provides high isolation enhancement from 12 to 25 dB at the resonance frequency. All the diversity parameters of the MIMO configuration are calculated and found within the desirable limits. Thus, it is found that the proposed antenna is well suited for modern wireless applications (WLAN). The radiation performances such as 2D and 3D patterns, total antenna efficiency, and peak realized gain are calculated and analyzed. Finally, a prototype is fabricated and validated experimentally.</p>", "Keywords": "defected ground structure;isolation;MIMO;planar inverted-F antenna", "DOI": "10.1002/mmce.22864", "PubYear": 2021, "Volume": "31", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Thapar Institute of Engineering and Technology, Patiala, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Thapar Institute of Engineering and Technology, Patiala, India; TIET-VT Center of Excellence for Emerging Materials (CEEMS), TIET, Patiala, India"}], "References": []}, {"ArticleId": 89954344, "Title": "Miniaturized reconfigurable tri‐polarization metantenna based on characteristic mode analysis with high‐aperture efficiency", "Abstract": "<p>This article presents a miniaturized reconfigurable tri-polarization metantenna with compact size of 0.58 λ 0 × 0.58 λ 0 × 0.04 λ 0 ( λ 0 is the free space wavelength referring to the antenna center frequency). Metantenna means the metasurface is directly used as the antenna radiator aperture instead of an accessory. The metantenna is formed by miniaturized windmill like units, which is evolved from square patch units based on characteristic mode analysis, and 46% size reduction is achieved. Two PIN diodes are introduced into the feeding microstrip for switching among tri-polarization. The measured −10 dB impedance bandwidths are 24.6% (3.24–4.15 GHz) and 24.1% (3.26–4.05 GHz) for circular polarization (CP) and linear polarization (LP), respectively. The measured overlapped 3 dB axial ratio (AR) bandwidth is 15% (3.4–3.95 GHz). The measured peak gains are 5.15 dBi, 5.21 dBic, and 5.25 dBic for LP, left hand (LH) CP and right hand (RH) CP, respectively. The aperture efficiency is 77.3%, 78.5%, and 79.1% for LP, LHCP, and RHCP, respectively. The measured AR beamwidth (BW) of LHCP in XZ-plane and YZ-plane is 192° and 174°, respectively, the measured ARBW of RHCP in XZ-plane and YZ-plane is 201° and 166°, respectively. The developed metantenna has the following advantages: broadband 3 dB AR bandwidth, wide ARBW > 166°, least PIN number (tri-polarization with 2 PIN), and high-aperture efficiency.</p>", "Keywords": "characteristic mode analysis;high-aperture efficiency;metantenna;miniaturized antenna;polarization reconfigurable", "DOI": "10.1002/mmce.22867", "PubYear": 2021, "Volume": "31", "Issue": "11", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "Haiyang Wen", "Affiliation": "School of Electronic and Information Engineering, South China University of Technology, Guangzhou, China"}], "References": []}, {"ArticleId": 89954475, "Title": "Blockchain-based green big data visualization: BGbV", "Abstract": "The progression of Internet of Things (IoT) has resulted in generation of huge amount of data. Effective handling and analysis of such big volumes of data proposes a crucial challenge. Existing cloud-based frameworks of Big Data visualization are rising costs for servers, equipment, and energy consumption. There is a need for a green solution targeting lesser cost and energy consumption with tamper-proof record-keeping, storage, and interactive visualization with only demanded data. We have proposed a Blockchain-based Green big data Visualization (BGbV) solution using Hyperledger Sawtooth for optimum utilization of organization resources. BGbV will support current distributed data visualization platforms and guarantee benefits like security and data availability with lesser storage costs. It helps reduce costs by utilizing small resources that are already available and consume less energy, making it an environmentally friendly solution.", "Keywords": "IoT; Decentralized visualization; Blockchain; Big data visualization; Hyperledger Sawtooth; Secure distributed storage; Green solution", "DOI": "10.1007/s40747-021-00466-y", "PubYear": 2022, "Volume": "8", "Issue": "5", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Software Engineering, MCS, National University of Sciences and Technology (NUST), Islamabad, Pakistan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, NBC, National University of Sciences and Technology (NUST), Islamabad, Pakistan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Software Engineering, MCS, National University of Sciences and Technology (NUST), Islamabad, Pakistan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, MCS, National University of Sciences and Technology (NUST), Islamabad, Pakistan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Capital University of Science and Technology, Islamabad, Pakistan"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, South Korea"}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Information and Communication Engineering, Yeungnam University, Gyeongsan, South Korea"}], "References": [{"Title": "Blockchain-based decentralized storage networks: A survey", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "162", "Issue": "", "Page": "102656", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Towards a blockchain-based certificate authentication system in Vietnam", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "6", "Issue": "", "Page": "e266", "JournalTitle": "PeerJ Computer Science"}]}, {"ArticleId": 89954658, "Title": "A centralized architecture for autonomic quality of experience oriented handover in dense networks", "Abstract": "This paper presents an Optimised Handover (HO) Algorithm for Dense Wireless Local Area Networks (WLANs) based on a novel architecture of Software Defined Wireless Network (SDWN). The work has been designed to be effective in large network environments with a high density of Access Points (APs) and stations, which increase the chances of the Ping-Pong HO effect. Specifically, it considers Quality of Experience (QoE) by applying an optimised HO algorithm for WLANs, which relies on Fuzzy Logic Control Theory (FLCT) combined with Adaptive Hysteresis Values (AHVs). SDWN allows to monitor and manage the networks and to autonomously programme the APs through a centralized controller. The paper includes also a detailed performance analysis of the algorithm developed in an SDWN-based simulator implemented through OPNET. Specifically, our algorithm achieved promising performance results compared to the state of the art in terms of QoE, throughput, delay and reduction of the ping-pong HO effect.", "Keywords": "Handover ; Hysteresis ; QoS ; QoE ; SDWN ; Fuzzy Logic ; Wi-Fi", "DOI": "10.1016/j.compeleceng.2021.107352", "PubYear": 2021, "Volume": "94", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Liverpool Johan Moores University, Liverpool, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Renewable Energy Research Center, University of Anbar, Anbar, Iraq"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Liverpool Johan Moores University, Liverpool, United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Liverpool Johan Moores University, Liverpool, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Karpagam College of Engineering, Coimbatore, India"}], "References": [{"Title": "A dynamic access point allocation algorithm for dense wireless LANs using potential game", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "167", "Issue": "", "Page": "106991", "JournalTitle": "Computer Networks"}]}, {"ArticleId": 89954710, "Title": "npcure: An R Package for Nonparametric Inference in Mixture Cure Models", "Abstract": "Mixture cure models have been widely used to analyze survival data with a cure fraction. They assume that a subgroup of the individuals under study will never experience the event (cured subjects). So, the goal is twofold: to study both the cure probability and the failure time of the uncured individuals through a proper survival function (latency). The R package npcure implements a completely nonparametric approach for estimating these functions in mixture cure models, considering right-censored survival times. Nonparametric estimators for the cure probability and the latency as functions of a covariate are provided. Bootstrap bandwidth selectors for the estimators are included. The package also implements a nonparametric covariate significance test for the cure probability, which can be applied with a continuous, discrete, or qualitative covariate. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-027", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Research Group MODES, CITIC, Departamento de Matemáticas, Facultade de Informática, Universidade da Coruña CITIC, Campus de Elvina s/n, A Coruna, 15071, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>,<PERSON><PERSON>", "Affiliation": "Research Group MODES, CITIC, Departamento de Matemáticas, Facultade de Ciencias, Universidade da Coruna Rúa da Fraga s/n, A Zapateira, A Coruna, 15071, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Research Group MODES, Departamento de Matemáticas, Escuela Universitaria Politécnica, Universidade da Coruña 15405, A Coruna, Ferrol, Spain"}], "References": []}, {"ArticleId": 89954711, "Title": "The HBV.IANIGLA Hydrological Model", "Abstract": "Over the past 40 years, the HBV (Hydrologiska Byrans Vattenbalansavdelning) hydrological model has been one of the most used worldwide due to its robustness, simplicity, and reliable results. Despite these advantages, the available versions impose some limitations for research studies in mountain watersheds dominated by ice-snow melt runoff (i.e., no glacier module, a limited number of elevation bands, among other constraints). Here we present HBV.IANIGLA, a tool for hydroclimatic studies in regions with steep topography and/or cryospheric processes which provides a modular and extended implementation of the HBV model as an R package. To our knowledge, this is the first modular version of the original HBV model. This feature can be very useful for teaching hydrological modeling, as it offers the possibility to build a customized, open-source model that can be adjusted to different requirements of students and users. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-059", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IANIGLA-CONICET, Av. <PERSON> s/n Parque General <PERSON>, Mendoza, Argentina"}, {"AuthorId": 2, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "IANIGLA-CONICET, Av. <PERSON> s/n Parque General <PERSON>, Mendoza, Argentina"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IANIGLA-CONICET, Av. <PERSON> s/n Parque General <PERSON>, Mendoza, Argentina"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IANIGLA-CONICET, Av. <PERSON> s/n Parque General <PERSON>, Mendoza, Argentina"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "IANIGLA-CONICET, Av. <PERSON> s/n Parque General <PERSON>, Mendoza, Argentina"}], "References": []}, {"ArticleId": 89954712, "Title": "Working with CRSP/COMPUSTAT in R: Reproducible Empirical Asset Pricing", "Abstract": "It is common to come across SAS or Stata manuals while working on academic empirical finance research. Nonetheless, given the popularity of open-source programming languages such as R, there are fewer resources in R covering popular databases such as CRSP and COMPUSTAT. The aim of this article is to bridge the gap and illustrate how to leverage R in working with both datasets. As an application, we illustrate how to form size-value portfolios with respect to Fama and French (1993) and study the sensitivity of the results with respect to different inputs. Ultimately, the purpose of the article is to advocate reproducible finance research and contribute to the recent idea of “Open Source Cross-Sectional Asset Pricing”, proposed by <PERSON> and <PERSON> (2020). © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-047", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Business, Stevens Institute of Technology 1 Castle Point Terrace, Babbio Center, Hoboken, NJ  07030, United States"}], "References": []}, {"ArticleId": 89954713, "Title": "Reproducible Summary Tables with the gtsummary Package", "Abstract": "The gtsummary package provides an elegant and flexible way to create publication-ready summary tables in R. A critical part of the work of statisticians, data scientists, and analysts is summarizing data sets and regression models in R and publishing or sharing polished summary tables. The gtsummary package was created to streamline these everyday analysis tasks by allowing users to easily create reproducible summaries of data sets, regression models, survey data, and survival data with a simple interface and very little code. The package follows a tidy framework, making it easy to integrate with standard data workflows, and offers many table customization features through function arguments, helper functions, and custom themes. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-053", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Memorial Sloan Kettering, Cancer Center 1275 York Ave., New York, New York  10022, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Memorial Sloan Kettering, Cancer Center 1275 York Ave., New York, New York  10022, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Memorial Sloan Kettering, Cancer Center 1275 York Ave., New York, New York  10022, United States"}, {"AuthorId": 4, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Memorial Sloan Kettering, Cancer Center 1275 York Ave., New York, New York  10022, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Centre Population & Développement, IRD, Université de Paris, Inserm 45 rue des Saints-Pères, PARIS, 75006, France"}], "References": []}, {"ArticleId": 89954715, "Title": "Wide-to-tall Data Reshaping Using Regular Expressions and the nc Package", "Abstract": "Regular expressions are powerful tools for extracting tables from non-tabular text data. Capturing regular expressions that describe the information to extract from column names can be especially useful when reshaping a data table from wide (few rows with many regularly named columns) to tall (fewer columns with more rows). We present the R package nc (short for named capture), which provides functions for wide-to-tall data reshaping using regular expressions. We describe the main new ideas of nc, and provide detailed comparisons with related R packages (stats, utils, data.table, tidyr, tidyfast, tidyfst, reshape2, cdata). © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-029", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON>", "Affiliation": "School of Informatics, Computing and Cyber Systems Northern Arizona, University Flagstaff, Arizona, United States"}], "References": []}, {"ArticleId": 89954716, "Title": "The bdpar Package: Big Data Pipelining Architecture for R", "Abstract": "In the last years, big data has become a useful paradigm for taking advantage of multiple sources to find relevant knowledge in real domains (such as the design of personalized marketing campaigns or helping to palliate the effects of several fatal diseases). Big data programming tools and methods have evolved over time from a MapReduce to a pipeline-based archetype. Concretely the use of pipelining schemes has become the most reliable way of processing and analyzing large amounts of data. To this end, this work introduces bdpar, a new highly customizable pipeline-based framework (using the OOP paradigm provided by R6 package) able to execute multiple preprocessing tasks over heterogeneous data sources. Moreover, to increase the flexibility and performance, bdpar provides helpful features such as (i) the definition of a novel object-based pipe operator (%>|%), (ii) the ability to easily design and deploy new (and customized) input data parsers, tasks, and pipelines, (iii) only-once execution which avoids the execution of previously processed information (instances), guaranteeing that only new both input data and pipelines are executed, (iv) the capability to perform serial or parallel operations according to the user needs, (v) the inclusion of a debugging mechanism which allows users to check the status of each instance (and find possible errors) throughout the process. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-065", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science SING Research Group, University of Vigo, Spain CINBIO - Centro de Investigaciones Biomédicas, University of Vigo, Campus Universitario Lagoas-Marcosende, Vigo, 36310, Spain"}, {"AuthorId": 2, "Name": "Tomás,R. <PERSON>-<PERSON>áñez", "Affiliation": "Department of Statistics and Operations Research SiDOR Research Group, University of Vigo CINBIO, Centro de Investigaciones Biomédicas, University of Vigo, Campus Universitario Lagoas-Marcosende, Vigo, 36310, Spain"}, {"AuthorId": 3, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Department of Computer Science SING Research Group, Galicia Sur Health Research Institute (IIS Galicia Sur), SERGAS-UVIGO, Spain CINBIO - Centro de Investigaciones Biomédicas, University of Vigo, Campus Universitario Lagoas-Marcosende, Vigo, 36310, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science SING Research Group, Galicia Sur Health Research Institute (IIS Galicia Sur), SERGAS-UVIGO, Spain CINBIO - Centro de Investigaciones Biomédicas, University of Vigo, Campus Universitario Lagoas-Marcosende, Vigo, 36310, Spain"}], "References": []}, {"ArticleId": 89954717, "Title": "IndexNumber: An R Package for Measuring the Evolution of Magnitudes", "Abstract": "Index numbers are descriptive statistical measures useful in economic settings for comparing simple and complex magnitudes registered, usually in two time periods. Although this theory has a large history, it still plays an important role in modern today's societies where big amounts of economic data are available and need to be analyzed. After a detailed revision on classical index numbers in literature, this paper is focused on the description of the R package IndexNumber with strong capabilities for calculating them. Two of the four real data sets contained in this library are used for illustrating the determination of the index numbers in this work. Graphical tools are also implemented in order to show the time evolution of considered magnitudes simplifying the interpretation of the results. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-038", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Estatística, Análise Matemática e Optimización Universidade de Santiago de Compostela, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Departamento de Estatística, Análise Matemática e Optimización Universidade de Santiago de Compostela, Spain"}], "References": []}, {"ArticleId": 89954718, "Title": "Analyzing Dependence between Point Processes in Time Using IndTestPP", "Abstract": "The need to analyze the dependence between two or more point processes in time appears in many modeling problems related to the occurrence of events, such as the occurrence of climate events at different spatial locations or synchrony detection in spike train analysis. The package IndTestPP provides a general framework for all the steps in this type of analysis, and one of its main features is the implementation of three families of tests to study independence given the intensities of the processes, which are not only useful to assess independence but also to identify factors causing dependence. The package also includes functions for generating different types of dependent point processes, and implements computational statistical inference tools using them. An application to characterize the dependence between the occurrence of extreme heat events in three Spanish locations using the package is shown. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-049", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "Ana,C. <PERSON>", "Affiliation": "University of Zaragoza Dpto, Métodos <PERSON>stadísticos, <PERSON><PERSON>. C Pedro <PERSON> 12., Zaragoza, 50009, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Zaragoza Dpto, Métodos Estadísticos, EINA, C María de Luna 3., Zaragoza, 50018, Spain"}], "References": []}, {"ArticleId": 89954719, "Title": "Towards a Grammar for Processing Clinical Trial Data", "Abstract": "The goal of this paper is to help define a path toward a grammar for processing clinical trials by a) defining a format in which we would like to represent data from standardized clinical trial data b) describing a standard set of operations to transform clinical trial data into this format, and c) to identify a set of verbs and other functionality to facilitate data processing and encourage reproducibility in the processing of these data. It provides a background on standard clinical trial data and goes through a simple preprocessing example illustrating the value of the proposed approach through the use of the forceps package, which is currently being used for data of this kind. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-052", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Yale University 60 College Street New Haven, CT, 06510, United States"}], "References": []}, {"ArticleId": 89954721, "Title": "Unidimensional and Multidimensional Methods for Recurrence Quantification Analysis with crqa", "Abstract": "Recurrence quantification analysis is a widely used method for characterizing patterns in time series. This article presents a comprehensive survey for conducting a wide range of recurrencebased analyses to quantify the dynamical structure of single and multivariate time series and capture coupling properties underlying leader-follower relationships. The basics of recurrence quantification analysis (RQA) and all its variants are formally introduced step-by-step from the simplest autorecurrence to the most advanced multivariate case. Importantly, we show how such RQA methods can be deployed under a single computational framework in R using a substantially renewed version of our crqa 2.0 package. This package includes implementations of several recent advances in recurrencebased analysis, among them applications to multivariate data and improved entropy calculations for categorical data. We show concrete applications of our package to example data, together with a detailed description of its functions and some guidelines on their usage. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-062", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "School of Psychology, University of East London, London, E154LZ, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Institute of Psychology, University of Economics and Human Sciences, Warsaw 01-043, Warsaw, Poland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Communication University of California, Los Angeles, CA  90005, United States"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Language and Literature Max Planck Institute for Empirical Aesthetics, Frankfurt a.M., GR-60322, Germany"}], "References": []}, {"ArticleId": 89954722, "Title": "ROCnReg: An R Package for Receiver Operating Characteristic Curve Inference With and Without Covariates", "Abstract": "This paper introduces the package ROCnReg that allows estimating the pooled ROC curve, the covariate-specific ROC curve, and the covariate-adjusted ROC curve by different methods, both from (semi) parametric and nonparametric perspectives and within Bayesian and frequentist paradigms. From the estimated ROC curve (pooled, covariate-specific, or covariate-adjusted), several summary measures of discriminatory accuracy, such as the (partial) area under the ROC curve and the Youden index, can be obtained. The package also provides functions to obtain ROC-based optimal threshold values using several criteria, namely, the Youden index criterion and the criterion that sets a target value for the false positive fraction. For the Bayesian methods, we provide tools for assessing model fit via posterior predictive checks, while the model choice can be carried out via several information criteria. Numerical and graphical outputs are provided for all methods. This is the only package implementing Bayesian procedures for ROC curves. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-066", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON><PERSON>", "Affiliation": "BCAM - Basque Center for Applied Mathematics and IKERBASQUE, Basque Foundation for Science, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89954740, "Title": "SEEDCCA: An Integrated R-Package for Canonical Correlation Analysis and Partial Least Squares", "Abstract": "Canonical correlation analysis (CCA) has a long history as an explanatory statistical method in high-dimensional data analysis and has been successfully applied in many scientific fields such as chemometrics, pattern recognition, genomic sequence analysis, and so on. The so-called seedCCA is a newly developed R package that implements not only the standard and seeded CCA but also partial least squares. The package enables us to fit CCA to large- p and small-n data. The paper provides a complete guide. Also, the seeded CCA application results are compared with the regularized CCA in the existing R package. It is believed that the package, along with the paper, will contribute to highdimensional data analysis in various science field practitioners and that the statistical methodologies in multivariate analysis become more fruitful. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-026", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Researcher Celltrion Incheon22014, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Postdoctoral Associate Department of Biostatistics, Yale University New HavenCT  06520, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>,<PERSON><PERSON>", "Affiliation": "Professor Department of Statistics, Ewha Womans University Seoul03760, South Korea"}], "References": []}, {"ArticleId": 89954741, "Title": "Statistical Quality Control with the qcr Package", "Abstract": "The R package qcr for Statistical Quality Control (SQC) is introduced and described. It includes a comprehensive set of univariate and multivariate SQC tools that completes and increases the SQC techniques available in R. Apart from integrating different R packages devoted to SQC (qcc, MSQC), qcr provides nonparametric tools that are highly useful when Gaussian assumption is not met. This package computes standard univariate control charts for individual measurements, (Formula presented), S, R, p, np, c, u, EWMA, and CUSUM. In addition, it includes functions to perform multivariate control charts such as Hotelling T2, MEWMA and MCUSUM. As representative features, multivariate nonparametric alternatives based on data depth are implemented in this package: r, Q and S control charts. The qcr library also estimates the most complete set of capability indices from first to the fourth generation, covering the nonparametric alternatives, and performing the corresponding capability analysis graphical outputs, including the process capability plots. Moreover, Phase I and II control charts for functional data are included. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-034", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "MODES, SIGTI, ADIAAC, Departamento de Matemáticas, Escuela Politécnica Nacional 170517, Quito, Ecuador"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "MODES, CITIC, Universidade da Coruna Facultade de Informática, Campus de Elvina, A Coruna, Spain"}, {"AuthorId": 3, "Name": "Salvador Naya", "Affiliation": "MODES, CITIC, ITMATI, Universidade da Coruna Escola Politécnica Superior, Mendizábal s/n, Ferrol, Spain"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "MODES, CITIC, Universidade da Coruna Escola Politécnica Superior, Mendizábal s/n, Ferrol, Spain"}], "References": []}, {"ArticleId": 89954742, "Title": "pdynmc: A Package for Estimating Linear Dynamic Panel Data Models Based on Nonlinear Moment Conditions", "Abstract": "This paper introduces pdynmc, an R package that provides users sufficient flexibility and precise control over the estimation and inference in linear dynamic panel data models. The package primarily allows for the inclusion of nonlinear moment conditions and the use of iterated GMM; additionally, visualizations for data structure and estimation results are provided. The current implementation reflects recent developments in literature, uses sensible argument defaults, and aligns commercial and noncommercial estimation commands. Since the understanding of the model assumptions is vital for setting up plausible estimation routines, we provide a broad introduction of linear dynamic panel data models directed towards practitioners before concisely describing the functionality available in pdynmc regarding instrument type, covariate type, estimation methodology, and general configuration. We then demonstrate the functionality by revisiting the popular firm-level dataset of <PERSON><PERSON><PERSON> and <PERSON> (1991). © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-035", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Passau School of Business, Economics and Information Systems, Passau, 94032, Germany"}, {"AuthorId": 2, "Name": "<PERSON>,<PERSON>,<PERSON>", "Affiliation": "MOE Key Laboratory of Econometrics The Wang Yanan Institute for Studies in Economics Department of Statistics, School of Economics Fujian Key Laboratory of Statistics Xiamen University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Passau School of Business, Economics and Information Systems, Passau, 94032, Germany"}], "References": []}, {"ArticleId": 89954743, "Title": "Conversations in Time: Interactive Visualization to Explore Structured Temporal Data", "Abstract": "Temporal data often has a hierarchical structure, defined by categorical variables describing different levels, such as political regions or sales products. The nesting of categorical variables produces a hierarchical structure. The tsibbletalk package is developed to allow a user to interactively explore temporal data, relative to the nested or crossed structures. It can help to discover differences between category levels, and uncover interesting periodic or aperiodic slices. The package implements a shared tsibble object that allows for linked brushing between coordinated views, and a shiny module that aids in wrapping timelines for seasonal patterns. The tools are demonstrated using two data examples: domestic tourism in Australia and pedestrian traffic in Melbourne. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-050", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Auckland Department of Statistics"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Monash University, Department of Econometrics and Business Statistics"}], "References": []}, {"ArticleId": 89954755, "Title": "penPHcure: Variable Selection in Proportional Hazards Cure Model with Time-Varying Covariates", "Abstract": "We describe the penPHcure R package, which implements the semiparametric proportionalhazards (PH) cure model of <PERSON><PERSON> <PERSON> (2000) extended to time-varying covariates and the variable selection technique based on its SCAD-penalized likelihood proposed by <PERSON><PERSON> and <PERSON> (2019a). In survival analysis, cure models are a useful tool when a fraction of the population is likely to be immune from the event of interest. They can separate the effects of certain factors on the probability of being susceptible and on the time until the occurrence of the event. Moreover, the penPHcure package allows the user to simulate data from a PH cure model, where the event-times are generated on a continuous scale from a piecewise exponential distribution conditional on time-varying covariates, with a method similar to <PERSON><PERSON><PERSON> (2014). We present the results of a simulation study to assess the finite sample performance of the methodology and illustrate the functionalities of the penPHcure package using criminal recidivism data. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-061", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Centre for Quantitative Methods and Operations Management (QuantOM), HEC Liège Rue Louvrex, 14 - 4000, Liège, Belgium"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for Quantitative Methods and Operations Management (QuantOM), HEC Liège Rue Louvrex, 14 - 4000, Liège, Belgium"}], "References": []}, {"ArticleId": 89954756, "Title": "clustcurv: An R Package for Determining Groups in Multiple Curves", "Abstract": "In many situations, it could be interesting to ascertain whether groups of curves can be performed, especially when confronted with a considerable number of curves. This paper introduces an R package, known as clustcurv, for determining clusters of curves with an automatic selection of their number. The package can be used for determining groups in multiple survival curves as well as for multiple regression curves. Moreover, it can be used with large numbers of curves. An illustration of the use of clustcurv is provided, using both real data examples and artificial data. © 2021. All Rights Reserved.", "Keywords": "cluster; multiple curves; nonparametric; number of groups; regression models; survival analysis", "DOI": "10.32614/RJ-2021-032", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Department of Statistics and OR, SiDOR research group, CINBIO University of Vigo, Spain"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Statistics and OR, SiDOR research group, CINBIO University of Vigo, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Mathematics Centre of Mathematics University of Minho, Portugal"}, {"AuthorId": 4, "Name": "<PERSON>Pardi<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89954757, "Title": "krippendorffsalpha: An R Package for Measuring Agreement Using <PERSON><PERSON><PERSON>dorff's Alpha Coefficient", "Abstract": "R package krippendorffsalpha provides tools for measuring agreement using K<PERSON><PERSON>dorf<PERSON>'s a coefficient, a well-known nonparametric measure of agreement (also called inter-rater reliability and various other names). This article first develops Krippendorff's a in a natural way and situates a among statistical procedures. Then, the usage of package krippendorffsalpha is illustrated via analyses of two datasets, the latter of which was collected during an imaging study of hip cartilage. The package permits users to apply the a methodology using built-in distance functions for the nominal, ordinal, interval, or ratio levels of measurement. User-defined distance functions are also supported. The fitting function can accommodate any number of units, any number of coders, and missingness. Bootstrap inference is supported, and the bootstrap computation can be carried out in parallel. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-046", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Statistics The Pennsylvania State University University Park, PA, United States"}], "References": []}, {"ArticleId": 89954758, "Title": "BayesSPsurv: An R Package to Estimate Bayesian (Spatial) Split-Population Survival Models", "Abstract": "Survival data often include a fraction of units that are susceptible to an event of interest as well as a fraction of “immune” units. In many applications, spatial clustering in unobserved risk factors across nearby units can also affect their survival rates and odds of becoming immune. To address these methodological challenges, this article introduces our BayesSPsurv R-package, which fits parametric Bayesian Spatial split-population survival (cure) models that can account for spatial autocorrelation in both subpopulations of the user 's time-to-event data. Spatial autocorrelation is modeled with spatially weighted frailties, which are estimated using a conditionally autoregressive prior. The user can also fit parametric cure models with or without nonspatial i.i.d. frailties, and each model can incorporate time-varying covariates. BayesSPsurv also includes various functions to conduct pre-estimation spatial autocorrelation tests, visualize results, and assess model performance, all of which are illustrated using data on post-civil war peace survival. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-068", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Political Science Penn State University, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Political Science, Universidad de la República, Uruguay"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Political Science, San José State University, United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Political Science Penn State University, United States"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Political Science Penn State University, United States"}], "References": []}, {"ArticleId": 89954762, "Title": "exPrior: An R Package for the Formulation of Ex-Situ Priors", "Abstract": "The exPrior package implements a procedure for formulating informative priors of geostatistical properties for a target field site, called ex-situ priors and introduced in <PERSON><PERSON><PERSON> et al. (2019). The procedure uses a Bayesian hierarchical model to assimilate multiple types of data coming from multiple sites considered as similar to the target site. This prior summarizes the information contained in the data in the form of a probability density function that can be used to better inform further geostatistical investigations at the site. The formulation of the prior uses ex-situ data, where the data set can either be gathered by the user or come in the form of a structured database. The package is designed to be flexible in that regard. For illustration purposes and for easiness of use, the package is ready to be used with the worldwide hydrogeological parameter database (WWHYPDA) Comunian and Renard (2009). © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-031", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "Falk Heße", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of California, Berkeley Berkeley, CA, United States"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics University of California, Berkeley Berkeley, CA, United States, Leuven Statistics Research Centre, KU Leuven, Leuven, Belgium"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Civil and Environmental Engineering, University of California, Berkeley Berkeley, CA, United States"}], "References": []}, {"ArticleId": 89954763, "Title": "Benchmarking R packages for Calculation of Persistent Homology", "Abstract": "<p>Several persistent homology software libraries have been implemented in R. Specifically, the Dionysus, GUDHI, and Ripser libraries have been wrapped by the <b>TDA</b> and <b>TDAstats</b> CRAN packages. These software represent powerful analysis tools that are computationally expensive and, to our knowledge, have not been formally benchmarked. Here, we analyze runtime and memory growth for the 2 R packages and the 3 underlying libraries. We find that datasets with less than 3 dimensions can be evaluated with persistent homology fastest by the GUDHI library in the <b>TDA</b> package. For higher-dimensional datasets, the Ripser library in the TDAstats package is the fastest. Ripser and <b>TDAstats</b> are also the most memory-efficient tools to calculate persistent homology.</p>", "Keywords": "", "DOI": "10.32614/RJ-2021-033", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>,<PERSON><PERSON>", "Affiliation": "School of Medicine, Case Western Reserve University, Cleveland, OH 44106, United States."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>,<PERSON><PERSON>", "Affiliation": "Department of Quantitative Life Sciences, McGill University, Montreal, QC H3A 0G4, Canada."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Translational Hematology and Oncology Research, Cleveland Clinic Foundation, Cleveland, OH 44195, United States."}, {"AuthorId": 4, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Department of Translational Hematology and Oncology Research, Cleveland Clinic Foundation, Cleveland, OH 44195, United States."}, {"AuthorId": 5, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Cleveland Clinic Lerner College of Medicine, Case Western Reserve University, Cleveland, OH 44195, United States."}], "References": []}, {"ArticleId": 89954765, "Title": "garchx: Flexible and Robust GARCH-X Modeling", "Abstract": "The garchx package provides a user-friendly, fast, flexible, and robust framework for the estimation and inference of GARCH(p, q, r)-X models, where p is the ARCH order, q is the GARCH order, r is the asymmetry or leverage order, and ‘X’ indicates that covariates can be included. Quasi Maximum Likelihood (QML) methods ensure estimates are consistent and standard errors valid, even when the standardized innovations are non-normal or dependent, or both. Zero-coefficient restrictions by omission enable parsimonious specifications, and functions to facilitate the non-standard inference associated with zero-restrictions in the null-hypothesis are provided. Finally, in the formal comparisons of precision and speed, the garchx package performs well relative to other prominent GARCH-packages on CRAN. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-057", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "BI Norwegian Business School, Nydalsveien 37, Oslo, 0484, Norway"}], "References": []}, {"ArticleId": 89954766, "Title": "RLumCarlo: Simulating Cold Light using Monte Carlo Methods", "Abstract": "The luminescence phenomena of insulators and semiconductors (e.g., natural minerals such as quartz) have various application domains. For instance, Earth Sciences and archaeology exploit luminescence as a dating method. Herein, we present the R package RLumCarlo implementing sets of luminescence models to be simulated with Monte Carlo (MC) methods. MC methods make a powerful ally to all kinds of simulation attempts involving stochastic processes. Luminescence production is such a stochastic process in the form of charge (electron-hole pairs) interaction within insulators and semiconductors. To simulate luminescence-signal curves, we distribute single and independent MC processes to virtual MC clusters. RLumCarlo comes with a modularized design and consistent user interface: (1) C++ functions represent the modeling core and implement models for specific stimulations modes. (2) R functions give access to combinations of models and stimulation modes, start the simulation and render terminal and graphical feedback. The combination of MC clusters supports the simulation of complex luminescence phenomena. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-043", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geography & Earth Sciences, Aberystwyth University Aberystwyth SY23 3DB, Wales, United Kingdom, IRAMAT-CRP2A, UMR 5060, CNRS-Université Bordeaux Montaigne Maison de l'Archéologie Esplanade, des Antilles, Pessac Cedex, 36607, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Chair of Geomorphology, University of Bayreuth Universitätsstr, Bayreuth, 30 95447, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Physics Department, McDaniel College, Westminster, MD  21157, United States"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Université de Paris, Institut de Physique du Globe de Paris, CNRS, Paris, 75005, France, Chair of Geomorphology, University of Bayreuth, Bayreuth, 95447, Germany"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Chair of Geomorphology, University of Bayreuth, Bayreuth, 95447, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Institute of Earth Surface Dynamics, University of Lausanne, Lausanne, 1015, Switzerland"}], "References": []}, {"ArticleId": ********, "Title": "The R Package smicd: Statistical Methods for Interval-Censored Data", "Abstract": "The package allows the use of two new statistical methods for the analysis of interval-censored data: 1) direct estimation/prediction of statistical indicators and 2) linear (mixed) regression analysis. Direct estimation of statistical indicators, for instance, poverty and inequality indicators, is facilitated by a non parametric kernel density algorithm. The algorithm is able to account for weights in the estimation of statistical indicators. The standard errors of the statistical indicators are estimated with a non parametric bootstrap. Furthermore, the package offers statistical methods for the estimation of linear and linear mixed regression models with an interval-censored dependent variable, particularly random slope and random intercept models. Parameter estimates are obtained through a stochastic expectation-maximization algorithm. Standard errors are estimated using a non parametric bootstrap in the linear regression model and by a parametric bootstrap in the linear mixed regression model. To handle departures from the model assumptions, fixed (logarithmic) and data-driven (Box-Cox) transformations are incorporated into the algorithm. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-045", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Freie Universität Berlin, Garystraße 21, Berlin, 14195, Germany"}], "References": []}, {"ArticleId": 89954768, "Title": "distr6: R6 Object-Oriented Probability Distributions Interface in R", "Abstract": "Distr6 is an object-oriented (OO) probability distributions interface leveraging the extensibility and scalability of R6 and the speed and efficiency of Rcpp. Over 50 probability distributions are currently implemented in the package with ‘core' methods, including density, distribution, and generating functions, and more ‘exotic' ones, including hazards and distribution function anti-derivatives. In addition to simple distributions, distr6 supports compositions such as truncation, mixtures, and product distributions. This paper presents the core functionality of the package and demonstrates examples for key use-cases. In addition, this paper provides a critical review of the object-oriented programming paradigms in R and describes some novel implementations for design patterns and core object-oriented features introduced by the package for supporting distr6 components. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-055", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Statistical Science, University College London Gower, Street LondonWC1E 6BT, United Kingdom"}, {"AuthorId": 2, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89954769, "Title": "Automating Reproducible, Collaborative Clinical Trial Document Generation with the listdown Package", "Abstract": "The conveyance of clinical trial explorations and analysis results from a statistician to a clinical investigator is a critical component of the drug development and clinical research cycle. Automating the process of generating documents for data descriptions, summaries, exploration, and analysis allows the statistician to provide a more comprehensive view of the information captured by a clinical trial, and efficient generation of these documents allows the statistican to focus more on the conceptual development of a trial or trial analysis and less on the implementation of the summaries and results on which decisions are made. This paper explores the use of the listdown package for automating reproducible documents in clinical trials that facilitate the collaboration between statisticians and clinicians as well as defining an analysis pipeline for document generation. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-051", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Yale University 60 College Street New Haven, CT, 06510, United States"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Amgen Inc, One Amgen Center Drive, Thousand Oaks, CA  91320-1799, United States"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "The University of Aukland 38 Princes Street Auckland Central, Aukland, NZ  1010"}], "References": []}, {"ArticleId": 89954770, "Title": "Regularized Transformation Models: The tramnet Package", "Abstract": "The tramnet package implements regularized linear transformation models by combining the flexible class of transformation models from tram with constrained convex optimization implemented in CVXR. Regularized transformation models unify many existing and novel regularized regression models under one theoretical and computational framework. Regularization strategies implemented for transformation models in tramnet include the Lasso, ridge regression, and the elastic net and follow the parameterization in glmnet. Several functionalities for optimizing the hyperparameters, including model-based optimization based on the mlrMBO package, are implemented. A multitude of S3 methods is deployed for visualization, handling, and simulation purposes. This work aims at illustrating all facets of tramnet in realistic settings and comparing regularized transformation models with existing implementations of similar models. © 2021. All Rights Reserved.", "Keywords": "", "DOI": "10.32614/RJ-2021-054", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institut für Epidemiologie, Biostatistik und Prävention Universität, Zürich Hirschengraben 84, Zürich, CH-8001, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Institut für Epidemiologie, Biostatistik und Prävention Universität, Zürich Hirschengraben 84, Zürich, CH-8001, Switzerland"}], "References": []}, {"ArticleId": 89954771, "Title": "stratamatch: Prognostic Score Stratification Using a Pilot Design", "Abstract": "Optimal propensity score matching has emerged as one of the most ubiquitous approaches for causal inference studies on observational data. However, outstanding critiques of the statistical properties of propensity score matching have cast doubt on the statistical efficiency of this technique, and the poor scalability of optimal matching to large data sets makes this approach inconvenient if not infeasible for sample sizes that are increasingly commonplace in modern observational data. The stratamatch package provides implementation support and diagnostics for ‘stratified matching designs,‘ an approach that addresses both of these issues with optimal propensity score matching for large-sample observational studies. First, stratifying the data enables more computationally efficient matching of large data sets. Second, stratamatch implements a ‘pilot design’ approach in order to stratify by a prognostic score, which may increase the precision of the effect estimate and increase power in sensitivity analyses of unmeasured confounding. © 2021. All Rights Reserved.", "Keywords": "R;causal inference;matching;pilot designs;prognostic score;stratification", "DOI": "10.32614/RJ-2021-063", "PubYear": 2021, "Volume": "13", "Issue": "1", "JournalId": 62824, "JournalTitle": "The R Journal", "ISSN": "", "EISSN": "2073-4859", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>,<PERSON><PERSON>", "Affiliation": "Interdepartmental Program in Biomedical Informatics Stanford University Stanford, CA, United States"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Biostatistics and Data Science Wake Forest School of Medicine Winston-SalemNorth Carolina"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Quantitative Sciences Unit Stanford University Stanford, CA <PERSON> Epidemiology and Population Health Stanford University StanfordCA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Stanford University."}, {"AuthorId": 5, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Division of Cardiovascular Surgery University of Pennsylvania PhiladelphiaPA"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Cardiothoracic Surgery Stanford University School of Medicine StanfordCA"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>,<PERSON>", "Affiliation": "Department of Cardiothoracic Surgery Stanford University School of Medicine StanfordCA"}, {"AuthorId": 8, "Name": "<PERSON>,<PERSON><PERSON>", "Affiliation": "Biomedical Informatics Research Institute Stanford University Medical Center StanfordCA"}], "References": []}, {"ArticleId": 89954809, "Title": "Teleoperated mobile robot with two arms: the influence of a human-machine interface, VR training and operator age", "Abstract": "The article presents a method of supporting work by using virtual reality techniques to control a two-armed mobile robot's movement. The robot developed for the study can carry a 20 kg load (10 kg in each arm), and its price is low enough to enable mass implementation in industry (e.g., in warehouses). The results of a study conducted with 81 participants (63 young and 18 older active workers), divided into five study groups, are discussed. Three main factors are considered: human-machine interface, training in virtual reality and operator's age. The operator's work effectiveness was examined. Subjective indicators, such as usability, acceptance of technology, or the operator's fatigue and stress level were also investigated. Our results indicate, among others, that interface without walking is considered the most useful, short VR training does not increase the operator's efficiency, and older operators are much less willing to accept this kind of technology. The robot's description and how it is controlled are presented in the paper.", "Keywords": "Human-machine interface ; Mobile robot ; Teleoperation ; Dual-arm robot ; Virtual reality", "DOI": "10.1016/j.ijhcs.2021.102707", "PubYear": 2021, "Volume": "156", "Issue": "", "JournalId": 2721, "JournalTitle": "International Journal of Human-Computer Studies", "ISSN": "1071-5819", "EISSN": "1095-9300", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Central Institute for Labour Protection–National Research Institute, Warsaw, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Central Institute for Labour Protection–National Research Institute, Warsaw, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Central Institute for Labour Protection–National Research Institute, Warsaw, Poland"}], "References": [{"Title": "Unifying bilateral teleoperation and tele-impedance for enhanced user experience", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "4", "Page": "514", "JournalTitle": "The International Journal of Robotics Research"}, {"Title": "Dual-arm robot teleoperation support with the virtual world", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "2", "Page": "286", "JournalTitle": "Artificial Life and Robotics"}, {"Title": "Immersive Virtual Reality and Complex Skill Learning: Transfer Effects After Training in Younger and Older Adults", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "1", "Issue": "", "Page": "40", "JournalTitle": "Frontiers in Virtual Reality"}]}, {"ArticleId": 89954824, "Title": "Co-evolutionary distance predictions contain flexibility information", "Abstract": "Motivation <p>Co-evolution analysis can be used to accurately predict residue–residue contacts from multiple sequence alignments. The introduction of machine-learning techniques has enabled substantial improvements in precision and a shift from predicting binary contacts to predict distances between pairs of residues. These developments have significantly improved the accuracy of de novo prediction of static protein structures. With AlphaFold2 lifting the accuracy of some predicted protein models close to experimental levels, structure prediction research will move on to other challenges. One of those areas is the prediction of more than one conformation of a protein. Here, we examine the potential of residue–residue distance predictions to be informative of protein flexibility rather than simply static structure.</p> Results <p>We used DMPfold to predict distance distributions for every residue pair in a set of proteins that showed both rigid and flexible behaviour. Residue pairs that were in contact in at least one reference structure were classified as rigid, flexible or neither. The predicted distance distribution of each residue pair was analysed for local maxima of probability indicating the most likely distance or distances between a pair of residues. We found that rigid residue pairs tended to have only a single local maximum in their predicted distance distributions while flexible residue pairs more often had multiple local maxima. These results suggest that the shape of predicted distance distributions contains information on the rigidity or flexibility of a protein and its constituent residues.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab562", "PubYear": 2021, "Volume": "38", "Issue": "1", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Statistics, University of Oxford, Oxford OX1 3LB, UK"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computational Engineering and Data Science, Large Molecule Research, Penzberg 82377, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Computer-Aided Drug Design, UCB Pharma, Slough SL1 3WE, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computer-Aided Drug Design, UCB Pharma, Slough SL1 3WE, UK"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Computational Engineering and Data Science, Large Molecule Research, Penzberg 82377, Germany"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Statistics, University of Oxford, Oxford OX1 3LB, UK"}], "References": []}, {"ArticleId": 89954831, "Title": "Camp chain for inverse problems of structures", "Abstract": "Simulation-based inverse analysis plays a vital role in studying the actual working conditions of hydraulic structures, and has been largely implemented by network models in recent years. However, network-based models generally demand expensive sampling of numerical simulations and often rely on different search algorithms. Accordingly, a camp chain (CC) model is proposed to reduce numerical calculations and seek unknowns independently. The CC is comprised of a set of small size networks named camps, and each camp is an independent pattern recognition model from the unknown parameters to the corresponding system responses. An additive model involving the Linear-Gaussian kernel transformation is introduced as the mapping function for straightforward linear identification. Predictions from the previous camp direct the construction of the following one, thereby shaping a chain. Subsequently, the inversion accuracy of the CC improves along with every new camp. The effectiveness of the CC has been validated through the inversion study of thermal conductivities of a concrete dam model. Current tests indicate that the CC estimates objectives with high accuracy and speed when the number of verifications divided by the number of objectives is larger than the golden ratio.", "Keywords": "Camp chain ; Inverse problem ; Hydraulic structure ; Pattern recognition ; Linear-Gaussian kernel ; Numerical simulation", "DOI": "10.1016/j.compstruc.2021.106634", "PubYear": 2021, "Volume": "256", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Hydraulic Department, Tsinghua University, Beijing 100084, China"}], "References": []}, {"ArticleId": 89954838, "Title": "ramr : an R/Bioconductor package for detection of rare aberrantly methylated regions", "Abstract": "Motivation <p>With recent advances in the field of epigenetics, the focus is widening from large and frequent disease- or phenotype-related methylation signatures to rare alterations transmitted mitotically or transgenerationally (constitutional epimutations). Merging evidence indicate that such constitutional alterations, albeit occurring at a low mosaic level, may confer risk of disease later in life. Given their inherently low incidence rate and mosaic nature, there is a need for bioinformatic tools specifically designed to analyze such events.</p> Results <p>We have developed a method (ramr) to identify aberrantly methylated DNA regions (AMRs). ramr can be applied to methylation data obtained by array or next-generation sequencing techniques to discover AMRs being associated with elevated risk of cancer as well as other diseases. We assessed accuracy and performance metrics of ramr and confirmed its applicability for analysis of large public datasets. Using ramr we identified aberrantly methylated regions that are known or may potentially be associated with development of colorectal cancer and provided functional annotation of AMRs that arise at early developmental stages.</p> Availability and implementation <p>The R package is freely available at https://github.com/BBCG/ramr and https://bioconductor.org/packages/ramr.</p> Supplementary information <p>Supplementary data are available at Bioinformatics online.</p>", "Keywords": "", "DOI": "10.1093/bioinformatics/btab586", "PubYear": 2021, "Volume": "38", "Issue": "1", "JournalId": 1356, "JournalTitle": "Bioinformatics", "ISSN": "1367-4803", "EISSN": "1460-2059", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Center for Genome-Directed Cancer Therapy, Department of Clinical Science, University of Bergen, Bergen, Norway"}, {"AuthorId": 2, "Name": "<PERSON> <PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Center for Genome-Directed Cancer Therapy, Department of Clinical Science, University of Bergen, Bergen, Norway;Department of Oncology, Haukeland University Hospital, Bergen, Norway"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> <PERSON><PERSON> Center for Genome-Directed Cancer Therapy, Department of Clinical Science, University of Bergen, Bergen, Norway;Department of Oncology, Haukeland University Hospital, Bergen, Norway"}], "References": []}, {"ArticleId": 89954857, "Title": "A Rotated Characteristic Decomposition Technique for High-Order Reconstructions in Multi-dimensions", "Abstract": "<p>When constructing high-order schemes for solving hyperbolic conservation laws, the corresponding high-order reconstructions are commonly performed in characteristic spaces to eliminate spurious oscillations as much as possible. For multi-dimensional finite volume (FV) schemes, we need to perform the characteristic decomposition several times in different normal directions of the target cell, which is very time-consuming. In this paper, we propose a rotated characteristic decomposition technique which requires only one-time decomposition for multi-dimensional reconstructions. The rotated direction depends only on the gradient of a specific physical quantity which is cheap to calculate. This technique not only reduces the computational cost remarkably, but also controls spurious oscillations effectively. We take a third-order weighted essentially non-oscillatory finite volume (WENO-FV) scheme for solving the Euler equations as an example to demonstrate the efficiency of the proposed technique.</p>", "Keywords": "Characteristic decomposition; High-order schemes; Hyperbolic conservation laws; WENO; Finite volume", "DOI": "10.1007/s10915-021-01602-z", "PubYear": 2021, "Volume": "88", "Issue": "3", "JournalId": 3127, "JournalTitle": "Journal of Scientific Computing", "ISSN": "0885-7474", "EISSN": "1573-7691", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, University of Electronic Science and Technology of China, Chengdu, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Extreme Computing Research Center (ECRC), Computer, Electrical and Mathematical Sciences and Engineering (CEMSE), King Abdullah University of Science and Technology (KAUST), Thuwal, Kingdom of Saudi Arabia"}], "References": []}, {"ArticleId": 89954861, "Title": "The Design of Simple Environmentally Friendly A coal Gasifier in Small Scale with Maximum Capacity of 2 Kgs/Hour", "Abstract": "The design of a a coal gasification system is a workable unit or device whose work principle to change a coal fuel that has low-calorie value to be gas with a coal gasification system. Gas which is produced from a coal gasification system is a clean gas and environmentally friendly that its gasifier unit is used. The gasification process can be defined as the building process of gas fuel (CO, H2, and N2) in a small sump from a chemical reaction solid fuel characteristically Carbonaceous or Cellulosic, for example, coal, wood, and agriculture rubbish. Reaction process of gasification a chemical going on complexly, with the step as follows: drying, oxidation, and reduction. The aim of making a coal gasification system design is for using gas fuel as replaced solar fuel in the industry. For long time which will be got in this research is the use of a coal that has low-calorie value gasification system design on a big scale with a maximum capacity of more than 50 kgs/hour. The method which is used in this research is the design method and ratio method suitable with the gas that has been produced and go on with designing and existing of a the coal gasification system. The result from this research is drawing design of a coal gasification system and gas production as big as 0,5 kg from burning of a coal fuel as 2 kgs, already formed a fire on the stove, the gas temperature can reach T3 at 400°C and gas pressure is 0,88 Atm.", "Keywords": "", "DOI": "10.30534/ijatcse/2021/381042021", "PubYear": 2021, "Volume": "10", "Issue": "4", "JournalId": 52155, "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering", "ISSN": "", "EISSN": "2278-3091", "Authors": [], "References": []}, {"ArticleId": 89954922, "Title": "Reinforcement learning-based register renaming policy for simultaneous multithreading CPUs", "Abstract": "Simultaneous multithreading (SMT) improves the performance of superscalar CPUs by exploiting thread-level parallelism with shared entries for better utilization of resources. A key issue for this out-of-order execution is that the occupancy latency of a physical rename register can be undesirably long due to many program execution-dependent factors that result in performance degradation. Such an issue becomes even more problematic in an SMT environment in which these registers are shared among concurrently running threads. Smartly managing this critical shared resource to ensure that slower threads do not block faster threads’ execution is essential to the advancement of SMT performance. In this paper, an actor–critic style reinforcement learning (RL) algorithm is proposed to dynamically assigning an upper-bound (cap) of the rename registers any thread is allowed to use according to the threads’ real-time demand. In particular, a critic network projects the current Issue Queues (IQ) usage, register file usage, and the cap value to a reward; an actor network is trained to project the current IQ usage and register file usage to the optimal real-time cap value via ascending the instructions per cycle (IPC) gradient within the trajectory distribution. The proposed method differs from the state-of-the-art (<PERSON> and <PERSON>, 2018) as the cap for the rename registers for each thread is adjusted in real-time according to the policy and state transition from self-play . The proposed method shows an improvement in IPC up to 162.8% in a 4-threaded system, 154.8% in a 6-threaded system and up to 101.7% in an 8-threaded system. The code is now available open source at https://github.com/98k-bot/RL-based-SMT-Register-Renaming-Policy .", "Keywords": "Reinforcement learning ; Simultaneous multithreading ; Actor–critic ; Machine learning", "DOI": "10.1016/j.eswa.2021.115717", "PubYear": 2021, "Volume": "186", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Texas Tech University, Lubbock, TX 79409-3104, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Texas Tech University, Lubbock, TX 79409-3104, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, University of Texas at San Antonio, San Antonio, TX 78249, USA;Corresponding author"}], "References": []}, {"ArticleId": 89954924, "Title": "RoleSim*: Scaling axiomatic role-based similarity ranking on large graphs", "Abstract": "RoleSim and SimRank are among the popular graph-theoretic similarity measures with many applications in, e.g., web search, collaborative filtering, and sociometry. While RoleSim addresses the automorphic (role) equivalence of pairwise similarity which SimRank lacks, it ignores the neighboring similarity information out of the automorphically equivalent set. Consequently, two pairs of nodes, which are not automorphically equivalent by nature, cannot be well distinguished by <PERSON>Sim if the averages of their neighboring similarities over the automorphically equivalent set are the same. To alleviate this problem: 1) We propose a novel similarity model, namely RoleSim*, which accurately evaluates pairwise role similarities in a more comprehensive manner. RoleSim* not only guarantees the automorphic equivalence that SimRank lacks, but also takes into account the neighboring similarity information outside the automorphically equivalent sets that are overlooked by RoleSim. 2) We prove the existence and uniqueness of the RoleSim* solution, and show its three axiomatic properties ( i.e., symmetry, boundedness, and non-increasing monotonicity). 3) We provide a concise bound for iteratively computing RoleSim* formula, and estimate the number of iterations required to attain a desired accuracy. 4) We induce a distance metric based on RoleSim* similarity, and show that the RoleSim* metric fulfills the triangular inequality, which implies the sum-transitivity of its similarity scores. 5) We present a threshold-based RoleSim* model that reduces the computational time further with provable accuracy guarantee. 6) We propose a single-source RoleSim* model, which scales well for sizable graphs. 7) We also devise methods to scale RoleSim* based search by incorporating its triangular inequality property with partitioning techniques. Our experimental results on real datasets demonstrate that RoleSim* achieves higher accuracy than its competitors while scaling well on sizable graphs with billions of edges.", "Keywords": "Role-based similarity; Retrieval models and ranking; Web search; Link analysis", "DOI": "10.1007/s11280-021-00925-z", "PubYear": 2022, "Volume": "25", "Issue": "2", "JournalId": 16089, "JournalTitle": "World Wide Web", "ISSN": "1386-145X", "EISSN": "1573-1413", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nanjing University of Science and Technology, Jiangsu, China;University of Warwick, Coventry, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Warwick, Coventry, UK"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Warwick, Coventry, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nanjing University of Science and Technology, Jiangsu, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Warwick, Coventry, UK"}], "References": [{"Title": "A matrix sampling approach for efficient SimRank computation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "556", "Issue": "", "Page": "1", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 89954972, "Title": "Detection of human lower limb mechanical axis key points and its application on patella misalignment detection", "Abstract": "<p>The human lower limb mechanical axis is the most basic and essential diagnosis reference in clinical orthopedics. Orthopedists diagnose the varus or valgus knee according to the status of the lower limb mechanical axis. The conventional method used in this task relies on manual measurement, which is time-consuming and has operational differences. Given the above reason, in this work, we focus on designing a deep learning algorithm to address this problem and present a novel convolutional neural network architecture for mechanical axis detection. After the mechanical axis is detected, HKAA (Hip-Knee-Ankle Angle), which is a medical index, can be calculated automatically to assist in the medical diagnosis. We locate the mechanical axis by detecting both ends’ key points. Then we apply the detected key points to implement the patella misalignment detection for auxiliary radiography imaging. The mechanical axis key points detection network is based on the stacked hourglass module and adopts the deformable convolution for modeling the geometric features. Besides, we introduce an offset branch to reduce the systematic error. Then a detector trained in a semi-supervised strategy is applied for patella detection. The horizontal deviation of the patella from the knee center reflects the alignment of the patella. We use 879 collected radiographs (X-ray images) to train the key point detection model and other 98 radiographs perform as the validation set in this study. The proposed model achieves an accuracy of 83.0% for key points and reaches 61.1 mAP in patella detection. This model achieves excellent performance in human lower limb mechanical axis and patella detection.</p>", "Keywords": "Computer-aided diagnosis; Lower limb mechanical axis; Patella detection; Deformable convolution; Stacked hourglass network", "DOI": "10.1007/s10489-021-02718-3", "PubYear": 2022, "Volume": "52", "Issue": "5", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Linyi People’s Hospital, Linyi, China"}], "References": [{"Title": "The assessment of small bowel motility with attentive deformable neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "22", "JournalTitle": "Information Sciences"}, {"Title": "COVIDetectioNet: COVID-19 diagnosis system based on X-ray images using features selected from pre-learned deep features ensemble", "Authors": "<PERSON><PERSON><PERSON>lu; Turk<PERSON>lu, Muammer", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1213", "JournalTitle": "Applied Intelligence"}, {"Title": "Automatic prognosis of lung cancer using heterogeneous deep learning models for nodule detection and eliciting its morphological features", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>rk<PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2471", "JournalTitle": "Applied Intelligence"}, {"Title": "Photometric transfer for direct visual odometry", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "213", "Issue": "", "Page": "106671", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Hope: heatmap and offset for pose estimation", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "6", "Page": "2937", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 89954983, "Title": "Industry 5.0: A survey on enabling technologies and potential applications", "Abstract": "Industry 5.0 is regarded as the next industrial evolution, its objective is to leverage the creativity of human experts in collaboration with efficient, intelligent and accurate machines, in order to obtain resource-efficient and user-preferred manufacturing solutions compared to Industry 4.0. Numerous promising technologies and applications are expected to assist Industry 5.0 in order to increase production and deliver customized products in a spontaneous manner. To provide a very first discussion of Industry 5.0, in this paper, we aim to provide a survey-based tutorial on potential applications and supporting technologies of Industry 5.0. We first introduce several new concepts and definitions of Industry 5.0 from the perspective of different industry practitioners and researchers. We then elaborately discuss the potential applications of Industry 5.0, such as intelligent healthcare, cloud manufacturing, supply chain management and manufacturing production. Subsequently, we discuss about some supporting technologies for Industry 5.0, such as edge computing, digital twins, collaborative robots, Internet of every things, blockchain , and 6G and beyond networks. Finally, we highlight several research challenges and open issues that should be further developed to realize Industry 5.0.", "Keywords": "", "DOI": "10.1016/j.jii.2021.100257", "PubYear": 2022, "Volume": "26", "Issue": "", "JournalId": 43270, "JournalTitle": "Journal of Industrial Information Integration", "ISSN": "2467-964X", "EISSN": "2452-414X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 2, "Name": "Quoc-V<PERSON> Pham", "Affiliation": "Korean Southeast Center for the 4th Industrial Revolution Leader Education, Pusan National University, Busan, Republic of Korea;Corresponding author"}, {"AuthorId": 3, "Name": "Prabadevi B", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 4, "Name": "N Deepa", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Institute of Intelligent Systems, University of Johannesburg, South Africa"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Gadekallu", "Affiliation": "School of Information Technology and Engineering, Vellore Institute of Technology, Vellore, India"}, {"AuthorId": 7, "Name": "Ruk<PERSON>ana Ruby", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science, University Collage Dublin, Ireland and Centre for Wireless Communications, University of Oulu, Finland"}], "References": [{"Title": "Digital Twin-driven smart manufacturing: Connotation, reference model, applications and research issues", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101837", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Innovation in the Era of IoT and Industry 5.0: Absolute Innovation Management (AIM) Framework", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "124", "JournalTitle": "Information"}, {"Title": "Innovation in the Era of IoT and Industry 5.0: Absolute Innovation Management (AIM) Framework", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "2", "Page": "124", "JournalTitle": "Information"}, {"Title": "Load balancing of energy cloud using wind driven and firefly algorithms in internet of everything", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "142", "Issue": "", "Page": "16", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "A big data-driven framework for sustainable and smart additive manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "67", "Issue": "", "Page": "102026", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Peeking into the void: Digital twins for construction site logistics", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "121", "Issue": "", "Page": "103264", "JournalTitle": "Computers in Industry"}, {"Title": "Predictive maintenance in the Industry 4.0: A systematic literature review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "150", "Issue": "", "Page": "106889", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Digital twin to improve the virtual-real integration of industrial IoT", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "", "Page": "100196", "JournalTitle": "Journal of Industrial Information Integration"}, {"Title": "Robust Attack Detection Approach for IIoT Using Ensemble Classifier", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "3", "Page": "2457", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Virtual reality: A survey of enabling technologies and its applications in IoT", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "102970", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 89955029, "Title": "A vehicle detection and shadow elimination method based on greyscale information, edge information, and prior knowledge", "Abstract": "Vehicle detection is one of the most fundamental aspects of traffic surveillance systems. However, the shadow problem often hinders the accuracy of vehicle detection. Shadows are often mistakenly understood as the parts of a vehicle, causing objects loss, or shape distortion. Collected data with these errors are unreliable, therefore shadow detection and elimination is a key step to promote the accuracy of vehicle detection. This paper proposes a robust vehicle detection method with shadow elimination. The method is divided into two steps: Firstly, we extract foreground regions using a background differential method based on edge information, then we detect and eliminate the shadows from the foreground regions combined with grayscale information, edge information, and prior knowledge. The experimental results show that the proposed method is superior to the faster R-CNN, and SSD methods in terms of real-time performance and vehicle detection accuracy. The proposed method has broad application prospects in the Intelligent Transportation Systems (ITS).", "Keywords": "Vehicle detection ; Shadow elimination ; Edge detection ; Object segmentation", "DOI": "10.1016/j.compeleceng.2021.107366", "PubYear": 2021, "Volume": "94", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Qingdao Ocean Shipping Mariners College, Qingdao 266071, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Military Teaching Department, Ocean University of China, Qingdao 266100, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Information Science and Engineering, Ocean University of China, Qingdao 266100, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Computing Center, Ocean University of China, Qingdao 266100, China;Corresponding author"}], "References": [{"Title": "UA-DETRAC: A new benchmark and protocol for multi-object detection and tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "193", "Issue": "", "Page": "102907", "JournalTitle": "Computer Vision and Image Understanding"}]}, {"ArticleId": 89955032, "Title": "Editorial Special Issue on AI-enabled System Simulation and Modelling", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11518-021-5491-7", "PubYear": 2021, "Volume": "30", "Issue": "4", "JournalId": 22773, "JournalTitle": "Journal of Systems Science and Systems Engineering", "ISSN": "1004-3756", "EISSN": "1861-9576", "Authors": [{"AuthorId": 1, "Name": "<PERSON>ng <PERSON>", "Affiliation": "Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 2, "Name": "Weihua Li", "Affiliation": "Faculty of Design and Creative Technologies, Auckland University of Technology, Auckland, New Zealand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of ICT, University of Tasmania, Hobart, Australia"}], "References": [{"Title": "GPSPiChain-Blockchain and AI based Self-Contained Anomaly Detection Family Security System in Smart Home", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "433", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "Argumentative Conversational Agents for Online Discussions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "450", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "Context-Aware Recommendation System using Graph-based Behaviours Analysis", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "482", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "An Agent-based Adaptive Mechanism for Efficient Job Scheduling in Open and Large-scale Environments", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "400", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "A Multiple Negotiations Model Considering Interdependence between Discrete Issues Across Negotiations", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "417", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "A Differentially Private Auction Mechanism in Online Social Networks", "Authors": "Xiangyu Hu; <PERSON>ong Ye; Tianqing Zhu", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "386", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "Integration of Facility Location and Hypercube Queuing Models in Emergency Medical Systems", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "495", "JournalTitle": "Journal of Systems Science and Systems Engineering"}, {"Title": "On Learning Adaptive Service Compositions", "Authors": "<PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "4", "Page": "465", "JournalTitle": "Journal of Systems Science and Systems Engineering"}]}, {"ArticleId": 89955037, "Title": "An inverse mathematical technique for improving the sharpness of magnetic resonance images", "Abstract": "<p>Magnetic Resonance Imaging (MRI) is a powerful imaging modality with highly specific applications in medicine. However, certain series and sequences of MRI do not produce images with good acuity. Widely-used sharpening algorithms like Shock Filter (SF) and Unsharp Masking (UM) do not provide output images with appreciable sharpness-to-noise ratio. Output images of UM are prone to discontinuity artefact. A noise-robust sharpening algorithm to enhance acuity of MR images termed as Nonlinear Amplification of Spatial Derivative (NASD) that does not have the issue of discontinuity artefact is introduced in this paper. In the NASD, the enhanced image is estimated from the amplified spatial derivative in an inverse way. To avoid the amplification of noise, the amplification factor at any pixel location is calculated from a nonlinear function of the mean of the absolute values of the gradients along eight different orientations at that location. On hundred test images, the sharpness-to-noise ratio exhibited by NASD, SF and UM are 0.0963 ± 0.0400, 0.1642 ± 0.0967 and 0.2851 ± 0.1610. The computation time (in seconds) of NASD, SF and UM are 0.0330 ± 0.0031, 0.0452 ± 0.0390 and 0.0554 ± 0.0112. The NASD exhibits higher values of sharpness-to-noise ratio than SF and UM. NASD is able to sharpen the edges in the input image without amplifying noise and it is computationally fast like SF and UM.</p>", "Keywords": "Acutance; Edge enhancement; Image sharpening; Visual acuity", "DOI": "10.1007/s12652-021-03416-1", "PubYear": 2023, "Volume": "14", "Issue": "3", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Ponda, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, National Institute of Technology, Ponda, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Bioengineering, VIT Bhopal University, Bhopal, India"}], "References": [{"Title": "Human visual system based optimized mathematical morphology approach for enhancement of brain MR images", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "1", "Page": "799", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Extraction and analysis of brain functional statuses for early mild cognitive impairment using variational auto-encoder", "Authors": "Zhuqing Jiao; Yixin Ji; Peng Gao", "PubYear": 2023, "Volume": "14", "Issue": "5", "Page": "5439", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "RETRACTED ARTICLE: Computer aided diagnosis of brain tumor using novel classification techniques", "Authors": "<PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "7499", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "Facial expression recognition with trade-offs between data augmentation and deep learning features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "13", "Issue": "2", "Page": "721", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}, {"Title": "RETRACTED ARTICLE: Research on the application of binary-like coding and Hough circle detection technology in PCB traceability system", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2024, "Volume": "15", "Issue": "S1", "Page": "173", "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing"}]}, {"ArticleId": 89955067, "Title": "High Throughput Implementation of Post-Quantum Key Encapsulation and Decapsulation on GPU for Internet of Things Applications", "Abstract": "Internet of Things (IoT) sensor nodes are placed ubiquitously to collect information, which is then vulnerable to malicious attacks. For instance, adversaries can perform side channel attack on the sensor nodes to recover the symmetric key for encrypting IoT data. Refreshing the symmetric key frequently can reduce the risk of compromised keys. However, the number of sensor nodes connected to the gateway and cloud server is massive. Refreshed symmetric keys need to be sent to gateway devices and cloud server frequently with a secure key encapsulation mechanism (KEM), which is time-consuming. In this article, novel and efficient implementation techniques are proposed to accelerate Kyber, a post-quantum KEM, on a Graphics Processing Unit (GPU). Fully parallel implementation of number theoretic transform (NTT) with combined levels is presented, which is 2.65× faster than state-of-the-art result on a GPU. Other proposed techniques include parallel rejection sampling, central binomial distribution with coalesced memory access and parallel fine-grain AES-256. These techniques enable high throughput performance with 162760 encapsulations/second and 107631 decapsulations/second on an RTX2060 GPU. This is also the first fine grain implementation of post-quantum KEM (Kyber) on a GPU, which can be used to offer key encapsulation/decapsulation as a service to reduce the burden on IoT systems.", "Keywords": "Post-quantum cryptography;key encapsulation mechanism;graphics processing units;Kyber;lattice-based cryptography", "DOI": "10.1109/TSC.2021.3103956", "PubYear": 2022, "Volume": "15", "Issue": "6", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Wai-<PERSON> Lee", "Affiliation": "Department of Computer Engineering, Gachon University, Seongnam, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Gachon University, Seongnam, South Korea"}], "References": [{"Title": "Novel Secure Outsourcing of Modular Inversion for Arbitrary and Variable Modulus", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "15", "Issue": "1", "Page": "241", "JournalTitle": "IEEE Transactions on Services Computing"}, {"Title": "Parallel implementation of <PERSON><PERSON><PERSON><PERSON> algorithm and number theoretic transform on a GPU platform: application to qTESLA", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "77", "Issue": "4", "Page": "3289", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 89955131, "Title": "An efficient vehicular-relay selection scheme for vehicular communication", "Abstract": "<p>Vehicular delay-tolerant networks follow a store–carry–forward mechanism to overcome the state of broken links between source and destination, which is crucial for autonomous vehicle networks. These networks depend upon the mobility of vehicular-relay nodes, which can adopt the store–carry–forward arrangement and provide consent to utilize their resources for packet forwarding. In reality, vehicular-relay nodes drop packets because of a lack of resources and selfish behavior, leading to potential failures in networks. The reason for this failure in performance is the absence of an efficient relay node selection strategy. Typically, vehicular delay networks can provide communication solutions in challenging conditions when other traditional networks fail to perform due to disconnection. However, if the routing strategy does not consider nodes’ selfish nature and relay nodes’ inefficiency, packet drops continue, and the approach will fail to perform. This paper proposes a routing strategy, known as the best-choice vehicular-relay selection strategy for vehicle-to-vehicle (V2V) and vehicle-to-infrastructure (V2I) transmission, to overcome this breakdown situation. The proposed strategy selects the best-performing vehicular-relay nodes and restricts the packet replication to low-performing vehicular nodes. The proposed routing strategy selects the best-choice relay nodes based on their past performance. Numerical analysis and our simulation results show that the proposed approach outperforms some of the leading routing strategies in its class by increasing the delivery probability, decreasing the overhead ratio, average latency, and the number of dropped packets in the vehicular network.</p>", "Keywords": "Intelligent relay selection; The best-choice problem; Opportunistic network; Vehicular networks; Delay-tolerant networks", "DOI": "10.1007/s00500-021-06106-4", "PubYear": 2023, "Volume": "27", "Issue": "6", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology (ISM) Dhanbad, Dhanbad, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Technology (ISM) Dhanbad, Dhanbad, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Technology (ISM) Dhanbad, Dhanbad, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Science, Brandon University, Brandon, Canada; Research Centre for Interneural Computing, China Medical University, Taichung City, Taiwan"}], "References": [{"Title": "Combinatorial resource allocation in D2D assisted heterogeneous relay networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "956", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Delay-tolerant routing and message scheduling for CR-VANETs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "", "Page": "291", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": ********, "Title": "Block sampling Kacz<PERSON>z–<PERSON><PERSON> methods for consistent linear systems", "Abstract": "<p>The sampling Kaczmarz–<PERSON> (SKM) method is a generalization of the randomized <PERSON><PERSON><PERSON><PERSON> method and the <PERSON><PERSON><PERSON> method. It first samples some rows of coefficient matrix randomly to build a set and then makes use of the maximum violation criterion within this set to determine a constraint. Finally, it makes progress by enforcing this single constraint. In this paper, based on the SKM method and the block strategies, we present two block sampling <PERSON>cz<PERSON><PERSON>–<PERSON> methods for consistent linear systems. Specifically, we also first sample a subset of rows of coefficient matrix and then determine an index in this set using the maximum violation criterion. Unlike the SKM method, in the block methods, we devise different greedy strategies to build index sets. Then, the new methods make progress by enforcing the corresponding multiple constraints simultaneously. Numerical experiments show that, for the same accuracy, our methods outperform the SKM method and the famous deterministic method, i.e., the CGLS method, in terms of the number of iterations and computing time.</p>", "Keywords": "Block sampling Ka<PERSON><PERSON>z–<PERSON><PERSON><PERSON> methods; Greedy strategy; Sampling <PERSON><PERSON><PERSON><PERSON> method; Consistent linear systems; 65F10; 65F20", "DOI": "10.1007/s10092-021-00429-2", "PubYear": 2021, "Volume": "58", "Issue": "3", "JournalId": 12677, "JournalTitle": "<PERSON><PERSON><PERSON>", "ISSN": "0008-0624", "EISSN": "1126-5434", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, People’s Republic of China"}], "References": [{"Title": "Greed Works: An Improved Analysis of Sampling Kacz<PERSON>z--<PERSON><PERSON><PERSON>", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "3", "Issue": "1", "Page": "342", "JournalTitle": "SIAM Journal on Mathematics of Data Science"}]}]