[{"ArticleId": 80581174, "Title": "Retraction notice to “Clinical effect of percutaneous vertebroplasty (pvp) in spinal surgery on senile osteoporotic compressible fractures of the spine” [Future Gener. Comput. Syst. 98 (2019) 197–200]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.future.2020.02.059", "PubYear": 2020, "Volume": "107", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Spinal Surgery Unit 1 in Hanzhong Central Hospital of Shaanxi Province, 723000, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>ling <PERSON>", "Affiliation": "Spinal Surgery Unit 1 in Hanzhong Central Hospital of Shaanxi Province, 723000, PR China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Bone And Joint Trauma Department in Hanzhong Central Hospital of Shaanxi Province, 723000, PR China"}], "References": []}, {"ArticleId": 80581182, "Title": "Retraction notice to “effect of dexmedetomidine on comfort and satisfaction of patients” [Future Gener. Comput. Syst. 98 (2019) 167–170]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.future.2020.02.062", "PubYear": 2020, "Volume": "107", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Anesthesiology, Gulou School of Clinical Medicine, Nanjing Medical University, Nanjing, Jiangsu, PR China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Anesthesiology, Gulou School of Clinical Medicine, Nanjing Medical University, Nanjing, Jiangsu, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Anesthesiology, Gulou School of Clinical Medicine, Nanjing Medical University, Nanjing, Jiangsu, PR China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Anesthesiology, Gulou School of Clinical Medicine, Nanjing Medical University, Nanjing, Jiangsu, PR China"}], "References": []}, {"ArticleId": 80581187, "Title": "Sharpening the edge: Towards improved edge computing environment for mobile and IoT applications", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.future.2019.06.017", "PubYear": 2020, "Volume": "107", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Faculty of Exact Sciences, UNICEN University, Argentina"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems and Cyber Security, University of Texas at San Antonio, San Antonio, TX 78249-0631, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, Faculty of Exact Sciences, UNICEN University, Argentina"}], "References": []}, {"ArticleId": 80581193, "Title": "Retraction notice to “Correlation analysis of biochemical indicators in common bile duct stone patients with negative magnetic resonance cholangiopancreatography” [Future Gener. Comput. Syst. 98 (2019) 530–535]", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.future.2020.02.056", "PubYear": 2020, "Volume": "107", "Issue": "", "JournalId": 2245, "JournalTitle": "Future Generation Computer Systems", "ISSN": "0167-739X", "EISSN": "1872-7115", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of General Surgery, Capital Medical University Affiliated Beijing Friendship Hospital, Beijing 100050, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Vascular Surgery, Capital Medical University Affiliated Beijing Friendship Hospital, Beijing 100050, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of General Surgery, Capital Medical University Affiliated Beijing Friendship Hospital, Beijing 100050, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of General Surgery, Capital Medical University Affiliated Beijing Friendship Hospital, Beijing 100050, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of General Surgery, Capital Medical University Affiliated Beijing Friendship Hospital, Beijing 100050, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of General Surgery, Capital Medical University Affiliated Beijing Friendship Hospital, Beijing 100050, China;Corresponding author"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of General Surgery, Capital Medical University Affiliated Beijing Friendship Hospital, Beijing 100050, China"}], "References": []}, {"ArticleId": 80581647, "Title": "Feature distillation network for aspect-based sentiment analysis", "Abstract": "A proliferation of user-generated content on the web is fueling research into sentiment analysis for improved extraction of human emotional information. Aspect-based sentiment analysis (ABSA) is currently at the focus, which seeks to predict the sentiment of certain aspects in text. The primary challenge is to recognize the relevant contexts for different aspects. Most prior approaches combining recurrent neural networks and attention mechanisms inevitably introduce extraneous noise and diminish prediction accuracy. Furthermore, the sentiment of some context words varies with different aspects and cannot be inferred from themselves alone, which is another challenge that prevents attention mechanisms from performing properly. In this study, we propose a feature distillation network (FDN) for reducing noise and distilling aspect-relevant sentiment features. A novel double-gate mechanism is designed to implement the interactions between aspects and their corresponding contexts at a fine granularity. We introduce a contextual nonlinear projection layer before the double-gate mechanism to generate aspect-specific word representations, which enables the double-gate mechanism to accurately distinguish between sentiment features of the same context word that corresponds to the different aspects. Experiments show that the FDN achieves state-of-the-art performance and improves accuracy from 1.0 percent to 2.0 percent on all benchmarks for ABSA task.", "Keywords": "Neural network ; Bidirectional long short-Term memory ; Gating mechanism ; Aspect-Based sentiment analysis", "DOI": "10.1016/j.inffus.2020.03.003", "PubYear": 2020, "Volume": "61", "Issue": "", "JournalId": 6577, "JournalTitle": "Information Fusion", "ISSN": "1566-2535", "EISSN": "1872-6305", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Networking & Switching Technology, Beijing University of Posts and Telecommunications, Beijing, 100876, P.R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking & Switching Technology, Beijing University of Posts and Telecommunications, Beijing, 100876, P.R. China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computing and Engineering, University of West London, W5 5RF, UK"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking & Switching Technology, Beijing University of Posts and Telecommunications, Beijing, 100876, P.R. China"}, {"AuthorId": 5, "Name": "Mengyu Gu", "Affiliation": "State Key Laboratory of Networking & Switching Technology, Beijing University of Posts and Telecommunications, Beijing, 100876, P.R. China"}], "References": []}, {"ArticleId": 80581696, "Title": "A minimal biologically-inspired algorithm for robots foraging energy in uncertain environments", "Abstract": "This work details the design and simulation results of a bioinspired minimalist algorithm based on <PERSON><PERSON> el<PERSON> , using autonomous agents to forage for attractant energy sources. The robotic agents are energy-constrained and depend on the energy they forage to recharge their batteries, which is significant as the foraging task is one of the canonical testbeds for cooperative robotics. The algorithm consists of 6 input parameters which were simulated and optimised in 9 unbounded environments of varying difficulty levels, containing attractant sources which robots would then have to forage from to maintain energy levels and survive the entirety of the simulation. The robots running the algorithm were then optimised using Evolutionary Algorithms and the best solutions in all 9 environments were categorised with the use of clustering techniques. The clustering results highlighted the different strategies which emerged. Ultimately across the 9 environments, 6 different strategies have been identified. The results demonstrate the applicability of the proposed algorithm to localise attractant sources and harvest energy in different scenarios using the same core algorithm.", "Keywords": "", "DOI": "10.1016/j.robot.2020.103499", "PubYear": 2020, "Volume": "128", "Issue": "", "JournalId": 1961, "JournalTitle": "Robotics and Autonomous Systems", "ISSN": "0921-8890", "EISSN": "1872-793X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Design, Robotics and Optimisation (iDRO), University of Leeds, UK;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Design, Robotics and Optimisation (iDRO), University of Leeds, UK"}], "References": []}, {"ArticleId": 80581713, "Title": "Experimental and finite element analysis on determining the fatigue life of pb-free solder joint (Sn-0.5Cu-3Bi-1Ag) used in electronic packages under harmonic loads", "Abstract": "<p>Electronic packages that are used these days are exposed to different types of vibration loadings in their usage environment. This vibration exposure can be categorized as harmonic and random vibrations. When reliability assessment of modern electronic systems is considered, vibration loading has an important role to play. One of the biggest challenges facing today is the accurate and rapid assessment of fatigue life under the vibration loading. Conventional solder joints were made of lead-tin alloy. According to many environment legislations and rules, lead is prohibited as an ingredient in the solder alloy. The reason for the prohibition of the usage of the lead is that it poisons the environment. In this study, Sn-0.5Cu-3Bi-1Ag is used as the lead-free solder alloy. Fatigue life prediction of electronic package containing SAC405 is conducted with the aid of vibration testing and Finite element analysis under harmonic vibration loading. A specially designed Plastic Ball Grid Array Package (PBGA) component is mounted on Printed Circuit Board (PCB). It is taken as a test vehicle for the vibration test. The test vehicle is excited by a sinusoidal vibration. The frequency of this excitation equals the fundamental frequency of the test vehicle and it is continued till the component fails. Since the solder balls are very small for direct measurement, Finite Element analysis (FEA) is used for noting down the stresses. The stress versus failures cycles (S-N) curve is made by relating both the stresses on the solder balls obtained and the number of failure cycles from vibration analysis. The fatigue life of the component can be estimated from the generated S-N curve. It is analyzed that the methodology is effective in predicting the component’s life. Hence, the reliability of electronic package can be improved.</p>", "Keywords": "", "DOI": "10.1142/S1793962320500208", "PubYear": 2020, "Volume": "11", "Issue": "3", "JournalId": 12484, "JournalTitle": "International Journal of Modeling, Simulation, and Scientific Computing", "ISSN": "1793-9623", "EISSN": "1793-9615", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, School of Engineering, Cochin University of Science and Technology, Kochi, Kerala-682022, India"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, School of Engineering, Cochin University of Science and Technology, Kochi, Kerala-682022, India"}], "References": []}, {"ArticleId": ********, "Title": "Understanding the Factors Affecting Employees’ Motivation to Engage in Co-Creation in the Banking Industry", "Abstract": "<p>Increasing digitalization and new technological possibilities also entail substantial changes for working methods in the B2B (business-to-business) environment in banking. In this context, the concept of co-creation is critical. Although this concept and the motivation factors behind it have been thoroughly investigated in the B2C (business-to-consumer) sector, only a few research results exist for the B2B context. This study aims to bridge the current knowledge gap and investigate individuals’ motivation to participate in B2B co-creation. By using a case study and qualitative interviews, this study focuses on two aspects: (a) It reveals how a co-creation measure is used in practice in the B2B environment; and (b) it provides information on the motivation factors and outcome from the point of view of the participants in the B2B co-creation project. The paper concludes with an integrative model of the main motivation factors behind B2B co-creation and their effects.</p>", "Keywords": "", "DOI": "10.1142/S0219877020500157", "PubYear": 2020, "Volume": "17", "Issue": "2", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Osnabrueck, School of Business Administration and Economics, Institute of Information Management and Information Systems Engineering, Chair of Accounting and Information Systems, Katharinenstr. 1, D-49074 Osnabrueck, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Osnabrueck, School of Business Administration and Economics, Institute of Information Management and Information Systems Engineering, Chair of Accounting and Information Systems, Katharinenstr. 1, D-49074 Osnabrueck, Germany"}], "References": []}, {"ArticleId": ********, "Title": "Social Recommendation Combining Trust Relationship and Distance Metric Factorization", "Abstract": "The recommender system predicts user preferences by mining user historical behavior data. This paper proposes a social recommendation combining trust relationship and distance metric factorization. On the one hand, the recommender system has a cold start problem, which can be effectively alleviated by adding social relations. Simultaneously, to improve the problem of sparse trust matrix, we use the <PERSON><PERSON><PERSON> similarity coefficient and the <PERSON><PERSON><PERSON> algorithm to reconstruct the trust matrix and explore the potential user trust relationship. On the other hand, the traditional matrix factorization algorithm is modeled by the user item potential factor dot product, however, it does not satisfy the triangle inequality property and affects the final recommender effect. The primary motivator behind our approach is to combine the best of both worlds, mitigate the inherent weaknesses of each paradigm. Combining the advantages of the two ideas, it has been demonstrated that our algorithm can enhance recommender performance and improve cold start in recommender systems.", "Keywords": "", "DOI": "10.1142/S0218126620502497", "PubYear": 2020, "Volume": "29", "Issue": "15", "JournalId": 9643, "JournalTitle": "Journal of Circuits, Systems and Computers", "ISSN": "0218-1266", "EISSN": "1793-6454", "Authors": [{"AuthorId": 1, "Name": "Ming Ye", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80581740, "Title": "Learning Multifunctional Binary Codes for Personalized Image Retrieval", "Abstract": "<p>Due to the highly complex semantic information of images, even with the same query image, the expected content-based image retrieval results could be very different and personalized in different scenarios. However, most existing hashing methods only preserve one single type of semantic similarity, making them incapable of addressing such realistic retrieval tasks. To deal with this problem, we propose a unified hashing framework to encode multiple types of information into the binary codes by exploiting convolutional networks (CNNs). Specifically, we assume that typical retrieval tasks are generally defined in two aspects, i.e. high-level semantics (e.g. object categories) and visual attributes (e.g. object shape and color). To this end, our Dual Purpose Hashing model is trained to jointly preserve two kinds of similarities characterizing the two aspects respectively. Moreover, since images with both category and attribute labels are scarce, our model is carefully designed to leverage the abundant partially labelled data as training inputs to alleviate the risk of overfitting. With such a framework, the binary codes of new-coming images can be readily obtained by quantizing the outputs of a specific CNN layer, and different retrieval tasks can be achieved by using the binary codes in different ways. Experiments on two large-scale datasets show that our method achieves comparable or even better performance than those state-of-the-art methods specifically designed for each individual retrieval task while being more compact than the compared methods.</p>", "Keywords": "Image retrieval; Multi-task learning; Hashing", "DOI": "10.1007/s11263-020-01315-0", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Information Processing, Chinese Academy of Sciences (CAS), Institute of Computing Technology, CAS, Beijing, China;University of Chinese Academy of Sciences, Beijing, China;Huawei EI Innovation Lab, Beijing, China"}, {"AuthorId": 2, "Name": "Ruiping <PERSON>", "Affiliation": "Key Laboratory of Intelligent Information Processing, Chinese Academy of Sciences (CAS), Institute of Computing Technology, CAS, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 3, "Name": "Shiguang Shan", "Affiliation": "Key Laboratory of Intelligent Information Processing, Chinese Academy of Sciences (CAS), Institute of Computing Technology, CAS, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Information Processing, Chinese Academy of Sciences (CAS), Institute of Computing Technology, CAS, Beijing, China;University of Chinese Academy of Sciences, Beijing, China"}], "References": [{"Title": "Deep Learning for Generic Object Detection: A Survey", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "261", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 80581741, "Title": "Semi-online Multi-people Tracking by Re-identification", "Abstract": "<p>In this paper, we propose a novel semi-online approach to tracking multiple people. In contrast to conventional offline approaches that take the whole image sequence as input, our semi-online approach tracks people in a frame-by-frame manner by exploring the time, space and multi-camera relationship of detection hypotheses in the near future frames. We cast the multi-people tracking task as a re-identification problem, and explicitly account for objects’ appearance changes and longer-term associations. We model our approach using a Multi-Label Markov Random Field, and introduce a fast ( lpha ) -expansion algorithm to solve it efficiently. To our best knowledge, this is the first semi-online approach achieved by re-identification. It yields very promising tracking results especially in challenging cases, such as scenarios of the crowded streets where pedestrians frequently occlude each other, scenes captured with moving cameras where objects may disappear and reappear randomly, and videos under changing illuminations wherein the appearances of objects are influenced.</p>", "Keywords": "Multi-object tracking; Semi-online methods; Combinatory optimization; Deep learning", "DOI": "10.1007/s11263-020-01314-1", "PubYear": 2020, "Volume": "128", "Issue": "7", "JournalId": 6213, "JournalTitle": "International Journal of Computer Vision", "ISSN": "0920-5691", "EISSN": "1573-1405", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Quantum Information and State Key Laboratory of High Performance Computing, College of Computer, National University of Defense Technology, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Stevens Institute of Technology, Hoboken, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Wormpex AI Research, Bellevue, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Image Formation and Processing Group (IFP), Beckman Institute, University of Illinois Urbana-Champaign (UIUC), Urbana, USA"}, {"AuthorId": 5, "Name": "Dacheng Tao", "Affiliation": "UBTECH Sydney Artificial Intelligence Centre and the School of Computer Science, in the Faculty of Engineering, The University of Sydney, Darlington, Australia"}], "References": []}, {"ArticleId": 80581911, "Title": "Editorial for the special issue on storage system and technology", "Abstract": "", "Keywords": "", "DOI": "10.1007/s42514-020-00026-9", "PubYear": 2020, "Volume": "2", "Issue": "1", "JournalId": 64694, "JournalTitle": "CCF Transactions on High Performance Computing", "ISSN": "2524-4922", "EISSN": "2524-4930", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Huazhong University of Science and Technology, Wuhan, China"}, {"AuthorId": 2, "Name": "Hong Jiang", "Affiliation": "University of Texas at Arlington, Arlington, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "St Francis Xavier University, Antigonish, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Temple University, Philadelphia, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "<PERSON>-Universität Mainz, Mainz, Germany"}], "References": []}, {"ArticleId": 80582208, "Title": "DeepFusion: predicting movie popularity via cross-platform feature fusion", "Abstract": "<p>For online video service providers, the accurate prediction of video popularity directly impacts their advertisement revenue, bandwidth provisioning policy and copyright procurement decision. Most of previous approaches only utilize data from a single platform (e.g., view history) for prediction. However, such approaches cannot provide satisfactory prediction accuracy, as video popularity may be affected by many influential features dispersed over multiple platforms. In this paper, we focus on the popularity prediction of online movies and propose a prediction framework called DeepFusion to fuse salient features from multiple platforms so as to boost the accuracy of popularity prediction of online movies. For this purpose, we extract influential factors from Douban, which is a leading movie rating website in China, and Youku, which is one of the largest online video service providers in China. Considering the complexity incurred by numerous parameters, we choose to feed these influential factors into deep neural networks for prediction and thus avoid the limitation of traditional predictive models. Compared with previous approaches, our solution can significantly improve the prediction accuracy over 40%. Moreover, even for movies without any historical views, our approach can also well capture their popular trends and overcome the cold-start problem.</p>", "Keywords": "Popularity prediction; Deep neural networks; Online video service", "DOI": "10.1007/s11042-020-08730-y", "PubYear": 2020, "Volume": "79", "Issue": "27-28", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-Sen University, Guangzhou, China;Collaborative Innovation Center of High Performance Computing, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computing, Macquarie University, Sydney, Australia"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Data and Computer Science, Sun Yat-Sen University, Guangzhou, China;Collaborative Innovation Center of High Performance Computing, Sun Yat-Sen University, Guangzhou, China;Guangdong Key Laboratory of Big Data Analysis and Processing, Sun Yat-Sen University, Guangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Software Engineering, Shenzhen University, Shenzhen, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Department of Communication Engineering, Xiamen University, Fujian, China"}], "References": []}, {"ArticleId": 80582278, "Title": "On modeling blockchain-enabled economic networks as stochastic dynamical systems", "Abstract": "Abstract Blockchain networks have attracted tremendous attention for creating cryptocurrencies and decentralized economies built on peer-to-peer protocols. However, the complex nature of the dynamics and feedback mechanisms within these economic networks has rendered it difficult to reason about the growth and evolution of these networks. Hence, proper mathematical frameworks to model and analyze the behavior of blockchain-enabled networks are essential. To address this need, we establish a formal mathematical framework, based on dynamical systems, to model the core concepts in blockchain-enabled economies. Drawing on concepts from differential games, control engineering, and stochastic dynamical systems, this paper proposes a methodology to model, simulate, and engineer networked token economies. To illustrate our framework, a model of a generalized token economy is developed, where miners provide a commodity service to a platform in exchange for a cryptocurrency and users consume a service from the platform. We illustrate the dynamics of token economies by simulating and testing two different block reward strategies. We then conclude by outlining future research directions that will integrate additional methods from signal processing and control theory into the toolkit for designers of blockchain-enabled economic systems.", "Keywords": "Economic networks;Differential games;Stochastic processes;Control systems", "DOI": "10.1007/s41109-020-0254-9", "PubYear": 2020, "Volume": "5", "Issue": "1", "JournalId": 5330, "JournalTitle": "Applied Network Science", "ISSN": "", "EISSN": "2364-8228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Systems Engineering, University of Pennsylvania, Philadelphia, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "BlockScience Inc, Oakland, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Electrical and Systems Engineering, University of Pennsylvania, Philadelphia, USA"}], "References": []}, {"ArticleId": ********, "Title": "A compact neuromorphic architecture with dynamic routing to efficiently simulate the FXECAP-L algorithm for real-time active noise control", "Abstract": "In this work, we introduce, for the first time, the design of a compact neuromorphic architecture to efficiently support a filtered-x error-coded affine projection-like (FXECAP-L) algorithm that is based on affine projection (AP) algorithms for active noise cancellation (ANC) in an acoustic duct. To date, few practical ANC implementations have used AP algorithms because of their high computational complexity, despite providing fast convergence speeds. One of the main factors that increases their computational complexity is linked to the dimensions of the matrix used in the AP algorithm’s computations. Evidently, the largest dimensions of the matrix increase the convergence speed of the AP algorithms by paying a penalty in terms of area consumption. However, convergence speed is crucial in ANC applications since this factor determines the speed at which the noise is canceled. Recently, an FXECAP-L algorithm with evolving order has been proposed to dynamically reduce the dimensions of the matrix by maintaining the convergence speed of AP algorithms. Here, we propose a compact neuromorphic architecture with a dynamic routing mechanism to efficiently implement the evolutionary method of the FXECAP-L algorithm by creating a virtual matrix, whose dimensions can be modified over the filter processing. In this way, we avoid spending a large amount of memory to save the largest matrix elements. In addition, the inclusion of the dynamic routing mechanism in the proposed neuromorphic architecture has allowed us to guarantee low area consumption since the neuromorphic architecture is capable of simulating different adaptive structures without modifying its structure. Here, the neuromorphic architecture has been configured as the system identification and ANC controller for practical noise cancellation in an acoustic duct. Our results have demonstrated that the combination of the properties of the FXECAP-L algorithm and the implementation techniques generate a versatile signal processing development tool that can be used in practical real-time ANC applications.", "Keywords": "Affine projection-like algorithm ; Spiking neural P systems ; Rules on the synapses ; Synaptic weights", "DOI": "10.1016/j.asoc.2020.106233", "PubYear": 2020, "Volume": "91", "Issue": "", "JournalId": 1884, "JournalTitle": "Applied Soft Computing", "ISSN": "1568-4946", "EISSN": "1872-9681", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico;Corresponding authors"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "Instituto Politécnico Nacional ESIME Culhuacan, Av. <PERSON> 1000, Coyoacan, 04260, Ciudad de México, Mexico"}], "References": []}, {"ArticleId": 80582669, "Title": "Editorial Preface", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11227-020-03228-8", "PubYear": 2020, "Volume": "76", "Issue": "3", "JournalId": 1803, "JournalTitle": "The Journal of Supercomputing", "ISSN": "0920-8542", "EISSN": "1573-0484", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Taichung University of Science and Technology, Taichung, Taiwan"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, The University of Aizu, Aizuwakamatsu, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universidade Federal do ABC, Santo André, Brazil"}], "References": [{"Title": "Effectiveness evaluation of Internet of Things-aided firefighting by simulation", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1383", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An implementation of cloud-based platform with R packages for spatiotemporal analysis of air pollution", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1416", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Grid-based indexing with expansion of resident domains for monitoring moving objects", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1482", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "WCP-RNN: a novel RNN-based approach for Bio-NER in Chinese EMRs", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1450", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Complexity reduction method for ultrasound imaging enhancement in tetrolet transform domain", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1438", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Direct mail promotion mechanisms and their application in supermarkets", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1398", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Genetic algorithm-based cost minimization pricing model for on-demand IaaS cloud service", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1536", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "GUIDE: an interactive and incremental approach for crawling Web applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1562", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "The study for dispatch decision of medical emergency resources with real-time spatial analysis", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1604", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "pISRA: privacy considered information security risk assessment model", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1468", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Empirical decision analytics approach of advanced granularity-based models for identifying performance measures of ERPS application", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1502", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An energy-efficient dynamic decision model for wireless multi-sensor network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1585", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Effectiveness evaluation of iris segmentation by using geodesic active contour (GAC)", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1628", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "An effective digitized GPS signal transmission for high temporal precision IoT services", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1365", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Transmission spectral analysis models for the assessment of white-shell eggs and brown-shell eggs freshness", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1680", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A two-layer SIR information propagation model with heterogeneity based on coupled network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1657", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Insights into relevant knowledge extraction techniques: a comprehensive review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1695", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "Multisensor data fusion of motion monitoring system based on BP neural network", "Authors": "<PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "3", "Page": "1642", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "RETRACTED ARTICLE: Cognitive learning performance assessment and analysis with CSCL applied on the NetGuru platform and CSPL applied on the TAoD platform for the network experiment class", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "1", "Page": "16", "JournalTitle": "The Journal of Supercomputing"}]}, {"ArticleId": 80582880, "Title": "Permittivity estimation of a shallow-layered medium using high-resolution ground-penetrating radar", "Abstract": "This paper proposes a method of permittivity estimation using ground penetrating radar (GPR). The proposed method can estimate the permittivity of a shallow-layered medium. In the proposed method, antenna calibration is introduced to improve the vertical resolution of the GPR. The conventional surface reflection method is slightly modified to be suitable for operation at a shallow depth since the antenna calibration impacts the signal amplitude being exploited to estimate the permittivity. Simulation and experimentation examples are illustrated to confirm that the proposed method can estimate the permittivity of each layer of dielectric ground medium. Simulations were divided into two main catalogues in order to investigate the resolution improvement and estimation accuracy of the GPR. The experimentation of the GPR along with the proposed method were conducted. The experimental results showed good agreement with those obtained from the commercial dielectric prob kit.", "Keywords": "", "DOI": "10.1080/01431161.2020.1723177", "PubYear": 2020, "Volume": "41", "Issue": "12", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Faculty of Engineering, Rajamangala University of Technology Rattanakosin, Nakhon Pathom, Thailand"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Center of Innovation Digital and Electromagnetic Technology, Department of Electrical and Computer Engineering, Faculty of Engineering, King <PERSON>’s University of Technology North Bangkok, Bangkok, Thailand"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Center of Innovation Digital and Electromagnetic Technology, Department of Electrical and Computer Engineering, Faculty of Engineering, King <PERSON>’s University of Technology North Bangkok, Bangkok, Thailand"}], "References": []}, {"ArticleId": 80582881, "Title": "Extracting impervious surfaces from full polarimetric SAR images in different urban areas", "Abstract": "Accurate mapping of impervious surface in urban areas is of great demand in environmental and socio-economic studies since impervious surface growth is recognized as an indicator of urbanization. To demonstrate the potential of full polarimetric Synthetic Aperture Radar (SAR) in impervious surface detection in different urban areas, this study focused on the exploitation of only SAR data. Three cities with different levels of urbanization – Tehran, Kordkuy, and Arak – have been selected to reduce the effect of input data on achieved results. Advanced Land Observing Satellite/Phased Array L-band Synthetic Aperture Radar (ALOS/PALSAR) images have been classified by support vector machine (SVM) with the help of training data from high-resolution satellite images. Quantitative assessment of classification accuracy revealed that Kordkuy, a not fully developed city (i.e. 84.2%) has the lowest accuracy and Arak, a medium urbanized city, has the highest accuracy (i.e. 90.0%). To further explore the efficiency of full polarimetric SAR, grey level co-occurrence matrix (GLCM) texture of polarized bands has been extracted and put into the classification procedure. The texture information of SAR data provided positive contribution to the impervious surface estimation in three study cases. The improvement is especially noted in dark impervious surface class. All three study areas show an increase of about 6–8% in classification accuracy. The results prove that single use of full polarimetric SAR images holds high potential in identifying impervious surfaces in urban areas. The findings are of great importance in frequent urban impervious surface mapping and monitoring especially in cloud-prone area, where the use of optical data as well as the fusion of optic and SAR data are limited.", "Keywords": "", "DOI": "10.1080/01431161.2020.1723178", "PubYear": 2020, "Volume": "41", "Issue": "12", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Remote Sensing and GIS, Faculty of Geography, University of Tehran, Tehran, Iran"}], "References": []}, {"ArticleId": 80583112, "Title": "Petri-net-based dynamic scheduling of flexible manufacturing system via deep reinforcement learning with graph convolutional network", "Abstract": "To benefit from the accurate simulation and high-throughput data contributed by advanced digital twin technologies in modern smart plants, the deep reinforcement learning (DRL) method is an appropriate choice to generate a self-optimizing scheduling policy. This study employs the deep Q-network (DQN), which is a successful DRL method, to solve the dynamic scheduling problem of flexible manufacturing systems (FMSs) involving shared resources, route flexibility, and stochastic arrivals of raw products. To model the system in consideration of both manufacturing efficiency and deadlock avoidance, we use a class of Petri nets combining timed-place Petri nets and a system of simple sequential processes with resources (S<sup>3</sup>PR), which is named as the timed S<sup>3</sup>PR. The dynamic scheduling problem of the timed S<sup>3</sup>PR is defined as a Markov decision process (MDP) that can be solved by the DQN. For constructing deep neural networks to approximate the DQN action-value function that maps the timed S<sup>3</sup>PR states to scheduling rewards, we innovatively employ a graph convolutional network (GCN) as the timed S<sup>3</sup>PR state approximator by proposing a novel graph convolution layer called a Petri-net convolution (PNC) layer. The PNC layer uses the input and output matrices of the timed S<sup>3</sup>PR to compute the propagation of features from places to transitions and from transitions to places, thereby reducing the number of parameters to be trained and ensuring robust convergence of the learning process. Experimental results verify that the proposed DQN with a PNC network can provide better solutions for dynamic scheduling problems in terms of manufacturing performance, computational efficiency, and adaptability compared with heuristic methods and a DQN with basic multilayer perceptrons.", "Keywords": "Dynamic scheduling ; Petri nets ; Deep reinforcement learning ; Graph convolutional networks ; Digital twin", "DOI": "10.1016/j.jmsy.2020.02.004", "PubYear": 2020, "Volume": "55", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Big Data & Software Engineering, Chongqing University, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Lab of CAD&CG, Zhejiang University, Hangzhou, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Zhejiang University, Hangzhou, China"}], "References": []}, {"ArticleId": 80583115, "Title": "A time-varying geometry modeling method for parts with deformation during machining process", "Abstract": "Deformation due to residual stress is a significant issue during the machining of thin-walled parts with low rigidity. If there are multiple processes with deformation during machining, some process suitability issues will appear. On this occasion, the actual geometric state of the deformed workpiece is needed for process adjustment. However, it is still a challenge to obtain the complete geometry information of deformed workpiece accurately and efficiently. In order to address this issue, a time-varying geometry modeling method, combining cutting simulation and in-process measurement, is proposed in this paper. The deformed workpiece model can be reconstructed via transforming the deformed workpiece with only a small amount of the measurement points by superimposing material removal and workpiece deformation simulation according to a time sequence, which takes advantage of the proposed Curved Surface Mapping based Geometric Representation Model ( CSMGRM ). Machining experiment of a typical structural part has shown that the deformed geometry model of the whole workpiece can be reconstructed within the error of 0.05mm, which is less than one tenth of the finish machining allowance in general cases, and it is sufficient to meet the accuracy requirements for interference or overcut/undercut analysis and process adjustment.", "Keywords": "Structural parts ; Deformation representation ; Cutting simulation ; Curved surface mapping ; Time-varying geometry modeling", "DOI": "10.1016/j.jmsy.2020.02.002", "PubYear": 2020, "Volume": "55", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China"}, {"AuthorId": 2, "Name": "Yingguang Li", "Affiliation": "National Key Laboratory of Science and Technology on Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Liu", "Affiliation": "National Key Laboratory of Science and Technology on Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "National Key Laboratory of Science and Technology on Helicopter Transmission, Nanjing University of Aeronautics and Astronautics, Nanjing, 210016, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Hong Kong University of Science and Technology, Clear Water Bay, HK, China"}], "References": [{"Title": "Enabling technologies and tools for digital twin", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "3", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 80583123, "Title": "Decomposition-based bi-objective optimization for sustainable robotic assembly line balancing problems", "Abstract": "Due to the increasing greenhouse gas emissions and the energy crisis, the manufacturing industry which is one of the most energy intensive sector is paying close attention to the improvement of environmental performance efficiency. Therefore, in this paper the automated assembly line is balanced in a sustainable way which aims to optimize a green manufacturing objective (the total energy consumption) and a productivity-related objective (similar working load) simultaneously. A comprehensive total energy consumption of each processing stage was analyzed and modeled. To make the model more practical, a sequence-based changeover time and robots with different efficiencies and energy consuming rates are considered and optimized. To properly solve the problem, the proposed novel optimal solution takes the well-known MOEA/D as a base and incorporates a well-designed coding scheme and a problem-specific local search mechanism. Computational experiments are conducted to evaluated each improving strategies of the algorithm and its superiority over two other high-performing multi-objective optimization methods. The model allows decision makers to select more sustainable assembly operations based on their decision impacts in both productivity and energy-saving.", "Keywords": "Multi-objective optimization ; Energy consumption models ; Robot ; Assembly line balancing ; MOEA/D", "DOI": "10.1016/j.jmsy.2020.02.005", "PubYear": 2020, "Volume": "55", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Tongji University, Caoan Road 4800, Shanghai, China;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Tongji University, Caoan Road 4800, Shanghai, China;@tongji.edu.cn"}], "References": []}, {"ArticleId": 80583153, "Title": "Performance evaluation for manufacturing systems under control-limit maintenance policy", "Abstract": "Maintenance decision-making in large manufacturing systems is complex as it requires the integration of various information. A control-limit policy is popular in practice, where maintenance is carried out when the degradation state of a machine reaches a threshold value. In this paper, by developing a framework based on discrete-time Markov chain models, we evaluate the system performance under the control-limit policy, in manufacturing systems that consist of multi-state machines and intermediate buffers. An exact analysis is performed for a two-machine-one-buffer system and an approximation method based on system decomposition is developed for multi-stage systems. Both steady-state and transient performance is analyzed. Numerical examples are presented to demonstrate the accuracy of the proposed method and the impact of different parameters (e.g., buffer capacity, uncertainty in the maintenance duration) on the system performance.", "Keywords": "Maintenance ; Manufacturing systems ; Performance evaluation ; Markov Chain", "DOI": "10.1016/j.jmsy.2020.03.003", "PubYear": 2020, "Volume": "55", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Mechanical & Aerospace Engineering, Rutgers University, Piscataway, NJ, USA;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Industrial & Systems Engineering, Rutgers University, Piscataway, NJ, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Dept. of Mechanical & Industrial Engineering, Northeastern University, Boston, MA, USA"}], "References": []}, {"ArticleId": 80583286, "Title": "The Unexpected Downside of Paying or Sending Messages to People to Make Them Walk", "Abstract": "People do not exercise as much and as regularly as they should. To support users in adopting healthy exercise routines, app designers integrate persuasive techniques in their apps. In this study, we focus on two of these techniques, i.e., offering\n tangible rewards \n and sending\n motivational messages \n to users. Past research has demonstrated the effects of these techniques in nudging recipients to increase their physical activity levels. However, the effect of these interventions on the\n intrinsic motivation \n of the participants has not yet been studied. We conducted a 10-month study involving 208 participants; this research consisted of a 3-month baseline (pre-phase), a 4-month experiment and a 3-month follow-up (post-phase). The participants were randomly assigned to one of the following three interventions: either they receive money ((i.) through a fixed incentive or (ii.) a lottery), or (iii.) informative messages. Their daily goal was to walk 10K steps. Through their smart phones, we recorded how many steps they walked every day. These interventions had no effect on the main outcome variable (i.e., the number of steps). However, the manipulations produced a detrimental effect on the intrinsic motivation of the participants, measured through a standardized questionnaire. This negative effect extended into the follow-up period. Our study reveals that tangible rewards and motivational messages decrease the intrinsic motivation of the participants, hence their connected physical activity. In our findings, we highlight the importance of intrinsic motivation in setting up healthy exercise routines that will be carried on autonomously by the participants after the period of the intervention. Finally, we present implications for the design of persuasive apps.", "Keywords": "LMER; longitudinal study; persuasive technology; randomized controlled trial", "DOI": "10.1145/3365665", "PubYear": 2020, "Volume": "27", "Issue": "2", "JournalId": 14202, "JournalTitle": "ACM Transactions on Computer-Human Interaction", "ISSN": "1073-0516", "EISSN": "1557-7325", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Lausanne, Lausanne, VD, Switzerland"}, {"AuthorId": 2, "Name": "Gabriela Villalobos-Zuñiga", "Affiliation": "University of Lausanne, Lausanne, VD, Switzerland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Lausanne, Lausanne, VD, Switzerland"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "HES-SO, Sion, VS, Switzerland"}], "References": []}, {"ArticleId": 80583581, "Title": "Solving two-dimensional integral equations of the second kind on non-rectangular domains with error estimate", "Abstract": "<p>In this paper, the collocation method is applied on two-dimensional integral equations of the second kind on non-rectangular domains. Since the domains of these equations are non-rectangular and so directly applying the collocation method for them is difficult, at first, the integral equations are converted to equivalent integral equations on rectangular domains. Then, two-dimensional Jacobi collocation method is applied. Furthermore, an error estimate for the method is investigated and several examples demonstrate the accuracy and efficiency of the method.</p>", "Keywords": "2D integral equations; Non-rectangular domains; Collocation method; Jacobi polynomials", "DOI": "10.1007/s00366-019-00727-y", "PubYear": 2020, "Volume": "36", "Issue": "2", "JournalId": 3930, "JournalTitle": "Engineering with Computers", "ISSN": "0177-0667", "EISSN": "1435-5663", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Shahid Beheshti University, Tehran, Iran;Department of Cognitive Modelling, Institute for Cognitive and Brain Sciences, Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Sciences, Shahid Beheshti University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics and Computer Sciences, Islamic Azad University, Bardaskan Branch, Bardaskan, Iran"}], "References": []}, {"ArticleId": 80583600, "Title": "A Systematic Literature Review of Data Classification Techniques", "Abstract": "The data mining and their different applications are becomes more popular now in these days a number of large and small scale applications are developed with the help of data mining techniques i.e. predictors, regulators, weather forecasting systems and business intelligence. Many of classification algorithms are available to analyze data. Classification is used to classify each item in a data set into one of a predefined set of classes or groups. Classification is the chore of identifying a model or function. There are two kinds of model are available for namely supervised and unsupervised. The performance and accuracy of the supervised data mining techniques are higher as compared to unsupervised techniques therefore in sensitive applications the supervised techniques are used for prediction and classification. In this presented work the supervised learning based data mining techniques for classification and prediction are analyzed.", "Keywords": "Data Mining;Classification;Decision Tree;KNN Classification;supervised learning", "DOI": "10.5120/ijca2020919971", "PubYear": 2020, "Volume": "177", "Issue": "44", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80583601, "Title": "The Exact Vacuum Solution for <PERSON>sner Metric from Bianchi Type-I Cosmological Model", "Abstract": "An exact solution of the vacuum Einstein field equations (VEFEs) has been obtained of a spatially homogeneous and anisotropic (SHA) Bianchi type-I cosmological model by <PERSON><PERSON>. The <PERSON><PERSON> metric is shown to be a special case, and the exact vacuum solution of <PERSON><PERSON> form model is obtained. Some physical properties of the model have been discussed.", "Keywords": "Bianchi type-I;Vacuum solution;Cosmological model;Kasner form", "DOI": "10.5120/ijca2020919969", "PubYear": 2020, "Volume": "177", "Issue": "45", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80583604, "Title": "Performance Measure of Fuzzy Simple Queue with Balking and Feedback Customers using Symmetric Heptagonal Fuzzy Numbers", "Abstract": "This paper investigates the simple queue with balking and feedback customers by using the fuzzy set theory. The main proposal is a mathematical programming approach to develop the membership function of the performance measures of the model, in which the arrival rate and service rate are defined as fuzzy numbers. The <PERSON><PERSON><PERSON>’s extension principle and", "Keywords": "Feedback queue;Simple queue;Symmetric heptagonal fuzzy number.", "DOI": "10.5120/ijca2020919972", "PubYear": 2020, "Volume": "177", "Issue": "47", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "M. S.", "Affiliation": ""}], "References": []}, {"ArticleId": 80583605, "Title": "IoT based Energy Saving Strategies for Student Hall at University of Chittagong, Bangladesh", "Abstract": "Controlling household electric devices automatically and remotely is of great beneficial and energy saving in modern life. We can design a small IoT based setup that can do a lot of work for us to make life easier. This system can be used in home, office, industries and all other places. Even we can use the system in the case of street light. Disabled people can be greatly benefited by using the system. To implement the system, we need a micro-controller with WIFI module to connect to the Internet. A front-end mobile application and a backend server will work together to make the project work smartly and intelligently. A student residence named ‘<PERSON><PERSON> Hall’ in Chittagong University, Bangladesh has been considered for the implementation of the proposed system. It has been found that 24% energy can be saved using the proposed system and it found very much cost effective.", "Keywords": "IoT;Home-automation;nodeMCU;WIFI module;Cloud Server;Remote Controlling;Energy Saving;Energy Costing", "DOI": "10.5120/ijca2020919986", "PubYear": 2020, "Volume": "177", "Issue": "46", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80583606, "Title": "Metaheuristic Tuning Generalisation by Cross-Validated Racing", "Abstract": "Many tuning methods are based on concepts of sensitivity analysis combined with heuristics that tend to reduce the search space by eliminating less promising configurations. Nevertheless, tuning parameters is a task that requires specific and timeconsuming experiments, especially when involving large problem instances. This is particularly due to existing methods were not designed to efficiently generalise a tuning of parameters to other instances that did not participate of the training process. In this paper, the recently proposed tuning method named Cross- Validated Racing (CVR) is revised in order to clarify theoretical fundamentals of tuning problem and expand the experiments to make possible evaluating its generalisation capacity against the reduction in the size of the training set. For validation, the Biased Random-Key Evolutionary Clustering Search (BRKeCS) is applied to solve scalable instance groups of Permutation Flow Shop Scheduling Problem. The computation results have demonstrated that CVR is robust in finding an effective parameter setting, requiring training process in only a half of total instance set.", "Keywords": "Tuning;Irace;Permutation Flow Shop Scheduling;BRKeCS;Evolutionary Clustering Search;Cross-Validated Racing Approach", "DOI": "10.5120/ijca2020919991", "PubYear": 2020, "Volume": "177", "Issue": "47", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80583662, "Title": "A dynamic model for process flowsheet simulation of semi-batch precipitation of sparingly soluble salts", "Abstract": "Semi-batch precipitation of sparingly soluble materials is an important unit operation in the chemical or pharmaceutical industry. Unfortunately, dynamic process flowsheet simulation of this unit operation has not been possible yet, as existing models for stirred-tank reactors are far too computationally intensive. In this work, we present a real-time capable semi-batch model for precipitation process flowsheet simulation. Its outstanding numerical performance is based on a frequently triggered approximation of the steady-state reaction zone in the tank. We validate the model by barium sulfate precipitation experiments and investigate the semi-batch specific process dynamics. Furthermore, the model is applied to simulation setups with dynamic boundaries and process parameters to characterize the influence of dynamic process parameters on the product particle size distribution (PSD). It is shown that dynamic process parameters can influence the final PSD heavily. The model will, therefore, be a useful tool for future application in dynamic process optimization.", "Keywords": "Process flowsheet simulation ; Semi-batch precipitation ; Barium sulfate ; DYSSOL", "DOI": "10.1016/j.compchemeng.2020.106818", "PubYear": 2020, "Volume": "137", "Issue": "", "JournalId": 580, "JournalTitle": "Computers & Chemical Engineering", "ISSN": "0098-1354", "EISSN": "1873-4375", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology (KIT), Institute of Thermal Process Engineering, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Karlsruhe Institute of Technology (KIT), Institute of Thermal Process Engineering, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Karlsruhe Institute of Technology (KIT), Institute of Thermal Process Engineering, Germany;Corresponding author"}], "References": []}, {"ArticleId": 80583663, "Title": "Enhancing image processing architecture using deep learning for embedded vision systems", "Abstract": "In recent years, the success and capabilities of embedded vision have showed up in embedded applications. The embedding of vision into electronic devices such as embedded medical applications is being driven by the availability of high-performance processors, integrating with deep learning algorithms, as well as advances in image processing technology. But, including image processing in embedded vision systems need huge amount of computational capabilities even to process a single image to detect an object and it's extremely challenging to implement in embedded systems. Implementing deep learning algorithms and testing it on a task specific data set could provide enhanced results. In this paper, an approach for enhancing image processing architecture using deep learning for embedded vision systems is proposed and analyzed. Implementing deep learning algorithms and testing it on embedded vision yielded effective results.", "Keywords": "Deep learning ; Embedded vision systems ; Embedded systems ; Image processing ; Feature extraction ; Convolutional neural networks ; Google inception network", "DOI": "10.1016/j.micpro.2020.103094", "PubYear": 2020, "Volume": "76", "Issue": "", "JournalId": 4498, "JournalTitle": "Microprocessors and Microsystems", "ISSN": "0141-9331", "EISSN": "1872-9436", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Bharathidasan University, Trichy, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Bharathidasan University, Trichy, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Nehru Institute of Engineering and Technology, Coimbatore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, Sri Ramanujar Engineering College, Chennai- 600127, Tamil Nadu, India"}], "References": []}, {"ArticleId": 80583664, "Title": "On Studying the Inter-relationships amongst the Challenges Faced by Traditional Handloom Industries of Bangladesh and India and the Success Factors behind the Digitization of these Industries to overcome these Barriers", "Abstract": "In both the countries of Asia i.e. India as well as Bangladesh, handloom textiles constitute a timeless facet of the their rich cultural heritage. They occupy the second position only after agriculture in providing livelihood to the people. However despite the popularity, traditional handloom textiles faces many challenges especially in recent times which has paved the way for the digitization of handloom industries . Present research work explores both the challenges faced by traditional handloom weavers and industrial sector and how digitization and technology have helped in reshaping, renovating and upsell them.", "Keywords": "Solid waste management ;ISM methodology ;Incineration;Solid waste;Hazardous;Non-hazardous", "DOI": "10.5120/ijca2020919945", "PubYear": 2020, "Volume": "177", "Issue": "43", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "V. K.", "Affiliation": ""}], "References": []}, {"ArticleId": 80583665, "Title": "Sentiment Analysis of Social Media Micro Blogs using Power Links and Genetic Algorithms", "Abstract": "In the current years, social media became one of the most important sources of data for different data analytic purposes. One of the most important issues is how to map different trends in social media and define the relation between different groups based on their sentiment or interests. In this paper, a two-phase approach is used for clustering a set of blogs. At the first phase, the approach builds a lexicon that provides the polarity of each word. In the second phase, the approach clusters the blogs bases on the polarity features and the Power Link features. The output of the second phase is used as the input of the first phase to get an improved lexicon. This process will continue in a loop between phase one and phase 2 till a stable set of clusters is gotten. The approach aims to develop a non-supervised cluster agent that can correctly cluster micro blogs and define different interests of different groups of people. The results of the approach are expressed in terms of precision, recall and F-measure.", "Keywords": "Sentiment Analysis;Evolution Calculation;Genetic Algorithms;Power Links;Social Media;Micro Blogs.", "DOI": "10.5120/ijca2020919967", "PubYear": 2020, "Volume": "177", "Issue": "44", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80583666, "Title": "Brain Neuroscience Cognitive Effects for Efficient Neurobusiness", "Abstract": "Recently, neuroscientists are considering neurobusiness as a mind of the market. A literature survey has been conducted that involves the nature of neurons and communication among themselves. A model based upon cognitive effects of brain for efficient neurobusiness is discussed with the motive to think in a way to help for improving human understanding along with the enhancement of brain intelligence for producing efficient and effective results. The concept of neuroscience, management and engineering based upon neurobusiness may significantly improve the growth of work integration in different domains. The main focus of neurobusiness is to improve the branding, and to develop the individual or unique level of thinking leading to innovative practices in the modern marketing trends. In addition, this concept also provides the best option to choose any newly launched market product through neuromarketing. Consequently, it helps to make customers’ search easy for the selection of brand of their choice during shopping.", "Keywords": "Neurobusiness;Neuromarketing;Brain Model;Digital Marketing;Neurons;Neuron Structure and Learning Platform.", "DOI": "10.5120/ijca2020919160", "PubYear": 2020, "Volume": "177", "Issue": "47", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "R<PERSON> <PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "K-<PERSON> Clustering", "Abstract": "In this paper, I apply K-mean clustering method to test the validity of Bank notes by separating notes in the real or fake group automatically with Python 3 in the Jupyter Lab.", "Keywords": "K-Mean clustering;center;group;distance;iteration", "DOI": "10.5120/ijca2020919937", "PubYear": 2020, "Volume": "177", "Issue": "47", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": ********, "Title": "Performance Optimization by Integrating Memoization and MPI_Info Object for Sieve of Prime Numbers", "Abstract": "Sieving prime numbers is an idle example of linear time algorithm since the first sieve of this kind proposed by <PERSON><PERSON><PERSON><PERSON>. Afterward many sieving algorithm are proposed such as-: <PERSON><PERSON> of Sundaram, <PERSON><PERSON> of Atkin, <PERSON><PERSON> of Sorenson and wheel factorization of prime number. In this paper we have proposed the integration of parallelism with these sieving algorithm. We have proposed MPI_Info object with memoization to avoid redundant steps during prime number processing and adding them into the sieve. Nevertheless this paper also demonstrates the MPI Binding with familiar/popular object oriented programming language such as-: C++ and Java. This binding done through the two different tools which includes OpenMPI and MPJ Express.", "Keywords": "Java;Open MPI;Prime Table;Java Native Interface;Message Passing", "DOI": "10.5120/ijca2020919983", "PubYear": 2020, "Volume": "177", "Issue": "47", "JournalId": 12839, "JournalTitle": "International Journal of Computer Applications", "ISSN": "", "EISSN": "0975-8887", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80583766, "Title": "High-dimensional count data clustering based on an exponential approximation to the multinomial Beta-Liouville distribution", "Abstract": "In this paper, we propose a mixture model for high-dimensional count data clustering based on an exponential-family approximation of the Multinomial Beta-Liouville distribution, which we call EMBL. We deal simultaneously with the problems of fitting the model to observed data and selecting the number of components. The learning algorithm automatically selects the optimal number of components and avoids several drawbacks of the standard Expectation-Maximization algorithm, including the sensitivity to initialization and possible convergence to the boundary of the parameter space. We demonstrate the effectiveness and robustness of the proposed clustering approach through a set of extensive empirical experiments that involve challenging real-world applications. The results reveal that the novel proposed model strives to achieve higher accuracy compared to the state-of-the-art generative models for count data clustering. Furthermore, the superior performance of EMBL demonstrates its flexibility and ability to address the burstiness phenomenon successfully, as well as shows its computational efficiency, especially when dealing with sparse high-dimensional vectors.", "Keywords": "Exponential family ; Finite mixtures ; Model selection ; Count data ; CEM ; Probabilistic kernels", "DOI": "10.1016/j.ins.2020.03.028", "PubYear": 2020, "Volume": "524", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Concordia Institute for Information Systems Engineering (CIISE), Concordia University, Montreal, QC, Canada;College of Computer Science and Engineering, University of Jeddah, Saudi Arabia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Concordia Institute for Information Systems Engineering (CIISE), Concordia University, Montreal, QC, Canada"}], "References": [{"Title": "Combining unsupervised and supervised learning in credit card fraud detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "557", "Issue": "", "Page": "317", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 80583775, "Title": "Implications of government subsidies for waste cooking oil considering asymmetric information", "Abstract": "<h3>Purpose</h3> <p>Without proper treatment, waste cooking oil (WCO) will bring serious environmental and health hazards, which can be effectively alleviated by converting it into biofuel. Subsidies from the government usually play a significant role in encouraging recycling activities and supporting sustainable supply chain. This paper aims to quantitatively investigate the incentive effects of government subsidies under asymmetric information.</p> <h3>Design/methodology/approach</h3> <p>This paper applies the principal–agent contract to compare the incentive effects of the two widely used subsidy modes (raw material price subsidy [MS] and finished product sale subsidy [FS]) in a management system which consists of the government and a bio-firm where the bio-firm’s conversion rate of the WCO remains as private information.</p> <h3>Findings</h3> <p>Results indicate that the two subsidy modes have the same performance under symmetric information, while under asymmetric information, the government always prefers the MS mode which is more environment-friendly. Besides, if the average conversion rate is large or the uncertainty level of the asymmetric information is moderate, the MS mode is Pareto-improving compared with the FS mode for the government and the high-type bio-firm. Only when the average conversion rate is small or the uncertainty level is very small/very large, the high-type bio-firm welcomes the FS mode.</p> <h3>Originality/value</h3> <p>Different from the existing literature, this paper applies the principal–agent contract into the WCO management system and quantitatively compares the two subsidy modes taking the practical problem of asymmetric information into consideration.</p>", "Keywords": "Game theory;Information asymmetry;Subsidy mode;Waste cooking oil", "DOI": "10.1108/K-10-2019-0708", "PubYear": 2021, "Volume": "50", "Issue": "2", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Management and Economics, Tianjin University , Tianjin, China"}, {"AuthorId": 2, "Name": "Wansheng Tang", "Affiliation": "College of Management and Economics, Tianjin University , Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Management and Economics, Tianjin University , Tianjin, China"}], "References": []}, {"ArticleId": 80583777, "Title": "Asymmetrical third-person effects on the perceptions of online risk and harm among adolescents and adults", "Abstract": "Although research has identified a range of opportunities, risks, and harms related to online social networking, the public debate on online risks follows a set pattern by which members of older age groups (parents, regulators) hold a picture of members of younger age groups (teenagers, digital natives) at a uniformly high level of risk. Perceptions of online risk, however, are prone to third-person effects in which individuals perceive risks to be more apparent in others than themselves. This study investigated third-person effects across age groups to further our understanding of the set positions found in current public debate. Multivariate analysis was used to compare adolescent and adult users’ personal and third-person perceptions of common psycho-social risks associated with social networking engagement in a sample of 506 UK-based Facebook users (53% male; 13–77 years). Results indicated that rates of exposure to online vulnerabilities were similar for both age groups. However, differences in adult and adolescent perceptions of risk highlighted apparent mismatches between reported exposure to risk and an individual’s perceptions, with adults demonstrating lower personal perceptions and higher third-person perceptions of risk than their adolescent counterparts. The research considers the implications of risk perception on an individual’s online vulnerability.", "Keywords": "Facebook ; risk perception ; online vulnerability ; third-person effect ; adolescent users ; online networking", "DOI": "10.1080/0144929X.2020.1742380", "PubYear": 2021, "Volume": "40", "Issue": "11", "JournalId": 3971, "JournalTitle": "Behaviour & Information Technology", "ISSN": "0144-929X", "EISSN": "1362-3001", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Psychology, School of Social Sciences, Nottingham Trent University, Nottingham, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Psychology, School of Social Sciences, Nottingham Trent University, Nottingham, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Psychology, School of Social Sciences, Nottingham Trent University, Nottingham, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Psychology, School of Social Sciences, Nottingham Trent University, Nottingham, UK"}], "References": []}, {"ArticleId": 80583780, "Title": "Temporary workers and firm performance", "Abstract": "<h3>Purpose</h3> <p>Although temporary work arrangements play a strategically important role in contemporary economic development, its influence on firm performance has remained elusive. In our work we measure firm performance from the systematic perspective, including both innovation and financial performance. Therefore, the purpose of this study is to test a model that specifies the influence of temporary work arrangements on the two indicators of firm performance, namely, innovation and sales.</p> <h3>Design/methodology/approach</h3> <p>This research is based on the Business Environment and Enterprise Performance Survey (BEEPS, 2009) created by the European Bank for Reconstruction and Development and the World Bank. As the relationship between temporary workers and firm performance could be contingent on institutional setting, we divide our sample in three country groups: European Union (EU), European countries outside the European Union (non-EU) and the Commonwealth of Independent States (CIS) countries. Furthermore, we analyze the actual question on all three groups of countries separately.</p> <h3>Findings</h3> <p>Findings provide support for the positive relationship between the percentage of temporary workers and innovation performance. On the other hand, the evidence reveals a negative link between the percentage of temporary workers and financial performance measured by sales. Taking into account country-specific effects, the results remain negative when examining the influence of the percentage of temporary workers on the financial performance measured by sales for all three country groups (EU, non-EU and CIS countries). However, the positive and significant effect of the share of temporary workers on innovation performance holds only for non-EU and CIS countries but is not observed for EU countries.</p> <h3>Practical implications</h3> <p>Given the importance of temporary work arrangements in terms of a systematic approach in contemporary business, policymakers should continue to work on improving their status to overcome potential negative outcomes related to the temporary workers’ engagement. In addition, managers of the firms should be aware that temporary workers could be a valuable source of innovation; however, they could hamper other aspects of firm performance. Therefore, they should be cautious when hiring temporary workers and find the “best balance” between permanent and temporary workers.</p> <h3>Originality/value</h3> <p>The obtained findings highlight the importance of performance context systematic approach in studying the impact of temporary workers and firm performance. Based on the obtained findings, we may suggest that knowledge and creativity accumulation through temporary workers’ engagement is beneficial for innovation performance improvement. Conversely, long-lasting labour engagement is necessary for financial performance improvement.</p>", "Keywords": "Innovation;Sales;Developing countries;Temporary workers", "DOI": "10.1108/K-11-2019-0765", "PubYear": 2021, "Volume": "50", "Issue": "5", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Tourism and Hotel Management, University of Montenegro , Kotor, Montenegro"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Tourism and Hotel Management, University of Montenegro , Kotor, Montenegro"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Tourism and Hotel Management, University of Montenegro , Kotor, Montenegro"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Organization Studies in Novo Mesto, Novo Mesto, Slovenia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Informatics, Faculty of Business and Economics, University of Zagreb , Zagreb, Croatia"}], "References": []}, {"ArticleId": 80583863, "Title": "Semantic image segmentation with shared decomposition convolution and boundary reinforcement structure", "Abstract": "<p>Deep convolutional neural networks (DCNNs) have shown excellent performances in the field of computer vision. In this paper, we propose a new semantic image segmentation model, and the two hallmarks of our architecture are the usage of shared decomposition convolution (SDC) operation and boundary reinforcement (BR) structure. SDC operation can extract dense features and increase correlation of features in the same group, which can relieve the grid artifact problem. BR structure combines the spatial information from different layers in DCNNs to enhance the spatial resolution and enrich target boundary position information simultaneously. The simulation results show that the proposed model can achieve 94.6% segmentation accuracy and 76.3% mIOU on PASCAL VOC 2012 database respectively, which verifies the effectiveness of the proposed model.</p>", "Keywords": "Convolutional neural networks; Semantic image segmentation; Shared decomposed convolution; Boundary reinforcement", "DOI": "10.1007/s10489-020-01671-x", "PubYear": 2020, "Volume": "50", "Issue": "9", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Sciences, Northeastern University, Shenyang, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Sciences, Northeastern University, Shenyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Sciences, Northeastern University, Shenyang, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Liu", "Affiliation": "College of Information Science and Engineering, Northeastern University, Shenyang, China"}], "References": []}, {"ArticleId": 80583889, "Title": "SWCNTs-based MEMS gas sensor array and its pattern recognition based on deep belief networks of gases detection in oil-immersed transformers", "Abstract": "MEMS gas sensor arrays and specially designed pattern recognition systems are the main research directions in the field of modern sensing technology in the engineering, especially in the smart sensing and monitoring of faults in large power equipment such as oil-immersed transformers. In this paper, the MEMS sensor array composed by eight SWCNTs-based (pure, OH functionalized, COOH functionalized, NH<sub>2</sub> functionalized by ethylenediamine, NH<sub>2</sub> functionalized by aniline, Ni-coated, Pd-doped, ZnO-doped) sensing units was palced in the fault characteristic gases (H<sub>2</sub>, CO, and C<sub>2</sub>H<sub>2</sub>) of oil-immersed transformers, and their gas-sensing characteristics were tested in single and mixed gas atmosphere. Combined with the DBN-DNN pattern recognition method, the qualitative identification and quantitative analysis of the sensor array in a mixed gas atmosphere was realized, and the accuracy and reliability of the results are higher than the traditional BPNN model.", "Keywords": "MEMS ; Gas sensor array ; SWCNTs ; Pattern recognition", "DOI": "10.1016/j.snb.2020.127998", "PubYear": 2020, "Volume": "312", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Power Transmission Equipment and System Security and New Technology, Chongqing University, Chongqing, 400030, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Power Transmission Equipment and System Security and New Technology, Chongqing University, Chongqing, 400030, China;Corresponding authors"}, {"AuthorId": 3, "Name": "Lingfeng Jin", "Affiliation": "State Key Laboratory of Power Transmission Equipment and System Security and New Technology, Chongqing University, Chongqing, 400030, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Power Transmission Equipment and System Security and New Technology, Chongqing University, Chongqing, 400030, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON> Li", "Affiliation": "School of Electronic and Electrical Engineering, Chongqing University of Arts and Sciences, Chongqing, 400030, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Engineering and Technology, Southwest University, Chongqing, 400715, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Power Transmission Equipment and System Security and New Technology, Chongqing University, Chongqing, 400030, China;Corresponding authors"}], "References": []}, {"ArticleId": 80583893, "Title": "Implementation of key predistribution scheme in WSN based on binary Goppa codes and Reed Solomon codes with enhanced connectivity and resiliency", "Abstract": "<p>Establishing secure transmissions of messages among the resource limited sensor nodes in wireless sensor network (WSN) is a critical issue and requires secret keys to be established among the communicating nodes. Key predistribution is the most commonly used technique and whereby secret keys are preloaded to the sensor nodes before their deployment into a hostile region. A WSN can be structured or unstructured, and sensor nodes may be deployed in an ad-hoc manner or pre-planned manner into the target field. As sensor nodes are low-cost electronic devices equipped with data processing, limited storage, communication and computation power, connectivity, and resiliency are the major focus in designing key predistribution scheme (KPS) for WSNs. Furthermore, we also expect the KPS to be scalable, enabling insertion of a set of new nodes in WSN at any point of time without altering the key setup of the already existing nodes. Combinatorial design is a widely used mathematical tool for the KPS. However, most of the KPS developed by using combinatorial design are not scalable. In this article, rather than using combinatorial techniques, we employ a code-based approach and design a new method for key predistribution by building a communication model and a connectivity model. We exploit the Reed Solomon code to establish our communication model, integrate the binary Goppa code to derive our connectivity model, and skillfully blend these two models to construct our code-based KPS. A C implementation of our KPS confirms the significant performance gain over the existing similar works. Additionally, nodes in our KPS are all self-dependent for communication and do not rely on cluster heads. Furthermore, we have control over the choice of the parameters in the underlying codes which makes our KPS flexible. To be specific, prior knowledge of additional node deployment increases the scalability of our connectivity model. By suitably choosing the parameters of the Goppa code at prior, we can accommodate extra nodes. More interestingly, our communication model is scalable without any previous knowledge of deployment.</p>", "Keywords": "Goppa codes; Reed Solomon codes; Connectivity key; Communication key", "DOI": "10.1007/s12652-020-01869-4", "PubYear": 2023, "Volume": "14", "Issue": "5", "JournalId": 1673, "JournalTitle": "Journal of Ambient Intelligence and Humanized Computing", "ISSN": "1868-5137", "EISSN": "1868-5145", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology, Kharagpur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics, Indian Institute of Technology, Kharagpur, India"}], "References": []}, {"ArticleId": 80583958, "Title": "Message from new EiC", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.ic.2020.104546", "PubYear": 2020, "Volume": "271", "Issue": "", "JournalId": 8189, "JournalTitle": "Information and Computation", "ISSN": "0890-5401", "EISSN": "1090-2651", "Authors": [], "References": []}, {"ArticleId": 80583981, "Title": "Compact quad‐band band‐pass filters using stepped‐impedance coupled‐line resonators", "Abstract": "<p>In this article, compact quad‐band band‐pass filters are realized by using stepped‐impedance coupled‐line quad‐mode resonators (SICLQMRs). The compactness of the quad‐mode resonator relies in its folded structure without extra space between the parallel lines. Unlike stepped‐impedance resonators, SICLQMRs provide more design freedoms for controlling the four resonating frequencies since the even‐ and odd‐mode equivalents can be separately assigned with characteristic impedances. Internal and external couplings are also parallel couplings, resulting in very compact dimensions of the filters. Simulated and measured S parameters are compared with good agreement, demonstrating the feasibility of the design.</p>", "Keywords": "quad‐band band‐pass filter;stepped‐impedance coupled‐line quad‐mode resonators", "DOI": "10.1002/mmce.22227", "PubYear": 2020, "Volume": "30", "Issue": "7", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Electronic and Information Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China; State Key Laboratory of Millimeter Waves, Southeast University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electronic and Information Engineering Department (DIEI), University of Perugia, Perugia, Italy"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Electronic and Information Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information, Jiangsu University of Science and Technology, Zhenjiang, China"}], "References": []}, {"ArticleId": 80583999, "Title": "Hierarchical Bayesian Choice of Laplacian ARMA Models Based on Reversible Jump MCMC Computation", "Abstract": "", "Keywords": "ARMA time series; Hierarchical Bayesian; Laplacian noise; Reversible jump MCMC", "DOI": "10.2991/ijcis.d.200310.006", "PubYear": 2020, "Volume": "13", "Issue": "1", "JournalId": 15927, "JournalTitle": "International Journal of Computational Intelligence Systems", "ISSN": "1875-6891", "EISSN": "1875-6883", "Authors": [{"AuthorId": 1, "Name": " <PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80584154, "Title": "FACt: FORTRAN toolbox for calculating fluctuations in atomic condensates", "Abstract": "We develop a FORTRAN code to compute fluctuations in atomic condensates (FACt) by solving the Bogoliubov–<PERSON> Gen<PERSON> (BdG) equations for two component Bose–Einstein condensate (TBEC) in quasi-two dimensions. The BdG equations are recast as matrix equations and solved self consistently. The code is suitable for handling quantum fluctuations as well as thermal fluctuations at temperatures below the critical point of Bose–Einstein condensation. The code is versatile, and the ground state density profile and low energy excitation modes obtained from the code can be easily adapted to compute different properties of TBECs — ground state energy, overlap integral, quasi particle amplitudes of BdG spectrum, dispersion relation and structure factor and other related experimental observables. Program summary Program Title: FACt Program Files doi: http://dx.doi.org/10.17632/5h348ndydg.1 Licensing provisions: MIT Programming language: FORTRAN 90 External routines/libraries: ARPACK Nature of problem: Compute the ground state density profile, ground state energy and chemical potential for individual species, evaluate the quasiparticle mode energies and corresponding amplitudes which can capture the transformation of the modes against the change of the parameters (intraspecies interaction, interspecies interaction, anisotropy parameter etc.) using <PERSON><PERSON><PERSON><PERSON><PERSON>–Bogoliubov theory with the <PERSON><PERSON> approximation. Calculate the overlap integral, dispersion relation and structure factor. Solution method: In the first step, the pair of coupled Gross–<PERSON> equations (CGPEs) is solved using split time-step Fourier pseudospectral method to compute the condensate density. To solve the BdG equations, as a basic input the first N b harmonic oscillator eigenstates are chosen as a basis to generate the BdG 011 matrix with dimension of 4 ( N b + 1 ) × 4 ( N b + 1 ) . Since the matrix size rapidly increases with N b , Arpack routines are used to diagonalize the BdG matrix efficiently. To compute the fluctuation and non-condensate density, a set of the low energy quasiparticle amplitudes above a threshold value of the Bose factor are considered. The equations are then solved iteratively till the condensate, and non-condensate densities converge to predefined accuracies. To accelerate the convergence we use the method of successive under-relaxation (SUR). Additional comments including restrictions and unusual features: For a large system size, if the harmonic oscillator basis size is also taken to be large, the dimension of the BdG matrix becomes huge. It may take several days to compute the low energy modes at finite temperature and this package may be computationally expensive. After successful computation of this package, one should obtain the equilibrium density profiles for TBEC, low energy Bogoliubov modes and the corresponding quasiparticle amplitudes. In addition, one can calculate the dispersion relation, structure factor, overlap integral, correlation function, etc. using this package with minimal modifications. In the theory section of the manuscript, we have provided the expressions to compute the above quantities numerically.", "Keywords": "<PERSON>–<PERSON> equation ; <PERSON><PERSON><PERSON>–<PERSON><PERSON> theory ; Bogoliubov–<PERSON> equations ; Quasiparticle spectra ; Goldstone mode ; Kohn/slosh mode ; Miscibility–immiscibility transition", "DOI": "10.1016/j.cpc.2020.107288", "PubYear": 2020, "Volume": "256", "Issue": "", "JournalId": 716, "JournalTitle": "Computer Physics Communications", "ISSN": "0010-4655", "EISSN": "1879-2944", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Physical Research Laboratory, Navarangpura, Ahmedabad 380 009, Gujarat, India;Max-Planck-Institut für Physik komplexer Systeme, Nöthnitzer Straße 38, 01187 Dresden, Germany"}, {"AuthorId": 2, "Name": "Sukla Pal", "Affiliation": "Physical Research Laboratory, Navarangpura, Ahmedabad 380 009, Gujarat, India;Dodd-Walls Centre for Photonic and Quantum Technologies and Department of Physics, University of Otago, Dunedin 9016, New Zealand;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Indian Institute of Technology Ropar, Rupnagar, Punjab 14001, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Physical Research Laboratory, Navarangpura, Ahmedabad 380 009, Gujarat, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Physics, Bharathidasan University, Tiruchirappalli, 620 024, India;Department of Medical Physics, Bharathidasan University, Tiruchirappalli 620 024, India"}], "References": []}, {"ArticleId": 80584185, "Title": "Deep learning model for end-to-end approximation of COSMIC functional size based on use-case names", "Abstract": "Context COSMIC is a widely used functional size measurement (FSM) method that supports software development effort estimation. The FSM methods measure functional product size based on functional requirements. Unfortunately, when the description of the product’s functionality is often abstract or incomplete, the size of the product can only be approximated since the object to be measured is not yet fully described. Also, the measurement performed by human-experts can be time-consuming, therefore, it is worth considering automating it. Objective Our objective is to design a new prediction model capable of approximating COSMIC-size of use cases based only on their names that is easier to train and more accurate than existing techniques. Method Several neural-network architectures are investigated to build a COSMIC size approximation model. The accuracy of models is evaluated in a simulation study on the dataset of 437 use cases from 27 software development projects in the Management Information Systems (MIS) domain. The accuracy of the models is compared with the Average Use-Case approximation (AUC), and two recently proposed two-step models—Average Use-Case Goal-aware Approximation (AUCG) and Bayesian Network Use-Case Goal AproxImatioN (BN-UCGAIN). Results The best prediction accuracy was obtained for a convolutional neural network using a word-embedding model trained on Wikipedia+Gigaworld. The accuracy of the model outperformed the baseline AUC model by ca. 20%, and the two-step models by ca. 5–7%. In the worst case, the improvement in the prediction accuracy is visible after estimating 10 use cases. Conclusions The proposed deep learning model can be used to automatically approximate COSMIC size of software applications for which the requirements are documented in the form of use cases (or at least in the form of use-case names). The advantage of the model is that it does not require collecting historical data other than COSMIC size and names of use cases.", "Keywords": "Functional size approximation ; Approximate software sizing methods ; COSMIC ; Deep learning ; Word embeddings ; Use cases", "DOI": "10.1016/j.infsof.2020.106310", "PubYear": 2020, "Volume": "123", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Poznan University of Technology, Faculty of Computing and Telecommunications, ul. Piotrowo 2, 60-965 Poznań, Poland;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>yl<PERSON><PERSON>", "Affiliation": "Poznan University of Technology, Faculty of Computing and Telecommunications, ul. Piotrowo 2, 60-965 Poznań, Poland"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Chalmers University of Gothenburg Sweden"}], "References": []}, {"ArticleId": 80584401, "Title": "Automatic highlighting of the region of interest in computed tomography images of the lungs", "Abstract": "<p>This article discusses the creation of masks for highlighting the lungs in computed tomography images using three methods – the Otsu method, a simple convolutional neural network consisting of 10 identical layers, and the convolutional neural network U-Net. We perform a study and comparison of methods used for automatically highlighting the region of interest (ROI) in computed tomography images of the lungs, which were provided as a courtesy from the Clinics of Samara State Medical University. The solution to this problem is relevant, because medical workers have to manually select the ROI as the first step of the automated processing of lung CT images. An algorithm for post-processing images based on the search for contours, which allows one to improve the quality of segmentation, is proposed. It is concluded that the U-Net highlights the ROI relating to the lung better than the other two methods. At the same time, the simple convolutional neural network highlights the ROI with an accuracy of 97.5%, which is better than the accuracy of 96.7% of the Otsu method and 96.4% of the U-Net.</p>", "Keywords": "image processing; computed tomography of the lungs; convolutional neural networks; U-Net.", "DOI": "10.18287/2412-6179-CO-659", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia, IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS, Molodogvardeyskaya 151, 443001, Samara, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara State Medical University, Samara, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara State Medical University, Samara, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia, IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS, Molodogvardeyskaya 151, 443001, Samara, Russia"}], "References": []}, {"ArticleId": 80584402, "Title": "3D-generalization of impulse noise removal method for video data processing", "Abstract": "<p>The paper proposes a generalized method of adaptive median impulse noise filtering for video data processing. The method is based on the combined use of iterative processing and transformation of the result of median filtering based on the Lorentz distribution. Four different combinations of algorithmic blocks of the method are proposed. The experimental part of the paper presents the results of comparing the quality of the proposed method with known analogues. Video distorted by impulse noise with pixel distortion probabilities from 1% to 99% inclusive was used for the simulation. Numerical assessment of the quality of cleaning video data from noise based on the mean square error (MSE) and structural similarity (SSIM) showed that the proposed method shows the best result of processing in all the considered cases, compared with the known approaches. The results obtained in the paper can be used in practical applications of digital video processing, for example, in systems of video surveillance, identification systems and control of industrial processes.</p>", "Keywords": "digital video processing; adaptive filtering; median filter.", "DOI": "10.18287/2412-6179-CO-577", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "North-Caucasus Federal University, 355009, Russia, Stavropol, Pushkin street 1"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "North-Caucasus Federal University, 355009, Russia, Stavropol, Pushkin street 1"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "North-Caucasus Federal University, 355009, Russia, Stavropol, Pushkin street 1"}], "References": []}, {"ArticleId": 80584408, "Title": "Adaptive interpolation based on optimization of the decision rule in a multidimensional feature space", "Abstract": "<p>An adaptive multidimensional signal interpolator is proposed, which selects an interpolating function at each signal point by means of the decision rule optimized in a multidimensional feature space using a decision tree. The search for the dividing boundary when splitting the decision tree vertices is carried out by a recurrence procedure that allows, in addition to the search for the boundary, selecting the best pair of interpolating functions from a predetermined set of functions of an arbitrary form. Results of computational experiments in nature multidimensional signals are presented, confirming the effectiveness of the adaptive interpolator.</p>", "Keywords": "multidimensional signal; adaptive interpolation; multidimensional feature; optimization; interpolation error.", "DOI": "10.18287/2412-6179-CO-661", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia, IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia"}], "References": []}, {"ArticleId": 80584421, "Title": "Orbital angular momentum and topological charge of a Gaussian beam with multiple optical vortices", "Abstract": "<p>Here we study theoretically and numerically a Gaussian beam with multiple optical vortices with unitary topological charge (TC) of the same sign, located uniformly on a circle. Simple expressions are obtained for the Gaussian beam power, its orbital angular momentum (OAM), and TC. We show that the OAM normalized to the beam power cannot exceed the number of vortices in the beam. This OAM decreases with increasing distance from the optical axis to the centers of the vortices. The topological charge, on the contrary, is independent of this distance and equals the number of vortices. The numerical simulation corroborates that after passing through a random phase screen (diffuser) and propagating in free space, the beams of interest can be identified by the number of local intensity minima (shadow spots) and by the OAM.</p>", "Keywords": "Gaussian beam; optical vortex; phase singularity; orbital angular momentum; topological charge; random screen; diffuser; scattering medium.", "DOI": "10.18287/2412-6179-CO-632", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 3, "Name": "D.S. Kali<PERSON>ina", "Affiliation": "Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}], "References": []}, {"ArticleId": 80584422, "Title": "Vortex energy flow in the tight focus of a non-vortex field with circular polarization", "Abstract": "<p>Using <PERSON><PERSON> formulas, we show that an axisymmetric circularly polarized vortex-free field can be focused into a sharp subwavelength focal spot, around which there is a region where the light energy flow propagates along a spiral. This effect can be explained by the conversion of the spin angular momentum of the circularly polarized field into the orbital angular momentum near the focus, although the on-axis orbital angular momentum remains zero. It is also shown that a linearly polarized optical vortex with topological charge 2 forms near the focal plane an on-axis reverse energy flow (defined by the negative longitudinal component of the <PERSON><PERSON><PERSON> vector) whose amplitude is comparable with the direct energy flow.</p>", "Keywords": "<PERSON><PERSON><PERSON> formulae; relation between spin angular momentum and orbital angular momentum; reverse energy flow; linear polarization.", "DOI": "10.18287/2412-6179-CO-582", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia; Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 2, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia; Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia; Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}], "References": []}, {"ArticleId": 80584424, "Title": "Methods for early recognition of OFDM data", "Abstract": "<p>A technique of early recognition (recovery) of data transmitted using OFDM technology by an incompletely received signal is considered. Theoretically, this approach is able to increase the speed of information transfer, as well as the resistance of the de-encoder to the loss of part of the transmitted signal. The article proposes a mathematical formulation of the OFDM signal early recognition problem, and also discusses several methods for solving it: a regularization method, an iterative method based on the fast Fourier transform, a gradient method based on learning, and an inverse operator method. The possibility of simultaneously using several methods to improve the accuracy of information recovery is considered. The results of numerical experiments presented in this work confirm the practical potential of the proposed approach.</p>", "Keywords": "OFDM; frequency modulation; signal recovery; early recognition; gradient descent; error-correcting codes.", "DOI": "10.18287/2412-6179-CO-662", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia, IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS, Molodogvardeyskaya 151, 443001, Samara, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia, IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS, Molodogvardeyskaya 151, 443001, Samara, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia, IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS, Molodogvardeyskaya 151, 443001, Samara, Russia"}], "References": []}, {"ArticleId": ********, "Title": "Modeling of image formation with a space-borne Offner hyperspectrometer", "Abstract": "<p>In this paper, we developed a mathematical model of image formation that allows a predictive hyperspectral image to be generated. The model takes into account the formation of an optical image using a matrix photodetector. The paper presents a numerical modeling of hyperspectral image formation and gives estimates of spatial and spectral resolution, as well as analyzing the adequacy of the results.</p>", "Keywords": "spaceborn hyperspectrometer; image formation; Offner scheme; photodetector; resolution; numerical simulation.", "DOI": "10.18287/2412-6179-CO-644", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 3, "Name": "N.L. Kazanskiy", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}], "References": []}, {"ArticleId": 80584428, "Title": "Focusing a second-order cylindrical vector beam with a gradient index Mikaelian lens", "Abstract": "<p>In this paper, we numerically simulate the focusing of a second-order cylindrical vector beam with a gradient index Mikaelian lens. It is shown that the lens forms a region of the reverse energy flow near its output surface. If the lens has an on-axis micropit, the region of the direct energy flow can be confined within the lens material, whereas that of the reverse energy flow is put out in free space.</p>", "Keywords": "Poynting vector; energy backflow; gradient index lens; cylindrical vector beam; scattering force.", "DOI": "10.18287/2412-6179-CO-633", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 2, "Name": "E.S. Kozlova", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}], "References": []}, {"ArticleId": 80584433, "Title": "An efficient algorithm for non-rigid object registration", "Abstract": "<p>An efficient algorithm for registration of two non-rigid objects based on geometrical transformation of the template object to target object is proposed. The transformation is considered as warping of the template onto the target. To choose the most suitable transformation from all possible warps, a registration algorithm should satisfy deformation constraints referred to as regularization of non-rigid objects. In this work, we use variational functionals for affine transformations. With the help of computer simulation, the proposed method for searching the optimal geometrical transformation is compared with that of common algorithms.</p>", "Keywords": "iterative closest points; nonrigid ICP; shape registration; affine transformation; orthogonal transformation; point-to-point; point-to-plane; deformable surfaces.", "DOI": "10.18287/2412-6179-CO-586", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Chelyabinsk State University, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, 129, 454001, Chelyabinsk, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Chelyabinsk State University, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, 129, 454001, Chelyabinsk, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Chelyabinsk State University, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, 129, 454001, Chelyabinsk, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Chelyabinsk State University, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, 129, 454001, Chelyabinsk, Russia"}], "References": []}, {"ArticleId": 80584435, "Title": "Deep learning application for box-office evaluation of images", "Abstract": "<p>The possibility of application a convolutional neural network to assess the box-office effect of digital images is reviewed. We studied various conditions for sample preparation, optimizer algorithms, the number of pixels in the samples, the size of the training sample, color schemes, compression quality, and other photometric parameters in view of effect on training the neural network. Due to the proposed preliminary data preparation, the optimum of the architecture and hyperparameters of the neural network we achieved a classification accuracy of at least 98%.</p>", "Keywords": "deep learning; neural networks; image analysis.", "DOI": "10.18287/2412-6179-CO-515", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Independent researcher"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Independent researcher"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Kovrov State Technological Academy named after <PERSON><PERSON><PERSON><PERSON>, Kovrov, Vladimir region, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "National Research Nuclear University “MEPhI”, Moscow, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Independent researcher"}], "References": []}, {"ArticleId": 80584436, "Title": "On a method for calculating generalized normal solutions of underdetermined linear systems", "Abstract": "<p>The article presents a novel algorithm for calculating generalized normal solutions of underdetermined systems of linear algebraic equations based on special extended systems. The advantage of this method is the ability to solve very poorly conditioned (possibly sparse) underdetermined linear systems of large dimension using modern versions of the iterative refinement method based on the generalized minimum residual method (GMRES - IT). Results of applying the considered algorithm to solve the problem of balancing chemical equations (mass balance) are presented.</p>", "Keywords": "underdetermined linear systems; generalized normal solution; augmented systems.", "DOI": "10.18287/2412-6179-CO-607", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara State Technical University, Samara, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara State Technical University, Samara, Russia"}], "References": []}, {"ArticleId": 80584439, "Title": "Mathematics and practice of color space invariants by the example of determining the gray balance for a digital printing system", "Abstract": "<p>In modern printing, a large number of tasks are associated with the mutual transformation of color spaces. In particular, the most common pair of hardware-dependent color spaces is RGB and CMYK, the mutual transformation of colors in which is ambiguous, which creates significant problems in color reproduction. To solve this problem, we propose using color space invariants — gradation trajectories and gradation surfaces, which are analogs of gradation curves for initial colorants and their binary overlays, constructed in the absolute color space of the CIE Lab. Invariants are introduced on the basis of the mathematical apparatus of the differential geometry of spatial curves and surfaces. Practical application of color space invariants involves certain difficulties associated with their complex analytical description; moreover, for most practical problems, the high accuracy of the model is redundant. For the practical application of invariants, we propose a simpler approach using natural color sampling in digital printing systems. As an example, the procedure for determining the gray balance for an electrophotographic printing press is given.</p>", "Keywords": "grey balance; gradation trajectories; gradation surfaces; digital printing.", "DOI": "10.18287/2412-6179-CO-580", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ural Federal University, Ekaterinburg, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Ural Federal University, Ekaterinburg, Russia"}], "References": []}, {"ArticleId": 80584441, "Title": "A method of generating a random optical field using the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> expansion to simulate atmospheric turbulence", "Abstract": "<p>It is proposed to use the random field generation in the numerical simulation of the propagation of radiation through a random medium using method based on the <PERSON><PERSON><PERSON><PERSON> expansion with various types of correlation operators to describe turbulence simulators. The properties of the calculated simulators of a random medium with a Gaussian correlation function were investigated in modeling the propagation of Laguerre-Gaussian vortex beams. The simulation results showed that an increase in the order of the optical vortex leads, as in the experiment, to lower stability of the phase singularity of the beams to random optical fluctuations. The similarity of the simulation results and the optical experiments indicates the promise of the proposed approach for the synthesis of random environment simulators.</p>", "Keywords": "correlation operator; eigenfunctions; Ka<PERSON>hun<PERSON>-<PERSON><PERSON> expansion; random optical medium simulator.", "DOI": "10.18287/2412-6179-CO-680", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "S.N<PERSON>", "Affiliation": "IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia"}, {"AuthorId": 2, "Name": "S.G. Volotovskiy", "Affiliation": "IPSI RAS – Branch of the FSRC “Crystallography and Photonics” RAS, Molodogvardeyskaya 151, 443001, Samara, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Samara National Research University, Moskovskoye Shosse 34, 443086, Samara, Russia"}], "References": []}, {"ArticleId": 80584443, "Title": "Limits of applicability of the direct ray approximation in modeling optical properties of liquid-crystal diffraction gratings", "Abstract": "<p>Using computer modeling, we estimate limits of applicability of the direct ray approximation in modeling the optical properties of liquid-crystal diffraction gratings with continuous spatial modulation of the local optic axis orientation in a liquid crystal layer. The data presented concerning the influence of the spatial frequency and character of modulation of the local optic axis, as well as the magnitude of birefringence of the medium, on the accuracy of the results obtained in this approximation are also useful in considering birefringent layers with an aperiodic variation of the local optic axis.</p>", "Keywords": "diffraction and gratings; optical devices; physical optics; birefringent diffraction gratings; direct ray approximation; modal grating method.", "DOI": "10.18287/2412-6179-CO-562", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Saratov State University, Saratov, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Saratov State University, Saratov, Russia"}], "References": []}, {"ArticleId": 80584447, "Title": "Person tracking algorithm based on convolutional neural network for indoor video surveillance", "Abstract": "<p>In this paper, a person tracking algorithm for indoor video surveillance is presented. The algorithm contains the following steps: person detection, person features formation, features similarity calculation for the detected objects, postprocessing, person indexing, and person visibility determination in the current frame. Convolutional Neural Network (CNN) YOLO v3 is used for person detection. Person features are formed based on H channel in HSV color space histograms and a modified CNN ResNet. The proposed architecture includes 29 convolutional and one fully connected layer. As the output, it forms a 128-feature vector for every input image. CNN model was trained to perform feature extraction. Experiments were conducted using MOT methodology on stable camera videos in indoor environment. Main characteristics of the presented algorithm are calculated and discussed, confirming its effectiveness in comparison with the current approaches for person tracking in an indoor environment. Our algorithm performs real time processing for object detection and tracking using CUDA technology and a graphics card NVIDIA GTX 1060.</p>", "Keywords": "person tracking; indoor video surveillance; convolutional neural networks.", "DOI": "10.18287/2412-6179-CO-565", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Polotsk State University, Polotsk, Belarus"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Polotsk State University, Polotsk, Belarus"}], "References": []}, {"ArticleId": 80584449, "Title": "Experiment with a diffractive lens with a fixed focus position at several given wavelengths", "Abstract": "<p>The paper presents results of the experimental investigation of “spectral” diffractive lenses the same focus position for several given wavelengths. Two spectral diffractive lenses designed to focus radiation of three and five specified wavelengths in the visible spectrum were investigated. Using a method of direct laser writing in photoresist with iterative correction of writing parameters, we fabricated a diffractive microrelief of the spectral lenses with the height deviation from the designed relief of less than 30 nm. Using a pinhole located at the focus of the fabricated lenses, we estimated the operation wavelengths. The point spread functions of the spectral lenses at the designed wavelengths were measured with the use of a tunable laser. The imaging properties of the spectral lenses were illustrated by the images of a reference color table.</p>", "Keywords": "spectral diffractive lens; harmonic lens; point spread function; focusing; photoresist direct laser recording method.", "DOI": "10.18287/2412-6179-CO-646", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 3, "Name": "S.<PERSON><PERSON>", "Affiliation": "PSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}, {"AuthorId": 6, "Name": "N.L. Kazanskiy", "Affiliation": "PSI RAS – Branch of the FSRC \"Crystallography and Photonics\" RAS, Molodogvardeyskaya 151, 443001, Samara, Russia, Samara National Research University, Moskovskoye shosse, 34, 443086, Samara, Russia"}], "References": []}, {"ArticleId": 80584450, "Title": "Highly reliable two-factor biometric authentication based on handwritten and voice passwords using flexible neural networks", "Abstract": "<p>The paper addresses a problem of highly reliable biometric authentication based on converters of secret biometric images into a long key or password, as well as their testing on relatively small samples (thousands of images). Static images are open, therefore with remote authentication they are of a limited trust. A process of calculating the biometric parameters of voice and handwritten passwords is described, a method for automatically generating a flexible hybrid network consisting of various types of neurons is proposed, and an absolutely stable algorithm for network learning using small samples of “Custom” (7-15 examples) is developed. A method of a trained hybrid \"biometrics-code\" converter based on knowledge extraction is proposed. Low values of FAR (false acceptance rate) are achieved.</p>", "Keywords": "hybrid networks; quadratic forms; Bayesian functionals; handwritten passwords; voice parameters; wide neural networks; biometrics-code converters; protected neural containers.", "DOI": "10.18287/2412-6179-CO-567", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Omsk State Technical University, Omsk, Russia"}], "References": []}, {"ArticleId": 80584452, "Title": "Threshold image target segmentation technology based on intelligent algorithms", "Abstract": "<p>This paper briefly introduces the optimal threshold calculation model and particle swarm optimization (PSO) algorithm for image segmentation and improves the PSO algorithm. Then the standard PSO algorithm and improved PSO algorithm were used in MATLAB software to make simulation analysis on image segmentation. The results show that the improved PSO algorithm converges faster and has higher fitness value; after the calculation of the two algorithms, it is found that the improved PSO algorithm is better in the subjective perspective, and the image obtained by the improved PSO segmentation has higher regional consistency and takes shorter time in the perspective of quantitative objective data. In conclusion, the improved PSO algorithm is effective in image segmentation.</p>", "Keywords": "particle swarm optimization; thresholding; image segmentation; relative basis.", "DOI": "10.18287/2412-6179-CO-630", "PubYear": 2020, "Volume": "44", "Issue": "1", "JournalId": 34923, "JournalTitle": "Computer Optics", "ISSN": "0134-2452", "EISSN": "2412-6179", "Authors": [{"AuthorId": 1, "Name": "Y.X. <PERSON>", "Affiliation": "Hengshui University, Hengshui, Hebei 053000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hengshui University, Hengshui, Hebei 053000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Hengshui University, Hengshui, Hebei 053000, China"}, {"AuthorId": 4, "Name": "D.D. Li", "Affiliation": "Hengshui University, Hengshui, Hebei 053000, China"}], "References": []}, {"ArticleId": 80584735, "Title": "Thermal and optical behavior dataset of surfaces coated with high reflectance and common materials under different conditions, used in Brazil", "Abstract": "<p>External and internal surface temperatures values of coated steel plates and the interior air temperature of a box-like model were collected throughout the time under different conditions: an artificial infrared radiation source was placed facing the external side of the plate to promote the temperature change, the energy power was variated by changing the distance from the plate and simultaneously different airflow conditions were inputted, data was collected by making experiments using the combination of distance and airflow changes. Reflectance raw data, of the materials, was also acquired by measuring the reflectance index of the samples according to ASTM E903 &quot;Standard Test Method for Solar Absorptance, Reflectance, and Transmittance of Materials Using Integrating Spheres&quot;. Amplified optical images and infrared images of the samples were taken (using a &quot;Flir e4&quot; infrared camera) while they were under the influence of the source in the three chosen distances (60 cm, 40 cm and 20 cm). Considering that researches at this particular field lack of more direct comparisons of temperature, reflectance and heat flux behaviours; between special high reflectance coatings and common ones when the substrate is steel, when the direction of airflow is changed and when the source is an infrared one; measurements intending to give faster insights and directions were done. These insights can be used by anyone who intends to develop new coating materials and compare them with already existing ones to streamline their researches in this area. Finally, the buying costs of these coatings, similar to commercially used ones, are showed.</p><p>© 2020 The Author(s).</p>", "Keywords": "Temperature data ; Temperature profiles ; Coatings thermal behavior ; Surface reflectance index ; Surface behavior ; Infrared images ; Heat flux behavior", "DOI": "10.1016/j.dib.2020.105445", "PubYear": 2020, "Volume": "30", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> e <PERSON>", "Affiliation": "Materials Engineering Department, Federal Technological Education Centre of Minas Gerais (Cefet/MG, Brazil)."}, {"AuthorId": 2, "Name": "Claudinei Rezende Calado", "Affiliation": "Chemistry Department, Federal Technological Education Centre of Minas Gerais (Cefet/MG), Brazil."}], "References": []}, {"ArticleId": 80584741, "Title": "Thank you note", "Abstract": "", "Keywords": "", "DOI": "10.1080/13600869.2020.1741636", "PubYear": 2020, "Volume": "34", "Issue": "2", "JournalId": 25543, "JournalTitle": "International Review of Law, Computers & Technology", "ISSN": "1360-0869", "EISSN": "1364-6885", "Authors": [], "References": []}, {"ArticleId": 80584879, "Title": "<PERSON> Chen-G Family of Distributions", "Abstract": "<p>Classical distributions do not always provide reasonable fit to all forms of datasets, hence the need to generalize existing distributions to enhance their flexibility in modeling of data. The study developed the odd Chen-G family of distributions. It derives the statistical properties of the new family such as the quantile, moments, and order statistics. Though capable of generalizing other distributions, the study proposed three special distributions; odd <PERSON>, odd <PERSON> and odd <PERSON> distributions. The application of the new family is then demonstrated using real data.</p>", "Keywords": "Odd; Chen; Lomax; Statistical distribution; Quantile; 62E15; 60E05", "DOI": "10.1007/s40745-020-00248-2", "PubYear": 2022, "Volume": "9", "Issue": "2", "JournalId": 6800, "JournalTitle": "Annals of Data Science", "ISSN": "2198-5804", "EISSN": "2198-5812", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Statistics, Faculty of Mathematical Sciences, University for Development Studies, Tamale, Ghana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Statistics, Faculty of Mathematical Sciences, University for Development Studies, Tamale, Ghana"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Statistics, Faculty of Mathematical Sciences, University for Development Studies, Tamale, Ghana"}], "References": [{"Title": "The Zubair-G Family of Distributions: Properties and Applications", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "7", "Issue": "2", "Page": "195", "JournalTitle": "Annals of Data Science"}]}, {"ArticleId": 80585030, "Title": "One-class support vector classifiers: A survey", "Abstract": "Over the past two decades, one-class classification (OCC) becomes very popular due to its diversified applicability in data mining and pattern recognition problems. Concerning to OCC, one-class support vector classifiers (OCSVCs) have been extensively studied and improved for the technology-driven applications; still, there is no comprehensive literature available to guide researchers for future exploration. This survey paper presents an up to date, structured and well-organized review on one-class support vector classifiers. This survey comprises available algorithms, parameter estimation techniques, feature selection strategies, sample reduction methodologies, workability in distributed environment and application domains related to OCSVCs. In this way, this paper offers a detailed overview to researchers looking for the state-of-the-art in this area.", "Keywords": "One-class classification (OCC) ; One-class support vector classifiers (OCSVCs) ; Parameter estimation ; Feature selection ; Sample reduction ; Distributed environment", "DOI": "10.1016/j.knosys.2020.105754", "PubYear": 2020, "Volume": "196", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, Prayagraj, 211015, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, Prayagraj, 211015, India;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, Prayagraj, 211015, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Indian Institute of Information Technology Allahabad, Prayagraj, 211015, India"}], "References": [{"Title": "Class-imbalanced dynamic financial distress prediction based on Adaboost-SVM ensemble combined with SMOTE and time weighting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "128", "JournalTitle": "Information Fusion"}, {"Title": "Robust AdaBoost based ensemble of one-class support vector machines", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "55", "Issue": "", "Page": "45", "JournalTitle": "Information Fusion"}, {"Title": "Sample reduction using farthest boundary point estimation (FBPE) for support vector data description (SVDD)", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "268", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A reduced universum twin support vector machine for class imbalance learning", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "102", "Issue": "", "Page": "107150", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 80585189, "Title": "A Gaze-Contingent System for Foveated Multiresolution Visualization of Vector and Volumetric Data", "Abstract": "Computational complexity is a limiting factor for visualizing large-scale scientific data. Most approaches to render large datasets are focused on novel algorithms that leverage cutting-edge graphics hardware to provide users with an interactive experience. In this paper, we alternatively\n demonstrate foveated imaging which allows interactive exploration using low-cost hardware by tracking the gaze of a participant to drive the rendering quality of an image. Foveated imaging exploits the fact that the spatial resolution of the human visual system decreases dramatically away\n from the central point of gaze, allowing computational resources to be reserved for areas of importance. We demonstrate this approach using face tracking to identify the gaze point of the participant for both vector and volumetric datasets and evaluate our results by comparing against traditional\n techniques. In our evaluation, we found a significant increase in computational performance using our foveated imaging approach while maintaining high image quality in regions of visual attention.", "Keywords": "Gaze ; Visualization ; Volume Rendering ; vector visualization", "DOI": "10.2352/ISSN.2470-1173.2020.1.VDA-374", "PubYear": 2020, "Volume": "32", "Issue": "1", "JournalId": 34798, "JournalTitle": "Electronic Imaging", "ISSN": "2470-1173", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Thanawut <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 4, "Name": "Al<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 80585219, "Title": "Detection and removal of infrequent behavior from event streams of business processes", "Abstract": "Process mining aims at gaining insights into business processes by analyzing the event data that is generated and recorded during process execution. The vast majority of existing process mining techniques works offline, i.e. using static, historical data, stored in event logs. Recently, the notion of online process mining has emerged, in which techniques are applied on live event streams, i.e. as the process executions unfold. Analyzing event streams allows us to gain instant insights into business processes. However, most online process mining techniques assume the input stream to be completely free of noise and other anomalous behavior. Hence, applying these techniques to real data leads to results of inferior quality. In this paper, we propose an event processor that enables us to filter out infrequent behavior from live event streams. Our experiments show that we are able to effectively filter out events from the input stream and, as such, improve online process mining results.", "Keywords": "Process mining ; Event streams ; Filtering ; Outlier detection ; Anomaly detection", "DOI": "10.1016/j.is.2019.101451", "PubYear": 2020, "Volume": "90", "Issue": "", "JournalId": 947, "JournalTitle": "Information Systems", "ISSN": "0306-4379", "EISSN": "1873-6076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Fraunhofer Institute for Applied Information Technology, Sankt Augustin, Germany;RWTH Aachen University, Aachen, Germany;Corresponding author at: Fraunhofer Institute for Applied Information Technology, Sankt Augustin, Germany"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "RWTH Aachen University, Aachen, Germany"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Melbourne, Melbourne, Australia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "University of Melbourne, Melbourne, Australia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "University of Melbourne, Melbourne, Australia"}], "References": []}, {"ArticleId": 80585263, "Title": "Memetic algorithm-based path generation for multiple Dubins vehicles performing remote tasks", "Abstract": "This paper formalises path planning problem for a group of heterogeneous Dubins vehicles performing tasks in a remote fashion and develops a memetic algorithm-based method to effectively produce the paths. In the setting, the vehicles are initially located at multiple depots in a two-dimensional space and the objective of planning is to minimise a weighted sum of the total tour cost of the group and the largest individual tour cost amongst the vehicles. While the presented formulation takes the form of a mixed-integer linear programme (MILP) for which off-the-shelf solvers are available, the MILP solver easily loses the tractability as the number of tasks and agents grow. Therefore, a memetic algorithm tailored to the presented formulation is proposed. The algorithm features a sophisticated encoding scheme to efficiently select the order of performing tasks. In addition, a path refinement technique that optimises detailed tours with the sequence of visits fixed is proposed to finally obtain further optimised trajectories. Comparative numerical experiments show the validity and efficiency of the proposed methods compared with the previous methods in the literature.", "Keywords": "Path planning ; travelling salesman problem ; remote surveillance ; memetic algorithm ; multi-agent systems ; evolutionary computation ; robotics", "DOI": "10.1080/00207721.2020.1737263", "PubYear": 2020, "Volume": "51", "Issue": "4", "JournalId": 2681, "JournalTitle": "International Journal of Systems Science", "ISSN": "0020-7721", "EISSN": "1464-5319", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, KAIST, Daejeon, Republic of Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Aerospace and Mechanical Engineering, Korea Aerospace University, Goyang, Republic of Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Aerospace Engineering, KAIST, Daejeon, Republic of Korea"}], "References": []}, {"ArticleId": 80585892, "Title": "Flexible Discrete Multi-view Hashing with Collective Latent Feature Learning", "Abstract": "<p>Multi-view hashing has gained considerable research attention in efficient multimedia studies due to its promising performance on heterogeneous data from various sources. However, its application in discriminative hash codes learning remains challenging as it fails to efficiently capture preferable components from multiple representations. In this work, we propose a novel discriminative multi-view hashing framework, dubbed flexible discrete multi-view hashing, in conjunction with collective latent feature learning by combining multiple views of data and consistent hash codes learning by fusing visual features and flexible semantics. Specifically, an adaptive multi-view analysis dictionary learning model is developed to skillfully combine diverse representations into an established common latent feature space where the complementary properties of different views are well explored based on an automatic multi-view weighting strategy. Moreover, we introduce a collaborative learning scheme to jointly encode the visual and semantic embeddings into an aligned consistent Hamming space, which can effectively mitigate the visual-semantic gap. Particularly, we employ the correntropy induced regularization to improve the robustness of the formulated flexible semantics. An efficient learning algorithm is proposed to solve the optimization problem. Extensive experiments show the state-of-art performance of the proposed method on several benchmark datasets.</p>", "Keywords": "", "DOI": "10.1007/s11063-020-10221-y", "PubYear": 2020, "Volume": "52", "Issue": "3", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information Technology and Electrical Engineering, University of Queensland, Brisbane, Australia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Bio-Computing Research Center, Harbin Institute of Technology, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Technology and Electrical Engineering, University of Queensland, Brisbane, Australia"}], "References": [{"Title": "Spectral rotation for deep one-step clustering", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107175", "JournalTitle": "Pattern Recognition"}, {"Title": "Robust SVM with adaptive graph learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23", "Issue": "3", "Page": "1945", "JournalTitle": "World Wide Web"}]}, {"ArticleId": 80585992, "Title": "Research on In-Situ Measurement Simulation Test of Inertia of Flywheel Simulator by SimMechanics", "Abstract": "Inertial mass simulation system is widely used in inertia load simulation of space mechanisms. The error of the inertia mass has a great influence on the mechanism performance test. In this paper, the in-situ measurement technology of flywheel inertial mass is studied: firstly, the theory of in-situ inertial mass measurement is analyzed by use of mass-spring model; secondly, the simulation models of the flywheel before or after attaching counterweights are established based on Sim-Mechanics of MATLAB; at last, according to the simulation test and analysis of the model, the correctness of the theoretical derivation of in-situ measurement is verified, which lays a theoretical foundation for in-situ inertial mass measurement of the flywheel.", "Keywords": "In-situ measurement of MOI ; Simulation test ; MATLAB ; SimMechanics ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.008", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Aerospace System Engineering Shanghai, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shanghai precision measuring and Testing Institute, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Aerospace System Engineering Shanghai, Shanghai, China"}, {"AuthorId": 4, "Name": "Hai <PERSON>", "Affiliation": "Aerospace System Engineering Shanghai, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Aerospace System Engineering Shanghai, Shanghai, China"}], "References": []}, {"ArticleId": 80585994, "Title": "Research on Clustering Method Based on Weighted Distance Density and K-Means", "Abstract": "In this paper, the effect of the initial clustering center selection on the performance of the K-means algorithm is studied, and the performance of the algorithm is enhanced through better initialization techniques. In the K-means clustering process, when calculating the density of a data set by using a weighted distance density calculation method, significant improvement in the defects of poor clustering results caused by the local optimum and large intra-cluster variance in the traditional K-means clustering algorithm has been found. Experimental results show that by using the improved method proposed in this paper, the intra-cluster variance of clustering results is reduced by 15.5% compared with the traditional method, which makes great improvement in the performance of the algorithm.", "Keywords": "K-means algorithm ; density calculation ; weighted distance ; cluster centroid ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.056", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China;@qq.com"}, {"AuthorId": 3, "Name": "Lihua Ma", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China"}, {"AuthorId": 4, "Name": "Huifang Sun", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China"}], "References": []}, {"ArticleId": ********, "Title": "A Wireless Mesh Multipath Routing Protocol Based on Sorting Ant Colony Algorithm", "Abstract": "In this paper, the traditional ant colony algorithm has a slow convergence rate for routing optimization of Mesh networks. A multi-path routing protocol based on improved ant colony algorithm, Fortified Ant protocol, is proposed. The protocol first adds a sorting algorithm based on the ant colony algorithm, and introduces the concept of elite ants to improve the speed of routing optimization. Secondly, this paper also studies the multipath transmission of self-organizing networks. The simulation results show that compared with ADOV, DSR and AOC routing algorithms, the algorithm can quickly find multiple paths with better quality, with fast convergence and overhead. Less advantage.", "Keywords": "Wireless Mesh network ; Ant colony algorithm ; Sorting algorithm ; Multipath routing ; Fortified Ant ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.018", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China"}], "References": []}, {"ArticleId": ********, "Title": "Force Feedback Based on Magnetorheological Fluid", "Abstract": "Surgical robots stand out from medical surgery with the advantages of precise surgery and small incision, but it is difficult for doctors to perform precise control operations on the surgical site only through vision and hearing. Therefore, the application of force feedback in medical operation has become the focus of research in recent years. At present, the problem of force feedback is that the force in the changing collection is not accurate enough. The output of the force feedback structure is unstable and the volume is too large. This paper discusses the feasibility of using magnetorheological fluid (MRF) to realize force feedback, and designs the device structure of force feedback. A solution is proposed for the precise collection of mechanical arm force, and the mean algorithm is used to realize the de-jittered process and improve the collection precision. The performance is improved by more than four times, and the simulation is performed to verify the feasibility.", "Keywords": "Force feedback ; magnetorheological fluid ; accurate collection of the force ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.005", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Demonstration Center for Experimental Electrical and electronic technology, Chanchun University of Science and Techonlgy, ChangChun, JiLin"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "National Demonstration Center for Experimental Electrical and electronic technology, Chanchun University of Science and Techonlgy, ChangChun, JiLin"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Demonstration Center for Experimental Electrical and electronic technology, Chanchun University of Science and Techonlgy, ChangChun, JiLin"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National Demonstration Center for Experimental Electrical and electronic technology, Chanchun University of Science and Techonlgy, ChangChun, JiLin"}], "References": []}, {"ArticleId": 80586008, "Title": "Research on the Features of Car Insurance Data Based on Machine Learning", "Abstract": "With the continuous development of machine learning, enterprises using machine learning methods to mine potential data information has become a hot topic in the research of major insurance companies. In this paper, the features of auto insurance data are analyzed, and the most important features affecting auto renewal are mined. The random forest (RF), gradient lifting tree (GBDT) and lifting machine algorithm (LightGBM) are compared. The test results show that: LightGBM model with the best superiority and robustness. Features of car insurance business channel, NCD, car age and new car purchase price have a greater impact on whether to renew insurance or not.", "Keywords": "Car insurance ; Feature engineering ; LightGBM ; Data analysis ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.016", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, China;@qq.com"}], "References": []}, {"ArticleId": 80586009, "Title": "Characteristic Analysis of Frequency Scanning Feed Network", "Abstract": "In this paper, the S-parameter method of microwave network is used to analyze the frequency scanning feeding network. Combining the grating lobe theory of antenna array, the conditions of antenna array integration with leaky wave function is analyzed. The analysis provides theoretical guidance for the design of leaky wave functional antenna arrays with high efficiency.", "Keywords": "frequency scanning ; feed network ; analysis ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.062", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Air and Missile-Defence College, Air Force Engineering University, Xi’an 710051, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Air and Missile-Defence College, Air Force Engineering University, Xi’an 710051, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Air Force Engineering University, Xi’an 710051, China"}], "References": []}, {"ArticleId": 80586010, "Title": "Simulation Analysis of the Effect of Turning Radius on the Running Safety of Piggyback Transportation Vehicle System", "Abstract": "In this paper, the dynamic simulation software Simpack is used to model and simulate the piggyback transport vehicle system. The running state of the piggyback transport vehicle system under different track radius conditions is studied. The minimum turning radius under the maximum operating speed of the piggyback transport vehicle system is obtained, which provides a basis for the safe operation of the piggyback transport.", "Keywords": "turning radius ; Simpack ; piggyback transportation ; simulation analysis ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.013", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON> <PERSON>", "Affiliation": "Key Laboratory of operation safety technology on transport vehicle. Beijing 100088, China;School of Mechanical Engineering, University of Jinan, Jinan 250022, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of operation safety technology on transport vehicle. Beijing 100088, China"}, {"AuthorId": 3, "Name": "Li Fa Jia", "Affiliation": "Key Laboratory of operation safety technology on transport vehicle. Beijing 100088, China;School of Mechanical Engineering, University of Jinan, Jinan 250022, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Mechanical Engineering, University of Jinan, Jinan 250022, China"}], "References": []}, {"ArticleId": 80586011, "Title": "Design of High Speed Data Acquisition System for Linear Array CCD Based on FPGA", "Abstract": "Linear array CCD has been widely used in non-contact photoelectric detection system, and the design of its driving circuit is one of the key technologies of the detection system. A high-speed data acquisition system of linear array CCD based on FPGA as the main controller and FT245RL as the USB interface chip is proposed. Firstly, based on the timing analysis of linear array CCD, using Verilog Hardware Description Language, the interface timing logic control circuit of linear array CCD driver, ADC and USB is designed. The functions of collecting 3648 pixels at a rate of 10 frames per second and uploading transient spectral information to PC for storage and display through USB2.0 interface are realized. It can provide a technical reference for CCD data acquisition system with similar requirements.", "Keywords": "FPGA ; USB ; Linear array CCD ; Photoelectric detection ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.073", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Xinyu University, Xinyu 338024, China"}], "References": []}, {"ArticleId": 80586014, "Title": "Developing an Artificial Intelligence (AI) Management System to Improve Product Quality and Production Efficiency in Furniture Manufacture", "Abstract": "At present, there are some problems in Chinese furniture production industry, such as low production efficiency, low accuracy, and lack of innovation for products. To resolve those problems, an AI management system is developed to improve the product quality and production efficiency in furniture enterprises in this paper. The AI management system is an organic body consisted of a data management system and an expert system. The model of information transmission and control for furniture manufacture by AI management is developed. It provides technical solutions for the AI application in furniture manufacture.", "Keywords": "artificial intelligence (AI) ; management ; Furniture ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.060", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Resources, Environment and Materials, Guangxi University, 530004, Nanning city, Guangxi, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Resources, Environment and Materials, Guangxi University, 530004, Nanning city, Guangxi, China"}, {"AuthorId": 3, "Name": "Hong Xing Cai", "Affiliation": "School of Resources, Environment and Materials, Guangxi University, 530004, Nanning city, Guangxi, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Resources, Environment and Materials, Guangxi University, 530004, Nanning city, Guangxi, China"}], "References": []}, {"ArticleId": 80586015, "Title": "Practical Analysis of Application of Electronic Diagnosis Technology in New Energy Vehicle Maintenance", "Abstract": "With the rapid development of social and economic level, the improvement of science and technology has brought great changes to all walks of life. In the automobile manufacturing industry, the new energy vehicle is a key development project, which has a good prospect. It is precisely because of this background that the corresponding maintenance work of new energy vehicles also needs to be upgraded to a certain extent. This paper first expounds the value of electronic diagnosis technology in the maintenance of new energy vehicles, then analyses the matters needing attention in the application of electronic diagnosis technology in the maintenance of new energy vehicles, and finally, from a reasonable and legitimate point of view, puts forward some suggestions for the application of electronic diagnosis technology in the maintenance of new energy vehicles.", "Keywords": "electronic diagnostic technology ; new energy vehicle ; application ; practical analysis ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.085", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong University of Science & Technology, Dongguan, 523083, China;@qq.com"}], "References": []}, {"ArticleId": 80586016, "Title": "Design and Realization of Highly Reliable Logic Control Software for Pre-Launch Escape Command and Control System", "Abstract": "In order to meet the sudden danger of manned launch vehicle on the ground, emergency escape control design in the pre-launching stage should ensure high reliability and high safety. Based on programmable logic control combination (PLC), a logic control software design was proposed. By constructing ‘data input module’, ‘logic control module’ and ‘function module’, emergency escape commands could be transmitted accurately; at the same time, combined with redundancy design, filtering design and other reliability design measures, the goal of occupying less system resources and high efficiency has been realized, which could guarantee that astronauts escape quickly and reliably at the first time of danger.", "Keywords": "PLC control software ; Pre-launch escape ; High reliability ; Manned space ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.101", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Astronautical Systems Engineering, Fengtai, Beijing, China;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Astronautical Systems Engineering, Fengtai, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Beijing Institute of Astronautical Systems Engineering, Fengtai, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Astronautical Systems Engineering, Fengtai, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Institute of Astronautical Systems Engineering, Fengtai, Beijing, China"}], "References": []}, {"ArticleId": 80586028, "Title": "Research on School Intelligent Classroom Management System Based on Internet of Things", "Abstract": "With the increase in the number and size of school campuses, the issue of campus energy waste has received extensive attention. In order to monitor the environment of the classroom, control the electrical appliances to reduce energy consumption, and analyze the environment and utilization of the classroom, this paper develops an intelligent classroom management system based on the Internet of Things. In this system, the sensor suite of Shanghai Qixiang Technology Co., Ltd. is used as the basic suite of the Internet of Things. The storage model is established by the relational database MySQL and the non-relational database HBase. And the B/S-based website is the user interface. The storage overhead is reduced to a certain extent, and the energy waste in the classroom is effectively reduced.", "Keywords": "Smart Classroom ; Internet of Things ; Distributed Storage ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.037", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Donghua University, Shanghai 201620, China;@mail.dhu.edu.cn"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Shanghai Jianqiao University, Shanghai 201306, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Jianqiao University, Shanghai 201306, China"}], "References": []}, {"ArticleId": 80586030, "Title": "A Survey of Symbolic Execution and Its Tool KLEE", "Abstract": "Symbolic execution is a highly practical program analysis technology. With the gradual deepening of its research and the continuous maturity of technology itself, it has been widely used in software testing and other fields. KLEE is a symbolic execution tool built on the LLVM compilation framework that automatically generates test cases for high coverage of complex and environmentally intensive programs. This article comprehensively describes the basic principle, development situation, current issues of symbolic execution, and KLEE’s infrastructure as well as the generation of test cases. To some extent, it highlights the unique advantages of symbolic execution in software testing and analysis.", "Keywords": "Symbolic Execution ; KLEE ; Test Cases ; Path Explosion ; Constraint Solving ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.090", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, College of Informatics, Huazhong Agriculture University, Wuhan, Hubei, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Power Engineering, Wuhan Electric Power Technical College, Wuhan, Hubei, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, College of Informatics, Huazhong Agriculture University, Wuhan, Hubei, China"}], "References": []}, {"ArticleId": 80586032, "Title": "The Embedded Modules Solution of Household Internet of Things System and The Future Development", "Abstract": "By analyzing the current situation of the Internet of Things, this paper realized that the household Internet of Things encounters many difficulties in many areas like electric appliances producing, consumers using, running and maintenance, control security. So this paper comes up with The Embedded Modules Solution of Household Internet of Things System, which is a household Internet of Things that allows all electric appliances been controlled by a single smartphone terminal. This system is able to be integrated, expanded, and optimized. And this paper comes up with the three stages of IoT in future which are “Internet of things with electrical appliances”, “Internet of things with any appliance”, and “Artificial Intelligence Internet of things”", "Keywords": "Internet of Things ; Embedded Modules Solution ; Difficulties ; Future Development ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.086", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "No.24, <PERSON> huan lu nan yi duan, Wuhou district, Chengdu City, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "University of Sichuan, China"}], "References": []}, {"ArticleId": 80586034, "Title": "Intelligent Tracking Obstacle Avoidance Wheel Robot Based on Arduino", "Abstract": "Combining the current development status of intelligent robots, obstacle avoidance and automatic tracking are the focus of robot travel problems.Based on the development status of domestic intelligent control systems, current technology, etc, this paper uses arduino as the core control system, combined with infrared tracking module. Four modules, such as ultrasonic obstacle avoidance module, motor drive module and power module, have designed a good control scheme, thus realizing the intelligent tracking and obstacle avoidance function of the wheeled robot.", "Keywords": "Wheel robot ; arduino ; tracking ; obstacle avoidance ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.100", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Kunming University of Science & Technology, Kunming 650051, China;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Kunming University of Science & Technology, Kunming 650051, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Xidian University, Xi’an 710000, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Kunming University of Science & Technology, Kunming 650051, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Kunming University of Science & Technology, Kunming 650051, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Kunming University of Science & Technology, Kunming 650051, China"}], "References": []}, {"ArticleId": 80586035, "Title": "Vibration Test on Welding Robot", "Abstract": "Taking the welding robot as a research object, stochastic noise test and linear frequency sweeping test are carried out on the welding gun of a welding robot. In the tests above, tri-axial vibration and exciting force are picked up by an acceleration sensor and a force sensor. The force sensor collects input force and the acceleration sensor collects vibration response. The dynamic performance of the robot is assessed by estimating force-displacement frequency response function. The weak links of the robot can be reflected through analyzing low order natural frequency distribution. This study shows that the low-frequency components of the welding robot structure are easily explored when the displacement response is measured, and experimental da-ta will supply a clue for optimization of robot structure design.", "Keywords": "Welding Robot ; Vibration Test ; Frequency-response estimation ; Modal Analysis ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.091", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "Li Hong", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, 200444, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, 200444, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Mechatronics Engineering and Automation, Shanghai University, Shanghai, 200444, China"}], "References": []}, {"ArticleId": 80586043, "Title": "Research on MVP Design Pattern Modeling Based on MDA", "Abstract": "In recent years, mobile devices (such as various brands of mobile phones, tablet computers, smart watches, etc.) have gained popularity among users, which has also promoted the rapid development of mobile software application market. In order to meet the needs of mobile device users for better user interaction experience, the demand of mobile software application market for user interface is becoming more and more complex, and software developers are constantly exploring and developing more popular user interface. Due to the shortage of production technology, the development of mobile user interface has some problems such as low efficiency and high cost. In order to solve these problems, this paper proposes a method of modeling PIM using IFML and UML by introducing MVP design pattern in a model-driven framework. By combining the advantages of model-driven and MVP design pattern, this method improves the granularity of PIM modeling in user interface development under model-driven framework, reduces the difficulty of model design, and promotes software reuse.", "Keywords": "IFML ; MVP design pattern ; MDA ; user interface development ; PIM ; modeling ; UML ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.012", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650000, Yunnan, China;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650000, Yunnan, China"}], "References": []}, {"ArticleId": 80586045, "Title": "Evaluating the Quality of GANs Based on F1 Harmonic Mean", "Abstract": "In this article, we discuss several methods to evaluate the quality of GANs, and briefly explain the internal mechanism they used and their shortcomings and promote our method.", "Keywords": "GAN ; neural networks ; assessment ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.064", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "CAS Key Lab of Space Manufacturing Technology;University of Chinese Academy of Sciences, Beijing, 100049, China;Technology and engineering center for space utilization, Chinese Academy of Sciences, Beijing 100094, China;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "CAS Key Lab of Space Manufacturing Technology;University of Chinese Academy of Sciences, Beijing, 100049, China;Technology and engineering center for space utilization, Chinese Academy of Sciences, Beijing 100094, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "CAS Key Lab of Space Manufacturing Technology;Technology and engineering center for space utilization, Chinese Academy of Sciences, Beijing 100094, China"}], "References": []}, {"ArticleId": 80586046, "Title": "Research and Development of Movie Social System", "Abstract": "In order to explore the inheritance and promotion of movie culture, this paper designs a web system with B/S architecture. With the separation of <PERSON> Layer, <PERSON> Layer and Controller Layer, the system has three modules, including topic area, recording area and recommendation area. The web development and research implement the communication and construction of movie community culture, which is of great significance for the understanding of the development tendency of social network. The users’ data of the system is helpful for the exploration of their network behaviors and personality characteristics, which can expand their spiritual world and reduce the pressure of life.", "Keywords": "Movie Culture ; B/S Architecture ; Social Networks ; Web Development ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.039", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Ocean University, Zhanjiang City, Guangdong Province, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Ocean University, Zhanjiang City, Guangdong Province, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Ocean University, Zhanjiang City, Guangdong Province, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Guangdong Ocean University, Zhanjiang City, Guangdong Province, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Guangdong Ocean University, Zhanjiang City, Guangdong Province, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Ocean University, Zhanjiang City, Guangdong Province, China"}], "References": []}, {"ArticleId": 80586047, "Title": "Design of Detecting Harmful Gas Inside the Vehicle and Voice Alarm System and Concentration Prediction", "Abstract": "A modular design including STM32F107 microcontroller, data acquisition, liquid crystal display and voice alarm is used to give a set of harmful gas detection voice in the vehicle. alarm system. In the data acquisition, each sensor measures the carbon monoxide concentration, formaldehyde concentration, PM 2.5 and temperature and humidity parameters in the vehicle and displays them on the TFT liquid crystal display in real time. The comparison between the system display value and the measured value shows that the maximum absolute error of the measured value of the system is 0.004 mg/m3. The GM(1,1) model is used to predict the concentration of harmful gases in the vehicle, and the accuracy of the system model is verified by the small error probability and the posterior difference ratio. The results show that the system is stable and reliable, and the cost is low. An accident in which the concentration of harmful gases is too high to endanger the safety of passengers.", "Keywords": "STM32 microcontroller ; median average filtering algorithm ; PM2.5 ; GM(1,1) ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.048", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Air Transportation, Shanghai University of Engineering Science, Shanghai 201620, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Air Transportation, Shanghai University of Engineering Science, Shanghai 201620, China"}, {"AuthorId": 3, "Name": "Yangyang Qi", "Affiliation": "School of Air Transportation, Shanghai University of Engineering Science, Shanghai 201620, China"}, {"AuthorId": 4, "Name": "<PERSON>u", "Affiliation": "School of Air Transportation, Shanghai University of Engineering Science, Shanghai 201620, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Air Transportation, Shanghai University of Engineering Science, Shanghai 201620, China"}], "References": []}, {"ArticleId": 80586048, "Title": "A Simple and Practical Embedded Software System Architecture", "Abstract": "For a long time, the embedded industry has been constrained by CPU performance, ROM and RAM capacity and other factors, so the software scale cannot be built too large. The new embedded designers often overturn the original version because they cannot comprehend the previous code. The lack of software framework standard leads to redundant and useless work. By making the analogy to the hierarchical idea of Android architecture, this paper proposes a simple and practical software architecture suitable for the embedded industry. The architecture adopts hierarchical design and each module operates independently, thus realizes high cohesion and low coupling of the system. The C structure variable is used to simulate the properties of the class, and the function pointer is used to replace the function in the class. By doing this, embedded programming changes from process-oriented to object-oriented. In addition, the architecture provides a registration mechanism to solve the upload of high-speed, real-time changing data.", "Keywords": "Embedded software system architecture ; Layered thought ; Nested encapsulation of structures ; Registration mechanism ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.021", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, P.R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen RainDi Technology Co., Ltd., Shenzhen 518071, P.R. China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, P.R. China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, P.R. China;College of Engineering & Science, Florida Institute of Technology, 150 W. University Blvd. Melbourne, FL 32901, U.S.A"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering and Automation, Kunming University of Science and Technology, Kunming 650500, P.R. China"}], "References": []}, {"ArticleId": 80586050, "Title": "Practice Exploration of Flipping Classroom in Table Tennis Club in the Informatization Age", "Abstract": "With the development of educational informatization, especially the deep integration of information technology and curriculum, the educational concept of flipping classroom has become the main trend of education reform. Flipping classroom is a new way of teaching. By transforming knowledge to the class, more classroom time is released for internalizing knowledge. It completely broke the original \"teacher teaching, student learning\" pattern, and truly realized the \"student-centered\" teaching concept. Therefore, we combine the flipping classroom with the table tennis club course, focusing on the implementation methods and the final results of the flipping classroom in the teaching of the table tennis club. Through a large number of teaching practices, we found that by using flipping classroom, the student-centered teaching concept has been fully reflected, and students’ ability to find, analyze and solve problems has been improved.", "Keywords": "Flipping Classroom ; Table Tennis Club ; Practice Exploration ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.044", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Humanities and Art, Yunnan College of Business Management"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Sports Department, SuZhou Vocational University"}], "References": []}, {"ArticleId": 80586060, "Title": "A Method of Temperature and Humidity Control for Air Material Depot", "Abstract": "Temperature and humidity are two important factors affecting the quality of aviation equipment in the air material depot. Controlling them within a certain range is conducive to the safety management of air material. It is of great significance to guarantee the supply of air material, implement the storage of air material and improve the efficiency of equipment. At present, the great majority of temperature and moisture controller employ the traditional proportional-integral-differential control principle, which is difficult to obtain higher control accuracy and better control quality. In the article, humanoid intelligent control algorithm is introduced into temperature and humidity control. Practice proves that good results have been achieved.", "Keywords": "temperature ; moisture control ; humanoid intelligent control ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.078", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Army Aviation Institute of PLA, Tongzhou District, Beijing, 101123, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Army Aviation Institute of PLA, Tongzhou District, Beijing, 101123, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Army Aviation Institute of PLA, Tongzhou District, Beijing, 101123, China"}], "References": []}, {"ArticleId": 80586065, "Title": "Design and Implementation of Automatic Invigilation Functions Using the Embedded Technology", "Abstract": "This paper designs and implements the automatic invigilation functions using the embedded technology. It proposes a framework for automatic invigilation, which conducts the invigilation functions of the entire examination process. In the examination preparation stage, the framework collects the registration details of examinees and verifies the details with the database through the remote server. During the examination ongoing stage, it keeps checking the consistence between each examinee and his examination materials by photographing the examinee and scanning the QR codes on examination papers, sketch papers, and answer sheets. In the examination ending stage, it checks the consistence of an examination bag and the materials being put into it by scanning and verifying the QR codes on them. The framework reduces the human workload by using automatic functions to replace the human work. The tests demonstrates that the framework can perform the designed functions.", "Keywords": "Invigilation Event Detection ; Embedded Technology ; STM32 ; QR Code ; RFID ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.010", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "National Engineering Research Center for E-Learning, Central China Normal University, Wuhan, China"}], "References": []}, {"ArticleId": 80586068, "Title": "Design and Implementation of App System for Legal Consulting Based on JAVA Technology", "Abstract": "At present, the people's legal awareness is becoming stronger and stronger, the demand for legal related business is also increasing, and the volume of legal consultation business is increasing. Traditional legal consultation business processing methods are difficult to adapt to the actual needs. In view of the fact that smart phone applications have become the mainstream at present, the smart phone platform is used to develop the software system of legal consultation business. For the development direction with broad market prospects. Based on this, this paper combines the author's many years of legal experience, analyses the current situation of legal consultation business in China, investigates and studies different types of legal consultation business, and develops a mobile legal consultation system using Android mobile phone platform.", "Keywords": "Legal consultation ; Android ; Mobile internet ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.028", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Arts and law, Wuhan Donghu University, P.R.China;@qq.com"}], "References": []}, {"ArticleId": 80586071, "Title": "Design of Intelligent Humidification System for Ionic Water Atomization", "Abstract": "After studying the principle of ion water atomization, an intelligent humidification system of ion water atomization was designed. The system sensed the external temperature and humidity through the sensor, and built-in intelligent chip to calculate the sensor data. Automatically adjust the amount of ionized water atomization to maintain environmental humidity; Ions can also be added in the atomization humidification process can effectively inhibit the growth of bacteria and eliminate pathogens, this technology has a good practical and practical value.", "Keywords": "Ion ; Electrolytic water ; Moisture retention ; Inhibition of bacteria ; Download full text in PDF", "DOI": "10.1016/j.procs.2020.02.079", "PubYear": 2020, "Volume": "166", "Issue": "", "JournalId": 3363, "JournalTitle": "Procedia Computer Science", "ISSN": "1877-0509", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Aerospace Control Technology Research Institute, Shanghai, 201109;Shanghai Servo system Engineering Technology Research Center, Shanghai, 201109;@qq.com"}, {"AuthorId": 2, "Name": "<PERSON> zheng", "Affiliation": "Shanghai Institute of Technical Physics of the Chinese Academy of Sciences, Shanghai, 200083"}, {"AuthorId": 3, "Name": "DaWei Gu", "Affiliation": "Shanghai Aerospace Control Technology Research Institute, Shanghai, 201109;Shanghai Servo system Engineering Technology Research Center, Shanghai, 201109"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Shanghai Aerospace Control Technology Research Institute, Shanghai, 201109;Shanghai Servo system Engineering Technology Research Center, Shanghai, 201109"}], "References": []}]