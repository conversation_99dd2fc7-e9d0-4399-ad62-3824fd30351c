from collections import deque

class State:
    def __init__(self, robot_pos, box_pos, holding, path):
        self.robot_pos = robot_pos    # 机器人当前位置
        self.box_pos = box_pos       # 盒子当前位置
        self.holding = holding       # 机器人是否持有盒子
        self.path = path             # 操作路径记录

    def __eq__(self, other):
        return (self.robot_pos == other.robot_pos and
                self.box_pos == other.box_pos and
                self.holding == other.holding)

    def __hash__(self):
        return hash((self.robot_pos, self.box_pos, self.holding))

def solve():
    # 初始状态：机器人在ALCOVE，盒子在A，未持有盒子
    initial = State("ALCOVE", "A", False, [])
    queue = deque([initial])
    visited = set()

    while queue:
        current = queue.popleft()

        # 检查是否达到目标状态：盒子在B，机器人在ALCOVE
        if current.box_pos == "B" and current.robot_pos == "ALCOVE":
            return current.path

        if current in visited:
            continue
        visited.add(current)

        # 生成所有可能的下一步操作
        # 操作1：移动机器人到其他位置
        for new_pos in ["ALCOVE", "A", "B"]:
            if new_pos != current.robot_pos:
                new_path = current.path + [f"移动到 {new_pos}"]
                queue.append(State(new_pos, current.box_pos, current.holding, new_path))

        # 操作2：拿起盒子（如果机器人在盒子位置且未持有）
        if current.robot_pos == current.box_pos and not current.holding:
            new_path = current.path + [f"拿起盒子"]
            queue.append(State(current.robot_pos, current.box_pos, True, new_path))

        # 操作3：放下盒子（如果持有盒子）
        if current.holding:
            new_box_pos = current.robot_pos
            new_path = current.path + [f"将盒子放在 {new_box_pos}"]
            queue.append(State(current.robot_pos, new_box_pos, False, new_path))

    return None  # 无解

# 执行求解并打印结果
solution = solve()
if solution:
    print("操作序列：")
    for step in solution:
        print(f"- {step}")
    print("魏海东 202331060927")
else:
    print("无解")
    print("魏海东 202331060927")