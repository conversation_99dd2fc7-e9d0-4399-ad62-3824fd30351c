[{"ArticleId": 89244722, "Title": "An Exploratory Analysis of Possible Effects of “Nudge” On Public Policies to Support Innovation in Brazil: The Case of Inovar-Auto Program", "Abstract": "<p>This study presents a proposal to improve public policies for supporting innovation in Brazil’s automotive industry by using a conceptual model with incremental benefits based on nudge concepts. This new model aims to reduce the complexity of the current fiscal mechanism. It makes the tax incentive mechanism more dynamic and stimulates innovative companies to improve their innovative performance. For this, a qualitative comparative analysis of the effects (empirical and simulated) of a public innovation policy — an automotive policy called Inovar-Auto — compares a consolidated traditional tax incentive mechanism with the new model. It is concluded that the incremental scale of benefits stimulates the companies-government interaction more safely and effectively, reducing the complexity of the current tax incentive mechanism and offering new paths and choice possibilities. This study contributes to the literature by presenting an innovative tax incentive mechanism, a “nudge” for changing companies’ behavior, which can be applied in other countries.</p>", "Keywords": "", "DOI": "10.1142/S0219877021500152", "PubYear": 2021, "Volume": "18", "Issue": "4", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Mastering Student of Mechatronics Systems Department – PPMEC, University of Brasília – UnB, 70910-900, Brasília, Brazil"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Professor of Production Engineering Department – EPR, University of Brasília – UnB, 70910-900, Brasília, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Director Disruptive Technologies of Ministry of Science, Technology, Innovations and Communications - MCTIC, 70043-900, Brasília, Brazil"}], "References": []}, {"ArticleId": 89244723, "Title": "Critical Success Factors for Leveraging Technology Transfer from Higher Education Institutions to Industry: Indian Context", "Abstract": "<p>This study aims to identify and empirically evaluates the critical factors of successful technology transfer (TT) from higher education institutions (HEI) to industry and to develop a TT model in the Indian context. With the help of questionnaire survey, the perception profile of 318 respondents was collected from PAN-India. Utilizing the survey data, factor analysis identified six constructs representing five critical factors of TT and an outcome factor. The results of multiple regression analysis indicate significant effect of all five critical factors on an outcome factor. The findings of the study validate the TT Model and identify supportive and promotive TT environment factor, market and finance factor and HEI factor to be the most important factors of successful TT. This study may help the policymakers in strategizing future initiatives to improve the rate of successful TT.</p>", "Keywords": "", "DOI": "10.1142/S0219877021500188", "PubYear": 2021, "Volume": "18", "Issue": "4", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Amity Business School, Amity University Uttar Pradesh, Noida, India"}, {"AuthorId": 2, "Name": "Balvinder Shukla", "Affiliation": "Amity University Uttar Pradesh, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Foundation for Innovation and Technology Transfer, Indian Institute of Technology, Delhi, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Centre for VUCA Studies, Amity University Uttar Pradesh, Lucknow, India"}], "References": []}, {"ArticleId": 89244724, "Title": "Privacy Concerns and Mobile App Store", "Abstract": "<p>Mobile apps have been transforming how individuals and organizations share information and conduct business. This research studies the relationships among user readiness factors, privacy concerns, and user acceptance of mobile app stores. A survey was conducted among college smart phone users. Results indicate that the privacy concerns construct has a direct negative effect on purchase intention of mobile apps in the app store. In addition, user readiness has a direct positive effect on attitudes to the app store, and a net positive effect on purchase intention of apps in the app store. Implications of our findings were discussed.</p>", "Keywords": "", "DOI": "10.1142/S021987702150019X", "PubYear": 2021, "Volume": "18", "Issue": "4", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "San Francisco State University, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": [{"Title": "Open Banking: The Emergence of New Digital Business Models", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "5", "Page": "2050033", "JournalTitle": "International Journal of Innovation and Technology Management"}, {"Title": "Video-Sharing Apps Business Models: TikTok Case Study", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "17", "Issue": "7", "Page": "2050050", "JournalTitle": "International Journal of Innovation and Technology Management"}]}, {"ArticleId": ********, "Title": "Leading Indicators for Detecting Change of Technology Trends: Comparison of Patents, Papers and Newspaper Articles in Japan and US", "Abstract": "<p>Continual development necessitates innovation. One must discover seeds of innovation and then concentrate resources on these seeds. To do so, one must recognize technology trends and then adopt and execute appropriate innovation strategies. This study used advanced change point detection method to investigate leading indicators that represent changes in technology trends. We examine patents, papers, and newspaper articles in Japan and US for 55 technologies. Results suggest that patents can be more appropriate as leading indicators than either papers or newspapers. This result can contribute to appropriate innovation strategies for planning and updating, and can provide tools that are useful to decision-makers.</p>", "Keywords": "", "DOI": "10.1142/S0219877021500176", "PubYear": 2021, "Volume": "18", "Issue": "4", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "New Energy and Industrial Technology Development Organization, Kanagawa, Japan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "The University of Tokyo, Tokyo, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Tokyo, Tokyo, Japan"}], "References": []}, {"ArticleId": 89244726, "Title": "Safety Technology Adoption: Predicting Intention to Use Car Dashcams in an Emerging Country", "Abstract": "<p>This study investigates the factors that affect the user’s intention to use dashcam in Malaysia. This study examines the quantitative relationship of intrinsic as well as extrinsic factors such as personal innovativeness, perceived uniqueness, perceived usefulness, perceived ease of use, attitude, perceived behavioral control, social influence, price value, and trust to the purchase intention of dashcam. Purposive sampling technique was employed to collect responses from 232 respondents based on two criteria: first, individuals who have experienced driving on the road and have a car; and second, individuals who not yet adopt or purchase the dashcam. The data were analyzed using SmartPLS (version 3.3.2). No relationship between personal innovativeness and perceived usefulness was found, in contrast to a significant relationship the former and perceived ease of use. Furthermore, perceived uniqueness was found significant to both perceived usefulness and perceived ease of use. Consistent with the literature, both perceived usefulness and perceived ease of use were identified as factors influencing attitude. However, perceived usefulness did not affect intention. Perceived behavioral control, social influence, attitude, and trust significantly affected the behavioral intention to use the dashcam in Malaysia. This study attempts to integrate and adapt two technology adoption models, namely the Combined Technology Acceptance Model and Theory Planned Behavior and extension of Unified Theory of Acceptance and Use of Technology, also extends the model with personal innovativeness, perceived uniqueness, and trust to fulfil the study’s objectives as well.</p>", "Keywords": "", "DOI": "10.1142/S021987702150022X", "PubYear": 2021, "Volume": "18", "Issue": "5", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate School of Business, Universiti Sains Malaysia, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Business, Universiti Sains Malaysia, Malaysia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Graduate School of Business, Universiti Sains Malaysia, Malaysia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Graduate School of Business, Universiti Sains Malaysia, Malaysia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Scientific Research and Graduate, School Universidad de Lima, Lima, Peru"}], "References": [{"Title": "Students’ behavioral intention to use and achievements in ICT-Integrated mathematics remedial instruction: Case study of a calculus course", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "145", "Issue": "", "Page": "103740", "JournalTitle": "Computers & Education"}, {"Title": "Using smartwatches for fitness and health monitoring: the UTAUT2 combined with threat appraisal as moderators", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "40", "Issue": "3", "Page": "282", "JournalTitle": "Behaviour & Information Technology"}, {"Title": "Supply chain information integration and its impact on the operational performance of manufacturing firms in Malaysia", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "8", "Page": "103386", "JournalTitle": "Information & Management"}]}, {"ArticleId": 89244727, "Title": "Innovation, Finance, and Economic Growth in OECD Countries: New Insights from a Panel Causality Approach", "Abstract": "<p>In this paper, using a panel causality approach, we examine endogenous connections between financial development, innovation, and economic growth in OECD countries for the period 1961–2018. The empirical results of our study show that financial development and innovation support long-run economic growth and that the short-run dynamics facet the multifarious interconnections between financial development, innovation, and economic growth. The strategic insight drawn from this research is that to ensure sustainable economic growth, policy-makers in the OECD countries must pay attention to establishing an integrated structure that looks into co-improvement policies concerning the activities that enhance financial development, innovation, and economic growth.</p>", "Keywords": "", "DOI": "10.1142/S0219877021500139", "PubYear": 2021, "Volume": "18", "Issue": "4", "JournalId": 7908, "JournalTitle": "International Journal of Innovation and Technology Management", "ISSN": "0219-8770", "EISSN": "1793-6950", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Gupta School of Management, Indian Institute of Technology, Kharagpur, WB 721302, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Gupta School of Management, Indian Institute of Technology, Kharagpur, WB 721302, India"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> School of Management, Indian Institute of Technology, Kharagpur, WB 721302, India;IBS-Hyderabad, ICFAI Foundation for Higher Education, Hyderabad, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> Gupta School of Management, Indian Institute of Technology, Kharagpur, WB 721302, India;National Informatics Centre, Baripada, India"}], "References": []}, {"ArticleId": 89244976, "Title": "Rational engineering of Ag-doped reduced graphene oxide as electrochemical sensor for trace mercury ions monitoring", "Abstract": "Food safety has aroused increasing public concerns because it is closely related to human health. As a common pollutant, mercury ions (Hg<sup>2+</sup>) can cause an enormous menace to human health even at very low fouling level. Herein, we designed a three-dimensional reduced graphene oxide (rGO) doped by silver nanoparticles (Ag-rGO), which could be employed as an effective sensing interface to electrochemically detect Hg<sup>2+</sup>. By characterization with scanning electron microscopy (SEM), energy dispersive spectroscopy (EDS) and X-ray diffraction (XRD), we confirmed that the Ag-rGO composites had multiple fold structures. After incubation with Hg<sup>2+</sup>, an obvious electrochemical signal enhancement was observed, which could be applied to detect the low level of Hg<sup>2+</sup>. The sensor based on Ag-rGO exhibited a low detection limit of 0.0049 μg/L of Hg<sup>2+</sup> due to its multiple folds structure providing a large electrochemical active area and facilitating the electron transfer. Significantly, this proposed sensor was successfully used to evaluate the amount of Hg<sup>2+</sup> in fish and human blood samples with acceptable results. This sensing strategy showed excellent performance for Hg<sup>2+</sup> detection in food and clinical-related systems, which is of great significance for safeguarding food safety and human health.", "Keywords": "Ag-rGO ; Hg<sup>2+</sup> ; Biosensor ; Electrochemical detection", "DOI": "10.1016/j.snb.2021.130383", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "Shan<PERSON><PERSON> Ma", "Affiliation": "Key Laboratory of Spin Electron and Nanomaterials of Anhui Higher Education Institutes, School of Chemistry and Chemical Engineering, Suzhou University, Suzhou, 234000, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Spin Electron and Nanomaterials of Anhui Higher Education Institutes, School of Chemistry and Chemical Engineering, Suzhou University, Suzhou, 234000, China;Corresponding authors at: Key Laboratory of Spin Electron and Nanomaterials of Anhui Higher Education Institutes, School of Chemistry and Chemical Engineering, Suzhou University, Suzhou, 234000, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Spin Electron and Nanomaterials of Anhui Higher Education Institutes, School of Chemistry and Chemical Engineering, Suzhou University, Suzhou, 234000, China"}, {"AuthorId": 4, "Name": "Hongwei Shi", "Affiliation": "Key Laboratory of Spin Electron and Nanomaterials of Anhui Higher Education Institutes, School of Chemistry and Chemical Engineering, Suzhou University, Suzhou, 234000, China"}, {"AuthorId": 5, "Name": "Keying <PERSON>", "Affiliation": "Key Laboratory of Spin Electron and Nanomaterials of Anhui Higher Education Institutes, School of Chemistry and Chemical Engineering, Suzhou University, Suzhou, 234000, China;State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Sciences, Shanghai, 200050, China;Corresponding authors at: Key Laboratory of Spin Electron and Nanomaterials of Anhui Higher Education Institutes, School of Chemistry and Chemical Engineering, Suzhou University, Suzhou, 234000, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON> Shen", "Affiliation": "School of Food & Biological Engineering, Key Laboratory for Agricultural Products Processing of Anhui Province, Hefei University of Technology, Hefei, 230009, China;Corresponding author"}], "References": []}, {"ArticleId": 89245238, "Title": "The Application of Internet of Things Technology in the Medical Field", "Abstract": "", "Keywords": "", "DOI": "10.12677/SEA.2021.103047", "PubYear": 2021, "Volume": "10", "Issue": "3", "JournalId": 13825, "JournalTitle": "Software Engineering and Applications", "ISSN": "2325-2286", "EISSN": "2325-2278", "Authors": [{"AuthorId": 1, "Name": "", "Affiliation": ""}], "References": []}, {"ArticleId": 89245391, "Title": "Mobil Devlet Kullanımını Etkileyen Faktörlerin Belirlenmesi: Ankara İli Örneği", "Abstract": "<p>Ka<PERSON><PERSON>uz ve mobil teknolojilerde yaşanan gelişmeler halka hizmet sunmada yeni bir kanal olan mobil devlete (m-devlet) olan ilgiyi arttırmıştır. Bu doğrultuda m-devlet uygulaması üzerinden sunulan hizmetlerin kullanıcılar tarafından benimsenmesi ve kullanılması kritik öneme sahiptir. Bu çalışmanın amacı, m-devlet kullanımını etkileyen faktörlerin belirlenmesidir. Literatür taraması sonucu m-devlet kullanımını etkilediği düşünülen yedi faktörden (bilgi kalitesi, güvenlik algısı, güven, hizmet maliyeti, kullanım kolaylığı, sosyal etki, yararlılık) oluşan kuramsal model ve çalışmanın hipotezleri oluşturulmuştur. Veri toplama aracı olarak M-Devlet Kullanımı Ölçeği (M-DKÖ) geliştirilmiştir. Çalışmanın verileri Ocak 2020 tarihinde Ankara’daki üç devlet üniversitesinde öğrenim gören 183 üniversite öğrencisinden toplanmıştır. M-devlet kullanımını etkileyen faktörlerin belirlenmesi için hazırlanan ölçeğinin faktör yapısı LISREL paket programı kullanılarak analiz edilmiştir. Ölçeğin yapı güvenirliği ve yakınsama geçerliği doğrulayıcı faktör analizi (DFA) ile gerçekleştirilmiştir. Analizler sonucu kuramsal modeldeki yedi faktörün t değerleri, hata varyansları, standart katsayıları ve uyum indeksleri kontrol edilmiştir. Çalışma sonucunda kuramsal modeldeki yedi faktörden biri olan hizmet maliyeti dışındaki diğer altı faktörün m-devlet kullanımını etkilediği görülmüştür. </p>", "Keywords": "<PERSON><PERSON><PERSON><PERSON>, M-Dev<PERSON> Kullanımı, Yapısal Eşitlik Modeli, Türkiye", "DOI": "10.26650/acin.866945", "PubYear": 2021, "Volume": "5", "Issue": "1", "JournalId": 55902, "JournalTitle": "Acta INFOLOGICA", "ISSN": "2602-3563", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89245393, "Title": "Two Level Kazakh Morphology", "Abstract": "<p>We present a comprehensive two level morphological analysis of contemporary Kazakh with implementation and a disambiguation test data set on the Nuve Framework. Our study differs from the similar studies in a number of ways: (i) Our study covers both derivational and inflectional morphology to a greater extend (ii) Our implementation consisting of orthographic rules, morphotactics, a root lexicon of roughly 24 thousand roots, a lexicon of roughly 150 suffixes is open source which can be downloaded, reviewed and tested. (ii) Roughly 10 thousand manually disambiguated parses are available as a morphological disambiguation data set. (iii) It is easily extensible meaning it can be modified or extended with new rules without any programming. (iv) we are able to tackle emerging problems quickly and easily since Nuve is maintained by our study group. (v) Our implementation can handle separately written morphemes or digraphs etc. directly. (vi) We also have a Turkish morphological parser/generator in Nuve for morphology based machine translation between Turkish and other Turkic languages since these closely related languages have a lot in common from lexical, morphological, and syntactic aspects.</p>", "Keywords": "Kazakh Morphology, Natural Language Processing, Computational Linguistics", "DOI": "10.26650/acin.842758", "PubYear": 2021, "Volume": "5", "Issue": "1", "JournalId": 55902, "JournalTitle": "Acta INFOLOGICA", "ISSN": "2602-3563", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89245397, "Title": "EEG-Based Demarcation of Yogic and Non-Yogic Sleep Patterns Using Power Spectral Analysis", "Abstract": "<p>Electroencephalogram (EEG) signals resulting from recordings of polysomnography play a significant role in determining the changes in physiology and behavior during sleep. This study aims at demarcating the sleep patterns of yogic and non-yogic subjects. Frequency domain features based on power spectral density methods were explored in this study. The EEG recordings were segmented into 1s and 0.5s. EEG patterns with four windowing scheme overlaps (0%, 50%, 60% and 75%) to ensure stationarity of the signal in order to investigate the effect of the pre-processing stage. In order to recognize the yoga and non-yoga group through N3 sleep stage, non-linear KNN classifier was introduced and performance was evaluated in terms of sensitivity and specificity. The experimental results show that modified covariance PSD estimate is the best method in classifying the sleep stage N3 of yogic and non-yogic subjects with 95% confidence interval, sensitivity, specificity and accuracy of 97.3%, 98% and 97%, respectively.</p>", "Keywords": "", "DOI": "10.4018/IJEHMC.20211101.oa2", "PubYear": 2021, "Volume": "12", "Issue": "6", "JournalId": 22950, "JournalTitle": "International Journal of E-Health and Medical Communications", "ISSN": "1947-315X", "EISSN": "1947-3168", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Center for Medical Electronics and Computing, M<PERSON><PERSON> Institute of Technology, Bangalore, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Center for Medical Electronics and Computing, M<PERSON><PERSON> Institute of Technology, Bangalore, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Center for Medical Electronics and Computing, M<PERSON><PERSON> Institute of Technology, Bangalore, India"}, {"AuthorId": 4, "Name": " Nithin N. S.", "Affiliation": "Center for Medical Electronics and Computing, M<PERSON><PERSON> Institute of Technology, Bangalore, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "SVYASA University, India"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Center for Medical Electronics and Computing, M<PERSON><PERSON> Institute of Technology, Bangalore, India"}], "References": []}, {"ArticleId": 89245485, "Title": "Activity recognition from Smartphone data using WSVM-HMM classification", "Abstract": "<p>A lot of real-life mobile sensing applications are becoming available nowadays. The traditional approach for activity recognition employs machine learning algorithms to learn from collected data from smartphpne and induce a model. The model generation is usually performed offline on a server system and later deployed to the phone for activity recognition. In this paper, we propose a new hybrid classification model to perform automatic recognition of activities using built-in embedded sensors present in smartphones. The proposed method uses a trick to classify the ongoing activity by combining Weighted Support Vector Machines (WSVM) model and Hidden Markov Model (HMM) model. The sensory inputs to the classifier are reduced with the Linear Discriminant Analysis (LDA). We demonstrate how to train the hybrid approach in this setting, introduce an adaptive regularization parameter for WSVM approach, and illustrate how our proposed method outperforms the state-of-the-art on a large benchmark dataset.</p>", "Keywords": "", "DOI": "10.4018/IJEHMC.20211101oa12", "PubYear": 2021, "Volume": "12", "Issue": "6", "JournalId": 22950, "JournalTitle": "International Journal of E-Health and Medical Communications", "ISSN": "1947-315X", "EISSN": "1947-3168", "Authors": [], "References": []}, {"ArticleId": 89245493, "Title": "Deploying a smart queuing system on edge with Intel OpenVINO toolkit", "Abstract": "<p>Recent increases in computational power and the development of specialized architecture led to the possibility to perform machine learning, especially inference, on the edge. OpenVINO is a toolkit based on convolutional neural networks that facilitates fast-track development of computer vision algorithms and deep learning neural networks into vision applications, and enables their easy heterogeneous execution across hardware platforms. A smart queue management can be the key to the success of any sector. In this paper, we focus on edge deployments to make the smart queuing system (SQS) accessible by all also providing ability to run it on cheap devices. This gives it the ability to run the queuing system deep learning algorithms on pre-existing computers which a retail store, public transportation facility or a factory may already possess, thus considerably reducing the cost of deployment of such a system. SQS demonstrates how to create a video AI solution on the edge. We validate our results by testing it on multiple edge devices, namely CPU, integrated edge graphic processing unit (iGPU), vision processing unit (VPU) and field-programmable gate arrays (FPGAs). Experimental results show that deploying a SQS on edge is very promising.</p><p>© The Author(s), under exclusive licence to Springer-Verlag GmbH Germany, part of Springer Nature 2021.</p>", "Keywords": "Edge AI;Edge computing;Intel OpenVINO;Optimization;Smart queuing system;Soft computing", "DOI": "10.1007/s00500-021-05891-2", "PubYear": 2021, "Volume": "25", "Issue": "15", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Thakur International School Mumbai, Mumbai, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Systems Engineering, Kocaeli University, Izmit, Turkey."}], "References": [{"Title": "From Batch &amp;amp; Queue to Industry 4.0-Type Manufacturing Systems: A Taxonomy of Alternative Production Models", "Authors": "<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "2", "Page": "299", "JournalTitle": "Journal of Service Science and Management"}]}, {"ArticleId": 89245569, "Title": "EDENet: Elaborate density estimation network for crowd counting", "Abstract": "For the CNN-based density estimation approaches in the field of crowd counting, how to generate a high-quality density map with accurate counting performance and detailed spatial description is still an open question. In this paper, to tackle the aforementioned contradiction, we propose an end-to-end trainable architecture called Elaborate Density Estimation Network for Crowd Counting (EDENet), which can gradually generate high-quality density estimation maps based on distributed supervision. Specifically, EDENet is composed of Feature Extraction Network (FEN), Feature Fusion Network (FFN), Double-Head Network (DHN) and Adaptive Density Fusion Network (ADFN). The FEN adopts VGG as the backbone network and employs Spatial Adaptive Pooling (SAP) to extract coarse-grained features. The FFN can effectively fuse contextual information and localization information for enhancing the spatial description ability of fine-grained features. In the DHN, the Density Attention Module (DAM) can provide attention masks of foreground-background, thereby urging the Density Regression Module (DRM) to focus on the pixels around the head annotations to regress density maps with different resolutions. The ADFN constructed on the basis of the adaptive weighting mechanism can directly introduce coarse-grained density representation into high-resolution density maps to strengthen the commonality and dependency among density maps. Extensive experiments on four benchmark crowd datasets (the ShanghaiTech, the UCF-QNRF, the JHU-CRWORD++ and the NWPU-Crowd) indicate that EDENet can achieve state-of-the-art recognition performance and high robustness. Not only that, the density map with the highest Peak Signal to Noise Ratio (PSNR) can be considered to be of high quality.", "Keywords": "Crowd counting ; Feature fusion ; Adaptive fusion ; Elaborate density estimation", "DOI": "10.1016/j.neucom.2021.06.086", "PubYear": 2021, "Volume": "459", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "Yinfeng Xia", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei 230027, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei 230027, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei 230027, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei 230027, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei 230027, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Automation, University of Science and Technology of China, Hefei 230027, China;Corresponding author"}], "References": [{"Title": "MobileCount: An efficient encoder-decoder framework for real-time crowd counting", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "407", "Issue": "", "Page": "292", "JournalTitle": "Neurocomputing"}, {"Title": "Pixel-Wise Crowd Understanding via Synthetic Data", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "129", "Issue": "1", "Page": "225", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 89245583, "Title": "A parallel multi-objective evolutionary algorithm for community detection in large-scale complex networks", "Abstract": "Community detection in large-scale complex networks has recently received significant attention as the volume of available data is becoming larger. The use of evolutionary algorithms (EAs) for community detection in large-scale networks has gained considerable popularity because these algorithms are fairly effective in networks with a relatively small number of nodes. In this paper, we propose a parallel multi-objective EA, called PMOEA, for community detection in large-scale networks, where the communities associated with key network nodes are detected in parallel. Specifically, we develop a multi-objective and a single-objective EA. The former is used to detect the communities of a key node instead of all communities in the network. The latter obtains the communities in the entire network using the previously detected communities of each key node. The performance of the proposed method was verified on both large-scale synthetic benchmark networks and real-world networks. The results demonstrated the superiority of PMOEA over six EA-based and two non-EA-based community-detection algorithms for large-scale networks. Introduction The world has a wide variety of complex systems, which can be effectively modeled using complex networks, such as social [1] and biological networks [2]. The task of detecting clusters of closely connected nodes in a complex network is termed community detection and is important for revealing functional and structural properties of the network [3]. For example, in protein–protein interaction networks, communities are group proteins with the same specific function in the cell [2]. Recently, community detection for large-scale complex networks has attracted considerable attention in a variety of areas because of the increasing volume of data related to real-world problems [4], such as identifying brain functions in large-scale brain networks based on human functional MRI data [5], and detecting IP addresses within a managed domain in large-scale IP networks [6]. In recent years, a large number of community detection algorithms have been developed based on different principles. They can be divided into three groups [3]. In the first group, traditional methods in graph partitioning and data clustering are adopted, including graph partitioning, hierarchical clustering, partitional clustering, and spectral clustering [7]. In the second group, divisive algorithms are used to remove the edges that connect nodes of different communities. The most popular algorithm in this group was developed by Girvan and Newman [8], [9]. In the third group, optimization algorithms, such as mathematical programming [10] and heuristics (e.g., simulated annealing [11]) are used to maximize the quality of the detected communities. Among these algorithms, most non–heuristic algorithms are deterministic. Despite the large number of algorithms mentioned above, community detection in large-scale networks remains challenging because the performance of most existing algorithms deteriorates considerably in terms of both detection quality and computational efficiency. Evolutionary algorithms (EAs) are a class of meta-heuristics that have been successfully applied to various complex real-world problems [12], [13], [14]. Several effective EAs have been developed for community detection in complex networks [15], [16]. These algorithms can be divided into two groups according to the number of objectives to be optimized. In the first group, the communities of a network are obtained by optimizing a single objective, where the modularity Q is often adopted as the optimization objective. Algorithms in this group include the genetic algorithm for community detection [17] and chemical-reaction optimization with dual representation for community detection [18]. In the second group, multiple conflicting objectives are simultaneously optimized. The first multi-objective EA (MOEA) for community detection, called MOGA-Net, was developed by Pizzuti in [19], where the fast non-dominated sorting genetic algorithm [20] was adopted to maximize the community score and minimize the community fitness. As reported in [19], MOEA-based community-detection algorithms can overcome certain disadvantages of most existing single-objective EAs, such as resolution limit of modularity. Therefore, a large number of promising community-detection MOEA-based algorithms have been developed, such as multi-objective discrete particle-swarm optimization based on decomposition (MODPSO) [16], quantum-behaved discrete multi-objective particle-swarm optimization (QDM-PSO) [21], and maximal clique-based MOEA [22]. Even though EAs have exhibited satisfactory community-detection performance in complex networks, most existing algorithms suffer from scalability issues owing to the curse of dimensionality, as pointed out by Pizzuti in [23]. As network size increases, the search space of EAs increases exponentially. Thus, most existing EA-based community-detection algorithms are only suitable for complex networks with a relatively small number of nodes. To address this issue, a few attempts have recently been made. For example, Liu et al. [24] developed a similarity-based MOEA (MEA<sub> s </sub>-SN) for signed networks, where an indirect community encoding was proposed to enhance the scalability. He et al. [25] proposed an EA (CoCoMi) that employs a cooperative co-evolutionary framework to handle large-scale networks, and it was experimentally demonstrated that CoCoMi could achieve satisfactory performance in complex networks with 27519 nodes. It is worth noting that a number of community detection algorithms for large-scale networks are not EA-based but use different ideas, such as simulated annealing [11] and local extension [26], [27]. In this study, we also focus on enhancing the scalability of EAs in community detection, and develop a parallel MOEA. It should be stressed that parallelization is a widely adopted method in EAs to handle large-scale optimization problems, such as the vehicle routing problem, to which the parallel multi-objective memetic algorithm was applied in [28], and high-dimensional feature selection problems, for which a parallel MOEA was used in [29]. To be specific, the main contributions of this study are summarized as follows. (1) A multi-objective evolutionary algorithm is proposed to detect the communities associated with a node instead of all communities in the network. To this end, two measures, namely, the conductance of a community and the number of key nodes in a community, are adopted as the optimization objectives. Moreover, a crossover operator and a mutation operator are designed to search for better communities associated with a node. (2) Based on the above algorithm, a parallel algorithm, called PMOEA, is proposed for community detection in large-scale networks. Specifically, some key nodes are first identified in the network, and then the communities associated with these nodes are detected in parallel. To obtain the communities of the entire network, a single-objective EA is also developed so that the communities associated with each key node may be integrated. (3) The effectiveness of the proposed PMOEA is verified on both large-scale synthetic benchmark networks and real-world networks. Experiments demonstrate that the PMOEA is superior to six EA-based and two non-EA-based community detection algorithms for large-scale networks. Moreover, it exhibits highly satisfactory performance for networks even with 200,000 nodes in terms of both community-detection quality and computational efficiency. The remainder of this paper is organized as follows. In Section 2, we present the definition of community detection, and review related work on EA-based community-detection algorithms for large-scale networks. The details of the proposed PMOEA are presented in Section 3, followed by the experimental results in Section 4. Finally, the paper is concluded in Section 5. Figures Framework of proposed PMOEA. Example of crossover and mutation operators in the proposed MOEA: (a) A network G with 10 nodes, where node 1 is assumed to be a key node in G; (b) two individuals p1 and p2 of a weight vector, which ... NMI value and runtime (s) of the algorithms under comparison on LFR networks with size ranging from 5000 to 10,000, and mixing parameter μ=0.25, averaged over 20 independent runs: (a) NMI value and (b... Number of key nodes identified by the proposed PMOEA and runtime (s) of the MOEA for detecting the communities associated with the key nodes when the parallel and serial frameworks are adopted on LFR ... Number of nodes checked by the MOEA in the proposed PMOEA for detecting a community associated with a key node on LFR networks with 5000 to 10,000 nodes and μ=0.25, averaged over all communities assoc... NMI value and runtime (s) of the algorithms under comparison on LFR networks with different values of the mixing parameter μ and 5000 nodes, averaged over 20 independent runs: (a) NMI value and (b) ru... Show all figures Section snippets Community detection and related work Herein, we first present the definition of community detection in complex networks, and then recall existing EAs for community detection in complex networks, particularly those for large-scale networks. Proposed Algorithm PMOEA Herein, we first present the framework of the proposed PMOEA for community detection in large-scale complex networks. Subsequently, we elaborate on the processes that are performed in parallel and the process whereby the final community partition of the entire network is obtained. Experimental results and analysis In this section, we experimentally verify the effectiveness of the proposed PMOEA for community detection in large-scale networks in terms of both computational efficiency and the quality of the detected communities. Specifically, the experiments are as follows. Conclusion In this study, a parallel multi-objective evolutionary algorithm, called PMOEA, was developed for community detection in large-scale networks. In the proposed PMOEA, the communities associated with key nodes are detected in parallel by copies of an MOEA, where each copy is responsible for the detection of the communities associated with a key node. Experiments on both synthetic and real-world networks demonstrated the effectiveness of the proposed PMOEA in community detection for large-scale CRediT authorship contribution statement Yansen Su: Investigation, Methodology, Writing - original draft. Kefei Zhou: Data curation, Visualization. Xingyi Zhang: Conceptualization, Investigation, Writing - review & editing. Ran Cheng: Writing - review & editing. Chunhou Zheng: Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements This work was supported by Key Project of Science and Technology Innovation 2030, funded by the Ministry of Science and Technology of China (2018AAA0101302), National Natural Science Foundation of China (61672033, 61822301, U1804262), Anhui Provincial Natural Science Foundation for Distinguished Young Scholars (1808085J06), Recruitment Program for Leading Talent Team of Anhui Province (2019–16), Key Program of Natural Science Project of Educational Commission of Anhui Province (KJ2019A0029), References (50) C. Shi et al. Multi-objective community detection in complex networks Applied Soft Computing (2012) X. Zhang et al. A network reduction-based multiobjective evolutionary algorithm for community detection in large-scale complex networks IEEE Transactions on Cybernetics (2020) X. Zhang et al. A network reduction-based multiobjective evolutionary algorithm for community detection in large-scale complex networks IEEE Transactions on Cybernetics (2020) D. Kimovski et al. Parallel alternatives for evolutionary multi-objective optimization in unsupervised feature selection Expert Systems with Applications (2015) L. Li et al. Quantum-behaved discrete multi-objective particle swarm optimization for complex network clustering Pattern Recognition (2017) D. Chen et al. Multi-objective optimization of community detection using discrete teaching-learning-based optimization with decomposition Information Sciences (2016) S. Zheng et al. A jumping genes inspired multi-objective differential evolution algorithm for microwave components optimization problems Applied Soft Computing (2017) C.-H. Mu et al. Memetic algorithm with simulated annealing strategy and tightness greedy optimization for community detection in networks Applied Soft Computing (2015) S. Srinivas et al. Community detection and influential node identification in complex networks using mathematical programming Expert Systems with Applications (2019) M.A. Javed et al. Community detection in networks: A multidisciplinary review Journal of Network and Computer Applications (2018) M. Najafi et al. Overlapping communities reveal rich structure in large-scale brain networks during rest and task conditions NeuroImage (2016) R. Wang et al. A novel graph clustering method with a greedy heuristic search algorithm for mining protein complexes from dynamic and static ppi networks Information Sciences (2020) X. Zhao et al. A community detection algorithm based on graph compression for large-scale social networks Information Sciences (2021) Y. Su, C. Liu, Y. Niu, F. Cheng, X. Zhang, A community structure enhancement-based community detection algorithm for... X. Zhang et al. A fast overlapping community detection algorithm based on weak cliques for large-scale networks IEEE Transactions on Computational Social Systems (2017) A. Jakalan et al. Community detection in large-scale IP networks by observing traffic at network boundary G. M, N. M, Community structure in social and biological networks, Proceedings of the National Academy of Sciences of... M.E. Newman et al. Finding and evaluating community structure in networks Physical Review E (2004) K.K. Dhaliwal et al. Integrated Cat Swarm Optimization and Differential Evolution Algorithm for Optimal IIR Filter Design in Multi-Objective Framework (2017) Y. Tian et al. An evolutionary algorithm for large-scale sparse multiobjective optimization problems IEEE Transactions on Evolutionary Computation (2020) M. Gong et al. Complex network clustering by multiobjective discrete particle swarm optimization based on decomposition IEEE Transactions on Evolutionary Computation (2014) C. Shi et al. A genetic algorithm for detecting communities in large-scale complex networks Advances in Complex Systems (2010) H. Chang et al. Community detection using dual-representation chemical reaction optimization IEEE Transactions on Cybernetics (2017) C. Pizzuti A multi-objective genetic algorithm for community detection in networks K. Deb et al. A fast and elitist multiobjective genetic algorithm: NSGA-II IEEE Transactions on Evolutionary Computation (2002) View more references Cited by (0) Recommended articles (6) Research article A multi-objective evolutionary algorithm based on length reduction for large-scale instance selection Information Sciences, Volume 576, 2021, pp. 105-121 Show abstract Instance selection, as an important data pre-processing task, is widely used in supervised classification. Recently, a series of instance selection algorithms with different techniques have been suggested. Among them, evolutionary algorithms (EAs) have shown competitive performance. However, when the size of instance set is large, these EA-based algorithms may face great challenges on search efficiency and computational cost. To this end, in this paper, a multi-objective evolutionary algorithm based on length reduction, termed as LRIS, is proposed for large-scale instance selection, where a length reduction strategy is suggested to recursively shorten the length of each individual in the population, and improve the computational efficiency of LRIS greatly. Specifically, in the proposed length reduction strategy of LRIS, each gene in the individuals has a probability of being deleted, whose probability is obtained according to the importance of the corresponding instance in the instance set and the importance of the corresponding gene in the population. Then, two evolutionary operators (e.g. crossover and mutation) based on the length reduction strategy are developed to generate offspring population from the reduced population. In addition, an individual repairing operator is also designed to repair the length of over-reduced individuals. Experimental results on 12 large-scale data sets have demonstrated the efficiency and the effectiveness of the proposed LRIS in comparison with the state-of-the-art EA-based instance selection algorithms. Research article Learning risk-mediated traversability maps in unstructured terrains navigation through robot-oriented models Information Sciences, Volume 576, 2021, pp. 1-23 Show abstract In robotic navigation, safety and efficiency play an important role and must be evaluated together. This paper proposes a simple and efficient method to derive the traversability maps of unstructured environments and the optimal path to be followed to reach target locations based on the specific characteristics of different types of robots available. The optimal solution minimises the path length while maintaining the risk associated with that path below the maximum acceptable upper bound. The ability of each robot to traverse terrains with specific characteristics is formalised and modelled using simple and efficient neural networks and trained in a dynamic simulation environment. The proposed shallow network topology achieves results, in terms of accuracy, that are comparable with other standard classifiers and more complex deep networks. Applying this procedure to different robotic structures, the best system within the team (wheeled, legged, and hybrid) can be selected to accomplish a specific assigned task. The proposed strategy, together with the obtained simulation results is presented, carefully analysed, and then compared using real-life simulated scenarios. Research article TPDE: A tri-population differential evolution based on zonal-constraint stepped division mechanism and multiple adaptive guided mutation strategies Information Sciences, Volume 575, 2021, pp. 22-40 Show abstract Differential evolution (DE) has been recognized as one of the most effective algorithms for solving numerical optimization problems. In this paper, we propose a tri-population differential evolution (TPDE) to further enhance the search capability of DE. More specifically, the parent population is partitioned into three sub-populations with different emphasises at each iteration based on a newly proposed zonal-constraint stepped division (ZSD) mechanism, which determines the size of each sub-population according to not only individual’s fitness value but also the evolutionary process. To make the best of information provided by elite individuals and play their leading role on other individuals, three elite-guided mutation strategies are presented for each sub-population. Moreover, three sets of adaptive control parameters including the scale factor F and crossover rate CR are configured for three mutations according to Gaussian distribution model, Cauchy distribution model and a triangular wave function respectively. The design of mutation strategies and control parameters for each sub-population is based on the principle of balancing the global exploration and local exploitation capabilities. To evaluate the performance of TPDE, comparative experiments are conducted based on 59 benchmark functions from CEC2014 and CEC2017 test suites. The results indicate that the proposed TPDE is significantly better than, or at least comparable to the recent nine state-of-the-art DE variants. Research article Hyperplane-driven and projection-assisted search for solving many-objective optimization problems Information Sciences, Volume 574, 2021, pp. 394-412 Show abstract Counterpoising convergence and distribution becomes more intractable in many-objective optimization where the number of objectives exceeds three, evolutionary algorithms (EAs) via decomposition are prominent in convergence promotion yet suffer from diversity loss. The setting of direction vectors (DVs), scalarizing function (SF) and selection strategy can significantly affect the performance of this sort of algorithms. To remedy the issue, we develop a hyperplane driven and projection assisted EA, referred here as HPEA, using three-stage search. At the very beginning, search is performed only along the extreme objective-wise points to capture the corners of Pareto front (PF). After that, the convergence and diversity are coordinated, a set of DVs, adapted by the evolving population itself, is utilized to extend the search wideness, and two novel SFs are exploited to collect elites in each subregion for approaching a more complete PF. At last, diversity is emphasized, a projection distance aided elimination mechanism is employed to prune poorly diversified solutions one by one. Note that hyperplane utilized at second stage aims at identifying well-converged solutions, the rationale behind using two novel SFs is to take complementary effects of different criteria. The resultant HPEA is compared with several state-of-the-art multiobjective EAs on handling various types of many-objective problems. Extensive empirical studies demonstrate the effectiveness and competitiveness of the proposal in obtaining high quality solution set. Research article A generalized approach to solve perfect Bayesian Nash equilibrium for practical network attack and defense Information Sciences, Volume 577, 2021, pp. 245-264 Show abstract To address the incomplete information dynamic network attack and defense game in practice, this paper proposes a generalized approach to solve for perfect Bayesian Nash equilibrium (BNE) for practical network attack and defense. To consider “role-shifting” in the practical network attack and defense environment, the proposed approach substitutes solving the Nash equilibrium (NE) problem with a payoff (reward) maximization problem via a profound combination between the subgame perfect NE of the complete information dynamic game and the BNE of the incomplete information static game. Furthermore, to evaluate the effectiveness of the proposed approach, a representative signaling game with specific values is examined from a theoretical perspective. Finally, a real penetration test case targeting a web server is implemented to substantiate the effectiveness of the proposed approach from a practical perspective with some visual verifications and crucial penetration codes, where the attacker successfully obtains the ROOT authority (the highest authority) of the target web server. Research article Distributed Cooperative Control for Multiple Heterogeneous Euler-Lagrangian Systems Under Global Equality and Inequality Constraints Information Sciences, 2021 Show abstract In this paper, we study the distributed optimization problem with globally equality and inequality constraints for a multi-agent system, where each agent is modeled by Euler-Lagrangian (EL) dynamics. The optimized function can be represented by the sum of all local cost functions corresponding to each individual agent. Two continuous-time algorithms are proposed to solve such the problem in a distributed manner. In virtue of geometric graph theory, convex analysis and Lyapunov stability theory, it is proved that all agents achieve consensus on the Lagrangian multipliers associated with constraints while the proposed algorithms converge exponentially to the optimal solution of the problem given in the case that the parameters of EL agents are known, and converge asymptotically to the optimal solution of the problem in the case that the parameters of EL agents are unknown, respectively. Finally, an example is provided to demonstrate the effectiveness of the theoretical results. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.089", "PubYear": 2021, "Volume": "576", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing Signal Processing of Ministry of Education, School of Artifical Intelligence, Anhui University, Hefei 230601, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing Signal Processing of Ministry of Education, School of Artifical Intelligence, Anhui University, Hefei 230601, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing Signal Processing of Ministry of Education, School of Artifical Intelligence, Anhui University, Hefei 230601, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shenzhen Key Laboratory of Computational Intelligence, Department of Computer Science and Engineering, Southern University of Science and Technology, Shenzhen 518055, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Computing Signal Processing of Ministry of Education, School of Artifical Intelligence, Anhui University, Hefei 230601, China"}], "References": [{"Title": "A novel graph clustering method with a greedy heuristic search algorithm for mining protein complexes from dynamic and static PPI networks", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "522", "Issue": "", "Page": "275", "JournalTitle": "Information Sciences"}, {"Title": "A community detection algorithm based on graph compression for large-scale social networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "551", "Issue": "", "Page": "358", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 89245616, "Title": "New bounds on guarding problems for orthogonal polygons in the plane using vertex guards with halfplane vision", "Abstract": "Given a set F of k disjoint monotone orthogonal polygons with a total of m vertices, we present bounds on the number of vertex guards required to guard the free space and the boundaries of the polygons in F when the range of vision of each guard is bounded by 180<sup>∘</sup> (the region in front of the guard). When the orthogonal polygons are axis aligned we prove that m 2 + ⌊ k 4 ⌋ + 4 vertex guards are always sufficient. When the orthogonal polygons are arbitrary oriented, we show that m 2 + k + 1 vertex guards are sometimes necessary and conjecture the bound is tight.", "Keywords": "Art gallery problem ; Guarding ; Orthogonal polygon ; Monotone polygon ; Visibility", "DOI": "10.1016/j.tcs.2021.06.012", "PubYear": 2021, "Volume": "882", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Texas at Dallas, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "The University of Texas at Dallas, USA;Corresponding author"}], "References": []}, {"ArticleId": 89245623, "Title": "Clustering-driven Deep Adversarial Hashing for scalable unsupervised cross-modal retrieval", "Abstract": "With the advent of the big data era, multimedia data is growing rapidly, and its data modalities is also becoming diversified. Therefore, the demand for the speed and accuracy of cross-modal information retrieval is increasing. Hashing-based cross-modal retrieval technology attracts widespread attention, it encodes multimedia data into a common binary hash space, thereby effectively measuring the correlation between samples from different modalities. In this paper, we propose a novel end-to-end deep cross-modal retrieval framework, namely Clustering-driven Deep Adversarial Hashing (CDAH), which has three main characteristics. Firstly, CDAH learns discriminative clusters recursively through a soft clustering model. It attempts to generate modal-invariant representations in a common space by obfuscating the modality classifier, which tries to distinguish different modalities according to the generated representations. Secondly, in order to minimize the modal gap between feature representations from different modalities with the same semantic label, and to maximize the distance between images and texts with different labels, CDAH constructs a fused-semantics matrix to integrate the original domain information from different modalities, serving as self-supervised information to refine the binary codes. Finally, CDAH skillfully uses a scaled tanh function to adaptively learn the binary codes, which will gradually converge to the original tricky binary coding problem. We conduct comprehensive experiments on four popular datasets, and the experimental results demonstrate the superiority of our model against the state-of-the-art methods.", "Keywords": "Cross-modal retrieval ; Hashing methods ; Semantic similarity representation ; Clustering algorithms", "DOI": "10.1016/j.neucom.2021.06.087", "PubYear": 2021, "Volume": "459", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, China;Corresponding author"}, {"AuthorId": 3, "Name": "Lunbo Li", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Harbin Institute of Technology, Shenzhen 518055, China"}, {"AuthorId": 5, "Name": "De<PERSON> Chen", "Affiliation": "School of Computer Science and Technology, Huaibei Normal University, Huaibei 235000, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Inception Institute of Artificial Intelligence, Abu Dhabi, United Arab Emirates"}], "References": [{"Title": "Weakly-supervised Semantic Guided Hashing for Social Image Retrieval", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "8-9", "Page": "2265", "JournalTitle": "International Journal of Computer Vision"}]}, {"ArticleId": 89245646, "Title": "A three-way decision method based on fuzzy rough set models under incomplete environments", "Abstract": "The paper primarily explores the applicability of three-way decision (TWD) to multi-attribute decision-making (MADM), and establishes a new three-way multi-attribute decision-making (TW-MADM) method under an incomplete environment. For the sake of making rational decisions for MADM problems with fuzzy values, fuzzy rough set models are first utilized to investigate a new TWD model. By taking into account the hesitation degree of each evaluation value, a data-driven method to determine the relative loss functions is presented. Moreover, a new conditional probability calculation method is put forth via the information granularity of each object. In light of the above statement, a novel TWD model with three strategies is proposed. Afterwards, the arithmetic mean method is adopted for patching the lost data to effectively address incomplete MADM problems. Given the uncertainty of the patched data and real data, a new TW-MADM method as well as a corresponding MADM algorithm is designed. By several comparative analysis and experimental analysis, the feasibility, effectiveness, superiority and stability of the method are demonstrated. In addition, the results show that the presented method with optimistic strategies is more viable and stable than the method with compromise and pessimistic strategies. Introduction Different from deterministic decisions, i.e., acceptances and rejections, three-way decision (TWD) adds a non-deterministic decision with delayed options, which ensures conformance with practical decision-making scenarios. For instance, a doctor can make the following three decisions based on certain conditions of patients in a multi-attribute decision-making (MADM) problem, i.e., treatment, further observation and no treatment, where further observation is a non-deterministic decision. It is obvious that this decision-making strategy coincides with the idea of TWD. As we know, the core of TWD is to divide a whole into three parts [43], [44]. Recently, TWD has been widely used in the MADM study [14], [20], [22], [20], [22], [24], [32], [47]. The main reasons for successful applications of TWD in MADM include not only TWD can provide a semantic interpretation for decision results, but also TWD [9], [44] can provide a powerful tool for addressing uncertain MADM problems [46]. Up to now, the study of three-way multi-attribute decision-making (TW-MADM) methods has become a hot spot in the decision-making theory. Compared with MADM methods with two-way decision, TW-MADM methods [23], [39] are more favored by scholars due to the following unique merits: (a) TW-MADM methods consider the losses triggered by taking various decision actions for all objects under different states, and the final decision is made based on the Bayesian theory with minimum losses. (b) All objects can be objectively divided into three domains, and the number of objects in each domain can be objectively determined. The above merits of TW-MADM methods are not available for MADM methods with two-way decision. Nevertheless, it is not possible to make a final decision simply by considering the classification of objects in some cases. For instance, there are seven projects and a manager who intends to recommend an optimal project for bidding. It is difficult for the manager to make a decision when there are two projects in the positive domain obtained by a TW-MADM method. Hence, the ranking problem also needs to be considered in numerous decision-making scenarios in light of TW-MADM. However, we can find that the study on ranking objects in the TW-MADM method under incomplete environments is rare. In order to effectively solve realistic incomplete MADM problems, the current paper intends to explore a new TW-MADM method. A typical incomplete MADM issue can be succinctly depicted via an incomplete information system (IIS). In general, there are the following three approaches to deal with IISs [6]: (a) completion, which means that each unknown value can be represented by a concrete value. Specifically, we may substitute the median or the mean of all known values of each attribute for the unknown values on the attribute; (b) object deletion, we delete or ignore objects with unknown values; (c) “best left alone”, which means that we just regard the unknown value as a special symbol and do not care about its specific value. Roughly speaking, the unknown value has two different types [30], that is, the lost value and the absent value. The former one indicates that the original value exists but has been lost; whereas the latter counterpart indicates that the original value does not exist. Moreover, Luo et al. [25] summarized four types of semantics of unknown values. In the current paper, we only focus on the situation that the unknown value is the lost value. We adopt the approach of completion to transform an IIS into an IS. Similar to the study of [41], the arithmetic mean method is presented. Given the uncertainty and imprecision of patched data and real data, we intend to develop a TWD model that can effectively handle the uncertain and fuzzy data. The concept of TWD was first initiated by Yao [42]. After more than a decade of development, TWD has formed a theoretical system and has been applied to many aspects in real life, such as medical diagnosis [2], [40], supplier selections [24], government management [31], and so forth. There are two main research directions in the study of TWD, one is the extension of TWD models; the other is the connotation of TWD models. Considering that equivalence relations are too strict for dealing with continuous fuzzy data, the current paper intends to put forward a new TWD model by using the fuzzy neighborhood operator improved by Ye et al. [45]. Furthermore, we aim to focus on the acquisition of loss functions and conditional probabilities in MADM problems. In the study of some MADM-driven TWD models, the loss functions are directly given in light of the knowledge and experience of experts [19], [21], [20], [22], [23], [47]. For the sake of reducing the subjectivity of loss functions, Jia et al. [14] combined the information of MADM problems to give the calculation method of relative loss functions according to the study of [18], [42]. Jia et al. [14] mainly investigated relative loss functions originated from fuzzy evaluation values. As we know, a fuzzy evaluation value can only describe the membership degree of an object in terms of an attribute, whereas the hesitation degree is indescribable. In order to conform to practical situations that decision-makers own hesitations, Xu [37] presented the notion of intuitionistic fuzzy numbers (IFNs) by extending the form of intuitionistic fuzzy sets (IFSs) [1]. Liu et al. [24] extended the study results of [14] to intuitionistic fuzzy environments and proposed a calculation method of relative loss functions obtained from IFNs. In addition, evaluation values are usually evaluated by real numbers, fuzzy numbers and linguistic variables based on realistic MADM problems. Since it is difficult to directly get data with IFNs from real life, we intend to present a novel relative loss function calculation method by considering the hesitation degree of each evaluation value. In addition, the acquisition of conditional probabilities is also an important research direction. By referring to existing studies [14], [20], [22], [24], [46], the condition probability of each object is either subjectively given or calculated based on information systems (ISs). In decision-making procedures, it should be emphasized that the objective conditional probability is more popular than the subjective conditional probability, because the subjective conditional probability is not convincing enough. For instance, Jia et al. [14] subjectively assumed that the conditional probabilities of different objects are identical, which do not correspond with practical decision-making situations. In general, different objects have different conditional probabilities. However, since there is no such state set in MADM methods with two-way decision, the objective conditional probability is difficult to obtain. For this reason, some scholars put forward corresponding solutions from different perspectives. Zhan et al. [47] gave the set of states by the experience of decision-makers, and presented a calculation conditional probability method based on outranking relations. Liang et al. [20], [22] explored two solutions to describe the state set, and used the TOPSIS method to compute the conditional probability. Liu et al. [24] adopted the same way as Liang et al. [20], [22] to obtain the state set, and calculated the conditional probability by means of the grey relational analysis [36]. In the studies of [20], [22], [24], it is not difficult to find that two states in the state set are the sets with respect to all attributes, which is different from the idea of Yao [42]. Moreover, the information granularity of each object is not considered in the process of calculating conditional probability, and each object is just an independent individual. Based on the study of [24], the current paper intends to present a new conditional probability calculation method by considering the information granularity of each object. In light of the above statement, we conclude the motivations of this paper below: (1) The existing TW-MADM methods for ranking alternatives are almost studied under complete environments, whereas there are few studies on the TW-MADM method with ranking under incomplete environments. For the sake of solving incomplete MADM problems in real world, we first adopt an indirect approach, i.e., completion, to address incomplete MADM problems. Then we propose a TWD model that can effectively handle the uncertain and fuzzy data. (2) In [14], [24], the calculation of relative loss functions does not take into account the hesitation degree of each evaluation value. For this reason, the current paper aims to propose a novel method to compute the relative loss functions by virtue of fuzzy rough set models. (3) In [20], [22], [24], the authors fail to consider the information granularity of each object, and each state is a collection of attributes. For the sake of decreasing the subjectivity of decisions and considering the information granularity of each object, the current paper presents a new conditional probability calculation method. (4) In practice, decision-makers have diverse cognitive bias towards decision results. For the sake of establishing a decision-making method that satisfies the preferences of more decision-makers, we propose a TW-MADM method with three strategies. In what follows, we sum up several major contributions of this paper: • In this paper, several existing research results of TW-MADM methods are extended to an incomplete environment. A novel TW-MADM method is proposed, which provides a new solution and decision support for incomplete MADM problems. • In view of the uncertainty of the patched data, we present a new fuzzy rough set model to convert the attribute values into IFNs, so as to mine more valuable information. By using the hesitation degree of each object, we present an approach to determine the relative loss functions derived from IFNs, which overcomes the shortcomings of some existing studies [14], [24]. • With the support of the study in [24], we put forward the concept of the normalized conditional probability by considering the information granularity of each object, which provides a new idea for the determination of the conditional probability, and also provides a new perspective for the combination of TWD and MADM. The rest of this paper is arranged below. Section 2 recalls several fundamental concepts. Section 3 develops a novel TWD model with three strategies via the proposed relative loss functions and the normalized conditional probability. Section 4 adopts an indirect way, i.e., completion, to handle incomplete MADM problems. After patching the lost data, we propose a new TW-MADM method and design a corresponding MADM algorithm. Sections 5 Comparative analysis based on an illustrative example, 6 Experimental analysis preform comparative and experimental analyses to show the feasibility, effectiveness, superiority and stability of the method. Section 7 concludes major contributions and several future research options. Figures The variation of TWD for different ρ. The classification results for different strategies. The SCCs between different decision-making methods. The ranking results of two fuzzy rough set model-based methods. The classification results of different TW-MADM methods. The classification results of different α. Show all figures Section snippets Preliminaries The fundamental notions of IISs, fuzzy neighborhood operators, IFSs and loss functions are reviewed. Fuzzy rough set-based three-way decision under multi-attribute environments This section proposes a new TWD model in light of MADM problems with fuzzy information. We first define a pair of extended fuzzy rough approximation operators by virtue of the improved fuzzy neighborhood operators. Then, an FIS is transformed into an intuitionistic fuzzy information system (IFIS) to better describe fuzzy data. We present an approach for obtaining the conditional probabilities and the relative loss functions. At last, by considering decision makers’ cognitive bias towards A novel TWD method to MADM problems with incomplete information In real world, there are a lot of incomplete MADM problems in many fields, such as medical fields, finance fields, and so forth. The appearance of incomplete information increases the difficulty of analyzing and solving problems. Facing with this challenge, this paper proposes a TWD method to handle MADM problems under incomplete environments. First, the description of MADM problems with incomplete information is given. Second, we focus on stating the processing of incomplete MADM problems. A Comparative analysis based on an illustrative example According to the presented TW-MADM method, we give an example from UCI to demonstrate the feasibility of the method in Section 5.1. Afterwards, Section 5.2 verifies the effectiveness of the method by comparing with several existing decision-making methods. In light of the comparison results, Section 5.3 discusses the merit of the method. Experimental analysis There are the three parameters ρ , α and δ in the proposed TW-MADM method. In order to demonstrate the impact of the above parameters on the classification and ranking results, sensitivity analysis of each parameter is performed in Section 6.1. Besides, only a pair of dual fuzzy logic operators are used in the previous work. Hence, Section 6.2 analyzes the stability of the method in light of diverse fuzzy logic operators. Conclusions This paper has taken an indirect approach, i.e., completion, to handle incomplete MADM problems in real world. By fusing the fuzzy rough set theory, TWD theory with MADM, we have put forward a new TW-MADM method with three strategies. According to an illustrative example, we have verified the feasibility, effectiveness and stability of the proposed TW-MADM method. Compared with the compromise and pessimistic strategies, the results of comparison and experimental analyses demonstrate that the CRediT authorship contribution statement Jin Ye: Conceptualization, Methodology, Writing - original draft. Jianming Zhan: Conceptualization, Methodology, Writing - original draft. Bingzhen Sun: Writing - review & editing. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments The authors are very thankful to editors and two referees for their suggestive reports and valuable comments which are conducive to enhancing the presentation of the paper. The work was partially supported by grants from the NNSFC (61866011; 11961025; 72071152) and the Graduate Innovative Education Foundation of Hubei Minzu University (MYK2020016). References (49) K. Zhang et al. Fuzzy β -covering based ( I , T ) -fuzzy rough set models and applications to multi-attribute decision-making Comput. Ind. Eng. (2019) J. Ye et al. A novel decision-making approach based on three-way decisions in fuzzy information systems Inf. Sci. (2020) J. Ye et al. A novel fuzzy rough set model with fuzzy neighborhood operators Inf. Sci. (2021) Y.Y. Yao Three-way granular computing, rough set, and formal concept analysis Int. J. Approx. Reason. (2020) Y.Y. Yao Three-way decision and granular computing Int. J. Approx. Reason. (2018) Y.Y. Yao Three-way decisions with probabilistic rough sets Inf. Sci. (2010) L.M. Yao et al. Incomplete interval type-2 fuzzy preference relations based on a multi-criteria group decision-making model for the evaluation of wastewater treatment technologies Measurement (2020) B. Yang et al. Fuzzy neighborhood operators and thier corresponding derived fuzzy coverings Fuzzy Sets Syst. (2019) G.W. Wei Gray relational analysis method for intuitionistic fuzzy multiple attribute decision making Expert Syst. Appl. (2011) T.X. Wang et al. A prospect theory-based three-way decision model Knowl.-Based Syst. (2020) E. Szmidt et al. Distances between intuitionistic fuzzy sets Fuzzy Sets Syst. (2000) B.Z. Sun et al. Three-way decisions approach to multiple attribute group decision making with linguistic information-based decision-theoretic rough fuzzy set Int. J. Approx. Reason. (2018) B.Z. Sun et al. Three-way decision making approach to conflict analysis and resolution using probabilistic rough set over two universes Inf. Sci. (2020) Z. Pawlak Information systems, theoretical foundations Inf. Syst. (1981) S. Opricovic et al. Compromise solution by MCDM methods: A comparative analysis of VIKOR and TOPSIS Eur. J. Oper. Res. (2004) L.W. Ma Two fuzzy covering rough set models and their generalizations over fuzzy lattices Fuzzy Sets Syst. (2016) J.F. Luo et al. On modeling similarity and three-way decision under incomplete information in rough set theory Knowl.-Based Syst. (2020) P.D. Liu et al. A multiple attribute decision making three-way model for intuitionistic fuzzy numbers Int. J. Approx. Reason. (2020) D. Liu et al. A novel three-way decision model based on incomplete information system Knowl.-Based Syst. (2016) D.C. Liang et al. Method for three-way decisions using ideal TOPSIS solutions at Pythagorean fuzzy information Inf. Sci. (2018) D.C. Liang et al. Risk appetite dual hesitant fuzzy three-way decisions with TODIM Inf. Sci. (2020) D.C. Liang et al. Heterogeneous multi-attribute nonadditivity fusion for behavioral three-way decisions in interval type-2 fuzzy environment Inf. Sci. (2019) H.X. Li et al. Risk decision making based on decision-theoretic rough set: a three-way view decision model Int. J. Comput. Intell. Syst. (2011) M. Kryszkiewicz Rough set approach to incomplete information system Inf. Sci. (1998) View more references Cited by (0) Recommended articles (6) Research article Learning risk-mediated traversability maps in unstructured terrains navigation through robot-oriented models Information Sciences, Volume 576, 2021, pp. 1-23 Show abstract In robotic navigation, safety and efficiency play an important role and must be evaluated together. This paper proposes a simple and efficient method to derive the traversability maps of unstructured environments and the optimal path to be followed to reach target locations based on the specific characteristics of different types of robots available. The optimal solution minimises the path length while maintaining the risk associated with that path below the maximum acceptable upper bound. The ability of each robot to traverse terrains with specific characteristics is formalised and modelled using simple and efficient neural networks and trained in a dynamic simulation environment. The proposed shallow network topology achieves results, in terms of accuracy, that are comparable with other standard classifiers and more complex deep networks. Applying this procedure to different robotic structures, the best system within the team (wheeled, legged, and hybrid) can be selected to accomplish a specific assigned task. The proposed strategy, together with the obtained simulation results is presented, carefully analysed, and then compared using real-life simulated scenarios. Research article K -size partial reduct: Positive region optimization for attribute reduction Knowledge-Based Systems, Volume 228, 2021, Article 107253 Show abstract Optimal reduct is one of the challenging problems in rough set theory, and most of the existing algorithms cannot achieve the optimal reduct on high dimensional data sets. To explore an efficient algorithm for the optimal reduct problem, this paper proposes its generalization problem, which is defined as the K -size partial reduct problem. For this type of problem, an inefficient enumeration algorithm is first proposed. Then we enhance the enumeration algorithm through three improvements with the local search algorithm, i.e., fast initial solution construction, generation rules of solution, and dynamic object weighting strategy. The fast initial solution construction dramatically reduces the number of iterations, the generation rules of solution define a reasonable neighborhood structure and an effective candidate solution transfer model, and the dynamic object weighting strategy adjusts the iterative process to guide the algorithm to jump out of the local optimal solution. On the basis of these three improvements, an efficient local search-based K -size partial reduct algorithm is raised. Finally, a K -size partial reduct-based attribute reduction algorithm is designed by using the relationship between optimal reduct and K -size partial reduct. To validate the effectiveness of our proposed algorithms, we implemented a broad range of experimental simulations. The results of the experiments show the superiorities and innovations of the proposed algorithms compared with state-of-the-art algorithms. Research article Robust visual object tracking using context-based spatial variation via multi-feature fusion Information Sciences, Volume 577, 2021, pp. 467-482 Show abstract With the emergence of camera technology, visual tracking has witnessed great attention in the field of computer vision. For instance, numerous discriminative correlation filter (DCF) methods are broadly used in tracking, nevertheless, most of them fail to efficiently find the target in challenging situations which leads to tracking failure throughout the sequences. In order to handle these issues, we propose contextual information based spatial variation with a multi-feature fusion method (CSVMF) for robust object tracking. This work incorporates the contextual information of the target to determine the location of the target accurately, which utilizes the relationship between the target and its surroundings to increase the efficiency of the tracker. In addition, we integrate the spatial variation information which measures the second-order difference of the filter to avoid the over-fitting problem caused by the changes in filter coefficient. Furthermore, we adopt multi-feature fusion strategy to enhance the target appearance by using different metrics. The tracking results from different features are fused by employing peak-to-sidelobe ratio (PSR) which measures the peak strength of the response. Finally, we conduct extensive experiments on TC128, DTB70, [email protected] , and UAV123 datasets to demonstrate that the proposed method achieves a favorable performance over the existing ones. Research article DSAGAN: A generative adversarial network based on dual-stream attention mechanism for anatomical and functional image fusion Information Sciences, Volume 576, 2021, pp. 484-506 Show abstract In recent years, extensive multimodal medical image fusion algorithms have been proposed. However, existing methods are primarily based on specific transformation theories. There are many problems with existing algorithms, such as poor adaptability, low efficiency and blurry details. To address these problems, this paper proposes a generative adversarial network based on dual-stream attention mechanism (DSAGAN) for anatomical and functional image fusion. The dual-stream architecture and multiscale convolutions are utilized to extract deep features. In addition, the attention mechanism is utilized to further enhance the fused features. Then, the fusion images and multimodal input images are put into the discriminator. In the update stage of the discriminator, we expect to judge the multimodal images as real, and to judge the fusion images as fake. Furthermore, the fusion images are expected to be judged as real in the update stage of the generator, forcing the generator to improve the fusion quality. The training process continues until the generator and discriminator reach a Nash equilibrium. After training, the fusion images can be obtained directly after inputting anatomical and functional images. Compared with the reference algorithms, DSAGAN consumes less fusion time and achieves better objective metrics in terms of Q <sub>AG</sub> , Q <sub>EN</sub> and Q <sub>NIQE</sub> . Research article A generalized approach to solve perfect Bayesian Nash equilibrium for practical network attack and defense Information Sciences, Volume 577, 2021, pp. 245-264 Show abstract To address the incomplete information dynamic network attack and defense game in practice, this paper proposes a generalized approach to solve for perfect Bayesian Nash equilibrium (BNE) for practical network attack and defense. To consider “role-shifting” in the practical network attack and defense environment, the proposed approach substitutes solving the Nash equilibrium (NE) problem with a payoff (reward) maximization problem via a profound combination between the subgame perfect NE of the complete information dynamic game and the BNE of the incomplete information static game. Furthermore, to evaluate the effectiveness of the proposed approach, a representative signaling game with specific values is examined from a theoretical perspective. Finally, a real penetration test case targeting a web server is implemented to substantiate the effectiveness of the proposed approach from a practical perspective with some visual verifications and crucial penetration codes, where the attacker successfully obtains the ROOT authority (the highest authority) of the target web server. Research article Distributed cooperative optimization for multiple heterogeneous Euler-Lagrangian systems under global equality and inequality constraints Information Sciences, Volume 577, 2021, pp. 449-466 Show abstract In this paper, we study the distributed cooperative optimization problem with globally equality and inequality constraints for a multi-agent system, where each agent is modeled by Euler-Lagrangian (EL) dynamics. The optimized function can be represented by the sum of all local cost functions corresponding to each individual agent. Two continuous-time algorithms are proposed to solve such the problem in a distributed manner. In virtue of geometric graph theory, convex analysis, and Lyapunov stability theory, it is proved that all agents achieve consensus on the Lagrangian multipliers associated with constraints while the proposed algorithms converge exponentially to the optimal solution of the problem given in the case that the parameters of EL agents are known, and converge asymptotically to the optimal solution of the problem in the case that the parameters of EL agents are unknown, respectively. Finally, an example is provided to demonstrate the effectiveness of the theoretical results. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.088", "PubYear": 2021, "Volume": "577", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Mathematics and Statistics, Hubei Minzu University, Enshi, Hubei 445000, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Hubei Minzu University, Enshi, Hubei 445000, China;Corresponding author"}, {"AuthorId": 3, "Name": "Bingzhen Sun", "Affiliation": "School of Economics and Management, Xidian University, Xi’an, Shaanxi 710071, China"}], "References": [{"Title": "Three-way decision making approach to conflict analysis and resolution using probabilistic rough set over two universes", "Authors": "Bingzhen Sun; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "809", "JournalTitle": "Information Sciences"}, {"Title": "Risk appetite dual hesitant fuzzy three-way decisions with TODIM", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "585", "JournalTitle": "Information Sciences"}, {"Title": "Three-way decisions in fuzzy incomplete information systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "667", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "Preference degree-based multi-granularity sequential three-way group conflict decisions approach to the integration of TCM and Western medicine", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> Sun; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "106393", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "A prospect theory-based three-way decision model", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106129", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "A novel decision-making approach based on three-way decisions in fuzzy information systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "541", "Issue": "", "Page": "362", "JournalTitle": "Information Sciences"}, {"Title": "A novel fuzzy rough set model with fuzzy neighborhood operators", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "266", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 89245666, "Title": "Modeling and Simulation of The UX-6 Fixed-Wing Unmanned Aerial Vehicle", "Abstract": "<p>The flight model plays an important role in the development of the fixed-wing unmanned aerial vehicle (UAV)’s control system. By using the flight model, aircraft motion characteristics could be identified. Therefore, this modeling method is very important in speeding up parameters tuning of autonomous control systems and also to improve safety. The objective of the present research was to build The UX-6 fixed-wing UAV flight models with two approaches. First, the UX-6 UAV model was built analytically by using Datcom + Pro software. Meanwhile, the second model was built empirically based on flight data system identification. Both approaches were compared and analyzed in longitudinal and lateral flight modes. This work observed eight model’s parameters, four in longitudinal modes and four in lateral modes. The parameters comparison demonstrated that five analytical model parameters have similar characteristics with empirical model parameters. Three model parameters had different characteristics, therefore, need to be improved. These parameters comparison results indicate that the model can represent the UX-6 UAV aircraft motion characteristics.</p>", "Keywords": "UAV; Fixed-wing aircraft modeling; Datcom + pro; System Identification; Analytical-empirical comparison", "DOI": "10.1007/s40313-021-00754-5", "PubYear": 2021, "Volume": "32", "Issue": "5", "JournalId": 2642, "JournalTitle": "Journal of Control, Automation and Electrical Systems", "ISSN": "2195-3880", "EISSN": "2195-3899", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Electronics, Faculty of Mathematics and Natural Science, Universitas Gadjah Mada, Yogyakarta, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Electronics, Faculty of Mathematics and Natural Science, Universitas Gadjah Mada, Yogyakarta, Indonesia"}], "References": []}, {"ArticleId": 89245690, "Title": "Permanent Magnet Synchronous Motor Speed Control Based on Improved Active Disturbance Rejection Control", "Abstract": "<p>An improved active disturbance rejection control (I-ADRC) to improve the disturbance attenuation of a permanent magnet synchronous motor speed controller was proposed in this paper. A nonlinear function with improved smoothness was adopted to design the controller. The Lyapunov stability of the improved tracking differentiator, the improved extended state observer, and the controller were analysed. Moreover, simulations and experiments confirmed the effectiveness of the proposed controller. The results demonstrate that the proposed controller has a smaller steady-state error and a stronger disturbance attenuation ability than the proportional integral derivative (PID) controller.</p>", "Keywords": "permanent magnet synchronous motor; improved active disturbance rejection control; proportional integral derivative; stability analysis; speed control permanent magnet synchronous motor ; improved active disturbance rejection control ; proportional integral derivative ; stability analysis ; speed control", "DOI": "10.3390/act10070147", "PubYear": 2021, "Volume": "10", "Issue": "7", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Engineering Research Center of Precision Measurement Technology and Instruments, Beijing University of Technology, No. 100, Pingleyuan, Chaoyang District, Beijing 100124, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Beijing Engineering Research Center of Precision Measurement Technology and Instruments, Beijing University of Technology, No. 100, Pingleyuan, Chaoyang District, Beijing 100124, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Engineering Research Center of Precision Measurement Technology and Instruments, Beijing University of Technology, No. 100, Pingleyuan, Chaoyang District, Beijing 100124, China ↑ Author to whom correspondence should be addressed. Academic Editor: <PERSON><PERSON><PERSON><PERSON>"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Beijing Engineering Research Center of Precision Measurement Technology and Instruments, Beijing University of Technology, No. 100, Pingleyuan, Chaoyang District, Beijing 100124, China"}], "References": []}, {"ArticleId": 89245700, "Title": "Matrix approach to spanning matroids of rough sets and its application to attribute reduction", "Abstract": "Rough set theory is a granular computing tool used to deal with ambiguity and uncertainty in information systems. However, many optimization problems in rough sets, such as attribute reduction, are NP-hard problems, and most of the algorithms for these problems are greedy. As a complex mathematical structure, matroids provide a powerful tool for solving combinatorial optimization problems related to attribute reduction. Therefore, it is necessary to study the combination of matroids and rough sets. This paper uses rough sets and matrix approaches to spanning matroids. Moreover, the features of spanning matroids are applied to attribute reduction in decision information systems. Firstly, we construct spanning sets based on equivalence relations which can induce matroids called spanning matroids. Secondly, some features of spanning matroids, such as closed sets, bases, are studied by matrix method. Finally, the judgment theorems with the features of spanning matroids are proposed for addressing the problems about attribute reduction in decision information systems. Simultaneously, the sufficient and necessary conditions for distinguishing upper approximation reduction in inconsistent decision information systems are obtained from the viewpoint of spanning matroids.", "Keywords": "Rough set ; Matrix ; Matroid ; Attribute reduction ; Decision information system", "DOI": "10.1016/j.tcs.2021.06.037", "PubYear": 2021, "Volume": "893", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, Beijing Normal University, Laboratory of Mathematics and Complex Systems, Ministry of Education, Beijing, 100875, China;Corresponding author"}, {"AuthorId": 2, "Name": "Fusheng Yu", "Affiliation": "School of Mathematical Sciences, Beijing Normal University, Laboratory of Mathematics and Complex Systems, Ministry of Education, Beijing, 100875, China"}], "References": [{"Title": "Granular matrix-based knowledge reductions of formal fuzzy contexts", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "3", "Page": "643", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}]}, {"ArticleId": 89246067, "Title": "ON THE USE OF THE CENTRAL DIFFERENCE SCHEME FOR SOLVING THE PROBLEM OF GAS DYNAMICS", "Abstract": "The paper proposes a method for solving the problem of gas dynamics, implemented on the basis of a central difference scheme, the stability of which is achieved by performing a correction of the calcu-lated flows. It is shown that when solving the problem of discontinuity decay, the proposed method is stable, comparable in accuracy with the <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> methods and surpasses them in performance.", "Keywords": "", "DOI": "10.22250/isu.2021.68.17-22", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Potapov Computer center of the FEB RAS"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "P.S.Timosh Computer center of the FEB RAS"}], "References": []}, {"ArticleId": 89246068, "Title": "FINDING DEFECTS IN AUTOMATIC CONTROL SYSTEMS BASED ON STRUCTURE SENSITIVITY FUNCTIONS AND ANALYSIS OF INTEGRAL ESTIMATES OF OUTPUT SIGNALS", "Abstract": "The paper presents an algorithm for defects screening in a continuous dynamic system with a depth of up to dynamic block based on the structure sensitivity function and the sign analysis of integral esti-mates of the output signals deviations.", "Keywords": "", "DOI": "10.22250/isu.2021.68.47-55", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Pacific State University"}, {"AuthorId": 2, "Name": "S.S. <PERSON>v", "Affiliation": "S.S<PERSON> S<PERSON>v Pacific State University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> Pacific State University"}], "References": []}, {"ArticleId": 89246069, "Title": "DEVELOPMENT AND RESEARCH OF A NEURAL NETWORK AUTOTUNING UNIT FOR A PID CONTROLLER OF THE ENERGY FACILITIES AUTOMATED PROCESS CONTROL SYSTEM", "Abstract": "An adaptive control system based on a neural network autotuning unit has been developed. A method for training a neural network for an autotuning block has been examined. A comparison between a control system with a PID-controller and a control system with a PID controller and an autotuning unit has been made.", "Keywords": "", "DOI": "10.22250/isu.2021.68.71-83", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> National Research University \""}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "MEI»"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON><PERSON> National Research University \"MEI»"}], "References": []}, {"ArticleId": 89246070, "Title": "BUILDING THE ICT-ARCHITECTURE OF A SMART CITY", "Abstract": "The paper examines the role of information and communication technologies in the development of smart cities, it shows the importance of supporting the consistency and functional compatibility of smart city devices and services through ICT architecture. A version of the multi-level ICT architecture of a smart city, based on the analysis of various architectures, is proposed, which describes in detail the connection and interaction of individual elements of the city digital infrastructure, as well as the impact which technical and regulatory requirements have on them.", "Keywords": "", "DOI": "10.22250/isu.2021.68.98-108", "PubYear": 2021, "Volume": "", "Issue": "2", "JournalId": 37421, "JournalTitle": "Informatika i sistemy upravleniya", "ISSN": "1814-2400", "EISSN": "1814-2419", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON><PERSON><PERSON> State University"}], "References": []}, {"ArticleId": 89246080, "Title": "An Improved Hybrid Algorithm for Optimizing the Parameters of Hidden Markov Models", "Abstract": "<p>Hidden Markov Models (HMMs) have become increasingly popular in the last several years due to the fact that, the models are very rich in mathematical structure and hence can form the theoretical basis for use in a wide range of applications. Various algorithms have been proposed in literature for optimizing the parameters of these models to make them applicable in real-life. However, the performance of these algorithms has remained computationally challenging largely due to slow/premature convergence and their sensitivity to preliminary estimates. In this paper, a hybrid algorithm comprising the Particle Swarm Optimization (PSO), Baum-Welch (BW), and Genetic Algorithms (GA) is proposed and implemented for optimizing the parameters of HMMs. The algorithm not only overcomes the shortcomings of the slow convergence speed of the PSO but also helps the BW escape from local optimal solution whilst improving the performance of GA despite the increase in the search space. Detailed experimental results demonstrates the effectiveness of our proposed approach when compared to other techniques available in literature.</p>", "Keywords": "Hidden Markov Models;training;hybrid;genetic algorithm;particle swarm optimization", "DOI": "10.9734/ajrcos/2021/v10i130235", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 63540, "JournalTitle": "Asian Journal of Research in Computer Science", "ISSN": "", "EISSN": "2581-8260", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science, Tamale Technical University, Tamale, Ghana"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Science, C. K. <PERSON>am University of Technology and Applied Science, Navrongo, Ghana"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Cyber Security and Computer Engineering Technology, C. K. Tedam University of Technology and Applied Science, Navrongo, Ghana"}], "References": []}, {"ArticleId": 89246087, "Title": "Cognitive Radio and Dynamic TDMA for efficient UAVs swarm communications", "Abstract": "In the last decades, under technological progress in electronic and avionics systems, Unmanned Aerial Vehicles (UAVs) have known an increasing use in several military and civilian missions. Multiple UAVs can cooperatively carry out dangerous applications for human operators or missions where human intervention is not needed, such as surveillance and monitoring of areas of interest and physical infrastructures. Due to the lack of proper communication rules and standards, communication remains one of the most crucial design issues for UAVs swarm. Furthermore, UAVs generally operate in frequency bands shared with other users. Hence, these spectral bands become overcrowded and UAVs may face a spectrum scarcity issue. To address the above challenges, it is essential to define a reliable communication architecture for multiple UAVs coordination and efficient bandwidth sharing. A promising technology that can satisfy the spectrum requirements of the emerging UAVs Networks is Cognitive Radio Network (CRN). The goal is to opportunistically use spectral bands with minimum interference to other users or applications, i.e., primary users. In this paper, we consider the problem of spectrum scarcity encountered by UAVs. We present a centralized CRN-based communication approach for UAVs-Ground Control Station (GCS) communication. The GCS is used as a central coordinator to handle bandwidth usage for the UAVs swarm in its coverage zone. It selects, allocates, and shares available frequencies using CRN and Software Defined Radio (SDR). To share the available CRN-frequency between the different UAVs in its scope, the GCS uses dynamic Time Division Multiple Access (TDMA) technique. The performance of the proposed communication approach is evaluated in a surveillance context in terms of the total time required to transfer UAVs data, the total number of packets sent, and the achieved throughput.", "Keywords": "Unmanned Aerial Vehicles Swarm ; Communication ; Cognitive Radio ; TDMA ; Spectrum sharing", "DOI": "10.1016/j.comnet.2021.108264", "PubYear": 2021, "Volume": "196", "Issue": "", "JournalId": 4823, "JournalTitle": "Computer Networks", "ISSN": "1389-1286", "EISSN": "1872-7069", "Authors": [{"AuthorId": 1, "Name": "Haifa Touati", "Affiliation": "<PERSON><PERSON> IResCoMath Research Laboratory, University of Gabes, Tunisia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> IResCoMath Research Laboratory, University of Gabes, Tunisia;National School of Computer Science (ENSI), Tunisia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Technology of Troyes (UTT), France"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CRISTAL Laboratory, University of Manouba, Tunisia;National School of Computer Science (ENSI), Tunisia"}], "References": [{"Title": "Implementation and analysis of MultiCode MultiCarrier Code Division Multiple Access (MC–MC CDMA) in IEEE 802.11ah for UAV Swarm communication", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "42", "Issue": "", "Page": "101159", "JournalTitle": "Physical Communication"}, {"Title": "Communication and networking technologies for UAVs: A survey", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "168", "Issue": "", "Page": "102739", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 89246295, "Title": "Designing 2D and 3D Non-Orthogonal Frame Fields", "Abstract": "We present a method for direction field design on surface and volumetric meshes supporting non-orthogonality. Our approach is a generalization of the representation of 3D cross fields in spherical harmonic basis. As such it induces a geometrically meaningful measure of smoothness, allows orthogonality control by a simple parameter and enables orientation constraints of a single direction. To the best of our knowledge this is the first work to propose non-orthogonal 3D frame field design. We demonstrate the applicability of our method to generate anisotropic quadrangular and hexahedral meshes which are particularly useful for remeshing CAD models.", "Keywords": "Geometry processing ; Vector field design ; Quadrilateral remeshing ; Hexahedral remeshing", "DOI": "10.1016/j.cad.2021.103081", "PubYear": 2021, "Volume": "139", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA, F-54000 Nancy, France;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA, F-54000 Nancy, France"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA, F-54000 Nancy, France"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA, F-54000 Nancy, France"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Université de Lorraine, CNRS, Inria, LORIA, F-54000 Nancy, France"}], "References": [{"Title": "Algebraic Representations for Volumetric Frame Fields", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "2", "Page": "1", "JournalTitle": "ACM Transactions on Graphics"}]}, {"ArticleId": 89246296, "Title": "A generalized approach to solve perfect Bayesian Nash equilibrium for practical network attack and defense", "Abstract": "To address the incomplete information dynamic network attack and defense game in practice, this paper proposes a generalized approach to solve for perfect Bayesian Nash equilibrium (BNE) for practical network attack and defense. To consider “role-shifting” in the practical network attack and defense environment, the proposed approach substitutes solving the Nash equilibrium (NE) problem with a payoff (reward) maximization problem via a profound combination between the subgame perfect NE of the complete information dynamic game and the BNE of the incomplete information static game. Furthermore, to evaluate the effectiveness of the proposed approach, a representative signaling game with specific values is examined from a theoretical perspective. Finally, a real penetration test case targeting a web server is implemented to substantiate the effectiveness of the proposed approach from a practical perspective with some visual verifications and crucial penetration codes, where the attacker successfully obtains the ROOT authority (the highest authority) of the target web server. Introduction For the first time, in the “2018 Global Risk Report” [4], cyber attacks were included among the top five global security risks, becoming the third-largest risk after extreme weather events and natural disasters. Particularly, with the progressive evolution of networks and related technologies[17], security incidents have experienced a considerable surge in recent years; for example, an email information leak affecting of 3 billion Yahoo users, “Wannacry” ransomware, and a range of advanced persistent threat (APT) attacks. In the confrontation between an attacker and a defender, who are always rational, one side strives to maximize its payoffs while considering the action and strategy of the other side. That is, the attacker is devoted to damaging the network system or stealing the target information resource to the best of its ability; in contrast, the defender strives to prevent these outcomes. In brief, the interactions between the two sides in network attack and defense precisely correspond to game theory. Inspired by this fact, this paper employs game theory to construct a confrontation framework for practical network attack and defense. Specifically, the strategies adopted by the attacker and defender play a critical role in the various security incidents that focus on the target network’s information assets. Generally, before taking concrete actions, the attacker and defender formulate several strategies. Nonetheless, the posterior decider has no idea what specific strategy will be adopted by the prior decider. Thus, it is worth and meaningful for both sides in the attack and defense game to clearly describe the strategies and to deduce the game outcomes by exploiting these descriptions before taking practical actions. Since game theory is the most appropriate tool and method to construct the network attack and defense framework, game theory has been popular in the cyberspace security domain, and many achievements have been gained in various application scenarios [13], [19], such as quantum bit commitment [40], smart contracts [41], and shadowed sets [42]. In [25], a new model for emergent evacuation with assailants (called EEA-SIS) based on susceptible-infected-susceptible model (SIS) is proposed to fill the gap in the strategy evolution of panicked pedestrians in emergent evacuation with assailants. A game-theoretic analysis consisting of two stages is presented in [35], which could provide the optimal security detection strategies for heterogeneous networked systems. A combination of Markov chain models and game theory is presented to assess the security state of domain name system (DNS) servers, whose accuracy in modeling the behavior of attackers on DNS servers can be tested via the simulation results [2]. By modeling the decision-making problem against attacks in a heterogeneous honeynet, [26] proposes a dynamic node-evolution model by considering the inherent characteristics and the functional interaction between ordinary nodes and honeypots. Coresident attacks and corresponding defense strategies are analyzed in [23] via a signaling game model, the solutions to which provide optimal defense strategies for the virtual machine (VM) monitor regarding the expected number of malicious VMs in collaboration. From the perspective of identifying the best defense strategy for techniques such as intrusion detection systems (IDSs), honeypots, and firewalls, a game-theoretic model GTM-CSec is proposed to handle the low-security characteristics in the cloud computing domain [10]. In [3], a novel semisupervised learning framework is presented, which integrates both survival analysis and game theory to address how to predict the existence of links at any point in the future. Despite the aforementioned achievements, the majority of these works emphasize theoretical analyses, while fewer of them directly engage with practical attack and defense applications. Unfortunately, in practical scenarios, there are always various information limitations facing players in the attack and defense game. Moreover, the confrontation processes of attackers and defenders adopting different response strategies are evolving and time-varying, which leads to continually dynamic strategies for both sides with incomplete information [18]. Nevertheless, due to the complexity of practical attack and defense scenarios and to the sensibility of attack strategies, most of works in this literature emphasize analyses and the choice of defense strategies. For example, a dynamic game framework can be used to model a long-term interaction between a stealthy attacker and a proactive defender by capturing stealthy and deceptive behaviors in a multistage game of incomplete information [12]. By solving for the Bayesian Nash equilibrium (BNE) of a mixed-strategy Bayesian attack-defense game with incomplete information, researchers have been able to develop a unified methodology that quantitatively and automatically models and analyzes cyber-physical attacks on industrial control systems [21]. To address the problems concerning the situation in which cooperative nodes have incomplete information regarding other neighboring nodes, [7] presents a cooperative authentication model based on an evolutionary game. To analyze false data injection attacks under incomplete information, a dynamic Bayesian game-theoretic approach is presented according to a proposed bilevel optimization model [32]. By employing incentives and pricing rules on the users of a network, in [6], a Bayesian game theory-based solution is developed to address the challenging repercussions of DDoS attacks. According to [1], [24], the Nash equilibrium (NE) termed as noncooperative game equilibrium, possesses comprehensive applications, such as a network security strategy, Bitcoin mining pool, and moving target defense [9], [16]. Additionally, how to solve for the NE has attracted increasing attention from researchers in the academic and engineering sectors [39]. With respect to limited resources and ability, [35] furnishes effective attack and defense strategies to both sides and presents a method to inspect and calculate the NE. In [15], the authors argue that if there are sufficiently many potential attackers, the game has a unique Nash equilibrium. The terrorist threat value during a firearms assault event can then be derived from a mixed strategy Nash equilibrium in [34]. Furthermore, the constrained relaxation problem is presented, and the corresponding solution is provided for computational complexity. To solve the problem of distributed NE computation in two-network zero-sum games, based on a sequential communication strategy, a novel incremental algorithm is developed to compute the NE [30]. A numerical method to describe the solution set of a generalized Nash equilibrium problem (GNEP) is proposed in [22] by extending the previous result on how to reformulate the GNEP as a family of parametric variational inequalities in the special case. In addition, concerning the problem of distributed Nash equilibrium computation in aggregative games, [29] propose a novel distributed algorithm with an event-triggered mechanism, where the communication between any two agents is only carried out when an edge-based event condition is triggered. As discussed above, game theory has enjoyed remarkable and widespread use in the cybersecurity area, especially in the attack and defense field. Regarding network attack and defense in practice, dynamic strategy selection with incomplete information is always a hot research topic and focus in this domain. In addition, increasing attention has been devoted to solving for the perfect BNE in academia and engineering, along with some generated feasible solving methods. Specifically, the drawbacks of existing methods mainly lie in the following three situations. 1. Most of them emphasize model construction and defense strategy determination for a specific application from a theoretical perspective, while fewer works directly focus on game relationships for practical network attack and defense games in a generalized form [12], [22], [29]. 2. The actual attack and defense process progressively varies with the attack and defense actions and hence possesses unique characteristics not present in other contexts. In addition, the existing matrix game-based methods lack a complete description of the practical network attack and defense game, which means that existing approaches fail to satisfy the security demands, not to mention the complex network attack and defense game with incomplete information and dynamic strategy. 3. With the evolution of attack and defense techniques, a new situation has arisen in recent years, which could be termed “role-shifting”. That is, with the comprehensive spread and deep penetration of attack and defense operations, the roles of attacker and defender could shift with one another at the right time when satisfying the right prerequisites. In this new phase, the original attacker must become the defender since the original defender shifts to being the attacker to counterattack the original attacker to maximize the original defender’s rewards in the overall attack and defense game. This phenomenon makes the practical network attack and defense game more intricate and sophisticated. Therefore, in this paper, the main contribution is to remedy the defects of existing methods from a practical perspective and quantify the complex network attack and defense game to cover the “role-shifting” case. By profoundly combining the subgame perfect NE of the complete information dynamic game and the BNE of the incomplete information static game, a generalized approach to solve for the perfect BNE for practical network attack and defense is proposed to address the incomplete information dynamic network attack and defense game. Moreover, to evaluate the effectiveness of the proposed approach, a representative signaling game with specific values is tested theoretically. Finally, a real penetration test case targeting a web server is implemented to substantiate the effectiveness of the proposed approach from a practical perspective with some visual verifications and crucial penetration codes, where the attacker successfully obtains the ROOT authority (the highest authority) of the target web server. The remainder of this paper is organized into five sections. In Section 2, the credibility and noncredibility of the NE are discussed, as are the definition of subgame and subgame perfect NE under the complete and perfect information dynamic attack and defense game. A generalized approach to solve for the perfect BNE for practical network attack and defense is proposed in Section 3 alongside design concepts and theoretical analyses for the effectiveness of the proposed method. In Section 4, a specific signaling game with specific values is tested from the theoretical perspective to evaluate the effectiveness of this proposed approach. From a practical perspective, a real penetration test case targeting a web server is implemented to substantiate the effectiveness of the proposed approach in Section 5. Finally, conclusions are drawn in Section 6 covering possible future work. In addition, a summary of notations and some crucial penetration codes are displayed in the Appendix. Before ending this section, the main contributions of this paper are highlighted: 1. In this paper, a generalized approach to solve for the perfect BNE for practical network attack and defense is proposed via a profound combination between the subgame perfect NE of a complete information dynamic game and the BNE of an incomplete information static game. This proposed method provides an effective and practical way to solve dynamic network attack and defense games with incomplete information. 2. A representative example with specific values of a signaling game and a real penetration test case targeting a web server are conducted to evaluate and substantiate the effectiveness of the proposed approach theoretically and practically, respectively. In particular, some real visual verifications and crucial penetration codes are presented in the real penetration test case, where the attacker successfully obtains the ROOT authority (the highest authority) of the target web server. 3. To the best of our knowledge, this is the first work to take the “role-shifting” situation into consideration during the model construction process for incomplete information dynamic network attack and defense games. 4. The proposed approach converts solving the NE problem into a maximizing payoff (reward) problem, which could be seen as an optimization problem in game theory and provides new ideas for solving the NE problem from the perspective of optimization. Figures Different representations to the proof. (a) Game tree representation. (b) Double matrix game representation. Subgames evolving downward from node 2 in the game tree portrayed in Fig. 1(a). (a) left subgame. (b) right subgame. The game tree of a specific signaling game. The network topology of the target web server. The game tree of the target web server with the rewards. The screenshot of collecting information. Show all figures Section snippets Credibility and noncredibility of the NE Theorem 1 In terms of complete and perfect information, the central problem of the dynamic game is credible. However, analyzing the dynamic game using static game approaches leads to the noncredibility of some NEs. Proof Assume that the target network has a defender (player 1) and an attacker (player 2). Furthermore, define that the strategy set is S 1 = { S 11 , S 12 } for the former which has priority in choosing strategy, while the latter’s strategy set is S 2 = ( S 21 , S 21 ) , ( S 21 , S 22 ) , ( S 22 , S 21 ) , ( S 22 , S 22 ) , Design concept Considering the complete information dynamic attack and defense game, a subgame perfect NE is the NE not only of the original game but also of each subgame that can be solved for via backward induction to eliminate those that are not credible. An incomplete information static attack and defense game can be converted into a complete but imperfect game by Harsanyi transformation. Straightforwardly, it seems that the incomplete information dynamic attack and defense game is a combination of these Evaluation via the signaling game In this section, a specific example of network attack and defense is conducted to evaluate the proposed approach (4), specifically utilizing a representative game form. A signaling game is a kind of incomplete information dynamic game that is simple but possesses comprehensive significance for application. In this regard, there are two players: player 1 is termed the signal sender with private type information, while player 2 is referred to as the signal receiver with public type information, Substantiation via a real penetration test case targeting a web server Since network attack and defense is a domain emphasizing practical application and has attracted growing attention [5], [11], [28], [31], [37], it is worth conducting a more practical application experiment than the aforementioned theoretical example. To further substantiate the effectiveness of the proposed approach (4), in this section, a real penetration test case targeting a web server is implemented by applying the proposed approach (4) to practical network attack and defense. Conclusions This paper, began by presenting a review of the existing research results in addressing the incomplete information dynamic game for network attack and defense. Furthermore, a generalized approach to solving for the perfect BNE in practical network attack and defense is proposed, in which the problem of solving for NE is transformed into an optimization problem in game theory. To the best of our knowledge, this is the first work to consider “role-shifting” when modeling dynamic network attack CRediT authorship contribution statement Liang Liu: Resources, Writing - review & editing, Methodology, Software, Investigation, Data curation. Lei Zhang: Resources, Writing - review & editing, Methodology, Software, Investigation, Data curation. Shan Liao: Writing - original draft, Conceptualization, Validation, Methodology, Investigation, Data Curation, Formal analysis, Visualization. Jiayong Liu: Writing - review & editing, Methodology. Zhenxue Wang: Writing - review & editing, Methodology. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work was supported in part by the National Key Research and Development Program of China under Grant 2017YFE0118900, in part by the Key Research and Development Program of Sichuan province under Grant 2020YFG0076, in part by the Sichuan Science and Technology Program under Grant 2021YFG0159, in part by the Fundamental Research Funds for the Central Universities. Besides, kindly note that Liang Liu and Lei Zhang are jointly of the first authorship. References (42) Y. Zhang et al. Game theoretic approach to shadowed sets: A three-way tradeoff perspective Inf. Sci. (2020) L. Zhang et al. A game-theoretic method based on Q-learning to invalidate criminal smart contracts Inf. Sci. (2019) L. Zhou et al. Game theoretic security of quantum bit commitment Inf. Sci. (2019) H. Wu et al. Game theoretical security detection strategy for networked systems Inf. Sci. (2018) C.-K. Wu A game theory approach for risk analysis and security force deployment against multiple coordinated attacks Environ. Res. (2021) F. Song et al. Smart collaborative distribution for privacy enhancement in moving target defense Inf. Sci. (2019) C. Shi et al. Nash equilibrium computation in two-network zero-sum games: An incremental algorithm Neurocomputing (2019) C. Shi et al. Distributed Nash equilibrium computation in aggregative games: An event-triggered algorithm Inf. Sci. (2019) J. Ren et al. A differential game method against attacks in heterogeneous honeynet Comput. Secur. (2020) Y. Niu et al. Strategy evolution of panic pedestrians in emergent evacuation with assailants based on susceptible-infected-susceptible model Inf. Sci. (2021) T. Migot et al. A parametrized variational inequality approach to track the solution set of a generalized nash equilibrium problem Eur. J. Oper. Res. (2020) X. Liu et al. Quantitative cyber-physical security analysis methodology for industrial control systems based on incomplete information Bayesian game Comput. Secur. (2021) S. Le et al. A congestion game framework for service chain composition in NFV with function benefit Inf. Sci. (2020) C. Luo et al. Updating three-way decisions in incomplete multi-scale information systems Inf. Sci. (2019) X. Liu et al. Event evolution model for cybersecurity event mining in tweet streams Inf. Sci. (2020) O. Jann et al. Regime change games with an active defender Games Econ. Behav. (2021) L. Han et al. Intrusion detection model of wireless sensor networks based on game theory and an autoregressive model Inf. Sci. (2019) L. Huang et al. A dynamic games approach to proactive defense strategies against Advanced Persistent Threats in cyber-physical systems Comput. Secur. (2020) T. Huang et al. Adversarial attacks on deep-learning-based radar range profile target recognition Inf. Sci. (2020) K.S. Gill et al. GTM-CSec: Game theoretic model for cloud security based on IDS and honeypot Comput. Secur. (2020) L. Fang et al. Incentive mechanism for cooperative authentication: An evolutionary game approach Inf. Sci. (2020) View more references Cited by (0) Recommended articles (6) Research article A perceptually based spatio-temporal computational framework for visual saliency estimation Signal Processing: Image Communication, Volume 38, 2015, pp. 15-31 Show abstract The purpose of this paper is to demonstrate a perceptually based spatio-temporal computational framework for visual saliency estimation. We have developed a new spatio-temporal visual frontend based on biologically inspired 3D Gabor filters, which is applied on both the luminance and the color streams and produces spatio-temporal energy maps. These volumes are fused for computing a single saliency map and can detect spatio-temporal phenomena that static saliency models cannot find. We also provide a new movie database with eye-tracking annotation. We have evaluated our spatio-temporal saliency model on the widely used CRCNS-ORIG database as well as our new database using different fusion schemes and feature sets. The proposed spatio-temporal computational framework incorporates many ideas based on psychological evidences and yields significant improvements on spatio-temporal saliency estimation. Research article Study of electromagnetic interference on quench detecting system of HTS current leads for EAST Fusion Engineering and Design, Volume 88, Issue 12, 2013, pp. 3095-3100 Show abstract High temperature superconducting (HTS) material B-2223/Ag-Au has been used for EAST poloidal field (PF) coil current leads for reducing construction and operation cost of cryogenic system. The quench propagation velocity of HTS superconducting material is several orders of magnitude lower than that of normal low temperature current leads. It is difficult to detect weak signal of quench which is easily influenced by strong electromagnetic interference (EMI). In this paper, the sources of EMI from quench detecting system of high temperature current leads have been introduced. And we have chosen reasonable methods for good transformation and protection on the basis of electromagnetic compatibility simulation diagnosis experiments. Recent experimental results showed that the restraint of EMI has been achieved and has met the requirements of experiment. Research article QHSL: A quantum hue, saturation, and lightness color model Information Sciences, Volume 577, 2021, pp. 196-213 Show abstract Image processing with any potential quantum computing hardware requires a quantum color model capable of capturing and manipulating color information in images. In this study, a quantum hue, saturation, and lightness (QHSL) model is proposed as a first attempt to encode perceptually relevant triplet color components using the properties of quantum mechanics (i.e., entanglement and parallelism). The proposed color model was used to define a representation of two-dimensional QHSL images for storage and transformation with fewer computing resources. The configuration of color-assignment attributes within this QHSL representation offer useful applications for image analysis. Specifically, a pseudocolor technique with flexible gray depth divisions is presented for highlighting fine visual details. Research article Notes on type-2 triangular norms and their residual operators Information Sciences, Volumes 346–347, 2016, pp. 338-350 Show abstract Li recently investigated type-2 triangular norms and their residual operators. In this paper, the flaws in calculations of T -extension operations of t-(co)norms and their residual operations are presented with counterexamples. Moreover, incorrect conclusions are corrected. Research article An interpretable Neural Fuzzy Hammerstein-Wiener network for stock price prediction Information Sciences, Volume 577, 2021, pp. 324-335 Show abstract An interpretable regression model is proposed in this paper for stock price prediction. Conventional offline neuro-fuzzy systems are only able to generate implications based on fuzzy rules induced during training, which requires the training data to be able to adequately represent all system behaviors. However, the distributions of test and training data could be significantly different, e.g., due to drastic data shifts. We address this problem through a novel approach that integrates a neuro-fuzzy system with the Hammerstein-Wiener model forming an indivisible five-layer network, where the implication of the neuro-fuzzy system is realized by the linear dynamic computation of the Hammerstein-Wiener model. The input and output nonlinearities of the Hammerstein-Wiener model are replaced by the nonlinear fuzzification and defuzzification processes of the fuzzy system so that the fuzzy linguistic rules, induced from the linear dynamic computation, can be used to interpret the inference processes. The effectiveness of the proposed model is evaluated on three financial stock datasets. Experimental results showed that the proposed Neural Fuzzy Hammerstein-Wiener ( NFHW ) outperforms other neuro-fuzzy systems and the conventional Hammerstein-Wiener model on these three datasets. Research article A parallel multi-objective evolutionary algorithm for community detection in large-scale complex networks Information Sciences, Volume 576, 2021, pp. 374-392 Show abstract Community detection in large-scale complex networks has recently received significant attention as the volume of available data is becoming larger. The use of evolutionary algorithms (EAs) for community detection in large-scale networks has gained considerable popularity because these algorithms are fairly effective in networks with a relatively small number of nodes. In this paper, we propose a parallel multi-objective EA, called PMOEA, for community detection in large-scale networks, where the communities associated with key network nodes are detected in parallel. Specifically, we develop a multi-objective and a single-objective EA. The former is used to detect the communities of a key node instead of all communities in the network. The latter obtains the communities in the entire network using the previously detected communities of each key node. The performance of the proposed method was verified on both large-scale synthetic benchmark networks and real-world networks. The results demonstrated the superiority of PMOEA over six EA-based and two non-EA-based community-detection algorithms for large-scale networks. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.078", "PubYear": 2021, "Volume": "577", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu 610065, China;Corresponding author"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Cyber Science and Engineering, Sichuan University, Chengdu 610065, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Electronics and Information Engineering, Sichuan University, Chengdu 610065, China"}], "References": [{"Title": "Game theoretic approach to shadowed sets: A three-way tradeoff perspective", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "540", "JournalTitle": "Information Sciences"}, {"Title": "Incentive mechanism for cooperative authentication: An evolutionary game approach", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "527", "Issue": "", "Page": "369", "JournalTitle": "Information Sciences"}, {"Title": "Game-theoretic analysis of development practices: Challenges and opportunities", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "159", "Issue": "", "Page": "110424", "JournalTitle": "Journal of Systems and Software"}, {"Title": "A dynamic games approach to proactive defense strategies against Advanced Persistent Threats in cyber-physical systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "101660", "JournalTitle": "Computers & Security"}, {"Title": "A congestion game framework for service chain composition in NFV with function benefit", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "514", "Issue": "", "Page": "512", "JournalTitle": "Information Sciences"}, {"Title": "GTM-CSec: Game theoretic model for cloud security based on IDS and honeypot", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101732", "JournalTitle": "Computers & Security"}, {"Title": "Event evolution model for cybersecurity event mining in tweet streams", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "524", "Issue": "", "Page": "254", "JournalTitle": "Information Sciences"}, {"Title": "MAG-GAN: Massive attack generator via GAN", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "536", "Issue": "", "Page": "67", "JournalTitle": "Information Sciences"}, {"Title": "Adversarial attacks on deep-learning-based radar range profile target recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "531", "Issue": "", "Page": "159", "JournalTitle": "Information Sciences"}, {"Title": "A differential game method against attacks in heterogeneous honeynet", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "97", "Issue": "", "Page": "101870", "JournalTitle": "Computers & Security"}, {"Title": "Particle filtering for a class of cyber-physical systems under Round-Robin protocol subject to randomly occurring deception attacks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "298", "JournalTitle": "Information Sciences"}, {"Title": "Game-theoretic modeling of the behavior of Domain Name System attacker", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "87", "Issue": "", "Page": "106801", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Quantitative cyber-physical security analysis methodology for industrial control systems based on incomplete information Bayesian game", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "102", "Issue": "", "Page": "102138", "JournalTitle": "Computers & Security"}, {"Title": "A reputation score policy and Bayesian game theory based incentivized mechanism for DDoS attacks mitigation and cyber defense", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "117", "Issue": "", "Page": "193", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Strategy evolution of panic pedestrians in emergent evacuation with assailants based on susceptible-infected-susceptible model", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>ian <PERSON>", "PubYear": 2021, "Volume": "570", "Issue": "", "Page": "105", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 89246497, "Title": "Avaddon ransomware: An in-depth analysis and decryption of infected systems", "Abstract": "Malware is an emerging and popular threat flourishing in the underground economy. The commoditization of Malware-as-a-Service (MaaS) allows criminals to obtain financial benefits at a low risk and with little technical background. One such popular product is ransomware, which is a popular type of malware traded in the underground economy. In ransomware attacks, data from infected systems is held hostage (encrypted) until a ransom is paid to the criminals. In addition, a recent blackmailing strategy adopted by criminals is to leak data online from the infected systems if the ransom is not paid before a given time, producing further economic and reputational damage. In this work, we perform an in-depth analysis of Avaddon, a ransomware offered in the underground economy as an affiliate program business. This threat has been linked to various cyberattacks and has infected and leaked data from at least 62 organizations. Additionally, it also runs Distributed Denial-of-Service (DDoS) attacks against victims that do not pay the ransom. We first provide an analysis of the criminal business model in the underground economy. Then, we identify and describe its technical capabilities, dissecting details of its inner structure. As a result, we provide tools to assist analysis, decrypting and labeling obfuscated strings observed in the ransomware binary. Additionally, we provide empirical evidence of links between this variant and a previous family, suggesting that the same group was behind the development and, possibly, the operation of both campaigns. Finally, we develop a procedure to recover files encrypted by Avaddon. We successfully tested the proposed procedure against different versions of Avaddon. The proposed method is released as an open-source tool so it can be incorporated in existing Antivirus engines and extended to decrypt other ransomware families that implement a similar encryption approach.", "Keywords": "Avaddon ; Ransomware ; Malware analysis ; Reverse engineering ; Cybersecurity", "DOI": "10.1016/j.cose.2021.102388", "PubYear": 2021, "Volume": "109", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Universidad Rey Juan Carlos, Madrid, Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universidad Carlos III, Madrid, Spain"}], "References": [{"Title": "Internet of things and ransomware: Evolution, mitigation and prevention", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "22", "Issue": "1", "Page": "105", "JournalTitle": "Egyptian Informatics Journal"}]}, {"ArticleId": 89246558, "Title": "Fault-tolerant Hamiltonicity of hypercubes with faulty subcubes", "Abstract": "Motivated by the famous Locke's Conjecture, we study the following special kind of fault-tolerance of hypercubes. Let Q m be a certain subcube of an n -dimensional hypercube Q n , i.e., 1 ≤ m ≤ n , and let F be a set of vertex-disjoint subcubes of Q m . Then ( a ) Q m -subcube connectivity κ s ( Q n ; Q m ) of Q n is the minimum cardinality over all F 's such that Q n − F is disconnected; ( b ) Q m -subcube fault-tolerant Hamiltonicity H s ( Q n ; Q m ) of Q n is the maximum integer k such that Q n − F is Hamiltonian for every F with   F   ≤ k . In particular, if every element in F is isomorphic to Q m , then κ ( Q n ; Q m ) (resp. H ( Q n ; Q m ) ) represents Q m -cube connectivity (resp. Q m -cube fault-tolerant Hamiltonicity ) of Q n . Similarly, HL s ( Q n ; Q m ) (resp. HL ( Q n ; Q m ) ) means Q m -subcube (resp. Q m -cube) fault-tolerant Hamilton laceability of Q n . Our main results are as follows: (1) κ s ( Q n ; Q m ) = κ ( Q n ; Q m ) = n − m for n − m ≥ 2 , i.e., by deleting ( n − m − 1 ) m -dimensional hypercube Q m or its subcubes from the n -dimensional hypercube Q n , the resulting graph is still connected. (2) H s ( Q n ; Q m ) = H ( Q n ; Q m ) = n − m − 1 for n − m ≥ 2 , i.e., by deleting ( n − m − 1 ) m -dimensional hypercube Q m or its subcubes from the n -dimensional hypercube Q n , the resulting graph is still Hamiltonian. (3) n − m − 2 ≤ HL s ( Q n ; Q m ) ≤ HL ( Q n ; Q m ) ≤ n − m − 1 for n − m ≥ 3 , i.e., by deleting ( n − m − 2 ) m -dimensional hypercube Q m or its subcubes from the n -dimensional hypercube Q n , the resulting graph is still Hamilton laceable.", "Keywords": "Connectivity ; Hamiltonicity ; Subcube faults ; Fault-tolerance ; Hypercubes", "DOI": "10.1016/j.ipl.2021.106160", "PubYear": 2021, "Volume": "172", "Issue": "", "JournalId": 5748, "JournalTitle": "Information Processing Letters", "ISSN": "0020-0190", "EISSN": "1872-6119", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi, Xinjiang, 830046, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Mathematics and System Sciences, Xinjiang University, Urumqi, Xinjiang, 830046, PR China;Corresponding author"}], "References": []}, {"ArticleId": 89246559, "Title": "Distributed cooperative optimization for multiple heterogeneous Euler-Lagrangian systems under global equality and inequality constraints", "Abstract": "In this paper, we study the distributed cooperative optimization problem with globally equality and inequality constraints for a multi-agent system, where each agent is modeled by Euler-Lagrangian (EL) dynamics. The optimized function can be represented by the sum of all local cost functions corresponding to each individual agent. Two continuous-time algorithms are proposed to solve such the problem in a distributed manner. In virtue of geometric graph theory, convex analysis , and Lyapunov stability theory , it is proved that all agents achieve consensus on the Lagrangian multipliers associated with constraints while the proposed algorithms converge exponentially to the optimal solution of the problem given in the case that the parameters of EL agents are known, and converge asymptotically to the optimal solution of the problem in the case that the parameters of EL agents are unknown, respectively. Finally, an example is provided to demonstrate the effectiveness of the theoretical results. Introduction Distributed control of multi-agent systems has received increasing attention in science and engineering fields owing to its extensive applications such as consensus, trajectory tracking, machine learning, formation control, resource allocation, and predictive control [22], [24], [10], [26], [32], [31]. Its core idea is to cooperatively fulfil a complex task for multi-agent systems by using only local information, which means that distributed control has many advantages such as reliability, scalability and robustness. This can also be used to explain why distributed strategies are highly favored by many researchers. In recent years, a universal class of the distributed optimization problems based on multi-agent networks has been investigated. Many researchers have detailed such the problem from different perspectives. Some results based on discrete-time algorithms were reported in [20], [21], [2], [30], [34], [15]. For instance, as an early method, the incremental algorithm was proposed to solve the distributed optimization problems with or without constraints in [20], [21]. The alternating direction method of multipliers was developed for distributed optimization in [2]. This algorithm was further improved to solve the distributed optimization problem in a parallel manner in [30]. [34] established a distributed saddle-point dynamics based algorithm to solve an online optimization problem. On the other hand, in actual situations, trajectories of systems are continuously changing, and Lyapunov theory is an important tool that is often used in the stability analysis of continuous-time systems. Based on these facts, there is also a growing interest to explore more suitable continuous-time algorithms for the distributed optimization problems [27], [12], [33], [13], [28], [7], [36]. For instance, [27] provided a class of novel analysis strategies for distributed optimization with constraints from a control viewpoint, then the class of continuous-time algorithms was further extended to solve the distributed optimization problems with constraints or without constraints [12], [33], [13], [28], [36], respectively. A distributed Laplacian-gradient coordination algorithm was established to deal with the economic dispatch problem in power systems [7], and the proposed algorithm was proved to be able to achieve the optimal scheduling with a Lyapunov function method [11]. [17], [14] studied the optimal consensus problems for a class of nonlinear multi-agent systems. In addition, [37] studied the multiple-objective optimization problem for hybrid energy system. The EL system is an important nonlinear system, and now its distributed cooperative control has received remarkable attention. Compared with linear systems, the EL dynamics can be used to accurately describe the motion behavior of many physical systems such as manipulators, spacecrafts, rigid bodies, mobile robots, and mobile vehicles in [23]. Based on this background, distributed cooperative control of EL systems has widespread applications including cooperative location control of multi-robot systems [29], leader-following formation control [5], attitude control of rigid body systems [16], and convex intersection calculation for EL systems [17]. Furthermore, with the help of the feedback linearization technique in [23], [35] established a continuous-time algorithm based on gradient to solve the distributed optimal consensus problem for a group of EL agents, and then also proposed a distributed estimator-based algorithm to solve the uncertain distributed optimal consensus problem. It is worth noting that, though many results on distributed control of multi-agent systems with a group of EL agents have been reported [5], [16], [17], [6], most of them only consider the consensus problem without leader or leader-following consensus problem with leader. Note that, in this process of achieving consensus, agents are driven by the energy provided by their own power supply, the energy consumed by each agent can be modeled by a local cost function associated with its state, which is only known by this agent. If we further focus on the relationship between the consistency state and the cost of energy consumption, then the consensus problem is evolved into a distributed optimization problem, and the consistent state is required to minimize the sum of a group of local cost functions associated with each individual agent. The class of optimization problems has been investigated in [35]. However, states of EL agents are often limited in real applications. For instance, their states need to satisfy some constraints, i.e., equality and inequality constraints. In addition, the parameters of EL dynamics are often unknown to each agent, which can induce challenges for the design of distributed control protocol. To the best of our knowledge, there are very few results related to the distributed optimization problem with equality and inequality constraints for multiple EL systems, which motivates the present exploration. In this paper, we study a distributed optimization problem with equality and inequality constraints for multiple heterogeneous EL systems. It is worth mentioning that the considered problem is different from the centralized optimization problem. The distributed optimization problem can be divided into several subproblems, which are solved by each agent only using its own information as well as exchanged information from its neighbors. The contributions of this paper are emphasized as follows. • When the parameters of EL dynamics are known to the agents, based on the saddle-point dynamics and feedback linearization technique, we propose a distributed continuous-time algorithm to solve the considered problem. • Supported by geometric graph theory, convex analysis and Lyapunov theory, it is proved that the Lagrangian multipliers associated with constraints achieve consensus while the proposed algorithm could give rise to the optimal solution of the problem. • In actual situations, since the parameters of EL dynamics are often unknown, a distributed estimator-based algorithm is developed to solve the optimization problem with constraints, and it is proved that the proposed algorithm is also able to present the optimal solution of the problem. The rest of this paper is organized as follows. Section 2 briefly reviews notations, preliminaries and formulates the problem of our interest. Section 3 presents algorithm design and convergence analysis. An example is provided to demonstrate the effectiveness of the theoretical results in Section 4. Section 5 summarises this investigation. Figures The communication topology among six manipulators. Trajectories of the tracking error ei,i∈1,2,3,4,5,6. Trajectories of xi of manipulator i,i∈1,2,3,4,5,6. Trajectories of ẋi of manipulator i,i∈1,2,3,4,5,6, where ω=ẋ. Trajectories of the Lagrangian multiplier λi,i∈1,2,3,4,5,6. Trajectories of the Lagrangian multiplier μi,i∈1,2,3,4,5,6. Show all figures Section snippets Preliminaries and problem formulation In this section, we first recall some basic concepts associated with convex analysis and algebraic graph theory, followed by problem formulation. Algorithm design and convergence analysis In this section, two different continuous-time algorithms are proposed to solve the distributed optimization problem (5) under two cases that the parameters of EL system (1) are known and unknown, respectively. Moreover, convergence analysis of the proposed algorithms are also given. Simulation In this section, an example is provided to illustrate the effectiveness of the theoretical results. We consider a cargo configuration problem for a logistics system with six manipulators, where its goal is to use manipulators to cooperatively move the goods to the suitable locations so that the cost of the whole handling process is minimized when certain constraints are satisfied. Specifically, the dynamics of each manipulator is modeled as an Euler-Lagrangian Eq. (1) with the parameters in [23] J Conclusion In this paper, two continuous-time algorithms have been proposed to solve the distributed optimization problem with the equality and inequality constraints for multiple heterogeneous Euler-Lagrangian systems. The algorithms were proved to converge to the optimal solution of the considered problem in the cases that the parameters of Euler-Lagrangian dynamics are known and unknown, respectively. Furthermore, an alternative condition was provided to alleviate the dependence on the global Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgements The authors are very grateful to the editors and anonymous reviewers for their valuable comments to improve the quality of the paper. This work was supported in part by the National Natural Science Foundation of China under Grant 61973050 and Grant 61773089, by Fundamental Research Funds for the Central Universities under Grant DUT20GJ209 and Grant DUT20JC14. References (37) Y. Zhang et al. Distributed optimal coordination for multiple heterogeneous Euler-Lagrangian systems Automatica (2017) J. Yan et al. Parallel alternating direction method of multipliers Inf. Sci. (2020) Z. Meng et al. Targeted agreement of multiple lagrangian systems Automatica (2017) L. Ma et al. Distributed finite-time attitude containment control of multi-rigid-body systems J. Frankl. Inst. (2015) Y.-W. Lv et al. Differentially private distributed optimization for multi-agent systems via the augmented lagrangian algorithm Inf. Sci. (2020) S.S. Kia et al. Distributed convex optimization via continuous-time coordination algorithms with discrete-time communication Automatica (2015) W. He et al. Network-based leader-following consensus of nonlinear multi-agent systems via distributed impulsive control Inf. Sci. (2017) H.J. Brascamp et al. Best constants in young’s inequality, its converse, and its generalization to more than three functions Adv. Math. (1976) M.S. Bazraa et al. Nonlinear programming: theory and algorithms (1979) S. Boyd et al. Distributed optimization and statistical learning via the alternating direction method of multipliers Found. Trends Mach. Learn. (2011) S. Boyd et al. Convex optimization, Cambridge (2004) H. Cai et al. Leader-following consensus of multiple uncertain Euler-Lagrange systems under switching network topology Int. J. Gen. Syst. (2014) B. Cheng et al. Distributed containment control of Euler-Lagrange systems over directed graphs via distributed continuous controllers IET Control Theory Appl. (2017) A. Cherukuri et al. Distributed generator coordination for initialization and anytime optimization in economic dispatch IEEE Trans. Control Netw. Syst. (2015) Z. Ding (2013) C. Godsil et al. (2013) H.K. Khalil Nonlinear systems, NJ (2002) X. Le et al. A neurodynamic approach to distributed optimization with globally coupled constraints IEEE Trans. Cybern. (2018) View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.080", "PubYear": 2021, "Volume": "577", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Optimization for Industrial Equipment of Ministry of Education, Dalian University of Technology, Dalian 116024, China School of Control Science and Engineering, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Optimization for Industrial Equipment of Ministry of Education, Dalian University of Technology, Dalian 116024, China School of Control Science and Engineering, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Optimization for Industrial Equipment of Ministry of Education, Dalian University of Technology, Dalian 116024, China School of Control Science and Engineering, Dalian University of Technology, Dalian 116024, China;Corresponding author at: Key Laboratory of Intelligent Control and Optimization for Industrial Equipment of Ministry of Education, Dalian University of Technology, Dalian 116024, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Intelligent Control and Optimization for Industrial Equipment of Ministry of Education, Dalian University of Technology, Dalian 116024, China School of Control Science and Engineering, Dalian University of Technology, Dalian 116024, China"}], "References": [{"Title": "Parallel alternating direction method of multipliers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "507", "Issue": "", "Page": "185", "JournalTitle": "Information Sciences"}, {"Title": "Differentially private distributed optimization for multi-agent systems via the augmented Lagrangian algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "538", "Issue": "", "Page": "39", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 89246564, "Title": "Sparse semi-supervised heterogeneous interbattery bayesian analysis", "Abstract": "The Bayesian approach to feature extraction, known as factor analysis (FA), has been widely studied in machine learning to obtain a latent representation of the data. An adequate selection of the probabilities and priors of these bayesian models allows the model to better adapt to the data nature (i.e. heterogeneity, sparsity), obtaining a more representative latent space. The objective of this article is to propose a general FA framework capable of modelling any problem. To do so, we start from the Bayesian Inter-Battery Factor Analysis (BIBFA) model, enhancing it with new functionalities to be able to work with heterogeneous data, to include feature selection, and to handle missing values as well as semi-supervised problems. The performance of the proposed model, Sparse Semi-supervised Heterogeneous Interbattery Bayesian Analysis (SSHIBA), has been tested on different scenarios to evaluate each one of its novelties, showing not only a great versatility and an interpretability gain, but also outperforming most of the state-of-the-art algorithms.", "Keywords": "Bayesian model ; Canonical correlation analysis ; Principal component analysis ; Factor analysis ; Feature selection ; Semi-supervised ; Multi-task", "DOI": "10.1016/j.patcog.2021.108141", "PubYear": 2021, "Volume": "120", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Signal Processing and Communications, Universidad Carlos III de Madrid Leganés, 28911 Spain;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Signal Processing and Communications, Universidad Carlos III de Madrid Leganés, 28911 Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Signal Processing and Communications, Universidad Carlos III de Madrid Leganés, 28911 Spain"}], "References": []}, {"ArticleId": 89246565, "Title": "The influence of computer network technology on national income distribution under the background of social economy", "Abstract": "Data mining algorithm is widely used in data management. In view of the current situation that the edge computing mode based on computer network technology is applied to data management, this paper studies the optimization and improvement of the classic Apriori algorithm and K-means algorithm respectively, improves the operation efficiency by optimizing the candidate set, enhances the clustering effect by determining the initial clustering center, and applies the improved algorithm to the national income management which contains 10 attributes. The algorithm performance test shows that the improved Apriori algorithm can generate frequent itemsets in a shorter time, which is about 2000 ms when the minimum support threshold is 30%–90%, while the original algorithm is about 4000 Ms. K-means algorithm is relatively sparse in the same type of distribution, while im-k-means algorithm is relatively aggregated. According to the confidence level and related attributes, the order of the influence on salary level from large to small is marital status, education level and time, working time and nature, and the corresponding confidence levels are about 0.95, 0.84 and 0.80 respectively. The improved Apriori algorithm and im-k-means algorithm can well show the relationship between education level and salary level. The research results make a great contribution to the application of computer network technology in data management, and also provide a scientific and reasonable method for the income classification of population.", "Keywords": "Income distribution ; Apriori algorithm ; k-means algorithm ; Data mining ; Edge computing mode", "DOI": "10.1016/j.comcom.2021.06.025", "PubYear": 2021, "Volume": "177", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Accounting School, Guangzhou Huashang College, Guangzhou City, Guangdong Province, 511300, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Accounting School, Guangzhou Huashang College, Guangzhou City, Guangdong Province, 511300, China;Corresponding author"}], "References": []}, {"ArticleId": ********, "Title": "Using 3D printing and femtosecond laser micromachining to fabricate biodegradable peripheral vascular stents with high structural uniformity and dimensional precision", "Abstract": "<p>This study combined rotational 3D printing with femtosecond laser postprocessing to produce a biodegradable polylactic acid (PLA) prototype stent suitable for treating peripheral arterial diseases. First, the peripheral arterial stent structure was designed with the assist of the finite element method. The external diameter of the stent was compressed from 4 to 2 mm for placement in the catheter, and balloon dilation was performed to expand the outer diameter to 5.4 mm. Subsequently, rotational 3D printing was conducted in conjunction with a femtosecond laser to produce and postprocess the designed stent structure. A PLA stent with uniform structural widths, smooth edges, and accurate structural dimensions was successfully produced. The strut width, connector width, and crown radius of the PLA stent were respectively 0.205 mm (standard deviation [SD] = 0.005 mm), 0.203 mm (SD = 0.004 mm), and 0.303 mm (SD = 0.003 mm). Finally, the stent was compressed and expanded to measure its radial force performance after expansion. The fabricated stent achieved the required radial force of 0.079 N/mm and met the required specifications for a peripheral arterial stent.</p>", "Keywords": "3D printing; Femtosecond laser; Biodegradable stent; Peripheral artery disease", "DOI": "10.1007/s00170-021-07446-z", "PubYear": 2021, "Volume": "116", "Issue": "5-6", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University of Science and Technology, Taipei, Taiwan (Republic of China)"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University of Science and Technology, Taipei, Taiwan (Republic of China)"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University of Science and Technology, Taipei, Taiwan (Republic of China)"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Taiwan University of Science and Technology, Taipei, Taiwan (Republic of China)"}], "References": []}, {"ArticleId": 89246626, "Title": "Optimization of fitness data monitoring system based on Internet of Things and cloud computing", "Abstract": "In the service dimension, the construction of fitness science data supervision service mode is discussed. Based on the stakeholder theory, through the statistical analysis of the stakeholders of fitness science data supervision, three core stakeholders of the government, users and data service personnel are identified. Based on these three dimensions, we find out the core concepts of government policy model, user demand model and service model. At the same time, each dimension is deeply analyzed. Through the relationship analysis between these three dimensions, the user-oriented collaborative supervision service model of fitness scientific data is expected to guide the specific service practice of fitness scientific data supervision through the establishment of this model. In addition, an unsupervised learning method in machine learning, the isolation forest algorithm, is introduced to detect abnormal data; at the same time, using real fitness data sets, through comparative experiments with local anomaly factor algorithms, it is verified that the isolation forest algorithm has a good effect of anomaly detection; this article also uses redis cache to optimize the performance of the fitness data monitoring system, which solves the access pressure of the main database in a multi-user high-concurrency environment; Finally, the usability and stability of the system are verified by functional tests and stress tests.", "Keywords": "Internet of Things ; Cloud computing ; Fitness data supervision ; Isolated forest algorithm ; Data monitoring system", "DOI": "10.1016/j.comcom.2021.06.027", "PubYear": 2021, "Volume": "177", "Issue": "", "JournalId": 2878, "JournalTitle": "Computer Communications", "ISSN": "0140-3664", "EISSN": "1873-703X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Physical Education, Sangmyung University, Seoul 03016, Republic of Korea"}, {"AuthorId": 2, "Name": "Xusheng Che", "Affiliation": "Sport and Science Collage, NanTong University, Jiangsu, NanTong 226019, China;Corresponding author"}], "References": []}, {"ArticleId": 89246653, "Title": "Scalable kernel-based SVM classification algorithm on imbalance air quality data for proficient healthcare", "Abstract": "In the last decade, we have seen drastic changes in the air pollution level, which has become a critical environmental issue. It should be handled carefully towards making the solutions for proficient healthcare. Reducing the impact of air pollution on human health is possible only if the data is correctly classified. In numerous classification problems, we are facing the class imbalance issue. Learning from imbalanced data is always a challenging task for researchers, and from time to time, possible solutions have been developed by researchers. In this paper, we are focused on dealing with the imbalanced class distribution in a way that the classification algorithm will not compromise its performance. The proposed algorithm is based on the concept of the adjusting kernel scaling (AKS) method to deal with the multi-class imbalanced dataset. The kernel function's selection has been evaluated with the help of weighting criteria and the chi-square test. All the experimental evaluation has been performed on sensor-based Indian Central Pollution Control Board (CPCB) dataset. The proposed algorithm with the highest accuracy of 99.66% wins the race among all the classification algorithms i.e. Adaboost (59.72%), Multi-Layer Perceptron (95.71%), GaussianNB (80.87%), and SVM (96.92). The results of the proposed algorithm are also better than the existing literature methods. It is also clear from these results that our proposed algorithm is efficient for dealing with class imbalance problems along with enhanced performance. Thus, accurate classification of air quality through our proposed algorithm will be useful for improving the existing preventive policies and will also help in enhancing the capabilities of effective emergency response in the worst pollution situation.", "Keywords": "Air quality; Classification; Proficient healthcare; Scalable kernel-based SVM; Imbalance data", "DOI": "10.1007/s40747-021-00435-5", "PubYear": 2021, "Volume": "7", "Issue": "5", "JournalId": 32210, "JournalTitle": "Complex & Intelligent Systems", "ISSN": "2199-4536", "EISSN": "2198-6053", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Institute of Science, Banaras Hindu University, Varanasi, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Institute of Science, Banaras Hindu University, Varanasi, India"}], "References": [{"Title": "Performance Analysis of Distributed Computing Frameworks for Big Data Analytics: Hadoop Vs Spark", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "669", "JournalTitle": "Computación y Sistemas"}, {"Title": "Enhanced Gaussian process regression-based forecasting model for COVID-19 outbreak and significance of IoT for its detection", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "3", "Page": "1492", "JournalTitle": "Applied Intelligence"}, {"Title": "A Hybrid Deep Learning Model for COVID-19 Prediction and Current Status of Clinical Trials Worldwide", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "66", "Issue": "2", "Page": "1896", "JournalTitle": "Computers, Materials & Continua"}, {"Title": "Detection and Classification of Leukocytes in Blood Smear Images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "2", "Page": "111", "JournalTitle": "International Journal of Ambient Computing and Intelligence"}]}, {"ArticleId": 89246654, "Title": "Research on Indoor UWB Positioning Based on Expectation Maximization in NLOS Environment", "Abstract": "<p>Ultra-wide band (UWB) technology is especially suitable for indoor positioning because of its strong anti-interference ability, it can achieve centimeter-level positioning accuracy in the line of sight (LOS) environment, but it is challenging to construct a robust and high-precision algorithm in indoor complex and changeable environments. Therefore, it establishes an improved standard Kalman filter (SKF) based on expectation maximization (SKF-EM), which models the prediction error covariance and measurement noise using the maximum likelihood criterion, estimates the filter parameters online through the expectation maximum theory, and updates the Kalman filter gain according to the Kalman filter based on dropped measurements method (SKF-DMM). Experimental results show that: in case of serious occlusion, the maximum positioning error of SKF-EM algorithm is 1.03 m, 39% lower than that of SKF and 60.2% lower than that of UWB.</p>", "Keywords": "expectation maximization (EM);indoor positioning;time difference of arrival (TDOA);ultra-wide band (UWB)", "DOI": "10.1002/cpe.6278", "PubYear": 2021, "Volume": "33", "Issue": "17", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information, Hunan University of Humanities, Science and Technology, Loudi, China; School of Electronics and Information, Northwestern Polytechnical University, Xi'an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Information, Hunan University of Humanities, Science and Technology, Loudi, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronics and Information, Northwestern Polytechnical University, Xi'an, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information, Hunan University of Humanities, Science and Technology, Loudi, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Information, Hunan University of Humanities, Science and Technology, Loudi, China"}], "References": [{"Title": "A Gaussian error correction multi‐objective positioning model with NSGA‐II", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "5", "Page": "", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Personalized Recommendation System Based on Collaborative Filtering for IoT Scenarios", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "4", "Page": "685", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 89246704, "Title": "A novel dense capsule network based on dense capsule layers", "Abstract": "<p>Capsule network, which performs feature presentations for classification tasks via novel capsule forms, has attracted more and more attention. However, its performance on complex datasets has not been fully utilized. Through an in-depth exploration of Dense Convolutional Network (DenseNet), we propose a novel dense capsule network based on dense capsule layers, named DenseCaps. As far as we know, this is the first attempt to achieve a cross-capsule feature concatenations. This architecture enhances feature reuse by realizing dense connections at capsule-level, and captures different levels of detailed features to improve the performance on color datasets. Extensive experiments and ablation studies prove the proposed model achieves competitive results on multiple benchmark datasets (MNIST, Fashion-MNIST, CIFAR-10, and SVHN).</p>", "Keywords": "Capsule network; DenseNet; Feature-capsules reuse; Dense capsule layers", "DOI": "10.1007/s10489-021-02630-w", "PubYear": 2022, "Volume": "52", "Issue": "3", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "Guangcong Sun", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China;Mine Digitization Engineering Research Center of Ministry of Education of the People’s Republic of China, Xuzhou, China"}, {"AuthorId": 3, "Name": "Tongfeng Sun", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, China University of Mining and Technology, Xuzhou, China"}], "References": [{"Title": "Dense connection and depthwise separable convolution based CNN for polarimetric SAR image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "194", "Issue": "", "Page": "105542", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Siamese capsule networks with global and local features for text classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "390", "Issue": "", "Page": "88", "JournalTitle": "Neurocomputing"}, {"Title": "SA-CapsGAN: Using Capsule Networks with embedded self-attention for Generative Adversarial Network", "Authors": "Guangcong Sun; <PERSON><PERSON><PERSON>; Tongfeng Sun", "PubYear": 2021, "Volume": "423", "Issue": "", "Page": "399", "JournalTitle": "Neurocomputing"}, {"Title": "Capsule network based analysis of histopathological images of oral squamous cell carcinoma", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "7", "Page": "4546", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Limitation of capsule networks", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "144", "Issue": "", "Page": "68", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "MIXCAPS: A capsule network-based mixture of experts for lung nodule malignancy prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "116", "Issue": "", "Page": "107942", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 89246714, "Title": "Joining different aluminum alloy sheets by flat clinching process", "Abstract": "<p>Increasing attention has been paid to the flat clinching process for constructing advanced engineering structures because it has more benefits than the conventional cliching process. The effect of material arrangements on flat clinching process was investigated in the paper. The four material arrangements using three dissimilar aluminum alloys were employed to develop flat clinching joints. The cross-sectional profile of the joint was observed, and its main geometrical parameters were determined. The mechanical characteristics of the joint include four parts: tensile strength, shearing strength, failure mode, and absorbed energy. Tension-shearing and tensile tests were carried out to assess the mechanical characteristics of the flat clinching joint. The geometric profile and the material properties determine the mechanical characteristics of the flat clinching joint. Compared with the flat clinching joint using a hard lower sheet, the flat clinching joint with a hard upper sheet demonstrates a larger neck thickness and a lower undercut. Meanwhile, it has greater static strength and absorbed energy. In tension-shearing tests, necking failure is only failure mode appearing; in tensile tests, failure mode mostly belongs to unbutton failure. The flat clinching process can effectively join dissimilar aluminum alloy sheets.</p>", "Keywords": "Flat clinching process; Clinching joint; Material arrangements; Mechanical characteristics", "DOI": "10.1007/s00170-021-07511-7", "PubYear": 2021, "Volume": "116", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of High Performance Complex Manufacturing, Light Alloy Research Institute, Central South University, Changsha, China;School of Mechanical and Electrical Engineering, Central South University, Changsha, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "State Key Laboratory of High Performance Complex Manufacturing, Light Alloy Research Institute, Central South University, Changsha, China;School of Mechanical and Electrical Engineering, Central South University, Changsha, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of High Performance Complex Manufacturing, Light Alloy Research Institute, Central South University, Changsha, China;School of Mechanical and Electrical Engineering, Central South University, Changsha, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of High Performance Complex Manufacturing, Light Alloy Research Institute, Central South University, Changsha, China;School of Mechanical and Electrical Engineering, Central South University, Changsha, China"}], "References": [{"Title": "Recent development of improved clinching process", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "11-12", "Page": "3169", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Investigation of flat-clinching process using various thicknesses aluminum alloy sheets", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "7-8", "Page": "2075", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 89246715, "Title": "Investigation of forming optimization of composite tubes based on liquid impact forming", "Abstract": "<p>Liquid impact forming (LIF), a composite forming technology of metal thin-walled tubes based on tube hydroforming (THF) and stamping forming, is introduced to increase the forming efficiency and decrease the cost. In this paper, a finite element model was established to study the forming characteristics of composite tubes under different mold cavities and clamping speeds. Then, the effects of different loading parameters on the bulging height, fillet radius and wall thickness distribution of composite tubes were analyzed systematically. Furthermore, the main factors affecting the formability of the tube were investigated based on response surface method (RSM), with the wall thickness variance, bulging height and fillet radius of the tube designed as targets, the mold side length, clamping speed and initial internal pressure selected as variables, the optimal loading parameters were obtained. At last, experiments of composite tubes based on the optimized loading parameters were conducted. The results showed that the deviation between the experimental results and the numerical simulation was within 5%, which verified the accuracy and reliability of the parameter optimization results.</p>", "Keywords": "Liquid impact forming; Composite tube; Forming optimization; Response surface method; Tube hydroforming", "DOI": "10.1007/s00170-021-07530-4", "PubYear": 2021, "Volume": "116", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Guilin University of Electronic Technology, Guilin, China;Key Laboratory of Pressure Systems and Safety (MOE), School of Mechanical and Power Engineering, East China University of Science and Technology, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Guilin University of Electronic Technology, Guilin, China;Mechanical and Electrical Engineering Training Center, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Art & Design, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical and Electrical Engineering, Guilin University of Electronic Technology, Guilin, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Mechanical and Electrical Engineering Training Center, Guilin University of Electronic Technology, Guilin, China"}], "References": []}, {"ArticleId": 89246716, "Title": "The flexible rolling process of three-dimensional curved parts using an auxiliary plate based on rigid arc-shaped rollers", "Abstract": "<p>The 3D curved part rolling method based on the rigid arc-shaped rollers is a novel sheet metal rolling process to rapidly manufacture 3D surface parts for various parameters. The flow of material inside the sheet metal is complicated during the cold rolling forming process of the 3D curved parts, whose quality of products is sensitive to process parameters. The work analyzed the friction force for the sheet metal inside the roll gap during rolling as well as the reason for the instability of the forming process. The instability of the forming process significantly affected the forming accuracy of curved parts. Therefore, the method of using an auxiliary plate was proposed to improve the stability of the rolling process. The finite element numerical simulation models were established for two rolling processes, with and without the auxiliary plate, and the forming errors of the two processes were computed. The numerical simulation results showed that the rolling process with the auxiliary plate can obtain a more stable rolling process, and the forming error of formed curved parts was smaller. The reliability of the numerical simulation results was verified through experiments.</p>", "Keywords": "3D curved part; Flexible rolling; Surface forming; Auxiliary plate; Numerical simulation", "DOI": "10.1007/s00170-021-07512-6", "PubYear": 2021, "Volume": "116", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Roll Forging Institute, Jilin University, Changchun, China;College of Materials Science and Engineering, Jilin University, Changchun, China"}, {"AuthorId": 2, "Name": "Wenzhi Fu", "Affiliation": "Roll Forging Institute, Jilin University, Changchun, China;College of Materials Science and Engineering, Jilin University, Changchun, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Roll Forging Institute, Jilin University, Changchun, China;College of Materials Science and Engineering, Jilin University, Changchun, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Roll Forging Institute, Jilin University, Changchun, China;College of Materials Science and Engineering, Jilin University, Changchun, China"}], "References": [{"Title": "Research on three-dimensional curved surface rolling based on rigid arc-shaped rollers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "1-2", "Page": "805", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}, {"Title": "Influence of thickness reduction on the forming results in the three-dimensional surface rolling process with rigid arc-shaped rollers", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "114", "Issue": "7-8", "Page": "2397", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 89246931, "Title": "A comprehensive perspective of contrastive self-supervised learning", "Abstract": "", "Keywords": "", "DOI": "10.1007/s11704-021-1900-9", "PubYear": 2021, "Volume": "15", "Issue": "4", "JournalId": 4733, "JournalTitle": "Frontiers of Computer Science", "ISSN": "2095-2228", "EISSN": "2095-2236", "Authors": [{"AuthorId": 1, "Name": "Song<PERSON> Chen", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China;MIIT Key Laboratory of Pattern Analysis and Machine Intelligence, Nanjing, China"}, {"AuthorId": 2, "Name": "Chu<PERSON><PERSON> Geng", "Affiliation": "College of Computer Science and Technology, Nanjing University of Aeronautics and Astronautics, Nanjing, China;MIIT Key Laboratory of Pattern Analysis and Machine Intelligence, Nanjing, China"}], "References": []}, {"ArticleId": 89247346, "Title": "Improving Imbalanced Land Cover Classification with K-Means SMOTE: Detecting and Oversampling Distinctive Minority Spectral Signatures", "Abstract": "<p>Land cover maps are a critical tool to support informed policy development, planning, and resource management decisions. With significant upsides, the automatic production of Land Use/Land Cover maps has been a topic of interest for the remote sensing community for several years, but it is still fraught with technical challenges. One such challenge is the imbalanced nature of most remotely sensed data. The asymmetric class distribution impacts negatively the performance of classifiers and adds a new source of error to the production of these maps. In this paper, we address the imbalanced learning problem, by using K-means and the Synthetic Minority Oversampling Technique (SMOTE) as an improved oversampling algorithm. K-means SMOTE improves the quality of newly created artificial data by addressing both the between-class imbalance, as traditional oversamplers do, but also the within-class imbalance, avoiding the generation of noisy data while effectively overcoming data imbalance. The performance of K-means SMOTE is compared to three popular oversampling methods (Random Oversampling, SMOTE and Borderline-SMOTE) using seven remote sensing benchmark datasets, three classifiers (Logistic Regression, K-Nearest Neighbors and Random Forest Classifier) and three evaluation metrics using a five-fold cross-validation approach with three different initialization seeds. The statistical analysis of the results show that the proposed method consistently outperforms the remaining oversamplers producing higher quality land cover classifications. These results suggest that LULC data can benefit significantly from the use of more sophisticated oversamplers as spectral signatures for the same class can vary according to geographical distribution.</p>", "Keywords": "LULC classification; imbalanced learning; oversampling; data augmentation; clustering LULC classification ; imbalanced learning ; oversampling ; data augmentation ; clustering", "DOI": "10.3390/info12070266", "PubYear": 2021, "Volume": "12", "Issue": "7", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Campus de Campolide, Universidade Nova de Lisboa, 1070-312 Lisboa, Portugal ↑ Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Campus de Campolide, Universidade Nova de Lisboa, 1070-312 Lisboa, Portugal"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "NOVA Information Management School (NOVA IMS), Campus de Campolide, Universidade Nova de Lisboa, 1070-312 Lisboa, Portugal"}], "References": [{"Title": "A Systematic Review on Imbalanced Data Challenges in Machine Learning", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "4", "Page": "1", "JournalTitle": "ACM Computing Surveys"}]}, {"ArticleId": 89247379, "Title": "Retracted: An integrated framework for COVID ‐19 classification based on classical and quantum transfer learning from a chest radiograph", "Abstract": "<p>COVID-19 is a quickly spreading over 10 million persons globally. The overall number of infected patients worldwide is estimated to be around 133,381,413 people. Infection rate is being increased on daily basis. It has also caused a devastating effect on the world economy and public health. Early stage detection of this disease is mandatory to reduce the mortality rate. Artificial intelligence performs a vital role for COVID-19 detection at an initial stage using chest radiographs. The proposed methods comprise of the two phases. Deep features (DFs) are derived from its last fully connected layers of pre-trained models like AlexNet and MobileNet in phase-I. Later these feature vectors are fused serially. Best features are selected through feature selection method of PCA and passed to the SVM and KNN for classification. In phase-II, quantum transfer learning model is utilized, in which a pre-trained ResNet-18 model is applied for DF collection and then these features are supplied as an input to the 4-qubit quantum circuit for model training with the tuned hyperparameters. The proposed technique is evaluated on two publicly available x-ray imaging datasets. The proposed methodology achieved an accuracy index of 99.0% with three classes including corona virus-positive images, normal images, and pneumonia radiographs. In comparison to other recently published work, the experimental findings show that the proposed approach outperforms it.</p><p>© 2021 John Wiley &amp; Sons Ltd.</p>", "Keywords": "COVID‐19;SVM;classification;deep features;feature selection;fusion;quantum", "DOI": "10.1002/cpe.6434", "PubYear": 2022, "Volume": "34", "Issue": "20", "JournalId": 4295, "JournalTitle": "Concurrency and Computation: Practice and Experience", "ISSN": "1532-0626", "EISSN": "1532-0634", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Computer Science Comsats University Islamabad, Wah Campus Rawalpindi Pakistan."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science University of Wah Rawalpindi Pakistan."}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Computer Science Comsats University Islamabad, Wah Campus Rawalpindi Pakistan."}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "National University of Technology (NUTECH) Islamabad Pakistan."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science Comsats University Islamabad, Wah Campus Rawalpindi Pakistan."}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Department of Computer Science Comsats University Islamabad, Wah Campus Rawalpindi Pakistan."}], "References": [{"Title": "A framework for offline signature verification system: Best features selection approach", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "50", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Facial expressions classification and false label reduction using LDA and threefold SVM", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "166", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A novel nonintrusive decision support approach for heart rate measurement", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "148", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "A distinctive approach in brain tumor detection and classification using MRI", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "118", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "An intrusion detection scheme based on the ensemble of discriminant classifiers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "86", "Issue": "", "Page": "106742", "JournalTitle": "Computers & Electrical Engineering"}, {"Title": "Deep learning approaches for COVID-19 detection based on chest X-ray images", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "164", "Issue": "", "Page": "114054", "JournalTitle": "Expert Systems with Applications"}, {"Title": "CoVNet-19: A Deep Learning model for the detection and analysis of COVID-19 patients", "Authors": "<PERSON><PERSON><PERSON><PERSON>;  <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "104", "Issue": "", "Page": "107184", "JournalTitle": "Applied Soft Computing"}, {"Title": "A new approach for computer-aided detection of coronavirus (COVID-19) from CT and X-ray images using machine learning methods", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "105", "Issue": "", "Page": "107323", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 89247465, "Title": "A Human–Computer Interaction framework for emotion recognition through time-series thermal video sequences", "Abstract": "Infrared-Thermal Imaging is a non-contact mechanism for psychophysiological research and application in Human–Computer Interaction (HCI). Real-time detection of the face and tracking the Regions of Interest (ROI) in the thermal video during HCI is challenging due to head motion artifacts. This paper proposes a three-stage HCI framework for computing the multivariate time-series thermal video sequences to recognize human emotion and provides distraction suggestions. The first stage comprises of face, eye, and nose detection using a Faster R-CNN (region-based convolutional neural network) architecture and used Multiple Instance Learning (MIL) algorithm for tracking the face ROIs across the thermal video. The mean intensity of ROIs is calculated which forms a multivariate time series (MTS) data. In the second stage, the smoothed MTS data are passed to the Dynamic Time Warping (DTW) algorithm to classify emotional states elicited by video stimulus. During HCI, our proposed framework provides relevant suggestions from a psychological and physical distraction perspective in the third stage. Our proposed approach signifies better accuracy in comparison with other classification methods and thermal data-sets.", "Keywords": "DTW ; Emotion Recognition ; Faster R-CNN ; MIL ; HCI ; <PERSON><PERSON> Filter ; MTS", "DOI": "10.1016/j.compeleceng.2021.107280", "PubYear": 2021, "Volume": "93", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Subir Chowdhury School of Quality and Reliability, IIT, Kharagpur, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, IIT, Kharagpur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical Engineering, IIT, Kharagpur, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Subir Chowdhury School of Quality and Reliability, IIT, Kharagpur, India"}], "References": [{"Title": "A snapshot research and implementation of multimodal information fusion for data-driven emotion recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "53", "Issue": "", "Page": "209", "JournalTitle": "Information Fusion"}, {"Title": "MultiD-CNN: A multi-dimensional feature learning approach based on deep convolutional networks for gesture recognition in RGB-D image sequences", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "112829", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Emotion recognition using multi-modal data and machine learning techniques: A tutorial and review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "59", "Issue": "", "Page": "103", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 89247720, "Title": "hp-VARIATIONAL PHYSICS-INFORMED NEURAL NETWORKS FOR NONLINEAR TWO-PHASE TRANSPORT IN POROUS MEDIA", "Abstract": "", "Keywords": "", "DOI": "10.1615/JMachLearnModelComput.2021038005", "PubYear": 2021, "Volume": "2", "Issue": "2", "JournalId": 78716, "JournalTitle": "Journal of Machine Learning for Modeling and Computing", "ISSN": "2689-3967", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89247768, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.hcc.2021.100027", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 85789, "JournalTitle": "High-Confidence Computing", "ISSN": "2667-2952", "EISSN": "", "Authors": [], "References": []}, {"ArticleId": 89247771, "Title": "Predicting personalized grouping and consumption: A collaborative evolution model", "Abstract": "With the prevalence of online social groups, the dynamic joint prediction of users’ grouping and consumption behaviors on social network platforms is critical for optimizing social link suggestions and product recommendations. The group influence theory indicates that group norms affect user preference and behavior; however, the individual preferences of group members can also alter group norms. Nevertheless, the problems of how to holistically model dynamic bidirectional influence, existing between individual preferences and group norms, and then, simultaneously predict the users’ grouping and consumption behaviors are still underexplored. In this study, we propose a collaborative evolution and prediction (CEP) model to address the above issues. We associate each social group with a latent group norm vector, and assign each user with a latent individual preference vector. The unobservable interplay between individual preferences and group norms is then modeled according to the underlying group influence theory. Based on these two latent vectors, we design a joint optimization function that incorporates the correlation between grouping and consumption behaviors, to enhance the prediction performance. Through extensive experiments and evolution analysis, we demonstrate the prediction effectiveness and the explanatory power of our CEP model.", "Keywords": "Consumption behavior ; Grouping behavior ; Group norms ; Collaborative evolution and prediction ; Temporal probabilistic matrix factorization", "DOI": "10.1016/j.knosys.2021.107248", "PubYear": 2021, "Volume": "228", "Issue": "", "JournalId": 1879, "JournalTitle": "Knowledge-Based Systems", "ISSN": "0950-7051", "EISSN": "1872-7409", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Anhui 230009, China;@mail.hfut.edu.cn"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Anhui 230009, China;Key Laboratory of Process Optimization and Intelligent Decision Marketing, Ministry of Education, Hefei, Anhui 230009, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Anhui 230009, China;Key Laboratory of Process Optimization and Intelligent Decision Marketing, Ministry of Education, Hefei, Anhui 230009, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Intelligent Interconnected Systems Laboratory of Anhui Province, Hefei University of Technology, Hefei, Anhui 230601, China;School of Computer Science and Information Engineering, Hefei University of Technology, Hefei, Anhui 230601, China;Correspondence to: 1515 School of Management Building 1, Hefei University of Technology, Anhui 230009, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Management, Hefei University of Technology, Hefei, Anhui 230009, China;Key Laboratory of Process Optimization and Intelligent Decision Marketing, Ministry of Education, Hefei, Anhui 230009, China"}], "References": [{"Title": "Link prediction of time-evolving network based on node ranking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "195", "Issue": "", "Page": "105740", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "The four dimensions of social network analysis: An overview of research methods, applications, and software tools", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "63", "Issue": "", "Page": "88", "JournalTitle": "Information Fusion"}, {"Title": "Learning from class-imbalance and heterogeneous data for 30-day hospital readmission", "Authors": "<PERSON><PERSON> Du; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "420", "Issue": "", "Page": "27", "JournalTitle": "Neurocomputing"}, {"Title": "A Joint Neural Model for User Behavior Prediction on Social Networking Platforms", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "6", "Page": "1", "JournalTitle": "ACM Transactions on Intelligent Systems and Technology"}, {"Title": "Adaptive ensemble of classifiers with regularization for imbalanced data classification", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "69", "Issue": "", "Page": "81", "JournalTitle": "Information Fusion"}]}, {"ArticleId": 89247841, "Title": "2D MoS2 nanosheets and hematein complexes deposited on screen-printed graphene electrodes as an efficient electrocatalytic sensor for detecting hydrazine", "Abstract": "A new strategy to modify screen-printed graphene electrodes (SPGrEs) with two-dimensional molybdenum disulfide (2D-MoS<sub>2</sub>) nanosheets is described. The nanomaterial confined on the electrode surface is further modified forming a ternary complex with the redox mediator hematein (HM), using Al<sup>3+</sup> salts as mordant. The formation of this ternary complex, HM-Al<sup>3+</sup>-(2D-MoS<sub>2</sub>), gives rise to new nanostructures that can be visualized by scanning electron microscopy (SEM). The resulting nanostructured platform has been characterized by electrochemical techniques. It exhibits a strong electrocatalytic activity towards hydrazine oxidation mainly due to the presence of quinone/hydroquinone moieties from the hematein and a good stability. The large value calculated for the electrocatalytic kinetic constant, (8.1 ± 0.1) x10<sup>4</sup> M<sup>−1</sup> s<sup>−1</sup>, suggests that the presence of 2D-MoS<sub>2</sub> nanosheets significantly improves the properties of hematein as an electron donor/acceptor. The catalytic current showed a linear dependence with the hydrazine concentration, which has allowed the development of a hydrazine sensor based on HM-Al<sup>3+</sup>-(2D-MoS<sub>2</sub>)/SPGrEs with detection and quantification limits of 1.05 μM and 3.48 μM, respectively. The presence of different interfering compounds such as glucose, urea and others did not significantly affect the response of the proposed hydrazine sensor. This sensor has been used for the determination of hydrazine in tap and river waters.", "Keywords": "2D-MoS<sub>2</sub> nanosheets ; Ternary complex ; Screen-printed graphene electrode ; Electrocatalytic oxidation ; Hydrazine sensor", "DOI": "10.1016/j.snb.2021.130385", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Departamento de Química Analítica y Análisis Instrumental, Universidad Autónoma de Madrid, 28049, Madrid, Spain"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>Pa<PERSON>", "Affiliation": "Departamento de Química Analítica y Análisis Instrumental, Universidad Autónoma de Madrid, 28049, Madrid, Spain;Institute for Advanced Research in Chemical Sciences (IAdChem). Universidad Autónoma de Madrid, 28049, Madrid, Spain;IMDEA-Nanociencia, Ciudad Universitaria de Cantoblanco, 28049, Madrid, Spain;Corresponding authors at: Departamento de Química Analítica y Análisis Instrumental, c/ Francisco Tomás y Valiente, 7, Universidad Autónoma de Madrid, Ciudad Universitaria de Cantoblanco, 28049 Madrid, Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "IMDEA-Nanociencia, Ciudad Universitaria de Cantoblanco, 28049, Madrid, Spain"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "IMDEA-Nanociencia, Ciudad Universitaria de Cantoblanco, 28049, Madrid, Spain"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "IMDEA-Nanociencia, Ciudad Universitaria de Cantoblanco, 28049, Madrid, Spain"}, {"AuthorId": 6, "Name": "Encarnación Lorenzo", "Affiliation": "Departamento de Química Analítica y Análisis Instrumental, Universidad Autónoma de Madrid, 28049, Madrid, Spain;Institute for Advanced Research in Chemical Sciences (IAdChem). Universidad Autónoma de Madrid, 28049, Madrid, Spain;IMDEA-Nanociencia, Ciudad Universitaria de Cantoblanco, 28049, Madrid, Spain"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Departamento de Química Analítica y Análisis Instrumental, Universidad Autónoma de Madrid, 28049, Madrid, Spain;Institute for Advanced Research in Chemical Sciences (IAdChem). Universidad Autónoma de Madrid, 28049, Madrid, Spain;Corresponding authors at: Departamento de Química Analítica y Análisis Instrumental, c/ Francisco Tomás y Valiente, 7, Universidad Autónoma de Madrid, Ciudad Universitaria de Cantoblanco, 28049 Madrid, Spain"}], "References": []}, {"ArticleId": 89247886, "Title": "Highly sensitive and portable electrochemical detection system based on AuNPs@CuO NWs/Cu2O/CF hierarchical nanostructures for enzymeless glucose sensing", "Abstract": "We developed [email protected] NWs/Cu<sub>2</sub>O/CF hierarchical nanostructures for portable and enzymeless glucose detection. The CuO nanowires/Cu<sub>2</sub>O nanocomposites were firstly obtained by in-situ growth on three-dimensional copper foam (CuO NWs/Cu<sub>2</sub>O/CF) and Au nanoparticles ( [email protected] NWs/Cu<sub>2</sub>O/CF) were added by electrodeposition. These as-synthesized hierarchical nanostructures of [email protected] NWs/Cu<sub>2</sub>O/CF were then used as sensing electrode and placed in a polydimethylsiloxane (PDMS) chamber for preparation of sensing element. The portable electrochemical detection system was developed with the sensing element, application-specific integrated circuit (ASIC) and smartphone APP. To optimize the electrocatalytic performance of the [email protected] NWs/Cu<sub>2</sub>O/CF sensing element, the influence of Au content on enzymeless glucose detection was systemically studied by varying the Au deposition time. Our results show that the as-prepared Au<sub>100</sub> [email protected] NWs/Cu<sub>2</sub>O/CF sensor exhibits the optimal electrocatalytic properties for glucose detection, including higher sensitivity of 1.619 μA/μM/cm<sup>2</sup>, larger linear interval of 2.82000 μM and lower detection limit of 0.9 μM. Noticeably, this portable detection system exhibits outstanding electrocatalytic performance which can be used to monitor the glucose level and its changing trend in the sweat.", "Keywords": "CuO nanowires/Cu<sub>2</sub>O nanostructures ; Au nanoparticles ; Electrochemical portable detection system ; Enzymeless glucose sensor", "DOI": "10.1016/j.snb.2021.130379", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Electronic Functional Materials and Devices, Huizhou University, Huizhou, 516001, Guangdong, China;Center of Nano Energy and Devices, College of Information and Computer, Taiyuan University of Technology, Taiyuan, 030024, Shanxi, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Electronic Functional Materials and Devices, Huizhou University, Huizhou, 516001, Guangdong, China"}, {"AuthorId": 3, "Name": "Yong<PERSON>ao Sun", "Affiliation": "Center of Nano Energy and Devices, College of Information and Computer, Taiyuan University of Technology, Taiyuan, 030024, Shanxi, China"}, {"AuthorId": 4, "Name": "Cancan Zhao", "Affiliation": "Guangdong Provincial Key Laboratory of Electronic Functional Materials and Devices, Huizhou University, Huizhou, 516001, Guangdong, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Electronic Functional Materials and Devices, Huizhou University, Huizhou, 516001, Guangdong, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Guangdong Provincial Key Laboratory of Electronic Functional Materials and Devices, Huizhou University, Huizhou, 516001, Guangdong, China;Corresponding authors"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Center of Nano Energy and Devices, College of Information and Computer, Taiyuan University of Technology, Taiyuan, 030024, Shanxi, China;Corresponding authors"}, {"AuthorId": 8, "Name": "<PERSON>", "Affiliation": "PASTEUR, Département de Chimie, École Normale Supérieure, PSL University, Sorbonne Université, CNRS, 75005, Paris, France;Corresponding authors"}], "References": [{"Title": "Confinement preparation of hierarchical NiO-N-doped carbon@reduced graphene oxide microspheres for high-performance non-enzymatic detection of glucose", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "309", "Issue": "", "Page": "127779", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Fabrication of ZnO quantum dots@SnO2 hollow nanospheres hybrid hierarchical structures for effectively detecting formaldehyde", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "318", "Issue": "", "Page": "128222", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 89247887, "Title": "The effects of secondary doping on ink-jet printed PEDOT:PSS gas sensors for VOCs and NO2 detection", "Abstract": "In the study of conductive conjugated polymers, electrical doping has long played an important role. A new polymeric gas sensor has been successfully fabricated by means of an ink-jet printer using a conductive aqueous formulation of poly(3,4-ethylenedioxythiophene) poly(styrene-sulfonate) (PEDOT:PSS). A simple yet robust treatment method for the irreversible secondary doping was performed (by H<sub>2</sub>SO<sub>4</sub> and MeOH post-treatments) to enhance conductivity and improve gas sensing performance. Real time gas sensing measurements were carried out by exposing the devices with eight different analytes in a low concentrations range of VOCs vapors up to 5 % of the saturated vapor pressure, 10 ppm of NO<sub>2</sub> and up to 10 % of relative humidity (RH) at 21 °C, exploiting dry air as carrier and diluting gas. The gas response, obtained as the ratio between the steady-state resistance variation and the baseline resistance of the device, was evaluated for different PEDOT:PSS post-treated sensors. An unexpected behavior of PEDOT:PSS post-treated with concentrated H<sub>2</sub>SO<sub>4</sub> was observed, while MeOH and diluted H<sub>2</sub>SO<sub>4</sub> post-treated sensors exhibited improved response towards all investigated analytes. The best performances were obtained towards 5 % of ammonia and NO<sub>2</sub> with a gas response of 6 % and 28 % respectively with the device post-treated with pure methanol and 16 % with the sensor post-treated with diluted sulfuric acid. Furthermore, long-term stability and the influence of temperature were evaluated on the fabricated sensors. Altogether, these promising results allow a better understanding of the secondary doping effects on the electrical and sensing properties, paving the way for electronic nose development.", "Keywords": "PEDOT: PSS ; Gas sensor ; Conductive polymer ; Ink-jet printing ; Secondary doping ; Electronic nose ; VOC ; NOx", "DOI": "10.1016/j.snb.2021.130381", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Scienza Applicata e Tecnologia (DISAT), Politecnico di Torino, C.so Duca Degli Abruzzi 24, 10129, Torino, Italy"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Scienza Applicata e Tecnologia (DISAT), Politecnico di Torino, C.so Duca Degli Abruzzi 24, 10129, Torino, Italy"}, {"AuthorId": 3, "Name": "<PERSON>.<PERSON><PERSON>", "Affiliation": "Dipartimento di Scienza Applicata e Tecnologia (DISAT), Politecnico di Torino, C.so Duca Degli Abruzzi 24, 10129, Torino, Italy;CNR-IMEM, Parco Area Delle Scienze, 37a, 43124, Parma, Italy;Corresponding author at: Dipartimento di Scienza Applicata e Tecnologia (DISAT), Politecnico di Torino, C.so Duca degli Abruzzi 24, 10129, Torino, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Scienza Applicata e Tecnologia (DISAT), Politecnico di Torino, C.so Duca Degli Abruzzi 24, 10129, Torino, Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "CNR-IMEM, Parco Area Delle Scienze, 37a, 43124, Parma, Italy"}, {"AuthorId": 6, "Name": "F.C<PERSON>", "Affiliation": "Dipartimento di Scienza Applicata e Tecnologia (DISAT), Politecnico di Torino, C.so Duca Degli Abruzzi 24, 10129, Torino, Italy;Istituto Italiano di Tecnologia, Center for Sustainable Future Technologies, Via Livorno 60, Torino, 10144, Italy"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Dipartimento di Scienza Applicata e Tecnologia (DISAT), Politecnico di Torino, C.so Duca Degli Abruzzi 24, 10129, Torino, Italy;CNR-IMEM, Parco Area Delle Scienze, 37a, 43124, Parma, Italy"}], "References": []}, {"ArticleId": 89248053, "Title": "Data-Driven Tuning of PID Controlled Piezoelectric Ultrasonic Motor", "Abstract": "<p>Ultrasonic motors employ resonance to amplify the vibrations of piezoelectric actuator, offering precise positioning and relatively long travel distances and making them ideal for robotic, optical, metrology and medical applications. As operating in resonance and force transfer through friction lead to nonlinear characteristics like creep and hysteresis, it is difficult to apply model-based control, so data-driven control offers a good alternative. Data-driven techniques are used here for iterative feedback tuning of a proportional integral derivative (PID) controller parameters and comparing between different motor driving techniques, single source and dual source dual frequency (DSDF). The controller and stage system used are both produced by the company Physik Instrumente GmbH, where a PID controller is tuned with the help of four search methods: grid search, Luus–<PERSON>aakola method, genetic algorithm, and a new hybrid method developed that combines elements of grid search and <PERSON><PERSON> method. The latter method was found to be quick to converge and produced consistent result, similar to the <PERSON>us–<PERSON> method. Genetic Algorithm was much slower and produced sub optimal results. The grid search has also proven the DSDF driving method to be robust, less parameter dependent, and produces far less integral position error than the single source driving method.</p>", "Keywords": "ultrasonic motor; PID controller; data-driven control; iterative feedback tuning; genetic algorithm; Luus-Jaa<PERSON><PERSON> ultrasonic motor ; PID controller ; data-driven control ; iterative feedback tuning ; genetic algorithm ; Luus-Jaa<PERSON>la", "DOI": "10.3390/act10070148", "PubYear": 2021, "Volume": "10", "Issue": "7", "JournalId": 41395, "JournalTitle": "Actuators", "ISSN": "", "EISSN": "2076-0825", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Applied Research, Karlsruhe University of Applied Sciences, 76133 Karlsruhe, Germany"}, {"AuthorId": 2, "Name": "Bülent Delibas", "Affiliation": "Physik Instrumente (PI) GmbH & Co. KG, 76228 Karlsruhe, Germany↑Author to whom correspondence should be addressed. Academic Editors: <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Physik Instrumente (PI) GmbH & Co. KG, 76228 Karlsruhe, Germany"}], "References": []}, {"ArticleId": 89248157, "Title": "Optimization of MQL turning process considering the distribution and control of cutting fluid mist particles", "Abstract": "<p>This paper presents the effect of cutting parameters on the mist particle diameters under the minimum quantity lubrication (MQL) for reducing the air pollution and harm to operators caused by the mist particles produced during the MQL process. In this study, turning experiments were conducted on AISI 304 stainless steel using MQL, for which 16 groups of Taguchi experiments were utilized. The results show that by using the MQL, the contribution rates of the cutting speed and air pressure to the mist particle diameters were 54.62% and 25.34%, respectively. The increase in air pressure caused an increase in the overall proportion of mist particles with diameters in the range of 2.5~10 μm, from 88 up to 96%. The increase in cutting speed aided the conversion of mist particles with a diameter in the range of 2.5~10 μm to a diameter of 2.5 μm, about 16 to 27% of the 2.5~10 μm. However, the overall proportion of the mist particles was not changed. Finally, by targeting the cutting efficiency, surface roughness, and deposition of the cutting fluid mist, the four parameters were optimized using a multi-objective optimization algorithm. Compared to the 14th group experiment with a similar cutting efficiency, the verification experiment results show that the surface roughness decreased 16% and the deposition of cutting fluid mist increased 173% by using the optimized parameters.</p>", "Keywords": "MQL; Mist particles; NSGA-II; GRA; Combination weight", "DOI": "10.1007/s00170-021-07480-x", "PubYear": 2021, "Volume": "116", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Nuclear Technology and Automation Engineering, Chengdu University of Technology, Chengdu, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Chengdu Tool Research Institute Co., Ltd., Chengdu, People’s Republic of China"}], "References": [{"Title": "Effect of turning environments and parameters on surface integrity of AA6061-T6: experimental analysis, predictive modeling, and multi-criteria optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "110", "Issue": "9-10", "Page": "2669", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 89248158, "Title": "Fixed-time synchronization of fuzzy neutral-type BAM memristive inertial neural networks with proportional delays", "Abstract": "In this paper, fixed-time synchronization (Fix-TS) of fuzzy neutral-type bidirectional associative memory memristive-inertial neural networks (FNT-BAM-MINNs) with proportional delays is investigated by non-reduced order method. First, a new lemma is proposed to solve the parameter mismatch of FNT-BAM-MINNs on the basis of differential inclusion theories and analysis methods. Second, the delay-dependent switched controller and the delay-dependent fuzzy switched controller are designed to reach the Fix-TS of the drive-response FNT-BAM-MINNs, respectively. Based on fixed-time stability theory, inequality techniques and the new lemma, some delay-independent sufficient conditions are given to guarantee the Fix-TS of FNT-BAM-MINNs, which are easy to implement in practice. Furthermore, the settling time independent of the initial value is estimated. Finally, numerical examples indicate that our results are effective. Introduction Neural networks (NNs), especially bidirectional associative memory neural networks(BAMNNs) [1], [2] and memristive neural networks (MNNs) [3], have been widely studied and applied in signal and image processing, pattern recognition, and artificial intelligence for their good information storage and associative memory performance. These applications heavily relied on the dynamic behavior of the NNs [4], [5], [6], [7], [8], [9]. <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> first proposed inertial neural networks (INNs) [10] and discussed the chaotic and bifurcation dynamics of the second-order differential equation, which are very attractive. The inertia term is a core tool of generating bifurcation and chaos. In the practical application of NNs, inertia term helps the disordered search of memory. INNs can be used as a research model in many fields such as physics, electronics, biology and mechanical engineering. It has very rich dynamic characteristics such as periodic motion, quasi-periodic motion and chaos [10], [11], [12]. Nowadays, there are many results [13], [14], [15], [16], [17], [18], [19], [20], [21] on the dynamic behavior of INNs. However, most of these results were obtained by transforming second-order differential equations into first-order differential equations using variable transformation method [10], [11], [12], [13], [14], [15]. The research method of variable transformation is correct on the basis of mathematics, but this method adds unnecessary dimensions to the INNs. In particular, the design of the controller may be more complicated and difficult to implement synchronization of the INNs. Therefore, it is more meaningful and realistic to use the non-reduced order method when studying INNs. It is worth mentioning that only several scholars directly analyzed the dynamic behavior of INNs using the non-reduced order method in [16], [17], [18], [19], [20], [21]. For example, without transforming the second order INNs into the first order differential systems by some variable substitutions, asymptotic stability and synchronization for a class of delayed INNs were investigated in [16]. Zhang et al. investigated a class of memristive inertial neural networks (MINNs) with mixed time-varying delays and obtained several new results ensuring global stability of the MINNs via non-reduced order method in [19]. Several new results were derived to ensure the exponential synchronization of switched NNs by using a novel hybrid control scheme and non-reduced order method in [21]. It is generally known that time delays and uncertainty factors are unavoidable in NNs and they lead to NNs instability and poor performance. In many practical problems, the state of a NN is not only related to the current specific state but also related to its past state and the changes of its past state. Therefore, a neutral-type (NT) NN is introduced as a broader class of time-delay NNs. As we all know, proportional delays have good application prospects in terms of web services [22]. In addition, it is not difficult to find that MNNs is a discontinuous system with state switched on the right-hand sides of the equation. Recent years, there are many researches on the dynamic behavior of bidirectional associative memory INNs (BAMINNs) [23], [24], [25] and MINNs [26], [27] with time delays and uncertainty factors. For example, some delay-dependent sufficient conditions were established to guarantee global asymptotic stability of NT-BAMINNs in [24] by using variable transforming. Without reducing the second order terms into first order, Jian et al. analyzed the passivity for uncertain BAMINNs with time-varying delays in [25]. Yu et al. investigated the robust exponential stability of fuzzy switched MINNs with time-varying delays on mode-dependent destabilizing impulsive control protocol in [26]. By employing some famous inequalities, Zhang et al. studied robust dissipativity for delayed MINNs in [27]. Nevertheless, there are few results that consider the effects of memristors, inertial terms, fuzzy, and neutral types on NNs at the same time. This is one of the purposes of our study of this article. Synchronization is one of the most important dynamic behaviors of NNs, which has been widely used in secure communication and artificial intelligence. Pecora and Carroll studied the synchronization of drive-response chaotic system for the first time in [28]. They pointed out that the behavior of drive system can affect the behavior of response system, and drive system does not depend on response system. In recent years, various synchronization of drive-response NNs have been studied, such as asymptotic synchronization [21], [29], [30], [31], [32], [33], [34], finite-time synchronization (Fin-TS) [15], [35], [36] etc. Based on the generalized matrix measure concept, Xiao et al. obtained several criteria to guarantee the global exponential stability of generalized discrete-time INNS in [29]. Wang et al. investigated the synchronization of multiple MNNs under cyber-physical attacks through distributed event-triggered control in [33]. Zhang et al. studied the exponential synchronization problem of memristive recurrent NNs by switching matrix approach in [34]. Yang et al. discussed the Fin-TS of coupled MINNs by selecting a suitable variable substitution in [35]. By constructing two Lyapunov functions and using integral inequality method, Cao et al. obtained some new synchronization criteria of the Fin-TS for above drive-response delayed INNs in [36]. Compared with asymptotic synchronization, Fin-TS is more widely used in practical applications because of its faster convergence speed. It is generally known that the settling time of Fin-TS is strictly related to the initial value of the drive-response NNs. However, the initial value is not easy to be measured or estimated in many practical the NNs, which greatly limits the application of Fin-TS control technology. In the production process of a factory, people often need to predict the time or a time range of the synchronization target to realize the rational use and optimization of resources. Fortunately, Polyakov first introduced the definition of fixed-time stability and established a settling time upper bound independent of the initial synchronization error in [37]. Fix-TS is independent of the initial value of the system, which is more advantageous and practical than Fin-TS. Therefore, many scholars were attracted to study Fix-TS [38], [39], [40], [41], [42], [43] for NNs. Based on the differential inclusion principle and the Lyapunov functional method, Wei et al. considered Fix-TS problem for quaternion-valued MNNs in [38]. Yang et al. investigated the Fix-TS of MNNs with time-delayed and coupled in [39]. Zhang et al. studied the Fix-TS of a kind of MINNs with impulse effect in [40]. Chen et al. designed four different kinds of feedback controllers and discussed the Fix-TS control of MINNs with discrete delay in [41]. Guo et al. investigated the Fix-TS of complex-valued memristive BAMNNs (MBAMNNs) with leakage time-varying delay and applications in image encryption and decryption in [42]. By applying of differential inclusion and set-valued map theories, Zheng et al. studied the Fix-TS of fuzzy memristive BAM cellular NNs (F-MBAMCNNs) with time-varying delays in [43]. These studies [38], [39], [40], [41], [42], [43] play an important role in the theoretical research and application of Fix-TS for NNs. In addition, most of previous studies on the Fix-TS of INNs used reduced order method. For example, by choosing proper variable transformation, Fin-TS of fuzzy neutral-type (FNT) INNs with time-varying coefficients and proportional delays was obtained in [44]. Zhang et al. investigated Fix-TS for complex-valued BAMNNS in [45]. Nevertheless, Fix-TS of FNT-BAM-MINNs with proportional delays via non-reduced order method has not been reported up to now. Based on previous researches and the inspiration of the above analysis, we will study Fix-TS of FNT-BAM-MINNs with proportional delays via non-reduced order method. The main contributions are mainly divided into the following aspects: (1) This paper establishes a kind of FNT-BAM-MINNs with memristive synaptic connections, bidirectional associative memory, inertial terms, fuzzy terms, neutral and proportional delays, and extends the previous published articles [13], [14], [15], [16], [17], [18], [20], [21], [39], [40], [44], [46]. (2) In this paper, Fix-TS of FNT-BAM-MINNs with proportional delays is studied via non-reduced order method. (3) Two delay-dependent switched controllers are designed to achieve Fix-TS of the FNT-BAM-MINNs with proportional delays and some delay-independent sufficient conditions are obtained for above Fix-TS. The proposed controllers are suitable for the study of Fix-TS and Fin-TS in general BAM-MINNs via non-reduced order method. (4) Parameter mismatch of state switched systems is solved by a new lemma and all the results avoid the limitation of conservative assumptions about parameters mismatch [8], [9], [38]. The main structure of this paper is organized as follows. Section 2 introduces the drive-response FNT-BAM-MINNs and some preliminaries. Main results are given in Section 3. Two examples illustrate the effectiveness of our main results in Section 4. Section 5 is conclusions and future works. Figures Trajectories of errors for (1), (2) with Q1,Q2,Q3 by controller (4). Trajectories for derivatives of errors of (1), (2) with Q1,Q2,Q3 by controller (4). Trajectories of errors for (1), (2) with Q1,Q2,Q3 by controller (21). Trajectories for derivatives of errors of (1), (2) with Q1,Q2,Q3 by controller (21). Section snippets Preliminaries In order to ensure the fluency of the paper, we have summarized some symbols commonly used in this paper in Table 1, and i ∈ I = { 1 , 2 , ⋯ , k } , j ∈ J = { 1 , 2 , ⋯ , m } . Consider the following FNT-BAM-MINNs with proportional delays, u ¨ i ( t ) = - α ́ i ( u i ( t ) ) u ̇ i ( t ) - β ́ i ( u i ( t ) ) u i ( t ) + N ́ i ( u ( t ) , s ( t ) ) + Δ ́ i ( s ( t ) ) + M ́ i ( t ) , s ¨ j ( t ) = - α ̀ j ( s j ( t ) ) s ̇ j ( t ) - β ̀ j ( s j ( t ) ) s j ( t ) + N ̀ j ( s ( t ) , u ( t ) ) + Δ ̀ j ( u ( t ) ) + M ̀ j ( t ) , where N ́ i ( u ( t ) , s ( t ) ) = ∑ j = 1 m λ ́ ij ( u i ( t ) ) g ́ j ( s j ( t ) ) + ∑ j = 1 m θ ́ ij ( u i ( t ) ) g ́ j ( s j ( p ij t ) ) + ∑ j = 1 m F ́ ij ( u i ( t ) ) g ́ j ( s ̇ j ( p ij t ) ) , Δ ́ i ( s ( t ) ) = ⋀ j = 1 m H ́ ij ( t ) g ́ j ( s j ( p ij t ) Main results In this section, we consider the Fix-TS of BAM-FNT-MINNs with different control inputs. We design control inputs U ́ i ( t ) , U ̀ j ( t ) as follows: U ́ i ( t ) = Ξ ́ i ( e i ( t ) , e ̇ i ( t ) ) - Δ ́ i ( w ( t ) ) - Ω i ( t ) , U ̀ j ( t ) = Ξ ̀ j ( w j ( t ) , w ̇ j ( t ) ) - Δ ̀ j ( e ( t ) ) - Γ j ( t ) , where Ξ ́ i ( e i ( t ) , e ̇ i ( t ) ) = - sign ( e ̇ i ( t ) ) × ρ 2 i   e i t   + ρ 3 i   e ̇ i t   φ 1 - 1 + ρ 4 i   e ̇ i t   φ 2 - 1 + ρ 5 i   e i t   φ 1 - 1 + ρ 6 i   e i t   φ 2 - 1 , Ξ ̀ j ( w j ( t ) , w ̇ j ( t ) ) = - sign ( w ̇ j ( t ) ) × ϱ 2 j   w j ( t )   + ϱ 3 j   w ̇ j ( t )   φ 1 - 1 + ϱ 4 j   w ̇ j ( t )   φ 2 - 1 + ϱ 5 j   w j ( t )   φ 1 - 1 + ϱ 6 j   w j ( t )   φ 2 - 1 , Δ ́ i ( w ( t ) ) , Δ ̀ j ( e ( t ) ) are similar to Δ ́ i ( s ( t ) ) , Δ ̀ j ( u ( t ) ) defined in (1), Examples Example 1 When i , j = 1 , 2 and t ⩾ t 0 = 0.2 , we consider drive NN (1) and response NN (2) with the following parameters (mark ∗ represent x i ( t ) and u i ( t ) , mark ★ represent y j ( t ) and s j ( t ) ): α ́ i ( ∗ ) = 20 ,   ∗   ≤ 0.2 14 ,   ∗   > 0.2 , β ́ i ( ∗ ) = 21 ,   ∗   ≤ 0.2 18 ,   ∗   > 0.2 , λ ́ i 1 ( ∗ ) = 0.2 ,   ∗   ≤ 0.2 0.9 ,   ∗   > 0.2 , λ ́ i 2 ( ∗ ) = - 1.2 ,   ∗   ≤ 0.2 1.2 ,   ∗   > 0.2 , θ ́ i 1 ( ∗ ) = 0.5 ,   ∗   ≤ 0.2 3 ,   ∗   > 0.2 , θ ́ i 2 ( ∗ ) = 0.2 ,   ∗   ≤ 0.2 2 ,   ∗   > 0.2 , F ́ i 1 ( ∗ ) = 1.2 ,   ∗   ≤ 0.2 0.4 ,   ∗   > 0.2 , F ́ i 2 ( ∗ ) = - 1.2 ,   ∗   ≤ 0.2 0.8 ,   ∗   > 0.2 , α ̀ j ( ★ ) = 17 ,   ★   ⩽ 0.5 20 ,   ★   > 0.5 , β ̀ j ( ★ ) = 18 ,   ★   ⩽ 0.5 19 ,   ★   > 0.5 , λ ̀ j 1 ( ★ ) = - 0.2 ,   ★   ⩽ 0.5 0.6 ,   ★   > 0.5 , λ Conclusions Based on fixed-time stability theories, differential inclusion theories and inequality techniques, we give a new lemma to solve the parameter mismatch problem of state switched FNT-BAM-MINNs and study the Fix-TS for FNT-BAM-MINNs with proportional delays via non-reduced order method. Some simple and delay-independent sufficient conditions for Fix-TS of FNT-BAM-MINNs are obtained by designed the delay-dependent switched controller and the delay-dependent switched fuzzy controller. At the same CRediT authorship contribution statement Liyan Duan: Conceptualization, Software, Methodology, Writing - original draft. Junmin Li: Validation, Formal analysis, Resources, Investigation, Supervision, Writing - review & editing. Declaration of competing interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments The authors are grateful for the support of the National Natural Science Foundation of China ( 61573013 ) and the Fundamental Research Funds for the Central Universities ( YJS2107 ). References (48) L.Y. Duan et al. Global exponential dissipativity of neutral-type BAM inertial neural networks with mixed time-varying delays Neurocomputing (2020) J.G. Jian et al. Finite-time synchronization for fuzzy neutral-type inertial neural networks with time-varying coefficients and proportional delays Fuzzy Sets Syst. (2020) C. Chen et al. Fixed-time synchronization of inertial memristor-based neural networks with discrete delay Neural Networks (2019) Y.L. Zhang et al. Fixed-time synchronization of the impulsive memristor-based neural networks Commun. Nonlinear Sci. Numer. Simul. (2019) C. Yang et al. Fixed-time synchronization of coupled memristor-based neural networks with time-varying delays Neural Networks (2019) R.Y. Wei et al. Fixed-time synchronization of quaternion-valued memristive neural networks with time delays Neural Networks (2019) R.M. Zhang et al. A new method for exponential synchronization of memristive recurrent neural networks Inf. Sci. (2018) S.B. Wang et al. Event-triggered distributed control for synchronization of multiple memristive neural networks under cyber-physical attacks Inf. Sci. (2020) R.M. Zhang et al. Novel discontinuous control for exponential synchronization of memristive recurrent neural networks with heterogeneous time-varying delays J. Franklin Inst. (2018) M.S. Zhang et al. Robust dissipativity analysis for delayed memristor-based inertial neural network Neurocomputing (2019) M.Y. Yan et al. Passivity analysis for uncertain BAM inertial neural networks with time-varying delays Neurocomputing (2021) G.D. Zhang et al. Novel results on synchronization for a class of switched inertial neural networks with distributed delays Inf. Sci. (2020) C.X. Huang et al. New studies on dynamic analysis of inertial neural networks involving non-reduced order method Neurocomputing (2019) X.Y. Li et al. Some new results on stability and synchronization for delayed inertial neural networks based on non-reduced order method Neural Networks (2017) Z.Q. Zhang et al. Further study on finite-time synchronization for delayed inertial neural networks via inequality skills Neurocomputing (2020) Z.W. Tu et al. Global exponential stability in Lagrange sense for inertial neural networks with time-varying delays Neurocomputing (2016) C.J. Xu et al. Existence and global exponential stability of anti-periodic solutions for BAM neural networks with inertial term and delay Neurocomputing (2015) D.W. Wheeler et al. Stability and chaos in an inertial two-neuron system Physica D (1997) K. Babcock et al. Dynamics of simple electronic neural networks Physica D (1987) G.D. Zhang et al. Global exponential periodicity and stability of a class of memristor-based recurrent neural networks with multiple delays Inf. Sci. (2013) A.L. Wu et al. Synchronization control of a class of memristor-based recurrent neural networks Inf. Sci. (2012) L.S. Wang et al. Global dissipativity of a class of BAM neural networks with both time-varying and continuously distributed delays Neurocomputing (2015) Z.W. Tu et al. Global dissipativity of a class of BAM neural networks with time-varying and unbound delays Commun. Nonlinear Sci. Numer. Simul. (2013) A.P. Chen et al. Existence and stability of almost periodic solution for BAM neural networks with delays Appl. Math. Comput. (2003) View more references Cited by (0) Recommended articles (6) Research article An efficient dictionary-based multi-view learning method Information Sciences, Volume 576, 2021, pp. 157-172 Show abstract Multi-view learning can be considered as a kind of classification method which explores common and unique information among different views. For dictionary learning, it can identify informative features by learning sparse representation of samples and has great advantages for classification. However, there are few researches on the problem of multi-view learning with dictionary learning. In order to improve the performance of multi-view classification, we propose a new multi-view dictionary learning with consensus of view(MVDL-CV). First of all, we learn a particular dictionary for each view and obtain the sparse representation of the sample. Then, by utilizing the regularization term between two dictionaries in consensus, we can determine the similarity of samples and obtain the discriminative sparse representation, which can be helpful to construct the improved classifiers. Further, we obtain the solution of the model through an alternating convex optimization method and present the convergence analysis of MVDL-CV. In the experiments, we compare the proposed method with previous multi-view learning methods, and the experimental results show that MVDL-CV is a feasible and competitive method. Research article Exploring the impact of node mobility on cascading failures in spatial networks Information Sciences, Volume 576, 2021, pp. 140-156 Show abstract Existing researches on cascading failures mainly focus on static spatial networks, but rarely consider network scenarios where mobile nodes and static nodes coexist. Therefore, in this work, we explore the impact of node mobility on cascading failures in spatial networks. We first develop a cascading model for static-mobile spatial network systems. In this model, we use the general betweenness to characterize the load of static nodes in the network, and adopt the Gauss–Markov mobility model to generate the movement trajectory of mobile nodes. On this basis, we develop three node interaction modes ( i.e., all-connection mode, high-load priority mode and low-load priority mode) to characterize the interaction between static nodes and mobile nodes. Experimental results have shown that 1) unlike the traditional cascading process that is a continuous process, the cascading process of static-mobile spatial networks consists of multiple cascading processes that occur at different times; 2) expanding the network size and reducing the number of mobile nodes can help the network resist cascading failures; 3) there is a tolerance space for network configuration parameters. When the configuration parameters fall into this space, the network can avoid cascading failures; 4) among the three interaction modes, the network robustness in all-connection mode is the worst, followed by low-load priority mode, and finally high-load priority mode. The obtained results can provide theoretical guidance for users to establish a more robust static-mobile spatial network. Research article Continual learning in sensor-based human activity recognition: An empirical benchmark analysis Information Sciences, Volume 575, 2021, pp. 1-21 Show abstract Sensor-based human activity recognition (HAR), i.e., the ability to discover human daily activity patterns from wearable or embedded sensors, is a key enabler for many real-world applications in smart homes, personal healthcare, and urban planning. However, with an increasing number of applications being deployed, an important question arises: how can a HAR system autonomously learn new activities over a long period of time without being re-engineered from scratch? This problem is known as continual learning and has been particularly popular in the domain of computer vision, where several techniques to attack it have been developed. This paper aims to assess to what extent such continual learning techniques can be applied to the HAR domain. To this end, we propose a general framework to evaluate the performance of such techniques on various types of commonly used HAR datasets. Then, we present a comprehensive empirical analysis of their computational cost and of their effectiveness of tackling HAR specific challenges (i.e., sensor noise and labels’ scarcity). The presented results uncover useful insights on their applicability and suggest future research directions for HAR systems. Research article An Interpretable Neural Fuzzy Hammerstein-Wiener Network for Stock Price Prediction Information Sciences, 2021 Show abstract An interpretable regression model is proposed in this paper for stock price prediction. Conventional offline neuro-fuzzy systems are only able to generate implications based on fuzzy rules induced during training, which requires the training data to be able to adequately represent all system behaviors. However, the distributions of test and training data could be significantly different, e.g., due to drastic data shifts. We address this problem through a novel approach that integrates a neuro-fuzzy system with the Hammerstein-Wiener model forming an indivisible five-layer network, where the implication of the neuro-fuzzy system is realized by the linear dynamic computation of the Hammerstein-Wiener model. The input and output nonlinearities of the Hammerstein-Wiener model are replaced by the nonlinear fuzzification and defuzzification processes of the fuzzy system so that the fuzzy linguistic rules, induced from the linear dynamic computation, can be used to interpret the inference processes. The effectiveness of the proposed model is evaluated on three financial stock datasets. Experimental results showed that the proposed Neural Fuzzy Hammerstein-Wiener ( NFHW ) outperforms other neuro-fuzzy systems and the conventional Hammerstein-Wiener model on these three datasets. Research article A three-way decision method based on fuzzy rough set models under incomplete environments Information Sciences, Volume 577, 2021, pp. 22-48 Show abstract The paper primarily explores the applicability of three-way decision (TWD) to multi-attribute decision-making (MADM), and establishes a new three-way multi-attribute decision-making (TW-MADM) method under an incomplete environment. For the sake of making rational decisions for MADM problems with fuzzy values, fuzzy rough set models are first utilized to investigate a new TWD model. By taking into account the hesitation degree of each evaluation value, a data-driven method to determine the relative loss functions is presented. Moreover, a new conditional probability calculation method is put forth via the information granularity of each object. In light of the above statement, a novel TWD model with three strategies is proposed. Afterwards, the arithmetic mean method is adopted for patching the lost data to effectively address incomplete MADM problems. Given the uncertainty of the patched data and real data, a new TW-MADM method as well as a corresponding MADM algorithm is designed. By several comparative analysis and experimental analysis, the feasibility, effectiveness, superiority and stability of the method are demonstrated. In addition, the results show that the presented method with optimistic strategies is more viable and stable than the method with compromise and pessimistic strategies. Research article Integrating multiple genomic imaging data for the study of lung metastasis in sarcomas using multi-dimensional constrained joint non-negative matrix factorization Information Sciences, Volume 576, 2021, pp. 24-36 Show abstract Integrative analysis of histopathology images and genomic data enables the discovery of potential biomarkers and multimodal association patterns. However, few studies have established effective association models for complex diseases, such as sarcoma, by combining histopathological images with multiple genetic variation data. Here, we present an integrative multiple genomic imaging framework called multi-dimensional constrained joint non-negative matrix factorization (MDJNMF) to identify modules related to lung metastasis of sarcomas based on sample-matched whole-solid image, DNA methylation, and copy number variation features. Three types of feature matrices were projected onto a common feature space, in which heterogeneous variables with large coefficients in the same projected direction form a common module. The correlation between image features and genetic variation features is used as network-regularized constraints to improve the module accuracy. Sparsity and orthogonal constraints are utilized to achieve the modular sparse solution. Multi-level analysis indicates that our method effectively discovers biologically functional modules associated with sarcoma or lung metastasis. The representative module reveals a significant correlation between image features and genetic variation features and excavates potential diagnostic biomarkers. In summary, the proposed method provides new clues for identifying association patterns and biomarkers using multiple types of data sources for other diseases. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.093", "PubYear": 2021, "Volume": "576", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an 710071, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematics and Statistics, Xidian University, Xi’an 710071, PR China;Corresponding author"}], "References": [{"Title": "Novel results on synchronization for a class of switched inertial neural networks with distributed delays", "Authors": "<PERSON><PERSON> Zhang; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "511", "Issue": "", "Page": "114", "JournalTitle": "Information Sciences"}, {"Title": "Further study on finite-time synchronization for delayed inertial neural networks via inequality skills", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "373", "Issue": "", "Page": "15", "JournalTitle": "Neurocomputing"}, {"Title": "Global exponential dissipativity of neutral-type BAM inertial neural networks with mixed time-varying delays", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "399", "JournalTitle": "Neurocomputing"}, {"Title": "Finite-Time Synchronization of Coupled Inertial Memristive Neural Networks with Mixed Delays via Nonlinear Feedback Control", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "51", "Issue": "2", "Page": "1921", "JournalTitle": "Neural Processing Letters"}, {"Title": "Event-triggered distributed control for synchronization of multiple memristive neural networks under cyber-physical attacks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "518", "Issue": "", "Page": "361", "JournalTitle": "Information Sciences"}, {"Title": "Passivity analysis for uncertain BAM inertial neural networks with time-varying delays", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "435", "Issue": "", "Page": "114", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 89248169, "Title": "DG-GMsFEM for Problems in Perforated Domains with Non-Homogeneous Boundary Conditions", "Abstract": "<p>Problems in perforated media are complex and require high resolution grid construction to capture complex irregular perforation boundaries leading to the large discrete system of equations. In this paper, we develop a multiscale model reduction technique based on the Discontinuous Galerkin Generalized Multiscale Finite Element Method (DG-GMsFEM) for problems in perforated domains with non-homogeneous boundary conditions on perforations. This method implies division of the perforated domain into several non-overlapping subdomains constructing local multiscale basis functions for each. We use two types of multiscale basis functions, which are constructed by imposing suitable non-homogeneous boundary conditions on subdomain boundary and perforation boundary. The construction of these basis functions contains two steps: (1) snapshot space construction and (2) solution of local spectral problems for dimension reduction in the snapshot space. The presented method is used to solve different model problems: elliptic, parabolic, elastic, and thermoelastic equations with non-homogeneous boundary conditions on perforations. The concepts for coarse grid construction and definition of the local domains are presented and investigated numerically. Numerical results for two test cases with homogeneous and non-homogeneous boundary conditions are included, as well. For the case with homogeneous boundary conditions on perforations, results are shown using only local basis functions with non-homogeneous boundary condition on subdomain boundary and homogeneous boundary condition on perforation boundary. Both types of basis functions are needed in order to obtain accurate solutions, and they are shown for problems with non-homogeneous boundary conditions on perforations. The numerical results show that the proposed method provides good results with a significant reduction of the system size.</p>", "Keywords": "multiscale method; discontinuous Galerkin; finite element method; perforated domain; non-homogeneous boundary condition; GMsFEM; multiscale model reduction; thermoelasticity problem multiscale method ; discontinuous Galerkin ; finite element method ; perforated domain ; non-homogeneous boundary condition ; GMsFEM ; multiscale model reduction ; thermoelasticity problem", "DOI": "10.3390/computation9070075", "PubYear": 2021, "Volume": "9", "Issue": "7", "JournalId": 29449, "JournalTitle": "Computation", "ISSN": "", "EISSN": "2079-3197", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Multiscale Model Reduction Laboratory, North-Eastern Federal University, 677000 Yakutsk, Russia↑Yakutsk Branch of the Regional Scientific and Educational Mathematical Center “Far Eastern Center of Mathematical Research”, North-Eastern Federal University, 677000 Yakutsk, Russia↑Author to whom correspondence should be addressed. Academic Editor: <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Center of Innovation for Flow through Porous Media, University of Wyoming, Laramie, WY 82071, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Multiscale Model Reduction Laboratory, North-Eastern Federal University, 677000 Yakutsk, Russia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, The Chinese University of Hong Kong, Shatin, New Territories, Hong Kong 999077, China"}], "References": [{"Title": "Machine learning for accelerating macroscopic parameters prediction for poroelasticity problem in stochastic media", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "84", "Issue": "", "Page": "185", "JournalTitle": "Computers & Mathematics with Applications"}]}, {"ArticleId": 89248173, "Title": "Learnable low-rank latent dictionary for subspace clustering", "Abstract": "Recently, Self-Expressive-based Subspace Clustering (SESC) has been widely applied in pattern clustering and machine learning as it aims to learn a representation that can faithfully reflect the correlation between data points. However, most existing SESC methods directly use the original data as the dictionary, which miss the intrinsic structure (e.g., low-rank and nonlinear) of the real-word data. To address this problem, we propose a novel Projection Low-Rank Subspace Clustering (PLRSC) method by integrating feature extraction and subspace clustering into a unified framework. In particular, PLRSC learns a projection transformation to extract the low-dimensional features and utilizes a low-rank regularizer to ensure the informative and important structures of the extracted features. The extracted low-rank features effectively enhance the self-expressive property of the dictionary. Furthermore, we extend PLRSC to a nonlinear version (i.e., NPLRSC) by integrating a nonlinear activator into the projection transformation. NPLRSC cannot only effectively extract features but also guarantee the data structure of the extracted features. The corresponding optimization problem is solved by the Alternating Direction Method (ADM), and we also prove that the algorithm converges to a stationary point. Experimental results on the real-world datasets validate the superior of our model over the existing subspace clustering methods.", "Keywords": "Subspace clustering ; Low-rank ; Feature extraction ; Block diagonal representation", "DOI": "10.1016/j.patcog.2021.108142", "PubYear": 2021, "Volume": "120", "Issue": "", "JournalId": 1835, "JournalTitle": "Pattern Recognition", "ISSN": "0031-3203", "EISSN": "1873-5142", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, and Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Jiangsu 210094 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "RIKEN Center for Advanced Intelligence Project, Tokyo 103-0027, Japan"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, and Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Jiangsu 210094 China;Corresponding authors"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "JD Finance America Corporation, USA"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "PCA Lab, Key Lab of Intelligent Perception and Systems for High-Dimensional Information of Ministry of Education, and Jiangsu Key Lab of Image and Video Understanding for Social Security, School of Computer Science and Engineering, Nanjing University of Science and Technology, Jiangsu 210094 China;Corresponding authors"}], "References": []}, {"ArticleId": 89248209, "Title": "The Human Digitalisation Journey: Technology First at the Expense of Humans?", "Abstract": "<p>The ongoing COVID-19 pandemic has enhanced the impact of digitalisation as a driver of transformation and advancements across almost every aspect of human life. With the majority actively embracing smart technologies and their benefits, the journey of human digitalisation has begun. Will human beings continue to remain solitary unaffected beings in the middle of the whirlpool—a gateway to the completely digitalised future? This journey of human digitalisation probably started much earlier, before we even realised. This paper, in the format of an objective review and discussion, aims to investigate the journey of human digitalisation, explore the reality of domination between technology and humans, provide a better understanding of the human value and human vulnerability in this fast transforming digital era, so as to achieve valuable and insightful suggestion on the future direction of the human digitalisation journey.</p>", "Keywords": "human digitalisation; human value; human vulnerability; technology dependence; artificial intelligence; intelligence augmentation human digitalisation ; human value ; human vulnerability ; technology dependence ; artificial intelligence ; intelligence augmentation", "DOI": "10.3390/info12070267", "PubYear": 2021, "Volume": "12", "Issue": "7", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Research Institute of Energy Management and Planning, University of Tehran, Tehran 1417466191, Iran↑Department of Business and Management, Webster Vienna Private University, 1020 Vienna, Austria↑Author to whom correspondence should be addressed. Academic Editors: <PERSON> and <PERSON>"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Business and Law, De Montfort University, Leicester LE1 9BH, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Fashion Business School, London College of Fashion, University of the Arts London, London WC1V 7EY, UK"}], "References": [{"Title": "The Ethics of AI Ethics: An Evaluation of Guidelines", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "30", "Issue": "1", "Page": "99", "JournalTitle": "Minds and Machines"}, {"Title": "Artificial Intelligence (AI) or Intelligence Augmentation (IA): What Is the Future?", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "2", "Page": "143", "JournalTitle": "AI"}, {"Title": "From local explanations to global understanding with explainable AI for trees", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "1", "Page": "56", "JournalTitle": "Nature Machine Intelligence"}, {"Title": "The effects of explainability and causability on perception, trust, and acceptance: Implications for explainable AI", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "146", "Issue": "", "Page": "102551", "JournalTitle": "International Journal of Human-Computer Studies"}]}, {"ArticleId": 89248210, "Title": "Distributed Hypothesis Testing over Noisy Broadcast Channels", "Abstract": "<p>This paper studies binary hypothesis testing with a single sensor that communicates with two decision centers over a memoryless broadcast channel. The main focus lies on the tradeoff between the two type-II error exponents achievable at the two decision centers. In our proposed scheme, we can partially mitigate this tradeoff when the transmitter has a probability larger than 1/2 to distinguish the alternate hypotheses at the decision centers, i.e., the hypotheses under which the decision centers wish to maximize their error exponents. In the cases where these hypotheses cannot be distinguished at the transmitter (because both decision centers have the same alternative hypothesis or because the transmitter’s observations have the same marginal distribution under both hypotheses), our scheme shows an important tradeoff between the two exponents. The results in this paper thus reinforce the previous conclusions drawn for a setup where communication is over a common noiseless link. Compared to such a noiseless scenario, here, however, we observe that even when the transmitter can distinguish the two hypotheses, a small exponent tradeoff can persist, simply because the noise in the channel prevents the transmitter to perfectly describe its guess of the hypothesis to the two decision centers.</p>", "Keywords": "hypothesis testing; broadcast channel; error exponents hypothesis testing ; broadcast channel ; error exponents", "DOI": "10.3390/info12070268", "PubYear": 2021, "Volume": "12", "Issue": "7", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Computer Engineering, College of Engineering, University of Tehran, Tehran 1433957131, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "LTCI, Telecom Paris, IP Paris, 91120 Paris, France↑Author to whom correspondence should be addressed.↑M<PERSON> has received funding from the European Research Council (ERC) under the European Union’s Horizon 2020 programme, grant agreement No 715111. Academic Editor: Shraga I. Bross"}], "References": []}, {"ArticleId": 89248276, "Title": "DSAGAN: A generative adversarial network based on dual-stream attention mechanism for anatomical and functional image fusion", "Abstract": "In recent years, extensive multimodal medical image fusion algorithms have been proposed. However, existing methods are primarily based on specific transformation theories. There are many problems with existing algorithms, such as poor adaptability, low efficiency and blurry details. To address these problems, this paper proposes a generative adversarial network based on dual-stream attention mechanism (DSAGAN) for anatomical and functional image fusion. The dual-stream architecture and multiscale convolutions are utilized to extract deep features. In addition, the attention mechanism is utilized to further enhance the fused features. Then, the fusion images and multimodal input images are put into the discriminator . In the update stage of the discriminator, we expect to judge the multimodal images as real, and to judge the fusion images as fake. Furthermore, the fusion images are expected to be judged as real in the update stage of the generator, forcing the generator to improve the fusion quality. The training process continues until the generator and discriminator reach a Nash equilibrium . After training, the fusion images can be obtained directly after inputting anatomical and functional images. Compared with the reference algorithms , DSAGAN consumes less fusion time and achieves better objective metrics in terms of Q <sub>AG</sub> , Q <sub>EN</sub> and Q <sub>NIQE</sub> . Introduction With the development of computer technology, multimodal medical images have been widely used in the medical field. Commonly used multimodal images include anatomical and functional images. Anatomical images include MRI (magnetic resonance images), and functional images include PET (positron emission tomography) and SPECT (single photon emission computed tomography) images. Generally, single equipment produces single modal images. The anatomical structure displayed by MRI is very clear. In addition, lesions are displayed clearly in anatomical background, making the relationship between lesions and anatomical structure much closer. PET and SPECT images accurately reflect information on cell metabolic activity. However, because lesions are not independent, there is a strong correlation between the lesions and abnormal information of the surrounding tissues. Single modal medical images have limited pathological information, which is not enough for disease diagnosis. The purpose of medical image fusion algorithms is to combine the multimodal images into one image, while preserving the original information. Therefore, the fusion images have multimodal information and accurately reveal the relationship between the lesions and surrounding tissues, providing objective referential information for medical technologies such as computer-aided diagnosis, disease analysis, and operations. Recently, a large number of image fusion algorithms have been proposed [1], [2], [3], [4], [5]. The existing algorithms basically include three processes: image transformation, fusion rules and inverse transformation. Image transformation is the process of transforming the image space to other spaces using a specific algorithm for image processing and analysis, and image inverse transformation is the process of transforming other spaces to the image space, which is the inverse operation of image transformation. A specific transformation domain is utilized to complete the image transformation process. The fusion rule is a specific mathematical algorithm for feature fusion. The purpose of the fusion rule is to realize the fusion of two or more features into a single feature. Recently, themultiscale transformation (MST) has become a common transformation domain due to its advantage of high flexibility. MST utilizes flexible fusion rules to extract the information of different scales to improve the fusion quality. Chen et al. [6] proposed an infrared and visible image fusion algorithm based on target-enhanced MST. Currently, many MST-based fusion algorithms have been proposed, such as wavelets [7], [8], shearlets [9], [10], pyramids [11], [12], nonsubsampled shearlet transformation (NSST) [13], [14], and nonsubsampled contourlet transformation (NSCT) [15], [16], [17]. However, in the process of image transformation and inverse transformation, MST-based fusion algorithms are prone to information loss, resulting in blurry details and edge degradation. Then, many image fusion algorithms based on the SR (sparse representation) model [18], [19], [20] have been proposed due to the rapid development of machine learning. Liu and Wang [21] proposed a fusion algorithm based on adaptive sparse representation. Zhang and Levine [22] proposed a multitask sparse representation fusion algorithm. Li et al. [23] proposed a fusion and denoising algorithm based on group sparse representation. SR-based fusion methods mainly rely on optimizing dictionaries, and these methods have the advantages of saving storage space and a wide application range. However, SR-based fusion algorithms depend on the results of optimization models. Different optimization algorithms and fusion strategies have different influences on the fusion quality. In addition, optimization is a very complicated process. Most existing algorithms employ a specific theory for image transformation and fusion, which results in a large number of parameters during the fusion process. Therefore, these proposed algorithms have high complexity and large amounts of calculation. In addition, designing fusion rules is very complex and difficult. Different rules are needed in different transformation domains, decreasing the generality of the methods. With the rapid development and application of DL (deep-learning), Ian Goodfellow [24] proposed generative adversarial networks (GANs), which are widely used in the imaging field because of their excellent simulation capability for arbitrary data distributions. GANs can generate things that are similar to the real world, such as images, music, and videos. A GAN consists of a generator and a discriminator. The generator generates images and the discriminator judges the authenticity of the image. Both the generator and discriminator are able to improve their performance during the training process. Therefore, generated images can be more realistic, and even fool the discriminator. In addition, the discriminator has also improved the discrimination performance, which sets a high standard for authenticity, forcing the generator to improve the fusion quality. Recently, numerous improved GANs have been proposed. For example, WGAN [25], [26] utilized the Wasserstein distance to measure the error between real and generative distributions. Compared with traditional GANs, WGAN has better stability and higher image quality. In addition, Mao et al. proposed LSGAN [27] to generate images and characters. However, WGAN clips weights to improve the generated quality at the expense of diversity limitations. LSGAN takes the mean square error (MSE) loss as the loss function and achieves better results. Nevertheless, the performance of LSGAN is singular, and it has poor adaptability for other images or tasks. Moreover, LSGAN needs to adjust the discrimination parameters, thereby increasing the complexity and decreasing the generality of algorithm. Due to the loss of detailed information in the fusion process, fusion images have weak edge intensities and blurry details. Undoubtedly, blurry fusion images could bring serious interference, delay the best treatment time and even cause misdiagnoses. Hence, fusion images with clear edges and details have significant research value for computer-aided diagnosis, disease prediction and other medical technologies. To solve the shortcomings of existing image fusion algorithms, DSAGAN is proposed for anatomical and functional image fusion, which is based on the dual-stream architecture and attention mechanism. The structure diagram of DSAGAN is illustrated in Fig. 1. The contributions of our paper are summarized as follows. (1) The DSAGAN is proposed for anatomical and functional medical image fusion. The dual-stream architecture is utilized to extract multiscale features, and the attention mechanism is employed to enhance the extracted features to improve the fusion quality. (2) The architecture and principle of DSAGAN are introduced, and the convergence and stability are proven theoretically in our paper. In addition, we discuss the histogram differences of the fusion images among the proposed and reference algorithms. The rationality of pixel distributions is also discussed. (3) DSAGAN is an end-to-end model. Once trained, the fusion images can be output directly without other operations. Compared with the existing algorithms, DSAGAN consumes less fusion time. (4) The fusion images of DSAGAN have clearer edges and richer details with excellent performance on objective metrics than that of the compared algorithms. (5) The remainder of our article is arranged as follows. Section 2 introduces the related works. In this section, common fusion methods and basic knowledge of GANs are introduced. Section 3 presents the principle and structure of DSAGAN, including the generator, discriminator, and loss functions. The parameters of the generator and discriminator are also given. Section 4 presents the theoretical analysis, including the proof of the convergence and stability of DSAGAN. Section 5 gives the experiments and comparisons of fusion results and metrics. Section 6 gives a discussion on the rationality of the fusion results of the proposed algorithm and compared algorithms. Section 7 summarizes our paper and provides future work. Figures Structure diagram of DSAGAN. Schematic diagram of DSAGAN. Attention module 1. Attention module 2. Attention module 3. Concatenation process of features. Show all figures Section snippets Related works This chapter introduces the related works of this paper. Traditional fusion methods, deep learning-based fusion methods and GAN-based fusion methods are introduced in sequence. Then, the advantages and shortcomings of each kind of algorithm are analyzed in detail. Proposed algorithm Although many fusion algorithms have been proposed, most of them have their own shortcomings, such as algorithm complexity, low efficiency, and blurry edges. Aiming at the defects of existing algorithms, DSAGAN is proposed. In this section, the principle and structure of DSAGAN are described in detail. DSAGAN is divided into a generator and a discriminator. The generator is utilized to generate fusion images. The discriminator is employed to discriminate the authenticity of the two input images Theoretical analysis In this section, the optimization process of DSAGAN is introduced, and the convergence and stability of the training are theoretically proven. The aforementioned content is introduced as follows. Experiments Three modal medical images are prepared in our experiments, which include MRI, PET and SPECT images. The fusion experiments of MRI-PET and MRI-SPECT were performed separately. Our code is based on the PyTorch framework and available at https://github.com/jeffsonfu/DSAGAN.git . The experimental environment is a computer with the Windows 10 (64 bit) operating system, an Intel Core i5-8400 CPU with 6 cores, an NVIDIA GeForce GTX 1050 Ti GPU and 8 GB of RAM. Discussion To compare the rationality of the fusion results, we draw the histograms of the fusion images generated by different algorithms in Fig. 18, which intuitively shows the image pixel distributions. As illustrated in Fig. 18, the histograms of the original images are quite different. The pixel distribution of the MRI image is high in the range of 0–150, and low in the range of 200–255. However, the pixel distribution of the PET image is only concentrated in the range of 0–180. In our experiments, Conclusion In this paper, we propose a new medical image fusion algorithm called DSAGAN, which is based on the dual-stream structure and attention mechanism. DSAGAN is an end-to-end model. After training, DSAGAN can obtain fusion images directly. Extensive experimental results show that our fusion results preserve the original image information well with clear edges and details. Compared with the reference algorithms, DSAGAN has better Q <sub>AG</sub> , Q <sub>EN</sub> and Q <sub>NIQE</sub> metrics. In addition, our algorithm consumes the CRediT authorship contribution statement Jun Fu: Conceptualization, Data curation, Formal analysis, Investigation, Methodology, Software, Writing - original draft. Weisheng Li: Funding acquisition, Project administration. Jiao Du: Supervision, Validation. Liming Xu: Resources. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments The authors would like to thank the anonymous editors and reviewers for their advice that improved this paper. This work was supported by the National Key Research and Development Program of China [Nos. 2019YFE0110800 , 2016YFC1000307-3 ], National Natural Science Foundation of China [Nos. 62027827 , 61972060 , U1713213 , 61802148 ], the Doctoral Talent Training Project of Chongqing University of Posts and Telecommunications [No. BYJS202002 ], and Natural Science Foundation of Chongqing [ References (49) L. Liu et al. No-reference Image Quality Assessment Based on Spatial and Spectral Entropies Signal Process.-Image Commun. (2014) R. Singh et al. Fusion of multimodal medical images using Daubechies complex wavelet transform – A multiresolution approach Information Fusion (2014) J. Du et al. Fusion of anatomical and functional images using parallel saliency features Inf. Sci. (2018) Z. Zhang et al. Gradient preconditioned mini-batch SGD for ridge regression Neurocomputing (2020) J. Ma et al. FusionGAN: A generative adversarial network for infrared and visible image fusion Information Fusion (2019) Y. Liu et al. Multi-focus image fusion with a deep convolutional neural network Information Fusion (2017) Y. Liu et al. A general framework for image fusion based on multi-scale transform and sparse representation Information Fusion (2015) J. Li et al. Infrared and visible image fusion using dual discriminators generative adversarial networks with Wasserstein distance Inf. Sci. (2020) Z. Zhu et al. A novel multi-modality image fusion method based on image decomposition and sparse representation Inf. Sci. (2018) H. Li et al. Noise-robust image fusion with low-rank sparse decomposition guided by external patch prior Inf. Sci. (2020) T. Li et al. Biological image fusion using a NSCT based variable weight method Information Fusion (2011) S. Singh et al. Nonsubsampled shearlet based CT and MR medical image fusion using biologically inspired spiking neural network Biomed. Signal Process. Control (2015) J. Du et al. Union Laplacian pyramid with multiple features for medical image fusion Neurocomputing (2016) L. Wang et al. Multi-modal medical image fusion using the inter-scale and intra-scale dependencies between image shift-invariant shearlet coefficients Information Fusion (2014) L. Wang et al. EGGDD: an explicit dependency model for multimodal medical image fusion in shift-invariant shearlet transform domain Information Fusion (2014) S. Li et al. Multifocus image fusion by combining curvelet and wavelet transform Pattern Recogn. Lett. (2008) J. Chen et al. Infrared and visible image fusion based on target-enhanced multiscale transform decomposition Inf. Sci. (2020) N. Anantrasirichai et al. Image Fusion via Sparse Regularization with Non-Convex Penalties Pattern Recogn. Lett. (2020) J. Du et al. An overview of multi-modal medical image fusion Neurocomputing (2016) B. Meher et al. A survey on region based image fusion methods Information Fusion (2019) K. Ma et al. Deep Guided Learning for Fast Multi-Exposure Image Fusion IEEE Trans. Image Process. (2020) R. Hou et al. VIF-Net: An Unsupervised Framework for Infrared and Visible Image Fusion IEEE Trans. Comput. Imaging (2020) P. Hill et al. Perceptual image fusion using wavelets IEEE Trans. Image Process. (2017) J. Du et al. Anatomical-functional image fusion by information of interest in local laplacian filtering domain IEEE Trans. Image Process. (2017) View more references Cited by (0) Recommended articles (6) Research article Exploring the impact of node mobility on cascading failures in spatial networks Information Sciences, Volume 576, 2021, pp. 140-156 Show abstract Existing researches on cascading failures mainly focus on static spatial networks, but rarely consider network scenarios where mobile nodes and static nodes coexist. Therefore, in this work, we explore the impact of node mobility on cascading failures in spatial networks. We first develop a cascading model for static-mobile spatial network systems. In this model, we use the general betweenness to characterize the load of static nodes in the network, and adopt the Gauss–Markov mobility model to generate the movement trajectory of mobile nodes. On this basis, we develop three node interaction modes ( i.e., all-connection mode, high-load priority mode and low-load priority mode) to characterize the interaction between static nodes and mobile nodes. Experimental results have shown that 1) unlike the traditional cascading process that is a continuous process, the cascading process of static-mobile spatial networks consists of multiple cascading processes that occur at different times; 2) expanding the network size and reducing the number of mobile nodes can help the network resist cascading failures; 3) there is a tolerance space for network configuration parameters. When the configuration parameters fall into this space, the network can avoid cascading failures; 4) among the three interaction modes, the network robustness in all-connection mode is the worst, followed by low-load priority mode, and finally high-load priority mode. The obtained results can provide theoretical guidance for users to establish a more robust static-mobile spatial network. Research article Extracting modular-based backbones in weighted networks Information Sciences, Volume 576, 2021, pp. 454-474 Show abstract Networks are an adequate representation for modeling and analyzing a great variety of complex systems. However, understanding networks with millions of nodes and billions of connections can be pretty challenging due to memory and time constraints. Therefore, selecting the relevant nodes and edges of these large-scale networks while preserving their core information is a major issue. In most cases, the so-called backbone extraction methods are based either on coarse-graining or filtering approaches. Coarse-graining techniques reduce the network size by gathering similar nodes into super-nodes, while filter-based methods eliminate nodes or edges according to a statistical property.In this work, a filter-based method is proposed and investigated. It uses the overlapping community structure to build the backbone in weighted networks. While most filtering techniques rely on link features to extract the backbone, the proposed method exploits both nodes and links. It takes advantage of the network communities through their main features (overlapping nodes, hubs, and bridging connections) to select influential edges and nodes while preserving the ability of the information dissemination of the original network. The so-called “Modular filtering backbone” combines two components. The first one is the network connecting the overlapping nodes and the top connected nodes (also called the hubs).One discards the edges with the lowest weights as long as connected components are maintained. The second component uses the network of the inter-community links with the nodes at their extremities. The disparity filter algorithm allows preserving only its most crucial connections. An extensive investigation is performed on a set of real-world weighted networks of various sizes and a wide range of origin. Results show the advantage of the proposed method over alternative filtering-based methods used for comparative purposes. Furthermore, this sheds new light on the most relevant parts of empirical networks hidden by their complexity. Research article Distributed Cooperative Control for Multiple Heterogeneous Euler-Lagrangian Systems Under Global Equality and Inequality Constraints Information Sciences, 2021 Show abstract In this paper, we study the distributed optimization problem with globally equality and inequality constraints for a multi-agent system, where each agent is modeled by Euler-Lagrangian (EL) dynamics. The optimized function can be represented by the sum of all local cost functions corresponding to each individual agent. Two continuous-time algorithms are proposed to solve such the problem in a distributed manner. In virtue of geometric graph theory, convex analysis and Lyapunov stability theory, it is proved that all agents achieve consensus on the Lagrangian multipliers associated with constraints while the proposed algorithms converge exponentially to the optimal solution of the problem given in the case that the parameters of EL agents are known, and converge asymptotically to the optimal solution of the problem in the case that the parameters of EL agents are unknown, respectively. Finally, an example is provided to demonstrate the effectiveness of the theoretical results. Research article Integrating multiple genomic imaging data for the study of lung metastasis in sarcomas using multi-dimensional constrained joint non-negative matrix factorization Information Sciences, Volume 576, 2021, pp. 24-36 Show abstract Integrative analysis of histopathology images and genomic data enables the discovery of potential biomarkers and multimodal association patterns. However, few studies have established effective association models for complex diseases, such as sarcoma, by combining histopathological images with multiple genetic variation data. Here, we present an integrative multiple genomic imaging framework called multi-dimensional constrained joint non-negative matrix factorization (MDJNMF) to identify modules related to lung metastasis of sarcomas based on sample-matched whole-solid image, DNA methylation, and copy number variation features. Three types of feature matrices were projected onto a common feature space, in which heterogeneous variables with large coefficients in the same projected direction form a common module. The correlation between image features and genetic variation features is used as network-regularized constraints to improve the module accuracy. Sparsity and orthogonal constraints are utilized to achieve the modular sparse solution. Multi-level analysis indicates that our method effectively discovers biologically functional modules associated with sarcoma or lung metastasis. The representative module reveals a significant correlation between image features and genetic variation features and excavates potential diagnostic biomarkers. In summary, the proposed method provides new clues for identifying association patterns and biomarkers using multiple types of data sources for other diseases. Research article A three-way decision method based on fuzzy rough set models under incomplete environments Information Sciences, Volume 577, 2021, pp. 22-48 Show abstract The paper primarily explores the applicability of three-way decision (TWD) to multi-attribute decision-making (MADM), and establishes a new three-way multi-attribute decision-making (TW-MADM) method under an incomplete environment. For the sake of making rational decisions for MADM problems with fuzzy values, fuzzy rough set models are first utilized to investigate a new TWD model. By taking into account the hesitation degree of each evaluation value, a data-driven method to determine the relative loss functions is presented. Moreover, a new conditional probability calculation method is put forth via the information granularity of each object. In light of the above statement, a novel TWD model with three strategies is proposed. Afterwards, the arithmetic mean method is adopted for patching the lost data to effectively address incomplete MADM problems. Given the uncertainty of the patched data and real data, a new TW-MADM method as well as a corresponding MADM algorithm is designed. By several comparative analysis and experimental analysis, the feasibility, effectiveness, superiority and stability of the method are demonstrated. In addition, the results show that the presented method with optimistic strategies is more viable and stable than the method with compromise and pessimistic strategies. Research article A genetic timing scheduling model for urban traffic signal control Information Sciences, Volume 576, 2021, pp. 475-483 Show abstract As urban traffic condition is diverse and complicated, maximizing the use of urban traffic signal control system to solve traffic issues becomes one of the hot and promising topics. Especially, how to control traffic signals at multiple intersections is a key challenge. To speed up urban traffic flow, this research presents a genetic timing scheduling model (GTSM) for urban traffic signal control. GTSM constructs cellular automata to update the timing cycles of traffic signals at multiple intersections, where state update functions are formulated to coordinate traffic signals. In addition, a proposed genetic optimization algorithm (GOA) in GTSM optimizes the timing cycles of traffic signals at multiple intersections in the dynamic timing optimization. The experimental results on urban road networks show that the performance of our model is excellent in various scenarios to control traffic signals to speed up urban traffic flow. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.083", "PubYear": 2021, "Volume": "576", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Chongqing Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing 400065, China;School of Information Engineering, Zunyi Normal University, Zunyi 563006, China"}, {"AuthorId": 2, "Name": "Weisheng Li", "Affiliation": "Chongqing Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing 400065, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Educational Software, Guangzhou University, Guangzhou 510006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing 400065, China"}], "References": [{"Title": "Infrared and visible image fusion based on target-enhanced multiscale transform decomposition", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "64", "JournalTitle": "Information Sciences"}, {"Title": "Image fusion via sparse regularization with non-convex penalties", "Authors": "Nan<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "131", "Issue": "", "Page": "355", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Noise-robust image fusion with low-rank sparse decomposition guided by external patch prior", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "523", "Issue": "", "Page": "14", "JournalTitle": "Information Sciences"}, {"Title": "Infrared and visible image fusion using dual discriminators generative adversarial networks with <PERSON><PERSON><PERSON> distance", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "529", "Issue": "", "Page": "28", "JournalTitle": "Information Sciences"}, {"Title": "Gradient preconditioned mini-batch SGD for ridge regression", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "413", "Issue": "", "Page": "284", "JournalTitle": "Neurocomputing"}]}, {"ArticleId": 89248277, "Title": "Information-theoretic measures of uncertainty for interval-set decision tables", "Abstract": "Uncertainty measurement is considered as a vital quantitative way for analyzing and mining potential characteristic features in different types of decision tables. However, considering the equivalent relation is not suitable for evaluating the relationships of objects, few studies focused on the interval-set decision tables. In this paper, we address the uncertainty measurement problem in interval-set decision tables. Firstly, a similarity relation is induced by the similarity degree. Based on the similarity relation, a notion of granular structure is defined and the corresponding properties are investigated in interval-set decision tables. Secondly, we extend the accuracy and the roughness, called the interval approximation accuracy and the interval approximation roughness, to measure the uncertainty under the granular structures. By the analysis of the two extended measures, they can effectively evaluate the uncertainty caused by the approximations in the rough set model. Considering that the size of similarity classes can also affect the uncertainty, an alternative uncertainty measure based on the conditional information entropy, called the interval-decision entropy, is proposed. Moreover, a definition of reduct based on our proposed measure is provided and a heuristic attribute reduction algorithm is designed. Finally, numerical experiments demonstrate that the proposed uncertainty measures are effective and suitable for interval-set decision tables. Introduction Uncertainty measurement is not only a general hot topic in many scientific areas [15], [24], but also a significant issue in rough set theory. Since rough set theory was proposed by <PERSON><PERSON><PERSON> [23], uncertainty measurements for rough sets have been studied by many researchers and successfully applied to many fields, such as feature selection [6], [8], [13], [17], [26], [27], [45], data mining [4], [16], [34], knowledge discovery [3], [5], [25], [28], [46] and so on [29], [40]. In a decision table, every object has a brief and precise description based on its corresponding attribute values. In terms of the domains of each attribute, there are three categories for the decision tables which are summarized in Fig. 1. Currently, most works focused on the single-valued decision tables [1], [7], [12], [13], [18], [20], [22], [32], [34]. However, in some practical situations, due to the limitation of data acquisition, omission and other reasons, it is not easy to obtain the certain or accurate values for all attributes. Thus, to represent the uncertain or vague property of attributes, it is suitable to consider the set-valued attributes or interval-set-valued attributes in the decision tables. Considering that the set-valued attributes are the special cases of interval-set-valued attributes, we only investigate the interval-set-valued attributes in this paper. Referring to Fig. 1, there are two types of interval decision tables, namely, interval-valued decision tables [6], [30] and interval-set decision tables [16], [39]. At present, various research problems in interval-valued decision tables have been widely studied, such as uncertainty measurements [5], [28], attribute reduction [6], incremental updating approximations [47] and so on. However, few studies focused on interval-set decision tables. In this paper, we mainly investigate the uncertainty measurement for interval-set decision tables. Instead of the single-valued attribute, a pair of crisp sets named lower-bound and upper-bound sets are regard as the value of an attribute Yao [31] firstly proposed a notion of an interval set and named a pair of bound sets as an interval set. Besides, the corresponding notion of interval sets from that of interval numbers are involved in representing qualitative information. The interval set is used to define the range of the unknown set instead of defining a set precisely. Based on the notion of interval sets, Yao and Liu [39] firstly proposed the definition of the interval-set information table. There is a big difference between interval-set information tables and ordinary information tables. The former owns the domain of values of attribute which is an interval set. In the light of this, the classical set operations are no longer applicable to interval-set information tables. To solve this problem, Yao [32] extended intersection, union and difference operations on interval sets from that of ordinary set theory. It laid the foundation of operators in interval-set information tables. Considering the representation of inaccuracy and uncertainty contents instead of a single element, an interval set is consist of a subset of elements, bounded by a pair of lower set and upper set. Interval-set analysis is regarded as the representations of extensions and intentions of partially-known concepts in an incomplete context and has been successfully applied widely in three-way concept analysis [35], rules induction [16], three-way decision [43] and rough cluster [2]. Considering that the existing binary relations are not used in interval-set information tables, recently, several researchers studied in interval-set information tables and proposed some different types of similarity relations. For example, Lin et al. [21] introduced conjunctive forms and a dominance relation in interval-set information tables and provided a definition of attribute reduction based on dominance relation. Li et al. [16] proposed an interval set approach to induce classification rules from an incomplete information table through replacing missing values by interval sets. Zhang et al. [44] defined the concept of similarity degree between two interval sets and proposed a new method for seeking a better approximation set of interval set. Zhong and Huang [48] discussed the granular structures of interval sets from both the measurement-theoretic and the set-theoretic perspectives, respectively. Zhang et al. [43] systematically investigated a framework for comparing two interval sets by inclusion measures, which constructed different inclusion measures to present the quantitative ranking of interval sets. Wang and Yue [28] used a fuzzy preference relation for interval-set information tables and investigated entropy measures and granularity measures. Zhang et al. [46] proposed an interval δ -similarity relation and investigated the uncertainty measures for interval-set information tables. In existing studies, some researchers have investigated interval sets and paid more and more attentions to some concepts and properties of interval-set information tables in the recent five years. In real situations, most of applications are related to decision problems, such as three-way classification [12], [14], attribute reduction [11], [17], [22], [27], [41], [45], clustering [13] and so on [36], [43]. However, the decision attribute is not considered and few studies focused on interval-set decision tables. In fact, besides the conditional attributes describing an object, each object usually has a unique label or decision attribute, which can form a decision table. In this paper, we mainly investigate the uncertainty measurement for interval-set decision tables. As is known to all, uncertainty measurement is significant and critical issue for decision tables in rough set theory. Since the essence of rough set theory is that the uncertain concept is represented by a pair of lower approximate set and upper approximate set, the uncertainty of decision tables is inevitable in rough set theory. With the deep development of granular computing, the variation of granular structure also affects the uncertainty of decision tables. Pawlak [23] considered that the cause of uncertainty of decision tables was the boundary region whose objects could not be classified correctly. On basis of it, the approximation accuracy and the approximation roughness were proposed for measuring uncertainty of decision tables. Gediga and Duntsch [9] analyzed the definitions of approximate sets and proposed two more suitable accuracy and roughness measures. Wang et al. [26] also adopted the approximate sets for monotonically measuring uncertainty of probabilistic rough set. Dai and Xu [7] summarized the existing uncertainty measurements of rough set and considered two types of approaches for measuring uncertainty in rough set theory, namely, the pure rough set approach and the information theory approach, respectively. At present, the information theory approach becomes an alternative for evaluating uncertainty measurements instead of the pure rough set approach. At earliest, Beaubouef et al. [1] combined the approximate sets with information entropy and constructed a new definition of rough entropy to measure uncertainty. Liang et al. [18], [19], [20] proposed four kinds of information entropies to measure the uncertainty of the complete information tables and incomplete information tables, respectively. From then on, the information theory approach had attracted much attention of scholars for evaluating uncertainty measures of decision tables, including ordinary decision tables [19], incomplete decision tables [7], [20], set-valued decision tables [3], [10] and interval-valued decision tables [5], [6]. Besides, from the perspective of granular computing [36], the uncertainty measurements of decision tables are related to granules and granular structures. A hierarchical granular structure is convenient for measuring uncertainty through constructing the process of refining and coarsening granules. As mentioned above, uncertainty measurement is an effective quantitative criterion and has been used widely for decision tables. However, there are few studies about uncertainty measurement for interval-set decision tables. In light of this, we will discuss the uncertainty measurement for interval-set decision tables based on conditional information entropy in this paper. Firstly, we give the definition of the interval-set decision table and introduce a new similarity relation that considers the interaction between the interval sets of any two objects. Secondly, based on the similarity relation, we extend the interval approximation accuracy and the interval approximation roughness and they can effectively evaluate the uncertainty caused by the approximations in the rough set model. In order to measure the uncertainty caused by the change of granular structure, an alternative uncertainty measure based on conditional information entropy is proposed. The corresponding theoretical studies and analysis are also provided and proved. Finally, an application for attribute reduction based on the proposed uncertainty measure is conducted on UCI datasets. The experimental results show that our proposed uncertainty measurements are effective and suitable for interval-set decision tables. The remainder of the paper is organized as follows. In Section 2, some basic notions of interval sets and information entropy theory are reviewed. In Section 3, an interval-set decision table is defined and the similarity relation is constructed between two interval sets. In Section 4, the interval approximation accuracy and interval approximation roughness, extended measures of accuracy and roughness, are investigated for interval-set decision tables. Furthermore, an alternative uncertainty measure based on conditional information entropy, called the interval-decision entropy, is proposed and the corresponding properties are investigated. In Section 5, in order to verify the effectiveness of our proposed uncertainty measurement, an application in attribute reduction for interval-set decision tables is provided. Besides, a heuristic attribute reduction algorithm based on the deletion strategy is proposed. In Section 6, several numerical experiments are conducted to investigate that our uncertainty measure is effective and valid for interval-set decision tables. In Section 7, we conclude the paper. Figures The existing several forms of decision tables. The interval lower and upper approximations formed by two different granular structures. The monotonicity of uncertainty measures on dataset Credit with respect to the conditional attribute set and the similarity rate. The monotonicity of uncertainty measures on dataset Breast-cancer with respect to the conditional attribute set and the similarity rate. The monotonicity of uncertainty measures on dataset Lymphography with respect to the conditional attribute set and the similarity rate. The monotonicity of uncertainty measures on dataset Unbalance with respect to the conditional attribute set and the similarity rate. Show all figures Section snippets Preliminaries In this section, the preliminaries about basic notions of interval-set information tables and information entropy theory are mainly introduced. More details can be referred to [31], [33], [37], [38]. Interval-set decision tables In this section, the decision attribute is taken into account to form an interval-set decision table. A new binary similarity relation is proposed instead of the equivalent relation and some basic properties of the interval-set decision tables are investigated. Uncertainty measurement for interval-set decision tables In Pawlak rough set model [23], the approximation accuracy and roughness were used to evaluate the uncertainty of decision tables. However, the measures are not sensitive to the change of the granular structures. To achieve this problem, the conditional information entropy is introduced to apply for uncertainty in decision tables. In this section, two extended measures are proposed for interval-set decision tables, which are called the interval approximation accuracy and the interval Monotonic uncertainty measures based attribute reduction in interval-set decision tables Attribute reduction [6], [13], [17], [41] or feature selection, is a significant problem in rough set theory. A reduct is a small set of attributes that can preserve or even improve some abilities based on the original attributes. In this section, a new reduct based on the interval-decision entropy is defined in the interval-set decision table. Besides, the significance of the attribute is defined as well. Finally, a heuristic algorithm based on the deletion strategy is investigated. Experiments In this section, several numerical experiments to verify the effectiveness of our proposed uncertainty measurements for interval-set decision tables are shown. All twelve practical classification datasets, which are all from UCI Machine Learning Repository, are used in our simulation experiments. The detailed characteristics of the datasets are displayed in Table 4, where   U   ,   C   , and   V d   represent the numbers of objects, condition attributes, and decision classes, respectively. Since the Conclusions Uncertainty measurement has been researched for many types of decision tables. However, few studies focuses on the uncertainty of interval-set decision tables. In this paper, we firstly provide the definition of an interval-set decision table. Considering the values of attributes which are interval sets, a similarity relation is defined to handle the relation of objects. Based on the similarity relation, a granular structure is proposed and the associated properties are analysed in interval-set CRediT authorship contribution statement Yimeng Zhang: Conceptualization, Methodology, Writing - original draft. Xiuyi Jia: Writing - review & editing, Investigation. Zhenmin Tang: Supervision, Project administration. Declaration of competing interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work was partially supported by the National Natural Science Foundation of China ( 61773208 ), the Natural Science Foundation of Jiangsu Province ( ********** ), and the Fundamental Research Funds for the Central Universities ( 30920021131 ). References (48) Y. Zhang et al. Incremental updating of rough approximations in interval-valued information systems under attribute generalization Inf. Sci. (2016) Y. Zhang et al. Uncertainty measures for interval set information tables based on interval δ )similarity relation Inf. Sci. (2019) H. Zhang et al. Ranking interval sets based on inclusion measures and applications to three-way decisions Knowl.-Based Syst. (2016) Y. Yao et al. Attribute reduction in decision-theoretic rough set models Inf. Sci. (2008) Y. Yao et al. A measurement theory view on the granularity of partitions Inf. Sci. (2012) Y. Yao Three-way decision and granular computing Int. J. Approximate Reasoning (2018) X. Yang et al. Dominance-based rough set approach to incomplete interval-valued information system Data Knowledge Eng. (2009) R.R. Yager Bi-directional dominance for measure modeled uncertainty Inf. Sci. (2018) G. Wang et al. Monotonic uncertainty measures for attribute reduction in probabilistic rough set model Int. J. Approximate Reasoning (2015) C. Wang et al. Uncertainty measures for general fuzzy relations Fuzzy Sets Syst. (2019) Z. Pawlak et al. Rough sets: Probabilistic versus deterministic approach J. Man-Mach. Stud. (1988) X.A. Ma et al. Cost-sensitive three-way class-specific attribute reduction Int. J. Approximate Reasoning (2019) W. Li et al. Multi-objective attribute reduction in three-way decision-theoretic rough set model Int. J. Approximate Reasoning (2019) H. Li et al. An interval set model for learning rules from incomplete information table Int. J. Approximate Reasoning (2012) H. Ju et al. Sequential three-way classifier with justifiable granularity Knowl.-Based Syst. (2019) X. Jia et al. A multiphase cost-sensitive learning method based on the multiclass three-way decision-theoretic rough set model Inf. Sci. (2019) X. Jia et al. Three-way decisions based feature fusion for chinese irony detection Int. J. Approximate Reasoning (2019) Y. Guan et al. Set-valued information systems Inf. Sci. (2006) G. Gediga et al. Rough approximation quality revisited Artif. Intell. (2001) C. Gao et al. Granular maximum decision entropy-based monotonic uncertainty measure for attribute reduction Int. J. Approximate Reasoning (2019) J. Dai et al. Approximations and uncertainty measures in incomplete information systems Inf. Sci. (2012) J. Dai et al. Uncertainty measurement for interval-valued decision systems based on extended conditional entropy Knowl.-Based Syst. (2012) J. Dai et al. Uncertainty measurement for interval-valued information systems Inf. Sci. (2013) J. Dai et al. Decision rule mining using classification consistency rate Knowl.-Based Syst. (2013) View more references Cited by (0) Recommended articles (6) Research article Multiattribute decision making based on interval-valued intuitionistic fuzzy values, score function of connection numbers, and the set pair analysis theory Information Sciences, Volume 551, 2021, pp. 100-112 Show abstract This paper proposes a new multiattribute decision making (MADM) method based on the proposed score function of connection numbers (CNs) and the set pair analysis (SPA) theory in the interval-valued intuitionist fuzzy (IVIF) context. Firstly, we develop a score function for ranking CNs. The various notable characteristics of the proposed score function of CNs are also presented. Then, we propose a new MADM method based on interval-valued intuitionist fuzzy values (IVIFVs), the proposed score function of CNs and the SPA theory, where we convert IVIFVs into CNs and the optimal weights of attributes are calculated from the IVIF weights of attributes. Finally, the proposed MADM method is applied for MADM in the IVIF context, where the preference orders (POs) of the alternatives obtained by the proposed MADM method are compared with the ones obtained by the existing MADM methods. The proposed MADM method can overcome the drawbacks of the existing MADM methods. Research article Decision-theoretic five-way approximation of fuzzy sets Information Sciences, Volume 572, 2021, pp. 200-222 Show abstract In this paper, decision-theoretic five-way approximation of fuzzy sets is introduced by extending existing three-way decision-theoretic models. The proposed model exhibits a number of useful features which allow a decision maker to consider weak acceptance or weak rejection options, thereby minimizing the overall approximation error and cost. Two special models: cost-sensitive and minimum distance, in decision-theoretic five-way approximation of fuzzy sets are investigated. Experimental studies and comparisons with existing decision-theoretic three-way approximation models show the advantage and promising performance of the proposed model. Research article A FMEA based novel intuitionistic fuzzy approach proposal: Intuitionistic fuzzy advance MCDM and mathematical modeling integration Expert Systems with Applications, Volume 183, 2021, Article 115413 Show abstract This study proposes a novel three-stage intuitionistic fuzzy risk assessment (RA) approach based on Failure Modes and Effects Analysis (FMEA). In this study, it was paid attention for considering real constraints of firms such as capital, time etc. to prevent nan-fatal failure modes (FMs), interactions between FMs and risk level similarities created by risk factors (RFs). At the first stage of the proposed approach, RFs’ weights are computed by a new intuitionistic fuzzy weighting method considering similarities between RFs for risk levels that they can create. At the second stage, Modified Intuitionistic Fuzzy Multi Attribute Border Approximation Area (MIF-MABAC) including interactions between FMs is used to determine the rankings of FMs by using Extended Haussdorff distance function. At the third stage, two intuitionistic fuzzy mathematical models are established to show the effect of the real constraints of the firm to identify the risk types (RTs) that must be avoided primarily. It was seen that the first model gives the same ranking results with the MIF-MABAC. Additionally, when including the real constraints, the first model can give the more suitable results than the second model. The results obtained from the first model show that experts’ assessments and mathematical modeling identify the same FMs for preventing primarily. This study is the first one to suggest a new RA approach that reflects the real constraints of the firms to RA. Additionally, this is the first study that models’ interactions between FMs and risk level similarities created by RFs. Research article Enhanced linguistic computational models and their similarity with Yager’s computing with words Information Sciences, Volume 574, 2021, pp. 259-278 Show abstract A generalized computational framework for Computing with Words (CWW) using linguistic information (LI) was proposed by Prof. Yager. This framework is based on three steps: translation, manipulation and retranslation. Other works have independently proposed the Linguistic Computational Models (LCMs) to express the semantics of LI using Type-1 Fuzzy Sets and Ordinal term sets. The former is called the extension principle, and the latter, the symbolic method. We found that a high degree of similarity can be drawn between these methodologies and Yager’s CWW framework, but no discussion exists in the literature of the similarity drawn between them. Further, the extension principle has a drawback: it considers LI to be equally weighted in the aggregation phase. Also, Intuitionistic fuzzy sets (IFSs) and rough sets have gained popularity to model semantics of LI, but no CWW methodologies have been proposed using them. Thus, the novel contributions of this work are twofold. Firstly, showing the similarity of the linguistic computational models based on extension principle and symbolic method, to the Yager’s generalized CWW framework. Secondly, proposing a new augmented flexible weighting for LCM based on the extension principle and two novel CWW methodologies based on IFS and rough sets. Research article Non-fragile H ∞ memory sampled-data state-feedback control for continuous-time nonlinear Markovian jump fuzzy systems with time-varying delay Information Sciences, Volume 577, 2021, pp. 214-233 Show abstract In this paper, non-fragile control is studied for time-varying delay Markovian jump systems described by T-S model on the basis of aperiodic memory sampled-data control. The consideration of both non-fragility and signal input delay in a sampled-data control for fuzzy Markovian jump system (FMJS) has not been well documented. An improved time-delay-dependent Lyapunov–Krasovskii functional (LKF) is proposed, which covers as much the sampling interval and the time-delay information in the system and controller as possible. On this basis, we use the advanced technique of treating integral inequality to estimate the derivative term of Lyapunov–Krasovskii functional. The weighted matrix is introduced in the integral inequality makes our results more flexible, which will be illustrated in the practical examples in the last section. Using the linear matrix inequalities (LMIs) method, a set of sufficient conditions is established to guarantee the system to be stochastically stable and satisfy the performance index. Finally, two examples are given to illustrate the effectiveness and superiority of the results. Research article Fixed-time synchronization of fuzzy neutral-type BAM memristive inertial neural networks with proportional delays Information Sciences, Volume 576, 2021, pp. 522-541 Show abstract In this paper, fixed-time synchronization (Fix-TS) of fuzzy neutral-type bidirectional associative memory memristive-inertial neural networks (FNT-BAM-MINNs) with proportional delays is investigated by non-reduced order method. First, a new lemma is proposed to solve the parameter mismatch of FNT-BAM-MINNs on the basis of differential inclusion theories and analysis methods. Second, the delay-dependent switched controller and the delay-dependent fuzzy switched controller are designed to reach the Fix-TS of the drive-response FNT-BAM-MINNs, respectively. Based on fixed-time stability theory, inequality techniques and the new lemma, some delay-independent sufficient conditions are given to guarantee the Fix-TS of FNT-BAM-MINNs, which are easy to implement in practice. Furthermore, the settling time independent of the initial value is estimated. Finally, numerical examples indicate that our results are effective. View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.092", "PubYear": 2021, "Volume": "577", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Institute of Remote Sensing Equipment, Beijing 100854, China;School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Nanjing University of Science and Technology, Nanjing 210094, China"}], "References": []}, {"ArticleId": 89248344, "Title": "A synergistic promotion strategy for selective trapping and sensing of lead(II) by oxygen-vacancy and surface modulation of MnO2 nanoflowers", "Abstract": "Designed and fabricated electrochemical sensing materials with high electrical conductivity, sufficient number of active sites, and good selectivity to target analytes are highly desirable for sensitive determination of heavy metal ions in water. In this work, MnO<sub>2</sub> nanoflowers with enriched oxygen vacancies and surface phosphate ions (P-MnO<sub>2-x</sub>) were prepared by a simple phosphorization process. For this novel electrode modifier, oxygen vacancies increased the electrical conductivity, thereby improving the signal-to-noise ratio of sensors. Phosphate ions acted as ligand molecules for trapping Pb(II) and thus enhanced the selectivity toward Pb(II), and through (PO<sub>4</sub>)<sup>3−</sup> bridge, a fast charge-transfer channel was formed and promoting the redox cycles between Mn(III)/Mn(IV) and Pb(II)/Pb(0). Thus, these multiple synergistic effects endowed P-MnO<sub>2-x</sub> with a high detection sensitivity of 50.11 μA μM<sup>−1</sup> and a low limit of detection of 0.0012 μM. More impressively, after phosphorization treatment, P-MnO<sub>2-x</sub> showed ultra-high selectivity toward Pb(II) while did not change the stripping signals obviously toward other common metal ions. This synergistic oxygen-vacancy and surface modulation strategy demonstrates a new way to construct high-performance electrode materials for electroanalysis.", "Keywords": "MnO<sub>2</sub> ; Phosphorization ; Oxygen vacancy ; Mn(III)/Mn(IV) cycle ; Pb(II) sensing", "DOI": "10.1016/j.snb.2021.130384", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Agro-Forestry Environmental Processes and Ecological Regulation of Hainan Province, School of Ecological and Environmental Sciences, Hainan University, Haikou, 570228, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Materials Science and Engineering, Hainan University, Haikou, 570228, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Hainan University, Haikou, 570228, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Hainan University, Haikou, 570228, China;State Key Laboratory of Marine Resource Utilization in South China Sea, Hainan University, Haikou, 570228, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Hainan University, Haikou, 570228, China;State Key Laboratory of Marine Resource Utilization in South China Sea, Hainan University, Haikou, 570228, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON> Ge", "Affiliation": "Key Laboratory of Agro-Forestry Environmental Processes and Ecological Regulation of Hainan Province, School of Ecological and Environmental Sciences, Hainan University, Haikou, 570228, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "School of Materials Science and Engineering, Hainan University, Haikou, 570228, China;State Key Laboratory of Marine Resource Utilization in South China Sea, Hainan University, Haikou, 570228, China;Corresponding author at: School of Materials Science and Engineering, Hainan University, Haikou, 570228, China"}], "References": [{"Title": "Sensitive and selective detection of Pb (II) and Cu (II) using a metal-organic framework/polypyrrole nanocomposite functionalized electrode", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "304", "Issue": "", "Page": "127286", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "A ratiometric electrochemical sensor for simultaneous detection of multiple heavy metal ions based on ferrocene-functionalized metal-organic framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "310", "Issue": "", "Page": "127756", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 89248430, "Title": "DCNet: diffusion convolutional networks for semantic image segmentation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJES.2021.10039167", "PubYear": 2021, "Volume": "14", "Issue": "3", "JournalId": 31256, "JournalTitle": "International Journal of Embedded Systems", "ISSN": "1741-1068", "EISSN": "1741-1076", "Authors": [{"AuthorId": 1, "Name": "Hong<PERSON> Zhou", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON> Jiang", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89248438, "Title": "A movie recommendation model combining time information and probability matrix factorisation", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJES.2021.10039153", "PubYear": 2021, "Volume": "14", "Issue": "3", "JournalId": 31256, "JournalTitle": "International Journal of Embedded Systems", "ISSN": "1741-1068", "EISSN": "1741-1076", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON><PERSON> Wang", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89248442, "Title": "An image measurement technique for trajectory and attitude of cargo airdrop", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJES.2021.10039160", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 31256, "JournalTitle": "International Journal of Embedded Systems", "ISSN": "1741-1068", "EISSN": "1741-1076", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Xiao<PERSON> Ma", "Affiliation": ""}, {"AuthorId": 3, "Name": "Binghua Hu", "Affiliation": ""}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89248490, "Title": "Exact outage performance of two NOMA users in small-cell network over Nakagami-m fading under imperfect CSI", "Abstract": "", "Keywords": "", "DOI": "10.1504/IJICT.2021.10039161", "PubYear": 2021, "Volume": "1", "Issue": "1", "JournalId": 10769, "JournalTitle": "International Journal of Information and Communication Technology", "ISSN": "1466-6642", "EISSN": "1741-8070", "Authors": [{"AuthorId": 1, "Name": "Chi Bao Le", "Affiliation": ""}, {"AuthorId": 2, "Name": "Dinh Thuan Do", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 89248546, "Title": "Intelligent fault recognition framework by using deep reinforcement learning with one dimension convolution and improved actor-critic algorithm", "Abstract": "The quality of fault recognition part is one of the key factors affecting the efficiency of intelligent manufacturing. Many excellent achievements in deep learning (DL) have been realized recently as methods of fault recognition. However, DL models have inherent shortcomings. In particular, the phenomenon of over-fitting or degradation suggests that such an intelligent algorithm cannot fully use its feature perception ability. Researchers have mainly adapted the network architecture for fault diagnosis, but the above limitations are not taken into account. In this study, we propose a novel deep reinforcement learning method that combines the perception of DL with the decision-making ability of reinforcement learning. This method enhances the classification accuracy of the DL module to autonomously learn much more knowledge hidden in raw data. The proposed method based on the convolutional neural network (CNN) also adopts an improved actor-critic algorithm for fault recognition. The important parts in standard actor-critic algorithm, such as environment, neural network, reward, and loss functions, have been fully considered in improved actor-critic algorithm. Additionally, to fully distinguish compound faults under heavy background noise, multi-channel signals are first stacked synchronously and then input into the model in the end-to-end training mode. The diagnostic results on the compound fault of the bearing and tool in the machine tool experimental system show that compared with other methods, the proposed network structure has more accurate results. These findings demonstrate that under the guidance of the improved actor-critic algorithm and processing method for multi-channel data, the proposed method thus has stronger exploration performance.", "Keywords": "Fault recognition ; Deep reinforcement learning ; Actor-critic algorithm ; 1D convolution", "DOI": "10.1016/j.aei.2021.101315", "PubYear": 2021, "Volume": "49", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Science and Engineering, Huazhong University of Science and Technology, Wuhan 430074, China;Corresponding author"}], "References": [{"Title": "Learning on the Edge: Investigating Boundary Filters in CNNs", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "773", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "An intelligent fault diagnosis method for rotor-bearing system using small labeled infrared thermal images and enhanced CNN transferred from CAE", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101150", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 89248837, "Title": "Predicting Students’ Difficulties From a Piece of Code", "Abstract": "Based on hundreds of thousands of hours of data about how students learn in massive open online courses, educational machine learning promises to help students who are learning to code. However, in most classrooms, students and assignments do not have enough historical data for feeding these data hungry algorithms. Previous work on predicting dropout is data hungry and, moreover, requires the code to be syntactically correct. As we deal with beginners’ code in a text-based language our models are trained on noisy student text; almost 40% of the code in our datasets contains parsing errors. In this article, we compare two machine learning models that predict whether students need help regardless of whether their code compiles or not. That is, we compare two methods for automatically predicting whether students will be able to solve a programming exercise on their own. The first model is a heavily feature-engineered approach that implements pedagogical theories of the relation between student interaction patterns and the probability of dropout; it requires a rich history of student interaction. The second method is based on a short program (that may contain errors) written by a student, together with a few hundred attempts by their classmates on the same exercise. This second method uses natural language processing techniques; it is based on the intuition that beginners’ code may be closer to a natural language than to a formal one. It is inspired by previous work on predicting people's fluency when learning a second natural language.", "Keywords": "Computer science education;interactive environments;machine learning;modeling, and prediction.", "DOI": "10.1109/TLT.2021.3092998", "PubYear": 2021, "Volume": "14", "Issue": "3", "JournalId": 11620, "JournalTitle": "IEEE Transactions on Learning Technologies", "ISSN": "1939-1382", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Computer Science Department, National University of Córdoba, Córdoba, Argentina"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Computer Science Department, National University of C&#x00F3;rdoba, C&#x00F3;rdoba, Argentina"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer Science Department, National University of C&#x00F3;rdoba, C&#x00F3;rdoba, Argentina"}], "References": []}, {"ArticleId": 89248959, "Title": "Empirical Comparative Study of Wearable Service Trust Based on User Clustering", "Abstract": "<p>Users of wearable services are different in age, occupation, income, education, personality, values and lifestyle, which also determine their different consumption patterns. Therefore, for the trust of wearable services, the influencing factors or strength may not be the same for different users. This article starts with the resource and motivation dimensions of VALSTM model, and the clustering model and questionnaire scale for consumers of wearable services were constructed. And then the users and potential users of wearable service are clustered by an improved clustering algorithm based on adaptive chaotic particle swarm optimization. Through clustering analysis of 535 valid questionnaires, users are grouped into three types of consumers with different lifestyles, respectively named: trend-following users, fashion-leading users and economic-rational users. Finally, this paper analyzes and compares the trust subgroup models of three clusters, and draws some conclusions.</p>", "Keywords": "", "DOI": "10.4018/JOEUC.20211101.oa18", "PubYear": 2021, "Volume": "33", "Issue": "6", "JournalId": 11438, "JournalTitle": "Journal of Organizational and End User Computing", "ISSN": "1546-2234", "EISSN": "1546-5012", "Authors": [{"AuthorId": 1, "Name": "<PERSON>hongwei Gu", "Affiliation": "Shanghai Dianji University, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Dianji University, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Shanghai University of Electric Power, China"}], "References": []}, {"ArticleId": 89248976, "Title": "EMG BASED ESSENTIAL TREMOR DETECTION USING PSD FEATURES WITH RECURRENT FEEDFORWARD BACKPROPOGATION NEURAL NETWORK", "Abstract": "<p>Essential tremors (ET) are slow progressive neurological disorder that reduces muscular movements and involuntary muscular contractions. The further complications of ET may lead to <PERSON>’s disease and therefore it is very crucial to identify at the early onset. This research study deals with the identification of the presence of ET from the EMG of the patient by using power spectral density (PSD) features. Several PSD estimation methods such as <PERSON>, <PERSON><PERSON>, covariance, modified covariance, Eigen Vector based on Eigen value and MUSIC, and Thompson Multitaper are employed and are then classified using a recurrent feedback Elman neural network (RFBEN). It is observed from the experimental results that the MUSIC method of estimating the PSD of the EMG along with RFBEN classifier yields a classification accuracy of 99.81%. It can be concluded that the proposed approach demonstrates the possibility of developing automated computer aided diagnostic tool for early detection of Essential tremors.</p>", "Keywords": "", "DOI": "10.4018/IJEHMC.20211101oa11", "PubYear": 2021, "Volume": "12", "Issue": "6", "JournalId": 22950, "JournalTitle": "International Journal of E-Health and Medical Communications", "ISSN": "1947-315X", "EISSN": "1947-3168", "Authors": [], "References": []}, {"ArticleId": 89249037, "Title": "Idempotent graphs, weak perfectness, and zero-divisor graphs", "Abstract": "<p>The idempotent graph I ( R ) of a ring R is a graph with nontrivial idempotents of R as vertices, and two vertices are adjacent in I ( R ) if and only if their product is zero. In the present paper, we prove that idempotent graphs are weakly perfect. We characterize the rings whose idempotent graphs have connected complements. As an application, the idempotent graph of an abelian <PERSON> ring R is used to obtain the zero-divisor graph \\(\\Gamma (R)\\) of R .</p>", "Keywords": "Idempotent graph; Weak perfect graph; Zero-divisor graph", "DOI": "10.1007/s00500-021-05982-0", "PubYear": 2021, "Volume": "25", "Issue": "15", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics, JET’s Z. B. Patil College, Dhule, India"}, {"AuthorId": 2, "Name": "P. S. Momale", "Affiliation": "Department of Mathematics, P. V. P. College, Pravaranagar, India"}], "References": []}, {"ArticleId": 89249168, "Title": "An Intelligent Dynamic Bandwidth Allocation Method to Support Quality of Service in Internet of Things", "Abstract": "<p>Worldwide, Internet of Things (IoT) devices will surpass a range of five billion by 2025 and developed countries will extend to advance by supplying almost two-thirds of such connections. With existing infrastructure, allocating bandwidth to billions of IoT devices is going to be cumbersome. This paper addresses the problem of Dynamic bandwidth allocation in IoT devices. We enhanced the dynamic bandwidth allocation algorithms to support QoS in different bandwidth ranges. Our Proposed innovative Machine learning-based Intelligent Dynamic Bandwidth Allocation (IDBA) algorithm allocates the bandwidth effectively between IoT devices based on utilization patterns observed through machine learning methods. Moreover, we showed that an IDBA algorithm results in supporting quality of service in terms of ensuring uninterrupted bandwidth to critical IoT application where bandwidth tolerance is zero percent, along with that IDBA increasing the network throughput correlated to other dynamic bandwidth allocation algorithms. We demonstrate simulations in different applications. The results show that IDBA achieves better throughput even in low bandwidth range.</p>", "Keywords": "Dynamic bandwidth allocation;Reinforcement Learning;Machine learning models;Quality of Service", "DOI": "10.47839/ijc.20.2.2173", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, Hindustan Institute of Technology & Science, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Department of CSE, Hindustan Institute of Technology & Science, Chennai, India"}], "References": []}, {"ArticleId": 89249169, "Title": "New Hypertensive Retinopathy Grading Based on the Ratio of Artery Venous Diameter from Retinal Image", "Abstract": "<p>Medical research indicated that narrowing of the retinal blood vessels might be an early indicator of cardiovascular diseases; one of them is hypertensive retinopathy. This paper proposed the new staging method of hypertensive retinopathy by measure the ratio of diameter artery and vein (AVR). The dataset used in this research is the public Messidor color fundus image dataset. The proposed method consists of image resizing using bicubic interpolation, optic disk detection, a region of interest computation, vessel diameter measuring, AVR calculation, and grading the new categories of Hypertensive Retinopathy based on Keith-W<PERSON>ner-Barker categories. The experiments show that the proposed method can determine the stage of hypertensive retinopathy into new categories.</p>", "Keywords": "hypertensive retinopathy;grading;artery-vein diameter ratio", "DOI": "10.47839/ijc.20.2.2169", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "Bambang Krismono <PERSON>", "Affiliation": "Binus Graduate Program Bina Nusantara University, Jl. Kebon Jeruk No.27, Jakarta, Indonesia; Department of Computer Science, Faculty of Engineering and Health, Bumigora University, J<PERSON><PERSON>,West Nusa Tenggara, Mataram, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON> <PERSON>", "Affiliation": "Department of Community Medicine, Faculty of Medicine, University of Indonesia, Jl. Pegangsaan Timur, Central Jakarta City, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Binus Graduate Program Bina Nusantara University, Jl. Kebon Jeruk No.27, Jakarta, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Binus Graduate Program Bina Nusantara University, Jl. Kebon Jeruk No.27, Jakarta, Indonesia"}], "References": []}, {"ArticleId": 89249170, "Title": "Modeling of Psychomotor Reactions of a Person Based on Modification of the Tapping Test", "Abstract": "<p>The paper considers the method for analysis of a psychophysical state of a person on psychomotor indicators – finger tapping test. The app for mobile phone that generalizes the classic tapping test is developed for experiments. Developed tool allows collecting samples and analyzing them like individual experiments and like dataset as a whole. The data based on statistical methods and optimization of hyperparameters is investigated for anomalies, and an algorithm for reducing their number is developed. The machine learning model is used to predict different features of the dataset. These experiments demonstrate the data structure obtained using finger tapping test. As a result, we gained knowledge of how to conduct experiments for better generalization of the model in future. A method for removing anomalies is developed and it can be used in further research to increase an accuracy of the model. Developed model is a multilayer recurrent neural network that works well with the classification of time series. Error of model learning on a synthetic dataset is 1.5% and on a real data from similar distribution is 5%.</p>", "Keywords": "tapping test;mathematical modeling;psychomotor reactions;time series;recurrent neural network", "DOI": "10.47839/ijc.20.2.2166", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Lviv Polytechnic National University, Lviv, 79013, Ukraine"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Lviv Polytechnic National University, Lviv, 79013, Ukraine"}], "References": []}, {"ArticleId": 89249210, "Title": "A Novel Approach to Spoken Arabic Number Recognition Based on Developed Ant Lion Algorithm", "Abstract": "<p>Intelligent spoken system is constructed to recognize numbers spoken in Arabic language by different people. Series of operations are performed on audio sound file as pre-processing stages. A novel approach is applied to extract features of audio files called Max Mean Log to reduce audio file dimensions in an efficient manner. Several stages of initial processing are used to prepare the file for the next step of the recognition process. The recognition process begins with the use of <PERSON><PERSON><PERSON>’s advanced intelligence algorithm to determine the type of the spoken number in Arabic and later convert it to a visual text that represents the value of the spoken number. The current proposal method is relatively fast and very effective. The percentage of recognizing numbers spoken by the proposed algorithm is 99%. For 1,800 different audio files, the error rate was 1%. Additional 40 audio files were used that are different from people’s original dataset. Due to an additional examination of the system and its ability to recognize the audio file, the rate of discrimination for such files was 72.5%. </p>", "Keywords": "Pre-processing;Feature Extraction;Recognition System;Ant-lion algorithm", "DOI": "10.47839/ijc.20.2.2175", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, College of Computer Science and Mathematics, University of Mosul, Mosul, Iraq"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Department, College of Computer Science and Mathematics, University of Mosul, Mosul, Iraq"}], "References": []}, {"ArticleId": 89249289, "Title": "Sports Recognition using Convolutional Neural Network with Optimization Techniques from Images and Live Streams", "Abstract": "<p>This paper deals with the issue of automated image and video recognition of sports. It is a category of appreciation of human behavior, which is a very difficult task in the present day to classify images and video clips into a categorized gallery. This research paper proposes a sports detection system using a deeper CNN model that combines a fully connected layer with fine-tuning. It is applied to classify five individual sports groups through images and videos. In this work, we use a video classification method based on the image. Extended Resnet50 and VGG16 two pre-trained Deep CNNs are applied to build this sports detection system. RMSProp, ADAM & SGD optimizers are used to train the extended CNN models for five Epochs on the proposed 5sports dataset by handpicking thousands of sports images from the internet to very smoothly classify the five different types of sports. Training accuracy of approximately 83% is observed for ResNet50 with an SGD optimizer for 5 sports classes and 95% is observed for 3 sports classes.</p>", "Keywords": "Sport classification;Multimedia content analysis;Deep learning;Pre-trained models;Convolutional Neural Networks;VGG16;Resnet50;Model-Optimizer", "DOI": "10.47839/ijc.20.2.2176", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Science & Engineering, International Islamic University Chittagong (IIUC), Kumira, Chittagong, 4318, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Science & Engineering, International Islamic University Chittagong (IIUC), Kumira, Chittagong, 4318, Bangladesh"}], "References": []}, {"ArticleId": 89249290, "Title": "The Efficient Distance Weighted Case Base Rule (DW-CBR) for Early Childhood Diseases Diagnosis", "Abstract": "<p>Children from newborns to six years old are more susceptible to diseases. A common methodology to diagnose childhood diseases is by using a reasoning technique. Reasoning techniques is one of a reliable method for expert systems. Reasoning techniques using the correct case of results have provided enormous support for predicting the diagnosis and treatment of diseases. This paper focuses on the main technical characteristics of two common reasoning techniques, namely; rule-based reasoning and case-based reasoning. This paper describes a comparative analysis of rule-based and case-based reasoning techniques using several commonly used similarity measures and a study on its performance for classification tasks. Moreover, this study proposes a new case-based reasoning approach using an alternative similarity measure, called Distance-Weighted Case Base Reasoning (DW-CBR). The proposed method aims to improve classification performance. The main result of this study shows that case-based reasoning is a more powerful methodology regarding the issues of maintenance and knowledge representations over the rule-based system and reveals that DWCBR has the best accuracy, which is 92%.</p>", "Keywords": "Expert System;Childhood;Reasoning;Similarity;Nearest Neighbor;Rule Based", "DOI": "10.47839/ijc.20.2.2174", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "Indah Werdiningsih", "Affiliation": "Department of Mathematics Universitas Airlangga, Mulyorejo Street, Surabaya, 60115, Indonesia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mathematics Universitas Airlangga, Mulyorejo Street, Surabaya, 60115, Indonesia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics Universitas Airlangga, Mulyorejo Street, Surabaya, 60115, Indonesia"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Mathematics Universitas Airlangga, Mulyorejo Street, Surabaya, 60115, Indonesia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mathematics Universitas Airlangga, Mulyorejo Street, Surabaya, 60115, Indonesia"}], "References": []}, {"ArticleId": 89249291, "Title": "Information Model for Monitoring the Development of the University", "Abstract": "<p>Higher education institutions are becoming increasingly complex and self-developing systems. Monitoring the activities of such systems requires a special approach and includes three main elements, that is, external monitoring, independent evaluation and internal monitoring. In the framework of this article, the main task of monitoring is the analysis and disclosure of internal patterns, causes and trends of the development processes of a university. In these conditions, the need to move to a broader model for solving and reducing complexity based on information and knowledge is revealed. The model reduces the uncertainty of the environment and thus provides more effective decision-making. The monitoring model includes changes in the strategic planning process that are consistent with the characteristics of the new organizational model. Ontologies, as a theory of content, which allow formalizing processes and knowledge, are a key element in this context.</p>", "Keywords": "Management;Indicators;Monitoring of university activities;Ontological model;Information system", "DOI": "10.47839/ijc.20.2.2167", "PubYear": 2021, "Volume": "", "Issue": "", "JournalId": 77265, "JournalTitle": "International Journal of Computing", "ISSN": "1727-6209", "EISSN": "2312-5381", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "<PERSON><PERSON> East Kazakhstan Technical University, A.K. Protazanov Str. 69, Ust-Kamenogorsk, 070004, Kazakhstan"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> East Kazakhstan Technical University, A.K. Protazanov Str. 69, Ust-Kamenogorsk, 070004, Kazakhstan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> East Kazakhstan Technical University, A.K. Protazanov Str. 69, Ust-Kamenogorsk, 070004, Kazakhstan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "<PERSON><PERSON> University of Kyiv, Volodymyrska Str. 64/13, Kyiv, 01601, Ukraine"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Astana IT University, EXPO Business Center, block C.1., Nur-Sultan, 010000, Kazakhstan"}], "References": []}, {"ArticleId": 89249693, "Title": "Big Data Analytics in Weather Forecasting: A Systematic Review", "Abstract": "<p>Weather forecasting, as an important and indispensable procedure in people’s daily lives, evaluates the alteration happening in the current condition of the atmosphere. Big data analytics is the process of analyzing big data to extract the concealed patterns and applicable information that can yield better results. Nowadays, several parts of society are interested in big data, and the meteorological institute is not excluded. Therefore, big data analytics will give better results in weather forecasting and will help forecasters to forecast weather more accurately. In order to achieve this goal and to recommend favorable solutions, several big data techniques and technologies have been suggested to manage and analyze the huge volume of weather data from different resources. By employing big data analytics in weather forecasting, the challenges related to traditional data management techniques and technology can be solved. This paper tenders a systematic literature review method for big data analytic approaches in weather forecasting (published between 2014 and August 2020). A feasible taxonomy of the current reviewed papers is proposed as technique-based, technology-based, and hybrid approaches. Moreover, this paper presents a comparison of the aforementioned categories regarding accuracy, scalability, execution time, and other Quality of Service factors. The types of algorithms, measurement environments, modeling tools, and the advantages and disadvantages per paper are extracted. In addition, open issues and future trends are debated.</p>", "Keywords": "", "DOI": "10.1007/s11831-021-09616-4", "PubYear": 2022, "Volume": "29", "Issue": "2", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Shahr-e-Qods Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Shahr-e-Qods Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}], "References": [{"Title": "Fog-based smart homes: A systematic review", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "153", "Issue": "", "Page": "102531", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Techniques Tanimoto correlated feature selection system and hybridization of clustering and boosting ensemble classification of remote sensed big data for weather forecasting", "Authors": "Pooja S.B.; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>.", "PubYear": 2020, "Volume": "151", "Issue": "", "Page": "266", "JournalTitle": "Computer Communications"}, {"Title": "A Systematic Review on Firefly Algorithm: Past, Present, and Future", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "4", "Page": "3269", "JournalTitle": "Archives of Computational Methods in Engineering"}, {"Title": "A hybrid classification method for Twitter spam detection based on differential evolution and random forest", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "21", "Page": "e6381", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "Leveraging big data in smart cities: A systematic review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "21", "Page": "e6379", "JournalTitle": "Concurrency and Computation: Practice and Experience"}, {"Title": "A systematic review of IoT in healthcare: Applications, techniques, and trends", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "192", "Issue": "", "Page": "103164", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Towards effective offloading mechanisms in fog computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "2", "Page": "1997", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 89249758, "Title": "3D target detection using dual domain attention and SIFT operator in indoor scenes", "Abstract": "<p>In a large number of real-life scenes and practical applications, 3D object detection is playing an increasingly important role. We need to estimate the position and direction of the 3D object in the real scene to complete the 3D object detection task. In this paper, we propose a new network architecture based on VoteNet to detect 3D point cloud targets. On the one hand, we use channel and spatial dual-domain attention module to enhance the features of the object to be detected while suppressing other useless features. On the other hand, the SIFT operator has scale invariance and the ability to resist occlusion and background interference. The PointSIFT module we use can capture information in different directions of point cloud in space, and is robust to shapes of different proportions, so as to better detect objects that are partially occluded. Our method is evaluated on the SUN-RGBD and ScanNet datasets of indoor scenes. The experimental results show that our method has better performance than VoteNet.</p>", "Keywords": "Object detection; 3D point cloud; SIFT; Attention mechanism", "DOI": "10.1007/s00371-021-02217-z", "PubYear": 2022, "Volume": "38", "Issue": "11", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin, China"}], "References": []}, {"ArticleId": 89249916, "Title": "Examining runner's outdoor heat exposure using urban microclimate modeling and GPS trajectory mining", "Abstract": "It is important to quantify human heat exposure in order to evaluate and mitigate the negative impacts of heat on human well-being in the context of global warming. This study proposed a human-centric framework to examine human personal heat exposure based on anonymous GPS trajectories data mining and urban microclimate modeling. The mean radiant temperature (T<sub>mrt</sub>) that represents human body's energy balance was used to indicate human heat exposure. The meteorological data and high-resolution 3D urban model generated from multispectral remotely sensed images and LiDAR data were used as inputs in urban microclimate modeling to map the spatio-temporal distribution of the T<sub>mrt</sub> in the Boston metropolitan area. The anonymous human GPS trajectory data collected from fitness Apps was used to map the spatio-temporal distribution of human outdoor activities. By overlaying the anonymous GPS trajectories on the generated spatio-temporal maps of T<sub>mrt</sub>, this study further examined the heat exposure of runners in different age-gender groups in the Boston area. Results show that there is no significant difference in terms of heat exposure for female and male runners. The female runners in the age of 45–54 are exposed to more heat than female runners of 18–24 and 25–34, while there is no significant difference among male runners. This study proposed a novel method to estimate human heat exposure, which would shed new light on mitigating the negative impacts of heat on human health.", "Keywords": "Personal heat exposure ; Urban heat ; GPS trajectories ; Urban climate modeling ; Mean radiant temperature (T<sub>mrt</sub>)", "DOI": "10.1016/j.compenvurbsys.2021.101678", "PubYear": 2021, "Volume": "89", "Issue": "", "JournalId": 1688, "JournalTitle": "Computers, Environment and Urban Systems", "ISSN": "0198-9715", "EISSN": "1873-7587", "Authors": [{"AuthorId": 1, "Name": "Xiaojiang Li", "Affiliation": "Department of Geography and Urban Studies, Temple University, Philadelphia, PA 19038, United States of America;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "NASA Goddard Space Flight Center, Greenbelt, MD, 20771, USA;Science Systems and Applications, Inc. (SSAI), Lanham, MD, 20706, USA"}], "References": []}, {"ArticleId": 89250073, "Title": "LSTM-assisted evolutionary self-expressive subspace clustering", "Abstract": "<p>Massive volumes of high-dimensional data that evolve over time are continuously collected by contemporary information processing systems, which bring up the problem of organizing these data into clusters, i.e. achieving the purpose of dimensional reduction, and meanwhile learning their temporal evolution patterns. In this paper, a framework for evolutionary subspace clustering, referred to as LSTM–ESCM, is introduced, which aims at clustering a set of evolving high-dimensional data points that lie in a union of low-dimensional evolving subspaces. In order to obtain the parsimonious data representation at each time step, we propose to exploit the so-called self-expressive trait of the data at each time point. At the same time, LSTM networks are implemented to extract the inherited temporal patterns behind data in the overall time frame. An efficient algorithm has been proposed. Numerous experiments are carried out on real-world datasets to demonstrate the effectiveness of our proposed approach. The results show that the suggested algorithm dramatically outperforms other known similar approaches in terms of both run time and accuracy.</p>", "Keywords": "Subspace clustering; Evolutionary clustering; Self-expressive models; Motion segmentation; LSTM; Deep learning; Temporal data", "DOI": "10.1007/s13042-021-01363-z", "PubYear": 2021, "Volume": "12", "Issue": "10", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "AI in Imaging and Neuroscience Lab, Faculty of Computer Science, The University of California, Los Angeles, USA"}, {"AuthorId": 2, "Name": "Mingyuan Bai", "Affiliation": "Discipline of Business Analytics, The University of Sydney Business School, Sydney, Australia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Beijing Advanced Innovation Center for Future Internet Technology, Beijing Municipal Key Lab of Multimedia and Intelligent Software Technology, Faculty of Information Technology, Beijing University of Technology, Beijing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Discipline of Business Analytics, The University of Sydney Business School, Sydney, Australia"}], "References": []}, {"ArticleId": 89250171, "Title": "Frequency dependent mass and stiffness matrices of bar and beam elements and their equivalency with the dynamic stiffness matrix", "Abstract": "Starting from the solutions of the governing differential equations of motion in free vibration, the frequency dependent mass and stiffness matrices of bar and beam elements have been derived in this paper, but importantly, their equivalency with the corresponding dynamic stiffness matrix is established. In sharp contrast to series solutions, reported in the literature, explicit expressions for each term of the frequency dependent mass and stiffness matrices of bar and beam elements are generated in concise form through the application of symbolic computation and their relationship with the single dynamic stiffness matrix (which contains both the mass and stiffness properties) for each of the two element types is highlighted. The theory is demonstrated by numerical results. By splitting the dynamic stiffness matrix into frequency dependent mass and stiffness matrices and at the same time retaining the exactness of results, the investigation paves the way for future research to overcome the difficulty to include damping in the dynamic stiffness research which has not been possible earlier. Furthermore, the frequency dependent mass and stiffness matrices derived in this paper permit the application of the W<PERSON><PERSON>-<PERSON> algorithm to compute with certainty the exact natural frequencies of structures comprising bar and beam elements.", "Keywords": "Dynamic stiffness method ; Free vibration analysis ; <PERSON><PERSON><PERSON><PERSON> algorithm ; Frequency dependent mass and stiffness matrices", "DOI": "10.1016/j.compstruc.2021.106616", "PubYear": 2021, "Volume": "254", "Issue": "", "JournalId": 5132, "JournalTitle": "Computers & Structures", "ISSN": "0045-7949", "EISSN": "1879-2243", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering and Aeronautics, School of Mathematics, Computer Science and Engineering, City, University of London, Northampton Square, London EC1V 0HB, United Kingdom"}], "References": []}, {"ArticleId": 89250194, "Title": "An energy‐aware virtual machines consolidation method for cloud computing: Simulation and verification", "Abstract": "<p>Cloud systems have become an essential part of our daily lives owing to various Internet-based services. Consequently, their energy utilization has also become a necessary concern in cloud computing systems increasingly. Live migration, including several virtual machines (VMs) packed on in minimal physical machines (PMs) as virtual machines consolidation (VMC) technique, is an approach to optimize power consumption. In this article, we have proposed an energy-aware method for the VMC problem, which is called energy-aware virtual machines consolidation (EVMC), to optimize the energy consumption regarding the quality of service guarantee, which comprises: (1) the support vector machine classification method based on the utilization rate of all resource of PMs that is used for PM detection in terms of the amount' load; (2) the modified minimization of migration approach which is used for VM selection; (3) the modified particle swarm optimization which is implemented for VM placement. Also, the evaluation of the functional requirements of the method is presented by the formal method and the non-functional requirements by simulation. Finally, in contrast to the standard greedy algorithms such as modified best fit decreasing, the EVMC decreases the active PMs and migration of VMs, respectively, 30%, 50% on average. Also, it is more efficient for the energy 30% on average, resources and the balance degree 15% on average in the cloud.</p>", "Keywords": "cloud computing systems (CCSs);data center;energy consumption;formal verification;virtual machines consolidation (VMC)", "DOI": "10.1002/spe.3010", "PubYear": 2022, "Volume": "52", "Issue": "1", "JournalId": 1840, "JournalTitle": "Software: Practice and Experience", "ISSN": "0038-0644", "EISSN": "1097-024X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Science and Research Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer Engineering, South Tehran Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Future Technology Research Center, National Yunlin University of Science and Technology, Douliou, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Saveh Branch, Islamic Azad University, Saveh, Iran"}], "References": [{"Title": "Adaptive Markov‐based approach for dynamic virtual machine consolidation in cloud data centers with quality‐of‐service constraints", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "50", "Issue": "2", "Page": "161", "JournalTitle": "Software: Practice and Experience"}, {"Title": "New comprehensive model based on virtual clusters and absorbing Markov chains for energy-efficient virtual machine management in cloud computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "76", "Issue": "9", "Page": "7438", "JournalTitle": "The Journal of Supercomputing"}, {"Title": "A novel energy-aware resource management technique using joint VM and container consolidation approach for green computing in cloud data centers", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "102127", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "Application of virtual machine consolidation in cloud computing systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON> <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "30", "Issue": "", "Page": "100524", "JournalTitle": "Sustainable Computing: Informatics and Systems"}]}, {"ArticleId": 89250280, "Title": "image2emmet: Automatic code generation from web user interface image", "Abstract": "<p>Web development usually follows with analyzing the functionality, designing the user interface (UI) prototype, implementing the UI by front-end (FE) developers and implementing the REpresentational State Transfer (RESTful) application programming interface (API) by back-end (BE) programmers. Unfortunately, web development is a tedious, cumbersome, and time-consuming task, which makes it a challenge for the FE programmers to work in an efficient way. In this paper, we propose an approach, image2emmet, to assist FE programmers in implementing the UI. First, we collect HyperText Markup Language, Cascading Style Sheets (HTML-CSS) dataset in an automatic and efficient way. The HTML-CSS dataset used for model training consists of HTML-CSS code and its display images. Second, the faster region-based convolutional neural network (CNN) (R-CNN) is utilized to detect the UI component. Finally, we build a model combining CNN and long short-term memory (LSTM) to transform the UI component into the HTML-CSS code. The empirical study demonstrates that image2emmet can achieve a precision of 80% on the UI component detection and 60% on the transformation of UI component into HTML-CSS code.</p>", "Keywords": "code generation;HTML-CSS code;UI component;web development", "DOI": "10.1002/smr.2369", "PubYear": 2021, "Volume": "33", "Issue": "8", "JournalId": 2327, "JournalTitle": "Journal of Software: Evolution and Process", "ISSN": "2047-7473", "EISSN": "2047-7481", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou China; State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>bing Sun", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou China; State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Information Engineering, Yangzhou University, Yangzhou China; State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 5, "Name": "Jing <PERSON>", "Affiliation": "School of Computer Science and Engineering, Beihang University, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Aviation Service Research Center, Air Force Logistics University, Xuzhou, Jiangsu, China"}], "References": [{"Title": "Analyzing bug fix for automatic bug cause classification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "163", "Issue": "", "Page": "110538", "JournalTitle": "Journal of Systems and Software"}, {"Title": "Automatic Code Generation of MVC Web Applications", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "9", "Issue": "3", "Page": "56", "JournalTitle": "Computers"}]}, {"ArticleId": 89250322, "Title": "Research on path planning of three-neighbor search A* algorithm combined with artificial potential field", "Abstract": "<p>Among the shortcomings of the A* algorithm, for example, there are many search nodes in path planning, and the calculation time is long. This article proposes a three-neighbor search A* algorithm combined with artificial potential fields to optimize the path planning problem of mobile robots. The algorithm integrates and improves the partial artificial potential field and the A* algorithm to address irregular obstacles in the forward direction. The artificial potential field guides the mobile robot to move forward quickly. The A* algorithm of the three-neighbor search method performs accurate obstacle avoidance. The current pose vector of the mobile robot is constructed during obstacle avoidance, the search range is narrowed to less than three neighbors, and repeated searches are avoided. In the matrix laboratory environment, grid maps with different obstacle ratios are compared with the A* algorithm. The experimental results show that the proposed improved algorithm avoids concave obstacle traps and shortens the path length, thus reducing the search time and the number of search nodes. The average path length is shortened by 5.58%, the path search time is shortened by 77.05%, and the number of path nodes is reduced by 88.85%. The experimental results fully show that the improved A* algorithm is effective and feasible and can provide optimal results.</p>", "Keywords": "", "DOI": "10.1177/17298814211026449", "PubYear": 2021, "Volume": "18", "Issue": "3", "JournalId": 1212, "JournalTitle": "International Journal of Advanced Robotic Systems", "ISSN": "1729-8814", "EISSN": "1729-8814", "Authors": [{"AuthorId": 1, "Name": "Ji<PERSON> Chen", "Affiliation": "School of Mechanical Engineering, Guangxi University, Nanning, China;Manufacturing System and Advanced Manufacturing Technology Key Laboratory, Guangxi Univerisity, Nanning, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Guangxi University, Nanning, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Mechanical Engineering, Guangxi University, Nanning, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "School of Mechanical Engineering, Guangxi University, Nanning, China"}, {"AuthorId": 5, "Name": "Ganwei Cai", "Affiliation": "School of Mechanical Engineering, Guangxi University, Nanning, China;Manufacturing System and Advanced Manufacturing Technology Key Laboratory, Guangxi Univerisity, Nanning, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Mechatronic Engineering and Automation, Shanghai University, Shanghai, China"}], "References": []}, {"ArticleId": 89250337, "Title": "Convergence Analysis of the Hessian Estimation Evolution\n Strategy", "Abstract": "<p>The class of algorithms called Hessian Estimation Evolution Strategies (HE-ESs) update the covariance matrix of their sampling distribution by directly estimating the curvature of the objective function. The approach is practically efficient, as attested by respectable performance on the BBOB testbed, even on rather irregular functions.</p><p>In this paper we formally prove two strong guarantees for the (1+4)-HE-ES, a minimal elitist member of the family: stability of the covariance matrix update, and as a consequence, linear convergence on all convex quadratic problems at a rate that is independent of the problem instance.</p>", "Keywords": "Evolution strategy;covariance matrix adaptation;linear convergence", "DOI": "10.1162/evco_a_00295", "PubYear": 2022, "Volume": "30", "Issue": "1", "JournalId": 11547, "JournalTitle": "Evolutionary Computation", "ISSN": "1063-6560", "EISSN": "1530-9304", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Institute for Neural Computation, Ruhr-University Bochum, Germany "}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, University of Copenhagen, Denmark "}], "References": []}, {"ArticleId": 89250427, "Title": "Sparsity of weighted networks: Measures and applications", "Abstract": "A majority of real life networks are weighted and sparse. The present article aims at characterization of weighted networks based on sparsity , as an indicator of inherent diversity of different network parameters. The measure called s parsity index defined on ordered degree sequence of simple networks is extended and new properties of this index are derived. The range of possible values of sparsity index of any connected network, with edge-count in specific intervals, is worked out analytically in terms of node-count and a pattern is uncovered in corresponding degree sequences. Given the edge-weight frequency distribution of a network, an expression of the sparsity index of edge-weights is formulated. Its properties are analyzed under different distributions of edge-weights. For example, the upper and lower bounds of sparsity index of edge-weights of a network, with all distinct edge-weights, is determined in terms of its node-count and edge-density. The article highlights that this summary index with low computational cost, computed on different network parameters, is useful to reveal different structural and organizational aspects of networks for performing analysis. An application of this index is demonstrated through devising a new overlapping community detection method. The results validated on artificial and real-world networks show its efficacy. Introduction Network graphs are often used to model relationships among a group of interacting entities in the physical world. The entities themselves are considered as nodes, an interaction between two entities is represented by an edge and the intensities of interactions are represented by edge-weights. There are many real-life examples of such networks, and they arise from different walks of human existence. The social [19], [50], biological [16], [47], cognitive [3], [43] relationships in human populations, the behavioral relationships among living creatures [38], the economic and trade relationships among nations or other territories [27], are examples of areas in which networks have been used extensively for research. A network is unweighted, when the model focuses only on the existence of a relationship between a pair of entities and ignores its intensity. Similarly, a network is undirected, when the direction of the relationship between two entities is of no significance for the model. The networks that are of specific interest to us, for the present article, are the weighted networks. In the network science literature of late, more emphasis is being given on the study of weighted networks. Weighted networks tend to mimic different real-world phenomena more closely than their unweighted counterparts, by attaching importance to the strength or magnitude of interactions at the modeling stage. In analyzing many networks that are part and parcel of modern human civilization, for example, transportation networks [4], [49], communication networks [29], [31], web networks [5], [44] and so on, weighted networks find more appropriate use. Also, it is a common observation that large-scale real life networks are highly sparse. Therefore, studying the network property of sparsity, in the case of weighted networks, is worthwhile. For a network graph, sparsity reveals the extent of the graph’s deviation from the corresponding fully connected graph. Sparsity lies at the opposite end of the spectrum of density of the graph. However, the term sparsity carries a different meaning when applied in the context of distribution of a certain quantity among a group of entities. In that context, sparsity indicates the relative diversity in distribution of a certain resource among related entities. For a network, we may be interested to measure how sparsely distributed the total degree (degree of a node is the number of edges incident on that node) of the graph is among its constituent nodes. This question assumes tremendous importance from various network analysis viewpoints. For example, in studying the propagation of failure through a system of inter-dependent networks [23], it has been shown that systems having degree distributions with higher variability tend to be more robust to random failures. From a community detection viewpoint, a network scoring low on sparsity of its node-degrees is unlikely to yield good communities. For dynamical processes like disease spreading, rumor spreading, opinion formation and evolution of social behaviors in networks, network topology has a fundamental role to play and sparsity of degree distribution, or degree heterogeneity, is of particular importance [37]. Sparsity index [18] is a summary measure based on the number of nodes, the degrees of the nodes and a constant factor at least as large as the total degrees of all nodes of the network. It provides an indication to the extent of heterogeneity in distribution of degrees among the constituent nodes of the network. With a suitable choice of the constant factor as mentioned in its definition, the measure provides an assessment of the graph’s deviation from the corresponding complete graph. Notably, this measure applies equally on weighted and unweighted networks, as it concerns itself primarily with degrees of nodes of the network. In this article we have delved deeper into properties of this index and have shown analytically that the sparsity index attains the highest value for a star graph and the lowest value for a Hamiltonian path among connected graphs with the same number of nodes and edges. This is a reflection of the way sparsity index addresses the structural aspect of heterogeneity associated with complex networks. The limiting value of sparsity index is found to be ½ for an infinite star network. Further, a pattern has been uncovered in degree sequences of networks to produce maximum sparsity with specific edge-counts. On a similar note, one may also be interested to find out a measure of sparsity of distribution of edge weights of a network. Edge-weights denote the intensities or the frequencies of connections; to what extent they vary in a network is a network property which can be exploited to analyze other properties and explain behavior of the network. For instance, in formulating a measure of complexity of brain networks, namely Shannon graph complexity [17], Gomez-Pilar et al. make use of the weight distribution in hypothesizing that for a fixed topology, the weight distribution of a complex network is directly related to its reliability and information propagation speed. In bioinformatics domain, Apeltsin et al. [1] have shown that pre-filtering a protein similarity network, based on a certain threshold on edge-weights, leads to an improvement in performance of clustering algorithms meant to group functionally similar proteins. The study further brings out a relationship between one such optimal threshold and the shape of the edge-weight distribution of the input protein similarity network. In the telecom domain, Dasgupta et al. [9] examine how social ties impact the propensity to churn in mobile telecom networks. Based on diffusion model, their work brings out the role of strong and weak ties (tie strength is expressed as edge-weight) in spreading the influence through the network. Interestingly however, all these different scenarios display a power-law distribution for the edge weights, with exponents lying somewhere between 1 and 3. In fact, it is not uncommon to notice power-law in the edge-weight distributions of a variety of large real-world networks [8], [42]. Therefore, the nature of the distribution of edge weights is an important consideration for analysis of weighted networks and this may lead to exploration of other characteristics of the network. It may be noted that the degree equivalent in a weighted network is strength [30]. The strength of a node is defined as the total weight of all edges incident on that node. Conceptually, strength represents an amalgamation of a node’s degree and associated edge-weight. For weighted networks, it is legitimate to ask for a measure of sparsity of the strength distribution of its nodes. For example, a community detection process in general, which takes into account edge-weights in some way to form the communities, may benefit from using this measure. A low value of this measure may straightaway indicate that well-defined communities may not be present at all within the network. Thus, a sparsity measure can be constructed on various network parameters and be computed to throw light on the extent of diversity present in that specific aspect of the network. The computational cost is insignificant (to the order of nlogn ) to compute these summary measures even for large networks, but the knowledge of the organization and structure of the network that is obtained through these measures during initial examination, may be significant. From information theoretic viewpoint, a measure which is often associated with degree distribution of complex networks is Shannon entropy . The definition of entropy based on a random walk model of random traversal through the network, quantifies the heterogeneity in the network’s degree distribution, in a sense that nodes with low degrees lower the overall network entropy, while high degree hubs cause its increase [46]. An interpretation of entropy, according to Wiedermann et al., is a measure of regularity or order in the network with respect to its navigability that is measured in terms of random walk. Just as sparsity index is a normalized measure, so is entropy, whereas the two approaches towards measuring diversity are very different and one is not quite a substitute of the other. To demonstrate an application of the sparsity indices discussed in this article, we have chosen the area of community detection. The proposed method identifies the central nodes to form communities based on the ordered sequence of node-strength of the network and thereby it makes use of the edge-weight information of the network. The method follows the overall structure of the overlapping community detection method as proposed by Goswami et al. [18] for simple networks. While the method for simple networks uses sparsity index defined on ordered degree sequence, the proposed method uses sparsity index defined on ordered strength sequence of the network. At the candidate community formation stage the new method makes use of the sparsity index on edge-weights. Notably, the method does not require any input parameter in terms of number or size of the communities and is focused on uncovering natural groupings of nodes that are inherent in the network. A common approach to study network properties, or, measures reflecting different network characteristics, is to uncover them first for simple networks and extend them, later, for weighted networks. For example, the centrality measure betweenness [11] was first introduced for simple networks and was later used by Park et al. [35] to characterize weighted complex networks. The authors analyze how this centrality measure relates to the degrees and the weights of a network. Another instance is the work by Saramaki et al. [40], which deals with several generalizations of another important network parameter clustering coefficient , for weighted networks. Further, Garas et al. [12] provide a generalization of a method k -shell decomposition , commonly used to identify the most influential nodes in terms of spreading in networks, for weighted networks. In this article, we follow similar approach, by extending a measure of sparsity originally proposed for simple networks to weighted networks. The main contribution of this paper is three-fold: discovering new properties of sparsity index based on degree sequence of networks; formulating sparsity index of edge weights of networks and exploring some of its properties; demonstrating an application of the newly formulated index by devising a community detection algorithm. Towards this end, we have shown analytically that the sparsity index based on ordered degree sequence of a connected graph with n ( ≥ 3 ) nodes and ( n - 1 ) edges lies between the sparsity indices of a Hamiltonian path and a star network of the same number of nodes and edges. In the process, the progression of the graphs to attain the lowest to the highest values of this index has been brought out by using the corresponding degree sequences. Also, a general version of the result has been included, in the sense that the condition on edge-counts has been relaxed and the upper and lower bounds of sparsity indices have been worked out in terms of sequential intervals of edge-counts, such that any connected graph can fit in one of the intervals. This has been done in Section 2 of this document. Given the frequency distribution of edge weights of a connected network, a formulation of the sparsity index of edge weights has been obtained directly from the corresponding Lorenz curve [13] definition and the index itself has been studied under different edge-weight frequency distributions. This has been done in Section 3. A node-strength based community detection algorithm has been proposed for weighted graphs. This method utilizes sparsity index on node-strengths, Gini index of node-degrees and sparsity index on edge-weights at different stages of the process and follows the overall structure of an existing algorithm meant for simple networks. The method has been applied on a synthetic network and a few benchmark real life networks for validation. The community detection method and the related experimental results appear in Section 4 of this document. In Section 5, the article has been concluded after summarizing the implications of the results obtained, both theoretically and experimentally, with a discussion on directions in which future work may proceed. Figures Lorenz curves for sparsity of degree distribution of nodes, drawn with two different choices of T1: (1), (2)T1=T T1>T. Degree sequences of connected graphs with n=7 nodes and (n-1) edges in increasing order of their sparsity indices. A pattern can be noticed in the degree sequences and the corresponding graphs. We hav... The table shows the highest sparsity index value (fourth column) and the corresponding degree sequence (third column) of a connected graph with given edge-count (second column) and n number of nodes. ... Lorenz curve to denote the sparsity of distribution of edge-weights of a weighted network. Communities as detected by the present algorithm on the example graph [38], along with relevant sparsity indices and quality measures. Community discovery in the Dolphin social network: Figure (a) represents the communities identified on the unweighted version of the network, by the sparsity index-based community detection algorithm ... Show all figures Section snippets Sparsity measure of network graphs By applying a sparsity measure on a network graph, weighted or unweighted, one expects to get an idea of i) the extent to which it deviates from a fully connected network ii) the inherent variability among the degrees of nodes of the network. Sparsity index has the potential to throw light on both these aspects, by appropriately choosing the constant factor at least as large as the total degrees of all nodes, as used in its expression (refer to expression (1) in section 2.2). As we focus our Edge-weight distribution and sparsity of networks In this section, we are going to investigate sparsity of networks for different scenarios of edge-weight distribution. We have presented three different edge-weight distributions, for which we have been able to work out the upper and lower bounds of weighted sparsity index SI ( G w ) . The first scenario depicts networks with binary weight values, in other words, they also represent unweighted networks. Instead of upper and lower bounds, we have arrived at the quantity to which the index is exactly Community detection and sparsity of weighted networks We are interested in exploring the use of weighted sparsity index in network analysis. A major area within network analysis is devoted to reveal functions and organizations of networks through the discovery of community structures. Although there is a plethora of community detection algorithms which can be applied to weighted networks, each one of them works with full efficiency within its specific area of application, i.e., domain context, underlying assumptions and the specific objective to Conclusion For a network graph, sparsity is a property to indicate the inherent variability of a certain quantity associated with the graph. Among different measures of sparsity, in this article we have focused on a specific measure called sparsity index, defined along the line of Gini index, from Lorenz curve. Sparsity index is a summary measure and is normalized. It was defined originally on the ordered degree sequence of a network graph. A theoretical result worked out in the present article shows that CRediT authorship contribution statement Swati Goswami: Conceptualization, Methodology, Software, Investigation, Writing - original draft. Asit K. Das: Supervision. Subhas C. Nandy: Validation, Project administration. Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgement This work has been financially supported by the Department of Science and Technology, Government of India through a research grant under the Women Scientist Scheme – A, vide reference number ET28/2017, as part of the project titled “characterization and analysis of interaction networks”. We are deeply indebted to late Prof. C. A. Murthy for his contribution towards producing this paper. With a sense of gratitude, we fondly remember his insight into making instructive suggestions and critical References (50) M.P. van den Heuvel et al. Network hubs in the human brain Trends Cognit. Sci. (2013) H. Shen et al. Detect overlapping and hierarchical community structure in networks Physica A (2009) A. Pérez-Suárez et al. An algorithm based on density and compactness for dynamic overlapping clustering Pattern Recogn. (2013) A. Medus et al. Detection of community structures in networks via global optimization Physica A (2005) S. Goswami et al. Sparsity measure of a network graph: gini index Inf. Sci. (2018) D. Chen et al. Detecting overlapping communities of weighted networks via a local algorithm Physica A (2010) S.J. Cairns et al. A comparison of association indices Anim. Behav. (1987) A. Broder et al. Graph structure in the web Comput. Netw. (2000) A. Baronchelli et al. Networks in cognitive science Trends Cognit. Sci. (2013) L. Apeltsin et al. Improving the quality of protein similarity network clustering algorithms using the network edge weight distribution Bioinformatics (2011) J. Aslam et al. November. Static and dynamic information organization with star clusters A. Barrat, M. Barthelemy, A. Vespignani. The Architecture of Complex Weighted Networks: Measurements and Models. Large... H. Cherifi, B. Gonçalves, R. Menezes, R. Sinatra. Complex networks VII. In Proceedings of the 7th workshop on complex... K. Dasgupta et al. R. Diestel Graph theory {graduate texts in mathematics (2000) L.C. Freeman A set of measures of centrality based on betweenness Sociometry (1977) A. Garas et al. A k-shell decomposition method for weighted networks New J. Phys. (2012) J.L. Gastwirth The estimation of the Lorenz curve and Gini index Rev. Econ. Statist. (1972) R.J. Gil-García et al. November. Extended star clustering algorithm C. Gini. Italian: Variabilità e mutabilità. Variability and Mutability’, C. Cuppini, Bologna.... M. Girvan et al. Community structure in social and biological networks Proc. Natl. Acad. Sci. USA (2001) J. Gomez-Pilar et al. Quantification of graph complexity based on the edge weight distribution balance: application to brain networks Int. J. Neural Syst. (2018) R. Guimera et al. Self-similar community structure in a network of human interactions Phys. Rev. E (2003) S. Hakami On the realizability of a set of integers as degrees of the vertices of a linear graph SIAM J. Appl. Math. (1962) P. Held, R. Kruse. Online community detection by using nearest hubs. arXiv preprint arXiv:1601.06527.... View more references Cited by (0) Recommended articles (6) View full text © 2021 Elsevier Inc. All rights reserved. About ScienceDirect Remote access Shopping cart Advertise Contact and support Terms and conditions Privacy policy We use cookies to help provide and enhance our service and tailor content and ads. By continuing you agree to the use of cookies . Copyright © 2021 Elsevier B.V. or its licensors or contributors. ScienceDirect ® is a registered trademark of Elsevier B.V. ScienceDirect ® is a registered trademark of Elsevier B.V.", "Keywords": "", "DOI": "10.1016/j.ins.2021.06.090", "PubYear": 2021, "Volume": "577", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Indian Institute of Engineering Science and Technology, Shibpur, Howrah 711103, India;Advanced Computing and Microelectronics Unit, Indian Statistical Institute, Kolkata 700108, India;Corresponding author at: Advanced Computing and Microelectronics Unit, Indian Statistical Institute, Kolkat, 700108, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Indian Institute of Engineering Science and Technology, Shibpur, Howrah 711103, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Advanced Computing and Microelectronics Unit, Indian Statistical Institute, Kolkata 700108, India"}], "References": []}, {"ArticleId": 89250443, "Title": "Android malware detection via an app similarity graph", "Abstract": "Due to the ever-increasing number of Android applications and constant advances in software development techniques, there is a need for scalable and flexible malware detectors that can efficiently address big data challenges. Motivated by large-scale recommender systems, we propose a static Android application analysis method which relies on an app similarity graph (ASG). We believe that the key to classifying app’s behavior lies in their common reusable building blocks, e.g. functions, in contrast to expert based features. We demonstrate our method on the Drebin benchmark in both balanced and unbalanced settings, on a brand new VTAz dataset from 2020, and on a dataset of approximately 190K applications provided by VirusTotal, achieving an accuracy of 0.975 in balanced settings, and AUC score of 0.987. The analysis and classification time of the proposed methods are notably lower than in the reviewed research (from 0.08 to 0.153 sec/app).", "Keywords": "Android ; Graph representations ; Machine learning ; Malware detection ; Matrix factorization ; Node embedding ; Recommender system ; Static analysis", "DOI": "10.1016/j.cose.2021.102386", "PubYear": 2021, "Volume": "109", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Telekom Innovation Labs, and Cyber Security Research Center at Ben Gurion University of the Negev, Beer Sheva, Israel;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Telekom Innovation Labs, and Cyber Security Research Center at Ben Gurion University of the Negev, Beer Sheva, Israel"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Telekom Innovation Labs, and Cyber Security Research Center at Ben Gurion University of the Negev, Beer Sheva, Israel"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Software and Information Systems Engineering, Telekom Innovation Labs, and Cyber Security Research Center at Ben Gurion University of the Negev, Beer Sheva, Israel"}], "References": [{"Title": "DL-Droid: Deep learning based android malware detection using real devices", "Authors": "<PERSON>; <PERSON><PERSON><PERSON> <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "89", "Issue": "", "Page": "101663", "JournalTitle": "Computers & Security"}, {"Title": "Deep learning for effective Android malware detection using API call graph embeddings", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "24", "Issue": "2", "Page": "1027", "JournalTitle": "Soft Computing"}, {"Title": "Similarity-based Android malware detection using Hamming distance of static binary features", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "230", "JournalTitle": "Future Generation Computer Systems"}, {"Title": "Intelligent mobile malware detection using permission requests and API calls", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "107", "Issue": "", "Page": "509", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 89250464, "Title": "Measurement of liquid thermo-optical coefficient based on all-fiber hybrid FPI-SPR sensor", "Abstract": "This paper proposes and demonstrates an integrated optical fiber sensor, which combines Fabry-Perot interference (FPI) and surface plasmon resonance (SPR) to measure the thermo-optical coefficient of liquid samples. SPR is excited by the total reflection of light on the capillary fiber wall and the reflected light on the two reflective surfaces of the Fabry-Perot (FP) cavity to produce FPI. Two sensing units measure temperature and refractive index (RI), respectively. This sensor not only separates two sensing wavebands but also solves the inherent cross-sensitivity problems. The SPR sensing unit has a sensitivity of 2565.4 nm/RIU, and its sensing waveband range is 500−900 nm. The FPI sensing unit has a sensitivity of 394.3 pm/°C, and its sensing waveband range is 1500−1600 nm. The measurement range of RI is 1.333–1.393, and the measurement range of temperature is 20 °C–80 °C. We verify the feasibility of the sensor in various liquid samples. The experimental results indicate that the measured value of the thermo-optical coefficient agrees with the theoretical value. The measurement error of the thermo-optical coefficient of absolute ethanol is about 0.51 %. This research provides a novel insight into solving the inherent cross-sensitivity problems and how to separate the sensing wavebands.", "Keywords": "Surface plasmon resonance ; Fabry-Perot interference ; Capillary fiber ; Integrated sensor", "DOI": "10.1016/j.sna.2021.112954", "PubYear": 2021, "Volume": "331", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China;Corresponding author"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China;National Demonstration Center for Experimental Physics Education, Harbin Engineering University, Harbin, 150001, PR China"}, {"AuthorId": 6, "Name": "<PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Lab of In-fiber Integrated Optics, Ministry Education of China, Harbin Engineering University, Harbin, 150001, PR China"}, {"AuthorId": 9, "Name": "Libo Yuan", "Affiliation": "Photonics Research Center, Guilin University of Electronics Technology, Guilin, 541004, PR China"}], "References": []}, {"ArticleId": 89250465, "Title": "Tricolor emission carbon dots for label-free ratiometric fluorescent and colorimetric recognition of Al3+ and pyrophosphate ion and cellular imaging", "Abstract": "Ratiometric determination endows fluorescence (FL) sensors improving accuracy and sensitivity by measuring intensity ratio of well-resolved emission peaks. Herein, tricolor-emission carbon dots (T-CDs) have been developed for ratiometric recognition of Al<sup>3+</sup> and pyrophosphate ion (PPi). T-CDs were fabricated from hematoxylin and Tris by one-pot hydrothermal treatment. As-prepared T-CDs engender amusing triple emission bands, including bright yellow and orange FL, but weak blue FL. Upon introducing Al<sup>3+</sup>, yellow and orange FL is distinctly quenched while blue one is enhanced. On subsequent addition of PPi, T-CDs’ FL can be partially recovered. The ratiometric fluorescent changes of T-CDs have been utilized to detect Al<sup>3+</sup> and PPi in aqueous solution with linear ranges of 5–15 μM and 0.75–10 μM, respectively, as well as limits of detection of 0.8 nM and 2.2 nM, respectively. More importantly, laser scanning confocal microscope imaging of HeLa cells demonstrates that T-CDs could be applied to visually monitor Al<sup>3+</sup> and PPi fluctuations in living cells, illustrating that proposed T-CDs have tremendous potential in biological systems.", "Keywords": "Tricolor emission ; Carbon dots ; Al<sup>3+</sup> ; PPi ; Ratiometric fluorescent ; Colorimetric", "DOI": "10.1016/j.snb.2021.130375", "PubYear": 2021, "Volume": "345", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry and Chemical Engineering, Shanxi University, Taiyuan, 030006, China;Corresponding authors"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry and Chemical Engineering, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry and Chemical Engineering, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry and Chemical Engineering, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Chemistry and Chemical Engineering, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Chemistry and Chemical Engineering, Shanxi University, Taiyuan, 030006, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Chemistry and Chemical Engineering, Shanxi University, Taiyuan, 030006, China;Corresponding authors"}], "References": []}, {"ArticleId": 89250503, "Title": "A survey on indoor 3D modeling and applications via RGB-D devices", "Abstract": "<p>With the fast development of consumer-level RGB-D cameras, real-world indoor three-dimensional (3D) scene modeling and robotic applications are gaining more attention. However, indoor 3D scene modeling is still challenging because the structure of interior objects may be complex and the RGB-D data acquired by consumer-level sensors may have poor quality. There is a lot of research in this area. In this survey, we provide an overview of recent advances in indoor scene modeling methods, public indoor datasets and libraries which can facilitate experiments and evaluations, and some typical applications using RGB-D devices including indoor localization and emergency evacuation.</p>", "Keywords": "3D indoor mapping; RGB-D; Indoor localization; Construction monitoring; Emergency evacuation; 三维室内制图; RGB-D; 室内定位; 施工监测; 应急疏散; P232", "DOI": "10.1631/FITEE.2000097", "PubYear": 2021, "Volume": "22", "Issue": "6", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Architecture and Urban Planning, Research Institute for Smart Cities, Shenzhen University & China Guangdong–Hong Kong–Macau Joint Laboratory for Smart Cities & Key Laboratory of Urban Land Resources Monitoring and Simulation, Ministry of Natural Resources, Shenzhen, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Architecture and Urban Planning, Research Institute for Smart Cities, Shenzhen University & China Guangdong–Hong Kong–Macau Joint Laboratory for Smart Cities & Key Laboratory of Urban Land Resources Monitoring and Simulation, Ministry of Natural Resources, Shenzhen, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Architecture and Urban Planning, Research Institute for Smart Cities, Shenzhen University & China Guangdong–Hong Kong–Macau Joint Laboratory for Smart Cities & Key Laboratory of Urban Land Resources Monitoring and Simulation, Ministry of Natural Resources, Shenzhen, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Information Engineering in Surveying Mapping and Remote Sensing, Wuhan University, Wuhan, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Architecture and Urban Planning, Research Institute for Smart Cities, Shenzhen University & China Guangdong–Hong Kong–Macau Joint Laboratory for Smart Cities & Key Laboratory of Urban Land Resources Monitoring and Simulation, Ministry of Natural Resources, Shenzhen, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "School of Architecture and Urban Planning, Research Institute for Smart Cities, Shenzhen University & China Guangdong–Hong Kong–Macau Joint Laboratory for Smart Cities & Key Laboratory of Urban Land Resources Monitoring and Simulation, Ministry of Natural Resources, Shenzhen, China"}], "References": []}, {"ArticleId": 89250504, "Title": "Decentralized multi-agent reinforcement learning with networked agents: recent advances", "Abstract": "<p>Multi-agent reinforcement learning (MARL) has long been a significant research topic in both machine learning and control systems. Recent development of (single-agent) deep reinforcement learning has created a resurgence of interest in developing new MARL algorithms, especially those founded on theoretical analysis. In this paper, we review recent advances on a sub-area of this topic: decentralized MARL with networked agents. In this scenario, multiple agents perform sequential decision-making in a common environment, and without the coordination of any central controller, while being allowed to exchange information with their neighbors over a communication network. Such a setting finds broad applications in the control and operation of robots, unmanned vehicles, mobile sensor networks, and the smart grid. This review covers several of our research endeavors in this direction, as well as progress made by other researchers along the line. We hope that this review promotes additional research efforts in this exciting yet challenging area.</p>", "Keywords": "Reinforcement learning; Multi-agent systems; Networked systems; Consensus optimization; Distributed optimization; Game theory; 强化学习; 多智能体系统; 网络系统; 一致性优化; 分布式优化; 博弈论; TP18", "DOI": "10.1631/FITEE.1900661", "PubYear": 2021, "Volume": "22", "Issue": "6", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> Zhang", "Affiliation": "Coordinated Science Laboratory, University of Illinois at Urbana-Champaign, Urbana-Champaign, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Operations Research and Financial Engineering, Princeton University, Princeton, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Coordinated Science Laboratory, University of Illinois at Urbana-Champaign, Urbana-Champaign, USA"}], "References": []}, {"ArticleId": 89250505, "Title": "Forward link outage performance of aeronautical broadband satellite communications", "Abstract": "<p>High-throughput satellites (HTSs) play an important role in future millimeter-wave (mmWave) aeronautical communication to meet high speed and broad bandwidth requirements. This paper investigates the outage performance of an aeronautical broadband satellite communication system’s forward link, where the feeder link from the gateway to the HTS uses free-space optical (FSO) transmission and the user link from the HTS to aircraft operates at the mmWave band. In the user link, spot beam technology is exploited at the HTS and a massive antenna array is deployed at the aircraft. We first present a location-based beamforming (BF) scheme to maximize the expected output signal-to-noise ratio (SNR) of the forward link with the amplify-and-forward (AF) protocol, which turns out to be a phased array. Then, by supposing that the FSO feeder link follows Gamma-Gamma fading whereas the mmWave user link experiences shadowed Rician fading, we take the influence of the phase error into account, and derive the closed-form expression of the outage probability (OP) for the considered system. To gain further insight, a simple asymptotic OP expression at a high SNR is provided to show the diversity order and coding gain. Finally, numerical simulations are conducted to confirm the validity of the theoretical analysis and reveal the effects of phase errors on the system outage performance.</p>", "Keywords": "Aeronautical broadband satellite network; Free-space optical (FSO) transmission; High throughput mmWave communication; Outage probability; Phase error; 航空宽带卫星网络; 自由空间光传输; 高通量毫米波通信; 中断概率; 相位误差; TN92", "DOI": "10.1631/FITEE.2000445", "PubYear": 2021, "Volume": "22", "Issue": "6", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "Huaicong Kong", "Affiliation": "College of Telecommunications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Telecommunications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Central South University, Changsha, China;Purple Mountain Laboratories, Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Telecommunications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "College of Telecommunications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "College of Telecommunications and Information Engineering, Nanjing University of Posts and Telecommunications, Nanjing, China;Department of Electrical and Computer Engineering, Concordia University, Montreal, Canada"}], "References": []}, {"ArticleId": 89250506, "Title": "Post-quantum blind signcryption scheme from lattice", "Abstract": "<p>Blind signcryption (BSC) can guarantee the blindness and untrackability of signcrypted messages, and moreover, it provides simultaneous unforgeability and confidentiality. Most traditional BSC schemes are based on the number theory. However, with the rapid development of quantum computing, traditional BSC systems are faced with severe security threats. As promising candidate cryptosystems with the ability to resist attacks from quantum computing, lattice-based cryptosystems have attracted increasing attention in academic fields. In this paper, a post-quantum blind signcryption scheme from lattice (PQ-LBSCS) is devised by applying BSC to lattice-based cryptosystems. PQ-LBSCS inherits the advantages of the lattice-based cryptosystem and blind signcryption technique. PQ-LBSCS is provably secure under the hard assumptions of the learning with error problem and small integer solution problem in the standard model. Simulations are carried out using the Matlab tool to analyze the computational efficiency, and the simulation results show that PQ-LBSCS is more efficient than previous schemes. PQ-LBSCS has extensive application prospects in e-commerce, mobile communication, and smart cards.</p>", "Keywords": "Lattice-based cryptosystem; Blind signcryption; Post-quantum computing; Learning with error assumption; Small integer solution assumption; 格密码系统; 盲签密; 抗量子计算; 带错误学习问题; 最短向量问题; TP309", "DOI": "10.1631/FITEE.2000099", "PubYear": 2021, "Volume": "22", "Issue": "6", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON> Yu", "Affiliation": "School of Cyberspace Security, Xi’an University of Posts and Telecommunications, Xi’an, China"}, {"AuthorId": 2, "Name": "Lu <PERSON>", "Affiliation": "School of Cyberspace Security, Xi’an University of Posts and Telecommunications, Xi’an, China"}], "References": []}, {"ArticleId": 89250507, "Title": "Video summarization with a graph convolutional attention network", "Abstract": "<p>Video summarization has established itself as a fundamental technique for generating compact and concise video, which alleviates managing and browsing large-scale video data. Existing methods fail to fully consider the local and global relations among frames of video, leading to a deteriorated summarization performance. To address the above problem, we propose a graph convolutional attention network (GCAN) for video summarization. GCAN consists of two parts, embedding learning and context fusion, where embedding learning includes the temporal branch and graph branch. In particular, GCAN uses dilated temporal convolution to model local cues and temporal self-attention to exploit global cues for video frames. It learns graph embedding via a multi-layer graph convolutional network to reveal the intrinsic structure of frame samples. The context fusion part combines the output streams from the temporal branch and graph branch to create the context-aware representation of frames, on which the importance scores are evaluated for selecting representative frames to generate video summary. Experiments are carried out on two benchmark databases, SumMe and TVSum, showing that the proposed GCAN approach enjoys superior performance compared to several state-of-the-art alternatives in three evaluation settings.</p>", "Keywords": "Temporal learning; Self-attention mechanism; Graph convolutional network; Context fusion; Video summarization; 时序学习; 自注意力机制; 图卷积网络; 上下文融合; 视频摘要; TP391", "DOI": "10.1631/FITEE.2000429", "PubYear": 2021, "Volume": "22", "Issue": "6", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China;The State Key Laboratory for Novel Software Technology, Nanjing University, Nanjing, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}, {"AuthorId": 3, "Name": "Xiang<PERSON> Xu", "Affiliation": "School of Computer Science and Technology, Hangzhou Dianzi University, Hangzhou, China"}], "References": [{"Title": "Survey of Compressed Domain Video Summarization Techniques", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "52", "Issue": "6", "Page": "1", "JournalTitle": "ACM Computing Surveys"}, {"Title": "VOSTR: Video Object Segmentation via Transferable Representations", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "4", "Page": "931", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "A novel convolutional neural network method for crowd counting", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "21", "Issue": "8", "Page": "1150", "JournalTitle": "Frontiers of Information Technology & Electronic Engineering"}, {"Title": "Exploring global diverse attention via pairwise temporal relation for video summarization", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107677", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 89250508, "Title": "Improved dynamic grey wolf optimizer", "Abstract": "<p>In the standard grey wolf optimizer (GWO), the search wolf must wait to update its current position until the comparison between the other search wolves and the three leader wolves is completed. During this waiting period, the standard GWO is seen as the static GWO. To get rid of this waiting period, two dynamic GWO algorithms are proposed: the first dynamic grey wolf optimizer (DGWO1) and the second dynamic grey wolf optimizer (DGWO2). In the dynamic GWO algorithms, the current search wolf does not need to wait for the comparisons between all other search wolves and the leading wolves, and its position can be updated after completing the comparison between itself or the previous search wolf and the leading wolves. The position of the search wolf is promptly updated in the dynamic GWO algorithms, which increases the iterative convergence rate. Based on the structure of the dynamic GWOs, the performance of the other improved GWOs is examined, verifying that for the same improved algorithm, the one based on dynamic GWO has better performance than that based on static GWO in most instances.</p>", "Keywords": "Swarm intelligence; Grey wolf optimizer; Dynamic grey wolf optimizer; Optimization experiment; 群智能; 灰狼优化算法; 动态灰狼优化算法; 优化实验; TP301", "DOI": "10.1631/FITEE.2000191", "PubYear": 2021, "Volume": "22", "Issue": "6", "JournalId": 29457, "JournalTitle": "Frontiers of Information Technology & Electronic Engineering", "ISSN": "2095-9184", "EISSN": "2095-9230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "@qq.com;School of Physics and Electronic Engineering, Xianyang Normal University, Xianyang, China;School of Mechano-Electronic Engineering, Xidian University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Physics and Electronic Engineering, Xianyang Normal University, Xianyang, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> Ming", "Affiliation": "School of Mechano-Electronic Engineering, Xidian University, Xi’an, China"}], "References": [{"Title": "Sine cosine grey wolf optimizer to solve engineering design problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "37", "Issue": "4", "Page": "3123", "JournalTitle": "Engineering with Computers"}, {"Title": "A memory-based Grey Wolf Optimizer for global optimization tasks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "106367", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 89250515, "Title": "Using Tactile Sensing to Improve the Sample Efficiency and Performance of Deep Deterministic Policy Gradients for Simulated In-Hand Manipulation Tasks", "Abstract": "<p>Deep Reinforcement Learning techniques demonstrate advances in the domain of robotics. One of the limiting factors is a large number of interaction samples usually required for training in simulated and real-world environments. In this work, we demonstrate for a set of simulated dexterous in-hand object manipulation tasks that tactile information can substantially increase sample efficiency for training (by up to more than threefold). We also observe an improvement in performance (up to 46%) after adding tactile information. To examine the role of tactile-sensor parameters in these improvements, we included experiments with varied sensor-measurement accuracy (ground truth continuous values, noisy continuous values, Boolean values), and varied spatial resolution of the tactile sensors (927 sensors, 92 sensors, and 16 pooled sensor areas in the hand). To facilitate further studies and comparisons, we make these touch-sensor extensions available as a part of the OpenAI Gym Shadow-Dexterous-Hand robotics environments.</p>", "Keywords": "tactile sensing; Robotics; reinforcement learning (RL); Shadow Dexterous Hand; In-hand manipulation", "DOI": "10.3389/frobt.2021.538773", "PubYear": 2021, "Volume": "8", "Issue": "", "JournalId": 14586, "JournalTitle": "Frontiers in Robotics and AI", "ISSN": "", "EISSN": "2296-9144", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Germany;Spain"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "United States"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Germany"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Germany"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Germany"}], "References": [{"Title": "Learning dexterous in-hand manipulation", "Authors": "OpenAI: <PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "39", "Issue": "1", "Page": "3", "JournalTitle": "The International Journal of Robotics Research"}]}, {"ArticleId": 89250530, "Title": "Virtual Reality-Induced Sensorimotor Conflict Evokes Limb-Specific Sensory Disturbances in Complex Regional Pain Syndrome", "Abstract": "<p>The origin of sensory disturbances in complex regional pain syndrome (CRPS) remains unclear. It has been hypothesized that such disturbances are due to attentional effects and/or sensorimotor integration deficits. If sensory disturbances are explained by sensorimotor integration deficits, they would be expected to be specific in terms of the category of sensation evoked and in terms of localization. Objective 1: To test whether sensory disturbances evoked by a unilateral sensorimotor conflict are specific to the painful limb and differ according to the category of sensory disturbances in individuals with a unilateral CRPS compared to healthy controls (HC). Objective 2: To assess the association between clinical characteristics and conflict-induced sensory disturbances. Objective 3: To assess conflict-induced motor disturbances. Ten adults with upper limb (UL) CRPS and 23 HC were recruited. Sensorimotor conflict was elicited with a KINARM exoskeleton interfaced with a 2D virtual environment allowing the projection of a virtual UL that was moving in either a congruent or incongruent manner relative to the actual UL movement. Participants rated sensory disturbances from 0 (no change) to 3 (high change) on a 8-item questionnaire. Items were classified into two Categories (Category 1: pain, discomfort, the feeling of losing a limb, change in weight and temperature; Category 2: feelings of peculiarity, the impression of gaining a limb and losing control). Motor disturbances were quantified as mediolateral drift and changes in amplitude of UL movement. Clinical characteristics included the intensity and duration of pain, proprioception, and body perception. CRPS participants report higher Category 1 than Category 2 disturbances for the Affected limb (while the reverse was observed for HC and for the Unaffected limb). In addition, no difference was observed between the Unaffected limb in CRPS and the Dominant limb in HC for Category 2 disturbances, while higher conflict sensitivity was observed for Category 1 disturbances. Conflict sensitivity was only related to higher pain for Category 1 disturbances in the Affected limb. Finally, no effect on motor disturbances was observed. While they do not completely rule out the attentional hypothesis, these results support the hypothesis of sensorimotor integration deficits.</p>", "Keywords": "Sensory disturbances; CRPS; Chronic Pain; virtual reality; Robotics; sensorimotor integration", "DOI": "10.3389/frvir.2021.694293", "PubYear": 2021, "Volume": "2", "Issue": "", "JournalId": 73463, "JournalTitle": "Frontiers in Virtual Reality", "ISSN": "", "EISSN": "2673-4192", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Canada"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Canada"}, {"AuthorId": 3, "Name": "Candida S. McCabe", "Affiliation": "United Kingdom"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Canada"}], "References": []}, {"ArticleId": 89250741, "Title": "Estimating Previous Quantization Factors on Multiple JPEG Compressed Images", "Abstract": "The JPEG compression algorithm has proven to be efficient in saving storage and preserving image quality thus becoming extremely popular. On the other hand, the overall process leaves traces into encoded signals which are typically exploited for forensic purposes: for instance, the compression parameters of the acquisition device (or editing software) could be inferred. To this aim, in this paper a novel technique to estimate “previous” JPEG quantization factors on images compressed multiple times, in the aligned case by analyzing statistical traces hidden on Discrete Cosine Transform (DCT) histograms is exploited. Experimental results on double, triple and quadruple compressed images, demonstrate the effectiveness of the proposed technique while unveiling further interesting insights.", "Keywords": "JPEG compression ; Multiple quantization ; Multiple compression ; Quantization estimation ; Image forensics", "DOI": "10.1186/s13635-021-00120-7", "PubYear": 2021, "Volume": "2021", "Issue": "1", "JournalId": 43752, "JournalTitle": "EURASIP Journal on Information Security", "ISSN": "", "EISSN": "2510-523X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "University of Catania, Catania, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "University of Catania, Catania, Italy;iCTLab s.r.l., Spin-off of University of Catania, Catania, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "University of Catania, Catania, Italy;iCTLab s.r.l., Spin-off of University of Catania, Catania, Italy"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "University of Cagliari, Cagliari, Italy"}], "References": []}]