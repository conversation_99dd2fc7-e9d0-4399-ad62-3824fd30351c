[{"ArticleId": 94770705, "Title": "Towards building a pragmatic cross-project defect prediction model combining non-effort based and effort-based performance measures for a balanced evaluation", "Abstract": "<b  >Context</b> Recent years have witnessed the growing trend in cross-project defect prediction (CPDP), where the training and the testing data come from different projects having different data distributions. Several CPDP methods have been presented in the literature to overcome differences in their distributions, but the majority of the existing approaches have been evaluated considering the availability of unlimited inspection effort, which is practically impossible, thus leading to fallacious conclusions. Further, they focused more on improving Recall over Precision leading to a high probability of false alarm (PF), causing significant wastage of developer&#x27;s efforts and time. <b  >Objective</b> Addressing these issues, we propose a Two-Phase Transfer Boosting (TPTB) model, which aims at improving the performance not only in terms of non-effort based measures (NEBMs) (making a balance between Recall and PF) but also in terms of effort based measures (EBMs), considering the availability of limited inspection effort. <b  >Method</b> To mitigate the distribution differences, the first phase assigns initial weights to the training modules based on the feature distribution and feature importance. The second phase applies the Dynamic Transfer AdaBoost algorithm to build an ensemble classifier to lessen the impact of contradictory training modules. In addition, a sorting strategy is designed to prioritize the modules for further inspection. <b  >Results</b> Statistical results on 62 datasets revealed a better-balanced performance of our TPTB model holistically over NN-filter, ManualDown, EASC, and Cruz model with performance comparable to WPDP (Within-project defect prediction) considering NEBMs. Besides, when considering EBMs together, TPTB showed statistically and practically more balanced performance as compared to ManualUP and Cruz with overall performance comparable to EASC. <b  >Conclusions</b> Our results demonstrate the efficacy of the TPTB model in a practical setting empowering the quality assurance team to predict and prioritize the defective modules allocating limited inspection effort by optimally focusing on highly defective modules.", "Keywords": "", "DOI": "10.1016/j.infsof.2022.106980", "PubYear": 2022, "Volume": "150", "Issue": "", "JournalId": 4981, "JournalTitle": "Information and Software Technology", "ISSN": "0950-5849", "EISSN": "1873-6025", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jaypee Institute of Information Technology, Department of Computer Science Engineering and Information Technology, Noida, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jaypee Institute of Information Technology, Department of Computer Science Engineering and Information Technology, Noida, India"}], "References": []}, {"ArticleId": 94770721, "Title": "Early Identification of Abused Domains in TLD through Passive DNS Applying Machine Learning Techniques", "Abstract": "<p>DNS is vital for the proper functioning of the Internet. However, users use this structure for domain registration and abuse. These domains are used as tools for these users to carry out the most varied attacks. Thus, early detection of abused domains prevents more people from falling into scams. In this work, an approach for identifying abused domains was developed using passive DNS collected from an authoritative DNS server TLD along with the data enriched through geolocation, thus enabling a global view of the domains. Therefore, the system monitors the domain’s first seven days of life after its first DNS query, in which two behavior checks are performed, the first with three days and the second with seven days. The generated models apply the machine learning algorithm LightGBM, and because of the unbalanced data, the combination of Cluster Centroids and K-Means SMOTE techniques were used. As a result, it obtained an average AUC of 0.9673 for the three-day model and an average AUC of 0.9674 for the seven-day model. Finally, the validation of three and seven days in a test environment reached a TPR of 0.8656 and 0.8682, respectively. It was noted that the system has a satisfactory performance for the early identification of abused domains and the importance of a TLD to identify these domains.</p>", "Keywords": "cybersecurity;passive DNS;abused domains in TLD;data imbalanced;machine learning algorithms", "DOI": "10.17762/ijcnis.v14i1.5256", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 94344, "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)", "ISSN": "2076-0930", "EISSN": "2073-607X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Sao Paulo State University (UNESP)"}], "References": []}, {"ArticleId": 94770729, "Title": "Bike sharing usage prediction with deep learning: a survey", "Abstract": "<p>As a representative of shared mobility, bike sharing has become a green and convenient way to travel in cities in recent years. Bike usage prediction becomes more important for supporting efficient operation and management in bike share systems as the basis of inventory management and bike rebalancing. The essential of usage prediction in bike sharing systems is to model the spatial interactions of nearby stations, the temporal dependence of demands, and the impacts of environmental and societal factors. Deep learning has shown a great advantage of making a precise prediction for bike sharing usage. Recurrent neural networks capture the temporal dependence with the memory cell and gate mechanisms. Convolutional neural networks and graph neural networks learn spatial interactions of nearby stations with local convolutional operations defined for the grid-format and graph-format inputs respectively. In this survey, the latest studies about bike sharing usage prediction with deep learning are reviewed, with a classification for the prediction problems and models. Different applications based on bike usage prediction are discussed, both within and beyond bike share systems. Some research directions are pointed out to encourage future research. To the best of our knowledge, this paper is the first comprehensive survey that focuses on bike sharing usage prediction with deep learning techniques.</p><p>© The Author(s), under exclusive licence to Springer-Verlag London Ltd., part of Springer Nature 2022.</p>", "Keywords": "Bike sharing;Bike usage prediction;Deep learning;Neural networks", "DOI": "10.1007/s00521-022-07380-5", "PubYear": 2022, "Volume": "34", "Issue": "18", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information and Communication Engineering, Beijing University of Posts and Telecommunications, Beijing, 100876 China."}], "References": [{"Title": "Origin and destination forecasting on dockless shared bicycle in a hybrid deep-learning algorithms", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "7-8", "Page": "5269", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A spatiotemporal attention mechanism-based model for multi-step citywide passenger demand prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "372", "JournalTitle": "Information Sciences"}, {"Title": "Demand prediction for a public bike sharing program based on spatio-temporal graph convolutional networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "15", "Page": "22907", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "A spatiotemporal hierarchical attention mechanism-based model for multi-step station-level crowd flow prediction", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "544", "Issue": "", "Page": "308", "JournalTitle": "Information Sciences"}, {"Title": "Deep learning based origin-destination prediction via contextual information fusion", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "81", "Issue": "9", "Page": "12029", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "An efficiency-enhanced deep learning model for citywide crowd flows prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "12", "Issue": "7", "Page": "1879", "JournalTitle": "International Journal of Machine Learning and Cybernetics"}, {"Title": "A transfer approach with attention reptile method and long-term generation mechanism for few-shot traffic prediction", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "452", "Issue": "", "Page": "15", "JournalTitle": "Neurocomputing"}, {"Title": "Applications of deep learning in stock market prediction: Recent progress", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "184", "Issue": "", "Page": "115537", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Dynamic graph convolutional network for long-term traffic flow prediction with reinforcement learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "401", "JournalTitle": "Information Sciences"}, {"Title": "Graph neural network for traffic forecasting: A survey", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "207", "Issue": "", "Page": "117921", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94770733, "Title": "Improved Residual Dense Network for Large Scale Super-Resolution via Generative Adversarial Network", "Abstract": "<p>Recent single image super resolution (SISR) studies were conducted extensively on small upscaling factors such as x2 and x4 on remote sensing images, while less work was conducted on large factors such as the factor x8 and x16. Owing to the high performance of the generative adversarial networks (GANs), in this paper, two GAN’s frameworks are implemented to study the SISR on the residual remote sensing image with large magnification under x8 scale factor, which is still lacking acceptable results. This work proposes a modified version of the residual dense network (RDN) and then it been implemented within GAN framework which named RDGAN. The second GAN framework has been built based on the densely sampled super resolution network (DSSR) and we named DSGAN. The used loss function for the training employs the adversarial, mean squared error (MSE) and the perceptual loss derived from the VGG19 model. We optimize the training by using Adam for number of epochs then switching to the SGD optimizer. We validate the frameworks on the proposed dataset of this work and other three remote sensing datasets: the UC Merced, WHU-RS19 and RSSCN7. To validate the frameworks, we use the following image quality assessment metrics: the PSNR and the SSIM on the RGB and the Y channel and the MSE. The RDGAN evaluation values on the proposed dataset were 26.02, 0.704, and 257.70 for PSNR, SSIM and the MSE, respectively, and the DSGAN evaluation on the same dataset yielded 26.13, 0.708 and 251.89 for the PSNR, the SSIM, and the MSE.</p>", "Keywords": "single image super-resolution;remote sensing;generative adversarial network;residual dense network;residual dense generative adversarial network", "DOI": "10.17762/ijcnis.v14i1.5221", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 94344, "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)", "ISSN": "2076-0930", "EISSN": "2073-607X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Jordan University of Science and Technology"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Jordan University of Science and Technology"}], "References": []}, {"ArticleId": 94770735, "Title": "TRW-MAC: A thermal-aware receiver-driven wake-up radio enabled duty cycle MAC protocol for multi-hop implantable wireless body area networks in Internet of Things", "Abstract": "<p>Implantable Wireless Body Area Network (IWBAN), a network of implantable medical sensors, is one of the emerging network paradigms due to the rapid proliferation of wireless technologies and growing demand of sophisticated healthcare. The wireless sensors in IWBAN is capable of communicating with each other through radio frequency (RF) link. However, recurring wireless communication inside the human body induces heat causing severe thermal damage to the human tissue which, if not controlled, may appear as a threat to human life. Moreover, higher propagation loss inside the human body as well as low-power requirement of the sensor nodes necessitate multi-hop communication for IWBAN. A IWBAN also requires meeting certain Quality of Service demands in terms of energy, delay, reliability etc. These pressing concerns engender the design of TRW-MAC: A thermal-aware receiver-driven wake-up radio enabled duty cycle MAC protocol for multi-hop IWBANs in Internet of Things. TRW-MAC introduces a thermal-aware duty cycle adjustment mechanism to reduce temperature inside the body and adopts wake-up radio (WuR) scheme for attaining higher energy efficiency. The protocol devises a wake-up estimation scheme to facilitate staggered wake-up schedule for multi-hop transmission. A superframe structure is introduced that utilizes both contention-based and contention free medium access operations. The performance of TRW-MAC is evaluated through simulations that exhibit its superior performance in attaining lower thermal-rise as well as satisfying other QoS metrics in terms of energy-efficiency, delay and reliability.</p>", "Keywords": "wireless body area networks;wake-up radio enabled MAC;duty cycle;multi-hop", "DOI": "10.17762/ijcnis.v14i1.5182", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 94344, "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)", "ISSN": "2076-0930", "EISSN": "2073-607X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Information Technology Faculty of Computing and Information Technology King Abdul<PERSON><PERSON><PERSON> University Jeddah-21589 Kingdom of Saudi Arabia"}], "References": []}, {"ArticleId": ********, "Title": "Performance analysis of black hole and worm hole attacks in MANETs", "Abstract": "<p>A Mobile Ad Hoc Network MANET is composed of a freely and mobility set of mobile nodes. They form a temporary dynamic wireless network without any infrastructure. Since the nodes act as both host and router in their communication, they act as a router provide connectivity by forwarding data packets among intermediate nodes to the destination. The routing protocol is used to grove their communication and connectivity as example, the Ad On-demand distance vector (AODV) routing protocol. However, due to the lack of security vulnerabilities of routing protocols and the absence of infrastructure, MANET is vulnerable to various security threats and attacks. This paper examines the impact of two types of attacks on AODV routing protocol using Network Simulator version 2 (NS2) environment. These attacks are Blackhole and Wormhole Attacks. The aim of both of them is to prevent data packets to reach the destination node and dropping all the traffic. </p>", "Keywords": "", "DOI": "10.17762/ijcnis.v14i1.5078", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 94344, "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)", "ISSN": "2076-0930", "EISSN": "2073-607X", "Authors": [{"AuthorId": 1, "Name": "Mazoon Hashil <PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "Hothefa Shaker <PERSON><PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "Baraa <PERSON>", "Affiliation": "Information Technology Department, College of Information Technology, Ahlia University. Manama, Bahrain"}], "References": []}, {"ArticleId": ********, "Title": "A Like ELGAMAL Cryptosystem But Resistant To Post-Quantum Attacks", "Abstract": "<p>The Modulo 1 Factoring Problem (M1FP) is an elegant mathematical problem which could be exploited to design safe cryptographic protocols and encryption schemes that resist to post quantum attacks. The ELGAMAL encryption scheme is a well-known and efficient public key algorithm designed by <PERSON><PERSON> ELGAMAL from discrete logarithm problem. It is always highly used in Internet security and many other applications after a large number of years. However, the imminent arrival of quantum computing threatens the security of ELGAMAL cryptosystem and impose to cryptologists to prepare a resilient algorithm to quantum computer-based attacks. In this paper we will present a like-ELGAMAL cryptosystem based on the M1FP NP-hard problem. This encryption scheme is very simple but efficient and supposed to be resistant to post quantum attacks.</p>", "Keywords": "ELGAMAL cryptosystem;public key cryptography;NP-hard;M1FP;post quantum attacks;one-way function", "DOI": "10.17762/ijcnis.v14i1.5180", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 94344, "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)", "ISSN": "2076-0930", "EISSN": "2073-607X", "Authors": [{"AuthorId": 1, "Name": "Ahmed EL-YAHYAOUI", "Affiliation": ""}, {"AuthorId": 2, "Name": "Fouzia OMARY", "Affiliation": ""}], "References": []}, {"ArticleId": 94770868, "Title": "Scheduling on-demand charging request in wireless rechargeable sensor network with fruit fly optimization-based path selection", "Abstract": "<p>Wireless Rechargeable Sensor Network (WRSN) is a promising solution to prolong the lifetime of Wireless Sensor Networks (WSNs). In WRSN, Mobile Charging Vehicle (MCV) is scheduled to wirelessly charge the Rechargeable Sensor Nodes (RSNs) upon receiving the charging request. As WRSN facilitates WSNs which are deployed in unattended and harsh environments, developing an efficient charging approach is a challenging task. To address this issue, this paper proposes a novel approach called Scheduling On-demand Charging Request with FFO based optimal path (SOCR-FFO) selection. It aims to jointly optimize the path selection for disseminating charging requests and scheduling MCV based on the current network status. To achieve these objectives, SOCR-FFO contributes two algorithms namely Optimal Path Selection with Fruit Fly Optimization (OPS-FFO) and Threshold based Route Selection Algorithm (TRSA). The former algorithm formulates an optimization solution by enhancing the Fruit Fly Optimization (FFO) algorithm to determine the best path for forwarding the charging requests. The proposed TRSA grants threshold-based route planning for MCV by minimizing the Stop Points (SPs). The performance of the proposed SOCR-FFO is revised with existing algorithms in terms of various parameters like charging throughput, success rate, response time, packet delivery ratio, and delay.</p>", "Keywords": "Wireless sensor network; Wireless rechargeable sensor network; Charging scheduling; Mobile charging vehicle; Warning thresholds; Fruit fly optimization", "DOI": "10.1007/s41870-022-00958-1", "PubYear": 2022, "Volume": "14", "Issue": "5", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of CSE, National Institute of Technology, Tiruchirappalli, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of CSE, National Institute of Technology, Tiruchirappalli, India"}], "References": []}, {"ArticleId": 94771042, "Title": "Understanding service connectivity based on digital serendipity: An actor-network approach", "Abstract": "The term smart city is an emerging urban concept that provides optimal public services in a city as a hybrid combination of people, technology, and place. However, previous research does not consider various factors and either depends on technology development or studies of human and non-human factors separately. In addition, even if the relationship between human and non-human elements is analyzed, it is difficult to realize the analyzed content as a service for real users. The actor-network theory (ANT), established by <PERSON><PERSON>, is a theory that provides a deep understanding of the underlying mechanisms of smart cities by illustrating how the networks of human and non-human actors create the weave of unpredictable connections. We call this digital serendipity when it is realized as a service from this network. This study aims to analyze the actor-network, where digital serendipity can occur, and apply it to increase the possibility of service formation. To this end, we collect the location data of users on a campus as a living lab and analyze the network between users and buildings. Further, by applying the results of the analyzed network, we develop a web-based application as an example where digital serendipity can be implemented for voluntary service formation. To verify the service connectivity, we conducted a test utilizing library book rental data.", "Keywords": "Actor-network theory ; Actor-network analysis ; Digital serendipity ; Service connectivity", "DOI": "10.1016/j.aei.2022.101647", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Graduate School of Culture Technology, KAIST, Daejeon, The Republic of Korea"}, {"AuthorId": 2, "Name": "Ju <PERSON> Park", "Affiliation": "Department of Convergence IT Engineering, POSTECH, Pohang, The Republic of Korea;Corresponding author"}], "References": [{"Title": "IoT-enabled smart appliances under industry 4.0: A case study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "43", "Issue": "", "Page": "101043", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Research on economic benefits of multi-city logistics development based on data-driven analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101322", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 94771087, "Title": "Facial expression recognition of online learners from real-time videos using a novel deep learning model", "Abstract": "<p>In every learning setting, in classrooms or online, a student's emotions throughout course involvement play a critical role. It employs disturbing, excite, and eye and head movement patterns to infer important information about a student's mood in an e-learning environment. Researchers from numerous disciplines have been focusing on emotion detection technologies to better understand user engagement, efficacy, and utility of systems that have been established or are being deployed. The goal of this study is to see if students' facial expressions can be used by lecturers to understand students' comprehension levels in a virtual classroom, as well as to determine the influence of facial expressions during lectures and the degree of comprehension displayed by these emotions. The objective is to determine which facial physical behaviours are associated with emotional states and then to determine how these emotional states are related to student understanding. The major purpose of this work is to plan and develop a new deep learning-oriented facial expression recognition (FER) of online learners from real-time videos. For the frames of online learners from real-time videos, the Viola–Jones Face detection algorithm is employed for face detection. Further, the pattern extraction is performed by the optimized local directional Texture Pattern (LDTP) using the hybrid Coyote Optimization Algorithm (COA), and Deer Hunting Optimization Algorithm (DHOA) referred as Coyote–Deer Hunting Optimization (C-DHO). These pattern images are inputted to the convolutional neural network (CNN) for deep feature extraction. Furthermore, the heuristically modified recurrent neural network (HM-RNN) using the same C-DHO is used for the expression recognition. The experimental research reveals that the suggested method aids in the identification of emotions as well as the classification of student participation and interest in the topic, all of which are displayed as feedback to the teacher in terms of improving the learner experience.</p>", "Keywords": "Facial expression recognition; Online learners; Real-time videos; Optimized local directional texture pattern; Heuristically modified recurrent neural network; Coyote; Deer hunting optimization", "DOI": "10.1007/s00530-022-00957-z", "PubYear": 2022, "Volume": "28", "Issue": "6", "JournalId": 9207, "JournalTitle": "Multimedia Systems", "ISSN": "0942-4962", "EISSN": "1432-1882", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, S.R.M Institute of Science and Technology, Kattankulathur, Chennai, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computing Technologies, S.R.M Institute of Science and Technology, Kattankulathur, Chennai, India"}], "References": [{"Title": "Multi angle optimal pattern-based deep learning for automatic facial expression recognition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "139", "Issue": "", "Page": "157", "JournalTitle": "Pattern Recognition Letters"}, {"Title": "Tunicate Swarm Algorithm: A new bio-inspired based metaheuristic paradigm for global optimization", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103541", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "Mutual information regularized identity-aware facial expression recognition in compressed video", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "108105", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 94771147, "Title": "Data-based modeling and identification for general nonlinear dynamical systems by the multidimensional Taylor network", "Abstract": "Purpose The present study is intended to develop an effective approach to the real-time modeling of general dynamic nonlinear systems based on the multidimensional Taylor network (MTN). Design/methodology/approach The authors present a detailed explanation for modeling the general discrete nonlinear dynamic system by the MTN. The weight coefficients of the network can be obtained by sampling data learning. Specifically, the least square (LS) method is adopted herein due to its desirable real-time performance and robustness. Findings Compared with the existing mainstream nonlinear time series analysis methods, the least square method-based multidimensional Taylor network (LSMTN) features its more desirable prediction accuracy and real-time performance. Model metric results confirm the satisfaction of modeling and identification for the generalized nonlinear system. In addition, the MTN is of simpler structure and lower computational complexity than neural networks. Research limitations/implications Once models of general nonlinear dynamical systems are formulated based on MTNs and their weight coefficients are identified using the data from the systems of ecosystems, society, organizations, businesses or human behavior, the forecasting, optimizing and controlling of the systems can be further studied by means of the MTN analytical models. Practical implications MTNs can be used as controllers, identifiers, filters, predictors, compensators and equation solvers (solving nonlinear differential equations or approximating nonlinear functions) of the systems of ecosystems, society, organizations, businesses or human behavior. Social implications The operating efficiency and benefits of social systems can be prominently enhanced, and their operating costs can be significantly reduced. Originality/value Nonlinear systems are typically impacted by a variety of factors, which makes it a challenge to build correct mathematical models for various tasks. As a result, existing modeling approaches necessitate a large number of limitations as preconditions, severely limiting their applicability. The proposed MTN methodology is believed to contribute much to the data-based modeling and identification of the general nonlinear dynamical system with no need for its prior knowledge.", "Keywords": "Time series prediction;Least square method;Multidimensional Taylor network;Nonlinear system;Identification", "DOI": "10.1108/K-09-2021-0882", "PubYear": 2023, "Volume": "52", "Issue": "10", "JournalId": 802, "JournalTitle": "Kybernetes", "ISSN": "0368-492X", "EISSN": "1758-7883", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation , Southeast University , Nanjing, China MOE Key Laboratory of Measurement and Control of Complex Systems of Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 2, "Name": "Zhong-Tian Bi", "Affiliation": "School of Automation , Southeast University , Nanjing, China MOE Key Laboratory of Measurement and Control of Complex Systems of Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Automation , Southeast University , Nanjing, China MOE Key Laboratory of Measurement and Control of Complex Systems of Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation , Southeast University , Nanjing, China MOE Key Laboratory of Measurement and Control of Complex Systems of Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation , Southeast University , Nanjing, China MOE Key Laboratory of Measurement and Control of Complex Systems of Engineering, Southeast University , Nanjing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Automation , Southeast University , Nanjing, China MOE Key Laboratory of Measurement and Control of Complex Systems of Engineering, Southeast University , Nanjing, China"}], "References": [{"Title": "Cash flow prediction: MLP and LSTM compared to ARIMA and Prophet", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "2", "Page": "371", "JournalTitle": "Electronic Commerce Research"}, {"Title": "Class-imbalanced dynamic financial distress prediction based on Adaboost-SVM ensemble combined with SMOTE and time weighting", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "128", "JournalTitle": "Information Fusion"}, {"Title": "Developing a deep learning framework with two-stage feature selection for multivariate financial time series forecasting", "Authors": "Tong Niu; Jianzhou Wang; Haiyan Lu", "PubYear": 2020, "Volume": "148", "Issue": "", "Page": "113237", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94771159, "Title": "Online structural clustering based on DBSCAN extension with granular descriptors", "Abstract": "In online structural clustering, general density-based clustering algorithms have problems of low scalability and high computation cost, especially in big data analysis, this paper proposed a DBSCAN extension algorithm with consideration of granule computing to handle these problems. This algorithm mainly makes use of advantages of DBSCAN and granular descriptors to realize effective and efficient structural online clustering. Frist, to extract structural clusters effectively, DBSCAN is considered as the basic clustering algorithm in this research. Second, since DBSCAN’s results are not numerical for online testing, this paper proposes to apply granule computing (GrC) to construct information granules describing arbitrarily-shaped clusters from DBSCAN. Third, to realize an efficient online structural clustering, especially in big data analysis, a series of granular fuzzy models are built with consideration of structural information, then a rule-based model is formed for guiding online clustering of new testing data. Through the proposed method, the online clustering ability of DBSCAN is developed with reduced computation cost, meanwhile the structural clustering ability is also retained in online testing. Experiments on synthetic data, publicly available data and real-world data are discussed, online testing accuracy and computation time are evaluated to validate the feasibility and effectiveness of the proposed method.", "Keywords": "", "DOI": "10.1016/j.ins.2022.06.027", "PubYear": 2022, "Volume": "607", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Artificial Intelligence Research Center (AIRC), National Institute of Advanced Industrial Science and Technology (AIST), Tokyo, Japan;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Systems and Control Engineering, Tokyo Institute of Technology, Tokyo, Japan"}], "References": [{"Title": "Clustering method for production of Z-number based if-then rules", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "520", "Issue": "", "Page": "155", "JournalTitle": "Information Sciences"}, {"Title": "FCM-RDpA: TSK fuzzy regression model construction using fuzzy C-means clustering, regularization, Droprule, and Powerball Adabelief", "Authors": "<PERSON><PERSON><PERSON> Shi; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "574", "Issue": "", "Page": "490", "JournalTitle": "Information Sciences"}, {"Title": "<PERSON><PERSON> supervised rough granular description model with the principle of justifiable granularity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "110", "Issue": "", "Page": "107612", "JournalTitle": "Applied Soft Computing"}]}, {"ArticleId": 94771162, "Title": "Tick Box Design: A bounded and packageable co-design method for large workshops", "Abstract": "We present Tick Box Design, a rapid co-design method for research and industry that allows users to gather many design ideas from large numbers of participants in a limited time whilst adhering to ethical principles around users understanding their contributions. The method is based on a design workshop model and can be packaged for delivery by remote teams making it well suited for distributed PD work. In this paper we describe an instance of the method in which 198 teenagers in one country, remotely contributed design ideas for a team in another country, across four rapid 60-minute workshops. In a systematic evaluation of the workshop, we take the needs of both sides into account, the teen participants, and the design team. We explore the participants’ ability to contribute ideas and the usefulness of these ideas to the design team. We show that the teenagers successfully participated in the activities and that the process delivered ideas that were useful to the design team. We discuss our evaluation in the context of ethical and useful participation of minors in HCI research and conclude that Tick Box Design is an efficient method that can be packaged for remote use and delivers value for designers and participants.", "Keywords": "Design workshops ; User centred design ; Teenagers ; Co-design ; Value ; Participatory design ; Design methods", "DOI": "10.1016/j.ijcci.2022.100505", "PubYear": 2022, "Volume": "33", "Issue": "", "JournalId": 7619, "JournalTitle": "International Journal of Child-Computer Interaction", "ISSN": "2212-8689", "EISSN": "2212-8697", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ChiCI Lab, University of Central Lancashire, Preston, Lancs, United Kingdom;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Reykjavik University, Reykjavik, Iceland"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "School of Computer Science, Reykjavik University, Reykjavik, Iceland"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Child Computer Interaction Group, University of Central Lancashire, Preston, Lancs, United Kingdom"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "CHiCI Lab, University of Central Lancashire, Preston, United Kingdom"}], "References": [{"Title": "Distributing participation in design: Addressing challenges of a global pandemic", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "28", "Issue": "", "Page": "100255", "JournalTitle": "International Journal of Child-Computer Interaction"}]}, {"ArticleId": ********, "Title": "Coordinated inauthentic behavior and information spreading on Twitter", "Abstract": "We explore the effects of coordinated users (i.e., users characterized by an unexpected, suspicious, or exceptional similarity) in information spreading on Twitter by quantifying the efficacy of their tactics in deceiving feed algorithms to maximize information outreach. In particular, we investigate the behavior of coordinated accounts within a large set of retweet-based information cascades identifying key differences between coordinated and non-coordinated accounts in terms of position within the cascade, action delay and outreach. On average, coordinated accounts occupy higher positions of the information cascade (i.e., closer to the root), spread messages faster and involve a slightly higher number of users. When considering cascade metrics such as size, number of edges and height, we observe clear differences among information cascades that are associated to a systematically larger proportion of coordinated accounts, as confirmed by comparisons with statistical null models. To further characterize the activity of coordinated accounts we introduce two new measures capturing their infectivity within the information cascade (i.e., their ability to involve other users) and their interaction with non-coordinated accounts. Finally, we find that the interaction pattern between the two classes of users follows a saturation-like process. A larger-scale targeting of non-coordinated users does not require a larger amount of coordinated accounts after a threshold value ~50%, after which involving more coordinated accounts within a cascade yields a null marginal effect. Our results contribute to shed light on the role of coordinated accounts and their effect on information diffusion.", "Keywords": "Coordinated inauthentic behavior ; Information spreading ; Disinformation ; Twitter", "DOI": "10.1016/j.dss.2022.113819", "PubYear": 2022, "Volume": "160", "Issue": "", "JournalId": 3351, "JournalTitle": "Decision Support Systems", "ISSN": "0167-9236", "EISSN": "1873-5797", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science, Sapienza University of Rome, Italy"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Institute of Informatics and Telematics, National Research Council (IIT-CNR), Italy;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Dept. of Computer Science, Sapienza University of Rome, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Institute of Informatics and Telematics, National Research Council (IIT-CNR), Italy"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Informatics and Telematics, National Research Council (IIT-CNR), Italy"}], "References": [{"Title": "Antisocial online behavior detection using deep learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "138", "Issue": "", "Page": "113362", "JournalTitle": "Decision Support Systems"}, {"Title": "Bots in Social and Interaction Networks", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "39", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Can bots help create knowledge? The effects of bot intervention in open collaboration", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> (<PERSON>) <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "148", "Issue": "", "Page": "113601", "JournalTitle": "Decision Support Systems"}, {"Title": "Amplifying influence through coordinated behaviour in social networks", "Authors": "<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "11", "Issue": "1", "Page": "111", "JournalTitle": "Social Network Analysis and Mining"}]}, {"ArticleId": 94771234, "Title": "Automatic Building of a Powerful IDS for The Cloud Based on Deep Neural Network by Using a Novel Combination of Simulated Annealing Algorithm and Improved Self- Adaptive Genetic Algorithm", "Abstract": "<p>Cloud computing (CC) is the fastest-growing data hosting and computational technology that stands today as a satisfactory answer to the problem of data storage and computing. Thereby, most organizations are now migratingtheir services into the cloud due to its appealing features and its tangible advantages. Nevertheless, providing privacy and security to protect cloud assets and resources still a very challenging issue. To address the aboveissues, we propose a smart approach to construct automatically an efficient and effective anomaly network IDS based on Deep Neural Network, by using a novel hybrid optimization framework “ISAGASAA”. ISAGASAA framework combines our new self-adaptive heuristic search algorithm called “Improved Self-Adaptive Genetic Algorithm” (ISAGA) and Simulated Annealing Algorithm (SAA). Our approach consists of using ISAGASAA with the aim of seeking the optimal or near optimal combination of most pertinent values of the parametersincluded in building of DNN based IDS or impacting its performance, which guarantee high detection rate, high accuracy and low false alarm rate. The experimental results turn out the capability of our IDS to uncover intrusionswith high detection accuracy and low false alarm rate, and demonstrate its superiority in comparison with stateof-the-art methods.</p>", "Keywords": "Cloud computing;Network intrusion detection system;Deep neural network;Genetic algorithm;Self-adaptive heuristic search algorithm;Simulated annealing algorithm.", "DOI": "10.17762/ijcnis.v14i1.5264", "PubYear": 2022, "Volume": "14", "Issue": "1", "JournalId": 94344, "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)", "ISSN": "2076-0930", "EISSN": "2073-607X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Sciences, Hassan II University of Casablanca, Casablanca, Morocco"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Sciences, Hassan II University of Casablanca, Casablanca, Morocco"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Sciences, Hassan II University of Casablanca, Casablanca, Morocco"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Sciences, Hassan II University of Casablanca, Casablanca, Morocco"}], "References": []}, {"ArticleId": ********, "Title": "COMBI: Artificial Intelligence for Computer-Based Forensic Analysis of Persons", "Abstract": "During the prosecution process the primary objective is to prove criminal offences to the correct perpetrator to convict them with legal effect. However, in reality this may often be difficult to achieve. Suppose a suspect has been identified and is accused of a bank robbery. Due to the location of the crime, it can be assumed that there is sufficient image and video surveillance footage available, having recorded the perpetrator at the crime scene. Depending on the surveillance system used, there could be even high-resolution material available. In short, optimal conditions seem to be in place for further investigations, especially as far as the identification of the perpetrator and the collection of evidence of their participation in the crime are concerned. However, perpetrators usually act using some kind of concealment to hide their identity. In most cases, they disguise their faces and even their gait. Conventional investigation approaches and methods such as facial recognition and gait analysis then quickly reach their limits. For this reason, an approach based on anthropometric person-specific digital skeletons, so-called rigs, that is being researched by the COMBI research project is presented in this publication. Using these rigs, it should be possible to assign known identities, comparable to suspects, to unknown identities, comparable to perpetrators. The aim of the COMBI research project is to study the anthropometric pattern as a biometric identifier as well as to make it feasible for the standardised application in the taking of evidence by the police and prosecution. The approach is intended to present computer-aided opportunities for the identification of perpetrators that can support already established procedures.", "Keywords": "Artificial intelligence; OpenPose; Forensic analyses", "DOI": "10.1007/s13218-022-00761-x", "PubYear": 2022, "Volume": "36", "Issue": "2", "JournalId": 4294, "JournalTitle": "KI - Künstliche Intelligenz", "ISSN": "0933-1875", "EISSN": "1610-1987", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Faculty of Applied Computer Sciences and Biosciences, University of Applied Sciences Mittweida, Mittweida, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Faculty of Applied Computer Sciences and Biosciences, University of Applied Sciences Mittweida, Mittweida, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Applied Computer Sciences and Biosciences, University of Applied Sciences Mittweida, Mittweida, Germany"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Applied Computer Sciences and Biosciences, University of Applied Sciences Mittweida, Mittweida, Germany"}], "References": [{"Title": "EfficientPose: Scalable single-person pose estimation", "Authors": "<PERSON>; <PERSON><PERSON>; Espen AF Ihlen", "PubYear": 2021, "Volume": "51", "Issue": "4", "Page": "2518", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 94771353, "Title": "Application Study of Process Control System in Cold Rolling Galvanizing Line", "Abstract": "", "Keywords": "", "DOI": "10.12677/CSA.2022.126149", "PubYear": 2022, "Volume": "12", "Issue": "6", "JournalId": 22271, "JournalTitle": "Computer Science and Application", "ISSN": "2161-8801", "EISSN": "2161-881X", "Authors": [{"AuthorId": 1, "Name": "智燕 王", "Affiliation": ""}], "References": []}, {"ArticleId": 94771376, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0020-0255(22)00572-2", "PubYear": 2022, "Volume": "605", "Issue": "", "JournalId": 1773, "JournalTitle": "Information Sciences", "ISSN": "0020-0255", "EISSN": "1872-6291", "Authors": [], "References": []}, {"ArticleId": 94771407, "Title": "A low-cost and simple-fabricated epidermal sweat patch based on “cut-and-paste” manufacture", "Abstract": "Continuous, non-invasive, and multi-parametric sensing of sweat is noted to be appealing to many applications such as personalized medicine, athletic performance, and military readiness. Microfluidic sensing patches are widely reported to perform sweat collection, transportation, and multi-parameters analysis; however, time-consuming and multi-step manufacturing process actually prevents the large-scale application. In this study, a “cut-and-paste (CAP)” method is devised to pattern and transfer polydimethylsiloxane (PDMS) and polyethylene terephthalate (PET) film to fabricate the microfluidic patch. The “bear like” patch, composed of four polymer-based layer, are completely carved automatically within 2 min by the desktop cutting machine. The sweat glucose and lactate are monitored in real time with newly proposed wearable sweat patches. The concentration ratio between blood glucose and iontophoresis-induced sweat glucose was over 512.37 as calculated. And the latter lagged behind the former over 70 min about peak of concentration. Also, using the fabricated sensors, we have analyzed the levels of glucose and lactate and compared them in both exercise- and iontophoresis-induced sweat. These results indicate the CAP method has great potential for the microfluidic integrated wearable platforms with low-cost, versatility, and mechanically flexibility, which would promote the development of wearable sweat microfluidic devices and application in individualized health monitoring.", "Keywords": "\"Cut-and-Paste (CAP)” manufacture ; Electrochemical analysis ; Microfluidic chip ; Sweat sensors ; Iontophoresis", "DOI": "10.1016/j.snb.2022.132184", "PubYear": 2022, "Volume": "368", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON>g <PERSON>", "Affiliation": "State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China;Shanghaitech University, Shanghai 201210, China;Center of Materials Science and Optoelectronics Engineering, University of Chines Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China;Center of Materials Science and Optoelectronics Engineering, University of Chines Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China;Center of Materials Science and Optoelectronics Engineering, University of Chines Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China;Center of Materials Science and Optoelectronics Engineering, University of Chines Academy of Sciences, Beijing 100049, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Institute of Molecular Medicine (IMM), Renji Hospital, School of Medicine, Shanghai Jiao Tong University, Shanghai 200127, China"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China;Shanghaitech University, Shanghai 201210, China;Center of Materials Science and Optoelectronics Engineering, University of Chines Academy of Sciences, Beijing 100049, China;Corresponding authors at: State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China"}, {"AuthorId": 8, "Name": "Hongju Mao", "Affiliation": "State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China;Center of Materials Science and Optoelectronics Engineering, University of Chines Academy of Sciences, Beijing 100049, China;Corresponding authors at: State Key Laboratory of Transducer Technology, Shanghai Institute of Microsystem and Information Technology, Chinese Academy of Science, Shanghai 200050, China"}], "References": []}, {"ArticleId": 94771409, "Title": "A knowledge-based method for tool path planning of large-sized parts", "Abstract": "Proper tool paths ensure the high-efficiency and high-quality machining of workpiece. The current tool path planning method is inadequate to meet the complex process requirements for the complex workpiece of abundant local features. Due to the lack of ability of independent process decision-making, the planning effect is still heavily dependent on human experience and the planning is time-consuming and laborious, which is not conducive to shortening development cycle of new products. Therefore, a novel knowledge-based method for tool path planning is proposed in this paper. The proposed method can automatically plan the tool path that meets the requirements of the machining process by the adaptive process decision-making according to the perceived features of complex workpiece. The knowledge extraction method based on the syntactic dependency analysis is used to extract named entities in the process document. And the correctly classified entities are filled in the nodes of knowledge model represented by the form of frame to finish the establishment of process knowledge base. Then, with the help of feature extraction module based on the hybrid clustering method, the semantic segmentation of the point cloud of the workpiece is completed, and the Feature Matrix (FM) is formed. This matrix is used to provide feature information required for process decision, including Feature Vector (FV) and Structure Description Matrix (SDM). Finally, the tool path is generated in the Path Generator. At last, an automatic path planning system is developed for the painting process of automotive exterior. The planned paths are tested for 20 types of bumpers, where the chromatic aberration Δ E of each product is less than 1.4; both the average thickness of each paint and the requirement of production rate are satisfactory. These test results verify the effectiveness of the proposed tool path planning method.", "Keywords": "Knowledge-based expert system ; Tool path planning ; Point cloud semantic segmentation ; Knowledge extraction", "DOI": "10.1016/j.eswa.2022.117685", "PubYear": 2022, "Volume": "205", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, Shanghai University, Shanghai, China;School of Mechatronic Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, Shanghai University, Shanghai, China;School of Mechatronic Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, Shanghai University, Shanghai, China;School of Mechatronic Engineering and Automation, Shanghai University, Shanghai, China;Corresponding author at: Room 431, Building 9, No. 333, Nanchen Road, Baoshan District, Shanghai 200444, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Shanghai Key Laboratory of Intelligent Manufacturing and Robotics, Shanghai University, Shanghai, China;School of Mechatronic Engineering and Automation, Shanghai University, Shanghai, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "SHANGHAI-FANUC Robotics CO. LTD, Shanghai, China"}], "References": [{"Title": "A knowledge based intelligent process planning method for controller of computer numerical control machine tools", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "31", "Issue": "7", "Page": "1751", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "A knowledge-based process planning framework for wire arc additive manufacturing", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "45", "Issue": "", "Page": "101135", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "A knowledge graph method for hazardous chemical management: Ontology design and entity identification", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "430", "Issue": "", "Page": "104", "JournalTitle": "Neurocomputing"}, {"Title": "A knowledge-based automated design system for mechanical products based on a general knowledge framework", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "178", "Issue": "", "Page": "114960", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Length-optimal tool path planning for freeform surfaces with preferred feed directions based on Poisson formulation", "Authors": "<PERSON><PERSON>", "PubYear": 2021, "Volume": "139", "Issue": "", "Page": "103072", "JournalTitle": "Computer-Aided Design"}, {"Title": "Knowledge-based program generation approach for robotic manufacturing systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102242", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 94771415, "Title": "Rapid non-mechanical reciprocal space mapping using LiNbO3-based bimorph piezoactuator", "Abstract": "A new approach is proposed for fast recording Reciprocal Space Maps (RSM) of crystals without using mechanical systems. It is based on the simultaneous use of two Adaptive Bending X-ray Optics (ABXO) elements made of lithium niobate crystals located on a laboratory X-ray diffractometer at the positions of the monochromator and analyzer. The technique has been tested on a model silicon crystal. It was shown that the proposed technique allows fast and precision measurements of the RSM, providing significant progress both in speed (up to 30 times) and accuracy of the data (up to 3 times) obtained. It opens up opportunities for studying the evolution of the defective structure of various prospective crystalline materials under external loading.", "Keywords": "X-ray diffraction methods ; Triple-crystal diffractometry ; Adaptive bending X-ray optics ; Time-resolved studies", "DOI": "10.1016/j.sna.2022.113674", "PubYear": 2022, "Volume": "343", "Issue": "", "JournalId": 3499, "JournalTitle": "Sensors and Actuators A: Physical", "ISSN": "0924-4247", "EISSN": "1873-3069", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia;National Research Center “Kurchatov Institute”, Pl. akademika Kurchatova 1, 123098 Moscow, Russia;Corresponding author at: Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia;National Research Center “Kurchatov Institute”, Pl. akademika Kurchatova 1, 123098 Moscow, Russia"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia;National Research Center “Kurchatov Institute”, Pl. akademika Kurchatova 1, 123098 Moscow, Russia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia;National Research Center “Kurchatov Institute”, Pl. akademika Kurchatova 1, 123098 Moscow, Russia"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia;National Research Center “Kurchatov Institute”, Pl. akademika Kurchatova 1, 123098 Moscow, Russia"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia"}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "Shubnikov Institute of Crystallography of Federal Scientific Research Centre “Crystallography and Photonics” of Russian Academy of Sciences, Leninsky prospekt 59, 119333 Moscow, Russia;National Research Center “Kurchatov Institute”, Pl. akademika Kurchatova 1, 123098 Moscow, Russia"}], "References": []}, {"ArticleId": 94771499, "Title": "Spectral-spatial hyperspectral image classification based on capsule network with limited training samples", "Abstract": "For hyperspectral images, the classification problem of limited training samples is an enormous challenge, and the lack of training samples is an essential factor that affects the classification accuracy. Making full use of the rich spectral and spatial information contained in hyperspectral images can achieve a high-precision classification of hyperspectral images with limited training samples. This paper proposed a new spectral-spatial feature extraction method and constructed a new classification framework for limited training samples. In our work, deep reinforcement learning is used to process the spectral features of hyperspectral images, and then the selected bands with the most information are used to extract the spatial features by extended morphological profiles. Finally, the hyperspectral image after feature extraction is classified by the capsule network, which is more effective at exploiting the relationships between hyperspectral imaging features in the spectral-spatial domain. We conducted classification experiments on five well-known hyperspectral image data sets. The experimental results reveal that the proposed method can better extract the spectral and spatial features in hyperspectral images, achieve higher classification accuracy with limited training samples, and reduce computational and time complexity.", "Keywords": "Hyperspectral image clsaaifiication ; feature extraction ; limited training samples ; deep reinforcement learning ; extend morphological profiles ; capsule network", "DOI": "10.1080/01431161.2022.2083459", "PubYear": 2022, "Volume": "43", "Issue": "8", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electrical and Information Engineering, Tianjin University, Tianjin, China;School of Information Engineering, Tianjin University of Commerce, Tianjin, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Information Engineering, Tianjin University of Commerce, Tianjin, China"}], "References": [{"Title": "A general generative adversarial capsule network for hyperspectral image spectral-spatial classification", "Authors": "<PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "11", "Issue": "1", "Page": "19", "JournalTitle": "Remote Sensing Letters"}]}, {"ArticleId": ********, "Title": "A New Objective Function Based on Additive Combination of Node and Link Metrics as a Mechanism Path Selection for RPL Protocol", "Abstract": "<p>Since its development by IETF, the IPv6 routing protocol for low power and lossy networks (RPL) remains the subject of several researches. RPL is based on objective function as a mechanism selection of paths in the network. However, the default objective functions standardized selects the routes according to a single routing metric that leads to an unoptimized path selection and a lot of parent changes. Thus, we propose in this paper weighted combined metrics objective function (WCM-OF) and non-weighted combined metrics objective function (NWCM-OF) that are based both on additive link quality and energy metrics with equal weights or not to achieve a tradeoff between reliability and saved energy levels. The proposed objective functions were implemented in the core of Contiki operating system and evaluated with Cooja emulator. Results show that the proposed objective functions improved the network performances compared to default objective functions.</p>", "Keywords": "", "DOI": "10.17762/ijcnis.v12i1.4446", "PubYear": 2020, "Volume": "12", "Issue": "1", "JournalId": 94344, "JournalTitle": "International Journal of Communication Networks and Information Security (IJCNIS)", "ISSN": "2076-0930", "EISSN": "2073-607X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "EEA&TI laboratory of FSTM, Hassan II University"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "EEA&TI laboratory of FSTM, Hassan II University"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "EEA&TI laboratory of FSTM, Hassan II University"}], "References": []}, {"ArticleId": 94771625, "Title": "A comprehensive investigation on novel center-based sampling for large-scale global optimization", "Abstract": "During the last decade, metaheuristic algorithms have been well-established approaches which are utilized for solving complex real-world optimization problems. The most metaheuristic algorithms uses stochastic strategies in their initialization phase as well as during their new candidate generation steps when there is no a-priori knowledge about the solution, which is a common valid assumption for any black-box optimization algorithm. In recent years, researchers have introduced a new concept called center-based sampling which can be utilized in any search component of the optimization process, but so far it mainly has been used just for population initialization. This novel concept clarifies that in a search space, the center point has a higher chance to be closer to an unknown solution compared to a random point, especially when the dimension of the search space increases. Thus, this concept helps the optimizer to find a better solution in a shorter time. In this paper, a comprehensive study has been conducted on the effect of center-based sampling to solve an optimization problem using three different levels of detailed investigation. These levels are as the follows: 1) considering no specific algorithm and no specific landscape (i.e., Monte-Carlo simulation); 2) considering a specific landscape but no specific algorithm (i.e., random search vs. center-based random search), and finally, 3) considering a specific algorithm and specific landscape which includes the proposing three different schemes for using center-based sampling scheme for solving Large-scale Global Optimization (LSGO) problems effectively. Furthermore, in this study, we seek to investigate the properties and capabilities of center-based sampling during optimization, which can be extended to utilize it in machine learning too, because optimization is a key role player in search and learning models. The proposed methods in this paper are evaluated on CEC 2013 LSGO benchmark functions and a real-world optimization problem, i.e., evolving ANN on two medical data sets. The experimental results confirm that center-based sampling has a crucial impact in improving the convergence rate of optimization/search algorithms when solving high-dimensional optimization problems.", "Keywords": "Center-based sampling ; Large-scale optimization ; Monte-Carlo simulation ; Differential Evolution ; High-dimensional optimization", "DOI": "10.1016/j.swevo.2022.101105", "PubYear": 2022, "Volume": "73", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Nature Inspired Computational Intelligence (NICI) Lab, Department of Electrical Computer and Software Engineering, Ontario Tech University (UOIT), Oshawa, Canada;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Nature Inspired Computational Intelligence (NICI) Lab, Department of Electrical Computer and Software Engineering, Ontario Tech University (UOIT), Oshawa, Canada"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Nature Inspired Computational Intelligence (NICI) Lab, Department of Electrical Computer and Software Engineering, Ontario Tech University (UOIT), Oshawa, Canada"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Business and Information Technology, Ontario Tech University (UOIT), Oshawa, Canada"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Nature Inspired Computational Intelligence (NICI) Lab, Department of Electrical Computer and Software Engineering, Ontario Tech University (UOIT), Oshawa, Canada"}], "References": [{"Title": "Recent trends in the use of statistical tests for comparing swarm and evolutionary computing algorithms: Practical guidelines and a critical review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100665", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 94771630, "Title": "A modified Ant Colony System for the asset protection problem", "Abstract": "During an escaped wildfire in a populated area’s vicinity, protective tasks should be carried out to secure crucial community assets, e.g., bridges, hospitals, power stations, and communication towers. In a real-life scenario, an important asset may require the combined effort of different fire suppression resources, which should be dispatched and scheduled to act synchronously in protecting the respective asset. The present research addresses the solution of a challenging routing problem in emergency response, the Asset Protection Problem (APP), which incorporates selective characteristics in routing a heterogeneous vehicle fleet with complex temporal and spatial constraints, i.e., time windows and synchronization requirements. Notably, the Modified Ant Colony System (MACS) algorithm is proposed to obtain effective APP solutions within a time suitable for operational purposes. Based on the conducted experiments, MACS outperforms the previously published solution approaches in the solution of large-scale APP benchmark instances. Notably, MACS obtained superior solutions in 159 out of 240 large-scale instances, while 87 of them represent new best results, considering the solutions achieved by the commercial solver CPLEX with a ten-hour time limit.", "Keywords": "Asset protection problem ; Ant colony optimization ; Synchronization ; Vehicle routing", "DOI": "10.1016/j.swevo.2022.101109", "PubYear": 2022, "Volume": "73", "Issue": "", "JournalId": 4179, "JournalTitle": "Swarm and Evolutionary Computation", "ISSN": "2210-6502", "EISSN": "2210-6510", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technical University of Crete, School of Production Engineering and Management University Campus, Chania, Crete, Greece"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Technical University of Crete, School of Production Engineering and Management University Campus, Chania, Crete, Greece"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Technical University of Crete, School of Production Engineering and Management University Campus, Chania, Crete, Greece"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Technical University of Crete, School of Production Engineering and Management University Campus, Chania, Crete, Greece;Corresponding author"}], "References": [{"Title": "A taxonomic review of metaheuristic algorithms for solving the vehicle routing problem and its variants", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "140", "Issue": "", "Page": "106242", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Recent trends in the use of statistical tests for comparing swarm and evolutionary computing algorithms: Practical guidelines and a critical review", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "", "Page": "100665", "JournalTitle": "Swarm and Evolutionary Computation"}, {"Title": "An ACS-based memetic algorithm for the heterogeneous vehicle routing problem with time windows", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "157", "Issue": "", "Page": "113379", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A new constraint programming model and a linear programming-based adaptive large neighborhood search for the vehicle routing problem with synchronization constraints", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "124", "Issue": "", "Page": "105085", "JournalTitle": "Computers & Operations Research"}, {"Title": "A Tutorial On the design, experimentation and application of metaheuristic algorithms to real-World optimization problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "64", "Issue": "", "Page": "100888", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": ********, "Title": "A new finite difference mapped unequal-sized WENO scheme for Hamilton-Jacobi equations", "Abstract": "In this paper, a new fifth-order finite difference mapped unequal-sized weighted essentially non-oscillatory (MUS-WENO) scheme is proposed for solving Hamilton-Jacobi equations in multi-dimensions. This new MUS-WENO scheme uses the same widest spatial stencils as that of the classical same order finite difference WENO scheme [19] , and could obtain smaller truncation errors and optimal fifth-order accuracy in smooth regions. The MUS-WENO scheme uses a convex combination of a quartic polynomial with two quadratic polynomials. The linear weights can be artificially set as any positive constants on condition that their summation is one. Together with a new mapping function, associated new mapped nonlinear weights are proposed to reduce the difference with the linear weights. So it is a first time that the new finite difference MUS-WENO scheme with a very small ε could reach optimal accuracy order convergence even near extreme points in smooth regions and avoid spurious oscillations near strong discontinuities when solving Hamilton-Jacobi equations. Generally speaking, the main advantages of such new MUS-WENO scheme comparing with the classical WENO scheme [19] are its good convergence, robustness, efficiency and easy extension to multi-dimensions. Numerical experiments are proposed to show the good performance of this new MUS-WENO scheme.", "Keywords": "Hamilton-Jacobi equation ; Mapping function ; Finite difference ; Mapped unequal-sized WENO scheme", "DOI": "10.1016/j.camwa.2022.06.002", "PubYear": 2022, "Volume": "119", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Mathematical Modelling and High Performance Computing of Air Vehicles (NUAA), MIIT, Nanjing University of Aeronautics and Astronautics, Nanjing, Jiangsu 210016, PR China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Mechanics and Control of Mechanical Structures, Key Laboratory of Mathematical Modelling and High Performance Computing of Air Vehicles (NUAA), MIIT, Nanjing University of Aeronautics and Astronautics, Nanjing, Jiangsu 210016, PR China;Corresponding author"}], "References": []}, {"ArticleId": 94771686, "Title": "PCS-LSTM: A hybrid deep learning model for multi-stations joint temperature prediction based on periodicity and closeness", "Abstract": "Temperature is one of the most important meteorological elements, which affects the daily lives of people all over the world. Owing to the rapid development of meteorological facilities, the number of meteorological observation stations on earth is gradually increasing, which brings challenges to the spatial association between stations. Many researchers focus on how to predict temperature more accurately utilizing these associations. However, the existing deep learning methods of temperature prediction have difficulty in capturing the interactions between neighboring stations in the spatial dimension. In addition, in the time dimension, the temperature in nature exhibits not only nearby variations but also periodic characteristics, which further increases the difficulty of temperature prediction. To solve the aforementioned two problems, we propose the periodicity and closeness social long short-term memory (PCS-LSTM) model, which includes PS-LSTM and CS-LSTM modules. Specifically, to model the relationships between multiple meteorological observation stations, we utilized the social pooling in the PS-LSTM and CS-LSTM modules to establish spatial associations. To further refine the temperature variation, we combine PS-LSTM and CS-LSTM to model the periodicity and closeness of the time series. Compared with the LSTM basic model, the experiments show that the MAE of our model prediction results is reduced by 0.109°C in the next 24 h compared.", "Keywords": "Temperature prediction ; LSTM ; Periodicity ; Closeness", "DOI": "10.1016/j.neucom.2022.06.015", "PubYear": 2022, "Volume": "501", "Issue": "", "JournalId": 1723, "JournalTitle": "Neurocomputing", "ISSN": "0925-2312", "EISSN": "1872-8286", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin 300401, China;Hebei Province Key Laboratory of Big Data Calculation, Tianjin 300401, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Artificial Intelligence, Hebei University of Technology, Tianjin 300401, China;Hebei Province Key Laboratory of Big Data Calculation, Tianjin 300401, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science, Nankai University, Tianjin 300071, China"}, {"AuthorId": 4, "Name": "Ming Han", "Affiliation": "School of Computer Science and Engineering, Shijiazhuang University, Shijiazhuang 050035, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Statistics and Data Science, Nankai University, Tianjin 300071, China;Corresponding author"}], "References": [{"Title": "Hyperlocal mapping of urban air temperature using remote sensing and crowdsourced weather data", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "242", "Issue": "", "Page": "111791", "JournalTitle": "Remote Sensing of Environment"}, {"Title": "A new drought monitoring approach: Vector Projection Analysis (VPA)", "Authors": "Bokyung Son; <PERSON>min <PERSON>; Jungho <PERSON>", "PubYear": 2021, "Volume": "252", "Issue": "", "Page": "112145", "JournalTitle": "Remote Sensing of Environment"}]}, {"ArticleId": 94771698, "Title": "SDN-assisted technique for traffic control and information execution in vehicular adhoc networks", "Abstract": "Recently, applying Software-Defined Networking (SDN) in automotive industry has sparked a lot of interest due to its capacity to ensure focused organization across the board coupled with its vast versatility of isolating data from the control plane. However, the major concern in traffic network is that varied crisis and diversion messages are expected to be shared or brought into by the connected vehicles. Thus, disclosure of this information results in an evaluation of the steering and control of such messages. This research aims at employing SDN-assisted technique in heterogeneous vehicular organizations in executing information traffic-using optimal course determination and guiding in situations like a blockage in Vehicle Ad Hoc Networks (VANETs). To realize this, a workable traffic reproduction model is proposed and the experiments realized a re-enactment findings in terms of Round Trip Time (RTT) and Packet Delivery Ratio (PDR) and improves the conventional information flow structure.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108108", "PubYear": 2022, "Volume": "102", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer and Information, Qiannan Normal University for Nationalities, Duyun, Guizhou, 558000,, China;Key Laboratory of Complex Systems and Intelligent Optimization of Guizhou, Duyun, Guizhou, 558000, China;Institute for Big Data Analytics and Artificial Intelligence (IBDAAI), Universiti Teknologi MARA, 40450 Shah Alam, Selangor, Malaysia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Computer and Mathematical Sciences, University Technology MARA, 40450 Shah Alam, Selangor, Malaysia;Institute for Big Data Analytics and Artificial Intelligence (IBDAAI), Universiti Teknologi MARA, 40450 Shah Alam, Selangor, Malaysia"}, {"AuthorId": 3, "Name": "Shahab B. Band", "Affiliation": "Future Technology Research Center, College of Future, National Yunlin University of Science and Technology, 123 University Road, Section 3, Douliou, Yunlin 64002, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Information Technology, Department of Information Technology, University of Technology and Applied sciences-Al Mussanah, Oman"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Research Centre, Future University in Egypt, New Cairo, 11745, Egypt"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Information and Communication Technology Research Group, Scientific Research Center, Al-Ayen University, Thi-Qar, Iraq;College of Computer Sciences and Information Technology, University of Kerbala, Karbala, Iraq"}, {"AuthorId": 7, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, Federal Polytechnic Oko, 434109, Oko, Nigeria;Corresponding author"}, {"AuthorId": 8, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Linguistic Data Sciences, University of Eastern Finland, 80101 Joensuu, Finland"}], "References": [{"Title": "SDN-based real-time urban traffic analysis in VANET environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "149", "Issue": "", "Page": "162", "JournalTitle": "Computer Communications"}, {"Title": "Sustainable Security for the Internet of Things Using Artificial Intelligence Architectures", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "21", "Issue": "3", "Page": "1", "JournalTitle": "ACM Transactions on Internet Technology"}, {"Title": "AI-empowered, blockchain and SDN integrated security architecture for IoT network of cyber physical systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "274", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 94771763, "Title": "Convolutional neural network based simulation and analysis for backward stochastic partial differential equations", "Abstract": "We develop a generic convolutional neural network (CNN) based numerical scheme to simulate the 2-tuple adapted strong solution to a unified system of backward stochastic partial differential equations (B-SPDEs) driven by Brownian motions, which can be used to model many real-world system dynamics such as optimal control and differential game problems. The dynamics of the scheme is modeled by a CNN through conditional expectation projection. It consists of two convolution parts: W layers of backward networks and L layers of reinforcement iterations. Furthermore, it is a completely discrete and iterative algorithm in terms of both time and space with mean-square error estimation and almost sure (a.s.) convergence supported by both theoretical proof and numerical examples. In doing so, we need to prove the unique existence of the 2-tuple adapted strong solution to the system under both conventional and Malliavin derivatives with general local Lipschitz and linear growth conditions.", "Keywords": "AI based Monte Carlo simulation ; Convolution neural network modeling ; Mean-square and almost sure convergence ; Backward stochastic partial differential equation ; Cauchy terminal value problem ; Random field Malliavin calculus", "DOI": "10.1016/j.camwa.2022.05.019", "PubYear": 2022, "Volume": "119", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "Wanyang Dai", "Affiliation": "Department of Mathematics and State Key Laboratory of Novel Software Technology, Nanjing University, Nanjing 210093, China"}], "References": []}, {"ArticleId": 94771916, "Title": "Soft computing for nonlinear risk assessment of complex socio-technical systems", "Abstract": "Work in socio-technical systems (STS) exhibits dynamic and complex behaviors, becoming difficult to model, evaluate and predict. This study develops an integrated soft computing approach for nonlinear risk assessment in STS: the functional resonance analysis method (FRAM) has been integrated with fuzzy sets. While FRAM is helpful to model performance variability in qualitative terms, the assessments are usually subjected to a high degree of uncertainty. This novel approach is meant to overcome the subjectivity associated with the qualitative analyses performed by experts’ judgments required by FRAM. For demonstration purposes, the approach has been applied to model a waste recycling process for construction materials. The results show how the approach allows assessing and ranking critical activities in STS operations.", "Keywords": "Soft computing ; Fuzzy sets ; Risk assessment ; Nonlinear method ; Functional Resonance Analysis Method (FRAM)", "DOI": "10.1016/j.eswa.2022.117828", "PubYear": 2022, "Volume": "206", "Issue": "", "JournalId": 1182, "JournalTitle": "Expert Systems with Applications", "ISSN": "0957-4174", "EISSN": "1873-6793", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Programa de Engenharia Ambiental, Universidade Federal do Rio de Janeiro, Brazil"}, {"AuthorId": 2, "Name": "Ana <PERSON> Rosa", "Affiliation": "Programa de Engenharia Ambiental, Universidade Federal do Rio de Janeiro, Brazil"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Sapienza University of Rome, Department of Mechanical and Aerospace Engineering, Italy"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Programa de Engenharia <PERSON>ntal, Universidade Federal do Rio de Janeiro, Brazil;Corresponding author at: Av. <PERSON><PERSON>, 149 – Centro de Tecnologia, D-207, Cidade Universitária, Rio de Janeiro, RJ CEP 21941-909, Brazil"}], "References": [{"Title": "A new insight into implementing Mamdani fuzzy inference system for dynamic process modeling: Application on flash separator fuzzy dynamic modeling", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "90", "Issue": "", "Page": "103485", "JournalTitle": "Engineering Applications of Artificial Intelligence"}, {"Title": "A review of fuzzy AHP methods for decision-making with subjective judgements", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "161", "Issue": "", "Page": "113738", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A novel three-way decision approach under hesitant fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "578", "Issue": "", "Page": "482", "JournalTitle": "Information Sciences"}, {"Title": "A three-way decision approach with probabilistic dominance relations under intuitionistic fuzzy information", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "582", "Issue": "", "Page": "114", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 94771990, "Title": "CAM-based non-local attention network for weakly supervised fire detection", "Abstract": "<p>Many available object detectors are already used in fire detection, such as Faster RCNN, SSD, YOLO, etc., to localize the fire in images. Although these approaches perform well, they require object-level annotations for training, which are manually labeled and very expensive. In this paper, we propose a method based on the Class Activation Map (CAM) and non-local attention to explore the Weakly Supervised Fire Detection (WSFD) given only image-level annotations. Specifically, we first train a deep neural network with non-local attention as the classifier for identifying fire and non-fire images. Then, we use the classifier to create a CAM for every fire image in the inference stage and finally generate a corresponding bounding box according to each connected domain of the CAM. To evaluate the availability of our method, a benchmark dataset named WS-FireNet is constructed, and comprehensive experiments are performed on the WS-FireNet dataset. The experimental results demonstrate that our approach is effective in image-level supervised fire detection.</p>", "Keywords": "Weakly supervised; Fire detection; Class activation map; Non-local attention", "DOI": "10.1007/s11761-022-00336-6", "PubYear": 2022, "Volume": "16", "Issue": "2", "JournalId": 4313, "JournalTitle": "Service Oriented Computing and Applications", "ISSN": "1863-2386", "EISSN": "1863-2394", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, China; School of Data Science and Information Engineering, Guizhou Minzu University, Guiyang, China; Key Laboratory of Big Data and Intelligent Robot, Ministry of Education, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, China; Key Laboratory of Big Data and Intelligent Robot, Ministry of Education, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Software Engineering, South China University of Technology, Guangzhou, China; Pazhou Lab, Guangzhou, China"}], "References": [{"Title": "A deep learning approach for collaborative prediction of Web service QoS", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "1", "Page": "5", "JournalTitle": "Service Oriented Computing and Applications"}, {"Title": "Fire Detection Method Based on Depthwise Separable Convolution and YOLOv3", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "18", "Issue": "2", "Page": "300", "JournalTitle": "International Journal of Automation and Computing"}]}, {"ArticleId": 94771994, "Title": "Image representation method based on Gaussian function and non-uniform partition", "Abstract": "<p>Image representation or reconstruction methods are important in digital image processing. Due to different image features happening in different regions, in this work, an image representation algorithm based on Gaussian function and non-uniform partition is proposed to represent the image with different functions in non-uniform regions. That means the pixel values in each region can be approximated by an extension of the Gaussian function after applying the least square approximation. The experimental results prove that the proposed algorithm has better performance than other non-uniform partition algorithms in terms of reconstructed image quality and time complexity. In addition, the partition mesh density can reflect the texture complexity of image regions and help to determine where the watermark can be embedded. Therefore, a novel watermark algorithm based on the proposed non-uniform partition is constructed and tested. The results show that it can embed a big gray watermark into the host image without causing its obvious distortion. This indicates some of the advantages of the proposed image representation algorithm.</p>", "Keywords": "Image representation; Image reconstruction; Gaussian function; Non-uniform partition; Watermark embedding", "DOI": "10.1007/s11042-022-13213-3", "PubYear": 2023, "Volume": "82", "Issue": "1", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Faculty of Innovation Engineering, Macau University of Science and Technology, Macau, China"}, {"AuthorId": 2, "Name": "KinTak U", "Affiliation": "School of Computer Science and Engineering, Faculty of Innovation Engineering, Macau University of Science and Technology, Macau, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Engineering, Faculty of Innovation Engineering, Macau University of Science and Technology, Macau, China"}], "References": [{"Title": "A robust digital image watermarking scheme based on bat algorithm optimization and SURF detector in SWT domain", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "79", "Issue": "29-30", "Page": "21653", "JournalTitle": "Multimedia Tools and Applications"}]}, {"ArticleId": 94771998, "Title": "A well-balanced discontinuous Galerkin method for the shallow water flows on erodible bottom", "Abstract": "In this paper, we investigate the shallow water (SW) flow over the erodible layer using a fully coupled mathematical model in two-dimensional (2D) space. A well-balanced discontinuous Galerkin (DG) scheme is proposed for solving the SW equations with sediment transport and bed evolution. To achieve the well-balanced property of the numerical scheme easily, the nonlinear SW equations are first reformulated into a new form by introducing an auxiliary variable. Then the DG method is used to discretize the model, in which the FORCE flux is used. By choosing an appropriate value of the auxiliary variable, we can prove that the numerical method can accurately maintain the steady solution in still water, so it is indeed an equilibrium preserving scheme. The well-balanced property can be extended to any numerical flux which satisfies the consistency. Moreover, we investigate the impact on the numerical results if the appropriate value of auxiliary variable is slightly modified. The effectiveness of the numerical method is finally verified by numerical experiments.", "Keywords": "Shallow water flows ; Discontinuous Galerkin method ; FORCE flux ; Well-balanced property", "DOI": "10.1016/j.camwa.2022.05.032", "PubYear": 2022, "Volume": "119", "Issue": "", "JournalId": 2539, "JournalTitle": "Computers & Mathematics with Applications", "ISSN": "0898-1221", "EISSN": "1873-7668", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "School of Mathematical Sciences, University of Electronic Science and Technology of China, Sichuan, 611731, PR China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, PR China"}, {"AuthorId": 3, "Name": "Haiyun Dong", "Affiliation": "College of Mathematics and Statistics, Chongqing University, Chongqing, 401331, PR China;Corresponding author"}], "References": []}, {"ArticleId": 94772005, "Title": "Attention-enhanced and trusted multimodal learning for micro-video venue recognition", "Abstract": "Unified venue representation is typically generated by integrating multi-modal information of micro-videos. It is also important to achieve reliable multi-modal fusion. In prior works, the reliability of decisions from multiple modalities is limited without the uncertainty estimation, which indicates whether or to what extent such decisions can be trusted. To this end, in this paper an attention-enhanced and trusted multimodal learning (AETML) model is proposed to achieve reasonable multimodal decision fusion for micro-video venue recognition. Specifically, a domain-adaptive visual transformer (VT) is used as the robust visual feature extractor. Denoise autoencoder and sentence2vector methods are applied to extract acoustic and textural features. Then, attention nets are devised to enhance the extracted features, and output class probabilities of each modality. An uncertainty estimation network is constructed to dynamically assess the reliability of such decisions, which are transformed into Dirichlet distribution for modeling the uncertainty. Lastly, trusted prediction results are yielded by integrating the adjusted distributions with uncertainty estimations according to the <PERSON><PERSON><PERSON>–<PERSON> evidence theory. The experimental results on a public micro-video dataset show that the proposed model outperforms the state-of-the-art methods. Further studies validate the effectiveness and robustness brought by the uncertainty estimation.", "Keywords": "", "DOI": "10.1016/j.compeleceng.2022.108127", "PubYear": 2022, "Volume": "102", "Issue": "", "JournalId": 3579, "JournalTitle": "Computers & Electrical Engineering", "ISSN": "0045-7906", "EISSN": "1879-0755", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Media Convergence and Communication, Communication University of China, Beijing, 100024, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Media Convergence and Communication, Communication University of China, Beijing, 100024, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Media Convergence and Communication, Communication University of China, Beijing, 100024, China;School of Computer and Cyber Sciences, Communication University of China, Beijing, 100024, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Media Convergence and Communication, Communication University of China, Beijing, 100024, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Media Convergence and Communication, Communication University of China, Beijing, 100024, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Media Convergence and Communication, Communication University of China, Beijing, 100024, China"}], "References": [{"Title": "Grad-CAM: Visual Explanations from Deep Networks via Gradient-Based Localization", "Authors": "<PERSON><PERSON><PERSON><PERSON> <PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "128", "Issue": "2", "Page": "336", "JournalTitle": "International Journal of Computer Vision"}, {"Title": "Attention based consistent semantic learning for micro-video scene recognition", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "543", "Issue": "", "Page": "504", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 94772049, "Title": "Local search genetic algorithm-based possibilistic weighted fuzzy c-means for clustering mixed numerical and categorical data", "Abstract": "<p>Clustering for mixed numerical and categorical attributes has attracted many researchers due to its necessity in many real-world applications. One crucial issue concerned in clustering mixed data is to select an appropriate distance metric for each attribute type. Besides, some current clustering methods are sensitive to the initial solutions and easily trap into a locally optimal solution. Thus, this study proposes a local search genetic algorithm-based possibilistic weighted fuzzy c -means (LSGA-PWFCM) for clustering mixed numerical and categorical data. The possibilistic weighted fuzzy c-means (PWFCM) is firstly proposed in which the object-cluster similarity measure is employed to calculate the distance between two mixed-attribute objects. Besides, each attribute is placed a different important role by calculating its corresponding weight in the PWFCM procedure. Thereafter, GA is used to find a set of optimal parameters and the initial clustering centroids for the PFCM algorithm. To avoid local optimal solution, local search-based variable neighborhoods are embedded in the GA procedure. The proposed LSGA-PWFCM algorithm is compared with other benchmark algorithms based on some public datasets in UCI machine learning repository to evaluate its performance. Two clustering validation indices are used, i.e., clustering accuracy and Rand index. The experimental results show that the proposed LSGA-PWFCM outperforms other algorithms on most of the tested datasets.</p>", "Keywords": "Local search genetic algorithm; Mixed data; Possibilistic fuzzy c-means; Variable neighborhood search", "DOI": "10.1007/s00521-022-07411-1", "PubYear": 2022, "Volume": "34", "Issue": "20", "JournalId": 1806, "JournalTitle": "Neural Computing and Applications", "ISSN": "0941-0643", "EISSN": "1433-3058", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Project Management, The University of Danang–University of Science and Technology, Danang, Vietnam"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Industrial Management, National Taiwan University of Science and Technology, Taipei, Taiwan, ROC"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Faculty of Transportation Mechanical Engineering, The University of Danang–University of Science and Technology, Danang, Viet Nam"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Project Management, The University of Danang–University of Science and Technology, Danang, Vietnam"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Project Management, The University of Danang–University of Science and Technology, Danang, Vietnam"}], "References": [{"Title": "Fuzzy c-means and K-means clustering with genetic algorithm for identification of homogeneous regions of groundwater quality", "Authors": "<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "8", "Page": "3763", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Application research of improved genetic algorithm based on machine learning in production scheduling", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "7", "Page": "1857", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Diagnosis method of ultrasonic elasticity image of peripheral lung cancer based on genetic algorithm", "Authors": "<PERSON>g Dai; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "24", "Page": "18315", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Metaheuristic-based possibilistic multivariate fuzzy weighted c-means algorithms for market segmentation", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "96", "Issue": "", "Page": "106639", "JournalTitle": "Applied Soft Computing"}, {"Title": "initKmix-A novel initial partition generation algorithm for clustering mixed data using k-means-based clustering", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "167", "Issue": "", "Page": "114149", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Micro-Genetic algorithm with fuzzy selection of operators for multi-Objective optimization: μFAME", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "100818", "JournalTitle": "Swarm and Evolutionary Computation"}]}, {"ArticleId": 94772091, "Title": "A Discrete Mathematical Model of the Variable State of the Pandemic", "Abstract": "<p>The paper describes and analyzes a discrete mathematical model of the variable state of the pandemic, which is important for determining production quantities of vaccines and antiviral drugs, predicting the number of infected persons, and the intensity of the process of disseminating information or new ideas to the public.    According to the system of differential equations of the pandemic, a discrete mathematical model in vector-matrix form was developed and the equilibrium of the model in the space state was proved.     As a result of the implementation of the pandemic model, the discrete dynamic curves of the variable state were obtained in a Matlab  package.</p>", "Keywords": "pandemic;mathematical modeling Covid-19;open systems;equation of the discrete variable state;superposition of epidemic waves;prediction", "DOI": "10.24203/ijcit.v11i2.210", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 75576, "JournalTitle": "International Journal of Computer and Information Technology(2279-0764)", "ISSN": "", "EISSN": "2279-0764", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technology, Akaki Tsereteli State University, Kutaisi, Georgia"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatics Systems and Networks, University of Debrecen Debrecen, Hungary"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Informatic Georgian Technical University, Tbilisi, Georgia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Technology, Akaki Tsereteli State University, Kutaisi, Georgia"}], "References": []}, {"ArticleId": 94772175, "Title": "A Characterization of approximate decentralized fixed modes of time‐delay systems", "Abstract": "<p>The concept of decentralized fixed mode (DFM) is crucial in determining the output feedback stabilizability by linear time-invariant (LTI) decentralized controllers. However, in some cases, although a system has no DFMs, some of its modes may be very close to being fixed. It is thus important to characterize these modes, which are called approximate DFMs (ADFMs). In this paper, the characterization of ADFMs is discussed for LTI neutral (possibly descriptor-type) time-delay systems under static and dynamic decentralized output feedback control laws, and a mode mobility measure is proposed to provide quantitative information about ADFMs. To analytically compare the proposed measure, some of the commonly used quantitative measures for the characterization of ADFMs of finite-dimensional systems have also been extended to the considered type of systems. The herein proposed approach allows multiple time delays and does not require additional expansions or preliminaries for multi-input multi-output systems, like some other commonly used methods. Thus, characterization with this measure, in general, requires less computational load, while it can provide more explicit information about the controller effort than the other considered measures.</p>", "Keywords": "assignability measure;decentralized fixed modes;mode mobility;time-delay systems", "DOI": "10.1002/asjc.2825", "PubYear": 2023, "Volume": "25", "Issue": "1", "JournalId": 3761, "JournalTitle": "Asian Journal of Control", "ISSN": "1561-8625", "EISSN": "1934-6093", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering Eskişehir Technical University  Eskişehir Turkey"}, {"AuthorId": 2, "Name": "Altuğ İftar", "Affiliation": "Department of Electrical and Electronics Engineering Eskişehir Technical University  Eskişehir Turkey"}], "References": [{"Title": "Control of large scale interconnected systems with input and state delays using decentralized adaptive state observers", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "22", "Issue": "4", "Page": "1458", "JournalTitle": "Asian Journal of Control"}, {"Title": "Decentralized robust adaptive output‐feedback control for a class of large‐scale stochastic time‐delay nonlinear systems with dynamic interactions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "23", "Issue": "3", "Page": "1557", "JournalTitle": "Asian Journal of Control"}]}, {"ArticleId": 94772192, "Title": "An approach for the development and implementation of commissioning service configurators in engineer-to-order companies", "Abstract": "The commissioning of newly developed, complex engineer-to-order (ETO) products, such as plants, buildings, and ships, entails the testing and validation of installed systems prior to operation. Challenges related to the delivery of such commissioning services are manifold, including high levels of uncertainty and challenging information management. In this paper, the question of how configurator technology can be developed and utilised for the specification of commissioning services is investigated. Specifically, while several studies have demonstrated the significant benefits of applying configurators to support product specification processes in ETO companies, none have explored their usefulness in relation to commissioning services. Thus, based on the literature pertaining to commissioning services and product configuration, a five-step approach to the development of commissioning service configurators is developed. The approach was tested in a case company, resulting in the creation of a commissioning configurator. The approach was well received by company employees. Moreover, its use resulted in a commissioning service configurator that reduced the service specification time by more than 70% and the number of people required for the specification by 40%. Other identified benefits included a reduction in planning efforts, improved utilisation of expert knowledge, and improved resource allocation.", "Keywords": "Configuration system ; Service configuration ; Modular architecture ; Service modularity", "DOI": "10.1016/j.compind.2022.103717", "PubYear": 2022, "Volume": "142", "Issue": "", "JournalId": 5958, "JournalTitle": "Computers in Industry", "ISSN": "0166-3615", "EISSN": "1872-6194", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Mechanical Engineering, Technical University of Denmark, Kgs. <PERSON>, Denmark;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Technical University of Denmark, Kgs. Lyngby, Denmark"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Department of Technology, Management and Economics, Technical University of Denmark, Kgs. Lyngby, Denmark"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Entrepreneurship and Relationship Management, University of Southern Denmark, Kolding, Denmark"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, Technical University of Denmark, Kgs. Lyngby, Denmark"}], "References": []}, {"ArticleId": 94772243, "Title": "Intelligent and Collaborative Orchestration of Network Slices", "Abstract": "5G and beyond network will support vertical industry applications, and the resource requirements of each service vary widely. The introduction of network slices provides great flexibility to the network, which can realize the differentiated customization requirements of service. However, while determining how to intelligently orchestrate the network slices is an important challenge, current solutions rarely treat multiple customized requirements of delay, bandwidth, load balancing, and slice isolation. In this article, network slice orchestration is considered from the perspective of slice isolation and cloud-edge collaboration. First, differentiated isolation level requirements are restricted to constraints, the customized isolation is realized. Second, bandwidth is saved and network latency is reduced via the collaboration of cloud and edge data centers. In addition, exclusive orchestration optimization objectives that match various service needs are proposed to distinguish the specific requirements of different slices. Finally, two deep reinforcement learning-based algorithms are proposed. The experimental results demonstrate that the proposed algorithms can optimize the objectives while ensuring differentiated isolation levels. For typical slices, the proposed algorithms respectively reduce bandwidth consumption by about 29% and 64%, reduce slice delay by about 14% and 70%, and optimize load balancing by about 17% and 23%.", "Keywords": "Network slice orchestration;slice isolation;cloud-edge-collaboration;deep reinforcement learning", "DOI": "10.1109/TSC.2022.3180831", "PubYear": 2023, "Volume": "16", "Issue": "2", "JournalId": 16720, "JournalTitle": "IEEE Transactions on Services Computing", "ISSN": "1939-1374", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 4, "Name": "Wen<PERSON>g Li", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>u", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "State Key Laboratory of Networking and Switching Technology, Beijing University of Posts and Telecommunications, Beijing, China"}, {"AuthorId": 7, "Name": "<PERSON>", "Affiliation": "Synchromedia Laboratory, École de Technologie Supérieure, Université du Québec, Québec, QC, Canada"}], "References": [{"Title": "A Reinforcement Learning Method for Constraint-Satisfied Services Composition", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "13", "Issue": "5", "Page": "786", "JournalTitle": "IEEE Transactions on Services Computing"}]}, {"ArticleId": 94772400, "Title": "Privacy-preserving machine learning based on secure four-party computations", "Abstract": "Статья посвящена анализу систем конфиденциального машинного обучения, основанных на концепции безопасных четырехсторонних вычислений. Анализируются преимущества четырехсторонней модели вычислений над двух- и трёхсторонними, приводятся определения, выражающие стойкость протоколов безопасных многосторонних вычислений к воздействию активного противника. В качестве примера системы безопасных четырехсторонних вычислений с развитой функциональностью, наиболее полно реализующей свойства стойкости к активному противнику, рассматривается система Tetrad. Анализируется реализованная в Tetrad концепция вычислений, основанная на идее смешанного использования арифметических, булевых и искаженных схем при вычислении функций. Рассматривается многоуровневая архитектура системы, подробно анализируются протоколы, относящиеся к различным уровням архитектуры. Проводится анализ различных форм разделения секрета. Систематизация протоколов завершается анализом высокоуровневых протоколов, используемых для реализации процедур обучения и применения моделей в конфиденциальном исполнении. Рассматриваемый набор протоколов позволяет выполнять стандартные для систем машинного обучения высокоуровневые операции, но с разделенными секретами. Приведены выводы о достоинствах, недостатках, особенностях и ограничениях четырехсторонних протоколов безопасных вычислений для решения задач конфиденциального машинного обучения.", "Keywords": "privacy-preserving machine learning; secure multi-party computations; secret sharing scheme.;конфиденциальное машинное обучение; безопасные многосторонние вычисления; схемы разделения секрета.", "DOI": "10.26583/bit.2022.2.04", "PubYear": 2022, "Volume": "29", "Issue": "2", "JournalId": 44598, "JournalTitle": "Bezopasnost informacionnyh tehnology", "ISSN": "2074-7128", "EISSN": "2074-7136", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "National Research Nuclear University MEPhI (Moscow Engineering Physics Institute)"}], "References": []}, {"ArticleId": 94772592, "Title": "Optimizing Time Complexity of Searching Operation: A <PERSON>", "Abstract": "", "Keywords": "", "DOI": "10.47760/ijcsmc.2022.v11i06.001", "PubYear": 2022, "Volume": "11", "Issue": "6", "JournalId": 80808, "JournalTitle": "International Journal of Computer Science and Mobile Computing", "ISSN": "", "EISSN": "2320-088X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "VIT Bhopal University"}], "References": []}, {"ArticleId": 94772610, "Title": "Revisiting the Extension of Matsui’s Algorithm 1 to Linear Hulls: Application to TinyJAMBU", "Abstract": "<p>At EUROCRYPT ’93, <PERSON><PERSON> introduced linear cryptanalysis. Both <PERSON><PERSON>’s Algorithm 1 and 2 use a linear approximation involving certain state bits. Algorithm 2 requires partial encryptions or decryptions to obtain these state bits after guessing extra key bits. For ciphers where only part of the state can be obtained, like some stream ciphers and authenticated encryption schemes, Algorithm 2 will not work efficiently since it is hard to implement partial encryptions or decryptions. In this case, Algorithm 1 is a good choice since it only involves these state bits, and one bit of key information can be recovered using a single linear approximation trail. However, when there are several strong trails containing the same state bits, known as the linear hull effect, recovering key bits with Algorithm 1 is infeasible. To overcome this, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> extended <PERSON><PERSON>’s Algorithm 1 to linear hulls. However, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> found that their theoretical estimates are quite pessimistic for low success probabilities and too optimistic for high success probabilities. To deal with this, we construct new statistical models where the theoretical success probabilities are in a good accordance with experimental ones, so that we provide the first accurate analysis of the extension of <PERSON><PERSON>’s Algorithm 1 to linear hulls. To illustrate the usefulness of our new models, we apply them to one of the ten finalists of the NIST Lightweight Cryptography (LWC) Standardization project: TinyJAMBU. We provide the first cryptanalysis under the nonce-respecting setting on the full TinyJAMBU v1 and the round-reduced TinyJAMBU v2, where partial key bits are recovered. Our results do not violate the security claims made by the designers.</p>", "Keywords": "Matsui’s Algorithm 1;Linear Hull;TinyJAMBU", "DOI": "10.46586/tosc.v2022.i2.161-200", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81466, "JournalTitle": "IACR Transactions on Symmetric Cryptology", "ISSN": "", "EISSN": "2519-173X", "Authors": [{"AuthorId": 1, "Name": "Muzhou Li", "Affiliation": "Key Laboratory of Cryptologic Technology and Information Security, Ministry of Education, Shandong University, Jinan, China; School of Cyber Science and Technology, Shandong University, Qingdao, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Strativia, Largo, MD, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Cryptologic Technology and Information Security, Ministry of Education, Shandong University, Jinan, China; School of Cyber Science and Technology, Shandong University, Qingdao, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Cryptologic Technology and Information Security, Ministry of Education, Shandong University, Jinan, China; School of Cyber Science and Technology, Shandong University, Qingdao, China; Quan Cheng Shandong Laboratory, Jinan, China"}], "References": []}, {"ArticleId": 94772680, "Title": "Evolutionary neural networks for deep learning: a review", "Abstract": "<p>Evolutionary neural networks (ENNs) are an adaptive approach that combines the adaptive mechanism of Evolutionary algorithms (EAs) with the learning mechanism of Artificial Neural Network (ANNs). In view of the difficulties in design and development of DNNs, ENNs can optimize and supplement deep learning algorithm, and the more powerful neural network systems are hopefully built. Many valuable conclusions and results have been obtained in this field, especially in the construction of automated deep learning systems. This study conducted a systematic review of the literature on ENNs by using the PRISMA protocol. In literature analysis, the basic principles and development background of ENNs are firstly introduced. Secondly, the main research techniques are introduced in terms of connection weights, architecture design and learning rules, and the existing research results are summarized and the advantages and disadvantages of different research methods are analyzed. Then, the key technologies and related research progress of ENNs are summarized. Finally, the applications of ENNs are summarized and the direction of future work is proposed.</p>", "Keywords": "Deep neural networks; Evolutionary algorithms; Evolutionary neural networks; Deep learning; PRISMA review", "DOI": "10.1007/s13042-022-01578-8", "PubYear": 2022, "Volume": "13", "Issue": "10", "JournalId": 7428, "JournalTitle": "International Journal of Machine Learning and Cybernetics", "ISSN": "1868-8071", "EISSN": "1868-808X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Physics and Electronic Engineering, Northwest Normal University, Lanzhou, China"}], "References": [{"Title": "Evolution of Deep Convolutional Neural Networks Using Cartesian Genetic Programming", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "1", "Page": "141", "JournalTitle": "Evolutionary Computation"}, {"Title": "Guiding Neuroevolution with Structural Objectives", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "1", "Page": "115", "JournalTitle": "Evolutionary Computation"}, {"Title": "Autonomous deep learning: A genetic DCNN designer for image classification", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "379", "Issue": "", "Page": "152", "JournalTitle": "Neurocomputing"}, {"Title": "Deep neural networks compression learning based on multiobjective evolutionary algorithms", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "378", "Issue": "", "Page": "260", "JournalTitle": "Neurocomputing"}, {"Title": "Neuroevolutionary based convolutional neural network with adaptive activation functions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "381", "Issue": "", "Page": "306", "JournalTitle": "Neurocomputing"}, {"Title": "DeepMaker: A multi-objective optimization framework for deep neural networks in embedded systems", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "73", "Issue": "", "Page": "102989", "JournalTitle": "Microprocessors and Microsystems"}, {"Title": "A Deep Learning Architecture for Psychometric Natural Language Processing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "38", "Issue": "1", "Page": "1", "JournalTitle": "ACM Transactions on Information Systems"}, {"Title": "Surrogate-Assisted Evolutionary Search of Spiking Neural Architectures in Liquid State Machines", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "406", "Issue": "", "Page": "12", "JournalTitle": "Neurocomputing"}, {"Title": "MLDroid—framework for Android malware detection using machine learning techniques", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2021, "Volume": "33", "Issue": "10", "Page": "5183", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Synergies between synaptic and intrinsic plasticity in echo state networks", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "432", "Issue": "", "Page": "32", "JournalTitle": "Neurocomputing"}, {"Title": "Evolving graph convolutional networks for neural architecture search", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "2", "Page": "899", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 94772681, "Title": "Experimental study of quality monitoring system integrated with a microphone array in laser microlap welding", "Abstract": "<p>In microlap welding, a real-time welding quality monitoring system is crucial to the identification of low strength joints caused by the unreliable contact between two layers of stainless metal sheets. In this study, a multispacing configured microphone array filter was designed and applied to a sound-based quality monitoring system for laser microlap welding, and the filter’s performance was evaluated to improve the reliability of the developed monitoring system when collected sound signals are contaminated by the noises generated around a welding site. In the experimental setup, joint strength was modulated by controlling the clamping conditions of the fixture and changing the welding location. The results indicated that noises contaminated the signals obtained from single microphones and reduced classification rates by up to 25% when time domain features were adopted. Through the application of the proposed microphone array in the developed monitoring system, the classification rate of weld quality can be improved to a level resembling that observed when artificial noise is not applied to the system.</p>", "Keywords": "Laser microwelding; Microphone array; Audible sound; Quality monitoring", "DOI": "10.1007/s00170-022-09459-8", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Chung Hsing University, Taichung, Taiwan"}, {"AuthorId": 2, "Name": "Ming-<PERSON><PERSON> Lu", "Affiliation": "Department of Mechanical Engineering, National Chung Hsing University, Taichung, Taiwan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Chung Hsing University, Taichung, Taiwan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Mechanical Engineering, National Chung Hsing University, Taichung, Taiwan"}], "References": [{"Title": "Application of sensing techniques and artificial intelligence-based methods to laser welding real-time monitoring: A critical review of recent literature", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "57", "Issue": "", "Page": "1", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 94772682, "Title": "Fault location in distribution network by solving the optimization problem using genetic algorithm based on the calculating voltage changes", "Abstract": "<p>Due to the extent and existence of multiple branches in the distribution network, one of the main challenges in this network will be the issue of fault location. Gradually, with the expansion of smart grids and the use of smart meters in the distribution network, various methods are proposed to use these devices to the fault location. In this paper, fault location in the distribution network is defined as an optimization problem with an objective function based on network voltage changes before and after the fault. The objective function of the problem consists of the difference between the two approaches. In the first approach, the combination of the power flow (PF) method with smart meter data is used to calculate voltage changes before and after the fault in each network node. In the second approach, by calculating the network impedance matrix per the hypothetical fault location in each node of the network, these voltage changes are calculated, and by comparing the results of these two approaches, when the objective function of the problem is minimized, the optimal answer will be the fault location in the network. Therefore, in this issue, two objective functions of the difference between these two approaches are defined, one to identify the faulty section and the other to fault location has been used. The required data are extracted from the network partly before the fault and partly after the fault. Due to the need to process these data, the volume of calculations and the wide distribution network, a genetic algorithm (GA) has been used to solve the problem. To evaluate the speed and accuracy of the proposed method in fault location, the proposed method has been implemented on the largest distribution network, namely the IEEE 123-node distribution test system through MATLAB software. The results showed that the proposed method had a good performance for types of faults and different fault resistance so that it was able to detect the location of the fault with an accuracy of 1.501% in less than 60 s.</p>", "Keywords": "Fault location; Distribution network; Smart meter; Voltage changes; Optimization; Genetic algorithm", "DOI": "10.1007/s00500-022-07203-8", "PubYear": 2022, "Volume": "26", "Issue": "17", "JournalId": 1536, "JournalTitle": "Soft Computing", "ISSN": "1432-7643", "EISSN": "1433-7479", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical Engineering Department, Bushehr Branch, Islamic Azad University, Bushehr, Iran"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electrical and Electronics Engineering, National Institute of Technology Delhi, New Delhi, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON>", "Affiliation": "Electrical Engineering Department, Bushehr Branch, Islamic Azad University, Bushehr, Iran"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Clinical-Laboratory Center of Power System & Protection, Persian Gulf University, Bushehr, Iran; Computer center and data analysis, Persian Gulf University, Bushehr, Iran"}], "References": [{"Title": "A Methodology for Security Classification applied to Smart Grid Infrastructures", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "28", "Issue": "", "Page": "100342", "JournalTitle": "International Journal of Critical Infrastructure Protection"}, {"Title": "Fault Location in the Transmission Network Using Artificial Neural Network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "54", "Issue": "1", "Page": "39", "JournalTitle": "Automatic Control and Computer Sciences"}, {"Title": "Aquila Optimizer: A novel meta-heuristic optimization algorithm", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "157", "Issue": "", "Page": "107250", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Reptile Search Algorithm (RSA): A nature-inspired meta-heuristic optimizer", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "191", "Issue": "", "Page": "116158", "JournalTitle": "Expert Systems with Applications"}]}, {"ArticleId": 94772688, "Title": "Surface Electromyography Signal Recognition Based on Deep Learning for Human-Robot Interaction and Collaboration", "Abstract": "<p>The interaction between humans and collaborative robots in performing given tasks has aroused the interest of researchers and industry for the development of gesture recognition systems. Surface electromyography (sEMG) devices are recommended to capture human hand gestures. However, this kind of technology raises significant challenges. sEMG signals are difficult to acquire and isolate reliably. The creation of a gesture representative model is hard due to the non-explicit nature of sEMG signals. Several solutions have been proposed for the recognition of sEMG-based hand gestures, but none of them are entirely satisfactory. This study contributes to take a step forward in finding the solution to this problem. A sEMG capturing prototype device was used to collect human hand gestures and a two-step algorithm is proposed to recognize five valid gestures, invalid gestures and non-gestures. The former algorithm step (segmentation) is used for sEMG signal isolation to separate signals containing gestures from signals containing non-gestures. The latter step of the algorithm (recognition) is based on a deep learning method, a convolutional neural network (CNN) that identifies which gesture is in the sEMG signals. The performances of the prototype device and recognition architecture were compared successfully with the off-the-shelf sEMG device Myo. Results indicated that the segmentation process played an important role in the success of the gesture recognition system, excluding sEMG signals containing non-gestures. The proposed system was applied successfully in the control loop of a collaborative robotic application, in which the gesture recognition system achieved an online class recognition rate (CR) of 98%, outperforming similar studies in the literature.</p>", "Keywords": "Pattern recognition; Data segmentation; Deep learning; Surface electromyography; Robotics; Industry 4.0", "DOI": "10.1007/s10846-022-01666-5", "PubYear": 2022, "Volume": "105", "Issue": "2", "JournalId": 9895, "JournalTitle": "Journal of Intelligent & Robotic Systems", "ISSN": "0921-0296", "EISSN": "1573-0409", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Research and Development Unit for Mechanical and Industrial Engineering, NOVA University of Lisbon, Campus da Caparica, Lisbon, Portugal"}], "References": []}, {"ArticleId": 94772698, "Title": "Automatic vocabulary and graph verification for accurate loop closure detection", "Abstract": "<p>Localizing previously visited places during long-term localization and mapping, that is, loop closure detection (LCD), is a crucial technique to correct accumulated inconsistencies. In common bag-of-words (BoW) model, a visual vocabulary is built to associate features for detecting loops. Currently, methods that build vocabularies off-line determine scales of the vocabulary by trial-and-error, which results in unreasonable feature association. Moreover, the detection precision of the algorithm declines due to perceptual aliasing given that the BoW-based method ignores the positions of visual features. To build the optimal vocabulary automatically and eliminate human heuristics, we propose a natural convergence criterion based on the comparison between the radii of nodes and the drifts of feature descriptors in vocabulary construction. Furthermore, a novel topological graph verification method is proposed for validating loop candidates, which can effectively distinguish visual ambiguities by involving geometrical position of features and thus improve the precision of LCD. Experiments on various public datasets verify the effectiveness of our proposed approach.</p>", "Keywords": "bag of words;feature association;loop closure detection;simultaneous localization and mapping (SLAM)", "DOI": "10.1002/rob.22088", "PubYear": 2022, "Volume": "39", "Issue": "7", "JournalId": 18627, "JournalTitle": "Journal of Field Robotics", "ISSN": "1556-4959", "EISSN": "1556-4967", "Authors": [{"AuthorId": 1, "Name": "Haosong Yue", "Affiliation": "School of Automation Science and Electrical Engineering Beihang University Beijing 100191 China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering Beihang University Beijing 100191 China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Science and Electrical Engineering Beihang University Beijing 100191 China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science University of Oxford Oxford UK"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Automation Zhejiang University of Technology Hangzhou China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Robotics Department Institute for Infocomm Research Singapore Singapore"}], "References": [{"Title": "Loop closure detection using supervised and unsupervised deep neural networks for monocular SLAM systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "126", "Issue": "", "Page": "103470", "JournalTitle": "Robotics and Autonomous Systems"}, {"Title": "A novel loop closure detection method with the combination of points and lines based on information entropy", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "38", "Issue": "3", "Page": "386", "JournalTitle": "Journal of Field Robotics"}, {"Title": "Fast and incremental loop closure detection with deep features and proximity graphs", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "39", "Issue": "4", "Page": "473", "JournalTitle": "Journal of Field Robotics"}]}, {"ArticleId": 94772700, "Title": "An improved incident‐wave formulation for broadband electromagnetic scattering by discontinuous Galerkin time‐domain method", "Abstract": "<p>In this article, an incident-wave directly loaded (IWDL) method based on the discontinuous Galerkin time-domain (DGTD) method for broadband electromagnetic (EM) scattering problems is proposed. Each element of DGTD has a set of unknowns. Therefore, it allows us to put the incident surface and scatterer boundary surface together, using the interior unknowns of the surface to impress the incident wave and using the exterior unknowns for fields extrapolation on the same surface. Compared with the traditional total-and scattered-field decomposition (TSFD) method, the computational domain in IWDL is reduced from four regions to three regions. The surfaces needed to be dealt with are reduced from three surfaces to only one surface. Therefore, IWDL can effectively reduce the number of the degrees of freedom (DoF) and simplify the EM scattering modeling. In addition, the accuracy has been greatly improved because of the elimination of the dispersion error, which is associated with the propagation of both the incident field from the incident surface to the scatterer and the scattered field from the scatterer to the surface used for field extrapolation. The correctness and efficiency of this method are verified by some numerical examples involving EM scattering problems.</p>", "Keywords": "broadband electromagnetic scattering;discontinuous Galerkin time-domain (DGTD) algorithm;incident-wave directly loaded (IWDL) method;total-and scattered-field decomposition (TSFD)", "DOI": "10.1002/mmce.23281", "PubYear": 2022, "Volume": "32", "Issue": "9", "JournalId": 2244, "JournalTitle": "International Journal of RF and Microwave Computer-Aided Engineering", "ISSN": "1096-4290", "EISSN": "1099-047X", "Authors": [{"AuthorId": 1, "Name": "Danfeng Han", "Affiliation": "School of Electronic Science and Engineering University of Electronic Science and Technology of China  Chengdu China;Department of Physics, Changzhi University  Changzhi China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Science and Engineering University of Electronic Science and Technology of China  Chengdu China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Division of Computer, Electrical, and Mathematical Science and Engineering, King Abdullah University of Science and Technology (KAUST)  Thuwal Saudi Arabia"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Electronic Science and Engineering University of Electronic Science and Technology of China  Chengdu China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Electronic Science and Engineering University of Electronic Science and Technology of China  Chengdu China"}], "References": []}, {"ArticleId": 94772709, "Title": "Disease mapping and innovation: A history from wood-block prints to Web 3.0", "Abstract": "This paper presents a point in the transition of publicly available data and the means of its presentation. With syndromic mapping and new systems of data collection and distribution at all levels, previously privileged materials are now generally available. At the same time, the means of their analysis and presentation are being transformed by new systems of digital collaboration and presentation. With the coronavirus disease 2019 COVID-19) dashboard as an example, the history of both data and their presentation is presented as the backcloth against which the evolving systems of data collection and graphic presentation can be understood in a world of interactive research and Web 3.0.", "Keywords": "DSML5: Mainstream: Data science output is well understood and (nearly) universally adopted", "DOI": "10.1016/j.patter.2022.100507", "PubYear": 2022, "Volume": "3", "Issue": "6", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Geography (Medical), University of British Columbia, Vancouver, BC, Canada;Alton Medical Centre, Toronto, ON, Canada;Corresponding author"}], "References": []}, {"ArticleId": 94772851, "Title": "Evaluation of additive friction stir deposition of AISI 316L for repairing surface material loss in AISI 4340", "Abstract": "<p>Additive technologies provide a means for repair of various failure modes associated with material degradation occurring during use in aggressive environments. Possible repair strategies for AISI 4340 steel using AISI 316L deposited by additive friction stir deposition (AFSD) were evaluated under this research by metallography, microhardness, and wear and mechanical testing. Two repair geometries were investigated: groove-filling and surface cladding. The former represents repair of localized grinding to eliminate cracks, while the latter represents material replacement over a larger area, for example, to repair general corrosion or wear. The 316L deposited by AFSD exhibited a refined microstructure with decreased grain size and plastic strain, ~ 12% lower strength, and 5–12% lower hardness than the as-received feedstock. Wear testing by both two-body abrasion and erosion by particle impingement indicated that the wear resistance of the 316L cladding was as good as, or better than, the substrate 4340 material; however, there was some evidence that the resistance to intergranular corrosion was compromised due to the formation of carbides or sigma phase. In both repair geometries, the microstructure of the substrate beneath the deposited material exhibited heat affected zones where the substrate appeared to have austenized to a depth of ~ 4 mm during the deposition process, and transformed to martensite or bainite during cooling. This report constitutes an initial evaluation of a novel approach to the repair of structural steel components damaged by microcracking, wear, or corrosion, with potentially broad applicability to the marine, aerospace, and heavy duty off-road industries.</p>", "Keywords": "Additive friction stir deposition; AFSD; AISI 316; Repair", "DOI": "10.1007/s00170-022-09507-3", "PubYear": 2022, "Volume": "121", "Issue": "3-4", "JournalId": 726, "JournalTitle": "The International Journal of Advanced Manufacturing Technology", "ISSN": "0268-3768", "EISSN": "1433-3015", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Golisano Institute for Sustainability, Rochester Institute of Technology, Rochester, USA"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Golisano Institute for Sustainability, Rochester Institute of Technology, Rochester, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Golisano Institute for Sustainability, Rochester Institute of Technology, Rochester, USA"}], "References": [{"Title": "Repair of aluminum 6061 plate by additive friction stir deposition", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "118", "Issue": "3-4", "Page": "759", "JournalTitle": "The International Journal of Advanced Manufacturing Technology"}]}, {"ArticleId": 94772861, "Title": "Feature Extraction using Histogram of Oriented Gradients for Image Classification in Maize Leaf Diseases", "Abstract": "<p>The paper presents feature extraction methods and classification algorithms used to classify maize leaf disease images. From maize disease images, features are extracted and passed to the machine learning classification algorithm to identify the possible disease based on the features detected using the feature extraction method. The maize disease images used include images of common rust, leaf spot, and northern leaf blight and healthy images. An evaluation was done for the feature extraction method to see which feature extraction method performs best with image classification algorithms. Based on the evaluation, the outcomes revealed Histogram of Oriented Gradients performed best with classifiers compared to KAZE and Oriented FAST and rotated BRIEF. The random forest classifier emerged the best in terms of image classification, based on four performance metrics which are accuracy, precision, recall, and F1-score. The experimental outcome indicated that the random forest had 0.74 accuracy, 0.77 precision, 0.77 recall, and 0.75 F1-score.</p>", "Keywords": "Feature extraction;ORB;HOG;KAZE;Image classification;machine learning;classifier", "DOI": "10.24203/ijcit.v11i2.204", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 75576, "JournalTitle": "International Journal of Computer and Information Technology(2279-0764)", "ISSN": "", "EISSN": "2279-0764", "Authors": [{"AuthorId": 1, "Name": " <PERSON>", "Affiliation": "School of Computing and Information Technology, Murang’a University of Technology, Murang’a, Kenya"}, {"AuthorId": 2, "Name": " <PERSON>", "Affiliation": "School of Computing and Information Technology, Murang’a University of Technology, Murang’a, Kenya"}, {"AuthorId": 3, "Name": " <PERSON>", "Affiliation": "School of Computing and Information Technology, Murang’a University of Technology, Murang’a, Kenya"}], "References": []}, {"ArticleId": 94772909, "Title": "SIMULATION OF PARAMETERS OF A COUPLING DEVICE \r OF A MEASURING RESONATOR WITH A MICROWAVE PATH", "Abstract": "<p>An electrodynamic simulation of a device for coupling a cavity resonator with a path is performed. The cavity coupling device is a distributed communication line, i.e. associated with both the magnetic and electric fields of the resonator, and makes it possible to obtain a coupling that depends little on the frequency. An algorithm for approximating the values of the input impedance from the resistance of the coupling element has been developed in order to calculate the set of characteristics of the loaded resonator. The performed calculations and the obtained dependences make it possible to determine the geometrical parameters of the design of a broadband cavity resonator coupling device in the microwave range of wavelengths.</p>", "Keywords": "объемный резонатор;устройство связи;добротность резонатора", "DOI": "10.52928/2070-1624-2022-38-4-43-48", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 99013, "JournalTitle": "HERALD OF POLOTSK STATE UNIVERSITY. Series С FUNDAMENTAL SCIENCES", "ISSN": "2070-1624", "EISSN": "2710-4230", "Authors": [{"AuthorId": 1, "Name": "V. BOGUSH", "Affiliation": "Belarusian State University of Informatics and Radioelectronics"}, {"AuthorId": 2, "Name": "V. RODIONOVA", "Affiliation": "Belarusian State University of Informatics and Radioelectronics"}, {"AuthorId": 3, "Name": "O. TANANA", "Affiliation": "Polotsk State University"}], "References": []}, {"ArticleId": 94772914, "Title": "Integral Cryptanalysis of WARP based on Monomial Prediction", "Abstract": "<p>WARP is a 128-bit block cipher published by <PERSON><PERSON> et al. at SAC 2020 as a lightweight alternative to AES. It is based on a generalized Feistel network and achieves the smallest area footprint among 128-bit block ciphers in many settings. Previous analysis results include integral key-recovery attacks on 21 out of 41 rounds. In this paper, we propose integral key-recovery attacks on up to 32 rounds by improving both the integral distinguisher and the key-recovery approach substantially. For the distinguisher, we show how to model the monomial prediction technique proposed by <PERSON> et al. at ASIACRYPT 2020 as a SAT problem and thus create a bit-oriented model of WARP taking the key schedule into account. Together with two additional observations on the properties of WARP’s construction, we extend the best previous distinguisher by 2 rounds (as a classical integral distinguisher) or 4 rounds (for a generalized integral distinguisher). For the key recovery, we create a graph-based model of the round function and demonstrate how to manipulate the graph to obtain a cipher representation amenable to FFT-based key recovery.</p>", "Keywords": "Lightweight cryptography;WARP;GFN;Integral cryptanalysis;Monomial prediction;CP;SAT;FFT key recovery", "DOI": "10.46586/tosc.v2022.i2.92-112", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81466, "JournalTitle": "IACR Transactions on Symmetric Cryptology", "ISSN": "", "EISSN": "2519-173X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Graz University of Technology, Graz, Austria"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Graz University of Technology, Graz, Austria"}], "References": []}, {"ArticleId": 94772944, "Title": "Majority-to-minority resampling for boosting-based classification under imbalanced data", "Abstract": "<p>Classification is a classical research field due to its broad applications in data mining such as event extraction, spam detection, and medical treatment. However, class imbalance is an unavoidable problem in many real-world applications. It is challenging for conventional learning algorithms to deal with imbalanced datasets, since they tend to be biased towards the majority class, while the minority class is crucial as well. Many previous studies have been explored to solve class imbalance, such as data sampling and class switching. In this paper, we propose a hybrid strategy named Majority-to-Minority Resampling (MMR) to select switched instances, which adaptively samples potential instances from the majority class to augment the minority class. To reduce the loss of information after sampling, we also propose a Majority-to-Minority Boosting (MMBoost) algorithm for classification by dynamically adjusting weights of the sampled instances. We conduct extensive experiments using real-world datasets. Experimental results demonstrate that the proposed framework achieves competitive performance for dealing with imbalanced data compared to several strong baselines across different common metrics.</p>", "Keywords": "Classification; Class imbalance; Sampling; Boosting", "DOI": "10.1007/s10489-022-03585-2", "PubYear": 2023, "Volume": "53", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "South China University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "South China University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "South China University of Technology, Guangzhou, China"}], "References": [{"Title": "Neighbourhood-based undersampling approach for handling imbalanced and overlapped data", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "509", "Issue": "", "Page": "47", "JournalTitle": "Information Sciences"}, {"Title": "NI-MWMOTE: An improving noise-immunity majority weighted minority oversampling technique for imbalanced classification problems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "158", "Issue": "", "Page": "113504", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Optimizing Weighted Extreme Learning Machines for imbalanced classification and application to credit card fraud detection", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "407", "Issue": "", "Page": "50", "JournalTitle": "Neurocomputing"}, {"Title": "Joint imbalanced classification and feature selection for hospital readmissions", "Authors": "<PERSON><PERSON> Du; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "200", "Issue": "", "Page": "106020", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "An ensemble imbalanced classification method based on model dynamic selection driven by data partition hybrid sampling", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "113660", "JournalTitle": "Expert Systems with Applications"}, {"Title": "A Neighborhood Undersampling Stacked Ensemble (NUS-SE) in imbalanced classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "168", "Issue": "", "Page": "114246", "JournalTitle": "Expert Systems with Applications"}, {"Title": "EUSC: A clustering-based surrogate model to accelerate evolutionary undersampling in imbalanced classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107033", "JournalTitle": "Applied Soft Computing"}, {"Title": "Experimental evaluation of ensemble classifiers for imbalance in Big Data", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON><PERSON>-<PERSON>; <PERSON>", "PubYear": 2021, "Volume": "108", "Issue": "", "Page": "107447", "JournalTitle": "Applied Soft Computing"}, {"Title": "Few-shot imbalanced classification based on data augmentation", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2023, "Volume": "29", "Issue": "5", "Page": "2843", "JournalTitle": "Multimedia Systems"}, {"Title": "A fuzzy association rule-based classifier for imbalanced classification problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "577", "Issue": "", "Page": "265", "JournalTitle": "Information Sciences"}, {"Title": "Imbalanced classification: A paradigm‐based review", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "14", "Issue": "5", "Page": "383", "JournalTitle": "Statistical Analysis and Data Mining: The ASA Data Science Journal"}]}, {"ArticleId": 94772977, "Title": "DRACO Stream Cipher: A Power-efficient Small-state Stream Cipher with Full Provable Security against TMDTO Attacks", "Abstract": "<p>Stream ciphers are vulnerable to generic time-memory-data tradeoff attacks. These attacks reduce the security level to half of the cipher’s internal state size. The conventional way to handle this vulnerability is to design the cipher with an internal state twice as large as the desired security level. In lightweight cryptography and heavily resource constrained devices, a large internal state size is a big drawback for the cipher. This design principle can be found in the eSTREAM portfolio members Grain and Trivium.Recently proposals have been made that reduce the internal state size. These ciphers distinguish between a volatile internal state and a non-volatile internal state. The volatile part would typically be updated during a state update while the non-volatile part remained constant. Cipher proposals like Sprout, Plantlet, Fruit and Atom reuse the secret key as non-volatile part of the cipher. However, when considering indistinguishability none of the ciphers mentioned above provides security beyond the birthday bound with regard to the volatile internal state. Partially this is due to the lack of a proper proof of security.We present a new stream cipher proposal called Draco which implements a construction scheme called CIVK. In contrast to the ciphers mentioned above, CIVK uses the initial value and a key prefix as its non-volatile state. Draco builds upon CIVK and uses a 128-bit key and a 96-bit initial value and requires 23 % less area and 31 % less power than Grain-128a at 10 MHz. Further, we present a proof that CIVK provides full security with regard to the volatile internal state length against distinguishing attacks. This makes Draco a suitable cipher choice for ultra-lightweight devices like RFID tags.</p>", "Keywords": "Symmetric-key cryptography;lightweight cryptography;stream ciphers;provable security;TMDTO attacks;Grain;RFID", "DOI": "10.46586/tosc.v2022.i2.1-42", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81466, "JournalTitle": "IACR Transactions on Symmetric Cryptology", "ISSN": "", "EISSN": "2519-173X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "ERNW Research GmbH, Heidelberg, Germany"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Universität Mannheim, Mannheim, Germany"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Universität Mannheim, Mannheim, Germany"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Universität Siegen, Siegen, Germany"}], "References": []}, {"ArticleId": 94772981, "Title": "Process-Aware Dialogue System With Clinical Guideline Knowledge", "Abstract": "<p>Task-oriented dialogue systems aim to engage in interactive dialogue with people to ultimately complete specific tasks. Typical application domains include ticket booking, online shopping, and healthcare providing. Medical dialogue systems can interact with patients, provide initial clinical advice, and improve the efficiency and quality of healthcare services. However, current medical dialogue systems lack the ability to utilize domain knowledge. This paper extracts regular domain knowledge as well as medical process knowledge from clinical guidelines to improve the performance of dialogue systems. Regular knowledge is used to generate accurate responses for a given input, and process knowledge is used to steer the conversation. The authors divide the task of multi-turn conversation generation into four sub-tasks and propose a 4-layer knowledge-based process-aware dialogue model that incorporates the domain knowledge to generate responses. Results indicate that the approach can lead medical conversations actively while providing accurate responses.</p>", "Keywords": "", "DOI": "10.4018/IJWSR.304392", "PubYear": 2022, "Volume": "19", "Issue": "1", "JournalId": 33589, "JournalTitle": "International Journal of Web Services Research", "ISSN": "1545-7362", "EISSN": "1546-5004", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": ""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": ""}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": ""}], "References": []}, {"ArticleId": 94772989, "Title": "Fast MILP Models for Division Property", "Abstract": "<p>Nowadays, MILP is a very popular tool to help cryptographers search for various distinguishers, in particular for integral distinguishers based on the division property. However, cryptographers tend to use MILP in a rather naive way, modeling problems in an exact manner and feeding them to a MILP solver. In this paper, we show that a proper use of some features of MILP solvers such as lazy constraints, along with using simpler but less accurate base models, can achieve much better solving times, while maintaining the precision of exact models. In particular, we describe several new modelization techniques for division property related models as well as a new variant of the Quine-McCluskey algorithm for this specific setting. Moreover, we positively answer a problem raised in [DF20] about handling the large sets of constraints describing valid transitions through Super S-boxes into a MILP model. As a result, we greatly improve the solving times to recover the distinguishers from several previous works ([DF20], [HWW20], [SWW17], [Udo21], [EY21]) and we were able to search for integral distinguishers on 5-round ARIA which was out of reach of previous modeling techniques.</p>", "Keywords": "block cipher;integral distinguisher;MILP", "DOI": "10.46586/tosc.v2022.i2.289-321", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81466, "JournalTitle": "IACR Transactions on Symmetric Cryptology", "ISSN": "", "EISSN": "2519-173X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Un<PERSON> Rennes, Centre National de la Recherche Scientifique (CNRS), Institut de Recherche en Informatique et Systèmes Aléatoires (IRISA), Rennes, France"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Ruhr University Bochum, Bochum, Germany"}], "References": []}, {"ArticleId": 94773004, "Title": "ELECTROMAGNETI<PERSON> METHODS OF SEARCHING AND DEFINITION OF HYDROCARBON DEPOSITS", "Abstract": "<p>The article studies the characteristics of an anisotropic medium over hydrocarbons with the complex use of electromagnetic geo-exploration methods. The simulation of the components of dielectric permittivity tensors in the mode of amplitude-modulated, frequency-modulated, amplitude-frequency-modulated and radio pulse signals is carried out. The frequencies of the electron plasma and electron cyclotron resonances for the specified regimes are established. A study was made of the influence of modes of probing signals on the characteristics of an anisotropic medium above deposits and the components of the dielectric tensor. The effect of particle concentration variation on the real components of the components of the dielectric permittivity of an anisotropic medium above the UVZ is analyzed. Recommendations are given for improving the methods of electrical exploration and their application for prospecting geophysics.</p>", "Keywords": "электромагнитные методы;углеводородная залежь;тензор диэлектрической проницаемости", "DOI": "10.52928/2070-1624-2022-38-4-81-91", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 99013, "JournalTitle": "HERALD OF POLOTSK STATE UNIVERSITY. Series С FUNDAMENTAL SCIENCES", "ISSN": "2070-1624", "EISSN": "2710-4230", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Polotsk State University"}, {"AuthorId": 2, "Name": "S. ALIEVA", "Affiliation": "Polotsk State University"}, {"AuthorId": 3, "Name": "S. KALINTSEV", "Affiliation": "Полоцкий государственный университет"}], "References": []}, {"ArticleId": 94773016, "Title": "ACCUMULATION OF RADIONUCLIDES IN REPLACEABLE PARTS AND WATER TARGET CYCLOTRON", "Abstract": "<p>The accumulation of unwanted long-lived radionuclides during the production of 18F-based radiopharmaceuticals using the IBA Cyclone 18/9 HC cyclotron is considered. Using high-resolution gamma-ray spectrometry with HPGe detectors, the identification of radionuclides and the assessment of activity in activated components (stripper, target entrance window) of the \"medical\" 18-MeV cyclotron IBA Cyclone 18/9 were carried out. More than 20 unwanted radionuclides have been identified in irradiated water. Various mechanisms for the entry of longlived radionuclides into irradiated water are described. The results obtained are of great importance for optimizing the methods of radioactive waste management in the production of radiopharmaceuticals and, as a result, minimizing the radiation exposure of personnel.</p>", "Keywords": "радиофармпрепараты;радионуклиды;циклотрон;обогащенная 18О вода;протонное облучение", "DOI": "10.52928/2070-1624-2022-38-4-69-80", "PubYear": 2022, "Volume": "38", "Issue": "4", "JournalId": 99013, "JournalTitle": "HERALD OF POLOTSK STATE UNIVERSITY. Series С FUNDAMENTAL SCIENCES", "ISSN": "2070-1624", "EISSN": "2710-4230", "Authors": [{"AuthorId": 1, "Name": "A. <PERSON>", "Affiliation": "Belarusian State Institute of Metrology"}, {"AuthorId": 2, "Name": "S. VABISHCHEVICH", "Affiliation": "Polotsk State University"}, {"AuthorId": 3, "Name": "N. VABISHCHEVICH", "Affiliation": "Polotsk State University"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Belarusian State University"}], "References": []}, {"ArticleId": 94773076, "Title": "A novel fluorescence-scattering ratiometric sensor based on Fe-N-C nanozyme with robust oxidase-like activity", "Abstract": "Numerous ratiometric fluorescence nanozyme sensors are developed, though, fluorescence-scattering ratiometric sensors based on nanozymes are hardly reported. First-order scattering (FOS) could be quenched due to inner filter effect of quenchers on scattering signals. Meanwhile, Second-order scattering (SOS) depended on FOS. Based on the principle, we designed a novel nanozyme sensor by coupling fluorescence (FL) and SOS. Specifically, Fe-N-C nanozyme (Fe-HCNP) was fabricated by doping Fe into carbon-nitrogen polymers via formamide condensations. Fe-HCNP possessed robust oxidase-like activity, effectively transforming O<sub>2</sub> into ·O<sub>2</sub><sup>-</sup> and ·OH. Then, Fe-HCNP and o-phenylenediamine (OPD) severed as a hybrid system to construct an FL-SOS ratiometric sensor for ascorbic acid (AA). Fe-HCNP with large size possessed strong SOS. After being added into the Fe-HCNP/OPD system, AA could fast be oxidized to dehydroascorbic acid (DAA) due to Fe-HCNP as oxidase mimics. Subsequently, fluorescent quinoxaline derivative (DFQ) was produced after condensation reactions between DAA and OPD, causing an increase in fluorescence. Meanwhile, the intensity of FOS was reduced because FOS peaks of Fe-HCNP overlapped absorption bands of DFQ, causing a decrease in SOS. The FL-SOS ratiometric sensor for AA showed outstanding advantages of high specificity and fast. Overall, this work provided a new strategy for nanozyme ratiometric sensors.", "Keywords": "Fe-N-C nanozyme ; Oxidase mimics ; Fluorescence-scattering ratiometric sensor", "DOI": "10.1016/j.snb.2022.132181", "PubYear": 2022, "Volume": "368", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, People’s Republic of China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, People’s Republic of China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, People’s Republic of China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, People’s Republic of China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, People’s Republic of China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Key Laboratory of Luminescence Analysis and Molecular Sensing, Ministry of Education, College of Chemistry and Chemical Engineering, Southwest University, Chongqing 400715, People’s Republic of China;Corresponding author"}], "References": [{"Title": "Size-controllable Fe-N/C single-atom nanozyme with exceptional oxidase-like activity for sensitive detection of alkaline phosphatase", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "305", "Issue": "", "Page": "127511", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Self-assembled dual-emissive nanoprobe with metal−organic frameworks as scaffolds for enhanced ascorbic acid and ascorbate oxidase sensing", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "339", "Issue": "", "Page": "129910", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 94773252, "Title": "Study on secure distribution of vehicle road collaborative data based on attribute-based encryption", "Abstract": "<p>In order to solve the problems of poor effect, low distribution accuracy and high average delay in traditional methods, a secure distribution method of vehicle road collaborative data based on attribute-based encryption is proposed. The attribute-based encryption algorithm is used to initialize the global and authorization center, and receive the vehicle road cooperation data to be distributed. The decision tree algorithm is used to classify the vehicle road collaboration data, and the corresponding index is established for the vehicle road collaboration data. The user private key is generated according to the data classification results, and the ciphertext is sent to the fog node to realize the safe distribution of vehicle road collaborative data. The experimental results show that the proposed method has a good effect on the secure distribution of vehicle road cooperative data, which can effectively improve the distribution accuracy and reduce the average distribution delay.</p>", "Keywords": "", "DOI": "10.3233/WEB-220015", "PubYear": 2022, "Volume": "20", "Issue": "3", "JournalId": 20625, "JournalTitle": "Web Intelligence", "ISSN": "2405-6456", "EISSN": "2405-6464", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China;Department of Artificial Intelligence Engineering, Xinjiang Vocational & Technical College of Communications, Urumqi 831401, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "College of Mechanical and Electrical Engineering, Nanjing University of Aeronautics and Astronautics, Nanjing 210016, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Research Institute of Highway Ministry of Transport, Beijing 100088, China"}], "References": [{"Title": "VerSA", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "15", "Issue": "3", "Page": "65", "JournalTitle": "International Journal of Information Security and Privacy"}]}, {"ArticleId": 94773275, "Title": "AI in drug discovery: Applications, opportunities, and challenges", "Abstract": "", "Keywords": "", "DOI": "10.1016/j.patter.2022.100529", "PubYear": 2022, "Volume": "3", "Issue": "6", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Arctoris and the Young Academy of the German National Academy of Sciences"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Scientific editor, <PERSON><PERSON><PERSON>"}], "References": []}, {"ArticleId": 94773366, "Title": "Design of Web Security Penetration Test System Based on Attack and Defense Game", "Abstract": "<p>Some sensitive data in the network will be leaked due to the loopholes or weaknesses of the web system itself, which will bring potential harm to the society or the public. Aiming at this, this study carries out the design of web security penetration test system. A test scheme comparing single method with an automatic comprehensive test method is designed. Based on this scheme, an automatic penetration test system script used under the terminal operation page is tested and designed. A security evaluation algorithm based on the prediction results of the game between attack and defense is proposed. Through this algorithm, different website systems are evaluated and scored, and the test results are compared through scoring. The automatic penetration test integration system designed and implemented in this study can meet the main objectives of web security and the protection requirements of websites against general, routine, and universal security attacks. The proposed evaluation algorithm is more detailed, accurate, and reference in scoring.</p>", "Keywords": "", "DOI": "10.1155/2022/8645969", "PubYear": 2022, "Volume": "2022", "Issue": "", "JournalId": 23915, "JournalTitle": "Scientific Programming", "ISSN": "1058-9244", "EISSN": "1875-919X", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Network Security, Henan Police College, Zhengzhou 450000, China"}, {"AuthorId": 2, "Name": "Li Sun", "Affiliation": "Department of Network Security, Henan Police College, Zhengzhou 450000, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Network Security, Henan Police College, Zhengzhou 450000, China"}], "References": [{"Title": "The fuzzy common vulnerability scoring system (F-CVSS) based on a least squares approach with fuzzy logistic regression", "Authors": "Kerem Gencer; Fatih <PERSON>çiftçi", "PubYear": 2021, "Volume": "22", "Issue": "2", "Page": "145", "JournalTitle": "Egyptian Informatics Journal"}, {"Title": "Digital twin in smart manufacturing", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "26", "Issue": "", "Page": "100289", "JournalTitle": "Journal of Industrial Information Integration"}]}, {"ArticleId": 94773450, "Title": "Movie Box Office Prediction Based on Multi-Model Ensembles", "Abstract": "<p>This paper is based on the box office data of films released in China in the past, which was collected from ENDATA on 30 November 2021, providing 5683 pieces of movie data, and enabling the selection of the top 2000 pieces of movie data to be used as the box office prediction dataset. In this paper, some types of Chinese micro-data are used, and a Baidu search of the index data of movie names 30 days before and after the release date, coronavirus disease 2019 (COVID-19) data in China, and other characteristics are introduced, and the stacking algorithm is optimized by adopting a two-layer model architecture. The first layer base learners adopt Extreme Gradient Boosting (XGBoost), the Light Gradient Boosting Machine (LightGBM), Categorical Boosting (CatBoost), the Gradient Boosting Decision Tree (GBDT), random forest (RF), and support vector regression (SVR), and the second layer meta-learner adopts a multiple linear regression model, to establish a box office prediction model with a prediction error, Mean Absolute Percentage Error (MAPE), of 14.49%. In addition, in order to study the impact of the COVID-19 epidemic on the movie box office, based on the data of 187 movies released from January 2020 to November 2021, and combined with a number of data features introduced earlier, this paper uses LightGBM to establish a model. By checking the importance of model features, it is found that the situation of the COVID-19 epidemic at the time of movie release had a certain related impact on the movie box office.</p>", "Keywords": "stacking predictor; box office predictor; impact of COVID-19 epidemic stacking predictor ; box office predictor ; impact of COVID-19 epidemic", "DOI": "10.3390/info13060299", "PubYear": 2022, "Volume": "13", "Issue": "6", "JournalId": 38781, "JournalTitle": "Information", "ISSN": "", "EISSN": "2078-2489", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Economics and Management, Beijing Information Science and Technology University, Beijing 100192, China"}, {"AuthorId": 2, "Name": "Fe<PERSON><PERSON>", "Affiliation": "Computer School, Beijing Information Science and Technology University, Beijing 100192, China↑Author to whom correspondence should be addressed"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Computer School, Beijing Information Science and Technology University, Beijing 100192, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Software & Microelectronics, Peking University, Beijing 102600, China"}], "References": []}, {"ArticleId": 94773462, "Title": "Dual-level contrastive learning network for generalized zero-shot learning", "Abstract": "<p>Generalized zero-shot learning (GZSL) aims to utilize semantic information to recognize the seen and unseen samples, where unseen classes are unavailable during training. Though recent advances have been made by incorporating contrastive learning into GZSL, existing approaches still suffer from two limitations: (1) without considering fine-grained cluster structures, these models cannot guarantee the discriminability and semantic awareness of synthetic features; (2) classifiers tend to overfit the seen classes, as they only concentrate on the seen domain. To address these challenges, we propose a Dual-level Contrastive Learning Network (DCLN), in which intra-domain and cross-domain contrastive learning are seamlessly integrated into a unified learning model. Specifically, the former performs center-prototype contrasting to fully explore the discriminative structure knowledge, while the latter is proposed to effectively alleviate the overfitting problem by utilizing the semantic relationships between the seen and unseen domain. Finally, the experimental results on four benchmark datasets demonstrate the superiority of our DCLN over the state-of-the-art methods.</p>", "Keywords": "Generalized zero-shot learning; Contrastive learning; Generative adversarial networks", "DOI": "10.1007/s00371-022-02539-6", "PubYear": 2022, "Volume": "38", "Issue": "9-10", "JournalId": 2123, "JournalTitle": "The Visual Computer", "ISSN": "0178-2789", "EISSN": "1432-2315", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Ping An Life Insurance of China, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Computer Science and Technology, Guangdong University of Technology, Guangzhou, China"}], "References": [{"Title": "Deep transductive network for generalized zero shot learning", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "105", "Issue": "", "Page": "107370", "JournalTitle": "Pattern Recognition"}]}, {"ArticleId": 94773482, "Title": "Mars-TRP: Classification of Mars imagery using dynamic polling between transferred features", "Abstract": "Expeditions on Mars and interest in research orienting around these exploration missions have been accelerating more than ever, recently. Due to lack of active human interference in Mars missions, processing and accurate classification of images taken by the rovers is a very essential part of the system. Proper identification of landforms governs the accessibility of the mobile rovers on Mars’ surface. Moreover, NASA has already collected over two million images from the planet, and more volumes are yet to arrive as these photographs serve as major documents for photogrammetry and studies based on remote sensing. Automatic labeling of incoming images and also making searching of the image database easier in the public interest requires highly accurate image classifiers. Deriving motivation from the above causes, this study intends to implement an efficient supervised multi-class image classifier for identifying Mars imagery. However, this objective is confronted by a major bottleneck. Most datasets that are accurately labeled, portray a highly skewed nature and insufficient data to train a deep model from scratch. The MSL surface imagery labeled dataset captured by the Curiosity rover, that has been considered for this study, is one such dataset with only 6691 images distributed unevenly into 25 classes. These obstacles are less signified in the existing literature and hence this paper addresses these challenges, outperforming the state-of-the-art metrics. Due to the absence of large data volume, a transfer learning based methodology was considered, using very deep convolutional networks pre-trained on ImageNet dataset. But images from Mars often involve a difference in hue, contrast and clarity when compared with images taken on Earth. Hence, the deep model was fine-tuned with our dataset and the extracted feature from the tuned neural network was used for the final classification. It was found that the results obtained from a single pre-trained model were not optimum and that ensemble approaches could unify many such results into a better result. Similar feature vectors were extracted from a few other pre-trained models. The whole setup converges into a dynamic routing module, a novel polling algorithm, which for each image, comes to an agreement about the best set of features while generating the output probability vector. The proposed approach is evaluated by several numeric metrics like accuracy, precision and recall, confusion matrices and roc curves, against the chosen individual pre-trained models and most prominent ensemble methods. Mars-TRP produces a test accuracy of around 88% in the standard test set of MSL surface dataset and an accuracy of 96% in the HiRise dataset, outperforming the individual pre-trained models, all the ensemble baselines and other existing approaches.", "Keywords": "Mars imagery ; Dynamic routing ; Routing by agreement ; Transfer learning ; Ensemble ; Polling", "DOI": "10.1016/j.engappai.2022.105014", "PubYear": 2022, "Volume": "114", "Issue": "", "JournalId": 2586, "JournalTitle": "Engineering Applications of Artificial Intelligence", "ISSN": "0952-1976", "EISSN": "1873-6769", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, Jadavpur University, Kolkata, India;Corresponding author"}], "References": []}, {"ArticleId": 94773487, "Title": "Experimental radar data for monitoring brain atrophy progression", "Abstract": "This data set contains complex frequency domain signals obtained from unidirectional antennas mainly fabricated for radar-based head imaging. Data were obtained as part of a project investigating radar-based microwave imaging for monitoring neurodegenerative diseases, especially Alzheimer&#x27;s disease. The wearable device, measurement setup, and phantoms used are described. Multiple experiments were performed to get the data from three lamb brain phantoms that realistically mimic the whole-brain atrophy due to Alzheimer&#x27;s disease. Microwave imaging has shown great potential for breast and brain screening due to its low cost, non-ionizing, portable, and wearable nature. Most of the studies are based on simulations with good results, but further evaluation on experimental data is required before its clinical viability. This work provides an open-source experimental dataset that can be used to evaluate novel signal processing and imaging techniques and validate simulation results. The data provide both the magnitude and phase value at each discrete frequency, making this data set useful for both time-delay and phase-shift based imaging algorithms.", "Keywords": "Alzheimer's disease;Brain atrophy;Brain imaging;Microwave imaging data;Radar data", "DOI": "10.1016/j.dib.2022.108379", "PubYear": 2022, "Volume": "43", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, University of Edinburgh."}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, University of Edinburgh."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "School of Engineering, University of Edinburgh."}], "References": []}, {"ArticleId": 94773645, "Title": "Traffic signal control using a cooperative EWMA-based multi-agent reinforcement learning", "Abstract": "<p>In contemporary urban, traffic signal control is still enormously difficult. Multi-agent reinforcement learning (MARL) is a promising ways to solve this problem. However, most MARL algorithms can not effectively transfer learning strategies when the agents increase or decrease. This paper proposes a new MARL algorithm called cooperative dynamic delay updating twin delayed deep deterministic policy gradient based on the exponentially weighted moving average (CoTD3-EWMA) to solve the problem. By introducing mean-field theory, the algorithm implicitly models the interaction between agents and environment. It reduces the dimension of action space and improves the scalability of the algorithm. In addition, we propose a dynamic delay updating method based on the exponentially weighted moving average (EWMA), which improves the Q value overestimation problem of the traditional TD3 algorithm. Moreover, a joint reward allocation mechanism and state sharing mechanism are proposed to improve the global strategy learning ability and robustness of the agent. The simulation results show that the performance of the new algorithm is better than the current state-of-the-art algorithms, which effectively reduces the delay time of vehicles and improves the traffic efficiency of the traffic network.</p>", "Keywords": "Mean-field; Traffic signal control; TD3; Multi-agent reinforcement learning", "DOI": "10.1007/s10489-022-03643-9", "PubYear": 2023, "Volume": "53", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Automation Science and Engineering, Xi’an Jiaotong University, Xi’an, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "School of Automation Science and Engineering, Xi’an Jiaotong University, Xi’an, China"}], "References": [{"Title": "A TD3-based multi-agent deep reinforcement learning method in mixed cooperation-competition environment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "411", "Issue": "", "Page": "206", "JournalTitle": "Neurocomputing"}, {"Title": "Cooperative multi-agent actor–critic control of traffic network flow based on edge computing", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "123", "Issue": "", "Page": "128", "JournalTitle": "Future Generation Computer Systems"}]}, {"ArticleId": 94773664, "Title": "Building a reverse dictionary with specific application to the COVID-19 pandemic", "Abstract": "<p>A Reverse Dictionary maps a natural language description to corresponding semantically appropriate words. It is of assistance, particularly to the language producers, in finding the correct word for a concept in mind while writing/speaking. As the COVID-19 pandemic intensely impacted almost all the functionalities across the globe, texts on this subject appear in a significant amount in various forms, including news updates, awareness and safety articles, notices and circulars, research articles, social media posts, etc. A Reverse Dictionary on this subject is a requisite in view of the following reasons, hence addressed. Firstly, the varied text forms involve a diverse range of language producers ranging from professional doctors to the general mass. Secondly, the COVID-19 pandemic's glossary is more specific than the general English language, hence unfamiliar to the language producers. We have carried out an implementation based on the Wordster Reverse Dictionary architecture, owing to its outperformance of the commercial Onelook Reverse Dictionary benchmark. We report an accuracy of 0.49 based on top-3 system responses. To address the limitations of the current implementation, we bring into consideration <PERSON><PERSON><PERSON>'s paradigm of the Computational Theory of Perceptions. Notably, the compilation of the COVID-19 glossary as a part of this study is another contribution in view that it is of assistance to the concerned readers.</p><p>© The Author(s), under exclusive licence to <PERSON><PERSON><PERSON>'s Institute of Computer Applications and Management 2022.</p>", "Keywords": "COVID-19;Coronavirus;Information retrieval;RD;Reverse Dictionary;Wordster RD", "DOI": "10.1007/s41870-022-00995-w", "PubYear": 2022, "Volume": "14", "Issue": "5", "JournalId": 2825, "JournalTitle": "International Journal of Information Technology", "ISSN": "2511-2104", "EISSN": "2511-2112", "Authors": [{"AuthorId": 1, "Name": "Bushra Siddique", "Affiliation": "Department of Computer Engineering, Aligarh Muslim University, Aligarh, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Engineering, Aligarh Muslim University, Aligarh, India."}], "References": []}, {"ArticleId": 94773796, "Title": "An energy efficient algorithm for sustainable monitoring of water quality in smart cities", "Abstract": "The Internet of Things (IoT) is a rapidly evolving technology that connects a huge number of devices for a wide range of applications such as smart cities, logistics, fleet management, etc. These applications demand a variety of requirements such as energy efficiency, cost, and battery life. The battery life of an IoT device is a significant issue, particularly for remote applications. This paper proposes an energy efficient algorithm for LoRa end-nodes for extended battery life. The efficacy of the proposed algorithm is demonstrated in a water quality monitoring system. A maximum energy saving of 62% is achieved compared to the conventional scheme. Additionally, a carbon footprint analysis is carried out, saving 8.6 kg of annual carbon emissions per node.", "Keywords": "Smart monitoring ; LoRaWAN ; Energy harvesting ; Carbon footprint", "DOI": "10.1016/j.suscom.2022.100768", "PubYear": 2022, "Volume": "35", "Issue": "", "JournalId": 7729, "JournalTitle": "Sustainable Computing: Informatics and Systems", "ISSN": "2210-5379", "EISSN": "2210-5387", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Rourkela, India;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Electronics and Communication Engineering, National Institute of Technology Rourkela, India"}], "References": [{"Title": "From serendipity to sustainable green IoT: Technical, industrial and political perspective", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "182", "Issue": "", "Page": "107469", "JournalTitle": "Computer Networks"}, {"Title": "Intelligent deception techniques against adversarial attack on the industrial system", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "36", "Issue": "5", "Page": "2412", "JournalTitle": "International Journal of Intelligent Systems"}]}, {"ArticleId": 94773813, "Title": "Improving the Stability of the Variable Selection with Small Datasets in Classification and Regression Tasks", "Abstract": "<p>Within the design of a machine learning-based solution for classification or regression problems, variable selection techniques are often applied to identify the input variables, which mainly affect the considered target. The selection of such variables provides very interesting advantages, such as lower complexity of the model and of the learning algorithm, reduction of computational time and improvement of performances. Moreover, variable selection is useful to gain a profound knowledge of the considered problem. High correlation in variables often produces multiple subsets of equally optimal variables, which makes the traditional method of variable selection unstable, leading to instability and reducing the confidence of selected variables. Stability identifies the reproducibility power of the variable selection method. Therefore, having a high stability is as important as the high precision of the developed model. The paper presents an automatic procedure for variable selection in classification (binary and multi-class) and regression tasks, which provides an optimal stability index without requiring any a priori information on data. The proposed approach has been tested on different small datasets, which are unstable by nature, and has achieved satisfactory results. </p>", "Keywords": "Variable Selection; Stability; Classification; Regression", "DOI": "10.1007/s11063-022-10916-4", "PubYear": 2023, "Volume": "55", "Issue": "5", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "TeCIP Institute and Department of Excellence in Robotics & AI, Scuola Superiore Sant’Anna, Pisa, Italy"}, {"AuthorId": 2, "Name": "Valentina Colla", "Affiliation": "TeCIP Institute and Department of Excellence in Robotics & AI, Scuola Superiore Sant’Anna, Pisa, Italy"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "TeCIP Institute and Department of Excellence in Robotics & AI, Scuola Superiore Sant’Anna, Pisa, Italy"}], "References": [{"Title": "Feature selection using an improved Chi-square for Arabic text classification", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "2", "Page": "225", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "Stability of feature selection algorithm: A review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "34", "Issue": "4", "Page": "1060", "JournalTitle": "Journal of King Saud University - Computer and Information Sciences"}, {"Title": "A novel wrapper feature selection algorithm based on iterated greedy metaheuristic for sentiment classification", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "146", "Issue": "", "Page": "113176", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Stable bagging feature selection on medical data", "Authors": "<PERSON> Alelyani", "PubYear": 2021, "Volume": "8", "Issue": "1", "Page": "", "JournalTitle": "Journal of Big Data"}, {"Title": "Least squares large margin distribution machine for regression", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "10", "Page": "7058", "JournalTitle": "Applied Intelligence"}]}, {"ArticleId": 94773925, "Title": "Retraction Note: Offline scripting-free author identification based on speeded-up robust features", "Abstract": "", "Keywords": "", "DOI": "10.1007/s10032-022-00404-9", "PubYear": 2022, "Volume": "25", "Issue": "3", "JournalId": 13682, "JournalTitle": "International Journal on Document Analysis and Recognition", "ISSN": "1433-2833", "EISSN": "1433-2825", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Jaipur National University, Jagatpura, Jaipur, India"}, {"AuthorId": 2, "Name": "Vijay Pal Dhaka", "Affiliation": "Jaipur National University, Jagatpura, Jaipur, India"}], "References": []}, {"ArticleId": 94773944, "Title": "Automatic logo detection from document image using HOG features", "Abstract": "<p>Document image analysis and processing has drawn the attention of many researchers due to its real-time applications in day-to-day life. Document database comprising of logo provides a good opportunity for an easier way of indexing, searching and retrieval of the documents. Logo detection is an essential need for the implementation of any logo-based document indexing or retrieval techniques. This paper aims to develop an efficient logo detection method for document images. The major steps employed in the developed system include preprocessing of the input document, finding the connected components and classification of these components into the logo and non-logo candidates. The preprocessing step employs a median filter and a unique procedure for the removal of clutter noise to reduce the false detection rate. Histogram of Oriented Gradient (HOG) features and an SVM classifier are used to identify the logo and non-logo candidates of the document. The presented system is evaluated using Tobacco 800 dataset and the results are compared with existing techniques. The results show an improvement of 5% in average logo detection rate with the proposed work.</p>", "Keywords": "Connected components; Logo detection; Document image processing; HOG; SVM; Logo detection rate", "DOI": "10.1007/s11042-022-13300-5", "PubYear": 2023, "Volume": "82", "Issue": "1", "JournalId": 1705, "JournalTitle": "Multimedia Tools and Applications", "ISSN": "1380-7501", "EISSN": "1573-7721", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Electronics & Communication Engineering, BLDEA’s V. P. Dr. P<PERSON> <PERSON><PERSON> College of Engineering and Technology, Vijayapur, India"}, {"AuthorId": 2, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Department of Computer Science and Engineering, BLDEA’s V. P. Dr. P<PERSON> <PERSON><PERSON> College of Engineering and Technology, Vijayapur, India"}, {"AuthorId": 3, "Name": "<PERSON><PERSON> <PERSON><PERSON>", "Affiliation": "Myanmar Institute of Information Technology (MIIT), Mandalay, Myanmar"}], "References": [{"Title": "Language-based document image retrieval for Trilingual System", "Authors": "<PERSON><PERSON>; <PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2020, "Volume": "12", "Issue": "4", "Page": "1217", "JournalTitle": "International Journal of Information Technology"}, {"Title": "Logo detection using weakly supervised saliency map", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "80", "Issue": "3", "Page": "4341", "JournalTitle": "Multimedia Tools and Applications"}, {"Title": "Vehicle logo recognition using histograms of oriented gradient descriptor and sparsity score", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "18", "Issue": "6", "Page": "3019", "JournalTitle": "TELKOMNIKA (Telecommunication Computing Electronics and Control)"}]}, {"ArticleId": 94773953, "Title": "Systematic indication extension for drugs using patient stratification insights generated by combinatorial analytics", "Abstract": "Indication extension or repositioning of drugs can, if done well, provide a faster, cheaper, and derisked route to the approval of new therapies, creating new options to address pockets of unmet medical need for patients and offering the potential for significant commercial and clinical benefits. We look at the promises and challenges of different repositioning strategies and the disease insights and scalability that new high-resolution patient stratification methodologies can bring. This is exemplified by a systematic analysis of all development candidates and on-market drugs, which identified 477 indication extension opportunities across 30 chronic disease areas, each supported by patient stratification biomarkers. This illustrates the potential that new artificial intelligence (AI) and combinatorial analytics methods have to enhance the rate and cost of innovation across the drug discovery industry.", "Keywords": "indication extension ; drug repositioning ; patient stratification ; biomarkers ; disease signatures ; combinatorial analytics ; DSML 2: Proof-of-concept: Data science output has been formulated; implemented; and tested for one domain/problem", "DOI": "10.1016/j.patter.2022.100496", "PubYear": 2022, "Volume": "3", "Issue": "6", "JournalId": 74143, "JournalTitle": "Patterns", "ISSN": "2666-3899", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "PrecisionLife, Unit 8b Bankside, Hanborough Business Park, Long Hanborough OX29 8LJ, UK"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "PrecisionLife, Unit 8b Bankside, Hanborough Business Park, Long Hanborough OX29 8LJ, UK"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "PrecisionLife, Unit 8b Bankside, Hanborough Business Park, Long Hanborough OX29 8LJ, UK"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "PrecisionLife, Unit 8b Bankside, Hanborough Business Park, Long Hanborough OX29 8LJ, UK;Corresponding author"}], "References": []}, {"ArticleId": ********, "Title": "Editorial Board", "Abstract": "", "Keywords": "", "DOI": "10.1016/S0010-4485(22)00101-4", "PubYear": 2022, "Volume": "150", "Issue": "", "JournalId": 1570, "JournalTitle": "Computer-Aided Design", "ISSN": "0010-4485", "EISSN": "1879-2685", "Authors": [], "References": []}, {"ArticleId": ********, "Title": "Is it a case study?—A critical analysis and guidance", "Abstract": "The term “case study” is not used consistently when describing studies and, most importantly, is not used according to the established definitions. Given the misuse of the term “case study”, we critically analyse articles that cite case study guidelines and report case studies. We find that only about 50% of the studies labelled “case study” are correctly labelled, and about 40% of studies labelled “case study” are actually better understood as “small-scale evaluations”. Based on our experiences conducting the analysis, we formulate support for ensuring and assuring the correct labelling of case studies. We develop a checklist and a self-assessment scheme. The checklist is intended to complement existing definitions and to encourage researchers to use the term “case study” correctly. The self-assessment scheme is intended to help the researcher identify when their empirical study is a “small-scale evaluation” and, again, encourages researchers to label their studies correctly. Finally, we develop and evaluate a smell indicator to automatically suggest when a reported case study may not actually be a case study. These three instruments have been developed to help ensure and assure that only those studies that are actually case studies are labelled as “case study”.", "Keywords": "Case study ; Guidelines ; Citation analysis ; Small-scale evaluation ; Checklist ; Smell indicator", "DOI": "10.1016/j.jss.2022.111395", "PubYear": 2022, "Volume": "192", "Issue": "", "JournalId": 3602, "JournalTitle": "Journal of Systems and Software", "ISSN": "0164-1212", "EISSN": "1873-1228", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Blekinge Institute of Technology, SE-371 79 Karlskrona, Sweden;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Queen’s University Belfast, 18 Malone Road, Computer Science Building, BT9 5BN, Belfast, Northern Ireland, UK"}], "References": [{"Title": "Meshing agile and plan-driven development in safety-critical software: a case study", "Authors": "<PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "2", "Page": "1035", "JournalTitle": "Empirical Software Engineering"}, {"Title": "MSL: A pattern language for engineering self-adaptive systems", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "164", "Issue": "", "Page": "110558", "JournalTitle": "Journal of Systems and Software"}, {"Title": "On the performance of hybrid search strategies for systematic literature reviews in software engineering", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "106294", "JournalTitle": "Information and Software Technology"}, {"Title": "Evaluating the agreement among technical debt measurement tools: building an empirical benchmark of technical debt liabilities", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "25", "Issue": "5", "Page": "4161", "JournalTitle": "Empirical Software Engineering"}, {"Title": "The costs and benefits of multistage configuration: A framework and case study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "153", "Issue": "", "Page": "107095", "JournalTitle": "Computers & Industrial Engineering"}, {"Title": "Case Study Research in Software Engineering—It is a Case, and it is a Study, but is it a Case Study?", "Authors": "<PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "133", "Issue": "", "Page": "106514", "JournalTitle": "Information and Software Technology"}, {"Title": "Quadruple Factors Interference and Its Effects on Quality of Outsourcing Testing: A Case Study", "Authors": "", "PubYear": 2021, "Volume": "10", "Issue": "1", "Page": "228", "JournalTitle": "International Journal of Advanced Trends in Computer Science and Engineering"}, {"Title": "The human in model‐driven engineering loop: A case study on integrating handwritten code in model‐driven engineering repositories", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "1308", "JournalTitle": "Software: Practice and Experience"}, {"Title": "RBAC protection-impacting changes identification: A case study of the security evolution of two PHP applications", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "139", "Issue": "", "Page": "106630", "JournalTitle": "Information and Software Technology"}, {"Title": "Guiding the selection of research methodology in industry–academia collaboration in software engineering", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "140", "Issue": "", "Page": "106678", "JournalTitle": "Information and Software Technology"}, {"Title": "A case study for risk assessment in AR-equipped socio-technical systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "119", "Issue": "", "Page": "102250", "JournalTitle": "Journal of Systems Architecture"}, {"Title": "Agile elicitation of scalability requirements for open systems: A case study", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "182", "Issue": "", "Page": "111064", "JournalTitle": "Journal of Systems and Software"}]}, {"ArticleId": 94774059, "Title": "Gewissensbits – wie würden Si<PERSON> urteilen?", "Abstract": "", "Keywords": "", "DOI": "10.1007/s00287-022-01463-2", "PubYear": 2022, "Volume": "45", "Issue": "4", "JournalId": 7707, "JournalTitle": "Informatik-Spektrum", "ISSN": "0170-6012", "EISSN": "1432-122X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "HAW <PERSON>hut, Landshut, Deutschland"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "TU München, Heilbronn, Deutschland"}, {"AuthorId": 3, "Name": "Franziska Gräfe", "Affiliation": "Staatliches Gymnasium Ernestinum, Gotha, Deutschland"}], "References": []}, {"ArticleId": 94774119, "Title": "Narrating by Doing: A Bridging Concept for Understanding and Informing the Design of Tangible Interfaces for Storytelling", "Abstract": "<p>We present and discuss the concept ‘narrating by doing’ as the process of creating narratives by performing different embodied actions with tangible interfaces for storytelling. We characterize it as a ‘bridging concept’ that can facilitate exchange between theory and design, informing research and design of TUIs for storytelling targeting young children. The concept builds on theories of cognition, learning and narration, specifically drawing upon the following perspectives: Constructionism, Socio-Constructivism, Embodied Cognition, Narratology and The Narrative Practice Hypothesis. Building upon these theoretical foundations, we identify and discuss four ‘design articulations’ (i.e. important parameters that express the qualities of the concept), namely communicative situation, narrative function of the tangible objects, collaborative and embodied actions and the narrator’s position. Finally, we add evidence to the concept and discuss its productiveness by presenting a set of considerations to inform the design of tangible interfaces for storytelling.</p>", "Keywords": "", "DOI": "10.1093/iwc/iwac016", "PubYear": 2021, "Volume": "33", "Issue": "6", "JournalId": 5476, "JournalTitle": "Interacting with Computers", "ISSN": "0953-5438", "EISSN": "1873-7951", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "University of Minho Research Centre on Child Studies, , Braga 4710-057, Portugal;Interactive Technologies Institute ITI , LARSyS, Instituto Superior Técnico, University of Lisbon, 1049-001, Lisbon, Portugal"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "University of Minho Research Centre on Child Studies, , Braga 4710-057, Portugal"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "University of Minho Research Centre on Education (CIEd), Institute of Education, , Braga 4710-057, Portugal"}], "References": [{"Title": "Mapping child–computer interaction research through co-word analysis", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "23-24", "Issue": "", "Page": "100165", "JournalTitle": "International Journal of Child-Computer Interaction"}, {"Title": "A close look into the storytelling process: The procedural nature of interactive digital narratives as learning opportunity", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "41", "Issue": "", "Page": "100466", "JournalTitle": "Entertainment Computing"}]}, {"ArticleId": 94774167, "Title": "An In-Depth Review on Blockchain Simulators for IoT Environments", "Abstract": "<p>Simulating blockchain technology within the IoT has never been as important. Along with this comes the need to find suitable blockchain simulators capable of simulating blockchain networks within an IoT environment. Despite there being a wide variety of blockchain simulators, not all are capable of simulating within an IoT environment and not all are suitable for every IoT environment. This article will review previously published works and present a list of suitable blockchain simulators as well as a few untested simulators that have the potential to simulate blockchain networks within an IoT environment. A total of 18 blockchain simulators are presented and discussed in this paper. In addition, a comprehensive list of the advantages and limitations of each simulator is presented to demonstrate the best situation in which simulators should be used. Finally, recommendations are made on when each simulator should be used and in what situation it should be avoided.</p>", "Keywords": "IoT; blockchain; simulator IoT ; blockchain ; simulator", "DOI": "10.3390/fi14060182", "PubYear": 2022, "Volume": "14", "Issue": "6", "JournalId": 18251, "JournalTitle": "Future Internet", "ISSN": "", "EISSN": "1999-5903", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Manhattan College, Riverdale, NY 10471, USA"}, {"AuthorId": 2, "Name": "Chidinma Dike", "Affiliation": "Electrical and Computer Engineering Department, Manhattan College, Riverdale, NY 10471, USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Manhattan College, Riverdale, NY 10471, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Manhattan College, Riverdale, NY 10471, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Manhattan College, Riverdale, NY 10471, USA"}, {"AuthorId": 6, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Electrical and Computer Engineering Department, Manhattan College, Riverdale, NY 10471, USA"}, {"AuthorId": 7, "Name": "Bingyang Wei", "Affiliation": "Computer Science Department, Texas Christian University, Fort Worth, TX 76109, USA"}], "References": [{"Title": "A comparative analysis of simulators for the Cloud to Fog continuum", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "101", "Issue": "", "Page": "102029", "JournalTitle": "Simulation Modelling Practice and Theory"}, {"Title": "BlockSim: An Extensible Simulation Tool for Blockchain Systems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "", "Page": "28", "JournalTitle": "Frontiers in Blockchain"}, {"Title": "An IoT-enabled intelligent automobile system for smart cities", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "18", "Issue": "", "Page": "100213", "JournalTitle": "Internet of Things"}]}, {"ArticleId": 94774289, "Title": "A Protection Layer over MapReduce Framework for Big Data Privacy", "Abstract": "<p>In many organizations, big data analytics has become a trend in gathering valuable data insights. The framework MapReduce, which is generally used for this purpose, has been accepted by most organizations for its exceptional characteristics. However, because of the availability of significant processing resources, dispersed privacy-sensitive details can be collected quickly, increasing the widespread privacy concerns.  This article reviews some of the existing research articles on the MapReduce framework's privacy issues and proposes an additional layer of privacy protection over the adopted framework. The data is split into bits and processed in the clouds, and two other steps are taken. <PERSON><PERSON> splits the file into bits of a smaller scale. The task tracker then allocates these bits to several mappers. First, the data is split up into key-value pairs, and the intermediate data sets are generated.  The efficiency of the suggested approach may then be effectively interpreted. Overall, the proposed method provides improved scalability. The following figures compare execution time with relation to file size and the number of partitions. As privacy protection technique is used, the loss of data content can be appropriately handled.  It has been demonstrated that MRPL outperforms current methods in terms of CPU optimization, memory usage, and reduced information loss.  Research reveals that the suggested strategy creates significant advantages for Big Data by enhancing privacy and protection. MRPL can considerably solve the privacy issues in Big Data.</p>", "Keywords": "Data Analytics; HADOOP; HDFS; Map Reduce Protection Layer (MRPL); Privacy-preserving", "DOI": "10.24203/ijcit.v11i2.263", "PubYear": 2022, "Volume": "11", "Issue": "2", "JournalId": 75576, "JournalTitle": "International Journal of Computer and Information Technology(2279-0764)", "ISSN": "", "EISSN": "2279-0764", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Information Technology, University of Technology and Applied Sciences, Oman"}], "References": []}, {"ArticleId": 94774322, "Title": "Forecasting Bitcoin price direction with random forests: How important are interest rates, inflation, and market volatility?", "Abstract": "Bitcoin has grown in popularity and has now attracted the attention of individual and institutional investors. Accurate Bitcoin price direction forecasts are important for determining the trend in Bitcoin prices and asset allocation. This paper addresses several unanswered questions. How important are business cycle variables like interest rates, inflation, and market volatility for forecasting Bitcoin prices? Does the importance of these variables change across time? Are the most important macroeconomic variables for forecasting Bitcoin prices the same as those for gold prices? To answer these questions, we utilize tree-based machine learning classifiers, along with traditional logit econometric models. The analysis reveals several important findings. First, random forests predict Bitcoin and gold price directions with a higher degree of accuracy than logit models. Prediction accuracy for bagging and random forests is between 75% and 80% for a five-day prediction. For 10-day to 20-day forecasts bagging and random forests record accuracies greater than 85%. Second, technical indicators are the most important features for predicting Bitcoin and gold price direction, suggesting some degree of market inefficiency. Third, oil price volatility is important for predicting Bitcoin and gold prices indicating that Bitcoin is a substitute for gold in diversifying this type of volatility. By comparison, gold prices are more influenced by inflation than Bitcoin prices, indicating that gold can be used as a hedge or diversification asset against inflation.", "Keywords": "Forecasting ; Machine learning ; Random forests ; Bitcoin ; Gold ; Inflation", "DOI": "10.1016/j.mlwa.2022.100355", "PubYear": 2022, "Volume": "9", "Issue": "", "JournalId": 78703, "JournalTitle": "Machine Learning with Applications", "ISSN": "2666-8270", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Department of Economics East West University, Aftabnagar Main Road, Dhaka, 1219, Bangladesh"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Schulich School of Business, York University, 4700 Keele Street, Toronto, Ontario, Canada M3J 1P3,;Corresponding author"}], "References": [{"Title": "A CNN–LSTM model for gold price time-series forecasting", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "32", "Issue": "23", "Page": "17351", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Stock market movement forecast: A Systematic review", "Authors": "O <PERSON>; <PERSON><PERSON>Quimbaya", "PubYear": 2020, "Volume": "156", "Issue": "", "Page": "113464", "JournalTitle": "Expert Systems with Applications"}, {"Title": "Time-series forecasting of Bitcoin prices using high-dimensional features: a machine learning approach", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "", "Issue": "", "Page": "1", "JournalTitle": "Neural Computing and Applications"}, {"Title": "Gold Against the Machine", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "57", "Issue": "1", "Page": "5", "JournalTitle": "Computational Economics"}, {"Title": "A Novel Cryptocurrency Price Prediction Model Using GRU, LSTM and bi-LSTM Machine Learning Algorithms", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "4", "Page": "477", "JournalTitle": "AI"}]}, {"ArticleId": 94774331, "Title": "Comprehensive illustration of transcriptomic and proteomic dataset for mitigation of arsenic toxicity in rice (Oryza sativa L.) by microbial consortium", "Abstract": "The present article represents the data for analysis of microbial consortium ( P.putida + C.vulgaris ) mediated amelioration of arsenic toxicity in rice plant. In the current study the transcriptome profiling of treated rice root and shoot was performed by illumina sequencing (Platform 2000). To process the reads and to analyse differential gene expression, Fastxtoolkit, NGSQCtoolkit, Bowtie 2 (version 2.1.0), Tophat program (version 2.0.8), Cufflinks and Cuffdiff programs were used. For Proteome profiling, total soluble proteins in shoot of rice plant among different treatments were extracted and separated by 2D poly acrylamide gel electrophoresis (PAGE) and then proteins were identified with the help of MALDI-TOF/TOF. In gel based method of protein identification, the isoelectric focusing machine (IPGphor system,Bio-Rad USA), gel unit (SDS-PAGE) and MALDI-TOF/TOF (4800 proteomic analyzer Applied Biosystem, USA) were used for successful separation and positive identification of proteins. To check the differential abundance of proteins among different treatments, PDQuest software was used for data analysis. For protein identification, Mascot search engine ( http://www.matrixscience.com ) using NCBIprot/SwissProt databases of rice was used. The analyzed data inferred comprehensive picture of key genes and their respective proteins involved in microbial consortium mediated improved plant growth and amelioration of As induced phyto-toxicity in rice. For the more comprehensive information of data, the related full-length article entitled “Microbial consortium mediated growth promotion and Arsenic reduction in Rice: An integrated transcriptome and proteome profiling” may be accessed.", "Keywords": "Antioxidant enzymes;Chlorella vulgaris; Pseudomonas putida; Phytohormone; Transporters", "DOI": "10.1016/j.dib.2022.108377", "PubYear": 2022, "Volume": "43", "Issue": "", "JournalId": 668, "JournalTitle": "Data in Brief", "ISSN": "2352-3409", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India. ;Plant Stress Biology Laboratory, Institute of Environment and Sustainable Development, Banaras Hindu University, Varanasi, 221005, India."}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India. ;Academy of Scientific and Innovative Research (AcSIR), <PERSON><PERSON><PERSON><PERSON>, 2 Rafi Marg, New Delhi, 110 001, India."}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Agriculture and Allied Science, Doon Business School, Dehradun, Uttarakhand, India."}, {"AuthorId": 7, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 8, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 9, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 10, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 11, "Name": "<PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 12, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}, {"AuthorId": 13, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Plant Stress Biology Laboratory, Institute of Environment and Sustainable Development, Banaras Hindu University, Varanasi, 221005, India."}, {"AuthorId": 14, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "CSIR - National Botanical Research Institute, Council of Scientific and Industrial Research, Rana Pratap Marg, Lucknow, 226 001, India."}], "References": []}, {"ArticleId": 94774332, "Title": "Machine learning algorithm selection for windage alteration fault diagnosis of mine ventilation system", "Abstract": "Machine learning algorithms have been widely used in mine fault diagnosis. The correct selection of the suitable algorithms is the key factor that affects the fault diagnosis. However, the impact of machine learning algorithms on the prediction performance of mine fault diagnosis models has not been fully evaluated. In this study, the windage alteration faults (WAFs) diagnosis models, which are based on K-nearest neighbor algorithm (KNN), multi-layer perceptron (MLP), support vector machine (SVM), and decision tree (DT), are constructed. Furthermore, the applicability of these four algorithms in the WAFs diagnosis is explored by a T-type ventilation network simulation experiment and the field empirical application research of Jinchuan No. 2 mine. The accuracy of the fault location diagnosis for the four models in both networks was 100%. In the simulation experiment, the mean absolute percentage error (MAPE) between the predicted values and the real values of the fault volume of the four models was 0.59%, 97.26%, 123.61%, and 8.78%, respectively. The MAPE for the field empirical application was 3.94%, 52.40%, 25.25%, and 7.15%, respectively. The results of the comprehensive evaluation of the fault location and fault volume diagnosis tests showed that the KNN model is the most suitable algorithm for the WAFs diagnosis, whereas the prediction performance of the DT model was the second-best. This study realizes the intelligent diagnosis of WAFs, and provides technical support for the realization of intelligent ventilation.", "Keywords": "Mine ventilation ; Fault diagnosis ; Machine learning ; Algorithm selection ; Windage alteration fault", "DOI": "10.1016/j.aei.2022.101666", "PubYear": 2022, "Volume": "53", "Issue": "", "JournalId": 3897, "JournalTitle": "Advanced Engineering Informatics", "ISSN": "1474-0346", "EISSN": "1873-5320", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "College of Safety Science & Engineering, Liaoning Technical University, Huludao 125105, China;Key Laboratory of Mine Thermo-motive Disaster & Prevention, Ministry of Education, Huludao 125105, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Safety Science & Engineering, Liaoning Technical University, Huludao 125105, China;Key Laboratory of Mine Thermo-motive Disaster & Prevention, Ministry of Education, Huludao 125105, China;Corresponding author at: Liaoning Technical University, Huludao, Liaoning 125105, China"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Safety Science & Engineering, Liaoning Technical University, Huludao 125105, China;Key Laboratory of Mine Thermo-motive Disaster & Prevention, Ministry of Education, Huludao 125105, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Resource Environment and Safety Engineering, University of South China, Hengyang 421001, China"}], "References": [{"Title": "A statistically based fault detection and diagnosis approach for non-residential building water distribution systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "46", "Issue": "", "Page": "101187", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Fault diagnosis based on deep learning for current-carrying ring of catenary system in sustainable railway transportation", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2021, "Volume": "100", "Issue": "", "Page": "106907", "JournalTitle": "Applied Soft Computing"}, {"Title": "New imbalanced bearing fault diagnosis method based on Sample-characteristic Oversampling TechniquE (SCOTE) and multi-class LS-SVM", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "101", "Issue": "", "Page": "107043", "JournalTitle": "Applied Soft Computing"}, {"Title": "A newly-designed fault diagnostic method for transformers via improved empirical wavelet transform and kernel extreme learning machine", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101320", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "Intelligent fault recognition framework by using deep reinforcement learning with one dimension convolution and improved actor-critic algorithm", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "49", "Issue": "", "Page": "101315", "JournalTitle": "Advanced Engineering Informatics"}, {"Title": "An improved convolutional neural network with an adaptable learning rate towards multi-signal fault diagnosis of hydraulic piston pump", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "50", "Issue": "", "Page": "101406", "JournalTitle": "Advanced Engineering Informatics"}]}, {"ArticleId": 94774333, "Title": "Stability Analysis of the Impulsive Projection Neural Network", "Abstract": "<p>Since the neural network model may be affected by the impulse, a projection neural network(PNN) model with impulsive effect, named impulsive projection neural network(IPNN), is proposed in this paper. The IPNN can solve the variational inequalities and related optimization problems much faster than the PNN. We obtain the stability of the IPNN in two steps. Firstly, we construct a Lyapunov function to prove the stability of the PNN. Secondly, we prove that the Lyapunov function is non-increasing under the influence of impulsive effect. Finally, we give three simulation examples to show the performance of the IPNN.</p>", "Keywords": "Projection neural network; Stability analysis; Impulsive effect; Variational inequalities; Optimization problem", "DOI": "10.1007/s11063-022-10901-x", "PubYear": 2023, "Volume": "55", "Issue": "1", "JournalId": 2162, "JournalTitle": "Neural Processing Letters", "ISSN": "1370-4621", "EISSN": "1573-773X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "College of Mathematics and Statistics, Sichuan University of Science and Engineering, Zigong, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Mathematics, Chongqing Jiaotong University, Chongqing, China"}, {"AuthorId": 3, "Name": "B. <PERSON><PERSON>", "Affiliation": "Key Laboratory of Intelligent Information Processing and Control,Chongqing Three Gorges University, Chongqing, People’s Republic of China; Department of Mathematics, University of Ibadan, Ibadan, Nigeria"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "School of Three Gorges Artificial Intelligence, Chongqing Three Gorges University, Chongqing, People’s Republic of China"}], "References": []}, {"ArticleId": 94774340, "Title": "Multi-level Machine Learning-Driven Tunnel Squeezing Prediction: Review and New Insights", "Abstract": "<p>Tunnel squeezing is a time-dependent phenomenon that often happens in weak or over-stressed rock masses, considerably impacting tunnel construction costs and timelines. This paper addresses the severity of potential squeezing problems based on the predicted squeezing levels. To answer this problem, a multi-level data mining decision making assessment methodology is developed based on four commonly available parameters at the early design stage, that is, diameter (D), buried depth (H), support stiffness (K), and rock tunneling quality index (Q) to predict squeezing conditions in rock tunnels. Six different types of machine learning classifier groups viz., decision tree (19 algorithms), rule (11 algorithms), miscellaneous (4 algorithms), function (3 algorithms), Bayesian (2 algorithms), and lazy (2 algorithms) classifiers are comparatively included in the proposed method to assist users in determining which method should be utilized to achieve the required degree of accuracy. The proposed models are trained and tested on a dataset collected from published literature comprising 117 tunnel squeezing inventories. The model is validated using tenfold cross-validation on the training set (containing 80% of data) and also using a 20% test dataset of case histories (24 cases) that had not been originally utilized to train the model. The results show that the proposed multi-level decision-making models can effectively be utilized to analyze tunnel squeezing problems and yield the promised results. The prediction accuracy of developed multi-level models can reach above 90%, which is a very encouraging result for squeezing risk mitigation and prevention. Moreover, based on the well-established confusion matrix, predictive capabilities of the taken tunnel squeezing models is calculated in term of precision, recall, and F-measure. These robust data-centric methods can be extended to various geoengineering classification challenges. By measuring the variable importance based on the best-performed classifiers, i.e., RandomForest, CSForest, and SimpleCart algorithms, in general, it can be concluded that K is the most sensitive predictor to squeezing severity development, followed by Q, H, and D parameters, each with a different degree of significance. However, different methods demonstrate different degrees of importance for these input variables. The results of this study provide beneficial insights for squeezing severity strategies and minimizing potential risks and costs via early-stage four supplied attributes. Moreover, this study leverages various state-of-the-art machine learning classification algorithms, which highlight their application as a tool of data mining technology in geoengineering.</p>", "Keywords": "", "DOI": "10.1007/s11831-022-09774-z", "PubYear": 2022, "Volume": "29", "Issue": "7", "JournalId": 423, "JournalTitle": "Archives of Computational Methods in Engineering", "ISSN": "1134-3060", "EISSN": "1886-1784", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Faculty of Mining Engineering, Petroleum and Geophysics, Shahrood University of Technology, Shahrood, Iran"}], "References": []}, {"ArticleId": 94774441, "Title": "Graduation-inspired synchronization for industry 4.0 planning, scheduling, and execution", "Abstract": "Manufacturing complexity and uncertainty still perplex scholars and practitioners, especially in the modern fast-changing market with diversified customer demand, growing product varieties, and mixed production volumes. Emerging Industry 4.0 technology helps resolve the complexity and uncertainty as it dramatically enhances the connectivity, visibility, and traceability via real-time data of production environments in both physical and cyber aspects. However, the management paradigms inherited from the last industrial revolution for decision-making in Planning, Scheduling, and Execution (PSE) are not ready to tap the potentials of Industry 4.0 real-time data to break the bottleneck of complexity and uncertainty. The deepening divide between technological innovations and methodological advancements inevitably hinders progress towards Industry 4.0 manufacturing. Graduation Manufacturing System (GMS) is a novel paradigm that provides flexible and resilient principles for Industry4.0 PSE, but the decision models of GMS have not been discussed in the literature. This paper proposes Graduation-inspired Synchronization (GiSync) framework as the PSE decision mechanism under GMS. GiSync leverages the combined strengths of real-time data collected by advanced technologies and the innovative management philosophy inspired by graduation ceremonies to facilitate real-time, flexible, and resilient decision-making for Industry 4.0 PSE. It includes proximity-based order-job synchronization, suitability-based job-workstation synchronization, and Out-of-Order-based operation-resource synchronization. A case study shows that GiSync outperforms the others on average and displays minor variations in statistics regarding cost-efficiency, punctuality, and simultaneity measures, indicating that GiSync is more effective, stable, and resilient in stochastic environments.", "Keywords": "Industry 4.0 ; Planning; scheduling; and execution (PSE) ; Synchronization ; Graduation Manufacturing System (GMS) ; Real-time data", "DOI": "10.1016/j.jmsy.2022.05.017", "PubYear": 2022, "Volume": "64", "Issue": "", "JournalId": 5250, "JournalTitle": "Journal of Manufacturing Systems", "ISSN": "0278-6125", "EISSN": "1878-6642", "Authors": [{"AuthorId": 1, "Name": "Mingxing Li", "Affiliation": "School of Intelligent Systems Science and Engineering, Jinan University (Zhuhai Campus), Zhuhai, 519070, China;Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Industrial and Systems Engineering, The Hong Kong Polytechnic University, Hung Hom, Hong Kong, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China;Department of Mechanical and Energy Engineering, Southern University of Science and Technology, Shenzhen, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "School of Intelligent Systems Science and Engineering, Jinan University (Zhuhai Campus), Zhuhai, 519070, China;Department of Industrial and Manufacturing Systems Engineering, The University of Hong Kong, Hong Kong, China"}], "References": [{"Title": "A real-time data-driven collaborative mechanism in fixed-position assembly systems for smart manufacturing", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "61", "Issue": "", "Page": "101841", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}, {"Title": "Real-time production scheduling in the Industry-4.0 context: Addressing uncertainties in job arrivals and machine breakdowns", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "123", "Issue": "", "Page": "105031", "JournalTitle": "Computers & Operations Research"}, {"Title": "An integrated data-driven modeling & global optimization approach for multi-period nonlinear production planning problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "107007", "JournalTitle": "Computers & Chemical Engineering"}, {"Title": "Cyber-physical systems architectures for industrial internet of things applications in Industry 4.0: A literature review", "Authors": "Diego G.S. <PERSON>; Luiz F.F. de Almeida; <PERSON>", "PubYear": 2021, "Volume": "58", "Issue": "", "Page": "176", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Spatial–temporal out-of-order execution for advanced planning and scheduling in cyber-physical factories", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "5", "Page": "1355", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Designing and developing smart production planning and control systems in the industry 4.0 era: a methodology and case study", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "1", "Page": "311", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "Smart manufacturing scheduling: A literature review", "Authors": "<PERSON>-<PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "265", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "Real-time integrated production-scheduling and maintenance-planning in a flexible job shop with machine deterioration and condition-based maintenance", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "61", "Issue": "", "Page": "423", "JournalTitle": "Journal of Manufacturing Systems"}, {"Title": "On reliability of reinforcement learning based production scheduling systems: a comparative survey", "Authors": "Constantin <PERSON><PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2022, "Volume": "33", "Issue": "4", "Page": "911", "JournalTitle": "Journal of Intelligent Manufacturing"}, {"Title": "An end-to-end big data analytics platform for IoT-enabled smart factories: A case study of battery module assembly system for electric vehicles", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "PubYear": 2022, "Volume": "63", "Issue": "", "Page": "214", "JournalTitle": "Journal of Manufacturing Systems"}]}, {"ArticleId": 94774605, "Title": "Test time optimization of solid-state drives and flash-memory chips during studies of the influence of TID on information preservation", "Abstract": "Тестирование всего объёма накопителя при оценке влияния ионизирующего излучения космического пространства на параметры флэш-памяти может занимать неприемлемое время при радиационных исследованиях. Оптимизация времени тестирования возможна путём уменьшения тестируемого объёма памяти с прогнозируемой потерей в точности определения уровня стойкости. В работе описан характер проявления ошибок сохранности информации при дозовом воздействии. Проанализированы экспериментальные зависимости числа ошибок в накопителе от относительного изменения накопленной дозы на примере результатов испытаний девяти типов микросхем. Приведена расчетная оценка объёма тестируемой памяти, достаточного для выявления ошибок в ячейках памяти всего накопителя с заданной вероятностью. С учетом результатов анализа экспериментальных данных показано, что при тестировании 10% накопителя оценка значения уровня стойкости завышается не более чем на 20%. Предложен алгоритм выявления областей адресного пространства твердотельных накопителей информации и микросхем флэш-памяти с потенциально наименьшим уровнем стойкости, знание которых позволяет обоснованно выбрать или дополнительного уменьшить тестируемый объем памяти.", "Keywords": "solid-state drive; flash-memory; radiation hardness; chip; test time optimization; accumulated dose; algorithm for identifying critical areas.;твердотельный накопитель; флэш-память; стойкость; микросхема; оптимизация времени тестирования; накопленная доза; алгоритм выявления критичных областей.", "DOI": "10.26583/bit.2022.2.06", "PubYear": 2022, "Volume": "29", "Issue": "2", "JournalId": 44598, "JournalTitle": "Bezopasnost informacionnyh tehnology", "ISSN": "2074-7128", "EISSN": "2074-7136", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "Joint Stock Company “Experimental Research and Production Association \r\r\nSPECIAL ELECTRONIC SYSTEM”"}], "References": []}, {"ArticleId": 94774771, "Title": "Modelling of DNA mismatch repair with a reversible process calculus", "Abstract": "We have demonstrated in previous work that the Calculus of Covalent Bonding (CCB) can be used to simulate higher-level biochemical processes. This is significant since CCB was originally devised to model lower-level organic chemical reactions. In this paper we extend the use of the calculus to model an important gene repair pathway, namely DNA Mismatch Repair (MMR). This complex pathway involves four helper proteins and needs a distinction between the two chains in a DNA strand. In order to achieve this, we extend the calculus by allowing prefixing with collections of bonding sites.", "Keywords": "Reversible computation ; Calculus of covalent bonding ; DNA mismatch repair", "DOI": "10.1016/j.tcs.2022.06.009", "PubYear": 2022, "Volume": "925", "Issue": "", "JournalId": 4153, "JournalTitle": "Theoretical Computer Science", "ISSN": "0304-3975", "EISSN": "", "Authors": [{"AuthorId": 1, "Name": "<PERSON>", "Affiliation": "School of Computer Science and Informatics, De Montfort University, Leicester, UK;Institute of Computer Science, University of Tartu, Tartu, Estonia;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "School of Computing and Mathematical Sciences, University of Leicester, Leicester, UK"}], "References": [{"Title": "Reversible computation in nature inspired rule-based systems", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "2", "Issue": "4", "Page": "246", "JournalTitle": "Journal of Membrane Computing"}]}, {"ArticleId": 94774784, "Title": "A flexible SDN-based framework for slow-rate DDoS attack mitigation by using deep reinforcement learning", "Abstract": "Distributed Denial-of-Service (DDoS) attacks are difficult to mitigate with existing defense tools. Fortunately, it has been demonstrated that Software-Defined Networking (SDN) with machine learning (ML) and deep learning (DL) techniques has a high potential to handle these threats effectively. However, although there are many SDN-based solutions for detecting DDoS attacks, only a few contain mitigation strategies. Additionally, most previous studies have focused on solving high-rate DDoS attacks. For the time being, recent slow-rate DDoS threats are hard to detect and mitigate. In this work, we propose a modular, flexible, and scalable SDN-based framework that integrates a DL-based intrusion detection system (IDS) and a deep reinforcement learning (DRL)-based intrusion prevention system (IPS) to address slow-rate DDoS threats. We incorporated scalability features into this framework, such as data-plane-based traffic monitoring and traffic flow sampling. Moreover, we have designed a lightweight DRL-based IPS to provide rapid mitigation responses. Furthermore, to evaluate the framework, we deployed a data center network using Mininet, Open Network Operating System (ONOS) controller, and Apache Web server. Next, we performed extensive experiments varying the number of attackers and the rate of attack connections. The proposed IDS achieved an average detection rate of 98%, with a flow sampling rate of 30%. In addition, IPS timely mitigated slow-rate DDoS with 100% of success for a few attackers. Taken together, these results show that the proposed framework provides effective responses to malicious and legitimate connections.", "Keywords": "Software defined networking ; Deep learning ; Deep reinforcement learning ; Intrusion detection system ; Slow rate DDoS ; Mitigation", "DOI": "10.1016/j.jnca.2022.103444", "PubYear": 2022, "Volume": "205", "Issue": "", "JournalId": 1794, "JournalTitle": "Journal of Network and Computer Applications", "ISSN": "1084-8045", "EISSN": "1095-8592", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON> <PERSON>-<PERSON>", "Affiliation": "Tecnologico de Monterrey, School of Engineering and Sciences, Monterrey 64849, Mexico;Corresponding author"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Tecnologico de Monterrey, School of Engineering and Sciences, Monterrey 64849, Mexico"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Tecnologico de Monterrey, School of Engineering and Sciences, Monterrey 64849, Mexico"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "School of Technologies, Universidad Tecnológica Empresarial de Guayaquil, Guayaquil 090511, Ecuador"}], "References": [{"Title": "Data-driven software defined network attack detection : State-of-the-art and perspectives", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "513", "Issue": "", "Page": "65", "JournalTitle": "Information Sciences"}, {"Title": "A novel deep learning model for detection of denial of service attacks in HTTP traffic over internet", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "33", "Issue": "4", "Page": "240", "JournalTitle": "International Journal of Ad Hoc and Ubiquitous Computing"}, {"Title": "A GRU deep learning system against attacks in software defined networks", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "177", "Issue": "", "Page": "102942", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "Emerging DDoS attack detection and mitigation strategies in software-defined networks: Taxonomy, challenges and future directions", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "187", "Issue": "", "Page": "103093", "JournalTitle": "Journal of Network and Computer Applications"}, {"Title": "ADMS: An online attack detection and mitigation system for LDoS attacks via SDN", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON> Yan", "PubYear": 2022, "Volume": "181", "Issue": "", "Page": "454", "JournalTitle": "Computer Communications"}]}, {"ArticleId": 94774786, "Title": "Energy-efficient scheduling algorithms based on task clustering in heterogeneous spark clusters", "Abstract": "Spark is widely used for its fast in-memory processing. It is important to improve energy efficiency under deadline constrains. In this paper, a Task Performance Clustering of Best Fitting Decrease (TPCBFD) scheduling algorithm is proposed. It divides tasks in Spark into three types, with the different types of tasks being placed on nodes with superior performance. However, the basic computation time for TPCBFD takes up a large proportion of the task execution time, so the Energy-Aware TPCBFD (EATPCBFD) algorithm based on the proposed energy consumption model is proposed, focusing on optimizing energy efficiency and Service Level Agreement (SLA) service times. The experimental results show that EATPCBFD increases the average energy efficiency in Spark by 77% and the average passing rate of SLA service time by 14% compared to comparison algorithms. EATPCBFD has higher energy efficiency on average than comparison algorithms under deadline. The average energy efficiency of EATPCBFD with the deadline constraint is higher than the comparison algorithm.", "Keywords": "", "DOI": "10.1016/j.parco.2022.102947", "PubYear": 2022, "Volume": "112", "Issue": "", "JournalId": 5203, "JournalTitle": "Parallel Computing", "ISSN": "0167-8191", "EISSN": "1872-7336", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON> Guan", "Affiliation": "Department of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China"}, {"AuthorId": 5, "Name": "<PERSON> jahan", "Affiliation": "Department of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, 400065, China"}], "References": [{"Title": "Dynamic memory-aware scheduling in spark computing environment", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "141", "Issue": "", "Page": "10", "JournalTitle": "Journal of Parallel and Distributed Computing"}, {"Title": "A Novel Resource Optimization Algorithm Based on Clustering and Improved Differential Evolution Strategy Under a Cloud Environment", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "20", "Issue": "5", "Page": "1", "JournalTitle": "ACM Transactions on Asian and Low-Resource Language Information Processing"}]}, {"ArticleId": 94774790, "Title": "Development of quinone linked immunosorbent assay (QuLISA) based on using <PERSON><PERSON>’s reagent as a non-enzymatic tag: Application to analysis of food allergens", "Abstract": "Enzyme-labeled immunoassays are widely used for trace analysis of clinically significant compounds owing to their high selectivity. However, utilizing an enzyme for labeling the antibody leads to many problems, including low reproducibility and inhibition of antigen-antibody immuno-reaction due to the enzymes’ instability and bulkiness, respectively. Hence, our research group has been using quinones as stable non-enzymatic tags for antibodies utilizing the redox cycle of quinones. Herein, we aimed to use the broadly available and cost-effective quinone, 1,2-naphthoquinone-4-sulfonate (NQS, <PERSON><PERSON>’s reagent), for signal tagging of immunoassays. NQS could bond with biotin in a relatively short time and without using a catalyst forming biotin-1,2-naphthoquinone (BT-NQ). The synthesized BT-NQ produces strong chemiluminescence or color upon mixing with reductant and luminol or tetrazolium salts, with sensitivity down to 7.7 nM and 49.0 nM, respectively. Next, BT-NQ was used for developing the first quinone-linked immunosorbent assay (QuLISA) through labeling biotinylated-detection antibodies using avidin and BT-NQ, and it was targeted to detect food allergen, β-casein using sandwich-immunoassay. Our method showed good linearity for determination of β-casein with good sensitivity down to 20.2 ng/mL. The results of our method were compared with ELISA kit results, and QuLISA showed good matching and higher sensitivity. Moreover, we applied Folin’s reagent for direct labeling of avidin, and the resulted compound possessed strong chemiluminescence originating from its quinone content. At last, we can conclude that the use of Folin’s reagent offered a simple, stable, sensitive, and cost-effective approach for labeling immunoassays and direct chemiluminescence labeling of proteins.", "Keywords": "Enzyme-free tag ; Biotin ; F<PERSON>’s reagent ; Quinone ; Immunoassay ; Food allergen", "DOI": "10.1016/j.snb.2022.132167", "PubYear": 2022, "Volume": "368", "Issue": "", "JournalId": 518, "JournalTitle": "Sensors and Actuators B: Chemical", "ISSN": "0925-4005", "EISSN": "1873-3077", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Analytical Chemistry for Pharmaceuticals, Course of Pharmaceutical Sciences, Graduate School of Biomedical Sciences, Nagasaki University, 1-14 Bunkyo-machi, Nagasaki 852-8521, Japan;Department of Pharmaceutical Analytical Chemistry, Faculty of Pharmacy, Mansoura University, Mansoura 35516, Egypt"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Analytical Chemistry for Pharmaceuticals, Course of Pharmaceutical Sciences, Graduate School of Biomedical Sciences, Nagasaki University, 1-14 Bunkyo-machi, Nagasaki 852-8521, Japan"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Analytical Chemistry for Pharmaceuticals, Course of Pharmaceutical Sciences, Graduate School of Biomedical Sciences, Nagasaki University, 1-14 Bunkyo-machi, Nagasaki 852-8521, Japan"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Analytical Chemistry for Pharmaceuticals, Course of Pharmaceutical Sciences, Graduate School of Biomedical Sciences, Nagasaki University, 1-14 Bunkyo-machi, Nagasaki 852-8521, Japan"}, {"AuthorId": 5, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Analytical Chemistry for Pharmaceuticals, Course of Pharmaceutical Sciences, Graduate School of Biomedical Sciences, Nagasaki University, 1-14 Bunkyo-machi, Nagasaki 852-8521, Japan;Corresponding author"}], "References": [{"Title": "Colorimetric immunoassays based on pyrroloquinoline quinone-catalyzed generation of Fe(II)-ferrozine with tris(2-carboxyethyl)phosphine as the reducing reagent", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; Xiangsheng Mu", "PubYear": 2020, "Volume": "306", "Issue": "", "Page": "127571", "JournalTitle": "Sensors and Actuators B: Chemical"}, {"Title": "Design of a dual functionalized chemiluminescence ultrasensitive probe for quinones based on their redox cycle. Application to the determination of doxorubicin in lyophilized powder and human serum", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "329", "Issue": "", "Page": "129226", "JournalTitle": "Sensors and Actuators B: Chemical"}]}, {"ArticleId": 94774832, "Title": "HAGDetector: Heterogeneous DGA domain name detection model", "Abstract": "The botnet relies on the Command and Control (C&amp;C) channels to conduct its malicious activities remotely. The Domain Generation Algorithm (DGA) is often used by botnets to hide their Command and Control (C&amp;C) server and evade take-down attempts, which allows the bot to generate a large number of domain names until it finds its C&amp;C server. The lengths of domain names generated by DGAs are different. Our research finds that the length of the domain name has an impact on the performance of the DGA domain name detection model. In other words, the model is sensitive to the length of the domain name. In this case, attackers can evade detection simply by designing domain names of specific lengths. Moreover, the detection accuracy of DGA domain names still needs to be further improved. To solve these problems, three feature extraction methods adapted to the length of the domain name are proposed in this paper. For extra-short domain names, we use the attention-based method to extract features, which can make use of the character-level semantic feature. For moderate-length domain names, a two-dimensional structure, namely Right Shifted Tensor (RST), is constructed to make the domain name present apparent features similar to images. For the extra-long domain name, the effective classification of domain names can be achieved by manually crafted easy-to-calculate features. Then, different detection structures are designed based on these tree feature extraction methods to form a heterogeneous DGA detection model, namely HAGDetector. In addition, the public suffix is an important part of the domain name. We further analyze the public suffix to evaluate its impact on the detection of DGA domain names. Finally, the experiments are conducted to assess the validity of HAGDetector, as well as compare our approach with the current state-of-the-art and highlight the impact of the domain name length. The experimental results show that our method greatly improves the detection performance.", "Keywords": "Network security ; Convolutional neural network ; Domain generation algorithm ; Machine learning ; Right shifted tensor (RST) ; DGA Detection", "DOI": "10.1016/j.cose.2022.102803", "PubYear": 2022, "Volume": "120", "Issue": "", "JournalId": 603, "JournalTitle": "Computers & Security", "ISSN": "0167-4048", "EISSN": "1872-6208", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, National University of Defense Technology, Changsha 410073, China;Corresponding author"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, National University of Defense Technology, Changsha 410073, China"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "College of Computer Science and Technology, National University of Defense Technology, Changsha 410073, China"}], "References": [{"Title": "A DGA domain names detection modeling method based on integrating an attention mechanism and deep neural network", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "3", "Issue": "1", "Page": "1", "JournalTitle": "Cybersecurity"}, {"Title": "UMUDGA: A dataset for profiling DGA-based botnet", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "92", "Issue": "", "Page": "101719", "JournalTitle": "Computers & Security"}, {"Title": "MaldomDetector: A system for detecting algorithmically generated domain names with machine learning", "Authors": "<PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "93", "Issue": "", "Page": "101787", "JournalTitle": "Computers & Security"}, {"Title": "Detection of algorithmically-generated domains: An adversarial machine learning approach", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "160", "Issue": "", "Page": "661", "JournalTitle": "Computer Communications"}, {"Title": "An effective node-removal method against P2P botnets", "Authors": "<PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "182", "Issue": "", "Page": "107488", "JournalTitle": "Computer Networks"}, {"Title": "Real-Time Detection of Dictionary DGA Network Traffic Using Deep Learning", "Authors": "<PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "2", "Issue": "2", "Page": "1", "JournalTitle": "SN Computer Science"}, {"Title": "Intercepting Hail Hydra: Real-time detection of Algorithmically Generated Domains", "Authors": "Fran <PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2021, "Volume": "190", "Issue": "", "Page": "103135", "JournalTitle": "Journal of Network and Computer Applications"}]}, {"ArticleId": 94774879, "Title": "Secure contact tracing platform from simplest private set intersection cardinality", "Abstract": "", "Keywords": "", "DOI": "10.1049/ise2.12070", "PubYear": 2022, "Volume": "16", "Issue": "5", "JournalId": 17160, "JournalTitle": "IET Information Security", "ISSN": "1751-8709", "EISSN": "1751-8717", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Biodesign Institution Arizona State University  Tempe Arizona USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Computer Science Amazon.com Inc  Seattle Washington USA"}, {"AuthorId": 3, "Name": "<PERSON>", "Affiliation": "Biodesign Institution Arizona State University  Tempe Arizona USA"}], "References": [{"Title": "WiFiTrace", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "5", "Issue": "1", "Page": "1", "JournalTitle": "Proceedings of the ACM on Interactive, Mobile, Wearable and Ubiquitous Technologies"}]}, {"ArticleId": 94774887, "Title": "Sensitivity examination of YOLOv4 regarding test image distortion and training dataset attribute for apple flower bud classification", "Abstract": "Applications of convolutional neural network (CNN)-based object detectors in agriculture have been a popular research topic in recent years. However, complicated agricultural environments bring many difficulties for ground truth annotation as well as potential uncertainties for image data quality. Using YOLOv4 as a representation of state-of-the-art object detectors, this study quantified YOLOv4’s sensitivity against artificial image distortions including white noise, motion blur, hue shift, saturation change, and intensity change, and examined the importance of various training dataset attributes based on model classification accuracies, including dataset size, label quality, negative sample presence, image sequence, and image distortion levels. The YOLOv4 model trained and validated on the original datasets failed at 31.91% white noise, 22.05-pixel motion blur, 77.38° hue clockwise shift, 64.81° hue counterclockwise shift, 89.98% saturation decrease, 895.35% saturation increase, 79.80% intensity decrease, and 162.71% intensity increase with 30% mean average precisions (mAPs) for four apple flower bud growth stages. The performance of YOLOv4 decreased with both declining training dataset size and training image label quality. Negative samples and training image sequence did not make a substantial difference in model performance. Incorporating distorted images during training improved the classification accuracies of YOLOv4 models on noisy test datasets by 13 to 390%. In the context of apple flower bud growth-stage classification, except for motion blur, YOLOv4 is sufficiently robust for potential image distortions by white noise, hue shift, saturation change, and intensity change in real life. Training image label quality and training instance number are more important factors than training dataset size. Exposing models to test-image-alike training images is crucial for optimal model classification accuracies. The study enhances understanding of implementing object detectors in agricultural research.", "Keywords": "Agriculture ; image annotation ; image distortion ; data quality ; training", "DOI": "10.1080/01431161.2022.2085069", "PubYear": 2022, "Volume": "43", "Issue": "8", "JournalId": 537, "JournalTitle": "International Journal of Remote Sensing", "ISSN": "0143-1161", "EISSN": "1366-5901", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON>", "Affiliation": "Department of Agricultural and Biological Engineering, The Pennsylvania State University, State College, PA, USA"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Agricultural and Biological Engineering, Gulf Coast Research and Education Center, University of Florida, Wimauma, FL, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "Department Department of Surveying Engineering, The Pennsylvania State University, Dallas, PA, USA"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Department of Agricultural and Biological Engineering, The Pennsylvania State University, State College, PA, USA"}, {"AuthorId": 5, "Name": "<PERSON>", "Affiliation": "Department of Agricultural and Biological Engineering, Fruit Research and Extension Center, The Pennsylvania State University, Biglerville, PA, USA"}], "References": [{"Title": "Detecting Citrus in Orchard Environment by Using Improved YOLOv4", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "2020", "Issue": "", "Page": "1", "JournalTitle": "Scientific Programming"}, {"Title": "A detection algorithm for cherry fruits based on the improved YOLO-v4 model", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2023, "Volume": "35", "Issue": "19", "Page": "13895", "JournalTitle": "Neural Computing and Applications"}]}, {"ArticleId": 94774949, "Title": "Indirect signs method for detecting hardware threats of technical means", "Abstract": "Использование зарубежных систем автоматизированного проектирования (САПР) и отладки сложно-функциональных блоков (СФ- или IP-блоков) при разработке сверхбольших интегральных схем (СБИС) связано с рисками появления «закладок», не декларируемых разработчиками программных продуктов и готовых изделий (чаще именуемых за рубежом «троянами»). «Трояны» сравнительно несложно «встроить» так, что они не будут обнаружены сгенерированными той же зарубежной САПР тестами и тестовыми последовательностями при контроле готового изделия. Выявление «троянов» позволяет повысить уровень информационной безопасности радиоэлектронной аппаратуры (РЭА), в составе которой применяются СБИС иностранного производства. Процедурам выявления «троянов» следует подвергать и отечественные СБИС, разработанные и изготовленные c использованием зарубежных САПР и их элементов. В качестве объектов угроз снижения информационной безопасности выбраны четыре класса элементов: -     аппаратные, не имеющие встроенного программного обеспечения; -     аппаратные, не имеющие встроенного программного обеспечения, изменяющие реализуемые функции в зависимости от внешнего воздействия; -     программно-аппаратные, содержащие компьютерную программу и данные, которые не могут изменяться средствами пользователя; -     программно-аппаратные, содержащие компьютерную программу и данные, которые могут изменяться средствами пользователя. В общем виде угроза безопасности информации рассматривается как несвойственная компоненту функция, реализация которой наносит ущерб пользователю РЭА. В настоящей статье рассмотрена задача создания методического аппарата выявления информационных закладок в сверхбольших интегральных схемах (СБИС). Для решения задачи предложен метод косвенных признаков, позволяющий выявить наличие или вероятность наличия информационной угрозы в СБИС или РЭА, использующей данную СБИС. Сущность метода заключается в применении сигнатурного анализа, основанного на применении тестовых последовательностей, позволяющих установить однозначное соответствие между входными воздействиями и откликами тестируемой СБИС, зависящее только от ее внутренних параметров. Разработанный математический аппарат может быть положен в основу аппаратно-программных средств контроля информационной безопасности образцов РЭА двойного назначения.", "Keywords": "electronic component base; verification; information security; information security; signature; database; software.;элементная компонентная база; проверка; информационная безопасность; безопасность информации; сигнатура.", "DOI": "10.26583/bit.2022.2.01", "PubYear": 2022, "Volume": "29", "Issue": "2", "JournalId": 44598, "JournalTitle": "Bezopasnost informacionnyh tehnology", "ISSN": "2074-7128", "EISSN": "2074-7136", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Federal State Budgetary Institution \"All-Russian Research Institute of Radio Electronics\""}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Limited Liability Company \"Information Security Center\""}], "References": []}, {"ArticleId": 94774996, "Title": "A model-based many-objective evolutionary algorithm with multiple reference vectors", "Abstract": "<p>In order to estimate the Pareto front, most of the existing evolutionary algorithms apply the discovery of non-dominated solutions in search space, and most algorithms need appropriate diversity. Sometimes the Pareto front is so much thin and several dominated solutions exist beside the Pareto front. This paper proposes a new inverse model-based evolutionary algorithm with multiple reference vectors in order to exact place of possible Pareto front and then a collection of the exact places of vectors are produced and through this collection, the solutions which are beside the Pareto front mapping to the hyperplane and clustered in order to produce more effective reference vectors point to Pareto front which ultimately leads to the proper guide of diversity and convergence of population. The suggested method has been experimented on the benchmark test suite for CEC’2018 Competition (MaF1–15) and Walking Fish Group (WFG)) and expresses that the suggested strategy is encouraging.</p>", "Keywords": "Many-objective optimization; Model-based evolutionary algorithm; Inverse modeling; Multiple reference vectors", "DOI": "10.1007/s13748-022-00283-5", "PubYear": 2022, "Volume": "11", "Issue": "3", "JournalId": 7519, "JournalTitle": "Progress in Artificial Intelligence", "ISSN": "2192-6352", "EISSN": "2192-6360", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer, South Tehran Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Department of Computer, South Tehran Branch, Islamic Azad University, Tehran, Iran"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer, South Tehran Branch, Islamic Azad University, Tehran, Iran"}], "References": [{"Title": "Multiobjective fuzzy mathematical model for a financially constrained closed‐loop supply chain with labor employment", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>‐Moghaddam", "PubYear": 2020, "Volume": "36", "Issue": "1", "Page": "4", "JournalTitle": "Computational Intelligence"}, {"Title": "Surrogate-assisted classification-collaboration differential evolution for expensive constrained optimization problems", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "508", "Issue": "", "Page": "50", "JournalTitle": "Information Sciences"}]}, {"ArticleId": 94775005, "Title": "Auto-calibration of robot workcells via remote laser scanning", "Abstract": "For new robotic products, the rapid generation of robot programs is generally ensured by offline programming (OLP). Nevertheless, robotic calibration of workcells is essential because of the geometric differences between the three-dimensional (3D) model environment and the real world. In contrast with previous robot self-calibration methods that use specific sensors, the proposed method requires neither marker settings nor initial positioning of the measurement system. Instead, the geometric feature of interplane relations finds relative differences between the coordinates of the 3D model and those of the real workcell. In addition, an iterative method of estimating world coordinates is introduced to increase the accuracy of the revised robot path. A portable laser scanner acquires 3D point-cloud data, so it can be located near the workcell instead of being pre-fixed, thereby facilitating its use. Through experimentation, an average distance of 0.0817 mm is found between the OLP-teaching points and the calibrated points of the pointing operation.", "Keywords": "", "DOI": "10.1016/j.rcim.2022.102394", "PubYear": 2022, "Volume": "78", "Issue": "", "JournalId": 5910, "JournalTitle": "Robotics and Computer-Integrated Manufacturing", "ISSN": "0736-5845", "EISSN": "1879-2537", "Authors": [{"AuthorId": 1, "Name": "J.<PERSON>. <PERSON>", "Affiliation": "Department of Industrial and Management Engineering, Pohang University of Science and Technology, Cheongam-Ro 77, Nam-Gu, Pohang, Gyeongbuk 37673, South Korea"}, {"AuthorId": 2, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Management Engineering, Pohang University of Science and Technology, Cheongam-Ro 77, Nam-Gu, Pohang, Gyeongbuk 37673, South Korea"}, {"AuthorId": 3, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Department of Industrial and Management Engineering, Pohang University of Science and Technology, Cheongam-Ro 77, Nam-Gu, Pohang, Gyeongbuk 37673, South Korea;Corresponding author"}], "References": [{"Title": "Advanced Robot Programming: a Review", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2020, "Volume": "1", "Issue": "4", "Page": "251", "JournalTitle": "Current Robotics Reports"}, {"Title": "Learning-based object detection and localization for a mobile robot manipulator in SME production", "Authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "PubYear": 2022, "Volume": "73", "Issue": "", "Page": "102229", "JournalTitle": "Robotics and Computer-Integrated Manufacturing"}]}, {"ArticleId": 94775067, "Title": "On the Quantum Security of OCB", "Abstract": "<p>The OCB mode of operation for block ciphers has three variants, OCB1, OCB2 and OCB3. OCB1 and OCB3 can be used as secure authenticated encryption schemes whereas OCB2 has been shown to be classically insecure (<PERSON><PERSON><PERSON> et al., Crypto 2019). Even further, in the presence of quantum queries to the encryption functionality, a series of works by <PERSON> et al. (Crypto 2016), <PERSON><PERSON><PERSON><PERSON> et al. (Asiacrypt 2021) and <PERSON><PERSON><PERSON> et al. (Asiacrypt 2021) have shown how to break the unforgeability of the OCB modes. However, these works did not consider the confidentiality of OCB in the presence of quantum queries.We fill this gap by presenting the first formal analysis of the IND-qCPA security of OCB. In particular, we show the first attacks breaking the IND-qCPA security of the OCB modes. Surprisingly, we are able to prove that OCB2 is IND-qCPA secure when used without associated data, while relying on the assumption that the underlying block cipher is a quantum-secure pseudorandom permutation. Additionally, we present new quantum attacks breaking the universal unforgeability of OCB. Our analysis of OCB has implications for the post-quantum security of XTS, a well-known disk encryption standard, that was considered but mostly left open by <PERSON> et al. (PQCrypto 2016).</p>", "Keywords": "OCB;IND-qCPA security;universal forgeability;Simon’s Algorithm;<PERSON><PERSON><PERSON>’s Algorithm;XTS", "DOI": "10.46586/tosc.v2022.i2.379-414", "PubYear": 2022, "Volume": "", "Issue": "", "JournalId": 81466, "JournalTitle": "IACR Transactions on Symmetric Cryptology", "ISSN": "", "EISSN": "2519-173X", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Department of Computer Science, ETH Zürich, Zürich, Switzerland"}, {"AuthorId": 2, "Name": "<PERSON>", "Affiliation": "Meta Research, Menlo Park, USA"}, {"AuthorId": 3, "Name": "<PERSON><PERSON>", "Affiliation": "IBM Research, Bangalore, India"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON><PERSON>", "Affiliation": "Visa Research, Palo Alto, USA"}], "References": []}, {"ArticleId": 94775135, "Title": "Collaborative analytics-supported reflective Assessment for Scaffolding Pre-service Teachers’ collaborative Inquiry and Knowledge Building", "Abstract": "Helping pre-service teachers (PSTs) develop competencies in collaborative inquiry and knowledge building is crucial, but this subject remains largely unexplored in CSCL. This study examines the design and process of collaborative analytics-supported reflective assessment and its effects on promoting PSTs to develop their competencies in collaborative inquiry and knowledge building. We used a quasi-experimental design that lasted 18 weeks. The experimental group was a class of 40 PSTs who took a liberal studies course with a knowledge building design enhanced by collaborative analytics-supported reflective assessment. The comparison group was a class of 28 PSTs taught by the same instructor who studied the same inquiry topics but experienced a regular knowledge building environment using portfolios. The analysis of the PSTs’ Knowledge Forum discourse showed that collaborative analytics-supported reflective assessment helps PSTs develop collaborative inquiry competencies for community knowledge advancement. The analysis of the PSTs’ reflection using collaborative analytics and prompt questions showed that the design using KBDeX visualization and knowledge building rubrics helped them engage in productive collaborative knowledge building inquiry by involving them in continuous monitoring, analysis, negotiation, synthesis of inquiry, identification of promising routes for inquiry, and actions to guide further collective inquiry. Implications for designing CSCL collaborative-analytics enriched with reflective assessment and student agency, and broadening CSCL and knowledge building approaches to pre-service teacher education are discussed.", "Keywords": "Collaborative learning analytics; Reflective assessment; Collaborative inquiry; Knowledge building; Pre-service teacher education", "DOI": "10.1007/s11412-022-09372-y", "PubYear": 2022, "Volume": "17", "Issue": "2", "JournalId": 29983, "JournalTitle": "International Journal of Computer-Supported Collaborative Learning", "ISSN": "1556-1607", "EISSN": "1556-1615", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Faculty of Artificial Intelligence in Education, Central China Normal University, Wuhan, P. R. China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "National Institute of Education (NIE), Nanyang Technological University, Queenstown, Singapore"}, {"AuthorId": 3, "Name": "<PERSON>r <PERSON>", "Affiliation": "Department of Mathematics and Information Technology, The Education University of Hong Kong, Ting Kok, Hong Kong S.A.R"}, {"AuthorId": 4, "Name": "<PERSON>", "Affiliation": "Faculty of Education, The University of Hong Kong, Pok Fu Lam, Hong Kong S.A.R"}], "References": [{"Title": "Exploring social and cognitive dimensions of collaborative problem solving in an open online simulation-based task", "Authors": "<PERSON>-<PERSON>; <PERSON>", "PubYear": 2020, "Volume": "104", "Issue": "", "Page": "105759", "JournalTitle": "Computers in Human Behavior"}, {"Title": "Towards a generalized competency model of collaborative problem solving", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "143", "Issue": "", "Page": "103672", "JournalTitle": "Computers & Education"}, {"Title": "Encouraging collaboration and building Community in Online Asynchronous Professional Development: designing for social capital", "Authors": "<PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "3", "Page": "351", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "Social practices in teacher knowledge creation and innovation adoption: a large-scale study in an online instructional design community for inquiry learning", "Authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "PubYear": 2020, "Volume": "15", "Issue": "4", "Page": "445", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}, {"Title": "Agency to transform: how did a grade 5 community co-configure dynamic knowledge building practices in a yearlong science inquiry?", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "16", "Issue": "3", "Page": "403", "JournalTitle": "International Journal of Computer-Supported Collaborative Learning"}]}, {"ArticleId": 94775139, "Title": "A location-aware siamese network for high-speed visual tracking", "Abstract": "<p>Accurately locating the target position is a challenging task during high-speed visual tracking. Most Siamese trackers based on shallow networks can maintain a fast speed, but they have poor positioning performance. The underlying reason for this is that the appearance features extracted from the shallow network are not effective enough, making it difficult to accurately locate the target from the complex background. Therefore, we present a location-aware Siamese network to address this issue. Specifically, we propose a novel context enhancement module (CEM), which contributes to capturing distinguished object information from both the local and the global levels. At the local level, the features of image local blocks contain more discriminative information that is conductive to locating the target. At the global level, global context information can effectively model long-range dependency, meaning that our tracker can better understand the tracking scene. Then, we construct a well-designed feature fusion network (F-net) to make full use of context information at different scales, where the location block can dynamically adjust to the convolution direction according to the geometry of the target. Finally, Distance-IoU loss (DIoU) is employed to guide the tracker to obtain a more accurate estimation of the target position. Extensive experiments on seven benchmarks including the VOT2016, VOT2018, VOT2019, OTB50, OTB100, UAV123 and LaSOT demonstrate that our tracker achieves competitive results while running at over 200 frames-per-second (FPS).</p>", "Keywords": "Visual tracking; Siamese network; Context enhancement; Feature fusion", "DOI": "10.1007/s10489-022-03636-8", "PubYear": 2023, "Volume": "53", "Issue": "4", "JournalId": 2750, "JournalTitle": "Applied Intelligence", "ISSN": "0924-669X", "EISSN": "1573-7497", "Authors": [{"AuthorId": 1, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 2, "Name": "<PERSON><PERSON>", "Affiliation": "College of Computer Science and Technology, Chongqing University of Posts and Telecommunications, Chongqing, China; Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 3, "Name": "Weisheng Li", "Affiliation": "Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 4, "Name": "<PERSON><PERSON><PERSON>", "Affiliation": "Key Laboratory of Image Cognition, Chongqing University of Posts and Telecommunications, Chongqing, China"}, {"AuthorId": 5, "Name": "<PERSON><PERSON>", "Affiliation": "Hubei Key Laboratory of Intelligent Vision Based Monitoring for Hydroelectric Engineering, China Three Gorges University, Yichang, China"}, {"AuthorId": 6, "Name": "<PERSON><PERSON>", "Affiliation": "Chongqing University Cancer Hospital, Chongqing University, Chongqing, China"}], "References": [{"Title": "Siamese Deformable Cross-Correlation Network for Real-Time Visual Tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "PubYear": 2020, "Volume": "401", "Issue": "", "Page": "36", "JournalTitle": "Neurocomputing"}, {"Title": "SiamAtt: Siamese attention network for visual tracking", "Authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2020, "Volume": "203", "Issue": "", "Page": "106079", "JournalTitle": "Knowledge-Based Systems"}, {"Title": "Adaptive feature fusion for visual object tracking", "Authors": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "111", "Issue": "", "Page": "107679", "JournalTitle": "Pattern Recognition"}, {"Title": "Hierarchical correlation siamese network for real-time object tracking", "Authors": "<PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "PubYear": 2021, "Volume": "51", "Issue": "6", "Page": "3202", "JournalTitle": "Applied Intelligence"}, {"Title": "Rotation adaptive correlation filter for moving object tracking in satellite videos", "Authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "438", "Issue": "", "Page": "94", "JournalTitle": "Neurocomputing"}, {"Title": "SiamPCF: siamese point regression with coarse-fine classification network for visual tracking", "Authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2022, "Volume": "52", "Issue": "5", "Page": "4973", "JournalTitle": "Applied Intelligence"}, {"Title": "IoU-guided Siamese region proposal network for real-time visual tracking", "Authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "PubYear": 2021, "Volume": "462", "Issue": "", "Page": "544", "JournalTitle": "Neurocomputing"}, {"Title": "Subspace reconstruction based correlation filter for object tracking", "Authors": "Yuan Tai; Yihua Tan; Shengzhou Xiong", "PubYear": 2021, "Volume": "212", "Issue": "", "Page": "103272", "JournalTitle": "Computer Vision and Image Understanding"}]}]