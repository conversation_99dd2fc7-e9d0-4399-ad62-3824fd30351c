{"cells": [{"cell_type": "markdown", "id": "d2877681", "metadata": {}, "source": ["# Pandas"]}, {"cell_type": "markdown", "id": "e5c7d46c", "metadata": {}, "source": ["## 导包"]}, {"cell_type": "code", "execution_count": 3, "id": "c7ec0adb", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "markdown", "id": "61462029", "metadata": {}, "source": ["## Series"]}, {"cell_type": "markdown", "id": "e4cf953f", "metadata": {}, "source": ["### series创建"]}, {"cell_type": "code", "execution_count": null, "id": "f509b1ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["0        1\n", "1      2.2\n", "2     True\n", "3    hello\n", "dtype: object"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 创建/初始化\n", "s = pd.Series(data=[1,2.2,True,'hello'])\n", "s"]}, {"cell_type": "code", "execution_count": null, "id": "0e9fd6e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    1\n", "1    2\n", "2    8\n", "3    0\n", "4    8\n", "5    4\n", "6    4\n", "7    1\n", "8    1\n", "9    0\n", "dtype: int32"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# index指定显示索引\n", "s = pd.Series(data=np.random.randint(0, 10, size=10), index=np.arange(10))\n", "s"]}, {"cell_type": "code", "execution_count": 6, "id": "6d80b059", "metadata": {}, "outputs": [{"data": {"text/plain": ["语文    90\n", "数学    95\n", "英语    85\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 显示索引可以增强series的可读性\n", "dic = {\n", "    '语文': 90,\n", "    '数学': 95,\n", "    '英语': 85\n", "}\n", "s = pd.Series(dic)\n", "s"]}, {"cell_type": "markdown", "id": "d0479d32", "metadata": {}, "source": ["### 索引和切片"]}, {"cell_type": "code", "execution_count": 7, "id": "24dde01c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["90\n", "90\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_17532\\3344452052.py:1: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  print(s[0])\n"]}], "source": ["print(s[0])\n", "print(s.语文)"]}, {"cell_type": "markdown", "id": "4a24812e", "metadata": {}, "source": ["### 常用属性\n", "    -shape 形状\n", "    -size 大小\n", "    -index 索引\n", "    -values 值\n", "    -dtype 数据类型"]}, {"cell_type": "code", "execution_count": 8, "id": "5de377b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(3,)\n", "3\n", "int64\n", "Index(['语文', '数学', '英语'], dtype='object')\n", "[90 95 85]\n"]}], "source": ["print(s.shape)\n", "print(s.size)\n", "print(s.dtype)\n", "print(s.index)\n", "print(s.values)"]}, {"cell_type": "markdown", "id": "a160ccc9", "metadata": {}, "source": ["### 常用方法\n", "    -head(),tail()\n", "    -unique()\n", "    -isnull(),notnull()\n", "    -add(),sub(),mul(),div()"]}, {"cell_type": "code", "execution_count": 9, "id": "9acb2780", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0    0\n", "1    4\n", "2    4\n", "3    2\n", "4    3\n", "dtype: int32\n", "5    5\n", "6    9\n", "7    3\n", "8    4\n", "9    3\n", "dtype: int32\n"]}], "source": ["s = pd.Series(data=np.random.randint(0, 10, size=10), index=np.arange(10))\n", "print(s.head(5))\n", "print(s.tail(5))"]}, {"cell_type": "code", "execution_count": 10, "id": "2a83400a", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 4, 2, 3, 5, 9], dtype=int32)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["s.unique() # 去重"]}, {"cell_type": "markdown", "id": "6c6f99b6", "metadata": {}, "source": ["### 算术运算"]}, {"cell_type": "code", "execution_count": 12, "id": "03a7c4c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["a    2.0\n", "b    4.0\n", "c    6.0\n", "d    8.0\n", "e    NaN\n", "f    NaN\n", "dtype: float64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["s1 = pd.Series(data=[1,2,3,4,5],index=['a','b','c','d','f'])\n", "s2 = pd.Series(data=[1,2,3,4,5],index=['a','b','c','d','e'])\n", "s1+s2"]}, {"cell_type": "markdown", "id": "7167e74f", "metadata": {}, "source": ["## DataFrame"]}, {"cell_type": "markdown", "id": "f9f3f159", "metadata": {}, "source": ["### 创建与初始化"]}, {"cell_type": "code", "execution_count": 13, "id": "8ce5e7ee", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   a  b  c\n", "0  1  2  3\n", "1  4  5  6"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame([[1, 2, 3], [4, 5, 6]], columns=['a', 'b', 'c'])\n", "df"]}, {"cell_type": "code", "execution_count": 14, "id": "2d4502ae", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>age</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>z<PERSON><PERSON></td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>lisi</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>wangwu</td>\n", "      <td>20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       name  age\n", "0  zhangsan   18\n", "1      lisi   19\n", "2    wangwu   20"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["dic = {\n", "    \"name\": [\"zhang<PERSON>\", \"lisi\", \"wangwu\"],\n", "    \"age\": [18, 19, 20],\n", "}\n", "df = pd.DataFrame(dic)\n", "df"]}, {"cell_type": "markdown", "id": "b2200f87", "metadata": {}, "source": ["### 常用属性\n", "    -与Series基本一致"]}, {"cell_type": "code", "execution_count": 15, "id": "b3769ec7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[['z<PERSON><PERSON>' 18]\n", " ['lisi' 19]\n", " ['wangwu' 20]]\n", "RangeIndex(start=0, stop=3, step=1)\n", "Index(['name', 'age'], dtype='object')\n", "name    object\n", "age      int64\n", "dtype: object\n", "(3, 2)\n"]}], "source": ["print(df.values)\n", "print(df.index)\n", "print(df.columns)\n", "print(df.dtypes)\n", "print(df.shape)"]}, {"cell_type": "markdown", "id": "72df46da", "metadata": {}, "source": ["### 索引和切片"]}, {"cell_type": "code", "execution_count": 17, "id": "25f4f8a9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>d</th>\n", "      <th>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>89</td>\n", "      <td>80</td>\n", "      <td>68</td>\n", "      <td>64</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>70</td>\n", "      <td>81</td>\n", "      <td>99</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>90</td>\n", "      <td>92</td>\n", "      <td>77</td>\n", "      <td>80</td>\n", "      <td>72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67</td>\n", "      <td>61</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>99</td>\n", "      <td>76</td>\n", "      <td>68</td>\n", "      <td>82</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>86</td>\n", "      <td>66</td>\n", "      <td>60</td>\n", "      <td>90</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>72</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "      <td>63</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>98</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "      <td>72</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>90</td>\n", "      <td>74</td>\n", "      <td>87</td>\n", "      <td>66</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>63</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>65</td>\n", "      <td>82</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    a   b   c   d   e\n", "0  89  80  68  64  69\n", "1  70  81  99  78  99\n", "2  90  92  77  80  72\n", "3  67  61  97  66  83\n", "4  99  76  68  82  93\n", "5  86  66  60  90  80\n", "6  72  78  99  63  70\n", "7  98  90  61  72  97\n", "8  90  74  87  66  61\n", "9  63  97  66  65  82"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "df = pd.DataFrame(data=np.random.randint(60, 100, size=(10, 5)), columns=['a', 'b', 'c', 'd', 'e'])\n", "df"]}, {"cell_type": "code", "execution_count": 18, "id": "319ebe17", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    89\n", "1    70\n", "2    90\n", "3    67\n", "4    99\n", "5    86\n", "6    72\n", "7    98\n", "8    90\n", "9    63\n", "Name: a, dtype: int32"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df['a'] # 取单列"]}, {"cell_type": "code", "execution_count": 20, "id": "dcce8a66", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>89</td>\n", "      <td>68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>70</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>90</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>99</td>\n", "      <td>68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>86</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>72</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>98</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>90</td>\n", "      <td>87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>63</td>\n", "      <td>66</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    a   c\n", "0  89  68\n", "1  70  99\n", "2  90  77\n", "3  67  97\n", "4  99  68\n", "5  86  60\n", "6  72  99\n", "7  98  61\n", "8  90  87\n", "9  63  66"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df[['a','c']] # 取多列"]}, {"cell_type": "code", "execution_count": 24, "id": "58f534f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["a    89\n", "b    80\n", "c    68\n", "d    64\n", "e    69\n", "Name: 0, dtype: int32"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df.iloc[0] # 取单行"]}, {"cell_type": "code", "execution_count": null, "id": "1743bf17", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>d</th>\n", "      <th>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>70</td>\n", "      <td>81</td>\n", "      <td>99</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>86</td>\n", "      <td>66</td>\n", "      <td>60</td>\n", "      <td>90</td>\n", "      <td>80</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    a   b   c   d   e\n", "1  70  81  99  78  99\n", "5  86  66  60  90  80"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df.iloc[[1,5]] # 取多行"]}, {"cell_type": "code", "execution_count": null, "id": "47296c12", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int32(89)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df.iloc[0,2] # 隐式索引\n", "df.loc[0,'a'] # 显示索引"]}, {"cell_type": "code", "execution_count": 29, "id": "cf4198f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>d</th>\n", "      <th>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>89</td>\n", "      <td>80</td>\n", "      <td>68</td>\n", "      <td>64</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>70</td>\n", "      <td>81</td>\n", "      <td>99</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    a   b   c   d   e\n", "0  89  80  68  64  69\n", "1  70  81  99  78  99"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["# 切片\n", "\n", "# 切行\n", "df[0:2]"]}, {"cell_type": "code", "execution_count": 30, "id": "d89b21f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>89</td>\n", "      <td>80</td>\n", "      <td>68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>70</td>\n", "      <td>81</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>90</td>\n", "      <td>92</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67</td>\n", "      <td>61</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>99</td>\n", "      <td>76</td>\n", "      <td>68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>86</td>\n", "      <td>66</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>72</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>98</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>90</td>\n", "      <td>74</td>\n", "      <td>87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>63</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    a   b   c\n", "0  89  80  68\n", "1  70  81  99\n", "2  90  92  77\n", "3  67  61  97\n", "4  99  76  68\n", "5  86  66  60\n", "6  72  78  99\n", "7  98  90  61\n", "8  90  74  87\n", "9  63  97  66"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# 切列\n", "df.il<PERSON>[:, 0:3]"]}, {"cell_type": "code", "execution_count": 31, "id": "54e2ab3a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>d</th>\n", "      <th>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>89</td>\n", "      <td>80</td>\n", "      <td>68</td>\n", "      <td>64</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>70</td>\n", "      <td>81</td>\n", "      <td>99</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>90</td>\n", "      <td>92</td>\n", "      <td>77</td>\n", "      <td>80</td>\n", "      <td>72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67</td>\n", "      <td>61</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>99</td>\n", "      <td>76</td>\n", "      <td>68</td>\n", "      <td>82</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>86</td>\n", "      <td>66</td>\n", "      <td>60</td>\n", "      <td>90</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>72</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "      <td>63</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>98</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "      <td>72</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>90</td>\n", "      <td>74</td>\n", "      <td>87</td>\n", "      <td>66</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>63</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>65</td>\n", "      <td>82</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    a   b   c   d   e\n", "0  89  80  68  64  69\n", "1  70  81  99  78  99\n", "2  90  92  77  80  72\n", "3  67  61  97  66  83\n", "4  99  76  68  82  93\n", "5  86  66  60  90  80\n", "6  72  78  99  63  70\n", "7  98  90  61  72  97\n", "8  90  74  87  66  61\n", "9  63  97  66  65  82"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 32, "id": "26d8916d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>d</th>\n", "      <th>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>80</td>\n", "      <td>68</td>\n", "      <td>64</td>\n", "      <td>69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>70</td>\n", "      <td>81</td>\n", "      <td>99</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>90</td>\n", "      <td>92</td>\n", "      <td>77</td>\n", "      <td>80</td>\n", "      <td>72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67</td>\n", "      <td>61</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>99</td>\n", "      <td>76</td>\n", "      <td>68</td>\n", "      <td>82</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>86</td>\n", "      <td>66</td>\n", "      <td>60</td>\n", "      <td>90</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>72</td>\n", "      <td>78</td>\n", "      <td>99</td>\n", "      <td>63</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>98</td>\n", "      <td>90</td>\n", "      <td>61</td>\n", "      <td>72</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>90</td>\n", "      <td>74</td>\n", "      <td>87</td>\n", "      <td>66</td>\n", "      <td>61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>63</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>65</td>\n", "      <td>82</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    a   b   c   d   e\n", "0   0  80  68  64  69\n", "1  70  81  99  78  99\n", "2  90  92  77  80  72\n", "3  67  61  97  66  83\n", "4  99  76  68  82  93\n", "5  86  66  60  90  80\n", "6  72  78  99  63  70\n", "7  98  90  61  72  97\n", "8  90  74  87  66  61\n", "9  63  97  66  65  82"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["qq = df\n", "qq.iloc[0,0] = 0\n", "df"]}, {"cell_type": "markdown", "id": "897f49c9", "metadata": {}, "source": ["### 时间数据类型的转换\n", "    -to_datetime"]}, {"cell_type": "code", "execution_count": 44, "id": "5d6477b5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-12-12</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-12-13</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-12-14</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         time  value\n", "0  2022-12-12      1\n", "1  2022-12-13      2\n", "2  2022-12-14      3"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["dic = {\n", "    'time': ['2022-12-12', '2022-12-13', '2022-12-14'],\n", "    'value': [1, 2, 3]\n", "}\n", "df = pd.DataFrame(dic)\n", "df"]}, {"cell_type": "code", "execution_count": 34, "id": "3dc4d63a", "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('O')"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df['time'].dtype"]}, {"cell_type": "code", "execution_count": 45, "id": "4ef89e74", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-12-12</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-12-13</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-12-14</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        time  value\n", "0 2022-12-12      1\n", "1 2022-12-13      2\n", "2 2022-12-14      3"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["df['time'] = pd.to_datetime(df['time'])\n", "df"]}, {"cell_type": "code", "execution_count": 38, "id": "f45c9295", "metadata": {}, "outputs": [{"data": {"text/plain": ["dtype('<M8[ns]')"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df['time'].dtype"]}, {"cell_type": "code", "execution_count": 49, "id": "1f45b2cf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>value</th>\n", "    </tr>\n", "    <tr>\n", "      <th>time</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-12-12</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-13</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-12-14</th>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            value\n", "time             \n", "2022-12-12      1\n", "2022-12-13      2\n", "2022-12-14      3"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将time列作为源数据的行索引\n", "df.set_index('time')"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}